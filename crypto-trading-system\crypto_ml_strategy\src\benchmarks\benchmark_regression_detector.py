"""
Crypto ML Strategy - 性能回归检测器

该模块实现了性能回归检测功能，包括基线管理、变化检测、
回归分析和告警生成，确保系统性能不会出现意外退化。

主要功能：
- RegressionDetector: 性能回归检测主管理器
- BaselineManager: 性能基线管理器
- ChangeDetector: 性能变化检测器
- RegressionAnalyzer: 回归分析器
- AlertGenerator: 回归告警生成器

作者: Crypto ML Strategy Team
日期: 2024-01-20
版本: 1.0.0
"""

import asyncio
import threading
import statistics
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Dict, List, Optional, Union, Callable, Tuple
from infrastructure.logging.logging_core_manager import get_logger
from benchmarks.performance_logging_core import get_global_performance_logger
from infrastructure.error_handling_system import get_global_error_handler
from benchmarks.benchmark_core_framework import BenchmarkMetrics, BenchmarkResult
from benchmarks.performance_metrics_collector import MetricsSnapshot, MetricsAggregation
import json
from pathlib import Path


class RegressionSeverity(Enum):
    """回归严重程度"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ChangeType(Enum):
    """变化类型"""
    IMPROVEMENT = "improvement"
    DEGRADATION = "degradation"
    STABLE = "stable"
    UNKNOWN = "unknown"


@dataclass
class PerformanceBaseline:
    """性能基线数据"""
    metric_name: str
    baseline_value: float
    confidence_interval: Tuple[float, float]
    sample_count: int
    created_at: datetime
    updated_at: datetime
    version: str = "1.0"
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "metric_name": self.metric_name,
            "baseline_value": self.baseline_value,
            "confidence_interval": list(self.confidence_interval),
            "sample_count": self.sample_count,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "version": self.version,
            "metadata": self.metadata
        }


@dataclass
class PerformanceChange:
    """性能变化检测结果"""
    metric_name: str
    baseline_value: float
    current_value: float
    change_percent: float
    change_type: ChangeType
    severity: RegressionSeverity
    confidence: float
    detected_at: datetime
    context: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "metric_name": self.metric_name,
            "baseline_value": self.baseline_value,
            "current_value": self.current_value,
            "change_percent": self.change_percent,
            "change_type": self.change_type.value,
            "severity": self.severity.value,
            "confidence": self.confidence,
            "detected_at": self.detected_at.isoformat(),
            "context": self.context
        }


@dataclass
class RegressionAlert:
    """回归告警"""
    alert_id: str
    metric_name: str
    change: PerformanceChange
    message: str
    severity: RegressionSeverity
    created_at: datetime
    acknowledged: bool = False
    resolved: bool = False
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "alert_id": self.alert_id,
            "metric_name": self.metric_name,
            "change": self.change.to_dict(),
            "message": self.message,
            "severity": self.severity.value,
            "created_at": self.created_at.isoformat(),
            "acknowledged": self.acknowledged,
            "resolved": self.resolved
        }


class BaselineManager:
    """性能基线管理器"""
    
    def __init__(self, storage_path: str = "logs/baselines"):
        self.storage_path = Path(storage_path)
        self.storage_path.mkdir(parents=True, exist_ok=True)
        self.logger = get_logger(f"{__name__}.BaselineManager")
        self._baselines: Dict[str, PerformanceBaseline] = {}
        self._lock = threading.RLock()
        
        # 加载现有基线
        self._load_baselines()
    
    def create_baseline(self, metric_name: str, values: List[float], 
                       confidence_level: float = 0.95) -> PerformanceBaseline:
        """创建性能基线"""
        try:
            if not values:
                raise ValueError("Values list cannot be empty")
            
            # 计算基线统计
            baseline_value = statistics.mean(values)
            std_dev = statistics.stdev(values) if len(values) > 1 else 0
            
            # 计算置信区间
            z_score = 1.96 if confidence_level == 0.95 else 2.576  # 95% or 99%
            margin = z_score * (std_dev / (len(values) ** 0.5))
            confidence_interval = (baseline_value - margin, baseline_value + margin)
            
            baseline = PerformanceBaseline(
                metric_name=metric_name,
                baseline_value=baseline_value,
                confidence_interval=confidence_interval,
                sample_count=len(values),
                created_at=datetime.now(),
                updated_at=datetime.now(),
                metadata={
                    "std_dev": std_dev,
                    "min_value": min(values),
                    "max_value": max(values),
                    "confidence_level": confidence_level
                }
            )
            
            with self._lock:
                self._baselines[metric_name] = baseline
                self._save_baseline(baseline)
            
            self.logger.info(f"Created baseline for {metric_name}: {baseline_value:.2f}")
            return baseline
            
        except Exception as e:
            self.logger.error(f"Failed to create baseline for {metric_name}: {e}")
            raise
    
    def update_baseline(self, metric_name: str, new_values: List[float]) -> Optional[PerformanceBaseline]:
        """更新性能基线"""
        try:
            existing_baseline = self.get_baseline(metric_name)
            if not existing_baseline:
                return self.create_baseline(metric_name, new_values)
            
            # 合并新旧数据（保留最近的数据）
            max_samples = 1000
            if len(new_values) > max_samples:
                combined_values = new_values[-max_samples:]
            else:
                # 从存储中获取历史数据并合并
                combined_values = new_values
            
            updated_baseline = self.create_baseline(metric_name, combined_values)
            updated_baseline.version = f"{float(existing_baseline.version) + 0.1:.1f}"
            
            return updated_baseline
            
        except Exception as e:
            self.logger.error(f"Failed to update baseline for {metric_name}: {e}")
            return None
    
    def get_baseline(self, metric_name: str) -> Optional[PerformanceBaseline]:
        """获取性能基线"""
        with self._lock:
            return self._baselines.get(metric_name)
    
    def list_baselines(self) -> List[str]:
        """列出所有基线指标名称"""
        with self._lock:
            return list(self._baselines.keys())
    
    def _save_baseline(self, baseline: PerformanceBaseline) -> None:
        """保存基线到文件"""
        try:
            file_path = self.storage_path / f"baseline_{baseline.metric_name}.json"
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(baseline.to_dict(), f, indent=2, ensure_ascii=False)
        except Exception as e:
            self.logger.error(f"Failed to save baseline {baseline.metric_name}: {e}")
    
    def _load_baselines(self) -> None:
        """从文件加载基线"""
        try:
            for file_path in self.storage_path.glob("baseline_*.json"):
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    baseline = PerformanceBaseline(
                        metric_name=data["metric_name"],
                        baseline_value=data["baseline_value"],
                        confidence_interval=tuple(data["confidence_interval"]),
                        sample_count=data["sample_count"],
                        created_at=datetime.fromisoformat(data["created_at"]),
                        updated_at=datetime.fromisoformat(data["updated_at"]),
                        version=data.get("version", "1.0"),
                        metadata=data.get("metadata", {})
                    )
                    
                    self._baselines[baseline.metric_name] = baseline
                    
                except Exception as e:
                    self.logger.warning(f"Failed to load baseline from {file_path}: {e}")
                    
        except Exception as e:
            self.logger.error(f"Failed to load baselines: {e}")


class ChangeDetector:
    """性能变化检测器"""
    
    def __init__(self, baseline_manager: BaselineManager):
        self.baseline_manager = baseline_manager
        self.logger = get_logger(f"{__name__}.ChangeDetector")
        
        # 检测阈值配置
        self.degradation_thresholds = {
            RegressionSeverity.LOW: 5.0,      # 5%
            RegressionSeverity.MEDIUM: 15.0,  # 15%
            RegressionSeverity.HIGH: 30.0,    # 30%
            RegressionSeverity.CRITICAL: 50.0 # 50%
        }
    
    def detect_change(self, metric_name: str, current_value: float) -> Optional[PerformanceChange]:
        """检测性能变化"""
        try:
            baseline = self.baseline_manager.get_baseline(metric_name)
            if not baseline:
                self.logger.debug(f"No baseline found for {metric_name}")
                return None
            
            # 计算变化百分比
            change_percent = ((current_value - baseline.baseline_value) / baseline.baseline_value) * 100
            
            # 确定变化类型
            change_type = self._classify_change(change_percent, metric_name)
            
            # 确定严重程度
            severity = self._determine_severity(abs(change_percent), change_type)
            
            # 计算置信度
            confidence = self._calculate_confidence(current_value, baseline)
            
            change = PerformanceChange(
                metric_name=metric_name,
                baseline_value=baseline.baseline_value,
                current_value=current_value,
                change_percent=change_percent,
                change_type=change_type,
                severity=severity,
                confidence=confidence,
                detected_at=datetime.now(),
                context={
                    "baseline_version": baseline.version,
                    "baseline_sample_count": baseline.sample_count
                }
            )
            
            if change_type != ChangeType.STABLE:
                self.logger.info(f"Detected {change_type.value} in {metric_name}: {change_percent:.2f}%")
            
            return change
            
        except Exception as e:
            self.logger.error(f"Failed to detect change for {metric_name}: {e}")
            return None
    
    def _classify_change(self, change_percent: float, metric_name: str) -> ChangeType:
        """分类变化类型"""
        # 对于不同类型的指标，改进和退化的定义不同
        is_latency_metric = any(keyword in metric_name.lower() 
                               for keyword in ["latency", "time", "duration", "delay"])
        is_error_metric = any(keyword in metric_name.lower() 
                             for keyword in ["error", "fail", "exception"])
        
        if abs(change_percent) < 2.0:  # 2%以内认为稳定
            return ChangeType.STABLE
        
        if is_latency_metric or is_error_metric:
            # 对于延迟和错误指标，增加是退化，减少是改进
            return ChangeType.DEGRADATION if change_percent > 0 else ChangeType.IMPROVEMENT
        else:
            # 对于吞吐量等指标，增加是改进，减少是退化
            return ChangeType.IMPROVEMENT if change_percent > 0 else ChangeType.DEGRADATION
    
    def _determine_severity(self, change_percent: float, change_type: ChangeType) -> RegressionSeverity:
        """确定严重程度"""
        if change_type == ChangeType.IMPROVEMENT or change_type == ChangeType.STABLE:
            return RegressionSeverity.LOW
        
        # 对于退化，根据百分比确定严重程度
        for severity in [RegressionSeverity.CRITICAL, RegressionSeverity.HIGH, 
                        RegressionSeverity.MEDIUM, RegressionSeverity.LOW]:
            if change_percent >= self.degradation_thresholds[severity]:
                return severity
        
        return RegressionSeverity.LOW
    
    def _calculate_confidence(self, current_value: float, baseline: PerformanceBaseline) -> float:
        """计算置信度"""
        try:
            # 检查当前值是否在置信区间内
            lower_bound, upper_bound = baseline.confidence_interval
            
            if lower_bound <= current_value <= upper_bound:
                return 0.95  # 在置信区间内，高置信度
            
            # 计算距离置信区间的距离
            if current_value < lower_bound:
                distance = lower_bound - current_value
                range_size = upper_bound - lower_bound
            else:
                distance = current_value - upper_bound
                range_size = upper_bound - lower_bound
            
            # 距离越远，置信度越高（更确定是真正的变化）
            confidence = min(0.99, 0.5 + (distance / range_size) * 0.4)
            return confidence
            
        except Exception:
            return 0.5  # 默认中等置信度


class AlertGenerator:
    """回归告警生成器"""
    
    def __init__(self, storage_path: str = "logs/alerts"):
        self.storage_path = Path(storage_path)
        self.storage_path.mkdir(parents=True, exist_ok=True)
        self.logger = get_logger(f"{__name__}.AlertGenerator")
        self._alert_handlers: List[Callable[[RegressionAlert], None]] = []
        self._alerts: List[RegressionAlert] = []
        self._lock = threading.RLock()
    
    def generate_alert(self, change: PerformanceChange) -> Optional[RegressionAlert]:
        """生成回归告警"""
        try:
            # 只为退化生成告警
            if change.change_type != ChangeType.DEGRADATION:
                return None
            
            # 只为中等及以上严重程度生成告警
            if change.severity == RegressionSeverity.LOW:
                return None
            
            alert_id = f"alert_{change.metric_name}_{int(change.detected_at.timestamp())}"
            
            message = self._generate_alert_message(change)
            
            alert = RegressionAlert(
                alert_id=alert_id,
                metric_name=change.metric_name,
                change=change,
                message=message,
                severity=change.severity,
                created_at=change.detected_at
            )
            
            with self._lock:
                self._alerts.append(alert)
                self._save_alert(alert)
            
            # 通知所有处理器
            for handler in self._alert_handlers:
                try:
                    handler(alert)
                except Exception as e:
                    self.logger.error(f"Alert handler failed: {e}")
            
            self.logger.warning(f"Generated regression alert: {alert.alert_id}")
            return alert
            
        except Exception as e:
            self.logger.error(f"Failed to generate alert for {change.metric_name}: {e}")
            return None
    
    def add_alert_handler(self, handler: Callable[[RegressionAlert], None]) -> None:
        """添加告警处理器"""
        self._alert_handlers.append(handler)
    
    def acknowledge_alert(self, alert_id: str) -> bool:
        """确认告警"""
        with self._lock:
            for alert in self._alerts:
                if alert.alert_id == alert_id:
                    alert.acknowledged = True
                    self._save_alert(alert)
                    self.logger.info(f"Alert acknowledged: {alert_id}")
                    return True
        return False
    
    def resolve_alert(self, alert_id: str) -> bool:
        """解决告警"""
        with self._lock:
            for alert in self._alerts:
                if alert.alert_id == alert_id:
                    alert.resolved = True
                    self._save_alert(alert)
                    self.logger.info(f"Alert resolved: {alert_id}")
                    return True
        return False
    
    def get_active_alerts(self) -> List[RegressionAlert]:
        """获取活跃告警"""
        with self._lock:
            return [alert for alert in self._alerts if not alert.resolved]
    
    def _generate_alert_message(self, change: PerformanceChange) -> str:
        """生成告警消息"""
        return (f"性能回归检测: {change.metric_name} "
                f"从 {change.baseline_value:.2f} 变化到 {change.current_value:.2f} "
                f"({change.change_percent:+.1f}%), "
                f"严重程度: {change.severity.value}, "
                f"置信度: {change.confidence:.1%}")
    
    def _save_alert(self, alert: RegressionAlert) -> None:
        """保存告警到文件"""
        try:
            date_str = alert.created_at.strftime("%Y-%m-%d")
            file_path = self.storage_path / f"alerts_{date_str}.jsonl"
            
            with open(file_path, 'a', encoding='utf-8') as f:
                json.dump(alert.to_dict(), f, ensure_ascii=False)
                f.write('\n')
                
        except Exception as e:
            self.logger.error(f"Failed to save alert {alert.alert_id}: {e}")


class RegressionAnalyzer:
    """回归分析器"""
    
    def __init__(self, baseline_manager: BaselineManager):
        self.baseline_manager = baseline_manager
        self.logger = get_logger(f"{__name__}.RegressionAnalyzer")
    
    def analyze_trend(self, metric_name: str, values: List[float], 
                     timestamps: List[datetime]) -> Dict[str, Any]:
        """分析性能趋势"""
        try:
            if len(values) != len(timestamps) or len(values) < 2:
                return {"error": "Insufficient data for trend analysis"}
            
            # 计算趋势斜率
            x_values = [(ts - timestamps[0]).total_seconds() for ts in timestamps]
            slope = self._calculate_slope(x_values, values)
            
            # 计算变异系数
            mean_value = statistics.mean(values)
            std_dev = statistics.stdev(values) if len(values) > 1 else 0
            coefficient_of_variation = (std_dev / mean_value) if mean_value != 0 else 0
            
            # 趋势分类
            trend_direction = "increasing" if slope > 0 else "decreasing" if slope < 0 else "stable"
            
            return {
                "metric_name": metric_name,
                "trend_direction": trend_direction,
                "slope": slope,
                "mean_value": mean_value,
                "std_deviation": std_dev,
                "coefficient_of_variation": coefficient_of_variation,
                "min_value": min(values),
                "max_value": max(values),
                "sample_count": len(values),
                "time_span_hours": (timestamps[-1] - timestamps[0]).total_seconds() / 3600
            }
            
        except Exception as e:
            self.logger.error(f"Failed to analyze trend for {metric_name}: {e}")
            return {"error": str(e)}
    
    def _calculate_slope(self, x_values: List[float], y_values: List[float]) -> float:
        """计算线性回归斜率"""
        n = len(x_values)
        sum_x = sum(x_values)
        sum_y = sum(y_values)
        sum_xy = sum(x * y for x, y in zip(x_values, y_values))
        sum_x2 = sum(x * x for x in x_values)
        
        denominator = n * sum_x2 - sum_x * sum_x
        if denominator == 0:
            return 0
        
        slope = (n * sum_xy - sum_x * sum_y) / denominator
        return slope


class RegressionDetector:
    """性能回归检测主管理器"""
    
    def __init__(self):
        self.baseline_manager = BaselineManager()
        self.change_detector = ChangeDetector(self.baseline_manager)
        self.alert_generator = AlertGenerator()
        self.analyzer = RegressionAnalyzer(self.baseline_manager)
        
        # 日志和监控
        self.logger = get_logger(f"{__name__}.RegressionDetector")
        self.perf_logger = get_global_performance_logger()
        self.error_handler = get_global_error_handler()
        
        # 检测统计
        self._detection_count = 0
        self._regression_count = 0
        self._alert_count = 0
        self._lock = threading.RLock()
    
    @perf_logger.monitor
    async def detect_regression(self, metric_name: str, current_value: float) -> Optional[PerformanceChange]:
        """检测性能回归"""
        try:
            with self._lock:
                self._detection_count += 1
            
            # 检测变化
            change = self.change_detector.detect_change(metric_name, current_value)
            
            if change and change.change_type == ChangeType.DEGRADATION:
                with self._lock:
                    self._regression_count += 1
                
                # 生成告警
                alert = self.alert_generator.generate_alert(change)
                if alert:
                    with self._lock:
                        self._alert_count += 1
            
            return change
            
        except Exception as e:
            self.error_handler.handle_error(e, context={"metric_name": metric_name})
            self.logger.error(f"Failed to detect regression for {metric_name}: {e}")
            return None
    
    async def batch_detect_regression(self, metrics: Dict[str, float]) -> List[PerformanceChange]:
        """批量检测性能回归"""
        changes = []
        
        for metric_name, value in metrics.items():
            change = await self.detect_regression(metric_name, value)
            if change:
                changes.append(change)
        
        return changes
    
    def create_baseline_from_results(self, results: List[BenchmarkResult]) -> Dict[str, PerformanceBaseline]:
        """从基准测试结果创建基线"""
        baselines = {}
        
        # 按指标分组收集数据
        metrics_data = {}
        
        for result in results:
            if result.metrics:
                metrics_dict = result.metrics.to_dict()
                for metric_name, value in metrics_dict.items():
                    if isinstance(value, (int, float)):
                        if metric_name not in metrics_data:
                            metrics_data[metric_name] = []
                        metrics_data[metric_name].append(float(value))
        
        # 为每个指标创建基线
        for metric_name, values in metrics_data.items():
            if len(values) >= 3:  # 至少需要3个样本
                try:
                    baseline = self.baseline_manager.create_baseline(metric_name, values)
                    baselines[metric_name] = baseline
                except Exception as e:
                    self.logger.error(f"Failed to create baseline for {metric_name}: {e}")
        
        return baselines
    
    def get_detection_statistics(self) -> Dict[str, Any]:
        """获取检测统计信息"""
        with self._lock:
            return {
                "total_detections": self._detection_count,
                "total_regressions": self._regression_count,
                "total_alerts": self._alert_count,
                "regression_rate": self._regression_count / max(self._detection_count, 1),
                "alert_rate": self._alert_count / max(self._regression_count, 1),
                "active_alerts": len(self.alert_generator.get_active_alerts()),
                "baseline_count": len(self.baseline_manager.list_baselines())
            }


# 全局回归检测器实例
_global_regression_detector: Optional[RegressionDetector] = None


def get_global_regression_detector() -> RegressionDetector:
    """获取全局回归检测器实例"""
    global _global_regression_detector
    
    if _global_regression_detector is None:
        _global_regression_detector = RegressionDetector()
    
    return _global_regression_detector


# 模块级别的日志器
module_logger = get_logger(__name__)