package com.crypto.trading.sdk.response;

import com.crypto.trading.sdk.converter.JsonConverter;
import com.crypto.trading.sdk.exception.ApiException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.function.Consumer;

/**
 * 响应处理器实现类
 * 处理API响应和WebSocket消息
 */
@Component
public class ResponseHandlerImpl implements ResponseHandler {

    private static final Logger log = LoggerFactory.getLogger(ResponseHandlerImpl.class);

    /**
     * JSON转换器
     */
    private final JsonConverter jsonConverter;

    /**
     * 构造函数
     *
     * @param jsonConverter JSON转换器（注入JacksonJsonConverter）
     */
    @Autowired
    public ResponseHandlerImpl(@Qualifier("jacksonJsonConverter") JsonConverter jsonConverter) {
        this.jsonConverter = jsonConverter;
    }

    @Override
    public <T> T handleApiResponse(String response, Class<T> clazz) {
        try {
            // 检查响应是否包含错误
            checkResponseError(response);
            
            // 转换为目标对象
            return jsonConverter.fromJson(response, clazz);
        } catch (Exception e) {
            log.error("处理API响应异常: {}", response, e);
            throw e;
        }
    }

    @Override
    public <T> void handleApiResponseAsync(String response, Class<T> clazz, Consumer<T> onSuccess, Consumer<Exception> onError) {
        CompletableFuture.runAsync(() -> {
            try {
                T result = handleApiResponse(response, clazz);
                if (onSuccess != null) {
                    onSuccess.accept(result);
                }
            } catch (Exception e) {
                if (onError != null) {
                    onError.accept(e);
                } else {
                    log.error("异步处理API响应异常: {}", response, e);
                }
            }
        });
    }

    @Override
    public <T> T handleWebSocketMessage(String message, Class<T> clazz) {
        try {
            // 检查消息是否包含错误
            checkResponseError(message);
            
            // 添加调试日志
            log.debug("处理WebSocket消息: {}, 目标类型: {}", message, clazz.getName());
            
            // 转换为目标对象
            T result = jsonConverter.fromJson(message, clazz);
            
            // 添加调试日志
            log.debug("WebSocket消息处理结果: {}", result);
            
            return result;
        } catch (Exception e) {
            log.error("处理WebSocket消息异常: {}", message, e);
            throw e;
        }
    }

    /**
     * 检查响应是否包含错误
     *
     * @param response JSON响应字符串
     */
    private void checkResponseError(String response) {
        // 检查响应是否为空
        if (response == null || response.isEmpty()) {
            throw new ApiException(1001, "空响应");
        }

        // 尝试解析为Map检查是否有错误码
        try {
            Map<String, Object> map = jsonConverter.jsonToMap(response);
            if (map.containsKey("code") && map.containsKey("msg")) {
                int code = Integer.parseInt(map.get("code").toString());
                String msg = map.get("msg").toString();
                
                // 如果code不为0或200，则表示有错误
                if (code != 0 && code != 200) {
                    throw new ApiException(code, msg);
                }
            }
        } catch (NumberFormatException | ClassCastException e) {
            // 忽略解析错误，因为响应可能不是错误格式
        }
    }
} 