package com.crypto.trading.trade.consumer;

import com.crypto.trading.trade.model.avro.TradingSignal;
import com.crypto.trading.trade.service.SignalProcessingService;
import com.crypto.trading.trade.service.SignalValidationService;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import com.crypto.trading.common.util.ExceptionUtil;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.Instant;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 交易信号消费者
 * 
 * <p>从Kafka消费交易信号，并进行验证和处理</p>
 */
@Component
public class TradingSignalConsumer {

    private static final Logger log = LoggerFactory.getLogger(TradingSignalConsumer.class);

    @Autowired
    private SignalValidationService signalValidationService;

    @Autowired
    private SignalProcessingService signalProcessingService;

    @Autowired
    @Qualifier("strategySignalTopic")
    private String strategySignalTopic;

    @Value("${spring.kafka.consumer.group-id}")
    private String consumerGroupId;

    private final ConcurrentHashMap<String, Long> processedSignals = new ConcurrentHashMap<>();
    private final AtomicLong totalMessages = new AtomicLong(0);
    private final AtomicLong validMessages = new AtomicLong(0);
    private final AtomicLong invalidMessages = new AtomicLong(0);
    private final AtomicLong processedMessages = new AtomicLong(0);
    private final AtomicLong failedMessages = new AtomicLong(0);

    @Autowired(required = false)
    private MeterRegistry meterRegistry;

    /**
     * 消费交易信号
     *
     * @param record Kafka消息记录
     * @param acknowledgment 手动确认
     */
    @KafkaListener(topics = "${crypto.trade.kafka.topics.strategy-signal}", 
                  groupId = "${spring.kafka.consumer.group-id}",
                  containerFactory = "kafkaListenerContainerFactory")
    public void consume(ConsumerRecord<String, TradingSignal> record, Acknowledgment acknowledgment) {
        Instant startTime = Instant.now();
        
        String messageKey = Optional.ofNullable(record.key()).orElse("null");
        TradingSignal signal = record.value();
        
        totalMessages.incrementAndGet();

        // 检查空值
        if (signal == null) {
            log.warn("收到空的交易信号消息: topic={}, partition={}, offset={}, key={}",
                    record.topic(), record.partition(), record.offset(), messageKey);
            acknowledgment.acknowledge();
            invalidMessages.incrementAndGet();
            recordMetrics("empty", startTime, false);
            return;
        }

        String signalId = signal.getId();
        try {
            log.info("收到交易信号: signalId={}, symbol={}, type={}, topic={}, partition={}, offset={}",
                    signalId, signal.getSymbol(), signal.getSignalType(),
                    record.topic(), record.partition(), record.offset());

            // 检查是否重复处理
            if (isDuplicate(signalId)) {
                log.warn("重复的交易信号，忽略处理: signalId={}", signalId);
                acknowledgment.acknowledge();
                recordMetrics("duplicate", startTime, true);
                return;
            }

            // 信号验证
            SignalValidationService.ValidationResult validationResult = signalValidationService.validateSignal(signal);
            
            if (validationResult.isValid()) {
                validMessages.incrementAndGet();
                
                // 处理有效信号
                com.crypto.trading.trade.model.result.SignalProcessResult processResult = 
                        signalProcessingService.processSignal(validationResult);
                
                if (processResult.isSuccess()) {
                    processedMessages.incrementAndGet();
                    log.info("交易信号处理成功: signalId={}, message={}", signalId, processResult.getMessage());
                } else {
                    failedMessages.incrementAndGet();
                    log.warn("交易信号处理失败: signalId={}, message={}", signalId, processResult.getMessage());
                }
                
                // 记录处理时间指标
                recordMetrics("process", startTime, processResult.isSuccess());
                
            } else {
                invalidMessages.incrementAndGet();
                log.warn("无效的交易信号: signalId={}, error={}", signalId, validationResult.getErrorMessage());
                recordMetrics("invalid", startTime, false);
            }

            // 记录已处理的信号ID，防止重复处理
            markAsProcessed(signalId);
            
            // 确认消息
            acknowledgment.acknowledge();
            
        } catch (Exception e) {
            failedMessages.incrementAndGet();
            log.error("处理交易信号异常: signalId={}, error={}, stacktrace={}",
                    signalId, e.getMessage(), ExceptionUtil.getStackTrace(e));
            
            // 如果是可恢复的异常，可以考虑不确认消息，让Kafka重新投递
            // 但这里我们选择确认消息，因为信号已经保存到数据库，可以通过其他方式恢复
            acknowledgment.acknowledge();
            
            recordMetrics("error", startTime, false);
        }
    }

    /**
     * 检查信号是否已处理
     *
     * @param signalId 信号ID
     * @return 是否重复
     */
    private boolean isDuplicate(String signalId) {
        return processedSignals.containsKey(signalId);
    }

    /**
     * 标记信号为已处理
     *
     * @param signalId 信号ID
     */
    private void markAsProcessed(String signalId) {
        // 保存信号ID和处理时间戳
        processedSignals.put(signalId, System.currentTimeMillis());
        
        // 清理太旧的记录，避免内存泄漏
        // 只保留最近1小时的记录
        long cutoffTime = System.currentTimeMillis() - TimeUnit.HOURS.toMillis(1);
        processedSignals.entrySet().removeIf(entry -> entry.getValue() < cutoffTime);
    }

    /**
     * 记录指标
     *
     * @param operation 操作类型
     * @param startTime 开始时间
     * @param success 是否成功
     */
    private void recordMetrics(String operation, Instant startTime, boolean success) {
        if (meterRegistry != null) {
            long latencyMs = Duration.between(startTime, Instant.now()).toMillis();
            
            // 记录消息处理延迟
            Timer.builder("crypto.trade.signal.latency")
                    .tag("operation", operation)
                    .tag("success", String.valueOf(success))
                    .tag("topic", strategySignalTopic)
                    .tag("consumer", consumerGroupId)
                    .register(meterRegistry)
                    .record(latencyMs, TimeUnit.MILLISECONDS);
            
            // 记录消息计数
            meterRegistry.counter("crypto.trade.signal.count", 
                    "operation", operation, 
                    "success", String.valueOf(success),
                    "topic", strategySignalTopic,
                    "consumer", consumerGroupId)
                    .increment();
        }
    }

    /**
     * 获取总处理消息数
     *
     * @return 总消息数
     */
    public long getTotalMessages() {
        return totalMessages.get();
    }

    /**
     * 获取有效消息数
     *
     * @return 有效消息数
     */
    public long getValidMessages() {
        return validMessages.get();
    }

    /**
     * 获取无效消息数
     *
     * @return 无效消息数
     */
    public long getInvalidMessages() {
        return invalidMessages.get();
    }

    /**
     * 获取成功处理消息数
     *
     * @return 成功处理消息数
     */
    public long getProcessedMessages() {
        return processedMessages.get();
    }

    /**
     * 获取处理失败消息数
     *
     * @return 处理失败消息数
     */
    public long getFailedMessages() {
        return failedMessages.get();
    }
}