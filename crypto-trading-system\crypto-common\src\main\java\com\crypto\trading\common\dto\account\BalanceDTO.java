package com.crypto.trading.common.dto.account;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 账户余额传输对象
 * <p>
 * 用于在系统各层之间传递账户余额信息
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class BalanceDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 账户ID
     */
    private String accountId;

    /**
     * 资产名称（例如：BTC, ETH, USDT）
     */
    private String asset;

    /**
     * 总余额
     */
    private BigDecimal total;

    /**
     * 可用余额
     */
    private BigDecimal free;

    /**
     * 冻结余额
     */
    private BigDecimal locked;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 构造函数
     */
    public BalanceDTO() {
    }

    /**
     * 构造函数
     *
     * @param accountId  账户ID
     * @param asset      资产名称
     * @param total      总余额
     * @param free       可用余额
     * @param locked     冻结余额
     * @param updateTime 更新时间
     */
    public BalanceDTO(String accountId, String asset, BigDecimal total, BigDecimal free, BigDecimal locked,
                      LocalDateTime updateTime) {
        this.accountId = accountId;
        this.asset = asset;
        this.total = total;
        this.free = free;
        this.locked = locked;
        this.updateTime = updateTime;
    }

    /**
     * 获取账户ID
     *
     * @return 账户ID
     */
    public String getAccountId() {
        return accountId;
    }

    /**
     * 设置账户ID
     *
     * @param accountId 账户ID
     */
    public void setAccountId(String accountId) {
        this.accountId = accountId;
    }

    /**
     * 获取资产名称
     *
     * @return 资产名称
     */
    public String getAsset() {
        return asset;
    }

    /**
     * 设置资产名称
     *
     * @param asset 资产名称
     */
    public void setAsset(String asset) {
        this.asset = asset;
    }

    /**
     * 获取总余额
     *
     * @return 总余额
     */
    public BigDecimal getTotal() {
        return total;
    }

    /**
     * 设置总余额
     *
     * @param total 总余额
     */
    public void setTotal(BigDecimal total) {
        this.total = total;
    }

    /**
     * 获取可用余额
     *
     * @return 可用余额
     */
    public BigDecimal getFree() {
        return free;
    }

    /**
     * 设置可用余额
     *
     * @param free 可用余额
     */
    public void setFree(BigDecimal free) {
        this.free = free;
    }

    /**
     * 获取冻结余额
     *
     * @return 冻结余额
     */
    public BigDecimal getLocked() {
        return locked;
    }

    /**
     * 设置冻结余额
     *
     * @param locked 冻结余额
     */
    public void setLocked(BigDecimal locked) {
        this.locked = locked;
    }

    /**
     * 获取更新时间
     *
     * @return 更新时间
     */
    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "BalanceDTO{" +
                "accountId='" + accountId + '\'' +
                ", asset='" + asset + '\'' +
                ", total=" + total +
                ", free=" + free +
                ", locked=" + locked +
                ", updateTime=" + updateTime +
                '}';
    }
} 