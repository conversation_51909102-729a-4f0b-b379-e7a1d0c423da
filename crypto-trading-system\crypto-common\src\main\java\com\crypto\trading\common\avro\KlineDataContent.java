/**
 * Autogenerated by Avro
 *
 * DO NOT EDIT DIRECTLY
 */
package com.crypto.trading.common.avro;

import org.apache.avro.generic.GenericArray;
import org.apache.avro.specific.SpecificData;
import org.apache.avro.util.Utf8;
import org.apache.avro.message.BinaryMessageEncoder;
import org.apache.avro.message.BinaryMessageDecoder;
import org.apache.avro.message.SchemaStore;

@org.apache.avro.specific.AvroGenerated
public class KlineDataContent extends org.apache.avro.specific.SpecificRecordBase implements org.apache.avro.specific.SpecificRecord {
  private static final long serialVersionUID = 3495752835905396553L;


  public static final org.apache.avro.Schema SCHEMA$ = new org.apache.avro.Schema.Parser().parse("{\"type\":\"record\",\"name\":\"KlineDataContent\",\"namespace\":\"com.crypto.trading.common.avro\",\"fields\":[{\"name\":\"symbol\",\"type\":{\"type\":\"string\",\"avro.java.string\":\"String\"},\"doc\":\"交易对符号\"},{\"name\":\"interval\",\"type\":{\"type\":\"string\",\"avro.java.string\":\"String\"},\"doc\":\"K线间隔\"},{\"name\":\"openTime\",\"type\":\"long\",\"doc\":\"开盘时间(毫秒)\"},{\"name\":\"closeTime\",\"type\":\"long\",\"doc\":\"收盘时间(毫秒)\"},{\"name\":\"open\",\"type\":\"double\",\"doc\":\"开盘价\"},{\"name\":\"high\",\"type\":\"double\",\"doc\":\"最高价\"},{\"name\":\"low\",\"type\":\"double\",\"doc\":\"最低价\"},{\"name\":\"close\",\"type\":\"double\",\"doc\":\"收盘价\"},{\"name\":\"volume\",\"type\":\"double\",\"doc\":\"交易量\"},{\"name\":\"quoteVolume\",\"type\":\"double\",\"doc\":\"报价资产交易量\"},{\"name\":\"trades\",\"type\":\"int\",\"doc\":\"交易笔数\"},{\"name\":\"completed\",\"type\":\"boolean\",\"doc\":\"是否已完成\"}]}");
  public static org.apache.avro.Schema getClassSchema() { return SCHEMA$; }

  private static final SpecificData MODEL$ = new SpecificData();

  private static final BinaryMessageEncoder<KlineDataContent> ENCODER =
      new BinaryMessageEncoder<>(MODEL$, SCHEMA$);

  private static final BinaryMessageDecoder<KlineDataContent> DECODER =
      new BinaryMessageDecoder<>(MODEL$, SCHEMA$);

  /**
   * Return the BinaryMessageEncoder instance used by this class.
   * @return the message encoder used by this class
   */
  public static BinaryMessageEncoder<KlineDataContent> getEncoder() {
    return ENCODER;
  }

  /**
   * Return the BinaryMessageDecoder instance used by this class.
   * @return the message decoder used by this class
   */
  public static BinaryMessageDecoder<KlineDataContent> getDecoder() {
    return DECODER;
  }

  /**
   * Create a new BinaryMessageDecoder instance for this class that uses the specified {@link SchemaStore}.
   * @param resolver a {@link SchemaStore} used to find schemas by fingerprint
   * @return a BinaryMessageDecoder instance for this class backed by the given SchemaStore
   */
  public static BinaryMessageDecoder<KlineDataContent> createDecoder(SchemaStore resolver) {
    return new BinaryMessageDecoder<>(MODEL$, SCHEMA$, resolver);
  }

  /**
   * Serializes this KlineDataContent to a ByteBuffer.
   * @return a buffer holding the serialized data for this instance
   * @throws java.io.IOException if this instance could not be serialized
   */
  public java.nio.ByteBuffer toByteBuffer() throws java.io.IOException {
    return ENCODER.encode(this);
  }

  /**
   * Deserializes a KlineDataContent from a ByteBuffer.
   * @param b a byte buffer holding serialized data for an instance of this class
   * @return a KlineDataContent instance decoded from the given buffer
   * @throws java.io.IOException if the given bytes could not be deserialized into an instance of this class
   */
  public static KlineDataContent fromByteBuffer(
      java.nio.ByteBuffer b) throws java.io.IOException {
    return DECODER.decode(b);
  }

  /** 交易对符号 */
  private java.lang.String symbol;
  /** K线间隔 */
  private java.lang.String interval;
  /** 开盘时间(毫秒) */
  private long openTime;
  /** 收盘时间(毫秒) */
  private long closeTime;
  /** 开盘价 */
  private double open;
  /** 最高价 */
  private double high;
  /** 最低价 */
  private double low;
  /** 收盘价 */
  private double close;
  /** 交易量 */
  private double volume;
  /** 报价资产交易量 */
  private double quoteVolume;
  /** 交易笔数 */
  private int trades;
  /** 是否已完成 */
  private boolean completed;

  /**
   * Default constructor.  Note that this does not initialize fields
   * to their default values from the schema.  If that is desired then
   * one should use <code>newBuilder()</code>.
   */
  public KlineDataContent() {}

  /**
   * All-args constructor.
   * @param symbol 交易对符号
   * @param interval K线间隔
   * @param openTime 开盘时间(毫秒)
   * @param closeTime 收盘时间(毫秒)
   * @param open 开盘价
   * @param high 最高价
   * @param low 最低价
   * @param close 收盘价
   * @param volume 交易量
   * @param quoteVolume 报价资产交易量
   * @param trades 交易笔数
   * @param completed 是否已完成
   */
  public KlineDataContent(java.lang.String symbol, java.lang.String interval, java.lang.Long openTime, java.lang.Long closeTime, java.lang.Double open, java.lang.Double high, java.lang.Double low, java.lang.Double close, java.lang.Double volume, java.lang.Double quoteVolume, java.lang.Integer trades, java.lang.Boolean completed) {
    this.symbol = symbol;
    this.interval = interval;
    this.openTime = openTime;
    this.closeTime = closeTime;
    this.open = open;
    this.high = high;
    this.low = low;
    this.close = close;
    this.volume = volume;
    this.quoteVolume = quoteVolume;
    this.trades = trades;
    this.completed = completed;
  }

  @Override
  public org.apache.avro.specific.SpecificData getSpecificData() { return MODEL$; }

  @Override
  public org.apache.avro.Schema getSchema() { return SCHEMA$; }

  // Used by DatumWriter.  Applications should not call.
  @Override
  public java.lang.Object get(int field$) {
    switch (field$) {
    case 0: return symbol;
    case 1: return interval;
    case 2: return openTime;
    case 3: return closeTime;
    case 4: return open;
    case 5: return high;
    case 6: return low;
    case 7: return close;
    case 8: return volume;
    case 9: return quoteVolume;
    case 10: return trades;
    case 11: return completed;
    default: throw new IndexOutOfBoundsException("Invalid index: " + field$);
    }
  }

  // Used by DatumReader.  Applications should not call.
  @Override
  @SuppressWarnings(value="unchecked")
  public void put(int field$, java.lang.Object value$) {
    switch (field$) {
    case 0: symbol = value$ != null ? value$.toString() : null; break;
    case 1: interval = value$ != null ? value$.toString() : null; break;
    case 2: openTime = (java.lang.Long)value$; break;
    case 3: closeTime = (java.lang.Long)value$; break;
    case 4: open = (java.lang.Double)value$; break;
    case 5: high = (java.lang.Double)value$; break;
    case 6: low = (java.lang.Double)value$; break;
    case 7: close = (java.lang.Double)value$; break;
    case 8: volume = (java.lang.Double)value$; break;
    case 9: quoteVolume = (java.lang.Double)value$; break;
    case 10: trades = (java.lang.Integer)value$; break;
    case 11: completed = (java.lang.Boolean)value$; break;
    default: throw new IndexOutOfBoundsException("Invalid index: " + field$);
    }
  }

  /**
   * Gets the value of the 'symbol' field.
   * @return 交易对符号
   */
  public java.lang.String getSymbol() {
    return symbol;
  }


  /**
   * Sets the value of the 'symbol' field.
   * 交易对符号
   * @param value the value to set.
   */
  public void setSymbol(java.lang.String value) {
    this.symbol = value;
  }

  /**
   * Gets the value of the 'interval' field.
   * @return K线间隔
   */
  public java.lang.String getInterval() {
    return interval;
  }


  /**
   * Sets the value of the 'interval' field.
   * K线间隔
   * @param value the value to set.
   */
  public void setInterval(java.lang.String value) {
    this.interval = value;
  }

  /**
   * Gets the value of the 'openTime' field.
   * @return 开盘时间(毫秒)
   */
  public long getOpenTime() {
    return openTime;
  }


  /**
   * Sets the value of the 'openTime' field.
   * 开盘时间(毫秒)
   * @param value the value to set.
   */
  public void setOpenTime(long value) {
    this.openTime = value;
  }

  /**
   * Gets the value of the 'closeTime' field.
   * @return 收盘时间(毫秒)
   */
  public long getCloseTime() {
    return closeTime;
  }


  /**
   * Sets the value of the 'closeTime' field.
   * 收盘时间(毫秒)
   * @param value the value to set.
   */
  public void setCloseTime(long value) {
    this.closeTime = value;
  }

  /**
   * Gets the value of the 'open' field.
   * @return 开盘价
   */
  public double getOpen() {
    return open;
  }


  /**
   * Sets the value of the 'open' field.
   * 开盘价
   * @param value the value to set.
   */
  public void setOpen(double value) {
    this.open = value;
  }

  /**
   * Gets the value of the 'high' field.
   * @return 最高价
   */
  public double getHigh() {
    return high;
  }


  /**
   * Sets the value of the 'high' field.
   * 最高价
   * @param value the value to set.
   */
  public void setHigh(double value) {
    this.high = value;
  }

  /**
   * Gets the value of the 'low' field.
   * @return 最低价
   */
  public double getLow() {
    return low;
  }


  /**
   * Sets the value of the 'low' field.
   * 最低价
   * @param value the value to set.
   */
  public void setLow(double value) {
    this.low = value;
  }

  /**
   * Gets the value of the 'close' field.
   * @return 收盘价
   */
  public double getClose() {
    return close;
  }


  /**
   * Sets the value of the 'close' field.
   * 收盘价
   * @param value the value to set.
   */
  public void setClose(double value) {
    this.close = value;
  }

  /**
   * Gets the value of the 'volume' field.
   * @return 交易量
   */
  public double getVolume() {
    return volume;
  }


  /**
   * Sets the value of the 'volume' field.
   * 交易量
   * @param value the value to set.
   */
  public void setVolume(double value) {
    this.volume = value;
  }

  /**
   * Gets the value of the 'quoteVolume' field.
   * @return 报价资产交易量
   */
  public double getQuoteVolume() {
    return quoteVolume;
  }


  /**
   * Sets the value of the 'quoteVolume' field.
   * 报价资产交易量
   * @param value the value to set.
   */
  public void setQuoteVolume(double value) {
    this.quoteVolume = value;
  }

  /**
   * Gets the value of the 'trades' field.
   * @return 交易笔数
   */
  public int getTrades() {
    return trades;
  }


  /**
   * Sets the value of the 'trades' field.
   * 交易笔数
   * @param value the value to set.
   */
  public void setTrades(int value) {
    this.trades = value;
  }

  /**
   * Gets the value of the 'completed' field.
   * @return 是否已完成
   */
  public boolean getCompleted() {
    return completed;
  }


  /**
   * Sets the value of the 'completed' field.
   * 是否已完成
   * @param value the value to set.
   */
  public void setCompleted(boolean value) {
    this.completed = value;
  }

  /**
   * Creates a new KlineDataContent RecordBuilder.
   * @return A new KlineDataContent RecordBuilder
   */
  public static com.crypto.trading.common.avro.KlineDataContent.Builder newBuilder() {
    return new com.crypto.trading.common.avro.KlineDataContent.Builder();
  }

  /**
   * Creates a new KlineDataContent RecordBuilder by copying an existing Builder.
   * @param other The existing builder to copy.
   * @return A new KlineDataContent RecordBuilder
   */
  public static com.crypto.trading.common.avro.KlineDataContent.Builder newBuilder(com.crypto.trading.common.avro.KlineDataContent.Builder other) {
    if (other == null) {
      return new com.crypto.trading.common.avro.KlineDataContent.Builder();
    } else {
      return new com.crypto.trading.common.avro.KlineDataContent.Builder(other);
    }
  }

  /**
   * Creates a new KlineDataContent RecordBuilder by copying an existing KlineDataContent instance.
   * @param other The existing instance to copy.
   * @return A new KlineDataContent RecordBuilder
   */
  public static com.crypto.trading.common.avro.KlineDataContent.Builder newBuilder(com.crypto.trading.common.avro.KlineDataContent other) {
    if (other == null) {
      return new com.crypto.trading.common.avro.KlineDataContent.Builder();
    } else {
      return new com.crypto.trading.common.avro.KlineDataContent.Builder(other);
    }
  }

  /**
   * RecordBuilder for KlineDataContent instances.
   */
  @org.apache.avro.specific.AvroGenerated
  public static class Builder extends org.apache.avro.specific.SpecificRecordBuilderBase<KlineDataContent>
    implements org.apache.avro.data.RecordBuilder<KlineDataContent> {

    /** 交易对符号 */
    private java.lang.String symbol;
    /** K线间隔 */
    private java.lang.String interval;
    /** 开盘时间(毫秒) */
    private long openTime;
    /** 收盘时间(毫秒) */
    private long closeTime;
    /** 开盘价 */
    private double open;
    /** 最高价 */
    private double high;
    /** 最低价 */
    private double low;
    /** 收盘价 */
    private double close;
    /** 交易量 */
    private double volume;
    /** 报价资产交易量 */
    private double quoteVolume;
    /** 交易笔数 */
    private int trades;
    /** 是否已完成 */
    private boolean completed;

    /** Creates a new Builder */
    private Builder() {
      super(SCHEMA$, MODEL$);
    }

    /**
     * Creates a Builder by copying an existing Builder.
     * @param other The existing Builder to copy.
     */
    private Builder(com.crypto.trading.common.avro.KlineDataContent.Builder other) {
      super(other);
      if (isValidValue(fields()[0], other.symbol)) {
        this.symbol = data().deepCopy(fields()[0].schema(), other.symbol);
        fieldSetFlags()[0] = other.fieldSetFlags()[0];
      }
      if (isValidValue(fields()[1], other.interval)) {
        this.interval = data().deepCopy(fields()[1].schema(), other.interval);
        fieldSetFlags()[1] = other.fieldSetFlags()[1];
      }
      if (isValidValue(fields()[2], other.openTime)) {
        this.openTime = data().deepCopy(fields()[2].schema(), other.openTime);
        fieldSetFlags()[2] = other.fieldSetFlags()[2];
      }
      if (isValidValue(fields()[3], other.closeTime)) {
        this.closeTime = data().deepCopy(fields()[3].schema(), other.closeTime);
        fieldSetFlags()[3] = other.fieldSetFlags()[3];
      }
      if (isValidValue(fields()[4], other.open)) {
        this.open = data().deepCopy(fields()[4].schema(), other.open);
        fieldSetFlags()[4] = other.fieldSetFlags()[4];
      }
      if (isValidValue(fields()[5], other.high)) {
        this.high = data().deepCopy(fields()[5].schema(), other.high);
        fieldSetFlags()[5] = other.fieldSetFlags()[5];
      }
      if (isValidValue(fields()[6], other.low)) {
        this.low = data().deepCopy(fields()[6].schema(), other.low);
        fieldSetFlags()[6] = other.fieldSetFlags()[6];
      }
      if (isValidValue(fields()[7], other.close)) {
        this.close = data().deepCopy(fields()[7].schema(), other.close);
        fieldSetFlags()[7] = other.fieldSetFlags()[7];
      }
      if (isValidValue(fields()[8], other.volume)) {
        this.volume = data().deepCopy(fields()[8].schema(), other.volume);
        fieldSetFlags()[8] = other.fieldSetFlags()[8];
      }
      if (isValidValue(fields()[9], other.quoteVolume)) {
        this.quoteVolume = data().deepCopy(fields()[9].schema(), other.quoteVolume);
        fieldSetFlags()[9] = other.fieldSetFlags()[9];
      }
      if (isValidValue(fields()[10], other.trades)) {
        this.trades = data().deepCopy(fields()[10].schema(), other.trades);
        fieldSetFlags()[10] = other.fieldSetFlags()[10];
      }
      if (isValidValue(fields()[11], other.completed)) {
        this.completed = data().deepCopy(fields()[11].schema(), other.completed);
        fieldSetFlags()[11] = other.fieldSetFlags()[11];
      }
    }

    /**
     * Creates a Builder by copying an existing KlineDataContent instance
     * @param other The existing instance to copy.
     */
    private Builder(com.crypto.trading.common.avro.KlineDataContent other) {
      super(SCHEMA$, MODEL$);
      if (isValidValue(fields()[0], other.symbol)) {
        this.symbol = data().deepCopy(fields()[0].schema(), other.symbol);
        fieldSetFlags()[0] = true;
      }
      if (isValidValue(fields()[1], other.interval)) {
        this.interval = data().deepCopy(fields()[1].schema(), other.interval);
        fieldSetFlags()[1] = true;
      }
      if (isValidValue(fields()[2], other.openTime)) {
        this.openTime = data().deepCopy(fields()[2].schema(), other.openTime);
        fieldSetFlags()[2] = true;
      }
      if (isValidValue(fields()[3], other.closeTime)) {
        this.closeTime = data().deepCopy(fields()[3].schema(), other.closeTime);
        fieldSetFlags()[3] = true;
      }
      if (isValidValue(fields()[4], other.open)) {
        this.open = data().deepCopy(fields()[4].schema(), other.open);
        fieldSetFlags()[4] = true;
      }
      if (isValidValue(fields()[5], other.high)) {
        this.high = data().deepCopy(fields()[5].schema(), other.high);
        fieldSetFlags()[5] = true;
      }
      if (isValidValue(fields()[6], other.low)) {
        this.low = data().deepCopy(fields()[6].schema(), other.low);
        fieldSetFlags()[6] = true;
      }
      if (isValidValue(fields()[7], other.close)) {
        this.close = data().deepCopy(fields()[7].schema(), other.close);
        fieldSetFlags()[7] = true;
      }
      if (isValidValue(fields()[8], other.volume)) {
        this.volume = data().deepCopy(fields()[8].schema(), other.volume);
        fieldSetFlags()[8] = true;
      }
      if (isValidValue(fields()[9], other.quoteVolume)) {
        this.quoteVolume = data().deepCopy(fields()[9].schema(), other.quoteVolume);
        fieldSetFlags()[9] = true;
      }
      if (isValidValue(fields()[10], other.trades)) {
        this.trades = data().deepCopy(fields()[10].schema(), other.trades);
        fieldSetFlags()[10] = true;
      }
      if (isValidValue(fields()[11], other.completed)) {
        this.completed = data().deepCopy(fields()[11].schema(), other.completed);
        fieldSetFlags()[11] = true;
      }
    }

    /**
      * Gets the value of the 'symbol' field.
      * 交易对符号
      * @return The value.
      */
    public java.lang.String getSymbol() {
      return symbol;
    }


    /**
      * Sets the value of the 'symbol' field.
      * 交易对符号
      * @param value The value of 'symbol'.
      * @return This builder.
      */
    public com.crypto.trading.common.avro.KlineDataContent.Builder setSymbol(java.lang.String value) {
      validate(fields()[0], value);
      this.symbol = value;
      fieldSetFlags()[0] = true;
      return this;
    }

    /**
      * Checks whether the 'symbol' field has been set.
      * 交易对符号
      * @return True if the 'symbol' field has been set, false otherwise.
      */
    public boolean hasSymbol() {
      return fieldSetFlags()[0];
    }


    /**
      * Clears the value of the 'symbol' field.
      * 交易对符号
      * @return This builder.
      */
    public com.crypto.trading.common.avro.KlineDataContent.Builder clearSymbol() {
      symbol = null;
      fieldSetFlags()[0] = false;
      return this;
    }

    /**
      * Gets the value of the 'interval' field.
      * K线间隔
      * @return The value.
      */
    public java.lang.String getInterval() {
      return interval;
    }


    /**
      * Sets the value of the 'interval' field.
      * K线间隔
      * @param value The value of 'interval'.
      * @return This builder.
      */
    public com.crypto.trading.common.avro.KlineDataContent.Builder setInterval(java.lang.String value) {
      validate(fields()[1], value);
      this.interval = value;
      fieldSetFlags()[1] = true;
      return this;
    }

    /**
      * Checks whether the 'interval' field has been set.
      * K线间隔
      * @return True if the 'interval' field has been set, false otherwise.
      */
    public boolean hasInterval() {
      return fieldSetFlags()[1];
    }


    /**
      * Clears the value of the 'interval' field.
      * K线间隔
      * @return This builder.
      */
    public com.crypto.trading.common.avro.KlineDataContent.Builder clearInterval() {
      interval = null;
      fieldSetFlags()[1] = false;
      return this;
    }

    /**
      * Gets the value of the 'openTime' field.
      * 开盘时间(毫秒)
      * @return The value.
      */
    public long getOpenTime() {
      return openTime;
    }


    /**
      * Sets the value of the 'openTime' field.
      * 开盘时间(毫秒)
      * @param value The value of 'openTime'.
      * @return This builder.
      */
    public com.crypto.trading.common.avro.KlineDataContent.Builder setOpenTime(long value) {
      validate(fields()[2], value);
      this.openTime = value;
      fieldSetFlags()[2] = true;
      return this;
    }

    /**
      * Checks whether the 'openTime' field has been set.
      * 开盘时间(毫秒)
      * @return True if the 'openTime' field has been set, false otherwise.
      */
    public boolean hasOpenTime() {
      return fieldSetFlags()[2];
    }


    /**
      * Clears the value of the 'openTime' field.
      * 开盘时间(毫秒)
      * @return This builder.
      */
    public com.crypto.trading.common.avro.KlineDataContent.Builder clearOpenTime() {
      fieldSetFlags()[2] = false;
      return this;
    }

    /**
      * Gets the value of the 'closeTime' field.
      * 收盘时间(毫秒)
      * @return The value.
      */
    public long getCloseTime() {
      return closeTime;
    }


    /**
      * Sets the value of the 'closeTime' field.
      * 收盘时间(毫秒)
      * @param value The value of 'closeTime'.
      * @return This builder.
      */
    public com.crypto.trading.common.avro.KlineDataContent.Builder setCloseTime(long value) {
      validate(fields()[3], value);
      this.closeTime = value;
      fieldSetFlags()[3] = true;
      return this;
    }

    /**
      * Checks whether the 'closeTime' field has been set.
      * 收盘时间(毫秒)
      * @return True if the 'closeTime' field has been set, false otherwise.
      */
    public boolean hasCloseTime() {
      return fieldSetFlags()[3];
    }


    /**
      * Clears the value of the 'closeTime' field.
      * 收盘时间(毫秒)
      * @return This builder.
      */
    public com.crypto.trading.common.avro.KlineDataContent.Builder clearCloseTime() {
      fieldSetFlags()[3] = false;
      return this;
    }

    /**
      * Gets the value of the 'open' field.
      * 开盘价
      * @return The value.
      */
    public double getOpen() {
      return open;
    }


    /**
      * Sets the value of the 'open' field.
      * 开盘价
      * @param value The value of 'open'.
      * @return This builder.
      */
    public com.crypto.trading.common.avro.KlineDataContent.Builder setOpen(double value) {
      validate(fields()[4], value);
      this.open = value;
      fieldSetFlags()[4] = true;
      return this;
    }

    /**
      * Checks whether the 'open' field has been set.
      * 开盘价
      * @return True if the 'open' field has been set, false otherwise.
      */
    public boolean hasOpen() {
      return fieldSetFlags()[4];
    }


    /**
      * Clears the value of the 'open' field.
      * 开盘价
      * @return This builder.
      */
    public com.crypto.trading.common.avro.KlineDataContent.Builder clearOpen() {
      fieldSetFlags()[4] = false;
      return this;
    }

    /**
      * Gets the value of the 'high' field.
      * 最高价
      * @return The value.
      */
    public double getHigh() {
      return high;
    }


    /**
      * Sets the value of the 'high' field.
      * 最高价
      * @param value The value of 'high'.
      * @return This builder.
      */
    public com.crypto.trading.common.avro.KlineDataContent.Builder setHigh(double value) {
      validate(fields()[5], value);
      this.high = value;
      fieldSetFlags()[5] = true;
      return this;
    }

    /**
      * Checks whether the 'high' field has been set.
      * 最高价
      * @return True if the 'high' field has been set, false otherwise.
      */
    public boolean hasHigh() {
      return fieldSetFlags()[5];
    }


    /**
      * Clears the value of the 'high' field.
      * 最高价
      * @return This builder.
      */
    public com.crypto.trading.common.avro.KlineDataContent.Builder clearHigh() {
      fieldSetFlags()[5] = false;
      return this;
    }

    /**
      * Gets the value of the 'low' field.
      * 最低价
      * @return The value.
      */
    public double getLow() {
      return low;
    }


    /**
      * Sets the value of the 'low' field.
      * 最低价
      * @param value The value of 'low'.
      * @return This builder.
      */
    public com.crypto.trading.common.avro.KlineDataContent.Builder setLow(double value) {
      validate(fields()[6], value);
      this.low = value;
      fieldSetFlags()[6] = true;
      return this;
    }

    /**
      * Checks whether the 'low' field has been set.
      * 最低价
      * @return True if the 'low' field has been set, false otherwise.
      */
    public boolean hasLow() {
      return fieldSetFlags()[6];
    }


    /**
      * Clears the value of the 'low' field.
      * 最低价
      * @return This builder.
      */
    public com.crypto.trading.common.avro.KlineDataContent.Builder clearLow() {
      fieldSetFlags()[6] = false;
      return this;
    }

    /**
      * Gets the value of the 'close' field.
      * 收盘价
      * @return The value.
      */
    public double getClose() {
      return close;
    }


    /**
      * Sets the value of the 'close' field.
      * 收盘价
      * @param value The value of 'close'.
      * @return This builder.
      */
    public com.crypto.trading.common.avro.KlineDataContent.Builder setClose(double value) {
      validate(fields()[7], value);
      this.close = value;
      fieldSetFlags()[7] = true;
      return this;
    }

    /**
      * Checks whether the 'close' field has been set.
      * 收盘价
      * @return True if the 'close' field has been set, false otherwise.
      */
    public boolean hasClose() {
      return fieldSetFlags()[7];
    }


    /**
      * Clears the value of the 'close' field.
      * 收盘价
      * @return This builder.
      */
    public com.crypto.trading.common.avro.KlineDataContent.Builder clearClose() {
      fieldSetFlags()[7] = false;
      return this;
    }

    /**
      * Gets the value of the 'volume' field.
      * 交易量
      * @return The value.
      */
    public double getVolume() {
      return volume;
    }


    /**
      * Sets the value of the 'volume' field.
      * 交易量
      * @param value The value of 'volume'.
      * @return This builder.
      */
    public com.crypto.trading.common.avro.KlineDataContent.Builder setVolume(double value) {
      validate(fields()[8], value);
      this.volume = value;
      fieldSetFlags()[8] = true;
      return this;
    }

    /**
      * Checks whether the 'volume' field has been set.
      * 交易量
      * @return True if the 'volume' field has been set, false otherwise.
      */
    public boolean hasVolume() {
      return fieldSetFlags()[8];
    }


    /**
      * Clears the value of the 'volume' field.
      * 交易量
      * @return This builder.
      */
    public com.crypto.trading.common.avro.KlineDataContent.Builder clearVolume() {
      fieldSetFlags()[8] = false;
      return this;
    }

    /**
      * Gets the value of the 'quoteVolume' field.
      * 报价资产交易量
      * @return The value.
      */
    public double getQuoteVolume() {
      return quoteVolume;
    }


    /**
      * Sets the value of the 'quoteVolume' field.
      * 报价资产交易量
      * @param value The value of 'quoteVolume'.
      * @return This builder.
      */
    public com.crypto.trading.common.avro.KlineDataContent.Builder setQuoteVolume(double value) {
      validate(fields()[9], value);
      this.quoteVolume = value;
      fieldSetFlags()[9] = true;
      return this;
    }

    /**
      * Checks whether the 'quoteVolume' field has been set.
      * 报价资产交易量
      * @return True if the 'quoteVolume' field has been set, false otherwise.
      */
    public boolean hasQuoteVolume() {
      return fieldSetFlags()[9];
    }


    /**
      * Clears the value of the 'quoteVolume' field.
      * 报价资产交易量
      * @return This builder.
      */
    public com.crypto.trading.common.avro.KlineDataContent.Builder clearQuoteVolume() {
      fieldSetFlags()[9] = false;
      return this;
    }

    /**
      * Gets the value of the 'trades' field.
      * 交易笔数
      * @return The value.
      */
    public int getTrades() {
      return trades;
    }


    /**
      * Sets the value of the 'trades' field.
      * 交易笔数
      * @param value The value of 'trades'.
      * @return This builder.
      */
    public com.crypto.trading.common.avro.KlineDataContent.Builder setTrades(int value) {
      validate(fields()[10], value);
      this.trades = value;
      fieldSetFlags()[10] = true;
      return this;
    }

    /**
      * Checks whether the 'trades' field has been set.
      * 交易笔数
      * @return True if the 'trades' field has been set, false otherwise.
      */
    public boolean hasTrades() {
      return fieldSetFlags()[10];
    }


    /**
      * Clears the value of the 'trades' field.
      * 交易笔数
      * @return This builder.
      */
    public com.crypto.trading.common.avro.KlineDataContent.Builder clearTrades() {
      fieldSetFlags()[10] = false;
      return this;
    }

    /**
      * Gets the value of the 'completed' field.
      * 是否已完成
      * @return The value.
      */
    public boolean getCompleted() {
      return completed;
    }


    /**
      * Sets the value of the 'completed' field.
      * 是否已完成
      * @param value The value of 'completed'.
      * @return This builder.
      */
    public com.crypto.trading.common.avro.KlineDataContent.Builder setCompleted(boolean value) {
      validate(fields()[11], value);
      this.completed = value;
      fieldSetFlags()[11] = true;
      return this;
    }

    /**
      * Checks whether the 'completed' field has been set.
      * 是否已完成
      * @return True if the 'completed' field has been set, false otherwise.
      */
    public boolean hasCompleted() {
      return fieldSetFlags()[11];
    }


    /**
      * Clears the value of the 'completed' field.
      * 是否已完成
      * @return This builder.
      */
    public com.crypto.trading.common.avro.KlineDataContent.Builder clearCompleted() {
      fieldSetFlags()[11] = false;
      return this;
    }

    @Override
    @SuppressWarnings("unchecked")
    public KlineDataContent build() {
      try {
        KlineDataContent record = new KlineDataContent();
        record.symbol = fieldSetFlags()[0] ? this.symbol : (java.lang.String) defaultValue(fields()[0]);
        record.interval = fieldSetFlags()[1] ? this.interval : (java.lang.String) defaultValue(fields()[1]);
        record.openTime = fieldSetFlags()[2] ? this.openTime : (java.lang.Long) defaultValue(fields()[2]);
        record.closeTime = fieldSetFlags()[3] ? this.closeTime : (java.lang.Long) defaultValue(fields()[3]);
        record.open = fieldSetFlags()[4] ? this.open : (java.lang.Double) defaultValue(fields()[4]);
        record.high = fieldSetFlags()[5] ? this.high : (java.lang.Double) defaultValue(fields()[5]);
        record.low = fieldSetFlags()[6] ? this.low : (java.lang.Double) defaultValue(fields()[6]);
        record.close = fieldSetFlags()[7] ? this.close : (java.lang.Double) defaultValue(fields()[7]);
        record.volume = fieldSetFlags()[8] ? this.volume : (java.lang.Double) defaultValue(fields()[8]);
        record.quoteVolume = fieldSetFlags()[9] ? this.quoteVolume : (java.lang.Double) defaultValue(fields()[9]);
        record.trades = fieldSetFlags()[10] ? this.trades : (java.lang.Integer) defaultValue(fields()[10]);
        record.completed = fieldSetFlags()[11] ? this.completed : (java.lang.Boolean) defaultValue(fields()[11]);
        return record;
      } catch (org.apache.avro.AvroMissingFieldException e) {
        throw e;
      } catch (java.lang.Exception e) {
        throw new org.apache.avro.AvroRuntimeException(e);
      }
    }
  }

  @SuppressWarnings("unchecked")
  private static final org.apache.avro.io.DatumWriter<KlineDataContent>
    WRITER$ = (org.apache.avro.io.DatumWriter<KlineDataContent>)MODEL$.createDatumWriter(SCHEMA$);

  @Override public void writeExternal(java.io.ObjectOutput out)
    throws java.io.IOException {
    WRITER$.write(this, SpecificData.getEncoder(out));
  }

  @SuppressWarnings("unchecked")
  private static final org.apache.avro.io.DatumReader<KlineDataContent>
    READER$ = (org.apache.avro.io.DatumReader<KlineDataContent>)MODEL$.createDatumReader(SCHEMA$);

  @Override public void readExternal(java.io.ObjectInput in)
    throws java.io.IOException {
    READER$.read(this, SpecificData.getDecoder(in));
  }

  @Override protected boolean hasCustomCoders() { return true; }

  @Override public void customEncode(org.apache.avro.io.Encoder out)
    throws java.io.IOException
  {
    out.writeString(this.symbol);

    out.writeString(this.interval);

    out.writeLong(this.openTime);

    out.writeLong(this.closeTime);

    out.writeDouble(this.open);

    out.writeDouble(this.high);

    out.writeDouble(this.low);

    out.writeDouble(this.close);

    out.writeDouble(this.volume);

    out.writeDouble(this.quoteVolume);

    out.writeInt(this.trades);

    out.writeBoolean(this.completed);

  }

  @Override public void customDecode(org.apache.avro.io.ResolvingDecoder in)
    throws java.io.IOException
  {
    org.apache.avro.Schema.Field[] fieldOrder = in.readFieldOrderIfDiff();
    if (fieldOrder == null) {
      this.symbol = in.readString();

      this.interval = in.readString();

      this.openTime = in.readLong();

      this.closeTime = in.readLong();

      this.open = in.readDouble();

      this.high = in.readDouble();

      this.low = in.readDouble();

      this.close = in.readDouble();

      this.volume = in.readDouble();

      this.quoteVolume = in.readDouble();

      this.trades = in.readInt();

      this.completed = in.readBoolean();

    } else {
      for (int i = 0; i < 12; i++) {
        switch (fieldOrder[i].pos()) {
        case 0:
          this.symbol = in.readString();
          break;

        case 1:
          this.interval = in.readString();
          break;

        case 2:
          this.openTime = in.readLong();
          break;

        case 3:
          this.closeTime = in.readLong();
          break;

        case 4:
          this.open = in.readDouble();
          break;

        case 5:
          this.high = in.readDouble();
          break;

        case 6:
          this.low = in.readDouble();
          break;

        case 7:
          this.close = in.readDouble();
          break;

        case 8:
          this.volume = in.readDouble();
          break;

        case 9:
          this.quoteVolume = in.readDouble();
          break;

        case 10:
          this.trades = in.readInt();
          break;

        case 11:
          this.completed = in.readBoolean();
          break;

        default:
          throw new java.io.IOException("Corrupt ResolvingDecoder.");
        }
      }
    }
  }
}










