<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.binance.connector.futures.client.impl.cm_futures</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.html" class="el_class">Classes</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">io.github.binance:binance-futures-connector-java</a> &gt; <span class="el_package">com.binance.connector.futures.client.impl.cm_futures</span></div><h1>com.binance.connector.futures.client.impl.cm_futures</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">0 of 245</td><td class="ctr2">100%</td><td class="bar">0 of 0</td><td class="ctr2">n/a</td><td class="ctr1">0</td><td class="ctr2">24</td><td class="ctr1">0</td><td class="ctr2">51</td><td class="ctr1">0</td><td class="ctr2">24</td><td class="ctr1">0</td><td class="ctr2">4</td></tr></tfoot><tbody><tr><td id="a0"><a href="CMAccount.java.html" class="el_source">CMAccount.java</a></td><td class="bar" id="b0"><img src="../jacoco-resources/greenbar.gif" width="120" height="10" title="128" alt="128"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d0"/><td class="ctr2" id="e0">n/a</td><td class="ctr1" id="f0">0</td><td class="ctr2" id="g0">11</td><td class="ctr1" id="h0">0</td><td class="ctr2" id="i0">23</td><td class="ctr1" id="j0">0</td><td class="ctr2" id="k0">11</td><td class="ctr1" id="l0">0</td><td class="ctr2" id="m0">1</td></tr><tr><td id="a1"><a href="CMMarket.java.html" class="el_source">CMMarket.java</a></td><td class="bar" id="b1"><img src="../jacoco-resources/greenbar.gif" width="91" height="10" title="98" alt="98"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d1"/><td class="ctr2" id="e1">n/a</td><td class="ctr1" id="f1">0</td><td class="ctr2" id="g1">10</td><td class="ctr1" id="h1">0</td><td class="ctr2" id="i1">23</td><td class="ctr1" id="j1">0</td><td class="ctr2" id="k1">10</td><td class="ctr1" id="l1">0</td><td class="ctr2" id="m1">1</td></tr><tr><td id="a2"><a href="CMPortfolioMargin.java.html" class="el_source">CMPortfolioMargin.java</a></td><td class="bar" id="b2"><img src="../jacoco-resources/greenbar.gif" width="11" height="10" title="12" alt="12"/></td><td class="ctr2" id="c2">100%</td><td class="bar" id="d2"/><td class="ctr2" id="e2">n/a</td><td class="ctr1" id="f2">0</td><td class="ctr2" id="g2">2</td><td class="ctr1" id="h2">0</td><td class="ctr2" id="i2">3</td><td class="ctr1" id="j2">0</td><td class="ctr2" id="k2">2</td><td class="ctr1" id="l2">0</td><td class="ctr2" id="m2">1</td></tr><tr><td id="a3"><a href="CMUserData.java.html" class="el_source">CMUserData.java</a></td><td class="bar" id="b3"><img src="../jacoco-resources/greenbar.gif" width="6" height="10" title="7" alt="7"/></td><td class="ctr2" id="c3">100%</td><td class="bar" id="d3"/><td class="ctr2" id="e3">n/a</td><td class="ctr1" id="f3">0</td><td class="ctr2" id="g3">1</td><td class="ctr1" id="h3">0</td><td class="ctr2" id="i3">2</td><td class="ctr1" id="j3">0</td><td class="ctr2" id="k3">1</td><td class="ctr1" id="l3">0</td><td class="ctr2" id="m3">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.7.202105040129</span></div></body></html>