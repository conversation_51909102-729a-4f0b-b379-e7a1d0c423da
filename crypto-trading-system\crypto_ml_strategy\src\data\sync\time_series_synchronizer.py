#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
时间序列同步器

处理多数据源的时间同步问题，确保不同时间周期和数据源的数据能够正确对齐和同步。
包括时间戳标准化、延迟补偿、数据插值和同步验证等功能。
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any, Union, Callable
from datetime import datetime, timedelta
import threading
from collections import defaultdict, deque
from dataclasses import dataclass
import time
from enum import Enum


class SyncMethod(Enum):
    """同步方法枚举"""
    NEAREST = "nearest"  # 最近邻
    LINEAR = "linear"    # 线性插值
    FORWARD_FILL = "forward_fill"  # 前向填充
    BACKWARD_FILL = "backward_fill"  # 后向填充
    CUBIC = "cubic"      # 三次样条插值


@dataclass
class SyncConfig:
    """同步配置"""
    method: SyncMethod = SyncMethod.NEAREST
    tolerance_ms: int = 5000  # 时间容差（毫秒）
    max_gap_ms: int = 60000   # 最大间隙（毫秒）
    min_overlap_ratio: float = 0.8  # 最小重叠比例
    enable_delay_compensation: bool = True
    delay_estimation_window: int = 100  # 延迟估计窗口大小


@dataclass
class DataSource:
    """数据源定义"""
    name: str
    priority: int  # 优先级，数字越小优先级越高
    expected_delay_ms: int = 0  # 预期延迟（毫秒）
    quality_weight: float = 1.0  # 质量权重
    is_primary: bool = False  # 是否为主数据源


class TimeSeriesSynchronizer:
    """时间序列同步器"""
    
    def __init__(self, config: Dict = None):
        """
        初始化时间序列同步器
        
        Args:
            config: 配置参数字典
        """
        self.logger = logging.getLogger('cryptoMlStrategy.data.time_sync')
        
        # 默认配置
        self.config = {
            'sync': {
                'default_method': 'nearest',
                'tolerance_ms': 5000,
                'max_gap_ms': 60000,
                'min_overlap_ratio': 0.8,
                'enable_delay_compensation': True,
                'delay_estimation_window': 100
            },
            'quality': {
                'enable_quality_check': True,
                'min_data_points': 10,
                'max_missing_ratio': 0.1,
                'outlier_detection': True,
                'outlier_threshold': 3.0  # 标准差倍数
            },
            'performance': {
                'enable_caching': True,
                'cache_size_limit': 1000,
                'enable_parallel_processing': False,
                'max_workers': 4
            }
        }
        
        # 更新用户配置
        if config:
            self._deep_update(self.config, config)
        
        # 数据源注册表
        self.data_sources: Dict[str, DataSource] = {}
        
        # 同步缓存：{sync_key: synchronized_data}
        self.sync_cache = {}
        
        # 延迟估计：{source_name: deque of delays}
        self.delay_estimates = defaultdict(lambda: deque(maxlen=self.config['sync']['delay_estimation_window']))
        
        # 同步统计
        self.sync_stats = {
            'total_syncs': 0,
            'successful_syncs': 0,
            'failed_syncs': 0,
            'cache_hits': 0,
            'average_sync_time_ms': 0,
            'last_sync_time': None
        }
        
        # 线程锁
        self.sync_lock = threading.RLock()
        
        self.logger.info("时间序列同步器初始化完成")
    
    def _deep_update(self, base_dict: Dict, update_dict: Dict):
        """深度更新字典"""
        for key, value in update_dict.items():
            if key in base_dict and isinstance(base_dict[key], dict) and isinstance(value, dict):
                self._deep_update(base_dict[key], value)
            else:
                base_dict[key] = value
    
    def register_data_source(self, name: str, priority: int = 1, 
                           expected_delay_ms: int = 0, quality_weight: float = 1.0,
                           is_primary: bool = False) -> bool:
        """
        注册数据源
        
        Args:
            name: 数据源名称
            priority: 优先级（数字越小优先级越高）
            expected_delay_ms: 预期延迟（毫秒）
            quality_weight: 质量权重
            is_primary: 是否为主数据源
            
        Returns:
            是否注册成功
        """
        try:
            if name in self.data_sources:
                self.logger.warning(f"数据源已存在，将更新配置: {name}")
            
            self.data_sources[name] = DataSource(
                name=name,
                priority=priority,
                expected_delay_ms=expected_delay_ms,
                quality_weight=quality_weight,
                is_primary=is_primary
            )
            
            self.logger.info(f"数据源注册成功: {name} (优先级: {priority})")
            return True
            
        except Exception as e:
            self.logger.error(f"数据源注册失败: {e}")
            return False
    
    def synchronize_data(self, data_dict: Dict[str, pd.DataFrame], 
                        target_timestamps: Optional[pd.DatetimeIndex] = None,
                        sync_config: Optional[SyncConfig] = None) -> Optional[pd.DataFrame]:
        """
        同步多个数据源的时间序列数据
        
        Args:
            data_dict: 数据字典，格式: {source_name: DataFrame}
            target_timestamps: 目标时间戳，如果为None则自动确定
            sync_config: 同步配置，如果为None则使用默认配置
            
        Returns:
            同步后的DataFrame
        """
        try:
            start_time = time.time()
            
            if not data_dict:
                self.logger.warning("没有提供数据进行同步")
                return None
            
            # 检查缓存
            cache_key = self._generate_cache_key(data_dict, target_timestamps, sync_config)
            if self.config['performance']['enable_caching'] and cache_key in self.sync_cache:
                self.sync_stats['cache_hits'] += 1
                return self.sync_cache[cache_key].copy()
            
            # 使用默认同步配置
            if sync_config is None:
                sync_config = SyncConfig(
                    method=SyncMethod(self.config['sync']['default_method']),
                    tolerance_ms=self.config['sync']['tolerance_ms'],
                    max_gap_ms=self.config['sync']['max_gap_ms'],
                    min_overlap_ratio=self.config['sync']['min_overlap_ratio'],
                    enable_delay_compensation=self.config['sync']['enable_delay_compensation'],
                    delay_estimation_window=self.config['sync']['delay_estimation_window']
                )
            
            # 数据质量检查
            if self.config['quality']['enable_quality_check']:
                data_dict = self._quality_check(data_dict)
            
            # 延迟补偿
            if sync_config.enable_delay_compensation:
                data_dict = self._apply_delay_compensation(data_dict)
            
            # 确定目标时间戳
            if target_timestamps is None:
                target_timestamps = self._determine_target_timestamps(data_dict, sync_config)
            
            if target_timestamps is None or len(target_timestamps) == 0:
                self.logger.error("无法确定目标时间戳")
                return None
            
            # 执行同步
            synchronized_df = self._perform_synchronization(data_dict, target_timestamps, sync_config)
            
            if synchronized_df is not None:
                # 缓存结果
                if self.config['performance']['enable_caching']:
                    self._update_cache(cache_key, synchronized_df)
                
                # 更新统计
                self.sync_stats['successful_syncs'] += 1
                sync_time_ms = (time.time() - start_time) * 1000
                self._update_sync_time_stats(sync_time_ms)
            else:
                self.sync_stats['failed_syncs'] += 1
            
            self.sync_stats['total_syncs'] += 1
            self.sync_stats['last_sync_time'] = datetime.now()
            
            return synchronized_df
            
        except Exception as e:
            self.logger.error(f"数据同步失败: {e}")
            self.sync_stats['failed_syncs'] += 1
            return None
    
    def _quality_check(self, data_dict: Dict[str, pd.DataFrame]) -> Dict[str, pd.DataFrame]:
        """
        数据质量检查
        
        Args:
            data_dict: 原始数据字典
            
        Returns:
            质量检查后的数据字典
        """
        try:
            cleaned_data = {}
            
            for source_name, df in data_dict.items():
                if df is None or df.empty:
                    self.logger.warning(f"数据源为空: {source_name}")
                    continue
                
                # 检查最小数据点数
                if len(df) < self.config['quality']['min_data_points']:
                    self.logger.warning(f"数据点不足: {source_name} ({len(df)} < {self.config['quality']['min_data_points']})")
                    continue
                
                # 检查缺失值比例
                missing_ratio = df.isnull().sum().sum() / (len(df) * len(df.columns))
                if missing_ratio > self.config['quality']['max_missing_ratio']:
                    self.logger.warning(f"缺失值过多: {source_name} ({missing_ratio:.2%})")
                    continue
                
                # 异常值检测
                if self.config['quality']['outlier_detection']:
                    df = self._remove_outliers(df, source_name)
                
                cleaned_data[source_name] = df
            
            return cleaned_data
            
        except Exception as e:
            self.logger.error(f"数据质量检查失败: {e}")
            return data_dict
    
    def _remove_outliers(self, df: pd.DataFrame, source_name: str) -> pd.DataFrame:
        """
        移除异常值
        
        Args:
            df: 数据DataFrame
            source_name: 数据源名称
            
        Returns:
            移除异常值后的DataFrame
        """
        try:
            cleaned_df = df.copy()
            threshold = self.config['quality']['outlier_threshold']
            
            # 对数值列进行异常值检测
            numeric_columns = df.select_dtypes(include=[np.number]).columns
            
            for col in numeric_columns:
                if col in df.columns:
                    # 使用Z-score方法检测异常值
                    z_scores = np.abs((df[col] - df[col].mean()) / df[col].std())
                    outlier_mask = z_scores > threshold
                    
                    if outlier_mask.sum() > 0:
                        self.logger.debug(f"检测到异常值: {source_name}.{col} ({outlier_mask.sum()} 个)")
                        # 用中位数替换异常值
                        cleaned_df.loc[outlier_mask, col] = df[col].median()
            
            return cleaned_df
            
        except Exception as e:
            self.logger.error(f"异常值处理失败: {e}")
            return df
    
    def _apply_delay_compensation(self, data_dict: Dict[str, pd.DataFrame]) -> Dict[str, pd.DataFrame]:
        """
        应用延迟补偿
        
        Args:
            data_dict: 原始数据字典
            
        Returns:
            延迟补偿后的数据字典
        """
        try:
            compensated_data = {}
            
            for source_name, df in data_dict.items():
                if source_name not in self.data_sources:
                    compensated_data[source_name] = df
                    continue
                
                source_config = self.data_sources[source_name]
                
                # 计算实际延迟
                estimated_delay = self._estimate_delay(source_name, df)
                total_delay_ms = source_config.expected_delay_ms + estimated_delay
                
                if total_delay_ms != 0:
                    # 应用时间偏移
                    compensated_df = df.copy()
                    if hasattr(compensated_df.index, 'to_pydatetime'):
                        # 如果索引是时间戳
                        time_offset = timedelta(milliseconds=total_delay_ms)
                        compensated_df.index = compensated_df.index + time_offset
                    elif 'timestamp' in compensated_df.columns:
                        # 如果有timestamp列
                        compensated_df['timestamp'] = compensated_df['timestamp'] + total_delay_ms
                    
                    compensated_data[source_name] = compensated_df
                    self.logger.debug(f"应用延迟补偿: {source_name} ({total_delay_ms}ms)")
                else:
                    compensated_data[source_name] = df
            
            return compensated_data
            
        except Exception as e:
            self.logger.error(f"延迟补偿失败: {e}")
            return data_dict
    
    def _estimate_delay(self, source_name: str, df: pd.DataFrame) -> int:
        """
        估计数据源延迟
        
        Args:
            source_name: 数据源名称
            df: 数据DataFrame
            
        Returns:
            估计的延迟（毫秒）
        """
        try:
            if len(df) < 2:
                return 0
            
            # 简单的延迟估计：比较数据时间戳与当前时间
            if hasattr(df.index, 'to_pydatetime'):
                latest_timestamp = df.index[-1]
                if hasattr(latest_timestamp, 'timestamp'):
                    delay_ms = (datetime.now() - latest_timestamp).total_seconds() * 1000
                else:
                    delay_ms = 0
            elif 'timestamp' in df.columns:
                latest_timestamp = df['timestamp'].iloc[-1]
                current_timestamp = int(time.time() * 1000)
                delay_ms = current_timestamp - latest_timestamp
            else:
                delay_ms = 0
            
            # 更新延迟估计历史
            self.delay_estimates[source_name].append(delay_ms)
            
            # 返回平均延迟
            if len(self.delay_estimates[source_name]) > 0:
                return int(np.mean(self.delay_estimates[source_name]))
            else:
                return 0
                
        except Exception as e:
            self.logger.error(f"延迟估计失败: {e}")
            return 0
    
    def _determine_target_timestamps(self, data_dict: Dict[str, pd.DataFrame], 
                                   sync_config: SyncConfig) -> Optional[pd.DatetimeIndex]:
        """
        确定目标时间戳
        
        Args:
            data_dict: 数据字典
            sync_config: 同步配置
            
        Returns:
            目标时间戳索引
        """
        try:
            # 收集所有时间戳
            all_timestamps = []
            
            for source_name, df in data_dict.items():
                if hasattr(df.index, 'to_pydatetime'):
                    timestamps = df.index
                elif 'timestamp' in df.columns:
                    timestamps = pd.to_datetime(df['timestamp'], unit='ms')
                elif 'datetime' in df.columns:
                    timestamps = pd.to_datetime(df['datetime'])
                else:
                    self.logger.warning(f"无法提取时间戳: {source_name}")
                    continue
                
                all_timestamps.extend(timestamps)
            
            if not all_timestamps:
                return None
            
            # 转换为DatetimeIndex并排序
            all_timestamps = pd.DatetimeIndex(all_timestamps).drop_duplicates().sort_values()
            
            # 确定时间范围
            start_time = all_timestamps.min()
            end_time = all_timestamps.max()
            
            # 计算重叠区间
            overlap_start = start_time
            overlap_end = end_time
            
            for source_name, df in data_dict.items():
                if hasattr(df.index, 'to_pydatetime'):
                    source_start = df.index.min()
                    source_end = df.index.max()
                elif 'timestamp' in df.columns:
                    source_timestamps = pd.to_datetime(df['timestamp'], unit='ms')
                    source_start = source_timestamps.min()
                    source_end = source_timestamps.max()
                else:
                    continue
                
                overlap_start = max(overlap_start, source_start)
                overlap_end = min(overlap_end, source_end)
            
            # 检查重叠比例
            total_duration = (end_time - start_time).total_seconds()
            overlap_duration = (overlap_end - overlap_start).total_seconds()
            
            if total_duration > 0:
                overlap_ratio = overlap_duration / total_duration
                if overlap_ratio < sync_config.min_overlap_ratio:
                    self.logger.warning(f"数据重叠比例过低: {overlap_ratio:.2%}")
            
            # 生成目标时间戳（使用重叠区间）
            target_timestamps = all_timestamps[(all_timestamps >= overlap_start) & 
                                             (all_timestamps <= overlap_end)]
            
            return target_timestamps
            
        except Exception as e:
            self.logger.error(f"目标时间戳确定失败: {e}")
            return None
    
    def _perform_synchronization(self, data_dict: Dict[str, pd.DataFrame], 
                               target_timestamps: pd.DatetimeIndex,
                               sync_config: SyncConfig) -> Optional[pd.DataFrame]:
        """
        执行数据同步
        
        Args:
            data_dict: 数据字典
            target_timestamps: 目标时间戳
            sync_config: 同步配置
            
        Returns:
            同步后的DataFrame
        """
        try:
            # 创建结果DataFrame
            result_df = pd.DataFrame(index=target_timestamps)
            
            # 按优先级排序数据源
            sorted_sources = sorted(data_dict.keys(), 
                                  key=lambda x: self.data_sources.get(x, DataSource(x, 999)).priority)
            
            # 逐个同步数据源
            for source_name in sorted_sources:
                df = data_dict[source_name]
                
                # 提取时间戳
                if hasattr(df.index, 'to_pydatetime'):
                    source_timestamps = df.index
                    source_data = df
                elif 'timestamp' in df.columns:
                    source_timestamps = pd.to_datetime(df['timestamp'], unit='ms')
                    source_data = df.set_index(source_timestamps)
                elif 'datetime' in df.columns:
                    source_timestamps = pd.to_datetime(df['datetime'])
                    source_data = df.set_index(source_timestamps)
                else:
                    self.logger.warning(f"无法处理数据源时间戳: {source_name}")
                    continue
                
                # 执行插值同步
                synchronized_data = self._interpolate_data(source_data, target_timestamps, sync_config)
                
                if synchronized_data is not None:
                    # 添加数据源前缀
                    synchronized_data = synchronized_data.add_prefix(f'{source_name}_')
                    
                    # 合并到结果DataFrame
                    result_df = result_df.join(synchronized_data, how='left')
            
            # 移除全为NaN的行
            result_df = result_df.dropna(how='all')
            
            return result_df if not result_df.empty else None
            
        except Exception as e:
            self.logger.error(f"数据同步执行失败: {e}")
            return None
    
    def _interpolate_data(self, source_data: pd.DataFrame, 
                         target_timestamps: pd.DatetimeIndex,
                         sync_config: SyncConfig) -> Optional[pd.DataFrame]:
        """
        插值数据到目标时间戳
        
        Args:
            source_data: 源数据
            target_timestamps: 目标时间戳
            sync_config: 同步配置
            
        Returns:
            插值后的数据
        """
        try:
            if sync_config.method == SyncMethod.NEAREST:
                # 最近邻插值
                result = source_data.reindex(target_timestamps, method='nearest', 
                                           tolerance=pd.Timedelta(milliseconds=sync_config.tolerance_ms))
            
            elif sync_config.method == SyncMethod.FORWARD_FILL:
                # 前向填充
                result = source_data.reindex(target_timestamps, method='ffill')
            
            elif sync_config.method == SyncMethod.BACKWARD_FILL:
                # 后向填充
                result = source_data.reindex(target_timestamps, method='bfill')
            
            elif sync_config.method == SyncMethod.LINEAR:
                # 线性插值
                combined_index = source_data.index.union(target_timestamps).sort_values()
                reindexed = source_data.reindex(combined_index)
                interpolated = reindexed.interpolate(method='linear')
                result = interpolated.reindex(target_timestamps)
            
            elif sync_config.method == SyncMethod.CUBIC:
                # 三次样条插值
                if len(source_data) >= 4:  # 三次样条至少需要4个点
                    combined_index = source_data.index.union(target_timestamps).sort_values()
                    reindexed = source_data.reindex(combined_index)
                    interpolated = reindexed.interpolate(method='cubic')
                    result = interpolated.reindex(target_timestamps)
                else:
                    # 数据点不足，降级为线性插值
                    combined_index = source_data.index.union(target_timestamps).sort_values()
                    reindexed = source_data.reindex(combined_index)
                    interpolated = reindexed.interpolate(method='linear')
                    result = interpolated.reindex(target_timestamps)
            
            else:
                self.logger.error(f"不支持的同步方法: {sync_config.method}")
                return None
            
            return result
            
        except Exception as e:
            self.logger.error(f"数据插值失败: {e}")
            return None
    
    def _generate_cache_key(self, data_dict: Dict[str, pd.DataFrame], 
                           target_timestamps: Optional[pd.DatetimeIndex],
                           sync_config: Optional[SyncConfig]) -> str:
        """生成缓存键"""
        try:
            # 简化的缓存键生成
            source_names = sorted(data_dict.keys())
            data_hashes = []
            
            for name in source_names:
                df = data_dict[name]
                # 使用数据形状和最后几行的哈希作为标识
                if not df.empty:
                    shape_str = f"{df.shape[0]}x{df.shape[1]}"
                    last_values = str(df.tail(3).values.flatten()[:10])
                    data_hashes.append(f"{name}:{shape_str}:{hash(last_values)}")
            
            cache_key = "|".join(data_hashes)
            
            if target_timestamps is not None:
                cache_key += f"|targets:{len(target_timestamps)}"
            
            if sync_config is not None:
                cache_key += f"|method:{sync_config.method.value}"
            
            return str(hash(cache_key))
            
        except Exception as e:
            self.logger.error(f"缓存键生成失败: {e}")
            return str(time.time())
    
    def _update_cache(self, cache_key: str, data: pd.DataFrame):
        """更新缓存"""
        try:
            with self.sync_lock:
                # 检查缓存大小限制
                if len(self.sync_cache) >= self.config['performance']['cache_size_limit']:
                    # 移除最旧的缓存项
                    oldest_key = next(iter(self.sync_cache))
                    del self.sync_cache[oldest_key]
                
                self.sync_cache[cache_key] = data.copy()
                
        except Exception as e:
            self.logger.error(f"缓存更新失败: {e}")
    
    def _update_sync_time_stats(self, sync_time_ms: float):
        """更新同步时间统计"""
        try:
            current_avg = self.sync_stats['average_sync_time_ms']
            total_syncs = self.sync_stats['total_syncs']
            
            if total_syncs == 0:
                self.sync_stats['average_sync_time_ms'] = sync_time_ms
            else:
                # 计算移动平均
                self.sync_stats['average_sync_time_ms'] = (current_avg * (total_syncs - 1) + sync_time_ms) / total_syncs
                
        except Exception as e:
            self.logger.error(f"同步时间统计更新失败: {e}")
    
    def get_sync_statistics(self) -> Dict[str, Any]:
        """获取同步统计信息"""
        return {
            'sync_stats': self.sync_stats.copy(),
            'data_sources': {name: {
                'priority': source.priority,
                'expected_delay_ms': source.expected_delay_ms,
                'quality_weight': source.quality_weight,
                'is_primary': source.is_primary,
                'estimated_delay_ms': int(np.mean(self.delay_estimates[name])) if self.delay_estimates[name] else 0
            } for name, source in self.data_sources.items()},
            'cache_info': {
                'cache_size': len(self.sync_cache),
                'cache_limit': self.config['performance']['cache_size_limit'],
                'cache_hit_rate': self.sync_stats['cache_hits'] / max(1, self.sync_stats['total_syncs'])
            },
            'config': self.config
        }
    
    def clear_cache(self):
        """清除同步缓存"""
        with self.sync_lock:
            self.sync_cache.clear()
            self.logger.info("同步缓存已清除")
    
    def validate_synchronization(self, synchronized_data: pd.DataFrame, 
                               original_data_dict: Dict[str, pd.DataFrame]) -> Dict[str, Any]:
        """
        验证同步结果
        
        Args:
            synchronized_data: 同步后的数据
            original_data_dict: 原始数据字典
            
        Returns:
            验证结果字典
        """
        try:
            validation_results = {
                'is_valid': True,
                'issues': [],
                'statistics': {},
                'quality_score': 0.0
            }
            
            if synchronized_data is None or synchronized_data.empty:
                validation_results['is_valid'] = False
                validation_results['issues'].append("同步后数据为空")
                return validation_results
            
            # 检查数据完整性
            missing_ratio = synchronized_data.isnull().sum().sum() / (len(synchronized_data) * len(synchronized_data.columns))
            validation_results['statistics']['missing_ratio'] = missing_ratio
            
            if missing_ratio > 0.5:
                validation_results['is_valid'] = False
                validation_results['issues'].append(f"缺失值比例过高: {missing_ratio:.2%}")
            
            # 检查时间连续性
            time_gaps = synchronized_data.index.to_series().diff().dt.total_seconds()
            max_gap = time_gaps.max()
            validation_results['statistics']['max_time_gap_seconds'] = max_gap
            
            if max_gap > self.config['sync']['max_gap_ms'] / 1000:
                validation_results['issues'].append(f"时间间隙过大: {max_gap}秒")
            
            # 计算质量分数
            quality_score = 1.0 - missing_ratio
            if len(validation_results['issues']) == 0:
                quality_score *= 1.0
            else:
                quality_score *= 0.5
            
            validation_results['quality_score'] = quality_score
            
            return validation_results
            
        except Exception as e:
            self.logger.error(f"同步验证失败: {e}")
            return {
                'is_valid': False,
                'issues': [f"验证过程出错: {str(e)}"],
                'statistics': {},
                'quality_score': 0.0
            }