#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
缓存存储后端组件

实现各种缓存存储后端，包括内存存储、分区存储、压缩存储等。
提供高性能的数据存储和检索能力。
"""

import logging
import threading
import pickle
import zlib
import json
from typing import Dict, List, Optional, Any, Union, Tuple
from collections import OrderedDict, defaultdict
import weakref
import mmap
import tempfile
import os
from datetime import datetime
import hashlib

from .cache_core import CacheBackend, CacheEntry, CacheKey, CacheLevel


class MemoryBackend:
    """内存缓存后端"""
    
    def __init__(self, max_size: int = 10000, enable_compression: bool = False):
        """
        初始化内存后端
        
        Args:
            max_size: 最大条目数
            enable_compression: 是否启用压缩
        """
        self.logger = logging.getLogger('cryptoMlStrategy.data.cache_storage')
        self.max_size = max_size
        self.enable_compression = enable_compression
        
        # 存储结构：使用OrderedDict支持LRU
        self._storage: OrderedDict[str, CacheEntry] = OrderedDict()
        
        # 索引结构：按不同维度建立索引
        self._symbol_index: Dict[str, List[str]] = defaultdict(list)
        self._timeframe_index: Dict[str, List[str]] = defaultdict(list)
        self._type_index: Dict[str, List[str]] = defaultdict(list)
        
        # 并发控制
        self._lock = threading.RLock()
        
        # 统计信息
        self._stats = {
            'total_size_bytes': 0,
            'compressed_entries': 0,
            'compression_ratio': 0.0
        }
        
        self.logger.info(f"内存缓存后端初始化完成，最大容量: {max_size}")
    
    def get(self, key: CacheKey) -> Optional[CacheEntry]:
        """获取缓存条目"""
        try:
            with self._lock:
                key_str = key.to_string()
                
                if key_str in self._storage:
                    entry = self._storage[key_str]
                    
                    # 移动到末尾（LRU更新）
                    self._storage.move_to_end(key_str)
                    
                    # 解压缩数据（如果需要）
                    if self.enable_compression and hasattr(entry, '_compressed'):
                        entry = self._decompress_entry(entry)
                    
                    return entry
                
                return None
                
        except Exception as e:
            self.logger.error(f"获取缓存条目失败: {e}")
            return None
    
    def put(self, entry: CacheEntry) -> bool:
        """存储缓存条目"""
        try:
            with self._lock:
                key_str = entry.key.to_string()
                
                # 检查容量限制
                if len(self._storage) >= self.max_size and key_str not in self._storage:
                    # 移除最旧的条目
                    self._evict_oldest()
                
                # 压缩数据（如果启用）
                if self.enable_compression:
                    entry = self._compress_entry(entry)
                
                # 更新索引
                self._update_indexes(entry.key, key_str, add=True)
                
                # 存储条目
                self._storage[key_str] = entry
                
                # 更新统计
                self._stats['total_size_bytes'] += entry.size_bytes
                
                return True
                
        except Exception as e:
            self.logger.error(f"存储缓存条目失败: {e}")
            return False
    
    def remove(self, key: CacheKey) -> bool:
        """移除缓存条目"""
        try:
            with self._lock:
                key_str = key.to_string()
                
                if key_str in self._storage:
                    entry = self._storage.pop(key_str)
                    
                    # 更新索引
                    self._update_indexes(key, key_str, add=False)
                    
                    # 更新统计
                    self._stats['total_size_bytes'] -= entry.size_bytes
                    
                    return True
                
                return False
                
        except Exception as e:
            self.logger.error(f"移除缓存条目失败: {e}")
            return False
    
    def clear(self) -> int:
        """清空缓存"""
        try:
            with self._lock:
                count = len(self._storage)
                self._storage.clear()
                self._symbol_index.clear()
                self._timeframe_index.clear()
                self._type_index.clear()
                self._stats['total_size_bytes'] = 0
                return count
                
        except Exception as e:
            self.logger.error(f"清空缓存失败: {e}")
            return 0
    
    def size(self) -> int:
        """获取缓存大小"""
        return len(self._storage)
    
    def keys(self) -> List[CacheKey]:
        """获取所有键"""
        try:
            with self._lock:
                return [CacheKey.from_string(key_str) for key_str in self._storage.keys()]
        except Exception as e:
            self.logger.error(f"获取缓存键失败: {e}")
            return []
    
    def get_by_symbol(self, symbol: str) -> List[CacheEntry]:
        """按交易对获取条目"""
        try:
            with self._lock:
                key_strs = self._symbol_index.get(symbol, [])
                return [self._storage[key_str] for key_str in key_strs if key_str in self._storage]
        except Exception as e:
            self.logger.error(f"按交易对获取条目失败: {e}")
            return []
    
    def get_by_timeframe(self, timeframe: str) -> List[CacheEntry]:
        """按时间框架获取条目"""
        try:
            with self._lock:
                key_strs = self._timeframe_index.get(timeframe, [])
                return [self._storage[key_str] for key_str in key_strs if key_str in self._storage]
        except Exception as e:
            self.logger.error(f"按时间框架获取条目失败: {e}")
            return []
    
    def _evict_oldest(self):
        """淘汰最旧的条目"""
        if self._storage:
            oldest_key, oldest_entry = self._storage.popitem(last=False)
            key = CacheKey.from_string(oldest_key)
            self._update_indexes(key, oldest_key, add=False)
            self._stats['total_size_bytes'] -= oldest_entry.size_bytes
    
    def _update_indexes(self, key: CacheKey, key_str: str, add: bool):
        """更新索引"""
        try:
            if add:
                self._symbol_index[key.symbol].append(key_str)
                self._timeframe_index[key.timeframe].append(key_str)
                self._type_index[key.data_type].append(key_str)
            else:
                if key_str in self._symbol_index[key.symbol]:
                    self._symbol_index[key.symbol].remove(key_str)
                if key_str in self._timeframe_index[key.timeframe]:
                    self._timeframe_index[key.timeframe].remove(key_str)
                if key_str in self._type_index[key.data_type]:
                    self._type_index[key.data_type].remove(key_str)
        except Exception as e:
            self.logger.error(f"更新索引失败: {e}")
    
    def _compress_entry(self, entry: CacheEntry) -> CacheEntry:
        """压缩缓存条目"""
        try:
            # 序列化数据
            serialized = pickle.dumps(entry.value)
            
            # 压缩数据
            compressed = zlib.compress(serialized)
            
            # 计算压缩比
            compression_ratio = len(compressed) / len(serialized)
            
            # 只有压缩效果好时才使用压缩
            if compression_ratio < 0.8:
                # 创建压缩后的条目
                compressed_entry = CacheEntry(
                    key=entry.key,
                    value=compressed,
                    size_bytes=len(compressed),
                    created_at=entry.created_at,
                    last_accessed=entry.last_accessed,
                    access_count=entry.access_count,
                    ttl_seconds=entry.ttl_seconds
                )
                compressed_entry._compressed = True
                compressed_entry._original_size = len(serialized)
                
                self._stats['compressed_entries'] += 1
                self._stats['compression_ratio'] = (
                    self._stats['compression_ratio'] * (self._stats['compressed_entries'] - 1) + compression_ratio
                ) / self._stats['compressed_entries']
                
                return compressed_entry
            
            return entry
            
        except Exception as e:
            self.logger.error(f"压缩条目失败: {e}")
            return entry
    
    def _decompress_entry(self, entry: CacheEntry) -> CacheEntry:
        """解压缩缓存条目"""
        try:
            if hasattr(entry, '_compressed') and entry._compressed:
                # 解压缩数据
                decompressed = zlib.decompress(entry.value)
                
                # 反序列化数据
                original_value = pickle.loads(decompressed)
                
                # 创建解压缩后的条目
                decompressed_entry = CacheEntry(
                    key=entry.key,
                    value=original_value,
                    size_bytes=getattr(entry, '_original_size', len(decompressed)),
                    created_at=entry.created_at,
                    last_accessed=entry.last_accessed,
                    access_count=entry.access_count,
                    ttl_seconds=entry.ttl_seconds
                )
                
                return decompressed_entry
            
            return entry
            
        except Exception as e:
            self.logger.error(f"解压缩条目失败: {e}")
            return entry
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取存储统计"""
        with self._lock:
            return {
                'backend_type': 'memory',
                'entry_count': len(self._storage),
                'max_size': self.max_size,
                'total_size_bytes': self._stats['total_size_bytes'],
                'compressed_entries': self._stats['compressed_entries'],
                'compression_ratio': self._stats['compression_ratio'],
                'index_sizes': {
                    'symbol_index': len(self._symbol_index),
                    'timeframe_index': len(self._timeframe_index),
                    'type_index': len(self._type_index)
                }
            }


class PartitionedBackend:
    """分区缓存后端"""
    
    def __init__(self, partition_count: int = 16, backend_factory=None):
        """
        初始化分区后端
        
        Args:
            partition_count: 分区数量
            backend_factory: 后端工厂函数
        """
        self.logger = logging.getLogger('cryptoMlStrategy.data.cache_storage')
        self.partition_count = partition_count
        
        # 创建分区
        self._partitions: List[CacheBackend] = []
        for i in range(partition_count):
            if backend_factory:
                backend = backend_factory()
            else:
                backend = MemoryBackend(max_size=1000)
            self._partitions.append(backend)
        
        # 分区锁
        self._partition_locks = [threading.RLock() for _ in range(partition_count)]
        
        self.logger.info(f"分区缓存后端初始化完成，分区数: {partition_count}")
    
    def _get_partition(self, key: CacheKey) -> Tuple[int, CacheBackend, threading.RLock]:
        """获取键对应的分区"""
        # 使用键的哈希值确定分区
        key_hash = hash(key.to_string())
        partition_index = key_hash % self.partition_count
        
        return partition_index, self._partitions[partition_index], self._partition_locks[partition_index]
    
    def get(self, key: CacheKey) -> Optional[CacheEntry]:
        """获取缓存条目"""
        try:
            _, backend, lock = self._get_partition(key)
            with lock:
                return backend.get(key)
        except Exception as e:
            self.logger.error(f"分区获取失败: {e}")
            return None
    
    def put(self, entry: CacheEntry) -> bool:
        """存储缓存条目"""
        try:
            _, backend, lock = self._get_partition(entry.key)
            with lock:
                return backend.put(entry)
        except Exception as e:
            self.logger.error(f"分区存储失败: {e}")
            return False
    
    def remove(self, key: CacheKey) -> bool:
        """移除缓存条目"""
        try:
            _, backend, lock = self._get_partition(key)
            with lock:
                return backend.remove(key)
        except Exception as e:
            self.logger.error(f"分区移除失败: {e}")
            return False
    
    def clear(self) -> int:
        """清空所有分区"""
        total_cleared = 0
        for i, (backend, lock) in enumerate(zip(self._partitions, self._partition_locks)):
            try:
                with lock:
                    cleared = backend.clear()
                    total_cleared += cleared
            except Exception as e:
                self.logger.error(f"清空分区 {i} 失败: {e}")
        
        return total_cleared
    
    def size(self) -> int:
        """获取总大小"""
        total_size = 0
        for backend, lock in zip(self._partitions, self._partition_locks):
            try:
                with lock:
                    total_size += backend.size()
            except Exception as e:
                self.logger.error(f"获取分区大小失败: {e}")
        
        return total_size
    
    def keys(self) -> List[CacheKey]:
        """获取所有键"""
        all_keys = []
        for backend, lock in zip(self._partitions, self._partition_locks):
            try:
                with lock:
                    all_keys.extend(backend.keys())
            except Exception as e:
                self.logger.error(f"获取分区键失败: {e}")
        
        return all_keys
    
    def get_partition_statistics(self) -> List[Dict[str, Any]]:
        """获取各分区统计"""
        stats = []
        for i, (backend, lock) in enumerate(zip(self._partitions, self._partition_locks)):
            try:
                with lock:
                    if hasattr(backend, 'get_statistics'):
                        partition_stats = backend.get_statistics()
                        partition_stats['partition_id'] = i
                        stats.append(partition_stats)
                    else:
                        stats.append({
                            'partition_id': i,
                            'size': backend.size()
                        })
            except Exception as e:
                self.logger.error(f"获取分区 {i} 统计失败: {e}")
                stats.append({'partition_id': i, 'error': str(e)})
        
        return stats


class PersistentBackend:
    """持久化缓存后端"""
    
    def __init__(self, cache_dir: str = None, max_file_size_mb: int = 100):
        """
        初始化持久化后端
        
        Args:
            cache_dir: 缓存目录
            max_file_size_mb: 最大文件大小（MB）
        """
        self.logger = logging.getLogger('cryptoMlStrategy.data.cache_storage')
        
        # 缓存目录
        if cache_dir is None:
            cache_dir = os.path.join(tempfile.gettempdir(), 'crypto_ml_cache')
        
        self.cache_dir = cache_dir
        self.max_file_size_bytes = max_file_size_mb * 1024 * 1024
        
        # 确保目录存在
        os.makedirs(cache_dir, exist_ok=True)
        
        # 内存索引
        self._index: Dict[str, Dict[str, Any]] = {}
        self._lock = threading.RLock()
        
        # 加载现有索引
        self._load_index()
        
        self.logger.info(f"持久化缓存后端初始化完成，目录: {cache_dir}")
    
    def get(self, key: CacheKey) -> Optional[CacheEntry]:
        """获取缓存条目"""
        try:
            with self._lock:
                key_str = key.to_string()
                
                if key_str not in self._index:
                    return None
                
                file_info = self._index[key_str]
                file_path = os.path.join(self.cache_dir, file_info['filename'])
                
                if not os.path.exists(file_path):
                    # 文件不存在，清理索引
                    del self._index[key_str]
                    return None
                
                # 读取文件
                with open(file_path, 'rb') as f:
                    data = f.read()
                
                # 反序列化
                entry = pickle.loads(data)
                
                # 检查是否过期
                if entry.is_expired():
                    self.remove(key)
                    return None
                
                return entry
                
        except Exception as e:
            self.logger.error(f"持久化获取失败: {e}")
            return None
    
    def put(self, entry: CacheEntry) -> bool:
        """存储缓存条目"""
        try:
            with self._lock:
                key_str = entry.key.to_string()
                
                # 序列化数据
                data = pickle.dumps(entry)
                
                # 检查文件大小
                if len(data) > self.max_file_size_bytes:
                    self.logger.warning(f"条目过大，跳过持久化: {len(data)} bytes")
                    return False
                
                # 生成文件名
                filename = self._generate_filename(entry.key)
                file_path = os.path.join(self.cache_dir, filename)
                
                # 写入文件
                with open(file_path, 'wb') as f:
                    f.write(data)
                
                # 更新索引
                self._index[key_str] = {
                    'filename': filename,
                    'size': len(data),
                    'created_at': entry.created_at.isoformat(),
                    'ttl_seconds': entry.ttl_seconds
                }
                
                # 保存索引
                self._save_index()
                
                return True
                
        except Exception as e:
            self.logger.error(f"持久化存储失败: {e}")
            return False
    
    def remove(self, key: CacheKey) -> bool:
        """移除缓存条目"""
        try:
            with self._lock:
                key_str = key.to_string()
                
                if key_str not in self._index:
                    return False
                
                file_info = self._index[key_str]
                file_path = os.path.join(self.cache_dir, file_info['filename'])
                
                # 删除文件
                if os.path.exists(file_path):
                    os.remove(file_path)
                
                # 删除索引
                del self._index[key_str]
                
                # 保存索引
                self._save_index()
                
                return True
                
        except Exception as e:
            self.logger.error(f"持久化移除失败: {e}")
            return False
    
    def clear(self) -> int:
        """清空缓存"""
        try:
            with self._lock:
                count = len(self._index)
                
                # 删除所有文件
                for file_info in self._index.values():
                    file_path = os.path.join(self.cache_dir, file_info['filename'])
                    if os.path.exists(file_path):
                        os.remove(file_path)
                
                # 清空索引
                self._index.clear()
                self._save_index()
                
                return count
                
        except Exception as e:
            self.logger.error(f"持久化清空失败: {e}")
            return 0
    
    def size(self) -> int:
        """获取缓存大小"""
        return len(self._index)
    
    def keys(self) -> List[CacheKey]:
        """获取所有键"""
        try:
            with self._lock:
                return [CacheKey.from_string(key_str) for key_str in self._index.keys()]
        except Exception as e:
            self.logger.error(f"获取持久化键失败: {e}")
            return []
    
    def _generate_filename(self, key: CacheKey) -> str:
        """生成文件名"""
        # 使用键的哈希值作为文件名
        key_hash = hashlib.md5(key.to_string().encode()).hexdigest()
        return f"cache_{key_hash}.pkl"
    
    def _load_index(self):
        """加载索引"""
        try:
            index_path = os.path.join(self.cache_dir, 'index.json')
            if os.path.exists(index_path):
                with open(index_path, 'r', encoding='utf-8') as f:
                    self._index = json.load(f)
                self.logger.info(f"加载了 {len(self._index)} 个索引条目")
        except Exception as e:
            self.logger.error(f"加载索引失败: {e}")
            self._index = {}
    
    def _save_index(self):
        """保存索引"""
        try:
            index_path = os.path.join(self.cache_dir, 'index.json')
            with open(index_path, 'w', encoding='utf-8') as f:
                json.dump(self._index, f, indent=2, ensure_ascii=False)
        except Exception as e:
            self.logger.error(f"保存索引失败: {e}")
    
    def cleanup_expired(self) -> int:
        """清理过期条目"""
        try:
            with self._lock:
                expired_keys = []
                current_time = datetime.now()
                
                for key_str, file_info in self._index.items():
                    if file_info.get('ttl_seconds'):
                        created_at = datetime.fromisoformat(file_info['created_at'])
                        age = (current_time - created_at).total_seconds()
                        
                        if age > file_info['ttl_seconds']:
                            expired_keys.append(key_str)
                
                # 删除过期条目
                for key_str in expired_keys:
                    key = CacheKey.from_string(key_str)
                    self.remove(key)
                
                return len(expired_keys)
                
        except Exception as e:
            self.logger.error(f"清理过期条目失败: {e}")
            return 0


class CacheBackendFactory:
    """缓存后端工厂"""
    
    @staticmethod
    def create_memory_backend(max_size: int = 10000, enable_compression: bool = False) -> MemoryBackend:
        """创建内存后端"""
        return MemoryBackend(max_size=max_size, enable_compression=enable_compression)
    
    @staticmethod
    def create_partitioned_backend(partition_count: int = 16, 
                                 backend_factory=None) -> PartitionedBackend:
        """创建分区后端"""
        return PartitionedBackend(partition_count=partition_count, backend_factory=backend_factory)
    
    @staticmethod
    def create_persistent_backend(cache_dir: str = None, 
                                max_file_size_mb: int = 100) -> PersistentBackend:
        """创建持久化后端"""
        return PersistentBackend(cache_dir=cache_dir, max_file_size_mb=max_file_size_mb)
    
    @staticmethod
    def create_hybrid_backend(memory_size: int = 5000, 
                            persistent_dir: str = None) -> PartitionedBackend:
        """创建混合后端（内存+持久化）"""
        def backend_factory():
            # 50%内存后端，50%持久化后端
            import random
            if random.random() < 0.5:
                return CacheBackendFactory.create_memory_backend(max_size=memory_size//2)
            else:
                return CacheBackendFactory.create_persistent_backend(cache_dir=persistent_dir)
        
        return PartitionedBackend(partition_count=8, backend_factory=backend_factory)