package com.crypto.trading.market.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.util.Arrays;
import java.util.List;

/**
 * 市场数据模块配置类
 * 负责提供市场数据相关配置和参数
 */
@Configuration
@EnableConfigurationProperties
@Import({WebSocketConfig.class, KafkaProducerConfig.class, InfluxDBConfig.class})
public class MarketDataConfig {

    private static final Logger log = LoggerFactory.getLogger(MarketDataConfig.class);

    /**
     * 订阅的交易对列表，多个交易对用逗号分隔
     */
    @Value("${market.symbols}")
    private String symbolsString;

    /**
     * K线是否启用
     */
    @Value("${market.kline.enabled:true}")
    private boolean klineEnabled;

    /**
     * K线间隔列表，多个间隔用逗号分隔
     */
    @Value("${market.kline.intervals:1m,5m,15m,30m,1h,4h,1d}")
    private String klineIntervalsString;

    /**
     * 深度是否启用
     */
    @Value("${market.depth.enabled:true}")
    private boolean depthEnabled;

    /**
     * 深度级别（5, 10, 20）
     */
    @Value("${market.depth.levels:10}")
    private int depthLevels;

    /**
     * 深度更新速度（100ms, 1000ms）
     */
    @Value("${market.depth.speed:1000}")
    private int depthSpeed;

    /**
     * 交易是否启用
     */
    @Value("${market.trade.enabled:true}")
    private boolean tradeEnabled;

    /**
     * InfluxDB批处理大小
     */
    @Value("${market.influxdb.batch-size:1000}")
    private int influxDbBatchSize;

    /**
     * InfluxDB刷新间隔（毫秒）
     */
    @Value("${market.influxdb.flush-interval:5000}")
    private int influxDbFlushInterval;

    /**
     * 获取交易对列表
     *
     * @return 交易对列表
     */
    @Bean
    @ConditionalOnMissingBean(name = "symbols")
    public List<String> symbols() {
        List<String> symbols = Arrays.asList(symbolsString.split(","));
        log.info("加载交易对列表: {}", symbols);
        return symbols;
    }

    /**
     * 获取K线间隔列表
     *
     * @return K线间隔列表
     */
    @Bean
    @ConditionalOnMissingBean(name = "klineIntervals")
    public List<String> klineIntervals() {
        List<String> intervals = Arrays.asList(klineIntervalsString.split(","));
        log.info("加载K线间隔列表: {}", intervals);
        return intervals;
    }

    /**
     * 是否启用K线数据
     *
     * @return 是否启用K线数据
     */
    public boolean isKlineEnabled() {
        return klineEnabled;
    }

    /**
     * 是否启用深度数据
     *
     * @return 是否启用深度数据
     */
    public boolean isDepthEnabled() {
        return depthEnabled;
    }

    /**
     * 是否启用交易数据
     *
     * @return 是否启用交易数据
     */
    public boolean isTradeEnabled() {
        return tradeEnabled;
    }

    /**
     * 获取深度级别
     *
     * @return 深度级别
     */
    public int getDepthLevels() {
        return depthLevels;
    }

    /**
     * 获取深度更新速度
     *
     * @return 深度更新速度
     */
    public int getDepthSpeed() {
        return depthSpeed;
    }

    /**
     * 获取InfluxDB批处理大小
     *
     * @return InfluxDB批处理大小
     */
    public int getInfluxDbBatchSize() {
        return influxDbBatchSize;
    }

    /**
     * 获取InfluxDB刷新间隔
     *
     * @return InfluxDB刷新间隔（毫秒）
     */
    public int getInfluxDbFlushInterval() {
        return influxDbFlushInterval;
    }
} 