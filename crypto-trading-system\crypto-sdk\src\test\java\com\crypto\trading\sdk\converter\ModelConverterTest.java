package com.crypto.trading.sdk.converter;

import com.crypto.trading.common.dto.market.KlineDataDTO;
import com.crypto.trading.sdk.response.model.KlineData;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 模型转换器测试类
 */
public class ModelConverterTest {

    private ModelConverter modelConverter;

    @BeforeEach
    public void setUp() {
        modelConverter = new ModelConverterImpl();
    }

    @Test
    public void testToDto() {
        // 创建API模型
        KlineData klineData = new KlineData();
        klineData.setOpenTime(1619049240000L);
        klineData.setCloseTime(1619049299999L);
        klineData.setSymbol("BTCUSDT");
        klineData.setInterval("1m");
        klineData.setOpen(new BigDecimal("54000.00"));
        klineData.setClose(new BigDecimal("54100.00"));
        klineData.setHigh(new BigDecimal("54200.00"));
        klineData.setLow(new BigDecimal("53900.00"));
        klineData.setVolume(new BigDecimal("10.5"));
        klineData.setQuoteAssetVolume(new BigDecimal("567890.00"));
        klineData.setNumberOfTrades(120L);
        
        // 转换为DTO
        KlineDataDTO dto = modelConverter.toDto(klineData, KlineDataDTO.class);
        
        // 验证结果
        assertNotNull(dto);
        assertEquals("BTCUSDT", dto.getSymbol());
        assertEquals("1m", dto.getInterval());
        assertEquals(LocalDateTime.ofInstant(Instant.ofEpochMilli(1619049240000L), ZoneId.systemDefault()), dto.getOpenTime());
        assertEquals(new BigDecimal("54000.00"), dto.getOpen());
        assertEquals(new BigDecimal("54100.00"), dto.getClose());
        assertEquals(new BigDecimal("54200.00"), dto.getHigh());
        assertEquals(new BigDecimal("53900.00"), dto.getLow());
        assertEquals(new BigDecimal("10.5"), dto.getVolume());
    }

    @Test
    public void testToApiModel() {
        // 创建DTO
        KlineDataDTO dto = new KlineDataDTO();
        dto.setSymbol("BTCUSDT");
        dto.setInterval("1m");
        dto.setOpenTime(LocalDateTime.ofInstant(Instant.ofEpochMilli(1619049240000L), ZoneId.systemDefault()));
        dto.setOpen(new BigDecimal("54000.00"));
        dto.setClose(new BigDecimal("54100.00"));
        dto.setHigh(new BigDecimal("54200.00"));
        dto.setLow(new BigDecimal("53900.00"));
        dto.setVolume(new BigDecimal("10.5"));
        
        // 转换为API模型
        KlineData klineData = modelConverter.toApiModel(dto, KlineData.class);
        
        // 验证结果
        assertNotNull(klineData);
        assertEquals("BTCUSDT", klineData.getSymbol());
        assertEquals("1m", klineData.getInterval());
        assertEquals(new BigDecimal("54000.00"), klineData.getOpen());
        assertEquals(new BigDecimal("54100.00"), klineData.getClose());
        assertEquals(new BigDecimal("54200.00"), klineData.getHigh());
        assertEquals(new BigDecimal("53900.00"), klineData.getLow());
        assertEquals(new BigDecimal("10.5"), klineData.getVolume());
    }
} 