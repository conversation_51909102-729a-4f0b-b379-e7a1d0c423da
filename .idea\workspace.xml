<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="8430db33-ecf8-4700-8791-680b0ade3e4e" name="更改" comment="init5">
      <change beforePath="$PROJECT_DIR$/crypto-trading-system/crypto-market-data/src/main/java/com/crypto/trading/market/config/KafkaProducerConfig.java" beforeDir="false" afterPath="$PROJECT_DIR$/crypto-trading-system/crypto-market-data/src/main/java/com/crypto/trading/market/config/KafkaProducerConfig.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/crypto-trading-system/crypto-market-data/src/main/java/com/crypto/trading/market/processor/DepthDataProcessor.java" beforeDir="false" afterPath="$PROJECT_DIR$/crypto-trading-system/crypto-market-data/src/main/java/com/crypto/trading/market/processor/DepthDataProcessor.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/crypto-trading-system/crypto-market-data/src/main/java/com/crypto/trading/market/processor/KlineDataProcessor.java" beforeDir="false" afterPath="$PROJECT_DIR$/crypto-trading-system/crypto-market-data/src/main/java/com/crypto/trading/market/processor/KlineDataProcessor.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/crypto-trading-system/crypto-market-data/src/main/java/com/crypto/trading/market/processor/TradeDataProcessor.java" beforeDir="false" afterPath="$PROJECT_DIR$/crypto-trading-system/crypto-market-data/src/main/java/com/crypto/trading/market/processor/TradeDataProcessor.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/crypto-trading-system/crypto-market-data/src/main/java/com/crypto/trading/market/producer/KafkaMessageProducer.java" beforeDir="false" afterPath="$PROJECT_DIR$/crypto-trading-system/crypto-market-data/src/main/java/com/crypto/trading/market/producer/KafkaMessageProducer.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/crypto-trading-system/crypto-market-data/src/main/java/com/crypto/trading/market/repository/InfluxDBRepository.java" beforeDir="false" afterPath="$PROJECT_DIR$/crypto-trading-system/crypto-market-data/src/main/java/com/crypto/trading/market/repository/InfluxDBRepository.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/crypto-trading-system/crypto-market-data/src/main/java/com/crypto/trading/market/repository/MarketDataRepository.java" beforeDir="false" afterPath="$PROJECT_DIR$/crypto-trading-system/crypto-market-data/src/main/java/com/crypto/trading/market/repository/MarketDataRepository.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/crypto-trading-system/crypto-market-data/src/main/java/com/crypto/trading/market/repository/MySQLMarketDataRepository.java" beforeDir="false" afterPath="$PROJECT_DIR$/crypto-trading-system/crypto-market-data/src/main/java/com/crypto/trading/market/repository/MySQLMarketDataRepository.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/crypto-trading-system/crypto-market-data/src/test/java/com/crypto/trading/market/processor/DepthDataProcessorTest.java" beforeDir="false" afterPath="$PROJECT_DIR$/crypto-trading-system/crypto-market-data/src/test/java/com/crypto/trading/market/processor/DepthDataProcessorTest.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/crypto-trading-system/crypto-market-data/src/test/java/com/crypto/trading/market/processor/KlineDataProcessorTest.java" beforeDir="false" afterPath="$PROJECT_DIR$/crypto-trading-system/crypto-market-data/src/test/java/com/crypto/trading/market/processor/KlineDataProcessorTest.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/crypto-trading-system/crypto-market-data/src/test/java/com/crypto/trading/market/processor/TradeDataProcessorTest.java" beforeDir="false" afterPath="$PROJECT_DIR$/crypto-trading-system/crypto-market-data/src/test/java/com/crypto/trading/market/processor/TradeDataProcessorTest.java" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="CompilerWorkspaceConfiguration">
    <option name="MAKE_PROJECT_ON_SAVE" value="true" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="GitHubPullRequestSearchHistory">{
  &quot;lastFilter&quot;: {
    &quot;state&quot;: &quot;OPEN&quot;,
    &quot;assignee&quot;: &quot;iaenolhy&quot;
  }
}</component>
  <component name="GithubPullRequestsUISettings">{
  &quot;selectedUrlAndAccountId&quot;: {
    &quot;url&quot;: &quot;https://github.com/iaenolhy/1-BA.git&quot;,
    &quot;accountId&quot;: &quot;a4ec50ee-3c43-4db9-9e5e-2da92a5cd5ec&quot;
  }
}</component>
  <component name="JRebelWorkspace">
    <option name="jrebelEnabledAutocompile" value="true" />
  </component>
  <component name="KubernetesApiPersistence">{}</component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="D:\Software\apache-maven-3.9.3" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="useMavenConfig" value="false" />
        <option name="userSettingsFile" value="D:\Software\apache-maven-3.9.3\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;customColor&quot;: &quot;&quot;,
  &quot;associatedIndex&quot;: 6
}</component>
  <component name="ProjectId" id="2xdGyGI7QetP2ybunukIMwpMFfV" />
  <component name="ProjectViewState">
    <option name="flattenModules" value="true" />
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
    <option name="showVisibilityIcons" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Docker.docker.influxdb: Compose 部署.executor&quot;: &quot;Run&quot;,
    &quot;Docker.docker.kafka-init: Compose 部署.executor&quot;: &quot;Run&quot;,
    &quot;Docker.docker.kafka-ui: Compose 部署.executor&quot;: &quot;Run&quot;,
    &quot;Docker.docker.kafka: Compose 部署.executor&quot;: &quot;Run&quot;,
    &quot;Docker.docker.redis: Compose 部署.executor&quot;: &quot;Run&quot;,
    &quot;Docker.docker.zookeeper: Compose 部署.executor&quot;: &quot;Run&quot;,
    &quot;Docker.docker: Compose 部署.executor&quot;: &quot;Run&quot;,
    &quot;Maven.crypto-trading [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.crypto-trading [install].executor&quot;: &quot;Run&quot;,
    &quot;Maven.crypto-trading [site].executor&quot;: &quot;Run&quot;,
    &quot;Maven.crypto-trading [validate].executor&quot;: &quot;Run&quot;,
    &quot;Maven.crypto-trading [verify].executor&quot;: &quot;Run&quot;,
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;Python.setup.executor&quot;: &quot;Run&quot;,
    &quot;Python.start.executor&quot;: &quot;Run&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;Spring Boot.CryptoApplication.executor&quot;: &quot;JRebel Debug&quot;,
    &quot;Spring Boot.TradeApplication.executor&quot;: &quot;JRebel Debug&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;junie.onboarding.icon.badge.shown&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;模块&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.15&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.3770115&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;MavenSettings&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  },
  &quot;keyToStringList&quot;: {
    &quot;DatabaseDriversLRU&quot;: [
      &quot;redis&quot;
    ]
  }
}</component>
  <component name="PyConsoleOptionsProvider">
    <option name="myPythonConsoleState">
      <console-settings sdk-home="$PROJECT_DIR$/../Software/anaconda3/Scripts/conda.exe">
        <option name="sdk">
          <Sdk />
        </option>
        <option name="mySdkHome" value="$PROJECT_DIR$/../Software/anaconda3/Scripts/conda.exe" />
        <option name="mySdk">
          <Sdk />
        </option>
      </console-settings>
    </option>
  </component>
  <component name="RebelAgentSelection">
    <selection>jr</selection>
  </component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="SpringBootApplicationConfigurationType" />
      </set>
    </option>
  </component>
  <component name="RunManager" selected="Docker.docker: Compose 部署">
    <configuration default="true" type="JetRunConfigurationType">
      <module name="1_deep_bian" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="KotlinStandaloneScriptRunConfigurationType">
      <module name="1_deep_bian" />
      <option name="filePath" />
      <method v="2" />
    </configuration>
    <configuration default="true" type="PythonConfigurationType" factoryName="Python">
      <module name="1_deep_bian" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="start" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="crypto-trading" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/crypto-trading-system/crypto-ml-strategy" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/crypto-trading-system/crypto-ml-strategy/start.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration default="true" type="Python.FlaskServer">
      <module name="1_deep_bian" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="launchJavascriptDebuger" value="false" />
      <method v="2" />
    </configuration>
    <configuration name="CryptoApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="crypto-bootstrap" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.crypto.trading.bootstrap.CryptoApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="TradeApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="crypto-trade" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.crypto.trading.trade.TradeApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="Tox" factoryName="Tox">
      <module name="1_deep_bian" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <method v="2" />
    </configuration>
    <configuration default="true" type="docker-deploy" factoryName="docker-compose.yml" temporary="true">
      <deployment type="docker-compose.yml">
        <settings />
      </deployment>
      <method v="2" />
    </configuration>
    <configuration name="docker: Compose 部署" type="docker-deploy" factoryName="docker-compose.yml" temporary="true" server-name="Docker">
      <deployment type="docker-compose.yml">
        <settings>
          <option name="sourceFilePath" value="crypto-trading-system/docker/docker-compose.yml" />
        </settings>
      </deployment>
      <method v="2" />
    </configuration>
    <configuration name="docker.influxdb: Compose 部署" type="docker-deploy" factoryName="docker-compose.yml" temporary="true" server-name="Docker">
      <deployment type="docker-compose.yml">
        <settings>
          <option name="services">
            <list>
              <option value="influxdb" />
            </list>
          </option>
          <option name="sourceFilePath" value="crypto-trading-system/docker/docker-compose.yml" />
        </settings>
      </deployment>
      <method v="2" />
    </configuration>
    <configuration name="docker.kafka-ui: Compose 部署" type="docker-deploy" factoryName="docker-compose.yml" temporary="true" server-name="Docker">
      <deployment type="docker-compose.yml">
        <settings>
          <option name="services">
            <list>
              <option value="kafka-ui" />
            </list>
          </option>
          <option name="sourceFilePath" value="crypto-trading-system/docker/docker-compose.yml" />
        </settings>
      </deployment>
      <method v="2" />
    </configuration>
    <configuration name="docker.redis: Compose 部署" type="docker-deploy" factoryName="docker-compose.yml" temporary="true" server-name="Docker">
      <deployment type="docker-compose.yml">
        <settings>
          <option name="services">
            <list>
              <option value="redis" />
            </list>
          </option>
          <option name="sourceFilePath" value="crypto-trading-system/docker/docker-compose.yml" />
        </settings>
      </deployment>
      <method v="2" />
    </configuration>
    <configuration default="true" type="tests" factoryName="Autodetect">
      <module name="market-data" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="_new_additionalArguments" value="&quot;&quot;" />
      <option name="_new_target" value="&quot;&quot;" />
      <option name="_new_targetType" value="&quot;PATH&quot;" />
      <method v="2" />
    </configuration>
    <configuration default="true" type="tests" factoryName="Doctests">
      <module name="market-data" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="" />
      <option name="CLASS_NAME" value="" />
      <option name="METHOD_NAME" value="" />
      <option name="FOLDER_NAME" value="" />
      <option name="TEST_TYPE" value="TEST_SCRIPT" />
      <option name="PATTERN" value="" />
      <option name="USE_PATTERN" value="false" />
      <method v="2" />
    </configuration>
    <list>
      <item itemvalue="Docker.docker: Compose 部署" />
      <item itemvalue="Docker.docker.influxdb: Compose 部署" />
      <item itemvalue="Docker.docker.kafka-ui: Compose 部署" />
      <item itemvalue="Docker.docker.redis: Compose 部署" />
      <item itemvalue="Python.start" />
      <item itemvalue="Spring Boot.CryptoApplication" />
      <item itemvalue="Spring Boot.TradeApplication" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="Docker.docker: Compose 部署" />
        <item itemvalue="Docker.docker.influxdb: Compose 部署" />
        <item itemvalue="Python.start" />
        <item itemvalue="Docker.docker.redis: Compose 部署" />
        <item itemvalue="Docker.docker.kafka-ui: Compose 部署" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.26094.121" />
        <option value="bundled-js-predefined-d6986cc7102b-b26f3e71634d-JavaScript-IU-251.26094.121" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="8430db33-ecf8-4700-8791-680b0ade3e4e" name="更改" comment="" />
      <created>1748260791007</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1748260791007</updated>
      <workItem from="1748260792967" duration="6959000" />
      <workItem from="1748281868109" duration="40982000" />
      <workItem from="1748449569735" duration="36172000" />
      <workItem from="1748620374420" duration="4187000" />
      <workItem from="1748950522692" duration="13552000" />
      <workItem from="1749059388426" duration="7654000" />
      <workItem from="1749069854517" duration="23298000" />
      <workItem from="1749187230148" duration="1407000" />
      <workItem from="1749272453858" duration="2204000" />
      <workItem from="1749295862195" duration="1535000" />
      <workItem from="1749297955661" duration="5464000" />
      <workItem from="1749341482869" duration="15846000" />
      <workItem from="1749387304599" duration="13318000" />
      <workItem from="1749557695107" duration="1704000" />
      <workItem from="1749562681941" duration="2174000" />
      <workItem from="1749566414021" duration="105000" />
      <workItem from="1749632832323" duration="7881000" />
      <workItem from="1749644546711" duration="1762000" />
      <workItem from="1749646386907" duration="16981000" />
      <workItem from="1749737144488" duration="39180000" />
      <workItem from="1749838002872" duration="405000" />
      <workItem from="1749876717118" duration="4032000" />
      <workItem from="1749915390143" duration="17957000" />
      <workItem from="1750073561922" duration="28660000" />
      <workItem from="1750164423356" duration="27012000" />
      <workItem from="1750256209355" duration="35795000" />
      <workItem from="1750501568729" duration="10502000" />
      <workItem from="1750524674915" duration="731000" />
      <workItem from="1750529860523" duration="1301000" />
      <workItem from="1750535000683" duration="1103000" />
      <workItem from="1750581562434" duration="1374000" />
      <workItem from="1750584548733" duration="6448000" />
    </task>
    <task id="LOCAL-00001" summary="init">
      <option name="closed" value="true" />
      <created>1750339492762</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1750339492762</updated>
    </task>
    <task id="LOCAL-00002" summary="init2">
      <option name="closed" value="true" />
      <created>1750340330917</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1750340330917</updated>
    </task>
    <task id="LOCAL-00003" summary="init3">
      <option name="closed" value="true" />
      <created>1750340450384</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1750340450384</updated>
    </task>
    <task id="LOCAL-00004" summary="init4">
      <option name="closed" value="true" />
      <created>1750348161381</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1750348161381</updated>
    </task>
    <task id="LOCAL-00005" summary="init5">
      <option name="closed" value="true" />
      <created>1750487815987</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1750487815987</updated>
    </task>
    <task id="LOCAL-00006" summary="init5">
      <option name="closed" value="true" />
      <created>1750487989008</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1750487989008</updated>
    </task>
    <task id="LOCAL-00007" summary="init5">
      <option name="closed" value="true" />
      <created>1750488076735</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1750488076735</updated>
    </task>
    <task id="LOCAL-00008" summary="init5">
      <option name="closed" value="true" />
      <created>1750518172313</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1750518172313</updated>
    </task>
    <option name="localTasksCounter" value="9" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="origin/feature/task-1101-risk-management-tests" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="init" />
    <MESSAGE value="init2" />
    <MESSAGE value="init3" />
    <MESSAGE value="init4" />
    <MESSAGE value="init5" />
    <option name="LAST_COMMIT_MESSAGE" value="init5" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/crypto-trading-system/crypto-bootstrap/src/main/java/com/crypto/trading/bootstrap/config/adapter/BinanceApiConfigAdapter.java</url>
          <line>63</line>
          <option name="timeStamp" value="1" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/crypto-trading-system/crypto-sdk/src/main/java/com/crypto/trading/sdk/client/CMFuturesApiClientImpl.java</url>
          <line>31</line>
          <option name="timeStamp" value="3" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/crypto-trading-system/crypto-sdk/src/main/java/com/crypto/trading/sdk/client/UMFuturesApiClientImpl.java</url>
          <line>34</line>
          <option name="timeStamp" value="4" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/crypto-trading-system/crypto-sdk/src/main/java/com/crypto/trading/sdk/config/BinanceApiConfig.java</url>
          <line>157</line>
          <option name="timeStamp" value="5" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/crypto-trading-system/crypto-bootstrap/src/main/java/com/crypto/trading/bootstrap/controller/HistoricalDataController.java</url>
          <line>85</line>
          <option name="timeStamp" value="7" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/crypto-trading-system/crypto-bootstrap/src/main/java/com/crypto/trading/bootstrap/controller/HistoricalDataController.java</url>
          <line>87</line>
          <option name="timeStamp" value="8" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/crypto-trading-system/crypto-bootstrap/src/main/java/com/crypto/trading/bootstrap/controller/HistoricalDataController.java</url>
          <line>111</line>
          <option name="timeStamp" value="9" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/crypto-trading-system/crypto-bootstrap/src/main/java/com/crypto/trading/bootstrap/controller/HistoricalDataController.java</url>
          <line>132</line>
          <option name="timeStamp" value="10" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/crypto-trading-system/crypto-bootstrap/src/main/java/com/crypto/trading/bootstrap/controller/HistoricalDataController.java</url>
          <line>134</line>
          <option name="timeStamp" value="11" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/crypto-trading-system/crypto-bootstrap/src/main/java/com/crypto/trading/bootstrap/controller/HistoricalDataController.java</url>
          <line>156</line>
          <option name="timeStamp" value="12" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/crypto-trading-system/crypto-bootstrap/src/main/java/com/crypto/trading/bootstrap/controller/HistoricalDataController.java</url>
          <line>179</line>
          <option name="timeStamp" value="13" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/crypto-trading-system/crypto-bootstrap/src/main/java/com/crypto/trading/bootstrap/controller/HistoricalDataController.java</url>
          <line>198</line>
          <option name="timeStamp" value="14" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/crypto-trading-system/crypto-bootstrap/src/main/java/com/crypto/trading/bootstrap/controller/HistoricalDataController.java</url>
          <line>240</line>
          <option name="timeStamp" value="15" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/crypto-trading-system/crypto-bootstrap/src/main/java/com/crypto/trading/bootstrap/controller/HistoricalDataController.java</url>
          <line>284</line>
          <option name="timeStamp" value="16" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/crypto-trading-system/crypto-bootstrap/src/main/java/com/crypto/trading/bootstrap/controller/HistoricalDataController.java</url>
          <line>307</line>
          <option name="timeStamp" value="17" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/crypto-trading-system/crypto-bootstrap/src/main/java/com/crypto/trading/bootstrap/controller/HistoricalDataController.java</url>
          <line>330</line>
          <option name="timeStamp" value="18" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/crypto-trading-system/crypto-bootstrap/src/main/java/com/crypto/trading/bootstrap/controller/HistoricalDataController.java</url>
          <line>64</line>
          <option name="timeStamp" value="19" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/crypto-trading-system/crypto-bootstrap/src/main/java/com/crypto/trading/bootstrap/controller/HistoricalDataController.java</url>
          <line>73</line>
          <option name="timeStamp" value="20" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/crypto-trading-system/crypto-bootstrap/src/main/java/com/crypto/trading/bootstrap/controller/HistoricalDataController.java</url>
          <line>72</line>
          <option name="timeStamp" value="21" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/crypto-trading-system/crypto-market-data/src/main/java/com/crypto/trading/market/service/impl/HistoricalDataServiceImpl.java</url>
          <line>107</line>
          <option name="timeStamp" value="22" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/crypto-trading-system/crypto-market-data/src/main/java/com/crypto/trading/market/processor/KlineDataProcessor.java</url>
          <line>56</line>
          <option name="timeStamp" value="24" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/crypto-trading-system/crypto-market-data/src/main/java/com/crypto/trading/market/listener/KlineDataListener.java</url>
          <line>124</line>
          <option name="timeStamp" value="25" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/crypto-trading-system/crypto-bootstrap/src/main/java/com/crypto/trading/bootstrap/controller/HistoricalDataController.java</url>
          <line>67</line>
          <option name="timeStamp" value="27" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/crypto-trading-system/crypto-bootstrap/src/main/java/com/crypto/trading/bootstrap/controller/HistoricalDataController.java</url>
          <line>77</line>
          <option name="timeStamp" value="28" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/crypto-trading-system/crypto-market-data/src/main/java/com/crypto/trading/market/listener/KlineDataListener.java</url>
          <line>103</line>
          <option name="timeStamp" value="30" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/crypto-trading-system/crypto-market-data/src/main/java/com/crypto/trading/market/service/impl/HistoricalDataServiceImpl.java</url>
          <line>160</line>
          <option name="timeStamp" value="34" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/crypto-trading-system/crypto-market-data/src/main/java/com/crypto/trading/market/processor/DepthDataProcessor.java</url>
          <line>55</line>
          <option name="timeStamp" value="37" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/crypto-trading-system/crypto-market-data/src/main/java/com/crypto/trading/market/listener/WebSocketStartupListener.java</url>
          <line>53</line>
          <option name="timeStamp" value="40" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/crypto-trading-system/crypto-market-data/src/main/java/com/crypto/trading/market/listener/KlineDataListener.java</url>
          <line>106</line>
          <option name="timeStamp" value="42" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/crypto-trading-system/crypto-sdk/src/main/java/com/crypto/trading/sdk/websocket/BinanceWebSocketClientImpl.java</url>
          <line>143</line>
          <option name="timeStamp" value="44" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/crypto-trading-system/crypto-market-data/src/main/java/com/crypto/trading/market/processor/DepthDataProcessor.java</url>
          <line>160</line>
          <option name="timeStamp" value="45" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/crypto-trading-system/crypto-market-data/src/main/java/com/crypto/trading/market/listener/DepthDataListener.java</url>
          <line>108</line>
          <option name="timeStamp" value="46" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/crypto-trading-system/crypto-market-data/src/main/java/com/crypto/trading/market/listener/DepthDataListener.java</url>
          <line>149</line>
          <option name="timeStamp" value="47" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/crypto-trading-system/crypto-market-data/src/main/java/com/crypto/trading/market/listener/DepthDataListener.java</url>
          <line>118</line>
          <option name="timeStamp" value="48" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/crypto-trading-system/crypto-market-data/src/main/java/com/crypto/trading/market/processor/DepthDataProcessor.java</url>
          <line>87</line>
          <option name="timeStamp" value="51" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/crypto-trading-system/crypto-market-data/src/main/java/com/crypto/trading/market/repository/MySQLMarketDataRepository.java</url>
          <line>114</line>
          <option name="timeStamp" value="52" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/crypto-trading-system/crypto-market-data/src/main/java/com/crypto/trading/market/listener/DepthDataListener.java</url>
          <line>180</line>
          <option name="timeStamp" value="53" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/crypto-trading-system/crypto-market-data/src/main/java/com/crypto/trading/market/processor/DepthDataProcessor.java</url>
          <line>95</line>
          <option name="timeStamp" value="54" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/crypto-trading-system/crypto-market-data/src/main/java/com/crypto/trading/market/processor/DepthDataProcessor.java</url>
          <line>74</line>
          <option name="timeStamp" value="55" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/crypto-trading-system/crypto-market-data/src/main/java/com/crypto/trading/market/processor/DepthDataProcessor.java</url>
          <line>63</line>
          <option name="timeStamp" value="56" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/crypto-trading-system/crypto-market-data/src/main/java/com/crypto/trading/market/converter/MarketDataConverter.java</url>
          <line>47</line>
          <option name="timeStamp" value="57" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/crypto-trading-system/crypto-market-data/src/main/java/com/crypto/trading/market/listener/TradeDataListener.java</url>
          <line>95</line>
          <option name="timeStamp" value="58" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/crypto-trading-system/crypto-market-data/src/main/java/com/crypto/trading/market/listener/TradeDataListener.java</url>
          <line>112</line>
          <option name="timeStamp" value="60" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/crypto-trading-system/crypto-market-data/src/main/java/com/crypto/trading/market/listener/TradeDataListener.java</url>
          <line>108</line>
          <option name="timeStamp" value="61" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/crypto-trading-system/crypto-market-data/src/main/java/com/crypto/trading/market/repository/MySQLMarketDataRepository.java</url>
          <line>202</line>
          <option name="timeStamp" value="62" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/crypto-trading-system/crypto-market-data/src/main/java/com/crypto/trading/market/repository/MySQLMarketDataRepository.java</url>
          <line>172</line>
          <option name="timeStamp" value="63" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/crypto-trading-system/crypto-market-data/src/main/java/com/crypto/trading/market/repository/MySQLMarketDataRepository.java</url>
          <line>118</line>
          <option name="timeStamp" value="64" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/crypto-trading-system/crypto-market-data/src/main/java/com/crypto/trading/market/converter/MarketDataConverter.java</url>
          <line>165</line>
          <option name="timeStamp" value="68" />
        </line-breakpoint>
        <line-breakpoint type="java-field">
          <url>file://$PROJECT_DIR$/crypto-trading-system/crypto-sdk/src/main/java/com/crypto/trading/sdk/config/BinanceApiConfig.java</url>
          <line>65</line>
          <properties field="coinFuturesWsBaseUrl" class="com.crypto.trading.sdk.config.BinanceApiConfig" />
          <option name="timeStamp" value="2" />
        </line-breakpoint>
      </breakpoints>
      <default-breakpoints>
        <breakpoint type="python-exception">
          <properties notifyOnTerminate="true" exception="BaseException">
            <option name="notifyOnTerminate" value="true" />
          </properties>
        </breakpoint>
      </default-breakpoints>
    </breakpoint-manager>
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/1_deep_bian$start.coverage" NAME="start 覆盖结果" MODIFIED="1749350181959" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/crypto-trading-system/crypto-ml-strategy" />
    <SUITE FILE_PATH="coverage/1_deep_bian$setup.coverage" NAME="setup 覆盖结果" MODIFIED="1749296773019" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/crypto-trading-system/crypto-ml-strategy" />
  </component>
</project>