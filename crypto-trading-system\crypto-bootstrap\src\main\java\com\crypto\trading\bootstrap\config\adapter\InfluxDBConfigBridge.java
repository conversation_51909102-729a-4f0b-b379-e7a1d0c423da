package com.crypto.trading.bootstrap.config.adapter;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.beans.factory.annotation.Autowired;

import com.crypto.trading.market.config.InfluxDBConfig;

/**
 * InfluxDBConfig桥接类
 * 负责将配置参数转换为market-data模块的InfluxDBConfig对象
 * 
 * 注意：此类已被简化，直接使用market-data模块的InfluxDBConfig
 */
@Configuration
public class InfluxDBConfigBridge {
    
    private static final Logger log = LoggerFactory.getLogger(InfluxDBConfigBridge.class);
    
    @Autowired
    private InfluxDBConfig influxDBConfig;
    
    /**
     * 为InfluxDBRepository创建所需的com.crypto.trading.market.config.InfluxDBConfig Bean
     * 由于从排除过滤器中移除了market模块的InfluxDBConfig，此方法只返回已注入的实例
     * 
     * @return Market模块需要的InfluxDBConfig实例
     */
    @Bean(name = "fallbackMarketModuleInfluxDBConfig")
    public InfluxDBConfig fallbackMarketModuleInfluxDBConfig() {
        log.info("提供Market模块InfluxDBConfig Bean - 直接使用已注入的实例");
        log.info("InfluxDB配置参数: url={}, org={}, bucket={}", 
                influxDBConfig.getUrl(), influxDBConfig.getOrg(), influxDBConfig.getBucket());
        
        return influxDBConfig;
    }
}