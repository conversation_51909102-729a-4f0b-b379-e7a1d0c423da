package com.crypto.trading.sdk.limiter;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 令牌桶限流器测试类
 */
public class TokenBucketRateLimiterTest {

    private TokenBucketRateLimiter rateLimiter;

    @BeforeEach
    public void setUp() {
        // 创建容量为10，每秒补充2个令牌的限流器
        rateLimiter = new TokenBucketRateLimiter(10, 2);
    }

    @AfterEach
    public void tearDown() {
        // 停止令牌桶的定时任务
        rateLimiter.stop();
    }

    @Test
    public void testTryAcquire_SinglePermit() {
        // 测试获取单个令牌
        assertTrue(rateLimiter.tryAcquire(), "应该能够获取单个令牌");
    }

    @Test
    public void testTryAcquire_MultiplePermits() {
        // 测试获取多个令牌
        assertTrue(rateLimiter.tryAcquire(5), "应该能够获取5个令牌");
        assertTrue(rateLimiter.tryAcquire(5), "应该能够获取剩余的5个令牌");
        assertFalse(rateLimiter.tryAcquire(1), "桶中已经没有令牌，应该获取失败");
    }

    @Test
    public void testTryAcquire_ZeroPermits() {
        // 测试获取0个令牌
        assertTrue(rateLimiter.tryAcquire(0), "获取0个令牌应该总是成功");
    }

    @Test
    public void testTryAcquire_NegativePermits() {
        // 测试获取负数个令牌
        assertTrue(rateLimiter.tryAcquire(-1), "获取负数个令牌应该总是成功");
    }

    @Test
    public void testTryAcquire_ExceedCapacity() {
        // 测试获取超过容量的令牌
        assertFalse(rateLimiter.tryAcquire(11), "获取超过容量的令牌应该失败");
    }

    @Test
    public void testAcquire_BlockingBehavior() throws InterruptedException {
        // 创建一个特殊的限流器，不会自动补充令牌
        TokenBucketRateLimiter specialRateLimiter = new TokenBucketRateLimiter(10, 2) {
            @Override
            public boolean tryAcquire(int permits) {
                // 覆盖方法，不调用refill
                if (permits <= 0) {
                    return true;
                }
                if (permits > this.getCurrentTokens()) {
                    return false;
                }
                return super.tryAcquire(permits);
            }
        };
        
        // 先获取所有令牌
        assertTrue(specialRateLimiter.tryAcquire(10), "应该能够获取所有令牌");

        // 创建一个计数器，记录成功获取令牌的次数
        AtomicInteger acquireCount = new AtomicInteger(0);

        // 创建一个线程，尝试获取令牌
        ExecutorService executor = Executors.newVirtualThreadPerTaskExecutor();
        CountDownLatch latch = new CountDownLatch(1);

        executor.submit(() -> {
            try {
                // 尝试获取1个令牌，这应该会阻塞
                specialRateLimiter.acquire(1);
                acquireCount.incrementAndGet();
                latch.countDown();
            } catch (Exception e) {
                fail("获取令牌不应该抛出异常: " + e.getMessage());
            }
        });

        // 等待一段时间，此时应该还没有获取到令牌
        TimeUnit.MILLISECONDS.sleep(500);
        assertEquals(0, acquireCount.get(), "应该还没有获取到令牌");

        // 手动补充令牌
        specialRateLimiter.forceRefill();

        // 等待线程完成
        assertTrue(latch.await(2, TimeUnit.SECONDS), "线程应该在令牌补充后完成");
        assertEquals(1, acquireCount.get(), "应该成功获取到令牌");

        executor.shutdown();
        specialRateLimiter.stop();
    }

    @Test
    public void testConcurrentAccess() throws InterruptedException {
        // 测试并发访问
        int threadCount = 20;
        ExecutorService executor = Executors.newVirtualThreadPerTaskExecutor();
        CountDownLatch startLatch = new CountDownLatch(1);
        CountDownLatch endLatch = new CountDownLatch(threadCount);
        AtomicInteger successCount = new AtomicInteger(0);

        // 创建20个线程，同时尝试获取令牌
        for (int i = 0; i < threadCount; i++) {
            executor.submit(() -> {
                try {
                    startLatch.await(); // 等待所有线程就绪
                    if (rateLimiter.tryAcquire()) {
                        successCount.incrementAndGet();
                    }
                } catch (Exception e) {
                    fail("获取令牌不应该抛出异常: " + e.getMessage());
                } finally {
                    endLatch.countDown();
                }
            });
        }

        // 启动所有线程
        startLatch.countDown();
        
        // 等待所有线程完成
        assertTrue(endLatch.await(2, TimeUnit.SECONDS), "所有线程应该在2秒内完成");
        
        // 验证只有10个线程成功获取到令牌
        assertEquals(10, successCount.get(), "应该只有10个线程成功获取到令牌");

        executor.shutdown();
    }

    @Test
    public void testRefill() throws InterruptedException {
        // 测试令牌自动补充
        
        // 先获取所有令牌
        assertTrue(rateLimiter.tryAcquire(10), "应该能够获取所有令牌");
        assertFalse(rateLimiter.tryAcquire(1), "桶中已经没有令牌，应该获取失败");

        // 等待令牌补充（每秒补充2个）
        TimeUnit.SECONDS.sleep(1);
        
        // 强制刷新令牌
        rateLimiter.forceRefill();

        // 应该能够获取2个令牌
        assertTrue(rateLimiter.tryAcquire(2), "应该能够获取2个补充的令牌");
        assertFalse(rateLimiter.tryAcquire(1), "桶中已经没有令牌，应该获取失败");

        // 再等待2秒，应该能够获取4个令牌
        TimeUnit.SECONDS.sleep(2);
        
        // 强制刷新令牌
        rateLimiter.forceRefill();
        
        // 打印当前令牌数
        System.out.println("当前令牌数: " + rateLimiter.getCurrentTokens());
        
        // 应该能够获取4个令牌
        assertTrue(rateLimiter.tryAcquire(4), "应该能够获取4个补充的令牌");
        assertFalse(rateLimiter.tryAcquire(1), "桶中已经没有令牌，应该获取失败");
    }
}