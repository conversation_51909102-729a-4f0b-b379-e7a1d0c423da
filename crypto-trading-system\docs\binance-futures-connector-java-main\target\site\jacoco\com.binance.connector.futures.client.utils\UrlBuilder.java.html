<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>UrlBuilder.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">io.github.binance:binance-futures-connector-java</a> &gt; <a href="index.source.html" class="el_package">com.binance.connector.futures.client.utils</a> &gt; <span class="el_source">UrlBuilder.java</span></div><h1>UrlBuilder.java</h1><pre class="source lang-java linenums">package com.binance.connector.futures.client.utils;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.DecimalFormat;
import java.text.DecimalFormatSymbols;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.Locale;
import java.util.Map;


public final class UrlBuilder {
    private static final int MAX_DECIMAL_DIGITS = 30;
    private static DecimalFormat df;


    private UrlBuilder() {
    }

    public static String buildFullUrl(String baseUrl, String urlPath, LinkedHashMap&lt;String, Object&gt; parameters, String signature) {
<span class="fc bfc" id="L23" title="All 4 branches covered.">        if (parameters != null &amp;&amp; !parameters.isEmpty()) {</span>
<span class="fc" id="L24">            StringBuilder sb = new StringBuilder(baseUrl);</span>
<span class="fc" id="L25">            sb.append(urlPath).append('?');</span>
<span class="fc" id="L26">            joinQueryParameters(sb, parameters);</span>
<span class="fc bfc" id="L27" title="All 2 branches covered.">            if (null != signature) {</span>
<span class="fc" id="L28">                sb.append(&quot;&amp;signature=&quot;).append(signature);</span>
            }
<span class="fc" id="L30">            return sb.toString();</span>
        } else {
<span class="fc" id="L32">            return baseUrl + urlPath;</span>
        }
    }

    public static String buildStreamUrl(String baseUrl, ArrayList&lt;String&gt; streams) {
<span class="fc" id="L37">        StringBuilder sb = new StringBuilder(baseUrl);</span>
<span class="fc" id="L38">        sb.append(&quot;?streams=&quot;);</span>
<span class="fc" id="L39">        return joinStreamUrls(sb, streams);</span>
    }

    //concatenate query parameters
    public static String joinQueryParameters(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L44">        return joinQueryParameters(new StringBuilder(), parameters).toString();</span>
    }

    public static StringBuilder joinQueryParameters(StringBuilder urlPath, LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="pc bpc" id="L48" title="1 of 4 branches missed.">        if (parameters == null || parameters.isEmpty()) {</span>
<span class="fc" id="L49">            return urlPath;</span>
        }

<span class="fc" id="L52">        boolean isFirst = true;</span>
<span class="fc bfc" id="L53" title="All 2 branches covered.">        for (Map.Entry&lt;String, Object&gt; mapElement : parameters.entrySet()) {</span>

<span class="fc bfc" id="L55" title="All 2 branches covered.">            if (mapElement.getValue() instanceof Double) {</span>
<span class="fc" id="L56">                parameters.replace(mapElement.getKey(), getFormatter().format(mapElement.getValue()));</span>
<span class="pc bpc" id="L57" title="1 of 2 branches missed.">            } else if (mapElement.getValue() instanceof ArrayList) {</span>
<span class="nc bnc" id="L58" title="All 2 branches missed.">                if (((ArrayList&lt;?&gt;) mapElement.getValue()).isEmpty()) {</span>
<span class="nc" id="L59">                    continue;</span>
                }
<span class="nc" id="L61">                String key = mapElement.getKey();</span>
<span class="nc" id="L62">                joinArrayListParameters(key, urlPath, (ArrayList&lt;?&gt;) mapElement.getValue(), isFirst);</span>
<span class="nc" id="L63">                isFirst = false;</span>
<span class="nc" id="L64">                continue;</span>
            }

<span class="fc bfc" id="L67" title="All 2 branches covered.">            if (isFirst) {</span>
<span class="fc" id="L68">                isFirst = false;</span>
            } else {
<span class="fc" id="L70">                urlPath.append('&amp;');</span>
            }

<span class="fc" id="L73">            urlPath.append(mapElement.getKey())</span>
<span class="fc" id="L74">                .append('=')</span>
<span class="fc" id="L75">                .append(urlEncode(mapElement.getValue().toString()));</span>
<span class="fc" id="L76">        }</span>
<span class="fc" id="L77">        return urlPath;</span>
    }

    private static void joinArrayListParameters(String key, StringBuilder urlPath, ArrayList&lt;?&gt; values, boolean isFirst) {
<span class="nc bnc" id="L81" title="All 2 branches missed.">        for (Object value: values) {</span>
<span class="nc bnc" id="L82" title="All 2 branches missed.">            if (isFirst) {</span>
<span class="nc" id="L83">                isFirst = false;</span>
            } else {
<span class="nc" id="L85">                urlPath.append('&amp;');</span>
            }

<span class="nc" id="L88">            urlPath.append(key)</span>
<span class="nc" id="L89">                    .append('=')</span>
<span class="nc" id="L90">                    .append(urlEncode(value.toString()));</span>
<span class="nc" id="L91">        }</span>
<span class="nc" id="L92">    }</span>

    private static String joinStreamUrls(StringBuilder urlPath, ArrayList&lt;String&gt; streams) {
<span class="fc" id="L95">        boolean isFirst = true;</span>
<span class="fc bfc" id="L96" title="All 2 branches covered.">        for (String stream: streams) {</span>
<span class="fc bfc" id="L97" title="All 2 branches covered.">            if (isFirst) {</span>
<span class="fc" id="L98">                isFirst = false;</span>
            } else {
<span class="fc" id="L100">                urlPath.append('/');</span>
            }
<span class="fc" id="L102">            urlPath.append(stream);</span>
<span class="fc" id="L103">        }</span>
<span class="fc" id="L104">        return urlPath.toString();</span>
    }


    public static String urlEncode(String s) {
        try {
<span class="fc" id="L110">            return URLEncoder.encode(s, StandardCharsets.UTF_8.name());</span>
<span class="nc" id="L111">        } catch (UnsupportedEncodingException e) {</span>
            // UTF-8 being unsuppored is unlikely
            // Replace with a unchecked exception to tidy up exception handling
<span class="nc" id="L114">            throw new RuntimeException(StandardCharsets.UTF_8.name() + &quot; is unsupported&quot;, e);</span>
        }
    }

    private static DecimalFormat getFormatter() {
<span class="fc bfc" id="L119" title="All 2 branches covered.">        if (null == df) {</span>
            // Overrides the default Locale
<span class="fc" id="L121">            DecimalFormatSymbols symbols = new DecimalFormatSymbols(Locale.ENGLISH);</span>
<span class="fc" id="L122">            df = new DecimalFormat(&quot;#,##0.###&quot;, symbols);</span>
<span class="fc" id="L123">            df.setMaximumFractionDigits(MAX_DECIMAL_DIGITS);</span>
<span class="fc" id="L124">            df.setGroupingUsed(false);</span>
        }
<span class="fc" id="L126">        return df;</span>
    }

    public static String buildTimestamp() {
<span class="fc" id="L130">        return String.valueOf(System.currentTimeMillis());</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.7.202105040129</span></div></body></html>