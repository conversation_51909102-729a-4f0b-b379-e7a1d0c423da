"""
Crypto ML Strategy - 基础设施模块

该包包含系统基础设施相关的所有组件，包括错误处理、日志管理、
启动序列、服务管理、可靠性保障等核心基础设施功能。

主要组件:
- error_handler_core: 核心错误处理器
- logging_core_manager: 日志核心管理器
- startup_sequence_core: 启动序列核心
- dependency_container: 依赖注入容器
- service_factory: 服务工厂
- circuit_breaker: 熔断器
- failover_manager: 故障转移管理器
- graceful_degradation: 优雅降级

作者: Crypto ML Strategy Team
版本: 1.0.0
"""

# 核心基础设施组件将在需要时导入
# from .error_handler_core import ErrorHandler
# from .logging_core_manager import LoggingCoreManager
# from .startup_sequence_core import StartupSequenceCore
# from .dependency_container import DependencyContainer
# from .service_factory import ServiceFactory

__all__ = []

__version__ = '1.0.0'
__author__ = 'Crypto ML Strategy Team'