"""
统一数据访问服务

协调所有数据访问组件，提供统一的数据访问接口。
支持数据源路由、负载均衡、故障转移、熔断机制等高级功能。

Author: Crypto ML Strategy Team
Date: 2024-12-19
"""

import asyncio
import logging
import time
import threading
from typing import Dict, List, Optional, Any, Union, Tuple, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import pandas as pd
from collections import defaultdict, deque
import json

from .data_config import (
    DataAccessConfig, DataSourceConfig, DataSourceType,
    APIConfig, DatabaseConfig, KafkaConfig, CacheConfig
)
from .clients.api_client import JavaAPIClient
from .clients.database_connector import DatabaseConnector, DatabaseManager
from .cache.cache_manager import CacheManager
from .realtime_processor import RealtimeProcessor, MessageHandler, StreamMessage
from .quality.data_validator import DataValidator, ValidationLevel, ValidationReport
from .quality.data_quality_checker import DataQualityChecker

logger = logging.getLogger(__name__)


class ServiceStatus(Enum):
    """服务状态枚举"""
    INITIALIZING = "initializing"
    RUNNING = "running"
    DEGRADED = "degraded"
    STOPPED = "stopped"
    ERROR = "error"


class CircuitBreakerState(Enum):
    """熔断器状态枚举"""
    CLOSED = "closed"      # 正常状态
    OPEN = "open"          # 熔断状态
    HALF_OPEN = "half_open"  # 半开状态


@dataclass
class CircuitBreaker:
    """熔断器"""
    failure_threshold: int = 5
    timeout: int = 60
    failure_count: int = 0
    last_failure_time: Optional[datetime] = None
    state: CircuitBreakerState = CircuitBreakerState.CLOSED
    
    def record_success(self) -> None:
        """记录成功"""
        self.failure_count = 0
        self.state = CircuitBreakerState.CLOSED
    
    def record_failure(self) -> None:
        """记录失败"""
        self.failure_count += 1
        self.last_failure_time = datetime.now()
        
        if self.failure_count >= self.failure_threshold:
            self.state = CircuitBreakerState.OPEN
    
    def can_execute(self) -> bool:
        """是否可以执行"""
        if self.state == CircuitBreakerState.CLOSED:
            return True
        
        if self.state == CircuitBreakerState.OPEN:
            if self.last_failure_time and \
               (datetime.now() - self.last_failure_time).total_seconds() > self.timeout:
                self.state = CircuitBreakerState.HALF_OPEN
                return True
            return False
        
        # HALF_OPEN状态
        return True


@dataclass
class ServiceMetrics:
    """服务指标"""
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    cache_hits: int = 0
    cache_misses: int = 0
    total_response_time: float = 0.0
    last_request_time: Optional[datetime] = None
    
    @property
    def success_rate(self) -> float:
        """成功率"""
        if self.total_requests == 0:
            return 0.0
        return self.successful_requests / self.total_requests
    
    @property
    def cache_hit_rate(self) -> float:
        """缓存命中率"""
        total_cache_requests = self.cache_hits + self.cache_misses
        if total_cache_requests == 0:
            return 0.0
        return self.cache_hits / total_cache_requests
    
    @property
    def average_response_time(self) -> float:
        """平均响应时间"""
        if self.successful_requests == 0:
            return 0.0
        return self.total_response_time / self.successful_requests


class DataAccessService:
    """统一数据访问服务"""
    
    def __init__(self, config: DataAccessConfig):
        """
        初始化数据访问服务
        
        Args:
            config: 数据访问配置
        """
        self.config = config
        self.status = ServiceStatus.INITIALIZING
        
        # 核心组件
        self.api_clients: Dict[str, JavaAPIClient] = {}
        self.database_manager = DatabaseManager()
        self.cache_manager: Optional[CacheManager] = None
        self.realtime_processor: Optional[RealtimeProcessor] = None
        self.data_validator: Optional[DataValidator] = None
        
        # 熔断器
        self.circuit_breakers: Dict[str, CircuitBreaker] = {}
        
        # 服务指标
        self.metrics = ServiceMetrics()
        
        # 线程锁
        self.lock = threading.RLock()
        
        # 健康检查任务
        self._health_check_task: Optional[asyncio.Task] = None
        
        logger.info("数据访问服务初始化开始")
    
    async def initialize(self) -> bool:
        """初始化服务"""
        try:
            self.status = ServiceStatus.INITIALIZING
            
            # 初始化缓存管理器
            await self._initialize_cache_manager()
            
            # 初始化数据验证器
            self._initialize_data_validator()
            
            # 初始化API客户端
            await self._initialize_api_clients()
            
            # 初始化数据库连接
            await self._initialize_databases()
            
            # 初始化实时数据处理器
            await self._initialize_realtime_processor()
            
            # 初始化熔断器
            self._initialize_circuit_breakers()
            
            # 启动健康检查
            self._start_health_check()
            
            self.status = ServiceStatus.RUNNING
            logger.info("数据访问服务初始化完成")
            return True
            
        except Exception as e:
            logger.error(f"数据访问服务初始化失败: {e}")
            self.status = ServiceStatus.ERROR
            return False
    
    async def _initialize_cache_manager(self) -> None:
        """初始化缓存管理器"""
        try:
            # 查找缓存配置
            cache_sources = self.config.get_sources_by_type(DataSourceType.CACHE)
            if cache_sources:
                cache_config = cache_sources[0].cache_config
                if cache_config:
                    self.cache_manager = CacheManager(cache_config)
                    logger.info("缓存管理器初始化成功")
            
            if not self.cache_manager:
                # 使用默认内存缓存
                from .data_config import DEFAULT_CACHE_CONFIG
                self.cache_manager = CacheManager(DEFAULT_CACHE_CONFIG)
                logger.info("使用默认缓存管理器")
                
        except Exception as e:
            logger.error(f"缓存管理器初始化失败: {e}")
    
    def _initialize_data_validator(self) -> None:
        """初始化数据验证器"""
        try:
            validator_config = {
                'quality_checker': {},
                'validation_level': ValidationLevel.STANDARD
            }
            self.data_validator = DataValidator(validator_config)
            logger.info("数据验证器初始化成功")
            
        except Exception as e:
            logger.error(f"数据验证器初始化失败: {e}")
    
    async def _initialize_api_clients(self) -> None:
        """初始化API客户端"""
        try:
            api_sources = self.config.get_sources_by_type(DataSourceType.JAVA_API)
            
            for source in api_sources:
                if source.api_config and source.enabled:
                    client = JavaAPIClient(source.api_config)
                    self.api_clients[source.name] = client
                    logger.info(f"API客户端初始化成功: {source.name}")
                    
        except Exception as e:
            logger.error(f"API客户端初始化失败: {e}")
    
    async def _initialize_databases(self) -> None:
        """初始化数据库连接"""
        try:
            db_sources = self.config.get_sources_by_type(DataSourceType.DATABASE)
            
            for source in db_sources:
                if source.database_config and source.enabled:
                    success = await self.database_manager.add_database(
                        source.name, source.database_config
                    )
                    if success:
                        logger.info(f"数据库连接初始化成功: {source.name}")
                    else:
                        logger.error(f"数据库连接初始化失败: {source.name}")
                        
        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
    
    async def _initialize_realtime_processor(self) -> None:
        """初始化实时数据处理器"""
        try:
            self.realtime_processor = RealtimeProcessor(self.cache_manager)
            
            # 添加Kafka处理器
            kafka_sources = self.config.get_sources_by_type(DataSourceType.KAFKA)
            for source in kafka_sources:
                if source.kafka_config and source.enabled:
                    success = await self.realtime_processor.add_kafka_processor(
                        source.name, source.kafka_config
                    )
                    if success:
                        logger.info(f"Kafka处理器添加成功: {source.name}")
            
            # 添加WebSocket处理器
            websocket_sources = self.config.get_sources_by_type(DataSourceType.WEBSOCKET)
            for source in websocket_sources:
                if source.enabled and 'url' in source.__dict__:
                    success = await self.realtime_processor.add_websocket_processor(
                        source.name, source.url
                    )
                    if success:
                        logger.info(f"WebSocket处理器添加成功: {source.name}")
            
            logger.info("实时数据处理器初始化成功")
            
        except Exception as e:
            logger.error(f"实时数据处理器初始化失败: {e}")
    
    def _initialize_circuit_breakers(self) -> None:
        """初始化熔断器"""
        try:
            if self.config.circuit_breaker_enabled:
                for source in self.config.data_sources:
                    if source.enabled:
                        self.circuit_breakers[source.name] = CircuitBreaker(
                            failure_threshold=self.config.circuit_breaker_threshold,
                            timeout=self.config.circuit_breaker_timeout
                        )
                
                logger.info("熔断器初始化成功")
                
        except Exception as e:
            logger.error(f"熔断器初始化失败: {e}")
    
    def _start_health_check(self) -> None:
        """启动健康检查"""
        try:
            if self.config.health_check_interval > 0:
                self._health_check_task = asyncio.create_task(
                    self._periodic_health_check()
                )
                logger.info("健康检查任务启动")
                
        except Exception as e:
            logger.error(f"启动健康检查失败: {e}")
    
    async def _periodic_health_check(self) -> None:
        """定期健康检查"""
        while self.status == ServiceStatus.RUNNING:
            try:
                await asyncio.sleep(self.config.health_check_interval)
                
                health_status = await self.health_check()
                unhealthy_count = sum(1 for status in health_status.values() if not status)
                
                if unhealthy_count > 0:
                    logger.warning(f"发现 {unhealthy_count} 个不健康的组件")
                    
                    # 如果超过一半的组件不健康，将服务状态设为降级
                    if unhealthy_count > len(health_status) / 2:
                        self.status = ServiceStatus.DEGRADED
                        logger.warning("服务状态降级")
                else:
                    if self.status == ServiceStatus.DEGRADED:
                        self.status = ServiceStatus.RUNNING
                        logger.info("服务状态恢复正常")
                        
            except Exception as e:
                logger.error(f"健康检查失败: {e}")
    
    async def get_historical_data(
        self,
        symbol: str,
        start_time: datetime,
        end_time: datetime,
        timeframe: str = "1h",
        source_preference: Optional[List[str]] = None,
        use_cache: bool = True,
        validate_data: bool = True
    ) -> Optional[pd.DataFrame]:
        """
        获取历史数据
        
        Args:
            symbol: 交易对符号
            start_time: 开始时间
            end_time: 结束时间
            timeframe: 时间框架
            source_preference: 数据源优先级列表
            use_cache: 是否使用缓存
            validate_data: 是否验证数据
            
        Returns:
            历史数据DataFrame
        """
        start_request_time = time.time()
        
        try:
            with self.lock:
                self.metrics.total_requests += 1
                self.metrics.last_request_time = datetime.now()
            
            # 生成缓存键
            cache_key = f"historical_{symbol}_{timeframe}_{start_time.isoformat()}_{end_time.isoformat()}"
            
            # 尝试从缓存获取
            if use_cache and self.cache_manager:
                cached_data = await self.cache_manager.get(cache_key)
                if cached_data is not None:
                    self.metrics.cache_hits += 1
                    logger.debug(f"从缓存获取历史数据: {symbol}")
                    
                    # 转换为DataFrame
                    if isinstance(cached_data, dict) and 'data' in cached_data:
                        df = pd.DataFrame(cached_data['data'])
                        if not df.empty and 'timestamp' in df.columns:
                            df['timestamp'] = pd.to_datetime(df['timestamp'])
                            df.set_index('timestamp', inplace=True)
                        return df
            
            self.metrics.cache_misses += 1
            
            # 确定数据源优先级
            sources = self._get_data_sources_by_priority(
                DataSourceType.JAVA_API, source_preference
            )
            
            # 尝试从各个数据源获取数据
            for source in sources:
                try:
                    # 检查熔断器
                    if not self._can_use_source(source.name):
                        continue
                    
                    data = await self._fetch_historical_data_from_source(
                        source, symbol, start_time, end_time, timeframe
                    )
                    
                    if data is not None and not data.empty:
                        # 记录成功
                        self._record_source_success(source.name)
                        
                        # 数据验证
                        if validate_data and self.data_validator:
                            validation_report = await self.data_validator.validate_data(
                                data, source.name, f"historical_{timeframe}"
                            )
                            
                            if validation_report.overall_result.value in ['failed', 'critical']:
                                logger.warning(f"数据验证失败: {source.name}")
                                continue
                        
                        # 缓存数据
                        if use_cache and self.cache_manager:
                            cache_data = {
                                'data': data.reset_index().to_dict('records'),
                                'metadata': {
                                    'source': source.name,
                                    'symbol': symbol,
                                    'timeframe': timeframe,
                                    'fetch_time': datetime.now().isoformat()
                                }
                            }
                            await self.cache_manager.put(cache_key, cache_data, ttl=3600)
                        
                        # 更新指标
                        response_time = time.time() - start_request_time
                        with self.lock:
                            self.metrics.successful_requests += 1
                            self.metrics.total_response_time += response_time
                        
                        logger.info(f"历史数据获取成功: {symbol} from {source.name}")
                        return data
                        
                except Exception as e:
                    logger.error(f"从数据源 {source.name} 获取历史数据失败: {e}")
                    self._record_source_failure(source.name)
                    continue
            
            # 所有数据源都失败
            with self.lock:
                self.metrics.failed_requests += 1
            
            logger.error(f"所有数据源获取历史数据失败: {symbol}")
            return None
            
        except Exception as e:
            logger.error(f"获取历史数据失败: {e}")
            with self.lock:
                self.metrics.failed_requests += 1
            return None
    
    async def _fetch_historical_data_from_source(
        self,
        source: DataSourceConfig,
        symbol: str,
        start_time: datetime,
        end_time: datetime,
        timeframe: str
    ) -> Optional[pd.DataFrame]:
        """从指定数据源获取历史数据"""
        if source.source_type == DataSourceType.JAVA_API:
            client = self.api_clients.get(source.name)
            if client:
                response = await client.get_historical_data(
                    symbol, start_time, end_time, timeframe
                )
                if response.success and isinstance(response.data, pd.DataFrame):
                    return response.data
        
        elif source.source_type == DataSourceType.DATABASE:
            connector = self.database_manager.get_connector(source.name)
            if connector:
                query = f"""
                SELECT * FROM market_data 
                WHERE symbol = '{symbol}' 
                AND timeframe = '{timeframe}'
                AND timestamp BETWEEN '{start_time}' AND '{end_time}'
                ORDER BY timestamp
                """
                return await connector.execute_dataframe_query(query)
        
        return None
    
    async def get_realtime_data(self, symbol: str) -> Optional[Dict[str, Any]]:
        """
        获取实时数据
        
        Args:
            symbol: 交易对符号
            
        Returns:
            实时数据
        """
        try:
            # 尝试从缓存获取最新数据
            if self.cache_manager:
                cache_key = f"realtime_{symbol}"
                cached_data = await self.cache_manager.get(cache_key)
                if cached_data:
                    return cached_data
            
            # 从API获取实时数据
            sources = self._get_data_sources_by_priority(DataSourceType.JAVA_API)
            
            for source in sources:
                try:
                    if not self._can_use_source(source.name):
                        continue
                    
                    client = self.api_clients.get(source.name)
                    if client:
                        response = await client.get_realtime_data(symbol)
                        if response.success:
                            self._record_source_success(source.name)
                            
                            # 缓存数据
                            if self.cache_manager:
                                await self.cache_manager.put(
                                    f"realtime_{symbol}", response.data, ttl=60
                                )
                            
                            return response.data
                            
                except Exception as e:
                    logger.error(f"从 {source.name} 获取实时数据失败: {e}")
                    self._record_source_failure(source.name)
                    continue
            
            return None
            
        except Exception as e:
            logger.error(f"获取实时数据失败: {e}")
            return None
    
    async def send_trading_signal(self, signal_data: Dict[str, Any]) -> bool:
        """
        发送交易信号
        
        Args:
            signal_data: 信号数据
            
        Returns:
            是否发送成功
        """
        try:
            # 验证信号数据
            if self.data_validator:
                # 这里可以添加信号数据验证逻辑
                pass
            
            # 发送到API
            sources = self._get_data_sources_by_priority(DataSourceType.JAVA_API)
            
            for source in sources:
                try:
                    if not self._can_use_source(source.name):
                        continue
                    
                    client = self.api_clients.get(source.name)
                    if client:
                        response = await client.send_trading_signal(signal_data)
                        if response.success:
                            self._record_source_success(source.name)
                            logger.info(f"交易信号发送成功: {source.name}")
                            return True
                            
                except Exception as e:
                    logger.error(f"向 {source.name} 发送交易信号失败: {e}")
                    self._record_source_failure(source.name)
                    continue
            
            # 发送到Kafka
            if self.realtime_processor:
                kafka_sources = self._get_data_sources_by_priority(DataSourceType.KAFKA)
                for source in kafka_sources:
                    try:
                        processor = self.realtime_processor.kafka_processors.get(source.name)
                        if processor:
                            success = await processor.send_message(
                                "trading_signals", signal_data
                            )
                            if success:
                                logger.info(f"交易信号发送到Kafka成功: {source.name}")
                                return True
                    except Exception as e:
                        logger.error(f"发送信号到Kafka失败: {e}")
            
            return False
            
        except Exception as e:
            logger.error(f"发送交易信号失败: {e}")
            return False
    
    def _get_data_sources_by_priority(
        self,
        source_type: DataSourceType,
        preference: Optional[List[str]] = None
    ) -> List[DataSourceConfig]:
        """根据优先级获取数据源"""
        sources = self.config.get_sources_by_type(source_type)
        enabled_sources = [s for s in sources if s.enabled]
        
        if preference:
            # 按照偏好排序
            preferred_sources = []
            for name in preference:
                for source in enabled_sources:
                    if source.name == name:
                        preferred_sources.append(source)
                        break
            
            # 添加剩余的源
            remaining_sources = [s for s in enabled_sources if s not in preferred_sources]
            remaining_sources.sort(key=lambda x: x.priority)
            
            return preferred_sources + remaining_sources
        else:
            # 按照配置的优先级排序
            enabled_sources.sort(key=lambda x: x.priority)
            return enabled_sources
    
    def _can_use_source(self, source_name: str) -> bool:
        """检查是否可以使用数据源"""
        if not self.config.circuit_breaker_enabled:
            return True
        
        breaker = self.circuit_breakers.get(source_name)
        if breaker:
            return breaker.can_execute()
        
        return True
    
    def _record_source_success(self, source_name: str) -> None:
        """记录数据源成功"""
        if self.config.circuit_breaker_enabled:
            breaker = self.circuit_breakers.get(source_name)
            if breaker:
                breaker.record_success()
    
    def _record_source_failure(self, source_name: str) -> None:
        """记录数据源失败"""
        if self.config.circuit_breaker_enabled:
            breaker = self.circuit_breakers.get(source_name)
            if breaker:
                breaker.record_failure()
    
    async def health_check(self) -> Dict[str, bool]:
        """健康检查"""
        health_status = {}
        
        try:
            # 检查API客户端
            for name, client in self.api_clients.items():
                try:
                    response = await client.health_check()
                    health_status[f"api_{name}"] = response.success
                except:
                    health_status[f"api_{name}"] = False
            
            # 检查数据库连接
            db_health = await self.database_manager.health_check_all()
            for name, status in db_health.items():
                health_status[f"db_{name}"] = status
            
            # 检查缓存管理器
            if self.cache_manager:
                cache_health = await self.cache_manager.health_check()
                health_status["cache"] = all(cache_health.values())
            
            # 检查实时数据处理器
            if self.realtime_processor:
                realtime_health = await self.realtime_processor.health_check()
                for name, status in realtime_health.items():
                    health_status[f"realtime_{name}"] = status
            
            return health_status
            
        except Exception as e:
            logger.error(f"健康检查失败: {e}")
            return {"error": False}
    
    def get_service_statistics(self) -> Dict[str, Any]:
        """获取服务统计信息"""
        try:
            stats = {
                'service_status': self.status.value,
                'service_metrics': {
                    'total_requests': self.metrics.total_requests,
                    'successful_requests': self.metrics.successful_requests,
                    'failed_requests': self.metrics.failed_requests,
                    'success_rate': self.metrics.success_rate,
                    'cache_hits': self.metrics.cache_hits,
                    'cache_misses': self.metrics.cache_misses,
                    'cache_hit_rate': self.metrics.cache_hit_rate,
                    'average_response_time': self.metrics.average_response_time,
                    'last_request_time': self.metrics.last_request_time.isoformat() if self.metrics.last_request_time else None
                },
                'circuit_breakers': {},
                'components': {}
            }
            
            # 熔断器状态
            for name, breaker in self.circuit_breakers.items():
                stats['circuit_breakers'][name] = {
                    'state': breaker.state.value,
                    'failure_count': breaker.failure_count,
                    'last_failure_time': breaker.last_failure_time.isoformat() if breaker.last_failure_time else None
                }
            
            # 组件统计
            if self.cache_manager:
                stats['components']['cache'] = self.cache_manager.get_statistics()
            
            if self.realtime_processor:
                stats['components']['realtime'] = self.realtime_processor.get_statistics()
            
            stats['components']['database'] = self.database_manager.get_all_statistics()
            
            if self.data_validator:
                stats['components']['validator'] = self.data_validator.get_validation_statistics()
            
            return stats
            
        except Exception as e:
            logger.error(f"获取服务统计失败: {e}")
            return {'error': str(e)}
    
    async def close(self) -> None:
        """关闭服务"""
        try:
            self.status = ServiceStatus.STOPPED
            
            # 停止健康检查
            if self._health_check_task:
                self._health_check_task.cancel()
            
            # 关闭API客户端
            for client in self.api_clients.values():
                await client.close()
            
            # 关闭数据库连接
            await self.database_manager.close_all()
            
            # 关闭缓存管理器
            if self.cache_manager:
                await self.cache_manager.close()
            
            # 关闭实时数据处理器
            if self.realtime_processor:
                await self.realtime_processor.close_all()
            
            logger.info("数据访问服务已关闭")
            
        except Exception as e:
            logger.error(f"关闭数据访问服务失败: {e}")
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.initialize()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.close()