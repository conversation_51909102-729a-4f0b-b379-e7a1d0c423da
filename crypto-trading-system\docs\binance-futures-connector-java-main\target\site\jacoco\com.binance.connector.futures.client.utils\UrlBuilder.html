<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>UrlBuilder</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">io.github.binance:binance-futures-connector-java</a> &gt; <a href="index.html" class="el_package">com.binance.connector.futures.client.utils</a> &gt; <span class="el_class">UrlBuilder</span></div><h1>UrlBuilder</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">65 of 249</td><td class="ctr2">73%</td><td class="bar">8 of 30</td><td class="ctr2">73%</td><td class="ctr1">6</td><td class="ctr2">24</td><td class="ctr1">17</td><td class="ctr2">60</td><td class="ctr1">1</td><td class="ctr2">9</td></tr></tfoot><tbody><tr><td id="a4"><a href="UrlBuilder.java.html#L81" class="el_method">joinArrayListParameters(String, StringBuilder, ArrayList, boolean)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="42" height="10" title="30" alt="30"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="34" height="10" title="4" alt="4"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f0">3</td><td class="ctr2" id="g2">3</td><td class="ctr1" id="h0">9</td><td class="ctr2" id="i1">9</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a6"><a href="UrlBuilder.java.html#L48" class="el_method">joinQueryParameters(StringBuilder, LinkedHashMap)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="28" height="10" title="20" alt="20"/><img src="../jacoco-resources/greenbar.gif" width="91" height="10" title="64" alt="64"/></td><td class="ctr2" id="c6">76%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="34" height="10" title="4" alt="4"/><img src="../jacoco-resources/greenbar.gif" width="85" height="10" title="10" alt="10"/></td><td class="ctr2" id="e3">71%</td><td class="ctr1" id="f1">3</td><td class="ctr2" id="g0">8</td><td class="ctr1" id="h1">6</td><td class="ctr2" id="i0">21</td><td class="ctr1" id="j1">0</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a8"><a href="UrlBuilder.java.html#L110" class="el_method">urlEncode(String)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="21" height="10" title="15" alt="15"/><img src="../jacoco-resources/greenbar.gif" width="7" height="10" title="5" alt="5"/></td><td class="ctr2" id="c7">25%</td><td class="bar" id="d5"/><td class="ctr2" id="e5">n/a</td><td class="ctr1" id="f2">0</td><td class="ctr2" id="g5">1</td><td class="ctr1" id="h2">2</td><td class="ctr2" id="i5">3</td><td class="ctr1" id="j2">0</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a0"><a href="UrlBuilder.java.html#L23" class="el_method">buildFullUrl(String, String, LinkedHashMap, String)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/greenbar.gif" width="58" height="10" title="41" alt="41"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d2"><img src="../jacoco-resources/greenbar.gif" width="51" height="10" title="6" alt="6"/></td><td class="ctr2" id="e0">100%</td><td class="ctr1" id="f3">0</td><td class="ctr2" id="g1">4</td><td class="ctr1" id="h3">0</td><td class="ctr2" id="i2">8</td><td class="ctr1" id="j3">0</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a7"><a href="UrlBuilder.java.html#L95" class="el_method">joinStreamUrls(StringBuilder, ArrayList)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/greenbar.gif" width="41" height="10" title="29" alt="29"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d3"><img src="../jacoco-resources/greenbar.gif" width="34" height="10" title="4" alt="4"/></td><td class="ctr2" id="e1">100%</td><td class="ctr1" id="f4">0</td><td class="ctr2" id="g3">3</td><td class="ctr1" id="h4">0</td><td class="ctr2" id="i3">8</td><td class="ctr1" id="j4">0</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a3"><a href="UrlBuilder.java.html#L119" class="el_method">getFormatter()</a></td><td class="bar" id="b5"><img src="../jacoco-resources/greenbar.gif" width="31" height="10" title="22" alt="22"/></td><td class="ctr2" id="c2">100%</td><td class="bar" id="d4"><img src="../jacoco-resources/greenbar.gif" width="17" height="10" title="2" alt="2"/></td><td class="ctr2" id="e2">100%</td><td class="ctr1" id="f5">0</td><td class="ctr2" id="g4">2</td><td class="ctr1" id="h5">0</td><td class="ctr2" id="i4">6</td><td class="ctr1" id="j5">0</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a1"><a href="UrlBuilder.java.html#L37" class="el_method">buildStreamUrl(String, ArrayList)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/greenbar.gif" width="18" height="10" title="13" alt="13"/></td><td class="ctr2" id="c3">100%</td><td class="bar" id="d6"/><td class="ctr2" id="e6">n/a</td><td class="ctr1" id="f6">0</td><td class="ctr2" id="g6">1</td><td class="ctr1" id="h6">0</td><td class="ctr2" id="i6">3</td><td class="ctr1" id="j6">0</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a5"><a href="UrlBuilder.java.html#L44" class="el_method">joinQueryParameters(LinkedHashMap)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/greenbar.gif" width="10" height="10" title="7" alt="7"/></td><td class="ctr2" id="c4">100%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f7">0</td><td class="ctr2" id="g7">1</td><td class="ctr1" id="h7">0</td><td class="ctr2" id="i7">1</td><td class="ctr1" id="j7">0</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a2"><a href="UrlBuilder.java.html#L130" class="el_method">buildTimestamp()</a></td><td class="bar" id="b8"><img src="../jacoco-resources/greenbar.gif" width="4" height="10" title="3" alt="3"/></td><td class="ctr2" id="c5">100%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f8">0</td><td class="ctr2" id="g8">1</td><td class="ctr1" id="h8">0</td><td class="ctr2" id="i8">1</td><td class="ctr1" id="j8">0</td><td class="ctr2" id="k8">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.7.202105040129</span></div></body></html>