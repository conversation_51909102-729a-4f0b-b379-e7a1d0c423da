package com.crypto.trading.sdk.response.model;

/**
 * API响应基类
 * 定义通用的响应属性
 */
public class ApiResponse<T> {

    /**
     * 响应代码
     */
    private int code;

    /**
     * 响应消息
     */
    private String msg;

    /**
     * 响应数据
     */
    private T data;

    /**
     * 默认构造函数
     */
    public ApiResponse() {
    }

    /**
     * 构造函数
     *
     * @param code 响应代码
     * @param msg  响应消息
     * @param data 响应数据
     */
    public ApiResponse(int code, String msg, T data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

    /**
     * 获取响应代码
     *
     * @return 响应代码
     */
    public int getCode() {
        return code;
    }

    /**
     * 设置响应代码
     *
     * @param code 响应代码
     */
    public void setCode(int code) {
        this.code = code;
    }

    /**
     * 获取响应消息
     *
     * @return 响应消息
     */
    public String getMsg() {
        return msg;
    }

    /**
     * 设置响应消息
     *
     * @param msg 响应消息
     */
    public void setMsg(String msg) {
        this.msg = msg;
    }

    /**
     * 获取响应数据
     *
     * @return 响应数据
     */
    public T getData() {
        return data;
    }

    /**
     * 设置响应数据
     *
     * @param data 响应数据
     */
    public void setData(T data) {
        this.data = data;
    }

    /**
     * 判断响应是否成功
     *
     * @return 是否成功
     */
    public boolean isSuccess() {
        return code == 0 || code == 200 || code == 201;
    }

    @Override
    public String toString() {
        return "ApiResponse{" +
                "code=" + code +
                ", msg='" + msg + '\'' +
                ", data=" + data +
                '}';
    }
} 