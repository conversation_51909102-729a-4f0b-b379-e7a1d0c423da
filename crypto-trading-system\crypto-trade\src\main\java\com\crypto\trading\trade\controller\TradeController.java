package com.crypto.trading.trade.controller;

import com.crypto.trading.common.dto.trade.OrderDTO;
import com.crypto.trading.common.dto.trade.OrderRequestDTO;
import com.crypto.trading.common.dto.trade.OrderResponseDTO;
import com.crypto.trading.common.dto.trade.OrderStatusDTO;
import com.crypto.trading.common.exception.BusinessException;
import com.crypto.trading.trade.service.TradeExecutionService;
import com.crypto.trading.trade.service.order.OrderConverter;
import com.crypto.trading.trade.service.order.OrderManagementService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * 交易控制器
 * <p>
 * 提供交易相关的REST API接口
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@RestController
@RequestMapping("/api/v1/trade")
public class TradeController {

    private static final Logger log = LoggerFactory.getLogger(TradeController.class);

    /**
     * 交易执行服务
     */
    @Autowired
    private TradeExecutionService tradeExecutionService;

    /**
     * 订单管理服务
     */
    @Autowired
    private OrderManagementService orderManagementService;

    /**
     * 创建市价单
     *
     * @param request 订单请求DTO
     * @return 订单响应DTO
     */
    @PostMapping("/orders/market")
    public ResponseEntity<OrderResponseDTO> createMarketOrder(@RequestBody OrderRequestDTO request) {
        log.info("创建市价单请求: {}", request);

        try {
            // 创建订单
            OrderDTO orderDTO = orderManagementService.createOrder(request);

            // 执行订单
            OrderDTO executedOrder = tradeExecutionService.executeMarketOrder(orderDTO)
                    .orTimeout(10, TimeUnit.SECONDS)
                    .join();

            // 转换为响应DTO
            OrderResponseDTO response = OrderConverter.toOrderResponseDTO(executedOrder);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("创建市价单失败: {}", e.getMessage(), e);
            throw new BusinessException("创建市价单失败: " + e.getMessage());
        }
    }

    /**
     * 查询订单状态
     *
     * @param symbol        交易对
     * @param orderId       订单ID
     * @param clientOrderId 客户端订单ID
     * @return 订单响应DTO
     */
    @GetMapping("/orders")
    public ResponseEntity<OrderResponseDTO> queryOrder(
            @RequestParam String symbol,
            @RequestParam(required = false) String orderId,
            @RequestParam(required = false) String clientOrderId) {
        log.info("查询订单状态请求: symbol={}, orderId={}, clientOrderId={}", symbol, orderId, clientOrderId);

        try {
            // 查询订单状态
            OrderDTO orderDTO = tradeExecutionService.queryOrderStatus(symbol, orderId, clientOrderId)
                    .orTimeout(5, TimeUnit.SECONDS)
                    .join();

            // 转换为响应DTO
            OrderResponseDTO response = OrderConverter.toOrderResponseDTO(orderDTO);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("查询订单状态失败: {}", e.getMessage(), e);
            throw new BusinessException("查询订单状态失败: " + e.getMessage());
        }
    }

    /**
     * 取消订单
     *
     * @param orderId       订单ID
     * @param clientOrderId 客户端订单ID
     * @return 订单响应DTO
     */
    @DeleteMapping("/orders")
    public ResponseEntity<OrderResponseDTO> cancelOrder(
            @RequestParam(required = false) String orderId,
            @RequestParam(required = false) String clientOrderId) {
        log.info("取消订单请求: orderId={}, clientOrderId={}", orderId, clientOrderId);

        try {
            // 取消订单
            OrderDTO canceledOrder = tradeExecutionService.cancelOrder(orderId, clientOrderId)
                    .orTimeout(5, TimeUnit.SECONDS)
                    .join();

            // 转换为响应DTO
            OrderResponseDTO response = OrderConverter.toOrderResponseDTO(canceledOrder);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("取消订单失败: {}", e.getMessage(), e);
            throw new BusinessException("取消订单失败: " + e.getMessage());
        }
    }

    /**
     * 批量查询订单
     *
     * @param symbol 交易对
     * @param status 订单状态
     * @param limit  返回数量限制
     * @return 订单响应DTO列表
     */
    @GetMapping("/orders/batch")
    public ResponseEntity<?> batchQueryOrders(
            @RequestParam(required = false) String symbol,
            @RequestParam(required = false) String status,
            @RequestParam(defaultValue = "10") int limit) {
        log.info("批量查询订单请求: symbol={}, status={}, limit={}", symbol, status, limit);

        try {
            // 批量查询订单
            return ResponseEntity.ok(orderManagementService.findOrders(symbol, status, limit));
        } catch (Exception e) {
            log.error("批量查询订单失败: {}", e.getMessage(), e);
            throw new BusinessException("批量查询订单失败: " + e.getMessage());
        }
    }
}