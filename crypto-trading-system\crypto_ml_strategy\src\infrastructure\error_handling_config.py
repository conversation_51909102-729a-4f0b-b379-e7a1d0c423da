"""
错误处理配置管理模块

提供crypto_ml_strategy项目的错误处理配置管理功能，包括配置加载、
验证、热更新和配置模板生成。

Author: Crypto ML Strategy Team
Date: 2024-12-20
Version: 1.0.0
"""

import json
import yaml
from dataclasses import dataclass, field, asdict
from datetime import timedelta
from pathlib import Path
from typing import Any, Dict, List, Optional, Union
from loguru import logger

from .error_handler_core import ErrorSeverity, ErrorCategory, ErrorCode
from .circuit_breaker import CircuitBreakerConfig
from .error_recovery import RetryPolicy, RetryStrategy
from .graceful_degradation import DegradationLevel


@dataclass
class AlertConfig:
    """告警配置"""
    enabled: bool = True
    email_enabled: bool = False
    email_recipients: List[str] = field(default_factory=list)
    webhook_enabled: bool = False
    webhook_url: str = ""
    slack_enabled: bool = False
    slack_webhook: str = ""
    suppression_window_minutes: int = 5
    max_alerts_per_hour: int = 100


@dataclass
class HealthCheckConfig:
    """健康检查配置"""
    enabled: bool = True
    interval_seconds: int = 30
    timeout_seconds: int = 10
    failure_threshold: int = 3
    success_threshold: int = 2
    cpu_threshold: float = 80.0
    memory_threshold: float = 85.0
    disk_threshold: float = 90.0


@dataclass
class RetryConfig:
    """重试配置"""
    enabled: bool = True
    max_attempts: int = 3
    base_delay_seconds: float = 1.0
    max_delay_seconds: float = 60.0
    strategy: str = "exponential_backoff"
    jitter_enabled: bool = True
    backoff_multiplier: float = 2.0
    retryable_error_codes: List[int] = field(default_factory=lambda: [1001, 1003, 3001, 3002, 5001])


@dataclass
class CircuitBreakerConfigData:
    """熔断器配置"""
    enabled: bool = True
    failure_threshold: int = 5
    recovery_timeout_seconds: int = 60
    success_threshold: int = 3
    timeout_seconds: float = 30.0
    monitor_window_seconds: int = 300


@dataclass
class DegradationConfig:
    """降级配置"""
    enabled: bool = True
    monitor_interval_seconds: int = 30
    auto_recovery_enabled: bool = True
    performance_thresholds: Dict[str, float] = field(default_factory=lambda: {
        "response_time_ms": 1000.0,
        "error_rate": 0.1,
        "cpu_usage": 80.0,
        "memory_usage": 85.0
    })
    load_thresholds: Dict[str, float] = field(default_factory=lambda: {
        "request_rate": 1000.0,
        "queue_size": 1000,
        "connection_count": 500
    })


@dataclass
class FailoverConfig:
    """故障转移配置"""
    enabled: bool = True
    monitor_interval_seconds: int = 30
    health_check_timeout_seconds: int = 10
    failover_cooldown_seconds: int = 60
    strategy: str = "priority"  # priority, round_robin, weighted
    auto_recovery_enabled: bool = True


@dataclass
class LoggingConfig:
    """日志配置"""
    level: str = "INFO"
    format: str = "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
    rotation: str = "1 day"
    retention: str = "30 days"
    compression: str = "gz"
    error_file: str = "logs/error_handling.log"
    performance_file: str = "logs/performance.log"


@dataclass
class ErrorHandlingConfig:
    """错误处理总配置"""
    version: str = "1.0.0"
    enabled: bool = True
    alert: AlertConfig = field(default_factory=AlertConfig)
    health_check: HealthCheckConfig = field(default_factory=HealthCheckConfig)
    retry: RetryConfig = field(default_factory=RetryConfig)
    circuit_breaker: CircuitBreakerConfigData = field(default_factory=CircuitBreakerConfigData)
    degradation: DegradationConfig = field(default_factory=DegradationConfig)
    failover: FailoverConfig = field(default_factory=FailoverConfig)
    logging: LoggingConfig = field(default_factory=LoggingConfig)
    custom_settings: Dict[str, Any] = field(default_factory=dict)


class ConfigValidator:
    """配置验证器"""
    
    @staticmethod
    def validate_config(config: ErrorHandlingConfig) -> List[str]:
        """验证配置"""
        errors = []
        
        # 验证告警配置
        if config.alert.enabled:
            if config.alert.email_enabled and not config.alert.email_recipients:
                errors.append("启用邮件告警但未配置收件人")
            
            if config.alert.webhook_enabled and not config.alert.webhook_url:
                errors.append("启用Webhook告警但未配置URL")
            
            if config.alert.slack_enabled and not config.alert.slack_webhook:
                errors.append("启用Slack告警但未配置Webhook")
        
        # 验证健康检查配置
        if config.health_check.enabled:
            if config.health_check.interval_seconds <= 0:
                errors.append("健康检查间隔必须大于0")
            
            if config.health_check.timeout_seconds <= 0:
                errors.append("健康检查超时时间必须大于0")
            
            if config.health_check.failure_threshold <= 0:
                errors.append("健康检查失败阈值必须大于0")
        
        # 验证重试配置
        if config.retry.enabled:
            if config.retry.max_attempts <= 0:
                errors.append("重试最大次数必须大于0")
            
            if config.retry.base_delay_seconds <= 0:
                errors.append("重试基础延迟必须大于0")
            
            if config.retry.strategy not in ["fixed_delay", "exponential_backoff", "linear_backoff", "random_jitter"]:
                errors.append(f"不支持的重试策略: {config.retry.strategy}")
        
        # 验证熔断器配置
        if config.circuit_breaker.enabled:
            if config.circuit_breaker.failure_threshold <= 0:
                errors.append("熔断器失败阈值必须大于0")
            
            if config.circuit_breaker.recovery_timeout_seconds <= 0:
                errors.append("熔断器恢复超时时间必须大于0")
        
        # 验证降级配置
        if config.degradation.enabled:
            if config.degradation.monitor_interval_seconds <= 0:
                errors.append("降级监控间隔必须大于0")
            
            for key, value in config.degradation.performance_thresholds.items():
                if value <= 0:
                    errors.append(f"性能阈值 {key} 必须大于0")
        
        # 验证故障转移配置
        if config.failover.enabled:
            if config.failover.monitor_interval_seconds <= 0:
                errors.append("故障转移监控间隔必须大于0")
            
            if config.failover.strategy not in ["priority", "round_robin", "weighted"]:
                errors.append(f"不支持的故障转移策略: {config.failover.strategy}")
        
        return errors


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: Optional[str] = None):
        self.config_file = config_file or "config/error_handling.yaml"
        self.config: Optional[ErrorHandlingConfig] = None
        self.config_watchers: List[callable] = []
        self._last_modified: Optional[float] = None
    
    def load_config(self, config_file: Optional[str] = None) -> ErrorHandlingConfig:
        """加载配置"""
        file_path = config_file or self.config_file
        
        try:
            path = Path(file_path)
            
            if not path.exists():
                logger.warning(f"配置文件不存在: {file_path}，使用默认配置")
                self.config = ErrorHandlingConfig()
                self._create_default_config_file(path)
                return self.config
            
            # 读取配置文件
            with open(path, 'r', encoding='utf-8') as f:
                if path.suffix.lower() in ['.yaml', '.yml']:
                    config_data = yaml.safe_load(f)
                elif path.suffix.lower() == '.json':
                    config_data = json.load(f)
                else:
                    raise ValueError(f"不支持的配置文件格式: {path.suffix}")
            
            # 转换为配置对象
            self.config = self._dict_to_config(config_data)
            
            # 验证配置
            errors = ConfigValidator.validate_config(self.config)
            if errors:
                logger.error(f"配置验证失败: {errors}")
                raise ValueError(f"配置验证失败: {'; '.join(errors)}")
            
            # 记录文件修改时间
            self._last_modified = path.stat().st_mtime
            
            logger.info(f"成功加载配置文件: {file_path}")
            return self.config
            
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            # 使用默认配置
            self.config = ErrorHandlingConfig()
            return self.config
    
    def save_config(self, config: Optional[ErrorHandlingConfig] = None, file_path: Optional[str] = None) -> bool:
        """保存配置"""
        try:
            config_to_save = config or self.config
            if not config_to_save:
                logger.error("没有配置可保存")
                return False
            
            file_path = file_path or self.config_file
            path = Path(file_path)
            
            # 确保目录存在
            path.parent.mkdir(parents=True, exist_ok=True)
            
            # 转换为字典
            config_dict = asdict(config_to_save)
            
            # 保存文件
            with open(path, 'w', encoding='utf-8') as f:
                if path.suffix.lower() in ['.yaml', '.yml']:
                    yaml.dump(config_dict, f, default_flow_style=False, allow_unicode=True)
                elif path.suffix.lower() == '.json':
                    json.dump(config_dict, f, indent=2, ensure_ascii=False)
                else:
                    raise ValueError(f"不支持的配置文件格式: {path.suffix}")
            
            logger.info(f"配置已保存到: {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"保存配置失败: {e}")
            return False
    
    def _dict_to_config(self, config_data: Dict[str, Any]) -> ErrorHandlingConfig:
        """将字典转换为配置对象"""
        # 处理嵌套配置
        alert_data = config_data.get('alert', {})
        health_check_data = config_data.get('health_check', {})
        retry_data = config_data.get('retry', {})
        circuit_breaker_data = config_data.get('circuit_breaker', {})
        degradation_data = config_data.get('degradation', {})
        failover_data = config_data.get('failover', {})
        logging_data = config_data.get('logging', {})
        
        return ErrorHandlingConfig(
            version=config_data.get('version', '1.0.0'),
            enabled=config_data.get('enabled', True),
            alert=AlertConfig(**alert_data),
            health_check=HealthCheckConfig(**health_check_data),
            retry=RetryConfig(**retry_data),
            circuit_breaker=CircuitBreakerConfigData(**circuit_breaker_data),
            degradation=DegradationConfig(**degradation_data),
            failover=FailoverConfig(**failover_data),
            logging=LoggingConfig(**logging_data),
            custom_settings=config_data.get('custom_settings', {})
        )
    
    def _create_default_config_file(self, path: Path) -> None:
        """创建默认配置文件"""
        try:
            # 确保目录存在
            path.parent.mkdir(parents=True, exist_ok=True)
            
            # 创建默认配置
            default_config = ErrorHandlingConfig()
            
            # 保存默认配置
            self.save_config(default_config, str(path))
            
            logger.info(f"已创建默认配置文件: {path}")
            
        except Exception as e:
            logger.error(f"创建默认配置文件失败: {e}")
    
    def watch_config_changes(self, callback: callable) -> None:
        """监听配置文件变化"""
        self.config_watchers.append(callback)
    
    def check_config_changes(self) -> bool:
        """检查配置文件是否有变化"""
        try:
            path = Path(self.config_file)
            if not path.exists():
                return False
            
            current_modified = path.stat().st_mtime
            
            if self._last_modified and current_modified > self._last_modified:
                logger.info("检测到配置文件变化，重新加载配置")
                old_config = self.config
                new_config = self.load_config()
                
                # 通知监听器
                for callback in self.config_watchers:
                    try:
                        callback(old_config, new_config)
                    except Exception as e:
                        logger.error(f"配置变化回调执行失败: {e}")
                
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"检查配置文件变化失败: {e}")
            return False
    
    def get_config(self) -> ErrorHandlingConfig:
        """获取当前配置"""
        if not self.config:
            return self.load_config()
        return self.config
    
    def update_config(self, updates: Dict[str, Any]) -> bool:
        """更新配置"""
        try:
            if not self.config:
                self.load_config()
            
            # 应用更新
            config_dict = asdict(self.config)
            self._deep_update(config_dict, updates)
            
            # 重新创建配置对象
            self.config = self._dict_to_config(config_dict)
            
            # 验证配置
            errors = ConfigValidator.validate_config(self.config)
            if errors:
                logger.error(f"配置更新验证失败: {errors}")
                return False
            
            # 保存配置
            return self.save_config()
            
        except Exception as e:
            logger.error(f"更新配置失败: {e}")
            return False
    
    def _deep_update(self, base_dict: Dict[str, Any], update_dict: Dict[str, Any]) -> None:
        """深度更新字典"""
        for key, value in update_dict.items():
            if key in base_dict and isinstance(base_dict[key], dict) and isinstance(value, dict):
                self._deep_update(base_dict[key], value)
            else:
                base_dict[key] = value
    
    def to_circuit_breaker_config(self) -> CircuitBreakerConfig:
        """转换为熔断器配置"""
        cb_config = self.get_config().circuit_breaker
        return CircuitBreakerConfig(
            failure_threshold=cb_config.failure_threshold,
            recovery_timeout=cb_config.recovery_timeout_seconds,
            success_threshold=cb_config.success_threshold,
            timeout=cb_config.timeout_seconds,
            monitor_window=cb_config.monitor_window_seconds
        )
    
    def to_retry_policy(self) -> RetryPolicy:
        """转换为重试策略"""
        retry_config = self.get_config().retry
        
        # 转换重试策略枚举
        strategy_map = {
            "fixed_delay": RetryStrategy.FIXED_DELAY,
            "exponential_backoff": RetryStrategy.EXPONENTIAL_BACKOFF,
            "linear_backoff": RetryStrategy.LINEAR_BACKOFF,
            "random_jitter": RetryStrategy.RANDOM_JITTER
        }
        
        return RetryPolicy(
            max_attempts=retry_config.max_attempts,
            base_delay=retry_config.base_delay_seconds,
            max_delay=retry_config.max_delay_seconds,
            strategy=strategy_map.get(retry_config.strategy, RetryStrategy.EXPONENTIAL_BACKOFF),
            jitter=retry_config.jitter_enabled,
            backoff_multiplier=retry_config.backoff_multiplier
        )
    
    def export_config_template(self, file_path: str) -> bool:
        """导出配置模板"""
        try:
            template_config = ErrorHandlingConfig()
            
            # 添加注释信息
            config_dict = asdict(template_config)
            config_dict['_comments'] = {
                'version': '配置文件版本',
                'enabled': '是否启用错误处理',
                'alert': '告警配置',
                'health_check': '健康检查配置',
                'retry': '重试配置',
                'circuit_breaker': '熔断器配置',
                'degradation': '降级配置',
                'failover': '故障转移配置',
                'logging': '日志配置',
                'custom_settings': '自定义设置'
            }
            
            path = Path(file_path)
            path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(path, 'w', encoding='utf-8') as f:
                yaml.dump(config_dict, f, default_flow_style=False, allow_unicode=True)
            
            logger.info(f"配置模板已导出到: {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"导出配置模板失败: {e}")
            return False


# 全局配置管理器
global_config_manager = ConfigManager()