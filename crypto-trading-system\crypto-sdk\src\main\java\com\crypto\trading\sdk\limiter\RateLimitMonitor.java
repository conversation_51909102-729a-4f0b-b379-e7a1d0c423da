package com.crypto.trading.sdk.limiter;

import com.crypto.trading.common.constant.APILimitConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Map;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 限流监控器
 * <p>
 * 监控API请求频率和限流情况，提供统计数据和报警功能
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
public class RateLimitMonitor {

    private static final Logger log = LoggerFactory.getLogger(RateLimitMonitor.class);
    
    /**
     * 监控间隔（毫秒）
     */
    private static final long MONITOR_INTERVAL_MS = 60000; // 1分钟
    
    /**
     * 端点请求计数器
     */
    private final Map<String, AtomicInteger> endpointRequestCounter = new ConcurrentHashMap<>();
    
    /**
     * 端点请求权重计数器
     */
    private final Map<String, AtomicInteger> endpointWeightCounter = new ConcurrentHashMap<>();
    
    /**
     * 端点请求延迟总和
     */
    private final Map<String, AtomicLong> endpointLatencySum = new ConcurrentHashMap<>();
    
    /**
     * 端点请求延迟计数
     */
    private final Map<String, AtomicInteger> endpointLatencyCount = new ConcurrentHashMap<>();
    
    /**
     * 限流次数计数器
     */
    private final AtomicInteger rateLimitCount = new AtomicInteger(0);
    
    /**
     * 错误次数计数器
     */
    private final AtomicInteger errorCount = new AtomicInteger(0);
    
    /**
     * 限流器
     */
    private final RateLimiter rateLimiter;
    
    /**
     * 定时执行器
     */
    private final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    
    /**
     * 构造函数
     *
     * @param rateLimiter 限流器
     */
    public RateLimitMonitor(RateLimiter rateLimiter) {
        this.rateLimiter = rateLimiter;
    }
    
    /**
     * 初始化监控器
     */
    @PostConstruct
    public void init() {
        // 定期打印监控数据
        scheduler.scheduleAtFixedRate(this::printMonitorData, MONITOR_INTERVAL_MS, MONITOR_INTERVAL_MS, TimeUnit.MILLISECONDS);
    }
    
    /**
     * 记录API请求
     *
     * @param endpoint API端点
     * @param weight   请求权重
     */
    public void recordRequest(String endpoint, int weight) {
        endpointRequestCounter.computeIfAbsent(endpoint, k -> new AtomicInteger(0)).incrementAndGet();
        endpointWeightCounter.computeIfAbsent(endpoint, k -> new AtomicInteger(0)).addAndGet(weight);
    }
    
    /**
     * 记录请求延迟
     *
     * @param endpoint API端点
     * @param latencyMs 延迟时间（毫秒）
     */
    public void recordLatency(String endpoint, long latencyMs) {
        endpointLatencySum.computeIfAbsent(endpoint, k -> new AtomicLong(0)).addAndGet(latencyMs);
        endpointLatencyCount.computeIfAbsent(endpoint, k -> new AtomicInteger(0)).incrementAndGet();
    }
    
    /**
     * 记录限流事件
     */
    public void recordRateLimit() {
        rateLimitCount.incrementAndGet();
    }
    
    /**
     * 记录错误事件
     */
    public void recordError() {
        errorCount.incrementAndGet();
    }
    
    /**
     * 打印监控数据
     */
    private void printMonitorData() {
        try {
            log.info("API限流监控数据 - 时间窗口: {}分钟", MONITOR_INTERVAL_MS / 60000);
            log.info("总请求数: {}, 总限流次数: {}, 总错误次数: {}", 
                    getTotalRequests(), rateLimitCount.get(), errorCount.get());
            
            // 打印每个端点的统计数据
            endpointRequestCounter.forEach((endpoint, count) -> {
                int requests = count.get();
                int weight = endpointWeightCounter.getOrDefault(endpoint, new AtomicInteger(0)).get();
                double avgLatency = calculateAverageLatency(endpoint);
                double usageRate = rateLimiter.getUsageRate(endpoint);
                
                log.info("端点[{}] - 请求数: {}, 总权重: {}, 平均延迟: {:.2f}ms, 使用率: {:.1f}%", 
                        endpoint, requests, weight, avgLatency, usageRate * 100);
                
                // 检查是否接近限制
                if (usageRate > APILimitConstants.RATE_LIMIT_WARNING_THRESHOLD) {
                    log.warn("端点[{}]的API使用率较高: {:.1f}%", endpoint, usageRate * 100);
                }
            });
            
            // 重置计数器
            resetCounters();
            
        } catch (Exception e) {
            log.error("打印监控数据时发生错误: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 计算平均延迟
     *
     * @param endpoint API端点
     * @return 平均延迟（毫秒）
     */
    private double calculateAverageLatency(String endpoint) {
        AtomicLong sum = endpointLatencySum.getOrDefault(endpoint, new AtomicLong(0));
        AtomicInteger count = endpointLatencyCount.getOrDefault(endpoint, new AtomicInteger(0));
        
        if (count.get() == 0) {
            return 0.0;
        }
        
        return (double) sum.get() / count.get();
    }
    
    /**
     * 获取总请求数
     *
     * @return 总请求数
     */
    private int getTotalRequests() {
        return endpointRequestCounter.values().stream()
                .mapToInt(AtomicInteger::get)
                .sum();
    }
    
    /**
     * 重置计数器
     */
    private void resetCounters() {
        endpointRequestCounter.clear();
        endpointWeightCounter.clear();
        endpointLatencySum.clear();
        endpointLatencyCount.clear();
        rateLimitCount.set(0);
        errorCount.set(0);
    }
}