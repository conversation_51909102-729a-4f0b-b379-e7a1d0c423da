package com.crypto.trading.trade.service;

import com.crypto.trading.trade.model.entity.TradingSignalEntity;
import com.crypto.trading.trade.model.result.SignalProcessResult;

/**
 * 信号处理服务接口
 * 
 * <p>负责保存和处理有效的交易信号，并根据信号类型决定是否执行交易</p>
 */
public interface SignalProcessingService {

    /**
     * 处理交易信号
     *
     * @param validationResult 验证结果
     * @return 处理结果
     */
    SignalProcessResult processSignal(SignalValidationService.ValidationResult validationResult);

    /**
     * 标记信号为已处理
     *
     * @param signalEntity 信号实体
     * @param success 是否成功
     * @param result 处理结果
     */
    void markSignalAsProcessed(TradingSignalEntity signalEntity, boolean success, String result);
}