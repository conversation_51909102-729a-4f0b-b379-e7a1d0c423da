"""
启动资源预加载模块

提供crypto_ml_strategy项目的启动资源预加载功能，包括模型预加载、
缓存预热、配置加载和数据预处理。

Author: Crypto ML Strategy Team
Date: 2024-12-20
Version: 1.0.0
"""

import asyncio
import json
import pickle
import time
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Callable
from loguru import logger

from .error_handler_core import (
    CryptoMLException, ErrorCode, ErrorSeverity, ErrorCategory,
    ErrorContext, handle_exception
)


@dataclass
class PreloadResult:
    """预加载结果"""
    name: str
    success: bool
    message: str
    data_size: int = 0
    duration: float = 0.0
    cached: bool = False
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "name": self.name,
            "success": self.success,
            "message": self.message,
            "data_size": self.data_size,
            "duration": self.duration,
            "cached": self.cached,
            "metadata": self.metadata
        }


class ResourcePreloader(ABC):
    """资源预加载器抽象基类"""
    
    @abstractmethod
    async def preload(self) -> PreloadResult:
        """执行预加载"""
        pass
    
    @abstractmethod
    def get_name(self) -> str:
        """获取预加载器名称"""
        pass
    
    @abstractmethod
    def get_priority(self) -> int:
        """获取优先级（数字越小优先级越高）"""
        pass
    
    @abstractmethod
    def get_estimated_size(self) -> int:
        """获取预估数据大小（字节）"""
        pass


class ModelPreloader(ResourcePreloader):
    """模型预加载器"""
    
    def __init__(
        self,
        model_name: str,
        model_path: str,
        model_type: str = "pickle",
        priority: int = 100,
        cache_enabled: bool = True
    ):
        self.model_name = model_name
        self.model_path = model_path
        self.model_type = model_type.lower()
        self.priority = priority
        self.cache_enabled = cache_enabled
        self.cached_model: Optional[Any] = None
        self.cache_time: Optional[datetime] = None
    
    async def preload(self) -> PreloadResult:
        """预加载模型"""
        start_time = time.time()
        
        try:
            # 检查缓存
            if self.cache_enabled and self.cached_model is not None:
                duration = time.time() - start_time
                return PreloadResult(
                    name=self.model_name,
                    success=True,
                    message="从缓存加载模型",
                    data_size=self._estimate_model_size(self.cached_model),
                    duration=duration,
                    cached=True,
                    metadata={
                        "model_type": self.model_type,
                        "cache_time": self.cache_time.isoformat() if self.cache_time else None
                    }
                )
            
            # 检查文件是否存在
            model_file = Path(self.model_path)
            if not model_file.exists():
                raise CryptoMLException(
                    f"模型文件不存在: {self.model_path}",
                    ErrorCode.DATA_MISSING,
                    ErrorSeverity.ERROR,
                    ErrorCategory.MODEL_ERROR
                )
            
            # 加载模型
            logger.info(f"开始预加载模型: {self.model_name}")
            
            if self.model_type == "pickle":
                model = await self._load_pickle_model()
            elif self.model_type == "json":
                model = await self._load_json_model()
            elif self.model_type == "torch":
                model = await self._load_torch_model()
            elif self.model_type == "tensorflow":
                model = await self._load_tensorflow_model()
            else:
                raise ValueError(f"不支持的模型类型: {self.model_type}")
            
            # 缓存模型
            if self.cache_enabled:
                self.cached_model = model
                self.cache_time = datetime.now()
            
            duration = time.time() - start_time
            data_size = self._estimate_model_size(model)
            
            logger.info(f"模型预加载完成: {self.model_name} ({data_size/1024/1024:.1f}MB, {duration:.2f}s)")
            
            return PreloadResult(
                name=self.model_name,
                success=True,
                message=f"模型加载成功",
                data_size=data_size,
                duration=duration,
                cached=False,
                metadata={
                    "model_type": self.model_type,
                    "file_path": self.model_path,
                    "file_size": model_file.stat().st_size
                }
            )
            
        except Exception as e:
            duration = time.time() - start_time
            logger.error(f"模型预加载失败 {self.model_name}: {e}")
            
            return PreloadResult(
                name=self.model_name,
                success=False,
                message=f"模型加载失败: {str(e)}",
                duration=duration,
                metadata={"error": str(e)}
            )
    
    async def _load_pickle_model(self) -> Any:
        """加载Pickle模型"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            None,
            lambda: pickle.load(open(self.model_path, 'rb'))
        )
    
    async def _load_json_model(self) -> Any:
        """加载JSON模型"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            None,
            lambda: json.load(open(self.model_path, 'r'))
        )
    
    async def _load_torch_model(self) -> Any:
        """加载PyTorch模型"""
        try:
            import torch
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(
                None,
                lambda: torch.load(self.model_path, map_location='cpu')
            )
        except ImportError:
            raise CryptoMLException(
                "PyTorch未安装，无法加载torch模型",
                ErrorCode.MODEL_LOAD_FAILED,
                ErrorSeverity.ERROR,
                ErrorCategory.MODEL_ERROR
            )
    
    async def _load_tensorflow_model(self) -> Any:
        """加载TensorFlow模型"""
        try:
            import tensorflow as tf
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(
                None,
                lambda: tf.keras.models.load_model(self.model_path)
            )
        except ImportError:
            raise CryptoMLException(
                "TensorFlow未安装，无法加载tensorflow模型",
                ErrorCode.MODEL_LOAD_FAILED,
                ErrorSeverity.ERROR,
                ErrorCategory.MODEL_ERROR
            )
    
    def _estimate_model_size(self, model: Any) -> int:
        """估算模型大小"""
        try:
            import sys
            return sys.getsizeof(model)
        except:
            return 0
    
    def get_name(self) -> str:
        return self.model_name
    
    def get_priority(self) -> int:
        return self.priority
    
    def get_estimated_size(self) -> int:
        try:
            model_file = Path(self.model_path)
            if model_file.exists():
                return model_file.stat().st_size
        except:
            pass
        return 0


class CachePrewarmer(ResourcePreloader):
    """缓存预热器"""
    
    def __init__(
        self,
        cache_name: str,
        cache_data_loader: Callable,
        priority: int = 200,
        max_cache_size: int = 1000,
        ttl_seconds: int = 3600
    ):
        self.cache_name = cache_name
        self.cache_data_loader = cache_data_loader
        self.priority = priority
        self.max_cache_size = max_cache_size
        self.ttl_seconds = ttl_seconds
        self.cache: Dict[str, Any] = {}
        self.cache_times: Dict[str, datetime] = {}
    
    async def preload(self) -> PreloadResult:
        """预热缓存"""
        start_time = time.time()
        
        try:
            logger.info(f"开始预热缓存: {self.cache_name}")
            
            # 调用数据加载器
            if asyncio.iscoroutinefunction(self.cache_data_loader):
                cache_data = await self.cache_data_loader()
            else:
                loop = asyncio.get_event_loop()
                cache_data = await loop.run_in_executor(None, self.cache_data_loader)
            
            # 填充缓存
            if isinstance(cache_data, dict):
                for key, value in cache_data.items():
                    if len(self.cache) >= self.max_cache_size:
                        break
                    self.cache[key] = value
                    self.cache_times[key] = datetime.now()
            elif isinstance(cache_data, list):
                for i, item in enumerate(cache_data):
                    if len(self.cache) >= self.max_cache_size:
                        break
                    key = f"item_{i}"
                    self.cache[key] = item
                    self.cache_times[key] = datetime.now()
            else:
                # 单个数据项
                self.cache["default"] = cache_data
                self.cache_times["default"] = datetime.now()
            
            duration = time.time() - start_time
            data_size = self._estimate_cache_size()
            
            logger.info(f"缓存预热完成: {self.cache_name} ({len(self.cache)} 项, {data_size/1024:.1f}KB, {duration:.2f}s)")
            
            return PreloadResult(
                name=self.cache_name,
                success=True,
                message=f"缓存预热成功 ({len(self.cache)} 项)",
                data_size=data_size,
                duration=duration,
                metadata={
                    "cache_items": len(self.cache),
                    "max_cache_size": self.max_cache_size,
                    "ttl_seconds": self.ttl_seconds
                }
            )
            
        except Exception as e:
            duration = time.time() - start_time
            logger.error(f"缓存预热失败 {self.cache_name}: {e}")
            
            return PreloadResult(
                name=self.cache_name,
                success=False,
                message=f"缓存预热失败: {str(e)}",
                duration=duration,
                metadata={"error": str(e)}
            )
    
    def _estimate_cache_size(self) -> int:
        """估算缓存大小"""
        try:
            import sys
            return sum(sys.getsizeof(value) for value in self.cache.values())
        except:
            return len(self.cache) * 1024  # 粗略估算
    
    def get_cache_item(self, key: str) -> Optional[Any]:
        """获取缓存项"""
        if key not in self.cache:
            return None
        
        # 检查TTL
        cache_time = self.cache_times.get(key)
        if cache_time and datetime.now() - cache_time > timedelta(seconds=self.ttl_seconds):
            del self.cache[key]
            del self.cache_times[key]
            return None
        
        return self.cache[key]
    
    def get_name(self) -> str:
        return self.cache_name
    
    def get_priority(self) -> int:
        return self.priority
    
    def get_estimated_size(self) -> int:
        return self.max_cache_size * 1024  # 粗略估算


class ConfigurationLoader(ResourcePreloader):
    """配置加载器"""
    
    def __init__(
        self,
        config_name: str,
        config_paths: List[str],
        priority: int = 50,
        required: bool = True
    ):
        self.config_name = config_name
        self.config_paths = config_paths
        self.priority = priority
        self.required = required
        self.loaded_config: Optional[Dict[str, Any]] = None
    
    async def preload(self) -> PreloadResult:
        """预加载配置"""
        start_time = time.time()
        
        try:
            logger.info(f"开始加载配置: {self.config_name}")
            
            merged_config = {}
            loaded_files = []
            
            for config_path in self.config_paths:
                config_file = Path(config_path)
                
                if not config_file.exists():
                    if self.required:
                        raise CryptoMLException(
                            f"必需的配置文件不存在: {config_path}",
                            ErrorCode.DATA_MISSING,
                            ErrorSeverity.ERROR,
                            ErrorCategory.CONFIGURATION_ERROR
                        )
                    else:
                        logger.warning(f"可选配置文件不存在: {config_path}")
                        continue
                
                # 根据文件扩展名加载配置
                if config_file.suffix.lower() in ['.json']:
                    config_data = await self._load_json_config(config_path)
                elif config_file.suffix.lower() in ['.yaml', '.yml']:
                    config_data = await self._load_yaml_config(config_path)
                else:
                    logger.warning(f"不支持的配置文件格式: {config_path}")
                    continue
                
                # 合并配置
                merged_config.update(config_data)
                loaded_files.append(config_path)
            
            self.loaded_config = merged_config
            
            duration = time.time() - start_time
            data_size = len(json.dumps(merged_config).encode('utf-8'))
            
            logger.info(f"配置加载完成: {self.config_name} ({len(loaded_files)} 个文件, {duration:.2f}s)")
            
            return PreloadResult(
                name=self.config_name,
                success=True,
                message=f"配置加载成功 ({len(loaded_files)} 个文件)",
                data_size=data_size,
                duration=duration,
                metadata={
                    "loaded_files": loaded_files,
                    "config_keys": list(merged_config.keys()),
                    "total_keys": len(merged_config)
                }
            )
            
        except Exception as e:
            duration = time.time() - start_time
            logger.error(f"配置加载失败 {self.config_name}: {e}")
            
            return PreloadResult(
                name=self.config_name,
                success=False,
                message=f"配置加载失败: {str(e)}",
                duration=duration,
                metadata={"error": str(e)}
            )
    
    async def _load_json_config(self, config_path: str) -> Dict[str, Any]:
        """加载JSON配置"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            None,
            lambda: json.load(open(config_path, 'r', encoding='utf-8'))
        )
    
    async def _load_yaml_config(self, config_path: str) -> Dict[str, Any]:
        """加载YAML配置"""
        try:
            import yaml
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(
                None,
                lambda: yaml.safe_load(open(config_path, 'r', encoding='utf-8'))
            )
        except ImportError:
            raise CryptoMLException(
                "PyYAML未安装，无法加载YAML配置",
                ErrorCode.CONFIGURATION_ERROR,
                ErrorSeverity.ERROR,
                ErrorCategory.CONFIGURATION_ERROR
            )
    
    def get_config(self) -> Optional[Dict[str, Any]]:
        """获取加载的配置"""
        return self.loaded_config
    
    def get_name(self) -> str:
        return self.config_name
    
    def get_priority(self) -> int:
        return self.priority
    
    def get_estimated_size(self) -> int:
        total_size = 0
        for config_path in self.config_paths:
            try:
                config_file = Path(config_path)
                if config_file.exists():
                    total_size += config_file.stat().st_size
            except:
                pass
        return total_size


class ResourcePreloadManager:
    """资源预加载管理器"""
    
    def __init__(self, max_parallel_preloaders: int = 3):
        self.preloaders: List[ResourcePreloader] = []
        self.results: Dict[str, PreloadResult] = {}
        self.max_parallel_preloaders = max_parallel_preloaders
        self.total_estimated_size = 0
    
    def add_preloader(self, preloader: ResourcePreloader) -> None:
        """添加预加载器"""
        self.preloaders.append(preloader)
        self.total_estimated_size += preloader.get_estimated_size()
        logger.debug(f"添加资源预加载器: {preloader.get_name()}")
    
    def add_model_preloader(self, model_name: str, model_path: str, **kwargs) -> None:
        """添加模型预加载器"""
        preloader = ModelPreloader(model_name, model_path, **kwargs)
        self.add_preloader(preloader)
    
    def add_cache_prewarmer(self, cache_name: str, cache_data_loader: Callable, **kwargs) -> None:
        """添加缓存预热器"""
        preloader = CachePrewarmer(cache_name, cache_data_loader, **kwargs)
        self.add_preloader(preloader)
    
    def add_config_loader(self, config_name: str, config_paths: List[str], **kwargs) -> None:
        """添加配置加载器"""
        preloader = ConfigurationLoader(config_name, config_paths, **kwargs)
        self.add_preloader(preloader)
    
    @handle_exception(component="resource_preloader", operation="preload_all")
    async def preload_all(self, parallel: bool = True) -> Dict[str, PreloadResult]:
        """预加载所有资源"""
        logger.info(f"开始资源预加载 ({len(self.preloaders)} 个预加载器, 预估 {self.total_estimated_size/1024/1024:.1f}MB)")
        
        # 按优先级排序
        sorted_preloaders = sorted(self.preloaders, key=lambda p: p.get_priority())
        
        if parallel:
            # 并行预加载（受限于最大并行数）
            semaphore = asyncio.Semaphore(self.max_parallel_preloaders)
            
            async def preload_with_semaphore(preloader):
                async with semaphore:
                    return await preloader.preload()
            
            results = await asyncio.gather(
                *[preload_with_semaphore(preloader) for preloader in sorted_preloaders],
                return_exceptions=True
            )
            
            for preloader, result in zip(sorted_preloaders, results):
                if isinstance(result, Exception):
                    self.results[preloader.get_name()] = PreloadResult(
                        name=preloader.get_name(),
                        success=False,
                        message=f"预加载异常: {str(result)}",
                        metadata={"error": str(result)}
                    )
                else:
                    self.results[preloader.get_name()] = result
        else:
            # 串行预加载
            for preloader in sorted_preloaders:
                try:
                    result = await preloader.preload()
                    self.results[preloader.get_name()] = result
                except Exception as e:
                    self.results[preloader.get_name()] = PreloadResult(
                        name=preloader.get_name(),
                        success=False,
                        message=f"预加载异常: {str(e)}",
                        metadata={"error": str(e)}
                    )
        
        # 统计结果
        total_preloaders = len(self.results)
        successful_preloaders = len([r for r in self.results.values() if r.success])
        total_data_size = sum(r.data_size for r in self.results.values())
        total_duration = sum(r.duration for r in self.results.values())
        
        logger.info(f"资源预加载完成: {successful_preloaders}/{total_preloaders} 成功, {total_data_size/1024/1024:.1f}MB, {total_duration:.2f}s")
        
        return self.results
    
    def get_preload_summary(self) -> Dict[str, Any]:
        """获取预加载摘要"""
        if not self.results:
            return {"status": "no_preload_performed"}
        
        total_preloaders = len(self.results)
        successful_preloaders = len([r for r in self.results.values() if r.success])
        failed_preloaders = total_preloaders - successful_preloaders
        
        total_data_size = sum(r.data_size for r in self.results.values())
        total_duration = sum(r.duration for r in self.results.values())
        
        return {
            "total_preloaders": total_preloaders,
            "successful_preloaders": successful_preloaders,
            "failed_preloaders": failed_preloaders,
            "success_rate": successful_preloaders / total_preloaders if total_preloaders > 0 else 0,
            "total_data_size": total_data_size,
            "total_duration": total_duration,
            "estimated_size": self.total_estimated_size,
            "all_successful": failed_preloaders == 0,
            "failed_preloaders": [
                r.name for r in self.results.values() if not r.success
            ]
        }


# 全局资源预加载管理器
global_resource_preloader = ResourcePreloadManager()