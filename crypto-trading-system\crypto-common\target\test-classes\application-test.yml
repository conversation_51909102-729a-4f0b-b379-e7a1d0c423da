# 测试环境配置

# 应用配置
app:
  name: crypto-trading
  version: 1.0.0
  debug: false
  thread-pool:
    max-size: 100
    core-size: 10
    queue-capacity: 1000
    keep-alive-seconds: 60

# 币安API配置
binance:
  api:
    key: test-api-key
    secret: test-api-secret
    base-url: https://fapi.binance.com
    use-testnet: false
    connection-timeout: 5000
    read-timeout: 5000
    write-timeout: 5000
  websocket:
    base-url: wss://fstream.binance.com
  rate-limit:
    weight-limit: 1200
    weight-interval: 1
    order-limit: 100
    order-interval: 10

# 数据库配置
spring:
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password: 
    driver-class-name: org.h2.Driver
    hikari:
      maximum-pool-size: 10
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
  
  # Kafka配置
  kafka:
    bootstrap-servers: localhost:9092
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
      acks: all
      retries: 3
      batch-size: 16384
      buffer-memory: 33554432
      compression-type: lz4
      properties:
        enable.idempotence: true
    consumer:
      group-id: crypto-trading-test-group
      auto-offset-reset: earliest
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      max-poll-records: 500
      auto-commit-interval: 5000
      enable-auto-commit: true

# InfluxDB配置
influxdb:
  url: http://localhost:8086
  token: test-token
  org: crypto-trading-org
  bucket: crypto-trading-test
  enable-batch: true
  batch-config:
    batch-size: 1000
    flush-interval: 1
    jitter-interval: 1
    buffer-limit: 10000

# 日志配置
logging:
  level:
    root: INFO
    com.crypto.trading: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/crypto-trading-test.log
    max-size: 100MB
    max-history: 30