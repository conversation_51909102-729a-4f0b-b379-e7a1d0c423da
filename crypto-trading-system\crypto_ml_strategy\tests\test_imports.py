#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
导入测试脚本

测试所有模块是否可以正常导入，用于验证导入路径的正确性。
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def test_imports():
    """测试所有模块的导入"""
    
    print("开始测试模块导入...")
    
    # 测试基础模块
    try:
        from src.config import Config
        print("[OK] src.config 导入成功")
    except ImportError as e:
        print(f"[FAIL] src.config 导入失败: {e}")
    
    try:
        from src.logger import setup_logger
        print("[OK] src.logger 导入成功")
    except ImportError as e:
        print(f"[FAIL] src.logger 导入失败: {e}")
    
    try:
        from src.kafka_client import KafkaClient
        print("✅ src.kafka_client 导入成功")
    except ImportError as e:
        print(f"❌ src.kafka_client 导入失败: {e}")
    
    try:
        from src.data_processor import DataProcessor
        print("✅ src.data_processor 导入成功")
    except ImportError as e:
        print(f"❌ src.data_processor 导入失败: {e}")
    
    # 测试新创建的数据模块
    try:
        from src.data.influxdb_client import InfluxDBClient
        print("✅ src.data.influxdb_client 导入成功")
    except ImportError as e:
        print(f"❌ src.data.influxdb_client 导入失败: {e}")
    
    try:
        from src.data.mysql_client import MySQLClient
        print("✅ src.data.mysql_client 导入成功")
    except ImportError as e:
        print(f"❌ src.data.mysql_client 导入失败: {e}")
    
    try:
        from src.data.real_data_loader import RealDataLoader
        print("✅ src.data.real_data_loader 导入成功")
    except ImportError as e:
        print(f"❌ src.data.real_data_loader 导入失败: {e}")
    
    # 测试新创建的模型模块
    try:
        from src.model.model_trainer import ModelTrainer
        print("✅ src.model.model_trainer 导入成功")
    except ImportError as e:
        print(f"❌ src.model.model_trainer 导入失败: {e}")
    
    try:
        from src.model.prediction import PredictionEngine
        print("✅ src.model.prediction 导入成功")
    except ImportError as e:
        print(f"❌ src.model.prediction 导入失败: {e}")
    
    try:
        from src.model.online_learner import OnlineLearner
        print("✅ src.model.online_learner 导入成功")
    except ImportError as e:
        print(f"❌ src.model.online_learner 导入失败: {e}")
    
    try:
        from src.model.versioning import ModelVersionManager
        print("✅ src.model.versioning 导入成功")
    except ImportError as e:
        print(f"❌ src.model.versioning 导入失败: {e}")
    
    # 测试策略模块
    try:
        from src.strategy.base import BaseStrategy
        print("✅ src.strategy.base 导入成功")
    except ImportError as e:
        print(f"❌ src.strategy.base 导入失败: {e}")
    
    try:
        from src.strategy.unified_ml import UnifiedMLStrategy
        print("✅ src.strategy.unified_ml 导入成功")
    except ImportError as e:
        print(f"❌ src.strategy.unified_ml 导入失败: {e}")
    
    # 测试技术指标模块
    try:
        from src.indicators.lppl_features import LPPLFeatureExtractor
        print("✅ src.indicators.lppl_features 导入成功")
    except ImportError as e:
        print(f"❌ src.indicators.lppl_features 导入失败: {e}")
    
    try:
        from src.indicators.hematread_features import HematreadFeatureExtractor
        print("✅ src.indicators.hematread_features 导入成功")
    except ImportError as e:
        print(f"❌ src.indicators.hematread_features 导入失败: {e}")
    
    try:
        from src.indicators.bull_market_support import BullMarketSupportFeatureExtractor
        print("✅ src.indicators.bull_market_support 导入成功")
    except ImportError as e:
        print(f"❌ src.indicators.bull_market_support 导入失败: {e}")
    
    try:
        from src.indicators.super_trend import SuperTrendCalculator
        print("✅ src.indicators.super_trend 导入成功")
    except ImportError as e:
        print(f"❌ src.indicators.super_trend 导入失败: {e}")
    
    # 测试主应用模块
    try:
        from src.main import TradingStrategyApp
        print("✅ src.main 导入成功")
    except ImportError as e:
        print(f"❌ src.main 导入失败: {e}")
    
    print("\n导入测试完成！")

def test_circular_imports():
    """测试循环导入问题"""
    
    print("\n开始测试循环导入...")
    
    # 这个测试通过尝试导入所有模块来检测循环导入
    # 如果存在循环导入，Python会抛出ImportError
    
    modules_to_test = [
        'src.config',
        'src.logger', 
        'src.kafka_client',
        'src.data_processor',
        'src.data.influxdb_client',
        'src.data.mysql_client',
        'src.data.real_data_loader',
        'src.model.model_trainer',
        'src.model.prediction',
        'src.model.online_learner',
        'src.model.versioning',
        'src.strategy.base',
        'src.strategy.unified_ml',
        'src.indicators.lppl_features',
        'src.indicators.hematread_features',
        'src.indicators.bull_market_support',
        'src.indicators.super_trend',
        'src.main'
    ]
    
    try:
        for module_name in modules_to_test:
            __import__(module_name)
        print("✅ 未检测到循环导入问题")
    except ImportError as e:
        print(f"❌ 检测到循环导入问题: {e}")
    except Exception as e:
        print(f"❌ 导入测试中出现其他错误: {e}")

if __name__ == "__main__":
    test_imports()
    test_circular_imports()