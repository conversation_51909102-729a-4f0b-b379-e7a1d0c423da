package com.crypto.trading.common.util;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 文件工具类测试
 * <p>
 * 测试文件操作相关的工具方法
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
class FileUtilTest {

    /**
     * 临时目录
     */
    @TempDir
    Path tempDir;

    /**
     * 测试文件
     */
    private Path testFile;

    /**
     * 测试文件内容
     */
    private static final String TEST_CONTENT = "测试内容\n第二行\n第三行";

    /**
     * 测试初始化
     */
    @BeforeEach
    void setUp() throws IOException {
        testFile = tempDir.resolve("test.txt");
        Files.writeString(testFile, TEST_CONTENT);
    }

    /**
     * 测试清理
     */
    @AfterEach
    void tearDown() throws IOException {
        if (Files.exists(testFile)) {
            Files.delete(testFile);
        }
    }

    /**
     * 测试读取文件内容为字符串
     */
    @Test
    void testReadToString() throws IOException {
        // 测试使用Path参数
        String content = FileUtil.readToString(testFile);
        assertEquals(TEST_CONTENT, content);

        // 测试使用File参数
        content = FileUtil.readToString(testFile.toFile());
        assertEquals(TEST_CONTENT, content);

        // 测试使用String参数
        content = FileUtil.readToString(testFile.toString());
        assertEquals(TEST_CONTENT, content);
    }

    /**
     * 测试读取文件内容为字节数组
     */
    @Test
    void testReadToByteArray() throws IOException {
        // 测试使用Path参数
        byte[] bytes = FileUtil.readToByteArray(testFile);
        assertArrayEquals(TEST_CONTENT.getBytes(), bytes);

        // 测试使用File参数
        bytes = FileUtil.readToByteArray(testFile.toFile());
        assertArrayEquals(TEST_CONTENT.getBytes(), bytes);

        // 测试使用String参数
        bytes = FileUtil.readToByteArray(testFile.toString());
        assertArrayEquals(TEST_CONTENT.getBytes(), bytes);
    }

    /**
     * 测试读取文件内容为行列表
     */
    @Test
    void testReadLines() throws IOException {
        List<String> expectedLines = Arrays.asList("测试内容", "第二行", "第三行");

        // 测试使用Path参数
        List<String> lines = FileUtil.readLines(testFile);
        assertEquals(expectedLines, lines);

        // 测试使用File参数
        lines = FileUtil.readLines(testFile.toFile());
        assertEquals(expectedLines, lines);

        // 测试使用String参数
        lines = FileUtil.readLines(testFile.toString());
        assertEquals(expectedLines, lines);
    }

    /**
     * 测试写入字符串到文件
     */
    @Test
    void testWriteString() throws IOException {
        String newContent = "新内容";
        Path newFile = tempDir.resolve("new.txt");

        // 测试使用Path参数
        FileUtil.writeString(newContent, newFile);
        assertEquals(newContent, Files.readString(newFile));

        // 测试使用File参数
        String newContent2 = "新内容2";
        FileUtil.writeString(newContent2, newFile.toFile());
        assertEquals(newContent2, Files.readString(newFile));

        // 测试使用String参数
        String newContent3 = "新内容3";
        FileUtil.writeString(newContent3, newFile.toString());
        assertEquals(newContent3, Files.readString(newFile));

        // 清理
        Files.deleteIfExists(newFile);
    }

    /**
     * 测试写入字节数组到文件
     */
    @Test
    void testWriteBytes() throws IOException {
        byte[] newContent = "新内容".getBytes();
        Path newFile = tempDir.resolve("new.txt");

        // 测试使用Path参数
        FileUtil.writeBytes(newContent, newFile);
        assertArrayEquals(newContent, Files.readAllBytes(newFile));

        // 测试使用File参数
        byte[] newContent2 = "新内容2".getBytes();
        FileUtil.writeBytes(newContent2, newFile.toFile());
        assertArrayEquals(newContent2, Files.readAllBytes(newFile));

        // 测试使用String参数
        byte[] newContent3 = "新内容3".getBytes();
        FileUtil.writeBytes(newContent3, newFile.toString());
        assertArrayEquals(newContent3, Files.readAllBytes(newFile));

        // 清理
        Files.deleteIfExists(newFile);
    }

    /**
     * 测试写入行列表到文件
     */
    @Test
    void testWriteLines() throws IOException {
        List<String> lines = Arrays.asList("行1", "行2", "行3");
        Path newFile = tempDir.resolve("new.txt");

        // 测试使用Path参数
        FileUtil.writeLines(lines, newFile);
        assertEquals(lines, Files.readAllLines(newFile));

        // 测试使用File参数
        List<String> lines2 = Arrays.asList("行A", "行B", "行C");
        FileUtil.writeLines(lines2, newFile.toFile());
        assertEquals(lines2, Files.readAllLines(newFile));

        // 测试使用String参数
        List<String> lines3 = Arrays.asList("行X", "行Y", "行Z");
        FileUtil.writeLines(lines3, newFile.toString());
        assertEquals(lines3, Files.readAllLines(newFile));

        // 清理
        Files.deleteIfExists(newFile);
    }

    /**
     * 测试追加字符串到文件
     */
    @Test
    void testAppendString() throws IOException {
        String appendContent = "\n追加内容";
        
        // 测试使用Path参数
        FileUtil.appendString(appendContent, testFile);
        assertEquals(TEST_CONTENT + appendContent, Files.readString(testFile));
        
        // 恢复测试文件内容
        Files.writeString(testFile, TEST_CONTENT);
        
        // 测试使用File参数
        FileUtil.appendString(appendContent, testFile.toFile());
        assertEquals(TEST_CONTENT + appendContent, Files.readString(testFile));
        
        // 恢复测试文件内容
        Files.writeString(testFile, TEST_CONTENT);
        
        // 测试使用String参数
        FileUtil.appendString(appendContent, testFile.toString());
        assertEquals(TEST_CONTENT + appendContent, Files.readString(testFile));
    }

    /**
     * 测试复制文件
     */
    @Test
    void testCopy() throws IOException {
        Path targetFile = tempDir.resolve("copy.txt");
        
        // 测试使用Path参数
        FileUtil.copy(testFile, targetFile);
        assertTrue(Files.exists(targetFile));
        assertEquals(TEST_CONTENT, Files.readString(targetFile));
        
        // 清理
        Files.delete(targetFile);
        
        // 测试使用File参数
        FileUtil.copy(testFile.toFile(), targetFile.toFile());
        assertTrue(Files.exists(targetFile));
        assertEquals(TEST_CONTENT, Files.readString(targetFile));
        
        // 清理
        Files.delete(targetFile);
        
        // 测试使用String参数
        FileUtil.copy(testFile.toString(), targetFile.toString());
        assertTrue(Files.exists(targetFile));
        assertEquals(TEST_CONTENT, Files.readString(targetFile));
        
        // 清理
        Files.delete(targetFile);
    }

    /**
     * 测试移动文件
     */
    @Test
    void testMove() throws IOException {
        Path sourceFile = tempDir.resolve("source.txt");
        Path targetFile = tempDir.resolve("target.txt");
        
        // 创建源文件
        Files.writeString(sourceFile, TEST_CONTENT);
        
        // 测试使用Path参数
        FileUtil.move(sourceFile, targetFile);
        assertFalse(Files.exists(sourceFile));
        assertTrue(Files.exists(targetFile));
        assertEquals(TEST_CONTENT, Files.readString(targetFile));
        
        // 清理
        Files.delete(targetFile);
        
        // 重新创建源文件
        Files.writeString(sourceFile, TEST_CONTENT);
        
        // 测试使用File参数
        FileUtil.move(sourceFile.toFile(), targetFile.toFile());
        assertFalse(Files.exists(sourceFile));
        assertTrue(Files.exists(targetFile));
        assertEquals(TEST_CONTENT, Files.readString(targetFile));
        
        // 清理
        Files.delete(targetFile);
        
        // 重新创建源文件
        Files.writeString(sourceFile, TEST_CONTENT);
        
        // 测试使用String参数
        FileUtil.move(sourceFile.toString(), targetFile.toString());
        assertFalse(Files.exists(sourceFile));
        assertTrue(Files.exists(targetFile));
        assertEquals(TEST_CONTENT, Files.readString(targetFile));
        
        // 清理
        Files.delete(targetFile);
    }

    /**
     * 测试删除文件
     */
    @Test
    void testDelete() throws IOException {
        Path fileToDelete = tempDir.resolve("to-delete.txt");
        Files.writeString(fileToDelete, "要删除的文件");
        
        // 测试使用Path参数
        assertTrue(Files.exists(fileToDelete));
        FileUtil.delete(fileToDelete);
        assertFalse(Files.exists(fileToDelete));
        
        // 重新创建文件
        Files.writeString(fileToDelete, "要删除的文件");
        
        // 测试使用File参数
        assertTrue(Files.exists(fileToDelete));
        FileUtil.delete(fileToDelete.toFile());
        assertFalse(Files.exists(fileToDelete));
        
        // 重新创建文件
        Files.writeString(fileToDelete, "要删除的文件");
        
        // 测试使用String参数
        assertTrue(Files.exists(fileToDelete));
        FileUtil.delete(fileToDelete.toString());
        assertFalse(Files.exists(fileToDelete));
    }

    /**
     * 测试创建目录
     */
    @Test
    void testCreateDirectory() throws IOException {
        Path dirPath = tempDir.resolve("new-dir");
        
        // 测试使用Path参数
        FileUtil.createDirectory(dirPath);
        assertTrue(Files.exists(dirPath));
        assertTrue(Files.isDirectory(dirPath));
        
        // 清理
        Files.delete(dirPath);
        
        // 测试使用File参数
        FileUtil.createDirectory(dirPath.toFile());
        assertTrue(Files.exists(dirPath));
        assertTrue(Files.isDirectory(dirPath));
        
        // 清理
        Files.delete(dirPath);
        
        // 测试使用String参数
        FileUtil.createDirectory(dirPath.toString());
        assertTrue(Files.exists(dirPath));
        assertTrue(Files.isDirectory(dirPath));
        
        // 清理
        Files.delete(dirPath);
    }

    /**
     * 测试获取文件大小
     */
    @Test
    void testSize() throws IOException {
        long expectedSize = TEST_CONTENT.getBytes().length;
        
        // 测试使用Path参数
        assertEquals(expectedSize, FileUtil.size(testFile));
        
        // 测试使用File参数
        assertEquals(expectedSize, FileUtil.size(testFile.toFile()));
        
        // 测试使用String参数
        assertEquals(expectedSize, FileUtil.size(testFile.toString()));
    }

    /**
     * 测试文件是否存在
     */
    @Test
    void testExists() {
        Path nonExistentFile = tempDir.resolve("non-existent.txt");
        
        // 测试使用Path参数
        assertTrue(FileUtil.exists(testFile));
        assertFalse(FileUtil.exists(nonExistentFile));
        
        // 测试使用File参数
        assertTrue(FileUtil.exists(testFile.toFile()));
        assertFalse(FileUtil.exists(nonExistentFile.toFile()));
        
        // 测试使用String参数
        assertTrue(FileUtil.exists(testFile.toString()));
        assertFalse(FileUtil.exists(nonExistentFile.toString()));
    }

    /**
     * 测试是否为目录
     */
    @Test
    void testIsDirectory() {
        // 测试使用Path参数
        assertTrue(FileUtil.isDirectory(tempDir));
        assertFalse(FileUtil.isDirectory(testFile));
        
        // 测试使用File参数
        assertTrue(FileUtil.isDirectory(tempDir.toFile()));
        assertFalse(FileUtil.isDirectory(testFile.toFile()));
        
        // 测试使用String参数
        assertTrue(FileUtil.isDirectory(tempDir.toString()));
        assertFalse(FileUtil.isDirectory(testFile.toString()));
    }

    /**
     * 测试是否为普通文件
     */
    @Test
    void testIsRegularFile() {
        // 测试使用Path参数
        assertTrue(FileUtil.isRegularFile(testFile));
        assertFalse(FileUtil.isRegularFile(tempDir));
        
        // 测试使用File参数
        assertTrue(FileUtil.isRegularFile(testFile.toFile()));
        assertFalse(FileUtil.isRegularFile(tempDir.toFile()));
        
        // 测试使用String参数
        assertTrue(FileUtil.isRegularFile(testFile.toString()));
        assertFalse(FileUtil.isRegularFile(tempDir.toString()));
    }

    /**
     * 测试获取文件名
     */
    @Test
    void testGetFileName() {
        assertEquals("test.txt", FileUtil.getFileName(testFile.toString()));
    }

    /**
     * 测试获取文件扩展名
     */
    @Test
    void testGetFileExtension() {
        assertEquals("txt", FileUtil.getFileExtension("test.txt"));
        assertEquals("", FileUtil.getFileExtension("test"));
    }

    /**
     * 测试获取文件名（不含扩展名）
     */
    @Test
    void testGetFileNameWithoutExtension() {
        assertEquals("test", FileUtil.getFileNameWithoutExtension("test.txt"));
        assertEquals("test", FileUtil.getFileNameWithoutExtension("test"));
    }

    /**
     * 测试列出目录下的文件
     */
    @Test
    void testListFiles() throws IOException {
        // 创建测试文件
        Path file1 = tempDir.resolve("file1.txt");
        Path file2 = tempDir.resolve("file2.txt");
        Files.writeString(file1, "file1");
        Files.writeString(file2, "file2");
        
        // 测试使用Path参数
        List<Path> files = FileUtil.listFiles(tempDir);
        assertEquals(3, files.size()); // 包括原始的testFile
        
        List<String> fileNames = files.stream()
                .map(Path::getFileName)
                .map(Path::toString)
                .collect(Collectors.toList());
        
        assertTrue(fileNames.contains("file1.txt"));
        assertTrue(fileNames.contains("file2.txt"));
        assertTrue(fileNames.contains("test.txt"));
        
        // 清理
        Files.delete(file1);
        Files.delete(file2);
    }

    /**
     * 测试压缩和解压缩
     */
    @Test
    void testZipAndUnzip() throws IOException {
        // 创建测试文件
        Path file1 = tempDir.resolve("file1.txt");
        Path file2 = tempDir.resolve("file2.txt");
        Files.writeString(file1, "file1 content");
        Files.writeString(file2, "file2 content");
        
        // 创建测试子目录和文件
        Path subDir = tempDir.resolve("subdir");
        Files.createDirectory(subDir);
        Path file3 = subDir.resolve("file3.txt");
        Files.writeString(file3, "file3 content");
        
        // 测试压缩目录
        Path zipFile = tempDir.resolve("test.zip");
        FileUtil.zip(tempDir, zipFile);
        assertTrue(Files.exists(zipFile));
        
        // 测试解压缩
        Path extractDir = tempDir.resolve("extract");
        Files.createDirectory(extractDir);
        FileUtil.unzip(zipFile, extractDir);
        
        // 验证解压结果
        assertTrue(Files.exists(extractDir.resolve("file1.txt")));
        assertTrue(Files.exists(extractDir.resolve("file2.txt")));
        assertTrue(Files.exists(extractDir.resolve("test.txt")));
        assertTrue(Files.exists(extractDir.resolve("subdir/file3.txt")));
        
        assertEquals("file1 content", Files.readString(extractDir.resolve("file1.txt")));
        assertEquals("file2 content", Files.readString(extractDir.resolve("file2.txt")));
        assertEquals("file3 content", Files.readString(extractDir.resolve("subdir/file3.txt")));
    }
} 