"""
风险管理模块

提供完整的风险管理功能，包括风险评估、仓位管理、止损止盈、
回撤控制和风险控制引擎等组件。

主要功能：
- 实时风险评估和监控
- 智能仓位计算和管理
- 灵活的止损止盈策略
- 回撤监控和控制
- 统一的风险控制引擎
- 与Java模块和Kafka的集成

Author: Crypto ML Strategy Team
Date: 2024-12-19
Version: 1.0.0
"""

from typing import Dict, List, Optional, Any
import logging

# 导入配置和数据结构
from .risk_config import (
    RiskConfig,
    RiskMetrics,
    PositionInfo,
    RiskEvent,
    RiskLevel,
    PositionSizeMethod,
    StopLossType,
    TakeProfitType,
    DEFAULT_RISK_CONFIG,
    CONSERVATIVE_RISK_CONFIG,
    AGGRESSIVE_RISK_CONFIG
)

# 导入核心组件
from .risk_assessor import RiskAssessor
from .position_manager import PositionManager
from .stop_loss_take_profit import StopLossTakeProfitManager
from .drawdown_controller import DrawdownController, DrawdownInfo, RecoveryAction
from .risk_control_engine import RiskControlEngine, RiskControlAction

# 版本信息
__version__ = "1.0.0"
__author__ = "Crypto ML Strategy Team"

# 配置日志
logger = logging.getLogger(__name__)

# 导出的公共接口
__all__ = [
    # 配置和数据结构
    'RiskConfig',
    'RiskMetrics',
    'PositionInfo',
    'RiskEvent',
    'RiskLevel',
    'PositionSizeMethod',
    'StopLossType',
    'TakeProfitType',
    'DrawdownInfo',
    'RecoveryAction',
    'RiskControlAction',
    
    # 预定义配置
    'DEFAULT_RISK_CONFIG',
    'CONSERVATIVE_RISK_CONFIG',
    'AGGRESSIVE_RISK_CONFIG',
    
    # 核心组件
    'RiskAssessor',
    'PositionManager',
    'StopLossTakeProfitManager',
    'DrawdownController',
    'RiskControlEngine',
    
    # 便捷函数
    'create_risk_manager',
    'create_conservative_risk_manager',
    'create_aggressive_risk_manager',
    'validate_risk_config',
    'get_default_risk_config',
    'get_risk_manager_info'
]


def create_risk_manager(config: Optional[RiskConfig] = None) -> RiskControlEngine:
    """
    创建风险管理器
    
    Args:
        config: 风险配置，如果为None则使用默认配置
        
    Returns:
        风险控制引擎实例
        
    Example:
        >>> risk_manager = create_risk_manager()
        >>> risk_manager.start()
    """
    try:
        if config is None:
            config = DEFAULT_RISK_CONFIG
        
        # 验证配置
        if not config.validate():
            raise ValueError("Invalid risk configuration")
        
        # 创建风险控制引擎
        engine = RiskControlEngine(config)
        
        logger.info("Risk manager created successfully")
        return engine
        
    except Exception as e:
        logger.error(f"Error creating risk manager: {e}")
        raise


def create_conservative_risk_manager() -> RiskControlEngine:
    """
    创建保守型风险管理器
    
    Returns:
        配置为保守策略的风险控制引擎
        
    Example:
        >>> conservative_manager = create_conservative_risk_manager()
        >>> conservative_manager.start()
    """
    try:
        return create_risk_manager(CONSERVATIVE_RISK_CONFIG)
    except Exception as e:
        logger.error(f"Error creating conservative risk manager: {e}")
        raise


def create_aggressive_risk_manager() -> RiskControlEngine:
    """
    创建激进型风险管理器
    
    Returns:
        配置为激进策略的风险控制引擎
        
    Example:
        >>> aggressive_manager = create_aggressive_risk_manager()
        >>> aggressive_manager.start()
    """
    try:
        return create_risk_manager(AGGRESSIVE_RISK_CONFIG)
    except Exception as e:
        logger.error(f"Error creating aggressive risk manager: {e}")
        raise


def validate_risk_config(config: RiskConfig) -> bool:
    """
    验证风险配置的有效性
    
    Args:
        config: 风险配置
        
    Returns:
        配置是否有效
        
    Example:
        >>> config = RiskConfig()
        >>> is_valid = validate_risk_config(config)
    """
    try:
        return config.validate()
    except Exception as e:
        logger.error(f"Error validating risk config: {e}")
        return False


def get_default_risk_config() -> RiskConfig:
    """
    获取默认风险配置
    
    Returns:
        默认风险配置实例
        
    Example:
        >>> config = get_default_risk_config()
        >>> config.max_single_position_ratio = 0.05
    """
    try:
        return DEFAULT_RISK_CONFIG
    except Exception as e:
        logger.error(f"Error getting default risk config: {e}")
        raise


def get_risk_manager_info() -> Dict[str, Any]:
    """
    获取风险管理模块信息
    
    Returns:
        模块信息字典
        
    Example:
        >>> info = get_risk_manager_info()
        >>> print(f"Version: {info['version']}")
    """
    try:
        return {
            'module_name': 'risk_management',
            'version': __version__,
            'author': __author__,
            'components': [
                'RiskAssessor',
                'PositionManager', 
                'StopLossTakeProfitManager',
                'DrawdownController',
                'RiskControlEngine'
            ],
            'features': [
                'Real-time risk assessment',
                'Intelligent position sizing',
                'Flexible stop-loss/take-profit',
                'Drawdown monitoring and control',
                'Unified risk control engine',
                'Java module integration',
                'Kafka messaging support'
            ],
            'supported_strategies': [
                'Fixed ratio positioning',
                'Kelly formula positioning',
                'Risk parity positioning',
                'Volatility adjusted positioning',
                'Adaptive positioning',
                'Fixed percentage stops',
                'ATR-based stops',
                'Trailing stops',
                'Technical indicator stops',
                'Adaptive stops'
            ]
        }
    except Exception as e:
        logger.error(f"Error getting risk manager info: {e}")
        return {'error': str(e)}


# 模块初始化
def _initialize_module():
    """初始化风险管理模块"""
    try:
        logger.info(f"Risk management module initialized - Version {__version__}")
        
        # 验证默认配置
        if not DEFAULT_RISK_CONFIG.validate():
            logger.warning("Default risk configuration validation failed")
        
        # 验证预设配置
        if not CONSERVATIVE_RISK_CONFIG.validate():
            logger.warning("Conservative risk configuration validation failed")
            
        if not AGGRESSIVE_RISK_CONFIG.validate():
            logger.warning("Aggressive risk configuration validation failed")
        
        logger.info("Risk management module ready")
        
    except Exception as e:
        logger.error(f"Error initializing risk management module: {e}")


# 执行模块初始化
_initialize_module()


# 便捷的组件创建函数
class RiskManagerFactory:
    """风险管理器工厂类"""
    
    @staticmethod
    def create_assessor(config: Optional[RiskConfig] = None) -> RiskAssessor:
        """创建风险评估器"""
        config = config or DEFAULT_RISK_CONFIG
        return RiskAssessor(config)
    
    @staticmethod
    def create_position_manager(config: Optional[RiskConfig] = None) -> PositionManager:
        """创建仓位管理器"""
        config = config or DEFAULT_RISK_CONFIG
        return PositionManager(config)
    
    @staticmethod
    def create_stop_manager(config: Optional[RiskConfig] = None) -> StopLossTakeProfitManager:
        """创建止损止盈管理器"""
        config = config or DEFAULT_RISK_CONFIG
        return StopLossTakeProfitManager(config)
    
    @staticmethod
    def create_drawdown_controller(config: Optional[RiskConfig] = None) -> DrawdownController:
        """创建回撤控制器"""
        config = config or DEFAULT_RISK_CONFIG
        return DrawdownController(config)
    
    @staticmethod
    def create_control_engine(config: Optional[RiskConfig] = None) -> RiskControlEngine:
        """创建风险控制引擎"""
        config = config or DEFAULT_RISK_CONFIG
        return RiskControlEngine(config)


# 添加工厂类到导出列表
__all__.append('RiskManagerFactory')


# 模块级别的便捷访问
def get_factory() -> RiskManagerFactory:
    """获取风险管理器工厂实例"""
    return RiskManagerFactory()


# 添加到导出列表
__all__.append('get_factory')


# 集成测试函数
def run_integration_test() -> bool:
    """
    运行集成测试
    
    Returns:
        测试是否通过
    """
    try:
        logger.info("Running risk management integration test...")
        
        # 测试配置创建
        config = get_default_risk_config()
        assert config.validate(), "Default config validation failed"
        
        # 测试组件创建
        factory = get_factory()
        
        assessor = factory.create_assessor(config)
        assert assessor is not None, "Risk assessor creation failed"
        
        position_manager = factory.create_position_manager(config)
        assert position_manager is not None, "Position manager creation failed"
        
        stop_manager = factory.create_stop_manager(config)
        assert stop_manager is not None, "Stop manager creation failed"
        
        drawdown_controller = factory.create_drawdown_controller(config)
        assert drawdown_controller is not None, "Drawdown controller creation failed"
        
        control_engine = factory.create_control_engine(config)
        assert control_engine is not None, "Control engine creation failed"
        
        # 测试风险管理器创建
        risk_manager = create_risk_manager(config)
        assert risk_manager is not None, "Risk manager creation failed"
        
        logger.info("Risk management integration test passed")
        return True
        
    except Exception as e:
        logger.error(f"Risk management integration test failed: {e}")
        return False


# 添加到导出列表
__all__.append('run_integration_test')
