package com.crypto.trading.market.listener;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.crypto.trading.market.config.MarketDataConfig;
import com.crypto.trading.market.processor.DepthDataProcessor;
import com.crypto.trading.sdk.response.model.DepthData;
import com.crypto.trading.sdk.response.model.WebSocketMessage;
import com.crypto.trading.sdk.websocket.BinanceWebSocketClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Consumer;

/**
 * 深度数据监听器
 * 负责订阅和处理币安WebSocket深度数据
 */
@Component
public class DepthDataListener {

    private static final Logger log = LoggerFactory.getLogger(DepthDataListener.class);

    @Autowired
    private BinanceWebSocketClient webSocketClient;

    @Autowired
    private DepthDataProcessor depthDataProcessor;

    @Autowired
    private MarketDataConfig marketDataConfig;

    @Autowired
    @org.springframework.beans.factory.annotation.Qualifier("symbolsList")
    private List<String> symbols;

    /**
     * 连接ID映射，用于跟踪WebSocket连接
     * 格式: symbol -> connectionId
     */
    private final Map<String, Integer> connectionIds = new ConcurrentHashMap<>();

    /**
     * 启动监听器
     */
    public void start() {
        startDepthDataSubscription();
    }
    
    /**
     * 启动深度数据订阅
     */
    public void startDepthDataSubscription() {
        log.info("开始订阅深度数据...");
        
        int levels = marketDataConfig.getDepthLevels();
        int speed = marketDataConfig.getDepthSpeed();
        
        // 为每个交易对创建WebSocket连接
        for (String symbol : symbols) {
            // 创建处理器
            Consumer<String> messageHandler = createDepthMessageHandler(symbol);
            
            // 订阅深度数据
            int connectionId = webSocketClient.subscribeDepth(symbol.toLowerCase(), levels, speed, messageHandler);
            
            // 保存连接ID
            connectionIds.put(symbol, connectionId);
            
            log.info("已订阅深度数据: symbol={}, levels={}, speed={}, connectionId={}", 
                    symbol, levels, speed, connectionId);
        }
    }

    /**
     * 停止深度数据订阅
     */
    public void stopDepthDataSubscription() {
        log.info("停止订阅深度数据...");
        
        // 关闭所有WebSocket连接
        for (Map.Entry<String, Integer> entry : connectionIds.entrySet()) {
            webSocketClient.closeConnection(entry.getValue());
            log.info("已关闭深度数据连接: symbol={}, connectionId={}", entry.getKey(), entry.getValue());
        }
        
        // 清空连接ID映射
        connectionIds.clear();
    }

    /**
     * 创建深度消息处理器
     *
     * @param symbol 交易对
     * @return 消息处理器
     */
    private Consumer<String> createDepthMessageHandler(String symbol) {
        return message -> {
            try {
                // 添加详细日志：记录收到的原始消息
                log.debug("收到WebSocket消息: symbol={}, messageLength={}", symbol, message.length());
                log.trace("原始消息内容: symbol={}, message={}", symbol, message);

                // 解析消息
                JSONObject jsonObject = JSON.parseObject(message);

                // 记录解析后的JSON结构
                log.debug("解析JSON成功: symbol={}, keys={}", symbol, jsonObject.keySet());

                // 检查消息是否包含data字段（币安WebSocket消息格式）
                JSONObject dataObject;
                if (jsonObject.containsKey("data")) {
                    // 嵌套格式：{"stream": "...", "data": {...}}
                    dataObject = jsonObject.getJSONObject("data");
                    log.debug("使用嵌套消息格式: symbol={}, dataKeys={}", symbol, dataObject.keySet());
                } else {
                    // 直接格式：{"u": ..., "b": [...], "a": [...]}
                    dataObject = jsonObject;
                    log.debug("使用直接消息格式: symbol={}, keys={}", symbol, dataObject.keySet());
                }

                // 检查是否为深度更新消息（币安使用"u"字段表示lastUpdateId）
                if (!dataObject.containsKey("u") && !dataObject.containsKey("lastUpdateId")) {
                    log.debug("消息不包含深度数据字段，跳过处理: symbol={}, keys={}", symbol, dataObject.keySet());
                    return;
                }

                // 记录深度数据基本信息（币安深度更新使用"u"字段）
                Long lastUpdateId = dataObject.containsKey("u") ?
                    dataObject.getLongValue("u") : dataObject.getLongValue("lastUpdateId");
                JSONArray bidsArray = dataObject.getJSONArray("b");  // 币安使用"b"表示bids
                JSONArray asksArray = dataObject.getJSONArray("a");  // 币安使用"a"表示asks

                // 如果没有找到bids和asks数组，尝试使用完整字段名
                if (bidsArray == null) {
                    bidsArray = dataObject.getJSONArray("bids");
                }
                if (asksArray == null) {
                    asksArray = dataObject.getJSONArray("asks");
                }

                log.info("收到深度数据: symbol={}, lastUpdateId={}, bids={}, asks={}",
                        symbol, lastUpdateId,
                        bidsArray != null ? bidsArray.size() : 0,
                        asksArray != null ? asksArray.size() : 0);

                // 创建WebSocketMessage包装器
                WebSocketMessage<DepthData> webSocketMessage = new WebSocketMessage<>();
                webSocketMessage.setSymbol(symbol.toUpperCase());
                webSocketMessage.setEventType("depth");
                webSocketMessage.setEventTime(System.currentTimeMillis());

                // 构建DepthData对象
                DepthData depthData = new DepthData();
                depthData.setLastUpdateId(lastUpdateId);

                // 解析买单和卖单列表
                List<DepthData.PriceQuantity> bids = parsePriceQuantities(bidsArray);
                List<DepthData.PriceQuantity> asks = parsePriceQuantities(asksArray);

                depthData.setBids(bids);
                depthData.setAsks(asks);
                depthData.setEventTime(System.currentTimeMillis());

                // 设置数据
                webSocketMessage.setData(depthData);

                log.debug("深度数据对象构建完成: symbol={}, bidsSize={}, asksSize={}",
                        symbol, bids.size(), asks.size());

                // 处理深度数据
                log.debug("开始处理深度数据: symbol={}", symbol);
                depthDataProcessor.processDepthData(webSocketMessage);
                log.debug("深度数据处理完成: symbol={}", symbol);

            } catch (Exception e) {
                log.error("处理深度数据异常: symbol={}, error={}, message={}", symbol, e.getMessage(), message, e);
            }
        };
    }

    /**
     * 解析价格和数量对列表
     *
     * @param jsonArray JSON数组
     * @return 价格和数量对列表
     */
    private List<DepthData.PriceQuantity> parsePriceQuantities(JSONArray jsonArray) {
        List<DepthData.PriceQuantity> priceQuantities = new ArrayList<>(jsonArray.size());
        
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONArray item = jsonArray.getJSONArray(i);
            BigDecimal price = new BigDecimal(item.getString(0));
            BigDecimal quantity = new BigDecimal(item.getString(1));
            priceQuantities.add(new DepthData.PriceQuantity(price, quantity));
        }
        
        return priceQuantities;
    }
} 