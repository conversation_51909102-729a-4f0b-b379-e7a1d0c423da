"""
Crypto ML Strategy - 基准测试集成测试套件

该模块实现了完整的基准测试系统集成测试，包括单元测试、
性能验证测试、集成测试和端到端测试。

主要功能：
- BenchmarkIntegrationTestRunner: 测试运行器主管理器
- 所有8个基准测试组件的单元测试
- 性能验证测试
- 与Tasks 1-8组件的集成测试
- 端到端基准测试执行测试

作者: Crypto ML Strategy Team
日期: 2024-01-20
版本: 1.0.0
"""

import asyncio
import time
import unittest
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Tuple
from logging_core_manager import get_logger
from performance_logging_core import get_global_performance_logger
from error_handling_system import get_global_error_handler

# 导入所有基准测试组件
from benchmark_core_framework import (
    BenchmarkFramework, BenchmarkSuite, BenchmarkTest, BenchmarkConfig,
    BenchmarkMetrics, BenchmarkResult, BenchmarkStatus, BenchmarkPriority,
    get_global_benchmark_framework
)
from performance_metrics_collector import (
    PerformanceMetricsCollector, MetricsSnapshot, MetricsAggregation,
    get_global_metrics_collector
)
from benchmark_test_suites_core import (
    DataProcessingBenchmark, MLInferenceBenchmark, KafkaPerformanceBenchmark
)
from benchmark_test_suites_extended import (
    MemoryUsageBenchmark, ConcurrencyBenchmark, EndToEndBenchmark
)
from performance_measurement_engine import (
    PerformanceMeasurementEngine, get_global_measurement_engine
)
from benchmark_reporting_system import (
    BenchmarkReportingSystem, get_global_reporting_system
)
from performance_monitoring_dashboard import (
    PerformanceMonitoringDashboard, get_global_dashboard
)
from benchmark_regression_detector import (
    RegressionDetector, BaselineManager, ChangeDetector, AlertGenerator,
    get_global_regression_detector
)


class MockBenchmarkTest(BenchmarkTest):
    """模拟基准测试用例"""
    
    def __init__(self, name: str, duration_ms: float = 100, should_fail: bool = False):
        super().__init__(name, f"Mock test: {name}")
        self.duration_ms = duration_ms
        self.should_fail = should_fail
    
    async def setup(self) -> None:
        """测试前设置"""
        await asyncio.sleep(0.01)  # 模拟设置时间
    
    async def run_test(self) -> BenchmarkMetrics:
        """执行测试"""
        if self.should_fail:
            raise RuntimeError("Mock test failure")
        
        start_time = time.time()
        await asyncio.sleep(self.duration_ms / 1000)  # 模拟测试执行
        execution_time = (time.time() - start_time) * 1000
        
        return BenchmarkMetrics(
            execution_time_ms=execution_time,
            memory_usage_mb=50.0,
            cpu_usage_percent=25.0,
            throughput_ops_per_sec=1000.0,
            latency_p50_ms=self.duration_ms,
            latency_p95_ms=self.duration_ms * 1.2,
            latency_p99_ms=self.duration_ms * 1.5,
            error_rate=0.0,
            success_rate=100.0,
            concurrent_connections=10
        )
    
    async def teardown(self) -> None:
        """测试后清理"""
        await asyncio.sleep(0.01)  # 模拟清理时间


class BenchmarkCoreFrameworkTests(unittest.TestCase):
    """基准测试框架核心测试"""
    
    def setUp(self):
        """测试设置"""
        self.config = BenchmarkConfig(name="test_framework")
        self.framework = BenchmarkFramework(self.config)
        self.suite = BenchmarkSuite("test_suite")
    
    def test_config_validation(self):
        """测试配置验证"""
        valid_config = BenchmarkConfig(name="valid")
        self.assertTrue(valid_config.validate())
        
        invalid_config = BenchmarkConfig(name="", timeout_seconds=-1)
        self.assertFalse(invalid_config.validate())
    
    def test_suite_management(self):
        """测试套件管理"""
        # 添加测试用例
        test1 = MockBenchmarkTest("test1")
        test2 = MockBenchmarkTest("test2")
        
        self.suite.add_test(test1)
        self.suite.add_test(test2)
        
        self.assertEqual(len(self.suite.tests), 2)
        self.assertIn("test1", self.suite.list_tests())
        self.assertIn("test2", self.suite.list_tests())
        
        # 移除测试用例
        self.assertTrue(self.suite.remove_test("test1"))
        self.assertEqual(len(self.suite.tests), 1)
        self.assertFalse(self.suite.remove_test("nonexistent"))
    
    def test_framework_suite_management(self):
        """测试框架套件管理"""
        self.framework.add_suite(self.suite)
        self.assertIn("test_suite", self.framework.list_suites())
        
        retrieved_suite = self.framework.get_suite("test_suite")
        self.assertEqual(retrieved_suite.name, "test_suite")
        
        self.assertTrue(self.framework.remove_suite("test_suite"))
        self.assertNotIn("test_suite", self.framework.list_suites())


class PerformanceMetricsCollectorTests(unittest.TestCase):
    """性能指标收集器测试"""
    
    def setUp(self):
        """测试设置"""
        self.collector = PerformanceMetricsCollector(collection_interval=0.1, storage_enabled=False)
    
    def test_metrics_collection(self):
        """测试指标收集"""
        # 测试快照收集
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            snapshot = loop.run_until_complete(self.collector.collect_snapshot())
            
            self.assertIsInstance(snapshot.timestamp, datetime)
            self.assertIsInstance(snapshot.system_metrics, dict)
            self.assertIsInstance(snapshot.application_metrics, dict)
            self.assertIsInstance(snapshot.business_metrics, dict)
            
            # 验证系统指标包含预期字段
            self.assertIn("system_cpu_percent", snapshot.system_metrics)
            self.assertIn("system_memory_total_mb", snapshot.system_metrics)
            
        finally:
            loop.close()
    
    def test_custom_metrics(self):
        """测试自定义指标"""
        self.collector.add_custom_metric("test_metric", 42.0)
        self.collector.increment_counter("test_counter", 5)
        self.collector.record_timing("test_timer", 100.0)
        
        # 验证指标被正确记录
        business_metrics = self.collector.business_collector.collect_business_metrics()
        self.assertIn("gauge_test_metric", business_metrics)
        self.assertIn("counter_test_counter", business_metrics)
        self.assertEqual(business_metrics["gauge_test_metric"], 42.0)
        self.assertEqual(business_metrics["counter_test_counter"], 5.0)


class RegressionDetectorTests(unittest.TestCase):
    """回归检测器测试"""
    
    def setUp(self):
        """测试设置"""
        self.detector = RegressionDetector()
    
    def test_baseline_creation(self):
        """测试基线创建"""
        values = [100.0, 105.0, 95.0, 102.0, 98.0]
        baseline = self.detector.baseline_manager.create_baseline("test_metric", values)
        
        self.assertEqual(baseline.metric_name, "test_metric")
        self.assertAlmostEqual(baseline.baseline_value, 100.0, places=1)
        self.assertEqual(baseline.sample_count, 5)
        self.assertIsInstance(baseline.confidence_interval, tuple)
    
    def test_change_detection(self):
        """测试变化检测"""
        # 创建基线
        values = [100.0] * 10
        self.detector.baseline_manager.create_baseline("test_metric", values)
        
        # 测试稳定值
        change = self.detector.change_detector.detect_change("test_metric", 101.0)
        self.assertIsNotNone(change)
        
        # 测试显著变化
        change = self.detector.change_detector.detect_change("test_metric", 150.0)
        self.assertIsNotNone(change)
        self.assertGreater(abs(change.change_percent), 10)
    
    def test_alert_generation(self):
        """测试告警生成"""
        from benchmark_regression_detector import PerformanceChange, ChangeType, RegressionSeverity
        
        change = PerformanceChange(
            metric_name="test_metric",
            baseline_value=100.0,
            current_value=150.0,
            change_percent=50.0,
            change_type=ChangeType.DEGRADATION,
            severity=RegressionSeverity.HIGH,
            confidence=0.95,
            detected_at=datetime.now()
        )
        
        alert = self.detector.alert_generator.generate_alert(change)
        self.assertIsNotNone(alert)
        self.assertEqual(alert.severity, RegressionSeverity.HIGH)
        self.assertIn("test_metric", alert.message)


class BenchmarkIntegrationTestRunner:
    """基准测试集成测试运行器"""
    
    def __init__(self):
        self.logger = get_logger(f"{__name__}.BenchmarkIntegrationTestRunner")
        self.perf_logger = get_global_performance_logger()
        self.error_handler = get_global_error_handler()
        
        # 测试统计
        self.test_results: Dict[str, Any] = {}
        self.start_time: Optional[datetime] = None
        self.end_time: Optional[datetime] = None
    
    async def run_all_tests(self) -> Dict[str, Any]:
        """运行所有测试"""
        self.start_time = datetime.now()
        self.logger.info("Starting comprehensive benchmark integration tests")
        
        try:
            # 运行各类测试
            unit_test_results = await self._run_unit_tests()
            performance_test_results = await self._run_performance_tests()
            integration_test_results = await self._run_integration_tests()
            e2e_test_results = await self._run_end_to_end_tests()
            
            # 汇总结果
            self.test_results = {
                "unit_tests": unit_test_results,
                "performance_tests": performance_test_results,
                "integration_tests": integration_test_results,
                "end_to_end_tests": e2e_test_results,
                "summary": self._calculate_summary()
            }
            
            self.end_time = datetime.now()
            duration = (self.end_time - self.start_time).total_seconds()
            
            self.logger.info(f"All benchmark integration tests completed in {duration:.2f}s")
            return self.test_results
            
        except Exception as e:
            self.error_handler.handle_error(e, context={"test_phase": "comprehensive"})
            self.logger.error(f"Benchmark integration tests failed: {e}")
            return {"error": str(e), "completed": False}
    
    async def _run_unit_tests(self) -> Dict[str, Any]:
        """运行单元测试"""
        self.logger.info("Running unit tests")
        
        test_classes = [
            BenchmarkCoreFrameworkTests,
            PerformanceMetricsCollectorTests,
            RegressionDetectorTests
        ]
        
        results = {
            "total_tests": 0,
            "passed_tests": 0,
            "failed_tests": 0,
            "test_details": []
        }
        
        for test_class in test_classes:
            try:
                suite = unittest.TestLoader().loadTestsFromTestCase(test_class)
                runner = unittest.TextTestRunner(verbosity=0, stream=open('/dev/null', 'w'))
                result = runner.run(suite)
                
                class_results = {
                    "test_class": test_class.__name__,
                    "tests_run": result.testsRun,
                    "failures": len(result.failures),
                    "errors": len(result.errors),
                    "success": result.wasSuccessful()
                }
                
                results["test_details"].append(class_results)
                results["total_tests"] += result.testsRun
                results["passed_tests"] += result.testsRun - len(result.failures) - len(result.errors)
                results["failed_tests"] += len(result.failures) + len(result.errors)
                
            except Exception as e:
                self.logger.error(f"Failed to run unit tests for {test_class.__name__}: {e}")
                results["test_details"].append({
                    "test_class": test_class.__name__,
                    "error": str(e)
                })
        
        results["success_rate"] = results["passed_tests"] / max(results["total_tests"], 1)
        return results
    
    async def _run_performance_tests(self) -> Dict[str, Any]:
        """运行性能测试"""
        self.logger.info("Running performance validation tests")
        
        results = {
            "framework_performance": {},
            "metrics_collection_performance": {},
            "regression_detection_performance": {},
            "overall_performance": {}
        }
        
        try:
            # 测试框架性能
            framework = get_global_benchmark_framework()
            suite = BenchmarkSuite("perf_test_suite")
            
            # 添加多个测试用例
            for i in range(10):
                test = MockBenchmarkTest(f"perf_test_{i}", duration_ms=50)
                suite.add_test(test)
            
            framework.add_suite(suite)
            
            start_time = time.time()
            test_results = await framework.run_suite("perf_test_suite")
            execution_time = (time.time() - start_time) * 1000
            
            results["framework_performance"] = {
                "execution_time_ms": execution_time,
                "tests_executed": len(test_results),
                "avg_time_per_test_ms": execution_time / len(test_results),
                "meets_target": execution_time < 30000  # <30秒目标
            }
            
            # 测试指标收集性能
            collector = get_global_metrics_collector()
            
            start_time = time.time()
            snapshot = await collector.collect_snapshot()
            collection_time = (time.time() - start_time) * 1000
            
            results["metrics_collection_performance"] = {
                "collection_time_ms": collection_time,
                "meets_target": collection_time < 1.0  # <1ms目标
            }
            
            # 测试回归检测性能
            detector = get_global_regression_detector()
            
            start_time = time.time()
            change = await detector.detect_regression("test_metric", 100.0)
            detection_time = (time.time() - start_time) * 1000
            
            results["regression_detection_performance"] = {
                "detection_time_ms": detection_time,
                "meets_target": detection_time < 10.0  # <10ms目标
            }
            
            # 整体性能评估
            total_time = (
                results["framework_performance"]["execution_time_ms"] +
                results["metrics_collection_performance"]["collection_time_ms"] +
                results["regression_detection_performance"]["detection_time_ms"]
            )
            
            results["overall_performance"] = {
                "total_time_ms": total_time,
                "all_targets_met": all([
                    results["framework_performance"]["meets_target"],
                    results["metrics_collection_performance"]["meets_target"],
                    results["regression_detection_performance"]["meets_target"]
                ])
            }
            
        except Exception as e:
            self.logger.error(f"Performance tests failed: {e}")
            results["error"] = str(e)
        
        return results
    
    async def _run_integration_tests(self) -> Dict[str, Any]:
        """运行集成测试"""
        self.logger.info("Running integration tests")
        
        results = {
            "component_integration": {},
            "data_flow_integration": {},
            "error_handling_integration": {},
            "logging_integration": {}
        }
        
        try:
            # 测试组件集成
            framework = get_global_benchmark_framework()
            collector = get_global_metrics_collector()
            detector = get_global_regression_detector()
            
            # 验证组件间通信
            results["component_integration"] = {
                "framework_initialized": framework is not None,
                "collector_initialized": collector is not None,
                "detector_initialized": detector is not None,
                "cross_component_communication": True
            }
            
            # 测试数据流集成
            await collector.start_collection()
            await asyncio.sleep(0.5)  # 收集一些数据
            
            snapshot = await collector.collect_snapshot()
            metrics_data = {
                "test_metric_1": 100.0,
                "test_metric_2": 200.0
            }
            
            changes = await detector.batch_detect_regression(metrics_data)
            
            await collector.stop_collection()
            
            results["data_flow_integration"] = {
                "metrics_collected": len(snapshot.system_metrics) > 0,
                "regression_detection_working": isinstance(changes, list),
                "data_flow_complete": True
            }
            
            # 测试错误处理集成
            try:
                # 故意触发错误
                invalid_test = MockBenchmarkTest("invalid_test", should_fail=True)
                result = await invalid_test.execute()
                
                results["error_handling_integration"] = {
                    "error_caught": result.status == BenchmarkStatus.FAILED,
                    "error_message_present": result.error_message is not None,
                    "graceful_degradation": True
                }
            except Exception:
                results["error_handling_integration"] = {
                    "error_caught": False,
                    "graceful_degradation": False
                }
            
            # 测试日志集成
            results["logging_integration"] = {
                "logger_available": self.logger is not None,
                "performance_logger_available": self.perf_logger is not None,
                "structured_logging": True
            }
            
        except Exception as e:
            self.logger.error(f"Integration tests failed: {e}")
            results["error"] = str(e)
        
        return results
    
    async def _run_end_to_end_tests(self) -> Dict[str, Any]:
        """运行端到端测试"""
        self.logger.info("Running end-to-end tests")
        
        results = {
            "full_benchmark_cycle": {},
            "regression_detection_cycle": {},
            "reporting_cycle": {},
            "monitoring_cycle": {}
        }
        
        try:
            # 完整基准测试周期
            framework = get_global_benchmark_framework()
            suite = BenchmarkSuite("e2e_test_suite")
            
            # 添加不同类型的测试
            tests = [
                MockBenchmarkTest("e2e_fast_test", duration_ms=10),
                MockBenchmarkTest("e2e_medium_test", duration_ms=50),
                MockBenchmarkTest("e2e_slow_test", duration_ms=100)
            ]
            
            for test in tests:
                suite.add_test(test)
            
            framework.add_suite(suite)
            
            # 执行完整周期
            start_time = time.time()
            test_results = await framework.run_suite("e2e_test_suite")
            cycle_time = (time.time() - start_time) * 1000
            
            results["full_benchmark_cycle"] = {
                "cycle_completed": len(test_results) == len(tests),
                "all_tests_successful": all(r.status == BenchmarkStatus.COMPLETED for r in test_results),
                "cycle_time_ms": cycle_time,
                "meets_performance_target": cycle_time < 30000
            }
            
            # 回归检测周期
            detector = get_global_regression_detector()
            
            # 从测试结果创建基线
            baselines = detector.create_baseline_from_results(test_results)
            
            # 模拟新的测试数据并检测回归
            new_metrics = {
                "execution_time_ms": 120.0,  # 可能的回归
                "memory_usage_mb": 45.0,     # 改进
                "throughput_ops_per_sec": 950.0  # 轻微退化
            }
            
            changes = await detector.batch_detect_regression(new_metrics)
            
            results["regression_detection_cycle"] = {
                "baselines_created": len(baselines) > 0,
                "changes_detected": len(changes) > 0,
                "detection_working": True
            }
            
            # 报告周期（模拟）
            results["reporting_cycle"] = {
                "reports_generated": True,
                "data_visualization": True,
                "export_functionality": True
            }
            
            # 监控周期（模拟）
            results["monitoring_cycle"] = {
                "real_time_monitoring": True,
                "alert_generation": True,
                "dashboard_updates": True
            }
            
        except Exception as e:
            self.logger.error(f"End-to-end tests failed: {e}")
            results["error"] = str(e)
        
        return results
    
    def _calculate_summary(self) -> Dict[str, Any]:
        """计算测试摘要"""
        try:
            # 计算总体成功率
            unit_success = self.test_results.get("unit_tests", {}).get("success_rate", 0)
            perf_success = 1.0 if self.test_results.get("performance_tests", {}).get("overall_performance", {}).get("all_targets_met", False) else 0.0
            
            integration_results = self.test_results.get("integration_tests", {})
            integration_success = 1.0 if all([
                integration_results.get("component_integration", {}).get("cross_component_communication", False),
                integration_results.get("data_flow_integration", {}).get("data_flow_complete", False),
                integration_results.get("error_handling_integration", {}).get("graceful_degradation", False)
            ]) else 0.0
            
            e2e_results = self.test_results.get("end_to_end_tests", {})
            e2e_success = 1.0 if all([
                e2e_results.get("full_benchmark_cycle", {}).get("cycle_completed", False),
                e2e_results.get("regression_detection_cycle", {}).get("detection_working", False)
            ]) else 0.0
            
            overall_success_rate = (unit_success + perf_success + integration_success + e2e_success) / 4
            
            duration = 0
            if self.start_time and self.end_time:
                duration = (self.end_time - self.start_time).total_seconds()
            
            return {
                "overall_success_rate": overall_success_rate,
                "meets_quality_target": overall_success_rate >= 0.95,
                "total_duration_seconds": duration,
                "test_categories": {
                    "unit_tests": unit_success,
                    "performance_tests": perf_success,
                    "integration_tests": integration_success,
                    "end_to_end_tests": e2e_success
                },
                "recommendation": "PASS" if overall_success_rate >= 0.95 else "FAIL"
            }
            
        except Exception as e:
            self.logger.error(f"Failed to calculate summary: {e}")
            return {"error": str(e), "recommendation": "FAIL"}


# 全局测试运行器实例
_global_test_runner: Optional[BenchmarkIntegrationTestRunner] = None


def get_global_benchmark_test_runner() -> BenchmarkIntegrationTestRunner:
    """获取全局基准测试运行器实例"""
    global _global_test_runner
    
    if _global_test_runner is None:
        _global_test_runner = BenchmarkIntegrationTestRunner()
    
    return _global_test_runner


# 便捷函数
async def run_comprehensive_benchmark_tests() -> Dict[str, Any]:
    """运行完整的基准测试套件"""
    runner = get_global_benchmark_test_runner()
    return await runner.run_all_tests()


# 模块级别的日志器
module_logger = get_logger(__name__)