package com.crypto.trading.trade.service.risk.impl;

import com.crypto.trading.common.model.signal.TradeSignal;
import com.crypto.trading.trade.model.entity.order.OrderEntity;
import com.crypto.trading.trade.model.entity.risk.RiskControlLogEntity;
import com.crypto.trading.trade.repository.mapper.order.OrderMapper;
import com.crypto.trading.trade.repository.mapper.risk.RiskControlLogMapper;
import com.crypto.trading.trade.service.risk.RiskControlService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 风险控制服务实现类
 */
@Service
public class RiskControlServiceImpl implements RiskControlService {
    
    private static final Logger log = LoggerFactory.getLogger(RiskControlServiceImpl.class);
    
    /**
     * 交易频率计数器，格式: symbol -> (timestamp -> count)
     */
    private final Map<String, Map<Long, AtomicInteger>> tradeFrequencyCounter = new ConcurrentHashMap<>();
    
    @Autowired
    private OrderMapper orderMapper;
    
    @Autowired
    private RiskControlLogMapper riskControlLogMapper;
    
    @Value("${risk.trade.max-amount:10000}")
    private BigDecimal maxTradeAmount;
    
    @Value("${risk.trade.max-frequency:10}")
    private int maxTradeFrequency;
    
    @Value("${risk.trade.frequency-window-ms:60000}")
    private long frequencyWindowMs;
    
    @Value("${risk.trade.enable:true}")
    private boolean riskControlEnabled;

    @Override
    public boolean checkSignal(TradeSignal signal) {
        log.info("检查交易信号风险: {}", signal);
        
        if (!riskControlEnabled) {
            log.info("风控检查已禁用，跳过检查");
            return true;
        }
        
        // 执行各种风控检查
        boolean frequencyCheck = checkTradeFrequency(signal.getSymbol(), frequencyWindowMs);
        if (!frequencyCheck) {
            logRiskCheck(
                    signal.getSignalId(),
                    null,
                    "FREQUENCY",
                    "交易频率限制",
                    "FREQUENCY",
                    "MEDIUM",
                    true,
                    "拒绝交易",
                    "交易频率超过限制: " + maxTradeFrequency + "/" + (frequencyWindowMs / 1000) + "秒"
            );
            return false;
        }
        
        // 计算交易金额
        BigDecimal amount;
        if (signal.getPrice() != null && signal.getPrice().compareTo(BigDecimal.ZERO) > 0) {
            amount = signal.getQuantity().multiply(signal.getPrice());
        } else {
            // 市价单，使用当前价格估算（实际项目中应从市场数据服务获取）
            // 这里简化处理，使用固定值
            amount = signal.getQuantity().multiply(new BigDecimal("1000"));
        }
        
        boolean amountCheck = checkAmountRisk(signal.getSymbol(), amount);
        if (!amountCheck) {
            logRiskCheck(
                    signal.getSignalId(),
                    null,
                    "AMOUNT",
                    "交易金额限制",
                    "AMOUNT",
                    "HIGH",
                    true,
                    "拒绝交易",
                    "交易金额超过限制: " + amount + " > " + maxTradeAmount
            );
            return false;
        }
        
        // 记录通过风控检查的日志
        logRiskCheck(
                signal.getSignalId(),
                null,
                "ALL_CHECKS",
                "全部风控检查",
                "GENERAL",
                "LOW",
                false,
                "允许交易",
                "通过所有风控检查"
        );
        
        log.info("交易信号通过风控检查");
        return true;
    }

    @Override
    public boolean checkOrder(OrderEntity order) {
        log.info("检查订单风险: orderId={}", order.getOrderId());
        
        if (!riskControlEnabled) {
            log.info("风控检查已禁用，跳过检查");
            return true;
        }
        
        // 计算交易金额
        BigDecimal amount;
        if (order.getPrice() != null && order.getPrice() > 0) {
            amount = BigDecimal.valueOf(order.getQuantity()).multiply(BigDecimal.valueOf(order.getPrice()));
        } else {
            // 市价单，使用已执行价格
            if (order.getExecutedPrice() != null && order.getExecutedPrice() > 0) {
                amount = BigDecimal.valueOf(order.getQuantity()).multiply(BigDecimal.valueOf(order.getExecutedPrice()));
            } else {
                // 无法确定价格，使用固定值
                amount = BigDecimal.valueOf(order.getQuantity()).multiply(new BigDecimal("1000"));
            }
        }
        
        boolean amountCheck = checkAmountRisk(order.getSymbol(), amount);
        if (!amountCheck) {
            logRiskCheck(
                    order.getStrategyId(), // 使用strategyId替代signalId
                    order.getOrderId(),
                    "AMOUNT",
                    "交易金额限制",
                    "AMOUNT",
                    "HIGH",
                    true,
                    "标记风险",
                    "交易金额超过限制: " + amount + " > " + maxTradeAmount
            );
            return false;
        }
        
        // 记录通过风控检查的日志
        logRiskCheck(
                order.getStrategyId(), // 使用strategyId替代signalId
                order.getOrderId(),
                "ALL_CHECKS",
                "全部风控检查",
                "GENERAL",
                "LOW",
                false,
                "允许交易",
                "通过所有风控检查"
        );
        
        log.info("订单通过风控检查");
        return true;
    }

    @Override
    public boolean checkAccountRisk(String accountId) {
        // 实际项目中应检查账户资金、杠杆、保证金等
        // 这里简化实现，默认通过
        return true;
    }

    @Override
    public boolean checkTradeFrequency(String symbol, long timeWindowMs) {
        log.info("检查交易频率: symbol={}, timeWindow={}ms", symbol, timeWindowMs);
        
        if (!riskControlEnabled) {
            return true;
        }
        
        long currentTimeWindow = System.currentTimeMillis() / timeWindowMs;
        
        // 获取或创建symbol的计数器
        Map<Long, AtomicInteger> windowCounters = tradeFrequencyCounter.computeIfAbsent(
                symbol, k -> new ConcurrentHashMap<>());
        
        // 获取或创建当前时间窗口的计数器
        AtomicInteger counter = windowCounters.computeIfAbsent(
                currentTimeWindow, k -> new AtomicInteger(0));
        
        // 增加计数并检查是否超过限制
        int count = counter.incrementAndGet();
        boolean result = count <= maxTradeFrequency;
        
        // 清理过期的时间窗口（简单实现，实际项目中可以使用定时任务）
        windowCounters.keySet().removeIf(window -> window < currentTimeWindow - 1);
        
        log.info("交易频率检查结果: count={}, limit={}, pass={}", count, maxTradeFrequency, result);
        return result;
    }

    @Override
    public boolean checkAmountRisk(String symbol, BigDecimal amount) {
        log.info("检查交易金额风险: symbol={}, amount={}", symbol, amount);
        
        if (!riskControlEnabled) {
            return true;
        }
        
        boolean result = amount.compareTo(maxTradeAmount) <= 0;
        log.info("交易金额检查结果: amount={}, limit={}, pass={}", amount, maxTradeAmount, result);
        return result;
    }

    @Override
    public RiskControlLogEntity logRiskCheck(
            String signalId,
            String orderId,
            String ruleId,
            String ruleName,
            String riskType,
            String riskLevel,
            boolean triggered,
            String actionTaken,
            String details) {
        
        log.info("记录风控检查: signalId={}, ruleId={}, triggered={}", signalId, ruleId, triggered);
        
        RiskControlLogEntity logEntity = new RiskControlLogEntity.Builder()
                .signalId(signalId)
                .orderId(orderId)
                .ruleId(ruleId)
                .ruleName(ruleName)
                .riskType(riskType)
                .riskLevel(riskLevel)
                .triggered(triggered)
                .actionTaken(actionTaken)
                .details(details)
                .build();
        
        riskControlLogMapper.insert(logEntity);
        return logEntity;
    }

    @Override
    public List<RiskControlLogEntity> getRiskLogs(String signalId) {
        return riskControlLogMapper.findBySignalId(signalId);
    }

    @Override
    public Map<String, Integer> getRuleTriggeredStats(long startTime, long endTime) {
        List<RiskControlLogEntity> logs = riskControlLogMapper.findByTimeRange(startTime, endTime, 1000);
        
        Map<String, Integer> stats = new HashMap<>();
        for (RiskControlLogEntity log : logs) {
            if (log.getTriggered()) {
                String ruleId = log.getRuleId();
                stats.put(ruleId, stats.getOrDefault(ruleId, 0) + 1);
            }
        }
        
        return stats;
    }
}