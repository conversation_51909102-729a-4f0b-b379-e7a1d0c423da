<?xml version="1.0" encoding="ISO-8859-1"?>
<!--
Licensed to the Apache Software Foundation (ASF) under one
or more contributor license agreements.  See the NOTICE file
distributed with this work for additional information
regarding copyright ownership.  The ASF licenses this file
to you under the Apache License, Version 2.0 (the
"License"); you may not use this file except in compliance
with the License.  You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on an
"AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, either express or implied.  See the License for the
specific language governing permissions and limitations
under the License.
-->
<rss version="0.91">
  <channel>
    <title>io.github.binance:binance-futures-connector-java - Checkstyle report</title>
    <link>https://github.com/binance/binance-futures-connector-java</link>
    <description>io.github.binance:binance-futures-connector-java - Checkstyle report</description>
    <language>en-us</language>
    <copyright>&#169;2025</copyright>
    <item>
      <title>File: 0,
             Errors: 0,
             Warnings: 0,
             Infos: 0
      </title>
            <link>https://github.com/binance/binance-futures-connector-java/checkstyle.html</link>
      <description>
        <p>Click <a href="https://github.com/binance/binance-futures-connector-java/checkstyle.html">here</a> for the full Checkstyle report.</p>

        <table summary="Files" boder="1">
          <thead>
            <tr>
              <th>Files</th>
              <th style="width:30px;"><abbr title="Info">I</abbr></th>
              <th style="width:30px;"><abbr title="Warning">W</abbr></th>
              <th style="width:30px;"><abbr title="Error">E</abbr></th>
            </tr>
          </thead>
          <tbody>
                      </tbody>
        </table>
        
      </description>
    </item>
  </channel>
</rss>

