<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>PortfolioMargin.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">io.github.binance:binance-futures-connector-java</a> &gt; <a href="index.source.html" class="el_package">com.binance.connector.futures.client.impl.futures</a> &gt; <span class="el_source">PortfolioMargin.java</span></div><h1>PortfolioMargin.java</h1><pre class="source lang-java linenums">package com.binance.connector.futures.client.impl.futures;

import com.binance.connector.futures.client.enums.HttpMethod;
import com.binance.connector.futures.client.utils.ProxyAuth;
import com.binance.connector.futures.client.utils.RequestHandler;
import com.binance.connector.futures.client.utils.ParameterChecker;
import java.util.LinkedHashMap;

/**
 * &lt;h2&gt;Portfolio Margin Endpoints&lt;/h2&gt;
 * Response will be returned in &lt;i&gt;String format&lt;/i&gt;.
 */
public abstract class PortfolioMargin {
    private String productUrl;
    private RequestHandler requestHandler;
    private boolean showLimitUsage;

<span class="fc" id="L18">    public PortfolioMargin(String productUrl, String apiKey, String secretKey, boolean showLimitUsage, ProxyAuth proxy) {</span>
<span class="fc" id="L19">        this.productUrl = productUrl;</span>
<span class="fc" id="L20">        this.requestHandler = new RequestHandler(apiKey, secretKey, proxy);</span>
<span class="fc" id="L21">        this.showLimitUsage = showLimitUsage;</span>
<span class="fc" id="L22">    }</span>

    public String getProductUrl() {
<span class="nc" id="L25">        return this.productUrl;</span>
    }

    public RequestHandler getRequestHandler() {
<span class="nc" id="L29">        return this.requestHandler;</span>
    }

    public boolean getShowLimitUsage() {
<span class="nc" id="L33">        return this.showLimitUsage;</span>
    }

    public void setProductUrl(String productUrl) {
<span class="nc" id="L37">        this.productUrl = productUrl;</span>
<span class="nc" id="L38">    }</span>

    public void setRequestHandler(String apiKey, String secretKey, ProxyAuth proxy) {
<span class="nc" id="L41">        this.requestHandler = new RequestHandler(apiKey, secretKey, proxy);</span>
<span class="nc" id="L42">    }</span>

    public void setShowLimitUsage(boolean showLimitUsage) {
<span class="nc" id="L45">        this.showLimitUsage = showLimitUsage;</span>
<span class="nc" id="L46">    }</span>

<span class="fc" id="L48">    private final String PORTFOLIO_MARGIN_EXCHANGE_INFO = &quot;/v1/pmExchangeInfo&quot;;</span>
    public String portfolioMarginExchangeInfo(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L50">        return requestHandler.sendSignedRequest(productUrl, PORTFOLIO_MARGIN_EXCHANGE_INFO, parameters, HttpMethod.GET, showLimitUsage);</span>
    }

<span class="fc" id="L53">    private final String PORTFOLIO_MARGIN_ACCOUNT_INFO = &quot;/v1/pmAccountInfo&quot;;</span>
    /**
     * Get Portfolio Margin current account information.
     * GET /v1/pmAccountInfo
     * &lt;br&gt;
     * @param
     * parameters LinkedHashedMap of String,Object pair
     *            where String is the name of the parameter and Object is the value of the parameter
     * &lt;br&gt;&lt;br&gt;
     * asset -- mandatory/string &lt;br&gt;
     * recvWindow -- optional/long &lt;br&gt;
     * @return String
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/coin-margined-futures/portfolio-margin-endpoints/Classic-Portfolio-Margin-Account-Information#api-description&quot;&gt;
     *     https://developers.binance.com/docs/derivatives/coin-margined-futures/portfolio-margin-endpoints/Classic-Portfolio-Margin-Account-Information#api-description&lt;/a&gt;
     */
    public String portfolioMarginAccountInfo(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L69">        ParameterChecker.checkParameter(parameters, &quot;asset&quot;, String.class);</span>
<span class="fc" id="L70">        return requestHandler.sendSignedRequest(productUrl, PORTFOLIO_MARGIN_ACCOUNT_INFO, parameters, HttpMethod.GET, showLimitUsage);</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.7.************</span></div></body></html>