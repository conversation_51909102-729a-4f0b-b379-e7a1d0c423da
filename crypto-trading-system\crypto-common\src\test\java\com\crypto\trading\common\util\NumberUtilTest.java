package com.crypto.trading.common.util;

import org.junit.jupiter.api.Test;
import java.math.BigDecimal;
import java.math.RoundingMode;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 数值工具类测试
 * <p>
 * 测试数值处理相关的工具方法
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
class NumberUtilTest {

    /**
     * 测试设置精度
     */
    @Test
    void testSetScale() {
        BigDecimal value = new BigDecimal("123.4567");
        
        // 测试默认舍入模式（HALF_UP）
        assertEquals("123.46", NumberUtil.setScale(value, 2).toPlainString());
        assertEquals("123.457", NumberUtil.setScale(value, 3).toPlainString());
        assertEquals("123", NumberUtil.setScale(value, 0).toPlainString());
        
        // 测试指定舍入模式
        assertEquals("123.45", NumberUtil.setScale(value, 2, RoundingMode.DOWN).toPlainString());
        assertEquals("123.46", NumberUtil.setScale(value, 2, RoundingMode.UP).toPlainString());
        
        // 测试null值
        assertNull(NumberUtil.setScale(null, 2));
    }

    /**
     * 测试格式化
     */
    @Test
    void testFormat() {
        BigDecimal value = new BigDecimal("1234567.89");
        
        // 测试指定小数位数格式化
        assertEquals("1234567.89", NumberUtil.format(value, 2));
        assertEquals("1234567.890", NumberUtil.format(value, 3));
        assertEquals("1234568", NumberUtil.format(value, 0));
        
        // 测试指定格式模式
        assertEquals("1,234,567.89", NumberUtil.format(value, "#,##0.00"));
        assertEquals("1234567.89000", NumberUtil.format(value, "0.00000"));
        
        // 测试带千分位格式化
        assertEquals("1,234,567.89", NumberUtil.formatWithGrouping(value, 2));
        
        // 测试百分比格式化
        BigDecimal percentValue = new BigDecimal("0.1234");
        assertEquals("12.34%", NumberUtil.formatPercent(percentValue, 2));
        
        // 测试null值
        assertNull(NumberUtil.format((BigDecimal)null, 2));
        assertNull(NumberUtil.format((BigDecimal)null, "#,##0.00"));
        assertNull(NumberUtil.formatWithGrouping(null, 2));
        assertNull(NumberUtil.formatPercent(null, 2));
    }

    /**
     * 测试舍入方法
     */
    @Test
    void testRounding() {
        BigDecimal value = new BigDecimal("123.456");
        
        // 测试四舍五入
        assertEquals("123.46", NumberUtil.round(value, 2).toPlainString());
        
        // 测试向上取整
        assertEquals("123.46", NumberUtil.ceil(value, 2).toPlainString());
        
        // 测试向下取整
        assertEquals("123.45", NumberUtil.floor(value, 2).toPlainString());
        
        // 测试截断
        assertEquals("123.45", NumberUtil.truncate(value, 2).toPlainString());
        
        // 测试null值
        assertNull(NumberUtil.round(null, 2));
        assertNull(NumberUtil.ceil(null, 2));
        assertNull(NumberUtil.floor(null, 2));
        assertNull(NumberUtil.truncate(null, 2));
    }

    /**
     * 测试数值比较
     */
    @Test
    void testComparison() {
        // 测试double比较
        assertTrue(NumberUtil.equals(1.0, 1.0));
        assertTrue(NumberUtil.equals(1.0, 1.0 + 1e-15));
        assertFalse(NumberUtil.equals(1.0, 1.1));
        
        // 测试BigDecimal比较
        BigDecimal value1 = new BigDecimal("1.00");
        BigDecimal value2 = new BigDecimal("1.0");
        BigDecimal value3 = new BigDecimal("1.01");
        
        assertTrue(NumberUtil.equals(value1, value2, 2));
        assertFalse(NumberUtil.equals(value1, value3, 2));
        
        // 测试null值
        assertTrue(NumberUtil.equals(null, null, 2));
        assertFalse(NumberUtil.equals(value1, null, 2));
        assertFalse(NumberUtil.equals(null, value1, 2));
        
        // 测试比较大小
        assertEquals(0, NumberUtil.compare(value1, value2, 2));
        assertEquals(-1, NumberUtil.compare(value1, value3, 2));
        assertEquals(1, NumberUtil.compare(value3, value1, 2));
        
        // 测试null值比较
        assertEquals(0, NumberUtil.compare(null, null, 2));
        assertEquals(-1, NumberUtil.compare(null, value1, 2));
        assertEquals(1, NumberUtil.compare(value1, null, 2));
    }

    /**
     * 测试零值判断
     */
    @Test
    void testIsZero() {
        // 测试double零值
        assertTrue(NumberUtil.isZero(0.0));
        assertTrue(NumberUtil.isZero(1e-15));
        assertFalse(NumberUtil.isZero(0.1));
        
        // 测试BigDecimal零值
        assertTrue(NumberUtil.isZero(BigDecimal.ZERO, 2));
        assertTrue(NumberUtil.isZero(new BigDecimal("0.00"), 2));
        assertFalse(NumberUtil.isZero(new BigDecimal("0.01"), 2));
        
        // 测试null值
        assertFalse(NumberUtil.isZero(null, 2));
    }

    /**
     * 测试获取精度
     */
    @Test
    void testGetScale() {
        assertEquals(0, NumberUtil.getScale(new BigDecimal("123")));
        assertEquals(2, NumberUtil.getScale(new BigDecimal("123.45")));
        assertEquals(2, NumberUtil.getScale(new BigDecimal("123.450").stripTrailingZeros()));
        assertEquals(0, NumberUtil.getScale(null));
    }

    /**
     * 测试涨跌幅计算
     */
    @Test
    void testChangeRate() {
        BigDecimal current = new BigDecimal("110");
        BigDecimal base = new BigDecimal("100");
        
        // 测试涨跌幅计算
        assertEquals("0.10000000", NumberUtil.calcChangeRate(current, base).toPlainString());
        
        // 测试涨跌幅百分比格式化
        assertEquals("10.00%", NumberUtil.calcChangeRatePercent(current, base, 2));
        
        // 测试涨跌额计算
        assertEquals("10", NumberUtil.calcChangeAmount(current, base).toPlainString());
        
        // 测试null值和零值
        assertEquals("0", NumberUtil.calcChangeRate(null, base).toPlainString());
        assertEquals("0", NumberUtil.calcChangeRate(current, null).toPlainString());
        assertEquals("0", NumberUtil.calcChangeRate(current, BigDecimal.ZERO).toPlainString());
    }

    /**
     * 测试价格和数量处理
     */
    @Test
    void testProcessPriceAndQuantity() {
        BigDecimal price = new BigDecimal("123.4567");
        BigDecimal quantity = new BigDecimal("1.23456");
        
        // 测试价格处理（四舍五入）
        assertEquals("123.46", NumberUtil.processPrice(price, 2).toPlainString());
        
        // 测试数量处理（截断）
        assertEquals("1.23", NumberUtil.processQuantity(quantity, 2).toPlainString());
    }

    /**
     * 测试转换为BigDecimal
     */
    @Test
    void testToBigDecimal() {
        // 测试字符串转BigDecimal
        assertEquals("123.45", NumberUtil.toBigDecimal("123.45").toPlainString());
        assertEquals("123.45", NumberUtil.toBigDecimal(" 123.45 ").toPlainString());
        assertNull(NumberUtil.toBigDecimal("abc"));
        assertNull(NumberUtil.toBigDecimal(""));
        assertNull(NumberUtil.toBigDecimal((String)null));
        
        // 测试带默认值的字符串转BigDecimal
        BigDecimal defaultValue = BigDecimal.ZERO;
        assertEquals("123.45", NumberUtil.toBigDecimal("123.45", defaultValue).toPlainString());
        assertEquals("0", NumberUtil.toBigDecimal("abc", defaultValue).toPlainString());
        
        // 测试对象转BigDecimal
        assertEquals("123.45", NumberUtil.toBigDecimal(new BigDecimal("123.45")).toPlainString());
        assertEquals("123", NumberUtil.toBigDecimal(123).toPlainString());
        assertEquals("123.45", NumberUtil.toBigDecimal(123.45).toPlainString());
        assertNull(NumberUtil.toBigDecimal(null));
        
        // 测试带默认值的对象转BigDecimal
        assertEquals("123.45", NumberUtil.toBigDecimal(new BigDecimal("123.45"), defaultValue).toPlainString());
        assertEquals("0", NumberUtil.toBigDecimal(null, defaultValue).toPlainString());
    }
} 