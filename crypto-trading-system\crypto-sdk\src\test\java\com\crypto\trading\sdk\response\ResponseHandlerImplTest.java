package com.crypto.trading.sdk.response;

import com.crypto.trading.sdk.converter.JsonConverter;
import com.crypto.trading.sdk.exception.ApiException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

/**
 * 响应处理器实现测试类
 */
@ExtendWith(MockitoExtension.class)
public class ResponseHandlerImplTest {

    @Mock
    private JsonConverter jsonConverter;

    private ResponseHandlerImpl responseHandler;

    @BeforeEach
    public void setUp() {
        responseHandler = new ResponseHandlerImpl(jsonConverter);
    }

    @Test
    public void testHandleApiResponseSuccess() {
        // 准备测试数据
        String responseBody = "{\"name\":\"Bitcoin\",\"symbol\":\"BTC\",\"price\":\"50000\"}";
        TestDto expectedDto = new TestDto("Bitcoin", "BTC", "50000");
        
        // 模拟JSON转换
        when(jsonConverter.fromJson(eq(responseBody), eq(TestDto.class))).thenReturn(expectedDto);
        
        // 为检查响应错误方法做准备
        Map<String, Object> responseMap = new HashMap<>();
        responseMap.put("code", 0);
        responseMap.put("msg", "success");
        when(jsonConverter.jsonToMap(responseBody)).thenReturn(responseMap);
        
        // 执行处理响应
        TestDto result = responseHandler.handleApiResponse(responseBody, TestDto.class);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(expectedDto.getName(), result.getName());
        assertEquals(expectedDto.getSymbol(), result.getSymbol());
        assertEquals(expectedDto.getPrice(), result.getPrice());
    }

    @Test
    public void testHandleApiResponseError() {
        // 准备测试数据 - 币安错误响应格式
        String errorResponseBody = "{\"code\":-1021,\"msg\":\"Timestamp for this request is outside of the recvWindow.\"}";
        
        // 模拟JSON转换
        Map<String, Object> errorMap = new HashMap<>();
        errorMap.put("code", -1021);
        errorMap.put("msg", "Timestamp for this request is outside of the recvWindow.");
        when(jsonConverter.jsonToMap(errorResponseBody)).thenReturn(errorMap);
        
        // 期望抛出异常
        ApiException exception = assertThrows(ApiException.class, () -> {
            responseHandler.handleApiResponse(errorResponseBody, TestDto.class);
        });
        
        // 验证异常
        assertEquals(-1021, exception.getCode());
        assertEquals("Timestamp for this request is outside of the recvWindow.", exception.getMessage());
    }

    @Test
    public void testHandleApiResponseEmptyBody() {
        // 准备测试数据 - 空响应体
        String emptyBody = "";
        
        // 期望抛出异常
        ApiException exception = assertThrows(ApiException.class, () -> {
            responseHandler.handleApiResponse(emptyBody, TestDto.class);
        });
        
        // 验证异常
        assertEquals(1001, exception.getCode());
        assertEquals("空响应", exception.getMessage());
    }

    @Test
    public void testHandleApiResponseNullBody() {
        // 准备测试数据 - null响应体
        String nullBody = null;
        
        // 期望抛出异常
        ApiException exception = assertThrows(ApiException.class, () -> {
            responseHandler.handleApiResponse(nullBody, TestDto.class);
        });
        
        // 验证异常
        assertEquals(1001, exception.getCode());
        assertEquals("空响应", exception.getMessage());
    }

    @Test
    public void testHandleWebSocketMessage() {
        // 准备测试数据
        String message = "{\"name\":\"Ethereum\",\"symbol\":\"ETH\",\"price\":\"4000\"}";
        TestDto expectedDto = new TestDto("Ethereum", "ETH", "4000");
        
        // 模拟JSON转换
        when(jsonConverter.fromJson(eq(message), eq(TestDto.class))).thenReturn(expectedDto);
        
        // 为检查响应错误方法做准备
        Map<String, Object> responseMap = new HashMap<>();
        responseMap.put("code", 0);
        responseMap.put("msg", "success");
        when(jsonConverter.jsonToMap(message)).thenReturn(responseMap);
        
        // 执行处理WebSocket消息
        TestDto result = responseHandler.handleWebSocketMessage(message, TestDto.class);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(expectedDto.getName(), result.getName());
        assertEquals(expectedDto.getSymbol(), result.getSymbol());
        assertEquals(expectedDto.getPrice(), result.getPrice());
    }

    @Test
    public void testHandleApiResponseAsync() throws InterruptedException {
        // 准备测试数据
        String responseBody = "{\"name\":\"Litecoin\",\"symbol\":\"LTC\",\"price\":\"200\"}";
        TestDto expectedDto = new TestDto("Litecoin", "LTC", "200");
        
        // 模拟JSON转换
        when(jsonConverter.fromJson(eq(responseBody), eq(TestDto.class))).thenReturn(expectedDto);
        
        // 为检查响应错误方法做准备
        Map<String, Object> responseMap = new HashMap<>();
        responseMap.put("code", 0);
        responseMap.put("msg", "success");
        when(jsonConverter.jsonToMap(responseBody)).thenReturn(responseMap);
        
        // 创建结果容器
        TestDto[] result = new TestDto[1];
        
        // 执行异步处理
        responseHandler.handleApiResponseAsync(responseBody, TestDto.class, 
            dto -> result[0] = dto, 
            ex -> fail("不应该抛出异常")
        );
        
        // 等待异步处理完成
        Thread.sleep(500);
        
        // 验证结果
        assertNotNull(result[0]);
        assertEquals(expectedDto.getName(), result[0].getName());
        assertEquals(expectedDto.getSymbol(), result[0].getSymbol());
        assertEquals(expectedDto.getPrice(), result[0].getPrice());
    }

    // 测试用的DTO类
    private static class TestDto {
        private String name;
        private String symbol;
        private String price;
        
        public TestDto() {
        }
        
        public TestDto(String name, String symbol, String price) {
            this.name = name;
            this.symbol = symbol;
            this.price = price;
        }
        
        public String getName() {
            return name;
        }
        
        public void setName(String name) {
            this.name = name;
        }
        
        public String getSymbol() {
            return symbol;
        }
        
        public void setSymbol(String symbol) {
            this.symbol = symbol;
        }
        
        public String getPrice() {
            return price;
        }
        
        public void setPrice(String price) {
            this.price = price;
        }
    }
} 