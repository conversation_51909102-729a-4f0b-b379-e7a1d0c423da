"""
数据缓存管理器

提供多层次缓存支持，包括内存缓存、持久化缓存和分布式缓存。
支持缓存策略、过期管理、性能监控和统计分析。

Author: Crypto ML Strategy Team
Date: 2024-12-19
"""

import asyncio
import json
import pickle
import time
import threading
from typing import Dict, List, Optional, Any, Union, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import logging
from pathlib import Path
import hashlib
import pandas as pd
import numpy as np
from collections import OrderedDict, defaultdict

from data.data_config import CacheConfig, CacheType

logger = logging.getLogger(__name__)


class CacheStrategy(Enum):
    """缓存策略枚举"""
    LRU = "lru"           # 最近最少使用
    LFU = "lfu"           # 最少使用频率
    FIFO = "fifo"         # 先进先出
    TTL = "ttl"           # 基于时间过期
    ADAPTIVE = "adaptive"  # 自适应策略


@dataclass
class CacheEntry:
    """缓存条目"""
    key: str
    value: Any
    created_time: datetime
    last_accessed: datetime
    access_count: int = 0
    ttl: Optional[int] = None
    size: int = 0
    
    @property
    def is_expired(self) -> bool:
        """检查是否过期"""
        if self.ttl is None:
            return False
        return (datetime.now() - self.created_time).total_seconds() > self.ttl
    
    @property
    def age(self) -> float:
        """获取缓存年龄（秒）"""
        return (datetime.now() - self.created_time).total_seconds()


@dataclass
class CacheStats:
    """缓存统计信息"""
    hits: int = 0
    misses: int = 0
    evictions: int = 0
    total_size: int = 0
    entry_count: int = 0
    
    @property
    def hit_rate(self) -> float:
        """命中率"""
        total = self.hits + self.misses
        return self.hits / total if total > 0 else 0.0
    
    @property
    def miss_rate(self) -> float:
        """未命中率"""
        return 1.0 - self.hit_rate


class MemoryCache:
    """内存缓存实现"""
    
    def __init__(self, max_size: int = 1000, strategy: CacheStrategy = CacheStrategy.LRU):
        self.max_size = max_size
        self.strategy = strategy
        self.cache: OrderedDict[str, CacheEntry] = OrderedDict()
        self.stats = CacheStats()
        self.lock = threading.RLock()
        
        logger.debug(f"内存缓存初始化: max_size={max_size}, strategy={strategy.value}")
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        with self.lock:
            if key not in self.cache:
                self.stats.misses += 1
                return None
            
            entry = self.cache[key]
            
            # 检查是否过期
            if entry.is_expired:
                del self.cache[key]
                self.stats.misses += 1
                self.stats.evictions += 1
                return None
            
            # 更新访问信息
            entry.last_accessed = datetime.now()
            entry.access_count += 1
            
            # LRU策略：移动到末尾
            if self.strategy == CacheStrategy.LRU:
                self.cache.move_to_end(key)
            
            self.stats.hits += 1
            return entry.value
    
    def put(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """存储缓存值"""
        with self.lock:
            now = datetime.now()
            
            # 计算值的大小
            try:
                size = len(pickle.dumps(value))
            except:
                size = 1  # 默认大小
            
            # 创建缓存条目
            entry = CacheEntry(
                key=key,
                value=value,
                created_time=now,
                last_accessed=now,
                access_count=0,
                ttl=ttl,
                size=size
            )
            
            # 如果键已存在，更新
            if key in self.cache:
                old_entry = self.cache[key]
                self.stats.total_size -= old_entry.size
            
            self.cache[key] = entry
            self.stats.total_size += size
            self.stats.entry_count = len(self.cache)
            
            # LRU策略：移动到末尾
            if self.strategy == CacheStrategy.LRU:
                self.cache.move_to_end(key)
            
            # 检查是否需要清理
            self._evict_if_needed()
    
    def delete(self, key: str) -> bool:
        """删除缓存条目"""
        with self.lock:
            if key in self.cache:
                entry = self.cache[key]
                self.stats.total_size -= entry.size
                del self.cache[key]
                self.stats.entry_count = len(self.cache)
                return True
            return False
    
    def clear(self) -> None:
        """清空缓存"""
        with self.lock:
            self.cache.clear()
            self.stats = CacheStats()
    
    def _evict_if_needed(self) -> None:
        """根据策略清理缓存"""
        while len(self.cache) > self.max_size:
            if self.strategy == CacheStrategy.LRU:
                # 删除最久未使用的
                key, entry = self.cache.popitem(last=False)
            elif self.strategy == CacheStrategy.FIFO:
                # 删除最早添加的
                key, entry = self.cache.popitem(last=False)
            elif self.strategy == CacheStrategy.LFU:
                # 删除使用频率最低的
                min_key = min(self.cache.keys(), 
                            key=lambda k: self.cache[k].access_count)
                entry = self.cache.pop(min_key)
            else:
                # 默认LRU
                key, entry = self.cache.popitem(last=False)
            
            self.stats.total_size -= entry.size
            self.stats.evictions += 1
        
        self.stats.entry_count = len(self.cache)


class CacheManager:
    """缓存管理器主类"""
    
    def __init__(self, config: CacheConfig):
        """
        初始化缓存管理器
        
        Args:
            config: 缓存配置
        """
        self.config = config
        self.memory_cache: Optional[MemoryCache] = None
        self.redis_client = None
        self.file_cache_dir: Optional[Path] = None
        
        # 统计信息
        self.global_stats = CacheStats()
        
        # 线程锁
        self.lock = threading.RLock()
        
        # 初始化缓存层
        self._initialize_cache_layers()
        
        logger.info(f"缓存管理器初始化完成: {config.cache_type.value}")
    
    def _initialize_cache_layers(self) -> None:
        """初始化缓存层"""
        try:
            if self.config.cache_type in [CacheType.MEMORY, CacheType.REDIS]:
                # 初始化内存缓存
                self.memory_cache = MemoryCache(
                    max_size=1000,
                    strategy=CacheStrategy.LRU
                )
            
            if self.config.cache_type == CacheType.REDIS:
                # 初始化Redis缓存
                self._initialize_redis()
            
            if self.config.cache_type == CacheType.FILE:
                # 初始化文件缓存
                self._initialize_file_cache()
                
        except Exception as e:
            logger.error(f"初始化缓存层失败: {e}")
            # 降级到内存缓存
            self.memory_cache = MemoryCache()
    
    def _initialize_redis(self) -> None:
        """初始化Redis连接"""
        try:
            import redis
            
            self.redis_client = redis.Redis(
                host=self.config.host or 'localhost',
                port=self.config.port or 6379,
                password=self.config.password,
                db=self.config.db,
                socket_timeout=self.config.socket_timeout,
                socket_connect_timeout=self.config.socket_connect_timeout,
                retry_on_timeout=self.config.retry_on_timeout,
                max_connections=self.config.max_connections,
                decode_responses=False
            )
            
            # 测试连接
            self.redis_client.ping()
            logger.info("Redis缓存连接成功")
            
        except ImportError:
            logger.warning("Redis库未安装，降级到内存缓存")
            self.redis_client = None
        except Exception as e:
            logger.error(f"Redis连接失败: {e}")
            self.redis_client = None
    
    def _initialize_file_cache(self) -> None:
        """初始化文件缓存"""
        try:
            self.file_cache_dir = Path("cache")
            self.file_cache_dir.mkdir(exist_ok=True)
            logger.info(f"文件缓存目录: {self.file_cache_dir}")
        except Exception as e:
            logger.error(f"初始化文件缓存失败: {e}")
            self.file_cache_dir = None
    
    async def get(self, key: str) -> Optional[Any]:
        """
        获取缓存值
        
        Args:
            key: 缓存键
            
        Returns:
            缓存值或None
        """
        try:
            # 首先尝试内存缓存
            if self.memory_cache:
                value = self.memory_cache.get(key)
                if value is not None:
                    self.global_stats.hits += 1
                    return value
            
            # 尝试Redis缓存
            if self.redis_client:
                try:
                    data = self.redis_client.get(key)
                    if data is not None:
                        value = pickle.loads(data)
                        # 回写到内存缓存
                        if self.memory_cache:
                            self.memory_cache.put(key, value)
                        self.global_stats.hits += 1
                        return value
                except Exception as e:
                    logger.warning(f"Redis获取失败: {e}")
            
            # 尝试文件缓存
            if self.file_cache_dir:
                try:
                    file_path = self.file_cache_dir / f"{self._hash_key(key)}.cache"
                    if file_path.exists():
                        with open(file_path, 'rb') as f:
                            cache_data = pickle.load(f)
                        
                        # 检查是否过期
                        if 'ttl' in cache_data and cache_data['ttl']:
                            if time.time() - cache_data['timestamp'] > cache_data['ttl']:
                                file_path.unlink()  # 删除过期文件
                                self.global_stats.misses += 1
                                return None
                        
                        value = cache_data['value']
                        # 回写到上层缓存
                        if self.memory_cache:
                            self.memory_cache.put(key, value)
                        self.global_stats.hits += 1
                        return value
                except Exception as e:
                    logger.warning(f"文件缓存获取失败: {e}")
            
            self.global_stats.misses += 1
            return None
            
        except Exception as e:
            logger.error(f"缓存获取失败: {e}")
            self.global_stats.misses += 1
            return None
    
    async def put(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """
        存储缓存值
        
        Args:
            key: 缓存键
            value: 缓存值
            ttl: 过期时间（秒）
            
        Returns:
            是否成功
        """
        try:
            ttl = ttl or self.config.default_ttl
            
            # 存储到内存缓存
            if self.memory_cache:
                self.memory_cache.put(key, value, ttl)
            
            # 存储到Redis缓存
            if self.redis_client:
                try:
                    data = pickle.dumps(value)
                    if ttl:
                        self.redis_client.setex(key, ttl, data)
                    else:
                        self.redis_client.set(key, data)
                except Exception as e:
                    logger.warning(f"Redis存储失败: {e}")
            
            # 存储到文件缓存
            if self.file_cache_dir:
                try:
                    cache_data = {
                        'value': value,
                        'timestamp': time.time(),
                        'ttl': ttl
                    }
                    file_path = self.file_cache_dir / f"{self._hash_key(key)}.cache"
                    with open(file_path, 'wb') as f:
                        pickle.dump(cache_data, f)
                except Exception as e:
                    logger.warning(f"文件缓存存储失败: {e}")
            
            return True
            
        except Exception as e:
            logger.error(f"缓存存储失败: {e}")
            return False
    
    async def delete(self, key: str) -> bool:
        """
        删除缓存条目
        
        Args:
            key: 缓存键
            
        Returns:
            是否成功
        """
        try:
            success = True
            
            # 从内存缓存删除
            if self.memory_cache:
                self.memory_cache.delete(key)
            
            # 从Redis缓存删除
            if self.redis_client:
                try:
                    self.redis_client.delete(key)
                except Exception as e:
                    logger.warning(f"Redis删除失败: {e}")
                    success = False
            
            # 从文件缓存删除
            if self.file_cache_dir:
                try:
                    file_path = self.file_cache_dir / f"{self._hash_key(key)}.cache"
                    if file_path.exists():
                        file_path.unlink()
                except Exception as e:
                    logger.warning(f"文件缓存删除失败: {e}")
                    success = False
            
            return success
            
        except Exception as e:
            logger.error(f"缓存删除失败: {e}")
            return False
    
    async def clear(self) -> bool:
        """清空所有缓存"""
        try:
            # 清空内存缓存
            if self.memory_cache:
                self.memory_cache.clear()
            
            # 清空Redis缓存
            if self.redis_client:
                try:
                    self.redis_client.flushdb()
                except Exception as e:
                    logger.warning(f"Redis清空失败: {e}")
            
            # 清空文件缓存
            if self.file_cache_dir:
                try:
                    for cache_file in self.file_cache_dir.glob("*.cache"):
                        cache_file.unlink()
                except Exception as e:
                    logger.warning(f"文件缓存清空失败: {e}")
            
            # 重置统计信息
            self.global_stats = CacheStats()
            
            return True
            
        except Exception as e:
            logger.error(f"缓存清空失败: {e}")
            return False
    
    def _hash_key(self, key: str) -> str:
        """生成键的哈希值"""
        return hashlib.md5(key.encode()).hexdigest()
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        try:
            stats = {
                'global_stats': {
                    'hits': self.global_stats.hits,
                    'misses': self.global_stats.misses,
                    'hit_rate': self.global_stats.hit_rate,
                    'miss_rate': self.global_stats.miss_rate
                },
                'config': self.config.to_dict()
            }
            
            # 内存缓存统计
            if self.memory_cache:
                stats['memory_cache'] = {
                    'hits': self.memory_cache.stats.hits,
                    'misses': self.memory_cache.stats.misses,
                    'evictions': self.memory_cache.stats.evictions,
                    'entry_count': self.memory_cache.stats.entry_count,
                    'total_size': self.memory_cache.stats.total_size,
                    'hit_rate': self.memory_cache.stats.hit_rate
                }
            
            # Redis缓存统计
            if self.redis_client:
                try:
                    redis_info = self.redis_client.info('memory')
                    stats['redis_cache'] = {
                        'used_memory': redis_info.get('used_memory', 0),
                        'used_memory_human': redis_info.get('used_memory_human', '0B'),
                        'connected': True
                    }
                except:
                    stats['redis_cache'] = {'connected': False}
            
            return stats
            
        except Exception as e:
            logger.error(f"获取缓存统计失败: {e}")
            return {'error': str(e)}
    
    async def health_check(self) -> Dict[str, bool]:
        """健康检查"""
        health = {}
        
        # 内存缓存健康检查
        health['memory_cache'] = self.memory_cache is not None
        
        # Redis缓存健康检查
        if self.redis_client:
            try:
                self.redis_client.ping()
                health['redis_cache'] = True
            except:
                health['redis_cache'] = False
        else:
            health['redis_cache'] = False
        
        # 文件缓存健康检查
        health['file_cache'] = (
            self.file_cache_dir is not None and 
            self.file_cache_dir.exists()
        )
        
        return health
    
    async def cleanup_expired(self) -> int:
        """清理过期缓存"""
        cleaned_count = 0
        
        try:
            # 清理文件缓存中的过期项
            if self.file_cache_dir:
                current_time = time.time()
                for cache_file in self.file_cache_dir.glob("*.cache"):
                    try:
                        with open(cache_file, 'rb') as f:
                            cache_data = pickle.load(f)
                        
                        if 'ttl' in cache_data and cache_data['ttl']:
                            if current_time - cache_data['timestamp'] > cache_data['ttl']:
                                cache_file.unlink()
                                cleaned_count += 1
                    except:
                        # 删除损坏的缓存文件
                        cache_file.unlink()
                        cleaned_count += 1
            
            logger.info(f"清理了 {cleaned_count} 个过期缓存项")
            return cleaned_count
            
        except Exception as e:
            logger.error(f"清理过期缓存失败: {e}")
            return 0
    
    async def close(self) -> None:
        """关闭缓存管理器"""
        try:
            if self.redis_client:
                self.redis_client.close()
            logger.info("缓存管理器已关闭")
        except Exception as e:
            logger.error(f"关闭缓存管理器失败: {e}")