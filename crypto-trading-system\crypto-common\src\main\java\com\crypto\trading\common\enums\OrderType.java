package com.crypto.trading.common.enums;

/**
 * 订单类型枚举
 * <p>
 * 定义订单的各种类型
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public enum OrderType {

    /**
     * 市价单
     */
    MARKET("MARKET", "市价单"),

    /**
     * 限价单
     */
    LIMIT("LIMIT", "限价单"),

    /**
     * 止损单
     */
    STOP_LOSS("STOP_LOSS", "止损单"),

    /**
     * 止盈单
     */
    TAKE_PROFIT("TAKE_PROFIT", "止盈单"),

    /**
     * 限价止损单
     */
    STOP_LOSS_LIMIT("STOP_LOSS_LIMIT", "限价止损单"),

    /**
     * 限价止盈单
     */
    TAKE_PROFIT_LIMIT("TAKE_PROFIT_LIMIT", "限价止盈单");

    /**
     * 类型码
     */
    private final String code;

    /**
     * 类型描述
     */
    private final String desc;

    /**
     * 构造函数
     *
     * @param code 类型码
     * @param desc 类型描述
     */
    OrderType(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 获取类型码
     *
     * @return 类型码
     */
    public String getCode() {
        return code;
    }

    /**
     * 获取类型描述
     *
     * @return 类型描述
     */
    public String getDesc() {
        return desc;
    }

    /**
     * 根据类型码获取枚举
     *
     * @param code 类型码
     * @return 枚举实例
     */
    public static OrderType fromCode(String code) {
        for (OrderType type : OrderType.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知的订单类型码: " + code);
    }

    /**
     * 判断是否为市价单
     *
     * @return 是否为市价单
     */
    public boolean isMarket() {
        return this == MARKET;
    }

    /**
     * 判断是否为限价单
     *
     * @return 是否为限价单
     */
    public boolean isLimit() {
        return this == LIMIT || this == STOP_LOSS_LIMIT || this == TAKE_PROFIT_LIMIT;
    }

    /**
     * 判断是否为止损单
     *
     * @return 是否为止损单
     */
    public boolean isStopLoss() {
        return this == STOP_LOSS || this == STOP_LOSS_LIMIT;
    }

    /**
     * 判断是否为止盈单
     *
     * @return 是否为止盈单
     */
    public boolean isTakeProfit() {
        return this == TAKE_PROFIT || this == TAKE_PROFIT_LIMIT;
    }
}