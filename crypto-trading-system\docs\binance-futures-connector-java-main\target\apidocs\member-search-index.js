memberSearchIndex = [{"p":"com.binance.connector.futures.client","c":"FuturesClient","l":"account()"},{"p":"com.binance.connector.futures.client.impl","c":"CMFuturesClientImpl","l":"account()"},{"p":"com.binance.connector.futures.client.impl","c":"UMFuturesClientImpl","l":"account()"},{"p":"com.binance.connector.futures.client.impl.futures","c":"Account","l":"Account(String, String, String, boolean, ProxyAuth)","u":"%3Cinit%3E(java.lang.String,java.lang.String,java.lang.String,boolean,com.binance.connector.futures.client.utils.ProxyAuth)"},{"p":"com.binance.connector.futures.client.impl.cm_futures","c":"CMAccount","l":"accountInformation(LinkedHashMap<String, Object>)","u":"accountInformation(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl.um_futures","c":"UMAccount","l":"accountInformation(LinkedHashMap<String, Object>)","u":"accountInformation(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl.cm_futures","c":"CMAccount","l":"accountTradeList(LinkedHashMap<String, Object>)","u":"accountTradeList(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl.futures","c":"Account","l":"accountTradeList(LinkedHashMap<String, Object>)","u":"accountTradeList(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl.um_futures","c":"UMAccount","l":"accountTradeList(LinkedHashMap<String, Object>)","u":"accountTradeList(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl.futures","c":"Market","l":"aggTrades(LinkedHashMap<String, Object>)","u":"aggTrades(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl","c":"WebsocketClientImpl","l":"aggTradeStream(String, WebSocketCallback)","u":"aggTradeStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)"},{"p":"com.binance.connector.futures.client","c":"WebsocketClient","l":"aggTradeStream(String, WebSocketCallback)","u":"aggTradeStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)"},{"p":"com.binance.connector.futures.client.impl","c":"WebsocketClientImpl","l":"aggTradeStream(String, WebSocketCallback, WebSocketCallback, WebSocketCallback, WebSocketCallback)","u":"aggTradeStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)"},{"p":"com.binance.connector.futures.client","c":"WebsocketClient","l":"aggTradeStream(String, WebSocketCallback, WebSocketCallback, WebSocketCallback, WebSocketCallback)","u":"aggTradeStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)"},{"p":"com.binance.connector.futures.client.impl","c":"WebsocketClientImpl","l":"allBookTickerStream(WebSocketCallback)","u":"allBookTickerStream(com.binance.connector.futures.client.utils.WebSocketCallback)"},{"p":"com.binance.connector.futures.client","c":"WebsocketClient","l":"allBookTickerStream(WebSocketCallback)","u":"allBookTickerStream(com.binance.connector.futures.client.utils.WebSocketCallback)"},{"p":"com.binance.connector.futures.client.impl","c":"WebsocketClientImpl","l":"allBookTickerStream(WebSocketCallback, WebSocketCallback, WebSocketCallback, WebSocketCallback)","u":"allBookTickerStream(com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)"},{"p":"com.binance.connector.futures.client","c":"WebsocketClient","l":"allBookTickerStream(WebSocketCallback, WebSocketCallback, WebSocketCallback, WebSocketCallback)","u":"allBookTickerStream(com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)"},{"p":"com.binance.connector.futures.client.impl","c":"WebsocketClientImpl","l":"allForceOrderStream(WebSocketCallback)","u":"allForceOrderStream(com.binance.connector.futures.client.utils.WebSocketCallback)"},{"p":"com.binance.connector.futures.client","c":"WebsocketClient","l":"allForceOrderStream(WebSocketCallback)","u":"allForceOrderStream(com.binance.connector.futures.client.utils.WebSocketCallback)"},{"p":"com.binance.connector.futures.client.impl","c":"WebsocketClientImpl","l":"allForceOrderStream(WebSocketCallback, WebSocketCallback, WebSocketCallback, WebSocketCallback)","u":"allForceOrderStream(com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)"},{"p":"com.binance.connector.futures.client","c":"WebsocketClient","l":"allForceOrderStream(WebSocketCallback, WebSocketCallback, WebSocketCallback, WebSocketCallback)","u":"allForceOrderStream(com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)"},{"p":"com.binance.connector.futures.client.impl","c":"UMWebsocketClientImpl","l":"allMarkPriceStream(int, WebSocketCallback)","u":"allMarkPriceStream(int,com.binance.connector.futures.client.utils.WebSocketCallback)"},{"p":"com.binance.connector.futures.client.impl","c":"UMWebsocketClientImpl","l":"allMarkPriceStream(int, WebSocketCallback, WebSocketCallback, WebSocketCallback, WebSocketCallback)","u":"allMarkPriceStream(int,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)"},{"p":"com.binance.connector.futures.client.impl","c":"WebsocketClientImpl","l":"allMiniTickerStream(WebSocketCallback)","u":"allMiniTickerStream(com.binance.connector.futures.client.utils.WebSocketCallback)"},{"p":"com.binance.connector.futures.client","c":"WebsocketClient","l":"allMiniTickerStream(WebSocketCallback)","u":"allMiniTickerStream(com.binance.connector.futures.client.utils.WebSocketCallback)"},{"p":"com.binance.connector.futures.client.impl","c":"WebsocketClientImpl","l":"allMiniTickerStream(WebSocketCallback, WebSocketCallback, WebSocketCallback, WebSocketCallback)","u":"allMiniTickerStream(com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)"},{"p":"com.binance.connector.futures.client","c":"WebsocketClient","l":"allMiniTickerStream(WebSocketCallback, WebSocketCallback, WebSocketCallback, WebSocketCallback)","u":"allMiniTickerStream(com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)"},{"p":"com.binance.connector.futures.client.impl.cm_futures","c":"CMAccount","l":"allOrders(LinkedHashMap<String, Object>)","u":"allOrders(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl.futures","c":"Account","l":"allOrders(LinkedHashMap<String, Object>)","u":"allOrders(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl.um_futures","c":"UMAccount","l":"allOrders(LinkedHashMap<String, Object>)","u":"allOrders(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl","c":"WebsocketClientImpl","l":"allTickerStream(WebSocketCallback)","u":"allTickerStream(com.binance.connector.futures.client.utils.WebSocketCallback)"},{"p":"com.binance.connector.futures.client","c":"WebsocketClient","l":"allTickerStream(WebSocketCallback)","u":"allTickerStream(com.binance.connector.futures.client.utils.WebSocketCallback)"},{"p":"com.binance.connector.futures.client.impl","c":"WebsocketClientImpl","l":"allTickerStream(WebSocketCallback, WebSocketCallback, WebSocketCallback, WebSocketCallback)","u":"allTickerStream(com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)"},{"p":"com.binance.connector.futures.client","c":"WebsocketClient","l":"allTickerStream(WebSocketCallback, WebSocketCallback, WebSocketCallback, WebSocketCallback)","u":"allTickerStream(com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)"},{"p":"com.binance.connector.futures.client.impl.um_futures","c":"UMMarket","l":"assetIndex(LinkedHashMap<String, Object>)","u":"assetIndex(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl.futures","c":"Account","l":"autoCancelOpen(LinkedHashMap<String, Object>)","u":"autoCancelOpen(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl.cm_futures","c":"CMMarket","l":"basis(LinkedHashMap<String, Object>)","u":"basis(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl.cm_futures","c":"CMMarket","l":"bookTicker(LinkedHashMap<String, Object>)","u":"bookTicker(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl.futures","c":"Market","l":"bookTicker(LinkedHashMap<String, Object>)","u":"bookTicker(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl.um_futures","c":"UMMarket","l":"bookTicker(LinkedHashMap<String, Object>)","u":"bookTicker(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl","c":"WebsocketClientImpl","l":"bookTicker(String, WebSocketCallback)","u":"bookTicker(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)"},{"p":"com.binance.connector.futures.client","c":"WebsocketClient","l":"bookTicker(String, WebSocketCallback)","u":"bookTicker(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)"},{"p":"com.binance.connector.futures.client.impl","c":"WebsocketClientImpl","l":"bookTicker(String, WebSocketCallback, WebSocketCallback, WebSocketCallback, WebSocketCallback)","u":"bookTicker(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)"},{"p":"com.binance.connector.futures.client","c":"WebsocketClient","l":"bookTicker(String, WebSocketCallback, WebSocketCallback, WebSocketCallback, WebSocketCallback)","u":"bookTicker(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)"},{"p":"com.binance.connector.futures.client.impl.futures","c":"Account","l":"cancelAllOpenOrders(LinkedHashMap<String, Object>)","u":"cancelAllOpenOrders(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl.futures","c":"Account","l":"cancelMultipleOrders(LinkedHashMap<String, Object>)","u":"cancelMultipleOrders(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl.futures","c":"Account","l":"cancelOrder(LinkedHashMap<String, Object>)","u":"cancelOrder(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl.futures","c":"Account","l":"changeInitialLeverage(LinkedHashMap<String, Object>)","u":"changeInitialLeverage(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl.futures","c":"Account","l":"changeMarginType(LinkedHashMap<String, Object>)","u":"changeMarginType(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl.um_futures","c":"UMAccount","l":"changeMultiAssetsMode(LinkedHashMap<String, Object>)","u":"changeMultiAssetsMode(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl.futures","c":"Account","l":"changePositionModeTrade(LinkedHashMap<String, Object>)","u":"changePositionModeTrade(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl","c":"WebsocketClientImpl","l":"closeAllConnections()"},{"p":"com.binance.connector.futures.client","c":"WebsocketClient","l":"closeAllConnections()"},{"p":"com.binance.connector.futures.client.impl","c":"WebsocketClientImpl","l":"closeConnection(int)"},{"p":"com.binance.connector.futures.client","c":"WebsocketClient","l":"closeConnection(int)"},{"p":"com.binance.connector.futures.client.impl.futures","c":"UserData","l":"closeListenKey()"},{"p":"com.binance.connector.futures.client.impl.cm_futures","c":"CMAccount","l":"CMAccount(String, String, String, boolean, ProxyAuth)","u":"%3Cinit%3E(java.lang.String,java.lang.String,java.lang.String,boolean,com.binance.connector.futures.client.utils.ProxyAuth)"},{"p":"com.binance.connector.futures.client.impl","c":"CMFuturesClientImpl","l":"CMFuturesClientImpl()","u":"%3Cinit%3E()"},{"p":"com.binance.connector.futures.client.impl","c":"CMFuturesClientImpl","l":"CMFuturesClientImpl(String)","u":"%3Cinit%3E(java.lang.String)"},{"p":"com.binance.connector.futures.client.impl","c":"CMFuturesClientImpl","l":"CMFuturesClientImpl(String, boolean)","u":"%3Cinit%3E(java.lang.String,boolean)"},{"p":"com.binance.connector.futures.client.impl","c":"CMFuturesClientImpl","l":"CMFuturesClientImpl(String, String)","u":"%3Cinit%3E(java.lang.String,java.lang.String)"},{"p":"com.binance.connector.futures.client.impl","c":"CMFuturesClientImpl","l":"CMFuturesClientImpl(String, String, boolean)","u":"%3Cinit%3E(java.lang.String,java.lang.String,boolean)"},{"p":"com.binance.connector.futures.client.impl","c":"CMFuturesClientImpl","l":"CMFuturesClientImpl(String, String, String)","u":"%3Cinit%3E(java.lang.String,java.lang.String,java.lang.String)"},{"p":"com.binance.connector.futures.client.impl.cm_futures","c":"CMMarket","l":"CMMarket(String, String, String, boolean, ProxyAuth)","u":"%3Cinit%3E(java.lang.String,java.lang.String,java.lang.String,boolean,com.binance.connector.futures.client.utils.ProxyAuth)"},{"p":"com.binance.connector.futures.client.impl.cm_futures","c":"CMPortfolioMargin","l":"CMPortfolioMargin(String, String, String, boolean, ProxyAuth)","u":"%3Cinit%3E(java.lang.String,java.lang.String,java.lang.String,boolean,com.binance.connector.futures.client.utils.ProxyAuth)"},{"p":"com.binance.connector.futures.client.impl.cm_futures","c":"CMUserData","l":"CMUserData(String, String, boolean, ProxyAuth)","u":"%3Cinit%3E(java.lang.String,java.lang.String,boolean,com.binance.connector.futures.client.utils.ProxyAuth)"},{"p":"com.binance.connector.futures.client.impl","c":"CMWebsocketClientImpl","l":"CMWebsocketClientImpl()","u":"%3Cinit%3E()"},{"p":"com.binance.connector.futures.client.impl","c":"CMWebsocketClientImpl","l":"CMWebsocketClientImpl(String)","u":"%3Cinit%3E(java.lang.String)"},{"p":"com.binance.connector.futures.client.impl","c":"WebsocketClientImpl","l":"combineStreams(ArrayList<String>, WebSocketCallback)","u":"combineStreams(java.util.ArrayList,com.binance.connector.futures.client.utils.WebSocketCallback)"},{"p":"com.binance.connector.futures.client","c":"WebsocketClient","l":"combineStreams(ArrayList<String>, WebSocketCallback)","u":"combineStreams(java.util.ArrayList,com.binance.connector.futures.client.utils.WebSocketCallback)"},{"p":"com.binance.connector.futures.client.impl","c":"WebsocketClientImpl","l":"combineStreams(ArrayList<String>, WebSocketCallback, WebSocketCallback, WebSocketCallback, WebSocketCallback)","u":"combineStreams(java.util.ArrayList,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)"},{"p":"com.binance.connector.futures.client","c":"WebsocketClient","l":"combineStreams(ArrayList<String>, WebSocketCallback, WebSocketCallback, WebSocketCallback, WebSocketCallback)","u":"combineStreams(java.util.ArrayList,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)"},{"p":"com.binance.connector.futures.client.impl","c":"UMWebsocketClientImpl","l":"compositeIndexSymbolInfo(String, WebSocketCallback)","u":"compositeIndexSymbolInfo(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)"},{"p":"com.binance.connector.futures.client.impl","c":"UMWebsocketClientImpl","l":"compositeIndexSymbolInfo(String, WebSocketCallback, WebSocketCallback, WebSocketCallback, WebSocketCallback)","u":"compositeIndexSymbolInfo(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)"},{"p":"com.binance.connector.futures.client.impl.futures","c":"Market","l":"continuousKlines(LinkedHashMap<String, Object>)","u":"continuousKlines(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl","c":"WebsocketClientImpl","l":"continuousKlineStream(String, String, String, WebSocketCallback)","u":"continuousKlineStream(java.lang.String,java.lang.String,java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)"},{"p":"com.binance.connector.futures.client","c":"WebsocketClient","l":"continuousKlineStream(String, String, String, WebSocketCallback)","u":"continuousKlineStream(java.lang.String,java.lang.String,java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)"},{"p":"com.binance.connector.futures.client.impl","c":"WebsocketClientImpl","l":"continuousKlineStream(String, String, String, WebSocketCallback, WebSocketCallback, WebSocketCallback, WebSocketCallback)","u":"continuousKlineStream(java.lang.String,java.lang.String,java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)"},{"p":"com.binance.connector.futures.client","c":"WebsocketClient","l":"continuousKlineStream(String, String, String, WebSocketCallback, WebSocketCallback, WebSocketCallback, WebSocketCallback)","u":"continuousKlineStream(java.lang.String,java.lang.String,java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)"},{"p":"com.binance.connector.futures.client.impl","c":"WebsocketClientImpl","l":"createConnection(WebSocketCallback, WebSocketCallback, WebSocketCallback, WebSocketCallback, Request)","u":"createConnection(com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,okhttp3.Request)"},{"p":"com.binance.connector.futures.client.impl.futures","c":"UserData","l":"createListenKey()"},{"p":"com.binance.connector.futures.client.impl.cm_futures","c":"CMAccount","l":"currentAllOpenOrders(LinkedHashMap<String, Object>)","u":"currentAllOpenOrders(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl.futures","c":"Account","l":"currentAllOpenOrders(LinkedHashMap<String, Object>)","u":"currentAllOpenOrders(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl.um_futures","c":"UMAccount","l":"currentAllOpenOrders(LinkedHashMap<String, Object>)","u":"currentAllOpenOrders(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl.futures","c":"Market","l":"depth(LinkedHashMap<String, Object>)","u":"depth(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl","c":"WebsocketClientImpl","l":"diffDepthStream(String, int, WebSocketCallback)","u":"diffDepthStream(java.lang.String,int,com.binance.connector.futures.client.utils.WebSocketCallback)"},{"p":"com.binance.connector.futures.client","c":"WebsocketClient","l":"diffDepthStream(String, int, WebSocketCallback)","u":"diffDepthStream(java.lang.String,int,com.binance.connector.futures.client.utils.WebSocketCallback)"},{"p":"com.binance.connector.futures.client.impl","c":"WebsocketClientImpl","l":"diffDepthStream(String, int, WebSocketCallback, WebSocketCallback, WebSocketCallback, WebSocketCallback)","u":"diffDepthStream(java.lang.String,int,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)"},{"p":"com.binance.connector.futures.client","c":"WebsocketClient","l":"diffDepthStream(String, int, WebSocketCallback, WebSocketCallback, WebSocketCallback, WebSocketCallback)","u":"diffDepthStream(java.lang.String,int,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)"},{"p":"com.binance.connector.futures.client.impl.futures","c":"Market","l":"exchangeInfo()"},{"p":"com.binance.connector.futures.client.impl.futures","c":"UserData","l":"extendListenKey()"},{"p":"com.binance.connector.futures.client.impl","c":"WebsocketClientImpl","l":"forceOrderStream(String, WebSocketCallback)","u":"forceOrderStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)"},{"p":"com.binance.connector.futures.client","c":"WebsocketClient","l":"forceOrderStream(String, WebSocketCallback)","u":"forceOrderStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)"},{"p":"com.binance.connector.futures.client.impl","c":"WebsocketClientImpl","l":"forceOrderStream(String, WebSocketCallback, WebSocketCallback, WebSocketCallback, WebSocketCallback)","u":"forceOrderStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)"},{"p":"com.binance.connector.futures.client","c":"WebsocketClient","l":"forceOrderStream(String, WebSocketCallback, WebSocketCallback, WebSocketCallback, WebSocketCallback)","u":"forceOrderStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)"},{"p":"com.binance.connector.futures.client.impl.futures","c":"Market","l":"fundingRate(LinkedHashMap<String, Object>)","u":"fundingRate(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl.cm_futures","c":"CMAccount","l":"futuresAccountBalance(LinkedHashMap<String, Object>)","u":"futuresAccountBalance(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl.um_futures","c":"UMAccount","l":"futuresAccountBalance(LinkedHashMap<String, Object>)","u":"futuresAccountBalance(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl","c":"FuturesClientImpl","l":"FuturesClientImpl(String, String)","u":"%3Cinit%3E(java.lang.String,java.lang.String)"},{"p":"com.binance.connector.futures.client.impl","c":"FuturesClientImpl","l":"FuturesClientImpl(String, String, boolean)","u":"%3Cinit%3E(java.lang.String,java.lang.String,boolean)"},{"p":"com.binance.connector.futures.client.impl","c":"FuturesClientImpl","l":"FuturesClientImpl(String, String, String, String)","u":"%3Cinit%3E(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"},{"p":"com.binance.connector.futures.client.impl","c":"FuturesClientImpl","l":"FuturesClientImpl(String, String, String, String, boolean)","u":"%3Cinit%3E(java.lang.String,java.lang.String,java.lang.String,java.lang.String,boolean)"},{"p":"com.binance.connector.futures.client.impl.um_futures","c":"UMAccount","l":"futuresDownloadId(LinkedHashMap<String, Object>)","u":"futuresDownloadId(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl.um_futures","c":"UMAccount","l":"futuresDownloadLink(LinkedHashMap<String, Object>)","u":"futuresDownloadLink(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl.futures","c":"Account","l":"getAdlQuantile(LinkedHashMap<String, Object>)","u":"getAdlQuantile(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl","c":"FuturesClientImpl","l":"getApiKey()"},{"p":"com.binance.connector.futures.client.impl.futures","c":"Market","l":"getBaseUrl()"},{"p":"com.binance.connector.futures.client.impl","c":"FuturesClientImpl","l":"getBaseUrl()"},{"p":"com.binance.connector.futures.client.impl","c":"WebsocketClientImpl","l":"getBaseUrl()"},{"p":"com.binance.connector.futures.client.impl.futures","c":"Account","l":"getCommissionRate(LinkedHashMap<String, Object>)","u":"getCommissionRate(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl.um_futures","c":"UMAccount","l":"getCurrentMultiAssetMode(LinkedHashMap<String, Object>)","u":"getCurrentMultiAssetMode(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl.futures","c":"Account","l":"getCurrentPositionMode(LinkedHashMap<String, Object>)","u":"getCurrentPositionMode(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl.futures","c":"Account","l":"getForceOrders(LinkedHashMap<String, Object>)","u":"getForceOrders(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl.futures","c":"Account","l":"getIncomeHistory(LinkedHashMap<String, Object>)","u":"getIncomeHistory(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl.cm_futures","c":"CMAccount","l":"getLeverageBracket(LinkedHashMap<String, Object>)","u":"getLeverageBracket(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl.futures","c":"Account","l":"getLeverageBracket(LinkedHashMap<String, Object>)","u":"getLeverageBracket(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl.um_futures","c":"UMAccount","l":"getLeverageBracket(LinkedHashMap<String, Object>)","u":"getLeverageBracket(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl.cm_futures","c":"CMAccount","l":"getLeverageBracketForPair(LinkedHashMap<String, Object>)","u":"getLeverageBracketForPair(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl","c":"WebsocketClientImpl","l":"getNoopCallback()"},{"p":"com.binance.connector.futures.client.impl.futures","c":"Account","l":"getPositionMarginChangeHistory(LinkedHashMap<String, Object>)","u":"getPositionMarginChangeHistory(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl.futures","c":"Account","l":"getProductUrl()"},{"p":"com.binance.connector.futures.client.impl.futures","c":"Market","l":"getProductUrl()"},{"p":"com.binance.connector.futures.client.impl.futures","c":"PortfolioMargin","l":"getProductUrl()"},{"p":"com.binance.connector.futures.client.impl.futures","c":"UserData","l":"getProductUrl()"},{"p":"com.binance.connector.futures.client.impl","c":"FuturesClientImpl","l":"getProductUrl()"},{"p":"com.binance.connector.futures.client.impl","c":"FuturesClientImpl","l":"getProxy()"},{"p":"com.binance.connector.futures.client.impl.futures","c":"Account","l":"getRequestHandler()"},{"p":"com.binance.connector.futures.client.impl.futures","c":"Market","l":"getRequestHandler()"},{"p":"com.binance.connector.futures.client.impl.futures","c":"PortfolioMargin","l":"getRequestHandler()"},{"p":"com.binance.connector.futures.client.impl.futures","c":"UserData","l":"getRequestHandler()"},{"p":"com.binance.connector.futures.client.impl","c":"FuturesClientImpl","l":"getSecretKey()"},{"p":"com.binance.connector.futures.client.impl.futures","c":"Account","l":"getShowLimitUsage()"},{"p":"com.binance.connector.futures.client.impl.futures","c":"Market","l":"getShowLimitUsage()"},{"p":"com.binance.connector.futures.client.impl.futures","c":"PortfolioMargin","l":"getShowLimitUsage()"},{"p":"com.binance.connector.futures.client.impl.futures","c":"UserData","l":"getShowLimitUsage()"},{"p":"com.binance.connector.futures.client.impl","c":"FuturesClientImpl","l":"getShowLimitUsage()"},{"p":"com.binance.connector.futures.client.impl.um_futures","c":"UMAccount","l":"getTradingRulesIndicators(LinkedHashMap<String, Object>)","u":"getTradingRulesIndicators(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl.um_futures","c":"UMMarket","l":"historicalBlvt(LinkedHashMap<String, Object>)","u":"historicalBlvt(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl.futures","c":"Market","l":"historicalTrades(LinkedHashMap<String, Object>)","u":"historicalTrades(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl.um_futures","c":"UMMarket","l":"indexInfo(LinkedHashMap<String, Object>)","u":"indexInfo(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl","c":"CMWebsocketClientImpl","l":"indexKlineCandlestick(String, String, WebSocketCallback)","u":"indexKlineCandlestick(java.lang.String,java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)"},{"p":"com.binance.connector.futures.client.impl","c":"CMWebsocketClientImpl","l":"indexKlineCandlestick(String, String, WebSocketCallback, WebSocketCallback, WebSocketCallback, WebSocketCallback)","u":"indexKlineCandlestick(java.lang.String,java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)"},{"p":"com.binance.connector.futures.client.impl.futures","c":"Market","l":"indexPriceKlines(LinkedHashMap<String, Object>)","u":"indexPriceKlines(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl","c":"CMWebsocketClientImpl","l":"indexPriceStream(String, int, WebSocketCallback)","u":"indexPriceStream(java.lang.String,int,com.binance.connector.futures.client.utils.WebSocketCallback)"},{"p":"com.binance.connector.futures.client.impl","c":"CMWebsocketClientImpl","l":"indexPriceStream(String, int, WebSocketCallback, WebSocketCallback, WebSocketCallback, WebSocketCallback)","u":"indexPriceStream(java.lang.String,int,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)"},{"p":"com.binance.connector.futures.client.impl.futures","c":"Market","l":"klines(LinkedHashMap<String, Object>)","u":"klines(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl","c":"WebsocketClientImpl","l":"klineStream(String, String, WebSocketCallback)","u":"klineStream(java.lang.String,java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)"},{"p":"com.binance.connector.futures.client","c":"WebsocketClient","l":"klineStream(String, String, WebSocketCallback)","u":"klineStream(java.lang.String,java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)"},{"p":"com.binance.connector.futures.client.impl","c":"WebsocketClientImpl","l":"klineStream(String, String, WebSocketCallback, WebSocketCallback, WebSocketCallback, WebSocketCallback)","u":"klineStream(java.lang.String,java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)"},{"p":"com.binance.connector.futures.client","c":"WebsocketClient","l":"klineStream(String, String, WebSocketCallback, WebSocketCallback, WebSocketCallback, WebSocketCallback)","u":"klineStream(java.lang.String,java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)"},{"p":"com.binance.connector.futures.client.impl","c":"WebsocketClientImpl","l":"listenUserStream(String, WebSocketCallback)","u":"listenUserStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)"},{"p":"com.binance.connector.futures.client","c":"WebsocketClient","l":"listenUserStream(String, WebSocketCallback)","u":"listenUserStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)"},{"p":"com.binance.connector.futures.client.impl","c":"WebsocketClientImpl","l":"listenUserStream(String, WebSocketCallback, WebSocketCallback, WebSocketCallback, WebSocketCallback)","u":"listenUserStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)"},{"p":"com.binance.connector.futures.client","c":"WebsocketClient","l":"listenUserStream(String, WebSocketCallback, WebSocketCallback, WebSocketCallback, WebSocketCallback)","u":"listenUserStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)"},{"p":"com.binance.connector.futures.client.impl.cm_futures","c":"CMMarket","l":"longShortRatio(LinkedHashMap<String, Object>)","u":"longShortRatio(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl.futures","c":"Market","l":"longShortRatio(LinkedHashMap<String, Object>)","u":"longShortRatio(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl.um_futures","c":"UMMarket","l":"longShortRatio(LinkedHashMap<String, Object>)","u":"longShortRatio(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client","c":"FuturesClient","l":"market()"},{"p":"com.binance.connector.futures.client.impl","c":"CMFuturesClientImpl","l":"market()"},{"p":"com.binance.connector.futures.client.impl","c":"UMFuturesClientImpl","l":"market()"},{"p":"com.binance.connector.futures.client.impl.futures","c":"Market","l":"Market(String, String, String, boolean, ProxyAuth)","u":"%3Cinit%3E(java.lang.String,java.lang.String,java.lang.String,boolean,com.binance.connector.futures.client.utils.ProxyAuth)"},{"p":"com.binance.connector.futures.client.impl","c":"CMWebsocketClientImpl","l":"markKlineCandlestick(String, String, WebSocketCallback)","u":"markKlineCandlestick(java.lang.String,java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)"},{"p":"com.binance.connector.futures.client.impl","c":"CMWebsocketClientImpl","l":"markKlineCandlestick(String, String, WebSocketCallback, WebSocketCallback, WebSocketCallback, WebSocketCallback)","u":"markKlineCandlestick(java.lang.String,java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)"},{"p":"com.binance.connector.futures.client.impl.cm_futures","c":"CMMarket","l":"markPrice(LinkedHashMap<String, Object>)","u":"markPrice(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl.futures","c":"Market","l":"markPrice(LinkedHashMap<String, Object>)","u":"markPrice(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl.um_futures","c":"UMMarket","l":"markPrice(LinkedHashMap<String, Object>)","u":"markPrice(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl.futures","c":"Market","l":"markPriceKlines(LinkedHashMap<String, Object>)","u":"markPriceKlines(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl","c":"WebsocketClientImpl","l":"markPriceStream(String, int, WebSocketCallback)","u":"markPriceStream(java.lang.String,int,com.binance.connector.futures.client.utils.WebSocketCallback)"},{"p":"com.binance.connector.futures.client","c":"WebsocketClient","l":"markPriceStream(String, int, WebSocketCallback)","u":"markPriceStream(java.lang.String,int,com.binance.connector.futures.client.utils.WebSocketCallback)"},{"p":"com.binance.connector.futures.client.impl","c":"WebsocketClientImpl","l":"markPriceStream(String, int, WebSocketCallback, WebSocketCallback, WebSocketCallback, WebSocketCallback)","u":"markPriceStream(java.lang.String,int,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)"},{"p":"com.binance.connector.futures.client","c":"WebsocketClient","l":"markPriceStream(String, int, WebSocketCallback, WebSocketCallback, WebSocketCallback, WebSocketCallback)","u":"markPriceStream(java.lang.String,int,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)"},{"p":"com.binance.connector.futures.client.impl","c":"CMWebsocketClientImpl","l":"markPriceSymbolsPairStream(String, int, WebSocketCallback)","u":"markPriceSymbolsPairStream(java.lang.String,int,com.binance.connector.futures.client.utils.WebSocketCallback)"},{"p":"com.binance.connector.futures.client.impl","c":"CMWebsocketClientImpl","l":"markPriceSymbolsPairStream(String, int, WebSocketCallback, WebSocketCallback, WebSocketCallback, WebSocketCallback)","u":"markPriceSymbolsPairStream(java.lang.String,int,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)"},{"p":"com.binance.connector.futures.client.impl","c":"WebsocketClientImpl","l":"miniTickerStream(String, WebSocketCallback)","u":"miniTickerStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)"},{"p":"com.binance.connector.futures.client","c":"WebsocketClient","l":"miniTickerStream(String, WebSocketCallback)","u":"miniTickerStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)"},{"p":"com.binance.connector.futures.client.impl","c":"WebsocketClientImpl","l":"miniTickerStream(String, WebSocketCallback, WebSocketCallback, WebSocketCallback, WebSocketCallback)","u":"miniTickerStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)"},{"p":"com.binance.connector.futures.client","c":"WebsocketClient","l":"miniTickerStream(String, WebSocketCallback, WebSocketCallback, WebSocketCallback, WebSocketCallback)","u":"miniTickerStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)"},{"p":"com.binance.connector.futures.client.impl.futures","c":"Account","l":"modifyIsolatedPositionMargin(LinkedHashMap<String, Object>)","u":"modifyIsolatedPositionMargin(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl.cm_futures","c":"CMAccount","l":"modifyOrder(LinkedHashMap<String, Object>)","u":"modifyOrder(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl.futures","c":"Account","l":"newOrder(LinkedHashMap<String, Object>)","u":"newOrder(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl.futures","c":"Market","l":"openInterest(LinkedHashMap<String, Object>)","u":"openInterest(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl.cm_futures","c":"CMMarket","l":"openInterestStatistics(LinkedHashMap<String, Object>)","u":"openInterestStatistics(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl.futures","c":"Market","l":"openInterestStatistics(LinkedHashMap<String, Object>)","u":"openInterestStatistics(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl.um_futures","c":"UMMarket","l":"openInterestStatistics(LinkedHashMap<String, Object>)","u":"openInterestStatistics(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl.cm_futures","c":"CMAccount","l":"orderModifyHistory(LinkedHashMap<String, Object>)","u":"orderModifyHistory(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl","c":"WebsocketClientImpl","l":"partialDepthStream(String, int, int, WebSocketCallback)","u":"partialDepthStream(java.lang.String,int,int,com.binance.connector.futures.client.utils.WebSocketCallback)"},{"p":"com.binance.connector.futures.client","c":"WebsocketClient","l":"partialDepthStream(String, int, int, WebSocketCallback)","u":"partialDepthStream(java.lang.String,int,int,com.binance.connector.futures.client.utils.WebSocketCallback)"},{"p":"com.binance.connector.futures.client.impl","c":"WebsocketClientImpl","l":"partialDepthStream(String, int, int, WebSocketCallback, WebSocketCallback, WebSocketCallback, WebSocketCallback)","u":"partialDepthStream(java.lang.String,int,int,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)"},{"p":"com.binance.connector.futures.client","c":"WebsocketClient","l":"partialDepthStream(String, int, int, WebSocketCallback, WebSocketCallback, WebSocketCallback, WebSocketCallback)","u":"partialDepthStream(java.lang.String,int,int,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)"},{"p":"com.binance.connector.futures.client.impl.futures","c":"Market","l":"ping()"},{"p":"com.binance.connector.futures.client.impl.futures","c":"Account","l":"placeMultipleOrders(LinkedHashMap<String, Object>)","u":"placeMultipleOrders(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client","c":"FuturesClient","l":"portfolioMargin()"},{"p":"com.binance.connector.futures.client.impl","c":"CMFuturesClientImpl","l":"portfolioMargin()"},{"p":"com.binance.connector.futures.client.impl.futures","c":"PortfolioMargin","l":"PortfolioMargin(String, String, String, boolean, ProxyAuth)","u":"%3Cinit%3E(java.lang.String,java.lang.String,java.lang.String,boolean,com.binance.connector.futures.client.utils.ProxyAuth)"},{"p":"com.binance.connector.futures.client.impl.futures","c":"PortfolioMargin","l":"portfolioMarginAccountInfo(LinkedHashMap<String, Object>)","u":"portfolioMarginAccountInfo(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl.cm_futures","c":"CMPortfolioMargin","l":"portfolioMarginExchangeInfo(LinkedHashMap<String, Object>)","u":"portfolioMarginExchangeInfo(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl.futures","c":"PortfolioMargin","l":"portfolioMarginExchangeInfo(LinkedHashMap<String, Object>)","u":"portfolioMarginExchangeInfo(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl.cm_futures","c":"CMAccount","l":"positionInformation(LinkedHashMap<String, Object>)","u":"positionInformation(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl.um_futures","c":"UMAccount","l":"positionInformation(LinkedHashMap<String, Object>)","u":"positionInformation(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl.futures","c":"Account","l":"queryCurrentOpenOrder(LinkedHashMap<String, Object>)","u":"queryCurrentOpenOrder(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl.futures","c":"Account","l":"queryOrder(LinkedHashMap<String, Object>)","u":"queryOrder(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl.futures","c":"Market","l":"setBaseUrl(String)","u":"setBaseUrl(java.lang.String)"},{"p":"com.binance.connector.futures.client.impl.futures","c":"Account","l":"setProductUrl(String)","u":"setProductUrl(java.lang.String)"},{"p":"com.binance.connector.futures.client.impl.futures","c":"Market","l":"setProductUrl(String)","u":"setProductUrl(java.lang.String)"},{"p":"com.binance.connector.futures.client.impl.futures","c":"PortfolioMargin","l":"setProductUrl(String)","u":"setProductUrl(java.lang.String)"},{"p":"com.binance.connector.futures.client.impl.futures","c":"UserData","l":"setProductUrl(String)","u":"setProductUrl(java.lang.String)"},{"p":"com.binance.connector.futures.client.impl","c":"FuturesClientImpl","l":"setProxy(ProxyAuth)","u":"setProxy(com.binance.connector.futures.client.utils.ProxyAuth)"},{"p":"com.binance.connector.futures.client.impl.futures","c":"UserData","l":"setRequestHandler(String, ProxyAuth)","u":"setRequestHandler(java.lang.String,com.binance.connector.futures.client.utils.ProxyAuth)"},{"p":"com.binance.connector.futures.client.impl.futures","c":"Account","l":"setRequestHandler(String, String, ProxyAuth)","u":"setRequestHandler(java.lang.String,java.lang.String,com.binance.connector.futures.client.utils.ProxyAuth)"},{"p":"com.binance.connector.futures.client.impl.futures","c":"Market","l":"setRequestHandler(String, String, ProxyAuth)","u":"setRequestHandler(java.lang.String,java.lang.String,com.binance.connector.futures.client.utils.ProxyAuth)"},{"p":"com.binance.connector.futures.client.impl.futures","c":"PortfolioMargin","l":"setRequestHandler(String, String, ProxyAuth)","u":"setRequestHandler(java.lang.String,java.lang.String,com.binance.connector.futures.client.utils.ProxyAuth)"},{"p":"com.binance.connector.futures.client.impl.futures","c":"Account","l":"setShowLimitUsage(boolean)"},{"p":"com.binance.connector.futures.client.impl.futures","c":"Market","l":"setShowLimitUsage(boolean)"},{"p":"com.binance.connector.futures.client.impl.futures","c":"PortfolioMargin","l":"setShowLimitUsage(boolean)"},{"p":"com.binance.connector.futures.client.impl.futures","c":"UserData","l":"setShowLimitUsage(boolean)"},{"p":"com.binance.connector.futures.client.impl","c":"FuturesClientImpl","l":"setShowLimitUsage(boolean)"},{"p":"com.binance.connector.futures.client.impl","c":"WebsocketClientImpl","l":"symbolTicker(String, WebSocketCallback)","u":"symbolTicker(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)"},{"p":"com.binance.connector.futures.client","c":"WebsocketClient","l":"symbolTicker(String, WebSocketCallback)","u":"symbolTicker(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)"},{"p":"com.binance.connector.futures.client.impl","c":"WebsocketClientImpl","l":"symbolTicker(String, WebSocketCallback, WebSocketCallback, WebSocketCallback, WebSocketCallback)","u":"symbolTicker(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)"},{"p":"com.binance.connector.futures.client","c":"WebsocketClient","l":"symbolTicker(String, WebSocketCallback, WebSocketCallback, WebSocketCallback, WebSocketCallback)","u":"symbolTicker(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)"},{"p":"com.binance.connector.futures.client.impl.um_futures","c":"UMMarket","l":"takerBuySellVol(LinkedHashMap<String, Object>)","u":"takerBuySellVol(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl.cm_futures","c":"CMMarket","l":"ticker24H(LinkedHashMap<String, Object>)","u":"ticker24H(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl.futures","c":"Market","l":"ticker24H(LinkedHashMap<String, Object>)","u":"ticker24H(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl.um_futures","c":"UMMarket","l":"ticker24H(LinkedHashMap<String, Object>)","u":"ticker24H(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl.cm_futures","c":"CMMarket","l":"tickerSymbol(LinkedHashMap<String, Object>)","u":"tickerSymbol(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl.futures","c":"Market","l":"tickerSymbol(LinkedHashMap<String, Object>)","u":"tickerSymbol(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl.um_futures","c":"UMMarket","l":"tickerSymbol(LinkedHashMap<String, Object>)","u":"tickerSymbol(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl.futures","c":"Market","l":"time()"},{"p":"com.binance.connector.futures.client.impl.cm_futures","c":"CMMarket","l":"topTraderLongShortAccs(LinkedHashMap<String, Object>)","u":"topTraderLongShortAccs(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl.futures","c":"Market","l":"topTraderLongShortAccs(LinkedHashMap<String, Object>)","u":"topTraderLongShortAccs(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl.um_futures","c":"UMMarket","l":"topTraderLongShortAccs(LinkedHashMap<String, Object>)","u":"topTraderLongShortAccs(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl.cm_futures","c":"CMMarket","l":"topTraderLongShortPos(LinkedHashMap<String, Object>)","u":"topTraderLongShortPos(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl.futures","c":"Market","l":"topTraderLongShortPos(LinkedHashMap<String, Object>)","u":"topTraderLongShortPos(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl.um_futures","c":"UMMarket","l":"topTraderLongShortPos(LinkedHashMap<String, Object>)","u":"topTraderLongShortPos(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl.futures","c":"Market","l":"trades(LinkedHashMap<String, Object>)","u":"trades(java.util.LinkedHashMap)"},{"p":"com.binance.connector.futures.client.impl.um_futures","c":"UMAccount","l":"UMAccount(String, String, String, boolean, ProxyAuth)","u":"%3Cinit%3E(java.lang.String,java.lang.String,java.lang.String,boolean,com.binance.connector.futures.client.utils.ProxyAuth)"},{"p":"com.binance.connector.futures.client.impl","c":"UMFuturesClientImpl","l":"UMFuturesClientImpl()","u":"%3Cinit%3E()"},{"p":"com.binance.connector.futures.client.impl","c":"UMFuturesClientImpl","l":"UMFuturesClientImpl(String)","u":"%3Cinit%3E(java.lang.String)"},{"p":"com.binance.connector.futures.client.impl","c":"UMFuturesClientImpl","l":"UMFuturesClientImpl(String, boolean)","u":"%3Cinit%3E(java.lang.String,boolean)"},{"p":"com.binance.connector.futures.client.impl","c":"UMFuturesClientImpl","l":"UMFuturesClientImpl(String, String)","u":"%3Cinit%3E(java.lang.String,java.lang.String)"},{"p":"com.binance.connector.futures.client.impl","c":"UMFuturesClientImpl","l":"UMFuturesClientImpl(String, String, boolean)","u":"%3Cinit%3E(java.lang.String,java.lang.String,boolean)"},{"p":"com.binance.connector.futures.client.impl","c":"UMFuturesClientImpl","l":"UMFuturesClientImpl(String, String, String)","u":"%3Cinit%3E(java.lang.String,java.lang.String,java.lang.String)"},{"p":"com.binance.connector.futures.client.impl.um_futures","c":"UMMarket","l":"UMMarket(String, String, String, boolean, ProxyAuth)","u":"%3Cinit%3E(java.lang.String,java.lang.String,java.lang.String,boolean,com.binance.connector.futures.client.utils.ProxyAuth)"},{"p":"com.binance.connector.futures.client.impl.um_futures","c":"UMUserData","l":"UMUserData(String, String, boolean, ProxyAuth)","u":"%3Cinit%3E(java.lang.String,java.lang.String,boolean,com.binance.connector.futures.client.utils.ProxyAuth)"},{"p":"com.binance.connector.futures.client.impl","c":"UMWebsocketClientImpl","l":"UMWebsocketClientImpl()","u":"%3Cinit%3E()"},{"p":"com.binance.connector.futures.client.impl","c":"UMWebsocketClientImpl","l":"UMWebsocketClientImpl(String)","u":"%3Cinit%3E(java.lang.String)"},{"p":"com.binance.connector.futures.client.impl","c":"FuturesClientImpl","l":"unsetProxy()"},{"p":"com.binance.connector.futures.client","c":"FuturesClient","l":"userData()"},{"p":"com.binance.connector.futures.client.impl","c":"CMFuturesClientImpl","l":"userData()"},{"p":"com.binance.connector.futures.client.impl","c":"UMFuturesClientImpl","l":"userData()"},{"p":"com.binance.connector.futures.client.impl.futures","c":"UserData","l":"UserData(String, String, boolean, ProxyAuth)","u":"%3Cinit%3E(java.lang.String,java.lang.String,boolean,com.binance.connector.futures.client.utils.ProxyAuth)"},{"p":"com.binance.connector.futures.client.impl","c":"WebsocketClientImpl","l":"WebsocketClientImpl(String)","u":"%3Cinit%3E(java.lang.String)"}];updateSearchResults();