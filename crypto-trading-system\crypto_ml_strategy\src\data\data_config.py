"""
数据访问配置模块

提供数据访问相关的配置参数、数据结构和常量定义。
支持多种数据源、数据库和缓存配置。

Author: Crypto ML Strategy Team
Date: 2024-12-19
"""

from dataclasses import dataclass, field
from typing import Dict, List, Optional, Union, Any
from enum import Enum
import logging
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)


class DataSourceType(Enum):
    """数据源类型枚举"""
    JAVA_API = "java_api"
    DATABASE = "database"
    KAFKA = "kafka"
    WEBSOCKET = "websocket"
    FILE = "file"
    CACHE = "cache"
    MOCK = "mock"


class DatabaseType(Enum):
    """数据库类型枚举"""
    MYSQL = "mysql"
    POSTGRESQL = "postgresql"
    INFLUXDB = "influxdb"
    MONGODB = "mongodb"
    REDIS = "redis"
    SQLITE = "sqlite"


class CacheType(Enum):
    """缓存类型枚举"""
    MEMORY = "memory"
    REDIS = "redis"
    MEMCACHED = "memcached"
    FILE = "file"


class DataFormat(Enum):
    """数据格式枚举"""
    JSON = "json"
    CSV = "csv"
    PARQUET = "parquet"
    AVRO = "avro"
    PROTOBUF = "protobuf"


@dataclass
class APIConfig:
    """API配置"""
    base_url: str
    api_key: Optional[str] = None
    secret_key: Optional[str] = None
    timeout: int = 30
    max_retries: int = 3
    retry_delay: float = 1.0
    rate_limit: Optional[int] = None  # requests per second
    headers: Dict[str, str] = field(default_factory=dict)
    auth_type: str = "bearer"  # bearer, basic, api_key
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'base_url': self.base_url,
            'api_key': self.api_key,
            'secret_key': self.secret_key,
            'timeout': self.timeout,
            'max_retries': self.max_retries,
            'retry_delay': self.retry_delay,
            'rate_limit': self.rate_limit,
            'headers': self.headers,
            'auth_type': self.auth_type
        }


@dataclass
class DatabaseConfig:
    """数据库配置"""
    db_type: DatabaseType
    host: str
    port: int
    database: str
    username: str
    password: str
    pool_size: int = 10
    max_overflow: int = 20
    pool_timeout: int = 30
    pool_recycle: int = 3600
    ssl_enabled: bool = False
    ssl_cert_path: Optional[str] = None
    connection_params: Dict[str, Any] = field(default_factory=dict)
    
    def get_connection_string(self) -> str:
        """获取连接字符串"""
        try:
            if self.db_type == DatabaseType.MYSQL:
                return f"mysql+pymysql://{self.username}:{self.password}@{self.host}:{self.port}/{self.database}"
            elif self.db_type == DatabaseType.POSTGRESQL:
                return f"postgresql+psycopg2://{self.username}:{self.password}@{self.host}:{self.port}/{self.database}"
            elif self.db_type == DatabaseType.SQLITE:
                return f"sqlite:///{self.database}"
            elif self.db_type == DatabaseType.INFLUXDB:
                return f"influxdb://{self.username}:{self.password}@{self.host}:{self.port}/{self.database}"
            else:
                raise ValueError(f"Unsupported database type: {self.db_type}")
        except Exception as e:
            logger.error(f"Error generating connection string: {e}")
            raise
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'db_type': self.db_type.value,
            'host': self.host,
            'port': self.port,
            'database': self.database,
            'username': self.username,
            'password': '***',  # 隐藏密码
            'pool_size': self.pool_size,
            'max_overflow': self.max_overflow,
            'pool_timeout': self.pool_timeout,
            'pool_recycle': self.pool_recycle,
            'ssl_enabled': self.ssl_enabled,
            'ssl_cert_path': self.ssl_cert_path,
            'connection_params': self.connection_params
        }


@dataclass
class KafkaConfig:
    """Kafka配置"""
    bootstrap_servers: List[str]
    consumer_group: str
    topics: List[str]
    auto_offset_reset: str = "latest"
    enable_auto_commit: bool = True
    auto_commit_interval_ms: int = 5000
    session_timeout_ms: int = 30000
    heartbeat_interval_ms: int = 3000
    max_poll_records: int = 500
    security_protocol: str = "PLAINTEXT"
    sasl_mechanism: Optional[str] = None
    sasl_username: Optional[str] = None
    sasl_password: Optional[str] = None
    ssl_cert_path: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'bootstrap_servers': self.bootstrap_servers,
            'consumer_group': self.consumer_group,
            'topics': self.topics,
            'auto_offset_reset': self.auto_offset_reset,
            'enable_auto_commit': self.enable_auto_commit,
            'auto_commit_interval_ms': self.auto_commit_interval_ms,
            'session_timeout_ms': self.session_timeout_ms,
            'heartbeat_interval_ms': self.heartbeat_interval_ms,
            'max_poll_records': self.max_poll_records,
            'security_protocol': self.security_protocol,
            'sasl_mechanism': self.sasl_mechanism,
            'sasl_username': self.sasl_username,
            'sasl_password': '***' if self.sasl_password else None,
            'ssl_cert_path': self.ssl_cert_path
        }


@dataclass
class CacheConfig:
    """缓存配置"""
    cache_type: CacheType
    host: Optional[str] = None
    port: Optional[int] = None
    password: Optional[str] = None
    db: int = 0
    max_memory: str = "100mb"
    default_ttl: int = 3600  # seconds
    max_connections: int = 10
    socket_timeout: int = 5
    socket_connect_timeout: int = 5
    retry_on_timeout: bool = True
    health_check_interval: int = 30
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'cache_type': self.cache_type.value,
            'host': self.host,
            'port': self.port,
            'password': '***' if self.password else None,
            'db': self.db,
            'max_memory': self.max_memory,
            'default_ttl': self.default_ttl,
            'max_connections': self.max_connections,
            'socket_timeout': self.socket_timeout,
            'socket_connect_timeout': self.socket_connect_timeout,
            'retry_on_timeout': self.retry_on_timeout,
            'health_check_interval': self.health_check_interval
        }


@dataclass
class DataSourceConfig:
    """数据源配置"""
    source_type: DataSourceType
    name: str
    priority: int = 1  # 1=highest, 5=lowest
    enabled: bool = True
    api_config: Optional[APIConfig] = None
    database_config: Optional[DatabaseConfig] = None
    kafka_config: Optional[KafkaConfig] = None
    cache_config: Optional[CacheConfig] = None
    file_path: Optional[str] = None
    data_format: DataFormat = DataFormat.JSON
    refresh_interval: int = 60  # seconds
    health_check_enabled: bool = True
    
    def validate(self) -> bool:
        """验证配置的有效性"""
        try:
            if self.source_type == DataSourceType.JAVA_API and not self.api_config:
                raise ValueError("API config is required for JAVA_API source type")
            
            if self.source_type == DataSourceType.DATABASE and not self.database_config:
                raise ValueError("Database config is required for DATABASE source type")
            
            if self.source_type == DataSourceType.KAFKA and not self.kafka_config:
                raise ValueError("Kafka config is required for KAFKA source type")
            
            if self.source_type == DataSourceType.FILE and not self.file_path:
                raise ValueError("File path is required for FILE source type")
            
            if not (1 <= self.priority <= 5):
                raise ValueError("Priority must be between 1 and 5")
            
            logger.info(f"Data source config validation passed: {self.name}")
            return True
            
        except ValueError as e:
            logger.error(f"Data source config validation failed: {e}")
            return False
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'source_type': self.source_type.value,
            'name': self.name,
            'priority': self.priority,
            'enabled': self.enabled,
            'api_config': self.api_config.to_dict() if self.api_config else None,
            'database_config': self.database_config.to_dict() if self.database_config else None,
            'kafka_config': self.kafka_config.to_dict() if self.kafka_config else None,
            'cache_config': self.cache_config.to_dict() if self.cache_config else None,
            'file_path': self.file_path,
            'data_format': self.data_format.value,
            'refresh_interval': self.refresh_interval,
            'health_check_enabled': self.health_check_enabled
        }


@dataclass
class DataAccessConfig:
    """数据访问配置"""
    data_sources: List[DataSourceConfig] = field(default_factory=list)
    default_cache_ttl: int = 3600
    max_concurrent_requests: int = 10
    request_timeout: int = 30
    enable_compression: bool = True
    enable_encryption: bool = False
    log_level: str = "INFO"
    metrics_enabled: bool = True
    health_check_interval: int = 60
    failover_enabled: bool = True
    circuit_breaker_enabled: bool = True
    circuit_breaker_threshold: int = 5
    circuit_breaker_timeout: int = 60
    
    def add_data_source(self, source_config: DataSourceConfig) -> None:
        """添加数据源"""
        try:
            if source_config.validate():
                self.data_sources.append(source_config)
                logger.info(f"Added data source: {source_config.name}")
            else:
                raise ValueError(f"Invalid data source config: {source_config.name}")
        except Exception as e:
            logger.error(f"Error adding data source: {e}")
            raise
    
    def get_data_source(self, name: str) -> Optional[DataSourceConfig]:
        """获取数据源配置"""
        for source in self.data_sources:
            if source.name == name:
                return source
        return None
    
    def get_enabled_sources(self) -> List[DataSourceConfig]:
        """获取启用的数据源"""
        return [source for source in self.data_sources if source.enabled]
    
    def get_sources_by_type(self, source_type: DataSourceType) -> List[DataSourceConfig]:
        """根据类型获取数据源"""
        return [source for source in self.data_sources if source.source_type == source_type]
    
    def validate(self) -> bool:
        """验证配置的有效性"""
        try:
            if not self.data_sources:
                raise ValueError("At least one data source must be configured")
            
            # 验证所有数据源
            for source in self.data_sources:
                if not source.validate():
                    raise ValueError(f"Invalid data source: {source.name}")
            
            # 检查优先级唯一性
            priorities = [source.priority for source in self.data_sources if source.enabled]
            if len(priorities) != len(set(priorities)):
                logger.warning("Duplicate priorities found in data sources")
            
            logger.info("Data access config validation passed")
            return True
            
        except ValueError as e:
            logger.error(f"Data access config validation failed: {e}")
            return False
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'data_sources': [source.to_dict() for source in self.data_sources],
            'default_cache_ttl': self.default_cache_ttl,
            'max_concurrent_requests': self.max_concurrent_requests,
            'request_timeout': self.request_timeout,
            'enable_compression': self.enable_compression,
            'enable_encryption': self.enable_encryption,
            'log_level': self.log_level,
            'metrics_enabled': self.metrics_enabled,
            'health_check_interval': self.health_check_interval,
            'failover_enabled': self.failover_enabled,
            'circuit_breaker_enabled': self.circuit_breaker_enabled,
            'circuit_breaker_threshold': self.circuit_breaker_threshold,
            'circuit_breaker_timeout': self.circuit_breaker_timeout
        }


# 默认配置
DEFAULT_API_CONFIG = APIConfig(
    base_url="http://localhost:8080/api",
    timeout=30,
    max_retries=3,
    retry_delay=1.0
)

DEFAULT_DATABASE_CONFIG = DatabaseConfig(
    db_type=DatabaseType.MYSQL,
    host="localhost",
    port=3306,
    database="crypto_trading",
    username="root",
    password="password",
    pool_size=10
)

DEFAULT_KAFKA_CONFIG = KafkaConfig(
    bootstrap_servers=["localhost:9092"],
    consumer_group="crypto_ml_strategy",
    topics=["market_data", "trading_signals"]
)

DEFAULT_CACHE_CONFIG = CacheConfig(
    cache_type=CacheType.MEMORY,
    default_ttl=3600,
    max_memory="100mb"
)

DEFAULT_JAVA_API_SOURCE = DataSourceConfig(
    source_type=DataSourceType.JAVA_API,
    name="java_api",
    priority=1,
    api_config=DEFAULT_API_CONFIG,
    cache_config=DEFAULT_CACHE_CONFIG
)

DEFAULT_DATABASE_SOURCE = DataSourceConfig(
    source_type=DataSourceType.DATABASE,
    name="mysql_db",
    priority=2,
    database_config=DEFAULT_DATABASE_CONFIG,
    cache_config=DEFAULT_CACHE_CONFIG
)

DEFAULT_KAFKA_SOURCE = DataSourceConfig(
    source_type=DataSourceType.KAFKA,
    name="kafka_stream",
    priority=3,
    kafka_config=DEFAULT_KAFKA_CONFIG
)

DEFAULT_DATA_ACCESS_CONFIG = DataAccessConfig(
    data_sources=[
        DEFAULT_JAVA_API_SOURCE,
        DEFAULT_DATABASE_SOURCE,
        DEFAULT_KAFKA_SOURCE
    ],
    default_cache_ttl=3600,
    max_concurrent_requests=10,
    failover_enabled=True,
    circuit_breaker_enabled=True
)
