#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
DeepSeek知识蒸馏器

实现DeepSeek知识蒸馏技术，将大型教师模型的知识转移到小型学生模型中，
在保持预测准确性的同时显著减少模型大小和推理时间。
"""

import logging
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from typing import Dict, List, Tuple, Optional, Any, Union
from pathlib import Path
import json
import pickle
from datetime import datetime

from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split


class TeacherModel(nn.Module):
    """教师模型 - 大型复杂模型，用于生成软标签"""
    
    def __init__(self, input_dim: int, hidden_dims: List[int] = None, output_dim: int = 1, dropout_rate: float = 0.3):
        """
        初始化教师模型
        
        Args:
            input_dim: 输入特征维度
            hidden_dims: 隐藏层维度列表
            output_dim: 输出维度
            dropout_rate: Dropout率
        """
        super(TeacherModel, self).__init__()
        
        if hidden_dims is None:
            hidden_dims = [512, 256, 128, 64]
        
        self.input_dim = input_dim
        self.hidden_dims = hidden_dims
        self.output_dim = output_dim
        self.dropout_rate = dropout_rate
        
        # 构建网络层
        layers = []
        prev_dim = input_dim
        
        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.BatchNorm1d(hidden_dim),
                nn.ReLU(),
                nn.Dropout(dropout_rate)
            ])
            prev_dim = hidden_dim
        
        # 输出层
        layers.append(nn.Linear(prev_dim, output_dim))
        
        self.network = nn.Sequential(*layers)
        
        # 注意力机制
        self.attention = nn.MultiheadAttention(
            embed_dim=hidden_dims[-1], 
            num_heads=8, 
            dropout=dropout_rate,
            batch_first=True
        )
        
        # 特征重要性权重
        self.feature_importance = nn.Linear(input_dim, input_dim)
        
    def forward(self, x: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        前向传播
        
        Args:
            x: 输入特征张量
            
        Returns:
            预测结果和中间特征的元组
        """
        batch_size = x.size(0)
        
        # 特征重要性加权
        feature_weights = torch.sigmoid(self.feature_importance(x))
        weighted_x = x * feature_weights
        
        # 通过网络层
        intermediate_features = []
        current_x = weighted_x
        
        for i, layer in enumerate(self.network):
            current_x = layer(current_x)
            # 保存中间特征用于蒸馏
            if isinstance(layer, nn.ReLU) and i < len(self.network) - 1:
                intermediate_features.append(current_x)
        
        # 最终预测
        prediction = current_x
        
        # 如果有足够的特征维度，应用注意力机制
        if len(intermediate_features) > 0:
            # 使用最后一个中间特征进行注意力计算
            last_feature = intermediate_features[-1]
            if last_feature.dim() == 2:
                # 添加序列维度用于注意力计算
                last_feature = last_feature.unsqueeze(1)
                attended_feature, _ = self.attention(last_feature, last_feature, last_feature)
                attended_feature = attended_feature.squeeze(1)
            else:
                attended_feature = last_feature
        else:
            attended_feature = prediction
        
        return prediction, attended_feature


class StudentModel(nn.Module):
    """学生模型 - 小型轻量模型，从教师模型学习"""
    
    def __init__(self, input_dim: int, hidden_dims: List[int] = None, output_dim: int = 1, dropout_rate: float = 0.2):
        """
        初始化学生模型
        
        Args:
            input_dim: 输入特征维度
            hidden_dims: 隐藏层维度列表（比教师模型小）
            output_dim: 输出维度
            dropout_rate: Dropout率
        """
        super(StudentModel, self).__init__()
        
        if hidden_dims is None:
            hidden_dims = [128, 64, 32]  # 比教师模型小
        
        self.input_dim = input_dim
        self.hidden_dims = hidden_dims
        self.output_dim = output_dim
        self.dropout_rate = dropout_rate
        
        # 构建网络层
        layers = []
        prev_dim = input_dim
        
        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.BatchNorm1d(hidden_dim),
                nn.ReLU(),
                nn.Dropout(dropout_rate)
            ])
            prev_dim = hidden_dim
        
        # 输出层
        layers.append(nn.Linear(prev_dim, output_dim))
        
        self.network = nn.Sequential(*layers)
        
    def forward(self, x: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        前向传播
        
        Args:
            x: 输入特征张量
            
        Returns:
            预测结果和中间特征的元组
        """
        intermediate_features = []
        current_x = x
        
        for i, layer in enumerate(self.network):
            current_x = layer(current_x)
            # 保存中间特征用于蒸馏损失计算
            if isinstance(layer, nn.ReLU) and i < len(self.network) - 1:
                intermediate_features.append(current_x)
        
        prediction = current_x
        
        # 返回最后一个中间特征用于特征匹配
        last_feature = intermediate_features[-1] if intermediate_features else prediction
        
        return prediction, last_feature


class DeepSeekDistiller:
    """DeepSeek知识蒸馏器，实现教师-学生模型的知识转移"""
    
    def __init__(self, config: Dict = None):
        """
        初始化DeepSeek蒸馏器
        
        Args:
            config: 配置参数字典
        """
        self.logger = logging.getLogger('cryptoMlStrategy.model.distillation.deepseek')
        
        # 默认配置
        self.config = {
            'teacher_hidden_dims': [512, 256, 128, 64],
            'student_hidden_dims': [128, 64, 32],
            'teacher_dropout': 0.3,
            'student_dropout': 0.2,
            'temperature': 4.0,  # 蒸馏温度
            'alpha': 0.7,  # 蒸馏损失权重
            'beta': 0.3,   # 硬标签损失权重
            'feature_loss_weight': 0.1,  # 特征匹配损失权重
            'learning_rate': 0.001,
            'batch_size': 64,
            'epochs': 100,
            'early_stopping_patience': 10,
            'device': 'cuda' if torch.cuda.is_available() else 'cpu'
        }
        
        # 更新用户配置
        if config:
            self.config.update(config)
        
        self.device = torch.device(self.config['device'])
        
        # 模型实例
        self.teacher_model = None
        self.student_model = None
        self.scaler = StandardScaler()
        
        # 训练历史
        self.training_history = {
            'teacher_loss': [],
            'student_loss': [],
            'distillation_loss': [],
            'feature_loss': [],
            'validation_loss': []
        }
        
        self.logger.info("DeepSeek蒸馏器初始化完成")
    
    def create_models(self, input_dim: int, output_dim: int = 1):
        """
        创建教师和学生模型
        
        Args:
            input_dim: 输入特征维度
            output_dim: 输出维度
        """
        try:
            # 创建教师模型
            self.teacher_model = TeacherModel(
                input_dim=input_dim,
                hidden_dims=self.config['teacher_hidden_dims'],
                output_dim=output_dim,
                dropout_rate=self.config['teacher_dropout']
            ).to(self.device)
            
            # 创建学生模型
            self.student_model = StudentModel(
                input_dim=input_dim,
                hidden_dims=self.config['student_hidden_dims'],
                output_dim=output_dim,
                dropout_rate=self.config['student_dropout']
            ).to(self.device)
            
            self.logger.info(f"模型创建完成 - 教师模型参数: {self._count_parameters(self.teacher_model)}, "
                           f"学生模型参数: {self._count_parameters(self.student_model)}")
            
        except Exception as e:
            self.logger.error(f"模型创建失败: {e}")
            raise
    
    def _count_parameters(self, model: nn.Module) -> int:
        """计算模型参数数量"""
        return sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    def train_teacher(self, X: np.ndarray, y: np.ndarray, 
                     validation_split: float = 0.2) -> Dict[str, Any]:
        """
        训练教师模型
        
        Args:
            X: 训练特征
            y: 训练标签
            validation_split: 验证集比例
            
        Returns:
            训练结果字典
        """
        try:
            if self.teacher_model is None:
                raise ValueError("教师模型未创建，请先调用create_models()")
            
            # 数据预处理
            X_scaled = self.scaler.fit_transform(X)
            X_train, X_val, y_train, y_val = train_test_split(
                X_scaled, y, test_size=validation_split, random_state=42
            )
            
            # 转换为张量
            X_train_tensor = torch.FloatTensor(X_train).to(self.device)
            y_train_tensor = torch.FloatTensor(y_train).to(self.device)
            X_val_tensor = torch.FloatTensor(X_val).to(self.device)
            y_val_tensor = torch.FloatTensor(y_val).to(self.device)
            
            # 优化器和损失函数
            optimizer = optim.Adam(self.teacher_model.parameters(), lr=self.config['learning_rate'])
            criterion = nn.MSELoss()
            scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=5, factor=0.5)
            
            # 训练循环
            best_val_loss = float('inf')
            patience_counter = 0
            
            self.logger.info("开始训练教师模型...")
            
            for epoch in range(self.config['epochs']):
                # 训练阶段
                self.teacher_model.train()
                train_loss = 0.0
                
                for i in range(0, len(X_train_tensor), self.config['batch_size']):
                    batch_X = X_train_tensor[i:i+self.config['batch_size']]
                    batch_y = y_train_tensor[i:i+self.config['batch_size']]
                    
                    optimizer.zero_grad()
                    predictions, _ = self.teacher_model(batch_X)
                    loss = criterion(predictions.squeeze(), batch_y)
                    loss.backward()
                    optimizer.step()
                    
                    train_loss += loss.item()
                
                # 验证阶段
                self.teacher_model.eval()
                with torch.no_grad():
                    val_predictions, _ = self.teacher_model(X_val_tensor)
                    val_loss = criterion(val_predictions.squeeze(), y_val_tensor).item()
                
                # 学习率调度
                scheduler.step(val_loss)
                
                # 记录训练历史
                avg_train_loss = train_loss / (len(X_train_tensor) // self.config['batch_size'] + 1)
                self.training_history['teacher_loss'].append(avg_train_loss)
                self.training_history['validation_loss'].append(val_loss)
                
                # 早停检查
                if val_loss < best_val_loss:
                    best_val_loss = val_loss
                    patience_counter = 0
                    # 保存最佳模型
                    torch.save(self.teacher_model.state_dict(), 'best_teacher_model.pth')
                else:
                    patience_counter += 1
                
                if patience_counter >= self.config['early_stopping_patience']:
                    self.logger.info(f"早停触发，在第{epoch+1}轮停止训练")
                    break
                
                if (epoch + 1) % 10 == 0:
                    self.logger.info(f"Epoch {epoch+1}/{self.config['epochs']}, "
                                   f"Train Loss: {avg_train_loss:.6f}, Val Loss: {val_loss:.6f}")
            
            # 加载最佳模型
            self.teacher_model.load_state_dict(torch.load('best_teacher_model.pth'))
            
            self.logger.info("教师模型训练完成")
            
            return {
                'best_val_loss': best_val_loss,
                'epochs_trained': epoch + 1,
                'final_train_loss': avg_train_loss
            }
            
        except Exception as e:
            self.logger.error(f"教师模型训练失败: {e}")
            raise
    
    def distill_knowledge(self, X: np.ndarray, y: np.ndarray, 
                         validation_split: float = 0.2) -> Dict[str, Any]:
        """
        知识蒸馏过程，训练学生模型
        
        Args:
            X: 训练特征
            y: 训练标签
            validation_split: 验证集比例
            
        Returns:
            蒸馏结果字典
        """
        try:
            if self.teacher_model is None or self.student_model is None:
                raise ValueError("教师或学生模型未创建")
            
            # 数据预处理（使用教师模型训练时的scaler）
            X_scaled = self.scaler.transform(X)
            X_train, X_val, y_train, y_val = train_test_split(
                X_scaled, y, test_size=validation_split, random_state=42
            )
            
            # 转换为张量
            X_train_tensor = torch.FloatTensor(X_train).to(self.device)
            y_train_tensor = torch.FloatTensor(y_train).to(self.device)
            X_val_tensor = torch.FloatTensor(X_val).to(self.device)
            y_val_tensor = torch.FloatTensor(y_val).to(self.device)
            
            # 优化器
            optimizer = optim.Adam(self.student_model.parameters(), lr=self.config['learning_rate'])
            scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=5, factor=0.5)
            
            # 损失函数
            mse_loss = nn.MSELoss()
            kl_loss = nn.KLDivLoss(reduction='batchmean')
            
            # 训练循环
            best_val_loss = float('inf')
            patience_counter = 0
            
            self.logger.info("开始知识蒸馏训练...")
            
            # 设置教师模型为评估模式
            self.teacher_model.eval()
            
            for epoch in range(self.config['epochs']):
                # 训练阶段
                self.student_model.train()
                total_loss = 0.0
                distill_loss_sum = 0.0
                hard_loss_sum = 0.0
                feature_loss_sum = 0.0
                
                for i in range(0, len(X_train_tensor), self.config['batch_size']):
                    batch_X = X_train_tensor[i:i+self.config['batch_size']]
                    batch_y = y_train_tensor[i:i+self.config['batch_size']]
                    
                    optimizer.zero_grad()
                    
                    # 教师模型预测（不计算梯度）
                    with torch.no_grad():
                        teacher_pred, teacher_features = self.teacher_model(batch_X)
                        teacher_soft = F.softmax(teacher_pred / self.config['temperature'], dim=-1)
                    
                    # 学生模型预测
                    student_pred, student_features = self.student_model(batch_X)
                    student_soft = F.log_softmax(student_pred / self.config['temperature'], dim=-1)
                    
                    # 计算损失
                    # 1. 蒸馏损失（软标签）
                    distill_loss = kl_loss(student_soft, teacher_soft) * (self.config['temperature'] ** 2)
                    
                    # 2. 硬标签损失
                    hard_loss = mse_loss(student_pred.squeeze(), batch_y)
                    
                    # 3. 特征匹配损失
                    feature_loss = mse_loss(student_features, teacher_features)
                    
                    # 总损失
                    total_batch_loss = (
                        self.config['alpha'] * distill_loss +
                        self.config['beta'] * hard_loss +
                        self.config['feature_loss_weight'] * feature_loss
                    )
                    
                    total_batch_loss.backward()
                    optimizer.step()
                    
                    total_loss += total_batch_loss.item()
                    distill_loss_sum += distill_loss.item()
                    hard_loss_sum += hard_loss.item()
                    feature_loss_sum += feature_loss.item()
                
                # 验证阶段
                self.student_model.eval()
                with torch.no_grad():
                    val_pred, _ = self.student_model(X_val_tensor)
                    val_loss = mse_loss(val_pred.squeeze(), y_val_tensor).item()
                
                # 学习率调度
                scheduler.step(val_loss)
                
                # 记录训练历史
                num_batches = len(X_train_tensor) // self.config['batch_size'] + 1
                self.training_history['student_loss'].append(total_loss / num_batches)
                self.training_history['distillation_loss'].append(distill_loss_sum / num_batches)
                self.training_history['feature_loss'].append(feature_loss_sum / num_batches)
                
                # 早停检查
                if val_loss < best_val_loss:
                    best_val_loss = val_loss
                    patience_counter = 0
                    # 保存最佳学生模型
                    torch.save(self.student_model.state_dict(), 'best_student_model.pth')
                else:
                    patience_counter += 1
                
                if patience_counter >= self.config['early_stopping_patience']:
                    self.logger.info(f"早停触发，在第{epoch+1}轮停止训练")
                    break
                
                if (epoch + 1) % 10 == 0:
                    self.logger.info(f"Epoch {epoch+1}/{self.config['epochs']}, "
                                   f"Total Loss: {total_loss/num_batches:.6f}, "
                                   f"Val Loss: {val_loss:.6f}")
            
            # 加载最佳学生模型
            self.student_model.load_state_dict(torch.load('best_student_model.pth'))
            
            self.logger.info("知识蒸馏完成")
            
            return {
                'best_val_loss': best_val_loss,
                'epochs_trained': epoch + 1,
                'compression_ratio': self._count_parameters(self.teacher_model) / self._count_parameters(self.student_model)
            }
            
        except Exception as e:
            self.logger.error(f"知识蒸馏失败: {e}")
            raise
    
    def predict(self, X: np.ndarray, use_teacher: bool = False) -> np.ndarray:
        """
        使用模型进行预测
        
        Args:
            X: 输入特征
            use_teacher: 是否使用教师模型
            
        Returns:
            预测结果
        """
        try:
            model = self.teacher_model if use_teacher else self.student_model
            if model is None:
                raise ValueError("模型未创建或训练")
            
            # 数据预处理
            X_scaled = self.scaler.transform(X)
            X_tensor = torch.FloatTensor(X_scaled).to(self.device)
            
            # 预测
            model.eval()
            with torch.no_grad():
                predictions, _ = model(X_tensor)
                return predictions.cpu().numpy().squeeze()
                
        except Exception as e:
            self.logger.error(f"预测失败: {e}")
            raise
    
    def save_models(self, save_dir: str):
        """
        保存模型和配置
        
        Args:
            save_dir: 保存目录
        """
        try:
            save_path = Path(save_dir)
            save_path.mkdir(parents=True, exist_ok=True)
            
            # 保存模型状态
            if self.teacher_model is not None:
                torch.save(self.teacher_model.state_dict(), 
                          save_path / 'teacher_model.pth')
            
            if self.student_model is not None:
                torch.save(self.student_model.state_dict(), 
                          save_path / 'student_model.pth')
            
            # 保存scaler
            with open(save_path / 'scaler.pkl', 'wb') as f:
                pickle.dump(self.scaler, f)
            
            # 保存配置
            with open(save_path / 'config.json', 'w') as f:
                json.dump(self.config, f, indent=2)
            
            # 保存训练历史
            with open(save_path / 'training_history.json', 'w') as f:
                json.dump(self.training_history, f, indent=2)
            
            self.logger.info(f"模型保存到: {save_path}")
            
        except Exception as e:
            self.logger.error(f"模型保存失败: {e}")
            raise
    
    def load_models(self, load_dir: str, input_dim: int, output_dim: int = 1):
        """
        加载模型和配置
        
        Args:
            load_dir: 加载目录
            input_dim: 输入维度
            output_dim: 输出维度
        """
        try:
            load_path = Path(load_dir)
            
            # 加载配置
            with open(load_path / 'config.json', 'r') as f:
                self.config.update(json.load(f))
            
            # 创建模型
            self.create_models(input_dim, output_dim)
            
            # 加载模型状态
            if (load_path / 'teacher_model.pth').exists():
                self.teacher_model.load_state_dict(
                    torch.load(load_path / 'teacher_model.pth', map_location=self.device)
                )
            
            if (load_path / 'student_model.pth').exists():
                self.student_model.load_state_dict(
                    torch.load(load_path / 'student_model.pth', map_location=self.device)
                )
            
            # 加载scaler
            with open(load_path / 'scaler.pkl', 'rb') as f:
                self.scaler = pickle.load(f)
            
            # 加载训练历史
            if (load_path / 'training_history.json').exists():
                with open(load_path / 'training_history.json', 'r') as f:
                    self.training_history = json.load(f)
            
            self.logger.info(f"模型从 {load_path} 加载完成")
            
        except Exception as e:
            self.logger.error(f"模型加载失败: {e}")
            raise
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        获取模型信息
        
        Returns:
            模型信息字典
        """
        info = {
            'config': self.config,
            'teacher_parameters': self._count_parameters(self.teacher_model) if self.teacher_model else 0,
            'student_parameters': self._count_parameters(self.student_model) if self.student_model else 0,
            'compression_ratio': 0,
            'device': str(self.device)
        }
        
        if self.teacher_model and self.student_model:
            info['compression_ratio'] = info['teacher_parameters'] / info['student_parameters']
        
        return info