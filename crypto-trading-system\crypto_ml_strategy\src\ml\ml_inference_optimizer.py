"""
Crypto ML Strategy - ML模型推理优化器

该模块实现了ML模型推理优化功能，包括智能模型缓存、批处理推理、
异步推理引擎、模型预加载和推理性能分析器。

主要功能：
- ModelCacheManager: 智能模型缓存，LRU淘汰和内存管理
- BatchInferenceProcessor: 批处理推理，优化批次大小
- AsyncInferenceEngine: 异步推理处理，线程/进程池
- ModelPreloader: 模型预加载和预热策略
- InferenceProfiler: 实时推理性能监控和瓶颈检测

作者: Crypto ML Strategy Team
日期: 2024-01-20
版本: 1.0.0
"""

import asyncio
import threading
import time
from collections import OrderedDict
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Union, Callable, Tuple
import numpy as np
from infrastructure.logging.logging_core_manager import get_logger
from benchmarks.performance_logging_core import get_global_performance_logger
from infrastructure.error_handling_system import get_global_error_handler
from .memory_efficient_data_structures import MemoryPool, get_global_profiler
import psutil
import weakref


@dataclass
class InferenceMetrics:
    """推理性能指标"""
    model_name: str
    inference_time_ms: float
    preprocessing_time_ms: float
    postprocessing_time_ms: float
    memory_usage_mb: float
    batch_size: int
    accuracy_score: Optional[float] = None
    timestamp: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "model_name": self.model_name,
            "inference_time_ms": self.inference_time_ms,
            "preprocessing_time_ms": self.preprocessing_time_ms,
            "postprocessing_time_ms": self.postprocessing_time_ms,
            "total_time_ms": self.inference_time_ms + self.preprocessing_time_ms + self.postprocessing_time_ms,
            "memory_usage_mb": self.memory_usage_mb,
            "batch_size": self.batch_size,
            "accuracy_score": self.accuracy_score,
            "timestamp": self.timestamp.isoformat()
        }


class ModelCacheManager:
    """智能模型缓存管理器"""
    
    def __init__(self, max_cache_size: int = 5, max_memory_mb: int = 2000):
        self.max_cache_size = max_cache_size
        self.max_memory_mb = max_memory_mb
        self.logger = get_logger(f"{__name__}.ModelCacheManager")
        self.perf_logger = get_global_performance_logger()
        
        self._cache: OrderedDict = OrderedDict()
        self._memory_usage: Dict[str, float] = {}
        self._access_count: Dict[str, int] = {}
        self._lock = threading.RLock()
    
    def get_model(self, model_key: str) -> Optional[Any]:
        """获取缓存的模型"""
        with self._lock:
            if model_key in self._cache:
                # 移动到末尾（最近使用）
                model = self._cache.pop(model_key)
                self._cache[model_key] = model
                self._access_count[model_key] = self._access_count.get(model_key, 0) + 1
                
                self.logger.debug(f"Cache hit for model: {model_key}")
                return model
            
            self.logger.debug(f"Cache miss for model: {model_key}")
            return None
    
    def put_model(self, model_key: str, model: Any, memory_usage_mb: float) -> bool:
        """缓存模型"""
        with self._lock:
            # 检查内存限制
            current_memory = sum(self._memory_usage.values())
            if current_memory + memory_usage_mb > self.max_memory_mb:
                if not self._evict_models(memory_usage_mb):
                    self.logger.warning(f"Cannot cache model {model_key}: memory limit exceeded")
                    return False
            
            # 检查缓存大小限制
            if len(self._cache) >= self.max_cache_size:
                self._evict_lru()
            
            self._cache[model_key] = model
            self._memory_usage[model_key] = memory_usage_mb
            self._access_count[model_key] = 1
            
            self.logger.info(f"Cached model: {model_key} ({memory_usage_mb:.1f}MB)")
            return True
    
    def _evict_models(self, required_memory_mb: float) -> bool:
        """淘汰模型以释放内存"""
        freed_memory = 0
        evicted_models = []
        
        # 按访问次数排序，优先淘汰访问次数少的
        sorted_models = sorted(self._cache.items(), 
                             key=lambda x: self._access_count.get(x[0], 0))
        
        for model_key, _ in sorted_models:
            if freed_memory >= required_memory_mb:
                break
            
            freed_memory += self._memory_usage.get(model_key, 0)
            evicted_models.append(model_key)
        
        for model_key in evicted_models:
            self._remove_model(model_key)
        
        self.logger.info(f"Evicted {len(evicted_models)} models, freed {freed_memory:.1f}MB")
        return freed_memory >= required_memory_mb
    
    def _evict_lru(self) -> None:
        """淘汰最近最少使用的模型"""
        if self._cache:
            model_key = next(iter(self._cache))
            self._remove_model(model_key)
    
    def _remove_model(self, model_key: str) -> None:
        """移除模型"""
        if model_key in self._cache:
            del self._cache[model_key]
            self._memory_usage.pop(model_key, None)
            self._access_count.pop(model_key, None)
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        with self._lock:
            return {
                "cached_models": len(self._cache),
                "total_memory_mb": sum(self._memory_usage.values()),
                "memory_utilization": sum(self._memory_usage.values()) / self.max_memory_mb,
                "access_counts": self._access_count.copy(),
                "model_sizes": self._memory_usage.copy()
            }


class BatchInferenceProcessor:
    """批处理推理处理器"""
    
    def __init__(self, optimal_batch_size: int = 32, max_batch_size: int = 128):
        self.optimal_batch_size = optimal_batch_size
        self.max_batch_size = max_batch_size
        self.logger = get_logger(f"{__name__}.BatchInferenceProcessor")
        self.perf_logger = get_global_performance_logger()
        
        self._pending_requests: List[Tuple[np.ndarray, asyncio.Future]] = []
        self._processing = False
        self._lock = threading.RLock()
    
    async def predict_async(self, model: Any, input_data: np.ndarray) -> np.ndarray:
        """异步预测"""
        future = asyncio.Future()
        
        with self._lock:
            self._pending_requests.append((input_data, future))
            
            if not self._processing:
                self._processing = True
                asyncio.create_task(self._process_batch(model))
        
        return await future
    
    async def _process_batch(self, model: Any) -> None:
        """处理批次"""
        try:
            while True:
                with self._lock:
                    if not self._pending_requests:
                        self._processing = False
                        break
                    
                    # 获取批次
                    batch_size = min(len(self._pending_requests), self.optimal_batch_size)
                    batch_requests = self._pending_requests[:batch_size]
                    self._pending_requests = self._pending_requests[batch_size:]
                
                # 准备批次数据
                batch_inputs = np.array([req[0] for req in batch_requests])
                batch_futures = [req[1] for req in batch_requests]
                
                # 执行批次推理
                start_time = time.time()
                try:
                    if hasattr(model, 'predict'):
                        batch_outputs = model.predict(batch_inputs)
                    else:
                        batch_outputs = model(batch_inputs)
                    
                    inference_time = (time.time() - start_time) * 1000
                    
                    # 分发结果
                    for i, future in enumerate(batch_futures):
                        if not future.cancelled():
                            future.set_result(batch_outputs[i])
                    
                    self.logger.debug(f"Processed batch of {batch_size} in {inference_time:.1f}ms")
                    
                except Exception as e:
                    # 分发错误
                    for future in batch_futures:
                        if not future.cancelled():
                            future.set_exception(e)
                    
                    self.logger.error(f"Batch inference failed: {e}")
                
                # 短暂等待以收集更多请求
                await asyncio.sleep(0.001)
                
        except Exception as e:
            self.logger.error(f"Batch processing error: {e}")
            self._processing = False


class AsyncInferenceEngine:
    """异步推理引擎"""
    
    def __init__(self, max_workers: int = 4):
        self.max_workers = max_workers
        self.logger = get_logger(f"{__name__}.AsyncInferenceEngine")
        self.perf_logger = get_global_performance_logger()
        self.error_handler = get_global_error_handler()
        
        self._executor = None
        self._active_tasks: weakref.WeakSet = weakref.WeakSet()
        self._lock = threading.RLock()
    
    async def infer_async(self, model: Any, input_data: np.ndarray, 
                         preprocess_func: Optional[Callable] = None,
                         postprocess_func: Optional[Callable] = None) -> Any:
        """异步推理"""
        try:
            start_time = time.time()
            
            # 预处理
            if preprocess_func:
                input_data = await self._run_in_executor(preprocess_func, input_data)
            
            preprocess_time = (time.time() - start_time) * 1000
            
            # 推理
            inference_start = time.time()
            if hasattr(model, 'predict'):
                result = await self._run_in_executor(model.predict, input_data)
            else:
                result = await self._run_in_executor(model, input_data)
            
            inference_time = (time.time() - inference_start) * 1000
            
            # 后处理
            postprocess_start = time.time()
            if postprocess_func:
                result = await self._run_in_executor(postprocess_func, result)
            
            postprocess_time = (time.time() - postprocess_start) * 1000
            
            total_time = (time.time() - start_time) * 1000
            
            self.logger.debug(f"Async inference completed in {total_time:.1f}ms "
                            f"(preprocess: {preprocess_time:.1f}ms, "
                            f"inference: {inference_time:.1f}ms, "
                            f"postprocess: {postprocess_time:.1f}ms)")
            
            return result
            
        except Exception as e:
            self.error_handler.handle_error(e, context={"operation": "async_inference"})
            raise
    
    async def _run_in_executor(self, func: Callable, *args) -> Any:
        """在执行器中运行函数"""
        loop = asyncio.get_event_loop()
        
        if self._executor is None:
            import concurrent.futures
            self._executor = concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers)
        
        return await loop.run_in_executor(self._executor, func, *args)
    
    def shutdown(self) -> None:
        """关闭推理引擎"""
        if self._executor:
            self._executor.shutdown(wait=True)
            self._executor = None


# 全局实例
_global_cache_manager: Optional[ModelCacheManager] = None
_global_inference_engine: Optional[AsyncInferenceEngine] = None


def get_global_cache_manager() -> ModelCacheManager:
    """获取全局模型缓存管理器"""
    global _global_cache_manager
    
    if _global_cache_manager is None:
        _global_cache_manager = ModelCacheManager()
    
    return _global_cache_manager


def get_global_inference_engine() -> AsyncInferenceEngine:
    """获取全局异步推理引擎"""
    global _global_inference_engine
    
    if _global_inference_engine is None:
        _global_inference_engine = AsyncInferenceEngine()
    
    return _global_inference_engine


class MLInferenceOptimizer:
    """
    机器学习推理优化器
    
    这是ML推理系统的核心优化器，提供了完整的推理优化功能，
    包括模型缓存、批处理、异步推理和性能监控。
    
    主要功能：
    - 智能模型缓存管理
    - 批处理推理优化
    - 异步推理引擎
    - 性能指标收集
    - 内存使用优化
    
    使用示例：
        optimizer = MLInferenceOptimizer()
        await optimizer.initialize()
        result = await optimizer.predict_async(model, input_data)
        metrics = optimizer.get_performance_metrics()
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化ML推理优化器
        
        Args:
            config: 配置字典，包含缓存大小、批处理设置等
        """
        self.config = config or {}
        self.logger = get_logger(f"{__name__}.MLInferenceOptimizer")
        self.perf_logger = get_global_performance_logger()
        self.error_handler = get_global_error_handler()
        
        # 初始化组件
        self.cache_manager = get_global_cache_manager()
        self.inference_engine = get_global_inference_engine()
        self.memory_profiler = get_global_profiler()
        
        # 性能指标
        self._metrics: List[InferenceMetrics] = []
        self._total_inferences = 0
        self._total_cache_hits = 0
        self._lock = threading.RLock()
        
        # 批处理器
        self._batch_processors: Dict[str, BatchInferenceProcessor] = {}
        
        logger.info("MLInferenceOptimizer initialized")
    
    async def initialize(self) -> bool:
        """
        初始化优化器
        
        Returns:
            是否初始化成功
        """
        try:
            self.logger.info("初始化ML推理优化器")
            
            # 配置缓存管理器
            cache_config = self.config.get("cache", {})
            if "max_cache_size" in cache_config:
                self.cache_manager.max_cache_size = cache_config["max_cache_size"]
            if "max_memory_mb" in cache_config:
                self.cache_manager.max_memory_mb = cache_config["max_memory_mb"]
            
            # 初始化批处理器
            batch_config = self.config.get("batch", {})
            default_batch_size = batch_config.get("optimal_batch_size", 32)
            max_batch_size = batch_config.get("max_batch_size", 128)
            
            self._batch_processors["default"] = BatchInferenceProcessor(
                optimal_batch_size=default_batch_size,
                max_batch_size=max_batch_size
            )
            
            # 启动内存监控
            self.memory_profiler.take_snapshot()
            
            self.logger.info("ML推理优化器初始化完成")
            return True
            
        except Exception as e:
            self.logger.error(f"ML推理优化器初始化失败: {e}")
            return False
    
    async def predict_async(self, model: Any, input_data: np.ndarray,
                           model_key: Optional[str] = None,
                           use_cache: bool = True,
                           use_batch: bool = False,
                           preprocess_func: Optional[Callable] = None,
                           postprocess_func: Optional[Callable] = None) -> Any:
        """
        异步预测
        
        Args:
            model: 模型对象
            input_data: 输入数据
            model_key: 模型缓存键
            use_cache: 是否使用缓存
            use_batch: 是否使用批处理
            preprocess_func: 预处理函数
            postprocess_func: 后处理函数
            
        Returns:
            预测结果
        """
        try:
            start_time = time.time()
            
            # 检查模型缓存
            cached_model = None
            if use_cache and model_key:
                cached_model = self.cache_manager.get_model(model_key)
                if cached_model:
                    model = cached_model
                    self._total_cache_hits += 1
                    self.logger.debug(f"使用缓存模型: {model_key}")
            
            # 选择推理方式
            if use_batch:
                processor = self._batch_processors.get("default")
                if processor:
                    result = await processor.predict_async(model, input_data)
                else:
                    result = await self.inference_engine.infer_async(
                        model, input_data, preprocess_func, postprocess_func
                    )
            else:
                result = await self.inference_engine.infer_async(
                    model, input_data, preprocess_func, postprocess_func
                )
            
            # 缓存模型（如果需要）
            if use_cache and model_key and not cached_model:
                model_memory = self._estimate_model_memory(model)
                self.cache_manager.put_model(model_key, model, model_memory)
            
            # 记录性能指标
            total_time = (time.time() - start_time) * 1000
            self._record_inference_metrics(
                model_key or "unknown",
                total_time,
                input_data.shape[0] if hasattr(input_data, 'shape') else 1
            )
            
            self._total_inferences += 1
            return result
            
        except Exception as e:
            self.logger.error(f"异步预测失败: {e}")
            await self.error_handler.handle_error(e, context={
                "operation": "predict_async",
                "model_key": model_key,
                "use_cache": use_cache,
                "use_batch": use_batch
            })
            raise
    
    def predict_sync(self, model: Any, input_data: np.ndarray,
                    model_key: Optional[str] = None,
                    use_cache: bool = True) -> Any:
        """
        同步预测
        
        Args:
            model: 模型对象
            input_data: 输入数据
            model_key: 模型缓存键
            use_cache: 是否使用缓存
            
        Returns:
            预测结果
        """
        try:
            start_time = time.time()
            
            # 检查模型缓存
            cached_model = None
            if use_cache and model_key:
                cached_model = self.cache_manager.get_model(model_key)
                if cached_model:
                    model = cached_model
                    self._total_cache_hits += 1
                    self.logger.debug(f"使用缓存模型: {model_key}")
            
            # 执行预测
            if hasattr(model, 'predict'):
                result = model.predict(input_data)
            else:
                result = model(input_data)
            
            # 缓存模型（如果需要）
            if use_cache and model_key and not cached_model:
                model_memory = self._estimate_model_memory(model)
                self.cache_manager.put_model(model_key, model, model_memory)
            
            # 记录性能指标
            total_time = (time.time() - start_time) * 1000
            self._record_inference_metrics(
                model_key or "unknown",
                total_time,
                input_data.shape[0] if hasattr(input_data, 'shape') else 1
            )
            
            self._total_inferences += 1
            return result
            
        except Exception as e:
            self.logger.error(f"同步预测失败: {e}")
            raise
    
    def _estimate_model_memory(self, model: Any) -> float:
        """
        估算模型内存使用量
        
        Args:
            model: 模型对象
            
        Returns:
            内存使用量（MB）
        """
        try:
            # 尝试获取模型参数大小
            if hasattr(model, 'parameters'):
                # PyTorch模型
                total_params = sum(p.numel() for p in model.parameters())
                # 假设每个参数4字节（float32）
                memory_mb = (total_params * 4) / (1024 * 1024)
            elif hasattr(model, 'count_params'):
                # Keras/TensorFlow模型
                total_params = model.count_params()
                memory_mb = (total_params * 4) / (1024 * 1024)
            else:
                # 使用系统内存估算
                import sys
                memory_mb = sys.getsizeof(model) / (1024 * 1024)
            
            return max(memory_mb, 10.0)  # 最小10MB
            
        except Exception as e:
            self.logger.warning(f"无法估算模型内存: {e}")
            return 50.0  # 默认50MB
    
    def _record_inference_metrics(self, model_name: str, total_time_ms: float, batch_size: int) -> None:
        """
        记录推理指标
        
        Args:
            model_name: 模型名称
            total_time_ms: 总时间（毫秒）
            batch_size: 批次大小
        """
        try:
            with self._lock:
                # 获取内存使用情况
                process = psutil.Process()
                memory_mb = process.memory_info().rss / (1024 * 1024)
                
                metrics = InferenceMetrics(
                    model_name=model_name,
                    inference_time_ms=total_time_ms,
                    preprocessing_time_ms=0.0,  # 简化版本
                    postprocessing_time_ms=0.0,  # 简化版本
                    memory_usage_mb=memory_mb,
                    batch_size=batch_size
                )
                
                self._metrics.append(metrics)
                
                # 保留最近1000个指标
                if len(self._metrics) > 1000:
                    self._metrics = self._metrics[-1000:]
                
        except Exception as e:
            self.logger.warning(f"记录推理指标失败: {e}")
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """
        获取性能指标
        
        Returns:
            性能指标字典
        """
        try:
            with self._lock:
                if not self._metrics:
                    return {"error": "No metrics available"}
                
                # 计算统计信息
                inference_times = [m.inference_time_ms for m in self._metrics]
                memory_usages = [m.memory_usage_mb for m in self._metrics]
                
                cache_hit_rate = (self._total_cache_hits / max(self._total_inferences, 1)) * 100
                
                return {
                    "total_inferences": self._total_inferences,
                    "cache_hit_rate": cache_hit_rate,
                    "average_inference_time_ms": np.mean(inference_times),
                    "median_inference_time_ms": np.median(inference_times),
                    "p95_inference_time_ms": np.percentile(inference_times, 95),
                    "average_memory_mb": np.mean(memory_usages),
                    "peak_memory_mb": np.max(memory_usages),
                    "cache_stats": self.cache_manager.get_cache_stats(),
                    "memory_trend": self.memory_profiler.analyze_memory_trend(),
                    "recent_metrics_count": len(self._metrics)
                }
                
        except Exception as e:
            self.logger.error(f"获取性能指标失败: {e}")
            return {"error": "Failed to get metrics"}
    
    def optimize_for_throughput(self) -> None:
        """优化吞吐量设置"""
        try:
            # 增加批处理大小
            for processor in self._batch_processors.values():
                processor.optimal_batch_size = min(processor.optimal_batch_size * 2, 256)
            
            # 增加缓存大小
            self.cache_manager.max_cache_size = min(self.cache_manager.max_cache_size * 2, 20)
            
            self.logger.info("已优化为高吞吐量模式")
            
        except Exception as e:
            self.logger.error(f"吞吐量优化失败: {e}")
    
    def optimize_for_latency(self) -> None:
        """优化延迟设置"""
        try:
            # 减少批处理大小
            for processor in self._batch_processors.values():
                processor.optimal_batch_size = max(processor.optimal_batch_size // 2, 1)
            
            # 预热缓存
            self.cache_manager.max_cache_size = min(self.cache_manager.max_cache_size + 5, 15)
            
            self.logger.info("已优化为低延迟模式")
            
        except Exception as e:
            self.logger.error(f"延迟优化失败: {e}")
    
    def get_optimization_recommendations(self) -> List[str]:
        """
        获取优化建议
        
        Returns:
            优化建议列表
        """
        try:
            recommendations = []
            metrics = self.get_performance_metrics()
            
            if isinstance(metrics, dict) and "error" not in metrics:
                # 基于缓存命中率的建议
                cache_hit_rate = metrics.get("cache_hit_rate", 0)
                if cache_hit_rate < 50:
                    recommendations.append("缓存命中率较低，建议增加缓存大小或优化缓存策略")
                
                # 基于推理时间的建议
                avg_time = metrics.get("average_inference_time_ms", 0)
                if avg_time > 100:
                    recommendations.append("平均推理时间较长，建议启用批处理或模型优化")
                
                # 基于内存使用的建议
                memory_trend = metrics.get("memory_trend", {})
                if isinstance(memory_trend, dict):
                    trend = memory_trend.get("memory_trend_mb_per_snapshot", 0)
                    if trend > 1.0:
                        recommendations.append("检测到内存使用持续增长，建议检查内存泄漏")
            
            if not recommendations:
                recommendations.append("当前性能表现良好，无需特殊优化")
            
            return recommendations
            
        except Exception as e:
            self.logger.error(f"获取优化建议失败: {e}")
            return ["无法生成优化建议"]
    
    def shutdown(self) -> None:
        """关闭优化器"""
        try:
            self.logger.info("关闭ML推理优化器")
            
            # 关闭推理引擎
            self.inference_engine.shutdown()
            
            # 清理缓存
            self.cache_manager._cache.clear()
            
            # 清理指标
            with self._lock:
                self._metrics.clear()
            
            self.logger.info("ML推理优化器已关闭")
            
        except Exception as e:
            self.logger.error(f"关闭ML推理优化器失败: {e}")


# 全局ML推理优化器实例
_global_ml_optimizer: Optional[MLInferenceOptimizer] = None


def get_global_ml_optimizer() -> MLInferenceOptimizer:
    """获取全局ML推理优化器实例"""
    global _global_ml_optimizer
    
    if _global_ml_optimizer is None:
        _global_ml_optimizer = MLInferenceOptimizer()
    
    return _global_ml_optimizer


# 模块级别的日志器
module_logger = get_logger(__name__)