package com.crypto.trading.common.util;

import com.crypto.trading.common.constant.SystemConstants;
import org.apache.commons.lang3.StringUtils;

import java.util.UUID;
import java.util.regex.Pattern;

/**
 * 字符串工具类
 * <p>
 * 提供字符串相关的工具方法，如判空、截取、生成等
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public final class StringUtil {

    /**
     * 私有构造函数，防止实例化
     */
    private StringUtil() {
        throw new IllegalStateException("工具类不允许实例化");
    }

    /**
     * 邮箱正则表达式
     */
    private static final Pattern EMAIL_PATTERN = Pattern.compile("^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\\.[a-zA-Z0-9_-]+)+$");

    /**
     * 手机号正则表达式（中国大陆）
     */
    private static final Pattern MOBILE_PATTERN = Pattern.compile("^1[3-9]\\d{9}$");

    /**
     * 判断字符串是否为空
     *
     * @param str 字符串
     * @return 是否为空
     */
    public static boolean isEmpty(String str) {
        return str == null || str.isEmpty();
    }

    /**
     * 判断字符串是否不为空
     *
     * @param str 字符串
     * @return 是否不为空
     */
    public static boolean isNotEmpty(String str) {
        return !isEmpty(str);
    }

    /**
     * 判断字符串是否为空白
     *
     * @param str 字符串
     * @return 是否为空白
     */
    public static boolean isBlank(String str) {
        return StringUtils.isBlank(str);
    }

    /**
     * 判断字符串是否不为空白
     *
     * @param str 字符串
     * @return 是否不为空白
     */
    public static boolean isNotBlank(String str) {
        return !isBlank(str);
    }

    /**
     * 获取字符串长度
     *
     * @param str 字符串
     * @return 长度
     */
    public static int length(String str) {
        return str == null ? 0 : str.length();
    }

    /**
     * 截取字符串
     *
     * @param str   字符串
     * @param start 开始位置（包含）
     * @param end   结束位置（不包含）
     * @return 截取后的字符串
     */
    public static String substring(String str, int start, int end) {
        if (str == null) {
            return null;
        }
        if (start < 0) {
            start = 0;
        }
        if (end > str.length()) {
            end = str.length();
        }
        if (start > end) {
            return SystemConstants.EMPTY;
        }
        return str.substring(start, end);
    }

    /**
     * 从头截取字符串
     *
     * @param str 字符串
     * @param end 结束位置（不包含）
     * @return 截取后的字符串
     */
    public static String substringTo(String str, int end) {
        return substring(str, 0, end);
    }

    /**
     * 从指定位置截取到末尾
     *
     * @param str   字符串
     * @param start 开始位置（包含）
     * @return 截取后的字符串
     */
    public static String substringFrom(String str, int start) {
        if (str == null) {
            return null;
        }
        if (start < 0) {
            start = 0;
        }
        if (start > str.length()) {
            return SystemConstants.EMPTY;
        }
        return str.substring(start);
    }

    /**
     * 生成UUID
     *
     * @return UUID字符串
     */
    public static String uuid() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 生成短UUID
     *
     * @return 短UUID字符串
     */
    public static String shortUuid() {
        return uuid().substring(0, 16);
    }

    /**
     * 格式化字符串
     *
     * @param template 模板
     * @param args     参数
     * @return 格式化后的字符串
     */
    public static String format(String template, Object... args) {
        return String.format(template, args);
    }

    /**
     * 判断字符串是否为数字
     *
     * @param str 字符串
     * @return 是否为数字
     */
    public static boolean isNumeric(String str) {
        return StringUtils.isNumeric(str);
    }

    /**
     * 判断字符串是否为字母
     *
     * @param str 字符串
     * @return 是否为字母
     */
    public static boolean isAlpha(String str) {
        return StringUtils.isAlpha(str);
    }

    /**
     * 判断字符串是否为字母或数字
     *
     * @param str 字符串
     * @return 是否为字母或数字
     */
    public static boolean isAlphanumeric(String str) {
        return StringUtils.isAlphanumeric(str);
    }

    /**
     * 首字母大写
     *
     * @param str 字符串
     * @return 首字母大写后的字符串
     */
    public static String capitalize(String str) {
        return StringUtils.capitalize(str);
    }

    /**
     * 首字母小写
     *
     * @param str 字符串
     * @return 首字母小写后的字符串
     */
    public static String uncapitalize(String str) {
        return StringUtils.uncapitalize(str);
    }

    /**
     * 驼峰转下划线
     *
     * @param str 驼峰字符串
     * @return 下划线字符串
     */
    public static String camelToUnderscore(String str) {
        if (isEmpty(str)) {
            return str;
        }
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < str.length(); i++) {
            char c = str.charAt(i);
            if (Character.isUpperCase(c)) {
                if (i > 0) {
                    sb.append('_');
                }
                sb.append(Character.toLowerCase(c));
            } else {
                sb.append(c);
            }
        }
        return sb.toString();
    }

    /**
     * 下划线转驼峰
     *
     * @param str 下划线字符串
     * @return 驼峰字符串
     */
    public static String underscoreToCamel(String str) {
        if (isEmpty(str)) {
            return str;
        }
        StringBuilder sb = new StringBuilder();
        boolean upperCase = false;
        for (int i = 0; i < str.length(); i++) {
            char c = str.charAt(i);
            if (c == '_') {
                upperCase = true;
            } else if (upperCase) {
                sb.append(Character.toUpperCase(c));
                upperCase = false;
            } else {
                sb.append(c);
            }
        }
        return sb.toString();
    }

    /**
     * 判断字符串是否为邮箱格式
     *
     * @param email 邮箱字符串
     * @return 是否为邮箱格式
     */
    public static boolean isEmail(String email) {
        return isNotEmpty(email) && EMAIL_PATTERN.matcher(email).matches();
    }

    /**
     * 判断字符串是否为手机号格式（中国大陆）
     *
     * @param mobile 手机号字符串
     * @return 是否为手机号格式
     */
    public static boolean isMobile(String mobile) {
        return isNotEmpty(mobile) && MOBILE_PATTERN.matcher(mobile).matches();
    }

    /**
     * 隐藏手机号中间四位
     *
     * @param mobile 手机号
     * @return 隐藏后的手机号
     */
    public static String maskMobile(String mobile) {
        if (isEmpty(mobile) || mobile.length() < 11) {
            return mobile;
        }
        return mobile.substring(0, 3) + "****" + mobile.substring(7);
    }

    /**
     * 隐藏邮箱用户名部分
     *
     * @param email 邮箱
     * @return 隐藏后的邮箱
     */
    public static String maskEmail(String email) {
        if (isEmpty(email) || !email.contains("@")) {
            return email;
        }
        int atIndex = email.indexOf('@');
        if (atIndex <= 1) {
            return email;
        }
        String username = email.substring(0, atIndex);
        String domain = email.substring(atIndex);
        if (username.length() <= 2) {
            return "*" + username.substring(1) + domain;
        }
        return username.charAt(0) + "***" + username.substring(username.length() - 1) + domain;
    }

    /**
     * 生成指定长度的随机字符串
     *
     * @param length 长度
     * @return 随机字符串
     */
    public static String random(int length) {
        if (length <= 0) {
            return SystemConstants.EMPTY;
        }
        String base = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < length; i++) {
            int number = (int) (Math.random() * base.length());
            sb.append(base.charAt(number));
        }
        return sb.toString();
    }
    
    /**
     * 生成UUID（无连字符）
     *
     * @return UUID字符串
     */
    public static String generateUuid() {
        return uuid();
    }

    /**
     * 生成UUID（带连字符）
     *
     * @return UUID字符串
     */
    public static String generateUuidWithHyphens() {
        return UUID.randomUUID().toString();
    }
    
    /**
     * 将字符串转换为驼峰命名
     *
     * @param str 字符串
     * @return 驼峰命名字符串
     */
    public static String toCamelCase(String str) {
        return underscoreToCamel(str);
    }

    /**
     * 将字符串转换为下划线命名
     *
     * @param str 字符串
     * @return 下划线命名字符串
     */
    public static String toUnderlineCase(String str) {
        return camelToUnderscore(str);
    }
    
    /**
     * 将字符串数组连接为一个字符串
     *
     * @param array     字符串数组
     * @param separator 分隔符
     * @return 连接后的字符串
     */
    public static String join(String[] array, String separator) {
        return StringUtils.join(array, separator);
    }
    
    /**
     * 去除字符串两端的空白字符
     *
     * @param str 字符串
     * @return 去除两端空白字符后的字符串
     */
    public static String trim(String str) {
        return str == null ? null : str.trim();
    }
    
    /**
     * 将对象转换为字符串，如果对象为null则返回空字符串
     *
     * @param obj 对象
     * @return 字符串
     */
    public static String toString(Object obj) {
        return obj == null ? SystemConstants.EMPTY : obj.toString();
    }

    /**
     * 将对象转换为字符串，如果对象为null则返回指定的默认值
     *
     * @param obj          对象
     * @param defaultValue 默认值
     * @return 字符串
     */
    public static String toString(Object obj, String defaultValue) {
        return obj == null ? defaultValue : obj.toString();
    }
    
    /**
     * 将字符串转换为指定长度，不足则在左侧补充指定字符
     *
     * @param str     字符串
     * @param size    长度
     * @param padChar 补充字符
     * @return 转换后的字符串
     */
    public static String leftPad(String str, int size, char padChar) {
        return StringUtils.leftPad(str, size, padChar);
    }

    /**
     * 将字符串转换为指定长度，不足则在右侧补充指定字符
     *
     * @param str     字符串
     * @param size    长度
     * @param padChar 补充字符
     * @return 转换后的字符串
     */
    public static String rightPad(String str, int size, char padChar) {
        return StringUtils.rightPad(str, size, padChar);
    }
    
    /**
     * 判断字符串是否匹配正则表达式
     *
     * @param str   字符串
     * @param regex 正则表达式
     * @return 是否匹配
     */
    public static boolean matches(String str, String regex) {
        return str != null && str.matches(regex);
    }
}