package com.crypto.trading.common.dto.trade;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * OrderStatusDTO单元测试类
 */
class OrderStatusDTOTest {

    /**
     * 测试默认构造函数
     */
    @Test
    void testDefaultConstructor() {
        OrderStatusDTO dto = new OrderStatusDTO();
        assertNotNull(dto);
    }

    /**
     * 测试getter和setter方法
     */
    @Test
    void testGettersAndSetters() {
        OrderStatusDTO dto = new OrderStatusDTO();

        String orderId = "987654321";
        dto.setOrderId(orderId);
        assertEquals(orderId, dto.getOrderId());

        String clientOrderId = "client-order-456";
        dto.setClientOrderId(clientOrderId);
        assertEquals(clientOrderId, dto.getClientOrderId());

        String symbol = "ETHUSDT";
        dto.setSymbol(symbol);
        assertEquals(symbol, dto.getSymbol());

        String status = "FILLED";
        dto.setStatus(status);
        assertEquals(status, dto.getStatus());

        Double executedQuantity = 2.0;
        dto.setExecutedQuantity(executedQuantity);
        assertEquals(executedQuantity, dto.getExecutedQuantity());

        Double executedPrice = 3000.0;
        dto.setExecutedPrice(executedPrice);
        assertEquals(executedPrice, dto.getExecutedPrice());

        String errorCode = "API_ERROR";
        dto.setErrorCode(errorCode);
        assertEquals(errorCode, dto.getErrorCode());

        String errorMessage = "Something went wrong";
        dto.setErrorMessage(errorMessage);
        assertEquals(errorMessage, dto.getErrorMessage());

        Long updatedTime = System.currentTimeMillis();
        dto.setUpdatedTime(updatedTime);
        assertEquals(updatedTime, dto.getUpdatedTime());

        String rawData = "{\"price\":\"3000.00\",\"qty\":\"2.0\"}";
        dto.setRawData(rawData);
        assertEquals(rawData, dto.getRawData());
    }

    /**
     * 测试toString方法
     */
    @Test
    void testToString() {
        OrderStatusDTO dto = new OrderStatusDTO();
        dto.setOrderId("123456789");
        dto.setSymbol("BTCUSDT");
        dto.setStatus("FILLED");
        dto.setExecutedQuantity(1.0);
        dto.setExecutedPrice(50000.0);

        String toString = dto.toString();
        
        // 因为实际类没有重写toString方法，所以只能验证基本的对象表示
        assertNotNull(toString);
        assertTrue(toString.contains("OrderStatusDTO"));
    }
} 