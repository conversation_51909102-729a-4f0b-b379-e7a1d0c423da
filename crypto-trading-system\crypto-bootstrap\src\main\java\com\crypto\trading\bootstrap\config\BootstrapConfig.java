package com.crypto.trading.bootstrap.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.FilterType;

import com.crypto.trading.bootstrap.config.adapter.BinanceApiConfigAdapter;
import com.crypto.trading.sdk.config.BinanceApiConfig;

/**
 * 启动模块配置类
 * 负责配置启动模块的组件扫描和相关配置
 */
@Configuration
@ComponentScan(
    basePackages = {
        "com.crypto.trading.bootstrap", 
        "com.crypto.trading.common", 
        "com.crypto.trading.sdk",
        "com.crypto.trading.market"
    },
    excludeFilters = {
        @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com\\.crypto\\.trading\\.common\\.config\\.AppConfig"),
        @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com\\.crypto\\.trading\\.common\\.config\\.BinanceConfig"),
        // 已移除对market模块InfluxDBConfig的排除，确保其可以被正确扫描到
        @ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE, classes = BinanceApiConfig.class),
        @ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE, classes = com.crypto.trading.market.config.MarketDataConfig.class)
    }
)
@EnableConfigurationProperties
@Import({
    AppConfig.class,
    com.crypto.trading.bootstrap.config.BinanceApiConfig.class,
    MarketDataConfig.class,
    JacksonConfig.class,
    BinanceClientConfig.class,
    BinanceApiConfigAdapter.class,
    com.crypto.trading.bootstrap.config.adapter.InfluxDBConfigAdapter.class,
    com.crypto.trading.bootstrap.config.adapter.MarketDataConfigAdapter.class,
    com.crypto.trading.bootstrap.config.adapter.InfluxDBConfigBridge.class,
    com.crypto.trading.bootstrap.config.adapter.MarketDataConfigBridge.class
    // 已从排除过滤器中排除market模块的InfluxDBConfig，使用market模块的InfluxDBConfig实现
})
public class BootstrapConfig {
    
    private static final Logger log = LoggerFactory.getLogger(BootstrapConfig.class);
    
    /**
     * 构造函数
     */
    public BootstrapConfig() {
        log.info("加载启动模块配置...");
        log.info("配置文件已整合至bootstrap模块");
        log.info("已排除common模块中的冲突配置类");
        log.info("已添加SDK配置类适配器");
    }
}