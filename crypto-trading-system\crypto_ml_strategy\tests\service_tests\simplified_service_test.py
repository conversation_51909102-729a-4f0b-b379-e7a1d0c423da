#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
简化基础服务测试

避免实际连接外部服务，专注测试服务类的实例化和基本功能
"""

import sys
import os
import time

# 添加src目录到Python路径
src_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 'src')
sys.path.insert(0, src_path)

def test_config_service():
    """测试配置服务"""
    try:
        from core.config import Config
        config = Config()
        # 测试基本配置读取
        result = config.get('strategy', 'symbols', default=['BTCUSDT'])
        print(f"PASS Config Service: symbols={result}")
        return True
    except Exception as e:
        print(f"FAIL Config Service: {e}")
        return False

def test_kafka_client():
    """测试Kafka客户端（不实际连接）"""
    try:
        from api.kafka_client import KafkaClient
        from core.config import Config
        
        config = Config()
        # 仅测试实例化，不连接
        client = KafkaClient(config)
        print("PASS Kafka Client: instantiation successful")
        return True
    except Exception as e:
        print(f"FAIL Kafka Client: {e}")
        return False

def test_database_clients():
    """测试数据库客户端（不实际连接）"""
    try:
        from data.clients.influxdb_client import InfluxDBClient
        from data.clients.mysql_client import MySQLClient
        from core.config import Config
        
        config = Config()
        
        # 仅测试实例化，不连接
        influx_client = InfluxDBClient(config)
        mysql_client = MySQLClient(config)
        
        print("PASS Database Clients: instantiation successful")
        return True
    except Exception as e:
        print(f"FAIL Database Clients: {e}")
        return False

def test_data_processor():
    """测试数据处理器"""
    try:
        from data.data_processor import DataProcessor
        from core.config import Config
        
        config = Config()
        processor = DataProcessor(config)
        
        # 测试基本方法
        symbols = processor.symbols
        timeframes = processor.timeframes
        
        print(f"PASS Data Processor: symbols={symbols}, timeframes={timeframes}")
        return True
    except Exception as e:
        print(f"FAIL Data Processor: {e}")
        return False

def test_cache_integration():
    """测试缓存集成"""
    try:
        from data.cache import CacheIntegration
        from core.config import Config
        
        config = Config()
        cache = CacheIntegration(config)
        
        print("PASS Cache Integration: instantiation successful")
        return True
    except Exception as e:
        print(f"FAIL Cache Integration: {e}")
        return False

def main():
    """主测试函数"""
    print("Simplified Service Test")
    print("=" * 60)
    
    test_functions = [
        ("Config Service", test_config_service),
        ("Kafka Client", test_kafka_client),
        ("Database Clients", test_database_clients),
        ("Data Processor", test_data_processor),
        ("Cache Integration", test_cache_integration),
    ]
    
    results = {}
    start_time = time.time()
    
    # 执行测试
    for test_name, test_func in test_functions:
        print(f"\nTesting {test_name}...")
        try:
            success = test_func()
            results[test_name] = success
        except Exception as e:
            print(f"FAIL {test_name}: {e}")
            results[test_name] = False
    
    total_time = time.time() - start_time
    
    # 生成报告
    print("\n" + "=" * 60)
    print("Service Test Results")
    print("=" * 60)
    
    passed = sum(1 for success in results.values() if success)
    total = len(results)
    
    for test_name, success in results.items():
        status = "PASS" if success else "FAIL"
        print(f"{test_name}: {status}")
    
    print(f"\nTotal: {passed}/{total} services passed")
    print(f"Time: {total_time:.2f}s")
    
    if passed == total:
        print("SUCCESS: All service tests passed!")
        return True
    else:
        print(f"WARNING: {total - passed} services need attention")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)