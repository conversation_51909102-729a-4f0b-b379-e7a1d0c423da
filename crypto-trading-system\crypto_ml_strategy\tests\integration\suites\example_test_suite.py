#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
示例集成测试套件

演示如何使用集成测试框架创建和执行测试套件。
"""

import asyncio
import time
from typing import Dict, Any
from loguru import logger

from ..utils.test_orchestrator import TestCase, TestSuite, TestPriority
from ..fixtures.mock_services import get_mock_manager


async def test_basic_functionality() -> Dict[str, Any]:
    """基础功能测试"""
    logger.info("执行基础功能测试...")
    
    # 模拟测试逻辑
    await asyncio.sleep(0.1)
    
    # 返回测试指标
    return {
        "execution_time": 0.1,
        "assertions_passed": 5,
        "test_type": "basic_functionality"
    }


async def test_api_integration() -> Dict[str, Any]:
    """API集成测试"""
    logger.info("执行API集成测试...")
    
    # 获取Mock服务
    mock_manager = get_mock_manager()
    java_api = mock_manager.get_java_api()
    
    # 测试API调用
    response = await java_api.request("GET", "/market/data")
    
    # 验证响应
    assert response.status_code == 200
    assert response.data is not None
    
    return {
        "execution_time": 0.2,
        "api_calls": 1,
        "response_time": 0.05,
        "test_type": "api_integration"
    }


async def test_kafka_messaging() -> Dict[str, Any]:
    """Kafka消息测试"""
    logger.info("执行Kafka消息测试...")
    
    # 获取Mock Kafka服务
    mock_manager = get_mock_manager()
    kafka_service = mock_manager.get_kafka_service()
    
    # 测试消息生产和消费
    test_message = {"symbol": "BTCUSDT", "price": 45000.0}
    await kafka_service.produce("test_topic", test_message)
    
    # 验证消息
    messages = kafka_service.get_messages("test_topic")
    assert len(messages) > 0
    assert messages[-1]["value"] == test_message
    
    return {
        "execution_time": 0.15,
        "messages_sent": 1,
        "messages_received": 1,
        "test_type": "kafka_messaging"
    }


def test_synchronous_operation() -> Dict[str, Any]:
    """同步操作测试"""
    logger.info("执行同步操作测试...")
    
    # 模拟同步测试逻辑
    time.sleep(0.05)
    
    return {
        "execution_time": 0.05,
        "sync_operations": 3,
        "test_type": "synchronous_operation"
    }


async def test_with_dependency() -> Dict[str, Any]:
    """依赖测试（依赖于基础功能测试）"""
    logger.info("执行依赖测试...")
    
    # 这个测试依赖于基础功能测试的完成
    await asyncio.sleep(0.08)
    
    return {
        "execution_time": 0.08,
        "dependency_verified": True,
        "test_type": "dependency_test"
    }


def create_example_test_suite() -> TestSuite:
    """
    创建示例测试套件
    
    Returns:
        TestSuite: 示例测试套件
    """
    
    # 定义测试用例
    test_cases = [
        TestCase(
            test_id="example.basic_functionality",
            name="基础功能测试",
            description="测试系统的基础功能是否正常工作",
            test_function=test_basic_functionality,
            priority=TestPriority.HIGH,
            timeout=30,
            tags=["basic", "core"]
        ),
        
        TestCase(
            test_id="example.api_integration",
            name="API集成测试",
            description="测试与Java API的集成是否正常",
            test_function=test_api_integration,
            dependencies=["example.basic_functionality"],
            priority=TestPriority.HIGH,
            timeout=60,
            tags=["api", "integration"]
        ),
        
        TestCase(
            test_id="example.kafka_messaging",
            name="Kafka消息测试",
            description="测试Kafka消息传递功能",
            test_function=test_kafka_messaging,
            dependencies=["example.basic_functionality"],
            priority=TestPriority.NORMAL,
            timeout=45,
            tags=["kafka", "messaging"]
        ),
        
        TestCase(
            test_id="example.synchronous_operation",
            name="同步操作测试",
            description="测试同步操作的执行",
            test_function=test_synchronous_operation,
            priority=TestPriority.LOW,
            timeout=30,
            tags=["sync", "operation"]
        ),
        
        TestCase(
            test_id="example.dependency_test",
            name="依赖测试",
            description="测试依赖关系的处理",
            test_function=test_with_dependency,
            dependencies=["example.basic_functionality", "example.api_integration"],
            priority=TestPriority.NORMAL,
            timeout=40,
            tags=["dependency", "advanced"]
        )
    ]
    
    # 创建测试套件
    test_suite = TestSuite(
        suite_id="example_suite",
        name="示例集成测试套件",
        description="演示集成测试框架功能的示例测试套件",
        test_cases=test_cases,
        parallel_execution=True,
        max_workers=2
    )
    
    return test_suite


if __name__ == "__main__":
    # 示例：如何运行测试套件
    import asyncio
    from ..utils.test_orchestrator import create_test_orchestrator
    from ..reports.test_reporter import create_test_reporter
    from ..fixtures.mock_services import setup_mock_environment, teardown_mock_environment
    
    async def run_example_tests():
        """运行示例测试"""
        # 设置Mock环境
        await setup_mock_environment()
        
        try:
            # 创建测试编排器
            orchestrator = create_test_orchestrator(max_workers=2)
            
            # 注册测试套件
            test_suite = create_example_test_suite()
            orchestrator.register_test_suite(test_suite)
            
            # 执行测试
            results = await orchestrator.execute_all_tests()
            
            # 生成报告
            reporter = create_test_reporter()
            summary = orchestrator.get_execution_summary()
            
            report_files = reporter.generate_all_reports(
                test_results=results,
                execution_summary=summary,
                report_name="example_test_report"
            )
            
            logger.info(f"测试完成，报告已生成: {report_files}")
            
        finally:
            # 清理Mock环境
            await teardown_mock_environment()
    
    # 运行示例
    asyncio.run(run_example_tests())