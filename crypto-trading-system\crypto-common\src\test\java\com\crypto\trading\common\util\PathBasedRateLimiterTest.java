package com.crypto.trading.common.util;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import static org.junit.jupiter.api.Assertions.*;

/**
 * PathBasedRateLimiter工具类测试
 * <p>
 * 测试API限流相关的工具方法
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
class PathBasedRateLimiterTest {

    /**
     * 测试用的PathBasedRateLimiter实例
     */
    private PathBasedRateLimiter rateLimiter;
    
    /**
     * 测试用的API路径
     */
    private final String TEST_PATH = "/api/test";

    /**
     * 每个测试方法执行前的准备工作
     */
    @BeforeEach
    void setUp() {
        // 创建一个新的PathBasedRateLimiter实例，窗口时间为1秒，便于测试
        rateLimiter = new PathBasedRateLimiter(1000);
        // 设置测试路径的最大请求数为5
        rateLimiter.setMaxRequests(TEST_PATH, 5);
    }

    /**
     * 每个测试方法执行后的清理工作
     */
    @AfterEach
    void tearDown() {
        // 关闭PathBasedRateLimiter，释放资源
        if (rateLimiter != null) {
            rateLimiter.shutdown();
            rateLimiter = null;
        }
    }

    /**
     * 测试设置和获取最大请求数
     */
    @Test
    @DisplayName("测试设置和获取最大请求数")
    void testSetAndGetMaxRequests() {
        // 重新设置最大请求数
        rateLimiter.setMaxRequests(TEST_PATH, 10);
        // 验证是否设置成功
        assertEquals(10, rateLimiter.getMaxRequests(TEST_PATH));
    }

    /**
     * 测试尝试获取令牌
     */
    @Test
    @DisplayName("测试尝试获取令牌")
    void testTryAcquire() {
        // 前5次请求应该成功
        for (int i = 0; i < 5; i++) {
            assertTrue(rateLimiter.tryAcquire(TEST_PATH));
        }
        // 第6次请求应该失败
        assertFalse(rateLimiter.tryAcquire(TEST_PATH));
    }

    /**
     * 测试获取当前计数
     */
    @Test
    @DisplayName("测试获取当前计数")
    void testGetCurrentCount() {
        // 初始计数应该为0
        assertEquals(0, rateLimiter.getCurrentCount(TEST_PATH));
        
        // 获取3次令牌
        for (int i = 0; i < 3; i++) {
            rateLimiter.tryAcquire(TEST_PATH);
        }
        
        // 验证当前计数是否正确
        assertEquals(3, rateLimiter.getCurrentCount(TEST_PATH));
    }

    /**
     * 测试不同路径的限流
     */
    @Test
    @DisplayName("测试不同路径的限流")
    void testDifferentPaths() {
        String path1 = "/api/path1";
        String path2 = "/api/path2";
        
        // 设置不同路径的最大请求数
        rateLimiter.setMaxRequests(path1, 3);
        rateLimiter.setMaxRequests(path2, 5);
        
        // path1可以获取3次令牌
        for (int i = 0; i < 3; i++) {
            assertTrue(rateLimiter.tryAcquire(path1));
        }
        // 第4次请求应该失败
        assertFalse(rateLimiter.tryAcquire(path1));
        
        // path2可以获取5次令牌
        for (int i = 0; i < 5; i++) {
            assertTrue(rateLimiter.tryAcquire(path2));
        }
        // 第6次请求应该失败
        assertFalse(rateLimiter.tryAcquire(path2));
    }

    /**
     * 测试默认最大请求数
     */
    @Test
    @DisplayName("测试默认最大请求数")
    void testDefaultMaxRequests() {
        String newPath = "/api/new";
        // 默认最大请求数应该是2400
        assertEquals(2400, rateLimiter.getMaxRequests(newPath));
    }
} 