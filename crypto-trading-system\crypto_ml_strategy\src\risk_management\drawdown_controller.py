"""
回撤控制器模块

实现投资组合回撤的实时监控、预警和控制功能，
包括回撤分析、恢复策略和风险控制措施。

Author: Crypto ML Strategy Team
Date: 2024-12-19
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta
from dataclasses import dataclass, field
import logging
import threading
from collections import deque

from .risk_config import RiskConfig, RiskEvent, RiskLevel

logger = logging.getLogger(__name__)


@dataclass
class DrawdownInfo:
    """回撤信息数据类"""
    timestamp: datetime
    portfolio_value: float
    peak_value: float
    drawdown_amount: float
    drawdown_percentage: float
    drawdown_duration: timedelta
    is_new_peak: bool = False
    recovery_percentage: float = 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'timestamp': self.timestamp.isoformat(),
            'portfolio_value': self.portfolio_value,
            'peak_value': self.peak_value,
            'drawdown_amount': self.drawdown_amount,
            'drawdown_percentage': self.drawdown_percentage,
            'drawdown_duration_seconds': self.drawdown_duration.total_seconds(),
            'is_new_peak': self.is_new_peak,
            'recovery_percentage': self.recovery_percentage
        }


@dataclass
class RecoveryAction:
    """恢复措施数据类"""
    action_type: str
    description: str
    priority: int
    parameters: Dict[str, Any] = field(default_factory=dict)
    estimated_impact: float = 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'action_type': self.action_type,
            'description': self.description,
            'priority': self.priority,
            'parameters': self.parameters,
            'estimated_impact': self.estimated_impact
        }


class DrawdownController:
    """回撤控制器类"""
    
    def __init__(self, config: RiskConfig):
        """
        初始化回撤控制器
        
        Args:
            config: 风险管理配置
        """
        self.config = config
        self.portfolio_history: deque = deque(maxlen=10000)  # 限制历史数据大小
        self.drawdown_history: List[DrawdownInfo] = []
        self.current_peak_value: float = 0.0
        self.current_peak_time: Optional[datetime] = None
        self.max_drawdown: float = 0.0
        self.max_drawdown_time: Optional[datetime] = None
        self.recovery_start_time: Optional[datetime] = None
        
        # 线程安全锁
        self._lock = threading.RLock()
        
        # 告警状态
        self.alert_levels = {
            'minor': 0.05,      # 5%
            'moderate': 0.10,   # 10%
            'severe': 0.15,     # 15%
            'critical': 0.20    # 20%
        }
        self.current_alert_level: Optional[str] = None
        
        logger.info("Drawdown controller initialized")
    
    def update_portfolio_value(self, portfolio_value: float, timestamp: Optional[datetime] = None) -> DrawdownInfo:
        """
        更新投资组合价值并计算回撤
        
        Args:
            portfolio_value: 投资组合价值
            timestamp: 时间戳
            
        Returns:
            回撤信息
        """
        try:
            with self._lock:
                if timestamp is None:
                    timestamp = datetime.now()
                
                # 添加到历史记录
                self.portfolio_history.append({
                    'timestamp': timestamp,
                    'value': portfolio_value
                })
                
                # 更新峰值
                is_new_peak = False
                if portfolio_value > self.current_peak_value:
                    self.current_peak_value = portfolio_value
                    self.current_peak_time = timestamp
                    is_new_peak = True
                    self.recovery_start_time = None
                
                # 计算回撤
                drawdown_amount = self.current_peak_value - portfolio_value
                drawdown_percentage = drawdown_amount / self.current_peak_value if self.current_peak_value > 0 else 0.0
                
                # 计算回撤持续时间
                drawdown_duration = timestamp - self.current_peak_time if self.current_peak_time else timedelta(0)
                
                # 更新最大回撤
                if drawdown_percentage > self.max_drawdown:
                    self.max_drawdown = drawdown_percentage
                    self.max_drawdown_time = timestamp
                
                # 计算恢复百分比
                recovery_percentage = 0.0
                if self.recovery_start_time and drawdown_percentage < self.max_drawdown:
                    recovery_percentage = (self.max_drawdown - drawdown_percentage) / self.max_drawdown
                elif drawdown_percentage < self.max_drawdown and not self.recovery_start_time:
                    self.recovery_start_time = timestamp
                
                # 创建回撤信息
                drawdown_info = DrawdownInfo(
                    timestamp=timestamp,
                    portfolio_value=portfolio_value,
                    peak_value=self.current_peak_value,
                    drawdown_amount=drawdown_amount,
                    drawdown_percentage=drawdown_percentage,
                    drawdown_duration=drawdown_duration,
                    is_new_peak=is_new_peak,
                    recovery_percentage=recovery_percentage
                )
                
                # 保存回撤历史
                self.drawdown_history.append(drawdown_info)
                
                # 保持历史数据在合理范围内
                if len(self.drawdown_history) > 1000:
                    self.drawdown_history = self.drawdown_history[-1000:]
                
                logger.debug(f"Updated portfolio value: {portfolio_value}, drawdown: {drawdown_percentage:.2%}")
                
                return drawdown_info
                
        except Exception as e:
            logger.error(f"Error updating portfolio value: {e}")
            raise
    
    def check_drawdown_limits(self, drawdown_info: DrawdownInfo) -> List[RiskEvent]:
        """
        检查回撤限制并生成告警
        
        Args:
            drawdown_info: 回撤信息
            
        Returns:
            风险事件列表
        """
        events = []
        
        try:
            with self._lock:
                current_drawdown = drawdown_info.drawdown_percentage
                
                # 检查各级别告警
                new_alert_level = None
                for level, threshold in sorted(self.alert_levels.items(), key=lambda x: x[1], reverse=True):
                    if current_drawdown >= threshold:
                        new_alert_level = level
                        break
                
                # 生成新的告警事件
                if new_alert_level and new_alert_level != self.current_alert_level:
                    severity_map = {
                        'minor': RiskLevel.LOW,
                        'moderate': RiskLevel.MEDIUM,
                        'severe': RiskLevel.HIGH,
                        'critical': RiskLevel.CRITICAL
                    }
                    
                    events.append(RiskEvent(
                        event_type=f"drawdown_{new_alert_level}",
                        severity=severity_map.get(new_alert_level, RiskLevel.MEDIUM),
                        message=f"Portfolio drawdown reached {new_alert_level} level: {current_drawdown:.2%}",
                        timestamp=drawdown_info.timestamp,
                        current_value=current_drawdown,
                        threshold_value=self.alert_levels[new_alert_level],
                        action_required=new_alert_level in ['severe', 'critical']
                    ))
                    
                    self.current_alert_level = new_alert_level
                
                # 检查最大回撤阈值
                if current_drawdown > self.config.max_drawdown_threshold:
                    events.append(RiskEvent(
                        event_type="max_drawdown_exceeded",
                        severity=RiskLevel.CRITICAL,
                        message=f"Maximum drawdown threshold exceeded: {current_drawdown:.2%} > {self.config.max_drawdown_threshold:.2%}",
                        timestamp=drawdown_info.timestamp,
                        current_value=current_drawdown,
                        threshold_value=self.config.max_drawdown_threshold,
                        action_required=True
                    ))
                
                # 检查紧急止损阈值
                if current_drawdown > self.config.emergency_stop_threshold:
                    events.append(RiskEvent(
                        event_type="emergency_stop_triggered",
                        severity=RiskLevel.CRITICAL,
                        message=f"Emergency stop triggered: {current_drawdown:.2%} > {self.config.emergency_stop_threshold:.2%}",
                        timestamp=drawdown_info.timestamp,
                        current_value=current_drawdown,
                        threshold_value=self.config.emergency_stop_threshold,
                        action_required=True,
                        metadata={'action': 'emergency_liquidation'}
                    ))
                
                # 检查回撤持续时间
                if drawdown_info.drawdown_duration.days > 30:  # 回撤持续超过30天
                    events.append(RiskEvent(
                        event_type="prolonged_drawdown",
                        severity=RiskLevel.HIGH,
                        message=f"Drawdown has persisted for {drawdown_info.drawdown_duration.days} days",
                        timestamp=drawdown_info.timestamp,
                        current_value=drawdown_info.drawdown_duration.days,
                        threshold_value=30,
                        action_required=True
                    ))
                
                logger.info(f"Drawdown limit check completed, {len(events)} events generated")
                
        except Exception as e:
            logger.error(f"Error checking drawdown limits: {e}")
        
        return events
    
    def suggest_recovery_actions(self, drawdown_info: DrawdownInfo) -> List[RecoveryAction]:
        """
        建议恢复措施
        
        Args:
            drawdown_info: 回撤信息
            
        Returns:
            恢复措施列表
        """
        actions = []
        
        try:
            current_drawdown = drawdown_info.drawdown_percentage
            
            # 根据回撤程度建议不同措施
            if current_drawdown > 0.20:  # 超过20%
                actions.extend([
                    RecoveryAction(
                        action_type="emergency_liquidation",
                        description="Emergency liquidation of all positions",
                        priority=1,
                        parameters={'liquidation_percentage': 100},
                        estimated_impact=-0.05
                    ),
                    RecoveryAction(
                        action_type="suspend_trading",
                        description="Suspend all trading activities",
                        priority=1,
                        parameters={'suspension_duration': 24},
                        estimated_impact=0.0
                    )
                ])
            
            elif current_drawdown > 0.15:  # 15-20%
                actions.extend([
                    RecoveryAction(
                        action_type="reduce_positions",
                        description="Reduce position sizes by 50%",
                        priority=2,
                        parameters={'reduction_percentage': 50},
                        estimated_impact=-0.02
                    ),
                    RecoveryAction(
                        action_type="tighten_stops",
                        description="Tighten stop loss levels",
                        priority=2,
                        parameters={'stop_loss_multiplier': 0.5},
                        estimated_impact=-0.01
                    )
                ])
            
            elif current_drawdown > 0.10:  # 10-15%
                actions.extend([
                    RecoveryAction(
                        action_type="reduce_positions",
                        description="Reduce position sizes by 30%",
                        priority=3,
                        parameters={'reduction_percentage': 30},
                        estimated_impact=-0.01
                    ),
                    RecoveryAction(
                        action_type="conservative_mode",
                        description="Switch to conservative trading mode",
                        priority=3,
                        parameters={'risk_multiplier': 0.7},
                        estimated_impact=0.0
                    )
                ])
            
            elif current_drawdown > 0.05:  # 5-10%
                actions.extend([
                    RecoveryAction(
                        action_type="reduce_positions",
                        description="Reduce position sizes by 20%",
                        priority=4,
                        parameters={'reduction_percentage': 20},
                        estimated_impact=-0.005
                    ),
                    RecoveryAction(
                        action_type="review_strategy",
                        description="Review and adjust trading strategy",
                        priority=4,
                        parameters={'review_period': 7},
                        estimated_impact=0.0
                    )
                ])
            
            # 基于回撤持续时间的建议
            if drawdown_info.drawdown_duration.days > 14:
                actions.append(RecoveryAction(
                    action_type="strategy_pause",
                    description="Pause strategy for comprehensive review",
                    priority=2,
                    parameters={'pause_duration': 48},
                    estimated_impact=0.0
                ))
            
            # 基于恢复进度的建议
            if drawdown_info.recovery_percentage > 0.5:
                actions.append(RecoveryAction(
                    action_type="gradual_increase",
                    description="Gradually increase position sizes",
                    priority=5,
                    parameters={'increase_percentage': 10},
                    estimated_impact=0.01
                ))
            
            # 按优先级排序
            actions.sort(key=lambda x: x.priority)
            
            logger.info(f"Generated {len(actions)} recovery action suggestions")
            
        except Exception as e:
            logger.error(f"Error suggesting recovery actions: {e}")
        
        return actions
    
    def analyze_drawdown_patterns(self) -> Dict[str, Any]:
        """
        分析回撤模式
        
        Returns:
            回撤分析结果
        """
        try:
            with self._lock:
                if len(self.drawdown_history) < 10:
                    return {'status': 'insufficient_data'}
                
                # 提取回撤数据
                drawdowns = [dd.drawdown_percentage for dd in self.drawdown_history]
                durations = [dd.drawdown_duration.total_seconds() / 3600 for dd in self.drawdown_history]  # 转换为小时
                
                # 基本统计
                analysis = {
                    'total_observations': len(drawdowns),
                    'max_drawdown': max(drawdowns),
                    'average_drawdown': np.mean(drawdowns),
                    'median_drawdown': np.median(drawdowns),
                    'drawdown_std': np.std(drawdowns),
                    'max_duration_hours': max(durations),
                    'average_duration_hours': np.mean(durations),
                    'median_duration_hours': np.median(durations)
                }
                
                # 回撤分布
                drawdown_ranges = {
                    '0-5%': sum(1 for dd in drawdowns if 0 <= dd < 0.05),
                    '5-10%': sum(1 for dd in drawdowns if 0.05 <= dd < 0.10),
                    '10-15%': sum(1 for dd in drawdowns if 0.10 <= dd < 0.15),
                    '15-20%': sum(1 for dd in drawdowns if 0.15 <= dd < 0.20),
                    '>20%': sum(1 for dd in drawdowns if dd >= 0.20)
                }
                analysis['drawdown_distribution'] = drawdown_ranges
                
                # 恢复分析
                recovery_times = []
                for i, dd in enumerate(self.drawdown_history):
                    if dd.recovery_percentage >= 1.0:  # 完全恢复
                        # 查找恢复时间
                        for j in range(i + 1, len(self.drawdown_history)):
                            if self.drawdown_history[j].is_new_peak:
                                recovery_time = (self.drawdown_history[j].timestamp - dd.timestamp).total_seconds() / 3600
                                recovery_times.append(recovery_time)
                                break
                
                if recovery_times:
                    analysis['recovery_statistics'] = {
                        'average_recovery_hours': np.mean(recovery_times),
                        'median_recovery_hours': np.median(recovery_times),
                        'max_recovery_hours': max(recovery_times),
                        'recovery_count': len(recovery_times)
                    }
                
                # 趋势分析
                if len(drawdowns) >= 20:
                    recent_drawdowns = drawdowns[-20:]
                    earlier_drawdowns = drawdowns[-40:-20] if len(drawdowns) >= 40 else drawdowns[:-20]
                    
                    if earlier_drawdowns:
                        recent_avg = np.mean(recent_drawdowns)
                        earlier_avg = np.mean(earlier_drawdowns)
                        trend = "improving" if recent_avg < earlier_avg else "deteriorating"
                        analysis['trend'] = {
                            'direction': trend,
                            'recent_average': recent_avg,
                            'earlier_average': earlier_avg,
                            'change_percentage': (recent_avg - earlier_avg) / earlier_avg if earlier_avg > 0 else 0
                        }
                
                logger.info("Drawdown pattern analysis completed")
                
                return analysis
                
        except Exception as e:
            logger.error(f"Error analyzing drawdown patterns: {e}")
            return {'status': 'error', 'message': str(e)}
    
    def get_drawdown_statistics(self) -> Dict[str, Any]:
        """获取回撤统计信息"""
        try:
            with self._lock:
                current_drawdown = 0.0
                if self.drawdown_history:
                    current_drawdown = self.drawdown_history[-1].drawdown_percentage
                
                return {
                    'current_drawdown': current_drawdown,
                    'max_drawdown': self.max_drawdown,
                    'max_drawdown_time': self.max_drawdown_time.isoformat() if self.max_drawdown_time else None,
                    'current_peak_value': self.current_peak_value,
                    'current_peak_time': self.current_peak_time.isoformat() if self.current_peak_time else None,
                    'current_alert_level': self.current_alert_level,
                    'total_observations': len(self.drawdown_history),
                    'recovery_in_progress': self.recovery_start_time is not None,
                    'recovery_start_time': self.recovery_start_time.isoformat() if self.recovery_start_time else None
                }
                
        except Exception as e:
            logger.error(f"Error getting drawdown statistics: {e}")
            return {'status': 'error', 'message': str(e)}
    
    def reset_drawdown_tracking(self) -> None:
        """重置回撤跟踪"""
        try:
            with self._lock:
                self.current_peak_value = 0.0
                self.current_peak_time = None
                self.max_drawdown = 0.0
                self.max_drawdown_time = None
                self.recovery_start_time = None
                self.current_alert_level = None
                self.drawdown_history.clear()
                self.portfolio_history.clear()
                
                logger.info("Drawdown tracking reset")
                
        except Exception as e:
            logger.error(f"Error resetting drawdown tracking: {e}")
            raise
