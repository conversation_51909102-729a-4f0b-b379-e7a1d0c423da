/**
 * Autogenerated by Avro
 *
 * DO NOT EDIT DIRECTLY
 */
package com.crypto.trading.trade.model.avro;
@org.apache.avro.specific.AvroGenerated
public enum TimeInForce implements org.apache.avro.generic.GenericEnumSymbol<TimeInForce> {
  GTC, IOC, FOK, GTX  ;
  public static final org.apache.avro.Schema SCHEMA$ = new org.apache.avro.Schema.Parser().parse("{\"type\":\"enum\",\"name\":\"TimeInForce\",\"namespace\":\"com.crypto.trading.trade.model.avro\",\"symbols\":[\"GTC\",\"IOC\",\"FOK\",\"GTX\"]}");
  public static org.apache.avro.Schema getClassSchema() { return SCHEMA$; }

  @Override
  public org.apache.avro.Schema getSchema() { return SCHEMA$; }
}
