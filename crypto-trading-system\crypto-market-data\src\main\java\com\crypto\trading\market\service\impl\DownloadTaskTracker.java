package com.crypto.trading.market.service.impl;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Semaphore;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 下载任务跟踪器
 * 用于记录和查询批量下载任务的状态
 * 增强版本：支持任务暂停、恢复、取消和分片功能
 */
@Component
public class DownloadTaskTracker {

    private static final Logger log = LoggerFactory.getLogger(DownloadTaskTracker.class);
    
    /**
     * 任务状态枚举
     */
    public enum TaskStatus {
        PENDING("等待中"),
        RUNNING("执行中"),
        PAUSED("已暂停"),
        COMPLETED("已完成"),
        CANCELED("已取消"),
        CANCELLED("已取消"),  // 增加别名，兼容CANCELED
        FAILED("失败");
        
        private final String description;
        
        TaskStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 任务优先级枚举
     */
    public enum TaskPriority {
        LOW(1),
        NORMAL(5),
        HIGH(10);
        
        private final int value;
        
        TaskPriority(int value) {
            this.value = value;
        }
        
        public int getValue() {
            return value;
        }
    }
    
    /**
     * 任务分片信息
     */
    public static class TaskChunk {
        private final int chunkIndex;
        private final LocalDateTime chunkStartTime;
        private final LocalDateTime chunkEndTime;
        private TaskStatus status;
        private int retryCount;
        private String message;
        
        public TaskChunk(int chunkIndex, LocalDateTime chunkStartTime, LocalDateTime chunkEndTime) {
            this.chunkIndex = chunkIndex;
            this.chunkStartTime = chunkStartTime;
            this.chunkEndTime = chunkEndTime;
            this.status = TaskStatus.PENDING;
            this.retryCount = 0;
            this.message = "分片等待执行";
        }
        
        public int getChunkIndex() {
            return chunkIndex;
        }
        
        public LocalDateTime getChunkStartTime() {
            return chunkStartTime;
        }
        
        public LocalDateTime getChunkEndTime() {
            return chunkEndTime;
        }
        
        public TaskStatus getStatus() {
            return status;
        }
        
        public void setStatus(TaskStatus status) {
            this.status = status;
        }
        
        public int getRetryCount() {
            return retryCount;
        }
        
        public void incrementRetryCount() {
            this.retryCount++;
        }
        
        public String getMessage() {
            return message;
        }
        
        public void setMessage(String message) {
            this.message = message;
        }
        
        @Override
        public String toString() {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            return String.format("分片 #%d: %s - %s, 状态: %s, 重试次数: %d, 消息: %s",
                    chunkIndex, 
                    chunkStartTime.format(formatter), 
                    chunkEndTime.format(formatter),
                    status.getDescription(),
                    retryCount,
                    message);
        }
    }
    
    /**
     * 任务信息类
     */
    public static class TaskInfo {
        private String taskId;
        private String taskType;
        private List<String> symbols;
        private String interval;
        private int limit;
        private LocalDateTime startTime;
        private LocalDateTime endTime;
        private LocalDateTime createTime;
        private LocalDateTime updateTime;
        private TaskStatus status;
        private String message;
        private Map<String, String> symbolStatus = new ConcurrentHashMap<>();
        private int totalSymbols;
        private int processedSymbols;
        
        // 新增属性
        private TaskPriority priority;
        private int maxRetries;
        private int maxConcurrentRequests;
        private boolean incrementalDownload;
        private int downloadedItemCount;
        private int skippedItemCount;
        private int chunkSizeHours;
        private List<TaskChunk> chunks;
        private AtomicInteger completedChunks;
        private Semaphore concurrencyControl;

        public TaskInfo(String taskId, String taskType, List<String> symbols, 
                      String interval, int limit, 
                      LocalDateTime startTime, LocalDateTime endTime) {
            this.taskId = taskId;
            this.taskType = taskType;
            this.symbols = new ArrayList<>(symbols);
            this.interval = interval;
            this.limit = limit;
            this.startTime = startTime;
            this.endTime = endTime;
            this.createTime = LocalDateTime.now();
            this.updateTime = this.createTime;
            this.status = TaskStatus.PENDING;
            this.message = "任务创建成功，等待执行";
            this.totalSymbols = symbols.size();
            this.processedSymbols = 0;
            
            // 新增属性初始化
            this.priority = TaskPriority.NORMAL;
            this.maxRetries = 3;
            this.maxConcurrentRequests = 5;
            this.incrementalDownload = true;
            this.downloadedItemCount = 0;
            this.skippedItemCount = 0;
            this.chunkSizeHours = 24; // 默认24小时一个分片
            this.chunks = initializeChunks(startTime, endTime, chunkSizeHours);
            this.completedChunks = new AtomicInteger(0);
            this.concurrencyControl = new Semaphore(maxConcurrentRequests);
        }
        
        /**
         * 初始化任务分片
         */
        private List<TaskChunk> initializeChunks(LocalDateTime start, LocalDateTime end, int chunkSizeHours) {
            List<TaskChunk> result = new ArrayList<>();
            
            LocalDateTime chunkStart = start;
            int chunkIndex = 0;
            
            while (chunkStart.isBefore(end)) {
                LocalDateTime chunkEnd = chunkStart.plusHours(chunkSizeHours);
                if (chunkEnd.isAfter(end)) {
                    chunkEnd = end;
                }
                
                result.add(new TaskChunk(chunkIndex++, chunkStart, chunkEnd));
                chunkStart = chunkEnd;
                
                if (chunkStart.equals(end)) {
                    break;
                }
            }
            
            return result;
        }
        
        // 现有getters和setters
        
        public String getTaskId() {
            return taskId;
        }
        
        public String getTaskType() {
            return taskType;
        }
        
        public List<String> getSymbols() {
            return symbols;
        }
        
        public String getInterval() {
            return interval;
        }
        
        public int getLimit() {
            return limit;
        }
        
        public LocalDateTime getStartTime() {
            return startTime;
        }
        
        public LocalDateTime getEndTime() {
            return endTime;
        }
        
        public LocalDateTime getCreateTime() {
            return createTime;
        }
        
        public LocalDateTime getUpdateTime() {
            return updateTime;
        }
        
        public TaskStatus getStatus() {
            return status;
        }
        
        public void setStatus(TaskStatus status) {
            this.status = status;
            this.updateTime = LocalDateTime.now();
        }
        
        public String getMessage() {
            return message;
        }
        
        public void setMessage(String message) {
            this.message = message;
            this.updateTime = LocalDateTime.now();
        }
        
        public Map<String, String> getSymbolStatus() {
            return symbolStatus;
        }
        
        public void updateSymbolStatus(String symbol, String status) {
            this.symbolStatus.put(symbol, status);
            this.processedSymbols++;
            this.updateTime = LocalDateTime.now();
        }
        
        public int getTotalSymbols() {
            return totalSymbols;
        }
        
        public int getProcessedSymbols() {
            return processedSymbols;
        }
        
        public int getProgressPercentage() {
            if (totalSymbols == 0) {
                return 0;
            }
            return (processedSymbols * 100) / totalSymbols;
        }
        
        // 新增getters和setters
        
        public TaskPriority getPriority() {
            return priority;
        }
        
        public void setPriority(TaskPriority priority) {
            this.priority = priority;
        }
        
        public int getMaxRetries() {
            return maxRetries;
        }
        
        public void setMaxRetries(int maxRetries) {
            this.maxRetries = maxRetries;
        }
        
        public int getMaxConcurrentRequests() {
            return maxConcurrentRequests;
        }
        
        public void setMaxConcurrentRequests(int maxConcurrentRequests) {
            this.maxConcurrentRequests = maxConcurrentRequests;
            this.concurrencyControl = new Semaphore(maxConcurrentRequests);
        }
        
        public boolean isIncrementalDownload() {
            return incrementalDownload;
        }
        
        public void setIncrementalDownload(boolean incrementalDownload) {
            this.incrementalDownload = incrementalDownload;
        }
        
        public int getDownloadedItemCount() {
            return downloadedItemCount;
        }
        
        public void incrementDownloadedItemCount(int count) {
            this.downloadedItemCount += count;
        }
        
        public int getSkippedItemCount() {
            return skippedItemCount;
        }
        
        public void incrementSkippedItemCount(int count) {
            this.skippedItemCount += count;
        }
        
        public int getChunkSizeHours() {
            return chunkSizeHours;
        }
        
        public List<TaskChunk> getChunks() {
            return chunks;
        }
        
        public int getCompletedChunks() {
            return completedChunks.get();
        }
        
        public void incrementCompletedChunks() {
            completedChunks.incrementAndGet();
        }
        
        public int getTotalChunks() {
            return chunks.size();
        }
        
        public int getChunkProgressPercentage() {
            if (chunks.isEmpty()) {
                return 0;
            }
            return (completedChunks.get() * 100) / chunks.size();
        }
        
        public Semaphore getConcurrencyControl() {
            return concurrencyControl;
        }
        
        public void acquireConcurrencyPermit() throws InterruptedException {
            concurrencyControl.acquire();
        }
        
        public void releaseConcurrencyPermit() {
            concurrencyControl.release();
        }
        
        @Override
        public String toString() {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            StringBuilder sb = new StringBuilder()
                .append("任务ID: ").append(taskId).append("\n")
                .append("任务类型: ").append(taskType).append("\n")
                .append("交易对数量: ").append(totalSymbols).append("\n")
                .append("优先级: ").append(priority).append("\n");
            
            if (interval != null) {
                sb.append("K线周期: ").append(interval).append("\n");
            }
            if (limit > 0) {
                sb.append("深度级别: ").append(limit).append("\n");
            }
            
            sb.append("数据时间范围: ")
                .append(startTime.format(formatter)).append(" 至 ")
                .append(endTime.format(formatter)).append("\n")
                .append("创建时间: ").append(createTime.format(formatter)).append("\n")
                .append("更新时间: ").append(updateTime.format(formatter)).append("\n")
                .append("任务状态: ").append(status.getDescription()).append("\n")
                .append("交易对进度: ").append(processedSymbols).append("/").append(totalSymbols)
                .append(" (").append(getProgressPercentage()).append("%)\n")
                .append("分片进度: ").append(completedChunks.get()).append("/").append(chunks.size())
                .append(" (").append(getChunkProgressPercentage()).append("%)\n")
                .append("增量下载: ").append(incrementalDownload ? "是" : "否").append("\n")
                .append("已下载数据量: ").append(downloadedItemCount).append("\n")
                .append("已跳过数据量: ").append(skippedItemCount).append("\n")
                .append("并发限制: ").append(maxConcurrentRequests).append("\n")
                .append("描述: ").append(message);
            
            return sb.toString();
        }
    }
    
    // 任务映射表，存储所有任务信息
    private final Map<String, TaskInfo> tasks = new ConcurrentHashMap<>();
    
    /**
     * 创建新任务
     *
     * @param taskType  任务类型 (kline, trade, depth)
     * @param symbols   交易对列表
     * @param interval  K线间隔 (仅适用于K线数据)
     * @param limit     深度级别 (仅适用于深度数据)
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 任务ID
     */
    public String createTask(String taskType, List<String> symbols, 
                           String interval, int limit, 
                           LocalDateTime startTime, LocalDateTime endTime) {
        String taskId = generateTaskId();
        TaskInfo taskInfo = new TaskInfo(taskId, taskType, symbols, interval, limit, startTime, endTime);
        tasks.put(taskId, taskInfo);
        log.info("创建下载任务: id={}, type={}, symbols={}, 开始时间={}, 结束时间={}", 
                taskId, taskType, symbols.size(), startTime, endTime);
        return taskId;
    }
    
    /**
     * 创建任务（增强版本，支持更多参数）
     *
     * @param taskType              任务类型 (kline, trade, depth)
     * @param symbols               交易对列表
     * @param interval              K线间隔 (仅适用于K线数据)
     * @param limit                 深度级别 (仅适用于深度数据)
     * @param startTime             开始时间
     * @param endTime               结束时间
     * @param priority              任务优先级
     * @param incrementalDownload   是否增量下载
     * @param maxRetries            最大重试次数
     * @param maxConcurrentRequests 最大并发请求数
     * @param chunkSizeHours        分片大小（小时）
     * @return 任务ID
     */
    public String createTask(String taskType, List<String> symbols, 
                           String interval, int limit, 
                           LocalDateTime startTime, LocalDateTime endTime,
                           TaskPriority priority, boolean incrementalDownload,
                           int maxRetries, int maxConcurrentRequests,
                           int chunkSizeHours) {
        String taskId = generateTaskId();
        TaskInfo taskInfo = new TaskInfo(taskId, taskType, symbols, interval, limit, startTime, endTime);
        
        taskInfo.setPriority(priority);
        taskInfo.setIncrementalDownload(incrementalDownload);
        taskInfo.setMaxRetries(maxRetries);
        taskInfo.setMaxConcurrentRequests(maxConcurrentRequests);
        
        // 重新初始化分片
        if (chunkSizeHours > 0 && chunkSizeHours != taskInfo.getChunkSizeHours()) {
            taskInfo.chunks = taskInfo.initializeChunks(startTime, endTime, chunkSizeHours);
        }
        
        tasks.put(taskId, taskInfo);
        log.info("创建增强下载任务: id={}, type={}, symbols={}, 优先级={}, 增量下载={}, 分片大小={}小时", 
                taskId, taskType, symbols.size(), priority, incrementalDownload, chunkSizeHours);
        return taskId;
    }
    
    /**
     * 更新任务状态
     *
     * @param taskId  任务ID
     * @param status  状态
     * @param message 消息
     */
    public void updateTaskStatus(String taskId, TaskStatus status, String message) {
        TaskInfo taskInfo = tasks.get(taskId);
        if (taskInfo != null) {
            taskInfo.setStatus(status);
            taskInfo.setMessage(message);
            log.info("更新任务状态: id={}, status={}, message={}", taskId, status, message);
        } else {
            log.warn("任务不存在: id={}", taskId);
        }
    }
    
    /**
     * 更新交易对处理状态
     *
     * @param taskId 任务ID
     * @param symbol 交易对
     * @param status 状态消息
     */
    public void updateSymbolStatus(String taskId, String symbol, String status) {
        TaskInfo taskInfo = tasks.get(taskId);
        if (taskInfo != null) {
            taskInfo.updateSymbolStatus(symbol, status);
            log.debug("更新交易对状态: task={}, symbol={}, status={}", taskId, symbol, status);
            
            // 检查是否所有交易对都已处理
            if (taskInfo.getProcessedSymbols() >= taskInfo.getTotalSymbols()) {
                taskInfo.setStatus(TaskStatus.COMPLETED);
                taskInfo.setMessage("任务已完成，共处理 " + taskInfo.getTotalSymbols() + " 个交易对");
                log.info("任务完成: id={}, processed={}", taskId, taskInfo.getProcessedSymbols());
            }
        } else {
            log.warn("任务不存在: id={}", taskId);
        }
    }
    
    /**
     * 更新任务分片状态
     *
     * @param taskId     任务ID
     * @param chunkIndex 分片索引
     * @param status     分片状态
     * @param message    状态消息
     */
    public void updateChunkStatus(String taskId, int chunkIndex, TaskStatus status, String message) {
        TaskInfo taskInfo = tasks.get(taskId);
        if (taskInfo != null && chunkIndex >= 0 && chunkIndex < taskInfo.getChunks().size()) {
            TaskChunk chunk = taskInfo.getChunks().get(chunkIndex);
            chunk.setStatus(status);
            chunk.setMessage(message);
            
            log.debug("更新任务分片状态: task={}, chunk={}, status={}, message={}", 
                    taskId, chunkIndex, status.getDescription(), message);
            
            // 如果分片完成，更新计数
            if (status == TaskStatus.COMPLETED) {
                taskInfo.incrementCompletedChunks();
                
                // 检查是否所有分片都已完成
                if (taskInfo.getCompletedChunks() >= taskInfo.getTotalChunks()) {
                    taskInfo.setStatus(TaskStatus.COMPLETED);
                    taskInfo.setMessage("任务已完成，共处理 " + taskInfo.getTotalChunks() + " 个分片");
                    log.info("任务所有分片完成: id={}, completed={}", taskId, taskInfo.getCompletedChunks());
                }
            }
        } else {
            log.warn("任务或分片不存在: id={}, chunkIndex={}", taskId, chunkIndex);
        }
    }
    
    /**
     * 获取任务信息
     *
     * @param taskId 任务ID
     * @return 任务信息
     */
    public TaskInfo getTaskInfo(String taskId) {
        return tasks.get(taskId);
    }
    
    /**
     * 获取所有任务信息
     *
     * @return 所有任务信息列表
     */
    public List<TaskInfo> getAllTasks() {
        return new ArrayList<>(tasks.values());
    }
    
    /**
     * 获取指定状态的任务
     *
     * @param status 任务状态
     * @return 符合状态的任务列表
     */
    public List<TaskInfo> getTasksByStatus(TaskStatus status) {
        return tasks.values().stream()
                .filter(task -> task.getStatus() == status)
                .sorted(Comparator.comparingInt(task -> -task.getPriority().getValue())) // 按优先级降序排序
                .toList();
    }
    
    /**
     * 暂停任务
     *
     * @param taskId 任务ID
     * @return 是否成功暂停
     */
    public boolean pauseTask(String taskId) {
        TaskInfo taskInfo = tasks.get(taskId);
        if (taskInfo != null && taskInfo.getStatus() == TaskStatus.RUNNING) {
            taskInfo.setStatus(TaskStatus.PAUSED);
            taskInfo.setMessage("任务已暂停");
            log.info("暂停任务: id={}", taskId);
            return true;
        } else {
            log.warn("无法暂停任务，任务不存在或状态不是运行中: id={}", taskId);
            return false;
        }
    }
    
    /**
     * 恢复任务
     *
     * @param taskId 任务ID
     * @return 是否成功恢复
     */
    public boolean resumeTask(String taskId) {
        TaskInfo taskInfo = tasks.get(taskId);
        if (taskInfo != null && taskInfo.getStatus() == TaskStatus.PAUSED) {
            taskInfo.setStatus(TaskStatus.RUNNING);
            taskInfo.setMessage("任务已恢复运行");
            log.info("恢复任务: id={}", taskId);
            return true;
        } else {
            log.warn("无法恢复任务，任务不存在或状态不是暂停: id={}", taskId);
            return false;
        }
    }
    
    /**
     * 取消任务
     *
     * @param taskId 任务ID
     * @return 是否成功取消
     */
    public boolean cancelTask(String taskId) {
        TaskInfo taskInfo = tasks.get(taskId);
        if (taskInfo != null && (taskInfo.getStatus() == TaskStatus.PENDING || 
                                taskInfo.getStatus() == TaskStatus.RUNNING || 
                                taskInfo.getStatus() == TaskStatus.PAUSED)) {
            taskInfo.setStatus(TaskStatus.CANCELED);
            taskInfo.setMessage("任务已取消");
            log.info("取消任务: id={}", taskId);
            return true;
        } else {
            log.warn("无法取消任务，任务不存在或状态不允许取消: id={}", taskId);
            return false;
        }
    }
    
    /**
     * 更新数据下载和跳过计数
     *
     * @param taskId    任务ID
     * @param downloaded 下载数量
     * @param skipped   跳过数量
     */
    public void updateItemCounts(String taskId, int downloaded, int skipped) {
        TaskInfo taskInfo = tasks.get(taskId);
        if (taskInfo != null) {
            taskInfo.incrementDownloadedItemCount(downloaded);
            taskInfo.incrementSkippedItemCount(skipped);
            log.debug("更新任务数据计数: id={}, downloaded={}, skipped={}, total_downloaded={}, total_skipped={}", 
                    taskId, downloaded, skipped, taskInfo.getDownloadedItemCount(), taskInfo.getSkippedItemCount());
        } else {
            log.warn("任务不存在，无法更新数据计数: id={}", taskId);
        }
    }
    
    /**
     * 清理已完成或失败的任务
     * 删除超过24小时的已完成或失败的任务
     */
    public void cleanupTasks() {
        LocalDateTime threshold = LocalDateTime.now().minusHours(24);
        List<String> toRemove = new ArrayList<>();
        
        for (Map.Entry<String, TaskInfo> entry : tasks.entrySet()) {
            TaskInfo task = entry.getValue();
            if ((task.getStatus() == TaskStatus.COMPLETED || 
                 task.getStatus() == TaskStatus.FAILED || 
                 task.getStatus() == TaskStatus.CANCELED) && 
                task.getUpdateTime().isBefore(threshold)) {
                toRemove.add(entry.getKey());
            }
        }
        
        for (String taskId : toRemove) {
            tasks.remove(taskId);
            log.info("清理过期任务: id={}", taskId);
        }
    }
    
    /**
     * 生成任务ID
     *
     * @return 任务ID
     */
    private String generateTaskId() {
        return UUID.randomUUID().toString().substring(0, 8);
    }
    
    /**
     * 创建简化任务
     * 
     * @param taskType 任务类型
     * @param priority 任务优先级
     * @return 任务ID
     */
    public String createTask(String taskType, int limit, TaskPriority priority) {
        String taskId = generateTaskId();
        List<String> symbols = new ArrayList<>();
        symbols.add("SYSTEM");
        
        TaskInfo taskInfo = new TaskInfo(taskId, taskType, symbols, "", limit, 
                LocalDateTime.now(), LocalDateTime.now().plusHours(1));
        taskInfo.setPriority(priority);
        
        tasks.put(taskId, taskInfo);
        
        log.info("创建简化任务: taskId={}, taskType={}, priority={}", taskId, taskType, priority);
        
        return taskId;
    }
    
    /**
     * 更新任务进度
     * 
     * @param taskId 任务ID
     * @param current 当前进度
     * @param total 总进度
     * @param message 进度消息
     */
    public void updateTaskProgress(String taskId, int current, int total, String message) {
        TaskInfo taskInfo = tasks.get(taskId);
        if (taskInfo != null) {
            taskInfo.processedSymbols = current;
            taskInfo.totalSymbols = total;
            taskInfo.message = message;
            taskInfo.updateTime = LocalDateTime.now();
            
            // 如果进度达到100%，则更新状态为已完成
            if (current >= total && total > 0) {
                taskInfo.status = TaskStatus.COMPLETED;
            }
            
            log.debug("更新任务进度: taskId={}, progress={}/{}, message={}", 
                    taskId, current, total, message);
        }
    }
} 