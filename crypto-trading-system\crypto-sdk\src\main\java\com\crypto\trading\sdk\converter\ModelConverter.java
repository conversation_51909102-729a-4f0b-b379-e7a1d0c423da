package com.crypto.trading.sdk.converter;

/**
 * 模型转换器接口
 * 定义API模型和DTO之间的转换方法
 */
public interface ModelConverter {

    /**
     * 将API模型转换为DTO
     *
     * @param apiModel API模型
     * @param dtoClass DTO类型
     * @param <A> API模型类型
     * @param <D> DTO类型
     * @return 转换后的DTO
     */
    <A, D> D toDto(A apiModel, Class<D> dtoClass);

    /**
     * 将DTO转换为API模型
     *
     * @param dto DTO
     * @param apiModelClass API模型类型
     * @param <A> API模型类型
     * @param <D> DTO类型
     * @return 转换后的API模型
     */
    <A, D> A toApiModel(D dto, Class<A> apiModelClass);
} 