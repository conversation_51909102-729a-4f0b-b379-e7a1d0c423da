package com.crypto.trading.common.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Duration;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * 线程工具类
 * <p>
 * 提供线程和并发操作相关的工具方法，如创建线程池、异步执行等
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public final class ThreadUtil {
    private static final Logger log = LoggerFactory.getLogger(ThreadUtil.class);

    /**
     * 私有构造函数，防止实例化
     */
    private ThreadUtil() {
        throw new IllegalStateException("工具类不允许实例化");
    }

    /**
     * 默认CPU核心数
     */
    private static final int CPU_COUNT = Runtime.getRuntime().availableProcessors();

    /**
     * 默认线程池核心线程数
     */
    private static final int CORE_POOL_SIZE = Math.max(2, Math.min(CPU_COUNT - 1, 4));

    /**
     * 默认线程池最大线程数
     */
    private static final int MAX_POOL_SIZE = CPU_COUNT * 2 + 1;

    /**
     * 默认线程池队列大小
     */
    private static final int QUEUE_CAPACITY = 1024;

    /**
     * 默认线程池线程空闲超时时间（秒）
     */
    private static final long KEEP_ALIVE_SECONDS = 60L;

    /**
     * 默认线程池线程命名前缀
     */
    private static final String DEFAULT_THREAD_NAME_PREFIX = "thread-pool-";

    /**
     * 默认线程池
     */
    private static final ThreadPoolExecutor DEFAULT_EXECUTOR = createThreadPool(
            CORE_POOL_SIZE,
            MAX_POOL_SIZE,
            KEEP_ALIVE_SECONDS,
            QUEUE_CAPACITY,
            DEFAULT_THREAD_NAME_PREFIX
    );

    /**
     * 默认虚拟线程池
     */
    private static final ExecutorService DEFAULT_VIRTUAL_EXECUTOR = Executors.newVirtualThreadPerTaskExecutor();

    /**
     * 创建线程池
     *
     * @param corePoolSize    核心线程数
     * @param maximumPoolSize 最大线程数
     * @param keepAliveTime   线程空闲超时时间（秒）
     * @param queueCapacity   队列大小
     * @param threadNamePrefix 线程命名前缀
     * @return 线程池
     */
    public static ThreadPoolExecutor createThreadPool(
            int corePoolSize,
            int maximumPoolSize,
            long keepAliveTime,
            int queueCapacity,
            String threadNamePrefix) {
        BlockingQueue<Runnable> workQueue = new LinkedBlockingQueue<>(queueCapacity);
        
        ThreadFactory threadFactory = new ThreadFactory() {
            private final AtomicInteger threadNumber = new AtomicInteger(1);
            
            @Override
            public Thread newThread(Runnable r) {
                Thread thread = new Thread(r, threadNamePrefix + threadNumber.getAndIncrement());
                thread.setDaemon(false);
                return thread;
            }
        };
        
        RejectedExecutionHandler rejectedExecutionHandler = new ThreadPoolExecutor.CallerRunsPolicy();
        
        ThreadPoolExecutor executor = new ThreadPoolExecutor(
                corePoolSize,
                maximumPoolSize,
                keepAliveTime,
                TimeUnit.SECONDS,
                workQueue,
                threadFactory,
                rejectedExecutionHandler
        );
        
        log.info("创建线程池成功: corePoolSize={}, maximumPoolSize={}, queueCapacity={}, threadNamePrefix={}",
                corePoolSize, maximumPoolSize, queueCapacity, threadNamePrefix);
        
        return executor;
    }

    /**
     * 创建虚拟线程池
     *
     * @return 虚拟线程池
     */
    public static ExecutorService createVirtualThreadPool() {
        return Executors.newVirtualThreadPerTaskExecutor();
    }

    /**
     * 获取默认线程池
     *
     * @return 默认线程池
     */
    public static ThreadPoolExecutor getDefaultExecutor() {
        return DEFAULT_EXECUTOR;
    }

    /**
     * 获取默认虚拟线程池
     *
     * @return 默认虚拟线程池
     */
    public static ExecutorService getDefaultVirtualExecutor() {
        return DEFAULT_VIRTUAL_EXECUTOR;
    }

    /**
     * 异步执行任务（使用默认线程池）
     *
     * @param task 任务
     * @return Future对象
     */
    public static Future<?> execute(Runnable task) {
        return DEFAULT_EXECUTOR.submit(task);
    }

    /**
     * 异步执行任务（使用默认虚拟线程池）
     *
     * @param task 任务
     * @return Future对象
     */
    public static Future<?> executeVirtual(Runnable task) {
        return DEFAULT_VIRTUAL_EXECUTOR.submit(task);
    }

    /**
     * 异步执行有返回值的任务（使用默认线程池）
     *
     * @param task 任务
     * @param <T>  返回值类型
     * @return Future对象
     */
    public static <T> Future<T> submit(Callable<T> task) {
        return DEFAULT_EXECUTOR.submit(task);
    }

    /**
     * 异步执行有返回值的任务（使用默认虚拟线程池）
     *
     * @param task 任务
     * @param <T>  返回值类型
     * @return Future对象
     */
    public static <T> Future<T> submitVirtual(Callable<T> task) {
        return DEFAULT_VIRTUAL_EXECUTOR.submit(task);
    }

    /**
     * 异步执行有返回值的任务（使用默认线程池，返回CompletableFuture）
     *
     * @param supplier 任务
     * @param <T>      返回值类型
     * @return CompletableFuture对象
     */
    public static <T> CompletableFuture<T> supplyAsync(Supplier<T> supplier) {
        return CompletableFuture.supplyAsync(supplier, DEFAULT_EXECUTOR);
    }

    /**
     * 异步执行有返回值的任务（使用默认虚拟线程池，返回CompletableFuture）
     *
     * @param supplier 任务
     * @param <T>      返回值类型
     * @return CompletableFuture对象
     */
    public static <T> CompletableFuture<T> supplyAsyncVirtual(Supplier<T> supplier) {
        return CompletableFuture.supplyAsync(supplier, DEFAULT_VIRTUAL_EXECUTOR);
    }

    /**
     * 异步执行无返回值的任务（使用默认线程池，返回CompletableFuture）
     *
     * @param runnable 任务
     * @return CompletableFuture对象
     */
    public static CompletableFuture<Void> runAsync(Runnable runnable) {
        return CompletableFuture.runAsync(runnable, DEFAULT_EXECUTOR);
    }

    /**
     * 异步执行无返回值的任务（使用默认虚拟线程池，返回CompletableFuture）
     *
     * @param runnable 任务
     * @return CompletableFuture对象
     */
    public static CompletableFuture<Void> runAsyncVirtual(Runnable runnable) {
        return CompletableFuture.runAsync(runnable, DEFAULT_VIRTUAL_EXECUTOR);
    }

    /**
     * 使用虚拟线程执行任务
     *
     * @param task 任务
     * @return 线程对象
     */
    public static Thread startVirtualThread(Runnable task) {
        return Thread.startVirtualThread(task);
    }

    /**
     * 使用虚拟线程执行任务
     *
     * @param name 线程名称
     * @param task 任务
     * @return 线程对象
     */
    public static Thread startVirtualThread(String name, Runnable task) {
        Thread thread = Thread.ofVirtual().name(name).unstarted(task);
        thread.start();
        return thread;
    }

    /**
     * 并行处理集合元素
     *
     * @param collection 集合
     * @param action     处理操作
     * @param <T>        元素类型
     */
    public static <T> void parallelForEach(Collection<T> collection, Consumer<T> action) {
        collection.parallelStream().forEach(action);
    }

    /**
     * 并行处理集合元素（使用虚拟线程）
     *
     * @param collection 集合
     * @param action     处理操作
     * @param <T>        元素类型
     * @throws InterruptedException 如果等待过程中线程被中断
     */
    public static <T> void virtualParallelForEach(Collection<T> collection, Consumer<T> action) 
            throws InterruptedException {
        List<CompletableFuture<Void>> futures = collection.stream()
                .map(item -> runAsyncVirtual(() -> action.accept(item)))
                .collect(Collectors.toList());
        
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
    }

    /**
     * 并行转换集合元素
     *
     * @param collection 集合
     * @param function   转换函数
     * @param <T>        源元素类型
     * @param <R>        目标元素类型
     * @return 转换后的列表
     */
    public static <T, R> List<R> parallelMap(Collection<T> collection, Function<T, R> function) {
        return collection.parallelStream().map(function).collect(Collectors.toList());
    }

    /**
     * 并行转换集合元素（使用虚拟线程）
     *
     * @param collection 集合
     * @param function   转换函数
     * @param <T>        源元素类型
     * @param <R>        目标元素类型
     * @return 转换后的列表
     * @throws InterruptedException 如果等待过程中线程被中断
     */
    public static <T, R> List<R> virtualParallelMap(Collection<T> collection, Function<T, R> function) 
            throws InterruptedException {
        List<CompletableFuture<R>> futures = collection.stream()
                .map(item -> supplyAsyncVirtual(() -> function.apply(item)))
                .collect(Collectors.toList());
        
        return futures.stream()
                .map(CompletableFuture::join)
                .collect(Collectors.toList());
    }

    /**
     * 休眠指定时间
     *
     * @param millis 毫秒数
     */
    public static void sleep(long millis) {
        try {
            Thread.sleep(millis);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.warn("线程休眠被中断", e);
        }
    }

    /**
     * 休眠指定时间
     *
     * @param duration 时长
     */
    public static void sleep(Duration duration) {
        sleep(duration.toMillis());
    }

    /**
     * 等待Future完成
     *
     * @param future  Future对象
     * @param timeout 超时时间（毫秒）
     * @param <T>     返回值类型
     * @return 返回值
     * @throws TimeoutException      如果等待超时
     * @throws InterruptedException  如果等待过程中线程被中断
     * @throws ExecutionException    如果执行过程中发生异常
     */
    public static <T> T waitFor(Future<T> future, long timeout) 
            throws TimeoutException, InterruptedException, ExecutionException {
        return future.get(timeout, TimeUnit.MILLISECONDS);
    }

    /**
     * 等待Future完成
     *
     * @param future   Future对象
     * @param duration 超时时长
     * @param <T>      返回值类型
     * @return 返回值
     * @throws TimeoutException      如果等待超时
     * @throws InterruptedException  如果等待过程中线程被中断
     * @throws ExecutionException    如果执行过程中发生异常
     */
    public static <T> T waitFor(Future<T> future, Duration duration) 
            throws TimeoutException, InterruptedException, ExecutionException {
        return future.get(duration.toMillis(), TimeUnit.MILLISECONDS);
    }

    /**
     * 关闭线程池
     *
     * @param executor 线程池
     * @param timeout  等待终止的超时时间（秒）
     */
    public static void shutdown(ExecutorService executor, long timeout) {
        if (executor != null && !executor.isShutdown()) {
            executor.shutdown();
            try {
                if (!executor.awaitTermination(timeout, TimeUnit.SECONDS)) {
                    executor.shutdownNow();
                    if (!executor.awaitTermination(timeout, TimeUnit.SECONDS)) {
                        log.error("线程池无法终止");
                    }
                }
            } catch (InterruptedException e) {
                executor.shutdownNow();
                Thread.currentThread().interrupt();
                log.warn("等待线程池终止被中断", e);
            }
        }
    }

    /**
     * 获取当前线程名称
     *
     * @return 线程名称
     */
    public static String getCurrentThreadName() {
        return Thread.currentThread().getName();
    }

    /**
     * 获取线程ID
     *
     * @return 线程ID
     */
    public static long getCurrentThreadId() {
        return Thread.currentThread().threadId();
    }

    /**
     * 获取当前线程的堆栈跟踪
     *
     * @return 堆栈跟踪
     */
    public static StackTraceElement[] getCurrentStackTrace() {
        return Thread.currentThread().getStackTrace();
    }
    
    /**
     * 将ThreadFactory转换为Executor
     *
     * @param threadFactory 线程工厂
     * @return 执行器
     */
    public static Executor threadFactoryToExecutor(ThreadFactory threadFactory) {
        return Executors.newCachedThreadPool(threadFactory);
    }
    
    /**
     * 将ThreadFactory转换为Executor（别名方法）
     *
     * @param threadFactory 线程工厂
     * @return 执行器
     */
    public static Executor toExecutor(ThreadFactory threadFactory) {
        return threadFactoryToExecutor(threadFactory);
    }

    /**
     * 创建虚拟线程执行器，带有线程名前缀
     *
     * @param namePrefix 线程名前缀
     * @return 虚拟线程执行器
     */
    public static ExecutorService newVirtualThreadExecutor(String namePrefix) {
        return Executors.newThreadPerTaskExecutor(Thread.ofVirtual().name(namePrefix).factory());
    }

    /**
     * 创建固定大小的线程池
     *
     * @param nThreads 线程数
     * @param namePrefix 线程名前缀
     * @return 线程池
     */
    public static ExecutorService newFixedThreadPool(int nThreads, String namePrefix) {
        return Executors.newFixedThreadPool(nThreads, r -> {
            Thread t = Executors.defaultThreadFactory().newThread(r);
            t.setName(namePrefix + "-" + t.getName());
            return t;
        });
    }
} 