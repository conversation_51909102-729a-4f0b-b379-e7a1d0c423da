<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ProxyAuth.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">io.github.binance:binance-futures-connector-java</a> &gt; <a href="index.source.html" class="el_package">com.binance.connector.futures.client.utils</a> &gt; <span class="el_source">ProxyAuth.java</span></div><h1>ProxyAuth.java</h1><pre class="source lang-java linenums">package com.binance.connector.futures.client.utils;

import okhttp3.Authenticator;
import java.net.Proxy;

public final class ProxyAuth {
    private Proxy proxy;
    private Authenticator auth;
    
<span class="nc" id="L10">    public ProxyAuth(Proxy proxy, Authenticator auth) {</span>
<span class="nc" id="L11">        this.proxy = proxy;</span>
<span class="nc" id="L12">        this.auth = auth;</span>
<span class="nc" id="L13">    }</span>

    public Proxy getProxy() {
<span class="nc" id="L16">        return proxy;</span>
    }

    public Authenticator getAuth() {
<span class="nc" id="L20">        return auth;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.7.202105040129</span></div></body></html>