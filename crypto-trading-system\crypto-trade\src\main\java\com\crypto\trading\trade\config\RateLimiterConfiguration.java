package com.crypto.trading.trade.config;

import io.github.resilience4j.ratelimiter.RateLimiter;
import io.github.resilience4j.ratelimiter.RateLimiterConfig;
import io.github.resilience4j.ratelimiter.RateLimiterRegistry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;

/**
 * 限流器配置
 * <p>
 * 用于配置系统中的各种限流器，防止超过API调用限制
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Configuration
public class RateLimiterConfiguration {

    private static final Logger log = LoggerFactory.getLogger(RateLimiterConfiguration.class);

    @Value("${trade.ratelimit.order.limitForPeriod:10}")
    private int orderLimitForPeriod;

    @Value("${trade.ratelimit.order.limitRefreshPeriod:1}")
    private int orderLimitRefreshPeriod;

    @Value("${trade.ratelimit.query.limitForPeriod:20}")
    private int queryLimitForPeriod;

    @Value("${trade.ratelimit.query.limitRefreshPeriod:1}")
    private int queryLimitRefreshPeriod;

    /**
     * 订单操作限流器
     * <p>
     * 用于限制下单、撤单等操作的频率
     * </p>
     *
     * @return 订单操作限流器
     */
    @Bean(name = "orderRateLimiter")
    public RateLimiter orderRateLimiter() {
        log.info("初始化订单限流器: limitForPeriod={}, limitRefreshPeriod={}秒",
                orderLimitForPeriod, orderLimitRefreshPeriod);

        RateLimiterConfig config = RateLimiterConfig.custom()
                .limitForPeriod(orderLimitForPeriod)
                .limitRefreshPeriod(Duration.ofSeconds(orderLimitRefreshPeriod))
                .timeoutDuration(Duration.ofSeconds(5))
                .build();

        RateLimiterRegistry registry = RateLimiterRegistry.of(config);
        return registry.rateLimiter("orderRateLimiter");
    }

    /**
     * 查询操作限流器
     * <p>
     * 用于限制查询操作的频率
     * </p>
     *
     * @return 查询操作限流器
     */
    @Bean(name = "queryRateLimiter")
    public RateLimiter queryRateLimiter() {
        log.info("初始化查询限流器: limitForPeriod={}, limitRefreshPeriod={}秒",
                queryLimitForPeriod, queryLimitRefreshPeriod);

        RateLimiterConfig config = RateLimiterConfig.custom()
                .limitForPeriod(queryLimitForPeriod)
                .limitRefreshPeriod(Duration.ofSeconds(queryLimitRefreshPeriod))
                .timeoutDuration(Duration.ofSeconds(3))
                .build();

        RateLimiterRegistry registry = RateLimiterRegistry.of(config);
        return registry.rateLimiter("queryRateLimiter");
    }
}