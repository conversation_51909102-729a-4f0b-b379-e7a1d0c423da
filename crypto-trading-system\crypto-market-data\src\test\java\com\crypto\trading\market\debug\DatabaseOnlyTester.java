package com.crypto.trading.market.debug;

import com.crypto.trading.common.dto.market.DepthDataDTO;
import com.crypto.trading.market.repository.InfluxDBRepository;
import com.crypto.trading.market.repository.MySQLMarketDataRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 数据库存储测试器（跳过Kafka）
 * 直接测试MySQL和InfluxDB的存储功能
 */
@SpringBootApplication
@ComponentScan(
    basePackages = "com.crypto.trading",
    excludeFilters = {
        @ComponentScan.Filter(type = FilterType.REGEX, pattern = ".*Kafka.*"),
        @ComponentScan.Filter(type = FilterType.REGEX, pattern = ".*Producer.*"),
        @ComponentScan.Filter(type = FilterType.REGEX, pattern = ".*Consumer.*")
    }
)
public class DatabaseOnlyTester implements CommandLineRunner {

    private static final Logger log = LoggerFactory.getLogger(DatabaseOnlyTester.class);

    @Autowired(required = false)
    private InfluxDBRepository influxDBRepository;

    @Autowired(required = false)
    private MySQLMarketDataRepository mySQLRepository;

    public static void main(String[] args) {
        // 设置环境变量，避免Kafka配置问题
        System.setProperty("spring.kafka.bootstrap-servers", "localhost:29092");
        System.setProperty("spring.profiles.active", "test");
        
        SpringApplication.run(DatabaseOnlyTester.class, args);
    }

    @Override
    public void run(String... args) throws Exception {
        log.info("开始数据库存储测试...");

        // 测试MySQL存储
        testMySQLStorage();

        // 测试InfluxDB存储
        testInfluxDBStorage();

        log.info("数据库存储测试完成");
        System.exit(0);
    }

    private void testMySQLStorage() {
        log.info("=== 测试MySQL存储 ===");
        
        if (mySQLRepository == null) {
            log.error("❌ MySQL Repository未注入");
            return;
        }

        try {
            DepthDataDTO testDTO = createTestDepthDataDTO();
            log.info("开始测试MySQL存储...");
            
            boolean result = mySQLRepository.saveDepthData(testDTO);
            log.info("MySQL存储调用结果: {}", result ? "✅ 成功" : "❌ 失败");
            
            // 等待异步处理完成
            Thread.sleep(3000);
            log.info("MySQL存储测试完成");
            
        } catch (Exception e) {
            log.error("❌ MySQL存储测试失败", e);
        }
    }

    private void testInfluxDBStorage() {
        log.info("=== 测试InfluxDB存储 ===");
        
        if (influxDBRepository == null) {
            log.error("❌ InfluxDB Repository未注入");
            return;
        }

        try {
            DepthDataDTO testDTO = createTestDepthDataDTO();
            log.info("开始测试InfluxDB存储...");
            
            influxDBRepository.saveOrderBookData(testDTO);
            log.info("✅ InfluxDB存储调用完成");
            
            // 等待异步处理完成
            Thread.sleep(3000);
            log.info("InfluxDB存储测试完成");
            
        } catch (Exception e) {
            log.error("❌ InfluxDB存储测试失败", e);
        }
    }

    private DepthDataDTO createTestDepthDataDTO() {
        DepthDataDTO dto = new DepthDataDTO();
        dto.setSymbol("ETHUSDT");
        dto.setLastUpdateId(53820091842L);
        dto.setLimit(5);
        dto.setUpdateTime(LocalDateTime.now());
        
        // 创建测试买盘数据
        List<DepthDataDTO.PriceQuantity> bids = new ArrayList<>();
        bids.add(new DepthDataDTO.PriceQuantity(new BigDecimal("2437.68"), new BigDecimal("1.804")));
        bids.add(new DepthDataDTO.PriceQuantity(new BigDecimal("2437.67"), new BigDecimal("32.925")));
        dto.setBids(bids);
        
        // 创建测试卖盘数据
        List<DepthDataDTO.PriceQuantity> asks = new ArrayList<>();
        asks.add(new DepthDataDTO.PriceQuantity(new BigDecimal("2437.82"), new BigDecimal("1.637")));
        asks.add(new DepthDataDTO.PriceQuantity(new BigDecimal("2437.91"), new BigDecimal("2.052")));
        dto.setAsks(asks);
        
        log.info("创建测试数据: symbol={}, lastUpdateId={}, bids={}, asks={}", 
                dto.getSymbol(), dto.getLastUpdateId(), 
                dto.getBids().size(), dto.getAsks().size());
        
        return dto;
    }
}
