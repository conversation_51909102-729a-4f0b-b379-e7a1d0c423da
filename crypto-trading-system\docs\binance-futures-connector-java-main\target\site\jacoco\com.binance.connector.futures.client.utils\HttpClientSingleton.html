<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>HttpClientSingleton</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">io.github.binance:binance-futures-connector-java</a> &gt; <a href="index.html" class="el_package">com.binance.connector.futures.client.utils</a> &gt; <span class="el_class">HttpClientSingleton</span></div><h1>HttpClientSingleton</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">36 of 66</td><td class="ctr2">45%</td><td class="bar">10 of 16</td><td class="ctr2">37%</td><td class="ctr1">8</td><td class="ctr2">13</td><td class="ctr1">7</td><td class="ctr2">18</td><td class="ctr1">1</td><td class="ctr2">5</td></tr></tfoot><tbody><tr><td id="a0"><a href="HttpClientSingleton.java.html#L29" class="el_method">createHttpClient(ProxyAuth)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="89" height="10" title="23" alt="23"/><img src="../jacoco-resources/greenbar.gif" width="30" height="10" title="8" alt="8"/></td><td class="ctr2" id="c3">25%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="45" height="10" title="3" alt="3"/><img src="../jacoco-resources/greenbar.gif" width="15" height="10" title="1" alt="1"/></td><td class="ctr2" id="e2">25%</td><td class="ctr1" id="f1">2</td><td class="ctr2" id="g1">3</td><td class="ctr1" id="h0">3</td><td class="ctr2" id="i0">6</td><td class="ctr1" id="j1">0</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a4"><a href="HttpClientSingleton.java.html#L41" class="el_method">verifyHttpClient(ProxyAuth)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="27" height="10" title="7" alt="7"/><img src="../jacoco-resources/greenbar.gif" width="38" height="10" title="10" alt="10"/></td><td class="ctr2" id="c2">58%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="75" height="10" title="5" alt="5"/><img src="../jacoco-resources/greenbar.gif" width="45" height="10" title="3" alt="3"/></td><td class="ctr2" id="e1">37%</td><td class="ctr1" id="f0">4</td><td class="ctr2" id="g0">5</td><td class="ctr1" id="h2">1</td><td class="ctr2" id="i1">4</td><td class="ctr1" id="j2">0</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a1"><a href="HttpClientSingleton.java.html#L13" class="el_method">getHttpClient()</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="23" height="10" title="6" alt="6"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="30" height="10" title="2" alt="2"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f2">2</td><td class="ctr2" id="g2">2</td><td class="ctr1" id="h1">3</td><td class="ctr2" id="i3">3</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a2"><a href="HttpClientSingleton.java.html#L20" class="el_method">getHttpClient(ProxyAuth)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/greenbar.gif" width="34" height="10" title="9" alt="9"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d3"><img src="../jacoco-resources/greenbar.gif" width="30" height="10" title="2" alt="2"/></td><td class="ctr2" id="e0">100%</td><td class="ctr1" id="f3">0</td><td class="ctr2" id="g3">2</td><td class="ctr1" id="h3">0</td><td class="ctr2" id="i2">4</td><td class="ctr1" id="j3">0</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a3"><a href="HttpClientSingleton.java.html#L7" class="el_method">static {...}</a></td><td class="bar" id="b4"><img src="../jacoco-resources/greenbar.gif" width="11" height="10" title="3" alt="3"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d4"/><td class="ctr2" id="e4">n/a</td><td class="ctr1" id="f4">0</td><td class="ctr2" id="g4">1</td><td class="ctr1" id="h4">0</td><td class="ctr2" id="i4">1</td><td class="ctr1" id="j4">0</td><td class="ctr2" id="k4">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.7.202105040129</span></div></body></html>