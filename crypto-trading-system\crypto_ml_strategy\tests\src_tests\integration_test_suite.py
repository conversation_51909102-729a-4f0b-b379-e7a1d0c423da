#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
集成测试套件模块

该模块提供Java系统集成测试功能，包括端到端测试、组件交互测试、
数据流验证和系统健康检查。
"""

import asyncio
import time
import json
import uuid
from typing import Dict, List, Any, Optional, Callable, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime, timezone
from concurrent.futures import ThreadPoolExecutor
from loguru import logger

from .config import Config
from .kafka_client import KafkaClient
from .java_api_core import ValidationResult, PerformanceMetrics
from .data.optimized_timeframe_sync import OptimizedTimeframeSync
from .data.data_quality_checker import <PERSON>QualityChecker
from .data.cache_manager import CacheManager as IntegratedCacheManager


@dataclass
class IntegrationTestConfig:
    """集成测试配置"""
    test_timeout_seconds: int = 300
    max_retry_attempts: int = 3
    retry_delay_seconds: int = 5
    health_check_interval_seconds: int = 30
    test_data_symbols: List[str] = None
    test_timeframes: List[str] = None
    
    def __post_init__(self):
        if self.test_data_symbols is None:
            self.test_data_symbols = ["BTCUSDT", "ETHUSDT"]
        if self.test_timeframes is None:
            self.test_timeframes = ["1m", "5m", "15m", "1h", "4h", "1d"]


class JavaIntegrationTestSuite:
    """
    Java系统集成测试套件
    
    提供与Java交易系统的全面集成测试功能。
    """
    
    def __init__(self, config: Config):
        """
        初始化Java集成测试套件
        
        Args:
            config: 配置对象
        """
        self.config = config
        self.kafka_client = KafkaClient(config)
        self.test_config = IntegrationTestConfig()
        self.test_results: List[ValidationResult] = []
        
        # 初始化组件
        self.timeframe_sync = OptimizedTimeframeSync(config)
        self.data_quality_checker = DataQualityChecker(config)
        self.cache_manager = IntegratedCacheManager(config)
        
        logger.info("Java集成测试套件初始化完成")
    
    async def run_full_integration_test(self) -> Dict[str, Any]:
        """
        运行完整的集成测试
        
        Returns:
            集成测试结果
        """
        logger.info("开始完整集成测试")
        
        test_results = {
            "test_start_time": datetime.now(timezone.utc).isoformat(),
            "component_tests": {},
            "integration_tests": {},
            "performance_tests": {},
            "health_checks": {},
            "overall_status": "UNKNOWN"
        }
        
        try:
            # 1. 组件单独测试
            test_results["component_tests"] = await self._test_individual_components()
            
            # 2. 组件集成测试
            test_results["integration_tests"] = await self._test_component_integration()
            
            # 3. 端到端测试
            test_results["end_to_end_tests"] = await self._test_end_to_end_flow()
            
            # 4. 性能集成测试
            test_results["performance_tests"] = await self._test_integration_performance()
            
            # 5. 系统健康检查
            test_results["health_checks"] = await self._perform_health_checks()
            
            # 6. 评估整体状态
            test_results["overall_status"] = self._evaluate_overall_status(test_results)
            
        except Exception as e:
            logger.error(f"集成测试异常: {e}")
            test_results["error"] = str(e)
            test_results["overall_status"] = "FAILED"
        
        test_results["test_end_time"] = datetime.now(timezone.utc).isoformat()
        
        logger.info(f"完整集成测试完成，状态: {test_results['overall_status']}")
        return test_results
    
    async def _test_individual_components(self) -> Dict[str, ValidationResult]:
        """测试各个组件的独立功能"""
        logger.info("开始组件独立功能测试")
        
        results = {}
        
        # 测试时间框架同步器
        results["timeframe_sync"] = await self._test_timeframe_sync_component()
        
        # 测试数据质量检查器
        results["data_quality_checker"] = await self._test_data_quality_component()
        
        # 测试缓存管理器
        results["cache_manager"] = await self._test_cache_manager_component()
        
        # 测试Kafka客户端
        results["kafka_client"] = await self._test_kafka_client_component()
        
        logger.info("组件独立功能测试完成")
        return results
    
    async def _test_timeframe_sync_component(self) -> ValidationResult:
        """测试时间框架同步组件"""
        start_time = time.time()
        
        try:
            # 创建测试数据
            test_data = self._create_test_kline_data()
            
            # 测试同步功能
            sync_result = await self.timeframe_sync.sync_timeframes(
                test_data, self.test_config.test_timeframes
            )
            
            # 验证同步结果
            if sync_result and len(sync_result) == len(self.test_config.test_timeframes):
                return ValidationResult(
                    test_name="timeframe_sync_component",
                    success=True,
                    message="时间框架同步组件测试通过",
                    details={
                        "synced_timeframes": len(sync_result),
                        "test_data_points": len(test_data)
                    },
                    execution_time_ms=(time.time() - start_time) * 1000,
                    timestamp=datetime.now(timezone.utc)
                )
            else:
                return ValidationResult(
                    test_name="timeframe_sync_component",
                    success=False,
                    message="时间框架同步结果不符合预期",
                    details={
                        "expected_timeframes": len(self.test_config.test_timeframes),
                        "actual_timeframes": len(sync_result) if sync_result else 0
                    },
                    execution_time_ms=(time.time() - start_time) * 1000,
                    timestamp=datetime.now(timezone.utc)
                )
                
        except Exception as e:
            logger.error(f"时间框架同步组件测试异常: {e}")
            return ValidationResult(
                test_name="timeframe_sync_component",
                success=False,
                message=f"测试异常: {str(e)}",
                details={"exception": str(e)},
                execution_time_ms=(time.time() - start_time) * 1000,
                timestamp=datetime.now(timezone.utc)
            )
    
    async def _test_data_quality_component(self) -> ValidationResult:
        """测试数据质量检查组件"""
        start_time = time.time()
        
        try:
            # 创建包含质量问题的测试数据
            test_data = self._create_test_data_with_quality_issues()
            
            # 执行数据质量检查
            quality_result = await self.data_quality_checker.check_data_quality(test_data)
            
            # 验证检查结果
            if quality_result and "issues_found" in quality_result:
                return ValidationResult(
                    test_name="data_quality_component",
                    success=True,
                    message="数据质量检查组件测试通过",
                    details={
                        "issues_detected": quality_result.get("issues_found", 0),
                        "data_points_checked": len(test_data)
                    },
                    execution_time_ms=(time.time() - start_time) * 1000,
                    timestamp=datetime.now(timezone.utc)
                )
            else:
                return ValidationResult(
                    test_name="data_quality_component",
                    success=False,
                    message="数据质量检查结果异常",
                    details={"result": quality_result},
                    execution_time_ms=(time.time() - start_time) * 1000,
                    timestamp=datetime.now(timezone.utc)
                )
                
        except Exception as e:
            logger.error(f"数据质量组件测试异常: {e}")
            return ValidationResult(
                test_name="data_quality_component",
                success=False,
                message=f"测试异常: {str(e)}",
                details={"exception": str(e)},
                execution_time_ms=(time.time() - start_time) * 1000,
                timestamp=datetime.now(timezone.utc)
            )
    
    async def _test_cache_manager_component(self) -> ValidationResult:
        """测试缓存管理器组件"""
        start_time = time.time()
        
        try:
            # 测试缓存存储和检索
            test_key = "test_cache_key"
            test_data = {"test": "data", "timestamp": time.time()}
            
            # 存储数据
            await self.cache_manager.set(test_key, test_data)
            
            # 检索数据
            retrieved_data = await self.cache_manager.get(test_key)
            
            # 验证数据一致性
            if retrieved_data == test_data:
                return ValidationResult(
                    test_name="cache_manager_component",
                    success=True,
                    message="缓存管理器组件测试通过",
                    details={
                        "cache_operation": "set_and_get",
                        "data_consistency": True
                    },
                    execution_time_ms=(time.time() - start_time) * 1000,
                    timestamp=datetime.now(timezone.utc)
                )
            else:
                return ValidationResult(
                    test_name="cache_manager_component",
                    success=False,
                    message="缓存数据不一致",
                    details={
                        "original_data": test_data,
                        "retrieved_data": retrieved_data
                    },
                    execution_time_ms=(time.time() - start_time) * 1000,
                    timestamp=datetime.now(timezone.utc)
                )
                
        except Exception as e:
            logger.error(f"缓存管理器组件测试异常: {e}")
            return ValidationResult(
                test_name="cache_manager_component",
                success=False,
                message=f"测试异常: {str(e)}",
                details={"exception": str(e)},
                execution_time_ms=(time.time() - start_time) * 1000,
                timestamp=datetime.now(timezone.utc)
            )
    
    async def _test_kafka_client_component(self) -> ValidationResult:
        """测试Kafka客户端组件"""
        start_time = time.time()
        
        try:
            # 创建测试消息
            test_message = {
                "messageId": str(uuid.uuid4()),
                "messageType": "integration_test",
                "timestamp": int(time.time() * 1000),
                "data": {"test": True}
            }
            
            # 发送消息
            self.kafka_client.publish_signal(test_message)
            
            # 简单验证（实际环境中需要消费者验证）
            return ValidationResult(
                test_name="kafka_client_component",
                success=True,
                message="Kafka客户端组件测试通过",
                details={
                    "message_sent": True,
                    "message_id": test_message["messageId"]
                },
                execution_time_ms=(time.time() - start_time) * 1000,
                timestamp=datetime.now(timezone.utc)
            )
            
        except Exception as e:
            logger.error(f"Kafka客户端组件测试异常: {e}")
            return ValidationResult(
                test_name="kafka_client_component",
                success=False,
                message=f"测试异常: {str(e)}",
                details={"exception": str(e)},
                execution_time_ms=(time.time() - start_time) * 1000,
                timestamp=datetime.now(timezone.utc)
            )
    
    async def _test_component_integration(self) -> Dict[str, ValidationResult]:
        """测试组件间集成"""
        logger.info("开始组件集成测试")
        
        results = {}
        
        # 测试数据流集成
        results["data_flow_integration"] = await self._test_data_flow_integration()
        
        # 测试缓存集成
        results["cache_integration"] = await self._test_cache_integration()
        
        # 测试错误处理集成
        results["error_handling_integration"] = await self._test_error_handling_integration()
        
        logger.info("组件集成测试完成")
        return results
    
    async def _test_data_flow_integration(self) -> ValidationResult:
        """测试数据流集成"""
        start_time = time.time()
        
        try:
            # 创建测试数据
            raw_data = self._create_test_kline_data()
            
            # 1. 数据质量检查
            quality_result = await self.data_quality_checker.check_data_quality(raw_data)
            
            # 2. 时间框架同步
            sync_result = await self.timeframe_sync.sync_timeframes(
                raw_data, ["1m", "5m", "15m"]
            )
            
            # 3. 缓存结果
            cache_key = f"integrated_data_{int(time.time())}"
            await self.cache_manager.set(cache_key, sync_result)
            
            # 4. 验证缓存数据
            cached_data = await self.cache_manager.get(cache_key)
            
            success = (quality_result is not None and 
                      sync_result is not None and 
                      cached_data == sync_result)
            
            return ValidationResult(
                test_name="data_flow_integration",
                success=success,
                message=f"数据流集成测试{'通过' if success else '失败'}",
                details={
                    "quality_check": quality_result is not None,
                    "sync_result": sync_result is not None,
                    "cache_consistency": cached_data == sync_result
                },
                execution_time_ms=(time.time() - start_time) * 1000,
                timestamp=datetime.now(timezone.utc)
            )
            
        except Exception as e:
            logger.error(f"数据流集成测试异常: {e}")
            return ValidationResult(
                test_name="data_flow_integration",
                success=False,
                message=f"测试异常: {str(e)}",
                details={"exception": str(e)},
                execution_time_ms=(time.time() - start_time) * 1000,
                timestamp=datetime.now(timezone.utc)
            )
    
    def _create_test_kline_data(self) -> List[Dict[str, Any]]:
        """创建测试K线数据"""
        base_time = int(time.time() * 1000)
        test_data = []
        
        for i in range(100):
            test_data.append({
                "symbol": "BTCUSDT",
                "interval": "1m",
                "openTime": base_time + i * 60000,
                "closeTime": base_time + (i + 1) * 60000,
                "open": 50000 + i * 10,
                "high": 50100 + i * 10,
                "low": 49900 + i * 10,
                "close": 50050 + i * 10,
                "volume": 1000 + i * 5
            })
        
        return test_data
    
    def _create_test_data_with_quality_issues(self) -> List[Dict[str, Any]]:
        """创建包含质量问题的测试数据"""
        test_data = self._create_test_kline_data()
        
        # 添加质量问题
        if len(test_data) > 10:
            # 缺失值
            test_data[5]["close"] = None
            # 异常值
            test_data[10]["high"] = 999999
            # 重复时间戳
            test_data[15]["openTime"] = test_data[14]["openTime"]
        
        return test_data
    
    async def _test_cache_integration(self) -> ValidationResult:
        """测试缓存集成"""
        start_time = time.time()
        
        try:
            # 测试与其他组件的缓存集成
            test_data = self._create_test_kline_data()
            
            # 通过时间框架同步器处理数据并缓存
            sync_result = await self.timeframe_sync.sync_timeframes(test_data, ["1m", "5m"])
            
            # 缓存同步结果
            cache_key = "sync_result_test"
            await self.cache_manager.set(cache_key, sync_result)
            
            # 验证缓存
            cached_result = await self.cache_manager.get(cache_key)
            
            success = cached_result == sync_result
            
            return ValidationResult(
                test_name="cache_integration",
                success=success,
                message=f"缓存集成测试{'通过' if success else '失败'}",
                details={
                    "cache_key": cache_key,
                    "data_consistency": success
                },
                execution_time_ms=(time.time() - start_time) * 1000,
                timestamp=datetime.now(timezone.utc)
            )
            
        except Exception as e:
            logger.error(f"缓存集成测试异常: {e}")
            return ValidationResult(
                test_name="cache_integration",
                success=False,
                message=f"测试异常: {str(e)}",
                details={"exception": str(e)},
                execution_time_ms=(time.time() - start_time) * 1000,
                timestamp=datetime.now(timezone.utc)
            )
    
    async def _test_error_handling_integration(self) -> ValidationResult:
        """测试错误处理集成"""
        start_time = time.time()
        
        try:
            # 测试各组件的错误处理
            error_scenarios = []
            
            # 1. 测试无效数据处理
            try:
                invalid_data = [{"invalid": "data"}]
                await self.timeframe_sync.sync_timeframes(invalid_data, ["1m"])
            except Exception as e:
                error_scenarios.append({"component": "timeframe_sync", "handled": True})
            
            # 2. 测试缓存错误处理
            try:
                await self.cache_manager.get("non_existent_key")
                error_scenarios.append({"component": "cache_manager", "handled": True})
            except Exception as e:
                error_scenarios.append({"component": "cache_manager", "handled": False})
            
            success = len(error_scenarios) > 0
            
            return ValidationResult(
                test_name="error_handling_integration",
                success=success,
                message=f"错误处理集成测试{'通过' if success else '失败'}",
                details={
                    "error_scenarios_tested": len(error_scenarios),
                    "scenarios": error_scenarios
                },
                execution_time_ms=(time.time() - start_time) * 1000,
                timestamp=datetime.now(timezone.utc)
            )
            
        except Exception as e:
            logger.error(f"错误处理集成测试异常: {e}")
            return ValidationResult(
                test_name="error_handling_integration",
                success=False,
                message=f"测试异常: {str(e)}",
                details={"exception": str(e)},
                execution_time_ms=(time.time() - start_time) * 1000,
                timestamp=datetime.now(timezone.utc)
            )
    
    async def _test_end_to_end_flow(self) -> ValidationResult:
        """测试端到端流程"""
        start_time = time.time()
        
        try:
            logger.info("开始端到端流程测试")
            
            # 模拟完整的数据处理流程
            # 1. 接收原始数据
            raw_data = self._create_test_kline_data()
            
            # 2. 数据质量检查
            quality_result = await self.data_quality_checker.check_data_quality(raw_data)
            
            # 3. 多时间框架同步
            sync_result = await self.timeframe_sync.sync_timeframes(
                raw_data, self.test_config.test_timeframes
            )
            
            # 4. 缓存处理结果
            cache_key = f"e2e_test_{int(time.time())}"
            await self.cache_manager.set(cache_key, sync_result)
            
            # 5. 生成信号（模拟）
            signal = {
                "messageId": str(uuid.uuid4()),
                "messageType": "signal",
                "timestamp": int(time.time() * 1000),
                "data": {
                    "strategyId": "integration_test_strategy",
                    "symbol": "BTCUSDT",
                    "signalType": "BUY",
                    "signalStrength": 0.8,
                    "timeFrame": "1h"
                }
            }
            
            # 6. 发送信号到Kafka
            self.kafka_client.publish_signal(signal)
            
            success = (quality_result is not None and 
                      sync_result is not None and 
                      len(sync_result) > 0)
            
            return ValidationResult(
                test_name="end_to_end_flow",
                success=success,
                message=f"端到端流程测试{'通过' if success else '失败'}",
                details={
                    "data_points_processed": len(raw_data),
                    "timeframes_synced": len(sync_result) if sync_result else 0,
                    "signal_sent": True,
                    "cache_key": cache_key
                },
                execution_time_ms=(time.time() - start_time) * 1000,
                timestamp=datetime.now(timezone.utc)
            )
            
        except Exception as e:
            logger.error(f"端到端流程测试异常: {e}")
            return ValidationResult(
                test_name="end_to_end_flow",
                success=False,
                message=f"测试异常: {str(e)}",
                details={"exception": str(e)},
                execution_time_ms=(time.time() - start_time) * 1000,
                timestamp=datetime.now(timezone.utc)
            )
    
    async def _test_integration_performance(self) -> Dict[str, Any]:
        """测试集成性能"""
        logger.info("开始集成性能测试")
        
        performance_results = {}
        
        # 测试数据处理性能
        start_time = time.time()
        test_data = self._create_test_kline_data()
        
        # 执行完整数据处理流程
        quality_result = await self.data_quality_checker.check_data_quality(test_data)
        sync_result = await self.timeframe_sync.sync_timeframes(test_data, ["1m", "5m", "15m"])
        
        processing_time = (time.time() - start_time) * 1000
        
        performance_results["data_processing"] = {
            "processing_time_ms": processing_time,
            "data_points": len(test_data),
            "throughput_points_per_sec": len(test_data) / (processing_time / 1000) if processing_time > 0 else 0
        }
        
        # 测试缓存性能
        cache_start = time.time()
        cache_key = "performance_test"
        await self.cache_manager.set(cache_key, sync_result)
        cached_data = await self.cache_manager.get(cache_key)
        cache_time = (time.time() - cache_start) * 1000
        
        performance_results["cache_performance"] = {
            "cache_operation_time_ms": cache_time,
            "data_size": len(str(sync_result)) if sync_result else 0
        }
        
        logger.info("集成性能测试完成")
        return performance_results
    
    async def _perform_health_checks(self) -> Dict[str, Any]:
        """执行系统健康检查"""
        logger.info("开始系统健康检查")
        
        health_status = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "components": {},
            "overall_health": "UNKNOWN"
        }
        
        # 检查各组件健康状态
        health_status["components"]["timeframe_sync"] = self._check_component_health(self.timeframe_sync)
        health_status["components"]["data_quality_checker"] = self._check_component_health(self.data_quality_checker)
        health_status["components"]["cache_manager"] = self._check_component_health(self.cache_manager)
        health_status["components"]["kafka_client"] = self._check_component_health(self.kafka_client)
        
        # 评估整体健康状态
        healthy_components = sum(1 for status in health_status["components"].values() if status == "HEALTHY")
        total_components = len(health_status["components"])
        
        if healthy_components == total_components:
            health_status["overall_health"] = "HEALTHY"
        elif healthy_components >= total_components * 0.75:
            health_status["overall_health"] = "DEGRADED"
        else:
            health_status["overall_health"] = "UNHEALTHY"
        
        logger.info(f"系统健康检查完成，状态: {health_status['overall_health']}")
        return health_status
    
    def _check_component_health(self, component: Any) -> str:
        """检查组件健康状态"""
        try:
            # 简单的健康检查（实际实现中可能更复杂）
            if hasattr(component, 'health_check'):
                return component.health_check()
            else:
                return "HEALTHY" if component is not None else "UNHEALTHY"
        except Exception as e:
            logger.warning(f"组件健康检查异常: {e}")
            return "UNHEALTHY"
    
    def _evaluate_overall_status(self, test_results: Dict[str, Any]) -> str:
        """评估整体测试状态"""
        try:
            # 统计成功的测试
            successful_tests = 0
            total_tests = 0
            
            # 检查组件测试
            if "component_tests" in test_results:
                for result in test_results["component_tests"].values():
                    if isinstance(result, ValidationResult):
                        total_tests += 1
                        if result.success:
                            successful_tests += 1
            
            # 检查集成测试
            if "integration_tests" in test_results:
                for result in test_results["integration_tests"].values():
                    if isinstance(result, ValidationResult):
                        total_tests += 1
                        if result.success:
                            successful_tests += 1
            
            # 检查端到端测试
            if "end_to_end_tests" in test_results:
                if isinstance(test_results["end_to_end_tests"], ValidationResult):
                    total_tests += 1
                    if test_results["end_to_end_tests"].success:
                        successful_tests += 1
            
            # 计算成功率
            success_rate = successful_tests / total_tests if total_tests > 0 else 0
            
            if success_rate >= 0.95:
                return "PASSED"
            elif success_rate >= 0.80:
                return "PASSED_WITH_WARNINGS"
            else:
                return "FAILED"
                
        except Exception as e:
            logger.error(f"评估整体状态异常: {e}")
            return "ERROR"


if __name__ == "__main__":
    # 测试代码
    import asyncio
    from .config import Config
    
    async def main():
        config = Config()
        test_suite = JavaIntegrationTestSuite(config)
        
        # 运行完整集成测试
        results = await test_suite.run_full_integration_test()
        
        logger.info("集成测试结果:")
        logger.info(json.dumps(results, indent=2, ensure_ascii=False, default=str))
    
    asyncio.run(main())