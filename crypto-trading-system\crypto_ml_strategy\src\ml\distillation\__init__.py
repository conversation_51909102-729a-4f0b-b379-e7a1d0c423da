"""
知识蒸馏优化模块

使用DeepSeek知识蒸馏技术压缩模型，优化推理速度和模型大小。

主要组件：
- DeepSeekDistiller: 核心知识蒸馏器，实现教师-学生模型训练
- ModelCompressor: 模型压缩器，提供量化、剪枝等压缩技术
- DistillationManager: 蒸馏管理器，协调整个蒸馏流程

使用示例：
```python
from src.model.distillation import DistillationManager

# 创建蒸馏管理器
manager = DistillationManager(config={
    'distillation': {
        'temperature': 4.0,
        'alpha': 0.7,
        'epochs': 100
    },
    'compression': {
        'enabled': True,
        'quantization': {'enabled': True},
        'pruning': {'enabled': True, 'sparsity': 0.3}
    }
})

# 运行完整蒸馏流程
results = manager.run_full_distillation(X_train, y_train)

# 使用蒸馏后的模型进行预测
predictions = manager.predict(X_test, model_type='compressed')
```
"""

from .deepseek_distiller import DeepSeekDistiller, TeacherModel, StudentModel
from .model_compressor import ModelCompressor
from .distillation_manager import DistillationManager

__all__ = [
    'DeepSeekDistiller', 
    'TeacherModel', 
    'StudentModel',
    'ModelCompressor', 
    'DistillationManager'
]

# 版本信息
__version__ = '1.0.0'

# 默认配置
DEFAULT_DISTILLATION_CONFIG = {
    'distillation': {
        'teacher_hidden_dims': [512, 256, 128, 64],
        'student_hidden_dims': [128, 64, 32],
        'temperature': 4.0,
        'alpha': 0.7,
        'beta': 0.3,
        'feature_loss_weight': 0.1,
        'learning_rate': 0.001,
        'batch_size': 64,
        'epochs': 100,
        'early_stopping_patience': 10
    },
    'compression': {
        'enabled': True,
        'quantization': {'enabled': True, 'bits': 8},
        'pruning': {'enabled': True, 'sparsity': 0.3},
        'weight_sharing': {'enabled': False, 'clusters': 16}
    },
    'validation': {
        'split_ratio': 0.2,
        'performance_threshold': 0.05
    },
    'output': {
        'save_teacher': True,
        'save_student': True,
        'save_compressed': True,
        'save_dir': 'models/distilled'
    }
}