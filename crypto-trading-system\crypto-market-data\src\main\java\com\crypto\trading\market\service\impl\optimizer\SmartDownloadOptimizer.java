package com.crypto.trading.market.service.impl.optimizer;

import com.crypto.trading.common.enums.KlineInterval;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

/**
 * 智能下载优化器
 * 负责根据数据请求的特性和系统运行状态，动态调整下载参数以优化性能
 */
@Component
public class SmartDownloadOptimizer {

    private static final Logger log = LoggerFactory.getLogger(SmartDownloadOptimizer.class);
    
    // 默认配置
    private static final int DEFAULT_MIN_CHUNK_SIZE_HOURS = 1;  // 最小块大小（小时）
    private static final int DEFAULT_MAX_CHUNK_SIZE_HOURS = 168; // 最大块大小（168小时 = 7天）
    private static final int DEFAULT_MIN_CONCURRENT = 1;         // 最小并发数
    private static final int DEFAULT_MAX_CONCURRENT = 10;        // 最大并发数
    private static final int DEFAULT_API_WEIGHT_LIMIT = 1200;    // 币安默认API权重限制（每分钟）
    private static final int DEFAULT_RECORDS_PER_REQUEST = 1000; // 每个请求最多返回的记录数
    private static final int DEFAULT_MAX_RETRIES = 3;            // 默认最大重试次数
    
    // 运行时状态跟踪
    private final Map<String, SymbolPerformanceStats> symbolStats = new HashMap<>();
    private final Map<KlineInterval, Integer> intervalWeights = new HashMap<>();
    private int successfulRequests = 0;
    private int failedRequests = 0;
    private int totalApiCallsMade = 0;
    private LocalDateTime lastRateLimitHit = null;
    
    public SmartDownloadOptimizer() {
        initializeIntervalWeights();
    }
    
    /**
     * 初始化不同K线间隔的API权重值
     */
    private void initializeIntervalWeights() {
        // 根据币安API文档设置不同间隔的权重
        intervalWeights.put(KlineInterval.MINUTE_1, 1);
        intervalWeights.put(KlineInterval.MINUTE_3, 1);
        intervalWeights.put(KlineInterval.MINUTE_5, 1);
        intervalWeights.put(KlineInterval.MINUTE_15, 1);
        intervalWeights.put(KlineInterval.MINUTE_30, 1);
        intervalWeights.put(KlineInterval.HOUR_1, 1);
        intervalWeights.put(KlineInterval.HOUR_2, 1);
        intervalWeights.put(KlineInterval.HOUR_4, 1);
        intervalWeights.put(KlineInterval.HOUR_6, 1);
        intervalWeights.put(KlineInterval.HOUR_8, 1);
        intervalWeights.put(KlineInterval.HOUR_12, 1);
        intervalWeights.put(KlineInterval.DAY_1, 1);
        intervalWeights.put(KlineInterval.DAY_3, 1);
        intervalWeights.put(KlineInterval.WEEK_1, 1);
        intervalWeights.put(KlineInterval.MONTH_1, 1);
    }
    
    /**
     * 计算每个分块的大小（小时）
     * 
     * @param symbol     交易对
     * @param interval   K线间隔
     * @param startTime  下载起始时间
     * @param endTime    下载截止时间
     * @return 推荐的分块大小（小时）
     */
    public int calculateOptimalChunkSize(String symbol, KlineInterval interval, LocalDateTime startTime, LocalDateTime endTime) {
        // 计算总时间跨度
        long totalHours = ChronoUnit.HOURS.between(startTime, endTime);
        if (totalHours <= DEFAULT_MIN_CHUNK_SIZE_HOURS) {
            return DEFAULT_MIN_CHUNK_SIZE_HOURS;
        }
        
        // 获取或创建交易对性能统计信息
        SymbolPerformanceStats stats = symbolStats.getOrDefault(symbol, new SymbolPerformanceStats(symbol));
        
        // 基于区间大小确定基础块大小
        int baseChunkSize = calculateBaseChunkSize(interval, totalHours);
        
        // 根据历史速率和错误率调整大小
        int adjustedSize = adjustChunkSizeByPerformance(baseChunkSize, stats);
        
        // 如果我们最近遇到了速率限制，降低块大小
        if (lastRateLimitHit != null && Duration.between(lastRateLimitHit, LocalDateTime.now()).toMinutes() < 10) {
            adjustedSize = Math.max(DEFAULT_MIN_CHUNK_SIZE_HOURS, adjustedSize / 2);
        }
        
        // 记录决策信息
        log.info("智能分块决策: 交易对={}, 间隔={}, 总时长={}小时, 基础块大小={}小时, 调整块大小={}小时",
                symbol, interval.getCode(), totalHours, baseChunkSize, adjustedSize);
        
        // 更新交易对统计信息
        symbolStats.put(symbol, stats);
        
        return adjustedSize;
    }
    
    /**
     * 计算基础块大小
     */
    private int calculateBaseChunkSize(KlineInterval interval, long totalHours) {
        // 针对不同的时间间隔和总时长采用不同策略
        if (totalHours > 24 * 30 * 3) {  // 超过3个月的数据
            // 对于大时间跨度，使用较大的块
            return 24 * 3;  // 3天一块
        } else if (totalHours > 24 * 7) {  // 超过1周的数据
            return 24;      // 1天一块
        } else if (totalHours > 24) {     // 超过1天的数据
            return 6;       // 6小时一块
        } else {
            // 对于小时间跨度，使用较小的块
            return Math.max(1, (int) (totalHours / 4));  // 将总时长分为最多4块
        }
    }
    
    /**
     * 根据历史性能调整块大小
     */
    private int adjustChunkSizeByPerformance(int baseSize, SymbolPerformanceStats stats) {
        if (stats.getTotalRequests() < 10) {
            // 数据不足，使用默认值
            return baseSize;
        }
        
        double errorRate = stats.getErrorRate();
        double avgResponseTime = stats.getAvgResponseTime();
        
        // 如果错误率高，减小分块大小
        if (errorRate > 0.2) {  // 错误率超过20%
            baseSize = Math.max(DEFAULT_MIN_CHUNK_SIZE_HOURS, baseSize / 2);
        }
        
        // 如果平均响应时间过长，减小分块大小
        if (avgResponseTime > 5000) {  // 平均响应时间超过5秒
            baseSize = Math.max(DEFAULT_MIN_CHUNK_SIZE_HOURS, baseSize * 2 / 3);
        }
        
        // 确保块大小不超出限制
        return Math.min(DEFAULT_MAX_CHUNK_SIZE_HOURS, Math.max(DEFAULT_MIN_CHUNK_SIZE_HOURS, baseSize));
    }
    
    /**
     * 计算最优并发请求数
     * 
     * @param symbol     交易对
     * @param interval   K线间隔
     * @return 推荐的并发请求数
     */
    public int calculateOptimalConcurrentRequests(String symbol, KlineInterval interval) {
        // 基础并发数
        int baseConcurrent = 3;
        
        // 获取交易对性能统计
        SymbolPerformanceStats stats = symbolStats.getOrDefault(symbol, new SymbolPerformanceStats(symbol));
        
        // 如果最近命中了速率限制，降低并发数
        if (lastRateLimitHit != null && Duration.between(lastRateLimitHit, LocalDateTime.now()).toMinutes() < 10) {
            baseConcurrent = Math.max(1, baseConcurrent / 2);
        }
        
        // 如果错误率高，降低并发数
        if (stats.getTotalRequests() > 10 && stats.getErrorRate() > 0.15) {
            baseConcurrent = Math.max(1, baseConcurrent - 1);
        }
        
        // 根据API总调用量动态调整
        if (totalApiCallsMade > 10000) {
            // 系统运行已久，根据整体成功率调整
            double overallSuccessRate = (double) successfulRequests / (successfulRequests + failedRequests);
            if (overallSuccessRate > 0.95) {
                // 成功率高，可以尝试提高并发度
                baseConcurrent = Math.min(DEFAULT_MAX_CONCURRENT, baseConcurrent + 1);
            }
        }
        
        // 添加一点随机性，避免所有请求同时触发速率限制
        int randomAdjustment = ThreadLocalRandom.current().nextInt(-1, 2);
        int finalConcurrent = Math.min(DEFAULT_MAX_CONCURRENT, 
                                       Math.max(DEFAULT_MIN_CONCURRENT, baseConcurrent + randomAdjustment));
        
        log.info("智能并发决策: 交易对={}, 间隔={}, 基础并发数={}, 最终并发数={}", 
                symbol, interval.getCode(), baseConcurrent, finalConcurrent);
        
        return finalConcurrent;
    }
    
    /**
     * 计算最佳重试等待时间（毫秒）
     * 使用指数退避策略
     * 
     * @param retryCount 当前重试次数
     * @return 等待毫秒数
     */
    public long calculateRetryWaitTime(int retryCount) {
        // 实现指数退避策略：Base * 2^retryCount + random
        long baseWait = 1000;  // 基础等待时间1秒
        long exponentialPart = (long) Math.pow(2, retryCount);
        long randomPart = ThreadLocalRandom.current().nextLong(1000);  // 添加最多1秒的随机时间
        
        long waitTime = baseWait * exponentialPart + randomPart;
        // 限制最大等待时间为2分钟
        return Math.min(waitTime, 120000);
    }
    
    /**
     * 报告请求成功
     * @param symbol 交易对
     * @param responseTimeMs 响应时间(毫秒)
     * @param dataPointsReturned 返回的数据点数量
     */
    public void reportSuccess(String symbol, long responseTimeMs, int dataPointsReturned) {
        SymbolPerformanceStats stats = symbolStats.getOrDefault(symbol, new SymbolPerformanceStats(symbol));
        stats.recordSuccess(responseTimeMs, dataPointsReturned);
        symbolStats.put(symbol, stats);
        
        successfulRequests++;
        totalApiCallsMade++;
    }
    
    /**
     * 报告请求失败
     * @param symbol 交易对
     * @param errorCode 错误代码 (429表示速率限制)
     * @param errorMsg 错误信息
     */
    public void reportFailure(String symbol, int errorCode, String errorMsg) {
        SymbolPerformanceStats stats = symbolStats.getOrDefault(symbol, new SymbolPerformanceStats(symbol));
        stats.recordFailure(errorCode, errorMsg);
        symbolStats.put(symbol, stats);
        
        failedRequests++;
        totalApiCallsMade++;
        
        // 记录速率限制触发时间
        if (errorCode == 429) {
            lastRateLimitHit = LocalDateTime.now();
            log.warn("触发API速率限制: 交易对={}, 消息={}", symbol, errorMsg);
        }
    }
    
    /**
     * 获取交易对的统计信息
     * @param symbol 交易对
     * @return 性能统计信息
     */
    public SymbolPerformanceStats getSymbolStats(String symbol) {
        return symbolStats.getOrDefault(symbol, new SymbolPerformanceStats(symbol));
    }
    
    /**
     * 计算给定间隔和数据量的API权重消耗
     * @param interval K线间隔
     * @param dataPoints 数据点数量
     * @return 预计API权重消耗
     */
    public int calculateApiWeight(KlineInterval interval, int dataPoints) {
        int baseWeight = intervalWeights.getOrDefault(interval, 1);
        // 每1000个数据点算一个基本权重
        return baseWeight * (1 + dataPoints / DEFAULT_RECORDS_PER_REQUEST);
    }
    
    /**
     * 内部类：交易对性能统计
     */
    public static class SymbolPerformanceStats {
        private final String symbol;
        private int successfulRequests;
        private int failedRequests;
        private long totalResponseTime;
        private int totalDataPointsReceived;
        private final Map<Integer, Integer> errorCounts = new HashMap<>();
        private LocalDateTime lastRequest;
        private LocalDateTime lastSuccess;
        private LocalDateTime lastFailure;
        
        public SymbolPerformanceStats(String symbol) {
            this.symbol = symbol;
            this.successfulRequests = 0;
            this.failedRequests = 0;
            this.totalResponseTime = 0;
            this.totalDataPointsReceived = 0;
        }
        
        public void recordSuccess(long responseTimeMs, int dataPointsReturned) {
            successfulRequests++;
            totalResponseTime += responseTimeMs;
            totalDataPointsReceived += dataPointsReturned;
            lastRequest = LocalDateTime.now();
            lastSuccess = LocalDateTime.now();
        }
        
        public void recordFailure(int errorCode, String errorMsg) {
            failedRequests++;
            errorCounts.put(errorCode, errorCounts.getOrDefault(errorCode, 0) + 1);
            lastRequest = LocalDateTime.now();
            lastFailure = LocalDateTime.now();
        }
        
        public int getTotalRequests() {
            return successfulRequests + failedRequests;
        }
        
        public double getErrorRate() {
            int total = getTotalRequests();
            return total > 0 ? (double) failedRequests / total : 0;
        }
        
        public double getAvgResponseTime() {
            return successfulRequests > 0 ? (double) totalResponseTime / successfulRequests : 0;
        }
        
        public double getAvgDataPointsPerRequest() {
            return successfulRequests > 0 ? (double) totalDataPointsReceived / successfulRequests : 0;
        }
        
        // Getters
        public String getSymbol() { return symbol; }
        public int getSuccessfulRequests() { return successfulRequests; }
        public int getFailedRequests() { return failedRequests; }
        public LocalDateTime getLastRequest() { return lastRequest; }
        public LocalDateTime getLastSuccess() { return lastSuccess; }
        public LocalDateTime getLastFailure() { return lastFailure; }
        public Map<Integer, Integer> getErrorCounts() { return Collections.unmodifiableMap(errorCounts); }
        
        @Override
        public String toString() {
            return String.format("SymbolStats[%s]: 成功=%d, 失败=%d, 错误率=%.2f%%, 平均响应时间=%.2fms, 平均数据点=%.2f",
                    symbol, successfulRequests, failedRequests, getErrorRate() * 100,
                    getAvgResponseTime(), getAvgDataPointsPerRequest());
        }
    }
}