# 项目结构设计

## 1. 项目总体结构

虚拟货币量化交易系统采用多模块Maven项目结构和Python策略模块，主要分为以下模块：

```
crypto-trading-system/
├── crypto-common/               # 公共模块
├── crypto-market-data/          # 市场数据模块
├── crypto-ml-strategy/          # Python机器学习策略模块
├── crypto-trade/                # 交易执行模块
├── crypto-account/              # 账户管理模块
├── crypto-risk/                 # 风险控制模块
├── crypto-monitor/              # 系统监控模块
├── crypto-bootstrap/            # 应用启动模块
├── crypto-sdk/                  # SDK集成模块
└── pom.xml                      # 父POM文件
```

## 2. 模块详细说明

### 2.1 crypto-common（公共模块）

存放公共组件、工具类、常量、异常、配置等。

```
crypto-common/
├── src/main/java/com/trading/common/
│   ├── config/                  # 配置类
│   │   ├── AppConfig.java
│   │   ├── BinanceConfig.java
│   │   ├── DatabaseConfig.java
│   │   └── KafkaConfig.java
│   ├── constant/                # 常量定义
│   │   ├── SystemConstants.java
│   │   ├── TradeConstants.java
│   │   └── ErrorCodes.java
│   ├── dto/                     # 数据传输对象
│   │   ├── ResponseResult.java
│   │   ├── PageResult.java
│   │   └── market/
│   ├── enums/                   # 枚举定义
│   │   ├── OrderStatus.java
│   │   ├── OrderSide.java
│   │   └── PositionSide.java
│   ├── exception/               # 异常类
│   │   ├── BaseException.java
│   │   ├── BusinessException.java
│   │   ├── SystemException.java
│   │   ├── TradeException.java
│   │   └── ApiException.java
│   └── utils/                   # 工具类
│       ├── DateTimeUtils.java
│       ├── NumberUtils.java
│       ├── JsonUtils.java
│       ├── HttpUtils.java
│       ├── ValidationUtils.java
│       └── EncryptUtils.java
├── src/main/resources/
│   ├── application-common.yml
│   └── logback-common.xml
└── pom.xml
```

### 2.2 crypto-market-data（市场数据模块）

负责从交易所获取市场数据，包括K线、深度、交易明细等。

```
crypto-market-data/
├── src/main/java/com/trading/market/
│   ├── config/                  # 模块配置
│   │   ├── WebSocketConfig.java
│   │   └── KafkaProducerConfig.java
│   ├── listener/                # WebSocket监听器
│   │   ├── KlineListener.java
│   │   ├── DepthListener.java
│   │   └── TradeListener.java
│   ├── processor/               # 数据处理器
│   │   ├── KlineProcessor.java
│   │   ├── DepthProcessor.java
│   │   └── TradeProcessor.java
│   ├── service/                 # 服务接口
│   │   ├── MarketDataService.java
│   │   └── impl/
│   │       └── MarketDataServiceImpl.java
│   ├── producer/                # Kafka生产者
│   │   ├── MarketDataProducer.java
│   │   └── impl/
│   │       └── MarketDataProducerImpl.java
│   ├── manager/                 # 管理类
│   │   ├── WebSocketManager.java
│   │   └── ReconnectionManager.java
│   ├── entity/                  # 实体类
│   │   ├── KlineData.java
│   │   ├── DepthData.java
│   │   └── TradeData.java
│   └── repository/              # 数据存储
│       ├── InfluxDBRepository.java
│       └── impl/
│           └── InfluxDBRepositoryImpl.java
├── src/main/resources/
│   ├── application-market.yml
│   └── logback-market.xml
└── pom.xml
```

### 2.3 crypto-ml-strategy（Python机器学习策略模块）

负责使用统一机器学习策略实现交易信号生成，整合多种策略特征，并通过Kafka与Java系统进行通信。该模块替代了原有的Java策略模块，利用真实数据训练、在线学习和DeepSeek蒸馏优化实现高效的交易决策。

```
crypto-ml-strategy/
├── config/                      # 配置文件
│   ├── config.ini               # 主配置文件，包含Kafka、日志等配置
│   ├── logging.conf             # 日志配置
│   └── strategy_params.json     # 统一策略参数配置
├── models/                      # 预训练模型
│   ├── unified_ml_model.pkl     # 统一机器学习策略模型
│   ├── distilled_model.pkl      # DeepSeek蒸馏优化后的模型
│   └── feature_scaler.pkl       # 特征标准化器
├── src/
│   ├── main.py                  # 主入口文件，负责启动策略模块
│   ├── config.py                # 配置管理，负责加载和解析配置
│   ├── logger.py                # 日志管理，负责配置日志记录器
│   ├── kafka_client.py          # Kafka通信，包括消费者和生产者
│   ├── data_processor.py        # 数据处理，负责预处理市场数据
│   ├── strategy/                # 策略实现
│   │   ├── __init__.py          # 策略工厂，用于创建策略实例
│   │   ├── base.py              # 基础策略类，定义策略接口
│   │   ├── unified_ml.py        # 统一机器学习策略，整合多种特征
│   │   ├── risk_evaluator.py    # 风险评估器，集成在统一策略中
│   │   └── signal_generator.py  # 信号生成器，基于模型预测产生交易信号
│   ├── utils/                   # 工具函数
│   │   ├── __init__.py
│   │   ├── data_utils.py        # 数据处理工具
│   │   ├── math_utils.py        # 数学计算工具
│   │   └── time_utils.py        # 时间处理工具
│   ├── indicators/              # 技术指标
│   │   ├── __init__.py
│   │   ├── basic.py             # 基础技术指标(MA, RSI等)
│   │   ├── advanced.py          # 高级技术指标(Ichimoku等)
│   │   ├── lppl_features.py     # LPPL泡沫检测特征
│   │   ├── hematread_features.py # Hematread策略特征
│   │   ├── bull_market_support.py # Bull Market Support Band特征
│   │   └── custom.py            # 自定义技术指标
│   ├── data/                    # 数据访问
│   │   ├── __init__.py
│   │   ├── influxdb_client.py   # InfluxDB客户端，用于访问时序数据
│   │   ├── mysql_client.py      # MySQL客户端，用于访问关系数据
│   │   └── real_data_loader.py  # 真实数据加载器，用于获取训练数据
│   ├── model/                   # 机器学习模型
│   │   ├── __init__.py
│   │   ├── feature_engineering.py # 特征工程，构建模型输入特征
│   │   ├── model_trainer.py     # 模型训练器，负责批量训练
│   │   ├── online_learner.py    # 在线学习器，负责模型增量更新
│   │   ├── distillation/        # 知识蒸馏优化
│   │   │   ├── __init__.py
│   │   │   ├── deepseek_distiller.py # DeepSeek知识蒸馏实现
│   │   │   └── model_compressor.py # 模型压缩优化
│   │   ├── versioning.py        # 模型版本管理
│   │   └── prediction.py        # 预测引擎，负责模型推理
│   └── evaluation/              # 模型评估
│       ├── __init__.py
│       ├── metrics.py           # 模型评估指标
│       ├── backtesting.py       # 回测框架
│       └── ab_testing.py        # A/B测试框架
├── tests/                       # 单元测试
│   ├── __init__.py
│   ├── test_data_processor.py   # 测试数据处理器
│   ├── test_indicators.py       # 测试技术指标
│   ├── test_unified_strategy.py # 测试统一策略
│   ├── test_online_learning.py  # 测试在线学习
│   └── test_distillation.py     # 测试DeepSeek蒸馏
├── notebooks/                   # Jupyter笔记本
│   ├── unified_strategy_development.ipynb # 统一策略开发笔记本
│   ├── feature_importance_analysis.ipynb  # 特征重要性分析
│   ├── distillation_optimization.ipynb    # 蒸馏优化分析
│   └── real_data_training.ipynb  # 真实数据训练分析
├── requirements.txt             # Python依赖
├── setup.py                     # 安装脚本
└── README.md                    # 模块说明
```

#### 2.3.1 Python模块与Java系统集成

Python机器学习策略模块通过以下方式与Java系统集成：

1. **通过Kafka消息队列通信**：
   - 消费市场数据：订阅`kline.data`、`depth.data`和`trade.data`主题
   - 发布策略信号：将生成的信号发布到`strategy.signal`主题

2. **统一的消息格式**：
   - 所有消息采用JSON格式
   - 市场数据和策略信号的格式在Java和Python端保持一致
   - 包含唯一消息ID、时间戳、消息类型和数据负载

3. **配置同步**：
   - 从配置文件加载初始配置
   - 统一策略参数可通过MySQL数据库动态更新
   - 支持运行时参数调整

4. **数据存储访问**：
   - 访问InfluxDB获取历史市场数据，用于特征计算、模型训练和在线学习
   - 访问MySQL数据库获取配置和元数据
   - 将模型性能指标、版本信息和预测结果写入数据库

5. **生命周期管理**：
   - 支持优雅启动和关闭
   - 处理系统信号(SIGTERM, SIGINT)
   - 支持健康检查和状态报告

#### 2.3.2 主要组件说明

1. **统一机器学习策略**：
   - 整合LPPL泡沫检测、hematread和bull market support band等多种策略特征
   - 集成风险评估功能，实现自适应风险控制
   - 采用集成学习方法融合多种机器学习模型
   - 可根据市场条件自动调整特征权重

2. **数据处理流程**：
   - Kafka消费者接收市场数据
   - 数据处理器对数据进行清洗和规范化
   - 特征工程组件计算多种策略特征
   - 统一模型推理引擎生成预测结果
   - 风险评估器评估当前市场风险
   - 信号生成器产生最终交易信号
   - Kafka生产者发布信号

3. **真实数据训练**：
   - 支持从InfluxDB加载真实历史市场数据
   - 实现数据质量检查和异常检测
   - 支持多种采样和数据增强方法
   - 提供特征重要性分析和可视化
   - 使用交叉验证评估模型性能

4. **在线学习机制**：
   - 实现增量学习，不断从新数据中学习
   - 支持模型的定期更新和实时更新
   - 实现概念漂移检测，自动适应市场变化
   - 维护多个模型版本，支持快速回滚
   - 提供模型性能监控和报警

5. **DeepSeek蒸馏优化**：
   - 利用DeepSeek知识蒸馏技术压缩模型
   - 保持模型性能的同时减少计算复杂度
   - 优化训练时间和推理延迟
   - 支持模型量化和结构优化
   - 提供蒸馏前后性能对比分析

6. **性能优化**：
   - 使用NumPy和Pandas进行向量化计算
   - 使用Numba JIT编译加速计算密集型函数
   - 实现数据缓存减少重复计算
   - 支持增量计算提高效率
   - 批量处理Kafka消息减少网络开销

#### 2.3.3 Python依赖

主要依赖项列表：

```
# 基础依赖
numpy==1.24.3
pandas==2.0.3
scikit-learn==1.3.0
scipy==1.11.1
matplotlib==3.7.2
seaborn==0.12.2

# 深度学习
torch==2.0.1
tensorflow==2.13.0
deepseek-core==1.2.0  # DeepSeek蒸馏核心库

# 数据处理和分析
statsmodels==0.14.0
ta-lib==0.4.26
prophet==1.1.4

# 消息队列
kafka-python==2.0.2
confluent-kafka==2.1.1

# 数据库连接
influxdb-client==1.36.1
pymysql==1.1.0
sqlalchemy==2.0.19

# 性能优化
numba==0.57.1
dask==2023.6.0

# 其他工具
tqdm==4.65.0
python-dotenv==1.0.0
loguru==0.7.0
```

#### 2.3.4 部署方式

Python机器学习策略模块支持以下部署方式：

1. **本地部署**：
   - 直接在本地环境运行
   - 使用`venv`或`conda`管理环境

2. **容器化部署**：
   - 使用Docker容器部署
   - 支持Docker Compose与其他服务集成

3. **生产环境最佳实践**：
   - 使用Supervisor或Systemd管理进程
   - 支持多实例部署提高可用性
   - 支持模型热更新不中断服务
   - 支持GPU加速（如可用）
   - 实现模型训练和推理分离，优化资源使用

### 2.4 crypto-trade（交易执行模块）

负责接收策略信号，执行交易操作，跟踪订单状态。

```
crypto-trade/
├── src/main/java/com/trading/trade/
│   ├── config/                  # 模块配置
│   │   ├── TradeConfig.java
│   │   ├── KafkaConsumerConfig.java
│   │   └── KafkaProducerConfig.java
│   ├── consumer/                # Kafka消费者
│   │   ├── SignalConsumer.java
│   │   └── impl/
│   │       └── SignalConsumerImpl.java
│   ├── service/                 # 服务接口
│   │   ├── OrderService.java
│   │   ├── TradeExecutionService.java
│   │   ├── OrderTrackingService.java
│   │   └── impl/
│   │       ├── OrderServiceImpl.java
│   │       ├── TradeExecutionServiceImpl.java
│   │       └── OrderTrackingServiceImpl.java
│   ├── executor/                # 交易执行器
│   │   ├── TradeExecutor.java
│   │   ├── LimitOrderExecutor.java
│   │   └── MarketOrderExecutor.java
│   ├── producer/                # Kafka生产者
│   │   ├── OrderUpdateProducer.java
│   │   └── impl/
│   │       └── OrderUpdateProducerImpl.java
│   ├── tracker/                 # 订单追踪
│   │   ├── OrderTracker.java
│   │   └── impl/
│   │       └── OrderTrackerImpl.java
│   ├── handler/                 # WebSocket处理器
│   │   ├── UserDataStreamHandler.java
│   │   └── impl/
│   │       └── UserDataStreamHandlerImpl.java
│   ├── validator/               # 验证器
│   │   ├── OrderValidator.java
│   │   └── impl/
│   │       └── OrderValidatorImpl.java
│   ├── limiter/                 # 限流器
│   │   ├── RateLimiter.java
│   │   └── impl/
│   │       └── RateLimiterImpl.java
│   ├── entity/                  # 实体类
│   │   ├── Order.java
│   │   ├── OrderUpdate.java
│   │   └── Trade.java
│   ├── mapper/                  # MyBatis映射
│   │   ├── OrderMapper.java
│   │   ├── OrderUpdateMapper.java
│   │   └── TradeMapper.java
│   └── repository/              # 数据访问
│       ├── OrderRepository.java
│       ├── OrderUpdateRepository.java
│       ├── TradeRepository.java
│       └── impl/
│           ├── OrderRepositoryImpl.java
│           ├── OrderUpdateRepositoryImpl.java
│           └── TradeRepositoryImpl.java
├── src/main/resources/
│   ├── application-trade.yml
│   ├── mapper/
│   │   ├── OrderMapper.xml
│   │   ├── OrderUpdateMapper.xml
│   │   └── TradeMapper.xml
│   └── logback-trade.xml
└── pom.xml
```

### 2.5 crypto-account（账户管理模块）

负责管理账户资金、持仓情况。

```
crypto-account/
├── src/main/java/com/trading/account/
│   ├── config/                  # 模块配置
│   │   ├── AccountConfig.java
│   │   └── KafkaConsumerConfig.java
│   ├── consumer/                # Kafka消费者
│   │   ├── OrderUpdateConsumer.java
│   │   └── impl/
│   │       └── OrderUpdateConsumerImpl.java
│   ├── service/                 # 服务接口
│   │   ├── AccountService.java
│   │   ├── PositionService.java
│   │   ├── PnLCalculationService.java
│   │   └── impl/
│   │       ├── AccountServiceImpl.java
│   │       ├── PositionServiceImpl.java
│   │       └── PnLCalculationServiceImpl.java
│   ├── manager/                 # 管理类
│   │   ├── BalanceManager.java
│   │   ├── PositionManager.java
│   │   └── impl/
│   │       ├── BalanceManagerImpl.java
│   │       └── PositionManagerImpl.java
│   ├── calculator/              # 计算器
│   │   ├── PnLCalculator.java
│   │   └── impl/
│   │       └── PnLCalculatorImpl.java
│   ├── entity/                  # 实体类
│   │   ├── AccountSnapshot.java
│   │   └── Position.java
│   ├── mapper/                  # MyBatis映射
│   │   ├── AccountSnapshotMapper.java
│   │   └── PositionMapper.java
│   └── repository/              # 数据访问
│       ├── AccountSnapshotRepository.java
│       ├── PositionRepository.java
│       └── impl/
│           ├── AccountSnapshotRepositoryImpl.java
│           └── PositionRepositoryImpl.java
├── src/main/resources/
│   ├── application-account.yml
│   ├── mapper/
│   │   ├── AccountSnapshotMapper.xml
│   │   └── PositionMapper.xml
│   └── logback-account.xml
└── pom.xml
```

### 2.6 crypto-risk（风险控制模块）

负责风险控制和监控。

```
crypto-risk/
├── src/main/java/com/trading/risk/
│   ├── config/                  # 模块配置
│   │   └── RiskConfig.java
│   ├── service/                 # 服务接口
│   │   ├── RiskService.java
│   │   ├── AlertService.java
│   │   └── impl/
│   │       ├── RiskServiceImpl.java
│   │       └── AlertServiceImpl.java
│   ├── limiter/                 # 限制器
│   │   ├── OrderLimiter.java
│   │   ├── PositionLimiter.java
│   │   ├── LossLimiter.java
│   │   └── impl/
│   │       ├── OrderLimiterImpl.java
│   │       ├── PositionLimiterImpl.java
│   │       └── LossLimiterImpl.java
│   ├── monitor/                 # 监控器
│   │   ├── RiskMetricsMonitor.java
│   │   └── impl/
│   │       └── RiskMetricsMonitorImpl.java
│   ├── trigger/                 # 触发器
│   │   ├── AlertTrigger.java
│   │   └── impl/
│   │       └── AlertTriggerImpl.java
│   ├── generator/               # 报告生成器
│   │   ├── RiskReportGenerator.java
│   │   └── impl/
│   │       └── RiskReportGeneratorImpl.java
│   ├── entity/                  # 实体类
│   │   ├── RiskRule.java
│   │   ├── Alert.java
│   │   └── RiskReport.java
│   ├── mapper/                  # MyBatis映射
│   │   ├── RiskRuleMapper.java
│   │   └── AlertMapper.java
│   └── repository/              # 数据访问
│       ├── RiskRuleRepository.java
│       ├── AlertRepository.java
│       └── impl/
│           ├── RiskRuleRepositoryImpl.java
│           └── AlertRepositoryImpl.java
├── src/main/resources/
│   ├── application-risk.yml
│   ├── mapper/
│   │   ├── RiskRuleMapper.xml
│   │   └── AlertMapper.xml
│   └── logback-risk.xml
└── pom.xml
```

### 2.7 crypto-monitor（系统监控模块）

负责系统的监控和健康检查。

```
crypto-monitor/
├── src/main/java/com/trading/monitor/
│   ├── config/                  # 模块配置
│   │   └── MonitorConfig.java
│   ├── service/                 # 服务接口
│   │   ├── MonitorService.java
│   │   ├── HealthCheckService.java
│   │   └── impl/
│   │       ├── MonitorServiceImpl.java
│   │       └── HealthCheckServiceImpl.java
│   ├── monitor/                 # 监控器
│   │   ├── JVMMonitor.java
│   │   ├── DataFlowMonitor.java
│   │   ├── LatencyMonitor.java
│   │   └── impl/
│   │       ├── JVMMonitorImpl.java
│   │       ├── DataFlowMonitorImpl.java
│   │       └── LatencyMonitorImpl.java
│   ├── checker/                 # 检查器
│   │   ├── ServiceHealthChecker.java
│   │   ├── ConnectionHealthChecker.java
│   │   └── impl/
│   │       ├── ServiceHealthCheckerImpl.java
│   │       └── ConnectionHealthCheckerImpl.java
│   ├── recovery/                # 恢复机制
│   │   ├── AutoRecoveryMechanism.java
│   │   └── impl/
│   │       └── AutoRecoveryMechanismImpl.java
│   ├── metrics/                 # 指标收集
│   │   ├── MetricsCollector.java
│   │   └── impl/
│   │       └── MetricsCollectorImpl.java
│   └── repository/              # 数据访问
│       ├── MetricsRepository.java
│       └── impl/
│           └── MetricsRepositoryImpl.java
├── src/main/resources/
│   ├── application-monitor.yml
│   └── logback-monitor.xml
└── pom.xml
```

### 2.8 crypto-bootstrap（应用启动模块）

负责系统的启动和生命周期管理。

```
crypto-bootstrap/
├── src/main/java/com/trading/bootstrap/
│   ├── CryptoTradingApplication.java    # 应用启动类
│   ├── config/                          # 配置类
│   │   ├── ApplicationConfig.java
│   │   └── DependencyInjectionConfig.java
│   ├── initializer/                     # 初始化器
│   │   ├── ModuleInitializer.java
│   │   └── impl/
│   │       ├── MarketDataInitializer.java
│   │       ├── StrategyInitializer.java
│   │       ├── TradeInitializer.java
│   │       └── AccountInitializer.java
│   └── lifecycle/                       # 生命周期管理
│       ├── StartupOrderManager.java
│       ├── ShutdownHook.java
│       └── ResourceCleaner.java
├── src/main/resources/
│   ├── application.yml                  # 主配置文件
│   ├── application-dev.yml              # 开发环境配置
│   ├── application-prod.yml             # 生产环境配置
│   ├── logback-spring.xml               # 日志配置
│   └── banner.txt                       # 启动横幅
├── src/test/java/com/trading/bootstrap/
│   └── CryptoTradingApplicationTests.java
└── pom.xml
```

### 2.9 crypto-sdk（SDK集成模块）

封装对币安SDK的调用，提供统一的接口。

```
crypto-sdk/
├── src/main/java/com/trading/sdk/
│   ├── config/                  # 模块配置
│   │   └── BinanceApiConfig.java
│   ├── client/                  # API客户端
│   │   ├── BinanceApiClient.java
│   │   └── impl/
│   │       └── BinanceApiClientImpl.java
│   ├── service/                 # 服务接口
│   │   ├── MarketService.java
│   │   ├── TradeService.java
│   │   ├── AccountService.java
│   │   └── impl/
│   │       ├── MarketServiceImpl.java
│   │       ├── TradeServiceImpl.java
│   │       └── AccountServiceImpl.java
│   ├── websocket/               # WebSocket客户端
│   │   ├── BinanceWebSocketClient.java
│   │   └── impl/
│   │       └── BinanceWebSocketClientImpl.java
│   ├── handler/                 # 响应处理器
│   │   ├── ResponseHandler.java
│   │   └── impl/
│   │       └── ResponseHandlerImpl.java
│   ├── converter/               # 数据转换器
│   │   ├── ResponseConverter.java
│   │   └── impl/
│   │       └── ResponseConverterImpl.java
│   └── limiter/                 # API限流器
│       ├── ApiRateLimiter.java
│       └── impl/
│           └── ApiRateLimiterImpl.java
├── src/main/resources/
│   └── application-sdk.yml
└── pom.xml
```

## 3. 父POM文件

父POM文件用于管理所有子模块的依赖版本和构建配置。

```xml
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.trading</groupId>
    <artifactId>crypto-trading-system</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>Crypto Trading System</name>
    <description>A high-performance cryptocurrency quantitative trading system</description>

    <modules>
        <module>crypto-common</module>
        <module>crypto-market-data</module>
        <module>crypto-ml-strategy</module>
        <module>crypto-trade</module>
        <module>crypto-account</module>
        <module>crypto-risk</module>
        <module>crypto-monitor</module>
        <module>crypto-bootstrap</module>
        <module>crypto-sdk</module>
    </modules>

    <properties>
        <java.version>21</java.version>
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <maven.compiler.target>${java.version}</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <spring-boot.version>3.2.0</spring-boot.version>
        <mybatis-plus.version>3.5.11</mybatis-plus.version>
        <mybatis-plus-jsqlparser.version>4.6.2</mybatis-plus-jsqlparser.version>
        <kafka.version>3.5.1</kafka.version>
        <influxdb.version>6.10.0</influxdb.version>
        <binance-futures-connector.version>3.0.5</binance-futures-connector.version>
        <jackson.version>2.15.3</jackson.version>
        <logback.version>1.4.11</logback.version>
        <slf4j.version>2.0.9</slf4j.version>
        <junit-jupiter.version>5.10.1</junit-jupiter.version>
        <mockito.version>5.8.0</mockito.version>
        <jasypt.version>3.0.5</jasypt.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- Spring Boot BOM -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- MyBatis-Plus -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            
            <!-- MyBatis-Plus JSqlParser -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-jsqlparser</artifactId>
                <version>${mybatis-plus-jsqlparser.version}</version>
            </dependency>

            <!-- Binance Futures Connector -->
            <dependency>
                <groupId>io.github.binance</groupId>
                <artifactId>binance-futures-connector-java</artifactId>
                <version>${binance-futures-connector.version}</version>
            </dependency>

            <!-- Kafka Client -->
            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>kafka-clients</artifactId>
                <version>${kafka.version}</version>
            </dependency>

            <!-- InfluxDB Client -->
            <dependency>
                <groupId>com.influxdb</groupId>
                <artifactId>influxdb-client-java</artifactId>
                <version>${influxdb.version}</version>
            </dependency>

            <!-- Logging -->
            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-classic</artifactId>
                <version>${logback.version}</version>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-api</artifactId>
                <version>${slf4j.version}</version>
            </dependency>

            <!-- Jackson -->
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>${jackson.version}</version>
            </dependency>

            <!-- Configuration Encryption -->
            <dependency>
                <groupId>com.github.ulisesbocchio</groupId>
                <artifactId>jasypt-spring-boot-starter</artifactId>
                <version>${jasypt.version}</version>
            </dependency>

            <!-- Testing -->
            <dependency>
                <groupId>org.junit.jupiter</groupId>
                <artifactId>junit-jupiter-api</artifactId>
                <version>${junit-jupiter.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-core</artifactId>
                <version>${mockito.version}</version>
                <scope>test</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring-boot.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.11.0</version>
                    <configuration>
                        <source>${java.version}</source>
                        <target>${java.version}</target>
                        <encoding>${project.build.sourceEncoding}</encoding>
                        <compilerArgs>
                            <arg>--enable-preview</arg>
                        </compilerArgs>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>3.2.2</version>
                    <configuration>
                        <argLine>--enable-preview</argLine>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>
</project>
```

## 4. 启动类设计

系统的主启动类位于crypto-bootstrap模块中：

```java
package com.trading.bootstrap;

import com.trading.bootstrap.initializer.ModuleInitializer;
import com.trading.bootstrap.lifecycle.ShutdownHook;
import com.trading.bootstrap.lifecycle.StartupOrderManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * 虚拟货币量化交易系统主启动类
 * 
 * <AUTHOR>
 */
@SpringBootApplication
@EnableAsync
@EnableTransactionManagement
@EnableAspectJAutoProxy(exposeProxy = true)
@ComponentScan(basePackages = {"com.trading"})
public class CryptoTradingApplication {

    private static final Logger log = LoggerFactory.getLogger(CryptoTradingApplication.class);

    public static void main(String[] args) {
        try {
            log.info("开始启动虚拟货币量化交易系统...");
            
            // 启动Spring应用上下文
            ConfigurableApplicationContext context = SpringApplication.run(CryptoTradingApplication.class, args);
            
            // 获取启动顺序管理器
            StartupOrderManager startupManager = context.getBean(StartupOrderManager.class);
            
            // 按顺序初始化各模块
            startupManager.startModules();
            
            // 注册关闭钩子
            ShutdownHook shutdownHook = context.getBean(ShutdownHook.class);
            Runtime.getRuntime().addShutdownHook(new Thread(shutdownHook::shutdown));
            
            log.info("虚拟货币量化交易系统启动完成");
        } catch (Exception e) {
            log.error("系统启动失败", e);
            System.exit(1);
        }
    }
}
```

## 5. 模块依赖关系

模块之间的依赖关系如下：

```
crypto-bootstrap
    |-- crypto-market-data
    |   |-- crypto-sdk
    |   |   |-- crypto-common
    |-- crypto-ml-strategy (Python模块)
    |-- crypto-trade
    |   |-- crypto-sdk
    |   |   |-- crypto-common
    |-- crypto-account
    |   |-- crypto-common
    |-- crypto-risk
    |   |-- crypto-common
    |-- crypto-monitor
    |   |-- crypto-common
```

## 6. 虚拟线程应用

系统充分利用JDK21提供的虚拟线程特性：

```java
// WebSocketManager中的虚拟线程应用示例
public void startWebSocketConnections() {
    for (String symbol : symbols) {
        // 使用虚拟线程处理WebSocket连接
        Thread.startVirtualThread(() -> {
            connectWebSocket(symbol);
        });
    }
}

// 策略执行器中的虚拟线程应用示例
public void executeStrategies(List<Strategy> strategies, MarketData marketData) {
    strategies.forEach(strategy -> {
        // 使用虚拟线程并行执行策略
        Thread.startVirtualThread(() -> {
            try {
                strategy.onMarketData(marketData);
            } catch (Exception e) {
                log.error("策略执行异常: {}", strategy.getId(), e);
            }
        });
    });
}

// 订单追踪器中的虚拟线程应用示例
public void startOrderTracking() {
    Thread.startVirtualThread(() -> {
        while (isRunning) {
            try {
                trackOrders();
                Thread.sleep(Duration.ofSeconds(1));
            } catch (InterruptedException e) {
                log.error("订单追踪线程中断", e);
                Thread.currentThread().interrupt();
                break;
            } catch (Exception e) {
                log.error("订单追踪异常", e);
            }
        }
    });
}

// API请求处理中的虚拟线程应用示例
public <T> CompletableFuture<T> sendApiRequest(Callable<T> apiCall) {
    return CompletableFuture.supplyAsync(() -> {
        try {
            return apiCall.call();
        } catch (Exception e) {
            throw new CompletionException(e);
        }
    }, Executors.newVirtualThreadPerTaskExecutor());
}
```

这种设计允许系统轻松处理大量并发任务，而不会占用过多的系统资源，从而提高了系统的性能和可伸缩性。