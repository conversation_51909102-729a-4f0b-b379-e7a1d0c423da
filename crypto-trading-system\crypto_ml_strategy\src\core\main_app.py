#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
主应用入口模块

提供简洁的应用入口点，协调所有已创建的模块。
"""

import sys
import asyncio
from typing import Optional
from loguru import logger

from infrastructure.dependency_setup import EnhancedDependencySetup
from infrastructure.lifecycle_management import EnhancedLifecycleManager
from infrastructure.dependency_container import get_container


class TradingStrategyApp:
    """交易策略应用主入口，协调所有模块。"""
    
    def __init__(self, config_path: Optional[str] = None):
        """初始化应用"""
        self.config_path = config_path
        self.container = get_container()
        logger.info("交易策略应用初始化完成")
    
    async def run(self) -> None:
        """运行应用"""
        try:
            # 设置增强依赖服务
            dependency_setup = EnhancedDependencySetup(self.container)
            services = dependency_setup.setup_enhanced_services()
            
            # 创建增强生命周期管理器
            lifecycle_manager = EnhancedLifecycleManager(
                memory_monitor=services['memory_monitor'],
                async_error_recovery=services['async_error_recovery'],
                health_check_service=self.container.resolve('IHealthCheckService'),
                metrics_service=self.container.resolve('IMetricsService')
            )
            
            # 启动应用并进入主循环
            await lifecycle_manager.start_application()
            while True:
                await asyncio.sleep(1)
                
        except KeyboardInterrupt:
            logger.info("收到中断信号，正在关闭...")
        except Exception as e:
            logger.error(f"应用运行失败: {e}")
            raise


def main():
    """主函数"""
    try:
        config_path = sys.argv[1] if len(sys.argv) > 1 else None
        app = TradingStrategyApp(config_path)
        asyncio.run(app.run())
    except Exception as e:
        logger.error(f"应用启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()