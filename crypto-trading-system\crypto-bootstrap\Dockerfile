FROM eclipse-temurin:21-jre-alpine

# 设置工作目录
WORKDIR /app

# 添加应用JAR文件
ADD target/crypto-bootstrap-*.jar app.jar

# 安装tzdata包并设置时区
RUN apk add --no-cache tzdata && \
    cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    echo "Asia/Shanghai" > /etc/timezone && \
    apk del tzdata

# 创建日志目录
RUN mkdir -p /logs

# 暴露端口
EXPOSE 8080

# 启动命令
ENTRYPOINT ["java", "-jar", "-XX:+UseZGC", "-Xms256m", "-Xmx1024m", "-Djava.security.egd=file:/dev/./urandom", "/app/app.jar"]