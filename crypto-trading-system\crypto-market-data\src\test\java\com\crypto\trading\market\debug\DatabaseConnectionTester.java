package com.crypto.trading.market.debug;

import com.crypto.trading.market.repository.InfluxDBRepository;
import com.crypto.trading.market.repository.MySQLMarketDataRepository;
import com.influxdb.client.InfluxDBClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;

import javax.sql.DataSource;
import java.sql.Connection;

/**
 * 数据库连接测试器
 * 用于验证InfluxDB和MySQL的连接状态
 */
@SpringBootApplication
@ComponentScan(basePackages = "com.crypto.trading")
public class DatabaseConnectionTester implements CommandLineRunner {

    private static final Logger log = LoggerFactory.getLogger(DatabaseConnectionTester.class);

    @Autowired(required = false)
    private InfluxDBRepository influxDBRepository;

    @Autowired(required = false)
    private MySQLMarketDataRepository mySQLRepository;

    @Autowired(required = false)
    private DataSource dataSource;

    @Autowired(required = false)
    private InfluxDBClient influxDBClient;

    public static void main(String[] args) {
        SpringApplication.run(DatabaseConnectionTester.class, args);
    }

    @Override
    public void run(String... args) throws Exception {
        log.info("开始数据库连接测试...");

        // 测试MySQL连接
        testMySQLConnection();

        // 测试InfluxDB连接
        testInfluxDBConnection();

        log.info("数据库连接测试完成");
        System.exit(0);
    }

    private void testMySQLConnection() {
        log.info("=== MySQL连接测试 ===");
        
        try {
            if (dataSource == null) {
                log.error("MySQL DataSource未配置");
                return;
            }

            try (Connection connection = dataSource.getConnection()) {
                if (connection != null && !connection.isClosed()) {
                    log.info("✅ MySQL连接成功");
                    log.info("数据库URL: {}", connection.getMetaData().getURL());
                    log.info("数据库产品: {}", connection.getMetaData().getDatabaseProductName());
                    log.info("数据库版本: {}", connection.getMetaData().getDatabaseProductVersion());
                    
                    // 测试简单查询
                    try (var stmt = connection.createStatement();
                         var rs = stmt.executeQuery("SELECT 1")) {
                        if (rs.next()) {
                            log.info("✅ MySQL查询测试成功");
                        }
                    }
                } else {
                    log.error("❌ MySQL连接失败：连接为空或已关闭");
                }
            }

            // 测试Repository
            if (mySQLRepository != null) {
                log.info("✅ MySQL Repository已注入");
            } else {
                log.warn("⚠️ MySQL Repository未注入");
            }

        } catch (Exception e) {
            log.error("❌ MySQL连接测试失败", e);
        }
    }

    private void testInfluxDBConnection() {
        log.info("=== InfluxDB连接测试 ===");
        
        try {
            if (influxDBClient == null) {
                log.error("❌ InfluxDB Client未配置");
                return;
            }

            // 测试连接
            try {
                var health = influxDBClient.health();
                if (health != null) {
                    log.info("✅ InfluxDB连接成功");
                    log.info("健康状态: {}", health.getStatus());
                    log.info("版本: {}", health.getVersion());
                } else {
                    log.error("❌ InfluxDB健康检查返回null");
                }
            } catch (Exception e) {
                log.error("❌ InfluxDB健康检查失败", e);
            }

            // 测试Repository
            if (influxDBRepository != null) {
                log.info("✅ InfluxDB Repository已注入");
            } else {
                log.warn("⚠️ InfluxDB Repository未注入");
            }

            // 测试基本操作
            try {
                var buckets = influxDBClient.getBucketsApi().findBuckets();
                log.info("✅ InfluxDB桶列表查询成功，找到{}个桶", buckets.size());
                buckets.forEach(bucket -> 
                    log.info("桶: {} (组织: {})", bucket.getName(), bucket.getOrgID()));
            } catch (Exception e) {
                log.error("❌ InfluxDB桶查询失败", e);
            }

        } catch (Exception e) {
            log.error("❌ InfluxDB连接测试失败", e);
        }
    }
}
