package com.crypto.trading.sdk.websocket;

import java.util.function.Consumer;

/**
 * @name: BinanceWebSocketClient
 * @author: zeek
 * @description: 币安WebSocket客户端的接口定义。
 *               封装了与币安WebSocket API的连接和交互逻辑。
 * @create: 2024-07-16 04:25
 */
public interface BinanceWebSocketClient {

    /**
     * 连接到用户数据流。
     *
     * @param listenKey 币安API返回的 listenKey
     * @param handler   用于处理接收到的消息的回调处理器
     * @return 返回一个WebSocket连接的唯一ID，可用于后续关闭连接等操作。
     */
    int connectUserStream(String listenKey, WebSocketMessageHandler handler);
    
    /**
     * 连接到用户数据流（Consumer<String>版本）。
     * 此方法是为了允许直接传递Java 8的Consumer<String>接口作为回调。
     *
     * @param listenKey 币安API返回的 listenKey
     * @param messageConsumer 用于处理接收到的消息的消费者函数
     * @return 返回一个WebSocket连接的唯一ID，可用于后续关闭连接等操作。
     */
    int connectUserStream(String listenKey, Consumer<String> messageConsumer);

    /**
     * 关闭指定的WebSocket连接。
     *
     * @param connectionId 要关闭的连接的ID
     */
    void closeConnection(int connectionId);
    
    /**
     * 订阅深度数据（使用币安官方partialDepthStream方法）。
     *
     * @param symbol 交易对，例如 "btcusdt"
     * @param levels 深度级别，例如 5, 10, 20
     * @param speed 更新速度（毫秒），例如 100, 250, 500
     * @param messageHandler 消息处理器
     * @return 返回一个WebSocket连接的唯一ID
     */
    int subscribeDepth(String symbol, int levels, int speed, Consumer<String> messageHandler);
    
    /**
     * 订阅部分深度数据（币安官方方法）。
     *
     * @param symbol 交易对，例如 "btcusdt"
     * @param levels 深度级别，例如 5, 10, 20
     * @param speed 更新速度（毫秒），例如 100, 250, 500
     * @param messageHandler 消息处理器
     * @return 返回一个WebSocket连接的唯一ID
     */
    int subscribePartialDepth(String symbol, int levels, int speed, Consumer<String> messageHandler);
    
    /**
     * 订阅差分深度数据（币安官方方法）。
     *
     * @param symbol 交易对，例如 "btcusdt"
     * @param speed 更新速度（毫秒），例如 100, 250, 500
     * @param messageHandler 消息处理器
     * @return 返回一个WebSocket连接的唯一ID
     */
    int subscribeDiffDepth(String symbol, int speed, Consumer<String> messageHandler);
    
    /**
     * 订阅K线数据。
     *
     * @param symbol 交易对，例如 "btcusdt"
     * @param interval K线间隔，例如 "1m", "5m", "1h", "1d"
     * @param messageHandler 消息处理器
     * @return 返回一个WebSocket连接的唯一ID
     */
    int subscribeKline(String symbol, String interval, Consumer<String> messageHandler);
    
    /**
     * 订阅聚合交易数据。
     *
     * @param symbol 交易对，例如 "btcusdt"
     * @param messageHandler 消息处理器
     * @return 返回一个WebSocket连接的唯一ID
     */
    int subscribeAggTrade(String symbol, Consumer<String> messageHandler);
    
    /**
     * 获取当前活跃的WebSocket连接数。
     *
     * @return 活跃连接数
     */
    int getActiveConnectionCount();
}
