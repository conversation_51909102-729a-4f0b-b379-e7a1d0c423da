"""
Crypto ML Strategy - 基准测试和性能监控模块

该包包含基准测试、性能监控、性能验证和性能报告相关的所有组件。
用于系统性能评估、回归检测和性能优化指导。

主要组件:
- benchmark_core_framework: 基准测试核心框架
- performance_measurement_engine: 性能测量引擎
- performance_monitoring_dashboard: 性能监控仪表板
- benchmark_reporting_system: 基准测试报告系统
- performance_validation_framework: 性能验证框架

作者: Crypto ML Strategy Team
版本: 1.0.0
"""

# 基准测试组件将在需要时导入
# from .benchmark_core_framework import BenchmarkCoreFramework
# from .performance_measurement_engine import PerformanceMeasurementEngine
# from .performance_monitoring_dashboard import PerformanceMonitoringDashboard

__all__ = []

__version__ = '1.0.0'
__author__ = 'Crypto ML Strategy Team'