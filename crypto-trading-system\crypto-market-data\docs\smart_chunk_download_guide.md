# 智能分块下载功能使用指南

## 功能概述

智能分块下载功能是对币安API数据获取的优化实现，通过动态调整块大小和并行请求数，高效获取大量历史数据。相比传统的下载方式，智能分块下载具有以下优势：

1. **自动调整块大小**：根据数据量、时间间隔和历史性能自动计算最佳分块大小
2. **并行下载**：支持多个分块并行下载，显著提高下载速度
3. **速率限制自适应**：自动检测API速率限制并动态调整请求频率
4. **智能重试**：使用指数退避重试策略，减少临时错误带来的影响
5. **性能监控**：记录每个交易对的下载性能，持续优化下载参数

## 使用方法

### 1. 单个交易对智能下载

```java
// 引入服务
@Autowired
private HistoricalDataService historicalDataService;

// 设置下载参数
String symbol = "BTCUSDT";
KlineInterval interval = KlineInterval.HOUR_1;
LocalDateTime startTime = LocalDateTime.now().minusDays(30);
LocalDateTime endTime = LocalDateTime.now();
boolean storeToDb = true;
boolean sendToKafka = true;

// 使用智能分块下载
List<KlineDataDTO> data = historicalDataService.getHistoricalKlinesWithSmartChunks(
    symbol, interval, startTime, endTime, storeToDb, sendToKafka
);

// 处理下载的数据
System.out.println("下载完成，共获取 " + data.size() + " 条数据");
```

### 2. 多个交易对批量智能下载

```java
// 引入服务
@Autowired
private HistoricalDataService historicalDataService;

// 设置下载参数
List<String> symbols = Arrays.asList("BTCUSDT", "ETHUSDT", "BNBUSDT");
KlineInterval interval = KlineInterval.HOUR_1;
LocalDateTime startTime = LocalDateTime.now().minusDays(30);
LocalDateTime endTime = LocalDateTime.now();
boolean storeToDb = true;
boolean sendToKafka = true;

// 启动批量下载任务
String taskId = historicalDataService.batchDownloadKlinesWithSmartChunks(
    symbols, interval, startTime, endTime, storeToDb, sendToKafka
);

// 获取任务状态
String status = historicalDataService.getTaskStatus(taskId);
System.out.println("任务状态: " + status);
```

### 3. 监控和管理下载任务

```java
// 获取任务状态
String status = historicalDataService.getTaskStatus(taskId);

// 暂停任务
historicalDataService.pauseTask(taskId);

// 恢复任务
historicalDataService.resumeTask(taskId);

// 取消任务
historicalDataService.cancelTask(taskId);

// 获取所有任务状态
List<String> allTasks = historicalDataService.getAllTaskStatus();
```

## 最佳实践和注意事项

1. **大数据量下载**：对于跨度很大的历史数据（如多个月或多年），建议使用批量智能下载，系统会自动选择最优参数。

2. **速率限制**：币安API对每个IP有请求频率限制，系统会自动适应这些限制，但如果您有多个应用共享同一IP，建议手动降低并发请求数。

3. **存储和消息队列**：对于大量数据，建议启用批量存储功能，以减少数据库和消息队列的压力。

4. **内存考虑**：下载大量数据可能会占用较多内存，请确保应用程序有足够的堆内存。

5. **状态监控**：对于长时间运行的下载任务，建议通过任务状态API定期检查进度和状态。

## 性能优化

智能分块下载算法会持续学习和优化，以下是一些能够帮助系统更好地优化性能的措施：

1. 允许系统完成足够数量的下载请求（>10次），以便收集足够的性能数据。

2. 对于频繁下载的交易对，系统会积累更多历史性能数据，从而提供更精确的优化。

3. 如果发现某个交易对始终下载速度较慢，可以考虑使用较小的时间范围分多次下载。

## 错误处理

系统内置了错误处理和重试机制，对于临时性错误（如网络波动、服务器繁忙等）会自动重试。对于持久性错误（如权限问题、无效参数等），系统会记录详细错误信息并停止相应的下载任务。

可以通过任务状态API查看详细的错误信息，以便进行故障排除。