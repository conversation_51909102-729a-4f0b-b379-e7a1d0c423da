"""
风险管理配置模块

提供风险管理相关的配置参数、数据结构和常量定义。
支持灵活的风险参数配置和策略选择。

Author: Crypto ML Strategy Team
Date: 2024-12-19
"""

from dataclasses import dataclass, field
from typing import Dict, List, Optional, Union, Any
from enum import Enum
import logging
from datetime import datetime

logger = logging.getLogger(__name__)


class PositionSizeMethod(Enum):
    """仓位计算方法枚举"""
    FIXED_RATIO = "fixed_ratio"           # 固定比例
    KELLY_FORMULA = "kelly_formula"       # Kelly公式
    RISK_PARITY = "risk_parity"          # 风险平价
    VOLATILITY_ADJUSTED = "volatility_adjusted"  # 波动率调整
    ADAPTIVE = "adaptive"                 # 自适应


class StopLossType(Enum):
    """止损类型枚举"""
    FIXED_PERCENTAGE = "fixed_percentage"  # 固定百分比
    ATR_BASED = "atr_based"               # 基于ATR
    TRAILING = "trailing"                 # 追踪止损
    TECHNICAL_INDICATOR = "technical_indicator"  # 技术指标
    ADAPTIVE = "adaptive"                 # 自适应


class TakeProfitType(Enum):
    """止盈类型枚举"""
    FIXED_TARGET = "fixed_target"         # 固定目标
    PARTIAL_PROFIT = "partial_profit"     # 分批止盈
    TRAILING = "trailing"                 # 追踪止盈
    RISK_REWARD_RATIO = "risk_reward_ratio"  # 风险收益比
    ADAPTIVE = "adaptive"                 # 自适应


class RiskLevel(Enum):
    """风险等级枚举"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class RiskMetrics:
    """风险指标数据类"""
    timestamp: datetime
    portfolio_value: float
    total_exposure: float
    max_drawdown: float
    current_drawdown: float
    var_95: float  # 95%置信水平的VaR
    cvar_95: float  # 95%置信水平的CVaR
    sharpe_ratio: float
    volatility: float
    beta: float
    correlation_matrix: Optional[Dict[str, Dict[str, float]]] = None
    risk_level: RiskLevel = RiskLevel.MEDIUM
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'timestamp': self.timestamp.isoformat(),
            'portfolio_value': self.portfolio_value,
            'total_exposure': self.total_exposure,
            'max_drawdown': self.max_drawdown,
            'current_drawdown': self.current_drawdown,
            'var_95': self.var_95,
            'cvar_95': self.cvar_95,
            'sharpe_ratio': self.sharpe_ratio,
            'volatility': self.volatility,
            'beta': self.beta,
            'correlation_matrix': self.correlation_matrix,
            'risk_level': self.risk_level.value
        }


@dataclass
class PositionInfo:
    """仓位信息数据类"""
    symbol: str
    current_size: float
    suggested_size: float
    max_allowed_size: float
    entry_price: float
    current_price: float
    unrealized_pnl: float
    stop_loss_price: Optional[float] = None
    take_profit_price: Optional[float] = None
    risk_amount: float = 0.0
    position_value: float = 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'symbol': self.symbol,
            'current_size': self.current_size,
            'suggested_size': self.suggested_size,
            'max_allowed_size': self.max_allowed_size,
            'entry_price': self.entry_price,
            'current_price': self.current_price,
            'unrealized_pnl': self.unrealized_pnl,
            'stop_loss_price': self.stop_loss_price,
            'take_profit_price': self.take_profit_price,
            'risk_amount': self.risk_amount,
            'position_value': self.position_value
        }


@dataclass
class RiskEvent:
    """风险事件数据类"""
    event_type: str
    severity: RiskLevel
    message: str
    timestamp: datetime
    symbol: Optional[str] = None
    current_value: Optional[float] = None
    threshold_value: Optional[float] = None
    action_required: bool = False
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'event_type': self.event_type,
            'severity': self.severity.value,
            'message': self.message,
            'timestamp': self.timestamp.isoformat(),
            'symbol': self.symbol,
            'current_value': self.current_value,
            'threshold_value': self.threshold_value,
            'action_required': self.action_required,
            'metadata': self.metadata
        }


@dataclass
class RiskConfig:
    """风险管理配置数据类"""
    # 仓位管理配置
    max_single_position_ratio: float = 0.1  # 最大单笔仓位比例
    max_total_position_ratio: float = 0.8   # 最大总仓位比例
    position_size_method: PositionSizeMethod = PositionSizeMethod.FIXED_RATIO
    default_position_ratio: float = 0.02    # 默认仓位比例
    
    # 止损配置
    stop_loss_type: StopLossType = StopLossType.FIXED_PERCENTAGE
    default_stop_loss_pct: float = 0.02     # 默认止损百分比
    max_stop_loss_pct: float = 0.05         # 最大止损百分比
    atr_multiplier: float = 2.0             # ATR倍数
    trailing_stop_distance: float = 0.01    # 追踪止损距离
    
    # 止盈配置
    take_profit_type: TakeProfitType = TakeProfitType.FIXED_TARGET
    default_take_profit_pct: float = 0.04   # 默认止盈百分比
    risk_reward_ratio: float = 2.0          # 风险收益比
    partial_profit_levels: List[float] = field(default_factory=lambda: [0.02, 0.04, 0.06])
    
    # 回撤控制配置
    max_drawdown_threshold: float = 0.15    # 最大回撤阈值
    daily_loss_limit: float = 0.05          # 日损失限制
    emergency_stop_threshold: float = 0.20  # 紧急止损阈值
    
    # 风险评估配置
    var_confidence_level: float = 0.95      # VaR置信水平
    risk_assessment_window: int = 252       # 风险评估时间窗口(天)
    volatility_window: int = 20             # 波动率计算窗口
    correlation_window: int = 60            # 相关性计算窗口
    
    # 监控和告警配置
    risk_check_interval: int = 60           # 风险检查间隔(秒)
    alert_thresholds: Dict[str, float] = field(default_factory=lambda: {
        'high_volatility': 0.05,
        'high_correlation': 0.8,
        'low_liquidity': 0.1,
        'position_concentration': 0.3
    })
    
    # 自适应配置
    enable_adaptive_sizing: bool = True     # 启用自适应仓位
    enable_dynamic_stops: bool = True       # 启用动态止损
    market_regime_detection: bool = True    # 市场状态检测
    
    def validate(self) -> bool:
        """验证配置参数的有效性"""
        try:
            # 检查比例参数
            if not (0 < self.max_single_position_ratio <= 1):
                raise ValueError("max_single_position_ratio must be between 0 and 1")
            
            if not (0 < self.max_total_position_ratio <= 1):
                raise ValueError("max_total_position_ratio must be between 0 and 1")
            
            if self.max_single_position_ratio > self.max_total_position_ratio:
                raise ValueError("max_single_position_ratio cannot exceed max_total_position_ratio")
            
            # 检查止损止盈参数
            if not (0 < self.default_stop_loss_pct < 1):
                raise ValueError("default_stop_loss_pct must be between 0 and 1")
            
            if not (0 < self.default_take_profit_pct < 1):
                raise ValueError("default_take_profit_pct must be between 0 and 1")
            
            # 检查回撤参数
            if not (0 < self.max_drawdown_threshold < 1):
                raise ValueError("max_drawdown_threshold must be between 0 and 1")
            
            # 检查时间窗口参数
            if self.risk_assessment_window <= 0:
                raise ValueError("risk_assessment_window must be positive")
            
            logger.info("Risk configuration validation passed")
            return True
            
        except ValueError as e:
            logger.error(f"Risk configuration validation failed: {e}")
            return False
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'max_single_position_ratio': self.max_single_position_ratio,
            'max_total_position_ratio': self.max_total_position_ratio,
            'position_size_method': self.position_size_method.value,
            'default_position_ratio': self.default_position_ratio,
            'stop_loss_type': self.stop_loss_type.value,
            'default_stop_loss_pct': self.default_stop_loss_pct,
            'max_stop_loss_pct': self.max_stop_loss_pct,
            'atr_multiplier': self.atr_multiplier,
            'trailing_stop_distance': self.trailing_stop_distance,
            'take_profit_type': self.take_profit_type.value,
            'default_take_profit_pct': self.default_take_profit_pct,
            'risk_reward_ratio': self.risk_reward_ratio,
            'partial_profit_levels': self.partial_profit_levels,
            'max_drawdown_threshold': self.max_drawdown_threshold,
            'daily_loss_limit': self.daily_loss_limit,
            'emergency_stop_threshold': self.emergency_stop_threshold,
            'var_confidence_level': self.var_confidence_level,
            'risk_assessment_window': self.risk_assessment_window,
            'volatility_window': self.volatility_window,
            'correlation_window': self.correlation_window,
            'risk_check_interval': self.risk_check_interval,
            'alert_thresholds': self.alert_thresholds,
            'enable_adaptive_sizing': self.enable_adaptive_sizing,
            'enable_dynamic_stops': self.enable_dynamic_stops,
            'market_regime_detection': self.market_regime_detection
        }


# 默认风险配置
DEFAULT_RISK_CONFIG = RiskConfig()

# 保守风险配置
CONSERVATIVE_RISK_CONFIG = RiskConfig(
    max_single_position_ratio=0.05,
    max_total_position_ratio=0.6,
    default_stop_loss_pct=0.015,
    default_take_profit_pct=0.03,
    max_drawdown_threshold=0.1,
    risk_reward_ratio=2.5
)

# 激进风险配置
AGGRESSIVE_RISK_CONFIG = RiskConfig(
    max_single_position_ratio=0.15,
    max_total_position_ratio=0.9,
    default_stop_loss_pct=0.03,
    default_take_profit_pct=0.06,
    max_drawdown_threshold=0.2,
    risk_reward_ratio=1.5
)
