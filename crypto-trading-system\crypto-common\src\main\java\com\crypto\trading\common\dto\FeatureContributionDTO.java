package com.crypto.trading.common.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.io.Serializable;

/**
 * @name: FeatureContributionDTO
 * @author: zeek
 * @description: 特征贡献度数据传输对象
 * @create: 2024-07-16 00:15
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FeatureContributionDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Double lppl_bubble_probability;
    private Double hematread_momentum;
    private Double bmsb_support_level;
    private Double super_trend_signal;
}
