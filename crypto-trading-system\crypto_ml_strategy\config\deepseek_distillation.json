{"model_architecture": {"teacher_model": {"hidden_dims": [512, 256, 128, 64], "dropout_rate": 0.3, "attention_heads": 8, "use_batch_norm": true, "activation": "relu"}, "student_model": {"hidden_dims": [128, 64, 32], "dropout_rate": 0.2, "use_batch_norm": true, "activation": "relu"}}, "distillation_parameters": {"temperature": 4.0, "alpha": 0.7, "beta": 0.3, "feature_loss_weight": 0.1, "attention_loss_weight": 0.05, "compression_target_ratio": 4.0}, "training_config": {"learning_rate": 0.001, "batch_size": 64, "epochs": 100, "early_stopping_patience": 10, "validation_split": 0.2, "optimizer": "adam", "scheduler": "reduce_lr_on_plateau", "scheduler_params": {"patience": 5, "factor": 0.5, "min_lr": 1e-06}}, "data_config": {"feature_scaling": "standard", "target_scaling": "none", "sequence_length": 60, "prediction_horizon": 1, "feature_selection_threshold": 0.05}, "model_paths": {"teacher_model": "models/deepseek_teacher.pth", "student_model": "models/deepseek_student.pth", "best_teacher": "models/best_teacher_model.pth", "best_student": "models/best_student_model.pth", "scaler": "models/feature_scaler.pkl", "config_backup": "models/distillation_config_backup.json"}, "performance_targets": {"max_inference_time_ms": 100, "min_accuracy_retention": 0.95, "max_model_size_mb": 50, "target_compression_ratio": 4.0, "memory_usage_limit_mb": 500}, "logging": {"log_level": "INFO", "log_training_metrics": true, "log_validation_metrics": true, "save_training_plots": true, "metrics_save_interval": 10}, "api_integration": {"enable_online_api": false, "api_fallback": true, "cache_api_responses": true, "cache_ttl_seconds": 3600, "rate_limit_requests_per_minute": 60}, "deployment": {"model_format": "pytorch", "enable_quantization": true, "quantization_bits": 8, "enable_pruning": false, "pruning_sparsity": 0.1, "enable_onnx_export": true, "onnx_optimization_level": "all"}}