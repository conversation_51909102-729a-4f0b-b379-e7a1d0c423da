#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
生命周期管理器模块

该模块提供组件生命周期管理功能，支持组件的初始化、启动、停止和清理。
"""

import signal
import time
import threading
from typing import Dict, Any, List, Optional, Callable
from enum import Enum
from dataclasses import dataclass
from loguru import logger

from .service_interfaces import ILifecycleService, IHealthCheckService, IMetricsService


class LifecycleState(Enum):
    """生命周期状态枚举"""
    CREATED = "created"
    INITIALIZING = "initializing"
    INITIALIZED = "initialized"
    STARTING = "starting"
    STARTED = "started"
    STOPPING = "stopping"
    STOPPED = "stopped"
    CLEANING = "cleaning"
    CLEANED = "cleaned"
    FAILED = "failed"


@dataclass
class ManagedComponent:
    """管理的组件"""
    name: str
    instance: Any
    priority: int = 0  # 启动优先级，数字越小优先级越高
    dependencies: List[str] = None
    
    def __post_init__(self):
        if self.dependencies is None:
            self.dependencies = []


class AdvancedLifecycleManager:
    """
    高级生命周期管理器
    
    提供组件的生命周期管理，包括依赖关系处理、优雅关闭等功能。
    """
    
    def __init__(self):
        """初始化生命周期管理器"""
        self._state = LifecycleState.CREATED
        self._components: Dict[str, ManagedComponent] = {}
        self._startup_order: List[str] = []
        self._shutdown_order: List[str] = []
        self._state_lock = threading.RLock()
        self._shutdown_timeout = 30  # 秒
        self._health_check_service: Optional[IHealthCheckService] = None
        self._metrics_service: Optional[IMetricsService] = None
        
        # 注册信号处理器
        self._setup_signal_handlers()
        
        logger.info("高级生命周期管理器初始化完成")
    
    def _setup_signal_handlers(self) -> None:
        """设置信号处理器"""
        try:
            signal.signal(signal.SIGINT, self._signal_handler)
            signal.signal(signal.SIGTERM, self._signal_handler)
            logger.debug("信号处理器设置完成")
        except Exception as e:
            logger.warning(f"设置信号处理器失败: {e}")
    
    def _signal_handler(self, signum: int, frame) -> None:
        """信号处理器"""
        logger.info(f"收到信号 {signum}，开始优雅关闭...")
        
        if self._state == LifecycleState.STARTED:
            threading.Thread(target=self.stop, daemon=True).start()
    
    def register_component(self, name: str, instance: Any, 
                          priority: int = 0, dependencies: List[str] = None) -> None:
        """
        注册组件
        
        Args:
            name: 组件名称
            instance: 组件实例
            priority: 启动优先级
            dependencies: 依赖的组件名称列表
        """
        with self._state_lock:
            if self._state not in [LifecycleState.CREATED, LifecycleState.INITIALIZED]:
                raise RuntimeError(f"无法在状态 {self._state.value} 下注册组件")
            
            component = ManagedComponent(
                name=name,
                instance=instance,
                priority=priority,
                dependencies=dependencies or []
            )
            
            self._components[name] = component
            logger.info(f"注册组件: {name} (优先级: {priority})")
    
    def set_health_check_service(self, health_check_service: IHealthCheckService) -> None:
        """设置健康检查服务"""
        self._health_check_service = health_check_service
    
    def set_metrics_service(self, metrics_service: IMetricsService) -> None:
        """设置指标服务"""
        self._metrics_service = metrics_service
    
    def initialize(self) -> None:
        """初始化所有组件"""
        with self._state_lock:
            if self._state != LifecycleState.CREATED:
                raise RuntimeError(f"无法从状态 {self._state.value} 初始化")
            
            self._state = LifecycleState.INITIALIZING
            logger.info("开始初始化组件...")
            
            try:
                # 计算启动顺序
                self._calculate_startup_order()
                
                # 初始化组件
                for component_name in self._startup_order:
                    self._initialize_component(component_name)
                
                self._state = LifecycleState.INITIALIZED
                logger.info("所有组件初始化完成")
                
            except Exception as e:
                self._state = LifecycleState.FAILED
                logger.error(f"组件初始化失败: {e}")
                raise
    
    def start(self) -> None:
        """启动所有组件"""
        with self._state_lock:
            if self._state != LifecycleState.INITIALIZED:
                raise RuntimeError(f"无法从状态 {self._state.value} 启动")
            
            self._state = LifecycleState.STARTING
            logger.info("开始启动组件...")
            
            try:
                start_time = time.time()
                
                # 启动组件
                for component_name in self._startup_order:
                    self._start_component(component_name)
                
                startup_duration = time.time() - start_time
                
                self._state = LifecycleState.STARTED
                logger.info(f"所有组件启动完成，耗时: {startup_duration:.2f}秒")
                
                # 记录指标
                if self._metrics_service:
                    self._metrics_service.record_timing("lifecycle_startup_duration", startup_duration * 1000)
                
            except Exception as e:
                self._state = LifecycleState.FAILED
                logger.error(f"组件启动失败: {e}")
                raise
    
    def stop(self) -> None:
        """停止所有组件"""
        with self._state_lock:
            if self._state not in [LifecycleState.STARTED, LifecycleState.FAILED]:
                logger.warning(f"从状态 {self._state.value} 停止")
            
            self._state = LifecycleState.STOPPING
            logger.info("开始停止组件...")
            
            try:
                stop_time = time.time()
                
                # 反向停止组件
                for component_name in reversed(self._shutdown_order):
                    self._stop_component(component_name)
                
                shutdown_duration = time.time() - stop_time
                
                self._state = LifecycleState.STOPPED
                logger.info(f"所有组件停止完成，耗时: {shutdown_duration:.2f}秒")
                
                # 记录指标
                if self._metrics_service:
                    self._metrics_service.record_timing("lifecycle_shutdown_duration", shutdown_duration * 1000)
                
            except Exception as e:
                self._state = LifecycleState.FAILED
                logger.error(f"组件停止失败: {e}")
                raise
    
    def cleanup(self) -> None:
        """清理所有组件"""
        with self._state_lock:
            if self._state not in [LifecycleState.STOPPED, LifecycleState.FAILED]:
                logger.warning(f"从状态 {self._state.value} 清理")
            
            self._state = LifecycleState.CLEANING
            logger.info("开始清理组件...")
            
            try:
                # 反向清理组件
                for component_name in reversed(self._shutdown_order):
                    self._cleanup_component(component_name)
                
                self._components.clear()
                self._startup_order.clear()
                self._shutdown_order.clear()
                
                self._state = LifecycleState.CLEANED
                logger.info("所有组件清理完成")
                
            except Exception as e:
                self._state = LifecycleState.FAILED
                logger.error(f"组件清理失败: {e}")
                raise
    
    def _calculate_startup_order(self) -> None:
        """计算启动顺序"""
        # 拓扑排序算法计算依赖顺序
        visited = set()
        temp_visited = set()
        order = []
        
        def visit(component_name: str):
            if component_name in temp_visited:
                raise RuntimeError(f"检测到循环依赖: {component_name}")
            
            if component_name not in visited:
                temp_visited.add(component_name)
                
                component = self._components[component_name]
                for dep in component.dependencies:
                    if dep not in self._components:
                        raise RuntimeError(f"组件 {component_name} 依赖的组件 {dep} 未注册")
                    visit(dep)
                
                temp_visited.remove(component_name)
                visited.add(component_name)
                order.append(component_name)
        
        # 按优先级排序组件
        sorted_components = sorted(
            self._components.items(),
            key=lambda x: x[1].priority
        )
        
        for component_name, _ in sorted_components:
            if component_name not in visited:
                visit(component_name)
        
        self._startup_order = order
        self._shutdown_order = order.copy()
        
        logger.info(f"启动顺序: {' -> '.join(self._startup_order)}")
    
    def _initialize_component(self, component_name: str) -> None:
        """初始化单个组件"""
        component = self._components[component_name]
        
        try:
            logger.debug(f"初始化组件: {component_name}")
            
            if hasattr(component.instance, 'initialize'):
                component.instance.initialize()
            
            logger.debug(f"组件 {component_name} 初始化成功")
            
        except Exception as e:
            logger.error(f"组件 {component_name} 初始化失败: {e}")
            raise
    
    def _start_component(self, component_name: str) -> None:
        """启动单个组件"""
        component = self._components[component_name]
        
        try:
            logger.debug(f"启动组件: {component_name}")
            
            start_time = time.time()
            
            if hasattr(component.instance, 'start'):
                component.instance.start()
            
            startup_time = (time.time() - start_time) * 1000
            
            logger.debug(f"组件 {component_name} 启动成功，耗时: {startup_time:.2f}ms")
            
            # 记录组件启动指标
            if self._metrics_service:
                self._metrics_service.record_timing(
                    f"component_startup_duration",
                    startup_time,
                    {"component": component_name}
                )
            
        except Exception as e:
            logger.error(f"组件 {component_name} 启动失败: {e}")
            raise
    
    def _stop_component(self, component_name: str) -> None:
        """停止单个组件"""
        component = self._components[component_name]
        
        try:
            logger.debug(f"停止组件: {component_name}")
            
            if hasattr(component.instance, 'stop'):
                # 设置超时
                def stop_with_timeout():
                    component.instance.stop()
                
                stop_thread = threading.Thread(target=stop_with_timeout)
                stop_thread.start()
                stop_thread.join(timeout=self._shutdown_timeout)
                
                if stop_thread.is_alive():
                    logger.warning(f"组件 {component_name} 停止超时")
            
            logger.debug(f"组件 {component_name} 停止成功")
            
        except Exception as e:
            logger.error(f"组件 {component_name} 停止失败: {e}")
            # 继续停止其他组件
    
    def _cleanup_component(self, component_name: str) -> None:
        """清理单个组件"""
        component = self._components[component_name]
        
        try:
            logger.debug(f"清理组件: {component_name}")
            
            if hasattr(component.instance, 'cleanup'):
                component.instance.cleanup()
            
            logger.debug(f"组件 {component_name} 清理成功")
            
        except Exception as e:
            logger.error(f"组件 {component_name} 清理失败: {e}")
            # 继续清理其他组件
    
    def get_lifecycle_state(self) -> str:
        """获取生命周期状态"""
        return self._state.value
    
    def get_component_status(self) -> Dict[str, Any]:
        """获取组件状态"""
        status = {
            "lifecycle_state": self._state.value,
            "total_components": len(self._components),
            "startup_order": self._startup_order,
            "components": {}
        }
        
        for name, component in self._components.items():
            component_status = {
                "priority": component.priority,
                "dependencies": component.dependencies,
                "type": type(component.instance).__name__
            }
            
            # 尝试获取组件健康状态
            if hasattr(component.instance, 'get_health_status'):
                try:
                    component_status["health"] = component.instance.get_health_status()
                except Exception as e:
                    component_status["health_error"] = str(e)
            
            status["components"][name] = component_status
        
        return status
    
    def perform_health_check(self) -> Dict[str, Any]:
        """执行健康检查"""
        if self._health_check_service:
            return self._health_check_service.check_health()
        
        # 简单的健康检查
        healthy_components = 0
        total_components = len(self._components)
        
        for name, component in self._components.items():
            if hasattr(component.instance, 'is_healthy'):
                try:
                    if component.instance.is_healthy():
                        healthy_components += 1
                except Exception:
                    pass
            else:
                healthy_components += 1  # 假设健康
        
        return {
            "overall_healthy": self._state == LifecycleState.STARTED and healthy_components == total_components,
            "healthy_components": healthy_components,
            "total_components": total_components,
            "lifecycle_state": self._state.value
        }


if __name__ == "__main__":
    # 测试代码
    import time
    
    class TestComponent:
        def __init__(self, name: str):
            self.name = name
            self.initialized = False
            self.started = False
        
        def initialize(self):
            logger.info(f"初始化 {self.name}")
            self.initialized = True
        
        def start(self):
            logger.info(f"启动 {self.name}")
            self.started = True
        
        def stop(self):
            logger.info(f"停止 {self.name}")
            self.started = False
        
        def cleanup(self):
            logger.info(f"清理 {self.name}")
            self.initialized = False
    
    # 创建生命周期管理器
    lifecycle_manager = AdvancedLifecycleManager()
    
    # 注册测试组件
    lifecycle_manager.register_component("component1", TestComponent("Component1"), priority=1)
    lifecycle_manager.register_component("component2", TestComponent("Component2"), priority=2, dependencies=["component1"])
    lifecycle_manager.register_component("component3", TestComponent("Component3"), priority=3, dependencies=["component2"])
    
    try:
        # 测试生命周期
        lifecycle_manager.initialize()
        lifecycle_manager.start()
        
        # 获取状态
        status = lifecycle_manager.get_component_status()
        logger.info(f"组件状态: {status}")
        
        # 执行健康检查
        health = lifecycle_manager.perform_health_check()
        logger.info(f"健康检查: {health}")
        
        time.sleep(1)
        
        lifecycle_manager.stop()
        lifecycle_manager.cleanup()
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        lifecycle_manager.cleanup()