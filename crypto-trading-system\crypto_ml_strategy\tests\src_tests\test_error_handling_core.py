"""
错误处理测试核心模块

提供crypto_ml_strategy项目的错误处理系统核心测试功能，包括
异常处理测试、错误分类测试、错误上下文测试和基础组件测试。

Author: Crypto ML Strategy Team
Date: 2024-12-20
Version: 1.0.0
"""

import asyncio
import pytest
import time
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional
from unittest.mock import Mock, AsyncMock, patch
from loguru import logger

from .error_handler_core import (
    ErrorSeverity, ErrorCategory, ErrorCode, ErrorContext,
    CryptoMLException, DataError, ModelError, NetworkError,
    SystemError, KafkaError, ErrorHandler, ErrorHandlerRegistry,
    ErrorMetrics, global_error_registry, global_error_metrics,
    handle_exception
)
from .system_health_monitor import (
    HealthStatus, AlertLevel, HealthCheckResult, SystemMetrics,
    Alert, HealthCheck, SystemHealthMonitor, global_health_monitor
)
from .circuit_breaker import (
    CircuitBreakerState, CircuitBreakerConfig, CircuitBreaker,
    CircuitBreakerOpenException, CircuitBreakerTimeoutException,
    global_circuit_registry, circuit_breaker
)


class TestErrorHandlerCore:
    """错误处理核心测试类"""
    
    def test_error_severity_enum(self):
        """测试错误严重程度枚举"""
        assert ErrorSeverity.INFO.value == "info"
        assert ErrorSeverity.WARNING.value == "warning"
        assert ErrorSeverity.ERROR.value == "error"
        assert ErrorSeverity.CRITICAL.value == "critical"
    
    def test_error_category_enum(self):
        """测试错误分类枚举"""
        assert ErrorCategory.DATA_ERROR.value == "data_error"
        assert ErrorCategory.MODEL_ERROR.value == "model_error"
        assert ErrorCategory.NETWORK_ERROR.value == "network_error"
        assert ErrorCategory.SYSTEM_ERROR.value == "system_error"
        assert ErrorCategory.KAFKA_ERROR.value == "kafka_error"
    
    def test_error_code_enum(self):
        """测试错误代码枚举"""
        assert ErrorCode.DATA_MISSING.value == 1001
        assert ErrorCode.MODEL_LOAD_FAILED.value == 2001
        assert ErrorCode.NETWORK_CONNECTION_FAILED.value == 3001
        assert ErrorCode.MEMORY_INSUFFICIENT.value == 4001
        assert ErrorCode.KAFKA_CONNECTION_FAILED.value == 5001
    
    def test_error_context_creation(self):
        """测试错误上下文创建"""
        context = ErrorContext()
        assert context.component == ""
        assert context.operation == ""
        assert isinstance(context.user_data, dict)
        assert isinstance(context.system_state, dict)
        assert isinstance(context.timestamp, datetime)
        
        # 测试添加上下文信息
        context.add_context("test_key", "test_value")
        assert context.user_data["test_key"] == "test_value"
        
        context.add_system_state("cpu_usage", 75.5)
        assert context.system_state["cpu_usage"] == 75.5
    
    def test_crypto_ml_exception_creation(self):
        """测试CryptoML异常创建"""
        context = ErrorContext(component="test_component", operation="test_operation")
        
        exception = CryptoMLException(
            message="测试异常",
            error_code=ErrorCode.DATA_MISSING,
            severity=ErrorSeverity.ERROR,
            category=ErrorCategory.DATA_ERROR,
            context=context
        )
        
        assert exception.message == "测试异常"
        assert exception.error_code == ErrorCode.DATA_MISSING
        assert exception.severity == ErrorSeverity.ERROR
        assert exception.category == ErrorCategory.DATA_ERROR
        assert exception.context.component == "test_component"
        assert isinstance(exception.timestamp, datetime)
        
        # 测试转换为字典
        exception_dict = exception.to_dict()
        assert exception_dict["message"] == "测试异常"
        assert exception_dict["error_code"] == 1001
        assert exception_dict["severity"] == "error"
        assert exception_dict["category"] == "data_error"
    
    def test_specific_exception_types(self):
        """测试特定异常类型"""
        # 测试数据错误
        data_error = DataError("数据缺失", ErrorCode.DATA_MISSING)
        assert data_error.category == ErrorCategory.DATA_ERROR
        
        # 测试模型错误
        model_error = ModelError("模型加载失败", ErrorCode.MODEL_LOAD_FAILED)
        assert model_error.category == ErrorCategory.MODEL_ERROR
        
        # 测试网络错误
        network_error = NetworkError("网络连接失败", ErrorCode.NETWORK_CONNECTION_FAILED)
        assert network_error.category == ErrorCategory.NETWORK_ERROR
        
        # 测试系统错误
        system_error = SystemError("内存不足", ErrorCode.MEMORY_INSUFFICIENT)
        assert system_error.category == ErrorCategory.SYSTEM_ERROR
        
        # 测试Kafka错误
        kafka_error = KafkaError("Kafka连接失败", ErrorCode.KAFKA_CONNECTION_FAILED)
        assert kafka_error.category == ErrorCategory.KAFKA_ERROR
    
    def test_error_metrics(self):
        """测试错误指标收集"""
        metrics = ErrorMetrics()
        
        # 创建测试异常
        error = CryptoMLException(
            message="测试错误",
            error_code=ErrorCode.DATA_MISSING,
            category=ErrorCategory.DATA_ERROR
        )
        
        # 记录错误
        metrics.record_error(error)
        
        # 验证指标
        summary = metrics.get_error_summary()
        assert summary["total_errors"] == 1
        assert "data_error:1001" in summary["error_counts_by_type"]
        assert len(summary["recent_errors"]) == 1
        assert "data_error" in summary["error_categories"]


class MockErrorHandler(ErrorHandler):
    """模拟错误处理器"""
    
    def __init__(self, can_handle_result: bool = True, handle_success: bool = True, priority: int = 100):
        self.can_handle_result = can_handle_result
        self.handle_success = handle_success
        self.priority_value = priority
        self.handled_errors = []
    
    def can_handle(self, error: Exception) -> bool:
        return self.can_handle_result
    
    async def handle_error(self, error: Exception, context: Optional[ErrorContext] = None) -> bool:
        self.handled_errors.append((error, context))
        return self.handle_success
    
    def get_priority(self) -> int:
        return self.priority_value


class TestErrorHandlerRegistry:
    """错误处理器注册表测试类"""
    
    def test_handler_registration(self):
        """测试处理器注册"""
        registry = ErrorHandlerRegistry()
        handler = MockErrorHandler()
        
        registry.register(handler)
        assert len(registry.handlers) == 1
        assert registry.handlers[0] == handler
    
    def test_handler_priority_sorting(self):
        """测试处理器优先级排序"""
        registry = ErrorHandlerRegistry()
        
        handler1 = MockErrorHandler(priority=200)
        handler2 = MockErrorHandler(priority=100)
        handler3 = MockErrorHandler(priority=150)
        
        registry.register(handler1)
        registry.register(handler2)
        registry.register(handler3)
        
        # 验证按优先级排序
        assert registry.handlers[0].get_priority() == 100
        assert registry.handlers[1].get_priority() == 150
        assert registry.handlers[2].get_priority() == 200
    
    def test_handler_unregistration(self):
        """测试处理器注销"""
        registry = ErrorHandlerRegistry()
        handler = MockErrorHandler()
        
        registry.register(handler)
        assert len(registry.handlers) == 1
        
        registry.unregister(MockErrorHandler)
        assert len(registry.handlers) == 0
    
    @pytest.mark.asyncio
    async def test_error_handling(self):
        """测试错误处理"""
        registry = ErrorHandlerRegistry()
        
        # 注册能处理错误的处理器
        successful_handler = MockErrorHandler(can_handle_result=True, handle_success=True)
        registry.register(successful_handler)
        
        # 创建测试错误
        error = CryptoMLException("测试错误", ErrorCode.DATA_MISSING)
        context = ErrorContext(component="test")
        
        # 处理错误
        result = await registry.handle_error(error, context)
        
        assert result is True
        assert len(successful_handler.handled_errors) == 1
        assert successful_handler.handled_errors[0][0] == error
        assert successful_handler.handled_errors[0][1] == context
    
    @pytest.mark.asyncio
    async def test_error_handling_failure(self):
        """测试错误处理失败"""
        registry = ErrorHandlerRegistry()
        
        # 注册不能处理错误的处理器
        failing_handler = MockErrorHandler(can_handle_result=True, handle_success=False)
        registry.register(failing_handler)
        
        # 创建测试错误
        error = CryptoMLException("测试错误", ErrorCode.DATA_MISSING)
        
        # 处理错误
        result = await registry.handle_error(error)
        
        assert result is False
    
    @pytest.mark.asyncio
    async def test_no_suitable_handler(self):
        """测试没有合适的处理器"""
        registry = ErrorHandlerRegistry()
        
        # 注册不能处理该类型错误的处理器
        handler = MockErrorHandler(can_handle_result=False)
        registry.register(handler)
        
        # 创建测试错误
        error = CryptoMLException("测试错误", ErrorCode.DATA_MISSING)
        
        # 处理错误
        result = await registry.handle_error(error)
        
        assert result is False
        assert len(handler.handled_errors) == 0


class TestHandleExceptionDecorator:
    """错误处理装饰器测试类"""
    
    @pytest.mark.asyncio
    async def test_async_function_success(self):
        """测试异步函数成功执行"""
        @handle_exception(component="test_component", operation="test_operation")
        async def test_async_func():
            return "success"
        
        result = await test_async_func()
        assert result == "success"
    
    @pytest.mark.asyncio
    async def test_async_function_exception(self):
        """测试异步函数异常处理"""
        @handle_exception(component="test_component", operation="test_operation", reraise=False)
        async def test_async_func():
            raise ValueError("测试异常")
        
        result = await test_async_func()
        assert result is None
    
    def test_sync_function_success(self):
        """测试同步函数成功执行"""
        @handle_exception(component="test_component", operation="test_operation")
        def test_sync_func():
            return "success"
        
        result = test_sync_func()
        assert result == "success"
    
    def test_sync_function_exception(self):
        """测试同步函数异常处理"""
        @handle_exception(component="test_component", operation="test_operation", reraise=False)
        def test_sync_func():
            raise ValueError("测试异常")
        
        result = test_sync_func()
        assert result is None


class TestSystemHealthMonitor:
    """系统健康监控测试类"""
    
    def test_health_status_enum(self):
        """测试健康状态枚举"""
        assert HealthStatus.HEALTHY.value == "healthy"
        assert HealthStatus.WARNING.value == "warning"
        assert HealthStatus.CRITICAL.value == "critical"
        assert HealthStatus.UNKNOWN.value == "unknown"
    
    def test_health_check_result(self):
        """测试健康检查结果"""
        result = HealthCheckResult(
            component="test_component",
            status=HealthStatus.HEALTHY,
            message="组件正常"
        )
        
        assert result.component == "test_component"
        assert result.status == HealthStatus.HEALTHY
        assert result.message == "组件正常"
        assert result.is_healthy() is True
        assert result.needs_attention() is False
        
        # 测试需要关注的状态
        warning_result = HealthCheckResult(
            component="test_component",
            status=HealthStatus.WARNING,
            message="组件警告"
        )
        assert warning_result.needs_attention() is True
    
    def test_system_metrics(self):
        """测试系统指标"""
        metrics = SystemMetrics(
            cpu_usage=75.5,
            memory_usage=80.2,
            disk_usage=65.0,
            process_count=150
        )
        
        assert metrics.cpu_usage == 75.5
        assert metrics.memory_usage == 80.2
        assert metrics.disk_usage == 65.0
        assert metrics.process_count == 150
        
        # 测试转换为字典
        metrics_dict = metrics.to_dict()
        assert metrics_dict["cpu_usage"] == 75.5
        assert metrics_dict["memory_usage"] == 80.2
        assert "timestamp" in metrics_dict
    
    def test_alert_creation(self):
        """测试告警创建"""
        alert = Alert(
            id="test_alert_001",
            level=AlertLevel.WARNING,
            component="test_component",
            message="测试告警"
        )
        
        assert alert.id == "test_alert_001"
        assert alert.level == AlertLevel.WARNING
        assert alert.component == "test_component"
        assert alert.message == "测试告警"
        assert alert.resolved is False
        
        # 测试解决告警
        alert.resolve()
        assert alert.resolved is True
        assert alert.resolved_at is not None


class TestCircuitBreaker:
    """熔断器测试类"""
    
    def test_circuit_breaker_config(self):
        """测试熔断器配置"""
        config = CircuitBreakerConfig(
            failure_threshold=3,
            recovery_timeout=30,
            success_threshold=2,
            timeout=10.0
        )
        
        assert config.failure_threshold == 3
        assert config.recovery_timeout == 30
        assert config.success_threshold == 2
        assert config.timeout == 10.0
    
    @pytest.mark.asyncio
    async def test_circuit_breaker_success(self):
        """测试熔断器成功调用"""
        cb = CircuitBreaker("test_circuit")
        
        async def success_func():
            return "success"
        
        result = await cb.call(success_func)
        assert result == "success"
        assert cb.get_state() == CircuitBreakerState.CLOSED
    
    @pytest.mark.asyncio
    async def test_circuit_breaker_failure_threshold(self):
        """测试熔断器失败阈值"""
        config = CircuitBreakerConfig(failure_threshold=2, recovery_timeout=1)
        cb = CircuitBreaker("test_circuit", config)
        
        async def failing_func():
            raise Exception("测试失败")
        
        # 第一次失败
        with pytest.raises(Exception):
            await cb.call(failing_func)
        assert cb.get_state() == CircuitBreakerState.CLOSED
        
        # 第二次失败，应该触发熔断
        with pytest.raises(Exception):
            await cb.call(failing_func)
        assert cb.get_state() == CircuitBreakerState.OPEN
    
    @pytest.mark.asyncio
    async def test_circuit_breaker_open_exception(self):
        """测试熔断器开启异常"""
        config = CircuitBreakerConfig(failure_threshold=1)
        cb = CircuitBreaker("test_circuit", config)
        
        # 强制开启熔断器
        await cb.force_open()
        
        async def test_func():
            return "success"
        
        # 应该抛出熔断器开启异常
        with pytest.raises(CircuitBreakerOpenException):
            await cb.call(test_func)
    
    @pytest.mark.asyncio
    async def test_circuit_breaker_timeout(self):
        """测试熔断器超时"""
        config = CircuitBreakerConfig(timeout=0.1)  # 100ms超时
        cb = CircuitBreaker("test_circuit", config)
        
        async def slow_func():
            await asyncio.sleep(0.2)  # 200ms延迟
            return "success"
        
        # 应该抛出超时异常
        with pytest.raises(CircuitBreakerTimeoutException):
            await cb.call(slow_func)
    
    def test_circuit_breaker_metrics(self):
        """测试熔断器指标"""
        cb = CircuitBreaker("test_circuit")
        metrics = cb.get_metrics()
        
        assert metrics["name"] == "test_circuit"
        assert metrics["state"] == "closed"
        assert metrics["total_requests"] == 0
        assert metrics["successful_requests"] == 0
        assert metrics["failed_requests"] == 0


class ErrorHandlingTestRunner:
    """错误处理测试运行器"""
    
    def __init__(self):
        self.test_results: Dict[str, Any] = {}
        self.start_time: Optional[datetime] = None
        self.end_time: Optional[datetime] = None
    
    async def run_all_tests(self) -> Dict[str, Any]:
        """运行所有测试"""
        self.start_time = datetime.now()
        logger.info("开始运行错误处理核心测试套件")
        
        test_classes = [
            TestErrorHandlerCore,
            TestErrorHandlerRegistry,
            TestHandleExceptionDecorator,
            TestSystemHealthMonitor,
            TestCircuitBreaker
        ]
        
        total_tests = 0
        passed_tests = 0
        failed_tests = 0
        
        for test_class in test_classes:
            class_name = test_class.__name__
            logger.info(f"运行测试类: {class_name}")
            
            try:
                test_instance = test_class()
                class_results = await self._run_test_class(test_instance)
                
                self.test_results[class_name] = class_results
                total_tests += class_results["total"]
                passed_tests += class_results["passed"]
                failed_tests += class_results["failed"]
                
            except Exception as e:
                logger.error(f"测试类 {class_name} 运行失败: {e}")
                self.test_results[class_name] = {
                    "total": 0,
                    "passed": 0,
                    "failed": 1,
                    "error": str(e)
                }
                failed_tests += 1
        
        self.end_time = datetime.now()
        duration = (self.end_time - self.start_time).total_seconds()
        
        summary = {
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "failed_tests": failed_tests,
            "success_rate": passed_tests / total_tests if total_tests > 0 else 0,
            "duration_seconds": duration,
            "test_results": self.test_results
        }
        
        logger.info(f"错误处理核心测试完成: {passed_tests}/{total_tests} 通过")
        return summary
    
    async def _run_test_class(self, test_instance) -> Dict[str, Any]:
        """运行单个测试类"""
        methods = [method for method in dir(test_instance) if method.startswith('test_')]
        
        total = len(methods)
        passed = 0
        failed = 0
        errors = []
        
        for method_name in methods:
            try:
                method = getattr(test_instance, method_name)
                
                if asyncio.iscoroutinefunction(method):
                    await method()
                else:
                    method()
                
                passed += 1
                logger.debug(f"测试通过: {method_name}")
                
            except Exception as e:
                failed += 1
                error_msg = f"{method_name}: {str(e)}"
                errors.append(error_msg)
                logger.error(f"测试失败: {error_msg}")
        
        return {
            "total": total,
            "passed": passed,
            "failed": failed,
            "errors": errors
        }


# 全局测试运行器实例
global_test_runner = ErrorHandlingTestRunner()