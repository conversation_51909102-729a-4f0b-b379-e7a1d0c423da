package com.crypto.trading.trade.consumer;

import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.retry.RetryCallback;
import org.springframework.retry.RetryContext;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.BiConsumer;

/**
 * 支持重试的消费者基类
 * 
 * <p>提供消息处理重试功能，支持自定义重试策略</p>
 *
 * @param <K> 消息键类型
 * @param <V> 消息值类型
 */
@Component
public class RetryableConsumer<K, V> {

    private static final Logger log = LoggerFactory.getLogger(RetryableConsumer.class);

    @Value("${crypto.trade.consumer.retry.max-attempts:3}")
    private int maxAttempts;

    @Value("${crypto.trade.consumer.retry.initial-interval-ms:1000}")
    private long initialIntervalMs;

    @Value("${crypto.trade.consumer.retry.max-interval-ms:10000}")
    private long maxIntervalMs;

    @Value("${crypto.trade.consumer.retry.multiplier:2.0}")
    private double multiplier;

    private final RetryTemplate retryTemplate;
    private final ConcurrentHashMap<String, Integer> retryCountMap = new ConcurrentHashMap<>();
    private final ScheduledExecutorService scheduledExecutor;

    /**
     * 构造函数
     */
    public RetryableConsumer() {
        this.retryTemplate = createRetryTemplate();
        this.scheduledExecutor = new ScheduledThreadPoolExecutor(1, r -> {
            Thread thread = new Thread(r, "retry-scheduler");
            thread.setDaemon(true);
            return thread;
        });
        
        // 定期清理过期的重试计数
        scheduledExecutor.scheduleAtFixedRate(
            this::cleanupRetryCount, 1, 1, TimeUnit.HOURS);
    }

    /**
     * 使用重试模板处理消息
     *
     * @param record 消息记录
     * @param acknowledgment 确认对象
     * @param handler 消息处理器
     */
    public void consumeWithRetry(ConsumerRecord<K, V> record, Acknowledgment acknowledgment,
                                 BiConsumer<ConsumerRecord<K, V>, Acknowledgment> handler) {
        
        String messageId = getMessageId(record);
        
        try {
            retryTemplate.execute(new RetryCallback<Void, Exception>() {
                @Override
                public Void doWithRetry(RetryContext context) throws Exception {
                    int retryCount = context.getRetryCount();
                    retryCountMap.put(messageId, retryCount);
                    
                    if (retryCount > 0) {
                        log.info("重试处理消息: messageId={}, retryCount={}, topic={}, partition={}, offset={}",
                                messageId, retryCount, record.topic(), record.partition(), record.offset());
                    }
                    
                    try {
                        handler.accept(record, acknowledgment);
                        retryCountMap.remove(messageId);
                        return null;
                    } catch (Exception e) {
                        log.error("处理消息异常，准备重试: messageId={}, retryCount={}, error={}",
                                messageId, retryCount, e.getMessage(), e);
                        throw e;
                    }
                }
            });
        } catch (Exception e) {
            log.error("消息处理失败，已达到最大重试次数: messageId={}, maxAttempts={}, error={}",
                    messageId, maxAttempts, e.getMessage(), e);
            
            // 达到最大重试次数后，确认消息并移除重试计数
            acknowledgment.acknowledge();
            retryCountMap.remove(messageId);
        }
    }

    /**
     * 获取消息ID
     *
     * @param record 消息记录
     * @return 消息ID
     */
    private String getMessageId(ConsumerRecord<K, V> record) {
        // 使用key+topic+partition+offset作为唯一标识
        String key = record.key() != null ? record.key().toString() : "null";
        return key + "-" + record.topic() + "-" + record.partition() + "-" + record.offset();
    }

    /**
     * 清理过期的重试计数
     */
    private void cleanupRetryCount() {
        log.debug("清理过期的重试计数，当前计数条目: {}", retryCountMap.size());
        // 简单实现：直接清空
        retryCountMap.clear();
    }

    /**
     * 创建重试模板
     *
     * @return 重试模板
     */
    private RetryTemplate createRetryTemplate() {
        org.springframework.retry.backoff.ExponentialBackOffPolicy backOffPolicy = 
                new org.springframework.retry.backoff.ExponentialBackOffPolicy();
        backOffPolicy.setInitialInterval(initialIntervalMs);
        backOffPolicy.setMaxInterval(maxIntervalMs);
        backOffPolicy.setMultiplier(multiplier);
        
        org.springframework.retry.policy.SimpleRetryPolicy retryPolicy = 
                new org.springframework.retry.policy.SimpleRetryPolicy(maxAttempts);
        
        RetryTemplate template = new RetryTemplate();
        template.setBackOffPolicy(backOffPolicy);
        template.setRetryPolicy(retryPolicy);
        
        return template;
    }
}