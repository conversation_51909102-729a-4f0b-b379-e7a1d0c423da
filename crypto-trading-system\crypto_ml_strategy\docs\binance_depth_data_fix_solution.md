# 币安深度数据修复方案

## 问题根本原因

通过分析币安官方连接器源码 `binance-futures-connector-java-main`，发现我们的深度数据实现存在以下问题：

### 1. 当前错误实现
```java
// 当前BinanceWebSocketClientImpl.java中的错误实现
String stream = String.format("%s@depth%d@%dms", symbol.toLowerCase(), levels, speed);
// 生成格式：btcusdt@depth10@1000ms （错误格式）
```

### 2. 币安官方正确实现

根据 `WebsocketClientImpl.java` 源码，币安支持两种深度数据流：

#### A. 部分深度数据流 (Partial Depth Stream)
```java
// 官方实现
public int partialDepthStream(String symbol, int levels, int speed, WebSocketCallback onMessageCallback) {
    Request request = null;
    final int defaultSpeed = 250;
    if (speed == defaultSpeed) {
        request = RequestBuilder.buildWebsocketRequest(String.format("%s/ws/%s@depth%s", baseUrl, symbol.toLowerCase(), levels));
        // 格式：btcusdt@depth5
    } else {
        request = RequestBuilder.buildWebsocketRequest(String.format("%s/ws/%s@depth%s@%sms", baseUrl, symbol.toLowerCase(), levels, speed));
        // 格式：btcusdt@depth5@100ms
    }
}
```

#### B. 差分深度数据流 (Diff Depth Stream)
```java
// 官方实现
public int diffDepthStream(String symbol, int speed, WebSocketCallback onMessageCallback) {
    Request request = null;
    final int defaultSpeed = 250;
    if (speed == defaultSpeed) {
        request = RequestBuilder.buildWebsocketRequest(String.format("%s/ws/%s@depth", baseUrl, symbol.toLowerCase(), speed));
        // 格式：btcusdt@depth
    } else {
        request = RequestBuilder.buildWebsocketRequest(String.format("%s/ws/%s@depth@%sms", baseUrl, symbol.toLowerCase(), speed));
        // 格式：btcusdt@depth@100ms
    }
}
```

## 修复方案

### 方案1：使用币安官方方法（推荐）

修改 `BinanceWebSocketClientImpl.java`，直接使用币安官方的方法：

```java
/**
 * 订阅部分深度数据（推荐）
 */
@Override
public int subscribePartialDepth(String symbol, int levels, int speed, Consumer<String> messageHandler) {
    log.info("正在订阅部分深度数据: symbol={}, levels={}, speed={}", symbol, levels, speed);
    try {
        int connectionId = websocketClient.partialDepthStream(symbol.toLowerCase(), levels, speed, adaptCallback(messageHandler));
        activeConnections.put(connectionId, "partialDepth_" + symbol);
        log.info("成功订阅部分深度数据: symbol={}, levels={}, speed={}, connectionId={}", 
                symbol, levels, speed, connectionId);
        return connectionId;
    } catch (Exception e) {
        log.error("订阅部分深度数据失败: symbol={}, levels={}, speed={}", symbol, levels, speed, e);
        throw new RuntimeException("无法订阅部分深度数据", e);
    }
}

/**
 * 订阅差分深度数据
 */
@Override
public int subscribeDiffDepth(String symbol, int speed, Consumer<String> messageHandler) {
    log.info("正在订阅差分深度数据: symbol={}, speed={}", symbol, speed);
    try {
        int connectionId = websocketClient.diffDepthStream(symbol.toLowerCase(), speed, adaptCallback(messageHandler));
        activeConnections.put(connectionId, "diffDepth_" + symbol);
        log.info("成功订阅差分深度数据: symbol={}, speed={}, connectionId={}", 
                symbol, speed, connectionId);
        return connectionId;
    } catch (Exception e) {
        log.error("订阅差分深度数据失败: symbol={}, speed={}", symbol, speed, e);
        throw new RuntimeException("无法订阅差分深度数据", e);
    }
}
```

### 方案2：修复当前实现

如果要保持当前的接口，修复stream格式：

```java
@Override
public int subscribeDepth(String symbol, int levels, int speed, Consumer<String> messageHandler) {
    log.info("正在订阅深度数据: symbol={}, levels={}, speed={}", symbol, levels, speed);
    try {
        // 使用正确的格式
        String stream;
        if (speed == 250) {
            // 默认速度不需要指定
            stream = String.format("%s@depth%d", symbol.toLowerCase(), levels);
        } else {
            // 指定速度
            stream = String.format("%s@depth%d@%dms", symbol.toLowerCase(), levels, speed);
        }
        
        java.util.ArrayList<String> streams = new java.util.ArrayList<>();
        streams.add(stream);
        int connectionId = websocketClient.combineStreams(streams, adaptCallback(messageHandler));
        activeConnections.put(connectionId, "depth_" + symbol);
        log.info("成功订阅深度数据: symbol={}, levels={}, speed={}, stream={}, connectionId={}", 
                symbol, levels, speed, stream, connectionId);
        return connectionId;
    } catch (Exception e) {
        log.error("订阅深度数据失败: symbol={}, levels={}, speed={}", symbol, levels, speed, e);
        throw new RuntimeException("无法订阅深度数据", e);
    }
}
```

## 配置优化

### 1. 支持的参数值

根据币安官方文档：

#### 深度级别 (levels)
- 支持值：5, 10, 20
- 推荐：5（数据量小，处理快）

#### 更新速度 (speed)
- 支持值：100ms, 250ms, 500ms
- 推荐：100ms（实时性好）
- 默认：250ms

### 2. 优化配置

```yaml
market:
  depth:
    enabled: true
    levels: 5              # 改为5级，减少数据量
    speed: 100             # 改为100ms，提高实时性
    topic: depth.data
    type: partial          # 新增：指定使用部分深度数据
```

## 接口修改

### 1. 更新BinanceWebSocketClient接口

```java
public interface BinanceWebSocketClient {
    // 现有方法保持兼容
    int subscribeDepth(String symbol, int levels, int speed, Consumer<String> messageHandler);
    
    // 新增官方方法
    int subscribePartialDepth(String symbol, int levels, int speed, Consumer<String> messageHandler);
    int subscribeDiffDepth(String symbol, int speed, Consumer<String> messageHandler);
}
```

### 2. 更新DepthDataListener

```java
@Component
public class DepthDataListener {
    
    public void startDepthDataSubscription() {
        log.info("开始订阅深度数据，配置：levels={}, speed={}", 
                 marketDataConfig.getDepthLevels(), 
                 marketDataConfig.getDepthSpeed());
        
        for (String symbol : symbols) {
            try {
                Consumer<String> messageHandler = createDepthMessageHandler(symbol);
                
                // 使用官方方法
                int connectionId = webSocketClient.subscribePartialDepth(
                    symbol.toLowerCase(), 
                    marketDataConfig.getDepthLevels(), 
                    marketDataConfig.getDepthSpeed(), 
                    messageHandler
                );
                
                connectionIds.put(symbol, connectionId);
                log.info("深度数据订阅成功：symbol={}, connectionId={}", symbol, connectionId);
            } catch (Exception e) {
                log.error("深度数据订阅失败：symbol={}, error={}", symbol, e.getMessage(), e);
                throw new RuntimeException("深度数据订阅失败：" + symbol, e);
            }
        }
    }
}
```

## 验证步骤

### 1. 立即验证
1. 修改BinanceWebSocketClientImpl使用正确的stream格式
2. 重启系统
3. 检查日志中的深度数据订阅信息
4. 验证t_depth_data表是否开始接收数据

### 2. 测试用例
```java
@Test
public void testPartialDepthStream() {
    // 测试部分深度数据订阅
    String symbol = "BTCUSDT";
    int levels = 5;
    int speed = 100;
    
    int connectionId = webSocketClient.subscribePartialDepth(symbol, levels, speed, message -> {
        log.info("收到深度数据: {}", message);
        // 验证消息格式
        assertThat(message).contains("lastUpdateId");
        assertThat(message).contains("bids");
        assertThat(message).contains("asks");
    });
    
    assertThat(connectionId).isGreaterThan(0);
}
```

## 预期结果

修复后应该看到：
1. 深度数据监听器成功启动日志
2. WebSocket连接建立日志
3. 深度数据接收和处理日志
4. t_depth_data表开始接收数据
5. t_api_request_log表记录API请求

## 时间戳
生成时间：2025-06-21 14:30:00
修复人员：AI Assistant