package com.crypto.trading.common.dto.trade;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;

/**
 * 订单状态数据传输对象
 * <p>
 * 用于在不同服务之间传递订单状态信息
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class OrderStatusDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单ID
     */
    private String orderId;

    /**
     * 客户端订单ID
     */
    private String clientOrderId;

    /**
     * 交易对
     */
    private String symbol;

    /**
     * 订单状态
     */
    private String status;

    /**
     * 已执行数量
     */
    private Double executedQuantity;

    /**
     * 成交价格
     */
    private Double executedPrice;

    /**
     * 错误代码
     */
    private String errorCode;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    private Long updatedTime;

    /**
     * 原始数据
     */
    private String rawData;

    /**
     * 获取订单ID
     *
     * @return 订单ID
     */
    public String getOrderId() {
        return orderId;
    }

    /**
     * 设置订单ID
     *
     * @param orderId 订单ID
     */
    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    /**
     * 获取客户端订单ID
     *
     * @return 客户端订单ID
     */
    public String getClientOrderId() {
        return clientOrderId;
    }

    /**
     * 设置客户端订单ID
     *
     * @param clientOrderId 客户端订单ID
     */
    public void setClientOrderId(String clientOrderId) {
        this.clientOrderId = clientOrderId;
    }

    /**
     * 获取交易对
     *
     * @return 交易对
     */
    public String getSymbol() {
        return symbol;
    }

    /**
     * 设置交易对
     *
     * @param symbol 交易对
     */
    public void setSymbol(String symbol) {
        this.symbol = symbol;
    }

    /**
     * 获取订单状态
     *
     * @return 订单状态
     */
    public String getStatus() {
        return status;
    }

    /**
     * 设置订单状态
     *
     * @param status 订单状态
     */
    public void setStatus(String status) {
        this.status = status;
    }

    /**
     * 获取已执行数量
     *
     * @return 已执行数量
     */
    public Double getExecutedQuantity() {
        return executedQuantity;
    }

    /**
     * 设置已执行数量
     *
     * @param executedQuantity 已执行数量
     */
    public void setExecutedQuantity(Double executedQuantity) {
        this.executedQuantity = executedQuantity;
    }

    /**
     * 获取成交价格
     *
     * @return 成交价格
     */
    public Double getExecutedPrice() {
        return executedPrice;
    }

    /**
     * 设置成交价格
     *
     * @param executedPrice 成交价格
     */
    public void setExecutedPrice(Double executedPrice) {
        this.executedPrice = executedPrice;
    }

    /**
     * 获取错误代码
     *
     * @return 错误代码
     */
    public String getErrorCode() {
        return errorCode;
    }

    /**
     * 设置错误代码
     *
     * @param errorCode 错误代码
     */
    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    /**
     * 获取错误信息
     *
     * @return 错误信息
     */
    public String getErrorMessage() {
        return errorMessage;
    }

    /**
     * 设置错误信息
     *
     * @param errorMessage 错误信息
     */
    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    /**
     * 获取更新时间
     *
     * @return 更新时间
     */
    public Long getUpdatedTime() {
        return updatedTime;
    }

    /**
     * 设置更新时间
     *
     * @param updatedTime 更新时间
     */
    public void setUpdatedTime(Long updatedTime) {
        this.updatedTime = updatedTime;
    }

    /**
     * 获取原始数据
     *
     * @return 原始数据
     */
    public String getRawData() {
        return rawData;
    }

    /**
     * 设置原始数据
     *
     * @param rawData 原始数据
     */
    public void setRawData(String rawData) {
        this.rawData = rawData;
    }
}