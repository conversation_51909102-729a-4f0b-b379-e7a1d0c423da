package com.crypto.trading.sdk.response.model;

import java.math.BigDecimal;

/**
 * K线数据模型
 * 表示一个K线数据
 */
public class KlineData {

    /**
     * 交易对
     */
    private String symbol;

    /**
     * K线间隔
     */
    private String interval;

    /**
     * 开盘时间
     */
    private Long openTime;

    /**
     * 开盘价
     */
    private BigDecimal open;

    /**
     * 最高价
     */
    private BigDecimal high;

    /**
     * 最低价
     */
    private BigDecimal low;

    /**
     * 收盘价
     */
    private BigDecimal close;

    /**
     * 成交量
     */
    private BigDecimal volume;

    /**
     * 收盘时间
     */
    private Long closeTime;

    /**
     * 成交金额
     */
    private BigDecimal quoteAssetVolume;

    /**
     * 成交笔数
     */
    private Long numberOfTrades;

    /**
     * 主动买入成交量
     */
    private BigDecimal takerBuyBaseAssetVolume;

    /**
     * 主动买入成交金额
     */
    private BigDecimal takerBuyQuoteAssetVolume;

    /**
     * 忽略
     */
    private BigDecimal ignore;

    /**
     * 默认构造函数
     */
    public KlineData() {
    }

    /**
     * 从数组创建K线数据
     *
     * @param klineArray K线数据数组
     * @return K线数据对象
     */
    public static KlineData fromArray(Object[] klineArray) {
        if (klineArray == null || klineArray.length < 12) {
            throw new IllegalArgumentException("K线数据数组格式不正确");
        }

        KlineData kline = new KlineData();
        kline.setOpenTime(Long.parseLong(klineArray[0].toString()));
        kline.setOpen(new BigDecimal(klineArray[1].toString()));
        kline.setHigh(new BigDecimal(klineArray[2].toString()));
        kline.setLow(new BigDecimal(klineArray[3].toString()));
        kline.setClose(new BigDecimal(klineArray[4].toString()));
        kline.setVolume(new BigDecimal(klineArray[5].toString()));
        kline.setCloseTime(Long.parseLong(klineArray[6].toString()));
        kline.setQuoteAssetVolume(new BigDecimal(klineArray[7].toString()));
        kline.setNumberOfTrades(Long.parseLong(klineArray[8].toString()));
        kline.setTakerBuyBaseAssetVolume(new BigDecimal(klineArray[9].toString()));
        kline.setTakerBuyQuoteAssetVolume(new BigDecimal(klineArray[10].toString()));
        kline.setIgnore(new BigDecimal(klineArray[11].toString()));
        return kline;
    }

    /**
     * 获取交易对
     *
     * @return 交易对
     */
    public String getSymbol() {
        return symbol;
    }

    /**
     * 设置交易对
     *
     * @param symbol 交易对
     */
    public void setSymbol(String symbol) {
        this.symbol = symbol;
    }

    /**
     * 获取K线间隔
     *
     * @return K线间隔
     */
    public String getInterval() {
        return interval;
    }

    /**
     * 设置K线间隔
     *
     * @param interval K线间隔
     */
    public void setInterval(String interval) {
        this.interval = interval;
    }

    /**
     * 获取开盘时间
     *
     * @return 开盘时间
     */
    public Long getOpenTime() {
        return openTime;
    }

    /**
     * 设置开盘时间
     *
     * @param openTime 开盘时间
     */
    public void setOpenTime(Long openTime) {
        this.openTime = openTime;
    }

    /**
     * 获取开盘价
     *
     * @return 开盘价
     */
    public BigDecimal getOpen() {
        return open;
    }

    /**
     * 设置开盘价
     *
     * @param open 开盘价
     */
    public void setOpen(BigDecimal open) {
        this.open = open;
    }

    /**
     * 获取最高价
     *
     * @return 最高价
     */
    public BigDecimal getHigh() {
        return high;
    }

    /**
     * 设置最高价
     *
     * @param high 最高价
     */
    public void setHigh(BigDecimal high) {
        this.high = high;
    }

    /**
     * 获取最低价
     *
     * @return 最低价
     */
    public BigDecimal getLow() {
        return low;
    }

    /**
     * 设置最低价
     *
     * @param low 最低价
     */
    public void setLow(BigDecimal low) {
        this.low = low;
    }

    /**
     * 获取收盘价
     *
     * @return 收盘价
     */
    public BigDecimal getClose() {
        return close;
    }

    /**
     * 设置收盘价
     *
     * @param close 收盘价
     */
    public void setClose(BigDecimal close) {
        this.close = close;
    }

    /**
     * 获取成交量
     *
     * @return 成交量
     */
    public BigDecimal getVolume() {
        return volume;
    }

    /**
     * 设置成交量
     *
     * @param volume 成交量
     */
    public void setVolume(BigDecimal volume) {
        this.volume = volume;
    }

    /**
     * 获取收盘时间
     *
     * @return 收盘时间
     */
    public Long getCloseTime() {
        return closeTime;
    }

    /**
     * 设置收盘时间
     *
     * @param closeTime 收盘时间
     */
    public void setCloseTime(Long closeTime) {
        this.closeTime = closeTime;
    }

    /**
     * 获取成交金额
     *
     * @return 成交金额
     */
    public BigDecimal getQuoteAssetVolume() {
        return quoteAssetVolume;
    }

    /**
     * 设置成交金额
     *
     * @param quoteAssetVolume 成交金额
     */
    public void setQuoteAssetVolume(BigDecimal quoteAssetVolume) {
        this.quoteAssetVolume = quoteAssetVolume;
    }

    /**
     * 获取成交笔数
     *
     * @return 成交笔数
     */
    public Long getNumberOfTrades() {
        return numberOfTrades;
    }

    /**
     * 设置成交笔数
     *
     * @param numberOfTrades 成交笔数
     */
    public void setNumberOfTrades(Long numberOfTrades) {
        this.numberOfTrades = numberOfTrades;
    }

    /**
     * 获取主动买入成交量
     *
     * @return 主动买入成交量
     */
    public BigDecimal getTakerBuyBaseAssetVolume() {
        return takerBuyBaseAssetVolume;
    }

    /**
     * 设置主动买入成交量
     *
     * @param takerBuyBaseAssetVolume 主动买入成交量
     */
    public void setTakerBuyBaseAssetVolume(BigDecimal takerBuyBaseAssetVolume) {
        this.takerBuyBaseAssetVolume = takerBuyBaseAssetVolume;
    }

    /**
     * 获取主动买入成交金额
     *
     * @return 主动买入成交金额
     */
    public BigDecimal getTakerBuyQuoteAssetVolume() {
        return takerBuyQuoteAssetVolume;
    }

    /**
     * 设置主动买入成交金额
     *
     * @param takerBuyQuoteAssetVolume 主动买入成交金额
     */
    public void setTakerBuyQuoteAssetVolume(BigDecimal takerBuyQuoteAssetVolume) {
        this.takerBuyQuoteAssetVolume = takerBuyQuoteAssetVolume;
    }

    /**
     * 获取忽略字段
     *
     * @return 忽略字段
     */
    public BigDecimal getIgnore() {
        return ignore;
    }

    /**
     * 设置忽略字段
     *
     * @param ignore 忽略字段
     */
    public void setIgnore(BigDecimal ignore) {
        this.ignore = ignore;
    }

    @Override
    public String toString() {
        return "KlineData{" +
                "symbol='" + symbol + '\'' +
                ", interval='" + interval + '\'' +
                ", openTime=" + openTime +
                ", open=" + open +
                ", high=" + high +
                ", low=" + low +
                ", close=" + close +
                ", volume=" + volume +
                ", closeTime=" + closeTime +
                ", quoteAssetVolume=" + quoteAssetVolume +
                ", numberOfTrades=" + numberOfTrades +
                ", takerBuyBaseAssetVolume=" + takerBuyBaseAssetVolume +
                ", takerBuyQuoteAssetVolume=" + takerBuyQuoteAssetVolume +
                ", ignore=" + ignore +
                '}';
    }
} 