package com.crypto.trading.common.dto.trade;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 交易信号传输对象
 * <p>
 * 用于在系统各层之间传递交易信号信息
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class SignalDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 信号ID
     */
    private String id;

    /**
     * 策略ID
     */
    private String strategyId;

    /**
     * 策略名称
     */
    private String strategyName;

    /**
     * 交易对（例如：BTCUSDT）
     */
    private String symbol;

    /**
     * 信号类型
     * <p>
     * BUY: 买入信号
     * SELL: 卖出信号
     * HOLD: 持有信号
     * </p>
     */
    private String signalType;

    /**
     * 信号强度（0-100）
     */
    private Integer strength;

    /**
     * 建议价格
     */
    private BigDecimal price;

    /**
     * 建议数量
     */
    private BigDecimal quantity;

    /**
     * 止损价
     */
    private BigDecimal stopLossPrice;

    /**
     * 止盈价
     */
    private BigDecimal takeProfitPrice;

    /**
     * 信号生成时间
     */
    private LocalDateTime time;

    /**
     * 信号有效期
     */
    private LocalDateTime expiryTime;

    /**
     * 信号描述
     */
    private String description;

    /**
     * 信号参数（JSON格式）
     */
    private String parameters;

    /**
     * 构造函数
     */
    public SignalDTO() {
    }

    /**
     * 构造函数
     *
     * @param id              信号ID
     * @param strategyId      策略ID
     * @param strategyName    策略名称
     * @param symbol          交易对
     * @param signalType      信号类型
     * @param strength        信号强度
     * @param price           建议价格
     * @param quantity        建议数量
     * @param stopLossPrice   止损价
     * @param takeProfitPrice 止盈价
     * @param time            信号生成时间
     * @param expiryTime      信号有效期
     * @param description     信号描述
     * @param parameters      信号参数
     */
    public SignalDTO(String id, String strategyId, String strategyName, String symbol,
                   String signalType, Integer strength, BigDecimal price, BigDecimal quantity,
                   BigDecimal stopLossPrice, BigDecimal takeProfitPrice, LocalDateTime time,
                   LocalDateTime expiryTime, String description, String parameters) {
        this.id = id;
        this.strategyId = strategyId;
        this.strategyName = strategyName;
        this.symbol = symbol;
        this.signalType = signalType;
        this.strength = strength;
        this.price = price;
        this.quantity = quantity;
        this.stopLossPrice = stopLossPrice;
        this.takeProfitPrice = takeProfitPrice;
        this.time = time;
        this.expiryTime = expiryTime;
        this.description = description;
        this.parameters = parameters;
    }

    /**
     * 获取信号ID
     *
     * @return 信号ID
     */
    public String getId() {
        return id;
    }

    /**
     * 设置信号ID
     *
     * @param id 信号ID
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * 获取策略ID
     *
     * @return 策略ID
     */
    public String getStrategyId() {
        return strategyId;
    }

    /**
     * 设置策略ID
     *
     * @param strategyId 策略ID
     */
    public void setStrategyId(String strategyId) {
        this.strategyId = strategyId;
    }

    /**
     * 获取策略名称
     *
     * @return 策略名称
     */
    public String getStrategyName() {
        return strategyName;
    }

    /**
     * 设置策略名称
     *
     * @param strategyName 策略名称
     */
    public void setStrategyName(String strategyName) {
        this.strategyName = strategyName;
    }

    /**
     * 获取交易对
     *
     * @return 交易对
     */
    public String getSymbol() {
        return symbol;
    }

    /**
     * 设置交易对
     *
     * @param symbol 交易对
     */
    public void setSymbol(String symbol) {
        this.symbol = symbol;
    }

    /**
     * 获取信号类型
     *
     * @return 信号类型
     */
    public String getSignalType() {
        return signalType;
    }

    /**
     * 设置信号类型
     *
     * @param signalType 信号类型
     */
    public void setSignalType(String signalType) {
        this.signalType = signalType;
    }

    /**
     * 获取信号强度
     *
     * @return 信号强度
     */
    public Integer getStrength() {
        return strength;
    }

    /**
     * 设置信号强度
     *
     * @param strength 信号强度
     */
    public void setStrength(Integer strength) {
        this.strength = strength;
    }

    /**
     * 获取建议价格
     *
     * @return 建议价格
     */
    public BigDecimal getPrice() {
        return price;
    }

    /**
     * 设置建议价格
     *
     * @param price 建议价格
     */
    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    /**
     * 获取建议数量
     *
     * @return 建议数量
     */
    public BigDecimal getQuantity() {
        return quantity;
    }

    /**
     * 设置建议数量
     *
     * @param quantity 建议数量
     */
    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    /**
     * 获取止损价
     *
     * @return 止损价
     */
    public BigDecimal getStopLossPrice() {
        return stopLossPrice;
    }

    /**
     * 设置止损价
     *
     * @param stopLossPrice 止损价
     */
    public void setStopLossPrice(BigDecimal stopLossPrice) {
        this.stopLossPrice = stopLossPrice;
    }

    /**
     * 获取止盈价
     *
     * @return 止盈价
     */
    public BigDecimal getTakeProfitPrice() {
        return takeProfitPrice;
    }

    /**
     * 设置止盈价
     *
     * @param takeProfitPrice 止盈价
     */
    public void setTakeProfitPrice(BigDecimal takeProfitPrice) {
        this.takeProfitPrice = takeProfitPrice;
    }

    /**
     * 获取信号生成时间
     *
     * @return 信号生成时间
     */
    public LocalDateTime getTime() {
        return time;
    }

    /**
     * 设置信号生成时间
     *
     * @param time 信号生成时间
     */
    public void setTime(LocalDateTime time) {
        this.time = time;
    }

    /**
     * 获取信号有效期
     *
     * @return 信号有效期
     */
    public LocalDateTime getExpiryTime() {
        return expiryTime;
    }

    /**
     * 设置信号有效期
     *
     * @param expiryTime 信号有效期
     */
    public void setExpiryTime(LocalDateTime expiryTime) {
        this.expiryTime = expiryTime;
    }

    /**
     * 获取信号描述
     *
     * @return 信号描述
     */
    public String getDescription() {
        return description;
    }

    /**
     * 设置信号描述
     *
     * @param description 信号描述
     */
    public void setDescription(String description) {
        this.description = description;
    }

    /**
     * 获取信号参数
     *
     * @return 信号参数
     */
    public String getParameters() {
        return parameters;
    }

    /**
     * 设置信号参数
     *
     * @param parameters 信号参数
     */
    public void setParameters(String parameters) {
        this.parameters = parameters;
    }

    @Override
    public String toString() {
        return "SignalDTO{" +
                "id='" + id + '\'' +
                ", strategyId='" + strategyId + '\'' +
                ", strategyName='" + strategyName + '\'' +
                ", symbol='" + symbol + '\'' +
                ", signalType='" + signalType + '\'' +
                ", strength=" + strength +
                ", price=" + price +
                ", quantity=" + quantity +
                ", stopLossPrice=" + stopLossPrice +
                ", takeProfitPrice=" + takeProfitPrice +
                ", time=" + time +
                ", expiryTime=" + expiryTime +
                ", description='" + description + '\'' +
                ", parameters='" + parameters + '\'' +
                '}';
    }
} 