name: <PERSON> stale issues and pull requests

on:
  schedule:
  - cron: "0 0/3 * * *"

jobs:
  stale:

    runs-on: ubuntu-latest

    steps:
    - uses: actions/stale@v3
      with:
        repo-token: ${{ secrets.GITHUB_TOKEN }}
        stale-issue-message: 'This issue is stale because it has been open for three months with no activity. Remove the stale label or comment on the issue otherwise this will be closed in 5 days'
        stale-pr-message: 'This PR is stale because it has been open for a year with no activity. Remove the stale label or comment on the PR otherwise this will be closed in 5 days'
        stale-issue-label: 'stale'
        stale-pr-label: 'stale'
        days-before-stale: 90
        days-before-pr-stale: 365
        days-before-close: 5
        operations-per-run: 100
        exempt-issue-labels: 'bug,enhancement'
        remove-stale-when-updated: true
