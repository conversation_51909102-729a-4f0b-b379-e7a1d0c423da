package com.crypto.trading.common.enums;

import static com.crypto.trading.common.constant.BinanceConstants.*;

/**
 * 币安环境枚举类
 * <p>
 * 定义币安不同环境（主网/测试网）的URL信息
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public enum BinanceEnvironment {
    /**
     * 生产环境（主网）
     */
    PRODUCTION(
            API_BASE_URL_FUTURES_USDT,         // USDT合约REST API URL
            API_BASE_URL_FUTURES_COIN,         // 币本位合约REST API URL
            WS_BASE_URL_FUTURES_USDT,          // USDT合约WebSocket URL
            WS_BASE_URL_FUTURES_COIN           // 币本位合约WebSocket URL
    ),

    /**
     * 测试环境（测试网）
     */
    TESTNET(
            API_BASE_URL_FUTURES_USDT_TESTNET,  // USDT合约测试网REST API URL
            API_BASE_URL_FUTURES_COIN_TESTNET,  // 币本位合约测试网REST API URL
            WS_BASE_URL_FUTURES_USDT_TESTNET,   // USDT合约测试网WebSocket URL
            WS_BASE_URL_FUTURES_COIN_TESTNET    // 币本位合约测试网WebSocket URL
    );

    /**
     * USDT合约API基础URL
     */
    private final String usdtFuturesApiBaseUrl;

    /**
     * 币本位合约API基础URL
     */
    private final String coinFuturesApiBaseUrl;

    /**
     * USDT合约WebSocket基础URL
     */
    private final String usdtFuturesWsBaseUrl;

    /**
     * 币本位合约WebSocket基础URL
     */
    private final String coinFuturesWsBaseUrl;

    /**
     * 构造方法
     *
     * @param usdtFuturesApiBaseUrl USDT合约API基础URL
     * @param coinFuturesApiBaseUrl 币本位合约API基础URL
     * @param usdtFuturesWsBaseUrl  USDT合约WebSocket基础URL
     * @param coinFuturesWsBaseUrl  币本位合约WebSocket基础URL
     */
    BinanceEnvironment(String usdtFuturesApiBaseUrl,
                       String coinFuturesApiBaseUrl,
                       String usdtFuturesWsBaseUrl,
                       String coinFuturesWsBaseUrl) {
        this.usdtFuturesApiBaseUrl = usdtFuturesApiBaseUrl;
        this.coinFuturesApiBaseUrl = coinFuturesApiBaseUrl;
        this.usdtFuturesWsBaseUrl = usdtFuturesWsBaseUrl;
        this.coinFuturesWsBaseUrl = coinFuturesWsBaseUrl;
    }

    /**
     * 根据是否使用测试网获取对应的环境
     *
     * @param useTestnet 是否使用测试网
     * @return 环境枚举
     */
    public static BinanceEnvironment fromUseTestnet(boolean useTestnet) {
        return useTestnet ? TESTNET : PRODUCTION;
    }

    /**
     * 获取USDT合约API基础URL
     *
     * @return USDT合约API基础URL
     */
    public String getUsdtFuturesApiBaseUrl() {
        return usdtFuturesApiBaseUrl;
    }

    /**
     * 获取币本位合约API基础URL
     *
     * @return 币本位合约API基础URL
     */
    public String getCoinFuturesApiBaseUrl() {
        return coinFuturesApiBaseUrl;
    }

    /**
     * 获取USDT合约WebSocket基础URL
     *
     * @return USDT合约WebSocket基础URL
     */
    public String getUsdtFuturesWsBaseUrl() {
        return usdtFuturesWsBaseUrl;
    }

    /**
     * 获取币本位合约WebSocket基础URL
     *
     * @return 币本位合约WebSocket基础URL
     */
    public String getCoinFuturesWsBaseUrl() {
        return coinFuturesWsBaseUrl;
    }
}