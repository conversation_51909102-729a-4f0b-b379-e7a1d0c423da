package com.crypto.trading.sdk.limiter;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.concurrent.TimeUnit;
import java.util.function.Predicate;
import java.util.function.Supplier;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * 重试处理器测试类
 */
@ExtendWith(MockitoExtension.class)
public class RetryWithBackoffHandlerTest {

    private RetryWithBackoffHandler retryHandler;

    @BeforeEach
    public void setUp() {
        // 创建重试处理器
        retryHandler = new RetryWithBackoffHandler();
    }

    @Test
    public void testExecuteWithRetry_Success() {
        // 准备测试数据
        String errorMessage = "测试错误";
        Supplier<String> supplier = () -> "success";
        
        // 执行测试
        String result = retryHandler.executeWithRetry(supplier, errorMessage);
        
        // 验证结果
        assertEquals("success", result, "应该返回成功结果");
    }

    @Test
    public void testExecuteWithRetry_SuccessAfterRetries() {
        // 准备测试数据
        String errorMessage = "测试错误";
        
        // 创建一个会失败两次然后成功的supplier
        Supplier<String> supplier = mock(Supplier.class);
        when(supplier.get())
                .thenThrow(new RuntimeException("rate limit exceeded")) // 使用限流错误，确保会触发重试
                .thenThrow(new RuntimeException("timeout")) // 使用网络错误，确保会触发重试
                .thenReturn("success after retries");
        
        // 执行测试
        String result = retryHandler.executeWithRetry(supplier, errorMessage);
        
        // 验证结果
        assertEquals("success after retries", result, "应该在重试后返回成功结果");
        verify(supplier, times(3)).get();
    }

    @Test
    public void testExecuteWithRetry_MaxRetriesExceeded() {
        // 准备测试数据
        String errorMessage = "测试错误";
            
        // 创建一个总是失败的supplier
        Supplier<String> supplier = mock(Supplier.class);
        RuntimeException exception = new RuntimeException("API rate limit exceeded");
        when(supplier.get()).thenThrow(exception);
        
        // 执行测试，应该抛出异常
        Exception thrown = assertThrows(RuntimeException.class, () -> {
            retryHandler.executeWithRetry(supplier, errorMessage);
        });
        
        // 验证异常和调用次数
        assertSame(exception, thrown, "应该抛出原始异常");
    }

    @Test
    public void testExecuteWithRetry_NonRetryableException() {
        // 准备测试数据
        String errorMessage = "测试错误";
        
        // 创建一个抛出非重试异常的supplier
        Supplier<String> supplier = mock(Supplier.class);
        RuntimeException exception = new RuntimeException("Non-retryable exception");
        when(supplier.get()).thenThrow(exception);
            
        // 执行测试，应该立即抛出异常
        Exception thrown = assertThrows(RuntimeException.class, () -> {
            retryHandler.executeWithRetry(supplier, errorMessage);
        });
        
        // 验证异常和调用次数
        assertSame(exception, thrown, "应该抛出原始异常");
        verify(supplier, times(1)).get();
    }
}