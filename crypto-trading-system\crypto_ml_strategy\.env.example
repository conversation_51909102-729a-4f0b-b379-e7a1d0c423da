# Crypto ML Strategy Environment Variables Template
# 复制此文件为 .env 并填入实际的配置值

# DeepSeek API Configuration
DEEPSEEK_API_KEY=your_deepseek_api_key_here
DEEPSEEK_API_BASE_URL=https://api.deepseek.com/v1
DEEPSEEK_MODEL_NAME=deepseek-chat

# Database Configuration
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=root
MYSQL_PASSWORD=your_mysql_password
MYSQL_DATABASE=crypto_trading

INFLUXDB_URL=http://localhost:8086
INFLUXDB_TOKEN=your_influxdb_token_here
INFLUXDB_ORG=crypto
INFLUXDB_BUCKET=market_data

# Kafka Configuration
KAFKA_BOOTSTRAP_SERVERS=localhost:29092
KAFKA_GROUP_ID=crypto_ml_strategy

# Security Configuration
JWT_SECRET_KEY=your_jwt_secret_key_here
ENCRYPTION_KEY=your_encryption_key_here

# Model Storage Configuration
MODEL_STORAGE_PATH=models/
DEEPSEEK_MODEL_PATH=models/deepseek_distilled.pkl

# Performance Configuration
MAX_WORKERS=8
MEMORY_LIMIT_MB=4096
GPU_ENABLED=true

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=logs/crypto_ml_strategy.log

# Development/Production Environment
ENVIRONMENT=development
DEBUG=false