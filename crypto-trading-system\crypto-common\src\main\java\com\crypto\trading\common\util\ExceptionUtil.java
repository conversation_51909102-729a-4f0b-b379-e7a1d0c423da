package com.crypto.trading.common.util;

import com.crypto.trading.common.exception.BusinessException;
import com.crypto.trading.common.exception.ErrorCode;
import com.crypto.trading.common.exception.SystemException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 异常工具类
 * <p>
 * 提供异常处理的公共方法，如异常信息提取、异常转换等
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public final class ExceptionUtil {

    private static final Logger log = LoggerFactory.getLogger(ExceptionUtil.class);

    /**
     * 私有构造函数，防止实例化
     */
    private ExceptionUtil() {
        throw new IllegalStateException("工具类不允许实例化");
    }

    /**
     * 获取异常堆栈信息
     *
     * @param e 异常
     * @return 异常堆栈信息
     */
    public static String getStackTrace(Throwable e) {
        StringBuilder sb = new StringBuilder();
        sb.append(e.toString()).append("\n");
        for (StackTraceElement element : e.getStackTrace()) {
            sb.append("\tat ").append(element).append("\n");
        }
        return sb.toString();
    }

    /**
     * 获取异常根因
     *
     * @param e 异常
     * @return 异常根因
     */
    public static Throwable getRootCause(Throwable e) {
        Throwable cause = e.getCause();
        if (cause == null) {
            return e;
        }
        return getRootCause(cause);
    }

    /**
     * 获取异常根因消息
     *
     * @param e 异常
     * @return 异常根因消息
     */
    public static String getRootCauseMessage(Throwable e) {
        Throwable rootCause = getRootCause(e);
        return rootCause.getMessage();
    }

    /**
     * 抛出业务异常
     *
     * @param errorCode 错误码枚举
     */
    public static void throwBusinessException(ErrorCode errorCode) {
        throw new BusinessException(String.valueOf(errorCode.getCode()), errorCode.getMessage());
    }

    /**
     * 抛出业务异常
     *
     * @param errorCode 错误码枚举
     * @param message   错误消息
     */
    public static void throwBusinessException(ErrorCode errorCode, String message) {
        throw new BusinessException(String.valueOf(errorCode.getCode()), message);
    }

    /**
     * 抛出系统异常
     *
     * @param errorCode 错误码枚举
     */
    public static void throwSystemException(ErrorCode errorCode) {
        throw new SystemException(errorCode);
    }

    /**
     * 抛出系统异常
     *
     * @param errorCode 错误码枚举
     * @param message   错误消息
     */
    public static void throwSystemException(ErrorCode errorCode, String message) {
        throw new SystemException(errorCode, message);
    }

    /**
     * 抛出系统异常
     *
     * @param errorCode 错误码枚举
     * @param cause     异常原因
     */
    public static void throwSystemException(ErrorCode errorCode, Throwable cause) {
        throw new SystemException(errorCode, cause);
    }

    /**
     * 抛出系统异常
     *
     * @param errorCode 错误码枚举
     * @param message   错误消息
     * @param cause     异常原因
     */
    public static void throwSystemException(ErrorCode errorCode, String message, Throwable cause) {
        throw new SystemException(errorCode, message, cause);
    }

    /**
     * 判断是否为业务异常
     *
     * @param e 异常
     * @return 是否为业务异常
     */
    public static boolean isBusinessException(Throwable e) {
        return e instanceof BusinessException;
    }

    /**
     * 判断是否为系统异常
     *
     * @param e 异常
     * @return 是否为系统异常
     */
    public static boolean isSystemException(Throwable e) {
        return e instanceof SystemException;
    }

    /**
     * 记录异常日志
     *
     * @param e 异常
     */
    public static void logException(Throwable e) {
        if (isBusinessException(e)) {
            log.warn("业务异常: {}", e.getMessage());
        } else if (isSystemException(e)) {
            log.error("系统异常: {}", e.getMessage(), e);
        } else {
            log.error("未知异常: {}", e.getMessage(), e);
        }
    }
}