#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Kafka兼容性测试模块

该模块提供Kafka消息格式和通信兼容性测试功能，确保Python ML组件
与Java模块之间的Kafka通信完全兼容。
"""

import json
import time
import asyncio
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass
from datetime import datetime, timezone
import uuid
from concurrent.futures import ThreadPoolExecutor
from loguru import logger

from .config import Config
from .kafka_client import KafkaClient
from .java_api_core import ValidationResult, PerformanceMetrics


@dataclass
class KafkaTestConfig:
    """Kafka测试配置"""
    bootstrap_servers: str
    test_topics: List[str]
    test_duration_seconds: int = 60
    message_rate_per_second: int = 10
    consumer_timeout_ms: int = 5000


class KafkaCompatibilityTester:
    """
    Kafka兼容性测试器
    
    测试Kafka消息格式和通信兼容性，确保与Java模块的完全兼容。
    """
    
    def __init__(self, config: Config):
        """
        初始化Kafka兼容性测试器
        
        Args:
            config: 配置对象
        """
        self.config = config
        self.kafka_client = KafkaClient(config)
        self.test_results: List[ValidationResult] = []
        self.performance_metrics: List[PerformanceMetrics] = []
        
        # 测试配置
        self.test_config = KafkaTestConfig(
            bootstrap_servers=config.get('kafka', 'bootstrap_servers'),
            test_topics=[
                config.get('kafka', 'kline_topic'),
                config.get('kafka', 'depth_topic'),
                config.get('kafka', 'trade_topic'),
                config.get('kafka', 'signal_topic')
            ]
        )
        
        logger.info("Kafka兼容性测试器初始化完成")
    
    async def test_topic_compatibility(self) -> List[ValidationResult]:
        """
        测试主题兼容性
        
        Returns:
            测试结果列表
        """
        logger.info("开始Kafka主题兼容性测试")
        results = []
        
        for topic in self.test_config.test_topics:
            result = await self._test_single_topic(topic)
            results.append(result)
            self.test_results.append(result)
        
        logger.info(f"主题兼容性测试完成，共测试{len(results)}个主题")
        return results
    
    async def _test_single_topic(self, topic: str) -> ValidationResult:
        """
        测试单个主题
        
        Args:
            topic: 主题名称
            
        Returns:
            验证结果
        """
        start_time = time.time()
        
        try:
            # 测试生产者
            test_message = self._create_test_message(topic)
            
            # 发送测试消息
            self.kafka_client.producer.produce(
                topic,
                value=json.dumps(test_message).encode('utf-8'),
                key=str(uuid.uuid4()).encode('utf-8')
            )
            self.kafka_client.producer.flush()
            
            # 测试消费者
            messages_received = []
            
            def message_handler(message):
                messages_received.append(message)
            
            # 启动消费者
            self.kafka_client.start_consuming(topic, message_handler)
            
            # 等待消息接收
            await asyncio.sleep(2)
            
            # 停止消费者
            self.kafka_client.stop_consuming(topic)
            
            if messages_received:
                return ValidationResult(
                    test_name=f"topic_compatibility_{topic}",
                    success=True,
                    message=f"主题{topic}兼容性测试通过",
                    details={
                        "topic": topic,
                        "messages_sent": 1,
                        "messages_received": len(messages_received)
                    },
                    execution_time_ms=(time.time() - start_time) * 1000,
                    timestamp=datetime.now(timezone.utc)
                )
            else:
                return ValidationResult(
                    test_name=f"topic_compatibility_{topic}",
                    success=False,
                    message=f"主题{topic}未收到消息",
                    details={"topic": topic, "messages_received": 0},
                    execution_time_ms=(time.time() - start_time) * 1000,
                    timestamp=datetime.now(timezone.utc)
                )
                
        except Exception as e:
            logger.error(f"主题{topic}测试异常: {e}")
            return ValidationResult(
                test_name=f"topic_compatibility_{topic}",
                success=False,
                message=f"测试异常: {str(e)}",
                details={"topic": topic, "exception": str(e)},
                execution_time_ms=(time.time() - start_time) * 1000,
                timestamp=datetime.now(timezone.utc)
            )
    
    def _create_test_message(self, topic: str) -> Dict[str, Any]:
        """
        创建测试消息
        
        Args:
            topic: 主题名称
            
        Returns:
            测试消息
        """
        base_message = {
            "messageId": str(uuid.uuid4()),
            "messageType": self._get_message_type_for_topic(topic),
            "timestamp": int(time.time() * 1000)
        }
        
        if 'kline' in topic:
            base_message["data"] = {
                "symbol": "BTCUSDT",
                "interval": "1m",
                "open": 50000.0,
                "high": 50100.0,
                "low": 49900.0,
                "close": 50050.0,
                "volume": 1000.0,
                "openTime": int(time.time() * 1000),
                "closeTime": int(time.time() * 1000) + 60000
            }
        elif 'signal' in topic:
            base_message["data"] = {
                "strategyId": "test-strategy",
                "symbol": "BTCUSDT",
                "signalType": "BUY",
                "signalStrength": 0.8,
                "timeFrame": "1h",
                "riskAssessment": {
                    "overallRisk": 0.3
                },
                "parameters": {
                    "confidence": 0.9
                }
            }
        else:
            base_message["data"] = {"test": True}
        
        return base_message
    
    def _get_message_type_for_topic(self, topic: str) -> str:
        """
        根据主题获取消息类型
        
        Args:
            topic: 主题名称
            
        Returns:
            消息类型
        """
        if 'kline' in topic:
            return 'kline'
        elif 'depth' in topic:
            return 'depth'
        elif 'trade' in topic:
            return 'trade'
        elif 'signal' in topic:
            return 'signal'
        else:
            return 'unknown'


class MessageSchemaValidator:
    """
    消息模式验证器
    
    验证消息是否符合预定义的模式。
    """
    
    def __init__(self):
        """初始化消息模式验证器"""
        self.schemas = self._load_message_schemas()
        logger.info("消息模式验证器初始化完成")
    
    def _load_message_schemas(self) -> Dict[str, Dict[str, Any]]:
        """
        加载消息模式定义
        
        Returns:
            消息模式字典
        """
        return {
            'kline': {
                'type': 'object',
                'required': ['messageId', 'messageType', 'timestamp', 'data'],
                'properties': {
                    'messageId': {'type': 'string'},
                    'messageType': {'type': 'string', 'enum': ['kline']},
                    'timestamp': {'type': 'integer'},
                    'data': {
                        'type': 'object',
                        'required': ['symbol', 'interval', 'open', 'high', 'low', 'close', 'volume'],
                        'properties': {
                            'symbol': {'type': 'string'},
                            'interval': {'type': 'string'},
                            'open': {'type': 'number'},
                            'high': {'type': 'number'},
                            'low': {'type': 'number'},
                            'close': {'type': 'number'},
                            'volume': {'type': 'number'}
                        }
                    }
                }
            },
            'signal': {
                'type': 'object',
                'required': ['messageId', 'messageType', 'timestamp', 'data'],
                'properties': {
                    'messageId': {'type': 'string'},
                    'messageType': {'type': 'string', 'enum': ['signal']},
                    'timestamp': {'type': 'integer'},
                    'data': {
                        'type': 'object',
                        'required': ['strategyId', 'symbol', 'signalType', 'signalStrength'],
                        'properties': {
                            'strategyId': {'type': 'string'},
                            'symbol': {'type': 'string'},
                            'signalType': {'type': 'string', 'enum': ['BUY', 'SELL', 'HOLD']},
                            'signalStrength': {'type': 'number', 'minimum': 0, 'maximum': 1}
                        }
                    }
                }
            }
        }
    
    def validate_message_schema(self, message: Dict[str, Any], 
                               message_type: str) -> ValidationResult:
        """
        验证消息模式
        
        Args:
            message: 待验证消息
            message_type: 消息类型
            
        Returns:
            验证结果
        """
        start_time = time.time()
        
        try:
            schema = self.schemas.get(message_type)
            if not schema:
                return ValidationResult(
                    test_name=f"schema_validation_{message_type}",
                    success=False,
                    message=f"未知消息类型: {message_type}",
                    details={"message_type": message_type},
                    execution_time_ms=(time.time() - start_time) * 1000,
                    timestamp=datetime.now(timezone.utc)
                )
            
            # 验证必需字段
            required_fields = schema.get('required', [])
            missing_fields = [field for field in required_fields 
                            if field not in message]
            
            if missing_fields:
                return ValidationResult(
                    test_name=f"schema_validation_{message_type}",
                    success=False,
                    message=f"缺少必需字段: {missing_fields}",
                    details={"missing_fields": missing_fields},
                    execution_time_ms=(time.time() - start_time) * 1000,
                    timestamp=datetime.now(timezone.utc)
                )
            
            # 验证数据字段
            if 'data' in message and 'data' in schema['properties']:
                data_schema = schema['properties']['data']
                data_validation = self._validate_data_schema(
                    message['data'], data_schema
                )
                if not data_validation:
                    return ValidationResult(
                        test_name=f"schema_validation_{message_type}",
                        success=False,
                        message="数据字段验证失败",
                        details={"data_validation": False},
                        execution_time_ms=(time.time() - start_time) * 1000,
                        timestamp=datetime.now(timezone.utc)
                    )
            
            return ValidationResult(
                test_name=f"schema_validation_{message_type}",
                success=True,
                message=f"{message_type}消息模式验证通过",
                details={"message_type": message_type},
                execution_time_ms=(time.time() - start_time) * 1000,
                timestamp=datetime.now(timezone.utc)
            )
            
        except Exception as e:
            logger.error(f"消息模式验证异常: {e}")
            return ValidationResult(
                test_name=f"schema_validation_{message_type}",
                success=False,
                message=f"验证异常: {str(e)}",
                details={"exception": str(e)},
                execution_time_ms=(time.time() - start_time) * 1000,
                timestamp=datetime.now(timezone.utc)
            )
    
    def _validate_data_schema(self, data: Dict[str, Any], 
                             schema: Dict[str, Any]) -> bool:
        """
        验证数据模式
        
        Args:
            data: 数据
            schema: 数据模式
            
        Returns:
            是否验证通过
        """
        try:
            # 验证必需字段
            required_fields = schema.get('required', [])
            for field in required_fields:
                if field not in data:
                    return False
            
            # 验证字段类型
            properties = schema.get('properties', {})
            for field, field_schema in properties.items():
                if field in data:
                    if not self._validate_field_value(data[field], field_schema):
                        return False
            
            return True
            
        except Exception as e:
            logger.error(f"数据模式验证异常: {e}")
            return False
    
    def _validate_field_value(self, value: Any, field_schema: Dict[str, Any]) -> bool:
        """
        验证字段值
        
        Args:
            value: 字段值
            field_schema: 字段模式
            
        Returns:
            是否验证通过
        """
        field_type = field_schema.get('type')
        
        # 类型验证
        if field_type == 'string' and not isinstance(value, str):
            return False
        elif field_type == 'number' and not isinstance(value, (int, float)):
            return False
        elif field_type == 'integer' and not isinstance(value, int):
            return False
        elif field_type == 'object' and not isinstance(value, dict):
            return False
        elif field_type == 'array' and not isinstance(value, list):
            return False
        
        # 枚举验证
        enum_values = field_schema.get('enum')
        if enum_values and value not in enum_values:
            return False
        
        # 数值范围验证
        if field_type == 'number':
            minimum = field_schema.get('minimum')
            maximum = field_schema.get('maximum')
            if minimum is not None and value < minimum:
                return False
            if maximum is not None and value > maximum:
                return False
        
        return True


class ProducerConsumerTester:
    """
    生产者消费者兼容性测试器
    
    测试Kafka生产者和消费者的兼容性。
    """
    
    def __init__(self, kafka_client: KafkaClient):
        """
        初始化生产者消费者测试器
        
        Args:
            kafka_client: Kafka客户端
        """
        self.kafka_client = kafka_client
        logger.info("生产者消费者测试器初始化完成")
    
    async def test_producer_consumer_compatibility(self, 
                                                  topic: str,
                                                  message_count: int = 10) -> ValidationResult:
        """
        测试生产者消费者兼容性
        
        Args:
            topic: 测试主题
            message_count: 消息数量
            
        Returns:
            验证结果
        """
        start_time = time.time()
        
        try:
            messages_sent = []
            messages_received = []
            
            # 消息处理器
            def message_handler(message):
                messages_received.append(message)
            
            # 启动消费者
            self.kafka_client.start_consuming(topic, message_handler)
            
            # 发送测试消息
            for i in range(message_count):
                test_message = {
                    "messageId": str(uuid.uuid4()),
                    "messageType": "test",
                    "timestamp": int(time.time() * 1000),
                    "data": {"index": i, "test": True}
                }
                
                self.kafka_client.producer.produce(
                    topic,
                    value=json.dumps(test_message).encode('utf-8'),
                    key=f"test-{i}".encode('utf-8')
                )
                messages_sent.append(test_message)
            
            self.kafka_client.producer.flush()
            
            # 等待消息接收
            await asyncio.sleep(3)
            
            # 停止消费者
            self.kafka_client.stop_consuming(topic)
            
            # 验证结果
            success = len(messages_received) == message_count
            
            return ValidationResult(
                test_name=f"producer_consumer_compatibility_{topic}",
                success=success,
                message=f"生产者消费者兼容性测试{'通过' if success else '失败'}",
                details={
                    "topic": topic,
                    "messages_sent": len(messages_sent),
                    "messages_received": len(messages_received),
                    "success_rate": len(messages_received) / len(messages_sent) if messages_sent else 0
                },
                execution_time_ms=(time.time() - start_time) * 1000,
                timestamp=datetime.now(timezone.utc)
            )
            
        except Exception as e:
            logger.error(f"生产者消费者兼容性测试异常: {e}")
            return ValidationResult(
                test_name=f"producer_consumer_compatibility_{topic}",
                success=False,
                message=f"测试异常: {str(e)}",
                details={"exception": str(e)},
                execution_time_ms=(time.time() - start_time) * 1000,
                timestamp=datetime.now(timezone.utc)
            )


if __name__ == "__main__":
    # 测试代码
    import asyncio
    from .config import Config
    
    async def main():
        config = Config()
        tester = KafkaCompatibilityTester(config)
        
        # 测试主题兼容性
        results = await tester.test_topic_compatibility()
        
        for result in results:
            logger.info(f"测试结果: {result.test_name} - {result.success}")
    
    asyncio.run(main())