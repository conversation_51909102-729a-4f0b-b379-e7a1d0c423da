package com.crypto.trading.common.util;

import com.crypto.trading.common.constant.SystemConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Duration;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * HTTP工具类
 * <p>
 * 提供HTTP请求相关的工具方法，如GET、POST等请求方式
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public final class HttpUtil {
    private static final Logger log = LoggerFactory.getLogger(HttpUtil.class);

    /**
     * 默认连接超时时间（秒）
     */
    private static final int DEFAULT_CONNECT_TIMEOUT = 10;

    /**
     * 默认请求超时时间（秒）
     */
    private static final int DEFAULT_REQUEST_TIMEOUT = 30;

    /**
     * 默认HTTP客户端
     */
    private static final HttpClient HTTP_CLIENT = HttpClient.newBuilder()
            .connectTimeout(Duration.ofSeconds(DEFAULT_CONNECT_TIMEOUT))
            .executor(Executors.newVirtualThreadPerTaskExecutor())
            .build();

    /**
     * 私有构造函数，防止实例化
     */
    private HttpUtil() {
        throw new IllegalStateException("工具类不允许实例化");
    }

    /**
     * 发送GET请求（同步）
     *
     * @param url 请求URL
     * @return 响应内容
     */
    public static String get(String url) {
        return get(url, null);
    }

    /**
     * 发送GET请求（同步）
     *
     * @param url     请求URL
     * @param headers 请求头
     * @return 响应内容
     */
    public static String get(String url, Map<String, String> headers) {
        try {
            HttpRequest.Builder requestBuilder = HttpRequest.newBuilder()
                    .uri(URI.create(url))
                    .timeout(Duration.ofSeconds(DEFAULT_REQUEST_TIMEOUT))
                    .GET();

            if (headers != null && !headers.isEmpty()) {
                headers.forEach(requestBuilder::header);
            }

            HttpRequest request = requestBuilder.build();
            HttpResponse<String> response = HTTP_CLIENT.send(request, HttpResponse.BodyHandlers.ofString());
            return handleResponse(response);
        } catch (Exception e) {
            log.error("发送GET请求失败: {}", url, e);
            throw new RuntimeException("发送GET请求失败", e);
        }
    }

    /**
     * 发送GET请求（异步）
     *
     * @param url 请求URL
     * @return 异步响应结果
     */
    public static CompletableFuture<String> getAsync(String url) {
        return getAsync(url, null);
    }

    /**
     * 发送GET请求（异步）
     *
     * @param url     请求URL
     * @param headers 请求头
     * @return 异步响应结果
     */
    public static CompletableFuture<String> getAsync(String url, Map<String, String> headers) {
        HttpRequest.Builder requestBuilder = HttpRequest.newBuilder()
                .uri(URI.create(url))
                .timeout(Duration.ofSeconds(DEFAULT_REQUEST_TIMEOUT))
                .GET();

        if (headers != null && !headers.isEmpty()) {
            headers.forEach(requestBuilder::header);
        }

        HttpRequest request = requestBuilder.build();
        return HTTP_CLIENT.sendAsync(request, HttpResponse.BodyHandlers.ofString())
                .thenApply(HttpUtil::handleResponse)
                .exceptionally(e -> {
                    log.error("异步发送GET请求失败: {}", url, e);
                    throw new RuntimeException("异步发送GET请求失败", e);
                });
    }

    /**
     * 发送POST请求（同步）
     *
     * @param url  请求URL
     * @param body 请求体
     * @return 响应内容
     */
    public static String post(String url, String body) {
        return post(url, body, null);
    }

    /**
     * 发送POST请求（同步）
     *
     * @param url     请求URL
     * @param body    请求体
     * @param headers 请求头
     * @return 响应内容
     */
    public static String post(String url, String body, Map<String, String> headers) {
        try {
            HttpRequest.Builder requestBuilder = HttpRequest.newBuilder()
                    .uri(URI.create(url))
                    .timeout(Duration.ofSeconds(DEFAULT_REQUEST_TIMEOUT))
                    .POST(HttpRequest.BodyPublishers.ofString(body));

            if (headers != null && !headers.isEmpty()) {
                headers.forEach(requestBuilder::header);
            }

            HttpRequest request = requestBuilder.build();
            HttpResponse<String> response = HTTP_CLIENT.send(request, HttpResponse.BodyHandlers.ofString());
            return handleResponse(response);
        } catch (Exception e) {
            log.error("发送POST请求失败: {}", url, e);
            throw new RuntimeException("发送POST请求失败", e);
        }
    }

    /**
     * 发送POST请求（异步）
     *
     * @param url  请求URL
     * @param body 请求体
     * @return 异步响应结果
     */
    public static CompletableFuture<String> postAsync(String url, String body) {
        return postAsync(url, body, null);
    }

    /**
     * 发送POST请求（异步）
     *
     * @param url     请求URL
     * @param body    请求体
     * @param headers 请求头
     * @return 异步响应结果
     */
    public static CompletableFuture<String> postAsync(String url, String body, Map<String, String> headers) {
        HttpRequest.Builder requestBuilder = HttpRequest.newBuilder()
                .uri(URI.create(url))
                .timeout(Duration.ofSeconds(DEFAULT_REQUEST_TIMEOUT))
                .POST(HttpRequest.BodyPublishers.ofString(body));

        if (headers != null && !headers.isEmpty()) {
            headers.forEach(requestBuilder::header);
        }

        HttpRequest request = requestBuilder.build();
        return HTTP_CLIENT.sendAsync(request, HttpResponse.BodyHandlers.ofString())
                .thenApply(HttpUtil::handleResponse)
                .exceptionally(e -> {
                    log.error("异步发送POST请求失败: {}", url, e);
                    throw new RuntimeException("异步发送POST请求失败", e);
                });
    }

    /**
     * 发送表单POST请求（同步）
     *
     * @param url    请求URL
     * @param params 表单参数
     * @return 响应内容
     */
    public static String postForm(String url, Map<String, String> params) {
        return postForm(url, params, null);
    }

    /**
     * 发送表单POST请求（同步）
     *
     * @param url     请求URL
     * @param params  表单参数
     * @param headers 请求头
     * @return 响应内容
     */
    public static String postForm(String url, Map<String, String> params, Map<String, String> headers) {
        try {
            String formData = params.entrySet()
                    .stream()
                    .map(entry -> entry.getKey() + "=" + entry.getValue())
                    .collect(Collectors.joining("&"));

            HttpRequest.Builder requestBuilder = HttpRequest.newBuilder()
                    .uri(URI.create(url))
                    .timeout(Duration.ofSeconds(DEFAULT_REQUEST_TIMEOUT))
                    .header("Content-Type", "application/x-www-form-urlencoded")
                    .POST(HttpRequest.BodyPublishers.ofString(formData));

            if (headers != null && !headers.isEmpty()) {
                headers.forEach(requestBuilder::header);
            }

            HttpRequest request = requestBuilder.build();
            HttpResponse<String> response = HTTP_CLIENT.send(request, HttpResponse.BodyHandlers.ofString());
            return handleResponse(response);
        } catch (Exception e) {
            log.error("发送表单POST请求失败: {}", url, e);
            throw new RuntimeException("发送表单POST请求失败", e);
        }
    }

    /**
     * 发送表单POST请求（异步）
     *
     * @param url    请求URL
     * @param params 表单参数
     * @return 异步响应结果
     */
    public static CompletableFuture<String> postFormAsync(String url, Map<String, String> params) {
        return postFormAsync(url, params, null);
    }

    /**
     * 发送表单POST请求（异步）
     *
     * @param url     请求URL
     * @param params  表单参数
     * @param headers 请求头
     * @return 异步响应结果
     */
    public static CompletableFuture<String> postFormAsync(String url, Map<String, String> params, Map<String, String> headers) {
        String formData = params.entrySet()
                .stream()
                .map(entry -> entry.getKey() + "=" + entry.getValue())
                .collect(Collectors.joining("&"));

        HttpRequest.Builder requestBuilder = HttpRequest.newBuilder()
                .uri(URI.create(url))
                .timeout(Duration.ofSeconds(DEFAULT_REQUEST_TIMEOUT))
                .header("Content-Type", "application/x-www-form-urlencoded")
                .POST(HttpRequest.BodyPublishers.ofString(formData));

        if (headers != null && !headers.isEmpty()) {
            headers.forEach(requestBuilder::header);
        }

        HttpRequest request = requestBuilder.build();
        return HTTP_CLIENT.sendAsync(request, HttpResponse.BodyHandlers.ofString())
                .thenApply(HttpUtil::handleResponse)
                .exceptionally(e -> {
                    log.error("异步发送表单POST请求失败: {}", url, e);
                    throw new RuntimeException("异步发送表单POST请求失败", e);
                });
    }

    /**
     * 发送PUT请求（同步）
     *
     * @param url  请求URL
     * @param body 请求体
     * @return 响应内容
     */
    public static String put(String url, String body) {
        return put(url, body, null);
    }

    /**
     * 发送PUT请求（同步）
     *
     * @param url     请求URL
     * @param body    请求体
     * @param headers 请求头
     * @return 响应内容
     */
    public static String put(String url, String body, Map<String, String> headers) {
        try {
            HttpRequest.Builder requestBuilder = HttpRequest.newBuilder()
                    .uri(URI.create(url))
                    .timeout(Duration.ofSeconds(DEFAULT_REQUEST_TIMEOUT))
                    .PUT(HttpRequest.BodyPublishers.ofString(body));

            if (headers != null && !headers.isEmpty()) {
                headers.forEach(requestBuilder::header);
            }

            HttpRequest request = requestBuilder.build();
            HttpResponse<String> response = HTTP_CLIENT.send(request, HttpResponse.BodyHandlers.ofString());
            return handleResponse(response);
        } catch (Exception e) {
            log.error("发送PUT请求失败: {}", url, e);
            throw new RuntimeException("发送PUT请求失败", e);
        }
    }

    /**
     * 发送DELETE请求（同步）
     *
     * @param url 请求URL
     * @return 响应内容
     */
    public static String delete(String url) {
        return delete(url, null);
    }

    /**
     * 发送DELETE请求（同步）
     *
     * @param url     请求URL
     * @param headers 请求头
     * @return 响应内容
     */
    public static String delete(String url, Map<String, String> headers) {
        try {
            HttpRequest.Builder requestBuilder = HttpRequest.newBuilder()
                    .uri(URI.create(url))
                    .timeout(Duration.ofSeconds(DEFAULT_REQUEST_TIMEOUT))
                    .DELETE();

            if (headers != null && !headers.isEmpty()) {
                headers.forEach(requestBuilder::header);
            }

            HttpRequest request = requestBuilder.build();
            HttpResponse<String> response = HTTP_CLIENT.send(request, HttpResponse.BodyHandlers.ofString());
            return handleResponse(response);
        } catch (Exception e) {
            log.error("发送DELETE请求失败: {}", url, e);
            throw new RuntimeException("发送DELETE请求失败", e);
        }
    }

    /**
     * 处理HTTP响应
     *
     * @param response HTTP响应
     * @return 响应内容
     */
    private static String handleResponse(HttpResponse<String> response) {
        int statusCode = response.statusCode();
        if (statusCode >= 200 && statusCode < 300) {
            return response.body();
        } else {
            String errorMsg = String.format("HTTP请求失败，状态码: %d，响应内容: %s", statusCode, response.body());
            log.error(errorMsg);
            throw new RuntimeException(errorMsg);
        }
    }
} 