<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (21) on Fri Apr 04 16:06:08 CST 2025 -->
<title>PortfolioMargin (io.github.binance:binance-futures-connector-java 3.0.5 API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-04-04">
<meta name="description" content="declaration: package: com.binance.connector.futures.client.impl.futures, class: PortfolioMargin">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="切换导航链接"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="../../../../../../../index.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="nav-bar-cell1-rev">类</li>
<li><a href="class-use/PortfolioMargin.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../../../index-all.html">索引</a></li>
<li><a href="../../../../../../../help-doc.html#class">帮助</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>概要:</p>
<ul>
<li>嵌套</li>
<li>字段</li>
<li><a href="#constructor-summary">构造器</a></li>
<li><a href="#method-summary">方法</a></li>
</ul>
</li>
<li>
<p>详细资料:</p>
<ul>
<li>字段</li>
<li><a href="#constructor-detail">构造器</a></li>
<li><a href="#method-detail">方法</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">方法</a></li>
</ul>
<ul class="sub-nav-list">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">方法</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="搜索">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">程序包</span>&nbsp;<a href="package-summary.html">com.binance.connector.futures.client.impl.futures</a></div>
<h1 title="类 PortfolioMargin" class="title">类 PortfolioMargin</h1>
</div>
<div class="inheritance" title="继承树"><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">java.lang.Object</a>
<div class="inheritance">com.binance.connector.futures.client.impl.futures.PortfolioMargin</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>直接已知子类:</dt>
<dd><code><a href="../cm_futures/CMPortfolioMargin.html" title="com.binance.connector.futures.client.impl.cm_futures中的类">CMPortfolioMargin</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public abstract class </span><span class="element-name type-name-label">PortfolioMargin</span>
<span class="extends-implements">extends <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a></span></div>
<div class="block"><h2 id="portfolio-margin-endpoints-heading">Portfolio Margin Endpoints</h2>
 Response will be returned in <i>String format</i>.</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>构造器概要</h2>
<div class="caption"><span>构造器</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">构造器</div>
<div class="table-header col-last">说明</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(java.lang.String,java.lang.String,java.lang.String,boolean,com.binance.connector.futures.client.utils.ProxyAuth)" class="member-name-link">PortfolioMargin</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;productUrl,
 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;apiKey,
 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;secretKey,
 boolean&nbsp;showLimitUsage,
 com.binance.connector.futures.client.utils.ProxyAuth&nbsp;proxy)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>方法概要</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">所有方法</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">实例方法</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">具体方法</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">修饰符和类型</div>
<div class="table-header col-second">方法</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getProductUrl()" class="member-name-link">getProductUrl</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>com.binance.connector.futures.client.utils.RequestHandler</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getRequestHandler()" class="member-name-link">getRequestHandler</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getShowLimitUsage()" class="member-name-link">getShowLimitUsage</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#portfolioMarginAccountInfo(java.util.LinkedHashMap)" class="member-name-link">portfolioMarginAccountInfo</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/util/LinkedHashMap.html" title="java.util中的类或接口" class="external-link">LinkedHashMap</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&nbsp;parameters)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get Portfolio Margin current account information.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#portfolioMarginExchangeInfo(java.util.LinkedHashMap)" class="member-name-link">portfolioMarginExchangeInfo</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/util/LinkedHashMap.html" title="java.util中的类或接口" class="external-link">LinkedHashMap</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&nbsp;parameters)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setProductUrl(java.lang.String)" class="member-name-link">setProductUrl</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;productUrl)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setRequestHandler(java.lang.String,java.lang.String,com.binance.connector.futures.client.utils.ProxyAuth)" class="member-name-link">setRequestHandler</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;apiKey,
 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;secretKey,
 com.binance.connector.futures.client.utils.ProxyAuth&nbsp;proxy)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setShowLimitUsage(boolean)" class="member-name-link">setShowLimitUsage</a><wbr>(boolean&nbsp;showLimitUsage)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">从类继承的方法&nbsp;java.lang.<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a></h3>
<code><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html#clone--" title="java.lang中的类或接口" class="external-link">clone</a>, <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html#equals-java.lang.Object-" title="java.lang中的类或接口" class="external-link">equals</a>, <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html#finalize--" title="java.lang中的类或接口" class="external-link">finalize</a>, <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html#getClass--" title="java.lang中的类或接口" class="external-link">getClass</a>, <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html#hashCode--" title="java.lang中的类或接口" class="external-link">hashCode</a>, <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html#notify--" title="java.lang中的类或接口" class="external-link">notify</a>, <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html#notifyAll--" title="java.lang中的类或接口" class="external-link">notifyAll</a>, <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html#toString--" title="java.lang中的类或接口" class="external-link">toString</a>, <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html#wait--" title="java.lang中的类或接口" class="external-link">wait</a>, <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html#wait-long-" title="java.lang中的类或接口" class="external-link">wait</a>, <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html#wait-long-int-" title="java.lang中的类或接口" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>构造器详细资料</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;(java.lang.String,java.lang.String,java.lang.String,boolean,com.binance.connector.futures.client.utils.ProxyAuth)">
<h3>PortfolioMargin</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">PortfolioMargin</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;productUrl,
 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;apiKey,
 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;secretKey,
 boolean&nbsp;showLimitUsage,
 com.binance.connector.futures.client.utils.ProxyAuth&nbsp;proxy)</span></div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>方法详细资料</h2>
<ul class="member-list">
<li>
<section class="detail" id="getProductUrl()">
<h3>getProductUrl</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></span>&nbsp;<span class="element-name">getProductUrl</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getRequestHandler()">
<h3>getRequestHandler</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">com.binance.connector.futures.client.utils.RequestHandler</span>&nbsp;<span class="element-name">getRequestHandler</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getShowLimitUsage()">
<h3>getShowLimitUsage</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">getShowLimitUsage</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setProductUrl(java.lang.String)">
<h3>setProductUrl</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setProductUrl</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;productUrl)</span></div>
</section>
</li>
<li>
<section class="detail" id="setRequestHandler(java.lang.String,java.lang.String,com.binance.connector.futures.client.utils.ProxyAuth)">
<h3>setRequestHandler</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setRequestHandler</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;apiKey,
 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;secretKey,
 com.binance.connector.futures.client.utils.ProxyAuth&nbsp;proxy)</span></div>
</section>
</li>
<li>
<section class="detail" id="setShowLimitUsage(boolean)">
<h3>setShowLimitUsage</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setShowLimitUsage</span><wbr><span class="parameters">(boolean&nbsp;showLimitUsage)</span></div>
</section>
</li>
<li>
<section class="detail" id="portfolioMarginExchangeInfo(java.util.LinkedHashMap)">
<h3>portfolioMarginExchangeInfo</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></span>&nbsp;<span class="element-name">portfolioMarginExchangeInfo</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/util/LinkedHashMap.html" title="java.util中的类或接口" class="external-link">LinkedHashMap</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&nbsp;parameters)</span></div>
</section>
</li>
<li>
<section class="detail" id="portfolioMarginAccountInfo(java.util.LinkedHashMap)">
<h3>portfolioMarginAccountInfo</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></span>&nbsp;<span class="element-name">portfolioMarginAccountInfo</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/util/LinkedHashMap.html" title="java.util中的类或接口" class="external-link">LinkedHashMap</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&nbsp;parameters)</span></div>
<div class="block">Get Portfolio Margin current account information.
 GET /v1/pmAccountInfo
 <br></div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>parameters</code> - LinkedHashedMap of String,Object pair
            where String is the name of the parameter and Object is the value of the parameter
 <br><br>
 asset -- mandatory/string <br>
 recvWindow -- optional/long <br></dd>
<dt>返回:</dt>
<dd>String</dd>
<dt>另请参阅:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://developers.binance.com/docs/derivatives/coin-margined-futures/portfolio-margin-endpoints/Classic-Portfolio-Margin-Account-Information#api-description">
     https://developers.binance.com/docs/derivatives/coin-margined-futures/portfolio-margin-endpoints/Classic-Portfolio-Margin-Account-Information#api-description</a></li>
</ul>
</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &#169; 2025. All rights reserved.</small></p>
</footer>
</div>
</div>
</body>
</html>
