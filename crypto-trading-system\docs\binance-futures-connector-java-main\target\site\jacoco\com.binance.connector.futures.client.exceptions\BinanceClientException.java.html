<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>BinanceClientException.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">io.github.binance:binance-futures-connector-java</a> &gt; <a href="index.source.html" class="el_package">com.binance.connector.futures.client.exceptions</a> &gt; <span class="el_source">BinanceClientException.java</span></div><h1>BinanceClientException.java</h1><pre class="source lang-java linenums">package com.binance.connector.futures.client.exceptions;

public class BinanceClientException extends RuntimeException {
    // 序列化版本号
    private static final long serialVersionUID = 1L;
    // 默认错误码
<span class="fc" id="L7">    private final int ERROR_CODE_0 = 0;</span>
    // HTTP状态码
    private final int httpStatusCode;
    // 错误码
    private final int errorCode;
    // 错误信息
    private String errMsg;

    /**
     * 构造函数，使用默认错误码
     * @param fullErrMsg 完整错误信息
     * @param httpStatusCode HTTP状态码
     */
    public BinanceClientException(String fullErrMsg, int httpStatusCode) {
<span class="fc" id="L21">        super(fullErrMsg);</span>
<span class="fc" id="L22">        this.httpStatusCode = httpStatusCode;</span>
<span class="fc" id="L23">        this.errorCode = ERROR_CODE_0;</span>
<span class="fc" id="L24">    }</span>

    /**
     * 构造函数，使用指定错误码
     * @param fullErrMsg 完整错误信息
     * @param errMsg 错误信息
     * @param httpStatusCode HTTP状态码
     * @param errorCode 错误码
     */
    public BinanceClientException(String fullErrMsg, String errMsg, int httpStatusCode, int errorCode) {
<span class="fc" id="L34">        super(fullErrMsg);</span>
<span class="fc" id="L35">        this.httpStatusCode = httpStatusCode;</span>
<span class="fc" id="L36">        this.errorCode = errorCode;</span>
<span class="fc" id="L37">        this.errMsg =  errMsg;</span>
<span class="fc" id="L38">    }</span>

    /**
     * 获取错误码
     * @return 错误码
     */
    public int getErrorCode() {
<span class="nc" id="L45">        return errorCode;</span>
    }

    /**
     * 获取HTTP状态码
     * @return HTTP状态码
     */
    public int getHttpStatusCode() {
<span class="nc" id="L53">        return httpStatusCode;</span>
    }

    /**
     * 获取错误信息
     * @return 错误信息
     */
    public String getErrMsg() {
<span class="nc" id="L61">        return errMsg;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.7.202105040129</span></div></body></html>