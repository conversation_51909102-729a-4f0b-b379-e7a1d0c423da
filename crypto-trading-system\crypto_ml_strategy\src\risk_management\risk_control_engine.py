"""
风险控制引擎模块

统一协调所有风险管理组件，实现风险控制策略执行、
紧急风险处理和与交易执行模块的接口集成。

Author: Crypto ML Strategy Team
Date: 2024-12-19
"""

import asyncio
import threading
from typing import Dict, List, Optional, Tuple, Any, Callable
from datetime import datetime, timedelta
import logging
import json
from dataclasses import dataclass, field

from .risk_config import RiskConfig, RiskEvent, RiskLevel, PositionInfo
from .risk_assessor import RiskAssessor
from .position_manager import PositionManager
from .stop_loss_take_profit import StopLossTakeProfitManager
from .drawdown_controller import DrawdownController, RecoveryAction

logger = logging.getLogger(__name__)


@dataclass
class RiskControlAction:
    """风险控制措施数据类"""
    action_id: str
    action_type: str
    description: str
    priority: int
    parameters: Dict[str, Any] = field(default_factory=dict)
    status: str = "pending"  # pending, executing, completed, failed
    created_time: datetime = field(default_factory=datetime.now)
    executed_time: Optional[datetime] = None
    result: Optional[Dict[str, Any]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'action_id': self.action_id,
            'action_type': self.action_type,
            'description': self.description,
            'priority': self.priority,
            'parameters': self.parameters,
            'status': self.status,
            'created_time': self.created_time.isoformat(),
            'executed_time': self.executed_time.isoformat() if self.executed_time else None,
            'result': self.result
        }


class RiskControlEngine:
    """风险控制引擎类"""
    
    def __init__(self, config: RiskConfig):
        """
        初始化风险控制引擎
        
        Args:
            config: 风险管理配置
        """
        self.config = config
        
        # 初始化各个组件
        self.risk_assessor = RiskAssessor(config)
        self.position_manager = PositionManager(config)
        self.stop_loss_take_profit_manager = StopLossTakeProfitManager(config)
        self.drawdown_controller = DrawdownController(config)
        
        # 控制状态
        self.is_running = False
        self.emergency_mode = False
        self.trading_suspended = False
        
        # 事件和动作队列
        self.risk_events: List[RiskEvent] = []
        self.pending_actions: List[RiskControlAction] = []
        self.action_history: List[RiskControlAction] = []
        
        # 回调函数
        self.event_callbacks: Dict[str, List[Callable]] = {}
        self.action_callbacks: Dict[str, List[Callable]] = {}
        
        # 线程控制
        self._lock = threading.RLock()
        self._monitor_thread: Optional[threading.Thread] = None
        self._stop_event = threading.Event()
        
        # 性能统计
        self.statistics = {
            'total_events': 0,
            'total_actions': 0,
            'emergency_triggers': 0,
            'successful_actions': 0,
            'failed_actions': 0,
            'last_update': datetime.now()
        }
        
        logger.info("Risk control engine initialized")
    
    def start(self) -> None:
        """启动风险控制引擎"""
        try:
            with self._lock:
                if self.is_running:
                    logger.warning("Risk control engine is already running")
                    return
                
                self.is_running = True
                self._stop_event.clear()
                
                # 启动监控线程
                self._monitor_thread = threading.Thread(
                    target=self._monitor_loop,
                    name="RiskControlMonitor",
                    daemon=True
                )
                self._monitor_thread.start()
                
                logger.info("Risk control engine started")
                
        except Exception as e:
            logger.error(f"Error starting risk control engine: {e}")
            raise
    
    def stop(self) -> None:
        """停止风险控制引擎"""
        try:
            with self._lock:
                if not self.is_running:
                    logger.warning("Risk control engine is not running")
                    return
                
                self.is_running = False
                self._stop_event.set()
                
                # 等待监控线程结束
                if self._monitor_thread and self._monitor_thread.is_alive():
                    self._monitor_thread.join(timeout=5.0)
                
                logger.info("Risk control engine stopped")
                
        except Exception as e:
            logger.error(f"Error stopping risk control engine: {e}")
            raise
    
    def update_market_data(self, symbol: str, price_data: Dict[str, Any]) -> None:
        """
        更新市场数据
        
        Args:
            symbol: 交易对符号
            price_data: 价格数据
        """
        try:
            # 更新各组件的市场数据
            if 'price_df' in price_data:
                self.risk_assessor.update_price_data(symbol, price_data['price_df'])
                self.stop_loss_take_profit_manager.update_price_data(symbol, price_data['price_df'])
            
            if 'indicators' in price_data:
                self.stop_loss_take_profit_manager.update_technical_indicators(symbol, price_data['indicators'])
            
            logger.debug(f"Updated market data for {symbol}")
            
        except Exception as e:
            logger.error(f"Error updating market data for {symbol}: {e}")
    
    def update_portfolio_data(self, portfolio_data: Dict[str, Any]) -> None:
        """
        更新投资组合数据
        
        Args:
            portfolio_data: 投资组合数据
        """
        try:
            # 更新风险评估器
            self.risk_assessor.update_portfolio_data(portfolio_data)
            
            # 更新仓位管理器
            if 'balance' in portfolio_data and 'available_balance' in portfolio_data:
                self.position_manager.update_account_info(
                    portfolio_data['balance'],
                    portfolio_data['available_balance']
                )
            
            # 更新回撤控制器
            if 'total_value' in portfolio_data:
                drawdown_info = self.drawdown_controller.update_portfolio_value(
                    portfolio_data['total_value']
                )
                
                # 检查回撤限制
                drawdown_events = self.drawdown_controller.check_drawdown_limits(drawdown_info)
                for event in drawdown_events:
                    self.add_risk_event(event)
            
            logger.debug("Updated portfolio data")
            
        except Exception as e:
            logger.error(f"Error updating portfolio data: {e}")
    
    def update_position(self, symbol: str, position_info: PositionInfo) -> None:
        """
        更新仓位信息
        
        Args:
            symbol: 交易对符号
            position_info: 仓位信息
        """
        try:
            self.position_manager.update_position(symbol, position_info)
            logger.debug(f"Updated position for {symbol}")
            
        except Exception as e:
            logger.error(f"Error updating position for {symbol}: {e}")
    
    def add_risk_event(self, event: RiskEvent) -> None:
        """
        添加风险事件
        
        Args:
            event: 风险事件
        """
        try:
            with self._lock:
                self.risk_events.append(event)
                self.statistics['total_events'] += 1
                
                # 触发事件回调
                self._trigger_event_callbacks(event)
                
                # 处理紧急事件
                if event.severity == RiskLevel.CRITICAL and event.action_required:
                    self._handle_emergency_event(event)
                
                logger.info(f"Added risk event: {event.event_type} - {event.severity.value}")
                
        except Exception as e:
            logger.error(f"Error adding risk event: {e}")
    
    def execute_risk_control_action(self, action: RiskControlAction) -> bool:
        """
        执行风险控制措施
        
        Args:
            action: 风险控制措施
            
        Returns:
            执行是否成功
        """
        try:
            with self._lock:
                action.status = "executing"
                action.executed_time = datetime.now()
                
                success = False
                result = {}
                
                # 根据动作类型执行相应措施
                if action.action_type == "emergency_liquidation":
                    success, result = self._execute_emergency_liquidation(action.parameters)
                
                elif action.action_type == "reduce_positions":
                    success, result = self._execute_position_reduction(action.parameters)
                
                elif action.action_type == "suspend_trading":
                    success, result = self._execute_trading_suspension(action.parameters)
                
                elif action.action_type == "tighten_stops":
                    success, result = self._execute_stop_tightening(action.parameters)
                
                elif action.action_type == "conservative_mode":
                    success, result = self._execute_conservative_mode(action.parameters)
                
                elif action.action_type == "strategy_pause":
                    success, result = self._execute_strategy_pause(action.parameters)
                
                else:
                    logger.warning(f"Unknown action type: {action.action_type}")
                    result = {'error': 'Unknown action type'}
                
                # 更新动作状态
                action.status = "completed" if success else "failed"
                action.result = result
                
                # 更新统计
                if success:
                    self.statistics['successful_actions'] += 1
                else:
                    self.statistics['failed_actions'] += 1
                
                # 触发动作回调
                self._trigger_action_callbacks(action)
                
                logger.info(f"Executed risk control action: {action.action_type} - {'success' if success else 'failed'}")
                
                return success
                
        except Exception as e:
            logger.error(f"Error executing risk control action: {e}")
            action.status = "failed"
            action.result = {'error': str(e)}
            return False
    
    def _monitor_loop(self) -> None:
        """监控循环"""
        try:
            while not self._stop_event.is_set():
                try:
                    # 执行风险检查
                    self._perform_risk_checks()
                    
                    # 处理待执行的动作
                    self._process_pending_actions()
                    
                    # 更新统计信息
                    self.statistics['last_update'] = datetime.now()
                    
                    # 等待下一次检查
                    self._stop_event.wait(self.config.risk_check_interval)
                    
                except Exception as e:
                    logger.error(f"Error in monitor loop: {e}")
                    self._stop_event.wait(5)  # 错误后等待5秒
                    
        except Exception as e:
            logger.error(f"Fatal error in monitor loop: {e}")
        finally:
            logger.info("Risk control monitor loop ended")
    
    def _perform_risk_checks(self) -> None:
        """执行风险检查"""
        try:
            # 检查仓位限制
            violations = self.position_manager.check_position_limits()
            for violation in violations:
                event = RiskEvent(
                    event_type=violation['type'],
                    severity=RiskLevel.HIGH if violation['severity'] == 'high' else RiskLevel.MEDIUM,
                    message=f"Position limit violation: {violation}",
                    timestamp=datetime.now(),
                    action_required=True,
                    metadata=violation
                )
                self.add_risk_event(event)
            
            # 检查止损止盈触发
            for symbol, position in self.position_manager.get_all_positions().items():
                current_price = self._get_current_price(symbol)
                if current_price:
                    position_dict = position.to_dict()
                    
                    # 检查止损
                    if self.stop_loss_take_profit_manager.check_stop_loss_trigger(symbol, current_price, position_dict):
                        event = RiskEvent(
                            event_type="stop_loss_triggered",
                            severity=RiskLevel.HIGH,
                            message=f"Stop loss triggered for {symbol}",
                            timestamp=datetime.now(),
                            symbol=symbol,
                            current_value=current_price,
                            threshold_value=position.stop_loss_price,
                            action_required=True
                        )
                        self.add_risk_event(event)
                    
                    # 检查止盈
                    if self.stop_loss_take_profit_manager.check_take_profit_trigger(symbol, current_price, position_dict):
                        event = RiskEvent(
                            event_type="take_profit_triggered",
                            severity=RiskLevel.MEDIUM,
                            message=f"Take profit triggered for {symbol}",
                            timestamp=datetime.now(),
                            symbol=symbol,
                            current_value=current_price,
                            threshold_value=position.take_profit_price,
                            action_required=True
                        )
                        self.add_risk_event(event)
            
        except Exception as e:
            logger.error(f"Error performing risk checks: {e}")
    
    def _process_pending_actions(self) -> None:
        """处理待执行的动作"""
        try:
            with self._lock:
                # 按优先级排序
                self.pending_actions.sort(key=lambda x: x.priority)
                
                # 执行高优先级动作
                actions_to_execute = []
                for action in self.pending_actions:
                    if action.status == "pending":
                        actions_to_execute.append(action)
                        if len(actions_to_execute) >= 5:  # 限制同时执行的动作数量
                            break
                
                # 执行动作
                for action in actions_to_execute:
                    self.execute_risk_control_action(action)
                    self.pending_actions.remove(action)
                    self.action_history.append(action)
                
                # 清理历史记录
                if len(self.action_history) > 1000:
                    self.action_history = self.action_history[-1000:]
                
        except Exception as e:
            logger.error(f"Error processing pending actions: {e}")
    
    def _handle_emergency_event(self, event: RiskEvent) -> None:
        """处理紧急事件"""
        try:
            self.emergency_mode = True
            self.statistics['emergency_triggers'] += 1
            
            # 根据事件类型创建紧急措施
            if event.event_type == "emergency_stop_triggered":
                action = RiskControlAction(
                    action_id=f"emergency_{datetime.now().timestamp()}",
                    action_type="emergency_liquidation",
                    description="Emergency liquidation due to excessive drawdown",
                    priority=1,
                    parameters={'liquidation_percentage': 100}
                )
                self.pending_actions.append(action)
            
            elif event.event_type == "max_drawdown_exceeded":
                action = RiskControlAction(
                    action_id=f"drawdown_{datetime.now().timestamp()}",
                    action_type="reduce_positions",
                    description="Reduce positions due to max drawdown exceeded",
                    priority=1,
                    parameters={'reduction_percentage': 50}
                )
                self.pending_actions.append(action)
            
            logger.critical(f"Emergency event handled: {event.event_type}")
            
        except Exception as e:
            logger.error(f"Error handling emergency event: {e}")
    
    def _execute_emergency_liquidation(self, parameters: Dict[str, Any]) -> Tuple[bool, Dict[str, Any]]:
        """执行紧急平仓"""
        try:
            liquidation_pct = parameters.get('liquidation_percentage', 100)
            
            # 这里应该调用交易执行模块进行平仓
            # 暂时返回模拟结果
            result = {
                'action': 'emergency_liquidation',
                'liquidation_percentage': liquidation_pct,
                'positions_closed': len(self.position_manager.get_all_positions()),
                'timestamp': datetime.now().isoformat()
            }
            
            # 设置紧急模式
            self.emergency_mode = True
            self.trading_suspended = True
            
            logger.critical(f"Emergency liquidation executed: {liquidation_pct}%")
            
            return True, result
            
        except Exception as e:
            logger.error(f"Error executing emergency liquidation: {e}")
            return False, {'error': str(e)}
    
    def _execute_position_reduction(self, parameters: Dict[str, Any]) -> Tuple[bool, Dict[str, Any]]:
        """执行仓位减少"""
        try:
            reduction_pct = parameters.get('reduction_percentage', 30)
            
            # 获取仓位调整建议
            suggestions = self.position_manager.suggest_position_adjustments()
            
            result = {
                'action': 'position_reduction',
                'reduction_percentage': reduction_pct,
                'suggestions': len(suggestions),
                'timestamp': datetime.now().isoformat()
            }
            
            logger.info(f"Position reduction executed: {reduction_pct}%")
            
            return True, result
            
        except Exception as e:
            logger.error(f"Error executing position reduction: {e}")
            return False, {'error': str(e)}
    
    def _execute_trading_suspension(self, parameters: Dict[str, Any]) -> Tuple[bool, Dict[str, Any]]:
        """执行交易暂停"""
        try:
            duration = parameters.get('suspension_duration', 24)  # 小时
            
            self.trading_suspended = True
            
            result = {
                'action': 'trading_suspension',
                'duration_hours': duration,
                'timestamp': datetime.now().isoformat()
            }
            
            logger.warning(f"Trading suspended for {duration} hours")
            
            return True, result
            
        except Exception as e:
            logger.error(f"Error executing trading suspension: {e}")
            return False, {'error': str(e)}
    
    def _execute_stop_tightening(self, parameters: Dict[str, Any]) -> Tuple[bool, Dict[str, Any]]:
        """执行止损收紧"""
        try:
            multiplier = parameters.get('stop_loss_multiplier', 0.5)
            
            result = {
                'action': 'stop_tightening',
                'multiplier': multiplier,
                'positions_affected': len(self.position_manager.get_all_positions()),
                'timestamp': datetime.now().isoformat()
            }
            
            logger.info(f"Stop loss tightening executed with multiplier: {multiplier}")
            
            return True, result
            
        except Exception as e:
            logger.error(f"Error executing stop tightening: {e}")
            return False, {'error': str(e)}
    
    def _execute_conservative_mode(self, parameters: Dict[str, Any]) -> Tuple[bool, Dict[str, Any]]:
        """执行保守模式"""
        try:
            risk_multiplier = parameters.get('risk_multiplier', 0.7)
            
            result = {
                'action': 'conservative_mode',
                'risk_multiplier': risk_multiplier,
                'timestamp': datetime.now().isoformat()
            }
            
            logger.info(f"Conservative mode activated with risk multiplier: {risk_multiplier}")
            
            return True, result
            
        except Exception as e:
            logger.error(f"Error executing conservative mode: {e}")
            return False, {'error': str(e)}
    
    def _execute_strategy_pause(self, parameters: Dict[str, Any]) -> Tuple[bool, Dict[str, Any]]:
        """执行策略暂停"""
        try:
            duration = parameters.get('pause_duration', 48)  # 小时
            
            result = {
                'action': 'strategy_pause',
                'duration_hours': duration,
                'timestamp': datetime.now().isoformat()
            }
            
            logger.info(f"Strategy paused for {duration} hours")
            
            return True, result
            
        except Exception as e:
            logger.error(f"Error executing strategy pause: {e}")
            return False, {'error': str(e)}
    
    def _get_current_price(self, symbol: str) -> Optional[float]:
        """获取当前价格"""
        try:
            # 这里应该从市场数据源获取当前价格
            # 暂时返回None
            return None
        except Exception as e:
            logger.error(f"Error getting current price for {symbol}: {e}")
            return None
    
    def _trigger_event_callbacks(self, event: RiskEvent) -> None:
        """触发事件回调"""
        try:
            callbacks = self.event_callbacks.get(event.event_type, [])
            for callback in callbacks:
                try:
                    callback(event)
                except Exception as e:
                    logger.error(f"Error in event callback: {e}")
        except Exception as e:
            logger.error(f"Error triggering event callbacks: {e}")
    
    def _trigger_action_callbacks(self, action: RiskControlAction) -> None:
        """触发动作回调"""
        try:
            callbacks = self.action_callbacks.get(action.action_type, [])
            for callback in callbacks:
                try:
                    callback(action)
                except Exception as e:
                    logger.error(f"Error in action callback: {e}")
        except Exception as e:
            logger.error(f"Error triggering action callbacks: {e}")
    
    def register_event_callback(self, event_type: str, callback: Callable) -> None:
        """注册事件回调"""
        if event_type not in self.event_callbacks:
            self.event_callbacks[event_type] = []
        self.event_callbacks[event_type].append(callback)
    
    def register_action_callback(self, action_type: str, callback: Callable) -> None:
        """注册动作回调"""
        if action_type not in self.action_callbacks:
            self.action_callbacks[action_type] = []
        self.action_callbacks[action_type].append(callback)
    
    def get_engine_status(self) -> Dict[str, Any]:
        """获取引擎状态"""
        try:
            with self._lock:
                return {
                    'is_running': self.is_running,
                    'emergency_mode': self.emergency_mode,
                    'trading_suspended': self.trading_suspended,
                    'pending_actions': len(self.pending_actions),
                    'recent_events': len([e for e in self.risk_events if (datetime.now() - e.timestamp).seconds < 3600]),
                    'statistics': self.statistics.copy(),
                    'last_update': datetime.now().isoformat()
                }
        except Exception as e:
            logger.error(f"Error getting engine status: {e}")
            return {'status': 'error', 'message': str(e)}
