package com.crypto.trading.market.listener;

import com.crypto.trading.sdk.config.BinanceApiConfig;
import com.crypto.trading.sdk.websocket.BinanceWebSocketClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationStartedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * WebSocket启动监听器
 * 在应用启动后自动启动WebSocket连接
 */
@Component
public class WebSocketStartupListener {
    
    private static final Logger log = LoggerFactory.getLogger(WebSocketStartupListener.class);
    
    @Autowired
    private KlineDataListener klineDataListener;
    
    @Autowired
    private DepthDataListener depthDataListener;
    
    @Autowired
    private TradeDataListener tradeDataListener;
    
    @Autowired
    private BinanceWebSocketClient webSocketClient;
    
    @Autowired
    private BinanceApiConfig binanceApiConfig;
    
    /**
     * 应用启动事件监听
     * 当应用启动完成后，自动启动WebSocket连接
     *
     * @param event 应用启动事件
     */
    @EventListener
    public void onApplicationStarted(ApplicationStartedEvent event) {
        log.info("应用启动完成，开始初始化WebSocket连接");
        log.info("币安API配置: {}", binanceApiConfig);
        log.info("使用测试网络: {}", binanceApiConfig.isUseTestnet());
        log.info("WebSocket基础URL: {}", binanceApiConfig.getUsdtFuturesWsBaseUrl());
        
        try {
            // 启动K线数据监听
            klineDataListener.start();
            
            // 启动深度数据监听
            depthDataListener.start();
            
            // 启动交易数据监听
            tradeDataListener.start();
            
            log.info("WebSocket连接初始化完成，当前活跃连接数: {}", webSocketClient.getActiveConnectionCount());
        } catch (Exception e) {
            log.error("初始化WebSocket连接失败", e);
        }
    }
}