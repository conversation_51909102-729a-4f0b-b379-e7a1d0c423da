package com.crypto.trading.trade.producer.impl;

import com.crypto.trading.common.dto.trade.OrderDTO;
import com.crypto.trading.common.dto.trade.OrderStatusDTO;
import com.crypto.trading.common.util.JsonUtil;
import com.crypto.trading.common.util.ThreadUtil;
import com.crypto.trading.trade.producer.TradeResultProducer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadFactory;

/**
 * Kafka交易结果生产者
 * <p>
 * 将交易结果发送到Kafka
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
public class KafkaTradeResultProducer implements TradeResultProducer {

    private static final Logger log = LoggerFactory.getLogger(KafkaTradeResultProducer.class);

    /**
     * Kafka模板
     */
    @Autowired
    private KafkaTemplate<String, String> kafkaTemplate;

    /**
     * 订单执行结果主题
     */
    @Value("${kafka.topic.order.execution.result:order.execution.result}")
    private String orderExecutionResultTopic;

    /**
     * 订单状态更新主题
     */
    @Value("${kafka.topic.order.status.update:order.status.update}")
    private String orderStatusUpdateTopic;

    /**
     * 订单执行错误主题
     */
    @Value("${kafka.topic.order.execution.error:order.execution.error}")
    private String orderExecutionErrorTopic;

    /**
     * 发送订单执行结果
     *
     * @param orderDTO 订单数据传输对象
     * @return 发送结果的CompletableFuture
     */
    @Override
    public CompletableFuture<Void> sendOrderExecutionResult(OrderDTO orderDTO) {
        log.info("发送订单执行结果到Kafka: orderId={}, symbol={}, status={}",
                orderDTO.getOrderId(), orderDTO.getSymbol(), orderDTO.getStatus());

        String key = orderDTO.getOrderId();
        String value = JsonUtil.toJsonString(orderDTO);

        // 使用虚拟线程发送消息
        ThreadFactory threadFactory = Thread.ofVirtual().factory();
        return CompletableFuture.supplyAsync(() -> {
            try {
                // 发送消息到Kafka
                CompletableFuture<SendResult<String, String>> future = kafkaTemplate.send(
                        orderExecutionResultTopic,
                        key,
                        value);

                // 等待发送结果
                SendResult<String, String> result = future.join();
                log.info("订单执行结果发送成功: topic={}, partition={}, offset={}",
                        result.getRecordMetadata().topic(),
                        result.getRecordMetadata().partition(),
                        result.getRecordMetadata().offset());

                return null;
            } catch (Exception e) {
                log.error("订单执行结果发送失败: orderId={}, error={}", orderDTO.getOrderId(), e.getMessage(), e);
                throw new RuntimeException("订单执行结果发送失败", e);
            }
        }, ThreadUtil.toExecutor(threadFactory));
    }

    /**
     * 发送订单状态更新
     *
     * @param orderStatusDTO 订单状态数据传输对象
     * @return 发送结果的CompletableFuture
     */
    @Override
    public CompletableFuture<Void> sendOrderStatusUpdate(OrderStatusDTO orderStatusDTO) {
        log.info("发送订单状态更新到Kafka: orderId={}, symbol={}, status={}",
                orderStatusDTO.getOrderId(), orderStatusDTO.getSymbol(), orderStatusDTO.getStatus());

        String key = orderStatusDTO.getOrderId();
        String value = JsonUtil.toJsonString(orderStatusDTO);

        // 使用虚拟线程发送消息
        ThreadFactory threadFactory = Thread.ofVirtual().factory();
        return CompletableFuture.supplyAsync(() -> {
            try {
                // 发送消息到Kafka
                CompletableFuture<SendResult<String, String>> future = kafkaTemplate.send(
                        orderStatusUpdateTopic,
                        key,
                        value);

                // 等待发送结果
                SendResult<String, String> result = future.join();
                log.info("订单状态更新发送成功: topic={}, partition={}, offset={}",
                        result.getRecordMetadata().topic(),
                        result.getRecordMetadata().partition(),
                        result.getRecordMetadata().offset());

                return null;
            } catch (Exception e) {
                log.error("订单状态更新发送失败: orderId={}, error={}", orderStatusDTO.getOrderId(), e.getMessage(), e);
                throw new RuntimeException("订单状态更新发送失败", e);
            }
        }, ThreadUtil.toExecutor(threadFactory));
    }

    /**
     * 发送订单执行错误
     *
     * @param clientOrderId 客户端订单ID
     * @param symbol        交易对
     * @param errorCode     错误代码
     * @param errorMessage  错误消息
     * @return 发送结果的CompletableFuture
     */
    @Override
    public CompletableFuture<Void> sendOrderExecutionError(String clientOrderId, String symbol, int errorCode, String errorMessage) {
        log.info("发送订单执行错误到Kafka: clientOrderId={}, symbol={}, errorCode={}, errorMessage={}",
                clientOrderId, symbol, errorCode, errorMessage);

        // 构建错误消息
        Map<String, Object> errorData = new HashMap<>();
        errorData.put("clientOrderId", clientOrderId);
        errorData.put("symbol", symbol);
        errorData.put("errorCode", errorCode);
        errorData.put("errorMessage", errorMessage);
        errorData.put("timestamp", System.currentTimeMillis());

        String key = clientOrderId;
        String value = JsonUtil.toJsonString(errorData);

        // 使用虚拟线程发送消息
        ThreadFactory threadFactory = Thread.ofVirtual().factory();
        return CompletableFuture.supplyAsync(() -> {
            try {
                // 发送消息到Kafka
                CompletableFuture<SendResult<String, String>> future = kafkaTemplate.send(
                        orderExecutionErrorTopic,
                        key,
                        value);

                // 等待发送结果
                SendResult<String, String> result = future.join();
                log.info("订单执行错误发送成功: topic={}, partition={}, offset={}",
                        result.getRecordMetadata().topic(),
                        result.getRecordMetadata().partition(),
                        result.getRecordMetadata().offset());

                return null;
            } catch (Exception e) {
                log.error("订单执行错误发送失败: clientOrderId={}, error={}", clientOrderId, e.getMessage(), e);
                throw new RuntimeException("订单执行错误发送失败", e);
            }
        }, ThreadUtil.toExecutor(threadFactory));
    }

    /**
     * 批量发送订单执行结果
     *
     * @param orderDTOs 订单数据传输对象列表
     * @return 发送结果的CompletableFuture
     */
    @Override
    public CompletableFuture<Void> sendBatchOrderExecutionResults(Iterable<OrderDTO> orderDTOs) {
        log.info("批量发送订单执行结果到Kafka");

        // 使用虚拟线程发送消息
        ThreadFactory threadFactory = Thread.ofVirtual().factory();
        return CompletableFuture.supplyAsync(() -> {
            try {
                // 创建一个CompletableFuture列表，用于跟踪所有发送操作
                CompletableFuture<Void> allOf = CompletableFuture.allOf();
                int count = 0;

                // 遍历订单列表，逐个发送
                for (OrderDTO orderDTO : orderDTOs) {
                    CompletableFuture<Void> future = sendOrderExecutionResult(orderDTO);
                    allOf = CompletableFuture.allOf(allOf, future);
                    count++;
                }

                // 等待所有发送操作完成
                allOf.join();
                log.info("批量发送订单执行结果完成，共发送{}条消息", count);

                return null;
            } catch (Exception e) {
                log.error("批量发送订单执行结果失败: error={}", e.getMessage(), e);
                throw new RuntimeException("批量发送订单执行结果失败", e);
            }
        }, ThreadUtil.toExecutor(threadFactory));
    }
}