package com.crypto.trading.market.producer;

import com.crypto.trading.market.config.KafkaProducerConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;

import java.util.concurrent.CompletableFuture;
import java.util.function.BiConsumer;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * KafkaMessageProducer单元测试
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class KafkaMessageProducerTest {

    @Mock
    private KafkaTemplate<String, String> kafkaTemplate;

    @Mock
    private KafkaProducerConfig kafkaProducerConfig;

    @Mock
    private CompletableFuture<SendResult<String, String>> future;

    @InjectMocks
    private KafkaMessageProducer kafkaMessageProducer;

    @BeforeEach
    void setUp() {
        // 设置mock行为
        doReturn("kline.data").when(kafkaProducerConfig).getKlineTopic();
        doReturn("depth.data").when(kafkaProducerConfig).getDepthTopic();
        doReturn("trade.data").when(kafkaProducerConfig).getTradeTopic();
        doReturn(future).when(kafkaTemplate).send(anyString(), anyString(), anyString());
        
        // 默认配置future的whenComplete行为
        doAnswer(invocation -> {
            return future;
        }).when(future).whenComplete(any());
    }

    @Test
    void testSendKlineData() throws InterruptedException {
        // 调用被测试方法
        kafkaMessageProducer.sendKlineData("BTCUSDT_1m", "{\"test\":\"kline\"}");
        
        // 由于使用了虚拟线程，需要等待短暂时间让任务执行
        Thread.sleep(100);
        
        // 验证消息被发送到正确的主题
        verify(kafkaTemplate).send(eq("kline.data"), eq("BTCUSDT_1m"), eq("{\"test\":\"kline\"}"));
    }

    @Test
    void testSendDepthData() throws InterruptedException {
        // 调用被测试方法
        kafkaMessageProducer.sendDepthData("BTCUSDT", "{\"test\":\"depth\"}");
        
        // 由于使用了虚拟线程，需要等待短暂时间让任务执行
        Thread.sleep(100);
        
        // 验证消息被发送到正确的主题
        verify(kafkaTemplate).send(eq("depth.data"), eq("BTCUSDT"), eq("{\"test\":\"depth\"}"));
    }

    @Test
    void testSendTradeData() throws InterruptedException {
        // 调用被测试方法
        kafkaMessageProducer.sendTradeData("BTCUSDT", "{\"test\":\"trade\"}");
        
        // 由于使用了虚拟线程，需要等待短暂时间让任务执行
        Thread.sleep(100);
        
        // 验证消息被发送到正确的主题
        verify(kafkaTemplate).send(eq("trade.data"), eq("BTCUSDT"), eq("{\"test\":\"trade\"}"));
    }

    @Test
    void testSendMessageWithException() throws InterruptedException {
        // 配置mock抛出异常
        doThrow(new RuntimeException("Kafka error")).when(kafkaTemplate).send(anyString(), anyString(), anyString());
        
        // 调用被测试方法，不应该抛出异常
        kafkaMessageProducer.sendKlineData("BTCUSDT_1m", "{\"test\":\"kline\"}");
        
        // 由于使用了虚拟线程，需要等待短暂时间让任务执行
        Thread.sleep(100);
        
        // 验证尝试发送消息
        verify(kafkaTemplate).send(eq("kline.data"), eq("BTCUSDT_1m"), eq("{\"test\":\"kline\"}"));
    }

    @SuppressWarnings("unchecked")
    @Test
    void testSendMessageWithFutureException() throws InterruptedException {
        // 重置future配置
        reset(future);
        
        // 重新配置kafkaTemplate.send返回future
        doReturn(future).when(kafkaTemplate).send(anyString(), anyString(), anyString());
        
        // 模拟future.whenComplete行为 - 但执行传入的BiConsumer
        doAnswer(invocation -> {
            BiConsumer<SendResult<String, String>, Throwable> callback = invocation.getArgument(0);
            // 触发回调的异常场景，第二个参数是异常对象
            callback.accept(null, new RuntimeException("Future error")); 
            return future;
        }).when(future).whenComplete(any());
        
        // 调用被测试方法
        kafkaMessageProducer.sendKlineData("BTCUSDT_1m", "{\"test\":\"kline\"}");
        
        // 由于使用了虚拟线程，需要等待更长时间让任务执行
        Thread.sleep(500);
        
        // 验证尝试发送消息
        verify(kafkaTemplate, timeout(1000)).send(eq("kline.data"), eq("BTCUSDT_1m"), eq("{\"test\":\"kline\"}"));
    }
} 