package com.crypto.trading.common.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.io.Serializable;

/**
 * @name: RiskAssessmentDTO
 * @author: zeek
 * @description: 风险评估数据传输对象
 * @create: 2024-07-16 00:20
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RiskAssessmentDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Double marketRisk;
    private Double volatilityRisk;
    private Double drawdownRisk;
    private Double overallRisk;
}
