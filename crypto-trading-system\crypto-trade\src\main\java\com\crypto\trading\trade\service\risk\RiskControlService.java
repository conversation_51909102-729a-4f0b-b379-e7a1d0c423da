package com.crypto.trading.trade.service.risk;

import com.crypto.trading.common.model.signal.TradeSignal;
import com.crypto.trading.trade.model.entity.order.OrderEntity;
import com.crypto.trading.trade.model.entity.risk.RiskControlLogEntity;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 风险控制服务接口
 * 
 * <p>负责交易前的风险控制检查，防止异常交易</p>
 */
public interface RiskControlService {

    /**
     * 检查交易信号是否通过风控
     *
     * @param signal 交易信号
     * @return 是否通过
     */
    boolean checkSignal(TradeSignal signal);

    /**
     * 检查订单是否通过风控
     *
     * @param order 订单实体
     * @return 是否通过
     */
    boolean checkOrder(OrderEntity order);

    /**
     * 检查账户风险
     *
     * @param accountId 账户ID
     * @return 是否通过
     */
    boolean checkAccountRisk(String accountId);

    /**
     * 检查交易频率风险
     *
     * @param symbol 交易对
     * @param timeWindowMs 时间窗口(毫秒)
     * @return 是否通过
     */
    boolean checkTradeFrequency(String symbol, long timeWindowMs);

    /**
     * 检查金额风险
     *
     * @param symbol 交易对
     * @param amount 交易金额
     * @return 是否通过
     */
    boolean checkAmountRisk(String symbol, BigDecimal amount);

    /**
     * 记录风控日志
     *
     * @param signalId 信号ID
     * @param orderId 订单ID
     * @param ruleId 规则ID
     * @param ruleName 规则名称
     * @param riskType 风险类型
     * @param riskLevel 风险等级
     * @param triggered 是否触发
     * @param actionTaken 采取的行动
     * @param details 详细信息
     * @return 风控日志实体
     */
    RiskControlLogEntity logRiskCheck(
            String signalId,
            String orderId,
            String ruleId,
            String ruleName,
            String riskType,
            String riskLevel,
            boolean triggered,
            String actionTaken,
            String details);

    /**
     * 获取风控日志
     *
     * @param signalId 信号ID
     * @return 风控日志列表
     */
    List<RiskControlLogEntity> getRiskLogs(String signalId);

    /**
     * 获取风控规则触发统计
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 规则触发统计
     */
    Map<String, Integer> getRuleTriggeredStats(long startTime, long endTime);
}