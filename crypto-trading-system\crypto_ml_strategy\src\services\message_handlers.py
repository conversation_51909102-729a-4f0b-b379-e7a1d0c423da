#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
消息处理模块

统一的消息处理入口，集成核心和扩展消息处理器，
实现完整数据流：Kafka消息 → 数据处理 → 技术指标计算 → ML预测 → 信号生成
"""

from typing import Dict, Any
from loguru import logger

from .message_handlers_core import EnhancedMessageHandlers
from .message_handlers_extended import MessageHandlersExtended
from ..infrastructure.intelligent_cache import IntelligentCacheService
from .technical_indicator_service import TechnicalIndicatorService
from .prediction_engine_service import PredictionEngineService
from ..infrastructure.async_error_recovery import AsyncErrorRecoveryService
from ..infrastructure.service_interfaces import IDataProcessorService, IStrategyService, IKafkaService, IMetricsService


class UnifiedMessageHandlers:
    """
    统一消息处理器
    
    集成核心和扩展消息处理功能，提供完整的消息处理能力。
    """
    
    def __init__(self,
                 cache_service: IntelligentCacheService,
                 technical_indicator_service: TechnicalIndicatorService,
                 prediction_engine_service: PredictionEngineService,
                 async_error_recovery: AsyncErrorRecoveryService,
                 data_processor: IDataProcessorService,
                 strategy_service: IStrategyService,
                 kafka_service: IKafkaService,
                 metrics_service: IMetricsService):
        """
        初始化统一消息处理器
        
        Args:
            cache_service: 智能缓存服务
            technical_indicator_service: 技术指标服务
            prediction_engine_service: 预测引擎服务
            async_error_recovery: 异步错误恢复服务
            data_processor: 数据处理服务
            strategy_service: 策略服务
            kafka_service: Kafka服务
            metrics_service: 指标服务
        """
        # 初始化核心处理器
        self.core_handler = EnhancedMessageHandlers(
            cache_service=cache_service,
            technical_indicator_service=technical_indicator_service,
            prediction_engine_service=prediction_engine_service,
            async_error_recovery=async_error_recovery,
            data_processor=data_processor,
            strategy_service=strategy_service,
            kafka_service=kafka_service,
            metrics_service=metrics_service
        )
        
        # 初始化扩展处理器
        self.extended_handler = MessageHandlersExtended(self.core_handler)
        
        logger.info("统一消息处理器初始化完成")
    
    async def handle_kline_message(self, message: Dict[str, Any]) -> None:
        """处理K线消息"""
        await self.core_handler.handle_kline_message(message)
    
    async def handle_depth_message(self, message: Dict[str, Any]) -> None:
        """处理深度消息"""
        await self.extended_handler.handle_depth_message(message)
    
    async def handle_trade_message(self, message: Dict[str, Any]) -> None:
        """处理交易消息"""
        await self.extended_handler.handle_trade_message(message)


# 向后兼容性别名
EnhancedMessageHandlers = UnifiedMessageHandlers