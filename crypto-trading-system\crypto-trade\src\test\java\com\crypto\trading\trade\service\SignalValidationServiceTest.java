package com.crypto.trading.trade.service;

import com.crypto.trading.trade.model.avro.OrderType;
import com.crypto.trading.trade.model.avro.SignalType;
import com.crypto.trading.trade.model.avro.TimeInForce;
import com.crypto.trading.trade.model.avro.TradingSignal;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * 信号验证服务测试类
 */
public class SignalValidationServiceTest {

    @InjectMocks
    private SignalValidationService signalValidationService;

    @Mock
    private List<String> supportedSymbols;

    private TradingSignal createValidSignal() {
        return TradingSignal.newBuilder()
            .setId("test-signal-1")
            .setSymbol("BTCUSDT")
            .setStrategy("test-strategy")
            .setSignalType(SignalType.BUY)
            .setConfidence(0.8)
            .setOrderType(OrderType.MARKET)
            .setTimeInForce(TimeInForce.GTC)
            .setGeneratedTime(System.currentTimeMillis())
            .setAdditionalParams(new HashMap<>())
            .build();
    }

    @BeforeEach
    public void setup() {
        MockitoAnnotations.openMocks(this);
        
        // 配置支持的交易对，使用doAnswer动态响应
        doAnswer(invocation -> {
            String symbol = invocation.getArgument(0);
            return symbol.equals("BTCUSDT") || symbol.equals("ETHUSDT");
        }).when(supportedSymbols).contains(anyString());
    }

    @Test
    public void testValidateSignalSuccess() {
        // 准备数据
        TradingSignal signal = createValidSignal();
        
        // 执行
        SignalValidationService.ValidationResult result = signalValidationService.validateSignal(signal);
        
        // 验证
        assertTrue(result.isValid());
        assertNull(result.getErrorMessage());
        assertEquals(signal, result.getSignal());
        assertNotNull(result.getValidationId());
    }

    @Test
    public void testValidateSignalMissingId() {
        // 准备数据
        TradingSignal signal = TradingSignal.newBuilder()
            .setId("")  // 使用空字符串代替完全不设置
            .setSymbol("BTCUSDT")
            .setStrategy("test-strategy")
            .setSignalType(SignalType.BUY)
            .setConfidence(0.8)
            .setOrderType(OrderType.MARKET)
            .setTimeInForce(TimeInForce.GTC)
            .setGeneratedTime(System.currentTimeMillis())
            .setAdditionalParams(new HashMap<>())
            .build();
        
        // 执行
        SignalValidationService.ValidationResult result = signalValidationService.validateSignal(signal);
        
        // 验证
        assertFalse(result.isValid());
        assertEquals("信号ID为空", result.getErrorMessage());
    }

    @Test
    public void testValidateSignalMissingSymbol() {
        // 准备数据
        TradingSignal signal = TradingSignal.newBuilder()
            .setId("test-signal-1")
            .setSymbol("")  // 使用空字符串代替null
            .setStrategy("test-strategy")
            .setSignalType(SignalType.BUY)
            .setConfidence(0.8)
            .setOrderType(OrderType.MARKET)
            .setTimeInForce(TimeInForce.GTC)
            .setGeneratedTime(System.currentTimeMillis())
            .setAdditionalParams(new HashMap<>())
            .build();
        
        // 执行
        SignalValidationService.ValidationResult result = signalValidationService.validateSignal(signal);
        
        // 验证
        assertFalse(result.isValid());
        assertEquals("交易对为空", result.getErrorMessage());
    }

    @Test
    public void testValidateSignalUnsupportedSymbol() {
        // 准备数据
        TradingSignal signal = TradingSignal.newBuilder(createValidSignal())
            .setSymbol("UNSUPPORTED")
            .build();
        
        // 执行
        SignalValidationService.ValidationResult result = signalValidationService.validateSignal(signal);
        
        // 验证
        assertFalse(result.isValid());
        assertTrue(result.getErrorMessage().contains("不支持的交易对"));
    }

    @Test
    public void testValidateSignalMissingLimitPrice() {
        // 准备数据 - 不使用price字段
        TradingSignal signal = TradingSignal.newBuilder()
            .setId("test-signal-1")
            .setSymbol("BTCUSDT")
            .setStrategy("test-strategy")
            .setSignalType(SignalType.BUY)
            .setConfidence(0.8)
            .setOrderType(OrderType.LIMIT)
            .setTimeInForce(TimeInForce.GTC)
            .setGeneratedTime(System.currentTimeMillis())
            .setAdditionalParams(new HashMap<>())
            // 不设置price字段，这样它将为null
            .build();
        
        // 执行
        SignalValidationService.ValidationResult result = signalValidationService.validateSignal(signal);
        
        // 验证
        assertFalse(result.isValid());
        assertEquals("限价单必须指定价格", result.getErrorMessage());
    }

    @Test
    public void testValidateSignalInvalidConfidence() {
        // 准备数据
        TradingSignal signal = TradingSignal.newBuilder(createValidSignal())
            .setConfidence(1.5)
            .build();
        
        // 执行
        SignalValidationService.ValidationResult result = signalValidationService.validateSignal(signal);
        
        // 验证
        assertFalse(result.isValid());
        assertTrue(result.getErrorMessage().contains("置信度必须在0.0-1.0之间"));
    }

    @Test
    public void testValidateSignalInvalidSignalTypeOrderType() {
        // 准备数据
        TradingSignal signal = TradingSignal.newBuilder(createValidSignal())
            .setSignalType(SignalType.HOLD)
            .setOrderType(OrderType.MARKET)
            .build();
        
        // 执行
        SignalValidationService.ValidationResult result = signalValidationService.validateSignal(signal);
        
        // 验证
        assertFalse(result.isValid());
        assertTrue(result.getErrorMessage().contains("信号类型与订单类型不匹配"));
    }
}