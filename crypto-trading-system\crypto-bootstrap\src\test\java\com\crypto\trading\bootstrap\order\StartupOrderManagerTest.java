package com.crypto.trading.bootstrap.order;

import com.crypto.trading.bootstrap.initializer.ModuleInitializer;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 启动顺序管理器测试类
 */
class StartupOrderManagerTest {
    
    /**
     * 测试用模块初始化器
     */
    private static class TestModuleInitializer implements ModuleInitializer {
        
        private final String name;
        private final int priority;
        private final String[] dependencies;
        private boolean initialized = false;
        private boolean beforeInitializeCalled = false;
        private boolean initializeCalled = false;
        private boolean afterInitializeCalled = false;
        private boolean shutdownCalled = false;
        private Exception exceptionToThrow = null;
        
        public TestModuleInitializer(String name, int priority, String... dependencies) {
            this.name = name;
            this.priority = priority;
            this.dependencies = dependencies;
        }
        
        public void setExceptionToThrow(Exception exceptionToThrow) {
            this.exceptionToThrow = exceptionToThrow;
        }
        
        @Override
        public String getName() {
            return name;
        }
        
        @Override
        public int getPriority() {
            return priority;
        }
        
        @Override
        public String[] getDependencies() {
            return dependencies;
        }
        
        @Override
        public void beforeInitialize() {
            beforeInitializeCalled = true;
        }
        
        @Override
        public void initialize() throws Exception {
            initializeCalled = true;
            if (exceptionToThrow != null) {
                throw exceptionToThrow;
            }
            initialized = true;
        }
        
        @Override
        public void afterInitialize() {
            afterInitializeCalled = true;
        }
        
        @Override
        public void shutdown() {
            shutdownCalled = true;
            initialized = false;
        }
        
        @Override
        public boolean isInitialized() {
            return initialized;
        }
        
        public boolean isBeforeInitializeCalled() {
            return beforeInitializeCalled;
        }
        
        public boolean isInitializeCalled() {
            return initializeCalled;
        }
        
        public boolean isAfterInitializeCalled() {
            return afterInitializeCalled;
        }
        
        public boolean isShutdownCalled() {
            return shutdownCalled;
        }
    }
    
    private StartupOrderManager manager;
    private List<ModuleInitializer> initializers;
    private TestModuleInitializer moduleA;
    private TestModuleInitializer moduleB;
    private TestModuleInitializer moduleC;
    
    @BeforeEach
    void setUp() {
        manager = new StartupOrderManager();
        initializers = new ArrayList<>();
        
        // 创建测试用的模块初始化器
        moduleA = new TestModuleInitializer("A", 10);
        moduleB = new TestModuleInitializer("B", 20, "A");
        moduleC = new TestModuleInitializer("C", 30, "B");
        
        initializers.add(moduleA);
        initializers.add(moduleB);
        initializers.add(moduleC);
        
        // 注册初始化器
        manager.registerInitializers(initializers);
    }
    
    /**
     * 测试获取排序后的初始化器列表
     */
    @Test
    void testGetSortedInitializers() {
        List<ModuleInitializer> sortedInitializers = manager.getSortedInitializers();
        
        // 验证排序顺序：优先级高的先执行，依赖的模块先执行
        assertEquals(3, sortedInitializers.size());
        assertEquals("A", sortedInitializers.get(0).getName());
        assertEquals("B", sortedInitializers.get(1).getName());
        assertEquals("C", sortedInitializers.get(2).getName());
    }
    
    /**
     * 测试循环依赖检测
     */
    @Test
    void testCircularDependencyDetection() {
        // 创建循环依赖：A -> D -> A
        TestModuleInitializer moduleD = new TestModuleInitializer("D", 40, "A");
        moduleA = new TestModuleInitializer("A", 10, "D");
        
        initializers.clear();
        initializers.add(moduleA);
        initializers.add(moduleD);
        
        // 重新注册初始化器
        manager = new StartupOrderManager();
        manager.registerInitializers(initializers);
        
        // 验证循环依赖检测
        assertThrows(IllegalStateException.class, () -> manager.getSortedInitializers());
    }
    
    /**
     * 测试初始化所有模块
     */
    @Test
    void testInitializeAll() throws Exception {
        // 执行初始化
        manager.initializeAll();
        
        // 验证初始化状态
        assertTrue(moduleA.isBeforeInitializeCalled());
        assertTrue(moduleA.isInitializeCalled());
        assertTrue(moduleA.isAfterInitializeCalled());
        assertTrue(moduleA.isInitialized());
        
        assertTrue(moduleB.isBeforeInitializeCalled());
        assertTrue(moduleB.isInitializeCalled());
        assertTrue(moduleB.isAfterInitializeCalled());
        assertTrue(moduleB.isInitialized());
        
        assertTrue(moduleC.isBeforeInitializeCalled());
        assertTrue(moduleC.isInitializeCalled());
        assertTrue(moduleC.isAfterInitializeCalled());
        assertTrue(moduleC.isInitialized());
    }
    
    /**
     * 测试初始化异常处理
     */
    @Test
    void testInitializeWithException() {
        // 设置模块B初始化时抛出异常
        moduleB.setExceptionToThrow(new RuntimeException("测试异常"));
        
        // 执行初始化，预期会抛出异常
        Exception exception = assertThrows(Exception.class, () -> manager.initializeAll());
        
        // 验证异常信息
        assertTrue(exception.getMessage().contains("测试异常"));
        
        // 验证初始化状态：A应该已初始化，B失败，C未初始化
        assertTrue(moduleA.isInitialized());
        assertFalse(moduleB.isInitialized());
        assertFalse(moduleC.isInitialized());
    }
    
    /**
     * 测试关闭所有模块
     */
    @Test
    void testShutdownAll() throws Exception {
        // 先初始化所有模块
        manager.initializeAll();
        
        // 执行关闭
        manager.shutdownAll();
        
        // 验证关闭状态：按照初始化的相反顺序关闭
        assertTrue(moduleA.isShutdownCalled());
        assertTrue(moduleB.isShutdownCalled());
        assertTrue(moduleC.isShutdownCalled());
        assertFalse(moduleA.isInitialized());
        assertFalse(moduleB.isInitialized());
        assertFalse(moduleC.isInitialized());
    }
}