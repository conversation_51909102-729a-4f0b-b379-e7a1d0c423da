#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据流集成测试专用运行器

专门用于执行数据流集成测试的运行器，提供详细的数据流性能分析和报告。
"""

import argparse
import asyncio
import sys
from pathlib import Path
from typing import Dict, Any, List
from datetime import datetime
from loguru import logger

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from tests.integration.config.integration_test_config import get_test_config
from tests.integration.utils.test_orchestrator import create_test_orchestrator
from tests.integration.reports.test_reporter import create_test_reporter, ReportConfig
from tests.integration.fixtures.mock_services import setup_mock_environment, teardown_mock_environment
from tests.integration.suites.data_flow_integration_test_suite import create_data_flow_integration_test_suite
from tests.integration.utils.data_flow_test_helpers import (
    DataFlowPerformanceMonitor,
    DataFlowValidator,
    create_standard_data_flow_scenarios
)


class DataFlowTestRunner:
    """数据流测试专用运行器"""
    
    def __init__(self, environment: str = "dev"):
        """
        初始化数据流测试运行器
        
        Args:
            environment: 测试环境
        """
        self.environment = environment
        self.config = get_test_config(environment)
        self.orchestrator = None
        self.reporter = None
        self.performance_monitor = DataFlowPerformanceMonitor()
        self.validator = DataFlowValidator()
        
        logger.info(f"数据流测试运行器已初始化 - 环境: {environment}")
    
    async def setup_test_environment(self) -> None:
        """设置测试环境"""
        logger.info("设置数据流测试环境...")
        
        # 设置Mock服务
        await setup_mock_environment()
        
        # 创建测试编排器
        self.orchestrator = create_test_orchestrator(
            max_workers=self.config.test_env.max_workers,
            default_timeout=self.config.test_env.test_timeout_seconds
        )
        
        # 创建报告生成器
        report_config = ReportConfig(
            output_dir=str(Path(__file__).parent / "reports" / "data_flow"),
            include_charts=True,
            include_logs=True,
            include_artifacts=True
        )
        self.reporter = create_test_reporter(report_config)
        
        logger.info("数据流测试环境设置完成")
    
    async def run_data_flow_tests(self, test_scenarios: List[str] = None) -> Dict[str, Any]:
        """
        运行数据流集成测试
        
        Args:
            test_scenarios: 要运行的测试场景列表
        
        Returns:
            Dict[str, Any]: 测试结果
        """
        logger.info("开始运行数据流集成测试...")
        
        try:
            # 设置环境
            await self.setup_test_environment()
            
            # 注册数据流测试套件
            data_flow_suite = create_data_flow_integration_test_suite()
            self.orchestrator.register_test_suite(data_flow_suite)
            
            # 开始性能监控
            self.performance_monitor.start_monitoring()
            
            # 添加进度回调
            def progress_callback(progress_info):
                logger.info(f"数据流测试进度: {progress_info['progress_percentage']:.1f}% "
                          f"({progress_info['completed_tests']}/{progress_info['total_tests']})")
                
                # 记录操作到性能监控器
                if progress_info['completed_tests'] > 0:
                    self.performance_monitor.record_operation(
                        success=progress_info['failed_tests'] == 0
                    )
            
            self.orchestrator.add_progress_callback(progress_callback)
            
            # 执行测试
            test_results = await self.orchestrator.execute_all_tests()
            
            # 停止性能监控
            performance_metrics = self.performance_monitor.stop_monitoring()
            
            # 获取执行摘要
            execution_summary = self.orchestrator.get_execution_summary()
            
            # 添加数据流特定的性能指标
            execution_summary['data_flow_metrics'] = {
                'avg_latency_ms': performance_metrics.latency_ms,
                'throughput_per_sec': performance_metrics.throughput_per_sec,
                'error_rate': performance_metrics.error_rate,
                'cache_hit_rate': performance_metrics.cache_hit_rate,
                'memory_usage_mb': performance_metrics.memory_usage_mb,
                'cpu_usage_percent': performance_metrics.cpu_usage_percent
            }
            
            # 验证性能阈值
            performance_check_results = self._validate_performance_thresholds(
                execution_summary['data_flow_metrics']
            )
            execution_summary['performance_validation'] = performance_check_results
            
            # 生成专门的数据流报告
            report_files = await self._generate_data_flow_reports(
                test_results, execution_summary
            )
            
            logger.info(f"数据流集成测试完成 - 成功率: {execution_summary['success_rate']:.1f}%")
            logger.info(f"平均延迟: {performance_metrics.latency_ms:.2f}ms")
            logger.info(f"吞吐量: {performance_metrics.throughput_per_sec:.2f} ops/sec")
            
            return {
                'test_results': test_results,
                'execution_summary': execution_summary,
                'performance_metrics': performance_metrics,
                'report_files': report_files,
                'success': execution_summary['failed_tests'] == 0 and performance_check_results['all_passed']
            }
            
        except Exception as e:
            logger.error(f"数据流测试执行失败: {str(e)}")
            raise
        
        finally:
            # 清理环境
            await teardown_mock_environment()
    
    def _validate_performance_thresholds(self, metrics: Dict[str, float]) -> Dict[str, Any]:
        """
        验证性能阈值
        
        Args:
            metrics: 性能指标
        
        Returns:
            Dict[str, Any]: 验证结果
        """
        thresholds = {
            'avg_latency_ms': self.config.performance.signal_generation_latency_ms,
            'error_rate': 5.0,  # 5%错误率阈值
            'memory_usage_mb': self.config.performance.memory_usage_mb,
            'cpu_usage_percent': self.config.performance.cpu_usage_percent,
            'cache_hit_rate': self.config.performance.cache_hit_rate_percent
        }
        
        validation_results = {
            'all_passed': True,
            'threshold_checks': {},
            'failed_checks': []
        }
        
        for metric, threshold in thresholds.items():
            actual_value = metrics.get(metric, 0)
            
            if metric == 'cache_hit_rate':
                # 缓存命中率应该高于阈值
                passed = actual_value >= threshold
            else:
                # 其他指标应该低于阈值
                passed = actual_value <= threshold
            
            validation_results['threshold_checks'][metric] = {
                'actual': actual_value,
                'threshold': threshold,
                'passed': passed
            }
            
            if not passed:
                validation_results['all_passed'] = False
                validation_results['failed_checks'].append({
                    'metric': metric,
                    'actual': actual_value,
                    'threshold': threshold,
                    'deviation': abs(actual_value - threshold)
                })
                
                logger.warning(f"性能阈值检查失败 {metric}: {actual_value} vs {threshold}")
        
        return validation_results
    
    async def _generate_data_flow_reports(self, test_results: Dict[str, Any], 
                                        execution_summary: Dict[str, Any]) -> Dict[str, str]:
        """
        生成数据流专用报告
        
        Args:
            test_results: 测试结果
            execution_summary: 执行摘要
        
        Returns:
            Dict[str, str]: 报告文件路径
        """
        # 生成标准报告
        report_files = self.reporter.generate_all_reports(
            test_results=test_results,
            execution_summary=execution_summary,
            report_name=f"data_flow_integration_{self.environment}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        )
        
        # 生成数据流性能分析报告
        performance_report_path = await self._generate_performance_analysis_report(
            execution_summary.get('data_flow_metrics', {}),
            execution_summary.get('performance_validation', {})
        )
        
        report_files['performance_analysis'] = performance_report_path
        
        return report_files
    
    async def _generate_performance_analysis_report(self, metrics: Dict[str, float], 
                                                  validation: Dict[str, Any]) -> str:
        """
        生成性能分析报告
        
        Args:
            metrics: 性能指标
            validation: 验证结果
        
        Returns:
            str: 报告文件路径
        """
        report_content = f"""
# 数据流性能分析报告

## 生成时间
{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 测试环境
- 环境: {self.environment}
- 最大工作线程: {self.config.test_env.max_workers}
- 测试超时: {self.config.test_env.test_timeout_seconds}秒

## 性能指标

### 延迟指标
- 平均延迟: {metrics.get('avg_latency_ms', 0):.2f} ms
- 阈值: {self.config.performance.signal_generation_latency_ms} ms
- 状态: {'✅ 通过' if metrics.get('avg_latency_ms', 0) <= self.config.performance.signal_generation_latency_ms else '❌ 超标'}

### 吞吐量指标
- 吞吐量: {metrics.get('throughput_per_sec', 0):.2f} ops/sec
- 错误率: {metrics.get('error_rate', 0):.2f}%
- 状态: {'✅ 通过' if metrics.get('error_rate', 0) <= 5.0 else '❌ 超标'}

### 资源使用指标
- 内存使用: {metrics.get('memory_usage_mb', 0):.2f} MB
- CPU使用: {metrics.get('cpu_usage_percent', 0):.2f}%
- 缓存命中率: {metrics.get('cache_hit_rate', 0):.2f}%

## 性能阈值验证

### 总体结果
{'✅ 所有阈值检查通过' if validation.get('all_passed', False) else '❌ 部分阈值检查失败'}

### 详细检查结果
"""
        
        for metric, check in validation.get('threshold_checks', {}).items():
            status = '✅ 通过' if check['passed'] else '❌ 失败'
            report_content += f"- {metric}: {check['actual']:.2f} (阈值: {check['threshold']:.2f}) - {status}\n"
        
        if validation.get('failed_checks'):
            report_content += "\n### 失败的检查项\n"
            for failed_check in validation['failed_checks']:
                report_content += f"- {failed_check['metric']}: 实际值 {failed_check['actual']:.2f}, 阈值 {failed_check['threshold']:.2f}, 偏差 {failed_check['deviation']:.2f}\n"
        
        report_content += f"""

## 建议

### 性能优化建议
"""
        
        # 根据性能指标提供建议
        if metrics.get('avg_latency_ms', 0) > self.config.performance.signal_generation_latency_ms:
            report_content += "- 延迟过高，建议优化数据处理算法或增加缓存\n"
        
        if metrics.get('error_rate', 0) > 5.0:
            report_content += "- 错误率过高，建议检查错误处理机制和重试策略\n"
        
        if metrics.get('cache_hit_rate', 0) < self.config.performance.cache_hit_rate_percent:
            report_content += "- 缓存命中率偏低，建议优化缓存策略和预加载机制\n"
        
        if metrics.get('memory_usage_mb', 0) > self.config.performance.memory_usage_mb:
            report_content += "- 内存使用过高，建议优化内存管理和数据结构\n"
        
        # 保存报告
        report_dir = Path(self.reporter.output_dir)
        report_file = report_dir / f"performance_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        logger.info(f"性能分析报告已生成: {report_file}")
        return str(report_file)
    
    async def run_performance_benchmark(self) -> Dict[str, Any]:
        """
        运行性能基准测试
        
        Returns:
            Dict[str, Any]: 基准测试结果
        """
        logger.info("开始运行数据流性能基准测试...")
        
        benchmark_results = {
            'baseline_metrics': {},
            'load_test_metrics': {},
            'stress_test_metrics': {},
            'recommendations': []
        }
        
        try:
            # 基线性能测试
            logger.info("执行基线性能测试...")
            baseline_result = await self.run_data_flow_tests(['baseline'])
            benchmark_results['baseline_metrics'] = baseline_result.get('performance_metrics', {})
            
            # 负载测试（模拟高负载）
            logger.info("执行负载测试...")
            # 这里可以增加负载测试的具体实现
            
            # 压力测试（模拟极限负载）
            logger.info("执行压力测试...")
            # 这里可以增加压力测试的具体实现
            
            logger.info("性能基准测试完成")
            
        except Exception as e:
            logger.error(f"性能基准测试失败: {str(e)}")
            raise
        
        return benchmark_results


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Crypto ML Strategy 数据流集成测试运行器")
    
    parser.add_argument(
        "--environment", "-e",
        choices=["dev", "staging", "prod-like"],
        default="dev",
        help="测试环境"
    )
    
    parser.add_argument(
        "--scenarios", "-s",
        nargs="*",
        help="要运行的测试场景"
    )
    
    parser.add_argument(
        "--benchmark", "-b",
        action="store_true",
        help="运行性能基准测试"
    )
    
    parser.add_argument(
        "--log-level", "-l",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="日志级别"
    )
    
    args = parser.parse_args()
    
    # 配置日志
    logger.remove()
    logger.add(
        sys.stdout,
        level=args.log_level,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
    )
    
    # 创建数据流测试运行器
    runner = DataFlowTestRunner(environment=args.environment)
    
    try:
        if args.benchmark:
            # 运行性能基准测试
            benchmark_results = await runner.run_performance_benchmark()
            
            print("\n" + "="*60)
            print("数据流性能基准测试结果")
            print("="*60)
            
            baseline = benchmark_results.get('baseline_metrics', {})
            if hasattr(baseline, 'latency_ms'):
                print(f"基线延迟: {baseline.latency_ms:.2f}ms")
                print(f"基线吞吐量: {baseline.throughput_per_sec:.2f} ops/sec")
                print(f"基线错误率: {baseline.error_rate:.2f}%")
            
            print("="*60)
            
        else:
            # 运行标准数据流测试
            result = await runner.run_data_flow_tests(test_scenarios=args.scenarios)
            
            # 输出结果摘要
            summary = result['execution_summary']
            metrics = result['performance_metrics']
            
            print("\n" + "="*60)
            print("数据流集成测试结果摘要")
            print("="*60)
            print(f"总测试数: {summary['total_tests']}")
            print(f"通过测试: {summary['passed_tests']}")
            print(f"失败测试: {summary['failed_tests']}")
            print(f"成功率: {summary['success_rate']:.1f}%")
            print(f"总耗时: {summary['total_duration']:.2f}秒")
            print()
            print("数据流性能指标:")
            print(f"  平均延迟: {metrics.latency_ms:.2f}ms")
            print(f"  吞吐量: {metrics.throughput_per_sec:.2f} ops/sec")
            print(f"  错误率: {metrics.error_rate:.2f}%")
            print(f"  缓存命中率: {metrics.cache_hit_rate:.2f}%")
            print(f"  内存使用: {metrics.memory_usage_mb:.2f}MB")
            print(f"  CPU使用: {metrics.cpu_usage_percent:.2f}%")
            print("="*60)
            
            # 输出报告文件路径
            print("\n报告文件:")
            for format_type, file_path in result['report_files'].items():
                print(f"  {format_type.upper()}: {file_path}")
            
            # 根据测试结果设置退出码
            sys.exit(0 if result['success'] else 1)
    
    except KeyboardInterrupt:
        logger.warning("数据流测试被用户中断")
        sys.exit(130)
    
    except Exception as e:
        logger.error(f"数据流测试运行失败: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())