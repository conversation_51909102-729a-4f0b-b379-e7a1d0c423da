package com.crypto.trading.market.listener;

import com.crypto.trading.market.processor.KlineDataProcessor;
import com.crypto.trading.sdk.response.model.KlineData;
import com.crypto.trading.sdk.response.model.WebSocketMessage;
import com.crypto.trading.sdk.websocket.BinanceWebSocketClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.List;
import java.util.function.Consumer;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * KlineDataListener单元测试
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class KlineDataListenerTest {

    @Mock
    private BinanceWebSocketClient webSocketClient;

    @Mock
    private KlineDataProcessor klineDataProcessor;

    @InjectMocks
    private KlineDataListener klineDataListener;

    private List<String> symbols;
    private List<String> klineIntervals;

    @BeforeEach
    void setUp() {
        // 在每次测试前重置所有mock
        clearInvocations(webSocketClient, klineDataProcessor);
        
        symbols = Arrays.asList("BTCUSDT", "ETHUSDT");
        klineIntervals = Arrays.asList("1m", "5m");
        ReflectionTestUtils.setField(klineDataListener, "symbols", symbols);
        ReflectionTestUtils.setField(klineDataListener, "klineIntervals", klineIntervals);
        
        // 使用lenient和doAnswer处理大小写不敏感的情况
        lenient().doAnswer(invocation -> {
            String symbol = invocation.getArgument(0);
            String interval = invocation.getArgument(1);
            if ("BTCUSDT".equalsIgnoreCase(symbol) && "1m".equals(interval)) return 1;
            if ("BTCUSDT".equalsIgnoreCase(symbol) && "5m".equals(interval)) return 2;
            if ("ETHUSDT".equalsIgnoreCase(symbol) && "1m".equals(interval)) return 3;
            if ("ETHUSDT".equalsIgnoreCase(symbol) && "5m".equals(interval)) return 4;
            return 0;
        }).when(webSocketClient).subscribeKline(anyString(), anyString(), any(Consumer.class));
    }

    @Test
    void testStartKlineDataSubscription() {
        // 调用被测试方法
        klineDataListener.startKlineDataSubscription();

        // 验证每个符号和间隔都订阅了，使用anyString()确保匹配
        verify(webSocketClient, times(4)).subscribeKline(anyString(), anyString(), any(Consumer.class));
    }

    @Test
    void testStopKlineDataSubscription() {
        // 首先启动订阅
        klineDataListener.startKlineDataSubscription();
        
        // 清除之前的调用记录，只关注stopKlineDataSubscription的效果
        clearInvocations(webSocketClient);
        
        // 调用被测试方法
        klineDataListener.stopKlineDataSubscription();

        // 验证每个连接都被关闭
        verify(webSocketClient).closeConnection(1);
        verify(webSocketClient).closeConnection(2);
        verify(webSocketClient).closeConnection(3);
        verify(webSocketClient).closeConnection(4);
    }

    @Test
    void testKlineMessageHandler() {
        // 捕获订阅时传递的消息处理器
        ArgumentCaptor<Consumer<String>> handlerCaptor = ArgumentCaptor.forClass(Consumer.class);
        
        // 通过调用startKlineDataSubscription来获取Handler
        klineDataListener.startKlineDataSubscription();
        
        // 验证方法调用并捕获参数
        verify(webSocketClient, times(4)).subscribeKline(anyString(), anyString(), handlerCaptor.capture());
        
        // 获取第一个捕获的Consumer（BTCUSDT_1m的Handler）
        Consumer<String> handler = handlerCaptor.getAllValues().get(0);
        
        // 测试有效的K线消息
        String validMessage = "{\"e\":\"kline\",\"E\":1625128665000,\"s\":\"BTCUSDT\",\"k\":{\"t\":1625128620000,\"T\":1625128679999,\"s\":\"BTCUSDT\",\"i\":\"1m\",\"f\":100,\"L\":200,\"o\":\"35000.00\",\"c\":\"35050.00\",\"h\":\"35100.00\",\"l\":\"34900.00\",\"v\":\"10.5\",\"n\":150,\"x\":true,\"q\":\"367500.00\",\"V\":\"5.2\",\"Q\":\"182000.00\",\"B\":\"0\"}}";
        
        // 执行处理器
        handler.accept(validMessage);
        
        // 验证处理器被调用
        verify(klineDataProcessor, times(1)).processKlineData(any(WebSocketMessage.class), anyString());
        
        // 清除之前的调用记录
        clearInvocations(klineDataProcessor);
        
        // 测试无效的消息类型
        String invalidTypeMessage = "{\"e\":\"trade\",\"E\":1625128665000,\"s\":\"BTCUSDT\"}";
        
        // 执行处理器，不应该抛出异常
        try {
            handler.accept(invalidTypeMessage);
        } catch (Exception e) {
            fail("处理器不应该抛出异常: " + e.getMessage());
        }
        
        // 验证处理器没有被调用（因为消息类型不匹配）
        verify(klineDataProcessor, never()).processKlineData(any(WebSocketMessage.class), any());
        
        // 测试格式错误的消息
        String malformedMessage = "not a json message";
        
        // 执行处理器，不应该抛出异常
        try {
            handler.accept(malformedMessage);
        } catch (Exception e) {
            fail("处理器不应该抛出异常: " + e.getMessage());
        }
        
        // 验证处理器没有被调用（因为格式错误）
        verify(klineDataProcessor, never()).processKlineData(any(WebSocketMessage.class), any());
    }
} 