package com.crypto.trading.market.config;

import com.influxdb.client.InfluxDBClient;
import com.influxdb.client.InfluxDBClientFactory;
import com.influxdb.client.InfluxDBClientOptions;
import com.influxdb.client.WriteApi;
import com.influxdb.client.WriteOptions;
import com.influxdb.client.domain.Bucket;
import com.influxdb.client.domain.BucketRetentionRules;
import com.influxdb.client.domain.WritePrecision;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import okhttp3.OkHttpClient;
import java.util.concurrent.TimeUnit;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.concurrent.TimeUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * InfluxDB配置
 * 负责提供InfluxDB相关的配置和连接
 */
@Configuration(value = "marketInfluxDBConfig")
@Primary
public class InfluxDBConfig {

    private static final Logger log = LoggerFactory.getLogger(InfluxDBConfig.class);

    /**
     * 根级别的InfluxDB URL配置（优先级更高）
     */
    @Value("${influxdb.url:}")
    private String rootUrl;
    
    /**
     * 根级别的InfluxDB token配置（优先级更高）
     */
    @Value("${influxdb.token:}")
    private String rootToken;
    
    /**
     * 根级别的InfluxDB org配置（优先级更高）
     */
    @Value("${influxdb.org:}")
    private String rootOrg;
    
    /**
     * 根级别的InfluxDB bucket配置（优先级更高）
     */
    @Value("${influxdb.bucket:}")
    private String rootBucket;
    
    /**
     * InfluxDB URL
     */
    @Value("${market.influxdb.url:http://localhost:8086}")
    private String url;

    /**
     * InfluxDB 令牌
     */
    @Value("${market.influxdb.token:}")
    private String token;

    /**
     * InfluxDB 组织
     */
    @Value("${market.influxdb.org:crypto}")
    private String org;

    /**
     * InfluxDB 存储
     */
    @Value("${market.influxdb.bucket:market_data}")
    private String bucket;
    
    /**
     * 高精度数据保留时间（小时）
     * 默认168小时（7天）
     */
    @Value("${market.influxdb.retention.high-precision:168}")
    private int highPrecisionRetentionHours;
    
    /**
     * 中精度数据保留时间（天）
     * 默认30天
     */
    @Value("${market.influxdb.retention.medium-precision:30}")
    private int mediumPrecisionRetentionDays;
    
    /**
     * 低精度数据保留时间（天）
     * 默认365天
     */
    @Value("${market.influxdb.retention.low-precision:365}")
    private int lowPrecisionRetentionDays;
    
    /**
     * 批量写入缓冲区大小
     */
    @Value("${market.influxdb.write.buffer-size:10000}")
    private int writeBufferSize;
    
    /**
     * 批量写入批次大小
     */
    @Value("${market.influxdb.write.batch-size:5000}")
    private int writeBatchSize;
    
    /**
     * 批量写入刷新间隔（毫秒）
     */
    @Value("${market.influxdb.write.flush-interval:1000}")
    private int writeFlushInterval;
    
    /**
     * 写入精度
     */
    @Value("${market.influxdb.write.precision:MS}")
    private String writePrecision;
    
    /**
     * 重试策略最大重试次数
     */
    @Value("${market.influxdb.write.retry-max:5}")
    private int writeRetryMax;
    
    /**
     * 重试策略初始延迟（毫秒）
     */
    @Value("${market.influxdb.write.retry-delay:1000}")
    private int writeRetryDelay;
    
    /**
     * 是否启用压缩
     */
    @Value("${market.influxdb.write.enable-gzip:true}")
    private boolean enableGzip;
    
    /**
     * 连接超时（毫秒）
     */
    @Value("${market.influxdb.connect-timeout:30000}")
    private int connectTimeout;
    
    /**
     * 读取超时（毫秒）
     */
    @Value("${market.influxdb.read-timeout:120000}")
    private int readTimeout;
    
    /**
     * 写入超时（毫秒）
     */
    @Value("${market.influxdb.write-timeout:60000}")
    private int writeTimeout;

    /**
     * 配置InfluxDB客户端
     *
     * @return InfluxDB客户端
     */
    @Bean(name = "marketInfluxDBClient")
    @ConditionalOnMissingBean(name = "marketInfluxDBClient")
    public InfluxDBClient influxDBClient() {
        // 优先使用根级别配置，如果根级别为空则使用market.influxdb级别的配置
        String effectiveUrl = !rootUrl.isEmpty() ? rootUrl : url;
        String effectiveToken = !rootToken.isEmpty() ? rootToken : token;
        String effectiveOrg = !rootOrg.isEmpty() ? rootOrg : org;
        String effectiveBucket = !rootBucket.isEmpty() ? rootBucket : bucket;
        
        log.info("创建InfluxDB客户端 url={}, org={}, bucket={}", effectiveUrl, effectiveOrg, effectiveBucket);
        
        try {
            InfluxDBClientOptions.Builder optionsBuilder = InfluxDBClientOptions.builder()
                    .url(effectiveUrl)
                    .org(effectiveOrg)
                    .bucket(effectiveBucket)
                    .okHttpClient(new OkHttpClient.Builder()
                        .connectTimeout(connectTimeout, TimeUnit.MILLISECONDS)
                        .readTimeout(readTimeout, TimeUnit.MILLISECONDS)
                        .writeTimeout(writeTimeout, TimeUnit.MILLISECONDS));
            
            // 只有当token不为空时才添加token认证
            if (effectiveToken != null && !effectiveToken.isEmpty()) {
                optionsBuilder.authenticateToken(effectiveToken.toCharArray());
                log.info("使用Token认证模式连接InfluxDB");
            } else {
                log.info("使用无认证模式连接InfluxDB");
            }
            
            // 创建客户端
            InfluxDBClient client = InfluxDBClientFactory.create(optionsBuilder.build());
            
            // 测试连接，确保配置正确
            try {
                // 尝试获取健康状态
                boolean isHealthy = client.health().getStatus() == com.influxdb.client.domain.HealthCheck.StatusEnum.PASS;
                log.info("InfluxDB连接测试: {}", isHealthy ? "成功" : "失败但已创建客户端");
            } catch (Exception e) {
                log.warn("InfluxDB连接测试失败，但将继续使用客户端: {}", e.getMessage());
            }

            return client;
        } catch (Exception e) {
            log.error("创建InfluxDB客户端失败 {}", e.getMessage(), e);
            // 创建一个无操作的客户端，避免应用程序崩溃，但所有操作都将被忽略
            log.warn("返回空InfluxDB客户端，数据将不会写入InfluxDB，但应用程序将继续运行");
            return null;
        }
    }

    /**
     * 配置InfluxDB写入API
     *
     * @param influxDBClient InfluxDB客户端
     * @return InfluxDB写入API
     */
    @Bean(name = "marketWriteApi")
    @ConditionalOnMissingBean(name = "marketWriteApi")
    public WriteApi writeApi(InfluxDBClient influxDBClient) {
        log.info("创建InfluxDB写入API, 批次大小: {}, 刷新间隔: {}ms", writeBatchSize, writeFlushInterval);

        // 创建高级写入选项
        WriteOptions writeOptions = WriteOptions.builder()
                .batchSize(writeBatchSize)
                .flushInterval(writeFlushInterval)
                .bufferLimit(writeBufferSize)
                .jitterInterval(500)
                .retryInterval(writeRetryDelay)
                .maxRetries(writeRetryMax)
                .maxRetryDelay(30_000) // 最大重试延迟30秒
                .exponentialBase(2) // 指数退避基数
                .build();

        return influxDBClient.makeWriteApi(writeOptions);
    }

    /**
     * 获取写入精度
     *
     * @return 写入精度
     */
    @Bean(name = "marketWritePrecision")
    @ConditionalOnMissingBean(name = "marketWritePrecision")
    public WritePrecision writePrecision() {
        try {
            return WritePrecision.valueOf(writePrecision);
        } catch (IllegalArgumentException e) {
            log.warn("无效的写入精度 {}, 使用默认? MS", writePrecision);
            return WritePrecision.MS;
        }
    }

    /**
     * 获取InfluxDB URL
     *
     * @return InfluxDB URL
     */
    public String getUrl() {
        return !rootUrl.isEmpty() ? rootUrl : url;
    }

    /**
     * 获取InfluxDB 令牌
     *
     * @return InfluxDB 令牌
     */
    public String getToken() {
        return !rootToken.isEmpty() ? rootToken : token;
    }

    /**
     * 获取InfluxDB 组织
     *
     * @return InfluxDB 组织
     */
    public String getOrg() {
        return !rootOrg.isEmpty() ? rootOrg : org;
    }

    /**
     * 获取InfluxDB 存储
     *
     * @return InfluxDB 存储
     */
    public String getBucket() {
        return !rootBucket.isEmpty() ? rootBucket : bucket;
    }

    /**
     * 获取高精度数据保留时间（小时）
     *
     * @return 高精度数据保留时间（小时）
     */
    public int getHighPrecisionRetentionHours() {
        return highPrecisionRetentionHours;
    }

    /**
     * 获取中精度数据保留时间（天）
     *
     * @return 中精度数据保留时间（天）
     */
    public int getMediumPrecisionRetentionDays() {
        return mediumPrecisionRetentionDays;
    }

    /**
     * 获取低精度数据保留时间（天）
     *
     * @return 低精度数据保留时间（天）
     */
    public int getLowPrecisionRetentionDays() {
        return lowPrecisionRetentionDays;
    }

    /**
     * 确保必要的存储桶存在
     * 
     * @param influxDBClient InfluxDB客户端
     * @return 是否成功创建或验证了所有必要的存储桶
     */
    @Bean(name = "influxDBBucketsInitializer")
    @ConditionalOnMissingBean(name = "influxDBBucketsInitializer")
    public boolean ensureRequiredBucketsExist(InfluxDBClient influxDBClient) {
        try {
            // 如果客户端为null，表示前面创建过程可能已经失败，此时直接返回true允许应用继续启动
            if (influxDBClient == null) {
                log.warn("InfluxDB客户端为null，无法创建存储桶，但将允许应用程序继续运行");
                return true;
            }
            
            log.info("开始检查并创建必要的InfluxDB存储桶");

            // 获取组织ID
            String orgID;
            try {
                orgID = influxDBClient.getOrganizationsApi().findOrganizations().stream()
                        .filter(org -> org.getName().equals(this.org))
                        .findFirst()
                        .orElseThrow(() -> new RuntimeException("组织不存在: " + this.org))
                        .getId();
                
                log.info("获取到组织ID: {}", orgID);
            } catch (com.influxdb.exceptions.UnauthorizedException e) {
                log.warn("InfluxDB认证失败，无法获取组织信息: {}。应用程序将继续运行，但不会创建存储桶。", e.getMessage());
                return true; // 允许应用程序继续运行
            } catch (Exception e) {
                log.warn("获取组织信息时发生错误: {}。应用程序将继续运行，但不会创建存储桶。", e.getMessage());
                return true; // 允许应用程序继续运行
            }

            // 获取现有存储桶
            List<Bucket> existingBuckets;
            try {
                existingBuckets = influxDBClient.getBucketsApi().findBuckets();
                Set<String> existingBucketNames = existingBuckets.stream()
                        .map(Bucket::getName)
                        .collect(Collectors.toSet());

                log.info("现有存储桶: {}", existingBucketNames);

                // 确保存储桶存在
                ensureBucketExists(influxDBClient, existingBucketNames, bucket, orgID, 0); // 主存储桶，无限期保留
                ensureBucketExists(influxDBClient, existingBucketNames, bucket + "_highres", orgID, highPrecisionRetentionHours * 3600);
                ensureBucketExists(influxDBClient, existingBucketNames, bucket + "_midres", orgID, mediumPrecisionRetentionDays * 86400);
                ensureBucketExists(influxDBClient, existingBucketNames, bucket + "_lowres", orgID, lowPrecisionRetentionDays * 86400);

                log.info("InfluxDB存储桶检查和创建完成");
            } catch (com.influxdb.exceptions.UnauthorizedException e) {
                log.warn("InfluxDB认证失败，无法获取或创建存储桶: {}。应用程序将继续运行，但不会创建存储桶。", e.getMessage());
                return true; // 允许应用程序继续运行
            } catch (Exception e) {
                log.warn("获取或创建存储桶时发生错误: {}。应用程序将继续运行，但功能可能受限。", e.getMessage());
                return true; // 允许应用程序继续运行
            }

            return true;

        } catch (Exception e) {
            log.error("创建InfluxDB存储桶时出错: {}", e.getMessage(), e);
            // 即使出错也返回true，允许应用程序继续运行
            return true;
        }
    }

    /**
     * 确保特定的存储桶存在
     *
     * @param influxDBClient InfluxDB客户端
     * @param existingBuckets 现有存储桶列表
     * @param bucketName 存储桶名
     * @param orgID 组织ID
     * @param retentionSeconds 保留秒数 (0表示无限期保留)
     */
    private void ensureBucketExists(InfluxDBClient influxDBClient, Set<String> existingBuckets,
                                   String bucketName, String orgID, int retentionSeconds) {
        if (!existingBuckets.contains(bucketName)) {
            log.info("创建存储桶 {}, 保留时间: {} 秒", bucketName, retentionSeconds > 0 ? retentionSeconds : "无限期");
            
            try {
                if (retentionSeconds > 0) {
                    // 创建有保留策略的存储桶
                    BucketRetentionRules retentionRules = new BucketRetentionRules();
                    retentionRules.setEverySeconds(Integer.valueOf(retentionSeconds));
                    
                    // 创建存储桶
                    influxDBClient.getBucketsApi().createBucket(bucketName, retentionRules, orgID);
                } else {
                    // 创建无保留策略的存储桶
                    influxDBClient.getBucketsApi().createBucket(bucketName, orgID);
                }
                
                log.info("存储桶创建成功 {}", bucketName);
            } catch (Exception e) {
                log.error("创建存储桶失败 {}, 错误: {}", bucketName, e.getMessage(), e);
            }
        } else {
            log.info("存储桶已存在: {}", bucketName);
        }
    }
}
