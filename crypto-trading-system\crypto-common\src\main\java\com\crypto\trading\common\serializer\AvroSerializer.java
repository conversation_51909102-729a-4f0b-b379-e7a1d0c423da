package com.crypto.trading.common.serializer;

import io.confluent.kafka.serializers.KafkaAvroSerializer;
import org.apache.kafka.common.serialization.Serializer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * Avro格式序列化器
 * <p>
 * 使用Apache Avro格式序列化数据，提供更高效的二进制序列化和模式演化支持。
 * 底层使用Confluent的KafkaAvroSerializer实现，支持Schema Registry集成。
 * </p>
 *
 * @param <T> 待序列化的数据类型
 */
public class AvroSerializer<T> implements Serializer<T> {

    private static final Logger log = LoggerFactory.getLogger(AvroSerializer.class);
    
    private final KafkaAvroSerializer avroSerializer;

    /**
     * 默认构造函数
     */
    public AvroSerializer() {
        this.avroSerializer = new KafkaAvroSerializer();
    }

    /**
     * 配置序列化器
     *
     * @param configs 配置参数
     * @param isKey   是否为键序列化器
     */
    @Override
    public void configure(Map<String, ?> configs, boolean isKey) {
        avroSerializer.configure(configs, isKey);
        log.info("Avro序列化器已配置: isKey={}", isKey);
    }

    /**
     * 序列化数据
     *
     * @param topic 目标主题
     * @param data  待序列化的数据
     * @return 序列化后的字节数组
     */
    @Override
    public byte[] serialize(String topic, T data) {
        try {
            if (data == null) {
                return null;
            }
            byte[] bytes = avroSerializer.serialize(topic, data);
            if (log.isDebugEnabled()) {
                log.debug("序列化数据成功: topic={}, dataType={}, size={}bytes", 
                        topic, data.getClass().getSimpleName(), bytes != null ? bytes.length : 0);
            }
            return bytes;
        } catch (Exception e) {
            log.error("序列化数据失败: topic={}, dataType={}", 
                    topic, data != null ? data.getClass().getSimpleName() : "null", e);
            throw e;
        }
    }

    /**
     * 关闭序列化器资源
     */
    @Override
    public void close() {
        avroSerializer.close();
        log.info("Avro序列化器已关闭");
    }
}