"""
预测服务模块

提供统一的预测服务接口，支持Kafka消息处理、结果分发、
服务状态监控和与Java模块的通信接口。

Author: Crypto ML Strategy Team
Date: 2024-12-19
"""

import asyncio
import json
import threading
import time
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime, timedelta
import logging
from dataclasses import dataclass, field
import uuid
from concurrent.futures import ThreadPoolExecutor
import queue

from .training_config import TrainingPipelineConfig, PredictionResult
from .prediction_engine import PredictionEngine
from .model_trainer import ModelTrainer
from .model_manager import ModelManager

logger = logging.getLogger(__name__)


@dataclass
class ServiceRequest:
    """服务请求数据类"""
    request_id: str
    request_type: str  # 'predict', 'train', 'status'
    payload: Dict[str, Any]
    timestamp: datetime = field(default_factory=datetime.now)
    priority: int = 1  # 1=high, 2=medium, 3=low
    callback: Optional[Callable] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'request_id': self.request_id,
            'request_type': self.request_type,
            'payload': self.payload,
            'timestamp': self.timestamp.isoformat(),
            'priority': self.priority
        }


@dataclass
class ServiceResponse:
    """服务响应数据类"""
    request_id: str
    success: bool
    result: Any = None
    error_message: Optional[str] = None
    processing_time: float = 0.0
    timestamp: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'request_id': self.request_id,
            'success': self.success,
            'result': self.result,
            'error_message': self.error_message,
            'processing_time': self.processing_time,
            'timestamp': self.timestamp.isoformat()
        }


class PredictionService:
    """预测服务类"""
    
    def __init__(self, config: TrainingPipelineConfig):
        """
        初始化预测服务
        
        Args:
            config: 训练管道配置
        """
        self.config = config
        
        # 初始化核心组件
        self.prediction_engine = PredictionEngine(config)
        self.model_trainer = ModelTrainer(config)
        self.model_manager = ModelManager(config)
        
        # 服务状态
        self.is_running = False
        self.service_id = str(uuid.uuid4())
        self.start_time: Optional[datetime] = None
        
        # 请求队列
        self.request_queue = queue.PriorityQueue()
        self.response_callbacks: Dict[str, Callable] = {}
        
        # 线程池
        self.executor = ThreadPoolExecutor(max_workers=8)
        
        # 服务统计
        self.service_stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'average_response_time': 0.0,
            'active_requests': 0,
            'last_request_time': None
        }
        
        # 健康检查
        self.health_status = {
            'status': 'healthy',
            'last_check': datetime.now(),
            'components': {
                'prediction_engine': 'healthy',
                'model_trainer': 'healthy',
                'model_manager': 'healthy'
            }
        }
        
        # 锁
        self._lock = threading.RLock()
        self._stop_event = threading.Event()
        
        # 工作线程
        self._worker_threads: List[threading.Thread] = []
        
        logger.info(f"Prediction service initialized: {self.service_id}")
    
    def start(self) -> bool:
        """启动预测服务"""
        try:
            with self._lock:
                if self.is_running:
                    logger.warning("Prediction service is already running")
                    return False
                
                self.is_running = True
                self.start_time = datetime.now()
                self._stop_event.clear()
                
                # 启动工作线程
                self._start_worker_threads()
                
                # 启动健康检查线程
                health_thread = threading.Thread(
                    target=self._health_check_loop,
                    name="HealthCheck",
                    daemon=True
                )
                health_thread.start()
                
                logger.info("Prediction service started successfully")
                return True
                
        except Exception as e:
            logger.error(f"Error starting prediction service: {e}")
            return False
    
    def stop(self) -> bool:
        """停止预测服务"""
        try:
            with self._lock:
                if not self.is_running:
                    logger.warning("Prediction service is not running")
                    return False
                
                self.is_running = False
                self._stop_event.set()
                
                # 等待工作线程结束
                for thread in self._worker_threads:
                    if thread.is_alive():
                        thread.join(timeout=5.0)
                
                # 清理资源
                self.prediction_engine.clear_cache()
                self.model_manager.clear_cache()
                
                logger.info("Prediction service stopped successfully")
                return True
                
        except Exception as e:
            logger.error(f"Error stopping prediction service: {e}")
            return False
    
    def _start_worker_threads(self) -> None:
        """启动工作线程"""
        try:
            # 启动请求处理线程
            for i in range(4):
                worker_thread = threading.Thread(
                    target=self._request_worker,
                    name=f"RequestWorker-{i}",
                    daemon=True
                )
                worker_thread.start()
                self._worker_threads.append(worker_thread)
            
            logger.info(f"Started {len(self._worker_threads)} worker threads")
            
        except Exception as e:
            logger.error(f"Error starting worker threads: {e}")
    
    def _request_worker(self) -> None:
        """请求处理工作线程"""
        try:
            while not self._stop_event.is_set():
                try:
                    # 从队列获取请求（带超时）
                    priority, request = self.request_queue.get(timeout=1.0)
                    
                    if request is None:
                        continue
                    
                    # 处理请求
                    self._process_request(request)
                    
                    # 标记任务完成
                    self.request_queue.task_done()
                    
                except queue.Empty:
                    continue
                except Exception as e:
                    logger.error(f"Error in request worker: {e}")
                    
        except Exception as e:
            logger.error(f"Fatal error in request worker: {e}")
    
    def _process_request(self, request: ServiceRequest) -> None:
        """处理单个请求"""
        start_time = time.time()
        
        try:
            with self._lock:
                self.service_stats['active_requests'] += 1
                self.service_stats['total_requests'] += 1
                self.service_stats['last_request_time'] = datetime.now()
            
            logger.info(f"Processing request: {request.request_id} ({request.request_type})")
            
            # 根据请求类型处理
            if request.request_type == 'predict':
                result = asyncio.run(self._handle_predict_request(request))
            elif request.request_type == 'train':
                result = asyncio.run(self._handle_train_request(request))
            elif request.request_type == 'status':
                result = self._handle_status_request(request)
            elif request.request_type == 'health':
                result = self._handle_health_request(request)
            else:
                raise ValueError(f"Unknown request type: {request.request_type}")
            
            # 创建成功响应
            processing_time = time.time() - start_time
            response = ServiceResponse(
                request_id=request.request_id,
                success=True,
                result=result,
                processing_time=processing_time
            )
            
            # 更新统计
            with self._lock:
                self.service_stats['successful_requests'] += 1
                self._update_average_response_time(processing_time)
            
            logger.info(f"Request completed: {request.request_id}, time: {processing_time:.3f}s")
            
        except Exception as e:
            # 创建错误响应
            processing_time = time.time() - start_time
            response = ServiceResponse(
                request_id=request.request_id,
                success=False,
                error_message=str(e),
                processing_time=processing_time
            )
            
            # 更新统计
            with self._lock:
                self.service_stats['failed_requests'] += 1
                self._update_average_response_time(processing_time)
            
            logger.error(f"Request failed: {request.request_id}, error: {e}")
        
        finally:
            with self._lock:
                self.service_stats['active_requests'] -= 1
            
            # 调用回调函数
            if request.callback:
                try:
                    request.callback(response)
                except Exception as e:
                    logger.error(f"Error in response callback: {e}")
    
    async def _handle_predict_request(self, request: ServiceRequest) -> Dict[str, Any]:
        """处理预测请求"""
        try:
            payload = request.payload
            
            # 提取参数
            model_id = payload.get('model_id')
            symbol = payload.get('symbol')
            timestamp = payload.get('timestamp')
            include_risk_assessment = payload.get('include_risk_assessment', False)
            
            if not model_id or not symbol:
                raise ValueError("model_id and symbol are required")
            
            # 解析时间戳
            if timestamp:
                if isinstance(timestamp, str):
                    timestamp = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            else:
                timestamp = datetime.now()
            
            # 执行预测
            if include_risk_assessment:
                result = await self.prediction_engine.predict_with_risk_assessment(
                    model_id, symbol, timestamp
                )
            else:
                prediction_result = await self.prediction_engine.predict_single(
                    model_id, symbol, timestamp
                )
                result = prediction_result.to_dict()
            
            return result
            
        except Exception as e:
            logger.error(f"Error handling predict request: {e}")
            raise
    
    async def _handle_train_request(self, request: ServiceRequest) -> Dict[str, Any]:
        """处理训练请求"""
        try:
            payload = request.payload
            
            # 提取参数
            symbol = payload.get('symbol')
            start_time = payload.get('start_time')
            end_time = payload.get('end_time')
            target_column = payload.get('target_column', 'target')
            optimize_hyperparameters = payload.get('optimize_hyperparameters', True)
            
            if not symbol or not start_time or not end_time:
                raise ValueError("symbol, start_time, and end_time are required")
            
            # 解析时间戳
            if isinstance(start_time, str):
                start_time = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
            if isinstance(end_time, str):
                end_time = datetime.fromisoformat(end_time.replace('Z', '+00:00'))
            
            # 执行训练
            training_result = await self.model_trainer.train_model(
                symbol, start_time, end_time, target_column, optimize_hyperparameters
            )
            
            return training_result.to_dict()
            
        except Exception as e:
            logger.error(f"Error handling train request: {e}")
            raise
    
    def _handle_status_request(self, request: ServiceRequest) -> Dict[str, Any]:
        """处理状态请求"""
        try:
            return self.get_service_status()
        except Exception as e:
            logger.error(f"Error handling status request: {e}")
            raise
    
    def _handle_health_request(self, request: ServiceRequest) -> Dict[str, Any]:
        """处理健康检查请求"""
        try:
            return self.get_health_status()
        except Exception as e:
            logger.error(f"Error handling health request: {e}")
            raise
    
    def _update_average_response_time(self, response_time: float) -> None:
        """更新平均响应时间"""
        try:
            total_requests = self.service_stats['total_requests']
            current_avg = self.service_stats['average_response_time']
            
            new_avg = (current_avg * (total_requests - 1) + response_time) / total_requests
            self.service_stats['average_response_time'] = new_avg
            
        except Exception as e:
            logger.error(f"Error updating average response time: {e}")
    
    def _health_check_loop(self) -> None:
        """健康检查循环"""
        try:
            while not self._stop_event.is_set():
                try:
                    self._perform_health_check()
                    time.sleep(30)  # 每30秒检查一次
                except Exception as e:
                    logger.error(f"Error in health check: {e}")
                    time.sleep(5)
                    
        except Exception as e:
            logger.error(f"Fatal error in health check loop: {e}")
    
    def _perform_health_check(self) -> None:
        """执行健康检查"""
        try:
            # 检查各组件状态
            components_status = {}
            
            # 检查预测引擎
            try:
                stats = self.prediction_engine.get_prediction_statistics()
                components_status['prediction_engine'] = 'healthy' if 'error' not in stats else 'unhealthy'
            except Exception:
                components_status['prediction_engine'] = 'unhealthy'
            
            # 检查模型管理器
            try:
                stats = self.model_manager.get_model_statistics()
                components_status['model_manager'] = 'healthy' if 'error' not in stats else 'unhealthy'
            except Exception:
                components_status['model_manager'] = 'unhealthy'
            
            # 检查模型训练器
            try:
                status = self.model_trainer.get_training_status()
                components_status['model_trainer'] = 'healthy' if 'error' not in status else 'unhealthy'
            except Exception:
                components_status['model_trainer'] = 'unhealthy'
            
            # 更新健康状态
            unhealthy_components = [k for k, v in components_status.items() if v == 'unhealthy']
            
            with self._lock:
                self.health_status['components'] = components_status
                self.health_status['last_check'] = datetime.now()
                
                if unhealthy_components:
                    self.health_status['status'] = 'degraded'
                    self.health_status['unhealthy_components'] = unhealthy_components
                else:
                    self.health_status['status'] = 'healthy'
                    self.health_status.pop('unhealthy_components', None)
            
        except Exception as e:
            logger.error(f"Error performing health check: {e}")
            with self._lock:
                self.health_status['status'] = 'unhealthy'
                self.health_status['error'] = str(e)
    
    def submit_request(
        self,
        request_type: str,
        payload: Dict[str, Any],
        priority: int = 2,
        callback: Optional[Callable] = None
    ) -> str:
        """
        提交请求
        
        Args:
            request_type: 请求类型
            payload: 请求载荷
            priority: 优先级
            callback: 回调函数
            
        Returns:
            请求ID
        """
        try:
            request_id = str(uuid.uuid4())
            
            request = ServiceRequest(
                request_id=request_id,
                request_type=request_type,
                payload=payload,
                priority=priority,
                callback=callback
            )
            
            # 添加到队列
            self.request_queue.put((priority, request))
            
            logger.info(f"Request submitted: {request_id} ({request_type})")
            return request_id
            
        except Exception as e:
            logger.error(f"Error submitting request: {e}")
            raise
    
    async def submit_request_async(
        self,
        request_type: str,
        payload: Dict[str, Any],
        priority: int = 2,
        timeout: float = 30.0
    ) -> ServiceResponse:
        """
        异步提交请求并等待结果
        
        Args:
            request_type: 请求类型
            payload: 请求载荷
            priority: 优先级
            timeout: 超时时间
            
        Returns:
            服务响应
        """
        try:
            # 创建Future对象
            future = asyncio.Future()
            
            def callback(response: ServiceResponse):
                if not future.done():
                    future.set_result(response)
            
            # 提交请求
            request_id = self.submit_request(request_type, payload, priority, callback)
            
            # 等待结果
            try:
                response = await asyncio.wait_for(future, timeout=timeout)
                return response
            except asyncio.TimeoutError:
                logger.error(f"Request timeout: {request_id}")
                return ServiceResponse(
                    request_id=request_id,
                    success=False,
                    error_message="Request timeout"
                )
                
        except Exception as e:
            logger.error(f"Error in async request: {e}")
            return ServiceResponse(
                request_id="",
                success=False,
                error_message=str(e)
            )
    
    def get_service_status(self) -> Dict[str, Any]:
        """获取服务状态"""
        try:
            with self._lock:
                uptime = None
                if self.start_time:
                    uptime = (datetime.now() - self.start_time).total_seconds()
                
                return {
                    'service_id': self.service_id,
                    'is_running': self.is_running,
                    'start_time': self.start_time.isoformat() if self.start_time else None,
                    'uptime_seconds': uptime,
                    'queue_size': self.request_queue.qsize(),
                    'worker_threads': len(self._worker_threads),
                    'statistics': self.service_stats.copy(),
                    'health': self.health_status.copy()
                }
                
        except Exception as e:
            logger.error(f"Error getting service status: {e}")
            return {'error': str(e)}
    
    def get_health_status(self) -> Dict[str, Any]:
        """获取健康状态"""
        try:
            with self._lock:
                return self.health_status.copy()
        except Exception as e:
            logger.error(f"Error getting health status: {e}")
            return {'status': 'unhealthy', 'error': str(e)}
    
    def process_kafka_message(self, message: Dict[str, Any]) -> None:
        """
        处理Kafka消息
        
        Args:
            message: Kafka消息
        """
        try:
            # 解析消息
            request_type = message.get('type', 'predict')
            payload = message.get('payload', {})
            priority = message.get('priority', 2)
            
            # 提交请求
            self.submit_request(request_type, payload, priority)
            
        except Exception as e:
            logger.error(f"Error processing Kafka message: {e}")
    
    def send_kafka_response(self, response: ServiceResponse) -> None:
        """
        发送Kafka响应
        
        Args:
            response: 服务响应
        """
        try:
            # 这里应该实现实际的Kafka发送逻辑
            # 暂时只记录日志
            logger.info(f"Kafka response: {response.request_id} - {response.success}")
            
        except Exception as e:
            logger.error(f"Error sending Kafka response: {e}")
    
    def warm_up_models(self, model_ids: List[str]) -> Dict[str, bool]:
        """
        预热模型
        
        Args:
            model_ids: 模型ID列表
            
        Returns:
            预热结果
        """
        try:
            results = {}
            
            for model_id in model_ids:
                try:
                    success = self.prediction_engine.warm_up_model(model_id)
                    results[model_id] = success
                except Exception as e:
                    logger.error(f"Error warming up model {model_id}: {e}")
                    results[model_id] = False
            
            return results
            
        except Exception as e:
            logger.error(f"Error warming up models: {e}")
            return {}
    
    def cleanup_resources(self) -> None:
        """清理资源"""
        try:
            # 清理缓存
            self.prediction_engine.clear_cache()
            self.model_manager.clear_cache()
            
            # 清理队列
            while not self.request_queue.empty():
                try:
                    self.request_queue.get_nowait()
                    self.request_queue.task_done()
                except queue.Empty:
                    break
            
            logger.info("Resources cleaned up")
            
        except Exception as e:
            logger.error(f"Error cleaning up resources: {e}")
