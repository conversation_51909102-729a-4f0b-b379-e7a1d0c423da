/**
 * Autogenerated by Avro
 *
 * DO NOT EDIT DIRECTLY
 */
package com.crypto.trading.trade.model.avro;
@org.apache.avro.specific.AvroGenerated
public enum SignalType implements org.apache.avro.generic.GenericEnumSymbol<SignalType> {
  BUY, SELL, CLOSE_LONG, CLOSE_SHORT, HOLD  ;
  public static final org.apache.avro.Schema SCHEMA$ = new org.apache.avro.Schema.Parser().parse("{\"type\":\"enum\",\"name\":\"SignalType\",\"namespace\":\"com.crypto.trading.trade.model.avro\",\"symbols\":[\"BUY\",\"SELL\",\"CLOSE_LONG\",\"CLOSE_SHORT\",\"HOLD\"]}");
  public static org.apache.avro.Schema getClassSchema() { return SCHEMA$; }

  @Override
  public org.apache.avro.Schema getSchema() { return SCHEMA$; }
}
