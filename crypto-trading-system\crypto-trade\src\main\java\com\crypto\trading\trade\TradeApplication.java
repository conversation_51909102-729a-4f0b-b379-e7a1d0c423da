package com.crypto.trading.trade;

// import io.github.resilience4j.ratelimiter.annotation.EnableRateLimiter;
import org.mybatis.spring.annotation.MapperScan;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

/**
 * 交易模块应用程序入口
 * <p>
 * 提供交易执行、订单管理等功能
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@SpringBootApplication
@ComponentScan(basePackages = {"com.crypto.trading"})
@MapperScan(basePackages = {"com.crypto.trading.trade.repository.mapper"})
@EnableAsync // 启用异步处理，支持虚拟线程的异步操作
@EnableScheduling // 启用定时任务，用于定期检查订单状态等
@EnableCaching // 启用缓存，提高频繁访问数据的性能
// @EnableRateLimiter // 启用Resilience4j速率限制功能，防止API请求过载
public class TradeApplication {

    private static final Logger log = LoggerFactory.getLogger(TradeApplication.class);
    
    /**
     * 配置虚拟线程执行器
     * 
     * @return 异步任务执行器
     */
    @Bean
    public Executor taskExecutor() {
        return Executors.newVirtualThreadPerTaskExecutor();
    }

    /**
     * 应用程序入口
     *
     * @param args 命令行参数
     */
    public static void main(String[] args) {
        try {
            // 启用虚拟线程
            System.setProperty("spring.threads.virtual.enabled", "true");
            SpringApplication.run(TradeApplication.class, args);
            log.info("交易模块启动成功");
        } catch (Exception e) {
            log.error("交易模块启动失败: {}", e.getMessage(), e);
        }
    }
}