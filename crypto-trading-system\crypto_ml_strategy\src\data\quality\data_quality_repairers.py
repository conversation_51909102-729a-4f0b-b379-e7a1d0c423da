#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据质量修复器组件

实现各种数据质量问题的自动修复机制，包括缺失值填充、异常值处理、数据平滑等。
每个修复器专注于特定类型的质量问题修复。
"""

import logging
import numpy as np
import pandas as pd
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
from scipy import interpolate
from sklearn.preprocessing import RobustScaler

from .data_quality_core import QualityRepairer, QualityIssue, QualityConfig, ValidationResult


class MissingValueRepairer:
    """缺失值修复器"""
    
    def get_repairer_name(self) -> str:
        return "missing_value_repairer"
    
    def repair(self, data: pd.DataFrame, issues: List[QualityIssue], 
              config: QualityConfig) -> Tuple[pd.DataFrame, List[str]]:
        """修复缺失值问题"""
        repaired_data = data.copy()
        repair_logs = []
        
        try:
            # 筛选缺失值相关问题
            missing_issues = [issue for issue in issues 
                            if issue.issue_type in ['missing_values', 'empty_rows']]
            
            for issue in missing_issues:
                if issue.issue_type == 'missing_values':
                    for column in issue.affected_columns:
                        if column in repaired_data.columns:
                            original_missing = repaired_data[column].isnull().sum()
                            
                            # 根据配置选择修复方法
                            if config.repair_missing_method == 'ffill':
                                repaired_data[column] = repaired_data[column].fillna(method='ffill')
                            elif config.repair_missing_method == 'bfill':
                                repaired_data[column] = repaired_data[column].fillna(method='bfill')
                            elif config.repair_missing_method == 'interpolate':
                                repaired_data[column] = self._interpolate_missing(repaired_data[column])
                            elif config.repair_missing_method == 'mean':
                                if repaired_data[column].dtype in ['float64', 'int64']:
                                    mean_value = repaired_data[column].mean()
                                    repaired_data[column] = repaired_data[column].fillna(mean_value)
                            
                            final_missing = repaired_data[column].isnull().sum()
                            repaired_count = original_missing - final_missing
                            
                            if repaired_count > 0:
                                repair_logs.append(
                                    f"列 {column}: 使用 {config.repair_missing_method} 方法修复了 {repaired_count} 个缺失值"
                                )
                
                elif issue.issue_type == 'empty_rows':
                    # 删除完全空的行
                    empty_rows_before = repaired_data.isnull().all(axis=1).sum()
                    repaired_data = repaired_data.dropna(how='all')
                    empty_rows_after = repaired_data.isnull().all(axis=1).sum()
                    
                    removed_count = empty_rows_before - empty_rows_after
                    if removed_count > 0:
                        repair_logs.append(f"删除了 {removed_count} 个完全空的行")
            
        except Exception as e:
            repair_logs.append(f"缺失值修复失败: {str(e)}")
        
        return repaired_data, repair_logs
    
    def _interpolate_missing(self, series: pd.Series) -> pd.Series:
        """插值填充缺失值"""
        try:
            if series.dtype in ['float64', 'int64']:
                # 数值型数据使用线性插值
                return series.interpolate(method='linear')
            else:
                # 非数值型数据使用前向填充
                return series.fillna(method='ffill').fillna(method='bfill')
        except Exception:
            return series.fillna(method='ffill').fillna(method='bfill')


class OutlierRepairer:
    """异常值修复器"""
    
    def get_repairer_name(self) -> str:
        return "outlier_repairer"
    
    def repair(self, data: pd.DataFrame, issues: List[QualityIssue], 
              config: QualityConfig) -> Tuple[pd.DataFrame, List[str]]:
        """修复异常值问题"""
        repaired_data = data.copy()
        repair_logs = []
        
        try:
            # 筛选异常值相关问题
            outlier_issues = [issue for issue in issues if issue.issue_type == 'outlier']
            
            for issue in outlier_issues:
                for column in issue.affected_columns:
                    if column in repaired_data.columns:
                        original_outliers = len(issue.affected_rows)
                        
                        # 根据配置选择修复方法
                        if config.repair_outlier_method == 'clip':
                            repaired_data[column] = self._clip_outliers(repaired_data[column], config)
                        elif config.repair_outlier_method == 'remove':
                            repaired_data = self._remove_outliers(repaired_data, column, issue.affected_rows)
                        elif config.repair_outlier_method == 'interpolate':
                            repaired_data[column] = self._interpolate_outliers(
                                repaired_data[column], issue.affected_rows
                            )
                        
                        repair_logs.append(
                            f"列 {column}: 使用 {config.repair_outlier_method} 方法处理了 {original_outliers} 个异常值"
                        )
            
        except Exception as e:
            repair_logs.append(f"异常值修复失败: {str(e)}")
        
        return repaired_data, repair_logs
    
    def _clip_outliers(self, series: pd.Series, config: QualityConfig) -> pd.Series:
        """裁剪异常值到合理范围"""
        try:
            if series.dtype in ['float64', 'int64']:
                Q1 = series.quantile(0.25)
                Q3 = series.quantile(0.75)
                IQR = Q3 - Q1
                
                lower_bound = Q1 - config.outlier_threshold * IQR
                upper_bound = Q3 + config.outlier_threshold * IQR
                
                return series.clip(lower=lower_bound, upper=upper_bound)
            else:
                return series
        except Exception:
            return series
    
    def _remove_outliers(self, data: pd.DataFrame, column: str, outlier_indices: List[int]) -> pd.DataFrame:
        """移除异常值行"""
        try:
            # 只移除指定列的异常值行
            valid_indices = [idx for idx in outlier_indices if idx in data.index]
            return data.drop(index=valid_indices)
        except Exception:
            return data
    
    def _interpolate_outliers(self, series: pd.Series, outlier_indices: List[int]) -> pd.Series:
        """插值替换异常值"""
        try:
            repaired_series = series.copy()
            valid_indices = [idx for idx in outlier_indices if idx in series.index]
            
            # 将异常值设为NaN，然后插值
            repaired_series.loc[valid_indices] = np.nan
            return repaired_series.interpolate(method='linear')
        except Exception:
            return series


class RangeRepairer:
    """数据范围修复器"""
    
    def get_repairer_name(self) -> str:
        return "range_repairer"
    
    def repair(self, data: pd.DataFrame, issues: List[QualityIssue], 
              config: QualityConfig) -> Tuple[pd.DataFrame, List[str]]:
        """修复数据范围问题"""
        repaired_data = data.copy()
        repair_logs = []
        
        try:
            # 筛选范围相关问题
            range_issues = [issue for issue in issues 
                          if 'range_violation' in issue.issue_type or 'logic_violation' in issue.issue_type]
            
            for issue in range_issues:
                if issue.issue_type == 'price_range_violation':
                    for column in issue.affected_columns:
                        if column in repaired_data.columns:
                            repaired_count = self._repair_price_range(repaired_data, column, config)
                            if repaired_count > 0:
                                repair_logs.append(f"列 {column}: 修复了 {repaired_count} 个价格范围问题")
                
                elif issue.issue_type == 'volume_range_violation':
                    for column in issue.affected_columns:
                        if column in repaired_data.columns:
                            repaired_count = self._repair_volume_range(repaired_data, column, config)
                            if repaired_count > 0:
                                repair_logs.append(f"列 {column}: 修复了 {repaired_count} 个成交量范围问题")
                
                elif issue.issue_type == 'ohlc_logic_violation':
                    repaired_count = self._repair_ohlc_logic(repaired_data)
                    if repaired_count > 0:
                        repair_logs.append(f"修复了 {repaired_count} 个OHLC逻辑问题")
            
        except Exception as e:
            repair_logs.append(f"范围修复失败: {str(e)}")
        
        return repaired_data, repair_logs
    
    def _repair_price_range(self, data: pd.DataFrame, column: str, config: QualityConfig) -> int:
        """修复价格范围问题"""
        try:
            original_invalid = ((data[column] < config.price_min) | 
                              (data[column] > config.price_max) | 
                              (data[column] <= 0)).sum()
            
            # 将无效价格裁剪到合理范围
            data[column] = data[column].clip(lower=config.price_min, upper=config.price_max)
            
            # 处理非正价格
            invalid_mask = data[column] <= 0
            if invalid_mask.any():
                # 使用前一个有效值填充
                data.loc[invalid_mask, column] = np.nan
                data[column] = data[column].fillna(method='ffill').fillna(method='bfill')
            
            final_invalid = ((data[column] < config.price_min) | 
                           (data[column] > config.price_max) | 
                           (data[column] <= 0)).sum()
            
            return original_invalid - final_invalid
            
        except Exception:
            return 0
    
    def _repair_volume_range(self, data: pd.DataFrame, column: str, config: QualityConfig) -> int:
        """修复成交量范围问题"""
        try:
            original_invalid = ((data[column] < config.volume_min) | 
                              (data[column] > config.volume_max) | 
                              (data[column] < 0)).sum()
            
            # 将负成交量设为0
            data.loc[data[column] < 0, column] = 0
            
            # 裁剪到合理范围
            data[column] = data[column].clip(lower=config.volume_min, upper=config.volume_max)
            
            final_invalid = ((data[column] < config.volume_min) | 
                           (data[column] > config.volume_max) | 
                           (data[column] < 0)).sum()
            
            return original_invalid - final_invalid
            
        except Exception:
            return 0
    
    def _repair_ohlc_logic(self, data: pd.DataFrame) -> int:
        """修复OHLC逻辑问题"""
        try:
            if not all(col in data.columns for col in ['open', 'high', 'low', 'close']):
                return 0
            
            repaired_count = 0
            
            # 修复high < max(open, close)的问题
            max_oc = data[['open', 'close']].max(axis=1)
            high_invalid = data['high'] < max_oc
            if high_invalid.any():
                data.loc[high_invalid, 'high'] = max_oc[high_invalid]
                repaired_count += high_invalid.sum()
            
            # 修复low > min(open, close)的问题
            min_oc = data[['open', 'close']].min(axis=1)
            low_invalid = data['low'] > min_oc
            if low_invalid.any():
                data.loc[low_invalid, 'low'] = min_oc[low_invalid]
                repaired_count += low_invalid.sum()
            
            return repaired_count
            
        except Exception:
            return 0


class DataSmoother:
    """数据平滑器"""
    
    def get_repairer_name(self) -> str:
        return "data_smoother"
    
    def repair(self, data: pd.DataFrame, issues: List[QualityIssue], 
              config: QualityConfig) -> Tuple[pd.DataFrame, List[str]]:
        """平滑数据以减少噪声"""
        repaired_data = data.copy()
        repair_logs = []
        
        try:
            # 对价格相关列进行平滑处理
            price_columns = [col for col in data.columns 
                           if any(keyword in col.lower() for keyword in ['price', 'close'])]
            
            for column in price_columns:
                if column in repaired_data.columns and len(repaired_data) > 5:
                    original_volatility = self._calculate_volatility(repaired_data[column])
                    
                    # 应用移动平均平滑
                    smoothed_series = self._apply_moving_average_smoothing(repaired_data[column])
                    
                    # 只在显著改善波动性时应用平滑
                    smoothed_volatility = self._calculate_volatility(smoothed_series)
                    if smoothed_volatility < original_volatility * 0.9:  # 至少减少10%的波动
                        repaired_data[column] = smoothed_series
                        improvement = (original_volatility - smoothed_volatility) / original_volatility
                        repair_logs.append(
                            f"列 {column}: 应用平滑处理，波动性降低 {improvement:.1%}"
                        )
            
        except Exception as e:
            repair_logs.append(f"数据平滑失败: {str(e)}")
        
        return repaired_data, repair_logs
    
    def _calculate_volatility(self, series: pd.Series) -> float:
        """计算序列的波动性"""
        try:
            if len(series) < 2:
                return 0.0
            returns = series.pct_change().dropna()
            return returns.std() if len(returns) > 0 else 0.0
        except Exception:
            return 0.0
    
    def _apply_moving_average_smoothing(self, series: pd.Series, window: int = 3) -> pd.Series:
        """应用移动平均平滑"""
        try:
            if len(series) < window:
                return series
            
            # 使用中心化移动平均
            smoothed = series.rolling(window=window, center=True, min_periods=1).mean()
            
            # 保持首尾值不变以避免过度平滑
            smoothed.iloc[0] = series.iloc[0]
            smoothed.iloc[-1] = series.iloc[-1]
            
            return smoothed
        except Exception:
            return series


class TimeSeriesRepairer:
    """时间序列修复器"""
    
    def get_repairer_name(self) -> str:
        return "timeseries_repairer"
    
    def repair(self, data: pd.DataFrame, issues: List[QualityIssue], 
              config: QualityConfig) -> Tuple[pd.DataFrame, List[str]]:
        """修复时间序列问题"""
        repaired_data = data.copy()
        repair_logs = []
        
        try:
            # 筛选时间序列相关问题
            time_issues = [issue for issue in issues 
                         if any(keyword in issue.issue_type for keyword in ['time', 'timestamp', 'gap'])]
            
            for issue in time_issues:
                if issue.issue_type == 'time_order_violation':
                    # 按时间排序
                    if hasattr(repaired_data.index, 'to_pydatetime'):
                        repaired_data = repaired_data.sort_index()
                        repair_logs.append("数据已按时间索引排序")
                
                elif issue.issue_type == 'duplicate_timestamps':
                    # 处理重复时间戳
                    if hasattr(repaired_data.index, 'to_pydatetime'):
                        original_count = len(repaired_data)
                        repaired_data = repaired_data[~repaired_data.index.duplicated(keep='first')]
                        removed_count = original_count - len(repaired_data)
                        if removed_count > 0:
                            repair_logs.append(f"删除了 {removed_count} 个重复时间戳的记录")
                
                elif issue.issue_type == 'time_gap':
                    # 填充时间间隙（如果间隙不太大）
                    filled_count = self._fill_time_gaps(repaired_data, config)
                    if filled_count > 0:
                        repair_logs.append(f"填充了 {filled_count} 个时间间隙")
            
        except Exception as e:
            repair_logs.append(f"时间序列修复失败: {str(e)}")
        
        return repaired_data, repair_logs
    
    def _fill_time_gaps(self, data: pd.DataFrame, config: QualityConfig) -> int:
        """填充时间间隙"""
        try:
            if not hasattr(data.index, 'to_pydatetime') or len(data) < 2:
                return 0
            
            # 检测间隙
            time_diffs = data.index.to_series().diff()
            median_diff = time_diffs.median()
            
            # 只填充小于最大允许间隙的空缺
            max_fill_gap = min(config.max_time_gap_seconds, median_diff.total_seconds() * 5)
            
            filled_count = 0
            gaps_to_fill = time_diffs[
                (time_diffs > median_diff * 2) & 
                (time_diffs.dt.total_seconds() <= max_fill_gap)
            ]
            
            for gap_idx in gaps_to_fill.index:
                if gap_idx in data.index:
                    # 在间隙中插入插值数据点
                    prev_idx = data.index[data.index.get_loc(gap_idx) - 1]
                    gap_duration = time_diffs.loc[gap_idx]
                    
                    # 计算需要插入的点数
                    points_needed = int(gap_duration / median_diff) - 1
                    
                    if 1 <= points_needed <= 5:  # 限制插入点数
                        # 创建时间点
                        time_points = pd.date_range(
                            start=prev_idx + median_diff,
                            end=gap_idx - median_diff,
                            periods=points_needed
                        )
                        
                        # 插值数据
                        for time_point in time_points:
                            # 简单线性插值
                            interpolated_row = self._interpolate_row(data, prev_idx, gap_idx)
                            data.loc[time_point] = interpolated_row
                            filled_count += 1
            
            # 重新排序
            if filled_count > 0:
                data.sort_index(inplace=True)
            
            return filled_count
            
        except Exception:
            return 0
    
    def _interpolate_row(self, data: pd.DataFrame, prev_idx, next_idx) -> pd.Series:
        """插值单行数据"""
        try:
            prev_row = data.loc[prev_idx]
            next_row = data.loc[next_idx]
            
            # 对数值列进行线性插值
            interpolated = {}
            for col in data.columns:
                if data[col].dtype in ['float64', 'int64']:
                    interpolated[col] = (prev_row[col] + next_row[col]) / 2
                else:
                    interpolated[col] = prev_row[col]  # 非数值列使用前值
            
            return pd.Series(interpolated)
            
        except Exception:
            return data.iloc[0]  # 返回第一行作为默认值