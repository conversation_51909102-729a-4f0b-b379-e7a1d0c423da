package com.crypto.trading.common.dto.market;

import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

/**
 * KlineDataDTO单元测试类
 */
class KlineDataDTOTest {

    /**
     * 测试默认构造函数
     */
    @Test
    void testDefaultConstructor() {
        KlineDataDTO dto = new KlineDataDTO();
        assertNotNull(dto);
    }

    /**
     * 测试带参数构造函数
     */
    @Test
    void testParameterizedConstructor() {
        String symbol = "BTCUSDT";
        String interval = "1m";
        LocalDateTime openTime = LocalDateTime.now().minusMinutes(1);
        LocalDateTime closeTime = LocalDateTime.now();
        BigDecimal open = new BigDecimal("50000.00");
        BigDecimal high = new BigDecimal("51000.00");
        BigDecimal low = new BigDecimal("49500.00");
        BigDecimal close = new BigDecimal("50500.00");
        BigDecimal volume = new BigDecimal("10.5");
        BigDecimal quoteAssetVolume = new BigDecimal("525000.00");
        Long numberOfTrades = 100L;
        BigDecimal takerBuyBaseAssetVolume = new BigDecimal("6.3");
        BigDecimal takerBuyQuoteAssetVolume = new BigDecimal("315000.00");
        boolean isClosed = true;

        KlineDataDTO dto = new KlineDataDTO(
                symbol, interval, openTime, closeTime, open, high, low, close,
                volume, quoteAssetVolume, numberOfTrades,
                takerBuyBaseAssetVolume, takerBuyQuoteAssetVolume, isClosed
        );

        assertNotNull(dto);
        assertEquals(symbol, dto.getSymbol());
        assertEquals(interval, dto.getInterval());
        assertEquals(openTime, dto.getOpenTime());
        assertEquals(closeTime, dto.getCloseTime());
        assertEquals(open, dto.getOpen());
        assertEquals(high, dto.getHigh());
        assertEquals(low, dto.getLow());
        assertEquals(close, dto.getClose());
        assertEquals(volume, dto.getVolume());
        assertEquals(quoteAssetVolume, dto.getQuoteAssetVolume());
        assertEquals(numberOfTrades, dto.getNumberOfTrades());
        assertEquals(takerBuyBaseAssetVolume, dto.getTakerBuyBaseAssetVolume());
        assertEquals(takerBuyQuoteAssetVolume, dto.getTakerBuyQuoteAssetVolume());
        assertEquals(isClosed, dto.isClosed());
    }

    /**
     * 测试getter和setter方法
     */
    @Test
    void testGettersAndSetters() {
        KlineDataDTO dto = new KlineDataDTO();

        String symbol = "ETHUSDT";
        dto.setSymbol(symbol);
        assertEquals(symbol, dto.getSymbol());

        String interval = "5m";
        dto.setInterval(interval);
        assertEquals(interval, dto.getInterval());

        LocalDateTime openTime = LocalDateTime.now().minusMinutes(5);
        dto.setOpenTime(openTime);
        assertEquals(openTime, dto.getOpenTime());

        LocalDateTime closeTime = LocalDateTime.now();
        dto.setCloseTime(closeTime);
        assertEquals(closeTime, dto.getCloseTime());

        BigDecimal open = new BigDecimal("3000.00");
        dto.setOpen(open);
        assertEquals(open, dto.getOpen());

        BigDecimal high = new BigDecimal("3100.00");
        dto.setHigh(high);
        assertEquals(high, dto.getHigh());

        BigDecimal low = new BigDecimal("2950.00");
        dto.setLow(low);
        assertEquals(low, dto.getLow());

        BigDecimal close = new BigDecimal("3050.00");
        dto.setClose(close);
        assertEquals(close, dto.getClose());

        BigDecimal volume = new BigDecimal("20.5");
        dto.setVolume(volume);
        assertEquals(volume, dto.getVolume());

        BigDecimal quoteAssetVolume = new BigDecimal("62000.00");
        dto.setQuoteAssetVolume(quoteAssetVolume);
        assertEquals(quoteAssetVolume, dto.getQuoteAssetVolume());

        Long numberOfTrades = 200L;
        dto.setNumberOfTrades(numberOfTrades);
        assertEquals(numberOfTrades, dto.getNumberOfTrades());

        BigDecimal takerBuyBaseAssetVolume = new BigDecimal("12.3");
        dto.setTakerBuyBaseAssetVolume(takerBuyBaseAssetVolume);
        assertEquals(takerBuyBaseAssetVolume, dto.getTakerBuyBaseAssetVolume());

        BigDecimal takerBuyQuoteAssetVolume = new BigDecimal("37000.00");
        dto.setTakerBuyQuoteAssetVolume(takerBuyQuoteAssetVolume);
        assertEquals(takerBuyQuoteAssetVolume, dto.getTakerBuyQuoteAssetVolume());

        boolean isClosed = false;
        dto.setClosed(isClosed);
        assertEquals(isClosed, dto.isClosed());
    }

    /**
     * 测试toString方法
     */
    @Test
    void testToString() {
        KlineDataDTO dto = new KlineDataDTO();
        dto.setSymbol("BTCUSDT");
        dto.setInterval("1h");
        dto.setOpen(new BigDecimal("50000.00"));
        dto.setClose(new BigDecimal("51000.00"));

        String toString = dto.toString();
        assertNotNull(toString);
        assertTrue(toString.contains("BTCUSDT"));
        assertTrue(toString.contains("1h"));
        assertTrue(toString.contains("50000.00"));
        assertTrue(toString.contains("51000.00"));
    }
} 