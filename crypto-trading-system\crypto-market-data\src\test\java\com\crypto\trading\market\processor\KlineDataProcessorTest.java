package com.crypto.trading.market.processor;

import com.crypto.trading.common.dto.market.KlineDataDTO;
import com.crypto.trading.market.converter.MarketDataConverter;
import com.crypto.trading.market.producer.AsyncMarketDataProducer;
import com.crypto.trading.sdk.response.model.KlineData;
import com.crypto.trading.sdk.response.model.WebSocketMessage;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.kafka.support.SendResult;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.concurrent.CompletableFuture;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * KlineDataProcessor单元测试
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class KlineDataProcessorTest {

    @Mock
    private AsyncMarketDataProducer asyncMarketDataProducer;

    @Mock
    private MarketDataConverter marketDataConverter;

    @InjectMocks
    private KlineDataProcessor klineDataProcessor;

    private WebSocketMessage<KlineData> klineMessage;
    private KlineDataDTO klineDataDTO;
    private String interval = "1m";

    @BeforeEach
    void setUp() throws Exception {
        // 准备测试数据 - 创建WebSocketMessage<KlineData>
        KlineData klineData = new KlineData();
        klineData.setOpenTime(1622535600000L);
        klineData.setCloseTime(1622535659999L);
        klineData.setOpen(new BigDecimal("35000.00"));
        klineData.setHigh(new BigDecimal("35100.00"));
        klineData.setLow(new BigDecimal("34900.00"));
        klineData.setClose(new BigDecimal("35050.00"));
        klineData.setVolume(new BigDecimal("10.5"));
        klineData.setQuoteAssetVolume(new BigDecimal("367500.00"));
        klineData.setNumberOfTrades(150L);
        
        klineMessage = new WebSocketMessage<>("kline", 1622535659999L, "BTCUSDT", klineData);
        
        // 准备KlineDataDTO
        klineDataDTO = new KlineDataDTO(
            "BTCUSDT",
            interval,
            LocalDateTime.ofInstant(Instant.ofEpochMilli(1622535600000L), ZoneId.systemDefault()),
            LocalDateTime.ofInstant(Instant.ofEpochMilli(1622535659999L), ZoneId.systemDefault()),
            new BigDecimal("35000.00"),
            new BigDecimal("35100.00"),
            new BigDecimal("34900.00"),
            new BigDecimal("35050.00"),
            new BigDecimal("10.5"),
            new BigDecimal("367500.00"),
            150L,
            new BigDecimal("5.2"),
            new BigDecimal("180000.00"),
            true
        );
        
        // 配置mock行为
        doReturn(klineDataDTO).when(marketDataConverter).convertToKlineDataDTO(any(KlineData.class), eq("BTCUSDT"), eq(interval));

        // 配置AsyncMarketDataProducer返回成功的CompletableFuture
        CompletableFuture<SendResult<String, Object>> successFuture = CompletableFuture.completedFuture(null);
        doReturn(successFuture).when(asyncMarketDataProducer).sendKlineRawData(any(KlineDataDTO.class));
    }

    @Test
    void testProcessKlineData() throws Exception {
        // 调用被测试方法
        klineDataProcessor.processKlineData(klineMessage, interval);

        // 验证发送到Kafka
        verify(asyncMarketDataProducer).sendKlineRawData(eq(klineDataDTO));
    }

    @Test
    void testProcessKlineDataWithKafkaException() throws Exception {
        // 配置mock抛出异常
        CompletableFuture<SendResult<String, Object>> failedFuture = new CompletableFuture<>();
        failedFuture.completeExceptionally(new RuntimeException("Kafka error"));
        doReturn(failedFuture).when(asyncMarketDataProducer).sendKlineRawData(any(KlineDataDTO.class));

        // 调用被测试方法
        klineDataProcessor.processKlineData(klineMessage, interval);

        // 验证尝试发送到Kafka
        verify(asyncMarketDataProducer).sendKlineRawData(eq(klineDataDTO));
    }

    @Test
    void testProcessKlineDataWithConversionFailure() throws Exception {
        // 配置mock返回null，模拟转换失败
        doReturn(null).when(marketDataConverter).convertToKlineDataDTO(any(KlineData.class), eq("BTCUSDT"), eq(interval));

        // 调用被测试方法
        klineDataProcessor.processKlineData(klineMessage, interval);

        // 验证不会尝试发送到Kafka
        verify(asyncMarketDataProducer, never()).sendKlineRawData(any(KlineDataDTO.class));
    }

    @Test
    void testProcessKlineDataWithNullMessage() throws Exception {
        // 调用被测试方法，传入null消息
        klineDataProcessor.processKlineData(null, interval);

        // 验证不会尝试发送到Kafka
        verify(asyncMarketDataProducer, never()).sendKlineRawData(any(KlineDataDTO.class));
    }
} 