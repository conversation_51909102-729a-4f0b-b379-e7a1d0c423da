package com.crypto.trading.common.util;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.transaction.support.TransactionTemplate;

import java.io.Serializable;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * DatabaseExecutor工具类的单元测试
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
class DatabaseExecutorTest {

    @Mock
    private TransactionTemplate transactionTemplate;
    
    @Mock
    private JdbcTemplate jdbcTemplate;
    
    @Mock
    private BaseMapper<TestEntity> baseMapper;
    
    @Mock
    private IService<TestEntity> service;
    
    @Mock
    private RowMapper<TestEntity> rowMapper;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        
        // 重置统计信息
        DatabaseExecutor.resetStatistics();
        
        // 设置事务模板的默认行为
        when(transactionTemplate.execute(any())).thenAnswer(invocation -> {
            return invocation.getArgument(0, org.springframework.transaction.support.TransactionCallback.class).doInTransaction(null);
        });
    }

    @Test
    void testExecuteAsync() throws ExecutionException, InterruptedException {
        // 准备测试数据
        TestEntity entity = new TestEntity(1L, "test");
        
        // 执行异步操作
        CompletableFuture<TestEntity> future = DatabaseExecutor.executeAsync(() -> entity);
        
        // 验证结果
        TestEntity result = future.get();
        assertEquals(entity, result);
        
        // 验证统计信息
        Map<String, Object> stats = DatabaseExecutor.getStatistics();
        assertEquals(1L, stats.get("totalExecutions"));
        assertEquals(0L, stats.get("failedOperations"));
    }

    @Test
    void testExecuteAsyncWithException() {
        // 执行会抛出异常的异步操作
        CompletableFuture<Object> future = DatabaseExecutor.executeAsync(() -> {
            throw new RuntimeException("测试异常");
        });
        
        // 验证异常
        ExecutionException exception = assertThrows(ExecutionException.class, future::get);
        assertTrue(exception.getCause() instanceof RuntimeException);
        assertEquals("数据库操作异常", exception.getCause().getMessage());
        
        // 验证统计信息
        Map<String, Object> stats = DatabaseExecutor.getStatistics();
        assertEquals(1L, stats.get("totalExecutions"));
        assertEquals(1L, stats.get("failedOperations"));
    }

    @Test
    void testExecuteInTransactionAsync() throws ExecutionException, InterruptedException {
        // 准备测试数据
        TestEntity entity = new TestEntity(1L, "test");
        
        // 执行事务内的异步操作
        CompletableFuture<TestEntity> future = DatabaseExecutor.executeInTransactionAsync(
                transactionTemplate,
                () -> entity
        );
        
        // 验证结果
        TestEntity result = future.get();
        assertEquals(entity, result);
        
        // 验证事务模板被调用
        verify(transactionTemplate, times(1)).execute(any());
    }

    @Test
    void testExecuteAllAsync() throws ExecutionException, InterruptedException {
        // 准备测试数据
        AtomicInteger counter = new AtomicInteger(0);
        
        // 执行多个异步操作
        CompletableFuture<Void> future = DatabaseExecutor.executeAllAsync(
                () -> counter.incrementAndGet(),
                () -> counter.incrementAndGet(),
                () -> counter.incrementAndGet()
        );
        
        // 等待所有操作完成
        future.get();
        
        // 验证结果
        assertEquals(3, counter.get());
        
        // 验证统计信息
        Map<String, Object> stats = DatabaseExecutor.getStatistics();
        assertEquals(3L, stats.get("totalExecutions"));
    }

    @Test
    void testQueryAsync() throws ExecutionException, InterruptedException {
        // 准备测试数据
        List<TestEntity> entities = Arrays.asList(
                new TestEntity(1L, "test1"),
                new TestEntity(2L, "test2")
        );
        
        // 设置模拟行为
        when(jdbcTemplate.query(anyString(), any(RowMapper.class), any())).thenReturn(entities);
        
        // 执行异步查询
        CompletableFuture<List<TestEntity>> future = DatabaseExecutor.queryAsync(
                jdbcTemplate,
                "SELECT * FROM test_table WHERE id = ?",
                rowMapper,
                1L
        );
        
        // 验证结果
        List<TestEntity> result = future.get();
        assertEquals(entities, result);
        
        // 验证JdbcTemplate被调用
        verify(jdbcTemplate, times(1)).query(anyString(), any(RowMapper.class), any());
    }

    @Test
    void testUpdateAsync() throws ExecutionException, InterruptedException {
        // 修改模拟行为，使用正确的参数匹配
        when(jdbcTemplate.update(
                eq("UPDATE test_table SET name = ? WHERE id = ?"), 
                eq("newName"), 
                eq(1L)
        )).thenReturn(1);
        
        // 执行异步更新
        CompletableFuture<Integer> future = DatabaseExecutor.updateAsync(
                jdbcTemplate,
                "UPDATE test_table SET name = ? WHERE id = ?",
                "newName",
                1L
        );
        
        // 验证结果
        Integer result = future.get();
        assertEquals(1, result);
        
        // 验证JdbcTemplate被调用
        verify(jdbcTemplate, times(1)).update(
                eq("UPDATE test_table SET name = ? WHERE id = ?"), 
                eq("newName"), 
                eq(1L)
        );
    }

    @Test
    void testSelectByIdAsync() throws ExecutionException, InterruptedException {
        // 准备测试数据
        TestEntity entity = new TestEntity(1L, "test");
        
        // 设置模拟行为
        when(baseMapper.selectById(any(Serializable.class))).thenReturn(entity);
        
        // 执行异步查询
        CompletableFuture<TestEntity> future = DatabaseExecutor.selectByIdAsync(baseMapper, 1L);
        
        // 验证结果
        TestEntity result = future.get();
        assertEquals(entity, result);
        
        // 验证BaseMapper被调用
        verify(baseMapper, times(1)).selectById(any(Serializable.class));
    }

    @Test
    void testSelectListAsync() throws ExecutionException, InterruptedException {
        // 准备测试数据
        List<TestEntity> entities = Arrays.asList(
                new TestEntity(1L, "test1"),
                new TestEntity(2L, "test2")
        );
        
        // 设置模拟行为
        when(baseMapper.selectList(any())).thenReturn(entities);
        
        // 执行异步查询
        CompletableFuture<List<TestEntity>> future = DatabaseExecutor.selectListAsync(
                baseMapper,
                new LambdaQueryWrapper<>()
        );
        
        // 验证结果
        List<TestEntity> result = future.get();
        assertEquals(entities, result);
        
        // 验证BaseMapper被调用
        verify(baseMapper, times(1)).selectList(any());
    }

    @Test
    void testSelectPageAsync() throws ExecutionException, InterruptedException {
        // 准备测试数据
        Page<TestEntity> page = new Page<>(1, 10);
        
        // 设置模拟行为
        when(baseMapper.selectPage(any(), any())).thenReturn(page);
        
        // 执行异步分页查询
        CompletableFuture<Page<TestEntity>> future = (CompletableFuture) DatabaseExecutor.selectPageAsync(
                baseMapper,
                new Page<>(1, 10),
                new LambdaQueryWrapper<>()
        );
        
        // 验证结果
        Page<TestEntity> result = future.get();
        assertEquals(page, result);
        
        // 验证BaseMapper被调用
        verify(baseMapper, times(1)).selectPage(any(), any());
    }

    @Test
    void testSaveAsync() throws ExecutionException, InterruptedException {
        // 准备测试数据
        TestEntity entity = new TestEntity(1L, "test");
        
        // 设置模拟行为
        when(service.save(any())).thenReturn(true);
        
        // 执行异步保存
        CompletableFuture<Boolean> future = DatabaseExecutor.saveAsync(service, entity);
        
        // 验证结果
        Boolean result = future.get();
        assertTrue(result);
        
        // 验证IService被调用
        verify(service, times(1)).save(any());
    }

    @Test
    void testSaveBatchAsync() throws ExecutionException, InterruptedException {
        // 准备测试数据
        List<TestEntity> entities = Arrays.asList(
                new TestEntity(1L, "test1"),
                new TestEntity(2L, "test2")
        );
        
        // 设置模拟行为
        when(service.saveBatch(anyList())).thenReturn(true);
        
        // 执行异步批量保存
        CompletableFuture<Boolean> future = DatabaseExecutor.saveBatchAsync(service, entities);
        
        // 验证结果
        Boolean result = future.get();
        assertTrue(result);
        
        // 验证IService被调用
        verify(service, times(1)).saveBatch(anyList());
    }

    @Test
    void testProcessBatchAsync() throws ExecutionException, InterruptedException {
        // 准备测试数据
        List<Integer> numbers = Arrays.asList(1, 2, 3, 4, 5, 6, 7, 8, 9, 10);
        AtomicInteger sum = new AtomicInteger(0);
        
        // 定义批处理器
        Consumer<List<Integer>> batchProcessor = batch -> {
            batch.forEach(sum::addAndGet);
        };
        
        // 执行批处理
        CompletableFuture<Void> future = DatabaseExecutor.processBatchAsync(numbers, 3, batchProcessor);
        
        // 等待批处理完成
        future.get();
        
        // 验证结果
        assertEquals(55, sum.get()); // 1+2+3+...+10=55
    }

    @Test
    void testProcessBatchWithResultAsync() throws ExecutionException, InterruptedException {
        // 准备测试数据
        List<Integer> numbers = Arrays.asList(1, 2, 3, 4, 5, 6, 7, 8, 9, 10);
        
        // 执行批处理并返回结果
        CompletableFuture<List<Integer>> future = DatabaseExecutor.processBatchWithResultAsync(
                numbers,
                3,
                batch -> batch.stream().map(n -> n * 2).toList()
        );
        
        // 验证结果
        List<Integer> result = future.get();
        assertEquals(10, result.size());
        assertEquals(Arrays.asList(2, 4, 6, 8, 10, 12, 14, 16, 18, 20), result);
    }

    @Test
    void testGetStatistics() {
        // 执行一些操作
        DatabaseExecutor.executeAsync(() -> "success").join();
        try {
            DatabaseExecutor.executeAsync(() -> {
                throw new RuntimeException("测试异常");
            }).exceptionally(ex -> null).join();
        } catch (Exception ignored) {
            // 忽略异常
        }
        
        // 获取统计信息
        Map<String, Object> stats = DatabaseExecutor.getStatistics();
        
        // 验证统计信息
        assertEquals(2L, stats.get("totalExecutions"));
        assertEquals(1L, stats.get("failedOperations"));
        assertEquals(0, stats.get("activeOperations"));
        assertTrue((Double)stats.get("successRate") > 0);
    }

    /**
     * 测试用实体类
     */
    private static class TestEntity {
        private Long id;
        private String name;
        
        public TestEntity(Long id, String name) {
            this.id = id;
            this.name = name;
        }
        
        public Long getId() {
            return id;
        }
        
        public void setId(Long id) {
            this.id = id;
        }
        
        public String getName() {
            return name;
        }
        
        public void setName(String name) {
            this.name = name;
        }
        
        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            TestEntity that = (TestEntity) o;
            return id.equals(that.id) && name.equals(that.name);
        }
    }
}