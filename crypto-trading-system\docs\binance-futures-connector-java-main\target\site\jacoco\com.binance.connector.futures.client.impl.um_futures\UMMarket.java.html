<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>UMMarket.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">io.github.binance:binance-futures-connector-java</a> &gt; <a href="index.source.html" class="el_package">com.binance.connector.futures.client.impl.um_futures</a> &gt; <span class="el_source">UMMarket.java</span></div><h1>UMMarket.java</h1><pre class="source lang-java linenums">package com.binance.connector.futures.client.impl.um_futures;

import com.binance.connector.futures.client.enums.HttpMethod;
import com.binance.connector.futures.client.utils.ParameterChecker;
import java.util.LinkedHashMap;
import com.binance.connector.futures.client.impl.futures.Market;
import com.binance.connector.futures.client.utils.ProxyAuth;

/**
 * &lt;h2&gt;USDⓈ-Margined Market Endpoints&lt;/h2&gt;
 * All endpoints under the
 * &lt;a href=&quot;https://developers.binance.com/docs/derivatives/usds-margined-futures/general-info&quot;&gt;Market Data Endpoint&lt;/a&gt;
 * section of the API documentation will be implemented in this class.
 * &lt;br&gt;
 * Response will be returned in &lt;i&gt;String format&lt;/i&gt;.
 */
public class UMMarket extends Market {
    public UMMarket(String productUrl, String baseUrl, String apiKey, boolean showLimitUsage, ProxyAuth proxy) {
<span class="fc" id="L19">        super(productUrl, baseUrl, apiKey, showLimitUsage, proxy);</span>
<span class="fc" id="L20">    }</span>

    /**
     * Mark Price and Funding Rate
     * &lt;br&gt;&lt;br&gt;
     * GET /v1/premiumIndex
     * &lt;br&gt;
     * @param
     * parameters LinkedHashedMap of String,Object pair
     *            where String is the name of the parameter and Object is the value of the parameter
     * &lt;br&gt;&lt;br&gt;
     * symbol -- optional/string -- the trading symbol &lt;br&gt;
     * @return String
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Mark-Price&quot;&gt;
     *     https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Mark-Price&lt;/a&gt;
     */
    public String markPrice(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L37">        return super.markPrice(parameters);</span>
    }

    /**
     * 24 hour rolling window price change statistics. Careful when accessing this with no symbol.
     * &lt;br&gt;&lt;br&gt;
     * GET /v1/ticker/24hr
     * &lt;br&gt;
     * @param
     * parameters LinkedHashedMap of String,Object pair
     *            where String is the name of the parameter and Object is the value of the parameter
     * &lt;br&gt;&lt;br&gt;
     * symbol -- optional/string -- the trading symbol &lt;br&gt;
     * @return String
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/24hr-Ticker-Price-Change-Statistics&quot;&gt;
     *     https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/24hr-Ticker-Price-Change-Statistics&lt;/a&gt;
     */
    public String ticker24H(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L55">        return super.ticker24H(parameters);</span>
    }

    /**
     * Latest price for a symbol or symbols.
     * &lt;br&gt;&lt;br&gt;
     * GET /v1/ticker/price
     * &lt;br&gt;
     * https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Symbol-Price-Ticker
     * @param
     * parameters LinkedHashedMap of String,Object pair
     *            where String is the name of the parameter and Object is the value of the parameter
     * &lt;br&gt;&lt;br&gt;
     * symbol -- optional/string -- the trading symbol &lt;br&gt;
     * @return String
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Symbol-Price-Ticker&quot;&gt;
     *     https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Symbol-Price-Ticker&lt;/a&gt;
     */
    public String tickerSymbol(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L74">        return super.tickerSymbol(parameters);</span>
    }

    /**
     * Best price/qty on the order book for a symbol or symbols.
     * &lt;br&gt;&lt;br&gt;
     * GET /v1/ticker/bookTicker
     * &lt;br&gt;
     * https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Symbol-Order-Book-Ticker
     * @param
     * parameters LinkedHashedMap of String,Object pair
     *            where String is the name of the parameter and Object is the value of the parameter
     * &lt;br&gt;&lt;br&gt;
     * symbol -- optional/string -- the trading symbol &lt;br&gt;
     * @return String
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Symbol-Order-Book-Ticker&quot;&gt;
     *     https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Symbol-Order-Book-Ticker&lt;/a&gt;
     */
    public String bookTicker(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L93">        return super.bookTicker(parameters);</span>
    }

    /**
     * Open Interest History
     * &lt;br&gt;&lt;br&gt;
     * GET /futures/data/openInterestHist
     * &lt;br&gt;
     * https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Open-Interest-Statistics
     * @param
     * parameters LinkedHashedMap of String,Object pair
     *            where String is the name of the parameter and Object is the value of the parameter
     * &lt;br&gt;&lt;br&gt;
     * symbol -- mandatory/string -- the trading pair &lt;br&gt;
     * period -- mandatory/enum -- &quot;5m&quot;,&quot;15m&quot;,&quot;30m&quot;,&quot;1h&quot;,&quot;2h&quot;,&quot;4h&quot;,&quot;6h&quot;,&quot;12h&quot;,&quot;1d&quot; &lt;br&gt;
     * limit -- optional/long -- default 30, max 500 &lt;br&gt;
     * startTime -- optional/long -- Start Time &lt;br&gt;
     * endTime -- optional/long -- End Time &lt;br&gt;
     * @return String
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Open-Interest-Statistics&quot;&gt;
     *     https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Open-Interest-Statistics&lt;/a&gt;
     */
    public String openInterestStatistics(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L116">        ParameterChecker.checkParameter(parameters, &quot;symbol&quot;, String.class);</span>
<span class="fc" id="L117">        ParameterChecker.checkParameter(parameters, &quot;period&quot;, String.class);</span>
<span class="fc" id="L118">        return super.openInterestStatistics(parameters);</span>
    }

    /**
     * Top Trader Long/Short Ratio (Positions)
     * &lt;br&gt;&lt;br&gt;
     * GET /futures/data/topLongShortPositionRatio
     * &lt;br&gt;
     * https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Top-Trader-Long-Short-Ratio
     * @param
     * parameters LinkedHashedMap of String,Object pair
     *            where String is the name of the parameter and Object is the value of the parameter
     * &lt;br&gt;&lt;br&gt;
     * symbol -- mandatory/string -- the trading pair &lt;br&gt;
     * period -- mandatory/enum -- &quot;5m&quot;,&quot;15m&quot;,&quot;30m&quot;,&quot;1h&quot;,&quot;2h&quot;,&quot;4h&quot;,&quot;6h&quot;,&quot;12h&quot;,&quot;1d&quot; &lt;br&gt;
     * limit -- optional/long -- default 30, max 500 &lt;br&gt;
     * startTime -- optional/long -- Start Time &lt;br&gt;
     * endTime -- optional/long -- End Time &lt;br&gt;
     * @return String
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Top-Trader-Long-Short-Ratio&quot;&gt;
     *     https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Top-Trader-Long-Short-Ratio&lt;/a&gt;
     */
    public String topTraderLongShortPos(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L141">        ParameterChecker.checkParameter(parameters, &quot;symbol&quot;, String.class);</span>
<span class="fc" id="L142">        ParameterChecker.checkParameter(parameters, &quot;period&quot;, String.class);</span>
<span class="fc" id="L143">        return super.topTraderLongShortPos(parameters);</span>
    }

    /**
     * Top Trader Long/Short Ratio (Accounts)
     * &lt;br&gt;&lt;br&gt;
     * GET /futures/data/topLongShortAccountRatio
     * &lt;br&gt;
     * https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Top-Long-Short-Account-Ratio
     * @param
     * parameters LinkedHashedMap of String,Object pair
     *            where String is the name of the parameter and Object is the value of the parameter
     * &lt;br&gt;&lt;br&gt;
     * symbol -- mandatory/string -- the trading pair &lt;br&gt;
     * period -- mandatory/enum -- &quot;5m&quot;,&quot;15m&quot;,&quot;30m&quot;,&quot;1h&quot;,&quot;2h&quot;,&quot;4h&quot;,&quot;6h&quot;,&quot;12h&quot;,&quot;1d&quot; &lt;br&gt;
     * limit -- optional/long -- default 30, max 500 &lt;br&gt;
     * startTime -- optional/long -- Start Time &lt;br&gt;
     * endTime -- optional/long -- End Time &lt;br&gt;
     * @return String
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Top-Long-Short-Account-Ratio&quot;&gt;
     *     https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Top-Long-Short-Account-Ratio&lt;/a&gt;
     */
    public String topTraderLongShortAccs(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L166">        ParameterChecker.checkParameter(parameters, &quot;symbol&quot;, String.class);</span>
<span class="fc" id="L167">        ParameterChecker.checkParameter(parameters, &quot;period&quot;, String.class);</span>
<span class="fc" id="L168">        return super.topTraderLongShortAccs(parameters);</span>
    }

    /**
     * Long/Short Ratio
     * &lt;br&gt;&lt;br&gt;
     * GET /futures/data/globalLongShortAccountRatio
     * &lt;br&gt;
     * https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Long-Short-Ratio
     * @param
     * parameters LinkedHashedMap of String,Object pair
     *            where String is the name of the parameter and Object is the value of the parameter
     * &lt;br&gt;&lt;br&gt;
     * symbol -- mandatory/string -- the trading pair &lt;br&gt;
     * period -- mandatory/enum -- &quot;5m&quot;,&quot;15m&quot;,&quot;30m&quot;,&quot;1h&quot;,&quot;2h&quot;,&quot;4h&quot;,&quot;6h&quot;,&quot;12h&quot;,&quot;1d&quot; &lt;br&gt;
     * limit -- optional/long -- default 30, max 500 &lt;br&gt;
     * startTime -- optional/long -- Start Time &lt;br&gt;
     * endTime -- optional/long -- End Time &lt;br&gt;
     * @return String
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Long-Short-Ratio&quot;&gt;
     *     https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Long-Short-Ratio&lt;/a&gt;
     */
    public String longShortRatio(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L191">        ParameterChecker.checkParameter(parameters, &quot;symbol&quot;, String.class);</span>
<span class="fc" id="L192">        ParameterChecker.checkParameter(parameters, &quot;period&quot;, String.class);</span>
<span class="fc" id="L193">        return super.longShortRatio(parameters);</span>
    }

<span class="fc" id="L196">    private final String TAKE_BUY_SELL_VOLUME = &quot;/futures/data/takerlongshortRatio&quot;;</span>
    /**
     * Taker Buy/Sell Volume
     * &lt;br&gt;&lt;br&gt;
     * GET /futures/data/takerlongshortRatio
     * &lt;br&gt;
     * https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Taker-BuySell-Volume
     * @param
     * parameters LinkedHashedMap of String,Object pair
     *            where String is the name of the parameter and Object is the value of the parameter
     * &lt;br&gt;&lt;br&gt;
     * symbol -- mandatory/string -- the trading pair &lt;br&gt;
     * period -- mandatory/enum -- &quot;5m&quot;,&quot;15m&quot;,&quot;30m&quot;,&quot;1h&quot;,&quot;2h&quot;,&quot;4h&quot;,&quot;6h&quot;,&quot;12h&quot;,&quot;1d&quot; &lt;br&gt;
     * limit -- optional/long -- default 30, max 500 &lt;br&gt;
     * startTime -- optional/long -- Start Time &lt;br&gt;
     * endTime -- optional/long -- End Time &lt;br&gt;
     * @return String
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Taker-BuySell-Volume&quot;&gt;
     *     https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Taker-BuySell-Volume&lt;/a&gt;
     */
    public String takerBuySellVol(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L217">        ParameterChecker.checkParameter(parameters, &quot;symbol&quot;, String.class);</span>
<span class="fc" id="L218">        ParameterChecker.checkParameter(parameters, &quot;period&quot;, String.class);</span>
<span class="fc" id="L219">        return getRequestHandler().sendPublicRequest(getBaseUrl(), TAKE_BUY_SELL_VOLUME, parameters, HttpMethod.GET, getShowLimitUsage());</span>
    }

<span class="fc" id="L222">    private final String HISTORICAL_BLVT = &quot;/v1/lvtKlines&quot;;</span>
    /**
     * The BLVT NAV system is based on Binance Futures, so the endpoint is based on fapi
     * &lt;br&gt;&lt;br&gt;
     * GET /v1/lvtKlines
     * &lt;br&gt;
     * https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Historical-BLVT-NAV-Kline-Candlestick
     * @param
     * parameters LinkedHashedMap of String,Object pair
     *            where String is the name of the parameter and Object is the value of the parameter
     * &lt;br&gt;&lt;br&gt;
     * symbol -- mandatory/string -- the trading pair &lt;br&gt;
     * interval -- mandatory/enum -- interval &lt;br&gt;
     * startTime -- optional/long -- Start Time &lt;br&gt;
     * endTime -- optional/long -- End Time &lt;br&gt;
     * limit -- optional/long -- default 500, max 1000 &lt;br&gt;
     * @return String
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Historical-BLVT-NAV-Kline-Candlestick&quot;&gt;
     *     https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Historical-BLVT-NAV-Kline-Candlestick&lt;/a&gt;
     */
    public String historicalBlvt(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L243">        ParameterChecker.checkParameter(parameters, &quot;symbol&quot;, String.class);</span>
<span class="fc" id="L244">        ParameterChecker.checkParameter(parameters, &quot;interval&quot;, String.class);</span>
<span class="fc" id="L245">        return getRequestHandler().sendPublicRequest(getProductUrl(), HISTORICAL_BLVT, parameters, HttpMethod.GET, getShowLimitUsage());</span>
    }

<span class="fc" id="L248">    private final String INDEX_INFO = &quot;/v1/indexInfo&quot;;</span>
    /**
     * GET /v1/indexInfo
     * &lt;br&gt;
     * https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Composite-Index-Symbol-Information
     * @param
     * parameters LinkedHashedMap of String,Object pair
     *            where String is the name of the parameter and Object is the value of the parameter
     * &lt;br&gt;&lt;br&gt;
     * symbol -- optional/string -- the trading pair &lt;br&gt;
     * @return String
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Composite-Index-Symbol-Information&quot;&gt;
     *     https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Composite-Index-Symbol-Information&lt;/a&gt;
     */
    public String indexInfo(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L263">        return getRequestHandler().sendPublicRequest(getProductUrl(), INDEX_INFO, parameters, HttpMethod.GET, getShowLimitUsage());</span>
    }

<span class="fc" id="L266">    private final String ASSET_INDEX = &quot;/v1/assetIndex&quot;;</span>
    /**
     * asset index for Multi-Assets mode
     * &lt;br&gt;&lt;br&gt;
     * GET /v1/assetIndex
     * &lt;br&gt;
     * https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Multi-Assets-Mode-Asset-Index
     * @param
     * parameters LinkedHashedMap of String,Object pair
     *            where String is the name of the parameter and Object is the value of the parameter
     * &lt;br&gt;&lt;br&gt;
     * symbol -- optional/string -- the trading pair &lt;br&gt;
     * @return String
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Multi-Assets-Mode-Asset-Index&quot;&gt;
     *     https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Multi-Assets-Mode-Asset-Index&lt;/a&gt;
     */
    public String assetIndex(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L283">        return getRequestHandler().sendPublicRequest(getProductUrl(), ASSET_INDEX, parameters, HttpMethod.GET, getShowLimitUsage());</span>
    }

}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.7.202105040129</span></div></body></html>