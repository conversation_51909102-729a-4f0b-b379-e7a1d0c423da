<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (21) on Fri Apr 04 16:06:08 CST 2025 -->
<title>UMWebsocketClientImpl (io.github.binance:binance-futures-connector-java 3.0.5 API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-04-04">
<meta name="description" content="declaration: package: com.binance.connector.futures.client.impl, class: UMWebsocketClientImpl">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="切换导航链接"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="../../../../../../index.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="nav-bar-cell1-rev">类</li>
<li><a href="class-use/UMWebsocketClientImpl.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../../index-all.html">索引</a></li>
<li><a href="../../../../../../help-doc.html#class">帮助</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>概要:</p>
<ul>
<li>嵌套</li>
<li>字段</li>
<li><a href="#constructor-summary">构造器</a></li>
<li><a href="#method-summary">方法</a></li>
</ul>
</li>
<li>
<p>详细资料:</p>
<ul>
<li>字段</li>
<li><a href="#constructor-detail">构造器</a></li>
<li><a href="#method-detail">方法</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">方法</a></li>
</ul>
<ul class="sub-nav-list">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">方法</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="搜索">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">程序包</span>&nbsp;<a href="package-summary.html">com.binance.connector.futures.client.impl</a></div>
<h1 title="类 UMWebsocketClientImpl" class="title">类 UMWebsocketClientImpl</h1>
</div>
<div class="inheritance" title="继承树"><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="WebsocketClientImpl.html" title="com.binance.connector.futures.client.impl中的类">com.binance.connector.futures.client.impl.WebsocketClientImpl</a>
<div class="inheritance">com.binance.connector.futures.client.impl.UMWebsocketClientImpl</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>所有已实现的接口:</dt>
<dd><code><a href="../WebsocketClient.html" title="com.binance.connector.futures.client中的接口">WebsocketClient</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">UMWebsocketClientImpl</span>
<span class="extends-implements">extends <a href="WebsocketClientImpl.html" title="com.binance.connector.futures.client.impl中的类">WebsocketClientImpl</a></span></div>
<div class="block"><h2 id="usd--m-websocket-streams-heading">USDⓈ-M  Websocket Streams</h2>
 All stream endpoints under the
 <a href="https://developers.binance.com/docs/derivatives/usds-margined-futures/websocket-market-streams/Connect"> Websocket Market Streams</a> and
 <a href="https://developers.binance.com/docs/derivatives/usds-margined-futures/user-data-streams/Connect"> User Data Streams</a>
 section of the API documentation will be implemented in this class.
 <br>
 Response will be returned as callback.</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>构造器概要</h2>
<div class="caption"><span>构造器</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">构造器</div>
<div class="table-header col-last">说明</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">UMWebsocketClientImpl</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(java.lang.String)" class="member-name-link">UMWebsocketClientImpl</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;baseUrl)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>方法概要</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">所有方法</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">实例方法</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">具体方法</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">修饰符和类型</div>
<div class="table-header col-second">方法</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#allMarkPriceStream(int,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">allMarkPriceStream</a><wbr>(int&nbsp;speed,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Mark price and funding rate for all symbols pushed every 3 seconds or every second.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#allMarkPriceStream(int,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">allMarkPriceStream</a><wbr>(int&nbsp;speed,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onOpenCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onClosingCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onFailureCallback)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Same as <a href="#allMarkPriceStream(int,com.binance.connector.futures.client.utils.WebSocketCallback)"><code>allMarkPriceStream(int, WebSocketCallback)</code></a> plus accepts callbacks for all major websocket connection events.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#compositeIndexSymbolInfo(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">compositeIndexSymbolInfo</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;symbol,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Composite index information for index symbols pushed every second.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#compositeIndexSymbolInfo(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">compositeIndexSymbolInfo</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;symbol,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onOpenCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onClosingCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onFailureCallback)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Same as <a href="#compositeIndexSymbolInfo(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)"><code>compositeIndexSymbolInfo(String, WebSocketCallback)</code></a> plus accepts callbacks for all major websocket connection events.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-com.binance.connector.futures.client.impl.WebsocketClientImpl">从类继承的方法&nbsp;com.binance.connector.futures.client.impl.<a href="WebsocketClientImpl.html" title="com.binance.connector.futures.client.impl中的类">WebsocketClientImpl</a></h3>
<code><a href="WebsocketClientImpl.html#aggTradeStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)">aggTradeStream</a>, <a href="WebsocketClientImpl.html#aggTradeStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)">aggTradeStream</a>, <a href="WebsocketClientImpl.html#allBookTickerStream(com.binance.connector.futures.client.utils.WebSocketCallback)">allBookTickerStream</a>, <a href="WebsocketClientImpl.html#allBookTickerStream(com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)">allBookTickerStream</a>, <a href="WebsocketClientImpl.html#allForceOrderStream(com.binance.connector.futures.client.utils.WebSocketCallback)">allForceOrderStream</a>, <a href="WebsocketClientImpl.html#allForceOrderStream(com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)">allForceOrderStream</a>, <a href="WebsocketClientImpl.html#allMiniTickerStream(com.binance.connector.futures.client.utils.WebSocketCallback)">allMiniTickerStream</a>, <a href="WebsocketClientImpl.html#allMiniTickerStream(com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)">allMiniTickerStream</a>, <a href="WebsocketClientImpl.html#allTickerStream(com.binance.connector.futures.client.utils.WebSocketCallback)">allTickerStream</a>, <a href="WebsocketClientImpl.html#allTickerStream(com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)">allTickerStream</a>, <a href="WebsocketClientImpl.html#bookTicker(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)">bookTicker</a>, <a href="WebsocketClientImpl.html#bookTicker(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)">bookTicker</a>, <a href="WebsocketClientImpl.html#closeAllConnections()">closeAllConnections</a>, <a href="WebsocketClientImpl.html#closeConnection(int)">closeConnection</a>, <a href="WebsocketClientImpl.html#combineStreams(java.util.ArrayList,com.binance.connector.futures.client.utils.WebSocketCallback)">combineStreams</a>, <a href="WebsocketClientImpl.html#combineStreams(java.util.ArrayList,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)">combineStreams</a>, <a href="WebsocketClientImpl.html#continuousKlineStream(java.lang.String,java.lang.String,java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)">continuousKlineStream</a>, <a href="WebsocketClientImpl.html#continuousKlineStream(java.lang.String,java.lang.String,java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)">continuousKlineStream</a>, <a href="WebsocketClientImpl.html#createConnection(com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,okhttp3.Request)">createConnection</a>, <a href="WebsocketClientImpl.html#diffDepthStream(java.lang.String,int,com.binance.connector.futures.client.utils.WebSocketCallback)">diffDepthStream</a>, <a href="WebsocketClientImpl.html#diffDepthStream(java.lang.String,int,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)">diffDepthStream</a>, <a href="WebsocketClientImpl.html#forceOrderStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)">forceOrderStream</a>, <a href="WebsocketClientImpl.html#forceOrderStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)">forceOrderStream</a>, <a href="WebsocketClientImpl.html#getBaseUrl()">getBaseUrl</a>, <a href="WebsocketClientImpl.html#getNoopCallback()">getNoopCallback</a>, <a href="WebsocketClientImpl.html#klineStream(java.lang.String,java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)">klineStream</a>, <a href="WebsocketClientImpl.html#klineStream(java.lang.String,java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)">klineStream</a>, <a href="WebsocketClientImpl.html#listenUserStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)">listenUserStream</a>, <a href="WebsocketClientImpl.html#listenUserStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)">listenUserStream</a>, <a href="WebsocketClientImpl.html#markPriceStream(java.lang.String,int,com.binance.connector.futures.client.utils.WebSocketCallback)">markPriceStream</a>, <a href="WebsocketClientImpl.html#markPriceStream(java.lang.String,int,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)">markPriceStream</a>, <a href="WebsocketClientImpl.html#miniTickerStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)">miniTickerStream</a>, <a href="WebsocketClientImpl.html#miniTickerStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)">miniTickerStream</a>, <a href="WebsocketClientImpl.html#partialDepthStream(java.lang.String,int,int,com.binance.connector.futures.client.utils.WebSocketCallback)">partialDepthStream</a>, <a href="WebsocketClientImpl.html#partialDepthStream(java.lang.String,int,int,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)">partialDepthStream</a>, <a href="WebsocketClientImpl.html#symbolTicker(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)">symbolTicker</a>, <a href="WebsocketClientImpl.html#symbolTicker(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)">symbolTicker</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">从类继承的方法&nbsp;java.lang.<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a></h3>
<code><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html#clone--" title="java.lang中的类或接口" class="external-link">clone</a>, <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html#equals-java.lang.Object-" title="java.lang中的类或接口" class="external-link">equals</a>, <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html#finalize--" title="java.lang中的类或接口" class="external-link">finalize</a>, <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html#getClass--" title="java.lang中的类或接口" class="external-link">getClass</a>, <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html#hashCode--" title="java.lang中的类或接口" class="external-link">hashCode</a>, <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html#notify--" title="java.lang中的类或接口" class="external-link">notify</a>, <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html#notifyAll--" title="java.lang中的类或接口" class="external-link">notifyAll</a>, <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html#toString--" title="java.lang中的类或接口" class="external-link">toString</a>, <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html#wait--" title="java.lang中的类或接口" class="external-link">wait</a>, <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html#wait-long-" title="java.lang中的类或接口" class="external-link">wait</a>, <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html#wait-long-int-" title="java.lang中的类或接口" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>构造器详细资料</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>UMWebsocketClientImpl</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">UMWebsocketClientImpl</span>()</div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(java.lang.String)">
<h3>UMWebsocketClientImpl</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">UMWebsocketClientImpl</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;baseUrl)</span></div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>方法详细资料</h2>
<ul class="member-list">
<li>
<section class="detail" id="allMarkPriceStream(int,com.binance.connector.futures.client.utils.WebSocketCallback)">
<h3>allMarkPriceStream</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">allMarkPriceStream</span><wbr><span class="parameters">(int&nbsp;speed,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback)</span></div>
<div class="block">Mark price and funding rate for all symbols pushed every 3 seconds or every second.
 <br><br>
 &lt;symbol&gt;@markPrice or &lt;symbol&gt;@markPrice@1s
 <br><br>
 Update Speed: 3000ms or 1000ms</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>speed</code> - speed in seconds, can be 1 or 3</dd>
<dd><code>onMessageCallback</code> - onMessageCallback</dd>
<dt>返回:</dt>
<dd>int - Connection ID</dd>
<dt>另请参阅:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://developers.binance.com/docs/derivatives/usds-margined-futures/websocket-market-streams/Mark-Price-Stream">
 https://developers.binance.com/docs/derivatives/usds-margined-futures/websocket-market-streams/Mark-Price-Stream</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="allMarkPriceStream(int,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)">
<h3>allMarkPriceStream</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">allMarkPriceStream</span><wbr><span class="parameters">(int&nbsp;speed,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onOpenCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onClosingCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onFailureCallback)</span></div>
<div class="block">Same as <a href="#allMarkPriceStream(int,com.binance.connector.futures.client.utils.WebSocketCallback)"><code>allMarkPriceStream(int, WebSocketCallback)</code></a> plus accepts callbacks for all major websocket connection events.</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>speed</code> - speed in seconds, can be 1 or 3</dd>
<dd><code>onOpenCallback</code> - onOpenCallback</dd>
<dd><code>onMessageCallback</code> - onMessageCallback</dd>
<dd><code>onClosingCallback</code> - onClosingCallback</dd>
<dd><code>onFailureCallback</code> - onFailureCallback</dd>
<dt>返回:</dt>
<dd>int - Connection ID</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="compositeIndexSymbolInfo(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)">
<h3>compositeIndexSymbolInfo</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">compositeIndexSymbolInfo</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;symbol,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback)</span></div>
<div class="block">Composite index information for index symbols pushed every second.
 <br><br>
 &lt;symbol&gt;@compositeIndex
 <br><br>
 Update Speed: 1000ms</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>symbol</code> - trading symbol</dd>
<dd><code>onMessageCallback</code> - onMessageCallback</dd>
<dt>返回:</dt>
<dd>int - Connection ID</dd>
<dt>另请参阅:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://developers.binance.com/docs/derivatives/usds-margined-futures/websocket-market-streams/Composite-Index-Symbol-Information-Streams">
 https://developers.binance.com/docs/derivatives/usds-margined-futures/websocket-market-streams/Composite-Index-Symbol-Information-Streams</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="compositeIndexSymbolInfo(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)">
<h3>compositeIndexSymbolInfo</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">compositeIndexSymbolInfo</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;symbol,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onOpenCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onClosingCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onFailureCallback)</span></div>
<div class="block">Same as <a href="#compositeIndexSymbolInfo(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)"><code>compositeIndexSymbolInfo(String, WebSocketCallback)</code></a> plus accepts callbacks for all major websocket connection events.</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>symbol</code> - trading symbol</dd>
<dd><code>onOpenCallback</code> - onOpenCallback</dd>
<dd><code>onMessageCallback</code> - onMessageCallback</dd>
<dd><code>onClosingCallback</code> - onClosingCallback</dd>
<dd><code>onFailureCallback</code> - onFailureCallback</dd>
<dt>返回:</dt>
<dd>int - Connection ID</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &#169; 2025. All rights reserved.</small></p>
</footer>
</div>
</div>
</body>
</html>
