package com.crypto.trading.sdk.response.model;

import org.junit.jupiter.api.Test;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.*;

/**
 * K线数据模型测试类
 */
public class KlineDataTest {

    @Test
    public void testDefaultConstructor() {
        // 测试默认构造函数
        KlineData klineData = new KlineData();
        
        // 验证默认值
        assertNull(klineData.getOpenTime());
        assertNull(klineData.getOpen());
        assertNull(klineData.getHigh());
        assertNull(klineData.getLow());
        assertNull(klineData.getClose());
        assertNull(klineData.getVolume());
        assertNull(klineData.getCloseTime());
        assertNull(klineData.getQuoteAssetVolume());
        assertNull(klineData.getNumberOfTrades());
        assertNull(klineData.getTakerBuyBaseAssetVolume());
        assertNull(klineData.getTakerBuyQuoteAssetVolume());
        assertNull(klineData.getIgnore());
    }

    @Test
    public void testFromArray() {
        // 测试从数组创建K线数据
        Object[] klineArray = new Object[]{
                "1618560000000",   // 开盘时间
                "54321.12",        // 开盘价
                "54987.65",        // 最高价
                "54123.45",        // 最低价
                "54789.98",        // 收盘价
                "12.345",          // 成交量
                "1618563599999",   // 收盘时间
                "675321.98",       // 成交金额
                "1234",            // 成交笔数
                "8.765",           // 主动买入成交量
                "470123.45",       // 主动买入成交金额
                "0"                // 忽略
        };
        
        // 创建K线数据
        KlineData klineData = KlineData.fromArray(klineArray);
        
        // 验证数据
        assertEquals(1618560000000L, klineData.getOpenTime());
        assertEquals(new BigDecimal("54321.12"), klineData.getOpen());
        assertEquals(new BigDecimal("54987.65"), klineData.getHigh());
        assertEquals(new BigDecimal("54123.45"), klineData.getLow());
        assertEquals(new BigDecimal("54789.98"), klineData.getClose());
        assertEquals(new BigDecimal("12.345"), klineData.getVolume());
        assertEquals(1618563599999L, klineData.getCloseTime());
        assertEquals(new BigDecimal("675321.98"), klineData.getQuoteAssetVolume());
        assertEquals(1234L, klineData.getNumberOfTrades());
        assertEquals(new BigDecimal("8.765"), klineData.getTakerBuyBaseAssetVolume());
        assertEquals(new BigDecimal("470123.45"), klineData.getTakerBuyQuoteAssetVolume());
        assertEquals(new BigDecimal("0"), klineData.getIgnore());
    }

    @Test
    public void testFromArrayInvalidInput() {
        // 测试无效输入
        Object[] invalidArray = new Object[]{"1618560000000", "54321.12"};
        
        // 应该抛出异常
        assertThrows(IllegalArgumentException.class, () -> {
            KlineData.fromArray(invalidArray);
        });
    }

    @Test
    public void testSettersAndGetters() {
        // 测试设置器和获取器
        KlineData klineData = new KlineData();
        
        // 设置属性
        klineData.setOpenTime(1618560000000L);
        klineData.setOpen(new BigDecimal("54321.12"));
        klineData.setHigh(new BigDecimal("54987.65"));
        klineData.setLow(new BigDecimal("54123.45"));
        klineData.setClose(new BigDecimal("54789.98"));
        klineData.setVolume(new BigDecimal("12.345"));
        klineData.setCloseTime(1618563599999L);
        klineData.setQuoteAssetVolume(new BigDecimal("675321.98"));
        klineData.setNumberOfTrades(1234L);
        klineData.setTakerBuyBaseAssetVolume(new BigDecimal("8.765"));
        klineData.setTakerBuyQuoteAssetVolume(new BigDecimal("470123.45"));
        klineData.setIgnore(new BigDecimal("0"));
        
        // 验证属性
        assertEquals(1618560000000L, klineData.getOpenTime());
        assertEquals(new BigDecimal("54321.12"), klineData.getOpen());
        assertEquals(new BigDecimal("54987.65"), klineData.getHigh());
        assertEquals(new BigDecimal("54123.45"), klineData.getLow());
        assertEquals(new BigDecimal("54789.98"), klineData.getClose());
        assertEquals(new BigDecimal("12.345"), klineData.getVolume());
        assertEquals(1618563599999L, klineData.getCloseTime());
        assertEquals(new BigDecimal("675321.98"), klineData.getQuoteAssetVolume());
        assertEquals(1234L, klineData.getNumberOfTrades());
        assertEquals(new BigDecimal("8.765"), klineData.getTakerBuyBaseAssetVolume());
        assertEquals(new BigDecimal("470123.45"), klineData.getTakerBuyQuoteAssetVolume());
        assertEquals(new BigDecimal("0"), klineData.getIgnore());
    }

    @Test
    public void testToString() {
        // 测试toString方法
        KlineData klineData = new KlineData();
        klineData.setOpenTime(1618560000000L);
        klineData.setClose(new BigDecimal("54789.98"));
        
        String toString = klineData.toString();
        assertNotNull(toString);
        assertTrue(toString.contains("1618560000000"));
        assertTrue(toString.contains("54789.98"));
    }
} 