package com.crypto.trading.common.exception;

/**
 * API调用异常
 * <p>
 * 用于表示调用外部API时发生的异常
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class ApiException extends RuntimeException {
    
    /**
     * 错误码
     */
    private final int code;
    
    /**
     * 异常数据
     */
    private final Object data;

    /**
     * 构造函数
     *
     * @param message 异常消息
     */
    public ApiException(String message) {
        super(message);
        this.code = ErrorCode.API_ERROR.getCode();
        this.data = null;
    }

    /**
     * 构造函数
     *
     * @param code    异常代码
     * @param message 异常消息
     */
    public ApiException(int code, String message) {
        super(message);
        this.code = code;
        this.data = null;
    }

    /**
     * 构造函数
     *
     * @param errorCode 错误码枚举
     */
    public ApiException(ErrorCode errorCode) {
        super(errorCode.getMessage());
        this.code = errorCode.getCode();
        this.data = null;
    }

    /**
     * 构造函数
     *
     * @param errorCode 错误码枚举
     * @param message   异常消息
     */
    public ApiException(ErrorCode errorCode, String message) {
        super(message);
        this.code = errorCode.getCode();
        this.data = null;
    }

    /**
     * 构造函数
     *
     * @param code    异常代码
     * @param message 异常消息
     * @param cause   原始异常
     */
    public ApiException(int code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
        this.data = null;
    }

    /**
     * 构造函数
     *
     * @param errorCode 错误码枚举
     * @param cause     原始异常
     */
    public ApiException(ErrorCode errorCode, Throwable cause) {
        super(errorCode.getMessage(), cause);
        this.code = errorCode.getCode();
        this.data = null;
    }

    /**
     * 构造函数
     *
     * @param code    异常代码
     * @param message 异常消息
     * @param data    异常数据
     */
    public ApiException(int code, String message, Object data) {
        super(message);
        this.code = code;
        this.data = data;
    }

    /**
     * 构造函数
     *
     * @param errorCode 错误码枚举
     * @param data      异常数据
     */
    public ApiException(ErrorCode errorCode, Object data) {
        super(errorCode.getMessage());
        this.code = errorCode.getCode();
        this.data = data;
    }

    /**
     * 获取错误码
     *
     * @return 错误码
     */
    public int getCode() {
        return code;
    }

    /**
     * 获取异常数据
     *
     * @return 异常数据
     */
    public Object getData() {
        return data;
    }
}