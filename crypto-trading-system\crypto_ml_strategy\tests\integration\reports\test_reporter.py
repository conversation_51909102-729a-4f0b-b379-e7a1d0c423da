#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试报告系统模块

负责生成详细的测试报告，支持多种格式输出和可视化分析。
"""

import json
import xml.etree.ElementTree as ET
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
import base64
from loguru import logger


@dataclass
class ReportConfig:
    """报告配置"""
    output_dir: str = "reports"
    include_charts: bool = True
    include_logs: bool = True
    include_artifacts: bool = True
    chart_width: int = 800
    chart_height: int = 400
    max_log_lines: int = 1000


class TestReporter:
    """测试报告生成器"""
    
    def __init__(self, config: Optional[ReportConfig] = None):
        """
        初始化测试报告生成器
        
        Args:
            config: 报告配置
        """
        self.config = config or ReportConfig()
        self.output_dir = Path(self.config.output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        logger.info(f"测试报告生成器已初始化 - 输出目录: {self.output_dir}")
    
    def generate_html_report(self, test_results: Dict[str, Any], 
                           execution_summary: Dict[str, Any],
                           report_name: str = "integration_test_report") -> str:
        """
        生成HTML格式报告
        
        Args:
            test_results: 测试结果
            execution_summary: 执行摘要
            report_name: 报告名称
        
        Returns:
            str: 报告文件路径
        """
        logger.info("生成HTML测试报告...")
        
        html_content = self._generate_html_content(test_results, execution_summary)
        
        report_file = self.output_dir / f"{report_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        logger.info(f"HTML报告已生成: {report_file}")
        return str(report_file)
    
    def generate_json_report(self, test_results: Dict[str, Any],
                           execution_summary: Dict[str, Any],
                           report_name: str = "integration_test_report") -> str:
        """
        生成JSON格式报告
        
        Args:
            test_results: 测试结果
            execution_summary: 执行摘要
            report_name: 报告名称
        
        Returns:
            str: 报告文件路径
        """
        logger.info("生成JSON测试报告...")
        
        report_data = {
            'report_metadata': {
                'generated_at': datetime.now().isoformat(),
                'report_name': report_name,
                'generator_version': '1.0.0'
            },
            'execution_summary': execution_summary,
            'test_results': self._serialize_test_results(test_results),
            'performance_metrics': self._calculate_performance_metrics(test_results),
            'error_analysis': self._analyze_errors(test_results),
            'trend_analysis': self._analyze_trends(test_results)
        }
        
        report_file = self.output_dir / f"{report_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False, default=str)
        
        logger.info(f"JSON报告已生成: {report_file}")
        return str(report_file)
    
    def generate_xml_report(self, test_results: Dict[str, Any],
                          execution_summary: Dict[str, Any],
                          report_name: str = "integration_test_report") -> str:
        """
        生成XML格式报告（JUnit兼容）
        
        Args:
            test_results: 测试结果
            execution_summary: 执行摘要
            report_name: 报告名称
        
        Returns:
            str: 报告文件路径
        """
        logger.info("生成XML测试报告...")
        
        root = ET.Element("testsuites")
        root.set("name", report_name)
        root.set("tests", str(execution_summary.get('total_tests', 0)))
        root.set("failures", str(execution_summary.get('failed_tests', 0)))
        root.set("errors", str(execution_summary.get('error_tests', 0)))
        root.set("time", str(execution_summary.get('total_duration', 0)))
        root.set("timestamp", datetime.now().isoformat())
        
        # 按套件分组测试结果
        suites_data = self._group_by_suite(test_results)
        
        for suite_name, suite_tests in suites_data.items():
            testsuite = ET.SubElement(root, "testsuite")
            testsuite.set("name", suite_name)
            testsuite.set("tests", str(len(suite_tests)))
            
            suite_failures = sum(1 for t in suite_tests if t.get('status') == 'FAILED')
            suite_errors = sum(1 for t in suite_tests if t.get('status') == 'ERROR')
            suite_time = sum(t.get('duration', 0) for t in suite_tests)
            
            testsuite.set("failures", str(suite_failures))
            testsuite.set("errors", str(suite_errors))
            testsuite.set("time", str(suite_time))
            
            for test in suite_tests:
                testcase = ET.SubElement(testsuite, "testcase")
                testcase.set("name", test.get('name', ''))
                testcase.set("classname", suite_name)
                testcase.set("time", str(test.get('duration', 0)))
                
                if test.get('status') == 'FAILED':
                    failure = ET.SubElement(testcase, "failure")
                    failure.set("message", test.get('error_message', ''))
                    failure.text = test.get('error_traceback', '')
                
                elif test.get('status') == 'ERROR':
                    error = ET.SubElement(testcase, "error")
                    error.set("message", test.get('error_message', ''))
                    error.text = test.get('error_traceback', '')
                
                elif test.get('status') == 'SKIPPED':
                    ET.SubElement(testcase, "skipped")
        
        report_file = self.output_dir / f"{report_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xml"
        
        tree = ET.ElementTree(root)
        tree.write(report_file, encoding='utf-8', xml_declaration=True)
        
        logger.info(f"XML报告已生成: {report_file}")
        return str(report_file)
    
    def _generate_html_content(self, test_results: Dict[str, Any], 
                             execution_summary: Dict[str, Any]) -> str:
        """生成HTML内容"""
        
        # HTML模板
        html_template = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>集成测试报告</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .metric-card { background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center; border-left: 4px solid #007bff; }
        .metric-value { font-size: 2em; font-weight: bold; color: #007bff; }
        .metric-label { color: #666; margin-top: 5px; }
        .success { border-left-color: #28a745; } .success .metric-value { color: #28a745; }
        .danger { border-left-color: #dc3545; } .danger .metric-value { color: #dc3545; }
        .warning { border-left-color: #ffc107; } .warning .metric-value { color: #ffc107; }
        .test-results { margin-top: 30px; }
        .test-item { background: white; border: 1px solid #ddd; margin-bottom: 10px; border-radius: 5px; }
        .test-header { padding: 15px; cursor: pointer; display: flex; justify-content: between; align-items: center; }
        .test-header:hover { background: #f8f9fa; }
        .test-status { padding: 5px 10px; border-radius: 3px; color: white; font-size: 0.8em; }
        .status-passed { background: #28a745; }
        .status-failed { background: #dc3545; }
        .status-timeout { background: #ffc107; }
        .status-error { background: #6c757d; }
        .test-details { padding: 15px; border-top: 1px solid #eee; display: none; }
        .error-details { background: #f8d7da; padding: 10px; border-radius: 3px; margin-top: 10px; }
        .chart-container { margin: 20px 0; text-align: center; }
        .progress-bar { width: 100%; height: 20px; background: #e9ecef; border-radius: 10px; overflow: hidden; }
        .progress-fill { height: 100%; background: linear-gradient(90deg, #28a745, #20c997); transition: width 0.3s ease; }
    </style>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 集成测试报告</h1>
            <p>生成时间: {generated_time}</p>
        </div>
        
        <div class="summary">
            <div class="metric-card">
                <div class="metric-value">{total_tests}</div>
                <div class="metric-label">总测试数</div>
            </div>
            <div class="metric-card success">
                <div class="metric-value">{passed_tests}</div>
                <div class="metric-label">通过测试</div>
            </div>
            <div class="metric-card danger">
                <div class="metric-value">{failed_tests}</div>
                <div class="metric-label">失败测试</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">{success_rate:.1f}%</div>
                <div class="metric-label">成功率</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">{total_duration:.1f}s</div>
                <div class="metric-label">总耗时</div>
            </div>
        </div>
        
        <div class="progress-bar">
            <div class="progress-fill" style="width: {success_rate}%"></div>
        </div>
        
        {charts_section}
        
        <div class="test-results">
            <h2>📋 测试结果详情</h2>
            {test_results_html}
        </div>
    </div>
    
    <script>
        function toggleDetails(testId) {{
            const details = document.getElementById('details-' + testId);
            details.style.display = details.style.display === 'none' ? 'block' : 'none';
        }}
    </script>
</body>
</html>
        """
        
        # 生成测试结果HTML
        test_results_html = self._generate_test_results_html(test_results)
        
        # 生成图表部分
        charts_section = ""
        if self.config.include_charts:
            charts_section = self._generate_charts_html(test_results, execution_summary)
        
        # 填充模板
        return html_template.format(
            generated_time=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            total_tests=execution_summary.get('total_tests', 0),
            passed_tests=execution_summary.get('passed_tests', 0),
            failed_tests=execution_summary.get('failed_tests', 0),
            success_rate=execution_summary.get('success_rate', 0),
            total_duration=execution_summary.get('total_duration', 0),
            charts_section=charts_section,
            test_results_html=test_results_html
        )
    
    def _generate_test_results_html(self, test_results: Dict[str, Any]) -> str:
        """生成测试结果HTML"""
        html_parts = []
        
        for test_id, result in test_results.items():
            status = result.status.value if hasattr(result.status, 'value') else str(result.status)
            status_class = f"status-{status.lower()}"
            
            html_parts.append(f"""
            <div class="test-item">
                <div class="test-header" onclick="toggleDetails('{test_id}')">
                    <div>
                        <strong>{result.name}</strong>
                        <span class="test-status {status_class}">{status.upper()}</span>
                    </div>
                    <div>
                        <small>耗时: {result.duration:.2f}s</small>
                    </div>
                </div>
                <div id="details-{test_id}" class="test-details">
                    <p><strong>测试ID:</strong> {test_id}</p>
                    <p><strong>开始时间:</strong> {result.start_time}</p>
                    <p><strong>结束时间:</strong> {result.end_time or 'N/A'}</p>
                    <p><strong>持续时间:</strong> {result.duration:.2f} 秒</p>
                    
                    {self._generate_error_html(result) if result.error_message else ''}
                    {self._generate_metrics_html(result.metrics) if result.metrics else ''}
                </div>
            </div>
            """)
        
        return '\n'.join(html_parts)
    
    def _generate_error_html(self, result) -> str:
        """生成错误信息HTML"""
        if not result.error_message:
            return ""
        
        return f"""
        <div class="error-details">
            <h4>❌ 错误信息</h4>
            <p><strong>错误:</strong> {result.error_message}</p>
            {f'<pre>{result.error_traceback}</pre>' if result.error_traceback else ''}
        </div>
        """
    
    def _generate_metrics_html(self, metrics: Dict[str, Any]) -> str:
        """生成指标HTML"""
        if not metrics:
            return ""
        
        metrics_html = "<h4>📊 性能指标</h4><ul>"
        for key, value in metrics.items():
            metrics_html += f"<li><strong>{key}:</strong> {value}</li>"
        metrics_html += "</ul>"
        
        return metrics_html
    
    def _generate_charts_html(self, test_results: Dict[str, Any], 
                            execution_summary: Dict[str, Any]) -> str:
        """生成图表HTML"""
        return f"""
        <div class="chart-container">
            <h2>📈 测试结果分析</h2>
            <canvas id="statusChart" width="{self.config.chart_width}" height="{self.config.chart_height}"></canvas>
        </div>
        
        <script>
        const ctx = document.getElementById('statusChart').getContext('2d');
        new Chart(ctx, {{
            type: 'doughnut',
            data: {{
                labels: ['通过', '失败', '超时', '错误'],
                datasets: [{{
                    data: [
                        {execution_summary.get('passed_tests', 0)},
                        {execution_summary.get('failed_tests', 0)},
                        {execution_summary.get('timeout_tests', 0)},
                        {execution_summary.get('error_tests', 0)}
                    ],
                    backgroundColor: ['#28a745', '#dc3545', '#ffc107', '#6c757d']
                }}]
            }},
            options: {{
                responsive: true,
                plugins: {{
                    title: {{
                        display: true,
                        text: '测试结果分布'
                    }}
                }}
            }}
        }});
        </script>
        """
    
    def _serialize_test_results(self, test_results: Dict[str, Any]) -> Dict[str, Any]:
        """序列化测试结果"""
        serialized = {}
        
        for test_id, result in test_results.items():
            serialized[test_id] = {
                'test_id': result.test_id,
                'name': result.name,
                'status': result.status.value if hasattr(result.status, 'value') else str(result.status),
                'start_time': result.start_time.isoformat() if result.start_time else None,
                'end_time': result.end_time.isoformat() if result.end_time else None,
                'duration': result.duration,
                'error_message': result.error_message,
                'error_traceback': result.error_traceback,
                'output': result.output,
                'metrics': result.metrics,
                'artifacts': result.artifacts
            }
        
        return serialized
    
    def _calculate_performance_metrics(self, test_results: Dict[str, Any]) -> Dict[str, Any]:
        """计算性能指标"""
        if not test_results:
            return {}
        
        durations = [r.duration for r in test_results.values() if r.duration > 0]
        
        if not durations:
            return {}
        
        return {
            'min_duration': min(durations),
            'max_duration': max(durations),
            'avg_duration': sum(durations) / len(durations),
            'total_duration': sum(durations),
            'duration_percentiles': {
                'p50': self._percentile(durations, 50),
                'p90': self._percentile(durations, 90),
                'p95': self._percentile(durations, 95),
                'p99': self._percentile(durations, 99)
            }
        }
    
    def _percentile(self, data: List[float], percentile: int) -> float:
        """计算百分位数"""
        if not data:
            return 0.0
        
        sorted_data = sorted(data)
        index = (percentile / 100) * (len(sorted_data) - 1)
        
        if index.is_integer():
            return sorted_data[int(index)]
        else:
            lower = sorted_data[int(index)]
            upper = sorted_data[int(index) + 1]
            return lower + (upper - lower) * (index - int(index))
    
    def _analyze_errors(self, test_results: Dict[str, Any]) -> Dict[str, Any]:
        """分析错误"""
        error_analysis = {
            'error_categories': {},
            'common_errors': {},
            'error_trends': []
        }
        
        for result in test_results.values():
            if result.error_message:
                # 错误分类
                error_type = type(result.error_message).__name__ if hasattr(result.error_message, '__class__') else 'Unknown'
                error_analysis['error_categories'][error_type] = error_analysis['error_categories'].get(error_type, 0) + 1
                
                # 常见错误
                error_msg = str(result.error_message)[:100]  # 截取前100字符
                error_analysis['common_errors'][error_msg] = error_analysis['common_errors'].get(error_msg, 0) + 1
        
        return error_analysis
    
    def _analyze_trends(self, test_results: Dict[str, Any]) -> Dict[str, Any]:
        """分析趋势"""
        # 这里可以实现更复杂的趋势分析
        # 比如与历史数据对比、性能趋势等
        return {
            'performance_trend': 'stable',  # 可以是 'improving', 'degrading', 'stable'
            'reliability_trend': 'stable',
            'notes': '基于当前测试运行的基础分析'
        }
    
    def _group_by_suite(self, test_results: Dict[str, Any]) -> Dict[str, List[Dict[str, Any]]]:
        """按套件分组测试结果"""
        suites = {}
        
        for test_id, result in test_results.items():
            # 从测试ID中提取套件名称（假设格式为 suite_name.test_name）
            suite_name = test_id.split('.')[0] if '.' in test_id else 'default'
            
            if suite_name not in suites:
                suites[suite_name] = []
            
            suites[suite_name].append(self._serialize_test_results({test_id: result})[test_id])
        
        return suites
    
    def generate_all_reports(self, test_results: Dict[str, Any],
                           execution_summary: Dict[str, Any],
                           report_name: str = "integration_test_report") -> Dict[str, str]:
        """
        生成所有格式的报告
        
        Args:
            test_results: 测试结果
            execution_summary: 执行摘要
            report_name: 报告名称
        
        Returns:
            Dict[str, str]: 各格式报告的文件路径
        """
        logger.info("生成所有格式的测试报告...")
        
        reports = {}
        
        try:
            reports['html'] = self.generate_html_report(test_results, execution_summary, report_name)
            reports['json'] = self.generate_json_report(test_results, execution_summary, report_name)
            reports['xml'] = self.generate_xml_report(test_results, execution_summary, report_name)
            
            logger.info(f"所有报告已生成完成: {len(reports)} 个文件")
            
        except Exception as e:
            logger.error(f"生成报告时发生错误: {str(e)}")
            raise
        
        return reports


def create_test_reporter(config: Optional[ReportConfig] = None) -> TestReporter:
    """
    创建测试报告生成器实例
    
    Args:
        config: 报告配置
    
    Returns:
        TestReporter: 报告生成器实例
    """
    return TestReporter(config)