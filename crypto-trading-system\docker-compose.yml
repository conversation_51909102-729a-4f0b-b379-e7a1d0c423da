version: '3.8'

services:
  # 主应用服务
  crypto-bootstrap:
    build: ./crypto-bootstrap
    image: crypto-trading/bootstrap:latest
    container_name: crypto-bootstrap
    restart: always
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - <PERSON><PERSON>KA_BOOTSTRAP_SERVERS=kafka:9092
      - INFLUXDB_URL=http://influxdb:8086
      - INFLUXDB_TOKEN=1mLmSyBqUYH5OKaSkFdybeS34Z6DyLlR3H3d802mmVvFVCiJ4GwOQzyT8QULaES96tBfPGI9ycDHkseYvPhGoA==
      - MYSQL_USER=root
      - MYSQL_PASSWORD=root
      - BINANCE_API_KEY=${BINANCE_API_KEY}
      - BINANCE_API_SECRET=${BINANCE_API_SECRET}
      - BINANCE_USE_TESTNET=true
    volumes:
      - ./logs:/logs
    networks:
      - crypto-net
    depends_on:
      - mysql
      - kafka
      - influxdb
      - strategy-service
  
  # 策略服务（可以横向扩展）
  strategy-service:
    build: ./crypto_ml_strategy
    image: crypto-trading/strategy:latest
    container_name: crypto-strategy
    restart: always
    ports:
      - "8000:8000"
    environment:
      - KAFKA_BOOTSTRAP_SERVERS=kafka:9092
      - LOG_LEVEL=info
    volumes:
      - ./crypto_ml_strategy/models:/app/models
      - ./crypto_ml_strategy/logs:/app/logs
      - ./crypto_ml_strategy/data:/app/data
    networks:
      - crypto-net
    depends_on:
      - kafka
  
  # 策略服务副本（演示负载均衡）
  strategy-service-2:
    build: ./crypto_ml_strategy
    image: crypto-trading/strategy:latest
    container_name: crypto-strategy-2
    restart: always
    ports:
      - "8001:8000"
    environment:
      - KAFKA_BOOTSTRAP_SERVERS=kafka:9092
      - LOG_LEVEL=info
    volumes:
      - ./crypto_ml_strategy/models:/app/models
      - ./crypto_ml_strategy/logs:/app/logs
      - ./crypto_ml_strategy/data:/app/data
    networks:
      - crypto-net
    depends_on:
      - kafka

  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: crypto-mysql
    restart: always
    ports:
      - "3306:3306"
    environment:
      - MYSQL_ROOT_PASSWORD=root
      - MYSQL_DATABASE=crypto_trading
    volumes:
      - mysql-data:/var/lib/mysql
    networks:
      - crypto-net
  
  # Kafka消息队列
  kafka:
    image: bitnami/kafka:latest
    container_name: crypto-kafka
    restart: always
    ports:
      - "9092:9092"
      - "29092:29092"
    environment:
      - KAFKA_ADVERTISED_LISTENERS=PLAINTEXT://kafka:9092,PLAINTEXT_HOST://localhost:29092
      - KAFKA_LISTENERS=PLAINTEXT://:9092,PLAINTEXT_HOST://:29092
      - KAFKA_LISTENER_SECURITY_PROTOCOL_MAP=PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      - ALLOW_PLAINTEXT_LISTENER=yes
    volumes:
      - kafka-data:/bitnami/kafka
    networks:
      - crypto-net
  
  # InfluxDB时序数据库
  influxdb:
    image: influxdb:latest
    container_name: crypto-influxdb
    restart: always
    ports:
      - "8086:8086"
    environment:
      - DOCKER_INFLUXDB_INIT_MODE=setup
      - DOCKER_INFLUXDB_INIT_USERNAME=admin
      - DOCKER_INFLUXDB_INIT_PASSWORD=adminadmin
      - DOCKER_INFLUXDB_INIT_ORG=crypto
      - DOCKER_INFLUXDB_INIT_BUCKET=market_data
      - DOCKER_INFLUXDB_INIT_ADMIN_TOKEN=1mLmSyBqUYH5OKaSkFdybeS34Z6DyLlR3H3d802mmVvFVCiJ4GwOQzyT8QULaES96tBfPGI9ycDHkseYvPhGoA==
    volumes:
      - influxdb-data:/var/lib/influxdb2
    networks:
      - crypto-net

networks:
  crypto-net:
    driver: bridge

volumes:
  mysql-data:
  kafka-data:
  influxdb-data: