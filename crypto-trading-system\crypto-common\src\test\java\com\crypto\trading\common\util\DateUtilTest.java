package com.crypto.trading.common.util;

import org.junit.jupiter.api.Test;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

import static org.junit.jupiter.api.Assertions.*;

/**
 * DateUtil工具类的单元测试
 *
 * <AUTHOR>
 */
public class DateUtilTest {

    /**
     * 测试获取当前日期时间
     */
    @Test
    public void testGetCurrentDateTime() {
        String dateTime = DateUtil.getCurrentDateTime();
        assertNotNull(dateTime);
        assertTrue(dateTime.matches("\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}"));
    }

    /**
     * 测试获取当前日期
     */
    @Test
    public void testGetCurrentDate() {
        String date = DateUtil.getCurrentDate();
        assertNotNull(date);
        assertTrue(date.matches("\\d{4}-\\d{2}-\\d{2}"));
    }

    /**
     * 测试获取当前时间
     */
    @Test
    public void testGetCurrentTime() {
        String time = DateUtil.getCurrentTime();
        assertNotNull(time);
        assertTrue(time.matches("\\d{2}:\\d{2}:\\d{2}"));
    }

    /**
     * 测试获取当前时间戳
     */
    @Test
    public void testGetCurrentTimestamp() {
        long timestamp = DateUtil.getCurrentTimestamp();
        assertTrue(timestamp > 0);
    }

    /**
     * 测试获取当前时间戳（秒）
     */
    @Test
    public void testGetCurrentTimestampSeconds() {
        long timestampSeconds = DateUtil.getCurrentTimestampSeconds();
        assertTrue(timestampSeconds > 0);
        assertTrue(DateUtil.getCurrentTimestamp() / 1000 >= timestampSeconds);
    }

    /**
     * 测试格式化日期时间
     */
    @Test
    public void testFormatDateTime() {
        LocalDateTime now = LocalDateTime.now();
        String formatted = DateUtil.formatDateTime(now, "yyyy/MM/dd HH:mm:ss");
        assertNotNull(formatted);
        assertEquals(now.format(DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss")), formatted);
    }

    /**
     * 测试解析日期时间字符串
     */
    @Test
    public void testParseDateTime() {
        String dateTimeStr = "2023-01-01 12:30:45";
        LocalDateTime dateTime = DateUtil.parseDateTime(dateTimeStr);
        assertNotNull(dateTime);
        assertEquals(2023, dateTime.getYear());
        assertEquals(1, dateTime.getMonthValue());
        assertEquals(1, dateTime.getDayOfMonth());
        assertEquals(12, dateTime.getHour());
        assertEquals(30, dateTime.getMinute());
        assertEquals(45, dateTime.getSecond());
    }

    /**
     * 测试将时间戳转换为日期时间字符串
     */
    @Test
    public void testFormatTimestamp() {
        long timestamp = 1672567845000L; // 2023-01-01 12:30:45
        String formatted = DateUtil.formatTimestamp(timestamp);
        assertNotNull(formatted);
        assertTrue(formatted.startsWith("2023-01-01"));
    }

    /**
     * 测试将日期时间字符串转换为时间戳
     */
    @Test
    public void testParseTimestamp() {
        String dateTimeStr = "2023-01-01 12:30:45";
        long timestamp = DateUtil.parseTimestamp(dateTimeStr);
        assertTrue(timestamp > 0);
    }

    /**
     * 测试计算两个日期之间的天数差
     */
    @Test
    public void testDaysBetween() {
        LocalDate start = LocalDate.of(2023, 1, 1);
        LocalDate end = LocalDate.of(2023, 1, 10);
        long days = DateUtil.daysBetween(start, end);
        assertEquals(9, days);
    }

    /**
     * 测试计算两个日期时间之间的毫秒数差
     */
    @Test
    public void testMillisBetween() {
        LocalDateTime start = LocalDateTime.of(2023, 1, 1, 12, 0, 0);
        LocalDateTime end = LocalDateTime.of(2023, 1, 1, 12, 0, 1);
        long millis = DateUtil.millisBetween(start, end);
        assertEquals(1000, millis);
    }

    /**
     * 测试计算两个时间戳之间的毫秒数差
     */
    @Test
    public void testMillisBetweenTimestamps() {
        long start = 1672567845000L;
        long end = 1672567846000L;
        long millis = DateUtil.millisBetween(start, end);
        assertEquals(1000, millis);
    }

    /**
     * 测试日期时间加上指定的天数
     */
    @Test
    public void testPlusDays() {
        LocalDateTime dateTime = LocalDateTime.of(2023, 1, 1, 12, 0, 0);
        LocalDateTime result = DateUtil.plusDays(dateTime, 5);
        assertEquals(6, result.getDayOfMonth());
    }

    /**
     * 测试日期时间减去指定的天数
     */
    @Test
    public void testMinusDays() {
        LocalDateTime dateTime = LocalDateTime.of(2023, 1, 10, 12, 0, 0);
        LocalDateTime result = DateUtil.minusDays(dateTime, 5);
        assertEquals(5, result.getDayOfMonth());
    }
}