package com.crypto.trading.common.dto.account;

import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

/**
 * AccountDTO单元测试类
 */
class AccountDTOTest {

    /**
     * 测试默认构造函数
     */
    @Test
    void testDefaultConstructor() {
        AccountDTO dto = new AccountDTO();
        assertNotNull(dto);
    }

    /**
     * 测试带参数构造函数
     */
    @Test
    void testParameterizedConstructor() {
        String accountId = "account-001";
        String accountType = "SPOT";
        String status = "NORMAL";
        boolean canTrade = true;
        boolean canWithdraw = true;
        boolean canDeposit = true;
        LocalDateTime createTime = LocalDateTime.now().minusDays(30);
        LocalDateTime updateTime = LocalDateTime.now();

        AccountDTO dto = new AccountDTO(
                accountId, accountType, status, canTrade, canWithdraw, canDeposit,
                createTime, updateTime
        );

        assertNotNull(dto);
        assertEquals(accountId, dto.getAccountId());
        assertEquals(accountType, dto.getAccountType());
        assertEquals(status, dto.getStatus());
        assertEquals(canTrade, dto.isCanTrade());
        assertEquals(canWithdraw, dto.isCanWithdraw());
        assertEquals(canDeposit, dto.isCanDeposit());
        assertEquals(createTime, dto.getCreateTime());
        assertEquals(updateTime, dto.getUpdateTime());
    }

    /**
     * 测试getter和setter方法
     */
    @Test
    void testGettersAndSetters() {
        AccountDTO dto = new AccountDTO();

        String accountId = "account-002";
        dto.setAccountId(accountId);
        assertEquals(accountId, dto.getAccountId());

        String accountType = "FUTURES";
        dto.setAccountType(accountType);
        assertEquals(accountType, dto.getAccountType());

        String status = "SUSPENDED";
        dto.setStatus(status);
        assertEquals(status, dto.getStatus());

        boolean canTrade = false;
        dto.setCanTrade(canTrade);
        assertEquals(canTrade, dto.isCanTrade());

        boolean canWithdraw = false;
        dto.setCanWithdraw(canWithdraw);
        assertEquals(canWithdraw, dto.isCanWithdraw());

        boolean canDeposit = true;
        dto.setCanDeposit(canDeposit);
        assertEquals(canDeposit, dto.isCanDeposit());

        LocalDateTime createTime = LocalDateTime.now().minusDays(60);
        dto.setCreateTime(createTime);
        assertEquals(createTime, dto.getCreateTime());

        LocalDateTime updateTime = LocalDateTime.now();
        dto.setUpdateTime(updateTime);
        assertEquals(updateTime, dto.getUpdateTime());
    }

    /**
     * 测试toString方法
     */
    @Test
    void testToString() {
        AccountDTO dto = new AccountDTO();
        dto.setAccountId("account-003");
        dto.setAccountType("MARGIN");
        dto.setStatus("LOCKED");
        dto.setCanTrade(false);
        dto.setCanWithdraw(false);
        dto.setCanDeposit(false);

        String toString = dto.toString();
        assertNotNull(toString);
        assertTrue(toString.contains("account-003"));
        assertTrue(toString.contains("MARGIN"));
        assertTrue(toString.contains("LOCKED"));
        assertTrue(toString.contains("canTrade=false"));
        assertTrue(toString.contains("canWithdraw=false"));
        assertTrue(toString.contains("canDeposit=false"));
    }
} 