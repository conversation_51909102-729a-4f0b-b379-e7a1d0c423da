package com.crypto.trading.bootstrap.config.adapter;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import com.crypto.trading.bootstrap.config.MarketDataConfig;

import java.util.List;

/**
 * 市场数据配置适配器
 * 负责将bootstrap模块的市场数据配置转换为market-data模块需要的格式
 */
@Configuration
public class MarketDataConfigAdapter {
    
    private static final Logger log = LoggerFactory.getLogger(MarketDataConfigAdapter.class);
    
    @Autowired
    private MarketDataConfig marketDataConfig;
    
    /**
     * 获取交易对列表
     *
     * @return 交易对列表
     */
    @Bean(name = "symbolsList")
    @Primary
    public List<String> symbols() {
        List<String> symbols = marketDataConfig.getSymbolList();
        log.info("适配器加载交易对列表: {}", symbols);
        return symbols;
    }
    
    /**
     * 获取K线间隔列表
     *
     * @return K线间隔列表
     */
    @Bean(name = "klineIntervalsList")
    public List<String> klineIntervals() {
        List<String> intervals = marketDataConfig.getKline().getIntervalList();
        log.info("适配器加载K线间隔列表: {}", intervals);
        return intervals;
    }
}