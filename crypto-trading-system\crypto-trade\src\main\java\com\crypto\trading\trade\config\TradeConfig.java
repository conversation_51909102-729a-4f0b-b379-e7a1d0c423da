package com.crypto.trading.trade.config;

// import com.crypto.trading.sdk.binance.BinanceApiManager;
import io.github.resilience4j.ratelimiter.RateLimiter;
import io.github.resilience4j.ratelimiter.RateLimiterConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;

import java.time.Duration;
import java.util.Arrays;
import java.util.List;

/**
 * 交易模块配置类
 * 
 * <p>提供交易模块所需的配置和依赖</p>
 */
@Configuration
public class TradeConfig {

    private static final Logger log = LoggerFactory.getLogger(TradeConfig.class);

    @Autowired
    private Environment environment;

    @Value("${crypto.trade.execution.enabled:false}")
    private boolean tradeExecutionEnabled;

    @Value("${crypto.trade.execution.dry-run:true}")
    private boolean dryRunMode;

    @Value("${crypto.trade.supported-symbols}")
    private List<String> supportedSymbols;

    @Value("${crypto.trade.rate-limiter.order-requests.capacity:50}")
    private int orderRateLimiterCapacity;

    @Value("${crypto.trade.rate-limiter.order-requests.rate-per-second:5}")
    private int orderRateLimiterRefillRate;

    @Value("${crypto.trade.rate-limiter.query-requests.capacity:500}")
    private int queryRateLimiterCapacity;

    @Value("${crypto.trade.rate-limiter.query-requests.rate-per-second:50}")
    private int queryRateLimiterRefillRate;

    /**
     * 交易执行配置
     *
     * @return 交易执行配置对象
     */
    @Bean
    public TradeExecutionConfig tradeExecutionConfig() {
        TradeExecutionConfig config = new TradeExecutionConfig();
        config.setEnabled(tradeExecutionEnabled);
        config.setDryRun(dryRunMode);
        
        log.info("交易执行已{}，模式: {}", 
                tradeExecutionEnabled ? "启用" : "禁用",
                dryRunMode ? "试运行(不实际下单)" : "实盘");
        
        // 如果在测试环境，设置为试运行模式
        String[] activeProfiles = environment.getActiveProfiles();
        if (Arrays.asList(activeProfiles).contains("test")) {
            config.setDryRun(true);
            log.warn("检测到测试环境，强制设置为试运行模式");
        }
        
        return config;
    }

    /**
     * 币安API订单限速器
     *
     * @return 订单请求限速器
     */
    @Bean(name = "orderRateLimiter")
    public RateLimiter orderRateLimiter() {
        RateLimiterConfig config = RateLimiterConfig.custom()
                .limitRefreshPeriod(Duration.ofSeconds(1))
                .limitForPeriod(orderRateLimiterRefillRate)
                .timeoutDuration(Duration.ofSeconds(2))
                .build();
        
        RateLimiter limiter = RateLimiter.of("orderRateLimiter", config);
        log.info("订单请求限速器已配置: 容量={}, 每秒恢复率={}", 
                orderRateLimiterCapacity, orderRateLimiterRefillRate);
        
        return limiter;
    }

    /**
     * 币安API查询限速器
     *
     * @return 查询请求限速器
     */
    @Bean(name = "queryRateLimiter")
    public RateLimiter queryRateLimiter() {
        RateLimiterConfig config = RateLimiterConfig.custom()
                .limitRefreshPeriod(Duration.ofSeconds(1))
                .limitForPeriod(queryRateLimiterRefillRate)
                .timeoutDuration(Duration.ofSeconds(2))
                .build();
        
        RateLimiter limiter = RateLimiter.of("queryRateLimiter", config);
        log.info("查询请求限速器已配置: 容量={}, 每秒恢复率={}", 
                queryRateLimiterCapacity, queryRateLimiterRefillRate);
        
        return limiter;
    }

    /**
     * 支持的交易对列表
     *
     * @return 交易对列表
     */
    @Bean(name = "supportedSymbols")
    public List<String> supportedSymbols() {
        log.info("支持的交易对: {}", supportedSymbols);
        return supportedSymbols;
    }
}