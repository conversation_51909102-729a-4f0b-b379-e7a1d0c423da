package com.crypto.trading.common.dto.trade;

import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

/**
 * SignalDTO单元测试类
 */
class SignalDTOTest {

    /**
     * 测试默认构造函数
     */
    @Test
    void testDefaultConstructor() {
        SignalDTO dto = new SignalDTO();
        assertNotNull(dto);
    }

    /**
     * 测试带参数构造函数
     */
    @Test
    void testParameterizedConstructor() {
        String id = "signal-123";
        String strategyId = "strategy-001";
        String strategyName = "Moving Average Crossover";
        String symbol = "BTCUSDT";
        String signalType = "BUY";
        Integer strength = 80;
        BigDecimal price = new BigDecimal("50000.00");
        BigDecimal quantity = new BigDecimal("0.5");
        BigDecimal stopLossPrice = new BigDecimal("49000.00");
        BigDecimal takeProfitPrice = new BigDecimal("52000.00");
        LocalDateTime time = LocalDateTime.now();
        LocalDateTime expiryTime = LocalDateTime.now().plusHours(2);
        String description = "BTC突破5万美元压力位，建议买入";
        String parameters = "{\"fastPeriod\":5,\"slowPeriod\":20}";

        SignalDTO dto = new SignalDTO(
                id, strategyId, strategyName, symbol, signalType, strength,
                price, quantity, stopLossPrice, takeProfitPrice, time, expiryTime,
                description, parameters
        );

        assertNotNull(dto);
        assertEquals(id, dto.getId());
        assertEquals(strategyId, dto.getStrategyId());
        assertEquals(strategyName, dto.getStrategyName());
        assertEquals(symbol, dto.getSymbol());
        assertEquals(signalType, dto.getSignalType());
        assertEquals(strength, dto.getStrength());
        assertEquals(price, dto.getPrice());
        assertEquals(quantity, dto.getQuantity());
        assertEquals(stopLossPrice, dto.getStopLossPrice());
        assertEquals(takeProfitPrice, dto.getTakeProfitPrice());
        assertEquals(time, dto.getTime());
        assertEquals(expiryTime, dto.getExpiryTime());
        assertEquals(description, dto.getDescription());
        assertEquals(parameters, dto.getParameters());
    }

    /**
     * 测试getter和setter方法
     */
    @Test
    void testGettersAndSetters() {
        SignalDTO dto = new SignalDTO();

        String id = "signal-456";
        dto.setId(id);
        assertEquals(id, dto.getId());

        String strategyId = "strategy-002";
        dto.setStrategyId(strategyId);
        assertEquals(strategyId, dto.getStrategyId());

        String strategyName = "Bollinger Bands";
        dto.setStrategyName(strategyName);
        assertEquals(strategyName, dto.getStrategyName());

        String symbol = "ETHUSDT";
        dto.setSymbol(symbol);
        assertEquals(symbol, dto.getSymbol());

        String signalType = "SELL";
        dto.setSignalType(signalType);
        assertEquals(signalType, dto.getSignalType());

        Integer strength = 65;
        dto.setStrength(strength);
        assertEquals(strength, dto.getStrength());

        BigDecimal price = new BigDecimal("3000.00");
        dto.setPrice(price);
        assertEquals(price, dto.getPrice());

        BigDecimal quantity = new BigDecimal("2.0");
        dto.setQuantity(quantity);
        assertEquals(quantity, dto.getQuantity());

        BigDecimal stopLossPrice = new BigDecimal("3100.00");
        dto.setStopLossPrice(stopLossPrice);
        assertEquals(stopLossPrice, dto.getStopLossPrice());

        BigDecimal takeProfitPrice = new BigDecimal("2800.00");
        dto.setTakeProfitPrice(takeProfitPrice);
        assertEquals(takeProfitPrice, dto.getTakeProfitPrice());

        LocalDateTime time = LocalDateTime.now();
        dto.setTime(time);
        assertEquals(time, dto.getTime());

        LocalDateTime expiryTime = LocalDateTime.now().plusHours(1);
        dto.setExpiryTime(expiryTime);
        assertEquals(expiryTime, dto.getExpiryTime());

        String description = "ETH触及阻力位，建议卖出";
        dto.setDescription(description);
        assertEquals(description, dto.getDescription());

        String parameters = "{\"period\":20,\"stdDev\":2.0}";
        dto.setParameters(parameters);
        assertEquals(parameters, dto.getParameters());
    }

    /**
     * 测试toString方法
     */
    @Test
    void testToString() {
        SignalDTO dto = new SignalDTO();
        dto.setId("signal-789");
        dto.setStrategyName("RSI Strategy");
        dto.setSymbol("BTCUSDT");
        dto.setSignalType("BUY");

        String toString = dto.toString();
        assertNotNull(toString);
        assertTrue(toString.contains("signal-789"));
        assertTrue(toString.contains("RSI Strategy"));
        assertTrue(toString.contains("BTCUSDT"));
        assertTrue(toString.contains("BUY"));
    }
} 