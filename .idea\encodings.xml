<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="Encoding">
    <file url="file://C:/Windows/System32/src/main/java" charset="UTF-8" />
    <file url="file://C:/Windows/System32/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/common/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/common/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/crypto-common/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/crypto-common/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/crypto-trading-system/crypto-bootstrap/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/crypto-trading-system/crypto-bootstrap/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/crypto-trading-system/crypto-common/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/crypto-trading-system/crypto-common/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/crypto-trading-system/crypto-market-data/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/crypto-trading-system/crypto-market-data/src/main/java/com/crypto/trading/market/config/InfluxDBConfig.java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/crypto-trading-system/crypto-market-data/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/crypto-trading-system/crypto-sdk/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/crypto-trading-system/crypto-sdk/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/crypto-trading-system/crypto-strategy/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/crypto-trading-system/crypto-strategy/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/crypto-trading-system/crypto-trade/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/crypto-trading-system/crypto-trade/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/crypto-trading-system/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/crypto-trading-system/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/market-data/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/market-data/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/src/main/resources" charset="UTF-8" />
  </component>
</project>