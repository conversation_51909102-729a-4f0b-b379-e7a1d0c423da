#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试数据生成器模块

负责生成BTCUSDT/ETHUSDT多时间框架测试数据。
支持时间框架：1m、5m、15m、1h、4h、1d
每个时间框架生成至少10,000个数据点。
包含正常数据、边界情况和异常数据。
"""

import os
import json
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
import random
from loguru import logger


@dataclass
class MarketDataPoint:
    """市场数据点"""
    timestamp: int
    open: float
    high: float
    low: float
    close: float
    volume: float
    symbol: str
    timeframe: str


@dataclass
class GeneratorConfig:
    """数据生成器配置"""
    symbols: List[str]
    timeframes: List[str]
    data_points_per_timeframe: int
    start_date: str
    end_date: str
    include_anomalies: bool
    anomaly_rate: float
    volatility_base: float
    trend_probability: float
    gap_probability: float
    spike_probability: float


class TestDataGenerator:
    """测试数据生成器"""
    
    def __init__(self, config: Optional[GeneratorConfig] = None):
        """
        初始化测试数据生成器
        
        Args:
            config: 生成器配置
        """
        self.config = config or self._get_default_config()
        self.output_dir = Path(__file__).parent
        
        # 时间框架到分钟的映射
        self.timeframe_minutes = {
            "1m": 1,
            "5m": 5,
            "15m": 15,
            "1h": 60,
            "4h": 240,
            "1d": 1440
        }
        
        # 符号基础价格
        self.base_prices = {
            "BTCUSDT": 45000.0,
            "ETHUSDT": 3000.0,
            "BNBUSDT": 400.0,
            "ADAUSDT": 1.2,
            "SOLUSDT": 100.0
        }
        
        # 符号波动率
        self.volatilities = {
            "BTCUSDT": 0.02,
            "ETHUSDT": 0.025,
            "BNBUSDT": 0.03,
            "ADAUSDT": 0.04,
            "SOLUSDT": 0.035
        }
        
        logger.info("测试数据生成器已初始化")
    
    def _get_default_config(self) -> GeneratorConfig:
        """获取默认配置"""
        return GeneratorConfig(
            symbols=["BTCUSDT", "ETHUSDT"],
            timeframes=["1m", "5m", "15m", "1h", "4h", "1d"],
            data_points_per_timeframe=10000,
            start_date="2023-01-01",
            end_date="2024-01-01",
            include_anomalies=True,
            anomaly_rate=0.01,
            volatility_base=0.02,
            trend_probability=0.3,
            gap_probability=0.001,
            spike_probability=0.002
        )
    
    def generate_all_data(self) -> Dict[str, Dict[str, pd.DataFrame]]:
        """
        生成所有符号和时间框架的测试数据
        
        Returns:
            Dict[symbol, Dict[timeframe, DataFrame]]: 生成的数据
        """
        logger.info("开始生成测试数据...")
        
        all_data = {}
        
        for symbol in self.config.symbols:
            logger.info(f"生成 {symbol} 数据...")
            all_data[symbol] = {}
            
            for timeframe in self.config.timeframes:
                logger.info(f"生成 {symbol} {timeframe} 数据...")
                data = self._generate_symbol_timeframe_data(symbol, timeframe)
                all_data[symbol][timeframe] = data
                
                # 保存到文件
                self._save_data_to_file(data, symbol, timeframe)
        
        logger.info("测试数据生成完成")
        return all_data
    
    def _generate_symbol_timeframe_data(self, symbol: str, timeframe: str) -> pd.DataFrame:
        """
        生成特定符号和时间框架的数据
        
        Args:
            symbol: 交易符号
            timeframe: 时间框架
        
        Returns:
            pd.DataFrame: 生成的数据
        """
        # 计算时间参数
        minutes_per_candle = self.timeframe_minutes[timeframe]
        start_time = datetime.strptime(self.config.start_date, "%Y-%m-%d")
        end_time = datetime.strptime(self.config.end_date, "%Y-%m-%d")
        
        # 计算需要的数据点数
        total_minutes = int((end_time - start_time).total_seconds() / 60)
        max_possible_points = total_minutes // minutes_per_candle
        actual_points = min(self.config.data_points_per_timeframe, max_possible_points)
        
        if actual_points < self.config.data_points_per_timeframe:
            logger.warning(f"{symbol} {timeframe}: 时间范围内最多只能生成 {actual_points} 个数据点")
        
        # 生成时间序列
        timestamps = []
        current_time = start_time
        for i in range(actual_points):
            timestamps.append(int(current_time.timestamp() * 1000))
            current_time += timedelta(minutes=minutes_per_candle)
        
        # 生成价格数据
        base_price = self.base_prices.get(symbol, 1000.0)
        volatility = self.volatilities.get(symbol, self.config.volatility_base)
        
        prices = self._generate_price_series(
            base_price=base_price,
            volatility=volatility,
            length=actual_points,
            symbol=symbol
        )
        
        # 生成成交量数据
        volumes = self._generate_volume_series(
            prices=prices,
            length=actual_points,
            symbol=symbol
        )
        
        # 创建DataFrame
        data = pd.DataFrame({
            'timestamp': timestamps,
            'open': prices['open'],
            'high': prices['high'],
            'low': prices['low'],
            'close': prices['close'],
            'volume': volumes,
            'symbol': symbol,
            'timeframe': timeframe
        })
        
        # 添加异常数据
        if self.config.include_anomalies:
            data = self._add_anomalies(data, symbol, timeframe)
        
        # 添加技术指标数据（用于测试）
        data = self._add_technical_indicators(data)
        
        return data
    
    def _generate_price_series(self, base_price: float, volatility: float, 
                             length: int, symbol: str) -> Dict[str, List[float]]:
        """
        生成价格序列（OHLC）
        
        Args:
            base_price: 基础价格
            volatility: 波动率
            length: 数据长度
            symbol: 交易符号
        
        Returns:
            Dict[str, List[float]]: OHLC价格数据
        """
        prices = {
            'open': [],
            'high': [],
            'low': [],
            'close': []
        }
        
        current_price = base_price
        trend_direction = 0  # -1: 下跌, 0: 横盘, 1: 上涨
        trend_strength = 0
        trend_duration = 0
        
        for i in range(length):
            # 趋势控制
            if trend_duration <= 0 or random.random() < 0.1:
                # 开始新趋势
                if random.random() < self.config.trend_probability:
                    trend_direction = random.choice([-1, 1])
                    trend_strength = random.uniform(0.0005, 0.002)
                    trend_duration = random.randint(50, 200)
                else:
                    trend_direction = 0
                    trend_strength = 0
                    trend_duration = random.randint(20, 100)
            
            # 应用趋势
            trend_change = trend_direction * trend_strength
            current_price *= (1 + trend_change)
            
            # 生成单根K线的OHLC
            open_price = current_price
            
            # 随机波动
            daily_volatility = volatility * random.uniform(0.5, 2.0)
            price_change = np.random.normal(0, daily_volatility)
            
            # 计算收盘价
            close_price = open_price * (1 + price_change)
            
            # 计算最高价和最低价
            high_low_range = abs(close_price - open_price) * random.uniform(1.2, 3.0)
            high_price = max(open_price, close_price) + high_low_range * random.uniform(0, 0.7)
            low_price = min(open_price, close_price) - high_low_range * random.uniform(0, 0.7)
            
            # 确保价格逻辑正确
            high_price = max(high_price, open_price, close_price)
            low_price = min(low_price, open_price, close_price)
            
            # 添加价格跳空（Gap）
            if random.random() < self.config.gap_probability:
                gap_size = random.uniform(0.01, 0.05)
                gap_direction = random.choice([-1, 1])
                gap_multiplier = 1 + (gap_size * gap_direction)
                
                open_price *= gap_multiplier
                high_price *= gap_multiplier
                low_price *= gap_multiplier
                close_price *= gap_multiplier
            
            # 添加价格尖峰（Spike）
            if random.random() < self.config.spike_probability:
                spike_size = random.uniform(0.02, 0.1)
                if random.random() < 0.5:
                    # 向上尖峰
                    high_price *= (1 + spike_size)
                else:
                    # 向下尖峰
                    low_price *= (1 - spike_size)
            
            prices['open'].append(round(open_price, 8))
            prices['high'].append(round(high_price, 8))
            prices['low'].append(round(low_price, 8))
            prices['close'].append(round(close_price, 8))
            
            current_price = close_price
            trend_duration -= 1
        
        return prices
    
    def _generate_volume_series(self, prices: Dict[str, List[float]], 
                              length: int, symbol: str) -> List[float]:
        """
        生成成交量序列
        
        Args:
            prices: 价格数据
            length: 数据长度
            symbol: 交易符号
        
        Returns:
            List[float]: 成交量数据
        """
        volumes = []
        
        # 基础成交量（根据符号调整）
        base_volume_map = {
            "BTCUSDT": 1000.0,
            "ETHUSDT": 5000.0,
            "BNBUSDT": 2000.0,
            "ADAUSDT": 10000.0,
            "SOLUSDT": 3000.0
        }
        base_volume = base_volume_map.get(symbol, 1000.0)
        
        for i in range(length):
            # 计算价格变化率
            if i > 0:
                price_change = abs(prices['close'][i] - prices['close'][i-1]) / prices['close'][i-1]
            else:
                price_change = 0
            
            # 成交量与价格变化相关
            volume_multiplier = 1 + (price_change * 10)  # 价格变化越大，成交量越大
            
            # 添加随机波动
            random_multiplier = random.uniform(0.3, 3.0)
            
            # 添加周期性模式（模拟交易时间活跃度）
            time_factor = 1 + 0.3 * np.sin(i * 0.1)  # 简单的周期性模式
            
            volume = base_volume * volume_multiplier * random_multiplier * time_factor
            volumes.append(round(volume, 2))
        
        return volumes
    
    def _add_anomalies(self, data: pd.DataFrame, symbol: str, timeframe: str) -> pd.DataFrame:
        """
        添加异常数据
        
        Args:
            data: 原始数据
            symbol: 交易符号
            timeframe: 时间框架
        
        Returns:
            pd.DataFrame: 包含异常的数据
        """
        data_copy = data.copy()
        num_anomalies = int(len(data) * self.config.anomaly_rate)
        
        if num_anomalies == 0:
            return data_copy
        
        anomaly_indices = random.sample(range(len(data)), num_anomalies)
        
        for idx in anomaly_indices:
            anomaly_type = random.choice([
                'missing_data',
                'extreme_price',
                'zero_volume',
                'negative_price',
                'invalid_ohlc',
                'timestamp_gap',
                'duplicate_timestamp'
            ])
            
            if anomaly_type == 'missing_data':
                # 缺失数据（NaN）
                data_copy.loc[idx, random.choice(['open', 'high', 'low', 'close', 'volume'])] = np.nan
            
            elif anomaly_type == 'extreme_price':
                # 极端价格
                multiplier = random.choice([0.01, 100])  # 极低或极高价格
                price_col = random.choice(['open', 'high', 'low', 'close'])
                data_copy.loc[idx, price_col] *= multiplier
            
            elif anomaly_type == 'zero_volume':
                # 零成交量
                data_copy.loc[idx, 'volume'] = 0
            
            elif anomaly_type == 'negative_price':
                # 负价格
                price_col = random.choice(['open', 'high', 'low', 'close'])
                data_copy.loc[idx, price_col] = -abs(data_copy.loc[idx, price_col])
            
            elif anomaly_type == 'invalid_ohlc':
                # 无效的OHLC关系（如low > high）
                data_copy.loc[idx, 'low'] = data_copy.loc[idx, 'high'] * 1.1
            
            elif anomaly_type == 'timestamp_gap':
                # 时间戳跳跃
                if idx < len(data) - 1:
                    gap_size = random.randint(2, 10)
                    original_timestamp = data_copy.loc[idx, 'timestamp']
                    data_copy.loc[idx, 'timestamp'] = original_timestamp + (gap_size * 60000)  # 增加分钟
            
            elif anomaly_type == 'duplicate_timestamp':
                # 重复时间戳
                if idx > 0:
                    data_copy.loc[idx, 'timestamp'] = data_copy.loc[idx-1, 'timestamp']
        
        logger.debug(f"为 {symbol} {timeframe} 添加了 {num_anomalies} 个异常数据点")
        return data_copy
    
    def _add_technical_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        添加技术指标数据（用于测试）
        
        Args:
            data: 原始数据
        
        Returns:
            pd.DataFrame: 包含技术指标的数据
        """
        data_copy = data.copy()
        
        # 简单移动平均线
        data_copy['sma_20'] = data_copy['close'].rolling(window=20).mean()
        data_copy['sma_50'] = data_copy['close'].rolling(window=50).mean()
        
        # 指数移动平均线
        data_copy['ema_12'] = data_copy['close'].ewm(span=12).mean()
        data_copy['ema_26'] = data_copy['close'].ewm(span=26).mean()
        
        # MACD
        data_copy['macd'] = data_copy['ema_12'] - data_copy['ema_26']
        data_copy['macd_signal'] = data_copy['macd'].ewm(span=9).mean()
        data_copy['macd_histogram'] = data_copy['macd'] - data_copy['macd_signal']
        
        # RSI
        delta = data_copy['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        data_copy['rsi'] = 100 - (100 / (1 + rs))
        
        # 布林带
        data_copy['bb_middle'] = data_copy['close'].rolling(window=20).mean()
        bb_std = data_copy['close'].rolling(window=20).std()
        data_copy['bb_upper'] = data_copy['bb_middle'] + (bb_std * 2)
        data_copy['bb_lower'] = data_copy['bb_middle'] - (bb_std * 2)
        
        # 成交量移动平均
        data_copy['volume_sma'] = data_copy['volume'].rolling(window=20).mean()
        
        return data_copy
    
    def _save_data_to_file(self, data: pd.DataFrame, symbol: str, timeframe: str) -> None:
        """
        保存数据到文件
        
        Args:
            data: 数据
            symbol: 交易符号
            timeframe: 时间框架
        """
        # 创建输出目录
        symbol_dir = self.output_dir / symbol.lower()
        symbol_dir.mkdir(exist_ok=True)
        
        # 保存为CSV
        csv_file = symbol_dir / f"{timeframe}.csv"
        data.to_csv(csv_file, index=False)
        
        # 保存为JSON（用于API测试）
        json_file = symbol_dir / f"{timeframe}.json"
        data_dict = data.to_dict('records')
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(data_dict, f, indent=2, ensure_ascii=False)
        
        # 保存为Parquet（高效存储）
        parquet_file = symbol_dir / f"{timeframe}.parquet"
        data.to_parquet(parquet_file, index=False)
        
        logger.info(f"数据已保存: {symbol} {timeframe} ({len(data)} 条记录)")
    
    def generate_edge_cases(self) -> Dict[str, pd.DataFrame]:
        """
        生成边界情况测试数据
        
        Returns:
            Dict[str, pd.DataFrame]: 边界情况数据集
        """
        edge_cases = {}
        
        # 空数据集
        edge_cases['empty_dataset'] = pd.DataFrame(columns=[
            'timestamp', 'open', 'high', 'low', 'close', 'volume', 'symbol', 'timeframe'
        ])
        
        # 单条记录
        edge_cases['single_record'] = pd.DataFrame([{
            'timestamp': int(datetime.now().timestamp() * 1000),
            'open': 50000.0,
            'high': 50100.0,
            'low': 49900.0,
            'close': 50050.0,
            'volume': 1000.0,
            'symbol': 'BTCUSDT',
            'timeframe': '1m'
        }])
        
        # 全部相同价格
        same_price_data = []
        base_time = int(datetime.now().timestamp() * 1000)
        for i in range(100):
            same_price_data.append({
                'timestamp': base_time + (i * 60000),
                'open': 50000.0,
                'high': 50000.0,
                'low': 50000.0,
                'close': 50000.0,
                'volume': 1000.0,
                'symbol': 'BTCUSDT',
                'timeframe': '1m'
            })
        edge_cases['same_price'] = pd.DataFrame(same_price_data)
        
        # 极端波动
        extreme_volatility_data = []
        current_price = 50000.0
        for i in range(100):
            # 极端价格变化
            change = random.uniform(-0.5, 0.5)  # ±50%变化
            new_price = current_price * (1 + change)
            
            extreme_volatility_data.append({
                'timestamp': base_time + (i * 60000),
                'open': current_price,
                'high': max(current_price, new_price) * 1.1,
                'low': min(current_price, new_price) * 0.9,
                'close': new_price,
                'volume': random.uniform(100, 10000),
                'symbol': 'BTCUSDT',
                'timeframe': '1m'
            })
            current_price = new_price
        
        edge_cases['extreme_volatility'] = pd.DataFrame(extreme_volatility_data)
        
        # 保存边界情况数据
        edge_cases_dir = self.output_dir / "edge_cases"
        edge_cases_dir.mkdir(exist_ok=True)
        
        for case_name, case_data in edge_cases.items():
            case_file = edge_cases_dir / f"{case_name}.csv"
            case_data.to_csv(case_file, index=False)
            
            case_json = edge_cases_dir / f"{case_name}.json"
            with open(case_json, 'w', encoding='utf-8') as f:
                json.dump(case_data.to_dict('records'), f, indent=2, ensure_ascii=False)
        
        logger.info(f"边界情况数据已生成: {len(edge_cases)} 个案例")
        return edge_cases
    
    def generate_performance_test_data(self, size_mb: int = 100) -> pd.DataFrame:
        """
        生成性能测试数据
        
        Args:
            size_mb: 目标数据大小（MB）
        
        Returns:
            pd.DataFrame: 性能测试数据
        """
        # 估算需要的记录数（每条记录约100字节）
        target_records = (size_mb * 1024 * 1024) // 100
        
        logger.info(f"生成性能测试数据: 目标 {size_mb}MB, 约 {target_records} 条记录")
        
        # 生成大量数据
        config = GeneratorConfig(
            symbols=["BTCUSDT"],
            timeframes=["1m"],
            data_points_per_timeframe=target_records,
            start_date="2020-01-01",
            end_date="2024-01-01",
            include_anomalies=False,
            anomaly_rate=0,
            volatility_base=0.02,
            trend_probability=0.3,
            gap_probability=0,
            spike_probability=0
        )
        
        # 临时更改配置
        original_config = self.config
        self.config = config
        
        try:
            data = self._generate_symbol_timeframe_data("BTCUSDT", "1m")
            
            # 保存性能测试数据
            perf_dir = self.output_dir / "performance"
            perf_dir.mkdir(exist_ok=True)
            
            perf_file = perf_dir / f"large_dataset_{size_mb}mb.csv"
            data.to_csv(perf_file, index=False)
            
            actual_size = os.path.getsize(perf_file) / (1024 * 1024)
            logger.info(f"性能测试数据已生成: {len(data)} 条记录, {actual_size:.2f}MB")
            
            return data
        
        finally:
            # 恢复原始配置
            self.config = original_config
    
    def validate_generated_data(self, data: pd.DataFrame) -> Dict[str, Any]:
        """
        验证生成的数据质量
        
        Args:
            data: 待验证的数据
        
        Returns:
            Dict[str, Any]: 验证结果
        """
        validation_result = {
            'total_records': len(data),
            'missing_values': data.isnull().sum().to_dict(),
            'data_types': data.dtypes.to_dict(),
            'price_validation': {},
            'volume_validation': {},
            'timestamp_validation': {},
            'anomalies_detected': []
        }
        
        if len(data) == 0:
            validation_result['status'] = 'empty_dataset'
            return validation_result
        
        # 价格验证
        price_cols = ['open', 'high', 'low', 'close']
        for col in price_cols:
            if col in data.columns:
                validation_result['price_validation'][col] = {
                    'min': float(data[col].min()),
                    'max': float(data[col].max()),
                    'mean': float(data[col].mean()),
                    'negative_count': int((data[col] < 0).sum()),
                    'zero_count': int((data[col] == 0).sum())
                }
        
        # OHLC逻辑验证
        if all(col in data.columns for col in price_cols):
            invalid_ohlc = (
                (data['low'] > data['high']) |
                (data['open'] > data['high']) |
                (data['open'] < data['low']) |
                (data['close'] > data['high']) |
                (data['close'] < data['low'])
            )
            validation_result['price_validation']['invalid_ohlc_count'] = int(invalid_ohlc.sum())
        
        # 成交量验证
        if 'volume' in data.columns:
            validation_result['volume_validation'] = {
                'min': float(data['volume'].min()),
                'max': float(data['volume'].max()),
                'mean': float(data['volume'].mean()),
                'negative_count': int((data['volume'] < 0).sum()),
                'zero_count': int((data['volume'] == 0).sum())
            }
        
        # 时间戳验证
        if 'timestamp' in data.columns:
            timestamps = data['timestamp'].dropna()
            if len(timestamps) > 1:
                time_diffs = timestamps.diff().dropna()
                validation_result['timestamp_validation'] = {
                    'min_interval': int(time_diffs.min()),
                    'max_interval': int(time_diffs.max()),
                    'mean_interval': float(time_diffs.mean()),
                    'duplicate_count': int(data['timestamp'].duplicated().sum()),
                    'chronological_order': bool(timestamps.is_monotonic_increasing)
                }
        
        # 异常检测
        for col in ['open', 'high', 'low', 'close', 'volume']:
            if col in data.columns:
                # 检测极端值（超过3个标准差）
                mean_val = data[col].mean()
                std_val = data[col].std()
                extreme_values = abs(data[col] - mean_val) > (3 * std_val)
                if extreme_values.sum() > 0:
                    validation_result['anomalies_detected'].append({
                        'column': col,
                        'type': 'extreme_values',
                        'count': int(extreme_values.sum())
                    })
        
        validation_result['status'] = 'validated'
        return validation_result


def create_test_data_generator(config_dict: Optional[Dict[str, Any]] = None) -> TestDataGenerator:
    """
    创建测试数据生成器实例
    
    Args:
        config_dict: 配置字典
    
    Returns:
        TestDataGenerator: 生成器实例
    """
    if config_dict:
        config = GeneratorConfig(**config_dict)
    else:
        config = None
    
    return TestDataGenerator(config)


if __name__ == "__main__":
    # 示例用法
    generator = TestDataGenerator()
    
    # 生成所有数据
    all_data = generator.generate_all_data()
    
    # 生成边界情况数据
    edge_cases = generator.generate_edge_cases()
    
    # 生成性能测试数据
    perf_data = generator.generate_performance_test_data(size_mb=50)
    
    # 验证数据质量
    for symbol in all_data:
        for timeframe in all_data[symbol]:
            data = all_data[symbol][timeframe]
            validation = generator.validate_generated_data(data)
            logger.info(f"{symbol} {timeframe} 验证结果: {validation['status']}")
    
    logger.info("测试数据生成完成")