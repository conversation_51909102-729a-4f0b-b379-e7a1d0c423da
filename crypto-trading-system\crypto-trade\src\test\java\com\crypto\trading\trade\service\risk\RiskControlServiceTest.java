package com.crypto.trading.trade.service.risk;

import com.crypto.trading.common.model.signal.TradeSignal;
import com.crypto.trading.trade.model.entity.order.OrderEntity;
import com.crypto.trading.trade.model.entity.risk.RiskControlLogEntity;
import com.crypto.trading.trade.repository.mapper.order.OrderMapper;
import com.crypto.trading.trade.repository.mapper.risk.RiskControlLogMapper;
import com.crypto.trading.trade.service.risk.impl.RiskControlServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 风险控制服务测试类
 * 
 * <p>测试风险控制服务的各个方法</p>
 */
public class RiskControlServiceTest {

    @Mock
    private OrderMapper orderMapper;

    @Mock
    private RiskControlLogMapper riskControlLogMapper;

    @InjectMocks
    private RiskControlServiceImpl riskControlService;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.openMocks(this);
        
        // 设置属性值
        ReflectionTestUtils.setField(riskControlService, "maxTradeAmount", new BigDecimal("10000"));
        ReflectionTestUtils.setField(riskControlService, "maxTradeFrequency", 10);
        ReflectionTestUtils.setField(riskControlService, "frequencyWindowMs", 60000L);
        ReflectionTestUtils.setField(riskControlService, "riskControlEnabled", true);
    }

    /**
     * 测试检查交易信号
     */
    @Test
    public void testCheckSignal() {
        // 准备测试数据
        TradeSignal signal = new TradeSignal.Builder()
                .signalId("test-signal-001")
                .symbol("BTCUSDT")
                .quantity(new BigDecimal("1.0"))
                .price(new BigDecimal("5000.0")) // 价格从50000.0改为5000.0，使交易金额不超过限制
                .build();
        
        // 设置模拟对象行为
        when(riskControlLogMapper.insert(any(RiskControlLogEntity.class))).thenReturn(1);
        
        // 调用被测试方法
        boolean result = riskControlService.checkSignal(signal);
        
        // 验证结果
        assertTrue(result, "交易信号应该通过风控检查");
        
        // 验证模拟对象的交互
        verify(riskControlLogMapper, times(1)).insert(any(RiskControlLogEntity.class));
    }

    /**
     * 测试检查交易信号 - 交易金额超过限制
     */
    @Test
    public void testCheckSignalAmountExceedsLimit() {
        // 准备测试数据
        TradeSignal signal = new TradeSignal.Builder()
                .signalId("test-signal-002")
                .symbol("BTCUSDT")
                .quantity(new BigDecimal("1.0"))
                .price(new BigDecimal("15000.0"))
                .build();
        
        // 设置模拟对象行为
        when(riskControlLogMapper.insert(any(RiskControlLogEntity.class))).thenReturn(1);
        
        // 调用被测试方法
        boolean result = riskControlService.checkSignal(signal);
        
        // 验证结果
        assertFalse(result, "交易信号应该因为金额超过限制而被拒绝");
        
        // 验证模拟对象的交互
        verify(riskControlLogMapper, times(1)).insert(any(RiskControlLogEntity.class));
    }

    /**
     * 测试检查交易信号 - 风控禁用
     */
    @Test
    public void testCheckSignalRiskControlDisabled() {
        // 准备测试数据
        TradeSignal signal = new TradeSignal.Builder()
                .signalId("test-signal-003")
                .symbol("BTCUSDT")
                .quantity(new BigDecimal("1.0"))
                .price(new BigDecimal("15000.0"))
                .build();
        
        // 禁用风控
        ReflectionTestUtils.setField(riskControlService, "riskControlEnabled", false);
        
        // 调用被测试方法
        boolean result = riskControlService.checkSignal(signal);
        
        // 验证结果
        assertTrue(result, "风控禁用时，交易信号应该通过风控检查");
        
        // 验证模拟对象的交互
        verify(riskControlLogMapper, never()).insert(any(RiskControlLogEntity.class));
        
        // 恢复风控设置
        ReflectionTestUtils.setField(riskControlService, "riskControlEnabled", true);
    }

    /**
     * 测试检查订单
     */
    @Test
    public void testCheckOrder() {
        // 准备测试数据
        OrderEntity order = new OrderEntity();
        order.setOrderId("test-order-001");
        order.setStrategyId("test-strategy-001");
        order.setSymbol("BTCUSDT");
        order.setQuantity(1.0);
        order.setPrice(5000.0);
        
        // 设置模拟对象行为
        when(riskControlLogMapper.insert(any(RiskControlLogEntity.class))).thenReturn(1);
        
        // 调用被测试方法
        boolean result = riskControlService.checkOrder(order);
        
        // 验证结果
        assertTrue(result, "订单应该通过风控检查");
        
        // 验证模拟对象的交互
        verify(riskControlLogMapper, times(1)).insert(any(RiskControlLogEntity.class));
    }

    /**
     * 测试检查订单 - 交易金额超过限制
     */
    @Test
    public void testCheckOrderAmountExceedsLimit() {
        // 准备测试数据
        OrderEntity order = new OrderEntity();
        order.setOrderId("test-order-002");
        order.setStrategyId("test-strategy-002");
        order.setSymbol("BTCUSDT");
        order.setQuantity(1.0);
        order.setPrice(15000.0);
        
        // 设置模拟对象行为
        when(riskControlLogMapper.insert(any(RiskControlLogEntity.class))).thenReturn(1);
        
        // 调用被测试方法
        boolean result = riskControlService.checkOrder(order);
        
        // 验证结果
        assertFalse(result, "订单应该因为金额超过限制而被拒绝");
        
        // 验证模拟对象的交互
        verify(riskControlLogMapper, times(1)).insert(any(RiskControlLogEntity.class));
    }

    /**
     * 测试检查账户风险
     */
    @Test
    public void testCheckAccountRisk() {
        // 调用被测试方法
        boolean result = riskControlService.checkAccountRisk("test-account-001");
        
        // 验证结果
        assertTrue(result, "账户风险检查应该通过");
    }

    /**
     * 测试检查交易频率
     */
    @Test
    public void testCheckTradeFrequency() {
        // 准备测试数据
        String symbol = "BTCUSDT";
        long timeWindowMs = 60000L;
        
        // 调用被测试方法多次
        boolean result1 = riskControlService.checkTradeFrequency(symbol, timeWindowMs);
        boolean result2 = riskControlService.checkTradeFrequency(symbol, timeWindowMs);
        boolean result3 = riskControlService.checkTradeFrequency(symbol, timeWindowMs);
        
        // 验证结果
        assertTrue(result1, "第一次交易频率检查应该通过");
        assertTrue(result2, "第二次交易频率检查应该通过");
        assertTrue(result3, "第三次交易频率检查应该通过");
        
        // 模拟超过频率限制
        // 重置计数器
        Map<String, Map<Long, AtomicInteger>> tradeFrequencyCounter = new ConcurrentHashMap<>();
        Map<Long, AtomicInteger> windowCounters = new ConcurrentHashMap<>();
        long currentTimeWindow = System.currentTimeMillis() / timeWindowMs;
        windowCounters.put(currentTimeWindow, new AtomicInteger(10)); // 已经有10次交易
        tradeFrequencyCounter.put(symbol, windowCounters);
        ReflectionTestUtils.setField(riskControlService, "tradeFrequencyCounter", tradeFrequencyCounter);
        
        // 调用被测试方法
        boolean result4 = riskControlService.checkTradeFrequency(symbol, timeWindowMs);
        
        // 验证结果
        assertFalse(result4, "超过频率限制的交易应该被拒绝");
    }

    /**
     * 测试检查交易金额风险
     */
    @Test
    public void testCheckAmountRisk() {
        // 准备测试数据
        String symbol = "BTCUSDT";
        BigDecimal amount1 = new BigDecimal("5000.0");
        BigDecimal amount2 = new BigDecimal("15000.0");
        
        // 调用被测试方法
        boolean result1 = riskControlService.checkAmountRisk(symbol, amount1);
        boolean result2 = riskControlService.checkAmountRisk(symbol, amount2);
        
        // 验证结果
        assertTrue(result1, "金额在限制范围内的交易应该通过");
        assertFalse(result2, "金额超过限制的交易应该被拒绝");
    }

    /**
     * 测试记录风控日志
     */
    @Test
    public void testLogRiskCheck() {
        // 准备测试数据
        String signalId = "test-signal-001";
        String orderId = "test-order-001";
        String ruleId = "AMOUNT";
        String ruleName = "交易金额限制";
        String riskType = "AMOUNT";
        String riskLevel = "HIGH";
        boolean triggered = true;
        String actionTaken = "拒绝交易";
        String details = "交易金额超过限制";
        
        // 设置模拟对象行为
        when(riskControlLogMapper.insert(any(RiskControlLogEntity.class))).thenReturn(1);
        
        // 调用被测试方法
        RiskControlLogEntity result = riskControlService.logRiskCheck(
                signalId, orderId, ruleId, ruleName, riskType, riskLevel, triggered, actionTaken, details);
        
        // 验证结果
        assertNotNull(result, "风控日志应该不为空");
        assertEquals(signalId, result.getSignalId(), "信号ID应该匹配");
        assertEquals(orderId, result.getOrderId(), "订单ID应该匹配");
        assertEquals(ruleId, result.getRuleId(), "规则ID应该匹配");
        assertEquals(ruleName, result.getRuleName(), "规则名称应该匹配");
        assertEquals(riskType, result.getRiskType(), "风险类型应该匹配");
        assertEquals(riskLevel, result.getRiskLevel(), "风险等级应该匹配");
        assertEquals(triggered, result.getTriggered(), "触发标志应该匹配");
        assertEquals(actionTaken, result.getActionTaken(), "采取的行动应该匹配");
        assertEquals(details, result.getDetails(), "详细信息应该匹配");
        
        // 验证模拟对象的交互
        verify(riskControlLogMapper, times(1)).insert(any(RiskControlLogEntity.class));
    }

    /**
     * 测试获取风控日志
     */
    @Test
    public void testGetRiskLogs() {
        // 准备测试数据
        String signalId = "test-signal-001";
        List<RiskControlLogEntity> expectedLogs = new ArrayList<>();
        RiskControlLogEntity log1 = new RiskControlLogEntity();
        log1.setSignalId(signalId);
        log1.setRuleId("AMOUNT");
        expectedLogs.add(log1);
        
        // 设置模拟对象行为
        when(riskControlLogMapper.findBySignalId(signalId)).thenReturn(expectedLogs);
        
        // 调用被测试方法
        List<RiskControlLogEntity> result = riskControlService.getRiskLogs(signalId);
        
        // 验证结果
        assertNotNull(result, "风控日志列表应该不为空");
        assertEquals(1, result.size(), "风控日志列表应该包含1个元素");
        assertEquals(signalId, result.get(0).getSignalId(), "信号ID应该匹配");
        
        // 验证模拟对象的交互
        verify(riskControlLogMapper, times(1)).findBySignalId(signalId);
    }

    /**
     * 测试获取规则触发统计
     */
    @Test
    public void testGetRuleTriggeredStats() {
        // 准备测试数据
        long startTime = System.currentTimeMillis() - 3600000; // 1小时前
        long endTime = System.currentTimeMillis();
        
        List<RiskControlLogEntity> logs = new ArrayList<>();
        
        RiskControlLogEntity log1 = new RiskControlLogEntity();
        log1.setRuleId("AMOUNT");
        log1.setTriggered(true);
        logs.add(log1);
        
        RiskControlLogEntity log2 = new RiskControlLogEntity();
        log2.setRuleId("FREQUENCY");
        log2.setTriggered(true);
        logs.add(log2);
        
        RiskControlLogEntity log3 = new RiskControlLogEntity();
        log3.setRuleId("AMOUNT");
        log3.setTriggered(true);
        logs.add(log3);
        
        RiskControlLogEntity log4 = new RiskControlLogEntity();
        log4.setRuleId("AMOUNT");
        log4.setTriggered(false); // 未触发，不应计入统计
        logs.add(log4);
        
        // 设置模拟对象行为
        when(riskControlLogMapper.findByTimeRange(startTime, endTime, 1000)).thenReturn(logs);
        
        // 调用被测试方法
        Map<String, Integer> result = riskControlService.getRuleTriggeredStats(startTime, endTime);
        
        // 验证结果
        assertNotNull(result, "规则触发统计应该不为空");
        assertEquals(2, result.size(), "规则触发统计应该包含2个规则");
        assertEquals(2, result.get("AMOUNT"), "AMOUNT规则应该触发2次");
        assertEquals(1, result.get("FREQUENCY"), "FREQUENCY规则应该触发1次");
        
        // 验证模拟对象的交互
        verify(riskControlLogMapper, times(1)).findByTimeRange(startTime, endTime, 1000);
    }
}