package com.crypto.trading.market.repository;

import com.crypto.trading.common.dto.market.KlineDataDTO;
import com.crypto.trading.common.dto.market.DepthDataDTO;
import com.crypto.trading.common.dto.market.TradeDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.ZoneOffset;
import java.util.List;
import java.util.concurrent.ExecutorService;

/**
 * MySQL市场数据存储库实现
 * 负责将市场数据存储到MySQL数据库中
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Repository
public class MySQLMarketDataRepository implements MarketDataRepository {

    private static final Logger log = LoggerFactory.getLogger(MySQLMarketDataRepository.class);

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private ExecutorService virtualThreadExecutor;

    /**
     * 保存K线数据到MySQL数据库
     *
     * @param klineDataDTO K线数据DTO
     * @return 是否保存成功
     */
    @Override
    public boolean saveKlineData(KlineDataDTO klineDataDTO) {
        try {
            // 使用虚拟线程异步处理
            virtualThreadExecutor.submit(() -> {
                try {
                    // 将LocalDateTime转换为毫秒时间戳
                    long openTime = klineDataDTO.getOpenTime().toInstant(ZoneOffset.UTC).toEpochMilli();
                    long closeTime = klineDataDTO.getCloseTime().toInstant(ZoneOffset.UTC).toEpochMilli();
                    long currentTime = System.currentTimeMillis();

                    // 插入K线数据
                    String sql = "INSERT INTO t_kline_data (symbol, `interval`, open_time, close_time, open, high, low, close, " +
                            "volume, quote_asset_volume, number_of_trades, taker_buy_base_asset_volume, " +
                            "taker_buy_quote_asset_volume, is_closed, created_time) " +
                            "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) " +
                            "ON DUPLICATE KEY UPDATE " +
                            "close_time = VALUES(close_time), " +
                            "open = VALUES(open), " +
                            "high = VALUES(high), " +
                            "low = VALUES(low), " +
                            "close = VALUES(close), " +
                            "volume = VALUES(volume), " +
                            "quote_asset_volume = VALUES(quote_asset_volume), " +
                            "number_of_trades = VALUES(number_of_trades), " +
                            "taker_buy_base_asset_volume = VALUES(taker_buy_base_asset_volume), " +
                            "taker_buy_quote_asset_volume = VALUES(taker_buy_quote_asset_volume), " +
                            "is_closed = VALUES(is_closed)";

                    jdbcTemplate.update(sql,
                            klineDataDTO.getSymbol(),
                            klineDataDTO.getInterval(),
                            openTime,
                            closeTime,
                            klineDataDTO.getOpen(),
                            klineDataDTO.getHigh(),
                            klineDataDTO.getLow(),
                            klineDataDTO.getClose(),
                            klineDataDTO.getVolume(),
                            klineDataDTO.getQuoteAssetVolume(),
                            klineDataDTO.getNumberOfTrades(),
                            klineDataDTO.getTakerBuyBaseAssetVolume(),
                            klineDataDTO.getTakerBuyQuoteAssetVolume(),
                            klineDataDTO.isClosed() ? 1 : 0,
                            currentTime);

                    log.debug("已保存K线数据到MySQL: symbol={}, interval={}, openTime={}",
                            klineDataDTO.getSymbol(), klineDataDTO.getInterval(), openTime);
                } catch (Exception e) {
                    log.error("保存K线数据到MySQL异常: symbol={}, interval={}, error={}",
                            klineDataDTO.getSymbol(), klineDataDTO.getInterval(), e.getMessage(), e);
                }
            });
            return true;
        } catch (Exception e) {
            log.error("提交K线数据保存任务异常: symbol={}, interval={}, error={}",
                    klineDataDTO.getSymbol(), klineDataDTO.getInterval(), e.getMessage(), e);
            return false;
        }
    }

    /**
     * 保存深度数据到MySQL数据库
     *
     * @param depthDataDTO 深度数据DTO
     * @return 是否保存成功
     */
    @Override
    public boolean saveDepthData(DepthDataDTO depthDataDTO) {
        try {
            // 使用虚拟线程异步处理
            virtualThreadExecutor.submit(() -> {
                try {
                    // 将LocalDateTime转换为毫秒时间戳
                    long updateTime = depthDataDTO.getUpdateTime().toInstant(ZoneOffset.UTC).toEpochMilli();
                    long currentTime = System.currentTimeMillis();

                    // 插入深度数据
                    String sql = "INSERT INTO t_depth_data (symbol, last_update_id, depth_limit, update_time, created_time) " +
                            "VALUES (?, ?, ?, ?, ?) " +
                            "ON DUPLICATE KEY UPDATE " +
                            "depth_limit = VALUES(depth_limit), " +
                            "update_time = VALUES(update_time)";

                    jdbcTemplate.update(sql,
                            depthDataDTO.getSymbol(),
                            depthDataDTO.getLastUpdateId(),
                            depthDataDTO.getLimit(),
                            updateTime,
                            currentTime);

                    // 获取刚插入的深度数据ID
                    String queryIdSql = "SELECT id FROM t_depth_data WHERE symbol = ? AND last_update_id = ?";
                    Long depthId = jdbcTemplate.queryForObject(queryIdSql, Long.class,
                            depthDataDTO.getSymbol(), depthDataDTO.getLastUpdateId());

                    if (depthId != null) {
                        // 插入买单数据
                        insertDepthPriceQuantity(depthId, depthDataDTO.getBids(), "BID", currentTime);
                        // 插入卖单数据
                        insertDepthPriceQuantity(depthId, depthDataDTO.getAsks(), "ASK", currentTime);
                    }

                    log.debug("已保存深度数据到MySQL: symbol={}, lastUpdateId={}",
                            depthDataDTO.getSymbol(), depthDataDTO.getLastUpdateId());
                } catch (Exception e) {
                    log.error("保存深度数据到MySQL异常: symbol={}, error={}",
                            depthDataDTO.getSymbol(), e.getMessage(), e);
                }
            });
            return true;
        } catch (Exception e) {
            log.error("提交深度数据保存任务异常: symbol={}, error={}",
                    depthDataDTO.getSymbol(), e.getMessage(), e);
            return false;
        }
    }

    /**
     * 插入深度价格数量数据
     *
     * @param depthId     深度数据ID
     * @param priceQuantities 价格数量列表
     * @param side        买卖方向
     * @param currentTime 当前时间
     */
    private void insertDepthPriceQuantity(Long depthId, List<DepthDataDTO.PriceQuantity> priceQuantities, String side, long currentTime) {
        if (priceQuantities == null || priceQuantities.isEmpty()) {
            return;
        }

        // 批量插入价格数量数据
        String sql = "INSERT INTO t_depth_price_quantity (depth_id, price, quantity, side, created_time) " +
                "VALUES (?, ?, ?, ?, ?)";

        jdbcTemplate.batchUpdate(sql, priceQuantities, priceQuantities.size(),
                (ps, priceQuantity) -> {
                    ps.setLong(1, depthId);
                    ps.setBigDecimal(2, priceQuantity.getPrice());
                    ps.setBigDecimal(3, priceQuantity.getQuantity());
                    ps.setString(4, side);
                    ps.setLong(5, currentTime);
                });
    }

    /**
     * 保存交易数据到MySQL数据库
     *
     * @param tradeDTO 交易数据DTO
     * @return 是否保存成功
     */
    @Override
    public boolean saveTradeData(TradeDTO tradeDTO) {
        try {
            // 使用虚拟线程异步处理
            virtualThreadExecutor.submit(() -> {
                try {
                    // 将LocalDateTime转换为毫秒时间戳
                    long tradeTime = tradeDTO.getTime().toInstant(ZoneOffset.UTC).toEpochMilli();
                    long currentTime = System.currentTimeMillis();

                    // 插入交易数据
                    String sql = "INSERT INTO t_trade_data (trade_id, symbol, price, quantity, quote_quantity, " +
                            "trade_time, is_buyer_maker, is_best_match, created_time) " +
                            "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?) " +
                            "ON DUPLICATE KEY UPDATE " +
                            "price = VALUES(price), " +
                            "quantity = VALUES(quantity), " +
                            "quote_quantity = VALUES(quote_quantity), " +
                            "trade_time = VALUES(trade_time), " +
                            "is_buyer_maker = VALUES(is_buyer_maker), " +
                            "is_best_match = VALUES(is_best_match)";

                    jdbcTemplate.update(sql,
                            tradeDTO.getId(),
                            tradeDTO.getSymbol(),
                            tradeDTO.getPrice(),
                            tradeDTO.getQuantity(),
                            tradeDTO.getQuoteQuantity(),
                            tradeTime,
                            tradeDTO.isBuyerMaker() ? 1 : 0,
                            tradeDTO.isBestMatch() ? 1 : 0,
                            currentTime);

                    log.debug("已保存交易数据到MySQL: symbol={}, tradeId={}",
                            tradeDTO.getSymbol(), tradeDTO.getId());
                } catch (Exception e) {
                    log.error("保存交易数据到MySQL异常: symbol={}, tradeId={}, error={}",
                            tradeDTO.getSymbol(), tradeDTO.getId(), e.getMessage(), e);
                }
            });
            return true;
        } catch (Exception e) {
            log.error("提交交易数据保存任务异常: symbol={}, tradeId={}, error={}",
                    tradeDTO.getSymbol(), tradeDTO.getId(), e.getMessage(), e);
            return false;
        }
    }

    /**
     * 查询K线数据数量
     *
     * @param symbol 交易对符号
     * @param interval 时间间隔
     * @return K线数据数量
     */
    @Override
    public long findKlineDataCount(String symbol, String interval) {
        try {
            String sql = "SELECT COUNT(*) FROM t_kline_data WHERE symbol = ? AND `interval` = ?";
            Long count = jdbcTemplate.queryForObject(sql, Long.class, symbol, interval);
            return count != null ? count : 0L;
        } catch (Exception e) {
            log.error("查询K线数据数量异常: symbol={}, interval={}, error={}",
                    symbol, interval, e.getMessage(), e);
            return 0L;
        }
    }
}