package com.crypto.trading.trade.service.trade;

import com.crypto.trading.common.exception.ApiException;
import com.crypto.trading.common.model.signal.TradeSignal;
import com.crypto.trading.trade.model.entity.order.OrderEntity;

/**
 * 交易执行服务接口
 * 
 * <p>负责将交易信号转化为实际的交易订单，调用交易所API执行交易</p>
 */
public interface TradeExecutionService {

    /**
     * 处理交易信号并执行交易
     *
     * @param signal 交易信号
     * @return 创建的订单实体
     * @throws ApiException 如果API调用失败
     */
    OrderEntity processTradeSignal(TradeSignal signal) throws ApiException;

    /**
     * 创建市价单
     *
     * @param symbol 交易对
     * @param side 交易方向 BUY/SELL
     * @param positionSide 仓位方向 BOTH/LONG/SHORT
     * @param quantity 交易数量
     * @param clientOrderId 客户端订单ID
     * @param signalId 关联的交易信号ID
     * @return 创建的订单实体
     * @throws ApiException 如果API调用失败
     */
    OrderEntity createMarketOrder(
            String symbol, 
            String side, 
            String positionSide, 
            java.math.BigDecimal quantity, 
            String clientOrderId, 
            String signalId) throws ApiException;

    /**
     * 创建限价单
     *
     * @param symbol 交易对
     * @param side 交易方向 BUY/SELL
     * @param positionSide 仓位方向 BOTH/LONG/SHORT
     * @param quantity 交易数量
     * @param price 交易价格
     * @param timeInForce 订单有效期 GTC/IOC/FOK
     * @param clientOrderId 客户端订单ID
     * @param signalId 关联的交易信号ID
     * @return 创建的订单实体
     * @throws ApiException 如果API调用失败
     */
    OrderEntity createLimitOrder(
            String symbol, 
            String side, 
            String positionSide, 
            java.math.BigDecimal quantity, 
            java.math.BigDecimal price, 
            String timeInForce, 
            String clientOrderId, 
            String signalId) throws ApiException;

    /**
     * 创建止损单
     *
     * @param symbol 交易对
     * @param side 交易方向 BUY/SELL
     * @param positionSide 仓位方向 BOTH/LONG/SHORT
     * @param quantity 交易数量
     * @param stopPrice 触发价格
     * @param price 执行价格
     * @param clientOrderId 客户端订单ID
     * @param signalId 关联的交易信号ID
     * @return 创建的订单实体
     * @throws ApiException 如果API调用失败
     */
    OrderEntity createStopOrder(
            String symbol, 
            String side, 
            String positionSide, 
            java.math.BigDecimal quantity, 
            java.math.BigDecimal stopPrice, 
            java.math.BigDecimal price, 
            String clientOrderId, 
            String signalId) throws ApiException;

    /**
     * 取消订单
     *
     * @param symbol 交易对
     * @param orderId 订单ID
     * @return 取消结果
     * @throws ApiException 如果API调用失败
     */
    boolean cancelOrder(String symbol, String orderId) throws ApiException;

    /**
     * 取消所有订单
     *
     * @param symbol 交易对
     * @return 取消结果
     * @throws ApiException 如果API调用失败
     */
    boolean cancelAllOrders(String symbol) throws ApiException;

    /**
     * 查询订单状态
     *
     * @param symbol 交易对
     * @param orderId 订单ID
     * @return 订单实体
     * @throws ApiException 如果API调用失败
     */
    OrderEntity getOrderStatus(String symbol, String orderId) throws ApiException;

    /**
     * 查询订单状态（根据客户端订单ID）
     *
     * @param symbol 交易对
     * @param clientOrderId 客户端订单ID
     * @return 订单实体
     * @throws ApiException 如果API调用失败
     */
    OrderEntity getOrderStatusByClientOrderId(String symbol, String clientOrderId) throws ApiException;
}