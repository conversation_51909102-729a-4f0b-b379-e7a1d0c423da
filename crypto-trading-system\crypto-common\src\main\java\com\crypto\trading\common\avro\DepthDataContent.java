/**
 * Autogenerated by Avro
 *
 * DO NOT EDIT DIRECTLY
 */
package com.crypto.trading.common.avro;

import org.apache.avro.generic.GenericArray;
import org.apache.avro.specific.SpecificData;
import org.apache.avro.util.Utf8;
import org.apache.avro.message.BinaryMessageEncoder;
import org.apache.avro.message.BinaryMessageDecoder;
import org.apache.avro.message.SchemaStore;

@org.apache.avro.specific.AvroGenerated
public class DepthDataContent extends org.apache.avro.specific.SpecificRecordBase implements org.apache.avro.specific.SpecificRecord {
  private static final long serialVersionUID = -8595010291489745269L;


  public static final org.apache.avro.Schema SCHEMA$ = new org.apache.avro.Schema.Parser().parse("{\"type\":\"record\",\"name\":\"DepthDataContent\",\"namespace\":\"com.crypto.trading.common.avro\",\"fields\":[{\"name\":\"symbol\",\"type\":{\"type\":\"string\",\"avro.java.string\":\"String\"},\"doc\":\"交易对符号\"},{\"name\":\"lastUpdateId\",\"type\":\"long\",\"doc\":\"最后更新ID\"},{\"name\":\"eventTime\",\"type\":\"long\",\"doc\":\"事件时间(毫秒)\"},{\"name\":\"bids\",\"type\":{\"type\":\"array\",\"items\":{\"type\":\"record\",\"name\":\"DepthLevel\",\"fields\":[{\"name\":\"price\",\"type\":\"double\",\"doc\":\"价格\"},{\"name\":\"quantity\",\"type\":\"double\",\"doc\":\"数量\"}]}},\"doc\":\"买单列表\"},{\"name\":\"asks\",\"type\":{\"type\":\"array\",\"items\":\"DepthLevel\"},\"doc\":\"卖单列表\"}]}");
  public static org.apache.avro.Schema getClassSchema() { return SCHEMA$; }

  private static final SpecificData MODEL$ = new SpecificData();

  private static final BinaryMessageEncoder<DepthDataContent> ENCODER =
      new BinaryMessageEncoder<>(MODEL$, SCHEMA$);

  private static final BinaryMessageDecoder<DepthDataContent> DECODER =
      new BinaryMessageDecoder<>(MODEL$, SCHEMA$);

  /**
   * Return the BinaryMessageEncoder instance used by this class.
   * @return the message encoder used by this class
   */
  public static BinaryMessageEncoder<DepthDataContent> getEncoder() {
    return ENCODER;
  }

  /**
   * Return the BinaryMessageDecoder instance used by this class.
   * @return the message decoder used by this class
   */
  public static BinaryMessageDecoder<DepthDataContent> getDecoder() {
    return DECODER;
  }

  /**
   * Create a new BinaryMessageDecoder instance for this class that uses the specified {@link SchemaStore}.
   * @param resolver a {@link SchemaStore} used to find schemas by fingerprint
   * @return a BinaryMessageDecoder instance for this class backed by the given SchemaStore
   */
  public static BinaryMessageDecoder<DepthDataContent> createDecoder(SchemaStore resolver) {
    return new BinaryMessageDecoder<>(MODEL$, SCHEMA$, resolver);
  }

  /**
   * Serializes this DepthDataContent to a ByteBuffer.
   * @return a buffer holding the serialized data for this instance
   * @throws java.io.IOException if this instance could not be serialized
   */
  public java.nio.ByteBuffer toByteBuffer() throws java.io.IOException {
    return ENCODER.encode(this);
  }

  /**
   * Deserializes a DepthDataContent from a ByteBuffer.
   * @param b a byte buffer holding serialized data for an instance of this class
   * @return a DepthDataContent instance decoded from the given buffer
   * @throws java.io.IOException if the given bytes could not be deserialized into an instance of this class
   */
  public static DepthDataContent fromByteBuffer(
      java.nio.ByteBuffer b) throws java.io.IOException {
    return DECODER.decode(b);
  }

  /** 交易对符号 */
  private java.lang.String symbol;
  /** 最后更新ID */
  private long lastUpdateId;
  /** 事件时间(毫秒) */
  private long eventTime;
  /** 买单列表 */
  private java.util.List<com.crypto.trading.common.avro.DepthLevel> bids;
  /** 卖单列表 */
  private java.util.List<com.crypto.trading.common.avro.DepthLevel> asks;

  /**
   * Default constructor.  Note that this does not initialize fields
   * to their default values from the schema.  If that is desired then
   * one should use <code>newBuilder()</code>.
   */
  public DepthDataContent() {}

  /**
   * All-args constructor.
   * @param symbol 交易对符号
   * @param lastUpdateId 最后更新ID
   * @param eventTime 事件时间(毫秒)
   * @param bids 买单列表
   * @param asks 卖单列表
   */
  public DepthDataContent(java.lang.String symbol, java.lang.Long lastUpdateId, java.lang.Long eventTime, java.util.List<com.crypto.trading.common.avro.DepthLevel> bids, java.util.List<com.crypto.trading.common.avro.DepthLevel> asks) {
    this.symbol = symbol;
    this.lastUpdateId = lastUpdateId;
    this.eventTime = eventTime;
    this.bids = bids;
    this.asks = asks;
  }

  @Override
  public org.apache.avro.specific.SpecificData getSpecificData() { return MODEL$; }

  @Override
  public org.apache.avro.Schema getSchema() { return SCHEMA$; }

  // Used by DatumWriter.  Applications should not call.
  @Override
  public java.lang.Object get(int field$) {
    switch (field$) {
    case 0: return symbol;
    case 1: return lastUpdateId;
    case 2: return eventTime;
    case 3: return bids;
    case 4: return asks;
    default: throw new IndexOutOfBoundsException("Invalid index: " + field$);
    }
  }

  // Used by DatumReader.  Applications should not call.
  @Override
  @SuppressWarnings(value="unchecked")
  public void put(int field$, java.lang.Object value$) {
    switch (field$) {
    case 0: symbol = value$ != null ? value$.toString() : null; break;
    case 1: lastUpdateId = (java.lang.Long)value$; break;
    case 2: eventTime = (java.lang.Long)value$; break;
    case 3: bids = (java.util.List<com.crypto.trading.common.avro.DepthLevel>)value$; break;
    case 4: asks = (java.util.List<com.crypto.trading.common.avro.DepthLevel>)value$; break;
    default: throw new IndexOutOfBoundsException("Invalid index: " + field$);
    }
  }

  /**
   * Gets the value of the 'symbol' field.
   * @return 交易对符号
   */
  public java.lang.String getSymbol() {
    return symbol;
  }


  /**
   * Sets the value of the 'symbol' field.
   * 交易对符号
   * @param value the value to set.
   */
  public void setSymbol(java.lang.String value) {
    this.symbol = value;
  }

  /**
   * Gets the value of the 'lastUpdateId' field.
   * @return 最后更新ID
   */
  public long getLastUpdateId() {
    return lastUpdateId;
  }


  /**
   * Sets the value of the 'lastUpdateId' field.
   * 最后更新ID
   * @param value the value to set.
   */
  public void setLastUpdateId(long value) {
    this.lastUpdateId = value;
  }

  /**
   * Gets the value of the 'eventTime' field.
   * @return 事件时间(毫秒)
   */
  public long getEventTime() {
    return eventTime;
  }


  /**
   * Sets the value of the 'eventTime' field.
   * 事件时间(毫秒)
   * @param value the value to set.
   */
  public void setEventTime(long value) {
    this.eventTime = value;
  }

  /**
   * Gets the value of the 'bids' field.
   * @return 买单列表
   */
  public java.util.List<com.crypto.trading.common.avro.DepthLevel> getBids() {
    return bids;
  }


  /**
   * Sets the value of the 'bids' field.
   * 买单列表
   * @param value the value to set.
   */
  public void setBids(java.util.List<com.crypto.trading.common.avro.DepthLevel> value) {
    this.bids = value;
  }

  /**
   * Gets the value of the 'asks' field.
   * @return 卖单列表
   */
  public java.util.List<com.crypto.trading.common.avro.DepthLevel> getAsks() {
    return asks;
  }


  /**
   * Sets the value of the 'asks' field.
   * 卖单列表
   * @param value the value to set.
   */
  public void setAsks(java.util.List<com.crypto.trading.common.avro.DepthLevel> value) {
    this.asks = value;
  }

  /**
   * Creates a new DepthDataContent RecordBuilder.
   * @return A new DepthDataContent RecordBuilder
   */
  public static com.crypto.trading.common.avro.DepthDataContent.Builder newBuilder() {
    return new com.crypto.trading.common.avro.DepthDataContent.Builder();
  }

  /**
   * Creates a new DepthDataContent RecordBuilder by copying an existing Builder.
   * @param other The existing builder to copy.
   * @return A new DepthDataContent RecordBuilder
   */
  public static com.crypto.trading.common.avro.DepthDataContent.Builder newBuilder(com.crypto.trading.common.avro.DepthDataContent.Builder other) {
    if (other == null) {
      return new com.crypto.trading.common.avro.DepthDataContent.Builder();
    } else {
      return new com.crypto.trading.common.avro.DepthDataContent.Builder(other);
    }
  }

  /**
   * Creates a new DepthDataContent RecordBuilder by copying an existing DepthDataContent instance.
   * @param other The existing instance to copy.
   * @return A new DepthDataContent RecordBuilder
   */
  public static com.crypto.trading.common.avro.DepthDataContent.Builder newBuilder(com.crypto.trading.common.avro.DepthDataContent other) {
    if (other == null) {
      return new com.crypto.trading.common.avro.DepthDataContent.Builder();
    } else {
      return new com.crypto.trading.common.avro.DepthDataContent.Builder(other);
    }
  }

  /**
   * RecordBuilder for DepthDataContent instances.
   */
  @org.apache.avro.specific.AvroGenerated
  public static class Builder extends org.apache.avro.specific.SpecificRecordBuilderBase<DepthDataContent>
    implements org.apache.avro.data.RecordBuilder<DepthDataContent> {

    /** 交易对符号 */
    private java.lang.String symbol;
    /** 最后更新ID */
    private long lastUpdateId;
    /** 事件时间(毫秒) */
    private long eventTime;
    /** 买单列表 */
    private java.util.List<com.crypto.trading.common.avro.DepthLevel> bids;
    /** 卖单列表 */
    private java.util.List<com.crypto.trading.common.avro.DepthLevel> asks;

    /** Creates a new Builder */
    private Builder() {
      super(SCHEMA$, MODEL$);
    }

    /**
     * Creates a Builder by copying an existing Builder.
     * @param other The existing Builder to copy.
     */
    private Builder(com.crypto.trading.common.avro.DepthDataContent.Builder other) {
      super(other);
      if (isValidValue(fields()[0], other.symbol)) {
        this.symbol = data().deepCopy(fields()[0].schema(), other.symbol);
        fieldSetFlags()[0] = other.fieldSetFlags()[0];
      }
      if (isValidValue(fields()[1], other.lastUpdateId)) {
        this.lastUpdateId = data().deepCopy(fields()[1].schema(), other.lastUpdateId);
        fieldSetFlags()[1] = other.fieldSetFlags()[1];
      }
      if (isValidValue(fields()[2], other.eventTime)) {
        this.eventTime = data().deepCopy(fields()[2].schema(), other.eventTime);
        fieldSetFlags()[2] = other.fieldSetFlags()[2];
      }
      if (isValidValue(fields()[3], other.bids)) {
        this.bids = data().deepCopy(fields()[3].schema(), other.bids);
        fieldSetFlags()[3] = other.fieldSetFlags()[3];
      }
      if (isValidValue(fields()[4], other.asks)) {
        this.asks = data().deepCopy(fields()[4].schema(), other.asks);
        fieldSetFlags()[4] = other.fieldSetFlags()[4];
      }
    }

    /**
     * Creates a Builder by copying an existing DepthDataContent instance
     * @param other The existing instance to copy.
     */
    private Builder(com.crypto.trading.common.avro.DepthDataContent other) {
      super(SCHEMA$, MODEL$);
      if (isValidValue(fields()[0], other.symbol)) {
        this.symbol = data().deepCopy(fields()[0].schema(), other.symbol);
        fieldSetFlags()[0] = true;
      }
      if (isValidValue(fields()[1], other.lastUpdateId)) {
        this.lastUpdateId = data().deepCopy(fields()[1].schema(), other.lastUpdateId);
        fieldSetFlags()[1] = true;
      }
      if (isValidValue(fields()[2], other.eventTime)) {
        this.eventTime = data().deepCopy(fields()[2].schema(), other.eventTime);
        fieldSetFlags()[2] = true;
      }
      if (isValidValue(fields()[3], other.bids)) {
        this.bids = data().deepCopy(fields()[3].schema(), other.bids);
        fieldSetFlags()[3] = true;
      }
      if (isValidValue(fields()[4], other.asks)) {
        this.asks = data().deepCopy(fields()[4].schema(), other.asks);
        fieldSetFlags()[4] = true;
      }
    }

    /**
      * Gets the value of the 'symbol' field.
      * 交易对符号
      * @return The value.
      */
    public java.lang.String getSymbol() {
      return symbol;
    }


    /**
      * Sets the value of the 'symbol' field.
      * 交易对符号
      * @param value The value of 'symbol'.
      * @return This builder.
      */
    public com.crypto.trading.common.avro.DepthDataContent.Builder setSymbol(java.lang.String value) {
      validate(fields()[0], value);
      this.symbol = value;
      fieldSetFlags()[0] = true;
      return this;
    }

    /**
      * Checks whether the 'symbol' field has been set.
      * 交易对符号
      * @return True if the 'symbol' field has been set, false otherwise.
      */
    public boolean hasSymbol() {
      return fieldSetFlags()[0];
    }


    /**
      * Clears the value of the 'symbol' field.
      * 交易对符号
      * @return This builder.
      */
    public com.crypto.trading.common.avro.DepthDataContent.Builder clearSymbol() {
      symbol = null;
      fieldSetFlags()[0] = false;
      return this;
    }

    /**
      * Gets the value of the 'lastUpdateId' field.
      * 最后更新ID
      * @return The value.
      */
    public long getLastUpdateId() {
      return lastUpdateId;
    }


    /**
      * Sets the value of the 'lastUpdateId' field.
      * 最后更新ID
      * @param value The value of 'lastUpdateId'.
      * @return This builder.
      */
    public com.crypto.trading.common.avro.DepthDataContent.Builder setLastUpdateId(long value) {
      validate(fields()[1], value);
      this.lastUpdateId = value;
      fieldSetFlags()[1] = true;
      return this;
    }

    /**
      * Checks whether the 'lastUpdateId' field has been set.
      * 最后更新ID
      * @return True if the 'lastUpdateId' field has been set, false otherwise.
      */
    public boolean hasLastUpdateId() {
      return fieldSetFlags()[1];
    }


    /**
      * Clears the value of the 'lastUpdateId' field.
      * 最后更新ID
      * @return This builder.
      */
    public com.crypto.trading.common.avro.DepthDataContent.Builder clearLastUpdateId() {
      fieldSetFlags()[1] = false;
      return this;
    }

    /**
      * Gets the value of the 'eventTime' field.
      * 事件时间(毫秒)
      * @return The value.
      */
    public long getEventTime() {
      return eventTime;
    }


    /**
      * Sets the value of the 'eventTime' field.
      * 事件时间(毫秒)
      * @param value The value of 'eventTime'.
      * @return This builder.
      */
    public com.crypto.trading.common.avro.DepthDataContent.Builder setEventTime(long value) {
      validate(fields()[2], value);
      this.eventTime = value;
      fieldSetFlags()[2] = true;
      return this;
    }

    /**
      * Checks whether the 'eventTime' field has been set.
      * 事件时间(毫秒)
      * @return True if the 'eventTime' field has been set, false otherwise.
      */
    public boolean hasEventTime() {
      return fieldSetFlags()[2];
    }


    /**
      * Clears the value of the 'eventTime' field.
      * 事件时间(毫秒)
      * @return This builder.
      */
    public com.crypto.trading.common.avro.DepthDataContent.Builder clearEventTime() {
      fieldSetFlags()[2] = false;
      return this;
    }

    /**
      * Gets the value of the 'bids' field.
      * 买单列表
      * @return The value.
      */
    public java.util.List<com.crypto.trading.common.avro.DepthLevel> getBids() {
      return bids;
    }


    /**
      * Sets the value of the 'bids' field.
      * 买单列表
      * @param value The value of 'bids'.
      * @return This builder.
      */
    public com.crypto.trading.common.avro.DepthDataContent.Builder setBids(java.util.List<com.crypto.trading.common.avro.DepthLevel> value) {
      validate(fields()[3], value);
      this.bids = value;
      fieldSetFlags()[3] = true;
      return this;
    }

    /**
      * Checks whether the 'bids' field has been set.
      * 买单列表
      * @return True if the 'bids' field has been set, false otherwise.
      */
    public boolean hasBids() {
      return fieldSetFlags()[3];
    }


    /**
      * Clears the value of the 'bids' field.
      * 买单列表
      * @return This builder.
      */
    public com.crypto.trading.common.avro.DepthDataContent.Builder clearBids() {
      bids = null;
      fieldSetFlags()[3] = false;
      return this;
    }

    /**
      * Gets the value of the 'asks' field.
      * 卖单列表
      * @return The value.
      */
    public java.util.List<com.crypto.trading.common.avro.DepthLevel> getAsks() {
      return asks;
    }


    /**
      * Sets the value of the 'asks' field.
      * 卖单列表
      * @param value The value of 'asks'.
      * @return This builder.
      */
    public com.crypto.trading.common.avro.DepthDataContent.Builder setAsks(java.util.List<com.crypto.trading.common.avro.DepthLevel> value) {
      validate(fields()[4], value);
      this.asks = value;
      fieldSetFlags()[4] = true;
      return this;
    }

    /**
      * Checks whether the 'asks' field has been set.
      * 卖单列表
      * @return True if the 'asks' field has been set, false otherwise.
      */
    public boolean hasAsks() {
      return fieldSetFlags()[4];
    }


    /**
      * Clears the value of the 'asks' field.
      * 卖单列表
      * @return This builder.
      */
    public com.crypto.trading.common.avro.DepthDataContent.Builder clearAsks() {
      asks = null;
      fieldSetFlags()[4] = false;
      return this;
    }

    @Override
    @SuppressWarnings("unchecked")
    public DepthDataContent build() {
      try {
        DepthDataContent record = new DepthDataContent();
        record.symbol = fieldSetFlags()[0] ? this.symbol : (java.lang.String) defaultValue(fields()[0]);
        record.lastUpdateId = fieldSetFlags()[1] ? this.lastUpdateId : (java.lang.Long) defaultValue(fields()[1]);
        record.eventTime = fieldSetFlags()[2] ? this.eventTime : (java.lang.Long) defaultValue(fields()[2]);
        record.bids = fieldSetFlags()[3] ? this.bids : (java.util.List<com.crypto.trading.common.avro.DepthLevel>) defaultValue(fields()[3]);
        record.asks = fieldSetFlags()[4] ? this.asks : (java.util.List<com.crypto.trading.common.avro.DepthLevel>) defaultValue(fields()[4]);
        return record;
      } catch (org.apache.avro.AvroMissingFieldException e) {
        throw e;
      } catch (java.lang.Exception e) {
        throw new org.apache.avro.AvroRuntimeException(e);
      }
    }
  }

  @SuppressWarnings("unchecked")
  private static final org.apache.avro.io.DatumWriter<DepthDataContent>
    WRITER$ = (org.apache.avro.io.DatumWriter<DepthDataContent>)MODEL$.createDatumWriter(SCHEMA$);

  @Override public void writeExternal(java.io.ObjectOutput out)
    throws java.io.IOException {
    WRITER$.write(this, SpecificData.getEncoder(out));
  }

  @SuppressWarnings("unchecked")
  private static final org.apache.avro.io.DatumReader<DepthDataContent>
    READER$ = (org.apache.avro.io.DatumReader<DepthDataContent>)MODEL$.createDatumReader(SCHEMA$);

  @Override public void readExternal(java.io.ObjectInput in)
    throws java.io.IOException {
    READER$.read(this, SpecificData.getDecoder(in));
  }

  @Override protected boolean hasCustomCoders() { return true; }

  @Override public void customEncode(org.apache.avro.io.Encoder out)
    throws java.io.IOException
  {
    out.writeString(this.symbol);

    out.writeLong(this.lastUpdateId);

    out.writeLong(this.eventTime);

    long size0 = this.bids.size();
    out.writeArrayStart();
    out.setItemCount(size0);
    long actualSize0 = 0;
    for (com.crypto.trading.common.avro.DepthLevel e0: this.bids) {
      actualSize0++;
      out.startItem();
      e0.customEncode(out);
    }
    out.writeArrayEnd();
    if (actualSize0 != size0)
      throw new java.util.ConcurrentModificationException("Array-size written was " + size0 + ", but element count was " + actualSize0 + ".");

    long size1 = this.asks.size();
    out.writeArrayStart();
    out.setItemCount(size1);
    long actualSize1 = 0;
    for (com.crypto.trading.common.avro.DepthLevel e1: this.asks) {
      actualSize1++;
      out.startItem();
      e1.customEncode(out);
    }
    out.writeArrayEnd();
    if (actualSize1 != size1)
      throw new java.util.ConcurrentModificationException("Array-size written was " + size1 + ", but element count was " + actualSize1 + ".");

  }

  @Override public void customDecode(org.apache.avro.io.ResolvingDecoder in)
    throws java.io.IOException
  {
    org.apache.avro.Schema.Field[] fieldOrder = in.readFieldOrderIfDiff();
    if (fieldOrder == null) {
      this.symbol = in.readString();

      this.lastUpdateId = in.readLong();

      this.eventTime = in.readLong();

      long size0 = in.readArrayStart();
      java.util.List<com.crypto.trading.common.avro.DepthLevel> a0 = this.bids;
      if (a0 == null) {
        a0 = new SpecificData.Array<com.crypto.trading.common.avro.DepthLevel>((int)size0, SCHEMA$.getField("bids").schema());
        this.bids = a0;
      } else a0.clear();
      SpecificData.Array<com.crypto.trading.common.avro.DepthLevel> ga0 = (a0 instanceof SpecificData.Array ? (SpecificData.Array<com.crypto.trading.common.avro.DepthLevel>)a0 : null);
      for ( ; 0 < size0; size0 = in.arrayNext()) {
        for ( ; size0 != 0; size0--) {
          com.crypto.trading.common.avro.DepthLevel e0 = (ga0 != null ? ga0.peek() : null);
          if (e0 == null) {
            e0 = new com.crypto.trading.common.avro.DepthLevel();
          }
          e0.customDecode(in);
          a0.add(e0);
        }
      }

      long size1 = in.readArrayStart();
      java.util.List<com.crypto.trading.common.avro.DepthLevel> a1 = this.asks;
      if (a1 == null) {
        a1 = new SpecificData.Array<com.crypto.trading.common.avro.DepthLevel>((int)size1, SCHEMA$.getField("asks").schema());
        this.asks = a1;
      } else a1.clear();
      SpecificData.Array<com.crypto.trading.common.avro.DepthLevel> ga1 = (a1 instanceof SpecificData.Array ? (SpecificData.Array<com.crypto.trading.common.avro.DepthLevel>)a1 : null);
      for ( ; 0 < size1; size1 = in.arrayNext()) {
        for ( ; size1 != 0; size1--) {
          com.crypto.trading.common.avro.DepthLevel e1 = (ga1 != null ? ga1.peek() : null);
          if (e1 == null) {
            e1 = new com.crypto.trading.common.avro.DepthLevel();
          }
          e1.customDecode(in);
          a1.add(e1);
        }
      }

    } else {
      for (int i = 0; i < 5; i++) {
        switch (fieldOrder[i].pos()) {
        case 0:
          this.symbol = in.readString();
          break;

        case 1:
          this.lastUpdateId = in.readLong();
          break;

        case 2:
          this.eventTime = in.readLong();
          break;

        case 3:
          long size0 = in.readArrayStart();
          java.util.List<com.crypto.trading.common.avro.DepthLevel> a0 = this.bids;
          if (a0 == null) {
            a0 = new SpecificData.Array<com.crypto.trading.common.avro.DepthLevel>((int)size0, SCHEMA$.getField("bids").schema());
            this.bids = a0;
          } else a0.clear();
          SpecificData.Array<com.crypto.trading.common.avro.DepthLevel> ga0 = (a0 instanceof SpecificData.Array ? (SpecificData.Array<com.crypto.trading.common.avro.DepthLevel>)a0 : null);
          for ( ; 0 < size0; size0 = in.arrayNext()) {
            for ( ; size0 != 0; size0--) {
              com.crypto.trading.common.avro.DepthLevel e0 = (ga0 != null ? ga0.peek() : null);
              if (e0 == null) {
                e0 = new com.crypto.trading.common.avro.DepthLevel();
              }
              e0.customDecode(in);
              a0.add(e0);
            }
          }
          break;

        case 4:
          long size1 = in.readArrayStart();
          java.util.List<com.crypto.trading.common.avro.DepthLevel> a1 = this.asks;
          if (a1 == null) {
            a1 = new SpecificData.Array<com.crypto.trading.common.avro.DepthLevel>((int)size1, SCHEMA$.getField("asks").schema());
            this.asks = a1;
          } else a1.clear();
          SpecificData.Array<com.crypto.trading.common.avro.DepthLevel> ga1 = (a1 instanceof SpecificData.Array ? (SpecificData.Array<com.crypto.trading.common.avro.DepthLevel>)a1 : null);
          for ( ; 0 < size1; size1 = in.arrayNext()) {
            for ( ; size1 != 0; size1--) {
              com.crypto.trading.common.avro.DepthLevel e1 = (ga1 != null ? ga1.peek() : null);
              if (e1 == null) {
                e1 = new com.crypto.trading.common.avro.DepthLevel();
              }
              e1.customDecode(in);
              a1.add(e1);
            }
          }
          break;

        default:
          throw new java.io.IOException("Corrupt ResolvingDecoder.");
        }
      }
    }
  }
}










