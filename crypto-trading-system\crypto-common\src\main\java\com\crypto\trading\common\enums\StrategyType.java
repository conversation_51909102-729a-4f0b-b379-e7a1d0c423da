package com.crypto.trading.common.enums;

/**
 * 策略类型枚举
 * <p>
 * 表示不同的交易策略类型
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public enum StrategyType {

    /**
     * 简单移动平均线策略
     */
    SMA("sma", "简单移动平均线", "基于简单移动平均线的交叉信号生成交易策略", RiskLevel.MEDIUM),

    /**
     * 指数移动平均线策略
     */
    EMA("ema", "指数移动平均线", "基于指数移动平均线的交叉信号生成交易策略", RiskLevel.MEDIUM),

    /**
     * 布林带策略
     */
    BOLLINGER_BANDS("bollingerBands", "布林带", "基于价格与布林带边界的关系生成交易信号", RiskLevel.MEDIUM),

    /**
     * 相对强弱指数策略
     */
    RSI("rsi", "相对强弱指数", "基于RSI指标的超买超卖区间生成交易信号", RiskLevel.HIGH),

    /**
     * MACD策略
     */
    MACD("macd", "MACD", "基于MACD指标的柱状图和信号线交叉生成交易信号", RiskLevel.MEDIUM),

    /**
     * 随机指标策略
     */
    STOCHASTIC("stochastic", "随机指标", "基于随机指标的超买超卖区间生成交易信号", RiskLevel.HIGH),

    /**
     * 双均线策略
     */
    DUAL_MA("dualMa", "双均线", "基于快速和慢速移动平均线的交叉生成交易信号", RiskLevel.MEDIUM),

    /**
     * 三均线策略
     */
    TRIPLE_MA("tripleMa", "三均线", "基于三条不同周期移动平均线的关系生成交易信号", RiskLevel.MEDIUM),

    /**
     * 网格交易策略
     */
    GRID("grid", "网格交易", "在预设价格区间内设置网格，按网格买入卖出", RiskLevel.LOW),

    /**
     * 马丁策略
     */
    MARTINGALE("martingale", "马丁策略", "亏损后按比例增加仓位的策略", RiskLevel.VERY_HIGH),

    /**
     * 反马丁策略
     */
    ANTI_MARTINGALE("antiMartingale", "反马丁策略", "盈利后按比例增加仓位的策略", RiskLevel.HIGH),

    /**
     * 追踪止损策略
     */
    TRAILING_STOP("trailingStop", "追踪止损", "根据价格变动动态调整止损价位的策略", RiskLevel.MEDIUM),

    /**
     * 动量策略
     */
    MOMENTUM("momentum", "动量策略", "基于价格变动方向和速度生成交易信号", RiskLevel.HIGH),

    /**
     * 突破策略
     */
    BREAKOUT("breakout", "突破策略", "基于价格突破关键水平生成交易信号", RiskLevel.HIGH),

    /**
     * 趋势跟踪策略
     */
    TREND_FOLLOWING("trendFollowing", "趋势跟踪", "识别并跟随市场趋势方向交易", RiskLevel.MEDIUM),

    /**
     * 套利策略
     */
    ARBITRAGE("arbitrage", "套利策略", "利用不同市场或产品间的价格差异获利", RiskLevel.LOW),

    /**
     * 统计套利策略
     */
    STATISTICAL_ARBITRAGE("statisticalArbitrage", "统计套利", "基于统计模型寻找价格偏离并回归的机会", RiskLevel.MEDIUM),

    /**
     * 日内交易策略
     */
    DAY_TRADING("dayTrading", "日内交易", "在单个交易日内完成交易循环的策略", RiskLevel.HIGH),

    /**
     * 情绪分析策略
     */
    SENTIMENT_ANALYSIS("sentimentAnalysis", "情绪分析", "基于市场情绪指标生成交易信号", RiskLevel.HIGH),

    /**
     * 波动率策略
     */
    VOLATILITY("volatility", "波动率策略", "基于市场波动率变化生成交易信号", RiskLevel.HIGH),

    /**
     * 价值平均策略
     */
    VALUE_AVERAGING("valueAveraging", "价值平均策略", "定期调整投资金额以保持预期增长率", RiskLevel.LOW),

    /**
     * 多因子策略
     */
    MULTI_FACTOR("multiFactor", "多因子策略", "综合多个因子信号生成交易决策", RiskLevel.MEDIUM),

    /**
     * 机器学习策略
     */
    MACHINE_LEARNING("machineLearning", "机器学习策略", "使用机器学习算法预测市场走势并交易", RiskLevel.HIGH);

    /**
     * 代码
     */
    private final String code;

    /**
     * 名称
     */
    private final String name;

    /**
     * 描述
     */
    private final String desc;

    /**
     * 风险级别
     */
    private final RiskLevel riskLevel;

    /**
     * 构造函数
     *
     * @param code      代码
     * @param name      名称
     * @param desc      描述
     * @param riskLevel 风险级别
     */
    StrategyType(String code, String name, String desc, RiskLevel riskLevel) {
        this.code = code;
        this.name = name;
        this.desc = desc;
        this.riskLevel = riskLevel;
    }

    /**
     * 获取代码
     *
     * @return 代码
     */
    public String getCode() {
        return code;
    }

    /**
     * 获取名称
     *
     * @return 名称
     */
    public String getName() {
        return name;
    }

    /**
     * 获取描述
     *
     * @return 描述
     */
    public String getDesc() {
        return desc;
    }

    /**
     * 获取风险级别
     *
     * @return 风险级别
     */
    public RiskLevel getRiskLevel() {
        return riskLevel;
    }

    /**
     * 根据代码获取枚举值
     *
     * @param code 代码
     * @return 枚举值
     */
    public static StrategyType fromCode(String code) {
        for (StrategyType type : StrategyType.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知的策略类型代码: " + code);
    }

    /**
     * 获取指定风险级别的策略类型列表
     *
     * @param riskLevel 风险级别
     * @return 策略类型数组
     */
    public static StrategyType[] getStrategiesByRiskLevel(RiskLevel riskLevel) {
        return java.util.Arrays.stream(values())
                .filter(type -> type.getRiskLevel() == riskLevel)
                .toArray(StrategyType[]::new);
    }

    /**
     * 获取风险级别不高于指定级别的策略类型列表
     *
     * @param riskLevel 风险级别
     * @return 策略类型数组
     */
    public static StrategyType[] getStrategiesNotHigherThan(RiskLevel riskLevel) {
        return java.util.Arrays.stream(values())
                .filter(type -> !type.getRiskLevel().isHigherThan(riskLevel))
                .toArray(StrategyType[]::new);
    }
} 