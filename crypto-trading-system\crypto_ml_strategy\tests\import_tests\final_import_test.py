#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
最终导入验证测试

测试修复后的所有导入路径，使用ASCII字符避免编码问题
"""

import sys
import os
import time

# 添加src目录到Python路径
src_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 'src')
sys.path.insert(0, src_path)

def test_single_import(module_path, class_name, test_name):
    """测试单个导入"""
    try:
        start_time = time.time()
        module = __import__(module_path, fromlist=[class_name])
        cls = getattr(module, class_name)
        import_time = (time.time() - start_time) * 1000
        print(f"PASS {test_name}: {import_time:.1f}ms")
        return True, None
    except Exception as e:
        print(f"FAIL {test_name}: {str(e)}")
        return False, str(e)

def main():
    """主测试函数"""
    print("Final Import Validation Test")
    print("=" * 60)
    
    # 测试用例
    test_cases = [
        ("data.cache", "CacheIntegration", "CacheIntegration"),
        ("data.quality.data_validator", "DataValidator", "DataValidator"),
        ("data.quality", "DataQualityCore", "DataQualityCore"),
        ("data.clients.influxdb_client", "InfluxDBClient", "InfluxDBClient"),
        ("data.clients.mysql_client", "MySQLClient", "MySQLClient"),
        ("data.data_processor", "DataProcessor", "DataProcessor"),
        ("core.config", "Config", "Config"),
        ("api.kafka_client", "KafkaClient", "KafkaClient"),
    ]
    
    results = {}
    start_time = time.time()
    
    # 执行测试
    for module_path, class_name, test_name in test_cases:
        success, error = test_single_import(module_path, class_name, test_name)
        results[test_name] = success
    
    total_time = time.time() - start_time
    
    # 生成报告
    print("\n" + "=" * 60)
    print("Import Test Results")
    print("=" * 60)
    
    passed = sum(1 for success in results.values() if success)
    total = len(results)
    
    for test_name, success in results.items():
        status = "PASS" if success else "FAIL"
        print(f"{test_name}: {status}")
    
    print(f"\nTotal: {passed}/{total} tests passed")
    print(f"Time: {total_time:.2f}s")
    
    if passed == total:
        print("SUCCESS: All import tests passed!")
        return True
    else:
        print(f"WARNING: {total - passed} imports need fixing")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)