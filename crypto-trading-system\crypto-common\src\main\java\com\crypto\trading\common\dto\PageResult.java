package com.crypto.trading.common.dto;

import java.io.Serializable;
import java.util.List;

/**
 * 分页结果封装类
 * <p>
 * 用于封装分页查询的返回结果
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class PageResult<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 当前页码
     */
    private int pageNum;

    /**
     * 每页大小
     */
    private int pageSize;

    /**
     * 总记录数
     */
    private long total;

    /**
     * 总页数
     */
    private int pages;

    /**
     * 数据列表
     */
    private List<T> list;

    /**
     * 是否有上一页
     */
    private boolean hasPrevious;

    /**
     * 是否有下一页
     */
    private boolean hasNext;

    /**
     * 构造函数
     */
    public PageResult() {
    }

    /**
     * 构造函数
     *
     * @param pageNum  当前页码
     * @param pageSize 每页大小
     * @param total    总记录数
     * @param list     数据列表
     */
    public PageResult(int pageNum, int pageSize, long total, List<T> list) {
        this.pageNum = pageNum;
        this.pageSize = pageSize;
        this.total = total;
        this.list = list;
        this.pages = (int) (total % pageSize == 0 ? total / pageSize : total / pageSize + 1);
        this.hasPrevious = pageNum > 1;
        this.hasNext = pageNum < pages;
    }

    /**
     * 构建分页结果
     *
     * @param pageNum  当前页码
     * @param pageSize 每页大小
     * @param total    总记录数
     * @param list     数据列表
     * @param <T>      数据类型
     * @return 分页结果
     */
    public static <T> PageResult<T> build(int pageNum, int pageSize, long total, List<T> list) {
        return new PageResult<>(pageNum, pageSize, total, list);
    }

    /**
     * 构建空分页结果
     *
     * @param <T> 数据类型
     * @return 空分页结果
     */
    public static <T> PageResult<T> empty() {
        return new PageResult<>(1, 10, 0, List.of());
    }

    /**
     * 获取当前页码
     *
     * @return 当前页码
     */
    public int getPageNum() {
        return pageNum;
    }

    /**
     * 设置当前页码
     *
     * @param pageNum 当前页码
     * @return this
     */
    public PageResult<T> setPageNum(int pageNum) {
        this.pageNum = pageNum;
        return this;
    }

    /**
     * 获取每页大小
     *
     * @return 每页大小
     */
    public int getPageSize() {
        return pageSize;
    }

    /**
     * 设置每页大小
     *
     * @param pageSize 每页大小
     * @return this
     */
    public PageResult<T> setPageSize(int pageSize) {
        this.pageSize = pageSize;
        return this;
    }

    /**
     * 获取总记录数
     *
     * @return 总记录数
     */
    public long getTotal() {
        return total;
    }

    /**
     * 设置总记录数
     *
     * @param total 总记录数
     * @return this
     */
    public PageResult<T> setTotal(long total) {
        this.total = total;
        return this;
    }

    /**
     * 获取总页数
     *
     * @return 总页数
     */
    public int getPages() {
        return pages;
    }

    /**
     * 设置总页数
     *
     * @param pages 总页数
     * @return this
     */
    public PageResult<T> setPages(int pages) {
        this.pages = pages;
        return this;
    }

    /**
     * 获取数据列表
     *
     * @return 数据列表
     */
    public List<T> getList() {
        return list;
    }

    /**
     * 设置数据列表
     *
     * @param list 数据列表
     * @return this
     */
    public PageResult<T> setList(List<T> list) {
        this.list = list;
        return this;
    }

    /**
     * 是否有上一页
     *
     * @return 是否有上一页
     */
    public boolean isHasPrevious() {
        return hasPrevious;
    }

    /**
     * 设置是否有上一页
     *
     * @param hasPrevious 是否有上一页
     * @return this
     */
    public PageResult<T> setHasPrevious(boolean hasPrevious) {
        this.hasPrevious = hasPrevious;
        return this;
    }

    /**
     * 是否有下一页
     *
     * @return 是否有下一页
     */
    public boolean isHasNext() {
        return hasNext;
    }

    /**
     * 设置是否有下一页
     *
     * @param hasNext 是否有下一页
     * @return this
     */
    public PageResult<T> setHasNext(boolean hasNext) {
        this.hasNext = hasNext;
        return this;
    }
}