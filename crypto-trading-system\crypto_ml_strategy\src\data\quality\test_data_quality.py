#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据质量验证系统测试套件

全面测试数据质量验证系统的各个组件，包括验证器、修复器、监控器和集成功能。
验证性能目标和功能正确性。
"""

import logging
import time
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any
import unittest

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def generate_test_data(rows: int = 100, add_issues: bool = True) -> pd.DataFrame:
    """生成测试数据"""
    try:
        # 生成基础时间序列
        start_time = datetime.now() - timedelta(hours=rows//60)
        timestamps = pd.date_range(start=start_time, periods=rows, freq='1T')
        
        # 生成基础OHLCV数据
        base_price = 50000.0
        data = {
            'timestamp': [int(ts.timestamp() * 1000) for ts in timestamps],
            'open': [],
            'high': [],
            'low': [],
            'close': [],
            'volume': []
        }
        
        current_price = base_price
        for i in range(rows):
            # 模拟价格变动
            change = np.random.normal(0, 0.02)  # 2%标准差
            current_price *= (1 + change)
            
            open_price = current_price
            high_price = open_price * (1 + abs(np.random.normal(0, 0.01)))
            low_price = open_price * (1 - abs(np.random.normal(0, 0.01)))
            close_price = open_price + np.random.normal(0, open_price * 0.005)
            volume = np.random.uniform(100, 1000)
            
            data['open'].append(open_price)
            data['high'].append(max(open_price, high_price, close_price))
            data['low'].append(min(open_price, low_price, close_price))
            data['close'].append(close_price)
            data['volume'].append(volume)
        
        df = pd.DataFrame(data)
        df['datetime'] = pd.to_datetime(df['timestamp'], unit='ms')
        df.set_index('datetime', inplace=True)
        
        # 添加质量问题（如果需要）
        if add_issues:
            df = add_quality_issues(df)
        
        return df
        
    except Exception as e:
        logger.error(f"生成测试数据失败: {e}")
        return pd.DataFrame()


def add_quality_issues(df: pd.DataFrame) -> pd.DataFrame:
    """向数据中添加质量问题"""
    try:
        # 添加缺失值
        missing_indices = np.random.choice(df.index, size=int(len(df) * 0.05), replace=False)
        df.loc[missing_indices, 'volume'] = np.nan
        
        # 添加异常值
        outlier_indices = np.random.choice(df.index, size=int(len(df) * 0.03), replace=False)
        df.loc[outlier_indices, 'close'] *= np.random.choice([0.1, 10], size=len(outlier_indices))
        
        # 添加OHLC逻辑错误
        logic_error_indices = np.random.choice(df.index, size=int(len(df) * 0.02), replace=False)
        for idx in logic_error_indices:
            df.loc[idx, 'high'] = df.loc[idx, 'close'] * 0.9  # high < close
        
        # 添加负价格
        negative_indices = np.random.choice(df.index, size=int(len(df) * 0.01), replace=False)
        df.loc[negative_indices, 'low'] = -abs(df.loc[negative_indices, 'low'])
        
        return df
        
    except Exception as e:
        logger.error(f"添加质量问题失败: {e}")
        return df


def test_quality_validators():
    """测试质量验证器"""
    logger.info("开始测试质量验证器...")
    
    try:
        from .data_quality_core import QualityConfig
        from .data_quality_validators import (
            MissingValueValidator, OutlierValidator, RangeValidator, TimeSeriesValidator
        )
        
        # 生成测试数据
        test_data = generate_test_data(100, add_issues=True)
        config = QualityConfig()
        
        # 测试缺失值验证器
        logger.info("测试缺失值验证器...")
        missing_validator = MissingValueValidator()
        missing_issues = missing_validator.validate(test_data, config)
        logger.info(f"发现 {len(missing_issues)} 个缺失值问题")
        
        # 测试异常值验证器
        logger.info("测试异常值验证器...")
        outlier_validator = OutlierValidator()
        outlier_issues = outlier_validator.validate(test_data, config)
        logger.info(f"发现 {len(outlier_issues)} 个异常值问题")
        
        # 测试范围验证器
        logger.info("测试范围验证器...")
        range_validator = RangeValidator()
        range_issues = range_validator.validate(test_data, config)
        logger.info(f"发现 {len(range_issues)} 个范围问题")
        
        # 测试时间序列验证器
        logger.info("测试时间序列验证器...")
        ts_validator = TimeSeriesValidator()
        ts_issues = ts_validator.validate(test_data, config)
        logger.info(f"发现 {len(ts_issues)} 个时间序列问题")
        
        total_issues = len(missing_issues) + len(outlier_issues) + len(range_issues) + len(ts_issues)
        logger.info(f"验证器测试完成，总共发现 {total_issues} 个问题")
        
        return total_issues > 0  # 应该发现一些问题
        
    except Exception as e:
        logger.error(f"验证器测试失败: {e}")
        return False


def test_quality_repairers():
    """测试质量修复器"""
    logger.info("开始测试质量修复器...")
    
    try:
        from .data_quality_core import QualityConfig, QualityIssue, ValidationResult
        from .data_quality_repairers import (
            MissingValueRepairer, OutlierRepairer, RangeRepairer
        )
        
        # 生成有问题的测试数据
        test_data = generate_test_data(100, add_issues=True)
        config = QualityConfig()
        
        # 创建模拟问题
        issues = [
            QualityIssue(
                issue_type="missing_values",
                severity=ValidationResult.WARNING,
                description="测试缺失值",
                affected_columns=['volume'],
                affected_rows=[]
            ),
            QualityIssue(
                issue_type="outlier",
                severity=ValidationResult.WARNING,
                description="测试异常值",
                affected_columns=['close'],
                affected_rows=[]
            )
        ]
        
        # 测试缺失值修复器
        logger.info("测试缺失值修复器...")
        missing_repairer = MissingValueRepairer()
        repaired_data, repair_logs = missing_repairer.repair(test_data, issues, config)
        logger.info(f"缺失值修复日志: {repair_logs}")
        
        # 测试异常值修复器
        logger.info("测试异常值修复器...")
        outlier_repairer = OutlierRepairer()
        repaired_data, repair_logs = outlier_repairer.repair(repaired_data, issues, config)
        logger.info(f"异常值修复日志: {repair_logs}")
        
        # 验证修复效果
        original_missing = test_data.isnull().sum().sum()
        repaired_missing = repaired_data.isnull().sum().sum()
        improvement = original_missing - repaired_missing
        
        logger.info(f"修复器测试完成，缺失值减少: {improvement}")
        
        return improvement >= 0  # 修复后缺失值应该不增加
        
    except Exception as e:
        logger.error(f"修复器测试失败: {e}")
        return False


def test_quality_checker_performance():
    """测试质量检查器性能"""
    logger.info("开始测试质量检查器性能...")
    
    try:
        from .data_quality_core import DataQualityChecker, QualityConfig
        from .data_quality_validators import (
            MissingValueValidator, OutlierValidator, RangeValidator
        )
        
        # 创建质量检查器
        config = QualityConfig()
        config.max_check_duration_ms = 50.0  # 50ms目标
        
        checker = DataQualityChecker(config)
        
        # 注册验证器
        checker.register_validator(MissingValueValidator())
        checker.register_validator(OutlierValidator())
        checker.register_validator(RangeValidator())
        
        # 性能测试
        test_sizes = [100, 500, 1000]
        performance_results = []
        
        for size in test_sizes:
            test_data = generate_test_data(size, add_issues=True)
            
            # 执行多次测试
            times = []
            for _ in range(10):
                start_time = time.time()
                metrics = checker.check_data_quality(test_data, "BTCUSDT", "1m")
                end_time = time.time()
                
                duration_ms = (end_time - start_time) * 1000
                times.append(duration_ms)
            
            avg_time = np.mean(times)
            performance_results.append({
                'size': size,
                'avg_time_ms': avg_time,
                'meets_target': avg_time <= config.max_check_duration_ms
            })
            
            logger.info(f"数据大小 {size}: 平均耗时 {avg_time:.1f}ms, 目标达成: {avg_time <= config.max_check_duration_ms}")
        
        # 验证性能目标
        all_meet_target = all(result['meets_target'] for result in performance_results)
        
        logger.info(f"性能测试完成，所有测试达成目标: {all_meet_target}")
        
        return all_meet_target
        
    except Exception as e:
        logger.error(f"性能测试失败: {e}")
        return False


def test_quality_monitor():
    """测试质量监控器"""
    logger.info("开始测试质量监控器...")
    
    try:
        from .data_quality_metrics import QualityMonitor
        from .data_quality_core import QualityMetrics, QualityLevel
        
        # 创建监控器
        monitor = QualityMonitor()
        
        # 生成模拟质量指标
        symbols = ['BTCUSDT', 'ETHUSDT']
        timeframes = ['1m', '5m', '15m']
        
        for symbol in symbols:
            for timeframe in timeframes:
                for i in range(10):
                    # 模拟质量指标
                    score = np.random.uniform(0.3, 0.9)
                    metrics = QualityMetrics(
                        overall_score=score,
                        completeness_score=score + np.random.uniform(-0.1, 0.1),
                        consistency_score=score + np.random.uniform(-0.1, 0.1),
                        accuracy_score=score + np.random.uniform(-0.1, 0.1),
                        timeliness_score=score + np.random.uniform(-0.1, 0.1),
                        validity_score=score + np.random.uniform(-0.1, 0.1),
                        total_records=100,
                        valid_records=int(100 * score),
                        missing_values=int(100 * (1 - score) * 0.5),
                        outliers_detected=int(100 * (1 - score) * 0.3),
                        quality_level=QualityLevel.GOOD if score > 0.7 else QualityLevel.FAIR,
                        timestamp=datetime.now() - timedelta(minutes=i)
                    )
                    
                    monitor.record_quality_metrics(symbol, timeframe, metrics)
        
        # 测试趋势计算
        trend = monitor.get_quality_trend('BTCUSDT', '1m', '1h')
        logger.info(f"质量趋势: {trend}")
        
        # 测试告警获取
        alerts = monitor.get_active_alerts()
        logger.info(f"活跃告警数量: {len(alerts)}")
        
        # 测试质量摘要
        summary = monitor.get_quality_summary()
        logger.info(f"质量摘要: {summary.get('overall_stats', {})}")
        
        # 测试统计信息
        stats = monitor.get_statistics()
        logger.info(f"监控器统计: {stats['statistics']}")
        
        logger.info("监控器测试完成")
        
        return True
        
    except Exception as e:
        logger.error(f"监控器测试失败: {e}")
        return False


def test_integration_manager():
    """测试集成管理器"""
    logger.info("开始测试集成管理器...")
    
    try:
        from .data_quality_integration import IntegratedQualityManager
        
        # 创建集成管理器
        manager = IntegratedQualityManager()
        
        # 生成测试数据
        test_data = generate_test_data(200, add_issues=True)
        
        # 测试数据验证和修复
        start_time = time.time()
        repaired_data, metrics, repair_logs = manager.validate_and_repair_data(
            test_data, "BTCUSDT", "1m"
        )
        processing_time = (time.time() - start_time) * 1000
        
        logger.info(f"集成处理耗时: {processing_time:.1f}ms")
        logger.info(f"质量分数: {metrics.overall_score:.3f}")
        logger.info(f"修复操作: {len(repair_logs)}")
        
        # 测试Kafka消息验证
        valid_message = {
            'data': {
                'symbol': 'BTCUSDT',
                'open': 50000.0,
                'high': 50100.0,
                'low': 49900.0,
                'close': 50050.0,
                'volume': 100.0
            },
            'timestamp': int(time.time() * 1000),
            'symbol': 'BTCUSDT'
        }
        
        is_valid, errors = manager.validate_kafka_message(valid_message)
        logger.info(f"Kafka消息验证: {is_valid}, 错误: {errors}")
        
        # 测试仪表板数据
        dashboard_data = manager.get_quality_dashboard_data(['BTCUSDT'])
        logger.info(f"仪表板数据获取成功: {len(dashboard_data)} 个部分")
        
        # 测试统计信息
        stats = manager.get_integration_statistics()
        logger.info(f"集成统计: {stats['integration_stats']}")
        
        # 验证性能目标
        performance_target_met = processing_time <= 50.0  # 50ms目标
        quality_acceptable = metrics.overall_score >= 0.5
        
        logger.info(f"集成管理器测试完成，性能目标达成: {performance_target_met}, 质量可接受: {quality_acceptable}")
        
        return performance_target_met and quality_acceptable
        
    except Exception as e:
        logger.error(f"集成管理器测试失败: {e}")
        return False


def test_memory_efficiency():
    """测试内存效率"""
    logger.info("开始测试内存效率...")
    
    try:
        import psutil
        from .data_quality_integration import IntegratedQualityManager
        
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # 创建管理器
        manager = IntegratedQualityManager()
        
        # 处理大量数据
        for i in range(50):  # 50次处理
            test_data = generate_test_data(500, add_issues=True)
            repaired_data, metrics, repair_logs = manager.validate_and_repair_data(
                test_data, f"TEST{i}", "1m"
            )
        
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory
        
        logger.info(f"初始内存: {initial_memory:.1f}MB")
        logger.info(f"最终内存: {final_memory:.1f}MB")
        logger.info(f"内存增长: {memory_increase:.1f}MB")
        
        # 验证内存目标
        memory_target_met = memory_increase < 100  # 目标：增长<100MB
        
        logger.info(f"内存效率测试完成，目标达成: {memory_target_met}")
        
        return memory_target_met
        
    except Exception as e:
        logger.error(f"内存效率测试失败: {e}")
        return False


def run_comprehensive_tests():
    """运行综合测试"""
    logger.info("=" * 60)
    logger.info("开始数据质量验证系统综合测试")
    logger.info("=" * 60)
    
    test_results = {}
    
    # 基础功能测试
    test_results['validators'] = test_quality_validators()
    test_results['repairers'] = test_quality_repairers()
    test_results['monitor'] = test_quality_monitor()
    test_results['integration'] = test_integration_manager()
    
    # 性能测试
    test_results['performance'] = test_quality_checker_performance()
    test_results['memory_efficiency'] = test_memory_efficiency()
    
    # 汇总结果
    logger.info("=" * 60)
    logger.info("测试结果汇总:")
    logger.info("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"{test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\n总体结果: {passed}/{total} 测试通过")
    
    # 性能目标验证
    logger.info("\n性能目标验证:")
    logger.info(f"质量检查延迟 <50ms: {'✅' if test_results.get('performance', False) else '❌'}")
    logger.info(f"内存使用 <100MB增长: {'✅' if test_results.get('memory_efficiency', False) else '❌'}")
    logger.info(f"功能完整性: {'✅' if all([test_results.get('validators', False), test_results.get('repairers', False), test_results.get('monitor', False)]) else '❌'}")
    logger.info(f"集成兼容性: {'✅' if test_results.get('integration', False) else '❌'}")
    
    if passed == total:
        logger.info("\n🎉 所有测试通过！数据质量验证系统工作正常。")
        logger.info("✅ 性能目标：质量检查延迟 <50ms")
        logger.info("✅ 内存效率：内存增长 <100MB")
        logger.info("✅ 功能完整：验证器、修复器、监控器全部正常")
        logger.info("✅ 集成兼容：与现有系统无缝集成")
    else:
        logger.warning("⚠️ 部分测试失败，需要进一步优化。")
    
    return passed == total


if __name__ == "__main__":
    run_comprehensive_tests()