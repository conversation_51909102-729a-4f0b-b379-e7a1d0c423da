package com.crypto.trading.common.util;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * JSON工具类
 * <p>
 * 提供JSON序列化和反序列化功能
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class JsonUtil {

    private static final Logger log = LoggerFactory.getLogger(JsonUtil.class);

    private static final ObjectMapper objectMapper = new ObjectMapper();

    static {
        // 配置ObjectMapper
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        objectMapper.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, true);
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
    }

    /**
     * 将对象转换为JSON字符串
     *
     * @param object 要转换的对象
     * @return JSON字符串
     */
    public static String toJsonString(Object object) {
        if (object == null) {
            return null;
        }

        try {
            return objectMapper.writeValueAsString(object);
        } catch (JsonProcessingException e) {
            log.error("将对象转换为JSON字符串失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 将JSON字符串转换为对象
     *
     * @param json  JSON字符串
     * @param clazz 目标类型
     * @param <T>   目标类型
     * @return 转换后的对象
     */
    public static <T> T parseJson(String json, Class<T> clazz) {
        if (json == null || json.isEmpty()) {
            return null;
        }

        try {
            return objectMapper.readValue(json, clazz);
        } catch (IOException e) {
            log.error("将JSON字符串转换为对象失败: json={}, class={}, error={}",
                    json, clazz.getName(), e.getMessage(), e);
            return null;
        }
    }

    /**
     * 将JSON字符串转换为复杂类型对象
     *
     * @param json          JSON字符串
     * @param typeReference 类型引用
     * @param <T>           目标类型
     * @return 转换后的对象
     */
    public static <T> T parseJson(String json, TypeReference<T> typeReference) {
        if (json == null || json.isEmpty()) {
            return null;
        }

        try {
            return objectMapper.readValue(json, typeReference);
        } catch (IOException e) {
            log.error("将JSON字符串转换为复杂类型对象失败: json={}, error={}",
                    json, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 将JSON字符串转换为Map
     *
     * @param json JSON字符串
     * @return Map对象
     */
    public static Map<String, Object> parseJsonToMap(String json) {
        if (json == null || json.isEmpty()) {
            return new HashMap<>();
        }

        try {
            return objectMapper.readValue(json, new TypeReference<Map<String, Object>>() {});
        } catch (IOException e) {
            log.error("将JSON字符串转换为Map失败: json={}, error={}",
                    json, e.getMessage(), e);
            return new HashMap<>();
        }
    }

    /**
     * 将JSON字符串转换为List
     *
     * @param json  JSON字符串
     * @param clazz 列表元素类型
     * @param <T>   列表元素类型
     * @return List对象
     */
    public static <T> List<T> parseJsonToList(String json, Class<T> clazz) {
        if (json == null || json.isEmpty()) {
            return new ArrayList<>();
        }

        try {
            return objectMapper.readValue(json, objectMapper.getTypeFactory().constructCollectionType(List.class, clazz));
        } catch (IOException e) {
            log.error("将JSON字符串转换为List失败: json={}, class={}, error={}",
                    json, clazz.getName(), e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取ObjectMapper实例
     *
     * @return ObjectMapper实例
     */
    public static ObjectMapper getObjectMapper() {
        return objectMapper;
    }
    
    /**
     * 将源对象转换为目标类型的对象
     * <p>
     * 通过JSON序列化和反序列化实现对象之间的转换
     * </p>
     *
     * @param source      源对象
     * @param targetClass 目标类型
     * @param <S>         源类型
     * @param <T>         目标类型
     * @return 转换后的对象
     */
    public static <S, T> T convertObject(S source, Class<T> targetClass) {
        if (source == null) {
            return null;
        }
        try {
            String json = toJsonString(source);
            return parseJson(json, targetClass);
        } catch (Exception e) {
            log.error("对象转换失败: source={}, target={}, error={}",
                    source.getClass().getName(), targetClass.getName(), e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * 将对象转换为格式化的JSON字符串
     *
     * @param object 要转换的对象
     * @return 格式化的JSON字符串
     */
    public static String toPrettyJsonString(Object object) {
        if (object == null) {
            return null;
        }

        try {
            return objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(object);
        } catch (JsonProcessingException e) {
            log.error("将对象转换为格式化JSON字符串失败: {}", e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * 将JSON字符串转换为对象（别名方法，与parseJson功能相同）
     *
     * @param json  JSON字符串
     * @param clazz 目标类型
     * @param <T>   目标类型
     * @return 转换后的对象
     */
    public static <T> T parseObject(String json, Class<T> clazz) {
        return parseJson(json, clazz);
    }
    
    /**
     * 将JSON字符串转换为复杂类型对象（别名方法，与parseJson功能相同）
     *
     * @param json          JSON字符串
     * @param typeReference 类型引用
     * @param <T>           目标类型
     * @return 转换后的对象
     */
    public static <T> T parseObject(String json, TypeReference<T> typeReference) {
        return parseJson(json, typeReference);
    }
    
    /**
     * 将JSON字符串转换为List（别名方法，与parseJsonToList功能相同）
     *
     * @param json  JSON字符串
     * @param clazz 列表元素类型
     * @param <T>   列表元素类型
     * @return List对象
     */
    public static <T> List<T> parseList(String json, Class<T> clazz) {
        return parseJsonToList(json, clazz);
    }
    
    /**
     * 将JSON字符串转换为Map
     *
     * @param json      JSON字符串
     * @param keyClass  键类型
     * @param valueClass 值类型
     * @param <K>       键类型
     * @param <V>       值类型
     * @return Map对象
     */
    public static <K, V> Map<K, V> parseMap(String json, Class<K> keyClass, Class<V> valueClass) {
        if (json == null || json.isEmpty()) {
            return new HashMap<>();
        }

        try {
            return objectMapper.readValue(json, objectMapper.getTypeFactory().constructMapType(Map.class, keyClass, valueClass));
        } catch (IOException e) {
            log.error("将JSON字符串转换为Map失败: json={}, keyClass={}, valueClass={}, error={}",
                    json, keyClass.getName(), valueClass.getName(), e.getMessage(), e);
            return new HashMap<>();
        }
    }
    
    /**
     * 将JSON字符串转换为JSONObject
     *
     * @param json JSON字符串
     * @return JSONObject对象
     */
    public static JSONObject parseJsonObject(String json) {
        if (json == null || json.isEmpty()) {
            return new JSONObject();
        }

        try {
            return JSON.parseObject(json);
        } catch (Exception e) {
            log.error("将JSON字符串转换为JSONObject失败: json={}, error={}",
                    json, e.getMessage(), e);
            return new JSONObject();
        }
    }
    
    /**
     * 使用FastJSON将对象转换为JSON字符串
     *
     * @param object 要转换的对象
     * @return JSON字符串
     */
    public static String toJsonStringByFastJson(Object object) {
        if (object == null) {
            return null;
        }

        try {
            return JSON.toJSONString(object);
        } catch (Exception e) {
            log.error("使用FastJSON将对象转换为JSON字符串失败: {}", e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * 使用FastJSON将JSON字符串转换为对象
     *
     * @param json  JSON字符串
     * @param clazz 目标类型
     * @param <T>   目标类型
     * @return 转换后的对象
     */
    public static <T> T parseObjectByFastJson(String json, Class<T> clazz) {
        if (json == null || json.isEmpty()) {
            return null;
        }

        try {
            return JSON.parseObject(json, clazz);
        } catch (Exception e) {
            log.error("使用FastJSON将JSON字符串转换为对象失败: json={}, class={}, error={}",
                    json, clazz.getName(), e.getMessage(), e);
            return null;
        }
    }
}