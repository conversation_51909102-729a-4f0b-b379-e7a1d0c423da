package com.crypto.trading.sdk.config;

import com.crypto.trading.common.constant.APILimitConstants;
import com.crypto.trading.sdk.limiter.BinanceApiInterceptor;
import okhttp3.ConnectionPool;
import okhttp3.OkHttpClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;

/**
 * API客户端配置类
 * <p>
 * 配置API客户端，包括OkHttpClient和拦截器
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Configuration
public class ApiClientConfig {

    /**
     * 连接超时时间
     */
    @Value("${api.connection-timeout-ms:#{T(com.crypto.trading.common.constant.APILimitConstants).API_CONNECTION_TIMEOUT_MS}}")
    private long connectionTimeoutMs;
    
    /**
     * 读取超时时间
     */
    @Value("${api.read-timeout-ms:#{T(com.crypto.trading.common.constant.APILimitConstants).API_READ_TIMEOUT_MS}}")
    private long readTimeoutMs;
    
    /**
     * 写入超时时间
     */
    @Value("${api.write-timeout-ms:#{T(com.crypto.trading.common.constant.APILimitConstants).API_WRITE_TIMEOUT_MS}}")
    private long writeTimeoutMs;
    
    /**
     * 最大空闲连接数
     */
    @Value("${api.max-idle-connections:10}")
    private int maxIdleConnections;
    
    /**
     * 保持连接时间
     */
    @Value("${api.keep-alive-duration-ms:300000}")
    private long keepAliveDurationMs;
    
    /**
     * API拦截器
     */
    private final BinanceApiInterceptor apiInterceptor;
    
    /**
     * 构造函数
     *
     * @param apiInterceptor API拦截器
     */
    public ApiClientConfig(BinanceApiInterceptor apiInterceptor) {
        this.apiInterceptor = apiInterceptor;
    }
    
    /**
     * 创建OkHttpClient Bean
     *
     * @return OkHttpClient实例
     */
    @Bean
    public OkHttpClient okHttpClient() {
        return new OkHttpClient.Builder()
                .connectTimeout(connectionTimeoutMs, TimeUnit.MILLISECONDS)
                .readTimeout(readTimeoutMs, TimeUnit.MILLISECONDS)
                .writeTimeout(writeTimeoutMs, TimeUnit.MILLISECONDS)
                .connectionPool(new ConnectionPool(maxIdleConnections, keepAliveDurationMs, TimeUnit.MILLISECONDS))
                .addInterceptor(apiInterceptor)
                .build();
    }
}