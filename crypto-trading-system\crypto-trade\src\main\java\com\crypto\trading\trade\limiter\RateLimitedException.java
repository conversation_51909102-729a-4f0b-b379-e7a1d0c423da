package com.crypto.trading.trade.limiter;

/**
 * 速率限制异常
 * 
 * <p>当API请求超出速率限制时抛出此异常</p>
 */
public class RateLimitedException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    /**
     * 创建速率限制异常
     *
     * @param message 异常消息
     */
    public RateLimitedException(String message) {
        super(message);
    }

    /**
     * 创建速率限制异常
     *
     * @param message 异常消息
     * @param cause 原始异常
     */
    public RateLimitedException(String message, Throwable cause) {
        super(message, cause);
    }
}