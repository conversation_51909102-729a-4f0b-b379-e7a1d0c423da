"""
Crypto ML Strategy - 日志集成测试套件

该模块实现了loguru日志系统的综合测试套件，包括单元测试、
性能测试、集成测试和压力测试。

主要功能：
- LoggingIntegrationTestRunner: 日志集成测试运行器
- 单元测试: 7个测试类，覆盖所有核心组件
- 性能测试: 日志写入性能、轮转效率、内存使用测试
- 集成测试: 与现有组件的集成测试
- 压力测试: 高并发日志写入测试

作者: Crypto ML Strategy Team
日期: 2024-01-20
版本: 1.0.0
"""

import asyncio
import json
import os
import tempfile
import threading
import time
import unittest
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Dict, List, Optional
from unittest.mock import Mock, patch, MagicMock

# 导入被测试的模块
from logging_core_manager import LoggingManager, LoggingConfig, LogLevel, get_global_logging_manager
from structured_logging_formatter import JSONLogFormatter, PerformanceLogFormatter, DebugLogFormatter
from log_rotation_manager import LogRotationManager, RotationConfig, get_global_rotation_manager
from performance_logging_core import PerformanceLogger, get_global_performance_logger
from performance_logging_reporter import PerformanceReporter, get_global_performance_reporter
from debug_logging_core import DebugLogger, DebugLevel, get_global_debug_logger
from log_output_handlers import LogOutputManager, FileOutputHandler, ConsoleOutputHandler
from log_security_privacy import LogSecurityManager, SecurityPolicy, AccessLevel


class TestLoggingCoreManager(unittest.TestCase):
    """测试日志核心管理器"""
    
    def setUp(self):
        """测试前设置"""
        self.temp_dir = tempfile.mkdtemp()
        self.config = LoggingConfig(
            file_path=os.path.join(self.temp_dir, "test.log"),
            enable_console=False
        )
        self.manager = LoggingManager(self.config)
    
    def tearDown(self):
        """测试后清理"""
        self.manager.shutdown()
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_initialization(self):
        """测试初始化"""
        self.assertTrue(self.manager.initialize())
        self.assertTrue(self.manager._initialized)
    
    def test_logger_creation(self):
        """测试日志器创建"""
        self.manager.initialize()
        logger = self.manager.get_logger("test_module")
        self.assertIsNotNone(logger)
    
    def test_config_validation(self):
        """测试配置验证"""
        valid_config = LoggingConfig()
        self.assertTrue(valid_config.validate())
        
        invalid_config = LoggingConfig(buffer_size=-1)
        self.assertFalse(invalid_config.validate())
    
    def test_statistics(self):
        """测试统计信息"""
        self.manager.initialize()
        stats = self.manager.get_statistics()
        self.assertIn("initialized", stats)
        self.assertTrue(stats["initialized"])


class TestStructuredLoggingFormatter(unittest.TestCase):
    """测试结构化日志格式化器"""
    
    def setUp(self):
        """测试前设置"""
        self.json_formatter = JSONLogFormatter()
        self.perf_formatter = PerformanceLogFormatter()
        self.debug_formatter = DebugLogFormatter()
    
    def test_json_formatting(self):
        """测试JSON格式化"""
        mock_record = {
            "time": "2024-01-20T10:30:45.123Z",
            "level": {"name": "INFO"},
            "name": "test_module",
            "function": "test_function",
            "line": 123,
            "message": "Test message",
            "extra": {"key": "value"}
        }
        
        result = self.json_formatter.format(mock_record)
        self.assertIsInstance(result, str)
        
        # 验证JSON格式
        parsed = json.loads(result)
        self.assertEqual(parsed["level"], "INFO")
        self.assertEqual(parsed["message"], "Test message")
    
    def test_performance_formatting(self):
        """测试性能格式化"""
        mock_record = {
            "time": "2024-01-20T10:30:45.123Z",
            "level": {"name": "INFO"},
            "name": "test_module",
            "function": "test_function",
            "message": "Performance test",
            "extra": {
                "execution_time_ms": 150.5,
                "memory_usage_mb": 64.2,
                "cpu_usage_percent": 25.3
            }
        }
        
        result = self.perf_formatter.format(mock_record)
        self.assertIsInstance(result, str)
        
        parsed = json.loads(result)
        self.assertEqual(parsed["level"], "PERFORMANCE")
        self.assertIn("metrics", parsed)
    
    def test_sensitive_data_filtering(self):
        """测试敏感数据过滤"""
        mock_record = {
            "time": "2024-01-20T10:30:45.123Z",
            "level": {"name": "INFO"},
            "name": "test_module",
            "function": "test_function",
            "message": "User login with password=secret123",
            "extra": {"api_key": "sk-1234567890abcdef"}
        }
        
        result = self.json_formatter.format(mock_record)
        parsed = json.loads(result)
        
        # 敏感数据应该被过滤
        self.assertIn("***REDACTED***", parsed["message"])


class TestLogRotationManager(unittest.TestCase):
    """测试日志轮转管理器"""
    
    def setUp(self):
        """测试前设置"""
        self.temp_dir = tempfile.mkdtemp()
        self.config = RotationConfig(
            strategy="size",
            max_size_mb=1,  # 1MB for testing
            cleanup_enabled=False  # 避免测试期间清理
        )
        self.manager = LogRotationManager(self.config)
    
    def tearDown(self):
        """测试后清理"""
        self.manager.shutdown()
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_initialization(self):
        """测试初始化"""
        self.assertTrue(self.manager.initialize())
        self.assertTrue(self.manager._initialized)
    
    def test_file_monitoring(self):
        """测试文件监控"""
        test_file = Path(self.temp_dir) / "test.log"
        test_file.write_text("test content")
        
        self.manager.initialize()
        self.manager.add_monitored_file(test_file, "test_log")
        
        self.assertIn("test_log", self.manager._monitored_files)
    
    def test_rotation_statistics(self):
        """测试轮转统计"""
        self.manager.initialize()
        stats = self.manager.get_rotation_statistics()
        
        self.assertIn("initialized", stats)
        self.assertIn("strategy", stats)
        self.assertEqual(stats["strategy"], "size")


class TestPerformanceLogging(unittest.TestCase):
    """测试性能日志记录"""
    
    def setUp(self):
        """测试前设置"""
        self.perf_logger = PerformanceLogger("test_performance")
        self.perf_reporter = PerformanceReporter()
    
    def tearDown(self):
        """测试后清理"""
        self.perf_logger.shutdown()
    
    def test_performance_monitoring_decorator(self):
        """测试性能监控装饰器"""
        @self.perf_logger.monitor
        def test_function(x, y):
            time.sleep(0.1)  # 模拟耗时操作
            return x + y
        
        result = test_function(1, 2)
        self.assertEqual(result, 3)
        
        # 检查是否记录了执行时间
        stats = self.perf_logger.time_tracker.get_statistics("test_performance_logging.test_function")
        self.assertGreater(len(stats), 0)
    
    def test_resource_monitoring(self):
        """测试资源监控"""
        self.perf_logger.resource_monitor.start_monitoring()
        time.sleep(2)  # 等待收集一些数据
        
        metrics = self.perf_logger.resource_monitor.get_current_metrics()
        self.assertIn("process_memory_mb", metrics)
        self.assertIn("process_cpu_percent", metrics)
        
        self.perf_logger.resource_monitor.stop_monitoring()
    
    def test_performance_reporting(self):
        """测试性能报告"""
        # 记录一些性能数据
        self.perf_reporter.record_performance(
            "test_function", 150.5, 64.2, 25.3
        )
        
        report = self.perf_reporter.generate_report(duration_minutes=1)
        self.assertIn("report_timestamp", report)
        self.assertIn("error_rates", report)


class TestDebugLogging(unittest.TestCase):
    """测试调试日志系统"""
    
    def setUp(self):
        """测试前设置"""
        self.debug_logger = DebugLogger()
    
    def test_debug_level_setting(self):
        """测试调试级别设置"""
        self.debug_logger.set_debug_level(DebugLevel.VERBOSE)
        level = self.debug_logger.mode_manager.get_debug_level()
        self.assertEqual(level, DebugLevel.VERBOSE)
    
    def test_module_debug_control(self):
        """测试模块调试控制"""
        self.debug_logger.enable_module_debug("test_module")
        self.assertTrue(self.debug_logger.mode_manager.is_debug_enabled("test_module"))
        
        self.debug_logger.disable_module_debug("test_module")
        self.assertFalse(self.debug_logger.mode_manager.is_debug_enabled("test_module"))
    
    def test_function_tracing(self):
        """测试函数跟踪"""
        self.debug_logger.set_debug_level(DebugLevel.DETAILED)
        self.debug_logger.enable_module_debug(__name__)
        
        @self.debug_logger.trace
        def traced_function(x):
            return x * 2
        
        result = traced_function(5)
        self.assertEqual(result, 10)
    
    def test_debug_point(self):
        """测试调试点"""
        self.debug_logger.set_debug_level(DebugLevel.BASIC)
        self.debug_logger.enable_module_debug(__name__)
        
        # 这应该不会抛出异常
        self.debug_logger.debug_point("Test debug point", {"var1": "value1"})


class TestLogOutputHandlers(unittest.TestCase):
    """测试日志输出处理器"""
    
    def setUp(self):
        """测试前设置"""
        self.temp_dir = tempfile.mkdtemp()
        self.output_manager = LogOutputManager()
    
    def tearDown(self):
        """测试后清理"""
        asyncio.run(self.output_manager.shutdown())
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_file_output_handler(self):
        """测试文件输出处理器"""
        test_file = os.path.join(self.temp_dir, "test_output.log")
        handler = FileOutputHandler("test_file", test_file)
        
        self.output_manager.add_handler(handler)
        self.assertIn("test_file", self.output_manager.list_handlers())
    
    def test_console_output_handler(self):
        """测试控制台输出处理器"""
        handler = ConsoleOutputHandler("test_console")
        
        self.output_manager.add_handler(handler)
        self.assertIn("test_console", self.output_manager.list_handlers())
    
    def test_handler_enable_disable(self):
        """测试处理器启用/禁用"""
        handler = ConsoleOutputHandler("test_handler")
        
        self.assertTrue(handler.is_enabled())
        
        handler.disable()
        self.assertFalse(handler.is_enabled())
        
        handler.enable()
        self.assertTrue(handler.is_enabled())


class TestLogSecurity(unittest.TestCase):
    """测试日志安全功能"""
    
    def setUp(self):
        """测试前设置"""
        self.policy = SecurityPolicy(
            encryption_enabled=True,
            access_control_enabled=True,
            audit_enabled=True
        )
        self.security_manager = LogSecurityManager(self.policy, "test_password")
    
    def test_sensitive_data_filtering(self):
        """测试敏感数据过滤"""
        message = "User login with password=secret123 and api_key=sk-abcdef"
        data = {"password": "secret", "username": "testuser"}
        
        result = self.security_manager.process_log_entry(message, data)
        
        # 敏感数据应该被过滤
        self.assertIn("***REDACTED***", result["message"])
        self.assertEqual(result["data"]["password"], "***REDACTED***")
    
    def test_access_control(self):
        """测试访问控制"""
        # 授予权限
        self.security_manager.access_control.grant_permission("test_user", AccessLevel.READ)
        
        # 检查权限
        has_permission = self.security_manager.check_access(
            "test_user", "127.0.0.1", AccessLevel.READ
        )
        self.assertTrue(has_permission)
        
        # 检查更高权限
        has_write_permission = self.security_manager.check_access(
            "test_user", "127.0.0.1", AccessLevel.WRITE
        )
        self.assertFalse(has_write_permission)
    
    def test_encryption(self):
        """测试加密功能"""
        if self.security_manager.encryption:
            original_message = "This is a test message"
            encrypted = self.security_manager.encryption.encrypt_message(original_message)
            decrypted = self.security_manager.encryption.decrypt_message(encrypted)
            
            self.assertNotEqual(original_message, encrypted)
            self.assertEqual(original_message, decrypted)


class LoggingPerformanceTest(unittest.TestCase):
    """日志性能测试"""
    
    def setUp(self):
        """测试前设置"""
        self.temp_dir = tempfile.mkdtemp()
        self.config = LoggingConfig(
            file_path=os.path.join(self.temp_dir, "perf_test.log"),
            enable_console=False,
            async_enabled=True
        )
        self.manager = LoggingManager(self.config)
        self.manager.initialize()
    
    def tearDown(self):
        """测试后清理"""
        self.manager.shutdown()
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_logging_throughput(self):
        """测试日志吞吐量"""
        logger = self.manager.get_logger("performance_test")
        
        start_time = time.time()
        num_logs = 1000
        
        for i in range(num_logs):
            logger.info(f"Performance test message {i}", extra={"iteration": i})
        
        end_time = time.time()
        duration = end_time - start_time
        throughput = num_logs / duration
        
        print(f"Logging throughput: {throughput:.2f} logs/second")
        
        # 期望至少每秒500条日志
        self.assertGreater(throughput, 500)
    
    def test_memory_usage(self):
        """测试内存使用"""
        import psutil
        import gc
        
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        logger = self.manager.get_logger("memory_test")
        
        # 生成大量日志
        for i in range(5000):
            logger.info(f"Memory test message {i}", extra={
                "data": "x" * 100,  # 100字符的数据
                "iteration": i
            })
        
        # 强制垃圾回收
        gc.collect()
        
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory
        
        print(f"Memory increase: {memory_increase:.2f} MB")
        
        # 期望内存增长不超过50MB
        self.assertLess(memory_increase, 50)


class LoggingStressTest(unittest.TestCase):
    """日志压力测试"""
    
    def setUp(self):
        """测试前设置"""
        self.temp_dir = tempfile.mkdtemp()
        self.config = LoggingConfig(
            file_path=os.path.join(self.temp_dir, "stress_test.log"),
            enable_console=False,
            async_enabled=True,
            buffer_size=5000
        )
        self.manager = LoggingManager(self.config)
        self.manager.initialize()
    
    def tearDown(self):
        """测试后清理"""
        self.manager.shutdown()
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_concurrent_logging(self):
        """测试并发日志记录"""
        logger = self.manager.get_logger("stress_test")
        num_threads = 10
        logs_per_thread = 500
        
        def log_worker(thread_id):
            for i in range(logs_per_thread):
                logger.info(f"Thread {thread_id} message {i}", extra={
                    "thread_id": thread_id,
                    "iteration": i,
                    "timestamp": time.time()
                })
        
        threads = []
        start_time = time.time()
        
        # 启动线程
        for thread_id in range(num_threads):
            thread = threading.Thread(target=log_worker, args=(thread_id,))
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        end_time = time.time()
        duration = end_time - start_time
        total_logs = num_threads * logs_per_thread
        throughput = total_logs / duration
        
        print(f"Concurrent logging throughput: {throughput:.2f} logs/second")
        print(f"Total logs: {total_logs}, Duration: {duration:.2f}s")
        
        # 期望并发吞吐量至少每秒1000条日志
        self.assertGreater(throughput, 1000)


class LoggingIntegrationTestRunner:
    """日志集成测试运行器"""
    
    def __init__(self):
        self.test_results = {}
        self.start_time = None
        self.end_time = None
    
    def run_all_tests(self) -> Dict[str, Any]:
        """运行所有测试"""
        self.start_time = datetime.now()
        
        test_classes = [
            TestLoggingCoreManager,
            TestStructuredLoggingFormatter,
            TestLogRotationManager,
            TestPerformanceLogging,
            TestDebugLogging,
            TestLogOutputHandlers,
            TestLogSecurity,
            LoggingPerformanceTest,
            LoggingStressTest
        ]
        
        for test_class in test_classes:
            self._run_test_class(test_class)
        
        self.end_time = datetime.now()
        
        return self._generate_test_report()
    
    def _run_test_class(self, test_class) -> None:
        """运行单个测试类"""
        try:
            suite = unittest.TestLoader().loadTestsFromTestCase(test_class)
            runner = unittest.TextTestRunner(verbosity=0, stream=open(os.devnull, 'w'))
            result = runner.run(suite)
            
            self.test_results[test_class.__name__] = {
                "tests_run": result.testsRun,
                "failures": len(result.failures),
                "errors": len(result.errors),
                "success_rate": (result.testsRun - len(result.failures) - len(result.errors)) / max(result.testsRun, 1),
                "details": {
                    "failures": [str(failure) for failure in result.failures],
                    "errors": [str(error) for error in result.errors]
                }
            }
            
        except Exception as e:
            self.test_results[test_class.__name__] = {
                "tests_run": 0,
                "failures": 0,
                "errors": 1,
                "success_rate": 0.0,
                "details": {"errors": [str(e)]}
            }
    
    def _generate_test_report(self) -> Dict[str, Any]:
        """生成测试报告"""
        total_tests = sum(result["tests_run"] for result in self.test_results.values())
        total_failures = sum(result["failures"] for result in self.test_results.values())
        total_errors = sum(result["errors"] for result in self.test_results.values())
        
        overall_success_rate = (total_tests - total_failures - total_errors) / max(total_tests, 1)
        
        duration = self.end_time - self.start_time if self.end_time and self.start_time else timedelta(0)
        
        return {
            "test_summary": {
                "total_test_classes": len(self.test_results),
                "total_tests": total_tests,
                "total_failures": total_failures,
                "total_errors": total_errors,
                "overall_success_rate": overall_success_rate,
                "duration_seconds": duration.total_seconds()
            },
            "test_results": self.test_results,
            "timestamp": datetime.now().isoformat(),
            "status": "PASSED" if overall_success_rate >= 0.95 else "FAILED"
        }
    
    def save_report(self, filename: Optional[str] = None) -> Path:
        """保存测试报告"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"logging_test_report_{timestamp}.json"
        
        report_path = Path("logs") / "test_reports" / filename
        report_path.parent.mkdir(parents=True, exist_ok=True)
        
        report = self._generate_test_report()
        
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        return report_path


def run_logging_integration_tests() -> Dict[str, Any]:
    """运行日志集成测试的便捷函数"""
    runner = LoggingIntegrationTestRunner()
    results = runner.run_all_tests()
    
    # 保存报告
    report_path = runner.save_report()
    results["report_path"] = str(report_path)
    
    # 打印摘要
    summary = results["test_summary"]
    print(f"\n{'='*60}")
    print("CRYPTO ML STRATEGY - LOGGING INTEGRATION TEST RESULTS")
    print(f"{'='*60}")
    print(f"Total Test Classes: {summary['total_test_classes']}")
    print(f"Total Tests: {summary['total_tests']}")
    print(f"Failures: {summary['total_failures']}")
    print(f"Errors: {summary['total_errors']}")
    print(f"Success Rate: {summary['overall_success_rate']:.2%}")
    print(f"Duration: {summary['duration_seconds']:.2f} seconds")
    print(f"Status: {results['status']}")
    print(f"Report saved to: {report_path}")
    print(f"{'='*60}\n")
    
    return results


if __name__ == "__main__":
    # 运行集成测试
    test_results = run_logging_integration_tests()
    
    # 退出码
    exit_code = 0 if test_results["status"] == "PASSED" else 1
    exit(exit_code)