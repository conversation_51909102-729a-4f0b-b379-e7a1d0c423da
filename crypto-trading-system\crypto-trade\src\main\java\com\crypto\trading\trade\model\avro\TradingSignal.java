/**
 * Autogenerated by Avro
 *
 * DO NOT EDIT DIRECTLY
 */
package com.crypto.trading.trade.model.avro;

import org.apache.avro.generic.GenericArray;
import org.apache.avro.specific.SpecificData;
import org.apache.avro.util.Utf8;
import org.apache.avro.message.BinaryMessageEncoder;
import org.apache.avro.message.BinaryMessageDecoder;
import org.apache.avro.message.SchemaStore;

/** 交易信号，由策略生成，包含交易决策信息 */
@org.apache.avro.specific.AvroGenerated
public class TradingSignal extends org.apache.avro.specific.SpecificRecordBase implements org.apache.avro.specific.SpecificRecord {
  private static final long serialVersionUID = -5391781977517966420L;


  public static final org.apache.avro.Schema SCHEMA$ = new org.apache.avro.Schema.Parser().parse("{\"type\":\"record\",\"name\":\"TradingSignal\",\"namespace\":\"com.crypto.trading.trade.model.avro\",\"doc\":\"交易信号，由策略生成，包含交易决策信息\",\"fields\":[{\"name\":\"id\",\"type\":{\"type\":\"string\",\"avro.java.string\":\"String\"},\"doc\":\"信号唯一ID\"},{\"name\":\"symbol\",\"type\":{\"type\":\"string\",\"avro.java.string\":\"String\"},\"doc\":\"交易对，如BTCUSDT\"},{\"name\":\"strategy\",\"type\":{\"type\":\"string\",\"avro.java.string\":\"String\"},\"doc\":\"策略名称\"},{\"name\":\"signalType\",\"type\":{\"type\":\"enum\",\"name\":\"SignalType\",\"symbols\":[\"BUY\",\"SELL\",\"CLOSE_LONG\",\"CLOSE_SHORT\",\"HOLD\"]},\"doc\":\"信号类型\"},{\"name\":\"confidence\",\"type\":\"double\",\"doc\":\"信号置信度，0.0-1.0\"},{\"name\":\"price\",\"type\":[\"null\",\"double\"],\"doc\":\"目标价格，可选\",\"default\":null},{\"name\":\"suggestedQuantity\",\"type\":[\"null\",\"double\"],\"doc\":\"建议交易数量，可选\",\"default\":null},{\"name\":\"stopLoss\",\"type\":[\"null\",\"double\"],\"doc\":\"止损价格，可选\",\"default\":null},{\"name\":\"takeProfit\",\"type\":[\"null\",\"double\"],\"doc\":\"止盈价格，可选\",\"default\":null},{\"name\":\"orderType\",\"type\":{\"type\":\"enum\",\"name\":\"OrderType\",\"symbols\":[\"MARKET\",\"LIMIT\",\"STOP\",\"STOP_MARKET\",\"TAKE_PROFIT\",\"TAKE_PROFIT_MARKET\"]},\"doc\":\"订单类型\"},{\"name\":\"timeInForce\",\"type\":{\"type\":\"enum\",\"name\":\"TimeInForce\",\"symbols\":[\"GTC\",\"IOC\",\"FOK\",\"GTX\"]},\"doc\":\"订单有效期\",\"default\":\"GTC\"},{\"name\":\"leverageLevel\",\"type\":[\"null\",\"int\"],\"doc\":\"杠杆倍数，可选\",\"default\":null},{\"name\":\"generatedTime\",\"type\":\"long\",\"doc\":\"信号生成时间（毫秒时间戳）\"},{\"name\":\"additionalParams\",\"type\":{\"type\":\"map\",\"values\":{\"type\":\"string\",\"avro.java.string\":\"String\"},\"avro.java.string\":\"String\"},\"doc\":\"额外参数，键值对形式\"}]}");
  public static org.apache.avro.Schema getClassSchema() { return SCHEMA$; }

  private static final SpecificData MODEL$ = new SpecificData();

  private static final BinaryMessageEncoder<TradingSignal> ENCODER =
      new BinaryMessageEncoder<>(MODEL$, SCHEMA$);

  private static final BinaryMessageDecoder<TradingSignal> DECODER =
      new BinaryMessageDecoder<>(MODEL$, SCHEMA$);

  /**
   * Return the BinaryMessageEncoder instance used by this class.
   * @return the message encoder used by this class
   */
  public static BinaryMessageEncoder<TradingSignal> getEncoder() {
    return ENCODER;
  }

  /**
   * Return the BinaryMessageDecoder instance used by this class.
   * @return the message decoder used by this class
   */
  public static BinaryMessageDecoder<TradingSignal> getDecoder() {
    return DECODER;
  }

  /**
   * Create a new BinaryMessageDecoder instance for this class that uses the specified {@link SchemaStore}.
   * @param resolver a {@link SchemaStore} used to find schemas by fingerprint
   * @return a BinaryMessageDecoder instance for this class backed by the given SchemaStore
   */
  public static BinaryMessageDecoder<TradingSignal> createDecoder(SchemaStore resolver) {
    return new BinaryMessageDecoder<>(MODEL$, SCHEMA$, resolver);
  }

  /**
   * Serializes this TradingSignal to a ByteBuffer.
   * @return a buffer holding the serialized data for this instance
   * @throws java.io.IOException if this instance could not be serialized
   */
  public java.nio.ByteBuffer toByteBuffer() throws java.io.IOException {
    return ENCODER.encode(this);
  }

  /**
   * Deserializes a TradingSignal from a ByteBuffer.
   * @param b a byte buffer holding serialized data for an instance of this class
   * @return a TradingSignal instance decoded from the given buffer
   * @throws java.io.IOException if the given bytes could not be deserialized into an instance of this class
   */
  public static TradingSignal fromByteBuffer(
      java.nio.ByteBuffer b) throws java.io.IOException {
    return DECODER.decode(b);
  }

  /** 信号唯一ID */
  private java.lang.String id;
  /** 交易对，如BTCUSDT */
  private java.lang.String symbol;
  /** 策略名称 */
  private java.lang.String strategy;
  /** 信号类型 */
  private com.crypto.trading.trade.model.avro.SignalType signalType;
  /** 信号置信度，0.0-1.0 */
  private double confidence;
  /** 目标价格，可选 */
  private java.lang.Double price;
  /** 建议交易数量，可选 */
  private java.lang.Double suggestedQuantity;
  /** 止损价格，可选 */
  private java.lang.Double stopLoss;
  /** 止盈价格，可选 */
  private java.lang.Double takeProfit;
  /** 订单类型 */
  private com.crypto.trading.trade.model.avro.OrderType orderType;
  /** 订单有效期 */
  private com.crypto.trading.trade.model.avro.TimeInForce timeInForce;
  /** 杠杆倍数，可选 */
  private java.lang.Integer leverageLevel;
  /** 信号生成时间（毫秒时间戳） */
  private long generatedTime;
  /** 额外参数，键值对形式 */
  private java.util.Map<java.lang.String,java.lang.String> additionalParams;

  /**
   * Default constructor.  Note that this does not initialize fields
   * to their default values from the schema.  If that is desired then
   * one should use <code>newBuilder()</code>.
   */
  public TradingSignal() {}

  /**
   * All-args constructor.
   * @param id 信号唯一ID
   * @param symbol 交易对，如BTCUSDT
   * @param strategy 策略名称
   * @param signalType 信号类型
   * @param confidence 信号置信度，0.0-1.0
   * @param price 目标价格，可选
   * @param suggestedQuantity 建议交易数量，可选
   * @param stopLoss 止损价格，可选
   * @param takeProfit 止盈价格，可选
   * @param orderType 订单类型
   * @param timeInForce 订单有效期
   * @param leverageLevel 杠杆倍数，可选
   * @param generatedTime 信号生成时间（毫秒时间戳）
   * @param additionalParams 额外参数，键值对形式
   */
  public TradingSignal(java.lang.String id, java.lang.String symbol, java.lang.String strategy, com.crypto.trading.trade.model.avro.SignalType signalType, java.lang.Double confidence, java.lang.Double price, java.lang.Double suggestedQuantity, java.lang.Double stopLoss, java.lang.Double takeProfit, com.crypto.trading.trade.model.avro.OrderType orderType, com.crypto.trading.trade.model.avro.TimeInForce timeInForce, java.lang.Integer leverageLevel, java.lang.Long generatedTime, java.util.Map<java.lang.String,java.lang.String> additionalParams) {
    this.id = id;
    this.symbol = symbol;
    this.strategy = strategy;
    this.signalType = signalType;
    this.confidence = confidence;
    this.price = price;
    this.suggestedQuantity = suggestedQuantity;
    this.stopLoss = stopLoss;
    this.takeProfit = takeProfit;
    this.orderType = orderType;
    this.timeInForce = timeInForce;
    this.leverageLevel = leverageLevel;
    this.generatedTime = generatedTime;
    this.additionalParams = additionalParams;
  }

  @Override
  public org.apache.avro.specific.SpecificData getSpecificData() { return MODEL$; }

  @Override
  public org.apache.avro.Schema getSchema() { return SCHEMA$; }

  // Used by DatumWriter.  Applications should not call.
  @Override
  public java.lang.Object get(int field$) {
    switch (field$) {
    case 0: return id;
    case 1: return symbol;
    case 2: return strategy;
    case 3: return signalType;
    case 4: return confidence;
    case 5: return price;
    case 6: return suggestedQuantity;
    case 7: return stopLoss;
    case 8: return takeProfit;
    case 9: return orderType;
    case 10: return timeInForce;
    case 11: return leverageLevel;
    case 12: return generatedTime;
    case 13: return additionalParams;
    default: throw new IndexOutOfBoundsException("Invalid index: " + field$);
    }
  }

  // Used by DatumReader.  Applications should not call.
  @Override
  @SuppressWarnings(value="unchecked")
  public void put(int field$, java.lang.Object value$) {
    switch (field$) {
    case 0: id = value$ != null ? value$.toString() : null; break;
    case 1: symbol = value$ != null ? value$.toString() : null; break;
    case 2: strategy = value$ != null ? value$.toString() : null; break;
    case 3: signalType = (com.crypto.trading.trade.model.avro.SignalType)value$; break;
    case 4: confidence = (java.lang.Double)value$; break;
    case 5: price = (java.lang.Double)value$; break;
    case 6: suggestedQuantity = (java.lang.Double)value$; break;
    case 7: stopLoss = (java.lang.Double)value$; break;
    case 8: takeProfit = (java.lang.Double)value$; break;
    case 9: orderType = (com.crypto.trading.trade.model.avro.OrderType)value$; break;
    case 10: timeInForce = (com.crypto.trading.trade.model.avro.TimeInForce)value$; break;
    case 11: leverageLevel = (java.lang.Integer)value$; break;
    case 12: generatedTime = (java.lang.Long)value$; break;
    case 13: additionalParams = (java.util.Map<java.lang.String,java.lang.String>)value$; break;
    default: throw new IndexOutOfBoundsException("Invalid index: " + field$);
    }
  }

  /**
   * Gets the value of the 'id' field.
   * @return 信号唯一ID
   */
  public java.lang.String getId() {
    return id;
  }



  /**
   * Gets the value of the 'symbol' field.
   * @return 交易对，如BTCUSDT
   */
  public java.lang.String getSymbol() {
    return symbol;
  }



  /**
   * Gets the value of the 'strategy' field.
   * @return 策略名称
   */
  public java.lang.String getStrategy() {
    return strategy;
  }



  /**
   * Gets the value of the 'signalType' field.
   * @return 信号类型
   */
  public com.crypto.trading.trade.model.avro.SignalType getSignalType() {
    return signalType;
  }



  /**
   * Gets the value of the 'confidence' field.
   * @return 信号置信度，0.0-1.0
   */
  public double getConfidence() {
    return confidence;
  }



  /**
   * Gets the value of the 'price' field.
   * @return 目标价格，可选
   */
  public java.lang.Double getPrice() {
    return price;
  }



  /**
   * Gets the value of the 'suggestedQuantity' field.
   * @return 建议交易数量，可选
   */
  public java.lang.Double getSuggestedQuantity() {
    return suggestedQuantity;
  }



  /**
   * Gets the value of the 'stopLoss' field.
   * @return 止损价格，可选
   */
  public java.lang.Double getStopLoss() {
    return stopLoss;
  }



  /**
   * Gets the value of the 'takeProfit' field.
   * @return 止盈价格，可选
   */
  public java.lang.Double getTakeProfit() {
    return takeProfit;
  }



  /**
   * Gets the value of the 'orderType' field.
   * @return 订单类型
   */
  public com.crypto.trading.trade.model.avro.OrderType getOrderType() {
    return orderType;
  }



  /**
   * Gets the value of the 'timeInForce' field.
   * @return 订单有效期
   */
  public com.crypto.trading.trade.model.avro.TimeInForce getTimeInForce() {
    return timeInForce;
  }



  /**
   * Gets the value of the 'leverageLevel' field.
   * @return 杠杆倍数，可选
   */
  public java.lang.Integer getLeverageLevel() {
    return leverageLevel;
  }



  /**
   * Gets the value of the 'generatedTime' field.
   * @return 信号生成时间（毫秒时间戳）
   */
  public long getGeneratedTime() {
    return generatedTime;
  }



  /**
   * Gets the value of the 'additionalParams' field.
   * @return 额外参数，键值对形式
   */
  public java.util.Map<java.lang.String,java.lang.String> getAdditionalParams() {
    return additionalParams;
  }



  /**
   * Creates a new TradingSignal RecordBuilder.
   * @return A new TradingSignal RecordBuilder
   */
  public static com.crypto.trading.trade.model.avro.TradingSignal.Builder newBuilder() {
    return new com.crypto.trading.trade.model.avro.TradingSignal.Builder();
  }

  /**
   * Creates a new TradingSignal RecordBuilder by copying an existing Builder.
   * @param other The existing builder to copy.
   * @return A new TradingSignal RecordBuilder
   */
  public static com.crypto.trading.trade.model.avro.TradingSignal.Builder newBuilder(com.crypto.trading.trade.model.avro.TradingSignal.Builder other) {
    if (other == null) {
      return new com.crypto.trading.trade.model.avro.TradingSignal.Builder();
    } else {
      return new com.crypto.trading.trade.model.avro.TradingSignal.Builder(other);
    }
  }

  /**
   * Creates a new TradingSignal RecordBuilder by copying an existing TradingSignal instance.
   * @param other The existing instance to copy.
   * @return A new TradingSignal RecordBuilder
   */
  public static com.crypto.trading.trade.model.avro.TradingSignal.Builder newBuilder(com.crypto.trading.trade.model.avro.TradingSignal other) {
    if (other == null) {
      return new com.crypto.trading.trade.model.avro.TradingSignal.Builder();
    } else {
      return new com.crypto.trading.trade.model.avro.TradingSignal.Builder(other);
    }
  }

  /**
   * RecordBuilder for TradingSignal instances.
   */
  @org.apache.avro.specific.AvroGenerated
  public static class Builder extends org.apache.avro.specific.SpecificRecordBuilderBase<TradingSignal>
    implements org.apache.avro.data.RecordBuilder<TradingSignal> {

    /** 信号唯一ID */
    private java.lang.String id;
    /** 交易对，如BTCUSDT */
    private java.lang.String symbol;
    /** 策略名称 */
    private java.lang.String strategy;
    /** 信号类型 */
    private com.crypto.trading.trade.model.avro.SignalType signalType;
    /** 信号置信度，0.0-1.0 */
    private double confidence;
    /** 目标价格，可选 */
    private java.lang.Double price;
    /** 建议交易数量，可选 */
    private java.lang.Double suggestedQuantity;
    /** 止损价格，可选 */
    private java.lang.Double stopLoss;
    /** 止盈价格，可选 */
    private java.lang.Double takeProfit;
    /** 订单类型 */
    private com.crypto.trading.trade.model.avro.OrderType orderType;
    /** 订单有效期 */
    private com.crypto.trading.trade.model.avro.TimeInForce timeInForce;
    /** 杠杆倍数，可选 */
    private java.lang.Integer leverageLevel;
    /** 信号生成时间（毫秒时间戳） */
    private long generatedTime;
    /** 额外参数，键值对形式 */
    private java.util.Map<java.lang.String,java.lang.String> additionalParams;

    /** Creates a new Builder */
    private Builder() {
      super(SCHEMA$, MODEL$);
    }

    /**
     * Creates a Builder by copying an existing Builder.
     * @param other The existing Builder to copy.
     */
    private Builder(com.crypto.trading.trade.model.avro.TradingSignal.Builder other) {
      super(other);
      if (isValidValue(fields()[0], other.id)) {
        this.id = data().deepCopy(fields()[0].schema(), other.id);
        fieldSetFlags()[0] = other.fieldSetFlags()[0];
      }
      if (isValidValue(fields()[1], other.symbol)) {
        this.symbol = data().deepCopy(fields()[1].schema(), other.symbol);
        fieldSetFlags()[1] = other.fieldSetFlags()[1];
      }
      if (isValidValue(fields()[2], other.strategy)) {
        this.strategy = data().deepCopy(fields()[2].schema(), other.strategy);
        fieldSetFlags()[2] = other.fieldSetFlags()[2];
      }
      if (isValidValue(fields()[3], other.signalType)) {
        this.signalType = data().deepCopy(fields()[3].schema(), other.signalType);
        fieldSetFlags()[3] = other.fieldSetFlags()[3];
      }
      if (isValidValue(fields()[4], other.confidence)) {
        this.confidence = data().deepCopy(fields()[4].schema(), other.confidence);
        fieldSetFlags()[4] = other.fieldSetFlags()[4];
      }
      if (isValidValue(fields()[5], other.price)) {
        this.price = data().deepCopy(fields()[5].schema(), other.price);
        fieldSetFlags()[5] = other.fieldSetFlags()[5];
      }
      if (isValidValue(fields()[6], other.suggestedQuantity)) {
        this.suggestedQuantity = data().deepCopy(fields()[6].schema(), other.suggestedQuantity);
        fieldSetFlags()[6] = other.fieldSetFlags()[6];
      }
      if (isValidValue(fields()[7], other.stopLoss)) {
        this.stopLoss = data().deepCopy(fields()[7].schema(), other.stopLoss);
        fieldSetFlags()[7] = other.fieldSetFlags()[7];
      }
      if (isValidValue(fields()[8], other.takeProfit)) {
        this.takeProfit = data().deepCopy(fields()[8].schema(), other.takeProfit);
        fieldSetFlags()[8] = other.fieldSetFlags()[8];
      }
      if (isValidValue(fields()[9], other.orderType)) {
        this.orderType = data().deepCopy(fields()[9].schema(), other.orderType);
        fieldSetFlags()[9] = other.fieldSetFlags()[9];
      }
      if (isValidValue(fields()[10], other.timeInForce)) {
        this.timeInForce = data().deepCopy(fields()[10].schema(), other.timeInForce);
        fieldSetFlags()[10] = other.fieldSetFlags()[10];
      }
      if (isValidValue(fields()[11], other.leverageLevel)) {
        this.leverageLevel = data().deepCopy(fields()[11].schema(), other.leverageLevel);
        fieldSetFlags()[11] = other.fieldSetFlags()[11];
      }
      if (isValidValue(fields()[12], other.generatedTime)) {
        this.generatedTime = data().deepCopy(fields()[12].schema(), other.generatedTime);
        fieldSetFlags()[12] = other.fieldSetFlags()[12];
      }
      if (isValidValue(fields()[13], other.additionalParams)) {
        this.additionalParams = data().deepCopy(fields()[13].schema(), other.additionalParams);
        fieldSetFlags()[13] = other.fieldSetFlags()[13];
      }
    }

    /**
     * Creates a Builder by copying an existing TradingSignal instance
     * @param other The existing instance to copy.
     */
    private Builder(com.crypto.trading.trade.model.avro.TradingSignal other) {
      super(SCHEMA$, MODEL$);
      if (isValidValue(fields()[0], other.id)) {
        this.id = data().deepCopy(fields()[0].schema(), other.id);
        fieldSetFlags()[0] = true;
      }
      if (isValidValue(fields()[1], other.symbol)) {
        this.symbol = data().deepCopy(fields()[1].schema(), other.symbol);
        fieldSetFlags()[1] = true;
      }
      if (isValidValue(fields()[2], other.strategy)) {
        this.strategy = data().deepCopy(fields()[2].schema(), other.strategy);
        fieldSetFlags()[2] = true;
      }
      if (isValidValue(fields()[3], other.signalType)) {
        this.signalType = data().deepCopy(fields()[3].schema(), other.signalType);
        fieldSetFlags()[3] = true;
      }
      if (isValidValue(fields()[4], other.confidence)) {
        this.confidence = data().deepCopy(fields()[4].schema(), other.confidence);
        fieldSetFlags()[4] = true;
      }
      if (isValidValue(fields()[5], other.price)) {
        this.price = data().deepCopy(fields()[5].schema(), other.price);
        fieldSetFlags()[5] = true;
      }
      if (isValidValue(fields()[6], other.suggestedQuantity)) {
        this.suggestedQuantity = data().deepCopy(fields()[6].schema(), other.suggestedQuantity);
        fieldSetFlags()[6] = true;
      }
      if (isValidValue(fields()[7], other.stopLoss)) {
        this.stopLoss = data().deepCopy(fields()[7].schema(), other.stopLoss);
        fieldSetFlags()[7] = true;
      }
      if (isValidValue(fields()[8], other.takeProfit)) {
        this.takeProfit = data().deepCopy(fields()[8].schema(), other.takeProfit);
        fieldSetFlags()[8] = true;
      }
      if (isValidValue(fields()[9], other.orderType)) {
        this.orderType = data().deepCopy(fields()[9].schema(), other.orderType);
        fieldSetFlags()[9] = true;
      }
      if (isValidValue(fields()[10], other.timeInForce)) {
        this.timeInForce = data().deepCopy(fields()[10].schema(), other.timeInForce);
        fieldSetFlags()[10] = true;
      }
      if (isValidValue(fields()[11], other.leverageLevel)) {
        this.leverageLevel = data().deepCopy(fields()[11].schema(), other.leverageLevel);
        fieldSetFlags()[11] = true;
      }
      if (isValidValue(fields()[12], other.generatedTime)) {
        this.generatedTime = data().deepCopy(fields()[12].schema(), other.generatedTime);
        fieldSetFlags()[12] = true;
      }
      if (isValidValue(fields()[13], other.additionalParams)) {
        this.additionalParams = data().deepCopy(fields()[13].schema(), other.additionalParams);
        fieldSetFlags()[13] = true;
      }
    }

    /**
      * Gets the value of the 'id' field.
      * 信号唯一ID
      * @return The value.
      */
    public java.lang.String getId() {
      return id;
    }


    /**
      * Sets the value of the 'id' field.
      * 信号唯一ID
      * @param value The value of 'id'.
      * @return This builder.
      */
    public com.crypto.trading.trade.model.avro.TradingSignal.Builder setId(java.lang.String value) {
      validate(fields()[0], value);
      this.id = value;
      fieldSetFlags()[0] = true;
      return this;
    }

    /**
      * Checks whether the 'id' field has been set.
      * 信号唯一ID
      * @return True if the 'id' field has been set, false otherwise.
      */
    public boolean hasId() {
      return fieldSetFlags()[0];
    }


    /**
      * Clears the value of the 'id' field.
      * 信号唯一ID
      * @return This builder.
      */
    public com.crypto.trading.trade.model.avro.TradingSignal.Builder clearId() {
      id = null;
      fieldSetFlags()[0] = false;
      return this;
    }

    /**
      * Gets the value of the 'symbol' field.
      * 交易对，如BTCUSDT
      * @return The value.
      */
    public java.lang.String getSymbol() {
      return symbol;
    }


    /**
      * Sets the value of the 'symbol' field.
      * 交易对，如BTCUSDT
      * @param value The value of 'symbol'.
      * @return This builder.
      */
    public com.crypto.trading.trade.model.avro.TradingSignal.Builder setSymbol(java.lang.String value) {
      validate(fields()[1], value);
      this.symbol = value;
      fieldSetFlags()[1] = true;
      return this;
    }

    /**
      * Checks whether the 'symbol' field has been set.
      * 交易对，如BTCUSDT
      * @return True if the 'symbol' field has been set, false otherwise.
      */
    public boolean hasSymbol() {
      return fieldSetFlags()[1];
    }


    /**
      * Clears the value of the 'symbol' field.
      * 交易对，如BTCUSDT
      * @return This builder.
      */
    public com.crypto.trading.trade.model.avro.TradingSignal.Builder clearSymbol() {
      symbol = null;
      fieldSetFlags()[1] = false;
      return this;
    }

    /**
      * Gets the value of the 'strategy' field.
      * 策略名称
      * @return The value.
      */
    public java.lang.String getStrategy() {
      return strategy;
    }


    /**
      * Sets the value of the 'strategy' field.
      * 策略名称
      * @param value The value of 'strategy'.
      * @return This builder.
      */
    public com.crypto.trading.trade.model.avro.TradingSignal.Builder setStrategy(java.lang.String value) {
      validate(fields()[2], value);
      this.strategy = value;
      fieldSetFlags()[2] = true;
      return this;
    }

    /**
      * Checks whether the 'strategy' field has been set.
      * 策略名称
      * @return True if the 'strategy' field has been set, false otherwise.
      */
    public boolean hasStrategy() {
      return fieldSetFlags()[2];
    }


    /**
      * Clears the value of the 'strategy' field.
      * 策略名称
      * @return This builder.
      */
    public com.crypto.trading.trade.model.avro.TradingSignal.Builder clearStrategy() {
      strategy = null;
      fieldSetFlags()[2] = false;
      return this;
    }

    /**
      * Gets the value of the 'signalType' field.
      * 信号类型
      * @return The value.
      */
    public com.crypto.trading.trade.model.avro.SignalType getSignalType() {
      return signalType;
    }


    /**
      * Sets the value of the 'signalType' field.
      * 信号类型
      * @param value The value of 'signalType'.
      * @return This builder.
      */
    public com.crypto.trading.trade.model.avro.TradingSignal.Builder setSignalType(com.crypto.trading.trade.model.avro.SignalType value) {
      validate(fields()[3], value);
      this.signalType = value;
      fieldSetFlags()[3] = true;
      return this;
    }

    /**
      * Checks whether the 'signalType' field has been set.
      * 信号类型
      * @return True if the 'signalType' field has been set, false otherwise.
      */
    public boolean hasSignalType() {
      return fieldSetFlags()[3];
    }


    /**
      * Clears the value of the 'signalType' field.
      * 信号类型
      * @return This builder.
      */
    public com.crypto.trading.trade.model.avro.TradingSignal.Builder clearSignalType() {
      signalType = null;
      fieldSetFlags()[3] = false;
      return this;
    }

    /**
      * Gets the value of the 'confidence' field.
      * 信号置信度，0.0-1.0
      * @return The value.
      */
    public double getConfidence() {
      return confidence;
    }


    /**
      * Sets the value of the 'confidence' field.
      * 信号置信度，0.0-1.0
      * @param value The value of 'confidence'.
      * @return This builder.
      */
    public com.crypto.trading.trade.model.avro.TradingSignal.Builder setConfidence(double value) {
      validate(fields()[4], value);
      this.confidence = value;
      fieldSetFlags()[4] = true;
      return this;
    }

    /**
      * Checks whether the 'confidence' field has been set.
      * 信号置信度，0.0-1.0
      * @return True if the 'confidence' field has been set, false otherwise.
      */
    public boolean hasConfidence() {
      return fieldSetFlags()[4];
    }


    /**
      * Clears the value of the 'confidence' field.
      * 信号置信度，0.0-1.0
      * @return This builder.
      */
    public com.crypto.trading.trade.model.avro.TradingSignal.Builder clearConfidence() {
      fieldSetFlags()[4] = false;
      return this;
    }

    /**
      * Gets the value of the 'price' field.
      * 目标价格，可选
      * @return The value.
      */
    public java.lang.Double getPrice() {
      return price;
    }


    /**
      * Sets the value of the 'price' field.
      * 目标价格，可选
      * @param value The value of 'price'.
      * @return This builder.
      */
    public com.crypto.trading.trade.model.avro.TradingSignal.Builder setPrice(java.lang.Double value) {
      validate(fields()[5], value);
      this.price = value;
      fieldSetFlags()[5] = true;
      return this;
    }

    /**
      * Checks whether the 'price' field has been set.
      * 目标价格，可选
      * @return True if the 'price' field has been set, false otherwise.
      */
    public boolean hasPrice() {
      return fieldSetFlags()[5];
    }


    /**
      * Clears the value of the 'price' field.
      * 目标价格，可选
      * @return This builder.
      */
    public com.crypto.trading.trade.model.avro.TradingSignal.Builder clearPrice() {
      price = null;
      fieldSetFlags()[5] = false;
      return this;
    }

    /**
      * Gets the value of the 'suggestedQuantity' field.
      * 建议交易数量，可选
      * @return The value.
      */
    public java.lang.Double getSuggestedQuantity() {
      return suggestedQuantity;
    }


    /**
      * Sets the value of the 'suggestedQuantity' field.
      * 建议交易数量，可选
      * @param value The value of 'suggestedQuantity'.
      * @return This builder.
      */
    public com.crypto.trading.trade.model.avro.TradingSignal.Builder setSuggestedQuantity(java.lang.Double value) {
      validate(fields()[6], value);
      this.suggestedQuantity = value;
      fieldSetFlags()[6] = true;
      return this;
    }

    /**
      * Checks whether the 'suggestedQuantity' field has been set.
      * 建议交易数量，可选
      * @return True if the 'suggestedQuantity' field has been set, false otherwise.
      */
    public boolean hasSuggestedQuantity() {
      return fieldSetFlags()[6];
    }


    /**
      * Clears the value of the 'suggestedQuantity' field.
      * 建议交易数量，可选
      * @return This builder.
      */
    public com.crypto.trading.trade.model.avro.TradingSignal.Builder clearSuggestedQuantity() {
      suggestedQuantity = null;
      fieldSetFlags()[6] = false;
      return this;
    }

    /**
      * Gets the value of the 'stopLoss' field.
      * 止损价格，可选
      * @return The value.
      */
    public java.lang.Double getStopLoss() {
      return stopLoss;
    }


    /**
      * Sets the value of the 'stopLoss' field.
      * 止损价格，可选
      * @param value The value of 'stopLoss'.
      * @return This builder.
      */
    public com.crypto.trading.trade.model.avro.TradingSignal.Builder setStopLoss(java.lang.Double value) {
      validate(fields()[7], value);
      this.stopLoss = value;
      fieldSetFlags()[7] = true;
      return this;
    }

    /**
      * Checks whether the 'stopLoss' field has been set.
      * 止损价格，可选
      * @return True if the 'stopLoss' field has been set, false otherwise.
      */
    public boolean hasStopLoss() {
      return fieldSetFlags()[7];
    }


    /**
      * Clears the value of the 'stopLoss' field.
      * 止损价格，可选
      * @return This builder.
      */
    public com.crypto.trading.trade.model.avro.TradingSignal.Builder clearStopLoss() {
      stopLoss = null;
      fieldSetFlags()[7] = false;
      return this;
    }

    /**
      * Gets the value of the 'takeProfit' field.
      * 止盈价格，可选
      * @return The value.
      */
    public java.lang.Double getTakeProfit() {
      return takeProfit;
    }


    /**
      * Sets the value of the 'takeProfit' field.
      * 止盈价格，可选
      * @param value The value of 'takeProfit'.
      * @return This builder.
      */
    public com.crypto.trading.trade.model.avro.TradingSignal.Builder setTakeProfit(java.lang.Double value) {
      validate(fields()[8], value);
      this.takeProfit = value;
      fieldSetFlags()[8] = true;
      return this;
    }

    /**
      * Checks whether the 'takeProfit' field has been set.
      * 止盈价格，可选
      * @return True if the 'takeProfit' field has been set, false otherwise.
      */
    public boolean hasTakeProfit() {
      return fieldSetFlags()[8];
    }


    /**
      * Clears the value of the 'takeProfit' field.
      * 止盈价格，可选
      * @return This builder.
      */
    public com.crypto.trading.trade.model.avro.TradingSignal.Builder clearTakeProfit() {
      takeProfit = null;
      fieldSetFlags()[8] = false;
      return this;
    }

    /**
      * Gets the value of the 'orderType' field.
      * 订单类型
      * @return The value.
      */
    public com.crypto.trading.trade.model.avro.OrderType getOrderType() {
      return orderType;
    }


    /**
      * Sets the value of the 'orderType' field.
      * 订单类型
      * @param value The value of 'orderType'.
      * @return This builder.
      */
    public com.crypto.trading.trade.model.avro.TradingSignal.Builder setOrderType(com.crypto.trading.trade.model.avro.OrderType value) {
      validate(fields()[9], value);
      this.orderType = value;
      fieldSetFlags()[9] = true;
      return this;
    }

    /**
      * Checks whether the 'orderType' field has been set.
      * 订单类型
      * @return True if the 'orderType' field has been set, false otherwise.
      */
    public boolean hasOrderType() {
      return fieldSetFlags()[9];
    }


    /**
      * Clears the value of the 'orderType' field.
      * 订单类型
      * @return This builder.
      */
    public com.crypto.trading.trade.model.avro.TradingSignal.Builder clearOrderType() {
      orderType = null;
      fieldSetFlags()[9] = false;
      return this;
    }

    /**
      * Gets the value of the 'timeInForce' field.
      * 订单有效期
      * @return The value.
      */
    public com.crypto.trading.trade.model.avro.TimeInForce getTimeInForce() {
      return timeInForce;
    }


    /**
      * Sets the value of the 'timeInForce' field.
      * 订单有效期
      * @param value The value of 'timeInForce'.
      * @return This builder.
      */
    public com.crypto.trading.trade.model.avro.TradingSignal.Builder setTimeInForce(com.crypto.trading.trade.model.avro.TimeInForce value) {
      validate(fields()[10], value);
      this.timeInForce = value;
      fieldSetFlags()[10] = true;
      return this;
    }

    /**
      * Checks whether the 'timeInForce' field has been set.
      * 订单有效期
      * @return True if the 'timeInForce' field has been set, false otherwise.
      */
    public boolean hasTimeInForce() {
      return fieldSetFlags()[10];
    }


    /**
      * Clears the value of the 'timeInForce' field.
      * 订单有效期
      * @return This builder.
      */
    public com.crypto.trading.trade.model.avro.TradingSignal.Builder clearTimeInForce() {
      timeInForce = null;
      fieldSetFlags()[10] = false;
      return this;
    }

    /**
      * Gets the value of the 'leverageLevel' field.
      * 杠杆倍数，可选
      * @return The value.
      */
    public java.lang.Integer getLeverageLevel() {
      return leverageLevel;
    }


    /**
      * Sets the value of the 'leverageLevel' field.
      * 杠杆倍数，可选
      * @param value The value of 'leverageLevel'.
      * @return This builder.
      */
    public com.crypto.trading.trade.model.avro.TradingSignal.Builder setLeverageLevel(java.lang.Integer value) {
      validate(fields()[11], value);
      this.leverageLevel = value;
      fieldSetFlags()[11] = true;
      return this;
    }

    /**
      * Checks whether the 'leverageLevel' field has been set.
      * 杠杆倍数，可选
      * @return True if the 'leverageLevel' field has been set, false otherwise.
      */
    public boolean hasLeverageLevel() {
      return fieldSetFlags()[11];
    }


    /**
      * Clears the value of the 'leverageLevel' field.
      * 杠杆倍数，可选
      * @return This builder.
      */
    public com.crypto.trading.trade.model.avro.TradingSignal.Builder clearLeverageLevel() {
      leverageLevel = null;
      fieldSetFlags()[11] = false;
      return this;
    }

    /**
      * Gets the value of the 'generatedTime' field.
      * 信号生成时间（毫秒时间戳）
      * @return The value.
      */
    public long getGeneratedTime() {
      return generatedTime;
    }


    /**
      * Sets the value of the 'generatedTime' field.
      * 信号生成时间（毫秒时间戳）
      * @param value The value of 'generatedTime'.
      * @return This builder.
      */
    public com.crypto.trading.trade.model.avro.TradingSignal.Builder setGeneratedTime(long value) {
      validate(fields()[12], value);
      this.generatedTime = value;
      fieldSetFlags()[12] = true;
      return this;
    }

    /**
      * Checks whether the 'generatedTime' field has been set.
      * 信号生成时间（毫秒时间戳）
      * @return True if the 'generatedTime' field has been set, false otherwise.
      */
    public boolean hasGeneratedTime() {
      return fieldSetFlags()[12];
    }


    /**
      * Clears the value of the 'generatedTime' field.
      * 信号生成时间（毫秒时间戳）
      * @return This builder.
      */
    public com.crypto.trading.trade.model.avro.TradingSignal.Builder clearGeneratedTime() {
      fieldSetFlags()[12] = false;
      return this;
    }

    /**
      * Gets the value of the 'additionalParams' field.
      * 额外参数，键值对形式
      * @return The value.
      */
    public java.util.Map<java.lang.String,java.lang.String> getAdditionalParams() {
      return additionalParams;
    }


    /**
      * Sets the value of the 'additionalParams' field.
      * 额外参数，键值对形式
      * @param value The value of 'additionalParams'.
      * @return This builder.
      */
    public com.crypto.trading.trade.model.avro.TradingSignal.Builder setAdditionalParams(java.util.Map<java.lang.String,java.lang.String> value) {
      validate(fields()[13], value);
      this.additionalParams = value;
      fieldSetFlags()[13] = true;
      return this;
    }

    /**
      * Checks whether the 'additionalParams' field has been set.
      * 额外参数，键值对形式
      * @return True if the 'additionalParams' field has been set, false otherwise.
      */
    public boolean hasAdditionalParams() {
      return fieldSetFlags()[13];
    }


    /**
      * Clears the value of the 'additionalParams' field.
      * 额外参数，键值对形式
      * @return This builder.
      */
    public com.crypto.trading.trade.model.avro.TradingSignal.Builder clearAdditionalParams() {
      additionalParams = null;
      fieldSetFlags()[13] = false;
      return this;
    }

    @Override
    @SuppressWarnings("unchecked")
    public TradingSignal build() {
      try {
        TradingSignal record = new TradingSignal();
        record.id = fieldSetFlags()[0] ? this.id : (java.lang.String) defaultValue(fields()[0]);
        record.symbol = fieldSetFlags()[1] ? this.symbol : (java.lang.String) defaultValue(fields()[1]);
        record.strategy = fieldSetFlags()[2] ? this.strategy : (java.lang.String) defaultValue(fields()[2]);
        record.signalType = fieldSetFlags()[3] ? this.signalType : (com.crypto.trading.trade.model.avro.SignalType) defaultValue(fields()[3]);
        record.confidence = fieldSetFlags()[4] ? this.confidence : (java.lang.Double) defaultValue(fields()[4]);
        record.price = fieldSetFlags()[5] ? this.price : (java.lang.Double) defaultValue(fields()[5]);
        record.suggestedQuantity = fieldSetFlags()[6] ? this.suggestedQuantity : (java.lang.Double) defaultValue(fields()[6]);
        record.stopLoss = fieldSetFlags()[7] ? this.stopLoss : (java.lang.Double) defaultValue(fields()[7]);
        record.takeProfit = fieldSetFlags()[8] ? this.takeProfit : (java.lang.Double) defaultValue(fields()[8]);
        record.orderType = fieldSetFlags()[9] ? this.orderType : (com.crypto.trading.trade.model.avro.OrderType) defaultValue(fields()[9]);
        record.timeInForce = fieldSetFlags()[10] ? this.timeInForce : (com.crypto.trading.trade.model.avro.TimeInForce) defaultValue(fields()[10]);
        record.leverageLevel = fieldSetFlags()[11] ? this.leverageLevel : (java.lang.Integer) defaultValue(fields()[11]);
        record.generatedTime = fieldSetFlags()[12] ? this.generatedTime : (java.lang.Long) defaultValue(fields()[12]);
        record.additionalParams = fieldSetFlags()[13] ? this.additionalParams : (java.util.Map<java.lang.String,java.lang.String>) defaultValue(fields()[13]);
        return record;
      } catch (org.apache.avro.AvroMissingFieldException e) {
        throw e;
      } catch (java.lang.Exception e) {
        throw new org.apache.avro.AvroRuntimeException(e);
      }
    }
  }

  @SuppressWarnings("unchecked")
  private static final org.apache.avro.io.DatumWriter<TradingSignal>
    WRITER$ = (org.apache.avro.io.DatumWriter<TradingSignal>)MODEL$.createDatumWriter(SCHEMA$);

  @Override public void writeExternal(java.io.ObjectOutput out)
    throws java.io.IOException {
    WRITER$.write(this, SpecificData.getEncoder(out));
  }

  @SuppressWarnings("unchecked")
  private static final org.apache.avro.io.DatumReader<TradingSignal>
    READER$ = (org.apache.avro.io.DatumReader<TradingSignal>)MODEL$.createDatumReader(SCHEMA$);

  @Override public void readExternal(java.io.ObjectInput in)
    throws java.io.IOException {
    READER$.read(this, SpecificData.getDecoder(in));
  }

  @Override protected boolean hasCustomCoders() { return true; }

  @Override public void customEncode(org.apache.avro.io.Encoder out)
    throws java.io.IOException
  {
    out.writeString(this.id);

    out.writeString(this.symbol);

    out.writeString(this.strategy);

    out.writeEnum(this.signalType.ordinal());

    out.writeDouble(this.confidence);

    if (this.price == null) {
      out.writeIndex(0);
      out.writeNull();
    } else {
      out.writeIndex(1);
      out.writeDouble(this.price);
    }

    if (this.suggestedQuantity == null) {
      out.writeIndex(0);
      out.writeNull();
    } else {
      out.writeIndex(1);
      out.writeDouble(this.suggestedQuantity);
    }

    if (this.stopLoss == null) {
      out.writeIndex(0);
      out.writeNull();
    } else {
      out.writeIndex(1);
      out.writeDouble(this.stopLoss);
    }

    if (this.takeProfit == null) {
      out.writeIndex(0);
      out.writeNull();
    } else {
      out.writeIndex(1);
      out.writeDouble(this.takeProfit);
    }

    out.writeEnum(this.orderType.ordinal());

    out.writeEnum(this.timeInForce.ordinal());

    if (this.leverageLevel == null) {
      out.writeIndex(0);
      out.writeNull();
    } else {
      out.writeIndex(1);
      out.writeInt(this.leverageLevel);
    }

    out.writeLong(this.generatedTime);

    long size0 = this.additionalParams.size();
    out.writeMapStart();
    out.setItemCount(size0);
    long actualSize0 = 0;
    for (java.util.Map.Entry<java.lang.String, java.lang.String> e0: this.additionalParams.entrySet()) {
      actualSize0++;
      out.startItem();
      out.writeString(e0.getKey());
      java.lang.String v0 = e0.getValue();
      out.writeString(v0);
    }
    out.writeMapEnd();
    if (actualSize0 != size0)
      throw new java.util.ConcurrentModificationException("Map-size written was " + size0 + ", but element count was " + actualSize0 + ".");

  }

  @Override public void customDecode(org.apache.avro.io.ResolvingDecoder in)
    throws java.io.IOException
  {
    org.apache.avro.Schema.Field[] fieldOrder = in.readFieldOrderIfDiff();
    if (fieldOrder == null) {
      this.id = in.readString();

      this.symbol = in.readString();

      this.strategy = in.readString();

      this.signalType = com.crypto.trading.trade.model.avro.SignalType.values()[in.readEnum()];

      this.confidence = in.readDouble();

      if (in.readIndex() != 1) {
        in.readNull();
        this.price = null;
      } else {
        this.price = in.readDouble();
      }

      if (in.readIndex() != 1) {
        in.readNull();
        this.suggestedQuantity = null;
      } else {
        this.suggestedQuantity = in.readDouble();
      }

      if (in.readIndex() != 1) {
        in.readNull();
        this.stopLoss = null;
      } else {
        this.stopLoss = in.readDouble();
      }

      if (in.readIndex() != 1) {
        in.readNull();
        this.takeProfit = null;
      } else {
        this.takeProfit = in.readDouble();
      }

      this.orderType = com.crypto.trading.trade.model.avro.OrderType.values()[in.readEnum()];

      this.timeInForce = com.crypto.trading.trade.model.avro.TimeInForce.values()[in.readEnum()];

      if (in.readIndex() != 1) {
        in.readNull();
        this.leverageLevel = null;
      } else {
        this.leverageLevel = in.readInt();
      }

      this.generatedTime = in.readLong();

      long size0 = in.readMapStart();
      java.util.Map<java.lang.String,java.lang.String> m0 = this.additionalParams; // Need fresh name due to limitation of macro system
      if (m0 == null) {
        m0 = new java.util.HashMap<java.lang.String,java.lang.String>((int)size0);
        this.additionalParams = m0;
      } else m0.clear();
      for ( ; 0 < size0; size0 = in.mapNext()) {
        for ( ; size0 != 0; size0--) {
          java.lang.String k0 = null;
          k0 = in.readString();
          java.lang.String v0 = null;
          v0 = in.readString();
          m0.put(k0, v0);
        }
      }

    } else {
      for (int i = 0; i < 14; i++) {
        switch (fieldOrder[i].pos()) {
        case 0:
          this.id = in.readString();
          break;

        case 1:
          this.symbol = in.readString();
          break;

        case 2:
          this.strategy = in.readString();
          break;

        case 3:
          this.signalType = com.crypto.trading.trade.model.avro.SignalType.values()[in.readEnum()];
          break;

        case 4:
          this.confidence = in.readDouble();
          break;

        case 5:
          if (in.readIndex() != 1) {
            in.readNull();
            this.price = null;
          } else {
            this.price = in.readDouble();
          }
          break;

        case 6:
          if (in.readIndex() != 1) {
            in.readNull();
            this.suggestedQuantity = null;
          } else {
            this.suggestedQuantity = in.readDouble();
          }
          break;

        case 7:
          if (in.readIndex() != 1) {
            in.readNull();
            this.stopLoss = null;
          } else {
            this.stopLoss = in.readDouble();
          }
          break;

        case 8:
          if (in.readIndex() != 1) {
            in.readNull();
            this.takeProfit = null;
          } else {
            this.takeProfit = in.readDouble();
          }
          break;

        case 9:
          this.orderType = com.crypto.trading.trade.model.avro.OrderType.values()[in.readEnum()];
          break;

        case 10:
          this.timeInForce = com.crypto.trading.trade.model.avro.TimeInForce.values()[in.readEnum()];
          break;

        case 11:
          if (in.readIndex() != 1) {
            in.readNull();
            this.leverageLevel = null;
          } else {
            this.leverageLevel = in.readInt();
          }
          break;

        case 12:
          this.generatedTime = in.readLong();
          break;

        case 13:
          long size0 = in.readMapStart();
          java.util.Map<java.lang.String,java.lang.String> m0 = this.additionalParams; // Need fresh name due to limitation of macro system
          if (m0 == null) {
            m0 = new java.util.HashMap<java.lang.String,java.lang.String>((int)size0);
            this.additionalParams = m0;
          } else m0.clear();
          for ( ; 0 < size0; size0 = in.mapNext()) {
            for ( ; size0 != 0; size0--) {
              java.lang.String k0 = null;
              k0 = in.readString();
              java.lang.String v0 = null;
              v0 = in.readString();
              m0.put(k0, v0);
            }
          }
          break;

        default:
          throw new java.io.IOException("Corrupt ResolvingDecoder.");
        }
      }
    }
  }
}










