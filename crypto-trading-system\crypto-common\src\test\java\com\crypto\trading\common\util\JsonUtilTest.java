package com.crypto.trading.common.util;

import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * JsonUtil工具类的单元测试
 *
 * <AUTHOR>
 */
public class JsonUtilTest {

    /**
     * 测试对象转JSON字符串
     */
    @Test
    public void testToJsonString() {
        TestUser user = new TestUser("张三", 25);
        String json = JsonUtil.toJsonString(user);
        assertNotNull(json);
        assertTrue(json.contains("\"name\":\"张三\""));
        assertTrue(json.contains("\"age\":25"));
    }

    /**
     * 测试对象转格式化JSON字符串
     */
    @Test
    public void testToPrettyJsonString() {
        TestUser user = new TestUser("李四", 30);
        String json = JsonUtil.toPrettyJsonString(user);
        assertNotNull(json);
        assertTrue(json.contains("\"name\""));
        assertTrue(json.contains("\"李四\""));
        assertTrue(json.contains("\"age\""));
        assertTrue(json.contains("30"));
    }

    /**
     * 测试JSON字符串解析为对象
     */
    @Test
    public void testParseObject() {
        String json = "{\"name\":\"王五\",\"age\":35}";
        TestUser user = JsonUtil.parseObject(json, TestUser.class);
        assertNotNull(user);
        assertEquals("王五", user.getName());
        assertEquals(35, user.getAge());
    }

    /**
     * 测试JSON字符串解析为对象（使用TypeReference）
     */
    @Test
    public void testParseObjectWithTypeReference() {
        String json = "{\"name\":\"赵六\",\"age\":40}";
        TestUser user = JsonUtil.parseObject(json, new TypeReference<TestUser>() {});
        assertNotNull(user);
        assertEquals("赵六", user.getName());
        assertEquals(40, user.getAge());
    }

    /**
     * 测试JSON字符串解析为List
     */
    @Test
    public void testParseList() {
        String json = "[{\"name\":\"张三\",\"age\":25},{\"name\":\"李四\",\"age\":30}]";
        List<TestUser> users = JsonUtil.parseList(json, TestUser.class);
        assertNotNull(users);
        assertEquals(2, users.size());
        assertEquals("张三", users.get(0).getName());
        assertEquals(25, users.get(0).getAge());
        assertEquals("李四", users.get(1).getName());
        assertEquals(30, users.get(1).getAge());
    }

    /**
     * 测试JSON字符串解析为Map
     */
    @Test
    public void testParseMap() {
        String json = "{\"张三\":25,\"李四\":30}";
        Map<String, Integer> map = JsonUtil.parseMap(json, String.class, Integer.class);
        assertNotNull(map);
        assertEquals(2, map.size());
        assertEquals(Integer.valueOf(25), map.get("张三"));
        assertEquals(Integer.valueOf(30), map.get("李四"));
    }

    /**
     * 测试JSON字符串解析为JSONObject
     */
    @Test
    public void testParseJsonObject() {
        String json = "{\"name\":\"张三\",\"age\":25,\"hobbies\":[\"读书\",\"游泳\"]}";
        JSONObject jsonObject = JsonUtil.parseJsonObject(json);
        assertNotNull(jsonObject);
        assertEquals("张三", jsonObject.getString("name"));
        assertEquals(25, jsonObject.getIntValue("age"));
        assertTrue(jsonObject.containsKey("hobbies"));
        assertEquals(2, jsonObject.getJSONArray("hobbies").size());
        assertEquals("读书", jsonObject.getJSONArray("hobbies").getString(0));
        assertEquals("游泳", jsonObject.getJSONArray("hobbies").getString(1));
    }

    /**
     * 测试对象转换
     */
    @Test
    public void testConvertObject() {
        TestUser sourceUser = new TestUser("张三", 25);
        TestUser targetUser = JsonUtil.convertObject(sourceUser, TestUser.class);
        assertNotNull(targetUser);
        assertEquals("张三", targetUser.getName());
        assertEquals(25, targetUser.getAge());
    }

    /**
     * 测试使用FastJSON进行序列化
     */
    @Test
    public void testToJsonStringByFastJson() {
        TestUser user = new TestUser("张三", 25);
        String json = JsonUtil.toJsonStringByFastJson(user);
        assertNotNull(json);
        assertTrue(json.contains("\"name\":\"张三\""));
        assertTrue(json.contains("\"age\":25"));
    }

    /**
     * 测试使用FastJSON进行反序列化
     */
    @Test
    public void testParseObjectByFastJson() {
        String json = "{\"name\":\"王五\",\"age\":35}";
        TestUser user = JsonUtil.parseObjectByFastJson(json, TestUser.class);
        assertNotNull(user);
        assertEquals("王五", user.getName());
        assertEquals(35, user.getAge());
    }

    /**
     * 测试用户类
     */
    static class TestUser {
        private String name;
        private int age;

        public TestUser() {
        }

        public TestUser(String name, int age) {
            this.name = name;
            this.age = age;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getAge() {
            return age;
        }

        public void setAge(int age) {
            this.age = age;
        }
    }
}