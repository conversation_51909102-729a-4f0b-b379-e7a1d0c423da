package com.crypto.trading.sdk.response.model;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * WebSocket消息模型测试类
 */
public class WebSocketMessageTest {

    @Test
    public void testDefaultConstructor() {
        // 测试默认构造函数
        WebSocketMessage<String> message = new WebSocketMessage<>();
        
        // 验证默认值
        assertNull(message.getEventType());
        assertNull(message.getData());
        assertEquals(0, message.getEventTime());
        assertNull(message.getSymbol());
    }

    @Test
    public void testConstructorWithParameters() {
        // 测试带参数的构造函数
        String eventType = "kline";
        long eventTime = 1618560000000L;
        String symbol = "BTCUSDT";
        String data = "{\"k\":{\"t\":1618560000000,\"T\":1618561200000,\"s\":\"BTCUSDT\",\"i\":\"1m\"}}";
        
        WebSocketMessage<String> message = new WebSocketMessage<>(eventType, eventTime, symbol, data);
        
        // 验证参数
        assertEquals(eventType, message.getEventType());
        assertEquals(data, message.getData());
        assertEquals(eventTime, message.getEventTime());
        assertEquals(symbol, message.getSymbol());
    }

    @Test
    public void testSettersAndGetters() {
        // 测试设置器和获取器
        WebSocketMessage<String> message = new WebSocketMessage<>();
        
        // 设置属性
        String eventType = "kline";
        long eventTime = 1618560000000L;
        String symbol = "BTCUSDT";
        String data = "{\"k\":{\"t\":1618560000000,\"T\":1618561200000,\"s\":\"BTCUSDT\",\"i\":\"1m\"}}";
        
        message.setEventType(eventType);
        message.setData(data);
        message.setEventTime(eventTime);
        message.setSymbol(symbol);
        
        // 验证属性
        assertEquals(eventType, message.getEventType());
        assertEquals(data, message.getData());
        assertEquals(eventTime, message.getEventTime());
        assertEquals(symbol, message.getSymbol());
    }

    @Test
    public void testToString() {
        // 测试toString方法
        String eventType = "kline";
        long eventTime = 1618560000000L;
        String symbol = "BTCUSDT";
        String data = "{\"k\":{\"t\":1618560000000,\"T\":1618561200000,\"s\":\"BTCUSDT\",\"i\":\"1m\"}}";
        
        WebSocketMessage<String> message = new WebSocketMessage<>(eventType, eventTime, symbol, data);
        
        String toString = message.toString();
        assertNotNull(toString);
        assertTrue(toString.contains(eventType));
        assertTrue(toString.contains(symbol));
        assertTrue(toString.contains(String.valueOf(eventTime)));
    }

    @Test
    public void testEquals() {
        // 测试equals方法
        WebSocketMessage<String> message1 = new WebSocketMessage<>("kline", 1618560000000L, "BTCUSDT", "data1");
        WebSocketMessage<String> message2 = new WebSocketMessage<>("kline", 1618560000000L, "BTCUSDT", "data1");
        WebSocketMessage<String> message3 = new WebSocketMessage<>("trade", 1618560000000L, "ETHUSDT", "data2");
        
        // 自反性
        assertEquals(message1, message1);
        
        // 对称性
        assertEquals(message1, message2);
        assertEquals(message2, message1);
        
        // 不相等的情况
        assertNotEquals(message1, message3);
        assertNotEquals(message1, null);
        assertNotEquals(message1, "string");
    }

    @Test
    public void testHashCode() {
        // 测试hashCode方法
        WebSocketMessage<String> message1 = new WebSocketMessage<>("kline", 1618560000000L, "BTCUSDT", "data1");
        WebSocketMessage<String> message2 = new WebSocketMessage<>("kline", 1618560000000L, "BTCUSDT", "data1");
        
        // 相等的对象应该有相同的哈希码
        assertEquals(message1.hashCode(), message2.hashCode());
    }
} 