package com.binance.connector.futures.client.impl;

import com.binance.connector.futures.client.FuturesClient;
import com.binance.connector.futures.client.exceptions.BinanceClientException;
import com.binance.connector.futures.client.exceptions.BinanceConnectorException;
import com.binance.connector.futures.client.utils.OkHttpUtils;
import com.binance.connector.futures.client.utils.ParameterChecker;
import com.binance.connector.futures.client.utils.RequestHandler;
import com.binance.connector.futures.client.utils.Signature;
import okhttp3.OkHttpClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.LinkedHashMap;

public abstract class FuturesClientImpl implements FuturesClient {
    private final String product; // "cm_futures" or "um_futures"
    private final String apiKey;
    private final String secretKey;
    private final String baseUrl;
    private boolean showLimitUsage = false;
    private OkHttpClient client = OkHttpUtils.getOkHttpClient(); // Default client
    private final Signature signature;
    private static final Logger logger = LoggerFactory.getLogger(FuturesClientImpl.class);


    public FuturesClientImpl(String product, String apiKey, String secretKey, String baseUrl) {
        this.product = product;
        this.apiKey = apiKey;
        this.secretKey = secretKey;
        this.baseUrl = baseUrl;
        this.signature = new Signature(this.secretKey);
    }

    public FuturesClientImpl(String product, String apiKey, String secretKey, String baseUrl, OkHttpClient client) {
        this(product, apiKey, secretKey, baseUrl);
        this.client = client;
    }

    public FuturesClientImpl(String product, String baseUrl) {
        this.product = product;
        this.apiKey = null;
        this.secretKey = null;
        this.baseUrl = baseUrl;
        this.signature = new Signature(this.secretKey);
    }

    public FuturesClientImpl(String product, String baseUrl, OkHttpClient client) {
        this(product, baseUrl);
        this.client = client;
    }

    public void setShowLimitUsage(boolean showLimitUsage) {
        this.showLimitUsage = showLimitUsage;
    }

    private RequestHandler getRequestHandler() {
        return new RequestHandler(this.apiKey, this.signature, this.client);
    }

    private String getFullUrl(String endpoint) {
        return this.baseUrl + endpoint;
    }

    private String sendRequest(String fullUrl, LinkedHashMap<String, Object> parameters, String httpMethod) {
        if (null == parameters) {
            parameters = new LinkedHashMap<>();
        }
        ParameterChecker.checkParameter(parameters, "timestamp", Long.class);
        parameters.put("timestamp", System.currentTimeMillis());

        RequestHandler requestHandler = getRequestHandler();
        try {
            String response;
            switch (httpMethod) {
                case "GET":
                    response = requestHandler.sendApiRequest(fullUrl, httpMethod, parameters, this.showLimitUsage);
                    break;
                case "POST":
                    response = requestHandler.sendApiRequest(fullUrl, httpMethod, parameters, this.showLimitUsage);
                    break;
                case "PUT":
                    response = requestHandler.sendApiRequest(fullUrl, httpMethod, parameters, this.showLimitUsage);
                    break;
                case "DELETE":
                    response = requestHandler.sendApiRequest(fullUrl, httpMethod, parameters, this.showLimitUsage);
                    break;
                default:
                    throw new BinanceConnectorException(String.format("Invalid HTTP method: %s", httpMethod));
            }
            return response;
        } catch (BinanceClientException e) {
            logger.error("Error sending request: {}", e.getMessage());
            throw e;
        }
    }

    protected String publicRequest(String endpoint, LinkedHashMap<String, Object> parameters) {
        ParameterChecker.checkParameter(parameters, "symbol", String.class);
        return sendRequest(getFullUrl(endpoint), parameters, "GET");
    }

    protected String publicRequest(String endpoint, LinkedHashMap<String, Object> parameters, String httpMethod) {
        return sendRequest(getFullUrl(endpoint), parameters, httpMethod);
    }

    protected String signedRequest(String endpoint, LinkedHashMap<String, Object> parameters, String httpMethod) {
        if (this.apiKey == null || this.secretKey == null) {
            throw new BinanceConnectorException("API key and secret key are required for signed requests.");
        }
        return sendRequest(getFullUrl(endpoint), parameters, httpMethod);
    }

    public String getProduct() {
        return product;
    }
}