"""
系统健康监控模块

提供crypto_ml_strategy项目的系统健康监控功能，包括组件健康检查、
性能指标收集、告警管理和系统状态监控。

Author: Crypto ML Strategy Team
Date: 2024-12-20
Version: 1.0.0
"""

import asyncio
import psutil
import time
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Dict, List, Optional, Callable, Set
from loguru import logger

from .error_handler_core import ErrorSeverity, ErrorContext, CryptoMLException, ErrorCode


class HealthStatus(Enum):
    """健康状态枚举"""
    HEALTHY = "healthy"
    WARNING = "warning"
    CRITICAL = "critical"
    UNKNOWN = "unknown"


class AlertLevel(Enum):
    """告警级别枚举"""
    INFO = "info"
    WARNING = "warning"
    CRITICAL = "critical"
    EMERGENCY = "emergency"


@dataclass
class HealthCheckResult:
    """健康检查结果"""
    component: str
    status: HealthStatus
    message: str
    timestamp: datetime = field(default_factory=datetime.now)
    metrics: Dict[str, Any] = field(default_factory=dict)
    details: Dict[str, Any] = field(default_factory=dict)
    
    def is_healthy(self) -> bool:
        """判断是否健康"""
        return self.status == HealthStatus.HEALTHY
    
    def needs_attention(self) -> bool:
        """判断是否需要关注"""
        return self.status in [HealthStatus.WARNING, HealthStatus.CRITICAL]


@dataclass
class SystemMetrics:
    """系统指标"""
    timestamp: datetime = field(default_factory=datetime.now)
    cpu_usage: float = 0.0
    memory_usage: float = 0.0
    disk_usage: float = 0.0
    network_io: Dict[str, int] = field(default_factory=dict)
    process_count: int = 0
    thread_count: int = 0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "timestamp": self.timestamp.isoformat(),
            "cpu_usage": self.cpu_usage,
            "memory_usage": self.memory_usage,
            "disk_usage": self.disk_usage,
            "network_io": self.network_io,
            "process_count": self.process_count,
            "thread_count": self.thread_count
        }


@dataclass
class Alert:
    """告警信息"""
    id: str
    level: AlertLevel
    component: str
    message: str
    timestamp: datetime = field(default_factory=datetime.now)
    resolved: bool = False
    resolved_at: Optional[datetime] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def resolve(self) -> None:
        """解决告警"""
        self.resolved = True
        self.resolved_at = datetime.now()


class HealthCheck(ABC):
    """健康检查抽象基类"""
    
    @abstractmethod
    async def check_health(self) -> HealthCheckResult:
        """执行健康检查"""
        pass
    
    @abstractmethod
    def get_component_name(self) -> str:
        """获取组件名称"""
        pass
    
    @abstractmethod
    def get_check_interval(self) -> int:
        """获取检查间隔（秒）"""
        pass


class SystemResourceHealthCheck(HealthCheck):
    """系统资源健康检查"""
    
    def __init__(
        self,
        cpu_threshold: float = 80.0,
        memory_threshold: float = 85.0,
        disk_threshold: float = 90.0
    ):
        self.cpu_threshold = cpu_threshold
        self.memory_threshold = memory_threshold
        self.disk_threshold = disk_threshold
    
    async def check_health(self) -> HealthCheckResult:
        """检查系统资源健康状态"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # 内存使用率
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            
            # 磁盘使用率
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            
            # 判断健康状态
            status = HealthStatus.HEALTHY
            messages = []
            
            if cpu_percent > self.cpu_threshold:
                status = HealthStatus.CRITICAL if cpu_percent > 95 else HealthStatus.WARNING
                messages.append(f"CPU使用率过高: {cpu_percent:.1f}%")
            
            if memory_percent > self.memory_threshold:
                status = HealthStatus.CRITICAL if memory_percent > 95 else HealthStatus.WARNING
                messages.append(f"内存使用率过高: {memory_percent:.1f}%")
            
            if disk_percent > self.disk_threshold:
                status = HealthStatus.CRITICAL if disk_percent > 98 else HealthStatus.WARNING
                messages.append(f"磁盘使用率过高: {disk_percent:.1f}%")
            
            message = "; ".join(messages) if messages else "系统资源正常"
            
            return HealthCheckResult(
                component="system_resources",
                status=status,
                message=message,
                metrics={
                    "cpu_usage": cpu_percent,
                    "memory_usage": memory_percent,
                    "disk_usage": disk_percent
                }
            )
            
        except Exception as e:
            logger.error(f"系统资源健康检查失败: {e}")
            return HealthCheckResult(
                component="system_resources",
                status=HealthStatus.UNKNOWN,
                message=f"健康检查失败: {str(e)}"
            )
    
    def get_component_name(self) -> str:
        return "system_resources"
    
    def get_check_interval(self) -> int:
        return 30  # 30秒检查一次


class ComponentHealthCheck(HealthCheck):
    """组件健康检查"""
    
    def __init__(
        self,
        component_name: str,
        health_check_func: Callable[[], bool],
        check_interval: int = 60
    ):
        self.component_name = component_name
        self.health_check_func = health_check_func
        self.check_interval = check_interval
    
    async def check_health(self) -> HealthCheckResult:
        """检查组件健康状态"""
        try:
            is_healthy = self.health_check_func()
            status = HealthStatus.HEALTHY if is_healthy else HealthStatus.CRITICAL
            message = "组件正常" if is_healthy else "组件异常"
            
            return HealthCheckResult(
                component=self.component_name,
                status=status,
                message=message
            )
            
        except Exception as e:
            logger.error(f"组件 {self.component_name} 健康检查失败: {e}")
            return HealthCheckResult(
                component=self.component_name,
                status=HealthStatus.UNKNOWN,
                message=f"健康检查失败: {str(e)}"
            )
    
    def get_component_name(self) -> str:
        return self.component_name
    
    def get_check_interval(self) -> int:
        return self.check_interval


class MetricsCollector:
    """指标收集器"""
    
    def __init__(self, max_history: int = 1000):
        self.max_history = max_history
        self.metrics_history: List[SystemMetrics] = []
        self.custom_metrics: Dict[str, List[Dict[str, Any]]] = {}
    
    def collect_system_metrics(self) -> SystemMetrics:
        """收集系统指标"""
        try:
            # CPU使用率
            cpu_usage = psutil.cpu_percent(interval=0.1)
            
            # 内存使用率
            memory = psutil.virtual_memory()
            memory_usage = memory.percent
            
            # 磁盘使用率
            disk = psutil.disk_usage('/')
            disk_usage = (disk.used / disk.total) * 100
            
            # 网络IO
            network_io = psutil.net_io_counters()._asdict()
            
            # 进程和线程数
            process_count = len(psutil.pids())
            
            metrics = SystemMetrics(
                cpu_usage=cpu_usage,
                memory_usage=memory_usage,
                disk_usage=disk_usage,
                network_io=network_io,
                process_count=process_count,
                thread_count=psutil.cpu_count()
            )
            
            # 保存到历史记录
            self.metrics_history.append(metrics)
            if len(self.metrics_history) > self.max_history:
                self.metrics_history.pop(0)
            
            return metrics
            
        except Exception as e:
            logger.error(f"收集系统指标失败: {e}")
            return SystemMetrics()
    
    def record_custom_metric(
        self,
        metric_name: str,
        value: Any,
        metadata: Optional[Dict[str, Any]] = None
    ) -> None:
        """记录自定义指标"""
        if metric_name not in self.custom_metrics:
            self.custom_metrics[metric_name] = []
        
        metric_record = {
            "timestamp": datetime.now().isoformat(),
            "value": value,
            "metadata": metadata or {}
        }
        
        self.custom_metrics[metric_name].append(metric_record)
        
        # 限制历史记录数量
        if len(self.custom_metrics[metric_name]) > self.max_history:
            self.custom_metrics[metric_name].pop(0)
    
    def get_metrics_summary(self) -> Dict[str, Any]:
        """获取指标摘要"""
        if not self.metrics_history:
            return {}
        
        recent_metrics = self.metrics_history[-10:]  # 最近10个指标
        
        return {
            "current_metrics": recent_metrics[-1].to_dict() if recent_metrics else {},
            "average_cpu": sum(m.cpu_usage for m in recent_metrics) / len(recent_metrics),
            "average_memory": sum(m.memory_usage for m in recent_metrics) / len(recent_metrics),
            "average_disk": sum(m.disk_usage for m in recent_metrics) / len(recent_metrics),
            "custom_metrics": {
                name: metrics[-5:] for name, metrics in self.custom_metrics.items()
            }
        }


class AlertManager:
    """告警管理器"""
    
    def __init__(self, max_alerts: int = 1000):
        self.max_alerts = max_alerts
        self.alerts: List[Alert] = []
        self.alert_handlers: List[Callable[[Alert], None]] = []
        self.suppression_rules: Dict[str, timedelta] = {}
        self.last_alert_times: Dict[str, datetime] = {}
    
    def add_alert_handler(self, handler: Callable[[Alert], None]) -> None:
        """添加告警处理器"""
        self.alert_handlers.append(handler)
    
    def set_suppression_rule(self, component: str, duration: timedelta) -> None:
        """设置告警抑制规则"""
        self.suppression_rules[component] = duration
    
    def create_alert(
        self,
        level: AlertLevel,
        component: str,
        message: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Optional[Alert]:
        """创建告警"""
        # 检查告警抑制
        suppression_key = f"{component}:{message}"
        if suppression_key in self.last_alert_times:
            last_time = self.last_alert_times[suppression_key]
            suppression_duration = self.suppression_rules.get(component, timedelta(minutes=5))
            
            if datetime.now() - last_time < suppression_duration:
                return None  # 被抑制的告警
        
        # 创建告警
        alert = Alert(
            id=f"{component}_{int(time.time())}",
            level=level,
            component=component,
            message=message,
            metadata=metadata or {}
        )
        
        # 记录告警时间
        self.last_alert_times[suppression_key] = alert.timestamp
        
        # 保存告警
        self.alerts.append(alert)
        if len(self.alerts) > self.max_alerts:
            self.alerts.pop(0)
        
        # 触发告警处理器
        for handler in self.alert_handlers:
            try:
                handler(alert)
            except Exception as e:
                logger.error(f"告警处理器执行失败: {e}")
        
        logger.warning(f"创建告警: [{level.value}] {component} - {message}")
        return alert
    
    def resolve_alert(self, alert_id: str) -> bool:
        """解决告警"""
        for alert in self.alerts:
            if alert.id == alert_id and not alert.resolved:
                alert.resolve()
                logger.info(f"告警已解决: {alert_id}")
                return True
        return False
    
    def get_active_alerts(self) -> List[Alert]:
        """获取活跃告警"""
        return [alert for alert in self.alerts if not alert.resolved]
    
    def get_alerts_summary(self) -> Dict[str, Any]:
        """获取告警摘要"""
        active_alerts = self.get_active_alerts()
        
        return {
            "total_alerts": len(self.alerts),
            "active_alerts": len(active_alerts),
            "critical_alerts": len([a for a in active_alerts if a.level == AlertLevel.CRITICAL]),
            "warning_alerts": len([a for a in active_alerts if a.level == AlertLevel.WARNING]),
            "recent_alerts": [
                {
                    "id": alert.id,
                    "level": alert.level.value,
                    "component": alert.component,
                    "message": alert.message,
                    "timestamp": alert.timestamp.isoformat()
                }
                for alert in self.alerts[-10:]
            ]
        }


class SystemHealthMonitor:
    """系统健康监控器"""
    
    def __init__(self):
        self.health_checks: List[HealthCheck] = []
        self.metrics_collector = MetricsCollector()
        self.alert_manager = AlertManager()
        self.running = False
        self.monitor_task: Optional[asyncio.Task] = None
        
        # 注册默认健康检查
        self.register_health_check(SystemResourceHealthCheck())
    
    def register_health_check(self, health_check: HealthCheck) -> None:
        """注册健康检查"""
        self.health_checks.append(health_check)
        logger.info(f"注册健康检查: {health_check.get_component_name()}")
    
    def unregister_health_check(self, component_name: str) -> None:
        """注销健康检查"""
        self.health_checks = [
            hc for hc in self.health_checks 
            if hc.get_component_name() != component_name
        ]
        logger.info(f"注销健康检查: {component_name}")
    
    async def start_monitoring(self) -> None:
        """开始监控"""
        if self.running:
            logger.warning("健康监控已在运行")
            return
        
        self.running = True
        self.monitor_task = asyncio.create_task(self._monitor_loop())
        logger.info("系统健康监控已启动")
    
    async def stop_monitoring(self) -> None:
        """停止监控"""
        self.running = False
        if self.monitor_task:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass
        logger.info("系统健康监控已停止")
    
    async def _monitor_loop(self) -> None:
        """监控循环"""
        last_check_times: Dict[str, datetime] = {}
        
        while self.running:
            try:
                # 收集系统指标
                self.metrics_collector.collect_system_metrics()
                
                # 执行健康检查
                current_time = datetime.now()
                
                for health_check in self.health_checks:
                    component_name = health_check.get_component_name()
                    check_interval = health_check.get_check_interval()
                    
                    # 检查是否需要执行健康检查
                    last_check = last_check_times.get(component_name)
                    if (last_check is None or 
                        current_time - last_check >= timedelta(seconds=check_interval)):
                        
                        try:
                            result = await health_check.check_health()
                            last_check_times[component_name] = current_time
                            
                            # 根据健康检查结果创建告警
                            if result.status == HealthStatus.CRITICAL:
                                self.alert_manager.create_alert(
                                    AlertLevel.CRITICAL,
                                    result.component,
                                    result.message,
                                    {"metrics": result.metrics}
                                )
                            elif result.status == HealthStatus.WARNING:
                                self.alert_manager.create_alert(
                                    AlertLevel.WARNING,
                                    result.component,
                                    result.message,
                                    {"metrics": result.metrics}
                                )
                            
                        except Exception as e:
                            logger.error(f"健康检查执行失败 {component_name}: {e}")
                
                # 等待下一次检查
                await asyncio.sleep(10)  # 每10秒检查一次
                
            except Exception as e:
                logger.error(f"监控循环异常: {e}")
                await asyncio.sleep(5)
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        return {
            "monitoring_active": self.running,
            "health_checks_count": len(self.health_checks),
            "metrics_summary": self.metrics_collector.get_metrics_summary(),
            "alerts_summary": self.alert_manager.get_alerts_summary(),
            "timestamp": datetime.now().isoformat()
        }
    
    def record_performance_metric(
        self,
        operation: str,
        duration_ms: float,
        success: bool = True
    ) -> None:
        """记录性能指标"""
        self.metrics_collector.record_custom_metric(
            f"performance_{operation}",
            duration_ms,
            {"success": success, "operation": operation}
        )
        
        # 如果性能超过阈值，创建告警
        if duration_ms > 100:  # 100ms阈值
            self.alert_manager.create_alert(
                AlertLevel.WARNING,
                "performance",
                f"操作 {operation} 耗时过长: {duration_ms:.2f}ms",
                {"duration_ms": duration_ms, "operation": operation}
            )


# 全局健康监控器实例
global_health_monitor = SystemHealthMonitor()