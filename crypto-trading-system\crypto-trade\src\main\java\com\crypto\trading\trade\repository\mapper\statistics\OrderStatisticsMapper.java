package com.crypto.trading.trade.repository.mapper.statistics;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.crypto.trading.trade.model.entity.statistics.OrderStatisticsEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.math.BigDecimal;
import java.util.List;

/**
 * 订单统计数据访问接口
 * 
 * <p>提供订单统计数据的CRUD操作和自定义查询</p>
 */
@Mapper
public interface OrderStatisticsMapper extends BaseMapper<OrderStatisticsEntity> {

    /**
     * 根据统计日期查询统计数据
     *
     * @param statsDate 统计日期（格式：yyyyMMdd）
     * @return 统计数据列表
     */
    @Select("SELECT * FROM t_order_statistics WHERE stats_date = #{statsDate} ORDER BY stats_hour")
    List<OrderStatisticsEntity> findByStatsDate(@Param("statsDate") String statsDate);

    /**
     * 根据统计日期和小时查询统计数据
     *
     * @param statsDate 统计日期（格式：yyyyMMdd）
     * @param statsHour 统计小时（0-23）
     * @return 统计数据列表
     */
    @Select("SELECT * FROM t_order_statistics WHERE stats_date = #{statsDate} AND stats_hour = #{statsHour}")
    List<OrderStatisticsEntity> findByStatsDateAndHour(
            @Param("statsDate") String statsDate, 
            @Param("statsHour") int statsHour);

    /**
     * 根据交易对查询统计数据
     *
     * @param symbol 交易对
     * @param limit 限制数量
     * @return 统计数据列表
     */
    @Select("SELECT * FROM t_order_statistics WHERE symbol = #{symbol} " +
            "ORDER BY stats_date DESC, stats_hour DESC LIMIT #{limit}")
    List<OrderStatisticsEntity> findBySymbol(@Param("symbol") String symbol, @Param("limit") int limit);

    /**
     * 根据策略查询统计数据
     *
     * @param strategy 策略名称
     * @param limit 限制数量
     * @return 统计数据列表
     */
    @Select("SELECT * FROM t_order_statistics WHERE strategy = #{strategy} " +
            "ORDER BY stats_date DESC, stats_hour DESC LIMIT #{limit}")
    List<OrderStatisticsEntity> findByStrategy(@Param("strategy") String strategy, @Param("limit") int limit);

    /**
     * 查询指定时间范围内的统计数据
     *
     * @param startDate 开始日期（格式：yyyyMMdd）
     * @param endDate 结束日期（格式：yyyyMMdd）
     * @return 统计数据列表
     */
    @Select("SELECT * FROM t_order_statistics WHERE stats_date BETWEEN #{startDate} AND #{endDate} " +
            "ORDER BY stats_date, stats_hour")
    List<OrderStatisticsEntity> findByDateRange(
            @Param("startDate") String startDate, 
            @Param("endDate") String endDate);

    /**
     * 查询指定日期和交易对的统计数据
     *
     * @param statsDate 统计日期（格式：yyyyMMdd）
     * @param symbol 交易对
     * @return 统计数据列表
     */
    @Select("SELECT * FROM t_order_statistics WHERE stats_date = #{statsDate} AND symbol = #{symbol} " +
            "ORDER BY stats_hour")
    List<OrderStatisticsEntity> findByDateAndSymbol(
            @Param("statsDate") String statsDate, 
            @Param("symbol") String symbol);

    /**
     * 查询或创建统计记录
     *
     * @param statsDate 统计日期（格式：yyyyMMdd）
     * @param statsHour 统计小时（0-23）
     * @param symbol 交易对
     * @param strategy 策略名称
     * @return 统计实体
     */
    @Select("SELECT * FROM t_order_statistics WHERE stats_date = #{statsDate} AND stats_hour = #{statsHour} " +
            "AND symbol = #{symbol} AND strategy = #{strategy} LIMIT 1")
    OrderStatisticsEntity findStatsRecord(
            @Param("statsDate") String statsDate,
            @Param("statsHour") int statsHour,
            @Param("symbol") String symbol,
            @Param("strategy") String strategy);
            
    /**
     * 根据日期、交易对和策略查询统计数据
     *
     * @param statsDate 统计日期（格式：yyyyMMdd）
     * @param symbol 交易对
     * @param strategy 策略名称
     * @return 统计实体
     */
    @Select("SELECT * FROM t_order_statistics WHERE stats_date = #{statsDate} " +
            "AND symbol = #{symbol} AND strategy = #{strategy} LIMIT 1")
    OrderStatisticsEntity findByDateAndSymbolAndStrategy(
            @Param("statsDate") String statsDate,
            @Param("symbol") String symbol,
            @Param("strategy") String strategy);

    /**
     * 更新统计记录
     *
     * @param id 主键ID
     * @param totalOrders 总订单数
     * @param successOrders 成功订单数
     * @param failedOrders 失败订单数
     * @param canceledOrders 取消订单数
     * @param totalAmount 总交易金额
     * @param avgExecutionTime 平均执行时间
     * @param minExecutionTime 最小执行时间
     * @param maxExecutionTime 最大执行时间
     * @param successRate 成功率
     * @param avgPriceDeviation 平均价格偏差率
     * @param updatedTime 更新时间
     * @return 影响的行数
     */
    @Update("UPDATE t_order_statistics SET " +
            "total_orders = #{totalOrders}, " +
            "success_orders = #{successOrders}, " +
            "failed_orders = #{failedOrders}, " +
            "canceled_orders = #{canceledOrders}, " +
            "total_amount = #{totalAmount}, " +
            "avg_execution_time = #{avgExecutionTime}, " +
            "min_execution_time = #{minExecutionTime}, " +
            "max_execution_time = #{maxExecutionTime}, " +
            "success_rate = #{successRate}, " +
            "avg_price_deviation = #{avgPriceDeviation}, " +
            "updated_time = #{updatedTime} " +
            "WHERE id = #{id}")
    int updateStats(
            @Param("id") Long id,
            @Param("totalOrders") int totalOrders,
            @Param("successOrders") int successOrders,
            @Param("failedOrders") int failedOrders,
            @Param("canceledOrders") int canceledOrders,
            @Param("totalAmount") BigDecimal totalAmount,
            @Param("avgExecutionTime") Long avgExecutionTime,
            @Param("minExecutionTime") Long minExecutionTime,
            @Param("maxExecutionTime") Long maxExecutionTime,
            @Param("successRate") BigDecimal successRate,
            @Param("avgPriceDeviation") BigDecimal avgPriceDeviation,
            @Param("updatedTime") Long updatedTime);

    /**
     * 分页查询订单统计数据
     *
     * @param page 分页参数
     * @param statsDate 统计日期（可选）
     * @param symbol 交易对（可选）
     * @param strategy 策略名称（可选）
     * @return 分页结果
     */
    IPage<OrderStatisticsEntity> pageOrderStatistics(
            IPage<OrderStatisticsEntity> page,
            @Param("statsDate") String statsDate,
            @Param("symbol") String symbol,
            @Param("strategy") String strategy);
}