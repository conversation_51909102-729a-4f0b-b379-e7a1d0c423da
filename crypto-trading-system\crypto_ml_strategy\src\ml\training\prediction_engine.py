"""
预测引擎模块

实现实时预测和批量预测功能，支持预测结果后处理、
信号生成、置信度评估和与风险管理模块的集成。

Author: Crypto ML Strategy Team
Date: 2024-12-19
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any, Union
from datetime import datetime, timedelta
import logging
import time
import asyncio
import threading
from concurrent.futures import ThreadPoolExecutor
import uuid

from .training_config import (
    PredictionConfig, PredictionResult, ModelType, TrainingPipelineConfig
)
from .data_pipeline import DataPipeline
from .model_manager import ModelManager

logger = logging.getLogger(__name__)


class PredictionEngine:
    """预测引擎类"""
    
    def __init__(self, config: TrainingPipelineConfig):
        """
        初始化预测引擎
        
        Args:
            config: 训练管道配置
        """
        self.config = config
        self.prediction_config = config.prediction_config
        
        # 初始化组件
        self.data_pipeline = DataPipeline(config)
        self.model_manager = ModelManager(config)
        
        # 预测缓存
        self.prediction_cache: Dict[str, PredictionResult] = {}
        self.model_cache: Dict[str, Tuple[Any, Any]] = {}  # (model, metadata)
        
        # 线程池
        self.executor = ThreadPoolExecutor(max_workers=4)
        
        # 锁
        self._lock = threading.RLock()
        
        # 统计信息
        self.prediction_stats = {
            'total_predictions': 0,
            'cache_hits': 0,
            'average_latency': 0.0,
            'last_prediction_time': None
        }
        
        logger.info("Prediction engine initialized")
    
    def _load_model(self, model_id: str) -> Tuple[Any, Any]:
        """
        加载模型（带缓存）
        
        Args:
            model_id: 模型ID
            
        Returns:
            (模型对象, 模型元数据)
        """
        try:
            with self._lock:
                # 检查缓存
                if model_id in self.model_cache:
                    logger.debug(f"Using cached model: {model_id}")
                    return self.model_cache[model_id]
                
                # 从模型管理器加载
                model, metadata = self.model_manager.load_model(model_id)
                
                # 缓存模型
                self.model_cache[model_id] = (model, metadata)
                
                # 限制缓存大小
                if len(self.model_cache) > 10:
                    # 删除最旧的缓存项
                    oldest_key = next(iter(self.model_cache))
                    del self.model_cache[oldest_key]
                
                return model, metadata
                
        except Exception as e:
            logger.error(f"Error loading model {model_id}: {e}")
            raise
    
    def _preprocess_features(
        self,
        features: np.ndarray,
        model: Any,
        metadata: Any
    ) -> np.ndarray:
        """
        预处理特征数据
        
        Args:
            features: 原始特征
            model: 模型对象
            metadata: 模型元数据
            
        Returns:
            预处理后的特征
        """
        try:
            # 处理包含scaler的模型
            if isinstance(model, dict) and 'scaler' in model:
                scaler = model['scaler']
                if scaler is not None:
                    features = scaler.transform(features)
            
            return features
            
        except Exception as e:
            logger.error(f"Error preprocessing features: {e}")
            return features
    
    def _extract_model(self, model: Any) -> Any:
        """提取实际的模型对象"""
        if isinstance(model, dict) and 'model' in model:
            return model['model']
        return model
    
    def _calculate_confidence(
        self,
        model: Any,
        features: np.ndarray,
        predictions: np.ndarray
    ) -> np.ndarray:
        """
        计算预测置信度
        
        Args:
            model: 模型对象
            features: 特征数据
            predictions: 预测结果
            
        Returns:
            置信度数组
        """
        try:
            actual_model = self._extract_model(model)
            
            if hasattr(actual_model, 'predict_proba'):
                # 概率预测
                probabilities = actual_model.predict_proba(features)
                # 使用最大概率作为置信度
                confidence = np.max(probabilities, axis=1)
            
            elif hasattr(actual_model, 'decision_function'):
                # 决策函数
                decision_scores = actual_model.decision_function(features)
                if decision_scores.ndim > 1:
                    # 多分类情况
                    confidence = np.max(np.abs(decision_scores), axis=1)
                else:
                    # 二分类情况
                    confidence = np.abs(decision_scores)
                
                # 归一化到[0,1]
                if len(confidence) > 0:
                    confidence = (confidence - np.min(confidence)) / (np.max(confidence) - np.min(confidence) + 1e-8)
            
            else:
                # 默认置信度
                confidence = np.full(len(predictions), 0.5)
            
            return confidence
            
        except Exception as e:
            logger.error(f"Error calculating confidence: {e}")
            return np.full(len(predictions), 0.5)
    
    def _generate_trading_signals(
        self,
        predictions: np.ndarray,
        probabilities: Optional[np.ndarray],
        confidence_scores: np.ndarray,
        metadata: Any
    ) -> List[Dict[str, Any]]:
        """
        生成交易信号
        
        Args:
            predictions: 预测结果
            probabilities: 预测概率
            confidence_scores: 置信度分数
            metadata: 模型元数据
            
        Returns:
            交易信号列表
        """
        try:
            signals = []
            
            for i, (pred, conf) in enumerate(zip(predictions, confidence_scores)):
                # 基本信号
                signal = {
                    'timestamp': datetime.now(),
                    'prediction': int(pred),
                    'confidence': float(conf),
                    'signal_strength': 'weak'
                }
                
                # 确定信号强度
                if conf >= 0.8:
                    signal['signal_strength'] = 'strong'
                elif conf >= 0.6:
                    signal['signal_strength'] = 'medium'
                
                # 交易方向
                if pred == 1:
                    signal['direction'] = 'buy'
                    signal['action'] = 'long'
                else:
                    signal['direction'] = 'sell'
                    signal['action'] = 'short'
                
                # 添加概率信息
                if probabilities is not None and i < len(probabilities):
                    if probabilities.ndim > 1:
                        signal['probabilities'] = probabilities[i].tolist()
                    else:
                        signal['probabilities'] = [1 - probabilities[i], probabilities[i]]
                
                # 风险评估
                signal['risk_level'] = self._assess_signal_risk(conf, pred)
                
                # 建议仓位大小（基于置信度）
                signal['suggested_position_size'] = self._calculate_position_size(conf)
                
                signals.append(signal)
            
            return signals
            
        except Exception as e:
            logger.error(f"Error generating trading signals: {e}")
            return []
    
    def _assess_signal_risk(self, confidence: float, prediction: int) -> str:
        """评估信号风险"""
        try:
            if confidence >= 0.8:
                return 'low'
            elif confidence >= 0.6:
                return 'medium'
            else:
                return 'high'
        except Exception:
            return 'medium'
    
    def _calculate_position_size(self, confidence: float) -> float:
        """基于置信度计算建议仓位大小"""
        try:
            # 简单的线性映射
            base_size = 0.02  # 基础仓位2%
            max_size = 0.1    # 最大仓位10%
            
            # 置信度越高，仓位越大
            position_size = base_size + (max_size - base_size) * confidence
            
            return round(position_size, 4)
            
        except Exception:
            return 0.02
    
    async def predict_single(
        self,
        model_id: str,
        symbol: str,
        timestamp: Optional[datetime] = None,
        features: Optional[np.ndarray] = None
    ) -> PredictionResult:
        """
        单次预测
        
        Args:
            model_id: 模型ID
            symbol: 交易对符号
            timestamp: 时间戳
            features: 预计算的特征（可选）
            
        Returns:
            预测结果
        """
        start_time = time.time()
        prediction_id = str(uuid.uuid4())
        
        try:
            if timestamp is None:
                timestamp = datetime.now()
            
            # 生成缓存键
            cache_key = f"{model_id}_{symbol}_{timestamp.isoformat()}"
            
            # 检查缓存
            if (self.prediction_config.cache_predictions and 
                cache_key in self.prediction_cache):
                
                cached_result = self.prediction_cache[cache_key]
                cache_age = (datetime.now() - cached_result.timestamp).total_seconds()
                
                if cache_age < self.prediction_config.cache_ttl:
                    with self._lock:
                        self.prediction_stats['cache_hits'] += 1
                    logger.debug(f"Using cached prediction: {cache_key}")
                    return cached_result
            
            # 加载模型
            model, metadata = self._load_model(model_id)
            
            # 准备特征
            if features is None:
                # 获取最新数据进行特征提取
                end_time = timestamp
                start_time_data = end_time - timedelta(days=30)  # 获取30天数据用于特征计算
                
                features_df = await self.data_pipeline.prepare_features(
                    symbol, start_time_data, end_time
                )
                
                if features_df.empty:
                    raise ValueError("No features data available")
                
                # 使用最新的特征
                feature_columns = [col for col in features_df.columns if col != 'target']
                features = features_df[feature_columns].iloc[-1:].values
            
            # 确保特征维度正确
            if features.ndim == 1:
                features = features.reshape(1, -1)
            
            # 预处理特征
            processed_features = self._preprocess_features(features, model, metadata)
            
            # 进行预测
            actual_model = self._extract_model(model)
            predictions = actual_model.predict(processed_features)
            
            # 获取概率（如果可用）
            probabilities = None
            if hasattr(actual_model, 'predict_proba'):
                probabilities = actual_model.predict_proba(processed_features)
            
            # 计算置信度
            confidence_scores = self._calculate_confidence(model, processed_features, predictions)
            
            # 生成交易信号
            trading_signals = self._generate_trading_signals(
                predictions, probabilities, confidence_scores, metadata
            )
            
            # 创建预测结果
            processing_time = time.time() - start_time
            
            result = PredictionResult(
                prediction_id=prediction_id,
                predictions=predictions.tolist(),
                probabilities=probabilities.tolist() if probabilities is not None else None,
                confidence_scores=confidence_scores.tolist(),
                feature_values=processed_features.tolist() if self.prediction_config.include_features else None,
                model_version=metadata.version,
                timestamp=timestamp,
                processing_time=processing_time
            )
            
            # 添加交易信号到结果
            result_dict = result.to_dict()
            result_dict['trading_signals'] = trading_signals
            
            # 缓存结果
            if self.prediction_config.cache_predictions:
                with self._lock:
                    self.prediction_cache[cache_key] = result
                    
                    # 限制缓存大小
                    if len(self.prediction_cache) > 1000:
                        # 删除最旧的缓存项
                        oldest_key = min(
                            self.prediction_cache.keys(),
                            key=lambda k: self.prediction_cache[k].timestamp
                        )
                        del self.prediction_cache[oldest_key]
            
            # 更新统计信息
            with self._lock:
                self.prediction_stats['total_predictions'] += 1
                self.prediction_stats['last_prediction_time'] = datetime.now()
                
                # 更新平均延迟
                total_predictions = self.prediction_stats['total_predictions']
                current_avg = self.prediction_stats['average_latency']
                self.prediction_stats['average_latency'] = (
                    (current_avg * (total_predictions - 1) + processing_time) / total_predictions
                )
            
            logger.info(f"Prediction completed: {prediction_id}, processing time: {processing_time:.3f}s")
            return result
            
        except Exception as e:
            logger.error(f"Error in single prediction: {e}")
            
            # 返回错误结果
            return PredictionResult(
                prediction_id=prediction_id,
                predictions=[],
                timestamp=timestamp or datetime.now(),
                processing_time=time.time() - start_time
            )
    
    async def predict_batch(
        self,
        model_id: str,
        symbol: str,
        timestamps: List[datetime],
        features_batch: Optional[np.ndarray] = None
    ) -> List[PredictionResult]:
        """
        批量预测
        
        Args:
            model_id: 模型ID
            symbol: 交易对符号
            timestamps: 时间戳列表
            features_batch: 预计算的特征批次（可选）
            
        Returns:
            预测结果列表
        """
        try:
            results = []
            
            # 并发执行预测
            tasks = []
            for i, timestamp in enumerate(timestamps):
                features = None
                if features_batch is not None and i < len(features_batch):
                    features = features_batch[i:i+1]
                
                task = self.predict_single(model_id, symbol, timestamp, features)
                tasks.append(task)
            
            # 等待所有预测完成
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 过滤异常结果
            valid_results = []
            for result in results:
                if isinstance(result, PredictionResult):
                    valid_results.append(result)
                else:
                    logger.error(f"Batch prediction error: {result}")
            
            logger.info(f"Batch prediction completed: {len(valid_results)}/{len(timestamps)} successful")
            return valid_results
            
        except Exception as e:
            logger.error(f"Error in batch prediction: {e}")
            return []
    
    def get_prediction_statistics(self) -> Dict[str, Any]:
        """获取预测统计信息"""
        try:
            with self._lock:
                stats = self.prediction_stats.copy()
                
                # 添加缓存信息
                stats.update({
                    'cache_size': len(self.prediction_cache),
                    'model_cache_size': len(self.model_cache),
                    'cache_hit_rate': (
                        self.prediction_stats['cache_hits'] / max(self.prediction_stats['total_predictions'], 1)
                    )
                })
                
                return stats
                
        except Exception as e:
            logger.error(f"Error getting prediction statistics: {e}")
            return {'error': str(e)}
    
    def clear_cache(self) -> None:
        """清理缓存"""
        try:
            with self._lock:
                self.prediction_cache.clear()
                self.model_cache.clear()
            logger.info("Prediction engine cache cleared")
        except Exception as e:
            logger.error(f"Error clearing cache: {e}")
    
    def warm_up_model(self, model_id: str) -> bool:
        """预热模型（加载到缓存）"""
        try:
            self._load_model(model_id)
            logger.info(f"Model {model_id} warmed up successfully")
            return True
        except Exception as e:
            logger.error(f"Error warming up model {model_id}: {e}")
            return False
    
    async def predict_with_risk_assessment(
        self,
        model_id: str,
        symbol: str,
        timestamp: Optional[datetime] = None,
        risk_config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        带风险评估的预测
        
        Args:
            model_id: 模型ID
            symbol: 交易对符号
            timestamp: 时间戳
            risk_config: 风险配置
            
        Returns:
            包含风险评估的预测结果
        """
        try:
            # 进行基础预测
            prediction_result = await self.predict_single(model_id, symbol, timestamp)
            
            # 集成风险管理模块（如果启用）
            risk_assessment = {}
            if self.config.enable_risk_management:
                try:
                    # 这里可以集成风险管理模块
                    # 暂时使用简化的风险评估
                    if prediction_result.confidence_scores:
                        avg_confidence = np.mean(prediction_result.confidence_scores)
                        
                        if avg_confidence >= 0.8:
                            risk_assessment['risk_level'] = 'low'
                            risk_assessment['recommended_action'] = 'proceed'
                        elif avg_confidence >= 0.6:
                            risk_assessment['risk_level'] = 'medium'
                            risk_assessment['recommended_action'] = 'proceed_with_caution'
                        else:
                            risk_assessment['risk_level'] = 'high'
                            risk_assessment['recommended_action'] = 'avoid'
                        
                        risk_assessment['confidence_threshold'] = self.prediction_config.confidence_threshold
                        risk_assessment['meets_threshold'] = avg_confidence >= self.prediction_config.confidence_threshold
                
                except Exception as e:
                    logger.warning(f"Risk assessment failed: {e}")
                    risk_assessment['error'] = str(e)
            
            # 组合结果
            result = prediction_result.to_dict()
            result['risk_assessment'] = risk_assessment
            
            return result
            
        except Exception as e:
            logger.error(f"Error in prediction with risk assessment: {e}")
            return {
                'prediction_id': str(uuid.uuid4()),
                'error': str(e),
                'timestamp': (timestamp or datetime.now()).isoformat()
            }
