package com.crypto.trading.bootstrap.controller;

import com.crypto.trading.bootstrap.discovery.ServiceRegistry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 服务控制器
 * 提供服务注册和发现相关的API
 */
@RestController
@RequestMapping("/service")
public class ServiceController {

    private static final Logger log = LoggerFactory.getLogger(ServiceController.class);
    
    /**
     * 服务注册表
     */
    private final ServiceRegistry serviceRegistry;
    
    /**
     * 构造函数
     * 
     * @param serviceRegistry 服务注册表
     */
    @Autowired
    public ServiceController(ServiceRegistry serviceRegistry) {
        this.serviceRegistry = serviceRegistry;
    }
    
    /**
     * 获取所有服务
     * 
     * @param healthyOnly 如果为true，则只返回健康的服务；如果为false，则返回所有服务
     * @return 服务列表
     */
    @GetMapping("/list")
    public ResponseEntity<Map<String, Object>> listServices(@RequestParam(defaultValue = "false") boolean healthyOnly) {
        List<String> services = serviceRegistry.getServiceUrls(healthyOnly);
        int totalCount = serviceRegistry.getServiceCount(false);
        int healthyCount = serviceRegistry.getServiceCount(true);
        
        Map<String, Object> response = new HashMap<>();
        response.put("services", services);
        response.put("totalCount", totalCount);
        response.put("healthyCount", healthyCount);
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 注册新的服务
     * 
     * @param serviceUrl 服务URL
     * @return 注册结果
     */
    @PostMapping("/register")
    public ResponseEntity<Map<String, Object>> registerService(@RequestParam String serviceUrl) {
        boolean result = serviceRegistry.registerService(serviceUrl);
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", result);
        if (result) {
            response.put("message", "服务注册成功");
        } else {
            response.put("message", "服务已经存在");
        }
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 取消注册服务
     * 
     * @param serviceUrl 服务URL
     * @return 取消注册结果
     */
    @DeleteMapping("/unregister")
    public ResponseEntity<Map<String, Object>> unregisterService(@RequestParam String serviceUrl) {
        boolean result = serviceRegistry.unregisterService(serviceUrl);
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", result);
        if (result) {
            response.put("message", "服务取消注册成功");
        } else {
            response.put("message", "服务不存在");
        }
        
        return ResponseEntity.ok(response);
    }
}