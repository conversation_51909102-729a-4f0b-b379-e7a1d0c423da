# 深度数据采集优化配置
# 基于问题诊断结果的配置优化方案

# 日志配置优化 - 启用详细调试日志
logging:
  level:
    root: info
    com.crypto.trading: debug
    com.crypto.trading.market: debug
    com.crypto.trading.sdk: debug
    com.crypto.trading.market.listener: debug
    com.crypto.trading.market.processor: debug
    com.crypto.trading.sdk.websocket: debug
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"

# 市场数据配置优化
market:
  # 交易对配置
  symbols: BTCUSDT,ETHUSDT
  
  # K线配置（保持不变）
  kline:
    enabled: true
    intervals: 1m,5m,15m,30m,1h,4h,1d
    topic: kline.data
  
  # 深度配置优化
  depth:
    enabled: true
    levels: 5              # 优化：减少到5级，降低数据量和处理复杂度
    speed: 100             # 优化：改为100ms，提高更新频率
    topic: depth.data
  
  # 交易配置（保持不变）
  trade:
    enabled: true
    topic: trade.data
  
  # WebSocket配置优化
  websocket:
    reconnect-interval: 3000      # 优化：减少重连间隔到3秒
    connect-timeout: 10000        # 优化：减少连接超时到10秒
    read-timeout: 20000           # 优化：减少读取超时到20秒
    ping-interval: 20000          # 优化：增加心跳频率到20秒
    health-check-interval: 30000  # 优化：增加健康检查频率到30秒
    max-fail-count: 5             # 优化：增加最大失败次数
    max-concurrent-connections: 50 # 优化：适当减少并发连接数

# 币安API配置
binance:
  api:
    # 测试网配置（当前）
    use-testnet: true
    key: ${BINANCE_API_KEY:87c1475c7a003d08400aa7051ee80fea1a105364a591ef2dbe2b80590eaeb379}
    secret: ${BINANCE_API_SECRET:d6aa03a75f489d4ce35bced30a1e80c5db0d70289938b14df4f7409a82e88b47}
    
    # 主网配置（备用方案）
    # use-testnet: false
    # key: ${BINANCE_MAINNET_API_KEY}
    # secret: ${BINANCE_MAINNET_API_SECRET}
    
    connection-timeout: 10000     # 优化：减少连接超时
    read-timeout: 15000           # 优化：减少读取超时
    write-timeout: 10000          # 优化：减少写入超时
  rate-limit:
    weight-limit: 1200
    weight-interval: 1
    order-limit: 100
    order-interval: 10

# 应用配置优化
app:
  thread-pool:
    max-size: 100               # 优化：减少线程池大小
    core-size: 10               # 优化：减少核心线程数
    queue-capacity: 1000        # 优化：减少队列容量
    keep-alive-seconds: 60

# Spring配置优化
spring:
  threads:
    virtual:
      enabled: true             # 确保虚拟线程启用
  main:
    allow-bean-definition-overriding: true

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,loggers
  endpoint:
    health:
      show-details: always
    loggers:
      enabled: true
  metrics:
    export:
      simple:
        enabled: true

# 额外的调试配置
debug: false
trace: false

# 系统属性优化
server:
  tomcat:
    threads:
      max: 50                   # 优化：减少Tomcat线程数
      min-spare: 5              # 优化：减少最小空闲线程数