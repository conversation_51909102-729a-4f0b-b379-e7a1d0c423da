<configuration>
    <conversionRule conversionWord="epoch"
        converterClass="com.binance.connector.futures.logging.util.MsEpochConverter" />

    <property name="SYSTEM" value="binance-java-connector" />
    <property name="LOG_PATTERN" value="%date{yyyy-MM-dd HH:mm:ss.SSS}[%epoch] | %-5level | %-10thread{10} | %-36logger{36} - %msg%n" />
    <property name="BASE_DIR" value="./logs" />
    <property name="OLD_DIR" value="${SYSTEM}" />
    <property name="FILE_NAME" value="${SYSTEM}.log" />


    <appender name="stdout" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
        </encoder>
    </appender>

    <appender name="dailyFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${BASE_DIR}/${FILE_NAME}</file>
        <append>true</append>
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${BASE_DIR}/${OLD_DIR}/${FILE_NAME}.%d{yyyy-MM-dd}.gz</fileNamePattern>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
    </appender>

  <appender name="dailyFileAsync" class="ch.qos.logback.classic.AsyncAppender">
      <queueSize>1000000</queueSize>
      <discardingThreshold>0</discardingThreshold>
      <appender-ref ref="dailyFile" />
  </appender>

  <root level="INFO">
      <appender-ref ref="stdout" />
      <appender-ref ref="dailyFileAsync" />
  </root>
</configuration>
