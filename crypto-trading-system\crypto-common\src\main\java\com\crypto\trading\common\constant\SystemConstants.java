package com.crypto.trading.common.constant;

/**
 * 系统常量类
 * <p>
 * 定义系统级别的常量，如系统状态码、配置项键名等
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public final class SystemConstants {

    /**
     * 私有构造函数，防止实例化
     */
    private SystemConstants() {
        throw new IllegalStateException("常量类不允许实例化");
    }

    /**
     * 系统名称
     */
    public static final String SYSTEM_NAME = "crypto-trading";

    /**
     * 系统版本
     */
    public static final String SYSTEM_VERSION = "1.0.0";

    /**
     * 成功状态码
     */
    public static final int SUCCESS_CODE = 200;

    /**
     * 失败状态码
     */
    public static final int ERROR_CODE = 500;

    /**
     * 未授权状态码
     */
    public static final int UNAUTHORIZED_CODE = 401;

    /**
     * 禁止访问状态码
     */
    public static final int FORBIDDEN_CODE = 403;

    /**
     * 请求参数错误状态码
     */
    public static final int BAD_REQUEST_CODE = 400;

    /**
     * 资源不存在状态码
     */
    public static final int NOT_FOUND_CODE = 404;

    /**
     * 成功消息
     */
    public static final String SUCCESS_MESSAGE = "操作成功";

    /**
     * 失败消息
     */
    public static final String ERROR_MESSAGE = "操作失败";

    /**
     * 默认分页大小
     */
    public static final int DEFAULT_PAGE_SIZE = 20;

    /**
     * 最大分页大小
     */
    public static final int MAX_PAGE_SIZE = 100;

    /**
     * 默认分页页码
     */
    public static final int DEFAULT_PAGE_NUM = 1;

    /**
     * 虚拟线程前缀
     */
    public static final String VIRTUAL_THREAD_PREFIX = "crypto-vt-";

    /**
     * 平台线程前缀
     */
    public static final String PLATFORM_THREAD_PREFIX = "crypto-pt-";

    /**
     * 日期时间格式
     */
    public static final String DATETIME_FORMAT = "yyyy-MM-dd HH:mm:ss";

    /**
     * 日期格式
     */
    public static final String DATE_FORMAT = "yyyy-MM-dd";

    /**
     * 时间格式
     */
    public static final String TIME_FORMAT = "HH:mm:ss";

    /**
     * 毫秒级时间戳格式
     */
    public static final String TIMESTAMP_FORMAT = "yyyy-MM-dd HH:mm:ss.SSS";

    /**
     * UTF-8 字符集
     */
    public static final String UTF8 = "UTF-8";

    /**
     * GBK 字符集
     */
    public static final String GBK = "GBK";

    /**
     * 默认缓存过期时间（秒）
     */
    public static final int DEFAULT_CACHE_EXPIRE = 3600;

    /**
     * 空字符串
     */
    public static final String EMPTY = "";

    /**
     * 逗号分隔符
     */
    public static final String COMMA = ",";

    /**
     * 点分隔符
     */
    public static final String DOT = ".";

    /**
     * 冒号分隔符
     */
    public static final String COLON = ":";

    /**
     * 分号分隔符
     */
    public static final String SEMICOLON = ";";

    /**
     * 下划线分隔符
     */
    public static final String UNDERSCORE = "_";

    /**
     * 斜杠分隔符
     */
    public static final String SLASH = "/";

    /**
     * 反斜杠分隔符
     */
    public static final String BACKSLASH = "\\";

    /**
     * 连字符分隔符
     */
    public static final String HYPHEN = "-";

    /**
     * 等号分隔符
     */
    public static final String EQUALS = "=";

    /**
     * 与符号分隔符
     */
    public static final String AND = "&";
}