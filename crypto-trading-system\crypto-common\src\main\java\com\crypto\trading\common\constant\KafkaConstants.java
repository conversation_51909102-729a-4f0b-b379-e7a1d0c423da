package com.crypto.trading.common.constant;

/**
 * Kafka常量类
 * <p>
 * 定义Kafka相关的常量，如主题名称、消费者组等
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public final class KafkaConstants {

    /**
     * 私有构造函数，防止实例化
     */
    private KafkaConstants() {
        throw new IllegalStateException("常量类不允许实例化");
    }

    /**
     * K线数据主题
     */
    public static final String TOPIC_KLINE_DATA = "kline.data";

    /**
     * 深度数据主题
     */
    public static final String TOPIC_DEPTH_DATA = "depth.data";

    /**
     * 交易数据主题
     */
    public static final String TOPIC_TRADE_DATA = "trade.data";

    /**
     * 策略信号主题
     */
    public static final String TOPIC_STRATEGY_SIGNAL = "strategy.signal";

    /**
     * 账户更新主题
     */
    public static final String TOPIC_ACCOUNT_UPDATE = "account.update";

    /**
     * 订单更新主题
     */
    public static final String TOPIC_ORDER_UPDATE = "order.update";

    /**
     * 系统事件主题
     */
    public static final String TOPIC_SYSTEM_EVENT = "system.event";

    /**
     * 错误事件主题
     */
    public static final String TOPIC_ERROR_EVENT = "error.event";

    /**
     * 市场数据消费者组
     */
    public static final String GROUP_MARKET_DATA = "market-data-group";

    /**
     * 策略消费者组
     */
    public static final String GROUP_STRATEGY = "strategy-group";

    /**
     * 交易执行消费者组
     */
    public static final String GROUP_TRADE = "trade-group";

    /**
     * 账户管理消费者组
     */
    public static final String GROUP_ACCOUNT = "account-group";

    /**
     * 监控消费者组
     */
    public static final String GROUP_MONITOR = "monitor-group";

    /**
     * 最大拉取记录数
     */
    public static final int MAX_POLL_RECORDS = 500;

    /**
     * 拉取超时时间（毫秒）
     */
    public static final int POLL_TIMEOUT = 1000;

    /**
     * 自动提交间隔（毫秒）
     */
    public static final int AUTO_COMMIT_INTERVAL = 5000;

    /**
     * 默认分区数
     */
    public static final int DEFAULT_PARTITIONS = 3;

    /**
     * 默认副本因子
     */
    public static final short DEFAULT_REPLICATION_FACTOR = 1;

    /**
     * 消息压缩类型
     */
    public static final String COMPRESSION_TYPE = "lz4";

    /**
     * 消息批量大小（字节）
     */
    public static final int BATCH_SIZE = 16384;

    /**
     * 消息发送缓冲区大小（字节）
     */
    public static final int BUFFER_MEMORY = ********;

    /**
     * 消息确认模式 - 全部
     */
    public static final String ACKS_ALL = "all";

    /**
     * 消息确认模式 - 无需确认
     */
    public static final String ACKS_NONE = "0";

    /**
     * 消息确认模式 - 仅领导者确认
     */
    public static final String ACKS_LEADER = "1";

    /**
     * 消息重试次数
     */
    public static final int RETRIES = 3;

    /**
     * 幂等性生产者
     */
    public static final boolean ENABLE_IDEMPOTENCE = true;

    /**
     * Kafka服务器地址
     */
    public static final String BOOTSTRAP_SERVERS = "localhost:9092";

    /**
     * 头部前缀 - 时间戳
     */
    public static final String HEADER_TIMESTAMP = "timestamp";

    /**
     * 头部前缀 - 源服务
     */
    public static final String HEADER_SOURCE = "source";

    /**
     * 头部前缀 - 版本
     */
    public static final String HEADER_VERSION = "version";

    /**
     * 头部前缀 - 交易对
     */
    public static final String HEADER_SYMBOL = "symbol";

    /**
     * 头部前缀 - K线周期
     */
    public static final String HEADER_INTERVAL = "interval";
}