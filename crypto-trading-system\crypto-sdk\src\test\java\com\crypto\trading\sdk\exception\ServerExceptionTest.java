package com.crypto.trading.sdk.exception;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 服务器异常测试类
 */
public class ServerExceptionTest {

    @Test
    public void testConstructorWithCodeAndMessage() {
        // 测试带代码和消息的构造函数
        int code = 5001;
        String message = "服务器测试异常消息";
        int httpStatusCode = 500;
        
        ServerException exception = new ServerException(code, message, httpStatusCode);
        
        // 验证属性
        assertEquals(code, exception.getCode());
        assertEquals(message, exception.getMessage());
        assertEquals(message, exception.getErrorMessage());
        assertEquals(httpStatusCode, exception.getHttpStatusCode());
        assertNull(exception.getCause());
    }

    @Test
    public void testConstructorWithCodeMessageAndCause() {
        // 测试带代码、消息和原因的构造函数
        int code = 5002;
        String message = "服务器测试异常消息与原因";
        Throwable cause = new RuntimeException("原始异常");
        int httpStatusCode = 500;
        
        ServerException exception = new ServerException(code, message, httpStatusCode, cause);
        
        // 验证属性
        assertEquals(code, exception.getCode());
        assertEquals(message, exception.getMessage());
        assertEquals(message, exception.getErrorMessage());
        assertEquals(httpStatusCode, exception.getHttpStatusCode());
        assertSame(cause, exception.getCause());
    }

    @Test
    public void testInheritance() {
        // 测试继承关系
        ServerException exception = new ServerException(5003, "继承测试", 500);
        
        assertTrue(exception instanceof ApiException);
    }

    @Test
    public void testErrorCode() {
        // 测试错误代码是否在正确的范围内
        ServerException exception = new ServerException(5004, "错误代码测试", 500);
        
        int code = exception.getCode();
        assertTrue(code >= 5000 && code < 6000, "服务器异常代码应该在5000-5999范围内");
    }
} 