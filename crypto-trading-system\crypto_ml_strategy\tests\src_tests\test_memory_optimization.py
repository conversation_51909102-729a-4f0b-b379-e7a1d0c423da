"""
Crypto ML Strategy - 内存优化测试套件

该模块实现了内存优化组件的全面测试，包括单元测试、内存使用基准测试、
性能对比测试、内存泄漏检测和集成测试。

主要功能：
- 内存高效数据结构的单元测试
- 大数据集处理器的性能测试
- 内存使用基准测试和分析
- 内存泄漏检测和监控
- 与现有crypto_ml_strategy组件的集成测试

作者: Crypto ML Strategy Team
日期: 2024-01-20
版本: 1.0.0
"""

import gc
import threading
import time
import unittest
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Tuple
import numpy as np
import pandas as pd
from logging_core_manager import get_logger
from performance_logging_core import get_global_performance_logger
from error_handling_system import get_global_error_handler

# 导入内存优化组件
from memory_efficient_data_structures import (
    MemoryOptimizedDataFrame, CircularBuffer, CompressedTimeSeriesStorage,
    MemoryPool, DataStructureProfiler, get_global_profiler,
    create_optimized_dataframe, create_circular_buffer, create_compressed_storage
)
from large_dataset_processor import (
    ChunkedDataProcessor, StreamingDataAggregator, MemoryMappedFileHandler,
    DataCompressionManager, GarbageCollectionOptimizer, get_global_gc_optimizer,
    create_chunked_processor, create_streaming_aggregator, create_compression_manager
)

# 导入基准测试组件进行集成测试
from benchmark_core_framework import BenchmarkTest, BenchmarkMetrics, BenchmarkStatus
from performance_metrics_collector import get_global_metrics_collector
import psutil
import tempfile
from pathlib import Path


class MemoryOptimizedDataFrameTests(unittest.TestCase):
    """内存优化DataFrame测试"""
    
    def setUp(self):
        """测试设置"""
        self.test_data = {
            'timestamp': np.arange(1000, dtype=np.int64),
            'price': np.random.random(1000).astype(np.float64),
            'volume': np.random.randint(1, 1000, 1000, dtype=np.int64)
        }
        self.df = MemoryOptimizedDataFrame(self.test_data)
    
    def test_dtype_optimization(self):
        """测试数据类型优化"""
        # 检查是否正确优化了数据类型
        price_data = self.df.get_column('price')
        self.assertIsNotNone(price_data)
        
        # 价格数据应该被优化为float32（如果在范围内）
        if np.all(np.isfinite(price_data)):
            self.assertIn(price_data.dtype, [np.float32, np.float64])
    
    def test_memory_usage_calculation(self):
        """测试内存使用计算"""
        usage = self.df.memory_usage()
        
        self.assertIn('total', usage)
        self.assertIn('timestamp', usage)
        self.assertIn('price', usage)
        self.assertIn('volume', usage)
        self.assertGreater(usage['total'], 0)
    
    def test_column_operations(self):
        """测试列操作"""
        # 添加新列
        new_data = np.random.random(1000).astype(np.float32)
        self.df.add_column('new_column', new_data)
        
        retrieved_data = self.df.get_column('new_column')
        np.testing.assert_array_equal(retrieved_data, new_data)
    
    def test_pandas_conversion(self):
        """测试与pandas的转换"""
        pandas_df = self.df.to_pandas()
        self.assertIsInstance(pandas_df, pd.DataFrame)
        self.assertEqual(len(pandas_df), 1000)
        
        # 从pandas创建
        new_df = MemoryOptimizedDataFrame.from_pandas(pandas_df)
        self.assertIsInstance(new_df, MemoryOptimizedDataFrame)


class CircularBufferTests(unittest.TestCase):
    """环形缓冲区测试"""
    
    def setUp(self):
        """测试设置"""
        self.buffer = CircularBuffer(capacity=100, dtype=float)
    
    def test_basic_operations(self):
        """测试基本操作"""
        # 添加元素
        for i in range(50):
            self.buffer.append(float(i))
        
        self.assertEqual(self.buffer.size(), 50)
        self.assertFalse(self.buffer.is_full())
        
        # 获取最新元素
        latest = self.buffer.get_latest(5)
        expected = np.array([45.0, 46.0, 47.0, 48.0, 49.0])
        np.testing.assert_array_equal(latest, expected)
    
    def test_overflow_behavior(self):
        """测试溢出行为"""
        # 填满缓冲区
        for i in range(150):  # 超过容量
            self.buffer.append(float(i))
        
        self.assertTrue(self.buffer.is_full())
        self.assertEqual(self.buffer.size(), 100)
        
        # 检查最新数据
        latest = self.buffer.get_latest(5)
        expected = np.array([145.0, 146.0, 147.0, 148.0, 149.0])
        np.testing.assert_array_equal(latest, expected)
    
    def test_memory_usage(self):
        """测试内存使用"""
        memory_usage = self.buffer.memory_usage()
        expected_size = 100 * np.dtype(float).itemsize
        self.assertEqual(memory_usage, expected_size)


class CompressedTimeSeriesStorageTests(unittest.TestCase):
    """压缩时间序列存储测试"""
    
    def setUp(self):
        """测试设置"""
        self.storage = CompressedTimeSeriesStorage(compression_level=4)
        self.timestamps = np.arange(1000, dtype=np.int64)
        self.values = np.random.random(1000).astype(np.float32)
    
    def test_store_and_load(self):
        """测试存储和加载"""
        # 存储数据
        self.storage.store_series('test_series', self.timestamps, self.values)
        
        # 加载数据
        loaded_timestamps, loaded_values = self.storage.load_series('test_series')
        
        np.testing.assert_array_equal(loaded_timestamps, self.timestamps)
        np.testing.assert_array_equal(loaded_values, self.values)
    
    def test_compression_ratio(self):
        """测试压缩比"""
        self.storage.store_series('test_series', self.timestamps, self.values)
        
        metadata = self.storage.get_metadata('test_series')
        self.assertIsNotNone(metadata)
        self.assertGreater(metadata['compression_ratio'], 1.0)
    
    def test_memory_usage_stats(self):
        """测试内存使用统计"""
        self.storage.store_series('series1', self.timestamps, self.values)
        self.storage.store_series('series2', self.timestamps * 2, self.values * 2)
        
        usage = self.storage.memory_usage()
        self.assertIn('total_compressed_bytes', usage)
        self.assertIn('compression_ratio', usage)
        self.assertEqual(usage['series_count'], 2)


class MemoryPoolTests(unittest.TestCase):
    """内存池测试"""
    
    def setUp(self):
        """测试设置"""
        self.pool = MemoryPool(list, initial_size=5, max_size=20)
    
    def test_object_acquisition_and_release(self):
        """测试对象获取和释放"""
        # 获取对象
        obj1 = self.pool.acquire()
        obj2 = self.pool.acquire()
        
        self.assertIsInstance(obj1, list)
        self.assertIsInstance(obj2, list)
        self.assertIsNot(obj1, obj2)
        
        # 释放对象
        self.pool.release(obj1)
        self.pool.release(obj2)
        
        # 再次获取应该重用对象
        obj3 = self.pool.acquire()
        self.assertIn(obj3, [obj1, obj2])
    
    def test_pool_statistics(self):
        """测试池统计信息"""
        stats = self.pool.get_stats()
        
        self.assertIn('pool_size', stats)
        self.assertIn('created_count', stats)
        self.assertIn('reused_count', stats)
        self.assertIn('reuse_ratio', stats)


class ChunkedDataProcessorTests(unittest.TestCase):
    """分块数据处理器测试"""
    
    def setUp(self):
        """测试设置"""
        self.processor = ChunkedDataProcessor(chunk_size=100, max_memory_mb=500)
        
        # 创建测试数据文件
        self.test_file = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False)
        test_data = pd.DataFrame({
            'timestamp': range(1000),
            'price': np.random.random(1000),
            'volume': np.random.randint(1, 1000, 1000)
        })
        test_data.to_csv(self.test_file.name, index=False)
        self.test_file.close()
    
    def tearDown(self):
        """测试清理"""
        Path(self.test_file.name).unlink(missing_ok=True)
    
    def test_file_processing(self):
        """测试文件处理"""
        def simple_processor(chunk):
            # 简单的处理：添加一个计算列
            chunk['price_volume'] = chunk['price'] * chunk['volume']
            return chunk
        
        stats = self.processor.process_file(self.test_file.name, simple_processor)
        
        self.assertGreater(stats.total_chunks, 0)
        self.assertEqual(stats.processed_chunks, stats.total_chunks)
        self.assertEqual(stats.failed_chunks, 0)
        self.assertEqual(stats.total_rows, 1000)
    
    def test_dataframe_processing(self):
        """测试DataFrame处理"""
        test_df = pd.DataFrame({
            'a': range(500),
            'b': np.random.random(500)
        })
        
        def double_processor(chunk):
            chunk['doubled'] = chunk['a'] * 2
            return chunk
        
        result = self.processor.process_dataframe(test_df, double_processor)
        
        self.assertEqual(len(result), 500)
        self.assertIn('doubled', result.columns)
        np.testing.assert_array_equal(result['doubled'], test_df['a'] * 2)


class StreamingDataAggregatorTests(unittest.TestCase):
    """流式数据聚合器测试"""
    
    def setUp(self):
        """测试设置"""
        self.aggregator = StreamingDataAggregator(
            window_size=10,
            aggregation_functions={'price': 'mean', 'volume': 'sum'}
        )
    
    def test_data_aggregation(self):
        """测试数据聚合"""
        # 添加数据点
        for i in range(15):
            data = {'price': float(i), 'volume': float(i * 10)}
            result = self.aggregator.add_data_point(data)
            
            if i == 9:  # 第一个窗口完成
                self.assertIsNotNone(result)
                self.assertIn('price', result)
                self.assertIn('volume', result)
                self.assertAlmostEqual(result['price'], 4.5)  # 0-9的平均值
                self.assertEqual(result['volume'], 450)  # 0-90的和
    
    def test_aggregated_history(self):
        """测试聚合历史"""
        # 添加足够的数据产生多个聚合结果
        for i in range(25):
            data = {'price': float(i)}
            self.aggregator.add_data_point(data)
        
        history = self.aggregator.get_aggregated_history('price')
        self.assertGreater(len(history), 0)


class DataCompressionManagerTests(unittest.TestCase):
    """数据压缩管理器测试"""
    
    def setUp(self):
        """测试设置"""
        self.manager = DataCompressionManager()
        self.test_data = b'This is test data for compression. ' * 100
    
    def test_compression_and_decompression(self):
        """测试压缩和解压缩"""
        compressed_data, algorithm, stats = self.manager.compress_data(self.test_data, 'lz4')
        
        self.assertLess(len(compressed_data), len(self.test_data))
        self.assertGreater(stats['compression_ratio'], 1.0)
        
        decompressed_data = self.manager.decompress_data(compressed_data, algorithm)
        self.assertEqual(decompressed_data, self.test_data)
    
    def test_algorithm_benchmark(self):
        """测试算法基准测试"""
        results = self.manager.benchmark_algorithms(self.test_data)
        
        self.assertIn('lz4', results)
        self.assertIn('zstd', results)
        
        for algorithm, result in results.items():
            if 'error' not in result:
                self.assertIn('compression_ratio', result)
                self.assertIn('compression_time_ms', result)
                self.assertTrue(result['data_integrity'])


class MemoryLeakDetectionTest(unittest.TestCase):
    """内存泄漏检测测试"""
    
    def setUp(self):
        """测试设置"""
        self.profiler = get_global_profiler()
        self.gc_optimizer = get_global_gc_optimizer()
    
    def test_memory_leak_detection(self):
        """测试内存泄漏检测"""
        # 获取初始内存快照
        initial_snapshot = self.profiler.take_snapshot()
        
        # 执行可能导致内存泄漏的操作
        data_structures = []
        for i in range(100):
            df = create_optimized_dataframe({
                'data': np.random.random(1000).astype(np.float32)
            })
            data_structures.append(df)
        
        # 获取操作后的快照
        after_snapshot = self.profiler.take_snapshot()
        
        # 清理数据结构
        data_structures.clear()
        gc.collect()
        
        # 获取清理后的快照
        cleanup_snapshot = self.profiler.take_snapshot()
        
        # 分析内存使用
        memory_increase = after_snapshot.process_memory_mb - initial_snapshot.process_memory_mb
        memory_recovered = after_snapshot.process_memory_mb - cleanup_snapshot.process_memory_mb
        
        # 验证内存是否正确释放（允许一定的误差）
        recovery_ratio = memory_recovered / max(memory_increase, 1)
        self.assertGreater(recovery_ratio, 0.5, "Memory recovery ratio too low, possible memory leak")
    
    def test_gc_optimization_context(self):
        """测试GC优化上下文"""
        initial_stats = self.gc_optimizer.get_gc_stats()
        
        with self.gc_optimizer.optimized_gc_context(disable_gc=True):
            # 在禁用GC的情况下执行操作
            data = [np.random.random(1000) for _ in range(100)]
            
            # 验证GC被禁用
            self.assertFalse(gc.isenabled())
        
        # 验证GC被重新启用
        final_stats = self.gc_optimizer.get_gc_stats()
        self.assertEqual(final_stats['gc_enabled'], initial_stats['gc_enabled'])


class MemoryOptimizationIntegrationTest(unittest.TestCase):
    """内存优化集成测试"""
    
    def setUp(self):
        """测试设置"""
        self.logger = get_logger(f"{__name__}.MemoryOptimizationIntegrationTest")
        self.metrics_collector = get_global_metrics_collector()
    
    def test_integration_with_benchmark_framework(self):
        """测试与基准测试框架的集成"""
        class MemoryOptimizationBenchmark(BenchmarkTest):
            def __init__(self):
                super().__init__("memory_optimization_test", "Memory optimization integration test")
                self.profiler = get_global_profiler()
            
            async def setup(self):
                self.initial_snapshot = self.profiler.take_snapshot()
            
            async def run_test(self):
                start_time = time.time()
                
                # 创建内存优化数据结构
                df = create_optimized_dataframe({
                    'timestamp': np.arange(10000, dtype=np.int64),
                    'price': np.random.random(10000).astype(np.float32),
                    'volume': np.random.randint(1, 1000, 10000, dtype=np.int32)
                })
                
                buffer = create_circular_buffer(1000, float)
                for i in range(1500):
                    buffer.append(float(i))
                
                storage = create_compressed_storage()
                storage.store_series('test', np.arange(1000), np.random.random(1000))
                
                execution_time = (time.time() - start_time) * 1000
                final_snapshot = self.profiler.take_snapshot()
                
                memory_usage = final_snapshot.process_memory_mb - self.initial_snapshot.process_memory_mb
                
                return BenchmarkMetrics(
                    execution_time_ms=execution_time,
                    memory_usage_mb=memory_usage,
                    cpu_usage_percent=25.0,
                    throughput_ops_per_sec=10000 / (execution_time / 1000),
                    latency_p50_ms=execution_time / 2,
                    latency_p95_ms=execution_time * 0.95,
                    latency_p99_ms=execution_time * 0.99,
                    error_rate=0.0,
                    success_rate=100.0,
                    concurrent_connections=1
                )
            
            async def teardown(self):
                gc.collect()
        
        # 运行基准测试
        benchmark = MemoryOptimizationBenchmark()
        
        # 使用asyncio运行测试
        import asyncio
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            result = loop.run_until_complete(benchmark.execute())
            
            self.assertEqual(result.status, BenchmarkStatus.COMPLETED)
            self.assertIsNotNone(result.metrics)
            self.assertLess(result.metrics.memory_usage_mb, 100)  # 内存使用应该合理
            
        finally:
            loop.close()
    
    def test_performance_comparison(self):
        """测试性能对比"""
        # 测试数据
        test_size = 50000
        test_data = {
            'timestamp': np.arange(test_size, dtype=np.int64),
            'price': np.random.random(test_size).astype(np.float64),
            'volume': np.random.randint(1, 1000, test_size, dtype=np.int64)
        }
        
        # 标准pandas DataFrame
        start_time = time.time()
        pandas_df = pd.DataFrame(test_data)
        pandas_memory = pandas_df.memory_usage(deep=True).sum()
        pandas_time = (time.time() - start_time) * 1000
        
        # 内存优化DataFrame
        start_time = time.time()
        optimized_df = create_optimized_dataframe(test_data)
        optimized_memory = sum(optimized_df.memory_usage().values())
        optimized_time = (time.time() - start_time) * 1000
        
        # 计算改进
        memory_reduction = (pandas_memory - optimized_memory) / pandas_memory * 100
        
        self.logger.info(f"Memory reduction: {memory_reduction:.1f}%")
        self.logger.info(f"Pandas memory: {pandas_memory / 1024 / 1024:.1f}MB")
        self.logger.info(f"Optimized memory: {optimized_memory / 1024 / 1024:.1f}MB")
        
        # 验证内存优化效果
        self.assertGreater(memory_reduction, 10, "Memory reduction should be at least 10%")


class MemoryOptimizationTestRunner:
    """内存优化测试运行器"""
    
    def __init__(self):
        self.logger = get_logger(f"{__name__}.MemoryOptimizationTestRunner")
        self.profiler = get_global_profiler()
    
    def run_all_tests(self) -> Dict[str, Any]:
        """运行所有内存优化测试"""
        self.logger.info("Starting memory optimization test suite")
        
        test_classes = [
            MemoryOptimizedDataFrameTests,
            CircularBufferTests,
            CompressedTimeSeriesStorageTests,
            MemoryPoolTests,
            ChunkedDataProcessorTests,
            StreamingDataAggregatorTests,
            DataCompressionManagerTests,
            MemoryLeakDetectionTest,
            MemoryOptimizationIntegrationTest
        ]
        
        results = {
            "start_time": datetime.now().isoformat(),
            "test_results": {},
            "memory_snapshots": [],
            "overall_success": True
        }
        
        initial_snapshot = self.profiler.take_snapshot()
        results["memory_snapshots"].append(("initial", initial_snapshot.to_dict()))
        
        for test_class in test_classes:
            try:
                self.logger.info(f"Running {test_class.__name__}")
                
                suite = unittest.TestLoader().loadTestsFromTestCase(test_class)
                runner = unittest.TextTestRunner(verbosity=0, stream=open('/dev/null', 'w'))
                result = runner.run(suite)
                
                test_result = {
                    "tests_run": result.testsRun,
                    "failures": len(result.failures),
                    "errors": len(result.errors),
                    "success": result.wasSuccessful()
                }
                
                results["test_results"][test_class.__name__] = test_result
                
                if not result.wasSuccessful():
                    results["overall_success"] = False
                
                # 获取内存快照
                snapshot = self.profiler.take_snapshot()
                results["memory_snapshots"].append((test_class.__name__, snapshot.to_dict()))
                
            except Exception as e:
                self.logger.error(f"Failed to run {test_class.__name__}: {e}")
                results["test_results"][test_class.__name__] = {"error": str(e)}
                results["overall_success"] = False
        
        final_snapshot = self.profiler.take_snapshot()
        results["memory_snapshots"].append(("final", final_snapshot.to_dict()))
        
        # 计算总体统计
        total_tests = sum(r.get("tests_run", 0) for r in results["test_results"].values() if isinstance(r, dict))
        total_failures = sum(r.get("failures", 0) for r in results["test_results"].values() if isinstance(r, dict))
        total_errors = sum(r.get("errors", 0) for r in results["test_results"].values() if isinstance(r, dict))
        
        results["summary"] = {
            "total_tests": total_tests,
            "total_failures": total_failures,
            "total_errors": total_errors,
            "success_rate": (total_tests - total_failures - total_errors) / max(total_tests, 1),
            "memory_change_mb": final_snapshot.process_memory_mb - initial_snapshot.process_memory_mb
        }
        
        results["end_time"] = datetime.now().isoformat()
        
        self.logger.info(f"Memory optimization test suite completed: {results['summary']}")
        return results


# 全局测试运行器实例
_global_test_runner: Optional[MemoryOptimizationTestRunner] = None


def get_global_memory_test_runner() -> MemoryOptimizationTestRunner:
    """获取全局内存优化测试运行器实例"""
    global _global_test_runner
    
    if _global_test_runner is None:
        _global_test_runner = MemoryOptimizationTestRunner()
    
    return _global_test_runner


# 便捷函数
async def run_comprehensive_memory_tests() -> Dict[str, Any]:
    """运行完整的内存优化测试套件"""
    runner = get_global_memory_test_runner()
    return runner.run_all_tests()


# 模块级别的日志器
module_logger = get_logger(__name__)