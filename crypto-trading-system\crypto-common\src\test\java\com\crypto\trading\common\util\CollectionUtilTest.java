package com.crypto.trading.common.util;

import org.junit.jupiter.api.Test;

import java.util.*;
import java.util.function.Function;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 集合工具类测试
 * <p>
 * 测试集合操作相关的工具方法
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
class CollectionUtilTest {

    /**
     * 测试空判断
     */
    @Test
    void testIsEmpty() {
        // 测试List
        List<String> emptyList = new ArrayList<>();
        List<String> nonEmptyList = Arrays.asList("a", "b", "c");

        assertTrue(CollectionUtil.isEmpty(emptyList));
        assertFalse(CollectionUtil.isEmpty(nonEmptyList));
        assertTrue(CollectionUtil.isEmpty((List<String>) null));

        assertFalse(CollectionUtil.isNotEmpty(emptyList));
        assertTrue(CollectionUtil.isNotEmpty(nonEmptyList));
        assertFalse(CollectionUtil.isNotEmpty((List<String>) null));

        // 测试Map
        Map<String, String> emptyMap = new HashMap<>();
        Map<String, String> nonEmptyMap = new HashMap<>();
        nonEmptyMap.put("key", "value");

        assertTrue(CollectionUtil.isEmpty(emptyMap));
        assertFalse(CollectionUtil.isEmpty(nonEmptyMap));
        assertTrue(CollectionUtil.isEmpty((Map<String, String>) null));

        assertFalse(CollectionUtil.isNotEmpty(emptyMap));
        assertTrue(CollectionUtil.isNotEmpty(nonEmptyMap));
        assertFalse(CollectionUtil.isNotEmpty((Map<String, String>) null));

        // 测试数组
        String[] emptyArray = new String[0];
        String[] nonEmptyArray = new String[]{"a", "b", "c"};

        assertTrue(CollectionUtil.isEmpty(emptyArray));
        assertFalse(CollectionUtil.isEmpty(nonEmptyArray));
        assertTrue(CollectionUtil.isEmpty((String[]) null));

        assertFalse(CollectionUtil.isNotEmpty(emptyArray));
        assertTrue(CollectionUtil.isNotEmpty(nonEmptyArray));
        assertFalse(CollectionUtil.isNotEmpty((String[]) null));
    }

    /**
     * 测试获取大小
     */
    @Test
    void testSize() {
        // 测试List
        List<String> emptyList = new ArrayList<>();
        List<String> nonEmptyList = Arrays.asList("a", "b", "c");

        assertEquals(0, CollectionUtil.size(emptyList));
        assertEquals(3, CollectionUtil.size(nonEmptyList));
        assertEquals(0, CollectionUtil.size((List<String>) null));

        // 测试Map
        Map<String, String> emptyMap = new HashMap<>();
        Map<String, String> nonEmptyMap = new HashMap<>();
        nonEmptyMap.put("key1", "value1");
        nonEmptyMap.put("key2", "value2");

        assertEquals(0, CollectionUtil.size(emptyMap));
        assertEquals(2, CollectionUtil.size(nonEmptyMap));
        assertEquals(0, CollectionUtil.size((Map<String, String>) null));

        // 测试数组
        String[] emptyArray = new String[0];
        String[] nonEmptyArray = new String[]{"a", "b", "c"};

        assertEquals(0, CollectionUtil.size(emptyArray));
        assertEquals(3, CollectionUtil.size(nonEmptyArray));
        assertEquals(0, CollectionUtil.size((String[]) null));
    }

    /**
     * 测试转换为List
     */
    @Test
    void testToList() {
        // 测试集合转List
        Set<String> set = new HashSet<>(Arrays.asList("a", "b", "c"));
        List<String> list = CollectionUtil.toList(set);
        assertEquals(3, list.size());
        assertTrue(list.contains("a"));
        assertTrue(list.contains("b"));
        assertTrue(list.contains("c"));

        // 测试空集合转List
        Set<String> emptySet = new HashSet<>();
        List<String> emptyList = CollectionUtil.toList(emptySet);
        assertTrue(emptyList.isEmpty());

        // 测试null转List
        List<String> nullList = CollectionUtil.toList((Set<String>) null);
        assertTrue(nullList.isEmpty());

        // 测试数组转List
        String[] array = new String[]{"a", "b", "c"};
        List<String> arrayList = CollectionUtil.toList(array);
        assertEquals(3, arrayList.size());
        assertEquals("a", arrayList.get(0));
        assertEquals("b", arrayList.get(1));
        assertEquals("c", arrayList.get(2));

        // 测试空数组转List
        String[] emptyArray = new String[0];
        List<String> emptyArrayList = CollectionUtil.toList(emptyArray);
        assertTrue(emptyArrayList.isEmpty());

        // 测试null数组转List
        List<String> nullArrayList = CollectionUtil.toList((String[]) null);
        assertTrue(nullArrayList.isEmpty());
    }

    /**
     * 测试转换为Set
     */
    @Test
    void testToSet() {
        // 测试集合转Set
        List<String> list = Arrays.asList("a", "b", "c", "a");
        Set<String> set = CollectionUtil.toSet(list);
        assertEquals(3, set.size());
        assertTrue(set.contains("a"));
        assertTrue(set.contains("b"));
        assertTrue(set.contains("c"));

        // 测试空集合转Set
        List<String> emptyList = new ArrayList<>();
        Set<String> emptySet = CollectionUtil.toSet(emptyList);
        assertTrue(emptySet.isEmpty());

        // 测试null转Set
        Set<String> nullSet = CollectionUtil.toSet((List<String>) null);
        assertTrue(nullSet.isEmpty());

        // 测试数组转Set
        String[] array = new String[]{"a", "b", "c", "a"};
        Set<String> arraySet = CollectionUtil.toSet(array);
        assertEquals(3, arraySet.size());
        assertTrue(arraySet.contains("a"));
        assertTrue(arraySet.contains("b"));
        assertTrue(arraySet.contains("c"));

        // 测试空数组转Set
        String[] emptyArray = new String[0];
        Set<String> emptyArraySet = CollectionUtil.toSet(emptyArray);
        assertTrue(emptyArraySet.isEmpty());

        // 测试null数组转Set
        Set<String> nullArraySet = CollectionUtil.toSet((String[]) null);
        assertTrue(nullArraySet.isEmpty());
    }

    /**
     * 测试转换为Map
     */
    @Test
    void testToMap() {
        // 测试集合转Map
        List<String> list = Arrays.asList("a", "b", "c");
        Map<String, Integer> map = CollectionUtil.toMap(list, 
                Function.identity(), String::length);
        assertEquals(3, map.size());
        assertEquals(Integer.valueOf(1), map.get("a"));
        assertEquals(Integer.valueOf(1), map.get("b"));
        assertEquals(Integer.valueOf(1), map.get("c"));

        // 测试空集合转Map
        List<String> emptyList = new ArrayList<>();
        Map<String, Integer> emptyMap = CollectionUtil.toMap(emptyList,
                Function.identity(), String::length);
        assertTrue(emptyMap.isEmpty());

        // 测试null转Map
        Map<String, Integer> nullMap = CollectionUtil.toMap(null,
                Function.identity(), String::length);
        assertTrue(nullMap.isEmpty());
    }

    /**
     * 测试过滤
     */
    @Test
    void testFilter() {
        // 测试过滤
        List<Integer> list = Arrays.asList(1, 2, 3, 4, 5);
        List<Integer> filtered = CollectionUtil.filter(list, i -> i % 2 == 0);
        assertEquals(2, filtered.size());
        assertEquals(Integer.valueOf(2), filtered.get(0));
        assertEquals(Integer.valueOf(4), filtered.get(1));

        // 测试空集合过滤
        List<Integer> emptyList = new ArrayList<>();
        List<Integer> emptyFiltered = CollectionUtil.filter(emptyList, i -> i % 2 == 0);
        assertTrue(emptyFiltered.isEmpty());

        // 测试null过滤
        List<Integer> nullFiltered = CollectionUtil.filter(null, i -> i % 2 == 0);
        assertTrue(nullFiltered.isEmpty());
    }

    /**
     * 测试映射
     */
    @Test
    void testMap() {
        // 测试映射
        List<String> list = Arrays.asList("a", "bb", "ccc");
        List<Integer> mapped = CollectionUtil.map(list, String::length);
        assertEquals(3, mapped.size());
        assertEquals(Integer.valueOf(1), mapped.get(0));
        assertEquals(Integer.valueOf(2), mapped.get(1));
        assertEquals(Integer.valueOf(3), mapped.get(2));

        // 测试空集合映射
        List<String> emptyList = new ArrayList<>();
        List<Integer> emptyMapped = CollectionUtil.map(emptyList, String::length);
        assertTrue(emptyMapped.isEmpty());

        // 测试null映射
        List<Integer> nullMapped = CollectionUtil.map(null, String::length);
        assertTrue(nullMapped.isEmpty());
    }

    /**
     * 测试分组
     */
    @Test
    void testGroupBy() {
        // 测试分组
        List<String> list = Arrays.asList("a", "bb", "ccc", "d", "ee");
        Map<Integer, List<String>> grouped = CollectionUtil.groupBy(list, String::length);
        assertEquals(3, grouped.size());
        assertEquals(2, grouped.get(1).size());
        assertEquals(2, grouped.get(2).size());
        assertEquals(1, grouped.get(3).size());
        
        assertTrue(grouped.get(1).contains("a"));
        assertTrue(grouped.get(1).contains("d"));
        assertTrue(grouped.get(2).contains("bb"));
        assertTrue(grouped.get(2).contains("ee"));
        assertTrue(grouped.get(3).contains("ccc"));

        // 测试空集合分组
        List<String> emptyList = new ArrayList<>();
        Map<Integer, List<String>> emptyGrouped = CollectionUtil.groupBy(emptyList, String::length);
        assertTrue(emptyGrouped.isEmpty());

        // 测试null分组
        Map<Integer, List<String>> nullGrouped = CollectionUtil.groupBy(null, String::length);
        assertTrue(nullGrouped.isEmpty());
    }

    /**
     * 测试获取第一个和最后一个元素
     */
    @Test
    void testGetFirstAndLast() {
        // 测试获取第一个元素
        List<String> list = Arrays.asList("a", "b", "c");
        assertEquals("a", CollectionUtil.getFirst(list));
        
        // 测试获取空集合的第一个元素
        List<String> emptyList = new ArrayList<>();
        assertNull(CollectionUtil.getFirst(emptyList));
        
        // 测试获取null的第一个元素
        assertNull(CollectionUtil.getFirst(null));
        
        // 测试获取最后一个元素
        assertEquals("c", CollectionUtil.getLast(list));
        
        // 测试获取空集合的最后一个元素
        assertNull(CollectionUtil.getLast(emptyList));
        
        // 测试获取null的最后一个元素
        assertNull(CollectionUtil.getLast(null));
    }

    /**
     * 测试集合操作
     */
    @Test
    void testSetOperations() {
        Set<String> set1 = new HashSet<>(Arrays.asList("a", "b", "c"));
        Set<String> set2 = new HashSet<>(Arrays.asList("b", "c", "d"));
        
        // 测试并集
        Set<String> union = CollectionUtil.union(set1, set2);
        assertEquals(4, union.size());
        assertTrue(union.contains("a"));
        assertTrue(union.contains("b"));
        assertTrue(union.contains("c"));
        assertTrue(union.contains("d"));
        
        // 测试交集
        Set<String> intersection = CollectionUtil.intersection(set1, set2);
        assertEquals(2, intersection.size());
        assertTrue(intersection.contains("b"));
        assertTrue(intersection.contains("c"));
        
        // 测试差集
        Set<String> difference = CollectionUtil.difference(set1, set2);
        assertEquals(1, difference.size());
        assertTrue(difference.contains("a"));
    }
} 