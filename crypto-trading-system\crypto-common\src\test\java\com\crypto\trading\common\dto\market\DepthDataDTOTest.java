package com.crypto.trading.common.dto.market;

import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * OrderBookDTO单元测试类
 */
class DepthDataDTOTest {

    /**
     * 测试默认构造函数
     */
    @Test
    void testDefaultConstructor() {
        DepthDataDTO dto = new DepthDataDTO();
        assertNotNull(dto);
    }

    /**
     * 测试带参数构造函数
     */
    @Test
    void testParameterizedConstructor() {
        String symbol = "BTCUSDT";
        Long lastUpdateId = 123456789L;
        Integer limit = 10;
        List<DepthDataDTO.PriceQuantity> bids = new ArrayList<>();
        bids.add(new DepthDataDTO.PriceQuantity(new BigDecimal("50000.00"), new BigDecimal("1.5")));
        bids.add(new DepthDataDTO.PriceQuantity(new BigDecimal("49900.00"), new BigDecimal("2.3")));

        List<DepthDataDTO.PriceQuantity> asks = new ArrayList<>();
        asks.add(new DepthDataDTO.PriceQuantity(new BigDecimal("50100.00"), new BigDecimal("1.2")));
        asks.add(new DepthDataDTO.PriceQuantity(new BigDecimal("50200.00"), new BigDecimal("3.0")));

        LocalDateTime updateTime = LocalDateTime.now();

        DepthDataDTO dto = new DepthDataDTO(symbol, lastUpdateId, limit, bids, asks, updateTime);

        assertNotNull(dto);
        assertEquals(symbol, dto.getSymbol());
        assertEquals(lastUpdateId, dto.getLastUpdateId());
        assertEquals(limit, dto.getLimit());
        assertEquals(bids, dto.getBids());
        assertEquals(asks, dto.getAsks());
        assertEquals(updateTime, dto.getUpdateTime());
    }

    /**
     * 测试getter和setter方法
     */
    @Test
    void testGettersAndSetters() {
        DepthDataDTO dto = new DepthDataDTO();

        String symbol = "ETHUSDT";
        dto.setSymbol(symbol);
        assertEquals(symbol, dto.getSymbol());

        Long lastUpdateId = 987654321L;
        dto.setLastUpdateId(lastUpdateId);
        assertEquals(lastUpdateId, dto.getLastUpdateId());

        Integer limit = 20;
        dto.setLimit(limit);
        assertEquals(limit, dto.getLimit());

        List<DepthDataDTO.PriceQuantity> bids = new ArrayList<>();
        bids.add(new DepthDataDTO.PriceQuantity(new BigDecimal("3000.00"), new BigDecimal("2.5")));
        dto.setBids(bids);
        assertEquals(bids, dto.getBids());

        List<DepthDataDTO.PriceQuantity> asks = new ArrayList<>();
        asks.add(new DepthDataDTO.PriceQuantity(new BigDecimal("3100.00"), new BigDecimal("1.8")));
        dto.setAsks(asks);
        assertEquals(asks, dto.getAsks());

        LocalDateTime updateTime = LocalDateTime.now();
        dto.setUpdateTime(updateTime);
        assertEquals(updateTime, dto.getUpdateTime());
    }

    /**
     * 测试PriceQuantity内部类的默认构造函数
     */
    @Test
    void testPriceQuantityDefaultConstructor() {
        DepthDataDTO.PriceQuantity priceQuantity = new DepthDataDTO.PriceQuantity();
        assertNotNull(priceQuantity);
    }

    /**
     * 测试PriceQuantity内部类的带参数构造函数
     */
    @Test
    void testPriceQuantityParameterizedConstructor() {
        BigDecimal price = new BigDecimal("50000.00");
        BigDecimal quantity = new BigDecimal("1.5");

        DepthDataDTO.PriceQuantity priceQuantity = new DepthDataDTO.PriceQuantity(price, quantity);

        assertNotNull(priceQuantity);
        assertEquals(price, priceQuantity.getPrice());
        assertEquals(quantity, priceQuantity.getQuantity());
    }

    /**
     * 测试PriceQuantity内部类的getter和setter方法
     */
    @Test
    void testPriceQuantityGettersAndSetters() {
        DepthDataDTO.PriceQuantity priceQuantity = new DepthDataDTO.PriceQuantity();

        BigDecimal price = new BigDecimal("51000.00");
        priceQuantity.setPrice(price);
        assertEquals(price, priceQuantity.getPrice());

        BigDecimal quantity = new BigDecimal("2.5");
        priceQuantity.setQuantity(quantity);
        assertEquals(quantity, priceQuantity.getQuantity());
    }

    /**
     * 测试PriceQuantity内部类的toString方法
     */
    @Test
    void testPriceQuantityToString() {
        BigDecimal price = new BigDecimal("50000.00");
        BigDecimal quantity = new BigDecimal("1.5");

        DepthDataDTO.PriceQuantity priceQuantity = new DepthDataDTO.PriceQuantity(price, quantity);
        String toString = priceQuantity.toString();

        assertNotNull(toString);
        assertTrue(toString.contains("50000.00"));
        assertTrue(toString.contains("1.5"));
    }

    /**
     * 测试toString方法
     */
    @Test
    void testToString() {
        DepthDataDTO dto = new DepthDataDTO();
        dto.setSymbol("BTCUSDT");
        dto.setLastUpdateId(123456789L);

        String toString = dto.toString();
        assertNotNull(toString);
        assertTrue(toString.contains("BTCUSDT"));
        assertTrue(toString.contains("123456789"));
    }
} 