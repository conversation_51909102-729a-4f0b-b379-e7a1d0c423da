package com.crypto.trading.market.repository;

import com.crypto.trading.common.dto.market.KlineDataDTO;
import com.crypto.trading.common.dto.market.DepthDataDTO;
import com.crypto.trading.common.dto.market.TradeDTO;

/**
 * 市场数据存储库接口
 * 定义存储市场数据的方法
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface MarketDataRepository {

    /**
     * 保存K线数据
     *
     * @param klineDataDTO K线数据DTO
     * @return 是否保存成功
     */
    boolean saveKlineData(KlineDataDTO klineDataDTO);

    /**
     * 保存深度数据
     *
     * @param depthDataDTO 深度数据DTO
     * @return 是否保存成功
     */
    boolean saveDepthData(DepthDataDTO depthDataDTO);

    /**
     * 保存交易数据
     *
     * @param tradeDTO 交易数据DTO
     * @return 是否保存成功
     */
    boolean saveTradeData(TradeDTO tradeDTO);

    /**
     * 查询K线数据数量
     *
     * @param symbol 交易对符号
     * @param interval 时间间隔
     * @return K线数据数量
     */
    long findKlineDataCount(String symbol, String interval);
}