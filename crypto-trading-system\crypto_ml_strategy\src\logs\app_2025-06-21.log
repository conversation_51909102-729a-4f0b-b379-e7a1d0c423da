{"text": "2025-06-21 00:13:05.531 | INFO | infrastructure.logging.logging_core_manager:initialize:261 | Logging system initialized successfully\n", "record": {"elapsed": {"repr": "0:00:04.967518", "seconds": 4.967518}, "exception": null, "extra": {}, "file": {"name": "logging_core_manager.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\logging\\logging_core_manager.py"}, "function": "initialize", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 261, "message": "Logging system initialized successfully", "module": "logging_core_manager", "name": "infrastructure.logging.logging_core_manager", "process": {"id": 43244, "name": "MainProcess"}, "thread": {"id": 30184, "name": "MainThread"}, "time": {"repr": "2025-06-21 00:13:05.531202+08:00", "timestamp": 1750435985.531202}}}
{"text": "2025-06-21 00:26:11.872 | INFO | infrastructure.logging.logging_core_manager:initialize:261 | Logging system initialized successfully\n", "record": {"elapsed": {"repr": "0:00:04.715923", "seconds": 4.715923}, "exception": null, "extra": {}, "file": {"name": "logging_core_manager.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\logging\\logging_core_manager.py"}, "function": "initialize", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 261, "message": "Logging system initialized successfully", "module": "logging_core_manager", "name": "infrastructure.logging.logging_core_manager", "process": {"id": 53992, "name": "MainProcess"}, "thread": {"id": 47312, "name": "MainThread"}, "time": {"repr": "2025-06-21 00:26:11.872462+08:00", "timestamp": **********.872462}}}
{"text": "2025-06-21 00:26:11.922 | INFO | infrastructure.system_health_monitor:register_health_check:434 | 注册健康检查: system_resources\n", "record": {"elapsed": {"repr": "0:00:04.766287", "seconds": 4.766287}, "exception": null, "extra": {}, "file": {"name": "system_health_monitor.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\system_health_monitor.py"}, "function": "register_health_check", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 434, "message": "注册健康检查: system_resources", "module": "system_health_monitor", "name": "infrastructure.system_health_monitor", "process": {"id": 53992, "name": "MainProcess"}, "thread": {"id": 47312, "name": "MainThread"}, "time": {"repr": "2025-06-21 00:26:11.922826+08:00", "timestamp": **********.922826}}}
{"text": "2025-06-21 00:26:11.929 | INFO | infrastructure.error_recovery:register_strategy:422 | 注册恢复策略: CacheRecoveryStrategy\n", "record": {"elapsed": {"repr": "0:00:04.773298", "seconds": 4.773298}, "exception": null, "extra": {}, "file": {"name": "error_recovery.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\error_recovery.py"}, "function": "register_strategy", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 422, "message": "注册恢复策略: CacheRecoveryStrategy", "module": "error_recovery", "name": "infrastructure.error_recovery", "process": {"id": 53992, "name": "MainProcess"}, "thread": {"id": 47312, "name": "MainThread"}, "time": {"repr": "2025-06-21 00:26:11.929837+08:00", "timestamp": **********.929837}}}
{"text": "2025-06-21 00:26:11.929 | INFO | infrastructure.error_recovery:register_strategy:422 | 注册恢复策略: RetryRecoveryStrategy\n", "record": {"elapsed": {"repr": "0:00:04.773298", "seconds": 4.773298}, "exception": null, "extra": {}, "file": {"name": "error_recovery.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\error_recovery.py"}, "function": "register_strategy", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 422, "message": "注册恢复策略: RetryRecoveryStrategy", "module": "error_recovery", "name": "infrastructure.error_recovery", "process": {"id": 53992, "name": "MainProcess"}, "thread": {"id": 47312, "name": "MainThread"}, "time": {"repr": "2025-06-21 00:26:11.929837+08:00", "timestamp": **********.929837}}}
{"text": "2025-06-21 00:26:11.930 | INFO | infrastructure.error_recovery:register_strategy:422 | 注册恢复策略: FallbackRecoveryStrategy\n", "record": {"elapsed": {"repr": "0:00:04.774298", "seconds": 4.774298}, "exception": null, "extra": {}, "file": {"name": "error_recovery.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\error_recovery.py"}, "function": "register_strategy", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 422, "message": "注册恢复策略: FallbackRecoveryStrategy", "module": "error_recovery", "name": "infrastructure.error_recovery", "process": {"id": 53992, "name": "MainProcess"}, "thread": {"id": 47312, "name": "MainThread"}, "time": {"repr": "2025-06-21 00:26:11.930837+08:00", "timestamp": **********.930837}}}
{"text": "2025-06-21 00:26:11.930 | INFO | infrastructure.error_recovery:register_strategy:422 | 注册恢复策略: ComponentRestartStrategy\n", "record": {"elapsed": {"repr": "0:00:04.774298", "seconds": 4.774298}, "exception": null, "extra": {}, "file": {"name": "error_recovery.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\error_recovery.py"}, "function": "register_strategy", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 422, "message": "注册恢复策略: ComponentRestartStrategy", "module": "error_recovery", "name": "infrastructure.error_recovery", "process": {"id": 53992, "name": "MainProcess"}, "thread": {"id": 47312, "name": "MainThread"}, "time": {"repr": "2025-06-21 00:26:11.930837+08:00", "timestamp": **********.930837}}}
{"text": "2025-06-21 00:26:11.938 | INFO | infrastructure.graceful_degradation:register_strategy:339 | 注册降级策略: performance_degradation\n", "record": {"elapsed": {"repr": "0:00:04.781562", "seconds": 4.781562}, "exception": null, "extra": {}, "file": {"name": "graceful_degradation.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\graceful_degradation.py"}, "function": "register_strategy", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 339, "message": "注册降级策略: performance_degradation", "module": "graceful_degradation", "name": "infrastructure.graceful_degradation", "process": {"id": 53992, "name": "MainProcess"}, "thread": {"id": 47312, "name": "MainThread"}, "time": {"repr": "2025-06-21 00:26:11.938101+08:00", "timestamp": **********.938101}}}
{"text": "2025-06-21 00:26:11.939 | INFO | infrastructure.graceful_degradation:register_strategy:339 | 注册降级策略: load_based_degradation\n", "record": {"elapsed": {"repr": "0:00:04.782564", "seconds": 4.782564}, "exception": null, "extra": {}, "file": {"name": "graceful_degradation.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\graceful_degradation.py"}, "function": "register_strategy", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 339, "message": "注册降级策略: load_based_degradation", "module": "graceful_degradation", "name": "infrastructure.graceful_degradation", "process": {"id": 53992, "name": "MainProcess"}, "thread": {"id": 47312, "name": "MainThread"}, "time": {"repr": "2025-06-21 00:26:11.939103+08:00", "timestamp": **********.939103}}}
{"text": "2025-06-21 00:37:25.245 | INFO | infrastructure.logging.logging_core_manager:initialize:261 | Logging system initialized successfully\n", "record": {"elapsed": {"repr": "0:00:04.705844", "seconds": 4.705844}, "exception": null, "extra": {}, "file": {"name": "logging_core_manager.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\logging\\logging_core_manager.py"}, "function": "initialize", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 261, "message": "Logging system initialized successfully", "module": "logging_core_manager", "name": "infrastructure.logging.logging_core_manager", "process": {"id": 35028, "name": "MainProcess"}, "thread": {"id": 73696, "name": "MainThread"}, "time": {"repr": "2025-06-21 00:37:25.245056+08:00", "timestamp": **********.245056}}}
{"text": "2025-06-21 00:37:25.284 | INFO | infrastructure.system_health_monitor:register_health_check:434 | 注册健康检查: system_resources\n", "record": {"elapsed": {"repr": "0:00:04.745364", "seconds": 4.745364}, "exception": null, "extra": {}, "file": {"name": "system_health_monitor.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\system_health_monitor.py"}, "function": "register_health_check", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 434, "message": "注册健康检查: system_resources", "module": "system_health_monitor", "name": "infrastructure.system_health_monitor", "process": {"id": 35028, "name": "MainProcess"}, "thread": {"id": 73696, "name": "MainThread"}, "time": {"repr": "2025-06-21 00:37:25.284576+08:00", "timestamp": **********.284576}}}
{"text": "2025-06-21 00:37:25.292 | INFO | infrastructure.error_recovery:register_strategy:422 | 注册恢复策略: CacheRecoveryStrategy\n", "record": {"elapsed": {"repr": "0:00:04.753363", "seconds": 4.753363}, "exception": null, "extra": {}, "file": {"name": "error_recovery.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\error_recovery.py"}, "function": "register_strategy", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 422, "message": "注册恢复策略: CacheRecoveryStrategy", "module": "error_recovery", "name": "infrastructure.error_recovery", "process": {"id": 35028, "name": "MainProcess"}, "thread": {"id": 73696, "name": "MainThread"}, "time": {"repr": "2025-06-21 00:37:25.292575+08:00", "timestamp": **********.292575}}}
{"text": "2025-06-21 00:37:25.294 | INFO | infrastructure.error_recovery:register_strategy:422 | 注册恢复策略: RetryRecoveryStrategy\n", "record": {"elapsed": {"repr": "0:00:04.755372", "seconds": 4.755372}, "exception": null, "extra": {}, "file": {"name": "error_recovery.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\error_recovery.py"}, "function": "register_strategy", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 422, "message": "注册恢复策略: RetryRecoveryStrategy", "module": "error_recovery", "name": "infrastructure.error_recovery", "process": {"id": 35028, "name": "MainProcess"}, "thread": {"id": 73696, "name": "MainThread"}, "time": {"repr": "2025-06-21 00:37:25.294584+08:00", "timestamp": **********.294584}}}
{"text": "2025-06-21 00:37:25.294 | INFO | infrastructure.error_recovery:register_strategy:422 | 注册恢复策略: FallbackRecoveryStrategy\n", "record": {"elapsed": {"repr": "0:00:04.755372", "seconds": 4.755372}, "exception": null, "extra": {}, "file": {"name": "error_recovery.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\error_recovery.py"}, "function": "register_strategy", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 422, "message": "注册恢复策略: FallbackRecoveryStrategy", "module": "error_recovery", "name": "infrastructure.error_recovery", "process": {"id": 35028, "name": "MainProcess"}, "thread": {"id": 73696, "name": "MainThread"}, "time": {"repr": "2025-06-21 00:37:25.294584+08:00", "timestamp": **********.294584}}}
{"text": "2025-06-21 00:37:25.295 | INFO | infrastructure.error_recovery:register_strategy:422 | 注册恢复策略: ComponentRestartStrategy\n", "record": {"elapsed": {"repr": "0:00:04.756371", "seconds": 4.756371}, "exception": null, "extra": {}, "file": {"name": "error_recovery.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\error_recovery.py"}, "function": "register_strategy", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 422, "message": "注册恢复策略: ComponentRestartStrategy", "module": "error_recovery", "name": "infrastructure.error_recovery", "process": {"id": 35028, "name": "MainProcess"}, "thread": {"id": 73696, "name": "MainThread"}, "time": {"repr": "2025-06-21 00:37:25.295583+08:00", "timestamp": **********.295583}}}
{"text": "2025-06-21 00:37:25.302 | INFO | infrastructure.graceful_degradation:register_strategy:339 | 注册降级策略: performance_degradation\n", "record": {"elapsed": {"repr": "0:00:04.763371", "seconds": 4.763371}, "exception": null, "extra": {}, "file": {"name": "graceful_degradation.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\graceful_degradation.py"}, "function": "register_strategy", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 339, "message": "注册降级策略: performance_degradation", "module": "graceful_degradation", "name": "infrastructure.graceful_degradation", "process": {"id": 35028, "name": "MainProcess"}, "thread": {"id": 73696, "name": "MainThread"}, "time": {"repr": "2025-06-21 00:37:25.302583+08:00", "timestamp": **********.302583}}}
{"text": "2025-06-21 00:37:25.302 | INFO | infrastructure.graceful_degradation:register_strategy:339 | 注册降级策略: load_based_degradation\n", "record": {"elapsed": {"repr": "0:00:04.763371", "seconds": 4.763371}, "exception": null, "extra": {}, "file": {"name": "graceful_degradation.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\graceful_degradation.py"}, "function": "register_strategy", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 339, "message": "注册降级策略: load_based_degradation", "module": "graceful_degradation", "name": "infrastructure.graceful_degradation", "process": {"id": 35028, "name": "MainProcess"}, "thread": {"id": 73696, "name": "MainThread"}, "time": {"repr": "2025-06-21 00:37:25.302583+08:00", "timestamp": **********.302583}}}
{"text": "2025-06-21 00:37:25.320 | INFO | infrastructure.test_error_handling_core:__init__:78 | ErrorHandlingCoreTestRunner initialized\n", "record": {"elapsed": {"repr": "0:00:04.781070", "seconds": 4.78107}, "exception": null, "extra": {}, "file": {"name": "test_error_handling_core.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\test_error_handling_core.py"}, "function": "__init__", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 78, "message": "ErrorHandlingCoreTestRunner initialized", "module": "test_error_handling_core", "name": "infrastructure.test_error_handling_core", "process": {"id": 35028, "name": "MainProcess"}, "thread": {"id": 73696, "name": "MainThread"}, "time": {"repr": "2025-06-21 00:37:25.320282+08:00", "timestamp": **********.320282}}}
{"text": "2025-06-21 00:37:25.323 | INFO | infrastructure.test_error_handling_integration:__init__:79 | ErrorHandlingIntegrationTestRunner initialized\n", "record": {"elapsed": {"repr": "0:00:04.784080", "seconds": 4.78408}, "exception": null, "extra": {}, "file": {"name": "test_error_handling_integration.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\test_error_handling_integration.py"}, "function": "__init__", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 79, "message": "ErrorHandlingIntegrationTestRunner initialized", "module": "test_error_handling_integration", "name": "infrastructure.test_error_handling_integration", "process": {"id": 35028, "name": "MainProcess"}, "thread": {"id": 73696, "name": "MainThread"}, "time": {"repr": "2025-06-21 00:37:25.323292+08:00", "timestamp": **********.323292}}}
{"text": "2025-06-21 00:42:00.278 | INFO | infrastructure.logging.logging_core_manager:initialize:261 | Logging system initialized successfully\n", "record": {"elapsed": {"repr": "0:00:05.052005", "seconds": 5.052005}, "exception": null, "extra": {}, "file": {"name": "logging_core_manager.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\logging\\logging_core_manager.py"}, "function": "initialize", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 261, "message": "Logging system initialized successfully", "module": "logging_core_manager", "name": "infrastructure.logging.logging_core_manager", "process": {"id": 62380, "name": "MainProcess"}, "thread": {"id": 54876, "name": "MainThread"}, "time": {"repr": "2025-06-21 00:42:00.278564+08:00", "timestamp": **********.278564}}}
{"text": "2025-06-21 00:42:00.329 | INFO | infrastructure.system_health_monitor:register_health_check:434 | 注册健康检查: system_resources\n", "record": {"elapsed": {"repr": "0:00:05.102606", "seconds": 5.102606}, "exception": null, "extra": {}, "file": {"name": "system_health_monitor.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\system_health_monitor.py"}, "function": "register_health_check", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 434, "message": "注册健康检查: system_resources", "module": "system_health_monitor", "name": "infrastructure.system_health_monitor", "process": {"id": 62380, "name": "MainProcess"}, "thread": {"id": 54876, "name": "MainThread"}, "time": {"repr": "2025-06-21 00:42:00.329165+08:00", "timestamp": **********.329165}}}
{"text": "2025-06-21 00:42:00.339 | INFO | infrastructure.error_recovery:register_strategy:422 | 注册恢复策略: CacheRecoveryStrategy\n", "record": {"elapsed": {"repr": "0:00:05.112599", "seconds": 5.112599}, "exception": null, "extra": {}, "file": {"name": "error_recovery.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\error_recovery.py"}, "function": "register_strategy", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 422, "message": "注册恢复策略: CacheRecoveryStrategy", "module": "error_recovery", "name": "infrastructure.error_recovery", "process": {"id": 62380, "name": "MainProcess"}, "thread": {"id": 54876, "name": "MainThread"}, "time": {"repr": "2025-06-21 00:42:00.339158+08:00", "timestamp": **********.339158}}}
{"text": "2025-06-21 00:42:00.340 | INFO | infrastructure.error_recovery:register_strategy:422 | 注册恢复策略: RetryRecoveryStrategy\n", "record": {"elapsed": {"repr": "0:00:05.113606", "seconds": 5.113606}, "exception": null, "extra": {}, "file": {"name": "error_recovery.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\error_recovery.py"}, "function": "register_strategy", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 422, "message": "注册恢复策略: RetryRecoveryStrategy", "module": "error_recovery", "name": "infrastructure.error_recovery", "process": {"id": 62380, "name": "MainProcess"}, "thread": {"id": 54876, "name": "MainThread"}, "time": {"repr": "2025-06-21 00:42:00.340165+08:00", "timestamp": **********.340165}}}
{"text": "2025-06-21 00:42:00.341 | INFO | infrastructure.error_recovery:register_strategy:422 | 注册恢复策略: FallbackRecoveryStrategy\n", "record": {"elapsed": {"repr": "0:00:05.114603", "seconds": 5.114603}, "exception": null, "extra": {}, "file": {"name": "error_recovery.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\error_recovery.py"}, "function": "register_strategy", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 422, "message": "注册恢复策略: FallbackRecoveryStrategy", "module": "error_recovery", "name": "infrastructure.error_recovery", "process": {"id": 62380, "name": "MainProcess"}, "thread": {"id": 54876, "name": "MainThread"}, "time": {"repr": "2025-06-21 00:42:00.341162+08:00", "timestamp": **********.341162}}}
{"text": "2025-06-21 00:42:00.342 | INFO | infrastructure.error_recovery:register_strategy:422 | 注册恢复策略: ComponentRestartStrategy\n", "record": {"elapsed": {"repr": "0:00:05.115600", "seconds": 5.1156}, "exception": null, "extra": {}, "file": {"name": "error_recovery.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\error_recovery.py"}, "function": "register_strategy", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 422, "message": "注册恢复策略: ComponentRestartStrategy", "module": "error_recovery", "name": "infrastructure.error_recovery", "process": {"id": 62380, "name": "MainProcess"}, "thread": {"id": 54876, "name": "MainThread"}, "time": {"repr": "2025-06-21 00:42:00.342159+08:00", "timestamp": **********.342159}}}
{"text": "2025-06-21 00:42:00.346 | INFO | infrastructure.graceful_degradation:register_strategy:339 | 注册降级策略: performance_degradation\n", "record": {"elapsed": {"repr": "0:00:05.119609", "seconds": 5.119609}, "exception": null, "extra": {}, "file": {"name": "graceful_degradation.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\graceful_degradation.py"}, "function": "register_strategy", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 339, "message": "注册降级策略: performance_degradation", "module": "graceful_degradation", "name": "infrastructure.graceful_degradation", "process": {"id": 62380, "name": "MainProcess"}, "thread": {"id": 54876, "name": "MainThread"}, "time": {"repr": "2025-06-21 00:42:00.346168+08:00", "timestamp": **********.346168}}}
{"text": "2025-06-21 00:42:00.347 | INFO | infrastructure.graceful_degradation:register_strategy:339 | 注册降级策略: load_based_degradation\n", "record": {"elapsed": {"repr": "0:00:05.120598", "seconds": 5.120598}, "exception": null, "extra": {}, "file": {"name": "graceful_degradation.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\graceful_degradation.py"}, "function": "register_strategy", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 339, "message": "注册降级策略: load_based_degradation", "module": "graceful_degradation", "name": "infrastructure.graceful_degradation", "process": {"id": 62380, "name": "MainProcess"}, "thread": {"id": 54876, "name": "MainThread"}, "time": {"repr": "2025-06-21 00:42:00.347157+08:00", "timestamp": **********.347157}}}
{"text": "2025-06-21 00:42:00.364 | INFO | infrastructure.test_error_handling_core:__init__:78 | ErrorHandlingCoreTestRunner initialized\n", "record": {"elapsed": {"repr": "0:00:05.138072", "seconds": 5.138072}, "exception": null, "extra": {}, "file": {"name": "test_error_handling_core.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\test_error_handling_core.py"}, "function": "__init__", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 78, "message": "ErrorHandlingCoreTestRunner initialized", "module": "test_error_handling_core", "name": "infrastructure.test_error_handling_core", "process": {"id": 62380, "name": "MainProcess"}, "thread": {"id": 54876, "name": "MainThread"}, "time": {"repr": "2025-06-21 00:42:00.364631+08:00", "timestamp": **********.364631}}}
{"text": "2025-06-21 00:42:00.369 | INFO | infrastructure.test_error_handling_integration:__init__:79 | ErrorHandlingIntegrationTestRunner initialized\n", "record": {"elapsed": {"repr": "0:00:05.142475", "seconds": 5.142475}, "exception": null, "extra": {}, "file": {"name": "test_error_handling_integration.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\test_error_handling_integration.py"}, "function": "__init__", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 79, "message": "ErrorHandlingIntegrationTestRunner initialized", "module": "test_error_handling_integration", "name": "infrastructure.test_error_handling_integration", "process": {"id": 62380, "name": "MainProcess"}, "thread": {"id": 54876, "name": "MainThread"}, "time": {"repr": "2025-06-21 00:42:00.369034+08:00", "timestamp": **********.369034}}}
{"text": "2025-06-21 01:14:26.662 | INFO | infrastructure.logging.logging_core_manager:initialize:261 | Logging system initialized successfully\n", "record": {"elapsed": {"repr": "0:00:04.696440", "seconds": 4.69644}, "exception": null, "extra": {}, "file": {"name": "logging_core_manager.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\logging\\logging_core_manager.py"}, "function": "initialize", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 261, "message": "Logging system initialized successfully", "module": "logging_core_manager", "name": "infrastructure.logging.logging_core_manager", "process": {"id": 1620, "name": "MainProcess"}, "thread": {"id": 56044, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:14:26.662935+08:00", "timestamp": **********.662935}}}
{"text": "2025-06-21 01:14:26.718 | INFO | infrastructure.system_health_monitor:register_health_check:434 | 注册健康检查: system_resources\n", "record": {"elapsed": {"repr": "0:00:04.752191", "seconds": 4.752191}, "exception": null, "extra": {}, "file": {"name": "system_health_monitor.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\system_health_monitor.py"}, "function": "register_health_check", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 434, "message": "注册健康检查: system_resources", "module": "system_health_monitor", "name": "infrastructure.system_health_monitor", "process": {"id": 1620, "name": "MainProcess"}, "thread": {"id": 56044, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:14:26.718686+08:00", "timestamp": **********.718686}}}
{"text": "2025-06-21 01:14:26.727 | INFO | infrastructure.error_recovery:register_strategy:422 | 注册恢复策略: CacheRecoveryStrategy\n", "record": {"elapsed": {"repr": "0:00:04.761193", "seconds": 4.761193}, "exception": null, "extra": {}, "file": {"name": "error_recovery.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\error_recovery.py"}, "function": "register_strategy", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 422, "message": "注册恢复策略: CacheRecoveryStrategy", "module": "error_recovery", "name": "infrastructure.error_recovery", "process": {"id": 1620, "name": "MainProcess"}, "thread": {"id": 56044, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:14:26.727688+08:00", "timestamp": **********.727688}}}
{"text": "2025-06-21 01:14:26.728 | INFO | infrastructure.error_recovery:register_strategy:422 | 注册恢复策略: RetryRecoveryStrategy\n", "record": {"elapsed": {"repr": "0:00:04.762225", "seconds": 4.762225}, "exception": null, "extra": {}, "file": {"name": "error_recovery.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\error_recovery.py"}, "function": "register_strategy", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 422, "message": "注册恢复策略: RetryRecoveryStrategy", "module": "error_recovery", "name": "infrastructure.error_recovery", "process": {"id": 1620, "name": "MainProcess"}, "thread": {"id": 56044, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:14:26.728720+08:00", "timestamp": **********.72872}}}
{"text": "2025-06-21 01:14:26.730 | INFO | infrastructure.error_recovery:register_strategy:422 | 注册恢复策略: FallbackRecoveryStrategy\n", "record": {"elapsed": {"repr": "0:00:04.764190", "seconds": 4.76419}, "exception": null, "extra": {}, "file": {"name": "error_recovery.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\error_recovery.py"}, "function": "register_strategy", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 422, "message": "注册恢复策略: FallbackRecoveryStrategy", "module": "error_recovery", "name": "infrastructure.error_recovery", "process": {"id": 1620, "name": "MainProcess"}, "thread": {"id": 56044, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:14:26.730685+08:00", "timestamp": **********.730685}}}
{"text": "2025-06-21 01:14:26.732 | INFO | infrastructure.error_recovery:register_strategy:422 | 注册恢复策略: ComponentRestartStrategy\n", "record": {"elapsed": {"repr": "0:00:04.766193", "seconds": 4.766193}, "exception": null, "extra": {}, "file": {"name": "error_recovery.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\error_recovery.py"}, "function": "register_strategy", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 422, "message": "注册恢复策略: ComponentRestartStrategy", "module": "error_recovery", "name": "infrastructure.error_recovery", "process": {"id": 1620, "name": "MainProcess"}, "thread": {"id": 56044, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:14:26.732688+08:00", "timestamp": **********.732688}}}
{"text": "2025-06-21 01:14:26.738 | INFO | infrastructure.graceful_degradation:register_strategy:339 | 注册降级策略: performance_degradation\n", "record": {"elapsed": {"repr": "0:00:04.772192", "seconds": 4.772192}, "exception": null, "extra": {}, "file": {"name": "graceful_degradation.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\graceful_degradation.py"}, "function": "register_strategy", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 339, "message": "注册降级策略: performance_degradation", "module": "graceful_degradation", "name": "infrastructure.graceful_degradation", "process": {"id": 1620, "name": "MainProcess"}, "thread": {"id": 56044, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:14:26.738687+08:00", "timestamp": **********.738687}}}
{"text": "2025-06-21 01:14:26.739 | INFO | infrastructure.graceful_degradation:register_strategy:339 | 注册降级策略: load_based_degradation\n", "record": {"elapsed": {"repr": "0:00:04.773189", "seconds": 4.773189}, "exception": null, "extra": {}, "file": {"name": "graceful_degradation.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\graceful_degradation.py"}, "function": "register_strategy", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 339, "message": "注册降级策略: load_based_degradation", "module": "graceful_degradation", "name": "infrastructure.graceful_degradation", "process": {"id": 1620, "name": "MainProcess"}, "thread": {"id": 56044, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:14:26.739684+08:00", "timestamp": **********.739684}}}
{"text": "2025-06-21 01:14:26.757 | INFO | infrastructure.test_error_handling_core:__init__:78 | ErrorHandlingCoreTestRunner initialized\n", "record": {"elapsed": {"repr": "0:00:04.791190", "seconds": 4.79119}, "exception": null, "extra": {}, "file": {"name": "test_error_handling_core.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\test_error_handling_core.py"}, "function": "__init__", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 78, "message": "ErrorHandlingCoreTestRunner initialized", "module": "test_error_handling_core", "name": "infrastructure.test_error_handling_core", "process": {"id": 1620, "name": "MainProcess"}, "thread": {"id": 56044, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:14:26.757685+08:00", "timestamp": **********.757685}}}
{"text": "2025-06-21 01:14:26.869 | INFO | infrastructure.test_error_handling_integration:__init__:79 | ErrorHandlingIntegrationTestRunner initialized\n", "record": {"elapsed": {"repr": "0:00:04.903167", "seconds": 4.903167}, "exception": null, "extra": {}, "file": {"name": "test_error_handling_integration.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\test_error_handling_integration.py"}, "function": "__init__", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 79, "message": "ErrorHandlingIntegrationTestRunner initialized", "module": "test_error_handling_integration", "name": "infrastructure.test_error_handling_integration", "process": {"id": 1620, "name": "MainProcess"}, "thread": {"id": 56044, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:14:26.869662+08:00", "timestamp": **********.869662}}}
{"text": "2025-06-21 01:14:30.502 | INFO | core.config:__init__:43 | 配置已加载: D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\config\\config.ini\n", "record": {"elapsed": {"repr": "0:00:08.535674", "seconds": 8.535674}, "exception": null, "extra": {}, "file": {"name": "config.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\core\\config.py"}, "function": "__init__", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 43, "message": "配置已加载: D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\config\\config.ini", "module": "config", "name": "core.config", "process": {"id": 1620, "name": "MainProcess"}, "thread": {"id": 56044, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:14:30.502169+08:00", "timestamp": 1750439670.502169}}}
{"text": "2025-06-21 01:14:51.556 | INFO | data.clients.influxdb_client:_connect:100 | InfluxDB连接建立成功\n", "record": {"elapsed": {"repr": "0:00:29.589520", "seconds": 29.58952}, "exception": null, "extra": {"component": "InfluxDBClient"}, "file": {"name": "influxdb_client.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\clients\\influxdb_client.py"}, "function": "_connect", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 100, "message": "InfluxDB连接建立成功", "module": "influxdb_client", "name": "data.clients.influxdb_client", "process": {"id": 1620, "name": "MainProcess"}, "thread": {"id": 56044, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:14:51.556015+08:00", "timestamp": 1750439691.556015}}}
{"text": "2025-06-21 01:14:51.557 | INFO | data.clients.influxdb_client:__init__:75 | InfluxDB客户端初始化完成: http://localhost:8086, 组织: crypto, 存储桶: market_data\n", "record": {"elapsed": {"repr": "0:00:29.590518", "seconds": 29.590518}, "exception": null, "extra": {"component": "InfluxDBClient"}, "file": {"name": "influxdb_client.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\clients\\influxdb_client.py"}, "function": "__init__", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 75, "message": "InfluxDB客户端初始化完成: http://localhost:8086, 组织: crypto, 存储桶: market_data", "module": "influxdb_client", "name": "data.clients.influxdb_client", "process": {"id": 1620, "name": "MainProcess"}, "thread": {"id": 56044, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:14:51.557013+08:00", "timestamp": 1750439691.557013}}}
{"text": "2025-06-21 01:24:55.881 | INFO | infrastructure.logging.logging_core_manager:initialize:261 | Logging system initialized successfully\n", "record": {"elapsed": {"repr": "0:00:05.947732", "seconds": 5.947732}, "exception": null, "extra": {}, "file": {"name": "logging_core_manager.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\logging\\logging_core_manager.py"}, "function": "initialize", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 261, "message": "Logging system initialized successfully", "module": "logging_core_manager", "name": "infrastructure.logging.logging_core_manager", "process": {"id": 62180, "name": "MainProcess"}, "thread": {"id": 35316, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:24:55.881918+08:00", "timestamp": **********.881918}}}
{"text": "2025-06-21 01:24:55.950 | INFO | infrastructure.system_health_monitor:register_health_check:434 | 注册健康检查: system_resources\n", "record": {"elapsed": {"repr": "0:00:06.016046", "seconds": 6.016046}, "exception": null, "extra": {}, "file": {"name": "system_health_monitor.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\system_health_monitor.py"}, "function": "register_health_check", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 434, "message": "注册健康检查: system_resources", "module": "system_health_monitor", "name": "infrastructure.system_health_monitor", "process": {"id": 62180, "name": "MainProcess"}, "thread": {"id": 35316, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:24:55.950232+08:00", "timestamp": **********.950232}}}
{"text": "2025-06-21 01:24:55.964 | INFO | infrastructure.error_recovery:register_strategy:422 | 注册恢复策略: CacheRecoveryStrategy\n", "record": {"elapsed": {"repr": "0:00:06.030120", "seconds": 6.03012}, "exception": null, "extra": {}, "file": {"name": "error_recovery.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\error_recovery.py"}, "function": "register_strategy", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 422, "message": "注册恢复策略: CacheRecoveryStrategy", "module": "error_recovery", "name": "infrastructure.error_recovery", "process": {"id": 62180, "name": "MainProcess"}, "thread": {"id": 35316, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:24:55.964306+08:00", "timestamp": **********.964306}}}
{"text": "2025-06-21 01:24:55.967 | INFO | infrastructure.error_recovery:register_strategy:422 | 注册恢复策略: RetryRecoveryStrategy\n", "record": {"elapsed": {"repr": "0:00:06.033120", "seconds": 6.03312}, "exception": null, "extra": {}, "file": {"name": "error_recovery.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\error_recovery.py"}, "function": "register_strategy", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 422, "message": "注册恢复策略: RetryRecoveryStrategy", "module": "error_recovery", "name": "infrastructure.error_recovery", "process": {"id": 62180, "name": "MainProcess"}, "thread": {"id": 35316, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:24:55.967306+08:00", "timestamp": **********.967306}}}
{"text": "2025-06-21 01:24:55.968 | INFO | infrastructure.error_recovery:register_strategy:422 | 注册恢复策略: FallbackRecoveryStrategy\n", "record": {"elapsed": {"repr": "0:00:06.034655", "seconds": 6.034655}, "exception": null, "extra": {}, "file": {"name": "error_recovery.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\error_recovery.py"}, "function": "register_strategy", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 422, "message": "注册恢复策略: FallbackRecoveryStrategy", "module": "error_recovery", "name": "infrastructure.error_recovery", "process": {"id": 62180, "name": "MainProcess"}, "thread": {"id": 35316, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:24:55.968841+08:00", "timestamp": **********.968841}}}
{"text": "2025-06-21 01:24:55.969 | INFO | infrastructure.error_recovery:register_strategy:422 | 注册恢复策略: ComponentRestartStrategy\n", "record": {"elapsed": {"repr": "0:00:06.035677", "seconds": 6.035677}, "exception": null, "extra": {}, "file": {"name": "error_recovery.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\error_recovery.py"}, "function": "register_strategy", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 422, "message": "注册恢复策略: ComponentRestartStrategy", "module": "error_recovery", "name": "infrastructure.error_recovery", "process": {"id": 62180, "name": "MainProcess"}, "thread": {"id": 35316, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:24:55.969863+08:00", "timestamp": **********.969863}}}
{"text": "2025-06-21 01:24:55.985 | INFO | infrastructure.graceful_degradation:register_strategy:339 | 注册降级策略: performance_degradation\n", "record": {"elapsed": {"repr": "0:00:06.051723", "seconds": 6.051723}, "exception": null, "extra": {}, "file": {"name": "graceful_degradation.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\graceful_degradation.py"}, "function": "register_strategy", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 339, "message": "注册降级策略: performance_degradation", "module": "graceful_degradation", "name": "infrastructure.graceful_degradation", "process": {"id": 62180, "name": "MainProcess"}, "thread": {"id": 35316, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:24:55.985909+08:00", "timestamp": **********.985909}}}
{"text": "2025-06-21 01:24:55.986 | INFO | infrastructure.graceful_degradation:register_strategy:339 | 注册降级策略: load_based_degradation\n", "record": {"elapsed": {"repr": "0:00:06.052723", "seconds": 6.052723}, "exception": null, "extra": {}, "file": {"name": "graceful_degradation.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\graceful_degradation.py"}, "function": "register_strategy", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 339, "message": "注册降级策略: load_based_degradation", "module": "graceful_degradation", "name": "infrastructure.graceful_degradation", "process": {"id": 62180, "name": "MainProcess"}, "thread": {"id": 35316, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:24:55.986909+08:00", "timestamp": **********.986909}}}
{"text": "2025-06-21 01:24:56.010 | INFO | infrastructure.test_error_handling_core:__init__:78 | ErrorHandlingCoreTestRunner initialized\n", "record": {"elapsed": {"repr": "0:00:06.075969", "seconds": 6.075969}, "exception": null, "extra": {}, "file": {"name": "test_error_handling_core.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\test_error_handling_core.py"}, "function": "__init__", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 78, "message": "ErrorHandlingCoreTestRunner initialized", "module": "test_error_handling_core", "name": "infrastructure.test_error_handling_core", "process": {"id": 62180, "name": "MainProcess"}, "thread": {"id": 35316, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:24:56.010155+08:00", "timestamp": 1750440296.010155}}}
{"text": "2025-06-21 01:24:56.227 | INFO | infrastructure.test_error_handling_integration:__init__:79 | ErrorHandlingIntegrationTestRunner initialized\n", "record": {"elapsed": {"repr": "0:00:06.293168", "seconds": 6.293168}, "exception": null, "extra": {}, "file": {"name": "test_error_handling_integration.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\test_error_handling_integration.py"}, "function": "__init__", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 79, "message": "ErrorHandlingIntegrationTestRunner initialized", "module": "test_error_handling_integration", "name": "infrastructure.test_error_handling_integration", "process": {"id": 62180, "name": "MainProcess"}, "thread": {"id": 35316, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:24:56.227354+08:00", "timestamp": 1750440296.227354}}}
{"text": "2025-06-21 01:25:01.718 | INFO | core.config:__init__:43 | 配置已加载: D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\config\\config.ini\n", "record": {"elapsed": {"repr": "0:00:11.784315", "seconds": 11.784315}, "exception": null, "extra": {}, "file": {"name": "config.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\core\\config.py"}, "function": "__init__", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 43, "message": "配置已加载: D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\config\\config.ini", "module": "config", "name": "core.config", "process": {"id": 62180, "name": "MainProcess"}, "thread": {"id": 35316, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:25:01.718501+08:00", "timestamp": 1750440301.718501}}}
{"text": "2025-06-21 01:25:22.828 | INFO | data.clients.influxdb_client:_connect:100 | InfluxDB连接建立成功\n", "record": {"elapsed": {"repr": "0:00:32.894378", "seconds": 32.894378}, "exception": null, "extra": {"component": "InfluxDBClient"}, "file": {"name": "influxdb_client.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\clients\\influxdb_client.py"}, "function": "_connect", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 100, "message": "InfluxDB连接建立成功", "module": "influxdb_client", "name": "data.clients.influxdb_client", "process": {"id": 62180, "name": "MainProcess"}, "thread": {"id": 35316, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:25:22.828564+08:00", "timestamp": 1750440322.828564}}}
{"text": "2025-06-21 01:25:22.829 | INFO | data.clients.influxdb_client:__init__:75 | InfluxDB客户端初始化完成: http://localhost:8086, 组织: crypto, 存储桶: market_data\n", "record": {"elapsed": {"repr": "0:00:32.895458", "seconds": 32.895458}, "exception": null, "extra": {"component": "InfluxDBClient"}, "file": {"name": "influxdb_client.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\clients\\influxdb_client.py"}, "function": "__init__", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 75, "message": "InfluxDB客户端初始化完成: http://localhost:8086, 组织: crypto, 存储桶: market_data", "module": "influxdb_client", "name": "data.clients.influxdb_client", "process": {"id": 62180, "name": "MainProcess"}, "thread": {"id": 35316, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:25:22.829644+08:00", "timestamp": 1750440322.829644}}}
{"text": "2025-06-21 01:31:25.169 | INFO | infrastructure.logging.logging_core_manager:initialize:261 | Logging system initialized successfully\n", "record": {"elapsed": {"repr": "0:00:05.303458", "seconds": 5.303458}, "exception": null, "extra": {}, "file": {"name": "logging_core_manager.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\logging\\logging_core_manager.py"}, "function": "initialize", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 261, "message": "Logging system initialized successfully", "module": "logging_core_manager", "name": "infrastructure.logging.logging_core_manager", "process": {"id": 36960, "name": "MainProcess"}, "thread": {"id": 62948, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:31:25.169637+08:00", "timestamp": **********.169637}}}
{"text": "2025-06-21 01:31:25.215 | INFO | infrastructure.system_health_monitor:register_health_check:434 | 注册健康检查: system_resources\n", "record": {"elapsed": {"repr": "0:00:05.349066", "seconds": 5.349066}, "exception": null, "extra": {}, "file": {"name": "system_health_monitor.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\system_health_monitor.py"}, "function": "register_health_check", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 434, "message": "注册健康检查: system_resources", "module": "system_health_monitor", "name": "infrastructure.system_health_monitor", "process": {"id": 36960, "name": "MainProcess"}, "thread": {"id": 62948, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:31:25.215245+08:00", "timestamp": **********.215245}}}
{"text": "2025-06-21 01:31:25.221 | INFO | infrastructure.error_recovery:register_strategy:422 | 注册恢复策略: CacheRecoveryStrategy\n", "record": {"elapsed": {"repr": "0:00:05.355591", "seconds": 5.355591}, "exception": null, "extra": {}, "file": {"name": "error_recovery.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\error_recovery.py"}, "function": "register_strategy", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 422, "message": "注册恢复策略: CacheRecoveryStrategy", "module": "error_recovery", "name": "infrastructure.error_recovery", "process": {"id": 36960, "name": "MainProcess"}, "thread": {"id": 62948, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:31:25.221770+08:00", "timestamp": **********.22177}}}
{"text": "2025-06-21 01:31:25.222 | INFO | infrastructure.error_recovery:register_strategy:422 | 注册恢复策略: RetryRecoveryStrategy\n", "record": {"elapsed": {"repr": "0:00:05.356588", "seconds": 5.356588}, "exception": null, "extra": {}, "file": {"name": "error_recovery.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\error_recovery.py"}, "function": "register_strategy", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 422, "message": "注册恢复策略: RetryRecoveryStrategy", "module": "error_recovery", "name": "infrastructure.error_recovery", "process": {"id": 36960, "name": "MainProcess"}, "thread": {"id": 62948, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:31:25.222767+08:00", "timestamp": **********.222767}}}
{"text": "2025-06-21 01:31:25.222 | INFO | infrastructure.error_recovery:register_strategy:422 | 注册恢复策略: FallbackRecoveryStrategy\n", "record": {"elapsed": {"repr": "0:00:05.356588", "seconds": 5.356588}, "exception": null, "extra": {}, "file": {"name": "error_recovery.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\error_recovery.py"}, "function": "register_strategy", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 422, "message": "注册恢复策略: FallbackRecoveryStrategy", "module": "error_recovery", "name": "infrastructure.error_recovery", "process": {"id": 36960, "name": "MainProcess"}, "thread": {"id": 62948, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:31:25.222767+08:00", "timestamp": **********.222767}}}
{"text": "2025-06-21 01:31:25.223 | INFO | infrastructure.error_recovery:register_strategy:422 | 注册恢复策略: ComponentRestartStrategy\n", "record": {"elapsed": {"repr": "0:00:05.357588", "seconds": 5.357588}, "exception": null, "extra": {}, "file": {"name": "error_recovery.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\error_recovery.py"}, "function": "register_strategy", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 422, "message": "注册恢复策略: ComponentRestartStrategy", "module": "error_recovery", "name": "infrastructure.error_recovery", "process": {"id": 36960, "name": "MainProcess"}, "thread": {"id": 62948, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:31:25.223767+08:00", "timestamp": **********.223767}}}
{"text": "2025-06-21 01:31:25.228 | INFO | infrastructure.graceful_degradation:register_strategy:339 | 注册降级策略: performance_degradation\n", "record": {"elapsed": {"repr": "0:00:05.362101", "seconds": 5.362101}, "exception": null, "extra": {}, "file": {"name": "graceful_degradation.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\graceful_degradation.py"}, "function": "register_strategy", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 339, "message": "注册降级策略: performance_degradation", "module": "graceful_degradation", "name": "infrastructure.graceful_degradation", "process": {"id": 36960, "name": "MainProcess"}, "thread": {"id": 62948, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:31:25.228280+08:00", "timestamp": **********.22828}}}
{"text": "2025-06-21 01:31:25.231 | INFO | infrastructure.graceful_degradation:register_strategy:339 | 注册降级策略: load_based_degradation\n", "record": {"elapsed": {"repr": "0:00:05.365112", "seconds": 5.365112}, "exception": null, "extra": {}, "file": {"name": "graceful_degradation.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\graceful_degradation.py"}, "function": "register_strategy", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 339, "message": "注册降级策略: load_based_degradation", "module": "graceful_degradation", "name": "infrastructure.graceful_degradation", "process": {"id": 36960, "name": "MainProcess"}, "thread": {"id": 62948, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:31:25.231291+08:00", "timestamp": **********.231291}}}
{"text": "2025-06-21 01:31:25.247 | INFO | infrastructure.test_error_handling_core:__init__:78 | ErrorHandlingCoreTestRunner initialized\n", "record": {"elapsed": {"repr": "0:00:05.381152", "seconds": 5.381152}, "exception": null, "extra": {}, "file": {"name": "test_error_handling_core.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\test_error_handling_core.py"}, "function": "__init__", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 78, "message": "ErrorHandlingCoreTestRunner initialized", "module": "test_error_handling_core", "name": "infrastructure.test_error_handling_core", "process": {"id": 36960, "name": "MainProcess"}, "thread": {"id": 62948, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:31:25.247331+08:00", "timestamp": **********.247331}}}
{"text": "2025-06-21 01:31:25.437 | INFO | infrastructure.test_error_handling_integration:__init__:79 | ErrorHandlingIntegrationTestRunner initialized\n", "record": {"elapsed": {"repr": "0:00:05.571487", "seconds": 5.571487}, "exception": null, "extra": {}, "file": {"name": "test_error_handling_integration.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\test_error_handling_integration.py"}, "function": "__init__", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 79, "message": "ErrorHandlingIntegrationTestRunner initialized", "module": "test_error_handling_integration", "name": "infrastructure.test_error_handling_integration", "process": {"id": 36960, "name": "MainProcess"}, "thread": {"id": 62948, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:31:25.437666+08:00", "timestamp": **********.437666}}}
{"text": "2025-06-21 01:31:28.959 | INFO | core.config:__init__:43 | 配置已加载: D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\config\\config.ini\n", "record": {"elapsed": {"repr": "0:00:09.093280", "seconds": 9.09328}, "exception": null, "extra": {}, "file": {"name": "config.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\core\\config.py"}, "function": "__init__", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 43, "message": "配置已加载: D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\config\\config.ini", "module": "config", "name": "core.config", "process": {"id": 36960, "name": "MainProcess"}, "thread": {"id": 62948, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:31:28.959459+08:00", "timestamp": 1750440688.959459}}}
{"text": "2025-06-21 01:31:50.341 | INFO | data.clients.influxdb_client:_connect:100 | InfluxDB连接建立成功\n", "record": {"elapsed": {"repr": "0:00:30.475386", "seconds": 30.475386}, "exception": null, "extra": {"component": "InfluxDBClient"}, "file": {"name": "influxdb_client.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\clients\\influxdb_client.py"}, "function": "_connect", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 100, "message": "InfluxDB连接建立成功", "module": "influxdb_client", "name": "data.clients.influxdb_client", "process": {"id": 36960, "name": "MainProcess"}, "thread": {"id": 62948, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:31:50.341565+08:00", "timestamp": 1750440710.341565}}}
{"text": "2025-06-21 01:31:50.344 | INFO | data.clients.influxdb_client:__init__:75 | InfluxDB客户端初始化完成: http://localhost:8086, 组织: crypto, 存储桶: market_data\n", "record": {"elapsed": {"repr": "0:00:30.478383", "seconds": 30.478383}, "exception": null, "extra": {"component": "InfluxDBClient"}, "file": {"name": "influxdb_client.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\clients\\influxdb_client.py"}, "function": "__init__", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 75, "message": "InfluxDB客户端初始化完成: http://localhost:8086, 组织: crypto, 存储桶: market_data", "module": "influxdb_client", "name": "data.clients.influxdb_client", "process": {"id": 36960, "name": "MainProcess"}, "thread": {"id": 62948, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:31:50.344562+08:00", "timestamp": 1750440710.344562}}}
{"text": "2025-06-21 01:31:50.889 | INFO | data.clients.mysql_client:_connect:130 | MySQL连接建立成功\n", "record": {"elapsed": {"repr": "0:00:31.023556", "seconds": 31.023556}, "exception": null, "extra": {"component": "MySQLClient"}, "file": {"name": "mysql_client.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\clients\\mysql_client.py"}, "function": "_connect", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 130, "message": "MySQL连接建立成功", "module": "mysql_client", "name": "data.clients.mysql_client", "process": {"id": 36960, "name": "MainProcess"}, "thread": {"id": 62948, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:31:50.889735+08:00", "timestamp": 1750440710.889735}}}
{"text": "2025-06-21 01:31:51.250 | INFO | data.clients.mysql_client:_init_tables:187 | 数据库表初始化完成\n", "record": {"elapsed": {"repr": "0:00:31.383986", "seconds": 31.383986}, "exception": null, "extra": {"component": "MySQLClient"}, "file": {"name": "mysql_client.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\clients\\mysql_client.py"}, "function": "_init_tables", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 187, "message": "数据库表初始化完成", "module": "mysql_client", "name": "data.clients.mysql_client", "process": {"id": 36960, "name": "MainProcess"}, "thread": {"id": 62948, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:31:51.250165+08:00", "timestamp": 1750440711.250165}}}
{"text": "2025-06-21 01:31:51.251 | INFO | data.clients.mysql_client:__init__:103 | MySQL客户端初始化完成: localhost:3306/crypto_trading\n", "record": {"elapsed": {"repr": "0:00:31.385497", "seconds": 31.385497}, "exception": null, "extra": {"component": "MySQLClient"}, "file": {"name": "mysql_client.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\clients\\mysql_client.py"}, "function": "__init__", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 103, "message": "MySQL客户端初始化完成: localhost:3306/crypto_trading", "module": "mysql_client", "name": "data.clients.mysql_client", "process": {"id": 36960, "name": "MainProcess"}, "thread": {"id": 62948, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:31:51.251676+08:00", "timestamp": 1750440711.251676}}}
{"text": "2025-06-21 01:31:52.176 | INFO | ml.models.versioning:__init__:56 | 模型版本管理器初始化完成\n", "record": {"elapsed": {"repr": "0:00:32.309892", "seconds": 32.309892}, "exception": null, "extra": {"component": "ModelVersionManager"}, "file": {"name": "versioning.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\ml\\models\\versioning.py"}, "function": "__init__", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 56, "message": "模型版本管理器初始化完成", "module": "versioning", "name": "ml.models.versioning", "process": {"id": 36960, "name": "MainProcess"}, "thread": {"id": 62948, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:31:52.176071+08:00", "timestamp": 1750440712.176071}}}
{"text": "2025-06-21 01:31:52.200 | INFO | ml.models.online_learner:__init__:102 | 在线学习器初始化完成\n", "record": {"elapsed": {"repr": "0:00:32.334765", "seconds": 32.334765}, "exception": null, "extra": {"component": "OnlineLearner"}, "file": {"name": "online_learner.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\ml\\models\\online_learner.py"}, "function": "__init__", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 102, "message": "在线学习器初始化完成", "module": "online_learner", "name": "ml.models.online_learner", "process": {"id": 36960, "name": "MainProcess"}, "thread": {"id": 62948, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:31:52.200944+08:00", "timestamp": 1750440712.200944}}}
{"text": "2025-06-21 01:31:52.241 | INFO | strategy.base:__init__:54 | 基础策略初始化: UnifiedMLStrategy\n", "record": {"elapsed": {"repr": "0:00:32.375030", "seconds": 32.37503}, "exception": null, "extra": {"component": "UnifiedMLStrategy"}, "file": {"name": "base.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\strategy\\base.py"}, "function": "__init__", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 54, "message": "基础策略初始化: UnifiedMLStrategy", "module": "base", "name": "strategy.base", "process": {"id": 36960, "name": "MainProcess"}, "thread": {"id": 62948, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:31:52.241209+08:00", "timestamp": 1750440712.241209}}}
{"text": "2025-06-21 01:31:52.249 | INFO | strategy.unified_ml:__init__:86 | 统一ML策略初始化完成，支持交易对: ['BTCUSDT', 'ETHUSDT']\n", "record": {"elapsed": {"repr": "0:00:32.383142", "seconds": 32.383142}, "exception": null, "extra": {"component": "UnifiedMLStrategy"}, "file": {"name": "unified_ml.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\strategy\\unified_ml.py"}, "function": "__init__", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 86, "message": "统一ML策略初始化完成，支持交易对: ['BTCUSDT', 'ETHUSDT']", "module": "unified_ml", "name": "strategy.unified_ml", "process": {"id": 36960, "name": "MainProcess"}, "thread": {"id": 62948, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:31:52.249321+08:00", "timestamp": 1750440712.249321}}}
{"text": "2025-06-21 01:31:52.249 | INFO | __main__:start:85 | 启动交易策略应用\n", "record": {"elapsed": {"repr": "0:00:32.383654", "seconds": 32.383654}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\main.py"}, "function": "start", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 85, "message": "启动交易策略应用", "module": "main", "name": "__main__", "process": {"id": 36960, "name": "MainProcess"}, "thread": {"id": 62948, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:31:52.249833+08:00", "timestamp": 1750440712.249833}}}
{"text": "2025-06-21 01:31:52.260 | INFO | __main__:start:90 | 未找到有效模型，开始训练...\n", "record": {"elapsed": {"repr": "0:00:32.394246", "seconds": 32.394246}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\main.py"}, "function": "start", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 90, "message": "未找到有效模型，开始训练...", "module": "main", "name": "__main__", "process": {"id": 36960, "name": "MainProcess"}, "thread": {"id": 62948, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:31:52.260425+08:00", "timestamp": 1750440712.260425}}}
{"text": "2025-06-21 01:31:52.263 | INFO | data.real_data_loader:__init__:60 | 数据加载器初始化完成，支持交易对: ['BTCUSDT', 'ETHUSDT'], 时间周期: ['1m', '5m', '15m', '1h', '4h', '1d']\n", "record": {"elapsed": {"repr": "0:00:32.397243", "seconds": 32.397243}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "__init__", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 60, "message": "数据加载器初始化完成，支持交易对: ['BTCUSDT', 'ETHUSDT'], 时间周期: ['1m', '5m', '15m', '1h', '4h', '1d']", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 36960, "name": "MainProcess"}, "thread": {"id": 62948, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:31:52.263422+08:00", "timestamp": 1750440712.263422}}}
{"text": "2025-06-21 01:31:52.263 | INFO | __main__:train_model:179 | 加载训练数据...\n", "record": {"elapsed": {"repr": "0:00:32.397243", "seconds": 32.397243}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\main.py"}, "function": "train_model", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 179, "message": "加载训练数据...", "module": "main", "name": "__main__", "process": {"id": 36960, "name": "MainProcess"}, "thread": {"id": 62948, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:31:52.263422+08:00", "timestamp": 1750440712.263422}}}
{"text": "2025-06-21 01:31:52.264 | INFO | data.real_data_loader:load_training_data:86 | 开始加载训练数据: 2025-03-23 01:31:52.264424 到 2025-06-21 01:31:52.264424\n", "record": {"elapsed": {"repr": "0:00:32.398245", "seconds": 32.398245}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "load_training_data", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 86, "message": "开始加载训练数据: 2025-03-23 01:31:52.264424 到 2025-06-21 01:31:52.264424", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 36960, "name": "MainProcess"}, "thread": {"id": 62948, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:31:52.264424+08:00", "timestamp": 1750440712.264424}}}
{"text": "2025-06-21 01:31:53.038 | WARNING | data.clients.influxdb_client:query_kline_data:224 | 未找到K线数据: BTCUSDT 1m\n", "record": {"elapsed": {"repr": "0:00:33.172517", "seconds": 33.172517}, "exception": null, "extra": {"component": "InfluxDBClient"}, "file": {"name": "influxdb_client.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\clients\\influxdb_client.py"}, "function": "query_kline_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 224, "message": "未找到K线数据: BTCUSDT 1m", "module": "influxdb_client", "name": "data.clients.influxdb_client", "process": {"id": 36960, "name": "MainProcess"}, "thread": {"id": 62948, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:31:53.038696+08:00", "timestamp": 1750440713.038696}}}
{"text": "2025-06-21 01:31:53.038 | WARNING | data.real_data_loader:_load_kline_data:145 | InfluxDB中未找到数据，尝试其他数据源: BTCUSDT 1m\n", "record": {"elapsed": {"repr": "0:00:33.172517", "seconds": 33.172517}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "_load_kline_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 145, "message": "InfluxDB中未找到数据，尝试其他数据源: BTCUSDT 1m", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 36960, "name": "MainProcess"}, "thread": {"id": 62948, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:31:53.038696+08:00", "timestamp": 1750440713.038696}}}
{"text": "2025-06-21 01:31:53.039 | WARNING | data.real_data_loader:load_training_data:108 | 未找到K线数据: BTCUSDT_1m\n", "record": {"elapsed": {"repr": "0:00:33.173524", "seconds": 33.173524}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "load_training_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 108, "message": "未找到K线数据: BTCUSDT_1m", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 36960, "name": "MainProcess"}, "thread": {"id": 62948, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:31:53.039703+08:00", "timestamp": 1750440713.039703}}}
{"text": "2025-06-21 01:31:53.118 | WARNING | data.clients.influxdb_client:query_kline_data:224 | 未找到K线数据: BTCUSDT 5m\n", "record": {"elapsed": {"repr": "0:00:33.252061", "seconds": 33.252061}, "exception": null, "extra": {"component": "InfluxDBClient"}, "file": {"name": "influxdb_client.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\clients\\influxdb_client.py"}, "function": "query_kline_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 224, "message": "未找到K线数据: BTCUSDT 5m", "module": "influxdb_client", "name": "data.clients.influxdb_client", "process": {"id": 36960, "name": "MainProcess"}, "thread": {"id": 62948, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:31:53.118240+08:00", "timestamp": 1750440713.11824}}}
{"text": "2025-06-21 01:31:53.118 | WARNING | data.real_data_loader:_load_kline_data:145 | InfluxDB中未找到数据，尝试其他数据源: BTCUSDT 5m\n", "record": {"elapsed": {"repr": "0:00:33.252061", "seconds": 33.252061}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "_load_kline_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 145, "message": "InfluxDB中未找到数据，尝试其他数据源: BTCUSDT 5m", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 36960, "name": "MainProcess"}, "thread": {"id": 62948, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:31:53.118240+08:00", "timestamp": 1750440713.11824}}}
{"text": "2025-06-21 01:31:53.119 | WARNING | data.real_data_loader:load_training_data:108 | 未找到K线数据: BTCUSDT_5m\n", "record": {"elapsed": {"repr": "0:00:33.253567", "seconds": 33.253567}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "load_training_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 108, "message": "未找到K线数据: BTCUSDT_5m", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 36960, "name": "MainProcess"}, "thread": {"id": 62948, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:31:53.119746+08:00", "timestamp": 1750440713.119746}}}
{"text": "2025-06-21 01:31:53.151 | WARNING | data.clients.influxdb_client:query_kline_data:224 | 未找到K线数据: BTCUSDT 15m\n", "record": {"elapsed": {"repr": "0:00:33.285275", "seconds": 33.285275}, "exception": null, "extra": {"component": "InfluxDBClient"}, "file": {"name": "influxdb_client.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\clients\\influxdb_client.py"}, "function": "query_kline_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 224, "message": "未找到K线数据: BTCUSDT 15m", "module": "influxdb_client", "name": "data.clients.influxdb_client", "process": {"id": 36960, "name": "MainProcess"}, "thread": {"id": 62948, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:31:53.151454+08:00", "timestamp": 1750440713.151454}}}
{"text": "2025-06-21 01:31:53.153 | WARNING | data.real_data_loader:_load_kline_data:145 | InfluxDB中未找到数据，尝试其他数据源: BTCUSDT 15m\n", "record": {"elapsed": {"repr": "0:00:33.287292", "seconds": 33.287292}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "_load_kline_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 145, "message": "InfluxDB中未找到数据，尝试其他数据源: BTCUSDT 15m", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 36960, "name": "MainProcess"}, "thread": {"id": 62948, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:31:53.153471+08:00", "timestamp": 1750440713.153471}}}
{"text": "2025-06-21 01:31:53.154 | WARNING | data.real_data_loader:load_training_data:108 | 未找到K线数据: BTCUSDT_15m\n", "record": {"elapsed": {"repr": "0:00:33.288806", "seconds": 33.288806}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "load_training_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 108, "message": "未找到K线数据: BTCUSDT_15m", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 36960, "name": "MainProcess"}, "thread": {"id": 62948, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:31:53.154985+08:00", "timestamp": 1750440713.154985}}}
{"text": "2025-06-21 01:31:53.313 | WARNING | data.clients.influxdb_client:query_kline_data:224 | 未找到K线数据: BTCUSDT 1h\n", "record": {"elapsed": {"repr": "0:00:33.447239", "seconds": 33.447239}, "exception": null, "extra": {"component": "InfluxDBClient"}, "file": {"name": "influxdb_client.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\clients\\influxdb_client.py"}, "function": "query_kline_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 224, "message": "未找到K线数据: BTCUSDT 1h", "module": "influxdb_client", "name": "data.clients.influxdb_client", "process": {"id": 36960, "name": "MainProcess"}, "thread": {"id": 62948, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:31:53.313418+08:00", "timestamp": 1750440713.313418}}}
{"text": "2025-06-21 01:31:53.314 | WARNING | data.real_data_loader:_load_kline_data:145 | InfluxDB中未找到数据，尝试其他数据源: BTCUSDT 1h\n", "record": {"elapsed": {"repr": "0:00:33.448235", "seconds": 33.448235}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "_load_kline_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 145, "message": "InfluxDB中未找到数据，尝试其他数据源: BTCUSDT 1h", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 36960, "name": "MainProcess"}, "thread": {"id": 62948, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:31:53.314414+08:00", "timestamp": 1750440713.314414}}}
{"text": "2025-06-21 01:31:53.315 | WARNING | data.real_data_loader:load_training_data:108 | 未找到K线数据: BTCUSDT_1h\n", "record": {"elapsed": {"repr": "0:00:33.449236", "seconds": 33.449236}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "load_training_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 108, "message": "未找到K线数据: BTCUSDT_1h", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 36960, "name": "MainProcess"}, "thread": {"id": 62948, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:31:53.315415+08:00", "timestamp": 1750440713.315415}}}
{"text": "2025-06-21 01:31:53.361 | WARNING | data.clients.influxdb_client:query_kline_data:224 | 未找到K线数据: BTCUSDT 4h\n", "record": {"elapsed": {"repr": "0:00:33.495471", "seconds": 33.495471}, "exception": null, "extra": {"component": "InfluxDBClient"}, "file": {"name": "influxdb_client.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\clients\\influxdb_client.py"}, "function": "query_kline_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 224, "message": "未找到K线数据: BTCUSDT 4h", "module": "influxdb_client", "name": "data.clients.influxdb_client", "process": {"id": 36960, "name": "MainProcess"}, "thread": {"id": 62948, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:31:53.361650+08:00", "timestamp": 1750440713.36165}}}
{"text": "2025-06-21 01:31:53.363 | WARNING | data.real_data_loader:_load_kline_data:145 | InfluxDB中未找到数据，尝试其他数据源: BTCUSDT 4h\n", "record": {"elapsed": {"repr": "0:00:33.497481", "seconds": 33.497481}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "_load_kline_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 145, "message": "InfluxDB中未找到数据，尝试其他数据源: BTCUSDT 4h", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 36960, "name": "MainProcess"}, "thread": {"id": 62948, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:31:53.363660+08:00", "timestamp": 1750440713.36366}}}
{"text": "2025-06-21 01:31:53.363 | WARNING | data.real_data_loader:load_training_data:108 | 未找到K线数据: BTCUSDT_4h\n", "record": {"elapsed": {"repr": "0:00:33.497481", "seconds": 33.497481}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "load_training_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 108, "message": "未找到K线数据: BTCUSDT_4h", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 36960, "name": "MainProcess"}, "thread": {"id": 62948, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:31:53.363660+08:00", "timestamp": 1750440713.36366}}}
{"text": "2025-06-21 01:31:53.507 | WARNING | data.clients.influxdb_client:query_kline_data:224 | 未找到K线数据: BTCUSDT 1d\n", "record": {"elapsed": {"repr": "0:00:33.641216", "seconds": 33.641216}, "exception": null, "extra": {"component": "InfluxDBClient"}, "file": {"name": "influxdb_client.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\clients\\influxdb_client.py"}, "function": "query_kline_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 224, "message": "未找到K线数据: BTCUSDT 1d", "module": "influxdb_client", "name": "data.clients.influxdb_client", "process": {"id": 36960, "name": "MainProcess"}, "thread": {"id": 62948, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:31:53.507395+08:00", "timestamp": 1750440713.507395}}}
{"text": "2025-06-21 01:31:53.508 | WARNING | data.real_data_loader:_load_kline_data:145 | InfluxDB中未找到数据，尝试其他数据源: BTCUSDT 1d\n", "record": {"elapsed": {"repr": "0:00:33.642732", "seconds": 33.642732}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "_load_kline_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 145, "message": "InfluxDB中未找到数据，尝试其他数据源: BTCUSDT 1d", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 36960, "name": "MainProcess"}, "thread": {"id": 62948, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:31:53.508911+08:00", "timestamp": 1750440713.508911}}}
{"text": "2025-06-21 01:31:53.508 | WARNING | data.real_data_loader:load_training_data:108 | 未找到K线数据: BTCUSDT_1d\n", "record": {"elapsed": {"repr": "0:00:33.642732", "seconds": 33.642732}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "load_training_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 108, "message": "未找到K线数据: BTCUSDT_1d", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 36960, "name": "MainProcess"}, "thread": {"id": 62948, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:31:53.508911+08:00", "timestamp": 1750440713.508911}}}
{"text": "2025-06-21 01:31:53.632 | WARNING | data.clients.influxdb_client:query_kline_data:224 | 未找到K线数据: ETHUSDT 1m\n", "record": {"elapsed": {"repr": "0:00:33.766461", "seconds": 33.766461}, "exception": null, "extra": {"component": "InfluxDBClient"}, "file": {"name": "influxdb_client.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\clients\\influxdb_client.py"}, "function": "query_kline_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 224, "message": "未找到K线数据: ETHUSDT 1m", "module": "influxdb_client", "name": "data.clients.influxdb_client", "process": {"id": 36960, "name": "MainProcess"}, "thread": {"id": 62948, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:31:53.632640+08:00", "timestamp": 1750440713.63264}}}
{"text": "2025-06-21 01:31:53.634 | WARNING | data.real_data_loader:_load_kline_data:145 | InfluxDB中未找到数据，尝试其他数据源: ETHUSDT 1m\n", "record": {"elapsed": {"repr": "0:00:33.768467", "seconds": 33.768467}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "_load_kline_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 145, "message": "InfluxDB中未找到数据，尝试其他数据源: ETHUSDT 1m", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 36960, "name": "MainProcess"}, "thread": {"id": 62948, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:31:53.634646+08:00", "timestamp": 1750440713.634646}}}
{"text": "2025-06-21 01:31:53.635 | WARNING | data.real_data_loader:load_training_data:108 | 未找到K线数据: ETHUSDT_1m\n", "record": {"elapsed": {"repr": "0:00:33.769464", "seconds": 33.769464}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "load_training_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 108, "message": "未找到K线数据: ETHUSDT_1m", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 36960, "name": "MainProcess"}, "thread": {"id": 62948, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:31:53.635643+08:00", "timestamp": 1750440713.635643}}}
{"text": "2025-06-21 01:31:53.870 | WARNING | data.clients.influxdb_client:query_kline_data:224 | 未找到K线数据: ETHUSDT 5m\n", "record": {"elapsed": {"repr": "0:00:34.004426", "seconds": 34.004426}, "exception": null, "extra": {"component": "InfluxDBClient"}, "file": {"name": "influxdb_client.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\clients\\influxdb_client.py"}, "function": "query_kline_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 224, "message": "未找到K线数据: ETHUSDT 5m", "module": "influxdb_client", "name": "data.clients.influxdb_client", "process": {"id": 36960, "name": "MainProcess"}, "thread": {"id": 62948, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:31:53.870605+08:00", "timestamp": 1750440713.870605}}}
{"text": "2025-06-21 01:31:53.904 | WARNING | data.real_data_loader:_load_kline_data:145 | InfluxDB中未找到数据，尝试其他数据源: ETHUSDT 5m\n", "record": {"elapsed": {"repr": "0:00:34.038542", "seconds": 34.038542}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "_load_kline_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 145, "message": "InfluxDB中未找到数据，尝试其他数据源: ETHUSDT 5m", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 36960, "name": "MainProcess"}, "thread": {"id": 62948, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:31:53.904721+08:00", "timestamp": 1750440713.904721}}}
{"text": "2025-06-21 01:31:54.001 | WARNING | data.real_data_loader:load_training_data:108 | 未找到K线数据: ETHUSDT_5m\n", "record": {"elapsed": {"repr": "0:00:34.134835", "seconds": 34.134835}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "load_training_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 108, "message": "未找到K线数据: ETHUSDT_5m", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 36960, "name": "MainProcess"}, "thread": {"id": 62948, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:31:54.001014+08:00", "timestamp": 1750440714.001014}}}
{"text": "2025-06-21 01:31:54.328 | WARNING | data.clients.influxdb_client:query_kline_data:224 | 未找到K线数据: ETHUSDT 15m\n", "record": {"elapsed": {"repr": "0:00:34.462114", "seconds": 34.462114}, "exception": null, "extra": {"component": "InfluxDBClient"}, "file": {"name": "influxdb_client.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\clients\\influxdb_client.py"}, "function": "query_kline_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 224, "message": "未找到K线数据: ETHUSDT 15m", "module": "influxdb_client", "name": "data.clients.influxdb_client", "process": {"id": 36960, "name": "MainProcess"}, "thread": {"id": 62948, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:31:54.328293+08:00", "timestamp": 1750440714.328293}}}
{"text": "2025-06-21 01:31:54.329 | WARNING | data.real_data_loader:_load_kline_data:145 | InfluxDB中未找到数据，尝试其他数据源: ETHUSDT 15m\n", "record": {"elapsed": {"repr": "0:00:34.463114", "seconds": 34.463114}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "_load_kline_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 145, "message": "InfluxDB中未找到数据，尝试其他数据源: ETHUSDT 15m", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 36960, "name": "MainProcess"}, "thread": {"id": 62948, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:31:54.329293+08:00", "timestamp": 1750440714.329293}}}
{"text": "2025-06-21 01:31:54.330 | WARNING | data.real_data_loader:load_training_data:108 | 未找到K线数据: ETHUSDT_15m\n", "record": {"elapsed": {"repr": "0:00:34.464133", "seconds": 34.464133}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "load_training_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 108, "message": "未找到K线数据: ETHUSDT_15m", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 36960, "name": "MainProcess"}, "thread": {"id": 62948, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:31:54.330312+08:00", "timestamp": 1750440714.330312}}}
{"text": "2025-06-21 01:31:54.382 | WARNING | data.clients.influxdb_client:query_kline_data:224 | 未找到K线数据: ETHUSDT 1h\n", "record": {"elapsed": {"repr": "0:00:34.516508", "seconds": 34.516508}, "exception": null, "extra": {"component": "InfluxDBClient"}, "file": {"name": "influxdb_client.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\clients\\influxdb_client.py"}, "function": "query_kline_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 224, "message": "未找到K线数据: ETHUSDT 1h", "module": "influxdb_client", "name": "data.clients.influxdb_client", "process": {"id": 36960, "name": "MainProcess"}, "thread": {"id": 62948, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:31:54.382687+08:00", "timestamp": 1750440714.382687}}}
{"text": "2025-06-21 01:31:54.383 | WARNING | data.real_data_loader:_load_kline_data:145 | InfluxDB中未找到数据，尝试其他数据源: ETHUSDT 1h\n", "record": {"elapsed": {"repr": "0:00:34.517518", "seconds": 34.517518}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "_load_kline_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 145, "message": "InfluxDB中未找到数据，尝试其他数据源: ETHUSDT 1h", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 36960, "name": "MainProcess"}, "thread": {"id": 62948, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:31:54.383697+08:00", "timestamp": 1750440714.383697}}}
{"text": "2025-06-21 01:31:54.384 | WARNING | data.real_data_loader:load_training_data:108 | 未找到K线数据: ETHUSDT_1h\n", "record": {"elapsed": {"repr": "0:00:34.518507", "seconds": 34.518507}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "load_training_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 108, "message": "未找到K线数据: ETHUSDT_1h", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 36960, "name": "MainProcess"}, "thread": {"id": 62948, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:31:54.384686+08:00", "timestamp": 1750440714.384686}}}
{"text": "2025-06-21 01:31:54.420 | WARNING | data.clients.influxdb_client:query_kline_data:224 | 未找到K线数据: ETHUSDT 4h\n", "record": {"elapsed": {"repr": "0:00:34.554758", "seconds": 34.554758}, "exception": null, "extra": {"component": "InfluxDBClient"}, "file": {"name": "influxdb_client.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\clients\\influxdb_client.py"}, "function": "query_kline_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 224, "message": "未找到K线数据: ETHUSDT 4h", "module": "influxdb_client", "name": "data.clients.influxdb_client", "process": {"id": 36960, "name": "MainProcess"}, "thread": {"id": 62948, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:31:54.420937+08:00", "timestamp": 1750440714.420937}}}
{"text": "2025-06-21 01:31:54.422 | WARNING | data.real_data_loader:_load_kline_data:145 | InfluxDB中未找到数据，尝试其他数据源: ETHUSDT 4h\n", "record": {"elapsed": {"repr": "0:00:34.556339", "seconds": 34.556339}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "_load_kline_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 145, "message": "InfluxDB中未找到数据，尝试其他数据源: ETHUSDT 4h", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 36960, "name": "MainProcess"}, "thread": {"id": 62948, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:31:54.422518+08:00", "timestamp": 1750440714.422518}}}
{"text": "2025-06-21 01:31:54.423 | WARNING | data.real_data_loader:load_training_data:108 | 未找到K线数据: ETHUSDT_4h\n", "record": {"elapsed": {"repr": "0:00:34.557300", "seconds": 34.5573}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "load_training_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 108, "message": "未找到K线数据: ETHUSDT_4h", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 36960, "name": "MainProcess"}, "thread": {"id": 62948, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:31:54.423479+08:00", "timestamp": 1750440714.423479}}}
{"text": "2025-06-21 01:31:54.493 | WARNING | data.clients.influxdb_client:query_kline_data:224 | 未找到K线数据: ETHUSDT 1d\n", "record": {"elapsed": {"repr": "0:00:34.627209", "seconds": 34.627209}, "exception": null, "extra": {"component": "InfluxDBClient"}, "file": {"name": "influxdb_client.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\clients\\influxdb_client.py"}, "function": "query_kline_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 224, "message": "未找到K线数据: ETHUSDT 1d", "module": "influxdb_client", "name": "data.clients.influxdb_client", "process": {"id": 36960, "name": "MainProcess"}, "thread": {"id": 62948, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:31:54.493388+08:00", "timestamp": 1750440714.493388}}}
{"text": "2025-06-21 01:31:54.500 | WARNING | data.real_data_loader:_load_kline_data:145 | InfluxDB中未找到数据，尝试其他数据源: ETHUSDT 1d\n", "record": {"elapsed": {"repr": "0:00:34.633949", "seconds": 34.633949}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "_load_kline_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 145, "message": "InfluxDB中未找到数据，尝试其他数据源: ETHUSDT 1d", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 36960, "name": "MainProcess"}, "thread": {"id": 62948, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:31:54.500128+08:00", "timestamp": 1750440714.500128}}}
{"text": "2025-06-21 01:31:54.505 | WARNING | data.real_data_loader:load_training_data:108 | 未找到K线数据: ETHUSDT_1d\n", "record": {"elapsed": {"repr": "0:00:34.639483", "seconds": 34.639483}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "load_training_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 108, "message": "未找到K线数据: ETHUSDT_1d", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 36960, "name": "MainProcess"}, "thread": {"id": 62948, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:31:54.505662+08:00", "timestamp": 1750440714.505662}}}
{"text": "2025-06-21 01:31:54.505 | ERROR | data.real_data_loader:load_training_data:116 | 未加载到任何训练数据\n", "record": {"elapsed": {"repr": "0:00:34.639483", "seconds": 34.639483}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "load_training_data", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 116, "message": "未加载到任何训练数据", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 36960, "name": "MainProcess"}, "thread": {"id": 62948, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:31:54.505662+08:00", "timestamp": 1750440714.505662}}}
{"text": "2025-06-21 01:31:54.508 | INFO | ml.models.model_trainer:__init__:94 | 模型训练器初始化完成\n", "record": {"elapsed": {"repr": "0:00:34.642000", "seconds": 34.642}, "exception": null, "extra": {"component": "ModelTrainer"}, "file": {"name": "model_trainer.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\ml\\models\\model_trainer.py"}, "function": "__init__", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 94, "message": "模型训练器初始化完成", "module": "model_trainer", "name": "ml.models.model_trainer", "process": {"id": 36960, "name": "MainProcess"}, "thread": {"id": 62948, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:31:54.508179+08:00", "timestamp": 1750440714.508179}}}
{"text": "2025-06-21 01:31:54.509 | INFO | __main__:train_model:186 | 开始训练模型...\n", "record": {"elapsed": {"repr": "0:00:34.642999", "seconds": 34.642999}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\main.py"}, "function": "train_model", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 186, "message": "开始训练模型...", "module": "main", "name": "__main__", "process": {"id": 36960, "name": "MainProcess"}, "thread": {"id": 62948, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:31:54.509178+08:00", "timestamp": 1750440714.509178}}}
{"text": "2025-06-21 01:31:54.509 | ERROR | ml.models.model_trainer:train:149 | 模型训练失败: 训练数据为空\n", "record": {"elapsed": {"repr": "0:00:34.642999", "seconds": 34.642999}, "exception": null, "extra": {"component": "ModelTrainer"}, "file": {"name": "model_trainer.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\ml\\models\\model_trainer.py"}, "function": "train", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 149, "message": "模型训练失败: 训练数据为空", "module": "model_trainer", "name": "ml.models.model_trainer", "process": {"id": 36960, "name": "MainProcess"}, "thread": {"id": 62948, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:31:54.509178+08:00", "timestamp": 1750440714.509178}}}
{"text": "2025-06-21 01:31:54.511 | INFO | __main__:train_model:189 | 模型训练完成\n", "record": {"elapsed": {"repr": "0:00:34.645002", "seconds": 34.645002}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\main.py"}, "function": "train_model", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 189, "message": "模型训练完成", "module": "main", "name": "__main__", "process": {"id": 36960, "name": "MainProcess"}, "thread": {"id": 62948, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:31:54.511181+08:00", "timestamp": 1750440714.511181}}}
{"text": "2025-06-21 01:31:54.513 | INFO | ml.models.prediction:__init__:76 | 预测引擎初始化完成\n", "record": {"elapsed": {"repr": "0:00:34.647527", "seconds": 34.647527}, "exception": null, "extra": {"component": "PredictionEngine"}, "file": {"name": "prediction.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\ml\\models\\prediction.py"}, "function": "__init__", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 76, "message": "预测引擎初始化完成", "module": "prediction", "name": "ml.models.prediction", "process": {"id": 36960, "name": "MainProcess"}, "thread": {"id": 62948, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:31:54.513706+08:00", "timestamp": 1750440714.513706}}}
{"text": "2025-06-21 01:31:54.514 | INFO | strategy.unified_ml:set_prediction_engine:96 | 预测引擎已设置\n", "record": {"elapsed": {"repr": "0:00:34.648517", "seconds": 34.648517}, "exception": null, "extra": {"component": "UnifiedMLStrategy"}, "file": {"name": "unified_ml.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\strategy\\unified_ml.py"}, "function": "set_prediction_engine", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 96, "message": "预测引擎已设置", "module": "unified_ml", "name": "strategy.unified_ml", "process": {"id": 36960, "name": "MainProcess"}, "thread": {"id": 62948, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:31:54.514696+08:00", "timestamp": 1750440714.514696}}}
{"text": "2025-06-21 01:31:54.534 | ERROR | __main__:main:218 | 应用运行异常: KafkaError{code=_INVALID_ARG,val=-186,str=\"No such configuration property: \"max.poll.records\"\"}\n", "record": {"elapsed": {"repr": "0:00:34.668321", "seconds": 34.668321}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\main.py"}, "function": "main", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 218, "message": "应用运行异常: KafkaError{code=_INVALID_ARG,val=-186,str=\"No such configuration property: \"max.poll.records\"\"}", "module": "main", "name": "__main__", "process": {"id": 36960, "name": "MainProcess"}, "thread": {"id": 62948, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:31:54.534500+08:00", "timestamp": 1750440714.5345}}}
{"text": "2025-06-21 01:31:54.535 | INFO | __main__:stop:202 | 关闭交易策略应用\n", "record": {"elapsed": {"repr": "0:00:34.669321", "seconds": 34.669321}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\main.py"}, "function": "stop", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 202, "message": "关闭交易策略应用", "module": "main", "name": "__main__", "process": {"id": 36960, "name": "MainProcess"}, "thread": {"id": 62948, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:31:54.535500+08:00", "timestamp": 1750440714.5355}}}
{"text": "2025-06-21 01:31:54.537 | INFO | data.clients.mysql_client:close:499 | MySQL连接已关闭\n", "record": {"elapsed": {"repr": "0:00:34.671316", "seconds": 34.671316}, "exception": null, "extra": {"component": "MySQLClient"}, "file": {"name": "mysql_client.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\clients\\mysql_client.py"}, "function": "close", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 499, "message": "MySQL连接已关闭", "module": "mysql_client", "name": "data.clients.mysql_client", "process": {"id": 36960, "name": "MainProcess"}, "thread": {"id": 62948, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:31:54.537495+08:00", "timestamp": 1750440714.537495}}}
{"text": "2025-06-21 01:31:54.539 | INFO | data.clients.influxdb_client:close:340 | InfluxDB连接已关闭\n", "record": {"elapsed": {"repr": "0:00:34.672844", "seconds": 34.672844}, "exception": null, "extra": {"component": "InfluxDBClient"}, "file": {"name": "influxdb_client.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\clients\\influxdb_client.py"}, "function": "close", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 340, "message": "InfluxDB连接已关闭", "module": "influxdb_client", "name": "data.clients.influxdb_client", "process": {"id": 36960, "name": "MainProcess"}, "thread": {"id": 62948, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:31:54.539023+08:00", "timestamp": 1750440714.539023}}}
{"text": "2025-06-21 01:39:32.083 | INFO | infrastructure.logging.logging_core_manager:initialize:261 | Logging system initialized successfully\n", "record": {"elapsed": {"repr": "0:00:04.572879", "seconds": 4.572879}, "exception": null, "extra": {}, "file": {"name": "logging_core_manager.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\logging\\logging_core_manager.py"}, "function": "initialize", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 261, "message": "Logging system initialized successfully", "module": "logging_core_manager", "name": "infrastructure.logging.logging_core_manager", "process": {"id": 44084, "name": "MainProcess"}, "thread": {"id": 48544, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:39:32.083943+08:00", "timestamp": **********.083943}}}
{"text": "2025-06-21 01:39:32.123 | INFO | infrastructure.system_health_monitor:register_health_check:434 | 注册健康检查: system_resources\n", "record": {"elapsed": {"repr": "0:00:04.612066", "seconds": 4.612066}, "exception": null, "extra": {}, "file": {"name": "system_health_monitor.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\system_health_monitor.py"}, "function": "register_health_check", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 434, "message": "注册健康检查: system_resources", "module": "system_health_monitor", "name": "infrastructure.system_health_monitor", "process": {"id": 44084, "name": "MainProcess"}, "thread": {"id": 48544, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:39:32.123130+08:00", "timestamp": **********.12313}}}
{"text": "2025-06-21 01:39:32.131 | INFO | infrastructure.error_recovery:register_strategy:422 | 注册恢复策略: CacheRecoveryStrategy\n", "record": {"elapsed": {"repr": "0:00:04.620927", "seconds": 4.620927}, "exception": null, "extra": {}, "file": {"name": "error_recovery.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\error_recovery.py"}, "function": "register_strategy", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 422, "message": "注册恢复策略: CacheRecoveryStrategy", "module": "error_recovery", "name": "infrastructure.error_recovery", "process": {"id": 44084, "name": "MainProcess"}, "thread": {"id": 48544, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:39:32.131991+08:00", "timestamp": **********.131991}}}
{"text": "2025-06-21 01:39:32.132 | INFO | infrastructure.error_recovery:register_strategy:422 | 注册恢复策略: RetryRecoveryStrategy\n", "record": {"elapsed": {"repr": "0:00:04.621928", "seconds": 4.621928}, "exception": null, "extra": {}, "file": {"name": "error_recovery.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\error_recovery.py"}, "function": "register_strategy", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 422, "message": "注册恢复策略: RetryRecoveryStrategy", "module": "error_recovery", "name": "infrastructure.error_recovery", "process": {"id": 44084, "name": "MainProcess"}, "thread": {"id": 48544, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:39:32.132992+08:00", "timestamp": **********.132992}}}
{"text": "2025-06-21 01:39:32.132 | INFO | infrastructure.error_recovery:register_strategy:422 | 注册恢复策略: FallbackRecoveryStrategy\n", "record": {"elapsed": {"repr": "0:00:04.621928", "seconds": 4.621928}, "exception": null, "extra": {}, "file": {"name": "error_recovery.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\error_recovery.py"}, "function": "register_strategy", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 422, "message": "注册恢复策略: FallbackRecoveryStrategy", "module": "error_recovery", "name": "infrastructure.error_recovery", "process": {"id": 44084, "name": "MainProcess"}, "thread": {"id": 48544, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:39:32.132992+08:00", "timestamp": **********.132992}}}
{"text": "2025-06-21 01:39:32.133 | INFO | infrastructure.error_recovery:register_strategy:422 | 注册恢复策略: ComponentRestartStrategy\n", "record": {"elapsed": {"repr": "0:00:04.622932", "seconds": 4.622932}, "exception": null, "extra": {}, "file": {"name": "error_recovery.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\error_recovery.py"}, "function": "register_strategy", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 422, "message": "注册恢复策略: ComponentRestartStrategy", "module": "error_recovery", "name": "infrastructure.error_recovery", "process": {"id": 44084, "name": "MainProcess"}, "thread": {"id": 48544, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:39:32.133996+08:00", "timestamp": **********.133996}}}
{"text": "2025-06-21 01:39:32.138 | INFO | infrastructure.graceful_degradation:register_strategy:339 | 注册降级策略: performance_degradation\n", "record": {"elapsed": {"repr": "0:00:04.627457", "seconds": 4.627457}, "exception": null, "extra": {}, "file": {"name": "graceful_degradation.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\graceful_degradation.py"}, "function": "register_strategy", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 339, "message": "注册降级策略: performance_degradation", "module": "graceful_degradation", "name": "infrastructure.graceful_degradation", "process": {"id": 44084, "name": "MainProcess"}, "thread": {"id": 48544, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:39:32.138521+08:00", "timestamp": **********.138521}}}
{"text": "2025-06-21 01:39:32.138 | INFO | infrastructure.graceful_degradation:register_strategy:339 | 注册降级策略: load_based_degradation\n", "record": {"elapsed": {"repr": "0:00:04.627457", "seconds": 4.627457}, "exception": null, "extra": {}, "file": {"name": "graceful_degradation.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\graceful_degradation.py"}, "function": "register_strategy", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 339, "message": "注册降级策略: load_based_degradation", "module": "graceful_degradation", "name": "infrastructure.graceful_degradation", "process": {"id": 44084, "name": "MainProcess"}, "thread": {"id": 48544, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:39:32.138521+08:00", "timestamp": **********.138521}}}
{"text": "2025-06-21 01:39:32.157 | INFO | infrastructure.test_error_handling_core:__init__:78 | ErrorHandlingCoreTestRunner initialized\n", "record": {"elapsed": {"repr": "0:00:04.646704", "seconds": 4.646704}, "exception": null, "extra": {}, "file": {"name": "test_error_handling_core.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\test_error_handling_core.py"}, "function": "__init__", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 78, "message": "ErrorHandlingCoreTestRunner initialized", "module": "test_error_handling_core", "name": "infrastructure.test_error_handling_core", "process": {"id": 44084, "name": "MainProcess"}, "thread": {"id": 48544, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:39:32.157768+08:00", "timestamp": **********.157768}}}
{"text": "2025-06-21 01:39:32.290 | INFO | infrastructure.test_error_handling_integration:__init__:79 | ErrorHandlingIntegrationTestRunner initialized\n", "record": {"elapsed": {"repr": "0:00:04.779689", "seconds": 4.779689}, "exception": null, "extra": {}, "file": {"name": "test_error_handling_integration.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\test_error_handling_integration.py"}, "function": "__init__", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 79, "message": "ErrorHandlingIntegrationTestRunner initialized", "module": "test_error_handling_integration", "name": "infrastructure.test_error_handling_integration", "process": {"id": 44084, "name": "MainProcess"}, "thread": {"id": 48544, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:39:32.290753+08:00", "timestamp": **********.290753}}}
{"text": "2025-06-21 01:39:35.564 | INFO | core.config:__init__:43 | 配置已加载: D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\config\\config.ini\n", "record": {"elapsed": {"repr": "0:00:08.053428", "seconds": 8.053428}, "exception": null, "extra": {}, "file": {"name": "config.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\core\\config.py"}, "function": "__init__", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 43, "message": "配置已加载: D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\config\\config.ini", "module": "config", "name": "core.config", "process": {"id": 44084, "name": "MainProcess"}, "thread": {"id": 48544, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:39:35.564492+08:00", "timestamp": 1750441175.564492}}}
{"text": "2025-06-21 01:39:56.641 | INFO | data.clients.influxdb_client:_connect:100 | InfluxDB连接建立成功\n", "record": {"elapsed": {"repr": "0:00:29.129946", "seconds": 29.129946}, "exception": null, "extra": {"component": "InfluxDBClient"}, "file": {"name": "influxdb_client.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\clients\\influxdb_client.py"}, "function": "_connect", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 100, "message": "InfluxDB连接建立成功", "module": "influxdb_client", "name": "data.clients.influxdb_client", "process": {"id": 44084, "name": "MainProcess"}, "thread": {"id": 48544, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:39:56.641010+08:00", "timestamp": 1750441196.64101}}}
{"text": "2025-06-21 01:39:56.642 | INFO | data.clients.influxdb_client:__init__:75 | InfluxDB客户端初始化完成: http://localhost:8086, 组织: crypto, 存储桶: market_data\n", "record": {"elapsed": {"repr": "0:00:29.130943", "seconds": 29.130943}, "exception": null, "extra": {"component": "InfluxDBClient"}, "file": {"name": "influxdb_client.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\clients\\influxdb_client.py"}, "function": "__init__", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 75, "message": "InfluxDB客户端初始化完成: http://localhost:8086, 组织: crypto, 存储桶: market_data", "module": "influxdb_client", "name": "data.clients.influxdb_client", "process": {"id": 44084, "name": "MainProcess"}, "thread": {"id": 48544, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:39:56.642007+08:00", "timestamp": 1750441196.642007}}}
{"text": "2025-06-21 01:39:56.684 | INFO | data.clients.mysql_client:_connect:130 | MySQL连接建立成功\n", "record": {"elapsed": {"repr": "0:00:29.173494", "seconds": 29.173494}, "exception": null, "extra": {"component": "MySQLClient"}, "file": {"name": "mysql_client.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\clients\\mysql_client.py"}, "function": "_connect", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 130, "message": "MySQL连接建立成功", "module": "mysql_client", "name": "data.clients.mysql_client", "process": {"id": 44084, "name": "MainProcess"}, "thread": {"id": 48544, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:39:56.684558+08:00", "timestamp": 1750441196.684558}}}
{"text": "2025-06-21 01:39:56.708 | INFO | data.clients.mysql_client:_init_tables:187 | 数据库表初始化完成\n", "record": {"elapsed": {"repr": "0:00:29.197130", "seconds": 29.19713}, "exception": null, "extra": {"component": "MySQLClient"}, "file": {"name": "mysql_client.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\clients\\mysql_client.py"}, "function": "_init_tables", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 187, "message": "数据库表初始化完成", "module": "mysql_client", "name": "data.clients.mysql_client", "process": {"id": 44084, "name": "MainProcess"}, "thread": {"id": 48544, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:39:56.708194+08:00", "timestamp": 1750441196.708194}}}
{"text": "2025-06-21 01:39:56.709 | INFO | data.clients.mysql_client:__init__:103 | MySQL客户端初始化完成: localhost:3306/crypto_trading\n", "record": {"elapsed": {"repr": "0:00:29.198096", "seconds": 29.198096}, "exception": null, "extra": {"component": "MySQLClient"}, "file": {"name": "mysql_client.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\clients\\mysql_client.py"}, "function": "__init__", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 103, "message": "MySQL客户端初始化完成: localhost:3306/crypto_trading", "module": "mysql_client", "name": "data.clients.mysql_client", "process": {"id": 44084, "name": "MainProcess"}, "thread": {"id": 48544, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:39:56.709160+08:00", "timestamp": 1750441196.70916}}}
{"text": "2025-06-21 01:39:56.717 | INFO | ml.models.versioning:__init__:56 | 模型版本管理器初始化完成\n", "record": {"elapsed": {"repr": "0:00:29.206604", "seconds": 29.206604}, "exception": null, "extra": {"component": "ModelVersionManager"}, "file": {"name": "versioning.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\ml\\models\\versioning.py"}, "function": "__init__", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 56, "message": "模型版本管理器初始化完成", "module": "versioning", "name": "ml.models.versioning", "process": {"id": 44084, "name": "MainProcess"}, "thread": {"id": 48544, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:39:56.717668+08:00", "timestamp": 1750441196.717668}}}
{"text": "2025-06-21 01:39:56.718 | INFO | ml.models.online_learner:__init__:102 | 在线学习器初始化完成\n", "record": {"elapsed": {"repr": "0:00:29.207606", "seconds": 29.207606}, "exception": null, "extra": {"component": "OnlineLearner"}, "file": {"name": "online_learner.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\ml\\models\\online_learner.py"}, "function": "__init__", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 102, "message": "在线学习器初始化完成", "module": "online_learner", "name": "ml.models.online_learner", "process": {"id": 44084, "name": "MainProcess"}, "thread": {"id": 48544, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:39:56.718670+08:00", "timestamp": 1750441196.71867}}}
{"text": "2025-06-21 01:39:56.720 | INFO | strategy.base:__init__:54 | 基础策略初始化: UnifiedMLStrategy\n", "record": {"elapsed": {"repr": "0:00:29.209615", "seconds": 29.209615}, "exception": null, "extra": {"component": "UnifiedMLStrategy"}, "file": {"name": "base.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\strategy\\base.py"}, "function": "__init__", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 54, "message": "基础策略初始化: UnifiedMLStrategy", "module": "base", "name": "strategy.base", "process": {"id": 44084, "name": "MainProcess"}, "thread": {"id": 48544, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:39:56.720679+08:00", "timestamp": 1750441196.720679}}}
{"text": "2025-06-21 01:39:56.721 | INFO | strategy.unified_ml:__init__:86 | 统一ML策略初始化完成，支持交易对: ['BTCUSDT', 'ETHUSDT']\n", "record": {"elapsed": {"repr": "0:00:29.210128", "seconds": 29.210128}, "exception": null, "extra": {"component": "UnifiedMLStrategy"}, "file": {"name": "unified_ml.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\strategy\\unified_ml.py"}, "function": "__init__", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 86, "message": "统一ML策略初始化完成，支持交易对: ['BTCUSDT', 'ETHUSDT']", "module": "unified_ml", "name": "strategy.unified_ml", "process": {"id": 44084, "name": "MainProcess"}, "thread": {"id": 48544, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:39:56.721192+08:00", "timestamp": 1750441196.721192}}}
{"text": "2025-06-21 01:39:56.722 | INFO | __main__:start:85 | 启动交易策略应用\n", "record": {"elapsed": {"repr": "0:00:29.211168", "seconds": 29.211168}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\main.py"}, "function": "start", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 85, "message": "启动交易策略应用", "module": "main", "name": "__main__", "process": {"id": 44084, "name": "MainProcess"}, "thread": {"id": 48544, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:39:56.722232+08:00", "timestamp": 1750441196.722232}}}
{"text": "2025-06-21 01:39:56.725 | INFO | __main__:start:90 | 未找到有效模型，开始训练...\n", "record": {"elapsed": {"repr": "0:00:29.214755", "seconds": 29.214755}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\main.py"}, "function": "start", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 90, "message": "未找到有效模型，开始训练...", "module": "main", "name": "__main__", "process": {"id": 44084, "name": "MainProcess"}, "thread": {"id": 48544, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:39:56.725819+08:00", "timestamp": 1750441196.725819}}}
{"text": "2025-06-21 01:39:56.725 | INFO | data.real_data_loader:__init__:60 | 数据加载器初始化完成，支持交易对: ['BTCUSDT', 'ETHUSDT'], 时间周期: ['1m', '5m', '15m', '1h', '4h', '1d']\n", "record": {"elapsed": {"repr": "0:00:29.214755", "seconds": 29.214755}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "__init__", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 60, "message": "数据加载器初始化完成，支持交易对: ['BTCUSDT', 'ETHUSDT'], 时间周期: ['1m', '5m', '15m', '1h', '4h', '1d']", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 44084, "name": "MainProcess"}, "thread": {"id": 48544, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:39:56.725819+08:00", "timestamp": 1750441196.725819}}}
{"text": "2025-06-21 01:39:56.726 | INFO | __main__:train_model:179 | 加载训练数据...\n", "record": {"elapsed": {"repr": "0:00:29.215681", "seconds": 29.215681}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\main.py"}, "function": "train_model", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 179, "message": "加载训练数据...", "module": "main", "name": "__main__", "process": {"id": 44084, "name": "MainProcess"}, "thread": {"id": 48544, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:39:56.726745+08:00", "timestamp": 1750441196.726745}}}
{"text": "2025-06-21 01:39:56.727 | INFO | data.real_data_loader:load_training_data:86 | 开始加载训练数据: 2025-03-23 01:39:56.727745 到 2025-06-21 01:39:56.727745\n", "record": {"elapsed": {"repr": "0:00:29.216681", "seconds": 29.216681}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "load_training_data", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 86, "message": "开始加载训练数据: 2025-03-23 01:39:56.727745 到 2025-06-21 01:39:56.727745", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 44084, "name": "MainProcess"}, "thread": {"id": 48544, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:39:56.727745+08:00", "timestamp": 1750441196.727745}}}
{"text": "2025-06-21 01:39:56.744 | WARNING | data.clients.influxdb_client:query_kline_data:224 | 未找到K线数据: BTCUSDT 1m\n", "record": {"elapsed": {"repr": "0:00:29.233028", "seconds": 29.233028}, "exception": null, "extra": {"component": "InfluxDBClient"}, "file": {"name": "influxdb_client.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\clients\\influxdb_client.py"}, "function": "query_kline_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 224, "message": "未找到K线数据: BTCUSDT 1m", "module": "influxdb_client", "name": "data.clients.influxdb_client", "process": {"id": 44084, "name": "MainProcess"}, "thread": {"id": 48544, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:39:56.744092+08:00", "timestamp": 1750441196.744092}}}
{"text": "2025-06-21 01:39:56.746 | WARNING | data.real_data_loader:_load_kline_data:145 | InfluxDB中未找到数据，尝试其他数据源: BTCUSDT 1m\n", "record": {"elapsed": {"repr": "0:00:29.235557", "seconds": 29.235557}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "_load_kline_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 145, "message": "InfluxDB中未找到数据，尝试其他数据源: BTCUSDT 1m", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 44084, "name": "MainProcess"}, "thread": {"id": 48544, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:39:56.746621+08:00", "timestamp": 1750441196.746621}}}
{"text": "2025-06-21 01:39:56.746 | WARNING | data.real_data_loader:load_training_data:108 | 未找到K线数据: BTCUSDT_1m\n", "record": {"elapsed": {"repr": "0:00:29.235557", "seconds": 29.235557}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "load_training_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 108, "message": "未找到K线数据: BTCUSDT_1m", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 44084, "name": "MainProcess"}, "thread": {"id": 48544, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:39:56.746621+08:00", "timestamp": 1750441196.746621}}}
{"text": "2025-06-21 01:39:56.759 | WARNING | data.clients.influxdb_client:query_kline_data:224 | 未找到K线数据: BTCUSDT 5m\n", "record": {"elapsed": {"repr": "0:00:29.248694", "seconds": 29.248694}, "exception": null, "extra": {"component": "InfluxDBClient"}, "file": {"name": "influxdb_client.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\clients\\influxdb_client.py"}, "function": "query_kline_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 224, "message": "未找到K线数据: BTCUSDT 5m", "module": "influxdb_client", "name": "data.clients.influxdb_client", "process": {"id": 44084, "name": "MainProcess"}, "thread": {"id": 48544, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:39:56.759758+08:00", "timestamp": 1750441196.759758}}}
{"text": "2025-06-21 01:39:56.760 | WARNING | data.real_data_loader:_load_kline_data:145 | InfluxDB中未找到数据，尝试其他数据源: BTCUSDT 5m\n", "record": {"elapsed": {"repr": "0:00:29.249199", "seconds": 29.249199}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "_load_kline_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 145, "message": "InfluxDB中未找到数据，尝试其他数据源: BTCUSDT 5m", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 44084, "name": "MainProcess"}, "thread": {"id": 48544, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:39:56.760263+08:00", "timestamp": 1750441196.760263}}}
{"text": "2025-06-21 01:39:56.761 | WARNING | data.real_data_loader:load_training_data:108 | 未找到K线数据: BTCUSDT_5m\n", "record": {"elapsed": {"repr": "0:00:29.250225", "seconds": 29.250225}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "load_training_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 108, "message": "未找到K线数据: BTCUSDT_5m", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 44084, "name": "MainProcess"}, "thread": {"id": 48544, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:39:56.761289+08:00", "timestamp": 1750441196.761289}}}
{"text": "2025-06-21 01:39:56.774 | WARNING | data.clients.influxdb_client:query_kline_data:224 | 未找到K线数据: BTCUSDT 15m\n", "record": {"elapsed": {"repr": "0:00:29.263139", "seconds": 29.263139}, "exception": null, "extra": {"component": "InfluxDBClient"}, "file": {"name": "influxdb_client.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\clients\\influxdb_client.py"}, "function": "query_kline_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 224, "message": "未找到K线数据: BTCUSDT 15m", "module": "influxdb_client", "name": "data.clients.influxdb_client", "process": {"id": 44084, "name": "MainProcess"}, "thread": {"id": 48544, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:39:56.774203+08:00", "timestamp": 1750441196.774203}}}
{"text": "2025-06-21 01:39:56.774 | WARNING | data.real_data_loader:_load_kline_data:145 | InfluxDB中未找到数据，尝试其他数据源: BTCUSDT 15m\n", "record": {"elapsed": {"repr": "0:00:29.263139", "seconds": 29.263139}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "_load_kline_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 145, "message": "InfluxDB中未找到数据，尝试其他数据源: BTCUSDT 15m", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 44084, "name": "MainProcess"}, "thread": {"id": 48544, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:39:56.774203+08:00", "timestamp": 1750441196.774203}}}
{"text": "2025-06-21 01:39:56.775 | WARNING | data.real_data_loader:load_training_data:108 | 未找到K线数据: BTCUSDT_15m\n", "record": {"elapsed": {"repr": "0:00:29.264645", "seconds": 29.264645}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "load_training_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 108, "message": "未找到K线数据: BTCUSDT_15m", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 44084, "name": "MainProcess"}, "thread": {"id": 48544, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:39:56.775709+08:00", "timestamp": 1750441196.775709}}}
{"text": "2025-06-21 01:39:56.786 | WARNING | data.clients.influxdb_client:query_kline_data:224 | 未找到K线数据: BTCUSDT 1h\n", "record": {"elapsed": {"repr": "0:00:29.275768", "seconds": 29.275768}, "exception": null, "extra": {"component": "InfluxDBClient"}, "file": {"name": "influxdb_client.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\clients\\influxdb_client.py"}, "function": "query_kline_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 224, "message": "未找到K线数据: BTCUSDT 1h", "module": "influxdb_client", "name": "data.clients.influxdb_client", "process": {"id": 44084, "name": "MainProcess"}, "thread": {"id": 48544, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:39:56.786832+08:00", "timestamp": 1750441196.786832}}}
{"text": "2025-06-21 01:39:56.787 | WARNING | data.real_data_loader:_load_kline_data:145 | InfluxDB中未找到数据，尝试其他数据源: BTCUSDT 1h\n", "record": {"elapsed": {"repr": "0:00:29.276762", "seconds": 29.276762}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "_load_kline_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 145, "message": "InfluxDB中未找到数据，尝试其他数据源: BTCUSDT 1h", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 44084, "name": "MainProcess"}, "thread": {"id": 48544, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:39:56.787826+08:00", "timestamp": 1750441196.787826}}}
{"text": "2025-06-21 01:39:56.787 | WARNING | data.real_data_loader:load_training_data:108 | 未找到K线数据: BTCUSDT_1h\n", "record": {"elapsed": {"repr": "0:00:29.276762", "seconds": 29.276762}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "load_training_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 108, "message": "未找到K线数据: BTCUSDT_1h", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 44084, "name": "MainProcess"}, "thread": {"id": 48544, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:39:56.787826+08:00", "timestamp": 1750441196.787826}}}
{"text": "2025-06-21 01:39:56.800 | WARNING | data.clients.influxdb_client:query_kline_data:224 | 未找到K线数据: BTCUSDT 4h\n", "record": {"elapsed": {"repr": "0:00:29.289312", "seconds": 29.289312}, "exception": null, "extra": {"component": "InfluxDBClient"}, "file": {"name": "influxdb_client.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\clients\\influxdb_client.py"}, "function": "query_kline_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 224, "message": "未找到K线数据: BTCUSDT 4h", "module": "influxdb_client", "name": "data.clients.influxdb_client", "process": {"id": 44084, "name": "MainProcess"}, "thread": {"id": 48544, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:39:56.800376+08:00", "timestamp": 1750441196.800376}}}
{"text": "2025-06-21 01:39:56.800 | WARNING | data.real_data_loader:_load_kline_data:145 | InfluxDB中未找到数据，尝试其他数据源: BTCUSDT 4h\n", "record": {"elapsed": {"repr": "0:00:29.289312", "seconds": 29.289312}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "_load_kline_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 145, "message": "InfluxDB中未找到数据，尝试其他数据源: BTCUSDT 4h", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 44084, "name": "MainProcess"}, "thread": {"id": 48544, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:39:56.800376+08:00", "timestamp": 1750441196.800376}}}
{"text": "2025-06-21 01:39:56.801 | WARNING | data.real_data_loader:load_training_data:108 | 未找到K线数据: BTCUSDT_4h\n", "record": {"elapsed": {"repr": "0:00:29.290312", "seconds": 29.290312}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "load_training_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 108, "message": "未找到K线数据: BTCUSDT_4h", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 44084, "name": "MainProcess"}, "thread": {"id": 48544, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:39:56.801376+08:00", "timestamp": 1750441196.801376}}}
{"text": "2025-06-21 01:39:56.813 | WARNING | data.clients.influxdb_client:query_kline_data:224 | 未找到K线数据: BTCUSDT 1d\n", "record": {"elapsed": {"repr": "0:00:29.302826", "seconds": 29.302826}, "exception": null, "extra": {"component": "InfluxDBClient"}, "file": {"name": "influxdb_client.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\clients\\influxdb_client.py"}, "function": "query_kline_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 224, "message": "未找到K线数据: BTCUSDT 1d", "module": "influxdb_client", "name": "data.clients.influxdb_client", "process": {"id": 44084, "name": "MainProcess"}, "thread": {"id": 48544, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:39:56.813890+08:00", "timestamp": 1750441196.81389}}}
{"text": "2025-06-21 01:39:56.814 | WARNING | data.real_data_loader:_load_kline_data:145 | InfluxDB中未找到数据，尝试其他数据源: BTCUSDT 1d\n", "record": {"elapsed": {"repr": "0:00:29.303824", "seconds": 29.303824}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "_load_kline_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 145, "message": "InfluxDB中未找到数据，尝试其他数据源: BTCUSDT 1d", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 44084, "name": "MainProcess"}, "thread": {"id": 48544, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:39:56.814888+08:00", "timestamp": 1750441196.814888}}}
{"text": "2025-06-21 01:39:56.814 | WARNING | data.real_data_loader:load_training_data:108 | 未找到K线数据: BTCUSDT_1d\n", "record": {"elapsed": {"repr": "0:00:29.303824", "seconds": 29.303824}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "load_training_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 108, "message": "未找到K线数据: BTCUSDT_1d", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 44084, "name": "MainProcess"}, "thread": {"id": 48544, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:39:56.814888+08:00", "timestamp": 1750441196.814888}}}
{"text": "2025-06-21 01:39:56.825 | WARNING | data.clients.influxdb_client:query_kline_data:224 | 未找到K线数据: ETHUSDT 1m\n", "record": {"elapsed": {"repr": "0:00:29.314623", "seconds": 29.314623}, "exception": null, "extra": {"component": "InfluxDBClient"}, "file": {"name": "influxdb_client.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\clients\\influxdb_client.py"}, "function": "query_kline_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 224, "message": "未找到K线数据: ETHUSDT 1m", "module": "influxdb_client", "name": "data.clients.influxdb_client", "process": {"id": 44084, "name": "MainProcess"}, "thread": {"id": 48544, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:39:56.825687+08:00", "timestamp": 1750441196.825687}}}
{"text": "2025-06-21 01:39:56.825 | WARNING | data.real_data_loader:_load_kline_data:145 | InfluxDB中未找到数据，尝试其他数据源: ETHUSDT 1m\n", "record": {"elapsed": {"repr": "0:00:29.314623", "seconds": 29.314623}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "_load_kline_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 145, "message": "InfluxDB中未找到数据，尝试其他数据源: ETHUSDT 1m", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 44084, "name": "MainProcess"}, "thread": {"id": 48544, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:39:56.825687+08:00", "timestamp": 1750441196.825687}}}
{"text": "2025-06-21 01:39:56.827 | WARNING | data.real_data_loader:load_training_data:108 | 未找到K线数据: ETHUSDT_1m\n", "record": {"elapsed": {"repr": "0:00:29.316574", "seconds": 29.316574}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "load_training_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 108, "message": "未找到K线数据: ETHUSDT_1m", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 44084, "name": "MainProcess"}, "thread": {"id": 48544, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:39:56.827638+08:00", "timestamp": 1750441196.827638}}}
{"text": "2025-06-21 01:39:56.839 | WARNING | data.clients.influxdb_client:query_kline_data:224 | 未找到K线数据: ETHUSDT 5m\n", "record": {"elapsed": {"repr": "0:00:29.328161", "seconds": 29.328161}, "exception": null, "extra": {"component": "InfluxDBClient"}, "file": {"name": "influxdb_client.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\clients\\influxdb_client.py"}, "function": "query_kline_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 224, "message": "未找到K线数据: ETHUSDT 5m", "module": "influxdb_client", "name": "data.clients.influxdb_client", "process": {"id": 44084, "name": "MainProcess"}, "thread": {"id": 48544, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:39:56.839225+08:00", "timestamp": 1750441196.839225}}}
{"text": "2025-06-21 01:39:56.840 | WARNING | data.real_data_loader:_load_kline_data:145 | InfluxDB中未找到数据，尝试其他数据源: ETHUSDT 5m\n", "record": {"elapsed": {"repr": "0:00:29.329124", "seconds": 29.329124}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "_load_kline_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 145, "message": "InfluxDB中未找到数据，尝试其他数据源: ETHUSDT 5m", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 44084, "name": "MainProcess"}, "thread": {"id": 48544, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:39:56.840188+08:00", "timestamp": 1750441196.840188}}}
{"text": "2025-06-21 01:39:56.840 | WARNING | data.real_data_loader:load_training_data:108 | 未找到K线数据: ETHUSDT_5m\n", "record": {"elapsed": {"repr": "0:00:29.329124", "seconds": 29.329124}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "load_training_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 108, "message": "未找到K线数据: ETHUSDT_5m", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 44084, "name": "MainProcess"}, "thread": {"id": 48544, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:39:56.840188+08:00", "timestamp": 1750441196.840188}}}
{"text": "2025-06-21 01:39:56.851 | WARNING | data.clients.influxdb_client:query_kline_data:224 | 未找到K线数据: ETHUSDT 15m\n", "record": {"elapsed": {"repr": "0:00:29.340637", "seconds": 29.340637}, "exception": null, "extra": {"component": "InfluxDBClient"}, "file": {"name": "influxdb_client.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\clients\\influxdb_client.py"}, "function": "query_kline_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 224, "message": "未找到K线数据: ETHUSDT 15m", "module": "influxdb_client", "name": "data.clients.influxdb_client", "process": {"id": 44084, "name": "MainProcess"}, "thread": {"id": 48544, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:39:56.851701+08:00", "timestamp": 1750441196.851701}}}
{"text": "2025-06-21 01:39:56.853 | WARNING | data.real_data_loader:_load_kline_data:145 | InfluxDB中未找到数据，尝试其他数据源: ETHUSDT 15m\n", "record": {"elapsed": {"repr": "0:00:29.342642", "seconds": 29.342642}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "_load_kline_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 145, "message": "InfluxDB中未找到数据，尝试其他数据源: ETHUSDT 15m", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 44084, "name": "MainProcess"}, "thread": {"id": 48544, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:39:56.853706+08:00", "timestamp": 1750441196.853706}}}
{"text": "2025-06-21 01:39:56.854 | WARNING | data.real_data_loader:load_training_data:108 | 未找到K线数据: ETHUSDT_15m\n", "record": {"elapsed": {"repr": "0:00:29.343636", "seconds": 29.343636}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "load_training_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 108, "message": "未找到K线数据: ETHUSDT_15m", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 44084, "name": "MainProcess"}, "thread": {"id": 48544, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:39:56.854700+08:00", "timestamp": 1750441196.8547}}}
{"text": "2025-06-21 01:39:56.865 | WARNING | data.clients.influxdb_client:query_kline_data:224 | 未找到K线数据: ETHUSDT 1h\n", "record": {"elapsed": {"repr": "0:00:29.354595", "seconds": 29.354595}, "exception": null, "extra": {"component": "InfluxDBClient"}, "file": {"name": "influxdb_client.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\clients\\influxdb_client.py"}, "function": "query_kline_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 224, "message": "未找到K线数据: ETHUSDT 1h", "module": "influxdb_client", "name": "data.clients.influxdb_client", "process": {"id": 44084, "name": "MainProcess"}, "thread": {"id": 48544, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:39:56.865659+08:00", "timestamp": 1750441196.865659}}}
{"text": "2025-06-21 01:39:56.865 | WARNING | data.real_data_loader:_load_kline_data:145 | InfluxDB中未找到数据，尝试其他数据源: ETHUSDT 1h\n", "record": {"elapsed": {"repr": "0:00:29.354595", "seconds": 29.354595}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "_load_kline_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 145, "message": "InfluxDB中未找到数据，尝试其他数据源: ETHUSDT 1h", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 44084, "name": "MainProcess"}, "thread": {"id": 48544, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:39:56.865659+08:00", "timestamp": 1750441196.865659}}}
{"text": "2025-06-21 01:39:56.866 | WARNING | data.real_data_loader:load_training_data:108 | 未找到K线数据: ETHUSDT_1h\n", "record": {"elapsed": {"repr": "0:00:29.355671", "seconds": 29.355671}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "load_training_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 108, "message": "未找到K线数据: ETHUSDT_1h", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 44084, "name": "MainProcess"}, "thread": {"id": 48544, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:39:56.866735+08:00", "timestamp": 1750441196.866735}}}
{"text": "2025-06-21 01:39:56.878 | WARNING | data.clients.influxdb_client:query_kline_data:224 | 未找到K线数据: ETHUSDT 4h\n", "record": {"elapsed": {"repr": "0:00:29.367255", "seconds": 29.367255}, "exception": null, "extra": {"component": "InfluxDBClient"}, "file": {"name": "influxdb_client.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\clients\\influxdb_client.py"}, "function": "query_kline_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 224, "message": "未找到K线数据: ETHUSDT 4h", "module": "influxdb_client", "name": "data.clients.influxdb_client", "process": {"id": 44084, "name": "MainProcess"}, "thread": {"id": 48544, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:39:56.878319+08:00", "timestamp": 1750441196.878319}}}
{"text": "2025-06-21 01:39:56.879 | WARNING | data.real_data_loader:_load_kline_data:145 | InfluxDB中未找到数据，尝试其他数据源: ETHUSDT 4h\n", "record": {"elapsed": {"repr": "0:00:29.368249", "seconds": 29.368249}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "_load_kline_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 145, "message": "InfluxDB中未找到数据，尝试其他数据源: ETHUSDT 4h", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 44084, "name": "MainProcess"}, "thread": {"id": 48544, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:39:56.879313+08:00", "timestamp": 1750441196.879313}}}
{"text": "2025-06-21 01:39:56.880 | WARNING | data.real_data_loader:load_training_data:108 | 未找到K线数据: ETHUSDT_4h\n", "record": {"elapsed": {"repr": "0:00:29.369248", "seconds": 29.369248}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "load_training_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 108, "message": "未找到K线数据: ETHUSDT_4h", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 44084, "name": "MainProcess"}, "thread": {"id": 48544, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:39:56.880312+08:00", "timestamp": 1750441196.880312}}}
{"text": "2025-06-21 01:39:56.890 | WARNING | data.clients.influxdb_client:query_kline_data:224 | 未找到K线数据: ETHUSDT 1d\n", "record": {"elapsed": {"repr": "0:00:29.379791", "seconds": 29.379791}, "exception": null, "extra": {"component": "InfluxDBClient"}, "file": {"name": "influxdb_client.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\clients\\influxdb_client.py"}, "function": "query_kline_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 224, "message": "未找到K线数据: ETHUSDT 1d", "module": "influxdb_client", "name": "data.clients.influxdb_client", "process": {"id": 44084, "name": "MainProcess"}, "thread": {"id": 48544, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:39:56.890855+08:00", "timestamp": 1750441196.890855}}}
{"text": "2025-06-21 01:39:56.891 | WARNING | data.real_data_loader:_load_kline_data:145 | InfluxDB中未找到数据，尝试其他数据源: ETHUSDT 1d\n", "record": {"elapsed": {"repr": "0:00:29.380824", "seconds": 29.380824}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "_load_kline_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 145, "message": "InfluxDB中未找到数据，尝试其他数据源: ETHUSDT 1d", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 44084, "name": "MainProcess"}, "thread": {"id": 48544, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:39:56.891888+08:00", "timestamp": 1750441196.891888}}}
{"text": "2025-06-21 01:39:56.891 | WARNING | data.real_data_loader:load_training_data:108 | 未找到K线数据: ETHUSDT_1d\n", "record": {"elapsed": {"repr": "0:00:29.380824", "seconds": 29.380824}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "load_training_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 108, "message": "未找到K线数据: ETHUSDT_1d", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 44084, "name": "MainProcess"}, "thread": {"id": 48544, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:39:56.891888+08:00", "timestamp": 1750441196.891888}}}
{"text": "2025-06-21 01:39:56.892 | ERROR | data.real_data_loader:load_training_data:116 | 未加载到任何训练数据\n", "record": {"elapsed": {"repr": "0:00:29.381823", "seconds": 29.381823}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "load_training_data", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 116, "message": "未加载到任何训练数据", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 44084, "name": "MainProcess"}, "thread": {"id": 48544, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:39:56.892887+08:00", "timestamp": 1750441196.892887}}}
{"text": "2025-06-21 01:39:56.893 | INFO | ml.models.model_trainer:__init__:94 | 模型训练器初始化完成\n", "record": {"elapsed": {"repr": "0:00:29.382791", "seconds": 29.382791}, "exception": null, "extra": {"component": "ModelTrainer"}, "file": {"name": "model_trainer.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\ml\\models\\model_trainer.py"}, "function": "__init__", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 94, "message": "模型训练器初始化完成", "module": "model_trainer", "name": "ml.models.model_trainer", "process": {"id": 44084, "name": "MainProcess"}, "thread": {"id": 48544, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:39:56.893855+08:00", "timestamp": 1750441196.893855}}}
{"text": "2025-06-21 01:39:56.894 | INFO | __main__:train_model:186 | 开始训练模型...\n", "record": {"elapsed": {"repr": "0:00:29.383794", "seconds": 29.383794}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\main.py"}, "function": "train_model", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 186, "message": "开始训练模型...", "module": "main", "name": "__main__", "process": {"id": 44084, "name": "MainProcess"}, "thread": {"id": 48544, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:39:56.894858+08:00", "timestamp": 1750441196.894858}}}
{"text": "2025-06-21 01:39:56.895 | ERROR | ml.models.model_trainer:train:149 | 模型训练失败: 训练数据为空\n", "record": {"elapsed": {"repr": "0:00:29.384790", "seconds": 29.38479}, "exception": null, "extra": {"component": "ModelTrainer"}, "file": {"name": "model_trainer.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\ml\\models\\model_trainer.py"}, "function": "train", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 149, "message": "模型训练失败: 训练数据为空", "module": "model_trainer", "name": "ml.models.model_trainer", "process": {"id": 44084, "name": "MainProcess"}, "thread": {"id": 48544, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:39:56.895854+08:00", "timestamp": 1750441196.895854}}}
{"text": "2025-06-21 01:39:56.897 | INFO | __main__:train_model:189 | 模型训练完成\n", "record": {"elapsed": {"repr": "0:00:29.386049", "seconds": 29.386049}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\main.py"}, "function": "train_model", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 189, "message": "模型训练完成", "module": "main", "name": "__main__", "process": {"id": 44084, "name": "MainProcess"}, "thread": {"id": 48544, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:39:56.897113+08:00", "timestamp": 1750441196.897113}}}
{"text": "2025-06-21 01:39:56.897 | INFO | ml.models.prediction:__init__:76 | 预测引擎初始化完成\n", "record": {"elapsed": {"repr": "0:00:29.386049", "seconds": 29.386049}, "exception": null, "extra": {"component": "PredictionEngine"}, "file": {"name": "prediction.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\ml\\models\\prediction.py"}, "function": "__init__", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 76, "message": "预测引擎初始化完成", "module": "prediction", "name": "ml.models.prediction", "process": {"id": 44084, "name": "MainProcess"}, "thread": {"id": 48544, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:39:56.897113+08:00", "timestamp": 1750441196.897113}}}
{"text": "2025-06-21 01:39:56.898 | INFO | strategy.unified_ml:set_prediction_engine:96 | 预测引擎已设置\n", "record": {"elapsed": {"repr": "0:00:29.387084", "seconds": 29.387084}, "exception": null, "extra": {"component": "UnifiedMLStrategy"}, "file": {"name": "unified_ml.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\strategy\\unified_ml.py"}, "function": "set_prediction_engine", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 96, "message": "预测引擎已设置", "module": "unified_ml", "name": "strategy.unified_ml", "process": {"id": 44084, "name": "MainProcess"}, "thread": {"id": 48544, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:39:56.898148+08:00", "timestamp": 1750441196.898148}}}
{"text": "2025-06-21 01:40:03.930 | WARNING | strategy.unified_ml:on_market_data:118 | 技术指标计算失败: BTCUSDT\n", "record": {"elapsed": {"repr": "0:00:36.419697", "seconds": 36.419697}, "exception": null, "extra": {"component": "UnifiedMLStrategy"}, "file": {"name": "unified_ml.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\strategy\\unified_ml.py"}, "function": "on_market_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 118, "message": "技术指标计算失败: BTCUSDT", "module": "unified_ml", "name": "strategy.unified_ml", "process": {"id": 44084, "name": "MainProcess"}, "thread": {"id": 70024, "name": "kafka-consumer-kline.data"}, "time": {"repr": "2025-06-21 01:40:03.930761+08:00", "timestamp": 1750441203.930761}}}
{"text": "2025-06-21 01:40:03.931 | WARNING | strategy.unified_ml:on_market_data:118 | 技术指标计算失败: BTCUSDT\n", "record": {"elapsed": {"repr": "0:00:36.420704", "seconds": 36.420704}, "exception": null, "extra": {"component": "UnifiedMLStrategy"}, "file": {"name": "unified_ml.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\strategy\\unified_ml.py"}, "function": "on_market_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 118, "message": "技术指标计算失败: BTCUSDT", "module": "unified_ml", "name": "strategy.unified_ml", "process": {"id": 44084, "name": "MainProcess"}, "thread": {"id": 70024, "name": "kafka-consumer-kline.data"}, "time": {"repr": "2025-06-21 01:40:03.931768+08:00", "timestamp": 1750441203.931768}}}
{"text": "2025-06-21 01:40:03.932 | WARNING | strategy.unified_ml:on_market_data:118 | 技术指标计算失败: BTCUSDT\n", "record": {"elapsed": {"repr": "0:00:36.421696", "seconds": 36.421696}, "exception": null, "extra": {"component": "UnifiedMLStrategy"}, "file": {"name": "unified_ml.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\strategy\\unified_ml.py"}, "function": "on_market_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 118, "message": "技术指标计算失败: BTCUSDT", "module": "unified_ml", "name": "strategy.unified_ml", "process": {"id": 44084, "name": "MainProcess"}, "thread": {"id": 70024, "name": "kafka-consumer-kline.data"}, "time": {"repr": "2025-06-21 01:40:03.932760+08:00", "timestamp": 1750441203.93276}}}
{"text": "2025-06-21 01:40:03.933 | WARNING | strategy.unified_ml:on_market_data:118 | 技术指标计算失败: BTCUSDT\n", "record": {"elapsed": {"repr": "0:00:36.422697", "seconds": 36.422697}, "exception": null, "extra": {"component": "UnifiedMLStrategy"}, "file": {"name": "unified_ml.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\strategy\\unified_ml.py"}, "function": "on_market_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 118, "message": "技术指标计算失败: BTCUSDT", "module": "unified_ml", "name": "strategy.unified_ml", "process": {"id": 44084, "name": "MainProcess"}, "thread": {"id": 70024, "name": "kafka-consumer-kline.data"}, "time": {"repr": "2025-06-21 01:40:03.933761+08:00", "timestamp": 1750441203.933761}}}
{"text": "2025-06-21 01:40:03.933 | WARNING | strategy.unified_ml:on_market_data:118 | 技术指标计算失败: BTCUSDT\n", "record": {"elapsed": {"repr": "0:00:36.422697", "seconds": 36.422697}, "exception": null, "extra": {"component": "UnifiedMLStrategy"}, "file": {"name": "unified_ml.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\strategy\\unified_ml.py"}, "function": "on_market_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 118, "message": "技术指标计算失败: BTCUSDT", "module": "unified_ml", "name": "strategy.unified_ml", "process": {"id": 44084, "name": "MainProcess"}, "thread": {"id": 70024, "name": "kafka-consumer-kline.data"}, "time": {"repr": "2025-06-21 01:40:03.933761+08:00", "timestamp": 1750441203.933761}}}
{"text": "2025-06-21 01:40:03.938 | WARNING | strategy.unified_ml:on_market_data:118 | 技术指标计算失败: BTCUSDT\n", "record": {"elapsed": {"repr": "0:00:36.427218", "seconds": 36.427218}, "exception": null, "extra": {"component": "UnifiedMLStrategy"}, "file": {"name": "unified_ml.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\strategy\\unified_ml.py"}, "function": "on_market_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 118, "message": "技术指标计算失败: BTCUSDT", "module": "unified_ml", "name": "strategy.unified_ml", "process": {"id": 44084, "name": "MainProcess"}, "thread": {"id": 70024, "name": "kafka-consumer-kline.data"}, "time": {"repr": "2025-06-21 01:40:03.938282+08:00", "timestamp": 1750441203.938282}}}
{"text": "2025-06-21 01:40:07.365 | INFO | __main__:handle_signal:197 | 接收到信号 2，准备关闭应用...\n", "record": {"elapsed": {"repr": "0:00:39.854794", "seconds": 39.854794}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\main.py"}, "function": "handle_signal", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 197, "message": "接收到信号 2，准备关闭应用...", "module": "main", "name": "__main__", "process": {"id": 44084, "name": "MainProcess"}, "thread": {"id": 48544, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:40:07.365858+08:00", "timestamp": 1750441207.365858}}}
{"text": "2025-06-21 01:40:07.718 | INFO | __main__:handle_signal:197 | 接收到信号 2，准备关闭应用...\n", "record": {"elapsed": {"repr": "0:00:40.207848", "seconds": 40.207848}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\main.py"}, "function": "handle_signal", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 197, "message": "接收到信号 2，准备关闭应用...", "module": "main", "name": "__main__", "process": {"id": 44084, "name": "MainProcess"}, "thread": {"id": 48544, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:40:07.718912+08:00", "timestamp": 1750441207.718912}}}
{"text": "2025-06-21 01:40:07.998 | INFO | __main__:handle_signal:197 | 接收到信号 2，准备关闭应用...\n", "record": {"elapsed": {"repr": "0:00:40.487482", "seconds": 40.487482}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\main.py"}, "function": "handle_signal", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 197, "message": "接收到信号 2，准备关闭应用...", "module": "main", "name": "__main__", "process": {"id": 44084, "name": "MainProcess"}, "thread": {"id": 48544, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:40:07.998546+08:00", "timestamp": 1750441207.998546}}}
{"text": "2025-06-21 01:40:08.074 | INFO | __main__:stop:202 | 关闭交易策略应用\n", "record": {"elapsed": {"repr": "0:00:40.563867", "seconds": 40.563867}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\main.py"}, "function": "stop", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 202, "message": "关闭交易策略应用", "module": "main", "name": "__main__", "process": {"id": 44084, "name": "MainProcess"}, "thread": {"id": 48544, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:40:08.074931+08:00", "timestamp": 1750441208.074931}}}
{"text": "2025-06-21 01:40:08.216 | INFO | data.clients.mysql_client:close:499 | MySQL连接已关闭\n", "record": {"elapsed": {"repr": "0:00:40.705895", "seconds": 40.705895}, "exception": null, "extra": {"component": "MySQLClient"}, "file": {"name": "mysql_client.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\clients\\mysql_client.py"}, "function": "close", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 499, "message": "MySQL连接已关闭", "module": "mysql_client", "name": "data.clients.mysql_client", "process": {"id": 44084, "name": "MainProcess"}, "thread": {"id": 48544, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:40:08.216959+08:00", "timestamp": 1750441208.216959}}}
{"text": "2025-06-21 01:40:08.218 | INFO | data.clients.influxdb_client:close:340 | InfluxDB连接已关闭\n", "record": {"elapsed": {"repr": "0:00:40.707904", "seconds": 40.707904}, "exception": null, "extra": {"component": "InfluxDBClient"}, "file": {"name": "influxdb_client.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\clients\\influxdb_client.py"}, "function": "close", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 340, "message": "InfluxDB连接已关闭", "module": "influxdb_client", "name": "data.clients.influxdb_client", "process": {"id": 44084, "name": "MainProcess"}, "thread": {"id": 48544, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:40:08.218968+08:00", "timestamp": 1750441208.218968}}}
{"text": "2025-06-21 01:40:29.117 | INFO | infrastructure.logging.logging_core_manager:initialize:261 | Logging system initialized successfully\n", "record": {"elapsed": {"repr": "0:00:07.480906", "seconds": 7.480906}, "exception": null, "extra": {}, "file": {"name": "logging_core_manager.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\logging\\logging_core_manager.py"}, "function": "initialize", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 261, "message": "Logging system initialized successfully", "module": "logging_core_manager", "name": "infrastructure.logging.logging_core_manager", "process": {"id": 16392, "name": "MainProcess"}, "thread": {"id": 67064, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:40:29.117068+08:00", "timestamp": **********.117068}}}
{"text": "2025-06-21 01:40:29.184 | INFO | infrastructure.system_health_monitor:register_health_check:434 | 注册健康检查: system_resources\n", "record": {"elapsed": {"repr": "0:00:07.548234", "seconds": 7.548234}, "exception": null, "extra": {}, "file": {"name": "system_health_monitor.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\system_health_monitor.py"}, "function": "register_health_check", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 434, "message": "注册健康检查: system_resources", "module": "system_health_monitor", "name": "infrastructure.system_health_monitor", "process": {"id": 16392, "name": "MainProcess"}, "thread": {"id": 67064, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:40:29.184396+08:00", "timestamp": **********.184396}}}
{"text": "2025-06-21 01:40:29.195 | INFO | infrastructure.error_recovery:register_strategy:422 | 注册恢复策略: CacheRecoveryStrategy\n", "record": {"elapsed": {"repr": "0:00:07.559279", "seconds": 7.559279}, "exception": null, "extra": {}, "file": {"name": "error_recovery.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\error_recovery.py"}, "function": "register_strategy", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 422, "message": "注册恢复策略: CacheRecoveryStrategy", "module": "error_recovery", "name": "infrastructure.error_recovery", "process": {"id": 16392, "name": "MainProcess"}, "thread": {"id": 67064, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:40:29.195441+08:00", "timestamp": **********.195441}}}
{"text": "2025-06-21 01:40:29.196 | INFO | infrastructure.error_recovery:register_strategy:422 | 注册恢复策略: RetryRecoveryStrategy\n", "record": {"elapsed": {"repr": "0:00:07.560293", "seconds": 7.560293}, "exception": null, "extra": {}, "file": {"name": "error_recovery.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\error_recovery.py"}, "function": "register_strategy", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 422, "message": "注册恢复策略: RetryRecoveryStrategy", "module": "error_recovery", "name": "infrastructure.error_recovery", "process": {"id": 16392, "name": "MainProcess"}, "thread": {"id": 67064, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:40:29.196455+08:00", "timestamp": **********.196455}}}
{"text": "2025-06-21 01:40:29.197 | INFO | infrastructure.error_recovery:register_strategy:422 | 注册恢复策略: FallbackRecoveryStrategy\n", "record": {"elapsed": {"repr": "0:00:07.561292", "seconds": 7.561292}, "exception": null, "extra": {}, "file": {"name": "error_recovery.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\error_recovery.py"}, "function": "register_strategy", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 422, "message": "注册恢复策略: FallbackRecoveryStrategy", "module": "error_recovery", "name": "infrastructure.error_recovery", "process": {"id": 16392, "name": "MainProcess"}, "thread": {"id": 67064, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:40:29.197454+08:00", "timestamp": **********.197454}}}
{"text": "2025-06-21 01:40:29.198 | INFO | infrastructure.error_recovery:register_strategy:422 | 注册恢复策略: ComponentRestartStrategy\n", "record": {"elapsed": {"repr": "0:00:07.562310", "seconds": 7.56231}, "exception": null, "extra": {}, "file": {"name": "error_recovery.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\error_recovery.py"}, "function": "register_strategy", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 422, "message": "注册恢复策略: ComponentRestartStrategy", "module": "error_recovery", "name": "infrastructure.error_recovery", "process": {"id": 16392, "name": "MainProcess"}, "thread": {"id": 67064, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:40:29.198472+08:00", "timestamp": **********.198472}}}
{"text": "2025-06-21 01:40:29.206 | INFO | infrastructure.graceful_degradation:register_strategy:339 | 注册降级策略: performance_degradation\n", "record": {"elapsed": {"repr": "0:00:07.570824", "seconds": 7.570824}, "exception": null, "extra": {}, "file": {"name": "graceful_degradation.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\graceful_degradation.py"}, "function": "register_strategy", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 339, "message": "注册降级策略: performance_degradation", "module": "graceful_degradation", "name": "infrastructure.graceful_degradation", "process": {"id": 16392, "name": "MainProcess"}, "thread": {"id": 67064, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:40:29.206986+08:00", "timestamp": **********.206986}}}
{"text": "2025-06-21 01:40:29.213 | INFO | infrastructure.graceful_degradation:register_strategy:339 | 注册降级策略: load_based_degradation\n", "record": {"elapsed": {"repr": "0:00:07.577835", "seconds": 7.577835}, "exception": null, "extra": {}, "file": {"name": "graceful_degradation.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\graceful_degradation.py"}, "function": "register_strategy", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 339, "message": "注册降级策略: load_based_degradation", "module": "graceful_degradation", "name": "infrastructure.graceful_degradation", "process": {"id": 16392, "name": "MainProcess"}, "thread": {"id": 67064, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:40:29.213997+08:00", "timestamp": **********.213997}}}
{"text": "2025-06-21 01:40:29.245 | INFO | infrastructure.test_error_handling_core:__init__:78 | ErrorHandlingCoreTestRunner initialized\n", "record": {"elapsed": {"repr": "0:00:07.609481", "seconds": 7.609481}, "exception": null, "extra": {}, "file": {"name": "test_error_handling_core.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\test_error_handling_core.py"}, "function": "__init__", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 78, "message": "ErrorHandlingCoreTestRunner initialized", "module": "test_error_handling_core", "name": "infrastructure.test_error_handling_core", "process": {"id": 16392, "name": "MainProcess"}, "thread": {"id": 67064, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:40:29.245643+08:00", "timestamp": **********.245643}}}
{"text": "2025-06-21 01:40:29.494 | INFO | infrastructure.test_error_handling_integration:__init__:79 | ErrorHandlingIntegrationTestRunner initialized\n", "record": {"elapsed": {"repr": "0:00:07.858278", "seconds": 7.858278}, "exception": null, "extra": {}, "file": {"name": "test_error_handling_integration.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\test_error_handling_integration.py"}, "function": "__init__", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 79, "message": "ErrorHandlingIntegrationTestRunner initialized", "module": "test_error_handling_integration", "name": "infrastructure.test_error_handling_integration", "process": {"id": 16392, "name": "MainProcess"}, "thread": {"id": 67064, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:40:29.494440+08:00", "timestamp": **********.49444}}}
{"text": "2025-06-21 01:40:32.545 | INFO | core.config:__init__:43 | 配置已加载: D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\config\\config.ini\n", "record": {"elapsed": {"repr": "0:00:10.909281", "seconds": 10.909281}, "exception": null, "extra": {}, "file": {"name": "config.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\core\\config.py"}, "function": "__init__", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 43, "message": "配置已加载: D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\config\\config.ini", "module": "config", "name": "core.config", "process": {"id": 16392, "name": "MainProcess"}, "thread": {"id": 67064, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:40:32.545443+08:00", "timestamp": 1750441232.545443}}}
{"text": "2025-06-21 01:40:53.618 | INFO | data.clients.influxdb_client:_connect:100 | InfluxDB连接建立成功\n", "record": {"elapsed": {"repr": "0:00:31.982257", "seconds": 31.982257}, "exception": null, "extra": {"component": "InfluxDBClient"}, "file": {"name": "influxdb_client.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\clients\\influxdb_client.py"}, "function": "_connect", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 100, "message": "InfluxDB连接建立成功", "module": "influxdb_client", "name": "data.clients.influxdb_client", "process": {"id": 16392, "name": "MainProcess"}, "thread": {"id": 67064, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:40:53.618419+08:00", "timestamp": 1750441253.618419}}}
{"text": "2025-06-21 01:40:53.619 | INFO | data.clients.influxdb_client:__init__:75 | InfluxDB客户端初始化完成: http://localhost:8086, 组织: crypto, 存储桶: market_data\n", "record": {"elapsed": {"repr": "0:00:31.983268", "seconds": 31.983268}, "exception": null, "extra": {"component": "InfluxDBClient"}, "file": {"name": "influxdb_client.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\clients\\influxdb_client.py"}, "function": "__init__", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 75, "message": "InfluxDB客户端初始化完成: http://localhost:8086, 组织: crypto, 存储桶: market_data", "module": "influxdb_client", "name": "data.clients.influxdb_client", "process": {"id": 16392, "name": "MainProcess"}, "thread": {"id": 67064, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:40:53.619430+08:00", "timestamp": 1750441253.61943}}}
{"text": "2025-06-21 01:40:53.672 | INFO | data.clients.mysql_client:_connect:130 | MySQL连接建立成功\n", "record": {"elapsed": {"repr": "0:00:32.036514", "seconds": 32.036514}, "exception": null, "extra": {"component": "MySQLClient"}, "file": {"name": "mysql_client.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\clients\\mysql_client.py"}, "function": "_connect", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 130, "message": "MySQL连接建立成功", "module": "mysql_client", "name": "data.clients.mysql_client", "process": {"id": 16392, "name": "MainProcess"}, "thread": {"id": 67064, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:40:53.672676+08:00", "timestamp": 1750441253.672676}}}
{"text": "2025-06-21 01:40:53.688 | INFO | data.clients.mysql_client:_init_tables:187 | 数据库表初始化完成\n", "record": {"elapsed": {"repr": "0:00:32.052144", "seconds": 32.052144}, "exception": null, "extra": {"component": "MySQLClient"}, "file": {"name": "mysql_client.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\clients\\mysql_client.py"}, "function": "_init_tables", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 187, "message": "数据库表初始化完成", "module": "mysql_client", "name": "data.clients.mysql_client", "process": {"id": 16392, "name": "MainProcess"}, "thread": {"id": 67064, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:40:53.688306+08:00", "timestamp": 1750441253.688306}}}
{"text": "2025-06-21 01:40:53.688 | INFO | data.clients.mysql_client:__init__:103 | MySQL客户端初始化完成: localhost:3306/crypto_trading\n", "record": {"elapsed": {"repr": "0:00:32.052144", "seconds": 32.052144}, "exception": null, "extra": {"component": "MySQLClient"}, "file": {"name": "mysql_client.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\clients\\mysql_client.py"}, "function": "__init__", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 103, "message": "MySQL客户端初始化完成: localhost:3306/crypto_trading", "module": "mysql_client", "name": "data.clients.mysql_client", "process": {"id": 16392, "name": "MainProcess"}, "thread": {"id": 67064, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:40:53.688306+08:00", "timestamp": 1750441253.688306}}}
{"text": "2025-06-21 01:40:53.698 | INFO | ml.models.versioning:__init__:56 | 模型版本管理器初始化完成\n", "record": {"elapsed": {"repr": "0:00:32.062401", "seconds": 32.062401}, "exception": null, "extra": {"component": "ModelVersionManager"}, "file": {"name": "versioning.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\ml\\models\\versioning.py"}, "function": "__init__", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 56, "message": "模型版本管理器初始化完成", "module": "versioning", "name": "ml.models.versioning", "process": {"id": 16392, "name": "MainProcess"}, "thread": {"id": 67064, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:40:53.698563+08:00", "timestamp": 1750441253.698563}}}
{"text": "2025-06-21 01:40:53.698 | INFO | ml.models.online_learner:__init__:102 | 在线学习器初始化完成\n", "record": {"elapsed": {"repr": "0:00:32.062401", "seconds": 32.062401}, "exception": null, "extra": {"component": "OnlineLearner"}, "file": {"name": "online_learner.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\ml\\models\\online_learner.py"}, "function": "__init__", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 102, "message": "在线学习器初始化完成", "module": "online_learner", "name": "ml.models.online_learner", "process": {"id": 16392, "name": "MainProcess"}, "thread": {"id": 67064, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:40:53.698563+08:00", "timestamp": 1750441253.698563}}}
{"text": "2025-06-21 01:40:53.700 | INFO | strategy.base:__init__:54 | 基础策略初始化: UnifiedMLStrategy\n", "record": {"elapsed": {"repr": "0:00:32.063976", "seconds": 32.063976}, "exception": null, "extra": {"component": "UnifiedMLStrategy"}, "file": {"name": "base.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\strategy\\base.py"}, "function": "__init__", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 54, "message": "基础策略初始化: UnifiedMLStrategy", "module": "base", "name": "strategy.base", "process": {"id": 16392, "name": "MainProcess"}, "thread": {"id": 67064, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:40:53.700138+08:00", "timestamp": 1750441253.700138}}}
{"text": "2025-06-21 01:40:53.701 | INFO | strategy.unified_ml:__init__:86 | 统一ML策略初始化完成，支持交易对: ['BTCUSDT', 'ETHUSDT']\n", "record": {"elapsed": {"repr": "0:00:32.064912", "seconds": 32.064912}, "exception": null, "extra": {"component": "UnifiedMLStrategy"}, "file": {"name": "unified_ml.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\strategy\\unified_ml.py"}, "function": "__init__", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 86, "message": "统一ML策略初始化完成，支持交易对: ['BTCUSDT', 'ETHUSDT']", "module": "unified_ml", "name": "strategy.unified_ml", "process": {"id": 16392, "name": "MainProcess"}, "thread": {"id": 67064, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:40:53.701074+08:00", "timestamp": 1750441253.701074}}}
{"text": "2025-06-21 01:40:53.702 | INFO | __main__:start:85 | 启动交易策略应用\n", "record": {"elapsed": {"repr": "0:00:32.065916", "seconds": 32.065916}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\main.py"}, "function": "start", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 85, "message": "启动交易策略应用", "module": "main", "name": "__main__", "process": {"id": 16392, "name": "MainProcess"}, "thread": {"id": 67064, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:40:53.702078+08:00", "timestamp": 1750441253.702078}}}
{"text": "2025-06-21 01:40:53.706 | INFO | __main__:start:90 | 未找到有效模型，开始训练...\n", "record": {"elapsed": {"repr": "0:00:32.070470", "seconds": 32.07047}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\main.py"}, "function": "start", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 90, "message": "未找到有效模型，开始训练...", "module": "main", "name": "__main__", "process": {"id": 16392, "name": "MainProcess"}, "thread": {"id": 67064, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:40:53.706632+08:00", "timestamp": 1750441253.706632}}}
{"text": "2025-06-21 01:40:53.707 | INFO | data.real_data_loader:__init__:60 | 数据加载器初始化完成，支持交易对: ['BTCUSDT', 'ETHUSDT'], 时间周期: ['1m', '5m', '15m', '1h', '4h', '1d']\n", "record": {"elapsed": {"repr": "0:00:32.071427", "seconds": 32.071427}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "__init__", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 60, "message": "数据加载器初始化完成，支持交易对: ['BTCUSDT', 'ETHUSDT'], 时间周期: ['1m', '5m', '15m', '1h', '4h', '1d']", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 16392, "name": "MainProcess"}, "thread": {"id": 67064, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:40:53.707589+08:00", "timestamp": 1750441253.707589}}}
{"text": "2025-06-21 01:40:53.708 | INFO | __main__:train_model:179 | 加载训练数据...\n", "record": {"elapsed": {"repr": "0:00:32.072427", "seconds": 32.072427}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\main.py"}, "function": "train_model", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 179, "message": "加载训练数据...", "module": "main", "name": "__main__", "process": {"id": 16392, "name": "MainProcess"}, "thread": {"id": 67064, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:40:53.708589+08:00", "timestamp": 1750441253.708589}}}
{"text": "2025-06-21 01:40:53.708 | INFO | data.real_data_loader:load_training_data:86 | 开始加载训练数据: 2025-03-23 01:40:53.708589 到 2025-06-21 01:40:53.708589\n", "record": {"elapsed": {"repr": "0:00:32.072427", "seconds": 32.072427}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "load_training_data", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 86, "message": "开始加载训练数据: 2025-03-23 01:40:53.708589 到 2025-06-21 01:40:53.708589", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 16392, "name": "MainProcess"}, "thread": {"id": 67064, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:40:53.708589+08:00", "timestamp": 1750441253.708589}}}
{"text": "2025-06-21 01:40:53.728 | WARNING | data.clients.influxdb_client:query_kline_data:224 | 未找到K线数据: BTCUSDT 1m\n", "record": {"elapsed": {"repr": "0:00:32.092328", "seconds": 32.092328}, "exception": null, "extra": {"component": "InfluxDBClient"}, "file": {"name": "influxdb_client.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\clients\\influxdb_client.py"}, "function": "query_kline_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 224, "message": "未找到K线数据: BTCUSDT 1m", "module": "influxdb_client", "name": "data.clients.influxdb_client", "process": {"id": 16392, "name": "MainProcess"}, "thread": {"id": 67064, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:40:53.728490+08:00", "timestamp": 1750441253.72849}}}
{"text": "2025-06-21 01:40:53.729 | WARNING | data.real_data_loader:_load_kline_data:145 | InfluxDB中未找到数据，尝试其他数据源: BTCUSDT 1m\n", "record": {"elapsed": {"repr": "0:00:32.093319", "seconds": 32.093319}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "_load_kline_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 145, "message": "InfluxDB中未找到数据，尝试其他数据源: BTCUSDT 1m", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 16392, "name": "MainProcess"}, "thread": {"id": 67064, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:40:53.729481+08:00", "timestamp": 1750441253.729481}}}
{"text": "2025-06-21 01:40:53.730 | WARNING | data.real_data_loader:load_training_data:108 | 未找到K线数据: BTCUSDT_1m\n", "record": {"elapsed": {"repr": "0:00:32.094319", "seconds": 32.094319}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "load_training_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 108, "message": "未找到K线数据: BTCUSDT_1m", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 16392, "name": "MainProcess"}, "thread": {"id": 67064, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:40:53.730481+08:00", "timestamp": 1750441253.730481}}}
{"text": "2025-06-21 01:40:53.742 | WARNING | data.clients.influxdb_client:query_kline_data:224 | 未找到K线数据: BTCUSDT 5m\n", "record": {"elapsed": {"repr": "0:00:32.105858", "seconds": 32.105858}, "exception": null, "extra": {"component": "InfluxDBClient"}, "file": {"name": "influxdb_client.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\clients\\influxdb_client.py"}, "function": "query_kline_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 224, "message": "未找到K线数据: BTCUSDT 5m", "module": "influxdb_client", "name": "data.clients.influxdb_client", "process": {"id": 16392, "name": "MainProcess"}, "thread": {"id": 67064, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:40:53.742020+08:00", "timestamp": 1750441253.74202}}}
{"text": "2025-06-21 01:40:53.743 | WARNING | data.real_data_loader:_load_kline_data:145 | InfluxDB中未找到数据，尝试其他数据源: BTCUSDT 5m\n", "record": {"elapsed": {"repr": "0:00:32.106851", "seconds": 32.106851}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "_load_kline_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 145, "message": "InfluxDB中未找到数据，尝试其他数据源: BTCUSDT 5m", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 16392, "name": "MainProcess"}, "thread": {"id": 67064, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:40:53.743013+08:00", "timestamp": 1750441253.743013}}}
{"text": "2025-06-21 01:40:53.744 | WARNING | data.real_data_loader:load_training_data:108 | 未找到K线数据: BTCUSDT_5m\n", "record": {"elapsed": {"repr": "0:00:32.107854", "seconds": 32.107854}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "load_training_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 108, "message": "未找到K线数据: BTCUSDT_5m", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 16392, "name": "MainProcess"}, "thread": {"id": 67064, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:40:53.744016+08:00", "timestamp": 1750441253.744016}}}
{"text": "2025-06-21 01:40:53.756 | WARNING | data.clients.influxdb_client:query_kline_data:224 | 未找到K线数据: BTCUSDT 15m\n", "record": {"elapsed": {"repr": "0:00:32.120432", "seconds": 32.120432}, "exception": null, "extra": {"component": "InfluxDBClient"}, "file": {"name": "influxdb_client.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\clients\\influxdb_client.py"}, "function": "query_kline_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 224, "message": "未找到K线数据: BTCUSDT 15m", "module": "influxdb_client", "name": "data.clients.influxdb_client", "process": {"id": 16392, "name": "MainProcess"}, "thread": {"id": 67064, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:40:53.756594+08:00", "timestamp": 1750441253.756594}}}
{"text": "2025-06-21 01:40:53.757 | WARNING | data.real_data_loader:_load_kline_data:145 | InfluxDB中未找到数据，尝试其他数据源: BTCUSDT 15m\n", "record": {"elapsed": {"repr": "0:00:32.121433", "seconds": 32.121433}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "_load_kline_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 145, "message": "InfluxDB中未找到数据，尝试其他数据源: BTCUSDT 15m", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 16392, "name": "MainProcess"}, "thread": {"id": 67064, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:40:53.757595+08:00", "timestamp": 1750441253.757595}}}
{"text": "2025-06-21 01:40:53.758 | WARNING | data.real_data_loader:load_training_data:108 | 未找到K线数据: BTCUSDT_15m\n", "record": {"elapsed": {"repr": "0:00:32.122442", "seconds": 32.122442}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "load_training_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 108, "message": "未找到K线数据: BTCUSDT_15m", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 16392, "name": "MainProcess"}, "thread": {"id": 67064, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:40:53.758604+08:00", "timestamp": 1750441253.758604}}}
{"text": "2025-06-21 01:40:53.770 | WARNING | data.clients.influxdb_client:query_kline_data:224 | 未找到K线数据: BTCUSDT 1h\n", "record": {"elapsed": {"repr": "0:00:32.134575", "seconds": 32.134575}, "exception": null, "extra": {"component": "InfluxDBClient"}, "file": {"name": "influxdb_client.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\clients\\influxdb_client.py"}, "function": "query_kline_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 224, "message": "未找到K线数据: BTCUSDT 1h", "module": "influxdb_client", "name": "data.clients.influxdb_client", "process": {"id": 16392, "name": "MainProcess"}, "thread": {"id": 67064, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:40:53.770737+08:00", "timestamp": 1750441253.770737}}}
{"text": "2025-06-21 01:40:53.771 | WARNING | data.real_data_loader:_load_kline_data:145 | InfluxDB中未找到数据，尝试其他数据源: BTCUSDT 1h\n", "record": {"elapsed": {"repr": "0:00:32.135571", "seconds": 32.135571}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "_load_kline_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 145, "message": "InfluxDB中未找到数据，尝试其他数据源: BTCUSDT 1h", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 16392, "name": "MainProcess"}, "thread": {"id": 67064, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:40:53.771733+08:00", "timestamp": 1750441253.771733}}}
{"text": "2025-06-21 01:40:53.772 | WARNING | data.real_data_loader:load_training_data:108 | 未找到K线数据: BTCUSDT_1h\n", "record": {"elapsed": {"repr": "0:00:32.136573", "seconds": 32.136573}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "load_training_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 108, "message": "未找到K线数据: BTCUSDT_1h", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 16392, "name": "MainProcess"}, "thread": {"id": 67064, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:40:53.772735+08:00", "timestamp": 1750441253.772735}}}
{"text": "2025-06-21 01:40:53.783 | WARNING | data.clients.influxdb_client:query_kline_data:224 | 未找到K线数据: BTCUSDT 4h\n", "record": {"elapsed": {"repr": "0:00:32.147730", "seconds": 32.14773}, "exception": null, "extra": {"component": "InfluxDBClient"}, "file": {"name": "influxdb_client.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\clients\\influxdb_client.py"}, "function": "query_kline_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 224, "message": "未找到K线数据: BTCUSDT 4h", "module": "influxdb_client", "name": "data.clients.influxdb_client", "process": {"id": 16392, "name": "MainProcess"}, "thread": {"id": 67064, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:40:53.783892+08:00", "timestamp": 1750441253.783892}}}
{"text": "2025-06-21 01:40:53.785 | WARNING | data.real_data_loader:_load_kline_data:145 | InfluxDB中未找到数据，尝试其他数据源: BTCUSDT 4h\n", "record": {"elapsed": {"repr": "0:00:32.149238", "seconds": 32.149238}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "_load_kline_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 145, "message": "InfluxDB中未找到数据，尝试其他数据源: BTCUSDT 4h", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 16392, "name": "MainProcess"}, "thread": {"id": 67064, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:40:53.785400+08:00", "timestamp": 1750441253.7854}}}
{"text": "2025-06-21 01:40:53.787 | WARNING | data.real_data_loader:load_training_data:108 | 未找到K线数据: BTCUSDT_4h\n", "record": {"elapsed": {"repr": "0:00:32.151248", "seconds": 32.151248}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "load_training_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 108, "message": "未找到K线数据: BTCUSDT_4h", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 16392, "name": "MainProcess"}, "thread": {"id": 67064, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:40:53.787410+08:00", "timestamp": 1750441253.78741}}}
{"text": "2025-06-21 01:40:53.799 | WARNING | data.clients.influxdb_client:query_kline_data:224 | 未找到K线数据: BTCUSDT 1d\n", "record": {"elapsed": {"repr": "0:00:32.163786", "seconds": 32.163786}, "exception": null, "extra": {"component": "InfluxDBClient"}, "file": {"name": "influxdb_client.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\clients\\influxdb_client.py"}, "function": "query_kline_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 224, "message": "未找到K线数据: BTCUSDT 1d", "module": "influxdb_client", "name": "data.clients.influxdb_client", "process": {"id": 16392, "name": "MainProcess"}, "thread": {"id": 67064, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:40:53.799948+08:00", "timestamp": 1750441253.799948}}}
{"text": "2025-06-21 01:40:53.800 | WARNING | data.real_data_loader:_load_kline_data:145 | InfluxDB中未找到数据，尝试其他数据源: BTCUSDT 1d\n", "record": {"elapsed": {"repr": "0:00:32.164793", "seconds": 32.164793}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "_load_kline_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 145, "message": "InfluxDB中未找到数据，尝试其他数据源: BTCUSDT 1d", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 16392, "name": "MainProcess"}, "thread": {"id": 67064, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:40:53.800955+08:00", "timestamp": 1750441253.800955}}}
{"text": "2025-06-21 01:40:53.804 | WARNING | data.real_data_loader:load_training_data:108 | 未找到K线数据: BTCUSDT_1d\n", "record": {"elapsed": {"repr": "0:00:32.167903", "seconds": 32.167903}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "load_training_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 108, "message": "未找到K线数据: BTCUSDT_1d", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 16392, "name": "MainProcess"}, "thread": {"id": 67064, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:40:53.804065+08:00", "timestamp": 1750441253.804065}}}
{"text": "2025-06-21 01:40:53.820 | WARNING | data.clients.influxdb_client:query_kline_data:224 | 未找到K线数据: ETHUSDT 1m\n", "record": {"elapsed": {"repr": "0:00:32.184755", "seconds": 32.184755}, "exception": null, "extra": {"component": "InfluxDBClient"}, "file": {"name": "influxdb_client.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\clients\\influxdb_client.py"}, "function": "query_kline_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 224, "message": "未找到K线数据: ETHUSDT 1m", "module": "influxdb_client", "name": "data.clients.influxdb_client", "process": {"id": 16392, "name": "MainProcess"}, "thread": {"id": 67064, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:40:53.820917+08:00", "timestamp": 1750441253.820917}}}
{"text": "2025-06-21 01:40:53.821 | WARNING | data.real_data_loader:_load_kline_data:145 | InfluxDB中未找到数据，尝试其他数据源: ETHUSDT 1m\n", "record": {"elapsed": {"repr": "0:00:32.185762", "seconds": 32.185762}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "_load_kline_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 145, "message": "InfluxDB中未找到数据，尝试其他数据源: ETHUSDT 1m", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 16392, "name": "MainProcess"}, "thread": {"id": 67064, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:40:53.821924+08:00", "timestamp": 1750441253.821924}}}
{"text": "2025-06-21 01:40:53.821 | WARNING | data.real_data_loader:load_training_data:108 | 未找到K线数据: ETHUSDT_1m\n", "record": {"elapsed": {"repr": "0:00:32.185762", "seconds": 32.185762}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "load_training_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 108, "message": "未找到K线数据: ETHUSDT_1m", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 16392, "name": "MainProcess"}, "thread": {"id": 67064, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:40:53.821924+08:00", "timestamp": 1750441253.821924}}}
{"text": "2025-06-21 01:40:53.837 | WARNING | data.clients.influxdb_client:query_kline_data:224 | 未找到K线数据: ETHUSDT 5m\n", "record": {"elapsed": {"repr": "0:00:32.201111", "seconds": 32.201111}, "exception": null, "extra": {"component": "InfluxDBClient"}, "file": {"name": "influxdb_client.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\clients\\influxdb_client.py"}, "function": "query_kline_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 224, "message": "未找到K线数据: ETHUSDT 5m", "module": "influxdb_client", "name": "data.clients.influxdb_client", "process": {"id": 16392, "name": "MainProcess"}, "thread": {"id": 67064, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:40:53.837273+08:00", "timestamp": 1750441253.837273}}}
{"text": "2025-06-21 01:40:53.838 | WARNING | data.real_data_loader:_load_kline_data:145 | InfluxDB中未找到数据，尝试其他数据源: ETHUSDT 5m\n", "record": {"elapsed": {"repr": "0:00:32.202104", "seconds": 32.202104}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "_load_kline_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 145, "message": "InfluxDB中未找到数据，尝试其他数据源: ETHUSDT 5m", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 16392, "name": "MainProcess"}, "thread": {"id": 67064, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:40:53.838266+08:00", "timestamp": 1750441253.838266}}}
{"text": "2025-06-21 01:40:53.839 | WARNING | data.real_data_loader:load_training_data:108 | 未找到K线数据: ETHUSDT_5m\n", "record": {"elapsed": {"repr": "0:00:32.203112", "seconds": 32.203112}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "load_training_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 108, "message": "未找到K线数据: ETHUSDT_5m", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 16392, "name": "MainProcess"}, "thread": {"id": 67064, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:40:53.839274+08:00", "timestamp": 1750441253.839274}}}
{"text": "2025-06-21 01:40:53.853 | WARNING | data.clients.influxdb_client:query_kline_data:224 | 未找到K线数据: ETHUSDT 15m\n", "record": {"elapsed": {"repr": "0:00:32.217640", "seconds": 32.21764}, "exception": null, "extra": {"component": "InfluxDBClient"}, "file": {"name": "influxdb_client.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\clients\\influxdb_client.py"}, "function": "query_kline_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 224, "message": "未找到K线数据: ETHUSDT 15m", "module": "influxdb_client", "name": "data.clients.influxdb_client", "process": {"id": 16392, "name": "MainProcess"}, "thread": {"id": 67064, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:40:53.853802+08:00", "timestamp": 1750441253.853802}}}
{"text": "2025-06-21 01:40:53.853 | WARNING | data.real_data_loader:_load_kline_data:145 | InfluxDB中未找到数据，尝试其他数据源: ETHUSDT 15m\n", "record": {"elapsed": {"repr": "0:00:32.217640", "seconds": 32.21764}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "_load_kline_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 145, "message": "InfluxDB中未找到数据，尝试其他数据源: ETHUSDT 15m", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 16392, "name": "MainProcess"}, "thread": {"id": 67064, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:40:53.853802+08:00", "timestamp": 1750441253.853802}}}
{"text": "2025-06-21 01:40:53.854 | WARNING | data.real_data_loader:load_training_data:108 | 未找到K线数据: ETHUSDT_15m\n", "record": {"elapsed": {"repr": "0:00:32.218632", "seconds": 32.218632}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "load_training_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 108, "message": "未找到K线数据: ETHUSDT_15m", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 16392, "name": "MainProcess"}, "thread": {"id": 67064, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:40:53.854794+08:00", "timestamp": 1750441253.854794}}}
{"text": "2025-06-21 01:40:53.870 | WARNING | data.clients.influxdb_client:query_kline_data:224 | 未找到K线数据: ETHUSDT 1h\n", "record": {"elapsed": {"repr": "0:00:32.234697", "seconds": 32.234697}, "exception": null, "extra": {"component": "InfluxDBClient"}, "file": {"name": "influxdb_client.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\clients\\influxdb_client.py"}, "function": "query_kline_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 224, "message": "未找到K线数据: ETHUSDT 1h", "module": "influxdb_client", "name": "data.clients.influxdb_client", "process": {"id": 16392, "name": "MainProcess"}, "thread": {"id": 67064, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:40:53.870859+08:00", "timestamp": 1750441253.870859}}}
{"text": "2025-06-21 01:40:53.871 | WARNING | data.real_data_loader:_load_kline_data:145 | InfluxDB中未找到数据，尝试其他数据源: ETHUSDT 1h\n", "record": {"elapsed": {"repr": "0:00:32.235694", "seconds": 32.235694}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "_load_kline_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 145, "message": "InfluxDB中未找到数据，尝试其他数据源: ETHUSDT 1h", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 16392, "name": "MainProcess"}, "thread": {"id": 67064, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:40:53.871856+08:00", "timestamp": 1750441253.871856}}}
{"text": "2025-06-21 01:40:53.872 | WARNING | data.real_data_loader:load_training_data:108 | 未找到K线数据: ETHUSDT_1h\n", "record": {"elapsed": {"repr": "0:00:32.236694", "seconds": 32.236694}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "load_training_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 108, "message": "未找到K线数据: ETHUSDT_1h", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 16392, "name": "MainProcess"}, "thread": {"id": 67064, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:40:53.872856+08:00", "timestamp": 1750441253.872856}}}
{"text": "2025-06-21 01:40:53.898 | WARNING | data.clients.influxdb_client:query_kline_data:224 | 未找到K线数据: ETHUSDT 4h\n", "record": {"elapsed": {"repr": "0:00:32.262430", "seconds": 32.26243}, "exception": null, "extra": {"component": "InfluxDBClient"}, "file": {"name": "influxdb_client.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\clients\\influxdb_client.py"}, "function": "query_kline_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 224, "message": "未找到K线数据: ETHUSDT 4h", "module": "influxdb_client", "name": "data.clients.influxdb_client", "process": {"id": 16392, "name": "MainProcess"}, "thread": {"id": 67064, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:40:53.898592+08:00", "timestamp": 1750441253.898592}}}
{"text": "2025-06-21 01:40:53.899 | WARNING | data.real_data_loader:_load_kline_data:145 | InfluxDB中未找到数据，尝试其他数据源: ETHUSDT 4h\n", "record": {"elapsed": {"repr": "0:00:32.263478", "seconds": 32.263478}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "_load_kline_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 145, "message": "InfluxDB中未找到数据，尝试其他数据源: ETHUSDT 4h", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 16392, "name": "MainProcess"}, "thread": {"id": 67064, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:40:53.899640+08:00", "timestamp": 1750441253.89964}}}
{"text": "2025-06-21 01:40:53.900 | WARNING | data.real_data_loader:load_training_data:108 | 未找到K线数据: ETHUSDT_4h\n", "record": {"elapsed": {"repr": "0:00:32.263993", "seconds": 32.263993}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "load_training_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 108, "message": "未找到K线数据: ETHUSDT_4h", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 16392, "name": "MainProcess"}, "thread": {"id": 67064, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:40:53.900155+08:00", "timestamp": 1750441253.900155}}}
{"text": "2025-06-21 01:40:53.916 | WARNING | data.clients.influxdb_client:query_kline_data:224 | 未找到K线数据: ETHUSDT 1d\n", "record": {"elapsed": {"repr": "0:00:32.280655", "seconds": 32.280655}, "exception": null, "extra": {"component": "InfluxDBClient"}, "file": {"name": "influxdb_client.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\clients\\influxdb_client.py"}, "function": "query_kline_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 224, "message": "未找到K线数据: ETHUSDT 1d", "module": "influxdb_client", "name": "data.clients.influxdb_client", "process": {"id": 16392, "name": "MainProcess"}, "thread": {"id": 67064, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:40:53.916817+08:00", "timestamp": 1750441253.916817}}}
{"text": "2025-06-21 01:40:53.917 | WARNING | data.real_data_loader:_load_kline_data:145 | InfluxDB中未找到数据，尝试其他数据源: ETHUSDT 1d\n", "record": {"elapsed": {"repr": "0:00:32.281664", "seconds": 32.281664}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "_load_kline_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 145, "message": "InfluxDB中未找到数据，尝试其他数据源: ETHUSDT 1d", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 16392, "name": "MainProcess"}, "thread": {"id": 67064, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:40:53.917826+08:00", "timestamp": 1750441253.917826}}}
{"text": "2025-06-21 01:40:53.919 | WARNING | data.real_data_loader:load_training_data:108 | 未找到K线数据: ETHUSDT_1d\n", "record": {"elapsed": {"repr": "0:00:32.283674", "seconds": 32.283674}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "load_training_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 108, "message": "未找到K线数据: ETHUSDT_1d", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 16392, "name": "MainProcess"}, "thread": {"id": 67064, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:40:53.919836+08:00", "timestamp": 1750441253.919836}}}
{"text": "2025-06-21 01:40:53.921 | ERROR | data.real_data_loader:load_training_data:116 | 未加载到任何训练数据\n", "record": {"elapsed": {"repr": "0:00:32.285663", "seconds": 32.285663}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "load_training_data", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 116, "message": "未加载到任何训练数据", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 16392, "name": "MainProcess"}, "thread": {"id": 67064, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:40:53.921825+08:00", "timestamp": 1750441253.921825}}}
{"text": "2025-06-21 01:40:53.922 | INFO | ml.models.model_trainer:__init__:94 | 模型训练器初始化完成\n", "record": {"elapsed": {"repr": "0:00:32.286667", "seconds": 32.286667}, "exception": null, "extra": {"component": "ModelTrainer"}, "file": {"name": "model_trainer.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\ml\\models\\model_trainer.py"}, "function": "__init__", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 94, "message": "模型训练器初始化完成", "module": "model_trainer", "name": "ml.models.model_trainer", "process": {"id": 16392, "name": "MainProcess"}, "thread": {"id": 67064, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:40:53.922829+08:00", "timestamp": 1750441253.922829}}}
{"text": "2025-06-21 01:40:53.924 | INFO | __main__:train_model:186 | 开始训练模型...\n", "record": {"elapsed": {"repr": "0:00:32.288269", "seconds": 32.288269}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\main.py"}, "function": "train_model", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 186, "message": "开始训练模型...", "module": "main", "name": "__main__", "process": {"id": 16392, "name": "MainProcess"}, "thread": {"id": 67064, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:40:53.924431+08:00", "timestamp": 1750441253.924431}}}
{"text": "2025-06-21 01:40:53.924 | ERROR | ml.models.model_trainer:train:149 | 模型训练失败: 训练数据为空\n", "record": {"elapsed": {"repr": "0:00:32.288777", "seconds": 32.288777}, "exception": null, "extra": {"component": "ModelTrainer"}, "file": {"name": "model_trainer.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\ml\\models\\model_trainer.py"}, "function": "train", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 149, "message": "模型训练失败: 训练数据为空", "module": "model_trainer", "name": "ml.models.model_trainer", "process": {"id": 16392, "name": "MainProcess"}, "thread": {"id": 67064, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:40:53.924939+08:00", "timestamp": 1750441253.924939}}}
{"text": "2025-06-21 01:40:53.925 | INFO | __main__:train_model:189 | 模型训练完成\n", "record": {"elapsed": {"repr": "0:00:32.289445", "seconds": 32.289445}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\main.py"}, "function": "train_model", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 189, "message": "模型训练完成", "module": "main", "name": "__main__", "process": {"id": 16392, "name": "MainProcess"}, "thread": {"id": 67064, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:40:53.925607+08:00", "timestamp": 1750441253.925607}}}
{"text": "2025-06-21 01:40:53.929 | INFO | ml.models.prediction:__init__:76 | 预测引擎初始化完成\n", "record": {"elapsed": {"repr": "0:00:32.293669", "seconds": 32.293669}, "exception": null, "extra": {"component": "PredictionEngine"}, "file": {"name": "prediction.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\ml\\models\\prediction.py"}, "function": "__init__", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 76, "message": "预测引擎初始化完成", "module": "prediction", "name": "ml.models.prediction", "process": {"id": 16392, "name": "MainProcess"}, "thread": {"id": 67064, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:40:53.929831+08:00", "timestamp": 1750441253.929831}}}
{"text": "2025-06-21 01:40:53.930 | INFO | strategy.unified_ml:set_prediction_engine:96 | 预测引擎已设置\n", "record": {"elapsed": {"repr": "0:00:32.294677", "seconds": 32.294677}, "exception": null, "extra": {"component": "UnifiedMLStrategy"}, "file": {"name": "unified_ml.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\strategy\\unified_ml.py"}, "function": "set_prediction_engine", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 96, "message": "预测引擎已设置", "module": "unified_ml", "name": "strategy.unified_ml", "process": {"id": 16392, "name": "MainProcess"}, "thread": {"id": 67064, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:40:53.930839+08:00", "timestamp": 1750441253.930839}}}
{"text": "2025-06-21 01:40:54.415 | INFO | __main__:handle_signal:197 | 接收到信号 2，准备关闭应用...\n", "record": {"elapsed": {"repr": "0:00:32.779297", "seconds": 32.779297}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\main.py"}, "function": "handle_signal", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 197, "message": "接收到信号 2，准备关闭应用...", "module": "main", "name": "__main__", "process": {"id": 16392, "name": "MainProcess"}, "thread": {"id": 67064, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:40:54.415459+08:00", "timestamp": 1750441254.415459}}}
{"text": "2025-06-21 01:40:54.728 | INFO | __main__:handle_signal:197 | 接收到信号 2，准备关闭应用...\n", "record": {"elapsed": {"repr": "0:00:33.092673", "seconds": 33.092673}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\main.py"}, "function": "handle_signal", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 197, "message": "接收到信号 2，准备关闭应用...", "module": "main", "name": "__main__", "process": {"id": 16392, "name": "MainProcess"}, "thread": {"id": 67064, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:40:54.728835+08:00", "timestamp": 1750441254.728835}}}
{"text": "2025-06-21 01:40:54.995 | INFO | __main__:stop:202 | 关闭交易策略应用\n", "record": {"elapsed": {"repr": "0:00:33.359515", "seconds": 33.359515}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\main.py"}, "function": "stop", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 202, "message": "关闭交易策略应用", "module": "main", "name": "__main__", "process": {"id": 16392, "name": "MainProcess"}, "thread": {"id": 67064, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:40:54.995677+08:00", "timestamp": 1750441254.995677}}}
{"text": "2025-06-21 01:40:57.066 | INFO | data.clients.mysql_client:close:499 | MySQL连接已关闭\n", "record": {"elapsed": {"repr": "0:00:35.430295", "seconds": 35.430295}, "exception": null, "extra": {"component": "MySQLClient"}, "file": {"name": "mysql_client.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\clients\\mysql_client.py"}, "function": "close", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 499, "message": "MySQL连接已关闭", "module": "mysql_client", "name": "data.clients.mysql_client", "process": {"id": 16392, "name": "MainProcess"}, "thread": {"id": 67064, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:40:57.066457+08:00", "timestamp": 1750441257.066457}}}
{"text": "2025-06-21 01:40:57.066 | INFO | data.clients.influxdb_client:close:340 | InfluxDB连接已关闭\n", "record": {"elapsed": {"repr": "0:00:35.430295", "seconds": 35.430295}, "exception": null, "extra": {"component": "InfluxDBClient"}, "file": {"name": "influxdb_client.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\clients\\influxdb_client.py"}, "function": "close", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 340, "message": "InfluxDB连接已关闭", "module": "influxdb_client", "name": "data.clients.influxdb_client", "process": {"id": 16392, "name": "MainProcess"}, "thread": {"id": 67064, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:40:57.066457+08:00", "timestamp": 1750441257.066457}}}
{"text": "2025-06-21 01:56:47.115 | INFO | infrastructure.logging.logging_core_manager:initialize:261 | Logging system initialized successfully\n", "record": {"elapsed": {"repr": "0:00:08.021753", "seconds": 8.021753}, "exception": null, "extra": {}, "file": {"name": "logging_core_manager.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\logging\\logging_core_manager.py"}, "function": "initialize", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 261, "message": "Logging system initialized successfully", "module": "logging_core_manager", "name": "infrastructure.logging.logging_core_manager", "process": {"id": 72816, "name": "MainProcess"}, "thread": {"id": 31280, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:56:47.115728+08:00", "timestamp": **********.115728}}}
{"text": "2025-06-21 01:56:47.165 | INFO | infrastructure.system_health_monitor:register_health_check:434 | 注册健康检查: system_resources\n", "record": {"elapsed": {"repr": "0:00:08.071992", "seconds": 8.071992}, "exception": null, "extra": {}, "file": {"name": "system_health_monitor.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\system_health_monitor.py"}, "function": "register_health_check", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 434, "message": "注册健康检查: system_resources", "module": "system_health_monitor", "name": "infrastructure.system_health_monitor", "process": {"id": 72816, "name": "MainProcess"}, "thread": {"id": 31280, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:56:47.165967+08:00", "timestamp": **********.165967}}}
{"text": "2025-06-21 01:56:47.175 | INFO | infrastructure.error_recovery:register_strategy:422 | 注册恢复策略: CacheRecoveryStrategy\n", "record": {"elapsed": {"repr": "0:00:08.081055", "seconds": 8.081055}, "exception": null, "extra": {}, "file": {"name": "error_recovery.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\error_recovery.py"}, "function": "register_strategy", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 422, "message": "注册恢复策略: CacheRecoveryStrategy", "module": "error_recovery", "name": "infrastructure.error_recovery", "process": {"id": 72816, "name": "MainProcess"}, "thread": {"id": 31280, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:56:47.175030+08:00", "timestamp": **********.17503}}}
{"text": "2025-06-21 01:56:47.176 | INFO | infrastructure.error_recovery:register_strategy:422 | 注册恢复策略: RetryRecoveryStrategy\n", "record": {"elapsed": {"repr": "0:00:08.082041", "seconds": 8.082041}, "exception": null, "extra": {}, "file": {"name": "error_recovery.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\error_recovery.py"}, "function": "register_strategy", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 422, "message": "注册恢复策略: RetryRecoveryStrategy", "module": "error_recovery", "name": "infrastructure.error_recovery", "process": {"id": 72816, "name": "MainProcess"}, "thread": {"id": 31280, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:56:47.176016+08:00", "timestamp": **********.176016}}}
{"text": "2025-06-21 01:56:47.177 | INFO | infrastructure.error_recovery:register_strategy:422 | 注册恢复策略: FallbackRecoveryStrategy\n", "record": {"elapsed": {"repr": "0:00:08.083044", "seconds": 8.083044}, "exception": null, "extra": {}, "file": {"name": "error_recovery.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\error_recovery.py"}, "function": "register_strategy", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 422, "message": "注册恢复策略: FallbackRecoveryStrategy", "module": "error_recovery", "name": "infrastructure.error_recovery", "process": {"id": 72816, "name": "MainProcess"}, "thread": {"id": 31280, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:56:47.177019+08:00", "timestamp": **********.177019}}}
{"text": "2025-06-21 01:56:47.178 | INFO | infrastructure.error_recovery:register_strategy:422 | 注册恢复策略: ComponentRestartStrategy\n", "record": {"elapsed": {"repr": "0:00:08.084043", "seconds": 8.084043}, "exception": null, "extra": {}, "file": {"name": "error_recovery.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\error_recovery.py"}, "function": "register_strategy", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 422, "message": "注册恢复策略: ComponentRestartStrategy", "module": "error_recovery", "name": "infrastructure.error_recovery", "process": {"id": 72816, "name": "MainProcess"}, "thread": {"id": 31280, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:56:47.178018+08:00", "timestamp": **********.178018}}}
{"text": "2025-06-21 01:56:47.190 | INFO | infrastructure.graceful_degradation:register_strategy:339 | 注册降级策略: performance_degradation\n", "record": {"elapsed": {"repr": "0:00:08.096572", "seconds": 8.096572}, "exception": null, "extra": {}, "file": {"name": "graceful_degradation.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\graceful_degradation.py"}, "function": "register_strategy", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 339, "message": "注册降级策略: performance_degradation", "module": "graceful_degradation", "name": "infrastructure.graceful_degradation", "process": {"id": 72816, "name": "MainProcess"}, "thread": {"id": 31280, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:56:47.190547+08:00", "timestamp": **********.190547}}}
{"text": "2025-06-21 01:56:47.197 | INFO | infrastructure.graceful_degradation:register_strategy:339 | 注册降级策略: load_based_degradation\n", "record": {"elapsed": {"repr": "0:00:08.103101", "seconds": 8.103101}, "exception": null, "extra": {}, "file": {"name": "graceful_degradation.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\graceful_degradation.py"}, "function": "register_strategy", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 339, "message": "注册降级策略: load_based_degradation", "module": "graceful_degradation", "name": "infrastructure.graceful_degradation", "process": {"id": 72816, "name": "MainProcess"}, "thread": {"id": 31280, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:56:47.197076+08:00", "timestamp": **********.197076}}}
{"text": "2025-06-21 01:56:47.220 | INFO | infrastructure.test_error_handling_core:__init__:78 | ErrorHandlingCoreTestRunner initialized\n", "record": {"elapsed": {"repr": "0:00:08.126163", "seconds": 8.126163}, "exception": null, "extra": {}, "file": {"name": "test_error_handling_core.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\test_error_handling_core.py"}, "function": "__init__", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 78, "message": "ErrorHandlingCoreTestRunner initialized", "module": "test_error_handling_core", "name": "infrastructure.test_error_handling_core", "process": {"id": 72816, "name": "MainProcess"}, "thread": {"id": 31280, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:56:47.220138+08:00", "timestamp": **********.220138}}}
{"text": "2025-06-21 01:56:47.389 | INFO | infrastructure.test_error_handling_integration:__init__:79 | ErrorHandlingIntegrationTestRunner initialized\n", "record": {"elapsed": {"repr": "0:00:08.295405", "seconds": 8.295405}, "exception": null, "extra": {}, "file": {"name": "test_error_handling_integration.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\test_error_handling_integration.py"}, "function": "__init__", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 79, "message": "ErrorHandlingIntegrationTestRunner initialized", "module": "test_error_handling_integration", "name": "infrastructure.test_error_handling_integration", "process": {"id": 72816, "name": "MainProcess"}, "thread": {"id": 31280, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:56:47.389380+08:00", "timestamp": **********.38938}}}
{"text": "2025-06-21 01:56:51.873 | INFO | core.config:__init__:43 | 配置已加载: D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\config\\config.ini\n", "record": {"elapsed": {"repr": "0:00:12.779489", "seconds": 12.779489}, "exception": null, "extra": {}, "file": {"name": "config.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\core\\config.py"}, "function": "__init__", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 43, "message": "配置已加载: D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\config\\config.ini", "module": "config", "name": "core.config", "process": {"id": 72816, "name": "MainProcess"}, "thread": {"id": 31280, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:56:51.873464+08:00", "timestamp": 1750442211.873464}}}
{"text": "2025-06-21 01:57:13.125 | INFO | data.clients.influxdb_client:_connect:100 | InfluxDB连接建立成功\n", "record": {"elapsed": {"repr": "0:00:34.031140", "seconds": 34.03114}, "exception": null, "extra": {"component": "InfluxDBClient"}, "file": {"name": "influxdb_client.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\clients\\influxdb_client.py"}, "function": "_connect", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 100, "message": "InfluxDB连接建立成功", "module": "influxdb_client", "name": "data.clients.influxdb_client", "process": {"id": 72816, "name": "MainProcess"}, "thread": {"id": 31280, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:57:13.125115+08:00", "timestamp": 1750442233.125115}}}
{"text": "2025-06-21 01:57:13.126 | INFO | data.clients.influxdb_client:__init__:75 | InfluxDB客户端初始化完成: http://localhost:8086, 组织: crypto, 存储桶: market_data\n", "record": {"elapsed": {"repr": "0:00:34.032140", "seconds": 34.03214}, "exception": null, "extra": {"component": "InfluxDBClient"}, "file": {"name": "influxdb_client.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\clients\\influxdb_client.py"}, "function": "__init__", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 75, "message": "InfluxDB客户端初始化完成: http://localhost:8086, 组织: crypto, 存储桶: market_data", "module": "influxdb_client", "name": "data.clients.influxdb_client", "process": {"id": 72816, "name": "MainProcess"}, "thread": {"id": 31280, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:57:13.126115+08:00", "timestamp": 1750442233.126115}}}
{"text": "2025-06-21 01:57:13.212 | INFO | data.clients.mysql_client:_connect:130 | MySQL连接建立成功\n", "record": {"elapsed": {"repr": "0:00:34.118928", "seconds": 34.118928}, "exception": null, "extra": {"component": "MySQLClient"}, "file": {"name": "mysql_client.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\clients\\mysql_client.py"}, "function": "_connect", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 130, "message": "MySQL连接建立成功", "module": "mysql_client", "name": "data.clients.mysql_client", "process": {"id": 72816, "name": "MainProcess"}, "thread": {"id": 31280, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:57:13.212903+08:00", "timestamp": 1750442233.212903}}}
{"text": "2025-06-21 01:57:13.226 | INFO | data.clients.mysql_client:_init_tables:187 | 数据库表初始化完成\n", "record": {"elapsed": {"repr": "0:00:34.132928", "seconds": 34.132928}, "exception": null, "extra": {"component": "MySQLClient"}, "file": {"name": "mysql_client.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\clients\\mysql_client.py"}, "function": "_init_tables", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 187, "message": "数据库表初始化完成", "module": "mysql_client", "name": "data.clients.mysql_client", "process": {"id": 72816, "name": "MainProcess"}, "thread": {"id": 31280, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:57:13.226903+08:00", "timestamp": 1750442233.226903}}}
{"text": "2025-06-21 01:57:13.228 | INFO | data.clients.mysql_client:__init__:103 | MySQL客户端初始化完成: localhost:3306/crypto_trading\n", "record": {"elapsed": {"repr": "0:00:34.134925", "seconds": 34.134925}, "exception": null, "extra": {"component": "MySQLClient"}, "file": {"name": "mysql_client.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\clients\\mysql_client.py"}, "function": "__init__", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 103, "message": "MySQL客户端初始化完成: localhost:3306/crypto_trading", "module": "mysql_client", "name": "data.clients.mysql_client", "process": {"id": 72816, "name": "MainProcess"}, "thread": {"id": 31280, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:57:13.228900+08:00", "timestamp": 1750442233.2289}}}
{"text": "2025-06-21 01:57:13.265 | INFO | ml.models.versioning:__init__:56 | 模型版本管理器初始化完成\n", "record": {"elapsed": {"repr": "0:00:34.171141", "seconds": 34.171141}, "exception": null, "extra": {"component": "ModelVersionManager"}, "file": {"name": "versioning.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\ml\\models\\versioning.py"}, "function": "__init__", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 56, "message": "模型版本管理器初始化完成", "module": "versioning", "name": "ml.models.versioning", "process": {"id": 72816, "name": "MainProcess"}, "thread": {"id": 31280, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:57:13.265116+08:00", "timestamp": 1750442233.265116}}}
{"text": "2025-06-21 01:57:13.266 | INFO | ml.models.online_learner:__init__:102 | 在线学习器初始化完成\n", "record": {"elapsed": {"repr": "0:00:34.172141", "seconds": 34.172141}, "exception": null, "extra": {"component": "OnlineLearner"}, "file": {"name": "online_learner.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\ml\\models\\online_learner.py"}, "function": "__init__", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 102, "message": "在线学习器初始化完成", "module": "online_learner", "name": "ml.models.online_learner", "process": {"id": 72816, "name": "MainProcess"}, "thread": {"id": 31280, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:57:13.266116+08:00", "timestamp": 1750442233.266116}}}
{"text": "2025-06-21 01:57:13.267 | INFO | strategy.base:__init__:54 | 基础策略初始化: UnifiedMLStrategy\n", "record": {"elapsed": {"repr": "0:00:34.173139", "seconds": 34.173139}, "exception": null, "extra": {"component": "UnifiedMLStrategy"}, "file": {"name": "base.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\strategy\\base.py"}, "function": "__init__", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 54, "message": "基础策略初始化: UnifiedMLStrategy", "module": "base", "name": "strategy.base", "process": {"id": 72816, "name": "MainProcess"}, "thread": {"id": 31280, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:57:13.267114+08:00", "timestamp": 1750442233.267114}}}
{"text": "2025-06-21 01:57:13.268 | INFO | strategy.unified_ml:__init__:86 | 统一ML策略初始化完成，支持交易对: ['BTCUSDT', 'ETHUSDT']\n", "record": {"elapsed": {"repr": "0:00:34.174139", "seconds": 34.174139}, "exception": null, "extra": {"component": "UnifiedMLStrategy"}, "file": {"name": "unified_ml.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\strategy\\unified_ml.py"}, "function": "__init__", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 86, "message": "统一ML策略初始化完成，支持交易对: ['BTCUSDT', 'ETHUSDT']", "module": "unified_ml", "name": "strategy.unified_ml", "process": {"id": 72816, "name": "MainProcess"}, "thread": {"id": 31280, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:57:13.268114+08:00", "timestamp": 1750442233.268114}}}
{"text": "2025-06-21 01:57:13.268 | INFO | __main__:start:85 | 启动交易策略应用\n", "record": {"elapsed": {"repr": "0:00:34.174139", "seconds": 34.174139}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\main.py"}, "function": "start", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 85, "message": "启动交易策略应用", "module": "main", "name": "__main__", "process": {"id": 72816, "name": "MainProcess"}, "thread": {"id": 31280, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:57:13.268114+08:00", "timestamp": 1750442233.268114}}}
{"text": "2025-06-21 01:57:13.274 | INFO | __main__:start:90 | 未找到有效模型，开始训练...\n", "record": {"elapsed": {"repr": "0:00:34.180681", "seconds": 34.180681}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\main.py"}, "function": "start", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 90, "message": "未找到有效模型，开始训练...", "module": "main", "name": "__main__", "process": {"id": 72816, "name": "MainProcess"}, "thread": {"id": 31280, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:57:13.274656+08:00", "timestamp": 1750442233.274656}}}
{"text": "2025-06-21 01:57:13.275 | INFO | data.real_data_loader:__init__:60 | 数据加载器初始化完成，支持交易对: ['BTCUSDT', 'ETHUSDT'], 时间周期: ['1m', '5m', '15m', '1h', '4h', '1d']\n", "record": {"elapsed": {"repr": "0:00:34.181671", "seconds": 34.181671}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "__init__", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 60, "message": "数据加载器初始化完成，支持交易对: ['BTCUSDT', 'ETHUSDT'], 时间周期: ['1m', '5m', '15m', '1h', '4h', '1d']", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 72816, "name": "MainProcess"}, "thread": {"id": 31280, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:57:13.275646+08:00", "timestamp": 1750442233.275646}}}
{"text": "2025-06-21 01:57:13.276 | INFO | __main__:train_model:179 | 加载训练数据...\n", "record": {"elapsed": {"repr": "0:00:34.182667", "seconds": 34.182667}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\main.py"}, "function": "train_model", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 179, "message": "加载训练数据...", "module": "main", "name": "__main__", "process": {"id": 72816, "name": "MainProcess"}, "thread": {"id": 31280, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:57:13.276642+08:00", "timestamp": 1750442233.276642}}}
{"text": "2025-06-21 01:57:13.278 | INFO | data.real_data_loader:load_training_data:86 | 开始加载训练数据: 2025-03-23 01:57:13.278642 到 2025-06-21 01:57:13.278642\n", "record": {"elapsed": {"repr": "0:00:34.184667", "seconds": 34.184667}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "load_training_data", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 86, "message": "开始加载训练数据: 2025-03-23 01:57:13.278642 到 2025-06-21 01:57:13.278642", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 72816, "name": "MainProcess"}, "thread": {"id": 31280, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:57:13.278642+08:00", "timestamp": 1750442233.278642}}}
{"text": "2025-06-21 01:58:05.245 | INFO | data.real_data_loader:load_training_data:104 | 加载训练数据成功: BTCUSDT_1m, 记录数: 87611\n", "record": {"elapsed": {"repr": "0:01:26.151200", "seconds": 86.1512}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "load_training_data", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 104, "message": "加载训练数据成功: BTCUSDT_1m, 记录数: 87611", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 72816, "name": "MainProcess"}, "thread": {"id": 31280, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:58:05.245175+08:00", "timestamp": 1750442285.245175}}}
{"text": "2025-06-21 01:58:13.850 | INFO | data.real_data_loader:load_training_data:104 | 加载训练数据成功: BTCUSDT_5m, 记录数: 17523\n", "record": {"elapsed": {"repr": "0:01:34.756627", "seconds": 94.756627}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "load_training_data", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 104, "message": "加载训练数据成功: BTCUSDT_5m, 记录数: 17523", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 72816, "name": "MainProcess"}, "thread": {"id": 31280, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:58:13.850602+08:00", "timestamp": 1750442293.850602}}}
{"text": "2025-06-21 01:58:17.368 | INFO | data.real_data_loader:load_training_data:104 | 加载训练数据成功: BTCUSDT_15m, 记录数: 5831\n", "record": {"elapsed": {"repr": "0:01:38.274714", "seconds": 98.274714}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "load_training_data", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 104, "message": "加载训练数据成功: BTCUSDT_15m, 记录数: 5831", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 72816, "name": "MainProcess"}, "thread": {"id": 31280, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:58:17.368689+08:00", "timestamp": 1750442297.368689}}}
{"text": "2025-06-21 01:58:18.171 | INFO | data.real_data_loader:load_training_data:104 | 加载训练数据成功: BTCUSDT_1h, 记录数: 1425\n", "record": {"elapsed": {"repr": "0:01:39.077240", "seconds": 99.07724}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "load_training_data", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 104, "message": "加载训练数据成功: BTCUSDT_1h, 记录数: 1425", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 72816, "name": "MainProcess"}, "thread": {"id": 31280, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:58:18.171215+08:00", "timestamp": 1750442298.171215}}}
{"text": "2025-06-21 01:58:18.442 | INFO | data.real_data_loader:load_training_data:104 | 加载训练数据成功: BTCUSDT_4h, 记录数: 316\n", "record": {"elapsed": {"repr": "0:01:39.348223", "seconds": 99.348223}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "load_training_data", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 104, "message": "加载训练数据成功: BTCUSDT_4h, 记录数: 316", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 72816, "name": "MainProcess"}, "thread": {"id": 31280, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:58:18.442198+08:00", "timestamp": 1750442298.442198}}}
{"text": "2025-06-21 01:58:18.610 | INFO | data.real_data_loader:load_training_data:104 | 加载训练数据成功: BTCUSDT_1d, 记录数: 8\n", "record": {"elapsed": {"repr": "0:01:39.516719", "seconds": 99.516719}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "load_training_data", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 104, "message": "加载训练数据成功: BTCUSDT_1d, 记录数: 8", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 72816, "name": "MainProcess"}, "thread": {"id": 31280, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:58:18.610694+08:00", "timestamp": 1750442298.610694}}}
{"text": "2025-06-21 01:59:11.416 | INFO | data.real_data_loader:load_training_data:104 | 加载训练数据成功: ETHUSDT_1m, 记录数: 86910\n", "record": {"elapsed": {"repr": "0:02:32.322655", "seconds": 152.322655}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "load_training_data", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 104, "message": "加载训练数据成功: ETHUSDT_1m, 记录数: 86910", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 72816, "name": "MainProcess"}, "thread": {"id": 31280, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:59:11.416630+08:00", "timestamp": 1750442351.41663}}}
{"text": "2025-06-21 01:59:21.914 | INFO | data.real_data_loader:load_training_data:104 | 加载训练数据成功: ETHUSDT_5m, 记录数: 17499\n", "record": {"elapsed": {"repr": "0:02:42.820526", "seconds": 162.820526}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "load_training_data", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 104, "message": "加载训练数据成功: ETHUSDT_5m, 记录数: 17499", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 72816, "name": "MainProcess"}, "thread": {"id": 31280, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:59:21.914501+08:00", "timestamp": 1750442361.914501}}}
{"text": "2025-06-21 01:59:25.760 | INFO | data.real_data_loader:load_training_data:104 | 加载训练数据成功: ETHUSDT_15m, 记录数: 5829\n", "record": {"elapsed": {"repr": "0:02:46.666025", "seconds": 166.666025}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "load_training_data", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 104, "message": "加载训练数据成功: ETHUSDT_15m, 记录数: 5829", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 72816, "name": "MainProcess"}, "thread": {"id": 31280, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:59:25.760000+08:00", "timestamp": 1750442365.76}}}
{"text": "2025-06-21 01:59:27.030 | INFO | data.real_data_loader:load_training_data:104 | 加载训练数据成功: ETHUSDT_1h, 记录数: 1425\n", "record": {"elapsed": {"repr": "0:02:47.936771", "seconds": 167.936771}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "load_training_data", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 104, "message": "加载训练数据成功: ETHUSDT_1h, 记录数: 1425", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 72816, "name": "MainProcess"}, "thread": {"id": 31280, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:59:27.030746+08:00", "timestamp": 1750442367.030746}}}
{"text": "2025-06-21 01:59:27.352 | INFO | data.real_data_loader:load_training_data:104 | 加载训练数据成功: ETHUSDT_4h, 记录数: 317\n", "record": {"elapsed": {"repr": "0:02:48.258293", "seconds": 168.258293}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "load_training_data", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 104, "message": "加载训练数据成功: ETHUSDT_4h, 记录数: 317", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 72816, "name": "MainProcess"}, "thread": {"id": 31280, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:59:27.352268+08:00", "timestamp": 1750442367.352268}}}
{"text": "2025-06-21 01:59:27.498 | INFO | data.real_data_loader:load_training_data:104 | 加载训练数据成功: ETHUSDT_1d, 记录数: 8\n", "record": {"elapsed": {"repr": "0:02:48.404370", "seconds": 168.40437}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "load_training_data", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 104, "message": "加载训练数据成功: ETHUSDT_1d, 记录数: 8", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 72816, "name": "MainProcess"}, "thread": {"id": 31280, "name": "MainThread"}, "time": {"repr": "2025-06-21 01:59:27.498345+08:00", "timestamp": 1750442367.498345}}}
{"text": "2025-06-21 02:22:28.897 | INFO | __main__:handle_signal:197 | 接收到信号 2，准备关闭应用...\n", "record": {"elapsed": {"repr": "0:25:49.803263", "seconds": 1549.803263}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\main.py"}, "function": "handle_signal", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 197, "message": "接收到信号 2，准备关闭应用...", "module": "main", "name": "__main__", "process": {"id": 72816, "name": "MainProcess"}, "thread": {"id": 31280, "name": "MainThread"}, "time": {"repr": "2025-06-21 02:22:28.897238+08:00", "timestamp": 1750443748.897238}}}
{"text": "2025-06-21 02:22:33.185 | INFO | __main__:handle_signal:197 | 接收到信号 2，准备关闭应用...\n", "record": {"elapsed": {"repr": "0:25:54.091753", "seconds": 1554.091753}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\main.py"}, "function": "handle_signal", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 197, "message": "接收到信号 2，准备关闭应用...", "module": "main", "name": "__main__", "process": {"id": 72816, "name": "MainProcess"}, "thread": {"id": 31280, "name": "MainThread"}, "time": {"repr": "2025-06-21 02:22:33.185728+08:00", "timestamp": 1750443753.185728}}}
{"text": "2025-06-21 02:22:33.692 | INFO | __main__:handle_signal:197 | 接收到信号 2，准备关闭应用...\n", "record": {"elapsed": {"repr": "0:25:54.598051", "seconds": 1554.598051}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\main.py"}, "function": "handle_signal", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 197, "message": "接收到信号 2，准备关闭应用...", "module": "main", "name": "__main__", "process": {"id": 72816, "name": "MainProcess"}, "thread": {"id": 31280, "name": "MainThread"}, "time": {"repr": "2025-06-21 02:22:33.692026+08:00", "timestamp": 1750443753.692026}}}
{"text": "2025-06-21 02:22:34.040 | INFO | __main__:handle_signal:197 | 接收到信号 2，准备关闭应用...\n", "record": {"elapsed": {"repr": "0:25:54.946247", "seconds": 1554.946247}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\main.py"}, "function": "handle_signal", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 197, "message": "接收到信号 2，准备关闭应用...", "module": "main", "name": "__main__", "process": {"id": 72816, "name": "MainProcess"}, "thread": {"id": 31280, "name": "MainThread"}, "time": {"repr": "2025-06-21 02:22:34.040222+08:00", "timestamp": 1750443754.040222}}}
{"text": "2025-06-21 03:13:18.440 | INFO | infrastructure.logging.logging_core_manager:initialize:261 | Logging system initialized successfully\n", "record": {"elapsed": {"repr": "0:00:05.370520", "seconds": 5.37052}, "exception": null, "extra": {}, "file": {"name": "logging_core_manager.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\logging\\logging_core_manager.py"}, "function": "initialize", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 261, "message": "Logging system initialized successfully", "module": "logging_core_manager", "name": "infrastructure.logging.logging_core_manager", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 17192, "name": "MainThread"}, "time": {"repr": "2025-06-21 03:13:18.440195+08:00", "timestamp": **********.440195}}}
{"text": "2025-06-21 03:13:18.490 | INFO | infrastructure.system_health_monitor:register_health_check:434 | 注册健康检查: system_resources\n", "record": {"elapsed": {"repr": "0:00:05.420430", "seconds": 5.42043}, "exception": null, "extra": {}, "file": {"name": "system_health_monitor.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\system_health_monitor.py"}, "function": "register_health_check", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 434, "message": "注册健康检查: system_resources", "module": "system_health_monitor", "name": "infrastructure.system_health_monitor", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 17192, "name": "MainThread"}, "time": {"repr": "2025-06-21 03:13:18.490105+08:00", "timestamp": **********.490105}}}
{"text": "2025-06-21 03:13:18.501 | INFO | infrastructure.error_recovery:register_strategy:422 | 注册恢复策略: CacheRecoveryStrategy\n", "record": {"elapsed": {"repr": "0:00:05.431925", "seconds": 5.431925}, "exception": null, "extra": {}, "file": {"name": "error_recovery.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\error_recovery.py"}, "function": "register_strategy", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 422, "message": "注册恢复策略: CacheRecoveryStrategy", "module": "error_recovery", "name": "infrastructure.error_recovery", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 17192, "name": "MainThread"}, "time": {"repr": "2025-06-21 03:13:18.501600+08:00", "timestamp": **********.5016}}}
{"text": "2025-06-21 03:13:18.503 | INFO | infrastructure.error_recovery:register_strategy:422 | 注册恢复策略: RetryRecoveryStrategy\n", "record": {"elapsed": {"repr": "0:00:05.433639", "seconds": 5.433639}, "exception": null, "extra": {}, "file": {"name": "error_recovery.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\error_recovery.py"}, "function": "register_strategy", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 422, "message": "注册恢复策略: RetryRecoveryStrategy", "module": "error_recovery", "name": "infrastructure.error_recovery", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 17192, "name": "MainThread"}, "time": {"repr": "2025-06-21 03:13:18.503314+08:00", "timestamp": **********.503314}}}
{"text": "2025-06-21 03:13:18.504 | INFO | infrastructure.error_recovery:register_strategy:422 | 注册恢复策略: FallbackRecoveryStrategy\n", "record": {"elapsed": {"repr": "0:00:05.435216", "seconds": 5.435216}, "exception": null, "extra": {}, "file": {"name": "error_recovery.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\error_recovery.py"}, "function": "register_strategy", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 422, "message": "注册恢复策略: FallbackRecoveryStrategy", "module": "error_recovery", "name": "infrastructure.error_recovery", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 17192, "name": "MainThread"}, "time": {"repr": "2025-06-21 03:13:18.504891+08:00", "timestamp": **********.504891}}}
{"text": "2025-06-21 03:13:18.508 | INFO | infrastructure.error_recovery:register_strategy:422 | 注册恢复策略: ComponentRestartStrategy\n", "record": {"elapsed": {"repr": "0:00:05.438735", "seconds": 5.438735}, "exception": null, "extra": {}, "file": {"name": "error_recovery.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\error_recovery.py"}, "function": "register_strategy", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 422, "message": "注册恢复策略: ComponentRestartStrategy", "module": "error_recovery", "name": "infrastructure.error_recovery", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 17192, "name": "MainThread"}, "time": {"repr": "2025-06-21 03:13:18.508410+08:00", "timestamp": **********.50841}}}
{"text": "2025-06-21 03:13:18.513 | INFO | infrastructure.graceful_degradation:register_strategy:339 | 注册降级策略: performance_degradation\n", "record": {"elapsed": {"repr": "0:00:05.444267", "seconds": 5.444267}, "exception": null, "extra": {}, "file": {"name": "graceful_degradation.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\graceful_degradation.py"}, "function": "register_strategy", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 339, "message": "注册降级策略: performance_degradation", "module": "graceful_degradation", "name": "infrastructure.graceful_degradation", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 17192, "name": "MainThread"}, "time": {"repr": "2025-06-21 03:13:18.513942+08:00", "timestamp": **********.513942}}}
{"text": "2025-06-21 03:13:18.514 | INFO | infrastructure.graceful_degradation:register_strategy:339 | 注册降级策略: load_based_degradation\n", "record": {"elapsed": {"repr": "0:00:05.444775", "seconds": 5.444775}, "exception": null, "extra": {}, "file": {"name": "graceful_degradation.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\graceful_degradation.py"}, "function": "register_strategy", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 339, "message": "注册降级策略: load_based_degradation", "module": "graceful_degradation", "name": "infrastructure.graceful_degradation", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 17192, "name": "MainThread"}, "time": {"repr": "2025-06-21 03:13:18.514450+08:00", "timestamp": **********.51445}}}
{"text": "2025-06-21 03:13:18.551 | INFO | infrastructure.test_error_handling_core:__init__:78 | ErrorHandlingCoreTestRunner initialized\n", "record": {"elapsed": {"repr": "0:00:05.482255", "seconds": 5.482255}, "exception": null, "extra": {}, "file": {"name": "test_error_handling_core.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\test_error_handling_core.py"}, "function": "__init__", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 78, "message": "ErrorHandlingCoreTestRunner initialized", "module": "test_error_handling_core", "name": "infrastructure.test_error_handling_core", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 17192, "name": "MainThread"}, "time": {"repr": "2025-06-21 03:13:18.551930+08:00", "timestamp": **********.55193}}}
{"text": "2025-06-21 03:13:18.780 | INFO | infrastructure.test_error_handling_integration:__init__:79 | ErrorHandlingIntegrationTestRunner initialized\n", "record": {"elapsed": {"repr": "0:00:05.710693", "seconds": 5.710693}, "exception": null, "extra": {}, "file": {"name": "test_error_handling_integration.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\test_error_handling_integration.py"}, "function": "__init__", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 79, "message": "ErrorHandlingIntegrationTestRunner initialized", "module": "test_error_handling_integration", "name": "infrastructure.test_error_handling_integration", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 17192, "name": "MainThread"}, "time": {"repr": "2025-06-21 03:13:18.780368+08:00", "timestamp": **********.780368}}}
{"text": "2025-06-21 03:13:23.056 | INFO | core.config:__init__:43 | 配置已加载: D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\config\\config.ini\n", "record": {"elapsed": {"repr": "0:00:09.986355", "seconds": 9.986355}, "exception": null, "extra": {}, "file": {"name": "config.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\core\\config.py"}, "function": "__init__", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 43, "message": "配置已加载: D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\config\\config.ini", "module": "config", "name": "core.config", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 17192, "name": "MainThread"}, "time": {"repr": "2025-06-21 03:13:23.056030+08:00", "timestamp": 1750446803.05603}}}
{"text": "2025-06-21 03:13:44.131 | INFO | data.clients.influxdb_client:_connect:100 | InfluxDB连接建立成功\n", "record": {"elapsed": {"repr": "0:00:31.061523", "seconds": 31.061523}, "exception": null, "extra": {"component": "InfluxDBClient"}, "file": {"name": "influxdb_client.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\clients\\influxdb_client.py"}, "function": "_connect", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 100, "message": "InfluxDB连接建立成功", "module": "influxdb_client", "name": "data.clients.influxdb_client", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 17192, "name": "MainThread"}, "time": {"repr": "2025-06-21 03:13:44.131198+08:00", "timestamp": 1750446824.131198}}}
{"text": "2025-06-21 03:13:44.135 | INFO | data.clients.influxdb_client:__init__:75 | InfluxDB客户端初始化完成: http://localhost:8086, 组织: crypto, 存储桶: market_data\n", "record": {"elapsed": {"repr": "0:00:31.065515", "seconds": 31.065515}, "exception": null, "extra": {"component": "InfluxDBClient"}, "file": {"name": "influxdb_client.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\clients\\influxdb_client.py"}, "function": "__init__", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 75, "message": "InfluxDB客户端初始化完成: http://localhost:8086, 组织: crypto, 存储桶: market_data", "module": "influxdb_client", "name": "data.clients.influxdb_client", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 17192, "name": "MainThread"}, "time": {"repr": "2025-06-21 03:13:44.135190+08:00", "timestamp": 1750446824.13519}}}
{"text": "2025-06-21 03:13:44.210 | INFO | data.clients.mysql_client:_connect:130 | MySQL连接建立成功\n", "record": {"elapsed": {"repr": "0:00:31.140742", "seconds": 31.140742}, "exception": null, "extra": {"component": "MySQLClient"}, "file": {"name": "mysql_client.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\clients\\mysql_client.py"}, "function": "_connect", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 130, "message": "MySQL连接建立成功", "module": "mysql_client", "name": "data.clients.mysql_client", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 17192, "name": "MainThread"}, "time": {"repr": "2025-06-21 03:13:44.210417+08:00", "timestamp": 1750446824.210417}}}
{"text": "2025-06-21 03:13:44.228 | INFO | data.clients.mysql_client:_init_tables:187 | 数据库表初始化完成\n", "record": {"elapsed": {"repr": "0:00:31.159304", "seconds": 31.159304}, "exception": null, "extra": {"component": "MySQLClient"}, "file": {"name": "mysql_client.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\clients\\mysql_client.py"}, "function": "_init_tables", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 187, "message": "数据库表初始化完成", "module": "mysql_client", "name": "data.clients.mysql_client", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 17192, "name": "MainThread"}, "time": {"repr": "2025-06-21 03:13:44.228979+08:00", "timestamp": 1750446824.228979}}}
{"text": "2025-06-21 03:13:44.230 | INFO | data.clients.mysql_client:__init__:103 | MySQL客户端初始化完成: localhost:3306/crypto_trading\n", "record": {"elapsed": {"repr": "0:00:31.160809", "seconds": 31.160809}, "exception": null, "extra": {"component": "MySQLClient"}, "file": {"name": "mysql_client.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\clients\\mysql_client.py"}, "function": "__init__", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 103, "message": "MySQL客户端初始化完成: localhost:3306/crypto_trading", "module": "mysql_client", "name": "data.clients.mysql_client", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 17192, "name": "MainThread"}, "time": {"repr": "2025-06-21 03:13:44.230484+08:00", "timestamp": 1750446824.230484}}}
{"text": "2025-06-21 03:13:44.247 | INFO | ml.models.versioning:__init__:56 | 模型版本管理器初始化完成\n", "record": {"elapsed": {"repr": "0:00:31.177363", "seconds": 31.177363}, "exception": null, "extra": {"component": "ModelVersionManager"}, "file": {"name": "versioning.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\ml\\models\\versioning.py"}, "function": "__init__", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 56, "message": "模型版本管理器初始化完成", "module": "versioning", "name": "ml.models.versioning", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 17192, "name": "MainThread"}, "time": {"repr": "2025-06-21 03:13:44.247038+08:00", "timestamp": 1750446824.247038}}}
{"text": "2025-06-21 03:13:44.248 | INFO | ml.models.online_learner:__init__:102 | 在线学习器初始化完成\n", "record": {"elapsed": {"repr": "0:00:31.178353", "seconds": 31.178353}, "exception": null, "extra": {"component": "OnlineLearner"}, "file": {"name": "online_learner.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\ml\\models\\online_learner.py"}, "function": "__init__", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 102, "message": "在线学习器初始化完成", "module": "online_learner", "name": "ml.models.online_learner", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 17192, "name": "MainThread"}, "time": {"repr": "2025-06-21 03:13:44.248028+08:00", "timestamp": 1750446824.248028}}}
{"text": "2025-06-21 03:13:44.249 | INFO | strategy.base:__init__:54 | 基础策略初始化: UnifiedMLStrategy\n", "record": {"elapsed": {"repr": "0:00:31.179352", "seconds": 31.179352}, "exception": null, "extra": {"component": "UnifiedMLStrategy"}, "file": {"name": "base.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\strategy\\base.py"}, "function": "__init__", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 54, "message": "基础策略初始化: UnifiedMLStrategy", "module": "base", "name": "strategy.base", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 17192, "name": "MainThread"}, "time": {"repr": "2025-06-21 03:13:44.249027+08:00", "timestamp": 1750446824.249027}}}
{"text": "2025-06-21 03:13:44.251 | INFO | strategy.unified_ml:__init__:86 | 统一ML策略初始化完成，支持交易对: ['BTCUSDT', 'ETHUSDT']\n", "record": {"elapsed": {"repr": "0:00:31.181876", "seconds": 31.181876}, "exception": null, "extra": {"component": "UnifiedMLStrategy"}, "file": {"name": "unified_ml.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\strategy\\unified_ml.py"}, "function": "__init__", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 86, "message": "统一ML策略初始化完成，支持交易对: ['BTCUSDT', 'ETHUSDT']", "module": "unified_ml", "name": "strategy.unified_ml", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 17192, "name": "MainThread"}, "time": {"repr": "2025-06-21 03:13:44.251551+08:00", "timestamp": 1750446824.251551}}}
{"text": "2025-06-21 03:13:44.252 | INFO | __main__:start:85 | 启动交易策略应用\n", "record": {"elapsed": {"repr": "0:00:31.182875", "seconds": 31.182875}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\main.py"}, "function": "start", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 85, "message": "启动交易策略应用", "module": "main", "name": "__main__", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 17192, "name": "MainThread"}, "time": {"repr": "2025-06-21 03:13:44.252550+08:00", "timestamp": 1750446824.25255}}}
{"text": "2025-06-21 03:13:44.257 | INFO | __main__:start:90 | 未找到有效模型，开始训练...\n", "record": {"elapsed": {"repr": "0:00:31.187884", "seconds": 31.187884}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\main.py"}, "function": "start", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 90, "message": "未找到有效模型，开始训练...", "module": "main", "name": "__main__", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 17192, "name": "MainThread"}, "time": {"repr": "2025-06-21 03:13:44.257559+08:00", "timestamp": 1750446824.257559}}}
{"text": "2025-06-21 03:13:44.258 | INFO | data.real_data_loader:__init__:60 | 数据加载器初始化完成，支持交易对: ['BTCUSDT', 'ETHUSDT'], 时间周期: ['1m', '5m', '15m', '1h', '4h', '1d']\n", "record": {"elapsed": {"repr": "0:00:31.188888", "seconds": 31.188888}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "__init__", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 60, "message": "数据加载器初始化完成，支持交易对: ['BTCUSDT', 'ETHUSDT'], 时间周期: ['1m', '5m', '15m', '1h', '4h', '1d']", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 17192, "name": "MainThread"}, "time": {"repr": "2025-06-21 03:13:44.258563+08:00", "timestamp": 1750446824.258563}}}
{"text": "2025-06-21 03:13:44.261 | INFO | __main__:train_model:179 | 加载训练数据...\n", "record": {"elapsed": {"repr": "0:00:31.191430", "seconds": 31.19143}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\main.py"}, "function": "train_model", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 179, "message": "加载训练数据...", "module": "main", "name": "__main__", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 17192, "name": "MainThread"}, "time": {"repr": "2025-06-21 03:13:44.261105+08:00", "timestamp": 1750446824.261105}}}
{"text": "2025-06-21 03:13:44.262 | INFO | data.real_data_loader:load_training_data:86 | 开始加载训练数据: 2025-03-23 03:13:44.262123 到 2025-06-21 03:13:44.262123\n", "record": {"elapsed": {"repr": "0:00:31.192448", "seconds": 31.192448}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "load_training_data", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 86, "message": "开始加载训练数据: 2025-03-23 03:13:44.262123 到 2025-06-21 03:13:44.262123", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 17192, "name": "MainThread"}, "time": {"repr": "2025-06-21 03:13:44.262123+08:00", "timestamp": 1750446824.262123}}}
{"text": "2025-06-21 03:14:22.647 | INFO | data.real_data_loader:load_training_data:104 | 加载训练数据成功: BTCUSDT_1m, 记录数: 87628\n", "record": {"elapsed": {"repr": "0:01:09.577453", "seconds": 69.577453}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "load_training_data", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 104, "message": "加载训练数据成功: BTCUSDT_1m, 记录数: 87628", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 17192, "name": "MainThread"}, "time": {"repr": "2025-06-21 03:14:22.647128+08:00", "timestamp": 1750446862.647128}}}
{"text": "2025-06-21 03:14:31.121 | INFO | data.real_data_loader:load_training_data:104 | 加载训练数据成功: BTCUSDT_5m, 记录数: 17529\n", "record": {"elapsed": {"repr": "0:01:18.051901", "seconds": 78.051901}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "load_training_data", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 104, "message": "加载训练数据成功: BTCUSDT_5m, 记录数: 17529", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 17192, "name": "MainThread"}, "time": {"repr": "2025-06-21 03:14:31.121576+08:00", "timestamp": 1750446871.121576}}}
{"text": "2025-06-21 03:14:35.730 | INFO | data.real_data_loader:load_training_data:104 | 加载训练数据成功: BTCUSDT_15m, 记录数: 5834\n", "record": {"elapsed": {"repr": "0:01:22.660325", "seconds": 82.660325}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "load_training_data", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 104, "message": "加载训练数据成功: BTCUSDT_15m, 记录数: 5834", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 17192, "name": "MainThread"}, "time": {"repr": "2025-06-21 03:14:35.730000+08:00", "timestamp": 1750446875.73}}}
{"text": "2025-06-21 03:14:37.103 | INFO | data.real_data_loader:load_training_data:104 | 加载训练数据成功: BTCUSDT_1h, 记录数: 1427\n", "record": {"elapsed": {"repr": "0:01:24.033514", "seconds": 84.033514}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "load_training_data", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 104, "message": "加载训练数据成功: BTCUSDT_1h, 记录数: 1427", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 17192, "name": "MainThread"}, "time": {"repr": "2025-06-21 03:14:37.103189+08:00", "timestamp": 1750446877.103189}}}
{"text": "2025-06-21 03:14:37.920 | INFO | data.real_data_loader:load_training_data:104 | 加载训练数据成功: BTCUSDT_4h, 记录数: 316\n", "record": {"elapsed": {"repr": "0:01:24.851224", "seconds": 84.851224}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "load_training_data", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 104, "message": "加载训练数据成功: BTCUSDT_4h, 记录数: 316", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 17192, "name": "MainThread"}, "time": {"repr": "2025-06-21 03:14:37.920899+08:00", "timestamp": 1750446877.920899}}}
{"text": "2025-06-21 03:14:38.058 | INFO | data.real_data_loader:load_training_data:104 | 加载训练数据成功: BTCUSDT_1d, 记录数: 8\n", "record": {"elapsed": {"repr": "0:01:24.988477", "seconds": 84.988477}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "load_training_data", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 104, "message": "加载训练数据成功: BTCUSDT_1d, 记录数: 8", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 17192, "name": "MainThread"}, "time": {"repr": "2025-06-21 03:14:38.058152+08:00", "timestamp": 1750446878.058152}}}
{"text": "2025-06-21 03:15:17.118 | INFO | data.real_data_loader:load_training_data:104 | 加载训练数据成功: ETHUSDT_1m, 记录数: 86927\n", "record": {"elapsed": {"repr": "0:02:04.048544", "seconds": 124.048544}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "load_training_data", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 104, "message": "加载训练数据成功: ETHUSDT_1m, 记录数: 86927", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 17192, "name": "MainThread"}, "time": {"repr": "2025-06-21 03:15:17.118219+08:00", "timestamp": 1750446917.118219}}}
{"text": "2025-06-21 03:15:24.296 | INFO | data.real_data_loader:load_training_data:104 | 加载训练数据成功: ETHUSDT_5m, 记录数: 17505\n", "record": {"elapsed": {"repr": "0:02:11.227126", "seconds": 131.227126}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "load_training_data", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 104, "message": "加载训练数据成功: ETHUSDT_5m, 记录数: 17505", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 17192, "name": "MainThread"}, "time": {"repr": "2025-06-21 03:15:24.296801+08:00", "timestamp": 1750446924.296801}}}
{"text": "2025-06-21 03:15:27.052 | INFO | data.real_data_loader:load_training_data:104 | 加载训练数据成功: ETHUSDT_15m, 记录数: 5832\n", "record": {"elapsed": {"repr": "0:02:13.983194", "seconds": 133.983194}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "load_training_data", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 104, "message": "加载训练数据成功: ETHUSDT_15m, 记录数: 5832", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 17192, "name": "MainThread"}, "time": {"repr": "2025-06-21 03:15:27.052869+08:00", "timestamp": 1750446927.052869}}}
{"text": "2025-06-21 03:15:27.676 | INFO | data.real_data_loader:load_training_data:104 | 加载训练数据成功: ETHUSDT_1h, 记录数: 1427\n", "record": {"elapsed": {"repr": "0:02:14.606524", "seconds": 134.606524}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "load_training_data", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 104, "message": "加载训练数据成功: ETHUSDT_1h, 记录数: 1427", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 17192, "name": "MainThread"}, "time": {"repr": "2025-06-21 03:15:27.676199+08:00", "timestamp": 1750446927.676199}}}
{"text": "2025-06-21 03:15:27.871 | INFO | data.real_data_loader:load_training_data:104 | 加载训练数据成功: ETHUSDT_4h, 记录数: 317\n", "record": {"elapsed": {"repr": "0:02:14.802096", "seconds": 134.802096}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "load_training_data", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 104, "message": "加载训练数据成功: ETHUSDT_4h, 记录数: 317", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 17192, "name": "MainThread"}, "time": {"repr": "2025-06-21 03:15:27.871771+08:00", "timestamp": 1750446927.871771}}}
{"text": "2025-06-21 03:15:27.969 | INFO | data.real_data_loader:load_training_data:104 | 加载训练数据成功: ETHUSDT_1d, 记录数: 8\n", "record": {"elapsed": {"repr": "0:02:14.899757", "seconds": 134.899757}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "load_training_data", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 104, "message": "加载训练数据成功: ETHUSDT_1d, 记录数: 8", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 17192, "name": "MainThread"}, "time": {"repr": "2025-06-21 03:15:27.969432+08:00", "timestamp": 1750446927.969432}}}
{"text": "2025-06-21 04:04:38.237 | INFO | data.real_data_loader:_align_multi_timeframe_data:371 | 多时间周期数据对齐完成，交易对数: 2\n", "record": {"elapsed": {"repr": "0:51:25.167924", "seconds": 3085.167924}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "_align_multi_timeframe_data", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 371, "message": "多时间周期数据对齐完成，交易对数: 2", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 17192, "name": "MainThread"}, "time": {"repr": "2025-06-21 04:04:38.237599+08:00", "timestamp": 1750449878.237599}}}
{"text": "2025-06-21 04:04:38.331 | INFO | data.real_data_loader:load_training_data:113 | 训练数据加载完成，总数据集: 2\n", "record": {"elapsed": {"repr": "0:51:25.262281", "seconds": 3085.262281}, "exception": null, "extra": {"component": "RealDataLoader"}, "file": {"name": "real_data_loader.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\real_data_loader.py"}, "function": "load_training_data", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 113, "message": "训练数据加载完成，总数据集: 2", "module": "real_data_loader", "name": "data.real_data_loader", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 17192, "name": "MainThread"}, "time": {"repr": "2025-06-21 04:04:38.331956+08:00", "timestamp": 1750449878.331956}}}
{"text": "2025-06-21 04:04:38.375 | INFO | ml.models.model_trainer:__init__:94 | 模型训练器初始化完成\n", "record": {"elapsed": {"repr": "0:51:25.305460", "seconds": 3085.30546}, "exception": null, "extra": {"component": "ModelTrainer"}, "file": {"name": "model_trainer.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\ml\\models\\model_trainer.py"}, "function": "__init__", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 94, "message": "模型训练器初始化完成", "module": "model_trainer", "name": "ml.models.model_trainer", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 17192, "name": "MainThread"}, "time": {"repr": "2025-06-21 04:04:38.375135+08:00", "timestamp": 1750449878.375135}}}
{"text": "2025-06-21 04:04:38.375 | INFO | __main__:train_model:186 | 开始训练模型...\n", "record": {"elapsed": {"repr": "0:51:25.305460", "seconds": 3085.30546}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\main.py"}, "function": "train_model", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 186, "message": "开始训练模型...", "module": "main", "name": "__main__", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 17192, "name": "MainThread"}, "time": {"repr": "2025-06-21 04:04:38.375135+08:00", "timestamp": 1750449878.375135}}}
{"text": "2025-06-21 04:04:38.376 | INFO | ml.models.model_trainer:train:110 | 开始模型训练，数据集数量: 2\n", "record": {"elapsed": {"repr": "0:51:25.306459", "seconds": 3085.306459}, "exception": null, "extra": {"component": "ModelTrainer"}, "file": {"name": "model_trainer.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\ml\\models\\model_trainer.py"}, "function": "train", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 110, "message": "开始模型训练，数据集数量: 2", "module": "model_trainer", "name": "ml.models.model_trainer", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 17192, "name": "MainThread"}, "time": {"repr": "2025-06-21 04:04:38.376134+08:00", "timestamp": 1750449878.376134}}}
{"text": "2025-06-21 04:04:38.377 | INFO | ml.models.model_trainer:train:115 | 训练模型: BTCUSDT\n", "record": {"elapsed": {"repr": "0:51:25.307464", "seconds": 3085.307464}, "exception": null, "extra": {"component": "ModelTrainer"}, "file": {"name": "model_trainer.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\ml\\models\\model_trainer.py"}, "function": "train", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 115, "message": "训练模型: BTCUSDT", "module": "model_trainer", "name": "ml.models.model_trainer", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 17192, "name": "MainThread"}, "time": {"repr": "2025-06-21 04:04:38.377139+08:00", "timestamp": 1750449878.377139}}}
{"text": "2025-06-21 04:07:49.637 | ERROR | ml.models.model_trainer:_train_neural_network:476 | 神经网络训练失败: could not determine the shape of object type 'Series'\n", "record": {"elapsed": {"repr": "0:54:36.568105", "seconds": 3276.568105}, "exception": null, "extra": {"component": "ModelTrainer"}, "file": {"name": "model_trainer.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\ml\\models\\model_trainer.py"}, "function": "_train_neural_network", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 476, "message": "神经网络训练失败: could not determine the shape of object type 'Series'", "module": "model_trainer", "name": "ml.models.model_trainer", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 17192, "name": "MainThread"}, "time": {"repr": "2025-06-21 04:07:49.637780+08:00", "timestamp": 1750450069.63778}}}
{"text": "2025-06-21 04:07:49.659 | INFO | ml.models.model_trainer:_select_best_model:536 | 最佳模型选择: BTCUSDT - random_forest, F1: 0.9999\n", "record": {"elapsed": {"repr": "0:54:36.589512", "seconds": 3276.589512}, "exception": null, "extra": {"component": "ModelTrainer"}, "file": {"name": "model_trainer.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\ml\\models\\model_trainer.py"}, "function": "_select_best_model", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 536, "message": "最佳模型选择: BTCUSDT - random_forest, F1: 0.9999", "module": "model_trainer", "name": "ml.models.model_trainer", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 17192, "name": "MainThread"}, "time": {"repr": "2025-06-21 04:07:49.659187+08:00", "timestamp": 1750450069.659187}}}
{"text": "2025-06-21 04:07:49.784 | INFO | ml.models.model_trainer:_save_model:579 | 模型保存成功: models/BTCUSDT_random_forest_20250621_040749.pkl\n", "record": {"elapsed": {"repr": "0:54:36.714707", "seconds": 3276.714707}, "exception": null, "extra": {"component": "ModelTrainer"}, "file": {"name": "model_trainer.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\ml\\models\\model_trainer.py"}, "function": "_save_model", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 579, "message": "模型保存成功: models/BTCUSDT_random_forest_20250621_040749.pkl", "module": "model_trainer", "name": "ml.models.model_trainer", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 17192, "name": "MainThread"}, "time": {"repr": "2025-06-21 04:07:49.784382+08:00", "timestamp": 1750450069.784382}}}
{"text": "2025-06-21 04:07:49.800 | ERROR | data.clients.mysql_client:execute_update:256 | 更新执行失败: (pymysql.err.ProgrammingError) (1064, \"You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '%s' at line 1\")\n[SQL: UPDATE model_versions SET is_active = 0 WHERE model_type = %%s]\n(Background on this error at: https://sqlalche.me/e/20/f405)\n", "record": {"elapsed": {"repr": "0:54:36.730785", "seconds": 3276.730785}, "exception": null, "extra": {"component": "MySQLClient"}, "file": {"name": "mysql_client.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\clients\\mysql_client.py"}, "function": "execute_update", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 256, "message": "更新执行失败: (pymysql.err.ProgrammingError) (1064, \"You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '%s' at line 1\")\n[SQL: UPDATE model_versions SET is_active = 0 WHERE model_type = %%s]\n(Background on this error at: https://sqlalche.me/e/20/f405)", "module": "mysql_client", "name": "data.clients.mysql_client", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 17192, "name": "MainThread"}, "time": {"repr": "2025-06-21 04:07:49.800460+08:00", "timestamp": 1750450069.80046}}}
{"text": "2025-06-21 04:07:49.825 | ERROR | data.clients.mysql_client:execute_query:231 | 查询执行失败: (pymysql.err.ProgrammingError) (1064, \"You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '%s AND is_active = 0 \\n                ORDER BY created_at DESC' at line 3\")\n[SQL: \n                SELECT version, model_path, created_at \n                FROM model_versions \n                WHERE model_type = %%s AND is_active = 0 \n                ORDER BY created_at DESC\n            ]\n(Background on this error at: https://sqlalche.me/e/20/f405)\n", "record": {"elapsed": {"repr": "0:54:36.755790", "seconds": 3276.75579}, "exception": null, "extra": {"component": "MySQLClient"}, "file": {"name": "mysql_client.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\clients\\mysql_client.py"}, "function": "execute_query", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 231, "message": "查询执行失败: (pymysql.err.ProgrammingError) (1064, \"You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '%s AND is_active = 0 \\n                ORDER BY created_at DESC' at line 3\")\n[SQL: \n                SELECT version, model_path, created_at \n                FROM model_versions \n                WHERE model_type = %%s AND is_active = 0 \n                ORDER BY created_at DESC\n            ]\n(Background on this error at: https://sqlalche.me/e/20/f405)", "module": "mysql_client", "name": "data.clients.mysql_client", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 17192, "name": "MainThread"}, "time": {"repr": "2025-06-21 04:07:49.825465+08:00", "timestamp": 1750450069.825465}}}
{"text": "2025-06-21 04:07:49.826 | INFO | ml.models.versioning:save_model_version:115 | 模型版本保存成功: BTCUSDT_20250621_040749 (random_forest)\n", "record": {"elapsed": {"repr": "0:54:36.756788", "seconds": 3276.756788}, "exception": null, "extra": {"component": "ModelVersionManager"}, "file": {"name": "versioning.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\ml\\models\\versioning.py"}, "function": "save_model_version", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 115, "message": "模型版本保存成功: BTCUSDT_20250621_040749 (random_forest)", "module": "versioning", "name": "ml.models.versioning", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 17192, "name": "MainThread"}, "time": {"repr": "2025-06-21 04:07:49.826463+08:00", "timestamp": 1750450069.826463}}}
{"text": "2025-06-21 04:07:49.827 | INFO | ml.models.model_trainer:_update_model_version:612 | 模型版本更新成功: BTCUSDT_20250621_040749\n", "record": {"elapsed": {"repr": "0:54:36.757787", "seconds": 3276.757787}, "exception": null, "extra": {"component": "ModelTrainer"}, "file": {"name": "model_trainer.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\ml\\models\\model_trainer.py"}, "function": "_update_model_version", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 612, "message": "模型版本更新成功: BTCUSDT_20250621_040749", "module": "model_trainer", "name": "ml.models.model_trainer", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 17192, "name": "MainThread"}, "time": {"repr": "2025-06-21 04:07:49.827462+08:00", "timestamp": 1750450069.827462}}}
{"text": "2025-06-21 04:07:49.828 | INFO | ml.models.model_trainer:train:141 | 模型训练完成: BTCUSDT, 最佳模型: random_forest\n", "record": {"elapsed": {"repr": "0:54:36.758789", "seconds": 3276.758789}, "exception": null, "extra": {"component": "ModelTrainer"}, "file": {"name": "model_trainer.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\ml\\models\\model_trainer.py"}, "function": "train", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 141, "message": "模型训练完成: BTCUSDT, 最佳模型: random_forest", "module": "model_trainer", "name": "ml.models.model_trainer", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 17192, "name": "MainThread"}, "time": {"repr": "2025-06-21 04:07:49.828464+08:00", "timestamp": 1750450069.828464}}}
{"text": "2025-06-21 04:07:49.828 | INFO | ml.models.model_trainer:train:115 | 训练模型: ETHUSDT\n", "record": {"elapsed": {"repr": "0:54:36.758789", "seconds": 3276.758789}, "exception": null, "extra": {"component": "ModelTrainer"}, "file": {"name": "model_trainer.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\ml\\models\\model_trainer.py"}, "function": "train", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 115, "message": "训练模型: ETHUSDT", "module": "model_trainer", "name": "ml.models.model_trainer", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 17192, "name": "MainThread"}, "time": {"repr": "2025-06-21 04:07:49.828464+08:00", "timestamp": 1750450069.828464}}}
{"text": "2025-06-21 04:29:26.107 | ERROR | ml.models.model_trainer:_train_neural_network:476 | 神经网络训练失败: could not determine the shape of object type 'Series'\n", "record": {"elapsed": {"repr": "1:16:13.038314", "seconds": 4573.038314}, "exception": null, "extra": {"component": "ModelTrainer"}, "file": {"name": "model_trainer.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\ml\\models\\model_trainer.py"}, "function": "_train_neural_network", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 476, "message": "神经网络训练失败: could not determine the shape of object type 'Series'", "module": "model_trainer", "name": "ml.models.model_trainer", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 17192, "name": "MainThread"}, "time": {"repr": "2025-06-21 04:29:26.107989+08:00", "timestamp": 1750451366.107989}}}
{"text": "2025-06-21 04:29:26.110 | INFO | ml.models.model_trainer:_select_best_model:536 | 最佳模型选择: ETHUSDT - gradient_boosting, F1: 0.9636\n", "record": {"elapsed": {"repr": "1:16:13.041317", "seconds": 4573.041317}, "exception": null, "extra": {"component": "ModelTrainer"}, "file": {"name": "model_trainer.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\ml\\models\\model_trainer.py"}, "function": "_select_best_model", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 536, "message": "最佳模型选择: ETHUSDT - gradient_boosting, F1: 0.9636", "module": "model_trainer", "name": "ml.models.model_trainer", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 17192, "name": "MainThread"}, "time": {"repr": "2025-06-21 04:29:26.110992+08:00", "timestamp": 1750451366.110992}}}
{"text": "2025-06-21 04:29:26.213 | INFO | ml.models.model_trainer:_save_model:579 | 模型保存成功: models/ETHUSDT_gradient_boosting_20250621_042926.pkl\n", "record": {"elapsed": {"repr": "1:16:13.143950", "seconds": 4573.14395}, "exception": null, "extra": {"component": "ModelTrainer"}, "file": {"name": "model_trainer.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\ml\\models\\model_trainer.py"}, "function": "_save_model", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 579, "message": "模型保存成功: models/ETHUSDT_gradient_boosting_20250621_042926.pkl", "module": "model_trainer", "name": "ml.models.model_trainer", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 17192, "name": "MainThread"}, "time": {"repr": "2025-06-21 04:29:26.213625+08:00", "timestamp": 1750451366.213625}}}
{"text": "2025-06-21 04:29:26.231 | ERROR | data.clients.mysql_client:execute_update:256 | 更新执行失败: (pymysql.err.ProgrammingError) (1064, \"You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '%s' at line 1\")\n[SQL: UPDATE model_versions SET is_active = 0 WHERE model_type = %%s]\n(Background on this error at: https://sqlalche.me/e/20/f405)\n", "record": {"elapsed": {"repr": "1:16:13.161643", "seconds": 4573.161643}, "exception": null, "extra": {"component": "MySQLClient"}, "file": {"name": "mysql_client.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\clients\\mysql_client.py"}, "function": "execute_update", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 256, "message": "更新执行失败: (pymysql.err.ProgrammingError) (1064, \"You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '%s' at line 1\")\n[SQL: UPDATE model_versions SET is_active = 0 WHERE model_type = %%s]\n(Background on this error at: https://sqlalche.me/e/20/f405)", "module": "mysql_client", "name": "data.clients.mysql_client", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 17192, "name": "MainThread"}, "time": {"repr": "2025-06-21 04:29:26.231318+08:00", "timestamp": 1750451366.231318}}}
{"text": "2025-06-21 04:29:26.246 | ERROR | data.clients.mysql_client:execute_query:231 | 查询执行失败: (pymysql.err.ProgrammingError) (1064, \"You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '%s AND is_active = 0 \\n                ORDER BY created_at DESC' at line 3\")\n[SQL: \n                SELECT version, model_path, created_at \n                FROM model_versions \n                WHERE model_type = %%s AND is_active = 0 \n                ORDER BY created_at DESC\n            ]\n(Background on this error at: https://sqlalche.me/e/20/f405)\n", "record": {"elapsed": {"repr": "1:16:13.176682", "seconds": 4573.176682}, "exception": null, "extra": {"component": "MySQLClient"}, "file": {"name": "mysql_client.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\clients\\mysql_client.py"}, "function": "execute_query", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 231, "message": "查询执行失败: (pymysql.err.ProgrammingError) (1064, \"You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '%s AND is_active = 0 \\n                ORDER BY created_at DESC' at line 3\")\n[SQL: \n                SELECT version, model_path, created_at \n                FROM model_versions \n                WHERE model_type = %%s AND is_active = 0 \n                ORDER BY created_at DESC\n            ]\n(Background on this error at: https://sqlalche.me/e/20/f405)", "module": "mysql_client", "name": "data.clients.mysql_client", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 17192, "name": "MainThread"}, "time": {"repr": "2025-06-21 04:29:26.246357+08:00", "timestamp": 1750451366.246357}}}
{"text": "2025-06-21 04:29:26.247 | INFO | ml.models.versioning:save_model_version:115 | 模型版本保存成功: ETHUSDT_20250621_042926 (gradient_boosting)\n", "record": {"elapsed": {"repr": "1:16:13.177678", "seconds": 4573.177678}, "exception": null, "extra": {"component": "ModelVersionManager"}, "file": {"name": "versioning.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\ml\\models\\versioning.py"}, "function": "save_model_version", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 115, "message": "模型版本保存成功: ETHUSDT_20250621_042926 (gradient_boosting)", "module": "versioning", "name": "ml.models.versioning", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 17192, "name": "MainThread"}, "time": {"repr": "2025-06-21 04:29:26.247353+08:00", "timestamp": 1750451366.247353}}}
{"text": "2025-06-21 04:29:26.248 | INFO | ml.models.model_trainer:_update_model_version:612 | 模型版本更新成功: ETHUSDT_20250621_042926\n", "record": {"elapsed": {"repr": "1:16:13.178684", "seconds": 4573.178684}, "exception": null, "extra": {"component": "ModelTrainer"}, "file": {"name": "model_trainer.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\ml\\models\\model_trainer.py"}, "function": "_update_model_version", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 612, "message": "模型版本更新成功: ETHUSDT_20250621_042926", "module": "model_trainer", "name": "ml.models.model_trainer", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 17192, "name": "MainThread"}, "time": {"repr": "2025-06-21 04:29:26.248359+08:00", "timestamp": 1750451366.248359}}}
{"text": "2025-06-21 04:29:26.248 | INFO | ml.models.model_trainer:train:141 | 模型训练完成: ETHUSDT, 最佳模型: gradient_boosting\n", "record": {"elapsed": {"repr": "1:16:13.178684", "seconds": 4573.178684}, "exception": null, "extra": {"component": "ModelTrainer"}, "file": {"name": "model_trainer.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\ml\\models\\model_trainer.py"}, "function": "train", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 141, "message": "模型训练完成: ETHUSDT, 最佳模型: gradient_boosting", "module": "model_trainer", "name": "ml.models.model_trainer", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 17192, "name": "MainThread"}, "time": {"repr": "2025-06-21 04:29:26.248359+08:00", "timestamp": 1750451366.248359}}}
{"text": "2025-06-21 04:29:26.248 | INFO | ml.models.model_trainer:train:145 | 所有模型训练完成，成功训练: 2\n", "record": {"elapsed": {"repr": "1:16:13.178684", "seconds": 4573.178684}, "exception": null, "extra": {"component": "ModelTrainer"}, "file": {"name": "model_trainer.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\ml\\models\\model_trainer.py"}, "function": "train", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 145, "message": "所有模型训练完成，成功训练: 2", "module": "model_trainer", "name": "ml.models.model_trainer", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 17192, "name": "MainThread"}, "time": {"repr": "2025-06-21 04:29:26.248359+08:00", "timestamp": 1750451366.248359}}}
{"text": "2025-06-21 04:29:26.249 | INFO | __main__:train_model:189 | 模型训练完成\n", "record": {"elapsed": {"repr": "1:16:13.179684", "seconds": 4573.179684}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\main.py"}, "function": "train_model", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 189, "message": "模型训练完成", "module": "main", "name": "__main__", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 17192, "name": "MainThread"}, "time": {"repr": "2025-06-21 04:29:26.249359+08:00", "timestamp": 1750451366.249359}}}
{"text": "2025-06-21 04:29:26.249 | INFO | ml.models.prediction:__init__:76 | 预测引擎初始化完成\n", "record": {"elapsed": {"repr": "1:16:13.179684", "seconds": 4573.179684}, "exception": null, "extra": {"component": "PredictionEngine"}, "file": {"name": "prediction.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\ml\\models\\prediction.py"}, "function": "__init__", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 76, "message": "预测引擎初始化完成", "module": "prediction", "name": "ml.models.prediction", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 17192, "name": "MainThread"}, "time": {"repr": "2025-06-21 04:29:26.249359+08:00", "timestamp": 1750451366.249359}}}
{"text": "2025-06-21 04:29:26.250 | INFO | strategy.unified_ml:set_prediction_engine:96 | 预测引擎已设置\n", "record": {"elapsed": {"repr": "1:16:13.181193", "seconds": 4573.181193}, "exception": null, "extra": {"component": "UnifiedMLStrategy"}, "file": {"name": "unified_ml.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\strategy\\unified_ml.py"}, "function": "set_prediction_engine", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 96, "message": "预测引擎已设置", "module": "unified_ml", "name": "strategy.unified_ml", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 17192, "name": "MainThread"}, "time": {"repr": "2025-06-21 04:29:26.250868+08:00", "timestamp": 1750451366.250868}}}
{"text": "2025-06-21 04:29:56.627 | WARNING | strategy.unified_ml:on_market_data:118 | 技术指标计算失败: BTCUSDT\n", "record": {"elapsed": {"repr": "1:16:43.558071", "seconds": 4603.558071}, "exception": null, "extra": {"component": "UnifiedMLStrategy"}, "file": {"name": "unified_ml.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\strategy\\unified_ml.py"}, "function": "on_market_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 118, "message": "技术指标计算失败: BTCUSDT", "module": "unified_ml", "name": "strategy.unified_ml", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 55304, "name": "kafka-consumer-kline.data"}, "time": {"repr": "2025-06-21 04:29:56.627746+08:00", "timestamp": 1750451396.627746}}}
{"text": "2025-06-21 04:29:56.628 | WARNING | strategy.unified_ml:on_market_data:118 | 技术指标计算失败: BTCUSDT\n", "record": {"elapsed": {"repr": "1:16:43.559070", "seconds": 4603.55907}, "exception": null, "extra": {"component": "UnifiedMLStrategy"}, "file": {"name": "unified_ml.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\strategy\\unified_ml.py"}, "function": "on_market_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 118, "message": "技术指标计算失败: BTCUSDT", "module": "unified_ml", "name": "strategy.unified_ml", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 55304, "name": "kafka-consumer-kline.data"}, "time": {"repr": "2025-06-21 04:29:56.628745+08:00", "timestamp": 1750451396.628745}}}
{"text": "2025-06-21 04:29:56.628 | WARNING | strategy.unified_ml:on_market_data:118 | 技术指标计算失败: BTCUSDT\n", "record": {"elapsed": {"repr": "1:16:43.559070", "seconds": 4603.55907}, "exception": null, "extra": {"component": "UnifiedMLStrategy"}, "file": {"name": "unified_ml.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\strategy\\unified_ml.py"}, "function": "on_market_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 118, "message": "技术指标计算失败: BTCUSDT", "module": "unified_ml", "name": "strategy.unified_ml", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 55304, "name": "kafka-consumer-kline.data"}, "time": {"repr": "2025-06-21 04:29:56.628745+08:00", "timestamp": 1750451396.628745}}}
{"text": "2025-06-21 04:29:56.629 | WARNING | strategy.unified_ml:on_market_data:118 | 技术指标计算失败: BTCUSDT\n", "record": {"elapsed": {"repr": "1:16:43.560070", "seconds": 4603.56007}, "exception": null, "extra": {"component": "UnifiedMLStrategy"}, "file": {"name": "unified_ml.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\strategy\\unified_ml.py"}, "function": "on_market_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 118, "message": "技术指标计算失败: BTCUSDT", "module": "unified_ml", "name": "strategy.unified_ml", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 55304, "name": "kafka-consumer-kline.data"}, "time": {"repr": "2025-06-21 04:29:56.629745+08:00", "timestamp": 1750451396.629745}}}
{"text": "2025-06-21 04:29:56.630 | WARNING | strategy.unified_ml:on_market_data:118 | 技术指标计算失败: BTCUSDT\n", "record": {"elapsed": {"repr": "1:16:43.561104", "seconds": 4603.561104}, "exception": null, "extra": {"component": "UnifiedMLStrategy"}, "file": {"name": "unified_ml.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\strategy\\unified_ml.py"}, "function": "on_market_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 118, "message": "技术指标计算失败: BTCUSDT", "module": "unified_ml", "name": "strategy.unified_ml", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 55304, "name": "kafka-consumer-kline.data"}, "time": {"repr": "2025-06-21 04:29:56.630779+08:00", "timestamp": 1750451396.630779}}}
{"text": "2025-06-21 04:29:56.630 | WARNING | strategy.unified_ml:on_market_data:118 | 技术指标计算失败: BTCUSDT\n", "record": {"elapsed": {"repr": "1:16:43.561104", "seconds": 4603.561104}, "exception": null, "extra": {"component": "UnifiedMLStrategy"}, "file": {"name": "unified_ml.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\strategy\\unified_ml.py"}, "function": "on_market_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 118, "message": "技术指标计算失败: BTCUSDT", "module": "unified_ml", "name": "strategy.unified_ml", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 55304, "name": "kafka-consumer-kline.data"}, "time": {"repr": "2025-06-21 04:29:56.630779+08:00", "timestamp": 1750451396.630779}}}
{"text": "2025-06-21 04:29:56.630 | WARNING | strategy.unified_ml:on_market_data:118 | 技术指标计算失败: BTCUSDT\n", "record": {"elapsed": {"repr": "1:16:43.561104", "seconds": 4603.561104}, "exception": null, "extra": {"component": "UnifiedMLStrategy"}, "file": {"name": "unified_ml.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\strategy\\unified_ml.py"}, "function": "on_market_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 118, "message": "技术指标计算失败: BTCUSDT", "module": "unified_ml", "name": "strategy.unified_ml", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 55304, "name": "kafka-consumer-kline.data"}, "time": {"repr": "2025-06-21 04:29:56.630779+08:00", "timestamp": 1750451396.630779}}}
{"text": "2025-06-21 04:29:56.631 | WARNING | strategy.unified_ml:on_market_data:118 | 技术指标计算失败: BTCUSDT\n", "record": {"elapsed": {"repr": "1:16:43.562101", "seconds": 4603.562101}, "exception": null, "extra": {"component": "UnifiedMLStrategy"}, "file": {"name": "unified_ml.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\strategy\\unified_ml.py"}, "function": "on_market_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 118, "message": "技术指标计算失败: BTCUSDT", "module": "unified_ml", "name": "strategy.unified_ml", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 55304, "name": "kafka-consumer-kline.data"}, "time": {"repr": "2025-06-21 04:29:56.631776+08:00", "timestamp": 1750451396.631776}}}
{"text": "2025-06-21 04:29:56.631 | WARNING | strategy.unified_ml:on_market_data:118 | 技术指标计算失败: BTCUSDT\n", "record": {"elapsed": {"repr": "1:16:43.562101", "seconds": 4603.562101}, "exception": null, "extra": {"component": "UnifiedMLStrategy"}, "file": {"name": "unified_ml.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\strategy\\unified_ml.py"}, "function": "on_market_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 118, "message": "技术指标计算失败: BTCUSDT", "module": "unified_ml", "name": "strategy.unified_ml", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 55304, "name": "kafka-consumer-kline.data"}, "time": {"repr": "2025-06-21 04:29:56.631776+08:00", "timestamp": 1750451396.631776}}}
{"text": "2025-06-21 04:29:56.632 | WARNING | strategy.unified_ml:on_market_data:118 | 技术指标计算失败: BTCUSDT\n", "record": {"elapsed": {"repr": "1:16:43.563101", "seconds": 4603.563101}, "exception": null, "extra": {"component": "UnifiedMLStrategy"}, "file": {"name": "unified_ml.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\strategy\\unified_ml.py"}, "function": "on_market_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 118, "message": "技术指标计算失败: BTCUSDT", "module": "unified_ml", "name": "strategy.unified_ml", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 55304, "name": "kafka-consumer-kline.data"}, "time": {"repr": "2025-06-21 04:29:56.632776+08:00", "timestamp": 1750451396.632776}}}
{"text": "2025-06-21 04:29:56.633 | WARNING | strategy.unified_ml:on_market_data:118 | 技术指标计算失败: BTCUSDT\n", "record": {"elapsed": {"repr": "1:16:43.564073", "seconds": 4603.564073}, "exception": null, "extra": {"component": "UnifiedMLStrategy"}, "file": {"name": "unified_ml.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\strategy\\unified_ml.py"}, "function": "on_market_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 118, "message": "技术指标计算失败: BTCUSDT", "module": "unified_ml", "name": "strategy.unified_ml", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 55304, "name": "kafka-consumer-kline.data"}, "time": {"repr": "2025-06-21 04:29:56.633748+08:00", "timestamp": 1750451396.633748}}}
{"text": "2025-06-21 04:29:56.635 | WARNING | strategy.unified_ml:on_market_data:118 | 技术指标计算失败: BTCUSDT\n", "record": {"elapsed": {"repr": "1:16:43.565583", "seconds": 4603.565583}, "exception": null, "extra": {"component": "UnifiedMLStrategy"}, "file": {"name": "unified_ml.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\strategy\\unified_ml.py"}, "function": "on_market_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 118, "message": "技术指标计算失败: BTCUSDT", "module": "unified_ml", "name": "strategy.unified_ml", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 55304, "name": "kafka-consumer-kline.data"}, "time": {"repr": "2025-06-21 04:29:56.635258+08:00", "timestamp": 1750451396.635258}}}
{"text": "2025-06-21 04:29:56.636 | WARNING | strategy.unified_ml:on_market_data:118 | 技术指标计算失败: BTCUSDT\n", "record": {"elapsed": {"repr": "1:16:43.566769", "seconds": 4603.566769}, "exception": null, "extra": {"component": "UnifiedMLStrategy"}, "file": {"name": "unified_ml.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\strategy\\unified_ml.py"}, "function": "on_market_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 118, "message": "技术指标计算失败: BTCUSDT", "module": "unified_ml", "name": "strategy.unified_ml", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 55304, "name": "kafka-consumer-kline.data"}, "time": {"repr": "2025-06-21 04:29:56.636444+08:00", "timestamp": 1750451396.636444}}}
{"text": "2025-06-21 04:29:56.637 | WARNING | strategy.unified_ml:on_market_data:118 | 技术指标计算失败: BTCUSDT\n", "record": {"elapsed": {"repr": "1:16:43.567775", "seconds": 4603.567775}, "exception": null, "extra": {"component": "UnifiedMLStrategy"}, "file": {"name": "unified_ml.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\strategy\\unified_ml.py"}, "function": "on_market_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 118, "message": "技术指标计算失败: BTCUSDT", "module": "unified_ml", "name": "strategy.unified_ml", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 55304, "name": "kafka-consumer-kline.data"}, "time": {"repr": "2025-06-21 04:29:56.637450+08:00", "timestamp": 1750451396.63745}}}
{"text": "2025-06-21 04:29:56.637 | WARNING | strategy.unified_ml:on_market_data:118 | 技术指标计算失败: BTCUSDT\n", "record": {"elapsed": {"repr": "1:16:43.567775", "seconds": 4603.567775}, "exception": null, "extra": {"component": "UnifiedMLStrategy"}, "file": {"name": "unified_ml.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\strategy\\unified_ml.py"}, "function": "on_market_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 118, "message": "技术指标计算失败: BTCUSDT", "module": "unified_ml", "name": "strategy.unified_ml", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 55304, "name": "kafka-consumer-kline.data"}, "time": {"repr": "2025-06-21 04:29:56.637450+08:00", "timestamp": 1750451396.63745}}}
{"text": "2025-06-21 04:29:56.638 | WARNING | strategy.unified_ml:on_market_data:118 | 技术指标计算失败: BTCUSDT\n", "record": {"elapsed": {"repr": "1:16:43.568776", "seconds": 4603.568776}, "exception": null, "extra": {"component": "UnifiedMLStrategy"}, "file": {"name": "unified_ml.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\strategy\\unified_ml.py"}, "function": "on_market_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 118, "message": "技术指标计算失败: BTCUSDT", "module": "unified_ml", "name": "strategy.unified_ml", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 55304, "name": "kafka-consumer-kline.data"}, "time": {"repr": "2025-06-21 04:29:56.638451+08:00", "timestamp": 1750451396.638451}}}
{"text": "2025-06-21 04:29:56.638 | WARNING | strategy.unified_ml:on_market_data:118 | 技术指标计算失败: BTCUSDT\n", "record": {"elapsed": {"repr": "1:16:43.568776", "seconds": 4603.568776}, "exception": null, "extra": {"component": "UnifiedMLStrategy"}, "file": {"name": "unified_ml.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\strategy\\unified_ml.py"}, "function": "on_market_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 118, "message": "技术指标计算失败: BTCUSDT", "module": "unified_ml", "name": "strategy.unified_ml", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 55304, "name": "kafka-consumer-kline.data"}, "time": {"repr": "2025-06-21 04:29:56.638451+08:00", "timestamp": 1750451396.638451}}}
{"text": "2025-06-21 04:29:56.639 | WARNING | strategy.unified_ml:on_market_data:118 | 技术指标计算失败: BTCUSDT\n", "record": {"elapsed": {"repr": "1:16:43.569806", "seconds": 4603.569806}, "exception": null, "extra": {"component": "UnifiedMLStrategy"}, "file": {"name": "unified_ml.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\strategy\\unified_ml.py"}, "function": "on_market_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 118, "message": "技术指标计算失败: BTCUSDT", "module": "unified_ml", "name": "strategy.unified_ml", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 55304, "name": "kafka-consumer-kline.data"}, "time": {"repr": "2025-06-21 04:29:56.639481+08:00", "timestamp": 1750451396.639481}}}
{"text": "2025-06-21 04:29:56.639 | WARNING | strategy.unified_ml:on_market_data:118 | 技术指标计算失败: BTCUSDT\n", "record": {"elapsed": {"repr": "1:16:43.569806", "seconds": 4603.569806}, "exception": null, "extra": {"component": "UnifiedMLStrategy"}, "file": {"name": "unified_ml.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\strategy\\unified_ml.py"}, "function": "on_market_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 118, "message": "技术指标计算失败: BTCUSDT", "module": "unified_ml", "name": "strategy.unified_ml", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 55304, "name": "kafka-consumer-kline.data"}, "time": {"repr": "2025-06-21 04:29:56.639481+08:00", "timestamp": 1750451396.639481}}}
{"text": "2025-06-21 04:29:56.640 | WARNING | strategy.unified_ml:on_market_data:118 | 技术指标计算失败: BTCUSDT\n", "record": {"elapsed": {"repr": "1:16:43.570806", "seconds": 4603.570806}, "exception": null, "extra": {"component": "UnifiedMLStrategy"}, "file": {"name": "unified_ml.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\strategy\\unified_ml.py"}, "function": "on_market_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 118, "message": "技术指标计算失败: BTCUSDT", "module": "unified_ml", "name": "strategy.unified_ml", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 55304, "name": "kafka-consumer-kline.data"}, "time": {"repr": "2025-06-21 04:29:56.640481+08:00", "timestamp": 1750451396.640481}}}
{"text": "2025-06-21 04:29:56.640 | WARNING | strategy.unified_ml:on_market_data:118 | 技术指标计算失败: BTCUSDT\n", "record": {"elapsed": {"repr": "1:16:43.570806", "seconds": 4603.570806}, "exception": null, "extra": {"component": "UnifiedMLStrategy"}, "file": {"name": "unified_ml.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\strategy\\unified_ml.py"}, "function": "on_market_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 118, "message": "技术指标计算失败: BTCUSDT", "module": "unified_ml", "name": "strategy.unified_ml", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 55304, "name": "kafka-consumer-kline.data"}, "time": {"repr": "2025-06-21 04:29:56.640481+08:00", "timestamp": 1750451396.640481}}}
{"text": "2025-06-21 04:29:56.641 | WARNING | strategy.unified_ml:on_market_data:118 | 技术指标计算失败: BTCUSDT\n", "record": {"elapsed": {"repr": "1:16:43.571809", "seconds": 4603.571809}, "exception": null, "extra": {"component": "UnifiedMLStrategy"}, "file": {"name": "unified_ml.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\strategy\\unified_ml.py"}, "function": "on_market_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 118, "message": "技术指标计算失败: BTCUSDT", "module": "unified_ml", "name": "strategy.unified_ml", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 55304, "name": "kafka-consumer-kline.data"}, "time": {"repr": "2025-06-21 04:29:56.641484+08:00", "timestamp": 1750451396.641484}}}
{"text": "2025-06-21 04:29:56.642 | WARNING | strategy.unified_ml:on_market_data:118 | 技术指标计算失败: BTCUSDT\n", "record": {"elapsed": {"repr": "1:16:43.572780", "seconds": 4603.57278}, "exception": null, "extra": {"component": "UnifiedMLStrategy"}, "file": {"name": "unified_ml.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\strategy\\unified_ml.py"}, "function": "on_market_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 118, "message": "技术指标计算失败: BTCUSDT", "module": "unified_ml", "name": "strategy.unified_ml", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 55304, "name": "kafka-consumer-kline.data"}, "time": {"repr": "2025-06-21 04:29:56.642455+08:00", "timestamp": 1750451396.642455}}}
{"text": "2025-06-21 04:29:56.643 | WARNING | strategy.unified_ml:on_market_data:118 | 技术指标计算失败: BTCUSDT\n", "record": {"elapsed": {"repr": "1:16:43.573793", "seconds": 4603.573793}, "exception": null, "extra": {"component": "UnifiedMLStrategy"}, "file": {"name": "unified_ml.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\strategy\\unified_ml.py"}, "function": "on_market_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 118, "message": "技术指标计算失败: BTCUSDT", "module": "unified_ml", "name": "strategy.unified_ml", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 55304, "name": "kafka-consumer-kline.data"}, "time": {"repr": "2025-06-21 04:29:56.643468+08:00", "timestamp": 1750451396.643468}}}
{"text": "2025-06-21 04:29:56.644 | WARNING | strategy.unified_ml:on_market_data:118 | 技术指标计算失败: BTCUSDT\n", "record": {"elapsed": {"repr": "1:16:43.575302", "seconds": 4603.575302}, "exception": null, "extra": {"component": "UnifiedMLStrategy"}, "file": {"name": "unified_ml.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\strategy\\unified_ml.py"}, "function": "on_market_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 118, "message": "技术指标计算失败: BTCUSDT", "module": "unified_ml", "name": "strategy.unified_ml", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 55304, "name": "kafka-consumer-kline.data"}, "time": {"repr": "2025-06-21 04:29:56.644977+08:00", "timestamp": 1750451396.644977}}}
{"text": "2025-06-21 04:29:56.645 | WARNING | strategy.unified_ml:on_market_data:118 | 技术指标计算失败: BTCUSDT\n", "record": {"elapsed": {"repr": "1:16:43.576308", "seconds": 4603.576308}, "exception": null, "extra": {"component": "UnifiedMLStrategy"}, "file": {"name": "unified_ml.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\strategy\\unified_ml.py"}, "function": "on_market_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 118, "message": "技术指标计算失败: BTCUSDT", "module": "unified_ml", "name": "strategy.unified_ml", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 55304, "name": "kafka-consumer-kline.data"}, "time": {"repr": "2025-06-21 04:29:56.645983+08:00", "timestamp": 1750451396.645983}}}
{"text": "2025-06-21 04:29:56.645 | WARNING | strategy.unified_ml:on_market_data:118 | 技术指标计算失败: BTCUSDT\n", "record": {"elapsed": {"repr": "1:16:43.576308", "seconds": 4603.576308}, "exception": null, "extra": {"component": "UnifiedMLStrategy"}, "file": {"name": "unified_ml.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\strategy\\unified_ml.py"}, "function": "on_market_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 118, "message": "技术指标计算失败: BTCUSDT", "module": "unified_ml", "name": "strategy.unified_ml", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 55304, "name": "kafka-consumer-kline.data"}, "time": {"repr": "2025-06-21 04:29:56.645983+08:00", "timestamp": 1750451396.645983}}}
{"text": "2025-06-21 04:29:56.646 | WARNING | strategy.unified_ml:on_market_data:118 | 技术指标计算失败: BTCUSDT\n", "record": {"elapsed": {"repr": "1:16:43.577310", "seconds": 4603.57731}, "exception": null, "extra": {"component": "UnifiedMLStrategy"}, "file": {"name": "unified_ml.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\strategy\\unified_ml.py"}, "function": "on_market_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 118, "message": "技术指标计算失败: BTCUSDT", "module": "unified_ml", "name": "strategy.unified_ml", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 55304, "name": "kafka-consumer-kline.data"}, "time": {"repr": "2025-06-21 04:29:56.646985+08:00", "timestamp": 1750451396.646985}}}
{"text": "2025-06-21 04:29:56.646 | WARNING | strategy.unified_ml:on_market_data:118 | 技术指标计算失败: BTCUSDT\n", "record": {"elapsed": {"repr": "1:16:43.577310", "seconds": 4603.57731}, "exception": null, "extra": {"component": "UnifiedMLStrategy"}, "file": {"name": "unified_ml.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\strategy\\unified_ml.py"}, "function": "on_market_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 118, "message": "技术指标计算失败: BTCUSDT", "module": "unified_ml", "name": "strategy.unified_ml", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 55304, "name": "kafka-consumer-kline.data"}, "time": {"repr": "2025-06-21 04:29:56.646985+08:00", "timestamp": 1750451396.646985}}}
{"text": "2025-06-21 04:29:56.647 | WARNING | strategy.unified_ml:on_market_data:118 | 技术指标计算失败: BTCUSDT\n", "record": {"elapsed": {"repr": "1:16:43.578308", "seconds": 4603.578308}, "exception": null, "extra": {"component": "UnifiedMLStrategy"}, "file": {"name": "unified_ml.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\strategy\\unified_ml.py"}, "function": "on_market_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 118, "message": "技术指标计算失败: BTCUSDT", "module": "unified_ml", "name": "strategy.unified_ml", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 55304, "name": "kafka-consumer-kline.data"}, "time": {"repr": "2025-06-21 04:29:56.647983+08:00", "timestamp": 1750451396.647983}}}
{"text": "2025-06-21 04:29:56.647 | WARNING | strategy.unified_ml:on_market_data:118 | 技术指标计算失败: BTCUSDT\n", "record": {"elapsed": {"repr": "1:16:43.578308", "seconds": 4603.578308}, "exception": null, "extra": {"component": "UnifiedMLStrategy"}, "file": {"name": "unified_ml.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\strategy\\unified_ml.py"}, "function": "on_market_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 118, "message": "技术指标计算失败: BTCUSDT", "module": "unified_ml", "name": "strategy.unified_ml", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 55304, "name": "kafka-consumer-kline.data"}, "time": {"repr": "2025-06-21 04:29:56.647983+08:00", "timestamp": 1750451396.647983}}}
{"text": "2025-06-21 04:29:56.649 | WARNING | strategy.unified_ml:on_market_data:118 | 技术指标计算失败: BTCUSDT\n", "record": {"elapsed": {"repr": "1:16:43.579339", "seconds": 4603.579339}, "exception": null, "extra": {"component": "UnifiedMLStrategy"}, "file": {"name": "unified_ml.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\strategy\\unified_ml.py"}, "function": "on_market_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 118, "message": "技术指标计算失败: BTCUSDT", "module": "unified_ml", "name": "strategy.unified_ml", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 55304, "name": "kafka-consumer-kline.data"}, "time": {"repr": "2025-06-21 04:29:56.649014+08:00", "timestamp": 1750451396.649014}}}
{"text": "2025-06-21 04:29:56.649 | WARNING | strategy.unified_ml:on_market_data:118 | 技术指标计算失败: BTCUSDT\n", "record": {"elapsed": {"repr": "1:16:43.579339", "seconds": 4603.579339}, "exception": null, "extra": {"component": "UnifiedMLStrategy"}, "file": {"name": "unified_ml.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\strategy\\unified_ml.py"}, "function": "on_market_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 118, "message": "技术指标计算失败: BTCUSDT", "module": "unified_ml", "name": "strategy.unified_ml", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 55304, "name": "kafka-consumer-kline.data"}, "time": {"repr": "2025-06-21 04:29:56.649014+08:00", "timestamp": 1750451396.649014}}}
{"text": "2025-06-21 04:29:56.649 | WARNING | strategy.unified_ml:on_market_data:118 | 技术指标计算失败: BTCUSDT\n", "record": {"elapsed": {"repr": "1:16:43.580309", "seconds": 4603.580309}, "exception": null, "extra": {"component": "UnifiedMLStrategy"}, "file": {"name": "unified_ml.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\strategy\\unified_ml.py"}, "function": "on_market_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 118, "message": "技术指标计算失败: BTCUSDT", "module": "unified_ml", "name": "strategy.unified_ml", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 55304, "name": "kafka-consumer-kline.data"}, "time": {"repr": "2025-06-21 04:29:56.649984+08:00", "timestamp": 1750451396.649984}}}
{"text": "2025-06-21 04:29:56.650 | WARNING | strategy.unified_ml:on_market_data:118 | 技术指标计算失败: BTCUSDT\n", "record": {"elapsed": {"repr": "1:16:43.581313", "seconds": 4603.581313}, "exception": null, "extra": {"component": "UnifiedMLStrategy"}, "file": {"name": "unified_ml.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\strategy\\unified_ml.py"}, "function": "on_market_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 118, "message": "技术指标计算失败: BTCUSDT", "module": "unified_ml", "name": "strategy.unified_ml", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 55304, "name": "kafka-consumer-kline.data"}, "time": {"repr": "2025-06-21 04:29:56.650988+08:00", "timestamp": 1750451396.650988}}}
{"text": "2025-06-21 04:29:56.652 | WARNING | strategy.unified_ml:on_market_data:118 | 技术指标计算失败: BTCUSDT\n", "record": {"elapsed": {"repr": "1:16:43.583312", "seconds": 4603.583312}, "exception": null, "extra": {"component": "UnifiedMLStrategy"}, "file": {"name": "unified_ml.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\strategy\\unified_ml.py"}, "function": "on_market_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 118, "message": "技术指标计算失败: BTCUSDT", "module": "unified_ml", "name": "strategy.unified_ml", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 55304, "name": "kafka-consumer-kline.data"}, "time": {"repr": "2025-06-21 04:29:56.652987+08:00", "timestamp": 1750451396.652987}}}
{"text": "2025-06-21 04:29:56.652 | WARNING | strategy.unified_ml:on_market_data:118 | 技术指标计算失败: BTCUSDT\n", "record": {"elapsed": {"repr": "1:16:43.583312", "seconds": 4603.583312}, "exception": null, "extra": {"component": "UnifiedMLStrategy"}, "file": {"name": "unified_ml.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\strategy\\unified_ml.py"}, "function": "on_market_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 118, "message": "技术指标计算失败: BTCUSDT", "module": "unified_ml", "name": "strategy.unified_ml", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 55304, "name": "kafka-consumer-kline.data"}, "time": {"repr": "2025-06-21 04:29:56.652987+08:00", "timestamp": 1750451396.652987}}}
{"text": "2025-06-21 04:29:56.654 | WARNING | strategy.unified_ml:on_market_data:118 | 技术指标计算失败: BTCUSDT\n", "record": {"elapsed": {"repr": "1:16:43.584341", "seconds": 4603.584341}, "exception": null, "extra": {"component": "UnifiedMLStrategy"}, "file": {"name": "unified_ml.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\strategy\\unified_ml.py"}, "function": "on_market_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 118, "message": "技术指标计算失败: BTCUSDT", "module": "unified_ml", "name": "strategy.unified_ml", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 55304, "name": "kafka-consumer-kline.data"}, "time": {"repr": "2025-06-21 04:29:56.654016+08:00", "timestamp": 1750451396.654016}}}
{"text": "2025-06-21 04:29:56.655 | WARNING | strategy.unified_ml:on_market_data:118 | 技术指标计算失败: BTCUSDT\n", "record": {"elapsed": {"repr": "1:16:43.585341", "seconds": 4603.585341}, "exception": null, "extra": {"component": "UnifiedMLStrategy"}, "file": {"name": "unified_ml.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\strategy\\unified_ml.py"}, "function": "on_market_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 118, "message": "技术指标计算失败: BTCUSDT", "module": "unified_ml", "name": "strategy.unified_ml", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 55304, "name": "kafka-consumer-kline.data"}, "time": {"repr": "2025-06-21 04:29:56.655016+08:00", "timestamp": 1750451396.655016}}}
{"text": "2025-06-21 04:29:56.655 | WARNING | strategy.unified_ml:on_market_data:118 | 技术指标计算失败: BTCUSDT\n", "record": {"elapsed": {"repr": "1:16:43.585341", "seconds": 4603.585341}, "exception": null, "extra": {"component": "UnifiedMLStrategy"}, "file": {"name": "unified_ml.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\strategy\\unified_ml.py"}, "function": "on_market_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 118, "message": "技术指标计算失败: BTCUSDT", "module": "unified_ml", "name": "strategy.unified_ml", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 55304, "name": "kafka-consumer-kline.data"}, "time": {"repr": "2025-06-21 04:29:56.655016+08:00", "timestamp": 1750451396.655016}}}
{"text": "2025-06-21 04:29:56.655 | WARNING | strategy.unified_ml:on_market_data:118 | 技术指标计算失败: BTCUSDT\n", "record": {"elapsed": {"repr": "1:16:43.585341", "seconds": 4603.585341}, "exception": null, "extra": {"component": "UnifiedMLStrategy"}, "file": {"name": "unified_ml.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\strategy\\unified_ml.py"}, "function": "on_market_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 118, "message": "技术指标计算失败: BTCUSDT", "module": "unified_ml", "name": "strategy.unified_ml", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 55304, "name": "kafka-consumer-kline.data"}, "time": {"repr": "2025-06-21 04:29:56.655016+08:00", "timestamp": 1750451396.655016}}}
{"text": "2025-06-21 04:29:56.656 | WARNING | strategy.unified_ml:on_market_data:118 | 技术指标计算失败: BTCUSDT\n", "record": {"elapsed": {"repr": "1:16:43.586580", "seconds": 4603.58658}, "exception": null, "extra": {"component": "UnifiedMLStrategy"}, "file": {"name": "unified_ml.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\strategy\\unified_ml.py"}, "function": "on_market_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 118, "message": "技术指标计算失败: BTCUSDT", "module": "unified_ml", "name": "strategy.unified_ml", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 55304, "name": "kafka-consumer-kline.data"}, "time": {"repr": "2025-06-21 04:29:56.656255+08:00", "timestamp": 1750451396.656255}}}
{"text": "2025-06-21 04:29:56.656 | WARNING | strategy.unified_ml:on_market_data:118 | 技术指标计算失败: BTCUSDT\n", "record": {"elapsed": {"repr": "1:16:43.586580", "seconds": 4603.58658}, "exception": null, "extra": {"component": "UnifiedMLStrategy"}, "file": {"name": "unified_ml.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\strategy\\unified_ml.py"}, "function": "on_market_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 118, "message": "技术指标计算失败: BTCUSDT", "module": "unified_ml", "name": "strategy.unified_ml", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 55304, "name": "kafka-consumer-kline.data"}, "time": {"repr": "2025-06-21 04:29:56.656255+08:00", "timestamp": 1750451396.656255}}}
{"text": "2025-06-21 04:29:56.657 | WARNING | strategy.unified_ml:on_market_data:118 | 技术指标计算失败: BTCUSDT\n", "record": {"elapsed": {"repr": "1:16:43.587577", "seconds": 4603.587577}, "exception": null, "extra": {"component": "UnifiedMLStrategy"}, "file": {"name": "unified_ml.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\strategy\\unified_ml.py"}, "function": "on_market_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 118, "message": "技术指标计算失败: BTCUSDT", "module": "unified_ml", "name": "strategy.unified_ml", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 55304, "name": "kafka-consumer-kline.data"}, "time": {"repr": "2025-06-21 04:29:56.657252+08:00", "timestamp": 1750451396.657252}}}
{"text": "2025-06-21 04:29:56.657 | WARNING | strategy.unified_ml:on_market_data:118 | 技术指标计算失败: BTCUSDT\n", "record": {"elapsed": {"repr": "1:16:43.587577", "seconds": 4603.587577}, "exception": null, "extra": {"component": "UnifiedMLStrategy"}, "file": {"name": "unified_ml.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\strategy\\unified_ml.py"}, "function": "on_market_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 118, "message": "技术指标计算失败: BTCUSDT", "module": "unified_ml", "name": "strategy.unified_ml", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 55304, "name": "kafka-consumer-kline.data"}, "time": {"repr": "2025-06-21 04:29:56.657252+08:00", "timestamp": 1750451396.657252}}}
{"text": "2025-06-21 04:29:56.658 | WARNING | strategy.unified_ml:on_market_data:118 | 技术指标计算失败: BTCUSDT\n", "record": {"elapsed": {"repr": "1:16:43.588546", "seconds": 4603.588546}, "exception": null, "extra": {"component": "UnifiedMLStrategy"}, "file": {"name": "unified_ml.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\strategy\\unified_ml.py"}, "function": "on_market_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 118, "message": "技术指标计算失败: BTCUSDT", "module": "unified_ml", "name": "strategy.unified_ml", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 55304, "name": "kafka-consumer-kline.data"}, "time": {"repr": "2025-06-21 04:29:56.658221+08:00", "timestamp": 1750451396.658221}}}
{"text": "2025-06-21 04:29:56.659 | WARNING | strategy.unified_ml:on_market_data:118 | 技术指标计算失败: BTCUSDT\n", "record": {"elapsed": {"repr": "1:16:43.589548", "seconds": 4603.589548}, "exception": null, "extra": {"component": "UnifiedMLStrategy"}, "file": {"name": "unified_ml.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\strategy\\unified_ml.py"}, "function": "on_market_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 118, "message": "技术指标计算失败: BTCUSDT", "module": "unified_ml", "name": "strategy.unified_ml", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 55304, "name": "kafka-consumer-kline.data"}, "time": {"repr": "2025-06-21 04:29:56.659223+08:00", "timestamp": 1750451396.659223}}}
{"text": "2025-06-21 04:29:56.661 | WARNING | strategy.unified_ml:on_market_data:118 | 技术指标计算失败: BTCUSDT\n", "record": {"elapsed": {"repr": "1:16:43.591547", "seconds": 4603.591547}, "exception": null, "extra": {"component": "UnifiedMLStrategy"}, "file": {"name": "unified_ml.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\strategy\\unified_ml.py"}, "function": "on_market_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 118, "message": "技术指标计算失败: BTCUSDT", "module": "unified_ml", "name": "strategy.unified_ml", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 55304, "name": "kafka-consumer-kline.data"}, "time": {"repr": "2025-06-21 04:29:56.661222+08:00", "timestamp": 1750451396.661222}}}
{"text": "2025-06-21 04:29:56.661 | WARNING | strategy.unified_ml:on_market_data:118 | 技术指标计算失败: BTCUSDT\n", "record": {"elapsed": {"repr": "1:16:43.591547", "seconds": 4603.591547}, "exception": null, "extra": {"component": "UnifiedMLStrategy"}, "file": {"name": "unified_ml.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\strategy\\unified_ml.py"}, "function": "on_market_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 118, "message": "技术指标计算失败: BTCUSDT", "module": "unified_ml", "name": "strategy.unified_ml", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 55304, "name": "kafka-consumer-kline.data"}, "time": {"repr": "2025-06-21 04:29:56.661222+08:00", "timestamp": 1750451396.661222}}}
{"text": "2025-06-21 04:29:56.662 | WARNING | strategy.unified_ml:on_market_data:118 | 技术指标计算失败: BTCUSDT\n", "record": {"elapsed": {"repr": "1:16:43.592577", "seconds": 4603.592577}, "exception": null, "extra": {"component": "UnifiedMLStrategy"}, "file": {"name": "unified_ml.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\strategy\\unified_ml.py"}, "function": "on_market_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 118, "message": "技术指标计算失败: BTCUSDT", "module": "unified_ml", "name": "strategy.unified_ml", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 55304, "name": "kafka-consumer-kline.data"}, "time": {"repr": "2025-06-21 04:29:56.662252+08:00", "timestamp": 1750451396.662252}}}
{"text": "2025-06-21 04:29:56.662 | WARNING | strategy.unified_ml:on_market_data:118 | 技术指标计算失败: BTCUSDT\n", "record": {"elapsed": {"repr": "1:16:43.592577", "seconds": 4603.592577}, "exception": null, "extra": {"component": "UnifiedMLStrategy"}, "file": {"name": "unified_ml.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\strategy\\unified_ml.py"}, "function": "on_market_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 118, "message": "技术指标计算失败: BTCUSDT", "module": "unified_ml", "name": "strategy.unified_ml", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 55304, "name": "kafka-consumer-kline.data"}, "time": {"repr": "2025-06-21 04:29:56.662252+08:00", "timestamp": 1750451396.662252}}}
{"text": "2025-06-21 04:29:56.663 | WARNING | strategy.unified_ml:on_market_data:118 | 技术指标计算失败: BTCUSDT\n", "record": {"elapsed": {"repr": "1:16:43.593548", "seconds": 4603.593548}, "exception": null, "extra": {"component": "UnifiedMLStrategy"}, "file": {"name": "unified_ml.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\strategy\\unified_ml.py"}, "function": "on_market_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 118, "message": "技术指标计算失败: BTCUSDT", "module": "unified_ml", "name": "strategy.unified_ml", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 55304, "name": "kafka-consumer-kline.data"}, "time": {"repr": "2025-06-21 04:29:56.663223+08:00", "timestamp": 1750451396.663223}}}
{"text": "2025-06-21 04:29:56.663 | WARNING | strategy.unified_ml:on_market_data:118 | 技术指标计算失败: BTCUSDT\n", "record": {"elapsed": {"repr": "1:16:43.593548", "seconds": 4603.593548}, "exception": null, "extra": {"component": "UnifiedMLStrategy"}, "file": {"name": "unified_ml.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\strategy\\unified_ml.py"}, "function": "on_market_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 118, "message": "技术指标计算失败: BTCUSDT", "module": "unified_ml", "name": "strategy.unified_ml", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 55304, "name": "kafka-consumer-kline.data"}, "time": {"repr": "2025-06-21 04:29:56.663223+08:00", "timestamp": 1750451396.663223}}}
{"text": "2025-06-21 04:29:56.663 | WARNING | strategy.unified_ml:on_market_data:118 | 技术指标计算失败: BTCUSDT\n", "record": {"elapsed": {"repr": "1:16:43.593548", "seconds": 4603.593548}, "exception": null, "extra": {"component": "UnifiedMLStrategy"}, "file": {"name": "unified_ml.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\strategy\\unified_ml.py"}, "function": "on_market_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 118, "message": "技术指标计算失败: BTCUSDT", "module": "unified_ml", "name": "strategy.unified_ml", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 55304, "name": "kafka-consumer-kline.data"}, "time": {"repr": "2025-06-21 04:29:56.663223+08:00", "timestamp": 1750451396.663223}}}
{"text": "2025-06-21 04:29:56.664 | WARNING | strategy.unified_ml:on_market_data:118 | 技术指标计算失败: BTCUSDT\n", "record": {"elapsed": {"repr": "1:16:43.595142", "seconds": 4603.595142}, "exception": null, "extra": {"component": "UnifiedMLStrategy"}, "file": {"name": "unified_ml.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\strategy\\unified_ml.py"}, "function": "on_market_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 118, "message": "技术指标计算失败: BTCUSDT", "module": "unified_ml", "name": "strategy.unified_ml", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 55304, "name": "kafka-consumer-kline.data"}, "time": {"repr": "2025-06-21 04:29:56.664817+08:00", "timestamp": 1750451396.664817}}}
{"text": "2025-06-21 04:29:56.664 | WARNING | strategy.unified_ml:on_market_data:118 | 技术指标计算失败: BTCUSDT\n", "record": {"elapsed": {"repr": "1:16:43.595142", "seconds": 4603.595142}, "exception": null, "extra": {"component": "UnifiedMLStrategy"}, "file": {"name": "unified_ml.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\strategy\\unified_ml.py"}, "function": "on_market_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 118, "message": "技术指标计算失败: BTCUSDT", "module": "unified_ml", "name": "strategy.unified_ml", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 55304, "name": "kafka-consumer-kline.data"}, "time": {"repr": "2025-06-21 04:29:56.664817+08:00", "timestamp": 1750451396.664817}}}
{"text": "2025-06-21 04:29:56.665 | WARNING | strategy.unified_ml:on_market_data:118 | 技术指标计算失败: BTCUSDT\n", "record": {"elapsed": {"repr": "1:16:43.596113", "seconds": 4603.596113}, "exception": null, "extra": {"component": "UnifiedMLStrategy"}, "file": {"name": "unified_ml.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\strategy\\unified_ml.py"}, "function": "on_market_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 118, "message": "技术指标计算失败: BTCUSDT", "module": "unified_ml", "name": "strategy.unified_ml", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 55304, "name": "kafka-consumer-kline.data"}, "time": {"repr": "2025-06-21 04:29:56.665788+08:00", "timestamp": 1750451396.665788}}}
{"text": "2025-06-21 04:29:56.666 | WARNING | strategy.unified_ml:on_market_data:118 | 技术指标计算失败: BTCUSDT\n", "record": {"elapsed": {"repr": "1:16:43.597093", "seconds": 4603.597093}, "exception": null, "extra": {"component": "UnifiedMLStrategy"}, "file": {"name": "unified_ml.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\strategy\\unified_ml.py"}, "function": "on_market_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 118, "message": "技术指标计算失败: BTCUSDT", "module": "unified_ml", "name": "strategy.unified_ml", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 55304, "name": "kafka-consumer-kline.data"}, "time": {"repr": "2025-06-21 04:29:56.666768+08:00", "timestamp": 1750451396.666768}}}
{"text": "2025-06-21 04:29:56.667 | WARNING | strategy.unified_ml:on_market_data:118 | 技术指标计算失败: BTCUSDT\n", "record": {"elapsed": {"repr": "1:16:43.598093", "seconds": 4603.598093}, "exception": null, "extra": {"component": "UnifiedMLStrategy"}, "file": {"name": "unified_ml.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\strategy\\unified_ml.py"}, "function": "on_market_data", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 118, "message": "技术指标计算失败: BTCUSDT", "module": "unified_ml", "name": "strategy.unified_ml", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 55304, "name": "kafka-consumer-kline.data"}, "time": {"repr": "2025-06-21 04:29:56.667768+08:00", "timestamp": 1750451396.667768}}}
{"text": "2025-06-21 14:01:01.908 | INFO | __main__:handle_signal:197 | 接收到信号 2，准备关闭应用...\n", "record": {"elapsed": {"repr": "10:47:48.838572", "seconds": 38868.838572}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\main.py"}, "function": "handle_signal", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 197, "message": "接收到信号 2，准备关闭应用...", "module": "main", "name": "__main__", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 17192, "name": "MainThread"}, "time": {"repr": "2025-06-21 14:01:01.908247+08:00", "timestamp": 1750485661.908247}}}
{"text": "2025-06-21 14:01:02.045 | INFO | __main__:stop:202 | 关闭交易策略应用\n", "record": {"elapsed": {"repr": "10:47:48.975709", "seconds": 38868.975709}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\main.py"}, "function": "stop", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 202, "message": "关闭交易策略应用", "module": "main", "name": "__main__", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 17192, "name": "MainThread"}, "time": {"repr": "2025-06-21 14:01:02.045384+08:00", "timestamp": 1750485662.045384}}}
{"text": "2025-06-21 14:01:02.103 | INFO | data.clients.mysql_client:close:499 | MySQL连接已关闭\n", "record": {"elapsed": {"repr": "10:47:49.033900", "seconds": 38869.0339}, "exception": null, "extra": {"component": "MySQLClient"}, "file": {"name": "mysql_client.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\clients\\mysql_client.py"}, "function": "close", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 499, "message": "MySQL连接已关闭", "module": "mysql_client", "name": "data.clients.mysql_client", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 17192, "name": "MainThread"}, "time": {"repr": "2025-06-21 14:01:02.103575+08:00", "timestamp": 1750485662.103575}}}
{"text": "2025-06-21 14:01:02.105 | INFO | data.clients.influxdb_client:close:340 | InfluxDB连接已关闭\n", "record": {"elapsed": {"repr": "10:47:49.035917", "seconds": 38869.035917}, "exception": null, "extra": {"component": "InfluxDBClient"}, "file": {"name": "influxdb_client.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\clients\\influxdb_client.py"}, "function": "close", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 340, "message": "InfluxDB连接已关闭", "module": "influxdb_client", "name": "data.clients.influxdb_client", "process": {"id": 68368, "name": "MainProcess"}, "thread": {"id": 17192, "name": "MainThread"}, "time": {"repr": "2025-06-21 14:01:02.105592+08:00", "timestamp": 1750485662.105592}}}
{"text": "2025-06-21 22:00:32.392 | INFO | infrastructure.logging.logging_core_manager:initialize:261 | Logging system initialized successfully\n", "record": {"elapsed": {"repr": "0:01:12.560974", "seconds": 72.560974}, "exception": null, "extra": {}, "file": {"name": "logging_core_manager.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\logging\\logging_core_manager.py"}, "function": "initialize", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 261, "message": "Logging system initialized successfully", "module": "logging_core_manager", "name": "infrastructure.logging.logging_core_manager", "process": {"id": 33200, "name": "MainProcess"}, "thread": {"id": 12304, "name": "MainThread"}, "time": {"repr": "2025-06-21 22:00:32.392840+08:00", "timestamp": **********.39284}}}
{"text": "2025-06-21 22:00:32.749 | INFO | infrastructure.system_health_monitor:register_health_check:434 | 注册健康检查: system_resources\n", "record": {"elapsed": {"repr": "0:01:12.917274", "seconds": 72.917274}, "exception": null, "extra": {}, "file": {"name": "system_health_monitor.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\system_health_monitor.py"}, "function": "register_health_check", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 434, "message": "注册健康检查: system_resources", "module": "system_health_monitor", "name": "infrastructure.system_health_monitor", "process": {"id": 33200, "name": "MainProcess"}, "thread": {"id": 12304, "name": "MainThread"}, "time": {"repr": "2025-06-21 22:00:32.749140+08:00", "timestamp": **********.74914}}}
{"text": "2025-06-21 22:00:32.812 | INFO | infrastructure.error_recovery:register_strategy:422 | 注册恢复策略: CacheRecoveryStrategy\n", "record": {"elapsed": {"repr": "0:01:12.980475", "seconds": 72.980475}, "exception": null, "extra": {}, "file": {"name": "error_recovery.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\error_recovery.py"}, "function": "register_strategy", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 422, "message": "注册恢复策略: CacheRecoveryStrategy", "module": "error_recovery", "name": "infrastructure.error_recovery", "process": {"id": 33200, "name": "MainProcess"}, "thread": {"id": 12304, "name": "MainThread"}, "time": {"repr": "2025-06-21 22:00:32.812341+08:00", "timestamp": **********.812341}}}
{"text": "2025-06-21 22:00:32.812 | INFO | infrastructure.error_recovery:register_strategy:422 | 注册恢复策略: RetryRecoveryStrategy\n", "record": {"elapsed": {"repr": "0:01:12.980475", "seconds": 72.980475}, "exception": null, "extra": {}, "file": {"name": "error_recovery.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\error_recovery.py"}, "function": "register_strategy", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 422, "message": "注册恢复策略: RetryRecoveryStrategy", "module": "error_recovery", "name": "infrastructure.error_recovery", "process": {"id": 33200, "name": "MainProcess"}, "thread": {"id": 12304, "name": "MainThread"}, "time": {"repr": "2025-06-21 22:00:32.812341+08:00", "timestamp": **********.812341}}}
{"text": "2025-06-21 22:00:32.812 | INFO | infrastructure.error_recovery:register_strategy:422 | 注册恢复策略: FallbackRecoveryStrategy\n", "record": {"elapsed": {"repr": "0:01:12.980475", "seconds": 72.980475}, "exception": null, "extra": {}, "file": {"name": "error_recovery.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\error_recovery.py"}, "function": "register_strategy", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 422, "message": "注册恢复策略: FallbackRecoveryStrategy", "module": "error_recovery", "name": "infrastructure.error_recovery", "process": {"id": 33200, "name": "MainProcess"}, "thread": {"id": 12304, "name": "MainThread"}, "time": {"repr": "2025-06-21 22:00:32.812341+08:00", "timestamp": **********.812341}}}
{"text": "2025-06-21 22:00:32.813 | INFO | infrastructure.error_recovery:register_strategy:422 | 注册恢复策略: ComponentRestartStrategy\n", "record": {"elapsed": {"repr": "0:01:12.981979", "seconds": 72.981979}, "exception": null, "extra": {}, "file": {"name": "error_recovery.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\error_recovery.py"}, "function": "register_strategy", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 422, "message": "注册恢复策略: ComponentRestartStrategy", "module": "error_recovery", "name": "infrastructure.error_recovery", "process": {"id": 33200, "name": "MainProcess"}, "thread": {"id": 12304, "name": "MainThread"}, "time": {"repr": "2025-06-21 22:00:32.813845+08:00", "timestamp": **********.813845}}}
{"text": "2025-06-21 22:00:32.833 | INFO | infrastructure.graceful_degradation:register_strategy:339 | 注册降级策略: performance_degradation\n", "record": {"elapsed": {"repr": "0:01:13.001559", "seconds": 73.001559}, "exception": null, "extra": {}, "file": {"name": "graceful_degradation.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\graceful_degradation.py"}, "function": "register_strategy", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 339, "message": "注册降级策略: performance_degradation", "module": "graceful_degradation", "name": "infrastructure.graceful_degradation", "process": {"id": 33200, "name": "MainProcess"}, "thread": {"id": 12304, "name": "MainThread"}, "time": {"repr": "2025-06-21 22:00:32.833425+08:00", "timestamp": **********.833425}}}
{"text": "2025-06-21 22:00:32.834 | INFO | infrastructure.graceful_degradation:register_strategy:339 | 注册降级策略: load_based_degradation\n", "record": {"elapsed": {"repr": "0:01:13.002763", "seconds": 73.002763}, "exception": null, "extra": {}, "file": {"name": "graceful_degradation.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\graceful_degradation.py"}, "function": "register_strategy", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 339, "message": "注册降级策略: load_based_degradation", "module": "graceful_degradation", "name": "infrastructure.graceful_degradation", "process": {"id": 33200, "name": "MainProcess"}, "thread": {"id": 12304, "name": "MainThread"}, "time": {"repr": "2025-06-21 22:00:32.834629+08:00", "timestamp": **********.834629}}}
{"text": "2025-06-21 22:00:32.892 | INFO | infrastructure.test_error_handling_core:__init__:78 | ErrorHandlingCoreTestRunner initialized\n", "record": {"elapsed": {"repr": "0:01:13.060968", "seconds": 73.060968}, "exception": null, "extra": {}, "file": {"name": "test_error_handling_core.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\test_error_handling_core.py"}, "function": "__init__", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 78, "message": "ErrorHandlingCoreTestRunner initialized", "module": "test_error_handling_core", "name": "infrastructure.test_error_handling_core", "process": {"id": 33200, "name": "MainProcess"}, "thread": {"id": 12304, "name": "MainThread"}, "time": {"repr": "2025-06-21 22:00:32.892834+08:00", "timestamp": **********.892834}}}
{"text": "2025-06-21 22:00:33.053 | INFO | infrastructure.test_error_handling_integration:__init__:79 | ErrorHandlingIntegrationTestRunner initialized\n", "record": {"elapsed": {"repr": "0:01:13.221608", "seconds": 73.221608}, "exception": null, "extra": {}, "file": {"name": "test_error_handling_integration.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\test_error_handling_integration.py"}, "function": "__init__", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 79, "message": "ErrorHandlingIntegrationTestRunner initialized", "module": "test_error_handling_integration", "name": "infrastructure.test_error_handling_integration", "process": {"id": 33200, "name": "MainProcess"}, "thread": {"id": 12304, "name": "MainThread"}, "time": {"repr": "2025-06-21 22:00:33.053474+08:00", "timestamp": 1750514433.053474}}}
{"text": "2025-06-21 22:01:05.235 | INFO | core.config:__init__:43 | 配置已加载: D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\config\\config.ini\n", "record": {"elapsed": {"repr": "0:01:45.403641", "seconds": 105.403641}, "exception": null, "extra": {}, "file": {"name": "config.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\core\\config.py"}, "function": "__init__", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 43, "message": "配置已加载: D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\config\\config.ini", "module": "config", "name": "core.config", "process": {"id": 33200, "name": "MainProcess"}, "thread": {"id": 12304, "name": "MainThread"}, "time": {"repr": "2025-06-21 22:01:05.235507+08:00", "timestamp": 1750514465.235507}}}
{"text": "2025-06-21 22:01:09.368 | ERROR | data.clients.influxdb_client:_connect:103 | InfluxDB连接失败: InfluxDB健康检查失败: <urllib3.connection.HTTPConnection object at 0x000002127C0F8400>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。\n", "record": {"elapsed": {"repr": "0:01:49.536994", "seconds": 109.536994}, "exception": null, "extra": {"component": "InfluxDBClient"}, "file": {"name": "influxdb_client.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\data\\clients\\influxdb_client.py"}, "function": "_connect", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 103, "message": "InfluxDB连接失败: InfluxDB健康检查失败: <urllib3.connection.HTTPConnection object at 0x000002127C0F8400>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。", "module": "influxdb_client", "name": "data.clients.influxdb_client", "process": {"id": 33200, "name": "MainProcess"}, "thread": {"id": 12304, "name": "MainThread"}, "time": {"repr": "2025-06-21 22:01:09.368860+08:00", "timestamp": 1750514469.36886}}}
