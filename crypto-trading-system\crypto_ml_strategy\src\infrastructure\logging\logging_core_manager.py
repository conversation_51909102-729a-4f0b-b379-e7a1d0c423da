"""
Crypto ML Strategy - Loguru日志系统核心管理器

该模块实现了基于loguru的企业级日志管理系统，提供结构化日志记录、
配置管理、日志器工厂和上下文管理功能。

主要功能：
- LoggingManager: 日志系统主管理器
- LoggerFactory: 日志器工厂，创建不同类型的日志器
- LoggingConfig: 日志配置数据结构和验证
- LogLevel: 日志级别枚举和管理
- LoggingContext: 日志上下文管理（请求ID、用户ID、会话ID等）

作者: Crypto ML Strategy Team
日期: 2024-01-20
版本: 1.0.0
"""

import asyncio
import threading
import uuid
from contextlib import contextmanager
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional, Union, Callable, Set
from loguru import logger
import yaml
import json
from datetime import datetime, timedelta


class LogLevel(Enum):
    """日志级别枚举"""
    TRACE = "TRACE"
    DEBUG = "DEBUG"
    INFO = "INFO"
    SUCCESS = "SUCCESS"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


@dataclass
class LoggingConfig:
    """日志配置数据结构"""
    level: LogLevel = LogLevel.INFO
    format_template: str = "{time:YYYY-MM-DD HH:mm:ss.SSS} | {level} | {name}:{function}:{line} | {message}"
    enable_console: bool = True
    enable_file: bool = True
    file_path: str = "logs/app_{time:YYYY-MM-DD}.log"
    rotation: str = "100 MB"
    retention: str = "30 days"
    compression: str = "zip"
    serialize: bool = True
    enqueue: bool = True
    backtrace: bool = True
    diagnose: bool = True
    colorize: bool = True
    
    # 性能配置
    async_enabled: bool = True
    buffer_size: int = 1000
    flush_interval_seconds: int = 5
    max_memory_mb: int = 100
    
    # 安全配置
    sensitive_fields: List[str] = field(default_factory=lambda: ["password", "api_key", "secret", "token"])
    encryption_enabled: bool = False
    access_control_enabled: bool = False
    audit_enabled: bool = True
    
    def validate(self) -> bool:
        """验证配置有效性"""
        try:
            # 验证日志级别
            if not isinstance(self.level, LogLevel):
                return False
            
            # 验证文件路径
            if self.enable_file:
                log_dir = Path(self.file_path).parent
                log_dir.mkdir(parents=True, exist_ok=True)
            
            # 验证数值范围
            if self.buffer_size <= 0 or self.flush_interval_seconds <= 0:
                return False
            
            if self.max_memory_mb <= 0:
                return False
            
            return True
        except Exception:
            return False


class LoggingContext:
    """日志上下文管理器"""
    
    def __init__(self):
        self._context: Dict[str, Any] = {}
        self._lock = threading.RLock()
    
    def set_context(self, key: str, value: Any) -> None:
        """设置上下文值"""
        with self._lock:
            self._context[key] = value
    
    def get_context(self, key: str, default: Any = None) -> Any:
        """获取上下文值"""
        with self._lock:
            return self._context.get(key, default)
    
    def update_context(self, context: Dict[str, Any]) -> None:
        """批量更新上下文"""
        with self._lock:
            self._context.update(context)
    
    def clear_context(self) -> None:
        """清空上下文"""
        with self._lock:
            self._context.clear()
    
    def get_all_context(self) -> Dict[str, Any]:
        """获取所有上下文"""
        with self._lock:
            return self._context.copy()
    
    @contextmanager
    def request_context(self, request_id: Optional[str] = None):
        """请求上下文管理器"""
        if request_id is None:
            request_id = str(uuid.uuid4())
        
        old_request_id = self.get_context("request_id")
        self.set_context("request_id", request_id)
        
        try:
            yield request_id
        finally:
            if old_request_id is not None:
                self.set_context("request_id", old_request_id)
            else:
                self._context.pop("request_id", None)


class LoggerFactory:
    """日志器工厂"""
    
    def __init__(self, config: LoggingConfig, context: LoggingContext):
        self.config = config
        self.context = context
        self._loggers: Dict[str, Any] = {}
        self._lock = threading.RLock()
    
    def create_logger(self, name: str, **kwargs) -> Any:
        """创建日志器"""
        with self._lock:
            if name in self._loggers:
                return self._loggers[name]
            
            # 创建新的日志器
            new_logger = logger.bind(name=name, **kwargs)
            self._loggers[name] = new_logger
            return new_logger
    
    def create_performance_logger(self, name: str) -> Any:
        """创建性能监控日志器"""
        return self.create_logger(
            f"{name}.performance",
            logger_type="performance"
        )
    
    def create_debug_logger(self, name: str) -> Any:
        """创建调试日志器"""
        return self.create_logger(
            f"{name}.debug",
            logger_type="debug"
        )
    
    def create_audit_logger(self, name: str) -> Any:
        """创建审计日志器"""
        return self.create_logger(
            f"{name}.audit",
            logger_type="audit"
        )
    
    def get_logger(self, name: str) -> Optional[Any]:
        """获取已存在的日志器"""
        with self._lock:
            return self._loggers.get(name)
    
    def list_loggers(self) -> List[str]:
        """列出所有日志器名称"""
        with self._lock:
            return list(self._loggers.keys())


class LoggingManager:
    """日志系统主管理器"""
    
    def __init__(self, config: Optional[LoggingConfig] = None):
        self.config = config or LoggingConfig()
        self.context = LoggingContext()
        self.factory = LoggerFactory(self.config, self.context)
        self._initialized = False
        self._handlers: List[int] = []
        self._lock = threading.RLock()
        
        # 性能监控
        self._log_count = 0
        self._error_count = 0
        self._start_time = datetime.now()
    
    def initialize(self) -> bool:
        """初始化日志系统"""
        try:
            with self._lock:
                if self._initialized:
                    return True
                
                # 验证配置
                if not self.config.validate():
                    raise ValueError("Invalid logging configuration")
                
                # 清除现有处理器
                logger.remove()
                
                # 配置控制台输出
                if self.config.enable_console:
                    handler_id = logger.add(
                        sink=lambda msg: print(msg, end=""),
                        format=self.config.format_template,
                        level=self.config.level.value,
                        colorize=self.config.colorize,
                        serialize=False
                    )
                    self._handlers.append(handler_id)
                
                # 配置文件输出
                if self.config.enable_file:
                    handler_id = logger.add(
                        sink=self.config.file_path,
                        format=self.config.format_template,
                        level=self.config.level.value,
                        rotation=self.config.rotation,
                        retention=self.config.retention,
                        compression=self.config.compression,
                        serialize=self.config.serialize,
                        enqueue=self.config.enqueue,
                        backtrace=self.config.backtrace,
                        diagnose=self.config.diagnose
                    )
                    self._handlers.append(handler_id)
                
                # 设置默认上下文
                self.context.set_context("request_id", "system")
                self.context.set_context("session_id", str(uuid.uuid4()))
                
                self._initialized = True
                logger.info("Logging system initialized successfully")
                return True
                
        except Exception as e:
            print(f"Failed to initialize logging system: {e}")
            return False
    
    def shutdown(self) -> None:
        """关闭日志系统"""
        try:
            with self._lock:
                if not self._initialized:
                    return
                
                # 移除所有处理器
                for handler_id in self._handlers:
                    logger.remove(handler_id)
                
                self._handlers.clear()
                self._initialized = False
                
                logger.info("Logging system shutdown completed")
                
        except Exception as e:
            print(f"Error during logging system shutdown: {e}")
    
    def get_logger(self, name: str) -> Any:
        """获取日志器"""
        if not self._initialized:
            self.initialize()
        
        return self.factory.create_logger(name)
    
    def update_config(self, new_config: LoggingConfig) -> bool:
        """更新配置（热更新）"""
        try:
            with self._lock:
                old_config = self.config
                self.config = new_config
                
                # 重新初始化
                self.shutdown()
                success = self.initialize()
                
                if not success:
                    # 回滚配置
                    self.config = old_config
                    self.initialize()
                    return False
                
                logger.info("Logging configuration updated successfully")
                return True
                
        except Exception as e:
            logger.error(f"Failed to update logging configuration: {e}")
            return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取日志统计信息"""
        uptime = datetime.now() - self._start_time
        
        return {
            "initialized": self._initialized,
            "uptime_seconds": uptime.total_seconds(),
            "total_logs": self._log_count,
            "error_count": self._error_count,
            "error_rate": self._error_count / max(self._log_count, 1),
            "handlers_count": len(self._handlers),
            "loggers_count": len(self.factory.list_loggers()),
            "config": {
                "level": self.config.level.value,
                "async_enabled": self.config.async_enabled,
                "buffer_size": self.config.buffer_size
            }
        }
    
    @classmethod
    def load_config_from_file(cls, config_path: str) -> LoggingConfig:
        """从文件加载配置"""
        try:
            config_file = Path(config_path)
            
            if not config_file.exists():
                raise FileNotFoundError(f"Config file not found: {config_path}")
            
            with open(config_file, 'r', encoding='utf-8') as f:
                if config_path.endswith('.yaml') or config_path.endswith('.yml'):
                    data = yaml.safe_load(f)
                elif config_path.endswith('.json'):
                    data = json.load(f)
                else:
                    raise ValueError("Unsupported config file format")
            
            # 提取logging配置
            logging_data = data.get('logging', {})
            
            # 转换为LoggingConfig对象
            config = LoggingConfig()
            
            if 'level' in logging_data:
                config.level = LogLevel(logging_data['level'])
            
            # 更新其他配置字段
            for key, value in logging_data.items():
                if hasattr(config, key) and key != 'level':
                    setattr(config, key, value)
            
            return config
            
        except Exception as e:
            logger.error(f"Failed to load config from file: {e}")
            return LoggingConfig()  # 返回默认配置


# 全局日志管理器实例
_global_logging_manager: Optional[LoggingManager] = None


def get_global_logging_manager() -> LoggingManager:
    """获取全局日志管理器实例"""
    global _global_logging_manager
    
    if _global_logging_manager is None:
        _global_logging_manager = LoggingManager()
        _global_logging_manager.initialize()
    
    return _global_logging_manager


def get_logger(name: str) -> Any:
    """获取日志器的便捷函数"""
    manager = get_global_logging_manager()
    return manager.get_logger(name)


# 模块级别的日志器
module_logger = get_logger(__name__)