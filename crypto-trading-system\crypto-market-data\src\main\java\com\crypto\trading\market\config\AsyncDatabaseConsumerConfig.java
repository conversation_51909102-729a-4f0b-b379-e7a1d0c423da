package com.crypto.trading.market.config;

import com.crypto.trading.market.consumer.AsyncDatabaseConsumer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.context.event.EventListener;

import javax.annotation.PreDestroy;

/**
 * 异步数据库消费者配置类
 * 负责初始化和销毁AsyncDatabaseConsumer
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Configuration
public class AsyncDatabaseConsumerConfig {

    private static final Logger log = LoggerFactory.getLogger(AsyncDatabaseConsumerConfig.class);

    @Autowired
    private AsyncDatabaseConsumer asyncDatabaseConsumer;

    /**
     * 应用启动完成后初始化异步数据库消费者
     */
    @EventListener(ContextRefreshedEvent.class)
    public void initializeAsyncDatabaseConsumer() {
        try {
            asyncDatabaseConsumer.init();
            log.info("异步数据库消费者初始化成功");
        } catch (Exception e) {
            log.error("异步数据库消费者初始化失败: {}", e.getMessage(), e);
            throw new RuntimeException("异步数据库消费者初始化失败", e);
        }
    }

    /**
     * 应用关闭前销毁异步数据库消费者
     */
    @PreDestroy
    public void destroyAsyncDatabaseConsumer() {
        try {
            asyncDatabaseConsumer.destroy();
            log.info("异步数据库消费者销毁成功");
        } catch (Exception e) {
            log.error("异步数据库消费者销毁失败: {}", e.getMessage(), e);
        }
    }
}
