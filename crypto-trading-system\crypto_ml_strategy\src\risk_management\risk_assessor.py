"""
风险评估器模块

实现投资组合和市场风险的实时评估功能，包括VaR、CVaR、最大回撤、
夏普比率等风险指标的计算和监控。

Author: Crypto ML Strategy Team
Date: 2024-12-19
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta
import logging
from scipy import stats
import warnings

from .risk_config import RiskConfig, RiskMetrics, RiskLevel, RiskEvent

logger = logging.getLogger(__name__)


class RiskAssessor:
    """风险评估器类"""
    
    def __init__(self, config: RiskConfig):
        """
        初始化风险评估器
        
        Args:
            config: 风险管理配置
        """
        self.config = config
        self.price_history: Dict[str, pd.DataFrame] = {}
        self.portfolio_history: List[Dict[str, Any]] = []
        self.risk_metrics_history: List[RiskMetrics] = []
        
        logger.info("Risk assessor initialized")
    
    def update_price_data(self, symbol: str, price_data: pd.DataFrame) -> None:
        """
        更新价格数据
        
        Args:
            symbol: 交易对符号
            price_data: 价格数据，包含timestamp, open, high, low, close, volume列
        """
        try:
            # 确保数据格式正确
            required_columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume']
            if not all(col in price_data.columns for col in required_columns):
                raise ValueError(f"Price data must contain columns: {required_columns}")
            
            # 转换时间戳
            if 'timestamp' in price_data.columns:
                price_data['timestamp'] = pd.to_datetime(price_data['timestamp'])
                price_data.set_index('timestamp', inplace=True)
            
            # 计算收益率
            price_data['returns'] = price_data['close'].pct_change()
            price_data['log_returns'] = np.log(price_data['close'] / price_data['close'].shift(1))
            
            # 保存数据
            self.price_history[symbol] = price_data.copy()
            
            logger.debug(f"Updated price data for {symbol}, {len(price_data)} records")
            
        except Exception as e:
            logger.error(f"Error updating price data for {symbol}: {e}")
            raise
    
    def update_portfolio_data(self, portfolio_data: Dict[str, Any]) -> None:
        """
        更新投资组合数据
        
        Args:
            portfolio_data: 投资组合数据，包含positions, total_value, cash等
        """
        try:
            portfolio_data['timestamp'] = datetime.now()
            self.portfolio_history.append(portfolio_data.copy())
            
            # 保持历史数据在合理范围内
            max_history = self.config.risk_assessment_window * 2
            if len(self.portfolio_history) > max_history:
                self.portfolio_history = self.portfolio_history[-max_history:]
            
            logger.debug(f"Updated portfolio data, total records: {len(self.portfolio_history)}")
            
        except Exception as e:
            logger.error(f"Error updating portfolio data: {e}")
            raise
    
    def calculate_var(self, returns: np.ndarray, confidence_level: float = 0.95) -> float:
        """
        计算风险价值(VaR)
        
        Args:
            returns: 收益率序列
            confidence_level: 置信水平
            
        Returns:
            VaR值
        """
        try:
            if len(returns) == 0:
                return 0.0
            
            # 移除NaN值
            returns = returns[~np.isnan(returns)]
            
            if len(returns) == 0:
                return 0.0
            
            # 计算VaR
            var = np.percentile(returns, (1 - confidence_level) * 100)
            
            return abs(var)
            
        except Exception as e:
            logger.error(f"Error calculating VaR: {e}")
            return 0.0
    
    def calculate_cvar(self, returns: np.ndarray, confidence_level: float = 0.95) -> float:
        """
        计算条件风险价值(CVaR)
        
        Args:
            returns: 收益率序列
            confidence_level: 置信水平
            
        Returns:
            CVaR值
        """
        try:
            if len(returns) == 0:
                return 0.0
            
            # 移除NaN值
            returns = returns[~np.isnan(returns)]
            
            if len(returns) == 0:
                return 0.0
            
            # 计算VaR
            var = np.percentile(returns, (1 - confidence_level) * 100)
            
            # 计算CVaR（超过VaR的期望损失）
            tail_losses = returns[returns <= var]
            
            if len(tail_losses) == 0:
                return abs(var)
            
            cvar = np.mean(tail_losses)
            
            return abs(cvar)
            
        except Exception as e:
            logger.error(f"Error calculating CVaR: {e}")
            return 0.0
    
    def calculate_max_drawdown(self, portfolio_values: np.ndarray) -> Tuple[float, float]:
        """
        计算最大回撤和当前回撤
        
        Args:
            portfolio_values: 投资组合价值序列
            
        Returns:
            (最大回撤, 当前回撤)
        """
        try:
            if len(portfolio_values) == 0:
                return 0.0, 0.0
            
            # 计算累计最高值
            peak = np.maximum.accumulate(portfolio_values)
            
            # 计算回撤
            drawdown = (portfolio_values - peak) / peak
            
            # 最大回撤
            max_drawdown = abs(np.min(drawdown))
            
            # 当前回撤
            current_drawdown = abs(drawdown[-1])
            
            return max_drawdown, current_drawdown
            
        except Exception as e:
            logger.error(f"Error calculating drawdown: {e}")
            return 0.0, 0.0
    
    def calculate_sharpe_ratio(self, returns: np.ndarray, risk_free_rate: float = 0.02) -> float:
        """
        计算夏普比率
        
        Args:
            returns: 收益率序列
            risk_free_rate: 无风险利率(年化)
            
        Returns:
            夏普比率
        """
        try:
            if len(returns) == 0:
                return 0.0
            
            # 移除NaN值
            returns = returns[~np.isnan(returns)]
            
            if len(returns) == 0:
                return 0.0
            
            # 年化收益率
            annual_return = np.mean(returns) * 252
            
            # 年化波动率
            annual_volatility = np.std(returns) * np.sqrt(252)
            
            if annual_volatility == 0:
                return 0.0
            
            # 夏普比率
            sharpe_ratio = (annual_return - risk_free_rate) / annual_volatility
            
            return sharpe_ratio
            
        except Exception as e:
            logger.error(f"Error calculating Sharpe ratio: {e}")
            return 0.0
    
    def calculate_volatility(self, returns: np.ndarray, window: Optional[int] = None) -> float:
        """
        计算波动率
        
        Args:
            returns: 收益率序列
            window: 计算窗口，None表示使用全部数据
            
        Returns:
            年化波动率
        """
        try:
            if len(returns) == 0:
                return 0.0
            
            # 移除NaN值
            returns = returns[~np.isnan(returns)]
            
            if len(returns) == 0:
                return 0.0
            
            # 使用指定窗口
            if window and len(returns) > window:
                returns = returns[-window:]
            
            # 计算年化波动率
            volatility = np.std(returns) * np.sqrt(252)
            
            return volatility
            
        except Exception as e:
            logger.error(f"Error calculating volatility: {e}")
            return 0.0
    
    def calculate_correlation_matrix(self, symbols: List[str]) -> Optional[Dict[str, Dict[str, float]]]:
        """
        计算相关性矩阵
        
        Args:
            symbols: 交易对符号列表
            
        Returns:
            相关性矩阵字典
        """
        try:
            if len(symbols) < 2:
                return None
            
            # 收集收益率数据
            returns_data = {}
            for symbol in symbols:
                if symbol in self.price_history:
                    returns = self.price_history[symbol]['returns'].dropna()
                    if len(returns) > 0:
                        returns_data[symbol] = returns
            
            if len(returns_data) < 2:
                return None
            
            # 创建DataFrame
            df = pd.DataFrame(returns_data)
            
            # 计算相关性矩阵
            corr_matrix = df.corr()
            
            # 转换为字典格式
            result = {}
            for symbol1 in corr_matrix.index:
                result[symbol1] = {}
                for symbol2 in corr_matrix.columns:
                    result[symbol1][symbol2] = float(corr_matrix.loc[symbol1, symbol2])
            
            return result
            
        except Exception as e:
            logger.error(f"Error calculating correlation matrix: {e}")
            return None
    
    def assess_portfolio_risk(self, portfolio_data: Dict[str, Any]) -> RiskMetrics:
        """
        评估投资组合风险
        
        Args:
            portfolio_data: 投资组合数据
            
        Returns:
            风险指标
        """
        try:
            timestamp = datetime.now()
            portfolio_value = portfolio_data.get('total_value', 0.0)
            positions = portfolio_data.get('positions', {})
            
            # 计算总敞口
            total_exposure = sum(abs(pos.get('value', 0)) for pos in positions.values())
            
            # 获取投资组合历史价值
            portfolio_values = [p.get('total_value', 0) for p in self.portfolio_history if p.get('total_value')]
            
            if len(portfolio_values) > 1:
                portfolio_values = np.array(portfolio_values)
                portfolio_returns = np.diff(portfolio_values) / portfolio_values[:-1]
            else:
                portfolio_returns = np.array([])
            
            # 计算风险指标
            var_95 = self.calculate_var(portfolio_returns, self.config.var_confidence_level)
            cvar_95 = self.calculate_cvar(portfolio_returns, self.config.var_confidence_level)
            max_drawdown, current_drawdown = self.calculate_max_drawdown(portfolio_values)
            sharpe_ratio = self.calculate_sharpe_ratio(portfolio_returns)
            volatility = self.calculate_volatility(portfolio_returns, self.config.volatility_window)
            
            # 计算Beta（相对于市场）
            beta = self._calculate_portfolio_beta(positions)
            
            # 计算相关性矩阵
            symbols = list(positions.keys())
            correlation_matrix = self.calculate_correlation_matrix(symbols)
            
            # 确定风险等级
            risk_level = self._determine_risk_level(current_drawdown, volatility, var_95)
            
            # 创建风险指标对象
            risk_metrics = RiskMetrics(
                timestamp=timestamp,
                portfolio_value=portfolio_value,
                total_exposure=total_exposure,
                max_drawdown=max_drawdown,
                current_drawdown=current_drawdown,
                var_95=var_95,
                cvar_95=cvar_95,
                sharpe_ratio=sharpe_ratio,
                volatility=volatility,
                beta=beta,
                correlation_matrix=correlation_matrix,
                risk_level=risk_level
            )
            
            # 保存历史记录
            self.risk_metrics_history.append(risk_metrics)
            
            # 保持历史数据在合理范围内
            max_history = self.config.risk_assessment_window
            if len(self.risk_metrics_history) > max_history:
                self.risk_metrics_history = self.risk_metrics_history[-max_history:]
            
            logger.info(f"Portfolio risk assessment completed, risk level: {risk_level.value}")
            
            return risk_metrics
            
        except Exception as e:
            logger.error(f"Error assessing portfolio risk: {e}")
            # 返回默认风险指标
            return RiskMetrics(
                timestamp=datetime.now(),
                portfolio_value=0.0,
                total_exposure=0.0,
                max_drawdown=0.0,
                current_drawdown=0.0,
                var_95=0.0,
                cvar_95=0.0,
                sharpe_ratio=0.0,
                volatility=0.0,
                beta=1.0,
                risk_level=RiskLevel.MEDIUM
            )
    
    def _calculate_portfolio_beta(self, positions: Dict[str, Any]) -> float:
        """计算投资组合Beta值"""
        try:
            # 简化实现，假设Beta为1.0
            # 实际实现中需要相对于基准指数计算
            return 1.0
        except Exception as e:
            logger.error(f"Error calculating portfolio beta: {e}")
            return 1.0
    
    def _determine_risk_level(self, current_drawdown: float, volatility: float, var: float) -> RiskLevel:
        """确定风险等级"""
        try:
            # 基于多个指标确定风险等级
            risk_score = 0
            
            # 回撤评分
            if current_drawdown > 0.15:
                risk_score += 3
            elif current_drawdown > 0.1:
                risk_score += 2
            elif current_drawdown > 0.05:
                risk_score += 1
            
            # 波动率评分
            if volatility > 0.5:
                risk_score += 3
            elif volatility > 0.3:
                risk_score += 2
            elif volatility > 0.2:
                risk_score += 1
            
            # VaR评分
            if var > 0.1:
                risk_score += 3
            elif var > 0.05:
                risk_score += 2
            elif var > 0.02:
                risk_score += 1
            
            # 确定风险等级
            if risk_score >= 7:
                return RiskLevel.CRITICAL
            elif risk_score >= 5:
                return RiskLevel.HIGH
            elif risk_score >= 3:
                return RiskLevel.MEDIUM
            else:
                return RiskLevel.LOW
                
        except Exception as e:
            logger.error(f"Error determining risk level: {e}")
            return RiskLevel.MEDIUM
    
    def generate_risk_events(self, risk_metrics: RiskMetrics) -> List[RiskEvent]:
        """
        生成风险事件
        
        Args:
            risk_metrics: 风险指标
            
        Returns:
            风险事件列表
        """
        events = []
        
        try:
            timestamp = datetime.now()
            
            # 检查最大回撤
            if risk_metrics.current_drawdown > self.config.max_drawdown_threshold:
                events.append(RiskEvent(
                    event_type="max_drawdown_exceeded",
                    severity=RiskLevel.HIGH,
                    message=f"Current drawdown {risk_metrics.current_drawdown:.2%} exceeds threshold {self.config.max_drawdown_threshold:.2%}",
                    timestamp=timestamp,
                    current_value=risk_metrics.current_drawdown,
                    threshold_value=self.config.max_drawdown_threshold,
                    action_required=True
                ))
            
            # 检查高波动率
            volatility_threshold = self.config.alert_thresholds.get('high_volatility', 0.05)
            if risk_metrics.volatility > volatility_threshold:
                events.append(RiskEvent(
                    event_type="high_volatility",
                    severity=RiskLevel.MEDIUM,
                    message=f"Portfolio volatility {risk_metrics.volatility:.2%} is high",
                    timestamp=timestamp,
                    current_value=risk_metrics.volatility,
                    threshold_value=volatility_threshold
                ))
            
            # 检查VaR
            if risk_metrics.var_95 > 0.1:  # 10% VaR阈值
                events.append(RiskEvent(
                    event_type="high_var",
                    severity=RiskLevel.HIGH,
                    message=f"95% VaR {risk_metrics.var_95:.2%} is elevated",
                    timestamp=timestamp,
                    current_value=risk_metrics.var_95,
                    threshold_value=0.1
                ))
            
            # 检查相关性
            if risk_metrics.correlation_matrix:
                high_corr_threshold = self.config.alert_thresholds.get('high_correlation', 0.8)
                for symbol1, correlations in risk_metrics.correlation_matrix.items():
                    for symbol2, corr in correlations.items():
                        if symbol1 != symbol2 and abs(corr) > high_corr_threshold:
                            events.append(RiskEvent(
                                event_type="high_correlation",
                                severity=RiskLevel.MEDIUM,
                                message=f"High correlation between {symbol1} and {symbol2}: {corr:.2f}",
                                timestamp=timestamp,
                                current_value=abs(corr),
                                threshold_value=high_corr_threshold,
                                metadata={'symbol1': symbol1, 'symbol2': symbol2}
                            ))
            
            logger.info(f"Generated {len(events)} risk events")
            
        except Exception as e:
            logger.error(f"Error generating risk events: {e}")
        
        return events
    
    def get_risk_summary(self) -> Dict[str, Any]:
        """获取风险摘要"""
        try:
            if not self.risk_metrics_history:
                return {'status': 'no_data'}
            
            latest_metrics = self.risk_metrics_history[-1]
            
            return {
                'timestamp': latest_metrics.timestamp.isoformat(),
                'risk_level': latest_metrics.risk_level.value,
                'portfolio_value': latest_metrics.portfolio_value,
                'current_drawdown': latest_metrics.current_drawdown,
                'max_drawdown': latest_metrics.max_drawdown,
                'volatility': latest_metrics.volatility,
                'var_95': latest_metrics.var_95,
                'sharpe_ratio': latest_metrics.sharpe_ratio,
                'total_assessments': len(self.risk_metrics_history)
            }
            
        except Exception as e:
            logger.error(f"Error getting risk summary: {e}")
            return {'status': 'error', 'message': str(e)}
