package com.crypto.trading.common.enums;

/**
 * 市场数据类型枚举
 * <p>
 * 表示不同类型的市场数据
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public enum MarketDataType {

    /**
     * K线数据
     */
    KLINE("kline", "K线数据"),

    /**
     * 深度数据
     */
    DEPTH("depth", "深度数据"),

    /**
     * 逐笔成交数据
     */
    TRADE("trade", "逐笔成交数据"),

    /**
     * 24小时统计数据
     */
    TICKER_24HR("ticker24h", "24小时统计数据"),

    /**
     * 最新价格数据
     */
    TICKER_PRICE("tickerPrice", "最新价格数据"),

    /**
     * 最优挂单数据
     */
    TICKER_BOOK("tickerBook", "最优挂单数据"),

    /**
     * 聚合交易数据
     */
    AGG_TRADE("aggTrade", "聚合交易数据"),

    /**
     * 强平订单数据
     */
    LIQUIDATION("liquidation", "强平订单数据"),

    /**
     * 标记价格数据
     */
    MARK_PRICE("markPrice", "标记价格数据"),

    /**
     * 指数价格数据
     */
    INDEX_PRICE("indexPrice", "指数价格数据"),

    /**
     * 资金费率数据
     */
    FUNDING_RATE("fundingRate", "资金费率数据"),

    /**
     * 连续合约K线数据
     */
    CONTINUOUS_KLINE("continuousKline", "连续合约K线数据"),

    /**
     * 综合指数K线数据
     */
    COMPOSITE_INDEX_KLINE("compositeIndexKline", "综合指数K线数据");

    /**
     * 代码
     */
    private final String code;

    /**
     * 描述
     */
    private final String desc;

    /**
     * 构造函数
     *
     * @param code 代码
     * @param desc 描述
     */
    MarketDataType(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 获取代码
     *
     * @return 代码
     */
    public String getCode() {
        return code;
    }

    /**
     * 获取描述
     *
     * @return 描述
     */
    public String getDesc() {
        return desc;
    }

    /**
     * 根据代码获取枚举值
     *
     * @param code 代码
     * @return 枚举值
     */
    public static MarketDataType fromCode(String code) {
        for (MarketDataType type : MarketDataType.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知的市场数据类型代码: " + code);
    }
} 