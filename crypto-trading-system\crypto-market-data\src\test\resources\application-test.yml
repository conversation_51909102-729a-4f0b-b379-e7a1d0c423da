# 测试环境配置
spring:
  main:
    allow-bean-definition-overriding: true

# WebSocket配置
market:
  websocket:
    # 重连间隔（毫秒）
    reconnect-interval: 100
    # 连接超时（毫秒）
    connect-timeout: 500
    # 读取超时（毫秒）
    read-timeout: 1000
    # 心跳间隔（毫秒）
    ping-interval: 1000
    # 健康检查间隔（毫秒）
    health-check-interval: 100
    # 最大失败次数
    max-fail-count: 2
    # 连接池初始容量
    pool-initial-capacity: 4
    # 最大并发连接数
    max-concurrent-connections: 10
    
  # 市场数据配置
  symbols: BTCUSDT,ETHUSDT
  kline:
    enabled: true
    intervals: 1m,5m
  depth:
    enabled: true
    levels: 5
    speed: 100
  trade:
    enabled: true