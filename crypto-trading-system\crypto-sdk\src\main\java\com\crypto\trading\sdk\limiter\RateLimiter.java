package com.crypto.trading.sdk.limiter;

import java.util.concurrent.CompletableFuture;
import java.util.function.Supplier;

/**
 * API限流接口
 * 定义限流方法，用于控制API请求速率，防止超过API限制
 */
public interface RateLimiter {

    /**
     * 同步执行API请求，如果超过限流则阻塞等待
     *
     * @param weight   请求权重
     * @param endpoint API端点
     * @param supplier 请求执行函数
     * @param <T>      返回类型
     * @return 请求结果
     */
    <T> T execute(int weight, String endpoint, Supplier<T> supplier);

    /**
     * 异步执行API请求，如果超过限流则延迟执行
     *
     * @param weight   请求权重
     * @param endpoint API端点
     * @param supplier 请求执行函数
     * @param <T>      返回类型
     * @return 包含请求结果的CompletableFuture
     */
    <T> CompletableFuture<T> executeAsync(int weight, String endpoint, Supplier<T> supplier);
    
    /**
     * 异步执行API请求（String特化版本），如果超过限流则延迟执行
     *
     * @param weight   请求权重
     * @param endpoint API端点
     * @param supplier 请求执行函数
     * @return 包含请求结果的CompletableFuture
     */
    CompletableFuture<String> executeAsyncString(int weight, String endpoint, Supplier<String> supplier);

    /**
     * 获取当前剩余可用请求量
     *
     * @param endpoint API端点
     * @return 剩余可用请求量
     */
    int getAvailablePermits(String endpoint);

    /**
     * 获取当前使用率（已用/总量）
     *
     * @param endpoint API端点
     * @return 使用率（0.0-1.0）
     */
    double getUsageRate(String endpoint);

    /**
     * 重置限流器
     *
     * @param endpoint API端点，如果为null则重置所有端点
     */
    void reset(String endpoint);

    /**
     * 更新限流配置
     *
     * @param endpoint    API端点
     * @param maxRequests 最大请求数
     * @param timeWindow  时间窗口（毫秒）
     */
    void updateConfig(String endpoint, int maxRequests, long timeWindow);
} 