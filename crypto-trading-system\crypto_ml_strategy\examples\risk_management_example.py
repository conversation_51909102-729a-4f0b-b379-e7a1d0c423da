"""
风险管理模块使用示例

演示如何使用风险管理模块进行完整的风险控制流程，
包括风险评估、仓位管理、止损止盈、回撤控制等功能。

Author: Crypto ML Strategy Team
Date: 2024-12-19
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import time
import asyncio
from typing import Dict, List, Any

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

# 导入风险管理模块
from risk_management import (
    create_risk_manager,
    create_conservative_risk_manager,
    create_aggressive_risk_manager,
    RiskConfig,
    PositionInfo,
    RiskEvent,
    RiskLevel,
    PositionSizeMethod,
    StopLossType,
    TakeProfitType,
    get_factory,
    run_integration_test
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def generate_sample_price_data(symbol: str, days: int = 30) -> pd.DataFrame:
    """
    生成示例价格数据
    
    Args:
        symbol: 交易对符号
        days: 数据天数
        
    Returns:
        价格数据DataFrame
    """
    try:
        # 生成时间序列
        end_time = datetime.now()
        start_time = end_time - timedelta(days=days)
        timestamps = pd.date_range(start=start_time, end=end_time, freq='1H')
        
        # 生成价格数据（随机游走）
        np.random.seed(42)  # 确保可重复性
        returns = np.random.normal(0, 0.02, len(timestamps))
        
        # 初始价格
        initial_price = 50000 if symbol == 'BTCUSDT' else 3000
        
        # 计算价格序列
        prices = [initial_price]
        for ret in returns[1:]:
            new_price = prices[-1] * (1 + ret)
            prices.append(new_price)
        
        # 创建OHLCV数据
        price_data = pd.DataFrame({
            'timestamp': timestamps,
            'open': prices,
            'high': [p * (1 + np.random.uniform(0, 0.01)) for p in prices],
            'low': [p * (1 - np.random.uniform(0, 0.01)) for p in prices],
            'close': prices,
            'volume': np.random.uniform(100, 1000, len(timestamps))
        })
        
        return price_data
        
    except Exception as e:
        logger.error(f"Error generating sample price data: {e}")
        raise


def generate_sample_portfolio_data(base_value: float = 100000) -> Dict[str, Any]:
    """
    生成示例投资组合数据
    
    Args:
        base_value: 基础价值
        
    Returns:
        投资组合数据
    """
    try:
        return {
            'total_value': base_value,
            'balance': base_value,
            'available_balance': base_value * 0.8,
            'positions': {
                'BTCUSDT': {
                    'size': 1.5,
                    'value': base_value * 0.6,
                    'side': 'long'
                },
                'ETHUSDT': {
                    'size': 10.0,
                    'value': base_value * 0.3,
                    'side': 'long'
                }
            }
        }
        
    except Exception as e:
        logger.error(f"Error generating sample portfolio data: {e}")
        raise


def demonstrate_risk_assessment():
    """演示风险评估功能"""
    try:
        logger.info("=== 风险评估演示 ===")
        
        # 创建风险评估器
        factory = get_factory()
        risk_assessor = factory.create_assessor()
        
        # 更新价格数据
        btc_data = generate_sample_price_data('BTCUSDT')
        eth_data = generate_sample_price_data('ETHUSDT')
        
        risk_assessor.update_price_data('BTCUSDT', btc_data)
        risk_assessor.update_price_data('ETHUSDT', eth_data)
        
        # 更新投资组合数据
        portfolio_data = generate_sample_portfolio_data()
        risk_assessor.update_portfolio_data(portfolio_data)
        
        # 评估风险
        risk_metrics = risk_assessor.assess_portfolio_risk(portfolio_data)
        
        logger.info(f"风险评估结果:")
        logger.info(f"  投资组合价值: ${risk_metrics.portfolio_value:,.2f}")
        logger.info(f"  当前回撤: {risk_metrics.current_drawdown:.2%}")
        logger.info(f"  最大回撤: {risk_metrics.max_drawdown:.2%}")
        logger.info(f"  95% VaR: {risk_metrics.var_95:.2%}")
        logger.info(f"  95% CVaR: {risk_metrics.cvar_95:.2%}")
        logger.info(f"  夏普比率: {risk_metrics.sharpe_ratio:.2f}")
        logger.info(f"  波动率: {risk_metrics.volatility:.2%}")
        logger.info(f"  风险等级: {risk_metrics.risk_level.value}")
        
        # 生成风险事件
        risk_events = risk_assessor.generate_risk_events(risk_metrics)
        if risk_events:
            logger.info(f"生成了 {len(risk_events)} 个风险事件:")
            for event in risk_events:
                logger.info(f"  - {event.event_type}: {event.message}")
        
        return risk_assessor
        
    except Exception as e:
        logger.error(f"风险评估演示失败: {e}")
        raise


def demonstrate_position_management():
    """演示仓位管理功能"""
    try:
        logger.info("=== 仓位管理演示 ===")
        
        # 创建仓位管理器
        config = RiskConfig(
            position_size_method=PositionSizeMethod.ADAPTIVE,
            max_single_position_ratio=0.1,
            max_total_position_ratio=0.8
        )
        
        factory = get_factory()
        position_manager = factory.create_position_manager(config)
        
        # 更新账户信息
        position_manager.update_account_info(100000, 80000)
        
        # 计算仓位大小
        entry_price = 50000
        stop_loss_price = 48000
        market_data = {
            'volatility': 0.25,
            'win_rate': 0.6,
            'avg_win': 0.03,
            'avg_loss': 0.02
        }
        
        position_size = position_manager.calculate_position_size(
            'BTCUSDT',
            entry_price,
            stop_loss_price,
            market_data,
            signal_confidence=0.8
        )
        
        logger.info(f"仓位计算结果:")
        logger.info(f"  交易对: BTCUSDT")
        logger.info(f"  入场价格: ${entry_price:,.2f}")
        logger.info(f"  止损价格: ${stop_loss_price:,.2f}")
        logger.info(f"  建议仓位: {position_size:.4f} BTC")
        logger.info(f"  仓位价值: ${position_size * entry_price:,.2f}")
        logger.info(f"  账户比例: {(position_size * entry_price) / 100000:.2%}")
        
        # 更新仓位信息
        position_info = PositionInfo(
            symbol='BTCUSDT',
            current_size=position_size,
            suggested_size=position_size,
            max_allowed_size=position_size * 1.5,
            entry_price=entry_price,
            current_price=entry_price,
            unrealized_pnl=0.0,
            stop_loss_price=stop_loss_price,
            position_value=position_size * entry_price
        )
        
        position_manager.update_position('BTCUSDT', position_info)
        
        # 检查仓位限制
        violations = position_manager.check_position_limits()
        if violations:
            logger.warning(f"发现 {len(violations)} 个仓位限制违规:")
            for violation in violations:
                logger.warning(f"  - {violation['type']}: {violation}")
        else:
            logger.info("所有仓位限制检查通过")
        
        # 获取仓位摘要
        summary = position_manager.get_position_summary()
        logger.info(f"仓位摘要:")
        logger.info(f"  总仓位数: {summary['total_positions']}")
        logger.info(f"  总敞口比例: {summary['total_exposure_ratio']:.2%}")
        logger.info(f"  最大单笔敞口: {summary['max_single_exposure_ratio']:.2%}")
        logger.info(f"  是否在限制内: {summary['within_limits']}")
        
        return position_manager
        
    except Exception as e:
        logger.error(f"仓位管理演示失败: {e}")
        raise


def demonstrate_stop_loss_take_profit():
    """演示止损止盈功能"""
    try:
        logger.info("=== 止损止盈演示 ===")
        
        # 创建止损止盈管理器
        config = RiskConfig(
            stop_loss_type=StopLossType.ADAPTIVE,
            take_profit_type=TakeProfitType.RISK_REWARD_RATIO,
            default_stop_loss_pct=0.02,
            default_take_profit_pct=0.04,
            risk_reward_ratio=2.0
        )
        
        factory = get_factory()
        stop_manager = factory.create_stop_manager(config)
        
        # 更新价格数据和技术指标
        price_data = generate_sample_price_data('BTCUSDT')
        stop_manager.update_price_data('BTCUSDT', price_data)
        
        technical_indicators = {
            'atr': 1000,
            'ma20': 49500,
            'support_level': 48500,
            'resistance_level': 51500
        }
        stop_manager.update_technical_indicators('BTCUSDT', technical_indicators)
        
        # 计算止损止盈价格
        entry_price = 50000
        position_side = 'long'
        
        stop_loss_price = stop_manager.calculate_stop_loss_price(
            'BTCUSDT',
            entry_price,
            position_side
        )
        
        take_profit_price = stop_manager.calculate_take_profit_price(
            'BTCUSDT',
            entry_price,
            position_side,
            stop_loss_price
        )
        
        logger.info(f"止损止盈计算结果:")
        logger.info(f"  入场价格: ${entry_price:,.2f}")
        logger.info(f"  止损价格: ${stop_loss_price:,.2f}")
        logger.info(f"  止盈价格: ${take_profit_price:,.2f}")
        logger.info(f"  止损距离: {abs(entry_price - stop_loss_price) / entry_price:.2%}")
        logger.info(f"  止盈距离: {abs(take_profit_price - entry_price) / entry_price:.2%}")
        logger.info(f"  风险收益比: {abs(take_profit_price - entry_price) / abs(entry_price - stop_loss_price):.2f}")
        
        # 模拟价格变动和触发检查
        test_prices = [49000, 48000, 51000, 52000]
        position_info = {
            'stop_loss_price': stop_loss_price,
            'take_profit_price': take_profit_price,
            'side': position_side
        }
        
        for test_price in test_prices:
            stop_triggered = stop_manager.check_stop_loss_trigger('BTCUSDT', test_price, position_info)
            profit_triggered = stop_manager.check_take_profit_trigger('BTCUSDT', test_price, position_info)
            
            logger.info(f"价格 ${test_price:,.2f}: 止损触发={stop_triggered}, 止盈触发={profit_triggered}")
        
        return stop_manager
        
    except Exception as e:
        logger.error(f"止损止盈演示失败: {e}")
        raise


def demonstrate_drawdown_control():
    """演示回撤控制功能"""
    try:
        logger.info("=== 回撤控制演示 ===")
        
        # 创建回撤控制器
        factory = get_factory()
        drawdown_controller = factory.create_drawdown_controller()
        
        # 模拟投资组合价值变化
        initial_value = 100000
        portfolio_values = [
            100000, 102000, 98000, 95000, 92000,  # 下跌阶段
            94000, 96000, 99000, 101000, 103000   # 恢复阶段
        ]
        
        logger.info("模拟投资组合价值变化:")
        
        for i, value in enumerate(portfolio_values):
            timestamp = datetime.now() - timedelta(days=len(portfolio_values)-i)
            drawdown_info = drawdown_controller.update_portfolio_value(value, timestamp)
            
            logger.info(f"  第{i+1}天: ${value:,.2f}, 回撤: {drawdown_info.drawdown_percentage:.2%}")
            
            # 检查回撤限制
            events = drawdown_controller.check_drawdown_limits(drawdown_info)
            if events:
                for event in events:
                    logger.warning(f"    风险事件: {event.event_type} - {event.message}")
            
            # 获取恢复建议
            if drawdown_info.drawdown_percentage > 0.05:  # 回撤超过5%
                recovery_actions = drawdown_controller.suggest_recovery_actions(drawdown_info)
                if recovery_actions:
                    logger.info(f"    恢复建议:")
                    for action in recovery_actions[:2]:  # 显示前2个建议
                        logger.info(f"      - {action.description}")
        
        # 获取回撤统计
        statistics = drawdown_controller.get_drawdown_statistics()
        logger.info(f"回撤统计:")
        logger.info(f"  当前回撤: {statistics['current_drawdown']:.2%}")
        logger.info(f"  最大回撤: {statistics['max_drawdown']:.2%}")
        logger.info(f"  当前峰值: ${statistics['current_peak_value']:,.2f}")
        logger.info(f"  告警级别: {statistics['current_alert_level']}")
        
        # 分析回撤模式
        pattern_analysis = drawdown_controller.analyze_drawdown_patterns()
        if pattern_analysis.get('status') != 'insufficient_data':
            logger.info(f"回撤模式分析:")
            logger.info(f"  平均回撤: {pattern_analysis['average_drawdown']:.2%}")
            logger.info(f"  回撤标准差: {pattern_analysis['drawdown_std']:.2%}")
            logger.info(f"  平均持续时间: {pattern_analysis['average_duration_hours']:.1f}小时")
        
        return drawdown_controller
        
    except Exception as e:
        logger.error(f"回撤控制演示失败: {e}")
        raise


def demonstrate_risk_control_engine():
    """演示风险控制引擎功能"""
    try:
        logger.info("=== 风险控制引擎演示 ===")
        
        # 创建风险控制引擎
        risk_manager = create_risk_manager()
        
        # 启动引擎
        risk_manager.start()
        logger.info("风险控制引擎已启动")
        
        # 更新市场数据
        btc_data = generate_sample_price_data('BTCUSDT')
        market_data = {
            'price_df': btc_data,
            'indicators': {
                'atr': 1000,
                'ma20': 49500,
                'volatility': 0.25
            }
        }
        risk_manager.update_market_data('BTCUSDT', market_data)
        
        # 更新投资组合数据
        portfolio_data = generate_sample_portfolio_data(90000)  # 模拟亏损
        risk_manager.update_portfolio_data(portfolio_data)
        
        # 更新仓位信息
        position_info = PositionInfo(
            symbol='BTCUSDT',
            current_size=1.5,
            suggested_size=1.5,
            max_allowed_size=2.0,
            entry_price=50000,
            current_price=48000,  # 模拟价格下跌
            unrealized_pnl=-3000,
            stop_loss_price=47000,
            take_profit_price=53000,
            position_value=72000
        )
        risk_manager.update_position('BTCUSDT', position_info)
        
        # 等待一段时间让引擎处理
        time.sleep(2)
        
        # 获取引擎状态
        status = risk_manager.get_engine_status()
        logger.info(f"引擎状态:")
        logger.info(f"  运行状态: {status['is_running']}")
        logger.info(f"  紧急模式: {status['emergency_mode']}")
        logger.info(f"  交易暂停: {status['trading_suspended']}")
        logger.info(f"  待处理动作: {status['pending_actions']}")
        logger.info(f"  近期事件: {status['recent_events']}")
        
        # 手动添加风险事件
        risk_event = RiskEvent(
            event_type="test_event",
            severity=RiskLevel.HIGH,
            message="测试风险事件",
            timestamp=datetime.now(),
            action_required=True
        )
        risk_manager.add_risk_event(risk_event)
        
        # 再次等待处理
        time.sleep(1)
        
        # 停止引擎
        risk_manager.stop()
        logger.info("风险控制引擎已停止")
        
        return risk_manager
        
    except Exception as e:
        logger.error(f"风险控制引擎演示失败: {e}")
        raise


def demonstrate_different_risk_profiles():
    """演示不同风险配置"""
    try:
        logger.info("=== 不同风险配置演示 ===")
        
        # 保守型风险管理器
        logger.info("创建保守型风险管理器:")
        conservative_manager = create_conservative_risk_manager()
        conservative_status = conservative_manager.get_engine_status()
        logger.info(f"  保守型配置创建成功")
        
        # 激进型风险管理器
        logger.info("创建激进型风险管理器:")
        aggressive_manager = create_aggressive_risk_manager()
        aggressive_status = aggressive_manager.get_engine_status()
        logger.info(f"  激进型配置创建成功")
        
        # 自定义配置
        logger.info("创建自定义风险管理器:")
        custom_config = RiskConfig(
            max_single_position_ratio=0.08,
            max_total_position_ratio=0.75,
            position_size_method=PositionSizeMethod.KELLY_FORMULA,
            stop_loss_type=StopLossType.ATR_BASED,
            take_profit_type=TakeProfitType.PARTIAL_PROFIT,
            max_drawdown_threshold=0.12,
            risk_reward_ratio=2.5
        )
        
        custom_manager = create_risk_manager(custom_config)
        custom_status = custom_manager.get_engine_status()
        logger.info(f"  自定义配置创建成功")
        
        logger.info("所有风险配置演示完成")
        
    except Exception as e:
        logger.error(f"不同风险配置演示失败: {e}")
        raise


def main():
    """主函数"""
    try:
        logger.info("开始风险管理模块完整演示")
        
        # 运行集成测试
        logger.info("运行集成测试...")
        test_result = run_integration_test()
        if not test_result:
            logger.error("集成测试失败，停止演示")
            return
        
        logger.info("集成测试通过，开始功能演示")
        
        # 演示各个功能模块
        demonstrate_risk_assessment()
        print("\n" + "="*50 + "\n")
        
        demonstrate_position_management()
        print("\n" + "="*50 + "\n")
        
        demonstrate_stop_loss_take_profit()
        print("\n" + "="*50 + "\n")
        
        demonstrate_drawdown_control()
        print("\n" + "="*50 + "\n")
        
        demonstrate_risk_control_engine()
        print("\n" + "="*50 + "\n")
        
        demonstrate_different_risk_profiles()
        
        logger.info("风险管理模块演示完成！")
        logger.info("所有功能组件运行正常，可以集成到主系统中使用。")
        
    except Exception as e:
        logger.error(f"演示过程中发生错误: {e}")
        raise


if __name__ == "__main__":
    main()
