package com.crypto.trading.common.dto;

import com.crypto.trading.common.constant.SystemConstants;
import com.crypto.trading.common.exception.ErrorCode;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ApiResponse单元测试类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
class ApiResponseTest {

    /**
     * 测试成功响应（无数据）
     */
    @Test
    void testSuccessWithoutData() {
        // 创建成功响应
        ApiResponse<Object> response = ApiResponse.success();

        // 验证属性
        assertEquals(SystemConstants.SUCCESS_CODE, response.getCode());
        assertEquals(SystemConstants.SUCCESS_MESSAGE, response.getMessage());
        assertNull(response.getData());
        assertTrue(response.getTimestamp() > 0);
    }

    /**
     * 测试成功响应（有数据）
     */
    @Test
    void testSuccessWithData() {
        // 创建测试数据
        String testData = "测试数据";

        // 创建成功响应
        ApiResponse<String> response = ApiResponse.success(testData);

        // 验证属性
        assertEquals(SystemConstants.SUCCESS_CODE, response.getCode());
        assertEquals(SystemConstants.SUCCESS_MESSAGE, response.getMessage());
        assertEquals(testData, response.getData());
        assertTrue(response.getTimestamp() > 0);
    }

    /**
     * 测试成功响应（自定义消息和数据）
     */
    @Test
    void testSuccessWithMessageAndData() {
        // 创建测试数据和消息
        String testData = "测试数据";
        String testMessage = "自定义成功消息";

        // 创建成功响应 - 使用消息和数据的方法
        ApiResponse<String> response = new ApiResponse<>(SystemConstants.SUCCESS_CODE, testMessage, testData);

        // 验证属性
        assertEquals(SystemConstants.SUCCESS_CODE, response.getCode());
        assertEquals(testMessage, response.getMessage());
        assertEquals(testData, response.getData());
        assertTrue(response.getTimestamp() > 0);
    }

    /**
     * 测试失败响应（无消息）
     */
    @Test
    void testErrorWithoutMessage() {
        // 创建失败响应
        ApiResponse<Object> response = ApiResponse.error();

        // 验证属性
        assertEquals(SystemConstants.ERROR_CODE, response.getCode());
        assertEquals(SystemConstants.ERROR_MESSAGE, response.getMessage());
        assertNull(response.getData());
        assertTrue(response.getTimestamp() > 0);
    }

    /**
     * 测试失败响应（自定义消息）
     */
    @Test
    void testErrorWithMessage() {
        // 创建自定义错误消息
        String errorMessage = "自定义错误消息";

        // 创建失败响应
        ApiResponse<Object> response = ApiResponse.error(errorMessage);

        // 验证属性
        assertEquals(SystemConstants.ERROR_CODE, response.getCode());
        assertEquals(errorMessage, response.getMessage());
        assertNull(response.getData());
        assertTrue(response.getTimestamp() > 0);
    }

    /**
     * 测试失败响应（自定义错误码和消息）
     */
    @Test
    void testErrorWithCodeAndMessage() {
        // 创建自定义错误码和消息
        int errorCode = 9999;
        String errorMessage = "自定义错误消息";

        // 创建失败响应
        ApiResponse<Object> response = ApiResponse.error(errorCode, errorMessage);

        // 验证属性
        assertEquals(errorCode, response.getCode());
        assertEquals(errorMessage, response.getMessage());
        assertNull(response.getData());
        assertTrue(response.getTimestamp() > 0);
    }

    /**
     * 测试失败响应（使用错误码枚举）
     */
    @Test
    void testErrorWithErrorCode() {
        // 创建失败响应
        ApiResponse<Object> response = ApiResponse.error(ErrorCode.PARAM_ERROR);

        // 验证属性
        assertEquals(ErrorCode.PARAM_ERROR.getCode(), response.getCode());
        assertEquals(ErrorCode.PARAM_ERROR.getMessage(), response.getMessage());
        assertNull(response.getData());
        assertTrue(response.getTimestamp() > 0);
    }

    /**
     * 测试失败响应（使用错误码枚举和自定义消息）
     */
    @Test
    void testErrorWithErrorCodeAndMessage() {
        // 创建自定义错误消息
        String errorMessage = "自定义参数错误消息";

        // 创建失败响应
        ApiResponse<Object> response = ApiResponse.error(ErrorCode.PARAM_ERROR, errorMessage);

        // 验证属性
        assertEquals(ErrorCode.PARAM_ERROR.getCode(), response.getCode());
        assertEquals(errorMessage, response.getMessage());
        assertNull(response.getData());
        assertTrue(response.getTimestamp() > 0);
    }

    /**
     * 测试构造函数
     */
    @Test
    void testConstructor() {
        // 创建测试数据
        int code = 200;
        String message = "测试消息";
        String data = "测试数据";

        // 创建响应
        ApiResponse<String> response = new ApiResponse<>(code, message, data);

        // 验证属性
        assertEquals(code, response.getCode());
        assertEquals(message, response.getMessage());
        assertEquals(data, response.getData());
        assertTrue(response.getTimestamp() > 0);
    }
} 