#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
技术指标集成服务模块

集成LPPL、Hematread、BMSB、SuperTrend四个技术指标，
提供统一的技术指标计算和信号生成接口。
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass
from loguru import logger

from ..indicators.lppl_features import LPPLFeatureExtractor
from ..indicators.hematread_features import HematreadFeatureExtractor
from ..indicators.bull_market_support import BullMarketSupportFeatureExtractor
from ..indicators.super_trend import SuperTrendCalculator
from ..infrastructure.intelligent_cache import cache_result, get_cache_service
from ..infrastructure.service_interfaces import ITechnicalIndicatorService


@dataclass
class TechnicalIndicatorResult:
    """技术指标计算结果"""
    symbol: str
    timeframe: str
    timestamp: float
    lppl_features: Dict[str, Any]
    hematread_features: Dict[str, Any]
    bmsb_features: Dict[str, Any]
    supertrend_features: Dict[str, Any]
    combined_signal: Dict[str, Any]
    confidence_score: float


class TechnicalIndicatorService(ITechnicalIndicatorService):
    """
    技术指标集成服务
    
    集成LPPL、Hematread、BMSB、SuperTrend四个技术指标，
    提供统一的技术指标计算和信号生成接口。
    """
    
    def __init__(self):
        """初始化技术指标服务"""
        # 初始化各个技术指标计算器
        self.lppl_extractor = LPPLFeatureExtractor()
        self.hematread_extractor = HematreadFeatureExtractor()
        self.bmsb_extractor = BullMarketSupportFeatureExtractor()
        self.supertrend_calculator = SuperTrendCalculator()
        
        # 缓存服务
        self.cache_service = get_cache_service()
        
        # 指标权重配置
        self.indicator_weights = {
            'lppl': 0.25,
            'hematread': 0.25,
            'bmsb': 0.25,
            'supertrend': 0.25
        }
        
        # 信号阈值配置
        self.signal_thresholds = {
            'strong_buy': 0.8,
            'buy': 0.6,
            'neutral': 0.4,
            'sell': 0.2,
            'strong_sell': 0.0
        }
        
        logger.info("技术指标服务初始化完成")
    
    @cache_result(ttl=60.0)  # 缓存1分钟
    def calculate_all_indicators(self, 
                                data: pd.DataFrame, 
                                symbol: str = "BTCUSDT",
                                timeframe: str = "1m") -> TechnicalIndicatorResult:
        """
        计算所有技术指标
        
        Args:
            data: 市场数据DataFrame
            symbol: 交易对符号
            timeframe: 时间框架
            
        Returns:
            技术指标计算结果
        """
        try:
            logger.debug(f"开始计算技术指标 - {symbol} {timeframe}")
            
            # 数据验证
            if data.empty or len(data) < 50:
                logger.warning(f"数据不足，无法计算技术指标: {len(data)} 行")
                return self._create_empty_result(symbol, timeframe)
            
            # 1. 计算LPPL特征
            lppl_features = self._calculate_lppl_features(data)
            
            # 2. 计算Hematread特征
            hematread_features = self._calculate_hematread_features(data)
            
            # 3. 计算BMSB特征
            bmsb_features = self._calculate_bmsb_features(data)
            
            # 4. 计算SuperTrend特征
            supertrend_features = self._calculate_supertrend_features(data)
            
            # 5. 生成综合信号
            combined_signal = self._generate_combined_signal(
                lppl_features, hematread_features, bmsb_features, supertrend_features
            )
            
            # 6. 计算置信度分数
            confidence_score = self._calculate_confidence_score(
                lppl_features, hematread_features, bmsb_features, supertrend_features
            )
            
            result = TechnicalIndicatorResult(
                symbol=symbol,
                timeframe=timeframe,
                timestamp=data.index[-1].timestamp() if hasattr(data.index[-1], 'timestamp') else data.index[-1],
                lppl_features=lppl_features,
                hematread_features=hematread_features,
                bmsb_features=bmsb_features,
                supertrend_features=supertrend_features,
                combined_signal=combined_signal,
                confidence_score=confidence_score
            )
            
            logger.debug(f"技术指标计算完成 - 信号: {combined_signal['signal']}, "
                        f"置信度: {confidence_score:.3f}")
            
            return result
            
        except Exception as e:
            logger.error(f"技术指标计算失败: {e}")
            return self._create_empty_result(symbol, timeframe)
    
    def _calculate_lppl_features(self, data: pd.DataFrame) -> Dict[str, Any]:
        """计算LPPL特征"""
        try:
            # 使用LPPL特征提取器
            features = self.lppl_extractor.extract_features(data)
            
            # 生成LPPL信号
            bubble_probability = features.get('bubble_probability', 0.0)
            confidence = features.get('confidence', 0.0)
            
            signal_strength = 0.0
            if bubble_probability > 0.7 and confidence > 0.6:
                signal_strength = -0.8  # 强烈卖出信号
            elif bubble_probability > 0.5 and confidence > 0.4:
                signal_strength = -0.4  # 卖出信号
            elif bubble_probability < 0.3:
                signal_strength = 0.2   # 轻微买入信号
            
            return {
                **features,
                'signal_strength': signal_strength,
                'signal_type': 'bubble_detection'
            }
            
        except Exception as e:
            logger.warning(f"LPPL特征计算失败: {e}")
            return {
                'bubble_probability': 0.0,
                'confidence': 0.0,
                'signal_strength': 0.0,
                'signal_type': 'bubble_detection',
                'error': str(e)
            }
    
    def _calculate_hematread_features(self, data: pd.DataFrame) -> Dict[str, Any]:
        """计算Hematread特征"""
        try:
            # 使用Hematread特征提取器
            features = self.hematread_extractor.extract_features(data)
            
            # 生成Hematread信号
            momentum_strength = features.get('momentum_strength', 0.0)
            trend_strength = features.get('trend_strength', 0.0)
            
            signal_strength = (momentum_strength + trend_strength) / 2
            
            return {
                **features,
                'signal_strength': signal_strength,
                'signal_type': 'momentum_trend'
            }
            
        except Exception as e:
            logger.warning(f"Hematread特征计算失败: {e}")
            return {
                'momentum_strength': 0.0,
                'trend_strength': 0.0,
                'signal_strength': 0.0,
                'signal_type': 'momentum_trend',
                'error': str(e)
            }
    
    def _calculate_bmsb_features(self, data: pd.DataFrame) -> Dict[str, Any]:
        """计算BMSB特征"""
        try:
            # 使用BMSB特征提取器
            features = self.bmsb_extractor.extract_features(data)
            
            # 生成BMSB信号
            support_strength = features.get('support_strength', 0.0)
            bounce_probability = features.get('bounce_probability', 0.0)
            
            signal_strength = 0.0
            if bounce_probability > 0.7 and support_strength > 0.6:
                signal_strength = 0.8   # 强烈买入信号
            elif bounce_probability > 0.5:
                signal_strength = 0.4   # 买入信号
            elif support_strength < 0.3:
                signal_strength = -0.2  # 轻微卖出信号
            
            return {
                **features,
                'signal_strength': signal_strength,
                'signal_type': 'support_bounce'
            }
            
        except Exception as e:
            logger.warning(f"BMSB特征计算失败: {e}")
            return {
                'support_strength': 0.0,
                'bounce_probability': 0.0,
                'signal_strength': 0.0,
                'signal_type': 'support_bounce',
                'error': str(e)
            }
    
    def _calculate_supertrend_features(self, data: pd.DataFrame) -> Dict[str, Any]:
        """计算SuperTrend特征"""
        try:
            # 使用SuperTrend计算器
            features = self.supertrend_calculator.calculate_multi_timeframe_supertrend(data)
            
            # 生成SuperTrend信号
            trend_direction = features.get('trend_direction', 0)
            trend_strength = features.get('trend_strength', 0.0)
            
            signal_strength = trend_direction * trend_strength
            
            return {
                **features,
                'signal_strength': signal_strength,
                'signal_type': 'trend_following'
            }
            
        except Exception as e:
            logger.warning(f"SuperTrend特征计算失败: {e}")
            return {
                'trend_direction': 0,
                'trend_strength': 0.0,
                'signal_strength': 0.0,
                'signal_type': 'trend_following',
                'error': str(e)
            }
    
    def _generate_combined_signal(self, 
                                 lppl_features: Dict[str, Any],
                                 hematread_features: Dict[str, Any],
                                 bmsb_features: Dict[str, Any],
                                 supertrend_features: Dict[str, Any]) -> Dict[str, Any]:
        """生成综合信号"""
        try:
            # 获取各指标的信号强度
            lppl_signal = lppl_features.get('signal_strength', 0.0)
            hematread_signal = hematread_features.get('signal_strength', 0.0)
            bmsb_signal = bmsb_features.get('signal_strength', 0.0)
            supertrend_signal = supertrend_features.get('signal_strength', 0.0)
            
            # 加权平均计算综合信号
            weighted_signal = (
                lppl_signal * self.indicator_weights['lppl'] +
                hematread_signal * self.indicator_weights['hematread'] +
                bmsb_signal * self.indicator_weights['bmsb'] +
                supertrend_signal * self.indicator_weights['supertrend']
            )
            
            # 归一化到[-1, 1]范围
            normalized_signal = np.clip(weighted_signal, -1.0, 1.0)
            
            # 转换为信号类型
            signal_type = self._signal_to_type(normalized_signal)
            
            # 计算信号一致性
            signals = [lppl_signal, hematread_signal, bmsb_signal, supertrend_signal]
            consistency = self._calculate_signal_consistency(signals)
            
            return {
                'signal': signal_type,
                'strength': abs(normalized_signal),
                'direction': 1 if normalized_signal > 0 else -1 if normalized_signal < 0 else 0,
                'raw_score': normalized_signal,
                'consistency': consistency,
                'individual_signals': {
                    'lppl': lppl_signal,
                    'hematread': hematread_signal,
                    'bmsb': bmsb_signal,
                    'supertrend': supertrend_signal
                }
            }
            
        except Exception as e:
            logger.error(f"综合信号生成失败: {e}")
            return {
                'signal': 'neutral',
                'strength': 0.0,
                'direction': 0,
                'raw_score': 0.0,
                'consistency': 0.0,
                'error': str(e)
            }
    
    def _signal_to_type(self, signal_value: float) -> str:
        """将信号值转换为信号类型"""
        if signal_value >= self.signal_thresholds['strong_buy']:
            return 'strong_buy'
        elif signal_value >= self.signal_thresholds['buy']:
            return 'buy'
        elif signal_value >= self.signal_thresholds['neutral']:
            return 'neutral'
        elif signal_value >= self.signal_thresholds['sell']:
            return 'sell'
        else:
            return 'strong_sell'
    
    def _calculate_signal_consistency(self, signals: List[float]) -> float:
        """计算信号一致性"""
        if not signals:
            return 0.0
        
        # 计算信号的标准差，标准差越小一致性越高
        std_dev = np.std(signals)
        max_std = 1.0  # 最大可能的标准差
        
        # 一致性 = 1 - (标准差 / 最大标准差)
        consistency = max(0.0, 1.0 - (std_dev / max_std))
        
        return consistency
    
    def _calculate_confidence_score(self, 
                                   lppl_features: Dict[str, Any],
                                   hematread_features: Dict[str, Any],
                                   bmsb_features: Dict[str, Any],
                                   supertrend_features: Dict[str, Any]) -> float:
        """计算置信度分数"""
        try:
            # 获取各指标的置信度
            lppl_confidence = lppl_features.get('confidence', 0.0)
            hematread_confidence = abs(hematread_features.get('signal_strength', 0.0))
            bmsb_confidence = bmsb_features.get('support_strength', 0.0)
            supertrend_confidence = supertrend_features.get('trend_strength', 0.0)
            
            # 加权平均计算总置信度
            total_confidence = (
                lppl_confidence * self.indicator_weights['lppl'] +
                hematread_confidence * self.indicator_weights['hematread'] +
                bmsb_confidence * self.indicator_weights['bmsb'] +
                supertrend_confidence * self.indicator_weights['supertrend']
            )
            
            return np.clip(total_confidence, 0.0, 1.0)
            
        except Exception as e:
            logger.warning(f"置信度计算失败: {e}")
            return 0.0
    
    def _create_empty_result(self, symbol: str, timeframe: str) -> TechnicalIndicatorResult:
        """创建空的结果对象"""
        return TechnicalIndicatorResult(
            symbol=symbol,
            timeframe=timeframe,
            timestamp=0.0,
            lppl_features={'signal_strength': 0.0},
            hematread_features={'signal_strength': 0.0},
            bmsb_features={'signal_strength': 0.0},
            supertrend_features={'signal_strength': 0.0},
            combined_signal={
                'signal': 'neutral',
                'strength': 0.0,
                'direction': 0,
                'raw_score': 0.0,
                'consistency': 0.0
            },
            confidence_score=0.0
        )
    
    def update_indicator_weights(self, weights: Dict[str, float]) -> None:
        """更新指标权重"""
        # 验证权重总和为1
        total_weight = sum(weights.values())
        if abs(total_weight - 1.0) > 0.01:
            logger.warning(f"指标权重总和不为1: {total_weight}")
            # 归一化权重
            weights = {k: v / total_weight for k, v in weights.items()}
        
        self.indicator_weights.update(weights)
        logger.info(f"指标权重已更新: {self.indicator_weights}")
    
    def update_signal_thresholds(self, thresholds: Dict[str, float]) -> None:
        """更新信号阈值"""
        self.signal_thresholds.update(thresholds)
        logger.info(f"信号阈值已更新: {self.signal_thresholds}")
    
    def get_indicator_status(self) -> Dict[str, Any]:
        """获取指标服务状态"""
        return {
            'service_name': 'TechnicalIndicatorService',
            'indicators': ['LPPL', 'Hematread', 'BMSB', 'SuperTrend'],
            'weights': self.indicator_weights,
            'thresholds': self.signal_thresholds,
            'cache_stats': self.cache_service.get_stats()
        }