#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
集成测试运行器

提供统一的集成测试执行入口，支持配置管理、测试执行和报告生成。
"""

import argparse
import asyncio
import sys
from pathlib import Path
from typing import List, Optional
from loguru import logger

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from tests.integration.config.integration_test_config import get_test_config
from tests.integration.utils.test_orchestrator import create_test_orchestrator
from tests.integration.reports.test_reporter import create_test_reporter, ReportConfig
from tests.integration.fixtures.mock_services import setup_mock_environment, teardown_mock_environment
from tests.integration.data.test_data_generator import create_test_data_generator
from tests.integration.suites.example_test_suite import create_example_test_suite
from tests.integration.suites.data_flow_integration_test_suite import create_data_flow_integration_test_suite
from tests.integration.suites.ml_pipeline_integration_test_suite import create_ml_pipeline_integration_test_suite
from tests.integration.suites.risk_management_integration_test_suite import RiskManagementIntegrationTestSuite
from tests.integration.suites.test_risk_control_system import RiskControlSystemTest
from tests.integration.suites.test_position_management import PositionManagementTest
from tests.integration.suites.test_risk_monitoring import RiskMonitoringTest


class IntegrationTestRunner:
    """集成测试运行器"""
    
    def __init__(self, environment: str = "dev", config_file: Optional[str] = None):
        """
        初始化测试运行器
        
        Args:
            environment: 测试环境
            config_file: 配置文件路径
        """
        self.environment = environment
        self.config = get_test_config(environment)
        self.orchestrator = None
        self.reporter = None
        
        logger.info(f"集成测试运行器已初始化 - 环境: {environment}")
    
    async def setup_environment(self) -> None:
        """设置测试环境"""
        logger.info("设置测试环境...")
        
        # 设置Mock服务
        await setup_mock_environment()
        
        # 创建测试编排器
        self.orchestrator = create_test_orchestrator(
            max_workers=self.config.test_env.max_workers,
            default_timeout=self.config.test_env.test_timeout_seconds
        )
        
        # 创建报告生成器
        report_config = ReportConfig(
            output_dir=str(Path(__file__).parent / "reports"),
            include_charts=True,
            include_logs=True,
            include_artifacts=True
        )
        self.reporter = create_test_reporter(report_config)
        
        logger.info("测试环境设置完成")
    
    async def teardown_environment(self) -> None:
        """清理测试环境"""
        logger.info("清理测试环境...")
        
        # 清理Mock服务
        await teardown_mock_environment()
        
        logger.info("测试环境清理完成")
    
    async def generate_test_data(self) -> None:
        """生成测试数据"""
        logger.info("生成测试数据...")
        
        # 创建数据生成器
        generator_config = {
            'symbols': self.config.test_data.symbols,
            'timeframes': self.config.test_data.timeframes,
            'data_points_per_timeframe': self.config.test_data.data_points_per_timeframe,
            'start_date': self.config.test_data.start_date,
            'end_date': self.config.test_data.end_date,
            'include_anomalies': self.config.test_data.include_anomalies,
            'anomaly_rate': self.config.test_data.anomaly_rate
        }
        
        generator = create_test_data_generator(generator_config)
        
        # 生成所有数据
        all_data = generator.generate_all_data()
        
        # 生成边界情况数据
        edge_cases = generator.generate_edge_cases()
        
        # 生成性能测试数据
        perf_data = generator.generate_performance_test_data(size_mb=10)
        
        logger.info(f"测试数据生成完成 - 符号: {len(all_data)}, 边界情况: {len(edge_cases)}")
    
    def register_test_suites(self, suite_names: Optional[List[str]] = None) -> None:
        """
        注册测试套件
        
        Args:
            suite_names: 要运行的测试套件名称列表，None表示运行所有套件
        """
        if not self.orchestrator:
            raise RuntimeError("测试编排器未初始化")
        
        # 注册示例测试套件
        if not suite_names or "example" in suite_names:
            example_suite = create_example_test_suite()
            self.orchestrator.register_test_suite(example_suite)
            logger.info("已注册示例测试套件")
        
        # 注册数据流集成测试套件
        if not suite_names or "data_flow" in suite_names:
            data_flow_suite = create_data_flow_integration_test_suite()
            self.orchestrator.register_test_suite(data_flow_suite)
            logger.info("已注册数据流集成测试套件")
        
        # 注册ML管道集成测试套件
        if not suite_names or "ml_pipeline" in suite_names:
            ml_pipeline_suite = create_ml_pipeline_integration_test_suite()
            self.orchestrator.register_test_suite(ml_pipeline_suite)
            logger.info("已注册ML管道集成测试套件")

        # 注册风险管理集成测试套件
        if not suite_names or "risk_management" in suite_names:
            # 创建风险管理测试套件
            import unittest
            risk_management_suite = unittest.TestLoader().loadTestsFromTestCase(RiskManagementIntegrationTestSuite)
            self.orchestrator.register_test_suite(risk_management_suite)
            logger.info("已注册风险管理集成测试套件")

        # 注册风险控制系统专项测试
        if not suite_names or "risk_control" in suite_names:
            risk_control_suite = unittest.TestLoader().loadTestsFromTestCase(RiskControlSystemTest)
            self.orchestrator.register_test_suite(risk_control_suite)
            logger.info("已注册风险控制系统专项测试套件")

        # 注册仓位管理专项测试
        if not suite_names or "position_management" in suite_names:
            position_mgmt_suite = unittest.TestLoader().loadTestsFromTestCase(PositionManagementTest)
            self.orchestrator.register_test_suite(position_mgmt_suite)
            logger.info("已注册仓位管理专项测试套件")

        # 注册实时风险监控测试
        if not suite_names or "risk_monitoring" in suite_names:
            risk_monitoring_suite = unittest.TestLoader().loadTestsFromTestCase(RiskMonitoringTest)
            self.orchestrator.register_test_suite(risk_monitoring_suite)
            logger.info("已注册实时风险监控测试套件")
    
    async def run_tests(self, suite_names: Optional[List[str]] = None) -> dict:
        """
        运行集成测试
        
        Args:
            suite_names: 要运行的测试套件名称列表
        
        Returns:
            dict: 测试结果和报告文件路径
        """
        logger.info("开始运行集成测试...")
        
        try:
            # 设置环境
            await self.setup_environment()
            
            # 生成测试数据
            await self.generate_test_data()
            
            # 注册测试套件
            self.register_test_suites(suite_names)
            
            # 添加进度回调
            def progress_callback(progress_info):
                logger.info(f"测试进度: {progress_info['progress_percentage']:.1f}% "
                          f"({progress_info['completed_tests']}/{progress_info['total_tests']})")
            
            self.orchestrator.add_progress_callback(progress_callback)
            
            # 执行测试
            test_results = await self.orchestrator.execute_all_tests()
            
            # 获取执行摘要
            execution_summary = self.orchestrator.get_execution_summary()
            
            # 生成报告
            report_files = self.reporter.generate_all_reports(
                test_results=test_results,
                execution_summary=execution_summary,
                report_name=f"integration_test_{self.environment}"
            )
            
            logger.info(f"集成测试完成 - 成功率: {execution_summary['success_rate']:.1f}%")
            logger.info(f"报告已生成: {report_files}")
            
            return {
                'test_results': test_results,
                'execution_summary': execution_summary,
                'report_files': report_files,
                'success': execution_summary['failed_tests'] == 0
            }
            
        except Exception as e:
            logger.error(f"集成测试执行失败: {str(e)}")
            raise
        
        finally:
            # 清理环境
            await self.teardown_environment()
    
    async def validate_environment(self) -> bool:
        """
        验证测试环境
        
        Returns:
            bool: 环境是否有效
        """
        logger.info("验证测试环境...")
        
        try:
            # 验证配置
            logger.info(f"配置验证: 环境={self.environment}")
            logger.info(f"数据库: {self.config.database.host}:{self.config.database.port}")
            logger.info(f"Kafka: {self.config.kafka.bootstrap_servers}")
            logger.info(f"API: {self.config.api.java_api_base_url}")
            
            # 验证Mock服务
            await setup_mock_environment()
            await teardown_mock_environment()
            
            logger.info("测试环境验证通过")
            return True
            
        except Exception as e:
            logger.error(f"测试环境验证失败: {str(e)}")
            return False


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Crypto ML Strategy 集成测试运行器")
    
    parser.add_argument(
        "--environment", "-e",
        choices=["dev", "staging", "prod-like"],
        default="dev",
        help="测试环境"
    )
    
    parser.add_argument(
        "--suites", "-s",
        nargs="*",
        help="要运行的测试套件名称"
    )
    
    parser.add_argument(
        "--validate-only", "-v",
        action="store_true",
        help="仅验证环境，不运行测试"
    )
    
    parser.add_argument(
        "--generate-data-only", "-g",
        action="store_true",
        help="仅生成测试数据"
    )
    
    parser.add_argument(
        "--log-level", "-l",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="日志级别"
    )
    
    args = parser.parse_args()
    
    # 配置日志
    logger.remove()
    logger.add(
        sys.stdout,
        level=args.log_level,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
    )
    
    # 创建测试运行器
    runner = IntegrationTestRunner(environment=args.environment)
    
    try:
        if args.validate_only:
            # 仅验证环境
            is_valid = await runner.validate_environment()
            sys.exit(0 if is_valid else 1)
        
        elif args.generate_data_only:
            # 仅生成测试数据
            await runner.generate_test_data()
            logger.info("测试数据生成完成")
            sys.exit(0)
        
        else:
            # 运行完整测试
            result = await runner.run_tests(suite_names=args.suites)
            
            # 输出结果摘要
            summary = result['execution_summary']
            print("\n" + "="*60)
            print("集成测试结果摘要")
            print("="*60)
            print(f"总测试数: {summary['total_tests']}")
            print(f"通过测试: {summary['passed_tests']}")
            print(f"失败测试: {summary['failed_tests']}")
            print(f"超时测试: {summary['timeout_tests']}")
            print(f"错误测试: {summary['error_tests']}")
            print(f"成功率: {summary['success_rate']:.1f}%")
            print(f"总耗时: {summary['total_duration']:.2f}秒")
            print("="*60)
            
            # 输出报告文件路径
            print("\n报告文件:")
            for format_type, file_path in result['report_files'].items():
                print(f"  {format_type.upper()}: {file_path}")
            
            # 根据测试结果设置退出码
            sys.exit(0 if result['success'] else 1)
    
    except KeyboardInterrupt:
        logger.warning("测试被用户中断")
        sys.exit(130)
    
    except Exception as e:
        logger.error(f"测试运行失败: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())