"""
Crypto ML Strategy - 性能监控日志核心组件

该模块实现了性能监控日志系统的核心功能，包括执行时间跟踪、
资源使用监控和性能指标收集。

主要功能：
- PerformanceLogger: 性能监控日志器
- ExecutionTimeTracker: 执行时间跟踪器
- ResourceUsageMonitor: 资源使用监控器
- PerformanceMetrics: 性能指标收集和计算

作者: Crypto ML Strategy Team
日期: 2024-01-20
版本: 1.0.0
"""

import asyncio
import functools
import gc
import threading
import time
from contextlib import contextmanager
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Union, Callable, NamedTuple
from loguru import logger
import psutil
import tracemalloc
from collections import defaultdict, deque


@dataclass
class PerformanceMetrics:
    """性能指标数据结构"""
    function_name: str
    execution_time_ms: float
    memory_usage_mb: float
    cpu_usage_percent: float
    timestamp: datetime
    thread_id: int
    process_id: int
    
    # 系统资源指标
    system_memory_percent: float = 0.0
    system_cpu_percent: float = 0.0
    io_read_bytes: int = 0
    io_write_bytes: int = 0
    network_sent_bytes: int = 0
    network_recv_bytes: int = 0
    
    # 应用指标
    gc_count: int = 0
    active_threads: int = 0
    open_files: int = 0
    
    # 自定义指标
    custom_metrics: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "function_name": self.function_name,
            "execution_time_ms": self.execution_time_ms,
            "memory_usage_mb": self.memory_usage_mb,
            "cpu_usage_percent": self.cpu_usage_percent,
            "timestamp": self.timestamp.isoformat(),
            "thread_id": self.thread_id,
            "process_id": self.process_id,
            "system_memory_percent": self.system_memory_percent,
            "system_cpu_percent": self.system_cpu_percent,
            "io_read_bytes": self.io_read_bytes,
            "io_write_bytes": self.io_write_bytes,
            "network_sent_bytes": self.network_sent_bytes,
            "network_recv_bytes": self.network_recv_bytes,
            "gc_count": self.gc_count,
            "active_threads": self.active_threads,
            "open_files": self.open_files,
            "custom_metrics": self.custom_metrics
        }


class ExecutionTimeTracker:
    """执行时间跟踪器"""
    
    def __init__(self):
        self._start_times: Dict[str, float] = {}
        self._execution_history: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        self._lock = threading.RLock()
    
    def start_timing(self, operation_id: str) -> None:
        """开始计时"""
        with self._lock:
            self._start_times[operation_id] = time.perf_counter()
    
    def end_timing(self, operation_id: str) -> Optional[float]:
        """结束计时并返回执行时间（毫秒）"""
        with self._lock:
            start_time = self._start_times.pop(operation_id, None)
            if start_time is None:
                return None
            
            execution_time_ms = (time.perf_counter() - start_time) * 1000
            self._execution_history[operation_id].append(execution_time_ms)
            
            return execution_time_ms
    
    @contextmanager
    def time_operation(self, operation_name: str):
        """上下文管理器形式的计时"""
        operation_id = f"{operation_name}_{threading.current_thread().ident}_{time.time()}"
        self.start_timing(operation_id)
        
        try:
            yield operation_id
        finally:
            execution_time = self.end_timing(operation_id)
            if execution_time is not None:
                logger.bind(
                    execution_time_ms=execution_time,
                    operation_name=operation_name
                ).info(f"Operation completed: {operation_name}")
    
    def get_statistics(self, operation_name: str) -> Dict[str, float]:
        """获取操作的统计信息"""
        with self._lock:
            history = self._execution_history.get(operation_name, deque())
            
            if not history:
                return {}
            
            times = list(history)
            return {
                "count": len(times),
                "avg_ms": sum(times) / len(times),
                "min_ms": min(times),
                "max_ms": max(times),
                "p50_ms": sorted(times)[len(times) // 2],
                "p95_ms": sorted(times)[int(len(times) * 0.95)],
                "p99_ms": sorted(times)[int(len(times) * 0.99)]
            }
    
    def get_all_statistics(self) -> Dict[str, Dict[str, float]]:
        """获取所有操作的统计信息"""
        with self._lock:
            return {
                operation: self.get_statistics(operation)
                for operation in self._execution_history.keys()
            }


class ResourceUsageMonitor:
    """资源使用监控器"""
    
    def __init__(self, sampling_interval: float = 1.0):
        self.sampling_interval = sampling_interval
        self._process = psutil.Process()
        self._monitoring = False
        self._monitor_thread: Optional[threading.Thread] = None
        self._resource_history: deque = deque(maxlen=3600)  # 保存1小时的数据
        self._lock = threading.RLock()
        
        # 初始化内存跟踪
        tracemalloc.start()
    
    def start_monitoring(self) -> None:
        """开始资源监控"""
        with self._lock:
            if self._monitoring:
                return
            
            self._monitoring = True
            self._monitor_thread = threading.Thread(
                target=self._monitor_loop,
                name="resource_monitor",
                daemon=True
            )
            self._monitor_thread.start()
            
            logger.info("Resource monitoring started")
    
    def stop_monitoring(self) -> None:
        """停止资源监控"""
        with self._lock:
            if not self._monitoring:
                return
            
            self._monitoring = False
            
            if self._monitor_thread and self._monitor_thread.is_alive():
                self._monitor_thread.join(timeout=5)
            
            logger.info("Resource monitoring stopped")
    
    def _monitor_loop(self) -> None:
        """监控循环"""
        while self._monitoring:
            try:
                metrics = self._collect_resource_metrics()
                
                with self._lock:
                    self._resource_history.append(metrics)
                
                time.sleep(self.sampling_interval)
                
            except Exception as e:
                logger.error(f"Error in resource monitoring: {e}")
                time.sleep(self.sampling_interval)
    
    def _collect_resource_metrics(self) -> Dict[str, Any]:
        """收集资源指标"""
        try:
            # 进程资源使用
            memory_info = self._process.memory_info()
            cpu_percent = self._process.cpu_percent()
            
            # 系统资源使用
            system_memory = psutil.virtual_memory()
            system_cpu = psutil.cpu_percent()
            
            # I/O统计
            io_counters = self._process.io_counters()
            
            # 网络统计（系统级别）
            net_io = psutil.net_io_counters()
            
            # 垃圾回收统计
            gc_stats = gc.get_stats()
            
            # 线程和文件句柄
            num_threads = self._process.num_threads()
            num_fds = self._process.num_fds() if hasattr(self._process, 'num_fds') else 0
            
            # 内存跟踪
            current_memory, peak_memory = tracemalloc.get_traced_memory()
            
            return {
                "timestamp": datetime.now(),
                "process_memory_mb": memory_info.rss / 1024 / 1024,
                "process_memory_vms_mb": memory_info.vms / 1024 / 1024,
                "process_cpu_percent": cpu_percent,
                "system_memory_percent": system_memory.percent,
                "system_cpu_percent": system_cpu,
                "io_read_bytes": io_counters.read_bytes,
                "io_write_bytes": io_counters.write_bytes,
                "network_sent_bytes": net_io.bytes_sent,
                "network_recv_bytes": net_io.bytes_recv,
                "gc_collections": sum(stat['collections'] for stat in gc_stats),
                "active_threads": num_threads,
                "open_files": num_fds,
                "traced_memory_mb": current_memory / 1024 / 1024,
                "peak_memory_mb": peak_memory / 1024 / 1024
            }
            
        except Exception as e:
            logger.error(f"Failed to collect resource metrics: {e}")
            return {"timestamp": datetime.now(), "error": str(e)}
    
    def get_current_metrics(self) -> Dict[str, Any]:
        """获取当前资源指标"""
        return self._collect_resource_metrics()
    
    def get_average_metrics(self, duration_minutes: int = 5) -> Dict[str, float]:
        """获取指定时间段内的平均指标"""
        with self._lock:
            if not self._resource_history:
                return {}
            
            cutoff_time = datetime.now() - timedelta(minutes=duration_minutes)
            recent_metrics = [
                m for m in self._resource_history
                if m.get("timestamp", datetime.min) >= cutoff_time
            ]
            
            if not recent_metrics:
                return {}
            
            # 计算平均值
            numeric_fields = [
                "process_memory_mb", "process_cpu_percent",
                "system_memory_percent", "system_cpu_percent",
                "active_threads", "open_files"
            ]
            
            averages = {}
            for field in numeric_fields:
                values = [m.get(field, 0) for m in recent_metrics if field in m]
                if values:
                    averages[f"avg_{field}"] = sum(values) / len(values)
            
            return averages
    
    def get_peak_metrics(self, duration_minutes: int = 5) -> Dict[str, float]:
        """获取指定时间段内的峰值指标"""
        with self._lock:
            if not self._resource_history:
                return {}
            
            cutoff_time = datetime.now() - timedelta(minutes=duration_minutes)
            recent_metrics = [
                m for m in self._resource_history
                if m.get("timestamp", datetime.min) >= cutoff_time
            ]
            
            if not recent_metrics:
                return {}
            
            # 计算峰值
            numeric_fields = [
                "process_memory_mb", "process_cpu_percent",
                "system_memory_percent", "system_cpu_percent",
                "active_threads", "open_files"
            ]
            
            peaks = {}
            for field in numeric_fields:
                values = [m.get(field, 0) for m in recent_metrics if field in m]
                if values:
                    peaks[f"peak_{field}"] = max(values)
            
            return peaks


class PerformanceLogger:
    """性能监控日志器"""
    
    def __init__(self, logger_name: str = "performance"):
        self.logger_name = logger_name
        self.time_tracker = ExecutionTimeTracker()
        self.resource_monitor = ResourceUsageMonitor()
        self._performance_logger = logger.bind(name=f"{logger_name}.performance")
        self._lock = threading.RLock()
        
        # 启动资源监控
        self.resource_monitor.start_monitoring()
    
    def monitor(self, func: Optional[Callable] = None, *, 
                include_args: bool = False, 
                include_result: bool = False,
                custom_metrics: Optional[Dict[str, Any]] = None):
        """性能监控装饰器"""
        def decorator(f: Callable) -> Callable:
            @functools.wraps(f)
            def wrapper(*args, **kwargs):
                return self._monitor_sync_function(
                    f, args, kwargs, include_args, include_result, custom_metrics
                )
            
            @functools.wraps(f)
            async def async_wrapper(*args, **kwargs):
                return await self._monitor_async_function(
                    f, args, kwargs, include_args, include_result, custom_metrics
                )
            
            if asyncio.iscoroutinefunction(f):
                return async_wrapper
            else:
                return wrapper
        
        if func is None:
            return decorator
        else:
            return decorator(func)
    
    def _monitor_sync_function(self, func: Callable, args: tuple, kwargs: dict,
                              include_args: bool, include_result: bool,
                              custom_metrics: Optional[Dict[str, Any]]) -> Any:
        """监控同步函数"""
        function_name = f"{func.__module__}.{func.__qualname__}"
        
        # 获取执行前的资源状态
        start_metrics = self.resource_monitor.get_current_metrics()
        
        # 开始计时
        with self.time_tracker.time_operation(function_name) as operation_id:
            try:
                result = func(*args, **kwargs)
                
                # 记录成功执行
                self._log_performance_metrics(
                    function_name, operation_id, start_metrics,
                    args if include_args else None,
                    result if include_result else None,
                    custom_metrics, None
                )
                
                return result
                
            except Exception as e:
                # 记录异常执行
                self._log_performance_metrics(
                    function_name, operation_id, start_metrics,
                    args if include_args else None,
                    None, custom_metrics, e
                )
                raise
    
    async def _monitor_async_function(self, func: Callable, args: tuple, kwargs: dict,
                                     include_args: bool, include_result: bool,
                                     custom_metrics: Optional[Dict[str, Any]]) -> Any:
        """监控异步函数"""
        function_name = f"{func.__module__}.{func.__qualname__}"
        
        # 获取执行前的资源状态
        start_metrics = self.resource_monitor.get_current_metrics()
        
        # 开始计时
        with self.time_tracker.time_operation(function_name) as operation_id:
            try:
                result = await func(*args, **kwargs)
                
                # 记录成功执行
                self._log_performance_metrics(
                    function_name, operation_id, start_metrics,
                    args if include_args else None,
                    result if include_result else None,
                    custom_metrics, None
                )
                
                return result
                
            except Exception as e:
                # 记录异常执行
                self._log_performance_metrics(
                    function_name, operation_id, start_metrics,
                    args if include_args else None,
                    None, custom_metrics, e
                )
                raise
    
    def _log_performance_metrics(self, function_name: str, operation_id: str,
                                start_metrics: Dict[str, Any],
                                args: Optional[tuple], result: Any,
                                custom_metrics: Optional[Dict[str, Any]],
                                exception: Optional[Exception]) -> None:
        """记录性能指标"""
        try:
            # 获取执行时间
            execution_time = self.time_tracker.end_timing(operation_id)
            if execution_time is None:
                execution_time = 0.0
            
            # 获取执行后的资源状态
            end_metrics = self.resource_monitor.get_current_metrics()
            
            # 构建性能指标
            metrics = PerformanceMetrics(
                function_name=function_name,
                execution_time_ms=execution_time,
                memory_usage_mb=end_metrics.get("process_memory_mb", 0),
                cpu_usage_percent=end_metrics.get("process_cpu_percent", 0),
                timestamp=datetime.now(),
                thread_id=threading.current_thread().ident,
                process_id=os.getpid() if 'os' in globals() else 0,
                system_memory_percent=end_metrics.get("system_memory_percent", 0),
                system_cpu_percent=end_metrics.get("system_cpu_percent", 0),
                io_read_bytes=end_metrics.get("io_read_bytes", 0),
                io_write_bytes=end_metrics.get("io_write_bytes", 0),
                network_sent_bytes=end_metrics.get("network_sent_bytes", 0),
                network_recv_bytes=end_metrics.get("network_recv_bytes", 0),
                gc_count=end_metrics.get("gc_collections", 0),
                active_threads=end_metrics.get("active_threads", 0),
                open_files=end_metrics.get("open_files", 0),
                custom_metrics=custom_metrics or {}
            )
            
            # 构建日志消息
            log_data = metrics.to_dict()
            
            if args is not None:
                log_data["args"] = str(args)[:500]  # 限制长度
            
            if result is not None:
                log_data["result"] = str(result)[:500]  # 限制长度
            
            if exception is not None:
                log_data["exception"] = {
                    "type": type(exception).__name__,
                    "message": str(exception),
                    "traceback": traceback.format_exc()
                }
            
            # 记录日志
            if exception:
                self._performance_logger.error(
                    f"Function {function_name} failed",
                    **log_data
                )
            else:
                self._performance_logger.info(
                    f"Function {function_name} completed",
                    **log_data
                )
                
        except Exception as e:
            logger.error(f"Failed to log performance metrics: {e}")
    
    def shutdown(self) -> None:
        """关闭性能监控"""
        try:
            self.resource_monitor.stop_monitoring()
            logger.info("Performance logger shutdown completed")
        except Exception as e:
            logger.error(f"Error during performance logger shutdown: {e}")


# 全局性能日志器实例
_global_performance_logger: Optional[PerformanceLogger] = None


def get_global_performance_logger() -> PerformanceLogger:
    """获取全局性能日志器实例"""
    global _global_performance_logger
    
    if _global_performance_logger is None:
        _global_performance_logger = PerformanceLogger()
    
    return _global_performance_logger


# 便捷的装饰器函数
def monitor_performance(func: Optional[Callable] = None, **kwargs):
    """便捷的性能监控装饰器"""
    performance_logger = get_global_performance_logger()
    return performance_logger.monitor(func, **kwargs)


# 模块级别的日志器
module_logger = logger.bind(name=__name__)