# 应用基础配置
spring:
  application:
    name: crypto-trading-system
  profiles:
    active: dev
  lifecycle:
    timeout-per-shutdown-phase: 30s
  threads:
    virtual:
      enabled: true
  main:
    allow-bean-definition-overriding: true
  # 数据库配置  
  datasource:
    url: ********************************************************************************************************************************************
    username: root
    password: root
    driver-class-name: com.mysql.cj.jdbc.Driver
    # 启用自动初始化数据库
    initialization-mode: always
    # 始终执行schema.sql
    schema-mode: always
    # 数据库平台
    platform: mysql
    # 初始化模式
    schema:
      - classpath:schema.sql
  # Kafka配置
  kafka:
    bootstrap-servers: ${KAFKA_BOOTSTRAP_SERVERS:localhost:29092}
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
      acks: all
      retries: 3
      batch-size: 16384
      buffer-memory: 33554432
      compression-type: lz4
      properties:
        enable.idempotence: true
    consumer:
      group-id: ${KAFKA_GROUP_ID:crypto-trading-group}
      auto-offset-reset: latest
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      max-poll-records: 500
      auto-commit-interval: 5000
      enable-auto-commit: true

# 服务器配置
server:
  port: 9527
  servlet:
    context-path: /api
  shutdown: graceful

# MyBatis-Plus配置
mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*.xml
  type-aliases-package: com.crypto.trading.*.entity
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false

# 应用自定义配置
app:
  name: 虚拟货币量化交易系统
  version: 1.0.0
  debug: true
  thread-pool:
    max-size: 200
    core-size: 20
    queue-capacity: 2000
    keep-alive-seconds: 60
  
  # Python策略配置
  python-strategy:
    enabled: true
    # 部署模式：local（本地进程）或service（远程服务）
    deploy-mode: service
    # 以下是本地进程模式的配置
    python-path: python
    strategy-path: D:/1_deep_bian/crypto-trading-system/crypto_ml_strategy/src/main.py
    working-dir: D:/1_deep_bian/crypto-trading-system/crypto_ml_strategy
    # 以下是远程服务模式的配置
    service-urls:
      - http://localhost:9530
    connect-timeout: 5000
    read-timeout: 30000
    health-check-interval: 60000

# 币安API配置
binance:
  api:
    key: ${BINANCE_API_KEY:87c1475c7a003d08400aa7051ee80fea1a105364a591ef2dbe2b80590eaeb379}
    secret: ${BINANCE_API_SECRET:d6aa03a75f489d4ce35bced30a1e80c5db0d70289938b14df4f7409a82e88b47}
    use-testnet: ${BINANCE_USE_TESTNET:true}
    connection-timeout: 15000
    read-timeout: 15000
    write-timeout: 15000
  rate-limit:
    weight-limit: 1200
    weight-interval: 1
    order-limit: 100
    order-interval: 10

# InfluxDB配置
influxdb:
  url: ${INFLUXDB_URL:http://localhost:8086}
  # 设置空token，允许在开发环境中使用无认证模式，或从环境变量获取有效token
  token: ${INFLUXDB_TOKEN:1mLmSyBqUYH5OKaSkFdybeS34Z6DyLlR3H3d802mmVvFVCiJ4GwOQzyT8QULaES96tBfPGI9ycDHkseYvPhGoA==}
  org: ${INFLUXDB_ORG:crypto}
  bucket: ${INFLUXDB_BUCKET:market_data}
  batch-size: 1000
  flush-interval: 5000
  connect-timeout: 30000
  read-timeout: 120000
  write-timeout: 60000
  # 添加重试配置
  retry:
    max-attempts: 5
    initial-interval: 1000
    max-interval: 10000
    multiplier: 2.0

# 市场数据配置
market:
  # 交易对列表，多个交易对用逗号分隔
  symbols: BTCUSDT,ETHUSDT,BNBUSDT
  
  # InfluxDB配置
  influxdb:
    # 确保与根级别的influxdb配置保持一致
    url: ${INFLUXDB_URL:http://localhost:8086}
    token: ${INFLUXDB_TOKEN:1mLmSyBqUYH5OKaSkFdybeS34Z6DyLlR3H3d802mmVvFVCiJ4GwOQzyT8QULaES96tBfPGI9ycDHkseYvPhGoA==}
    org: ${INFLUXDB_ORG:crypto}
    bucket: ${INFLUXDB_BUCKET:market_data}
    # 配置传输选项
    batch-size: 1000
    flush-interval: 5000
    connect-timeout: 30000
    read-timeout: 120000
    write-timeout: 60000
    # 自动创建桶和任务
    auto-create-buckets: true
    auto-create-tasks: true
    # 数据保留策略（单位：小时/天）
    retention:
      high-precision: 17520    # 2年 (24小时 * 365天 * 2)
      medium-precision: 730    # 2年 (365天 * 2)
      low-precision: 1825      # 5年 (365天 * 5)
  
  # K线配置
  kline:
    enabled: true
    # K线间隔列表，多个间隔用逗号分隔
    intervals: 1m,5m,15m,30m,1h,4h,1d
    # Kafka主题
    topic: kline.data
  
  # 深度配置
  depth:
    enabled: true
    # 深度级别（5, 10, 20）- 优化：减少到5级，降低数据量
    levels: 5
    # 更新速度（100ms, 250ms, 500ms）- 优化：改为100ms，提高实时性
    speed: 100
    # Kafka主题
    topic: depth.data
  
  # 交易配置
  trade:
    enabled: true
    # Kafka主题
    topic: trade.data
  
  # WebSocket配置
  websocket:
    # 自动重连间隔（毫秒）
    reconnect-interval: 5000
    # 连接超时时间（毫秒）
    connect-timeout: 15000

# 日志配置
logging:
  level:
    root: debug
    com.crypto.trading: debug
    # 深度数据处理相关的详细日志
    com.crypto.trading.market.listener.DepthDataListener: trace
    com.crypto.trading.market.processor.DepthDataProcessor: trace
    com.crypto.trading.market.repository: debug
    com.crypto.trading.market.converter: debug
    com.crypto.trading.sdk.websocket: debug
    # 数据库相关日志
    org.springframework.jdbc: debug
    com.influxdb: debug
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS}[%d{SSS}] | %-5level | %thread | %-40.40logger{39} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS}[%d{SSS}] | %-5level | %thread | %-40.40logger{39} - %msg%n"
  file:
    path: ./logs
    name: crypto-trading-system.log
    max-size: 100MB
    max-history: 30