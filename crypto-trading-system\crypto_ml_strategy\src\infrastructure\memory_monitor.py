#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
内存监控服务模块

提供实时内存使用监控、内存泄漏检测和内存优化建议。
"""

import gc
import psutil
import threading
import time
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass
from loguru import logger


@dataclass
class MemorySnapshot:
    """内存快照数据类"""
    timestamp: float
    total_memory: int  # 总内存 (bytes)
    available_memory: int  # 可用内存 (bytes)
    used_memory: int  # 已用内存 (bytes)
    memory_percent: float  # 内存使用百分比
    process_memory: int  # 进程内存 (bytes)
    process_memory_percent: float  # 进程内存百分比
    gc_objects: int  # GC对象数量
    gc_collections: Dict[int, int]  # GC收集次数


class MemoryMonitor:
    """
    内存监控器
    
    提供实时内存监控、内存泄漏检测和性能分析功能。
    """
    
    def __init__(self, 
                 monitoring_interval: float = 30.0,
                 memory_threshold: float = 80.0,
                 leak_detection_window: int = 10):
        """
        初始化内存监控器
        
        Args:
            monitoring_interval: 监控间隔（秒）
            memory_threshold: 内存使用阈值（百分比）
            leak_detection_window: 内存泄漏检测窗口大小
        """
        self.monitoring_interval = monitoring_interval
        self.memory_threshold = memory_threshold
        self.leak_detection_window = leak_detection_window
        
        self.process = psutil.Process()
        self.snapshots: List[MemorySnapshot] = []
        self.alert_callbacks: List[Callable[[Dict[str, Any]], None]] = []
        
        self._monitoring = False
        self._monitor_thread: Optional[threading.Thread] = None
        self._lock = threading.Lock()
        
        logger.info(f"内存监控器初始化完成 - 间隔: {monitoring_interval}s, 阈值: {memory_threshold}%")
    
    def start_monitoring(self) -> None:
        """启动内存监控"""
        if self._monitoring:
            logger.warning("内存监控已经在运行")
            return
        
        self._monitoring = True
        self._monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self._monitor_thread.start()
        
        logger.info("内存监控已启动")
    
    def stop_monitoring(self) -> None:
        """停止内存监控"""
        if not self._monitoring:
            return
        
        self._monitoring = False
        if self._monitor_thread:
            self._monitor_thread.join(timeout=5.0)
        
        logger.info("内存监控已停止")
    
    def _monitor_loop(self) -> None:
        """监控循环"""
        while self._monitoring:
            try:
                snapshot = self._take_snapshot()
                
                with self._lock:
                    self.snapshots.append(snapshot)
                    
                    # 保持快照数量在合理范围内
                    if len(self.snapshots) > 1000:
                        self.snapshots = self.snapshots[-500:]
                
                # 检查内存使用情况
                self._check_memory_usage(snapshot)
                
                # 检查内存泄漏
                if len(self.snapshots) >= self.leak_detection_window:
                    self._check_memory_leak()
                
                time.sleep(self.monitoring_interval)
                
            except Exception as e:
                logger.error(f"内存监控循环异常: {e}")
                time.sleep(self.monitoring_interval)
    
    def _take_snapshot(self) -> MemorySnapshot:
        """获取内存快照"""
        # 系统内存信息
        memory_info = psutil.virtual_memory()
        
        # 进程内存信息
        process_memory_info = self.process.memory_info()
        process_memory_percent = self.process.memory_percent()
        
        # GC信息
        gc_objects = len(gc.get_objects())
        gc_collections = {i: gc.get_count()[i] for i in range(3)}
        
        return MemorySnapshot(
            timestamp=time.time(),
            total_memory=memory_info.total,
            available_memory=memory_info.available,
            used_memory=memory_info.used,
            memory_percent=memory_info.percent,
            process_memory=process_memory_info.rss,
            process_memory_percent=process_memory_percent,
            gc_objects=gc_objects,
            gc_collections=gc_collections
        )
    
    def _check_memory_usage(self, snapshot: MemorySnapshot) -> None:
        """检查内存使用情况"""
        if snapshot.memory_percent > self.memory_threshold:
            alert = {
                "type": "high_memory_usage",
                "severity": "warning",
                "message": f"系统内存使用率过高: {snapshot.memory_percent:.1f}%",
                "details": {
                    "system_memory_percent": snapshot.memory_percent,
                    "process_memory_mb": snapshot.process_memory / 1024 / 1024,
                    "process_memory_percent": snapshot.process_memory_percent,
                    "timestamp": snapshot.timestamp
                }
            }
            
            self._trigger_alert(alert)
        
        # 检查进程内存使用
        process_memory_mb = snapshot.process_memory / 1024 / 1024
        if process_memory_mb > 1000:  # 超过1GB
            alert = {
                "type": "high_process_memory",
                "severity": "warning",
                "message": f"进程内存使用过高: {process_memory_mb:.1f}MB",
                "details": {
                    "process_memory_mb": process_memory_mb,
                    "process_memory_percent": snapshot.process_memory_percent,
                    "gc_objects": snapshot.gc_objects,
                    "timestamp": snapshot.timestamp
                }
            }
            
            self._trigger_alert(alert)
    
    def _check_memory_leak(self) -> None:
        """检查内存泄漏"""
        with self._lock:
            if len(self.snapshots) < self.leak_detection_window:
                return
            
            recent_snapshots = self.snapshots[-self.leak_detection_window:]
            
            # 计算内存增长趋势
            memory_growth = []
            for i in range(1, len(recent_snapshots)):
                growth = recent_snapshots[i].process_memory - recent_snapshots[i-1].process_memory
                memory_growth.append(growth)
            
            # 检查是否持续增长
            if len(memory_growth) >= 5:
                positive_growth_count = sum(1 for growth in memory_growth[-5:] if growth > 0)
                
                if positive_growth_count >= 4:  # 5次中有4次增长
                    total_growth = sum(memory_growth[-5:])
                    growth_mb = total_growth / 1024 / 1024
                    
                    if growth_mb > 50:  # 增长超过50MB
                        alert = {
                            "type": "memory_leak_detected",
                            "severity": "error",
                            "message": f"检测到可能的内存泄漏: 最近增长 {growth_mb:.1f}MB",
                            "details": {
                                "growth_mb": growth_mb,
                                "growth_pattern": memory_growth[-5:],
                                "current_memory_mb": recent_snapshots[-1].process_memory / 1024 / 1024,
                                "gc_objects": recent_snapshots[-1].gc_objects,
                                "timestamp": recent_snapshots[-1].timestamp
                            }
                        }
                        
                        self._trigger_alert(alert)
                        
                        # 触发垃圾回收
                        self._force_garbage_collection()
    
    def _force_garbage_collection(self) -> None:
        """强制垃圾回收"""
        try:
            logger.info("触发强制垃圾回收...")
            
            before_objects = len(gc.get_objects())
            collected = gc.collect()
            after_objects = len(gc.get_objects())
            
            logger.info(f"垃圾回收完成 - 回收对象: {collected}, "
                       f"对象数量: {before_objects} -> {after_objects}")
            
        except Exception as e:
            logger.error(f"强制垃圾回收失败: {e}")
    
    def _trigger_alert(self, alert: Dict[str, Any]) -> None:
        """触发内存告警"""
        logger.warning(f"内存告警: {alert['message']}")
        
        for callback in self.alert_callbacks:
            try:
                callback(alert)
            except Exception as e:
                logger.error(f"内存告警回调执行失败: {e}")
    
    def add_alert_callback(self, callback: Callable[[Dict[str, Any]], None]) -> None:
        """添加告警回调"""
        self.alert_callbacks.append(callback)
    
    def get_current_status(self) -> Dict[str, Any]:
        """获取当前内存状态"""
        snapshot = self._take_snapshot()
        
        return {
            "system_memory": {
                "total_gb": snapshot.total_memory / 1024 / 1024 / 1024,
                "available_gb": snapshot.available_memory / 1024 / 1024 / 1024,
                "used_percent": snapshot.memory_percent
            },
            "process_memory": {
                "used_mb": snapshot.process_memory / 1024 / 1024,
                "used_percent": snapshot.process_memory_percent
            },
            "gc_info": {
                "objects_count": snapshot.gc_objects,
                "collections": snapshot.gc_collections
            },
            "monitoring": {
                "is_monitoring": self._monitoring,
                "snapshots_count": len(self.snapshots),
                "threshold_percent": self.memory_threshold
            }
        }
    
    def get_memory_trend(self, hours: int = 1) -> Dict[str, Any]:
        """获取内存使用趋势"""
        cutoff_time = time.time() - (hours * 3600)
        
        with self._lock:
            recent_snapshots = [
                s for s in self.snapshots 
                if s.timestamp >= cutoff_time
            ]
        
        if not recent_snapshots:
            return {"error": "没有足够的历史数据"}
        
        # 计算趋势
        memory_values = [s.process_memory / 1024 / 1024 for s in recent_snapshots]
        timestamps = [s.timestamp for s in recent_snapshots]
        
        return {
            "time_range_hours": hours,
            "data_points": len(recent_snapshots),
            "memory_trend": {
                "min_mb": min(memory_values),
                "max_mb": max(memory_values),
                "avg_mb": sum(memory_values) / len(memory_values),
                "current_mb": memory_values[-1] if memory_values else 0
            },
            "growth_analysis": {
                "total_growth_mb": memory_values[-1] - memory_values[0] if len(memory_values) >= 2 else 0,
                "growth_rate_mb_per_hour": (memory_values[-1] - memory_values[0]) / hours if len(memory_values) >= 2 else 0
            }
        }
    
    def optimize_memory(self) -> Dict[str, Any]:
        """内存优化"""
        try:
            before_snapshot = self._take_snapshot()
            
            # 1. 强制垃圾回收
            self._force_garbage_collection()
            
            # 2. 等待一段时间让GC完成
            time.sleep(1)
            
            after_snapshot = self._take_snapshot()
            
            # 计算优化效果
            memory_freed = before_snapshot.process_memory - after_snapshot.process_memory
            memory_freed_mb = memory_freed / 1024 / 1024
            
            result = {
                "optimization_completed": True,
                "memory_freed_mb": memory_freed_mb,
                "before": {
                    "process_memory_mb": before_snapshot.process_memory / 1024 / 1024,
                    "gc_objects": before_snapshot.gc_objects
                },
                "after": {
                    "process_memory_mb": after_snapshot.process_memory / 1024 / 1024,
                    "gc_objects": after_snapshot.gc_objects
                }
            }
            
            logger.info(f"内存优化完成 - 释放内存: {memory_freed_mb:.1f}MB")
            return result
            
        except Exception as e:
            logger.error(f"内存优化失败: {e}")
            return {
                "optimization_completed": False,
                "error": str(e)
            }