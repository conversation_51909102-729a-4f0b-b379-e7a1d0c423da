package com.crypto.trading.common.exception;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * APILimitExceededException单元测试类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
class APILimitExceededExceptionTest {

    /**
     * 测试使用错误码枚举、限流类型和重试时间构造异常
     */
    @Test
    void testConstructWithErrorCodeLimitTypeAndRetryAfter() {
        // 测试数据
        String limitType = "WEIGHT";
        long retryAfterMs = 5000;

        // 构造异常
        APILimitExceededException exception = new APILimitExceededException(
                ErrorCode.BINANCE_API_WEIGHT_LIMIT, limitType, retryAfterMs);

        // 验证属性
        assertEquals(String.valueOf(ErrorCode.BINANCE_API_WEIGHT_LIMIT.getCode()), exception.getErrorCode());
        assertEquals(ErrorCode.BINANCE_API_WEIGHT_LIMIT.getMessage(), exception.getMessage());
        assertEquals(limitType, exception.getLimitType());
        assertEquals(retryAfterMs, exception.getRetryAfterMs());
    }

    /**
     * 测试使用错误码枚举、自定义消息、限流类型和重试时间构造异常
     */
    @Test
    void testConstructWithErrorCodeMessageLimitTypeAndRetryAfter() {
        // 测试数据
        String customMessage = "API权重限制超过，请稍后重试";
        String limitType = "WEIGHT";
        long retryAfterMs = 5000;

        // 构造异常
        APILimitExceededException exception = new APILimitExceededException(
                ErrorCode.BINANCE_API_WEIGHT_LIMIT, customMessage, limitType, retryAfterMs);

        // 验证属性
        assertEquals(String.valueOf(ErrorCode.BINANCE_API_WEIGHT_LIMIT.getCode()), exception.getErrorCode());
        assertEquals(customMessage, exception.getMessage());
        assertEquals(limitType, exception.getLimitType());
        assertEquals(retryAfterMs, exception.getRetryAfterMs());
    }

    /**
     * 测试异常的业务使用场景
     */
    @Test
    void testBusinessUsageScenario() {
        // 模拟限流场景
        boolean isWeightLimitExceeded = true;
        long retryAfterMs = 3000;

        // 处理限流
        APILimitExceededException exception = null;
        try {
            if (isWeightLimitExceeded) {
                throw new APILimitExceededException(
                        ErrorCode.BINANCE_API_WEIGHT_LIMIT,
                        "API权重限制超过，请等待 " + retryAfterMs + " 毫秒后重试",
                        "WEIGHT",
                        retryAfterMs);
            }
        } catch (APILimitExceededException e) {
            exception = e;
        }

        // 验证异常处理
        assertNotNull(exception);
        assertEquals(String.valueOf(ErrorCode.BINANCE_API_WEIGHT_LIMIT.getCode()), exception.getErrorCode());
        assertEquals("WEIGHT", exception.getLimitType());
        assertEquals(retryAfterMs, exception.getRetryAfterMs());
        assertTrue(exception.getMessage().contains("3000"));
    }

    /**
     * 测试异常继承关系
     */
    @Test
    void testExceptionHierarchy() {
        // 创建异常实例
        APILimitExceededException apiLimitException = new APILimitExceededException(
                ErrorCode.BINANCE_API_WEIGHT_LIMIT, "WEIGHT", 1000);

        // 验证继承关系
        assertTrue(apiLimitException instanceof BusinessException);
        assertTrue(apiLimitException instanceof RuntimeException);
    }
} 