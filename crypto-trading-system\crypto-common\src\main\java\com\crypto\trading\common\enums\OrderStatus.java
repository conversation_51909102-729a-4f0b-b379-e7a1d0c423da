package com.crypto.trading.common.enums;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * 订单状态枚举
 * <p>
 * 定义订单的各种状态
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public enum OrderStatus {

    /**
     * 新建
     */
    NEW("NEW", "新建"),

    /**
     * 部分成交
     */
    PARTIALLY_FILLED("PARTIALLY_FILLED", "部分成交"),

    /**
     * 全部成交
     */
    FILLED("FILLED", "全部成交"),

    /**
     * 已取消
     */
    CANCELED("CANCELED", "已取消"),

    /**
     * 已拒绝
     */
    REJECTED("REJECTED", "已拒绝"),

    /**
     * 已过期
     */
    EXPIRED("EXPIRED", "已过期"),

    /**
     * 待处理
     */
    PENDING("PENDING", "待处理");

    /**
     * 状态码
     */
    private final String code;

    /**
     * 状态描述
     */
    private final String desc;

    /**
     * 最终状态集合
     */
    private static final Set<String> FINAL_STATUSES = new HashSet<>(
            Arrays.asList(FILLED.code, CANCELED.code, REJECTED.code, EXPIRED.code)
    );

    /**
     * 构造函数
     *
     * @param code 状态码
     * @param desc 状态描述
     */
    OrderStatus(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 获取状态码
     *
     * @return 状态码
     */
    public String getCode() {
        return code;
    }

    /**
     * 获取状态描述
     *
     * @return 状态描述
     */
    public String getDesc() {
        return desc;
    }

    /**
     * 根据状态码获取枚举
     *
     * @param code 状态码
     * @return 枚举实例
     */
    public static OrderStatus fromCode(String code) {
        for (OrderStatus status : OrderStatus.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("未知的订单状态码: " + code);
    }

    /**
     * 根据状态字符串获取枚举
     *
     * @param status 状态字符串
     * @return 枚举实例
     */
    public static OrderStatus fromString(String status) {
        try {
            return OrderStatus.valueOf(status);
        } catch (IllegalArgumentException e) {
            // 尝试使用code匹配
            return fromCode(status);
        }
    }

    /**
     * 判断是否为最终状态
     *
     * @param status 状态码
     * @return 是否为最终状态
     */
    public static boolean isFinalStatus(String status) {
        return FINAL_STATUSES.contains(status);
    }

    /**
     * 判断是否为成功状态
     *
     * @param status 状态码
     * @return 是否为成功状态
     */
    public static boolean isSuccessStatus(String status) {
        return FILLED.code.equals(status);
    }

    /**
     * 判断是否为失败状态
     *
     * @param status 状态码
     * @return 是否为失败状态
     */
    public static boolean isFailureStatus(String status) {
        return CANCELED.code.equals(status) || REJECTED.code.equals(status) || EXPIRED.code.equals(status);
    }
}