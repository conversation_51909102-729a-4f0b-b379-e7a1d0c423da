package com.crypto.trading.sdk.converter;

import com.crypto.trading.common.dto.market.KlineDataDTO;
import com.crypto.trading.common.util.JsonUtil;
import com.crypto.trading.sdk.response.model.KlineData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.HashMap;
import java.util.Map;

/**
 * 模型转换器实现类
 * <p>
 * 提供API模型和应用模型之间的转换功能
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
public class ModelConverterImpl implements ModelConverter {

    private static final Logger log = LoggerFactory.getLogger(ModelConverterImpl.class);

    @Override
    public <A, D> D toDto(A apiModel, Class<D> dtoClass) {
        // 特殊处理KlineData到KlineDataDTO的转换
        if (apiModel instanceof KlineData && dtoClass.equals(KlineDataDTO.class)) {
            return (D) convertKlineDataToDTO((KlineData) apiModel);
        }
        return JsonUtil.convertObject(apiModel, dtoClass);
    }

    @Override
    public <A, D> A toApiModel(D dto, Class<A> apiModelClass) {
        // 特殊处理KlineDataDTO到KlineData的转换
        if (dto instanceof KlineDataDTO && apiModelClass.equals(KlineData.class)) {
            return (A) convertDTOToKlineData((KlineDataDTO) dto);
        }
        return JsonUtil.convertObject(dto, apiModelClass);
    }
    
    /**
     * 将KlineData转换为KlineDataDTO
     *
     * @param klineData K线数据
     * @return K线数据DTO
     */
    private KlineDataDTO convertKlineDataToDTO(KlineData klineData) {
        KlineDataDTO dto = new KlineDataDTO();
        dto.setSymbol(klineData.getSymbol());
        dto.setInterval(klineData.getInterval());
        
        // 将时间戳转换为LocalDateTime
        if (klineData.getOpenTime() != null) {
            dto.setOpenTime(LocalDateTime.ofInstant(
                    Instant.ofEpochMilli(klineData.getOpenTime()), 
                    ZoneId.systemDefault()));
        }
        
        if (klineData.getCloseTime() != null) {
            dto.setCloseTime(LocalDateTime.ofInstant(
                    Instant.ofEpochMilli(klineData.getCloseTime()), 
                    ZoneId.systemDefault()));
        }
        
        dto.setOpen(klineData.getOpen());
        dto.setHigh(klineData.getHigh());
        dto.setLow(klineData.getLow());
        dto.setClose(klineData.getClose());
        dto.setVolume(klineData.getVolume());
        dto.setQuoteAssetVolume(klineData.getQuoteAssetVolume());
        dto.setNumberOfTrades(klineData.getNumberOfTrades());
        dto.setTakerBuyBaseAssetVolume(klineData.getTakerBuyBaseAssetVolume());
        dto.setTakerBuyQuoteAssetVolume(klineData.getTakerBuyQuoteAssetVolume());
        
        return dto;
    }
    
    /**
     * 将KlineDataDTO转换为KlineData
     *
     * @param dto K线数据DTO
     * @return K线数据
     */
    private KlineData convertDTOToKlineData(KlineDataDTO dto) {
        KlineData klineData = new KlineData();
        klineData.setSymbol(dto.getSymbol());
        klineData.setInterval(dto.getInterval());
        
        // 将LocalDateTime转换为时间戳
        if (dto.getOpenTime() != null) {
            klineData.setOpenTime(dto.getOpenTime()
                    .atZone(ZoneId.systemDefault())
                    .toInstant()
                    .toEpochMilli());
        }
        
        if (dto.getCloseTime() != null) {
            klineData.setCloseTime(dto.getCloseTime()
                    .atZone(ZoneId.systemDefault())
                    .toInstant()
                    .toEpochMilli());
        }
        
        klineData.setOpen(dto.getOpen());
        klineData.setHigh(dto.getHigh());
        klineData.setLow(dto.getLow());
        klineData.setClose(dto.getClose());
        klineData.setVolume(dto.getVolume());
        klineData.setQuoteAssetVolume(dto.getQuoteAssetVolume());
        klineData.setNumberOfTrades(dto.getNumberOfTrades());
        klineData.setTakerBuyBaseAssetVolume(dto.getTakerBuyBaseAssetVolume());
        klineData.setTakerBuyQuoteAssetVolume(dto.getTakerBuyQuoteAssetVolume());
        
        return klineData;
    }
} 