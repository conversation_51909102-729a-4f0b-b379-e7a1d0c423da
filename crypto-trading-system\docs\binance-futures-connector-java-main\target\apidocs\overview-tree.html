<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (21) on Fri Apr 04 16:06:08 CST 2025 -->
<title>类分层结构 (io.github.binance:binance-futures-connector-java 3.0.5 API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-04-04">
<meta name="description" content="class tree">
<meta name="generator" content="javadoc/TreeWriter">
<link rel="stylesheet" type="text/css" href="stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="script.js"></script>
<script type="text/javascript" src="script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="script-dir/jquery-ui.min.js"></script>
</head>
<body class="tree-page">
<script type="text/javascript">var pathtoroot = "./";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="切换导航链接"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="index.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li class="nav-bar-cell1-rev">树</li>
<li><a href="index-all.html">索引</a></li>
<li><a href="help-doc.html#tree">帮助</a></li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list"></div>
<div class="nav-list-search"><a href="search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="搜索">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 class="title">所有程序包的分层结构</h1>
</div>
<span class="package-hierarchy-label">程序包分层结构:</span>
<ul class="horizontal contents-list">
<li><a href="com/binance/connector/futures/client/package-tree.html">com.binance.connector.futures.client</a>, </li>
<li><a href="com/binance/connector/futures/client/impl/package-tree.html">com.binance.connector.futures.client.impl</a>, </li>
<li><a href="com/binance/connector/futures/client/impl/cm_futures/package-tree.html">com.binance.connector.futures.client.impl.cm_futures</a>, </li>
<li><a href="com/binance/connector/futures/client/impl/futures/package-tree.html">com.binance.connector.futures.client.impl.futures</a>, </li>
<li><a href="com/binance/connector/futures/client/impl/um_futures/package-tree.html">com.binance.connector.futures.client.impl.um_futures</a></li>
</ul>
<section class="hierarchy">
<h2 title="类分层结构">类分层结构</h2>
<ul>
<li class="circle">java.lang.<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html" class="type-name-link external-link" title="java.lang中的类或接口">Object</a>
<ul>
<li class="circle">com.binance.connector.futures.client.impl.futures.<a href="com/binance/connector/futures/client/impl/futures/Account.html" class="type-name-link" title="com.binance.connector.futures.client.impl.futures中的类">Account</a>
<ul>
<li class="circle">com.binance.connector.futures.client.impl.cm_futures.<a href="com/binance/connector/futures/client/impl/cm_futures/CMAccount.html" class="type-name-link" title="com.binance.connector.futures.client.impl.cm_futures中的类">CMAccount</a></li>
<li class="circle">com.binance.connector.futures.client.impl.um_futures.<a href="com/binance/connector/futures/client/impl/um_futures/UMAccount.html" class="type-name-link" title="com.binance.connector.futures.client.impl.um_futures中的类">UMAccount</a></li>
</ul>
</li>
<li class="circle">com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/FuturesClientImpl.html" class="type-name-link" title="com.binance.connector.futures.client.impl中的类">FuturesClientImpl</a> (implements com.binance.connector.futures.client.<a href="com/binance/connector/futures/client/FuturesClient.html" title="com.binance.connector.futures.client中的接口">FuturesClient</a>)
<ul>
<li class="circle">com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/CMFuturesClientImpl.html" class="type-name-link" title="com.binance.connector.futures.client.impl中的类">CMFuturesClientImpl</a></li>
<li class="circle">com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/UMFuturesClientImpl.html" class="type-name-link" title="com.binance.connector.futures.client.impl中的类">UMFuturesClientImpl</a></li>
</ul>
</li>
<li class="circle">com.binance.connector.futures.client.impl.futures.<a href="com/binance/connector/futures/client/impl/futures/Market.html" class="type-name-link" title="com.binance.connector.futures.client.impl.futures中的类">Market</a>
<ul>
<li class="circle">com.binance.connector.futures.client.impl.cm_futures.<a href="com/binance/connector/futures/client/impl/cm_futures/CMMarket.html" class="type-name-link" title="com.binance.connector.futures.client.impl.cm_futures中的类">CMMarket</a></li>
<li class="circle">com.binance.connector.futures.client.impl.um_futures.<a href="com/binance/connector/futures/client/impl/um_futures/UMMarket.html" class="type-name-link" title="com.binance.connector.futures.client.impl.um_futures中的类">UMMarket</a></li>
</ul>
</li>
<li class="circle">com.binance.connector.futures.client.impl.futures.<a href="com/binance/connector/futures/client/impl/futures/PortfolioMargin.html" class="type-name-link" title="com.binance.connector.futures.client.impl.futures中的类">PortfolioMargin</a>
<ul>
<li class="circle">com.binance.connector.futures.client.impl.cm_futures.<a href="com/binance/connector/futures/client/impl/cm_futures/CMPortfolioMargin.html" class="type-name-link" title="com.binance.connector.futures.client.impl.cm_futures中的类">CMPortfolioMargin</a></li>
</ul>
</li>
<li class="circle">com.binance.connector.futures.client.impl.futures.<a href="com/binance/connector/futures/client/impl/futures/UserData.html" class="type-name-link" title="com.binance.connector.futures.client.impl.futures中的类">UserData</a>
<ul>
<li class="circle">com.binance.connector.futures.client.impl.cm_futures.<a href="com/binance/connector/futures/client/impl/cm_futures/CMUserData.html" class="type-name-link" title="com.binance.connector.futures.client.impl.cm_futures中的类">CMUserData</a></li>
<li class="circle">com.binance.connector.futures.client.impl.um_futures.<a href="com/binance/connector/futures/client/impl/um_futures/UMUserData.html" class="type-name-link" title="com.binance.connector.futures.client.impl.um_futures中的类">UMUserData</a></li>
</ul>
</li>
<li class="circle">com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html" class="type-name-link" title="com.binance.connector.futures.client.impl中的类">WebsocketClientImpl</a> (implements com.binance.connector.futures.client.<a href="com/binance/connector/futures/client/WebsocketClient.html" title="com.binance.connector.futures.client中的接口">WebsocketClient</a>)
<ul>
<li class="circle">com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/CMWebsocketClientImpl.html" class="type-name-link" title="com.binance.connector.futures.client.impl中的类">CMWebsocketClientImpl</a></li>
<li class="circle">com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/UMWebsocketClientImpl.html" class="type-name-link" title="com.binance.connector.futures.client.impl中的类">UMWebsocketClientImpl</a></li>
</ul>
</li>
</ul>
</li>
</ul>
</section>
<section class="hierarchy">
<h2 title="接口分层结构">接口分层结构</h2>
<ul>
<li class="circle">com.binance.connector.futures.client.<a href="com/binance/connector/futures/client/FuturesClient.html" class="type-name-link" title="com.binance.connector.futures.client中的接口">FuturesClient</a></li>
<li class="circle">com.binance.connector.futures.client.<a href="com/binance/connector/futures/client/WebsocketClient.html" class="type-name-link" title="com.binance.connector.futures.client中的接口">WebsocketClient</a></li>
</ul>
</section>
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &#169; 2025. All rights reserved.</small></p>
</footer>
</div>
</div>
</body>
</html>
