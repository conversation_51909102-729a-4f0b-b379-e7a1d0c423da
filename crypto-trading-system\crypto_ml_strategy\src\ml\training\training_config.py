"""
训练配置模块

提供模型训练和预测相关的配置参数、数据结构和常量定义。
支持灵活的训练参数配置和模型选择。

Author: Crypto ML Strategy Team
Date: 2024-12-19
"""

from dataclasses import dataclass, field
from typing import Dict, List, Optional, Union, Any, Tuple
from enum import Enum
import logging
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)


class ModelType(Enum):
    """模型类型枚举"""
    LINEAR_REGRESSION = "linear_regression"
    LOGISTIC_REGRESSION = "logistic_regression"
    RANDOM_FOREST = "random_forest"
    XGBOOST = "xgboost"
    LIGHTGBM = "lightgbm"
    CATBOOST = "catboost"
    SVM = "svm"
    MLP = "mlp"
    LSTM = "lstm"
    GRU = "gru"
    TRANSFORMER = "transformer"
    ENSEMBLE = "ensemble"
    DEEPSEEK_DISTILLED = "deepseek_distilled"


class DataSource(Enum):
    """数据源类型枚举"""
    JAVA_API = "java_api"
    DATABASE = "database"
    FILE = "file"
    KAFKA = "kafka"
    MOCK = "mock"


class FeatureType(Enum):
    """特征类型枚举"""
    TECHNICAL_INDICATORS = "technical_indicators"
    PRICE_FEATURES = "price_features"
    VOLUME_FEATURES = "volume_features"
    TIME_FEATURES = "time_features"
    MULTI_TIMEFRAME = "multi_timeframe"
    CUSTOM = "custom"


class TrainingMode(Enum):
    """训练模式枚举"""
    BATCH = "batch"
    ONLINE = "online"
    INCREMENTAL = "incremental"
    TRANSFER = "transfer"
    ENSEMBLE = "ensemble"


@dataclass
class DataSourceConfig:
    """数据源配置"""
    source_type: DataSource
    connection_params: Dict[str, Any] = field(default_factory=dict)
    api_endpoint: Optional[str] = None
    database_url: Optional[str] = None
    file_path: Optional[str] = None
    kafka_config: Optional[Dict[str, Any]] = None
    timeout: int = 30
    retry_attempts: int = 3
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'source_type': self.source_type.value,
            'connection_params': self.connection_params,
            'api_endpoint': self.api_endpoint,
            'database_url': self.database_url,
            'file_path': self.file_path,
            'kafka_config': self.kafka_config,
            'timeout': self.timeout,
            'retry_attempts': self.retry_attempts
        }


@dataclass
class FeatureConfig:
    """特征配置"""
    feature_types: List[FeatureType] = field(default_factory=lambda: [FeatureType.TECHNICAL_INDICATORS])
    technical_indicators: List[str] = field(default_factory=lambda: ['sma', 'ema', 'rsi', 'macd'])
    timeframes: List[str] = field(default_factory=lambda: ['1h', '4h', '1d'])
    lookback_periods: Dict[str, int] = field(default_factory=lambda: {'short': 20, 'medium': 50, 'long': 200})
    feature_selection: bool = True
    feature_importance_threshold: float = 0.01
    max_features: Optional[int] = None
    custom_features: List[str] = field(default_factory=list)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'feature_types': [ft.value for ft in self.feature_types],
            'technical_indicators': self.technical_indicators,
            'timeframes': self.timeframes,
            'lookback_periods': self.lookback_periods,
            'feature_selection': self.feature_selection,
            'feature_importance_threshold': self.feature_importance_threshold,
            'max_features': self.max_features,
            'custom_features': self.custom_features
        }


@dataclass
class ModelConfig:
    """模型配置"""
    model_type: ModelType
    hyperparameters: Dict[str, Any] = field(default_factory=dict)
    ensemble_models: List[ModelType] = field(default_factory=list)
    ensemble_weights: Optional[List[float]] = None
    use_gpu: bool = False
    random_state: int = 42
    model_save_path: str = "models/"
    model_name: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'model_type': self.model_type.value,
            'hyperparameters': self.hyperparameters,
            'ensemble_models': [em.value for em in self.ensemble_models],
            'ensemble_weights': self.ensemble_weights,
            'use_gpu': self.use_gpu,
            'random_state': self.random_state,
            'model_save_path': self.model_save_path,
            'model_name': self.model_name
        }


@dataclass
class TrainingConfig:
    """训练配置"""
    training_mode: TrainingMode = TrainingMode.BATCH
    train_test_split: float = 0.8
    validation_split: float = 0.2
    cross_validation_folds: int = 5
    batch_size: int = 1000
    max_epochs: int = 100
    early_stopping_patience: int = 10
    learning_rate: float = 0.001
    optimization_metric: str = "accuracy"
    use_class_weights: bool = True
    shuffle_data: bool = True
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'training_mode': self.training_mode.value,
            'train_test_split': self.train_test_split,
            'validation_split': self.validation_split,
            'cross_validation_folds': self.cross_validation_folds,
            'batch_size': self.batch_size,
            'max_epochs': self.max_epochs,
            'early_stopping_patience': self.early_stopping_patience,
            'learning_rate': self.learning_rate,
            'optimization_metric': self.optimization_metric,
            'use_class_weights': self.use_class_weights,
            'shuffle_data': self.shuffle_data
        }


@dataclass
class PredictionConfig:
    """预测配置"""
    model_path: str
    model_version: Optional[str] = None
    batch_size: int = 100
    confidence_threshold: float = 0.5
    use_ensemble: bool = False
    ensemble_method: str = "voting"  # voting, stacking, blending
    output_format: str = "json"  # json, csv, kafka
    include_confidence: bool = True
    include_features: bool = False
    cache_predictions: bool = True
    cache_ttl: int = 300  # seconds
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'model_path': self.model_path,
            'model_version': self.model_version,
            'batch_size': self.batch_size,
            'confidence_threshold': self.confidence_threshold,
            'use_ensemble': self.use_ensemble,
            'ensemble_method': self.ensemble_method,
            'output_format': self.output_format,
            'include_confidence': self.include_confidence,
            'include_features': self.include_features,
            'cache_predictions': self.cache_predictions,
            'cache_ttl': self.cache_ttl
        }


@dataclass
class ModelMetadata:
    """模型元数据"""
    model_id: str
    model_type: ModelType
    version: str
    created_at: datetime
    trained_on: Dict[str, Any]
    performance_metrics: Dict[str, float]
    feature_names: List[str]
    target_names: List[str]
    hyperparameters: Dict[str, Any]
    training_duration: float
    model_size: int
    description: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'model_id': self.model_id,
            'model_type': self.model_type.value,
            'version': self.version,
            'created_at': self.created_at.isoformat(),
            'trained_on': self.trained_on,
            'performance_metrics': self.performance_metrics,
            'feature_names': self.feature_names,
            'target_names': self.target_names,
            'hyperparameters': self.hyperparameters,
            'training_duration': self.training_duration,
            'model_size': self.model_size,
            'description': self.description
        }


@dataclass
class TrainingResult:
    """训练结果"""
    model_id: str
    success: bool
    training_metrics: Dict[str, float]
    validation_metrics: Dict[str, float]
    test_metrics: Optional[Dict[str, float]] = None
    feature_importance: Optional[Dict[str, float]] = None
    training_time: float = 0.0
    model_path: Optional[str] = None
    error_message: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'model_id': self.model_id,
            'success': self.success,
            'training_metrics': self.training_metrics,
            'validation_metrics': self.validation_metrics,
            'test_metrics': self.test_metrics,
            'feature_importance': self.feature_importance,
            'training_time': self.training_time,
            'model_path': self.model_path,
            'error_message': self.error_message
        }


@dataclass
class PredictionResult:
    """预测结果"""
    prediction_id: str
    predictions: List[float]
    probabilities: Optional[List[List[float]]] = None
    confidence_scores: Optional[List[float]] = None
    feature_values: Optional[List[List[float]]] = None
    model_version: Optional[str] = None
    timestamp: datetime = field(default_factory=datetime.now)
    processing_time: float = 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'prediction_id': self.prediction_id,
            'predictions': self.predictions,
            'probabilities': self.probabilities,
            'confidence_scores': self.confidence_scores,
            'feature_values': self.feature_values,
            'model_version': self.model_version,
            'timestamp': self.timestamp.isoformat(),
            'processing_time': self.processing_time
        }


@dataclass
class PerformanceMetrics:
    """性能指标"""
    accuracy: float
    precision: float
    recall: float
    f1_score: float
    auc_roc: Optional[float] = None
    auc_pr: Optional[float] = None
    mse: Optional[float] = None
    mae: Optional[float] = None
    r2_score: Optional[float] = None
    sharpe_ratio: Optional[float] = None
    max_drawdown: Optional[float] = None
    win_rate: Optional[float] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'accuracy': self.accuracy,
            'precision': self.precision,
            'recall': self.recall,
            'f1_score': self.f1_score,
            'auc_roc': self.auc_roc,
            'auc_pr': self.auc_pr,
            'mse': self.mse,
            'mae': self.mae,
            'r2_score': self.r2_score,
            'sharpe_ratio': self.sharpe_ratio,
            'max_drawdown': self.max_drawdown,
            'win_rate': self.win_rate
        }


@dataclass
class TrainingPipelineConfig:
    """训练管道配置"""
    data_source: DataSourceConfig
    feature_config: FeatureConfig
    model_config: ModelConfig
    training_config: TrainingConfig
    prediction_config: PredictionConfig
    
    # 集成配置
    enable_risk_management: bool = True
    enable_online_learning: bool = True
    enable_deepseek_distillation: bool = True
    enable_multi_timeframe: bool = True
    
    # 监控配置
    enable_monitoring: bool = True
    log_level: str = "INFO"
    save_intermediate_results: bool = True
    
    def validate(self) -> bool:
        """验证配置的有效性"""
        try:
            # 检查数据源配置
            if self.data_source.source_type == DataSource.JAVA_API and not self.data_source.api_endpoint:
                raise ValueError("Java API endpoint is required when using JAVA_API data source")
            
            # 检查模型配置
            if self.model_config.model_type == ModelType.ENSEMBLE and not self.model_config.ensemble_models:
                raise ValueError("Ensemble models must be specified when using ENSEMBLE model type")
            
            # 检查训练配置
            if not (0 < self.training_config.train_test_split < 1):
                raise ValueError("train_test_split must be between 0 and 1")
            
            if not (0 < self.training_config.validation_split < 1):
                raise ValueError("validation_split must be between 0 and 1")
            
            # 检查预测配置
            if not (0 <= self.prediction_config.confidence_threshold <= 1):
                raise ValueError("confidence_threshold must be between 0 and 1")
            
            logger.info("Training pipeline configuration validation passed")
            return True
            
        except ValueError as e:
            logger.error(f"Training pipeline configuration validation failed: {e}")
            return False
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'data_source': self.data_source.to_dict(),
            'feature_config': self.feature_config.to_dict(),
            'model_config': self.model_config.to_dict(),
            'training_config': self.training_config.to_dict(),
            'prediction_config': self.prediction_config.to_dict(),
            'enable_risk_management': self.enable_risk_management,
            'enable_online_learning': self.enable_online_learning,
            'enable_deepseek_distillation': self.enable_deepseek_distillation,
            'enable_multi_timeframe': self.enable_multi_timeframe,
            'enable_monitoring': self.enable_monitoring,
            'log_level': self.log_level,
            'save_intermediate_results': self.save_intermediate_results
        }


# 默认配置
DEFAULT_DATA_SOURCE_CONFIG = DataSourceConfig(
    source_type=DataSource.JAVA_API,
    api_endpoint="http://localhost:8080/api/data",
    timeout=30,
    retry_attempts=3
)

DEFAULT_FEATURE_CONFIG = FeatureConfig(
    feature_types=[FeatureType.TECHNICAL_INDICATORS, FeatureType.MULTI_TIMEFRAME],
    technical_indicators=['sma', 'ema', 'rsi', 'macd', 'bollinger', 'atr'],
    timeframes=['1h', '4h', '1d'],
    feature_selection=True
)

DEFAULT_MODEL_CONFIG = ModelConfig(
    model_type=ModelType.XGBOOST,
    hyperparameters={
        'n_estimators': 100,
        'max_depth': 6,
        'learning_rate': 0.1,
        'subsample': 0.8,
        'colsample_bytree': 0.8
    }
)

DEFAULT_TRAINING_CONFIG = TrainingConfig(
    training_mode=TrainingMode.BATCH,
    cross_validation_folds=5,
    early_stopping_patience=10
)

DEFAULT_PREDICTION_CONFIG = PredictionConfig(
    model_path="models/",
    confidence_threshold=0.6,
    include_confidence=True
)

DEFAULT_PIPELINE_CONFIG = TrainingPipelineConfig(
    data_source=DEFAULT_DATA_SOURCE_CONFIG,
    feature_config=DEFAULT_FEATURE_CONFIG,
    model_config=DEFAULT_MODEL_CONFIG,
    training_config=DEFAULT_TRAINING_CONFIG,
    prediction_config=DEFAULT_PREDICTION_CONFIG
)
