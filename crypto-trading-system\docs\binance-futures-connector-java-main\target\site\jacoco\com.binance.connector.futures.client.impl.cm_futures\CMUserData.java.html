<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>CMUserData.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">io.github.binance:binance-futures-connector-java</a> &gt; <a href="index.source.html" class="el_package">com.binance.connector.futures.client.impl.cm_futures</a> &gt; <span class="el_source">CMUserData.java</span></div><h1>CMUserData.java</h1><pre class="source lang-java linenums">package com.binance.connector.futures.client.impl.cm_futures;

import com.binance.connector.futures.client.impl.futures.UserData;
import com.binance.connector.futures.client.utils.ProxyAuth;

/**
 * &lt;h2&gt;Coin-Margined User Data Streams Endpoints&lt;/h2&gt;
 * All endpoints under the
 * &lt;a href=&quot;https://developers.binance.com/docs/derivatives/coin-margined-futures/user-data-streams/Connect&quot;&gt;User Data Streams&lt;/a&gt;
 * section of the API documentation will be implemented in this class.
 * &lt;br&gt;
 * Response will be returned in &lt;i&gt;String format&lt;/i&gt;.
 */
public class CMUserData extends UserData {
    public CMUserData(String productUrl, String apiKey, boolean showLimitUsage, ProxyAuth proxy) {
<span class="fc" id="L16">        super(productUrl, apiKey, showLimitUsage, proxy);</span>
<span class="fc" id="L17">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.7.202105040129</span></div></body></html>