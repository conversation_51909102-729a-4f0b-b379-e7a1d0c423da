<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="jacoco-resources/report.gif" type="image/gif"/><title>Sessions</title></head><body><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="jacoco-sessions.html" class="el_session">Sessions</a></span><a href="index.html" class="el_report">io.github.binance:binance-futures-connector-java</a> &gt; <span class="el_session">Sessions</span></div><h1>Sessions</h1><p>This coverage report is based on execution data from the following sessions:</p><table class="coverage" cellspacing="0"><thead><tr><td>Session</td><td>Start Time</td><td>Dump Time</td></tr></thead><tbody><tr><td><span class="el_session">IAENO-1c9e02d9</span></td><td>2025年4月4日 16:04:26</td><td>2025年4月4日 16:04:34</td></tr><tr><td><span class="el_session">IAENO-306a0246</span></td><td>2025年4月4日 16:05:53</td><td>2025年4月4日 16:05:59</td></tr></tbody></table><p>Execution data for the following classes is considered in this report:</p><table class="coverage" cellspacing="0"><thead><tr><td>Class</td><td>Id</td></tr></thead><tbody><tr><td><span class="el_class">ch.qos.logback.classic.AsyncAppender</span></td><td><code>00f3b9efea68867f</code></td></tr><tr><td><span class="el_class">ch.qos.logback.classic.Level</span></td><td><code>e2155b45608f35d7</code></td></tr><tr><td><span class="el_class">ch.qos.logback.classic.Logger</span></td><td><code>f35d4d4ad6b0173a</code></td></tr><tr><td><span class="el_class">ch.qos.logback.classic.LoggerContext</span></td><td><code>d057ce3cea631d6b</code></td></tr><tr><td><span class="el_class">ch.qos.logback.classic.PatternLayout</span></td><td><code>6b4fcc6f23c89763</code></td></tr><tr><td><span class="el_class">ch.qos.logback.classic.encoder.PatternLayoutEncoder</span></td><td><code>b5df0ef8a1a735ea</code></td></tr><tr><td><span class="el_class">ch.qos.logback.classic.joran.JoranConfigurator</span></td><td><code>63bb214e0f720ae8</code></td></tr><tr><td><span class="el_class">ch.qos.logback.classic.joran.action.ConfigurationAction</span></td><td><code>90d861250f52b75f</code></td></tr><tr><td><span class="el_class">ch.qos.logback.classic.joran.action.ConsolePluginAction</span></td><td><code>2969e4b8b532cec5</code></td></tr><tr><td><span class="el_class">ch.qos.logback.classic.joran.action.ContextNameAction</span></td><td><code>4ffd1a75c51a473f</code></td></tr><tr><td><span class="el_class">ch.qos.logback.classic.joran.action.EvaluatorAction</span></td><td><code>cc2e7d3c2fc18087</code></td></tr><tr><td><span class="el_class">ch.qos.logback.classic.joran.action.InsertFromJNDIAction</span></td><td><code>fce902dbb9dbd2a7</code></td></tr><tr><td><span class="el_class">ch.qos.logback.classic.joran.action.JMXConfiguratorAction</span></td><td><code>a58b513df0924938</code></td></tr><tr><td><span class="el_class">ch.qos.logback.classic.joran.action.LevelAction</span></td><td><code>8f89eefaf59271f1</code></td></tr><tr><td><span class="el_class">ch.qos.logback.classic.joran.action.LoggerAction</span></td><td><code>8d55f78fdf86cda9</code></td></tr><tr><td><span class="el_class">ch.qos.logback.classic.joran.action.LoggerContextListenerAction</span></td><td><code>835263a7d9309be9</code></td></tr><tr><td><span class="el_class">ch.qos.logback.classic.joran.action.ReceiverAction</span></td><td><code>9e9bd00760b812f2</code></td></tr><tr><td><span class="el_class">ch.qos.logback.classic.joran.action.RootLoggerAction</span></td><td><code>0528540059645c3d</code></td></tr><tr><td><span class="el_class">ch.qos.logback.classic.pattern.ClassicConverter</span></td><td><code>78403f02659989af</code></td></tr><tr><td><span class="el_class">ch.qos.logback.classic.pattern.DateConverter</span></td><td><code>5c52dc34531b028d</code></td></tr><tr><td><span class="el_class">ch.qos.logback.classic.pattern.EnsureExceptionHandling</span></td><td><code>f9c97b8da786f083</code></td></tr><tr><td><span class="el_class">ch.qos.logback.classic.pattern.LevelConverter</span></td><td><code>05b4415a3dbcaaf4</code></td></tr><tr><td><span class="el_class">ch.qos.logback.classic.pattern.LineSeparatorConverter</span></td><td><code>2e2dc69c3bdc6cd3</code></td></tr><tr><td><span class="el_class">ch.qos.logback.classic.pattern.LoggerConverter</span></td><td><code>e250f04c84d66501</code></td></tr><tr><td><span class="el_class">ch.qos.logback.classic.pattern.MessageConverter</span></td><td><code>ef2f64b51bca1aac</code></td></tr><tr><td><span class="el_class">ch.qos.logback.classic.pattern.NamedConverter</span></td><td><code>2d8a1e4cd16b9929</code></td></tr><tr><td><span class="el_class">ch.qos.logback.classic.pattern.TargetLengthBasedClassNameAbbreviator</span></td><td><code>ec60b2fb41d57b0a</code></td></tr><tr><td><span class="el_class">ch.qos.logback.classic.pattern.ThreadConverter</span></td><td><code>a95aaedda263355c</code></td></tr><tr><td><span class="el_class">ch.qos.logback.classic.pattern.ThrowableHandlingConverter</span></td><td><code>266cc4ca75fcd39d</code></td></tr><tr><td><span class="el_class">ch.qos.logback.classic.pattern.ThrowableProxyConverter</span></td><td><code>46dc88ad0c97e462</code></td></tr><tr><td><span class="el_class">ch.qos.logback.classic.selector.DefaultContextSelector</span></td><td><code>fd861e3242ccff2f</code></td></tr><tr><td><span class="el_class">ch.qos.logback.classic.sift.SiftAction</span></td><td><code>9f73df3037d696a7</code></td></tr><tr><td><span class="el_class">ch.qos.logback.classic.spi.EventArgUtil</span></td><td><code>88f3990bf293da69</code></td></tr><tr><td><span class="el_class">ch.qos.logback.classic.spi.LoggerContextVO</span></td><td><code>ecac106025bca4a3</code></td></tr><tr><td><span class="el_class">ch.qos.logback.classic.spi.LoggingEvent</span></td><td><code>75c5fe4974050a6f</code></td></tr><tr><td><span class="el_class">ch.qos.logback.classic.spi.PlatformInfo</span></td><td><code>0e826c07ba59ae45</code></td></tr><tr><td><span class="el_class">ch.qos.logback.classic.spi.TurboFilterList</span></td><td><code>aa3cf39d0c0c651e</code></td></tr><tr><td><span class="el_class">ch.qos.logback.classic.util.ContextInitializer</span></td><td><code>f560906e9553d69f</code></td></tr><tr><td><span class="el_class">ch.qos.logback.classic.util.ContextSelectorStaticBinder</span></td><td><code>271bbf6fa66123b1</code></td></tr><tr><td><span class="el_class">ch.qos.logback.classic.util.DefaultNestedComponentRules</span></td><td><code>840b992fa00c7e60</code></td></tr><tr><td><span class="el_class">ch.qos.logback.classic.util.EnvUtil</span></td><td><code>39b5543082458460</code></td></tr><tr><td><span class="el_class">ch.qos.logback.classic.util.LogbackMDCAdapter</span></td><td><code>a05682a253fd41d4</code></td></tr><tr><td><span class="el_class">ch.qos.logback.classic.util.LoggerNameUtil</span></td><td><code>b8d88c97a0cadcfa</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.AsyncAppenderBase</span></td><td><code>62d45df726cf3e80</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.AsyncAppenderBase.Worker</span></td><td><code>a7e04f1347015236</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.BasicStatusManager</span></td><td><code>f42ab87c1f66e222</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.ConsoleAppender</span></td><td><code>d101474cda5e45c9</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.ContextBase</span></td><td><code>707ceedbd09855e6</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.CoreConstants</span></td><td><code>09363a83cd5b4101</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.FileAppender</span></td><td><code>9bcdaf4f32f84b05</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.LayoutBase</span></td><td><code>e6bfd3b1edc3ab01</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.OutputStreamAppender</span></td><td><code>79e07918442741f3</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.UnsynchronizedAppenderBase</span></td><td><code>0672be5753362c70</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.encoder.EncoderBase</span></td><td><code>f2507a7276f26c10</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.encoder.LayoutWrappingEncoder</span></td><td><code>6c80790d34287d6b</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.helpers.CyclicBuffer</span></td><td><code>422c7b9f7318f10a</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.joran.GenericConfigurator</span></td><td><code>3f448ac12ab6a263</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.joran.JoranConfiguratorBase</span></td><td><code>38c4decb94b320f7</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.joran.action.AbstractEventEvaluatorAction</span></td><td><code>bf3cf252a2822906</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.joran.action.Action</span></td><td><code>7cf2d4f3569d0788</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.joran.action.ActionUtil</span></td><td><code>da5c6cf74bffc921</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.joran.action.ActionUtil.1</span></td><td><code>c00c37a033db136c</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.joran.action.ActionUtil.Scope</span></td><td><code>461815209cc76697</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.joran.action.AppenderAction</span></td><td><code>22c3c549e13663a1</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.joran.action.AppenderRefAction</span></td><td><code>3c0bd482c9925292</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.joran.action.ContextPropertyAction</span></td><td><code>4d47e7c289aa172b</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.joran.action.ConversionRuleAction</span></td><td><code>6ad21d1237f36c71</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.joran.action.DefinePropertyAction</span></td><td><code>3d08042673a6e5dc</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.joran.action.IADataForBasicProperty</span></td><td><code>cbe844e4f3903797</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.joran.action.IADataForComplexProperty</span></td><td><code>9b210f34ec734f9e</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.joran.action.ImplicitAction</span></td><td><code>86dae105afebc13c</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.joran.action.IncludeAction</span></td><td><code>2775b098b6b111dc</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.joran.action.NOPAction</span></td><td><code>69348e8c62d1a733</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.joran.action.NestedBasicPropertyIA</span></td><td><code>89ed90b29bc14f36</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.joran.action.NestedBasicPropertyIA.1</span></td><td><code>08e44e1168d7ea7b</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.joran.action.NestedComplexPropertyIA</span></td><td><code>178aace2d0448f6a</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.joran.action.NestedComplexPropertyIA.1</span></td><td><code>5160250e9b77af57</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.joran.action.NewRuleAction</span></td><td><code>265aa9ab808da62d</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.joran.action.ParamAction</span></td><td><code>ad2376677140dcb4</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.joran.action.PropertyAction</span></td><td><code>81b578f6564d00a1</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.joran.action.ShutdownHookAction</span></td><td><code>e67fa543b234ff0d</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.joran.action.StatusListenerAction</span></td><td><code>4cf479b0b81398f3</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.joran.action.TimestampAction</span></td><td><code>d7a48c3648a91ea8</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.joran.conditional.ElseAction</span></td><td><code>fe56c4a40374cd79</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.joran.conditional.IfAction</span></td><td><code>87c92d3efc3996c9</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.joran.conditional.ThenAction</span></td><td><code>dd7886fdda1bb93e</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.joran.conditional.ThenOrElseActionBase</span></td><td><code>9e00d4141028a50c</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.joran.event.BodyEvent</span></td><td><code>0c8f2f07c6888bab</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.joran.event.EndEvent</span></td><td><code>0c2e1da47ad508cc</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.joran.event.SaxEvent</span></td><td><code>80662212b5cc3b53</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.joran.event.SaxEventRecorder</span></td><td><code>639eb66c9ea90531</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.joran.event.StartEvent</span></td><td><code>914de9498a78076d</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.joran.spi.CAI_WithLocatorSupport</span></td><td><code>f96b1cd7be830663</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.joran.spi.ConfigurationWatchList</span></td><td><code>fba78df767e05182</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.joran.spi.ConsoleTarget</span></td><td><code>6e2cdd5051fbf329</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.joran.spi.ConsoleTarget.1</span></td><td><code>9612187e03729cd5</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.joran.spi.ConsoleTarget.2</span></td><td><code>ea3332451607183e</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.joran.spi.DefaultNestedComponentRegistry</span></td><td><code>f3ac4f0369a959d6</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.joran.spi.ElementPath</span></td><td><code>ab4711e5039d31b0</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.joran.spi.ElementSelector</span></td><td><code>605584d4fe3a6b67</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.joran.spi.EventPlayer</span></td><td><code>739ef0261c196bb2</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.joran.spi.HostClassAndPropertyDouble</span></td><td><code>199aef84b04dd48c</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.joran.spi.InterpretationContext</span></td><td><code>ce4c00a894617c6e</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.joran.spi.Interpreter</span></td><td><code>634fa7d2dde257a5</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.joran.spi.NoAutoStartUtil</span></td><td><code>6fe8a98ba9c5ce85</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.joran.spi.SimpleRuleStore</span></td><td><code>19c383749dc55e01</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.joran.util.ConfigurationWatchListUtil</span></td><td><code>a35db514967601cf</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.joran.util.PropertySetter</span></td><td><code>8f7e7385541ef400</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.joran.util.StringToObjectConverter</span></td><td><code>2e393f7832702c3f</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.joran.util.beans.BeanDescription</span></td><td><code>a249e33828fc438a</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.joran.util.beans.BeanDescriptionCache</span></td><td><code>9d679b6b2b24c9f7</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.joran.util.beans.BeanDescriptionFactory</span></td><td><code>1abb714ec36ec08c</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.joran.util.beans.BeanUtil</span></td><td><code>889c2d82913f56d3</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.net.ssl.SSLNestedComponentRegistryRules</span></td><td><code>cdeda61b0c175e73</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.pattern.Converter</span></td><td><code>925f6cb417029041</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.pattern.ConverterUtil</span></td><td><code>dd9b10877d49fdef</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.pattern.DynamicConverter</span></td><td><code>66d903dd096314f6</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.pattern.FormatInfo</span></td><td><code>875526d52e168bcb</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.pattern.FormattingConverter</span></td><td><code>c3110b5495da3c0a</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.pattern.LiteralConverter</span></td><td><code>65b2e319699170e6</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.pattern.PatternLayoutBase</span></td><td><code>a804a6743796ed4f</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.pattern.PatternLayoutEncoderBase</span></td><td><code>8869b320200d58ca</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.pattern.SpacePadder</span></td><td><code>e82e4efc2cb997cb</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.pattern.parser.Compiler</span></td><td><code>1c6d6460ba38602b</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.pattern.parser.FormattingNode</span></td><td><code>c1ea708a78deec04</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.pattern.parser.Node</span></td><td><code>6c2db44212d84b68</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.pattern.parser.OptionTokenizer</span></td><td><code>b9b225507c800bd5</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.pattern.parser.Parser</span></td><td><code>7b1aef016f4f95f3</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.pattern.parser.SimpleKeywordNode</span></td><td><code>f700f290325e600d</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.pattern.parser.Token</span></td><td><code>4f7e433507e860ed</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.pattern.parser.TokenStream</span></td><td><code>b0bdcf4b6e0f87aa</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.pattern.parser.TokenStream.1</span></td><td><code>fd95c0c735fd0ef7</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.pattern.parser.TokenStream.TokenizerState</span></td><td><code>3467111fb3bf68e6</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.pattern.util.AlmostAsIsEscapeUtil</span></td><td><code>e719d65b9213d1fa</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.pattern.util.AsIsEscapeUtil</span></td><td><code>59f6b4aeb7076212</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.pattern.util.RegularEscapeUtil</span></td><td><code>1cc07c8d9d362995</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.pattern.util.RestrictedEscapeUtil</span></td><td><code>05ac894407a1822b</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.recovery.ResilientFileOutputStream</span></td><td><code>2afb940d9286a078</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.recovery.ResilientOutputStreamBase</span></td><td><code>f417572e91a72b81</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.rolling.DefaultTimeBasedFileNamingAndTriggeringPolicy</span></td><td><code>ccc58d7139de9656</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.rolling.RollingFileAppender</span></td><td><code>b6e18b43ca440d7f</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.rolling.RollingPolicyBase</span></td><td><code>e331e0f5c275c3e5</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.rolling.TimeBasedFileNamingAndTriggeringPolicyBase</span></td><td><code>f72a2ecc11c360b0</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.rolling.TimeBasedRollingPolicy</span></td><td><code>3b049b19333df91f</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.rolling.helper.CompressionMode</span></td><td><code>14d64b070c0eb595</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.rolling.helper.Compressor</span></td><td><code>f753a94d3fa3e08d</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.rolling.helper.Compressor.1</span></td><td><code>50968601a58575f8</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.rolling.helper.DateTokenConverter</span></td><td><code>fcdec6d98dfc9b78</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.rolling.helper.FileFilterUtil</span></td><td><code>cb34f67c8a1e87a6</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.rolling.helper.FileNamePattern</span></td><td><code>93234362df021b15</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.rolling.helper.PeriodicityType</span></td><td><code>8b1d4c0f7314f120</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.rolling.helper.RenameUtil</span></td><td><code>7ac7ef63c2866975</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.rolling.helper.RollingCalendar</span></td><td><code>80a301a204e2d598</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.rolling.helper.RollingCalendar.1</span></td><td><code>3639a0e622f23134</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.rolling.helper.TimeBasedArchiveRemover</span></td><td><code>f1f68ffac72fed27</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.spi.AppenderAttachableImpl</span></td><td><code>356e7661a1308dba</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.spi.ContextAwareBase</span></td><td><code>507768fbb8be644f</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.spi.ContextAwareImpl</span></td><td><code>e054ab71d51b27ec</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.spi.FilterAttachableImpl</span></td><td><code>e0d2c4e50fd975d2</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.spi.FilterReply</span></td><td><code>8ffb0681c411c96a</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.spi.LogbackLock</span></td><td><code>b3b7af385a799776</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.status.InfoStatus</span></td><td><code>1d3c0987bb0ffe10</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.status.StatusBase</span></td><td><code>7c1cffd1a9986020</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.status.StatusUtil</span></td><td><code>b5fec2971e383d38</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.subst.Node</span></td><td><code>173ef78e5278fe04</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.subst.Node.Type</span></td><td><code>b8a40f4b8fbe988c</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.subst.NodeToStringTransformer</span></td><td><code>1e8620cc7b5415cb</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.subst.NodeToStringTransformer.1</span></td><td><code>5967309dea3614e0</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.subst.Parser</span></td><td><code>c06549d7b1e1487d</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.subst.Parser.1</span></td><td><code>78a0480962b020ea</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.subst.Token</span></td><td><code>3f38da4ca554aafd</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.subst.Token.Type</span></td><td><code>d037d0aeea85e517</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.subst.Tokenizer</span></td><td><code>6a388c818909b082</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.subst.Tokenizer.1</span></td><td><code>5446562f97e885f7</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.subst.Tokenizer.TokenizerState</span></td><td><code>a43d7665d3995d51</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.util.AggregationType</span></td><td><code>e82dcae26638e651</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.util.COWArrayList</span></td><td><code>fd4fbd3c0c90c052</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.util.CachingDateFormatter</span></td><td><code>371338e1c1d98e24</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.util.CharSequenceState</span></td><td><code>f898d54b4e66eaeb</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.util.CharSequenceToRegexMapper</span></td><td><code>32675eaa72c190be</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.util.DatePatternToRegexUtil</span></td><td><code>9386e299c5f6ea76</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.util.EnvUtil</span></td><td><code>7b577f5a09fa36fe</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.util.FileSize</span></td><td><code>4fd8e390d1cf1518</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.util.FileUtil</span></td><td><code>92c6cbedcb07ec63</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.util.Loader</span></td><td><code>6a7f26fdd43cf12b</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.util.Loader.1</span></td><td><code>d6e48f075e51e44b</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.util.OptionHelper</span></td><td><code>52dae6015c6ac77b</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.util.StatusListenerConfigHelper</span></td><td><code>b3e50ff76e275069</code></td></tr><tr><td><span class="el_class">ch.qos.logback.core.util.StatusPrinter</span></td><td><code>04fef78263405164</code></td></tr><tr><td><a href="com.binance.connector.futures.client.enums/HttpMethod.html" class="el_class">com.binance.connector.futures.client.enums.HttpMethod</a></td><td><code>87ef9eda5abee4fb</code></td></tr><tr><td><a href="com.binance.connector.futures.client.enums/RequestType.html" class="el_class">com.binance.connector.futures.client.enums.RequestType</a></td><td><code>019fdad7103353f8</code></td></tr><tr><td><a href="com.binance.connector.futures.client.exceptions/BinanceClientException.html" class="el_class">com.binance.connector.futures.client.exceptions.BinanceClientException</a></td><td><code>df1c64d6265f01ed</code></td></tr><tr><td><a href="com.binance.connector.futures.client.exceptions/BinanceConnectorException.html" class="el_class">com.binance.connector.futures.client.exceptions.BinanceConnectorException</a></td><td><code>ecf3489ea56692cd</code></td></tr><tr><td><a href="com.binance.connector.futures.client.exceptions/BinanceServerException.html" class="el_class">com.binance.connector.futures.client.exceptions.BinanceServerException</a></td><td><code>25e5c0d8d167d741</code></td></tr><tr><td><a href="com.binance.connector.futures.client.impl/CMFuturesClientImpl.html" class="el_class">com.binance.connector.futures.client.impl.CMFuturesClientImpl</a></td><td><code>e0d1e19e38202d0a</code></td></tr><tr><td><a href="com.binance.connector.futures.client.impl/FuturesClientImpl.html" class="el_class">com.binance.connector.futures.client.impl.FuturesClientImpl</a></td><td><code>b74e149cc751d3be</code></td></tr><tr><td><a href="com.binance.connector.futures.client.impl/UMFuturesClientImpl.html" class="el_class">com.binance.connector.futures.client.impl.UMFuturesClientImpl</a></td><td><code>d8c964027eefbf5d</code></td></tr><tr><td><a href="com.binance.connector.futures.client.impl.cm_futures/CMAccount.html" class="el_class">com.binance.connector.futures.client.impl.cm_futures.CMAccount</a></td><td><code>f0291cb76529563e</code></td></tr><tr><td><a href="com.binance.connector.futures.client.impl.cm_futures/CMMarket.html" class="el_class">com.binance.connector.futures.client.impl.cm_futures.CMMarket</a></td><td><code>f19bffe6e4eb7069</code></td></tr><tr><td><a href="com.binance.connector.futures.client.impl.cm_futures/CMPortfolioMargin.html" class="el_class">com.binance.connector.futures.client.impl.cm_futures.CMPortfolioMargin</a></td><td><code>a08c939428578ade</code></td></tr><tr><td><a href="com.binance.connector.futures.client.impl.cm_futures/CMUserData.html" class="el_class">com.binance.connector.futures.client.impl.cm_futures.CMUserData</a></td><td><code>a001d634838a97fb</code></td></tr><tr><td><a href="com.binance.connector.futures.client.impl.futures/Account.html" class="el_class">com.binance.connector.futures.client.impl.futures.Account</a></td><td><code>bbc98e38c37a156c</code></td></tr><tr><td><a href="com.binance.connector.futures.client.impl.futures/Market.html" class="el_class">com.binance.connector.futures.client.impl.futures.Market</a></td><td><code>be17dd294dc04649</code></td></tr><tr><td><a href="com.binance.connector.futures.client.impl.futures/PortfolioMargin.html" class="el_class">com.binance.connector.futures.client.impl.futures.PortfolioMargin</a></td><td><code>ce478898e0e63330</code></td></tr><tr><td><a href="com.binance.connector.futures.client.impl.futures/UserData.html" class="el_class">com.binance.connector.futures.client.impl.futures.UserData</a></td><td><code>4211399c1812bbb0</code></td></tr><tr><td><a href="com.binance.connector.futures.client.impl.um_futures/UMAccount.html" class="el_class">com.binance.connector.futures.client.impl.um_futures.UMAccount</a></td><td><code>0159cd3bf850d7ee</code></td></tr><tr><td><a href="com.binance.connector.futures.client.impl.um_futures/UMMarket.html" class="el_class">com.binance.connector.futures.client.impl.um_futures.UMMarket</a></td><td><code>e11fab0c76d407fa</code></td></tr><tr><td><a href="com.binance.connector.futures.client.impl.um_futures/UMUserData.html" class="el_class">com.binance.connector.futures.client.impl.um_futures.UMUserData</a></td><td><code>4130f811737608a0</code></td></tr><tr><td><a href="com.binance.connector.futures.client.utils/HttpClientSingleton.html" class="el_class">com.binance.connector.futures.client.utils.HttpClientSingleton</a></td><td><code>22fbcaf3b4da66ec</code></td></tr><tr><td><a href="com.binance.connector.futures.client.utils/JSONParser.html" class="el_class">com.binance.connector.futures.client.utils.JSONParser</a></td><td><code>d4ddfb0754eab9ba</code></td></tr><tr><td><a href="com.binance.connector.futures.client.utils/ParameterChecker.html" class="el_class">com.binance.connector.futures.client.utils.ParameterChecker</a></td><td><code>519c0123f7b7d39c</code></td></tr><tr><td><a href="com.binance.connector.futures.client.utils/RequestBuilder.html" class="el_class">com.binance.connector.futures.client.utils.RequestBuilder</a></td><td><code>ebeb901b323837e2</code></td></tr><tr><td><span class="el_class">com.binance.connector.futures.client.utils.RequestBuilder.1</span></td><td><code>aa9f70c2e81ba1c7</code></td></tr><tr><td><a href="com.binance.connector.futures.client.utils/RequestHandler.html" class="el_class">com.binance.connector.futures.client.utils.RequestHandler</a></td><td><code>31696499f3fc726a</code></td></tr><tr><td><span class="el_class">com.binance.connector.futures.client.utils.RequestHandler.1</span></td><td><code>e9dc9ef282118ca3</code></td></tr><tr><td><a href="com.binance.connector.futures.client.utils/ResponseHandler.html" class="el_class">com.binance.connector.futures.client.utils.ResponseHandler</a></td><td><code>223a3cc453de9ded</code></td></tr><tr><td><a href="com.binance.connector.futures.client.utils/SignatureGenerator.html" class="el_class">com.binance.connector.futures.client.utils.SignatureGenerator</a></td><td><code>dbdca8f8e565f1ee</code></td></tr><tr><td><a href="com.binance.connector.futures.client.utils/UrlBuilder.html" class="el_class">com.binance.connector.futures.client.utils.UrlBuilder</a></td><td><code>4bfdf33a6259cee3</code></td></tr><tr><td><a href="com.binance.connector.futures.logging.util/MsEpochConverter.html" class="el_class">com.binance.connector.futures.logging.util.MsEpochConverter</a></td><td><code>95f7814a116244cc</code></td></tr><tr><td><span class="el_class">kotlin.Pair</span></td><td><code>293d54fad4911819</code></td></tr><tr><td><span class="el_class">kotlin.TuplesKt</span></td><td><code>17434c57a8070a93</code></td></tr><tr><td><span class="el_class">kotlin.Unit</span></td><td><code>525649945818806c</code></td></tr><tr><td><span class="el_class">kotlin.collections.AbstractCollection</span></td><td><code>dac1458732546ecf</code></td></tr><tr><td><span class="el_class">kotlin.collections.AbstractList</span></td><td><code>a508ace426a9c37e</code></td></tr><tr><td><span class="el_class">kotlin.collections.AbstractList.Companion</span></td><td><code>21c4944ba89d5da5</code></td></tr><tr><td><span class="el_class">kotlin.collections.ArrayAsCollection</span></td><td><code>349ad302c4727217</code></td></tr><tr><td><span class="el_class">kotlin.collections.ArraysKt___ArraysJvmKt</span></td><td><code>3e3c5da3e846eb8a</code></td></tr><tr><td><span class="el_class">kotlin.collections.ArraysKt___ArraysKt</span></td><td><code>b970dcdc981305b4</code></td></tr><tr><td><span class="el_class">kotlin.collections.ArraysUtilJVM</span></td><td><code>9595b65dba34e5b6</code></td></tr><tr><td><span class="el_class">kotlin.collections.CollectionsKt__CollectionsJVMKt</span></td><td><code>cbb4a593ec360d12</code></td></tr><tr><td><span class="el_class">kotlin.collections.CollectionsKt__CollectionsKt</span></td><td><code>bfee8dcc50284d87</code></td></tr><tr><td><span class="el_class">kotlin.collections.CollectionsKt__IterablesKt</span></td><td><code>4e8c6bb98722f754</code></td></tr><tr><td><span class="el_class">kotlin.collections.CollectionsKt__MutableCollectionsJVMKt</span></td><td><code>8f9466a15ab8d3d9</code></td></tr><tr><td><span class="el_class">kotlin.collections.CollectionsKt__MutableCollectionsKt</span></td><td><code>e15041d70141aebe</code></td></tr><tr><td><span class="el_class">kotlin.collections.CollectionsKt___CollectionsKt</span></td><td><code>f07fc13f0aaf7249</code></td></tr><tr><td><span class="el_class">kotlin.collections.EmptyList</span></td><td><code>c20c5f4d23d9884e</code></td></tr><tr><td><span class="el_class">kotlin.collections.EmptyMap</span></td><td><code>58c2df57350abc7b</code></td></tr><tr><td><span class="el_class">kotlin.collections.EmptySet</span></td><td><code>1d56510c4d03e5b5</code></td></tr><tr><td><span class="el_class">kotlin.collections.MapsKt__MapsKt</span></td><td><code>248a5bb9bb83325b</code></td></tr><tr><td><span class="el_class">kotlin.collections.SetsKt__SetsKt</span></td><td><code>8ea112a91f3539bb</code></td></tr><tr><td><span class="el_class">kotlin.comparisons.ComparisonsKt__ComparisonsKt</span></td><td><code>0b28441186dd8b27</code></td></tr><tr><td><span class="el_class">kotlin.internal.ProgressionUtilKt</span></td><td><code>29801081d7d99e47</code></td></tr><tr><td><span class="el_class">kotlin.io.CloseableKt</span></td><td><code>51a1fdae7ab7c81c</code></td></tr><tr><td><span class="el_class">kotlin.jvm.internal.ArrayIterator</span></td><td><code>e7cca165be907e85</code></td></tr><tr><td><span class="el_class">kotlin.jvm.internal.ArrayIteratorKt</span></td><td><code>0e7bd1298b98e165</code></td></tr><tr><td><span class="el_class">kotlin.jvm.internal.Intrinsics</span></td><td><code>842f06f411842f7e</code></td></tr><tr><td><span class="el_class">kotlin.jvm.internal.Lambda</span></td><td><code>c28837c3bcb890f4</code></td></tr><tr><td><span class="el_class">kotlin.ranges.IntProgression</span></td><td><code>89535c4367f3ab90</code></td></tr><tr><td><span class="el_class">kotlin.ranges.IntProgression.Companion</span></td><td><code>8a5746a079b4af6f</code></td></tr><tr><td><span class="el_class">kotlin.ranges.IntRange</span></td><td><code>573882adafcff832</code></td></tr><tr><td><span class="el_class">kotlin.ranges.IntRange.Companion</span></td><td><code>c7c956f6734b2a80</code></td></tr><tr><td><span class="el_class">kotlin.ranges.RangesKt__RangesKt</span></td><td><code>b101b16ffbf62048</code></td></tr><tr><td><span class="el_class">kotlin.ranges.RangesKt___RangesKt</span></td><td><code>5d8d9ae19c9ff950</code></td></tr><tr><td><span class="el_class">kotlin.text.CharsKt__CharJVMKt</span></td><td><code>32f32176972b845c</code></td></tr><tr><td><span class="el_class">kotlin.text.Charsets</span></td><td><code>12ea43b70c031a8a</code></td></tr><tr><td><span class="el_class">kotlin.text.Regex</span></td><td><code>cd2d13ec6ceabde2</code></td></tr><tr><td><span class="el_class">kotlin.text.Regex.Companion</span></td><td><code>fa191f1ed7f55e04</code></td></tr><tr><td><span class="el_class">kotlin.text.StringsKt__StringNumberConversionsKt</span></td><td><code>b2da196ffad80790</code></td></tr><tr><td><span class="el_class">kotlin.text.StringsKt__StringsJVMKt</span></td><td><code>549bb7f2033c9782</code></td></tr><tr><td><span class="el_class">kotlin.text.StringsKt__StringsKt</span></td><td><code>7a723121eeaf9de7</code></td></tr><tr><td><span class="el_class">okhttp3.Address</span></td><td><code>a92230353a61fb64</code></td></tr><tr><td><span class="el_class">okhttp3.Authenticator</span></td><td><code>48acc4243e8fe449</code></td></tr><tr><td><span class="el_class">okhttp3.Authenticator.Companion</span></td><td><code>ff8262c1c6baeb13</code></td></tr><tr><td><span class="el_class">okhttp3.Authenticator.Companion.AuthenticatorNone</span></td><td><code>c140ce1e145519a0</code></td></tr><tr><td><span class="el_class">okhttp3.CacheControl</span></td><td><code>f5b600fa5a4863f0</code></td></tr><tr><td><span class="el_class">okhttp3.CacheControl.Builder</span></td><td><code>32e8b66333fb3290</code></td></tr><tr><td><span class="el_class">okhttp3.CacheControl.Companion</span></td><td><code>a59063fb2c7608ad</code></td></tr><tr><td><span class="el_class">okhttp3.CertificatePinner</span></td><td><code>5ddc1d05c58e1bdf</code></td></tr><tr><td><span class="el_class">okhttp3.CertificatePinner.Builder</span></td><td><code>b12d37865875ae96</code></td></tr><tr><td><span class="el_class">okhttp3.CertificatePinner.Companion</span></td><td><code>8c20054c6cbc0416</code></td></tr><tr><td><span class="el_class">okhttp3.CipherSuite</span></td><td><code>0eaa2132ee6e2706</code></td></tr><tr><td><span class="el_class">okhttp3.CipherSuite.Companion</span></td><td><code>17ee86ddcceb7e4a</code></td></tr><tr><td><span class="el_class">okhttp3.CipherSuite.Companion.ORDER_BY_NAME.1</span></td><td><code>ba597ec154d0ee66</code></td></tr><tr><td><span class="el_class">okhttp3.ConnectionPool</span></td><td><code>ea05a4cced58609c</code></td></tr><tr><td><span class="el_class">okhttp3.ConnectionSpec</span></td><td><code>012a1ffc11f3b1fe</code></td></tr><tr><td><span class="el_class">okhttp3.ConnectionSpec.Builder</span></td><td><code>3b8eb37b21db0fcc</code></td></tr><tr><td><span class="el_class">okhttp3.ConnectionSpec.Companion</span></td><td><code>71951efbfcef404f</code></td></tr><tr><td><span class="el_class">okhttp3.CookieJar</span></td><td><code>bc54a64c46466638</code></td></tr><tr><td><span class="el_class">okhttp3.CookieJar.Companion</span></td><td><code>475aa1d0143d5a2a</code></td></tr><tr><td><span class="el_class">okhttp3.CookieJar.Companion.NoCookies</span></td><td><code>e3b5e4871c5eba44</code></td></tr><tr><td><span class="el_class">okhttp3.Dispatcher</span></td><td><code>c460c11f7a7ca04d</code></td></tr><tr><td><span class="el_class">okhttp3.Dns</span></td><td><code>9669c9051983f50e</code></td></tr><tr><td><span class="el_class">okhttp3.Dns.Companion</span></td><td><code>44e434016d4f07fb</code></td></tr><tr><td><span class="el_class">okhttp3.Dns.Companion.DnsSystem</span></td><td><code>57ea8acc10183a14</code></td></tr><tr><td><span class="el_class">okhttp3.EventListener</span></td><td><code>c50bd229b1b00f1b</code></td></tr><tr><td><span class="el_class">okhttp3.EventListener.Companion</span></td><td><code>6dbc254653db2bef</code></td></tr><tr><td><span class="el_class">okhttp3.EventListener.Companion.NONE.1</span></td><td><code>a0f6885b341318d2</code></td></tr><tr><td><span class="el_class">okhttp3.Headers</span></td><td><code>9fe4d16b6a6b95bc</code></td></tr><tr><td><span class="el_class">okhttp3.Headers.Builder</span></td><td><code>c2f1e637f0b284dc</code></td></tr><tr><td><span class="el_class">okhttp3.Headers.Companion</span></td><td><code>6f72a0bb0c6942e6</code></td></tr><tr><td><span class="el_class">okhttp3.HttpUrl</span></td><td><code>99ea0d124e1ae019</code></td></tr><tr><td><span class="el_class">okhttp3.HttpUrl.Builder</span></td><td><code>73e51c8dc417155e</code></td></tr><tr><td><span class="el_class">okhttp3.HttpUrl.Builder.Companion</span></td><td><code>882623dea99153d4</code></td></tr><tr><td><span class="el_class">okhttp3.HttpUrl.Companion</span></td><td><code>f91c1d7d580d118b</code></td></tr><tr><td><span class="el_class">okhttp3.MediaType</span></td><td><code>874da141e814eed9</code></td></tr><tr><td><span class="el_class">okhttp3.MediaType.Companion</span></td><td><code>35465bbaa840d19e</code></td></tr><tr><td><span class="el_class">okhttp3.OkHttpClient</span></td><td><code>4c8aacb81cf3525b</code></td></tr><tr><td><span class="el_class">okhttp3.OkHttpClient.Builder</span></td><td><code>7efbd1cc56f32c47</code></td></tr><tr><td><span class="el_class">okhttp3.OkHttpClient.Companion</span></td><td><code>9b723979b6fa78c0</code></td></tr><tr><td><span class="el_class">okhttp3.Protocol</span></td><td><code>f6ccc44e4e8bfa3c</code></td></tr><tr><td><span class="el_class">okhttp3.Protocol.Companion</span></td><td><code>14f6b1975836f78b</code></td></tr><tr><td><span class="el_class">okhttp3.Request</span></td><td><code>3e8a303312c674b2</code></td></tr><tr><td><span class="el_class">okhttp3.Request.Builder</span></td><td><code>7ca03188b1ac8df6</code></td></tr><tr><td><span class="el_class">okhttp3.RequestBody</span></td><td><code>618beef5446695e5</code></td></tr><tr><td><span class="el_class">okhttp3.RequestBody.Companion</span></td><td><code>416268e6649d363b</code></td></tr><tr><td><span class="el_class">okhttp3.RequestBody.Companion.toRequestBody.2</span></td><td><code>3c11ab4750a670f7</code></td></tr><tr><td><span class="el_class">okhttp3.Response</span></td><td><code>aceccadfe4cfad09</code></td></tr><tr><td><span class="el_class">okhttp3.Response.Builder</span></td><td><code>78bf45e20b95615a</code></td></tr><tr><td><span class="el_class">okhttp3.ResponseBody</span></td><td><code>e3132fdf997fcda9</code></td></tr><tr><td><span class="el_class">okhttp3.ResponseBody.Companion</span></td><td><code>b06a628c80e77ce5</code></td></tr><tr><td><span class="el_class">okhttp3.ResponseBody.Companion.asResponseBody.1</span></td><td><code>031b6168cd0b84bd</code></td></tr><tr><td><span class="el_class">okhttp3.Route</span></td><td><code>2819d8367671142d</code></td></tr><tr><td><span class="el_class">okhttp3.TlsVersion</span></td><td><code>24674983c0f57201</code></td></tr><tr><td><span class="el_class">okhttp3.TlsVersion.Companion</span></td><td><code>f3c8cd05f5fa3931</code></td></tr><tr><td><span class="el_class">okhttp3.internal.HostnamesKt</span></td><td><code>b8fda54f93e9197b</code></td></tr><tr><td><span class="el_class">okhttp3.internal.Internal</span></td><td><code>cb1908d36b3b8430</code></td></tr><tr><td><span class="el_class">okhttp3.internal.Util</span></td><td><code>27b15bd4a5173807</code></td></tr><tr><td><span class="el_class">okhttp3.internal.Util.asFactory.1</span></td><td><code>908795f49264ea5a</code></td></tr><tr><td><span class="el_class">okhttp3.internal.Util.threadFactory.1</span></td><td><code>3127b8da07a224af</code></td></tr><tr><td><span class="el_class">okhttp3.internal.authenticator.JavaNetAuthenticator</span></td><td><code>6fb37d8f935d7f62</code></td></tr><tr><td><span class="el_class">okhttp3.internal.cache.CacheInterceptor</span></td><td><code>78b7581cba606985</code></td></tr><tr><td><span class="el_class">okhttp3.internal.cache.CacheInterceptor.Companion</span></td><td><code>0bcefac127d429a2</code></td></tr><tr><td><span class="el_class">okhttp3.internal.cache.CacheStrategy</span></td><td><code>72396fb73b82b435</code></td></tr><tr><td><span class="el_class">okhttp3.internal.cache.CacheStrategy.Companion</span></td><td><code>cdd1a30f783fec6c</code></td></tr><tr><td><span class="el_class">okhttp3.internal.cache.CacheStrategy.Factory</span></td><td><code>ca20c762c72f5b6a</code></td></tr><tr><td><span class="el_class">okhttp3.internal.concurrent.Task</span></td><td><code>3035d445f54280ef</code></td></tr><tr><td><span class="el_class">okhttp3.internal.concurrent.TaskQueue</span></td><td><code>9b48d32901fab490</code></td></tr><tr><td><span class="el_class">okhttp3.internal.concurrent.TaskRunner</span></td><td><code>2e9c17ecd934a52c</code></td></tr><tr><td><span class="el_class">okhttp3.internal.concurrent.TaskRunner.Companion</span></td><td><code>fd1b7f5504dab12a</code></td></tr><tr><td><span class="el_class">okhttp3.internal.concurrent.TaskRunner.RealBackend</span></td><td><code>c9a7795b660070fc</code></td></tr><tr><td><span class="el_class">okhttp3.internal.concurrent.TaskRunner.runnable.1</span></td><td><code>4eede62df92750a3</code></td></tr><tr><td><span class="el_class">okhttp3.internal.connection.ConnectInterceptor</span></td><td><code>29a0c71ed011f275</code></td></tr><tr><td><span class="el_class">okhttp3.internal.connection.ConnectionSpecSelector</span></td><td><code>220fdf37c5dd96ec</code></td></tr><tr><td><span class="el_class">okhttp3.internal.connection.Exchange</span></td><td><code>20e3be68478fbd2c</code></td></tr><tr><td><span class="el_class">okhttp3.internal.connection.Exchange.RequestBodySink</span></td><td><code>5e344f4cf352d0be</code></td></tr><tr><td><span class="el_class">okhttp3.internal.connection.Exchange.ResponseBodySource</span></td><td><code>b28bfbefb804a8a7</code></td></tr><tr><td><span class="el_class">okhttp3.internal.connection.ExchangeFinder</span></td><td><code>418ec53ee457e120</code></td></tr><tr><td><span class="el_class">okhttp3.internal.connection.RealCall</span></td><td><code>6cc466ba5f516977</code></td></tr><tr><td><span class="el_class">okhttp3.internal.connection.RealCall.CallReference</span></td><td><code>bf0e9f43952ee7c0</code></td></tr><tr><td><span class="el_class">okhttp3.internal.connection.RealCall.timeout.1</span></td><td><code>237fa10d8faabfca</code></td></tr><tr><td><span class="el_class">okhttp3.internal.connection.RealConnection</span></td><td><code>fa88a771b704b03d</code></td></tr><tr><td><span class="el_class">okhttp3.internal.connection.RealConnection.Companion</span></td><td><code>1f1b73546c4a9909</code></td></tr><tr><td><span class="el_class">okhttp3.internal.connection.RealConnection.WhenMappings</span></td><td><code>ce8efb1bbe5c5685</code></td></tr><tr><td><span class="el_class">okhttp3.internal.connection.RealConnectionPool</span></td><td><code>1b969936df171dd5</code></td></tr><tr><td><span class="el_class">okhttp3.internal.connection.RealConnectionPool.Companion</span></td><td><code>cb99e5e893cd5aec</code></td></tr><tr><td><span class="el_class">okhttp3.internal.connection.RealConnectionPool.cleanupTask.1</span></td><td><code>1d203298adac2d50</code></td></tr><tr><td><span class="el_class">okhttp3.internal.connection.RouteDatabase</span></td><td><code>d7cc2c8549603d5b</code></td></tr><tr><td><span class="el_class">okhttp3.internal.connection.RouteSelector</span></td><td><code>c8c0fd3d4a53238c</code></td></tr><tr><td><span class="el_class">okhttp3.internal.connection.RouteSelector.Companion</span></td><td><code>2c608728bff4afcf</code></td></tr><tr><td><span class="el_class">okhttp3.internal.connection.RouteSelector.Selection</span></td><td><code>4773a6cd5fa5d081</code></td></tr><tr><td><span class="el_class">okhttp3.internal.connection.RouteSelector.resetNextProxy.1</span></td><td><code>adf5acad15def78a</code></td></tr><tr><td><span class="el_class">okhttp3.internal.duplex.MwsDuplexAccess</span></td><td><code>523cf870a95dcfbe</code></td></tr><tr><td><span class="el_class">okhttp3.internal.duplex.MwsDuplexAccess.Companion</span></td><td><code>020f3b30238665b8</code></td></tr><tr><td><span class="el_class">okhttp3.internal.http.BridgeInterceptor</span></td><td><code>de90802166d12946</code></td></tr><tr><td><span class="el_class">okhttp3.internal.http.CallServerInterceptor</span></td><td><code>9ad006f91ccbe01a</code></td></tr><tr><td><span class="el_class">okhttp3.internal.http.HttpHeaders</span></td><td><code>8d03f42f0ae17001</code></td></tr><tr><td><span class="el_class">okhttp3.internal.http.HttpMethod</span></td><td><code>537784350705c1b4</code></td></tr><tr><td><span class="el_class">okhttp3.internal.http.RealInterceptorChain</span></td><td><code>107dac0f4a2cb720</code></td></tr><tr><td><span class="el_class">okhttp3.internal.http.RealResponseBody</span></td><td><code>eb17742c80026772</code></td></tr><tr><td><span class="el_class">okhttp3.internal.http.RequestLine</span></td><td><code>f3041b5ca0fc10cd</code></td></tr><tr><td><span class="el_class">okhttp3.internal.http.RetryAndFollowUpInterceptor</span></td><td><code>9bc524d614bd30f0</code></td></tr><tr><td><span class="el_class">okhttp3.internal.http.RetryAndFollowUpInterceptor.Companion</span></td><td><code>ec18c1a19d925170</code></td></tr><tr><td><span class="el_class">okhttp3.internal.http.StatusLine</span></td><td><code>0283612ddc9f8a99</code></td></tr><tr><td><span class="el_class">okhttp3.internal.http.StatusLine.Companion</span></td><td><code>e5ebdfa1d04e1db5</code></td></tr><tr><td><span class="el_class">okhttp3.internal.http1.HeadersReader</span></td><td><code>b67a881531c9d39a</code></td></tr><tr><td><span class="el_class">okhttp3.internal.http1.HeadersReader.Companion</span></td><td><code>73ac2dbb1d86f6d8</code></td></tr><tr><td><span class="el_class">okhttp3.internal.http1.Http1ExchangeCodec</span></td><td><code>d06ca856de43bb66</code></td></tr><tr><td><span class="el_class">okhttp3.internal.http1.Http1ExchangeCodec.AbstractSource</span></td><td><code>e2492126a4efcf5c</code></td></tr><tr><td><span class="el_class">okhttp3.internal.http1.Http1ExchangeCodec.Companion</span></td><td><code>9aa724b7125aaf35</code></td></tr><tr><td><span class="el_class">okhttp3.internal.http1.Http1ExchangeCodec.FixedLengthSource</span></td><td><code>613bf89dde7fd5e3</code></td></tr><tr><td><span class="el_class">okhttp3.internal.http1.Http1ExchangeCodec.KnownLengthSink</span></td><td><code>195a992d475ef50f</code></td></tr><tr><td><span class="el_class">okhttp3.internal.http2.Http2Connection.Listener</span></td><td><code>3213555447ac5585</code></td></tr><tr><td><span class="el_class">okhttp3.internal.http2.Http2Connection.Listener.Companion</span></td><td><code>eab789f80da48ba1</code></td></tr><tr><td><span class="el_class">okhttp3.internal.http2.Http2Connection.Listener.Companion.REFUSE_INCOMING_STREAMS.1</span></td><td><code>b6fa3c3bfc568597</code></td></tr><tr><td><span class="el_class">okhttp3.internal.http2.Settings</span></td><td><code>b3c4347c02e4e8fb</code></td></tr><tr><td><span class="el_class">okhttp3.internal.http2.Settings.Companion</span></td><td><code>249ee3bc86cf27f8</code></td></tr><tr><td><span class="el_class">okhttp3.internal.platform.Jdk9Platform</span></td><td><code>60629ea2b05a9410</code></td></tr><tr><td><span class="el_class">okhttp3.internal.platform.Jdk9Platform.Companion</span></td><td><code>8c2e2601f3744945</code></td></tr><tr><td><span class="el_class">okhttp3.internal.platform.Platform</span></td><td><code>bbf6e3b6adaf1f82</code></td></tr><tr><td><span class="el_class">okhttp3.internal.platform.Platform.Companion</span></td><td><code>e58f6d70b79ff3e7</code></td></tr><tr><td><span class="el_class">okhttp3.internal.tls.BasicCertificateChainCleaner</span></td><td><code>d4d8478e117b3e72</code></td></tr><tr><td><span class="el_class">okhttp3.internal.tls.BasicCertificateChainCleaner.Companion</span></td><td><code>fcbcc8ce2f7548ca</code></td></tr><tr><td><span class="el_class">okhttp3.internal.tls.BasicTrustRootIndex</span></td><td><code>127de4b4d7e5b30f</code></td></tr><tr><td><span class="el_class">okhttp3.internal.tls.CertificateChainCleaner</span></td><td><code>548bee962c20236c</code></td></tr><tr><td><span class="el_class">okhttp3.internal.tls.CertificateChainCleaner.Companion</span></td><td><code>146f80aa989eebf8</code></td></tr><tr><td><span class="el_class">okhttp3.internal.tls.OkHostnameVerifier</span></td><td><code>5b11bce6d19b3341</code></td></tr><tr><td><span class="el_class">okhttp3.mockwebserver.Dispatcher</span></td><td><code>aff64f7f4fb517f9</code></td></tr><tr><td><span class="el_class">okhttp3.mockwebserver.MockResponse</span></td><td><code>93abce3066257c12</code></td></tr><tr><td><span class="el_class">okhttp3.mockwebserver.MockResponse.Companion</span></td><td><code>37be77554120e61b</code></td></tr><tr><td><span class="el_class">okhttp3.mockwebserver.MockWebServer</span></td><td><code>7b1b5d0bcc049e58</code></td></tr><tr><td><span class="el_class">okhttp3.mockwebserver.MockWebServer.Companion</span></td><td><code>b13e4e5207a2113c</code></td></tr><tr><td><span class="el_class">okhttp3.mockwebserver.MockWebServer.Companion.1</span></td><td><code>df2fcce43424d194</code></td></tr><tr><td><span class="el_class">okhttp3.mockwebserver.MockWebServer.Companion.UNTRUSTED_TRUST_MANAGER.1</span></td><td><code>27fea65f065fc5c6</code></td></tr><tr><td><span class="el_class">okhttp3.mockwebserver.MockWebServer.SocketHandler</span></td><td><code>f579e8256a1bf2ae</code></td></tr><tr><td><span class="el_class">okhttp3.mockwebserver.MockWebServer.SocketHandler.WhenMappings</span></td><td><code>b03aba3352143f97</code></td></tr><tr><td><span class="el_class">okhttp3.mockwebserver.MockWebServer.TruncatingBuffer</span></td><td><code>a9180217910f73ca</code></td></tr><tr><td><span class="el_class">okhttp3.mockwebserver.MockWebServer.serveConnection..inlined.execute.1</span></td><td><code>b22cb13790b142fe</code></td></tr><tr><td><span class="el_class">okhttp3.mockwebserver.MockWebServer.start..inlined.execute.1</span></td><td><code>ad7ecd64c174e06e</code></td></tr><tr><td><span class="el_class">okhttp3.mockwebserver.QueueDispatcher</span></td><td><code>6e0ffd5bc36b53f1</code></td></tr><tr><td><span class="el_class">okhttp3.mockwebserver.QueueDispatcher.Companion</span></td><td><code>684471ccb35d97a4</code></td></tr><tr><td><span class="el_class">okhttp3.mockwebserver.RecordedRequest</span></td><td><code>7a309ce3394e2ede</code></td></tr><tr><td><span class="el_class">okhttp3.mockwebserver.SocketPolicy</span></td><td><code>7b389e4b08ddb69f</code></td></tr><tr><td><span class="el_class">okio.-Platform</span></td><td><code>d36293560f59c7bb</code></td></tr><tr><td><span class="el_class">okio.-Util</span></td><td><code>ba142450c6ecd1ef</code></td></tr><tr><td><span class="el_class">okio.AsyncTimeout</span></td><td><code>a39a89cd6a777c6b</code></td></tr><tr><td><span class="el_class">okio.AsyncTimeout.Companion</span></td><td><code>9aa50846b29417d9</code></td></tr><tr><td><span class="el_class">okio.AsyncTimeout.Watchdog</span></td><td><code>29135f6d0b8a71d3</code></td></tr><tr><td><span class="el_class">okio.AsyncTimeout.sink.1</span></td><td><code>0a463cb04014ca8b</code></td></tr><tr><td><span class="el_class">okio.AsyncTimeout.source.1</span></td><td><code>9da588e55ec2908e</code></td></tr><tr><td><span class="el_class">okio.Buffer</span></td><td><code>c54a8ca48f4b17cc</code></td></tr><tr><td><span class="el_class">okio.ByteString</span></td><td><code>a4ef4be72543fed3</code></td></tr><tr><td><span class="el_class">okio.ByteString.Companion</span></td><td><code>27f1800ebb72a350</code></td></tr><tr><td><span class="el_class">okio.ForwardingSink</span></td><td><code>4102cc149c836c28</code></td></tr><tr><td><span class="el_class">okio.ForwardingSource</span></td><td><code>4f6f1c08b1a35a92</code></td></tr><tr><td><span class="el_class">okio.ForwardingTimeout</span></td><td><code>aa6760c2eee504a3</code></td></tr><tr><td><span class="el_class">okio.InputStreamSource</span></td><td><code>6253214b113fda89</code></td></tr><tr><td><span class="el_class">okio.Okio</span></td><td><code>a29f62259282e676</code></td></tr><tr><td><span class="el_class">okio.Okio__JvmOkioKt</span></td><td><code>d517ea5449478cf8</code></td></tr><tr><td><span class="el_class">okio.Okio__OkioKt</span></td><td><code>74b1ac198904e148</code></td></tr><tr><td><span class="el_class">okio.Options</span></td><td><code>5ecf225e898cc4ad</code></td></tr><tr><td><span class="el_class">okio.Options.Companion</span></td><td><code>9bd9cda622dafec9</code></td></tr><tr><td><span class="el_class">okio.OutputStreamSink</span></td><td><code>06426788baff45b0</code></td></tr><tr><td><span class="el_class">okio.RealBufferedSink</span></td><td><code>af6fb6b4014d8e09</code></td></tr><tr><td><span class="el_class">okio.RealBufferedSource</span></td><td><code>44e65b15f70b5b59</code></td></tr><tr><td><span class="el_class">okio.Segment</span></td><td><code>bb15f279e614a76b</code></td></tr><tr><td><span class="el_class">okio.Segment.Companion</span></td><td><code>b393eb92dd98b03b</code></td></tr><tr><td><span class="el_class">okio.SegmentPool</span></td><td><code>f2f52128ebd8f89a</code></td></tr><tr><td><span class="el_class">okio.SocketAsyncTimeout</span></td><td><code>58db2dc9634d48de</code></td></tr><tr><td><span class="el_class">okio.Timeout</span></td><td><code>988b7cdba32683c4</code></td></tr><tr><td><span class="el_class">okio.Timeout.Companion</span></td><td><code>779f91eb9385a6e3</code></td></tr><tr><td><span class="el_class">okio.Timeout.Companion.NONE.1</span></td><td><code>909c52e3a357030c</code></td></tr><tr><td><span class="el_class">okio.internal.BufferKt</span></td><td><code>da381c13bb33ee63</code></td></tr><tr><td><span class="el_class">okio.internal.ByteStringKt</span></td><td><code>6ed15eb9808248c5</code></td></tr><tr><td><span class="el_class">org.apache.commons.codec.binary.Hex</span></td><td><code>05945e51666f7b57</code></td></tr><tr><td><span class="el_class">org.apache.maven.surefire.NonAbstractClassFilter</span></td><td><code>1e5a6074e0969c3f</code></td></tr><tr><td><span class="el_class">org.apache.maven.surefire.booter.BaseProviderFactory</span></td><td><code>7ba29961dfebc4ba</code></td></tr><tr><td><span class="el_class">org.apache.maven.surefire.booter.BooterDeserializer</span></td><td><code>442f429c26b69928</code></td></tr><tr><td><span class="el_class">org.apache.maven.surefire.booter.ClassLoaderConfiguration</span></td><td><code>6e06a3a75082a947</code></td></tr><tr><td><span class="el_class">org.apache.maven.surefire.booter.Classpath</span></td><td><code>e71f884825972c25</code></td></tr><tr><td><span class="el_class">org.apache.maven.surefire.booter.ClasspathConfiguration</span></td><td><code>b4f746df2629957b</code></td></tr><tr><td><span class="el_class">org.apache.maven.surefire.booter.ForkedBooter</span></td><td><code>812225234cbb3325</code></td></tr><tr><td><span class="el_class">org.apache.maven.surefire.booter.ForkedBooter.1</span></td><td><code>bdfcde0181550217</code></td></tr><tr><td><span class="el_class">org.apache.maven.surefire.booter.ForkingReporterFactory</span></td><td><code>076c5152b5fb9c11</code></td></tr><tr><td><span class="el_class">org.apache.maven.surefire.booter.ForkingRunListener</span></td><td><code>b96d9f327ffdaaf4</code></td></tr><tr><td><span class="el_class">org.apache.maven.surefire.booter.IsolatedClassLoader</span></td><td><code>ae0066301f45e487</code></td></tr><tr><td><span class="el_class">org.apache.maven.surefire.booter.JdkReflector</span></td><td><code>d6f5d6728272255a</code></td></tr><tr><td><span class="el_class">org.apache.maven.surefire.booter.PropertiesWrapper</span></td><td><code>e77715f3c27548eb</code></td></tr><tr><td><span class="el_class">org.apache.maven.surefire.booter.ProviderConfiguration</span></td><td><code>d735e73a98b2dd3b</code></td></tr><tr><td><span class="el_class">org.apache.maven.surefire.booter.ProviderFactory</span></td><td><code>10eaa6c74a289a7c</code></td></tr><tr><td><span class="el_class">org.apache.maven.surefire.booter.ProviderFactory.ProviderProxy</span></td><td><code>7a176b1eaeb3c61e</code></td></tr><tr><td><span class="el_class">org.apache.maven.surefire.booter.StartupConfiguration</span></td><td><code>b4437ea6e48d2360</code></td></tr><tr><td><span class="el_class">org.apache.maven.surefire.booter.SurefireReflector</span></td><td><code>073f35a652554917</code></td></tr><tr><td><span class="el_class">org.apache.maven.surefire.booter.SystemPropertyManager</span></td><td><code>586f8b734e8457bd</code></td></tr><tr><td><span class="el_class">org.apache.maven.surefire.booter.TypeEncodedValue</span></td><td><code>520e6294ac61be38</code></td></tr><tr><td><span class="el_class">org.apache.maven.surefire.common.junit3.JUnit3TestChecker</span></td><td><code>7102b454a4fc6b40</code></td></tr><tr><td><span class="el_class">org.apache.maven.surefire.common.junit4.JUnit4Reflector</span></td><td><code>6be9a9a787b76734</code></td></tr><tr><td><span class="el_class">org.apache.maven.surefire.common.junit4.JUnit4RunListener</span></td><td><code>b6d5ab51bf0a9446</code></td></tr><tr><td><span class="el_class">org.apache.maven.surefire.common.junit4.JUnit4RunListenerFactory</span></td><td><code>fc6a4376532e9afe</code></td></tr><tr><td><span class="el_class">org.apache.maven.surefire.common.junit4.JUnit4TestChecker</span></td><td><code>db394de547f23665</code></td></tr><tr><td><span class="el_class">org.apache.maven.surefire.junit4.JUnit4Provider</span></td><td><code>bd99b88d79022720</code></td></tr><tr><td><span class="el_class">org.apache.maven.surefire.providerapi.AbstractProvider</span></td><td><code>41999770f94bdbc3</code></td></tr><tr><td><span class="el_class">org.apache.maven.surefire.report.ConsoleOutputCapture</span></td><td><code>8698f1f1b0d3d1ac</code></td></tr><tr><td><span class="el_class">org.apache.maven.surefire.report.ConsoleOutputCapture.ForwardingPrintStream</span></td><td><code>07ce062280b3f566</code></td></tr><tr><td><span class="el_class">org.apache.maven.surefire.report.ReporterConfiguration</span></td><td><code>af3c1ea7f8012a4f</code></td></tr><tr><td><span class="el_class">org.apache.maven.surefire.report.SimpleReportEntry</span></td><td><code>c4fe7d3fd77a7f9f</code></td></tr><tr><td><span class="el_class">org.apache.maven.surefire.suite.RunResult</span></td><td><code>4f4ef8baf9a9eaed</code></td></tr><tr><td><span class="el_class">org.apache.maven.surefire.testset.DirectoryScannerParameters</span></td><td><code>dfed7046a1155a37</code></td></tr><tr><td><span class="el_class">org.apache.maven.surefire.testset.RunOrderParameters</span></td><td><code>0c8e07ddcfeb0d3f</code></td></tr><tr><td><span class="el_class">org.apache.maven.surefire.testset.TestArtifactInfo</span></td><td><code>3385503f66e44602</code></td></tr><tr><td><span class="el_class">org.apache.maven.surefire.testset.TestRequest</span></td><td><code>c341fc8ac6d6267e</code></td></tr><tr><td><span class="el_class">org.apache.maven.surefire.util.DefaultRunOrderCalculator</span></td><td><code>6eae8682ccf1a30a</code></td></tr><tr><td><span class="el_class">org.apache.maven.surefire.util.DefaultScanResult</span></td><td><code>61b36318792d6986</code></td></tr><tr><td><span class="el_class">org.apache.maven.surefire.util.ReflectionUtils</span></td><td><code>f3750af81644d0aa</code></td></tr><tr><td><span class="el_class">org.apache.maven.surefire.util.RunOrder</span></td><td><code>d922f6bc92e209d9</code></td></tr><tr><td><span class="el_class">org.apache.maven.surefire.util.TestsToRun</span></td><td><code>0f98b18d5c7c18ab</code></td></tr><tr><td><span class="el_class">org.apache.maven.surefire.util.UrlUtils</span></td><td><code>1ff98da53c689785</code></td></tr><tr><td><span class="el_class">org.apache.maven.surefire.util.internal.StringUtils</span></td><td><code>517a9dd6a335139c</code></td></tr><tr><td><span class="el_class">org.apache.xerces.impl.Constants</span></td><td><code>a815025e8bfc3641</code></td></tr><tr><td><span class="el_class">org.apache.xerces.impl.Constants.ArrayEnumeration</span></td><td><code>929d153955f16a18</code></td></tr><tr><td><span class="el_class">org.apache.xerces.impl.XMLDTDScannerImpl</span></td><td><code>df23d392ec7d9eda</code></td></tr><tr><td><span class="el_class">org.apache.xerces.impl.XMLDocumentFragmentScannerImpl</span></td><td><code>42c4752e9d14ffa5</code></td></tr><tr><td><span class="el_class">org.apache.xerces.impl.XMLDocumentFragmentScannerImpl.ElementStack</span></td><td><code>5092662a55f68b1b</code></td></tr><tr><td><span class="el_class">org.apache.xerces.impl.XMLDocumentFragmentScannerImpl.FragmentContentDispatcher</span></td><td><code>22ad5d0f0423d856</code></td></tr><tr><td><span class="el_class">org.apache.xerces.impl.XMLDocumentScannerImpl</span></td><td><code>f95b8336839d7f50</code></td></tr><tr><td><span class="el_class">org.apache.xerces.impl.XMLDocumentScannerImpl.ContentDispatcher</span></td><td><code>5e8688b4476f7d87</code></td></tr><tr><td><span class="el_class">org.apache.xerces.impl.XMLDocumentScannerImpl.DTDDispatcher</span></td><td><code>acbfc8ada70ea6e4</code></td></tr><tr><td><span class="el_class">org.apache.xerces.impl.XMLDocumentScannerImpl.PrologDispatcher</span></td><td><code>05017bcceeaf2e50</code></td></tr><tr><td><span class="el_class">org.apache.xerces.impl.XMLDocumentScannerImpl.TrailingMiscDispatcher</span></td><td><code>4ac4ac375f68cb9a</code></td></tr><tr><td><span class="el_class">org.apache.xerces.impl.XMLDocumentScannerImpl.XMLDeclDispatcher</span></td><td><code>2399f75b5af9e12e</code></td></tr><tr><td><span class="el_class">org.apache.xerces.impl.XMLEntityManager</span></td><td><code>981fbcf7ff00f984</code></td></tr><tr><td><span class="el_class">org.apache.xerces.impl.XMLEntityManager.1</span></td><td><code>bb865e7bc2c442b3</code></td></tr><tr><td><span class="el_class">org.apache.xerces.impl.XMLEntityManager.ByteBufferPool</span></td><td><code>a322defbaf63fdc0</code></td></tr><tr><td><span class="el_class">org.apache.xerces.impl.XMLEntityManager.CharacterBuffer</span></td><td><code>6462023b58b8cc81</code></td></tr><tr><td><span class="el_class">org.apache.xerces.impl.XMLEntityManager.CharacterBufferPool</span></td><td><code>c9afee8a27afdf8b</code></td></tr><tr><td><span class="el_class">org.apache.xerces.impl.XMLEntityManager.Entity</span></td><td><code>8d6dc89d3fc555e5</code></td></tr><tr><td><span class="el_class">org.apache.xerces.impl.XMLEntityManager.RewindableInputStream</span></td><td><code>afbaf76d3ae9b52f</code></td></tr><tr><td><span class="el_class">org.apache.xerces.impl.XMLEntityManager.ScannedEntity</span></td><td><code>f5d34eec17a4135f</code></td></tr><tr><td><span class="el_class">org.apache.xerces.impl.XMLEntityScanner</span></td><td><code>d5c88d7e2efbe754</code></td></tr><tr><td><span class="el_class">org.apache.xerces.impl.XMLEntityScanner.1</span></td><td><code>ff0395540810acc0</code></td></tr><tr><td><span class="el_class">org.apache.xerces.impl.XMLErrorReporter</span></td><td><code>88fcd7c1279b18df</code></td></tr><tr><td><span class="el_class">org.apache.xerces.impl.XMLNSDocumentScannerImpl</span></td><td><code>fde0185d08fbe400</code></td></tr><tr><td><span class="el_class">org.apache.xerces.impl.XMLNSDocumentScannerImpl.NSContentDispatcher</span></td><td><code>02d084bd31e3aa1c</code></td></tr><tr><td><span class="el_class">org.apache.xerces.impl.XMLScanner</span></td><td><code>d77ca2d5a39e5b3c</code></td></tr><tr><td><span class="el_class">org.apache.xerces.impl.XMLVersionDetector</span></td><td><code>0923e4113106f670</code></td></tr><tr><td><span class="el_class">org.apache.xerces.impl.dtd.DTDGrammarBucket</span></td><td><code>df8b1dc6ad7ef837</code></td></tr><tr><td><span class="el_class">org.apache.xerces.impl.dtd.XMLAttributeDecl</span></td><td><code>46e4635b06cbf39a</code></td></tr><tr><td><span class="el_class">org.apache.xerces.impl.dtd.XMLDTDDescription</span></td><td><code>b489ecda2f5a9495</code></td></tr><tr><td><span class="el_class">org.apache.xerces.impl.dtd.XMLDTDProcessor</span></td><td><code>c5747140967d0577</code></td></tr><tr><td><span class="el_class">org.apache.xerces.impl.dtd.XMLDTDValidator</span></td><td><code>6c571eb50eee95f5</code></td></tr><tr><td><span class="el_class">org.apache.xerces.impl.dtd.XMLElementDecl</span></td><td><code>babada4539fdcc2e</code></td></tr><tr><td><span class="el_class">org.apache.xerces.impl.dtd.XMLEntityDecl</span></td><td><code>21e05a3c0abdeb50</code></td></tr><tr><td><span class="el_class">org.apache.xerces.impl.dtd.XMLNSDTDValidator</span></td><td><code>1a8a64e1b4a92c66</code></td></tr><tr><td><span class="el_class">org.apache.xerces.impl.dtd.XMLSimpleType</span></td><td><code>b28eaccdd7ac5d6c</code></td></tr><tr><td><span class="el_class">org.apache.xerces.impl.dv.DTDDVFactory</span></td><td><code>cf818429315036aa</code></td></tr><tr><td><span class="el_class">org.apache.xerces.impl.dv.ObjectFactory</span></td><td><code>476c28770bcbbbb5</code></td></tr><tr><td><span class="el_class">org.apache.xerces.impl.dv.SecuritySupport</span></td><td><code>18f8e769c75df574</code></td></tr><tr><td><span class="el_class">org.apache.xerces.impl.dv.SecuritySupport.1</span></td><td><code>1b8738af9f965e81</code></td></tr><tr><td><span class="el_class">org.apache.xerces.impl.dv.SecuritySupport.2</span></td><td><code>03ded5f52fdaaa44</code></td></tr><tr><td><span class="el_class">org.apache.xerces.impl.dv.dtd.DTDDVFactoryImpl</span></td><td><code>0b0440647e9e800e</code></td></tr><tr><td><span class="el_class">org.apache.xerces.impl.dv.dtd.ENTITYDatatypeValidator</span></td><td><code>5e69f788c026a00f</code></td></tr><tr><td><span class="el_class">org.apache.xerces.impl.dv.dtd.IDDatatypeValidator</span></td><td><code>cb7a5922e7cd926a</code></td></tr><tr><td><span class="el_class">org.apache.xerces.impl.dv.dtd.IDREFDatatypeValidator</span></td><td><code>6bd828a544290567</code></td></tr><tr><td><span class="el_class">org.apache.xerces.impl.dv.dtd.ListDatatypeValidator</span></td><td><code>f31a06ca1a58a9eb</code></td></tr><tr><td><span class="el_class">org.apache.xerces.impl.dv.dtd.NMTOKENDatatypeValidator</span></td><td><code>b215e61cfcb76a64</code></td></tr><tr><td><span class="el_class">org.apache.xerces.impl.dv.dtd.NOTATIONDatatypeValidator</span></td><td><code>b693001e6fffd70e</code></td></tr><tr><td><span class="el_class">org.apache.xerces.impl.dv.dtd.StringDatatypeValidator</span></td><td><code>63695ebcff73b37f</code></td></tr><tr><td><span class="el_class">org.apache.xerces.impl.io.UTF8Reader</span></td><td><code>40d65b9ff029f01e</code></td></tr><tr><td><span class="el_class">org.apache.xerces.impl.msg.XMLMessageFormatter</span></td><td><code>9308971d48383433</code></td></tr><tr><td><span class="el_class">org.apache.xerces.impl.validation.ValidationManager</span></td><td><code>80820e1751c94dd9</code></td></tr><tr><td><span class="el_class">org.apache.xerces.impl.validation.ValidationState</span></td><td><code>4accdcc5718c1b8f</code></td></tr><tr><td><span class="el_class">org.apache.xerces.jaxp.SAXParserFactoryImpl</span></td><td><code>d547c9ded9586081</code></td></tr><tr><td><span class="el_class">org.apache.xerces.jaxp.SAXParserImpl</span></td><td><code>030b22ffd879ce27</code></td></tr><tr><td><span class="el_class">org.apache.xerces.jaxp.SAXParserImpl.JAXPSAXParser</span></td><td><code>58755dbc3b266b34</code></td></tr><tr><td><span class="el_class">org.apache.xerces.parsers.AbstractSAXParser</span></td><td><code>f0d6f020e5d75267</code></td></tr><tr><td><span class="el_class">org.apache.xerces.parsers.AbstractSAXParser.AttributesProxy</span></td><td><code>556c81522bf1f08c</code></td></tr><tr><td><span class="el_class">org.apache.xerces.parsers.AbstractSAXParser.LocatorProxy</span></td><td><code>947940eaaaf10d76</code></td></tr><tr><td><span class="el_class">org.apache.xerces.parsers.AbstractXMLDocumentParser</span></td><td><code>8863894dca746b5c</code></td></tr><tr><td><span class="el_class">org.apache.xerces.parsers.ObjectFactory</span></td><td><code>c5b9e35cca47fe2a</code></td></tr><tr><td><span class="el_class">org.apache.xerces.parsers.SAXParser</span></td><td><code>6297dd6cb4aebd4c</code></td></tr><tr><td><span class="el_class">org.apache.xerces.parsers.SecuritySupport</span></td><td><code>f9d232d57c39ff25</code></td></tr><tr><td><span class="el_class">org.apache.xerces.parsers.SecuritySupport.1</span></td><td><code>b1ca08ec9dbd4e98</code></td></tr><tr><td><span class="el_class">org.apache.xerces.parsers.SecuritySupport.2</span></td><td><code>b5c190edd9eb7465</code></td></tr><tr><td><span class="el_class">org.apache.xerces.parsers.SecuritySupport.4</span></td><td><code>ff65608800cc0f00</code></td></tr><tr><td><span class="el_class">org.apache.xerces.parsers.SecuritySupport.6</span></td><td><code>c57cafe3d88f5bf0</code></td></tr><tr><td><span class="el_class">org.apache.xerces.parsers.SecuritySupport.7</span></td><td><code>a09a354f38d98914</code></td></tr><tr><td><span class="el_class">org.apache.xerces.parsers.XIncludeAwareParserConfiguration</span></td><td><code>0d52edf1ea95ce8c</code></td></tr><tr><td><span class="el_class">org.apache.xerces.parsers.XML11Configuration</span></td><td><code>4232068250c66a72</code></td></tr><tr><td><span class="el_class">org.apache.xerces.parsers.XMLParser</span></td><td><code>5ea5d99c949266c4</code></td></tr><tr><td><span class="el_class">org.apache.xerces.util.AugmentationsImpl</span></td><td><code>0c1fe14da46bb416</code></td></tr><tr><td><span class="el_class">org.apache.xerces.util.AugmentationsImpl.AugmentationsItemsContainer</span></td><td><code>63b56783ead2f577</code></td></tr><tr><td><span class="el_class">org.apache.xerces.util.AugmentationsImpl.SmallContainer</span></td><td><code>3fd5ef329d7f0abb</code></td></tr><tr><td><span class="el_class">org.apache.xerces.util.EntityResolverWrapper</span></td><td><code>ae54928d232d4a0f</code></td></tr><tr><td><span class="el_class">org.apache.xerces.util.ErrorHandlerWrapper</span></td><td><code>ceb581598ca9223b</code></td></tr><tr><td><span class="el_class">org.apache.xerces.util.NamespaceSupport</span></td><td><code>2ec78d35e1dd6396</code></td></tr><tr><td><span class="el_class">org.apache.xerces.util.ParserConfigurationSettings</span></td><td><code>b2b8341658db5683</code></td></tr><tr><td><span class="el_class">org.apache.xerces.util.SymbolTable</span></td><td><code>c57500a0993e2cd1</code></td></tr><tr><td><span class="el_class">org.apache.xerces.util.SymbolTable.Entry</span></td><td><code>1ae25b103ff622ca</code></td></tr><tr><td><span class="el_class">org.apache.xerces.util.URI</span></td><td><code>884caaebb8950799</code></td></tr><tr><td><span class="el_class">org.apache.xerces.util.XMLAttributesImpl</span></td><td><code>5372548792f8d9a1</code></td></tr><tr><td><span class="el_class">org.apache.xerces.util.XMLAttributesImpl.Attribute</span></td><td><code>4be0ee5403a6dde4</code></td></tr><tr><td><span class="el_class">org.apache.xerces.util.XMLChar</span></td><td><code>471759fddc8dee13</code></td></tr><tr><td><span class="el_class">org.apache.xerces.util.XMLResourceIdentifierImpl</span></td><td><code>fbc9da91fce32602</code></td></tr><tr><td><span class="el_class">org.apache.xerces.util.XMLStringBuffer</span></td><td><code>98e81028fe44707d</code></td></tr><tr><td><span class="el_class">org.apache.xerces.util.XMLSymbols</span></td><td><code>fb3448511f7b090a</code></td></tr><tr><td><span class="el_class">org.apache.xerces.xni.NamespaceContext</span></td><td><code>a042cfa9211e0090</code></td></tr><tr><td><span class="el_class">org.apache.xerces.xni.QName</span></td><td><code>e1fbb8aed5fee97e</code></td></tr><tr><td><span class="el_class">org.apache.xerces.xni.XMLString</span></td><td><code>cd18fb32e73070cb</code></td></tr><tr><td><span class="el_class">org.apache.xerces.xni.parser.XMLInputSource</span></td><td><code>9c76a32c97e5c2fb</code></td></tr><tr><td><span class="el_class">org.json.JSONException</span></td><td><code>73926069d9fa4fad</code></td></tr><tr><td><span class="el_class">org.json.JSONObject</span></td><td><code>c8ee6217e5827b6e</code></td></tr><tr><td><span class="el_class">org.json.JSONObject.Null</span></td><td><code>df146a6607d77d33</code></td></tr><tr><td><span class="el_class">org.json.JSONTokener</span></td><td><code>fe373bba367b32d5</code></td></tr><tr><td><span class="el_class">org.junit.Assert</span></td><td><code>eda6db924019425b</code></td></tr><tr><td><span class="el_class">org.junit.internal.Checks</span></td><td><code>5f543b0bb87b92da</code></td></tr><tr><td><span class="el_class">org.junit.internal.MethodSorter</span></td><td><code>a26607ae067f7352</code></td></tr><tr><td><span class="el_class">org.junit.internal.MethodSorter.1</span></td><td><code>d3997b4bdb7889c1</code></td></tr><tr><td><span class="el_class">org.junit.internal.MethodSorter.2</span></td><td><code>c8e6351cbf098013</code></td></tr><tr><td><span class="el_class">org.junit.internal.builders.AllDefaultPossibilitiesBuilder</span></td><td><code>4f18a1d7932cb8ab</code></td></tr><tr><td><span class="el_class">org.junit.internal.builders.AnnotatedBuilder</span></td><td><code>0faf353d180c9332</code></td></tr><tr><td><span class="el_class">org.junit.internal.builders.IgnoredBuilder</span></td><td><code>e152f333c53967a6</code></td></tr><tr><td><span class="el_class">org.junit.internal.builders.JUnit3Builder</span></td><td><code>4a2cc8e608e1275e</code></td></tr><tr><td><span class="el_class">org.junit.internal.builders.JUnit4Builder</span></td><td><code>f2e00a3e1fc23005</code></td></tr><tr><td><span class="el_class">org.junit.internal.builders.SuiteMethodBuilder</span></td><td><code>1df136431e07e393</code></td></tr><tr><td><span class="el_class">org.junit.internal.requests.ClassRequest</span></td><td><code>47dbc61675e5a92e</code></td></tr><tr><td><span class="el_class">org.junit.internal.requests.ClassRequest.CustomAllDefaultPossibilitiesBuilder</span></td><td><code>ea1c269d9656f543</code></td></tr><tr><td><span class="el_class">org.junit.internal.requests.ClassRequest.CustomSuiteMethodBuilder</span></td><td><code>03d01020b1c503c7</code></td></tr><tr><td><span class="el_class">org.junit.internal.requests.MemoizingRequest</span></td><td><code>1e70801476dbab8f</code></td></tr><tr><td><span class="el_class">org.junit.internal.runners.model.EachTestNotifier</span></td><td><code>077481995383e000</code></td></tr><tr><td><span class="el_class">org.junit.internal.runners.model.ReflectiveCallable</span></td><td><code>d591724635588bcb</code></td></tr><tr><td><span class="el_class">org.junit.internal.runners.rules.RuleMemberValidator</span></td><td><code>95b5ee2068ec6875</code></td></tr><tr><td><span class="el_class">org.junit.internal.runners.rules.RuleMemberValidator.Builder</span></td><td><code>f24845fa6fd065af</code></td></tr><tr><td><span class="el_class">org.junit.internal.runners.rules.RuleMemberValidator.DeclaringClassMustBePublic</span></td><td><code>1de994463c748d89</code></td></tr><tr><td><span class="el_class">org.junit.internal.runners.rules.RuleMemberValidator.FieldMustBeARule</span></td><td><code>e24e9f59de6fe5b7</code></td></tr><tr><td><span class="el_class">org.junit.internal.runners.rules.RuleMemberValidator.FieldMustBeATestRule</span></td><td><code>690823bd2992f52e</code></td></tr><tr><td><span class="el_class">org.junit.internal.runners.rules.RuleMemberValidator.MemberMustBeNonStaticOrAlsoClassRule</span></td><td><code>1e703fb3e7f4e533</code></td></tr><tr><td><span class="el_class">org.junit.internal.runners.rules.RuleMemberValidator.MemberMustBePublic</span></td><td><code>806c174eb921b478</code></td></tr><tr><td><span class="el_class">org.junit.internal.runners.rules.RuleMemberValidator.MemberMustBeStatic</span></td><td><code>ac28a03dd36b2b5a</code></td></tr><tr><td><span class="el_class">org.junit.internal.runners.rules.RuleMemberValidator.MethodMustBeARule</span></td><td><code>88ea4a2237de2b8b</code></td></tr><tr><td><span class="el_class">org.junit.internal.runners.rules.RuleMemberValidator.MethodMustBeATestRule</span></td><td><code>9f4dd18a26005c18</code></td></tr><tr><td><span class="el_class">org.junit.internal.runners.statements.InvokeMethod</span></td><td><code>05a7aa636afa2c39</code></td></tr><tr><td><span class="el_class">org.junit.internal.runners.statements.RunBefores</span></td><td><code>ef86a61ca3ab20ba</code></td></tr><tr><td><span class="el_class">org.junit.rules.ExternalResource</span></td><td><code>fe234abb7ed11f04</code></td></tr><tr><td><span class="el_class">org.junit.runner.Description</span></td><td><code>1d6f7ddbbf223f9a</code></td></tr><tr><td><span class="el_class">org.junit.runner.Request</span></td><td><code>214d9ade1c7dc38d</code></td></tr><tr><td><span class="el_class">org.junit.runner.Result</span></td><td><code>ecf6c1c04298ff7d</code></td></tr><tr><td><span class="el_class">org.junit.runner.Result.Listener</span></td><td><code>cf649a4ffbe55db9</code></td></tr><tr><td><span class="el_class">org.junit.runner.Runner</span></td><td><code>f5abacc70e2e08a4</code></td></tr><tr><td><span class="el_class">org.junit.runner.notification.RunListener</span></td><td><code>69d2c783b42f6720</code></td></tr><tr><td><span class="el_class">org.junit.runner.notification.RunNotifier</span></td><td><code>f6313076e2224ebb</code></td></tr><tr><td><span class="el_class">org.junit.runner.notification.RunNotifier.1</span></td><td><code>e31025c12b4dbdee</code></td></tr><tr><td><span class="el_class">org.junit.runner.notification.RunNotifier.2</span></td><td><code>4c7314c6d595dc3e</code></td></tr><tr><td><span class="el_class">org.junit.runner.notification.RunNotifier.3</span></td><td><code>df2bada5cb3794f3</code></td></tr><tr><td><span class="el_class">org.junit.runner.notification.RunNotifier.4</span></td><td><code>fbdd84204c215de7</code></td></tr><tr><td><span class="el_class">org.junit.runner.notification.RunNotifier.5</span></td><td><code>f62dc396b601f8bd</code></td></tr><tr><td><span class="el_class">org.junit.runner.notification.RunNotifier.9</span></td><td><code>c3c3d54b8ed47ee1</code></td></tr><tr><td><span class="el_class">org.junit.runner.notification.RunNotifier.SafeNotifier</span></td><td><code>0b43c10299733bfb</code></td></tr><tr><td><span class="el_class">org.junit.runner.notification.SynchronizedRunListener</span></td><td><code>2b59d5cb3b105225</code></td></tr><tr><td><span class="el_class">org.junit.runners.BlockJUnit4ClassRunner</span></td><td><code>95752fb34ff12f3f</code></td></tr><tr><td><span class="el_class">org.junit.runners.BlockJUnit4ClassRunner.1</span></td><td><code>d0f63145230a5f42</code></td></tr><tr><td><span class="el_class">org.junit.runners.BlockJUnit4ClassRunner.2</span></td><td><code>f93eace695ddd30e</code></td></tr><tr><td><span class="el_class">org.junit.runners.BlockJUnit4ClassRunner.RuleCollector</span></td><td><code>9c768e710e39c989</code></td></tr><tr><td><span class="el_class">org.junit.runners.JUnit4</span></td><td><code>6d26e2305347fe01</code></td></tr><tr><td><span class="el_class">org.junit.runners.ParentRunner</span></td><td><code>335ee90b10f96ea1</code></td></tr><tr><td><span class="el_class">org.junit.runners.ParentRunner.1</span></td><td><code>ecc6961e8bc209c4</code></td></tr><tr><td><span class="el_class">org.junit.runners.ParentRunner.2</span></td><td><code>c5cb913a629ec4c8</code></td></tr><tr><td><span class="el_class">org.junit.runners.ParentRunner.3</span></td><td><code>20bad8188aebc0f2</code></td></tr><tr><td><span class="el_class">org.junit.runners.ParentRunner.4</span></td><td><code>80476dbdcb8d52cc</code></td></tr><tr><td><span class="el_class">org.junit.runners.ParentRunner.ClassRuleCollector</span></td><td><code>26f7fb338afcd13b</code></td></tr><tr><td><span class="el_class">org.junit.runners.RuleContainer</span></td><td><code>d44c3ba6dc65af53</code></td></tr><tr><td><span class="el_class">org.junit.runners.RuleContainer.1</span></td><td><code>57bbc73f6f47763b</code></td></tr><tr><td><span class="el_class">org.junit.runners.model.FrameworkField</span></td><td><code>2fe27c284e7d39f4</code></td></tr><tr><td><span class="el_class">org.junit.runners.model.FrameworkMember</span></td><td><code>bfd059486f267475</code></td></tr><tr><td><span class="el_class">org.junit.runners.model.FrameworkMethod</span></td><td><code>f293b82d5aa86323</code></td></tr><tr><td><span class="el_class">org.junit.runners.model.FrameworkMethod.1</span></td><td><code>8fd5e02769c0e0c2</code></td></tr><tr><td><span class="el_class">org.junit.runners.model.RunnerBuilder</span></td><td><code>585cad2d320dc86e</code></td></tr><tr><td><span class="el_class">org.junit.runners.model.Statement</span></td><td><code>9a75aa5de27bf4d5</code></td></tr><tr><td><span class="el_class">org.junit.runners.model.TestClass</span></td><td><code>7e71209792391ee8</code></td></tr><tr><td><span class="el_class">org.junit.runners.model.TestClass.FieldComparator</span></td><td><code>1b96cd3d5c4aeb07</code></td></tr><tr><td><span class="el_class">org.junit.runners.model.TestClass.MethodComparator</span></td><td><code>0369eb29eb04248a</code></td></tr><tr><td><span class="el_class">org.junit.validator.AnnotationValidatorFactory</span></td><td><code>e1e5570798173ab9</code></td></tr><tr><td><span class="el_class">org.junit.validator.AnnotationsValidator</span></td><td><code>6cbe8454c9a93bb8</code></td></tr><tr><td><span class="el_class">org.junit.validator.AnnotationsValidator.AnnotatableValidator</span></td><td><code>d211a963f22be103</code></td></tr><tr><td><span class="el_class">org.junit.validator.AnnotationsValidator.ClassValidator</span></td><td><code>1b463c4e6642e880</code></td></tr><tr><td><span class="el_class">org.junit.validator.AnnotationsValidator.FieldValidator</span></td><td><code>64068b954dc56a31</code></td></tr><tr><td><span class="el_class">org.junit.validator.AnnotationsValidator.MethodValidator</span></td><td><code>f16b57f17c787036</code></td></tr><tr><td><span class="el_class">org.junit.validator.PublicClassValidator</span></td><td><code>3bac248cf06b18e4</code></td></tr><tr><td><span class="el_class">org.slf4j.LoggerFactory</span></td><td><code>0963abe5eaf18772</code></td></tr><tr><td><span class="el_class">org.slf4j.MDC</span></td><td><code>da862873ef1a68e8</code></td></tr><tr><td><span class="el_class">org.slf4j.helpers.FormattingTuple</span></td><td><code>46e388b1eb4cb5c1</code></td></tr><tr><td><span class="el_class">org.slf4j.helpers.MessageFormatter</span></td><td><code>f00b59cd4c833983</code></td></tr><tr><td><span class="el_class">org.slf4j.helpers.NOPLoggerFactory</span></td><td><code>54f5632bfcb8d8d5</code></td></tr><tr><td><span class="el_class">org.slf4j.helpers.SubstituteLoggerFactory</span></td><td><code>dc7efc0107a4a62d</code></td></tr><tr><td><span class="el_class">org.slf4j.helpers.Util</span></td><td><code>b80ff25a61f7aa8b</code></td></tr><tr><td><span class="el_class">org.slf4j.impl.StaticLoggerBinder</span></td><td><code>039b3c899e055991</code></td></tr><tr><td><span class="el_class">org.slf4j.impl.StaticMDCBinder</span></td><td><code>649700d80abb641d</code></td></tr><tr><td><span class="el_class">unit.MockData</span></td><td><code>d40f3af9dfefd33e</code></td></tr><tr><td><span class="el_class">unit.MockData.1</span></td><td><code>e7d27beade7ce49c</code></td></tr><tr><td><span class="el_class">unit.MockWebServerDispatcher</span></td><td><code>ca61d1495c092ea5</code></td></tr><tr><td><span class="el_class">unit.MockWebServerDispatcher.1</span></td><td><code>3a594ef442a53419</code></td></tr><tr><td><span class="el_class">unit.TestJSONParser</span></td><td><code>ddacbca1942282ab</code></td></tr><tr><td><span class="el_class">unit.TestParameterChecker</span></td><td><code>81ff2e5dc58d4361</code></td></tr><tr><td><span class="el_class">unit.TestRequestBuilder</span></td><td><code>ebada5e907670155</code></td></tr><tr><td><span class="el_class">unit.TestResponseHandler</span></td><td><code>db20d20f99ed2c1f</code></td></tr><tr><td><span class="el_class">unit.TestUrlBuilder</span></td><td><code>657edb4001ecc536</code></td></tr><tr><td><span class="el_class">unit.TestUrlBuilder.1</span></td><td><code>ebd5a5e71837fd46</code></td></tr><tr><td><span class="el_class">unit.TestUrlBuilder.2</span></td><td><code>a53109d95116732a</code></td></tr><tr><td><span class="el_class">unit.TestUrlBuilder.3</span></td><td><code>7cbc17d028604adf</code></td></tr><tr><td><span class="el_class">unit.cm_futures.account.TestCMAccountInformation</span></td><td><code>5a7e31d661d01911</code></td></tr><tr><td><span class="el_class">unit.cm_futures.account.TestCMAccountTradeList</span></td><td><code>5f2c43676fb4fa07</code></td></tr><tr><td><span class="el_class">unit.cm_futures.account.TestCMAllOrders</span></td><td><code>c1e4b8ee9ed84507</code></td></tr><tr><td><span class="el_class">unit.cm_futures.account.TestCMAutoCancelAllOpenOrders</span></td><td><code>7b127adcc0de718d</code></td></tr><tr><td><span class="el_class">unit.cm_futures.account.TestCMCancelAllOpenOrders</span></td><td><code>032267fbc697f96a</code></td></tr><tr><td><span class="el_class">unit.cm_futures.account.TestCMCancelMultipleOrders</span></td><td><code>a17b5f77710fa4fe</code></td></tr><tr><td><span class="el_class">unit.cm_futures.account.TestCMCancelOrder</span></td><td><code>93b78a68103bf6bf</code></td></tr><tr><td><span class="el_class">unit.cm_futures.account.TestCMChangeInitialLeverage</span></td><td><code>f8f53684a09a1c7b</code></td></tr><tr><td><span class="el_class">unit.cm_futures.account.TestCMChangePositionMode</span></td><td><code>491d28d7a7620147</code></td></tr><tr><td><span class="el_class">unit.cm_futures.account.TestCMCurrentAllOpenOrders</span></td><td><code>99d5b2c2fdcb88ad</code></td></tr><tr><td><span class="el_class">unit.cm_futures.account.TestCMCurrentPositionMode</span></td><td><code>6185b1dbfa163c6a</code></td></tr><tr><td><span class="el_class">unit.cm_futures.account.TestCMFuturesAccountBalance</span></td><td><code>6ebdb0dfdcd841a6</code></td></tr><tr><td><span class="el_class">unit.cm_futures.account.TestCMGetLeverageBracket</span></td><td><code>37e22fecc90ffcd3</code></td></tr><tr><td><span class="el_class">unit.cm_futures.account.TestCMGetLeverageBracketPair</span></td><td><code>85408039c4a5de01</code></td></tr><tr><td><span class="el_class">unit.cm_futures.account.TestCMIncomeHistory</span></td><td><code>846f025d1b0f3e1a</code></td></tr><tr><td><span class="el_class">unit.cm_futures.account.TestCMModifyIsolatedPositionMargin</span></td><td><code>7b005c7e6af1554c</code></td></tr><tr><td><span class="el_class">unit.cm_futures.account.TestCMModifyOrder</span></td><td><code>ffbe781658f2ff4c</code></td></tr><tr><td><span class="el_class">unit.cm_futures.account.TestCMModifyOrderHistory</span></td><td><code>53c393f179231515</code></td></tr><tr><td><span class="el_class">unit.cm_futures.account.TestCMNewOrder</span></td><td><code>51004afacd5e0346</code></td></tr><tr><td><span class="el_class">unit.cm_futures.account.TestCMNotionalAndLeverageBrackets</span></td><td><code>3e74584dd0dda0d1</code></td></tr><tr><td><span class="el_class">unit.cm_futures.account.TestCMPositionAdlQuantileEstimation</span></td><td><code>d7fb54c1d3a34280</code></td></tr><tr><td><span class="el_class">unit.cm_futures.account.TestCMPositionInformation</span></td><td><code>fb6012f9b3f15485</code></td></tr><tr><td><span class="el_class">unit.cm_futures.account.TestCMPositionMarginChangeHistory</span></td><td><code>69b4445519320093</code></td></tr><tr><td><span class="el_class">unit.cm_futures.account.TestCMQueryCurrentOpenOrder</span></td><td><code>72174e2e9b68c867</code></td></tr><tr><td><span class="el_class">unit.cm_futures.account.TestCMQueryOrder</span></td><td><code>fda9282283c8041f</code></td></tr><tr><td><span class="el_class">unit.cm_futures.account.TestCMUserCommissionRate</span></td><td><code>6d63425ccf53c5d3</code></td></tr><tr><td><span class="el_class">unit.cm_futures.account.TestCMUsersForceOrders</span></td><td><code>a859b509b99ae03a</code></td></tr><tr><td><span class="el_class">unit.cm_futures.market.TestCMAggTrades</span></td><td><code>ee0ae19a9b9defd1</code></td></tr><tr><td><span class="el_class">unit.cm_futures.market.TestCMBasis</span></td><td><code>1b8c6b15777229c1</code></td></tr><tr><td><span class="el_class">unit.cm_futures.market.TestCMBookTicker</span></td><td><code>2d4873365d91c4dd</code></td></tr><tr><td><span class="el_class">unit.cm_futures.market.TestCMContinuousKlines</span></td><td><code>d289af777e45b5e6</code></td></tr><tr><td><span class="el_class">unit.cm_futures.market.TestCMDepth</span></td><td><code>f9bcba16af561f65</code></td></tr><tr><td><span class="el_class">unit.cm_futures.market.TestCMExchangeInfo</span></td><td><code>495272a98bd90a58</code></td></tr><tr><td><span class="el_class">unit.cm_futures.market.TestCMFundingRateHistory</span></td><td><code>af4ea84d2fad38d9</code></td></tr><tr><td><span class="el_class">unit.cm_futures.market.TestCMHistoricalTrades</span></td><td><code>24b4f3ec57285bb8</code></td></tr><tr><td><span class="el_class">unit.cm_futures.market.TestCMIndexPriceKlines</span></td><td><code>61c31d5f67963dc5</code></td></tr><tr><td><span class="el_class">unit.cm_futures.market.TestCMKlines</span></td><td><code>2eca0fe7b22e7a50</code></td></tr><tr><td><span class="el_class">unit.cm_futures.market.TestCMLongShortRatio</span></td><td><code>49a972fd372d1e6f</code></td></tr><tr><td><span class="el_class">unit.cm_futures.market.TestCMMarkPrice</span></td><td><code>b8103bf79a523602</code></td></tr><tr><td><span class="el_class">unit.cm_futures.market.TestCMMarkPriceKlines</span></td><td><code>d77cee2e36c2cf47</code></td></tr><tr><td><span class="el_class">unit.cm_futures.market.TestCMOpenInterest</span></td><td><code>898a8cc3b36f1fb3</code></td></tr><tr><td><span class="el_class">unit.cm_futures.market.TestCMOpenInterestStatistics</span></td><td><code>94a29b6b7ac78c75</code></td></tr><tr><td><span class="el_class">unit.cm_futures.market.TestCMPing</span></td><td><code>6dac571711637db9</code></td></tr><tr><td><span class="el_class">unit.cm_futures.market.TestCMTicker24H</span></td><td><code>ade3471f7e8e2fe4</code></td></tr><tr><td><span class="el_class">unit.cm_futures.market.TestCMTickerPrice</span></td><td><code>135c8e04ace10fcb</code></td></tr><tr><td><span class="el_class">unit.cm_futures.market.TestCMTime</span></td><td><code>1f1b886001da22ef</code></td></tr><tr><td><span class="el_class">unit.cm_futures.market.TestCMTopLongShortAccountRatio</span></td><td><code>18c7b422b4886ba1</code></td></tr><tr><td><span class="el_class">unit.cm_futures.market.TestCMTopLongShortPositionRatio</span></td><td><code>ad9e98a95668b0ab</code></td></tr><tr><td><span class="el_class">unit.cm_futures.market.TestCMTrades</span></td><td><code>205bd41adb71caf9</code></td></tr><tr><td><span class="el_class">unit.cm_futures.portfoliomargin.TestCMPortfolioMarginAccountInfo</span></td><td><code>9a77c64dd6bc15d3</code></td></tr><tr><td><span class="el_class">unit.cm_futures.portfoliomargin.TestCMPortfolioMarginExchangeInfo</span></td><td><code>6c6e3c4489a7b37f</code></td></tr><tr><td><span class="el_class">unit.cm_futures.userdata.TestCMCloseListenKey</span></td><td><code>ffa428d045edcb2f</code></td></tr><tr><td><span class="el_class">unit.cm_futures.userdata.TestCMCreateListenKey</span></td><td><code>b83bdb75943fcb54</code></td></tr><tr><td><span class="el_class">unit.cm_futures.userdata.TestCMExtendListenKey</span></td><td><code>391643d5cfcfa3e1</code></td></tr><tr><td><span class="el_class">unit.um_futures.account.TestUMAccountInformation</span></td><td><code>****************</code></td></tr><tr><td><span class="el_class">unit.um_futures.account.TestUMAccountTradeList</span></td><td><code>72461c004898d25a</code></td></tr><tr><td><span class="el_class">unit.um_futures.account.TestUMAllOrders</span></td><td><code>b26c9cb7ab840426</code></td></tr><tr><td><span class="el_class">unit.um_futures.account.TestUMAutoCancelAllOpenOrders</span></td><td><code>52f5f3c9265be9e8</code></td></tr><tr><td><span class="el_class">unit.um_futures.account.TestUMCancelAllOpenOrders</span></td><td><code>ac949d008fb6f71b</code></td></tr><tr><td><span class="el_class">unit.um_futures.account.TestUMCancelMultipleOrders</span></td><td><code>35b7a3b9ac9e6b1f</code></td></tr><tr><td><span class="el_class">unit.um_futures.account.TestUMCancelOrder</span></td><td><code>74f5955f5a01b220</code></td></tr><tr><td><span class="el_class">unit.um_futures.account.TestUMChangeInitialLeverage</span></td><td><code>526136dd4e8cdb7a</code></td></tr><tr><td><span class="el_class">unit.um_futures.account.TestUMChangeMarginType</span></td><td><code>fbe4d17c9e1d923f</code></td></tr><tr><td><span class="el_class">unit.um_futures.account.TestUMChangeMultiAssetsMode</span></td><td><code>378b0558d1feb03e</code></td></tr><tr><td><span class="el_class">unit.um_futures.account.TestUMChangePositionMode</span></td><td><code>8bce4394fff8e8f1</code></td></tr><tr><td><span class="el_class">unit.um_futures.account.TestUMCurrentAllOpenOrders</span></td><td><code>db8760ad3c96ca0e</code></td></tr><tr><td><span class="el_class">unit.um_futures.account.TestUMCurrentPositionMode</span></td><td><code>35ae7fcfe8b7b628</code></td></tr><tr><td><span class="el_class">unit.um_futures.account.TestUMDownloadIdForFuturesTransactionHistory</span></td><td><code>f2a4067ea9cb127b</code></td></tr><tr><td><span class="el_class">unit.um_futures.account.TestUMDownloadLinkForFuturesTransactionHistory</span></td><td><code>e54950e0be2beebc</code></td></tr><tr><td><span class="el_class">unit.um_futures.account.TestUMFuturesAccountBalance</span></td><td><code>ccf2b8bffaf64ce6</code></td></tr><tr><td><span class="el_class">unit.um_futures.account.TestUMGetLeverageBracket</span></td><td><code>733651d4f2e7f669</code></td></tr><tr><td><span class="el_class">unit.um_futures.account.TestUMGetMultiAssetsMode</span></td><td><code>5b3afcced211eab4</code></td></tr><tr><td><span class="el_class">unit.um_futures.account.TestUMIncomeHistory</span></td><td><code>88a4b0a4362f845c</code></td></tr><tr><td><span class="el_class">unit.um_futures.account.TestUMModifyIsolatedPositionMargin</span></td><td><code>9c7051720077e230</code></td></tr><tr><td><span class="el_class">unit.um_futures.account.TestUMNewOrder</span></td><td><code>e4203f7a8600cb74</code></td></tr><tr><td><span class="el_class">unit.um_futures.account.TestUMNotionalAndLeverageBrackets</span></td><td><code>3070b7519036803a</code></td></tr><tr><td><span class="el_class">unit.um_futures.account.TestUMPositionAdlQuantileEstimation</span></td><td><code>b0515d02b7dd24da</code></td></tr><tr><td><span class="el_class">unit.um_futures.account.TestUMPositionInformation</span></td><td><code>6bc82430eb63b93c</code></td></tr><tr><td><span class="el_class">unit.um_futures.account.TestUMPositionMarginChangeHistory</span></td><td><code>eeb79783c5af9780</code></td></tr><tr><td><span class="el_class">unit.um_futures.account.TestUMQuantitativeRulesIndicators</span></td><td><code>c7f49588d71377ea</code></td></tr><tr><td><span class="el_class">unit.um_futures.account.TestUMQueryCurrentOpenOrder</span></td><td><code>c50273a66e885fdc</code></td></tr><tr><td><span class="el_class">unit.um_futures.account.TestUMQueryOrder</span></td><td><code>969145a3e523c600</code></td></tr><tr><td><span class="el_class">unit.um_futures.account.TestUMUserCommissionRate</span></td><td><code>5374da8bfd305a2b</code></td></tr><tr><td><span class="el_class">unit.um_futures.account.TestUMUsersForceOrders</span></td><td><code>aa9f22fb69d549a0</code></td></tr><tr><td><span class="el_class">unit.um_futures.market.TestUMAggTrades</span></td><td><code>da13b8a826d6837d</code></td></tr><tr><td><span class="el_class">unit.um_futures.market.TestUMBookTicker</span></td><td><code>a795dea8f5e8b81b</code></td></tr><tr><td><span class="el_class">unit.um_futures.market.TestUMContinuousKlines</span></td><td><code>4ca1283e0d083b15</code></td></tr><tr><td><span class="el_class">unit.um_futures.market.TestUMDepth</span></td><td><code>ec0898d02d50f6bf</code></td></tr><tr><td><span class="el_class">unit.um_futures.market.TestUMExchangeInfo</span></td><td><code>1383fd457a6d0d5a</code></td></tr><tr><td><span class="el_class">unit.um_futures.market.TestUMFundingRateHistory</span></td><td><code>911d4a877dcbbb47</code></td></tr><tr><td><span class="el_class">unit.um_futures.market.TestUMHistoricalBlvtKlines</span></td><td><code>051bd92b44ba8e0e</code></td></tr><tr><td><span class="el_class">unit.um_futures.market.TestUMHistoricalTrades</span></td><td><code>534392dee23d8ad0</code></td></tr><tr><td><span class="el_class">unit.um_futures.market.TestUMIndexInfo</span></td><td><code>99895d79d8e35faa</code></td></tr><tr><td><span class="el_class">unit.um_futures.market.TestUMIndexPriceKlines</span></td><td><code>aa35e12177a28b60</code></td></tr><tr><td><span class="el_class">unit.um_futures.market.TestUMKlines</span></td><td><code>68b01b7db15da1ee</code></td></tr><tr><td><span class="el_class">unit.um_futures.market.TestUMLongShortRatio</span></td><td><code>d2afe96318ff921c</code></td></tr><tr><td><span class="el_class">unit.um_futures.market.TestUMMarkPrice</span></td><td><code>27c759e45ffabb66</code></td></tr><tr><td><span class="el_class">unit.um_futures.market.TestUMMarkPriceKlines</span></td><td><code>256a597dbdbab977</code></td></tr><tr><td><span class="el_class">unit.um_futures.market.TestUMMultiAssetsModeIndex</span></td><td><code>7810bb6fd90c86f9</code></td></tr><tr><td><span class="el_class">unit.um_futures.market.TestUMOpenInterest</span></td><td><code>64be9ead97208142</code></td></tr><tr><td><span class="el_class">unit.um_futures.market.TestUMOpenInterestStatistics</span></td><td><code>04e72471582aaee1</code></td></tr><tr><td><span class="el_class">unit.um_futures.market.TestUMPing</span></td><td><code>334d8f7242960cb2</code></td></tr><tr><td><span class="el_class">unit.um_futures.market.TestUMTakerBuySellVolume</span></td><td><code>4a281e83503061a1</code></td></tr><tr><td><span class="el_class">unit.um_futures.market.TestUMTicker24H</span></td><td><code>72ebd8d84c254ad1</code></td></tr><tr><td><span class="el_class">unit.um_futures.market.TestUMTickerPrice</span></td><td><code>1381726a77c0b4f8</code></td></tr><tr><td><span class="el_class">unit.um_futures.market.TestUMTime</span></td><td><code>41fa5005522f53e4</code></td></tr><tr><td><span class="el_class">unit.um_futures.market.TestUMTopLongShortAccountRatio</span></td><td><code>7e0bae467aae5938</code></td></tr><tr><td><span class="el_class">unit.um_futures.market.TestUMTopLongShortPositionRatio</span></td><td><code>26f972fdb064652b</code></td></tr><tr><td><span class="el_class">unit.um_futures.market.TestUMTrades</span></td><td><code>851adb6ff06cf28f</code></td></tr><tr><td><span class="el_class">unit.um_futures.userdata.TestUMCloseListenKey</span></td><td><code>a8dfcf80f9be64fc</code></td></tr><tr><td><span class="el_class">unit.um_futures.userdata.TestUMCreateListenKey</span></td><td><code>22925d9f91c0fd38</code></td></tr><tr><td><span class="el_class">unit.um_futures.userdata.TestUMExtendListenKey</span></td><td><code>ba44b4495138ccfd</code></td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.7.************</span></div></body></html>