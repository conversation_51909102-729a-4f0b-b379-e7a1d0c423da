package com.crypto.trading.sdk.limiter;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.regex.Pattern;
import java.util.regex.Matcher;

/**
 * 响应头限流信息更新器
 * <p>
 * 处理Binance API响应头中的限流信息，动态更新限流配置
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
public class ResponseHeaderRateLimitUpdater {

    private static final Logger log = LoggerFactory.getLogger(ResponseHeaderRateLimitUpdater.class);
    
    /**
     * 币安API使用权重响应头
     */
    private static final String HEADER_USED_WEIGHT = "x-mbx-used-weight";
    
    /**
     * 币安API使用权重（1分钟）响应头
     */
    private static final String HEADER_USED_WEIGHT_1M = "x-mbx-used-weight-1m";
    
    /**
     * 币安API订单数限制响应头
     */
    private static final String HEADER_ORDER_COUNT = "x-mbx-order-count";
    
    /**
     * 币安API订单数限制（10秒）响应头
     */
    private static final String HEADER_ORDER_COUNT_10S = "x-mbx-order-count-10s";
    
    /**
     * 币安API订单数限制（1分钟）响应头
     */
    private static final String HEADER_ORDER_COUNT_1M = "x-mbx-order-count-1m";
    
    /**
     * 币安API限制响应头
     */
    private static final String HEADER_LIMIT = "x-mbx-limit";
    
    /**
     * 币安API剩余限制响应头前缀
     */
    private static final String HEADER_LIMIT_STATUS_PREFIX = "x-mbx-limit-status";
    
    /**
     * 重试时间响应头
     */
    private static final String HEADER_RETRY_AFTER = "retry-after";
    
    /**
     * 币安API限制类型正则表达式
     */
    private static final Pattern LIMIT_TYPE_PATTERN = Pattern.compile("x-mbx-limit-status-(.+)");
    
    /**
     * 默认权重限制（每分钟）
     */
    private static final int DEFAULT_WEIGHT_LIMIT = 1200;
    
    /**
     * 默认订单限制（每10秒）
     */
    private static final int DEFAULT_ORDER_10S_LIMIT = 50;
    
    /**
     * 默认订单限制（每分钟）
     */
    private static final int DEFAULT_ORDER_1M_LIMIT = 300;
    
    /**
     * 限流器
     */
    private final RateLimiter rateLimiter;
    
    /**
     * 构造函数
     *
     * @param rateLimiter 限流器
     */
    public ResponseHeaderRateLimitUpdater(RateLimiter rateLimiter) {
        this.rateLimiter = rateLimiter;
    }
    
    /**
     * 处理响应头中的限流信息
     *
     * @param headers  响应头
     * @param endpoint API端点
     */
    public void processResponseHeaders(Map<String, String> headers, String endpoint) {
        if (headers == null || headers.isEmpty()) {
            return;
        }
        
        try {
            // 检查是否需要重置限流器
            if (headers.containsKey(HEADER_RETRY_AFTER)) {
                // 存在retry-after头，表示已被限流，需要重置限流器
                rateLimiter.reset(endpoint);
                return;
            }
            
            // 处理使用权重信息
            processUsedWeight(headers, endpoint);
            
            // 处理订单数限制
            processOrderCount(headers, endpoint);
            
            // 处理特定端点限制
            processEndpointLimits(headers, endpoint);
            
        } catch (Exception e) {
            log.warn("处理响应头限流信息异常: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 处理使用权重信息
     *
     * @param headers  响应头
     * @param endpoint API端点
     */
    private void processUsedWeight(Map<String, String> headers, String endpoint) {
        String usedWeight = headers.get(HEADER_USED_WEIGHT);
        String usedWeight1m = headers.get(HEADER_USED_WEIGHT_1M);
        
        if (usedWeight != null) {
            try {
                int weight = Integer.parseInt(usedWeight);
                log.debug("当前API使用权重: {}", weight);
                
                // 如果使用权重接近限制，记录警告日志
                if (weight > 1000) {
                    log.warn("API使用权重较高: {}", weight);
                }
                
                // 计算剩余权重并更新限流配置
                int remainingWeight = DEFAULT_WEIGHT_LIMIT - weight;
                if (remainingWeight <= 0) {
                    // 权重已用完，重置限流器
                    rateLimiter.reset(endpoint);
                } else {
                    // 更新权重限制配置
                    rateLimiter.updateConfig("WEIGHT", remainingWeight, 60000L);
                }
            } catch (NumberFormatException e) {
                log.warn("解析API使用权重失败: {}", usedWeight);
            }
        }
        
        if (usedWeight1m != null) {
            try {
                int weight1m = Integer.parseInt(usedWeight1m);
                log.debug("当前API 1分钟使用权重: {}", weight1m);
                
                // 计算剩余权重并更新限流配置
                int remainingWeight = DEFAULT_WEIGHT_LIMIT - weight1m;
                if (remainingWeight <= 0) {
                    // 权重已用完，重置限流器
                    rateLimiter.reset(endpoint);
                } else {
                    // 更新权重限制配置
                    rateLimiter.updateConfig("WEIGHT", remainingWeight, 60000L);
                }
            } catch (NumberFormatException e) {
                log.warn("解析API 1分钟使用权重失败: {}", usedWeight1m);
            }
        }
    }
    
    /**
     * 处理订单数限制
     *
     * @param headers  响应头
     * @param endpoint API端点
     */
    private void processOrderCount(Map<String, String> headers, String endpoint) {
        String orderCount10s = headers.get(HEADER_ORDER_COUNT_10S);
        String orderCount1m = headers.get(HEADER_ORDER_COUNT_1M);
        
        if (orderCount10s != null) {
            try {
                int count10s = Integer.parseInt(orderCount10s);
                log.debug("当前10秒订单数: {}", count10s);
                
                // 计算剩余订单数并更新限流配置
                int remainingOrders = DEFAULT_ORDER_10S_LIMIT - count10s;
                if (remainingOrders <= 0) {
                    // 订单数已用完，重置限流器
                    rateLimiter.reset(endpoint);
                } else {
                    // 更新订单数限制配置
                    rateLimiter.updateConfig("ORDER_10S", remainingOrders, 10000L);
                }
            } catch (NumberFormatException e) {
                log.warn("解析10秒订单数失败: {}", orderCount10s);
            }
        }
        
        if (orderCount1m != null) {
            try {
                int count1m = Integer.parseInt(orderCount1m);
                log.debug("当前1分钟订单数: {}", count1m);
                
                // 计算剩余订单数并更新限流配置
                int remainingOrders = DEFAULT_ORDER_1M_LIMIT - count1m;
                if (remainingOrders <= 0) {
                    // 订单数已用完，重置限流器
                    rateLimiter.reset(endpoint);
                } else {
                    // 更新订单数限制配置
                    rateLimiter.updateConfig("ORDER_1M", remainingOrders, 60000L);
                }
            } catch (NumberFormatException e) {
                log.warn("解析1分钟订单数失败: {}", orderCount1m);
            }
        }
    }
    
    /**
     * 处理特定端点限制
     *
     * @param headers  响应头
     * @param endpoint API端点
     */
    private void processEndpointLimits(Map<String, String> headers, String endpoint) {
        // 处理通用限制
        String limit = headers.get(HEADER_LIMIT);
        if (limit != null) {
            try {
                int limitValue = Integer.parseInt(limit);
                log.debug("API限制: {}", limitValue);
            } catch (NumberFormatException e) {
                log.warn("解析API限制失败: {}", limit);
            }
        }
        
        // 处理特定类型限制
        for (Map.Entry<String, String> entry : headers.entrySet()) {
            String key = entry.getKey();
            Matcher matcher = LIMIT_TYPE_PATTERN.matcher(key);
            
            if (matcher.matches()) {
                String limitType = matcher.group(1);
                String limitStatus = entry.getValue();
                
                try {
                    // 格式通常为: 当前值/最大值
                    String[] parts = limitStatus.split("/");
                    if (parts.length == 2) {
                        int current = Integer.parseInt(parts[0]);
                        int max = Integer.parseInt(parts[1]);
                        
                        log.debug("限制类型[{}]状态: {}/{}", limitType, current, max);
                        
                        // 如果接近限制，记录警告日志
                        double usageRate = (double) current / max;
                        if (usageRate > 0.8) {
                            log.warn("限制类型[{}]接近上限: {}/{} ({}%)", 
                                    limitType, current, max, String.format("%.1f", usageRate * 100));
                        }
                        
                        // 动态更新特定端点的限流配置
                        if (endpoint != null && !endpoint.isEmpty()) {
                            updateRateLimitConfig(endpoint, limitType, current, max);
                        }
                    }
                } catch (NumberFormatException e) {
                    log.warn("解析限制状态失败: {}", limitStatus);
                }
            }
        }
    }
    
    /**
     * 更新限流配置
     *
     * @param endpoint  API端点
     * @param limitType 限制类型
     * @param current   当前值
     * @param max       最大值
     */
    private void updateRateLimitConfig(String endpoint, String limitType, int current, int max) {
        // 根据限制类型和当前使用情况，动态调整限流配置
        // 例如，如果接近限制，可以降低请求速率
        
        // 计算剩余可用量
        int remaining = max - current;
        
        // 如果剩余量较少，调整限流配置
        if (remaining < max * 0.2) {
            log.warn("端点[{}]的[{}]限制剩余量较少: {}，调整限流配置", endpoint, limitType, remaining);
            
            // 根据不同的限制类型设置不同的时间窗口
            long timeWindow;
            switch (limitType.toLowerCase()) {
                case "minute":
                    timeWindow = 60000; // 1分钟
                    break;
                case "second":
                    timeWindow = 1000; // 1秒
                    break;
                case "day":
                    timeWindow = 86400000; // 1天
                    break;
                default:
                    timeWindow = 60000; // 默认1分钟
            }
            
            // 更新限流配置，将最大请求数调整为剩余量的80%，以留出安全余量
            int adjustedMax = Math.max(1, (int)(remaining * 0.8));
            rateLimiter.updateConfig(endpoint, adjustedMax, timeWindow);
            
            log.info("已调整端点[{}]的限流配置: 最大请求数={}, 时间窗口={}ms", endpoint, adjustedMax, timeWindow);
        }
    }
}