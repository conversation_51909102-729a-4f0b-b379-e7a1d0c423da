package com.crypto.trading.sdk.retry;

import com.crypto.trading.sdk.exception.ApiException;
import com.crypto.trading.sdk.exception.ClientException;
import com.crypto.trading.sdk.exception.ServerException;

import java.net.ConnectException;
import java.net.SocketException;
import java.net.SocketTimeoutException;
import java.util.Set;
import java.util.function.Predicate;

/**
 * 错误分类器
 * 用于识别不同类型的错误，并决定是否应该重试
 */
public class ErrorClassifier {

    // 可重试的HTTP状态码
    private static final Set<Integer> RETRYABLE_STATUS_CODES = Set.of(
        408, // Request Timeout
        429, // Too Many Requests
        500, // Internal Server Error
        502, // Bad Gateway
        503, // Service Unavailable
        504, // Gateway Timeout
        520, // Unknown Error
        522, // Connection Timed Out
        524  // A Timeout Occurred
    );
    
    // 可重试的错误代码 - 扩展了更多可能需要重试的错误
    private static final Set<Integer> RETRYABLE_ERROR_CODES = Set.of(
        -1001, // 断开的连接
        -1002, // 未授权
        -1003, // 请求过多
        -1007, // 超时
        -1008, // 服务器超时
        -1015, // 请求权重过多
        -1020, // 未知错误
        -1021, // 时间戳超出服务器时间
        -1022, // 签名无效
        -1100, // 非法参数（可能因为超时导致）
        -2008, // 超时
        -3008  // 内部错误
    );
    
    /**
     * 标准重试条件
     * 对于常见的临时错误进行重试，例如网络错误、服务器错误和限流错误
     *
     * @return 重试条件
     */
    public static Predicate<Exception> standardRetryCondition() {
        return e -> {
            // 网络相关异常
            if (e instanceof SocketTimeoutException || 
                e instanceof ConnectException || 
                e instanceof SocketException) {
                return true;
            }
            
            // 检查异常消息中的超时相关内容
            if (e != null && e.getMessage() != null) {
                String message = e.getMessage().toLowerCase();
                if (message.contains("timeout") || 
                    message.contains("timed out") || 
                    message.contains("connect timed out") ||
                    message.contains("read timed out") ||
                    message.contains("connection reset")) {
                    return true;
                }
            }
            
            // 检查嵌套的原因
            if (e != null && e.getCause() != null) {
                Throwable cause = e.getCause();
                if (cause instanceof SocketTimeoutException || 
                    cause instanceof ConnectException || 
                    cause instanceof SocketException) {
                    return true;
                }
                
                // 检查嵌套异常的消息
                if (cause.getMessage() != null) {
                    String message = cause.getMessage().toLowerCase();
                    if (message.contains("timeout") || 
                        message.contains("timed out") || 
                        message.contains("connection")) {
                        return true;
                    }
                }
            }
            
            // API异常
            if (e instanceof ApiException) {
                // 检查API异常的消息，某些API异常是可以重试的
                String message = e.getMessage() != null ? e.getMessage().toLowerCase() : "";
                if (message.contains("timeout") || 
                    message.contains("timed out") || 
                    message.contains("connection") ||
                    message.contains("overloaded") ||
                    message.contains("busy")) {
                    return true;
                }
                return false; // 其他API异常不重试
            }
            
            // 客户端异常
            if (e instanceof ClientException) {
                ClientException ce = (ClientException) e;
                
                // 检查HTTP状态码
                if (RETRYABLE_STATUS_CODES.contains(ce.getHttpStatusCode())) {
                    return true;
                }
                
                // 检查错误代码
                return RETRYABLE_ERROR_CODES.contains(ce.getCode());
            }
            
            // 服务器异常
            if (e instanceof ServerException) {
                ServerException se = (ServerException) e;
                return RETRYABLE_STATUS_CODES.contains(se.getHttpStatusCode());
            }
            
            // 默认不重试
            return false;
        };
    }
    
    /**
     * 宽松重试条件
     * 对大多数异常进行重试，除了明确的客户端错误
     *
     * @return 重试条件
     */
    public static Predicate<Exception> lenientRetryCondition() {
        return e -> {
            // 客户端异常
            if (e instanceof ClientException) {
                ClientException ce = (ClientException) e;
                int statusCode = ce.getHttpStatusCode();
                
                // 不重试明确的客户端错误（400-499），除了可重试的状态码
                return statusCode < 400 || statusCode >= 500 || RETRYABLE_STATUS_CODES.contains(statusCode);
            }
            
            // 其他所有异常都重试
            return true;
        };
    }
}