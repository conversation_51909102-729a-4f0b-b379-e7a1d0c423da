package com.crypto.trading.market.service.impl.resumable;

import com.crypto.trading.common.dto.market.KlineDataDTO;
import com.crypto.trading.common.enums.KlineInterval;
import com.crypto.trading.market.service.impl.DownloadTaskTracker;
import com.crypto.trading.market.service.impl.optimizer.SmartChunkDownloader;
import com.crypto.trading.market.service.impl.optimizer.SmartDownloadOptimizer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.BiFunction;

/**
 * 可恢复下载引擎
 * 管理下载任务的状态，支持断点续传功能
 */
@Component
public class ResumableDownloadEngine {

    private static final Logger log = LoggerFactory.getLogger(ResumableDownloadEngine.class);
    private static final String CHECKPOINT_DIR = "checkpoint";
    private static final String DOWNLOAD_STATE_DIR = "download_state";
    
    @Autowired
    private SmartChunkDownloader chunkDownloader;
    
    @Autowired
    private SmartDownloadOptimizer optimizer;
    
    @Autowired
    private DownloadTaskTracker taskTracker;
    
    // 内存中的下载状态缓存
    private final Map<String, DownloadState> downloadStates = new ConcurrentHashMap<>();    @PostConstruct
    public void init() {
        // 创建断点续传所需的目录
        createDirectories();
        // 加载之前保存的下载状态
        loadSavedDownloadStates();
    }
    
    /**
     * 创建断点续传所需的目录
     */
    private void createDirectories() {
        try {
            Path checkpointDir = Paths.get(CHECKPOINT_DIR);
            Path downloadStateDir = Paths.get(CHECKPOINT_DIR, DOWNLOAD_STATE_DIR);
            
            if (!Files.exists(checkpointDir)) {
                Files.createDirectories(checkpointDir);
                log.info("创建断点续传目录: {}", checkpointDir);
            }
            
            if (!Files.exists(downloadStateDir)) {
                Files.createDirectories(downloadStateDir);
                log.info("创建下载状态目录: {}", downloadStateDir);
            }
        } catch (IOException e) {
            log.error("创建断点续传目录失败", e);
        }
    }
    
    /**
     * 加载保存的下载状态
     */
    private void loadSavedDownloadStates() {
        Path downloadStateDir = Paths.get(CHECKPOINT_DIR, DOWNLOAD_STATE_DIR);
        
        try {
            Files.list(downloadStateDir)
                .filter(path -> path.toString().endsWith(".state"))
                .forEach(path -> {
                    try (ObjectInputStream ois = new ObjectInputStream(new FileInputStream(path.toFile()))) {
                        DownloadState state = (DownloadState) ois.readObject();
                        downloadStates.put(state.getTaskId(), state);
                        log.info("加载下载状态: {}, 任务ID: {}", path, state.getTaskId());
                    } catch (IOException | ClassNotFoundException e) {
                        log.error("加载下载状态文件失败: {}", path, e);
                    }
                });
        } catch (IOException e) {
            log.error("扫描下载状态目录失败", e);
        }
    }    
    /**
     * 创建或获取下载状态
     *
     * @param taskId     任务ID
     * @param symbol     交易对
     * @param interval   K线间隔
     * @param startTime  开始时间
     * @param endTime    结束时间
     * @return 下载状态
     */
    public DownloadState getOrCreateDownloadState(String taskId, String symbol, KlineInterval interval,
                                                LocalDateTime startTime, LocalDateTime endTime) {
        return downloadStates.computeIfAbsent(taskId, id -> {
            DownloadState state = new DownloadState(id, symbol, interval, startTime, endTime);
            saveDownloadState(state);
            return state;
        });
    }
    
    /**
     * 保存下载状态
     *
     * @param state 下载状态
     */
    public void saveDownloadState(DownloadState state) {
        Path statePath = Paths.get(CHECKPOINT_DIR, DOWNLOAD_STATE_DIR, state.getTaskId() + ".state");
        
        try (ObjectOutputStream oos = new ObjectOutputStream(new FileOutputStream(statePath.toFile()))) {
            oos.writeObject(state);
            log.debug("保存下载状态: {}", statePath);
        } catch (IOException e) {
            log.error("保存下载状态失败: {}", statePath, e);
        }
    }
    
    /**
     * 删除下载状态
     *
     * @param taskId 任务ID
     */
    public void removeDownloadState(String taskId) {
        downloadStates.remove(taskId);
        Path statePath = Paths.get(CHECKPOINT_DIR, DOWNLOAD_STATE_DIR, taskId + ".state");
        
        try {
            Files.deleteIfExists(statePath);
            log.info("删除下载状态: {}", statePath);
        } catch (IOException e) {
            log.error("删除下载状态失败: {}", statePath, e);
        }
    }    
    /**
     * 更新下载状态中的分块状态
     *
     * @param state     下载状态
     * @param chunkIndex 分块索引
     * @param successful 是否成功
     * @param data       下载的数据
     */
    public void updateChunkStatus(DownloadState state, int chunkIndex, boolean successful, List<KlineDataDTO> data) {
        state.setChunkStatus(chunkIndex, successful);
        
        if (successful && data != null && !data.isEmpty()) {
            state.setChunkData(chunkIndex, data);
        }
        
        saveDownloadState(state);
        
        // 更新任务跟踪器中的状态
        taskTracker.updateChunkStatus(
            state.getTaskId(), 
            chunkIndex, 
            successful ? DownloadTaskTracker.TaskStatus.COMPLETED : DownloadTaskTracker.TaskStatus.FAILED,
            successful ? "分块下载成功" : "分块下载失败"
        );
    }    
    /**
     * 执行可恢复下载
     *
     * @param taskId       任务ID
     * @param symbol       交易对
     * @param interval     K线间隔
     * @param startTime    开始时间
     * @param endTime      结束时间
     * @param downloadFunc 执行实际下载的函数
     * @return 下载的K线数据
     */
    public List<KlineDataDTO> downloadWithResumption(
            String taskId,
            String symbol,
            KlineInterval interval,
            LocalDateTime startTime,
            LocalDateTime endTime,
            BiFunction<SmartChunkDownloader.TimeChunk, Integer, List<KlineDataDTO>> downloadFunc) {

        // 获取或创建下载状态
        DownloadState state = getOrCreateDownloadState(taskId, symbol, interval, startTime, endTime);
        
        if (state.isCompleted()) {
            log.info("任务 {} 已经完成，直接返回缓存的数据", taskId);
            return state.getAllDataSorted();
        }
        
        // 获取任务信息（如果存在）
        DownloadTaskTracker.TaskInfo taskInfo = taskTracker.getTaskInfo(taskId);
        
        // 将下载状态的分块信息同步到任务跟踪器
        if (taskInfo != null) {
            syncStateToTaskTracker(state, taskInfo);
        }
        
        // 封装下载函数，加入断点续传逻辑
        BiFunction<SmartChunkDownloader.TimeChunk, Integer, List<KlineDataDTO>> resumableFunc = 
            (chunk, retryCount) -> {
                int chunkIndex = chunk.getIndex();
                
                // 检查分块是否已经成功下载
                if (state.isChunkCompleted(chunkIndex)) {
                    log.info("分块 {} 已经下载完成，使用缓存数据", chunkIndex);
                    return state.getChunkData(chunkIndex);
                }
                
                try {
                    // 执行实际下载
                    List<KlineDataDTO> data = downloadFunc.apply(chunk, retryCount);
                    
                    // 更新下载状态
                    if (data != null && !data.isEmpty()) {
                        updateChunkStatus(state, chunkIndex, true, data);
                    } else {
                        updateChunkStatus(state, chunkIndex, false, null);
                    }
                    
                    return data;
                } catch (Exception e) {
                    log.error("下载分块 {} 失败", chunkIndex, e);
                    updateChunkStatus(state, chunkIndex, false, null);
                    throw e;
                }
            };
        
        // 使用智能分块下载器执行下载
        List<KlineDataDTO> allData = chunkDownloader.downloadWithSmartChunks(
            symbol, interval, startTime, endTime, resumableFunc, taskId, taskInfo
        );
        
        // 检查是否所有分块都已完成
        if (state.allChunksCompleted()) {
            state.setCompleted(true);
            saveDownloadState(state);
        }
        
        return allData;
    }    
    /**
     * 将下载状态同步到任务跟踪器
     *
     * @param state    下载状态
     * @param taskInfo 任务信息
     */
    private void syncStateToTaskTracker(DownloadState state, DownloadTaskTracker.TaskInfo taskInfo) {
        Map<Integer, Boolean> chunkStatuses = state.getChunkStatuses();
        
        for (Map.Entry<Integer, Boolean> entry : chunkStatuses.entrySet()) {
            int chunkIndex = entry.getKey();
            boolean completed = entry.getValue();
            
            taskTracker.updateChunkStatus(
                state.getTaskId(),
                chunkIndex,
                completed ? DownloadTaskTracker.TaskStatus.COMPLETED : DownloadTaskTracker.TaskStatus.FAILED,
                completed ? "分块下载已完成" : "分块下载失败，等待重试"
            );
        }
    }
    
    /**
     * 获取所有未完成的下载任务
     *
     * @return 未完成的下载状态列表
     */
    public List<DownloadState> getIncompleteDownloads() {
        List<DownloadState> incomplete = new ArrayList<>();
        
        for (DownloadState state : downloadStates.values()) {
            if (!state.isCompleted()) {
                incomplete.add(state);
            }
        }
        
        return incomplete;
    }
}