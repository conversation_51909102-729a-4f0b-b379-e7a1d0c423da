<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,INSTRUC<PERSON>ON_MISSED,INSTRUCTION_COVERED,<PERSON><PERSON><PERSON>_MISSED,<PERSON><PERSON><PERSON>_COVERED,LINE_MISSED,<PERSON>INE_COVERED,COMPLEXITY_MISSED,COMPLEXITY_COVERED,<PERSON><PERSON><PERSON>_MISSED,METHOD_COVERED
io.github.binance:binance-futures-connector-java,com.binance.connector.futures.client.impl,UMFuturesClientImpl,26,57,0,0,8,9,4,6,4,6
io.github.binance:binance-futures-connector-java,com.binance.connector.futures.client.impl,FuturesClientImpl,20,61,0,0,8,18,4,9,4,9
io.github.binance:binance-futures-connector-java,com.binance.connector.futures.client.impl,UMWebsocketClientImpl,105,0,2,0,16,0,7,0,6,0
io.github.binance:binance-futures-connector-java,com.binance.connector.futures.client.impl,CMWebsocketClientImpl,255,0,4,0,32,0,12,0,10,0
io.github.binance:binance-futures-connector-java,com.binance.connector.futures.client.impl,CMFuturesClientImpl,26,71,0,0,8,10,4,7,4,7
io.github.binance:binance-futures-connector-java,com.binance.connector.futures.client.impl,WebsocketClientImpl,860,0,14,0,114,0,47,0,40,0
io.github.binance:binance-futures-connector-java,com.binance.connector.futures.client.exceptions,BinanceConnectorException,0,4,0,0,0,2,0,1,0,1
io.github.binance:binance-futures-connector-java,com.binance.connector.futures.client.exceptions,BinanceClientException,9,29,0,0,3,10,3,2,3,2
io.github.binance:binance-futures-connector-java,com.binance.connector.futures.client.exceptions,BinanceServerException,10,7,0,0,4,3,2,1,2,1
io.github.binance:binance-futures-connector-java,com.binance.connector.futures.client.enums,HttpMethod,0,33,0,0,0,6,0,1,0,1
io.github.binance:binance-futures-connector-java,com.binance.connector.futures.client.enums,RequestType,0,21,0,0,0,4,0,1,0,1
io.github.binance:binance-futures-connector-java,com.binance.connector.futures.logging.util,MsEpochConverter,0,7,0,0,0,2,0,2,0,2
io.github.binance:binance-futures-connector-java,com.binance.connector.futures.client.impl.cm_futures,CMPortfolioMargin,0,12,0,0,0,3,0,2,0,2
io.github.binance:binance-futures-connector-java,com.binance.connector.futures.client.impl.cm_futures,CMAccount,0,128,0,0,0,23,0,11,0,11
io.github.binance:binance-futures-connector-java,com.binance.connector.futures.client.impl.cm_futures,CMUserData,0,7,0,0,0,2,0,1,0,1
io.github.binance:binance-futures-connector-java,com.binance.connector.futures.client.impl.cm_futures,CMMarket,0,98,0,0,0,23,0,10,0,10
io.github.binance:binance-futures-connector-java,com.binance.connector.futures.client,FuturesClient,5,0,0,0,1,0,1,0,1,0
io.github.binance:binance-futures-connector-java,com.binance.connector.futures.client.impl.futures,PortfolioMargin,26,49,0,0,9,10,6,3,6,3
io.github.binance:binance-futures-connector-java,com.binance.connector.futures.client.impl.futures,Market,20,381,0,0,8,66,4,26,4,26
io.github.binance:binance-futures-connector-java,com.binance.connector.futures.client.impl.futures,Account,42,391,0,0,9,69,4,25,4,25
io.github.binance:binance-futures-connector-java,com.binance.connector.futures.client.impl.futures,UserData,25,52,0,0,9,9,6,4,6,4
io.github.binance:binance-futures-connector-java,com.binance.connector.futures.client.impl.um_futures,UMUserData,0,7,0,0,0,2,0,1,0,1
io.github.binance:binance-futures-connector-java,com.binance.connector.futures.client.impl.um_futures,UMMarket,0,144,0,0,0,30,0,13,0,13
io.github.binance:binance-futures-connector-java,com.binance.connector.futures.client.impl.um_futures,UMAccount,0,149,0,0,0,25,0,13,0,13
io.github.binance:binance-futures-connector-java,com.binance.connector.futures.client.utils,SignatureGenerator,7,22,0,0,2,6,0,1,0,1
io.github.binance:binance-futures-connector-java,com.binance.connector.futures.client.utils,JSONParser,21,44,0,0,4,8,1,2,1,2
io.github.binance:binance-futures-connector-java,com.binance.connector.futures.client.utils,RequestHandler,22,129,7,8,3,27,7,8,0,7
io.github.binance:binance-futures-connector-java,com.binance.connector.futures.client.utils,WebSocketConnection,152,0,4,0,37,0,11,0,9,0
io.github.binance:binance-futures-connector-java,com.binance.connector.futures.client.utils,ResponseHandler,78,74,11,9,16,17,8,6,1,3
io.github.binance:binance-futures-connector-java,com.binance.connector.futures.client.utils,UrlBuilder,65,184,8,22,17,43,6,18,1,8
io.github.binance:binance-futures-connector-java,com.binance.connector.futures.client.utils,ParameterChecker,0,94,0,12,0,14,0,10,0,4
io.github.binance:binance-futures-connector-java,com.binance.connector.futures.client.utils,HttpClientSingleton,36,30,10,6,7,11,8,5,1,4
io.github.binance:binance-futures-connector-java,com.binance.connector.futures.client.utils,ProxyAuth,15,0,0,0,6,0,3,0,3,0
io.github.binance:binance-futures-connector-java,com.binance.connector.futures.client.utils,RequestBuilder,0,144,0,10,0,20,0,12,0,4
