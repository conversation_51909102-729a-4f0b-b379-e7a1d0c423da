package com.crypto.trading.common.dto.trade;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * OrderDTO单元测试类
 */
class OrderDTOTest {

    /**
     * 测试默认构造函数
     */
    @Test
    void testDefaultConstructor() {
        OrderDTO dto = new OrderDTO();
        assertNotNull(dto);
    }

    /**
     * 测试getter和setter方法
     */
    @Test
    void testGettersAndSetters() {
        OrderDTO dto = new OrderDTO();

        String orderId = "987654321";
        dto.setOrderId(orderId);
        assertEquals(orderId, dto.getOrderId());

        String symbol = "ETHUSDT";
        dto.setSymbol(symbol);
        assertEquals(symbol, dto.getSymbol());

        String clientOrderId = "client-order-456";
        dto.setClientOrderId(clientOrderId);
        assertEquals(clientOrderId, dto.getClientOrderId());
        
        String side = "SELL";
        dto.setSide(side);
        assertEquals(side, dto.getSide());
        
        String positionSide = "BOTH";
        dto.setPositionSide(positionSide);
        assertEquals(positionSide, dto.getPositionSide());
        
        String type = "MARKET";
        dto.setType(type);
        assertEquals(type, dto.getType());

        Double quantity = 2.0;
        dto.setQuantity(quantity);
        assertEquals(quantity, dto.getQuantity());

        Double price = 3000.00;
        dto.setPrice(price);
        assertEquals(price, dto.getPrice());

        Double executedQuantity = 1.0;
        dto.setExecutedQuantity(executedQuantity);
        assertEquals(executedQuantity, dto.getExecutedQuantity());
        
        Double executedPrice = 3000.00;
        dto.setExecutedPrice(executedPrice);
        assertEquals(executedPrice, dto.getExecutedPrice());

        String status = "FILLED";
        dto.setStatus(status);
        assertEquals(status, dto.getStatus());
        
        String strategyId = "strategy-001";
        dto.setStrategyId(strategyId);
        assertEquals(strategyId, dto.getStrategyId());
        
        String errorCode = "ERROR_CODE";
        dto.setErrorCode(errorCode);
        assertEquals(errorCode, dto.getErrorCode());
        
        String errorMessage = "Error message";
        dto.setErrorMessage(errorMessage);
        assertEquals(errorMessage, dto.getErrorMessage());

        Long createdTime = System.currentTimeMillis() - 600000; // 10分钟前
        dto.setCreatedTime(createdTime);
        assertEquals(createdTime, dto.getCreatedTime());

        Long updatedTime = System.currentTimeMillis();
        dto.setUpdatedTime(updatedTime);
        assertEquals(updatedTime, dto.getUpdatedTime());
        
        Long executedTime = System.currentTimeMillis();
        dto.setExecutedTime(executedTime);
        assertEquals(executedTime, dto.getExecutedTime());
        
        String remark = "备注信息";
        dto.setRemark(remark);
        assertEquals(remark, dto.getRemark());
    }

    /**
     * 测试toString方法
     */
    @Test
    void testToString() {
        OrderDTO dto = new OrderDTO();
        dto.setOrderId("123456789");
        dto.setSymbol("BTCUSDT");
        dto.setStatus("NEW");
        dto.setSide("BUY");
        dto.setType("LIMIT");

        String toString = dto.toString();
        
        // 因为实际类没有重写toString方法，所以只能验证基本的对象表示
        assertNotNull(toString);
        assertTrue(toString.contains("OrderDTO"));
    }
} 