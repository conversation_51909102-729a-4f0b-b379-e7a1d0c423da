#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
API兼容性文档生成模块

该模块提供API兼容性文档生成功能，包括验证报告生成、
兼容性矩阵生成和测试结果分析。
"""

import json
import time
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime, timezone
import uuid
from pathlib import Path
import csv
from loguru import logger

from .java_api_core import ValidationResult, PerformanceMetrics


@dataclass
class CompatibilityReport:
    """兼容性报告数据类"""
    report_id: str
    generation_time: datetime
    test_summary: Dict[str, Any]
    validation_results: List[ValidationResult]
    performance_metrics: List[PerformanceMetrics]
    compatibility_matrix: Dict[str, Dict[str, Any]]
    recommendations: List[str]
    overall_score: float


class ApiCompatibilityDocGenerator:
    """
    API兼容性文档生成器
    
    生成详细的API兼容性文档和报告。
    """
    
    def __init__(self, output_dir: str = "reports"):
        """
        初始化文档生成器
        
        Args:
            output_dir: 输出目录
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        logger.info(f"API兼容性文档生成器初始化完成，输出目录: {self.output_dir}")
    
    def generate_comprehensive_report(self, 
                                    validation_results: List[ValidationResult],
                                    performance_metrics: List[PerformanceMetrics],
                                    test_metadata: Dict[str, Any] = None) -> CompatibilityReport:
        """
        生成综合兼容性报告
        
        Args:
            validation_results: 验证结果列表
            performance_metrics: 性能指标列表
            test_metadata: 测试元数据
            
        Returns:
            兼容性报告
        """
        logger.info("开始生成综合兼容性报告")
        
        report_id = str(uuid.uuid4())
        generation_time = datetime.now(timezone.utc)
        
        # 生成测试摘要
        test_summary = self._generate_test_summary(validation_results, performance_metrics)
        
        # 生成兼容性矩阵
        compatibility_matrix = self._generate_compatibility_matrix(validation_results)
        
        # 生成建议
        recommendations = self._generate_recommendations(validation_results, performance_metrics)
        
        # 计算整体评分
        overall_score = self._calculate_overall_score(validation_results, performance_metrics)
        
        # 创建报告对象
        report = CompatibilityReport(
            report_id=report_id,
            generation_time=generation_time,
            test_summary=test_summary,
            validation_results=validation_results,
            performance_metrics=performance_metrics,
            compatibility_matrix=compatibility_matrix,
            recommendations=recommendations,
            overall_score=overall_score
        )
        
        # 保存报告
        self._save_report(report)
        
        logger.info(f"综合兼容性报告生成完成，报告ID: {report_id}")
        return report
    
    def _generate_test_summary(self, 
                              validation_results: List[ValidationResult],
                              performance_metrics: List[PerformanceMetrics]) -> Dict[str, Any]:
        """生成测试摘要"""
        total_tests = len(validation_results)
        passed_tests = sum(1 for result in validation_results if result.success)
        failed_tests = total_tests - passed_tests
        
        # 性能统计
        avg_latency = 0
        avg_throughput = 0
        if performance_metrics:
            latencies = [m.avg_latency_ms for m in performance_metrics if m.avg_latency_ms != float('inf')]
            throughputs = [m.throughput_ops_per_sec for m in performance_metrics if m.throughput_ops_per_sec > 0]
            
            avg_latency = sum(latencies) / len(latencies) if latencies else 0
            avg_throughput = sum(throughputs) / len(throughputs) if throughputs else 0
        
        return {
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "failed_tests": failed_tests,
            "success_rate": passed_tests / total_tests if total_tests > 0 else 0,
            "performance_summary": {
                "avg_latency_ms": avg_latency,
                "avg_throughput_ops_per_sec": avg_throughput,
                "total_performance_tests": len(performance_metrics)
            },
            "test_categories": self._categorize_test_results(validation_results)
        }
    
    def _categorize_test_results(self, validation_results: List[ValidationResult]) -> Dict[str, Dict[str, int]]:
        """按类别统计测试结果"""
        categories = {}
        
        for result in validation_results:
            # 从测试名称推断类别
            category = self._extract_test_category(result.test_name)
            
            if category not in categories:
                categories[category] = {"total": 0, "passed": 0, "failed": 0}
            
            categories[category]["total"] += 1
            if result.success:
                categories[category]["passed"] += 1
            else:
                categories[category]["failed"] += 1
        
        return categories
    
    def _extract_test_category(self, test_name: str) -> str:
        """从测试名称提取类别"""
        if "json" in test_name.lower():
            return "JSON序列化"
        elif "kafka" in test_name.lower():
            return "Kafka通信"
        elif "performance" in test_name.lower() or "latency" in test_name.lower() or "throughput" in test_name.lower():
            return "性能测试"
        elif "integration" in test_name.lower():
            return "集成测试"
        elif "compatibility" in test_name.lower():
            return "兼容性测试"
        else:
            return "其他测试"
    
    def _generate_compatibility_matrix(self, validation_results: List[ValidationResult]) -> Dict[str, Dict[str, Any]]:
        """生成兼容性矩阵"""
        matrix = {
            "message_formats": {},
            "data_types": {},
            "api_endpoints": {},
            "serialization": {},
            "performance": {}
        }
        
        for result in validation_results:
            # 根据测试名称分类
            if "message" in result.test_name.lower():
                matrix["message_formats"][result.test_name] = {
                    "compatible": result.success,
                    "details": result.details,
                    "execution_time_ms": result.execution_time_ms
                }
            elif "type" in result.test_name.lower():
                matrix["data_types"][result.test_name] = {
                    "compatible": result.success,
                    "details": result.details,
                    "execution_time_ms": result.execution_time_ms
                }
            elif "api" in result.test_name.lower():
                matrix["api_endpoints"][result.test_name] = {
                    "compatible": result.success,
                    "details": result.details,
                    "execution_time_ms": result.execution_time_ms
                }
            elif "serialization" in result.test_name.lower() or "json" in result.test_name.lower():
                matrix["serialization"][result.test_name] = {
                    "compatible": result.success,
                    "details": result.details,
                    "execution_time_ms": result.execution_time_ms
                }
            elif "performance" in result.test_name.lower():
                matrix["performance"][result.test_name] = {
                    "compatible": result.success,
                    "details": result.details,
                    "execution_time_ms": result.execution_time_ms
                }
        
        return matrix
    
    def _generate_recommendations(self, 
                                 validation_results: List[ValidationResult],
                                 performance_metrics: List[PerformanceMetrics]) -> List[str]:
        """生成改进建议"""
        recommendations = []
        
        # 分析失败的测试
        failed_tests = [result for result in validation_results if not result.success]
        
        if failed_tests:
            # 按类别分析失败原因
            failure_categories = {}
            for test in failed_tests:
                category = self._extract_test_category(test.test_name)
                if category not in failure_categories:
                    failure_categories[category] = []
                failure_categories[category].append(test)
            
            for category, tests in failure_categories.items():
                if category == "JSON序列化":
                    recommendations.append(f"JSON序列化存在{len(tests)}个问题，建议检查数据类型映射和编码格式")
                elif category == "Kafka通信":
                    recommendations.append(f"Kafka通信存在{len(tests)}个问题，建议检查主题配置和消息格式")
                elif category == "性能测试":
                    recommendations.append(f"性能测试存在{len(tests)}个问题，建议优化处理逻辑和资源使用")
                elif category == "集成测试":
                    recommendations.append(f"集成测试存在{len(tests)}个问题，建议检查组件间接口和数据流")
        
        # 分析性能指标
        if performance_metrics:
            high_latency_tests = [m for m in performance_metrics if m.avg_latency_ms > 100]
            low_throughput_tests = [m for m in performance_metrics if m.throughput_ops_per_sec < 50]
            
            if high_latency_tests:
                recommendations.append(f"发现{len(high_latency_tests)}个高延迟操作，建议优化处理逻辑")
            
            if low_throughput_tests:
                recommendations.append(f"发现{len(low_throughput_tests)}个低吞吐量操作，建议增加并发处理能力")
        
        # 通用建议
        if not recommendations:
            recommendations.append("所有兼容性测试通过，系统运行良好")
            recommendations.append("建议定期执行兼容性测试以确保持续兼容")
        
        recommendations.append("建议建立持续集成流程，自动执行兼容性测试")
        recommendations.append("建议监控生产环境中的跨语言通信性能")
        
        return recommendations
    
    def _calculate_overall_score(self, 
                                validation_results: List[ValidationResult],
                                performance_metrics: List[PerformanceMetrics]) -> float:
        """计算整体评分"""
        if not validation_results:
            return 0.0
        
        # 兼容性评分 (70%)
        compatibility_score = sum(1 for result in validation_results if result.success) / len(validation_results)
        
        # 性能评分 (30%)
        performance_score = 1.0  # 默认满分
        if performance_metrics:
            # 基于延迟和吞吐量计算性能评分
            latency_scores = []
            throughput_scores = []
            
            for metric in performance_metrics:
                # 延迟评分 (越低越好)
                if metric.avg_latency_ms != float('inf'):
                    if metric.avg_latency_ms <= 10:
                        latency_scores.append(1.0)
                    elif metric.avg_latency_ms <= 50:
                        latency_scores.append(0.8)
                    elif metric.avg_latency_ms <= 100:
                        latency_scores.append(0.6)
                    else:
                        latency_scores.append(0.4)
                
                # 吞吐量评分 (越高越好)
                if metric.throughput_ops_per_sec > 0:
                    if metric.throughput_ops_per_sec >= 200:
                        throughput_scores.append(1.0)
                    elif metric.throughput_ops_per_sec >= 100:
                        throughput_scores.append(0.8)
                    elif metric.throughput_ops_per_sec >= 50:
                        throughput_scores.append(0.6)
                    else:
                        throughput_scores.append(0.4)
            
            if latency_scores or throughput_scores:
                avg_latency_score = sum(latency_scores) / len(latency_scores) if latency_scores else 1.0
                avg_throughput_score = sum(throughput_scores) / len(throughput_scores) if throughput_scores else 1.0
                performance_score = (avg_latency_score + avg_throughput_score) / 2
        
        # 综合评分
        overall_score = compatibility_score * 0.7 + performance_score * 0.3
        return round(overall_score * 100, 2)  # 转换为百分制
    
    def _save_report(self, report: CompatibilityReport) -> None:
        """保存报告到文件"""
        try:
            # 生成JSON报告
            json_file = self.output_dir / f"compatibility_report_{report.report_id}.json"
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(asdict(report), f, indent=2, ensure_ascii=False, default=str)
            
            # 生成HTML报告
            html_file = self.output_dir / f"compatibility_report_{report.report_id}.html"
            self._generate_html_report(report, html_file)
            
            # 生成CSV摘要
            csv_file = self.output_dir / f"compatibility_summary_{report.report_id}.csv"
            self._generate_csv_summary(report, csv_file)
            
            logger.info(f"报告已保存: JSON={json_file}, HTML={html_file}, CSV={csv_file}")
            
        except Exception as e:
            logger.error(f"保存报告异常: {e}")
    
    def _generate_html_report(self, report: CompatibilityReport, output_file: Path) -> None:
        """生成HTML格式报告"""
        html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API兼容性测试报告</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .header {{ background-color: #f0f0f0; padding: 20px; border-radius: 5px; }}
        .summary {{ margin: 20px 0; }}
        .score {{ font-size: 24px; font-weight: bold; color: {'green' if report.overall_score >= 80 else 'orange' if report.overall_score >= 60 else 'red'}; }}
        .section {{ margin: 20px 0; }}
        .test-result {{ margin: 10px 0; padding: 10px; border-left: 4px solid {'green' if report.overall_score >= 80 else 'orange'}; }}
        .failed {{ border-left-color: red; }}
        .recommendations {{ background-color: #f9f9f9; padding: 15px; border-radius: 5px; }}
        table {{ width: 100%; border-collapse: collapse; }}
        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
        th {{ background-color: #f2f2f2; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>API兼容性测试报告</h1>
        <p><strong>报告ID:</strong> {report.report_id}</p>
        <p><strong>生成时间:</strong> {report.generation_time.strftime('%Y-%m-%d %H:%M:%S UTC')}</p>
        <p class="score"><strong>整体评分:</strong> {report.overall_score}/100</p>
    </div>
    
    <div class="summary">
        <h2>测试摘要</h2>
        <p><strong>总测试数:</strong> {report.test_summary['total_tests']}</p>
        <p><strong>通过测试:</strong> {report.test_summary['passed_tests']}</p>
        <p><strong>失败测试:</strong> {report.test_summary['failed_tests']}</p>
        <p><strong>成功率:</strong> {report.test_summary['success_rate']:.2%}</p>
    </div>
    
    <div class="section">
        <h2>测试结果详情</h2>
        {''.join([f'<div class="test-result {'failed' if not result.success else ''}"><strong>{result.test_name}</strong>: {result.message}</div>' for result in report.validation_results])}
    </div>
    
    <div class="section">
        <h2>性能指标</h2>
        <table>
            <tr><th>操作</th><th>平均延迟(ms)</th><th>吞吐量(ops/s)</th><th>成功率</th></tr>
            {''.join([f'<tr><td>{metric.operation}</td><td>{metric.avg_latency_ms:.2f}</td><td>{metric.throughput_ops_per_sec:.2f}</td><td>{metric.success_rate:.2%}</td></tr>' for metric in report.performance_metrics])}
        </table>
    </div>
    
    <div class="recommendations">
        <h2>改进建议</h2>
        <ul>
            {''.join([f'<li>{rec}</li>' for rec in report.recommendations])}
        </ul>
    </div>
</body>
</html>
        """
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
    
    def _generate_csv_summary(self, report: CompatibilityReport, output_file: Path) -> None:
        """生成CSV格式摘要"""
        with open(output_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            
            # 写入基本信息
            writer.writerow(['报告信息', ''])
            writer.writerow(['报告ID', report.report_id])
            writer.writerow(['生成时间', report.generation_time.strftime('%Y-%m-%d %H:%M:%S UTC')])
            writer.writerow(['整体评分', f'{report.overall_score}/100'])
            writer.writerow([''])
            
            # 写入测试摘要
            writer.writerow(['测试摘要', ''])
            writer.writerow(['总测试数', report.test_summary['total_tests']])
            writer.writerow(['通过测试', report.test_summary['passed_tests']])
            writer.writerow(['失败测试', report.test_summary['failed_tests']])
            writer.writerow(['成功率', f"{report.test_summary['success_rate']:.2%}"])
            writer.writerow([''])
            
            # 写入测试结果
            writer.writerow(['测试名称', '结果', '消息', '执行时间(ms)'])
            for result in report.validation_results:
                writer.writerow([
                    result.test_name,
                    '通过' if result.success else '失败',
                    result.message,
                    f'{result.execution_time_ms:.2f}'
                ])


class ValidationReportGenerator:
    """
    验证报告生成器
    
    专门生成验证测试报告。
    """
    
    def __init__(self):
        """初始化验证报告生成器"""
        logger.info("验证报告生成器初始化完成")
    
    def generate_validation_report(self, validation_results: List[ValidationResult]) -> Dict[str, Any]:
        """
        生成验证报告
        
        Args:
            validation_results: 验证结果列表
            
        Returns:
            验证报告
        """
        if not validation_results:
            return {"error": "没有验证结果"}
        
        # 统计信息
        total_tests = len(validation_results)
        passed_tests = sum(1 for result in validation_results if result.success)
        failed_tests = total_tests - passed_tests
        
        # 按类别分组
        categories = {}
        for result in validation_results:
            category = self._extract_category(result.test_name)
            if category not in categories:
                categories[category] = {"passed": 0, "failed": 0, "tests": []}
            
            if result.success:
                categories[category]["passed"] += 1
            else:
                categories[category]["failed"] += 1
            
            categories[category]["tests"].append(result)
        
        # 生成报告
        report = {
            "generation_time": datetime.now(timezone.utc).isoformat(),
            "summary": {
                "total_tests": total_tests,
                "passed_tests": passed_tests,
                "failed_tests": failed_tests,
                "success_rate": passed_tests / total_tests if total_tests > 0 else 0
            },
            "categories": categories,
            "failed_tests_details": [
                {
                    "test_name": result.test_name,
                    "message": result.message,
                    "details": result.details,
                    "execution_time_ms": result.execution_time_ms
                }
                for result in validation_results if not result.success
            ]
        }
        
        return report
    
    def _extract_category(self, test_name: str) -> str:
        """提取测试类别"""
        if "json" in test_name.lower():
            return "JSON兼容性"
        elif "kafka" in test_name.lower():
            return "Kafka兼容性"
        elif "serialization" in test_name.lower():
            return "序列化兼容性"
        elif "performance" in test_name.lower():
            return "性能测试"
        elif "integration" in test_name.lower():
            return "集成测试"
        else:
            return "其他"


class CompatibilityMatrixGenerator:
    """
    兼容性矩阵生成器
    
    生成详细的兼容性矩阵。
    """
    
    def __init__(self):
        """初始化兼容性矩阵生成器"""
        self.java_components = [
            "crypto-common", "crypto-market-data", "crypto-trade", 
            "crypto-sdk", "crypto-bootstrap"
        ]
        self.python_components = [
            "kafka_client", "data_processor", "unified_ml", 
            "risk_management", "strategy"
        ]
        logger.info("兼容性矩阵生成器初始化完成")
    
    def generate_compatibility_matrix(self, validation_results: List[ValidationResult]) -> Dict[str, Any]:
        """
        生成兼容性矩阵
        
        Args:
            validation_results: 验证结果列表
            
        Returns:
            兼容性矩阵
        """
        matrix = {
            "generation_time": datetime.now(timezone.utc).isoformat(),
            "java_components": self.java_components,
            "python_components": self.python_components,
            "compatibility_grid": {},
            "interface_compatibility": {},
            "data_format_compatibility": {},
            "performance_compatibility": {}
        }
        
        # 生成兼容性网格
        for java_comp in self.java_components:
            matrix["compatibility_grid"][java_comp] = {}
            for python_comp in self.python_components:
                # 基于测试结果评估兼容性
                compatibility_score = self._calculate_component_compatibility(
                    java_comp, python_comp, validation_results
                )
                matrix["compatibility_grid"][java_comp][python_comp] = compatibility_score
        
        # 接口兼容性
        matrix["interface_compatibility"] = self._analyze_interface_compatibility(validation_results)
        
        # 数据格式兼容性
        matrix["data_format_compatibility"] = self._analyze_data_format_compatibility(validation_results)
        
        # 性能兼容性
        matrix["performance_compatibility"] = self._analyze_performance_compatibility(validation_results)
        
        return matrix
    
    def _calculate_component_compatibility(self, 
                                         java_comp: str, 
                                         python_comp: str, 
                                         validation_results: List[ValidationResult]) -> Dict[str, Any]:
        """计算组件间兼容性"""
        # 简化的兼容性计算（实际实现中会更复杂）
        relevant_tests = [
            result for result in validation_results 
            if java_comp.lower() in result.test_name.lower() or 
               python_comp.lower() in result.test_name.lower()
        ]
        
        if not relevant_tests:
            return {"status": "UNKNOWN", "score": 0, "tests": 0}
        
        passed_tests = sum(1 for test in relevant_tests if test.success)
        total_tests = len(relevant_tests)
        score = passed_tests / total_tests if total_tests > 0 else 0
        
        if score >= 0.95:
            status = "FULLY_COMPATIBLE"
        elif score >= 0.80:
            status = "MOSTLY_COMPATIBLE"
        elif score >= 0.60:
            status = "PARTIALLY_COMPATIBLE"
        else:
            status = "INCOMPATIBLE"
        
        return {
            "status": status,
            "score": score,
            "tests": total_tests,
            "passed": passed_tests,
            "failed": total_tests - passed_tests
        }
    
    def _analyze_interface_compatibility(self, validation_results: List[ValidationResult]) -> Dict[str, Any]:
        """分析接口兼容性"""
        interface_tests = [
            result for result in validation_results 
            if "api" in result.test_name.lower() or "interface" in result.test_name.lower()
        ]
        
        return {
            "total_interface_tests": len(interface_tests),
            "passed_interface_tests": sum(1 for test in interface_tests if test.success),
            "interface_compatibility_score": sum(1 for test in interface_tests if test.success) / len(interface_tests) if interface_tests else 0
        }
    
    def _analyze_data_format_compatibility(self, validation_results: List[ValidationResult]) -> Dict[str, Any]:
        """分析数据格式兼容性"""
        format_tests = [
            result for result in validation_results 
            if "format" in result.test_name.lower() or "serialization" in result.test_name.lower() or "json" in result.test_name.lower()
        ]
        
        return {
            "total_format_tests": len(format_tests),
            "passed_format_tests": sum(1 for test in format_tests if test.success),
            "format_compatibility_score": sum(1 for test in format_tests if test.success) / len(format_tests) if format_tests else 0
        }
    
    def _analyze_performance_compatibility(self, validation_results: List[ValidationResult]) -> Dict[str, Any]:
        """分析性能兼容性"""
        performance_tests = [
            result for result in validation_results 
            if "performance" in result.test_name.lower() or "latency" in result.test_name.lower() or "throughput" in result.test_name.lower()
        ]
        
        return {
            "total_performance_tests": len(performance_tests),
            "passed_performance_tests": sum(1 for test in performance_tests if test.success),
            "performance_compatibility_score": sum(1 for test in performance_tests if test.success) / len(performance_tests) if performance_tests else 0
        }


if __name__ == "__main__":
    # 测试代码
    from .java_api_core import create_test_signal_message
    
    # 创建测试数据
    test_results = [
        ValidationResult(
            test_name="json_serialization_test",
            success=True,
            message="JSON序列化测试通过",
            details={"test": "data"},
            execution_time_ms=5.2,
            timestamp=datetime.now(timezone.utc)
        ),
        ValidationResult(
            test_name="kafka_compatibility_test",
            success=False,
            message="Kafka兼容性测试失败",
            details={"error": "connection_failed"},
            execution_time_ms=15.8,
            timestamp=datetime.now(timezone.utc)
        )
    ]
    
    test_metrics = [
        PerformanceMetrics(
            operation="kafka_message_send",
            avg_latency_ms=8.5,
            max_latency_ms=15.2,
            min_latency_ms=3.1,
            throughput_ops_per_sec=120.5,
            success_rate=0.95,
            total_operations=1000
        )
    ]
    
    # 生成报告
    doc_generator = ApiCompatibilityDocGenerator()
    report = doc_generator.generate_comprehensive_report(test_results, test_metrics)
    
    logger.info(f"测试报告生成完成，评分: {report.overall_score}/100")