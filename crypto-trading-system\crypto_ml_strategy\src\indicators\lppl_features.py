#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Log-Periodic Power Law (LPPL) 特征提取模块

实现LPPL模型特征提取，用于检测市场泡沫和崩溃
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Union
from scipy.optimize import differential_evolution

try:
    import lppls
    LPPLS_AVAILABLE = True
except ImportError:
    LPPLS_AVAILABLE = False
    logging.warning("lppls 库未安装，LPPL特征提取功能将不可用。请使用 pip install lppls 安装。")


class LPPLFeatureExtractor:
    """LPPL特征提取器，用于检测市场泡沫和崩溃模式"""

    def __init__(self, config: Dict = None):
        """
        初始化LPPL特征提取器
        
        Args:
            config: 配置参数字典，包括以下可选参数：
                - window_sizes: 滑动窗口大小列表，默认[100, 200, 500]
                - critical_time_range: 临界时间范围，默认[0.1, 0.3]
                - oscillation_min: 振荡最小值，默认2.5
                - oscillation_max: 振荡最大值，默认25
                - damping_min: 阻尼最小值，默认0.1
                - damping_max: 阻尼最大值，默认1.0
        """
        self.logger = logging.getLogger('cryptoMlStrategy.indicators.lppl')
        
        if not LPPLS_AVAILABLE:
            self.logger.error("lppls 库未安装，无法使用LPPL特征提取功能")
            return
        
        # 默认配置
        self.config = {
            'window_sizes': [100, 200, 500],
            'critical_time_range': [0.1, 0.3],
            'oscillation_min': 2.5,
            'oscillation_max': 25,
            'damping_min': 0.1,
            'damping_max': 1.0
        }
        
        # 更新用户配置
        if config:
            self.config.update(config)
            
        self.logger.info("LPPL特征提取器初始化完成")

    def fit_lppl(self, prices: np.ndarray, observation_times: np.ndarray) -> Tuple[Dict, float]:
        """
        拟合LPPL模型
        
        Args:
            prices: 价格数组
            observation_times: 观测时间数组
            
        Returns:
            拟合参数字典和拟合误差
        """
        if not LPPLS_AVAILABLE:
            return {}, float('inf')
        
        # 数据长度检查
        if len(prices) < 50:
            self.logger.warning("数据长度不足，无法拟合LPPL模型")
            return {}, float('inf')
        
        try:
            # 创建LPPLS模型
            lppls_model = lppls.LPPLS(prices, observation_times)
            
            # 设置约束条件
            tc_min, tc_max = observation_times[-1], observation_times[-1] * (1 + self.config['critical_time_range'][1])
            m_min, m_max = 0.1, 2.0
            omega_min, omega_max = self.config['oscillation_min'], self.config['oscillation_max']
            
            # 拟合模型
            lppls_model.fit(observations=len(prices), 
                           tc_bounds=(tc_min, tc_max),
                           m_bounds=(m_min, m_max),
                           omega_bounds=(omega_min, omega_max))
            
            # 获取最佳参数
            best_params = lppls_model.res_best
            
            # 计算拟合误差
            fitted_values = lppls_model.lppls(observation_times, best_params)
            mse = np.mean(np.square(prices - fitted_values))
            
            # 提取参数
            params = {
                'tc': best_params[0],    # 临界时间
                'A': best_params[1],     # 对数价格偏移
                'B': best_params[2],     # 上升/下降趋势强度
                'C': best_params[3],     # 振荡幅度
                'm': best_params[4],     # 幂律指数
                'omega': best_params[5], # 对数周期频率
                'phi': best_params[6]    # 相位参数
            }
            
            return params, mse
        
        except Exception as e:
            self.logger.error(f"LPPL拟合失败: {e}")
            return {}, float('inf')

    def calculate_bubble_confidence(self, params: Dict, current_time: float, tc_tolerance: float = 0.1) -> float:
        """
        根据LPPL参数计算泡沫置信度
        
        Args:
            params: LPPL参数字典
            current_time: 当前时间
            tc_tolerance: 临界时间容差
            
        Returns:
            泡沫置信度(0-1)
        """
        if not params:
            return 0.0
        
        try:
            # 提取参数
            tc = params['tc']
            m = params['m']
            omega = params['omega']
            B = params['B']
            
            # 检查关键泡沫条件
            is_m_valid = 0 < m < 1  # m应在(0,1)范围内
            is_omega_valid = omega > 6.0  # omega应大于6，表示适当的振荡
            is_tc_near = abs(tc - current_time) / current_time < tc_tolerance  # 临界时间接近当前时间
            is_B_negative = B < 0  # B应为负，表示价格在临界时间前上涨
            
            # 计算基础置信度
            base_confidence = 0.0
            if is_m_valid:
                base_confidence += 0.3
            if is_omega_valid:
                base_confidence += 0.2
            if is_tc_near:
                base_confidence += 0.3
            if is_B_negative:
                base_confidence += 0.2
                
            # 调整置信度
            # m越接近0.4(经验值)，置信度越高
            m_adjustment = 1.0 - min(abs(m - 0.4) / 0.4, 1.0)
            
            # 临界时间越近，置信度越高
            tc_distance = max(0, (tc - current_time) / current_time)
            tc_adjustment = max(0, 1.0 - tc_distance / tc_tolerance)
            
            # 计算最终置信度
            final_confidence = base_confidence * 0.7 + m_adjustment * 0.15 + tc_adjustment * 0.15
            
            return min(max(final_confidence, 0.0), 1.0)  # 确保在[0,1]范围内
        
        except Exception as e:
            self.logger.error(f"泡沫置信度计算失败: {e}")
            return 0.0

    def extract_features(self, df: pd.DataFrame, price_col: str = 'close') -> pd.DataFrame:
        """
        从DataFrame中提取LPPL特征
        
        Args:
            df: 包含价格数据的DataFrame
            price_col: 价格列名称
            
        Returns:
            添加了LPPL特征的DataFrame
        """
        if not LPPLS_AVAILABLE or df is None or df.empty:
            return df
        
        result_df = df.copy()
        
        try:
            # 准备数据
            prices = result_df[price_col].values
            times = np.arange(len(prices))
            
            # 为每个窗口大小计算LPPL特征
            for window_size in self.config['window_sizes']:
                if len(prices) < window_size:
                    continue
                    
                # 初始化特征列
                col_prefix = f'lppl_{window_size}_'
                result_df[f'{col_prefix}bubble_confidence'] = 0.0
                result_df[f'{col_prefix}crash_confidence'] = 0.0
                result_df[f'{col_prefix}tc_distance'] = np.nan
                result_df[f'{col_prefix}oscillation'] = np.nan
                result_df[f'{col_prefix}power_law'] = np.nan
                
                # 使用滑动窗口计算特征
                for i in range(window_size, len(prices)):
                    # 提取窗口数据
                    window_prices = prices[i-window_size:i]
                    window_times = times[i-window_size:i]
                    
                    # 对价格取对数
                    log_prices = np.log(window_prices)
                    
                    # 归一化时间至[0,1]
                    normalized_times = (window_times - window_times[0]) / (window_times[-1] - window_times[0])
                    
                    # 拟合上升(多头)LPPL模型
                    bull_params, bull_mse = self.fit_lppl(log_prices, normalized_times)
                    
                    # 拟合下降(空头)LPPL模型
                    bear_params, bear_mse = self.fit_lppl(-log_prices, normalized_times)
                    
                    # 计算当前归一化时间
                    current_norm_time = 1.0  # 窗口末尾的归一化时间
                    
                    # 计算泡沫和崩溃置信度
                    if bull_params:
                        bubble_confidence = self.calculate_bubble_confidence(bull_params, current_norm_time)
                        result_df.at[df.index[i-1], f'{col_prefix}bubble_confidence'] = bubble_confidence
                        result_df.at[df.index[i-1], f'{col_prefix}tc_distance'] = bull_params.get('tc', np.nan) - current_norm_time
                        result_df.at[df.index[i-1], f'{col_prefix}oscillation'] = bull_params.get('omega', np.nan)
                        result_df.at[df.index[i-1], f'{col_prefix}power_law'] = bull_params.get('m', np.nan)
                    
                    if bear_params:
                        crash_confidence = self.calculate_bubble_confidence(bear_params, current_norm_time)
                        result_df.at[df.index[i-1], f'{col_prefix}crash_confidence'] = crash_confidence
            
            # 计算综合特征
            if any(f'lppl_{ws}_bubble_confidence' in result_df.columns for ws in self.config['window_sizes']):
                # 计算多窗口平均值
                bubble_cols = [f'lppl_{ws}_bubble_confidence' for ws in self.config['window_sizes'] 
                               if f'lppl_{ws}_bubble_confidence' in result_df.columns]
                crash_cols = [f'lppl_{ws}_crash_confidence' for ws in self.config['window_sizes']
                              if f'lppl_{ws}_crash_confidence' in result_df.columns]
                
                if bubble_cols:
                    result_df['lppl_bubble_probability'] = result_df[bubble_cols].mean(axis=1)
                if crash_cols:
                    result_df['lppl_crash_probability'] = result_df[crash_cols].mean(axis=1)
                
                # 计算总体泡沫指标
                if 'lppl_bubble_probability' in result_df.columns and 'lppl_crash_probability' in result_df.columns:
                    result_df['lppl_indicator'] = result_df['lppl_bubble_probability'] - result_df['lppl_crash_probability']
            
            return result_df
        
        except Exception as e:
            self.logger.error(f"LPPL特征提取失败: {e}")
            return df