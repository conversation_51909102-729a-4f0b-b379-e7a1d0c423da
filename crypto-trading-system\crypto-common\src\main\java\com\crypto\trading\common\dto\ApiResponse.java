package com.crypto.trading.common.dto;

import com.crypto.trading.common.constant.SystemConstants;
import com.crypto.trading.common.exception.ErrorCode;

import java.io.Serial;
import java.io.Serializable;

/**
 * API响应类
 * <p>
 * 用于统一API响应格式，包括状态码、消息和数据
 * </p>
 *
 * @param <T> 响应数据类型
 * <AUTHOR>
 * @since 1.0.0
 */
public class ApiResponse<T> implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 状态码
     */
    private final int code;

    /**
     * 消息
     */
    private final String message;

    /**
     * 数据
     */
    private final T data;

    /**
     * 时间戳
     */
    private final long timestamp;

    /**
     * 构造函数
     *
     * @param code    状态码
     * @param message 消息
     * @param data    数据
     */
    public ApiResponse(int code, String message, T data) {
        this.code = code;
        this.message = message;
        this.data = data;
        this.timestamp = System.currentTimeMillis();
    }

    /**
     * 成功响应
     *
     * @param <T> 响应数据类型
     * @return API响应
     */
    public static <T> ApiResponse<T> success() {
        return new ApiResponse<>(SystemConstants.SUCCESS_CODE, SystemConstants.SUCCESS_MESSAGE, null);
    }

    /**
     * 成功响应
     *
     * @param data 响应数据
     * @param <T>  响应数据类型
     * @return API响应
     */
    public static <T> ApiResponse<T> success(T data) {
        return new ApiResponse<>(SystemConstants.SUCCESS_CODE, SystemConstants.SUCCESS_MESSAGE, data);
    }

    /**
     * 成功响应
     *
     * @param message 成功消息
     * @param data    响应数据
     * @param <T>     响应数据类型
     * @return API响应
     */
    public static <T> ApiResponse<T> success(String message, T data) {
        return new ApiResponse<>(SystemConstants.SUCCESS_CODE, message, data);
    }
    
    /**
     * 成功响应（支持数据和消息参数顺序反转）
     *
     * @param data    响应数据
     * @param message 成功消息
     * @param <T>     响应数据类型
     * @return API响应
     */
    public static <T> ApiResponse<T> success(T data, String message) {
        return new ApiResponse<>(SystemConstants.SUCCESS_CODE, message, data);
    }

    /**
     * 失败响应
     *
     * @param <T> 响应数据类型
     * @return API响应
     */
    public static <T> ApiResponse<T> error() {
        return new ApiResponse<>(SystemConstants.ERROR_CODE, SystemConstants.ERROR_MESSAGE, null);
    }

    /**
     * 失败响应
     *
     * @param message 错误消息
     * @param <T>     响应数据类型
     * @return API响应
     */
    public static <T> ApiResponse<T> error(String message) {
        return new ApiResponse<>(SystemConstants.ERROR_CODE, message, null);
    }

    /**
     * 失败响应
     *
     * @param code    状态码
     * @param message 错误消息
     * @param <T>     响应数据类型
     * @return API响应
     */
    public static <T> ApiResponse<T> error(int code, String message) {
        return new ApiResponse<>(code, message, null);
    }

    /**
     * 失败响应
     *
     * @param errorCode 错误码枚举
     * @param <T>       响应数据类型
     * @return API响应
     */
    public static <T> ApiResponse<T> error(ErrorCode errorCode) {
        return new ApiResponse<>(errorCode.getCode(), errorCode.getMessage(), null);
    }

    /**
     * 失败响应
     *
     * @param errorCode 错误码枚举
     * @param message   错误消息
     * @param <T>       响应数据类型
     * @return API响应
     */
    public static <T> ApiResponse<T> error(ErrorCode errorCode, String message) {
        return new ApiResponse<>(errorCode.getCode(), message, null);
    }

    /**
     * 获取状态码
     *
     * @return 状态码
     */
    public int getCode() {
        return code;
    }

    /**
     * 获取消息
     *
     * @return 消息
     */
    public String getMessage() {
        return message;
    }

    /**
     * 获取数据
     *
     * @return 数据
     */
    public T getData() {
        return data;
    }

    /**
     * 获取时间戳
     *
     * @return 时间戳
     */
    public long getTimestamp() {
        return timestamp;
    }
} 