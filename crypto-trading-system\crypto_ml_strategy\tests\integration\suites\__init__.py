#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
集成测试套件模块

包含各种集成测试套件的定义和实现。
"""

from .data_flow_integration_test_suite import DataFlowIntegrationTestSuite
from .ml_pipeline_integration_test_suite import MLPipelineIntegrationTestSuite
from .example_test_suite import ExampleTestSuite
from .risk_management_integration_test_suite import RiskManagementIntegrationTestSuite
from .test_risk_control_system import RiskControlSystemTest
from .test_position_management import PositionManagementTest
from .test_risk_monitoring import RiskMonitoringTest

__all__ = [
    'DataFlowIntegrationTestSuite',
    'MLPipelineIntegrationTestSuite',
    'ExampleTestSuite',
    'RiskManagementIntegrationTestSuite',
    'RiskControlSystemTest',
    'PositionManagementTest',
    'RiskMonitoringTest'
]

__version__ = "1.0.0"