package com.crypto.trading.trade.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * 交易执行配置类
 * <p>
 * 存储交易执行相关的配置参数
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Configuration
public class TradeExecutionConfig {

    /**
     * 是否为试运行模式
     * 在试运行模式下，不会实际发送订单到交易所
     */
    @Value("${trade.execution.dryRun:false}")
    private boolean dryRun;

    /**
     * 订单限速：每秒最大请求数
     */
    @Value("${trade.execution.rateLimit.order:10}")
    private int orderRateLimit;

    /**
     * 查询限速：每秒最大请求数
     */
    @Value("${trade.execution.rateLimit.query:20}")
    private int queryRateLimit;

    /**
     * 订单执行超时时间（毫秒）
     */
    @Value("${trade.execution.timeout:5000}")
    private long executionTimeout;

    /**
     * 订单状态查询间隔（毫秒）
     */
    @Value("${trade.execution.statusQueryInterval:1000}")
    private long statusQueryInterval;

    /**
     * 最大重试次数
     */
    @Value("${trade.execution.maxRetries:3}")
    private int maxRetries;

    /**
     * 重试间隔（毫秒）
     */
    @Value("${trade.execution.retryInterval:2000}")
    private long retryInterval;

    /**
     * 获取是否为试运行模式
     *
     * @return 是否为试运行模式
     */
    public boolean isDryRun() {
        return dryRun;
    }

    /**
     * 设置是否为试运行模式
     *
     * @param dryRun 是否为试运行模式
     */
    public void setDryRun(boolean dryRun) {
        this.dryRun = dryRun;
    }

    /**
     * 获取订单限速：每秒最大请求数
     *
     * @return 订单限速
     */
    public int getOrderRateLimit() {
        return orderRateLimit;
    }

    /**
     * 设置订单限速：每秒最大请求数
     *
     * @param orderRateLimit 订单限速
     */
    public void setOrderRateLimit(int orderRateLimit) {
        this.orderRateLimit = orderRateLimit;
    }

    /**
     * 获取查询限速：每秒最大请求数
     *
     * @return 查询限速
     */
    public int getQueryRateLimit() {
        return queryRateLimit;
    }

    /**
     * 设置查询限速：每秒最大请求数
     *
     * @param queryRateLimit 查询限速
     */
    public void setQueryRateLimit(int queryRateLimit) {
        this.queryRateLimit = queryRateLimit;
    }

    /**
     * 获取订单执行超时时间（毫秒）
     *
     * @return 订单执行超时时间
     */
    public long getExecutionTimeout() {
        return executionTimeout;
    }

    /**
     * 设置订单执行超时时间（毫秒）
     *
     * @param executionTimeout 订单执行超时时间
     */
    public void setExecutionTimeout(long executionTimeout) {
        this.executionTimeout = executionTimeout;
    }

    /**
     * 获取订单状态查询间隔（毫秒）
     *
     * @return 订单状态查询间隔
     */
    public long getStatusQueryInterval() {
        return statusQueryInterval;
    }

    /**
     * 设置订单状态查询间隔（毫秒）
     *
     * @param statusQueryInterval 订单状态查询间隔
     */
    public void setStatusQueryInterval(long statusQueryInterval) {
        this.statusQueryInterval = statusQueryInterval;
    }

    /**
     * 获取最大重试次数
     *
     * @return 最大重试次数
     */
    public int getMaxRetries() {
        return maxRetries;
    }

    /**
     * 设置最大重试次数
     *
     * @param maxRetries 最大重试次数
     */
    public void setMaxRetries(int maxRetries) {
        this.maxRetries = maxRetries;
    }

    /**
     * 获取重试间隔（毫秒）
     *
     * @return 重试间隔
     */
    public long getRetryInterval() {
        return retryInterval;
    }

    /**
     * 设置重试间隔（毫秒）
     *
     * @param retryInterval 重试间隔
     */
    public void setRetryInterval(long retryInterval) {
        this.retryInterval = retryInterval;
    }
    
    /**
     * 是否启用交易执行
     */
    private boolean enabled;
    
    /**
     * 获取是否启用交易执行
     *
     * @return 是否启用交易执行
     */
    public boolean isEnabled() {
        return enabled;
    }
    
    /**
     * 设置是否启用交易执行
     *
     * @param enabled 是否启用交易执行
     */
    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }
}