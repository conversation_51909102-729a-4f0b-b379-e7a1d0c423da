<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>CMAccount.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">io.github.binance:binance-futures-connector-java</a> &gt; <a href="index.source.html" class="el_package">com.binance.connector.futures.client.impl.cm_futures</a> &gt; <span class="el_source">CMAccount.java</span></div><h1>CMAccount.java</h1><pre class="source lang-java linenums">package com.binance.connector.futures.client.impl.cm_futures;

import com.binance.connector.futures.client.enums.HttpMethod;
import com.binance.connector.futures.client.utils.ParameterChecker;
import java.util.LinkedHashMap;
import com.binance.connector.futures.client.impl.futures.Account;
import com.binance.connector.futures.client.utils.ProxyAuth;

/**
 * &lt;h2&gt;Coin-Margined Trade Endpoints&lt;/h2&gt;
 * All endpoints under the
 * &lt;a href=&quot;https://developers.binance.com/docs/derivatives/coin-margined-futures/account/New-Future-Account-Transfer&quot;&gt;Futures Account/Trade Endpoint&lt;/a&gt;
 * section of the API documentation will be implemented in this class.
 * &lt;br&gt;
 * Response will be returned in &lt;i&gt;String format&lt;/i&gt;.
 */
public class CMAccount extends Account {
    public CMAccount(String productUrl, String apiKey, String secretKey, boolean showLimitUsage, ProxyAuth proxy) {
<span class="fc" id="L19">        super(productUrl, apiKey, secretKey, showLimitUsage, proxy);</span>
<span class="fc" id="L20">    }</span>

<span class="fc" id="L22">    private final String ORDER = &quot;/v1/order&quot;;</span>
    /**
     * Order modify function, currently only LIMIT order modification is supported, modified orders will be reordered in the match queue
     * &lt;br&gt;&lt;br&gt;
     * PUT /v1/order
     * &lt;br&gt;
     * @param
     * parameters LinkedHashedMap of String,Object pair
     *            where String is the name of the parameter and Object is the value of the parameter
     * &lt;br&gt;&lt;br&gt;
     * orderId -- optional/long &lt;br&gt;
     * origClientOrderId -- optional/string &lt;br&gt;
     * symbol - mandatory/string &lt;br&gt;
     * side -- mandatory/enum &lt;br&gt;
     * quantity -- optional/decimal &lt;br&gt;
     * price -- optional/decimal &lt;br&gt;
     * recvWindow -- optional/long &lt;br&gt;
     * @return String
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/coin-margined-futures/trade/Modify-Order&quot;&gt;
     *    https://developers.binance.com/docs/derivatives/coin-margined-futures/trade/Modify-Order&lt;/a&gt;
     */
    public String modifyOrder(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L44">        ParameterChecker.checkParameter(parameters, &quot;symbol&quot;, String.class);</span>
<span class="fc" id="L45">        ParameterChecker.checkParameter(parameters, &quot;side&quot;, String.class);</span>
<span class="fc" id="L46">        return getRequestHandler().sendSignedRequest(getProductUrl(), ORDER, parameters, HttpMethod.PUT, getShowLimitUsage());</span>
    }

<span class="fc" id="L49">    private final String ORDER_AMENDMENT = &quot;/v1/orderAmendment&quot;;</span>
    /**
     * Get order modification history
     * &lt;br&gt;&lt;br&gt;
     * GET /v1/orderAmendment
     * &lt;br&gt;
     * @param
     * parameters LinkedHashedMap of String,Object pair
     *            where String is the name of the parameter and Object is the value of the parameter
     * &lt;br&gt;&lt;br&gt;
     * symbol -- mandatory/string &lt;br&gt;
     * orderId -- optional/long &lt;br&gt;
     * origClientOrderId -- optional/string &lt;br&gt;
     * startTime -- optional/long &lt;br&gt;
     * endTime -- optional/long &lt;br&gt;
     * limit -- optional/integer &lt;br&gt;
     * recvWindow -- optional/long &lt;br&gt;
     * @return String
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/coin-margined-futures/trade/Get-Order-Modify-History&quot;&gt;
     *     https://developers.binance.com/docs/derivatives/coin-margined-futures/trade/Get-Order-Modify-History&lt;/a&gt;
     */
    public String orderModifyHistory(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L71">        ParameterChecker.checkParameter(parameters, &quot;symbol&quot;, String.class);</span>
<span class="fc" id="L72">        return getRequestHandler().sendSignedRequest(getProductUrl(), ORDER_AMENDMENT, parameters, HttpMethod.GET, getShowLimitUsage());</span>
    }


    /**
     * Get all open orders on a symbol. Careful when accessing this with no symbol.
     * &lt;br&gt;&lt;br&gt;
     * GET /v1/openOrders
     * &lt;br&gt;
     * @param
     * parameters LinkedHashedMap of String,Object pair
     *            where String is the name of the parameter and Object is the value of the parameter
     * &lt;br&gt;&lt;br&gt;
     * symbol -- optional/string &lt;br&gt;
     * pair -- optional/string &lt;br&gt;
     * recvWindow -- optional/long &lt;br&gt;
     * @return String
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/coin-margined-futures/trade/Current-All-Open-Orders&quot;&gt;
     *    https://developers.binance.com/docs/derivatives/coin-margined-futures/trade/Current-All-Open-Orders&lt;/a&gt;
     */
    public String currentAllOpenOrders(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L93">        return super.currentAllOpenOrders(parameters);</span>
    }

    /**
     * Get all open orders on a symbol. Careful when accessing this with no symbol.
     * &lt;br&gt;&lt;br&gt;
     * GET /v1/allOrders
     * &lt;br&gt;
     * @param
     * parameters LinkedHashedMap of String,Object pair
     *            where String is the name of the parameter and Object is the value of the parameter
     * &lt;br&gt;&lt;br&gt;
     * symbol -- optional/string &lt;br&gt;
     * pair -- optional/string &lt;br&gt;
     * orderId -- optional/long &lt;br&gt;
     * startTime -- optional/long &lt;br&gt;
     * endTime -- optional/long &lt;br&gt;
     * limit -- optional/integer &lt;br&gt;
     * recvWindow -- optional/long &lt;br&gt;
     * @return String
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/coin-margined-futures/trade/All-Orders&quot;&gt;
     *    https://developers.binance.com/docs/derivatives/coin-margined-futures/trade/All-Orders&lt;/a&gt;
     */
    public String allOrders(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L117">        ParameterChecker.checkOrParameters(parameters, &quot;symbol&quot;, &quot;pair&quot;);</span>
<span class="fc" id="L118">        return super.allOrders(parameters);</span>
    }

<span class="fc" id="L121">    private final String BALANCE = &quot;/v1/balance&quot;;</span>
    /**
     * Get Futures Account Balance
     * &lt;br&gt;&lt;br&gt;
     * GET /v1/balance
     * &lt;br&gt;
     * @param
     * parameters LinkedHashedMap of String,Object pair
     *            where String is the name of the parameter and Object is the value of the parameter
     * &lt;br&gt;&lt;br&gt;
     * recvWindow -- optional/long &lt;br&gt;
     * @return String
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/coin-margined-futures/account/Futures-Account-Balance&quot;&gt;
     *    https://developers.binance.com/docs/derivatives/coin-margined-futures/account/Futures-Account-Balance&lt;/a&gt;
     */
    public String futuresAccountBalance(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L137">        return getRequestHandler().sendSignedRequest(getProductUrl(), BALANCE, parameters, HttpMethod.GET, getShowLimitUsage());</span>
    }

<span class="fc" id="L140">    private final String ACCOUNT_INFORMATION = &quot;/v1/account&quot;;</span>
    /**
     * Get current account information. User in single-asset/ multi-assets mode will see different value, see comments in response section for detail.
     * &lt;br&gt;&lt;br&gt;
     * GET /v1/account
     * &lt;br&gt;
     * @param
     * parameters LinkedHashedMap of String,Object pair
     *            where String is the name of the parameter and Object is the value of the parameter
     * &lt;br&gt;&lt;br&gt;
     * recvWindow -- optional/long &lt;br&gt;
     * @return String
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/coin-margined-futures/account/Account-Information&quot;&gt;
     *    https://developers.binance.com/docs/derivatives/coin-margined-futures/account/Account-Information&lt;/a&gt;
     */
    public String accountInformation(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L156">        return getRequestHandler().sendSignedRequest(getProductUrl(), ACCOUNT_INFORMATION, parameters, HttpMethod.GET, getShowLimitUsage());</span>
    }

<span class="fc" id="L159">    private final String POSITION_RISK_V1 = &quot;/v1/positionRisk&quot;;</span>
    /**
     * Get current position information.
     * &lt;br&gt;&lt;br&gt;
     * GET /v1/positionRisk
     * &lt;br&gt;
     * @param
     * parameters LinkedHashedMap of String,Object pair
     *            where String is the name of the parameter and Object is the value of the parameter
     * &lt;br&gt;&lt;br&gt;
     * marginAsset -- optional/string &lt;br&gt;
     * pair -- optional/string &lt;br&gt;
     * recvWindow -- optional/long &lt;br&gt;
     * @return String
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/coin-margined-futures/trade/Position-Information&quot;&gt;
     *    https://developers.binance.com/docs/derivatives/coin-margined-futures/trade/Position-Information&lt;/a&gt;
     */
    public String positionInformation(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L177">        return getRequestHandler().sendSignedRequest(getProductUrl(), POSITION_RISK_V1, parameters, HttpMethod.GET, getShowLimitUsage());</span>
    }

    /**
     * Get trades for a specific account and symbol.
     * &lt;br&gt;&lt;br&gt;
     * GET /v1/userTrades
     * &lt;br&gt;
     * @param
     * parameters LinkedHashedMap of String,Object pair
     *            where String is the name of the parameter and Object is the value of the parameter
     * &lt;br&gt;&lt;br&gt;
     * symbol -- optional/string &lt;br&gt;
     * pair -- optional/string &lt;br&gt;
     * startTime -- optional/long &lt;br&gt;
     * endTime -- optional/long &lt;br&gt;
     * fromId -- optional/long &lt;br&gt;
     * limit -- optional/integer &lt;br&gt;
     * recvWindow -- optional/long &lt;br&gt;
     * @return String
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/coin-margined-futures/trade/Account-Trade-List&quot;&gt;
     *    https://developers.binance.com/docs/derivatives/coin-margined-futures/trade/Account-Trade-List&lt;/a&gt;
     */
    public String accountTradeList(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L201">        ParameterChecker.checkOrParameters(parameters, &quot;symbol&quot;, &quot;pair&quot;);</span>
<span class="fc" id="L202">        return super.accountTradeList(parameters);</span>
    }

    /**
     * Notional and Leverage Brackets
     * &lt;br&gt;&lt;br&gt;
     * GET /v1/leverageBracket
     * &lt;br&gt;
     * @param
     * parameters LinkedHashedMap of String,Object pair
     *            where String is the name of the parameter and Object is the value of the parameter
     * &lt;br&gt;&lt;br&gt;
     * pair -- optional/string &lt;br&gt;
     * recvWindow -- optional/long &lt;br&gt;
     * @return String
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/coin-margined-futures/account/Notional-Bracket-for-Symbol&quot;&gt;
     *    https://developers.binance.com/docs/derivatives/coin-margined-futures/account/Notional-Bracket-for-Symbol&lt;/a&gt;
     */
    public String getLeverageBracket(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L221">        return super.getLeverageBracket(parameters);</span>
    }

<span class="fc" id="L224">    private final String LEVERAGE_BRACKET_PAIR = &quot;/v2/leverageBracket&quot;;</span>
    /**
     * Notional and Leverage Brackets
     * &lt;br&gt;&lt;br&gt;
     * GET /v1/leverageBracket
     * &lt;br&gt;
     * @param
     * parameters LinkedHashedMap of String,Object pair
     *            where String is the name of the parameter and Object is the value of the parameter
     * &lt;br&gt;&lt;br&gt;
     * symbol -- optional/string &lt;br&gt;
     * recvWindow -- optional/long &lt;br&gt;
     * @return String
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/coin-margined-futures/account/Notional-Bracket-for-Pair&quot;&gt;
     *    https://developers.binance.com/docs/derivatives/coin-margined-futures/account/Notional-Bracket-for-Pair&lt;/a&gt;
     */
    public String getLeverageBracketForPair(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L241">        return getRequestHandler().sendSignedRequest(getProductUrl(), LEVERAGE_BRACKET_PAIR, parameters, HttpMethod.GET, getShowLimitUsage());</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.7.************</span></div></body></html>