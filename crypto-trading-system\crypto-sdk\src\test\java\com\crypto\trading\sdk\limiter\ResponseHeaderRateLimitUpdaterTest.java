package com.crypto.trading.sdk.limiter;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.HashMap;
import java.util.Map;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 响应头限流信息更新器测试类
 */
@ExtendWith(MockitoExtension.class)
public class ResponseHeaderRateLimitUpdaterTest {

    @Mock
    private RateLimiter rateLimiter;

    private ResponseHeaderRateLimitUpdater headerUpdater;

    @BeforeEach
    public void setUp() {
        headerUpdater = new ResponseHeaderRateLimitUpdater(rateLimiter);
    }

    @Test
    public void testProcessResponseHeaders_UsedWeight() {
        // 准备测试数据 - 权重使用情况
        Map<String, String> headers = new HashMap<>();
        headers.put("x-mbx-used-weight-1m", "300");
        headers.put("x-mbx-order-count-10s", "5");
        
        String endpoint = "klines";
        
        // 执行测试
        headerUpdater.processResponseHeaders(headers, endpoint);
        
        // 验证限流器被正确更新
        // 权重限制默认为1200/分钟，已使用300，剩余900
        verify(rateLimiter).updateConfig(eq("WEIGHT"), eq(900), eq(60000L));
    }

    @Test
    public void testProcessResponseHeaders_OrderCount() {
        // 准备测试数据 - 订单计数
        Map<String, String> headers = new HashMap<>();
        headers.put("x-mbx-order-count-10s", "5");
        headers.put("x-mbx-order-count-1m", "20");
        
        String endpoint = "order";
        
        // 执行测试
        headerUpdater.processResponseHeaders(headers, endpoint);
        
        // 验证限流器被正确更新
        // 10秒内订单限制为50，已使用5，剩余45
        verify(rateLimiter).updateConfig(eq("ORDER_10S"), eq(45), eq(10000L));
        
        // 1分钟内订单限制为300，已使用20，剩余280
        verify(rateLimiter).updateConfig(eq("ORDER_1M"), eq(280), eq(60000L));
    }

    @Test
    public void testProcessResponseHeaders_RateLimits() {
        // 准备测试数据 - 429响应中的限流信息
        Map<String, String> headers = new HashMap<>();
        headers.put("retry-after", "30");
        
        String endpoint = "depth";
        
        // 执行测试
        headerUpdater.processResponseHeaders(headers, endpoint);
        
        // 验证限流器被重置
        verify(rateLimiter).reset(eq(endpoint));
    }

    @Test
    public void testProcessResponseHeaders_EmptyHeaders() {
        // 准备测试数据 - 空响应头
        Map<String, String> headers = new HashMap<>();
        
        String endpoint = "ticker";
        
        // 执行测试
        headerUpdater.processResponseHeaders(headers, endpoint);
        
        // 验证限流器没有被更新
        verify(rateLimiter, never()).updateConfig(anyString(), anyInt(), anyLong());
        verify(rateLimiter, never()).reset(anyString());
    }

    @Test
    public void testProcessResponseHeaders_MultipleHeaders() {
        // 准备测试数据 - 多种限流信息
        Map<String, String> headers = new HashMap<>();
        headers.put("x-mbx-used-weight-1m", "500");
        headers.put("x-mbx-order-count-10s", "10");
        headers.put("x-mbx-order-count-1m", "50");
        
        String endpoint = "order";
        
        // 执行测试
        headerUpdater.processResponseHeaders(headers, endpoint);
        
        // 验证限流器被正确更新
        // 权重限制默认为1200/分钟，已使用500，剩余700
        verify(rateLimiter).updateConfig(eq("WEIGHT"), eq(700), eq(60000L));
        
        // 10秒内订单限制为50，已使用10，剩余40
        verify(rateLimiter).updateConfig(eq("ORDER_10S"), eq(40), eq(10000L));
        
        // 1分钟内订单限制为300，已使用50，剩余250
        verify(rateLimiter).updateConfig(eq("ORDER_1M"), eq(250), eq(60000L));
    }

    @Test
    public void testProcessResponseHeaders_InvalidValues() {
        // 准备测试数据 - 无效值
        Map<String, String> headers = new HashMap<>();
        headers.put("x-mbx-used-weight-1m", "invalid");
        headers.put("x-mbx-order-count-10s", "abc");
        
        String endpoint = "klines";
        
        // 执行测试
        headerUpdater.processResponseHeaders(headers, endpoint);
        
        // 验证限流器没有被更新
        verify(rateLimiter, never()).updateConfig(anyString(), anyInt(), anyLong());
    }

    @Test
    public void testProcessResponseHeaders_HighUsage() {
        // 准备测试数据 - 高使用率
        Map<String, String> headers = new HashMap<>();
        headers.put("x-mbx-used-weight-1m", "1100"); // 接近限制
        
        String endpoint = "depth";
        
        // 执行测试
        headerUpdater.processResponseHeaders(headers, endpoint);
        
        // 验证限流器被正确更新
        // 权重限制默认为1200/分钟，已使用1100，剩余100
        verify(rateLimiter).updateConfig(eq("WEIGHT"), eq(100), eq(60000L));
    }

    @Test
    public void testProcessResponseHeaders_ExceededLimit() {
        // 准备测试数据 - 超过限制
        Map<String, String> headers = new HashMap<>();
        headers.put("x-mbx-used-weight-1m", "1200"); // 达到限制
        headers.put("retry-after", "5");
        
        String endpoint = "trades";
        
        // 执行测试
        headerUpdater.processResponseHeaders(headers, endpoint);
        
        // 验证限流器被重置
        verify(rateLimiter).reset(eq(endpoint));
    }

    @Test
    public void testProcessResponseHeaders_DifferentEndpoints() {
        // 准备测试数据 - 不同端点
        Map<String, String> headers = new HashMap<>();
        headers.put("x-mbx-used-weight-1m", "300");
        
        // 执行测试 - 第一个端点
        headerUpdater.processResponseHeaders(headers, "klines");
        
        // 验证第一个端点的限流器更新
        verify(rateLimiter).updateConfig(eq("WEIGHT"), eq(900), eq(60000L));
        
        // 重置模拟对象
        reset(rateLimiter);
        
        // 执行测试 - 第二个端点
        headerUpdater.processResponseHeaders(headers, "depth");
        
        // 验证第二个端点的限流器更新
        verify(rateLimiter).updateConfig(eq("WEIGHT"), eq(900), eq(60000L));
    }
}