package com.crypto.trading.sdk.retry;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.function.Supplier;

/**
 * 异步指数退避重试策略
 * 用于异步API调用失败时进行重试，支持自定义重试条件和最大重试次数
 */
public class AsyncRetryPolicy {

    private static final Logger log = LoggerFactory.getLogger(AsyncRetryPolicy.class);

    private final int maxRetries;
    private final long initialBackoffMs;
    private final long maxBackoffMs;
    private final double backoffMultiplier;
    private final boolean addJitter;
    private final Predicate<Exception> retryCondition;
    private final ScheduledExecutorService scheduler;
    
    /**
     * 构造函数
     *
     * @param maxRetries        最大重试次数
     * @param initialBackoffMs  初始退避时间（毫秒）
     * @param maxBackoffMs      最大退避时间（毫秒）
     * @param backoffMultiplier 退避乘数
     * @param addJitter         是否添加抖动
     * @param retryCondition    重试条件
     * @param scheduler         调度器
     */
    public AsyncRetryPolicy(int maxRetries, long initialBackoffMs, long maxBackoffMs, 
                         double backoffMultiplier, boolean addJitter, 
                         Predicate<Exception> retryCondition, ScheduledExecutorService scheduler) {
        this.maxRetries = maxRetries;
        this.initialBackoffMs = initialBackoffMs;
        this.maxBackoffMs = maxBackoffMs;
        this.backoffMultiplier = backoffMultiplier;
        this.addJitter = addJitter;
        this.retryCondition = retryCondition;
        this.scheduler = scheduler;
    }    /**
     * 异步执行函数，失败时自动重试
     *
     * @param supplier 要执行的函数
     * @param <T>      返回类型
     * @return 包含结果的CompletableFuture
     */
    public <T> CompletableFuture<T> executeAsync(Supplier<T> supplier) {
        return executeAsyncInternal(supplier, 0);
    }
    
    /**
     * 异步执行函数，失败时自动重试（String特化版本）
     *
     * @param supplier 要执行的函数
     * @return 包含结果的CompletableFuture
     */
    public CompletableFuture<String> executeAsyncString(Supplier<String> supplier) {
        return executeAsyncInternal(supplier, 0);
    }
    
    /**
     * 内部异步执行函数，失败时自动重试
     *
     * @param supplier    要执行的函数
     * @param retryCount  当前重试次数
     * @param <T>         返回类型
     * @return 包含结果的CompletableFuture
     */
    private <T> CompletableFuture<T> executeAsyncInternal(Supplier<T> supplier, int retryCount) {
        CompletableFuture<T> future = new CompletableFuture<>();
        
        try {
            // 尝试执行操作
            T result = supplier.get();
            future.complete(result);
        } catch (Exception e) {
            // 检查是否应该重试
            if (retryCount < maxRetries && retryCondition.test(e)) {
                // 计算退避时间
                long delayMs = calculateBackoff(retryCount);
                
                log.info("异步操作失败，将在{}ms后重试 (尝试 {}/{}): {}", 
                        delayMs, retryCount + 1, maxRetries, e.getMessage());
                
                // 延迟后重试
                scheduler.schedule(() -> {
                    executeAsyncInternal(supplier, retryCount + 1)
                        .whenComplete((result, throwable) -> {
                            if (throwable != null) {
                                future.completeExceptionally(throwable);
                            } else {
                                future.complete(result);
                            }
                        });
                }, delayMs, TimeUnit.MILLISECONDS);
            } else {
                // 达到最大重试次数或不应该重试，完成future并带有异常
                future.completeExceptionally(e);
            }
        }
        
        return future;
    }
    
    /**
     * 计算退避时间
     *
     * @param retryCount 当前重试次数
     * @return 退避时间（毫秒）
     */
    private long calculateBackoff(int retryCount) {
        // 计算指数退避
        long delay = (long) (initialBackoffMs * Math.pow(backoffMultiplier, retryCount));
        
        // 确保不超过最大退避时间
        delay = Math.min(delay, maxBackoffMs);
        
        // 添加抖动（如果需要）
        if (addJitter) {
            delay = (long) (delay * (0.5 + ThreadLocalRandom.current().nextDouble() * 0.5));
        }
        
        return delay;
    }    /**
     * 构建器类，用于创建AsyncRetryPolicy实例
     */
    public static class Builder {
        private int maxRetries = 3;
        private long initialBackoffMs = 100;
        private long maxBackoffMs = 10000;
        private double backoffMultiplier = 2.0;
        private boolean addJitter = true;
        private Predicate<Exception> retryCondition = e -> true;
        private ScheduledExecutorService scheduler;
        
        /**
         * 设置最大重试次数
         * 
         * @param maxRetries 最大重试次数
         * @return Builder实例
         */
        public Builder maxRetries(int maxRetries) {
            this.maxRetries = maxRetries;
            return this;
        }
        
        /**
         * 设置初始退避时间（毫秒）
         * 
         * @param initialBackoffMs 初始退避时间
         * @return Builder实例
         */
        public Builder initialBackoffMs(long initialBackoffMs) {
            this.initialBackoffMs = initialBackoffMs;
            return this;
        }
        
        /**
         * 设置最大退避时间（毫秒）
         * 
         * @param maxBackoffMs 最大退避时间
         * @return Builder实例
         */
        public Builder maxBackoffMs(long maxBackoffMs) {
            this.maxBackoffMs = maxBackoffMs;
            return this;
        }
        
        /**
         * 设置退避乘数
         * 
         * @param backoffMultiplier 退避乘数
         * @return Builder实例
         */
        public Builder backoffMultiplier(double backoffMultiplier) {
            this.backoffMultiplier = backoffMultiplier;
            return this;
        }
        
        /**
         * 设置是否添加抖动
         * 
         * @param addJitter 是否添加抖动
         * @return Builder实例
         */
        public Builder addJitter(boolean addJitter) {
            this.addJitter = addJitter;
            return this;
        }
        
        /**
         * 设置重试条件
         * 
         * @param retryCondition 重试条件
         * @return Builder实例
         */
        public Builder retryCondition(Predicate<Exception> retryCondition) {
            this.retryCondition = retryCondition;
            return this;
        }
        
        /**
         * 设置调度器
         * 
         * @param scheduler 调度器
         * @return Builder实例
         */
        public Builder scheduler(ScheduledExecutorService scheduler) {
            this.scheduler = scheduler;
            return this;
        }
        
        /**
         * 构建异步重试策略
         * 
         * @return 异步重试策略实例
         */
        public AsyncRetryPolicy build() {
            if (scheduler == null) {
                throw new IllegalStateException("调度器不能为空");
            }
            return new AsyncRetryPolicy(maxRetries, initialBackoffMs, maxBackoffMs, backoffMultiplier, addJitter, retryCondition, scheduler);
        }
    }
}