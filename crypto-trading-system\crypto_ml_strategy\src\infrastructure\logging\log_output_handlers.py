"""
Crypto ML Strategy - 日志输出处理器

该模块实现了多种日志输出处理器，支持文件、控制台、远程服务输出，
以及异步日志写入和缓冲区管理功能。

主要功能：
- LogOutputManager: 日志输出管理器
- FileOutputHandler: 文件输出处理器
- ConsoleOutputHandler: 控制台输出处理器
- RemoteLogHandler: 远程日志服务处理器
- AsyncLogWriter: 异步日志写入器
- LogBufferManager: 日志缓冲区管理器

作者: Crypto ML Strategy Team
日期: 2024-01-20
版本: 1.0.0
"""

import asyncio
import json
import queue
import threading
import time
from abc import ABC, abstractmethod
from dataclasses import dataclass
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Dict, List, Optional, Union, Callable
from loguru import logger
import aiohttp
import aiofiles
from collections import deque


@dataclass
class LogEntry:
    """日志条目"""
    timestamp: datetime
    level: str
    message: str
    data: Dict[str, Any]
    formatted_message: str
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "timestamp": self.timestamp.isoformat(),
            "level": self.level,
            "message": self.message,
            "data": self.data,
            "formatted_message": self.formatted_message
        }


class OutputHandler(ABC):
    """输出处理器基类"""
    
    def __init__(self, name: str):
        self.name = name
        self._enabled = True
        self._lock = threading.RLock()
    
    @abstractmethod
    async def write_log(self, entry: LogEntry) -> bool:
        """写入日志条目"""
        pass
    
    @abstractmethod
    def close(self) -> None:
        """关闭处理器"""
        pass
    
    def enable(self) -> None:
        """启用处理器"""
        with self._lock:
            self._enabled = True
    
    def disable(self) -> None:
        """禁用处理器"""
        with self._lock:
            self._enabled = False
    
    def is_enabled(self) -> bool:
        """检查是否启用"""
        with self._lock:
            return self._enabled


class FileOutputHandler(OutputHandler):
    """文件输出处理器"""
    
    def __init__(self, name: str, file_path: Union[str, Path], 
                 max_file_size: int = 100 * 1024 * 1024,  # 100MB
                 backup_count: int = 5,
                 encoding: str = 'utf-8'):
        super().__init__(name)
        self.file_path = Path(file_path)
        self.max_file_size = max_file_size
        self.backup_count = backup_count
        self.encoding = encoding
        
        # 确保目录存在
        self.file_path.parent.mkdir(parents=True, exist_ok=True)
        
        self._current_size = 0
        self._file_handle: Optional[Any] = None
        self._init_file()
    
    def _init_file(self) -> None:
        """初始化文件"""
        try:
            if self.file_path.exists():
                self._current_size = self.file_path.stat().st_size
            else:
                self._current_size = 0
        except Exception as e:
            logger.error(f"Failed to initialize file {self.file_path}: {e}")
            self._current_size = 0
    
    async def write_log(self, entry: LogEntry) -> bool:
        """写入日志到文件"""
        if not self.is_enabled():
            return False
        
        try:
            # 检查文件大小
            if self._current_size >= self.max_file_size:
                await self._rotate_file()
            
            # 写入日志
            log_line = entry.formatted_message + '\n'
            
            async with aiofiles.open(self.file_path, 'a', encoding=self.encoding) as f:
                await f.write(log_line)
                self._current_size += len(log_line.encode(self.encoding))
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to write log to file {self.file_path}: {e}")
            return False
    
    async def _rotate_file(self) -> None:
        """轮转文件"""
        try:
            # 删除最旧的备份文件
            oldest_backup = self.file_path.with_suffix(f"{self.file_path.suffix}.{self.backup_count}")
            if oldest_backup.exists():
                oldest_backup.unlink()
            
            # 移动现有备份文件
            for i in range(self.backup_count - 1, 0, -1):
                old_backup = self.file_path.with_suffix(f"{self.file_path.suffix}.{i}")
                new_backup = self.file_path.with_suffix(f"{self.file_path.suffix}.{i + 1}")
                
                if old_backup.exists():
                    old_backup.rename(new_backup)
            
            # 移动当前文件
            if self.file_path.exists():
                backup_path = self.file_path.with_suffix(f"{self.file_path.suffix}.1")
                self.file_path.rename(backup_path)
            
            # 重置大小计数器
            self._current_size = 0
            
            logger.info(f"File rotated: {self.file_path}")
            
        except Exception as e:
            logger.error(f"Failed to rotate file {self.file_path}: {e}")
    
    def close(self) -> None:
        """关闭文件处理器"""
        try:
            if self._file_handle:
                self._file_handle.close()
                self._file_handle = None
        except Exception as e:
            logger.error(f"Error closing file handler: {e}")


class ConsoleOutputHandler(OutputHandler):
    """控制台输出处理器"""
    
    def __init__(self, name: str, use_colors: bool = True, 
                 error_to_stderr: bool = True):
        super().__init__(name)
        self.use_colors = use_colors
        self.error_to_stderr = error_to_stderr
        
        # 颜色映射
        self.color_map = {
            'TRACE': '\033[90m',     # 灰色
            'DEBUG': '\033[36m',     # 青色
            'INFO': '\033[32m',      # 绿色
            'SUCCESS': '\033[92m',   # 亮绿色
            'WARNING': '\033[33m',   # 黄色
            'ERROR': '\033[31m',     # 红色
            'CRITICAL': '\033[91m',  # 亮红色
        }
        self.reset_color = '\033[0m'
    
    async def write_log(self, entry: LogEntry) -> bool:
        """写入日志到控制台"""
        if not self.is_enabled():
            return False
        
        try:
            message = entry.formatted_message
            
            # 添加颜色
            if self.use_colors and entry.level in self.color_map:
                color = self.color_map[entry.level]
                message = f"{color}{message}{self.reset_color}"
            
            # 选择输出流
            if self.error_to_stderr and entry.level in ['ERROR', 'CRITICAL']:
                print(message, file=__import__('sys').stderr)
            else:
                print(message)
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to write log to console: {e}")
            return False
    
    def close(self) -> None:
        """关闭控制台处理器"""
        pass  # 控制台不需要关闭


class RemoteLogHandler(OutputHandler):
    """远程日志服务处理器"""
    
    def __init__(self, name: str, endpoint_url: str, 
                 api_key: Optional[str] = None,
                 batch_size: int = 100,
                 flush_interval: int = 30,
                 timeout: int = 10):
        super().__init__(name)
        self.endpoint_url = endpoint_url
        self.api_key = api_key
        self.batch_size = batch_size
        self.flush_interval = flush_interval
        self.timeout = timeout
        
        self._batch_buffer: List[LogEntry] = []
        self._last_flush = datetime.now()
        self._session: Optional[aiohttp.ClientSession] = None
        self._flush_task: Optional[asyncio.Task] = None
        
        # 启动定期刷新任务
        self._start_flush_task()
    
    def _start_flush_task(self) -> None:
        """启动定期刷新任务"""
        try:
            loop = asyncio.get_event_loop()
            self._flush_task = loop.create_task(self._periodic_flush())
        except RuntimeError:
            # 如果没有事件循环，稍后再启动
            pass
    
    async def _periodic_flush(self) -> None:
        """定期刷新缓冲区"""
        while self.is_enabled():
            try:
                await asyncio.sleep(self.flush_interval)
                await self._flush_buffer()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in periodic flush: {e}")
    
    async def write_log(self, entry: LogEntry) -> bool:
        """写入日志到远程服务"""
        if not self.is_enabled():
            return False
        
        try:
            with self._lock:
                self._batch_buffer.append(entry)
                
                # 检查是否需要刷新
                should_flush = (
                    len(self._batch_buffer) >= self.batch_size or
                    datetime.now() - self._last_flush >= timedelta(seconds=self.flush_interval)
                )
            
            if should_flush:
                await self._flush_buffer()
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to write log to remote service: {e}")
            return False
    
    async def _flush_buffer(self) -> None:
        """刷新缓冲区到远程服务"""
        try:
            with self._lock:
                if not self._batch_buffer:
                    return
                
                batch = self._batch_buffer.copy()
                self._batch_buffer.clear()
                self._last_flush = datetime.now()
            
            # 准备数据
            payload = {
                "logs": [entry.to_dict() for entry in batch],
                "timestamp": datetime.now().isoformat(),
                "source": "crypto_ml_strategy"
            }
            
            # 发送到远程服务
            await self._send_to_remote(payload)
            
        except Exception as e:
            logger.error(f"Failed to flush buffer to remote service: {e}")
            
            # 将失败的日志重新加入缓冲区
            with self._lock:
                self._batch_buffer.extend(batch)
    
    async def _send_to_remote(self, payload: Dict[str, Any]) -> None:
        """发送数据到远程服务"""
        try:
            if self._session is None:
                connector = aiohttp.TCPConnector(limit=10, limit_per_host=5)
                timeout = aiohttp.ClientTimeout(total=self.timeout)
                self._session = aiohttp.ClientSession(
                    connector=connector,
                    timeout=timeout
                )
            
            headers = {'Content-Type': 'application/json'}
            if self.api_key:
                headers['Authorization'] = f'Bearer {self.api_key}'
            
            async with self._session.post(
                self.endpoint_url,
                json=payload,
                headers=headers
            ) as response:
                if response.status != 200:
                    logger.warning(f"Remote log service returned status {response.status}")
                
        except Exception as e:
            logger.error(f"Failed to send logs to remote service: {e}")
            raise
    
    def close(self) -> None:
        """关闭远程处理器"""
        try:
            # 取消定期刷新任务
            if self._flush_task and not self._flush_task.done():
                self._flush_task.cancel()
            
            # 最后一次刷新
            if self._batch_buffer:
                asyncio.create_task(self._flush_buffer())
            
            # 关闭会话
            if self._session:
                asyncio.create_task(self._session.close())
                self._session = None
                
        except Exception as e:
            logger.error(f"Error closing remote handler: {e}")


class LogBufferManager:
    """日志缓冲区管理器"""
    
    def __init__(self, max_size: int = 10000, 
                 flush_interval: float = 1.0,
                 max_memory_mb: int = 100):
        self.max_size = max_size
        self.flush_interval = flush_interval
        self.max_memory_mb = max_memory_mb
        
        self._buffer: deque = deque(maxlen=max_size)
        self._buffer_size_bytes = 0
        self._last_flush = time.time()
        self._lock = threading.RLock()
        self._flush_callbacks: List[Callable[[List[LogEntry]], None]] = []
    
    def add_log_entry(self, entry: LogEntry) -> None:
        """添加日志条目到缓冲区"""
        try:
            with self._lock:
                # 估算条目大小
                entry_size = len(entry.formatted_message.encode('utf-8'))
                entry_size += len(json.dumps(entry.data).encode('utf-8'))
                
                # 检查内存限制
                max_size_bytes = self.max_memory_mb * 1024 * 1024
                if self._buffer_size_bytes + entry_size > max_size_bytes:
                    self._force_flush()
                
                self._buffer.append(entry)
                self._buffer_size_bytes += entry_size
                
                # 检查是否需要刷新
                current_time = time.time()
                should_flush = (
                    len(self._buffer) >= self.max_size or
                    current_time - self._last_flush >= self.flush_interval
                )
                
                if should_flush:
                    self._flush_buffer()
                    
        except Exception as e:
            logger.error(f"Failed to add log entry to buffer: {e}")
    
    def _flush_buffer(self) -> None:
        """刷新缓冲区"""
        try:
            with self._lock:
                if not self._buffer:
                    return
                
                # 复制缓冲区内容
                entries = list(self._buffer)
                self._buffer.clear()
                self._buffer_size_bytes = 0
                self._last_flush = time.time()
            
            # 调用刷新回调
            for callback in self._flush_callbacks:
                try:
                    callback(entries)
                except Exception as e:
                    logger.error(f"Error in flush callback: {e}")
                    
        except Exception as e:
            logger.error(f"Failed to flush buffer: {e}")
    
    def _force_flush(self) -> None:
        """强制刷新（内存压力时）"""
        try:
            with self._lock:
                # 移除最旧的一半条目
                remove_count = len(self._buffer) // 2
                for _ in range(remove_count):
                    if self._buffer:
                        removed_entry = self._buffer.popleft()
                        # 重新计算大小（简化估算）
                        self._buffer_size_bytes = max(0, self._buffer_size_bytes - 1000)
                
                logger.warning(f"Forced buffer flush: removed {remove_count} entries")
                
        except Exception as e:
            logger.error(f"Failed to force flush buffer: {e}")
    
    def add_flush_callback(self, callback: Callable[[List[LogEntry]], None]) -> None:
        """添加刷新回调"""
        self._flush_callbacks.append(callback)
    
    def get_buffer_stats(self) -> Dict[str, Any]:
        """获取缓冲区统计信息"""
        with self._lock:
            return {
                "buffer_size": len(self._buffer),
                "max_size": self.max_size,
                "buffer_size_bytes": self._buffer_size_bytes,
                "max_memory_mb": self.max_memory_mb,
                "flush_interval": self.flush_interval,
                "last_flush": self._last_flush,
                "callback_count": len(self._flush_callbacks)
            }


class AsyncLogWriter:
    """异步日志写入器"""
    
    def __init__(self, max_workers: int = 3):
        self.max_workers = max_workers
        self._write_queue: asyncio.Queue = asyncio.Queue(maxsize=1000)
        self._workers: List[asyncio.Task] = []
        self._running = False
        self._lock = asyncio.Lock()
    
    async def start(self) -> None:
        """启动异步写入器"""
        async with self._lock:
            if self._running:
                return
            
            self._running = True
            
            # 启动工作线程
            for i in range(self.max_workers):
                worker = asyncio.create_task(self._worker_loop(f"worker-{i}"))
                self._workers.append(worker)
            
            logger.info(f"Async log writer started with {self.max_workers} workers")
    
    async def stop(self) -> None:
        """停止异步写入器"""
        async with self._lock:
            if not self._running:
                return
            
            self._running = False
            
            # 等待队列清空
            await self._write_queue.join()
            
            # 取消工作任务
            for worker in self._workers:
                worker.cancel()
            
            # 等待工作任务完成
            await asyncio.gather(*self._workers, return_exceptions=True)
            
            self._workers.clear()
            logger.info("Async log writer stopped")
    
    async def write_async(self, handler: OutputHandler, entry: LogEntry) -> None:
        """异步写入日志"""
        if not self._running:
            await self.start()
        
        try:
            await self._write_queue.put((handler, entry), timeout=1.0)
        except asyncio.TimeoutError:
            logger.warning("Log write queue is full, dropping log entry")
    
    async def _worker_loop(self, worker_name: str) -> None:
        """工作线程循环"""
        logger.debug(f"Log writer worker {worker_name} started")
        
        while self._running:
            try:
                # 获取写入任务
                handler, entry = await asyncio.wait_for(
                    self._write_queue.get(), timeout=1.0
                )
                
                # 执行写入
                try:
                    await handler.write_log(entry)
                except Exception as e:
                    logger.error(f"Error writing log in {worker_name}: {e}")
                finally:
                    self._write_queue.task_done()
                    
            except asyncio.TimeoutError:
                continue
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in log writer {worker_name}: {e}")
        
        logger.debug(f"Log writer worker {worker_name} stopped")


class LogOutputManager:
    """日志输出管理器"""
    
    def __init__(self):
        self._handlers: Dict[str, OutputHandler] = {}
        self._buffer_manager = LogBufferManager()
        self._async_writer = AsyncLogWriter()
        self._lock = threading.RLock()
        
        # 设置缓冲区刷新回调
        self._buffer_manager.add_flush_callback(self._handle_buffer_flush)
    
    def add_handler(self, handler: OutputHandler) -> None:
        """添加输出处理器"""
        with self._lock:
            self._handlers[handler.name] = handler
            logger.info(f"Added log output handler: {handler.name}")
    
    def remove_handler(self, name: str) -> None:
        """移除输出处理器"""
        with self._lock:
            handler = self._handlers.pop(name, None)
            if handler:
                handler.close()
                logger.info(f"Removed log output handler: {name}")
    
    def get_handler(self, name: str) -> Optional[OutputHandler]:
        """获取输出处理器"""
        with self._lock:
            return self._handlers.get(name)
    
    def list_handlers(self) -> List[str]:
        """列出所有处理器名称"""
        with self._lock:
            return list(self._handlers.keys())
    
    async def write_log(self, entry: LogEntry) -> None:
        """写入日志到所有处理器"""
        # 添加到缓冲区
        self._buffer_manager.add_log_entry(entry)
    
    def _handle_buffer_flush(self, entries: List[LogEntry]) -> None:
        """处理缓冲区刷新"""
        try:
            # 为每个处理器异步写入
            for entry in entries:
                for handler in self._handlers.values():
                    if handler.is_enabled():
                        asyncio.create_task(
                            self._async_writer.write_async(handler, entry)
                        )
        except Exception as e:
            logger.error(f"Error handling buffer flush: {e}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        with self._lock:
            handler_stats = {}
            for name, handler in self._handlers.items():
                handler_stats[name] = {
                    "enabled": handler.is_enabled(),
                    "type": type(handler).__name__
                }
            
            return {
                "handlers": handler_stats,
                "buffer_stats": self._buffer_manager.get_buffer_stats()
            }
    
    async def shutdown(self) -> None:
        """关闭输出管理器"""
        try:
            # 停止异步写入器
            await self._async_writer.stop()
            
            # 关闭所有处理器
            with self._lock:
                for handler in self._handlers.values():
                    handler.close()
                
                self._handlers.clear()
            
            logger.info("Log output manager shutdown completed")
            
        except Exception as e:
            logger.error(f"Error during output manager shutdown: {e}")


class LogOutputHandlers:
    """
    日志输出处理器主类
    
    这是日志输出系统的主要接口类，提供了统一的日志输出管理功能，
    包括文件输出、控制台输出、远程日志服务和异步写入管理。
    
    主要功能：
    - 多种输出处理器管理
    - 异步日志写入
    - 缓冲区管理
    - 输出统计和监控
    
    使用示例：
        handlers = LogOutputHandlers()
        handlers.add_file_handler("app.log")
        handlers.add_console_handler()
        await handlers.write_log(log_entry)
    """
    
    def __init__(self):
        """初始化日志输出处理器"""
        self._output_manager = LogOutputManager()
        self._initialized = False
        
        logger.info("LogOutputHandlers initialized")
    
    async def initialize(self) -> None:
        """初始化处理器系统"""
        if self._initialized:
            return
        
        try:
            # 启动异步写入器
            await self._output_manager._async_writer.start()
            self._initialized = True
            
            logger.info("LogOutputHandlers system initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize LogOutputHandlers: {e}")
            raise
    
    def add_file_handler(self, file_path: Union[str, Path], 
                        name: Optional[str] = None,
                        max_file_size: int = 100 * 1024 * 1024,
                        backup_count: int = 5,
                        encoding: str = 'utf-8') -> str:
        """
        添加文件输出处理器
        
        Args:
            file_path: 文件路径
            name: 处理器名称（可选）
            max_file_size: 最大文件大小
            backup_count: 备份文件数量
            encoding: 文件编码
            
        Returns:
            处理器名称
        """
        if name is None:
            name = f"file_{Path(file_path).stem}"
        
        handler = FileOutputHandler(
            name=name,
            file_path=file_path,
            max_file_size=max_file_size,
            backup_count=backup_count,
            encoding=encoding
        )
        
        self._output_manager.add_handler(handler)
        logger.info(f"Added file handler: {name} -> {file_path}")
        
        return name
    
    def add_console_handler(self, name: str = "console",
                           use_colors: bool = True,
                           error_to_stderr: bool = True) -> str:
        """
        添加控制台输出处理器
        
        Args:
            name: 处理器名称
            use_colors: 是否使用颜色
            error_to_stderr: 错误是否输出到stderr
            
        Returns:
            处理器名称
        """
        handler = ConsoleOutputHandler(
            name=name,
            use_colors=use_colors,
            error_to_stderr=error_to_stderr
        )
        
        self._output_manager.add_handler(handler)
        logger.info(f"Added console handler: {name}")
        
        return name
    
    def add_remote_handler(self, endpoint_url: str,
                          name: Optional[str] = None,
                          api_key: Optional[str] = None,
                          batch_size: int = 100,
                          flush_interval: int = 30,
                          timeout: int = 10) -> str:
        """
        添加远程日志服务处理器
        
        Args:
            endpoint_url: 远程服务端点URL
            name: 处理器名称（可选）
            api_key: API密钥
            batch_size: 批处理大小
            flush_interval: 刷新间隔（秒）
            timeout: 超时时间（秒）
            
        Returns:
            处理器名称
        """
        if name is None:
            name = f"remote_{hash(endpoint_url) % 10000}"
        
        handler = RemoteLogHandler(
            name=name,
            endpoint_url=endpoint_url,
            api_key=api_key,
            batch_size=batch_size,
            flush_interval=flush_interval,
            timeout=timeout
        )
        
        self._output_manager.add_handler(handler)
        logger.info(f"Added remote handler: {name} -> {endpoint_url}")
        
        return name
    
    def remove_handler(self, name: str) -> bool:
        """
        移除输出处理器
        
        Args:
            name: 处理器名称
            
        Returns:
            是否成功移除
        """
        try:
            self._output_manager.remove_handler(name)
            logger.info(f"Removed handler: {name}")
            return True
        except Exception as e:
            logger.error(f"Failed to remove handler {name}: {e}")
            return False
    
    def enable_handler(self, name: str) -> bool:
        """
        启用处理器
        
        Args:
            name: 处理器名称
            
        Returns:
            是否成功启用
        """
        try:
            handler = self._output_manager.get_handler(name)
            if handler:
                handler.enable()
                logger.info(f"Enabled handler: {name}")
                return True
            else:
                logger.warning(f"Handler not found: {name}")
                return False
        except Exception as e:
            logger.error(f"Failed to enable handler {name}: {e}")
            return False
    
    def disable_handler(self, name: str) -> bool:
        """
        禁用处理器
        
        Args:
            name: 处理器名称
            
        Returns:
            是否成功禁用
        """
        try:
            handler = self._output_manager.get_handler(name)
            if handler:
                handler.disable()
                logger.info(f"Disabled handler: {name}")
                return True
            else:
                logger.warning(f"Handler not found: {name}")
                return False
        except Exception as e:
            logger.error(f"Failed to disable handler {name}: {e}")
            return False
    
    def list_handlers(self) -> List[str]:
        """
        列出所有处理器名称
        
        Returns:
            处理器名称列表
        """
        return self._output_manager.list_handlers()
    
    async def write_log(self, entry: LogEntry) -> None:
        """
        写入日志条目
        
        Args:
            entry: 日志条目
        """
        if not self._initialized:
            await self.initialize()
        
        await self._output_manager.write_log(entry)
    
    async def write_log_dict(self, timestamp: datetime, level: str, 
                            message: str, data: Dict[str, Any],
                            formatted_message: str) -> None:
        """
        写入日志（字典参数）
        
        Args:
            timestamp: 时间戳
            level: 日志级别
            message: 消息
            data: 数据
            formatted_message: 格式化消息
        """
        entry = LogEntry(
            timestamp=timestamp,
            level=level,
            message=message,
            data=data,
            formatted_message=formatted_message
        )
        
        await self.write_log(entry)
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取统计信息
        
        Returns:
            统计信息字典
        """
        stats = self._output_manager.get_statistics()
        stats.update({
            "initialized": self._initialized,
            "total_handlers": len(self.list_handlers())
        })
        return stats
    
    def get_handler_info(self, name: str) -> Optional[Dict[str, Any]]:
        """
        获取处理器信息
        
        Args:
            name: 处理器名称
            
        Returns:
            处理器信息字典
        """
        try:
            handler = self._output_manager.get_handler(name)
            if handler:
                return {
                    "name": handler.name,
                    "type": type(handler).__name__,
                    "enabled": handler.is_enabled()
                }
            return None
        except Exception as e:
            logger.error(f"Failed to get handler info for {name}: {e}")
            return None
    
    async def shutdown(self) -> None:
        """关闭输出处理器系统"""
        try:
            await self._output_manager.shutdown()
            self._initialized = False
            logger.info("LogOutputHandlers shutdown completed")
        except Exception as e:
            logger.error(f"Error during LogOutputHandlers shutdown: {e}")
    
    def create_log_entry(self, timestamp: datetime, level: str,
                        message: str, data: Optional[Dict[str, Any]] = None,
                        formatted_message: Optional[str] = None) -> LogEntry:
        """
        创建日志条目
        
        Args:
            timestamp: 时间戳
            level: 日志级别
            message: 消息
            data: 数据（可选）
            formatted_message: 格式化消息（可选）
            
        Returns:
            日志条目
        """
        if data is None:
            data = {}
        
        if formatted_message is None:
            formatted_message = f"[{timestamp.strftime('%Y-%m-%d %H:%M:%S')}] [{level}] {message}"
        
        return LogEntry(
            timestamp=timestamp,
            level=level,
            message=message,
            data=data,
            formatted_message=formatted_message
        )


# 全局输出管理器实例
_global_output_manager: Optional[LogOutputManager] = None


def get_global_output_manager() -> LogOutputManager:
    """获取全局输出管理器实例"""
    global _global_output_manager
    
    if _global_output_manager is None:
        _global_output_manager = LogOutputManager()
    
    return _global_output_manager


# 全局输出处理器实例
_global_output_handlers: Optional[LogOutputHandlers] = None


def get_global_output_handlers() -> LogOutputHandlers:
    """获取全局输出处理器实例"""
    global _global_output_handlers
    
    if _global_output_handlers is None:
        _global_output_handlers = LogOutputHandlers()
    
    return _global_output_handlers


# 模块级别的日志器
module_logger = logger.bind(name=__name__)