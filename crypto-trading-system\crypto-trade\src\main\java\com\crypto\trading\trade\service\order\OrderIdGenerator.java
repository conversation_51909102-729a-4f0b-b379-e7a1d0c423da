package com.crypto.trading.trade.service.order;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Random;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 订单ID生成器
 * 
 * <p>生成唯一的订单ID和客户端订单ID</p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
public class OrderIdGenerator {

    private static final Logger log = LoggerFactory.getLogger(OrderIdGenerator.class);
    
    /**
     * 序列号生成器
     */
    private final AtomicInteger sequence = new AtomicInteger(0);
    
    /**
     * 随机数生成器
     */
    private final Random random = new Random();
    
    /**
     * 日期时间格式化器
     */
    private final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
    
    /**
     * 最大序列号
     */
    private static final int MAX_SEQUENCE = 9999;
    
    /**
     * 生成客户端订单ID
     * <p>
     * 格式: CID + 年月日时分秒 + 4位随机数 + 4位序列号
     * 例如: CID202407200930001234
     * </p>
     *
     * @param prefix 前缀，默认为CID
     * @return 客户端订单ID
     */
    public String generateClientOrderId(String prefix) {
        String actualPrefix = prefix != null ? prefix : "CID";
        String timestamp = LocalDateTime.now().format(formatter);
        String randomStr = String.format("%04d", random.nextInt(10000));
        String sequenceStr = String.format("%04d", getNextSequence());
        
        String clientOrderId = actualPrefix + timestamp + randomStr + sequenceStr;
        log.debug("生成客户端订单ID: {}", clientOrderId);
        
        return clientOrderId;
    }
    
    /**
     * 生成客户端订单ID，使用默认前缀CID
     *
     * @return 客户端订单ID
     */
    public String generateClientOrderId() {
        return generateClientOrderId("CID");
    }
    
    /**
     * 生成策略订单ID
     * <p>
     * 格式: 策略名称 + 年月日时分秒 + 4位随机数 + 4位序列号
     * 例如: MACD202407200930001234
     * </p>
     *
     * @param strategy 策略名称
     * @return 策略订单ID
     */
    public String generateStrategyOrderId(String strategy) {
        String timestamp = LocalDateTime.now().format(formatter);
        String randomStr = String.format("%04d", random.nextInt(10000));
        String sequenceStr = String.format("%04d", getNextSequence());
        
        String strategyOrderId = strategy + timestamp + randomStr + sequenceStr;
        log.debug("生成策略订单ID: {}", strategyOrderId);
        
        return strategyOrderId;
    }
    
    /**
     * 生成信号ID
     * <p>
     * 格式: SIG + 年月日时分秒 + 4位随机数 + 4位序列号
     * 例如: SIG202407200930001234
     * </p>
     *
     * @return 信号ID
     */
    public String generateSignalId() {
        return generateClientOrderId("SIG");
    }
    
    /**
     * 获取下一个序列号
     *
     * @return 序列号
     */
    private int getNextSequence() {
        int current = sequence.getAndIncrement();
        if (current > MAX_SEQUENCE) {
            sequence.set(0);
            return 0;
        }
        return current;
    }
}