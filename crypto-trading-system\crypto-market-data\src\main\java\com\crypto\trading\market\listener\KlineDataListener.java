package com.crypto.trading.market.listener;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.crypto.trading.market.processor.KlineDataProcessor;
import com.crypto.trading.sdk.response.model.KlineData;
import com.crypto.trading.sdk.response.model.WebSocketMessage;
import com.crypto.trading.sdk.websocket.BinanceWebSocketClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Consumer;

/**
 * K线数据监听器
 * 负责订阅和处理币安WebSocket K线数据
 */
@Component
public class KlineDataListener {

    private static final Logger log = LoggerFactory.getLogger(KlineDataListener.class);

    @Autowired
    private BinanceWebSocketClient webSocketClient;

    @Autowired
    private KlineDataProcessor klineDataProcessor;

    @Autowired
    @org.springframework.beans.factory.annotation.Qualifier("symbolsList")
    private List<String> symbols;

    @Autowired
    @org.springframework.beans.factory.annotation.Qualifier("klineIntervalsList")
    private List<String> klineIntervals;

    /**
     * 连接ID映射，用于跟踪WebSocket连接
     * 格式: symbol_interval -> connectionId
     */
    private final Map<String, Integer> connectionIds = new ConcurrentHashMap<>();

    /**
     * 启动监听器
     */
    public void start() {
        startKlineDataSubscription();
    }
    
    /**
     * 启动K线数据订阅
     */
    public void startKlineDataSubscription() {
        log.info("开始订阅K线数据...");
        
        // 为每个交易对和每个时间间隔创建WebSocket连接
        for (String symbol : symbols) {
            for (String interval : klineIntervals) {
                // 创建处理器
                Consumer<String> messageHandler = createKlineMessageHandler(symbol, interval);
                
                // 订阅K线数据
                int connectionId = webSocketClient.subscribeKline(symbol.toLowerCase(), interval, messageHandler);
                
                // 保存连接ID
                String key = getConnectionKey(symbol, interval);
                connectionIds.put(key, connectionId);
                
                log.info("已订阅K线数据: symbol={}, interval={}, connectionId={}", symbol, interval, connectionId);
            }
        }
    }

    /**
     * 停止K线数据订阅
     */
    public void stopKlineDataSubscription() {
        log.info("停止订阅K线数据...");
        
        // 关闭所有WebSocket连接
        for (Map.Entry<String, Integer> entry : connectionIds.entrySet()) {
            webSocketClient.closeConnection(entry.getValue());
            log.info("已关闭K线数据连接: key={}, connectionId={}", entry.getKey(), entry.getValue());
        }
        
        // 清空连接ID映射
        connectionIds.clear();
    }

    /**
     * 创建K线消息处理器
     *
     * @param symbol   交易对
     * @param interval K线间隔
     * @return 消息处理器
     */
    private Consumer<String> createKlineMessageHandler(String symbol, String interval) {
        return message -> {
            try {
                // 解析消息
                JSONObject jsonObject = JSON.parseObject(message);
                
                // 处理嵌套的消息格式，获取data部分
                JSONObject dataObject;
                if (jsonObject.containsKey("data")) {
                    // 处理格式: {"stream":"symbol@kline_interval","data":{实际数据...}}
                    dataObject = jsonObject.getJSONObject("data");
                } else {
                    // 处理可能的直接格式: {实际数据...}
                    dataObject = jsonObject;
                }
                
                // 确保消息类型是k线数据
                if (!"kline".equals(dataObject.getString("e"))) {
                    return;
                }
                
                // 获取K线数据
                JSONObject k = dataObject.getJSONObject("k");
                
                // 创建WebSocketMessage包装器
                WebSocketMessage<KlineData> webSocketMessage = new WebSocketMessage<>();
                webSocketMessage.setSymbol(symbol.toUpperCase());
                webSocketMessage.setEventType("kline");
                webSocketMessage.setEventTime(dataObject.getLongValue("E"));
                
                // 构建KlineData对象
                KlineData klineData = new KlineData();
                klineData.setOpenTime(k.getLongValue("t"));      // 开盘时间
                klineData.setCloseTime(k.getLongValue("T"));     // 收盘时间
                klineData.setOpen(new BigDecimal(k.getString("o")));    // 开盘价
                klineData.setHigh(new BigDecimal(k.getString("h")));    // 最高价
                klineData.setLow(new BigDecimal(k.getString("l")));     // 最低价
                klineData.setClose(new BigDecimal(k.getString("c")));   // 收盘价
                klineData.setVolume(new BigDecimal(k.getString("v")));  // 交易量
                klineData.setQuoteAssetVolume(new BigDecimal(k.getString("q"))); // 交易额
                klineData.setNumberOfTrades(k.getLongValue("n")); // 成交笔数
                klineData.setTakerBuyBaseAssetVolume(new BigDecimal(k.getString("V"))); // 主动买入成交量
                klineData.setTakerBuyQuoteAssetVolume(new BigDecimal(k.getString("Q"))); // 主动买入成交额
                
                // 设置数据
                webSocketMessage.setData(klineData);
                
                // 处理K线数据
                klineDataProcessor.processKlineData(webSocketMessage, interval);
                
                if (log.isDebugEnabled()) {
                    log.debug("成功处理K线数据: symbol={}, interval={}, openTime={}", 
                            symbol, interval, klineData.getOpenTime());
                }
                
            } catch (Exception e) {
                log.error("处理K线数据异常: symbol={}, interval={}, error={}", 
                        symbol, interval, e.getMessage(), e);
            }
        };
    }

    /**
     * 获取连接键
     *
     * @param symbol   交易对
     * @param interval K线间隔
     * @return 连接键
     */
    private String getConnectionKey(String symbol, String interval) {
        return symbol + "_" + interval;
    }
} 