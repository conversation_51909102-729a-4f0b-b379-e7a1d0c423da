<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>RequestBuilder.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">io.github.binance:binance-futures-connector-java</a> &gt; <a href="index.source.html" class="el_package">com.binance.connector.futures.client.utils</a> &gt; <span class="el_source">RequestBuilder.java</span></div><h1>RequestBuilder.java</h1><pre class="source lang-java linenums">package com.binance.connector.futures.client.utils;

import com.binance.connector.futures.client.enums.HttpMethod;
import com.binance.connector.futures.client.exceptions.BinanceConnectorException;
import okhttp3.MediaType;
import okhttp3.Request;
import okhttp3.RequestBody;

public final class RequestBuilder {
<span class="fc" id="L10">    private static final MediaType JSON_TYPE = MediaType.parse(&quot;application/json; charset=utf-8&quot;);</span>
    private static final String USER_AGENT = &quot;binance-futures-connector-java/3.0.5&quot;;
    private static final String CONTENT_TYPE = &quot;application/x-www-form-urlencoded&quot;;

    private RequestBuilder() {
    }
    public static Request buildPublicRequest(String fullUrl, HttpMethod httpMethod) {
        try {
<span class="fc" id="L18">            final Request.Builder requestBuilder = new Request.Builder().addHeader(&quot;User-Agent&quot;, USER_AGENT).addHeader(&quot;Content-Type&quot;, CONTENT_TYPE).url(fullUrl);</span>
<span class="fc bfc" id="L19" title="All 5 branches covered.">            switch (httpMethod) {</span>
                case POST:
<span class="fc" id="L21">                    return requestBuilder.post(RequestBody.create(&quot;&quot;, JSON_TYPE)).build();</span>
                case GET:
<span class="fc" id="L23">                    return requestBuilder.get().build();</span>
                case PUT:
<span class="fc" id="L25">                    return requestBuilder.put(RequestBody.create(&quot;&quot;, JSON_TYPE)).build();</span>
                case DELETE:
<span class="fc" id="L27">                    return requestBuilder.delete().build();</span>
                default:
<span class="fc" id="L29">                    throw new BinanceConnectorException(&quot;Invalid HTTP method: &quot; + httpMethod);</span>
            }
<span class="fc" id="L31">        } catch (IllegalArgumentException e) {</span>
<span class="fc" id="L32">            throw new BinanceConnectorException(&quot;Invalid URL: &quot; + e.getMessage());</span>
        }
    }

    public static Request buildApiKeyRequest(String fullUrl, HttpMethod httpMethod, String apiKey) {
        try {
<span class="fc" id="L38">            final Request.Builder requestBuilder = new Request.Builder().addHeader(&quot;User-Agent&quot;, USER_AGENT).addHeader(&quot;Content-Type&quot;, CONTENT_TYPE).addHeader(&quot;X-MBX-APIKEY&quot;, apiKey).url(fullUrl);</span>
<span class="fc bfc" id="L39" title="All 5 branches covered.">            switch (httpMethod) {</span>
                case POST:
<span class="fc" id="L41">                    return requestBuilder.post(RequestBody.create(&quot;&quot;, JSON_TYPE)).build();</span>
                case GET:
<span class="fc" id="L43">                    return requestBuilder.get().build();</span>
                case PUT:
<span class="fc" id="L45">                    return requestBuilder.put(RequestBody.create(&quot;&quot;, JSON_TYPE)).build();</span>
                case DELETE:
<span class="fc" id="L47">                    return requestBuilder.delete().build();</span>
                default:
<span class="fc" id="L49">                    throw new BinanceConnectorException(&quot;Invalid HTTP method: &quot; + httpMethod);</span>
            }
<span class="fc" id="L51">        } catch (IllegalArgumentException e) {</span>
<span class="fc" id="L52">            throw new BinanceConnectorException(&quot;Invalid URL: &quot; + e.getMessage());</span>
        }
    }

    public static Request buildWebsocketRequest(String fullUrl) {
<span class="fc" id="L57">        return new Request.Builder().url(fullUrl).build();</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.7.202105040129</span></div></body></html>