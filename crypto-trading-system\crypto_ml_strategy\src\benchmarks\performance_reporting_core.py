"""
Crypto ML Strategy - 性能报告核心组件

该模块实现了Task 12性能报告和监控系统的核心组件，包括报告生成、
实时监控和性能分析功能。

主要功能：
- PerformanceReportGenerator: 全面性能报告生成
- RealTimePerformanceMonitor: 实时性能监控仪表板
- PerformanceReport: 性能报告数据结构

作者: Crypto ML Strategy Team
日期: 2024-01-20
版本: 1.0.0
"""

import json
import threading
import time
from collections import deque
from dataclasses import dataclass, field
from datetime import datetime
from typing import Any, Dict, List, Optional
import numpy as np
from pathlib import Path
import scipy.stats as stats


@dataclass
class PerformanceReport:
    """性能报告数据结构"""
    report_id: str
    validation_results: Dict[str, Any]
    integration_results: Dict[str, Any]
    summary_metrics: Dict[str, float]
    statistical_analysis: Dict[str, Any]
    recommendations: List[str]
    performance_score: float
    generated_at: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "report_id": self.report_id,
            "validation_results": {k: v.to_dict() if hasattr(v, 'to_dict') else v 
                                 for k, v in self.validation_results.items()},
            "integration_results": {k: v.to_dict() if hasattr(v, 'to_dict') else v 
                                  for k, v in self.integration_results.items()},
            "summary_metrics": self.summary_metrics,
            "statistical_analysis": self.statistical_analysis,
            "recommendations": self.recommendations,
            "performance_score": self.performance_score,
            "generated_at": self.generated_at.isoformat()
        }


class PerformanceReportGenerator:
    """性能报告生成器"""
    
    def __init__(self, output_dir: str = "logs/performance_reports"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
    
    async def generate_comprehensive_report(self) -> PerformanceReport:
        """生成全面性能报告"""
        try:
            report_id = f"perf_report_{int(time.time())}"
            
            # 运行验证测试
            from performance_validation_extended import get_global_system_validator
            validator = get_global_system_validator()
            validation_results = await validator.run_comprehensive_validation()
            
            # 运行集成测试
            from integration_tests_extended import get_global_integration_tests
            integration_tests = get_global_integration_tests()
            integration_results = {}
            
            integration_results["multi_timeframe"] = integration_tests["multi_timeframe"].test_timeframe_processing_performance()
            integration_results["technical_indicator"] = integration_tests["technical_indicator"].test_indicator_calculation_performance()
            integration_results["deepseek_model"] = integration_tests["deepseek_model"].test_deepseek_inference_performance()
            integration_results["concurrent_load"] = integration_tests["concurrent_load"].test_concurrent_request_handling()
            
            # 计算汇总指标
            summary_metrics = self._calculate_summary_metrics(validation_results, integration_results)
            
            # 统计分析
            statistical_analysis = self._perform_statistical_analysis(validation_results, integration_results)
            
            # 生成建议
            recommendations = self._generate_recommendations(validation_results, integration_results)
            
            # 计算性能评分
            performance_score = self._calculate_performance_score(validation_results, integration_results)
            
            report = PerformanceReport(
                report_id=report_id,
                validation_results=validation_results,
                integration_results=integration_results,
                summary_metrics=summary_metrics,
                statistical_analysis=statistical_analysis,
                recommendations=recommendations,
                performance_score=performance_score
            )
            
            # 保存报告
            self._save_report(report)
            
            return report
            
        except Exception as e:
            raise
    
    def _calculate_summary_metrics(self, validation_results: Dict[str, Any],
                                 integration_results: Dict[str, Any]) -> Dict[str, float]:
        """计算汇总指标"""
        # 验证通过率
        validation_pass_rate = sum(1 for r in validation_results.values() if r.passed) / len(validation_results)
        
        # 集成测试通过率
        integration_pass_rate = sum(1 for r in integration_results.values() if r.passed) / len(integration_results)
        
        # 平均延迟
        latency_values = []
        if "latency" in validation_results:
            latency_values.append(validation_results["latency"].actual_value)
        latency_values.extend([r.average_latency_ms for r in integration_results.values()])
        avg_latency = np.mean(latency_values) if latency_values else 0
        
        # 平均成功率
        success_rates = [r.success_rate for r in integration_results.values()]
        avg_success_rate = np.mean(success_rates) if success_rates else 0
        
        # 统计显著性比例
        significant_tests = sum(1 for r in validation_results.values() if r.p_value < 0.05)
        significant_tests += sum(1 for r in integration_results.values() if r.statistical_significance)
        total_tests = len(validation_results) + len(integration_results)
        significance_rate = significant_tests / total_tests if total_tests > 0 else 0
        
        return {
            "validation_pass_rate": validation_pass_rate,
            "integration_pass_rate": integration_pass_rate,
            "overall_pass_rate": (validation_pass_rate + integration_pass_rate) / 2,
            "average_latency_ms": avg_latency,
            "average_success_rate": avg_success_rate,
            "statistical_significance_rate": significance_rate,
            "total_tests_conducted": total_tests
        }
    
    def _perform_statistical_analysis(self, validation_results: Dict[str, Any],
                                    integration_results: Dict[str, Any]) -> Dict[str, Any]:
        """执行统计分析"""
        analysis = {
            "bonferroni_correction": {},
            "effect_sizes": {},
            "confidence_intervals": {},
            "power_analysis": {}
        }
        
        # Bonferroni校正
        total_tests = len(validation_results) + len(integration_results)
        corrected_alpha = 0.05 / total_tests if total_tests > 0 else 0.05
        analysis["bonferroni_correction"]["corrected_alpha"] = corrected_alpha
        analysis["bonferroni_correction"]["significant_after_correction"] = 0
        
        # 效应量分析
        effect_sizes = []
        for result in validation_results.values():
            if hasattr(result, 'effect_size'):
                effect_sizes.append(result.effect_size)
                if result.p_value < corrected_alpha:
                    analysis["bonferroni_correction"]["significant_after_correction"] += 1
        
        analysis["effect_sizes"]["mean_effect_size"] = np.mean(effect_sizes) if effect_sizes else 0
        analysis["effect_sizes"]["large_effects_count"] = sum(1 for es in effect_sizes if abs(es) > 0.8)
        
        # 置信区间汇总
        ci_widths = []
        for result in validation_results.values():
            if result.confidence_interval:
                width = result.confidence_interval[1] - result.confidence_interval[0]
                ci_widths.append(width)
        
        analysis["confidence_intervals"]["mean_ci_width"] = np.mean(ci_widths) if ci_widths else 0
        analysis["confidence_intervals"]["narrow_cis_count"] = sum(1 for w in ci_widths if w < 10)
        
        return analysis
    
    def _generate_recommendations(self, validation_results: Dict[str, Any],
                                integration_results: Dict[str, Any]) -> List[str]:
        """生成优化建议"""
        recommendations = []
        
        # 检查验证结果
        for name, result in validation_results.items():
            if not result.passed:
                if name == "latency":
                    recommendations.append(f"延迟优化：当前{result.actual_value:.1f}ms超过目标{result.target_value:.1f}ms，建议进一步优化推理管道")
                elif name == "memory":
                    recommendations.append(f"内存优化：当前{result.actual_value:.1f}MB超过目标{result.target_value:.1f}MB，建议优化数据结构")
                elif name == "accuracy":
                    recommendations.append(f"精度优化：当前{result.actual_value:.3f}低于目标{result.target_value:.3f}，建议调整模型参数")
                elif name == "throughput":
                    recommendations.append(f"吞吐量优化：当前{result.actual_value:.1f}低于目标{result.target_value:.1f}，建议优化批处理")
        
        # 检查集成测试结果
        for name, result in integration_results.items():
            if not result.passed:
                if result.success_rate < 0.95:
                    recommendations.append(f"{name}稳定性改进：成功率{result.success_rate:.1%}偏低，建议增强错误处理")
                if result.average_latency_ms > 100:
                    recommendations.append(f"{name}性能优化：平均延迟{result.average_latency_ms:.1f}ms偏高，建议优化算法")
        
        # 统计显著性建议
        significant_count = sum(1 for r in validation_results.values() if r.p_value < 0.05)
        if significant_count < len(validation_results) * 0.8:
            recommendations.append("建议增加样本量以提高统计检验力度")
        
        if not recommendations:
            recommendations.append("系统性能表现优秀，所有目标均已达成，建议保持当前优化策略")
        
        return recommendations
    
    def _calculate_performance_score(self, validation_results: Dict[str, Any],
                                   integration_results: Dict[str, Any]) -> float:
        """计算性能评分 (0-100分)"""
        base_score = 0
        bonus_score = 0
        
        # 基础分数：验证测试通过得分
        for result in validation_results.values():
            if result.passed:
                base_score += 20  # 每项验证通过得20分
        
        # 集成测试通过得分
        for result in integration_results.values():
            if result.passed:
                base_score += 5  # 每项集成测试通过得5分
        
        # 奖励分数：超额完成
        if "latency" in validation_results and validation_results["latency"].passed:
            improvement = (validation_results["latency"].target_value - validation_results["latency"].actual_value) / validation_results["latency"].target_value
            bonus_score += min(15, improvement * 30)
        
        if "throughput" in validation_results and validation_results["throughput"].passed:
            improvement = (validation_results["throughput"].actual_value - validation_results["throughput"].target_value) / validation_results["throughput"].target_value
            bonus_score += min(10, improvement * 20)
        
        # 统计显著性奖励
        significant_count = sum(1 for r in validation_results.values() if r.p_value < 0.05)
        if significant_count == len(validation_results):
            bonus_score += 5
        
        return min(100, base_score + bonus_score)
    
    def _save_report(self, report: PerformanceReport) -> None:
        """保存报告"""
        try:
            # JSON格式报告
            json_file = self.output_dir / f"{report.report_id}.json"
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(report.to_dict(), f, indent=2, ensure_ascii=False)
            
            # HTML格式报告
            html_file = self.output_dir / f"{report.report_id}.html"
            self._generate_html_report(report, html_file)
            
        except Exception as e:
            pass
    
    def _generate_html_report(self, report: PerformanceReport, html_file: Path) -> None:
        """生成HTML格式报告"""
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>性能验证报告 - {report.report_id}</title>
            <meta charset="utf-8">
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .header {{ background-color: #f0f0f0; padding: 20px; border-radius: 5px; }}
                .score {{ font-size: 24px; font-weight: bold; color: #2e8b57; }}
                .section {{ margin: 20px 0; }}
                .passed {{ color: green; }}
                .failed {{ color: red; }}
                table {{ border-collapse: collapse; width: 100%; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                th {{ background-color: #f2f2f2; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>Crypto ML Strategy 性能验证报告</h1>
                <p>报告ID: {report.report_id}</p>
                <p>生成时间: {report.generated_at.strftime('%Y-%m-%d %H:%M:%S')}</p>
                <p class="score">性能评分: {report.performance_score:.1f}/100</p>
            </div>
            
            <div class="section">
                <h2>验证结果汇总</h2>
                <p>总体通过率: {report.summary_metrics['overall_pass_rate']:.1%}</p>
                <p>平均延迟: {report.summary_metrics['average_latency_ms']:.1f}ms</p>
                <p>统计显著性比例: {report.summary_metrics['statistical_significance_rate']:.1%}</p>
            </div>
            
            <div class="section">
                <h2>优化建议</h2>
                <ul>
                    {''.join(f'<li>{rec}</li>' for rec in report.recommendations)}
                </ul>
            </div>
        </body>
        </html>
        """
        
        try:
            with open(html_file, 'w', encoding='utf-8') as f:
                f.write(html_content)
        except Exception:
            pass