package com.crypto.trading.trade.util;

import com.crypto.trading.common.dto.trade.OrderDTO;
import com.crypto.trading.common.dto.trade.OrderResponseDTO;
import com.crypto.trading.trade.model.entity.order.OrderEntity;

/**
 * 订单转换工具类
 * <p>
 * 用于在不同的订单对象之间进行转换
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class OrderConverter {

    private OrderConverter() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * 将OrderEntity转换为OrderResponseDTO
     *
     * @param entity 订单实体
     * @return 订单响应DTO
     */
    public static OrderResponseDTO toOrderResponseDTO(OrderEntity entity) {
        if (entity == null) {
            return null;
        }

        OrderResponseDTO dto = new OrderResponseDTO();
        dto.setOrderId(entity.getOrderId());
        dto.setClientOrderId(entity.getClientOrderId());
        dto.setSymbol(entity.getSymbol());
        dto.setSide(entity.getSide());
        dto.setPositionSide(entity.getPositionSide());
        dto.setOrderType(entity.getOrderType());

        // 设置Double数值字段 - 直接设置，无需类型转换
        dto.setQuantity(entity.getQuantity());
        dto.setPrice(entity.getPrice());
        dto.setExecutedPrice(entity.getExecutedPrice());
        dto.setExecutedQuantity(entity.getExecutedQuantity());

        dto.setStatus(entity.getStatus());
        dto.setErrorCode(entity.getErrorCode());
        dto.setErrorMessage(entity.getErrorMessage());
        dto.setStrategy(entity.getStrategy());
        dto.setSignalId(entity.getSignalId());

        // 设置时间戳字段 - 直接使用Long类型
        dto.setCreatedTime(entity.getCreatedTime());
        dto.setUpdatedTime(entity.getUpdatedTime());
        dto.setExecutedTime(entity.getExecutedTime());

        return dto;
    }

    /**
     * 将OrderEntity转换为OrderDTO
     *
     * @param entity 订单实体
     * @return 订单DTO
     */
    public static OrderDTO toOrderDTO(OrderEntity entity) {
        if (entity == null) {
            return null;
        }

        OrderDTO dto = new OrderDTO();
        dto.setOrderId(entity.getOrderId());
        dto.setClientOrderId(entity.getClientOrderId());
        dto.setSymbol(entity.getSymbol());
        dto.setSide(entity.getSide());
        dto.setPositionSide(entity.getPositionSide());
        dto.setType(entity.getOrderType()); // 注意OrderDTO中使用的是type而非orderType

        // 设置数值字段
        dto.setQuantity(entity.getQuantity());
        dto.setPrice(entity.getPrice());
        dto.setExecutedQuantity(entity.getExecutedQuantity());
        dto.setExecutedPrice(entity.getExecutedPrice());

        dto.setStatus(entity.getStatus());
        dto.setErrorCode(entity.getErrorCode());
        dto.setErrorMessage(entity.getErrorMessage());
        dto.setStrategyId(entity.getStrategy()); // 注意OrderDTO中使用的是strategyId而非strategy

        // 设置时间字段
        dto.setCreatedTime(entity.getCreatedTime());
        dto.setUpdatedTime(entity.getUpdatedTime());
        dto.setExecutedTime(entity.getExecutedTime());
        
        dto.setRemark(entity.getRemark());

        return dto;
    }

    /**
     * 安全地转换Double值，避免空指针异常
     *
     * @param value Double值
     * @return 非空的Double值，如果输入为null则返回null
     */
    public static Double toDouble(Double value) {
        return value;
    }
}