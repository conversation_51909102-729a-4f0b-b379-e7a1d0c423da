package com.crypto.trading.bootstrap.lifecycle;

import com.crypto.trading.bootstrap.order.StartupOrderManager;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * 关闭钩子测试类
 */
class ShutdownHookTest {
    
    private StartupOrderManager mockStartupOrderManager;
    private ShutdownHook shutdownHook;
    
    @BeforeEach
    void setUp() {
        mockStartupOrderManager = Mockito.mock(StartupOrderManager.class);
        shutdownHook = new ShutdownHook(mockStartupOrderManager);
    }
    
    /**
     * 测试初始化关闭钩子
     */
    @Test
    void testAfterPropertiesSet() {
        // 执行初始化
        shutdownHook.afterPropertiesSet();
        
        // 验证状态：初始时不处于关闭状态
        assertFalse(shutdownHook.isShuttingDown());
    }
    
    /**
     * 测试执行关闭操作
     */
    @Test
    void testPerformShutdown() {
        // 执行关闭
        shutdownHook.performShutdown();
        
        // 验证关闭方法被调用
        verify(mockStartupOrderManager, times(1)).shutdownAll();
        verify(mockStartupOrderManager, times(1)).clearThreadLocal();
        
        // 验证状态：处于关闭状态
        assertTrue(shutdownHook.isShuttingDown());
        
        // 再次执行关闭，验证不会重复执行
        shutdownHook.performShutdown();
        
        // 验证关闭方法只被调用一次
        verify(mockStartupOrderManager, times(1)).shutdownAll();
    }
    
    /**
     * 测试销毁操作
     */
    @Test
    void testDestroy() {
        // 执行销毁
        shutdownHook.destroy();
        
        // 验证关闭方法被调用
        verify(mockStartupOrderManager, times(1)).shutdownAll();
        
        // 验证状态：处于关闭状态
        assertTrue(shutdownHook.isShuttingDown());
    }
    
    /**
     * 测试关闭过程中的异常处理
     */
    @Test
    void testPerformShutdownWithException() {
        // 设置关闭时抛出异常
        doThrow(new RuntimeException("测试异常")).when(mockStartupOrderManager).shutdownAll();
        
        // 执行关闭，验证不会抛出异常
        assertDoesNotThrow(() -> shutdownHook.performShutdown());
        
        // 验证关闭方法被调用
        verify(mockStartupOrderManager, times(1)).shutdownAll();
        
        // 验证状态：处于关闭状态
        assertTrue(shutdownHook.isShuttingDown());
    }
}