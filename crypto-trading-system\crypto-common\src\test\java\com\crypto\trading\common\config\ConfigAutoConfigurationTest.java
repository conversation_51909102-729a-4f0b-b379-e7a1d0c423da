package com.crypto.trading.common.config;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Import;
import com.crypto.trading.common.TestConfig;

/**
 * ConfigAutoConfiguration测试类
 * 注：此测试类已被修改，移除了对已迁移至bootstrap模块的配置类的引用
 */
@SpringBootTest
@Import(TestConfig.class)
class ConfigAutoConfigurationTest {
    
    @Autowired
    private ApplicationContext context;
    
    @Test
    void testConfigAutoConfiguration() {
        // 测试配置自动装配
        assert context.containsBean("databaseConfig");
        assert context.containsBean("kafkaConfig");
        assert context.containsBean("loggingConfig");
    }
}