package com.crypto.trading.sdk.converter;

import java.util.List;
import java.util.Map;

/**
 * JSON转换器接口
 * 定义JSON和Java对象互相转换的方法
 */
public interface JsonConverter {

    /**
     * 将JSON字符串转换为Java对象
     *
     * @param json  JSON字符串
     * @param clazz 目标类型
     * @param <T>   返回类型
     * @return 转换后的对象
     */
    <T> T fromJson(String json, Class<T> clazz);

    /**
     * 将JSON字符串转换为Java对象列表
     *
     * @param json  JSON字符串
     * @param clazz 列表元素类型
     * @param <T>   元素类型
     * @return 转换后的对象列表
     */
    <T> List<T> fromJsonArray(String json, Class<T> clazz);

    /**
     * 将Java对象转换为JSON字符串
     *
     * @param object 对象
     * @return JSON字符串
     */
    String toJson(Object object);

    /**
     * 将JSON字符串转换为Map
     *
     * @param json JSON字符串
     * @return Map对象
     */
    Map<String, Object> jsonToMap(String json);
} 