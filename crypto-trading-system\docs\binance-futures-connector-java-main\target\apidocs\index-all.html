<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (21) on Fri Apr 04 16:06:08 CST 2025 -->
<title>索引 (io.github.binance:binance-futures-connector-java 3.0.5 API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-04-04">
<meta name="description" content="index">
<meta name="generator" content="javadoc/IndexWriter">
<link rel="stylesheet" type="text/css" href="stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="script.js"></script>
<script type="text/javascript" src="script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="script-dir/jquery-ui.min.js"></script>
</head>
<body class="index-page">
<script type="text/javascript">var pathtoroot = "./";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="切换导航链接"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="index.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="overview-tree.html">树</a></li>
<li class="nav-bar-cell1-rev">索引</li>
<li><a href="help-doc.html#index">帮助</a></li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list"></div>
<div class="nav-list-search"><a href="search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="搜索">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1>索引</h1>
</div>
<a href="#I:A">A</a>&nbsp;<a href="#I:B">B</a>&nbsp;<a href="#I:C">C</a>&nbsp;<a href="#I:D">D</a>&nbsp;<a href="#I:E">E</a>&nbsp;<a href="#I:F">F</a>&nbsp;<a href="#I:G">G</a>&nbsp;<a href="#I:H">H</a>&nbsp;<a href="#I:I">I</a>&nbsp;<a href="#I:K">K</a>&nbsp;<a href="#I:L">L</a>&nbsp;<a href="#I:M">M</a>&nbsp;<a href="#I:N">N</a>&nbsp;<a href="#I:O">O</a>&nbsp;<a href="#I:P">P</a>&nbsp;<a href="#I:Q">Q</a>&nbsp;<a href="#I:S">S</a>&nbsp;<a href="#I:T">T</a>&nbsp;<a href="#I:U">U</a>&nbsp;<a href="#I:W">W</a>&nbsp;<br><a href="allpackages-index.html">所有程序包</a><span class="vertical-separator">|</span><a href="allclasses-index.html">所有类和接口</a>
<h2 class="title" id="I:A">A</h2>
<dl class="index">
<dt><a href="com/binance/connector/futures/client/FuturesClient.html#account()" class="member-name-link">account()</a> - 接口中的方法 com.binance.connector.futures.client.<a href="com/binance/connector/futures/client/FuturesClient.html" title="com.binance.connector.futures.client中的接口">FuturesClient</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/CMFuturesClientImpl.html#account()" class="member-name-link">account()</a> - 类中的方法 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/CMFuturesClientImpl.html" title="com.binance.connector.futures.client.impl中的类">CMFuturesClientImpl</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/UMFuturesClientImpl.html#account()" class="member-name-link">account()</a> - 类中的方法 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/UMFuturesClientImpl.html" title="com.binance.connector.futures.client.impl中的类">UMFuturesClientImpl</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/futures/Account.html" class="type-name-link" title="com.binance.connector.futures.client.impl.futures中的类">Account</a> - <a href="com/binance/connector/futures/client/impl/futures/package-summary.html">com.binance.connector.futures.client.impl.futures</a>中的类</dt>
<dd>
<div class="block">Trade Endpoints</div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/futures/Account.html#%3Cinit%3E(java.lang.String,java.lang.String,java.lang.String,boolean,com.binance.connector.futures.client.utils.ProxyAuth)" class="member-name-link">Account(String, String, String, boolean, ProxyAuth)</a> - 类的构造器 com.binance.connector.futures.client.impl.futures.<a href="com/binance/connector/futures/client/impl/futures/Account.html" title="com.binance.connector.futures.client.impl.futures中的类">Account</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/cm_futures/CMAccount.html#accountInformation(java.util.LinkedHashMap)" class="member-name-link">accountInformation(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.cm_futures.<a href="com/binance/connector/futures/client/impl/cm_futures/CMAccount.html" title="com.binance.connector.futures.client.impl.cm_futures中的类">CMAccount</a></dt>
<dd>
<div class="block">Get current account information.</div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/um_futures/UMAccount.html#accountInformation(java.util.LinkedHashMap)" class="member-name-link">accountInformation(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.um_futures.<a href="com/binance/connector/futures/client/impl/um_futures/UMAccount.html" title="com.binance.connector.futures.client.impl.um_futures中的类">UMAccount</a></dt>
<dd>
<div class="block">Get current account information.</div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/cm_futures/CMAccount.html#accountTradeList(java.util.LinkedHashMap)" class="member-name-link">accountTradeList(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.cm_futures.<a href="com/binance/connector/futures/client/impl/cm_futures/CMAccount.html" title="com.binance.connector.futures.client.impl.cm_futures中的类">CMAccount</a></dt>
<dd>
<div class="block">Get trades for a specific account and symbol.</div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/futures/Account.html#accountTradeList(java.util.LinkedHashMap)" class="member-name-link">accountTradeList(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.futures.<a href="com/binance/connector/futures/client/impl/futures/Account.html" title="com.binance.connector.futures.client.impl.futures中的类">Account</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/um_futures/UMAccount.html#accountTradeList(java.util.LinkedHashMap)" class="member-name-link">accountTradeList(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.um_futures.<a href="com/binance/connector/futures/client/impl/um_futures/UMAccount.html" title="com.binance.connector.futures.client.impl.um_futures中的类">UMAccount</a></dt>
<dd>
<div class="block">Get trades for a specific account and symbol.</div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/futures/Market.html#aggTrades(java.util.LinkedHashMap)" class="member-name-link">aggTrades(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.futures.<a href="com/binance/connector/futures/client/impl/futures/Market.html" title="com.binance.connector.futures.client.impl.futures中的类">Market</a></dt>
<dd>
<div class="block">Get compressed, aggregate trades.</div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html#aggTradeStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">aggTradeStream(String, WebSocketCallback)</a> - 类中的方法 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html" title="com.binance.connector.futures.client.impl中的类">WebsocketClientImpl</a></dt>
<dd>
<div class="block">The Aggregate Trade Streams push market trade information that is aggregated for fills with same price and taking side every 100 milliseconds.</div>
</dd>
<dt><a href="com/binance/connector/futures/client/WebsocketClient.html#aggTradeStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">aggTradeStream(String, WebSocketCallback)</a> - 接口中的方法 com.binance.connector.futures.client.<a href="com/binance/connector/futures/client/WebsocketClient.html" title="com.binance.connector.futures.client中的接口">WebsocketClient</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html#aggTradeStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">aggTradeStream(String, WebSocketCallback, WebSocketCallback, WebSocketCallback, WebSocketCallback)</a> - 类中的方法 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html" title="com.binance.connector.futures.client.impl中的类">WebsocketClientImpl</a></dt>
<dd>
<div class="block">Same as <a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html#aggTradeStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)"><code>WebsocketClientImpl.aggTradeStream(String, WebSocketCallback)</code></a> plus accepts callbacks for all major websocket connection events.</div>
</dd>
<dt><a href="com/binance/connector/futures/client/WebsocketClient.html#aggTradeStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">aggTradeStream(String, WebSocketCallback, WebSocketCallback, WebSocketCallback, WebSocketCallback)</a> - 接口中的方法 com.binance.connector.futures.client.<a href="com/binance/connector/futures/client/WebsocketClient.html" title="com.binance.connector.futures.client中的接口">WebsocketClient</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html#allBookTickerStream(com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">allBookTickerStream(WebSocketCallback)</a> - 类中的方法 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html" title="com.binance.connector.futures.client.impl中的类">WebsocketClientImpl</a></dt>
<dd>
<div class="block">Pushes any update to the best bid or ask's price or quantity in real-time for all symbols.</div>
</dd>
<dt><a href="com/binance/connector/futures/client/WebsocketClient.html#allBookTickerStream(com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">allBookTickerStream(WebSocketCallback)</a> - 接口中的方法 com.binance.connector.futures.client.<a href="com/binance/connector/futures/client/WebsocketClient.html" title="com.binance.connector.futures.client中的接口">WebsocketClient</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html#allBookTickerStream(com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">allBookTickerStream(WebSocketCallback, WebSocketCallback, WebSocketCallback, WebSocketCallback)</a> - 类中的方法 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html" title="com.binance.connector.futures.client.impl中的类">WebsocketClientImpl</a></dt>
<dd>
<div class="block">Same as <a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html#allBookTickerStream(com.binance.connector.futures.client.utils.WebSocketCallback)"><code>WebsocketClientImpl.allBookTickerStream(WebSocketCallback)</code></a> plus accepts callbacks for all major websocket connection events.</div>
</dd>
<dt><a href="com/binance/connector/futures/client/WebsocketClient.html#allBookTickerStream(com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">allBookTickerStream(WebSocketCallback, WebSocketCallback, WebSocketCallback, WebSocketCallback)</a> - 接口中的方法 com.binance.connector.futures.client.<a href="com/binance/connector/futures/client/WebsocketClient.html" title="com.binance.connector.futures.client中的接口">WebsocketClient</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html#allForceOrderStream(com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">allForceOrderStream(WebSocketCallback)</a> - 类中的方法 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html" title="com.binance.connector.futures.client.impl中的类">WebsocketClientImpl</a></dt>
<dd>
<div class="block">The All Liquidation Order Snapshot Streams push force liquidation order information for all symbols in the market.</div>
</dd>
<dt><a href="com/binance/connector/futures/client/WebsocketClient.html#allForceOrderStream(com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">allForceOrderStream(WebSocketCallback)</a> - 接口中的方法 com.binance.connector.futures.client.<a href="com/binance/connector/futures/client/WebsocketClient.html" title="com.binance.connector.futures.client中的接口">WebsocketClient</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html#allForceOrderStream(com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">allForceOrderStream(WebSocketCallback, WebSocketCallback, WebSocketCallback, WebSocketCallback)</a> - 类中的方法 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html" title="com.binance.connector.futures.client.impl中的类">WebsocketClientImpl</a></dt>
<dd>
<div class="block">Same as <a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html#allForceOrderStream(com.binance.connector.futures.client.utils.WebSocketCallback)"><code>WebsocketClientImpl.allForceOrderStream(WebSocketCallback)</code></a> plus accepts callbacks for all major websocket connection events.</div>
</dd>
<dt><a href="com/binance/connector/futures/client/WebsocketClient.html#allForceOrderStream(com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">allForceOrderStream(WebSocketCallback, WebSocketCallback, WebSocketCallback, WebSocketCallback)</a> - 接口中的方法 com.binance.connector.futures.client.<a href="com/binance/connector/futures/client/WebsocketClient.html" title="com.binance.connector.futures.client中的接口">WebsocketClient</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/UMWebsocketClientImpl.html#allMarkPriceStream(int,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">allMarkPriceStream(int, WebSocketCallback)</a> - 类中的方法 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/UMWebsocketClientImpl.html" title="com.binance.connector.futures.client.impl中的类">UMWebsocketClientImpl</a></dt>
<dd>
<div class="block">Mark price and funding rate for all symbols pushed every 3 seconds or every second.</div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/UMWebsocketClientImpl.html#allMarkPriceStream(int,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">allMarkPriceStream(int, WebSocketCallback, WebSocketCallback, WebSocketCallback, WebSocketCallback)</a> - 类中的方法 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/UMWebsocketClientImpl.html" title="com.binance.connector.futures.client.impl中的类">UMWebsocketClientImpl</a></dt>
<dd>
<div class="block">Same as <a href="com/binance/connector/futures/client/impl/UMWebsocketClientImpl.html#allMarkPriceStream(int,com.binance.connector.futures.client.utils.WebSocketCallback)"><code>UMWebsocketClientImpl.allMarkPriceStream(int, WebSocketCallback)</code></a> plus accepts callbacks for all major websocket connection events.</div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html#allMiniTickerStream(com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">allMiniTickerStream(WebSocketCallback)</a> - 类中的方法 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html" title="com.binance.connector.futures.client.impl中的类">WebsocketClientImpl</a></dt>
<dd>
<div class="block">24hr rolling window mini-ticker statistics for all symbols that changed in an array.</div>
</dd>
<dt><a href="com/binance/connector/futures/client/WebsocketClient.html#allMiniTickerStream(com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">allMiniTickerStream(WebSocketCallback)</a> - 接口中的方法 com.binance.connector.futures.client.<a href="com/binance/connector/futures/client/WebsocketClient.html" title="com.binance.connector.futures.client中的接口">WebsocketClient</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html#allMiniTickerStream(com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">allMiniTickerStream(WebSocketCallback, WebSocketCallback, WebSocketCallback, WebSocketCallback)</a> - 类中的方法 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html" title="com.binance.connector.futures.client.impl中的类">WebsocketClientImpl</a></dt>
<dd>
<div class="block">Same as <a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html#allMiniTickerStream(com.binance.connector.futures.client.utils.WebSocketCallback)"><code>WebsocketClientImpl.allMiniTickerStream(WebSocketCallback)</code></a> plus accepts callbacks for all major websocket connection events.</div>
</dd>
<dt><a href="com/binance/connector/futures/client/WebsocketClient.html#allMiniTickerStream(com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">allMiniTickerStream(WebSocketCallback, WebSocketCallback, WebSocketCallback, WebSocketCallback)</a> - 接口中的方法 com.binance.connector.futures.client.<a href="com/binance/connector/futures/client/WebsocketClient.html" title="com.binance.connector.futures.client中的接口">WebsocketClient</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/cm_futures/CMAccount.html#allOrders(java.util.LinkedHashMap)" class="member-name-link">allOrders(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.cm_futures.<a href="com/binance/connector/futures/client/impl/cm_futures/CMAccount.html" title="com.binance.connector.futures.client.impl.cm_futures中的类">CMAccount</a></dt>
<dd>
<div class="block">Get all open orders on a symbol.</div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/futures/Account.html#allOrders(java.util.LinkedHashMap)" class="member-name-link">allOrders(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.futures.<a href="com/binance/connector/futures/client/impl/futures/Account.html" title="com.binance.connector.futures.client.impl.futures中的类">Account</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/um_futures/UMAccount.html#allOrders(java.util.LinkedHashMap)" class="member-name-link">allOrders(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.um_futures.<a href="com/binance/connector/futures/client/impl/um_futures/UMAccount.html" title="com.binance.connector.futures.client.impl.um_futures中的类">UMAccount</a></dt>
<dd>
<div class="block">Get all open orders on a symbol.</div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html#allTickerStream(com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">allTickerStream(WebSocketCallback)</a> - 类中的方法 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html" title="com.binance.connector.futures.client.impl中的类">WebsocketClientImpl</a></dt>
<dd>
<div class="block">24hr rolling window ticker statistics for all symbols.</div>
</dd>
<dt><a href="com/binance/connector/futures/client/WebsocketClient.html#allTickerStream(com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">allTickerStream(WebSocketCallback)</a> - 接口中的方法 com.binance.connector.futures.client.<a href="com/binance/connector/futures/client/WebsocketClient.html" title="com.binance.connector.futures.client中的接口">WebsocketClient</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html#allTickerStream(com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">allTickerStream(WebSocketCallback, WebSocketCallback, WebSocketCallback, WebSocketCallback)</a> - 类中的方法 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html" title="com.binance.connector.futures.client.impl中的类">WebsocketClientImpl</a></dt>
<dd>
<div class="block">Same as <a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html#allTickerStream(com.binance.connector.futures.client.utils.WebSocketCallback)"><code>WebsocketClientImpl.allTickerStream(WebSocketCallback)</code></a> plus accepts callbacks for all major websocket connection events.</div>
</dd>
<dt><a href="com/binance/connector/futures/client/WebsocketClient.html#allTickerStream(com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">allTickerStream(WebSocketCallback, WebSocketCallback, WebSocketCallback, WebSocketCallback)</a> - 接口中的方法 com.binance.connector.futures.client.<a href="com/binance/connector/futures/client/WebsocketClient.html" title="com.binance.connector.futures.client中的接口">WebsocketClient</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/um_futures/UMMarket.html#assetIndex(java.util.LinkedHashMap)" class="member-name-link">assetIndex(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.um_futures.<a href="com/binance/connector/futures/client/impl/um_futures/UMMarket.html" title="com.binance.connector.futures.client.impl.um_futures中的类">UMMarket</a></dt>
<dd>
<div class="block">asset index for Multi-Assets mode
 <br><br>
 GET /v1/assetIndex
 <br>
 https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Multi-Assets-Mode-Asset-Index</div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/futures/Account.html#autoCancelOpen(java.util.LinkedHashMap)" class="member-name-link">autoCancelOpen(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.futures.<a href="com/binance/connector/futures/client/impl/futures/Account.html" title="com.binance.connector.futures.client.impl.futures中的类">Account</a></dt>
<dd>
<div class="block">Cancel all open orders of the specified symbol at the end of the specified countdown.</div>
</dd>
</dl>
<h2 class="title" id="I:B">B</h2>
<dl class="index">
<dt><a href="com/binance/connector/futures/client/impl/cm_futures/CMMarket.html#basis(java.util.LinkedHashMap)" class="member-name-link">basis(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.cm_futures.<a href="com/binance/connector/futures/client/impl/cm_futures/CMMarket.html" title="com.binance.connector.futures.client.impl.cm_futures中的类">CMMarket</a></dt>
<dd>
<div class="block">For COIN-M Futures Only
 <br><br>
 GET /futures/data/basis
 <br>
 https://developers.binance.com/docs/derivatives/coin-margined-futures/market-data/Basis</div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html#bookTicker(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">bookTicker(String, WebSocketCallback)</a> - 类中的方法 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html" title="com.binance.connector.futures.client.impl中的类">WebsocketClientImpl</a></dt>
<dd>
<div class="block">Pushes any update to the best bid or ask's price or quantity in real-time for a specified symbol.</div>
</dd>
<dt><a href="com/binance/connector/futures/client/WebsocketClient.html#bookTicker(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">bookTicker(String, WebSocketCallback)</a> - 接口中的方法 com.binance.connector.futures.client.<a href="com/binance/connector/futures/client/WebsocketClient.html" title="com.binance.connector.futures.client中的接口">WebsocketClient</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html#bookTicker(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">bookTicker(String, WebSocketCallback, WebSocketCallback, WebSocketCallback, WebSocketCallback)</a> - 类中的方法 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html" title="com.binance.connector.futures.client.impl中的类">WebsocketClientImpl</a></dt>
<dd>
<div class="block">Same as <a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html#bookTicker(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)"><code>WebsocketClientImpl.bookTicker(String, WebSocketCallback)</code></a> plus accepts callbacks for all major websocket connection events.</div>
</dd>
<dt><a href="com/binance/connector/futures/client/WebsocketClient.html#bookTicker(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">bookTicker(String, WebSocketCallback, WebSocketCallback, WebSocketCallback, WebSocketCallback)</a> - 接口中的方法 com.binance.connector.futures.client.<a href="com/binance/connector/futures/client/WebsocketClient.html" title="com.binance.connector.futures.client中的接口">WebsocketClient</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/cm_futures/CMMarket.html#bookTicker(java.util.LinkedHashMap)" class="member-name-link">bookTicker(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.cm_futures.<a href="com/binance/connector/futures/client/impl/cm_futures/CMMarket.html" title="com.binance.connector.futures.client.impl.cm_futures中的类">CMMarket</a></dt>
<dd>
<div class="block">Best price/qty on the order book for a symbol or symbols.</div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/futures/Market.html#bookTicker(java.util.LinkedHashMap)" class="member-name-link">bookTicker(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.futures.<a href="com/binance/connector/futures/client/impl/futures/Market.html" title="com.binance.connector.futures.client.impl.futures中的类">Market</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/um_futures/UMMarket.html#bookTicker(java.util.LinkedHashMap)" class="member-name-link">bookTicker(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.um_futures.<a href="com/binance/connector/futures/client/impl/um_futures/UMMarket.html" title="com.binance.connector.futures.client.impl.um_futures中的类">UMMarket</a></dt>
<dd>
<div class="block">Best price/qty on the order book for a symbol or symbols.</div>
</dd>
</dl>
<h2 class="title" id="I:C">C</h2>
<dl class="index">
<dt><a href="com/binance/connector/futures/client/impl/futures/Account.html#cancelAllOpenOrders(java.util.LinkedHashMap)" class="member-name-link">cancelAllOpenOrders(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.futures.<a href="com/binance/connector/futures/client/impl/futures/Account.html" title="com.binance.connector.futures.client.impl.futures中的类">Account</a></dt>
<dd>
<div class="block">Cancel all open orders.</div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/futures/Account.html#cancelMultipleOrders(java.util.LinkedHashMap)" class="member-name-link">cancelMultipleOrders(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.futures.<a href="com/binance/connector/futures/client/impl/futures/Account.html" title="com.binance.connector.futures.client.impl.futures中的类">Account</a></dt>
<dd>
<div class="block">Cancel multiple orders.</div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/futures/Account.html#cancelOrder(java.util.LinkedHashMap)" class="member-name-link">cancelOrder(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.futures.<a href="com/binance/connector/futures/client/impl/futures/Account.html" title="com.binance.connector.futures.client.impl.futures中的类">Account</a></dt>
<dd>
<div class="block">Cancel an active order.</div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/futures/Account.html#changeInitialLeverage(java.util.LinkedHashMap)" class="member-name-link">changeInitialLeverage(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.futures.<a href="com/binance/connector/futures/client/impl/futures/Account.html" title="com.binance.connector.futures.client.impl.futures中的类">Account</a></dt>
<dd>
<div class="block">Change user's initial leverage of specific symbol market.</div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/futures/Account.html#changeMarginType(java.util.LinkedHashMap)" class="member-name-link">changeMarginType(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.futures.<a href="com/binance/connector/futures/client/impl/futures/Account.html" title="com.binance.connector.futures.client.impl.futures中的类">Account</a></dt>
<dd>
<div class="block">Change user's margin type
 <br><br>
 POST /v1/marginType
 <br></div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/um_futures/UMAccount.html#changeMultiAssetsMode(java.util.LinkedHashMap)" class="member-name-link">changeMultiAssetsMode(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.um_futures.<a href="com/binance/connector/futures/client/impl/um_futures/UMAccount.html" title="com.binance.connector.futures.client.impl.um_futures中的类">UMAccount</a></dt>
<dd>
<div class="block">Change user's Multi-Assets mode (Multi-Assets Mode or Single-Asset Mode) on Every symbol
 <br><br>
 POST /v1/multiAssetsMargin
 <br></div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/futures/Account.html#changePositionModeTrade(java.util.LinkedHashMap)" class="member-name-link">changePositionModeTrade(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.futures.<a href="com/binance/connector/futures/client/impl/futures/Account.html" title="com.binance.connector.futures.client.impl.futures中的类">Account</a></dt>
<dd>
<div class="block">Change user's position mode (Hedge Mode or One-way Mode ) on EVERY symbol
 <br><br>
 POST /v1/positionSide/dual
 <br></div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html#closeAllConnections()" class="member-name-link">closeAllConnections()</a> - 类中的方法 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html" title="com.binance.connector.futures.client.impl中的类">WebsocketClientImpl</a></dt>
<dd>
<div class="block">Closes all streams</div>
</dd>
<dt><a href="com/binance/connector/futures/client/WebsocketClient.html#closeAllConnections()" class="member-name-link">closeAllConnections()</a> - 接口中的方法 com.binance.connector.futures.client.<a href="com/binance/connector/futures/client/WebsocketClient.html" title="com.binance.connector.futures.client中的接口">WebsocketClient</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html#closeConnection(int)" class="member-name-link">closeConnection(int)</a> - 类中的方法 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html" title="com.binance.connector.futures.client.impl中的类">WebsocketClientImpl</a></dt>
<dd>
<div class="block">Closes a specific stream based on stream Id.</div>
</dd>
<dt><a href="com/binance/connector/futures/client/WebsocketClient.html#closeConnection(int)" class="member-name-link">closeConnection(int)</a> - 接口中的方法 com.binance.connector.futures.client.<a href="com/binance/connector/futures/client/WebsocketClient.html" title="com.binance.connector.futures.client中的接口">WebsocketClient</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/futures/UserData.html#closeListenKey()" class="member-name-link">closeListenKey()</a> - 类中的方法 com.binance.connector.futures.client.impl.futures.<a href="com/binance/connector/futures/client/impl/futures/UserData.html" title="com.binance.connector.futures.client.impl.futures中的类">UserData</a></dt>
<dd>
<div class="block">Close out a user data stream.</div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/cm_futures/CMAccount.html" class="type-name-link" title="com.binance.connector.futures.client.impl.cm_futures中的类">CMAccount</a> - <a href="com/binance/connector/futures/client/impl/cm_futures/package-summary.html">com.binance.connector.futures.client.impl.cm_futures</a>中的类</dt>
<dd>
<div class="block">Coin-Margined Trade Endpoints</div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/cm_futures/CMAccount.html#%3Cinit%3E(java.lang.String,java.lang.String,java.lang.String,boolean,com.binance.connector.futures.client.utils.ProxyAuth)" class="member-name-link">CMAccount(String, String, String, boolean, ProxyAuth)</a> - 类的构造器 com.binance.connector.futures.client.impl.cm_futures.<a href="com/binance/connector/futures/client/impl/cm_futures/CMAccount.html" title="com.binance.connector.futures.client.impl.cm_futures中的类">CMAccount</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/CMFuturesClientImpl.html" class="type-name-link" title="com.binance.connector.futures.client.impl中的类">CMFuturesClientImpl</a> - <a href="com/binance/connector/futures/client/impl/package-summary.html">com.binance.connector.futures.client.impl</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/CMFuturesClientImpl.html#%3Cinit%3E()" class="member-name-link">CMFuturesClientImpl()</a> - 类的构造器 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/CMFuturesClientImpl.html" title="com.binance.connector.futures.client.impl中的类">CMFuturesClientImpl</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/CMFuturesClientImpl.html#%3Cinit%3E(java.lang.String)" class="member-name-link">CMFuturesClientImpl(String)</a> - 类的构造器 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/CMFuturesClientImpl.html" title="com.binance.connector.futures.client.impl中的类">CMFuturesClientImpl</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/CMFuturesClientImpl.html#%3Cinit%3E(java.lang.String,boolean)" class="member-name-link">CMFuturesClientImpl(String, boolean)</a> - 类的构造器 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/CMFuturesClientImpl.html" title="com.binance.connector.futures.client.impl中的类">CMFuturesClientImpl</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/CMFuturesClientImpl.html#%3Cinit%3E(java.lang.String,java.lang.String)" class="member-name-link">CMFuturesClientImpl(String, String)</a> - 类的构造器 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/CMFuturesClientImpl.html" title="com.binance.connector.futures.client.impl中的类">CMFuturesClientImpl</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/CMFuturesClientImpl.html#%3Cinit%3E(java.lang.String,java.lang.String,boolean)" class="member-name-link">CMFuturesClientImpl(String, String, boolean)</a> - 类的构造器 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/CMFuturesClientImpl.html" title="com.binance.connector.futures.client.impl中的类">CMFuturesClientImpl</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/CMFuturesClientImpl.html#%3Cinit%3E(java.lang.String,java.lang.String,java.lang.String)" class="member-name-link">CMFuturesClientImpl(String, String, String)</a> - 类的构造器 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/CMFuturesClientImpl.html" title="com.binance.connector.futures.client.impl中的类">CMFuturesClientImpl</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/cm_futures/CMMarket.html" class="type-name-link" title="com.binance.connector.futures.client.impl.cm_futures中的类">CMMarket</a> - <a href="com/binance/connector/futures/client/impl/cm_futures/package-summary.html">com.binance.connector.futures.client.impl.cm_futures</a>中的类</dt>
<dd>
<div class="block">Coin-Margined Market Endpoints</div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/cm_futures/CMMarket.html#%3Cinit%3E(java.lang.String,java.lang.String,java.lang.String,boolean,com.binance.connector.futures.client.utils.ProxyAuth)" class="member-name-link">CMMarket(String, String, String, boolean, ProxyAuth)</a> - 类的构造器 com.binance.connector.futures.client.impl.cm_futures.<a href="com/binance/connector/futures/client/impl/cm_futures/CMMarket.html" title="com.binance.connector.futures.client.impl.cm_futures中的类">CMMarket</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/cm_futures/CMPortfolioMargin.html" class="type-name-link" title="com.binance.connector.futures.client.impl.cm_futures中的类">CMPortfolioMargin</a> - <a href="com/binance/connector/futures/client/impl/cm_futures/package-summary.html">com.binance.connector.futures.client.impl.cm_futures</a>中的类</dt>
<dd>
<div class="block">Coin-Margined Portfolio Margin Endpoints</div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/cm_futures/CMPortfolioMargin.html#%3Cinit%3E(java.lang.String,java.lang.String,java.lang.String,boolean,com.binance.connector.futures.client.utils.ProxyAuth)" class="member-name-link">CMPortfolioMargin(String, String, String, boolean, ProxyAuth)</a> - 类的构造器 com.binance.connector.futures.client.impl.cm_futures.<a href="com/binance/connector/futures/client/impl/cm_futures/CMPortfolioMargin.html" title="com.binance.connector.futures.client.impl.cm_futures中的类">CMPortfolioMargin</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/cm_futures/CMUserData.html" class="type-name-link" title="com.binance.connector.futures.client.impl.cm_futures中的类">CMUserData</a> - <a href="com/binance/connector/futures/client/impl/cm_futures/package-summary.html">com.binance.connector.futures.client.impl.cm_futures</a>中的类</dt>
<dd>
<div class="block">Coin-Margined User Data Streams Endpoints</div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/cm_futures/CMUserData.html#%3Cinit%3E(java.lang.String,java.lang.String,boolean,com.binance.connector.futures.client.utils.ProxyAuth)" class="member-name-link">CMUserData(String, String, boolean, ProxyAuth)</a> - 类的构造器 com.binance.connector.futures.client.impl.cm_futures.<a href="com/binance/connector/futures/client/impl/cm_futures/CMUserData.html" title="com.binance.connector.futures.client.impl.cm_futures中的类">CMUserData</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/CMWebsocketClientImpl.html" class="type-name-link" title="com.binance.connector.futures.client.impl中的类">CMWebsocketClientImpl</a> - <a href="com/binance/connector/futures/client/impl/package-summary.html">com.binance.connector.futures.client.impl</a>中的类</dt>
<dd>
<div class="block">COIN-M Websocket Streams</div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/CMWebsocketClientImpl.html#%3Cinit%3E()" class="member-name-link">CMWebsocketClientImpl()</a> - 类的构造器 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/CMWebsocketClientImpl.html" title="com.binance.connector.futures.client.impl中的类">CMWebsocketClientImpl</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/CMWebsocketClientImpl.html#%3Cinit%3E(java.lang.String)" class="member-name-link">CMWebsocketClientImpl(String)</a> - 类的构造器 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/CMWebsocketClientImpl.html" title="com.binance.connector.futures.client.impl中的类">CMWebsocketClientImpl</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/cm_futures/CMMarket.html#coin-margined-market-endpoints-heading" class="search-tag-link">Coin-Margined Market Endpoints</a> - 类 com.binance.connector.futures.client.impl.cm_futures.CMMarket中的搜索标记</dt>
<dd>节</dd>
<dt><a href="com/binance/connector/futures/client/impl/cm_futures/CMPortfolioMargin.html#coin-margined-portfolio-margin-endpoints-heading" class="search-tag-link">Coin-Margined Portfolio Margin Endpoints</a> - 类 com.binance.connector.futures.client.impl.cm_futures.CMPortfolioMargin中的搜索标记</dt>
<dd>节</dd>
<dt><a href="com/binance/connector/futures/client/impl/cm_futures/CMAccount.html#coin-margined-trade-endpoints-heading" class="search-tag-link">Coin-Margined Trade Endpoints</a> - 类 com.binance.connector.futures.client.impl.cm_futures.CMAccount中的搜索标记</dt>
<dd>节</dd>
<dt><a href="com/binance/connector/futures/client/impl/cm_futures/CMUserData.html#coin-margined-user-data-streams-endpoints-heading" class="search-tag-link">Coin-Margined User Data Streams Endpoints</a> - 类 com.binance.connector.futures.client.impl.cm_futures.CMUserData中的搜索标记</dt>
<dd>节</dd>
<dt><a href="com/binance/connector/futures/client/impl/CMWebsocketClientImpl.html#coin-m-websocket-streams-heading" class="search-tag-link">COIN-M Websocket Streams</a> - 类 com.binance.connector.futures.client.impl.CMWebsocketClientImpl中的搜索标记</dt>
<dd>节</dd>
<dt><a href="com/binance/connector/futures/client/package-summary.html">com.binance.connector.futures.client</a> - 程序包 com.binance.connector.futures.client</dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/package-summary.html">com.binance.connector.futures.client.impl</a> - 程序包 com.binance.connector.futures.client.impl</dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/cm_futures/package-summary.html">com.binance.connector.futures.client.impl.cm_futures</a> - 程序包 com.binance.connector.futures.client.impl.cm_futures</dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/futures/package-summary.html">com.binance.connector.futures.client.impl.futures</a> - 程序包 com.binance.connector.futures.client.impl.futures</dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/um_futures/package-summary.html">com.binance.connector.futures.client.impl.um_futures</a> - 程序包 com.binance.connector.futures.client.impl.um_futures</dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html#combineStreams(java.util.ArrayList,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">combineStreams(ArrayList&lt;String&gt;, WebSocketCallback)</a> - 类中的方法 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html" title="com.binance.connector.futures.client.impl中的类">WebsocketClientImpl</a></dt>
<dd>
<div class="block">Combined streams are accessed at /stream?</div>
</dd>
<dt><a href="com/binance/connector/futures/client/WebsocketClient.html#combineStreams(java.util.ArrayList,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">combineStreams(ArrayList&lt;String&gt;, WebSocketCallback)</a> - 接口中的方法 com.binance.connector.futures.client.<a href="com/binance/connector/futures/client/WebsocketClient.html" title="com.binance.connector.futures.client中的接口">WebsocketClient</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html#combineStreams(java.util.ArrayList,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">combineStreams(ArrayList&lt;String&gt;, WebSocketCallback, WebSocketCallback, WebSocketCallback, WebSocketCallback)</a> - 类中的方法 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html" title="com.binance.connector.futures.client.impl中的类">WebsocketClientImpl</a></dt>
<dd>
<div class="block">Same as <a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html#combineStreams(java.util.ArrayList,com.binance.connector.futures.client.utils.WebSocketCallback)"><code>WebsocketClientImpl.combineStreams(ArrayList, WebSocketCallback)</code></a> plus accepts callbacks for all major websocket connection events.</div>
</dd>
<dt><a href="com/binance/connector/futures/client/WebsocketClient.html#combineStreams(java.util.ArrayList,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">combineStreams(ArrayList&lt;String&gt;, WebSocketCallback, WebSocketCallback, WebSocketCallback, WebSocketCallback)</a> - 接口中的方法 com.binance.connector.futures.client.<a href="com/binance/connector/futures/client/WebsocketClient.html" title="com.binance.connector.futures.client中的接口">WebsocketClient</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/UMWebsocketClientImpl.html#compositeIndexSymbolInfo(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">compositeIndexSymbolInfo(String, WebSocketCallback)</a> - 类中的方法 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/UMWebsocketClientImpl.html" title="com.binance.connector.futures.client.impl中的类">UMWebsocketClientImpl</a></dt>
<dd>
<div class="block">Composite index information for index symbols pushed every second.</div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/UMWebsocketClientImpl.html#compositeIndexSymbolInfo(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">compositeIndexSymbolInfo(String, WebSocketCallback, WebSocketCallback, WebSocketCallback, WebSocketCallback)</a> - 类中的方法 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/UMWebsocketClientImpl.html" title="com.binance.connector.futures.client.impl中的类">UMWebsocketClientImpl</a></dt>
<dd>
<div class="block">Same as <a href="com/binance/connector/futures/client/impl/UMWebsocketClientImpl.html#compositeIndexSymbolInfo(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)"><code>UMWebsocketClientImpl.compositeIndexSymbolInfo(String, WebSocketCallback)</code></a> plus accepts callbacks for all major websocket connection events.</div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/futures/Market.html#continuousKlines(java.util.LinkedHashMap)" class="member-name-link">continuousKlines(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.futures.<a href="com/binance/connector/futures/client/impl/futures/Market.html" title="com.binance.connector.futures.client.impl.futures中的类">Market</a></dt>
<dd>
<div class="block">Kline/candlestick bars for a specific contract type.</div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html#continuousKlineStream(java.lang.String,java.lang.String,java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">continuousKlineStream(String, String, String, WebSocketCallback)</a> - 类中的方法 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html" title="com.binance.connector.futures.client.impl中的类">WebsocketClientImpl</a></dt>
<dd>
<div class="block">The Kline/Candlestick Stream push updates to the current klines/candlestick every 250 milliseconds (if existing).</div>
</dd>
<dt><a href="com/binance/connector/futures/client/WebsocketClient.html#continuousKlineStream(java.lang.String,java.lang.String,java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">continuousKlineStream(String, String, String, WebSocketCallback)</a> - 接口中的方法 com.binance.connector.futures.client.<a href="com/binance/connector/futures/client/WebsocketClient.html" title="com.binance.connector.futures.client中的接口">WebsocketClient</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html#continuousKlineStream(java.lang.String,java.lang.String,java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">continuousKlineStream(String, String, String, WebSocketCallback, WebSocketCallback, WebSocketCallback, WebSocketCallback)</a> - 类中的方法 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html" title="com.binance.connector.futures.client.impl中的类">WebsocketClientImpl</a></dt>
<dd>
<div class="block">Same as <a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html#continuousKlineStream(java.lang.String,java.lang.String,java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)"><code>WebsocketClientImpl.continuousKlineStream(String, String, String, WebSocketCallback)</code></a> plus accepts callbacks for all major websocket connection events.</div>
</dd>
<dt><a href="com/binance/connector/futures/client/WebsocketClient.html#continuousKlineStream(java.lang.String,java.lang.String,java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">continuousKlineStream(String, String, String, WebSocketCallback, WebSocketCallback, WebSocketCallback, WebSocketCallback)</a> - 接口中的方法 com.binance.connector.futures.client.<a href="com/binance/connector/futures/client/WebsocketClient.html" title="com.binance.connector.futures.client中的接口">WebsocketClient</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html#createConnection(com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,okhttp3.Request)" class="member-name-link">createConnection(WebSocketCallback, WebSocketCallback, WebSocketCallback, WebSocketCallback, Request)</a> - 类中的方法 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html" title="com.binance.connector.futures.client.impl中的类">WebsocketClientImpl</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/futures/UserData.html#createListenKey()" class="member-name-link">createListenKey()</a> - 类中的方法 com.binance.connector.futures.client.impl.futures.<a href="com/binance/connector/futures/client/impl/futures/UserData.html" title="com.binance.connector.futures.client.impl.futures中的类">UserData</a></dt>
<dd>
<div class="block">Start a new user data stream.</div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/cm_futures/CMAccount.html#currentAllOpenOrders(java.util.LinkedHashMap)" class="member-name-link">currentAllOpenOrders(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.cm_futures.<a href="com/binance/connector/futures/client/impl/cm_futures/CMAccount.html" title="com.binance.connector.futures.client.impl.cm_futures中的类">CMAccount</a></dt>
<dd>
<div class="block">Get all open orders on a symbol.</div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/futures/Account.html#currentAllOpenOrders(java.util.LinkedHashMap)" class="member-name-link">currentAllOpenOrders(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.futures.<a href="com/binance/connector/futures/client/impl/futures/Account.html" title="com.binance.connector.futures.client.impl.futures中的类">Account</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/um_futures/UMAccount.html#currentAllOpenOrders(java.util.LinkedHashMap)" class="member-name-link">currentAllOpenOrders(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.um_futures.<a href="com/binance/connector/futures/client/impl/um_futures/UMAccount.html" title="com.binance.connector.futures.client.impl.um_futures中的类">UMAccount</a></dt>
<dd>
<div class="block">Get all open orders on a symbol.</div>
</dd>
</dl>
<h2 class="title" id="I:D">D</h2>
<dl class="index">
<dt><a href="com/binance/connector/futures/client/impl/futures/Market.html#depth(java.util.LinkedHashMap)" class="member-name-link">depth(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.futures.<a href="com/binance/connector/futures/client/impl/futures/Market.html" title="com.binance.connector.futures.client.impl.futures中的类">Market</a></dt>
<dd>
<div class="block">GET /v1/depth
 <br></div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html#diffDepthStream(java.lang.String,int,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">diffDepthStream(String, int, WebSocketCallback)</a> - 类中的方法 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html" title="com.binance.connector.futures.client.impl中的类">WebsocketClientImpl</a></dt>
<dd>
<div class="block">Bids and asks, pushed every 250 milliseconds, 500 milliseconds, 100 milliseconds (if existing)
 <br><br>
 &lt;symbol&gt;@depth@&lt;speed&gt;ms
 <br><br>
 Update Speed: 250ms, 500ms, 100ms</div>
</dd>
<dt><a href="com/binance/connector/futures/client/WebsocketClient.html#diffDepthStream(java.lang.String,int,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">diffDepthStream(String, int, WebSocketCallback)</a> - 接口中的方法 com.binance.connector.futures.client.<a href="com/binance/connector/futures/client/WebsocketClient.html" title="com.binance.connector.futures.client中的接口">WebsocketClient</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html#diffDepthStream(java.lang.String,int,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">diffDepthStream(String, int, WebSocketCallback, WebSocketCallback, WebSocketCallback, WebSocketCallback)</a> - 类中的方法 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html" title="com.binance.connector.futures.client.impl中的类">WebsocketClientImpl</a></dt>
<dd>
<div class="block">Same as <a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html#diffDepthStream(java.lang.String,int,com.binance.connector.futures.client.utils.WebSocketCallback)"><code>WebsocketClientImpl.diffDepthStream(String, int, WebSocketCallback)</code></a> plus accepts callbacks for all major websocket connection events.</div>
</dd>
<dt><a href="com/binance/connector/futures/client/WebsocketClient.html#diffDepthStream(java.lang.String,int,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">diffDepthStream(String, int, WebSocketCallback, WebSocketCallback, WebSocketCallback, WebSocketCallback)</a> - 接口中的方法 com.binance.connector.futures.client.<a href="com/binance/connector/futures/client/WebsocketClient.html" title="com.binance.connector.futures.client中的接口">WebsocketClient</a></dt>
<dd>&nbsp;</dd>
</dl>
<h2 class="title" id="I:E">E</h2>
<dl class="index">
<dt><a href="com/binance/connector/futures/client/impl/futures/Market.html#exchangeInfo()" class="member-name-link">exchangeInfo()</a> - 类中的方法 com.binance.connector.futures.client.impl.futures.<a href="com/binance/connector/futures/client/impl/futures/Market.html" title="com.binance.connector.futures.client.impl.futures中的类">Market</a></dt>
<dd>
<div class="block">Current exchange trading rules and symbol information.</div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/futures/UserData.html#extendListenKey()" class="member-name-link">extendListenKey()</a> - 类中的方法 com.binance.connector.futures.client.impl.futures.<a href="com/binance/connector/futures/client/impl/futures/UserData.html" title="com.binance.connector.futures.client.impl.futures中的类">UserData</a></dt>
<dd>
<div class="block">Keepalive a user data stream to prevent a time out.</div>
</dd>
</dl>
<h2 class="title" id="I:F">F</h2>
<dl class="index">
<dt><a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html#forceOrderStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">forceOrderStream(String, WebSocketCallback)</a> - 类中的方法 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html" title="com.binance.connector.futures.client.impl中的类">WebsocketClientImpl</a></dt>
<dd>
<div class="block">The Liquidation Order Snapshot Streams push force liquidation order information for specific symbol.</div>
</dd>
<dt><a href="com/binance/connector/futures/client/WebsocketClient.html#forceOrderStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">forceOrderStream(String, WebSocketCallback)</a> - 接口中的方法 com.binance.connector.futures.client.<a href="com/binance/connector/futures/client/WebsocketClient.html" title="com.binance.connector.futures.client中的接口">WebsocketClient</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html#forceOrderStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">forceOrderStream(String, WebSocketCallback, WebSocketCallback, WebSocketCallback, WebSocketCallback)</a> - 类中的方法 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html" title="com.binance.connector.futures.client.impl中的类">WebsocketClientImpl</a></dt>
<dd>
<div class="block">Same as <a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html#forceOrderStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)"><code>WebsocketClientImpl.forceOrderStream(String, WebSocketCallback)</code></a> plus accepts callbacks for all major websocket connection events.</div>
</dd>
<dt><a href="com/binance/connector/futures/client/WebsocketClient.html#forceOrderStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">forceOrderStream(String, WebSocketCallback, WebSocketCallback, WebSocketCallback, WebSocketCallback)</a> - 接口中的方法 com.binance.connector.futures.client.<a href="com/binance/connector/futures/client/WebsocketClient.html" title="com.binance.connector.futures.client中的接口">WebsocketClient</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/futures/Market.html#fundingRate(java.util.LinkedHashMap)" class="member-name-link">fundingRate(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.futures.<a href="com/binance/connector/futures/client/impl/futures/Market.html" title="com.binance.connector.futures.client.impl.futures中的类">Market</a></dt>
<dd>
<div class="block">Get funding rate history
 <br><br>
 GET /v1/fundingRate
 <br></div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/cm_futures/CMAccount.html#futuresAccountBalance(java.util.LinkedHashMap)" class="member-name-link">futuresAccountBalance(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.cm_futures.<a href="com/binance/connector/futures/client/impl/cm_futures/CMAccount.html" title="com.binance.connector.futures.client.impl.cm_futures中的类">CMAccount</a></dt>
<dd>
<div class="block">Get Futures Account Balance
 <br><br>
 GET /v1/balance
 <br></div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/um_futures/UMAccount.html#futuresAccountBalance(java.util.LinkedHashMap)" class="member-name-link">futuresAccountBalance(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.um_futures.<a href="com/binance/connector/futures/client/impl/um_futures/UMAccount.html" title="com.binance.connector.futures.client.impl.um_futures中的类">UMAccount</a></dt>
<dd>
<div class="block">Get Futures Account Balance
 <br><br>
 GET /v2/balance
 <br></div>
</dd>
<dt><a href="com/binance/connector/futures/client/FuturesClient.html" class="type-name-link" title="com.binance.connector.futures.client中的接口">FuturesClient</a> - <a href="com/binance/connector/futures/client/package-summary.html">com.binance.connector.futures.client</a>中的接口</dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/FuturesClientImpl.html" class="type-name-link" title="com.binance.connector.futures.client.impl中的类">FuturesClientImpl</a> - <a href="com/binance/connector/futures/client/impl/package-summary.html">com.binance.connector.futures.client.impl</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/FuturesClientImpl.html#%3Cinit%3E(java.lang.String,java.lang.String)" class="member-name-link">FuturesClientImpl(String, String)</a> - 类的构造器 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/FuturesClientImpl.html" title="com.binance.connector.futures.client.impl中的类">FuturesClientImpl</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/FuturesClientImpl.html#%3Cinit%3E(java.lang.String,java.lang.String,boolean)" class="member-name-link">FuturesClientImpl(String, String, boolean)</a> - 类的构造器 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/FuturesClientImpl.html" title="com.binance.connector.futures.client.impl中的类">FuturesClientImpl</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/FuturesClientImpl.html#%3Cinit%3E(java.lang.String,java.lang.String,java.lang.String,java.lang.String)" class="member-name-link">FuturesClientImpl(String, String, String, String)</a> - 类的构造器 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/FuturesClientImpl.html" title="com.binance.connector.futures.client.impl中的类">FuturesClientImpl</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/FuturesClientImpl.html#%3Cinit%3E(java.lang.String,java.lang.String,java.lang.String,java.lang.String,boolean)" class="member-name-link">FuturesClientImpl(String, String, String, String, boolean)</a> - 类的构造器 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/FuturesClientImpl.html" title="com.binance.connector.futures.client.impl中的类">FuturesClientImpl</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/um_futures/UMAccount.html#futuresDownloadId(java.util.LinkedHashMap)" class="member-name-link">futuresDownloadId(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.um_futures.<a href="com/binance/connector/futures/client/impl/um_futures/UMAccount.html" title="com.binance.connector.futures.client.impl.um_futures中的类">UMAccount</a></dt>
<dd>
<div class="block">Get Download Id For Futures Transaction History
 <br><br>
 GET /v1/income/asyn
 <br></div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/um_futures/UMAccount.html#futuresDownloadLink(java.util.LinkedHashMap)" class="member-name-link">futuresDownloadLink(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.um_futures.<a href="com/binance/connector/futures/client/impl/um_futures/UMAccount.html" title="com.binance.connector.futures.client.impl.um_futures中的类">UMAccount</a></dt>
<dd>
<div class="block">Get Futures Transaction History Download Link by Id
 <br><br>
 GET /v1/income/asyn/id
 <br></div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html#futures-websocket-streams-heading" class="search-tag-link">Futures Websocket Streams</a> - 类 com.binance.connector.futures.client.impl.WebsocketClientImpl中的搜索标记</dt>
<dd>节</dd>
</dl>
<h2 class="title" id="I:G">G</h2>
<dl class="index">
<dt><a href="com/binance/connector/futures/client/impl/futures/Account.html#getAdlQuantile(java.util.LinkedHashMap)" class="member-name-link">getAdlQuantile(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.futures.<a href="com/binance/connector/futures/client/impl/futures/Account.html" title="com.binance.connector.futures.client.impl.futures中的类">Account</a></dt>
<dd>
<div class="block">Position ADL Quantile Estimation
 <br><br>
 GET /v1/adlQuantile
 <br></div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/FuturesClientImpl.html#getApiKey()" class="member-name-link">getApiKey()</a> - 类中的方法 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/FuturesClientImpl.html" title="com.binance.connector.futures.client.impl中的类">FuturesClientImpl</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/futures/Market.html#getBaseUrl()" class="member-name-link">getBaseUrl()</a> - 类中的方法 com.binance.connector.futures.client.impl.futures.<a href="com/binance/connector/futures/client/impl/futures/Market.html" title="com.binance.connector.futures.client.impl.futures中的类">Market</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/FuturesClientImpl.html#getBaseUrl()" class="member-name-link">getBaseUrl()</a> - 类中的方法 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/FuturesClientImpl.html" title="com.binance.connector.futures.client.impl中的类">FuturesClientImpl</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html#getBaseUrl()" class="member-name-link">getBaseUrl()</a> - 类中的方法 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html" title="com.binance.connector.futures.client.impl中的类">WebsocketClientImpl</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/futures/Account.html#getCommissionRate(java.util.LinkedHashMap)" class="member-name-link">getCommissionRate(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.futures.<a href="com/binance/connector/futures/client/impl/futures/Account.html" title="com.binance.connector.futures.client.impl.futures中的类">Account</a></dt>
<dd>
<div class="block">User's Commission Rate
 <br><br>
 GET /v1/commissionRate
 <br></div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/um_futures/UMAccount.html#getCurrentMultiAssetMode(java.util.LinkedHashMap)" class="member-name-link">getCurrentMultiAssetMode(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.um_futures.<a href="com/binance/connector/futures/client/impl/um_futures/UMAccount.html" title="com.binance.connector.futures.client.impl.um_futures中的类">UMAccount</a></dt>
<dd>
<div class="block">Get user's Multi-Assets mode (Multi-Assets Mode or Single-Asset Mode) on Every symbol
 <br><br>
 GET /v1/multiAssetsMargin
 <br></div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/futures/Account.html#getCurrentPositionMode(java.util.LinkedHashMap)" class="member-name-link">getCurrentPositionMode(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.futures.<a href="com/binance/connector/futures/client/impl/futures/Account.html" title="com.binance.connector.futures.client.impl.futures中的类">Account</a></dt>
<dd>
<div class="block">Check an order's status.</div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/futures/Account.html#getForceOrders(java.util.LinkedHashMap)" class="member-name-link">getForceOrders(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.futures.<a href="com/binance/connector/futures/client/impl/futures/Account.html" title="com.binance.connector.futures.client.impl.futures中的类">Account</a></dt>
<dd>
<div class="block">User's Force Orders
 <br><br>
 GET /v1/forceOrders
 <br></div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/futures/Account.html#getIncomeHistory(java.util.LinkedHashMap)" class="member-name-link">getIncomeHistory(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.futures.<a href="com/binance/connector/futures/client/impl/futures/Account.html" title="com.binance.connector.futures.client.impl.futures中的类">Account</a></dt>
<dd>
<div class="block">Get Income History
 <br><br>
 GET /v1/income
 <br></div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/cm_futures/CMAccount.html#getLeverageBracket(java.util.LinkedHashMap)" class="member-name-link">getLeverageBracket(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.cm_futures.<a href="com/binance/connector/futures/client/impl/cm_futures/CMAccount.html" title="com.binance.connector.futures.client.impl.cm_futures中的类">CMAccount</a></dt>
<dd>
<div class="block">Notional and Leverage Brackets
 <br><br>
 GET /v1/leverageBracket
 <br></div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/futures/Account.html#getLeverageBracket(java.util.LinkedHashMap)" class="member-name-link">getLeverageBracket(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.futures.<a href="com/binance/connector/futures/client/impl/futures/Account.html" title="com.binance.connector.futures.client.impl.futures中的类">Account</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/um_futures/UMAccount.html#getLeverageBracket(java.util.LinkedHashMap)" class="member-name-link">getLeverageBracket(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.um_futures.<a href="com/binance/connector/futures/client/impl/um_futures/UMAccount.html" title="com.binance.connector.futures.client.impl.um_futures中的类">UMAccount</a></dt>
<dd>
<div class="block">Notional and Leverage Brackets
 <br><br>
 GET /v1/leverageBracket
 <br></div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/cm_futures/CMAccount.html#getLeverageBracketForPair(java.util.LinkedHashMap)" class="member-name-link">getLeverageBracketForPair(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.cm_futures.<a href="com/binance/connector/futures/client/impl/cm_futures/CMAccount.html" title="com.binance.connector.futures.client.impl.cm_futures中的类">CMAccount</a></dt>
<dd>
<div class="block">Notional and Leverage Brackets
 <br><br>
 GET /v1/leverageBracket
 <br></div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html#getNoopCallback()" class="member-name-link">getNoopCallback()</a> - 类中的方法 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html" title="com.binance.connector.futures.client.impl中的类">WebsocketClientImpl</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/futures/Account.html#getPositionMarginChangeHistory(java.util.LinkedHashMap)" class="member-name-link">getPositionMarginChangeHistory(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.futures.<a href="com/binance/connector/futures/client/impl/futures/Account.html" title="com.binance.connector.futures.client.impl.futures中的类">Account</a></dt>
<dd>
<div class="block">Get position margin change history
 <br><br>
 GET /v1/positionMargin/history
 <br></div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/futures/Account.html#getProductUrl()" class="member-name-link">getProductUrl()</a> - 类中的方法 com.binance.connector.futures.client.impl.futures.<a href="com/binance/connector/futures/client/impl/futures/Account.html" title="com.binance.connector.futures.client.impl.futures中的类">Account</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/futures/Market.html#getProductUrl()" class="member-name-link">getProductUrl()</a> - 类中的方法 com.binance.connector.futures.client.impl.futures.<a href="com/binance/connector/futures/client/impl/futures/Market.html" title="com.binance.connector.futures.client.impl.futures中的类">Market</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/futures/PortfolioMargin.html#getProductUrl()" class="member-name-link">getProductUrl()</a> - 类中的方法 com.binance.connector.futures.client.impl.futures.<a href="com/binance/connector/futures/client/impl/futures/PortfolioMargin.html" title="com.binance.connector.futures.client.impl.futures中的类">PortfolioMargin</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/futures/UserData.html#getProductUrl()" class="member-name-link">getProductUrl()</a> - 类中的方法 com.binance.connector.futures.client.impl.futures.<a href="com/binance/connector/futures/client/impl/futures/UserData.html" title="com.binance.connector.futures.client.impl.futures中的类">UserData</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/FuturesClientImpl.html#getProductUrl()" class="member-name-link">getProductUrl()</a> - 类中的方法 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/FuturesClientImpl.html" title="com.binance.connector.futures.client.impl中的类">FuturesClientImpl</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/FuturesClientImpl.html#getProxy()" class="member-name-link">getProxy()</a> - 类中的方法 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/FuturesClientImpl.html" title="com.binance.connector.futures.client.impl中的类">FuturesClientImpl</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/futures/Account.html#getRequestHandler()" class="member-name-link">getRequestHandler()</a> - 类中的方法 com.binance.connector.futures.client.impl.futures.<a href="com/binance/connector/futures/client/impl/futures/Account.html" title="com.binance.connector.futures.client.impl.futures中的类">Account</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/futures/Market.html#getRequestHandler()" class="member-name-link">getRequestHandler()</a> - 类中的方法 com.binance.connector.futures.client.impl.futures.<a href="com/binance/connector/futures/client/impl/futures/Market.html" title="com.binance.connector.futures.client.impl.futures中的类">Market</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/futures/PortfolioMargin.html#getRequestHandler()" class="member-name-link">getRequestHandler()</a> - 类中的方法 com.binance.connector.futures.client.impl.futures.<a href="com/binance/connector/futures/client/impl/futures/PortfolioMargin.html" title="com.binance.connector.futures.client.impl.futures中的类">PortfolioMargin</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/futures/UserData.html#getRequestHandler()" class="member-name-link">getRequestHandler()</a> - 类中的方法 com.binance.connector.futures.client.impl.futures.<a href="com/binance/connector/futures/client/impl/futures/UserData.html" title="com.binance.connector.futures.client.impl.futures中的类">UserData</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/FuturesClientImpl.html#getSecretKey()" class="member-name-link">getSecretKey()</a> - 类中的方法 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/FuturesClientImpl.html" title="com.binance.connector.futures.client.impl中的类">FuturesClientImpl</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/futures/Account.html#getShowLimitUsage()" class="member-name-link">getShowLimitUsage()</a> - 类中的方法 com.binance.connector.futures.client.impl.futures.<a href="com/binance/connector/futures/client/impl/futures/Account.html" title="com.binance.connector.futures.client.impl.futures中的类">Account</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/futures/Market.html#getShowLimitUsage()" class="member-name-link">getShowLimitUsage()</a> - 类中的方法 com.binance.connector.futures.client.impl.futures.<a href="com/binance/connector/futures/client/impl/futures/Market.html" title="com.binance.connector.futures.client.impl.futures中的类">Market</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/futures/PortfolioMargin.html#getShowLimitUsage()" class="member-name-link">getShowLimitUsage()</a> - 类中的方法 com.binance.connector.futures.client.impl.futures.<a href="com/binance/connector/futures/client/impl/futures/PortfolioMargin.html" title="com.binance.connector.futures.client.impl.futures中的类">PortfolioMargin</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/futures/UserData.html#getShowLimitUsage()" class="member-name-link">getShowLimitUsage()</a> - 类中的方法 com.binance.connector.futures.client.impl.futures.<a href="com/binance/connector/futures/client/impl/futures/UserData.html" title="com.binance.connector.futures.client.impl.futures中的类">UserData</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/FuturesClientImpl.html#getShowLimitUsage()" class="member-name-link">getShowLimitUsage()</a> - 类中的方法 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/FuturesClientImpl.html" title="com.binance.connector.futures.client.impl中的类">FuturesClientImpl</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/um_futures/UMAccount.html#getTradingRulesIndicators(java.util.LinkedHashMap)" class="member-name-link">getTradingRulesIndicators(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.um_futures.<a href="com/binance/connector/futures/client/impl/um_futures/UMAccount.html" title="com.binance.connector.futures.client.impl.um_futures中的类">UMAccount</a></dt>
<dd>
<div class="block">Futures Trading Quantitative Rules Indicators
 For more information on this, please refer to the <a href="https://www.binance.com/en/support/faq/4f462ebe6ff445d4a170be7d9e897272">Futures Trading Quantitative Rules</a>
 <br><br>
 GET /v1/apiTradingStatus
 <br></div>
</dd>
</dl>
<h2 class="title" id="I:H">H</h2>
<dl class="index">
<dt><a href="com/binance/connector/futures/client/impl/um_futures/UMMarket.html#historicalBlvt(java.util.LinkedHashMap)" class="member-name-link">historicalBlvt(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.um_futures.<a href="com/binance/connector/futures/client/impl/um_futures/UMMarket.html" title="com.binance.connector.futures.client.impl.um_futures中的类">UMMarket</a></dt>
<dd>
<div class="block">The BLVT NAV system is based on Binance Futures, so the endpoint is based on fapi
 <br><br>
 GET /v1/lvtKlines
 <br>
 https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Historical-BLVT-NAV-Kline-Candlestick</div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/futures/Market.html#historicalTrades(java.util.LinkedHashMap)" class="member-name-link">historicalTrades(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.futures.<a href="com/binance/connector/futures/client/impl/futures/Market.html" title="com.binance.connector.futures.client.impl.futures中的类">Market</a></dt>
<dd>
<div class="block">Get older market trades.</div>
</dd>
</dl>
<h2 class="title" id="I:I">I</h2>
<dl class="index">
<dt><a href="com/binance/connector/futures/client/impl/um_futures/UMMarket.html#indexInfo(java.util.LinkedHashMap)" class="member-name-link">indexInfo(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.um_futures.<a href="com/binance/connector/futures/client/impl/um_futures/UMMarket.html" title="com.binance.connector.futures.client.impl.um_futures中的类">UMMarket</a></dt>
<dd>
<div class="block">GET /v1/indexInfo
 <br>
 https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Composite-Index-Symbol-Information</div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/CMWebsocketClientImpl.html#indexKlineCandlestick(java.lang.String,java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">indexKlineCandlestick(String, String, WebSocketCallback)</a> - 类中的方法 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/CMWebsocketClientImpl.html" title="com.binance.connector.futures.client.impl中的类">CMWebsocketClientImpl</a></dt>
<dd>
<div class="block">The Kline/Candlestick Stream push updates to the current klines/candlestick every 250 milliseconds (if existing).</div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/CMWebsocketClientImpl.html#indexKlineCandlestick(java.lang.String,java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">indexKlineCandlestick(String, String, WebSocketCallback, WebSocketCallback, WebSocketCallback, WebSocketCallback)</a> - 类中的方法 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/CMWebsocketClientImpl.html" title="com.binance.connector.futures.client.impl中的类">CMWebsocketClientImpl</a></dt>
<dd>
<div class="block">Same as <a href="com/binance/connector/futures/client/impl/CMWebsocketClientImpl.html#indexKlineCandlestick(java.lang.String,java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)"><code>CMWebsocketClientImpl.indexKlineCandlestick(String, String, WebSocketCallback)</code></a> plus accepts callbacks for all major websocket connection events.</div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/futures/Market.html#indexPriceKlines(java.util.LinkedHashMap)" class="member-name-link">indexPriceKlines(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.futures.<a href="com/binance/connector/futures/client/impl/futures/Market.html" title="com.binance.connector.futures.client.impl.futures中的类">Market</a></dt>
<dd>
<div class="block">Kline/candlestick bars for the index price of a pair.</div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/CMWebsocketClientImpl.html#indexPriceStream(java.lang.String,int,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">indexPriceStream(String, int, WebSocketCallback)</a> - 类中的方法 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/CMWebsocketClientImpl.html" title="com.binance.connector.futures.client.impl中的类">CMWebsocketClientImpl</a></dt>
<dd>
<div class="block">Index Price Stream
 <br><br>
 &lt;pair&gt;@indexPrice or &lt;pair&gt;@indexPrice@1s
 <br><br>
 Update Speed: 3000ms or 1000ms</div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/CMWebsocketClientImpl.html#indexPriceStream(java.lang.String,int,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">indexPriceStream(String, int, WebSocketCallback, WebSocketCallback, WebSocketCallback, WebSocketCallback)</a> - 类中的方法 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/CMWebsocketClientImpl.html" title="com.binance.connector.futures.client.impl中的类">CMWebsocketClientImpl</a></dt>
<dd>
<div class="block">Same as <a href="com/binance/connector/futures/client/impl/CMWebsocketClientImpl.html#indexPriceStream(java.lang.String,int,com.binance.connector.futures.client.utils.WebSocketCallback)"><code>CMWebsocketClientImpl.indexPriceStream(String, int, WebSocketCallback)</code></a> plus accepts callbacks for all major websocket connection events.</div>
</dd>
</dl>
<h2 class="title" id="I:K">K</h2>
<dl class="index">
<dt><a href="com/binance/connector/futures/client/impl/futures/Market.html#klines(java.util.LinkedHashMap)" class="member-name-link">klines(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.futures.<a href="com/binance/connector/futures/client/impl/futures/Market.html" title="com.binance.connector.futures.client.impl.futures中的类">Market</a></dt>
<dd>
<div class="block">Kline/candlestick bars for a symbol.</div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html#klineStream(java.lang.String,java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">klineStream(String, String, WebSocketCallback)</a> - 类中的方法 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html" title="com.binance.connector.futures.client.impl中的类">WebsocketClientImpl</a></dt>
<dd>
<div class="block">The Kline/Candlestick Stream push updates to the current klines/candlestick every 250 milliseconds (if existing).</div>
</dd>
<dt><a href="com/binance/connector/futures/client/WebsocketClient.html#klineStream(java.lang.String,java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">klineStream(String, String, WebSocketCallback)</a> - 接口中的方法 com.binance.connector.futures.client.<a href="com/binance/connector/futures/client/WebsocketClient.html" title="com.binance.connector.futures.client中的接口">WebsocketClient</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html#klineStream(java.lang.String,java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">klineStream(String, String, WebSocketCallback, WebSocketCallback, WebSocketCallback, WebSocketCallback)</a> - 类中的方法 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html" title="com.binance.connector.futures.client.impl中的类">WebsocketClientImpl</a></dt>
<dd>
<div class="block">Same as <a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html#klineStream(java.lang.String,java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)"><code>WebsocketClientImpl.klineStream(String, String, WebSocketCallback)</code></a> plus accepts callbacks for all major websocket connection events.</div>
</dd>
<dt><a href="com/binance/connector/futures/client/WebsocketClient.html#klineStream(java.lang.String,java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">klineStream(String, String, WebSocketCallback, WebSocketCallback, WebSocketCallback, WebSocketCallback)</a> - 接口中的方法 com.binance.connector.futures.client.<a href="com/binance/connector/futures/client/WebsocketClient.html" title="com.binance.connector.futures.client中的接口">WebsocketClient</a></dt>
<dd>&nbsp;</dd>
</dl>
<h2 class="title" id="I:L">L</h2>
<dl class="index">
<dt><a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html#listenUserStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">listenUserStream(String, WebSocketCallback)</a> - 类中的方法 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html" title="com.binance.connector.futures.client.impl中的类">WebsocketClientImpl</a></dt>
<dd>
<div class="block">User Data Streams are accessed at /ws/&lt;listenKey&gt;</div>
</dd>
<dt><a href="com/binance/connector/futures/client/WebsocketClient.html#listenUserStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">listenUserStream(String, WebSocketCallback)</a> - 接口中的方法 com.binance.connector.futures.client.<a href="com/binance/connector/futures/client/WebsocketClient.html" title="com.binance.connector.futures.client中的接口">WebsocketClient</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html#listenUserStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">listenUserStream(String, WebSocketCallback, WebSocketCallback, WebSocketCallback, WebSocketCallback)</a> - 类中的方法 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html" title="com.binance.connector.futures.client.impl中的类">WebsocketClientImpl</a></dt>
<dd>
<div class="block">Same as <a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html#listenUserStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)"><code>WebsocketClientImpl.listenUserStream(String, WebSocketCallback)</code></a> plus accepts callbacks for all major websocket connection events.</div>
</dd>
<dt><a href="com/binance/connector/futures/client/WebsocketClient.html#listenUserStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">listenUserStream(String, WebSocketCallback, WebSocketCallback, WebSocketCallback, WebSocketCallback)</a> - 接口中的方法 com.binance.connector.futures.client.<a href="com/binance/connector/futures/client/WebsocketClient.html" title="com.binance.connector.futures.client中的接口">WebsocketClient</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/cm_futures/CMMarket.html#longShortRatio(java.util.LinkedHashMap)" class="member-name-link">longShortRatio(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.cm_futures.<a href="com/binance/connector/futures/client/impl/cm_futures/CMMarket.html" title="com.binance.connector.futures.client.impl.cm_futures中的类">CMMarket</a></dt>
<dd>
<div class="block">Long/Short Ratio
 <br><br>
 GET /futures/data/globalLongShortAccountRatio
 <br>
 https://developers.binance.com/docs/derivatives/coin-margined-futures/market-data/Long-Short-Ratio</div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/futures/Market.html#longShortRatio(java.util.LinkedHashMap)" class="member-name-link">longShortRatio(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.futures.<a href="com/binance/connector/futures/client/impl/futures/Market.html" title="com.binance.connector.futures.client.impl.futures中的类">Market</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/um_futures/UMMarket.html#longShortRatio(java.util.LinkedHashMap)" class="member-name-link">longShortRatio(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.um_futures.<a href="com/binance/connector/futures/client/impl/um_futures/UMMarket.html" title="com.binance.connector.futures.client.impl.um_futures中的类">UMMarket</a></dt>
<dd>
<div class="block">Long/Short Ratio
 <br><br>
 GET /futures/data/globalLongShortAccountRatio
 <br>
 https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Long-Short-Ratio</div>
</dd>
</dl>
<h2 class="title" id="I:M">M</h2>
<dl class="index">
<dt><a href="com/binance/connector/futures/client/FuturesClient.html#market()" class="member-name-link">market()</a> - 接口中的方法 com.binance.connector.futures.client.<a href="com/binance/connector/futures/client/FuturesClient.html" title="com.binance.connector.futures.client中的接口">FuturesClient</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/CMFuturesClientImpl.html#market()" class="member-name-link">market()</a> - 类中的方法 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/CMFuturesClientImpl.html" title="com.binance.connector.futures.client.impl中的类">CMFuturesClientImpl</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/UMFuturesClientImpl.html#market()" class="member-name-link">market()</a> - 类中的方法 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/UMFuturesClientImpl.html" title="com.binance.connector.futures.client.impl中的类">UMFuturesClientImpl</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/futures/Market.html" class="type-name-link" title="com.binance.connector.futures.client.impl.futures中的类">Market</a> - <a href="com/binance/connector/futures/client/impl/futures/package-summary.html">com.binance.connector.futures.client.impl.futures</a>中的类</dt>
<dd>
<div class="block">Market Endpoints</div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/futures/Market.html#%3Cinit%3E(java.lang.String,java.lang.String,java.lang.String,boolean,com.binance.connector.futures.client.utils.ProxyAuth)" class="member-name-link">Market(String, String, String, boolean, ProxyAuth)</a> - 类的构造器 com.binance.connector.futures.client.impl.futures.<a href="com/binance/connector/futures/client/impl/futures/Market.html" title="com.binance.connector.futures.client.impl.futures中的类">Market</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/futures/Market.html#market-endpoints-heading" class="search-tag-link">Market Endpoints</a> - 类 com.binance.connector.futures.client.impl.futures.Market中的搜索标记</dt>
<dd>节</dd>
<dt><a href="com/binance/connector/futures/client/impl/CMWebsocketClientImpl.html#markKlineCandlestick(java.lang.String,java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">markKlineCandlestick(String, String, WebSocketCallback)</a> - 类中的方法 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/CMWebsocketClientImpl.html" title="com.binance.connector.futures.client.impl中的类">CMWebsocketClientImpl</a></dt>
<dd>
<div class="block">The Kline/Candlestick Stream push updates to the current klines/candlestick every 250 milliseconds (if existing).</div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/CMWebsocketClientImpl.html#markKlineCandlestick(java.lang.String,java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">markKlineCandlestick(String, String, WebSocketCallback, WebSocketCallback, WebSocketCallback, WebSocketCallback)</a> - 类中的方法 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/CMWebsocketClientImpl.html" title="com.binance.connector.futures.client.impl中的类">CMWebsocketClientImpl</a></dt>
<dd>
<div class="block">Same as <a href="com/binance/connector/futures/client/impl/CMWebsocketClientImpl.html#markKlineCandlestick(java.lang.String,java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)"><code>CMWebsocketClientImpl.markKlineCandlestick(String, String, WebSocketCallback)</code></a> plus accepts callbacks for all major websocket connection events.</div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/cm_futures/CMMarket.html#markPrice(java.util.LinkedHashMap)" class="member-name-link">markPrice(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.cm_futures.<a href="com/binance/connector/futures/client/impl/cm_futures/CMMarket.html" title="com.binance.connector.futures.client.impl.cm_futures中的类">CMMarket</a></dt>
<dd>
<div class="block">Mark Price and Funding Rate
 <br><br>
 GET /v1/premiumIndex
 <br></div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/futures/Market.html#markPrice(java.util.LinkedHashMap)" class="member-name-link">markPrice(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.futures.<a href="com/binance/connector/futures/client/impl/futures/Market.html" title="com.binance.connector.futures.client.impl.futures中的类">Market</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/um_futures/UMMarket.html#markPrice(java.util.LinkedHashMap)" class="member-name-link">markPrice(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.um_futures.<a href="com/binance/connector/futures/client/impl/um_futures/UMMarket.html" title="com.binance.connector.futures.client.impl.um_futures中的类">UMMarket</a></dt>
<dd>
<div class="block">Mark Price and Funding Rate
 <br><br>
 GET /v1/premiumIndex
 <br></div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/futures/Market.html#markPriceKlines(java.util.LinkedHashMap)" class="member-name-link">markPriceKlines(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.futures.<a href="com/binance/connector/futures/client/impl/futures/Market.html" title="com.binance.connector.futures.client.impl.futures中的类">Market</a></dt>
<dd>
<div class="block">Kline/candlestick bars for the mark price of a symbol.</div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html#markPriceStream(java.lang.String,int,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">markPriceStream(String, int, WebSocketCallback)</a> - 类中的方法 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html" title="com.binance.connector.futures.client.impl中的类">WebsocketClientImpl</a></dt>
<dd>
<div class="block">Mark price and funding rate for a single symbol pushed every 3 seconds or every second.</div>
</dd>
<dt><a href="com/binance/connector/futures/client/WebsocketClient.html#markPriceStream(java.lang.String,int,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">markPriceStream(String, int, WebSocketCallback)</a> - 接口中的方法 com.binance.connector.futures.client.<a href="com/binance/connector/futures/client/WebsocketClient.html" title="com.binance.connector.futures.client中的接口">WebsocketClient</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html#markPriceStream(java.lang.String,int,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">markPriceStream(String, int, WebSocketCallback, WebSocketCallback, WebSocketCallback, WebSocketCallback)</a> - 类中的方法 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html" title="com.binance.connector.futures.client.impl中的类">WebsocketClientImpl</a></dt>
<dd>
<div class="block">Same as <a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html#markPriceStream(java.lang.String,int,com.binance.connector.futures.client.utils.WebSocketCallback)"><code>WebsocketClientImpl.markPriceStream(String, int, WebSocketCallback)</code></a> plus accepts callbacks for all major websocket connection events.</div>
</dd>
<dt><a href="com/binance/connector/futures/client/WebsocketClient.html#markPriceStream(java.lang.String,int,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">markPriceStream(String, int, WebSocketCallback, WebSocketCallback, WebSocketCallback, WebSocketCallback)</a> - 接口中的方法 com.binance.connector.futures.client.<a href="com/binance/connector/futures/client/WebsocketClient.html" title="com.binance.connector.futures.client中的接口">WebsocketClient</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/CMWebsocketClientImpl.html#markPriceSymbolsPairStream(java.lang.String,int,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">markPriceSymbolsPairStream(String, int, WebSocketCallback)</a> - 类中的方法 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/CMWebsocketClientImpl.html" title="com.binance.connector.futures.client.impl中的类">CMWebsocketClientImpl</a></dt>
<dd>
<div class="block">Mark price and funding rate for a single pair pushed every 3 seconds or every second.</div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/CMWebsocketClientImpl.html#markPriceSymbolsPairStream(java.lang.String,int,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">markPriceSymbolsPairStream(String, int, WebSocketCallback, WebSocketCallback, WebSocketCallback, WebSocketCallback)</a> - 类中的方法 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/CMWebsocketClientImpl.html" title="com.binance.connector.futures.client.impl中的类">CMWebsocketClientImpl</a></dt>
<dd>
<div class="block">Same as <a href="com/binance/connector/futures/client/impl/CMWebsocketClientImpl.html#markPriceSymbolsPairStream(java.lang.String,int,com.binance.connector.futures.client.utils.WebSocketCallback)"><code>CMWebsocketClientImpl.markPriceSymbolsPairStream(String, int, WebSocketCallback)</code></a> plus accepts callbacks for all major websocket connection events.</div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html#miniTickerStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">miniTickerStream(String, WebSocketCallback)</a> - 类中的方法 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html" title="com.binance.connector.futures.client.impl中的类">WebsocketClientImpl</a></dt>
<dd>
<div class="block">24hr rolling window mini-ticker statistics.</div>
</dd>
<dt><a href="com/binance/connector/futures/client/WebsocketClient.html#miniTickerStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">miniTickerStream(String, WebSocketCallback)</a> - 接口中的方法 com.binance.connector.futures.client.<a href="com/binance/connector/futures/client/WebsocketClient.html" title="com.binance.connector.futures.client中的接口">WebsocketClient</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html#miniTickerStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">miniTickerStream(String, WebSocketCallback, WebSocketCallback, WebSocketCallback, WebSocketCallback)</a> - 类中的方法 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html" title="com.binance.connector.futures.client.impl中的类">WebsocketClientImpl</a></dt>
<dd>
<div class="block">Same as <a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html#miniTickerStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)"><code>WebsocketClientImpl.miniTickerStream(String, WebSocketCallback)</code></a> plus accepts callbacks for all major websocket connection events.</div>
</dd>
<dt><a href="com/binance/connector/futures/client/WebsocketClient.html#miniTickerStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">miniTickerStream(String, WebSocketCallback, WebSocketCallback, WebSocketCallback, WebSocketCallback)</a> - 接口中的方法 com.binance.connector.futures.client.<a href="com/binance/connector/futures/client/WebsocketClient.html" title="com.binance.connector.futures.client中的接口">WebsocketClient</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/futures/Account.html#modifyIsolatedPositionMargin(java.util.LinkedHashMap)" class="member-name-link">modifyIsolatedPositionMargin(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.futures.<a href="com/binance/connector/futures/client/impl/futures/Account.html" title="com.binance.connector.futures.client.impl.futures中的类">Account</a></dt>
<dd>
<div class="block">Modify Isolated Position Margin
 <br><br>
 POST /v1/positionMargin
 <br></div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/cm_futures/CMAccount.html#modifyOrder(java.util.LinkedHashMap)" class="member-name-link">modifyOrder(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.cm_futures.<a href="com/binance/connector/futures/client/impl/cm_futures/CMAccount.html" title="com.binance.connector.futures.client.impl.cm_futures中的类">CMAccount</a></dt>
<dd>
<div class="block">Order modify function, currently only LIMIT order modification is supported, modified orders will be reordered in the match queue
 <br><br>
 PUT /v1/order
 <br></div>
</dd>
</dl>
<h2 class="title" id="I:N">N</h2>
<dl class="index">
<dt><a href="com/binance/connector/futures/client/impl/futures/Account.html#newOrder(java.util.LinkedHashMap)" class="member-name-link">newOrder(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.futures.<a href="com/binance/connector/futures/client/impl/futures/Account.html" title="com.binance.connector.futures.client.impl.futures中的类">Account</a></dt>
<dd>
<div class="block">Send in a new order.</div>
</dd>
</dl>
<h2 class="title" id="I:O">O</h2>
<dl class="index">
<dt><a href="com/binance/connector/futures/client/impl/futures/Market.html#openInterest(java.util.LinkedHashMap)" class="member-name-link">openInterest(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.futures.<a href="com/binance/connector/futures/client/impl/futures/Market.html" title="com.binance.connector.futures.client.impl.futures中的类">Market</a></dt>
<dd>
<div class="block">Get present open interest of a specific symbol.</div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/cm_futures/CMMarket.html#openInterestStatistics(java.util.LinkedHashMap)" class="member-name-link">openInterestStatistics(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.cm_futures.<a href="com/binance/connector/futures/client/impl/cm_futures/CMMarket.html" title="com.binance.connector.futures.client.impl.cm_futures中的类">CMMarket</a></dt>
<dd>
<div class="block">Open Interest History
 <br><br>
 GET /futures/data/openInterestHist
 <br>
 https://developers.binance.com/docs/derivatives/coin-margined-futures/market-data/Open-Interest</div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/futures/Market.html#openInterestStatistics(java.util.LinkedHashMap)" class="member-name-link">openInterestStatistics(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.futures.<a href="com/binance/connector/futures/client/impl/futures/Market.html" title="com.binance.connector.futures.client.impl.futures中的类">Market</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/um_futures/UMMarket.html#openInterestStatistics(java.util.LinkedHashMap)" class="member-name-link">openInterestStatistics(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.um_futures.<a href="com/binance/connector/futures/client/impl/um_futures/UMMarket.html" title="com.binance.connector.futures.client.impl.um_futures中的类">UMMarket</a></dt>
<dd>
<div class="block">Open Interest History
 <br><br>
 GET /futures/data/openInterestHist
 <br>
 https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Open-Interest-Statistics</div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/cm_futures/CMAccount.html#orderModifyHistory(java.util.LinkedHashMap)" class="member-name-link">orderModifyHistory(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.cm_futures.<a href="com/binance/connector/futures/client/impl/cm_futures/CMAccount.html" title="com.binance.connector.futures.client.impl.cm_futures中的类">CMAccount</a></dt>
<dd>
<div class="block">Get order modification history
 <br><br>
 GET /v1/orderAmendment
 <br></div>
</dd>
</dl>
<h2 class="title" id="I:P">P</h2>
<dl class="index">
<dt><a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html#partialDepthStream(java.lang.String,int,int,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">partialDepthStream(String, int, int, WebSocketCallback)</a> - 类中的方法 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html" title="com.binance.connector.futures.client.impl中的类">WebsocketClientImpl</a></dt>
<dd>
<div class="block">Top bids and asks, Valid are 5, 10, or 20.</div>
</dd>
<dt><a href="com/binance/connector/futures/client/WebsocketClient.html#partialDepthStream(java.lang.String,int,int,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">partialDepthStream(String, int, int, WebSocketCallback)</a> - 接口中的方法 com.binance.connector.futures.client.<a href="com/binance/connector/futures/client/WebsocketClient.html" title="com.binance.connector.futures.client中的接口">WebsocketClient</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html#partialDepthStream(java.lang.String,int,int,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">partialDepthStream(String, int, int, WebSocketCallback, WebSocketCallback, WebSocketCallback, WebSocketCallback)</a> - 类中的方法 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html" title="com.binance.connector.futures.client.impl中的类">WebsocketClientImpl</a></dt>
<dd>
<div class="block">Same as <a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html#partialDepthStream(java.lang.String,int,int,com.binance.connector.futures.client.utils.WebSocketCallback)"><code>WebsocketClientImpl.partialDepthStream(String, int, int, WebSocketCallback)</code></a> plus accepts callbacks for all major websocket connection events.</div>
</dd>
<dt><a href="com/binance/connector/futures/client/WebsocketClient.html#partialDepthStream(java.lang.String,int,int,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">partialDepthStream(String, int, int, WebSocketCallback, WebSocketCallback, WebSocketCallback, WebSocketCallback)</a> - 接口中的方法 com.binance.connector.futures.client.<a href="com/binance/connector/futures/client/WebsocketClient.html" title="com.binance.connector.futures.client中的接口">WebsocketClient</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/futures/Market.html#ping()" class="member-name-link">ping()</a> - 类中的方法 com.binance.connector.futures.client.impl.futures.<a href="com/binance/connector/futures/client/impl/futures/Market.html" title="com.binance.connector.futures.client.impl.futures中的类">Market</a></dt>
<dd>
<div class="block">Test connectivity to the Rest API.</div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/futures/Account.html#placeMultipleOrders(java.util.LinkedHashMap)" class="member-name-link">placeMultipleOrders(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.futures.<a href="com/binance/connector/futures/client/impl/futures/Account.html" title="com.binance.connector.futures.client.impl.futures中的类">Account</a></dt>
<dd>
<div class="block">Send in a new order.</div>
</dd>
<dt><a href="com/binance/connector/futures/client/FuturesClient.html#portfolioMargin()" class="member-name-link">portfolioMargin()</a> - 接口中的方法 com.binance.connector.futures.client.<a href="com/binance/connector/futures/client/FuturesClient.html" title="com.binance.connector.futures.client中的接口">FuturesClient</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/CMFuturesClientImpl.html#portfolioMargin()" class="member-name-link">portfolioMargin()</a> - 类中的方法 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/CMFuturesClientImpl.html" title="com.binance.connector.futures.client.impl中的类">CMFuturesClientImpl</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/futures/PortfolioMargin.html" class="type-name-link" title="com.binance.connector.futures.client.impl.futures中的类">PortfolioMargin</a> - <a href="com/binance/connector/futures/client/impl/futures/package-summary.html">com.binance.connector.futures.client.impl.futures</a>中的类</dt>
<dd>
<div class="block">Portfolio Margin Endpoints</div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/futures/PortfolioMargin.html#%3Cinit%3E(java.lang.String,java.lang.String,java.lang.String,boolean,com.binance.connector.futures.client.utils.ProxyAuth)" class="member-name-link">PortfolioMargin(String, String, String, boolean, ProxyAuth)</a> - 类的构造器 com.binance.connector.futures.client.impl.futures.<a href="com/binance/connector/futures/client/impl/futures/PortfolioMargin.html" title="com.binance.connector.futures.client.impl.futures中的类">PortfolioMargin</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/futures/PortfolioMargin.html#portfolioMarginAccountInfo(java.util.LinkedHashMap)" class="member-name-link">portfolioMarginAccountInfo(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.futures.<a href="com/binance/connector/futures/client/impl/futures/PortfolioMargin.html" title="com.binance.connector.futures.client.impl.futures中的类">PortfolioMargin</a></dt>
<dd>
<div class="block">Get Portfolio Margin current account information.</div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/futures/PortfolioMargin.html#portfolio-margin-endpoints-heading" class="search-tag-link">Portfolio Margin Endpoints</a> - 类 com.binance.connector.futures.client.impl.futures.PortfolioMargin中的搜索标记</dt>
<dd>节</dd>
<dt><a href="com/binance/connector/futures/client/impl/cm_futures/CMPortfolioMargin.html#portfolioMarginExchangeInfo(java.util.LinkedHashMap)" class="member-name-link">portfolioMarginExchangeInfo(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.cm_futures.<a href="com/binance/connector/futures/client/impl/cm_futures/CMPortfolioMargin.html" title="com.binance.connector.futures.client.impl.cm_futures中的类">CMPortfolioMargin</a></dt>
<dd>
<div class="block">Current Portfolio Margin exchange trading rules.</div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/futures/PortfolioMargin.html#portfolioMarginExchangeInfo(java.util.LinkedHashMap)" class="member-name-link">portfolioMarginExchangeInfo(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.futures.<a href="com/binance/connector/futures/client/impl/futures/PortfolioMargin.html" title="com.binance.connector.futures.client.impl.futures中的类">PortfolioMargin</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/cm_futures/CMAccount.html#positionInformation(java.util.LinkedHashMap)" class="member-name-link">positionInformation(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.cm_futures.<a href="com/binance/connector/futures/client/impl/cm_futures/CMAccount.html" title="com.binance.connector.futures.client.impl.cm_futures中的类">CMAccount</a></dt>
<dd>
<div class="block">Get current position information.</div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/um_futures/UMAccount.html#positionInformation(java.util.LinkedHashMap)" class="member-name-link">positionInformation(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.um_futures.<a href="com/binance/connector/futures/client/impl/um_futures/UMAccount.html" title="com.binance.connector.futures.client.impl.um_futures中的类">UMAccount</a></dt>
<dd>
<div class="block">Get current position information.</div>
</dd>
</dl>
<h2 class="title" id="I:Q">Q</h2>
<dl class="index">
<dt><a href="com/binance/connector/futures/client/impl/futures/Account.html#queryCurrentOpenOrder(java.util.LinkedHashMap)" class="member-name-link">queryCurrentOpenOrder(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.futures.<a href="com/binance/connector/futures/client/impl/futures/Account.html" title="com.binance.connector.futures.client.impl.futures中的类">Account</a></dt>
<dd>
<div class="block">Query Current Open Order
 <br><br>
 GET /v1/openOrder
 <br></div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/futures/Account.html#queryOrder(java.util.LinkedHashMap)" class="member-name-link">queryOrder(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.futures.<a href="com/binance/connector/futures/client/impl/futures/Account.html" title="com.binance.connector.futures.client.impl.futures中的类">Account</a></dt>
<dd>
<div class="block">Check an order's status.</div>
</dd>
</dl>
<h2 class="title" id="I:S">S</h2>
<dl class="index">
<dt><a href="com/binance/connector/futures/client/impl/futures/Market.html#setBaseUrl(java.lang.String)" class="member-name-link">setBaseUrl(String)</a> - 类中的方法 com.binance.connector.futures.client.impl.futures.<a href="com/binance/connector/futures/client/impl/futures/Market.html" title="com.binance.connector.futures.client.impl.futures中的类">Market</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/futures/Account.html#setProductUrl(java.lang.String)" class="member-name-link">setProductUrl(String)</a> - 类中的方法 com.binance.connector.futures.client.impl.futures.<a href="com/binance/connector/futures/client/impl/futures/Account.html" title="com.binance.connector.futures.client.impl.futures中的类">Account</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/futures/Market.html#setProductUrl(java.lang.String)" class="member-name-link">setProductUrl(String)</a> - 类中的方法 com.binance.connector.futures.client.impl.futures.<a href="com/binance/connector/futures/client/impl/futures/Market.html" title="com.binance.connector.futures.client.impl.futures中的类">Market</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/futures/PortfolioMargin.html#setProductUrl(java.lang.String)" class="member-name-link">setProductUrl(String)</a> - 类中的方法 com.binance.connector.futures.client.impl.futures.<a href="com/binance/connector/futures/client/impl/futures/PortfolioMargin.html" title="com.binance.connector.futures.client.impl.futures中的类">PortfolioMargin</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/futures/UserData.html#setProductUrl(java.lang.String)" class="member-name-link">setProductUrl(String)</a> - 类中的方法 com.binance.connector.futures.client.impl.futures.<a href="com/binance/connector/futures/client/impl/futures/UserData.html" title="com.binance.connector.futures.client.impl.futures中的类">UserData</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/FuturesClientImpl.html#setProxy(com.binance.connector.futures.client.utils.ProxyAuth)" class="member-name-link">setProxy(ProxyAuth)</a> - 类中的方法 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/FuturesClientImpl.html" title="com.binance.connector.futures.client.impl中的类">FuturesClientImpl</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/futures/UserData.html#setRequestHandler(java.lang.String,com.binance.connector.futures.client.utils.ProxyAuth)" class="member-name-link">setRequestHandler(String, ProxyAuth)</a> - 类中的方法 com.binance.connector.futures.client.impl.futures.<a href="com/binance/connector/futures/client/impl/futures/UserData.html" title="com.binance.connector.futures.client.impl.futures中的类">UserData</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/futures/Account.html#setRequestHandler(java.lang.String,java.lang.String,com.binance.connector.futures.client.utils.ProxyAuth)" class="member-name-link">setRequestHandler(String, String, ProxyAuth)</a> - 类中的方法 com.binance.connector.futures.client.impl.futures.<a href="com/binance/connector/futures/client/impl/futures/Account.html" title="com.binance.connector.futures.client.impl.futures中的类">Account</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/futures/Market.html#setRequestHandler(java.lang.String,java.lang.String,com.binance.connector.futures.client.utils.ProxyAuth)" class="member-name-link">setRequestHandler(String, String, ProxyAuth)</a> - 类中的方法 com.binance.connector.futures.client.impl.futures.<a href="com/binance/connector/futures/client/impl/futures/Market.html" title="com.binance.connector.futures.client.impl.futures中的类">Market</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/futures/PortfolioMargin.html#setRequestHandler(java.lang.String,java.lang.String,com.binance.connector.futures.client.utils.ProxyAuth)" class="member-name-link">setRequestHandler(String, String, ProxyAuth)</a> - 类中的方法 com.binance.connector.futures.client.impl.futures.<a href="com/binance/connector/futures/client/impl/futures/PortfolioMargin.html" title="com.binance.connector.futures.client.impl.futures中的类">PortfolioMargin</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/futures/Account.html#setShowLimitUsage(boolean)" class="member-name-link">setShowLimitUsage(boolean)</a> - 类中的方法 com.binance.connector.futures.client.impl.futures.<a href="com/binance/connector/futures/client/impl/futures/Account.html" title="com.binance.connector.futures.client.impl.futures中的类">Account</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/futures/Market.html#setShowLimitUsage(boolean)" class="member-name-link">setShowLimitUsage(boolean)</a> - 类中的方法 com.binance.connector.futures.client.impl.futures.<a href="com/binance/connector/futures/client/impl/futures/Market.html" title="com.binance.connector.futures.client.impl.futures中的类">Market</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/futures/PortfolioMargin.html#setShowLimitUsage(boolean)" class="member-name-link">setShowLimitUsage(boolean)</a> - 类中的方法 com.binance.connector.futures.client.impl.futures.<a href="com/binance/connector/futures/client/impl/futures/PortfolioMargin.html" title="com.binance.connector.futures.client.impl.futures中的类">PortfolioMargin</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/futures/UserData.html#setShowLimitUsage(boolean)" class="member-name-link">setShowLimitUsage(boolean)</a> - 类中的方法 com.binance.connector.futures.client.impl.futures.<a href="com/binance/connector/futures/client/impl/futures/UserData.html" title="com.binance.connector.futures.client.impl.futures中的类">UserData</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/FuturesClientImpl.html#setShowLimitUsage(boolean)" class="member-name-link">setShowLimitUsage(boolean)</a> - 类中的方法 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/FuturesClientImpl.html" title="com.binance.connector.futures.client.impl中的类">FuturesClientImpl</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html#symbolTicker(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">symbolTicker(String, WebSocketCallback)</a> - 类中的方法 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html" title="com.binance.connector.futures.client.impl中的类">WebsocketClientImpl</a></dt>
<dd>
<div class="block">24hr rolling window ticker statistics for a single symbol.</div>
</dd>
<dt><a href="com/binance/connector/futures/client/WebsocketClient.html#symbolTicker(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">symbolTicker(String, WebSocketCallback)</a> - 接口中的方法 com.binance.connector.futures.client.<a href="com/binance/connector/futures/client/WebsocketClient.html" title="com.binance.connector.futures.client中的接口">WebsocketClient</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html#symbolTicker(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">symbolTicker(String, WebSocketCallback, WebSocketCallback, WebSocketCallback, WebSocketCallback)</a> - 类中的方法 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html" title="com.binance.connector.futures.client.impl中的类">WebsocketClientImpl</a></dt>
<dd>
<div class="block">Same as <a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html#symbolTicker(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)"><code>WebsocketClientImpl.symbolTicker(String, WebSocketCallback)</code></a> plus accepts callbacks for all major websocket connection events.</div>
</dd>
<dt><a href="com/binance/connector/futures/client/WebsocketClient.html#symbolTicker(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">symbolTicker(String, WebSocketCallback, WebSocketCallback, WebSocketCallback, WebSocketCallback)</a> - 接口中的方法 com.binance.connector.futures.client.<a href="com/binance/connector/futures/client/WebsocketClient.html" title="com.binance.connector.futures.client中的接口">WebsocketClient</a></dt>
<dd>&nbsp;</dd>
</dl>
<h2 class="title" id="I:T">T</h2>
<dl class="index">
<dt><a href="com/binance/connector/futures/client/impl/um_futures/UMMarket.html#takerBuySellVol(java.util.LinkedHashMap)" class="member-name-link">takerBuySellVol(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.um_futures.<a href="com/binance/connector/futures/client/impl/um_futures/UMMarket.html" title="com.binance.connector.futures.client.impl.um_futures中的类">UMMarket</a></dt>
<dd>
<div class="block">Taker Buy/Sell Volume
 <br><br>
 GET /futures/data/takerlongshortRatio
 <br>
 https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Taker-BuySell-Volume</div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/cm_futures/CMMarket.html#ticker24H(java.util.LinkedHashMap)" class="member-name-link">ticker24H(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.cm_futures.<a href="com/binance/connector/futures/client/impl/cm_futures/CMMarket.html" title="com.binance.connector.futures.client.impl.cm_futures中的类">CMMarket</a></dt>
<dd>
<div class="block">24 hour rolling window price change statistics.</div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/futures/Market.html#ticker24H(java.util.LinkedHashMap)" class="member-name-link">ticker24H(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.futures.<a href="com/binance/connector/futures/client/impl/futures/Market.html" title="com.binance.connector.futures.client.impl.futures中的类">Market</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/um_futures/UMMarket.html#ticker24H(java.util.LinkedHashMap)" class="member-name-link">ticker24H(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.um_futures.<a href="com/binance/connector/futures/client/impl/um_futures/UMMarket.html" title="com.binance.connector.futures.client.impl.um_futures中的类">UMMarket</a></dt>
<dd>
<div class="block">24 hour rolling window price change statistics.</div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/cm_futures/CMMarket.html#tickerSymbol(java.util.LinkedHashMap)" class="member-name-link">tickerSymbol(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.cm_futures.<a href="com/binance/connector/futures/client/impl/cm_futures/CMMarket.html" title="com.binance.connector.futures.client.impl.cm_futures中的类">CMMarket</a></dt>
<dd>
<div class="block">Latest price for a symbol or symbols.</div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/futures/Market.html#tickerSymbol(java.util.LinkedHashMap)" class="member-name-link">tickerSymbol(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.futures.<a href="com/binance/connector/futures/client/impl/futures/Market.html" title="com.binance.connector.futures.client.impl.futures中的类">Market</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/um_futures/UMMarket.html#tickerSymbol(java.util.LinkedHashMap)" class="member-name-link">tickerSymbol(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.um_futures.<a href="com/binance/connector/futures/client/impl/um_futures/UMMarket.html" title="com.binance.connector.futures.client.impl.um_futures中的类">UMMarket</a></dt>
<dd>
<div class="block">Latest price for a symbol or symbols.</div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/futures/Market.html#time()" class="member-name-link">time()</a> - 类中的方法 com.binance.connector.futures.client.impl.futures.<a href="com/binance/connector/futures/client/impl/futures/Market.html" title="com.binance.connector.futures.client.impl.futures中的类">Market</a></dt>
<dd>
<div class="block">Test connectivity to the Rest API and get the current server time.</div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/cm_futures/CMMarket.html#topTraderLongShortAccs(java.util.LinkedHashMap)" class="member-name-link">topTraderLongShortAccs(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.cm_futures.<a href="com/binance/connector/futures/client/impl/cm_futures/CMMarket.html" title="com.binance.connector.futures.client.impl.cm_futures中的类">CMMarket</a></dt>
<dd>
<div class="block">Top Trader Long/Short Ratio (Accounts)
 <br><br>
 GET /futures/data/topLongShortAccountRatio
 <br>
 https://developers.binance.com/docs/derivatives/coin-margined-futures/market-data/Top-Long-Short-Account-Ratio</div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/futures/Market.html#topTraderLongShortAccs(java.util.LinkedHashMap)" class="member-name-link">topTraderLongShortAccs(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.futures.<a href="com/binance/connector/futures/client/impl/futures/Market.html" title="com.binance.connector.futures.client.impl.futures中的类">Market</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/um_futures/UMMarket.html#topTraderLongShortAccs(java.util.LinkedHashMap)" class="member-name-link">topTraderLongShortAccs(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.um_futures.<a href="com/binance/connector/futures/client/impl/um_futures/UMMarket.html" title="com.binance.connector.futures.client.impl.um_futures中的类">UMMarket</a></dt>
<dd>
<div class="block">Top Trader Long/Short Ratio (Accounts)
 <br><br>
 GET /futures/data/topLongShortAccountRatio
 <br>
 https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Top-Long-Short-Account-Ratio</div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/cm_futures/CMMarket.html#topTraderLongShortPos(java.util.LinkedHashMap)" class="member-name-link">topTraderLongShortPos(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.cm_futures.<a href="com/binance/connector/futures/client/impl/cm_futures/CMMarket.html" title="com.binance.connector.futures.client.impl.cm_futures中的类">CMMarket</a></dt>
<dd>
<div class="block">Top Trader Long/Short Ratio (Positions)
 <br><br>
 GET /futures/data/topLongShortPositionRatio
 <br>
 https://developers.binance.com/docs/derivatives/coin-margined-futures/market-data/Top-Trader-Long-Short-Ratio</div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/futures/Market.html#topTraderLongShortPos(java.util.LinkedHashMap)" class="member-name-link">topTraderLongShortPos(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.futures.<a href="com/binance/connector/futures/client/impl/futures/Market.html" title="com.binance.connector.futures.client.impl.futures中的类">Market</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/um_futures/UMMarket.html#topTraderLongShortPos(java.util.LinkedHashMap)" class="member-name-link">topTraderLongShortPos(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.um_futures.<a href="com/binance/connector/futures/client/impl/um_futures/UMMarket.html" title="com.binance.connector.futures.client.impl.um_futures中的类">UMMarket</a></dt>
<dd>
<div class="block">Top Trader Long/Short Ratio (Positions)
 <br><br>
 GET /futures/data/topLongShortPositionRatio
 <br>
 https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Top-Trader-Long-Short-Ratio</div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/futures/Account.html#trade-endpoints-heading" class="search-tag-link">Trade Endpoints</a> - 类 com.binance.connector.futures.client.impl.futures.Account中的搜索标记</dt>
<dd>节</dd>
<dt><a href="com/binance/connector/futures/client/impl/futures/Market.html#trades(java.util.LinkedHashMap)" class="member-name-link">trades(LinkedHashMap&lt;String, Object&gt;)</a> - 类中的方法 com.binance.connector.futures.client.impl.futures.<a href="com/binance/connector/futures/client/impl/futures/Market.html" title="com.binance.connector.futures.client.impl.futures中的类">Market</a></dt>
<dd>
<div class="block">Get recent trades.</div>
</dd>
</dl>
<h2 class="title" id="I:U">U</h2>
<dl class="index">
<dt><a href="com/binance/connector/futures/client/impl/um_futures/UMAccount.html" class="type-name-link" title="com.binance.connector.futures.client.impl.um_futures中的类">UMAccount</a> - <a href="com/binance/connector/futures/client/impl/um_futures/package-summary.html">com.binance.connector.futures.client.impl.um_futures</a>中的类</dt>
<dd>
<div class="block">USDⓈ-Margined Trade Endpoints</div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/um_futures/UMAccount.html#%3Cinit%3E(java.lang.String,java.lang.String,java.lang.String,boolean,com.binance.connector.futures.client.utils.ProxyAuth)" class="member-name-link">UMAccount(String, String, String, boolean, ProxyAuth)</a> - 类的构造器 com.binance.connector.futures.client.impl.um_futures.<a href="com/binance/connector/futures/client/impl/um_futures/UMAccount.html" title="com.binance.connector.futures.client.impl.um_futures中的类">UMAccount</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/UMFuturesClientImpl.html" class="type-name-link" title="com.binance.connector.futures.client.impl中的类">UMFuturesClientImpl</a> - <a href="com/binance/connector/futures/client/impl/package-summary.html">com.binance.connector.futures.client.impl</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/UMFuturesClientImpl.html#%3Cinit%3E()" class="member-name-link">UMFuturesClientImpl()</a> - 类的构造器 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/UMFuturesClientImpl.html" title="com.binance.connector.futures.client.impl中的类">UMFuturesClientImpl</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/UMFuturesClientImpl.html#%3Cinit%3E(java.lang.String)" class="member-name-link">UMFuturesClientImpl(String)</a> - 类的构造器 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/UMFuturesClientImpl.html" title="com.binance.connector.futures.client.impl中的类">UMFuturesClientImpl</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/UMFuturesClientImpl.html#%3Cinit%3E(java.lang.String,boolean)" class="member-name-link">UMFuturesClientImpl(String, boolean)</a> - 类的构造器 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/UMFuturesClientImpl.html" title="com.binance.connector.futures.client.impl中的类">UMFuturesClientImpl</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/UMFuturesClientImpl.html#%3Cinit%3E(java.lang.String,java.lang.String)" class="member-name-link">UMFuturesClientImpl(String, String)</a> - 类的构造器 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/UMFuturesClientImpl.html" title="com.binance.connector.futures.client.impl中的类">UMFuturesClientImpl</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/UMFuturesClientImpl.html#%3Cinit%3E(java.lang.String,java.lang.String,boolean)" class="member-name-link">UMFuturesClientImpl(String, String, boolean)</a> - 类的构造器 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/UMFuturesClientImpl.html" title="com.binance.connector.futures.client.impl中的类">UMFuturesClientImpl</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/UMFuturesClientImpl.html#%3Cinit%3E(java.lang.String,java.lang.String,java.lang.String)" class="member-name-link">UMFuturesClientImpl(String, String, String)</a> - 类的构造器 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/UMFuturesClientImpl.html" title="com.binance.connector.futures.client.impl中的类">UMFuturesClientImpl</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/um_futures/UMMarket.html" class="type-name-link" title="com.binance.connector.futures.client.impl.um_futures中的类">UMMarket</a> - <a href="com/binance/connector/futures/client/impl/um_futures/package-summary.html">com.binance.connector.futures.client.impl.um_futures</a>中的类</dt>
<dd>
<div class="block">USDⓈ-Margined Market Endpoints</div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/um_futures/UMMarket.html#%3Cinit%3E(java.lang.String,java.lang.String,java.lang.String,boolean,com.binance.connector.futures.client.utils.ProxyAuth)" class="member-name-link">UMMarket(String, String, String, boolean, ProxyAuth)</a> - 类的构造器 com.binance.connector.futures.client.impl.um_futures.<a href="com/binance/connector/futures/client/impl/um_futures/UMMarket.html" title="com.binance.connector.futures.client.impl.um_futures中的类">UMMarket</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/um_futures/UMUserData.html" class="type-name-link" title="com.binance.connector.futures.client.impl.um_futures中的类">UMUserData</a> - <a href="com/binance/connector/futures/client/impl/um_futures/package-summary.html">com.binance.connector.futures.client.impl.um_futures</a>中的类</dt>
<dd>
<div class="block">USDⓈ-Margined User Data Streams Endpoints</div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/um_futures/UMUserData.html#%3Cinit%3E(java.lang.String,java.lang.String,boolean,com.binance.connector.futures.client.utils.ProxyAuth)" class="member-name-link">UMUserData(String, String, boolean, ProxyAuth)</a> - 类的构造器 com.binance.connector.futures.client.impl.um_futures.<a href="com/binance/connector/futures/client/impl/um_futures/UMUserData.html" title="com.binance.connector.futures.client.impl.um_futures中的类">UMUserData</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/UMWebsocketClientImpl.html" class="type-name-link" title="com.binance.connector.futures.client.impl中的类">UMWebsocketClientImpl</a> - <a href="com/binance/connector/futures/client/impl/package-summary.html">com.binance.connector.futures.client.impl</a>中的类</dt>
<dd>
<div class="block">USDⓈ-M  Websocket Streams</div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/UMWebsocketClientImpl.html#%3Cinit%3E()" class="member-name-link">UMWebsocketClientImpl()</a> - 类的构造器 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/UMWebsocketClientImpl.html" title="com.binance.connector.futures.client.impl中的类">UMWebsocketClientImpl</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/UMWebsocketClientImpl.html#%3Cinit%3E(java.lang.String)" class="member-name-link">UMWebsocketClientImpl(String)</a> - 类的构造器 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/UMWebsocketClientImpl.html" title="com.binance.connector.futures.client.impl中的类">UMWebsocketClientImpl</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/FuturesClientImpl.html#unsetProxy()" class="member-name-link">unsetProxy()</a> - 类中的方法 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/FuturesClientImpl.html" title="com.binance.connector.futures.client.impl中的类">FuturesClientImpl</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/um_futures/UMMarket.html#usd--margined-market-endpoints-heading" class="search-tag-link">USDⓈ-Margined Market Endpoints</a> - 类 com.binance.connector.futures.client.impl.um_futures.UMMarket中的搜索标记</dt>
<dd>节</dd>
<dt><a href="com/binance/connector/futures/client/impl/um_futures/UMAccount.html#usd--margined-trade-endpoints-heading" class="search-tag-link">USDⓈ-Margined Trade Endpoints</a> - 类 com.binance.connector.futures.client.impl.um_futures.UMAccount中的搜索标记</dt>
<dd>节</dd>
<dt><a href="com/binance/connector/futures/client/impl/um_futures/UMUserData.html#usd--margined-user-data-streams-endpoints-heading" class="search-tag-link">USDⓈ-Margined User Data Streams Endpoints</a> - 类 com.binance.connector.futures.client.impl.um_futures.UMUserData中的搜索标记</dt>
<dd>节</dd>
<dt><a href="com/binance/connector/futures/client/impl/UMWebsocketClientImpl.html#usd--m-websocket-streams-heading" class="search-tag-link">USDⓈ-M Websocket Streams</a> - 类 com.binance.connector.futures.client.impl.UMWebsocketClientImpl中的搜索标记</dt>
<dd>节</dd>
<dt><a href="com/binance/connector/futures/client/FuturesClient.html#userData()" class="member-name-link">userData()</a> - 接口中的方法 com.binance.connector.futures.client.<a href="com/binance/connector/futures/client/FuturesClient.html" title="com.binance.connector.futures.client中的接口">FuturesClient</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/CMFuturesClientImpl.html#userData()" class="member-name-link">userData()</a> - 类中的方法 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/CMFuturesClientImpl.html" title="com.binance.connector.futures.client.impl中的类">CMFuturesClientImpl</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/UMFuturesClientImpl.html#userData()" class="member-name-link">userData()</a> - 类中的方法 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/UMFuturesClientImpl.html" title="com.binance.connector.futures.client.impl中的类">UMFuturesClientImpl</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/futures/UserData.html" class="type-name-link" title="com.binance.connector.futures.client.impl.futures中的类">UserData</a> - <a href="com/binance/connector/futures/client/impl/futures/package-summary.html">com.binance.connector.futures.client.impl.futures</a>中的类</dt>
<dd>
<div class="block">User Data Streams Endpoints</div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/futures/UserData.html#%3Cinit%3E(java.lang.String,java.lang.String,boolean,com.binance.connector.futures.client.utils.ProxyAuth)" class="member-name-link">UserData(String, String, boolean, ProxyAuth)</a> - 类的构造器 com.binance.connector.futures.client.impl.futures.<a href="com/binance/connector/futures/client/impl/futures/UserData.html" title="com.binance.connector.futures.client.impl.futures中的类">UserData</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/futures/UserData.html#user-data-streams-endpoints-heading" class="search-tag-link">User Data Streams Endpoints</a> - 类 com.binance.connector.futures.client.impl.futures.UserData中的搜索标记</dt>
<dd>节</dd>
</dl>
<h2 class="title" id="I:W">W</h2>
<dl class="index">
<dt><a href="com/binance/connector/futures/client/WebsocketClient.html" class="type-name-link" title="com.binance.connector.futures.client中的接口">WebsocketClient</a> - <a href="com/binance/connector/futures/client/package-summary.html">com.binance.connector.futures.client</a>中的接口</dt>
<dd>&nbsp;</dd>
<dt><a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html" class="type-name-link" title="com.binance.connector.futures.client.impl中的类">WebsocketClientImpl</a> - <a href="com/binance/connector/futures/client/impl/package-summary.html">com.binance.connector.futures.client.impl</a>中的类</dt>
<dd>
<div class="block">Futures Websocket Streams</div>
</dd>
<dt><a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html#%3Cinit%3E(java.lang.String)" class="member-name-link">WebsocketClientImpl(String)</a> - 类的构造器 com.binance.connector.futures.client.impl.<a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html" title="com.binance.connector.futures.client.impl中的类">WebsocketClientImpl</a></dt>
<dd>&nbsp;</dd>
</dl>
<a href="#I:A">A</a>&nbsp;<a href="#I:B">B</a>&nbsp;<a href="#I:C">C</a>&nbsp;<a href="#I:D">D</a>&nbsp;<a href="#I:E">E</a>&nbsp;<a href="#I:F">F</a>&nbsp;<a href="#I:G">G</a>&nbsp;<a href="#I:H">H</a>&nbsp;<a href="#I:I">I</a>&nbsp;<a href="#I:K">K</a>&nbsp;<a href="#I:L">L</a>&nbsp;<a href="#I:M">M</a>&nbsp;<a href="#I:N">N</a>&nbsp;<a href="#I:O">O</a>&nbsp;<a href="#I:P">P</a>&nbsp;<a href="#I:Q">Q</a>&nbsp;<a href="#I:S">S</a>&nbsp;<a href="#I:T">T</a>&nbsp;<a href="#I:U">U</a>&nbsp;<a href="#I:W">W</a>&nbsp;<br><a href="allpackages-index.html">所有程序包</a><span class="vertical-separator">|</span><a href="allclasses-index.html">所有类和接口</a></main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &#169; 2025. All rights reserved.</small></p>
</footer>
</div>
</div>
</body>
</html>
