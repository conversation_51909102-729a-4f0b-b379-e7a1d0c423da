<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (21) on Fri Apr 04 16:06:08 CST 2025 -->
<title>WebsocketClient (io.github.binance:binance-futures-connector-java 3.0.5 API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-04-04">
<meta name="description" content="declaration: package: com.binance.connector.futures.client, interface: WebsocketClient">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="切换导航链接"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="../../../../../index.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="nav-bar-cell1-rev">类</li>
<li><a href="class-use/WebsocketClient.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../index-all.html">索引</a></li>
<li><a href="../../../../../help-doc.html#class">帮助</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>概要:</p>
<ul>
<li>嵌套</li>
<li>字段</li>
<li>构造器</li>
<li><a href="#method-summary">方法</a></li>
</ul>
</li>
<li>
<p>详细资料:</p>
<ul>
<li>字段</li>
<li>构造器</li>
<li><a href="#method-detail">方法</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method-summary">方法</a></li>
</ul>
<ul class="sub-nav-list">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method-detail">方法</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="搜索">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">程序包</span>&nbsp;<a href="package-summary.html">com.binance.connector.futures.client</a></div>
<h1 title="接口 WebsocketClient" class="title">接口 WebsocketClient</h1>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>所有已知实现类:</dt>
<dd><code><a href="impl/CMWebsocketClientImpl.html" title="com.binance.connector.futures.client.impl中的类">CMWebsocketClientImpl</a></code>, <code><a href="impl/UMWebsocketClientImpl.html" title="com.binance.connector.futures.client.impl中的类">UMWebsocketClientImpl</a></code>, <code><a href="impl/WebsocketClientImpl.html" title="com.binance.connector.futures.client.impl中的类">WebsocketClientImpl</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public interface </span><span class="element-name type-name-label">WebsocketClient</span></div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>方法概要</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">所有方法</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">实例方法</button><button id="method-summary-table-tab3" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab3', 3)" class="table-tab">抽象方法</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">修饰符和类型</div>
<div class="table-header col-second">方法</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#aggTradeStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">aggTradeStream</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;symbol,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#aggTradeStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">aggTradeStream</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;symbol,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onOpenCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onClosingCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onFailureCallback)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#allBookTickerStream(com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">allBookTickerStream</a><wbr>(com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#allBookTickerStream(com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">allBookTickerStream</a><wbr>(com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onOpenCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onClosingCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onFailureCallback)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#allForceOrderStream(com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">allForceOrderStream</a><wbr>(com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#allForceOrderStream(com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">allForceOrderStream</a><wbr>(com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onOpenCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onClosingCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onFailureCallback)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#allMiniTickerStream(com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">allMiniTickerStream</a><wbr>(com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#allMiniTickerStream(com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">allMiniTickerStream</a><wbr>(com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onOpenCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onClosingCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onFailureCallback)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#allTickerStream(com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">allTickerStream</a><wbr>(com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#allTickerStream(com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">allTickerStream</a><wbr>(com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onOpenCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onClosingCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onFailureCallback)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#bookTicker(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">bookTicker</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;symbol,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#bookTicker(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">bookTicker</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;symbol,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onOpenCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onClosingCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onFailureCallback)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#closeAllConnections()" class="member-name-link">closeAllConnections</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#closeConnection(int)" class="member-name-link">closeConnection</a><wbr>(int&nbsp;streamId)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#combineStreams(java.util.ArrayList,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">combineStreams</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/util/ArrayList.html" title="java.util中的类或接口" class="external-link">ArrayList</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&gt;&nbsp;streams,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#combineStreams(java.util.ArrayList,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">combineStreams</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/util/ArrayList.html" title="java.util中的类或接口" class="external-link">ArrayList</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&gt;&nbsp;streams,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onOpenCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onClosingCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onFailureCallback)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#continuousKlineStream(java.lang.String,java.lang.String,java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">continuousKlineStream</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;pair,
 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;interval,
 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;contractType,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#continuousKlineStream(java.lang.String,java.lang.String,java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">continuousKlineStream</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;pair,
 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;interval,
 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;contractType,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onOpenCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onClosingCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onFailureCallback)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#diffDepthStream(java.lang.String,int,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">diffDepthStream</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;symbol,
 int&nbsp;speed,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#diffDepthStream(java.lang.String,int,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">diffDepthStream</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;symbol,
 int&nbsp;speed,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onOpenCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onClosingCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onFailureCallback)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#forceOrderStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">forceOrderStream</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;symbol,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#forceOrderStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">forceOrderStream</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;symbol,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onOpenCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onClosingCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onFailureCallback)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#klineStream(java.lang.String,java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">klineStream</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;symbol,
 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;interval,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#klineStream(java.lang.String,java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">klineStream</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;symbol,
 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;interval,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onOpenCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onClosingCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onFailureCallback)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#listenUserStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">listenUserStream</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;listenKey,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#listenUserStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">listenUserStream</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;listenKey,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onOpenCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onClosingCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onFailureCallback)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#markPriceStream(java.lang.String,int,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">markPriceStream</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;symbol,
 int&nbsp;speed,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#markPriceStream(java.lang.String,int,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">markPriceStream</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;symbol,
 int&nbsp;speed,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onOpenCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onClosingCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onFailureCallback)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#miniTickerStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">miniTickerStream</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;symbol,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#miniTickerStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">miniTickerStream</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;symbol,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onOpenCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onClosingCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onFailureCallback)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#partialDepthStream(java.lang.String,int,int,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">partialDepthStream</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;symbol,
 int&nbsp;levels,
 int&nbsp;speed,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#partialDepthStream(java.lang.String,int,int,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">partialDepthStream</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;symbol,
 int&nbsp;levels,
 int&nbsp;speed,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onOpenCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onClosingCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onFailureCallback)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#symbolTicker(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">symbolTicker</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;symbol,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#symbolTicker(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">symbolTicker</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;symbol,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onOpenCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onClosingCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onFailureCallback)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">&nbsp;</div>
</div>
</div>
</div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>方法详细资料</h2>
<ul class="member-list">
<li>
<section class="detail" id="symbolTicker(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)">
<h3>symbolTicker</h3>
<div class="member-signature"><span class="return-type">int</span>&nbsp;<span class="element-name">symbolTicker</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;symbol,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback)</span></div>
</section>
</li>
<li>
<section class="detail" id="symbolTicker(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)">
<h3>symbolTicker</h3>
<div class="member-signature"><span class="return-type">int</span>&nbsp;<span class="element-name">symbolTicker</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;symbol,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onOpenCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onClosingCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onFailureCallback)</span></div>
</section>
</li>
<li>
<section class="detail" id="klineStream(java.lang.String,java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)">
<h3>klineStream</h3>
<div class="member-signature"><span class="return-type">int</span>&nbsp;<span class="element-name">klineStream</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;symbol,
 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;interval,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback)</span></div>
</section>
</li>
<li>
<section class="detail" id="klineStream(java.lang.String,java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)">
<h3>klineStream</h3>
<div class="member-signature"><span class="return-type">int</span>&nbsp;<span class="element-name">klineStream</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;symbol,
 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;interval,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onOpenCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onClosingCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onFailureCallback)</span></div>
</section>
</li>
<li>
<section class="detail" id="aggTradeStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)">
<h3>aggTradeStream</h3>
<div class="member-signature"><span class="return-type">int</span>&nbsp;<span class="element-name">aggTradeStream</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;symbol,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback)</span></div>
</section>
</li>
<li>
<section class="detail" id="aggTradeStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)">
<h3>aggTradeStream</h3>
<div class="member-signature"><span class="return-type">int</span>&nbsp;<span class="element-name">aggTradeStream</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;symbol,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onOpenCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onClosingCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onFailureCallback)</span></div>
</section>
</li>
<li>
<section class="detail" id="miniTickerStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)">
<h3>miniTickerStream</h3>
<div class="member-signature"><span class="return-type">int</span>&nbsp;<span class="element-name">miniTickerStream</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;symbol,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback)</span></div>
</section>
</li>
<li>
<section class="detail" id="miniTickerStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)">
<h3>miniTickerStream</h3>
<div class="member-signature"><span class="return-type">int</span>&nbsp;<span class="element-name">miniTickerStream</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;symbol,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onOpenCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onClosingCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onFailureCallback)</span></div>
</section>
</li>
<li>
<section class="detail" id="allTickerStream(com.binance.connector.futures.client.utils.WebSocketCallback)">
<h3>allTickerStream</h3>
<div class="member-signature"><span class="return-type">int</span>&nbsp;<span class="element-name">allTickerStream</span><wbr><span class="parameters">(com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback)</span></div>
</section>
</li>
<li>
<section class="detail" id="allTickerStream(com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)">
<h3>allTickerStream</h3>
<div class="member-signature"><span class="return-type">int</span>&nbsp;<span class="element-name">allTickerStream</span><wbr><span class="parameters">(com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onOpenCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onClosingCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onFailureCallback)</span></div>
</section>
</li>
<li>
<section class="detail" id="allMiniTickerStream(com.binance.connector.futures.client.utils.WebSocketCallback)">
<h3>allMiniTickerStream</h3>
<div class="member-signature"><span class="return-type">int</span>&nbsp;<span class="element-name">allMiniTickerStream</span><wbr><span class="parameters">(com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback)</span></div>
</section>
</li>
<li>
<section class="detail" id="allMiniTickerStream(com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)">
<h3>allMiniTickerStream</h3>
<div class="member-signature"><span class="return-type">int</span>&nbsp;<span class="element-name">allMiniTickerStream</span><wbr><span class="parameters">(com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onOpenCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onClosingCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onFailureCallback)</span></div>
</section>
</li>
<li>
<section class="detail" id="bookTicker(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)">
<h3>bookTicker</h3>
<div class="member-signature"><span class="return-type">int</span>&nbsp;<span class="element-name">bookTicker</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;symbol,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback)</span></div>
</section>
</li>
<li>
<section class="detail" id="bookTicker(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)">
<h3>bookTicker</h3>
<div class="member-signature"><span class="return-type">int</span>&nbsp;<span class="element-name">bookTicker</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;symbol,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onOpenCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onClosingCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onFailureCallback)</span></div>
</section>
</li>
<li>
<section class="detail" id="allBookTickerStream(com.binance.connector.futures.client.utils.WebSocketCallback)">
<h3>allBookTickerStream</h3>
<div class="member-signature"><span class="return-type">int</span>&nbsp;<span class="element-name">allBookTickerStream</span><wbr><span class="parameters">(com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback)</span></div>
</section>
</li>
<li>
<section class="detail" id="allBookTickerStream(com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)">
<h3>allBookTickerStream</h3>
<div class="member-signature"><span class="return-type">int</span>&nbsp;<span class="element-name">allBookTickerStream</span><wbr><span class="parameters">(com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onOpenCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onClosingCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onFailureCallback)</span></div>
</section>
</li>
<li>
<section class="detail" id="partialDepthStream(java.lang.String,int,int,com.binance.connector.futures.client.utils.WebSocketCallback)">
<h3>partialDepthStream</h3>
<div class="member-signature"><span class="return-type">int</span>&nbsp;<span class="element-name">partialDepthStream</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;symbol,
 int&nbsp;levels,
 int&nbsp;speed,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback)</span></div>
</section>
</li>
<li>
<section class="detail" id="partialDepthStream(java.lang.String,int,int,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)">
<h3>partialDepthStream</h3>
<div class="member-signature"><span class="return-type">int</span>&nbsp;<span class="element-name">partialDepthStream</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;symbol,
 int&nbsp;levels,
 int&nbsp;speed,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onOpenCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onClosingCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onFailureCallback)</span></div>
</section>
</li>
<li>
<section class="detail" id="diffDepthStream(java.lang.String,int,com.binance.connector.futures.client.utils.WebSocketCallback)">
<h3>diffDepthStream</h3>
<div class="member-signature"><span class="return-type">int</span>&nbsp;<span class="element-name">diffDepthStream</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;symbol,
 int&nbsp;speed,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback)</span></div>
</section>
</li>
<li>
<section class="detail" id="diffDepthStream(java.lang.String,int,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)">
<h3>diffDepthStream</h3>
<div class="member-signature"><span class="return-type">int</span>&nbsp;<span class="element-name">diffDepthStream</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;symbol,
 int&nbsp;speed,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onOpenCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onClosingCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onFailureCallback)</span></div>
</section>
</li>
<li>
<section class="detail" id="combineStreams(java.util.ArrayList,com.binance.connector.futures.client.utils.WebSocketCallback)">
<h3>combineStreams</h3>
<div class="member-signature"><span class="return-type">int</span>&nbsp;<span class="element-name">combineStreams</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/util/ArrayList.html" title="java.util中的类或接口" class="external-link">ArrayList</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&gt;&nbsp;streams,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback)</span></div>
</section>
</li>
<li>
<section class="detail" id="combineStreams(java.util.ArrayList,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)">
<h3>combineStreams</h3>
<div class="member-signature"><span class="return-type">int</span>&nbsp;<span class="element-name">combineStreams</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/util/ArrayList.html" title="java.util中的类或接口" class="external-link">ArrayList</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&gt;&nbsp;streams,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onOpenCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onClosingCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onFailureCallback)</span></div>
</section>
</li>
<li>
<section class="detail" id="listenUserStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)">
<h3>listenUserStream</h3>
<div class="member-signature"><span class="return-type">int</span>&nbsp;<span class="element-name">listenUserStream</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;listenKey,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback)</span></div>
</section>
</li>
<li>
<section class="detail" id="listenUserStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)">
<h3>listenUserStream</h3>
<div class="member-signature"><span class="return-type">int</span>&nbsp;<span class="element-name">listenUserStream</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;listenKey,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onOpenCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onClosingCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onFailureCallback)</span></div>
</section>
</li>
<li>
<section class="detail" id="closeConnection(int)">
<h3>closeConnection</h3>
<div class="member-signature"><span class="return-type">void</span>&nbsp;<span class="element-name">closeConnection</span><wbr><span class="parameters">(int&nbsp;streamId)</span></div>
</section>
</li>
<li>
<section class="detail" id="closeAllConnections()">
<h3>closeAllConnections</h3>
<div class="member-signature"><span class="return-type">void</span>&nbsp;<span class="element-name">closeAllConnections</span>()</div>
</section>
</li>
<li>
<section class="detail" id="markPriceStream(java.lang.String,int,com.binance.connector.futures.client.utils.WebSocketCallback)">
<h3>markPriceStream</h3>
<div class="member-signature"><span class="return-type">int</span>&nbsp;<span class="element-name">markPriceStream</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;symbol,
 int&nbsp;speed,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback)</span></div>
</section>
</li>
<li>
<section class="detail" id="markPriceStream(java.lang.String,int,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)">
<h3>markPriceStream</h3>
<div class="member-signature"><span class="return-type">int</span>&nbsp;<span class="element-name">markPriceStream</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;symbol,
 int&nbsp;speed,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onOpenCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onClosingCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onFailureCallback)</span></div>
</section>
</li>
<li>
<section class="detail" id="continuousKlineStream(java.lang.String,java.lang.String,java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)">
<h3>continuousKlineStream</h3>
<div class="member-signature"><span class="return-type">int</span>&nbsp;<span class="element-name">continuousKlineStream</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;pair,
 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;interval,
 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;contractType,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback)</span></div>
</section>
</li>
<li>
<section class="detail" id="continuousKlineStream(java.lang.String,java.lang.String,java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)">
<h3>continuousKlineStream</h3>
<div class="member-signature"><span class="return-type">int</span>&nbsp;<span class="element-name">continuousKlineStream</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;pair,
 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;interval,
 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;contractType,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onOpenCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onClosingCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onFailureCallback)</span></div>
</section>
</li>
<li>
<section class="detail" id="forceOrderStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)">
<h3>forceOrderStream</h3>
<div class="member-signature"><span class="return-type">int</span>&nbsp;<span class="element-name">forceOrderStream</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;symbol,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback)</span></div>
</section>
</li>
<li>
<section class="detail" id="forceOrderStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)">
<h3>forceOrderStream</h3>
<div class="member-signature"><span class="return-type">int</span>&nbsp;<span class="element-name">forceOrderStream</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;symbol,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onOpenCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onClosingCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onFailureCallback)</span></div>
</section>
</li>
<li>
<section class="detail" id="allForceOrderStream(com.binance.connector.futures.client.utils.WebSocketCallback)">
<h3>allForceOrderStream</h3>
<div class="member-signature"><span class="return-type">int</span>&nbsp;<span class="element-name">allForceOrderStream</span><wbr><span class="parameters">(com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback)</span></div>
</section>
</li>
<li>
<section class="detail" id="allForceOrderStream(com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)">
<h3>allForceOrderStream</h3>
<div class="member-signature"><span class="return-type">int</span>&nbsp;<span class="element-name">allForceOrderStream</span><wbr><span class="parameters">(com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onOpenCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onClosingCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onFailureCallback)</span></div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &#169; 2025. All rights reserved.</small></p>
</footer>
</div>
</div>
</body>
</html>
