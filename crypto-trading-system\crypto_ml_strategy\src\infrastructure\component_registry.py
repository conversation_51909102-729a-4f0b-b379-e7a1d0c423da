#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
组件注册表模块

该模块提供组件注册和配置功能，支持依赖注入容器的自动配置。
"""

from typing import Dict, Any, List, Optional, Type
from loguru import logger

from .dependency_container import DependencyContainer, ServiceLifetime
from .service_interfaces import *
from .service_factory import ServiceFactory


class ComponentRegistry:
    """
    组件注册表
    
    负责注册所有服务组件到依赖注入容器中。
    """
    
    def __init__(self, container: DependencyContainer):
        """
        初始化组件注册表
        
        Args:
            container: 依赖注入容器
        """
        self.container = container
        self._registered_components: List[str] = []
        logger.info("组件注册表初始化完成")
    
    def register_all_components(self, config_path: Optional[str] = None) -> None:
        """
        注册所有组件
        
        Args:
            config_path: 配置文件路径
        """
        logger.info("开始注册所有组件...")
        
        # 1. 注册核心服务
        self._register_core_services(config_path)
        
        # 2. 注册数据服务
        self._register_data_services()
        
        # 3. 注册模型服务
        self._register_model_services()
        
        # 4. 注册策略服务
        self._register_strategy_services()
        
        # 5. 注册监控服务
        self._register_monitoring_services()
        
        # 6. 注册应用服务
        self._register_application_services()
        
        logger.info(f"组件注册完成，共注册 {len(self._registered_components)} 个组件")
        self._log_registered_components()
    
    def _register_core_services(self, config_path: Optional[str] = None) -> None:
        """注册核心服务"""
        logger.debug("注册核心服务...")
        
        # 配置服务 - 单例，最先注册
        self.container.register_singleton(
            IConfigService,
            factory=lambda: ServiceFactory(None).create_config_service(config_path)
        )
        self._registered_components.append("ConfigService")
        
        # 日志服务 - 单例
        self.container.register_singleton(
            ILoggerService,
            factory=lambda config: ServiceFactory(config).create_logger_service()
        )
        self._registered_components.append("LoggerService")
        
        # 服务工厂 - 单例
        self.container.register_singleton(
            ServiceFactory,
            factory=lambda config: ServiceFactory(config)
        )
        self._registered_components.append("ServiceFactory")
    
    def _register_data_services(self) -> None:
        """注册数据服务"""
        logger.debug("注册数据服务...")
        
        # InfluxDB服务 - 单例
        self.container.register_singleton(
            IInfluxDBService,
            factory=lambda factory: factory.create_influxdb_service()
        )
        self._registered_components.append("InfluxDBService")
        
        # MySQL服务 - 单例
        self.container.register_singleton(
            IMySQLService,
            factory=lambda factory: factory.create_mysql_service()
        )
        self._registered_components.append("MySQLService")
        
        # Kafka服务 - 单例
        self.container.register_singleton(
            IKafkaService,
            factory=lambda factory: factory.create_kafka_service()
        )
        self._registered_components.append("KafkaService")
        
        # 数据处理服务 - 单例
        self.container.register_singleton(
            IDataProcessorService,
            factory=lambda factory: factory.create_data_processor_service()
        )
        self._registered_components.append("DataProcessorService")
        
        # 数据加载服务 - 单例
        self.container.register_singleton(
            IDataLoaderService,
            factory=lambda factory, influxdb, mysql: factory.create_data_loader_service(influxdb, mysql)
        )
        self._registered_components.append("DataLoaderService")
    
    def _register_model_services(self) -> None:
        """注册模型服务"""
        logger.debug("注册模型服务...")
        
        # 模型版本管理服务 - 单例
        self.container.register_singleton(
            IModelVersionService,
            factory=lambda factory, mysql: factory.create_model_version_service(mysql)
        )
        self._registered_components.append("ModelVersionService")
        
        # 模型训练服务 - 单例
        self.container.register_singleton(
            IModelTrainerService,
            factory=lambda factory, version_service: factory.create_model_trainer_service(version_service)
        )
        self._registered_components.append("ModelTrainerService")
        
        # 预测引擎服务 - 单例
        self.container.register_singleton(
            IPredictionEngineService,
            factory=lambda factory, version_service: factory.create_prediction_engine_service(version_service)
        )
        self._registered_components.append("PredictionEngineService")
        
        # 在线学习服务 - 单例
        self.container.register_singleton(
            IOnlineLearnerService,
            factory=lambda factory, version_service: factory.create_online_learner_service(version_service)
        )
        self._registered_components.append("OnlineLearnerService")
    
    def _register_strategy_services(self) -> None:
        """注册策略服务"""
        logger.debug("注册策略服务...")
        
        # 策略服务 - 单例
        self.container.register_singleton(
            IStrategyService,
            factory=lambda factory: factory.create_strategy_service()
        )
        self._registered_components.append("StrategyService")
    
    def _register_monitoring_services(self) -> None:
        """注册监控服务"""
        logger.debug("注册监控服务...")
        
        # 健康检查服务 - 单例
        self.container.register_singleton(
            IHealthCheckService,
            factory=lambda factory: factory.create_health_check_service()
        )
        self._registered_components.append("HealthCheckService")
        
        # 指标服务 - 单例
        self.container.register_singleton(
            IMetricsService,
            factory=lambda factory: factory.create_metrics_service()
        )
        self._registered_components.append("MetricsService")
    
    def _register_application_services(self) -> None:
        """注册应用服务"""
        logger.debug("注册应用服务...")
        
        # 生命周期管理服务 - 单例
        self.container.register_singleton(
            ILifecycleService,
            factory=lambda: LifecycleManager()
        )
        self._registered_components.append("LifecycleService")
        
        # 应用服务 - 单例
        self.container.register_singleton(
            IApplicationService,
            factory=lambda container: TradingApplication(container)
        )
        self._registered_components.append("ApplicationService")
    
    def _log_registered_components(self) -> None:
        """记录已注册的组件"""
        logger.info("已注册的组件:")
        for i, component in enumerate(self._registered_components, 1):
            logger.info(f"  {i:2d}. {component}")
    
    def get_registered_components(self) -> List[str]:
        """
        获取已注册的组件列表
        
        Returns:
            已注册的组件列表
        """
        return self._registered_components.copy()
    
    def validate_registrations(self) -> Dict[str, Any]:
        """
        验证组件注册
        
        Returns:
            验证结果
        """
        validation_results = {
            "total_registered": len(self._registered_components),
            "successful_resolutions": 0,
            "failed_resolutions": 0,
            "errors": []
        }
        
        # 测试解析所有已注册的服务接口
        service_interfaces = [
            IConfigService, ILoggerService, IInfluxDBService, IMySQLService,
            IKafkaService, IDataProcessorService, IDataLoaderService,
            IModelVersionService, IModelTrainerService, IPredictionEngineService,
            IOnlineLearnerService, IStrategyService, IHealthCheckService,
            IMetricsService, ILifecycleService, IApplicationService
        ]
        
        for service_interface in service_interfaces:
            try:
                service = self.container.resolve(service_interface)
                if service is not None:
                    validation_results["successful_resolutions"] += 1
                    logger.debug(f"✅ 成功解析: {service_interface.__name__}")
                else:
                    validation_results["failed_resolutions"] += 1
                    validation_results["errors"].append(f"解析返回None: {service_interface.__name__}")
                    
            except Exception as e:
                validation_results["failed_resolutions"] += 1
                error_msg = f"解析失败 {service_interface.__name__}: {str(e)}"
                validation_results["errors"].append(error_msg)
                logger.error(f"❌ {error_msg}")
        
        validation_results["success_rate"] = (
            validation_results["successful_resolutions"] / 
            (validation_results["successful_resolutions"] + validation_results["failed_resolutions"])
            if (validation_results["successful_resolutions"] + validation_results["failed_resolutions"]) > 0 else 0
        )
        
        return validation_results


class LifecycleManager:
    """生命周期管理器实现"""
    
    def __init__(self):
        """初始化生命周期管理器"""
        self._state = "CREATED"
        self._managed_services: List[Any] = []
        logger.info("生命周期管理器初始化完成")
    
    def initialize(self) -> None:
        """初始化"""
        if self._state != "CREATED":
            raise RuntimeError(f"无法从状态 {self._state} 初始化")
        
        self._state = "INITIALIZED"
        logger.info("生命周期管理器已初始化")
    
    def start(self) -> None:
        """启动"""
        if self._state != "INITIALIZED":
            raise RuntimeError(f"无法从状态 {self._state} 启动")
        
        self._state = "STARTED"
        logger.info("生命周期管理器已启动")
    
    def stop(self) -> None:
        """停止"""
        if self._state != "STARTED":
            logger.warning(f"从状态 {self._state} 停止")
        
        # 反向停止所有管理的服务
        for service in reversed(self._managed_services):
            try:
                if hasattr(service, 'stop'):
                    service.stop()
            except Exception as e:
                logger.error(f"停止服务失败: {e}")
        
        self._state = "STOPPED"
        logger.info("生命周期管理器已停止")
    
    def cleanup(self) -> None:
        """清理资源"""
        # 清理所有管理的服务
        for service in self._managed_services:
            try:
                if hasattr(service, 'cleanup'):
                    service.cleanup()
            except Exception as e:
                logger.error(f"清理服务失败: {e}")
        
        self._managed_services.clear()
        self._state = "CLEANED"
        logger.info("生命周期管理器已清理")
    
    def get_lifecycle_state(self) -> str:
        """获取生命周期状态"""
        return self._state
    
    def add_managed_service(self, service: Any) -> None:
        """添加管理的服务"""
        self._managed_services.append(service)


class TradingApplication:
    """交易应用实现"""
    
    def __init__(self, container: DependencyContainer):
        """
        初始化交易应用
        
        Args:
            container: 依赖注入容器
        """
        self.container = container
        self._status = "CREATED"
        self._services: Dict[str, Any] = {}
        logger.info("交易应用初始化完成")
    
    def start(self) -> None:
        """启动应用"""
        try:
            logger.info("启动交易应用...")
            
            # 解析核心服务
            self._services['lifecycle'] = self.container.resolve(ILifecycleService)
            self._services['health_check'] = self.container.resolve(IHealthCheckService)
            self._services['metrics'] = self.container.resolve(IMetricsService)
            
            # 初始化生命周期管理器
            self._services['lifecycle'].initialize()
            self._services['lifecycle'].start()
            
            self._status = "RUNNING"
            logger.info("交易应用启动成功")
            
        except Exception as e:
            self._status = "FAILED"
            logger.error(f"交易应用启动失败: {e}")
            raise
    
    def stop(self) -> None:
        """停止应用"""
        try:
            logger.info("停止交易应用...")
            
            if 'lifecycle' in self._services:
                self._services['lifecycle'].stop()
                self._services['lifecycle'].cleanup()
            
            self._status = "STOPPED"
            logger.info("交易应用已停止")
            
        except Exception as e:
            logger.error(f"停止交易应用失败: {e}")
            raise
    
    def get_status(self) -> Dict[str, Any]:
        """获取应用状态"""
        status = {
            "application_status": self._status,
            "services_loaded": len(self._services),
            "timestamp": time.time()
        }
        
        if 'lifecycle' in self._services:
            status["lifecycle_state"] = self._services['lifecycle'].get_lifecycle_state()
        
        if 'health_check' in self._services:
            try:
                status["health_status"] = self._services['health_check'].check_health()
            except Exception as e:
                status["health_error"] = str(e)
        
        return status
    
    def handle_signal(self, signum: int, frame) -> None:
        """处理系统信号"""
        logger.info(f"收到系统信号: {signum}")
        
        if signum in [2, 15]:  # SIGINT, SIGTERM
            logger.info("收到停止信号，开始优雅关闭...")
            self.stop()


if __name__ == "__main__":
    # 测试代码
    import time
    
    # 创建容器和注册表
    container = DependencyContainer()
    registry = ComponentRegistry(container)
    
    # 注册所有组件
    registry.register_all_components()
    
    # 验证注册
    validation_results = registry.validate_registrations()
    logger.info(f"验证结果: {validation_results}")
    
    # 测试应用启动
    try:
        app = container.resolve(IApplicationService)
        app.start()
        
        status = app.get_status()
        logger.info(f"应用状态: {status}")
        
        time.sleep(1)
        app.stop()
        
    except Exception as e:
        logger.error(f"应用测试失败: {e}")