/**
 * Autogenerated by Avro
 *
 * DO NOT EDIT DIRECTLY
 */
package com.crypto.trading.trade.model.avro;
@org.apache.avro.specific.AvroGenerated
public enum OrderStatus implements org.apache.avro.generic.GenericEnumSymbol<OrderStatus> {
  NEW, PARTIALLY_FILLED, FILLED, CANCELED, REJECTED, EXPIRED  ;
  public static final org.apache.avro.Schema SCHEMA$ = new org.apache.avro.Schema.Parser().parse("{\"type\":\"enum\",\"name\":\"OrderStatus\",\"namespace\":\"com.crypto.trading.trade.model.avro\",\"symbols\":[\"NEW\",\"PARTIALLY_FILLED\",\"FILLED\",\"CANCELED\",\"REJECTED\",\"EXPIRED\"]}");
  public static org.apache.avro.Schema getClassSchema() { return SCHEMA$; }

  @Override
  public org.apache.avro.Schema getSchema() { return SCHEMA$; }
}
