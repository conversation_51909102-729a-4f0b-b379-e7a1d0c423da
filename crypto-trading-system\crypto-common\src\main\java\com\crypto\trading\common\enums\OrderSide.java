package com.crypto.trading.common.enums;

/**
 * 订单方向枚举
 * <p>
 * 定义订单的买卖方向
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public enum OrderSide {

    /**
     * 买入
     */
    BUY("BUY", "买入"),

    /**
     * 卖出
     */
    SELL("SELL", "卖出");

    /**
     * 方向码
     */
    private final String code;

    /**
     * 方向描述
     */
    private final String desc;

    /**
     * 构造函数
     *
     * @param code 方向码
     * @param desc 方向描述
     */
    OrderSide(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 获取方向码
     *
     * @return 方向码
     */
    public String getCode() {
        return code;
    }

    /**
     * 获取方向描述
     *
     * @return 方向描述
     */
    public String getDesc() {
        return desc;
    }

    /**
     * 根据方向码获取枚举
     *
     * @param code 方向码
     * @return 枚举实例
     */
    public static OrderSide fromCode(String code) {
        for (OrderSide side : OrderSide.values()) {
            if (side.getCode().equals(code)) {
                return side;
            }
        }
        throw new IllegalArgumentException("未知的订单方向码: " + code);
    }

    /**
     * 获取相反方向
     *
     * @return 相反方向的枚举实例
     */
    public OrderSide opposite() {
        return this == BUY ? SELL : BUY;
    }
}