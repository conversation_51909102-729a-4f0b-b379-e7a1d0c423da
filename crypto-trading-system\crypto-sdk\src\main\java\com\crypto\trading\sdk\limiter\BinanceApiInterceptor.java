package com.crypto.trading.sdk.limiter;

import com.crypto.trading.common.constant.APILimitConstants;
import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.Response;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 币安API拦截器
 * <p>
 * 拦截币安API请求，实现限流和响应头处理
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
public class BinanceApiInterceptor implements Interceptor {

    private static final Logger log = LoggerFactory.getLogger(BinanceApiInterceptor.class);
    
    /**
     * API端点正则表达式
     */
    private static final Pattern API_ENDPOINT_PATTERN = Pattern.compile(".*/([^/?]+)(?:\\?.*)?$");
    
    /**
     * 限流器
     */
    private final RateLimiter rateLimiter;
    
    /**
     * 响应头限流信息更新器
     */
    private final ResponseHeaderRateLimitUpdater headerUpdater;
    
    /**
     * 构造函数
     *
     * @param rateLimiter  限流器
     * @param headerUpdater 响应头限流信息更新器
     */
    public BinanceApiInterceptor(RateLimiter rateLimiter, ResponseHeaderRateLimitUpdater headerUpdater) {
        this.rateLimiter = rateLimiter;
        this.headerUpdater = headerUpdater;
    }
    
    @Override
    public Response intercept(Chain chain) throws IOException {
        Request request = chain.request();
        String url = request.url().toString();
        
        // 提取API端点
        String endpoint = extractEndpoint(url);
        
        // 确定请求权重
        int weight = determineRequestWeight(endpoint, request);
        
        // 检查限流并等待
        checkRateLimit(endpoint, weight);
        
        // 执行请求
        long startTime = System.currentTimeMillis();
        Response response = chain.proceed(request);
        long endTime = System.currentTimeMillis();
        
        // 记录请求耗时
        log.debug("请求[{}]耗时: {}ms", endpoint, (endTime - startTime));
        
        // 处理响应头中的限流信息
        processResponseHeaders(response, endpoint);
        
        return response;
    }
    
    /**
     * 提取API端点
     *
     * @param url 请求URL
     * @return API端点
     */
    private String extractEndpoint(String url) {
        Matcher matcher = API_ENDPOINT_PATTERN.matcher(url);
        if (matcher.find()) {
            return matcher.group(1);
        }
        return "unknown";
    }
    
    /**
     * 确定请求权重
     *
     * @param endpoint API端点
     * @param request  请求
     * @return 请求权重
     */
    private int determineRequestWeight(String endpoint, Request request) {
        // 根据端点和请求方法确定权重
        String method = request.method();
        
        // 市场数据请求
        if (endpoint.contains("depth")) {
            String limit = request.url().queryParameter("limit");
            if (limit != null) {
                int limitValue = Integer.parseInt(limit);
                if (limitValue > 500) {
                    return APILimitConstants.BINANCE_FUTURES_WEIGHT_DEPTH_HEAVY;
                }
            }
            return APILimitConstants.BINANCE_FUTURES_WEIGHT_DEPTH_DEFAULT;
        } else if (endpoint.contains("klines")) {
            String limit = request.url().queryParameter("limit");
            if (limit != null) {
                int limitValue = Integer.parseInt(limit);
                if (limitValue > 500) {
                    return APILimitConstants.BINANCE_FUTURES_WEIGHT_KLINES_HEAVY;
                }
            }
            return APILimitConstants.BINANCE_FUTURES_WEIGHT_KLINES;
        } else if (endpoint.contains("trades") && request.url().queryParameter("limit") != null) {
            int limitValue = Integer.parseInt(request.url().queryParameter("limit"));
            if (limitValue > 500) {
                return APILimitConstants.BINANCE_FUTURES_WEIGHT_TRADES_HISTORY_HEAVY;
            }
            return APILimitConstants.BINANCE_FUTURES_WEIGHT_TRADES_HISTORY;
        }
        
        // 账户和订单请求
        if (endpoint.contains("account")) {
            return APILimitConstants.BINANCE_FUTURES_WEIGHT_ACCOUNT_INFO;
        } else if (endpoint.contains("order") && "POST".equals(method)) {
            return APILimitConstants.BINANCE_FUTURES_WEIGHT_ORDER;
        } else if (endpoint.contains("orders") && "POST".equals(method)) {
            return APILimitConstants.BINANCE_FUTURES_WEIGHT_BATCH_ORDERS;
        } else if (endpoint.contains("order") && "DELETE".equals(method)) {
            return APILimitConstants.BINANCE_FUTURES_WEIGHT_CANCEL_ORDER;
        } else if (endpoint.contains("allOpenOrders") && "DELETE".equals(method)) {
            return APILimitConstants.BINANCE_FUTURES_WEIGHT_CANCEL_ALL_ORDERS;
        } else if (endpoint.contains("order") && "GET".equals(method)) {
            return APILimitConstants.BINANCE_FUTURES_WEIGHT_QUERY_ORDER;
        } else if (endpoint.contains("allOrders") && "GET".equals(method)) {
            return APILimitConstants.BINANCE_FUTURES_WEIGHT_QUERY_ALL_ORDERS;
        } else if (endpoint.contains("position")) {
            return APILimitConstants.BINANCE_FUTURES_WEIGHT_POSITION_INFO;
        }
        
        // 默认权重
        return APILimitConstants.BINANCE_FUTURES_WEIGHT_MARKET_DEFAULT;
    }
    
    /**
     * 检查限流并等待
     *
     * @param endpoint API端点
     * @param weight   请求权重
     */
    private void checkRateLimit(String endpoint, int weight) {
        // 检查使用率
        double usageRate = rateLimiter.getUsageRate(endpoint);
        
        // 如果使用率超过警告阈值，记录警告日志
        if (usageRate > APILimitConstants.RATE_LIMIT_WARNING_THRESHOLD) {
            log.warn("端点[{}]的API使用率较高: {}%", endpoint, String.format("%.1f", usageRate * 100));
        }
        
        // 如果使用率超过紧急阈值，增加延迟
        if (usageRate > APILimitConstants.RATE_LIMIT_CRITICAL_THRESHOLD) {
            log.warn("端点[{}]的API使用率接近上限: {}%，增加请求延迟", endpoint, String.format("%.1f", usageRate * 100));
            try {
                // 根据使用率动态计算延迟时间
                long delayMs = (long) (1000 * (usageRate - APILimitConstants.RATE_LIMIT_WARNING_THRESHOLD) * 10);
                TimeUnit.MILLISECONDS.sleep(Math.min(delayMs, 5000)); // 最大延迟5秒
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.warn("请求延迟被中断");
            }
        }
    }
    
    /**
     * 处理响应头中的限流信息
     *
     * @param response 响应
     * @param endpoint API端点
     */
    private void processResponseHeaders(Response response, String endpoint) {
        // 提取所有响应头
        Map<String, String> headers = new HashMap<>();
        for (String name : response.headers().names()) {
            headers.put(name.toLowerCase(), response.header(name));
        }
        
        // 处理响应头中的限流信息
        headerUpdater.processResponseHeaders(headers, endpoint);
    }
}