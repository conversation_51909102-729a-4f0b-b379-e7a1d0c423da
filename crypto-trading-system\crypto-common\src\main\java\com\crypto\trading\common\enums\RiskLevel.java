package com.crypto.trading.common.enums;

/**
 * 风险级别枚举
 * <p>
 * 表示交易策略或持仓的风险级别
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public enum RiskLevel {

    /**
     * 极低风险
     */
    VERY_LOW(1, "极低风险", "保守型策略，最大回撤通常小于5%"),

    /**
     * 低风险
     */
    LOW(2, "低风险", "稳健型策略，最大回撤通常在5%-10%之间"),

    /**
     * 中等风险
     */
    MEDIUM(3, "中等风险", "平衡型策略，最大回撤通常在10%-20%之间"),

    /**
     * 高风险
     */
    HIGH(4, "高风险", "进取型策略，最大回撤通常在20%-30%之间"),

    /**
     * 极高风险
     */
    VERY_HIGH(5, "极高风险", "激进型策略，最大回撤可能超过30%");

    /**
     * 级别值
     */
    private final int level;

    /**
     * 描述
     */
    private final String desc;

    /**
     * 详细说明
     */
    private final String detail;

    /**
     * 构造函数
     *
     * @param level  级别值
     * @param desc   描述
     * @param detail 详细说明
     */
    RiskLevel(int level, String desc, String detail) {
        this.level = level;
        this.desc = desc;
        this.detail = detail;
    }

    /**
     * 获取级别值
     *
     * @return 级别值
     */
    public int getLevel() {
        return level;
    }

    /**
     * 获取描述
     *
     * @return 描述
     */
    public String getDesc() {
        return desc;
    }

    /**
     * 获取详细说明
     *
     * @return 详细说明
     */
    public String getDetail() {
        return detail;
    }

    /**
     * 根据级别值获取枚举值
     *
     * @param level 级别值
     * @return 枚举值
     */
    public static RiskLevel fromLevel(int level) {
        for (RiskLevel riskLevel : RiskLevel.values()) {
            if (riskLevel.getLevel() == level) {
                return riskLevel;
            }
        }
        throw new IllegalArgumentException("未知的风险级别值: " + level);
    }

    /**
     * 判断当前风险级别是否高于指定风险级别
     *
     * @param other 指定风险级别
     * @return 如果当前风险级别高于指定风险级别，则返回true，否则返回false
     */
    public boolean isHigherThan(RiskLevel other) {
        return this.level > other.level;
    }

    /**
     * 判断当前风险级别是否低于指定风险级别
     *
     * @param other 指定风险级别
     * @return 如果当前风险级别低于指定风险级别，则返回true，否则返回false
     */
    public boolean isLowerThan(RiskLevel other) {
        return this.level < other.level;
    }
} 