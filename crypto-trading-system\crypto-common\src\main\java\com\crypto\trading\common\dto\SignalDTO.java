package com.crypto.trading.common.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.io.Serializable;

/**
 * @name: SignalDTO
 * @author: zeek
 * @description: 顶层策略信号数据传输对象，对应从Kafka接收的完整消息结构。
 * @create: 2024-07-16 00:28
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SignalDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String messageId;
    private String messageType;
    private Long timestamp;
    
    // 添加实际使用的字段，使得类的结构与代码中的调用方式相匹配
    private String signalId;
    private String strategyId;
    private String symbol;
    
    // 将data改名为signalPayload，更贴合实际用法
    private SignalPayloadDTO signalPayload;
    
    /**
     * 兼容性方法，用于保持与原有代码的兼容性
     * @return 返回信号载荷数据
     */
    public SignalPayloadDTO getData() {
        return this.signalPayload;
    }
    
    /**
     * 兼容性方法，用于保持与原有代码的兼容性
     * @param data 信号载荷数据
     */
    public void setData(SignalPayloadDTO data) {
        this.signalPayload = data;
    }
}
