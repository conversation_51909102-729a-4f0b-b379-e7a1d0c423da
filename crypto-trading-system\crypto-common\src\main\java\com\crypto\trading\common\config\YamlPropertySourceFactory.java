package com.crypto.trading.common.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.config.YamlPropertiesFactoryBean;
import org.springframework.core.env.PropertiesPropertySource;
import org.springframework.core.env.PropertySource;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.EncodedResource;
import org.springframework.core.io.support.PropertySourceFactory;

import java.io.IOException;
import java.util.Properties;

/**
 * YAML属性源工厂类
 * <p>
 * 用于加载YAML格式的配置文件，使Spring能够解析和加载YAML格式的配置文件。
 * 可用于@PropertySource注解，使其支持YAML格式。
 * </p>
 * 
 * 使用示例：
 * <pre>
 * {@code
 * @Configuration
 * @PropertySource(value = "classpath:custom-config.yml", factory = YamlPropertySourceFactory.class)
 * public class CustomConfig {
 *     // ...
 * }
 * }
 * </pre>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class YamlPropertySourceFactory implements PropertySourceFactory {
    
    private static final Logger log = LoggerFactory.getLogger(YamlPropertySourceFactory.class);

    /**
     * 创建属性源
     *
     * @param name 属性源名称
     * @param encodedResource 编码资源
     * @return 属性源
     * @throws IOException 如果资源读取失败
     */
    @Override
    public PropertySource<?> createPropertySource(String name, EncodedResource encodedResource) throws IOException {
        Resource resource = encodedResource.getResource();
        String sourceName = name != null ? name : resource.getFilename();
        
        log.info("加载YAML配置文件: {}", sourceName);
        
        YamlPropertiesFactoryBean factory = new YamlPropertiesFactoryBean();
        factory.setResources(resource);
        factory.afterPropertiesSet();
        
        Properties properties = factory.getObject();
        if (properties == null) {
            throw new IOException("无法加载YAML配置文件: " + sourceName);
        }
        
        return new PropertiesPropertySource(sourceName, properties);
    }
}