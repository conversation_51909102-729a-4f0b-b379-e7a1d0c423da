package com.crypto.trading.common.constant;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * 常量类测试
 * <p>
 * 测试常量类是否可以正常访问
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class ConstantsTest {

    /**
     * 测试KafkaConstants
     */
    @Test
    public void testKafkaConstants() {
        assertEquals("kline.data", KafkaConstants.TOPIC_KLINE_DATA);
        assertEquals("depth.data", KafkaConstants.TOPIC_DEPTH_DATA);
        assertEquals("trade.data", KafkaConstants.TOPIC_TRADE_DATA);
        assertEquals("strategy.signal", KafkaConstants.TOPIC_STRATEGY_SIGNAL);
        assertEquals("account.update", KafkaConstants.TOPIC_ACCOUNT_UPDATE);
        assertEquals("order.update", KafkaConstants.TOPIC_ORDER_UPDATE);
    }

    /**
     * 测试SystemConstants
     */
    @Test
    public void testSystemConstants() {
        assertEquals("crypto-trading", SystemConstants.SYSTEM_NAME);
        assertEquals("1.0.0", SystemConstants.SYSTEM_VERSION);
        assertEquals(200, SystemConstants.SUCCESS_CODE);
        assertEquals(500, SystemConstants.ERROR_CODE);
        assertEquals(401, SystemConstants.UNAUTHORIZED_CODE);
        assertEquals(403, SystemConstants.FORBIDDEN_CODE);
        assertEquals(400, SystemConstants.BAD_REQUEST_CODE);
        assertEquals(404, SystemConstants.NOT_FOUND_CODE);
    }

    /**
     * 测试TradeConstants
     */
    @Test
    public void testTradeConstants() {
        assertEquals("BUY", TradeConstants.DIRECTION_BUY);
        assertEquals("SELL", TradeConstants.DIRECTION_SELL);
        assertEquals("LONG", TradeConstants.POSITION_LONG);
        assertEquals("SHORT", TradeConstants.POSITION_SHORT);
        assertEquals("MARKET", TradeConstants.ORDER_TYPE_MARKET);
        assertEquals("LIMIT", TradeConstants.ORDER_TYPE_LIMIT);
        assertEquals("STOP", TradeConstants.ORDER_TYPE_STOP);
        assertEquals("STOP_LIMIT", TradeConstants.ORDER_TYPE_STOP_LIMIT);
    }

    /**
     * 测试BinanceConstants
     */
    @Test
    public void testBinanceConstants() {
        assertEquals("https://fapi.binance.com", BinanceConstants.API_BASE_URL_FUTURES_USDT);
        assertEquals("https://testnet.binancefuture.com", BinanceConstants.API_BASE_URL_FUTURES_USDT_TESTNET);
        assertEquals("wss://fstream.binance.com", BinanceConstants.WS_BASE_URL_FUTURES_USDT);
        assertEquals("X-MBX-APIKEY", BinanceConstants.API_KEY_HEADER);
        assertEquals("signature", BinanceConstants.SIGNATURE_PARAM);
        assertEquals("timestamp", BinanceConstants.TIMESTAMP_PARAM);
    }

    /**
     * 测试InfluxDBConstants
     */
    @Test
    public void testInfluxDBConstants() {
        assertEquals("http://localhost:8086", InfluxDBConstants.DEFAULT_URL);
        assertEquals("crypto_trading", InfluxDBConstants.DEFAULT_DATABASE);
        assertEquals("autogen", InfluxDBConstants.DEFAULT_RETENTION_POLICY);
        assertEquals("kline_data", InfluxDBConstants.MEASUREMENT_KLINE);
        assertEquals("depth_data", InfluxDBConstants.MEASUREMENT_DEPTH);
        assertEquals("trade_data", InfluxDBConstants.MEASUREMENT_TRADE);
        assertEquals("symbol", InfluxDBConstants.TAG_SYMBOL);
    }

    /**
     * 测试ExceptionConstants
     */
    @Test
    public void testExceptionConstants() {
        assertEquals("COMMON-", ExceptionConstants.ERROR_PREFIX_COMMON);
        assertEquals("MARKET-", ExceptionConstants.ERROR_PREFIX_MARKET);
        assertEquals("STRATEGY-", ExceptionConstants.ERROR_PREFIX_STRATEGY);
        assertEquals("TRADE-", ExceptionConstants.ERROR_PREFIX_TRADE);
        assertEquals("ACCOUNT-", ExceptionConstants.ERROR_PREFIX_ACCOUNT);
        assertEquals("RISK-", ExceptionConstants.ERROR_PREFIX_RISK);
        assertEquals("MONITOR-", ExceptionConstants.ERROR_PREFIX_MONITOR);
    }

    /**
     * 测试APILimitConstants
     */
    @Test
    public void testAPILimitConstants() {
        assertEquals(60_000L, APILimitConstants.BINANCE_FUTURES_WINDOW_MS);
        assertEquals(2400, APILimitConstants.BINANCE_FUTURES_IP_LIMIT_ALL);
        assertEquals(1200, APILimitConstants.BINANCE_FUTURES_IP_LIMIT_ORDER);
        assertEquals(2400, APILimitConstants.BINANCE_FUTURES_IP_LIMIT_QUERY);
        assertEquals(50, APILimitConstants.BINANCE_FUTURES_WEBSOCKET_CONNECTIONS_LIMIT);
        assertEquals(1, APILimitConstants.BINANCE_FUTURES_WEIGHT_MARKET_DEFAULT);
        assertEquals(5, APILimitConstants.BINANCE_FUTURES_WEIGHT_ACCOUNT_INFO);
    }

    /**
     * 测试常量类的私有构造函数（提高覆盖率）
     */
    @Test
    public void testPrivateConstructors() {
        assertThrows(Throwable.class, () -> {
            // 使用反射调用私有构造函数
            java.lang.reflect.Constructor<KafkaConstants> constructor = KafkaConstants.class.getDeclaredConstructor();
            constructor.setAccessible(true);
            constructor.newInstance();
        });

        assertThrows(Throwable.class, () -> {
            java.lang.reflect.Constructor<SystemConstants> constructor = SystemConstants.class.getDeclaredConstructor();
            constructor.setAccessible(true);
            constructor.newInstance();
        });
    }
} 