-- 创建订单表
CREATE TABLE IF NOT EXISTS `t_order` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `order_id` VARCHAR(64) NOT NULL COMMENT '交易所订单ID',
    `client_order_id` VARCHAR(64) NOT NULL COMMENT '客户端订单ID',
    `signal_id` VARCHAR(64) COMMENT '关联的交易信号ID',
    `symbol` VARCHAR(20) NOT NULL COMMENT '交易对',
    `side` VARCHAR(10) NOT NULL COMMENT '订单方向 BUY/SELL',
    `position_side` VARCHAR(10) DEFAULT 'BOTH' COMMENT '仓位方向 BOTH/LONG/SHORT',
    `order_type` VARCHAR(20) NOT NULL COMMENT '订单类型 MARKET/LIMIT/STOP等',
    `time_in_force` VARCHAR(10) DEFAULT 'GTC' COMMENT '订单有效期 GTC/IOC/FOK',
    `quantity` DECIMAL(20,8) NOT NULL COMMENT '交易数量',
    `price` DECIMAL(20,8) COMMENT '订单价格',
    `executed_price` DECIMAL(20,8) COMMENT '执行价格',
    `executed_quantity` DECIMAL(20,8) COMMENT '已执行数量',
    `status` VARCHAR(20) NOT NULL COMMENT '订单状态 NEW/FILLED/CANCELED等',
    `error_code` VARCHAR(64) COMMENT '错误代码',
    `error_message` VARCHAR(255) COMMENT '错误信息',
    `strategy` VARCHAR(64) NOT NULL COMMENT '策略名称',
    `created_time` BIGINT NOT NULL COMMENT '创建时间（毫秒时间戳）',
    `updated_time` BIGINT NOT NULL COMMENT '更新时间（毫秒时间戳）',
    `executed_time` BIGINT COMMENT '执行时间（毫秒时间戳）',
    `is_deleted` TINYINT(1) DEFAULT 0 COMMENT '是否删除 0:否 1:是',
    PRIMARY KEY (`id`),
    UNIQUE INDEX `idx_order_id` (`order_id`),
    UNIQUE INDEX `idx_client_order_id` (`client_order_id`),
    INDEX `idx_signal_id` (`signal_id`),
    INDEX `idx_symbol` (`symbol`),
    INDEX `idx_strategy` (`strategy`),
    INDEX `idx_status` (`status`),
    INDEX `idx_created_time` (`created_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单表';

-- 创建交易信号表
CREATE TABLE IF NOT EXISTS `t_trading_signal` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `signal_id` VARCHAR(64) NOT NULL COMMENT '信号唯一ID',
    `symbol` VARCHAR(20) NOT NULL COMMENT '交易对',
    `strategy` VARCHAR(64) NOT NULL COMMENT '策略名称',
    `signal_type` VARCHAR(20) NOT NULL COMMENT '信号类型 BUY/SELL/HOLD等',
    `confidence` DECIMAL(5,4) NOT NULL COMMENT '信号置信度 0-1',
    `price` DECIMAL(20,8) COMMENT '目标价格',
    `quantity` DECIMAL(20,8) COMMENT '建议交易数量',
    `stop_loss` DECIMAL(20,8) COMMENT '止损价格',
    `take_profit` DECIMAL(20,8) COMMENT '止盈价格',
    `order_type` VARCHAR(20) NOT NULL COMMENT '订单类型',
    `time_in_force` VARCHAR(10) DEFAULT 'GTC' COMMENT '订单有效期',
    `leverage_level` INT COMMENT '杠杆倍数',
    `additional_params` TEXT COMMENT '额外参数（JSON格式）',
    `processed` TINYINT(1) DEFAULT 0 COMMENT '是否已处理 0:否 1:是',
    `processed_time` BIGINT COMMENT '处理时间（毫秒时间戳）',
    `process_result` VARCHAR(255) COMMENT '处理结果',
    `generated_time` BIGINT NOT NULL COMMENT '信号生成时间（毫秒时间戳）',
    `created_time` BIGINT NOT NULL COMMENT '创建时间（毫秒时间戳）',
    `is_deleted` TINYINT(1) DEFAULT 0 COMMENT '是否删除 0:否 1:是',
    PRIMARY KEY (`id`),
    UNIQUE INDEX `idx_signal_id` (`signal_id`),
    INDEX `idx_symbol` (`symbol`),
    INDEX `idx_strategy` (`strategy`),
    INDEX `idx_signal_type` (`signal_type`),
    INDEX `idx_processed` (`processed`),
    INDEX `idx_generated_time` (`generated_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='交易信号表';

-- 创建订单执行日志表
CREATE TABLE IF NOT EXISTS `t_order_execution_log` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `execution_id` VARCHAR(64) NOT NULL COMMENT '执行ID',
    `order_id` VARCHAR(64) NOT NULL COMMENT '订单ID',
    `client_order_id` VARCHAR(64) NOT NULL COMMENT '客户端订单ID',
    `action` VARCHAR(20) NOT NULL COMMENT '执行动作 CREATE/CANCEL/QUERY等',
    `request_data` TEXT COMMENT '请求数据（JSON格式）',
    `response_data` TEXT COMMENT '响应数据（JSON格式）',
    `success` TINYINT(1) NOT NULL COMMENT '是否成功 0:否 1:是',
    `error_code` VARCHAR(64) COMMENT '错误代码',
    `error_message` VARCHAR(255) COMMENT '错误信息',
    `execution_time` BIGINT NOT NULL COMMENT '执行时间（毫秒时间戳）',
    `created_time` BIGINT NOT NULL COMMENT '创建时间（毫秒时间戳）',
    PRIMARY KEY (`id`),
    UNIQUE INDEX `idx_execution_id` (`execution_id`),
    INDEX `idx_order_id` (`order_id`),
    INDEX `idx_client_order_id` (`client_order_id`),
    INDEX `idx_action` (`action`),
    INDEX `idx_execution_time` (`execution_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单执行日志表';

-- 创建风控日志表
CREATE TABLE IF NOT EXISTS `t_risk_control_log` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `signal_id` VARCHAR(64) COMMENT '交易信号ID',
    `order_id` VARCHAR(64) COMMENT '订单ID',
    `rule_id` VARCHAR(64) NOT NULL COMMENT '风控规则ID',
    `rule_name` VARCHAR(64) NOT NULL COMMENT '风控规则名称',
    `risk_type` VARCHAR(64) NOT NULL COMMENT '风险类型',
    `risk_level` VARCHAR(20) NOT NULL COMMENT '风险等级 LOW/MEDIUM/HIGH',
    `triggered` TINYINT(1) NOT NULL COMMENT '是否触发 0:否 1:是',
    `action_taken` VARCHAR(64) COMMENT '采取的行动',
    `details` TEXT COMMENT '风控详细信息（JSON格式）',
    `triggered_time` BIGINT NOT NULL COMMENT '触发时间（毫秒时间戳）',
    `created_time` BIGINT NOT NULL COMMENT '创建时间（毫秒时间戳）',
    PRIMARY KEY (`id`),
    INDEX `idx_signal_id` (`signal_id`),
    INDEX `idx_order_id` (`order_id`),
    INDEX `idx_rule_id` (`rule_id`),
    INDEX `idx_risk_type` (`risk_type`),
    INDEX `idx_triggered` (`triggered`),
    INDEX `idx_triggered_time` (`triggered_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='风控日志表';

-- 创建API请求日志表（用于跟踪API限速）
CREATE TABLE IF NOT EXISTS `t_api_request_log` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `endpoint` VARCHAR(255) NOT NULL COMMENT 'API端点',
    `method` VARCHAR(10) NOT NULL COMMENT '请求方法 GET/POST/DELETE等',
    `weight` INT NOT NULL DEFAULT 1 COMMENT 'API权重',
    `request_time` BIGINT NOT NULL COMMENT '请求时间（毫秒时间戳）',
    `response_time` BIGINT COMMENT '响应时间（毫秒时间戳）',
    `time_cost` INT COMMENT '耗时（毫秒）',
    `ip_limited` TINYINT(1) DEFAULT 0 COMMENT '是否IP限制 0:否 1:是',
    `weight_limited` TINYINT(1) DEFAULT 0 COMMENT '是否权重限制 0:否 1:是',
    `order_limited` TINYINT(1) DEFAULT 0 COMMENT '是否订单限制 0:否 1:是',
    `headers` TEXT COMMENT '响应头信息（JSON格式）',
    `created_time` BIGINT NOT NULL COMMENT '创建时间（毫秒时间戳）',
    PRIMARY KEY (`id`),
    INDEX `idx_endpoint` (`endpoint`),
    INDEX `idx_method` (`method`),
    INDEX `idx_request_time` (`request_time`),
    INDEX `idx_ip_limited` (`ip_limited`),
    INDEX `idx_weight_limited` (`weight_limited`),
    INDEX `idx_order_limited` (`order_limited`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='API请求日志表';

-- 创建K线数据表
CREATE TABLE IF NOT EXISTS `t_kline_data` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `symbol` VARCHAR(20) NOT NULL COMMENT '交易对',
    `interval` VARCHAR(10) NOT NULL COMMENT 'K线间隔 1m/3m/5m/15m/30m/1h/2h/4h/6h/8h/12h/1d/3d/1w/1M',
    `open_time` BIGINT NOT NULL COMMENT '开盘时间（毫秒时间戳）',
    `close_time` BIGINT NOT NULL COMMENT '收盘时间（毫秒时间戳）',
    `open` DECIMAL(20,8) NOT NULL COMMENT '开盘价',
    `high` DECIMAL(20,8) NOT NULL COMMENT '最高价',
    `low` DECIMAL(20,8) NOT NULL COMMENT '最低价',
    `close` DECIMAL(20,8) NOT NULL COMMENT '收盘价',
    `volume` DECIMAL(20,8) NOT NULL COMMENT '成交量',
    `quote_asset_volume` DECIMAL(20,8) NOT NULL COMMENT '成交额',
    `number_of_trades` BIGINT NOT NULL COMMENT '成交笔数',
    `taker_buy_base_asset_volume` DECIMAL(20,8) NOT NULL COMMENT '主动买入成交量',
    `taker_buy_quote_asset_volume` DECIMAL(20,8) NOT NULL COMMENT '主动买入成交额',
    `is_closed` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '是否完成 0:否 1:是',
    `created_time` BIGINT NOT NULL COMMENT '创建时间（毫秒时间戳）',
    PRIMARY KEY (`id`),
    UNIQUE INDEX `idx_symbol_interval_open_time` (`symbol`, `interval`, `open_time`),
    INDEX `idx_symbol` (`symbol`),
    INDEX `idx_interval` (`interval`),
    INDEX `idx_open_time` (`open_time`),
    INDEX `idx_close_time` (`close_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='K线数据表';

-- 创建深度数据表
CREATE TABLE IF NOT EXISTS `t_depth_data` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `symbol` VARCHAR(20) NOT NULL COMMENT '交易对',
    `last_update_id` BIGINT NOT NULL COMMENT '最后更新ID',
    `depth_limit` INT NOT NULL COMMENT '深度级别 5/10/20/50/100/500/1000',
    `update_time` BIGINT NOT NULL COMMENT '更新时间（毫秒时间戳）',
    `created_time` BIGINT NOT NULL COMMENT '创建时间（毫秒时间戳）',
    PRIMARY KEY (`id`),
    UNIQUE INDEX `idx_symbol_update_id` (`symbol`, `last_update_id`),
    INDEX `idx_symbol` (`symbol`),
    INDEX `idx_update_time` (`update_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='深度数据表';

-- 创建深度价格数量表
CREATE TABLE IF NOT EXISTS `t_depth_price_quantity` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `depth_id` BIGINT NOT NULL COMMENT '关联的深度数据ID',
    `price` DECIMAL(20,8) NOT NULL COMMENT '价格',
    `quantity` DECIMAL(20,8) NOT NULL COMMENT '数量',
    `side` VARCHAR(4) NOT NULL COMMENT '买卖方向 BID/ASK',
    `created_time` BIGINT NOT NULL COMMENT '创建时间（毫秒时间戳）',
    PRIMARY KEY (`id`),
    INDEX `idx_depth_id` (`depth_id`),
    INDEX `idx_side` (`side`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='深度价格数量表';

-- 创建交易数据表
CREATE TABLE IF NOT EXISTS `t_trade_data` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `trade_id` BIGINT NOT NULL COMMENT '交易ID',
    `symbol` VARCHAR(20) NOT NULL COMMENT '交易对',
    `price` DECIMAL(20,8) NOT NULL COMMENT '价格',
    `quantity` DECIMAL(20,8) NOT NULL COMMENT '数量',
    `quote_quantity` DECIMAL(20,8) NOT NULL COMMENT '成交额',
    `trade_time` BIGINT NOT NULL COMMENT '交易时间（毫秒时间戳）',
    `is_buyer_maker` TINYINT(1) NOT NULL COMMENT '买方是否是做市商 0:否 1:是',
    `is_best_match` TINYINT(1) NOT NULL COMMENT '是否是最优价格匹配 0:否 1:是',
    `created_time` BIGINT NOT NULL COMMENT '创建时间（毫秒时间戳）',
    PRIMARY KEY (`id`),
    UNIQUE INDEX `idx_trade_id_symbol` (`trade_id`, `symbol`),
    INDEX `idx_symbol` (`symbol`),
    INDEX `idx_trade_time` (`trade_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='交易数据表';