package com.crypto.trading.sdk.interceptor;

import lombok.extern.slf4j.Slf4j;
import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.Response;

import java.io.IOException;

/**
 * @name: RetryInterceptor
 * @author: zeek
 * @description: 一个OkHttp拦截器，用于在API请求失败时自动重试。
 *               它会检查响应的状态码，如果是一个可重试的错误（例如，5xx服务器错误），
 *               它将尝试重新发送请求，直到达到最大重试次数。
 *               这有助于提高系统在面对临时的网络或服务器问题时的健壮性。
 * @create: 2024-07-16 02:05
 */
@Slf4j
public class RetryInterceptor implements Interceptor {

    private final int maxRetries;
    private int attempt = 0;

    public RetryInterceptor(int maxRetries) {
        this.maxRetries = maxRetries;
    }

    @Override
    public Response intercept(Chain chain) throws IOException {
        Request request = chain.request();
        Response response = null;
        IOException exception = null;

        while (attempt < maxRetries) {
            try {
                response = chain.proceed(request);
                if (response.isSuccessful()) {
                    return response;
                }
            } catch (IOException e) {
                exception = e;
                log.warn("请求失败，准备重试 [第{}次], URL: {}, 错误: {}", attempt + 1, request.url(), e.getMessage());
            }

            attempt++;
            log.info("请求不成功，准备重试 [第{}次], URL: {}, 状态码: {}", attempt, request.url(), response != null ? response.code() : "N/A");

            try {
                // 退避策略：简单地等待一段时间再重试
                Thread.sleep(1000 * attempt);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new IOException("重试等待时被中断", e);
            }
        }

        if (response != null) {
            return response;
        } else {
            throw new IOException("在达到最大重试次数后仍然无法执行请求", exception);
        }
    }
}
