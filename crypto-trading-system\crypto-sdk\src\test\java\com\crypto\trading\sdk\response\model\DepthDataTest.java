package com.crypto.trading.sdk.response.model;

import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 深度数据模型测试类
 */
public class DepthDataTest {

    @Test
    public void testDefaultConstructor() {
        // 测试默认构造函数
        DepthData depthData = new DepthData();
        
        // 验证默认值
        assertNull(depthData.getLastUpdateId());
        assertNull(depthData.getBids());
        assertNull(depthData.getAsks());
        assertNull(depthData.getEventTime());
    }

    @Test
    public void testConstructorWithParameters() {
        // 测试带参数的构造函数
        Long lastUpdateId = 123456789L;
        List<DepthData.PriceQuantity> bids = new ArrayList<>();
        List<DepthData.PriceQuantity> asks = new ArrayList<>();
        Long eventTime = 1618560000000L;
        
        DepthData depthData = new DepthData(lastUpdateId, bids, asks, eventTime);
        
        // 验证参数
        assertEquals(lastUpdateId, depthData.getLastUpdateId());
        assertSame(bids, depthData.getBids());
        assertSame(asks, depthData.getAsks());
        assertEquals(eventTime, depthData.getEventTime());
    }

    @Test
    public void testSettersAndGetters() {
        // 测试设置器和获取器
        DepthData depthData = new DepthData();
        
        // 创建测试数据
        Long lastUpdateId = 123456789L;
        List<DepthData.PriceQuantity> bids = new ArrayList<>();
        List<DepthData.PriceQuantity> asks = new ArrayList<>();
        Long eventTime = 1618560000000L;
        
        // 设置属性
        depthData.setLastUpdateId(lastUpdateId);
        depthData.setBids(bids);
        depthData.setAsks(asks);
        depthData.setEventTime(eventTime);
        
        // 验证属性
        assertEquals(lastUpdateId, depthData.getLastUpdateId());
        assertSame(bids, depthData.getBids());
        assertSame(asks, depthData.getAsks());
        assertEquals(eventTime, depthData.getEventTime());
    }

    @Test
    public void testToString() {
        // 测试toString方法
        DepthData depthData = new DepthData();
        depthData.setLastUpdateId(123456789L);
        
        String toString = depthData.toString();
        assertNotNull(toString);
        assertTrue(toString.contains("123456789"));
    }
    
    // 测试PriceQuantity内部类
    @Test
    public void testPriceQuantityDefaultConstructor() {
        // 测试默认构造函数
        DepthData.PriceQuantity priceQuantity = new DepthData.PriceQuantity();
        
        // 验证默认值
        assertNull(priceQuantity.getPrice());
        assertNull(priceQuantity.getQuantity());
    }
    
    @Test
    public void testPriceQuantityConstructorWithParameters() {
        // 测试带参数的构造函数
        BigDecimal price = new BigDecimal("54321.12");
        BigDecimal quantity = new BigDecimal("1.23456");
        
        DepthData.PriceQuantity priceQuantity = new DepthData.PriceQuantity(price, quantity);
        
        // 验证参数
        assertEquals(price, priceQuantity.getPrice());
        assertEquals(quantity, priceQuantity.getQuantity());
    }
    
    @Test
    public void testPriceQuantityFromArray() {
        // 测试从数组创建价格和数量对
        Object[] array = new Object[]{"54321.12", "1.23456"};
        
        DepthData.PriceQuantity priceQuantity = DepthData.PriceQuantity.fromArray(array);
        
        // 验证数据
        assertEquals(new BigDecimal("54321.12"), priceQuantity.getPrice());
        assertEquals(new BigDecimal("1.23456"), priceQuantity.getQuantity());
    }
    
    @Test
    public void testPriceQuantityFromArrayInvalidInput() {
        // 测试无效输入
        Object[] invalidArray = new Object[]{"54321.12"};
        
        // 应该抛出异常
        assertThrows(IllegalArgumentException.class, () -> {
            DepthData.PriceQuantity.fromArray(invalidArray);
        });
    }
    
    @Test
    public void testPriceQuantitySettersAndGetters() {
        // 测试设置器和获取器
        DepthData.PriceQuantity priceQuantity = new DepthData.PriceQuantity();
        
        // 设置属性
        BigDecimal price = new BigDecimal("54321.12");
        BigDecimal quantity = new BigDecimal("1.23456");
        priceQuantity.setPrice(price);
        priceQuantity.setQuantity(quantity);
        
        // 验证属性
        assertEquals(price, priceQuantity.getPrice());
        assertEquals(quantity, priceQuantity.getQuantity());
    }
    
    @Test
    public void testPriceQuantityToString() {
        // 测试toString方法
        BigDecimal price = new BigDecimal("54321.12");
        BigDecimal quantity = new BigDecimal("1.23456");
        DepthData.PriceQuantity priceQuantity = new DepthData.PriceQuantity(price, quantity);
        
        String toString = priceQuantity.toString();
        assertNotNull(toString);
        assertTrue(toString.contains("54321.12"));
        assertTrue(toString.contains("1.23456"));
    }
} 