package com.crypto.trading.sdk.converter;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * JSON转换器测试类
 */
public class JsonConverterTest {

    private JsonConverter jsonConverter;

    @BeforeEach
    public void setUp() {
        jsonConverter = new JacksonJsonConverter();
    }

    @Test
    public void testFromJson() {
        // 创建测试数据
        String json = "{\"name\":\"BTC\",\"price\":50000.0,\"active\":true}";
        
        // 转换为Map
        TestObject result = jsonConverter.fromJson(json, TestObject.class);
        
        // 验证结果
        assertNotNull(result);
        assertEquals("BTC", result.getName());
        assertEquals(50000.0, result.getPrice());
        assertTrue(result.isActive());
    }

    @Test
    public void testFromJsonArray() {
        // 创建测试数据
        String json = "[{\"name\":\"BTC\",\"price\":50000.0,\"active\":true},{\"name\":\"ETH\",\"price\":3000.0,\"active\":true}]";
        
        // 转换为List
        List<TestObject> result = jsonConverter.fromJsonArray(json, TestObject.class);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("BTC", result.get(0).getName());
        assertEquals(50000.0, result.get(0).getPrice());
        assertTrue(result.get(0).isActive());
        assertEquals("ETH", result.get(1).getName());
        assertEquals(3000.0, result.get(1).getPrice());
        assertTrue(result.get(1).isActive());
    }

    @Test
    public void testToJson() {
        // 创建测试数据
        TestObject testObject = new TestObject();
        testObject.setName("BTC");
        testObject.setPrice(50000.0);
        testObject.setActive(true);
        
        // 转换为JSON
        String result = jsonConverter.toJson(testObject);
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.contains("\"name\":\"BTC\""));
        assertTrue(result.contains("\"price\":50000.0"));
        assertTrue(result.contains("\"active\":true"));
    }

    @Test
    public void testJsonToMap() {
        // 创建测试数据
        String json = "{\"name\":\"BTC\",\"price\":50000.0,\"active\":true}";
        
        // 转换为Map
        Map<String, Object> result = jsonConverter.jsonToMap(json);
        
        // 验证结果
        assertNotNull(result);
        assertEquals("BTC", result.get("name"));
        assertEquals(50000.0, result.get("price"));
        assertEquals(true, result.get("active"));
    }

    /**
     * 测试对象
     */
    public static class TestObject {
        private String name;
        private double price;
        private boolean active;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public double getPrice() {
            return price;
        }

        public void setPrice(double price) {
            this.price = price;
        }

        public boolean isActive() {
            return active;
        }

        public void setActive(boolean active) {
            this.active = active;
        }
    }
} 