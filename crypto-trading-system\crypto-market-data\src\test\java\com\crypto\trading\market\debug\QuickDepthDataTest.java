package com.crypto.trading.market.debug;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.crypto.trading.sdk.response.model.DepthData;
import com.crypto.trading.sdk.response.model.WebSocketMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 快速深度数据测试
 * 验证消息解析逻辑是否正确
 */
public class QuickDepthDataTest {

    private static final Logger log = LoggerFactory.getLogger(QuickDepthDataTest.class);

    public static void main(String[] args) {
        log.info("开始快速深度数据测试...");

        // 测试真实的币安消息格式
        String realMessage = """
            {
              "e": "depthUpdate",
              "E": 1750503378654,
              "T": 1750503378624,
              "s": "ETHUSDT",
              "U": 53820091771,
              "u": 53820091842,
              "pu": 53820091131,
              "b": [
                ["2437.68", "1.804"],
                ["2437.67", "32.925"],
                ["2437.66", "80.409"],
                ["2437.65", "0.483"],
                ["2437.64", "43.628"]
              ],
              "a": [
                ["2437.82", "1.637"],
                ["2437.91", "2.052"],
                ["2437.92", "0.015"],
                ["2437.93", "2.929"],
                ["2437.94", "3.517"]
              ]
            }
            """;

        testMessageParsing(realMessage);
        testDepthDataCreation(realMessage);

        log.info("快速深度数据测试完成");
    }

    private static void testMessageParsing(String message) {
        log.info("=== 测试消息解析 ===");
        
        try {
            // 解析消息
            JSONObject jsonObject = JSON.parseObject(message);
            log.info("✅ JSON解析成功");
            
            // 检查字段
            log.info("消息类型: {}", jsonObject.getString("e"));
            log.info("交易对: {}", jsonObject.getString("s"));
            
            // 检查深度字段（修复后的逻辑）
            boolean hasU = jsonObject.containsKey("u");
            boolean hasLastUpdateId = jsonObject.containsKey("lastUpdateId");
            log.info("包含'u'字段: {}", hasU);
            log.info("包含'lastUpdateId'字段: {}", hasLastUpdateId);
            
            // 新的检查逻辑
            boolean passesCheck = hasU || hasLastUpdateId;
            log.info("深度字段检查: {}", passesCheck ? "✅ 通过" : "❌ 失败");
            
            if (passesCheck) {
                Long lastUpdateId = hasU ? jsonObject.getLongValue("u") : jsonObject.getLongValue("lastUpdateId");
                log.info("lastUpdateId: {}", lastUpdateId);
                
                JSONArray bidsArray = jsonObject.getJSONArray("b");
                JSONArray asksArray = jsonObject.getJSONArray("a");
                
                if (bidsArray == null) {
                    bidsArray = jsonObject.getJSONArray("bids");
                }
                if (asksArray == null) {
                    asksArray = jsonObject.getJSONArray("asks");
                }
                
                log.info("买盘数量: {}", bidsArray != null ? bidsArray.size() : 0);
                log.info("卖盘数量: {}", asksArray != null ? asksArray.size() : 0);
                
                if (bidsArray != null && bidsArray.size() > 0) {
                    JSONArray firstBid = bidsArray.getJSONArray(0);
                    log.info("第一档买盘: 价格={}, 数量={}", firstBid.getString(0), firstBid.getString(1));
                }
                
                if (asksArray != null && asksArray.size() > 0) {
                    JSONArray firstAsk = asksArray.getJSONArray(0);
                    log.info("第一档卖盘: 价格={}, 数量={}", firstAsk.getString(0), firstAsk.getString(1));
                }
            }
            
        } catch (Exception e) {
            log.error("❌ 消息解析失败", e);
        }
    }

    private static void testDepthDataCreation(String message) {
        log.info("=== 测试深度数据对象创建 ===");
        
        try {
            JSONObject jsonObject = JSON.parseObject(message);
            String symbol = jsonObject.getString("s");
            
            // 检查深度字段
            if (!jsonObject.containsKey("u") && !jsonObject.containsKey("lastUpdateId")) {
                log.warn("消息不包含深度数据字段，跳过处理");
                return;
            }
            
            // 创建WebSocketMessage包装器
            WebSocketMessage<DepthData> webSocketMessage = new WebSocketMessage<>();
            webSocketMessage.setSymbol(symbol.toUpperCase());
            webSocketMessage.setEventType("depth");
            webSocketMessage.setEventTime(System.currentTimeMillis());
            
            // 构建DepthData对象
            DepthData depthData = new DepthData();
            Long lastUpdateId = jsonObject.containsKey("u") ? 
                jsonObject.getLongValue("u") : jsonObject.getLongValue("lastUpdateId");
            depthData.setLastUpdateId(lastUpdateId);
            
            // 解析买单和卖单列表
            JSONArray bidsArray = jsonObject.getJSONArray("b");
            JSONArray asksArray = jsonObject.getJSONArray("a");
            
            if (bidsArray == null) {
                bidsArray = jsonObject.getJSONArray("bids");
            }
            if (asksArray == null) {
                asksArray = jsonObject.getJSONArray("asks");
            }
            
            List<DepthData.PriceQuantity> bids = parsePriceQuantities(bidsArray);
            List<DepthData.PriceQuantity> asks = parsePriceQuantities(asksArray);
            
            depthData.setBids(bids);
            depthData.setAsks(asks);
            depthData.setEventTime(System.currentTimeMillis());
            
            // 设置数据
            webSocketMessage.setData(depthData);
            
            log.info("✅ 深度数据对象创建成功");
            log.info("交易对: {}", webSocketMessage.getSymbol());
            log.info("lastUpdateId: {}", depthData.getLastUpdateId());
            log.info("买盘数量: {}", bids.size());
            log.info("卖盘数量: {}", asks.size());
            
            if (!bids.isEmpty()) {
                DepthData.PriceQuantity firstBid = bids.get(0);
                log.info("第一档买盘: 价格={}, 数量={}", firstBid.getPrice(), firstBid.getQuantity());
            }
            
            if (!asks.isEmpty()) {
                DepthData.PriceQuantity firstAsk = asks.get(0);
                log.info("第一档卖盘: 价格={}, 数量={}", firstAsk.getPrice(), firstAsk.getQuantity());
            }
            
        } catch (Exception e) {
            log.error("❌ 深度数据对象创建失败", e);
        }
    }

    private static List<DepthData.PriceQuantity> parsePriceQuantities(JSONArray jsonArray) {
        if (jsonArray == null) {
            return new ArrayList<>();
        }
        
        List<DepthData.PriceQuantity> priceQuantities = new ArrayList<>(jsonArray.size());
        
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONArray item = jsonArray.getJSONArray(i);
            BigDecimal price = new BigDecimal(item.getString(0));
            BigDecimal quantity = new BigDecimal(item.getString(1));
            priceQuantities.add(new DepthData.PriceQuantity(price, quantity));
        }
        
        return priceQuantities;
    }
}
