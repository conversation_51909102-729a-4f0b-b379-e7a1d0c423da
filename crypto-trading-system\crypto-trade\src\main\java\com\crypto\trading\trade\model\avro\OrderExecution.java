/**
 * Autogenerated by Avro
 *
 * DO NOT EDIT DIRECTLY
 */
package com.crypto.trading.trade.model.avro;

import org.apache.avro.generic.GenericArray;
import org.apache.avro.specific.SpecificData;
import org.apache.avro.util.Utf8;
import org.apache.avro.message.BinaryMessageEncoder;
import org.apache.avro.message.BinaryMessageDecoder;
import org.apache.avro.message.SchemaStore;

/** 订单执行信息，包含订单的执行结果 */
@org.apache.avro.specific.AvroGenerated
public class OrderExecution extends org.apache.avro.specific.SpecificRecordBase implements org.apache.avro.specific.SpecificRecord {
  private static final long serialVersionUID = -5878579629714316209L;


  public static final org.apache.avro.Schema SCHEMA$ = new org.apache.avro.Schema.Parser().parse("{\"type\":\"record\",\"name\":\"OrderExecution\",\"namespace\":\"com.crypto.trading.trade.model.avro\",\"doc\":\"订单执行信息，包含订单的执行结果\",\"fields\":[{\"name\":\"id\",\"type\":{\"type\":\"string\",\"avro.java.string\":\"String\"},\"doc\":\"订单执行唯一ID\"},{\"name\":\"signalId\",\"type\":{\"type\":\"string\",\"avro.java.string\":\"String\"},\"doc\":\"关联的交易信号ID\"},{\"name\":\"orderId\",\"type\":{\"type\":\"string\",\"avro.java.string\":\"String\"},\"doc\":\"交易所订单ID\"},{\"name\":\"clientOrderId\",\"type\":{\"type\":\"string\",\"avro.java.string\":\"String\"},\"doc\":\"客户端订单ID\"},{\"name\":\"symbol\",\"type\":{\"type\":\"string\",\"avro.java.string\":\"String\"},\"doc\":\"交易对，如BTCUSDT\"},{\"name\":\"strategy\",\"type\":{\"type\":\"string\",\"avro.java.string\":\"String\"},\"doc\":\"策略名称\"},{\"name\":\"orderType\",\"type\":{\"type\":\"enum\",\"name\":\"OrderType\",\"symbols\":[\"MARKET\",\"LIMIT\",\"STOP\",\"STOP_MARKET\",\"TAKE_PROFIT\",\"TAKE_PROFIT_MARKET\"]},\"doc\":\"订单类型\"},{\"name\":\"side\",\"type\":{\"type\":\"enum\",\"name\":\"OrderSide\",\"symbols\":[\"BUY\",\"SELL\"]},\"doc\":\"订单方向\"},{\"name\":\"positionSide\",\"type\":{\"type\":\"enum\",\"name\":\"PositionSide\",\"symbols\":[\"BOTH\",\"LONG\",\"SHORT\"]},\"doc\":\"仓位方向\",\"default\":\"BOTH\"},{\"name\":\"quantity\",\"type\":\"double\",\"doc\":\"交易数量\"},{\"name\":\"price\",\"type\":[\"null\",\"double\"],\"doc\":\"订单价格，市价单为null\",\"default\":null},{\"name\":\"executedPrice\",\"type\":[\"null\",\"double\"],\"doc\":\"执行价格\",\"default\":null},{\"name\":\"status\",\"type\":{\"type\":\"enum\",\"name\":\"OrderStatus\",\"symbols\":[\"NEW\",\"PARTIALLY_FILLED\",\"FILLED\",\"CANCELED\",\"REJECTED\",\"EXPIRED\"]},\"doc\":\"订单状态\"},{\"name\":\"createdTime\",\"type\":\"long\",\"doc\":\"订单创建时间（毫秒时间戳）\"},{\"name\":\"executedTime\",\"type\":[\"null\",\"long\"],\"doc\":\"订单执行时间（毫秒时间戳）\",\"default\":null},{\"name\":\"errorCode\",\"type\":[\"null\",{\"type\":\"string\",\"avro.java.string\":\"String\"}],\"doc\":\"错误代码，如果有\",\"default\":null},{\"name\":\"errorMessage\",\"type\":[\"null\",{\"type\":\"string\",\"avro.java.string\":\"String\"}],\"doc\":\"错误信息，如果有\",\"default\":null}]}");
  public static org.apache.avro.Schema getClassSchema() { return SCHEMA$; }

  private static final SpecificData MODEL$ = new SpecificData();

  private static final BinaryMessageEncoder<OrderExecution> ENCODER =
      new BinaryMessageEncoder<>(MODEL$, SCHEMA$);

  private static final BinaryMessageDecoder<OrderExecution> DECODER =
      new BinaryMessageDecoder<>(MODEL$, SCHEMA$);

  /**
   * Return the BinaryMessageEncoder instance used by this class.
   * @return the message encoder used by this class
   */
  public static BinaryMessageEncoder<OrderExecution> getEncoder() {
    return ENCODER;
  }

  /**
   * Return the BinaryMessageDecoder instance used by this class.
   * @return the message decoder used by this class
   */
  public static BinaryMessageDecoder<OrderExecution> getDecoder() {
    return DECODER;
  }

  /**
   * Create a new BinaryMessageDecoder instance for this class that uses the specified {@link SchemaStore}.
   * @param resolver a {@link SchemaStore} used to find schemas by fingerprint
   * @return a BinaryMessageDecoder instance for this class backed by the given SchemaStore
   */
  public static BinaryMessageDecoder<OrderExecution> createDecoder(SchemaStore resolver) {
    return new BinaryMessageDecoder<>(MODEL$, SCHEMA$, resolver);
  }

  /**
   * Serializes this OrderExecution to a ByteBuffer.
   * @return a buffer holding the serialized data for this instance
   * @throws java.io.IOException if this instance could not be serialized
   */
  public java.nio.ByteBuffer toByteBuffer() throws java.io.IOException {
    return ENCODER.encode(this);
  }

  /**
   * Deserializes a OrderExecution from a ByteBuffer.
   * @param b a byte buffer holding serialized data for an instance of this class
   * @return a OrderExecution instance decoded from the given buffer
   * @throws java.io.IOException if the given bytes could not be deserialized into an instance of this class
   */
  public static OrderExecution fromByteBuffer(
      java.nio.ByteBuffer b) throws java.io.IOException {
    return DECODER.decode(b);
  }

  /** 订单执行唯一ID */
  private java.lang.String id;
  /** 关联的交易信号ID */
  private java.lang.String signalId;
  /** 交易所订单ID */
  private java.lang.String orderId;
  /** 客户端订单ID */
  private java.lang.String clientOrderId;
  /** 交易对，如BTCUSDT */
  private java.lang.String symbol;
  /** 策略名称 */
  private java.lang.String strategy;
  /** 订单类型 */
  private com.crypto.trading.trade.model.avro.OrderType orderType;
  /** 订单方向 */
  private com.crypto.trading.trade.model.avro.OrderSide side;
  /** 仓位方向 */
  private com.crypto.trading.trade.model.avro.PositionSide positionSide;
  /** 交易数量 */
  private double quantity;
  /** 订单价格，市价单为null */
  private java.lang.Double price;
  /** 执行价格 */
  private java.lang.Double executedPrice;
  /** 订单状态 */
  private com.crypto.trading.trade.model.avro.OrderStatus status;
  /** 订单创建时间（毫秒时间戳） */
  private long createdTime;
  /** 订单执行时间（毫秒时间戳） */
  private java.lang.Long executedTime;
  /** 错误代码，如果有 */
  private java.lang.String errorCode;
  /** 错误信息，如果有 */
  private java.lang.String errorMessage;

  /**
   * Default constructor.  Note that this does not initialize fields
   * to their default values from the schema.  If that is desired then
   * one should use <code>newBuilder()</code>.
   */
  public OrderExecution() {}

  /**
   * All-args constructor.
   * @param id 订单执行唯一ID
   * @param signalId 关联的交易信号ID
   * @param orderId 交易所订单ID
   * @param clientOrderId 客户端订单ID
   * @param symbol 交易对，如BTCUSDT
   * @param strategy 策略名称
   * @param orderType 订单类型
   * @param side 订单方向
   * @param positionSide 仓位方向
   * @param quantity 交易数量
   * @param price 订单价格，市价单为null
   * @param executedPrice 执行价格
   * @param status 订单状态
   * @param createdTime 订单创建时间（毫秒时间戳）
   * @param executedTime 订单执行时间（毫秒时间戳）
   * @param errorCode 错误代码，如果有
   * @param errorMessage 错误信息，如果有
   */
  public OrderExecution(java.lang.String id, java.lang.String signalId, java.lang.String orderId, java.lang.String clientOrderId, java.lang.String symbol, java.lang.String strategy, com.crypto.trading.trade.model.avro.OrderType orderType, com.crypto.trading.trade.model.avro.OrderSide side, com.crypto.trading.trade.model.avro.PositionSide positionSide, java.lang.Double quantity, java.lang.Double price, java.lang.Double executedPrice, com.crypto.trading.trade.model.avro.OrderStatus status, java.lang.Long createdTime, java.lang.Long executedTime, java.lang.String errorCode, java.lang.String errorMessage) {
    this.id = id;
    this.signalId = signalId;
    this.orderId = orderId;
    this.clientOrderId = clientOrderId;
    this.symbol = symbol;
    this.strategy = strategy;
    this.orderType = orderType;
    this.side = side;
    this.positionSide = positionSide;
    this.quantity = quantity;
    this.price = price;
    this.executedPrice = executedPrice;
    this.status = status;
    this.createdTime = createdTime;
    this.executedTime = executedTime;
    this.errorCode = errorCode;
    this.errorMessage = errorMessage;
  }

  @Override
  public org.apache.avro.specific.SpecificData getSpecificData() { return MODEL$; }

  @Override
  public org.apache.avro.Schema getSchema() { return SCHEMA$; }

  // Used by DatumWriter.  Applications should not call.
  @Override
  public java.lang.Object get(int field$) {
    switch (field$) {
    case 0: return id;
    case 1: return signalId;
    case 2: return orderId;
    case 3: return clientOrderId;
    case 4: return symbol;
    case 5: return strategy;
    case 6: return orderType;
    case 7: return side;
    case 8: return positionSide;
    case 9: return quantity;
    case 10: return price;
    case 11: return executedPrice;
    case 12: return status;
    case 13: return createdTime;
    case 14: return executedTime;
    case 15: return errorCode;
    case 16: return errorMessage;
    default: throw new IndexOutOfBoundsException("Invalid index: " + field$);
    }
  }

  // Used by DatumReader.  Applications should not call.
  @Override
  @SuppressWarnings(value="unchecked")
  public void put(int field$, java.lang.Object value$) {
    switch (field$) {
    case 0: id = value$ != null ? value$.toString() : null; break;
    case 1: signalId = value$ != null ? value$.toString() : null; break;
    case 2: orderId = value$ != null ? value$.toString() : null; break;
    case 3: clientOrderId = value$ != null ? value$.toString() : null; break;
    case 4: symbol = value$ != null ? value$.toString() : null; break;
    case 5: strategy = value$ != null ? value$.toString() : null; break;
    case 6: orderType = (com.crypto.trading.trade.model.avro.OrderType)value$; break;
    case 7: side = (com.crypto.trading.trade.model.avro.OrderSide)value$; break;
    case 8: positionSide = (com.crypto.trading.trade.model.avro.PositionSide)value$; break;
    case 9: quantity = (java.lang.Double)value$; break;
    case 10: price = (java.lang.Double)value$; break;
    case 11: executedPrice = (java.lang.Double)value$; break;
    case 12: status = (com.crypto.trading.trade.model.avro.OrderStatus)value$; break;
    case 13: createdTime = (java.lang.Long)value$; break;
    case 14: executedTime = (java.lang.Long)value$; break;
    case 15: errorCode = value$ != null ? value$.toString() : null; break;
    case 16: errorMessage = value$ != null ? value$.toString() : null; break;
    default: throw new IndexOutOfBoundsException("Invalid index: " + field$);
    }
  }

  /**
   * Gets the value of the 'id' field.
   * @return 订单执行唯一ID
   */
  public java.lang.String getId() {
    return id;
  }



  /**
   * Gets the value of the 'signalId' field.
   * @return 关联的交易信号ID
   */
  public java.lang.String getSignalId() {
    return signalId;
  }



  /**
   * Gets the value of the 'orderId' field.
   * @return 交易所订单ID
   */
  public java.lang.String getOrderId() {
    return orderId;
  }



  /**
   * Gets the value of the 'clientOrderId' field.
   * @return 客户端订单ID
   */
  public java.lang.String getClientOrderId() {
    return clientOrderId;
  }



  /**
   * Gets the value of the 'symbol' field.
   * @return 交易对，如BTCUSDT
   */
  public java.lang.String getSymbol() {
    return symbol;
  }



  /**
   * Gets the value of the 'strategy' field.
   * @return 策略名称
   */
  public java.lang.String getStrategy() {
    return strategy;
  }



  /**
   * Gets the value of the 'orderType' field.
   * @return 订单类型
   */
  public com.crypto.trading.trade.model.avro.OrderType getOrderType() {
    return orderType;
  }



  /**
   * Gets the value of the 'side' field.
   * @return 订单方向
   */
  public com.crypto.trading.trade.model.avro.OrderSide getSide() {
    return side;
  }



  /**
   * Gets the value of the 'positionSide' field.
   * @return 仓位方向
   */
  public com.crypto.trading.trade.model.avro.PositionSide getPositionSide() {
    return positionSide;
  }



  /**
   * Gets the value of the 'quantity' field.
   * @return 交易数量
   */
  public double getQuantity() {
    return quantity;
  }



  /**
   * Gets the value of the 'price' field.
   * @return 订单价格，市价单为null
   */
  public java.lang.Double getPrice() {
    return price;
  }



  /**
   * Gets the value of the 'executedPrice' field.
   * @return 执行价格
   */
  public java.lang.Double getExecutedPrice() {
    return executedPrice;
  }



  /**
   * Gets the value of the 'status' field.
   * @return 订单状态
   */
  public com.crypto.trading.trade.model.avro.OrderStatus getStatus() {
    return status;
  }



  /**
   * Gets the value of the 'createdTime' field.
   * @return 订单创建时间（毫秒时间戳）
   */
  public long getCreatedTime() {
    return createdTime;
  }



  /**
   * Gets the value of the 'executedTime' field.
   * @return 订单执行时间（毫秒时间戳）
   */
  public java.lang.Long getExecutedTime() {
    return executedTime;
  }



  /**
   * Gets the value of the 'errorCode' field.
   * @return 错误代码，如果有
   */
  public java.lang.String getErrorCode() {
    return errorCode;
  }



  /**
   * Gets the value of the 'errorMessage' field.
   * @return 错误信息，如果有
   */
  public java.lang.String getErrorMessage() {
    return errorMessage;
  }



  /**
   * Creates a new OrderExecution RecordBuilder.
   * @return A new OrderExecution RecordBuilder
   */
  public static com.crypto.trading.trade.model.avro.OrderExecution.Builder newBuilder() {
    return new com.crypto.trading.trade.model.avro.OrderExecution.Builder();
  }

  /**
   * Creates a new OrderExecution RecordBuilder by copying an existing Builder.
   * @param other The existing builder to copy.
   * @return A new OrderExecution RecordBuilder
   */
  public static com.crypto.trading.trade.model.avro.OrderExecution.Builder newBuilder(com.crypto.trading.trade.model.avro.OrderExecution.Builder other) {
    if (other == null) {
      return new com.crypto.trading.trade.model.avro.OrderExecution.Builder();
    } else {
      return new com.crypto.trading.trade.model.avro.OrderExecution.Builder(other);
    }
  }

  /**
   * Creates a new OrderExecution RecordBuilder by copying an existing OrderExecution instance.
   * @param other The existing instance to copy.
   * @return A new OrderExecution RecordBuilder
   */
  public static com.crypto.trading.trade.model.avro.OrderExecution.Builder newBuilder(com.crypto.trading.trade.model.avro.OrderExecution other) {
    if (other == null) {
      return new com.crypto.trading.trade.model.avro.OrderExecution.Builder();
    } else {
      return new com.crypto.trading.trade.model.avro.OrderExecution.Builder(other);
    }
  }

  /**
   * RecordBuilder for OrderExecution instances.
   */
  @org.apache.avro.specific.AvroGenerated
  public static class Builder extends org.apache.avro.specific.SpecificRecordBuilderBase<OrderExecution>
    implements org.apache.avro.data.RecordBuilder<OrderExecution> {

    /** 订单执行唯一ID */
    private java.lang.String id;
    /** 关联的交易信号ID */
    private java.lang.String signalId;
    /** 交易所订单ID */
    private java.lang.String orderId;
    /** 客户端订单ID */
    private java.lang.String clientOrderId;
    /** 交易对，如BTCUSDT */
    private java.lang.String symbol;
    /** 策略名称 */
    private java.lang.String strategy;
    /** 订单类型 */
    private com.crypto.trading.trade.model.avro.OrderType orderType;
    /** 订单方向 */
    private com.crypto.trading.trade.model.avro.OrderSide side;
    /** 仓位方向 */
    private com.crypto.trading.trade.model.avro.PositionSide positionSide;
    /** 交易数量 */
    private double quantity;
    /** 订单价格，市价单为null */
    private java.lang.Double price;
    /** 执行价格 */
    private java.lang.Double executedPrice;
    /** 订单状态 */
    private com.crypto.trading.trade.model.avro.OrderStatus status;
    /** 订单创建时间（毫秒时间戳） */
    private long createdTime;
    /** 订单执行时间（毫秒时间戳） */
    private java.lang.Long executedTime;
    /** 错误代码，如果有 */
    private java.lang.String errorCode;
    /** 错误信息，如果有 */
    private java.lang.String errorMessage;

    /** Creates a new Builder */
    private Builder() {
      super(SCHEMA$, MODEL$);
    }

    /**
     * Creates a Builder by copying an existing Builder.
     * @param other The existing Builder to copy.
     */
    private Builder(com.crypto.trading.trade.model.avro.OrderExecution.Builder other) {
      super(other);
      if (isValidValue(fields()[0], other.id)) {
        this.id = data().deepCopy(fields()[0].schema(), other.id);
        fieldSetFlags()[0] = other.fieldSetFlags()[0];
      }
      if (isValidValue(fields()[1], other.signalId)) {
        this.signalId = data().deepCopy(fields()[1].schema(), other.signalId);
        fieldSetFlags()[1] = other.fieldSetFlags()[1];
      }
      if (isValidValue(fields()[2], other.orderId)) {
        this.orderId = data().deepCopy(fields()[2].schema(), other.orderId);
        fieldSetFlags()[2] = other.fieldSetFlags()[2];
      }
      if (isValidValue(fields()[3], other.clientOrderId)) {
        this.clientOrderId = data().deepCopy(fields()[3].schema(), other.clientOrderId);
        fieldSetFlags()[3] = other.fieldSetFlags()[3];
      }
      if (isValidValue(fields()[4], other.symbol)) {
        this.symbol = data().deepCopy(fields()[4].schema(), other.symbol);
        fieldSetFlags()[4] = other.fieldSetFlags()[4];
      }
      if (isValidValue(fields()[5], other.strategy)) {
        this.strategy = data().deepCopy(fields()[5].schema(), other.strategy);
        fieldSetFlags()[5] = other.fieldSetFlags()[5];
      }
      if (isValidValue(fields()[6], other.orderType)) {
        this.orderType = data().deepCopy(fields()[6].schema(), other.orderType);
        fieldSetFlags()[6] = other.fieldSetFlags()[6];
      }
      if (isValidValue(fields()[7], other.side)) {
        this.side = data().deepCopy(fields()[7].schema(), other.side);
        fieldSetFlags()[7] = other.fieldSetFlags()[7];
      }
      if (isValidValue(fields()[8], other.positionSide)) {
        this.positionSide = data().deepCopy(fields()[8].schema(), other.positionSide);
        fieldSetFlags()[8] = other.fieldSetFlags()[8];
      }
      if (isValidValue(fields()[9], other.quantity)) {
        this.quantity = data().deepCopy(fields()[9].schema(), other.quantity);
        fieldSetFlags()[9] = other.fieldSetFlags()[9];
      }
      if (isValidValue(fields()[10], other.price)) {
        this.price = data().deepCopy(fields()[10].schema(), other.price);
        fieldSetFlags()[10] = other.fieldSetFlags()[10];
      }
      if (isValidValue(fields()[11], other.executedPrice)) {
        this.executedPrice = data().deepCopy(fields()[11].schema(), other.executedPrice);
        fieldSetFlags()[11] = other.fieldSetFlags()[11];
      }
      if (isValidValue(fields()[12], other.status)) {
        this.status = data().deepCopy(fields()[12].schema(), other.status);
        fieldSetFlags()[12] = other.fieldSetFlags()[12];
      }
      if (isValidValue(fields()[13], other.createdTime)) {
        this.createdTime = data().deepCopy(fields()[13].schema(), other.createdTime);
        fieldSetFlags()[13] = other.fieldSetFlags()[13];
      }
      if (isValidValue(fields()[14], other.executedTime)) {
        this.executedTime = data().deepCopy(fields()[14].schema(), other.executedTime);
        fieldSetFlags()[14] = other.fieldSetFlags()[14];
      }
      if (isValidValue(fields()[15], other.errorCode)) {
        this.errorCode = data().deepCopy(fields()[15].schema(), other.errorCode);
        fieldSetFlags()[15] = other.fieldSetFlags()[15];
      }
      if (isValidValue(fields()[16], other.errorMessage)) {
        this.errorMessage = data().deepCopy(fields()[16].schema(), other.errorMessage);
        fieldSetFlags()[16] = other.fieldSetFlags()[16];
      }
    }

    /**
     * Creates a Builder by copying an existing OrderExecution instance
     * @param other The existing instance to copy.
     */
    private Builder(com.crypto.trading.trade.model.avro.OrderExecution other) {
      super(SCHEMA$, MODEL$);
      if (isValidValue(fields()[0], other.id)) {
        this.id = data().deepCopy(fields()[0].schema(), other.id);
        fieldSetFlags()[0] = true;
      }
      if (isValidValue(fields()[1], other.signalId)) {
        this.signalId = data().deepCopy(fields()[1].schema(), other.signalId);
        fieldSetFlags()[1] = true;
      }
      if (isValidValue(fields()[2], other.orderId)) {
        this.orderId = data().deepCopy(fields()[2].schema(), other.orderId);
        fieldSetFlags()[2] = true;
      }
      if (isValidValue(fields()[3], other.clientOrderId)) {
        this.clientOrderId = data().deepCopy(fields()[3].schema(), other.clientOrderId);
        fieldSetFlags()[3] = true;
      }
      if (isValidValue(fields()[4], other.symbol)) {
        this.symbol = data().deepCopy(fields()[4].schema(), other.symbol);
        fieldSetFlags()[4] = true;
      }
      if (isValidValue(fields()[5], other.strategy)) {
        this.strategy = data().deepCopy(fields()[5].schema(), other.strategy);
        fieldSetFlags()[5] = true;
      }
      if (isValidValue(fields()[6], other.orderType)) {
        this.orderType = data().deepCopy(fields()[6].schema(), other.orderType);
        fieldSetFlags()[6] = true;
      }
      if (isValidValue(fields()[7], other.side)) {
        this.side = data().deepCopy(fields()[7].schema(), other.side);
        fieldSetFlags()[7] = true;
      }
      if (isValidValue(fields()[8], other.positionSide)) {
        this.positionSide = data().deepCopy(fields()[8].schema(), other.positionSide);
        fieldSetFlags()[8] = true;
      }
      if (isValidValue(fields()[9], other.quantity)) {
        this.quantity = data().deepCopy(fields()[9].schema(), other.quantity);
        fieldSetFlags()[9] = true;
      }
      if (isValidValue(fields()[10], other.price)) {
        this.price = data().deepCopy(fields()[10].schema(), other.price);
        fieldSetFlags()[10] = true;
      }
      if (isValidValue(fields()[11], other.executedPrice)) {
        this.executedPrice = data().deepCopy(fields()[11].schema(), other.executedPrice);
        fieldSetFlags()[11] = true;
      }
      if (isValidValue(fields()[12], other.status)) {
        this.status = data().deepCopy(fields()[12].schema(), other.status);
        fieldSetFlags()[12] = true;
      }
      if (isValidValue(fields()[13], other.createdTime)) {
        this.createdTime = data().deepCopy(fields()[13].schema(), other.createdTime);
        fieldSetFlags()[13] = true;
      }
      if (isValidValue(fields()[14], other.executedTime)) {
        this.executedTime = data().deepCopy(fields()[14].schema(), other.executedTime);
        fieldSetFlags()[14] = true;
      }
      if (isValidValue(fields()[15], other.errorCode)) {
        this.errorCode = data().deepCopy(fields()[15].schema(), other.errorCode);
        fieldSetFlags()[15] = true;
      }
      if (isValidValue(fields()[16], other.errorMessage)) {
        this.errorMessage = data().deepCopy(fields()[16].schema(), other.errorMessage);
        fieldSetFlags()[16] = true;
      }
    }

    /**
      * Gets the value of the 'id' field.
      * 订单执行唯一ID
      * @return The value.
      */
    public java.lang.String getId() {
      return id;
    }


    /**
      * Sets the value of the 'id' field.
      * 订单执行唯一ID
      * @param value The value of 'id'.
      * @return This builder.
      */
    public com.crypto.trading.trade.model.avro.OrderExecution.Builder setId(java.lang.String value) {
      validate(fields()[0], value);
      this.id = value;
      fieldSetFlags()[0] = true;
      return this;
    }

    /**
      * Checks whether the 'id' field has been set.
      * 订单执行唯一ID
      * @return True if the 'id' field has been set, false otherwise.
      */
    public boolean hasId() {
      return fieldSetFlags()[0];
    }


    /**
      * Clears the value of the 'id' field.
      * 订单执行唯一ID
      * @return This builder.
      */
    public com.crypto.trading.trade.model.avro.OrderExecution.Builder clearId() {
      id = null;
      fieldSetFlags()[0] = false;
      return this;
    }

    /**
      * Gets the value of the 'signalId' field.
      * 关联的交易信号ID
      * @return The value.
      */
    public java.lang.String getSignalId() {
      return signalId;
    }


    /**
      * Sets the value of the 'signalId' field.
      * 关联的交易信号ID
      * @param value The value of 'signalId'.
      * @return This builder.
      */
    public com.crypto.trading.trade.model.avro.OrderExecution.Builder setSignalId(java.lang.String value) {
      validate(fields()[1], value);
      this.signalId = value;
      fieldSetFlags()[1] = true;
      return this;
    }

    /**
      * Checks whether the 'signalId' field has been set.
      * 关联的交易信号ID
      * @return True if the 'signalId' field has been set, false otherwise.
      */
    public boolean hasSignalId() {
      return fieldSetFlags()[1];
    }


    /**
      * Clears the value of the 'signalId' field.
      * 关联的交易信号ID
      * @return This builder.
      */
    public com.crypto.trading.trade.model.avro.OrderExecution.Builder clearSignalId() {
      signalId = null;
      fieldSetFlags()[1] = false;
      return this;
    }

    /**
      * Gets the value of the 'orderId' field.
      * 交易所订单ID
      * @return The value.
      */
    public java.lang.String getOrderId() {
      return orderId;
    }


    /**
      * Sets the value of the 'orderId' field.
      * 交易所订单ID
      * @param value The value of 'orderId'.
      * @return This builder.
      */
    public com.crypto.trading.trade.model.avro.OrderExecution.Builder setOrderId(java.lang.String value) {
      validate(fields()[2], value);
      this.orderId = value;
      fieldSetFlags()[2] = true;
      return this;
    }

    /**
      * Checks whether the 'orderId' field has been set.
      * 交易所订单ID
      * @return True if the 'orderId' field has been set, false otherwise.
      */
    public boolean hasOrderId() {
      return fieldSetFlags()[2];
    }


    /**
      * Clears the value of the 'orderId' field.
      * 交易所订单ID
      * @return This builder.
      */
    public com.crypto.trading.trade.model.avro.OrderExecution.Builder clearOrderId() {
      orderId = null;
      fieldSetFlags()[2] = false;
      return this;
    }

    /**
      * Gets the value of the 'clientOrderId' field.
      * 客户端订单ID
      * @return The value.
      */
    public java.lang.String getClientOrderId() {
      return clientOrderId;
    }


    /**
      * Sets the value of the 'clientOrderId' field.
      * 客户端订单ID
      * @param value The value of 'clientOrderId'.
      * @return This builder.
      */
    public com.crypto.trading.trade.model.avro.OrderExecution.Builder setClientOrderId(java.lang.String value) {
      validate(fields()[3], value);
      this.clientOrderId = value;
      fieldSetFlags()[3] = true;
      return this;
    }

    /**
      * Checks whether the 'clientOrderId' field has been set.
      * 客户端订单ID
      * @return True if the 'clientOrderId' field has been set, false otherwise.
      */
    public boolean hasClientOrderId() {
      return fieldSetFlags()[3];
    }


    /**
      * Clears the value of the 'clientOrderId' field.
      * 客户端订单ID
      * @return This builder.
      */
    public com.crypto.trading.trade.model.avro.OrderExecution.Builder clearClientOrderId() {
      clientOrderId = null;
      fieldSetFlags()[3] = false;
      return this;
    }

    /**
      * Gets the value of the 'symbol' field.
      * 交易对，如BTCUSDT
      * @return The value.
      */
    public java.lang.String getSymbol() {
      return symbol;
    }


    /**
      * Sets the value of the 'symbol' field.
      * 交易对，如BTCUSDT
      * @param value The value of 'symbol'.
      * @return This builder.
      */
    public com.crypto.trading.trade.model.avro.OrderExecution.Builder setSymbol(java.lang.String value) {
      validate(fields()[4], value);
      this.symbol = value;
      fieldSetFlags()[4] = true;
      return this;
    }

    /**
      * Checks whether the 'symbol' field has been set.
      * 交易对，如BTCUSDT
      * @return True if the 'symbol' field has been set, false otherwise.
      */
    public boolean hasSymbol() {
      return fieldSetFlags()[4];
    }


    /**
      * Clears the value of the 'symbol' field.
      * 交易对，如BTCUSDT
      * @return This builder.
      */
    public com.crypto.trading.trade.model.avro.OrderExecution.Builder clearSymbol() {
      symbol = null;
      fieldSetFlags()[4] = false;
      return this;
    }

    /**
      * Gets the value of the 'strategy' field.
      * 策略名称
      * @return The value.
      */
    public java.lang.String getStrategy() {
      return strategy;
    }


    /**
      * Sets the value of the 'strategy' field.
      * 策略名称
      * @param value The value of 'strategy'.
      * @return This builder.
      */
    public com.crypto.trading.trade.model.avro.OrderExecution.Builder setStrategy(java.lang.String value) {
      validate(fields()[5], value);
      this.strategy = value;
      fieldSetFlags()[5] = true;
      return this;
    }

    /**
      * Checks whether the 'strategy' field has been set.
      * 策略名称
      * @return True if the 'strategy' field has been set, false otherwise.
      */
    public boolean hasStrategy() {
      return fieldSetFlags()[5];
    }


    /**
      * Clears the value of the 'strategy' field.
      * 策略名称
      * @return This builder.
      */
    public com.crypto.trading.trade.model.avro.OrderExecution.Builder clearStrategy() {
      strategy = null;
      fieldSetFlags()[5] = false;
      return this;
    }

    /**
      * Gets the value of the 'orderType' field.
      * 订单类型
      * @return The value.
      */
    public com.crypto.trading.trade.model.avro.OrderType getOrderType() {
      return orderType;
    }


    /**
      * Sets the value of the 'orderType' field.
      * 订单类型
      * @param value The value of 'orderType'.
      * @return This builder.
      */
    public com.crypto.trading.trade.model.avro.OrderExecution.Builder setOrderType(com.crypto.trading.trade.model.avro.OrderType value) {
      validate(fields()[6], value);
      this.orderType = value;
      fieldSetFlags()[6] = true;
      return this;
    }

    /**
      * Checks whether the 'orderType' field has been set.
      * 订单类型
      * @return True if the 'orderType' field has been set, false otherwise.
      */
    public boolean hasOrderType() {
      return fieldSetFlags()[6];
    }


    /**
      * Clears the value of the 'orderType' field.
      * 订单类型
      * @return This builder.
      */
    public com.crypto.trading.trade.model.avro.OrderExecution.Builder clearOrderType() {
      orderType = null;
      fieldSetFlags()[6] = false;
      return this;
    }

    /**
      * Gets the value of the 'side' field.
      * 订单方向
      * @return The value.
      */
    public com.crypto.trading.trade.model.avro.OrderSide getSide() {
      return side;
    }


    /**
      * Sets the value of the 'side' field.
      * 订单方向
      * @param value The value of 'side'.
      * @return This builder.
      */
    public com.crypto.trading.trade.model.avro.OrderExecution.Builder setSide(com.crypto.trading.trade.model.avro.OrderSide value) {
      validate(fields()[7], value);
      this.side = value;
      fieldSetFlags()[7] = true;
      return this;
    }

    /**
      * Checks whether the 'side' field has been set.
      * 订单方向
      * @return True if the 'side' field has been set, false otherwise.
      */
    public boolean hasSide() {
      return fieldSetFlags()[7];
    }


    /**
      * Clears the value of the 'side' field.
      * 订单方向
      * @return This builder.
      */
    public com.crypto.trading.trade.model.avro.OrderExecution.Builder clearSide() {
      side = null;
      fieldSetFlags()[7] = false;
      return this;
    }

    /**
      * Gets the value of the 'positionSide' field.
      * 仓位方向
      * @return The value.
      */
    public com.crypto.trading.trade.model.avro.PositionSide getPositionSide() {
      return positionSide;
    }


    /**
      * Sets the value of the 'positionSide' field.
      * 仓位方向
      * @param value The value of 'positionSide'.
      * @return This builder.
      */
    public com.crypto.trading.trade.model.avro.OrderExecution.Builder setPositionSide(com.crypto.trading.trade.model.avro.PositionSide value) {
      validate(fields()[8], value);
      this.positionSide = value;
      fieldSetFlags()[8] = true;
      return this;
    }

    /**
      * Checks whether the 'positionSide' field has been set.
      * 仓位方向
      * @return True if the 'positionSide' field has been set, false otherwise.
      */
    public boolean hasPositionSide() {
      return fieldSetFlags()[8];
    }


    /**
      * Clears the value of the 'positionSide' field.
      * 仓位方向
      * @return This builder.
      */
    public com.crypto.trading.trade.model.avro.OrderExecution.Builder clearPositionSide() {
      positionSide = null;
      fieldSetFlags()[8] = false;
      return this;
    }

    /**
      * Gets the value of the 'quantity' field.
      * 交易数量
      * @return The value.
      */
    public double getQuantity() {
      return quantity;
    }


    /**
      * Sets the value of the 'quantity' field.
      * 交易数量
      * @param value The value of 'quantity'.
      * @return This builder.
      */
    public com.crypto.trading.trade.model.avro.OrderExecution.Builder setQuantity(double value) {
      validate(fields()[9], value);
      this.quantity = value;
      fieldSetFlags()[9] = true;
      return this;
    }

    /**
      * Checks whether the 'quantity' field has been set.
      * 交易数量
      * @return True if the 'quantity' field has been set, false otherwise.
      */
    public boolean hasQuantity() {
      return fieldSetFlags()[9];
    }


    /**
      * Clears the value of the 'quantity' field.
      * 交易数量
      * @return This builder.
      */
    public com.crypto.trading.trade.model.avro.OrderExecution.Builder clearQuantity() {
      fieldSetFlags()[9] = false;
      return this;
    }

    /**
      * Gets the value of the 'price' field.
      * 订单价格，市价单为null
      * @return The value.
      */
    public java.lang.Double getPrice() {
      return price;
    }


    /**
      * Sets the value of the 'price' field.
      * 订单价格，市价单为null
      * @param value The value of 'price'.
      * @return This builder.
      */
    public com.crypto.trading.trade.model.avro.OrderExecution.Builder setPrice(java.lang.Double value) {
      validate(fields()[10], value);
      this.price = value;
      fieldSetFlags()[10] = true;
      return this;
    }

    /**
      * Checks whether the 'price' field has been set.
      * 订单价格，市价单为null
      * @return True if the 'price' field has been set, false otherwise.
      */
    public boolean hasPrice() {
      return fieldSetFlags()[10];
    }


    /**
      * Clears the value of the 'price' field.
      * 订单价格，市价单为null
      * @return This builder.
      */
    public com.crypto.trading.trade.model.avro.OrderExecution.Builder clearPrice() {
      price = null;
      fieldSetFlags()[10] = false;
      return this;
    }

    /**
      * Gets the value of the 'executedPrice' field.
      * 执行价格
      * @return The value.
      */
    public java.lang.Double getExecutedPrice() {
      return executedPrice;
    }


    /**
      * Sets the value of the 'executedPrice' field.
      * 执行价格
      * @param value The value of 'executedPrice'.
      * @return This builder.
      */
    public com.crypto.trading.trade.model.avro.OrderExecution.Builder setExecutedPrice(java.lang.Double value) {
      validate(fields()[11], value);
      this.executedPrice = value;
      fieldSetFlags()[11] = true;
      return this;
    }

    /**
      * Checks whether the 'executedPrice' field has been set.
      * 执行价格
      * @return True if the 'executedPrice' field has been set, false otherwise.
      */
    public boolean hasExecutedPrice() {
      return fieldSetFlags()[11];
    }


    /**
      * Clears the value of the 'executedPrice' field.
      * 执行价格
      * @return This builder.
      */
    public com.crypto.trading.trade.model.avro.OrderExecution.Builder clearExecutedPrice() {
      executedPrice = null;
      fieldSetFlags()[11] = false;
      return this;
    }

    /**
      * Gets the value of the 'status' field.
      * 订单状态
      * @return The value.
      */
    public com.crypto.trading.trade.model.avro.OrderStatus getStatus() {
      return status;
    }


    /**
      * Sets the value of the 'status' field.
      * 订单状态
      * @param value The value of 'status'.
      * @return This builder.
      */
    public com.crypto.trading.trade.model.avro.OrderExecution.Builder setStatus(com.crypto.trading.trade.model.avro.OrderStatus value) {
      validate(fields()[12], value);
      this.status = value;
      fieldSetFlags()[12] = true;
      return this;
    }

    /**
      * Checks whether the 'status' field has been set.
      * 订单状态
      * @return True if the 'status' field has been set, false otherwise.
      */
    public boolean hasStatus() {
      return fieldSetFlags()[12];
    }


    /**
      * Clears the value of the 'status' field.
      * 订单状态
      * @return This builder.
      */
    public com.crypto.trading.trade.model.avro.OrderExecution.Builder clearStatus() {
      status = null;
      fieldSetFlags()[12] = false;
      return this;
    }

    /**
      * Gets the value of the 'createdTime' field.
      * 订单创建时间（毫秒时间戳）
      * @return The value.
      */
    public long getCreatedTime() {
      return createdTime;
    }


    /**
      * Sets the value of the 'createdTime' field.
      * 订单创建时间（毫秒时间戳）
      * @param value The value of 'createdTime'.
      * @return This builder.
      */
    public com.crypto.trading.trade.model.avro.OrderExecution.Builder setCreatedTime(long value) {
      validate(fields()[13], value);
      this.createdTime = value;
      fieldSetFlags()[13] = true;
      return this;
    }

    /**
      * Checks whether the 'createdTime' field has been set.
      * 订单创建时间（毫秒时间戳）
      * @return True if the 'createdTime' field has been set, false otherwise.
      */
    public boolean hasCreatedTime() {
      return fieldSetFlags()[13];
    }


    /**
      * Clears the value of the 'createdTime' field.
      * 订单创建时间（毫秒时间戳）
      * @return This builder.
      */
    public com.crypto.trading.trade.model.avro.OrderExecution.Builder clearCreatedTime() {
      fieldSetFlags()[13] = false;
      return this;
    }

    /**
      * Gets the value of the 'executedTime' field.
      * 订单执行时间（毫秒时间戳）
      * @return The value.
      */
    public java.lang.Long getExecutedTime() {
      return executedTime;
    }


    /**
      * Sets the value of the 'executedTime' field.
      * 订单执行时间（毫秒时间戳）
      * @param value The value of 'executedTime'.
      * @return This builder.
      */
    public com.crypto.trading.trade.model.avro.OrderExecution.Builder setExecutedTime(java.lang.Long value) {
      validate(fields()[14], value);
      this.executedTime = value;
      fieldSetFlags()[14] = true;
      return this;
    }

    /**
      * Checks whether the 'executedTime' field has been set.
      * 订单执行时间（毫秒时间戳）
      * @return True if the 'executedTime' field has been set, false otherwise.
      */
    public boolean hasExecutedTime() {
      return fieldSetFlags()[14];
    }


    /**
      * Clears the value of the 'executedTime' field.
      * 订单执行时间（毫秒时间戳）
      * @return This builder.
      */
    public com.crypto.trading.trade.model.avro.OrderExecution.Builder clearExecutedTime() {
      executedTime = null;
      fieldSetFlags()[14] = false;
      return this;
    }

    /**
      * Gets the value of the 'errorCode' field.
      * 错误代码，如果有
      * @return The value.
      */
    public java.lang.String getErrorCode() {
      return errorCode;
    }


    /**
      * Sets the value of the 'errorCode' field.
      * 错误代码，如果有
      * @param value The value of 'errorCode'.
      * @return This builder.
      */
    public com.crypto.trading.trade.model.avro.OrderExecution.Builder setErrorCode(java.lang.String value) {
      validate(fields()[15], value);
      this.errorCode = value;
      fieldSetFlags()[15] = true;
      return this;
    }

    /**
      * Checks whether the 'errorCode' field has been set.
      * 错误代码，如果有
      * @return True if the 'errorCode' field has been set, false otherwise.
      */
    public boolean hasErrorCode() {
      return fieldSetFlags()[15];
    }


    /**
      * Clears the value of the 'errorCode' field.
      * 错误代码，如果有
      * @return This builder.
      */
    public com.crypto.trading.trade.model.avro.OrderExecution.Builder clearErrorCode() {
      errorCode = null;
      fieldSetFlags()[15] = false;
      return this;
    }

    /**
      * Gets the value of the 'errorMessage' field.
      * 错误信息，如果有
      * @return The value.
      */
    public java.lang.String getErrorMessage() {
      return errorMessage;
    }


    /**
      * Sets the value of the 'errorMessage' field.
      * 错误信息，如果有
      * @param value The value of 'errorMessage'.
      * @return This builder.
      */
    public com.crypto.trading.trade.model.avro.OrderExecution.Builder setErrorMessage(java.lang.String value) {
      validate(fields()[16], value);
      this.errorMessage = value;
      fieldSetFlags()[16] = true;
      return this;
    }

    /**
      * Checks whether the 'errorMessage' field has been set.
      * 错误信息，如果有
      * @return True if the 'errorMessage' field has been set, false otherwise.
      */
    public boolean hasErrorMessage() {
      return fieldSetFlags()[16];
    }


    /**
      * Clears the value of the 'errorMessage' field.
      * 错误信息，如果有
      * @return This builder.
      */
    public com.crypto.trading.trade.model.avro.OrderExecution.Builder clearErrorMessage() {
      errorMessage = null;
      fieldSetFlags()[16] = false;
      return this;
    }

    @Override
    @SuppressWarnings("unchecked")
    public OrderExecution build() {
      try {
        OrderExecution record = new OrderExecution();
        record.id = fieldSetFlags()[0] ? this.id : (java.lang.String) defaultValue(fields()[0]);
        record.signalId = fieldSetFlags()[1] ? this.signalId : (java.lang.String) defaultValue(fields()[1]);
        record.orderId = fieldSetFlags()[2] ? this.orderId : (java.lang.String) defaultValue(fields()[2]);
        record.clientOrderId = fieldSetFlags()[3] ? this.clientOrderId : (java.lang.String) defaultValue(fields()[3]);
        record.symbol = fieldSetFlags()[4] ? this.symbol : (java.lang.String) defaultValue(fields()[4]);
        record.strategy = fieldSetFlags()[5] ? this.strategy : (java.lang.String) defaultValue(fields()[5]);
        record.orderType = fieldSetFlags()[6] ? this.orderType : (com.crypto.trading.trade.model.avro.OrderType) defaultValue(fields()[6]);
        record.side = fieldSetFlags()[7] ? this.side : (com.crypto.trading.trade.model.avro.OrderSide) defaultValue(fields()[7]);
        record.positionSide = fieldSetFlags()[8] ? this.positionSide : (com.crypto.trading.trade.model.avro.PositionSide) defaultValue(fields()[8]);
        record.quantity = fieldSetFlags()[9] ? this.quantity : (java.lang.Double) defaultValue(fields()[9]);
        record.price = fieldSetFlags()[10] ? this.price : (java.lang.Double) defaultValue(fields()[10]);
        record.executedPrice = fieldSetFlags()[11] ? this.executedPrice : (java.lang.Double) defaultValue(fields()[11]);
        record.status = fieldSetFlags()[12] ? this.status : (com.crypto.trading.trade.model.avro.OrderStatus) defaultValue(fields()[12]);
        record.createdTime = fieldSetFlags()[13] ? this.createdTime : (java.lang.Long) defaultValue(fields()[13]);
        record.executedTime = fieldSetFlags()[14] ? this.executedTime : (java.lang.Long) defaultValue(fields()[14]);
        record.errorCode = fieldSetFlags()[15] ? this.errorCode : (java.lang.String) defaultValue(fields()[15]);
        record.errorMessage = fieldSetFlags()[16] ? this.errorMessage : (java.lang.String) defaultValue(fields()[16]);
        return record;
      } catch (org.apache.avro.AvroMissingFieldException e) {
        throw e;
      } catch (java.lang.Exception e) {
        throw new org.apache.avro.AvroRuntimeException(e);
      }
    }
  }

  @SuppressWarnings("unchecked")
  private static final org.apache.avro.io.DatumWriter<OrderExecution>
    WRITER$ = (org.apache.avro.io.DatumWriter<OrderExecution>)MODEL$.createDatumWriter(SCHEMA$);

  @Override public void writeExternal(java.io.ObjectOutput out)
    throws java.io.IOException {
    WRITER$.write(this, SpecificData.getEncoder(out));
  }

  @SuppressWarnings("unchecked")
  private static final org.apache.avro.io.DatumReader<OrderExecution>
    READER$ = (org.apache.avro.io.DatumReader<OrderExecution>)MODEL$.createDatumReader(SCHEMA$);

  @Override public void readExternal(java.io.ObjectInput in)
    throws java.io.IOException {
    READER$.read(this, SpecificData.getDecoder(in));
  }

  @Override protected boolean hasCustomCoders() { return true; }

  @Override public void customEncode(org.apache.avro.io.Encoder out)
    throws java.io.IOException
  {
    out.writeString(this.id);

    out.writeString(this.signalId);

    out.writeString(this.orderId);

    out.writeString(this.clientOrderId);

    out.writeString(this.symbol);

    out.writeString(this.strategy);

    out.writeEnum(this.orderType.ordinal());

    out.writeEnum(this.side.ordinal());

    out.writeEnum(this.positionSide.ordinal());

    out.writeDouble(this.quantity);

    if (this.price == null) {
      out.writeIndex(0);
      out.writeNull();
    } else {
      out.writeIndex(1);
      out.writeDouble(this.price);
    }

    if (this.executedPrice == null) {
      out.writeIndex(0);
      out.writeNull();
    } else {
      out.writeIndex(1);
      out.writeDouble(this.executedPrice);
    }

    out.writeEnum(this.status.ordinal());

    out.writeLong(this.createdTime);

    if (this.executedTime == null) {
      out.writeIndex(0);
      out.writeNull();
    } else {
      out.writeIndex(1);
      out.writeLong(this.executedTime);
    }

    if (this.errorCode == null) {
      out.writeIndex(0);
      out.writeNull();
    } else {
      out.writeIndex(1);
      out.writeString(this.errorCode);
    }

    if (this.errorMessage == null) {
      out.writeIndex(0);
      out.writeNull();
    } else {
      out.writeIndex(1);
      out.writeString(this.errorMessage);
    }

  }

  @Override public void customDecode(org.apache.avro.io.ResolvingDecoder in)
    throws java.io.IOException
  {
    org.apache.avro.Schema.Field[] fieldOrder = in.readFieldOrderIfDiff();
    if (fieldOrder == null) {
      this.id = in.readString();

      this.signalId = in.readString();

      this.orderId = in.readString();

      this.clientOrderId = in.readString();

      this.symbol = in.readString();

      this.strategy = in.readString();

      this.orderType = com.crypto.trading.trade.model.avro.OrderType.values()[in.readEnum()];

      this.side = com.crypto.trading.trade.model.avro.OrderSide.values()[in.readEnum()];

      this.positionSide = com.crypto.trading.trade.model.avro.PositionSide.values()[in.readEnum()];

      this.quantity = in.readDouble();

      if (in.readIndex() != 1) {
        in.readNull();
        this.price = null;
      } else {
        this.price = in.readDouble();
      }

      if (in.readIndex() != 1) {
        in.readNull();
        this.executedPrice = null;
      } else {
        this.executedPrice = in.readDouble();
      }

      this.status = com.crypto.trading.trade.model.avro.OrderStatus.values()[in.readEnum()];

      this.createdTime = in.readLong();

      if (in.readIndex() != 1) {
        in.readNull();
        this.executedTime = null;
      } else {
        this.executedTime = in.readLong();
      }

      if (in.readIndex() != 1) {
        in.readNull();
        this.errorCode = null;
      } else {
        this.errorCode = in.readString();
      }

      if (in.readIndex() != 1) {
        in.readNull();
        this.errorMessage = null;
      } else {
        this.errorMessage = in.readString();
      }

    } else {
      for (int i = 0; i < 17; i++) {
        switch (fieldOrder[i].pos()) {
        case 0:
          this.id = in.readString();
          break;

        case 1:
          this.signalId = in.readString();
          break;

        case 2:
          this.orderId = in.readString();
          break;

        case 3:
          this.clientOrderId = in.readString();
          break;

        case 4:
          this.symbol = in.readString();
          break;

        case 5:
          this.strategy = in.readString();
          break;

        case 6:
          this.orderType = com.crypto.trading.trade.model.avro.OrderType.values()[in.readEnum()];
          break;

        case 7:
          this.side = com.crypto.trading.trade.model.avro.OrderSide.values()[in.readEnum()];
          break;

        case 8:
          this.positionSide = com.crypto.trading.trade.model.avro.PositionSide.values()[in.readEnum()];
          break;

        case 9:
          this.quantity = in.readDouble();
          break;

        case 10:
          if (in.readIndex() != 1) {
            in.readNull();
            this.price = null;
          } else {
            this.price = in.readDouble();
          }
          break;

        case 11:
          if (in.readIndex() != 1) {
            in.readNull();
            this.executedPrice = null;
          } else {
            this.executedPrice = in.readDouble();
          }
          break;

        case 12:
          this.status = com.crypto.trading.trade.model.avro.OrderStatus.values()[in.readEnum()];
          break;

        case 13:
          this.createdTime = in.readLong();
          break;

        case 14:
          if (in.readIndex() != 1) {
            in.readNull();
            this.executedTime = null;
          } else {
            this.executedTime = in.readLong();
          }
          break;

        case 15:
          if (in.readIndex() != 1) {
            in.readNull();
            this.errorCode = null;
          } else {
            this.errorCode = in.readString();
          }
          break;

        case 16:
          if (in.readIndex() != 1) {
            in.readNull();
            this.errorMessage = null;
          } else {
            this.errorMessage = in.readString();
          }
          break;

        default:
          throw new java.io.IOException("Corrupt ResolvingDecoder.");
        }
      }
    }
  }
}










