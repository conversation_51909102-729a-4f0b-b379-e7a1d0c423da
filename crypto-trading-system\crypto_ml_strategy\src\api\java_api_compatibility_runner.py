#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Java API兼容性测试运行器

该模块整合所有Java API兼容性测试组件，提供统一的测试执行入口。
"""

import asyncio
import time
import json
from typing import Dict, List, Any, Optional
from datetime import datetime, timezone
from loguru import logger

from .config import Config
from .java_api_core import JavaApiValidator, MessageFormatValidator, create_test_signal_message
from .kafka_compatibility_tests import KafkaCompatibilityTester, MessageSchemaValidator, ProducerConsumerTester
from .serialization_compatibility import SerializationCompatibilityTester, JsonCompatibilityValidator, DataTypeValidator
from .performance_benchmarks import CrossLanguagePerformanceBenchmark
from .integration_test_suite import JavaIntegrationTestSuite
from .api_compatibility_docs import ApiCompatibilityDocGenerator, ValidationReportGenerator, CompatibilityMatrixGenerator


class JavaApiCompatibilityRunner:
    """
    Java API兼容性测试运行器
    
    整合所有兼容性测试组件，提供统一的测试执行和报告生成。
    """
    
    def __init__(self, config: Config):
        """
        初始化兼容性测试运行器
        
        Args:
            config: 配置对象
        """
        self.config = config
        
        # 初始化所有测试组件
        self.api_validator = JavaApiValidator(config)
        self.message_validator = MessageFormatValidator()
        self.kafka_tester = KafkaCompatibilityTester(config)
        self.schema_validator = MessageSchemaValidator()
        self.serialization_tester = SerializationCompatibilityTester()
        self.json_validator = JsonCompatibilityValidator()
        self.type_validator = DataTypeValidator()
        self.performance_benchmark = CrossLanguagePerformanceBenchmark(config)
        self.integration_suite = JavaIntegrationTestSuite(config)
        
        # 报告生成器
        self.doc_generator = ApiCompatibilityDocGenerator()
        self.report_generator = ValidationReportGenerator()
        self.matrix_generator = CompatibilityMatrixGenerator()
        
        # 测试结果存储
        self.all_validation_results = []
        self.all_performance_metrics = []
        
        logger.info("Java API兼容性测试运行器初始化完成")
    
    async def run_complete_compatibility_test(self) -> Dict[str, Any]:
        """
        运行完整的兼容性测试套件
        
        Returns:
            完整的测试结果
        """
        logger.info("🚀 开始Java API兼容性测试套件")
        
        test_start_time = time.time()
        
        results = {
            "test_suite_info": {
                "start_time": datetime.now(timezone.utc).isoformat(),
                "test_version": "1.0.0",
                "components_tested": [
                    "Java API Validator", "Kafka Compatibility", 
                    "Serialization Compatibility", "Performance Benchmarks",
                    "Integration Tests"
                ]
            },
            "test_results": {},
            "performance_results": {},
            "integration_results": {},
            "compatibility_reports": {},
            "overall_assessment": {}
        }
        
        try:
            # 1. 基础API兼容性测试
            logger.info("📋 执行基础API兼容性测试...")
            results["test_results"]["api_compatibility"] = await self._run_api_compatibility_tests()
            
            # 2. Kafka兼容性测试
            logger.info("📡 执行Kafka兼容性测试...")
            results["test_results"]["kafka_compatibility"] = await self._run_kafka_compatibility_tests()
            
            # 3. 序列化兼容性测试
            logger.info("🔄 执行序列化兼容性测试...")
            results["test_results"]["serialization_compatibility"] = await self._run_serialization_compatibility_tests()
            
            # 4. 性能基准测试
            logger.info("⚡ 执行性能基准测试...")
            results["performance_results"] = await self._run_performance_benchmarks()
            
            # 5. 集成测试
            logger.info("🔗 执行集成测试...")
            results["integration_results"] = await self._run_integration_tests()
            
            # 6. 生成兼容性报告
            logger.info("📊 生成兼容性报告...")
            results["compatibility_reports"] = await self._generate_compatibility_reports()
            
            # 7. 整体评估
            results["overall_assessment"] = self._perform_overall_assessment(results)
            
            test_end_time = time.time()
            results["test_suite_info"]["end_time"] = datetime.now(timezone.utc).isoformat()
            results["test_suite_info"]["total_duration_seconds"] = test_end_time - test_start_time
            
            logger.info(f"✅ Java API兼容性测试套件完成，总耗时: {test_end_time - test_start_time:.2f}秒")
            
        except Exception as e:
            logger.error(f"❌ 兼容性测试套件执行异常: {e}")
            results["error"] = str(e)
            results["test_suite_info"]["status"] = "FAILED"
        
        return results
    
    async def _run_api_compatibility_tests(self) -> Dict[str, Any]:
        """运行API兼容性测试"""
        api_results = {
            "message_format_tests": [],
            "api_validation_tests": [],
            "summary": {}
        }
        
        # 消息格式验证测试
        test_signal = create_test_signal_message()
        signal_validation = self.message_validator.validate_signal_message(test_signal)
        api_results["message_format_tests"].append(signal_validation)
        self.all_validation_results.append(signal_validation)
        
        # 创建多种测试消息
        test_messages = [
            {
                "messageId": "test-001",
                "messageType": "kline",
                "timestamp": int(time.time() * 1000),
                "data": {
                    "symbol": "BTCUSDT",
                    "interval": "1m",
                    "open": 50000.0,
                    "high": 50100.0,
                    "low": 49900.0,
                    "close": 50050.0,
                    "volume": 1000.0
                }
            },
            {
                "messageId": "test-002",
                "messageType": "depth",
                "timestamp": int(time.time() * 1000),
                "data": {
                    "symbol": "ETHUSDT",
                    "bids": [[3000.0, 10.0], [2999.0, 5.0]],
                    "asks": [[3001.0, 8.0], [3002.0, 12.0]]
                }
            }
        ]
        
        # 验证各种消息格式
        for msg in test_messages:
            schema = {"required": ["messageId", "messageType", "timestamp", "data"]}
            validation_result = self.api_validator.validate_message_format(msg, schema)
            api_results["api_validation_tests"].append(validation_result)
            self.all_validation_results.append(validation_result)
        
        # 生成摘要
        total_tests = len(api_results["message_format_tests"]) + len(api_results["api_validation_tests"])
        passed_tests = sum(1 for test in api_results["message_format_tests"] + api_results["api_validation_tests"] if test.success)
        
        api_results["summary"] = {
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "failed_tests": total_tests - passed_tests,
            "success_rate": passed_tests / total_tests if total_tests > 0 else 0
        }
        
        return api_results
    
    async def _run_kafka_compatibility_tests(self) -> Dict[str, Any]:
        """运行Kafka兼容性测试"""
        kafka_results = {
            "topic_compatibility_tests": [],
            "schema_validation_tests": [],
            "producer_consumer_tests": [],
            "summary": {}
        }
        
        # 主题兼容性测试
        topic_results = await self.kafka_tester.test_topic_compatibility()
        kafka_results["topic_compatibility_tests"] = topic_results
        self.all_validation_results.extend(topic_results)
        
        # 消息模式验证测试
        test_messages = [
            {
                "messageId": "schema-test-001",
                "messageType": "kline",
                "timestamp": int(time.time() * 1000),
                "data": {
                    "symbol": "BTCUSDT",
                    "interval": "1m",
                    "open": 50000.0,
                    "high": 50100.0,
                    "low": 49900.0,
                    "close": 50050.0,
                    "volume": 1000.0
                }
            },
            create_test_signal_message()
        ]
        
        for msg in test_messages:
            msg_type = msg.get("messageType", "unknown")
            schema_result = self.schema_validator.validate_message_schema(msg, msg_type)
            kafka_results["schema_validation_tests"].append(schema_result)
            self.all_validation_results.append(schema_result)
        
        # 生产者消费者测试
        pc_tester = ProducerConsumerTester(self.kafka_tester.kafka_client)
        test_topic = self.config.get('kafka', 'signal_topic')
        pc_result = await pc_tester.test_producer_consumer_compatibility(test_topic, 5)
        kafka_results["producer_consumer_tests"].append(pc_result)
        self.all_validation_results.append(pc_result)
        
        # 生成摘要
        all_kafka_tests = (kafka_results["topic_compatibility_tests"] + 
                          kafka_results["schema_validation_tests"] + 
                          kafka_results["producer_consumer_tests"])
        
        total_tests = len(all_kafka_tests)
        passed_tests = sum(1 for test in all_kafka_tests if test.success)
        
        kafka_results["summary"] = {
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "failed_tests": total_tests - passed_tests,
            "success_rate": passed_tests / total_tests if total_tests > 0 else 0
        }
        
        return kafka_results
    
    async def _run_serialization_compatibility_tests(self) -> Dict[str, Any]:
        """运行序列化兼容性测试"""
        serialization_results = {
            "json_compatibility_tests": [],
            "type_compatibility_tests": [],
            "java_json_compatibility_tests": [],
            "summary": {}
        }
        
        # JSON兼容性测试
        json_results = self.serialization_tester.test_json_compatibility()
        serialization_results["json_compatibility_tests"] = json_results
        self.all_validation_results.extend(json_results)
        
        # 类型兼容性测试
        test_data_samples = [
            {"string": "test", "integer": 123, "float": 123.45, "boolean": True},
            {"array": [1, 2, 3], "object": {"nested": "value"}},
            create_test_signal_message()
        ]
        
        for data in test_data_samples:
            type_result = self.type_validator.validate_type_compatibility(data)
            serialization_results["type_compatibility_tests"].append(type_result)
            self.all_validation_results.append(type_result)
        
        # Java JSON兼容性测试
        for data in test_data_samples:
            java_json_result = self.json_validator.validate_java_json_compatibility(data)
            serialization_results["java_json_compatibility_tests"].append(java_json_result)
            self.all_validation_results.append(java_json_result)
        
        # 生成摘要
        all_serialization_tests = (serialization_results["json_compatibility_tests"] + 
                                 serialization_results["type_compatibility_tests"] + 
                                 serialization_results["java_json_compatibility_tests"])
        
        total_tests = len(all_serialization_tests)
        passed_tests = sum(1 for test in all_serialization_tests if test.success)
        
        serialization_results["summary"] = {
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "failed_tests": total_tests - passed_tests,
            "success_rate": passed_tests / total_tests if total_tests > 0 else 0
        }
        
        return serialization_results
    
    async def _run_performance_benchmarks(self) -> Dict[str, Any]:
        """运行性能基准测试"""
        logger.info("开始性能基准测试...")
        
        # 运行综合性能基准测试
        benchmark_results = await self.performance_benchmark.run_comprehensive_benchmark()
        
        # 收集性能指标
        self.all_performance_metrics.extend(self.performance_benchmark.performance_metrics)
        
        return benchmark_results
    
    async def _run_integration_tests(self) -> Dict[str, Any]:
        """运行集成测试"""
        logger.info("开始集成测试...")
        
        # 运行完整集成测试
        integration_results = await self.integration_suite.run_full_integration_test()
        
        # 收集集成测试的验证结果
        if "component_tests" in integration_results:
            for test_result in integration_results["component_tests"].values():
                if hasattr(test_result, 'success'):
                    self.all_validation_results.append(test_result)
        
        if "integration_tests" in integration_results:
            for test_result in integration_results["integration_tests"].values():
                if hasattr(test_result, 'success'):
                    self.all_validation_results.append(test_result)
        
        return integration_results
    
    async def _generate_compatibility_reports(self) -> Dict[str, Any]:
        """生成兼容性报告"""
        logger.info("生成兼容性报告...")
        
        reports = {}
        
        # 生成综合兼容性报告
        comprehensive_report = self.doc_generator.generate_comprehensive_report(
            self.all_validation_results,
            self.all_performance_metrics,
            {"test_suite": "Java API Compatibility"}
        )
        reports["comprehensive_report"] = comprehensive_report
        
        # 生成验证报告
        validation_report = self.report_generator.generate_validation_report(
            self.all_validation_results
        )
        reports["validation_report"] = validation_report
        
        # 生成兼容性矩阵
        compatibility_matrix = self.matrix_generator.generate_compatibility_matrix(
            self.all_validation_results
        )
        reports["compatibility_matrix"] = compatibility_matrix
        
        return reports
    
    def _perform_overall_assessment(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """执行整体评估"""
        assessment = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "test_categories": {},
            "performance_assessment": {},
            "integration_assessment": {},
            "overall_score": 0,
            "grade": "F",
            "status": "UNKNOWN",
            "recommendations": []
        }
        
        # 评估各测试类别
        test_categories = ["api_compatibility", "kafka_compatibility", "serialization_compatibility"]
        
        for category in test_categories:
            if category in results.get("test_results", {}):
                category_data = results["test_results"][category]
                summary = category_data.get("summary", {})
                
                assessment["test_categories"][category] = {
                    "success_rate": summary.get("success_rate", 0),
                    "total_tests": summary.get("total_tests", 0),
                    "passed_tests": summary.get("passed_tests", 0),
                    "status": "PASS" if summary.get("success_rate", 0) >= 0.8 else "FAIL"
                }
        
        # 性能评估
        if "performance_results" in results:
            perf_data = results["performance_results"]
            overall_assessment = perf_data.get("overall_assessment", {})
            
            assessment["performance_assessment"] = {
                "overall_score": overall_assessment.get("overall_score", 0),
                "grade": overall_assessment.get("grade", "F"),
                "latency_score": overall_assessment.get("latency_score", 0),
                "throughput_score": overall_assessment.get("throughput_score", 0),
                "resource_score": overall_assessment.get("resource_score", 0)
            }
        
        # 集成评估
        if "integration_results" in results:
            integration_data = results["integration_results"]
            assessment["integration_assessment"] = {
                "overall_status": integration_data.get("overall_status", "UNKNOWN"),
                "component_health": integration_data.get("health_checks", {}).get("overall_health", "UNKNOWN")
            }
        
        # 计算整体评分
        category_scores = [cat["success_rate"] for cat in assessment["test_categories"].values()]
        avg_category_score = sum(category_scores) / len(category_scores) if category_scores else 0
        
        performance_score = assessment["performance_assessment"].get("overall_score", 0) / 100
        
        # 综合评分 (测试70% + 性能30%)
        overall_score = avg_category_score * 0.7 + performance_score * 0.3
        assessment["overall_score"] = round(overall_score * 100, 2)
        
        # 评级
        if assessment["overall_score"] >= 90:
            assessment["grade"] = "A"
            assessment["status"] = "EXCELLENT"
        elif assessment["overall_score"] >= 80:
            assessment["grade"] = "B"
            assessment["status"] = "GOOD"
        elif assessment["overall_score"] >= 70:
            assessment["grade"] = "C"
            assessment["status"] = "ACCEPTABLE"
        elif assessment["overall_score"] >= 60:
            assessment["grade"] = "D"
            assessment["status"] = "NEEDS_IMPROVEMENT"
        else:
            assessment["grade"] = "F"
            assessment["status"] = "POOR"
        
        # 生成建议
        assessment["recommendations"] = self._generate_assessment_recommendations(assessment)
        
        return assessment
    
    def _generate_assessment_recommendations(self, assessment: Dict[str, Any]) -> List[str]:
        """生成评估建议"""
        recommendations = []
        
        # 基于整体评分的建议
        overall_score = assessment["overall_score"]
        
        if overall_score >= 90:
            recommendations.append("🎉 系统兼容性优秀，建议保持当前配置")
            recommendations.append("📈 建议建立持续监控机制，确保长期稳定性")
        elif overall_score >= 80:
            recommendations.append("✅ 系统兼容性良好，有少量改进空间")
            recommendations.append("🔍 建议关注失败的测试用例，进行针对性优化")
        elif overall_score >= 70:
            recommendations.append("⚠️ 系统兼容性可接受，但需要改进")
            recommendations.append("🛠️ 建议优先解决关键兼容性问题")
        else:
            recommendations.append("❌ 系统兼容性存在严重问题，需要立即处理")
            recommendations.append("🚨 建议暂停生产部署，先解决兼容性问题")
        
        # 基于具体测试类别的建议
        for category, data in assessment["test_categories"].items():
            if data["success_rate"] < 0.8:
                if category == "api_compatibility":
                    recommendations.append("🔧 API兼容性需要改进，检查消息格式和接口定义")
                elif category == "kafka_compatibility":
                    recommendations.append("📡 Kafka兼容性需要改进，检查主题配置和消息模式")
                elif category == "serialization_compatibility":
                    recommendations.append("🔄 序列化兼容性需要改进，检查数据类型映射")
        
        # 基于性能的建议
        perf_score = assessment["performance_assessment"].get("overall_score", 0)
        if perf_score < 70:
            recommendations.append("⚡ 性能表现需要优化，关注延迟和吞吐量指标")
        
        # 基于集成测试的建议
        integration_status = assessment["integration_assessment"].get("overall_status", "UNKNOWN")
        if integration_status != "PASSED":
            recommendations.append("🔗 集成测试存在问题，检查组件间协作")
        
        return recommendations


async def main():
    """主函数，用于测试"""
    try:
        # 初始化配置
        config = Config()
        
        # 创建测试运行器
        runner = JavaApiCompatibilityRunner(config)
        
        # 运行完整兼容性测试
        logger.info("🚀 启动Java API兼容性测试套件")
        results = await runner.run_complete_compatibility_test()
        
        # 输出结果摘要
        logger.info("📊 测试结果摘要:")
        logger.info(f"整体评分: {results['overall_assessment']['overall_score']}/100")
        logger.info(f"评级: {results['overall_assessment']['grade']}")
        logger.info(f"状态: {results['overall_assessment']['status']}")
        
        # 保存详细结果
        output_file = f"java_api_compatibility_results_{int(time.time())}.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False, default=str)
        
        logger.info(f"📄 详细结果已保存到: {output_file}")
        
        return results
        
    except Exception as e:
        logger.error(f"❌ 测试执行异常: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())