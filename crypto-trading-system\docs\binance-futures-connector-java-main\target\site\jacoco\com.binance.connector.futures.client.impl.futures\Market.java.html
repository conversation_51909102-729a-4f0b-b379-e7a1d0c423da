<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>Market.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">io.github.binance:binance-futures-connector-java</a> &gt; <a href="index.source.html" class="el_package">com.binance.connector.futures.client.impl.futures</a> &gt; <span class="el_source">Market.java</span></div><h1>Market.java</h1><pre class="source lang-java linenums">package com.binance.connector.futures.client.impl.futures;

import com.binance.connector.futures.client.enums.HttpMethod;
import com.binance.connector.futures.client.utils.ParameterChecker;
import com.binance.connector.futures.client.utils.ProxyAuth;
import com.binance.connector.futures.client.utils.RequestHandler;
import java.util.LinkedHashMap;

/**
 * &lt;h2&gt;Market Endpoints&lt;/h2&gt;
 * Response will be returned in &lt;i&gt;String format&lt;/i&gt;.
 */
public abstract class Market {
    private String baseUrl;
    private String productUrl;
    private RequestHandler requestHandler;
    private boolean showLimitUsage;

<span class="fc" id="L19">    public Market(String productUrl, String baseUrl, String apiKey, boolean showLimitUsage, ProxyAuth proxy) {</span>
<span class="fc" id="L20">        this.baseUrl = baseUrl;</span>
<span class="fc" id="L21">        this.productUrl = productUrl;</span>
<span class="fc" id="L22">        this.requestHandler = new RequestHandler(apiKey, proxy);</span>
<span class="fc" id="L23">        this.showLimitUsage = showLimitUsage;</span>
<span class="fc" id="L24">    }</span>

    public String getBaseUrl() {
<span class="fc" id="L27">        return this.baseUrl;</span>
    }

    public String getProductUrl() {
<span class="fc" id="L31">        return this.productUrl;</span>
    }

    public RequestHandler getRequestHandler() {
<span class="fc" id="L35">        return this.requestHandler;</span>
    }

    public boolean getShowLimitUsage() {
<span class="fc" id="L39">        return this.showLimitUsage;</span>
    }

    public void setBaseUrl(String baseUrl) {
<span class="nc" id="L43">        this.baseUrl = baseUrl;</span>
<span class="nc" id="L44">    }</span>

    public void setProductUrl(String productUrl) {
<span class="nc" id="L47">        this.productUrl = productUrl;</span>
<span class="nc" id="L48">    }</span>

    public void setRequestHandler(String apiKey, String secretKey, ProxyAuth proxy) {
<span class="nc" id="L51">        new RequestHandler(apiKey, secretKey, proxy);</span>
<span class="nc" id="L52">    }</span>

    public void setShowLimitUsage(boolean showLimitUsage) {
<span class="nc" id="L55">        this.showLimitUsage = showLimitUsage;</span>
<span class="nc" id="L56">    }</span>

<span class="fc" id="L58">    private final String MARK_PRICE = &quot;/v1/premiumIndex&quot;;</span>
    public String markPrice(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L60">        return requestHandler.sendPublicRequest(productUrl, MARK_PRICE, parameters, HttpMethod.GET, showLimitUsage);</span>
    }

<span class="fc" id="L63">    private final String TICKER_24H = &quot;/v1/ticker/24hr&quot;;</span>
    public String ticker24H(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L65">        return requestHandler.sendPublicRequest(productUrl, TICKER_24H, parameters, HttpMethod.GET, showLimitUsage);</span>
    }

<span class="fc" id="L68">    private final String TICKER_SYMBOL = &quot;/v1/ticker/price&quot;;</span>
    public String tickerSymbol(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L70">        return requestHandler.sendPublicRequest(productUrl, TICKER_SYMBOL, parameters, HttpMethod.GET, showLimitUsage);</span>
    }

<span class="fc" id="L73">    private final String BOOK_TICKER = &quot;/v1/ticker/bookTicker&quot;;</span>
    public String bookTicker(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L75">        return requestHandler.sendPublicRequest(productUrl, BOOK_TICKER, parameters, HttpMethod.GET, showLimitUsage);</span>
    }

<span class="fc" id="L78">    private final String OPEN_INTEREST_STATS = &quot;/futures/data/openInterestHist&quot;;</span>
    public String openInterestStatistics(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L80">        return requestHandler.sendPublicRequest(baseUrl, OPEN_INTEREST_STATS, parameters, HttpMethod.GET, showLimitUsage);</span>
    }

<span class="fc" id="L83">    private final String TOP_TRADER_LONG_SHORT_RATIO_POSITIONS = &quot;/futures/data/topLongShortPositionRatio&quot;;</span>
    public String topTraderLongShortPos(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L85">        return requestHandler.sendPublicRequest(baseUrl, TOP_TRADER_LONG_SHORT_RATIO_POSITIONS, parameters, HttpMethod.GET, showLimitUsage);</span>
    }

<span class="fc" id="L88">    private final String TOP_TRADER_LONG_SHORT_RATIO_ACCOUNTS = &quot;/futures/data/topLongShortAccountRatio&quot;;</span>
    public String topTraderLongShortAccs(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L90">        return requestHandler.sendPublicRequest(baseUrl, TOP_TRADER_LONG_SHORT_RATIO_ACCOUNTS, parameters, HttpMethod.GET, showLimitUsage);</span>
    }

<span class="fc" id="L93">    private final String GLOBAL_LONG_SHORT = &quot;/futures/data/globalLongShortAccountRatio&quot;;</span>
    public String longShortRatio(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L95">        return requestHandler.sendPublicRequest(baseUrl, GLOBAL_LONG_SHORT, parameters, HttpMethod.GET, showLimitUsage);</span>
    }


<span class="fc" id="L99">    private final String PING = &quot;/v1/ping&quot;;</span>
    /**
     * Test connectivity to the Rest API.
     * &lt;br&gt;&lt;br&gt;
     * GET /v1/ping
     * &lt;br&gt;
     * @return String
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Test-Connectivity&quot;&gt;
     *     https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Test-Connectivity&lt;/a&gt;
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/coin-margined-futures/market-data/Test-Connectivity&quot;&gt;
     * https://developers.binance.com/docs/derivatives/coin-margined-futures/market-data/Test-Connectivity&lt;/a&gt;
     */
    public String ping() {
<span class="fc" id="L112">        return requestHandler.sendPublicRequest(productUrl, PING, null, HttpMethod.GET, showLimitUsage);</span>
    }

<span class="fc" id="L115">    private final String TIME = &quot;/v1/time&quot;;</span>
    /**
     * Test connectivity to the Rest API and get the current server time.
     * &lt;br&gt;&lt;br&gt;
     * GET /api/v1/time
     * &lt;br&gt;
     * @return String
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Check-Server-Time&quot;&gt;
     *     https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Check-Server-Time&lt;/a&gt;
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/coin-margined-futures/market-data/Check-Server-time&quot;&gt;
     *     https://developers.binance.com/docs/derivatives/coin-margined-futures/market-data/Check-Server-time&lt;/a&gt;
     */
    public String time() {
<span class="fc" id="L128">        return requestHandler.sendPublicRequest(productUrl, TIME, null, HttpMethod.GET, showLimitUsage);</span>
    }

<span class="fc" id="L131">    private final String EXCHANGE_INFO = &quot;/v1/exchangeInfo&quot;;</span>
    /**
     * Current exchange trading rules and symbol information.
     * &lt;br&gt;&lt;br&gt;
     * GET /v1/exchangeinfo
     * &lt;br&gt;
     * @return String
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Exchange-Information&quot;&gt;
     *     https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Exchange-Information&lt;/a&gt;
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/coin-margined-futures/market-data/Exchange-Information&quot;&gt;
     *     https://developers.binance.com/docs/derivatives/coin-margined-futures/market-data/Exchange-Information&lt;/a&gt;
     */
    public String exchangeInfo() {
<span class="fc" id="L144">        return requestHandler.sendPublicRequest(productUrl, EXCHANGE_INFO, null, HttpMethod.GET, showLimitUsage);</span>
    }

<span class="fc" id="L147">    private final String DEPTH = &quot;/v1/depth&quot;;</span>
    /**
     * GET /v1/depth
     * &lt;br&gt;
     * @param
     * parameters LinkedHashedMap of String,Object pair
     *            where String is the name of the parameter and Object is the value of the parameter
     * &lt;br&gt;&lt;br&gt;
     * symbol -- mandatory/string &lt;br&gt;
     * limit -- optional/integer -- limit the results
     *            Default 100; max 5000. Valid limits:[5, 10, 20, 50, 100, 500, 1000, 5000] &lt;br&gt;
     * @return String
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Order-Book&quot;&gt;
     *     https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Order-Book&lt;/a&gt;
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/coin-margined-futures/market-data/Order-Book&quot;&gt;
     *     https://developers.binance.com/docs/derivatives/coin-margined-futures/market-data/Order-Book&lt;/a&gt;
     */
    public String depth(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L165">        ParameterChecker.checkParameter(parameters, &quot;symbol&quot;, String.class);</span>
<span class="fc" id="L166">        return requestHandler.sendPublicRequest(productUrl, DEPTH, parameters, HttpMethod.GET, showLimitUsage);</span>
    }

<span class="fc" id="L169">    private final String TRADES = &quot;/v1/trades&quot;;</span>
    /**
     * Get recent trades.
     * &lt;br&gt;&lt;br&gt;
     * GET /v1/trades
     * &lt;br&gt;
     * @param
     * parameters LinkedHashedMap of String,Object pair
     *            where String is the name of the parameter and Object is the value of the parameter
     * &lt;br&gt;&lt;br&gt;
     * symbol -- mandatory/string &lt;br&gt;
     * limit -- optional/integer -- limit the results Default 500; max 1000 &lt;br&gt;
     * @return String
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Recent-Trades-List&quot;&gt;
     *     https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Recent-Trades-List&lt;/a&gt;
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/coin-margined-futures/market-data/Recent-Trades-List&quot;&gt;
     *     https://developers.binance.com/docs/derivatives/coin-margined-futures/market-data/Recent-Trades-List&lt;/a&gt;
     */
    public String trades(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L188">        ParameterChecker.checkParameter(parameters, &quot;symbol&quot;, String.class);</span>
<span class="fc" id="L189">        return requestHandler.sendPublicRequest(productUrl, TRADES, parameters, HttpMethod.GET, showLimitUsage);</span>
    }

<span class="fc" id="L192">    private final String HISTORICAL_TRADES = &quot;/v1/historicalTrades&quot;;</span>
    /**
     * Get older market trades.
     * &lt;br&gt;&lt;br&gt;
     * GET /v1/historicalTrades
     * &lt;br&gt;
     * @param
     * parameters LinkedHashedMap of String,Object pair
     *            where String is the name of the parameter and Object is the value of the parameter
     * &lt;br&gt;&lt;br&gt;
     * symbol -- mandatory/string &lt;br&gt;
     * limit -- optional/integer -- limit the result Default 500; max 1000 &lt;br&gt;
     * fromId -- optional/long -- trade id to fetch from. Default gets most recent trades &lt;br&gt;
     * @return String
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Old-Trades-Lookup&quot;&gt;
     *     https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Old-Trades-Lookup&lt;/a&gt;
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/coin-margined-futures/market-data/Old-Trades-Lookup&quot;&gt;
     *     https://developers.binance.com/docs/derivatives/coin-margined-futures/market-data/Old-Trades-Lookup&lt;/a&gt;
     *
     */
    public String historicalTrades(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L213">        ParameterChecker.checkParameter(parameters, &quot;symbol&quot;, String.class);</span>
<span class="fc" id="L214">        return requestHandler.sendWithApiKeyRequest(productUrl, HISTORICAL_TRADES, parameters, HttpMethod.GET, showLimitUsage);</span>
    }

<span class="fc" id="L217">    private final String AGG_TRADES = &quot;/v1/aggTrades&quot;;</span>
    /**
     * Get compressed, aggregate trades. Trades that fill at the time, from the same order,
     * with the same price will have the quantity aggregated.
     * &lt;br&gt;&lt;br&gt;
     * GET /v1/aggTrades
     * &lt;br&gt;
     * @param
     * parameters LinkedHashedMap of String,Object pair
     *            where String is the name of the parameter and Object is the value of the parameter
     * &lt;br&gt;&lt;br&gt;
     * symbol -- mandatory/string &lt;br&gt;
     * fromId -- optional/long -- id to get aggregate trades from INCLUSIVE &lt;br&gt;
     * startTime -- optional/long -- Timestamp in ms to get aggregate trades from INCLUSIVE &lt;br&gt;
     * endTime -- optional/long -- Timestamp in ms to get aggregate trades until INCLUSIVE &lt;br&gt;
     * limit -- optional/integer -- limit the results Default 500; max 1000 &lt;br&gt;
     * @return String
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Compressed-Aggregate-Trades-List&quot;&gt;
     *     https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Compressed-Aggregate-Trades-List&lt;/a&gt;
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/coin-margined-futures/market-data/Compressed-Aggregate-Trades-List&quot;&gt;
     *     https://developers.binance.com/docs/derivatives/coin-margined-futures/market-data/Compressed-Aggregate-Trades-List&lt;/a&gt;
     */
    public String aggTrades(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L240">        ParameterChecker.checkParameter(parameters, &quot;symbol&quot;, String.class);</span>
<span class="fc" id="L241">        return requestHandler.sendPublicRequest(productUrl, AGG_TRADES, parameters, HttpMethod.GET, showLimitUsage);</span>
    }

<span class="fc" id="L244">    private final String KLINES = &quot;/v1/klines&quot;;</span>
    /**
     * Kline/candlestick bars for a symbol.
     * Klines are uniquely identified by their open time.
     * &lt;br&gt;&lt;br&gt;
     * GET /v1/klines
     * &lt;br&gt;
     * @param
     * parameters LinkedHashedMap of String,Object pair
     *            where String is the name of the parameter and Object is the value of the parameter
     * &lt;br&gt;&lt;br&gt;
     * symbol -- mandatory/string &lt;br&gt;
     * interval -- mandatory/string &lt;br&gt;
     * startTime -- optional/long &lt;br&gt;
     * endTime -- optional/long &lt;br&gt;
     * limit -- optional/integer -- limit the results Default 500; max 1000 &lt;br&gt;
     * @return String
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Kline-Candlestick-Data&quot;&gt;
     *     https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Kline-Candlestick-Data&lt;/a&gt;
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/coin-margined-futures/market-data/Kline-Candlestick-Data&quot;&gt;
     *     https://developers.binance.com/docs/derivatives/coin-margined-futures/market-data/Kline-Candlestick-Data&lt;/a&gt;
     */
    public String klines(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L267">        ParameterChecker.checkParameter(parameters, &quot;symbol&quot;, String.class);</span>
<span class="fc" id="L268">        ParameterChecker.checkParameter(parameters, &quot;interval&quot;, String.class);</span>
<span class="fc" id="L269">        return requestHandler.sendPublicRequest(productUrl, KLINES, parameters, HttpMethod.GET, showLimitUsage);</span>
    }

<span class="fc" id="L272">    private final String CONTINUOUSKLINES = &quot;/v1/continuousKlines&quot;;</span>
    /**
     * Kline/candlestick bars for a specific contract type.
     * Klines are uniquely identified by their open time.
     * &lt;br&gt;&lt;br&gt;
     * GET /v1/continuousKlines
     * &lt;br&gt;
     * @param
     * parameters LinkedHashedMap of String,Object pair
     *            where String is the name of the parameter and Object is the value of the parameter
     * &lt;br&gt;&lt;br&gt;
     * pair -- mandatory/string &lt;br&gt;
     * contractType -- mandatory/enum &lt;br&gt;
     * interval -- mandatory/enum &lt;br&gt;
     * startTime -- optional/long &lt;br&gt;
     * endTime -- optional/long &lt;br&gt;
     * limit -- optional/integer -- limit the results Default 500; max 1000 &lt;br&gt;
     * @return String
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Continuous-Contract-Kline-Candlestick-Data&quot;&gt;
     *     https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Continuous-Contract-Kline-Candlestick-Data&lt;/a&gt;
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/coin-margined-futures/market-data/Continuous-Contract-Kline-Candlestick-Data&quot;&gt;
     *     https://developers.binance.com/docs/derivatives/coin-margined-futures/market-data/Continuous-Contract-Kline-Candlestick-Data&lt;/a&gt;
     */
    public String continuousKlines(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L296">        ParameterChecker.checkParameter(parameters, &quot;pair&quot;, String.class);</span>
<span class="fc" id="L297">        ParameterChecker.checkParameter(parameters, &quot;contractType&quot;, String.class);</span>
<span class="fc" id="L298">        ParameterChecker.checkParameter(parameters, &quot;interval&quot;, String.class);</span>
<span class="fc" id="L299">        return requestHandler.sendPublicRequest(productUrl, CONTINUOUSKLINES, parameters, HttpMethod.GET, showLimitUsage);</span>
    }

<span class="fc" id="L302">    private final String INDEXPRICEKLINES = &quot;/v1/indexPriceKlines&quot;;</span>
    /**
     * Kline/candlestick bars for the index price of a pair.
     * Klines are uniquely identified by their open time.
     * &lt;br&gt;&lt;br&gt;
     * GET /v1/indexPriceKlines
     * &lt;br&gt;
     * @param
     * parameters LinkedHashedMap of String,Object pair
     *            where String is the name of the parameter and Object is the value of the parameter
     * &lt;br&gt;&lt;br&gt;
     * pair -- mandatory/string &lt;br&gt;
     * interval -- mandatory/enum &lt;br&gt;
     * startTime -- optional/long &lt;br&gt;
     * endTime -- optional/long &lt;br&gt;
     * limit -- optional/integer -- limit the results Default 500; max 1000 &lt;br&gt;
     * @return String
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Index-Price-Kline-Candlestick-Data&quot;&gt;
     *     https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Index-Price-Kline-Candlestick-Data&lt;/a&gt;
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/coin-margined-futures/market-data/Index-Price-Kline-Candlestick-Data&quot;&gt;
     *     https://developers.binance.com/docs/derivatives/coin-margined-futures/market-data/Index-Price-Kline-Candlestick-Data&lt;/a&gt;
     */
    public String indexPriceKlines(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L325">        ParameterChecker.checkParameter(parameters, &quot;pair&quot;, String.class);</span>
<span class="fc" id="L326">        ParameterChecker.checkParameter(parameters, &quot;interval&quot;, String.class);</span>
<span class="fc" id="L327">        return requestHandler.sendPublicRequest(productUrl, INDEXPRICEKLINES, parameters, HttpMethod.GET, showLimitUsage);</span>
    }

<span class="fc" id="L330">    private final String MARKPRICEKLINES = &quot;/v1/markPriceKlines&quot;;</span>
    /**
     * Kline/candlestick bars for the mark price of a symbol.
     * Klines are uniquely identified by their open time.
     * &lt;br&gt;&lt;br&gt;
     * GET /v1/markPriceKlines
     * &lt;br&gt;
     * @param
     * parameters LinkedHashedMap of String,Object pair
     *            where String is the name of the parameter and Object is the value of the parameter
     * &lt;br&gt;&lt;br&gt;
     * symbol -- mandatory/string &lt;br&gt;
     * interval -- mandatory/enum &lt;br&gt;
     * startTime -- optional/long &lt;br&gt;
     * endTime -- optional/long &lt;br&gt;
     * limit -- optional/integer -- limit the results Default 500; max 1000 &lt;br&gt;
     * @return String
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Mark-Price-Kline-Candlestick-Data&quot;&gt;
     *     https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Mark-Price-Kline-Candlestick-Data&lt;/a&gt;
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/coin-margined-futures/market-data/Mark-Price-Kline-Candlestick-Data&quot;&gt;
     *     https://developers.binance.com/docs/derivatives/coin-margined-futures/market-data/Mark-Price-Kline-Candlestick-Data&lt;/a&gt;
     */
    public String markPriceKlines(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L353">        ParameterChecker.checkParameter(parameters, &quot;symbol&quot;, String.class);</span>
<span class="fc" id="L354">        ParameterChecker.checkParameter(parameters, &quot;interval&quot;, String.class);</span>
<span class="fc" id="L355">        return requestHandler.sendPublicRequest(productUrl, MARKPRICEKLINES, parameters, HttpMethod.GET, showLimitUsage);</span>
    }

<span class="fc" id="L358">    private final String FUNDING_RATE = &quot;/v1/fundingRate&quot;;</span>
    /**
     * Get funding rate history
     * &lt;br&gt;&lt;br&gt;
     * GET /v1/fundingRate
     * &lt;br&gt;
     * @param
     * parameters LinkedHashedMap of String,Object pair
     *            where String is the name of the parameter and Object is the value of the parameter
     * &lt;br&gt;&lt;br&gt;
     * symbol -- optional/string -- the trading pair &lt;br&gt;
     * startTime -- optional/long -- Timestamp in ms to get funding rate from INCLUSIVE. &lt;br&gt;
     * endTime -- optional/long -- Timestamp in ms to get funding rate until INCLUSIVE. &lt;br&gt;
     * limit -- optional/int -- Default 100; max 1000 &lt;br&gt;
     * @return String
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Get-Funding-Rate-History&quot;&gt;
     *     https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Get-Funding-Rate-History&lt;/a&gt;
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/coin-margined-futures/market-data/Get-Funding-Rate-History-of-Perpetual-Futures&quot;&gt;
     *     https://developers.binance.com/docs/derivatives/coin-margined-futures/market-data/Get-Funding-Rate-History-of-Perpetual-Futures&lt;/a&gt;
     */
    public String fundingRate(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L379">        return requestHandler.sendPublicRequest(productUrl, FUNDING_RATE, parameters, HttpMethod.GET, showLimitUsage);</span>
    }

<span class="fc" id="L382">    private final String OPEN_INTEREST = &quot;/v1/openInterest&quot;;</span>
    /**
     * Get present open interest of a specific symbol.
     * &lt;br&gt;&lt;br&gt;
     * GET /v1/openInterest
     * &lt;br&gt;
     * https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Open-Interest
     * @param
     * parameters LinkedHashedMap of String,Object pair
     *            where String is the name of the parameter and Object is the value of the parameter
     * &lt;br&gt;&lt;br&gt;
     * symbol -- mandatory/string -- the trading pair &lt;br&gt;
     * @return String
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Open-Interest&quot;&gt;
     *     https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Open-Interest&lt;/a&gt;
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/coin-margined-futures/market-data/Open-Interest&quot;&gt;
     *     https://developers.binance.com/docs/derivatives/coin-margined-futures/market-data/Open-Interest&lt;/a&gt;
     */
    public String openInterest(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L401">        ParameterChecker.checkParameter(parameters, &quot;symbol&quot;, String.class);</span>
<span class="fc" id="L402">        return requestHandler.sendPublicRequest(productUrl, OPEN_INTEREST, parameters, HttpMethod.GET, showLimitUsage);</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.7.202105040129</span></div></body></html>