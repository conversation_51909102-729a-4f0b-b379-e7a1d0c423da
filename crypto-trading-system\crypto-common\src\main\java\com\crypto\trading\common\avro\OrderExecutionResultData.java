/**
 * Autogenerated by Avro
 *
 * DO NOT EDIT DIRECTLY
 */
package com.crypto.trading.common.avro;

import org.apache.avro.generic.GenericArray;
import org.apache.avro.specific.SpecificData;
import org.apache.avro.util.Utf8;
import org.apache.avro.message.BinaryMessageEncoder;
import org.apache.avro.message.BinaryMessageDecoder;
import org.apache.avro.message.SchemaStore;

@org.apache.avro.specific.AvroGenerated
public class OrderExecutionResultData extends org.apache.avro.specific.SpecificRecordBase implements org.apache.avro.specific.SpecificRecord {
  private static final long serialVersionUID = 3646858227814085319L;


  public static final org.apache.avro.Schema SCHEMA$ = new org.apache.avro.Schema.Parser().parse("{\"type\":\"record\",\"name\":\"OrderExecutionResultData\",\"namespace\":\"com.crypto.trading.common.avro\",\"fields\":[{\"name\":\"orderId\",\"type\":\"long\",\"doc\":\"订单ID\"},{\"name\":\"clientOrderId\",\"type\":[{\"type\":\"string\",\"avro.java.string\":\"String\"},\"null\"],\"doc\":\"客户端订单ID\"},{\"name\":\"symbol\",\"type\":{\"type\":\"string\",\"avro.java.string\":\"String\"},\"doc\":\"交易对符号\"},{\"name\":\"accountId\",\"type\":{\"type\":\"string\",\"avro.java.string\":\"String\"},\"doc\":\"账户ID\"},{\"name\":\"price\",\"type\":[\"double\",\"null\"],\"doc\":\"订单价格\"},{\"name\":\"origQty\",\"type\":\"double\",\"doc\":\"原始数量\"},{\"name\":\"executedQty\",\"type\":\"double\",\"doc\":\"已执行数量\"},{\"name\":\"cummulativeQuoteQty\",\"type\":[\"double\",\"null\"],\"doc\":\"累计成交金额\"},{\"name\":\"status\",\"type\":{\"type\":\"string\",\"avro.java.string\":\"String\"},\"doc\":\"订单状态\"},{\"name\":\"type\",\"type\":{\"type\":\"string\",\"avro.java.string\":\"String\"},\"doc\":\"订单类型\"},{\"name\":\"side\",\"type\":{\"type\":\"string\",\"avro.java.string\":\"String\"},\"doc\":\"订单方向\"},{\"name\":\"positionSide\",\"type\":[{\"type\":\"string\",\"avro.java.string\":\"String\"},\"null\"],\"doc\":\"持仓方向\"},{\"name\":\"stopPrice\",\"type\":[\"double\",\"null\"],\"doc\":\"止损价格\"},{\"name\":\"avgPrice\",\"type\":[\"double\",\"null\"],\"doc\":\"平均成交价格\"},{\"name\":\"commissionAsset\",\"type\":[{\"type\":\"string\",\"avro.java.string\":\"String\"},\"null\"],\"doc\":\"手续费资产\"},{\"name\":\"commissionAmount\",\"type\":[\"double\",\"null\"],\"doc\":\"手续费金额\"},{\"name\":\"time\",\"type\":\"long\",\"doc\":\"订单时间(毫秒)\"},{\"name\":\"updateTime\",\"type\":\"long\",\"doc\":\"更新时间(毫秒)\"},{\"name\":\"isWorking\",\"type\":\"boolean\",\"doc\":\"订单是否生效中\"},{\"name\":\"fills\",\"type\":[{\"type\":\"string\",\"avro.java.string\":\"String\"},\"null\"],\"doc\":\"成交明细JSON\"},{\"name\":\"errorCode\",\"type\":[\"int\",\"null\"],\"doc\":\"错误代码\"},{\"name\":\"errorMessage\",\"type\":[{\"type\":\"string\",\"avro.java.string\":\"String\"},\"null\"],\"doc\":\"错误信息\"}]}");
  public static org.apache.avro.Schema getClassSchema() { return SCHEMA$; }

  private static final SpecificData MODEL$ = new SpecificData();

  private static final BinaryMessageEncoder<OrderExecutionResultData> ENCODER =
      new BinaryMessageEncoder<>(MODEL$, SCHEMA$);

  private static final BinaryMessageDecoder<OrderExecutionResultData> DECODER =
      new BinaryMessageDecoder<>(MODEL$, SCHEMA$);

  /**
   * Return the BinaryMessageEncoder instance used by this class.
   * @return the message encoder used by this class
   */
  public static BinaryMessageEncoder<OrderExecutionResultData> getEncoder() {
    return ENCODER;
  }

  /**
   * Return the BinaryMessageDecoder instance used by this class.
   * @return the message decoder used by this class
   */
  public static BinaryMessageDecoder<OrderExecutionResultData> getDecoder() {
    return DECODER;
  }

  /**
   * Create a new BinaryMessageDecoder instance for this class that uses the specified {@link SchemaStore}.
   * @param resolver a {@link SchemaStore} used to find schemas by fingerprint
   * @return a BinaryMessageDecoder instance for this class backed by the given SchemaStore
   */
  public static BinaryMessageDecoder<OrderExecutionResultData> createDecoder(SchemaStore resolver) {
    return new BinaryMessageDecoder<>(MODEL$, SCHEMA$, resolver);
  }

  /**
   * Serializes this OrderExecutionResultData to a ByteBuffer.
   * @return a buffer holding the serialized data for this instance
   * @throws java.io.IOException if this instance could not be serialized
   */
  public java.nio.ByteBuffer toByteBuffer() throws java.io.IOException {
    return ENCODER.encode(this);
  }

  /**
   * Deserializes a OrderExecutionResultData from a ByteBuffer.
   * @param b a byte buffer holding serialized data for an instance of this class
   * @return a OrderExecutionResultData instance decoded from the given buffer
   * @throws java.io.IOException if the given bytes could not be deserialized into an instance of this class
   */
  public static OrderExecutionResultData fromByteBuffer(
      java.nio.ByteBuffer b) throws java.io.IOException {
    return DECODER.decode(b);
  }

  /** 订单ID */
  private long orderId;
  /** 客户端订单ID */
  private java.lang.String clientOrderId;
  /** 交易对符号 */
  private java.lang.String symbol;
  /** 账户ID */
  private java.lang.String accountId;
  /** 订单价格 */
  private java.lang.Double price;
  /** 原始数量 */
  private double origQty;
  /** 已执行数量 */
  private double executedQty;
  /** 累计成交金额 */
  private java.lang.Double cummulativeQuoteQty;
  /** 订单状态 */
  private java.lang.String status;
  /** 订单类型 */
  private java.lang.String type;
  /** 订单方向 */
  private java.lang.String side;
  /** 持仓方向 */
  private java.lang.String positionSide;
  /** 止损价格 */
  private java.lang.Double stopPrice;
  /** 平均成交价格 */
  private java.lang.Double avgPrice;
  /** 手续费资产 */
  private java.lang.String commissionAsset;
  /** 手续费金额 */
  private java.lang.Double commissionAmount;
  /** 订单时间(毫秒) */
  private long time;
  /** 更新时间(毫秒) */
  private long updateTime;
  /** 订单是否生效中 */
  private boolean isWorking;
  /** 成交明细JSON */
  private java.lang.String fills;
  /** 错误代码 */
  private java.lang.Integer errorCode;
  /** 错误信息 */
  private java.lang.String errorMessage;

  /**
   * Default constructor.  Note that this does not initialize fields
   * to their default values from the schema.  If that is desired then
   * one should use <code>newBuilder()</code>.
   */
  public OrderExecutionResultData() {}

  /**
   * All-args constructor.
   * @param orderId 订单ID
   * @param clientOrderId 客户端订单ID
   * @param symbol 交易对符号
   * @param accountId 账户ID
   * @param price 订单价格
   * @param origQty 原始数量
   * @param executedQty 已执行数量
   * @param cummulativeQuoteQty 累计成交金额
   * @param status 订单状态
   * @param type 订单类型
   * @param side 订单方向
   * @param positionSide 持仓方向
   * @param stopPrice 止损价格
   * @param avgPrice 平均成交价格
   * @param commissionAsset 手续费资产
   * @param commissionAmount 手续费金额
   * @param time 订单时间(毫秒)
   * @param updateTime 更新时间(毫秒)
   * @param isWorking 订单是否生效中
   * @param fills 成交明细JSON
   * @param errorCode 错误代码
   * @param errorMessage 错误信息
   */
  public OrderExecutionResultData(java.lang.Long orderId, java.lang.String clientOrderId, java.lang.String symbol, java.lang.String accountId, java.lang.Double price, java.lang.Double origQty, java.lang.Double executedQty, java.lang.Double cummulativeQuoteQty, java.lang.String status, java.lang.String type, java.lang.String side, java.lang.String positionSide, java.lang.Double stopPrice, java.lang.Double avgPrice, java.lang.String commissionAsset, java.lang.Double commissionAmount, java.lang.Long time, java.lang.Long updateTime, java.lang.Boolean isWorking, java.lang.String fills, java.lang.Integer errorCode, java.lang.String errorMessage) {
    this.orderId = orderId;
    this.clientOrderId = clientOrderId;
    this.symbol = symbol;
    this.accountId = accountId;
    this.price = price;
    this.origQty = origQty;
    this.executedQty = executedQty;
    this.cummulativeQuoteQty = cummulativeQuoteQty;
    this.status = status;
    this.type = type;
    this.side = side;
    this.positionSide = positionSide;
    this.stopPrice = stopPrice;
    this.avgPrice = avgPrice;
    this.commissionAsset = commissionAsset;
    this.commissionAmount = commissionAmount;
    this.time = time;
    this.updateTime = updateTime;
    this.isWorking = isWorking;
    this.fills = fills;
    this.errorCode = errorCode;
    this.errorMessage = errorMessage;
  }

  @Override
  public org.apache.avro.specific.SpecificData getSpecificData() { return MODEL$; }

  @Override
  public org.apache.avro.Schema getSchema() { return SCHEMA$; }

  // Used by DatumWriter.  Applications should not call.
  @Override
  public java.lang.Object get(int field$) {
    switch (field$) {
    case 0: return orderId;
    case 1: return clientOrderId;
    case 2: return symbol;
    case 3: return accountId;
    case 4: return price;
    case 5: return origQty;
    case 6: return executedQty;
    case 7: return cummulativeQuoteQty;
    case 8: return status;
    case 9: return type;
    case 10: return side;
    case 11: return positionSide;
    case 12: return stopPrice;
    case 13: return avgPrice;
    case 14: return commissionAsset;
    case 15: return commissionAmount;
    case 16: return time;
    case 17: return updateTime;
    case 18: return isWorking;
    case 19: return fills;
    case 20: return errorCode;
    case 21: return errorMessage;
    default: throw new IndexOutOfBoundsException("Invalid index: " + field$);
    }
  }

  // Used by DatumReader.  Applications should not call.
  @Override
  @SuppressWarnings(value="unchecked")
  public void put(int field$, java.lang.Object value$) {
    switch (field$) {
    case 0: orderId = (java.lang.Long)value$; break;
    case 1: clientOrderId = value$ != null ? value$.toString() : null; break;
    case 2: symbol = value$ != null ? value$.toString() : null; break;
    case 3: accountId = value$ != null ? value$.toString() : null; break;
    case 4: price = (java.lang.Double)value$; break;
    case 5: origQty = (java.lang.Double)value$; break;
    case 6: executedQty = (java.lang.Double)value$; break;
    case 7: cummulativeQuoteQty = (java.lang.Double)value$; break;
    case 8: status = value$ != null ? value$.toString() : null; break;
    case 9: type = value$ != null ? value$.toString() : null; break;
    case 10: side = value$ != null ? value$.toString() : null; break;
    case 11: positionSide = value$ != null ? value$.toString() : null; break;
    case 12: stopPrice = (java.lang.Double)value$; break;
    case 13: avgPrice = (java.lang.Double)value$; break;
    case 14: commissionAsset = value$ != null ? value$.toString() : null; break;
    case 15: commissionAmount = (java.lang.Double)value$; break;
    case 16: time = (java.lang.Long)value$; break;
    case 17: updateTime = (java.lang.Long)value$; break;
    case 18: isWorking = (java.lang.Boolean)value$; break;
    case 19: fills = value$ != null ? value$.toString() : null; break;
    case 20: errorCode = (java.lang.Integer)value$; break;
    case 21: errorMessage = value$ != null ? value$.toString() : null; break;
    default: throw new IndexOutOfBoundsException("Invalid index: " + field$);
    }
  }

  /**
   * Gets the value of the 'orderId' field.
   * @return 订单ID
   */
  public long getOrderId() {
    return orderId;
  }


  /**
   * Sets the value of the 'orderId' field.
   * 订单ID
   * @param value the value to set.
   */
  public void setOrderId(long value) {
    this.orderId = value;
  }

  /**
   * Gets the value of the 'clientOrderId' field.
   * @return 客户端订单ID
   */
  public java.lang.String getClientOrderId() {
    return clientOrderId;
  }


  /**
   * Sets the value of the 'clientOrderId' field.
   * 客户端订单ID
   * @param value the value to set.
   */
  public void setClientOrderId(java.lang.String value) {
    this.clientOrderId = value;
  }

  /**
   * Gets the value of the 'symbol' field.
   * @return 交易对符号
   */
  public java.lang.String getSymbol() {
    return symbol;
  }


  /**
   * Sets the value of the 'symbol' field.
   * 交易对符号
   * @param value the value to set.
   */
  public void setSymbol(java.lang.String value) {
    this.symbol = value;
  }

  /**
   * Gets the value of the 'accountId' field.
   * @return 账户ID
   */
  public java.lang.String getAccountId() {
    return accountId;
  }


  /**
   * Sets the value of the 'accountId' field.
   * 账户ID
   * @param value the value to set.
   */
  public void setAccountId(java.lang.String value) {
    this.accountId = value;
  }

  /**
   * Gets the value of the 'price' field.
   * @return 订单价格
   */
  public java.lang.Double getPrice() {
    return price;
  }


  /**
   * Sets the value of the 'price' field.
   * 订单价格
   * @param value the value to set.
   */
  public void setPrice(java.lang.Double value) {
    this.price = value;
  }

  /**
   * Gets the value of the 'origQty' field.
   * @return 原始数量
   */
  public double getOrigQty() {
    return origQty;
  }


  /**
   * Sets the value of the 'origQty' field.
   * 原始数量
   * @param value the value to set.
   */
  public void setOrigQty(double value) {
    this.origQty = value;
  }

  /**
   * Gets the value of the 'executedQty' field.
   * @return 已执行数量
   */
  public double getExecutedQty() {
    return executedQty;
  }


  /**
   * Sets the value of the 'executedQty' field.
   * 已执行数量
   * @param value the value to set.
   */
  public void setExecutedQty(double value) {
    this.executedQty = value;
  }

  /**
   * Gets the value of the 'cummulativeQuoteQty' field.
   * @return 累计成交金额
   */
  public java.lang.Double getCummulativeQuoteQty() {
    return cummulativeQuoteQty;
  }


  /**
   * Sets the value of the 'cummulativeQuoteQty' field.
   * 累计成交金额
   * @param value the value to set.
   */
  public void setCummulativeQuoteQty(java.lang.Double value) {
    this.cummulativeQuoteQty = value;
  }

  /**
   * Gets the value of the 'status' field.
   * @return 订单状态
   */
  public java.lang.String getStatus() {
    return status;
  }


  /**
   * Sets the value of the 'status' field.
   * 订单状态
   * @param value the value to set.
   */
  public void setStatus(java.lang.String value) {
    this.status = value;
  }

  /**
   * Gets the value of the 'type' field.
   * @return 订单类型
   */
  public java.lang.String getType() {
    return type;
  }


  /**
   * Sets the value of the 'type' field.
   * 订单类型
   * @param value the value to set.
   */
  public void setType(java.lang.String value) {
    this.type = value;
  }

  /**
   * Gets the value of the 'side' field.
   * @return 订单方向
   */
  public java.lang.String getSide() {
    return side;
  }


  /**
   * Sets the value of the 'side' field.
   * 订单方向
   * @param value the value to set.
   */
  public void setSide(java.lang.String value) {
    this.side = value;
  }

  /**
   * Gets the value of the 'positionSide' field.
   * @return 持仓方向
   */
  public java.lang.String getPositionSide() {
    return positionSide;
  }


  /**
   * Sets the value of the 'positionSide' field.
   * 持仓方向
   * @param value the value to set.
   */
  public void setPositionSide(java.lang.String value) {
    this.positionSide = value;
  }

  /**
   * Gets the value of the 'stopPrice' field.
   * @return 止损价格
   */
  public java.lang.Double getStopPrice() {
    return stopPrice;
  }


  /**
   * Sets the value of the 'stopPrice' field.
   * 止损价格
   * @param value the value to set.
   */
  public void setStopPrice(java.lang.Double value) {
    this.stopPrice = value;
  }

  /**
   * Gets the value of the 'avgPrice' field.
   * @return 平均成交价格
   */
  public java.lang.Double getAvgPrice() {
    return avgPrice;
  }


  /**
   * Sets the value of the 'avgPrice' field.
   * 平均成交价格
   * @param value the value to set.
   */
  public void setAvgPrice(java.lang.Double value) {
    this.avgPrice = value;
  }

  /**
   * Gets the value of the 'commissionAsset' field.
   * @return 手续费资产
   */
  public java.lang.String getCommissionAsset() {
    return commissionAsset;
  }


  /**
   * Sets the value of the 'commissionAsset' field.
   * 手续费资产
   * @param value the value to set.
   */
  public void setCommissionAsset(java.lang.String value) {
    this.commissionAsset = value;
  }

  /**
   * Gets the value of the 'commissionAmount' field.
   * @return 手续费金额
   */
  public java.lang.Double getCommissionAmount() {
    return commissionAmount;
  }


  /**
   * Sets the value of the 'commissionAmount' field.
   * 手续费金额
   * @param value the value to set.
   */
  public void setCommissionAmount(java.lang.Double value) {
    this.commissionAmount = value;
  }

  /**
   * Gets the value of the 'time' field.
   * @return 订单时间(毫秒)
   */
  public long getTime() {
    return time;
  }


  /**
   * Sets the value of the 'time' field.
   * 订单时间(毫秒)
   * @param value the value to set.
   */
  public void setTime(long value) {
    this.time = value;
  }

  /**
   * Gets the value of the 'updateTime' field.
   * @return 更新时间(毫秒)
   */
  public long getUpdateTime() {
    return updateTime;
  }


  /**
   * Sets the value of the 'updateTime' field.
   * 更新时间(毫秒)
   * @param value the value to set.
   */
  public void setUpdateTime(long value) {
    this.updateTime = value;
  }

  /**
   * Gets the value of the 'isWorking' field.
   * @return 订单是否生效中
   */
  public boolean getIsWorking() {
    return isWorking;
  }


  /**
   * Sets the value of the 'isWorking' field.
   * 订单是否生效中
   * @param value the value to set.
   */
  public void setIsWorking(boolean value) {
    this.isWorking = value;
  }

  /**
   * Gets the value of the 'fills' field.
   * @return 成交明细JSON
   */
  public java.lang.String getFills() {
    return fills;
  }


  /**
   * Sets the value of the 'fills' field.
   * 成交明细JSON
   * @param value the value to set.
   */
  public void setFills(java.lang.String value) {
    this.fills = value;
  }

  /**
   * Gets the value of the 'errorCode' field.
   * @return 错误代码
   */
  public java.lang.Integer getErrorCode() {
    return errorCode;
  }


  /**
   * Sets the value of the 'errorCode' field.
   * 错误代码
   * @param value the value to set.
   */
  public void setErrorCode(java.lang.Integer value) {
    this.errorCode = value;
  }

  /**
   * Gets the value of the 'errorMessage' field.
   * @return 错误信息
   */
  public java.lang.String getErrorMessage() {
    return errorMessage;
  }


  /**
   * Sets the value of the 'errorMessage' field.
   * 错误信息
   * @param value the value to set.
   */
  public void setErrorMessage(java.lang.String value) {
    this.errorMessage = value;
  }

  /**
   * Creates a new OrderExecutionResultData RecordBuilder.
   * @return A new OrderExecutionResultData RecordBuilder
   */
  public static com.crypto.trading.common.avro.OrderExecutionResultData.Builder newBuilder() {
    return new com.crypto.trading.common.avro.OrderExecutionResultData.Builder();
  }

  /**
   * Creates a new OrderExecutionResultData RecordBuilder by copying an existing Builder.
   * @param other The existing builder to copy.
   * @return A new OrderExecutionResultData RecordBuilder
   */
  public static com.crypto.trading.common.avro.OrderExecutionResultData.Builder newBuilder(com.crypto.trading.common.avro.OrderExecutionResultData.Builder other) {
    if (other == null) {
      return new com.crypto.trading.common.avro.OrderExecutionResultData.Builder();
    } else {
      return new com.crypto.trading.common.avro.OrderExecutionResultData.Builder(other);
    }
  }

  /**
   * Creates a new OrderExecutionResultData RecordBuilder by copying an existing OrderExecutionResultData instance.
   * @param other The existing instance to copy.
   * @return A new OrderExecutionResultData RecordBuilder
   */
  public static com.crypto.trading.common.avro.OrderExecutionResultData.Builder newBuilder(com.crypto.trading.common.avro.OrderExecutionResultData other) {
    if (other == null) {
      return new com.crypto.trading.common.avro.OrderExecutionResultData.Builder();
    } else {
      return new com.crypto.trading.common.avro.OrderExecutionResultData.Builder(other);
    }
  }

  /**
   * RecordBuilder for OrderExecutionResultData instances.
   */
  @org.apache.avro.specific.AvroGenerated
  public static class Builder extends org.apache.avro.specific.SpecificRecordBuilderBase<OrderExecutionResultData>
    implements org.apache.avro.data.RecordBuilder<OrderExecutionResultData> {

    /** 订单ID */
    private long orderId;
    /** 客户端订单ID */
    private java.lang.String clientOrderId;
    /** 交易对符号 */
    private java.lang.String symbol;
    /** 账户ID */
    private java.lang.String accountId;
    /** 订单价格 */
    private java.lang.Double price;
    /** 原始数量 */
    private double origQty;
    /** 已执行数量 */
    private double executedQty;
    /** 累计成交金额 */
    private java.lang.Double cummulativeQuoteQty;
    /** 订单状态 */
    private java.lang.String status;
    /** 订单类型 */
    private java.lang.String type;
    /** 订单方向 */
    private java.lang.String side;
    /** 持仓方向 */
    private java.lang.String positionSide;
    /** 止损价格 */
    private java.lang.Double stopPrice;
    /** 平均成交价格 */
    private java.lang.Double avgPrice;
    /** 手续费资产 */
    private java.lang.String commissionAsset;
    /** 手续费金额 */
    private java.lang.Double commissionAmount;
    /** 订单时间(毫秒) */
    private long time;
    /** 更新时间(毫秒) */
    private long updateTime;
    /** 订单是否生效中 */
    private boolean isWorking;
    /** 成交明细JSON */
    private java.lang.String fills;
    /** 错误代码 */
    private java.lang.Integer errorCode;
    /** 错误信息 */
    private java.lang.String errorMessage;

    /** Creates a new Builder */
    private Builder() {
      super(SCHEMA$, MODEL$);
    }

    /**
     * Creates a Builder by copying an existing Builder.
     * @param other The existing Builder to copy.
     */
    private Builder(com.crypto.trading.common.avro.OrderExecutionResultData.Builder other) {
      super(other);
      if (isValidValue(fields()[0], other.orderId)) {
        this.orderId = data().deepCopy(fields()[0].schema(), other.orderId);
        fieldSetFlags()[0] = other.fieldSetFlags()[0];
      }
      if (isValidValue(fields()[1], other.clientOrderId)) {
        this.clientOrderId = data().deepCopy(fields()[1].schema(), other.clientOrderId);
        fieldSetFlags()[1] = other.fieldSetFlags()[1];
      }
      if (isValidValue(fields()[2], other.symbol)) {
        this.symbol = data().deepCopy(fields()[2].schema(), other.symbol);
        fieldSetFlags()[2] = other.fieldSetFlags()[2];
      }
      if (isValidValue(fields()[3], other.accountId)) {
        this.accountId = data().deepCopy(fields()[3].schema(), other.accountId);
        fieldSetFlags()[3] = other.fieldSetFlags()[3];
      }
      if (isValidValue(fields()[4], other.price)) {
        this.price = data().deepCopy(fields()[4].schema(), other.price);
        fieldSetFlags()[4] = other.fieldSetFlags()[4];
      }
      if (isValidValue(fields()[5], other.origQty)) {
        this.origQty = data().deepCopy(fields()[5].schema(), other.origQty);
        fieldSetFlags()[5] = other.fieldSetFlags()[5];
      }
      if (isValidValue(fields()[6], other.executedQty)) {
        this.executedQty = data().deepCopy(fields()[6].schema(), other.executedQty);
        fieldSetFlags()[6] = other.fieldSetFlags()[6];
      }
      if (isValidValue(fields()[7], other.cummulativeQuoteQty)) {
        this.cummulativeQuoteQty = data().deepCopy(fields()[7].schema(), other.cummulativeQuoteQty);
        fieldSetFlags()[7] = other.fieldSetFlags()[7];
      }
      if (isValidValue(fields()[8], other.status)) {
        this.status = data().deepCopy(fields()[8].schema(), other.status);
        fieldSetFlags()[8] = other.fieldSetFlags()[8];
      }
      if (isValidValue(fields()[9], other.type)) {
        this.type = data().deepCopy(fields()[9].schema(), other.type);
        fieldSetFlags()[9] = other.fieldSetFlags()[9];
      }
      if (isValidValue(fields()[10], other.side)) {
        this.side = data().deepCopy(fields()[10].schema(), other.side);
        fieldSetFlags()[10] = other.fieldSetFlags()[10];
      }
      if (isValidValue(fields()[11], other.positionSide)) {
        this.positionSide = data().deepCopy(fields()[11].schema(), other.positionSide);
        fieldSetFlags()[11] = other.fieldSetFlags()[11];
      }
      if (isValidValue(fields()[12], other.stopPrice)) {
        this.stopPrice = data().deepCopy(fields()[12].schema(), other.stopPrice);
        fieldSetFlags()[12] = other.fieldSetFlags()[12];
      }
      if (isValidValue(fields()[13], other.avgPrice)) {
        this.avgPrice = data().deepCopy(fields()[13].schema(), other.avgPrice);
        fieldSetFlags()[13] = other.fieldSetFlags()[13];
      }
      if (isValidValue(fields()[14], other.commissionAsset)) {
        this.commissionAsset = data().deepCopy(fields()[14].schema(), other.commissionAsset);
        fieldSetFlags()[14] = other.fieldSetFlags()[14];
      }
      if (isValidValue(fields()[15], other.commissionAmount)) {
        this.commissionAmount = data().deepCopy(fields()[15].schema(), other.commissionAmount);
        fieldSetFlags()[15] = other.fieldSetFlags()[15];
      }
      if (isValidValue(fields()[16], other.time)) {
        this.time = data().deepCopy(fields()[16].schema(), other.time);
        fieldSetFlags()[16] = other.fieldSetFlags()[16];
      }
      if (isValidValue(fields()[17], other.updateTime)) {
        this.updateTime = data().deepCopy(fields()[17].schema(), other.updateTime);
        fieldSetFlags()[17] = other.fieldSetFlags()[17];
      }
      if (isValidValue(fields()[18], other.isWorking)) {
        this.isWorking = data().deepCopy(fields()[18].schema(), other.isWorking);
        fieldSetFlags()[18] = other.fieldSetFlags()[18];
      }
      if (isValidValue(fields()[19], other.fills)) {
        this.fills = data().deepCopy(fields()[19].schema(), other.fills);
        fieldSetFlags()[19] = other.fieldSetFlags()[19];
      }
      if (isValidValue(fields()[20], other.errorCode)) {
        this.errorCode = data().deepCopy(fields()[20].schema(), other.errorCode);
        fieldSetFlags()[20] = other.fieldSetFlags()[20];
      }
      if (isValidValue(fields()[21], other.errorMessage)) {
        this.errorMessage = data().deepCopy(fields()[21].schema(), other.errorMessage);
        fieldSetFlags()[21] = other.fieldSetFlags()[21];
      }
    }

    /**
     * Creates a Builder by copying an existing OrderExecutionResultData instance
     * @param other The existing instance to copy.
     */
    private Builder(com.crypto.trading.common.avro.OrderExecutionResultData other) {
      super(SCHEMA$, MODEL$);
      if (isValidValue(fields()[0], other.orderId)) {
        this.orderId = data().deepCopy(fields()[0].schema(), other.orderId);
        fieldSetFlags()[0] = true;
      }
      if (isValidValue(fields()[1], other.clientOrderId)) {
        this.clientOrderId = data().deepCopy(fields()[1].schema(), other.clientOrderId);
        fieldSetFlags()[1] = true;
      }
      if (isValidValue(fields()[2], other.symbol)) {
        this.symbol = data().deepCopy(fields()[2].schema(), other.symbol);
        fieldSetFlags()[2] = true;
      }
      if (isValidValue(fields()[3], other.accountId)) {
        this.accountId = data().deepCopy(fields()[3].schema(), other.accountId);
        fieldSetFlags()[3] = true;
      }
      if (isValidValue(fields()[4], other.price)) {
        this.price = data().deepCopy(fields()[4].schema(), other.price);
        fieldSetFlags()[4] = true;
      }
      if (isValidValue(fields()[5], other.origQty)) {
        this.origQty = data().deepCopy(fields()[5].schema(), other.origQty);
        fieldSetFlags()[5] = true;
      }
      if (isValidValue(fields()[6], other.executedQty)) {
        this.executedQty = data().deepCopy(fields()[6].schema(), other.executedQty);
        fieldSetFlags()[6] = true;
      }
      if (isValidValue(fields()[7], other.cummulativeQuoteQty)) {
        this.cummulativeQuoteQty = data().deepCopy(fields()[7].schema(), other.cummulativeQuoteQty);
        fieldSetFlags()[7] = true;
      }
      if (isValidValue(fields()[8], other.status)) {
        this.status = data().deepCopy(fields()[8].schema(), other.status);
        fieldSetFlags()[8] = true;
      }
      if (isValidValue(fields()[9], other.type)) {
        this.type = data().deepCopy(fields()[9].schema(), other.type);
        fieldSetFlags()[9] = true;
      }
      if (isValidValue(fields()[10], other.side)) {
        this.side = data().deepCopy(fields()[10].schema(), other.side);
        fieldSetFlags()[10] = true;
      }
      if (isValidValue(fields()[11], other.positionSide)) {
        this.positionSide = data().deepCopy(fields()[11].schema(), other.positionSide);
        fieldSetFlags()[11] = true;
      }
      if (isValidValue(fields()[12], other.stopPrice)) {
        this.stopPrice = data().deepCopy(fields()[12].schema(), other.stopPrice);
        fieldSetFlags()[12] = true;
      }
      if (isValidValue(fields()[13], other.avgPrice)) {
        this.avgPrice = data().deepCopy(fields()[13].schema(), other.avgPrice);
        fieldSetFlags()[13] = true;
      }
      if (isValidValue(fields()[14], other.commissionAsset)) {
        this.commissionAsset = data().deepCopy(fields()[14].schema(), other.commissionAsset);
        fieldSetFlags()[14] = true;
      }
      if (isValidValue(fields()[15], other.commissionAmount)) {
        this.commissionAmount = data().deepCopy(fields()[15].schema(), other.commissionAmount);
        fieldSetFlags()[15] = true;
      }
      if (isValidValue(fields()[16], other.time)) {
        this.time = data().deepCopy(fields()[16].schema(), other.time);
        fieldSetFlags()[16] = true;
      }
      if (isValidValue(fields()[17], other.updateTime)) {
        this.updateTime = data().deepCopy(fields()[17].schema(), other.updateTime);
        fieldSetFlags()[17] = true;
      }
      if (isValidValue(fields()[18], other.isWorking)) {
        this.isWorking = data().deepCopy(fields()[18].schema(), other.isWorking);
        fieldSetFlags()[18] = true;
      }
      if (isValidValue(fields()[19], other.fills)) {
        this.fills = data().deepCopy(fields()[19].schema(), other.fills);
        fieldSetFlags()[19] = true;
      }
      if (isValidValue(fields()[20], other.errorCode)) {
        this.errorCode = data().deepCopy(fields()[20].schema(), other.errorCode);
        fieldSetFlags()[20] = true;
      }
      if (isValidValue(fields()[21], other.errorMessage)) {
        this.errorMessage = data().deepCopy(fields()[21].schema(), other.errorMessage);
        fieldSetFlags()[21] = true;
      }
    }

    /**
      * Gets the value of the 'orderId' field.
      * 订单ID
      * @return The value.
      */
    public long getOrderId() {
      return orderId;
    }


    /**
      * Sets the value of the 'orderId' field.
      * 订单ID
      * @param value The value of 'orderId'.
      * @return This builder.
      */
    public com.crypto.trading.common.avro.OrderExecutionResultData.Builder setOrderId(long value) {
      validate(fields()[0], value);
      this.orderId = value;
      fieldSetFlags()[0] = true;
      return this;
    }

    /**
      * Checks whether the 'orderId' field has been set.
      * 订单ID
      * @return True if the 'orderId' field has been set, false otherwise.
      */
    public boolean hasOrderId() {
      return fieldSetFlags()[0];
    }


    /**
      * Clears the value of the 'orderId' field.
      * 订单ID
      * @return This builder.
      */
    public com.crypto.trading.common.avro.OrderExecutionResultData.Builder clearOrderId() {
      fieldSetFlags()[0] = false;
      return this;
    }

    /**
      * Gets the value of the 'clientOrderId' field.
      * 客户端订单ID
      * @return The value.
      */
    public java.lang.String getClientOrderId() {
      return clientOrderId;
    }


    /**
      * Sets the value of the 'clientOrderId' field.
      * 客户端订单ID
      * @param value The value of 'clientOrderId'.
      * @return This builder.
      */
    public com.crypto.trading.common.avro.OrderExecutionResultData.Builder setClientOrderId(java.lang.String value) {
      validate(fields()[1], value);
      this.clientOrderId = value;
      fieldSetFlags()[1] = true;
      return this;
    }

    /**
      * Checks whether the 'clientOrderId' field has been set.
      * 客户端订单ID
      * @return True if the 'clientOrderId' field has been set, false otherwise.
      */
    public boolean hasClientOrderId() {
      return fieldSetFlags()[1];
    }


    /**
      * Clears the value of the 'clientOrderId' field.
      * 客户端订单ID
      * @return This builder.
      */
    public com.crypto.trading.common.avro.OrderExecutionResultData.Builder clearClientOrderId() {
      clientOrderId = null;
      fieldSetFlags()[1] = false;
      return this;
    }

    /**
      * Gets the value of the 'symbol' field.
      * 交易对符号
      * @return The value.
      */
    public java.lang.String getSymbol() {
      return symbol;
    }


    /**
      * Sets the value of the 'symbol' field.
      * 交易对符号
      * @param value The value of 'symbol'.
      * @return This builder.
      */
    public com.crypto.trading.common.avro.OrderExecutionResultData.Builder setSymbol(java.lang.String value) {
      validate(fields()[2], value);
      this.symbol = value;
      fieldSetFlags()[2] = true;
      return this;
    }

    /**
      * Checks whether the 'symbol' field has been set.
      * 交易对符号
      * @return True if the 'symbol' field has been set, false otherwise.
      */
    public boolean hasSymbol() {
      return fieldSetFlags()[2];
    }


    /**
      * Clears the value of the 'symbol' field.
      * 交易对符号
      * @return This builder.
      */
    public com.crypto.trading.common.avro.OrderExecutionResultData.Builder clearSymbol() {
      symbol = null;
      fieldSetFlags()[2] = false;
      return this;
    }

    /**
      * Gets the value of the 'accountId' field.
      * 账户ID
      * @return The value.
      */
    public java.lang.String getAccountId() {
      return accountId;
    }


    /**
      * Sets the value of the 'accountId' field.
      * 账户ID
      * @param value The value of 'accountId'.
      * @return This builder.
      */
    public com.crypto.trading.common.avro.OrderExecutionResultData.Builder setAccountId(java.lang.String value) {
      validate(fields()[3], value);
      this.accountId = value;
      fieldSetFlags()[3] = true;
      return this;
    }

    /**
      * Checks whether the 'accountId' field has been set.
      * 账户ID
      * @return True if the 'accountId' field has been set, false otherwise.
      */
    public boolean hasAccountId() {
      return fieldSetFlags()[3];
    }


    /**
      * Clears the value of the 'accountId' field.
      * 账户ID
      * @return This builder.
      */
    public com.crypto.trading.common.avro.OrderExecutionResultData.Builder clearAccountId() {
      accountId = null;
      fieldSetFlags()[3] = false;
      return this;
    }

    /**
      * Gets the value of the 'price' field.
      * 订单价格
      * @return The value.
      */
    public java.lang.Double getPrice() {
      return price;
    }


    /**
      * Sets the value of the 'price' field.
      * 订单价格
      * @param value The value of 'price'.
      * @return This builder.
      */
    public com.crypto.trading.common.avro.OrderExecutionResultData.Builder setPrice(java.lang.Double value) {
      validate(fields()[4], value);
      this.price = value;
      fieldSetFlags()[4] = true;
      return this;
    }

    /**
      * Checks whether the 'price' field has been set.
      * 订单价格
      * @return True if the 'price' field has been set, false otherwise.
      */
    public boolean hasPrice() {
      return fieldSetFlags()[4];
    }


    /**
      * Clears the value of the 'price' field.
      * 订单价格
      * @return This builder.
      */
    public com.crypto.trading.common.avro.OrderExecutionResultData.Builder clearPrice() {
      price = null;
      fieldSetFlags()[4] = false;
      return this;
    }

    /**
      * Gets the value of the 'origQty' field.
      * 原始数量
      * @return The value.
      */
    public double getOrigQty() {
      return origQty;
    }


    /**
      * Sets the value of the 'origQty' field.
      * 原始数量
      * @param value The value of 'origQty'.
      * @return This builder.
      */
    public com.crypto.trading.common.avro.OrderExecutionResultData.Builder setOrigQty(double value) {
      validate(fields()[5], value);
      this.origQty = value;
      fieldSetFlags()[5] = true;
      return this;
    }

    /**
      * Checks whether the 'origQty' field has been set.
      * 原始数量
      * @return True if the 'origQty' field has been set, false otherwise.
      */
    public boolean hasOrigQty() {
      return fieldSetFlags()[5];
    }


    /**
      * Clears the value of the 'origQty' field.
      * 原始数量
      * @return This builder.
      */
    public com.crypto.trading.common.avro.OrderExecutionResultData.Builder clearOrigQty() {
      fieldSetFlags()[5] = false;
      return this;
    }

    /**
      * Gets the value of the 'executedQty' field.
      * 已执行数量
      * @return The value.
      */
    public double getExecutedQty() {
      return executedQty;
    }


    /**
      * Sets the value of the 'executedQty' field.
      * 已执行数量
      * @param value The value of 'executedQty'.
      * @return This builder.
      */
    public com.crypto.trading.common.avro.OrderExecutionResultData.Builder setExecutedQty(double value) {
      validate(fields()[6], value);
      this.executedQty = value;
      fieldSetFlags()[6] = true;
      return this;
    }

    /**
      * Checks whether the 'executedQty' field has been set.
      * 已执行数量
      * @return True if the 'executedQty' field has been set, false otherwise.
      */
    public boolean hasExecutedQty() {
      return fieldSetFlags()[6];
    }


    /**
      * Clears the value of the 'executedQty' field.
      * 已执行数量
      * @return This builder.
      */
    public com.crypto.trading.common.avro.OrderExecutionResultData.Builder clearExecutedQty() {
      fieldSetFlags()[6] = false;
      return this;
    }

    /**
      * Gets the value of the 'cummulativeQuoteQty' field.
      * 累计成交金额
      * @return The value.
      */
    public java.lang.Double getCummulativeQuoteQty() {
      return cummulativeQuoteQty;
    }


    /**
      * Sets the value of the 'cummulativeQuoteQty' field.
      * 累计成交金额
      * @param value The value of 'cummulativeQuoteQty'.
      * @return This builder.
      */
    public com.crypto.trading.common.avro.OrderExecutionResultData.Builder setCummulativeQuoteQty(java.lang.Double value) {
      validate(fields()[7], value);
      this.cummulativeQuoteQty = value;
      fieldSetFlags()[7] = true;
      return this;
    }

    /**
      * Checks whether the 'cummulativeQuoteQty' field has been set.
      * 累计成交金额
      * @return True if the 'cummulativeQuoteQty' field has been set, false otherwise.
      */
    public boolean hasCummulativeQuoteQty() {
      return fieldSetFlags()[7];
    }


    /**
      * Clears the value of the 'cummulativeQuoteQty' field.
      * 累计成交金额
      * @return This builder.
      */
    public com.crypto.trading.common.avro.OrderExecutionResultData.Builder clearCummulativeQuoteQty() {
      cummulativeQuoteQty = null;
      fieldSetFlags()[7] = false;
      return this;
    }

    /**
      * Gets the value of the 'status' field.
      * 订单状态
      * @return The value.
      */
    public java.lang.String getStatus() {
      return status;
    }


    /**
      * Sets the value of the 'status' field.
      * 订单状态
      * @param value The value of 'status'.
      * @return This builder.
      */
    public com.crypto.trading.common.avro.OrderExecutionResultData.Builder setStatus(java.lang.String value) {
      validate(fields()[8], value);
      this.status = value;
      fieldSetFlags()[8] = true;
      return this;
    }

    /**
      * Checks whether the 'status' field has been set.
      * 订单状态
      * @return True if the 'status' field has been set, false otherwise.
      */
    public boolean hasStatus() {
      return fieldSetFlags()[8];
    }


    /**
      * Clears the value of the 'status' field.
      * 订单状态
      * @return This builder.
      */
    public com.crypto.trading.common.avro.OrderExecutionResultData.Builder clearStatus() {
      status = null;
      fieldSetFlags()[8] = false;
      return this;
    }

    /**
      * Gets the value of the 'type' field.
      * 订单类型
      * @return The value.
      */
    public java.lang.String getType() {
      return type;
    }


    /**
      * Sets the value of the 'type' field.
      * 订单类型
      * @param value The value of 'type'.
      * @return This builder.
      */
    public com.crypto.trading.common.avro.OrderExecutionResultData.Builder setType(java.lang.String value) {
      validate(fields()[9], value);
      this.type = value;
      fieldSetFlags()[9] = true;
      return this;
    }

    /**
      * Checks whether the 'type' field has been set.
      * 订单类型
      * @return True if the 'type' field has been set, false otherwise.
      */
    public boolean hasType() {
      return fieldSetFlags()[9];
    }


    /**
      * Clears the value of the 'type' field.
      * 订单类型
      * @return This builder.
      */
    public com.crypto.trading.common.avro.OrderExecutionResultData.Builder clearType() {
      type = null;
      fieldSetFlags()[9] = false;
      return this;
    }

    /**
      * Gets the value of the 'side' field.
      * 订单方向
      * @return The value.
      */
    public java.lang.String getSide() {
      return side;
    }


    /**
      * Sets the value of the 'side' field.
      * 订单方向
      * @param value The value of 'side'.
      * @return This builder.
      */
    public com.crypto.trading.common.avro.OrderExecutionResultData.Builder setSide(java.lang.String value) {
      validate(fields()[10], value);
      this.side = value;
      fieldSetFlags()[10] = true;
      return this;
    }

    /**
      * Checks whether the 'side' field has been set.
      * 订单方向
      * @return True if the 'side' field has been set, false otherwise.
      */
    public boolean hasSide() {
      return fieldSetFlags()[10];
    }


    /**
      * Clears the value of the 'side' field.
      * 订单方向
      * @return This builder.
      */
    public com.crypto.trading.common.avro.OrderExecutionResultData.Builder clearSide() {
      side = null;
      fieldSetFlags()[10] = false;
      return this;
    }

    /**
      * Gets the value of the 'positionSide' field.
      * 持仓方向
      * @return The value.
      */
    public java.lang.String getPositionSide() {
      return positionSide;
    }


    /**
      * Sets the value of the 'positionSide' field.
      * 持仓方向
      * @param value The value of 'positionSide'.
      * @return This builder.
      */
    public com.crypto.trading.common.avro.OrderExecutionResultData.Builder setPositionSide(java.lang.String value) {
      validate(fields()[11], value);
      this.positionSide = value;
      fieldSetFlags()[11] = true;
      return this;
    }

    /**
      * Checks whether the 'positionSide' field has been set.
      * 持仓方向
      * @return True if the 'positionSide' field has been set, false otherwise.
      */
    public boolean hasPositionSide() {
      return fieldSetFlags()[11];
    }


    /**
      * Clears the value of the 'positionSide' field.
      * 持仓方向
      * @return This builder.
      */
    public com.crypto.trading.common.avro.OrderExecutionResultData.Builder clearPositionSide() {
      positionSide = null;
      fieldSetFlags()[11] = false;
      return this;
    }

    /**
      * Gets the value of the 'stopPrice' field.
      * 止损价格
      * @return The value.
      */
    public java.lang.Double getStopPrice() {
      return stopPrice;
    }


    /**
      * Sets the value of the 'stopPrice' field.
      * 止损价格
      * @param value The value of 'stopPrice'.
      * @return This builder.
      */
    public com.crypto.trading.common.avro.OrderExecutionResultData.Builder setStopPrice(java.lang.Double value) {
      validate(fields()[12], value);
      this.stopPrice = value;
      fieldSetFlags()[12] = true;
      return this;
    }

    /**
      * Checks whether the 'stopPrice' field has been set.
      * 止损价格
      * @return True if the 'stopPrice' field has been set, false otherwise.
      */
    public boolean hasStopPrice() {
      return fieldSetFlags()[12];
    }


    /**
      * Clears the value of the 'stopPrice' field.
      * 止损价格
      * @return This builder.
      */
    public com.crypto.trading.common.avro.OrderExecutionResultData.Builder clearStopPrice() {
      stopPrice = null;
      fieldSetFlags()[12] = false;
      return this;
    }

    /**
      * Gets the value of the 'avgPrice' field.
      * 平均成交价格
      * @return The value.
      */
    public java.lang.Double getAvgPrice() {
      return avgPrice;
    }


    /**
      * Sets the value of the 'avgPrice' field.
      * 平均成交价格
      * @param value The value of 'avgPrice'.
      * @return This builder.
      */
    public com.crypto.trading.common.avro.OrderExecutionResultData.Builder setAvgPrice(java.lang.Double value) {
      validate(fields()[13], value);
      this.avgPrice = value;
      fieldSetFlags()[13] = true;
      return this;
    }

    /**
      * Checks whether the 'avgPrice' field has been set.
      * 平均成交价格
      * @return True if the 'avgPrice' field has been set, false otherwise.
      */
    public boolean hasAvgPrice() {
      return fieldSetFlags()[13];
    }


    /**
      * Clears the value of the 'avgPrice' field.
      * 平均成交价格
      * @return This builder.
      */
    public com.crypto.trading.common.avro.OrderExecutionResultData.Builder clearAvgPrice() {
      avgPrice = null;
      fieldSetFlags()[13] = false;
      return this;
    }

    /**
      * Gets the value of the 'commissionAsset' field.
      * 手续费资产
      * @return The value.
      */
    public java.lang.String getCommissionAsset() {
      return commissionAsset;
    }


    /**
      * Sets the value of the 'commissionAsset' field.
      * 手续费资产
      * @param value The value of 'commissionAsset'.
      * @return This builder.
      */
    public com.crypto.trading.common.avro.OrderExecutionResultData.Builder setCommissionAsset(java.lang.String value) {
      validate(fields()[14], value);
      this.commissionAsset = value;
      fieldSetFlags()[14] = true;
      return this;
    }

    /**
      * Checks whether the 'commissionAsset' field has been set.
      * 手续费资产
      * @return True if the 'commissionAsset' field has been set, false otherwise.
      */
    public boolean hasCommissionAsset() {
      return fieldSetFlags()[14];
    }


    /**
      * Clears the value of the 'commissionAsset' field.
      * 手续费资产
      * @return This builder.
      */
    public com.crypto.trading.common.avro.OrderExecutionResultData.Builder clearCommissionAsset() {
      commissionAsset = null;
      fieldSetFlags()[14] = false;
      return this;
    }

    /**
      * Gets the value of the 'commissionAmount' field.
      * 手续费金额
      * @return The value.
      */
    public java.lang.Double getCommissionAmount() {
      return commissionAmount;
    }


    /**
      * Sets the value of the 'commissionAmount' field.
      * 手续费金额
      * @param value The value of 'commissionAmount'.
      * @return This builder.
      */
    public com.crypto.trading.common.avro.OrderExecutionResultData.Builder setCommissionAmount(java.lang.Double value) {
      validate(fields()[15], value);
      this.commissionAmount = value;
      fieldSetFlags()[15] = true;
      return this;
    }

    /**
      * Checks whether the 'commissionAmount' field has been set.
      * 手续费金额
      * @return True if the 'commissionAmount' field has been set, false otherwise.
      */
    public boolean hasCommissionAmount() {
      return fieldSetFlags()[15];
    }


    /**
      * Clears the value of the 'commissionAmount' field.
      * 手续费金额
      * @return This builder.
      */
    public com.crypto.trading.common.avro.OrderExecutionResultData.Builder clearCommissionAmount() {
      commissionAmount = null;
      fieldSetFlags()[15] = false;
      return this;
    }

    /**
      * Gets the value of the 'time' field.
      * 订单时间(毫秒)
      * @return The value.
      */
    public long getTime() {
      return time;
    }


    /**
      * Sets the value of the 'time' field.
      * 订单时间(毫秒)
      * @param value The value of 'time'.
      * @return This builder.
      */
    public com.crypto.trading.common.avro.OrderExecutionResultData.Builder setTime(long value) {
      validate(fields()[16], value);
      this.time = value;
      fieldSetFlags()[16] = true;
      return this;
    }

    /**
      * Checks whether the 'time' field has been set.
      * 订单时间(毫秒)
      * @return True if the 'time' field has been set, false otherwise.
      */
    public boolean hasTime() {
      return fieldSetFlags()[16];
    }


    /**
      * Clears the value of the 'time' field.
      * 订单时间(毫秒)
      * @return This builder.
      */
    public com.crypto.trading.common.avro.OrderExecutionResultData.Builder clearTime() {
      fieldSetFlags()[16] = false;
      return this;
    }

    /**
      * Gets the value of the 'updateTime' field.
      * 更新时间(毫秒)
      * @return The value.
      */
    public long getUpdateTime() {
      return updateTime;
    }


    /**
      * Sets the value of the 'updateTime' field.
      * 更新时间(毫秒)
      * @param value The value of 'updateTime'.
      * @return This builder.
      */
    public com.crypto.trading.common.avro.OrderExecutionResultData.Builder setUpdateTime(long value) {
      validate(fields()[17], value);
      this.updateTime = value;
      fieldSetFlags()[17] = true;
      return this;
    }

    /**
      * Checks whether the 'updateTime' field has been set.
      * 更新时间(毫秒)
      * @return True if the 'updateTime' field has been set, false otherwise.
      */
    public boolean hasUpdateTime() {
      return fieldSetFlags()[17];
    }


    /**
      * Clears the value of the 'updateTime' field.
      * 更新时间(毫秒)
      * @return This builder.
      */
    public com.crypto.trading.common.avro.OrderExecutionResultData.Builder clearUpdateTime() {
      fieldSetFlags()[17] = false;
      return this;
    }

    /**
      * Gets the value of the 'isWorking' field.
      * 订单是否生效中
      * @return The value.
      */
    public boolean getIsWorking() {
      return isWorking;
    }


    /**
      * Sets the value of the 'isWorking' field.
      * 订单是否生效中
      * @param value The value of 'isWorking'.
      * @return This builder.
      */
    public com.crypto.trading.common.avro.OrderExecutionResultData.Builder setIsWorking(boolean value) {
      validate(fields()[18], value);
      this.isWorking = value;
      fieldSetFlags()[18] = true;
      return this;
    }

    /**
      * Checks whether the 'isWorking' field has been set.
      * 订单是否生效中
      * @return True if the 'isWorking' field has been set, false otherwise.
      */
    public boolean hasIsWorking() {
      return fieldSetFlags()[18];
    }


    /**
      * Clears the value of the 'isWorking' field.
      * 订单是否生效中
      * @return This builder.
      */
    public com.crypto.trading.common.avro.OrderExecutionResultData.Builder clearIsWorking() {
      fieldSetFlags()[18] = false;
      return this;
    }

    /**
      * Gets the value of the 'fills' field.
      * 成交明细JSON
      * @return The value.
      */
    public java.lang.String getFills() {
      return fills;
    }


    /**
      * Sets the value of the 'fills' field.
      * 成交明细JSON
      * @param value The value of 'fills'.
      * @return This builder.
      */
    public com.crypto.trading.common.avro.OrderExecutionResultData.Builder setFills(java.lang.String value) {
      validate(fields()[19], value);
      this.fills = value;
      fieldSetFlags()[19] = true;
      return this;
    }

    /**
      * Checks whether the 'fills' field has been set.
      * 成交明细JSON
      * @return True if the 'fills' field has been set, false otherwise.
      */
    public boolean hasFills() {
      return fieldSetFlags()[19];
    }


    /**
      * Clears the value of the 'fills' field.
      * 成交明细JSON
      * @return This builder.
      */
    public com.crypto.trading.common.avro.OrderExecutionResultData.Builder clearFills() {
      fills = null;
      fieldSetFlags()[19] = false;
      return this;
    }

    /**
      * Gets the value of the 'errorCode' field.
      * 错误代码
      * @return The value.
      */
    public java.lang.Integer getErrorCode() {
      return errorCode;
    }


    /**
      * Sets the value of the 'errorCode' field.
      * 错误代码
      * @param value The value of 'errorCode'.
      * @return This builder.
      */
    public com.crypto.trading.common.avro.OrderExecutionResultData.Builder setErrorCode(java.lang.Integer value) {
      validate(fields()[20], value);
      this.errorCode = value;
      fieldSetFlags()[20] = true;
      return this;
    }

    /**
      * Checks whether the 'errorCode' field has been set.
      * 错误代码
      * @return True if the 'errorCode' field has been set, false otherwise.
      */
    public boolean hasErrorCode() {
      return fieldSetFlags()[20];
    }


    /**
      * Clears the value of the 'errorCode' field.
      * 错误代码
      * @return This builder.
      */
    public com.crypto.trading.common.avro.OrderExecutionResultData.Builder clearErrorCode() {
      errorCode = null;
      fieldSetFlags()[20] = false;
      return this;
    }

    /**
      * Gets the value of the 'errorMessage' field.
      * 错误信息
      * @return The value.
      */
    public java.lang.String getErrorMessage() {
      return errorMessage;
    }


    /**
      * Sets the value of the 'errorMessage' field.
      * 错误信息
      * @param value The value of 'errorMessage'.
      * @return This builder.
      */
    public com.crypto.trading.common.avro.OrderExecutionResultData.Builder setErrorMessage(java.lang.String value) {
      validate(fields()[21], value);
      this.errorMessage = value;
      fieldSetFlags()[21] = true;
      return this;
    }

    /**
      * Checks whether the 'errorMessage' field has been set.
      * 错误信息
      * @return True if the 'errorMessage' field has been set, false otherwise.
      */
    public boolean hasErrorMessage() {
      return fieldSetFlags()[21];
    }


    /**
      * Clears the value of the 'errorMessage' field.
      * 错误信息
      * @return This builder.
      */
    public com.crypto.trading.common.avro.OrderExecutionResultData.Builder clearErrorMessage() {
      errorMessage = null;
      fieldSetFlags()[21] = false;
      return this;
    }

    @Override
    @SuppressWarnings("unchecked")
    public OrderExecutionResultData build() {
      try {
        OrderExecutionResultData record = new OrderExecutionResultData();
        record.orderId = fieldSetFlags()[0] ? this.orderId : (java.lang.Long) defaultValue(fields()[0]);
        record.clientOrderId = fieldSetFlags()[1] ? this.clientOrderId : (java.lang.String) defaultValue(fields()[1]);
        record.symbol = fieldSetFlags()[2] ? this.symbol : (java.lang.String) defaultValue(fields()[2]);
        record.accountId = fieldSetFlags()[3] ? this.accountId : (java.lang.String) defaultValue(fields()[3]);
        record.price = fieldSetFlags()[4] ? this.price : (java.lang.Double) defaultValue(fields()[4]);
        record.origQty = fieldSetFlags()[5] ? this.origQty : (java.lang.Double) defaultValue(fields()[5]);
        record.executedQty = fieldSetFlags()[6] ? this.executedQty : (java.lang.Double) defaultValue(fields()[6]);
        record.cummulativeQuoteQty = fieldSetFlags()[7] ? this.cummulativeQuoteQty : (java.lang.Double) defaultValue(fields()[7]);
        record.status = fieldSetFlags()[8] ? this.status : (java.lang.String) defaultValue(fields()[8]);
        record.type = fieldSetFlags()[9] ? this.type : (java.lang.String) defaultValue(fields()[9]);
        record.side = fieldSetFlags()[10] ? this.side : (java.lang.String) defaultValue(fields()[10]);
        record.positionSide = fieldSetFlags()[11] ? this.positionSide : (java.lang.String) defaultValue(fields()[11]);
        record.stopPrice = fieldSetFlags()[12] ? this.stopPrice : (java.lang.Double) defaultValue(fields()[12]);
        record.avgPrice = fieldSetFlags()[13] ? this.avgPrice : (java.lang.Double) defaultValue(fields()[13]);
        record.commissionAsset = fieldSetFlags()[14] ? this.commissionAsset : (java.lang.String) defaultValue(fields()[14]);
        record.commissionAmount = fieldSetFlags()[15] ? this.commissionAmount : (java.lang.Double) defaultValue(fields()[15]);
        record.time = fieldSetFlags()[16] ? this.time : (java.lang.Long) defaultValue(fields()[16]);
        record.updateTime = fieldSetFlags()[17] ? this.updateTime : (java.lang.Long) defaultValue(fields()[17]);
        record.isWorking = fieldSetFlags()[18] ? this.isWorking : (java.lang.Boolean) defaultValue(fields()[18]);
        record.fills = fieldSetFlags()[19] ? this.fills : (java.lang.String) defaultValue(fields()[19]);
        record.errorCode = fieldSetFlags()[20] ? this.errorCode : (java.lang.Integer) defaultValue(fields()[20]);
        record.errorMessage = fieldSetFlags()[21] ? this.errorMessage : (java.lang.String) defaultValue(fields()[21]);
        return record;
      } catch (org.apache.avro.AvroMissingFieldException e) {
        throw e;
      } catch (java.lang.Exception e) {
        throw new org.apache.avro.AvroRuntimeException(e);
      }
    }
  }

  @SuppressWarnings("unchecked")
  private static final org.apache.avro.io.DatumWriter<OrderExecutionResultData>
    WRITER$ = (org.apache.avro.io.DatumWriter<OrderExecutionResultData>)MODEL$.createDatumWriter(SCHEMA$);

  @Override public void writeExternal(java.io.ObjectOutput out)
    throws java.io.IOException {
    WRITER$.write(this, SpecificData.getEncoder(out));
  }

  @SuppressWarnings("unchecked")
  private static final org.apache.avro.io.DatumReader<OrderExecutionResultData>
    READER$ = (org.apache.avro.io.DatumReader<OrderExecutionResultData>)MODEL$.createDatumReader(SCHEMA$);

  @Override public void readExternal(java.io.ObjectInput in)
    throws java.io.IOException {
    READER$.read(this, SpecificData.getDecoder(in));
  }

  @Override protected boolean hasCustomCoders() { return true; }

  @Override public void customEncode(org.apache.avro.io.Encoder out)
    throws java.io.IOException
  {
    out.writeLong(this.orderId);

    if (this.clientOrderId == null) {
      out.writeIndex(1);
      out.writeNull();
    } else {
      out.writeIndex(0);
      out.writeString(this.clientOrderId);
    }

    out.writeString(this.symbol);

    out.writeString(this.accountId);

    if (this.price == null) {
      out.writeIndex(1);
      out.writeNull();
    } else {
      out.writeIndex(0);
      out.writeDouble(this.price);
    }

    out.writeDouble(this.origQty);

    out.writeDouble(this.executedQty);

    if (this.cummulativeQuoteQty == null) {
      out.writeIndex(1);
      out.writeNull();
    } else {
      out.writeIndex(0);
      out.writeDouble(this.cummulativeQuoteQty);
    }

    out.writeString(this.status);

    out.writeString(this.type);

    out.writeString(this.side);

    if (this.positionSide == null) {
      out.writeIndex(1);
      out.writeNull();
    } else {
      out.writeIndex(0);
      out.writeString(this.positionSide);
    }

    if (this.stopPrice == null) {
      out.writeIndex(1);
      out.writeNull();
    } else {
      out.writeIndex(0);
      out.writeDouble(this.stopPrice);
    }

    if (this.avgPrice == null) {
      out.writeIndex(1);
      out.writeNull();
    } else {
      out.writeIndex(0);
      out.writeDouble(this.avgPrice);
    }

    if (this.commissionAsset == null) {
      out.writeIndex(1);
      out.writeNull();
    } else {
      out.writeIndex(0);
      out.writeString(this.commissionAsset);
    }

    if (this.commissionAmount == null) {
      out.writeIndex(1);
      out.writeNull();
    } else {
      out.writeIndex(0);
      out.writeDouble(this.commissionAmount);
    }

    out.writeLong(this.time);

    out.writeLong(this.updateTime);

    out.writeBoolean(this.isWorking);

    if (this.fills == null) {
      out.writeIndex(1);
      out.writeNull();
    } else {
      out.writeIndex(0);
      out.writeString(this.fills);
    }

    if (this.errorCode == null) {
      out.writeIndex(1);
      out.writeNull();
    } else {
      out.writeIndex(0);
      out.writeInt(this.errorCode);
    }

    if (this.errorMessage == null) {
      out.writeIndex(1);
      out.writeNull();
    } else {
      out.writeIndex(0);
      out.writeString(this.errorMessage);
    }

  }

  @Override public void customDecode(org.apache.avro.io.ResolvingDecoder in)
    throws java.io.IOException
  {
    org.apache.avro.Schema.Field[] fieldOrder = in.readFieldOrderIfDiff();
    if (fieldOrder == null) {
      this.orderId = in.readLong();

      if (in.readIndex() != 0) {
        in.readNull();
        this.clientOrderId = null;
      } else {
        this.clientOrderId = in.readString();
      }

      this.symbol = in.readString();

      this.accountId = in.readString();

      if (in.readIndex() != 0) {
        in.readNull();
        this.price = null;
      } else {
        this.price = in.readDouble();
      }

      this.origQty = in.readDouble();

      this.executedQty = in.readDouble();

      if (in.readIndex() != 0) {
        in.readNull();
        this.cummulativeQuoteQty = null;
      } else {
        this.cummulativeQuoteQty = in.readDouble();
      }

      this.status = in.readString();

      this.type = in.readString();

      this.side = in.readString();

      if (in.readIndex() != 0) {
        in.readNull();
        this.positionSide = null;
      } else {
        this.positionSide = in.readString();
      }

      if (in.readIndex() != 0) {
        in.readNull();
        this.stopPrice = null;
      } else {
        this.stopPrice = in.readDouble();
      }

      if (in.readIndex() != 0) {
        in.readNull();
        this.avgPrice = null;
      } else {
        this.avgPrice = in.readDouble();
      }

      if (in.readIndex() != 0) {
        in.readNull();
        this.commissionAsset = null;
      } else {
        this.commissionAsset = in.readString();
      }

      if (in.readIndex() != 0) {
        in.readNull();
        this.commissionAmount = null;
      } else {
        this.commissionAmount = in.readDouble();
      }

      this.time = in.readLong();

      this.updateTime = in.readLong();

      this.isWorking = in.readBoolean();

      if (in.readIndex() != 0) {
        in.readNull();
        this.fills = null;
      } else {
        this.fills = in.readString();
      }

      if (in.readIndex() != 0) {
        in.readNull();
        this.errorCode = null;
      } else {
        this.errorCode = in.readInt();
      }

      if (in.readIndex() != 0) {
        in.readNull();
        this.errorMessage = null;
      } else {
        this.errorMessage = in.readString();
      }

    } else {
      for (int i = 0; i < 22; i++) {
        switch (fieldOrder[i].pos()) {
        case 0:
          this.orderId = in.readLong();
          break;

        case 1:
          if (in.readIndex() != 0) {
            in.readNull();
            this.clientOrderId = null;
          } else {
            this.clientOrderId = in.readString();
          }
          break;

        case 2:
          this.symbol = in.readString();
          break;

        case 3:
          this.accountId = in.readString();
          break;

        case 4:
          if (in.readIndex() != 0) {
            in.readNull();
            this.price = null;
          } else {
            this.price = in.readDouble();
          }
          break;

        case 5:
          this.origQty = in.readDouble();
          break;

        case 6:
          this.executedQty = in.readDouble();
          break;

        case 7:
          if (in.readIndex() != 0) {
            in.readNull();
            this.cummulativeQuoteQty = null;
          } else {
            this.cummulativeQuoteQty = in.readDouble();
          }
          break;

        case 8:
          this.status = in.readString();
          break;

        case 9:
          this.type = in.readString();
          break;

        case 10:
          this.side = in.readString();
          break;

        case 11:
          if (in.readIndex() != 0) {
            in.readNull();
            this.positionSide = null;
          } else {
            this.positionSide = in.readString();
          }
          break;

        case 12:
          if (in.readIndex() != 0) {
            in.readNull();
            this.stopPrice = null;
          } else {
            this.stopPrice = in.readDouble();
          }
          break;

        case 13:
          if (in.readIndex() != 0) {
            in.readNull();
            this.avgPrice = null;
          } else {
            this.avgPrice = in.readDouble();
          }
          break;

        case 14:
          if (in.readIndex() != 0) {
            in.readNull();
            this.commissionAsset = null;
          } else {
            this.commissionAsset = in.readString();
          }
          break;

        case 15:
          if (in.readIndex() != 0) {
            in.readNull();
            this.commissionAmount = null;
          } else {
            this.commissionAmount = in.readDouble();
          }
          break;

        case 16:
          this.time = in.readLong();
          break;

        case 17:
          this.updateTime = in.readLong();
          break;

        case 18:
          this.isWorking = in.readBoolean();
          break;

        case 19:
          if (in.readIndex() != 0) {
            in.readNull();
            this.fills = null;
          } else {
            this.fills = in.readString();
          }
          break;

        case 20:
          if (in.readIndex() != 0) {
            in.readNull();
            this.errorCode = null;
          } else {
            this.errorCode = in.readInt();
          }
          break;

        case 21:
          if (in.readIndex() != 0) {
            in.readNull();
            this.errorMessage = null;
          } else {
            this.errorMessage = in.readString();
          }
          break;

        default:
          throw new java.io.IOException("Corrupt ResolvingDecoder.");
        }
      }
    }
  }
}










