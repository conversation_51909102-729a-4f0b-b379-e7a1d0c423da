package com.crypto.trading.common.dto.account;

import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

/**
 * PositionDTO单元测试类
 */
class PositionDTOTest {

    /**
     * 测试默认构造函数
     */
    @Test
    void testDefaultConstructor() {
        PositionDTO dto = new PositionDTO();
        assertNotNull(dto);
    }

    /**
     * 测试带参数构造函数
     */
    @Test
    void testParameterizedConstructor() {
        String accountId = "account-001";
        String symbol = "BTCUSDT";
        String positionSide = "LONG";
        Integer leverage = 5;
        BigDecimal quantity = new BigDecimal("1.0");
        BigDecimal entryPrice = new BigDecimal("50000.00");
        BigDecimal markPrice = new BigDecimal("51000.00");
        BigDecimal unrealizedProfit = new BigDecimal("1000.00");
        BigDecimal realizedProfit = new BigDecimal("500.00");
        BigDecimal initialMargin = new BigDecimal("10000.00");
        BigDecimal maintMargin = new BigDecimal("500.00");
        BigDecimal maxQuantity = new BigDecimal("1.0");
        LocalDateTime updateTime = LocalDateTime.now();

        PositionDTO dto = new PositionDTO(
                accountId, symbol, positionSide, leverage, quantity, entryPrice,
                markPrice, unrealizedProfit, realizedProfit, initialMargin,
                maintMargin, maxQuantity, updateTime
        );

        assertNotNull(dto);
        assertEquals(accountId, dto.getAccountId());
        assertEquals(symbol, dto.getSymbol());
        assertEquals(positionSide, dto.getPositionSide());
        assertEquals(leverage, dto.getLeverage());
        assertEquals(quantity, dto.getQuantity());
        assertEquals(entryPrice, dto.getEntryPrice());
        assertEquals(markPrice, dto.getMarkPrice());
        assertEquals(unrealizedProfit, dto.getUnrealizedProfit());
        assertEquals(realizedProfit, dto.getRealizedProfit());
        assertEquals(initialMargin, dto.getInitialMargin());
        assertEquals(maintMargin, dto.getMaintMargin());
        assertEquals(maxQuantity, dto.getMaxQuantity());
        assertEquals(updateTime, dto.getUpdateTime());
    }

    /**
     * 测试getter和setter方法
     */
    @Test
    void testGettersAndSetters() {
        PositionDTO dto = new PositionDTO();

        String accountId = "account-002";
        dto.setAccountId(accountId);
        assertEquals(accountId, dto.getAccountId());

        String symbol = "ETHUSDT";
        dto.setSymbol(symbol);
        assertEquals(symbol, dto.getSymbol());

        String positionSide = "SHORT";
        dto.setPositionSide(positionSide);
        assertEquals(positionSide, dto.getPositionSide());

        Integer leverage = 10;
        dto.setLeverage(leverage);
        assertEquals(leverage, dto.getLeverage());

        BigDecimal quantity = new BigDecimal("2.0");
        dto.setQuantity(quantity);
        assertEquals(quantity, dto.getQuantity());

        BigDecimal entryPrice = new BigDecimal("3000.00");
        dto.setEntryPrice(entryPrice);
        assertEquals(entryPrice, dto.getEntryPrice());

        BigDecimal markPrice = new BigDecimal("2900.00");
        dto.setMarkPrice(markPrice);
        assertEquals(markPrice, dto.getMarkPrice());

        BigDecimal unrealizedProfit = new BigDecimal("200.00");
        dto.setUnrealizedProfit(unrealizedProfit);
        assertEquals(unrealizedProfit, dto.getUnrealizedProfit());

        BigDecimal realizedProfit = new BigDecimal("100.00");
        dto.setRealizedProfit(realizedProfit);
        assertEquals(realizedProfit, dto.getRealizedProfit());

        BigDecimal initialMargin = new BigDecimal("600.00");
        dto.setInitialMargin(initialMargin);
        assertEquals(initialMargin, dto.getInitialMargin());

        BigDecimal maintMargin = new BigDecimal("30.00");
        dto.setMaintMargin(maintMargin);
        assertEquals(maintMargin, dto.getMaintMargin());

        BigDecimal maxQuantity = new BigDecimal("2.0");
        dto.setMaxQuantity(maxQuantity);
        assertEquals(maxQuantity, dto.getMaxQuantity());

        LocalDateTime updateTime = LocalDateTime.now();
        dto.setUpdateTime(updateTime);
        assertEquals(updateTime, dto.getUpdateTime());
    }

    /**
     * 测试toString方法
     */
    @Test
    void testToString() {
        PositionDTO dto = new PositionDTO();
        dto.setAccountId("account-003");
        dto.setSymbol("BTCUSDT");
        dto.setPositionSide("LONG");
        dto.setLeverage(3);
        dto.setQuantity(new BigDecimal("0.5"));
        dto.setEntryPrice(new BigDecimal("48000.00"));

        String toString = dto.toString();
        assertNotNull(toString);
        assertTrue(toString.contains("account-003"));
        assertTrue(toString.contains("BTCUSDT"));
        assertTrue(toString.contains("LONG"));
        assertTrue(toString.contains("leverage=3"));
        assertTrue(toString.contains("0.5"));
        assertTrue(toString.contains("48000.00"));
    }
} 