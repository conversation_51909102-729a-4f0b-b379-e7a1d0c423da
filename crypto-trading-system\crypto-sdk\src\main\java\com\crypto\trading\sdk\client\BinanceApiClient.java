package com.crypto.trading.sdk.client;

import java.math.BigDecimal;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * @name: BinanceApiClient
 * @author: zeek
 * @description: 币安API客户端接口。
 *               封装了对币安交易所API的直接调用，为上层服务（如交易执行器）提供统一、简洁的访问方式。
 *               实现该接口的类将负责处理所有与API的通信细节，包括请求签名、参数序列化和错误处理。
 * @create: 2024-07-16 01:35
 */
public interface BinanceApiClient {

    /**
     * 向交易所提交一个新的市价单。
     *
     * @param symbol        交易对，例如 "BTCUSDT"
     * @param side          买卖方向 ("BUY" 或 "SELL")
     * @param positionSide  持仓方向 ("LONG", "SHORT", 或 "BOTH")
     * @param quantity      下单数量
     * @param clientOrderId 客户端自定义的订单ID，用于保证唯一性
     * @return 成功提交后，返回由交易所生成的订单ID (orderId)。如果提交失败，则可能抛出异常或返回null。
     * @throws com.crypto.trading.sdk.exception.ApiException 当API调用失败时（例如，网络问题、参数错误、认证失败等）
     */
    String placeMarketOrder(String symbol, String side, String positionSide, BigDecimal quantity, String clientOrderId);

    /**
     * 查询指定订单的当前状态。可以根据交易所订单ID或客户端自定义ID进行查询。
     *
     * @param symbol        交易对，例如 "BTCUSDT"
     * @param orderId       交易所返回的订单ID (可选)
     * @param clientOrderId 客户端自定义的订单ID (可选, 如果orderId未提供)
     * @return 一个包含订单所有字段的Map。如果查询失败或订单不存在，则返回null。
     * @throws com.crypto.trading.sdk.exception.ApiException 当API调用失败时
     */
    Map<String, Object> queryOrder(String symbol, String orderId, String clientOrderId);

    /**
     * 开始一个新的用户数据流，并返回一个 listenKey。
     * 这个 listenKey 用于订阅该用户的账户和订单更新信息。
     *
     * @return listenKey 字符串
     * @throws com.crypto.trading.sdk.exception.ApiException 当API调用失败时
     */
    String startUserDataStream();

    /**
     * 延长指定 listenKey 的有效期。
     * 用户数据流的 listenKey 在创建后有60分钟的有效期，需要定期调用此方法进行续期。
     *
     * @param listenKey 需要续期的 listenKey
     * @throws com.crypto.trading.sdk.exception.ApiException 当API调用失败时
     */
    void keepAliveUserDataStream(String listenKey);

    /**
     * 获取历史K线数据。
     *
     * @param parameters 请求参数，封装在Map中
     * @return API返回的原始JSON字符串
     * @throws com.crypto.trading.sdk.exception.ApiException 当API调用失败时
     */
    String getKlines(Map<String, Object> parameters);

    /**
     * 获取历史成交数据。
     *
     * @param parameters 请求参数，封装在Map中
     * @return API返回的原始JSON字符串
     * @throws com.crypto.trading.sdk.exception.ApiException 当API调用失败时
     */
    String getTrades(Map<String, Object> parameters);

    /**
     * 获取订单簿深度数据。
     *
     * @param parameters 请求参数，封装在Map中
     * @return API返回的原始JSON字符串
     * @throws com.crypto.trading.sdk.exception.ApiException 当API调用失败时
     */
    String getDepth(Map<String, Object> parameters);
    
    /**
     * 获取订单信息（直接使用参数Map）
     *
     * @param parameters 查询参数，必须包含symbol和orderId或origClientOrderId
     * @return API返回的原始JSON字符串
     * @throws com.crypto.trading.sdk.exception.ApiException 当API调用失败时
     */
    default String getOrder(LinkedHashMap<String, Object> parameters) {
        throw new UnsupportedOperationException("方法未实现");
    }
    
    /**
     * 创建新订单（直接使用参数Map）
     *
     * @param parameters 订单参数，必须包含symbol、side、type和quantity等
     * @return API返回的原始JSON字符串
     * @throws com.crypto.trading.sdk.exception.ApiException 当API调用失败时
     */
    default String newOrder(LinkedHashMap<String, Object> parameters) {
        throw new UnsupportedOperationException("方法未实现");
    }
    
    /**
     * 取消订单（直接使用参数Map）
     *
     * @param parameters 取消参数，必须包含symbol和orderId或origClientOrderId
     * @return API返回的原始JSON字符串
     * @throws com.crypto.trading.sdk.exception.ApiException 当API调用失败时
     */
    default String cancelOrder(LinkedHashMap<String, Object> parameters) {
        throw new UnsupportedOperationException("方法未实现");
    }
    
    /**
     * 取消所有未平仓订单（直接使用参数Map）
     *
     * @param parameters 取消参数，必须包含symbol
     * @return API返回的原始JSON字符串
     * @throws com.crypto.trading.sdk.exception.ApiException 当API调用失败时
     */
    default String cancelAllOpenOrders(LinkedHashMap<String, Object> parameters) {
        throw new UnsupportedOperationException("方法未实现");
    }
    
    // 其他可能的扩展方法：
    //
    // /**
    //  * 向交易所提交一个新的限价单。
    //  * ...
    //  */
    // String placeLimitOrder(...);
    //
    // /**
    //  * 查询当前账户信息。
    //  * ...
    //  */
    // AccountInfoResponse getAccountInfo();
}
