package com.binance.connector.futures.client.exceptions;

public class BinanceClientException extends RuntimeException {
    // 序列化版本号
    private static final long serialVersionUID = 1L;
    // 默认错误码
    private final int ERROR_CODE_0 = 0;
    // HTTP状态码
    private final int httpStatusCode;
    // 错误码
    private final int errorCode;
    // 错误信息
    private String errMsg;

    /**
     * 构造函数，使用默认错误码
     * @param fullErrMsg 完整错误信息
     * @param httpStatusCode HTTP状态码
     */
    public BinanceClientException(String fullErrMsg, int httpStatusCode) {
        super(fullErrMsg);
        this.httpStatusCode = httpStatusCode;
        this.errorCode = ERROR_CODE_0;
    }

    /**
     * 构造函数，使用指定错误码
     * @param fullErrMsg 完整错误信息
     * @param errMsg 错误信息
     * @param httpStatusCode HTTP状态码
     * @param errorCode 错误码
     */
    public BinanceClientException(String fullErrMsg, String errMsg, int httpStatusCode, int errorCode) {
        super(fullErrMsg);
        this.httpStatusCode = httpStatusCode;
        this.errorCode = errorCode;
        this.errMsg =  errMsg;
    }

    /**
     * 获取错误码
     * @return 错误码
     */
    public int getErrorCode() {
        return errorCode;
    }

    /**
     * 获取HTTP状态码
     * @return HTTP状态码
     */
    public int getHttpStatusCode() {
        return httpStatusCode;
    }

    /**
     * 获取错误信息
     * @return 错误信息
     */
    public String getErrMsg() {
        return errMsg;
    }
}