#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
蒸馏管理器

协调整个DeepSeek知识蒸馏流程，包括教师模型训练、知识蒸馏、模型压缩等步骤。
"""

import logging
import numpy as np
import pandas as pd
import torch
from typing import Dict, List, Tuple, Optional, Any, Union
from pathlib import Path
import json
from datetime import datetime

from .deepseek_distiller import DeepSeekDistiller
from .model_compressor import ModelCompressor


class DistillationManager:
    """蒸馏管理器，协调整个知识蒸馏流程"""
    
    def __init__(self, config: Dict = None):
        """
        初始化蒸馏管理器
        
        Args:
            config: 配置参数字典
        """
        self.logger = logging.getLogger('cryptoMlStrategy.model.distillation.manager')
        
        # 默认配置
        self.config = {
            'distillation': {
                'teacher_hidden_dims': [512, 256, 128, 64],
                'student_hidden_dims': [128, 64, 32],
                'temperature': 4.0,
                'alpha': 0.7,
                'beta': 0.3,
                'feature_loss_weight': 0.1,
                'learning_rate': 0.001,
                'batch_size': 64,
                'epochs': 100,
                'early_stopping_patience': 10
            },
            'compression': {
                'enabled': True,
                'quantization': {'enabled': True, 'bits': 8},
                'pruning': {'enabled': True, 'sparsity': 0.3},
                'weight_sharing': {'enabled': False, 'clusters': 16}
            },
            'validation': {
                'split_ratio': 0.2,
                'performance_threshold': 0.05  # 最大允许性能损失
            },
            'output': {
                'save_teacher': True,
                'save_student': True,
                'save_compressed': True,
                'save_dir': 'models/distilled'
            }
        }
        
        # 更新用户配置
        if config:
            self._deep_update(self.config, config)
        
        # 初始化组件
        self.distiller = DeepSeekDistiller(self.config['distillation'])
        self.compressor = ModelCompressor(self.config['compression']) if self.config['compression']['enabled'] else None
        
        # 训练历史和统计
        self.training_history = {
            'teacher_training': {},
            'distillation': {},
            'compression': {}
        }
        
        self.performance_metrics = {}
        
        self.logger.info("蒸馏管理器初始化完成")
    
    def _deep_update(self, base_dict: Dict, update_dict: Dict):
        """深度更新字典"""
        for key, value in update_dict.items():
            if key in base_dict and isinstance(base_dict[key], dict) and isinstance(value, dict):
                self._deep_update(base_dict[key], value)
            else:
                base_dict[key] = value
    
    def run_full_distillation(self, X: np.ndarray, y: np.ndarray, 
                             feature_names: List[str] = None) -> Dict[str, Any]:
        """
        运行完整的蒸馏流程
        
        Args:
            X: 训练特征
            y: 训练标签
            feature_names: 特征名称列表
            
        Returns:
            蒸馏结果字典
        """
        try:
            self.logger.info("开始完整蒸馏流程...")
            start_time = datetime.now()
            
            # 数据验证
            if X.shape[0] != y.shape[0]:
                raise ValueError("特征和标签数量不匹配")
            
            input_dim = X.shape[1]
            output_dim = 1 if len(y.shape) == 1 else y.shape[1]
            
            # 1. 创建模型
            self.logger.info("创建教师和学生模型...")
            self.distiller.create_models(input_dim, output_dim)
            
            # 2. 训练教师模型
            self.logger.info("训练教师模型...")
            teacher_results = self.distiller.train_teacher(
                X, y, validation_split=self.config['validation']['split_ratio']
            )
            self.training_history['teacher_training'] = teacher_results
            
            # 3. 知识蒸馏
            self.logger.info("开始知识蒸馏...")
            distillation_results = self.distiller.distill_knowledge(
                X, y, validation_split=self.config['validation']['split_ratio']
            )
            self.training_history['distillation'] = distillation_results
            
            # 4. 模型压缩（如果启用）
            compressed_model = None
            if self.compressor is not None:
                self.logger.info("开始模型压缩...")
                
                # 准备验证数据
                from sklearn.model_selection import train_test_split
                X_scaled = self.distiller.scaler.transform(X)
                _, X_val, _, y_val = train_test_split(
                    X_scaled, y, test_size=self.config['validation']['split_ratio'], random_state=42
                )
                
                X_val_tensor = torch.FloatTensor(X_val).to(self.distiller.device)
                y_val_tensor = torch.FloatTensor(y_val).to(self.distiller.device)
                
                compressed_model = self.compressor.compress_model(
                    self.distiller.student_model,
                    validation_data=(X_val_tensor, y_val_tensor)
                )
                self.training_history['compression'] = self.compressor.compression_stats
            
            # 5. 性能评估
            self.logger.info("评估模型性能...")
            performance_metrics = self._evaluate_models(X, y)
            self.performance_metrics = performance_metrics
            
            # 6. 保存模型
            if self.config['output']['save_teacher'] or self.config['output']['save_student'] or self.config['output']['save_compressed']:
                self.logger.info("保存模型...")
                self._save_models(compressed_model)
            
            # 7. 生成报告
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            results = {
                'success': True,
                'duration_seconds': duration,
                'input_dim': input_dim,
                'output_dim': output_dim,
                'feature_names': feature_names,
                'teacher_training': teacher_results,
                'distillation': distillation_results,
                'compression': self.training_history['compression'] if self.compressor else None,
                'performance_metrics': performance_metrics,
                'model_info': self._get_model_info(),
                'config': self.config
            }
            
            self.logger.info(f"蒸馏流程完成，耗时: {duration:.2f}秒")
            return results
            
        except Exception as e:
            self.logger.error(f"蒸馏流程失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'config': self.config
            }
    
    def _evaluate_models(self, X: np.ndarray, y: np.ndarray) -> Dict[str, Any]:
        """评估模型性能"""
        try:
            from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
            from sklearn.model_selection import train_test_split
            
            # 分割数据
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.2, random_state=42
            )
            
            metrics = {}
            
            # 评估教师模型
            if self.distiller.teacher_model is not None:
                teacher_pred = self.distiller.predict(X_test, use_teacher=True)
                metrics['teacher'] = {
                    'mse': mean_squared_error(y_test, teacher_pred),
                    'mae': mean_absolute_error(y_test, teacher_pred),
                    'r2': r2_score(y_test, teacher_pred)
                }
            
            # 评估学生模型
            if self.distiller.student_model is not None:
                student_pred = self.distiller.predict(X_test, use_teacher=False)
                metrics['student'] = {
                    'mse': mean_squared_error(y_test, student_pred),
                    'mae': mean_absolute_error(y_test, student_pred),
                    'r2': r2_score(y_test, student_pred)
                }
            
            # 评估压缩模型
            if self.compressor is not None and self.compressor.compressed_model is not None:
                # 使用压缩模型预测
                X_test_scaled = self.distiller.scaler.transform(X_test)
                X_test_tensor = torch.FloatTensor(X_test_scaled).to(self.distiller.device)
                
                self.compressor.compressed_model.eval()
                with torch.no_grad():
                    compressed_pred_tensor = self.compressor.compressed_model(X_test_tensor)
                    if isinstance(compressed_pred_tensor, tuple):
                        compressed_pred_tensor = compressed_pred_tensor[0]
                    compressed_pred = compressed_pred_tensor.cpu().numpy().squeeze()
                
                metrics['compressed'] = {
                    'mse': mean_squared_error(y_test, compressed_pred),
                    'mae': mean_absolute_error(y_test, compressed_pred),
                    'r2': r2_score(y_test, compressed_pred)
                }
            
            # 计算性能损失
            if 'teacher' in metrics and 'student' in metrics:
                metrics['student_performance_loss'] = {
                    'mse_loss': (metrics['student']['mse'] - metrics['teacher']['mse']) / metrics['teacher']['mse'],
                    'mae_loss': (metrics['student']['mae'] - metrics['teacher']['mae']) / metrics['teacher']['mae'],
                    'r2_loss': (metrics['teacher']['r2'] - metrics['student']['r2']) / abs(metrics['teacher']['r2'])
                }
            
            if 'student' in metrics and 'compressed' in metrics:
                metrics['compression_performance_loss'] = {
                    'mse_loss': (metrics['compressed']['mse'] - metrics['student']['mse']) / metrics['student']['mse'],
                    'mae_loss': (metrics['compressed']['mae'] - metrics['student']['mae']) / metrics['student']['mae'],
                    'r2_loss': (metrics['student']['r2'] - metrics['compressed']['r2']) / abs(metrics['student']['r2'])
                }
            
            return metrics
            
        except Exception as e:
            self.logger.error(f"模型评估失败: {e}")
            return {}
    
    def _get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        info = {}
        
        # 蒸馏器信息
        if self.distiller:
            info['distiller'] = self.distiller.get_model_info()
        
        # 压缩器信息
        if self.compressor:
            info['compressor'] = self.compressor.get_compression_info()
        
        return info
    
    def _save_models(self, compressed_model=None):
        """保存模型"""
        try:
            save_dir = Path(self.config['output']['save_dir'])
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # 创建保存目录
            model_save_dir = save_dir / f"distilled_models_{timestamp}"
            model_save_dir.mkdir(parents=True, exist_ok=True)
            
            # 保存蒸馏器模型
            if self.config['output']['save_teacher'] or self.config['output']['save_student']:
                distiller_dir = model_save_dir / "distiller"
                self.distiller.save_models(str(distiller_dir))
            
            # 保存压缩模型
            if self.config['output']['save_compressed'] and compressed_model is not None:
                compressed_path = model_save_dir / "compressed_model.pth"
                self.compressor.save_compressed_model(str(compressed_path))
            
            # 保存训练历史和配置
            history_path = model_save_dir / "training_history.json"
            with open(history_path, 'w') as f:
                json.dump(self.training_history, f, indent=2)
            
            config_path = model_save_dir / "config.json"
            with open(config_path, 'w') as f:
                json.dump(self.config, f, indent=2)
            
            metrics_path = model_save_dir / "performance_metrics.json"
            with open(metrics_path, 'w') as f:
                json.dump(self.performance_metrics, f, indent=2)
            
            self.logger.info(f"模型保存到: {model_save_dir}")
            
        except Exception as e:
            self.logger.error(f"模型保存失败: {e}")
    
    def load_distilled_models(self, load_dir: str, input_dim: int, output_dim: int = 1):
        """
        加载蒸馏模型
        
        Args:
            load_dir: 加载目录
            input_dim: 输入维度
            output_dim: 输出维度
        """
        try:
            load_path = Path(load_dir)
            
            # 加载配置
            config_path = load_path / "config.json"
            if config_path.exists():
                with open(config_path, 'r') as f:
                    loaded_config = json.load(f)
                    self._deep_update(self.config, loaded_config)
            
            # 重新初始化组件
            self.distiller = DeepSeekDistiller(self.config['distillation'])
            if self.config['compression']['enabled']:
                self.compressor = ModelCompressor(self.config['compression'])
            
            # 加载蒸馏器模型
            distiller_dir = load_path / "distiller"
            if distiller_dir.exists():
                self.distiller.load_models(str(distiller_dir), input_dim, output_dim)
            
            # 加载压缩模型
            compressed_path = load_path / "compressed_model.pth"
            if compressed_path.exists() and self.compressor is not None:
                from .deepseek_distiller import StudentModel
                self.compressor.load_compressed_model(
                    StudentModel, str(compressed_path),
                    input_dim, self.config['distillation']['student_hidden_dims'], output_dim
                )
            
            # 加载训练历史
            history_path = load_path / "training_history.json"
            if history_path.exists():
                with open(history_path, 'r') as f:
                    self.training_history = json.load(f)
            
            # 加载性能指标
            metrics_path = load_path / "performance_metrics.json"
            if metrics_path.exists():
                with open(metrics_path, 'r') as f:
                    self.performance_metrics = json.load(f)
            
            self.logger.info(f"蒸馏模型从 {load_path} 加载完成")
            
        except Exception as e:
            self.logger.error(f"加载蒸馏模型失败: {e}")
            raise
    
    def predict(self, X: np.ndarray, model_type: str = 'compressed') -> np.ndarray:
        """
        使用指定模型进行预测
        
        Args:
            X: 输入特征
            model_type: 模型类型 ('teacher', 'student', 'compressed')
            
        Returns:
            预测结果
        """
        try:
            if model_type == 'teacher':
                return self.distiller.predict(X, use_teacher=True)
            elif model_type == 'student':
                return self.distiller.predict(X, use_teacher=False)
            elif model_type == 'compressed' and self.compressor is not None:
                # 使用压缩模型预测
                X_scaled = self.distiller.scaler.transform(X)
                X_tensor = torch.FloatTensor(X_scaled).to(self.distiller.device)
                
                self.compressor.compressed_model.eval()
                with torch.no_grad():
                    pred_tensor = self.compressor.compressed_model(X_tensor)
                    if isinstance(pred_tensor, tuple):
                        pred_tensor = pred_tensor[0]
                    return pred_tensor.cpu().numpy().squeeze()
            else:
                raise ValueError(f"不支持的模型类型: {model_type}")
                
        except Exception as e:
            self.logger.error(f"预测失败: {e}")
            raise
    
    def get_distillation_summary(self) -> Dict[str, Any]:
        """获取蒸馏摘要"""
        return {
            'config': self.config,
            'training_history': self.training_history,
            'performance_metrics': self.performance_metrics,
            'model_info': self._get_model_info()
        }