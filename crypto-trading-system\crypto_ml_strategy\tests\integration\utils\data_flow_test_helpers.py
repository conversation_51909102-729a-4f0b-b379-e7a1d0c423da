#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据流测试辅助工具

提供数据流集成测试所需的辅助函数和工具类。
"""

import asyncio
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from loguru import logger


@dataclass
class DataFlowMetrics:
    """数据流性能指标"""
    latency_ms: float
    throughput_per_sec: float
    error_rate: float
    data_loss_rate: float
    cache_hit_rate: float
    memory_usage_mb: float
    cpu_usage_percent: float


@dataclass
class TimeframeData:
    """时间框架数据"""
    timeframe: str
    timestamp: int
    open: float
    high: float
    low: float
    close: float
    volume: float
    symbol: str


class DataFlowValidator:
    """数据流验证器"""
    
    def __init__(self):
        """初始化数据流验证器"""
        self.validation_rules = {
            'price_range': (0.01, 1000000.0),  # 价格范围
            'volume_range': (0.0, float('inf')),  # 成交量范围
            'timestamp_tolerance_ms': 5000,  # 时间戳容差5秒
            'ohlc_consistency': True,  # OHLC一致性检查
            'required_fields': ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'symbol']
        }
        
        logger.info("数据流验证器已初始化")
    
    def validate_market_data(self, data: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """
        验证市场数据
        
        Args:
            data: 市场数据
        
        Returns:
            Tuple[bool, List[str]]: (是否有效, 错误列表)
        """
        errors = []
        
        # 检查必需字段
        for field in self.validation_rules['required_fields']:
            if field not in data:
                errors.append(f"缺少必需字段: {field}")
        
        if errors:
            return False, errors
        
        # 价格范围检查
        price_min, price_max = self.validation_rules['price_range']
        for price_field in ['open', 'high', 'low', 'close']:
            price = data.get(price_field, 0)
            if not (price_min <= price <= price_max):
                errors.append(f"{price_field}价格超出范围: {price}")
        
        # 成交量检查
        volume = data.get('volume', 0)
        volume_min, volume_max = self.validation_rules['volume_range']
        if not (volume_min <= volume <= volume_max):
            errors.append(f"成交量超出范围: {volume}")
        
        # OHLC一致性检查
        if self.validation_rules['ohlc_consistency']:
            open_price = data.get('open', 0)
            high_price = data.get('high', 0)
            low_price = data.get('low', 0)
            close_price = data.get('close', 0)
            
            if high_price < low_price:
                errors.append("最高价小于最低价")
            
            if open_price > high_price or open_price < low_price:
                errors.append("开盘价超出最高最低价范围")
            
            if close_price > high_price or close_price < low_price:
                errors.append("收盘价超出最高最低价范围")
        
        # 时间戳检查
        timestamp = data.get('timestamp', 0)
        current_time = int(time.time() * 1000)
        time_diff = abs(current_time - timestamp)
        
        if time_diff > self.validation_rules['timestamp_tolerance_ms']:
            errors.append(f"时间戳偏差过大: {time_diff}ms")
        
        return len(errors) == 0, errors
    
    def validate_timeframe_consistency(self, data_1m: List[Dict], data_5m: Dict) -> Tuple[bool, List[str]]:
        """
        验证时间框架一致性
        
        Args:
            data_1m: 1分钟数据列表（5个数据点）
            data_5m: 5分钟聚合数据
        
        Returns:
            Tuple[bool, List[str]]: (是否一致, 错误列表)
        """
        errors = []
        
        if len(data_1m) != 5:
            errors.append(f"1分钟数据点数不正确: {len(data_1m)}")
            return False, errors
        
        # 计算期望的5分钟数据
        expected_open = data_1m[0]['open']
        expected_close = data_1m[-1]['close']
        expected_high = max(d['high'] for d in data_1m)
        expected_low = min(d['low'] for d in data_1m)
        expected_volume = sum(d['volume'] for d in data_1m)
        
        # 验证聚合数据
        tolerance = 0.01  # 1%容差
        
        if abs(data_5m['open'] - expected_open) > expected_open * tolerance:
            errors.append(f"5分钟开盘价不一致: {data_5m['open']} vs {expected_open}")
        
        if abs(data_5m['close'] - expected_close) > expected_close * tolerance:
            errors.append(f"5分钟收盘价不一致: {data_5m['close']} vs {expected_close}")
        
        if abs(data_5m['high'] - expected_high) > expected_high * tolerance:
            errors.append(f"5分钟最高价不一致: {data_5m['high']} vs {expected_high}")
        
        if abs(data_5m['low'] - expected_low) > expected_low * tolerance:
            errors.append(f"5分钟最低价不一致: {data_5m['low']} vs {expected_low}")
        
        if abs(data_5m['volume'] - expected_volume) > expected_volume * tolerance:
            errors.append(f"5分钟成交量不一致: {data_5m['volume']} vs {expected_volume}")
        
        return len(errors) == 0, errors


class DataFlowPerformanceMonitor:
    """数据流性能监控器"""
    
    def __init__(self):
        """初始化性能监控器"""
        self.metrics_history = []
        self.start_time = None
        self.operation_count = 0
        self.error_count = 0
        self.cache_hits = 0
        self.cache_misses = 0
        
        logger.info("数据流性能监控器已初始化")
    
    def start_monitoring(self) -> None:
        """开始监控"""
        self.start_time = time.time()
        self.operation_count = 0
        self.error_count = 0
        self.cache_hits = 0
        self.cache_misses = 0
        logger.debug("开始性能监控")
    
    def record_operation(self, success: bool = True) -> None:
        """记录操作"""
        self.operation_count += 1
        if not success:
            self.error_count += 1
    
    def record_cache_hit(self) -> None:
        """记录缓存命中"""
        self.cache_hits += 1
    
    def record_cache_miss(self) -> None:
        """记录缓存未命中"""
        self.cache_misses += 1
    
    def get_current_metrics(self) -> DataFlowMetrics:
        """获取当前性能指标"""
        if self.start_time is None:
            raise RuntimeError("监控未开始")
        
        elapsed_time = time.time() - self.start_time
        
        # 计算指标
        throughput = self.operation_count / elapsed_time if elapsed_time > 0 else 0
        error_rate = (self.error_count / self.operation_count * 100) if self.operation_count > 0 else 0
        cache_total = self.cache_hits + self.cache_misses
        cache_hit_rate = (self.cache_hits / cache_total * 100) if cache_total > 0 else 0
        
        # 模拟系统资源使用情况
        memory_usage = 512.0 + (self.operation_count * 0.1)  # 模拟内存使用
        cpu_usage = min(80.0, 10.0 + (throughput * 0.5))  # 模拟CPU使用
        
        return DataFlowMetrics(
            latency_ms=elapsed_time * 1000 / max(1, self.operation_count),
            throughput_per_sec=throughput,
            error_rate=error_rate,
            data_loss_rate=0.0,  # 简化为0
            cache_hit_rate=cache_hit_rate,
            memory_usage_mb=memory_usage,
            cpu_usage_percent=cpu_usage
        )
    
    def stop_monitoring(self) -> DataFlowMetrics:
        """停止监控并返回最终指标"""
        final_metrics = self.get_current_metrics()
        self.metrics_history.append(final_metrics)
        logger.debug(f"性能监控已停止 - 吞吐量: {final_metrics.throughput_per_sec:.2f} ops/sec")
        return final_metrics


class TimeframeDataGenerator:
    """时间框架数据生成器"""
    
    def __init__(self, base_price: float = 45000.0, volatility: float = 0.02):
        """
        初始化时间框架数据生成器
        
        Args:
            base_price: 基础价格
            volatility: 波动率
        """
        self.base_price = base_price
        self.volatility = volatility
        self.current_price = base_price
        
        logger.info(f"时间框架数据生成器已初始化 - 基础价格: {base_price}")
    
    def generate_1m_data(self, symbol: str, count: int = 1) -> List[TimeframeData]:
        """
        生成1分钟数据
        
        Args:
            symbol: 交易符号
            count: 数据点数量
        
        Returns:
            List[TimeframeData]: 1分钟数据列表
        """
        data_points = []
        current_time = int(time.time() * 1000)
        
        for i in range(count):
            # 生成价格变化
            price_change = (hash(f"{symbol}_{i}") % 1000 - 500) / 10000 * self.volatility
            self.current_price *= (1 + price_change)
            
            # 生成OHLC
            open_price = self.current_price
            close_price = self.current_price * (1 + price_change * 0.5)
            high_price = max(open_price, close_price) * (1 + abs(price_change) * 0.3)
            low_price = min(open_price, close_price) * (1 - abs(price_change) * 0.3)
            volume = 1000 + (hash(f"{symbol}_{i}_vol") % 2000)
            
            data_point = TimeframeData(
                timeframe="1m",
                timestamp=current_time + (i * 60000),  # 每分钟
                open=round(open_price, 2),
                high=round(high_price, 2),
                low=round(low_price, 2),
                close=round(close_price, 2),
                volume=float(volume),
                symbol=symbol
            )
            
            data_points.append(data_point)
            self.current_price = close_price
        
        return data_points
    
    def aggregate_to_5m(self, data_1m: List[TimeframeData]) -> TimeframeData:
        """
        将1分钟数据聚合为5分钟数据
        
        Args:
            data_1m: 1分钟数据列表（应包含5个数据点）
        
        Returns:
            TimeframeData: 5分钟聚合数据
        """
        if len(data_1m) != 5:
            raise ValueError(f"需要5个1分钟数据点，实际收到: {len(data_1m)}")
        
        # 聚合计算
        open_price = data_1m[0].open
        close_price = data_1m[-1].close
        high_price = max(d.high for d in data_1m)
        low_price = min(d.low for d in data_1m)
        total_volume = sum(d.volume for d in data_1m)
        
        # 使用第一个数据点的时间戳（对齐到5分钟边界）
        base_timestamp = data_1m[0].timestamp
        aligned_timestamp = (base_timestamp // 300000) * 300000  # 5分钟对齐
        
        return TimeframeData(
            timeframe="5m",
            timestamp=aligned_timestamp,
            open=open_price,
            high=high_price,
            low=low_price,
            close=close_price,
            volume=total_volume,
            symbol=data_1m[0].symbol
        )


class DataFlowTestScenario:
    """数据流测试场景"""
    
    def __init__(self, name: str, description: str):
        """
        初始化测试场景
        
        Args:
            name: 场景名称
            description: 场景描述
        """
        self.name = name
        self.description = description
        self.steps = []
        self.expected_results = {}
        self.performance_thresholds = {}
        
        logger.info(f"数据流测试场景已创建: {name}")
    
    def add_step(self, step_name: str, step_function, expected_result: Any = None) -> None:
        """
        添加测试步骤
        
        Args:
            step_name: 步骤名称
            step_function: 步骤执行函数
            expected_result: 期望结果
        """
        self.steps.append({
            'name': step_name,
            'function': step_function,
            'expected_result': expected_result
        })
        
        if expected_result is not None:
            self.expected_results[step_name] = expected_result
    
    def set_performance_threshold(self, metric: str, threshold: float) -> None:
        """
        设置性能阈值
        
        Args:
            metric: 性能指标名称
            threshold: 阈值
        """
        self.performance_thresholds[metric] = threshold
    
    async def execute(self) -> Dict[str, Any]:
        """
        执行测试场景
        
        Returns:
            Dict[str, Any]: 执行结果
        """
        logger.info(f"开始执行测试场景: {self.name}")
        
        results = {
            'scenario_name': self.name,
            'description': self.description,
            'steps_executed': 0,
            'steps_passed': 0,
            'steps_failed': 0,
            'step_results': {},
            'performance_metrics': {},
            'overall_success': False,
            'execution_time': 0.0
        }
        
        start_time = time.time()
        monitor = DataFlowPerformanceMonitor()
        monitor.start_monitoring()
        
        try:
            for step in self.steps:
                step_name = step['name']
                step_function = step['function']
                expected_result = step.get('expected_result')
                
                logger.debug(f"执行步骤: {step_name}")
                
                try:
                    if asyncio.iscoroutinefunction(step_function):
                        step_result = await step_function()
                    else:
                        step_result = step_function()
                    
                    results['step_results'][step_name] = {
                        'success': True,
                        'result': step_result,
                        'expected': expected_result,
                        'matches_expected': step_result == expected_result if expected_result is not None else True
                    }
                    
                    results['steps_passed'] += 1
                    monitor.record_operation(success=True)
                    
                except Exception as e:
                    logger.error(f"步骤执行失败 {step_name}: {str(e)}")
                    results['step_results'][step_name] = {
                        'success': False,
                        'error': str(e),
                        'expected': expected_result
                    }
                    
                    results['steps_failed'] += 1
                    monitor.record_operation(success=False)
                
                results['steps_executed'] += 1
            
            # 获取性能指标
            performance_metrics = monitor.stop_monitoring()
            results['performance_metrics'] = {
                'latency_ms': performance_metrics.latency_ms,
                'throughput_per_sec': performance_metrics.throughput_per_sec,
                'error_rate': performance_metrics.error_rate,
                'cache_hit_rate': performance_metrics.cache_hit_rate,
                'memory_usage_mb': performance_metrics.memory_usage_mb,
                'cpu_usage_percent': performance_metrics.cpu_usage_percent
            }
            
            # 检查性能阈值
            performance_passed = True
            for metric, threshold in self.performance_thresholds.items():
                actual_value = results['performance_metrics'].get(metric, 0)
                if actual_value > threshold:
                    performance_passed = False
                    logger.warning(f"性能阈值超标 {metric}: {actual_value} > {threshold}")
            
            results['overall_success'] = (results['steps_failed'] == 0 and performance_passed)
            results['execution_time'] = time.time() - start_time
            
            logger.info(f"测试场景完成: {self.name} - 成功: {results['overall_success']}")
            
        except Exception as e:
            logger.error(f"测试场景执行异常: {str(e)}")
            results['overall_success'] = False
            results['execution_error'] = str(e)
        
        return results


def create_standard_data_flow_scenarios() -> List[DataFlowTestScenario]:
    """
    创建标准数据流测试场景
    
    Returns:
        List[DataFlowTestScenario]: 标准测试场景列表
    """
    scenarios = []
    
    # 场景1: 基础数据流测试
    basic_scenario = DataFlowTestScenario(
        name="基础数据流测试",
        description="测试基本的API数据获取和Kafka消息传递"
    )
    basic_scenario.set_performance_threshold('latency_ms', 100.0)
    basic_scenario.set_performance_threshold('error_rate', 5.0)
    scenarios.append(basic_scenario)
    
    # 场景2: 高负载数据流测试
    load_scenario = DataFlowTestScenario(
        name="高负载数据流测试",
        description="测试高负载情况下的数据流性能"
    )
    load_scenario.set_performance_threshold('latency_ms', 200.0)
    load_scenario.set_performance_threshold('throughput_per_sec', 100.0)
    load_scenario.set_performance_threshold('memory_usage_mb', 1024.0)
    scenarios.append(load_scenario)
    
    # 场景3: 数据一致性测试
    consistency_scenario = DataFlowTestScenario(
        name="数据一致性测试",
        description="测试多时间框架数据的一致性和完整性"
    )
    consistency_scenario.set_performance_threshold('error_rate', 1.0)
    scenarios.append(consistency_scenario)
    
    return scenarios


# 导出主要类和函数
__all__ = [
    'DataFlowMetrics',
    'TimeframeData',
    'DataFlowValidator',
    'DataFlowPerformanceMonitor',
    'TimeframeDataGenerator',
    'DataFlowTestScenario',
    'create_standard_data_flow_scenarios'
]