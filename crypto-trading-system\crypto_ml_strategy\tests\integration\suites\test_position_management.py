#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
仓位管理专项测试

专门测试仓位管理系统的核心功能，包括仓位计算、
仓位限制检查、动态仓位调整等关键功能。

Author: Crypto ML Strategy Team
Date: 2024-12-19
"""

import sys
import os
import unittest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import logging
import time
from unittest.mock import Mock, patch, MagicMock

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..', 'src'))

# 导入风险管理模块
from risk_management import (
    RiskConfig, PositionInfo, PositionManager,
    PositionSizeMethod, StopLossType, TakeProfitType
)

# 导入测试工具
from ..config.integration_test_config import IntegrationTestConfig
from ..data.test_data_generator import TestDataGenerator

logger = logging.getLogger(__name__)


class PositionManagementTest(unittest.TestCase):
    """仓位管理专项测试"""
    
    @classmethod
    def setUpClass(cls):
        """测试类初始化"""
        cls.config = IntegrationTestConfig()
        cls.data_generator = TestDataGenerator()
        
        # 设置日志
        logging.basicConfig(level=logging.INFO)
        
        logger.info("Position Management Test initialized")
    
    def setUp(self):
        """每个测试用例的初始化"""
        self.test_start_time = datetime.now()
        
        # 创建不同的风险配置用于测试
        self.conservative_config = RiskConfig(
            max_single_position_ratio=0.05,
            max_total_position_ratio=0.6,
            position_size_method=PositionSizeMethod.FIXED_RATIO,
            stop_loss_type=StopLossType.PERCENTAGE,
            take_profit_type=TakeProfitType.FIXED_RATIO
        )
        
        self.aggressive_config = RiskConfig(
            max_single_position_ratio=0.2,
            max_total_position_ratio=0.95,
            position_size_method=PositionSizeMethod.KELLY_FORMULA,
            stop_loss_type=StopLossType.ATR_BASED,
            take_profit_type=TakeProfitType.RISK_REWARD_RATIO
        )
        
        self.adaptive_config = RiskConfig(
            max_single_position_ratio=0.1,
            max_total_position_ratio=0.8,
            position_size_method=PositionSizeMethod.ADAPTIVE,
            stop_loss_type=StopLossType.ADAPTIVE,
            take_profit_type=TakeProfitType.PARTIAL_PROFIT
        )
        
        logger.info(f"Test case setup: {self._testMethodName}")
    
    def tearDown(self):
        """每个测试用例的清理"""
        test_duration = (datetime.now() - self.test_start_time).total_seconds()
        logger.info(f"Test case completed: {self._testMethodName}, Duration: {test_duration:.2f}s")
    
    def test_position_size_calculation_methods(self):
        """测试不同仓位计算方法"""
        logger.info("Testing position size calculation methods...")
        
        try:
            # 测试数据
            entry_price = 50000
            stop_loss_price = 48000
            account_balance = 100000
            available_balance = 80000
            
            market_data = {
                'volatility': 0.25,
                'win_rate': 0.6,
                'avg_win': 0.03,
                'avg_loss': 0.02,
                'atr': 1000
            }
            
            # 测试固定比例方法
            conservative_manager = PositionManager(self.conservative_config)
            conservative_manager.update_account_info(account_balance, available_balance)
            
            conservative_size = conservative_manager.calculate_position_size(
                'BTCUSDT', entry_price, stop_loss_price, market_data, signal_confidence=0.8
            )
            
            # 验证保守型仓位
            self.assertGreater(conservative_size, 0, "Conservative position size should be positive")
            conservative_value = conservative_size * entry_price
            conservative_ratio = conservative_value / account_balance
            self.assertLessEqual(conservative_ratio, self.conservative_config.max_single_position_ratio * 1.1)
            
            # 测试Kelly公式方法
            aggressive_manager = PositionManager(self.aggressive_config)
            aggressive_manager.update_account_info(account_balance, available_balance)
            
            aggressive_size = aggressive_manager.calculate_position_size(
                'BTCUSDT', entry_price, stop_loss_price, market_data, signal_confidence=0.8
            )
            
            # 验证激进型仓位
            self.assertGreater(aggressive_size, 0, "Aggressive position size should be positive")
            aggressive_value = aggressive_size * entry_price
            aggressive_ratio = aggressive_value / account_balance
            self.assertLessEqual(aggressive_ratio, self.aggressive_config.max_single_position_ratio * 1.1)
            
            # 测试自适应方法
            adaptive_manager = PositionManager(self.adaptive_config)
            adaptive_manager.update_account_info(account_balance, available_balance)
            
            adaptive_size = adaptive_manager.calculate_position_size(
                'BTCUSDT', entry_price, stop_loss_price, market_data, signal_confidence=0.8
            )
            
            # 验证自适应仓位
            self.assertGreater(adaptive_size, 0, "Adaptive position size should be positive")
            adaptive_value = adaptive_size * entry_price
            adaptive_ratio = adaptive_value / account_balance
            self.assertLessEqual(adaptive_ratio, self.adaptive_config.max_single_position_ratio * 1.1)
            
            # 验证不同方法产生不同结果
            sizes = [conservative_size, aggressive_size, adaptive_size]
            self.assertGreater(len(set(sizes)), 1, "Different methods should produce different sizes")
            
            logger.info("✅ Position size calculation methods test passed")
            logger.info(f"   Conservative: {conservative_size:.4f} BTC ({conservative_ratio:.2%})")
            logger.info(f"   Aggressive: {aggressive_size:.4f} BTC ({aggressive_ratio:.2%})")
            logger.info(f"   Adaptive: {adaptive_size:.4f} BTC ({adaptive_ratio:.2%})")
            
        except Exception as e:
            logger.error(f"❌ Position size calculation methods test failed: {e}")
            raise
    
    def test_position_limits_enforcement(self):
        """测试仓位限制执行"""
        logger.info("Testing position limits enforcement...")
        
        try:
            position_manager = PositionManager(self.adaptive_config)
            position_manager.update_account_info(100000, 80000)
            
            # 创建多个仓位，测试限制
            positions = [
                PositionInfo(
                    symbol='BTCUSDT',
                    current_size=1.5,
                    suggested_size=1.5,
                    max_allowed_size=2.0,
                    entry_price=50000,
                    current_price=50000,
                    unrealized_pnl=0,
                    stop_loss_price=48000,
                    take_profit_price=53000,
                    position_value=75000
                ),
                PositionInfo(
                    symbol='ETHUSDT',
                    current_size=20.0,
                    suggested_size=20.0,
                    max_allowed_size=25.0,
                    entry_price=3000,
                    current_price=3000,
                    unrealized_pnl=0,
                    stop_loss_price=2850,
                    take_profit_price=3150,
                    position_value=60000
                ),
                PositionInfo(
                    symbol='ADAUSDT',
                    current_size=50000,
                    suggested_size=50000,
                    max_allowed_size=60000,
                    entry_price=0.5,
                    current_price=0.5,
                    unrealized_pnl=0,
                    stop_loss_price=0.475,
                    take_profit_price=0.525,
                    position_value=25000
                )
            ]
            
            # 更新所有仓位
            for position in positions:
                position_manager.update_position(position.symbol, position)
            
            # 检查仓位限制
            violations = position_manager.check_position_limits()
            
            # 验证限制检查
            self.assertIsInstance(violations, list)
            
            # 计算总敞口
            total_exposure = sum(pos.position_value for pos in positions)
            total_exposure_ratio = total_exposure / 100000
            
            if total_exposure_ratio > self.adaptive_config.max_total_position_ratio:
                self.assertGreater(len(violations), 0, "Should detect total exposure violation")
            
            # 检查单个仓位限制
            for position in positions:
                position_ratio = position.position_value / 100000
                if position_ratio > self.adaptive_config.max_single_position_ratio:
                    single_violations = [v for v in violations if v.get('symbol') == position.symbol]
                    self.assertGreater(len(single_violations), 0, f"Should detect violation for {position.symbol}")
            
            # 获取仓位摘要
            summary = position_manager.get_position_summary()
            self.assertIsInstance(summary, dict)
            self.assertIn('total_positions', summary)
            self.assertIn('total_exposure_ratio', summary)
            self.assertIn('within_limits', summary)
            
            logger.info("✅ Position limits enforcement test passed")
            logger.info(f"   Total positions: {summary['total_positions']}")
            logger.info(f"   Total exposure: {summary['total_exposure_ratio']:.2%}")
            logger.info(f"   Violations found: {len(violations)}")
            
        except Exception as e:
            logger.error(f"❌ Position limits enforcement test failed: {e}")
            raise
    
    def test_dynamic_position_adjustment(self):
        """测试动态仓位调整"""
        logger.info("Testing dynamic position adjustment...")
        
        try:
            position_manager = PositionManager(self.adaptive_config)
            position_manager.update_account_info(100000, 80000)
            
            # 初始仓位
            initial_position = PositionInfo(
                symbol='BTCUSDT',
                current_size=1.0,
                suggested_size=1.0,
                max_allowed_size=2.0,
                entry_price=50000,
                current_price=50000,
                unrealized_pnl=0,
                stop_loss_price=48000,
                take_profit_price=53000,
                position_value=50000
            )
            
            position_manager.update_position('BTCUSDT', initial_position)
            
            # 模拟市场变化和仓位调整
            price_scenarios = [
                {'price': 52000, 'volatility': 0.2, 'confidence': 0.9},  # 价格上涨，增加仓位
                {'price': 48000, 'volatility': 0.4, 'confidence': 0.3},  # 价格下跌，减少仓位
                {'price': 51000, 'volatility': 0.25, 'confidence': 0.7}, # 价格恢复，调整仓位
            ]
            
            adjustment_history = []
            
            for i, scenario in enumerate(price_scenarios):
                current_price = scenario['price']
                volatility = scenario['volatility']
                confidence = scenario['confidence']
                
                # 更新当前仓位信息
                updated_position = PositionInfo(
                    symbol='BTCUSDT',
                    current_size=initial_position.current_size,
                    suggested_size=initial_position.current_size,
                    max_allowed_size=2.0,
                    entry_price=initial_position.entry_price,
                    current_price=current_price,
                    unrealized_pnl=(current_price - initial_position.entry_price) * initial_position.current_size,
                    stop_loss_price=current_price * 0.96,  # 动态止损
                    take_profit_price=current_price * 1.06,  # 动态止盈
                    position_value=current_price * initial_position.current_size
                )
                
                position_manager.update_position('BTCUSDT', updated_position)
                
                # 获取仓位调整建议
                adjustments = position_manager.suggest_position_adjustments()
                
                # 记录调整历史
                adjustment_history.append({
                    'scenario': i + 1,
                    'price': current_price,
                    'volatility': volatility,
                    'confidence': confidence,
                    'adjustments': len(adjustments),
                    'current_size': updated_position.current_size,
                    'unrealized_pnl': updated_position.unrealized_pnl
                })
                
                # 验证调整建议
                self.assertIsInstance(adjustments, list)
                
                # 如果有调整建议，验证其合理性
                for adjustment in adjustments:
                    self.assertIn('symbol', adjustment)
                    self.assertIn('action', adjustment)
                    self.assertIn('reason', adjustment)
                    self.assertIn('suggested_size', adjustment)
            
            # 验证调整历史
            self.assertEqual(len(adjustment_history), len(price_scenarios))
            
            # 验证在不同市场条件下产生了不同的调整建议
            total_adjustments = sum(record['adjustments'] for record in adjustment_history)
            self.assertGreaterEqual(total_adjustments, 0, "Should generate adjustment suggestions")
            
            logger.info("✅ Dynamic position adjustment test passed")
            for record in adjustment_history:
                logger.info(f"   Scenario {record['scenario']}: Price=${record['price']:,}, "
                          f"Adjustments={record['adjustments']}, PnL=${record['unrealized_pnl']:,.2f}")
            
        except Exception as e:
            logger.error(f"❌ Dynamic position adjustment test failed: {e}")
            raise
    
    def test_position_risk_metrics(self):
        """测试仓位风险指标计算"""
        logger.info("Testing position risk metrics calculation...")
        
        try:
            position_manager = PositionManager(self.adaptive_config)
            position_manager.update_account_info(100000, 80000)
            
            # 创建测试仓位
            test_position = PositionInfo(
                symbol='BTCUSDT',
                current_size=1.5,
                suggested_size=1.5,
                max_allowed_size=2.0,
                entry_price=50000,
                current_price=48000,  # 当前亏损
                unrealized_pnl=-3000,
                stop_loss_price=47000,
                take_profit_price=53000,
                position_value=72000
            )
            
            position_manager.update_position('BTCUSDT', test_position)
            
            # 计算风险指标
            risk_metrics = position_manager.calculate_position_risk_metrics('BTCUSDT')
            
            # 验证风险指标
            self.assertIsInstance(risk_metrics, dict)
            
            # 验证必要的风险指标存在
            expected_metrics = [
                'position_ratio',
                'unrealized_pnl_ratio',
                'distance_to_stop_loss',
                'distance_to_take_profit',
                'risk_reward_ratio',
                'position_risk_score'
            ]
            
            for metric in expected_metrics:
                self.assertIn(metric, risk_metrics, f"Missing risk metric: {metric}")
                self.assertIsInstance(risk_metrics[metric], (int, float), f"Invalid type for {metric}")
            
            # 验证风险指标的合理性
            self.assertGreater(risk_metrics['position_ratio'], 0)
            self.assertLess(risk_metrics['position_ratio'], 1)
            
            self.assertLess(risk_metrics['unrealized_pnl_ratio'], 0)  # 当前亏损
            
            self.assertGreater(risk_metrics['distance_to_stop_loss'], 0)
            self.assertGreater(risk_metrics['distance_to_take_profit'], 0)
            
            self.assertGreater(risk_metrics['risk_reward_ratio'], 0)
            
            # 测试多个仓位的风险指标
            eth_position = PositionInfo(
                symbol='ETHUSDT',
                current_size=20.0,
                suggested_size=20.0,
                max_allowed_size=25.0,
                entry_price=3000,
                current_price=3100,  # 当前盈利
                unrealized_pnl=2000,
                stop_loss_price=2850,
                take_profit_price=3200,
                position_value=62000
            )
            
            position_manager.update_position('ETHUSDT', eth_position)
            
            # 获取所有仓位的风险指标
            all_risk_metrics = position_manager.get_all_position_risk_metrics()
            
            self.assertIsInstance(all_risk_metrics, dict)
            self.assertIn('BTCUSDT', all_risk_metrics)
            self.assertIn('ETHUSDT', all_risk_metrics)
            
            # 验证盈利仓位的指标
            eth_metrics = all_risk_metrics['ETHUSDT']
            self.assertGreater(eth_metrics['unrealized_pnl_ratio'], 0)  # 当前盈利
            
            logger.info("✅ Position risk metrics calculation test passed")
            logger.info(f"   BTC Risk Score: {risk_metrics['position_risk_score']:.2f}")
            logger.info(f"   ETH Risk Score: {eth_metrics['position_risk_score']:.2f}")
            
        except Exception as e:
            logger.error(f"❌ Position risk metrics calculation test failed: {e}")
            raise
    
    def test_position_correlation_analysis(self):
        """测试仓位相关性分析"""
        logger.info("Testing position correlation analysis...")
        
        try:
            position_manager = PositionManager(self.adaptive_config)
            position_manager.update_account_info(100000, 80000)
            
            # 创建相关性较高的仓位组合
            correlated_positions = [
                PositionInfo(
                    symbol='BTCUSDT',
                    current_size=1.0,
                    suggested_size=1.0,
                    max_allowed_size=1.5,
                    entry_price=50000,
                    current_price=50000,
                    unrealized_pnl=0,
                    stop_loss_price=48000,
                    take_profit_price=53000,
                    position_value=50000
                ),
                PositionInfo(
                    symbol='ETHUSDT',
                    current_size=15.0,
                    suggested_size=15.0,
                    max_allowed_size=20.0,
                    entry_price=3000,
                    current_price=3000,
                    unrealized_pnl=0,
                    stop_loss_price=2850,
                    take_profit_price=3150,
                    position_value=45000
                )
            ]
            
            # 更新仓位
            for position in correlated_positions:
                position_manager.update_position(position.symbol, position)
            
            # 模拟价格数据用于相关性分析
            price_history = {
                'BTCUSDT': [50000, 51000, 49000, 52000, 48000, 53000, 47000],
                'ETHUSDT': [3000, 3060, 2940, 3120, 2880, 3180, 2820]  # 高相关性
            }
            
            # 更新价格历史（模拟）
            for symbol, prices in price_history.items():
                # 这里应该有实际的价格历史更新方法
                # 暂时跳过具体实现，验证接口存在
                pass
            
            # 分析仓位相关性
            correlation_analysis = position_manager.analyze_position_correlations()
            
            # 验证相关性分析结果
            self.assertIsInstance(correlation_analysis, dict)
            
            # 验证包含必要的分析结果
            if correlation_analysis:  # 如果有相关性数据
                expected_keys = ['correlation_matrix', 'high_correlation_pairs', 'diversification_score']
                for key in expected_keys:
                    if key in correlation_analysis:
                        self.assertIsNotNone(correlation_analysis[key])
            
            # 获取仓位多样化建议
            diversification_suggestions = position_manager.get_diversification_suggestions()
            
            self.assertIsInstance(diversification_suggestions, list)
            
            logger.info("✅ Position correlation analysis test passed")
            logger.info(f"   Correlation analysis keys: {list(correlation_analysis.keys())}")
            logger.info(f"   Diversification suggestions: {len(diversification_suggestions)}")
            
        except Exception as e:
            logger.error(f"❌ Position correlation analysis test failed: {e}")
            raise


if __name__ == '__main__':
    unittest.main()
