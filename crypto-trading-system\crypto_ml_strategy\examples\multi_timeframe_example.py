#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
多时间周期数据处理示例

演示如何使用MultiTimeframeManager及其相关组件进行多时间周期数据处理，
包括数据质量检查、时间序列同步、特征融合等完整流程。

使用场景：
1. 实时数据处理和质量监控
2. 多时间周期特征融合
3. 与Java模块API集成
4. 错误处理和性能监控

作者：Crypto ML Strategy Team
版本：1.0.0
日期：2025-06-19
"""

import sys
import os
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any
import time
import json

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入多时间周期处理组件
from src.data import (
    MultiTimeframeManager,
    MultiTimeframeProcessor, 
    TimeSeriesSynchronizer,
    DataQualityChecker,
    ProcessingMode,
    FusionMethod,
    QualityLevel
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('multi_timeframe_example.log')
    ]
)

logger = logging.getLogger('MultiTimeframeExample')


class MultiTimeframeExample:
    """多时间周期数据处理示例类"""
    
    def __init__(self):
        """初始化示例"""
        self.logger = logger
        self.manager = None
        self.sample_data = {}
        
        # 示例配置
        self.config = {
            'processing': {
                'mode': 'real_time',
                'fusion_method': 'weighted_average',
                'quality_threshold': 0.8,
                'auto_quality_fix': True,
                'enable_caching': True,
                'max_cache_size': 100
            },
            'timeframes': {
                '1m': {'enabled': True, 'weight': 0.1, 'priority': 1},
                '5m': {'enabled': True, 'weight': 0.2, 'priority': 2},
                '15m': {'enabled': True, 'weight': 0.3, 'priority': 3},
                '1h': {'enabled': True, 'weight': 0.4, 'priority': 4},
                '4h': {'enabled': False, 'weight': 0.0, 'priority': 5},
                '1d': {'enabled': False, 'weight': 0.0, 'priority': 6}
            },
            'quality': {
                'min_quality_score': 0.7,
                'enable_auto_fix': True,
                'quality_check_interval': 300
            }
        }
        
        self.logger.info("多时间周期数据处理示例初始化完成")
    
    def setup_manager(self):
        """设置多时间周期管理器"""
        try:
            self.logger.info("正在初始化多时间周期管理器...")
            
            # 创建管理器实例
            self.manager = MultiTimeframeManager(self.config)
            
            self.logger.info("多时间周期管理器初始化成功")
            return True
            
        except Exception as e:
            self.logger.error(f"多时间周期管理器初始化失败: {e}")
            return False
    
    def generate_sample_data(self, symbol: str = "BTCUSDT", days: int = 7) -> Dict[str, List[Dict]]:
        """
        生成示例数据
        
        Args:
            symbol: 交易对符号
            days: 生成数据的天数
            
        Returns:
            各时间周期的示例数据
        """
        try:
            self.logger.info(f"正在生成 {symbol} 的 {days} 天示例数据...")
            
            # 基础价格和时间设置
            base_price = 50000.0  # BTC基础价格
            start_time = datetime.now() - timedelta(days=days)
            
            timeframes_data = {}
            
            # 为每个启用的时间周期生成数据
            enabled_timeframes = ['1m', '5m', '15m', '1h']
            timeframe_minutes = {'1m': 1, '5m': 5, '15m': 15, '1h': 60}
            
            for timeframe in enabled_timeframes:
                minutes = timeframe_minutes[timeframe]
                total_points = (days * 24 * 60) // minutes
                
                data_points = []
                current_time = start_time
                current_price = base_price
                
                for i in range(total_points):
                    # 模拟价格波动
                    price_change = np.random.normal(0, base_price * 0.001)  # 0.1%标准差
                    current_price = max(current_price + price_change, base_price * 0.5)
                    
                    # 生成OHLCV数据
                    high = current_price * (1 + abs(np.random.normal(0, 0.002)))
                    low = current_price * (1 - abs(np.random.normal(0, 0.002)))
                    open_price = current_price + np.random.normal(0, base_price * 0.0005)
                    close_price = current_price + np.random.normal(0, base_price * 0.0005)
                    volume = abs(np.random.normal(100, 50))
                    
                    data_point = {
                        'timestamp': int(current_time.timestamp() * 1000),
                        'datetime': current_time.isoformat(),
                        'open': round(open_price, 2),
                        'high': round(high, 2),
                        'low': round(low, 2),
                        'close': round(close_price, 2),
                        'volume': round(volume, 4),
                        'symbol': symbol,
                        'timeframe': timeframe
                    }
                    
                    data_points.append(data_point)
                    current_time += timedelta(minutes=minutes)
                
                timeframes_data[timeframe] = data_points
                self.logger.info(f"{timeframe} 时间周期生成 {len(data_points)} 个数据点")
            
            self.sample_data[symbol] = timeframes_data
            self.logger.info(f"示例数据生成完成: {symbol}")
            return timeframes_data
            
        except Exception as e:
            self.logger.error(f"生成示例数据失败: {e}")
            return {}
    
    def load_data_to_manager(self, symbol: str) -> bool:
        """
        将示例数据加载到管理器
        
        Args:
            symbol: 交易对符号
            
        Returns:
            是否加载成功
        """
        try:
            if symbol not in self.sample_data:
                self.logger.error(f"没有找到 {symbol} 的示例数据")
                return False
            
            self.logger.info(f"正在加载 {symbol} 数据到管理器...")
            
            total_loaded = 0
            for timeframe, data_points in self.sample_data[symbol].items():
                success_count = 0
                
                for data_point in data_points:
                    if self.manager.add_timeframe_data(symbol, timeframe, data_point):
                        success_count += 1
                
                total_loaded += success_count
                self.logger.info(f"{timeframe} 时间周期加载 {success_count}/{len(data_points)} 个数据点")
            
            self.logger.info(f"数据加载完成: {symbol}, 总计 {total_loaded} 个数据点")
            return total_loaded > 0
            
        except Exception as e:
            self.logger.error(f"加载数据到管理器失败: {e}")
            return False
    
    def demonstrate_processing(self, symbol: str) -> bool:
        """
        演示数据处理流程
        
        Args:
            symbol: 交易对符号
            
        Returns:
            是否处理成功
        """
        try:
            self.logger.info(f"开始演示 {symbol} 的数据处理流程...")
            
            # 1. 基础数据处理
            self.logger.info("=== 1. 基础多时间周期数据处理 ===")
            result = self.manager.process_multi_timeframe_data(
                symbol=symbol,
                target_timeframe='1m',
                quality_check=True
            )
            
            if result.success:
                self.logger.info(f"处理成功! 耗时: {result.processing_time_ms:.2f}ms")
                self.logger.info(f"对齐数据形状: {result.aligned_data.shape if result.aligned_data is not None else 'None'}")
                self.logger.info(f"融合特征形状: {result.fused_features.shape if result.fused_features is not None else 'None'}")
                
                # 显示质量检查结果
                if result.quality_metrics:
                    self.logger.info("质量检查结果:")
                    for timeframe, metrics in result.quality_metrics.items():
                        self.logger.info(f"  {timeframe}: 总分={metrics.overall_score:.3f}, 等级={metrics.quality_level.value}")
            else:
                self.logger.error(f"处理失败: {result.error_message}")
                return False
            
            # 2. 不同融合方法演示
            self.logger.info("=== 2. 不同特征融合方法演示 ===")
            fusion_methods = ['weighted_average', 'concatenate']
            
            for method in fusion_methods:
                # 临时更新配置
                original_method = self.manager.config['feature_fusion']['default_method']
                self.manager.config['feature_fusion']['default_method'] = method
                
                result = self.manager.process_multi_timeframe_data(symbol, quality_check=False)
                
                if result.success:
                    self.logger.info(f"{method} 融合方法: 特征形状={result.fused_features.shape if result.fused_features is not None else 'None'}")
                
                # 恢复原配置
                self.manager.config['feature_fusion']['default_method'] = original_method
            
            # 3. 性能统计演示
            self.logger.info("=== 3. 性能统计信息 ===")
            stats = self.manager.get_manager_statistics()
            
            self.logger.info("管理器统计:")
            self.logger.info(f"  总处理次数: {stats['manager_stats']['total_processed']}")
            self.logger.info(f"  成功处理次数: {stats['manager_stats']['successful_processed']}")
            self.logger.info(f"  平均处理时间: {stats['manager_stats']['average_processing_time_ms']:.2f}ms")
            self.logger.info(f"  质量检查次数: {stats['manager_stats']['quality_checks_performed']}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"演示数据处理流程失败: {e}")
            return False
    
    def demonstrate_quality_monitoring(self, symbol: str) -> bool:
        """
        演示数据质量监控
        
        Args:
            symbol: 交易对符号
            
        Returns:
            是否演示成功
        """
        try:
            self.logger.info(f"=== 数据质量监控演示: {symbol} ===")
            
            # 获取质量检查器
            quality_checker = self.manager.quality_checker
            
            # 处理数据并获取质量指标
            result = self.manager.process_multi_timeframe_data(symbol, quality_check=True)
            
            if not result.success:
                self.logger.error("数据处理失败，无法进行质量监控演示")
                return False
            
            # 显示详细的质量检查结果
            self.logger.info("详细质量检查结果:")
            for timeframe, metrics in result.quality_metrics.items():
                self.logger.info(f"\n{timeframe} 时间周期质量指标:")
                self.logger.info(f"  完整性分数: {metrics.completeness_score:.3f}")
                self.logger.info(f"  一致性分数: {metrics.consistency_score:.3f}")
                self.logger.info(f"  准确性分数: {metrics.accuracy_score:.3f}")
                self.logger.info(f"  时效性分数: {metrics.timeliness_score:.3f}")
                self.logger.info(f"  总体分数: {metrics.overall_score:.3f}")
                self.logger.info(f"  质量等级: {metrics.quality_level.value}")
                self.logger.info(f"  问题数量: {metrics.issues_count}")
                
                if metrics.recommendations:
                    self.logger.info(f"  改进建议: {', '.join(metrics.recommendations)}")
            
            # 获取质量检查器统计
            quality_stats = quality_checker.get_quality_statistics()
            self.logger.info(f"\n质量检查器统计:")
            self.logger.info(f"  总检查次数: {quality_stats['stats']['total_checks']}")
            self.logger.info(f"  通过检查次数: {quality_stats['stats']['passed_checks']}")
            self.logger.info(f"  失败检查次数: {quality_stats['stats']['failed_checks']}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"质量监控演示失败: {e}")
            return False
    
    def demonstrate_error_handling(self) -> bool:
        """演示错误处理机制"""
        try:
            self.logger.info("=== 错误处理机制演示 ===")
            
            # 1. 处理不存在的交易对
            self.logger.info("1. 处理不存在的交易对:")
            result = self.manager.process_multi_timeframe_data("NONEXISTENT")
            self.logger.info(f"结果: success={result.success}, error={result.error_message}")
            
            # 2. 处理空数据
            self.logger.info("2. 处理空数据:")
            empty_df = pd.DataFrame()
            quality_result = self.manager.quality_checker.check_data_quality(empty_df)
            self.logger.info(f"质量检查结果: 总分={quality_result.overall_score}, 等级={quality_result.quality_level.value}")
            
            # 3. 配置错误处理
            self.logger.info("3. 配置错误处理:")
            try:
                # 尝试更新不存在的时间周期配置
                update_result = self.manager.update_timeframe_config("invalid_tf", {"enabled": True})
                self.logger.info(f"配置更新结果: {update_result}")
            except Exception as e:
                self.logger.info(f"捕获到预期的配置错误: {e}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"错误处理演示失败: {e}")
            return False
    
    def demonstrate_java_api_integration(self, symbol: str) -> bool:
        """
        演示与Java模块API集成的示例
        
        Args:
            symbol: 交易对符号
            
        Returns:
            是否演示成功
        """
        try:
            self.logger.info("=== Java模块API集成示例 ===")
            
            # 模拟从Java模块接收的Kafka消息格式
            kafka_message_example = {
                "symbol": symbol,
                "timeframe": "1m",
                "timestamp": int(time.time() * 1000),
                "data": {
                    "open": 50000.0,
                    "high": 50100.0,
                    "low": 49900.0,
                    "close": 50050.0,
                    "volume": 125.5
                }
            }
            
            self.logger.info("模拟Kafka消息格式:")
            self.logger.info(json.dumps(kafka_message_example, indent=2))
            
            # 处理Kafka消息
            success = self.manager.add_timeframe_data(
                symbol=kafka_message_example["symbol"],
                timeframe=kafka_message_example["timeframe"],
                data=kafka_message_example["data"]
            )
            
            self.logger.info(f"Kafka消息处理结果: {success}")
            
            # 模拟向Java模块发送处理结果
            result = self.manager.process_multi_timeframe_data(symbol)
            
            if result.success:
                # 构造发送给Java模块的响应格式
                response_to_java = {
                    "symbol": result.symbol,
                    "timestamp": int(time.time() * 1000),
                    "processing_time_ms": result.processing_time_ms,
                    "quality_scores": {
                        tf: metrics.overall_score 
                        for tf, metrics in result.quality_metrics.items()
                    },
                    "features_available": result.fused_features is not None,
                    "data_points": len(result.aligned_data) if result.aligned_data is not None else 0
                }
                
                self.logger.info("发送给Java模块的响应格式:")
                self.logger.info(json.dumps(response_to_java, indent=2))
            
            return True
            
        except Exception as e:
            self.logger.error(f"Java API集成演示失败: {e}")
            return False
    
    def run_complete_example(self):
        """运行完整示例"""
        try:
            self.logger.info("开始运行多时间周期数据处理完整示例")
            self.logger.info("=" * 60)
            
            # 1. 设置管理器
            if not self.setup_manager():
                return False
            
            # 2. 生成示例数据
            symbol = "BTCUSDT"
            if not self.generate_sample_data(symbol, days=2):
                return False
            
            # 3. 加载数据到管理器
            if not self.load_data_to_manager(symbol):
                return False
            
            # 4. 演示数据处理流程
            if not self.demonstrate_processing(symbol):
                return False
            
            # 5. 演示质量监控
            if not self.demonstrate_quality_monitoring(symbol):
                return False
            
            # 6. 演示错误处理
            if not self.demonstrate_error_handling():
                return False
            
            # 7. 演示Java API集成
            if not self.demonstrate_java_api_integration(symbol):
                return False
            
            self.logger.info("=" * 60)
            self.logger.info("多时间周期数据处理完整示例运行成功!")
            
            # 最终统计信息
            final_stats = self.manager.get_manager_statistics()
            self.logger.info("\n最终统计信息:")
            self.logger.info(f"总处理次数: {final_stats['manager_stats']['total_processed']}")
            self.logger.info(f"成功率: {final_stats['manager_stats']['successful_processed'] / max(1, final_stats['manager_stats']['total_processed']) * 100:.1f}%")
            self.logger.info(f"平均处理时间: {final_stats['manager_stats']['average_processing_time_ms']:.2f}ms")
            
            return True
            
        except Exception as e:
            self.logger.error(f"运行完整示例失败: {e}")
            return False


def main():
    """主函数"""
    try:
        print("多时间周期数据处理示例")
        print("=" * 50)
        
        # 创建示例实例
        example = MultiTimeframeExample()
        
        # 运行完整示例
        success = example.run_complete_example()
        
        if success:
            print("\n✅ 示例运行成功!")
            print("请查看日志文件 'multi_timeframe_example.log' 获取详细信息")
        else:
            print("\n❌ 示例运行失败!")
            print("请查看日志文件了解错误详情")
        
        return 0 if success else 1
        
    except Exception as e:
        print(f"示例运行出错: {e}")
        return 1


if __name__ == "__main__":
    exit(main())