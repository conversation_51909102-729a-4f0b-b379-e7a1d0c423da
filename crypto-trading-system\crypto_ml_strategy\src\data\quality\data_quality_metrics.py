#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据质量指标和监控组件

实现实时质量评分、历史质量趋势分析、告警机制等功能。
提供全面的数据质量监控和报告能力。
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any, Callable
from datetime import datetime, timedelta
import time
import threading
from collections import deque, defaultdict
from dataclasses import dataclass, field
import json

from .data_quality_core import QualityMetrics, QualityLevel, QualityIssue, ValidationResult


@dataclass
class QualityAlert:
    """质量告警"""
    alert_id: str
    alert_type: str  # 'quality_drop', 'threshold_breach', 'system_error'
    severity: str    # 'low', 'medium', 'high', 'critical'
    message: str
    affected_symbol: str
    affected_timeframe: str
    quality_score: float
    threshold: float
    timestamp: datetime = field(default_factory=datetime.now)
    acknowledged: bool = False


@dataclass
class QualityTrend:
    """质量趋势"""
    symbol: str
    timeframe: str
    time_window: str  # '1h', '1d', '1w'
    avg_score: float
    min_score: float
    max_score: float
    score_trend: float  # 正值表示改善，负值表示恶化
    sample_count: int
    last_update: datetime = field(default_factory=datetime.now)


class QualityMonitor:
    """数据质量监控器"""
    
    def __init__(self, config: Optional[Dict] = None):
        """
        初始化质量监控器
        
        Args:
            config: 监控配置
        """
        self.logger = logging.getLogger('cryptoMlStrategy.data.quality_monitor')
        
        # 默认配置
        self.config = {
            'alert_thresholds': {
                'critical': 0.3,
                'high': 0.5,
                'medium': 0.7,
                'low': 0.8
            },
            'trend_windows': {
                '1h': 60,    # 1小时内的样本数
                '1d': 1440,  # 1天内的样本数
                '1w': 10080  # 1周内的样本数
            },
            'history_retention_days': 30,
            'alert_cooldown_minutes': 15,
            'enable_auto_alerts': True,
            'max_alerts_per_hour': 10
        }
        
        if config:
            self.config.update(config)
        
        # 质量历史记录：{symbol: {timeframe: deque}}
        self._quality_history = defaultdict(lambda: defaultdict(lambda: deque(maxlen=10080)))  # 1周数据
        
        # 告警历史
        self._alerts = deque(maxlen=1000)
        self._alert_counts = defaultdict(int)  # 每小时告警计数
        self._last_alert_time = defaultdict(datetime)  # 最后告警时间
        
        # 趋势缓存
        self._trend_cache = {}
        self._trend_cache_time = {}
        
        # 统计信息
        self._stats = {
            'total_quality_checks': 0,
            'alerts_generated': 0,
            'trends_calculated': 0,
            'start_time': time.time()
        }
        
        # 线程锁
        self._lock = threading.RLock()
        
        # 启动清理线程
        self._cleanup_active = True
        self._start_cleanup_thread()
        
        self.logger.info("数据质量监控器初始化完成")
    
    def record_quality_metrics(self, symbol: str, timeframe: str, metrics: QualityMetrics):
        """
        记录质量指标
        
        Args:
            symbol: 交易对符号
            timeframe: 时间框架
            metrics: 质量指标
        """
        try:
            with self._lock:
                # 记录到历史
                self._quality_history[symbol][timeframe].append({
                    'timestamp': metrics.timestamp,
                    'overall_score': metrics.overall_score,
                    'completeness_score': metrics.completeness_score,
                    'consistency_score': metrics.consistency_score,
                    'accuracy_score': metrics.accuracy_score,
                    'timeliness_score': metrics.timeliness_score,
                    'validity_score': metrics.validity_score,
                    'quality_level': metrics.quality_level.value,
                    'total_records': metrics.total_records,
                    'issues_count': len(metrics.issues)
                })
                
                self._stats['total_quality_checks'] += 1
            
            # 检查是否需要生成告警
            if self.config['enable_auto_alerts']:
                self._check_and_generate_alerts(symbol, timeframe, metrics)
            
            # 清理过期数据
            self._cleanup_expired_data()
            
        except Exception as e:
            self.logger.error(f"记录质量指标失败: {e}")
    
    def get_quality_trend(self, symbol: str, timeframe: str, 
                         window: str = '1h') -> Optional[QualityTrend]:
        """
        获取质量趋势
        
        Args:
            symbol: 交易对符号
            timeframe: 时间框架
            window: 时间窗口
            
        Returns:
            质量趋势
        """
        try:
            cache_key = f"{symbol}_{timeframe}_{window}"
            
            # 检查缓存
            if (cache_key in self._trend_cache and 
                cache_key in self._trend_cache_time and
                time.time() - self._trend_cache_time[cache_key] < 300):  # 5分钟缓存
                return self._trend_cache[cache_key]
            
            # 计算趋势
            trend = self._calculate_trend(symbol, timeframe, window)
            
            # 缓存结果
            if trend:
                self._trend_cache[cache_key] = trend
                self._trend_cache_time[cache_key] = time.time()
                self._stats['trends_calculated'] += 1
            
            return trend
            
        except Exception as e:
            self.logger.error(f"获取质量趋势失败: {e}")
            return None
    
    def get_active_alerts(self, symbol: Optional[str] = None, 
                         severity: Optional[str] = None) -> List[QualityAlert]:
        """
        获取活跃告警
        
        Args:
            symbol: 筛选特定交易对
            severity: 筛选特定严重程度
            
        Returns:
            告警列表
        """
        try:
            with self._lock:
                alerts = [alert for alert in self._alerts if not alert.acknowledged]
                
                if symbol:
                    alerts = [alert for alert in alerts if alert.affected_symbol == symbol]
                
                if severity:
                    alerts = [alert for alert in alerts if alert.severity == severity]
                
                # 按时间倒序排列
                alerts.sort(key=lambda x: x.timestamp, reverse=True)
                
                return alerts
                
        except Exception as e:
            self.logger.error(f"获取活跃告警失败: {e}")
            return []
    
    def acknowledge_alert(self, alert_id: str) -> bool:
        """
        确认告警
        
        Args:
            alert_id: 告警ID
            
        Returns:
            是否成功确认
        """
        try:
            with self._lock:
                for alert in self._alerts:
                    if alert.alert_id == alert_id:
                        alert.acknowledged = True
                        self.logger.info(f"告警已确认: {alert_id}")
                        return True
                
                return False
                
        except Exception as e:
            self.logger.error(f"确认告警失败: {e}")
            return False
    
    def get_quality_summary(self, symbol: Optional[str] = None, 
                          timeframe: Optional[str] = None) -> Dict[str, Any]:
        """
        获取质量摘要
        
        Args:
            symbol: 筛选特定交易对
            timeframe: 筛选特定时间框架
            
        Returns:
            质量摘要
        """
        try:
            with self._lock:
                summary = {
                    'overall_stats': {},
                    'by_symbol': {},
                    'recent_alerts': [],
                    'quality_distribution': {},
                    'trends': {}
                }
                
                # 收集数据
                all_scores = []
                symbol_stats = defaultdict(list)
                
                for sym, timeframes in self._quality_history.items():
                    if symbol and sym != symbol:
                        continue
                    
                    for tf, history in timeframes.items():
                        if timeframe and tf != timeframe:
                            continue
                        
                        if history:
                            recent_scores = [record['overall_score'] for record in list(history)[-100:]]
                            all_scores.extend(recent_scores)
                            symbol_stats[sym].extend(recent_scores)
                
                # 计算总体统计
                if all_scores:
                    summary['overall_stats'] = {
                        'avg_score': np.mean(all_scores),
                        'min_score': np.min(all_scores),
                        'max_score': np.max(all_scores),
                        'std_score': np.std(all_scores),
                        'sample_count': len(all_scores)
                    }
                
                # 按交易对统计
                for sym, scores in symbol_stats.items():
                    if scores:
                        summary['by_symbol'][sym] = {
                            'avg_score': np.mean(scores),
                            'min_score': np.min(scores),
                            'max_score': np.max(scores),
                            'sample_count': len(scores)
                        }
                
                # 质量分布
                if all_scores:
                    summary['quality_distribution'] = {
                        'excellent': sum(1 for s in all_scores if s >= 0.9) / len(all_scores),
                        'good': sum(1 for s in all_scores if 0.7 <= s < 0.9) / len(all_scores),
                        'fair': sum(1 for s in all_scores if 0.5 <= s < 0.7) / len(all_scores),
                        'poor': sum(1 for s in all_scores if 0.3 <= s < 0.5) / len(all_scores),
                        'critical': sum(1 for s in all_scores if s < 0.3) / len(all_scores)
                    }
                
                # 最近告警
                summary['recent_alerts'] = [
                    {
                        'alert_id': alert.alert_id,
                        'alert_type': alert.alert_type,
                        'severity': alert.severity,
                        'message': alert.message,
                        'symbol': alert.affected_symbol,
                        'timeframe': alert.affected_timeframe,
                        'timestamp': alert.timestamp.isoformat(),
                        'acknowledged': alert.acknowledged
                    }
                    for alert in list(self._alerts)[-10:]  # 最近10个告警
                ]
                
                return summary
                
        except Exception as e:
            self.logger.error(f"获取质量摘要失败: {e}")
            return {}
    
    def _calculate_trend(self, symbol: str, timeframe: str, window: str) -> Optional[QualityTrend]:
        """计算质量趋势"""
        try:
            if symbol not in self._quality_history or timeframe not in self._quality_history[symbol]:
                return None
            
            history = self._quality_history[symbol][timeframe]
            if len(history) < 2:
                return None
            
            # 获取时间窗口内的数据
            window_minutes = self.config['trend_windows'].get(window, 60)
            cutoff_time = datetime.now() - timedelta(minutes=window_minutes)
            
            recent_records = [
                record for record in history 
                if record['timestamp'] >= cutoff_time
            ]
            
            if len(recent_records) < 2:
                return None
            
            # 计算统计指标
            scores = [record['overall_score'] for record in recent_records]
            avg_score = np.mean(scores)
            min_score = np.min(scores)
            max_score = np.max(scores)
            
            # 计算趋势（线性回归斜率）
            timestamps = [(record['timestamp'] - recent_records[0]['timestamp']).total_seconds() 
                         for record in recent_records]
            
            if len(timestamps) > 1:
                # 简单线性回归
                n = len(timestamps)
                sum_x = sum(timestamps)
                sum_y = sum(scores)
                sum_xy = sum(x * y for x, y in zip(timestamps, scores))
                sum_x2 = sum(x * x for x in timestamps)
                
                if n * sum_x2 - sum_x * sum_x != 0:
                    slope = (n * sum_xy - sum_x * sum_y) / (n * sum_x2 - sum_x * sum_x)
                else:
                    slope = 0.0
            else:
                slope = 0.0
            
            return QualityTrend(
                symbol=symbol,
                timeframe=timeframe,
                time_window=window,
                avg_score=avg_score,
                min_score=min_score,
                max_score=max_score,
                score_trend=slope,
                sample_count=len(recent_records)
            )
            
        except Exception as e:
            self.logger.error(f"计算质量趋势失败: {e}")
            return None
    
    def _check_and_generate_alerts(self, symbol: str, timeframe: str, metrics: QualityMetrics):
        """检查并生成告警"""
        try:
            current_time = datetime.now()
            alert_key = f"{symbol}_{timeframe}"
            
            # 检查告警冷却时间
            if (alert_key in self._last_alert_time and 
                current_time - self._last_alert_time[alert_key] < 
                timedelta(minutes=self.config['alert_cooldown_minutes'])):
                return
            
            # 检查每小时告警限制
            hour_key = current_time.strftime('%Y%m%d%H')
            if self._alert_counts[hour_key] >= self.config['max_alerts_per_hour']:
                return
            
            # 检查质量阈值
            score = metrics.overall_score
            alert_generated = False
            
            for severity, threshold in self.config['alert_thresholds'].items():
                if score <= threshold:
                    alert = QualityAlert(
                        alert_id=f"{symbol}_{timeframe}_{int(current_time.timestamp())}",
                        alert_type='quality_drop',
                        severity=severity,
                        message=f"{symbol} {timeframe} 数据质量下降到 {score:.3f} (阈值: {threshold})",
                        affected_symbol=symbol,
                        affected_timeframe=timeframe,
                        quality_score=score,
                        threshold=threshold,
                        timestamp=current_time
                    )
                    
                    with self._lock:
                        self._alerts.append(alert)
                        self._last_alert_time[alert_key] = current_time
                        self._alert_counts[hour_key] += 1
                        self._stats['alerts_generated'] += 1
                    
                    self.logger.warning(f"生成质量告警: {alert.message}")
                    alert_generated = True
                    break  # 只生成最严重的告警
            
            # 检查特定问题类型
            if not alert_generated:
                critical_issues = [issue for issue in metrics.issues 
                                 if issue.severity == ValidationResult.ERROR]
                
                if critical_issues:
                    alert = QualityAlert(
                        alert_id=f"{symbol}_{timeframe}_error_{int(current_time.timestamp())}",
                        alert_type='system_error',
                        severity='high',
                        message=f"{symbol} {timeframe} 发现 {len(critical_issues)} 个严重数据问题",
                        affected_symbol=symbol,
                        affected_timeframe=timeframe,
                        quality_score=score,
                        threshold=0.0,
                        timestamp=current_time
                    )
                    
                    with self._lock:
                        self._alerts.append(alert)
                        self._last_alert_time[alert_key] = current_time
                        self._alert_counts[hour_key] += 1
                        self._stats['alerts_generated'] += 1
                    
                    self.logger.error(f"生成错误告警: {alert.message}")
            
        except Exception as e:
            self.logger.error(f"生成告警失败: {e}")
    
    def _cleanup_expired_data(self):
        """清理过期数据"""
        try:
            current_time = datetime.now()
            retention_cutoff = current_time - timedelta(days=self.config['history_retention_days'])
            
            # 清理质量历史
            with self._lock:
                for symbol in self._quality_history:
                    for timeframe in self._quality_history[symbol]:
                        history = self._quality_history[symbol][timeframe]
                        # 移除过期记录
                        while history and history[0]['timestamp'] < retention_cutoff:
                            history.popleft()
                
                # 清理告警计数
                current_hour = current_time.strftime('%Y%m%d%H')
                expired_hours = [hour for hour in self._alert_counts.keys() 
                               if hour < (current_time - timedelta(hours=24)).strftime('%Y%m%d%H')]
                for hour in expired_hours:
                    del self._alert_counts[hour]
            
        except Exception as e:
            self.logger.error(f"清理过期数据失败: {e}")
    
    def _start_cleanup_thread(self):
        """启动清理线程"""
        def cleanup_worker():
            while self._cleanup_active:
                try:
                    self._cleanup_expired_data()
                    time.sleep(3600)  # 每小时清理一次
                except Exception as e:
                    self.logger.error(f"清理线程错误: {e}")
                    time.sleep(300)  # 出错后5分钟重试
        
        cleanup_thread = threading.Thread(target=cleanup_worker, daemon=True)
        cleanup_thread.start()
    
    def export_quality_report(self, symbol: Optional[str] = None, 
                            timeframe: Optional[str] = None,
                            format: str = 'json') -> Optional[str]:
        """
        导出质量报告
        
        Args:
            symbol: 筛选特定交易对
            timeframe: 筛选特定时间框架
            format: 导出格式 ('json', 'csv')
            
        Returns:
            导出文件路径
        """
        try:
            from pathlib import Path
            
            # 获取质量摘要
            summary = self.get_quality_summary(symbol, timeframe)
            
            # 创建导出目录
            export_dir = Path('exports/quality_reports')
            export_dir.mkdir(parents=True, exist_ok=True)
            
            # 生成文件名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            symbol_suffix = f"_{symbol}" if symbol else "_all"
            tf_suffix = f"_{timeframe}" if timeframe else "_all"
            filename = f"quality_report{symbol_suffix}{tf_suffix}_{timestamp}.{format}"
            filepath = export_dir / filename
            
            # 导出数据
            if format == 'json':
                with open(filepath, 'w', encoding='utf-8') as f:
                    json.dump(summary, f, indent=2, ensure_ascii=False, default=str)
            elif format == 'csv':
                # 将摘要转换为CSV格式
                df = pd.DataFrame([summary['overall_stats']])
                df.to_csv(filepath, index=False)
            else:
                raise ValueError(f"不支持的导出格式: {format}")
            
            self.logger.info(f"质量报告已导出到: {filepath}")
            return str(filepath)
            
        except Exception as e:
            self.logger.error(f"导出质量报告失败: {e}")
            return None
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取监控统计信息"""
        with self._lock:
            elapsed_time = time.time() - self._stats['start_time']
            
            return {
                'statistics': self._stats.copy(),
                'performance': {
                    'checks_per_second': self._stats['total_quality_checks'] / max(1, elapsed_time),
                    'alerts_per_hour': self._stats['alerts_generated'] / max(1, elapsed_time / 3600),
                    'active_alerts': len([alert for alert in self._alerts if not alert.acknowledged])
                },
                'data_retention': {
                    'symbols_monitored': len(self._quality_history),
                    'total_timeframes': sum(len(tfs) for tfs in self._quality_history.values()),
                    'total_records': sum(
                        len(history) 
                        for symbol_data in self._quality_history.values()
                        for history in symbol_data.values()
                    )
                },
                'config': self.config
            }
    
    def shutdown(self):
        """关闭监控器"""
        self._cleanup_active = False
        self.logger.info("数据质量监控器已关闭")