package com.crypto.trading.common.util;

import com.crypto.trading.common.constant.SystemConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Cipher;
import javax.crypto.Mac;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;

/**
 * 安全工具类
 * <p>
 * 提供加密、解密和签名相关的工具方法
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public final class SecurityUtil {
    private static final Logger log = LoggerFactory.getLogger(SecurityUtil.class);

    /**
     * 私有构造函数，防止实例化
     */
    private SecurityUtil() {
        throw new IllegalStateException("工具类不允许实例化");
    }

    /**
     * MD5算法名称
     */
    private static final String MD5_ALGORITHM = "MD5";

    /**
     * SHA-256算法名称
     */
    private static final String SHA256_ALGORITHM = "SHA-256";

    /**
     * HMAC-SHA256算法名称
     */
    private static final String HMAC_SHA256_ALGORITHM = "HmacSHA256";

    /**
     * AES算法名称
     */
    private static final String AES_ALGORITHM = "AES";

    /**
     * AES加密模式
     */
    private static final String AES_TRANSFORMATION = "AES/CBC/PKCS5Padding";

    /**
     * 计算MD5哈希
     *
     * @param input 输入字符串
     * @return MD5哈希值（十六进制字符串）
     */
    public static String md5(String input) {
        try {
            MessageDigest md = MessageDigest.getInstance(MD5_ALGORITHM);
            byte[] hashBytes = md.digest(input.getBytes(StandardCharsets.UTF_8));
            return bytesToHex(hashBytes);
        } catch (NoSuchAlgorithmException e) {
            log.error("MD5哈希计算失败", e);
            throw new RuntimeException("MD5哈希计算失败", e);
        }
    }

    /**
     * 计算SHA-256哈希
     *
     * @param input 输入字符串
     * @return SHA-256哈希值（十六进制字符串）
     */
    public static String sha256(String input) {
        try {
            MessageDigest md = MessageDigest.getInstance(SHA256_ALGORITHM);
            byte[] hashBytes = md.digest(input.getBytes(StandardCharsets.UTF_8));
            return bytesToHex(hashBytes);
        } catch (NoSuchAlgorithmException e) {
            log.error("SHA-256哈希计算失败", e);
            throw new RuntimeException("SHA-256哈希计算失败", e);
        }
    }

    /**
     * 计算HMAC-SHA256签名
     *
     * @param input 输入字符串
     * @param key   密钥
     * @return HMAC-SHA256签名（十六进制字符串）
     */
    public static String hmacSha256(String input, String key) {
        try {
            Mac hmacSha256 = Mac.getInstance(HMAC_SHA256_ALGORITHM);
            SecretKeySpec secretKeySpec = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), HMAC_SHA256_ALGORITHM);
            hmacSha256.init(secretKeySpec);
            byte[] hashBytes = hmacSha256.doFinal(input.getBytes(StandardCharsets.UTF_8));
            
            // 确保使用正确的十六进制转换实现
            return bytesToHex(hashBytes);
        } catch (Exception e) {
            log.error("HMAC-SHA256签名计算失败", e);
            throw new RuntimeException("HMAC-SHA256签名计算失败", e);
        }
    }

    /**
     * Base64编码
     *
     * @param input 输入字符串
     * @return Base64编码后的字符串
     */
    public static String base64Encode(String input) {
        return Base64.getEncoder().encodeToString(input.getBytes(StandardCharsets.UTF_8));
    }

    /**
     * Base64编码
     *
     * @param bytes 输入字节数组
     * @return Base64编码后的字符串
     */
    public static String base64Encode(byte[] bytes) {
        return Base64.getEncoder().encodeToString(bytes);
    }

    /**
     * Base64解码
     *
     * @param input Base64编码的字符串
     * @return 解码后的字符串
     */
    public static String base64Decode(String input) {
        byte[] decodedBytes = Base64.getDecoder().decode(input);
        return new String(decodedBytes, StandardCharsets.UTF_8);
    }

    /**
     * Base64解码为字节数组
     *
     * @param input Base64编码的字符串
     * @return 解码后的字节数组
     */
    public static byte[] base64DecodeToBytes(String input) {
        return Base64.getDecoder().decode(input);
    }

    /**
     * URL编码
     *
     * @param input 输入字符串
     * @return URL编码后的字符串
     */
    public static String urlEncode(String input) {
        try {
            return URLEncoder.encode(input, SystemConstants.UTF8);
        } catch (Exception e) {
            log.error("URL编码失败", e);
            throw new RuntimeException("URL编码失败", e);
        }
    }

    /**
     * URL解码
     *
     * @param input URL编码的字符串
     * @return 解码后的字符串
     */
    public static String urlDecode(String input) {
        try {
            return URLDecoder.decode(input, SystemConstants.UTF8);
        } catch (Exception e) {
            log.error("URL解码失败", e);
            throw new RuntimeException("URL解码失败", e);
        }
    }

    /**
     * AES加密
     *
     * @param input 输入字符串
     * @param key   密钥（16字节）
     * @param iv    初始化向量（16字节）
     * @return 加密后的Base64字符串
     */
    public static String aesEncrypt(String input, String key, String iv) {
        try {
            byte[] keyBytes = key.getBytes(StandardCharsets.UTF_8);
            byte[] ivBytes = iv.getBytes(StandardCharsets.UTF_8);
            SecretKeySpec keySpec = new SecretKeySpec(keyBytes, AES_ALGORITHM);
            IvParameterSpec ivSpec = new IvParameterSpec(ivBytes);

            Cipher cipher = Cipher.getInstance(AES_TRANSFORMATION);
            cipher.init(Cipher.ENCRYPT_MODE, keySpec, ivSpec);
            byte[] encryptedBytes = cipher.doFinal(input.getBytes(StandardCharsets.UTF_8));
            return base64Encode(encryptedBytes);
        } catch (Exception e) {
            log.error("AES加密失败", e);
            throw new RuntimeException("AES加密失败", e);
        }
    }

    /**
     * AES解密
     *
     * @param input 加密后的Base64字符串
     * @param key   密钥（16字节）
     * @param iv    初始化向量（16字节）
     * @return 解密后的字符串
     */
    public static String aesDecrypt(String input, String key, String iv) {
        try {
            byte[] keyBytes = key.getBytes(StandardCharsets.UTF_8);
            byte[] ivBytes = iv.getBytes(StandardCharsets.UTF_8);
            SecretKeySpec keySpec = new SecretKeySpec(keyBytes, AES_ALGORITHM);
            IvParameterSpec ivSpec = new IvParameterSpec(ivBytes);

            Cipher cipher = Cipher.getInstance(AES_TRANSFORMATION);
            cipher.init(Cipher.DECRYPT_MODE, keySpec, ivSpec);
            byte[] decryptedBytes = cipher.doFinal(base64DecodeToBytes(input));
            return new String(decryptedBytes, StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("AES解密失败", e);
            throw new RuntimeException("AES解密失败", e);
        }
    }

    /**
     * 字节数组转十六进制字符串
     *
     * @param bytes 字节数组
     * @return 十六进制字符串
     */
    public static String bytesToHex(byte[] bytes) {
        if (bytes == null) {
            return null;
        }
        StringBuilder hexString = new StringBuilder(2 * bytes.length);
        for (byte b : bytes) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) {
                hexString.append('0');
            }
            hexString.append(hex);
        }
        return hexString.toString();
    }

    /**
     * 十六进制字符串转字节数组
     *
     * @param hexString 十六进制字符串
     * @return 字节数组
     */
    public static byte[] hexToBytes(String hexString) {
        int len = hexString.length();
        byte[] data = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            data[i / 2] = (byte) ((Character.digit(hexString.charAt(i), 16) << 4)
                    + Character.digit(hexString.charAt(i + 1), 16));
        }
        return data;
    }
} 