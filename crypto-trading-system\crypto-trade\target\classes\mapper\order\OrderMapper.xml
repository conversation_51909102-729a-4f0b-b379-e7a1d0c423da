<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.crypto.trading.trade.repository.mapper.order.OrderMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.crypto.trading.trade.model.entity.order.OrderEntity">
        <id column="id" property="id"/>
        <result column="order_id" property="orderId"/>
        <result column="client_order_id" property="clientOrderId"/>
        <result column="signal_id" property="signalId"/>
        <result column="symbol" property="symbol"/>
        <result column="side" property="side"/>
        <result column="position_side" property="positionSide"/>
        <result column="order_type" property="orderType"/>
        <result column="time_in_force" property="timeInForce"/>
        <result column="quantity" property="quantity"/>
        <result column="price" property="price"/>
        <result column="executed_price" property="executedPrice"/>
        <result column="executed_quantity" property="executedQuantity"/>
        <result column="status" property="status"/>
        <result column="error_code" property="errorCode"/>
        <result column="error_message" property="errorMessage"/>
        <result column="strategy" property="strategy"/>
        <result column="created_time" property="createdTime"/>
        <result column="updated_time" property="updatedTime"/>
        <result column="executed_time" property="executedTime"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <!-- 基础字段列表 -->
    <sql id="Base_Column_List">
        id, order_id, client_order_id, signal_id, symbol, side, position_side, order_type, time_in_force,
        quantity, price, executed_price, executed_quantity, status, error_code, error_message,
        strategy, created_time, updated_time, executed_time, is_deleted
    </sql>

    <!-- 分页查询订单列表 -->
    <select id="pageOrders" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_order
        <where>
            is_deleted = 0
            <if test="symbol != null and symbol != ''">
                AND symbol = #{symbol}
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
            <if test="strategy != null and strategy != ''">
                AND strategy = #{strategy}
            </if>
            <if test="startTime != null">
                AND created_time &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                AND created_time &lt;= #{endTime}
            </if>
        </where>
        ORDER BY created_time DESC
    </select>

    <!-- 批量插入订单 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO t_order (
            order_id, client_order_id, signal_id, symbol, side, position_side, order_type, time_in_force,
            quantity, price, executed_price, executed_quantity, status, error_code, error_message,
            strategy, created_time, updated_time, executed_time, is_deleted
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.orderId},
                #{item.clientOrderId},
                #{item.signalId},
                #{item.symbol},
                #{item.side},
                #{item.positionSide},
                #{item.orderType},
                #{item.timeInForce},
                #{item.quantity},
                #{item.price},
                #{item.executedPrice},
                #{item.executedQuantity},
                #{item.status},
                #{item.errorCode},
                #{item.errorMessage},
                #{item.strategy},
                #{item.createdTime},
                #{item.updatedTime},
                #{item.executedTime},
                #{item.isDeleted}
            )
        </foreach>
    </insert>

    <!-- 批量更新订单状态 -->
    <update id="batchUpdateStatus">
        UPDATE t_order
        SET status = #{status},
            updated_time = #{updatedTime}
        WHERE order_id IN
        <foreach collection="orderIds" item="orderId" open="(" separator="," close=")">
            #{orderId}
        </foreach>
    </update>

    <!-- 查询一段时间内的订单统计信息 -->
    <select id="getOrderStatistics" resultType="java.util.Map">
        SELECT
            symbol,
            side,
            COUNT(*) as total_count,
            SUM(CASE WHEN status = 'FILLED' THEN 1 ELSE 0 END) as filled_count,
            SUM(CASE WHEN status = 'CANCELED' THEN 1 ELSE 0 END) as canceled_count,
            SUM(CASE WHEN status = 'REJECTED' THEN 1 ELSE 0 END) as rejected_count,
            AVG(CASE WHEN executed_price IS NOT NULL THEN executed_price ELSE 0 END) as avg_price,
            SUM(CASE WHEN executed_quantity IS NOT NULL THEN executed_quantity ELSE 0 END) as total_quantity
        FROM t_order
        WHERE created_time BETWEEN #{startTime} AND #{endTime}
        <if test="symbol != null and symbol != ''">
            AND symbol = #{symbol}
        </if>
        <if test="strategy != null and strategy != ''">
            AND strategy = #{strategy}
        </if>
        GROUP BY symbol, side
    </select>

    <!-- 查询未完成的订单 -->
    <select id="findPendingOrders" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_order
        WHERE status IN ('NEW', 'PARTIALLY_FILLED')
        AND is_deleted = 0
        <if test="symbol != null and symbol != ''">
            AND symbol = #{symbol}
        </if>
        ORDER BY created_time ASC
    </select>

    <!-- 查询长时间未完成的订单 -->
    <select id="findStaleOrders" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_order
        WHERE status IN ('NEW', 'PARTIALLY_FILLED')
        AND created_time &lt; #{cutoffTime}
        AND is_deleted = 0
        ORDER BY created_time ASC
    </select>

    <!-- 清理过期数据 -->
    <update id="cleanupExpiredData">
        UPDATE t_order
        SET is_deleted = 1
        WHERE created_time &lt; #{cutoffTime}
        AND status IN ('FILLED', 'CANCELED', 'REJECTED', 'EXPIRED')
    </update>
</mapper>