#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
模型压缩器

实现多种模型压缩技术，包括量化、剪枝、权重共享等，
进一步优化蒸馏后的学生模型性能。
"""

import logging
import numpy as np
import torch
import torch.nn as nn
import torch.nn.utils.prune as prune
from typing import Dict, List, Tuple, Optional, Any, Union
from pathlib import Path
import json
import copy
from collections import OrderedDict


class ModelCompressor:
    """模型压缩器，提供多种压缩技术"""
    
    def __init__(self, config: Dict = None):
        """
        初始化模型压缩器
        
        Args:
            config: 配置参数字典
        """
        self.logger = logging.getLogger('cryptoMlStrategy.model.distillation.compressor')
        
        # 默认配置
        self.config = {
            'quantization': {
                'enabled': True,
                'bits': 8,  # 量化位数
                'method': 'dynamic'  # 'dynamic' 或 'static'
            },
            'pruning': {
                'enabled': True,
                'sparsity': 0.3,  # 剪枝稀疏度
                'method': 'magnitude',  # 'magnitude' 或 'structured'
                'global_pruning': True
            },
            'weight_sharing': {
                'enabled': False,
                'clusters': 16  # 权重聚类数量
            },
            'optimization': {
                'fuse_layers': True,  # 层融合
                'remove_dropout': True,  # 移除Dropout层
                'optimize_for_inference': True
            }
        }
        
        # 更新用户配置
        if config:
            self.config.update(config)
        
        self.original_model = None
        self.compressed_model = None
        self.compression_stats = {}
        
        self.logger.info("模型压缩器初始化完成")
    
    def compress_model(self, model: nn.Module, 
                      validation_data: Tuple[torch.Tensor, torch.Tensor] = None) -> nn.Module:
        """
        压缩模型
        
        Args:
            model: 要压缩的模型
            validation_data: 验证数据，用于评估压缩效果
            
        Returns:
            压缩后的模型
        """
        try:
            self.original_model = copy.deepcopy(model)
            self.compressed_model = copy.deepcopy(model)
            
            # 记录原始模型统计
            original_stats = self._get_model_stats(self.original_model)
            self.logger.info(f"原始模型统计: {original_stats}")
            
            # 1. 优化模型结构
            if self.config['optimization']['optimize_for_inference']:
                self.compressed_model = self._optimize_for_inference(self.compressed_model)
            
            # 2. 剪枝
            if self.config['pruning']['enabled']:
                self.compressed_model = self._apply_pruning(self.compressed_model)
            
            # 3. 量化
            if self.config['quantization']['enabled']:
                self.compressed_model = self._apply_quantization(self.compressed_model)
            
            # 4. 权重共享
            if self.config['weight_sharing']['enabled']:
                self.compressed_model = self._apply_weight_sharing(self.compressed_model)
            
            # 记录压缩后模型统计
            compressed_stats = self._get_model_stats(self.compressed_model)
            self.logger.info(f"压缩后模型统计: {compressed_stats}")
            
            # 计算压缩比
            self.compression_stats = self._calculate_compression_ratio(original_stats, compressed_stats)
            
            # 验证压缩效果
            if validation_data is not None:
                self._validate_compression(validation_data)
            
            self.logger.info("模型压缩完成")
            return self.compressed_model
            
        except Exception as e:
            self.logger.error(f"模型压缩失败: {e}")
            raise
    
    def _optimize_for_inference(self, model: nn.Module) -> nn.Module:
        """
        优化模型结构以提高推理性能
        
        Args:
            model: 输入模型
            
        Returns:
            优化后的模型
        """
        try:
            # 移除Dropout层
            if self.config['optimization']['remove_dropout']:
                model = self._remove_dropout_layers(model)
            
            # 融合BatchNorm层
            if self.config['optimization']['fuse_layers']:
                model = self._fuse_batch_norm(model)
            
            # 设置为评估模式
            model.eval()
            
            self.logger.info("模型结构优化完成")
            return model
            
        except Exception as e:
            self.logger.error(f"模型结构优化失败: {e}")
            return model
    
    def _remove_dropout_layers(self, model: nn.Module) -> nn.Module:
        """移除Dropout层"""
        for name, module in list(model.named_children()):
            if isinstance(module, nn.Dropout):
                setattr(model, name, nn.Identity())
            else:
                self._remove_dropout_layers(module)
        return model
    
    def _fuse_batch_norm(self, model: nn.Module) -> nn.Module:
        """融合BatchNorm层到前面的Linear层"""
        modules = list(model.named_children())
        
        for i in range(len(modules) - 1):
            name1, module1 = modules[i]
            name2, module2 = modules[i + 1]
            
            if isinstance(module1, nn.Linear) and isinstance(module2, nn.BatchNorm1d):
                # 融合Linear和BatchNorm
                fused_layer = self._fuse_linear_bn(module1, module2)
                setattr(model, name1, fused_layer)
                setattr(model, name2, nn.Identity())
        
        return model
    
    def _fuse_linear_bn(self, linear: nn.Linear, bn: nn.BatchNorm1d) -> nn.Linear:
        """融合Linear和BatchNorm层"""
        # 计算融合后的权重和偏置
        w = linear.weight.clone()
        b = linear.bias.clone() if linear.bias is not None else torch.zeros(linear.out_features)
        
        # BatchNorm参数
        gamma = bn.weight.clone()
        beta = bn.bias.clone()
        mean = bn.running_mean.clone()
        var = bn.running_var.clone()
        eps = bn.eps
        
        # 融合计算
        std = torch.sqrt(var + eps)
        w_fused = w * (gamma / std).unsqueeze(1)
        b_fused = (b - mean) * gamma / std + beta
        
        # 创建新的Linear层
        fused_linear = nn.Linear(linear.in_features, linear.out_features)
        fused_linear.weight.data = w_fused
        fused_linear.bias.data = b_fused
        
        return fused_linear
    
    def _apply_pruning(self, model: nn.Module) -> nn.Module:
        """
        应用剪枝
        
        Args:
            model: 输入模型
            
        Returns:
            剪枝后的模型
        """
        try:
            sparsity = self.config['pruning']['sparsity']
            method = self.config['pruning']['method']
            global_pruning = self.config['pruning']['global_pruning']
            
            if method == 'magnitude':
                if global_pruning:
                    # 全局幅度剪枝
                    parameters_to_prune = []
                    for module in model.modules():
                        if isinstance(module, nn.Linear):
                            parameters_to_prune.append((module, 'weight'))
                    
                    prune.global_unstructured(
                        parameters_to_prune,
                        pruning_method=prune.L1Unstructured,
                        amount=sparsity
                    )
                else:
                    # 局部幅度剪枝
                    for module in model.modules():
                        if isinstance(module, nn.Linear):
                            prune.l1_unstructured(module, name='weight', amount=sparsity)
            
            elif method == 'structured':
                # 结构化剪枝
                for module in model.modules():
                    if isinstance(module, nn.Linear):
                        prune.ln_structured(module, name='weight', amount=sparsity, n=2, dim=0)
            
            # 移除剪枝重参数化
            for module in model.modules():
                if isinstance(module, nn.Linear):
                    try:
                        prune.remove(module, 'weight')
                    except:
                        pass  # 如果没有剪枝参数，忽略错误
            
            self.logger.info(f"剪枝完成，稀疏度: {sparsity}")
            return model
            
        except Exception as e:
            self.logger.error(f"剪枝失败: {e}")
            return model
    
    def _apply_quantization(self, model: nn.Module) -> nn.Module:
        """
        应用量化
        
        Args:
            model: 输入模型
            
        Returns:
            量化后的模型
        """
        try:
            method = self.config['quantization']['method']
            
            if method == 'dynamic':
                # 动态量化
                quantized_model = torch.quantization.quantize_dynamic(
                    model,
                    {nn.Linear},
                    dtype=torch.qint8
                )
            elif method == 'static':
                # 静态量化（需要校准数据）
                model.qconfig = torch.quantization.get_default_qconfig('fbgemm')
                torch.quantization.prepare(model, inplace=True)
                # 这里需要校准数据，暂时跳过
                quantized_model = torch.quantization.convert(model, inplace=False)
            else:
                quantized_model = model
            
            self.logger.info(f"量化完成，方法: {method}")
            return quantized_model
            
        except Exception as e:
            self.logger.error(f"量化失败: {e}")
            return model
    
    def _apply_weight_sharing(self, model: nn.Module) -> nn.Module:
        """
        应用权重共享
        
        Args:
            model: 输入模型
            
        Returns:
            权重共享后的模型
        """
        try:
            clusters = self.config['weight_sharing']['clusters']
            
            for module in model.modules():
                if isinstance(module, nn.Linear):
                    # 对权重进行K-means聚类
                    weight = module.weight.data.flatten()
                    centroids = self._kmeans_1d(weight.numpy(), clusters)
                    
                    # 将权重替换为最近的聚类中心
                    quantized_weight = torch.zeros_like(weight)
                    for i, w in enumerate(weight):
                        closest_centroid = min(centroids, key=lambda x: abs(x - w.item()))
                        quantized_weight[i] = closest_centroid
                    
                    module.weight.data = quantized_weight.reshape(module.weight.shape)
            
            self.logger.info(f"权重共享完成，聚类数: {clusters}")
            return model
            
        except Exception as e:
            self.logger.error(f"权重共享失败: {e}")
            return model
    
    def _kmeans_1d(self, data: np.ndarray, k: int, max_iters: int = 100) -> List[float]:
        """一维K-means聚类"""
        # 初始化聚类中心
        centroids = np.linspace(data.min(), data.max(), k)
        
        for _ in range(max_iters):
            # 分配数据点到最近的聚类中心
            distances = np.abs(data[:, np.newaxis] - centroids)
            assignments = np.argmin(distances, axis=1)
            
            # 更新聚类中心
            new_centroids = []
            for i in range(k):
                cluster_points = data[assignments == i]
                if len(cluster_points) > 0:
                    new_centroids.append(cluster_points.mean())
                else:
                    new_centroids.append(centroids[i])
            
            new_centroids = np.array(new_centroids)
            
            # 检查收敛
            if np.allclose(centroids, new_centroids):
                break
            
            centroids = new_centroids
        
        return centroids.tolist()
    
    def _get_model_stats(self, model: nn.Module) -> Dict[str, Any]:
        """获取模型统计信息"""
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        
        # 计算模型大小（MB）
        param_size = sum(p.numel() * p.element_size() for p in model.parameters())
        buffer_size = sum(b.numel() * b.element_size() for b in model.buffers())
        model_size_mb = (param_size + buffer_size) / (1024 * 1024)
        
        # 计算稀疏度
        zero_params = sum((p == 0).sum().item() for p in model.parameters())
        sparsity = zero_params / total_params if total_params > 0 else 0
        
        return {
            'total_parameters': total_params,
            'trainable_parameters': trainable_params,
            'model_size_mb': model_size_mb,
            'sparsity': sparsity
        }
    
    def _calculate_compression_ratio(self, original_stats: Dict, compressed_stats: Dict) -> Dict[str, float]:
        """计算压缩比"""
        return {
            'parameter_reduction': 1 - (compressed_stats['total_parameters'] / original_stats['total_parameters']),
            'size_reduction': 1 - (compressed_stats['model_size_mb'] / original_stats['model_size_mb']),
            'sparsity_increase': compressed_stats['sparsity'] - original_stats['sparsity'],
            'compression_ratio': original_stats['model_size_mb'] / compressed_stats['model_size_mb']
        }
    
    def _validate_compression(self, validation_data: Tuple[torch.Tensor, torch.Tensor]):
        """验证压缩效果"""
        try:
            X_val, y_val = validation_data
            
            # 原始模型预测
            self.original_model.eval()
            with torch.no_grad():
                original_pred = self.original_model(X_val)[0] if isinstance(self.original_model(X_val), tuple) else self.original_model(X_val)
                original_loss = nn.MSELoss()(original_pred.squeeze(), y_val).item()
            
            # 压缩模型预测
            self.compressed_model.eval()
            with torch.no_grad():
                compressed_pred = self.compressed_model(X_val)[0] if isinstance(self.compressed_model(X_val), tuple) else self.compressed_model(X_val)
                compressed_loss = nn.MSELoss()(compressed_pred.squeeze(), y_val).item()
            
            # 计算性能损失
            performance_loss = (compressed_loss - original_loss) / original_loss
            
            self.compression_stats['original_loss'] = original_loss
            self.compression_stats['compressed_loss'] = compressed_loss
            self.compression_stats['performance_loss'] = performance_loss
            
            self.logger.info(f"压缩验证 - 原始损失: {original_loss:.6f}, "
                           f"压缩损失: {compressed_loss:.6f}, "
                           f"性能损失: {performance_loss:.2%}")
            
        except Exception as e:
            self.logger.error(f"压缩验证失败: {e}")
    
    def save_compressed_model(self, save_path: str):
        """保存压缩模型"""
        try:
            save_path = Path(save_path)
            save_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 保存压缩模型
            torch.save(self.compressed_model.state_dict(), save_path)
            
            # 保存压缩统计
            stats_path = save_path.parent / f"{save_path.stem}_compression_stats.json"
            with open(stats_path, 'w') as f:
                json.dump(self.compression_stats, f, indent=2)
            
            self.logger.info(f"压缩模型保存到: {save_path}")
            
        except Exception as e:
            self.logger.error(f"保存压缩模型失败: {e}")
            raise
    
    def load_compressed_model(self, model_class: type, load_path: str, *args, **kwargs):
        """加载压缩模型"""
        try:
            # 创建模型实例
            model = model_class(*args, **kwargs)
            
            # 加载状态字典
            state_dict = torch.load(load_path, map_location='cpu')
            model.load_state_dict(state_dict)
            
            self.compressed_model = model
            
            # 加载压缩统计
            stats_path = Path(load_path).parent / f"{Path(load_path).stem}_compression_stats.json"
            if stats_path.exists():
                with open(stats_path, 'r') as f:
                    self.compression_stats = json.load(f)
            
            self.logger.info(f"压缩模型从 {load_path} 加载完成")
            return model
            
        except Exception as e:
            self.logger.error(f"加载压缩模型失败: {e}")
            raise
    
    def get_compression_info(self) -> Dict[str, Any]:
        """获取压缩信息"""
        return {
            'config': self.config,
            'compression_stats': self.compression_stats,
            'original_model_info': self._get_model_stats(self.original_model) if self.original_model else None,
            'compressed_model_info': self._get_model_stats(self.compressed_model) if self.compressed_model else None
        }
    
    def benchmark_inference_speed(self, input_shape: Tuple[int, ...], num_runs: int = 100) -> Dict[str, float]:
        """基准测试推理速度"""
        try:
            import time
            
            # 创建测试输入
            test_input = torch.randn(input_shape)
            
            # 测试原始模型
            if self.original_model is not None:
                self.original_model.eval()
                start_time = time.time()
                for _ in range(num_runs):
                    with torch.no_grad():
                        _ = self.original_model(test_input)
                original_time = (time.time() - start_time) / num_runs
            else:
                original_time = 0
            
            # 测试压缩模型
            if self.compressed_model is not None:
                self.compressed_model.eval()
                start_time = time.time()
                for _ in range(num_runs):
                    with torch.no_grad():
                        _ = self.compressed_model(test_input)
                compressed_time = (time.time() - start_time) / num_runs
            else:
                compressed_time = 0
            
            speedup = original_time / compressed_time if compressed_time > 0 else 0
            
            benchmark_results = {
                'original_inference_time': original_time,
                'compressed_inference_time': compressed_time,
                'speedup': speedup
            }
            
            self.logger.info(f"推理速度基准测试完成: {benchmark_results}")
            return benchmark_results
            
        except Exception as e:
            self.logger.error(f"推理速度基准测试失败: {e}")
            return {}