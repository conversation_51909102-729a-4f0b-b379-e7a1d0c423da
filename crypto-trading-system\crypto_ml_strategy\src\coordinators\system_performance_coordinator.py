"""
Crypto ML Strategy - 系统性能协调器

该模块实现了Task 12性能验证系统的核心协调器，负责统一管理
所有验证器并提供全面的性能验证服务。

主要功能：
- SystemPerformanceValidator: 系统性能验证协调器
- 全面性能验证流程管理
- 性能评分计算

作者: Crypto ML Strategy Team
日期: 2024-01-20
版本: 1.0.0
"""

import asyncio
from typing import Dict, Optional
from core.validation_result_types import ValidationResult
from validators.latency_throughput_validators import LatencyValidator, ThroughputValidator
from validators.memory_accuracy_validators import MemoryValidator, AccuracyValidator


class SystemPerformanceValidator:
    """系统性能验证协调器"""
    
    def __init__(self):
        self.latency_validator = LatencyValidator()
        self.throughput_validator = ThroughputValidator()
        self.memory_validator = MemoryValidator()
        self.accuracy_validator = AccuracyValidator()
    
    async def run_comprehensive_validation(self) -> Dict[str, ValidationResult]:
        """运行全面性能验证，返回所有验证结果"""
        try:
            results = {}
            
            # 延迟验证
            results["latency"] = self.latency_validator.validate_signal_generation_latency()
            
            # 吞吐量验证
            results["throughput"] = self.throughput_validator.validate_prediction_throughput()
            
            # 内存验证
            results["memory"] = self.memory_validator.validate_memory_usage()
            
            # 精度验证
            results["accuracy"] = self.accuracy_validator.validate_model_accuracy()
            
            # 计算总体性能评分
            overall_score = self._calculate_performance_score(results)
            
            # 统计总体通过率
            passed_count = sum(1 for result in results.values() if result.passed)
            total_count = len(results)
            overall_pass_rate = passed_count / total_count
            
            return results
            
        except Exception as e:
            raise
    
    async def run_quick_validation(self, reduced_samples: bool = True) -> Dict[str, ValidationResult]:
        """运行快速验证（用于认证测试）"""
        try:
            results = {}
            
            # 使用减少的样本量进行快速验证
            sample_size = 30 if reduced_samples else 100
            duration = 3 if reduced_samples else 10
            accuracy_samples = 200 if reduced_samples else 1000
            
            # 延迟验证
            results["latency"] = self.latency_validator.validate_signal_generation_latency(test_samples=sample_size)
            
            # 吞吐量验证
            results["throughput"] = self.throughput_validator.validate_prediction_throughput(test_duration_seconds=duration)
            
            # 内存验证
            results["memory"] = self.memory_validator.validate_memory_usage()
            
            # 精度验证
            results["accuracy"] = self.accuracy_validator.validate_model_accuracy(test_samples=accuracy_samples)
            
            return results
            
        except Exception as e:
            raise
    
    def _calculate_performance_score(self, results: Dict[str, ValidationResult]) -> float:
        """计算系统性能评分 (0-100分)"""
        base_score = 0
        bonus_score = 0
        
        # 基础分数：每项通过得25分
        for result in results.values():
            if result.passed:
                base_score += 25
        
        # 奖励分数：超额完成目标
        if "latency" in results and results["latency"].passed:
            improvement = (results["latency"].target_value - results["latency"].actual_value) / results["latency"].target_value
            bonus_score += min(10, improvement * 20)  # 最多10分奖励
        
        if "throughput" in results and results["throughput"].passed:
            improvement = (results["throughput"].actual_value - results["throughput"].target_value) / results["throughput"].target_value
            bonus_score += min(10, improvement * 20)  # 最多10分奖励
        
        return min(100, base_score + bonus_score)
    
    def get_validation_summary(self, results: Dict[str, ValidationResult]) -> Dict[str, any]:
        """获取验证结果摘要"""
        passed_count = sum(1 for result in results.values() if result.passed)
        total_count = len(results)
        
        # 统计显著性测试
        significant_tests = sum(1 for result in results.values() if result.p_value < 0.05)
        
        # Bonferroni校正
        corrected_alpha = 0.05 / total_count if total_count > 0 else 0.05
        significant_after_correction = sum(1 for result in results.values() 
                                         if result.p_value < corrected_alpha)
        
        return {
            "passed_tests": passed_count,
            "total_tests": total_count,
            "pass_rate": passed_count / total_count if total_count > 0 else 0,
            "all_passed": passed_count == total_count,
            "significant_tests": significant_tests,
            "bonferroni_corrected_significant": significant_after_correction,
            "corrected_alpha": corrected_alpha,
            "performance_score": self._calculate_performance_score(results)
        }


# 全局实例
_global_system_validator: Optional[SystemPerformanceValidator] = None


def get_global_system_validator() -> SystemPerformanceValidator:
    """获取全局系统性能验证器实例"""
    global _global_system_validator
    
    if _global_system_validator is None:
        _global_system_validator = SystemPerformanceValidator()
    
    return _global_system_validator