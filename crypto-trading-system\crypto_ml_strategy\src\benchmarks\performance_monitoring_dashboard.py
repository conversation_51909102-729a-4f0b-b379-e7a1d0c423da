"""
Crypto ML Strategy - 性能监控仪表板

该模块实现了实时性能监控仪表板功能，包括实时监控、
历史跟踪、告警管理和仪表板管理。

主要功能：
- PerformanceMonitor: 性能监控器主管理器
- RealTimeMonitor: 实时性能监控
- HistoricalMonitor: 历史性能监控
- AlertManager: 性能告警管理器
- DashboardManager: 性能仪表板管理器

作者: Crypto ML Strategy Team
日期: 2024-01-20
版本: 1.0.0
"""

import asyncio
import json
import threading
import time
from collections import defaultdict, deque
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Dict, List, Optional, Callable
from infrastructure.logging.logging_core_manager import get_logger
from performance_metrics_collector import get_global_metrics_collector, MetricsSnapshot
from .benchmark_core_framework import BenchmarkResult


class AlertSeverity(Enum):
    """告警严重程度"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class AlertStatus(Enum):
    """告警状态"""
    ACTIVE = "active"
    ACKNOWLEDGED = "acknowledged"
    RESOLVED = "resolved"


@dataclass
class PerformanceAlert:
    """性能告警"""
    alert_id: str
    metric_name: str
    current_value: float
    threshold_value: float
    severity: AlertSeverity
    status: AlertStatus
    message: str
    timestamp: datetime
    acknowledged_by: Optional[str] = None
    acknowledged_at: Optional[datetime] = None
    resolved_at: Optional[datetime] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "alert_id": self.alert_id,
            "metric_name": self.metric_name,
            "current_value": self.current_value,
            "threshold_value": self.threshold_value,
            "severity": self.severity.value,
            "status": self.status.value,
            "message": self.message,
            "timestamp": self.timestamp.isoformat(),
            "acknowledged_by": self.acknowledged_by,
            "acknowledged_at": self.acknowledged_at.isoformat() if self.acknowledged_at else None,
            "resolved_at": self.resolved_at.isoformat() if self.resolved_at else None,
            "metadata": self.metadata
        }


@dataclass
class MonitoringConfig:
    """监控配置"""
    enabled: bool = True
    sampling_interval_seconds: float = 1.0
    alert_check_interval_seconds: float = 5.0
    history_retention_hours: int = 24
    dashboard_refresh_interval_seconds: float = 2.0
    
    # 告警阈值
    alert_thresholds: Dict[str, Dict[str, float]] = field(default_factory=lambda: {
        "execution_time_ms": {"high": 1000, "critical": 5000},
        "throughput_ops_per_sec": {"low": 100, "critical": 10},
        "latency_p95_ms": {"high": 500, "critical": 2000},
        "memory_usage_mb": {"high": 1000, "critical": 2000},
        "cpu_usage_percent": {"high": 80, "critical": 95},
        "error_rate": {"medium": 0.05, "high": 0.1, "critical": 0.2}
    })
    
    def validate(self) -> bool:
        """验证配置有效性"""
        try:
            if self.sampling_interval_seconds <= 0 or self.alert_check_interval_seconds <= 0:
                return False
            
            if self.history_retention_hours <= 0:
                return False
            
            return True
        except Exception:
            return False


class RealTimeMonitor:
    """实时性能监控器"""
    
    def __init__(self, config: MonitoringConfig):
        self.config = config
        self.logger = get_logger(f"{__name__}.RealTimeMonitor")
        self.metrics_collector = get_global_metrics_collector()
        
        # 实时数据
        self._current_metrics: Dict[str, float] = {}
        self._metrics_history: deque = deque(maxlen=1000)
        self._monitoring = False
        self._monitor_task: Optional[asyncio.Task] = None
        self._lock = threading.RLock()
        
        # 回调函数
        self._metric_callbacks: Dict[str, List[Callable]] = defaultdict(list)
    
    async def start_monitoring(self) -> None:
        """开始实时监控"""
        with self._lock:
            if self._monitoring:
                return
            
            self._monitoring = True
            self._monitor_task = asyncio.create_task(self._monitoring_loop())
            
            self.logger.info("Real-time monitoring started")
    
    async def stop_monitoring(self) -> None:
        """停止实时监控"""
        with self._lock:
            if not self._monitoring:
                return
            
            self._monitoring = False
            
            if self._monitor_task and not self._monitor_task.done():
                self._monitor_task.cancel()
                try:
                    await self._monitor_task
                except asyncio.CancelledError:
                    pass
            
            self.logger.info("Real-time monitoring stopped")
    
    async def _monitoring_loop(self) -> None:
        """监控循环"""
        while self._monitoring:
            try:
                # 收集当前指标
                snapshot = await self.metrics_collector.collect_snapshot()
                
                # 更新当前指标
                with self._lock:
                    self._current_metrics = {
                        **snapshot.system_metrics,
                        **snapshot.application_metrics,
                        **snapshot.business_metrics,
                        **snapshot.custom_metrics
                    }
                    
                    self._metrics_history.append({
                        "timestamp": snapshot.timestamp,
                        "metrics": self._current_metrics.copy()
                    })
                
                # 触发回调
                await self._trigger_callbacks()
                
                await asyncio.sleep(self.config.sampling_interval_seconds)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error in real-time monitoring: {e}")
                await asyncio.sleep(self.config.sampling_interval_seconds)
    
    async def _trigger_callbacks(self) -> None:
        """触发指标回调"""
        try:
            for metric_name, value in self._current_metrics.items():
                callbacks = self._metric_callbacks.get(metric_name, [])
                
                for callback in callbacks:
                    try:
                        if asyncio.iscoroutinefunction(callback):
                            await callback(metric_name, value)
                        else:
                            callback(metric_name, value)
                    except Exception as e:
                        self.logger.error(f"Error in metric callback for {metric_name}: {e}")
        except Exception as e:
            self.logger.error(f"Error triggering callbacks: {e}")
    
    def add_metric_callback(self, metric_name: str, callback: Callable) -> None:
        """添加指标回调"""
        with self._lock:
            self._metric_callbacks[metric_name].append(callback)
    
    def remove_metric_callback(self, metric_name: str, callback: Callable) -> None:
        """移除指标回调"""
        with self._lock:
            if metric_name in self._metric_callbacks:
                try:
                    self._metric_callbacks[metric_name].remove(callback)
                except ValueError:
                    pass
    
    def get_current_metrics(self) -> Dict[str, float]:
        """获取当前指标"""
        with self._lock:
            return self._current_metrics.copy()
    
    def get_recent_history(self, duration_minutes: int = 5) -> List[Dict[str, Any]]:
        """获取最近历史数据"""
        cutoff_time = datetime.now() - timedelta(minutes=duration_minutes)
        
        with self._lock:
            return [
                entry for entry in self._metrics_history
                if entry["timestamp"] >= cutoff_time
            ]


class HistoricalMonitor:
    """历史性能监控器"""
    
    def __init__(self, config: MonitoringConfig):
        self.config = config
        self.logger = get_logger(f"{__name__}.HistoricalMonitor")
        
        # 历史数据存储
        self._historical_data: Dict[str, deque] = defaultdict(lambda: deque(maxlen=10000))
        self._lock = threading.RLock()
    
    def record_metrics(self, metrics: Dict[str, float], timestamp: Optional[datetime] = None) -> None:
        """记录历史指标"""
        timestamp = timestamp or datetime.now()
        
        with self._lock:
            for metric_name, value in metrics.items():
                self._historical_data[metric_name].append({
                    "timestamp": timestamp,
                    "value": value
                })
    
    def get_historical_data(self, metric_name: str, 
                          start_time: Optional[datetime] = None,
                          end_time: Optional[datetime] = None) -> List[Dict[str, Any]]:
        """获取历史数据"""
        end_time = end_time or datetime.now()
        start_time = start_time or (end_time - timedelta(hours=self.config.history_retention_hours))
        
        with self._lock:
            data = self._historical_data.get(metric_name, deque())
            
            return [
                entry for entry in data
                if start_time <= entry["timestamp"] <= end_time
            ]
    
    def get_metric_statistics(self, metric_name: str, duration_hours: int = 1) -> Dict[str, float]:
        """获取指标统计信息"""
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=duration_hours)
        
        historical_data = self.get_historical_data(metric_name, start_time, end_time)
        
        if not historical_data:
            return {}
        
        values = [entry["value"] for entry in historical_data]
        
        import statistics
        
        return {
            "count": len(values),
            "min": min(values),
            "max": max(values),
            "avg": statistics.mean(values),
            "median": statistics.median(values),
            "std_dev": statistics.stdev(values) if len(values) > 1 else 0
        }
    
    def cleanup_old_data(self) -> None:
        """清理过期数据"""
        cutoff_time = datetime.now() - timedelta(hours=self.config.history_retention_hours)
        
        with self._lock:
            for metric_name, data in self._historical_data.items():
                # 移除过期数据
                while data and data[0]["timestamp"] < cutoff_time:
                    data.popleft()


class AlertManager:
    """性能告警管理器"""
    
    def __init__(self, config: MonitoringConfig):
        self.config = config
        self.logger = get_logger(f"{__name__}.AlertManager")
        
        # 告警存储
        self._active_alerts: Dict[str, PerformanceAlert] = {}
        self._alert_history: deque = deque(maxlen=10000)
        self._alert_callbacks: List[Callable] = []
        self._lock = threading.RLock()
        
        # 告警检查
        self._checking = False
        self._check_task: Optional[asyncio.Task] = None
    
    async def start_alert_checking(self) -> None:
        """开始告警检查"""
        with self._lock:
            if self._checking:
                return
            
            self._checking = True
            self._check_task = asyncio.create_task(self._alert_check_loop())
            
            self.logger.info("Alert checking started")
    
    async def stop_alert_checking(self) -> None:
        """停止告警检查"""
        with self._lock:
            if not self._checking:
                return
            
            self._checking = False
            
            if self._check_task and not self._check_task.done():
                self._check_task.cancel()
                try:
                    await self._check_task
                except asyncio.CancelledError:
                    pass
            
            self.logger.info("Alert checking stopped")
    
    async def _alert_check_loop(self) -> None:
        """告警检查循环"""
        while self._checking:
            try:
                await self._check_alerts()
                await asyncio.sleep(self.config.alert_check_interval_seconds)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error in alert checking: {e}")
                await asyncio.sleep(self.config.alert_check_interval_seconds)
    
    async def _check_alerts(self) -> None:
        """检查告警条件"""
        try:
            # 获取当前指标
            metrics_collector = get_global_metrics_collector()
            snapshot = await metrics_collector.collect_snapshot()
            
            current_metrics = {
                **snapshot.system_metrics,
                **snapshot.application_metrics,
                **snapshot.business_metrics,
                **snapshot.custom_metrics
            }
            
            # 检查每个指标的告警条件
            for metric_name, value in current_metrics.items():
                await self._check_metric_alert(metric_name, value)
                
        except Exception as e:
            self.logger.error(f"Error checking alerts: {e}")
    
    async def _check_metric_alert(self, metric_name: str, value: float) -> None:
        """检查单个指标的告警条件"""
        try:
            thresholds = self.config.alert_thresholds.get(metric_name, {})
            
            for severity_name, threshold in thresholds.items():
                severity = self._get_severity_from_name(severity_name)
                
                # 检查是否触发告警
                if self._should_trigger_alert(metric_name, value, threshold, severity):
                    await self._trigger_alert(metric_name, value, threshold, severity)
                
                # 检查是否解决告警
                elif self._should_resolve_alert(metric_name, value, threshold, severity):
                    await self._resolve_alert(metric_name, severity)
                    
        except Exception as e:
            self.logger.error(f"Error checking alert for {metric_name}: {e}")
    
    def _should_trigger_alert(self, metric_name: str, value: float, threshold: float, severity: AlertSeverity) -> bool:
        """判断是否应该触发告警"""
        alert_key = f"{metric_name}_{severity.value}"
        
        # 如果告警已经存在，不重复触发
        if alert_key in self._active_alerts:
            return False
        
        # 根据指标类型判断告警条件
        if metric_name == "throughput_ops_per_sec":
            return value < threshold  # 吞吐量低于阈值
        else:
            return value > threshold  # 其他指标高于阈值
    
    def _should_resolve_alert(self, metric_name: str, value: float, threshold: float, severity: AlertSeverity) -> bool:
        """判断是否应该解决告警"""
        alert_key = f"{metric_name}_{severity.value}"
        
        # 如果告警不存在，无需解决
        if alert_key not in self._active_alerts:
            return False
        
        # 根据指标类型判断解决条件
        if metric_name == "throughput_ops_per_sec":
            return value >= threshold * 1.1  # 吞吐量恢复到阈值的110%
        else:
            return value <= threshold * 0.9  # 其他指标降到阈值的90%
    
    async def _trigger_alert(self, metric_name: str, value: float, threshold: float, severity: AlertSeverity) -> None:
        """触发告警"""
        try:
            import uuid
            
            alert_id = str(uuid.uuid4())
            alert_key = f"{metric_name}_{severity.value}"
            
            alert = PerformanceAlert(
                alert_id=alert_id,
                metric_name=metric_name,
                current_value=value,
                threshold_value=threshold,
                severity=severity,
                status=AlertStatus.ACTIVE,
                message=f"{metric_name} {severity.value} alert: {value:.2f} (threshold: {threshold:.2f})",
                timestamp=datetime.now()
            )
            
            with self._lock:
                self._active_alerts[alert_key] = alert
                self._alert_history.append(alert)
            
            # 触发告警回调
            await self._trigger_alert_callbacks(alert)
            
            self.logger.warning(f"Alert triggered: {alert.message}")
            
        except Exception as e:
            self.logger.error(f"Error triggering alert: {e}")
    
    async def _resolve_alert(self, metric_name: str, severity: AlertSeverity) -> None:
        """解决告警"""
        try:
            alert_key = f"{metric_name}_{severity.value}"
            
            with self._lock:
                alert = self._active_alerts.pop(alert_key, None)
                
                if alert:
                    alert.status = AlertStatus.RESOLVED
                    alert.resolved_at = datetime.now()
                    
                    # 更新历史记录
                    for historical_alert in self._alert_history:
                        if historical_alert.alert_id == alert.alert_id:
                            historical_alert.status = AlertStatus.RESOLVED
                            historical_alert.resolved_at = alert.resolved_at
                            break
            
            if alert:
                self.logger.info(f"Alert resolved: {alert.message}")
                
        except Exception as e:
            self.logger.error(f"Error resolving alert: {e}")
    
    async def _trigger_alert_callbacks(self, alert: PerformanceAlert) -> None:
        """触发告警回调"""
        for callback in self._alert_callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(alert)
                else:
                    callback(alert)
            except Exception as e:
                self.logger.error(f"Error in alert callback: {e}")
    
    def _get_severity_from_name(self, severity_name: str) -> AlertSeverity:
        """从名称获取严重程度"""
        severity_map = {
            "low": AlertSeverity.LOW,
            "medium": AlertSeverity.MEDIUM,
            "high": AlertSeverity.HIGH,
            "critical": AlertSeverity.CRITICAL
        }
        return severity_map.get(severity_name, AlertSeverity.MEDIUM)
    
    def add_alert_callback(self, callback: Callable) -> None:
        """添加告警回调"""
        self._alert_callbacks.append(callback)
    
    def get_active_alerts(self) -> List[PerformanceAlert]:
        """获取活跃告警"""
        with self._lock:
            return list(self._active_alerts.values())
    
    def get_alert_history(self, limit: int = 100) -> List[PerformanceAlert]:
        """获取告警历史"""
        with self._lock:
            return list(self._alert_history)[-limit:]
    
    def acknowledge_alert(self, alert_id: str, acknowledged_by: str) -> bool:
        """确认告警"""
        with self._lock:
            for alert in self._active_alerts.values():
                if alert.alert_id == alert_id:
                    alert.status = AlertStatus.ACKNOWLEDGED
                    alert.acknowledged_by = acknowledged_by
                    alert.acknowledged_at = datetime.now()
                    return True
        
        return False


class DashboardManager:
    """性能仪表板管理器"""
    
    def __init__(self, config: MonitoringConfig):
        self.config = config
        self.logger = get_logger(f"{__name__}.DashboardManager")
        
        # 仪表板数据
        self._dashboard_data: Dict[str, Any] = {}
        self._lock = threading.RLock()
    
    def update_dashboard_data(self, real_time_monitor: RealTimeMonitor,
                            historical_monitor: HistoricalMonitor,
                            alert_manager: AlertManager) -> None:
        """更新仪表板数据"""
        try:
            with self._lock:
                self._dashboard_data = {
                    "timestamp": datetime.now().isoformat(),
                    "real_time_metrics": real_time_monitor.get_current_metrics(),
                    "recent_history": real_time_monitor.get_recent_history(5),
                    "active_alerts": [alert.to_dict() for alert in alert_manager.get_active_alerts()],
                    "alert_summary": self._get_alert_summary(alert_manager),
                    "system_health": self._calculate_system_health(real_time_monitor.get_current_metrics())
                }
                
        except Exception as e:
            self.logger.error(f"Error updating dashboard data: {e}")
    
    def _get_alert_summary(self, alert_manager: AlertManager) -> Dict[str, int]:
        """获取告警摘要"""
        active_alerts = alert_manager.get_active_alerts()
        
        summary = {
            "total": len(active_alerts),
            "critical": 0,
            "high": 0,
            "medium": 0,
            "low": 0
        }
        
        for alert in active_alerts:
            summary[alert.severity.value] += 1
        
        return summary
    
    def _calculate_system_health(self, metrics: Dict[str, float]) -> str:
        """计算系统健康状态"""
        try:
            # 简化的健康评分算法
            score = 100
            
            # CPU使用率影响
            cpu_usage = metrics.get("system_cpu_percent", 0)
            if cpu_usage > 80:
                score -= 30
            elif cpu_usage > 60:
                score -= 15
            
            # 内存使用率影响
            memory_usage = metrics.get("system_memory_percent", 0)
            if memory_usage > 90:
                score -= 25
            elif memory_usage > 75:
                score -= 10
            
            # 错误率影响
            error_rate = metrics.get("error_rate", 0)
            if error_rate > 0.1:
                score -= 40
            elif error_rate > 0.05:
                score -= 20
            
            # 确定健康状态
            if score >= 80:
                return "healthy"
            elif score >= 60:
                return "warning"
            elif score >= 40:
                return "degraded"
            else:
                return "critical"
                
        except Exception:
            return "unknown"
    
    def get_dashboard_data(self) -> Dict[str, Any]:
        """获取仪表板数据"""
        with self._lock:
            return self._dashboard_data.copy()
    
    def export_dashboard_data(self, format: str = "json") -> str:
        """导出仪表板数据"""
        data = self.get_dashboard_data()
        
        if format == "json":
            return json.dumps(data, indent=2, ensure_ascii=False)
        else:
            raise ValueError(f"Unsupported export format: {format}")


class PerformanceMonitor:
    """性能监控器主管理器"""
    
    def __init__(self, config: Optional[MonitoringConfig] = None):
        self.config = config or MonitoringConfig()
        self.logger = get_logger(f"{__name__}.PerformanceMonitor")
        
        # 初始化组件
        self.real_time_monitor = RealTimeMonitor(self.config)
        self.historical_monitor = HistoricalMonitor(self.config)
        self.alert_manager = AlertManager(self.config)
        self.dashboard_manager = DashboardManager(self.config)
        
        # 监控状态
        self._monitoring = False
        self._dashboard_task: Optional[asyncio.Task] = None
        self._lock = threading.RLock()
    
    async def start_monitoring(self) -> None:
        """开始性能监控"""
        with self._lock:
            if self._monitoring:
                return
            
            if not self.config.validate():
                raise ValueError("Invalid monitoring configuration")
            
            self._monitoring = True
        
        try:
            # 启动各个组件
            await self.real_time_monitor.start_monitoring()
            await self.alert_manager.start_alert_checking()
            
            # 启动仪表板更新任务
            self._dashboard_task = asyncio.create_task(self._dashboard_update_loop())
            
            # 设置指标记录回调
            self.real_time_monitor.add_metric_callback("*", self._record_historical_metrics)
            
            self.logger.info("Performance monitoring started")
            
        except Exception as e:
            self.logger.error(f"Failed to start performance monitoring: {e}")
            await self.stop_monitoring()
            raise
    
    async def stop_monitoring(self) -> None:
        """停止性能监控"""
        with self._lock:
            if not self._monitoring:
                return
            
            self._monitoring = False
        
        try:
            # 停止仪表板更新
            if self._dashboard_task and not self._dashboard_task.done():
                self._dashboard_task.cancel()
                try:
                    await self._dashboard_task
                except asyncio.CancelledError:
                    pass
            
            # 停止各个组件
            await self.real_time_monitor.stop_monitoring()
            await self.alert_manager.stop_alert_checking()
            
            self.logger.info("Performance monitoring stopped")
            
        except Exception as e:
            self.logger.error(f"Error stopping performance monitoring: {e}")
    
    async def _dashboard_update_loop(self) -> None:
        """仪表板更新循环"""
        while self._monitoring:
            try:
                self.dashboard_manager.update_dashboard_data(
                    self.real_time_monitor,
                    self.historical_monitor,
                    self.alert_manager
                )
                
                await asyncio.sleep(self.config.dashboard_refresh_interval_seconds)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error updating dashboard: {e}")
                await asyncio.sleep(self.config.dashboard_refresh_interval_seconds)
    
    def _record_historical_metrics(self, metric_name: str, value: float) -> None:
        """记录历史指标"""
        try:
            current_metrics = self.real_time_monitor.get_current_metrics()
            self.historical_monitor.record_metrics(current_metrics)
        except Exception as e:
            self.logger.error(f"Error recording historical metrics: {e}")
    
    def get_monitoring_status(self) -> Dict[str, Any]:
        """获取监控状态"""
        with self._lock:
            return {
                "monitoring": self._monitoring,
                "config": {
                    "enabled": self.config.enabled,
                    "sampling_interval_seconds": self.config.sampling_interval_seconds,
                    "alert_check_interval_seconds": self.config.alert_check_interval_seconds,
                    "history_retention_hours": self.config.history_retention_hours
                },
                "components": {
                    "real_time_monitor": "active" if self._monitoring else "inactive",
                    "historical_monitor": "active",
                    "alert_manager": "active" if self._monitoring else "inactive",
                    "dashboard_manager": "active"
                }
            }


# 全局性能监控器实例
_global_performance_monitor: Optional[PerformanceMonitor] = None


def get_global_performance_monitor() -> PerformanceMonitor:
    """获取全局性能监控器实例"""
    global _global_performance_monitor
    
    if _global_performance_monitor is None:
        _global_performance_monitor = PerformanceMonitor()
    
    return _global_performance_monitor


# 模块级别的日志器
module_logger = get_logger(__name__)