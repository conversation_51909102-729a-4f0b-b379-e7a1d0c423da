package com.crypto.trading.bootstrap.controller;

import com.crypto.trading.common.dto.ApiResponse;
import com.crypto.trading.common.dto.market.KlineDataDTO;
import com.crypto.trading.common.enums.KlineInterval;
import com.crypto.trading.market.service.HistoricalDataService;
import com.crypto.trading.market.service.impl.DownloadTaskTracker;
import com.crypto.trading.bootstrap.config.ApiRateLimiter;
import com.crypto.trading.bootstrap.dto.Response;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.server.ResponseStatusException;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

/**
 * 历史数据控制器
 * 提供历史K线数据获取的REST API
 */
@RestController
@RequestMapping("/v1/historical-data")
public class HistoricalDataController {

    private static final Logger log = LoggerFactory.getLogger(HistoricalDataController.class);

    @Autowired
    private HistoricalDataService historicalDataService;

    @Autowired
    private ApiRateLimiter apiRateLimiter;

    /**
     * 获取历史K线数据
     *
     * @param symbol       交易对
     * @param interval     K线间隔
     * @param startTime    开始时间
     * @param endTime      结束时间
     * @param storeToDb    是否存储到数据库
     * @param sendToKafka  是否发送到Kafka
     * @return API响应
     */
    @GetMapping("/klines")
    public ResponseEntity<Response> getHistoricalKlines(
            @RequestParam String symbol,
            @RequestParam String interval,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
        
        // API速率限制检查
        if (!apiRateLimiter.allowRequest("klines")) {
            return ResponseEntity.status(HttpStatus.TOO_MANY_REQUESTS)
                    .body(new Response(false, "Too many requests, please try again later", null));
        }
        
        try {
            log.info("收到获取历史K线数据请求: symbol={}, interval={}, startTime={}, endTime={}",
                    symbol, interval, startTime, endTime);
            
            KlineInterval klineInterval = KlineInterval.fromCode(interval);
            if (klineInterval == null) {
                return ResponseEntity.badRequest()
                        .body(new Response(false, "无效的K线间隔: " + interval, null));
            }
            
            var data = historicalDataService.getHistoricalKlines(symbol, klineInterval, startTime, endTime, 
                true, true);
            return ResponseEntity.ok(new Response(true, "成功获取 " + (data != null ? data.size() : 0) + " 条K线数据", data));
        } catch (Exception e) {
            log.error("获取历史K线数据异常: symbol={}, interval={}, error={}", 
                    symbol, interval, e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new Response(false, "获取历史K线数据失败: " + e.getMessage(), null));
        }
    }





    /**
     * 批量下载K线数据
     *
     * @param symbols      交易对列表，多个交易对用逗号分隔
     * @param interval     K线间隔
     * @param startTime    开始时间
     * @param endTime      结束时间
     * @param storeToDb    是否存储到数据库
     * @param sendToKafka  是否发送到Kafka
     * @return API响应
     */
    @GetMapping("/batch/klines")
    public ApiResponse<String> batchDownloadKlines(
            @RequestParam String symbols,
            @RequestParam String interval,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime,
            @RequestParam(defaultValue = "true") boolean storeToDb,
            @RequestParam(defaultValue = "true") boolean sendToKafka) {
        
        try {
            log.info("收到批量下载K线数据请求: symbols={}, interval={}, startTime={}, endTime={}, storeToDb={}, sendToKafka={}",
                    symbols, interval, startTime, endTime, storeToDb, sendToKafka);
            
            KlineInterval klineInterval = KlineInterval.fromCode(interval);
            if (klineInterval == null) {
                return ApiResponse.error("无效的K线间隔: " + interval);
            }
            
            List<String> symbolList = Arrays.asList(symbols.split(","));
            
            String taskId = historicalDataService.batchDownloadKlines(
                    symbolList, klineInterval, startTime, endTime, storeToDb, sendToKafka);
            
            return new ApiResponse<>(0, "批量下载K线数据任务已启动，共 " + symbolList.size() + 
                    " 个交易对，任务ID: " + taskId, taskId);
        } catch (Exception e) {
            log.error("批量下载K线数据异常: symbols={}, interval={}, error={}", 
                    symbols, interval, e.getMessage(), e);
            return ApiResponse.error("批量下载K线数据失败: " + e.getMessage());
        }
    }


    


    /**
     * 检查是否存在历史数据
     *
     * @param symbol       交易对
     * @param dataType     数据类型（kline）
     * @param interval     K线间隔
     * @param startTime    开始时间
     * @param endTime      结束时间
     * @return API响应
     */
    @GetMapping("/check")
    public ApiResponse<Boolean> hasHistoricalData(
            @RequestParam String symbol,
            @RequestParam String dataType,
            @RequestParam(required = false) String interval,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
        
        try {
            log.info("收到检查历史数据请求: symbol={}, dataType={}, interval={}, startTime={}, endTime={}",
                    symbol, dataType, interval, startTime, endTime);
            
            boolean hasData = historicalDataService.hasHistoricalData(
                    symbol, dataType, interval, startTime, endTime);
            
            return new ApiResponse<>(0, hasData ? "存在历史数据" : "不存在历史数据", hasData);
        } catch (Exception e) {
            log.error("检查历史数据异常: symbol={}, dataType={}, interval={}, error={}", 
                    symbol, dataType, interval, e.getMessage(), e);
            return ApiResponse.error("检查历史数据失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取下载任务的状态
     *
     * @param taskId 任务ID
     * @return API响应
     */
    @GetMapping("/task/{taskId}")
    public ApiResponse<String> getTaskStatus(@PathVariable String taskId) {
        try {
            log.info("收到获取任务状态请求: taskId={}", taskId);
            
            String status = historicalDataService.getTaskStatus(taskId);
            
            return new ApiResponse<>(0, "获取任务状态成功", status);
        } catch (Exception e) {
            log.error("获取任务状态异常: taskId={}, error={}", taskId, e.getMessage(), e);
            return ApiResponse.error("获取任务状态失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取所有任务的状态
     *
     * @return API响应
     */
    @GetMapping("/tasks")
    public ApiResponse<List<String>> getAllTaskStatus() {
        try {
            log.info("收到获取所有任务状态请求");
            
            List<String> statuses = historicalDataService.getAllTaskStatus();
            
            return new ApiResponse<>(0, "获取所有任务状态成功，共 " + statuses.size() + " 个任务", statuses);
        } catch (Exception e) {
            log.error("获取所有任务状态异常: error={}", e.getMessage(), e);
            return ApiResponse.error("获取所有任务状态失败: " + e.getMessage());
        }
    }

    /**
     * 批量下载K线数据（增强版）
     *
     * @param symbols            交易对列表，多个交易对用逗号分隔
     * @param interval           K线间隔
     * @param startTime          开始时间
     * @param endTime            结束时间
     * @param storeToDb          是否存储到数据库
     * @param sendToKafka        是否发送到Kafka
     * @param incrementalDownload 是否增量下载
     * @param priority           任务优先级
     * @param maxRetries         最大重试次数
     * @param maxConcurrent      最大并发请求数
     * @param chunkSizeHours     分片大小（小时）
     * @return API响应
     */
    @GetMapping("/batch/klines/enhanced")
    public ApiResponse<String> batchDownloadKlinesEnhanced(
            @RequestParam String symbols,
            @RequestParam String interval,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime,
            @RequestParam(defaultValue = "true") boolean storeToDb,
            @RequestParam(defaultValue = "true") boolean sendToKafka,
            @RequestParam(defaultValue = "true") boolean incrementalDownload,
            @RequestParam(defaultValue = "NORMAL") String priority,
            @RequestParam(defaultValue = "3") int maxRetries,
            @RequestParam(defaultValue = "5") int maxConcurrent,
            @RequestParam(defaultValue = "24") int chunkSizeHours) {
        
        try {
            log.info("收到增强版批量下载K线数据请求: symbols={}, interval={}, startTime={}, endTime={}, " + 
                    "storeToDb={}, sendToKafka={}, incrementalDownload={}, priority={}, " +
                    "maxRetries={}, maxConcurrent={}, chunkSizeHours={}",
                    symbols, interval, startTime, endTime, storeToDb, sendToKafka,
                    incrementalDownload, priority, maxRetries, maxConcurrent, chunkSizeHours);
            
            KlineInterval klineInterval = KlineInterval.fromCode(interval);
            if (klineInterval == null) {
                return ApiResponse.error("无效的K线间隔: " + interval);
            }
            
            List<String> symbolList = Arrays.asList(symbols.split(","));
            
            DownloadTaskTracker.TaskPriority taskPriority;
            try {
                taskPriority = DownloadTaskTracker.TaskPriority.valueOf(priority.toUpperCase());
            } catch (IllegalArgumentException e) {
                log.warn("无效的任务优先级: {}, 使用默认值NORMAL", priority);
                taskPriority = DownloadTaskTracker.TaskPriority.NORMAL;
            }
            
            String taskId = historicalDataService.batchDownloadKlinesEnhanced(
                    symbolList, klineInterval, startTime, endTime, 
                    storeToDb, sendToKafka, incrementalDownload, 
                    taskPriority, maxRetries, maxConcurrent, chunkSizeHours);
            
            return new ApiResponse<>(0, "增强版批量下载K线数据任务已启动，共 " + symbolList.size() + 
                    " 个交易对，使用 " + chunkSizeHours + " 小时分片，任务ID: " + taskId, taskId);
        } catch (Exception e) {
            log.error("增强版批量下载K线数据异常: symbols={}, interval={}, error={}", 
                    symbols, interval, e.getMessage(), e);
            return ApiResponse.error("增强版批量下载K线数据失败: " + e.getMessage());
        }
    }
    
    /**
     * 暂停下载任务
     *
     * @param taskId 任务ID
     * @return API响应
     */
    @PostMapping("/task/{taskId}/pause")
    public ApiResponse<Boolean> pauseTask(@PathVariable String taskId) {
        try {
            log.info("收到暂停任务请求: taskId={}", taskId);
            boolean success = historicalDataService.pauseTask(taskId);
            
            if (success) {
                return new ApiResponse<>(0, "任务已暂停: " + taskId, true);
            } else {
                return ApiResponse.error("无法暂停任务: " + taskId);
            }
        } catch (Exception e) {
            log.error("暂停任务异常: taskId={}, error={}", taskId, e.getMessage(), e);
            return ApiResponse.error("暂停任务失败: " + e.getMessage());
        }
    }
    
    /**
     * 恢复下载任务
     *
     * @param taskId 任务ID
     * @return API响应
     */
    @PostMapping("/task/{taskId}/resume")
    public ApiResponse<Boolean> resumeTask(@PathVariable String taskId) {
        try {
            log.info("收到恢复任务请求: taskId={}", taskId);
            boolean success = historicalDataService.resumeTask(taskId);
            
            if (success) {
                return new ApiResponse<>(0, "任务已恢复: " + taskId, true);
            } else {
                return ApiResponse.error("无法恢复任务: " + taskId);
            }
        } catch (Exception e) {
            log.error("恢复任务异常: taskId={}, error={}", taskId, e.getMessage(), e);
            return ApiResponse.error("恢复任务失败: " + e.getMessage());
        }
    }
    
    /**
     * 取消下载任务
     *
     * @param taskId 任务ID
     * @return API响应
     */
    @PostMapping("/task/{taskId}/cancel")
    public ApiResponse<Boolean> cancelTask(@PathVariable String taskId) {
        try {
            log.info("收到取消任务请求: taskId={}", taskId);
            boolean success = historicalDataService.cancelTask(taskId);
            
            if (success) {
                return new ApiResponse<>(0, "任务已取消: " + taskId, true);
            } else {
                return ApiResponse.error("无法取消任务: " + taskId);
            }
        } catch (Exception e) {
            log.error("取消任务异常: taskId={}, error={}", taskId, e.getMessage(), e);
            return ApiResponse.error("取消任务失败: " + e.getMessage());
        }
    }


} 