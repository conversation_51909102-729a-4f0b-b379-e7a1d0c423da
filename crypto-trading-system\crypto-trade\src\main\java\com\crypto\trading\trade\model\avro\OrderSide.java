/**
 * Autogenerated by Avro
 *
 * DO NOT EDIT DIRECTLY
 */
package com.crypto.trading.trade.model.avro;
@org.apache.avro.specific.AvroGenerated
public enum OrderSide implements org.apache.avro.generic.GenericEnumSymbol<OrderSide> {
  BUY, SELL  ;
  public static final org.apache.avro.Schema SCHEMA$ = new org.apache.avro.Schema.Parser().parse("{\"type\":\"enum\",\"name\":\"OrderSide\",\"namespace\":\"com.crypto.trading.trade.model.avro\",\"symbols\":[\"BUY\",\"SELL\"]}");
  public static org.apache.avro.Schema getClassSchema() { return SCHEMA$; }

  @Override
  public org.apache.avro.Schema getSchema() { return SCHEMA$; }
}
