package com.crypto.trading.common.exception;

/**
 * 错误码枚举
 * <p>
 * 定义系统中的错误码，用于标识不同类型的异常
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public enum ErrorCode {

    /**
     * 成功
     */
    SUCCESS(200, "操作成功"),

    /**
     * 系统错误
     */
    SYSTEM_ERROR(500, "系统错误"),

    /**
     * 参数错误
     */
    PARAM_ERROR(400, "参数错误"),

    /**
     * 未授权
     */
    UNAUTHORIZED(401, "未授权"),

    /**
     * 禁止访问
     */
    FORBIDDEN(403, "禁止访问"),

    /**
     * 资源不存在
     */
    NOT_FOUND(404, "资源不存在"),

    /**
     * 请求方法不支持
     */
    METHOD_NOT_ALLOWED(405, "请求方法不支持"),

    /**
     * 请求超时
     */
    REQUEST_TIMEOUT(408, "请求超时"),

    /**
     * 业务异常
     */
    BUSINESS_ERROR(1000, "业务异常"),

    /**
     * 数据库操作异常
     */
    DB_ERROR(1001, "数据库操作异常"),

    /**
     * Redis操作异常
     */
    REDIS_ERROR(1002, "Redis操作异常"),

    /**
     * Kafka操作异常
     */
    KAFKA_ERROR(1003, "Kafka操作异常"),

    /**
     * 币安API调用异常
     */
    BINANCE_API_ERROR(2000, "币安API调用异常"),

    /**
     * 币安API密钥错误
     */
    BINANCE_API_KEY_ERROR(2001, "币安API密钥错误"),

    /**
     * 币安API权重限制
     */
    BINANCE_API_WEIGHT_LIMIT(2002, "币安API权重限制"),

    /**
     * 币安API订单数量限制
     */
    BINANCE_API_ORDER_LIMIT(2003, "币安API订单数量限制"),

    /**
     * 币安API签名错误
     */
    BINANCE_API_SIGNATURE_ERROR(2004, "币安API签名错误"),

    /**
     * 币安API服务器错误
     */
    BINANCE_API_SERVER_ERROR(2005, "币安API服务器错误"),
    
    /**
     * API调用异常
     */
    API_ERROR(2010, "API调用异常"),

    /**
     * 币安WebSocket连接异常
     */
    BINANCE_WEBSOCKET_ERROR(2100, "币安WebSocket连接异常"),

    /**
     * 币安WebSocket订阅失败
     */
    BINANCE_WEBSOCKET_SUBSCRIBE_ERROR(2101, "币安WebSocket订阅失败"),

    /**
     * 币安WebSocket断线
     */
    BINANCE_WEBSOCKET_DISCONNECT(2102, "币安WebSocket断线"),

    /**
     * 交易所资金不足
     */
    INSUFFICIENT_BALANCE(3000, "交易所资金不足"),

    /**
     * 交易对不存在
     */
    SYMBOL_NOT_FOUND(3001, "交易对不存在"),

    /**
     * 下单失败
     */
    ORDER_FAILED(3100, "下单失败"),

    /**
     * 撤单失败
     */
    CANCEL_ORDER_FAILED(3101, "撤单失败"),

    /**
     * 订单查询失败
     */
    QUERY_ORDER_FAILED(3102, "订单查询失败"),

    /**
     * 仓位查询失败
     */
    QUERY_POSITION_FAILED(3103, "仓位查询失败"),

    /**
     * 账户查询失败
     */
    QUERY_ACCOUNT_FAILED(3104, "账户查询失败"),

    /**
     * 杠杆设置失败
     */
    SET_LEVERAGE_FAILED(3105, "杠杆设置失败"),

    /**
     * 策略不存在
     */
    STRATEGY_NOT_FOUND(4000, "策略不存在"),

    /**
     * 策略启动失败
     */
    STRATEGY_START_FAILED(4001, "策略启动失败"),

    /**
     * 策略停止失败
     */
    STRATEGY_STOP_FAILED(4002, "策略停止失败"),

    /**
     * 策略参数错误
     */
    STRATEGY_PARAM_ERROR(4003, "策略参数错误");

    /**
     * 错误码
     */
    private final int code;

    /**
     * 错误信息
     */
    private final String message;

    /**
     * 构造函数
     *
     * @param code    错误码
     * @param message 错误信息
     */
    ErrorCode(int code, String message) {
        this.code = code;
        this.message = message;
    }

    /**
     * 获取错误码
     *
     * @return 错误码
     */
    public int getCode() {
        return code;
    }

    /**
     * 获取错误信息
     *
     * @return 错误信息
     */
    public String getMessage() {
        return message;
    }
}