#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
智能缓存服务模块

提供多层级缓存、智能预取、缓存预热和性能优化功能。
"""

import time
import threading
import pickle
import hashlib
import asyncio
from typing import Dict, Any, Optional, Callable, Union, List, Tuple
from dataclasses import dataclass, field
from enum import Enum
from collections import OrderedDict
from loguru import logger


class CacheStrategy(Enum):
    """缓存策略枚举"""
    LRU = "lru"  # 最近最少使用
    LFU = "lfu"  # 最少使用频率
    FIFO = "fifo"  # 先进先出
    TTL = "ttl"  # 基于时间
    ADAPTIVE = "adaptive"  # 自适应


class CacheLevel(Enum):
    """缓存级别枚举"""
    L1_MEMORY = "l1_memory"  # L1内存缓存
    L2_COMPRESSED = "l2_compressed"  # L2压缩缓存
    L3_DISK = "l3_disk"  # L3磁盘缓存


@dataclass
class CacheEntry:
    """缓存条目"""
    key: str
    value: Any
    created_time: float
    last_access_time: float
    access_count: int = 0
    ttl: Optional[float] = None
    size_bytes: int = 0
    level: CacheLevel = CacheLevel.L1_MEMORY
    compressed: bool = False
    
    def is_expired(self) -> bool:
        """检查是否过期"""
        if self.ttl is None:
            return False
        return time.time() - self.created_time > self.ttl
    
    def update_access(self) -> None:
        """更新访问信息"""
        self.last_access_time = time.time()
        self.access_count += 1


@dataclass
class CacheConfig:
    """缓存配置"""
    max_size: int = 1000  # 最大条目数
    max_memory_mb: int = 100  # 最大内存使用(MB)
    default_ttl: Optional[float] = None  # 默认TTL(秒)
    strategy: CacheStrategy = CacheStrategy.LRU
    enable_compression: bool = True
    enable_disk_cache: bool = False
    disk_cache_path: Optional[str] = None
    enable_prefetch: bool = True
    prefetch_threshold: float = 0.8  # 预取阈值


class LRUCache:
    """LRU缓存实现"""
    
    def __init__(self, max_size: int):
        self.max_size = max_size
        self.cache: OrderedDict[str, CacheEntry] = OrderedDict()
        self._lock = threading.RLock()
    
    def get(self, key: str) -> Optional[CacheEntry]:
        """获取缓存条目"""
        with self._lock:
            if key in self.cache:
                entry = self.cache[key]
                if entry.is_expired():
                    del self.cache[key]
                    return None
                
                # 移动到末尾（最近使用）
                self.cache.move_to_end(key)
                entry.update_access()
                return entry
            return None
    
    def put(self, key: str, entry: CacheEntry) -> None:
        """放入缓存条目"""
        with self._lock:
            if key in self.cache:
                self.cache.move_to_end(key)
            else:
                if len(self.cache) >= self.max_size:
                    # 移除最久未使用的条目
                    self.cache.popitem(last=False)
            
            self.cache[key] = entry
    
    def remove(self, key: str) -> bool:
        """移除缓存条目"""
        with self._lock:
            if key in self.cache:
                del self.cache[key]
                return True
            return False
    
    def clear(self) -> None:
        """清空缓存"""
        with self._lock:
            self.cache.clear()
    
    def size(self) -> int:
        """获取缓存大小"""
        return len(self.cache)
    
    def keys(self) -> List[str]:
        """获取所有键"""
        with self._lock:
            return list(self.cache.keys())


class IntelligentCacheService:
    """
    智能缓存服务
    
    提供多层级缓存、智能预取、缓存预热和性能优化功能。
    """
    
    def __init__(self, config: CacheConfig):
        self.config = config
        
        # 多层级缓存
        self.l1_cache = LRUCache(config.max_size)
        self.l2_cache = LRUCache(config.max_size // 2) if config.enable_compression else None
        self.l3_cache = LRUCache(config.max_size // 4) if config.enable_disk_cache else None
        
        # 缓存统计
        self.stats = {
            "hits": 0,
            "misses": 0,
            "evictions": 0,
            "prefetch_hits": 0,
            "memory_usage_bytes": 0
        }
        
        # 预取相关
        self.prefetch_patterns: Dict[str, List[str]] = {}
        self.access_history: List[Tuple[str, float]] = []
        
        # 线程锁
        self._lock = threading.RLock()
        
        logger.info(f"智能缓存服务初始化完成 - 策略: {config.strategy.value}, "
                   f"最大大小: {config.max_size}, 最大内存: {config.max_memory_mb}MB")
    
    def get(self, key: str) -> Optional[Any]:
        """
        获取缓存值
        
        Args:
            key: 缓存键
            
        Returns:
            缓存值，如果不存在返回None
        """
        with self._lock:
            # 记录访问历史
            self.access_history.append((key, time.time()))
            if len(self.access_history) > 1000:
                self.access_history = self.access_history[-500:]
            
            # L1缓存查找
            entry = self.l1_cache.get(key)
            if entry:
                self.stats["hits"] += 1
                self._trigger_prefetch(key)
                return entry.value
            
            # L2缓存查找
            if self.l2_cache:
                entry = self.l2_cache.get(key)
                if entry:
                    self.stats["hits"] += 1
                    # 提升到L1缓存
                    self._promote_to_l1(key, entry)
                    self._trigger_prefetch(key)
                    return self._decompress_value(entry.value) if entry.compressed else entry.value
            
            # L3缓存查找
            if self.l3_cache:
                entry = self.l3_cache.get(key)
                if entry:
                    self.stats["hits"] += 1
                    # 提升到L1缓存
                    self._promote_to_l1(key, entry)
                    self._trigger_prefetch(key)
                    return self._load_from_disk(entry.value) if entry.level == CacheLevel.L3_DISK else entry.value
            
            self.stats["misses"] += 1
            return None
    
    def put(self, key: str, value: Any, ttl: Optional[float] = None) -> None:
        """
        放入缓存值
        
        Args:
            key: 缓存键
            value: 缓存值
            ttl: 生存时间（秒）
        """
        with self._lock:
            # 计算值的大小
            size_bytes = self._calculate_size(value)
            
            # 检查内存使用
            if self._check_memory_usage(size_bytes):
                self._evict_entries()
            
            # 创建缓存条目
            entry = CacheEntry(
                key=key,
                value=value,
                created_time=time.time(),
                last_access_time=time.time(),
                ttl=ttl or self.config.default_ttl,
                size_bytes=size_bytes,
                level=CacheLevel.L1_MEMORY
            )
            
            # 放入L1缓存
            self.l1_cache.put(key, entry)
            self.stats["memory_usage_bytes"] += size_bytes
            
            # 更新预取模式
            self._update_prefetch_patterns(key)
            
            logger.debug(f"缓存条目已添加: {key}, 大小: {size_bytes} bytes")
    
    def remove(self, key: str) -> bool:
        """
        移除缓存条目
        
        Args:
            key: 缓存键
            
        Returns:
            是否成功移除
        """
        with self._lock:
            removed = False
            
            # 从所有层级移除
            if self.l1_cache.remove(key):
                removed = True
            
            if self.l2_cache and self.l2_cache.remove(key):
                removed = True
            
            if self.l3_cache and self.l3_cache.remove(key):
                removed = True
            
            if removed:
                logger.debug(f"缓存条目已移除: {key}")
            
            return removed
    
    def clear(self) -> None:
        """清空所有缓存"""
        with self._lock:
            self.l1_cache.clear()
            if self.l2_cache:
                self.l2_cache.clear()
            if self.l3_cache:
                self.l3_cache.clear()
            
            self.stats["memory_usage_bytes"] = 0
            logger.info("所有缓存已清空")
    
    def _promote_to_l1(self, key: str, entry: CacheEntry) -> None:
        """提升条目到L1缓存"""
        # 解压缩或从磁盘加载
        if entry.compressed:
            value = self._decompress_value(entry.value)
        elif entry.level == CacheLevel.L3_DISK:
            value = self._load_from_disk(entry.value)
        else:
            value = entry.value
        
        # 创建新的L1条目
        l1_entry = CacheEntry(
            key=key,
            value=value,
            created_time=entry.created_time,
            last_access_time=time.time(),
            access_count=entry.access_count + 1,
            ttl=entry.ttl,
            size_bytes=self._calculate_size(value),
            level=CacheLevel.L1_MEMORY
        )
        
        self.l1_cache.put(key, l1_entry)
    
    def _trigger_prefetch(self, key: str) -> None:
        """触发预取"""
        if not self.config.enable_prefetch:
            return
        
        # 基于访问模式预取相关数据
        if key in self.prefetch_patterns:
            for related_key in self.prefetch_patterns[key]:
                if self.get(related_key) is None:
                    # 这里可以触发异步预取
                    logger.debug(f"触发预取: {related_key}")
    
    def _update_prefetch_patterns(self, key: str) -> None:
        """更新预取模式"""
        # 分析最近的访问历史，找出访问模式
        recent_accesses = [k for k, t in self.access_history[-10:]]
        
        # 简单的序列模式检测
        if len(recent_accesses) >= 3:
            for i in range(len(recent_accesses) - 2):
                pattern_key = recent_accesses[i]
                next_keys = recent_accesses[i+1:i+3]
                
                if pattern_key not in self.prefetch_patterns:
                    self.prefetch_patterns[pattern_key] = []
                
                for next_key in next_keys:
                    if next_key not in self.prefetch_patterns[pattern_key]:
                        self.prefetch_patterns[pattern_key].append(next_key)
    
    def _check_memory_usage(self, additional_size: int) -> bool:
        """检查内存使用是否超限"""
        max_memory_bytes = self.config.max_memory_mb * 1024 * 1024
        current_usage = self.stats["memory_usage_bytes"]
        
        return (current_usage + additional_size) > max_memory_bytes
    
    def _evict_entries(self) -> None:
        """驱逐缓存条目"""
        # 根据策略驱逐条目
        if self.config.strategy == CacheStrategy.LRU:
            self._evict_lru()
        elif self.config.strategy == CacheStrategy.LFU:
            self._evict_lfu()
        elif self.config.strategy == CacheStrategy.TTL:
            self._evict_expired()
        else:
            self._evict_lru()  # 默认使用LRU
    
    def _evict_lru(self) -> None:
        """LRU驱逐"""
        # 移动一些L1条目到L2
        if self.l2_cache and self.l1_cache.size() > 0:
            keys_to_move = list(self.l1_cache.keys())[:self.l1_cache.size() // 4]
            
            for key in keys_to_move:
                entry = self.l1_cache.get(key)
                if entry:
                    # 压缩并移动到L2
                    compressed_value = self._compress_value(entry.value)
                    l2_entry = CacheEntry(
                        key=key,
                        value=compressed_value,
                        created_time=entry.created_time,
                        last_access_time=entry.last_access_time,
                        access_count=entry.access_count,
                        ttl=entry.ttl,
                        size_bytes=len(compressed_value),
                        level=CacheLevel.L2_COMPRESSED,
                        compressed=True
                    )
                    
                    self.l2_cache.put(key, l2_entry)
                    self.l1_cache.remove(key)
                    self.stats["evictions"] += 1
    
    def _evict_lfu(self) -> None:
        """LFU驱逐"""
        # 找出访问频率最低的条目
        min_access_count = float('inf')
        lfu_key = None
        
        for key in self.l1_cache.keys():
            entry = self.l1_cache.get(key)
            if entry and entry.access_count < min_access_count:
                min_access_count = entry.access_count
                lfu_key = key
        
        if lfu_key:
            self.l1_cache.remove(lfu_key)
            self.stats["evictions"] += 1
    
    def _evict_expired(self) -> None:
        """驱逐过期条目"""
        expired_keys = []
        
        for key in self.l1_cache.keys():
            entry = self.l1_cache.get(key)
            if entry and entry.is_expired():
                expired_keys.append(key)
        
        for key in expired_keys:
            self.l1_cache.remove(key)
            self.stats["evictions"] += 1
    
    def _compress_value(self, value: Any) -> bytes:
        """压缩值"""
        try:
            import zlib
            serialized = pickle.dumps(value)
            compressed = zlib.compress(serialized)
            return compressed
        except Exception as e:
            logger.warning(f"值压缩失败: {e}")
            return pickle.dumps(value)
    
    def _decompress_value(self, compressed_value: bytes) -> Any:
        """解压缩值"""
        try:
            import zlib
            decompressed = zlib.decompress(compressed_value)
            return pickle.loads(decompressed)
        except Exception as e:
            logger.warning(f"值解压缩失败: {e}")
            return pickle.loads(compressed_value)
    
    def _save_to_disk(self, value: Any) -> str:
        """保存到磁盘"""
        # 这里可以实现磁盘缓存逻辑
        # 返回文件路径或标识符
        return f"disk_cache_{hash(str(value))}"
    
    def _load_from_disk(self, disk_id: str) -> Any:
        """从磁盘加载"""
        # 这里可以实现从磁盘加载的逻辑
        return None
    
    def _calculate_size(self, value: Any) -> int:
        """计算值的大小"""
        try:
            return len(pickle.dumps(value))
        except Exception:
            return 1024  # 默认大小
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        total_requests = self.stats["hits"] + self.stats["misses"]
        hit_rate = self.stats["hits"] / total_requests if total_requests > 0 else 0
        
        return {
            **self.stats,
            "hit_rate": hit_rate,
            "total_requests": total_requests,
            "l1_size": self.l1_cache.size(),
            "l2_size": self.l2_cache.size() if self.l2_cache else 0,
            "l3_size": self.l3_cache.size() if self.l3_cache else 0,
            "memory_usage_mb": self.stats["memory_usage_bytes"] / 1024 / 1024,
            "prefetch_patterns_count": len(self.prefetch_patterns)
        }
    
    def optimize_cache(self) -> Dict[str, Any]:
        """优化缓存"""
        logger.info("开始缓存优化...")
        
        # 清理过期条目
        expired_count = 0
        for cache in [self.l1_cache, self.l2_cache, self.l3_cache]:
            if cache:
                for key in list(cache.keys()):
                    entry = cache.get(key)
                    if entry and entry.is_expired():
                        cache.remove(key)
                        expired_count += 1
        
        # 压缩低频访问的条目
        compressed_count = 0
        if self.l2_cache:
            low_access_keys = []
            for key in self.l1_cache.keys():
                entry = self.l1_cache.get(key)
                if entry and entry.access_count < 2:
                    low_access_keys.append(key)
            
            for key in low_access_keys[:10]:  # 限制数量
                entry = self.l1_cache.get(key)
                if entry:
                    compressed_value = self._compress_value(entry.value)
                    l2_entry = CacheEntry(
                        key=key,
                        value=compressed_value,
                        created_time=entry.created_time,
                        last_access_time=entry.last_access_time,
                        access_count=entry.access_count,
                        ttl=entry.ttl,
                        size_bytes=len(compressed_value),
                        level=CacheLevel.L2_COMPRESSED,
                        compressed=True
                    )
                    
                    self.l2_cache.put(key, l2_entry)
                    self.l1_cache.remove(key)
                    compressed_count += 1
        
        result = {
            "expired_entries_removed": expired_count,
            "entries_compressed": compressed_count,
            "optimization_completed": True
        }
        
        logger.info(f"缓存优化完成: {result}")
        return result


# 全局缓存服务实例
_cache_service: Optional[IntelligentCacheService] = None


def get_cache_service() -> IntelligentCacheService:
    """获取全局缓存服务实例"""
    global _cache_service
    if _cache_service is None:
        config = CacheConfig(
            max_size=1000,
            max_memory_mb=100,
            strategy=CacheStrategy.LRU,
            enable_compression=True,
            enable_prefetch=True
        )
        _cache_service = IntelligentCacheService(config)
    return _cache_service


# 缓存装饰器
def cache_result(ttl: Optional[float] = None, key_func: Optional[Callable] = None):
    """
    缓存结果装饰器
    
    Args:
        ttl: 缓存生存时间
        key_func: 自定义键生成函数
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            # 生成缓存键
            if key_func:
                cache_key = key_func(*args, **kwargs)
            else:
                cache_key = f"{func.__name__}_{hash(str(args))}_{hash(str(kwargs))}"
            
            # 尝试从缓存获取
            cache_service = get_cache_service()
            result = cache_service.get(cache_key)
            
            if result is not None:
                return result
            
            # 执行函数并缓存结果
            result = func(*args, **kwargs)
            cache_service.put(cache_key, result, ttl)
            
            return result
        
        return wrapper
    return decorator