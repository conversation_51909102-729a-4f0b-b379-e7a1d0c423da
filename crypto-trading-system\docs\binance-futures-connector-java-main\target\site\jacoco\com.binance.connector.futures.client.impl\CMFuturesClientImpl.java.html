<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>CMFuturesClientImpl.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">io.github.binance:binance-futures-connector-java</a> &gt; <a href="index.source.html" class="el_package">com.binance.connector.futures.client.impl</a> &gt; <span class="el_source">CMFuturesClientImpl.java</span></div><h1>CMFuturesClientImpl.java</h1><pre class="source lang-java linenums">package com.binance.connector.futures.client.impl;

import com.binance.connector.futures.client.enums.DefaultUrls;
import com.binance.connector.futures.client.impl.cm_futures.CMMarket;
import com.binance.connector.futures.client.impl.cm_futures.CMAccount;
import com.binance.connector.futures.client.impl.cm_futures.CMUserData;
import com.binance.connector.futures.client.impl.cm_futures.CMPortfolioMargin;

public class CMFuturesClientImpl extends FuturesClientImpl {
<span class="fc" id="L10">    private static String defaultBaseUrl = DefaultUrls.COINM_PROD_URL;</span>
<span class="fc" id="L11">    private static String cmProduct = &quot;/dapi&quot;;</span>

    public CMFuturesClientImpl() {
<span class="nc" id="L14">        super(defaultBaseUrl, cmProduct);</span>
<span class="nc" id="L15">    }</span>

    public CMFuturesClientImpl(String baseUrl) {
<span class="fc" id="L18">        super(baseUrl, cmProduct);</span>
<span class="fc" id="L19">    }</span>

    public CMFuturesClientImpl(String apiKey, String secretKey) {
<span class="nc" id="L22">        super(apiKey, secretKey, defaultBaseUrl, cmProduct);</span>
<span class="nc" id="L23">    }</span>

    public CMFuturesClientImpl(String baseUrl, boolean showLimitUsage) {
<span class="nc" id="L26">        super(baseUrl, cmProduct, showLimitUsage);</span>
<span class="nc" id="L27">    }</span>

    public CMFuturesClientImpl(String apiKey, String secretKey, boolean showLimitUsage) {
<span class="nc" id="L30">        super(apiKey, secretKey, defaultBaseUrl, cmProduct, showLimitUsage);</span>
<span class="nc" id="L31">    }</span>

    public CMFuturesClientImpl(String apiKey, String secretKey, String baseUrl) {
<span class="fc" id="L34">        super(apiKey, secretKey, baseUrl, cmProduct);</span>
<span class="fc" id="L35">    }</span>

    @Override
    public CMMarket market() {
<span class="fc" id="L39">        return new CMMarket(getProductUrl(), getBaseUrl(), getApiKey(), getShowLimitUsage(), getProxy());</span>
    }

    @Override
    public CMAccount account() {
<span class="fc" id="L44">        return new CMAccount(getProductUrl(), getApiKey(), getSecretKey(), getShowLimitUsage(), getProxy());</span>
    }

    @Override
    public CMUserData userData() {
<span class="fc" id="L49">        return new CMUserData(getProductUrl(), getApiKey(), getShowLimitUsage(), getProxy());</span>
    }

    @Override
    public CMPortfolioMargin portfolioMargin() {
<span class="fc" id="L54">        return new CMPortfolioMargin(getProductUrl(), getApiKey(), getSecretKey(), getShowLimitUsage(), getProxy());</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.7.************</span></div></body></html>