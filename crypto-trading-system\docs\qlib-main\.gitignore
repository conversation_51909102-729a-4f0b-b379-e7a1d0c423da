# https://github.com/github/gitignore/blob/master/Python.gitignore
__pycache__/

*.pyc
*.pyd
*.so
*.ipynb
.ipynb_checkpoints
_build
build/
dist/

*.pkl
*.hd5
*.csv

.env
.vim
.nvimrc
.vscode

qlib/VERSION.txt
qlib/data/_libs/expanding.cpp
qlib/data/_libs/rolling.cpp
examples/estimator/estimator_example/
examples/rl/data/
examples/rl/checkpoints/
examples/rl/outputs/
examples/rl_order_execution/data/
examples/rl_order_execution/outputs/

*.egg-info/

# test related
test-output.xml
.output
.data

# special software
mlruns/

tags

.pytest_cache/
.mypy_cache/
.vscode/

*.swp

./pretrain
.idea/
.aider*
