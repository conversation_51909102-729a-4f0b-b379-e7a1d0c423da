{"namespace": "com.crypto.trading.common.avro", "type": "record", "name": "DepthData", "doc": "深度数据Avro模式定义", "fields": [{"name": "id", "type": "string", "doc": "唯一消息ID"}, {"name": "timestamp", "type": "long", "doc": "消息时间戳(毫秒)"}, {"name": "type", "type": "string", "doc": "消息类型，固定为DEPTH"}, {"name": "source", "type": "string", "doc": "消息来源模块"}, {"name": "version", "type": "string", "doc": "消息版本"}, {"name": "data", "type": {"type": "record", "name": "DepthDataContent", "fields": [{"name": "symbol", "type": "string", "doc": "交易对符号"}, {"name": "lastUpdateId", "type": "long", "doc": "最后更新ID"}, {"name": "eventTime", "type": "long", "doc": "事件时间(毫秒)"}, {"name": "bids", "type": {"type": "array", "items": {"type": "record", "name": "DepthLevel", "fields": [{"name": "price", "type": "double", "doc": "价格"}, {"name": "quantity", "type": "double", "doc": "数量"}]}}, "doc": "买单列表"}, {"name": "asks", "type": {"type": "array", "items": "DepthLevel"}, "doc": "卖单列表"}]}, "doc": "深度数据内容"}]}