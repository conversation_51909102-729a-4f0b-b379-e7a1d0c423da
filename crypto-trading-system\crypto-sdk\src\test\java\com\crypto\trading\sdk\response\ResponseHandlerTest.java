package com.crypto.trading.sdk.response;

import com.crypto.trading.sdk.converter.JacksonJsonConverter;
import com.crypto.trading.sdk.converter.JsonConverter;
import com.crypto.trading.sdk.exception.ApiException;
import com.crypto.trading.sdk.response.model.ApiResponse;
import com.crypto.trading.sdk.response.model.WebSocketMessage;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 响应处理器测试类
 */
public class ResponseHandlerTest {

    private JsonConverter jsonConverter;
    private ResponseHandler responseHandler;

    @BeforeEach
    public void setUp() {
        jsonConverter = new JacksonJsonConverter();
        responseHandler = new ResponseHandlerImpl(jsonConverter);
    }

    @Test
    public void testHandleApiResponse_Success() {
        // 创建测试数据
        ApiResponse<Map<String, Object>> apiResponse = new ApiResponse<>(0, "success", new HashMap<>());
        apiResponse.getData().put("symbol", "BTCUSDT");
        apiResponse.getData().put("price", "50000.00");
        
        String jsonResponse = jsonConverter.toJson(apiResponse);
        
        // 处理响应
        ApiResponse<?> result = responseHandler.handleApiResponse(jsonResponse, ApiResponse.class);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getCode());
        assertEquals("success", result.getMsg());
        assertTrue(result.isSuccess());
    }

    @Test
    public void testHandleApiResponse_Error() {
        // 创建错误响应
        String jsonResponse = "{\"code\":-1021,\"msg\":\"Timestamp for this request is outside of the recvWindow.\"}";
        
        // 验证异常
        Exception exception = assertThrows(ApiException.class, () -> {
            responseHandler.handleApiResponse(jsonResponse, ApiResponse.class);
        });
        
        assertTrue(exception.getMessage().contains("Timestamp"));
    }

    @Test
    public void testHandleApiResponseAsync() throws InterruptedException {
        // 创建测试数据
        String jsonResponse = "{\"code\":0,\"msg\":\"success\",\"data\":{\"symbol\":\"BTCUSDT\",\"price\":\"50000.00\"}}";
        
        // 异步处理
        CountDownLatch latch = new CountDownLatch(1);
        AtomicReference<ApiResponse<?>> resultRef = new AtomicReference<>();
        
        responseHandler.handleApiResponseAsync(jsonResponse, ApiResponse.class, 
            result -> {
                resultRef.set(result);
                latch.countDown();
            },
            error -> {
                fail("Should not get error: " + error.getMessage());
                latch.countDown();
            });
        
        // 等待处理完成
        assertTrue(latch.await(1, TimeUnit.SECONDS));
        
        // 验证结果
        ApiResponse<?> result = resultRef.get();
        assertNotNull(result);
        assertEquals(0, result.getCode());
        assertEquals("success", result.getMsg());
    }

    @Test
    public void testHandleWebSocketMessage() {
        // 创建WebSocket消息，确保使用字段名 e, E, s 而不是 eventType, eventTime, symbol
        String message = "{\"e\":\"kline\",\"E\":1619049297332,\"s\":\"BTCUSDT\",\"data\":{\"t\":1619049240000,\"T\":1619049299999,\"s\":\"BTCUSDT\",\"i\":\"1m\",\"o\":\"54000.00\",\"c\":\"54100.00\",\"h\":\"54200.00\",\"l\":\"53900.00\",\"v\":\"10.5\",\"q\":\"567890.00\",\"n\":120}}";
        
        // 处理消息
        WebSocketMessage<?> result = responseHandler.handleWebSocketMessage(message, WebSocketMessage.class);
        
        // 验证结果
        assertNotNull(result);
        assertEquals("kline", result.getEventType());
        assertEquals("BTCUSDT", result.getSymbol());
        assertEquals(1619049297332L, result.getEventTime());
        assertNotNull(result.getData());
    }
} 