package com.crypto.trading.trade.repository.mapper.order.status;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.crypto.trading.trade.model.entity.order.status.OrderStatusEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 订单状态数据访问接口
 * 
 * <p>提供订单状态历史的CRUD操作和自定义查询</p>
 */
@Mapper
public interface OrderStatusMapper extends BaseMapper<OrderStatusEntity> {

    /**
     * 根据订单ID查询状态变更历史
     *
     * @param orderId 订单ID
     * @return 状态变更历史列表
     */
    @Select("SELECT * FROM t_order_status WHERE order_id = #{orderId} ORDER BY update_time DESC")
    List<OrderStatusEntity> findByOrderId(@Param("orderId") String orderId);

    /**
     * 根据客户端订单ID查询状态变更历史
     *
     * @param clientOrderId 客户端订单ID
     * @return 状态变更历史列表
     */
    @Select("SELECT * FROM t_order_status WHERE client_order_id = #{clientOrderId} ORDER BY update_time DESC")
    List<OrderStatusEntity> findByClientOrderId(@Param("clientOrderId") String clientOrderId);

    /**
     * 根据新状态查询状态变更记录
     *
     * @param newStatus 新状态
     * @param limit 限制数量
     * @return 状态变更记录列表
     */
    @Select("SELECT * FROM t_order_status WHERE new_status = #{newStatus} ORDER BY update_time DESC LIMIT #{limit}")
    List<OrderStatusEntity> findByNewStatus(@Param("newStatus") String newStatus, @Param("limit") int limit);

    /**
     * 根据时间范围查询状态变更记录
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param limit 限制数量
     * @return 状态变更记录列表
     */
    @Select("SELECT * FROM t_order_status WHERE update_time BETWEEN #{startTime} AND #{endTime} " +
            "ORDER BY update_time DESC LIMIT #{limit}")
    List<OrderStatusEntity> findByTimeRange(
            @Param("startTime") long startTime, 
            @Param("endTime") long endTime, 
            @Param("limit") int limit);

    /**
     * 根据订单ID和新状态查询状态变更记录
     *
     * @param orderId 订单ID
     * @param newStatus 新状态
     * @return 状态变更记录
     */
    @Select("SELECT * FROM t_order_status WHERE order_id = #{orderId} AND new_status = #{newStatus} " +
            "ORDER BY update_time DESC LIMIT 1")
    OrderStatusEntity findByOrderIdAndNewStatus(
            @Param("orderId") String orderId, 
            @Param("newStatus") String newStatus);

    /**
     * 分页查询订单状态变更历史
     *
     * @param page 分页参数
     * @param orderId 订单ID（可选）
     * @param newStatus 新状态（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @return 分页结果
     */
    IPage<OrderStatusEntity> pageOrderStatus(
            IPage<OrderStatusEntity> page,
            @Param("orderId") String orderId,
            @Param("newStatus") String newStatus,
            @Param("startTime") Long startTime,
            @Param("endTime") Long endTime);
}