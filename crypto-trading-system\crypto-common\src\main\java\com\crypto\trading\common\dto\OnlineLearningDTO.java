package com.crypto.trading.common.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.io.Serializable;

/**
 * @name: OnlineLearningDTO
 * @author: zeek
 * @description: 在线学习相关信息的数据传输对象
 * @create: 2024-07-16 00:18
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OnlineLearningDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Double recentAccuracy;
    private Double adaptationRate;
    private Long lastUpdateTime;
}
