name: Java Main Workflow

on:
  pull_request:
    branches: [ master, main, rc-** ]

jobs:
  build:
    runs-on: ${{ matrix.operating-system }}
    strategy:
      matrix:
        operating-system: [ ubuntu-latest ]
        java-version: [ 8, 11, 17 ]

    steps:
      - uses: actions/checkout@v3
      - name: Set up JDK 8
        uses: actions/setup-java@v3
        with:
          java-version: ${{ matrix.java-version }}
          distribution: 'adopt'
          cache: 'maven'
      - name: Run checkstyle
        run: mvn checkstyle:check
      - name: Run unit tests
        run: mvn clean test
      - name: Build with Maven
        run: mvn install '-Dgpg.skip=true' '-Dmaven.javadoc.skip=true'
