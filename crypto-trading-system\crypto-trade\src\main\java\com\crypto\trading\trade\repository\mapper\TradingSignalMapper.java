package com.crypto.trading.trade.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.crypto.trading.trade.model.entity.TradingSignalEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 交易信号数据访问接口
 * 
 * <p>提供交易信号的CRUD操作和自定义查询</p>
 */
@Mapper
public interface TradingSignalMapper extends BaseMapper<TradingSignalEntity> {

    /**
     * 根据信号ID查询交易信号
     *
     * @param signalId 信号ID
     * @return 交易信号
     */
    @Select("SELECT * FROM t_trading_signal WHERE signal_id = #{signalId} AND is_deleted = 0")
    TradingSignalEntity findBySignalId(@Param("signalId") String signalId);

    /**
     * 根据交易对和时间范围查询交易信号
     *
     * @param symbol 交易对
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 交易信号列表
     */
    @Select("SELECT * FROM t_trading_signal WHERE symbol = #{symbol} AND generated_time BETWEEN #{startTime} AND #{endTime} AND is_deleted = 0 ORDER BY generated_time DESC")
    List<TradingSignalEntity> findBySymbolAndTimeRange(
            @Param("symbol") String symbol, 
            @Param("startTime") long startTime, 
            @Param("endTime") long endTime);

    /**
     * 根据策略和时间范围查询交易信号
     *
     * @param strategy 策略名称
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 交易信号列表
     */
    @Select("SELECT * FROM t_trading_signal WHERE strategy = #{strategy} AND generated_time BETWEEN #{startTime} AND #{endTime} AND is_deleted = 0 ORDER BY generated_time DESC")
    List<TradingSignalEntity> findByStrategyAndTimeRange(
            @Param("strategy") String strategy, 
            @Param("startTime") long startTime, 
            @Param("endTime") long endTime);

    /**
     * 查询未处理的交易信号
     *
     * @param limit 限制数量
     * @return 未处理的交易信号列表
     */
    @Select("SELECT * FROM t_trading_signal WHERE processed = 0 AND is_deleted = 0 ORDER BY generated_time ASC LIMIT #{limit}")
    List<TradingSignalEntity> findUnprocessedSignals(@Param("limit") int limit);

    /**
     * 更新信号处理状态
     *
     * @param signalId 信号ID
     * @param processed 处理状态
     * @param processResult 处理结果
     * @return 影响的行数
     */
    @Update("UPDATE t_trading_signal SET processed = #{processed}, processed_time = #{processedTime}, process_result = #{processResult} WHERE signal_id = #{signalId}")
    int updateProcessStatus(
            @Param("signalId") String signalId, 
            @Param("processed") boolean processed, 
            @Param("processedTime") long processedTime, 
            @Param("processResult") String processResult);
}