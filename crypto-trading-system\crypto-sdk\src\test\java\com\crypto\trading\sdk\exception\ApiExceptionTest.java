package com.crypto.trading.sdk.exception;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * API异常测试类
 */
public class ApiExceptionTest {

    @Test
    public void testConstructorWithCodeAndMessage() {
        // 测试带代码和消息的构造函数
        int code = 1001;
        String message = "测试异常消息";
        
        ApiException exception = new ApiException(code, message);
        
        assertEquals(code, exception.getCode());
        assertEquals(message, exception.getMessage());
        assertEquals(message, exception.getErrorMessage());
        assertNull(exception.getCause());
    }

    @Test
    public void testConstructorWithCodeMessageAndCause() {
        // 测试带代码、消息和原因的构造函数
        int code = 1002;
        String message = "测试异常消息与原因";
        Throwable cause = new RuntimeException("原始异常");
        
        ApiException exception = new ApiException(code, message, cause);
        
        assertEquals(code, exception.getCode());
        assertEquals(message, exception.getMessage());
        assertEquals(message, exception.getErrorMessage());
        assertSame(cause, exception.getCause());
    }

    @Test
    public void testInheritance() {
        // 测试继承关系
        ApiException exception = new ApiException(1003, "继承测试");
        
        assertTrue(exception instanceof com.crypto.trading.common.exception.BaseException);
    }
} 