package com.crypto.trading.trade.service.trade;

import com.crypto.trading.common.dto.trade.OrderDTO;
import com.crypto.trading.common.dto.trade.OrderRequestDTO;
import com.crypto.trading.common.dto.trade.OrderResponseDTO;
import com.crypto.trading.trade.model.entity.order.OrderEntity;

/**
 * 订单转换服务接口
 * <p>
 * 提供订单相关DTO和实体之间的转换功能
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface OrderConverterService {

    /**
     * 将OrderRequestDTO转换为OrderEntity
     *
     * @param requestDTO 订单请求DTO
     * @return 订单实体
     */
    OrderEntity convertToEntity(OrderRequestDTO requestDTO);

    /**
     * 将OrderEntity转换为OrderDTO
     *
     * @param entity 订单实体
     * @return 订单DTO
     */
    OrderDTO convertToDTO(OrderEntity entity);

    /**
     * 将OrderEntity转换为OrderResponseDTO
     *
     * @param entity 订单实体
     * @return 订单响应DTO
     */
    OrderResponseDTO convertToResponseDTO(OrderEntity entity);

    /**
     * 将OrderDTO转换为OrderResponseDTO
     *
     * @param dto 订单DTO
     * @return 订单响应DTO
     */
    OrderResponseDTO convertToResponseDTO(OrderDTO dto);
}