package com.crypto.trading.bootstrap.initializer;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 抽象模块初始化器测试类
 */
class AbstractModuleInitializerTest {
    
    /**
     * 测试用模块初始化器
     */
    private static class TestModuleInitializer extends AbstractModuleInitializer {
        
        private boolean initializeCalled = false;
        private boolean shutdownCalled = false;
        private Exception exceptionToThrow = null;
        
        @Override
        public String getName() {
            return "test-module";
        }
        
        @Override
        protected void doInitialize() throws Exception {
            initializeCalled = true;
            if (exceptionToThrow != null) {
                throw exceptionToThrow;
            }
        }
        
        @Override
        protected void doShutdown() {
            shutdownCalled = true;
        }
        
        public void setExceptionToThrow(Exception exceptionToThrow) {
            this.exceptionToThrow = exceptionToThrow;
        }
        
        public boolean isInitializeCalled() {
            return initializeCalled;
        }
        
        public boolean isShutdownCalled() {
            return shutdownCalled;
        }
    }
    
    private TestModuleInitializer initializer;
    
    @BeforeEach
    void setUp() {
        initializer = new TestModuleInitializer();
    }
    
    /**
     * 测试初始化流程
     */
    @Test
    void testInitialize() throws Exception {
        // 验证初始状态
        assertFalse(initializer.isInitialized());
        assertFalse(initializer.isInitializeCalled());
        
        // 执行初始化
        initializer.beforeInitialize();
        initializer.initialize();
        initializer.afterInitialize();
        
        // 验证初始化后的状态
        assertTrue(initializer.isInitialized());
        assertTrue(initializer.isInitializeCalled());
    }
    
    /**
     * 测试初始化异常处理
     */
    @Test
    void testInitializeWithException() {
        // 设置初始化时抛出异常
        Exception expectedException = new RuntimeException("测试异常");
        initializer.setExceptionToThrow(expectedException);
        
        // 验证初始状态
        assertFalse(initializer.isInitialized());
        
        // 执行初始化，预期会抛出异常
        initializer.beforeInitialize();
        Exception actualException = assertThrows(RuntimeException.class, () -> initializer.initialize());
        
        // 验证抛出的是预期的异常
        assertEquals(expectedException, actualException);
        
        // 验证初始化失败后的状态
        assertFalse(initializer.isInitialized());
        assertTrue(initializer.isInitializeCalled());
    }
    
    /**
     * 测试关闭流程
     */
    @Test
    void testShutdown() throws Exception {
        // 先初始化
        initializer.initialize();
        assertTrue(initializer.isInitialized());
        
        // 执行关闭
        initializer.shutdown();
        
        // 验证关闭后的状态
        assertFalse(initializer.isInitialized());
        assertTrue(initializer.isShutdownCalled());
    }
    
    /**
     * 测试默认优先级和依赖
     */
    @Test
    void testDefaultPriorityAndDependencies() {
        assertEquals(100, initializer.getPriority());
        assertEquals(0, initializer.getDependencies().length);
    }
}