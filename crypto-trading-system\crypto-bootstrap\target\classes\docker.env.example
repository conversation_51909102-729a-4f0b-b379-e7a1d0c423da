# 密钥和敏感数据 - 复制此文件为.env并填写实际值
# 数据库配置
DB_HOST=mysql
DB_PORT=3306
DB_NAME=crypto_trading
DB_USER=root
DB_PASSWORD=root

# Kafka配置
KAFKA_SERVERS=kafka:9092
KAFKA_GROUP_ID=crypto-trading-group

# InfluxDB配置
INFLUXDB_URL=http://influxdb:8086
INFLUXDB_TOKEN=your_influxdb_token_here
INFLUXDB_ORG=crypto
INFLUXDB_BUCKET=market_data

# 币安API配置
BINANCE_API_KEY=your_api_key_here
BINANCE_API_SECRET=your_api_secret_here
BINANCE_USE_TESTNET=false

# 日志配置
LOG_PATH=/var/log/crypto-trading
LOG_LEVEL=INFO

# 应用配置
SPRING_PROFILES_ACTIVE=prod
SERVER_PORT=8080 