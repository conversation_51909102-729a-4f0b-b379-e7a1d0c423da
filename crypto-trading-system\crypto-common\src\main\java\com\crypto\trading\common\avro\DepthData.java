/**
 * Autogenerated by Avro
 *
 * DO NOT EDIT DIRECTLY
 */
package com.crypto.trading.common.avro;

import org.apache.avro.generic.GenericArray;
import org.apache.avro.specific.SpecificData;
import org.apache.avro.util.Utf8;
import org.apache.avro.message.BinaryMessageEncoder;
import org.apache.avro.message.BinaryMessageDecoder;
import org.apache.avro.message.SchemaStore;

/** 深度数据的Avro模式定义 */
@org.apache.avro.specific.AvroGenerated
public class DepthData extends org.apache.avro.specific.SpecificRecordBase implements org.apache.avro.specific.SpecificRecord {
  private static final long serialVersionUID = 3274082969016755783L;


  public static final org.apache.avro.Schema SCHEMA$ = new org.apache.avro.Schema.Parser().parse("{\"type\":\"record\",\"name\":\"DepthData\",\"namespace\":\"com.crypto.trading.common.avro\",\"doc\":\"深度数据的Avro模式定义\",\"fields\":[{\"name\":\"symbol\",\"type\":{\"type\":\"string\",\"avro.java.string\":\"String\"},\"doc\":\"交易对符号，如BTCUSDT\"},{\"name\":\"updateId\",\"type\":\"long\",\"doc\":\"更新ID\"},{\"name\":\"timestamp\",\"type\":\"long\",\"doc\":\"消息生成时间（毫秒时间戳）\"},{\"name\":\"bids\",\"type\":{\"type\":\"array\",\"items\":{\"type\":\"record\",\"name\":\"PriceLevel\",\"fields\":[{\"name\":\"price\",\"type\":\"double\",\"doc\":\"价格\"},{\"name\":\"quantity\",\"type\":\"double\",\"doc\":\"数量\"}]}},\"doc\":\"买单列表\"},{\"name\":\"asks\",\"type\":{\"type\":\"array\",\"items\":\"PriceLevel\"},\"doc\":\"卖单列表\"},{\"name\":\"source\",\"type\":{\"type\":\"string\",\"avro.java.string\":\"String\"},\"doc\":\"数据来源\",\"default\":\"binance\"}]}");
  public static org.apache.avro.Schema getClassSchema() { return SCHEMA$; }

  private static final SpecificData MODEL$ = new SpecificData();

  private static final BinaryMessageEncoder<DepthData> ENCODER =
      new BinaryMessageEncoder<>(MODEL$, SCHEMA$);

  private static final BinaryMessageDecoder<DepthData> DECODER =
      new BinaryMessageDecoder<>(MODEL$, SCHEMA$);

  /**
   * Return the BinaryMessageEncoder instance used by this class.
   * @return the message encoder used by this class
   */
  public static BinaryMessageEncoder<DepthData> getEncoder() {
    return ENCODER;
  }

  /**
   * Return the BinaryMessageDecoder instance used by this class.
   * @return the message decoder used by this class
   */
  public static BinaryMessageDecoder<DepthData> getDecoder() {
    return DECODER;
  }

  /**
   * Create a new BinaryMessageDecoder instance for this class that uses the specified {@link SchemaStore}.
   * @param resolver a {@link SchemaStore} used to find schemas by fingerprint
   * @return a BinaryMessageDecoder instance for this class backed by the given SchemaStore
   */
  public static BinaryMessageDecoder<DepthData> createDecoder(SchemaStore resolver) {
    return new BinaryMessageDecoder<>(MODEL$, SCHEMA$, resolver);
  }

  /**
   * Serializes this DepthData to a ByteBuffer.
   * @return a buffer holding the serialized data for this instance
   * @throws java.io.IOException if this instance could not be serialized
   */
  public java.nio.ByteBuffer toByteBuffer() throws java.io.IOException {
    return ENCODER.encode(this);
  }

  /**
   * Deserializes a DepthData from a ByteBuffer.
   * @param b a byte buffer holding serialized data for an instance of this class
   * @return a DepthData instance decoded from the given buffer
   * @throws java.io.IOException if the given bytes could not be deserialized into an instance of this class
   */
  public static DepthData fromByteBuffer(
      java.nio.ByteBuffer b) throws java.io.IOException {
    return DECODER.decode(b);
  }

  /** 交易对符号，如BTCUSDT */
  private java.lang.String symbol;
  /** 更新ID */
  private long updateId;
  /** 消息生成时间（毫秒时间戳） */
  private long timestamp;
  /** 买单列表 */
  private java.util.List<com.crypto.trading.common.avro.PriceLevel> bids;
  /** 卖单列表 */
  private java.util.List<com.crypto.trading.common.avro.PriceLevel> asks;
  /** 数据来源 */
  private java.lang.String source;

  /**
   * Default constructor.  Note that this does not initialize fields
   * to their default values from the schema.  If that is desired then
   * one should use <code>newBuilder()</code>.
   */
  public DepthData() {}

  /**
   * All-args constructor.
   * @param symbol 交易对符号，如BTCUSDT
   * @param updateId 更新ID
   * @param timestamp 消息生成时间（毫秒时间戳）
   * @param bids 买单列表
   * @param asks 卖单列表
   * @param source 数据来源
   */
  public DepthData(java.lang.String symbol, java.lang.Long updateId, java.lang.Long timestamp, java.util.List<com.crypto.trading.common.avro.PriceLevel> bids, java.util.List<com.crypto.trading.common.avro.PriceLevel> asks, java.lang.String source) {
    this.symbol = symbol;
    this.updateId = updateId;
    this.timestamp = timestamp;
    this.bids = bids;
    this.asks = asks;
    this.source = source;
  }

  @Override
  public org.apache.avro.specific.SpecificData getSpecificData() { return MODEL$; }

  @Override
  public org.apache.avro.Schema getSchema() { return SCHEMA$; }

  // Used by DatumWriter.  Applications should not call.
  @Override
  public java.lang.Object get(int field$) {
    switch (field$) {
    case 0: return symbol;
    case 1: return updateId;
    case 2: return timestamp;
    case 3: return bids;
    case 4: return asks;
    case 5: return source;
    default: throw new IndexOutOfBoundsException("Invalid index: " + field$);
    }
  }

  // Used by DatumReader.  Applications should not call.
  @Override
  @SuppressWarnings(value="unchecked")
  public void put(int field$, java.lang.Object value$) {
    switch (field$) {
    case 0: symbol = value$ != null ? value$.toString() : null; break;
    case 1: updateId = (java.lang.Long)value$; break;
    case 2: timestamp = (java.lang.Long)value$; break;
    case 3: bids = (java.util.List<com.crypto.trading.common.avro.PriceLevel>)value$; break;
    case 4: asks = (java.util.List<com.crypto.trading.common.avro.PriceLevel>)value$; break;
    case 5: source = value$ != null ? value$.toString() : null; break;
    default: throw new IndexOutOfBoundsException("Invalid index: " + field$);
    }
  }

  /**
   * Gets the value of the 'symbol' field.
   * @return 交易对符号，如BTCUSDT
   */
  public java.lang.String getSymbol() {
    return symbol;
  }


  /**
   * Sets the value of the 'symbol' field.
   * 交易对符号，如BTCUSDT
   * @param value the value to set.
   */
  public void setSymbol(java.lang.String value) {
    this.symbol = value;
  }

  /**
   * Gets the value of the 'updateId' field.
   * @return 更新ID
   */
  public long getUpdateId() {
    return updateId;
  }


  /**
   * Sets the value of the 'updateId' field.
   * 更新ID
   * @param value the value to set.
   */
  public void setUpdateId(long value) {
    this.updateId = value;
  }

  /**
   * Gets the value of the 'timestamp' field.
   * @return 消息生成时间（毫秒时间戳）
   */
  public long getTimestamp() {
    return timestamp;
  }


  /**
   * Sets the value of the 'timestamp' field.
   * 消息生成时间（毫秒时间戳）
   * @param value the value to set.
   */
  public void setTimestamp(long value) {
    this.timestamp = value;
  }

  /**
   * Gets the value of the 'bids' field.
   * @return 买单列表
   */
  public java.util.List<com.crypto.trading.common.avro.PriceLevel> getBids() {
    return bids;
  }


  /**
   * Sets the value of the 'bids' field.
   * 买单列表
   * @param value the value to set.
   */
  public void setBids(java.util.List<com.crypto.trading.common.avro.PriceLevel> value) {
    this.bids = value;
  }

  /**
   * Gets the value of the 'asks' field.
   * @return 卖单列表
   */
  public java.util.List<com.crypto.trading.common.avro.PriceLevel> getAsks() {
    return asks;
  }


  /**
   * Sets the value of the 'asks' field.
   * 卖单列表
   * @param value the value to set.
   */
  public void setAsks(java.util.List<com.crypto.trading.common.avro.PriceLevel> value) {
    this.asks = value;
  }

  /**
   * Gets the value of the 'source' field.
   * @return 数据来源
   */
  public java.lang.String getSource() {
    return source;
  }


  /**
   * Sets the value of the 'source' field.
   * 数据来源
   * @param value the value to set.
   */
  public void setSource(java.lang.String value) {
    this.source = value;
  }

  /**
   * Creates a new DepthData RecordBuilder.
   * @return A new DepthData RecordBuilder
   */
  public static com.crypto.trading.common.avro.DepthData.Builder newBuilder() {
    return new com.crypto.trading.common.avro.DepthData.Builder();
  }

  /**
   * Creates a new DepthData RecordBuilder by copying an existing Builder.
   * @param other The existing builder to copy.
   * @return A new DepthData RecordBuilder
   */
  public static com.crypto.trading.common.avro.DepthData.Builder newBuilder(com.crypto.trading.common.avro.DepthData.Builder other) {
    if (other == null) {
      return new com.crypto.trading.common.avro.DepthData.Builder();
    } else {
      return new com.crypto.trading.common.avro.DepthData.Builder(other);
    }
  }

  /**
   * Creates a new DepthData RecordBuilder by copying an existing DepthData instance.
   * @param other The existing instance to copy.
   * @return A new DepthData RecordBuilder
   */
  public static com.crypto.trading.common.avro.DepthData.Builder newBuilder(com.crypto.trading.common.avro.DepthData other) {
    if (other == null) {
      return new com.crypto.trading.common.avro.DepthData.Builder();
    } else {
      return new com.crypto.trading.common.avro.DepthData.Builder(other);
    }
  }

  /**
   * RecordBuilder for DepthData instances.
   */
  @org.apache.avro.specific.AvroGenerated
  public static class Builder extends org.apache.avro.specific.SpecificRecordBuilderBase<DepthData>
    implements org.apache.avro.data.RecordBuilder<DepthData> {

    /** 交易对符号，如BTCUSDT */
    private java.lang.String symbol;
    /** 更新ID */
    private long updateId;
    /** 消息生成时间（毫秒时间戳） */
    private long timestamp;
    /** 买单列表 */
    private java.util.List<com.crypto.trading.common.avro.PriceLevel> bids;
    /** 卖单列表 */
    private java.util.List<com.crypto.trading.common.avro.PriceLevel> asks;
    /** 数据来源 */
    private java.lang.String source;

    /** Creates a new Builder */
    private Builder() {
      super(SCHEMA$, MODEL$);
    }

    /**
     * Creates a Builder by copying an existing Builder.
     * @param other The existing Builder to copy.
     */
    private Builder(com.crypto.trading.common.avro.DepthData.Builder other) {
      super(other);
      if (isValidValue(fields()[0], other.symbol)) {
        this.symbol = data().deepCopy(fields()[0].schema(), other.symbol);
        fieldSetFlags()[0] = other.fieldSetFlags()[0];
      }
      if (isValidValue(fields()[1], other.updateId)) {
        this.updateId = data().deepCopy(fields()[1].schema(), other.updateId);
        fieldSetFlags()[1] = other.fieldSetFlags()[1];
      }
      if (isValidValue(fields()[2], other.timestamp)) {
        this.timestamp = data().deepCopy(fields()[2].schema(), other.timestamp);
        fieldSetFlags()[2] = other.fieldSetFlags()[2];
      }
      if (isValidValue(fields()[3], other.bids)) {
        this.bids = data().deepCopy(fields()[3].schema(), other.bids);
        fieldSetFlags()[3] = other.fieldSetFlags()[3];
      }
      if (isValidValue(fields()[4], other.asks)) {
        this.asks = data().deepCopy(fields()[4].schema(), other.asks);
        fieldSetFlags()[4] = other.fieldSetFlags()[4];
      }
      if (isValidValue(fields()[5], other.source)) {
        this.source = data().deepCopy(fields()[5].schema(), other.source);
        fieldSetFlags()[5] = other.fieldSetFlags()[5];
      }
    }

    /**
     * Creates a Builder by copying an existing DepthData instance
     * @param other The existing instance to copy.
     */
    private Builder(com.crypto.trading.common.avro.DepthData other) {
      super(SCHEMA$, MODEL$);
      if (isValidValue(fields()[0], other.symbol)) {
        this.symbol = data().deepCopy(fields()[0].schema(), other.symbol);
        fieldSetFlags()[0] = true;
      }
      if (isValidValue(fields()[1], other.updateId)) {
        this.updateId = data().deepCopy(fields()[1].schema(), other.updateId);
        fieldSetFlags()[1] = true;
      }
      if (isValidValue(fields()[2], other.timestamp)) {
        this.timestamp = data().deepCopy(fields()[2].schema(), other.timestamp);
        fieldSetFlags()[2] = true;
      }
      if (isValidValue(fields()[3], other.bids)) {
        this.bids = data().deepCopy(fields()[3].schema(), other.bids);
        fieldSetFlags()[3] = true;
      }
      if (isValidValue(fields()[4], other.asks)) {
        this.asks = data().deepCopy(fields()[4].schema(), other.asks);
        fieldSetFlags()[4] = true;
      }
      if (isValidValue(fields()[5], other.source)) {
        this.source = data().deepCopy(fields()[5].schema(), other.source);
        fieldSetFlags()[5] = true;
      }
    }

    /**
      * Gets the value of the 'symbol' field.
      * 交易对符号，如BTCUSDT
      * @return The value.
      */
    public java.lang.String getSymbol() {
      return symbol;
    }


    /**
      * Sets the value of the 'symbol' field.
      * 交易对符号，如BTCUSDT
      * @param value The value of 'symbol'.
      * @return This builder.
      */
    public com.crypto.trading.common.avro.DepthData.Builder setSymbol(java.lang.String value) {
      validate(fields()[0], value);
      this.symbol = value;
      fieldSetFlags()[0] = true;
      return this;
    }

    /**
      * Checks whether the 'symbol' field has been set.
      * 交易对符号，如BTCUSDT
      * @return True if the 'symbol' field has been set, false otherwise.
      */
    public boolean hasSymbol() {
      return fieldSetFlags()[0];
    }


    /**
      * Clears the value of the 'symbol' field.
      * 交易对符号，如BTCUSDT
      * @return This builder.
      */
    public com.crypto.trading.common.avro.DepthData.Builder clearSymbol() {
      symbol = null;
      fieldSetFlags()[0] = false;
      return this;
    }

    /**
      * Gets the value of the 'updateId' field.
      * 更新ID
      * @return The value.
      */
    public long getUpdateId() {
      return updateId;
    }


    /**
      * Sets the value of the 'updateId' field.
      * 更新ID
      * @param value The value of 'updateId'.
      * @return This builder.
      */
    public com.crypto.trading.common.avro.DepthData.Builder setUpdateId(long value) {
      validate(fields()[1], value);
      this.updateId = value;
      fieldSetFlags()[1] = true;
      return this;
    }

    /**
      * Checks whether the 'updateId' field has been set.
      * 更新ID
      * @return True if the 'updateId' field has been set, false otherwise.
      */
    public boolean hasUpdateId() {
      return fieldSetFlags()[1];
    }


    /**
      * Clears the value of the 'updateId' field.
      * 更新ID
      * @return This builder.
      */
    public com.crypto.trading.common.avro.DepthData.Builder clearUpdateId() {
      fieldSetFlags()[1] = false;
      return this;
    }

    /**
      * Gets the value of the 'timestamp' field.
      * 消息生成时间（毫秒时间戳）
      * @return The value.
      */
    public long getTimestamp() {
      return timestamp;
    }


    /**
      * Sets the value of the 'timestamp' field.
      * 消息生成时间（毫秒时间戳）
      * @param value The value of 'timestamp'.
      * @return This builder.
      */
    public com.crypto.trading.common.avro.DepthData.Builder setTimestamp(long value) {
      validate(fields()[2], value);
      this.timestamp = value;
      fieldSetFlags()[2] = true;
      return this;
    }

    /**
      * Checks whether the 'timestamp' field has been set.
      * 消息生成时间（毫秒时间戳）
      * @return True if the 'timestamp' field has been set, false otherwise.
      */
    public boolean hasTimestamp() {
      return fieldSetFlags()[2];
    }


    /**
      * Clears the value of the 'timestamp' field.
      * 消息生成时间（毫秒时间戳）
      * @return This builder.
      */
    public com.crypto.trading.common.avro.DepthData.Builder clearTimestamp() {
      fieldSetFlags()[2] = false;
      return this;
    }

    /**
      * Gets the value of the 'bids' field.
      * 买单列表
      * @return The value.
      */
    public java.util.List<com.crypto.trading.common.avro.PriceLevel> getBids() {
      return bids;
    }


    /**
      * Sets the value of the 'bids' field.
      * 买单列表
      * @param value The value of 'bids'.
      * @return This builder.
      */
    public com.crypto.trading.common.avro.DepthData.Builder setBids(java.util.List<com.crypto.trading.common.avro.PriceLevel> value) {
      validate(fields()[3], value);
      this.bids = value;
      fieldSetFlags()[3] = true;
      return this;
    }

    /**
      * Checks whether the 'bids' field has been set.
      * 买单列表
      * @return True if the 'bids' field has been set, false otherwise.
      */
    public boolean hasBids() {
      return fieldSetFlags()[3];
    }


    /**
      * Clears the value of the 'bids' field.
      * 买单列表
      * @return This builder.
      */
    public com.crypto.trading.common.avro.DepthData.Builder clearBids() {
      bids = null;
      fieldSetFlags()[3] = false;
      return this;
    }

    /**
      * Gets the value of the 'asks' field.
      * 卖单列表
      * @return The value.
      */
    public java.util.List<com.crypto.trading.common.avro.PriceLevel> getAsks() {
      return asks;
    }


    /**
      * Sets the value of the 'asks' field.
      * 卖单列表
      * @param value The value of 'asks'.
      * @return This builder.
      */
    public com.crypto.trading.common.avro.DepthData.Builder setAsks(java.util.List<com.crypto.trading.common.avro.PriceLevel> value) {
      validate(fields()[4], value);
      this.asks = value;
      fieldSetFlags()[4] = true;
      return this;
    }

    /**
      * Checks whether the 'asks' field has been set.
      * 卖单列表
      * @return True if the 'asks' field has been set, false otherwise.
      */
    public boolean hasAsks() {
      return fieldSetFlags()[4];
    }


    /**
      * Clears the value of the 'asks' field.
      * 卖单列表
      * @return This builder.
      */
    public com.crypto.trading.common.avro.DepthData.Builder clearAsks() {
      asks = null;
      fieldSetFlags()[4] = false;
      return this;
    }

    /**
      * Gets the value of the 'source' field.
      * 数据来源
      * @return The value.
      */
    public java.lang.String getSource() {
      return source;
    }


    /**
      * Sets the value of the 'source' field.
      * 数据来源
      * @param value The value of 'source'.
      * @return This builder.
      */
    public com.crypto.trading.common.avro.DepthData.Builder setSource(java.lang.String value) {
      validate(fields()[5], value);
      this.source = value;
      fieldSetFlags()[5] = true;
      return this;
    }

    /**
      * Checks whether the 'source' field has been set.
      * 数据来源
      * @return True if the 'source' field has been set, false otherwise.
      */
    public boolean hasSource() {
      return fieldSetFlags()[5];
    }


    /**
      * Clears the value of the 'source' field.
      * 数据来源
      * @return This builder.
      */
    public com.crypto.trading.common.avro.DepthData.Builder clearSource() {
      source = null;
      fieldSetFlags()[5] = false;
      return this;
    }

    @Override
    @SuppressWarnings("unchecked")
    public DepthData build() {
      try {
        DepthData record = new DepthData();
        record.symbol = fieldSetFlags()[0] ? this.symbol : (java.lang.String) defaultValue(fields()[0]);
        record.updateId = fieldSetFlags()[1] ? this.updateId : (java.lang.Long) defaultValue(fields()[1]);
        record.timestamp = fieldSetFlags()[2] ? this.timestamp : (java.lang.Long) defaultValue(fields()[2]);
        record.bids = fieldSetFlags()[3] ? this.bids : (java.util.List<com.crypto.trading.common.avro.PriceLevel>) defaultValue(fields()[3]);
        record.asks = fieldSetFlags()[4] ? this.asks : (java.util.List<com.crypto.trading.common.avro.PriceLevel>) defaultValue(fields()[4]);
        record.source = fieldSetFlags()[5] ? this.source : (java.lang.String) defaultValue(fields()[5]);
        return record;
      } catch (org.apache.avro.AvroMissingFieldException e) {
        throw e;
      } catch (java.lang.Exception e) {
        throw new org.apache.avro.AvroRuntimeException(e);
      }
    }
  }

  @SuppressWarnings("unchecked")
  private static final org.apache.avro.io.DatumWriter<DepthData>
    WRITER$ = (org.apache.avro.io.DatumWriter<DepthData>)MODEL$.createDatumWriter(SCHEMA$);

  @Override public void writeExternal(java.io.ObjectOutput out)
    throws java.io.IOException {
    WRITER$.write(this, SpecificData.getEncoder(out));
  }

  @SuppressWarnings("unchecked")
  private static final org.apache.avro.io.DatumReader<DepthData>
    READER$ = (org.apache.avro.io.DatumReader<DepthData>)MODEL$.createDatumReader(SCHEMA$);

  @Override public void readExternal(java.io.ObjectInput in)
    throws java.io.IOException {
    READER$.read(this, SpecificData.getDecoder(in));
  }

  @Override protected boolean hasCustomCoders() { return true; }

  @Override public void customEncode(org.apache.avro.io.Encoder out)
    throws java.io.IOException
  {
    out.writeString(this.symbol);

    out.writeLong(this.updateId);

    out.writeLong(this.timestamp);

    long size0 = this.bids.size();
    out.writeArrayStart();
    out.setItemCount(size0);
    long actualSize0 = 0;
    for (com.crypto.trading.common.avro.PriceLevel e0: this.bids) {
      actualSize0++;
      out.startItem();
      e0.customEncode(out);
    }
    out.writeArrayEnd();
    if (actualSize0 != size0)
      throw new java.util.ConcurrentModificationException("Array-size written was " + size0 + ", but element count was " + actualSize0 + ".");

    long size1 = this.asks.size();
    out.writeArrayStart();
    out.setItemCount(size1);
    long actualSize1 = 0;
    for (com.crypto.trading.common.avro.PriceLevel e1: this.asks) {
      actualSize1++;
      out.startItem();
      e1.customEncode(out);
    }
    out.writeArrayEnd();
    if (actualSize1 != size1)
      throw new java.util.ConcurrentModificationException("Array-size written was " + size1 + ", but element count was " + actualSize1 + ".");

    out.writeString(this.source);

  }

  @Override public void customDecode(org.apache.avro.io.ResolvingDecoder in)
    throws java.io.IOException
  {
    org.apache.avro.Schema.Field[] fieldOrder = in.readFieldOrderIfDiff();
    if (fieldOrder == null) {
      this.symbol = in.readString();

      this.updateId = in.readLong();

      this.timestamp = in.readLong();

      long size0 = in.readArrayStart();
      java.util.List<com.crypto.trading.common.avro.PriceLevel> a0 = this.bids;
      if (a0 == null) {
        a0 = new SpecificData.Array<com.crypto.trading.common.avro.PriceLevel>((int)size0, SCHEMA$.getField("bids").schema());
        this.bids = a0;
      } else a0.clear();
      SpecificData.Array<com.crypto.trading.common.avro.PriceLevel> ga0 = (a0 instanceof SpecificData.Array ? (SpecificData.Array<com.crypto.trading.common.avro.PriceLevel>)a0 : null);
      for ( ; 0 < size0; size0 = in.arrayNext()) {
        for ( ; size0 != 0; size0--) {
          com.crypto.trading.common.avro.PriceLevel e0 = (ga0 != null ? ga0.peek() : null);
          if (e0 == null) {
            e0 = new com.crypto.trading.common.avro.PriceLevel();
          }
          e0.customDecode(in);
          a0.add(e0);
        }
      }

      long size1 = in.readArrayStart();
      java.util.List<com.crypto.trading.common.avro.PriceLevel> a1 = this.asks;
      if (a1 == null) {
        a1 = new SpecificData.Array<com.crypto.trading.common.avro.PriceLevel>((int)size1, SCHEMA$.getField("asks").schema());
        this.asks = a1;
      } else a1.clear();
      SpecificData.Array<com.crypto.trading.common.avro.PriceLevel> ga1 = (a1 instanceof SpecificData.Array ? (SpecificData.Array<com.crypto.trading.common.avro.PriceLevel>)a1 : null);
      for ( ; 0 < size1; size1 = in.arrayNext()) {
        for ( ; size1 != 0; size1--) {
          com.crypto.trading.common.avro.PriceLevel e1 = (ga1 != null ? ga1.peek() : null);
          if (e1 == null) {
            e1 = new com.crypto.trading.common.avro.PriceLevel();
          }
          e1.customDecode(in);
          a1.add(e1);
        }
      }

      this.source = in.readString();

    } else {
      for (int i = 0; i < 6; i++) {
        switch (fieldOrder[i].pos()) {
        case 0:
          this.symbol = in.readString();
          break;

        case 1:
          this.updateId = in.readLong();
          break;

        case 2:
          this.timestamp = in.readLong();
          break;

        case 3:
          long size0 = in.readArrayStart();
          java.util.List<com.crypto.trading.common.avro.PriceLevel> a0 = this.bids;
          if (a0 == null) {
            a0 = new SpecificData.Array<com.crypto.trading.common.avro.PriceLevel>((int)size0, SCHEMA$.getField("bids").schema());
            this.bids = a0;
          } else a0.clear();
          SpecificData.Array<com.crypto.trading.common.avro.PriceLevel> ga0 = (a0 instanceof SpecificData.Array ? (SpecificData.Array<com.crypto.trading.common.avro.PriceLevel>)a0 : null);
          for ( ; 0 < size0; size0 = in.arrayNext()) {
            for ( ; size0 != 0; size0--) {
              com.crypto.trading.common.avro.PriceLevel e0 = (ga0 != null ? ga0.peek() : null);
              if (e0 == null) {
                e0 = new com.crypto.trading.common.avro.PriceLevel();
              }
              e0.customDecode(in);
              a0.add(e0);
            }
          }
          break;

        case 4:
          long size1 = in.readArrayStart();
          java.util.List<com.crypto.trading.common.avro.PriceLevel> a1 = this.asks;
          if (a1 == null) {
            a1 = new SpecificData.Array<com.crypto.trading.common.avro.PriceLevel>((int)size1, SCHEMA$.getField("asks").schema());
            this.asks = a1;
          } else a1.clear();
          SpecificData.Array<com.crypto.trading.common.avro.PriceLevel> ga1 = (a1 instanceof SpecificData.Array ? (SpecificData.Array<com.crypto.trading.common.avro.PriceLevel>)a1 : null);
          for ( ; 0 < size1; size1 = in.arrayNext()) {
            for ( ; size1 != 0; size1--) {
              com.crypto.trading.common.avro.PriceLevel e1 = (ga1 != null ? ga1.peek() : null);
              if (e1 == null) {
                e1 = new com.crypto.trading.common.avro.PriceLevel();
              }
              e1.customDecode(in);
              a1.add(e1);
            }
          }
          break;

        case 5:
          this.source = in.readString();
          break;

        default:
          throw new java.io.IOException("Corrupt ResolvingDecoder.");
        }
      }
    }
  }
}










