<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.binance.connector.futures.client.impl.futures</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.source.html" class="el_source">Source Files</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">io.github.binance:binance-futures-connector-java</a> &gt; <span class="el_package">com.binance.connector.futures.client.impl.futures</span></div><h1>com.binance.connector.futures.client.impl.futures</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">113 of 986</td><td class="ctr2">88%</td><td class="bar">0 of 0</td><td class="ctr2">n/a</td><td class="ctr1">20</td><td class="ctr2">78</td><td class="ctr1">35</td><td class="ctr2">189</td><td class="ctr1">20</td><td class="ctr2">78</td><td class="ctr1">0</td><td class="ctr2">4</td></tr></tfoot><tbody><tr><td id="a0"><a href="Account.html" class="el_class">Account</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="11" height="10" title="42" alt="42"/><img src="../jacoco-resources/greenbar.gif" width="108" height="10" title="391" alt="391"/></td><td class="ctr2" id="c1">90%</td><td class="bar" id="d0"/><td class="ctr2" id="e0">n/a</td><td class="ctr1" id="f2">4</td><td class="ctr2" id="g1">29</td><td class="ctr1" id="h0">9</td><td class="ctr2" id="i0">78</td><td class="ctr1" id="j2">4</td><td class="ctr2" id="k1">29</td><td class="ctr1" id="l0">0</td><td class="ctr2" id="m0">1</td></tr><tr><td id="a2"><a href="PortfolioMargin.html" class="el_class">PortfolioMargin</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="26" alt="26"/><img src="../jacoco-resources/greenbar.gif" width="13" height="10" title="49" alt="49"/></td><td class="ctr2" id="c3">65%</td><td class="bar" id="d1"/><td class="ctr2" id="e1">n/a</td><td class="ctr1" id="f0">6</td><td class="ctr2" id="g3">9</td><td class="ctr1" id="h1">9</td><td class="ctr2" id="i2">19</td><td class="ctr1" id="j0">6</td><td class="ctr2" id="k3">9</td><td class="ctr1" id="l1">0</td><td class="ctr2" id="m1">1</td></tr><tr><td id="a3"><a href="UserData.html" class="el_class">UserData</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="25" alt="25"/><img src="../jacoco-resources/greenbar.gif" width="14" height="10" title="52" alt="52"/></td><td class="ctr2" id="c2">67%</td><td class="bar" id="d2"/><td class="ctr2" id="e2">n/a</td><td class="ctr1" id="f1">6</td><td class="ctr2" id="g2">10</td><td class="ctr1" id="h2">9</td><td class="ctr2" id="i3">18</td><td class="ctr1" id="j1">6</td><td class="ctr2" id="k2">10</td><td class="ctr1" id="l2">0</td><td class="ctr2" id="m2">1</td></tr><tr><td id="a1"><a href="Market.html" class="el_class">Market</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="20" alt="20"/><img src="../jacoco-resources/greenbar.gif" width="105" height="10" title="381" alt="381"/></td><td class="ctr2" id="c0">95%</td><td class="bar" id="d3"/><td class="ctr2" id="e3">n/a</td><td class="ctr1" id="f3">4</td><td class="ctr2" id="g0">30</td><td class="ctr1" id="h3">8</td><td class="ctr2" id="i1">74</td><td class="ctr1" id="j3">4</td><td class="ctr2" id="k0">30</td><td class="ctr1" id="l3">0</td><td class="ctr2" id="m3">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.7.202105040129</span></div></body></html>