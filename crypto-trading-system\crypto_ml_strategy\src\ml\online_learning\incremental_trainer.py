#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
增量训练器

处理在线学习的增量训练任务，包括数据流处理、特征提取、批次管理、模型更新等功能。
与多时间周期数据处理、技术指标模块和数据质量检查器集成。
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any, Union, Callable
from datetime import datetime, timedelta
import threading
from collections import defaultdict, deque
from dataclasses import dataclass, field
import time
from enum import Enum
import json
import asyncio
from concurrent.futures import ThreadPoolExecutor
import queue


class TrainingMode(Enum):
    """训练模式枚举"""
    ONLINE = "online"               # 在线单样本学习
    MINI_BATCH = "mini_batch"       # 小批量学习
    BATCH = "batch"                 # 批量学习
    ADAPTIVE = "adaptive"           # 自适应模式


class UpdateTrigger(Enum):
    """更新触发器枚举"""
    TIME_BASED = "time_based"       # 基于时间
    SAMPLE_BASED = "sample_based"   # 基于样本数
    PERFORMANCE_BASED = "performance_based"  # 基于性能
    HYBRID = "hybrid"               # 混合触发


@dataclass
class TrainingBatch:
    """训练批次"""
    batch_id: str
    features: np.ndarray
    targets: np.ndarray
    timestamps: List[datetime]
    metadata: Dict[str, Any]
    quality_score: float
    sample_count: int


@dataclass
class TrainingResult:
    """训练结果"""
    batch_id: str
    success: bool
    training_time: float
    loss_before: float
    loss_after: float
    accuracy_before: float
    accuracy_after: float
    samples_processed: int
    error_message: Optional[str] = None


class IncrementalTrainer:
    """增量训练器"""
    
    def __init__(self, config: Dict = None):
        """
        初始化增量训练器
        
        Args:
            config: 配置参数字典
        """
        self.logger = logging.getLogger('cryptoMlStrategy.online_learning.incremental_trainer')
        
        # 默认配置
        self.config = {
            'training': {
                'mode': 'adaptive',
                'batch_size': 32,
                'max_batch_size': 128,
                'min_batch_size': 8,
                'learning_rate': 0.001,
                'momentum': 0.9,
                'weight_decay': 1e-4,
                'gradient_clip': 1.0
            },
            'triggers': {
                'primary_trigger': 'hybrid',
                'time_interval_seconds': 300,      # 5分钟
                'sample_threshold': 100,
                'performance_threshold': 0.05,     # 5%性能下降
                'quality_threshold': 0.8
            },
            'data_processing': {
                'enable_quality_check': True,
                'feature_scaling': True,
                'outlier_removal': True,
                'missing_value_handling': 'interpolate',
                'data_buffer_size': 1000
            },
            'performance': {
                'enable_parallel_processing': False,
                'max_workers': 4,
                'training_timeout_seconds': 300,
                'memory_limit_mb': 2000
            },
            'integration': {
                'use_multi_timeframe_data': True,
                'use_technical_indicators': True,
                'use_quality_checker': True,
                'feature_fusion_method': 'weighted_average'
            }
        }
        
        # 更新用户配置
        if config:
            self._deep_update(self.config, config)
        
        # 数据缓冲区
        self.data_buffer = deque(maxlen=self.config['data_processing']['data_buffer_size'])
        self.feature_buffer = deque(maxlen=self.config['data_processing']['data_buffer_size'])
        self.target_buffer = deque(maxlen=self.config['data_processing']['data_buffer_size'])
        
        # 训练状态
        self.training_active = False
        self.current_model = None
        self.last_training_time = None
        self.last_performance_check = None
        
        # 批次管理
        self.pending_batches = queue.Queue()
        self.training_history = deque(maxlen=1000)
        
        # 性能监控
        self.performance_metrics = {
            'current_loss': 0.0,
            'current_accuracy': 0.0,
            'training_samples': 0,
            'last_update_time': None
        }
        
        # 统计信息
        self.stats = {
            'total_batches_processed': 0,
            'successful_updates': 0,
            'failed_updates': 0,
            'total_samples_processed': 0,
            'average_training_time': 0.0,
            'last_training_result': None
        }
        
        # 线程管理
        self.trainer_lock = threading.RLock()
        self.training_executor = ThreadPoolExecutor(
            max_workers=self.config['performance']['max_workers']
        )
        
        # 启动训练循环
        self._start_training_loop()
        
        self.logger.info("增量训练器初始化完成")
    
    def _deep_update(self, base_dict: Dict, update_dict: Dict):
        """深度更新字典"""
        for key, value in update_dict.items():
            if key in base_dict and isinstance(base_dict[key], dict) and isinstance(value, dict):
                self._deep_update(base_dict[key], value)
            else:
                base_dict[key] = value
    
    def set_model(self, model: Any):
        """
        设置要训练的模型
        
        Args:
            model: 模型对象
        """
        try:
            with self.trainer_lock:
                self.current_model = model
                self.logger.info("训练模型已设置")
                
        except Exception as e:
            self.logger.error(f"设置模型失败: {e}")
    
    def add_training_data(self, features: np.ndarray, targets: np.ndarray,
                         timestamps: List[datetime] = None,
                         metadata: Dict[str, Any] = None) -> bool:
        """
        添加训练数据
        
        Args:
            features: 特征数据
            targets: 目标数据
            timestamps: 时间戳列表
            metadata: 元数据
            
        Returns:
            是否添加成功
        """
        try:
            if features is None or targets is None:
                return False
            
            if len(features) != len(targets):
                self.logger.error("特征和目标数据长度不匹配")
                return False
            
            # 数据质量检查
            if self.config['data_processing']['enable_quality_check']:
                if not self._check_data_quality(features, targets):
                    self.logger.warning("数据质量检查未通过，跳过此批数据")
                    return False
            
            # 数据预处理
            processed_features = self._preprocess_features(features)
            processed_targets = self._preprocess_targets(targets)
            
            # 添加到缓冲区
            with self.trainer_lock:
                for i in range(len(processed_features)):
                    data_point = {
                        'features': processed_features[i],
                        'target': processed_targets[i],
                        'timestamp': timestamps[i] if timestamps else datetime.now(),
                        'metadata': metadata or {}
                    }
                    self.data_buffer.append(data_point)
                    self.feature_buffer.append(processed_features[i])
                    self.target_buffer.append(processed_targets[i])
            
            # 检查是否需要触发训练
            self._check_training_triggers()
            
            return True
            
        except Exception as e:
            self.logger.error(f"添加训练数据失败: {e}")
            return False
    
    def _check_data_quality(self, features: np.ndarray, targets: np.ndarray) -> bool:
        """检查数据质量"""
        try:
            # 检查NaN值
            if np.isnan(features).any() or np.isnan(targets).any():
                return False
            
            # 检查无穷值
            if np.isinf(features).any() or np.isinf(targets).any():
                return False
            
            # 检查数据范围
            if np.abs(features).max() > 1e6 or np.abs(targets).max() > 1e6:
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"数据质量检查失败: {e}")
            return False
    
    def _preprocess_features(self, features: np.ndarray) -> np.ndarray:
        """预处理特征数据"""
        try:
            processed = features.copy()
            
            # 特征缩放
            if self.config['data_processing']['feature_scaling']:
                # 简单的标准化
                mean = np.mean(processed, axis=0)
                std = np.std(processed, axis=0)
                std[std == 0] = 1  # 避免除零
                processed = (processed - mean) / std
            
            # 异常值处理
            if self.config['data_processing']['outlier_removal']:
                # 使用IQR方法移除异常值
                Q1 = np.percentile(processed, 25, axis=0)
                Q3 = np.percentile(processed, 75, axis=0)
                IQR = Q3 - Q1
                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR
                
                # 将异常值限制在合理范围内
                processed = np.clip(processed, lower_bound, upper_bound)
            
            return processed
            
        except Exception as e:
            self.logger.error(f"特征预处理失败: {e}")
            return features
    
    def _preprocess_targets(self, targets: np.ndarray) -> np.ndarray:
        """预处理目标数据"""
        try:
            processed = targets.copy()
            
            # 处理缺失值
            if self.config['data_processing']['missing_value_handling'] == 'interpolate':
                # 简单的线性插值
                mask = ~np.isnan(processed)
                if mask.any():
                    processed[~mask] = np.interp(
                        np.where(~mask)[0],
                        np.where(mask)[0],
                        processed[mask]
                    )
            
            return processed
            
        except Exception as e:
            self.logger.error(f"目标预处理失败: {e}")
            return targets
    
    def _check_training_triggers(self):
        """检查训练触发条件"""
        try:
            trigger_type = self.config['triggers']['primary_trigger']
            should_trigger = False
            
            if trigger_type == 'time_based':
                should_trigger = self._check_time_trigger()
            elif trigger_type == 'sample_based':
                should_trigger = self._check_sample_trigger()
            elif trigger_type == 'performance_based':
                should_trigger = self._check_performance_trigger()
            elif trigger_type == 'hybrid':
                should_trigger = (self._check_time_trigger() or 
                                self._check_sample_trigger() or 
                                self._check_performance_trigger())
            
            if should_trigger:
                self._trigger_training()
                
        except Exception as e:
            self.logger.error(f"检查训练触发条件失败: {e}")
    
    def _check_time_trigger(self) -> bool:
        """检查时间触发条件"""
        if self.last_training_time is None:
            return True
        
        time_interval = self.config['triggers']['time_interval_seconds']
        elapsed = (datetime.now() - self.last_training_time).total_seconds()
        return elapsed >= time_interval
    
    def _check_sample_trigger(self) -> bool:
        """检查样本数触发条件"""
        sample_threshold = self.config['triggers']['sample_threshold']
        return len(self.data_buffer) >= sample_threshold
    
    def _check_performance_trigger(self) -> bool:
        """检查性能触发条件"""
        # 简化的性能检查
        if self.last_performance_check is None:
            return False
        
        # 这里可以实现更复杂的性能下降检测逻辑
        return False
    
    def _trigger_training(self):
        """触发训练"""
        try:
            if not self.current_model or len(self.data_buffer) == 0:
                return
            
            # 创建训练批次
            batch = self._create_training_batch()
            if batch:
                # 添加到待处理队列
                self.pending_batches.put(batch)
                self.logger.info(f"触发训练，批次ID: {batch.batch_id}")
                
        except Exception as e:
            self.logger.error(f"触发训练失败: {e}")
    
    def _create_training_batch(self) -> Optional[TrainingBatch]:
        """创建训练批次"""
        try:
            with self.trainer_lock:
                if len(self.data_buffer) == 0:
                    return None
                
                # 确定批次大小
                batch_size = min(
                    len(self.data_buffer),
                    self.config['training']['batch_size']
                )
                
                # 提取数据
                batch_data = []
                for _ in range(batch_size):
                    if self.data_buffer:
                        batch_data.append(self.data_buffer.popleft())
                
                if not batch_data:
                    return None
                
                # 构建特征和目标数组
                features = np.array([item['features'] for item in batch_data])
                targets = np.array([item['target'] for item in batch_data])
                timestamps = [item['timestamp'] for item in batch_data]
                
                # 计算质量分数
                quality_score = self._calculate_batch_quality(features, targets)
                
                # 生成批次ID
                batch_id = f"batch_{int(datetime.now().timestamp())}_{len(batch_data)}"
                
                batch = TrainingBatch(
                    batch_id=batch_id,
                    features=features,
                    targets=targets,
                    timestamps=timestamps,
                    metadata={'creation_time': datetime.now()},
                    quality_score=quality_score,
                    sample_count=len(batch_data)
                )
                
                return batch
                
        except Exception as e:
            self.logger.error(f"创建训练批次失败: {e}")
            return None
    
    def _calculate_batch_quality(self, features: np.ndarray, targets: np.ndarray) -> float:
        """计算批次质量分数"""
        try:
            quality_score = 1.0
            
            # 检查数据完整性
            if np.isnan(features).any() or np.isnan(targets).any():
                quality_score *= 0.5
            
            # 检查数据方差
            feature_std = np.std(features, axis=0)
            if np.any(feature_std < 1e-6):  # 方差过小
                quality_score *= 0.8
            
            # 检查目标分布
            target_std = np.std(targets)
            if target_std < 1e-6:  # 目标值过于单一
                quality_score *= 0.7
            
            return max(0.0, min(1.0, quality_score))
            
        except Exception as e:
            self.logger.error(f"计算批次质量失败: {e}")
            return 0.5
    
    def _start_training_loop(self):
        """启动训练循环"""
        try:
            self.training_active = True
            training_thread = threading.Thread(target=self._training_loop, daemon=True)
            training_thread.start()
            self.logger.info("训练循环已启动")
            
        except Exception as e:
            self.logger.error(f"启动训练循环失败: {e}")
    
    def _training_loop(self):
        """训练循环"""
        while self.training_active:
            try:
                # 等待训练批次
                try:
                    batch = self.pending_batches.get(timeout=1.0)
                except queue.Empty:
                    continue
                
                # 执行训练
                result = self._train_batch(batch)
                
                # 记录结果
                if result:
                    self.training_history.append(result)
                    self._update_performance_metrics(result)
                    self.stats['last_training_result'] = result
                
                # 标记任务完成
                self.pending_batches.task_done()
                
            except Exception as e:
                self.logger.error(f"训练循环错误: {e}")
                time.sleep(1)
    
    def _train_batch(self, batch: TrainingBatch) -> Optional[TrainingResult]:
        """训练单个批次"""
        try:
            if not self.current_model:
                return None
            
            start_time = time.time()
            
            # 记录训练前性能
            loss_before = self._evaluate_model(batch.features, batch.targets)
            accuracy_before = self._calculate_accuracy(batch.features, batch.targets)
            
            # 执行训练
            success = self._perform_model_update(batch)
            
            # 记录训练后性能
            loss_after = self._evaluate_model(batch.features, batch.targets)
            accuracy_after = self._calculate_accuracy(batch.features, batch.targets)
            
            training_time = time.time() - start_time
            
            # 创建训练结果
            result = TrainingResult(
                batch_id=batch.batch_id,
                success=success,
                training_time=training_time,
                loss_before=loss_before,
                loss_after=loss_after,
                accuracy_before=accuracy_before,
                accuracy_after=accuracy_after,
                samples_processed=batch.sample_count
            )
            
            # 更新统计
            self.stats['total_batches_processed'] += 1
            self.stats['total_samples_processed'] += batch.sample_count
            
            if success:
                self.stats['successful_updates'] += 1
            else:
                self.stats['failed_updates'] += 1
            
            # 更新平均训练时间
            total_batches = self.stats['total_batches_processed']
            current_avg = self.stats['average_training_time']
            self.stats['average_training_time'] = (
                (current_avg * (total_batches - 1) + training_time) / total_batches
            )
            
            self.last_training_time = datetime.now()
            
            self.logger.info(f"批次训练完成: {batch.batch_id}, 成功: {success}, 耗时: {training_time:.3f}s")
            return result
            
        except Exception as e:
            self.logger.error(f"训练批次失败: {e}")
            return TrainingResult(
                batch_id=batch.batch_id,
                success=False,
                training_time=0.0,
                loss_before=0.0,
                loss_after=0.0,
                accuracy_before=0.0,
                accuracy_after=0.0,
                samples_processed=0,
                error_message=str(e)
            )
    
    def _perform_model_update(self, batch: TrainingBatch) -> bool:
        """执行模型更新"""
        try:
            # 这里实现具体的模型更新逻辑
            # 根据模型类型调用相应的训练方法
            
            # 示例：如果模型有partial_fit方法（增量学习）
            if hasattr(self.current_model, 'partial_fit'):
                self.current_model.partial_fit(batch.features, batch.targets)
                return True
            
            # 示例：如果模型有fit方法
            elif hasattr(self.current_model, 'fit'):
                self.current_model.fit(batch.features, batch.targets)
                return True
            
            else:
                self.logger.error("模型不支持增量学习")
                return False
                
        except Exception as e:
            self.logger.error(f"模型更新失败: {e}")
            return False
    
    def _evaluate_model(self, features: np.ndarray, targets: np.ndarray) -> float:
        """评估模型损失"""
        try:
            if not self.current_model or not hasattr(self.current_model, 'predict'):
                return 0.0
            
            predictions = self.current_model.predict(features)
            
            # 计算均方误差
            mse = np.mean((predictions - targets) ** 2)
            return float(mse)
            
        except Exception as e:
            self.logger.error(f"模型评估失败: {e}")
            return 0.0
    
    def _calculate_accuracy(self, features: np.ndarray, targets: np.ndarray) -> float:
        """计算模型准确率"""
        try:
            if not self.current_model or not hasattr(self.current_model, 'predict'):
                return 0.0
            
            predictions = self.current_model.predict(features)
            
            # 对于回归问题，计算相对准确率
            relative_errors = np.abs((predictions - targets) / (targets + 1e-8))
            accuracy = 1.0 - np.mean(relative_errors)
            
            return max(0.0, min(1.0, float(accuracy)))
            
        except Exception as e:
            self.logger.error(f"准确率计算失败: {e}")
            return 0.0
    
    def _update_performance_metrics(self, result: TrainingResult):
        """更新性能指标"""
        try:
            self.performance_metrics['current_loss'] = result.loss_after
            self.performance_metrics['current_accuracy'] = result.accuracy_after
            self.performance_metrics['training_samples'] += result.samples_processed
            self.performance_metrics['last_update_time'] = datetime.now()
            
        except Exception as e:
            self.logger.error(f"更新性能指标失败: {e}")
    
    def get_training_statistics(self) -> Dict[str, Any]:
        """获取训练统计信息"""
        try:
            with self.trainer_lock:
                return {
                    'training_active': self.training_active,
                    'current_model_set': self.current_model is not None,
                    'data_buffer_size': len(self.data_buffer),
                    'pending_batches': self.pending_batches.qsize(),
                    'last_training_time': self.last_training_time.isoformat() if self.last_training_time else None,
                    'performance_metrics': self.performance_metrics.copy(),
                    'stats': self.stats.copy(),
                    'config': self.config,
                    'training_history_length': len(self.training_history)
                }
                
        except Exception as e:
            self.logger.error(f"获取训练统计失败: {e}")
            return {}
    
    def stop_training(self):
        """停止训练"""
        try:
            self.training_active = False
            self.training_executor.shutdown(wait=True)
            self.logger.info("增量训练已停止")
            
        except Exception as e:
            self.logger.error(f"停止训练失败: {e}")
    
    def force_training_update(self) -> bool:
        """强制触发训练更新"""
        try:
            if len(self.data_buffer) > 0:
                self._trigger_training()
                return True
            return False
            
        except Exception as e:
            self.logger.error(f"强制训练更新失败: {e}")
            return False