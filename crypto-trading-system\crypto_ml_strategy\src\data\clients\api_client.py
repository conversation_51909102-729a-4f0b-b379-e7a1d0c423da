"""
Java模块API客户端

实现与Java后端模块的HTTP API通信，支持认证、重试、
超时处理和数据格式转换等功能。

Author: Crypto ML Strategy Team
Date: 2024-12-19
"""

import asyncio
import aiohttp
import json
import time
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timedelta
import logging
from dataclasses import dataclass
import pandas as pd
from urllib.parse import urljoin, urlencode

from data.data_config import APIConfig, DataSourceConfig, DataSourceType

logger = logging.getLogger(__name__)


@dataclass
class APIResponse:
    """API响应数据类"""
    status_code: int
    data: Any
    headers: Dict[str, str]
    response_time: float
    success: bool
    error_message: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'status_code': self.status_code,
            'data': self.data,
            'headers': self.headers,
            'response_time': self.response_time,
            'success': self.success,
            'error_message': self.error_message
        }


class JavaAPIClient:
    """Java API客户端类"""
    
    def __init__(self, config: APIConfig):
        """
        初始化API客户端
        
        Args:
            config: API配置
        """
        self.config = config
        self.session: Optional[aiohttp.ClientSession] = None
        self.rate_limiter = None
        
        # 统计信息
        self.stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'total_response_time': 0.0,
            'last_request_time': None
        }
        
        # 初始化速率限制器
        if self.config.rate_limit:
            self._init_rate_limiter()
        
        logger.info("Java API client initialized")
    
    def _init_rate_limiter(self) -> None:
        """初始化速率限制器"""
        try:
            self.rate_limiter = {
                'requests': [],
                'limit': self.config.rate_limit,
                'window': 1.0  # 1秒窗口
            }
        except Exception as e:
            logger.error(f"Error initializing rate limiter: {e}")
    
    async def _check_rate_limit(self) -> None:
        """检查速率限制"""
        try:
            if not self.rate_limiter:
                return
            
            now = time.time()
            window_start = now - self.rate_limiter['window']
            
            # 清理过期的请求记录
            self.rate_limiter['requests'] = [
                req_time for req_time in self.rate_limiter['requests']
                if req_time > window_start
            ]
            
            # 检查是否超过限制
            if len(self.rate_limiter['requests']) >= self.rate_limiter['limit']:
                sleep_time = self.rate_limiter['requests'][0] + self.rate_limiter['window'] - now
                if sleep_time > 0:
                    await asyncio.sleep(sleep_time)
            
            # 记录当前请求
            self.rate_limiter['requests'].append(now)
            
        except Exception as e:
            logger.error(f"Error checking rate limit: {e}")
    
    async def _get_session(self) -> aiohttp.ClientSession:
        """获取HTTP会话"""
        try:
            if self.session is None or self.session.closed:
                timeout = aiohttp.ClientTimeout(total=self.config.timeout)
                
                # 设置默认头部
                headers = {
                    'Content-Type': 'application/json',
                    'User-Agent': 'CryptoMLStrategy/1.0',
                    **self.config.headers
                }
                
                # 添加认证头部
                if self.config.api_key:
                    if self.config.auth_type == "bearer":
                        headers['Authorization'] = f"Bearer {self.config.api_key}"
                    elif self.config.auth_type == "api_key":
                        headers['X-API-Key'] = self.config.api_key
                
                self.session = aiohttp.ClientSession(
                    timeout=timeout,
                    headers=headers
                )
            
            return self.session
            
        except Exception as e:
            logger.error(f"Error getting session: {e}")
            raise
    
    async def _make_request(
        self,
        method: str,
        endpoint: str,
        params: Optional[Dict[str, Any]] = None,
        data: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None
    ) -> APIResponse:
        """
        发送HTTP请求
        
        Args:
            method: HTTP方法
            endpoint: API端点
            params: 查询参数
            data: 请求数据
            headers: 额外头部
            
        Returns:
            API响应
        """
        start_time = time.time()
        
        try:
            # 检查速率限制
            await self._check_rate_limit()
            
            # 构建URL
            url = urljoin(self.config.base_url, endpoint)
            
            # 获取会话
            session = await self._get_session()
            
            # 合并头部
            request_headers = {}
            if headers:
                request_headers.update(headers)
            
            # 发送请求
            async with session.request(
                method=method,
                url=url,
                params=params,
                json=data,
                headers=request_headers
            ) as response:
                
                response_time = time.time() - start_time
                response_headers = dict(response.headers)
                
                # 读取响应数据
                if response.content_type == 'application/json':
                    response_data = await response.json()
                else:
                    response_data = await response.text()
                
                # 更新统计信息
                self.stats['total_requests'] += 1
                self.stats['total_response_time'] += response_time
                self.stats['last_request_time'] = datetime.now()
                
                # 创建响应对象
                api_response = APIResponse(
                    status_code=response.status,
                    data=response_data,
                    headers=response_headers,
                    response_time=response_time,
                    success=200 <= response.status < 300
                )
                
                if api_response.success:
                    self.stats['successful_requests'] += 1
                    logger.debug(f"API request successful: {method} {endpoint}")
                else:
                    self.stats['failed_requests'] += 1
                    api_response.error_message = f"HTTP {response.status}: {response_data}"
                    logger.warning(f"API request failed: {method} {endpoint} - {api_response.error_message}")
                
                return api_response
                
        except Exception as e:
            response_time = time.time() - start_time
            self.stats['total_requests'] += 1
            self.stats['failed_requests'] += 1
            self.stats['total_response_time'] += response_time
            
            error_message = str(e)
            logger.error(f"API request error: {method} {endpoint} - {error_message}")
            
            return APIResponse(
                status_code=0,
                data=None,
                headers={},
                response_time=response_time,
                success=False,
                error_message=error_message
            )
    
    async def _retry_request(
        self,
        method: str,
        endpoint: str,
        params: Optional[Dict[str, Any]] = None,
        data: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None
    ) -> APIResponse:
        """
        带重试的请求
        
        Args:
            method: HTTP方法
            endpoint: API端点
            params: 查询参数
            data: 请求数据
            headers: 额外头部
            
        Returns:
            API响应
        """
        last_response = None
        
        for attempt in range(self.config.max_retries + 1):
            try:
                response = await self._make_request(method, endpoint, params, data, headers)
                
                if response.success:
                    return response
                
                last_response = response
                
                # 如果是客户端错误（4xx），不重试
                if 400 <= response.status_code < 500:
                    break
                
                # 如果不是最后一次尝试，等待后重试
                if attempt < self.config.max_retries:
                    delay = self.config.retry_delay * (2 ** attempt)  # 指数退避
                    logger.info(f"Retrying request in {delay}s (attempt {attempt + 1}/{self.config.max_retries})")
                    await asyncio.sleep(delay)
                
            except Exception as e:
                logger.error(f"Request attempt {attempt + 1} failed: {e}")
                if attempt == self.config.max_retries:
                    return APIResponse(
                        status_code=0,
                        data=None,
                        headers={},
                        response_time=0.0,
                        success=False,
                        error_message=str(e)
                    )
        
        return last_response or APIResponse(
            status_code=0,
            data=None,
            headers={},
            response_time=0.0,
            success=False,
            error_message="All retry attempts failed"
        )
    
    async def get_historical_data(
        self,
        symbol: str,
        start_time: datetime,
        end_time: datetime,
        timeframe: str = "1h",
        limit: Optional[int] = None
    ) -> APIResponse:
        """
        获取历史数据
        
        Args:
            symbol: 交易对符号
            start_time: 开始时间
            end_time: 结束时间
            timeframe: 时间框架
            limit: 数据条数限制
            
        Returns:
            API响应
        """
        try:
            params = {
                'symbol': symbol,
                'start_time': start_time.isoformat(),
                'end_time': end_time.isoformat(),
                'timeframe': timeframe
            }
            
            if limit:
                params['limit'] = limit
            
            response = await self._retry_request('GET', '/data/historical', params=params)
            
            # 转换数据格式
            if response.success and response.data:
                try:
                    # 假设返回的是JSON格式的OHLCV数据
                    df = pd.DataFrame(response.data)
                    if not df.empty and 'timestamp' in df.columns:
                        df['timestamp'] = pd.to_datetime(df['timestamp'])
                        df.set_index('timestamp', inplace=True)
                    response.data = df
                except Exception as e:
                    logger.warning(f"Error converting historical data to DataFrame: {e}")
            
            return response
            
        except Exception as e:
            logger.error(f"Error getting historical data: {e}")
            return APIResponse(
                status_code=0,
                data=None,
                headers={},
                response_time=0.0,
                success=False,
                error_message=str(e)
            )
    
    async def get_realtime_data(self, symbol: str) -> APIResponse:
        """
        获取实时数据
        
        Args:
            symbol: 交易对符号
            
        Returns:
            API响应
        """
        try:
            params = {'symbol': symbol}
            return await self._retry_request('GET', '/data/realtime', params=params)
            
        except Exception as e:
            logger.error(f"Error getting realtime data: {e}")
            return APIResponse(
                status_code=0,
                data=None,
                headers={},
                response_time=0.0,
                success=False,
                error_message=str(e)
            )
    
    async def get_market_data(self, symbols: Optional[List[str]] = None) -> APIResponse:
        """
        获取市场数据
        
        Args:
            symbols: 交易对符号列表
            
        Returns:
            API响应
        """
        try:
            params = {}
            if symbols:
                params['symbols'] = ','.join(symbols)
            
            return await self._retry_request('GET', '/data/market', params=params)
            
        except Exception as e:
            logger.error(f"Error getting market data: {e}")
            return APIResponse(
                status_code=0,
                data=None,
                headers={},
                response_time=0.0,
                success=False,
                error_message=str(e)
            )
    
    async def get_portfolio_data(self, account_id: Optional[str] = None) -> APIResponse:
        """
        获取投资组合数据
        
        Args:
            account_id: 账户ID
            
        Returns:
            API响应
        """
        try:
            params = {}
            if account_id:
                params['account_id'] = account_id
            
            return await self._retry_request('GET', '/data/portfolio', params=params)
            
        except Exception as e:
            logger.error(f"Error getting portfolio data: {e}")
            return APIResponse(
                status_code=0,
                data=None,
                headers={},
                response_time=0.0,
                success=False,
                error_message=str(e)
            )
    
    async def send_trading_signal(self, signal_data: Dict[str, Any]) -> APIResponse:
        """
        发送交易信号
        
        Args:
            signal_data: 信号数据
            
        Returns:
            API响应
        """
        try:
            return await self._retry_request('POST', '/signals/trading', data=signal_data)
            
        except Exception as e:
            logger.error(f"Error sending trading signal: {e}")
            return APIResponse(
                status_code=0,
                data=None,
                headers={},
                response_time=0.0,
                success=False,
                error_message=str(e)
            )
    
    async def health_check(self) -> APIResponse:
        """
        健康检查
        
        Returns:
            API响应
        """
        try:
            return await self._retry_request('GET', '/health')
            
        except Exception as e:
            logger.error(f"Error in health check: {e}")
            return APIResponse(
                status_code=0,
                data=None,
                headers={},
                response_time=0.0,
                success=False,
                error_message=str(e)
            )
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取客户端统计信息"""
        try:
            avg_response_time = 0.0
            if self.stats['total_requests'] > 0:
                avg_response_time = self.stats['total_response_time'] / self.stats['total_requests']
            
            success_rate = 0.0
            if self.stats['total_requests'] > 0:
                success_rate = self.stats['successful_requests'] / self.stats['total_requests']
            
            return {
                'total_requests': self.stats['total_requests'],
                'successful_requests': self.stats['successful_requests'],
                'failed_requests': self.stats['failed_requests'],
                'success_rate': success_rate,
                'average_response_time': avg_response_time,
                'last_request_time': self.stats['last_request_time'].isoformat() if self.stats['last_request_time'] else None
            }
            
        except Exception as e:
            logger.error(f"Error getting statistics: {e}")
            return {'error': str(e)}
    
    async def close(self) -> None:
        """关闭客户端"""
        try:
            if self.session and not self.session.closed:
                await self.session.close()
            logger.info("Java API client closed")
        except Exception as e:
            logger.error(f"Error closing API client: {e}")
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.close()
