"""
启动优化测试套件

提供crypto_ml_strategy项目的启动优化系统全面测试功能，包括
单元测试、集成测试、性能测试和端到端测试。

Author: Crypto ML Strategy Team
Date: 2024-12-20
Version: 1.0.0
"""

import asyncio
import pytest
import time
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional
from unittest.mock import Mock, AsyncMock, patch
from loguru import logger

from .startup_sequence_core import (
    StartupSequenceManager, StartupTask, StartupPhase, StartupTaskPriority,
    StartupTaskStatus, global_startup_manager
)
from .startup_dependency_validator import (
    DependencyValidator, ExternalServiceChecker, FileSystemValidator,
    ResourceAvailabilityChecker, global_dependency_validator
)
from .startup_resource_preloader import (
    ResourcePreloadManager, ModelPreloader, CachePrewarmer,
    ConfigurationLoader, global_resource_preloader
)
from .startup_progress_monitor import (
    StartupProgressMonitor, ProgressEvent, ProgressEventType,
    global_startup_monitor
)
from .startup_optimizer import (
    StartupOptimizer, OptimizationConfig, OptimizationStrategy,
    global_startup_optimizer
)
from .startup_health_checker import (
    StartupHealthChecker, SystemReadinessCheck, ServiceAvailabilityCheck,
    PerformanceValidationCheck, global_startup_health_checker
)
from .startup_config_manager import (
    StartupConfigManager, StartupConfig, global_startup_config_manager
)


class TestStartupSequenceCore:
    """启动序列核心测试类"""
    
    def test_startup_task_creation(self):
        """测试启动任务创建"""
        async def dummy_task():
            return "success"
        
        task = StartupTask(
            name="test_task",
            phase=StartupPhase.CORE_INITIALIZATION,
            priority=StartupTaskPriority.HIGH,
            task_func=dummy_task,
            dependencies={"dep1", "dep2"},
            timeout=30.0
        )
        
        assert task.name == "test_task"
        assert task.phase == StartupPhase.CORE_INITIALIZATION
        assert task.priority == StartupTaskPriority.HIGH
        assert task.dependencies == {"dep1", "dep2"}
        assert task.timeout == 30.0
        assert task.status == StartupTaskStatus.PENDING
    
    def test_startup_task_ready_check(self):
        """测试启动任务就绪检查"""
        task = StartupTask(
            name="test_task",
            phase=StartupPhase.CORE_INITIALIZATION,
            priority=StartupTaskPriority.HIGH,
            task_func=lambda: None,
            dependencies={"dep1", "dep2"}
        )
        
        # 依赖未满足
        assert not task.is_ready_to_run({"dep1"})
        
        # 依赖已满足
        assert task.is_ready_to_run({"dep1", "dep2"})
        assert task.is_ready_to_run({"dep1", "dep2", "dep3"})
    
    @pytest.mark.asyncio
    async def test_startup_sequence_manager(self):
        """测试启动序列管理器"""
        manager = StartupSequenceManager()
        
        # 创建测试任务
        async def test_task():
            await asyncio.sleep(0.1)
            return "completed"
        
        task = StartupTask(
            name="test_task",
            phase=StartupPhase.PRE_STARTUP,
            priority=StartupTaskPriority.HIGH,
            task_func=test_task
        )
        
        # 注册任务
        manager.register_task(task)
        assert "test_task" in manager.tasks
        
        # 获取进度
        progress = manager.get_progress()
        assert progress["total_tasks"] == 1
        assert progress["completed_tasks"] == 0


class TestDependencyValidator:
    """依赖验证器测试类"""
    
    def test_external_service_checker_creation(self):
        """测试外部服务检查器创建"""
        checker = ExternalServiceChecker(
            service_name="test_service",
            host="localhost",
            port=8080,
            timeout=5.0
        )
        
        assert checker.service_name == "test_service"
        assert checker.host == "localhost"
        assert checker.port == 8080
        assert checker.timeout == 5.0
        assert checker.get_name() == "test_service"
        assert checker.get_timeout() == 5.0
    
    def test_filesystem_validator_creation(self):
        """测试文件系统验证器创建"""
        validator = FileSystemValidator(
            name="test_filesystem",
            required_paths=["/tmp"],
            required_files=["test.txt"],
            required_directories=["test_dir"]
        )
        
        assert validator.name == "test_filesystem"
        assert validator.required_paths == ["/tmp"]
        assert validator.required_files == ["test.txt"]
        assert validator.required_directories == ["test_dir"]
    
    @pytest.mark.asyncio
    async def test_dependency_validator(self):
        """测试依赖验证器"""
        validator = DependencyValidator()
        
        # 添加检查器
        validator.add_resource_checker()
        assert len(validator.checkers) == 1
        
        # 获取验证摘要
        summary = validator.get_validation_summary()
        assert summary["status"] == "no_validation_performed"


class TestResourcePreloader:
    """资源预加载器测试类"""
    
    def test_model_preloader_creation(self):
        """测试模型预加载器创建"""
        preloader = ModelPreloader(
            model_name="test_model",
            model_path="/path/to/model.pkl",
            model_type="pickle",
            priority=100
        )
        
        assert preloader.model_name == "test_model"
        assert preloader.model_path == "/path/to/model.pkl"
        assert preloader.model_type == "pickle"
        assert preloader.get_name() == "test_model"
        assert preloader.get_priority() == 100
    
    def test_cache_prewarmer_creation(self):
        """测试缓存预热器创建"""
        def dummy_loader():
            return {"key1": "value1", "key2": "value2"}
        
        prewarmer = CachePrewarmer(
            cache_name="test_cache",
            cache_data_loader=dummy_loader,
            priority=200,
            max_cache_size=1000
        )
        
        assert prewarmer.cache_name == "test_cache"
        assert prewarmer.cache_data_loader == dummy_loader
        assert prewarmer.get_name() == "test_cache"
        assert prewarmer.get_priority() == 200
    
    @pytest.mark.asyncio
    async def test_resource_preload_manager(self):
        """测试资源预加载管理器"""
        manager = ResourcePreloadManager()
        
        # 添加预加载器
        def dummy_loader():
            return {"test": "data"}
        
        manager.add_cache_prewarmer("test_cache", dummy_loader)
        assert len(manager.preloaders) == 1
        
        # 获取预加载摘要
        summary = manager.get_preload_summary()
        assert summary["status"] == "no_preload_performed"


class TestProgressMonitor:
    """进度监控器测试类"""
    
    def test_progress_event_creation(self):
        """测试进度事件创建"""
        event = ProgressEvent(
            event_type=ProgressEventType.TASK_STARTED,
            timestamp=datetime.now(),
            phase=StartupPhase.CORE_INITIALIZATION,
            task_name="test_task",
            message="任务开始"
        )
        
        assert event.event_type == ProgressEventType.TASK_STARTED
        assert event.phase == StartupPhase.CORE_INITIALIZATION
        assert event.task_name == "test_task"
        assert event.message == "任务开始"
    
    def test_startup_metrics_properties(self):
        """测试启动指标属性"""
        from .startup_progress_monitor import StartupMetrics
        
        start_time = datetime.now() - timedelta(seconds=10)
        metrics = StartupMetrics(
            start_time=start_time,
            total_tasks=10,
            completed_tasks=7,
            failed_tasks=1
        )
        
        assert metrics.elapsed_time > 9  # 大约10秒
        assert metrics.progress_percentage == 70.0  # 7/10 * 100
        assert metrics.success_rate == 87.5  # 7/8 * 100
    
    def test_startup_progress_monitor(self):
        """测试启动进度监控器"""
        monitor = StartupProgressMonitor()
        
        # 开始监控
        monitor.start_monitoring(total_tasks=5)
        assert monitor.metrics is not None
        assert monitor.metrics.total_tasks == 5
        assert monitor.monitoring is True


class TestStartupOptimizer:
    """启动优化器测试类"""
    
    def test_optimization_config_creation(self):
        """测试优化配置创建"""
        config = OptimizationConfig(
            strategy=OptimizationStrategy.ADAPTIVE,
            max_parallel_tasks=8,
            max_memory_usage_mb=4096,
            max_cpu_usage_percent=85.0
        )
        
        assert config.strategy == OptimizationStrategy.ADAPTIVE
        assert config.max_parallel_tasks == 8
        assert config.max_memory_usage_mb == 4096
        assert config.max_cpu_usage_percent == 85.0
    
    @pytest.mark.asyncio
    async def test_startup_optimizer(self):
        """测试启动优化器"""
        optimizer = StartupOptimizer()
        
        # 启动优化器
        await optimizer.start()
        
        # 创建测试任务
        tasks = [
            StartupTask(
                name=f"task_{i}",
                phase=StartupPhase.CORE_INITIALIZATION,
                priority=StartupTaskPriority.NORMAL,
                task_func=lambda: None
            )
            for i in range(3)
        ]
        
        # 优化任务执行顺序
        optimized_tasks = optimizer.optimize_task_execution_order(tasks)
        assert len(optimized_tasks) == 3
        
        # 停止优化器
        await optimizer.stop()


class TestHealthChecker:
    """健康检查器测试类"""
    
    def test_system_readiness_check_creation(self):
        """测试系统就绪检查创建"""
        check = SystemReadinessCheck(required_services=["service1", "service2"])
        
        assert check.required_services == ["service1", "service2"]
        assert check.get_name() == "system_readiness"
        assert check.get_timeout() == 10.0
        assert check.is_critical() is True
    
    def test_service_availability_check_creation(self):
        """测试服务可用性检查创建"""
        endpoints = {"service1": "localhost:8080", "service2": "localhost:9090"}
        check = ServiceAvailabilityCheck(endpoints)
        
        assert check.service_endpoints == endpoints
        assert check.get_name() == "service_availability"
        assert check.is_critical() is True
    
    @pytest.mark.asyncio
    async def test_startup_health_checker(self):
        """测试启动健康检查器"""
        checker = StartupHealthChecker()
        
        # 添加健康检查
        checker.add_system_readiness_check()
        assert len(checker.health_checks) == 1
        
        # 获取健康摘要
        summary = checker.get_health_summary()
        assert summary["status"] == "no_checks_performed"


class TestConfigManager:
    """配置管理器测试类"""
    
    def test_startup_config_creation(self):
        """测试启动配置创建"""
        config = StartupConfig()
        
        assert config.version == "1.0.0"
        assert config.enabled is True
        assert config.timing.max_startup_time_seconds == 10.0
        assert config.parallelization.max_parallel_tasks == 5
        assert config.optimization.strategy == "adaptive"
    
    def test_config_validation(self):
        """测试配置验证"""
        from .startup_config_manager import StartupConfigValidator
        
        config = StartupConfig()
        errors = StartupConfigValidator.validate_config(config)
        assert len(errors) == 0  # 默认配置应该有效
        
        # 测试无效配置
        config.timing.max_startup_time_seconds = -1
        errors = StartupConfigValidator.validate_config(config)
        assert len(errors) > 0
    
    def test_startup_config_manager(self):
        """测试启动配置管理器"""
        manager = StartupConfigManager()
        
        # 获取配置
        config = manager.get_config()
        assert config is not None
        assert isinstance(config, StartupConfig)
        
        # 获取配置摘要
        summary = manager.get_config_summary()
        assert "version" in summary
        assert "enabled" in summary


class StartupOptimizationTestRunner:
    """启动优化测试运行器"""
    
    def __init__(self):
        self.test_results: Dict[str, Any] = {}
        self.start_time: Optional[datetime] = None
        self.end_time: Optional[datetime] = None
    
    async def run_all_tests(self) -> Dict[str, Any]:
        """运行所有测试"""
        self.start_time = datetime.now()
        logger.info("开始运行启动优化测试套件")
        
        test_classes = [
            TestStartupSequenceCore,
            TestDependencyValidator,
            TestResourcePreloader,
            TestProgressMonitor,
            TestStartupOptimizer,
            TestHealthChecker,
            TestConfigManager
        ]
        
        total_tests = 0
        passed_tests = 0
        failed_tests = 0
        
        for test_class in test_classes:
            class_name = test_class.__name__
            logger.info(f"运行测试类: {class_name}")
            
            try:
                test_instance = test_class()
                class_results = await self._run_test_class(test_instance)
                
                self.test_results[class_name] = class_results
                total_tests += class_results["total"]
                passed_tests += class_results["passed"]
                failed_tests += class_results["failed"]
                
            except Exception as e:
                logger.error(f"测试类 {class_name} 运行失败: {e}")
                self.test_results[class_name] = {
                    "total": 0,
                    "passed": 0,
                    "failed": 1,
                    "error": str(e)
                }
                failed_tests += 1
        
        self.end_time = datetime.now()
        duration = (self.end_time - self.start_time).total_seconds()
        
        summary = {
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "failed_tests": failed_tests,
            "success_rate": passed_tests / total_tests if total_tests > 0 else 0,
            "duration_seconds": duration,
            "test_results": self.test_results
        }
        
        logger.info(f"启动优化测试完成: {passed_tests}/{total_tests} 通过")
        return summary
    
    async def _run_test_class(self, test_instance) -> Dict[str, Any]:
        """运行单个测试类"""
        methods = [method for method in dir(test_instance) if method.startswith('test_')]
        
        total = len(methods)
        passed = 0
        failed = 0
        errors = []
        
        for method_name in methods:
            try:
                method = getattr(test_instance, method_name)
                
                if asyncio.iscoroutinefunction(method):
                    await method()
                else:
                    method()
                
                passed += 1
                logger.debug(f"测试通过: {method_name}")
                
            except Exception as e:
                failed += 1
                error_msg = f"{method_name}: {str(e)}"
                errors.append(error_msg)
                logger.error(f"测试失败: {error_msg}")
        
        return {
            "total": total,
            "passed": passed,
            "failed": failed,
            "errors": errors
        }
    
    async def run_performance_tests(self) -> Dict[str, Any]:
        """运行性能测试"""
        logger.info("开始运行启动优化性能测试")
        
        performance_results = {}
        
        # 测试启动时间性能
        startup_time_result = await self._test_startup_time_performance()
        performance_results["startup_time"] = startup_time_result
        
        # 测试并行化性能
        parallelization_result = await self._test_parallelization_performance()
        performance_results["parallelization"] = parallelization_result
        
        # 测试资源使用性能
        resource_usage_result = await self._test_resource_usage_performance()
        performance_results["resource_usage"] = resource_usage_result
        
        return performance_results
    
    async def _test_startup_time_performance(self) -> Dict[str, Any]:
        """测试启动时间性能"""
        try:
            start_time = time.time()
            
            # 模拟启动序列
            manager = StartupSequenceManager()
            
            # 创建测试任务
            tasks = []
            for i in range(10):
                async def dummy_task():
                    await asyncio.sleep(0.1)
                    return f"task_{i}_completed"
                
                task = StartupTask(
                    name=f"perf_task_{i}",
                    phase=StartupPhase.CORE_INITIALIZATION,
                    priority=StartupTaskPriority.NORMAL,
                    task_func=dummy_task
                )
                tasks.append(task)
                manager.register_task(task)
            
            # 执行启动序列
            result = await manager.start_sequence()
            
            duration = time.time() - start_time
            
            return {
                "success": result.success,
                "duration": duration,
                "target_time": 10.0,
                "performance_rating": "excellent" if duration < 5.0 else "good" if duration < 10.0 else "poor",
                "total_tasks": len(tasks)
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "duration": 0
            }
    
    async def _test_parallelization_performance(self) -> Dict[str, Any]:
        """测试并行化性能"""
        try:
            # 测试串行执行
            start_time = time.time()
            for i in range(5):
                await asyncio.sleep(0.1)
            serial_duration = time.time() - start_time
            
            # 测试并行执行
            start_time = time.time()
            await asyncio.gather(*[asyncio.sleep(0.1) for _ in range(5)])
            parallel_duration = time.time() - start_time
            
            speedup = serial_duration / parallel_duration if parallel_duration > 0 else 0
            
            return {
                "serial_duration": serial_duration,
                "parallel_duration": parallel_duration,
                "speedup": speedup,
                "efficiency": speedup / 5,  # 理论最大加速比为5
                "performance_rating": "excellent" if speedup > 4 else "good" if speedup > 2 else "poor"
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _test_resource_usage_performance(self) -> Dict[str, Any]:
        """测试资源使用性能"""
        try:
            import psutil
            
            # 获取初始资源使用
            initial_memory = psutil.virtual_memory().used / (1024 * 1024)
            initial_cpu = psutil.cpu_percent(interval=1)
            
            # 模拟资源密集型操作
            data = []
            for i in range(1000):
                data.append([j for j in range(100)])
            
            # 获取峰值资源使用
            peak_memory = psutil.virtual_memory().used / (1024 * 1024)
            peak_cpu = psutil.cpu_percent(interval=1)
            
            memory_increase = peak_memory - initial_memory
            cpu_increase = peak_cpu - initial_cpu
            
            return {
                "initial_memory_mb": initial_memory,
                "peak_memory_mb": peak_memory,
                "memory_increase_mb": memory_increase,
                "initial_cpu_percent": initial_cpu,
                "peak_cpu_percent": peak_cpu,
                "cpu_increase_percent": cpu_increase,
                "memory_efficiency": "good" if memory_increase < 100 else "poor",
                "cpu_efficiency": "good" if cpu_increase < 50 else "poor"
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
    
    async def run_integration_tests(self) -> Dict[str, Any]:
        """运行集成测试"""
        logger.info("开始运行启动优化集成测试")
        
        try:
            # 测试完整启动流程
            integration_result = await self._test_full_startup_integration()
            
            return {
                "full_startup_integration": integration_result,
                "overall_success": integration_result.get("success", False)
            }
            
        except Exception as e:
            logger.error(f"集成测试失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _test_full_startup_integration(self) -> Dict[str, Any]:
        """测试完整启动集成"""
        try:
            # 初始化所有组件
            config_manager = StartupConfigManager()
            dependency_validator = DependencyValidator()
            resource_preloader = ResourcePreloadManager()
            progress_monitor = StartupProgressMonitor()
            optimizer = StartupOptimizer()
            health_checker = StartupHealthChecker()
            
            # 加载配置
            config = config_manager.get_config()
            
            # 添加依赖检查
            dependency_validator.add_resource_checker()
            
            # 添加资源预加载
            def dummy_cache_loader():
                return {"test": "data"}
            resource_preloader.add_cache_prewarmer("test_cache", dummy_cache_loader)
            
            # 添加健康检查
            health_checker.add_system_readiness_check()
            
            # 启动监控
            progress_monitor.start_monitoring(total_tasks=3)
            
            # 启动优化器
            await optimizer.start()
            
            # 执行依赖验证
            dependency_results = await dependency_validator.validate_all()
            
            # 执行资源预加载
            preload_results = await resource_preloader.preload_all()
            
            # 执行健康检查
            health_results = await health_checker.run_all_checks()
            
            # 停止组件
            await progress_monitor.stop_monitoring()
            await optimizer.stop()
            
            # 评估结果
            dependency_success = all(r.success for r in dependency_results.values())
            preload_success = all(r.success for r in preload_results.values())
            health_success = all(r.is_healthy() for r in health_results.values())
            
            overall_success = dependency_success and preload_success and health_success
            
            return {
                "success": overall_success,
                "dependency_validation": {
                    "success": dependency_success,
                    "total_checks": len(dependency_results),
                    "passed_checks": len([r for r in dependency_results.values() if r.success])
                },
                "resource_preloading": {
                    "success": preload_success,
                    "total_preloaders": len(preload_results),
                    "successful_preloaders": len([r for r in preload_results.values() if r.success])
                },
                "health_checking": {
                    "success": health_success,
                    "total_checks": len(health_results),
                    "healthy_checks": len([r for r in health_results.values() if r.is_healthy()])
                }
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }


# 全局测试运行器实例
global_startup_test_runner = StartupOptimizationTestRunner()