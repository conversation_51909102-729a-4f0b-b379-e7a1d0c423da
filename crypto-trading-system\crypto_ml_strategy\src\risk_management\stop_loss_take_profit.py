"""
止损止盈管理器模块

实现智能止损止盈策略，支持多种止损止盈类型，
包括固定百分比、ATR基础、追踪止损、技术指标等策略。

Author: Crypto ML Strategy Team
Date: 2024-12-19
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime
import logging

from .risk_config import RiskConfig, StopLossType, TakeProfitType, RiskEvent, RiskLevel

logger = logging.getLogger(__name__)


class StopLossTakeProfitManager:
    """止损止盈管理器类"""
    
    def __init__(self, config: RiskConfig):
        """
        初始化止损止盈管理器
        
        Args:
            config: 风险管理配置
        """
        self.config = config
        self.active_orders: Dict[str, Dict[str, Any]] = {}
        self.price_history: Dict[str, pd.DataFrame] = {}
        self.technical_indicators: Dict[str, Dict[str, float]] = {}
        
        logger.info("Stop loss take profit manager initialized")
    
    def update_price_data(self, symbol: str, price_data: pd.DataFrame) -> None:
        """
        更新价格数据
        
        Args:
            symbol: 交易对符号
            price_data: 价格数据
        """
        try:
            self.price_history[symbol] = price_data.copy()
            logger.debug(f"Updated price data for {symbol}")
            
        except Exception as e:
            logger.error(f"Error updating price data for {symbol}: {e}")
            raise
    
    def update_technical_indicators(self, symbol: str, indicators: Dict[str, float]) -> None:
        """
        更新技术指标数据
        
        Args:
            symbol: 交易对符号
            indicators: 技术指标字典
        """
        try:
            self.technical_indicators[symbol] = indicators.copy()
            logger.debug(f"Updated technical indicators for {symbol}")
            
        except Exception as e:
            logger.error(f"Error updating technical indicators for {symbol}: {e}")
            raise
    
    def calculate_stop_loss_price(
        self,
        symbol: str,
        entry_price: float,
        position_side: str,  # 'long' or 'short'
        market_data: Optional[Dict[str, Any]] = None
    ) -> float:
        """
        计算止损价格
        
        Args:
            symbol: 交易对符号
            entry_price: 入场价格
            position_side: 仓位方向
            market_data: 市场数据
            
        Returns:
            止损价格
        """
        try:
            if self.config.stop_loss_type == StopLossType.FIXED_PERCENTAGE:
                return self._calculate_fixed_percentage_stop_loss(entry_price, position_side)
            
            elif self.config.stop_loss_type == StopLossType.ATR_BASED:
                return self._calculate_atr_stop_loss(symbol, entry_price, position_side)
            
            elif self.config.stop_loss_type == StopLossType.TRAILING:
                return self._calculate_trailing_stop_loss(symbol, entry_price, position_side)
            
            elif self.config.stop_loss_type == StopLossType.TECHNICAL_INDICATOR:
                return self._calculate_technical_stop_loss(symbol, entry_price, position_side)
            
            elif self.config.stop_loss_type == StopLossType.ADAPTIVE:
                return self._calculate_adaptive_stop_loss(symbol, entry_price, position_side, market_data)
            
            else:
                return self._calculate_fixed_percentage_stop_loss(entry_price, position_side)
                
        except Exception as e:
            logger.error(f"Error calculating stop loss price for {symbol}: {e}")
            return self._calculate_fixed_percentage_stop_loss(entry_price, position_side)
    
    def calculate_take_profit_price(
        self,
        symbol: str,
        entry_price: float,
        position_side: str,
        stop_loss_price: Optional[float] = None,
        market_data: Optional[Dict[str, Any]] = None
    ) -> float:
        """
        计算止盈价格
        
        Args:
            symbol: 交易对符号
            entry_price: 入场价格
            position_side: 仓位方向
            stop_loss_price: 止损价格
            market_data: 市场数据
            
        Returns:
            止盈价格
        """
        try:
            if self.config.take_profit_type == TakeProfitType.FIXED_TARGET:
                return self._calculate_fixed_target_take_profit(entry_price, position_side)
            
            elif self.config.take_profit_type == TakeProfitType.PARTIAL_PROFIT:
                return self._calculate_partial_profit_take_profit(entry_price, position_side)
            
            elif self.config.take_profit_type == TakeProfitType.TRAILING:
                return self._calculate_trailing_take_profit(symbol, entry_price, position_side)
            
            elif self.config.take_profit_type == TakeProfitType.RISK_REWARD_RATIO:
                return self._calculate_risk_reward_take_profit(entry_price, position_side, stop_loss_price)
            
            elif self.config.take_profit_type == TakeProfitType.ADAPTIVE:
                return self._calculate_adaptive_take_profit(symbol, entry_price, position_side, stop_loss_price, market_data)
            
            else:
                return self._calculate_fixed_target_take_profit(entry_price, position_side)
                
        except Exception as e:
            logger.error(f"Error calculating take profit price for {symbol}: {e}")
            return self._calculate_fixed_target_take_profit(entry_price, position_side)
    
    def _calculate_fixed_percentage_stop_loss(self, entry_price: float, position_side: str) -> float:
        """计算固定百分比止损"""
        try:
            stop_loss_pct = self.config.default_stop_loss_pct
            
            if position_side.lower() == 'long':
                return entry_price * (1 - stop_loss_pct)
            else:  # short
                return entry_price * (1 + stop_loss_pct)
                
        except Exception as e:
            logger.error(f"Error calculating fixed percentage stop loss: {e}")
            return entry_price
    
    def _calculate_atr_stop_loss(self, symbol: str, entry_price: float, position_side: str) -> float:
        """计算基于ATR的止损"""
        try:
            # 获取ATR值
            atr = self._get_atr(symbol)
            if atr is None:
                return self._calculate_fixed_percentage_stop_loss(entry_price, position_side)
            
            # 使用ATR倍数计算止损距离
            stop_distance = atr * self.config.atr_multiplier
            
            if position_side.lower() == 'long':
                return entry_price - stop_distance
            else:  # short
                return entry_price + stop_distance
                
        except Exception as e:
            logger.error(f"Error calculating ATR stop loss: {e}")
            return self._calculate_fixed_percentage_stop_loss(entry_price, position_side)
    
    def _calculate_trailing_stop_loss(self, symbol: str, entry_price: float, position_side: str) -> float:
        """计算追踪止损"""
        try:
            # 获取当前价格
            current_price = self._get_current_price(symbol)
            if current_price is None:
                return self._calculate_fixed_percentage_stop_loss(entry_price, position_side)
            
            trailing_distance = self.config.trailing_stop_distance
            
            if position_side.lower() == 'long':
                # 多头追踪止损：当前价格下方一定距离
                return current_price * (1 - trailing_distance)
            else:  # short
                # 空头追踪止损：当前价格上方一定距离
                return current_price * (1 + trailing_distance)
                
        except Exception as e:
            logger.error(f"Error calculating trailing stop loss: {e}")
            return self._calculate_fixed_percentage_stop_loss(entry_price, position_side)
    
    def _calculate_technical_stop_loss(self, symbol: str, entry_price: float, position_side: str) -> float:
        """计算基于技术指标的止损"""
        try:
            indicators = self.technical_indicators.get(symbol, {})
            
            if position_side.lower() == 'long':
                # 多头：使用支撑位作为止损
                support_level = indicators.get('support_level')
                if support_level and support_level < entry_price:
                    return support_level
                
                # 如果没有支撑位，使用移动平均线
                ma20 = indicators.get('ma20')
                if ma20 and ma20 < entry_price:
                    return ma20
                    
            else:  # short
                # 空头：使用阻力位作为止损
                resistance_level = indicators.get('resistance_level')
                if resistance_level and resistance_level > entry_price:
                    return resistance_level
                
                # 如果没有阻力位，使用移动平均线
                ma20 = indicators.get('ma20')
                if ma20 and ma20 > entry_price:
                    return ma20
            
            # 如果没有合适的技术指标，回退到固定百分比
            return self._calculate_fixed_percentage_stop_loss(entry_price, position_side)
            
        except Exception as e:
            logger.error(f"Error calculating technical stop loss: {e}")
            return self._calculate_fixed_percentage_stop_loss(entry_price, position_side)
    
    def _calculate_adaptive_stop_loss(
        self,
        symbol: str,
        entry_price: float,
        position_side: str,
        market_data: Optional[Dict[str, Any]]
    ) -> float:
        """计算自适应止损"""
        try:
            # 综合多种方法
            stop_prices = []
            
            # 固定百分比方法
            fixed_stop = self._calculate_fixed_percentage_stop_loss(entry_price, position_side)
            stop_prices.append(fixed_stop)
            
            # ATR方法
            atr_stop = self._calculate_atr_stop_loss(symbol, entry_price, position_side)
            stop_prices.append(atr_stop)
            
            # 技术指标方法
            tech_stop = self._calculate_technical_stop_loss(symbol, entry_price, position_side)
            stop_prices.append(tech_stop)
            
            # 根据市场波动率调整
            if market_data and 'volatility' in market_data:
                volatility = market_data['volatility']
                # 高波动率时放宽止损，低波动率时收紧止损
                vol_adjustment = max(0.5, min(2.0, volatility / 0.2))
                
                if position_side.lower() == 'long':
                    # 调整止损距离
                    adjusted_stops = [
                        entry_price - abs(entry_price - stop) * vol_adjustment 
                        for stop in stop_prices
                    ]
                else:
                    adjusted_stops = [
                        entry_price + abs(stop - entry_price) * vol_adjustment 
                        for stop in stop_prices
                    ]
                
                stop_prices.extend(adjusted_stops)
            
            # 选择最保守的止损价格
            if position_side.lower() == 'long':
                # 多头选择最高的止损价格（最保守）
                adaptive_stop = max(stop_prices)
            else:
                # 空头选择最低的止损价格（最保守）
                adaptive_stop = min(stop_prices)
            
            return adaptive_stop
            
        except Exception as e:
            logger.error(f"Error calculating adaptive stop loss: {e}")
            return self._calculate_fixed_percentage_stop_loss(entry_price, position_side)
    
    def _calculate_fixed_target_take_profit(self, entry_price: float, position_side: str) -> float:
        """计算固定目标止盈"""
        try:
            take_profit_pct = self.config.default_take_profit_pct
            
            if position_side.lower() == 'long':
                return entry_price * (1 + take_profit_pct)
            else:  # short
                return entry_price * (1 - take_profit_pct)
                
        except Exception as e:
            logger.error(f"Error calculating fixed target take profit: {e}")
            return entry_price
    
    def _calculate_partial_profit_take_profit(self, entry_price: float, position_side: str) -> float:
        """计算分批止盈"""
        try:
            # 使用第一个止盈水平
            if self.config.partial_profit_levels:
                first_level = self.config.partial_profit_levels[0]
                
                if position_side.lower() == 'long':
                    return entry_price * (1 + first_level)
                else:  # short
                    return entry_price * (1 - first_level)
            
            # 如果没有配置分批水平，使用默认止盈
            return self._calculate_fixed_target_take_profit(entry_price, position_side)
            
        except Exception as e:
            logger.error(f"Error calculating partial profit take profit: {e}")
            return self._calculate_fixed_target_take_profit(entry_price, position_side)
    
    def _calculate_trailing_take_profit(self, symbol: str, entry_price: float, position_side: str) -> float:
        """计算追踪止盈"""
        try:
            # 获取当前价格
            current_price = self._get_current_price(symbol)
            if current_price is None:
                return self._calculate_fixed_target_take_profit(entry_price, position_side)
            
            trailing_distance = self.config.trailing_stop_distance
            
            if position_side.lower() == 'long':
                # 多头追踪止盈：当前价格下方一定距离
                return current_price * (1 - trailing_distance)
            else:  # short
                # 空头追踪止盈：当前价格上方一定距离
                return current_price * (1 + trailing_distance)
                
        except Exception as e:
            logger.error(f"Error calculating trailing take profit: {e}")
            return self._calculate_fixed_target_take_profit(entry_price, position_side)
    
    def _calculate_risk_reward_take_profit(
        self,
        entry_price: float,
        position_side: str,
        stop_loss_price: Optional[float]
    ) -> float:
        """计算基于风险收益比的止盈"""
        try:
            if stop_loss_price is None:
                return self._calculate_fixed_target_take_profit(entry_price, position_side)
            
            # 计算风险（止损距离）
            risk = abs(entry_price - stop_loss_price)
            
            # 计算目标收益
            target_reward = risk * self.config.risk_reward_ratio
            
            if position_side.lower() == 'long':
                return entry_price + target_reward
            else:  # short
                return entry_price - target_reward
                
        except Exception as e:
            logger.error(f"Error calculating risk reward take profit: {e}")
            return self._calculate_fixed_target_take_profit(entry_price, position_side)
    
    def _calculate_adaptive_take_profit(
        self,
        symbol: str,
        entry_price: float,
        position_side: str,
        stop_loss_price: Optional[float],
        market_data: Optional[Dict[str, Any]]
    ) -> float:
        """计算自适应止盈"""
        try:
            # 综合多种方法
            take_profit_prices = []
            
            # 固定目标方法
            fixed_tp = self._calculate_fixed_target_take_profit(entry_price, position_side)
            take_profit_prices.append(fixed_tp)
            
            # 风险收益比方法
            if stop_loss_price:
                rr_tp = self._calculate_risk_reward_take_profit(entry_price, position_side, stop_loss_price)
                take_profit_prices.append(rr_tp)
            
            # 根据市场条件调整
            if market_data:
                volatility = market_data.get('volatility', 0.2)
                trend_strength = market_data.get('trend_strength', 0.5)
                
                # 强趋势时扩大止盈目标
                if trend_strength > 0.7:
                    extended_tp_pct = self.config.default_take_profit_pct * 1.5
                    if position_side.lower() == 'long':
                        extended_tp = entry_price * (1 + extended_tp_pct)
                    else:
                        extended_tp = entry_price * (1 - extended_tp_pct)
                    take_profit_prices.append(extended_tp)
            
            # 选择适中的止盈价格
            adaptive_tp = np.median(take_profit_prices)
            
            return adaptive_tp
            
        except Exception as e:
            logger.error(f"Error calculating adaptive take profit: {e}")
            return self._calculate_fixed_target_take_profit(entry_price, position_side)
    
    def _get_atr(self, symbol: str) -> Optional[float]:
        """获取ATR值"""
        try:
            indicators = self.technical_indicators.get(symbol, {})
            return indicators.get('atr')
        except Exception as e:
            logger.error(f"Error getting ATR for {symbol}: {e}")
            return None
    
    def _get_current_price(self, symbol: str) -> Optional[float]:
        """获取当前价格"""
        try:
            if symbol in self.price_history:
                price_data = self.price_history[symbol]
                if not price_data.empty:
                    return float(price_data['close'].iloc[-1])
            return None
        except Exception as e:
            logger.error(f"Error getting current price for {symbol}: {e}")
            return None
    
    def check_stop_loss_trigger(self, symbol: str, current_price: float, position_info: Dict[str, Any]) -> bool:
        """
        检查是否触发止损
        
        Args:
            symbol: 交易对符号
            current_price: 当前价格
            position_info: 仓位信息
            
        Returns:
            是否触发止损
        """
        try:
            stop_loss_price = position_info.get('stop_loss_price')
            position_side = position_info.get('side', 'long')
            
            if stop_loss_price is None:
                return False
            
            if position_side.lower() == 'long':
                # 多头：当前价格跌破止损价格
                return current_price <= stop_loss_price
            else:  # short
                # 空头：当前价格涨破止损价格
                return current_price >= stop_loss_price
                
        except Exception as e:
            logger.error(f"Error checking stop loss trigger for {symbol}: {e}")
            return False
    
    def check_take_profit_trigger(self, symbol: str, current_price: float, position_info: Dict[str, Any]) -> bool:
        """
        检查是否触发止盈
        
        Args:
            symbol: 交易对符号
            current_price: 当前价格
            position_info: 仓位信息
            
        Returns:
            是否触发止盈
        """
        try:
            take_profit_price = position_info.get('take_profit_price')
            position_side = position_info.get('side', 'long')
            
            if take_profit_price is None:
                return False
            
            if position_side.lower() == 'long':
                # 多头：当前价格涨破止盈价格
                return current_price >= take_profit_price
            else:  # short
                # 空头：当前价格跌破止盈价格
                return current_price <= take_profit_price
                
        except Exception as e:
            logger.error(f"Error checking take profit trigger for {symbol}: {e}")
            return False
    
    def update_trailing_stops(self, symbol: str, current_price: float, position_info: Dict[str, Any]) -> Dict[str, float]:
        """
        更新追踪止损止盈
        
        Args:
            symbol: 交易对符号
            current_price: 当前价格
            position_info: 仓位信息
            
        Returns:
            更新后的止损止盈价格
        """
        try:
            position_side = position_info.get('side', 'long')
            current_stop_loss = position_info.get('stop_loss_price')
            current_take_profit = position_info.get('take_profit_price')
            
            new_stop_loss = current_stop_loss
            new_take_profit = current_take_profit
            
            trailing_distance = self.config.trailing_stop_distance
            
            if position_side.lower() == 'long':
                # 多头追踪止损：价格上涨时提高止损价格
                potential_stop_loss = current_price * (1 - trailing_distance)
                if current_stop_loss is None or potential_stop_loss > current_stop_loss:
                    new_stop_loss = potential_stop_loss
                
                # 多头追踪止盈：价格上涨时提高止盈价格
                if self.config.take_profit_type == TakeProfitType.TRAILING:
                    potential_take_profit = current_price * (1 + trailing_distance)
                    if current_take_profit is None or potential_take_profit > current_take_profit:
                        new_take_profit = potential_take_profit
                        
            else:  # short
                # 空头追踪止损：价格下跌时降低止损价格
                potential_stop_loss = current_price * (1 + trailing_distance)
                if current_stop_loss is None or potential_stop_loss < current_stop_loss:
                    new_stop_loss = potential_stop_loss
                
                # 空头追踪止盈：价格下跌时降低止盈价格
                if self.config.take_profit_type == TakeProfitType.TRAILING:
                    potential_take_profit = current_price * (1 - trailing_distance)
                    if current_take_profit is None or potential_take_profit < current_take_profit:
                        new_take_profit = potential_take_profit
            
            return {
                'stop_loss_price': new_stop_loss,
                'take_profit_price': new_take_profit
            }
            
        except Exception as e:
            logger.error(f"Error updating trailing stops for {symbol}: {e}")
            return {
                'stop_loss_price': position_info.get('stop_loss_price'),
                'take_profit_price': position_info.get('take_profit_price')
            }
    
    def get_stop_loss_take_profit_summary(self) -> Dict[str, Any]:
        """获取止损止盈摘要"""
        try:
            return {
                'stop_loss_type': self.config.stop_loss_type.value,
                'take_profit_type': self.config.take_profit_type.value,
                'default_stop_loss_pct': self.config.default_stop_loss_pct,
                'default_take_profit_pct': self.config.default_take_profit_pct,
                'risk_reward_ratio': self.config.risk_reward_ratio,
                'atr_multiplier': self.config.atr_multiplier,
                'trailing_stop_distance': self.config.trailing_stop_distance,
                'active_orders': len(self.active_orders)
            }
            
        except Exception as e:
            logger.error(f"Error getting stop loss take profit summary: {e}")
            return {'status': 'error', 'message': str(e)}
