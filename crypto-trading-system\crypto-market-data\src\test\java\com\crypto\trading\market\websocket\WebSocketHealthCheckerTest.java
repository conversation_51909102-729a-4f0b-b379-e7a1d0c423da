package com.crypto.trading.market.websocket;

import com.crypto.trading.market.config.WebSocketConfig;
import com.crypto.trading.sdk.websocket.WebSocketConnection;
import com.crypto.trading.sdk.websocket.WebSocketConnectionPool;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.lang.reflect.Field;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * WebSocketHealthChecker单元测试
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class WebSocketHealthCheckerTest {

    @Mock
    private WebSocketConnectionPool connectionPool;

    @Mock
    private WebSocketConfig webSocketConfig;

    @Mock
    private WebSocketConnection connection;

    @InjectMocks
    private WebSocketHealthChecker healthChecker;

    @BeforeEach
    void setUp() {
        // 配置健康检查间隔
        when(webSocketConfig.getHealthCheckInterval()).thenReturn(1000L);
        when(webSocketConfig.getMaxFailCount()).thenReturn(3);
        when(webSocketConfig.getReconnectInterval()).thenReturn(500L);
    }

    @Test
    void testRegisterConnection() {
        // 注册连接
        healthChecker.registerConnection(1, "test-connection");

        // 验证连接状态映射
        Map<Integer, WebSocketHealthChecker.ConnectionStatus> statusMap = healthChecker.getConnectionStatusMap();
        assertNotNull(statusMap);
        assertEquals(1, statusMap.size());
        assertTrue(statusMap.containsKey(1));
        assertEquals("test-connection", statusMap.get(1).getDescription());
    }

    @Test
    void testUnregisterConnection() {
        // 注册连接
        healthChecker.registerConnection(1, "test-connection");
        
        // 注销连接
        healthChecker.unregisterConnection(1);

        // 验证连接状态映射
        Map<Integer, WebSocketHealthChecker.ConnectionStatus> statusMap = healthChecker.getConnectionStatusMap();
        assertNotNull(statusMap);
        assertEquals(0, statusMap.size());
    }

    @Test
    void testStartStop() throws Exception {
        // 获取running字段
        Field runningField = WebSocketHealthChecker.class.getDeclaredField("running");
        runningField.setAccessible(true);
        AtomicBoolean running = (AtomicBoolean) runningField.get(healthChecker);
        
        // 启动前状态
        assertFalse(running.get());
        
        // 启动健康检查
        healthChecker.start();
        
        // 验证健康检查已启动
        assertTrue(running.get());
        
        // 停止健康检查
        healthChecker.stop();
        
        // 验证健康检查已停止
        assertFalse(running.get());
    }

    @Test
    void testConnectionStatus() {
        // 创建连接状态
        WebSocketHealthChecker.ConnectionStatus status = new WebSocketHealthChecker.ConnectionStatus(1, "test-connection");
        
        // 验证初始状态
        assertEquals(1, status.getConnectionId());
        assertEquals("test-connection", status.getDescription());
        assertEquals(0, status.getFailCount());
        
        // 增加失败计数
        status.incrementFailCount();
        assertEquals(1, status.getFailCount());
        
        // 再次增加失败计数
        status.incrementFailCount();
        assertEquals(2, status.getFailCount());
        
        // 重置失败计数
        status.resetFailCount();
        assertEquals(0, status.getFailCount());
        
        // 设置最后检查时间
        long time = System.currentTimeMillis();
        status.setLastCheckTime(time);
        assertEquals(time, status.getLastCheckTime());
    }
    
    @Test
    void testCheckConnection() throws Exception {
        // 注册连接
        healthChecker.registerConnection(1, "test-connection");
        
        // 模拟连接存在
        when(connectionPool.hasConnection(1)).thenReturn(true);
        
        // 模拟连接活跃
        when(connectionPool.isConnectionActive(1)).thenReturn(true);
        
        // 使用反射调用私有方法
        java.lang.reflect.Method method = WebSocketHealthChecker.class.getDeclaredMethod("checkConnection", int.class, WebSocketHealthChecker.ConnectionStatus.class);
        method.setAccessible(true);
        
        // 获取连接状态
        Map<Integer, WebSocketHealthChecker.ConnectionStatus> statusMap = healthChecker.getConnectionStatusMap();
        WebSocketHealthChecker.ConnectionStatus status = statusMap.get(1);
        
        // 调用checkConnection方法
        method.invoke(healthChecker, 1, status);
        
        // 验证失败计数被重置
        assertEquals(0, status.getFailCount());
        
        // 模拟连接不活跃
        when(connectionPool.isConnectionActive(1)).thenReturn(false);
        
        // 再次调用checkConnection方法
        method.invoke(healthChecker, 1, status);
        
        // 验证失败计数增加
        assertEquals(1, status.getFailCount());
    }
    
    @Test
    void testRepairConnection() throws Exception {
        // 注册连接
        healthChecker.registerConnection(1, "test-connection");
        
        // 模拟获取连接
        when(connectionPool.getConnection(1)).thenReturn(connection);
        
        // 使用反射调用私有方法
        java.lang.reflect.Method method = WebSocketHealthChecker.class.getDeclaredMethod("repairConnection", int.class);
        method.setAccessible(true);
        
        // 调用repairConnection方法
        method.invoke(healthChecker, 1);
        
        // 等待虚拟线程执行完成
        Thread.sleep(100);
        
        // 验证连接被关闭和重新连接
        verify(connection, timeout(1000)).close();
        verify(connection, timeout(1000)).connect();
    }
}