<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.crypto.trading</groupId>
    <artifactId>crypto-trading</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>crypto-trading</name>
    <description>基于Java+Python的高性能低延迟虚拟货币量化交易系</description>

    <!-- 子模块列�?-->
    <modules>
        <module>crypto-common</module>
        <module>crypto-sdk</module>
        <module>crypto-bootstrap</module>
        <module>crypto-market-data</module>
        <module>crypto-trade</module>

        <!-- 以下模块暂未实现 -->
        <!-- <module>crypto-account</module> -->
        <!-- <module>crypto-risk</module> -->
        <!-- <module>crypto-monitor</module> -->
    </modules>

    <!-- 属性配�?-->
    <properties>
        <java.version>21</java.version>
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <maven.compiler.target>${java.version}</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        
        <!-- 依赖版本 -->
        <spring-boot.version>3.2.0</spring-boot.version>
        <mybatis-plus.version>3.5.11</mybatis-plus.version>
        <mybatis-plus-jsqlparser.version>4.6</mybatis-plus-jsqlparser.version>
        <mysql.version>8.0.33</mysql.version>
        <kafka.version>3.5.1</kafka.version>
        <influxdb.version>6.10.0</influxdb.version>
        <binance-futures-connector.version>3.0.5</binance-futures-connector.version>
        <jasypt.version>3.0.5</jasypt.version>
        <slf4j.version>2.0.9</slf4j.version>
        <logback.version>1.4.11</logback.version>
        <junit.version>5.10.0</junit.version>
        <jackson.version>2.15.3</jackson.version>
        <commons-lang3.version>3.13.0</commons-lang3.version>
        <commons-io.version>2.15.0</commons-io.version>
        <guava.version>32.1.2-jre</guava.version>
        <fastjson2.version>2.0.41</fastjson2.version>
        <avro.version>1.11.3</avro.version>
        <confluent.version>7.5.3</confluent.version>
        <resilience4j.version>2.1.0</resilience4j.version>
        <lombok.version>1.18.30</lombok.version>
        <jakarta.annotation-api.version>2.1.1</jakarta.annotation-api.version>
        <mockito.version>4.8.0</mockito.version>
    </properties>
    
    <!-- 所有模块的公共依赖 -->
    <dependencies>
        <!-- Spring Boot 核心依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
            <version>${spring-boot.version}</version>
        </dependency>
        
        <!-- MyBatis-Plus -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
            <version>${mybatis-plus.version}</version>
        </dependency>
        
        <!-- MyBatis-Plus Extension -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-extension</artifactId>
            <version>${mybatis-plus.version}</version>
        </dependency>
        
        <!-- 日志框架 -->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <version>${slf4j.version}</version>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
            <version>${logback.version}</version>
        </dependency>
        
        <!-- 工具类库 -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>${commons-lang3.version}</version>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>${commons-io.version}</version>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>${guava.version}</version>
        </dependency>
        
        <!-- JSON处理 -->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>${jackson.version}</version>
        </dependency>
        
        <!-- Jakarta Annotation API -->
        <dependency>
            <groupId>jakarta.annotation</groupId>
            <artifactId>jakarta.annotation-api</artifactId>
            <version>${jakarta.annotation-api.version}</version>
        </dependency>
        
        <!-- Lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
            <scope>provided</scope>
        </dependency>
        
        <!-- 测试框架 -->
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-api</artifactId>
            <version>${junit.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-engine</artifactId>
            <version>${junit.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <version>${spring-boot.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <version>${mockito.version}</version>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <!-- 依赖管理 -->
    <dependencyManagement>
        <dependencies>
            <!-- Spring Boot 依赖管理 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- MyBatis Plus -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            
            <!-- MyBatis-Plus JSqlParser -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-jsqlparser</artifactId>
                <version>${mybatis-plus-jsqlparser.version}</version>
            </dependency>

            <!-- MyBatis-Plus Extension -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-extension</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <!-- MySQL 连接�?-->
            <dependency>
                <groupId>com.mysql</groupId>
                <artifactId>mysql-connector-j</artifactId>
                <version>${mysql.version}</version>
            </dependency>

            <!-- Kafka 依赖 -->
            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>kafka-clients</artifactId>
                <version>${kafka.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.kafka</groupId>
                <artifactId>spring-kafka</artifactId>
                <version>${spring-boot.version}</version>
            </dependency>
            
            <!-- Avro序列化相�?-->
            <dependency>
                <groupId>org.apache.avro</groupId>
                <artifactId>avro</artifactId>
                <version>${avro.version}</version>
            </dependency>
            <dependency>
                <groupId>io.confluent</groupId>
                <artifactId>kafka-avro-serializer</artifactId>
                <version>${confluent.version}</version>
            </dependency>
            <dependency>
                <groupId>io.confluent</groupId>
                <artifactId>kafka-schema-registry-client</artifactId>
                <version>${confluent.version}</version>
            </dependency>

            <!-- InfluxDB 依赖 -->
            <dependency>
                <groupId>com.influxdb</groupId>
                <artifactId>influxdb-client-java</artifactId>
                <version>${influxdb.version}</version>
            </dependency>

            <!-- 币安期货API SDK -->
            <dependency>
                <groupId>io.github.binance</groupId>
                <artifactId>binance-futures-connector-java</artifactId>
                <version>${binance-futures-connector.version}</version>
            </dependency>

            <!-- Resilience4j 限流组件 -->
            <dependency>
                <groupId>io.github.resilience4j</groupId>
                <artifactId>resilience4j-ratelimiter</artifactId>
                <version>${resilience4j.version}</version>
            </dependency>
            <dependency>
                <groupId>io.github.resilience4j</groupId>
                <artifactId>resilience4j-spring-boot3</artifactId>
                <version>${resilience4j.version}</version>
            </dependency>

            <!-- 配置文件加密 -->
            <dependency>
                <groupId>com.github.ulisesbocchio</groupId>
                <artifactId>jasypt-spring-boot-starter</artifactId>
                <version>${jasypt.version}</version>
            </dependency>

            <!-- 日志框架 -->
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-api</artifactId>
                <version>${slf4j.version}</version>
            </dependency>
            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-classic</artifactId>
                <version>${logback.version}</version>
            </dependency>
            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-core</artifactId>
                <version>${logback.version}</version>
            </dependency>

            <!-- Jackson -->
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.dataformat</groupId>
                <artifactId>jackson-dataformat-yaml</artifactId>
                <version>${jackson.version}</version>
            </dependency>

            <!-- FastJSON2 -->
            <dependency>
                <groupId>com.alibaba.fastjson2</groupId>
                <artifactId>fastjson2</artifactId>
                <version>${fastjson2.version}</version>
            </dependency>

            <!-- Apache Commons -->
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${commons-lang3.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons-io.version}</version>
            </dependency>

            <!-- Guava -->
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>

            <!-- 测试框架 -->
            <dependency>
                <groupId>org.junit.jupiter</groupId>
                <artifactId>junit-jupiter-api</artifactId>
                <version>${junit.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.junit.jupiter</groupId>
                <artifactId>junit-jupiter-engine</artifactId>
                <version>${junit.version}</version>
                <scope>test</scope>
            </dependency>

            <!-- 子模块依�?-->
            <dependency>
                <groupId>com.crypto.trading</groupId>
                <artifactId>crypto-sdk</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.crypto.trading</groupId>
                <artifactId>crypto-market-data</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.crypto.trading</groupId>
                <artifactId>crypto-bootstrap</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.crypto.trading</groupId>
                <artifactId>crypto-common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.crypto.trading</groupId>
                <artifactId>crypto-trade</artifactId>
                <version>${project.version}</version>
            </dependency>
            <!-- 以下模块暂未实现 
            <dependency>
                <groupId>com.crypto.trading</groupId>
                <artifactId>crypto-strategy</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.crypto.trading</groupId>
                <artifactId>crypto-account</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.crypto.trading</groupId>
                <artifactId>crypto-risk</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.crypto.trading</groupId>
                <artifactId>crypto-monitor</artifactId>
                <version>${project.version}</version>
            </dependency>
            -->
        </dependencies>
    </dependencyManagement>

    <!-- 仓库配置 -->
    <repositories>
        <repository>
            <id>confluent</id>
            <url>https://packages.confluent.io/maven/</url>
        </repository>
    </repositories>
    
    <!-- 构建配置 -->
    <build>
        <pluginManagement>
            <plugins>
                <!-- Maven编译插件 -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.11.0</version>
                    <configuration>
                        <source>${java.version}</source>
                        <target>${java.version}</target>
                        <encoding>${project.build.sourceEncoding}</encoding>
                        <compilerArgs>
                            <arg>-parameters</arg>
                            <arg>--enable-preview</arg>
                        </compilerArgs>
                    </configuration>
                </plugin>
                <!-- Maven Surefire插件，用于运行单元测�?-->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>3.1.2</version>
                    <configuration>
                        <argLine>-Dfile.encoding=UTF-8 --enable-preview</argLine>
                    </configuration>
                </plugin>
                <!-- Spring Boot Maven插件 -->
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring-boot.version}</version>
                    <configuration>
                        <excludes>
                            <exclude>
                                <groupId>org.projectlombok</groupId>
                                <artifactId>lombok</artifactId>
                            </exclude>
                        </excludes>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>
</project>
