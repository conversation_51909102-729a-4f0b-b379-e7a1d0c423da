package com.crypto.trading.common.dto.trade;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;

/**
 * 订单数据传输对象
 * <p>
 * 用于在不同服务之间传递订单信息
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class OrderDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单ID
     */
    private String orderId;

    /**
     * 客户端订单ID
     */
    private String clientOrderId;

    /**
     * 交易对
     */
    private String symbol;

    /**
     * 买卖方向
     */
    private String side;

    /**
     * 持仓方向
     */
    private String positionSide;

    /**
     * 订单类型
     */
    private String type;

    /**
     * 订单数量
     */
    private Double quantity;

    /**
     * 订单价格
     */
    private Double price;

    /**
     * 已执行数量
     */
    private Double executedQuantity;

    /**
     * 成交价格
     */
    private Double executedPrice;

    /**
     * 订单状态
     */
    private String status;

    /**
     * 策略ID
     */
    private String strategyId;

    /**
     * 错误代码
     */
    private String errorCode;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    private Long createdTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    private Long updatedTime;

    /**
     * 执行时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    private Long executedTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 获取订单ID
     *
     * @return 订单ID
     */
    public String getOrderId() {
        return orderId;
    }

    /**
     * 设置订单ID
     *
     * @param orderId 订单ID
     */
    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    /**
     * 获取客户端订单ID
     *
     * @return 客户端订单ID
     */
    public String getClientOrderId() {
        return clientOrderId;
    }

    /**
     * 设置客户端订单ID
     *
     * @param clientOrderId 客户端订单ID
     */
    public void setClientOrderId(String clientOrderId) {
        this.clientOrderId = clientOrderId;
    }

    /**
     * 获取交易对
     *
     * @return 交易对
     */
    public String getSymbol() {
        return symbol;
    }

    /**
     * 设置交易对
     *
     * @param symbol 交易对
     */
    public void setSymbol(String symbol) {
        this.symbol = symbol;
    }

    /**
     * 获取买卖方向
     *
     * @return 买卖方向
     */
    public String getSide() {
        return side;
    }

    /**
     * 设置买卖方向
     *
     * @param side 买卖方向
     */
    public void setSide(String side) {
        this.side = side;
    }

    /**
     * 获取持仓方向
     *
     * @return 持仓方向
     */
    public String getPositionSide() {
        return positionSide;
    }

    /**
     * 设置持仓方向
     *
     * @param positionSide 持仓方向
     */
    public void setPositionSide(String positionSide) {
        this.positionSide = positionSide;
    }

    /**
     * 获取订单类型
     *
     * @return 订单类型
     */
    public String getType() {
        return type;
    }

    /**
     * 设置订单类型
     *
     * @param type 订单类型
     */
    public void setType(String type) {
        this.type = type;
    }

    /**
     * 获取订单数量
     *
     * @return 订单数量
     */
    public Double getQuantity() {
        return quantity;
    }

    /**
     * 设置订单数量
     *
     * @param quantity 订单数量
     */
    public void setQuantity(Double quantity) {
        this.quantity = quantity;
    }

    /**
     * 获取订单价格
     *
     * @return 订单价格
     */
    public Double getPrice() {
        return price;
    }

    /**
     * 设置订单价格
     *
     * @param price 订单价格
     */
    public void setPrice(Double price) {
        this.price = price;
    }

    /**
     * 获取已执行数量
     *
     * @return 已执行数量
     */
    public Double getExecutedQuantity() {
        return executedQuantity;
    }

    /**
     * 设置已执行数量
     *
     * @param executedQuantity 已执行数量
     */
    public void setExecutedQuantity(Double executedQuantity) {
        this.executedQuantity = executedQuantity;
    }

    /**
     * 获取成交价格
     *
     * @return 成交价格
     */
    public Double getExecutedPrice() {
        return executedPrice;
    }

    /**
     * 设置成交价格
     *
     * @param executedPrice 成交价格
     */
    public void setExecutedPrice(Double executedPrice) {
        this.executedPrice = executedPrice;
    }

    /**
     * 获取订单状态
     *
     * @return 订单状态
     */
    public String getStatus() {
        return status;
    }

    /**
     * 设置订单状态
     *
     * @param status 订单状态
     */
    public void setStatus(String status) {
        this.status = status;
    }

    /**
     * 获取策略ID
     *
     * @return 策略ID
     */
    public String getStrategyId() {
        return strategyId;
    }

    /**
     * 设置策略ID
     *
     * @param strategyId 策略ID
     */
    public void setStrategyId(String strategyId) {
        this.strategyId = strategyId;
    }

    /**
     * 获取错误代码
     *
     * @return 错误代码
     */
    public String getErrorCode() {
        return errorCode;
    }

    /**
     * 设置错误代码
     *
     * @param errorCode 错误代码
     */
    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    /**
     * 获取错误信息
     *
     * @return 错误信息
     */
    public String getErrorMessage() {
        return errorMessage;
    }

    /**
     * 设置错误信息
     *
     * @param errorMessage 错误信息
     */
    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    /**
     * 获取创建时间
     *
     * @return 创建时间
     */
    public Long getCreatedTime() {
        return createdTime;
    }

    /**
     * 设置创建时间
     *
     * @param createdTime 创建时间
     */
    public void setCreatedTime(Long createdTime) {
        this.createdTime = createdTime;
    }

    /**
     * 获取更新时间
     *
     * @return 更新时间
     */
    public Long getUpdatedTime() {
        return updatedTime;
    }

    /**
     * 设置更新时间
     *
     * @param updatedTime 更新时间
     */
    public void setUpdatedTime(Long updatedTime) {
        this.updatedTime = updatedTime;
    }

    /**
     * 获取执行时间
     *
     * @return 执行时间
     */
    public Long getExecutedTime() {
        return executedTime;
    }

    /**
     * 设置执行时间
     *
     * @param executedTime 执行时间
     */
    public void setExecutedTime(Long executedTime) {
        this.executedTime = executedTime;
    }

    /**
     * 获取备注
     *
     * @return 备注
     */
    public String getRemark() {
        return remark;
    }

    /**
     * 设置备注
     *
     * @param remark 备注
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }
}