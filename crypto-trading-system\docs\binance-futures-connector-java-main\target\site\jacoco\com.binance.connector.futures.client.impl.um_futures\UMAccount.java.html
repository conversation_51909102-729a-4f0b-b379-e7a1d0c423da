<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>UMAccount.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">io.github.binance:binance-futures-connector-java</a> &gt; <a href="index.source.html" class="el_package">com.binance.connector.futures.client.impl.um_futures</a> &gt; <span class="el_source">UMAccount.java</span></div><h1>UMAccount.java</h1><pre class="source lang-java linenums">package com.binance.connector.futures.client.impl.um_futures;

import com.binance.connector.futures.client.enums.HttpMethod;
import com.binance.connector.futures.client.utils.ParameterChecker;
import java.util.LinkedHashMap;
import com.binance.connector.futures.client.impl.futures.Account;
import com.binance.connector.futures.client.utils.ProxyAuth;

/**
 * &lt;h2&gt;USDⓈ-Margined Trade Endpoints&lt;/h2&gt;
 * All endpoints under the
 * &lt;a href=&quot;https://developers.binance.com/docs/derivatives/usds-margined-futures/account/rest-api/New-Future-Account-Transfer&quot;&gt;Futures Account/Trade Endpoint&lt;/a&gt;
 * section of the API documentation will be implemented in this class.
 * &lt;br&gt;
 * Response will be returned in &lt;i&gt;String format&lt;/i&gt;.
 */
public class UMAccount extends Account {
    public UMAccount(String productUrl, String apiKey, String secretKey, boolean showLimitUsage, ProxyAuth proxy) {
<span class="fc" id="L19">        super(productUrl, apiKey, secretKey, showLimitUsage, proxy);</span>
<span class="fc" id="L20">    }</span>

<span class="fc" id="L22">    private final String MULTI_ASSETS_MARGIN = &quot;/v1/multiAssetsMargin&quot;;</span>
    /**
     * Change user's Multi-Assets mode (Multi-Assets Mode or Single-Asset Mode) on Every symbol
     * &lt;br&gt;&lt;br&gt;
     * POST /v1/multiAssetsMargin
     * &lt;br&gt;
     * @param
     * parameters LinkedHashedMap of String,Object pair
     *            where String is the name of the parameter and Object is the value of the parameter
     * &lt;br&gt;&lt;br&gt;
     * multiAssetsMargin -- mandatory/string &lt;br&gt;
     * recvWindow -- optional/long &lt;br&gt;
     * @return String
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/usds-margined-futures/trade/rest-api/Change-Multi-Assets-Mode&quot;&gt;
     *     https://developers.binance.com/docs/derivatives/usds-margined-futures/trade/rest-api/Change-Multi-Assets-Mode&lt;/a&gt;
     */
    public String changeMultiAssetsMode(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L39">        ParameterChecker.checkParameter(parameters, &quot;dualSidePosition&quot;, String.class);</span>
<span class="fc" id="L40">        return getRequestHandler().sendSignedRequest(getProductUrl(), MULTI_ASSETS_MARGIN, parameters, HttpMethod.POST, getShowLimitUsage());</span>
    }

    /**
     * Get user's Multi-Assets mode (Multi-Assets Mode or Single-Asset Mode) on Every symbol
     * &lt;br&gt;&lt;br&gt;
     * GET /v1/multiAssetsMargin
     * &lt;br&gt;
     * @param
     * parameters LinkedHashedMap of String,Object pair
     *            where String is the name of the parameter and Object is the value of the parameter
     * &lt;br&gt;&lt;br&gt;
     * recvWindow -- optional/long &lt;br&gt;
     * @return String
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/usds-margined-futures/account/rest-api/Get-Current-Multi-Assets-Mode&quot;&gt;
     *     https://developers.binance.com/docs/derivatives/usds-margined-futures/account/rest-api/Get-Current-Multi-Assets-Mode&lt;/a&gt;
     */
    public String getCurrentMultiAssetMode(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L58">        return getRequestHandler().sendSignedRequest(getProductUrl(), MULTI_ASSETS_MARGIN, parameters, HttpMethod.GET, getShowLimitUsage());</span>
    }


    /**
     * Get all open orders on a symbol. Careful when accessing this with no symbol.
     * &lt;br&gt;&lt;br&gt;
     * GET /v1/openOrders
     * &lt;br&gt;
     * @param
     * parameters LinkedHashedMap of String,Object pair
     *            where String is the name of the parameter and Object is the value of the parameter
     * &lt;br&gt;&lt;br&gt;
     * symbol -- optional/string &lt;br&gt;
     * recvWindow -- optional/long &lt;br&gt;
     * @return String
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/usds-margined-futures/trade/rest-api/Current-All-Open-Orders&quot;&gt;
     *    https://developers.binance.com/docs/derivatives/usds-margined-futures/trade/rest-api/Current-All-Open-Orders&lt;/a&gt;
     */
    public String currentAllOpenOrders(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L78">        return super.currentAllOpenOrders(parameters);</span>
    }

    /**
     * Get all open orders on a symbol. Careful when accessing this with no symbol.
     * &lt;br&gt;&lt;br&gt;
     * GET /v1/allOrders
     * &lt;br&gt;
     * @param
     * parameters LinkedHashedMap of String,Object pair
     *            where String is the name of the parameter and Object is the value of the parameter
     * &lt;br&gt;&lt;br&gt;
     * symbol -- mandatory/string &lt;br&gt;
     * orderId -- optional/long &lt;br&gt;
     * startTime -- optional/long &lt;br&gt;
     * endTime -- optional/long &lt;br&gt;
     * limit -- optional/integer &lt;br&gt;
     * recvWindow -- optional/long &lt;br&gt;
     * @return String
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/usds-margined-futures/trade/rest-api/All-Orders&quot;&gt;
     *    https://developers.binance.com/docs/derivatives/usds-margined-futures/trade/rest-api/All-Orders&lt;/a&gt;
     */
    public String allOrders(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L101">        ParameterChecker.checkParameter(parameters, &quot;symbol&quot;, String.class);</span>
<span class="fc" id="L102">        return super.allOrders(parameters);</span>
    }

<span class="fc" id="L105">    private final String BALANCE = &quot;/v2/balance&quot;;</span>
    /**
     * Get Futures Account Balance
     * &lt;br&gt;&lt;br&gt;
     * GET /v2/balance
     * &lt;br&gt;
     * @param
     * parameters LinkedHashedMap of String,Object pair
     *            where String is the name of the parameter and Object is the value of the parameter
     * &lt;br&gt;&lt;br&gt;
     * recvWindow -- optional/long &lt;br&gt;
     * @return String
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/usds-margined-futures/account/rest-api/Futures-Account-Balance-V2&quot;&gt;
     *    https://developers.binance.com/docs/derivatives/usds-margined-futures/account/rest-api/Futures-Account-Balance-V2&lt;/a&gt;
     */
    public String futuresAccountBalance(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L121">        return getRequestHandler().sendSignedRequest(getProductUrl(), BALANCE, parameters, HttpMethod.GET, getShowLimitUsage());</span>
    }

<span class="fc" id="L124">    private final String ACCOUNT_INFORMATION = &quot;/v2/account&quot;;</span>
    /**
     * Get current account information. User in single-asset/ multi-assets mode will see different value, see comments in response section for detail.
     * &lt;br&gt;&lt;br&gt;
     * GET /v2/account
     * &lt;br&gt;
     * @param
     * parameters LinkedHashedMap of String,Object pair
     *            where String is the name of the parameter and Object is the value of the parameter
     * &lt;br&gt;&lt;br&gt;
     * recvWindow -- optional/long &lt;br&gt;
     * @return String
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/usds-margined-futures/account/rest-api/Account-Information-V2&quot;&gt;
     *    https://developers.binance.com/docs/derivatives/usds-margined-futures/account/rest-api/Account-Information-V2&lt;/a&gt;
     */
    public String accountInformation(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L140">        return getRequestHandler().sendSignedRequest(getProductUrl(), ACCOUNT_INFORMATION, parameters, HttpMethod.GET, getShowLimitUsage());</span>
    }

<span class="fc" id="L143">    private final String POSITION_RISK = &quot;/v2/positionRisk&quot;;</span>
    /**
     * Get current position information.
     * &lt;br&gt;&lt;br&gt;
     * GET /v2/positionRisk
     * &lt;br&gt;
     * @param
     * parameters LinkedHashedMap of String,Object pair
     *            where String is the name of the parameter and Object is the value of the parameter
     * &lt;br&gt;&lt;br&gt;
     * symbol -- optional/string &lt;br&gt;
     * recvWindow -- optional/long &lt;br&gt;
     * @return String
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/usds-margined-futures/trade/rest-api/Position-Information-V2&quot;&gt;
     *    https://developers.binance.com/docs/derivatives/usds-margined-futures/trade/rest-api/Position-Information-V2&lt;/a&gt;
     */
    public String positionInformation(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L160">        return getRequestHandler().sendSignedRequest(getProductUrl(), POSITION_RISK, parameters, HttpMethod.GET, getShowLimitUsage());</span>
    }

    /**
     * Get trades for a specific account and symbol.
     * &lt;br&gt;&lt;br&gt;
     * GET /v1/userTrades
     * &lt;br&gt;
     * @param
     * parameters LinkedHashedMap of String,Object pair
     *            where String is the name of the parameter and Object is the value of the parameter
     * &lt;br&gt;&lt;br&gt;
     * symbol -- mandatory/string &lt;br&gt;
     * startTime -- optional/long &lt;br&gt;
     * endTime -- optional/long &lt;br&gt;
     * fromId -- optional/long &lt;br&gt;
     * limit -- optional/integer &lt;br&gt;
     * recvWindow -- optional/long &lt;br&gt;
     * @return String
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/usds-margined-futures/trade/rest-api/Account-Trade-List&quot;&gt;
     *    https://developers.binance.com/docs/derivatives/usds-margined-futures/trade/rest-api/Account-Trade-List&lt;/a&gt;
     */
    public String accountTradeList(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L183">        ParameterChecker.checkParameter(parameters, &quot;symbol&quot;, String.class);</span>
<span class="fc" id="L184">        return super.accountTradeList(parameters);</span>
    }

    /**
     * Notional and Leverage Brackets
     * &lt;br&gt;&lt;br&gt;
     * GET /v1/leverageBracket
     * &lt;br&gt;
     * @param
     * parameters LinkedHashedMap of String,Object pair
     *            where String is the name of the parameter and Object is the value of the parameter
     * &lt;br&gt;&lt;br&gt;
     * symbol -- optional/string &lt;br&gt;
     * recvWindow -- optional/long &lt;br&gt;
     * @return String
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/usds-margined-futures/account/rest-api/Notional-and-Leverage-Brackets&quot;&gt;
     *    https://developers.binance.com/docs/derivatives/usds-margined-futures/account/rest-api/Notional-and-Leverage-Brackets&lt;/a&gt;
     */
    public String getLeverageBracket(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L203">        return super.getLeverageBracket(parameters);</span>
    }

<span class="fc" id="L206">    private final String API_TRADING_STATUS = &quot;/v1/apiTradingStatus&quot;;</span>
    /**
     * Futures Trading Quantitative Rules Indicators
     * For more information on this, please refer to the &lt;a href=&quot;https://www.binance.com/en/support/faq/4f462ebe6ff445d4a170be7d9e897272&quot;&gt;Futures Trading Quantitative Rules&lt;/a&gt;
     * &lt;br&gt;&lt;br&gt;
     * GET /v1/apiTradingStatus
     * &lt;br&gt;
     * @param
     * parameters LinkedHashedMap of String,Object pair
     *            where String is the name of the parameter and Object is the value of the parameter
     * &lt;br&gt;&lt;br&gt;
     * symbol -- optional/string &lt;br&gt;
     * recvWindow -- optional/long &lt;br&gt;
     * @return String
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/usds-margined-futures/account/rest-api/Futures-Trading-Quantitative-Rules-Indicators&quot;&gt;
     *    https://developers.binance.com/docs/derivatives/usds-margined-futures/account/rest-api/Futures-Trading-Quantitative-Rules-Indicators&lt;/a&gt;
     */
    public String getTradingRulesIndicators(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L224">        return getRequestHandler().sendSignedRequest(getProductUrl(), API_TRADING_STATUS, parameters, HttpMethod.GET, getShowLimitUsage());</span>
    }

<span class="fc" id="L227">    private final String INCOME_ASYN = &quot;/v1/income/asyn&quot;;</span>
    /**
     * Get Download Id For Futures Transaction History
     * &lt;br&gt;&lt;br&gt;
     * GET /v1/income/asyn
     * &lt;br&gt;
     * @param
     * parameters LinkedHashedMap of String,Object pair
     *            where String is the name of the parameter and Object is the value of the parameter
     * &lt;br&gt;&lt;br&gt;
     * startTime -- optional/long &lt;br&gt;
     * endTime -- optional/long &lt;br&gt;
     * recvWindow -- optional/long &lt;br&gt;
     * @return String
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/usds-margined-futures/account/rest-api/Get-Download-Id-For-Futures-Transaction-History&quot;&gt;
     *    https://developers.binance.com/docs/derivatives/usds-margined-futures/account/rest-api/Get-Download-Id-For-Futures-Transaction-History&lt;/a&gt;
     */
    public String futuresDownloadId(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L245">        return getRequestHandler().sendSignedRequest(getProductUrl(), INCOME_ASYN, parameters, HttpMethod.GET, getShowLimitUsage());</span>
    }

<span class="fc" id="L248">    private final String INCOME_ASYN_ID = &quot;/v1/income/asyn/id&quot;;</span>
    /**
     * Get Futures Transaction History Download Link by Id
     * &lt;br&gt;&lt;br&gt;
     * GET /v1/income/asyn/id
     * &lt;br&gt;
     * @param
     * parameters LinkedHashedMap of String,Object pair
     *            where String is the name of the parameter and Object is the value of the parameter
     * &lt;br&gt;&lt;br&gt;
     * downloadId -- mandatory/string &lt;br&gt;
     * recvWindow -- optional/long &lt;br&gt;
     * @return String
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/usds-margined-futures/account/rest-api/Get-Futures-Transaction-History-Download-Link-by-Id&quot;&gt;
     *    https://developers.binance.com/docs/derivatives/usds-margined-futures/account/rest-api/Get-Futures-Transaction-History-Download-Link-by-Id&lt;/a&gt;
     */
    public String futuresDownloadLink(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L265">        ParameterChecker.checkParameter(parameters, &quot;downloadId&quot;, String.class);</span>
<span class="fc" id="L266">        return getRequestHandler().sendSignedRequest(getProductUrl(), INCOME_ASYN_ID, parameters, HttpMethod.GET, getShowLimitUsage());</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.7.************</span></div></body></html>