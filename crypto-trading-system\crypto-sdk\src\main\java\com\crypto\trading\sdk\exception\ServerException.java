package com.crypto.trading.sdk.exception;

import com.crypto.trading.common.exception.ErrorCode;

/**
 * 服务端异常类
 * 表示服务端错误导致的异常
 */
public class ServerException extends ApiException {

    private static final long serialVersionUID = 1L;

    /**
     * HTTP状态码
     */
    private final int httpStatusCode;

    /**
     * 构造函数
     *
     * @param code 错误代码
     * @param message 错误消息
     * @param httpStatusCode HTTP状态码
     */
    public ServerException(int code, String message, int httpStatusCode) {
        super(code, message);
        this.httpStatusCode = httpStatusCode;
    }

    /**
     * 构造函数
     *
     * @param code 错误代码
     * @param message 错误消息
     * @param httpStatusCode HTTP状态码
     * @param cause 异常原因
     */
    public ServerException(int code, String message, int httpStatusCode, Throwable cause) {
        super(code, message, cause);
        this.httpStatusCode = httpStatusCode;
    }

    /**
     * 构造函数
     *
     * @param errorCode 错误码枚举
     * @param message 错误消息
     * @param httpStatusCode HTTP状态码
     */
    public ServerException(ErrorCode errorCode, String message, int httpStatusCode) {
        super(errorCode, message);
        this.httpStatusCode = httpStatusCode;
    }

    /**
     * 获取HTTP状态码
     *
     * @return HTTP状态码
     */
    public int getHttpStatusCode() {
        return httpStatusCode;
    }

    @Override
    public String toString() {
        return "ServerException{" +
                "errorCode=" + getCode() +
                ", errorMessage='" + getErrorMessage() + '\'' +
                ", httpStatusCode=" + httpStatusCode +
                '}';
    }
}