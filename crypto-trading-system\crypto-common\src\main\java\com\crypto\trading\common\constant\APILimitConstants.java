package com.crypto.trading.common.constant;

/**
 * API限流常量类
 * <p>
 * 定义API限流相关的常量，如窗口时间、限制数量等
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public final class APILimitConstants {

    /**
     * 私有构造函数，防止实例化
     */
    private APILimitConstants() {
        throw new IllegalStateException("常量类不允许实例化");
    }

    /**
     * 币安期货API窗口时间（毫秒）
     */
    public static final long BINANCE_FUTURES_WINDOW_MS = 60_000L;

    /**
     * 币安期货IP限制 - 所有请求
     */
    public static final int BINANCE_FUTURES_IP_LIMIT_ALL = 2400;

    /**
     * 币安期货IP限制 - 下单请求
     */
    public static final int BINANCE_FUTURES_IP_LIMIT_ORDER = 1200;

    /**
     * 币安期货IP限制 - 查询请求
     */
    public static final int BINANCE_FUTURES_IP_LIMIT_QUERY = 2400;

    /**
     * 币安期货WebSocket连接数限制
     */
    public static final int BINANCE_FUTURES_WEBSOCKET_CONNECTIONS_LIMIT = 50;

    /**
     * 币安期货权重 - 市场数据默认权重
     */
    public static final int BINANCE_FUTURES_WEIGHT_MARKET_DEFAULT = 1;

    /**
     * 币安期货权重 - 账户信息
     */
    public static final int BINANCE_FUTURES_WEIGHT_ACCOUNT_INFO = 5;

    /**
     * 币安期货权重 - 下单
     */
    public static final int BINANCE_FUTURES_WEIGHT_ORDER = 1;

    /**
     * 币安期货权重 - 批量下单
     */
    public static final int BINANCE_FUTURES_WEIGHT_BATCH_ORDERS = 5;

    /**
     * 币安期货权重 - 撤单
     */
    public static final int BINANCE_FUTURES_WEIGHT_CANCEL_ORDER = 1;

    /**
     * 币安期货权重 - 批量撤单
     */
    public static final int BINANCE_FUTURES_WEIGHT_CANCEL_ALL_ORDERS = 5;

    /**
     * 币安期货权重 - 查询订单
     */
    public static final int BINANCE_FUTURES_WEIGHT_QUERY_ORDER = 1;

    /**
     * 币安期货权重 - 查询所有订单
     */
    public static final int BINANCE_FUTURES_WEIGHT_QUERY_ALL_ORDERS = 5;

    /**
     * 币安期货权重 - 查询持仓
     */
    public static final int BINANCE_FUTURES_WEIGHT_QUERY_POSITION = 1;

    /**
     * 币安期货单个连接订阅数量限制
     */
    public static final int BINANCE_FUTURES_WEBSOCKET_SUBSCRIPTIONS_PER_CONNECTION = 200;

    /**
     * 币安期货请求权重 - 持仓查询
     */
    public static final int BINANCE_FUTURES_WEIGHT_POSITION_INFO = 5;

    /**
     * 币安期货请求权重 - 查询所有订单
     */
    public static final int BINANCE_FUTURES_WEIGHT_GET_ALL_ORDERS = 5;

    /**
     * 币安期货请求权重 - K线数据查询
     */
    public static final int BINANCE_FUTURES_WEIGHT_KLINES = 1;

    /**
     * 币安期货请求权重 - 大量K线数据查询
     */
    public static final int BINANCE_FUTURES_WEIGHT_KLINES_HEAVY = 5;

    /**
     * 币安期货请求权重 - 深度数据查询（默认）
     */
    public static final int BINANCE_FUTURES_WEIGHT_DEPTH_DEFAULT = 5;

    /**
     * 币安期货请求权重 - 深度数据查询（高级别）
     */
    public static final int BINANCE_FUTURES_WEIGHT_DEPTH_HEAVY = 10;

    /**
     * 币安期货请求权重 - 历史成交查询
     */
    public static final int BINANCE_FUTURES_WEIGHT_TRADES_HISTORY = 5;

    /**
     * 币安期货请求权重 - 大量历史成交查询
     */
    public static final int BINANCE_FUTURES_WEIGHT_TRADES_HISTORY_HEAVY = 10;

    /**
     * 币安期货请求权重 - 用户数据流创建
     */
    public static final int BINANCE_FUTURES_WEIGHT_USER_DATA_STREAM_START = 1;

    /**
     * 币安期货请求权重 - 用户数据流续期
     */
    public static final int BINANCE_FUTURES_WEIGHT_USER_DATA_STREAM_KEEPALIVE = 1;

    /**
     * 币安期货请求权重 - 用户数据流关闭
     */
    public static final int BINANCE_FUTURES_WEIGHT_USER_DATA_STREAM_CLOSE = 1;

    /**
     * 币安期货限流指数退避初始延迟（毫秒）
     */
    public static final long RATE_LIMIT_BACKOFF_INITIAL_DELAY_MS = 1000L;

    /**
     * 币安期货限流指数退避最大延迟（毫秒）
     */
    public static final long RATE_LIMIT_BACKOFF_MAX_DELAY_MS = 30000L;

    /**
     * 币安期货限流指数退避因子
     */
    public static final double RATE_LIMIT_BACKOFF_FACTOR = 2.0;

    /**
     * 币安期货限流指数退避随机因子
     */
    public static final double RATE_LIMIT_BACKOFF_JITTER = 0.5;

    /**
     * 币安期货限流最大重试次数
     */
    public static final int RATE_LIMIT_MAX_RETRIES = 5;

    /**
     * API限流警告阈值（百分比）
     */
    public static final double RATE_LIMIT_WARNING_THRESHOLD = 0.8;

    /**
     * API限流紧急阈值（百分比）
     */
    public static final double RATE_LIMIT_CRITICAL_THRESHOLD = 0.95;

    /**
     * API请求默认超时时间（毫秒）
     */
    public static final long API_DEFAULT_TIMEOUT_MS = 5000L;

    /**
     * API请求连接超时时间（毫秒）
     */
    public static final long API_CONNECTION_TIMEOUT_MS = 3000L;

    /**
     * API请求读取超时时间（毫秒）
     */
    public static final long API_READ_TIMEOUT_MS = 30000L;

    /**
     * API请求写入超时时间（毫秒）
     */
    public static final long API_WRITE_TIMEOUT_MS = 30000L;
} 