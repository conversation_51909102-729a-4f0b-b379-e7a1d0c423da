<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ResponseHandler</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">io.github.binance:binance-futures-connector-java</a> &gt; <a href="index.html" class="el_package">com.binance.connector.futures.client.utils</a> &gt; <span class="el_class">ResponseHandler</span></div><h1>ResponseHandler</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">78 of 152</td><td class="ctr2">48%</td><td class="bar">11 of 20</td><td class="ctr2">45%</td><td class="ctr1">8</td><td class="ctr2">14</td><td class="ctr1">16</td><td class="ctr2">33</td><td class="ctr1">1</td><td class="ctr2">4</td></tr></tfoot><tbody><tr><td id="a3"><a href="ResponseHandler.java.html#L26" class="el_method">handleResponse(Request, boolean, ProxyAuth)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="63" height="10" title="50" alt="50"/><img src="../jacoco-resources/greenbar.gif" width="56" height="10" title="45" alt="45"/></td><td class="ctr2" id="c2">47%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="66" height="10" title="10" alt="10"/><img src="../jacoco-resources/greenbar.gif" width="53" height="10" title="8" alt="8"/></td><td class="ctr2" id="e1">44%</td><td class="ctr1" id="f0">6</td><td class="ctr2" id="g0">10</td><td class="ctr1" id="h0">10</td><td class="ctr2" id="i0">20</td><td class="ctr1" id="j1">0</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a0"><a href="ResponseHandler.java.html#L59" class="el_method">getlimitUsage(Response, String)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="32" height="10" title="26" alt="26"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d2"/><td class="ctr2" id="e2">n/a</td><td class="ctr1" id="f1">1</td><td class="ctr2" id="g2">1</td><td class="ctr1" id="h1">5</td><td class="ctr2" id="i1">5</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a1"><a href="ResponseHandler.java.html#L78" class="el_method">getResponseBodyAsString(ResponseBody)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="2" alt="2"/><img src="../jacoco-resources/greenbar.gif" width="7" height="10" title="6" alt="6"/></td><td class="ctr2" id="c1">75%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="6" height="10" title="1" alt="1"/></td><td class="ctr2" id="e0">50%</td><td class="ctr1" id="f2">1</td><td class="ctr2" id="g1">2</td><td class="ctr1" id="h2">1</td><td class="ctr2" id="i3">3</td><td class="ctr1" id="j2">0</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a2"><a href="ResponseHandler.java.html#L69" class="el_method">handleErrorResponse(String, int)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/greenbar.gif" width="29" height="10" title="23" alt="23"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d3"/><td class="ctr2" id="e3">n/a</td><td class="ctr1" id="f3">0</td><td class="ctr2" id="g3">1</td><td class="ctr1" id="h3">0</td><td class="ctr2" id="i2">5</td><td class="ctr1" id="j3">0</td><td class="ctr2" id="k3">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.7.202105040129</span></div></body></html>