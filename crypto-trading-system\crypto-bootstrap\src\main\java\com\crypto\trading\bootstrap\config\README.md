# 配置文件整合说明

## 概述

本项目已将所有配置文件整合到`crypto-bootstrap`模块中，采用统一的YAML格式，并通过环境特定文件进行环境隔离。配置类也统一放置在bootstrap模块的config包下，便于统一管理和维护。

## 配置文件结构

```
crypto-bootstrap/src/main/resources/
├── application.yml              # 主配置文件，包含所有通用配置
├── application-dev.yml          # 开发环境特定配置
├── application-test.yml         # 测试环境特定配置
├── application-prod.yml         # 生产环境特定配置
└── docker.env.example           # Docker环境变量示例文件
```

## 配置类层次结构

```
com.crypto.trading.bootstrap.config
├── AppConfig.java               # 应用全局配置类
├── BinanceApiConfig.java        # 币安API配置类
├── BootstrapConfig.java         # 启动模块配置类（主配置类）
<!-- 已移除冗余的InfluxDBConfig类，使用market-data模块的InfluxDBConfig -->
└── MarketDataConfig.java        # 市场数据配置类
```

## 使用方法

### 1. 配置注入

所有配置属性已通过`@ConfigurationProperties`映射到对应的配置类，可以直接通过依赖注入的方式在应用中使用：

```java
@Service
public class ExampleService {

    private final AppConfig appConfig;
    private final BinanceApiConfig binanceConfig;
    
    @Autowired
    public ExampleService(AppConfig appConfig, BinanceApiConfig binanceConfig) {
        this.appConfig = appConfig;
        this.binanceConfig = binanceConfig;
    }
    
    public void doSomething() {
        // 使用配置属性
        String apiKey = binanceConfig.getApi().getKey();
        boolean debug = appConfig.isDebug();
    }
}
```

### 2. 环境配置

通过设置`spring.profiles.active`来指定运行环境：

```
# 开发环境
java -jar app.jar --spring.profiles.active=dev

# 测试环境
java -jar app.jar --spring.profiles.active=test

# 生产环境
java -jar app.jar --spring.profiles.active=prod
```

### 3. Docker环境配置

使用Docker部署时，复制`docker.env.example`文件为`.env`，并根据实际情况修改其中的配置值。

## 敏感信息处理

敏感配置信息（如API密钥、数据库密码等）通过环境变量注入，避免在配置文件中硬编码：

```yaml
binance:
  api:
    key: ${BINANCE_API_KEY:}
    secret: ${BINANCE_API_SECRET:}
```

在生产环境中，请确保正确设置这些环境变量。

## 配置项说明

| 配置项 | 说明 | 默认值 |
|-------|-----|-------|
| spring.application.name | 应用名称 | crypto-trading-system |
| server.port | 服务端口 | 8080 |
| binance.api.use-testnet | 是否使用币安测试网络 | false |
| app.debug | 是否开启调试模式 | false |
| influxdb.url | InfluxDB服务器URL | http://localhost:8086 |
| market.symbols | 交易对列表 | BTCUSDT,ETHUSDT,BNBUSDT |

更多配置项请参考各配置文件。 