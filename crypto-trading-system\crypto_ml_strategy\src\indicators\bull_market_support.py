#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Bull Market Support Band (BMSB) 特征提取模块

实现Bull Market Support Band指标特征提取，该指标基于长期移动平均线的支撑位识别牛市中的重要支撑位置。
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Union


class BullMarketSupportFeatureExtractor:
    """Bull Market Support Band特征提取器，用于识别牛市支撑位"""

    def __init__(self, config: Dict = None):
        """
        初始化Bull Market Support Band特征提取器
        
        Args:
            config: 配置参数字典，包括以下可选参数：
                - ema_period: EMA周期，默认21
                - sma_period: SMA周期，默认200
                - support_threshold: 支撑阈值，默认0.05
        """
        self.logger = logging.getLogger('cryptoMlStrategy.indicators.bull_market_support')
        
        # 默认配置
        self.config = {
            'ema_period': 21,
            'sma_period': 200,
            'support_threshold': 0.05
        }
        
        # 更新用户配置
        if config:
            self.config.update(config)
            
        self.logger.info("Bull Market Support Band特征提取器初始化完成")

    def calculate_bmsb(self, df: pd.DataFrame, price_col: str = 'close') -> pd.DataFrame:
        """
        计算Bull Market Support Band指标
        
        Args:
            df: 价格数据DataFrame
            price_col: 价格列名称
            
        Returns:
            添加了BMSB指标的DataFrame
        """
        result_df = df.copy()
        
        # 获取参数
        ema_period = self.config['ema_period']
        sma_period = self.config['sma_period']
        
        # 确保数据长度足够计算长期MA
        if len(result_df) < sma_period:
            self.logger.warning(f"数据长度不足以计算{sma_period}日SMA")
            return df
        
        # 计算EMA和SMA
        result_df[f'bmsb_ema{ema_period}'] = result_df[price_col].ewm(span=ema_period, adjust=False).mean()
        result_df[f'bmsb_sma{sma_period}'] = result_df[price_col].rolling(window=sma_period).mean()
        
        # 计算支撑带上界和下界
        # 上界是21日EMA，下界是200日SMA
        result_df['bmsb_upper_band'] = result_df[f'bmsb_ema{ema_period}']
        result_df['bmsb_lower_band'] = result_df[f'bmsb_sma{sma_period}']
        
        # 计算当前价格相对于支撑带的位置
        result_df['bmsb_position'] = (result_df[price_col] - result_df['bmsb_lower_band']) / (
            result_df['bmsb_upper_band'] - result_df['bmsb_lower_band']
        )
        
        # 处理上下界相等的情况
        mask = result_df['bmsb_upper_band'] == result_df['bmsb_lower_band']
        result_df.loc[mask, 'bmsb_position'] = 0.5
        
        # 计算距离支撑带的标准化距离（负数表示在支撑带下方）
        upper_distance = (result_df[price_col] - result_df['bmsb_upper_band']) / result_df['bmsb_upper_band']
        lower_distance = (result_df[price_col] - result_df['bmsb_lower_band']) / result_df['bmsb_lower_band']
        
        result_df['bmsb_upper_distance'] = upper_distance
        result_df['bmsb_lower_distance'] = lower_distance
        
        # 计算支撑带宽度，表示波动性
        result_df['bmsb_band_width'] = (result_df['bmsb_upper_band'] - result_df['bmsb_lower_band']) / result_df['bmsb_lower_band']
        
        return result_df

    def identify_support_bounce(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        识别从支撑带反弹的情况
        
        Args:
            df: 包含BMSB指标的DataFrame
            
        Returns:
            添加了支撑反弹特征的DataFrame
        """
        result_df = df.copy()
        support_threshold = self.config['support_threshold']
        
        # 检查必要的列是否存在
        required_cols = ['bmsb_lower_distance', 'bmsb_upper_distance', 'bmsb_position']
        if not all(col in result_df.columns for col in required_cols):
            self.logger.warning("缺少必要的BMSB指标列，无法计算支撑反弹")
            return df
        
        # 计算价格是否接近支撑带下界
        result_df['bmsb_near_lower_band'] = result_df['bmsb_lower_distance'].abs() < support_threshold
        
        # 计算价格是否接近支撑带上界
        result_df['bmsb_near_upper_band'] = result_df['bmsb_upper_distance'].abs() < support_threshold
        
        # 计算价格是否在支撑带内
        result_df['bmsb_inside_band'] = (result_df['bmsb_position'] >= 0) & (result_df['bmsb_position'] <= 1)
        
        # 计算支撑带反弹信号
        # 当价格从下方接近支撑带下界并开始上涨时，视为支撑反弹
        
        # 计算价格短期动量（5日价格变化率）
        if 'close' in result_df.columns:
            result_df['price_momentum_5d'] = result_df['close'].pct_change(5)
            
            # 识别反弹信号
            result_df['bmsb_bounce_signal'] = np.where(
                (result_df['bmsb_near_lower_band']) & (result_df['price_momentum_5d'] > 0),
                1,  # 从下方反弹
                np.where(
                    (result_df['bmsb_near_upper_band']) & (result_df['price_momentum_5d'] < 0),
                    -1,  # 从上方反弹（可能的阻力）
                    0  # 无明显反弹
                )
            )
            
            # 计算反弹强度
            result_df['bmsb_bounce_strength'] = np.where(
                result_df['bmsb_bounce_signal'] != 0,
                abs(result_df['price_momentum_5d']),
                0
            )
        
        return result_df

    def calculate_trend_state(self, df: pd.DataFrame, price_col: str = 'close') -> pd.DataFrame:
        """
        计算市场趋势状态
        
        Args:
            df: 包含BMSB指标的DataFrame
            price_col: 价格列名称
            
        Returns:
            添加了趋势状态特征的DataFrame
        """
        result_df = df.copy()
        
        # 检查必要的列是否存在
        required_cols = ['bmsb_upper_band', 'bmsb_lower_band', 'bmsb_position']
        if not all(col in result_df.columns for col in required_cols):
            self.logger.warning("缺少必要的BMSB指标列，无法计算趋势状态")
            return df
        
        # 计算价格相对于EMA和SMA的位置
        result_df['bmsb_above_ema'] = result_df[price_col] > result_df['bmsb_upper_band']
        result_df['bmsb_above_sma'] = result_df[price_col] > result_df['bmsb_lower_band']
        
        # 定义市场状态:
        # 1: 强势牛市 - 价格在EMA21上方
        # 0: 弱势牛市/横盘 - 价格在SMA200和EMA21之间
        # -1: 熊市/调整 - 价格在SMA200下方
        result_df['bmsb_market_state'] = np.where(
            result_df['bmsb_above_ema'],
            1,  # 强势牛市
            np.where(
                result_df['bmsb_above_sma'],
                0,  # 弱势牛市/横盘
                -1  # 熊市/调整
            )
        )
        
        # 计算市场状态变化
        result_df['bmsb_state_change'] = result_df['bmsb_market_state'].diff()
        
        # 计算牛市支撑概率
        # 当价格在支撑带内或上方时，牛市支撑概率较高
        result_df['bmsb_support_probability'] = np.where(
            result_df['bmsb_market_state'] == 1,
            0.9,  # 强势牛市，支撑概率高
            np.where(
                result_df['bmsb_market_state'] == 0,
                0.6,  # 弱势牛市，支撑概率中等
                0.1   # 熊市，支撑概率低
            )
        )
        
        # 优化支撑概率，考虑反弹信号
        if 'bmsb_bounce_signal' in result_df.columns:
            # 有反弹信号时提高支撑概率
            result_df['bmsb_support_probability'] = np.where(
                result_df['bmsb_bounce_signal'] == 1,
                np.minimum(result_df['bmsb_support_probability'] + 0.2, 1.0),
                result_df['bmsb_support_probability']
            )
        
        return result_df

    def generate_signals(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        基于BMSB指标生成交易信号
        
        Args:
            df: 包含BMSB指标的DataFrame
            
        Returns:
            添加了信号特征的DataFrame
        """
        result_df = df.copy()
        
        # 检查必要的列是否存在
        required_cols = ['bmsb_market_state', 'bmsb_bounce_signal', 'bmsb_position']
        if not all(col in result_df.columns for col in required_cols):
            self.logger.warning("缺少必要的BMSB指标列，无法生成完整信号")
            return df
        
        # 生成BMSB基础信号
        # 1: 买入信号 - 牛市中的支撑反弹
        # 0: 中性信号 - 无明确方向
        # -1: 卖出信号 - 跌破支撑或熊市中的反弹
        
        result_df['bmsb_signal'] = np.where(
            (result_df['bmsb_market_state'] >= 0) & (result_df['bmsb_bounce_signal'] == 1),
            1,  # 牛市中的支撑反弹，买入信号
            np.where(
                (result_df['bmsb_market_state'] < 0) | 
                ((result_df['bmsb_market_state'] == 0) & (result_df['bmsb_bounce_signal'] == -1)),
                -1,  # 熊市或弱势牛市中的阻力反弹，卖出信号
                0  # 中性信号
            )
        )
        
        # 计算信号强度
        if 'bmsb_bounce_strength' in result_df.columns:
            result_df['bmsb_signal_strength'] = np.where(
                result_df['bmsb_signal'] != 0,
                result_df['bmsb_bounce_strength'],
                0
            )
        
        # 计算综合指标
        result_df['bmsb_indicator'] = result_df['bmsb_market_state'] * 0.5 + result_df['bmsb_position'] * 0.5
        
        return result_df

    def extract_features(self, df: pd.DataFrame, price_col: str = 'close') -> pd.DataFrame:
        """
        从DataFrame中提取BMSB特征
        
        Args:
            df: 包含价格数据的DataFrame
            price_col: 价格列名称
            
        Returns:
            添加了BMSB特征的DataFrame
        """
        if df is None or df.empty:
            return df
            
        try:
            # 1. 计算基本BMSB指标
            result_df = self.calculate_bmsb(df, price_col)
            
            # 2. 识别支撑反弹
            result_df = self.identify_support_bounce(result_df)
            
            # 3. 计算趋势状态
            result_df = self.calculate_trend_state(result_df, price_col)
            
            # 4. 生成信号
            result_df = self.generate_signals(result_df)
            
            return result_df
            
        except Exception as e:
            self.logger.error(f"BMSB特征提取失败: {e}")
            return df