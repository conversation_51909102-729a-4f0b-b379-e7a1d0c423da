<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (21) on Fri Apr 04 16:06:08 CST 2025 -->
<title>所有类和接口 (io.github.binance:binance-futures-connector-java 3.0.5 API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-04-04">
<meta name="description" content="class index">
<meta name="generator" content="javadoc/AllClassesIndexWriter">
<link rel="stylesheet" type="text/css" href="stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="script.js"></script>
<script type="text/javascript" src="script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="script-dir/jquery-ui.min.js"></script>
</head>
<body class="all-classes-index-page">
<script type="text/javascript">var pathtoroot = "./";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="切换导航链接"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="index.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="overview-tree.html">树</a></li>
<li><a href="index-all.html">索引</a></li>
<li><a href="help-doc.html#all-classes">帮助</a></li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list"></div>
<div class="nav-list-search"><a href="search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="搜索">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 title="所有类和接口" class="title">所有类和接口</h1>
</div>
<div id="all-classes-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="all-classes-table-tab0" role="tab" aria-selected="true" aria-controls="all-classes-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('all-classes-table', 'all-classes-table', 2)" class="active-table-tab">所有类和接口</button><button id="all-classes-table-tab1" role="tab" aria-selected="false" aria-controls="all-classes-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('all-classes-table', 'all-classes-table-tab1', 2)" class="table-tab">接口</button><button id="all-classes-table-tab2" role="tab" aria-selected="false" aria-controls="all-classes-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('all-classes-table', 'all-classes-table-tab2', 2)" class="table-tab">类</button></div>
<div id="all-classes-table.tabpanel" role="tabpanel">
<div class="summary-table two-column-summary" aria-labelledby="all-classes-table-tab0">
<div class="table-header col-first">类</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/binance/connector/futures/client/impl/futures/Account.html" title="com.binance.connector.futures.client.impl.futures中的类">Account</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Trade Endpoints</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/binance/connector/futures/client/impl/cm_futures/CMAccount.html" title="com.binance.connector.futures.client.impl.cm_futures中的类">CMAccount</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Coin-Margined Trade Endpoints</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/binance/connector/futures/client/impl/CMFuturesClientImpl.html" title="com.binance.connector.futures.client.impl中的类">CMFuturesClientImpl</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/binance/connector/futures/client/impl/cm_futures/CMMarket.html" title="com.binance.connector.futures.client.impl.cm_futures中的类">CMMarket</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Coin-Margined Market Endpoints</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/binance/connector/futures/client/impl/cm_futures/CMPortfolioMargin.html" title="com.binance.connector.futures.client.impl.cm_futures中的类">CMPortfolioMargin</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Coin-Margined Portfolio Margin Endpoints</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/binance/connector/futures/client/impl/cm_futures/CMUserData.html" title="com.binance.connector.futures.client.impl.cm_futures中的类">CMUserData</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Coin-Margined User Data Streams Endpoints</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/binance/connector/futures/client/impl/CMWebsocketClientImpl.html" title="com.binance.connector.futures.client.impl中的类">CMWebsocketClientImpl</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">COIN-M Websocket Streams</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab1"><a href="com/binance/connector/futures/client/FuturesClient.html" title="com.binance.connector.futures.client中的接口">FuturesClient</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab1">&nbsp;</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/binance/connector/futures/client/impl/FuturesClientImpl.html" title="com.binance.connector.futures.client.impl中的类">FuturesClientImpl</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/binance/connector/futures/client/impl/futures/Market.html" title="com.binance.connector.futures.client.impl.futures中的类">Market</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Market Endpoints</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/binance/connector/futures/client/impl/futures/PortfolioMargin.html" title="com.binance.connector.futures.client.impl.futures中的类">PortfolioMargin</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Portfolio Margin Endpoints</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/binance/connector/futures/client/impl/um_futures/UMAccount.html" title="com.binance.connector.futures.client.impl.um_futures中的类">UMAccount</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">USDⓈ-Margined Trade Endpoints</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/binance/connector/futures/client/impl/UMFuturesClientImpl.html" title="com.binance.connector.futures.client.impl中的类">UMFuturesClientImpl</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/binance/connector/futures/client/impl/um_futures/UMMarket.html" title="com.binance.connector.futures.client.impl.um_futures中的类">UMMarket</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">USDⓈ-Margined Market Endpoints</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/binance/connector/futures/client/impl/um_futures/UMUserData.html" title="com.binance.connector.futures.client.impl.um_futures中的类">UMUserData</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">USDⓈ-Margined User Data Streams Endpoints</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/binance/connector/futures/client/impl/UMWebsocketClientImpl.html" title="com.binance.connector.futures.client.impl中的类">UMWebsocketClientImpl</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">USDⓈ-M  Websocket Streams</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/binance/connector/futures/client/impl/futures/UserData.html" title="com.binance.connector.futures.client.impl.futures中的类">UserData</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">User Data Streams Endpoints</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab1"><a href="com/binance/connector/futures/client/WebsocketClient.html" title="com.binance.connector.futures.client中的接口">WebsocketClient</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab1">&nbsp;</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/binance/connector/futures/client/impl/WebsocketClientImpl.html" title="com.binance.connector.futures.client.impl中的类">WebsocketClientImpl</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Futures Websocket Streams</div>
</div>
</div>
</div>
</div>
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &#169; 2025. All rights reserved.</small></p>
</footer>
</div>
</div>
</body>
</html>
