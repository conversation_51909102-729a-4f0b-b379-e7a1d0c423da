package com.crypto.trading.sdk.exception;

import com.crypto.trading.common.exception.ErrorCode;

/**
 * 客户端异常类
 * 表示客户端请求错误导致的异常
 */
public class ClientException extends ApiException {

    private static final long serialVersionUID = 1L;

    /**
     * HTTP状态码
     */
    private final int httpStatusCode;

    /**
     * 构造函数
     *
     * @param code 错误代码
     * @param message 错误消息
     */
    public ClientException(int code, String message) {
        super(code, message);
        this.httpStatusCode = 0; // Default value, actual HTTP status code should be set
    }

    /**
     * 构造函数
     *
     * @param code 错误代码
     * @param message 错误消息
     * @param httpStatusCode HTTP状态码
     */
    public ClientException(int code, String message, int httpStatusCode) {
        super(code, message);
        this.httpStatusCode = httpStatusCode;
    }

    /**
     * 构造函数
     *
     * @param code 错误代码
     * @param message 错误消息
     * @param cause 异常原因
     */
    public ClientException(int code, String message, Throwable cause) {
        super(code, message, cause);
        this.httpStatusCode = 0; // Default value, actual HTTP status code should be set
    }

    /**
     * 构造函数
     *
     * @param errorCode 错误码枚举
     * @param message 错误消息
     */
    public ClientException(ErrorCode errorCode, String message) {
        super(errorCode, message);
        this.httpStatusCode = 0;
    }

    /**
     * 获取HTTP状态码
     *
     * @return HTTP状态码
     */
    public int getHttpStatusCode() {
        return httpStatusCode;
    }

    @Override
    public String toString() {
        return "ClientException{" +
                "errorCode=" + getCode() +
                ", errorMessage='" + getErrorMessage() + '\'' +
                ", httpStatusCode=" + httpStatusCode +
                '}';
    }
} 