<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>HttpClientSingleton.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">io.github.binance:binance-futures-connector-java</a> &gt; <a href="index.source.html" class="el_package">com.binance.connector.futures.client.utils</a> &gt; <span class="el_source">HttpClientSingleton.java</span></div><h1>HttpClientSingleton.java</h1><pre class="source lang-java linenums">package com.binance.connector.futures.client.utils;

import okhttp3.OkHttpClient;
import java.net.Proxy;

public final class HttpClientSingleton {
<span class="fc" id="L7">    private static OkHttpClient httpClient = null;</span>

    private HttpClientSingleton() {
    }

    public static OkHttpClient getHttpClient() {
<span class="nc bnc" id="L13" title="All 2 branches missed.">        if (httpClient == null) {</span>
<span class="nc" id="L14">            createHttpClient(null);</span>
        }
<span class="nc" id="L16">        return httpClient;</span>
    }

    public static OkHttpClient getHttpClient(ProxyAuth proxy) {
<span class="fc bfc" id="L20" title="All 2 branches covered.">        if (httpClient == null) {</span>
<span class="fc" id="L21">            createHttpClient(proxy);</span>
        } else {
<span class="fc" id="L23">            verifyHttpClient(proxy);</span>
        }
<span class="fc" id="L25">        return httpClient;</span>
    }

    private static void createHttpClient(ProxyAuth proxy) {
<span class="pc bpc" id="L29" title="1 of 2 branches missed.">        if (proxy == null) {</span>
<span class="fc" id="L30">            httpClient = new OkHttpClient();</span>
        } else {
<span class="nc bnc" id="L32" title="All 2 branches missed.">            if (proxy.getAuth() == null) {</span>
<span class="nc" id="L33">                httpClient = new OkHttpClient.Builder().proxy(proxy.getProxy()).build();</span>
            } else {
<span class="nc" id="L35">                httpClient = new OkHttpClient.Builder().proxy(proxy.getProxy()).proxyAuthenticator(proxy.getAuth()).build();</span>
            }
        }
<span class="fc" id="L38">    }</span>

    private static void verifyHttpClient(ProxyAuth proxy) {
<span class="fc" id="L41">        Proxy prevProxy = httpClient.proxy();</span>

<span class="pc bpc" id="L43" title="5 of 8 branches missed.">        if ((proxy != null &amp;&amp; !proxy.getProxy().equals(prevProxy)) || (proxy == null &amp;&amp; prevProxy != null)) {</span>
<span class="nc" id="L44">            createHttpClient(proxy);</span>
        }
<span class="fc" id="L46">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.7.202105040129</span></div></body></html>