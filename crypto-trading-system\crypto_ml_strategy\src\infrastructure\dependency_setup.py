#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
依赖设置模块

负责新服务的解析和初始化逻辑，集成内存监控、错误恢复、
缓存服务、技术指标服务和预测引擎服务。
"""

from typing import Dict, Any, Optional
from loguru import logger

from .infrastructure.memory_monitor import MemoryMonitor
from .infrastructure.async_error_recovery import AsyncErrorRecoveryService
from .infrastructure.intelligent_cache import IntelligentCacheService, CacheConfig
from .services.technical_indicator_service import TechnicalIndicatorService
from .services.prediction_engine_service import PredictionEngineService
from .infrastructure.dependency_container import DependencyContainer


class EnhancedDependencySetup:
    """
    增强的依赖设置类
    
    负责初始化和配置新集成的5个核心服务。
    """
    
    def __init__(self, container: DependencyContainer):
        """
        初始化依赖设置
        
        Args:
            container: 依赖注入容器
        """
        self.container = container
        self._services_initialized = False
        
        logger.info("增强依赖设置初始化完成")
    
    def setup_enhanced_services(self) -> Dict[str, Any]:
        """
        设置增强服务
        
        Returns:
            服务实例字典
        """
        if self._services_initialized:
            logger.warning("增强服务已经初始化")
            return self._get_service_instances()
        
        try:
            logger.info("开始设置增强服务...")
            
            # 1. 初始化内存监控服务
            memory_monitor = self._setup_memory_monitor()
            
            # 2. 初始化异步错误恢复服务
            async_error_recovery = self._setup_async_error_recovery()
            
            # 3. 初始化智能缓存服务
            cache_service = self._setup_intelligent_cache()
            
            # 4. 初始化技术指标服务
            technical_indicator_service = self._setup_technical_indicator_service()
            
            # 5. 初始化预测引擎服务
            prediction_engine_service = self._setup_prediction_engine_service()
            
            # 注册到容器
            self._register_services_to_container({
                'memory_monitor': memory_monitor,
                'async_error_recovery': async_error_recovery,
                'cache_service': cache_service,
                'technical_indicator_service': technical_indicator_service,
                'prediction_engine_service': prediction_engine_service
            })
            
            self._services_initialized = True
            logger.info("增强服务设置完成")
            
            return self._get_service_instances()
            
        except Exception as e:
            logger.error(f"增强服务设置失败: {e}")
            raise
    
    def _setup_memory_monitor(self) -> MemoryMonitor:
        """设置内存监控服务"""
        logger.debug("初始化内存监控服务...")
        
        memory_monitor = MemoryMonitor(
            monitoring_interval=30.0,
            memory_threshold=80.0,
            leak_detection_window=10
        )
        
        logger.info("内存监控服务初始化完成")
        return memory_monitor
    
    def _setup_async_error_recovery(self) -> AsyncErrorRecoveryService:
        """设置异步错误恢复服务"""
        logger.debug("初始化异步错误恢复服务...")
        
        async_error_recovery = AsyncErrorRecoveryService()
        
        logger.info("异步错误恢复服务初始化完成")
        return async_error_recovery
    
    def _setup_intelligent_cache(self) -> IntelligentCacheService:
        """设置智能缓存服务"""
        logger.debug("初始化智能缓存服务...")
        
        cache_config = CacheConfig(
            max_size=1000,
            max_memory_mb=100,
            default_ttl=300.0,  # 5分钟默认TTL
            enable_compression=True,
            enable_prefetch=True
        )
        
        cache_service = IntelligentCacheService(cache_config)
        
        logger.info("智能缓存服务初始化完成")
        return cache_service
    
    def _setup_technical_indicator_service(self) -> TechnicalIndicatorService:
        """设置技术指标服务"""
        logger.debug("初始化技术指标服务...")
        
        technical_indicator_service = TechnicalIndicatorService()
        
        logger.info("技术指标服务初始化完成")
        return technical_indicator_service
    
    def _setup_prediction_engine_service(self) -> PredictionEngineService:
        """设置预测引擎服务"""
        logger.debug("初始化预测引擎服务...")
        
        prediction_engine_service = PredictionEngineService()
        
        logger.info("预测引擎服务初始化完成")
        return prediction_engine_service
    
    def _register_services_to_container(self, services: Dict[str, Any]) -> None:
        """将服务注册到容器"""
        for service_name, service_instance in services.items():
            # 注册服务实例
            self.container.register_instance(service_name, service_instance)
            logger.debug(f"服务 {service_name} 已注册到容器")
    
    def _get_service_instances(self) -> Dict[str, Any]:
        """获取服务实例"""
        return {
            'memory_monitor': self.container.resolve('memory_monitor'),
            'async_error_recovery': self.container.resolve('async_error_recovery'),
            'cache_service': self.container.resolve('cache_service'),
            'technical_indicator_service': self.container.resolve('technical_indicator_service'),
            'prediction_engine_service': self.container.resolve('prediction_engine_service')
        }