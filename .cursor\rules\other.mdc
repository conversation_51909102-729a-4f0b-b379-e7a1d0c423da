---
description: 
globs: 
alwaysApply: false
---
## 开发要求与技术规范

### 技术栈要求

1. **JDK版本要求**
   - 使用JDK21，并充分利用虚拟线程(Virtual Threads)提升系统性能
   - 所有异步操作优先使用虚拟线程实现
   - 合理利用JDK21的新特性优化系统性能

2. **配置管理**
   - 避免硬编码，所有配置项需通过配置文件或常量类进行管理
   - 使用application.yml或application.properties进行外部配置
   - 敏感配置通过环境变量或配置中心管理

3. **设计模式与架构**
   - 合理应用设计模式，提升代码的可维护性和扩展性
   - 采用分层架构：Controller → Service → Repository
   - 应用工厂模式、策略模式、观察者模式等提升代码质量

4. **项目管理**
   - 使用Maven进行项目管理，确保依赖的统一性和版本的可控性
   - 统一版本管理，使用dependencyManagement控制版本
   - 模块化设计，合理拆分common、core、web等模块

### 数据存储与持久化

5. **数据库选择**
   - 使用MySQL进行数据存储，特别是异步消息的存储
   - 合理设计数据库表结构，包含索引优化
   - 支持读写分离和主从复制配置

6. **ORM框架**
   - 使用MyBatis-Plus进行数据持久化，采用XML配置方式
   - 引入mybatis-plus-jsqlparser依赖以支持分页功能
   - 统一数据访问层规范，包含基础CRUD操作

7. **时序数据库**
   - 集成InfluxDB用于存储时序数据和监控指标
   - 支持高性能的时间序列数据写入和查询
   - 提供数据可视化和监控告警功能

### 代码质量要求

8. **编译验证**
   - 所有代码需通过编译验证，确保无编译错误
   - 使用Maven编译插件进行严格的编译检查
   - 集成代码静态分析工具

9. **测试覆盖**
   - 编写完整的测试类，覆盖各个功能模块
   - 单元测试覆盖率不低于80%
   - 包含集成测试和端到端测试

10. **代码注释**
    - 所有代码需包含完整的中文注释，提升代码的可读性
    - 类级别、方法级别、关键逻辑都需要详细注释
    - 使用JavaDoc规范编写API文档

### 开发约束

11. **开发方式限制**
    - 禁止使用终端命令进行代码修改，所有修改需通过代码实现
    - 所有文件操作必须通过desktop-commander MCP工具完成
    - 严格遵循版本控制规范

12. **模块组织**
    - 所有配置、常量、枚举、工具类等统一放置于common模块中
    - 按功能模块进行包结构划分
    - 保持模块间的低耦合高内聚

### 日志与监控

13. **日志规范**
    - 不使用@Slf4j注解，需手动定义Logger对象：`private static final Logger log = LoggerFactory.getLogger(ClassName.class);`
    - 使用标准的日志级别：ERROR、WARN、INFO、DEBUG
    - 统一日志格式和输出规范

14. **关键日志**
    - 打印关键的日志信息，便于问题的排查和定位
    - 包含请求追踪、异常处理、性能监控等关键节点
    - 敏感信息脱敏处理

### 数据处理要求

15. **数据真实性**
    - 禁止使用模拟数据，所有数据需来自真实的市场数据
    - 建立数据校验和清洗机制
    - 确保数据的准确性和完整性

16. **消息队列**
    - 使用Kafka进行消息队列处理，确保数据的异步处理能力
    - 支持消息的可靠传输和重试机制
    - 实现消息的顺序性和幂等性保证

17. **数据流转**
    - 数据要通过内存流转，异步写入MySQL
    - 使用虚拟线程优化I/O密集型操作
    - 实现数据的批量处理和流式处理

### 开发流程约束

18. **需求遵循**
    - 严格遵守用户的所有要求，确保系统的完整性和一致性
    - 任何功能变更都需要明确的需求确认
    - 保持需求的可追溯性

19. **问题解决**
    - 遇到疑问时，需联网搜索解决方案，确保问题的及时解决
    - 建立问题知识库，积累解决方案
    - 优先使用官方文档和最佳实践

20. **功能完整性**
    - 禁止使用TODO标记，所有功能需完整实现
    - 确保每个功能都经过充分测试
    - 提供完整的错误处理和异常管理

### 开发辅助要求

21. **AI辅助开发**
    - 每一步开发过程中，需调用AI模型进行思考，确保方案的最优性
    - 使用sequentialthinking MCP进行方案规划
    - 结合Context7MCP保持开发上下文

22. **工具使用规范**
    - 所有命令行操作都使用desktop-commander MCP
    - 强制使用指定的MCP工具集
    - 保持工具使用的一致性和规范性

23. **文档要求**
    - 大型文件需要进行合理封装和模块化
    - 提供完整的API文档和使用说明
    - 严格按照docs目录中的文档规范执行

24. **沟通规范**
    - 全部回复使用中文
    - 保持专业和准确的技术表达
    - 及时反馈开发进度和问题

### 性能与优化要求

25. **性能优化**
    - 充分利用JDK21虚拟线程特性
    - 实现异步非阻塞的I/O操作
    - 优化数据库查询和缓存策略

26. **内存管理**
    - 合理管理内存使用，避免内存泄漏
    - 实现对象池和连接池优化
    - 监控内存使用情况和GC性能

27. **可扩展性**
    - 设计支持水平扩展的架构
    - 实现无状态服务设计
    - 支持分布式部署和负载均衡

### 安全与合规

28. **安全要求**
    - 实现数据传输加密和存储安全
    - 建立访问控制和权限管理
    - 防范SQL注入、XSS等安全威胁

29. **文档合规**
    - 严格按照docs里面的文档要求执行
    - 保持文档的实时更新和维护
    - 确保文档与代码的一致性

---

## 总结


本协议确保了Claude在开发过程中的严格控制和高质量输出，通过分阶段的工作流程和强制性的工具使用，保证了代码的质量、性能和可维护性。所有开发活动都必须严格遵循上述技术规范和开发约束，确保项目的成功交付。