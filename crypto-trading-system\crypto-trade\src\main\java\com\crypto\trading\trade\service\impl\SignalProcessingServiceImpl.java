package com.crypto.trading.trade.service.impl;

import com.alibaba.fastjson2.JSON;
import com.crypto.trading.common.util.DatabaseExecutor;
import com.crypto.trading.trade.model.avro.SignalType;
import com.crypto.trading.trade.model.avro.TradingSignal;
import com.crypto.trading.trade.model.entity.TradingSignalEntity;
import com.crypto.trading.trade.model.result.SignalProcessResult;
import com.crypto.trading.trade.repository.mapper.TradingSignalMapper;
import com.crypto.trading.trade.service.SignalProcessingService;
import com.crypto.trading.trade.service.SignalValidationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 信号处理服务实现类
 * 
 * <p>负责保存和处理有效的交易信号，并根据信号类型决定是否执行交易</p>
 * <p>使用虚拟线程优化数据库操作性能</p>
 */
@Service
public class SignalProcessingServiceImpl implements SignalProcessingService {

    private static final Logger log = LoggerFactory.getLogger(SignalProcessingServiceImpl.class);

    @Autowired
    private TradingSignalMapper tradingSignalMapper;
    
    @Autowired
    private TransactionTemplate transactionTemplate;

    // 后续会注入交易执行服务
    // @Autowired
    // private TradeExecutionService tradeExecutionService;

    @Value("${crypto.trade.execution.enabled}")
    private boolean tradeExecutionEnabled;

    /**
     * 处理交易信号
     * 使用虚拟线程异步执行数据库操作
     *
     * @param validationResult 验证结果
     * @return 处理结果
     */
    @Override
    public SignalProcessResult processSignal(SignalValidationService.ValidationResult validationResult) {
        if (!validationResult.isValid()) {
            return new SignalProcessResult(false, "信号验证失败: " + validationResult.getErrorMessage(), null);
        }

        TradingSignal signal = validationResult.getSignal();
        String validationId = validationResult.getValidationId();

        try {
            // 使用虚拟线程异步检查信号是否已存在
            CompletableFuture<TradingSignalEntity> existingSignalFuture = DatabaseExecutor.executeAsync(() -> 
                    tradingSignalMapper.findBySignalId(signal.getId()));
            
            // 等待检查结果
            TradingSignalEntity existingSignal = existingSignalFuture.join();
            
            if (existingSignal != null) {
                log.warn("交易信号已存在 [{}]: signalId={}", validationId, signal.getId());
                return new SignalProcessResult(false, "信号已存在", existingSignal);
            }

            // 使用虚拟线程在事务中保存信号
            CompletableFuture<TradingSignalEntity> savedSignalFuture = DatabaseExecutor.executeInTransactionAsync(
                    transactionTemplate,
                    () -> {
                        // 创建数据库实体并保存
                        TradingSignalEntity signalEntity = new TradingSignalEntity(signal);
                        tradingSignalMapper.insert(signalEntity);
                        log.info("交易信号已保存到数据库 [{}]: signalId={}, id={}", validationId, signal.getId(), signalEntity.getId());
                        return signalEntity;
                    });
            
            // 等待保存结果
            TradingSignalEntity signalEntity = savedSignalFuture.join();

            // 如果是HOLD信号，直接标记为已处理
            if (signal.getSignalType() == SignalType.HOLD) {
                // 使用虚拟线程异步标记信号为已处理
                DatabaseExecutor.executeAsync(() -> {
                    markSignalAsProcessed(signalEntity, true, "HOLD信号，无需执行交易");
                    return null;
                });
                
                log.info("HOLD信号已处理 [{}]: signalId={}", validationId, signal.getId());
                return new SignalProcessResult(true, "HOLD信号已处理", signalEntity);
            }

            // 异步执行交易（如果启用）
            if (tradeExecutionEnabled) {
                // 这里暂时注释掉，因为TradeExecutionService还未实现
                // CompletableFuture.runAsync(() -> {
                //     try {
                //         tradeExecutionService.executeSignal(signal);
                //     } catch (Exception e) {
                //         log.error("交易执行异常 [{}]: signalId={}, error={}", validationId, signal.getId(), e.getMessage(), e);
                //         markSignalAsProcessed(signalEntity, false, "交易执行异常: " + e.getMessage());
                //     }
                // }, ThreadUtil.getDefaultVirtualExecutor());
                
                log.info("交易信号已提交执行 [{}]: signalId={}", validationId, signal.getId());
                return new SignalProcessResult(true, "信号已提交执行", signalEntity);
            } else {
                // 交易执行未启用，使用虚拟线程异步标记信号为已处理
                DatabaseExecutor.executeAsync(() -> {
                    markSignalAsProcessed(signalEntity, false, "交易执行功能未启用");
                    return null;
                });
                
                log.info("交易执行功能未启用 [{}]: signalId={}", validationId, signal.getId());
                return new SignalProcessResult(true, "信号已保存，但交易执行功能未启用", signalEntity);
            }
        } catch (Exception e) {
            log.error("处理交易信号异常 [{}]: signalId={}, error={}", validationId, signal.getId(), e.getMessage(), e);
            return new SignalProcessResult(false, "处理异常: " + e.getMessage(), null);
        }
    }

    /**
     * 标记信号为已处理
     * 使用虚拟线程异步执行数据库操作
     *
     * @param signalEntity 信号实体
     * @param success 是否成功
     * @param result 处理结果
     */
    @Override
    public void markSignalAsProcessed(TradingSignalEntity signalEntity, boolean success, String result) {
        // 使用虚拟线程在事务中更新信号状态
        DatabaseExecutor.executeInTransactionAsync(transactionTemplate, () -> {
            try {
                long processedTime = System.currentTimeMillis();
                Map<String, Object> resultMap = new HashMap<>();
                resultMap.put("success", success);
                resultMap.put("message", result);
                resultMap.put("time", processedTime);
                
                tradingSignalMapper.updateProcessStatus(
                        signalEntity.getSignalId(),
                        success,
                        processedTime,
                        JSON.toJSONString(resultMap)
                );
                
                log.info("信号处理状态已更新: signalId={}, success={}, result={}", 
                        signalEntity.getSignalId(), success, result);
            } catch (Exception e) {
                log.error("更新信号处理状态异常: signalId={}, error={}", 
                        signalEntity.getSignalId(), e.getMessage(), e);
                throw e;
            }
        });
    }
}