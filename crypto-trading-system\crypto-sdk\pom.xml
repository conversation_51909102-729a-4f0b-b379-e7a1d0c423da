<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    
    <!-- 父项目引�?-->
    <parent>
        <groupId>com.crypto.trading</groupId>
        <artifactId>crypto-trading</artifactId>
        <version>1.0.0-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <!-- 模块信息 -->
    <artifactId>crypto-sdk</artifactId>
    <packaging>jar</packaging>
    <name>crypto-sdk</name>
    <description>虚拟货币量化交易系统SDK集成模块，封装币安期货API，实现WebSocket连接管理和API限流等功</description>

    <properties>
        <java-websocket.version>1.5.3</java-websocket.version>
    </properties>

    <!-- 模块依赖 -->
    <dependencies>
        <!-- 项目公共模块 -->
        <dependency>
            <groupId>com.crypto.trading</groupId>
            <artifactId>crypto-common</artifactId>
        </dependency>

        <!-- 币安期货API SDK -->
        <dependency>
            <groupId>io.github.binance</groupId>
            <artifactId>binance-futures-connector-java</artifactId>
        </dependency>

        <!-- Java WebSocket 客户�?-->
        <dependency>
            <groupId>org.java-websocket</groupId>
            <artifactId>Java-WebSocket</artifactId>
            <version>${java-websocket.version}</version>
        </dependency>

        <!-- FastJSON2 -->
        <dependency>
            <groupId>com.alibaba.fastjson2</groupId>
            <artifactId>fastjson2</artifactId>
        </dependency>
        
        <!-- javax.annotation -->
        <dependency>
            <groupId>javax.annotation</groupId>
            <artifactId>javax.annotation-api</artifactId>
            <version>1.3.2</version>
        </dependency>

        <!-- Mockito内联依赖，支持final类和静态方法的模拟 -->
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-inline</artifactId>
            <version>${mockito.version}</version>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>
