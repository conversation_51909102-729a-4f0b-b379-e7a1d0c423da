package com.crypto.trading.market.service.impl;

import com.crypto.trading.common.dto.market.KlineDataDTO;
import com.crypto.trading.common.enums.KlineInterval;
import com.crypto.trading.common.util.DateUtil;
import com.crypto.trading.market.converter.MarketDataConverter;
import com.crypto.trading.market.producer.KafkaMessageProducer;
import com.crypto.trading.market.repository.InfluxDBRepository;
import com.crypto.trading.market.service.HistoricalDataService;
import com.crypto.trading.market.service.impl.optimizer.SmartChunkDownloader;
import com.crypto.trading.market.service.impl.optimizer.SmartDownloadOptimizer;
import com.crypto.trading.market.service.impl.resumable.DownloadState;
import com.crypto.trading.market.service.impl.resumable.ResumableDownloadEngine;
import com.crypto.trading.sdk.client.BinanceApiClient;
import com.crypto.trading.sdk.client.UMFuturesApiClientImpl;
import com.crypto.trading.sdk.exception.ApiException;
import com.crypto.trading.sdk.retry.ErrorClassifier;
import com.crypto.trading.sdk.retry.RetryPolicy;
import com.crypto.trading.sdk.response.model.KlineData;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.function.BiFunction;

/**
 * 历史数据服务实现
 * 负责从交易所获取历史K线数据
 */
@Service
public class HistoricalDataServiceImpl implements HistoricalDataService {

    private static final Logger log = LoggerFactory.getLogger(HistoricalDataServiceImpl.class);
    
    @Autowired
    @Qualifier("umFuturesApiClient")
    private BinanceApiClient binanceApiClient;
    
    @Autowired
    private MarketDataConverter marketDataConverter;
    
    @Autowired
    private InfluxDBRepository influxDBRepository;
    
    @Autowired
    private KafkaMessageProducer kafkaMessageProducer;
    
    @Autowired
    private DownloadTaskTracker taskTracker;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    @Autowired
    private ExecutorService virtualThreadExecutor;
    
    @Autowired
    private SmartDownloadOptimizer smartDownloadOptimizer;
    
    @Autowired
    private SmartChunkDownloader smartChunkDownloader;
    
    @Autowired
    private ResumableDownloadEngine resumableDownloadEngine;
    
    // 标准重试策略，适用于大多数API调用
    private final RetryPolicy standardRetryPolicy = RetryPolicy.builder()
            .maxRetries(3)
            .initialBackoffMs(200)
            .maxBackoffMs(10000)
            .backoffMultiplier(2.0)
            .addJitter(true)
            .retryCondition(ErrorClassifier.standardRetryCondition())
            .build();

    /**
     * 获取历史K线数据
     *
     * @param symbol       交易对
     * @param interval     K线间隔
     * @param startTime    开始时间
     * @param endTime      结束时间
     * @param storeToDb    是否存储到数据库
     * @param sendToKafka  是否发送到Kafka
     * @return K线数据列表
     */
    @Override
    public List<KlineDataDTO> getHistoricalKlines(String symbol, KlineInterval interval, LocalDateTime startTime, LocalDateTime endTime, boolean storeToDb, boolean sendToKafka) {
        try {
            log.info("获取历史K线数据: symbol={}, interval={}, startTime={}, endTime={}", symbol, interval.getCode(), startTime, endTime);
            
            // 查询数据库是否有缓存
            if (storeToDb && hasKlineData(symbol, interval, startTime, endTime)) {
                log.info("数据库中存在历史K线数据，从数据库获取: symbol={}, interval={}", symbol, interval.getCode());
                return influxDBRepository.queryKlineData(symbol, interval.getCode(), startTime, endTime);
            }
            
            // 从币安API获取数据
                LinkedHashMap<String, Object> parameters = new LinkedHashMap<>();
            parameters.put("symbol", symbol.toUpperCase());
                parameters.put("interval", interval.getCode());
            parameters.put("startTime", DateUtil.toEpochMilli(startTime));
            parameters.put("endTime", DateUtil.toEpochMilli(endTime));
            parameters.put("limit", 1000);  // 币安API每次最多返回1000条K线数据
            
            String response = binanceApiClient.getKlines(parameters);
            List<KlineDataDTO> klineDTOs = parseKlineData(response, symbol, interval.getCode());
                
                // 存储到数据库
                if (storeToDb) {
                storeKlinesToDb(klineDTOs);
                }
                
                // 发送到Kafka
                if (sendToKafka) {
                    sendKlinesToKafka(klineDTOs);
                }
                
            log.info("历史K线数据获取成功: symbol={}, interval={}, count={}", symbol, interval.getCode(), klineDTOs.size());
            return klineDTOs;
            
        } catch (Exception e) {
            log.error("获取历史K线数据异常: symbol={}, interval={}, error={}", symbol, interval.getCode(), e.getMessage(), e);
            throw new RuntimeException("获取历史K线数据失败", e);
        }
    }

    /**
     * 异步获取历史K线数据
     *
     * @param symbol       交易对
     * @param interval     K线间隔
     * @param startTime    开始时间
     * @param endTime      结束时间
     * @param storeToDb    是否存储到数据库
     * @param sendToKafka  是否发送到Kafka
     * @return 包含K线数据列表的CompletableFuture
     */
    @Override
    public CompletableFuture<List<KlineDataDTO>> getHistoricalKlinesAsync(String symbol, KlineInterval interval, LocalDateTime startTime, LocalDateTime endTime, boolean storeToDb, boolean sendToKafka) {
        return CompletableFuture.supplyAsync(() -> {
            return getHistoricalKlines(symbol, interval, startTime, endTime, storeToDb, sendToKafka);
        }, virtualThreadExecutor);
    }



    /**
     * 批量获取多个交易对的历史K线数据
     *
     * @param symbols      交易对列表
     * @param interval     K线间隔
     * @param startTime    开始时间
     * @param endTime      结束时间
     * @param storeToDb    是否存储到数据库
     * @param sendToKafka  是否发送到Kafka
     * @return 操作结果，包括成功和失败的交易对信息
     */
    @Override
    public String batchDownloadKlines(List<String> symbols, KlineInterval interval, LocalDateTime startTime, LocalDateTime endTime, boolean storeToDb, boolean sendToKafka) {
        // 创建任务，使用默认配置
        String taskId = taskTracker.createTask("kline", symbols, interval.getCode(), 0, startTime, endTime);
        taskTracker.updateTaskStatus(taskId, DownloadTaskTracker.TaskStatus.RUNNING, "开始批量下载K线数据");
        
        // 使用异步线程执行下载任务
        processKlineDownloadTask(taskId, symbols, interval, startTime, endTime, storeToDb, sendToKafka);
        
        return taskId;
    }
    
    /**
     * 批量获取多个交易对的历史K线数据（增强版）
     *
     * @param symbols            交易对列表
     * @param interval           K线间隔
     * @param startTime          开始时间
     * @param endTime            结束时间
     * @param storeToDb          是否存储到数据库
     * @param sendToKafka        是否发送到Kafka
     * @param incrementalDownload 是否增量下载（只下载缺失部分）
     * @param priority           任务优先级
     * @param maxRetries         最大重试次数
     * @param maxConcurrent      最大并发请求数
     * @param chunkSizeHours     分片大小（小时）
     * @return 任务ID
     */
    /**
     * 使用智能分块批量下载多个交易对的历史K线数据
     *
     * @param symbols       交易对列表
     * @param interval      K线间隔
     * @param startTime     开始时间
     * @param endTime       结束时间
     * @param storeToDb     是否存储到数据库
     * @param sendToKafka   是否发送到Kafka
     * @return 任务ID
     */
    @Override
    public String batchDownloadKlinesWithSmartChunks(List<String> symbols, KlineInterval interval, 
                                                  LocalDateTime startTime, LocalDateTime endTime, 
                                                  boolean storeToDb, boolean sendToKafka) {
        try {
            log.info("开始智能分块批量下载K线数据: symbols={}, interval={}, startTime={}, endTime={}", 
                    symbols, interval.getCode(), startTime, endTime);
            
            // 创建下载任务，由系统自动决定最佳参数
            String taskId = taskTracker.createTask("klines-smart", symbols, interval.getCode(), 1000, startTime, endTime);
            
            // 启动下载任务
            CompletableFuture.runAsync(() -> {
                try {
                    // 更新任务状态
                    taskTracker.updateTaskStatus(taskId, DownloadTaskTracker.TaskStatus.RUNNING, 
                            "开始智能分块批量下载");
                    
                    // 对每个交易对执行智能分块下载
                    for (String symbol : symbols) {
                        // 检查任务是否被取消
                        DownloadTaskTracker.TaskInfo taskInfo = taskTracker.getTaskInfo(taskId);
                        if (taskInfo.getStatus() == DownloadTaskTracker.TaskStatus.CANCELED) {
                            log.info("任务已取消: taskId={}", taskId);
                            return;
                        }
                        
                        // 检查任务是否被暂停
                        if (taskInfo.getStatus() == DownloadTaskTracker.TaskStatus.PAUSED) {
                            log.info("任务已暂停: taskId={}", taskId);
                            // 等待恢复信号
                            while (taskTracker.getTaskInfo(taskId).getStatus() == DownloadTaskTracker.TaskStatus.PAUSED) {
                                try {
                                    Thread.sleep(1000);
                                } catch (InterruptedException e) {
                                    Thread.currentThread().interrupt();
                                    log.error("等待任务恢复被中断: taskId={}", taskId, e);
                                    return;
                                }
                                
                                // 再次检查是否被取消
                                if (taskTracker.getTaskInfo(taskId).getStatus() == DownloadTaskTracker.TaskStatus.CANCELED) {
                                    log.info("暂停中的任务已取消: taskId={}", taskId);
                                    return;
                                }
                            }
                        }
                        
                        try {
                            // 更新交易对状态
                            taskTracker.updateSymbolStatus(taskId, symbol, "开始下载");
                            
                            // 执行智能分块下载
                            List<KlineDataDTO> data = getHistoricalKlinesWithSmartChunks(
                                    symbol, interval, startTime, endTime, storeToDb, sendToKafka);
                            
                            // 更新交易对状态
                            taskTracker.updateSymbolStatus(taskId, symbol, String.format("下载成功，获取%d条数据", data.size()));
                            
                            // 更新已处理交易对数量
                            taskInfo = taskTracker.getTaskInfo(taskId);
                            taskTracker.updateTaskStatus(taskId, taskInfo.getStatus(), 
                                String.format("已处理%d/%d个交易对", taskInfo.getProcessedSymbols() + 1, taskInfo.getTotalSymbols()));
                            
                        } catch (Exception e) {
                            log.error("智能分块下载交易对数据异常: taskId={}, symbol={}, error={}",
                                    taskId, symbol, e.getMessage(), e);
                            
                            // 更新交易对状态
                            taskTracker.updateSymbolStatus(taskId, symbol, "下载失败: " + e.getMessage());
                        }
                    }
                    
                    // 所有交易对处理完成
                    taskTracker.updateTaskStatus(taskId, DownloadTaskTracker.TaskStatus.COMPLETED, "任务完成");
                    
                } catch (Exception e) {
                    log.error("批量下载任务异常: taskId={}, error={}", taskId, e.getMessage(), e);
                    taskTracker.updateTaskStatus(taskId, DownloadTaskTracker.TaskStatus.FAILED, "任务失败: " + e.getMessage());
                }
            }, virtualThreadExecutor);
            
            return taskId;
            
        } catch (Exception e) {
            log.error("创建智能分块批量下载任务异常: error={}", e.getMessage(), e);
            throw new RuntimeException("创建智能分块批量下载任务失败", e);
        }
    }
    
    @Override
    public String batchDownloadKlinesEnhanced(List<String> symbols, KlineInterval interval, 
                                           LocalDateTime startTime, LocalDateTime endTime, 
                                           boolean storeToDb, boolean sendToKafka,
                                           boolean incrementalDownload,
                                           DownloadTaskTracker.TaskPriority priority,
                                           int maxRetries, int maxConcurrent, int chunkSizeHours) {
        // 创建增强版任务
        String taskId = taskTracker.createTask(
            "kline", symbols, interval.getCode(), 0, startTime, endTime,
            priority, incrementalDownload, maxRetries, maxConcurrent, chunkSizeHours);
        
        taskTracker.updateTaskStatus(taskId, DownloadTaskTracker.TaskStatus.RUNNING, 
            "开始批量下载K线数据（增强版：优先级=" + priority + 
            "，增量下载=" + incrementalDownload +
            "，分片大小=" + chunkSizeHours + "小时）");
        
        // 使用异步线程执行下载任务
        processKlineDownloadTask(taskId, symbols, interval, startTime, endTime, storeToDb, sendToKafka);
        
        return taskId;
    }
    
    /**
     * 处理K线下载任务
     * 
     * @param taskId       任务ID
     * @param symbols      交易对列表
     * @param interval     K线间隔
     * @param startTime    开始时间
     * @param endTime      结束时间
     * @param storeToDb    是否存储到数据库
     * @param sendToKafka  是否发送到Kafka
     */
    private void processKlineDownloadTask(String taskId, List<String> symbols, KlineInterval interval, 
                                      LocalDateTime startTime, LocalDateTime endTime, 
                                      boolean storeToDb, boolean sendToKafka) {
        virtualThreadExecutor.submit(() -> {
            try {
                DownloadTaskTracker.TaskInfo taskInfo = taskTracker.getTaskInfo(taskId);
                if (taskInfo == null) {
                    log.error("任务不存在: id={}", taskId);
                    return;
                }
                
                if (taskInfo.getChunks().isEmpty()) {
                    // 如果没有分片，按照常规方式下载
                    downloadKlinesNoChunk(taskId, taskInfo, symbols, interval, startTime, endTime, storeToDb, sendToKafka);
                } else {
                    // 使用分片方式下载
                    downloadKlinesWithChunks(taskId, taskInfo, symbols, interval, storeToDb, sendToKafka);
                }
            } catch (Exception e) {
                taskTracker.updateTaskStatus(taskId, DownloadTaskTracker.TaskStatus.FAILED, "批量下载K线数据失败：" + e.getMessage());
                log.error("批量下载K线数据异常: symbols={}, interval={}, error={}", symbols, interval.getCode(), e.getMessage(), e);
            }
        });
    }
    
    /**
     * 不使用分片下载K线数据
     */
    private void downloadKlinesNoChunk(String taskId, DownloadTaskTracker.TaskInfo taskInfo, 
                                  List<String> symbols, KlineInterval interval,
                                    LocalDateTime startTime, LocalDateTime endTime, 
                                    boolean storeToDb, boolean sendToKafka) {
        try {
            List<CompletableFuture<Void>> futures = new ArrayList<>();
            
            for (String symbol : symbols) {
                futures.add(CompletableFuture.runAsync(() -> {
                    try {
                        // 获取并发许可
                        taskInfo.acquireConcurrencyPermit();
                        
                        // 检查任务是否已取消或暂停
                        if (taskInfo.getStatus() == DownloadTaskTracker.TaskStatus.CANCELED) {
                            log.info("任务已取消，跳过处理: taskId={}, symbol={}", taskId, symbol);
                            return;
                        }
                        
                        if (taskInfo.getStatus() == DownloadTaskTracker.TaskStatus.PAUSED) {
                            log.info("任务已暂停，跳过处理: taskId={}, symbol={}", taskId, symbol);
                            return;
                        }
                        
                        // 如果是增量下载，检查数据库中是否已存在数据
                        boolean skipDownload = false;
                        if (taskInfo.isIncrementalDownload() && storeToDb) {
                            skipDownload = hasKlineData(symbol, interval, startTime, endTime);
                            if (skipDownload) {
                                String status = "已跳过，数据库中已存在";
                                taskTracker.updateSymbolStatus(taskId, symbol, status);
                                taskTracker.updateItemCounts(taskId, 0, 1);
                                log.info("跳过下载已存在的K线数据: taskId={}, symbol={}, interval={}", 
                                    taskId, symbol, interval.getCode());
                            }
                        }
                        
                        if (!skipDownload) {
                            // 下载数据
                            List<KlineDataDTO> klines = getHistoricalKlines(
                                symbol, interval, startTime, endTime, storeToDb, sendToKafka);
                            
                            String status = "成功，获取到 " + klines.size() + " 条K线数据";
                            taskTracker.updateSymbolStatus(taskId, symbol, status);
                            taskTracker.updateItemCounts(taskId, klines.size(), 0);
                        }
                    } catch (Exception e) {
                        String status = "失败，错误：" + e.getMessage();
                        taskTracker.updateSymbolStatus(taskId, symbol, status);
                        log.error("下载K线数据异常: taskId={}, symbol={}, interval={}, error={}", 
                            taskId, symbol, interval.getCode(), e.getMessage(), e);
                    } finally {
                        // 释放并发许可
                        taskInfo.releaseConcurrencyPermit();
                    }
                }, virtualThreadExecutor));
            }
            
            // 等待所有任务完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
            
        } catch (Exception e) {
            taskTracker.updateTaskStatus(taskId, DownloadTaskTracker.TaskStatus.FAILED, "批量下载K线数据失败：" + e.getMessage());
            log.error("批量下载K线数据异常: taskId={}, symbols={}, interval={}, error={}", 
                taskId, symbols, interval.getCode(), e.getMessage(), e);
        }
    }
    
    /**
     * 使用分片下载K线数据
     */
    private void downloadKlinesWithChunks(String taskId, DownloadTaskTracker.TaskInfo taskInfo,
                                     List<String> symbols, KlineInterval interval,
                                     boolean storeToDb, boolean sendToKafka) {
        try {
            Map<String, Integer> symbolRetryCount = new ConcurrentHashMap<>();
            List<DownloadTaskTracker.TaskChunk> chunks = taskInfo.getChunks();
            
            // 初始化每个交易对的重试计数
            for (String symbol : symbols) {
                symbolRetryCount.put(symbol, 0);
            }
            
            // 按顺序处理每个分片
            for (DownloadTaskTracker.TaskChunk chunk : chunks) {
                final int chunkIndex = chunk.getChunkIndex();
                LocalDateTime chunkStart = chunk.getChunkStartTime();
                LocalDateTime chunkEnd = chunk.getChunkEndTime();
                
                log.info("开始处理分片: taskId={}, chunkIndex={}, timeRange={} to {}", 
                    taskId, chunkIndex, chunkStart, chunkEnd);
                
                // 更新分片状态
                taskTracker.updateChunkStatus(taskId, chunkIndex, DownloadTaskTracker.TaskStatus.RUNNING, 
                    "开始处理分片 " + chunkIndex);
                
                List<CompletableFuture<Void>> chunkFutures = new ArrayList<>();
                
                for (String symbol : symbols) {
                    final String finalSymbol = symbol;
                    
                    chunkFutures.add(CompletableFuture.runAsync(() -> {
                        try {
                            // 获取并发许可
                            taskInfo.acquireConcurrencyPermit();
                            
                            // 检查任务是否已取消或暂停
                            if (taskInfo.getStatus() == DownloadTaskTracker.TaskStatus.CANCELED) {
                                log.info("任务已取消，跳过分片处理: taskId={}, chunk={}, symbol={}", 
                                    taskId, chunkIndex, finalSymbol);
                                return;
                            }
                            
                            if (taskInfo.getStatus() == DownloadTaskTracker.TaskStatus.PAUSED) {
                                log.info("任务已暂停，跳过分片处理: taskId={}, chunk={}, symbol={}", 
                                    taskId, chunkIndex, finalSymbol);
                                return;
                            }
                            
                            // 如果是增量下载，检查数据库中是否已存在数据
                            boolean skipDownload = false;
                            if (taskInfo.isIncrementalDownload() && storeToDb) {
                                skipDownload = hasKlineData(finalSymbol, interval, chunkStart, chunkEnd);
                                if (skipDownload) {
                                    log.info("跳过下载已存在的分片数据: taskId={}, chunk={}, symbol={}, interval={}", 
                                        taskId, chunkIndex, finalSymbol, interval.getCode());
                                    taskTracker.updateItemCounts(taskId, 0, 1);
                                }
                            }
                            
                            if (!skipDownload) {
                                // 下载数据
                                List<KlineDataDTO> klines = getHistoricalKlines(
                                    finalSymbol, interval, chunkStart, chunkEnd, storeToDb, sendToKafka);
                                
                                log.info("成功获取分片K线数据: taskId={}, chunk={}, symbol={}, count={}", 
                                    taskId, chunkIndex, finalSymbol, klines.size());
                                taskTracker.updateItemCounts(taskId, klines.size(), 0);
                            }
                            
                        } catch (Exception e) {
                            int retryCount = symbolRetryCount.getOrDefault(finalSymbol, 0);
                            if (retryCount < taskInfo.getMaxRetries()) {
                                // 增加重试计数并重新排队
                                symbolRetryCount.put(finalSymbol, retryCount + 1);
                                log.warn("下载K线数据失败，准备重试: taskId={}, chunk={}, symbol={}, retry={}/{}, error={}", 
                                    taskId, chunkIndex, finalSymbol, retryCount + 1, taskInfo.getMaxRetries(), e.getMessage());
                                
                                // 延迟重试
                                try {
                                    Thread.sleep((retryCount + 1) * 2000L); // 指数退避
                                    
                                    // 再次尝试下载
                                    List<KlineDataDTO> klines = getHistoricalKlines(
                                        finalSymbol, interval, chunkStart, chunkEnd, storeToDb, sendToKafka);
                                    
                                    log.info("重试成功获取分片K线数据: taskId={}, chunk={}, symbol={}, retry={}, count={}", 
                                        taskId, chunkIndex, finalSymbol, retryCount + 1, klines.size());
                                    taskTracker.updateItemCounts(taskId, klines.size(), 0);
                                    
                                } catch (Exception retryEx) {
                                    log.error("重试下载K线数据仍然失败: taskId={}, chunk={}, symbol={}, retry={}/{}, error={}", 
                                        taskId, chunkIndex, finalSymbol, retryCount + 1, taskInfo.getMaxRetries(), retryEx.getMessage());
                                    taskTracker.updateSymbolStatus(taskId, finalSymbol, 
                                        "分片 " + chunkIndex + " 失败，错误：" + retryEx.getMessage());
                                }
                            } else {
                                log.error("下载K线数据失败，已达到最大重试次数: taskId={}, chunk={}, symbol={}, maxRetries={}, error={}", 
                                    taskId, chunkIndex, finalSymbol, taskInfo.getMaxRetries(), e.getMessage());
                                taskTracker.updateSymbolStatus(taskId, finalSymbol, 
                                    "分片 " + chunkIndex + " 失败，已重试" + taskInfo.getMaxRetries() + "次");
                            }
                        } finally {
                            // 释放并发许可
                            taskInfo.releaseConcurrencyPermit();
                        }
                    }, virtualThreadExecutor));
                }
                
                // 等待当前分片的所有任务完成
                CompletableFuture.allOf(chunkFutures.toArray(new CompletableFuture[0])).join();
                
                // 更新分片状态为完成
                taskTracker.updateChunkStatus(taskId, chunkIndex, DownloadTaskTracker.TaskStatus.COMPLETED, 
                    "分片 " + chunkIndex + " 处理完成");
                
                // 检查任务状态，如果任务被取消或暂停，则中断处理
                DownloadTaskTracker.TaskStatus currentStatus = taskInfo.getStatus();
                if (currentStatus == DownloadTaskTracker.TaskStatus.CANCELED || 
                    currentStatus == DownloadTaskTracker.TaskStatus.PAUSED) {
                    log.info("停止处理后续分片，任务状态为: {}, taskId={}", currentStatus, taskId);
                    break;
                }
            }
            
            // 所有分片处理完成后的最终更新
            if (taskInfo.getStatus() == DownloadTaskTracker.TaskStatus.RUNNING) {
                taskTracker.updateTaskStatus(taskId, DownloadTaskTracker.TaskStatus.COMPLETED, 
                    "任务完成，下载了 " + taskInfo.getDownloadedItemCount() + " 条数据，跳过了 " + 
                    taskInfo.getSkippedItemCount() + " 条已存在数据");
            }
            
        } catch (Exception e) {
            taskTracker.updateTaskStatus(taskId, DownloadTaskTracker.TaskStatus.FAILED, "分片下载K线数据失败：" + e.getMessage());
            log.error("分片下载K线数据异常: taskId={}, symbols={}, interval={}, error={}", 
                taskId, symbols, interval.getCode(), e.getMessage(), e);
        }
    }



    /**
     * 检查是否存在给定时间范围内的历史数据
     *
     * @param symbol       交易对
     * @param dataType     数据类型（kline）
     * @param interval     K线间隔（仅适用于K线数据）
     * @param startTime    开始时间
     * @param endTime      结束时间
     * @return 是否存在数据
     */
    @Override
    public boolean hasHistoricalData(String symbol, String dataType, String interval, LocalDateTime startTime, LocalDateTime endTime) {
        try {
            if ("kline".equalsIgnoreCase(dataType)) {
                return hasKlineData(symbol, KlineInterval.fromCode(interval), startTime, endTime);
            }
            return false;
        } catch (Exception e) {
            log.error("检查历史数据异常: symbol={}, dataType={}, interval={}, error={}", 
                    symbol, dataType, interval, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 获取下载任务的状态
     *
     * @param taskId 任务ID
     * @return 任务状态信息
     */
    @Override
    public String getTaskStatus(String taskId) {
        DownloadTaskTracker.TaskInfo taskInfo = taskTracker.getTaskInfo(taskId);
        if (taskInfo != null) {
            return taskInfo.toString();
        }
        return "任务不存在: " + taskId;
    }

    /**
     * 获取所有下载任务的状态
     *
     * @return 所有任务状态信息
     */
    @Override
    public List<String> getAllTaskStatus() {
        return taskTracker.getAllTasks().stream()
                .map(DownloadTaskTracker.TaskInfo::toString)
                .collect(Collectors.toList());
    }

    /**
     * 检查是否存在K线数据
     *
     * @param symbol       交易对
     * @param interval     K线间隔
     * @param startTime    开始时间
     * @param endTime      结束时间
     * @return 是否存在数据
     */
    private boolean hasKlineData(String symbol, KlineInterval interval, LocalDateTime startTime, LocalDateTime endTime) {
        return influxDBRepository.hasKlineData(symbol, interval.getCode(), startTime, endTime);
    }



    /**
     * 解析K线数据
     *
     * @param jsonResponse JSON响应
     * @param symbol       交易对
     * @param interval     K线间隔
     * @return K线数据列表
     */
    private List<KlineDataDTO> parseKlineData(String jsonResponse, String symbol, String interval) {
        try {
            List<Object[]> klineArrays = objectMapper.readValue(jsonResponse, new TypeReference<List<Object[]>>() {});
            List<KlineDataDTO> klineDTOs = new ArrayList<>();
            
            for (Object[] klineArray : klineArrays) {
                KlineData klineData = KlineData.fromArray(klineArray);
                klineData.setSymbol(symbol);
                klineData.setInterval(interval);
                
                KlineDataDTO klineDTO = marketDataConverter.convertToKlineDTO(klineData);
                klineDTOs.add(klineDTO);
            }
            
            return klineDTOs;
        } catch (Exception e) {
            log.error("解析K线数据异常: symbol={}, interval={}, error={}", symbol, interval, e.getMessage(), e);
            throw new RuntimeException("解析K线数据失败", e);
        }
    }



    /**
     * 存储K线数据到数据库
     *
     * @param klineDTOs K线数据列表
     */
    private void storeKlinesToDb(List<KlineDataDTO> klineDTOs) {
            for (KlineDataDTO klineDTO : klineDTOs) {
                influxDBRepository.saveKlineData(klineDTO);
            }
    }



    /**
     * 发送K线数据到Kafka
     *
     * @param klineDTOs K线数据列表
     */
    private void sendKlinesToKafka(List<KlineDataDTO> klineDTOs) {
            for (KlineDataDTO klineDTO : klineDTOs) {
            kafkaMessageProducer.sendKlineData(klineDTO);
        }
    }



    /**
     * 暂停下载任务
     *
     * @param taskId 任务ID
     * @return 是否成功暂停
     */
    @Override
    public boolean pauseTask(String taskId) {
        log.info("暂停任务: id={}", taskId);
        return taskTracker.pauseTask(taskId);
    }
    
    /**
     * 恢复下载任务
     *
     * @param taskId 任务ID
     * @return 是否成功恢复
     */
    @Override
    public boolean resumeTask(String taskId) {
        log.info("恢复任务: id={}", taskId);
        boolean success = taskTracker.resumeTask(taskId);
        
        if (success) {
            DownloadTaskTracker.TaskInfo taskInfo = taskTracker.getTaskInfo(taskId);
            if (taskInfo == null) {
                log.error("任务不存在: id={}", taskId);
                return false;
            }
            
            // 从暂停点继续执行任务
            String taskType = taskInfo.getTaskType();
            List<String> symbols = taskInfo.getSymbols();
            LocalDateTime startTime = taskInfo.getStartTime();
            LocalDateTime endTime = taskInfo.getEndTime();
            
            // 根据任务类型继续执行对应任务
            if ("kline".equals(taskType)) {
                KlineInterval interval = KlineInterval.fromCode(taskInfo.getInterval());
                if (interval == null) {
                    log.error("无效的K线间隔: {}, taskId={}", taskInfo.getInterval(), taskId);
                    return false;
                }
                // 异步继续执行K线下载任务
                virtualThreadExecutor.submit(() -> {
                    try {
                        if (taskInfo.getChunks().isEmpty()) {
                            downloadKlinesNoChunk(taskId, taskInfo, symbols, interval, startTime, endTime, true, true);
                        } else {
                            downloadKlinesWithChunks(taskId, taskInfo, symbols, interval, true, true);
                        }
                    } catch (Exception e) {
                        log.error("继续执行K线下载任务异常: taskId={}, error={}", taskId, e.getMessage(), e);
                    }
                });
            } else if ("trade".equals(taskType)) {
                // 异步继续执行交易数据下载任务
                // TODO: 实现交易数据的分片下载功能
            } else if ("depth".equals(taskType)) {
                // 异步继续执行深度数据下载任务
                // TODO: 实现深度数据的分片下载功能
            } else {
                log.error("未知的任务类型: {}, taskId={}", taskType, taskId);
                return false;
            }
        }
        
        return success;
    }
    
    /**
     * 取消下载任务
     *
     * @param taskId 任务ID
     * @return 是否成功取消
     */
    @Override
    public boolean cancelTask(String taskId) {
        log.info("取消任务: id={}", taskId);
        return taskTracker.cancelTask(taskId);
    }

    /**
     * 增强型获取历史K线数据
     * 支持断点续传、请求限流控制、并发下载、数据验证等高级功能
     *
     * @param symbol              交易对
     * @param interval            K线间隔
     * @param startTime           开始时间
     * @param endTime             结束时间
     * @param batchSize           每批次请求的数据量（1-1000）
     * @param maxConcurrentBatches 最大并发批次数
     * @param retryLimit          重试限制次数
     * @param storeToDb           是否存储到数据库
     * @param sendToKafka         是否发送到Kafka
     * @return 获取到的K线数据
     */
    /**
     * 智能分块下载K线数据
     * 使用动态调整的块大小和并行下载优化性能
     *
     * @param symbol       交易对
     * @param interval     K线间隔
     * @param startTime    开始时间
     * @param endTime      结束时间
     * @param storeToDb    是否存储到数据库
     * @param sendToKafka  是否发送到Kafka
     * @return K线数据列表
     */
    @Override
    public List<KlineDataDTO> getHistoricalKlinesWithSmartChunks(String symbol, KlineInterval interval, 
                                                              LocalDateTime startTime, LocalDateTime endTime, 
                                                              boolean storeToDb, boolean sendToKafka) {
        try {
            log.info("智能分块下载历史K线数据: symbol={}, interval={}, startTime={}, endTime={}", 
                    symbol, interval.getCode(), startTime, endTime);
            
            // 使用SmartChunkDownloader进行智能分块下载
            BiFunction<com.crypto.trading.market.service.impl.optimizer.SmartChunkDownloader.TimeChunk, Integer, List<KlineDataDTO>> downloadFunction = 
                    new BiFunction<com.crypto.trading.market.service.impl.optimizer.SmartChunkDownloader.TimeChunk, Integer, List<KlineDataDTO>>() {
                        @Override
                        public List<KlineDataDTO> apply(com.crypto.trading.market.service.impl.optimizer.SmartChunkDownloader.TimeChunk chunk, Integer retryCount) {
                            try {
                                // 创建请求参数
                                LinkedHashMap<String, Object> parameters = new LinkedHashMap<>();
                                parameters.put("symbol", symbol.toUpperCase());
                                parameters.put("interval", interval.getCode());
                                parameters.put("startTime", DateUtil.toEpochMilli(chunk.getStartTime()));
                                parameters.put("endTime", DateUtil.toEpochMilli(chunk.getEndTime()));
                                parameters.put("limit", 1000);  // 币安API每次最多返回1000条K线数据
                                
                                // 记录请求开始时间
                                long startTimeMs = System.currentTimeMillis();
                                
                                // 发送请求
                                String response = binanceApiClient.getKlines(parameters);
                                
                                // 解析响应
                                List<KlineDataDTO> chunkData = parseKlineData(response, symbol, interval.getCode());
                                
                                // 记录成功结果
                                long responseTimeMs = System.currentTimeMillis() - startTimeMs;
                                smartDownloadOptimizer.reportSuccess(symbol, responseTimeMs, chunkData.size());
                                
                                // 记录分块下载结果
                                log.info("分块下载成功: 交易对={}, 间隔={}, 块索引={}, 数据点数={}, 响应时间={}ms",
                                        symbol, interval.getCode(), chunk.getIndex(), chunkData.size(), responseTimeMs);
                                
                                return chunkData;
                            } catch (Exception e) {
                                // 记录失败
                                smartDownloadOptimizer.reportFailure(symbol, 0, e.getMessage());
                                
                                // 根据重试次数决定是否重试
                                if (retryCount < 3) {
                                    // 计算等待时间
                                    long waitTimeMs = smartDownloadOptimizer.calculateRetryWaitTime(retryCount);
                                    
                                    log.warn("分块下载失败，将重试: 交易对={}, 间隔={}, 块索引={}, 重试次数={}, 等待时间={}ms, 错误={}",
                                            symbol, interval.getCode(), chunk.getIndex(), retryCount + 1, waitTimeMs, e.getMessage());
                                    
                                    // 等待一段时间后重试
                                    try {
                                        Thread.sleep(waitTimeMs);
                                        
                                        // 递归调用BiFunction进行重试，增加重试计数
                                        return this.apply(chunk, retryCount + 1);
                                    } catch (InterruptedException ie) {
                                        Thread.currentThread().interrupt();
                                        log.error("重试等待被中断: {}", ie.getMessage());
                                    }
                                }
                                
                                log.error("分块下载失败，已达最大重试次数: 交易对={}, 间隔={}, 块索引={}, 错误={}", 
                                        symbol, interval.getCode(), chunk.getIndex(), e.getMessage(), e);
                                
                                // 达到最大重试次数，返回空列表
                                return new ArrayList<>();
                            }
                        }
                    };
                    
            List<KlineDataDTO> klineDTOs = smartChunkDownloader.downloadWithSmartChunks(
                    symbol, interval, startTime, endTime, downloadFunction, null, null);
            
            // 存储到数据库
            if (storeToDb && !klineDTOs.isEmpty()) {
                storeKlinesToDb(klineDTOs);
            }
            
            // 发送到Kafka
            if (sendToKafka && !klineDTOs.isEmpty()) {
                sendKlinesToKafka(klineDTOs);
            }
            
            log.info("智能分块下载完成: symbol={}, interval={}, count={}", symbol, interval.getCode(), klineDTOs.size());
            return klineDTOs;
            
        } catch (Exception e) {
            log.error("智能分块下载异常: symbol={}, interval={}, error={}", symbol, interval.getCode(), e.getMessage(), e);
            throw new RuntimeException("智能分块下载失败", e);
        }
    }

    @Override
    public List<KlineDataDTO> getHistoricalKlinesEnhanced(String symbol, KlineInterval interval, 
                                                      LocalDateTime startTime, LocalDateTime endTime,
                                                      int batchSize, int maxConcurrentBatches, int retryLimit,
                                                      boolean storeToDb, boolean sendToKafka) {
        log.info("增强型获取历史K线数据: symbol={}, interval={}, startTime={}, endTime={}, batchSize={}, maxConcurrentBatches={}",
                symbol, interval.getCode(), startTime, endTime, batchSize, maxConcurrentBatches);
        
        if (batchSize <= 0 || batchSize > 1000) {
            batchSize = 1000; // 币安API限制
        }
        
        // 检查数据库中是否已存在数据，支持断点续传
        List<KlineDataDTO> existingData = new ArrayList<>();
        LocalDateTime nextStartTime = startTime;
        
        if (storeToDb) {
            existingData = influxDBRepository.queryKlineData(symbol, interval.getCode(), startTime, endTime);
            
            if (!existingData.isEmpty()) {
                log.info("数据库中已存在历史K线数据: symbol={}, interval={}, count={}", 
                        symbol, interval.getCode(), existingData.size());
                
                // 根据已存在的数据计算下一个开始时间点
                Optional<LocalDateTime> maxTime = existingData.stream()
                        .map(KlineDataDTO::getOpenTime)
                        .max(LocalDateTime::compareTo);
                
                if (maxTime.isPresent()) {
                    // 将时间设置为最后一条记录之后
                    nextStartTime = maxTime.get().plusSeconds(interval.getSeconds());
                    
                    log.info("断点续传: 从最后记录之后开始下载: symbol={}, interval={}, nextStartTime={}", 
                            symbol, interval.getCode(), nextStartTime);
                }
                
                // 如果下一个开始时间已经超过结束时间，说明所有数据都已存在，直接返回
                if (nextStartTime.isAfter(endTime)) {
                    log.info("所有数据已存在，无需重新下载: symbol={}, interval={}", symbol, interval.getCode());
                    return existingData;
                }
            }
        }
        
        // 分批下载数据
        List<KlineDataDTO> allData = new ArrayList<>(existingData);
        Set<LocalDateTime> existingTimestamps = existingData.stream()
                .map(KlineDataDTO::getOpenTime)
                .collect(Collectors.toSet());
        
        // 计算需要下载的时间段
        List<TimeRange> timeRanges = calculateTimeRanges(nextStartTime, endTime, interval, batchSize);
        log.info("需要下载的时间段数量: {}", timeRanges.size());
        
        // 使用信号量控制并发数
        Semaphore semaphore = new Semaphore(maxConcurrentBatches);
        
        // 并发下载各个时间段的数据
        List<CompletableFuture<List<KlineDataDTO>>> futures = timeRanges.stream()
                .map(range -> CompletableFuture.supplyAsync(() -> {
                    try {
                        // 获取并发许可
                        semaphore.acquire();
                        
                        // 添加指数退避重试
                        return downloadWithRetry(symbol, interval, range.start, range.end, retryLimit);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        throw new RuntimeException("下载任务被中断", e);
                    } finally {
                        // 释放并发许可
                        semaphore.release();
                    }
                }, virtualThreadExecutor))
                .collect(Collectors.toList());
        
        // 收集所有下载的数据
        for (CompletableFuture<List<KlineDataDTO>> future : futures) {
            try {
                List<KlineDataDTO> batchData = future.join();
                
                // 过滤掉重复数据
                List<KlineDataDTO> uniqueData = batchData.stream()
                        .filter(kline -> !existingTimestamps.contains(kline.getOpenTime()))
                        .collect(Collectors.toList());
                
                if (!uniqueData.isEmpty()) {
                    allData.addAll(uniqueData);
                    existingTimestamps.addAll(uniqueData.stream()
                            .map(KlineDataDTO::getOpenTime)
                            .collect(Collectors.toSet()));
                    
                    // 增量存储到数据库
                    if (storeToDb) {
                        storeKlinesToDb(uniqueData);
                    }
                    
                    // 发送到Kafka
                    if (sendToKafka) {
                        sendKlinesToKafka(uniqueData);
                    }
                }
            } catch (Exception e) {
                log.error("处理批次数据异常: symbol={}, interval={}, error={}", 
                        symbol, interval.getCode(), e.getMessage(), e);
            }
        }
        
        // 数据验证和清洗
        List<KlineDataDTO> validatedData = validateAndCleanData(symbol, interval, allData, startTime, endTime);
        
        log.info("增强型历史K线数据获取完成: symbol={}, interval={}, totalCount={}, newCount={}",
                symbol, interval.getCode(), validatedData.size(), validatedData.size() - existingData.size());
        
        return validatedData;
    }
    
    /**
     * 带重试的数据下载
     *
     * @param symbol      交易对
     * @param interval    K线间隔
     * @param startTime   开始时间
     * @param endTime     结束时间
     * @param retryLimit  重试限制
     * @return 下载的K线数据
     */
    private List<KlineDataDTO> downloadWithRetry(String symbol, KlineInterval interval, 
                                            LocalDateTime startTime, LocalDateTime endTime, 
                                            int retryLimit) {
        int retryCount = 0;
        long waitTimeMs = 1000; // 初始等待时间1秒
        
        while (true) {
            try {
                LinkedHashMap<String, Object> parameters = new LinkedHashMap<>();
                parameters.put("symbol", symbol.toUpperCase());
                parameters.put("interval", interval.getCode());
                parameters.put("startTime", DateUtil.toEpochMilli(startTime));
                parameters.put("endTime", DateUtil.toEpochMilli(endTime));
                parameters.put("limit", 1000);
                
                String response = binanceApiClient.getKlines(parameters);
                return parseKlineData(response, symbol, interval.getCode());
            } catch (Exception e) {
                retryCount++;
                
                if (retryCount > retryLimit) {
                    log.error("下载K线数据失败，超过重试次数: symbol={}, interval={}, startTime={}, endTime={}, error={}",
                            symbol, interval.getCode(), startTime, endTime, e.getMessage());
                    throw new RuntimeException("下载失败，超过重试次数", e);
                }
                
                log.warn("下载K线数据失败，准备第{}次重试: symbol={}, interval={}, startTime={}, endTime={}, error={}",
                        retryCount, symbol, interval.getCode(), startTime, endTime, e.getMessage());
                
                try {
                    // 指数退避策略
                    Thread.sleep(waitTimeMs);
                    waitTimeMs = Math.min(waitTimeMs * 2, 30000); // 最大等待30秒
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException("重试等待被中断", ie);
                }
            }
        }
    }
    
    /**
     * 验证和清洗K线数据
     * 
     * @param symbol    交易对
     * @param interval  K线间隔
     * @param data      K线数据
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 验证和清洗后的数据
     */
    private List<KlineDataDTO> validateAndCleanData(String symbol, KlineInterval interval, 
                                              List<KlineDataDTO> data, 
                                              LocalDateTime startTime, LocalDateTime endTime) {
        // 首先按时间排序
        data.sort(Comparator.comparing(KlineDataDTO::getOpenTime));
        
        List<KlineDataDTO> result = new ArrayList<>();
        Set<LocalDateTime> timeSet = new HashSet<>();
        
        for (KlineDataDTO kline : data) {
            // 检查时间范围
            if (kline.getOpenTime().isBefore(startTime) || kline.getOpenTime().isAfter(endTime)) {
                log.warn("过滤范围外的K线数据: symbol={}, interval={}, time={}", 
                        symbol, interval.getCode(), kline.getOpenTime());
                continue;
            }
            
            // 检查重复数据
            if (!timeSet.add(kline.getOpenTime())) {
                log.warn("过滤重复K线数据: symbol={}, interval={}, time={}", 
                        symbol, interval.getCode(), kline.getOpenTime());
                continue;
            }
            
            // 检查数据合法性
            if (kline.getHigh().compareTo(kline.getLow()) < 0 || kline.getOpen().compareTo(BigDecimal.ZERO) <= 0 || 
                    kline.getClose().compareTo(BigDecimal.ZERO) <= 0 ||
                    kline.getHigh().compareTo(BigDecimal.ZERO) <= 0 || kline.getLow().compareTo(BigDecimal.ZERO) <= 0) {
                log.warn("过滤异常K线数据: symbol={}, interval={}, time={}, open={}, high={}, low={}, close={}",
                        symbol, interval.getCode(), kline.getOpenTime(),
                        kline.getOpen(), kline.getHigh(), kline.getLow(), kline.getClose());
                continue;
            }
            
            result.add(kline);
        }
        
        // 检查数据连续性
        checkDataContinuity(symbol, interval, result);
        
        return result;
    }
    
    /**
     * 检查K线数据的连续性
     * 
     * @param symbol    交易对
     * @param interval  K线间隔
     * @param data      K线数据
     */
    private void checkDataContinuity(String symbol, KlineInterval interval, List<KlineDataDTO> data) {
        if (data.size() < 2) {
            return;
        }
        
        long intervalSeconds = interval.getSeconds();
        
        for (int i = 0; i < data.size() - 1; i++) {
            LocalDateTime current = data.get(i).getOpenTime();
            LocalDateTime next = data.get(i + 1).getOpenTime();
            
            long expectedDiffSeconds = intervalSeconds;
            long actualDiffSeconds = next.toEpochSecond(ZoneOffset.UTC) - current.toEpochSecond(ZoneOffset.UTC);
            
            if (actualDiffSeconds > expectedDiffSeconds) {
                log.warn("检测到K线数据不连续: symbol={}, interval={}, gap={}秒, 从{}到{}",
                        symbol, interval.getCode(), actualDiffSeconds - expectedDiffSeconds,
                        current, next);
            }
        }
    }
    
    /**
     * 计算需要下载的时间段
     * 
     * @param startTime  开始时间
     * @param endTime    结束时间
     * @param interval   K线间隔
     * @param batchSize  每批次的大小
     * @return 时间段列表
     */
    private List<TimeRange> calculateTimeRanges(LocalDateTime startTime, LocalDateTime endTime, 
                                         KlineInterval interval, int batchSize) {
        List<TimeRange> ranges = new ArrayList<>();
        
        long intervalSeconds = interval.getSeconds();
        long batchDurationSeconds = intervalSeconds * batchSize;
        
        LocalDateTime current = startTime;
        while (current.isBefore(endTime)) {
            LocalDateTime batchEnd = current.plusSeconds(batchDurationSeconds);
            
            // 确保不超过结束时间
            if (batchEnd.isAfter(endTime)) {
                batchEnd = endTime;
            }
            
            ranges.add(new TimeRange(current, batchEnd));
            current = batchEnd;
        }
        
        return ranges;
    }
    
    /**
     * 时间范围内部类
     */
    private static class TimeRange {
        final LocalDateTime start;
        final LocalDateTime end;
        
        TimeRange(LocalDateTime start, LocalDateTime end) {
            this.start = start;
            this.end = end;
        }
    }
    
    /**
     * 使用断点续传机制下载历史K线数据
     *
     * @param symbol       交易对
     * @param interval     K线间隔
     * @param startTime    开始时间
     * @param endTime      结束时间
     * @param storeToDb    是否存储到数据库
     * @param sendToKafka  是否发送到Kafka
     * @return K线数据列表
     */
    @Override
    public List<KlineDataDTO> getHistoricalKlinesWithResumption(String symbol, KlineInterval interval, 
                                                              LocalDateTime startTime, LocalDateTime endTime, 
                                                              boolean storeToDb, boolean sendToKafka) {
        try {
            log.info("使用断点续传下载历史K线数据: symbol={}, interval={}, startTime={}, endTime={}", 
                    symbol, interval.getCode(), startTime, endTime);
            
            // 生成任务ID
            String taskId = UUID.randomUUID().toString();
            
            // 准备下载函数
            BiFunction<com.crypto.trading.market.service.impl.optimizer.SmartChunkDownloader.TimeChunk, Integer, List<KlineDataDTO>> downloadFunc = 
                new BiFunction<com.crypto.trading.market.service.impl.optimizer.SmartChunkDownloader.TimeChunk, Integer, List<KlineDataDTO>>() {
                    @Override
                    public List<KlineDataDTO> apply(com.crypto.trading.market.service.impl.optimizer.SmartChunkDownloader.TimeChunk chunk, Integer retryCount) {
                        try {
                            // 创建请求参数
                            LinkedHashMap<String, Object> parameters = new LinkedHashMap<>();
                            parameters.put("symbol", symbol.toUpperCase());
                            parameters.put("interval", interval.getCode());
                            parameters.put("startTime", DateUtil.toEpochMilli(chunk.getStartTime()));
                            parameters.put("endTime", DateUtil.toEpochMilli(chunk.getEndTime()));
                            parameters.put("limit", 1000);  // 币安API每次最多返回1000条K线数据
                            
                            // 记录请求开始时间
                            long startTimeMs = System.currentTimeMillis();
                            
                            // 发送请求
                            String response = binanceApiClient.getKlines(parameters);
                            
                            // 解析响应
                            List<KlineDataDTO> chunkData = parseKlineData(response, symbol, interval.getCode());
                            
                            // 记录成功结果
                            long responseTimeMs = System.currentTimeMillis() - startTimeMs;
                            smartDownloadOptimizer.reportSuccess(symbol, responseTimeMs, chunkData.size());
                            
                            // 记录分块下载结果
                            log.info("分块下载成功: 交易对={}, 间隔={}, 块索引={}, 数据点数={}, 响应时间={}ms",
                                    symbol, interval.getCode(), chunk.getIndex(), chunkData.size(), responseTimeMs);
                            
                            return chunkData;
                        } catch (Exception e) {
                            // 记录失败
                            smartDownloadOptimizer.reportFailure(symbol, 0, e.getMessage());
                            
                            // 根据重试次数决定是否重试
                            if (retryCount < 3) {
                                // 计算等待时间
                                long waitTimeMs = smartDownloadOptimizer.calculateRetryWaitTime(retryCount);
                                
                                log.warn("分块下载失败，将重试: 交易对={}, 间隔={}, 块索引={}, 重试次数={}, 等待时间={}ms, 错误={}",
                                        symbol, interval.getCode(), chunk.getIndex(), retryCount + 1, waitTimeMs, e.getMessage());
                                
                                // 等待一段时间后重试
                                try {
                                    Thread.sleep(waitTimeMs);
                                    
                                    // 递归调用BiFunction进行重试，增加重试计数
                                    return this.apply(chunk, retryCount + 1);
                                } catch (InterruptedException ie) {
                                    Thread.currentThread().interrupt();
                                    log.error("重试等待被中断: {}", ie.getMessage());
                                }
                            }
                            
                            log.error("分块下载异常: 交易对={}, 间隔={}, 块索引={}, 错误={}", 
                                    symbol, interval.getCode(), chunk.getIndex(), e.getMessage(), e);
                            
                            // 达到最大重试次数，抛出异常
                            throw e;
                        }
                    }
                };
                
            // 使用断点续传引擎执行下载
            List<KlineDataDTO> klineDTOs = resumableDownloadEngine.downloadWithResumption(
                    taskId, symbol, interval, startTime, endTime, downloadFunc);
            
            // 存储到数据库
            if (storeToDb && !klineDTOs.isEmpty()) {
                storeKlinesToDb(klineDTOs);
            }
            
            // 发送到Kafka
            if (sendToKafka && !klineDTOs.isEmpty()) {
                sendKlinesToKafka(klineDTOs);
            }
            
            log.info("断点续传下载完成: symbol={}, interval={}, count={}", symbol, interval.getCode(), klineDTOs.size());
            return klineDTOs;
            
        } catch (Exception e) {
            log.error("断点续传下载异常: symbol={}, interval={}, error={}", symbol, interval.getCode(), e.getMessage(), e);
            throw new RuntimeException("断点续传下载失败", e);
        }
    }
    
    /**
     * 使用断点续传批量下载多个交易对的历史K线数据
     *
     * @param symbols       交易对列表
     * @param interval      K线间隔
     * @param startTime     开始时间
     * @param endTime       结束时间
     * @param storeToDb     是否存储到数据库
     * @param sendToKafka   是否发送到Kafka
     * @return 任务ID
     */
    @Override
    public String batchDownloadKlinesWithResumption(List<String> symbols, KlineInterval interval, 
                                                 LocalDateTime startTime, LocalDateTime endTime, 
                                                 boolean storeToDb, boolean sendToKafka) {
        log.info("批量断点续传下载K线数据: symbols={}, interval={}, startTime={}, endTime={}", 
                symbols, interval.getCode(), startTime, endTime);
        
        // 创建下载任务
        String taskId = taskTracker.createTask(
                "批量断点续传下载K线数据: " + symbols.size() + "个交易对, 间隔=" + interval.getCode(),
                symbols.size(),
                DownloadTaskTracker.TaskPriority.NORMAL);
        
        // 在后台线程中处理下载任务
        CompletableFuture.runAsync(() -> {
            try {
                // 获取任务信息
                DownloadTaskTracker.TaskInfo taskInfo = taskTracker.getTaskInfo(taskId);
                
                // 更新任务状态
                taskTracker.updateTaskStatus(taskId, DownloadTaskTracker.TaskStatus.RUNNING, "开始下载");
                
                // 为每个交易对创建下载任务
                int symbolIndex = 0;
                for (String symbol : symbols) {
                    final int currentIndex = symbolIndex++;
                    
                    // 检查任务是否已取消
                    if (taskInfo != null && taskInfo.getStatus() == DownloadTaskTracker.TaskStatus.CANCELLED) {
                        log.info("任务已取消: taskId={}", taskId);
                        break;
                    }
                    
                    // 检查任务是否已暂停
                    if (taskInfo != null && taskInfo.getStatus() == DownloadTaskTracker.TaskStatus.PAUSED) {
                        log.info("任务已暂停: taskId={}", taskId);
                        break;
                    }
                    
                    // 更新任务进度
                    taskTracker.updateTaskProgress(taskId, currentIndex, symbols.size(), 
                            "处理交易对: " + symbol);
                    
                    try {
                        // 使用断点续传引擎下载数据
                        List<KlineDataDTO> data = getHistoricalKlinesWithResumption(
                                symbol, interval, startTime, endTime, storeToDb, sendToKafka);
                        
                        // 更新任务状态
                        taskTracker.updateChunkStatus(taskId, currentIndex, 
                                DownloadTaskTracker.TaskStatus.COMPLETED, 
                                "下载完成: " + symbol + ", 数据点数: " + data.size());
                    } catch (Exception e) {
                        log.error("下载失败: symbol={}, error={}", symbol, e.getMessage(), e);
                        
                        // 更新任务状态
                        taskTracker.updateChunkStatus(taskId, currentIndex, 
                                DownloadTaskTracker.TaskStatus.FAILED, 
                                "下载失败: " + symbol + ", 错误: " + e.getMessage());
                    }
                }
                
                // 更新任务状态
                taskTracker.updateTaskStatus(taskId, DownloadTaskTracker.TaskStatus.COMPLETED, 
                        "批量下载完成, 总交易对: " + symbols.size());
                
            } catch (Exception e) {
                log.error("批量下载任务异常: taskId={}, error={}", taskId, e.getMessage(), e);
                taskTracker.updateTaskStatus(taskId, DownloadTaskTracker.TaskStatus.FAILED, 
                        "批量下载失败: " + e.getMessage());
            }
        }, virtualThreadExecutor);
        
        return taskId;
    }
    }