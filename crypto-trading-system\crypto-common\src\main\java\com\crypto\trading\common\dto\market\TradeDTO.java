package com.crypto.trading.common.dto.market;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 交易数据传输对象
 * <p>
 * 用于在系统各层之间传递交易记录数据
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class TradeDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 交易ID
     */
    private Long id;

    /**
     * 交易对（例如：BTCUSDT）
     */
    private String symbol;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 数量
     */
    private BigDecimal quantity;

    /**
     * 成交额
     */
    private BigDecimal quoteQuantity;

    /**
     * 交易时间
     */
    private LocalDateTime time;

    /**
     * 买方是否是做市商
     */
    private boolean isBuyerMaker;

    /**
     * 是否是最优价格匹配
     */
    private boolean isBestMatch;

    /**
     * 构造函数
     */
    public TradeDTO() {
    }

    /**
     * 构造函数
     *
     * @param id            交易ID
     * @param symbol        交易对
     * @param price         价格
     * @param quantity      数量
     * @param quoteQuantity 成交额
     * @param time          交易时间
     * @param isBuyerMaker  买方是否是做市商
     * @param isBestMatch   是否是最优价格匹配
     */
    public TradeDTO(Long id, String symbol, BigDecimal price, BigDecimal quantity,
                  BigDecimal quoteQuantity, LocalDateTime time, boolean isBuyerMaker,
                  boolean isBestMatch) {
        this.id = id;
        this.symbol = symbol;
        this.price = price;
        this.quantity = quantity;
        this.quoteQuantity = quoteQuantity;
        this.time = time;
        this.isBuyerMaker = isBuyerMaker;
        this.isBestMatch = isBestMatch;
    }

    /**
     * 获取交易ID
     *
     * @return 交易ID
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置交易ID
     *
     * @param id 交易ID
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取交易对
     *
     * @return 交易对
     */
    public String getSymbol() {
        return symbol;
    }

    /**
     * 设置交易对
     *
     * @param symbol 交易对
     */
    public void setSymbol(String symbol) {
        this.symbol = symbol;
    }

    /**
     * 获取价格
     *
     * @return 价格
     */
    public BigDecimal getPrice() {
        return price;
    }

    /**
     * 设置价格
     *
     * @param price 价格
     */
    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    /**
     * 获取数量
     *
     * @return 数量
     */
    public BigDecimal getQuantity() {
        return quantity;
    }

    /**
     * 设置数量
     *
     * @param quantity 数量
     */
    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    /**
     * 获取成交额
     *
     * @return 成交额
     */
    public BigDecimal getQuoteQuantity() {
        return quoteQuantity;
    }

    /**
     * 设置成交额
     *
     * @param quoteQuantity 成交额
     */
    public void setQuoteQuantity(BigDecimal quoteQuantity) {
        this.quoteQuantity = quoteQuantity;
    }

    /**
     * 获取交易时间
     *
     * @return 交易时间
     */
    public LocalDateTime getTime() {
        return time;
    }

    /**
     * 设置交易时间
     *
     * @param time 交易时间
     */
    public void setTime(LocalDateTime time) {
        this.time = time;
    }

    /**
     * 获取买方是否是做市商
     *
     * @return 买方是否是做市商
     */
    public boolean isBuyerMaker() {
        return isBuyerMaker;
    }

    /**
     * 设置买方是否是做市商
     *
     * @param buyerMaker 买方是否是做市商
     */
    public void setBuyerMaker(boolean buyerMaker) {
        isBuyerMaker = buyerMaker;
    }

    /**
     * 获取是否是最优价格匹配
     *
     * @return 是否是最优价格匹配
     */
    public boolean isBestMatch() {
        return isBestMatch;
    }

    /**
     * 设置是否是最优价格匹配
     *
     * @param bestMatch 是否是最优价格匹配
     */
    public void setBestMatch(boolean bestMatch) {
        isBestMatch = bestMatch;
    }

    @Override
    public String toString() {
        return "TradeDTO{" +
                "id=" + id +
                ", symbol='" + symbol + '\'' +
                ", price=" + price +
                ", quantity=" + quantity +
                ", quoteQuantity=" + quoteQuantity +
                ", time=" + time +
                ", isBuyerMaker=" + isBuyerMaker +
                ", isBestMatch=" + isBestMatch +
                '}';
    }
} 