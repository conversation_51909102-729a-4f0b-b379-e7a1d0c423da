spring:
  application:
    name: crypto-market-data-kafka-test
  profiles:
    active: kafka-test

  # Kafka配置
  kafka:
    bootstrap-servers: localhost:9092
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.springframework.kafka.support.serializer.JsonSerializer
      acks: all
      retries: 3
      batch-size: 16384
      buffer-memory: 33554432
      compression-type: lz4
      properties:
        enable.idempotence: true
    consumer:
      group-id: crypto-market-data-test-group
      auto-offset-reset: latest
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.springframework.kafka.support.serializer.JsonDeserializer
      max-poll-records: 100
      auto-commit-interval: 5000
      enable-auto-commit: false
      properties:
        spring.json.trusted.packages: "com.crypto.trading.common.dto.market"

# 市场数据配置
market:
  kafka:
    trade-topic: trade-data-test
    trade-topic-string: trade-data-string-test
    consumer-group: crypto-market-data-test-group
    enable-batch: false
    batch-size: 10
    batch-wait-ms: 1000
  
  # 主题配置
  trade:
    topic: trade-data-test
  kline:
    topic: kline-data-test
  depth:
    topic: depth-data-test

# 数据库配置（测试用）
spring.datasource:
  driver-class-name: org.h2.Driver
  url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
  username: sa
  password: 
  
spring.jpa:
  hibernate:
    ddl-auto: create-drop
  show-sql: true
  database-platform: org.hibernate.dialect.H2Dialect

# InfluxDB配置（测试用 - 可选）
influxdb:
  url: http://localhost:8086
  token: test-token
  org: test-org
  bucket: test-bucket
  enabled: false

# 日志配置
logging:
  level:
    com.crypto.trading: DEBUG
    org.springframework.kafka: INFO
    org.apache.kafka: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"

# 虚拟线程配置
thread:
  virtual:
    enabled: true
    core-pool-size: 10
    max-pool-size: 100
