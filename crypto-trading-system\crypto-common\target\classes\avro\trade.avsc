{"namespace": "com.crypto.trading.common.avro", "type": "record", "name": "TradeData", "doc": "交易数据Avro模式定义", "fields": [{"name": "id", "type": "string", "doc": "唯一消息ID"}, {"name": "timestamp", "type": "long", "doc": "消息时间戳(毫秒)"}, {"name": "type", "type": "string", "doc": "消息类型，固定为TRADE"}, {"name": "source", "type": "string", "doc": "消息来源模块"}, {"name": "version", "type": "string", "doc": "消息版本"}, {"name": "data", "type": {"type": "record", "name": "TradeDataContent", "fields": [{"name": "symbol", "type": "string", "doc": "交易对符号"}, {"name": "tradeId", "type": "long", "doc": "交易ID"}, {"name": "price", "type": "double", "doc": "成交价格"}, {"name": "quantity", "type": "double", "doc": "成交数量"}, {"name": "quoteQuantity", "type": "double", "doc": "报价资产成交数量"}, {"name": "time", "type": "long", "doc": "成交时间(毫秒)"}, {"name": "isBuyerMaker", "type": "boolean", "doc": "是否买方挂单成交"}, {"name": "isBestMatch", "type": "boolean", "doc": "是否最优匹配"}]}, "doc": "交易数据内容"}]}