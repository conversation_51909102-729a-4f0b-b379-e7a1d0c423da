{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## Introduction\n", "\n", "This notebook aims to give you an extremely simple example of how to use Qlib RL. We will show you how to build up a simulator, a policy, and reward function. Then, we will show you how to run RL training & backtest workflows based on the aforementioned componenets.\n", "\n", "This notebook assumes that you are aware of basic concepts of reinforcement learning. If you are unfamiliar with RL, please search for materials (for example, [Wikipedia](https://en.wikipedia.org/wiki/Reinforcement_learning)) to get basic understanding of RL.\n", "\n", "Let's start with the simulator."]}, {"attachments": {"image.png": {"image/png": "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"}}, "cell_type": "markdown", "metadata": {}, "source": ["## Simulator\n", "\n", "![image.png](attachment:image.png)\n", "\n", "As stated in the above figure, environment is one of the two core componenets in the RL ecosystem. The operating logic of the environment is implemented in the **simulator**. In this notebook, we define a primitive simulator based on `qlib.rl.simulator.Simulator`:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from collections import namedtuple\n", "from typing import Any\n", "from qlib.rl.simulator import Simulator\n", "\n", "State = namedtuple(\"State\", [\"value\", \"last_action\"])\n", "\n", "\n", "class SimpleSimulator(Simulator[float, State, float]):\n", "    def __init__(self, initial: float, nsteps: int, **kwargs: Any) -> None:\n", "        super().__init__(initial)\n", "\n", "        self.value = initial\n", "        self.last_action = 0.0\n", "        self.remain_steps = nsteps\n", "\n", "    def step(self, action: float) -> None:\n", "        assert 0.0 <= action <= self.value\n", "        self.last_action = action\n", "        self.remain_steps -= 1\n", "\n", "    def get_state(self) -> State:\n", "        return State(self.value, self.last_action)\n", "\n", "    def done(self) -> bool:\n", "        return self.remain_steps == 0"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This simulator will run `nsteps` steps fixedly. It has a internal `value`, but this value will not be affected by the action. We will talk about what this property does later. The expected action from the agent should be a float number in the range of `[0.0, self.value]`. Each time the `step()` method is called, the simulator will record the action value, and wrap it into the simulator's state (see in `get_state()` method)."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Intepreters & Reward\n", "\n", "There are three items in the process of interaction between environment and agent: **state**, **action**, and **reward**. Let's talk about state and action first. \n", "\n", "Environment and agent may represent state/action in different formats, so a \"format converter\" is needed between them. In Qlib RL, it is called **interpreter**. State interpreter takes states from the environment, and convert it to a format that agent could understand. Action interpreter works in the opposite direction. It takes actions from agent and convert it to a format that environment accepts.\n", "\n", "As we mentioned when introducing the simulator, a state generated by the simulator is a tuple of two float numbers. The state interpreter is defined as follows:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from typing import Tuple\n", "import numpy as np\n", "from gym import spaces\n", "from qlib.rl.interpreter import StateInterpreter\n", "\n", "\n", "class SimpleStateInterpreter(StateInterpreter[Tuple[float, float], np.ndarray]):\n", "    def interpret(self, state: State) -> np.ndarray:\n", "        # Convert state.value to a 1D Numpy array\n", "        # last_action is not used by agents.\n", "        return np.array([state.value], dtype=np.float32)\n", "\n", "    @property\n", "    def observation_space(self) -> spaces.Box:\n", "        return spaces.Box(0, np.inf, shape=(1,), dtype=np.float32)\n", "\n", "\n", "state_interpreter = SimpleStateInterpreter()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["As for the action interpreter, in this notebook, we assume that the agent will not directly generate a float action. Instead, it generates a bucketized value (an integer) because we want to use a categorical model to build the policy. To be concrete, we divide `simulator.value` into N parts evenly, and the agent's action should be an integer in `[0, N]`. The value of this integer will be converted to the float number as follows:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from qlib.rl.interpreter import ActionInterpreter\n", "\n", "\n", "class SimpleActionInterpreter(ActionInterpreter[State, int, float]):\n", "    def __init__(self, n_value: int) -> None:\n", "        self.n_value = n_value\n", "\n", "    @property\n", "    def action_space(self) -> spaces.Discrete:\n", "        return spaces.Discrete(self.n_value + 1)\n", "\n", "    def interpret(self, simulator_state: State, action: int) -> float:\n", "        assert 0 <= action <= self.n_value\n", "        # simulator_state.value is used as the denominator\n", "        return simulator_state.value * (action / self.n_value)\n", "\n", "\n", "action_interpreter = SimpleActionInterpreter(n_value=10)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Finally, let's define the reward function. In this example, we use the proportion of `action` in `value` as the reward. In other words, the larger the action, the larger the reward:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from qlib.rl.reward import Reward\n", "\n", "\n", "class SimpleReward(Reward[State]):\n", "    def reward(self, simulator_state: State) -> float:\n", "        # Use last_action to calculate reward. This is why it should be in the state.\n", "        rew = simulator_state.last_action / simulator_state.value\n", "        return rew\n", "\n", "\n", "reward = SimpleReward()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Agent & Policy\n", "\n", "The last thing we haven't talk about in the RL ecosystem is the agent. In RL, agents take actions follow particular policies, so defining agents is actually equivalent to defining policies. In this example, we define a PPO policy with a naive categorical neural network:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from typing import List\n", "import torch\n", "from torch import nn\n", "from qlib.rl.order_execution import PPO\n", "\n", "\n", "class SimpleFullyConnect(nn.Module):\n", "    def __init__(self, dims: List[int]) -> None:\n", "        super().__init__()\n", "\n", "        self.dims = [1] + dims\n", "        self.output_dim = dims[-1]\n", "\n", "        layers = []\n", "        for in_dim, out_dim in zip(self.dims[:-1], self.dims[1:]):\n", "            layers.append(nn.Linear(in_dim, out_dim))\n", "            layers.append(nn.ReLU())\n", "        self.fc = nn.Sequential(*layers)\n", "\n", "    def forward(self, x: torch.Tensor) -> torch.Tensor:\n", "        return self.fc(x)\n", "\n", "\n", "policy = PPO(\n", "    network=SimpleFullyConnect(dims=[16, 8]),\n", "    obs_space=state_interpreter.observation_space,\n", "    action_space=action_interpreter.action_space,\n", "    lr=0.01,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Dataset\n", "\n", "In order to get sufficient training data, usually we need to build multiple environments in parallel, and collect data from all these environments. To achieve that, we need to create multiple simulators with different initial settings. In Qlib RL, we could use a data set to implement this function:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from torch.utils.data import Dataset\n", "\n", "\n", "class SimpleDataset(Dataset):\n", "    def __init__(self, positions: List[float]) -> None:\n", "        self.positions = positions\n", "\n", "    def __len__(self) -> int:\n", "        return len(self.positions)\n", "\n", "    def __getitem__(self, index: int) -> float:\n", "        return self.positions[index]\n", "\n", "\n", "dataset = SimpleDataset(positions=[10.0, 50.0, 100.0])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Each number in `positions` will be used to create a unique simulator. The creation of simulators will be handle by Qlib RL automatically. The only thing that developers need to do is defining the data set class and create a data set instance with data they want to use."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Training workflow\n", "\n", "Based on all the componenets we already have, it is super easy to launch a training workflow:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "from typing import cast\n", "from qlib.rl.trainer import Checkpoint, train\n", "\n", "NSTEPS = 10\n", "\n", "trainer_kwargs = {\n", "    \"max_iters\": 10,\n", "    \"finite_env_type\": \"dummy\",\n", "    \"callbacks\": [\n", "        Checkpoint(\n", "            dirpath=Path(\"./checkpoints\"),\n", "            every_n_iters=1,\n", "            save_latest=\"copy\",\n", "        )\n", "    ],\n", "}\n", "vessel_kwargs = {\n", "    \"update_kwargs\": {\"batch_size\": 16, \"repeat\": 5},\n", "    \"episode_per_iter\": 50,\n", "}\n", "\n", "print(\"Training started\")\n", "train(\n", "    simulator_fn=lambda position: SimpleSimulator(position, NSTEPS),\n", "    state_interpreter=state_interpreter,\n", "    action_interpreter=action_interpreter,\n", "    policy=policy,\n", "    reward=reward,\n", "    initial_states=cast(List[float], SimpleDataset([10.0, 50.0, 100.0])),\n", "    trainer_kwargs=trainer_kwargs,\n", "    vessel_kwargs=vessel_kwargs,\n", ")\n", "print(\"Training finished\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Backtest workflow\n", "\n", "After the policy is trained, we could let it run in the environment and see its behaviors. This is called \"backtest\":"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from tianshou.data import Batch\n", "\n", "simulator = SimpleSimulator(100.0, NSTEPS)\n", "state = simulator.get_state()\n", "obs = [{\"obs\": state_interpreter.interpret(state)}]\n", "policy_out = policy(Batch(obs))\n", "act = float(action_interpreter.interpret(state, policy_out.act))\n", "\n", "simulator.step(act)\n", "rew = float(reward(simulator.get_state()))\n", "\n", "print(f\"Action = {act:.6f}, Reward = {rew:.6f}.\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.13"}}, "nbformat": 4, "nbformat_minor": 2}