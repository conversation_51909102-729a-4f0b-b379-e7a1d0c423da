"""
数据访问层使用示例

演示如何使用数据访问层的各个组件，包括：
- 统一数据访问服务的使用
- Java API数据获取
- 数据库连接和查询
- 缓存管理
- 实时数据处理
- 数据验证

Author: Crypto ML Strategy Team
Date: 2024-12-19
"""

import asyncio
import logging
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any

# 导入数据访问层组件
from data.data_config import (
    DataAccessConfig, DataSourceConfig, APIConfig, DatabaseConfig, 
    KafkaConfig, CacheConfig, DataSourceType, DatabaseType, CacheType
)
from data.data_access_service import DataAccessService
from data.clients.api_client import JavaAPIClient
from data.clients.database_connector import DatabaseConnector
from data.cache.cache_manager import CacheManager
from data.realtime_processor import RealtimeProcessor, MessageHandler, StreamMessage
from data.quality.data_validator import DataValidator, ValidationLevel

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class ExampleMessageHandler(MessageHandler):
    """示例消息处理器"""
    
    def __init__(self, handler_id: str):
        super().__init__(handler_id)
        self.processed_messages = []
    
    async def process_message_data(self, message: StreamMessage) -> bool:
        """处理消息数据"""
        try:
            logger.info(f"处理消息: {message.message_type.value} from {message.source}")
            self.processed_messages.append(message.to_dict())
            
            # 这里可以添加具体的消息处理逻辑
            # 例如：更新缓存、触发交易信号、记录日志等
            
            return True
        except Exception as e:
            logger.error(f"处理消息失败: {e}")
            return False


async def example_basic_api_client():
    """示例：基础API客户端使用"""
    logger.info("=== 基础API客户端示例 ===")
    
    # 创建API配置
    api_config = APIConfig(
        base_url="http://localhost:8080/api",
        api_key="your_api_key",
        timeout=30,
        max_retries=3
    )
    
    # 创建API客户端
    async with JavaAPIClient(api_config) as client:
        # 健康检查
        health_response = await client.health_check()
        logger.info(f"API健康检查: {health_response.success}")
        
        # 获取市场数据
        market_response = await client.get_market_data(["BTCUSDT", "ETHUSDT"])
        if market_response.success:
            logger.info(f"市场数据获取成功: {len(market_response.data) if market_response.data else 0} 条记录")
        
        # 获取历史数据
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=24)
        
        historical_response = await client.get_historical_data(
            symbol="BTCUSDT",
            start_time=start_time,
            end_time=end_time,
            timeframe="1h"
        )
        
        if historical_response.success and isinstance(historical_response.data, pd.DataFrame):
            logger.info(f"历史数据获取成功: {len(historical_response.data)} 条记录")
            logger.info(f"数据列: {list(historical_response.data.columns)}")
        
        # 获取客户端统计信息
        stats = client.get_statistics()
        logger.info(f"API客户端统计: {stats}")


async def example_database_connector():
    """示例：数据库连接器使用"""
    logger.info("=== 数据库连接器示例 ===")
    
    # 创建数据库配置
    db_config = DatabaseConfig(
        db_type=DatabaseType.MYSQL,
        host="localhost",
        port=3306,
        database="crypto_trading",
        username="root",
        password="password",
        pool_size=5
    )
    
    # 创建数据库连接器
    async with DatabaseConnector(db_config) as connector:
        # 健康检查
        health_ok = await connector.health_check()
        logger.info(f"数据库健康检查: {health_ok}")
        
        if health_ok:
            # 执行查询
            query = "SELECT COUNT(*) as count FROM market_data WHERE symbol = 'BTCUSDT'"
            result = await connector.execute_query(query)
            if result:
                logger.info(f"查询结果: {result}")
            
            # 使用DataFrame查询
            df_query = """
            SELECT symbol, timestamp, close, volume 
            FROM market_data 
            WHERE symbol IN ('BTCUSDT', 'ETHUSDT') 
            AND timestamp >= DATE_SUB(NOW(), INTERVAL 1 DAY)
            ORDER BY timestamp DESC 
            LIMIT 100
            """
            
            df = await connector.execute_dataframe_query(df_query)
            if df is not None and not df.empty:
                logger.info(f"DataFrame查询成功: {len(df)} 行数据")
                logger.info(f"数据概览:\n{df.head()}")
            
            # 获取连接统计
            stats = connector.get_statistics()
            logger.info(f"数据库连接统计: {stats}")


async def example_cache_manager():
    """示例：缓存管理器使用"""
    logger.info("=== 缓存管理器示例 ===")
    
    # 创建缓存配置
    cache_config = CacheConfig(
        cache_type=CacheType.MEMORY,
        default_ttl=3600,
        max_memory="50mb"
    )
    
    # 创建缓存管理器
    cache_manager = CacheManager(cache_config)
    
    try:
        # 存储数据
        test_data = {
            "symbol": "BTCUSDT",
            "price": 45000.0,
            "timestamp": datetime.now().isoformat(),
            "volume": 1234.56
        }
        
        success = await cache_manager.put("test_key", test_data, ttl=300)
        logger.info(f"缓存存储: {success}")
        
        # 获取数据
        cached_data = await cache_manager.get("test_key")
        if cached_data:
            logger.info(f"缓存获取成功: {cached_data}")
        
        # 存储DataFrame
        df = pd.DataFrame({
            'timestamp': pd.date_range('2024-01-01', periods=100, freq='1H'),
            'price': range(100),
            'volume': range(100, 200)
        })
        
        await cache_manager.put("dataframe_key", df, ttl=600)
        cached_df = await cache_manager.get("dataframe_key")
        
        if cached_df is not None:
            logger.info(f"DataFrame缓存成功: {len(cached_df)} 行")
        
        # 健康检查
        health = await cache_manager.health_check()
        logger.info(f"缓存健康检查: {health}")
        
        # 获取统计信息
        stats = cache_manager.get_statistics()
        logger.info(f"缓存统计: {stats}")
        
        # 清理过期缓存
        cleaned = await cache_manager.cleanup_expired()
        logger.info(f"清理过期缓存: {cleaned} 个")
        
    finally:
        await cache_manager.close()


async def example_data_validator():
    """示例：数据验证器使用"""
    logger.info("=== 数据验证器示例 ===")
    
    # 创建数据验证器
    validator = DataValidator()
    
    # 创建测试数据
    test_data = pd.DataFrame({
        'timestamp': pd.date_range('2024-01-01', periods=100, freq='1H'),
        'open': [100 + i for i in range(100)],
        'high': [105 + i for i in range(100)],
        'low': [95 + i for i in range(100)],
        'close': [102 + i for i in range(100)],
        'volume': [1000 + i * 10 for i in range(100)]
    })
    
    # 基础验证
    basic_report = await validator.validate_data(
        test_data,
        data_source="test_source",
        data_type="market_data",
        validation_level=ValidationLevel.BASIC
    )
    
    logger.info(f"基础验证结果: {basic_report.overall_result.value}")
    logger.info(f"验证成功率: {basic_report.success_rate:.2%}")
    logger.info(f"验证时间: {basic_report.validation_time:.3f}s")
    
    # 严格验证
    strict_report = await validator.validate_data(
        test_data,
        data_source="test_source",
        data_type="market_data",
        validation_level=ValidationLevel.STRICT
    )
    
    logger.info(f"严格验证结果: {strict_report.overall_result.value}")
    logger.info(f"问题数量: {len(strict_report.issues)}")
    logger.info(f"建议: {strict_report.recommendations}")
    
    # 创建有问题的数据进行测试
    bad_data = test_data.copy()
    bad_data.loc[10:15, 'high'] = bad_data.loc[10:15, 'low'] - 1  # 高价小于低价
    bad_data.loc[20:25, 'volume'] = -100  # 负数成交量
    
    bad_report = await validator.validate_data(
        bad_data,
        data_source="test_source",
        data_type="market_data",
        validation_level=ValidationLevel.COMPREHENSIVE
    )
    
    logger.info(f"问题数据验证结果: {bad_report.overall_result.value}")
    logger.info(f"失败规则数: {bad_report.failed_rules}")
    logger.info(f"严重问题数: {bad_report.critical_rules}")
    
    # 获取验证统计
    stats = validator.get_validation_statistics()
    logger.info(f"验证器统计: {stats}")


async def example_realtime_processor():
    """示例：实时数据处理器使用"""
    logger.info("=== 实时数据处理器示例 ===")
    
    # 创建缓存管理器
    cache_config = CacheConfig(cache_type=CacheType.MEMORY)
    cache_manager = CacheManager(cache_config)
    
    # 创建实时数据处理器
    realtime_processor = RealtimeProcessor(cache_manager)
    
    try:
        # 添加消息处理器
        message_handler = ExampleMessageHandler("example_handler")
        realtime_processor.add_message_handler(message_handler)
        
        # 添加Kafka处理器（如果Kafka可用）
        kafka_config = KafkaConfig(
            bootstrap_servers=["localhost:9092"],
            consumer_group="crypto_ml_strategy",
            topics=["market_data", "trading_signals"]
        )
        
        kafka_success = await realtime_processor.add_kafka_processor("main_kafka", kafka_config)
        logger.info(f"Kafka处理器添加: {kafka_success}")
        
        # 添加WebSocket处理器（示例URL）
        ws_success = await realtime_processor.add_websocket_processor(
            "binance_ws", 
            "wss://stream.binance.com:9443/ws/btcusdt@ticker"
        )
        logger.info(f"WebSocket处理器添加: {ws_success}")
        
        # 获取健康检查
        health = await realtime_processor.health_check()
        logger.info(f"实时处理器健康检查: {health}")
        
        # 获取统计信息
        stats = realtime_processor.get_statistics()
        logger.info(f"实时处理器统计: {stats}")
        
        # 模拟运行一段时间（在实际应用中，这会持续运行）
        logger.info("模拟运行实时处理器...")
        await asyncio.sleep(2)
        
        # 检查处理的消息
        logger.info(f"处理器处理的消息数: {len(message_handler.processed_messages)}")
        
    finally:
        await realtime_processor.close_all()
        await cache_manager.close()


async def example_unified_data_access_service():
    """示例：统一数据访问服务使用"""
    logger.info("=== 统一数据访问服务示例 ===")
    
    # 创建完整的数据访问配置
    config = DataAccessConfig()
    
    # 添加Java API数据源
    api_config = APIConfig(
        base_url="http://localhost:8080/api",
        api_key="your_api_key",
        timeout=30
    )
    
    api_source = DataSourceConfig(
        source_type=DataSourceType.JAVA_API,
        name="main_api",
        priority=1,
        api_config=api_config
    )
    config.add_data_source(api_source)
    
    # 添加数据库数据源
    db_config = DatabaseConfig(
        db_type=DatabaseType.MYSQL,
        host="localhost",
        port=3306,
        database="crypto_trading",
        username="root",
        password="password"
    )
    
    db_source = DataSourceConfig(
        source_type=DataSourceType.DATABASE,
        name="main_db",
        priority=2,
        database_config=db_config
    )
    config.add_data_source(db_source)
    
    # 添加Kafka数据源
    kafka_config = KafkaConfig(
        bootstrap_servers=["localhost:9092"],
        consumer_group="crypto_ml_strategy",
        topics=["market_data"]
    )
    
    kafka_source = DataSourceConfig(
        source_type=DataSourceType.KAFKA,
        name="main_kafka",
        priority=3,
        kafka_config=kafka_config
    )
    config.add_data_source(kafka_source)
    
    # 创建数据访问服务
    async with DataAccessService(config) as service:
        logger.info(f"服务状态: {service.status.value}")
        
        # 健康检查
        health = await service.health_check()
        logger.info(f"服务健康检查: {health}")
        
        # 获取历史数据
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=24)
        
        historical_data = await service.get_historical_data(
            symbol="BTCUSDT",
            start_time=start_time,
            end_time=end_time,
            timeframe="1h",
            use_cache=True,
            validate_data=True
        )
        
        if historical_data is not None:
            logger.info(f"历史数据获取成功: {len(historical_data)} 条记录")
            logger.info(f"数据时间范围: {historical_data.index.min()} 到 {historical_data.index.max()}")
        
        # 获取实时数据
        realtime_data = await service.get_realtime_data("BTCUSDT")
        if realtime_data:
            logger.info(f"实时数据获取成功: {realtime_data}")
        
        # 发送交易信号
        signal_data = {
            "symbol": "BTCUSDT",
            "action": "BUY",
            "quantity": 0.001,
            "price": 45000.0,
            "timestamp": datetime.now().isoformat(),
            "strategy": "ml_strategy",
            "confidence": 0.85
        }
        
        signal_sent = await service.send_trading_signal(signal_data)
        logger.info(f"交易信号发送: {signal_sent}")
        
        # 获取服务统计
        stats = service.get_service_statistics()
        logger.info(f"服务统计: {stats}")


async def example_integration_workflow():
    """示例：完整的集成工作流"""
    logger.info("=== 完整集成工作流示例 ===")
    
    try:
        # 1. 创建和配置服务
        logger.info("1. 创建数据访问服务...")
        from data.data_config import DEFAULT_DATA_ACCESS_CONFIG
        
        async with DataAccessService(DEFAULT_DATA_ACCESS_CONFIG) as service:
            
            # 2. 获取历史数据进行模型训练
            logger.info("2. 获取历史数据...")
            end_time = datetime.now()
            start_time = end_time - timedelta(days=7)
            
            training_data = await service.get_historical_data(
                symbol="BTCUSDT",
                start_time=start_time,
                end_time=end_time,
                timeframe="1h",
                use_cache=True,
                validate_data=True
            )
            
            if training_data is not None and len(training_data) > 0:
                logger.info(f"训练数据准备完成: {len(training_data)} 条记录")
                
                # 3. 数据预处理和特征工程
                logger.info("3. 数据预处理...")
                # 这里可以调用技术指标模块进行特征计算
                # 例如：indicators.calculate_all_indicators(training_data)
                
                # 4. 模型训练
                logger.info("4. 模型训练...")
                # 这里可以调用训练模块
                # 例如：trainer.train_model(training_data)
                
                # 5. 实时数据处理和预测
                logger.info("5. 实时数据处理...")
                realtime_data = await service.get_realtime_data("BTCUSDT")
                
                if realtime_data:
                    # 6. 生成交易信号
                    logger.info("6. 生成交易信号...")
                    
                    # 模拟信号生成逻辑
                    signal = {
                        "symbol": "BTCUSDT",
                        "action": "BUY",
                        "quantity": 0.001,
                        "price": realtime_data.get("price", 45000),
                        "timestamp": datetime.now().isoformat(),
                        "strategy": "ml_integration_example",
                        "confidence": 0.75
                    }
                    
                    # 7. 发送交易信号
                    logger.info("7. 发送交易信号...")
                    success = await service.send_trading_signal(signal)
                    
                    if success:
                        logger.info("交易信号发送成功")
                    else:
                        logger.warning("交易信号发送失败")
                
                # 8. 监控和统计
                logger.info("8. 获取系统统计...")
                stats = service.get_service_statistics()
                logger.info(f"系统运行统计: 成功率 {stats['service_metrics']['success_rate']:.2%}")
                
            else:
                logger.warning("无法获取训练数据")
    
    except Exception as e:
        logger.error(f"集成工作流执行失败: {e}")


async def main():
    """主函数：运行所有示例"""
    logger.info("开始运行数据访问层示例...")
    
    try:
        # 运行各个示例
        await example_basic_api_client()
        await asyncio.sleep(1)
        
        await example_database_connector()
        await asyncio.sleep(1)
        
        await example_cache_manager()
        await asyncio.sleep(1)
        
        await example_data_validator()
        await asyncio.sleep(1)
        
        await example_realtime_processor()
        await asyncio.sleep(1)
        
        await example_unified_data_access_service()
        await asyncio.sleep(1)
        
        await example_integration_workflow()
        
        logger.info("所有示例运行完成！")
        
    except Exception as e:
        logger.error(f"示例运行失败: {e}")


if __name__ == "__main__":
    # 运行示例
    asyncio.run(main())