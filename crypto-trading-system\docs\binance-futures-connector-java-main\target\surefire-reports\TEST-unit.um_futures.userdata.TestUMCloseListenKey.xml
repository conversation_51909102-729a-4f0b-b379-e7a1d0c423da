<?xml version="1.0" encoding="UTF-8" ?>
<testsuite tests="1" failures="0" name="unit.um_futures.userdata.TestUMCloseListenKey" time="0.001" errors="0" skipped="0">
  <properties>
    <property name="java.runtime.name" value="OpenJDK Runtime Environment"/>
    <property name="java.vm.version" value="21.0.2+13-58"/>
    <property name="sun.boot.library.path" value="C:\Users\<USER>\.jdks\openjdk-21.0.2\bin"/>
    <property name="maven.multiModuleProjectDirectory" value="D:\1_deep_bian\binance-quant-system\docs\binance-futures-connector-java-main"/>
    <property name="java.vm.vendor" value="Oracle Corporation"/>
    <property name="java.vendor.url" value="https://java.oracle.com/"/>
    <property name="guice.disable.misplaced.annotation.check" value="true"/>
    <property name="path.separator" value=";"/>
    <property name="java.vm.name" value="OpenJDK 64-Bit Server VM"/>
    <property name="sun.os.patch.level" value=""/>
    <property name="user.script" value=""/>
    <property name="user.country" value="CN"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="user.dir" value="D:\1_deep_bian\binance-quant-system\docs\binance-futures-connector-java-main"/>
    <property name="java.vm.compressedOopsMode" value="Zero based"/>
    <property name="java.runtime.version" value="21.0.2+13-58"/>
    <property name="os.arch" value="amd64"/>
    <property name="java.io.tmpdir" value="C:\Users\<USER>\AppData\Local\Temp\"/>
    <property name="gpg.skip" value="true"/>
    <property name="line.separator" value="
"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="user.variant" value=""/>
    <property name="stderr.encoding" value="ms936"/>
    <property name="os.name" value="Windows 11"/>
    <property name="classworlds.conf" value="D:\Software\apache-maven-3.6.0\bin\..\bin\m2.conf"/>
    <property name="sun.jnu.encoding" value="GBK"/>
    <property name="stdout.encoding" value="ms936"/>
    <property name="java.library.path" value="C:\Users\<USER>\.jdks\openjdk-21.0.2\bin;C:\Windows\Sun\Java\bin;C:\Windows\system32;C:\Windows;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.6\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.6\libnvvp;D:\Software\vm\bin\;D:\jdk\flutter_windows_3.22.2-stable\flutter\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\MinGW\bin;D:\Software\apache-maven-3.6.0\bin;C:\Program Files\Docker\Docker\resources\bin;D:\Software\Git\cmd;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;D:\jdk\Android\Sdk\platform-tools;D:\jdk\Android\Sdk\build-tools;C:\Program Files\WireGuard\;C:\Program Files\NVIDIA Corporation\NVIDIA app\NvDLISR;C:\Program Files\NVIDIA Corporation\Nsight Compute 2024.3.2\;C:\Program Files\CursorModifier;C:\Program Files\Redis\;C:\Program Files\MySQL\MySQL Server 9.0\bin;C:\Users\<USER>\.jdks\openjdk-21.0.2\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python313\;C:\Program Files\Google\Chrome\Application;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;D:\Software\MySQL Server 8.0\bin;C:\ProgramData\miniconda3;C:\ProgramData\miniconda3\Scripts;D:\Software\dartsdk-windows-x64-release\dart-sdk\bin;D:\jdk\Andro;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;D:\Software\Microsoft VS Code\bin;."/>
    <property name="maven.conf" value="D:\Software\apache-maven-3.6.0\bin\../conf"/>
    <property name="jdk.debug" value="release"/>
    <property name="java.class.version" value="65.0"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="os.version" value="10.0"/>
    <property name="library.jansi.path" value="D:\Software\apache-maven-3.6.0\bin\..\lib\jansi-native"/>
    <property name="user.home" value="C:\Users\<USER>\Software\apache-maven-3.6.0\bin\..\boot\plexus-classworlds-2.5.2.jar"/>
    <property name="java.vm.specification.version" value="21"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="sun.java.command" value="org.codehaus.plexus.classworlds.launcher.Launcher install -Dgpg.skip=true"/>
    <property name="java.home" value="C:\Users\<USER>\.jdks\openjdk-21.0.2"/>
    <property name="user.language" value="zh"/>
    <property name="java.specification.vendor" value="Oracle Corporation"/>
    <property name="java.vm.info" value="mixed mode, sharing"/>
    <property name="java.version" value="21.0.2"/>
    <property name="native.encoding" value="GBK"/>
    <property name="java.vendor" value="Oracle Corporation"/>
    <property name="maven.home" value="D:\Software\apache-maven-3.6.0\bin\.."/>
    <property name="file.separator" value="\"/>
    <property name="java.version.date" value="2024-01-16"/>
    <property name="java.vendor.url.bug" value="https://bugreport.java.com/bugreport/"/>
    <property name="sun.io.unicode.encoding" value="UnicodeLittle"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="sun.cpu.isalist" value="amd64"/>
  </properties>
  <testcase classname="unit.um_futures.userdata.TestUMCloseListenKey" name="testCloseListenKey" time="0.001"/>
</testsuite>