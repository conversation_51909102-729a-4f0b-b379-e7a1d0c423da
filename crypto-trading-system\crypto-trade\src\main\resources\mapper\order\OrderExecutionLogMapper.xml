<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.crypto.trading.trade.repository.mapper.order.OrderExecutionLogMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.crypto.trading.trade.model.entity.order.OrderExecutionLogEntity">
        <id column="id" property="id" />
        <result column="execution_id" property="executionId" />
        <result column="order_id" property="orderId" />
        <result column="client_order_id" property="clientOrderId" />
        <result column="action" property="action" />
        <result column="request_data" property="requestData" />
        <result column="response_data" property="responseData" />
        <result column="success" property="success" />
        <result column="error_code" property="errorCode" />
        <result column="error_message" property="errorMessage" />
        <result column="created_time" property="createdTime" />
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id, execution_id, order_id, client_order_id, action, request_data, response_data,
        success, error_code, error_message, created_time
    </sql>

    <!-- 根据订单ID查询执行日志 -->
    <select id="selectByOrderId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM t_order_execution_log
        WHERE order_id = #{orderId}
        ORDER BY created_time DESC
    </select>

    <!-- 根据客户端订单ID查询执行日志 -->
    <select id="selectByClientOrderId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM t_order_execution_log
        WHERE client_order_id = #{clientOrderId}
        ORDER BY created_time DESC
    </select>

    <!-- 根据执行ID查询执行日志 -->
    <select id="selectByExecutionId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM t_order_execution_log
        WHERE execution_id = #{executionId}
    </select>

</mapper>