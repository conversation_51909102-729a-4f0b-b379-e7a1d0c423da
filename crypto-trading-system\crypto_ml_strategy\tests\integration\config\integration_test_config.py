#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
集成测试配置管理模块

负责管理集成测试的配置，支持多环境配置（dev/staging/prod-like）。
提供数据库连接、Kafka配置、API端点、性能阈值等配置管理。
"""

import os
import json
import yaml
from pathlib import Path
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass, asdict
from loguru import logger


@dataclass
class DatabaseConfig:
    """数据库配置"""
    host: str = "localhost"
    port: int = 3306
    username: str = "test_user"
    password: str = "test_password"
    database: str = "crypto_test"
    pool_size: int = 10
    max_overflow: int = 20
    pool_timeout: int = 30
    pool_recycle: int = 3600


@dataclass
class KafkaConfig:
    """Kafka配置"""
    bootstrap_servers: List[str] = None
    consumer_group_id: str = "crypto_ml_test_group"
    auto_offset_reset: str = "earliest"
    enable_auto_commit: bool = False
    max_poll_records: int = 500
    session_timeout_ms: int = 30000
    heartbeat_interval_ms: int = 3000
    request_timeout_ms: int = 40000
    
    def __post_init__(self):
        if self.bootstrap_servers is None:
            self.bootstrap_servers = ["localhost:9092"]


@dataclass
class APIConfig:
    """API配置"""
    java_api_base_url: str = "http://localhost:8080/api"
    timeout: int = 30
    retry_attempts: int = 3
    retry_delay: float = 1.0
    auth_token: Optional[str] = None
    rate_limit_per_second: int = 100


@dataclass
class PerformanceThresholds:
    """性能阈值配置"""
    signal_generation_latency_ms: int = 100
    data_processing_latency_ms: int = 50
    model_prediction_latency_ms: int = 200
    end_to_end_latency_ms: int = 500
    memory_usage_mb: int = 2048
    cpu_usage_percent: float = 80.0
    cache_hit_rate_percent: float = 90.0
    uptime_percent: float = 99.9
    data_loss_rate_percent: float = 0.1


@dataclass
class TestDataConfig:
    """测试数据配置"""
    symbols: List[str] = None
    timeframes: List[str] = None
    data_points_per_timeframe: int = 10000
    start_date: str = "2023-01-01"
    end_date: str = "2024-01-01"
    include_anomalies: bool = True
    anomaly_rate: float = 0.01
    
    def __post_init__(self):
        if self.symbols is None:
            self.symbols = ["BTCUSDT", "ETHUSDT"]
        if self.timeframes is None:
            self.timeframes = ["1m", "5m", "15m", "1h", "4h", "1d"]


@dataclass
class TestEnvironmentConfig:
    """测试环境配置"""
    environment: str = "dev"
    debug_mode: bool = True
    log_level: str = "DEBUG"
    parallel_execution: bool = True
    max_workers: int = 4
    test_timeout_seconds: int = 300
    cleanup_after_test: bool = True
    preserve_test_data: bool = False


class IntegrationTestConfig:
    """集成测试配置管理器"""
    
    def __init__(self, environment: str = "dev", config_file: Optional[str] = None):
        """
        初始化集成测试配置管理器
        
        Args:
            environment: 测试环境 (dev/staging/prod-like)
            config_file: 配置文件路径
        """
        self.environment = environment
        self.config_file = config_file or self._get_default_config_file()
        
        # 初始化默认配置
        self.database = DatabaseConfig()
        self.kafka = KafkaConfig()
        self.api = APIConfig()
        self.performance = PerformanceThresholds()
        self.test_data = TestDataConfig()
        self.test_env = TestEnvironmentConfig()
        
        # 加载配置
        self._load_config()
        
        # 应用环境变量覆盖
        self._apply_env_overrides()
        
        # 验证配置
        self._validate_config()
        
        logger.info(f"集成测试配置已加载 - 环境: {self.environment}")
    
    def _get_default_config_file(self) -> str:
        """获取默认配置文件路径"""
        config_dir = Path(__file__).parent
        return str(config_dir / f"test_config_{self.environment}.yaml")
    
    def _load_config(self) -> None:
        """加载配置文件"""
        if not os.path.exists(self.config_file):
            logger.warning(f"配置文件不存在，使用默认配置: {self.config_file}")
            self._create_default_config_file()
            return
        
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                if self.config_file.endswith('.yaml') or self.config_file.endswith('.yml'):
                    config_data = yaml.safe_load(f)
                else:
                    config_data = json.load(f)
            
            # 更新配置对象
            if 'database' in config_data:
                self.database = DatabaseConfig(**config_data['database'])
            if 'kafka' in config_data:
                self.kafka = KafkaConfig(**config_data['kafka'])
            if 'api' in config_data:
                self.api = APIConfig(**config_data['api'])
            if 'performance' in config_data:
                self.performance = PerformanceThresholds(**config_data['performance'])
            if 'test_data' in config_data:
                self.test_data = TestDataConfig(**config_data['test_data'])
            if 'test_env' in config_data:
                self.test_env = TestEnvironmentConfig(**config_data['test_env'])
            
            logger.info(f"配置文件加载成功: {self.config_file}")
            
        except Exception as e:
            logger.error(f"加载配置文件失败: {str(e)}")
            raise
    
    def _create_default_config_file(self) -> None:
        """创建默认配置文件"""
        default_config = {
            'database': asdict(self.database),
            'kafka': asdict(self.kafka),
            'api': asdict(self.api),
            'performance': asdict(self.performance),
            'test_data': asdict(self.test_data),
            'test_env': asdict(self.test_env)
        }
        
        # 根据环境调整配置
        if self.environment == "staging":
            default_config['database']['host'] = "staging-db.example.com"
            default_config['api']['java_api_base_url'] = "http://staging-api.example.com:8080/api"
            default_config['kafka']['bootstrap_servers'] = ["staging-kafka.example.com:9092"]
            default_config['test_env']['debug_mode'] = False
            default_config['test_env']['log_level'] = "INFO"
        elif self.environment == "prod-like":
            default_config['database']['host'] = "prod-like-db.example.com"
            default_config['api']['java_api_base_url'] = "http://prod-like-api.example.com:8080/api"
            default_config['kafka']['bootstrap_servers'] = ["prod-like-kafka.example.com:9092"]
            default_config['test_env']['debug_mode'] = False
            default_config['test_env']['log_level'] = "WARNING"
            default_config['performance']['signal_generation_latency_ms'] = 50
            default_config['performance']['end_to_end_latency_ms'] = 200
        
        try:
            os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
            with open(self.config_file, 'w', encoding='utf-8') as f:
                yaml.dump(default_config, f, default_flow_style=False, allow_unicode=True)
            logger.info(f"默认配置文件已创建: {self.config_file}")
        except Exception as e:
            logger.error(f"创建默认配置文件失败: {str(e)}")
            raise
    
    def _apply_env_overrides(self) -> None:
        """应用环境变量覆盖"""
        env_prefix = "CRYPTO_TEST_"
        
        # 数据库配置覆盖
        if os.getenv(f"{env_prefix}DB_HOST"):
            self.database.host = os.getenv(f"{env_prefix}DB_HOST")
        if os.getenv(f"{env_prefix}DB_PORT"):
            self.database.port = int(os.getenv(f"{env_prefix}DB_PORT"))
        if os.getenv(f"{env_prefix}DB_USER"):
            self.database.username = os.getenv(f"{env_prefix}DB_USER")
        if os.getenv(f"{env_prefix}DB_PASSWORD"):
            self.database.password = os.getenv(f"{env_prefix}DB_PASSWORD")
        if os.getenv(f"{env_prefix}DB_NAME"):
            self.database.database = os.getenv(f"{env_prefix}DB_NAME")
        
        # Kafka配置覆盖
        if os.getenv(f"{env_prefix}KAFKA_SERVERS"):
            self.kafka.bootstrap_servers = os.getenv(f"{env_prefix}KAFKA_SERVERS").split(",")
        if os.getenv(f"{env_prefix}KAFKA_GROUP_ID"):
            self.kafka.consumer_group_id = os.getenv(f"{env_prefix}KAFKA_GROUP_ID")
        
        # API配置覆盖
        if os.getenv(f"{env_prefix}API_BASE_URL"):
            self.api.java_api_base_url = os.getenv(f"{env_prefix}API_BASE_URL")
        if os.getenv(f"{env_prefix}API_TOKEN"):
            self.api.auth_token = os.getenv(f"{env_prefix}API_TOKEN")
        
        # 测试环境配置覆盖
        if os.getenv(f"{env_prefix}DEBUG"):
            self.test_env.debug_mode = os.getenv(f"{env_prefix}DEBUG").lower() == "true"
        if os.getenv(f"{env_prefix}LOG_LEVEL"):
            self.test_env.log_level = os.getenv(f"{env_prefix}LOG_LEVEL")
        if os.getenv(f"{env_prefix}PARALLEL"):
            self.test_env.parallel_execution = os.getenv(f"{env_prefix}PARALLEL").lower() == "true"
        if os.getenv(f"{env_prefix}MAX_WORKERS"):
            self.test_env.max_workers = int(os.getenv(f"{env_prefix}MAX_WORKERS"))
        
        logger.debug("环境变量覆盖已应用")
    
    def _validate_config(self) -> None:
        """验证配置有效性"""
        errors = []
        
        # 验证数据库配置
        if not self.database.host:
            errors.append("数据库主机不能为空")
        if not (1 <= self.database.port <= 65535):
            errors.append("数据库端口必须在1-65535范围内")
        if not self.database.username:
            errors.append("数据库用户名不能为空")
        if not self.database.database:
            errors.append("数据库名称不能为空")
        
        # 验证Kafka配置
        if not self.kafka.bootstrap_servers:
            errors.append("Kafka服务器列表不能为空")
        if not self.kafka.consumer_group_id:
            errors.append("Kafka消费者组ID不能为空")
        
        # 验证API配置
        if not self.api.java_api_base_url:
            errors.append("Java API基础URL不能为空")
        if self.api.timeout <= 0:
            errors.append("API超时时间必须大于0")
        
        # 验证性能阈值
        if self.performance.signal_generation_latency_ms <= 0:
            errors.append("信号生成延迟阈值必须大于0")
        if not (0 <= self.performance.uptime_percent <= 100):
            errors.append("正常运行时间百分比必须在0-100范围内")
        
        # 验证测试数据配置
        if not self.test_data.symbols:
            errors.append("测试符号列表不能为空")
        if not self.test_data.timeframes:
            errors.append("时间框架列表不能为空")
        if self.test_data.data_points_per_timeframe <= 0:
            errors.append("每个时间框架的数据点数必须大于0")
        
        # 验证测试环境配置
        if self.test_env.max_workers <= 0:
            errors.append("最大工作线程数必须大于0")
        if self.test_env.test_timeout_seconds <= 0:
            errors.append("测试超时时间必须大于0")
        
        if errors:
            error_msg = "配置验证失败:\n" + "\n".join(f"- {error}" for error in errors)
            logger.error(error_msg)
            raise ValueError(error_msg)
        
        logger.info("配置验证通过")
    
    def get_database_url(self) -> str:
        """获取数据库连接URL"""
        return (f"mysql+pymysql://{self.database.username}:{self.database.password}"
                f"@{self.database.host}:{self.database.port}/{self.database.database}")
    
    def get_kafka_config(self) -> Dict[str, Any]:
        """获取Kafka配置字典"""
        return {
            'bootstrap_servers': self.kafka.bootstrap_servers,
            'group_id': self.kafka.consumer_group_id,
            'auto_offset_reset': self.kafka.auto_offset_reset,
            'enable_auto_commit': self.kafka.enable_auto_commit,
            'max_poll_records': self.kafka.max_poll_records,
            'session_timeout_ms': self.kafka.session_timeout_ms,
            'heartbeat_interval_ms': self.kafka.heartbeat_interval_ms,
            'request_timeout_ms': self.kafka.request_timeout_ms
        }
    
    def get_api_headers(self) -> Dict[str, str]:
        """获取API请求头"""
        headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }
        if self.api.auth_token:
            headers['Authorization'] = f"Bearer {self.api.auth_token}"
        return headers
    
    def to_dict(self) -> Dict[str, Any]:
        """将配置转换为字典"""
        return {
            'environment': self.environment,
            'database': asdict(self.database),
            'kafka': asdict(self.kafka),
            'api': asdict(self.api),
            'performance': asdict(self.performance),
            'test_data': asdict(self.test_data),
            'test_env': asdict(self.test_env)
        }
    
    def save_config(self, file_path: Optional[str] = None) -> None:
        """保存配置到文件"""
        save_path = file_path or self.config_file
        config_dict = self.to_dict()
        
        try:
            os.makedirs(os.path.dirname(save_path), exist_ok=True)
            with open(save_path, 'w', encoding='utf-8') as f:
                if save_path.endswith('.yaml') or save_path.endswith('.yml'):
                    yaml.dump(config_dict, f, default_flow_style=False, allow_unicode=True)
                else:
                    json.dump(config_dict, f, indent=2, ensure_ascii=False)
            logger.info(f"配置已保存到: {save_path}")
        except Exception as e:
            logger.error(f"保存配置失败: {str(e)}")
            raise


# 全局配置实例
_config_instances = {}


def get_test_config(environment: str = "dev") -> IntegrationTestConfig:
    """
    获取测试配置实例（单例模式）
    
    Args:
        environment: 测试环境
    
    Returns:
        IntegrationTestConfig: 配置实例
    """
    if environment not in _config_instances:
        _config_instances[environment] = IntegrationTestConfig(environment)
    return _config_instances[environment]


def reset_test_config(environment: str = "dev") -> None:
    """
    重置测试配置实例
    
    Args:
        environment: 测试环境
    """
    if environment in _config_instances:
        del _config_instances[environment]
    logger.info(f"测试配置已重置: {environment}")