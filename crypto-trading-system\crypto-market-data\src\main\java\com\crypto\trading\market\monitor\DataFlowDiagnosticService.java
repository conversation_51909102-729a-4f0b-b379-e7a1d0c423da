package com.crypto.trading.market.monitor;

import com.crypto.trading.market.consumer.AsyncDatabaseConsumer;
import com.crypto.trading.market.producer.AsyncMarketDataProducer;
import com.crypto.trading.market.processor.KlineDataProcessor;
import com.crypto.trading.market.processor.DepthDataProcessor;
import com.crypto.trading.market.processor.TradeDataProcessor;
import com.crypto.trading.market.repository.InfluxDBRepository;
import com.crypto.trading.market.repository.MarketDataRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 数据流诊断服务
 * 用于诊断K线数据、深度数据、订单数据没有正确到达数据库的问题
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
public class DataFlowDiagnosticService {

    private static final Logger log = LoggerFactory.getLogger(DataFlowDiagnosticService.class);

    @Autowired
    private AsyncMarketDataProducer asyncMarketDataProducer;

    @Autowired
    private AsyncDatabaseConsumer asyncDatabaseConsumer;

    @Autowired
    private KlineDataProcessor klineDataProcessor;

    @Autowired
    private DepthDataProcessor depthDataProcessor;

    @Autowired
    private TradeDataProcessor tradeDataProcessor;

    @Autowired
    private InfluxDBRepository influxDBRepository;

    @Autowired
    private MarketDataRepository marketDataRepository;

    @Autowired
    @Qualifier("jsonKafkaTemplate")
    private KafkaTemplate<String, Object> kafkaTemplate;

    /**
     * 执行完整的数据流诊断
     * 
     * @return 诊断报告
     */
    public Map<String, Object> performFullDiagnosis() {
        Map<String, Object> report = new HashMap<>();
        
        log.info("开始执行数据流诊断...");
        
        // 1. 检查Kafka连接
        report.put("kafkaConnection", checkKafkaConnection());
        
        // 2. 检查数据库连接
        report.put("databaseConnection", checkDatabaseConnection());
        
        // 3. 检查生产者统计
        report.put("producerStatistics", getProducerStatistics());
        
        // 4. 检查消费者统计
        report.put("consumerStatistics", getConsumerStatistics());
        
        // 5. 检查处理器统计
        report.put("processorStatistics", getProcessorStatistics());
        
        // 6. 检查数据库中的数据
        report.put("databaseDataCheck", checkDatabaseData());
        
        // 7. 生成诊断建议
        report.put("recommendations", generateRecommendations(report));
        
        report.put("timestamp", LocalDateTime.now());
        
        log.info("数据流诊断完成");
        
        return report;
    }

    /**
     * 检查Kafka连接状态
     */
    private Map<String, Object> checkKafkaConnection() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 尝试发送测试消息
            kafkaTemplate.send("test.topic", "test-key", "test-message");
            result.put("status", "CONNECTED");
            result.put("message", "Kafka连接正常");
        } catch (Exception e) {
            result.put("status", "ERROR");
            result.put("message", "Kafka连接失败: " + e.getMessage());
            result.put("error", e.getClass().getSimpleName());
        }
        
        return result;
    }

    /**
     * 检查数据库连接状态
     */
    private Map<String, Object> checkDatabaseConnection() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 检查InfluxDB连接
            boolean influxConnected = checkInfluxDBConnection();
            result.put("influxdb", influxConnected ? "CONNECTED" : "ERROR");
            
            // 检查MySQL连接
            boolean mysqlConnected = checkMySQLConnection();
            result.put("mysql", mysqlConnected ? "CONNECTED" : "ERROR");
            
            result.put("status", (influxConnected && mysqlConnected) ? "CONNECTED" : "PARTIAL");
            
        } catch (Exception e) {
            result.put("status", "ERROR");
            result.put("message", "数据库连接检查失败: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 检查InfluxDB连接
     */
    private boolean checkInfluxDBConnection() {
        try {
            // 尝试查询InfluxDB
            influxDBRepository.hasKlineData("BTCUSDT", "1m", 
                LocalDateTime.now().minusMinutes(1), LocalDateTime.now());
            return true;
        } catch (Exception e) {
            log.warn("InfluxDB连接检查失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 检查MySQL连接
     */
    private boolean checkMySQLConnection() {
        try {
            // 尝试查询MySQL
            marketDataRepository.findKlineDataCount("BTCUSDT", "1m");
            return true;
        } catch (Exception e) {
            log.warn("MySQL连接检查失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 获取生产者统计信息
     */
    private Map<String, Object> getProducerStatistics() {
        try {
            Map<String, Long> stats = asyncMarketDataProducer.getStatistics();
            return new HashMap<>(stats);
        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("error", "获取生产者统计失败: " + e.getMessage());
            return error;
        }
    }

    /**
     * 获取消费者统计信息
     */
    private Map<String, Object> getConsumerStatistics() {
        try {
            Map<String, Long> stats = asyncDatabaseConsumer.getStatistics();
            return new HashMap<>(stats);
        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("error", "获取消费者统计失败: " + e.getMessage());
            return error;
        }
    }

    /**
     * 获取处理器统计信息
     */
    private Map<String, Object> getProcessorStatistics() {
        Map<String, Object> stats = new HashMap<>();
        
        try {
            stats.put("klineProcessor", klineDataProcessor.getStatistics());
        } catch (Exception e) {
            stats.put("klineProcessor", "获取统计失败: " + e.getMessage());
        }
        
        try {
            stats.put("depthProcessor", depthDataProcessor.getStatistics());
        } catch (Exception e) {
            stats.put("depthProcessor", "获取统计失败: " + e.getMessage());
        }
        
        try {
            stats.put("tradeProcessor", tradeDataProcessor.getStatistics());
        } catch (Exception e) {
            stats.put("tradeProcessor", "获取统计失败: " + e.getMessage());
        }
        
        return stats;
    }

    /**
     * 检查数据库中的数据
     */
    private Map<String, Object> checkDatabaseData() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 检查最近1小时的数据
            LocalDateTime endTime = LocalDateTime.now();
            LocalDateTime startTime = endTime.minusHours(1);
            
            // 检查InfluxDB中的数据
            boolean hasInfluxKlineData = influxDBRepository.hasKlineData("BTCUSDT", "1m", startTime, endTime);
            boolean hasInfluxTradeData = influxDBRepository.hasTradeData("BTCUSDT", startTime, endTime);
            
            result.put("influxdb_kline_data", hasInfluxKlineData);
            result.put("influxdb_trade_data", hasInfluxTradeData);
            
            // 检查MySQL中的数据
            long mysqlKlineCount = marketDataRepository.findKlineDataCount("BTCUSDT", "1m");
            result.put("mysql_kline_count", mysqlKlineCount);
            
        } catch (Exception e) {
            result.put("error", "检查数据库数据失败: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 生成诊断建议
     */
    private Map<String, Object> generateRecommendations(Map<String, Object> report) {
        Map<String, Object> recommendations = new HashMap<>();
        
        // 检查Kafka连接问题
        Map<String, Object> kafkaConnection = (Map<String, Object>) report.get("kafkaConnection");
        if (!"CONNECTED".equals(kafkaConnection.get("status"))) {
            recommendations.put("kafka", "检查Kafka服务是否启动，配置是否正确");
        }
        
        // 检查数据库连接问题
        Map<String, Object> dbConnection = (Map<String, Object>) report.get("databaseConnection");
        if (!"CONNECTED".equals(dbConnection.get("status"))) {
            recommendations.put("database", "检查InfluxDB和MySQL服务是否启动，配置是否正确");
        }
        
        // 检查生产者问题
        Map<String, Object> producerStats = (Map<String, Object>) report.get("producerStatistics");
        if (producerStats.containsKey("failureCount")) {
            Long failureCount = (Long) producerStats.get("failureCount");
            if (failureCount != null && failureCount > 0) {
                recommendations.put("producer", "生产者有失败记录，检查Kafka Topic是否存在，网络是否正常");
            }
        }
        
        // 检查消费者问题
        Map<String, Object> consumerStats = (Map<String, Object>) report.get("consumerStatistics");
        if (consumerStats.containsKey("totalFailureCount")) {
            Long failureCount = (Long) consumerStats.get("totalFailureCount");
            if (failureCount != null && failureCount > 0) {
                recommendations.put("consumer", "消费者有失败记录，检查数据库连接和批处理配置");
            }
        }
        
        return recommendations;
    }

    /**
     * 重置所有统计信息
     */
    public void resetAllStatistics() {
        try {
            asyncMarketDataProducer.resetStatistics();
            asyncDatabaseConsumer.resetStatistics();
            klineDataProcessor.resetStatistics();
            log.info("所有统计信息已重置");
        } catch (Exception e) {
            log.error("重置统计信息失败: {}", e.getMessage(), e);
        }
    }
}
