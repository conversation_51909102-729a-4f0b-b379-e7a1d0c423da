<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>Account.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">io.github.binance:binance-futures-connector-java</a> &gt; <a href="index.source.html" class="el_package">com.binance.connector.futures.client.impl.futures</a> &gt; <span class="el_source">Account.java</span></div><h1>Account.java</h1><pre class="source lang-java linenums">package com.binance.connector.futures.client.impl.futures;

import com.binance.connector.futures.client.enums.HttpMethod;
import com.binance.connector.futures.client.utils.ParameterChecker;
import com.binance.connector.futures.client.utils.ProxyAuth;
import com.binance.connector.futures.client.utils.RequestHandler;
import java.util.LinkedHashMap;

/**
 * &lt;h2&gt;Trade Endpoints&lt;/h2&gt;
 * Response will be returned in &lt;i&gt;String format&lt;/i&gt;.
 */
public abstract class Account {
    private String productUrl;
    private RequestHandler requestHandler;
    private boolean showLimitUsage;

<span class="fc" id="L18">    public Account(String productUrl, String apiKey, String secretKey, boolean showLimitUsage, ProxyAuth proxy) {</span>
<span class="fc" id="L19">        this.productUrl = productUrl;</span>
<span class="fc" id="L20">        this.requestHandler = new RequestHandler(apiKey, secretKey, proxy);</span>
<span class="fc" id="L21">        this.showLimitUsage = showLimitUsage;</span>
<span class="fc" id="L22">    }</span>

    public String getProductUrl() {
<span class="fc" id="L25">        return this.productUrl;</span>
    }

    public RequestHandler getRequestHandler() {
<span class="fc" id="L29">        return this.requestHandler;</span>
    }

    public boolean getShowLimitUsage() {
<span class="fc" id="L33">        return this.showLimitUsage;</span>
    }

    public void setProductUrl(String productUrl) {
<span class="nc" id="L37">        this.productUrl = productUrl;</span>
<span class="nc" id="L38">    }</span>

    public void setRequestHandler(String apiKey, String secretKey, ProxyAuth proxy) {
<span class="nc" id="L41">        this.requestHandler = new RequestHandler(apiKey, secretKey, proxy);</span>
<span class="nc" id="L42">    }</span>

    public void setShowLimitUsage(boolean showLimitUsage) {
<span class="nc" id="L45">        this.showLimitUsage = showLimitUsage;</span>
<span class="nc" id="L46">    }</span>

<span class="fc" id="L48">    private final String OPEN_ORDERS = &quot;/v1/openOrders&quot;;</span>
    public String currentAllOpenOrders(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L50">        return requestHandler.sendSignedRequest(productUrl, OPEN_ORDERS, parameters, HttpMethod.GET, showLimitUsage);</span>
    }

<span class="fc" id="L53">    private final String ALL_ORDERS = &quot;/v1/allOrders&quot;;</span>
    public String allOrders(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L55">        return requestHandler.sendSignedRequest(productUrl, ALL_ORDERS, parameters, HttpMethod.GET, showLimitUsage);</span>
    }

<span class="fc" id="L58">    private final String USER_TRADES = &quot;/v1/userTrades&quot;;</span>
    public String accountTradeList(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L60">        return requestHandler.sendSignedRequest(productUrl, USER_TRADES, parameters, HttpMethod.GET, showLimitUsage);</span>
    }

<span class="fc" id="L63">    private final String LEVERAGE_BRACKET = &quot;/v1/leverageBracket&quot;;</span>
    public String getLeverageBracket(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L65">        return requestHandler.sendSignedRequest(productUrl, LEVERAGE_BRACKET, parameters, HttpMethod.GET, showLimitUsage);</span>
    }

<span class="fc" id="L68">    private final String POSITION_SIDE_DUAL = &quot;/v1/positionSide/dual&quot;;</span>
    /**
     * Change user's position mode (Hedge Mode or One-way Mode ) on EVERY symbol
     * &lt;br&gt;&lt;br&gt;
     * POST /v1/positionSide/dual
     * &lt;br&gt;
     * @param
     * parameters LinkedHashedMap of String,Object pair
     *            where String is the name of the parameter and Object is the value of the parameter
     * &lt;br&gt;&lt;br&gt;
     * dualSidePosition -- mandatory/string &lt;br&gt;
     * recvWindow -- optional/long &lt;br&gt;
     * @return String
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/usds-margined-futures/trade/rest-api/Change-Position-Mode&quot;&gt;
     *     https://developers.binance.com/docs/derivatives/usds-margined-futures/trade/rest-api/Change-Position-Mode&lt;/a&gt;
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/coin-margined-futures/trade/Change-Position-Mode&quot;&gt;
     *     https://developers.binance.com/docs/derivatives/coin-margined-futures/trade/Change-Position-Mode&lt;/a&gt;
     */
    public String changePositionModeTrade(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L87">        ParameterChecker.checkParameter(parameters, &quot;dualSidePosition&quot;, String.class);</span>
<span class="fc" id="L88">        return requestHandler.sendSignedRequest(productUrl, POSITION_SIDE_DUAL, parameters, HttpMethod.POST, showLimitUsage);</span>
    }

    /**
     * Check an order's status.
     * &lt;br&gt;&lt;br&gt;
     * GET /v1/positionSide/dual
     * &lt;br&gt;
     * @param
     * parameters LinkedHashedMap of String,Object pair
     *            where String is the name of the parameter and Object is the value of the parameter
     * &lt;br&gt;&lt;br&gt;
     * recvWindow -- optional/long &lt;br&gt;
     * @return String
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/usds-margined-futures/account/rest-api/Get-Current-Position-Mode&quot;&gt;
     *     https://developers.binance.com/docs/derivatives/usds-margined-futures/account/rest-api/Get-Current-Position-Mode&lt;/a&gt;
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/coin-margined-futures/account/Get-Current-Position-Mode&quot;&gt;
     *     https://developers.binance.com/docs/derivatives/coin-margined-futures/account/Get-Current-Position-Mode&lt;/a&gt;
     */
    public String getCurrentPositionMode(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L108">        return requestHandler.sendSignedRequest(productUrl, POSITION_SIDE_DUAL, parameters, HttpMethod.GET, showLimitUsage);</span>
    }

<span class="fc" id="L111">    private final String ORDER = &quot;/v1/order&quot;;</span>
    /**
     * Send in a new order.
     * &lt;br&gt;&lt;br&gt;
     * POST /v1/order
     * &lt;br&gt;
     * @param
     * parameters LinkedHashedMap of String,Object pair
     *            where String is the name of the parameter and Object is the value of the parameter
     * &lt;br&gt;&lt;br&gt;
     * symbol -- mandatory/string &lt;br&gt;
     * side -- mandatory/enum &lt;br&gt;
     * positionSide - optional/enum &lt;br&gt;
     * type -- mandatory/enum &lt;br&gt;
     * timeInForce -- optional/enum &lt;br&gt;
     * quantity -- optional/decimal &lt;br&gt;
     * reduceOnly -- optional/string &lt;br&gt;
     * price -- optional/decimal &lt;br&gt;
     * newClientOrderId -- optional/string &lt;br&gt;
     * stopPrice -- optional/decimal &lt;br&gt;
     * closePosition -- optional/string &lt;br&gt;
     * activationPrice -- optional/decimal &lt;br&gt;
     * callbackRate -- optional/decimal &lt;br&gt;
     * workingType -- optional/enum &lt;br&gt;
     * priceProtect -- optional/string &lt;br&gt;
     * newOrderRespType -- optional/enum &lt;br&gt;
     * recvWindow -- optional/long &lt;br&gt;
     * @return String
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/usds-margined-futures/trade/rest-api/New-Order&quot;&gt;
     *    https://developers.binance.com/docs/derivatives/usds-margined-futures/trade/rest-api/New-Order&lt;/a&gt;
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/coin-margined-futures/trade/New-Order&quot;&gt;
     *    https://developers.binance.com/docs/derivatives/coin-margined-futures/trade/New-Order&lt;/a&gt;
     */
    public String newOrder(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L145">        ParameterChecker.checkParameter(parameters, &quot;symbol&quot;, String.class);</span>
<span class="fc" id="L146">        ParameterChecker.checkParameter(parameters, &quot;side&quot;, String.class);</span>
<span class="fc" id="L147">        ParameterChecker.checkParameter(parameters, &quot;type&quot;, String.class);</span>
<span class="fc" id="L148">        return requestHandler.sendSignedRequest(productUrl, ORDER, parameters, HttpMethod.POST, showLimitUsage);</span>
    }

<span class="fc" id="L151">    private final String BATCH_ORDERS = &quot;/v1/batchOrders&quot;;</span>
    /**
     * Send in a new order.
     * &lt;br&gt;&lt;br&gt;
     * POST /v1/batchOrders
     * &lt;br&gt;
     * @param
     * parameters LinkedHashedMap of String,Object pair
     *            where String is the name of the parameter and Object is the value of the parameter
     * &lt;br&gt;&lt;br&gt;
     * batchOrders -- mandatory/list &lt;br&gt;
     * recvWindow -- optional/long &lt;br&gt;
     * @return String
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/usds-margined-futures/trade/rest-api/Place-Multiple-Orders&quot;&gt;
     *     https://developers.binance.com/docs/derivatives/usds-margined-futures/trade/rest-api/Place-Multiple-Orders&lt;/a&gt;
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/coin-margined-futures/trade/Modify-Multiple-Orders&quot;&gt;
     *     https://developers.binance.com/docs/derivatives/coin-margined-futures/trade/Modify-Multiple-Orders&lt;/a&gt;
     */
    public String placeMultipleOrders(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="nc" id="L170">        ParameterChecker.checkRequiredParameter(parameters, &quot;batchOrders&quot;);</span>
<span class="nc" id="L171">        return requestHandler.sendSignedRequest(productUrl, BATCH_ORDERS, parameters, HttpMethod.POST, showLimitUsage);</span>
    }

    /**
     * Check an order's status.
     * &lt;br&gt;&lt;br&gt;
     * GET /v1/order
     * &lt;br&gt;
     * @param
     * parameters LinkedHashedMap of String,Object pair
     *            where String is the name of the parameter and Object is the value of the parameter
     * &lt;br&gt;&lt;br&gt;
     * symbol -- mandatory/string &lt;br&gt;
     * orderId -- optional/long &lt;br&gt;
     * origClientOrderId -- optional/string &lt;br&gt;
     * recvWindow -- optional/long &lt;br&gt;
     * @return String
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/usds-margined-futures/trade/rest-api/Query-Order&quot;&gt;
     *     https://developers.binance.com/docs/derivatives/usds-margined-futures/trade/rest-api/Query-Order&lt;/a&gt;
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/coin-margined-futures/trade/Query-Order&quot;&gt;
     *     https://developers.binance.com/docs/derivatives/coin-margined-futures/trade/Query-Order&lt;/a&gt;
     */
    public String queryOrder(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L194">        ParameterChecker.checkParameter(parameters, &quot;symbol&quot;, String.class);</span>
<span class="fc" id="L195">        ParameterChecker.checkOrParameters(parameters, &quot;orderId&quot;, &quot;origClientOrderId&quot;);</span>
<span class="fc" id="L196">        return requestHandler.sendSignedRequest(productUrl, ORDER, parameters, HttpMethod.GET, showLimitUsage);</span>
    }

    /**
     * Cancel an active order.
     * &lt;br&gt;&lt;br&gt;
     * DELETE /v1/order
     * &lt;br&gt;
     * @param
     * parameters LinkedHashedMap of String,Object pair
     *            where String is the name of the parameter and Object is the value of the parameter
     * &lt;br&gt;&lt;br&gt;
     * symbol -- mandatory/string &lt;br&gt;
     * orderId -- optional/long &lt;br&gt;
     * origClientOrderId -- optional/string &lt;br&gt;
     * recvWindow -- optional/long &lt;br&gt;
     * @return String
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/usds-margined-futures/trade/rest-api/Cancel-Order&quot;&gt;
     *     https://developers.binance.com/docs/derivatives/usds-margined-futures/trade/rest-api/Cancel-Order&lt;/a&gt;
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/coin-margined-futures/trade/Cancel-Order&quot;&gt;
     *     https://developers.binance.com/docs/derivatives/coin-margined-futures/trade/Cancel-Order&lt;/a&gt;
     */
    public String cancelOrder(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L219">        ParameterChecker.checkParameter(parameters, &quot;symbol&quot;, String.class);</span>
<span class="fc" id="L220">        ParameterChecker.checkOrParameters(parameters, &quot;orderId&quot;, &quot;origClientOrderId&quot;);</span>
<span class="fc" id="L221">        return requestHandler.sendSignedRequest(productUrl, ORDER, parameters, HttpMethod.DELETE, showLimitUsage);</span>
    }

<span class="fc" id="L224">    private final String ALL_OPEN_ORDERS = &quot;/v1/allOpenOrders&quot;;</span>
    /**
     * Cancel all open orders.
     * &lt;br&gt;&lt;br&gt;
     * DELETE /v1/allOpenOrders
     * &lt;br&gt;
     * @param
     * parameters LinkedHashedMap of String,Object pair
     *            where String is the name of the parameter and Object is the value of the parameter
     * &lt;br&gt;&lt;br&gt;
     * symbol -- mandatory/string &lt;br&gt;
     * recvWindow -- optional/long &lt;br&gt;
     * @return String
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/usds-margined-futures/trade/rest-api/Cancel-All-Open-Orders&quot;&gt;
     *     https://developers.binance.com/docs/derivatives/usds-margined-futures/trade/rest-api/Cancel-All-Open-Orders&lt;/a&gt;
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/coin-margined-futures/trade/Cancel-All-Open-Orders&quot;&gt;
     *     https://developers.binance.com/docs/derivatives/coin-margined-futures/trade/Cancel-All-Open-Orders&lt;/a&gt;
     */
    public String cancelAllOpenOrders(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L243">        ParameterChecker.checkParameter(parameters, &quot;symbol&quot;, String.class);</span>
<span class="fc" id="L244">        return requestHandler.sendSignedRequest(productUrl, ALL_OPEN_ORDERS, parameters, HttpMethod.DELETE, showLimitUsage);</span>
    }

    /**
     * Cancel multiple orders.
     * &lt;br&gt;&lt;br&gt;
     * DELETE /v1/batchOrders
     * &lt;br&gt;
     * @param
     * parameters LinkedHashedMap of String,Object pair
     *            where String is the name of the parameter and Object is the value of the parameter
     * &lt;br&gt;&lt;br&gt;
     * symbol -- mandatory/string &lt;br&gt;
     * orderIdList -- optional/list &lt;br&gt;
     * origClientOrderIdList -- optional/list &lt;br&gt;
     * recvWindow -- optional/long &lt;br&gt;
     * @return String
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/usds-margined-futures/trade/rest-api/Cancel-Multiple-Orders&quot;&gt;
     *     https://developers.binance.com/docs/derivatives/usds-margined-futures/trade/rest-api/Cancel-Multiple-Orders&lt;/a&gt;
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/coin-margined-futures/trade/Cancel-Multiple-Orders&quot;&gt;
     *     https://developers.binance.com/docs/derivatives/coin-margined-futures/trade/Cancel-Multiple-Orders&lt;/a&gt;
     */
    public String cancelMultipleOrders(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L267">        ParameterChecker.checkParameter(parameters, &quot;symbol&quot;, String.class);</span>
<span class="nc" id="L268">        return requestHandler.sendSignedRequest(productUrl, BATCH_ORDERS, parameters, HttpMethod.DELETE, showLimitUsage);</span>
    }

<span class="fc" id="L271">    private final String CANCEL_ALL = &quot;/v1/countdownCancelAll&quot;;</span>
    /**
     * Cancel all open orders of the specified symbol at the end of the specified countdown.
     * &lt;br&gt;&lt;br&gt;
     * POST /v1/countdownCancelAll
     * &lt;br&gt;
     * @param
     * parameters LinkedHashedMap of String,Object pair
     *            where String is the name of the parameter and Object is the value of the parameter
     * &lt;br&gt;&lt;br&gt;
     * symbol -- mandatory/string &lt;br&gt;
     * countdownTime -- mandatory/long &lt;br&gt;
     * recvWindow -- optional/long &lt;br&gt;
     * @return String
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/usds-margined-futures/trade/rest-api/Auto-Cancel-All-Open-Orders&quot;&gt;
     *     https://developers.binance.com/docs/derivatives/usds-margined-futures/trade/rest-api/Auto-Cancel-All-Open-Orders&lt;/a&gt;
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/coin-margined-futures/trade/Auto-Cancel-All-Open-Orders&quot;&gt;
     *     https://developers.binance.com/docs/derivatives/coin-margined-futures/trade/Auto-Cancel-All-Open-Orders&lt;/a&gt;
     */
    public String autoCancelOpen(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L291">        ParameterChecker.checkParameter(parameters, &quot;symbol&quot;, String.class);</span>
<span class="fc" id="L292">        ParameterChecker.checkParameter(parameters, &quot;countdownTime&quot;, Integer.class);</span>
<span class="fc" id="L293">        return requestHandler.sendSignedRequest(productUrl, CANCEL_ALL, parameters, HttpMethod.POST, showLimitUsage);</span>
    }

<span class="fc" id="L296">    private final String OPEN_ORDER = &quot;/v1/openOrder&quot;;</span>
    /**
     * Query Current Open Order
     * &lt;br&gt;&lt;br&gt;
     * GET /v1/openOrder
     * &lt;br&gt;
     * @param
     * parameters LinkedHashedMap of String,Object pair
     *            where String is the name of the parameter and Object is the value of the parameter
     * &lt;br&gt;&lt;br&gt;
     * symbol -- mandatory/string &lt;br&gt;
     * orderId -- optional/long &lt;br&gt;
     * origClientOrderId - optional/string &lt;br&gt;
     * recvWindow -- optional/long &lt;br&gt;
     * @return String
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/usds-margined-futures/trade/rest-api/Query-Current-Open-Order&quot;&gt;
     *    https://developers.binance.com/docs/derivatives/usds-margined-futures/trade/rest-api/Query-Current-Open-Order&lt;/a&gt;
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/coin-margined-futures/trade/Query-Current-Open-Order&quot;&gt;
     *    https://developers.binance.com/docs/derivatives/coin-margined-futures/trade/Query-Current-Open-Order&lt;/a&gt;
     */
    public String queryCurrentOpenOrder(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L317">        ParameterChecker.checkParameter(parameters, &quot;symbol&quot;, String.class);</span>
<span class="fc" id="L318">        ParameterChecker.checkOrParameters(parameters, &quot;orderId&quot;, &quot;origClientOrderId&quot;);</span>
<span class="fc" id="L319">        return requestHandler.sendSignedRequest(productUrl, OPEN_ORDER, parameters, HttpMethod.GET, showLimitUsage);</span>
    }

<span class="fc" id="L322">    private final String LEVERAGE = &quot;/v1/leverage&quot;;</span>
    /**
     * Change user's initial leverage of specific symbol market.
     * &lt;br&gt;&lt;br&gt;
     * POST /v1/leverage
     * &lt;br&gt;
     * @param
     * parameters LinkedHashedMap of String,Object pair
     *            where String is the name of the parameter and Object is the value of the parameter
     * &lt;br&gt;&lt;br&gt;
     * symbol -- mandatory/string &lt;br&gt;
     * leverage -- mandatory/integer &lt;br&gt;
     * recvWindow -- optional/long &lt;br&gt;
     * @return String
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/usds-margined-futures/trade/rest-api/Change-Initial-Leverage&quot;&gt;
     *     https://developers.binance.com/docs/derivatives/usds-margined-futures/trade/rest-api/Change-Initial-Leverage&lt;/a&gt;
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/coin-margined-futures/trade/Change-Initial-Leverage&quot;&gt;
     *     https://developers.binance.com/docs/derivatives/coin-margined-futures/trade/Change-Initial-Leverage&lt;/a&gt;
     */
    public String changeInitialLeverage(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L342">        ParameterChecker.checkParameter(parameters, &quot;symbol&quot;, String.class);</span>
<span class="fc" id="L343">        ParameterChecker.checkParameter(parameters, &quot;leverage&quot;, Integer.class);</span>
<span class="fc" id="L344">        return requestHandler.sendSignedRequest(productUrl, LEVERAGE, parameters, HttpMethod.POST, showLimitUsage);</span>
    }

<span class="fc" id="L347">    private final String MARGIN_TYPE = &quot;/v1/marginType&quot;;</span>
    /**
     * Change user's margin type
     * &lt;br&gt;&lt;br&gt;
     * POST /v1/marginType
     * &lt;br&gt;
     * @param
     * parameters LinkedHashedMap of String,Object pair
     *            where String is the name of the parameter and Object is the value of the parameter
     * &lt;br&gt;&lt;br&gt;
     * symbol -- mandatory/string &lt;br&gt;
     * marginType -- mandatory/enum &lt;br&gt;
     * recvWindow -- optional/long &lt;br&gt;
     * @return String
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/usds-margined-futures/trade/rest-api/Change-Margin-Type&quot;&gt;
     *     https://developers.binance.com/docs/derivatives/usds-margined-futures/trade/rest-api/Change-Margin-Type&lt;/a&gt;
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/coin-margined-futures/trade/Change-Margin-Type&quot;&gt;
     *     https://developers.binance.com/docs/derivatives/coin-margined-futures/trade/Change-Margin-Type&lt;/a&gt;
     */
    public String changeMarginType(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L367">        ParameterChecker.checkParameter(parameters, &quot;symbol&quot;, String.class);</span>
<span class="fc" id="L368">        ParameterChecker.checkParameter(parameters, &quot;marginType&quot;, String.class);</span>
<span class="fc" id="L369">        return requestHandler.sendSignedRequest(productUrl, MARGIN_TYPE, parameters, HttpMethod.POST, showLimitUsage);</span>
    }

<span class="fc" id="L372">    private final String POSITION_MARGIN = &quot;/v1/positionMargin&quot;;</span>
    /**
     * Modify Isolated Position Margin
     * &lt;br&gt;&lt;br&gt;
     * POST /v1/positionMargin
     * &lt;br&gt;
     * @param
     * parameters LinkedHashedMap of String,Object pair
     *            where String is the name of the parameter and Object is the value of the parameter
     * &lt;br&gt;&lt;br&gt;
     * symbol -- mandatory/string &lt;br&gt;
     * positionSide -- optional/enum &lt;br&gt;
     * amount -- mandatory/decimal &lt;br&gt;
     * type -- mandatory/integer &lt;br&gt;
     * recvWindow -- optional/long &lt;br&gt;
     * @return String
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/usds-margined-futures/trade/rest-api/Modify-Isolated-Position-Margin&quot;&gt;
     *     https://developers.binance.com/docs/derivatives/usds-margined-futures/trade/rest-api/Modify-Isolated-Position-Margin&lt;/a&gt;
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/coin-margined-futures/trade/Modify-Isolated-Position-Margin&quot;&gt;
     *     https://developers.binance.com/docs/derivatives/coin-margined-futures/trade/Modify-Isolated-Position-Margin&lt;/a&gt;
     */
    public String modifyIsolatedPositionMargin(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L394">        ParameterChecker.checkParameter(parameters, &quot;symbol&quot;, String.class);</span>
<span class="fc" id="L395">        ParameterChecker.checkRequiredParameter(parameters, &quot;amount&quot;);</span>
<span class="fc" id="L396">        ParameterChecker.checkParameter(parameters, &quot;type&quot;, Integer.class);</span>
<span class="fc" id="L397">        return requestHandler.sendSignedRequest(productUrl, POSITION_MARGIN, parameters, HttpMethod.POST, showLimitUsage);</span>
    }

<span class="fc" id="L400">    private final String POSITION_MARGIN_HISTORY = &quot;/v1/positionMargin/history&quot;;</span>
    /**
     * Get position margin change history
     * &lt;br&gt;&lt;br&gt;
     * GET /v1/positionMargin/history
     * &lt;br&gt;
     * @param
     * parameters LinkedHashedMap of String,Object pair
     *            where String is the name of the parameter and Object is the value of the parameter
     * &lt;br&gt;&lt;br&gt;
     * symbol -- mandatory/string &lt;br&gt;
     * type -- optional/integer &lt;br&gt;
     * startTime -- optional/long &lt;br&gt;
     * endTime -- optional/long &lt;br&gt;
     * limit -- optional/integer &lt;br&gt;
     * recvWindow -- optional/long &lt;br&gt;
     * @return String
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/usds-margined-futures/trade/rest-api/Get-Position-Margin-Change-History&quot;&gt;
     *    https://developers.binance.com/docs/derivatives/usds-margined-futures/trade/rest-api/Get-Position-Margin-Change-History&lt;/a&gt;
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/coin-margined-futures/trade/Get-Position-Margin-Change-History&quot;&gt;
     *    https://developers.binance.com/docs/derivatives/coin-margined-futures/trade/Get-Position-Margin-Change-History&lt;/a&gt;
     */
    public String getPositionMarginChangeHistory(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L423">        ParameterChecker.checkParameter(parameters, &quot;symbol&quot;, String.class);</span>
<span class="fc" id="L424">        return requestHandler.sendSignedRequest(productUrl, POSITION_MARGIN_HISTORY, parameters, HttpMethod.GET, showLimitUsage);</span>
    }

<span class="fc" id="L427">    private final String INCOME = &quot;/v1/income&quot;;</span>
    /**
     * Get Income History
     * &lt;br&gt;&lt;br&gt;
     * GET /v1/income
     * &lt;br&gt;
     * @param
     * parameters LinkedHashedMap of String,Object pair
     *            where String is the name of the parameter and Object is the value of the parameter
     * &lt;br&gt;&lt;br&gt;
     * symbol -- optional/string &lt;br&gt;
     * incomeType -- optional/string &lt;br&gt;
     * startTime -- optional/long &lt;br&gt;
     * endTime -- optional/long &lt;br&gt;
     * limit -- optional/integer &lt;br&gt;
     * recvWindow -- optional/long &lt;br&gt;
     * @return String
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/usds-margined-futures/account/rest-api/Get-Income-History&quot;&gt;
     *    https://developers.binance.com/docs/derivatives/usds-margined-futures/account/rest-api/Get-Income-History&lt;/a&gt;
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/coin-margined-futures/account/Get-Income-History&quot;&gt;
     *    https://developers.binance.com/docs/derivatives/coin-margined-futures/account/Get-Income-History&lt;/a&gt;
     */
    public String getIncomeHistory(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L450">        return requestHandler.sendSignedRequest(productUrl, INCOME, parameters, HttpMethod.GET, showLimitUsage);</span>
    }

<span class="fc" id="L453">    private final String ADL_QUANTILE = &quot;/v1/adlQuantile&quot;;</span>
    /**
     * Position ADL Quantile Estimation
     * &lt;br&gt;&lt;br&gt;
     * GET /v1/adlQuantile
     * &lt;br&gt;
     * @param
     * parameters LinkedHashedMap of String,Object pair
     *            where String is the name of the parameter and Object is the value of the parameter
     * &lt;br&gt;&lt;br&gt;
     * symbol -- optional/string &lt;br&gt;
     * recvWindow -- optional/long &lt;br&gt;
     * @return String
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/usds-margined-futures/trade/rest-api/Position-ADL-Quantile-Estimation&quot;&gt;
     *    https://developers.binance.com/docs/derivatives/usds-margined-futures/trade/rest-api/Position-ADL-Quantile-Estimation&lt;/a&gt;
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/coin-margined-futures/trade/Position-ADL-Quantile-Estimation&quot;&gt;
     *    https://developers.binance.com/docs/derivatives/coin-margined-futures/trade/Position-ADL-Quantile-Estimation&lt;/a&gt;
     */
    public String getAdlQuantile(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L472">        return requestHandler.sendSignedRequest(productUrl, ADL_QUANTILE, parameters, HttpMethod.GET, showLimitUsage);</span>
    }

<span class="fc" id="L475">    private final String FORCE_ORDERS = &quot;/v1/forceOrders&quot;;</span>
    /**
     * User's Force Orders
     * &lt;br&gt;&lt;br&gt;
     * GET /v1/forceOrders
     * &lt;br&gt;
     * @param
     * parameters LinkedHashedMap of String,Object pair
     *            where String is the name of the parameter and Object is the value of the parameter
     * &lt;br&gt;&lt;br&gt;
     * symbol -- optional/string &lt;br&gt;
     * autoCloseType -- optional/enum &lt;br&gt;
     * startTime -- optional/long &lt;br&gt;
     * endTime -- optional/long &lt;br&gt;
     * limit -- optional/integer &lt;br&gt;
     * recvWindow -- optional/long &lt;br&gt;
     * @return String
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/usds-margined-futures/trade/rest-api/Users-Force-Orders&quot;&gt;
     *    https://developers.binance.com/docs/derivatives/usds-margined-futures/trade/rest-api/Users-Force-Orders&lt;/a&gt;
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/coin-margined-futures/trade/Users-Force-Orders&quot;&gt;
     *    https://developers.binance.com/docs/derivatives/coin-margined-futures/trade/Users-Force-Orders&lt;/a&gt;
     */
    public String getForceOrders(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L498">        return requestHandler.sendSignedRequest(productUrl, FORCE_ORDERS, parameters, HttpMethod.GET, showLimitUsage);</span>
    }

<span class="fc" id="L501">    private final String COMMISSION_RATE = &quot;/v1/commissionRate&quot;;</span>
    /**
     * User's Commission Rate
     * &lt;br&gt;&lt;br&gt;
     * GET /v1/commissionRate
     * &lt;br&gt;
     * @param
     * parameters LinkedHashedMap of String,Object pair
     *            where String is the name of the parameter and Object is the value of the parameter
     * &lt;br&gt;&lt;br&gt;
     * symbol -- mandatory/string &lt;br&gt;
     * recvWindow -- optional/long &lt;br&gt;
     * @return String
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/usds-margined-futures/account/rest-api/User-Commission-Rate&quot;&gt;
     *    https://developers.binance.com/docs/derivatives/usds-margined-futures/account/rest-api/User-Commission-Rate&lt;/a&gt;
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/coin-margined-futures/account/User-Commission-Rate&quot;&gt;
     *    https://developers.binance.com/docs/derivatives/coin-margined-futures/account/User-Commission-Rate&lt;/a&gt;
     */
    public String getCommissionRate(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L520">        ParameterChecker.checkParameter(parameters, &quot;symbol&quot;, String.class);</span>
<span class="fc" id="L521">        return requestHandler.sendSignedRequest(productUrl, COMMISSION_RATE, parameters, HttpMethod.GET, showLimitUsage);</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.7.************</span></div></body></html>