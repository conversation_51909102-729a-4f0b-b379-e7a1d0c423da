#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
多时间周期管理器

统一管理多个时间周期的数据处理流程，协调MultiTimeframeProcessor、TimeSeriesSynchronizer
和DataQualityChecker三个组件，提供统一的数据访问接口和特征融合管理功能。
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any, Union, Callable
from datetime import datetime, timedelta
import threading
from collections import defaultdict, deque
from dataclasses import dataclass
import time
from enum import Enum

# 导入相关组件
from .multi_timeframe_processor import MultiTimeframeProcessor, TimeframeConfig
from .time_series_synchronizer import TimeSeriesSynchronizer, SyncConfig, DataSource
from data.quality.data_quality_checker import DataQualityChecker, QualityMetrics, QualityLevel


class ProcessingMode(Enum):
    """处理模式枚举"""
    REAL_TIME = "real_time"      # 实时处理
    BATCH = "batch"              # 批量处理
    HYBRID = "hybrid"            # 混合模式


class FusionMethod(Enum):
    """特征融合方法枚举"""
    WEIGHTED_AVERAGE = "weighted_average"    # 加权平均
    ATTENTION = "attention"                  # 注意力机制
    CONCATENATE = "concatenate"              # 简单拼接
    ENSEMBLE = "ensemble"                    # 集成方法


@dataclass
class ProcessingConfig:
    """处理配置"""
    mode: ProcessingMode = ProcessingMode.REAL_TIME
    fusion_method: FusionMethod = FusionMethod.WEIGHTED_AVERAGE
    quality_threshold: float = 0.8
    auto_quality_fix: bool = True
    enable_caching: bool = True
    max_cache_size: int = 1000


@dataclass
class ProcessingResult:
    """处理结果"""
    symbol: str
    timeframes: List[str]
    aligned_data: Optional[pd.DataFrame]
    fused_features: Optional[pd.DataFrame]
    quality_metrics: Dict[str, QualityMetrics]
    processing_time_ms: float
    success: bool
    error_message: Optional[str]


class MultiTimeframeManager:
    """多时间周期管理器"""
    
    def __init__(self, config: Dict = None):
        """
        初始化多时间周期管理器
        
        Args:
            config: 配置参数字典
        """
        self.logger = logging.getLogger('cryptoMlStrategy.data.multi_timeframe_manager')
        
        # 默认配置
        self.config = {
            'processing': {
                'mode': 'real_time',
                'fusion_method': 'weighted_average',
                'quality_threshold': 0.8,
                'auto_quality_fix': True,
                'enable_caching': True,
                'max_cache_size': 1000
            },
            'timeframes': {
                '1m': {'enabled': True, 'weight': 0.1, 'priority': 1},
                '5m': {'enabled': True, 'weight': 0.2, 'priority': 2},
                '15m': {'enabled': True, 'weight': 0.3, 'priority': 3},
                '1h': {'enabled': True, 'weight': 0.4, 'priority': 4},
                '4h': {'enabled': False, 'weight': 0.0, 'priority': 5},
                '1d': {'enabled': False, 'weight': 0.0, 'priority': 6}
            },
            'quality': {
                'min_quality_score': 0.7,
                'enable_auto_fix': True,
                'quality_check_interval': 300,  # 秒
                'quality_history_size': 100
            },
            'synchronization': {
                'default_method': 'nearest',
                'tolerance_ms': 5000,
                'enable_delay_compensation': True,
                'max_sync_gap_ms': 60000
            },
            'feature_fusion': {
                'default_method': 'weighted_average',
                'normalize_features': True,
                'feature_selection': True,
                'selection_threshold': 0.01
            },
            'performance': {
                'enable_parallel_processing': False,
                'max_workers': 4,
                'processing_timeout_seconds': 30,
                'memory_limit_mb': 1000
            }
        }
        
        # 更新用户配置
        if config:
            self._deep_update(self.config, config)
        
        # 初始化组件
        self.processor = MultiTimeframeProcessor(self.config.get('processor', {}))
        self.synchronizer = TimeSeriesSynchronizer(self.config.get('synchronizer', {}))
        self.quality_checker = DataQualityChecker(self.config.get('quality_checker', {}))
        
        # 启用的时间周期
        self.enabled_timeframes = [
            tf for tf, cfg in self.config['timeframes'].items() 
            if cfg.get('enabled', False)
        ]
        
        # 数据源注册
        self._register_data_sources()
        
        # 处理结果缓存
        self.result_cache = {}
        
        # 统计信息
        self.stats = {
            'total_processed': 0,
            'successful_processed': 0,
            'failed_processed': 0,
            'average_processing_time_ms': 0,
            'quality_checks_performed': 0,
            'auto_fixes_applied': 0,
            'last_processing_time': None
        }
        
        # 线程锁
        self.manager_lock = threading.RLock()
        
        self.logger.info(f"多时间周期管理器初始化完成，启用时间周期: {self.enabled_timeframes}")
    
    def _deep_update(self, base_dict: Dict, update_dict: Dict):
        """深度更新字典"""
        for key, value in update_dict.items():
            if key in base_dict and isinstance(base_dict[key], dict) and isinstance(value, dict):
                self._deep_update(base_dict[key], value)
            else:
                base_dict[key] = value
    
    def _register_data_sources(self):
        """注册数据源到同步器"""
        try:
            for timeframe in self.enabled_timeframes:
                tf_config = self.config['timeframes'][timeframe]
                priority = tf_config.get('priority', 1)
                weight = tf_config.get('weight', 1.0)
                
                self.synchronizer.register_data_source(
                    name=timeframe,
                    priority=priority,
                    quality_weight=weight,
                    is_primary=(timeframe == '1m')  # 1分钟作为主数据源
                )
            
            self.logger.info("数据源注册完成")
            
        except Exception as e:
            self.logger.error(f"数据源注册失败: {e}")
    
    def add_timeframe_data(self, symbol: str, timeframe: str, data: Dict[str, Any]) -> bool:
        """
        添加时间周期数据
        
        Args:
            symbol: 交易对符号
            timeframe: 时间周期
            data: 数据字典
            
        Returns:
            是否成功添加
        """
        try:
            if timeframe not in self.enabled_timeframes:
                self.logger.warning(f"时间周期未启用: {timeframe}")
                return False
            
            # 添加数据到处理器
            success = self.processor.add_data_point(symbol, timeframe, data)
            
            if success:
                # 清除相关缓存
                self._clear_related_cache(symbol)
                self.logger.debug(f"数据添加成功: {symbol} {timeframe}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"添加时间周期数据失败: {e}")
            return False
    
    def process_multi_timeframe_data(self, symbol: str, 
                                   target_timeframe: str = None,
                                   quality_check: bool = True) -> ProcessingResult:
        """
        处理多时间周期数据
        
        Args:
            symbol: 交易对符号
            target_timeframe: 目标时间周期
            quality_check: 是否执行质量检查
            
        Returns:
            处理结果
        """
        start_time = time.time()
        
        try:
            with self.manager_lock:
                self.stats['total_processed'] += 1
                self.stats['last_processing_time'] = datetime.now()
                
                # 检查缓存
                cache_key = f"{symbol}_{target_timeframe}_{quality_check}"
                if self.config['processing']['enable_caching'] and cache_key in self.result_cache:
                    cached_result = self.result_cache[cache_key]
                    self.logger.debug(f"使用缓存结果: {symbol}")
                    return cached_result
                
                # 获取对齐后的数据
                aligned_data = self.processor.get_aligned_data(symbol, target_timeframe)
                
                if aligned_data is None or aligned_data.empty:
                    return self._create_failed_result(
                        symbol, [], "无法获取对齐后的数据", start_time
                    )
                
                # 质量检查
                quality_metrics = {}
                if quality_check:
                    quality_metrics = self._perform_quality_checks(symbol, aligned_data)
                    
                    # 检查质量是否满足阈值
                    min_quality = min(metrics.overall_score for metrics in quality_metrics.values())
                    if min_quality < self.config['quality']['min_quality_score']:
                        if self.config['quality']['enable_auto_fix']:
                            aligned_data = self._auto_fix_quality_issues(aligned_data, quality_metrics)
                            self.stats['auto_fixes_applied'] += 1
                        else:
                            self.logger.warning(f"数据质量不满足要求: {symbol}, 最低分数: {min_quality:.3f}")
                
                # 特征融合
                fused_features = self._perform_feature_fusion(symbol, aligned_data)
                
                # 创建处理结果
                processing_time_ms = (time.time() - start_time) * 1000
                result = ProcessingResult(
                    symbol=symbol,
                    timeframes=self.enabled_timeframes,
                    aligned_data=aligned_data,
                    fused_features=fused_features,
                    quality_metrics=quality_metrics,
                    processing_time_ms=processing_time_ms,
                    success=True,
                    error_message=None
                )
                
                # 缓存结果
                if self.config['processing']['enable_caching']:
                    self._cache_result(cache_key, result)
                
                # 更新统计
                self.stats['successful_processed'] += 1
                self._update_processing_time_stats(processing_time_ms)
                
                self.logger.info(f"多时间周期数据处理完成: {symbol}, 耗时: {processing_time_ms:.2f}ms")
                return result
                
        except Exception as e:
            self.logger.error(f"多时间周期数据处理失败: {e}")
            self.stats['failed_processed'] += 1
            return self._create_failed_result(symbol, self.enabled_timeframes, str(e), start_time)
    
    def _perform_quality_checks(self, symbol: str, data: pd.DataFrame) -> Dict[str, QualityMetrics]:
        """执行质量检查"""
        try:
            quality_metrics = {}
            
            # 对每个时间周期的数据进行质量检查
            for timeframe in self.enabled_timeframes:
                # 提取该时间周期的列
                timeframe_columns = [col for col in data.columns if col.endswith(f'_{timeframe}')]
                
                if timeframe_columns:
                    timeframe_data = data[timeframe_columns]
                    # 移除时间周期后缀以便质量检查器处理
                    timeframe_data.columns = [col.replace(f'_{timeframe}', '') for col in timeframe_data.columns]
                    
                    metrics = self.quality_checker.check_data_quality(
                        timeframe_data, symbol, timeframe
                    )
                    quality_metrics[timeframe] = metrics
            
            self.stats['quality_checks_performed'] += len(quality_metrics)
            return quality_metrics
            
        except Exception as e:
            self.logger.error(f"质量检查失败: {e}")
            return {}
    
    def _auto_fix_quality_issues(self, data: pd.DataFrame, 
                               quality_metrics: Dict[str, QualityMetrics]) -> pd.DataFrame:
        """自动修复质量问题"""
        try:
            fixed_data = data.copy()
            
            # 简单的自动修复：前向填充缺失值
            fixed_data = fixed_data.fillna(method='ffill')
            
            # 移除仍有缺失值的行
            fixed_data = fixed_data.dropna()
            
            self.logger.info("自动质量修复完成")
            return fixed_data
            
        except Exception as e:
            self.logger.error(f"自动质量修复失败: {e}")
            return data
    
    def _perform_feature_fusion(self, symbol: str, data: pd.DataFrame) -> Optional[pd.DataFrame]:
        """执行特征融合"""
        try:
            fusion_method = self.config['feature_fusion']['default_method']
            
            if fusion_method == 'weighted_average':
                return self._weighted_average_fusion(data)
            elif fusion_method == 'concatenate':
                return data  # 简单拼接就是原数据
            elif fusion_method == 'attention':
                return self._attention_fusion(data)
            else:
                self.logger.warning(f"不支持的融合方法: {fusion_method}")
                return data
                
        except Exception as e:
            self.logger.error(f"特征融合失败: {e}")
            return data
    
    def _weighted_average_fusion(self, data: pd.DataFrame) -> pd.DataFrame:
        """加权平均特征融合"""
        try:
            fused_data = data.copy()
            
            # 基础特征列表
            base_features = ['open', 'high', 'low', 'close', 'volume']
            
            for feature in base_features:
                # 找到所有时间周期的该特征列
                feature_cols = [col for col in data.columns if col.startswith(f'{feature}_')]
                
                if len(feature_cols) > 1:
                    # 计算加权平均
                    weighted_sum = 0
                    total_weight = 0
                    
                    for col in feature_cols:
                        # 提取时间周期名称
                        timeframe = col.split('_')[-1]
                        if timeframe in self.config['timeframes']:
                            weight = self.config['timeframes'][timeframe].get('weight', 1.0)
                            weighted_sum += data[col] * weight
                            total_weight += weight
                    
                    if total_weight > 0:
                        fused_data[f'{feature}_fused'] = weighted_sum / total_weight
            
            return fused_data
            
        except Exception as e:
            self.logger.error(f"加权平均融合失败: {e}")
            return data
    
    def _attention_fusion(self, data: pd.DataFrame) -> pd.DataFrame:
        """注意力机制特征融合（简化版）"""
        try:
            # 简化的注意力机制实现
            return self._weighted_average_fusion(data)
            
        except Exception as e:
            self.logger.error(f"注意力融合失败: {e}")
            return data
    
    def _clear_related_cache(self, symbol: str):
        """清除相关缓存"""
        try:
            keys_to_remove = [key for key in self.result_cache.keys() if key.startswith(symbol)]
            for key in keys_to_remove:
                del self.result_cache[key]
                
        except Exception as e:
            self.logger.error(f"清除缓存失败: {e}")
    
    def _cache_result(self, cache_key: str, result: ProcessingResult):
        """缓存处理结果"""
        try:
            # 检查缓存大小限制
            max_cache_size = self.config['processing']['max_cache_size']
            if len(self.result_cache) >= max_cache_size:
                # 移除最旧的缓存项
                oldest_key = next(iter(self.result_cache))
                del self.result_cache[oldest_key]
            
            self.result_cache[cache_key] = result
            
        except Exception as e:
            self.logger.error(f"缓存结果失败: {e}")
    
    def _update_processing_time_stats(self, processing_time_ms: float):
        """更新处理时间统计"""
        try:
            current_avg = self.stats['average_processing_time_ms']
            total_processed = self.stats['total_processed']
            
            if total_processed <= 1:
                self.stats['average_processing_time_ms'] = processing_time_ms
            else:
                # 计算移动平均
                self.stats['average_processing_time_ms'] = (
                    (current_avg * (total_processed - 1) + processing_time_ms) / total_processed
                )
                
        except Exception as e:
            self.logger.error(f"处理时间统计更新失败: {e}")
    
    def _create_failed_result(self, symbol: str, timeframes: List[str], 
                            error_message: str, start_time: float) -> ProcessingResult:
        """创建失败的处理结果"""
        processing_time_ms = (time.time() - start_time) * 1000
        
        return ProcessingResult(
            symbol=symbol,
            timeframes=timeframes,
            aligned_data=None,
            fused_features=None,
            quality_metrics={},
            processing_time_ms=processing_time_ms,
            success=False,
            error_message=error_message
        )
    
    def get_manager_statistics(self) -> Dict[str, Any]:
        """获取管理器统计信息"""
        return {
            'manager_stats': self.stats.copy(),
            'enabled_timeframes': self.enabled_timeframes,
            'processor_stats': self.processor.get_statistics(),
            'synchronizer_stats': self.synchronizer.get_sync_statistics(),
            'quality_checker_stats': self.quality_checker.get_quality_statistics(),
            'cache_info': {
                'cache_size': len(self.result_cache),
                'cache_limit': self.config['processing']['max_cache_size']
            },
            'config': self.config
        }
    
    def clear_all_caches(self):
        """清除所有缓存"""
        try:
            with self.manager_lock:
                self.result_cache.clear()
                self.processor.clear_cache()
                self.synchronizer.clear_cache()
                
            self.logger.info("所有缓存已清除")
            
        except Exception as e:
            self.logger.error(f"清除缓存失败: {e}")
    
    def update_timeframe_config(self, timeframe: str, config: Dict[str, Any]) -> bool:
        """
        更新时间周期配置
        
        Args:
            timeframe: 时间周期
            config: 新配置
            
        Returns:
            是否更新成功
        """
        try:
            if timeframe not in self.config['timeframes']:
                self.logger.error(f"不支持的时间周期: {timeframe}")
                return False
            
            # 更新配置
            self.config['timeframes'][timeframe].update(config)
            
            # 重新计算启用的时间周期
            self.enabled_timeframes = [
                tf for tf, cfg in self.config['timeframes'].items() 
                if cfg.get('enabled', False)
            ]
            
            # 重新注册数据源
            self._register_data_sources()
            
            # 清除缓存
            self.clear_all_caches()
            
            self.logger.info(f"时间周期配置更新成功: {timeframe}")
            return True
            
        except Exception as e:
            self.logger.error(f"更新时间周期配置失败: {e}")
            return False