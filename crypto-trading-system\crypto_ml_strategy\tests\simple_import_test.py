#!/usr/bin/env python3
"""
Crypto ML Strategy - 简化导入测试脚本

该脚本验证重组后的目录结构中所有模块的导入功能。
"""

import sys
import os
from datetime import datetime

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

def test_imports():
    """测试所有模块导入"""
    print("开始导入测试...")
    print("=" * 50)
    
    total_tests = 0
    passed_tests = 0
    failed_tests = []
    
    # 测试模块列表
    test_modules = [
        # Task 12核心组件
        ("core.validation_result_types", "ValidationResult"),
        ("validators.latency_throughput_validators", "LatencyValidator"),
        ("validators.memory_accuracy_validators", "MemoryValidator"),
        ("coordinators.system_performance_coordinator", "SystemPerformanceValidator"),
        
        # 主要模块
        ("ml.ml_inference_optimizer", "MLInferenceOptimizer"),
        ("ml.memory_efficient_data_structures", "MemoryEfficientDataStructures"),
        ("benchmarks.benchmark_core_framework", "BenchmarkFramework"),
        ("infrastructure.error_handling_system", None),
        ("infrastructure.logging.logger", None),
        
        # 数据模块
        ("data.data_processor", None),
        ("data.cache.cache_manager", None),
        ("data.clients.influxdb_client", None),
        ("data.quality.data_quality_core", None),
        ("data.sync.optimized_sync_core", None),
        
        # 其他模块
        ("indicators.lppl_features", None),
        ("strategy.unified_ml", None),
        ("risk_management", None),
        ("utils", None),
        ("api.kafka_client", None),
        ("config", None),
    ]
    
    for module_name, class_name in test_modules:
        total_tests += 1
        try:
            if class_name:
                # 测试特定类的导入
                module = __import__(module_name, fromlist=[class_name])
                getattr(module, class_name)
                print(f"[PASS] {module_name}.{class_name}")
            else:
                # 测试模块导入
                __import__(module_name)
                print(f"[PASS] {module_name}")
            passed_tests += 1
        except Exception as e:
            print(f"[FAIL] {module_name}: {str(e)}")
            failed_tests.append((module_name, str(e)))
    
    # 测试跨目录引用
    print("\n测试跨目录引用...")
    try:
        from validators.latency_throughput_validators import LatencyValidator
        validator = LatencyValidator(target_latency_ms=100.0)
        print("[PASS] validators -> core 引用成功")
        passed_tests += 1
    except Exception as e:
        print(f"[FAIL] validators -> core 引用失败: {e}")
        failed_tests.append(("cross_ref.validators_to_core", str(e)))
    total_tests += 1
    
    try:
        from coordinators.system_performance_coordinator import SystemPerformanceValidator
        coordinator = SystemPerformanceValidator()
        print("[PASS] coordinators -> validators/core 引用成功")
        passed_tests += 1
    except Exception as e:
        print(f"[FAIL] coordinators -> validators/core 引用失败: {e}")
        failed_tests.append(("cross_ref.coordinators", str(e)))
    total_tests += 1
    
    # 输出结果
    print("\n" + "=" * 50)
    success_rate = (passed_tests / total_tests) * 100
    print(f"测试完成: {passed_tests}/{total_tests} 通过 ({success_rate:.1f}%)")
    
    if failed_tests:
        print("\n失败的测试:")
        for module, error in failed_tests:
            print(f"  - {module}: {error}")
    else:
        print("\n所有导入测试通过！目录重组成功！")
    
    # 生成简单报告
    report = f"""
# 导入测试报告

测试时间: {datetime.now().isoformat()}
测试总数: {total_tests}
通过测试: {passed_tests}
失败测试: {len(failed_tests)}
成功率: {success_rate:.1f}%

## 失败的测试
"""
    
    if failed_tests:
        for module, error in failed_tests:
            report += f"- {module}: {error}\n"
    else:
        report += "无失败测试\n"
    
    try:
        with open('import_test_report.txt', 'w', encoding='utf-8') as f:
            f.write(report)
        print(f"\n报告已保存到 import_test_report.txt")
    except Exception as e:
        print(f"无法保存报告: {e}")
    
    return len(failed_tests) == 0

if __name__ == "__main__":
    success = test_imports()
    sys.exit(0 if success else 1)