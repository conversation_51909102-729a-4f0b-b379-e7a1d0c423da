"""
模型管理器模块

实现模型的保存、加载、版本管理、性能评估和部署功能，
支持多种机器学习模型和与在线学习模块的集成。

Author: Crypto ML Strategy Team
Date: 2024-12-19
"""

import os
import pickle
import joblib
import json
import shutil
from typing import Dict, List, Optional, Tuple, Any, Union
from datetime import datetime
import logging
import threading
from pathlib import Path
import hashlib
import numpy as np
import pandas as pd

from .training_config import (
    ModelType, ModelMetadata, TrainingResult, PerformanceMetrics,
    TrainingPipelineConfig
)

logger = logging.getLogger(__name__)


class ModelManager:
    """模型管理器类"""
    
    def __init__(self, config: TrainingPipelineConfig):
        """
        初始化模型管理器
        
        Args:
            config: 训练管道配置
        """
        self.config = config
        self.model_save_path = Path(config.model_config.model_save_path)
        self.model_save_path.mkdir(parents=True, exist_ok=True)
        
        # 模型注册表
        self.model_registry: Dict[str, ModelMetadata] = {}
        self.active_models: Dict[str, Any] = {}
        
        # 线程锁
        self._lock = threading.RLock()
        
        # 加载现有模型注册表
        self._load_model_registry()
        
        logger.info("Model manager initialized")
    
    def _load_model_registry(self) -> None:
        """加载模型注册表"""
        try:
            registry_file = self.model_save_path / "model_registry.json"
            if registry_file.exists():
                with open(registry_file, 'r') as f:
                    registry_data = json.load(f)
                
                for model_id, metadata_dict in registry_data.items():
                    # 重建ModelMetadata对象
                    metadata_dict['created_at'] = datetime.fromisoformat(metadata_dict['created_at'])
                    metadata_dict['model_type'] = ModelType(metadata_dict['model_type'])
                    
                    self.model_registry[model_id] = ModelMetadata(**metadata_dict)
                
                logger.info(f"Loaded {len(self.model_registry)} models from registry")
            
        except Exception as e:
            logger.error(f"Error loading model registry: {e}")
    
    def _save_model_registry(self) -> None:
        """保存模型注册表"""
        try:
            registry_file = self.model_save_path / "model_registry.json"
            registry_data = {}
            
            for model_id, metadata in self.model_registry.items():
                registry_data[model_id] = metadata.to_dict()
            
            with open(registry_file, 'w') as f:
                json.dump(registry_data, f, indent=2)
            
            logger.debug("Model registry saved")
            
        except Exception as e:
            logger.error(f"Error saving model registry: {e}")
    
    def _generate_model_id(self, model_type: ModelType, timestamp: datetime) -> str:
        """生成模型ID"""
        try:
            # 使用模型类型和时间戳生成唯一ID
            id_string = f"{model_type.value}_{timestamp.strftime('%Y%m%d_%H%M%S')}"
            
            # 添加哈希确保唯一性
            hash_object = hashlib.md5(id_string.encode())
            hash_hex = hash_object.hexdigest()[:8]
            
            return f"{id_string}_{hash_hex}"
            
        except Exception as e:
            logger.error(f"Error generating model ID: {e}")
            return f"model_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    def save_model(
        self,
        model: Any,
        model_type: ModelType,
        performance_metrics: PerformanceMetrics,
        feature_names: List[str],
        target_names: List[str],
        hyperparameters: Dict[str, Any],
        training_duration: float,
        description: Optional[str] = None
    ) -> str:
        """
        保存模型
        
        Args:
            model: 训练好的模型对象
            model_type: 模型类型
            performance_metrics: 性能指标
            feature_names: 特征名称列表
            target_names: 目标变量名称列表
            hyperparameters: 超参数
            training_duration: 训练时长
            description: 模型描述
            
        Returns:
            模型ID
        """
        try:
            with self._lock:
                timestamp = datetime.now()
                model_id = self._generate_model_id(model_type, timestamp)
                version = "1.0.0"
                
                # 检查是否已存在同类型模型，如果存在则增加版本号
                existing_models = [
                    m for m in self.model_registry.values() 
                    if m.model_type == model_type
                ]
                if existing_models:
                    latest_version = max([m.version for m in existing_models])
                    major, minor, patch = map(int, latest_version.split('.'))
                    version = f"{major}.{minor}.{patch + 1}"
                
                # 创建模型目录
                model_dir = self.model_save_path / model_id
                model_dir.mkdir(parents=True, exist_ok=True)
                
                # 保存模型文件
                model_file = model_dir / "model.pkl"
                
                # 根据模型类型选择保存方法
                if hasattr(model, 'save'):
                    # 深度学习模型（PyTorch, TensorFlow）
                    model.save(str(model_dir / "model"))
                elif hasattr(model, 'save_model'):
                    # XGBoost, LightGBM等
                    model.save_model(str(model_file))
                else:
                    # scikit-learn等
                    joblib.dump(model, model_file)
                
                # 计算模型大小
                model_size = sum(f.stat().st_size for f in model_dir.rglob('*') if f.is_file())
                
                # 创建模型元数据
                metadata = ModelMetadata(
                    model_id=model_id,
                    model_type=model_type,
                    version=version,
                    created_at=timestamp,
                    trained_on={
                        'features': len(feature_names),
                        'targets': len(target_names),
                        'config': self.config.to_dict()
                    },
                    performance_metrics=performance_metrics.to_dict(),
                    feature_names=feature_names,
                    target_names=target_names,
                    hyperparameters=hyperparameters,
                    training_duration=training_duration,
                    model_size=model_size,
                    description=description
                )
                
                # 保存元数据
                metadata_file = model_dir / "metadata.json"
                with open(metadata_file, 'w') as f:
                    json.dump(metadata.to_dict(), f, indent=2)
                
                # 保存特征名称
                features_file = model_dir / "features.json"
                with open(features_file, 'w') as f:
                    json.dump({
                        'feature_names': feature_names,
                        'target_names': target_names
                    }, f, indent=2)
                
                # 更新注册表
                self.model_registry[model_id] = metadata
                self._save_model_registry()
                
                logger.info(f"Model saved successfully: {model_id}")
                return model_id
                
        except Exception as e:
            logger.error(f"Error saving model: {e}")
            raise
    
    def load_model(self, model_id: str) -> Tuple[Any, ModelMetadata]:
        """
        加载模型
        
        Args:
            model_id: 模型ID
            
        Returns:
            (模型对象, 模型元数据)
        """
        try:
            with self._lock:
                if model_id not in self.model_registry:
                    raise ValueError(f"Model {model_id} not found in registry")
                
                # 检查缓存
                if model_id in self.active_models:
                    logger.debug(f"Using cached model: {model_id}")
                    return self.active_models[model_id], self.model_registry[model_id]
                
                metadata = self.model_registry[model_id]
                model_dir = self.model_save_path / model_id
                
                if not model_dir.exists():
                    raise FileNotFoundError(f"Model directory not found: {model_dir}")
                
                # 加载模型
                model_file = model_dir / "model.pkl"
                model_tf_dir = model_dir / "model"
                
                if model_tf_dir.exists():
                    # 深度学习模型
                    try:
                        import torch
                        model = torch.load(model_tf_dir / "model.pth")
                    except ImportError:
                        try:
                            import tensorflow as tf
                            model = tf.keras.models.load_model(str(model_tf_dir))
                        except ImportError:
                            raise ImportError("Neither PyTorch nor TensorFlow available for loading deep learning model")
                
                elif model_file.exists():
                    # 传统机器学习模型
                    if metadata.model_type in [ModelType.XGBOOST, ModelType.LIGHTGBM, ModelType.CATBOOST]:
                        # 树模型
                        if metadata.model_type == ModelType.XGBOOST:
                            import xgboost as xgb
                            model = xgb.Booster()
                            model.load_model(str(model_file))
                        elif metadata.model_type == ModelType.LIGHTGBM:
                            import lightgbm as lgb
                            model = lgb.Booster(model_file=str(model_file))
                        elif metadata.model_type == ModelType.CATBOOST:
                            from catboost import CatBoost
                            model = CatBoost()
                            model.load_model(str(model_file))
                    else:
                        # scikit-learn模型
                        model = joblib.load(model_file)
                
                else:
                    raise FileNotFoundError(f"Model file not found in {model_dir}")
                
                # 缓存模型
                self.active_models[model_id] = model
                
                logger.info(f"Model loaded successfully: {model_id}")
                return model, metadata
                
        except Exception as e:
            logger.error(f"Error loading model {model_id}: {e}")
            raise
    
    def get_model_metadata(self, model_id: str) -> Optional[ModelMetadata]:
        """获取模型元数据"""
        return self.model_registry.get(model_id)
    
    def list_models(
        self,
        model_type: Optional[ModelType] = None,
        sort_by: str = "created_at",
        ascending: bool = False
    ) -> List[ModelMetadata]:
        """
        列出模型
        
        Args:
            model_type: 模型类型过滤
            sort_by: 排序字段
            ascending: 是否升序
            
        Returns:
            模型元数据列表
        """
        try:
            models = list(self.model_registry.values())
            
            # 类型过滤
            if model_type:
                models = [m for m in models if m.model_type == model_type]
            
            # 排序
            if sort_by in ['created_at', 'training_duration', 'model_size']:
                models.sort(key=lambda x: getattr(x, sort_by), reverse=not ascending)
            elif sort_by in ['accuracy', 'f1_score', 'auc_roc']:
                models.sort(
                    key=lambda x: x.performance_metrics.get(sort_by, 0),
                    reverse=not ascending
                )
            
            return models
            
        except Exception as e:
            logger.error(f"Error listing models: {e}")
            return []
    
    def compare_models(self, model_ids: List[str]) -> pd.DataFrame:
        """
        比较模型性能
        
        Args:
            model_ids: 模型ID列表
            
        Returns:
            比较结果DataFrame
        """
        try:
            comparison_data = []
            
            for model_id in model_ids:
                if model_id in self.model_registry:
                    metadata = self.model_registry[model_id]
                    
                    row = {
                        'model_id': model_id,
                        'model_type': metadata.model_type.value,
                        'version': metadata.version,
                        'created_at': metadata.created_at,
                        'training_duration': metadata.training_duration,
                        'model_size': metadata.model_size,
                        **metadata.performance_metrics
                    }
                    comparison_data.append(row)
            
            df = pd.DataFrame(comparison_data)
            
            if not df.empty:
                # 按性能指标排序
                if 'accuracy' in df.columns:
                    df = df.sort_values('accuracy', ascending=False)
                elif 'f1_score' in df.columns:
                    df = df.sort_values('f1_score', ascending=False)
            
            return df
            
        except Exception as e:
            logger.error(f"Error comparing models: {e}")
            return pd.DataFrame()
    
    def delete_model(self, model_id: str) -> bool:
        """
        删除模型
        
        Args:
            model_id: 模型ID
            
        Returns:
            是否删除成功
        """
        try:
            with self._lock:
                if model_id not in self.model_registry:
                    logger.warning(f"Model {model_id} not found in registry")
                    return False
                
                # 删除模型文件
                model_dir = self.model_save_path / model_id
                if model_dir.exists():
                    shutil.rmtree(model_dir)
                
                # 从注册表中删除
                del self.model_registry[model_id]
                
                # 从缓存中删除
                if model_id in self.active_models:
                    del self.active_models[model_id]
                
                # 保存注册表
                self._save_model_registry()
                
                logger.info(f"Model deleted successfully: {model_id}")
                return True
                
        except Exception as e:
            logger.error(f"Error deleting model {model_id}: {e}")
            return False
    
    def get_best_model(
        self,
        model_type: Optional[ModelType] = None,
        metric: str = "accuracy"
    ) -> Optional[Tuple[str, ModelMetadata]]:
        """
        获取最佳模型
        
        Args:
            model_type: 模型类型过滤
            metric: 评估指标
            
        Returns:
            (模型ID, 模型元数据)
        """
        try:
            models = self.list_models(model_type)
            
            if not models:
                return None
            
            # 找到指标最高的模型
            best_model = max(
                models,
                key=lambda x: x.performance_metrics.get(metric, 0)
            )
            
            return best_model.model_id, best_model
            
        except Exception as e:
            logger.error(f"Error getting best model: {e}")
            return None
    
    def deploy_model(self, model_id: str, deployment_name: str = "production") -> bool:
        """
        部署模型
        
        Args:
            model_id: 模型ID
            deployment_name: 部署名称
            
        Returns:
            是否部署成功
        """
        try:
            with self._lock:
                if model_id not in self.model_registry:
                    raise ValueError(f"Model {model_id} not found")
                
                # 创建部署目录
                deployment_dir = self.model_save_path / "deployments" / deployment_name
                deployment_dir.mkdir(parents=True, exist_ok=True)
                
                # 复制模型文件
                model_dir = self.model_save_path / model_id
                deployment_model_dir = deployment_dir / "model"
                
                if deployment_model_dir.exists():
                    shutil.rmtree(deployment_model_dir)
                
                shutil.copytree(model_dir, deployment_model_dir)
                
                # 创建部署信息
                deployment_info = {
                    'model_id': model_id,
                    'deployment_name': deployment_name,
                    'deployed_at': datetime.now().isoformat(),
                    'model_metadata': self.model_registry[model_id].to_dict()
                }
                
                deployment_info_file = deployment_dir / "deployment_info.json"
                with open(deployment_info_file, 'w') as f:
                    json.dump(deployment_info, f, indent=2)
                
                logger.info(f"Model {model_id} deployed as {deployment_name}")
                return True
                
        except Exception as e:
            logger.error(f"Error deploying model {model_id}: {e}")
            return False
    
    def cleanup_old_models(self, keep_count: int = 10) -> int:
        """
        清理旧模型
        
        Args:
            keep_count: 保留的模型数量
            
        Returns:
            删除的模型数量
        """
        try:
            with self._lock:
                models = self.list_models(sort_by="created_at", ascending=True)
                
                if len(models) <= keep_count:
                    return 0
                
                models_to_delete = models[:-keep_count]
                deleted_count = 0
                
                for model in models_to_delete:
                    if self.delete_model(model.model_id):
                        deleted_count += 1
                
                logger.info(f"Cleaned up {deleted_count} old models")
                return deleted_count
                
        except Exception as e:
            logger.error(f"Error cleaning up old models: {e}")
            return 0
    
    def get_model_statistics(self) -> Dict[str, Any]:
        """获取模型统计信息"""
        try:
            models = list(self.model_registry.values())
            
            if not models:
                return {'total_models': 0}
            
            # 按类型统计
            type_counts = {}
            for model in models:
                model_type = model.model_type.value
                type_counts[model_type] = type_counts.get(model_type, 0) + 1
            
            # 性能统计
            accuracies = [m.performance_metrics.get('accuracy', 0) for m in models]
            f1_scores = [m.performance_metrics.get('f1_score', 0) for m in models]
            
            # 大小统计
            total_size = sum(m.model_size for m in models)
            avg_size = total_size / len(models)
            
            return {
                'total_models': len(models),
                'models_by_type': type_counts,
                'active_models': len(self.active_models),
                'average_accuracy': np.mean(accuracies) if accuracies else 0,
                'average_f1_score': np.mean(f1_scores) if f1_scores else 0,
                'total_size_mb': total_size / (1024 * 1024),
                'average_size_mb': avg_size / (1024 * 1024),
                'latest_model': max(models, key=lambda x: x.created_at).model_id if models else None
            }
            
        except Exception as e:
            logger.error(f"Error getting model statistics: {e}")
            return {'error': str(e)}
    
    def clear_cache(self) -> None:
        """清理模型缓存"""
        try:
            with self._lock:
                self.active_models.clear()
            logger.info("Model cache cleared")
        except Exception as e:
            logger.error(f"Error clearing model cache: {e}")
    
    def backup_models(self, backup_path: str) -> bool:
        """
        备份模型
        
        Args:
            backup_path: 备份路径
            
        Returns:
            是否备份成功
        """
        try:
            backup_dir = Path(backup_path)
            backup_dir.mkdir(parents=True, exist_ok=True)
            
            # 复制整个模型目录
            backup_model_dir = backup_dir / "models"
            if backup_model_dir.exists():
                shutil.rmtree(backup_model_dir)
            
            shutil.copytree(self.model_save_path, backup_model_dir)
            
            # 创建备份信息
            backup_info = {
                'backup_time': datetime.now().isoformat(),
                'total_models': len(self.model_registry),
                'models': list(self.model_registry.keys())
            }
            
            backup_info_file = backup_dir / "backup_info.json"
            with open(backup_info_file, 'w') as f:
                json.dump(backup_info, f, indent=2)
            
            logger.info(f"Models backed up to {backup_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error backing up models: {e}")
            return False
