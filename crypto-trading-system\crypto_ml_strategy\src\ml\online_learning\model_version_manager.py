#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
模型版本管理器

管理在线学习模型的版本控制，包括版本存储、比较、回滚、元数据管理等功能。
支持基于性能的版本保留策略和自动清理机制。
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any, Union
from datetime import datetime, timedelta
import threading
from collections import defaultdict, deque
from dataclasses import dataclass, field
import time
from enum import Enum
import json
import pickle
import hashlib
import shutil
from pathlib import Path
import os


class VersionStatus(Enum):
    """版本状态枚举"""
    ACTIVE = "active"           # 活跃版本
    ARCHIVED = "archived"       # 归档版本
    DEPRECATED = "deprecated"   # 废弃版本
    TESTING = "testing"         # 测试版本
    ROLLBACK = "rollback"       # 回滚版本


class ComparisonMetric(Enum):
    """比较指标枚举"""
    ACCURACY = "accuracy"
    LOSS = "loss"
    F1_SCORE = "f1_score"
    PRECISION = "precision"
    RECALL = "recall"
    AUC = "auc"
    CUSTOM = "custom"


@dataclass
class ModelMetadata:
    """模型元数据"""
    version_id: str
    model_name: str
    creation_time: datetime
    training_samples: int
    training_duration: float
    performance_metrics: Dict[str, float]
    hyperparameters: Dict[str, Any]
    data_hash: str
    model_size_mb: float
    status: VersionStatus
    tags: List[str] = field(default_factory=list)
    description: str = ""
    parent_version: Optional[str] = None
    checkpoint_path: str = ""


@dataclass
class VersionComparison:
    """版本比较结果"""
    version_a: str
    version_b: str
    comparison_metrics: Dict[str, Dict[str, float]]
    better_version: Optional[str]
    improvement_percentage: Dict[str, float]
    recommendation: str


class ModelVersionManager:
    """模型版本管理器"""
    
    def __init__(self, config: Dict = None):
        """
        初始化模型版本管理器
        
        Args:
            config: 配置参数字典
        """
        self.logger = logging.getLogger('cryptoMlStrategy.online_learning.version_manager')
        
        # 默认配置
        self.config = {
            'storage': {
                'base_path': './models/versions',
                'max_versions': 50,
                'auto_cleanup': True,
                'cleanup_interval_hours': 24,
                'compression_enabled': True
            },
            'retention': {
                'keep_best_versions': 10,
                'keep_recent_days': 7,
                'performance_threshold': 0.8,
                'min_improvement': 0.01
            },
            'backup': {
                'enable_backup': True,
                'backup_interval_hours': 6,
                'backup_path': './models/backups',
                'max_backups': 20
            },
            'comparison': {
                'default_metrics': ['accuracy', 'loss', 'f1_score'],
                'significance_threshold': 0.05,
                'min_samples_for_comparison': 100
            }
        }
        
        # 更新用户配置
        if config:
            self._deep_update(self.config, config)
        
        # 初始化存储路径
        self.base_path = Path(self.config['storage']['base_path'])
        self.backup_path = Path(self.config['backup']['backup_path'])
        self._ensure_directories()
        
        # 版本管理
        self.versions: Dict[str, ModelMetadata] = {}
        self.active_version: Optional[str] = None
        
        # 加载现有版本
        self._load_existing_versions()
        
        # 统计信息
        self.stats = {
            'total_versions': 0,
            'active_versions': 0,
            'archived_versions': 0,
            'total_rollbacks': 0,
            'total_comparisons': 0,
            'last_cleanup_time': None,
            'last_backup_time': None
        }
        
        # 线程锁
        self.version_lock = threading.RLock()
        
        # 启动后台任务
        self._start_background_tasks()
        
        self.logger.info("模型版本管理器初始化完成")
    
    def _deep_update(self, base_dict: Dict, update_dict: Dict):
        """深度更新字典"""
        for key, value in update_dict.items():
            if key in base_dict and isinstance(base_dict[key], dict) and isinstance(value, dict):
                self._deep_update(base_dict[key], value)
            else:
                base_dict[key] = value
    
    def _ensure_directories(self):
        """确保目录存在"""
        try:
            self.base_path.mkdir(parents=True, exist_ok=True)
            self.backup_path.mkdir(parents=True, exist_ok=True)
            
            # 创建子目录
            (self.base_path / 'models').mkdir(exist_ok=True)
            (self.base_path / 'metadata').mkdir(exist_ok=True)
            (self.base_path / 'checkpoints').mkdir(exist_ok=True)
            
        except Exception as e:
            self.logger.error(f"创建目录失败: {e}")
    
    def _load_existing_versions(self):
        """加载现有版本"""
        try:
            metadata_path = self.base_path / 'metadata'
            if not metadata_path.exists():
                return
            
            for metadata_file in metadata_path.glob('*.json'):
                try:
                    with open(metadata_file, 'r', encoding='utf-8') as f:
                        metadata_dict = json.load(f)
                    
                    # 转换为ModelMetadata对象
                    metadata = self._dict_to_metadata(metadata_dict)
                    self.versions[metadata.version_id] = metadata
                    
                    # 设置活跃版本
                    if metadata.status == VersionStatus.ACTIVE:
                        self.active_version = metadata.version_id
                        
                except Exception as e:
                    self.logger.error(f"加载版本元数据失败 {metadata_file}: {e}")
            
            self._update_stats()
            self.logger.info(f"加载了 {len(self.versions)} 个版本")
            
        except Exception as e:
            self.logger.error(f"加载现有版本失败: {e}")
    
    def _dict_to_metadata(self, metadata_dict: Dict) -> ModelMetadata:
        """将字典转换为ModelMetadata对象"""
        return ModelMetadata(
            version_id=metadata_dict['version_id'],
            model_name=metadata_dict['model_name'],
            creation_time=datetime.fromisoformat(metadata_dict['creation_time']),
            training_samples=metadata_dict['training_samples'],
            training_duration=metadata_dict['training_duration'],
            performance_metrics=metadata_dict['performance_metrics'],
            hyperparameters=metadata_dict['hyperparameters'],
            data_hash=metadata_dict['data_hash'],
            model_size_mb=metadata_dict['model_size_mb'],
            status=VersionStatus(metadata_dict['status']),
            tags=metadata_dict.get('tags', []),
            description=metadata_dict.get('description', ''),
            parent_version=metadata_dict.get('parent_version'),
            checkpoint_path=metadata_dict.get('checkpoint_path', '')
        )
    
    def _metadata_to_dict(self, metadata: ModelMetadata) -> Dict:
        """将ModelMetadata对象转换为字典"""
        return {
            'version_id': metadata.version_id,
            'model_name': metadata.model_name,
            'creation_time': metadata.creation_time.isoformat(),
            'training_samples': metadata.training_samples,
            'training_duration': metadata.training_duration,
            'performance_metrics': metadata.performance_metrics,
            'hyperparameters': metadata.hyperparameters,
            'data_hash': metadata.data_hash,
            'model_size_mb': metadata.model_size_mb,
            'status': metadata.status.value,
            'tags': metadata.tags,
            'description': metadata.description,
            'parent_version': metadata.parent_version,
            'checkpoint_path': metadata.checkpoint_path
        }
    
    def save_model_version(self, model: Any, model_name: str, 
                          performance_metrics: Dict[str, float],
                          hyperparameters: Dict[str, Any],
                          training_samples: int,
                          training_duration: float,
                          data_hash: str = None,
                          tags: List[str] = None,
                          description: str = "",
                          parent_version: str = None) -> Optional[str]:
        """
        保存模型版本
        
        Args:
            model: 模型对象
            model_name: 模型名称
            performance_metrics: 性能指标
            hyperparameters: 超参数
            training_samples: 训练样本数
            training_duration: 训练时长
            data_hash: 数据哈希
            tags: 标签列表
            description: 描述
            parent_version: 父版本ID
            
        Returns:
            版本ID
        """
        try:
            with self.version_lock:
                # 生成版本ID
                version_id = self._generate_version_id(model_name)
                
                # 保存模型文件
                model_path = self.base_path / 'models' / f'{version_id}.pkl'
                with open(model_path, 'wb') as f:
                    pickle.dump(model, f)
                
                # 计算模型大小
                model_size_mb = model_path.stat().st_size / (1024 * 1024)
                
                # 创建元数据
                metadata = ModelMetadata(
                    version_id=version_id,
                    model_name=model_name,
                    creation_time=datetime.now(),
                    training_samples=training_samples,
                    training_duration=training_duration,
                    performance_metrics=performance_metrics,
                    hyperparameters=hyperparameters,
                    data_hash=data_hash or self._calculate_data_hash(performance_metrics),
                    model_size_mb=model_size_mb,
                    status=VersionStatus.TESTING,
                    tags=tags or [],
                    description=description,
                    parent_version=parent_version,
                    checkpoint_path=str(model_path)
                )
                
                # 保存元数据
                self._save_metadata(metadata)
                
                # 存储版本
                self.versions[version_id] = metadata
                
                # 更新统计
                self._update_stats()
                
                self.logger.info(f"模型版本保存成功: {version_id}")
                return version_id
                
        except Exception as e:
            self.logger.error(f"保存模型版本失败: {e}")
            return None
    
    def _generate_version_id(self, model_name: str) -> str:
        """生成版本ID"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        hash_suffix = hashlib.md5(f"{model_name}_{timestamp}".encode()).hexdigest()[:8]
        return f"{model_name}_v{timestamp}_{hash_suffix}"
    
    def _calculate_data_hash(self, performance_metrics: Dict[str, float]) -> str:
        """计算数据哈希"""
        data_str = json.dumps(performance_metrics, sort_keys=True)
        return hashlib.md5(data_str.encode()).hexdigest()
    
    def _save_metadata(self, metadata: ModelMetadata):
        """保存元数据"""
        try:
            metadata_path = self.base_path / 'metadata' / f'{metadata.version_id}.json'
            metadata_dict = self._metadata_to_dict(metadata)
            
            with open(metadata_path, 'w', encoding='utf-8') as f:
                json.dump(metadata_dict, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            self.logger.error(f"保存元数据失败: {e}")
    
    def load_model_version(self, version_id: str) -> Optional[Any]:
        """
        加载模型版本
        
        Args:
            version_id: 版本ID
            
        Returns:
            模型对象
        """
        try:
            if version_id not in self.versions:
                self.logger.error(f"版本不存在: {version_id}")
                return None
            
            metadata = self.versions[version_id]
            model_path = Path(metadata.checkpoint_path)
            
            if not model_path.exists():
                self.logger.error(f"模型文件不存在: {model_path}")
                return None
            
            with open(model_path, 'rb') as f:
                model = pickle.load(f)
            
            self.logger.info(f"模型版本加载成功: {version_id}")
            return model
            
        except Exception as e:
            self.logger.error(f"加载模型版本失败: {e}")
            return None
    
    def set_active_version(self, version_id: str) -> bool:
        """
        设置活跃版本
        
        Args:
            version_id: 版本ID
            
        Returns:
            是否设置成功
        """
        try:
            with self.version_lock:
                if version_id not in self.versions:
                    self.logger.error(f"版本不存在: {version_id}")
                    return False
                
                # 将当前活跃版本设为归档
                if self.active_version:
                    old_metadata = self.versions[self.active_version]
                    old_metadata.status = VersionStatus.ARCHIVED
                    self._save_metadata(old_metadata)
                
                # 设置新的活跃版本
                new_metadata = self.versions[version_id]
                new_metadata.status = VersionStatus.ACTIVE
                self._save_metadata(new_metadata)
                
                self.active_version = version_id
                self._update_stats()
                
                self.logger.info(f"活跃版本已设置: {version_id}")
                return True
                
        except Exception as e:
            self.logger.error(f"设置活跃版本失败: {e}")
            return False
    
    def compare_versions(self, version_a: str, version_b: str,
                        metrics: List[str] = None) -> Optional[VersionComparison]:
        """
        比较两个版本
        
        Args:
            version_a: 版本A ID
            version_b: 版本B ID
            metrics: 比较指标列表
            
        Returns:
            比较结果
        """
        try:
            if version_a not in self.versions or version_b not in self.versions:
                self.logger.error("版本不存在")
                return None
            
            metadata_a = self.versions[version_a]
            metadata_b = self.versions[version_b]
            
            # 使用默认指标如果未指定
            if metrics is None:
                metrics = self.config['comparison']['default_metrics']
            
            # 比较指标
            comparison_metrics = {}
            improvement_percentage = {}
            better_count = 0
            
            for metric in metrics:
                if metric in metadata_a.performance_metrics and metric in metadata_b.performance_metrics:
                    value_a = metadata_a.performance_metrics[metric]
                    value_b = metadata_b.performance_metrics[metric]
                    
                    comparison_metrics[metric] = {
                        'version_a': value_a,
                        'version_b': value_b,
                        'difference': value_b - value_a
                    }
                    
                    # 计算改进百分比
                    if value_a != 0:
                        improvement = ((value_b - value_a) / abs(value_a)) * 100
                        improvement_percentage[metric] = improvement
                        
                        # 判断哪个版本更好（假设数值越大越好，除了loss）
                        if metric.lower() == 'loss':
                            if value_b < value_a:
                                better_count += 1
                        else:
                            if value_b > value_a:
                                better_count += 1
            
            # 确定更好的版本
            total_metrics = len(comparison_metrics)
            if better_count > total_metrics / 2:
                better_version = version_b
                recommendation = f"版本 {version_b} 在大多数指标上表现更好"
            elif better_count < total_metrics / 2:
                better_version = version_a
                recommendation = f"版本 {version_a} 在大多数指标上表现更好"
            else:
                better_version = None
                recommendation = "两个版本性能相近，建议进一步测试"
            
            comparison = VersionComparison(
                version_a=version_a,
                version_b=version_b,
                comparison_metrics=comparison_metrics,
                better_version=better_version,
                improvement_percentage=improvement_percentage,
                recommendation=recommendation
            )
            
            self.stats['total_comparisons'] += 1
            
            self.logger.info(f"版本比较完成: {version_a} vs {version_b}")
            return comparison
            
        except Exception as e:
            self.logger.error(f"版本比较失败: {e}")
            return None
    
    def rollback_to_version(self, version_id: str) -> bool:
        """
        回滚到指定版本
        
        Args:
            version_id: 目标版本ID
            
        Returns:
            是否回滚成功
        """
        try:
            with self.version_lock:
                if version_id not in self.versions:
                    self.logger.error(f"回滚目标版本不存在: {version_id}")
                    return False
                
                # 记录当前活跃版本
                old_active = self.active_version
                
                # 设置回滚版本为活跃
                if self.set_active_version(version_id):
                    # 更新版本状态
                    rollback_metadata = self.versions[version_id]
                    rollback_metadata.status = VersionStatus.ROLLBACK
                    self._save_metadata(rollback_metadata)
                    
                    self.stats['total_rollbacks'] += 1
                    
                    self.logger.info(f"成功回滚到版本: {version_id} (从 {old_active})")
                    return True
                
                return False
                
        except Exception as e:
            self.logger.error(f"版本回滚失败: {e}")
            return False
    
    def _start_background_tasks(self):
        """启动后台任务"""
        try:
            # 启动清理任务
            if self.config['storage']['auto_cleanup']:
                cleanup_thread = threading.Thread(target=self._cleanup_loop, daemon=True)
                cleanup_thread.start()
            
            # 启动备份任务
            if self.config['backup']['enable_backup']:
                backup_thread = threading.Thread(target=self._backup_loop, daemon=True)
                backup_thread.start()
                
        except Exception as e:
            self.logger.error(f"启动后台任务失败: {e}")
    
    def _cleanup_loop(self):
        """清理循环"""
        while True:
            try:
                time.sleep(self.config['storage']['cleanup_interval_hours'] * 3600)
                self._perform_cleanup()
                
            except Exception as e:
                self.logger.error(f"清理循环错误: {e}")
    
    def _backup_loop(self):
        """备份循环"""
        while True:
            try:
                time.sleep(self.config['backup']['backup_interval_hours'] * 3600)
                self._perform_backup()
                
            except Exception as e:
                self.logger.error(f"备份循环错误: {e}")
    
    def _perform_cleanup(self):
        """执行清理"""
        try:
            with self.version_lock:
                self.logger.info("开始执行版本清理")
                
                # 获取需要保留的版本
                versions_to_keep = self._get_versions_to_keep()
                
                # 删除不需要的版本
                deleted_count = 0
                for version_id in list(self.versions.keys()):
                    if version_id not in versions_to_keep:
                        if self._delete_version(version_id):
                            deleted_count += 1
                
                self.stats['last_cleanup_time'] = datetime.now()
                self.logger.info(f"版本清理完成，删除了 {deleted_count} 个版本")
                
        except Exception as e:
            self.logger.error(f"执行清理失败: {e}")
    
    def _get_versions_to_keep(self) -> set:
        """获取需要保留的版本"""
        versions_to_keep = set()
        
        # 保留活跃版本
        if self.active_version:
            versions_to_keep.add(self.active_version)
        
        # 保留最近的版本
        recent_cutoff = datetime.now() - timedelta(days=self.config['retention']['keep_recent_days'])
        recent_versions = [
            v_id for v_id, metadata in self.versions.items()
            if metadata.creation_time >= recent_cutoff
        ]
        versions_to_keep.update(recent_versions)
        
        # 保留性能最好的版本
        performance_threshold = self.config['retention']['performance_threshold']
        best_versions = []
        
        for v_id, metadata in self.versions.items():
            if 'accuracy' in metadata.performance_metrics:
                accuracy = metadata.performance_metrics['accuracy']
                if accuracy >= performance_threshold:
                    best_versions.append((v_id, accuracy))
        
        # 按性能排序并保留前N个
        best_versions.sort(key=lambda x: x[1], reverse=True)
        keep_best = self.config['retention']['keep_best_versions']
        versions_to_keep.update([v_id for v_id, _ in best_versions[:keep_best]])
        
        return versions_to_keep
    
    def _delete_version(self, version_id: str) -> bool:
        """删除版本"""
        try:
            if version_id not in self.versions:
                return False
            
            metadata = self.versions[version_id]
            
            # 删除模型文件
            model_path = Path(metadata.checkpoint_path)
            if model_path.exists():
                model_path.unlink()
            
            # 删除元数据文件
            metadata_path = self.base_path / 'metadata' / f'{version_id}.json'
            if metadata_path.exists():
                metadata_path.unlink()
            
            # 从内存中移除
            del self.versions[version_id]
            
            self.logger.debug(f"版本已删除: {version_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"删除版本失败: {e}")
            return False
    
    def _perform_backup(self):
        """执行备份"""
        try:
            self.logger.info("开始执行版本备份")
            
            # 创建备份目录
            backup_timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_dir = self.backup_path / f'backup_{backup_timestamp}'
            backup_dir.mkdir(parents=True, exist_ok=True)
            
            # 备份元数据
            metadata_backup_dir = backup_dir / 'metadata'
            metadata_backup_dir.mkdir(exist_ok=True)
            
            metadata_source = self.base_path / 'metadata'
            if metadata_source.exists():
                shutil.copytree(metadata_source, metadata_backup_dir, dirs_exist_ok=True)
            
            # 备份活跃版本的模型文件
            if self.active_version and self.active_version in self.versions:
                active_metadata = self.versions[self.active_version]
                model_source = Path(active_metadata.checkpoint_path)
                if model_source.exists():
                    model_backup_path = backup_dir / f'active_model_{self.active_version}.pkl'
                    shutil.copy2(model_source, model_backup_path)
            
            # 清理旧备份
            self._cleanup_old_backups()
            
            self.stats['last_backup_time'] = datetime.now()
            self.logger.info(f"版本备份完成: {backup_dir}")
            
        except Exception as e:
            self.logger.error(f"执行备份失败: {e}")
    
    def _cleanup_old_backups(self):
        """清理旧备份"""
        try:
            if not self.backup_path.exists():
                return
            
            backup_dirs = [d for d in self.backup_path.iterdir() if d.is_dir()]
            backup_dirs.sort(key=lambda x: x.stat().st_mtime, reverse=True)
            
            max_backups = self.config['backup']['max_backups']
            if len(backup_dirs) > max_backups:
                for old_backup in backup_dirs[max_backups:]:
                    shutil.rmtree(old_backup)
                    self.logger.debug(f"删除旧备份: {old_backup}")
                    
        except Exception as e:
            self.logger.error(f"清理旧备份失败: {e}")
    
    def _update_stats(self):
        """更新统计信息"""
        try:
            self.stats['total_versions'] = len(self.versions)
            self.stats['active_versions'] = sum(
                1 for metadata in self.versions.values() 
                if metadata.status == VersionStatus.ACTIVE
            )
            self.stats['archived_versions'] = sum(
                1 for metadata in self.versions.values() 
                if metadata.status == VersionStatus.ARCHIVED
            )
            
        except Exception as e:
            self.logger.error(f"更新统计信息失败: {e}")
    
    def get_version_statistics(self) -> Dict[str, Any]:
        """获取版本统计信息"""
        try:
            with self.version_lock:
                return {
                    'active_version': self.active_version,
                    'total_versions': len(self.versions),
                    'version_list': list(self.versions.keys()),
                    'stats': self.stats.copy(),
                    'config': self.config,
                    'storage_info': {
                        'base_path': str(self.base_path),
                        'backup_path': str(self.backup_path),
                        'total_size_mb': self._calculate_total_size()
                    }
                }
                
        except Exception as e:
            self.logger.error(f"获取版本统计失败: {e}")
            return {}
    
    def _calculate_total_size(self) -> float:
        """计算总存储大小"""
        try:
            total_size = 0
            for metadata in self.versions.values():
                total_size += metadata.model_size_mb
            return total_size
            
        except Exception as e:
            self.logger.error(f"计算存储大小失败: {e}")
            return 0.0