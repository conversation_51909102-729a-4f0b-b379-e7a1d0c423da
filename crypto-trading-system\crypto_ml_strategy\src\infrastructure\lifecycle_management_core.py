#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
生命周期管理核心模块

包含核心生命周期管理器类和应用启动/停止逻辑，
集成内存监控和错误恢复机制。
"""

import time
from typing import Dict, Any, Optional
from loguru import logger

from .infrastructure.memory_monitor import MemoryMonitor
from .infrastructure.async_error_recovery import AsyncErrorRecoveryService
from .infrastructure.lifecycle_manager import AdvancedLifecycleManager
from .infrastructure.service_interfaces import IHealthCheckService, IMetricsService


class EnhancedLifecycleManager:
    """
    增强的生命周期管理器核心类
    
    集成内存监控、异步错误恢复和信号处理，
    提供完整的应用生命周期管理功能。
    """
    
    def __init__(self,
                 memory_monitor: MemoryMonitor,
                 async_error_recovery: AsyncErrorRecoveryService,
                 health_check_service: IHealthCheckService,
                 metrics_service: IMetricsService,
                 base_lifecycle_manager: Optional[AdvancedLifecycleManager] = None):
        """
        初始化增强生命周期管理器
        
        Args:
            memory_monitor: 内存监控服务
            async_error_recovery: 异步错误恢复服务
            health_check_service: 健康检查服务
            metrics_service: 指标服务
            base_lifecycle_manager: 基础生命周期管理器（向后兼容）
        """
        self.memory_monitor = memory_monitor
        self.async_error_recovery = async_error_recovery
        self.health_check_service = health_check_service
        self.metrics_service = metrics_service
        self.base_lifecycle_manager = base_lifecycle_manager
        
        self._running = False
        self._startup_time = 0.0
        
        logger.info("增强生命周期管理器初始化完成")
    
    async def start_application(self) -> None:
        """
        启动应用
        
        集成内存监控、健康检查和信号处理。
        """
        start_time = time.time()
        
        try:
            logger.info("开始启动应用...")
            
            # 1. 启动内存监控
            await self._start_memory_monitoring()
            
            # 2. 启动健康检查
            await self._start_health_checks()
            
            # 3. 启动基础生命周期管理器（向后兼容）
            if self.base_lifecycle_manager:
                await self._start_base_lifecycle_manager()
            
            self._running = True
            self._startup_time = time.time() - start_time
            
            # 记录启动指标
            self.metrics_service.increment_counter("app_startup_count")
            self.metrics_service.record_histogram("app_startup_time_seconds", self._startup_time)
            
            logger.info(f"应用启动完成，耗时: {self._startup_time:.2f}秒")
            
        except Exception as e:
            logger.error(f"应用启动失败: {e}")
            self.metrics_service.increment_counter("app_startup_failures")
            raise
    
    async def stop_application(self) -> None:
        """
        停止应用
        
        优雅关闭所有服务和资源清理。
        """
        if not self._running:
            logger.warning("应用未在运行")
            return
        
        try:
            logger.info("开始停止应用...")
            
            # 1. 停止基础生命周期管理器
            if self.base_lifecycle_manager:
                await self._stop_base_lifecycle_manager()
            
            # 2. 停止内存监控
            await self._stop_memory_monitoring()
            
            # 3. 清理异步错误恢复服务
            await self._cleanup_async_services()
            
            self._running = False
            
            # 记录停止指标
            self.metrics_service.increment_counter("app_shutdown_count")
            
            logger.info("应用停止完成")
            
        except Exception as e:
            logger.error(f"应用停止失败: {e}")
            self.metrics_service.increment_counter("app_shutdown_failures")
            raise