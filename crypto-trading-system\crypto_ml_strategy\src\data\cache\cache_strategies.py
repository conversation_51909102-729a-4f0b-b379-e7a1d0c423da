#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
缓存策略实现组件

实现各种缓存淘汰策略和智能预加载机制。
包括LRU、LFU、TTL、自适应策略等。
"""

import logging
import time
import heapq
from typing import List, Dict, Optional, Any, Tuple
from datetime import datetime, timedelta
from collections import OrderedDict, defaultdict
import threading
import numpy as np

from .cache_core import CacheEntry, CacheEvictionPolicy, CacheStrategy


class LRUEvictionPolicy:
    """LRU（最近最少使用）淘汰策略"""
    
    def __init__(self):
        self.logger = logging.getLogger('cryptoMlStrategy.data.cache_strategies')
    
    def should_evict(self, entry: CacheEntry, cache_size: int, max_size: int) -> bool:
        """判断是否应该淘汰条目"""
        return cache_size >= max_size
    
    def select_victims(self, entries: List[CacheEntry], count: int) -> List[CacheEntry]:
        """选择要淘汰的条目（最近最少使用）"""
        try:
            # 按最后访问时间排序，最旧的在前
            sorted_entries = sorted(entries, key=lambda x: x.last_accessed)
            return sorted_entries[:count]
        except Exception as e:
            self.logger.error(f"LRU淘汰选择失败: {e}")
            return []


class LFUEvictionPolicy:
    """LFU（最少使用频率）淘汰策略"""
    
    def __init__(self):
        self.logger = logging.getLogger('cryptoMlStrategy.data.cache_strategies')
    
    def should_evict(self, entry: CacheEntry, cache_size: int, max_size: int) -> bool:
        """判断是否应该淘汰条目"""
        return cache_size >= max_size
    
    def select_victims(self, entries: List[CacheEntry], count: int) -> List[CacheEntry]:
        """选择要淘汰的条目（最少使用频率）"""
        try:
            # 按访问次数排序，最少的在前
            sorted_entries = sorted(entries, key=lambda x: x.access_count)
            return sorted_entries[:count]
        except Exception as e:
            self.logger.error(f"LFU淘汰选择失败: {e}")
            return []


class TTLEvictionPolicy:
    """TTL（生存时间）淘汰策略"""
    
    def __init__(self):
        self.logger = logging.getLogger('cryptoMlStrategy.data.cache_strategies')
    
    def should_evict(self, entry: CacheEntry, cache_size: int, max_size: int) -> bool:
        """判断是否应该淘汰条目"""
        return entry.is_expired() or cache_size >= max_size
    
    def select_victims(self, entries: List[CacheEntry], count: int) -> List[CacheEntry]:
        """选择要淘汰的条目（优先过期的，然后按剩余TTL）"""
        try:
            # 分离过期和未过期的条目
            expired = [entry for entry in entries if entry.is_expired()]
            not_expired = [entry for entry in entries if not entry.is_expired()]
            
            victims = []
            
            # 首先选择所有过期的条目
            victims.extend(expired[:count])
            remaining = count - len(victims)
            
            # 如果还需要更多，按剩余TTL排序
            if remaining > 0 and not_expired:
                # 计算剩余TTL并排序
                ttl_entries = []
                for entry in not_expired:
                    if entry.ttl_seconds:
                        elapsed = entry.age_seconds()
                        remaining_ttl = entry.ttl_seconds - elapsed
                        ttl_entries.append((remaining_ttl, entry))
                
                # 按剩余TTL排序，最少的在前
                ttl_entries.sort(key=lambda x: x[0])
                victims.extend([entry for _, entry in ttl_entries[:remaining]])
            
            return victims
            
        except Exception as e:
            self.logger.error(f"TTL淘汰选择失败: {e}")
            return []


class AdaptiveEvictionPolicy:
    """自适应淘汰策略"""
    
    def __init__(self):
        self.logger = logging.getLogger('cryptoMlStrategy.data.cache_strategies')
        self._access_patterns = defaultdict(list)  # 访问模式统计
        self._strategy_weights = {
            'lru': 0.4,
            'lfu': 0.3,
            'ttl': 0.3
        }
        self._lock = threading.RLock()
    
    def should_evict(self, entry: CacheEntry, cache_size: int, max_size: int) -> bool:
        """判断是否应该淘汰条目"""
        return entry.is_expired() or cache_size >= max_size
    
    def select_victims(self, entries: List[CacheEntry], count: int) -> List[CacheEntry]:
        """选择要淘汰的条目（自适应策略）"""
        try:
            if not entries:
                return []
            
            # 更新访问模式
            self._update_access_patterns(entries)
            
            # 动态调整策略权重
            self._adjust_strategy_weights()
            
            # 计算每个条目的淘汰分数
            scored_entries = []
            for entry in entries:
                score = self._calculate_eviction_score(entry)
                scored_entries.append((score, entry))
            
            # 按分数排序，分数高的优先淘汰
            scored_entries.sort(key=lambda x: x[0], reverse=True)
            
            return [entry for _, entry in scored_entries[:count]]
            
        except Exception as e:
            self.logger.error(f"自适应淘汰选择失败: {e}")
            return []
    
    def _update_access_patterns(self, entries: List[CacheEntry]):
        """更新访问模式"""
        try:
            with self._lock:
                current_time = time.time()
                
                for entry in entries:
                    key_pattern = f"{entry.key.symbol}_{entry.key.timeframe}_{entry.key.data_type}"
                    
                    # 记录访问模式
                    self._access_patterns[key_pattern].append({
                        'timestamp': current_time,
                        'access_count': entry.access_count,
                        'age': entry.age_seconds()
                    })
                    
                    # 保持最近100次记录
                    if len(self._access_patterns[key_pattern]) > 100:
                        self._access_patterns[key_pattern] = self._access_patterns[key_pattern][-100:]
                        
        except Exception as e:
            self.logger.error(f"更新访问模式失败: {e}")
    
    def _adjust_strategy_weights(self):
        """动态调整策略权重"""
        try:
            with self._lock:
                # 分析访问模式，调整权重
                total_patterns = len(self._access_patterns)
                if total_patterns == 0:
                    return
                
                # 计算访问频率分布
                high_frequency_count = 0
                temporal_locality_count = 0
                
                for pattern_data in self._access_patterns.values():
                    if len(pattern_data) < 5:
                        continue
                    
                    recent_accesses = [p for p in pattern_data if time.time() - p['timestamp'] < 3600]  # 1小时内
                    
                    # 高频访问模式
                    if len(recent_accesses) > 10:
                        high_frequency_count += 1
                    
                    # 时间局部性模式
                    if len(recent_accesses) > 0:
                        access_times = [p['timestamp'] for p in recent_accesses]
                        if max(access_times) - min(access_times) < 1800:  # 30分钟内集中访问
                            temporal_locality_count += 1
                
                # 调整权重
                if high_frequency_count > total_patterns * 0.3:
                    # 高频访问模式，增加LFU权重
                    self._strategy_weights['lfu'] = 0.5
                    self._strategy_weights['lru'] = 0.3
                    self._strategy_weights['ttl'] = 0.2
                elif temporal_locality_count > total_patterns * 0.3:
                    # 时间局部性强，增加LRU权重
                    self._strategy_weights['lru'] = 0.5
                    self._strategy_weights['lfu'] = 0.2
                    self._strategy_weights['ttl'] = 0.3
                else:
                    # 平衡策略
                    self._strategy_weights['lru'] = 0.4
                    self._strategy_weights['lfu'] = 0.3
                    self._strategy_weights['ttl'] = 0.3
                    
        except Exception as e:
            self.logger.error(f"调整策略权重失败: {e}")
    
    def _calculate_eviction_score(self, entry: CacheEntry) -> float:
        """计算淘汰分数（分数越高越应该被淘汰）"""
        try:
            score = 0.0
            
            # LRU分数（最后访问时间越久，分数越高）
            lru_score = entry.age_seconds() / 3600.0  # 标准化到小时
            score += lru_score * self._strategy_weights['lru']
            
            # LFU分数（访问次数越少，分数越高）
            max_access_count = 100  # 假设最大访问次数
            lfu_score = 1.0 - min(entry.access_count / max_access_count, 1.0)
            score += lfu_score * self._strategy_weights['lfu']
            
            # TTL分数（过期或接近过期，分数越高）
            if entry.ttl_seconds:
                elapsed_ratio = entry.age_seconds() / entry.ttl_seconds
                ttl_score = min(elapsed_ratio, 2.0)  # 过期的条目分数为2.0
                score += ttl_score * self._strategy_weights['ttl']
            
            return score
            
        except Exception as e:
            self.logger.error(f"计算淘汰分数失败: {e}")
            return 1.0  # 默认分数


class IntelligentPreloader:
    """智能预加载器"""
    
    def __init__(self, cache_manager):
        self.logger = logging.getLogger('cryptoMlStrategy.data.cache_strategies')
        self.cache_manager = cache_manager
        
        # 预加载统计
        self._access_patterns = defaultdict(list)
        self._prediction_accuracy = defaultdict(float)
        self._preload_queue = []
        
        # 配置
        self.min_access_count = 5  # 最小访问次数
        self.prediction_window_hours = 24  # 预测窗口
        self.preload_threshold = 0.7  # 预加载阈值
        
        self._lock = threading.RLock()
        self._active = True
        
        # 启动预加载线程
        self._start_preloader()
    
    def record_access(self, key_str: str, hit: bool):
        """记录访问模式"""
        try:
            with self._lock:
                current_time = time.time()
                
                self._access_patterns[key_str].append({
                    'timestamp': current_time,
                    'hit': hit
                })
                
                # 保持最近1000次记录
                if len(self._access_patterns[key_str]) > 1000:
                    self._access_patterns[key_str] = self._access_patterns[key_str][-1000:]
                    
        except Exception as e:
            self.logger.error(f"记录访问模式失败: {e}")
    
    def predict_next_accesses(self) -> List[Tuple[str, float]]:
        """预测下一次访问"""
        try:
            predictions = []
            current_time = time.time()
            
            with self._lock:
                for key_str, accesses in self._access_patterns.items():
                    if len(accesses) < self.min_access_count:
                        continue
                    
                    # 分析访问模式
                    recent_accesses = [
                        a for a in accesses 
                        if current_time - a['timestamp'] < self.prediction_window_hours * 3600
                    ]
                    
                    if len(recent_accesses) < 3:
                        continue
                    
                    # 计算访问概率
                    probability = self._calculate_access_probability(recent_accesses, current_time)
                    
                    if probability > self.preload_threshold:
                        predictions.append((key_str, probability))
            
            # 按概率排序
            predictions.sort(key=lambda x: x[1], reverse=True)
            return predictions[:10]  # 返回前10个预测
            
        except Exception as e:
            self.logger.error(f"预测访问失败: {e}")
            return []
    
    def _calculate_access_probability(self, accesses: List[Dict], current_time: float) -> float:
        """计算访问概率"""
        try:
            if not accesses:
                return 0.0
            
            # 时间间隔分析
            timestamps = [a['timestamp'] for a in accesses]
            intervals = [timestamps[i] - timestamps[i-1] for i in range(1, len(timestamps))]
            
            if not intervals:
                return 0.0
            
            # 计算平均间隔和标准差
            avg_interval = np.mean(intervals)
            std_interval = np.std(intervals)
            
            # 预测下次访问时间
            last_access = timestamps[-1]
            time_since_last = current_time - last_access
            
            # 基于正态分布的概率计算
            if std_interval > 0:
                z_score = abs(time_since_last - avg_interval) / std_interval
                probability = max(0.0, 1.0 - z_score / 3.0)  # 3-sigma规则
            else:
                probability = 1.0 if time_since_last >= avg_interval else 0.5
            
            # 考虑访问频率
            hit_rate = sum(1 for a in accesses if a['hit']) / len(accesses)
            probability *= hit_rate
            
            return min(probability, 1.0)
            
        except Exception as e:
            self.logger.error(f"计算访问概率失败: {e}")
            return 0.0
    
    def _start_preloader(self):
        """启动预加载线程"""
        def preload_worker():
            while self._active:
                try:
                    # 预测需要预加载的数据
                    predictions = self.predict_next_accesses()
                    
                    for key_str, probability in predictions:
                        if not self._active:
                            break
                        
                        try:
                            # 解析缓存键
                            from .cache_core import CacheKey
                            cache_key = CacheKey.from_string(key_str)
                            
                            # 检查是否已在缓存中
                            if self.cache_manager.get(cache_key) is None:
                                # 触发预加载（这里需要与数据源集成）
                                self._trigger_preload(cache_key, probability)
                                
                        except Exception as e:
                            self.logger.error(f"预加载处理失败: {e}")
                    
                    time.sleep(300)  # 每5分钟执行一次预测
                    
                except Exception as e:
                    self.logger.error(f"预加载线程错误: {e}")
                    time.sleep(600)  # 出错后10分钟重试
        
        preload_thread = threading.Thread(target=preload_worker, daemon=True)
        preload_thread.start()
    
    def _trigger_preload(self, cache_key, probability: float):
        """触发预加载"""
        try:
            # 这里应该与数据源集成，实际加载数据
            # 目前只是记录预加载请求
            self.logger.info(f"预加载请求: {cache_key.to_string()}, 概率: {probability:.3f}")
            
            # 可以在这里调用数据源的加载方法
            # 例如：data = data_source.load(cache_key)
            # self.cache_manager.put(cache_key, data)
            
        except Exception as e:
            self.logger.error(f"触发预加载失败: {e}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取预加载统计"""
        with self._lock:
            return {
                'tracked_patterns': len(self._access_patterns),
                'total_accesses': sum(len(accesses) for accesses in self._access_patterns.values()),
                'prediction_accuracy': dict(self._prediction_accuracy),
                'config': {
                    'min_access_count': self.min_access_count,
                    'prediction_window_hours': self.prediction_window_hours,
                    'preload_threshold': self.preload_threshold
                }
            }
    
    def shutdown(self):
        """关闭预加载器"""
        self._active = False
        self.logger.info("智能预加载器已关闭")


class CacheStrategyFactory:
    """缓存策略工厂"""
    
    @staticmethod
    def create_eviction_policy(strategy: CacheStrategy) -> CacheEvictionPolicy:
        """创建淘汰策略"""
        if strategy == CacheStrategy.LRU:
            return LRUEvictionPolicy()
        elif strategy == CacheStrategy.LFU:
            return LFUEvictionPolicy()
        elif strategy == CacheStrategy.TTL:
            return TTLEvictionPolicy()
        elif strategy == CacheStrategy.ADAPTIVE:
            return AdaptiveEvictionPolicy()
        else:
            raise ValueError(f"不支持的缓存策略: {strategy}")
    
    @staticmethod
    def create_preloader(cache_manager) -> IntelligentPreloader:
        """创建智能预加载器"""
        return IntelligentPreloader(cache_manager)


class CachePerformanceOptimizer:
    """缓存性能优化器"""
    
    def __init__(self, cache_manager):
        self.cache_manager = cache_manager
        self.logger = logging.getLogger('cryptoMlStrategy.data.cache_strategies')
        
        # 性能监控
        self._performance_history = []
        self._optimization_suggestions = []
        
    def analyze_performance(self) -> Dict[str, Any]:
        """分析缓存性能"""
        try:
            stats = self.cache_manager.get_statistics()
            cache_stats = stats['cache_stats']
            
            analysis = {
                'performance_score': 0.0,
                'bottlenecks': [],
                'suggestions': [],
                'metrics': cache_stats
            }
            
            # 计算性能分数
            hit_ratio = cache_stats['hit_ratio']
            avg_access_time = cache_stats['avg_access_time_ms']
            memory_usage_ratio = cache_stats['memory_usage_mb'] / self.cache_manager.config.max_memory_mb
            
            # 命中率分数 (40%)
            hit_score = hit_ratio * 40
            
            # 访问时间分数 (30%)
            time_score = max(0, 30 - avg_access_time) if avg_access_time < 30 else 0
            
            # 内存使用分数 (30%)
            memory_score = max(0, 30 * (1 - memory_usage_ratio)) if memory_usage_ratio < 1 else 0
            
            analysis['performance_score'] = hit_score + time_score + memory_score
            
            # 识别瓶颈
            if hit_ratio < 0.8:
                analysis['bottlenecks'].append('缓存命中率低')
                analysis['suggestions'].append('考虑增加缓存大小或调整淘汰策略')
            
            if avg_access_time > 5.0:
                analysis['bottlenecks'].append('访问延迟高')
                analysis['suggestions'].append('优化数据结构或启用并发访问')
            
            if memory_usage_ratio > 0.9:
                analysis['bottlenecks'].append('内存使用率高')
                analysis['suggestions'].append('增加内存限制或更积极的淘汰策略')
            
            return analysis
            
        except Exception as e:
            self.logger.error(f"性能分析失败: {e}")
            return {}
    
    def optimize_configuration(self) -> Dict[str, Any]:
        """优化缓存配置"""
        try:
            analysis = self.analyze_performance()
            optimizations = {}
            
            # 基于分析结果提供优化建议
            if analysis['performance_score'] < 60:
                if '缓存命中率低' in analysis['bottlenecks']:
                    optimizations['increase_cache_size'] = True
                    optimizations['adjust_ttl'] = 'increase'
                
                if '访问延迟高' in analysis['bottlenecks']:
                    optimizations['enable_preloading'] = True
                    optimizations['optimize_data_structure'] = True
                
                if '内存使用率高' in analysis['bottlenecks']:
                    optimizations['aggressive_eviction'] = True
                    optimizations['enable_compression'] = True
            
            return optimizations
            
        except Exception as e:
            self.logger.error(f"配置优化失败: {e}")
            return {}