#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
DeepSeek蒸馏模块使用示例

展示如何使用知识蒸馏技术训练和压缩机器学习模型。
"""

import numpy as np
import pandas as pd
import logging
from typing import Dict, Any

from .distillation_manager import DistillationManager
from .deepseek_distiller import DeepSeekDistiller
from .model_compressor import ModelCompressor


def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )


def generate_sample_data(n_samples: int = 1000, n_features: int = 50) -> tuple:
    """
    生成示例数据
    
    Args:
        n_samples: 样本数量
        n_features: 特征数量
        
    Returns:
        特征和标签的元组
    """
    np.random.seed(42)
    
    # 生成特征
    X = np.random.randn(n_samples, n_features)
    
    # 生成复杂的非线性目标
    # 模拟金融时间序列的复杂模式
    weights = np.random.randn(n_features) * 0.1
    y = (
        np.dot(X, weights) +
        0.1 * np.sin(X[:, 0] * 2) +
        0.05 * np.cos(X[:, 1] * 3) +
        0.02 * X[:, 2] ** 2 +
        0.01 * np.random.randn(n_samples)
    )
    
    return X, y


def example_basic_distillation():
    """基础蒸馏示例"""
    print("\n=== 基础知识蒸馏示例 ===")
    
    # 生成数据
    X, y = generate_sample_data(1000, 30)
    print(f"数据形状: X={X.shape}, y={y.shape}")
    
    # 创建蒸馏器
    distiller = DeepSeekDistiller(config={
        'teacher_hidden_dims': [256, 128, 64],
        'student_hidden_dims': [64, 32],
        'temperature': 3.0,
        'epochs': 50,
        'batch_size': 32
    })
    
    # 创建模型
    distiller.create_models(input_dim=X.shape[1], output_dim=1)
    
    # 训练教师模型
    print("训练教师模型...")
    teacher_results = distiller.train_teacher(X, y)
    print(f"教师模型训练完成: {teacher_results}")
    
    # 知识蒸馏
    print("开始知识蒸馏...")
    distillation_results = distiller.distill_knowledge(X, y)
    print(f"知识蒸馏完成: {distillation_results}")
    
    # 比较预测结果
    X_test = X[:100]  # 使用前100个样本测试
    teacher_pred = distiller.predict(X_test, use_teacher=True)
    student_pred = distiller.predict(X_test, use_teacher=False)
    
    print(f"教师模型预测范围: [{teacher_pred.min():.4f}, {teacher_pred.max():.4f}]")
    print(f"学生模型预测范围: [{student_pred.min():.4f}, {student_pred.max():.4f}]")
    
    # 计算预测差异
    pred_diff = np.mean(np.abs(teacher_pred - student_pred))
    print(f"教师-学生预测平均差异: {pred_diff:.6f}")


def example_model_compression():
    """模型压缩示例"""
    print("\n=== 模型压缩示例 ===")
    
    # 生成数据
    X, y = generate_sample_data(500, 20)
    
    # 创建蒸馏器并训练
    distiller = DeepSeekDistiller(config={
        'teacher_hidden_dims': [128, 64],
        'student_hidden_dims': [64, 32],
        'epochs': 30
    })
    
    distiller.create_models(input_dim=X.shape[1], output_dim=1)
    distiller.train_teacher(X, y)
    distiller.distill_knowledge(X, y)
    
    # 创建压缩器
    compressor = ModelCompressor(config={
        'quantization': {'enabled': True, 'bits': 8},
        'pruning': {'enabled': True, 'sparsity': 0.4},
        'weight_sharing': {'enabled': True, 'clusters': 8}
    })
    
    # 准备验证数据
    import torch
    from sklearn.model_selection import train_test_split
    
    X_scaled = distiller.scaler.transform(X)
    _, X_val, _, y_val = train_test_split(X_scaled, y, test_size=0.2, random_state=42)
    
    X_val_tensor = torch.FloatTensor(X_val)
    y_val_tensor = torch.FloatTensor(y_val)
    
    # 压缩模型
    print("压缩学生模型...")
    compressed_model = compressor.compress_model(
        distiller.student_model,
        validation_data=(X_val_tensor, y_val_tensor)
    )
    
    # 显示压缩信息
    compression_info = compressor.get_compression_info()
    print(f"压缩统计: {compression_info['compression_stats']}")
    
    # 基准测试推理速度
    benchmark_results = compressor.benchmark_inference_speed((1, X.shape[1]), num_runs=50)
    print(f"推理速度基准测试: {benchmark_results}")


def example_full_distillation_pipeline():
    """完整蒸馏流程示例"""
    print("\n=== 完整蒸馏流程示例 ===")
    
    # 生成数据
    X, y = generate_sample_data(1500, 40)
    feature_names = [f"feature_{i}" for i in range(X.shape[1])]
    
    # 创建蒸馏管理器
    manager = DistillationManager(config={
        'distillation': {
            'teacher_hidden_dims': [256, 128, 64, 32],
            'student_hidden_dims': [64, 32, 16],
            'temperature': 4.0,
            'alpha': 0.8,
            'epochs': 60,
            'batch_size': 64
        },
        'compression': {
            'enabled': True,
            'quantization': {'enabled': True, 'bits': 8},
            'pruning': {'enabled': True, 'sparsity': 0.3}
        },
        'output': {
            'save_teacher': True,
            'save_student': True,
            'save_compressed': True,
            'save_dir': 'models/example_distilled'
        }
    })
    
    # 运行完整流程
    print("运行完整蒸馏流程...")
    results = manager.run_full_distillation(X, y, feature_names)
    
    if results['success']:
        print(f"蒸馏流程成功完成，耗时: {results['duration_seconds']:.2f}秒")
        print(f"模型信息: {results['model_info']}")
        print(f"性能指标: {results['performance_metrics']}")
        
        # 测试不同模型的预测
        X_test = X[:50]
        
        teacher_pred = manager.predict(X_test, model_type='teacher')
        student_pred = manager.predict(X_test, model_type='student')
        compressed_pred = manager.predict(X_test, model_type='compressed')
        
        print(f"\n预测结果比较:")
        print(f"教师模型预测均值: {teacher_pred.mean():.6f}")
        print(f"学生模型预测均值: {student_pred.mean():.6f}")
        print(f"压缩模型预测均值: {compressed_pred.mean():.6f}")
        
        # 计算预测一致性
        teacher_student_corr = np.corrcoef(teacher_pred, student_pred)[0, 1]
        student_compressed_corr = np.corrcoef(student_pred, compressed_pred)[0, 1]
        
        print(f"教师-学生预测相关性: {teacher_student_corr:.4f}")
        print(f"学生-压缩预测相关性: {student_compressed_corr:.4f}")
        
    else:
        print(f"蒸馏流程失败: {results['error']}")


def example_load_and_use_model():
    """加载和使用模型示例"""
    print("\n=== 加载和使用模型示例 ===")
    
    try:
        # 创建管理器
        manager = DistillationManager()
        
        # 尝试加载之前保存的模型
        model_dir = "models/example_distilled"
        
        # 这里需要实际的模型目录，示例中可能不存在
        print(f"尝试从 {model_dir} 加载模型...")
        
        # 生成测试数据
        X_test, _ = generate_sample_data(100, 40)
        
        # 如果模型存在，加载并预测
        # manager.load_distilled_models(model_dir, input_dim=40)
        # predictions = manager.predict(X_test, model_type='compressed')
        # print(f"加载模型预测完成，预测形状: {predictions.shape}")
        
        print("注意: 需要先运行完整蒸馏流程以生成模型文件")
        
    except Exception as e:
        print(f"加载模型失败: {e}")


def main():
    """主函数"""
    setup_logging()
    
    print("DeepSeek知识蒸馏模块使用示例")
    print("=" * 50)
    
    try:
        # 运行各种示例
        example_basic_distillation()
        example_model_compression()
        example_full_distillation_pipeline()
        example_load_and_use_model()
        
        print("\n所有示例运行完成!")
        
    except Exception as e:
        print(f"示例运行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()