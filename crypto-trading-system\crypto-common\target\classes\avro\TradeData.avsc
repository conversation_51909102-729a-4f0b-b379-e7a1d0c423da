{"namespace": "com.crypto.trading.common.avro", "type": "record", "name": "TradeData", "doc": "交易数据的Avro模式定义", "fields": [{"name": "symbol", "type": "string", "doc": "交易对符号，如BTCUSDT"}, {"name": "id", "type": "long", "doc": "交易ID"}, {"name": "price", "type": "double", "doc": "成交价格"}, {"name": "quantity", "type": "double", "doc": "成交数量"}, {"name": "tradeTime", "type": "long", "doc": "成交时间（毫秒时间戳）"}, {"name": "isBuyerMaker", "type": "boolean", "doc": "是否由买方发起订单"}, {"name": "isBestMatch", "type": "boolean", "doc": "是否是最佳匹配价格"}, {"name": "timestamp", "type": "long", "doc": "消息生成时间（毫秒时间戳）"}, {"name": "source", "type": "string", "doc": "数据来源", "default": "binance"}]}