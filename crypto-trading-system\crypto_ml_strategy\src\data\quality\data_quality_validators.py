#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据质量验证器组件

实现各种数据质量验证器，包括缺失值检测、异常值识别、数据类型验证等。
每个验证器专注于特定类型的质量问题检测。
"""

import logging
import numpy as np
import pandas as pd
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
from scipy import stats
from sklearn.ensemble import IsolationForest

from .data_quality_core import QualityValidator, QualityIssue, QualityConfig, ValidationResult


class MissingValueValidator:
    """缺失值验证器"""
    
    def get_validator_name(self) -> str:
        return "missing_value_validator"
    
    def validate(self, data: pd.DataFrame, config: QualityConfig) -> List[QualityIssue]:
        """检测缺失值问题"""
        issues = []
        
        try:
            # 检查每列的缺失值
            for column in data.columns:
                missing_count = data[column].isnull().sum()
                missing_ratio = missing_count / len(data)
                
                if missing_ratio > config.max_missing_ratio:
                    missing_indices = data[data[column].isnull()].index.tolist()
                    
                    issues.append(QualityIssue(
                        issue_type="missing_values",
                        severity=ValidationResult.FAIL if missing_ratio > 0.5 else ValidationResult.WARNING,
                        description=f"列 {column} 缺失值比例过高: {missing_ratio:.2%}",
                        affected_columns=[column],
                        affected_rows=missing_indices,
                        suggested_fix=f"使用 {config.repair_missing_method} 方法填充缺失值"
                    ))
            
            # 检查完全空的行
            empty_rows = data.isnull().all(axis=1)
            if empty_rows.any():
                empty_indices = data[empty_rows].index.tolist()
                issues.append(QualityIssue(
                    issue_type="empty_rows",
                    severity=ValidationResult.FAIL,
                    description=f"发现 {len(empty_indices)} 个完全空的行",
                    affected_columns=list(data.columns),
                    affected_rows=empty_indices,
                    suggested_fix="删除空行或填充数据"
                ))
            
        except Exception as e:
            issues.append(QualityIssue(
                issue_type="missing_value_check_error",
                severity=ValidationResult.ERROR,
                description=f"缺失值检查失败: {str(e)}",
                affected_columns=[],
                affected_rows=[]
            ))
        
        return issues


class OutlierValidator:
    """异常值验证器"""
    
    def get_validator_name(self) -> str:
        return "outlier_validator"
    
    def validate(self, data: pd.DataFrame, config: QualityConfig) -> List[QualityIssue]:
        """检测异常值"""
        issues = []
        
        try:
            numeric_columns = data.select_dtypes(include=[np.number]).columns
            
            for column in numeric_columns:
                if column in data.columns:
                    outliers = self._detect_outliers(data[column], config)
                    
                    if len(outliers) > 0:
                        outlier_ratio = len(outliers) / len(data)
                        
                        if outlier_ratio > config.max_outlier_ratio:
                            severity = ValidationResult.FAIL if outlier_ratio > 0.1 else ValidationResult.WARNING
                        else:
                            severity = ValidationResult.WARNING
                        
                        issues.append(QualityIssue(
                            issue_type="outlier",
                            severity=severity,
                            description=f"列 {column} 发现 {len(outliers)} 个异常值 ({outlier_ratio:.2%})",
                            affected_columns=[column],
                            affected_rows=outliers,
                            suggested_fix=f"使用 {config.repair_outlier_method} 方法处理异常值"
                        ))
            
        except Exception as e:
            issues.append(QualityIssue(
                issue_type="outlier_check_error",
                severity=ValidationResult.ERROR,
                description=f"异常值检查失败: {str(e)}",
                affected_columns=[],
                affected_rows=[]
            ))
        
        return issues
    
    def _detect_outliers(self, series: pd.Series, config: QualityConfig) -> List[int]:
        """检测异常值"""
        try:
            if config.outlier_detection_method == 'zscore':
                return self._zscore_outliers(series, config.outlier_threshold)
            elif config.outlier_detection_method == 'iqr':
                return self._iqr_outliers(series, config.outlier_threshold)
            elif config.outlier_detection_method == 'isolation_forest':
                return self._isolation_forest_outliers(series)
            else:
                return self._iqr_outliers(series, config.outlier_threshold)
                
        except Exception:
            return []
    
    def _zscore_outliers(self, series: pd.Series, threshold: float) -> List[int]:
        """Z-score方法检测异常值"""
        clean_series = series.dropna()
        if len(clean_series) < 3:
            return []
        
        z_scores = np.abs(stats.zscore(clean_series))
        outlier_mask = z_scores > threshold
        return clean_series[outlier_mask].index.tolist()
    
    def _iqr_outliers(self, series: pd.Series, multiplier: float) -> List[int]:
        """IQR方法检测异常值"""
        clean_series = series.dropna()
        if len(clean_series) < 4:
            return []
        
        Q1 = clean_series.quantile(0.25)
        Q3 = clean_series.quantile(0.75)
        IQR = Q3 - Q1
        
        lower_bound = Q1 - multiplier * IQR
        upper_bound = Q3 + multiplier * IQR
        
        outlier_mask = (clean_series < lower_bound) | (clean_series > upper_bound)
        return clean_series[outlier_mask].index.tolist()
    
    def _isolation_forest_outliers(self, series: pd.Series) -> List[int]:
        """孤立森林方法检测异常值"""
        clean_series = series.dropna()
        if len(clean_series) < 10:
            return []
        
        try:
            iso_forest = IsolationForest(contamination=0.1, random_state=42)
            outlier_labels = iso_forest.fit_predict(clean_series.values.reshape(-1, 1))
            outlier_mask = outlier_labels == -1
            return clean_series[outlier_mask].index.tolist()
        except Exception:
            return []


class RangeValidator:
    """数据范围验证器"""
    
    def get_validator_name(self) -> str:
        return "range_validator"
    
    def validate(self, data: pd.DataFrame, config: QualityConfig) -> List[QualityIssue]:
        """验证数据范围"""
        issues = []
        
        try:
            # 价格相关列的范围检查
            price_columns = [col for col in data.columns 
                           if any(keyword in col.lower() for keyword in ['price', 'open', 'high', 'low', 'close'])]
            
            for column in price_columns:
                if column in data.columns:
                    invalid_indices = self._check_price_range(data[column], config)
                    if invalid_indices:
                        issues.append(QualityIssue(
                            issue_type="price_range_violation",
                            severity=ValidationResult.FAIL,
                            description=f"列 {column} 存在超出合理范围的价格数据",
                            affected_columns=[column],
                            affected_rows=invalid_indices,
                            suggested_fix="检查数据源或使用范围限制"
                        ))
            
            # 成交量相关列的范围检查
            volume_columns = [col for col in data.columns 
                            if 'volume' in col.lower()]
            
            for column in volume_columns:
                if column in data.columns:
                    invalid_indices = self._check_volume_range(data[column], config)
                    if invalid_indices:
                        issues.append(QualityIssue(
                            issue_type="volume_range_violation",
                            severity=ValidationResult.WARNING,
                            description=f"列 {column} 存在异常的成交量数据",
                            affected_columns=[column],
                            affected_rows=invalid_indices,
                            suggested_fix="检查成交量数据的合理性"
                        ))
            
            # OHLC逻辑检查
            if all(col in data.columns for col in ['open', 'high', 'low', 'close']):
                logic_issues = self._check_ohlc_logic(data)
                issues.extend(logic_issues)
            
        except Exception as e:
            issues.append(QualityIssue(
                issue_type="range_check_error",
                severity=ValidationResult.ERROR,
                description=f"范围检查失败: {str(e)}",
                affected_columns=[],
                affected_rows=[]
            ))
        
        return issues
    
    def _check_price_range(self, series: pd.Series, config: QualityConfig) -> List[int]:
        """检查价格范围"""
        invalid_mask = (series < config.price_min) | (series > config.price_max) | (series <= 0)
        return series[invalid_mask].index.tolist()
    
    def _check_volume_range(self, series: pd.Series, config: QualityConfig) -> List[int]:
        """检查成交量范围"""
        invalid_mask = (series < config.volume_min) | (series > config.volume_max) | (series < 0)
        return series[invalid_mask].index.tolist()
    
    def _check_ohlc_logic(self, data: pd.DataFrame) -> List[QualityIssue]:
        """检查OHLC逻辑"""
        issues = []
        
        try:
            # 检查 high >= max(open, close) 和 low <= min(open, close)
            high_invalid = data['high'] < data[['open', 'close']].max(axis=1)
            low_invalid = data['low'] > data[['open', 'close']].min(axis=1)
            
            if high_invalid.any():
                invalid_indices = data[high_invalid].index.tolist()
                issues.append(QualityIssue(
                    issue_type="ohlc_logic_violation",
                    severity=ValidationResult.FAIL,
                    description=f"发现 {len(invalid_indices)} 个高价低于开盘价或收盘价的记录",
                    affected_columns=['high', 'open', 'close'],
                    affected_rows=invalid_indices,
                    suggested_fix="修正OHLC数据的逻辑关系"
                ))
            
            if low_invalid.any():
                invalid_indices = data[low_invalid].index.tolist()
                issues.append(QualityIssue(
                    issue_type="ohlc_logic_violation",
                    severity=ValidationResult.FAIL,
                    description=f"发现 {len(invalid_indices)} 个低价高于开盘价或收盘价的记录",
                    affected_columns=['low', 'open', 'close'],
                    affected_rows=invalid_indices,
                    suggested_fix="修正OHLC数据的逻辑关系"
                ))
            
        except Exception as e:
            issues.append(QualityIssue(
                issue_type="ohlc_logic_check_error",
                severity=ValidationResult.ERROR,
                description=f"OHLC逻辑检查失败: {str(e)}",
                affected_columns=[],
                affected_rows=[]
            ))
        
        return issues


class TimeSeriesValidator:
    """时间序列验证器"""
    
    def get_validator_name(self) -> str:
        return "timeseries_validator"
    
    def validate(self, data: pd.DataFrame, config: QualityConfig) -> List[QualityIssue]:
        """验证时间序列数据"""
        issues = []
        
        try:
            # 检查时间列
            time_columns = [col for col in data.columns 
                          if any(keyword in col.lower() for keyword in ['time', 'date', 'timestamp'])]
            
            if not time_columns and hasattr(data.index, 'to_pydatetime'):
                # 使用索引作为时间列
                time_issues = self._validate_time_index(data, config)
                issues.extend(time_issues)
            else:
                for column in time_columns:
                    if column in data.columns:
                        time_issues = self._validate_time_column(data[column], config)
                        issues.extend(time_issues)
            
            # 检查时间间隙
            gap_issues = self._check_time_gaps(data, config)
            issues.extend(gap_issues)
            
            # 检查未来数据
            future_issues = self._check_future_data(data, config)
            issues.extend(future_issues)
            
        except Exception as e:
            issues.append(QualityIssue(
                issue_type="timeseries_check_error",
                severity=ValidationResult.ERROR,
                description=f"时间序列检查失败: {str(e)}",
                affected_columns=[],
                affected_rows=[]
            ))
        
        return issues
    
    def _validate_time_index(self, data: pd.DataFrame, config: QualityConfig) -> List[QualityIssue]:
        """验证时间索引"""
        issues = []
        
        try:
            if not data.index.is_monotonic_increasing:
                issues.append(QualityIssue(
                    issue_type="time_order_violation",
                    severity=ValidationResult.WARNING,
                    description="时间索引不是单调递增的",
                    affected_columns=[],
                    affected_rows=[],
                    suggested_fix="对数据按时间排序"
                ))
            
            # 检查重复时间戳
            duplicates = data.index.duplicated()
            if duplicates.any():
                duplicate_indices = data[duplicates].index.tolist()
                issues.append(QualityIssue(
                    issue_type="duplicate_timestamps",
                    severity=ValidationResult.WARNING,
                    description=f"发现 {len(duplicate_indices)} 个重复的时间戳",
                    affected_columns=[],
                    affected_rows=list(range(len(duplicate_indices))),
                    suggested_fix="去除或合并重复时间戳的数据"
                ))
            
        except Exception as e:
            issues.append(QualityIssue(
                issue_type="time_index_check_error",
                severity=ValidationResult.ERROR,
                description=f"时间索引检查失败: {str(e)}",
                affected_columns=[],
                affected_rows=[]
            ))
        
        return issues
    
    def _validate_time_column(self, series: pd.Series, config: QualityConfig) -> List[QualityIssue]:
        """验证时间列"""
        issues = []
        
        try:
            # 尝试转换为时间格式
            try:
                time_series = pd.to_datetime(series)
            except Exception:
                issues.append(QualityIssue(
                    issue_type="time_format_error",
                    severity=ValidationResult.FAIL,
                    description=f"时间列 {series.name} 格式无效",
                    affected_columns=[series.name],
                    affected_rows=[],
                    suggested_fix="检查时间格式或转换方法"
                ))
                return issues
            
            # 检查时间范围合理性
            now = datetime.now()
            too_old = now - timedelta(days=365*10)  # 10年前
            too_new = now + timedelta(seconds=config.future_data_tolerance_seconds)
            
            invalid_times = (time_series < too_old) | (time_series > too_new)
            if invalid_times.any():
                invalid_indices = series[invalid_times].index.tolist()
                issues.append(QualityIssue(
                    issue_type="time_range_violation",
                    severity=ValidationResult.WARNING,
                    description=f"时间列 {series.name} 存在不合理的时间值",
                    affected_columns=[series.name],
                    affected_rows=invalid_indices,
                    suggested_fix="检查时间数据的合理性"
                ))
            
        except Exception as e:
            issues.append(QualityIssue(
                issue_type="time_column_check_error",
                severity=ValidationResult.ERROR,
                description=f"时间列检查失败: {str(e)}",
                affected_columns=[],
                affected_rows=[]
            ))
        
        return issues
    
    def _check_time_gaps(self, data: pd.DataFrame, config: QualityConfig) -> List[QualityIssue]:
        """检查时间间隙"""
        issues = []
        
        try:
            if hasattr(data.index, 'to_pydatetime') and len(data) > 1:
                time_diffs = data.index.to_series().diff().dt.total_seconds()
                large_gaps = time_diffs > config.max_time_gap_seconds
                
                if large_gaps.any():
                    gap_indices = data[large_gaps].index.tolist()
                    max_gap = time_diffs.max()
                    
                    issues.append(QualityIssue(
                        issue_type="time_gap",
                        severity=ValidationResult.WARNING,
                        description=f"发现 {len(gap_indices)} 个大时间间隙，最大间隙: {max_gap:.0f}秒",
                        affected_columns=[],
                        affected_rows=list(range(len(gap_indices))),
                        suggested_fix="检查数据连续性或调整时间间隙阈值"
                    ))
            
        except Exception as e:
            issues.append(QualityIssue(
                issue_type="time_gap_check_error",
                severity=ValidationResult.ERROR,
                description=f"时间间隙检查失败: {str(e)}",
                affected_columns=[],
                affected_rows=[]
            ))
        
        return issues
    
    def _check_future_data(self, data: pd.DataFrame, config: QualityConfig) -> List[QualityIssue]:
        """检查未来数据"""
        issues = []
        
        try:
            now = datetime.now()
            tolerance = timedelta(seconds=config.future_data_tolerance_seconds)
            
            if hasattr(data.index, 'to_pydatetime'):
                future_mask = data.index > (now + tolerance)
                if future_mask.any():
                    future_indices = data[future_mask].index.tolist()
                    issues.append(QualityIssue(
                        issue_type="future_data",
                        severity=ValidationResult.WARNING,
                        description=f"发现 {len(future_indices)} 个未来时间戳的数据",
                        affected_columns=[],
                        affected_rows=list(range(len(future_indices))),
                        suggested_fix="检查系统时间或数据时间戳"
                    ))
            
        except Exception as e:
            issues.append(QualityIssue(
                issue_type="future_data_check_error",
                severity=ValidationResult.ERROR,
                description=f"未来数据检查失败: {str(e)}",
                affected_columns=[],
                affected_rows=[]
            ))
        
        return issues