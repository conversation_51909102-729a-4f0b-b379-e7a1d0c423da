package com.crypto.trading.common.dto.account;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 持仓信息传输对象
 * <p>
 * 用于在系统各层之间传递持仓信息
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class PositionDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 账户ID
     */
    private String accountId;

    /**
     * 交易对（例如：BTCUSDT）
     */
    private String symbol;

    /**
     * 持仓方向
     * <p>
     * LONG: 多头
     * SHORT: 空头
     * </p>
     */
    private String positionSide;

    /**
     * 杠杆倍数
     */
    private Integer leverage;

    /**
     * 持仓数量
     */
    private BigDecimal quantity;

    /**
     * 持仓均价
     */
    private BigDecimal entryPrice;

    /**
     * 标记价格
     */
    private BigDecimal markPrice;

    /**
     * 未实现盈亏
     */
    private BigDecimal unrealizedProfit;

    /**
     * 已实现盈亏
     */
    private BigDecimal realizedProfit;

    /**
     * 初始保证金
     */
    private BigDecimal initialMargin;

    /**
     * 维持保证金
     */
    private BigDecimal maintMargin;

    /**
     * 最大可平仓数量
     */
    private BigDecimal maxQuantity;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 构造函数
     */
    public PositionDTO() {
    }

    /**
     * 构造函数
     *
     * @param accountId        账户ID
     * @param symbol           交易对
     * @param positionSide     持仓方向
     * @param leverage         杠杆倍数
     * @param quantity         持仓数量
     * @param entryPrice       持仓均价
     * @param markPrice        标记价格
     * @param unrealizedProfit 未实现盈亏
     * @param realizedProfit   已实现盈亏
     * @param initialMargin    初始保证金
     * @param maintMargin      维持保证金
     * @param maxQuantity      最大可平仓数量
     * @param updateTime       更新时间
     */
    public PositionDTO(String accountId, String symbol, String positionSide, Integer leverage,
                      BigDecimal quantity, BigDecimal entryPrice, BigDecimal markPrice,
                      BigDecimal unrealizedProfit, BigDecimal realizedProfit,
                      BigDecimal initialMargin, BigDecimal maintMargin,
                      BigDecimal maxQuantity, LocalDateTime updateTime) {
        this.accountId = accountId;
        this.symbol = symbol;
        this.positionSide = positionSide;
        this.leverage = leverage;
        this.quantity = quantity;
        this.entryPrice = entryPrice;
        this.markPrice = markPrice;
        this.unrealizedProfit = unrealizedProfit;
        this.realizedProfit = realizedProfit;
        this.initialMargin = initialMargin;
        this.maintMargin = maintMargin;
        this.maxQuantity = maxQuantity;
        this.updateTime = updateTime;
    }

    /**
     * 获取账户ID
     *
     * @return 账户ID
     */
    public String getAccountId() {
        return accountId;
    }

    /**
     * 设置账户ID
     *
     * @param accountId 账户ID
     */
    public void setAccountId(String accountId) {
        this.accountId = accountId;
    }

    /**
     * 获取交易对
     *
     * @return 交易对
     */
    public String getSymbol() {
        return symbol;
    }

    /**
     * 设置交易对
     *
     * @param symbol 交易对
     */
    public void setSymbol(String symbol) {
        this.symbol = symbol;
    }

    /**
     * 获取持仓方向
     *
     * @return 持仓方向
     */
    public String getPositionSide() {
        return positionSide;
    }

    /**
     * 设置持仓方向
     *
     * @param positionSide 持仓方向
     */
    public void setPositionSide(String positionSide) {
        this.positionSide = positionSide;
    }

    /**
     * 获取杠杆倍数
     *
     * @return 杠杆倍数
     */
    public Integer getLeverage() {
        return leverage;
    }

    /**
     * 设置杠杆倍数
     *
     * @param leverage 杠杆倍数
     */
    public void setLeverage(Integer leverage) {
        this.leverage = leverage;
    }

    /**
     * 获取持仓数量
     *
     * @return 持仓数量
     */
    public BigDecimal getQuantity() {
        return quantity;
    }

    /**
     * 设置持仓数量
     *
     * @param quantity 持仓数量
     */
    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    /**
     * 获取持仓均价
     *
     * @return 持仓均价
     */
    public BigDecimal getEntryPrice() {
        return entryPrice;
    }

    /**
     * 设置持仓均价
     *
     * @param entryPrice 持仓均价
     */
    public void setEntryPrice(BigDecimal entryPrice) {
        this.entryPrice = entryPrice;
    }

    /**
     * 获取标记价格
     *
     * @return 标记价格
     */
    public BigDecimal getMarkPrice() {
        return markPrice;
    }

    /**
     * 设置标记价格
     *
     * @param markPrice 标记价格
     */
    public void setMarkPrice(BigDecimal markPrice) {
        this.markPrice = markPrice;
    }

    /**
     * 获取未实现盈亏
     *
     * @return 未实现盈亏
     */
    public BigDecimal getUnrealizedProfit() {
        return unrealizedProfit;
    }

    /**
     * 设置未实现盈亏
     *
     * @param unrealizedProfit 未实现盈亏
     */
    public void setUnrealizedProfit(BigDecimal unrealizedProfit) {
        this.unrealizedProfit = unrealizedProfit;
    }

    /**
     * 获取已实现盈亏
     *
     * @return 已实现盈亏
     */
    public BigDecimal getRealizedProfit() {
        return realizedProfit;
    }

    /**
     * 设置已实现盈亏
     *
     * @param realizedProfit 已实现盈亏
     */
    public void setRealizedProfit(BigDecimal realizedProfit) {
        this.realizedProfit = realizedProfit;
    }

    /**
     * 获取初始保证金
     *
     * @return 初始保证金
     */
    public BigDecimal getInitialMargin() {
        return initialMargin;
    }

    /**
     * 设置初始保证金
     *
     * @param initialMargin 初始保证金
     */
    public void setInitialMargin(BigDecimal initialMargin) {
        this.initialMargin = initialMargin;
    }

    /**
     * 获取维持保证金
     *
     * @return 维持保证金
     */
    public BigDecimal getMaintMargin() {
        return maintMargin;
    }

    /**
     * 设置维持保证金
     *
     * @param maintMargin 维持保证金
     */
    public void setMaintMargin(BigDecimal maintMargin) {
        this.maintMargin = maintMargin;
    }

    /**
     * 获取最大可平仓数量
     *
     * @return 最大可平仓数量
     */
    public BigDecimal getMaxQuantity() {
        return maxQuantity;
    }

    /**
     * 设置最大可平仓数量
     *
     * @param maxQuantity 最大可平仓数量
     */
    public void setMaxQuantity(BigDecimal maxQuantity) {
        this.maxQuantity = maxQuantity;
    }

    /**
     * 获取更新时间
     *
     * @return 更新时间
     */
    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "PositionDTO{" +
                "accountId='" + accountId + '\'' +
                ", symbol='" + symbol + '\'' +
                ", positionSide='" + positionSide + '\'' +
                ", leverage=" + leverage +
                ", quantity=" + quantity +
                ", entryPrice=" + entryPrice +
                ", markPrice=" + markPrice +
                ", unrealizedProfit=" + unrealizedProfit +
                ", realizedProfit=" + realizedProfit +
                ", initialMargin=" + initialMargin +
                ", maintMargin=" + maintMargin +
                ", maxQuantity=" + maxQuantity +
                ", updateTime=" + updateTime +
                '}';
    }
} 