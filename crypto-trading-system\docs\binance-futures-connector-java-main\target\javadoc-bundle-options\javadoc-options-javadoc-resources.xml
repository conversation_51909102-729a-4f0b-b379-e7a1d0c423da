<?xml version="1.0" encoding="UTF-8"?>
<javadocOptions>
  <docletArtifacts>
    <docletArtifact />
  </docletArtifacts>
  <tagletArtifacts>
    <tagletArtifact />
  </tagletArtifacts>
  <excludePackageNames>
    <excludePackageName>com.binance.connector.futures.client.utils</excludePackageName>
    <excludePackageName>com.binance.connector.futures.client.enums</excludePackageName>
    <excludePackageName>com.binance.connector.futures.client.exceptions</excludePackageName>
    <excludePackageName>com.binance.connector.futures.client.impl.FuturesClientImpl</excludePackageName>
    <excludePackageName>com.binance.connector.futures.logging</excludePackageName>
  </excludePackageNames>
  <javadocResourcesDirectory>src/main/javadoc</javadocResourcesDirectory>
</javadocOptions>
