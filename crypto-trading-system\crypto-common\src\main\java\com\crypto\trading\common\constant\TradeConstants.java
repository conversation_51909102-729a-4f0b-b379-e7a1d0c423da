package com.crypto.trading.common.constant;

import java.math.BigDecimal;

/**
 * 交易常量类
 * <p>
 * 定义交易相关的常量，如交易类型、交易状态等
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public final class TradeConstants {

    /**
     * 私有构造函数，防止实例化
     */
    private TradeConstants() {
        throw new IllegalStateException("常量类不允许实例化");
    }

    /**
     * 买入方向
     */
    public static final String DIRECTION_BUY = "BUY";

    /**
     * 卖出方向
     */
    public static final String DIRECTION_SELL = "SELL";

    /**
     * 做多方向
     */
    public static final String POSITION_LONG = "LONG";

    /**
     * 做空方向
     */
    public static final String POSITION_SHORT = "SHORT";

    /**
     * 市价单类型
     */
    public static final String ORDER_TYPE_MARKET = "MARKET";

    /**
     * 限价单类型
     */
    public static final String ORDER_TYPE_LIMIT = "LIMIT";

    /**
     * 止损单类型
     */
    public static final String ORDER_TYPE_STOP = "STOP";

    /**
     * 止损限价单类型
     */
    public static final String ORDER_TYPE_STOP_LIMIT = "STOP_LIMIT";

    /**
     * 跟踪止损单类型
     */
    public static final String ORDER_TYPE_TRAILING_STOP = "TRAILING_STOP";

    /**
     * 新订单状态
     */
    public static final String ORDER_STATUS_NEW = "NEW";

    /**
     * 部分成交状态
     */
    public static final String ORDER_STATUS_PARTIALLY_FILLED = "PARTIALLY_FILLED";

    /**
     * 完全成交状态
     */
    public static final String ORDER_STATUS_FILLED = "FILLED";

    /**
     * 已取消状态
     */
    public static final String ORDER_STATUS_CANCELED = "CANCELED";

    /**
     * 已拒绝状态
     */
    public static final String ORDER_STATUS_REJECTED = "REJECTED";

    /**
     * 已过期状态
     */
    public static final String ORDER_STATUS_EXPIRED = "EXPIRED";

    /**
     * 有效至取消
     */
    public static final String TIME_IN_FORCE_GTC = "GTC";

    /**
     * 立即成交或取消
     */
    public static final String TIME_IN_FORCE_IOC = "IOC";

    /**
     * 全部成交或取消
     */
    public static final String TIME_IN_FORCE_FOK = "FOK";

    /**
     * 永续合约类型
     */
    public static final String CONTRACT_TYPE_PERPETUAL = "PERPETUAL";

    /**
     * 交割合约类型
     */
    public static final String CONTRACT_TYPE_DELIVERY = "DELIVERY";

    /**
     * 1分钟K线周期
     */
    public static final String KLINE_INTERVAL_1M = "1m";

    /**
     * 3分钟K线周期
     */
    public static final String KLINE_INTERVAL_3M = "3m";

    /**
     * 5分钟K线周期
     */
    public static final String KLINE_INTERVAL_5M = "5m";

    /**
     * 15分钟K线周期
     */
    public static final String KLINE_INTERVAL_15M = "15m";

    /**
     * 30分钟K线周期
     */
    public static final String KLINE_INTERVAL_30M = "30m";

    /**
     * 1小时K线周期
     */
    public static final String KLINE_INTERVAL_1H = "1h";

    /**
     * 2小时K线周期
     */
    public static final String KLINE_INTERVAL_2H = "2h";

    /**
     * 4小时K线周期
     */
    public static final String KLINE_INTERVAL_4H = "4h";

    /**
     * 6小时K线周期
     */
    public static final String KLINE_INTERVAL_6H = "6h";

    /**
     * 8小时K线周期
     */
    public static final String KLINE_INTERVAL_8H = "8h";

    /**
     * 12小时K线周期
     */
    public static final String KLINE_INTERVAL_12H = "12h";

    /**
     * 1天K线周期
     */
    public static final String KLINE_INTERVAL_1D = "1d";

    /**
     * 3天K线周期
     */
    public static final String KLINE_INTERVAL_3D = "3d";

    /**
     * 1周K线周期
     */
    public static final String KLINE_INTERVAL_1W = "1w";

    /**
     * 1月K线周期
     */
    public static final String KLINE_INTERVAL_1M_MONTH = "1M";

    /**
     * 杠杆倍数 - 1倍
     */
    public static final int LEVERAGE_1X = 1;

    /**
     * 杠杆倍数 - 5倍
     */
    public static final int LEVERAGE_5X = 5;

    /**
     * 杠杆倍数 - 10倍
     */
    public static final int LEVERAGE_10X = 10;

    /**
     * 杠杆倍数 - 20倍
     */
    public static final int LEVERAGE_20X = 20;

    /**
     * 杠杆倍数 - 50倍
     */
    public static final int LEVERAGE_50X = 50;

    /**
     * 杠杆倍数 - 100倍
     */
    public static final int LEVERAGE_100X = 100;

    /**
     * 杠杆倍数 - 125倍
     */
    public static final int LEVERAGE_125X = 125;

    /**
     * 交易手续费率 - 开仓Maker
     */
    public static final BigDecimal FEE_RATE_MAKER_OPEN = new BigDecimal("0.0002");

    /**
     * 交易手续费率 - 开仓Taker
     */
    public static final BigDecimal FEE_RATE_TAKER_OPEN = new BigDecimal("0.0004");

    /**
     * 交易手续费率 - 平仓Maker
     */
    public static final BigDecimal FEE_RATE_MAKER_CLOSE = new BigDecimal("0.0002");

    /**
     * 交易手续费率 - 平仓Taker
     */
    public static final BigDecimal FEE_RATE_TAKER_CLOSE = new BigDecimal("0.0004");

    /**
     * 最小下单数量
     */
    public static final BigDecimal MIN_ORDER_QUANTITY = new BigDecimal("0.001");

    /**
     * 最大下单数量
     */
    public static final BigDecimal MAX_ORDER_QUANTITY = new BigDecimal("10000");

    /**
     * 价格精度
     */
    public static final int PRICE_PRECISION = 2;

    /**
     * 数量精度
     */
    public static final int QUANTITY_PRECISION = 3;

    /**
     * 起始仓位资金（USDT）
     */
    public static final BigDecimal INITIAL_POSITION_SIZE = new BigDecimal("100");

    /**
     * 最小追踪止损激活价格变动百分比
     */
    public static final BigDecimal MIN_TRAILING_STOP_ACTIVATION_PERCENT = new BigDecimal("0.01");

    /**
     * 最大追踪止损激活价格变动百分比
     */
    public static final BigDecimal MAX_TRAILING_STOP_ACTIVATION_PERCENT = new BigDecimal("5.00");

    /**
     * 默认追踪止损回调百分比
     */
    public static final BigDecimal DEFAULT_TRAILING_STOP_CALLBACK_PERCENT = new BigDecimal("0.5");
}