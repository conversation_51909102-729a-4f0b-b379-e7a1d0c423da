#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
ML管道测试辅助工具

提供ML管道集成测试所需的辅助函数和工具类，包括性能监控、模型验证、
特征工程测试和预测准确性验证等功能。
"""

import asyncio
import json
import numpy as np
import pandas as pd
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple, Union
from dataclasses import dataclass, field
from pathlib import Path
from loguru import logger


@dataclass
class MLPerformanceMetrics:
    """ML性能指标"""
    training_time_seconds: float
    inference_time_ms: float
    model_accuracy: float
    precision: float
    recall: float
    f1_score: float
    auc_score: float
    prediction_latency_ms: float
    memory_usage_mb: float
    model_size_mb: float
    feature_extraction_time_ms: float
    signal_generation_time_ms: float


@dataclass
class ModelValidationResult:
    """模型验证结果"""
    is_valid: bool
    validation_errors: List[str]
    performance_metrics: MLPerformanceMetrics
    model_metadata: Dict[str, Any]
    validation_timestamp: datetime


@dataclass
class FeatureQualityMetrics:
    """特征质量指标"""
    feature_count: int
    missing_value_rate: float
    correlation_matrix: Optional[np.ndarray]
    feature_importance: Dict[str, float]
    outlier_rate: float
    feature_stability: float
    information_value: Dict[str, float]


class MLPerformanceMonitor:
    """ML性能监控器"""
    
    def __init__(self):
        """初始化ML性能监控器"""
        self.metrics_history = []
        self.current_metrics = {}
        self.start_times = {}
        self.operation_counts = {}
        
        logger.info("ML性能监控器已初始化")
    
    def start_operation(self, operation_name: str) -> None:
        """
        开始监控操作
        
        Args:
            operation_name: 操作名称
        """
        self.start_times[operation_name] = time.time()
        self.operation_counts[operation_name] = self.operation_counts.get(operation_name, 0) + 1
        logger.debug(f"开始监控操作: {operation_name}")
    
    def end_operation(self, operation_name: str) -> float:
        """
        结束监控操作
        
        Args:
            operation_name: 操作名称
        
        Returns:
            float: 操作耗时（秒）
        """
        if operation_name not in self.start_times:
            logger.warning(f"操作 {operation_name} 未开始监控")
            return 0.0
        
        duration = time.time() - self.start_times[operation_name]
        self.current_metrics[f"{operation_name}_duration"] = duration
        
        logger.debug(f"操作完成: {operation_name}, 耗时: {duration:.3f}秒")
        return duration
    
    def record_metric(self, metric_name: str, value: Union[float, int]) -> None:
        """
        记录指标
        
        Args:
            metric_name: 指标名称
            value: 指标值
        """
        self.current_metrics[metric_name] = value
        logger.debug(f"记录指标: {metric_name} = {value}")
    
    def get_current_metrics(self) -> Dict[str, Any]:
        """获取当前指标"""
        return self.current_metrics.copy()
    
    def reset_metrics(self) -> None:
        """重置指标"""
        self.current_metrics.clear()
        self.start_times.clear()
        logger.debug("指标已重置")


class ModelValidator:
    """模型验证器"""
    
    def __init__(self):
        """初始化模型验证器"""
        self.validation_rules = {
            'min_accuracy': 0.6,  # 最小准确率
            'min_precision': 0.5,  # 最小精确率
            'min_recall': 0.5,     # 最小召回率
            'max_inference_time_ms': 100,  # 最大推理时间
            'max_model_size_mb': 500,      # 最大模型大小
            'min_feature_count': 5,        # 最小特征数量
            'max_memory_usage_mb': 1024    # 最大内存使用
        }
        
        logger.info("模型验证器已初始化")
    
    def validate_model_performance(self, metrics: MLPerformanceMetrics) -> ModelValidationResult:
        """
        验证模型性能
        
        Args:
            metrics: ML性能指标
        
        Returns:
            ModelValidationResult: 验证结果
        """
        errors = []
        
        # 准确率检查
        if metrics.model_accuracy < self.validation_rules['min_accuracy']:
            errors.append(f"模型准确率过低: {metrics.model_accuracy:.3f} < {self.validation_rules['min_accuracy']}")
        
        # 精确率检查
        if metrics.precision < self.validation_rules['min_precision']:
            errors.append(f"模型精确率过低: {metrics.precision:.3f} < {self.validation_rules['min_precision']}")
        
        # 召回率检查
        if metrics.recall < self.validation_rules['min_recall']:
            errors.append(f"模型召回率过低: {metrics.recall:.3f} < {self.validation_rules['min_recall']}")
        
        # 推理时间检查
        if metrics.inference_time_ms > self.validation_rules['max_inference_time_ms']:
            errors.append(f"推理时间过长: {metrics.inference_time_ms:.1f}ms > {self.validation_rules['max_inference_time_ms']}ms")
        
        # 模型大小检查
        if metrics.model_size_mb > self.validation_rules['max_model_size_mb']:
            errors.append(f"模型大小过大: {metrics.model_size_mb:.1f}MB > {self.validation_rules['max_model_size_mb']}MB")
        
        # 内存使用检查
        if metrics.memory_usage_mb > self.validation_rules['max_memory_usage_mb']:
            errors.append(f"内存使用过高: {metrics.memory_usage_mb:.1f}MB > {self.validation_rules['max_memory_usage_mb']}MB")
        
        return ModelValidationResult(
            is_valid=len(errors) == 0,
            validation_errors=errors,
            performance_metrics=metrics,
            model_metadata={
                'validation_timestamp': datetime.now().isoformat(),
                'validation_rules': self.validation_rules
            },
            validation_timestamp=datetime.now()
        )
    
    def validate_model_predictions(self, predictions: np.ndarray, 
                                 ground_truth: np.ndarray) -> Dict[str, float]:
        """
        验证模型预测结果
        
        Args:
            predictions: 预测结果
            ground_truth: 真实标签
        
        Returns:
            Dict[str, float]: 验证指标
        """
        from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score
        
        try:
            # 计算分类指标
            accuracy = accuracy_score(ground_truth, predictions)
            precision = precision_score(ground_truth, predictions, average='weighted', zero_division=0)
            recall = recall_score(ground_truth, predictions, average='weighted', zero_division=0)
            f1 = f1_score(ground_truth, predictions, average='weighted', zero_division=0)
            
            # 计算AUC（如果是二分类）
            try:
                if len(np.unique(ground_truth)) == 2:
                    auc = roc_auc_score(ground_truth, predictions)
                else:
                    auc = 0.0
            except:
                auc = 0.0
            
            return {
                'accuracy': accuracy,
                'precision': precision,
                'recall': recall,
                'f1_score': f1,
                'auc_score': auc
            }
            
        except Exception as e:
            logger.error(f"预测验证失败: {str(e)}")
            return {
                'accuracy': 0.0,
                'precision': 0.0,
                'recall': 0.0,
                'f1_score': 0.0,
                'auc_score': 0.0
            }


class FeatureEngineeringTester:
    """特征工程测试器"""
    
    def __init__(self):
        """初始化特征工程测试器"""
        self.feature_tests = {
            'missing_value_threshold': 0.1,  # 缺失值阈值
            'correlation_threshold': 0.95,   # 相关性阈值
            'outlier_threshold': 0.05,       # 异常值阈值
            'min_variance': 0.01             # 最小方差
        }
        
        logger.info("特征工程测试器已初始化")
    
    def test_feature_quality(self, features: pd.DataFrame) -> FeatureQualityMetrics:
        """
        测试特征质量
        
        Args:
            features: 特征数据
        
        Returns:
            FeatureQualityMetrics: 特征质量指标
        """
        # 缺失值率
        missing_rate = features.isnull().sum().sum() / (features.shape[0] * features.shape[1])
        
        # 相关性矩阵
        try:
            correlation_matrix = features.corr().values
        except:
            correlation_matrix = None
        
        # 特征重要性（简化计算）
        feature_importance = {}
        for col in features.columns:
            if features[col].dtype in ['int64', 'float64']:
                # 使用方差作为重要性的简单度量
                importance = features[col].var()
                feature_importance[col] = float(importance) if not np.isnan(importance) else 0.0
        
        # 异常值率（使用IQR方法）
        outlier_count = 0
        total_values = 0
        
        for col in features.select_dtypes(include=[np.number]).columns:
            Q1 = features[col].quantile(0.25)
            Q3 = features[col].quantile(0.75)
            IQR = Q3 - Q1
            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR
            
            outliers = ((features[col] < lower_bound) | (features[col] > upper_bound)).sum()
            outlier_count += outliers
            total_values += len(features[col])
        
        outlier_rate = outlier_count / total_values if total_values > 0 else 0.0
        
        # 特征稳定性（简化为方差的倒数）
        variances = features.select_dtypes(include=[np.number]).var()
        feature_stability = float(1.0 / (variances.mean() + 1e-8)) if len(variances) > 0 else 0.0
        
        # 信息价值（简化计算）
        information_value = {}
        for col in features.columns:
            if features[col].dtype in ['int64', 'float64']:
                # 简化的信息价值计算
                iv = float(features[col].std()) if not features[col].std() == 0 else 0.0
                information_value[col] = iv
        
        return FeatureQualityMetrics(
            feature_count=len(features.columns),
            missing_value_rate=missing_rate,
            correlation_matrix=correlation_matrix,
            feature_importance=feature_importance,
            outlier_rate=outlier_rate,
            feature_stability=feature_stability,
            information_value=information_value
        )
    
    def validate_feature_engineering(self, original_data: pd.DataFrame, 
                                   engineered_features: pd.DataFrame) -> Dict[str, Any]:
        """
        验证特征工程结果
        
        Args:
            original_data: 原始数据
            engineered_features: 工程化特征
        
        Returns:
            Dict[str, Any]: 验证结果
        """
        validation_result = {
            'feature_count_increase': len(engineered_features.columns) - len(original_data.columns),
            'data_integrity_preserved': len(engineered_features) == len(original_data),
            'missing_values_handled': engineered_features.isnull().sum().sum() <= original_data.isnull().sum().sum(),
            'feature_quality_metrics': self.test_feature_quality(engineered_features),
            'validation_passed': True,
            'validation_errors': []
        }
        
        # 检查特征数量
        if validation_result['feature_count_increase'] <= 0:
            validation_result['validation_errors'].append("特征工程未增加特征数量")
            validation_result['validation_passed'] = False
        
        # 检查数据完整性
        if not validation_result['data_integrity_preserved']:
            validation_result['validation_errors'].append("数据完整性未保持")
            validation_result['validation_passed'] = False
        
        # 检查缺失值处理
        if not validation_result['missing_values_handled']:
            validation_result['validation_errors'].append("缺失值处理不当")
            validation_result['validation_passed'] = False
        
        return validation_result


class PredictionAccuracyValidator:
    """预测准确性验证器"""
    
    def __init__(self):
        """初始化预测准确性验证器"""
        self.accuracy_thresholds = {
            'classification_accuracy': 0.6,
            'regression_mse': 0.1,
            'regression_mae': 0.05,
            'directional_accuracy': 0.55  # 方向准确率
        }
        
        logger.info("预测准确性验证器已初始化")
    
    def validate_classification_predictions(self, predictions: np.ndarray, 
                                          ground_truth: np.ndarray) -> Dict[str, Any]:
        """
        验证分类预测准确性
        
        Args:
            predictions: 预测结果
            ground_truth: 真实标签
        
        Returns:
            Dict[str, Any]: 验证结果
        """
        from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
        
        try:
            accuracy = accuracy_score(ground_truth, predictions)
            report = classification_report(ground_truth, predictions, output_dict=True)
            conf_matrix = confusion_matrix(ground_truth, predictions)
            
            validation_result = {
                'accuracy': accuracy,
                'classification_report': report,
                'confusion_matrix': conf_matrix.tolist(),
                'accuracy_threshold_met': accuracy >= self.accuracy_thresholds['classification_accuracy'],
                'validation_passed': accuracy >= self.accuracy_thresholds['classification_accuracy']
            }
            
            return validation_result
            
        except Exception as e:
            logger.error(f"分类预测验证失败: {str(e)}")
            return {
                'accuracy': 0.0,
                'validation_passed': False,
                'error': str(e)
            }
    
    def validate_regression_predictions(self, predictions: np.ndarray, 
                                      ground_truth: np.ndarray) -> Dict[str, Any]:
        """
        验证回归预测准确性
        
        Args:
            predictions: 预测结果
            ground_truth: 真实值
        
        Returns:
            Dict[str, Any]: 验证结果
        """
        from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
        
        try:
            mse = mean_squared_error(ground_truth, predictions)
            mae = mean_absolute_error(ground_truth, predictions)
            r2 = r2_score(ground_truth, predictions)
            
            validation_result = {
                'mse': mse,
                'mae': mae,
                'r2_score': r2,
                'mse_threshold_met': mse <= self.accuracy_thresholds['regression_mse'],
                'mae_threshold_met': mae <= self.accuracy_thresholds['regression_mae'],
                'validation_passed': (mse <= self.accuracy_thresholds['regression_mse'] and 
                                    mae <= self.accuracy_thresholds['regression_mae'])
            }
            
            return validation_result
            
        except Exception as e:
            logger.error(f"回归预测验证失败: {str(e)}")
            return {
                'mse': float('inf'),
                'mae': float('inf'),
                'r2_score': -float('inf'),
                'validation_passed': False,
                'error': str(e)
            }
    
    def validate_directional_accuracy(self, predictions: np.ndarray, 
                                    ground_truth: np.ndarray) -> Dict[str, Any]:
        """
        验证方向预测准确性
        
        Args:
            predictions: 预测结果
            ground_truth: 真实值
        
        Returns:
            Dict[str, Any]: 验证结果
        """
        try:
            # 计算方向（上涨/下跌）
            pred_direction = np.sign(np.diff(predictions))
            true_direction = np.sign(np.diff(ground_truth))
            
            # 计算方向准确率
            directional_accuracy = np.mean(pred_direction == true_direction)
            
            validation_result = {
                'directional_accuracy': directional_accuracy,
                'threshold_met': directional_accuracy >= self.accuracy_thresholds['directional_accuracy'],
                'validation_passed': directional_accuracy >= self.accuracy_thresholds['directional_accuracy'],
                'total_predictions': len(pred_direction),
                'correct_directions': np.sum(pred_direction == true_direction)
            }
            
            return validation_result
            
        except Exception as e:
            logger.error(f"方向预测验证失败: {str(e)}")
            return {
                'directional_accuracy': 0.0,
                'validation_passed': False,
                'error': str(e)
            }


class SignalQualityAssessor:
    """信号质量评估器"""
    
    def __init__(self):
        """初始化信号质量评估器"""
        self.quality_thresholds = {
            'signal_consistency': 0.8,    # 信号一致性
            'signal_stability': 0.7,      # 信号稳定性
            'signal_responsiveness': 0.6,  # 信号响应性
            'false_signal_rate': 0.2      # 虚假信号率
        }
        
        logger.info("信号质量评估器已初始化")
    
    def assess_signal_quality(self, signals: List[Dict[str, Any]], 
                            market_data: pd.DataFrame) -> Dict[str, Any]:
        """
        评估信号质量
        
        Args:
            signals: 交易信号列表
            market_data: 市场数据
        
        Returns:
            Dict[str, Any]: 信号质量评估结果
        """
        if not signals:
            return {
                'signal_count': 0,
                'quality_score': 0.0,
                'assessment_passed': False,
                'error': '无信号数据'
            }
        
        try:
            # 信号统计
            signal_count = len(signals)
            buy_signals = sum(1 for s in signals if s.get('action') == 'buy')
            sell_signals = sum(1 for s in signals if s.get('action') == 'sell')
            hold_signals = signal_count - buy_signals - sell_signals
            
            # 信号一致性（相邻信号的一致性）
            consistency_score = self._calculate_signal_consistency(signals)
            
            # 信号稳定性（信号强度的稳定性）
            stability_score = self._calculate_signal_stability(signals)
            
            # 信号响应性（对市场变化的响应）
            responsiveness_score = self._calculate_signal_responsiveness(signals, market_data)
            
            # 虚假信号率估算
            false_signal_rate = self._estimate_false_signal_rate(signals, market_data)
            
            # 综合质量分数
            quality_score = (
                consistency_score * 0.3 +
                stability_score * 0.3 +
                responsiveness_score * 0.2 +
                (1 - false_signal_rate) * 0.2
            )
            
            assessment_result = {
                'signal_count': signal_count,
                'buy_signals': buy_signals,
                'sell_signals': sell_signals,
                'hold_signals': hold_signals,
                'consistency_score': consistency_score,
                'stability_score': stability_score,
                'responsiveness_score': responsiveness_score,
                'false_signal_rate': false_signal_rate,
                'quality_score': quality_score,
                'assessment_passed': (
                    consistency_score >= self.quality_thresholds['signal_consistency'] and
                    stability_score >= self.quality_thresholds['signal_stability'] and
                    responsiveness_score >= self.quality_thresholds['signal_responsiveness'] and
                    false_signal_rate <= self.quality_thresholds['false_signal_rate']
                )
            }
            
            return assessment_result
            
        except Exception as e:
            logger.error(f"信号质量评估失败: {str(e)}")
            return {
                'signal_count': len(signals),
                'quality_score': 0.0,
                'assessment_passed': False,
                'error': str(e)
            }
    
    def _calculate_signal_consistency(self, signals: List[Dict[str, Any]]) -> float:
        """计算信号一致性"""
        if len(signals) < 2:
            return 1.0
        
        consistent_count = 0
        total_pairs = 0
        
        for i in range(1, len(signals)):
            prev_signal = signals[i-1]
            curr_signal = signals[i]
            
            # 检查信号强度的一致性
            prev_strength = prev_signal.get('strength', 0.5)
            curr_strength = curr_signal.get('strength', 0.5)
            
            if abs(prev_strength - curr_strength) < 0.3:  # 强度变化不大
                consistent_count += 1
            
            total_pairs += 1
        
        return consistent_count / total_pairs if total_pairs > 0 else 1.0
    
    def _calculate_signal_stability(self, signals: List[Dict[str, Any]]) -> float:
        """计算信号稳定性"""
        strengths = [s.get('strength', 0.5) for s in signals]
        
        if len(strengths) < 2:
            return 1.0
        
        # 计算强度的标准差，稳定性与标准差成反比
        std_dev = np.std(strengths)
        stability = 1.0 / (1.0 + std_dev)
        
        return float(stability)
    
    def _calculate_signal_responsiveness(self, signals: List[Dict[str, Any]], 
                                       market_data: pd.DataFrame) -> float:
        """计算信号响应性"""
        if len(signals) < 2 or len(market_data) < 2:
            return 0.5
        
        # 简化的响应性计算：检查信号变化与价格变化的相关性
        try:
            signal_changes = []
            price_changes = []
            
            for i in range(1, min(len(signals), len(market_data))):
                # 信号变化
                prev_strength = signals[i-1].get('strength', 0.5)
                curr_strength = signals[i].get('strength', 0.5)
                signal_change = curr_strength - prev_strength
                signal_changes.append(signal_change)
                
                # 价格变化
                if 'close' in market_data.columns:
                    price_change = market_data.iloc[i]['close'] - market_data.iloc[i-1]['close']
                    price_changes.append(price_change)
            
            if len(signal_changes) > 1 and len(price_changes) > 1:
                correlation = np.corrcoef(signal_changes, price_changes)[0, 1]
                responsiveness = abs(correlation) if not np.isnan(correlation) else 0.0
                return float(responsiveness)
            
        except Exception as e:
            logger.debug(f"响应性计算失败: {str(e)}")
        
        return 0.5
    
    def _estimate_false_signal_rate(self, signals: List[Dict[str, Any]], 
                                  market_data: pd.DataFrame) -> float:
        """估算虚假信号率"""
        # 简化的虚假信号率估算
        # 实际应用中需要更复杂的逻辑来判断信号的有效性
        
        if len(signals) < 5:
            return 0.0
        
        # 假设频繁变化的信号可能是虚假信号
        action_changes = 0
        for i in range(1, len(signals)):
            prev_action = signals[i-1].get('action', 'hold')
            curr_action = signals[i].get('action', 'hold')
            
            if prev_action != curr_action and prev_action != 'hold' and curr_action != 'hold':
                action_changes += 1
        
        # 虚假信号率 = 频繁变化的比例
        false_rate = action_changes / len(signals)
        return min(false_rate, 1.0)


# 导出主要类和函数
__all__ = [
    'MLPerformanceMetrics',
    'ModelValidationResult',
    'FeatureQualityMetrics',
    'MLPerformanceMonitor',
    'ModelValidator',
    'FeatureEngineeringTester',
    'PredictionAccuracyValidator',
    'SignalQualityAssessor'
]