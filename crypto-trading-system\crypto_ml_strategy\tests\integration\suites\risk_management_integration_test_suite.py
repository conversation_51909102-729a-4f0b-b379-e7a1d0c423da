#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
风险管理集成测试套件

验证风险管理系统的完整功能，包括风险控制、仓位管理、
止损止盈、回撤控制等核心功能的集成测试。

Author: Crypto ML Strategy Team
Date: 2024-12-19
"""

import sys
import os
import unittest
import asyncio
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import logging
import time
import threading
from unittest.mock import Mock, patch, MagicMock

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..', 'src'))

# 导入风险管理模块
from risk_management import (
    RiskConfig, RiskEvent, RiskLevel, PositionInfo,
    RiskControlEngine, RiskAssessor, PositionManager,
    StopLossTakeProfitManager, DrawdownController,
    PositionSizeMethod, StopLossType, TakeProfitType,
    create_risk_manager, create_conservative_risk_manager,
    create_aggressive_risk_manager
)

# 导入测试工具
from ..utils.test_orchestrator import TestOrchestrator
from ..config.integration_test_config import IntegrationTestConfig
from ..data.test_data_generator import TestDataGenerator
from ..fixtures.mock_services import MockKafkaService, MockJavaAPIService

logger = logging.getLogger(__name__)


class RiskManagementIntegrationTestSuite(unittest.TestCase):
    """风险管理集成测试套件"""
    
    @classmethod
    def setUpClass(cls):
        """测试套件初始化"""
        cls.config = IntegrationTestConfig()
        cls.test_orchestrator = TestOrchestrator(cls.config)
        cls.data_generator = TestDataGenerator()
        
        # 设置日志
        logging.basicConfig(level=logging.INFO)
        
        # 初始化模拟服务
        cls.mock_kafka = MockKafkaService()
        cls.mock_java_api = MockJavaAPIService()
        
        logger.info("Risk Management Integration Test Suite initialized")
    
    @classmethod
    def tearDownClass(cls):
        """测试套件清理"""
        if hasattr(cls, 'mock_kafka'):
            cls.mock_kafka.stop()
        if hasattr(cls, 'mock_java_api'):
            cls.mock_java_api.stop()
        
        logger.info("Risk Management Integration Test Suite cleaned up")
    
    def setUp(self):
        """每个测试用例的初始化"""
        self.test_start_time = datetime.now()
        
        # 创建测试用的风险管理配置
        self.risk_config = RiskConfig(
            max_single_position_ratio=0.1,
            max_total_position_ratio=0.8,
            position_size_method=PositionSizeMethod.ADAPTIVE,
            stop_loss_type=StopLossType.ADAPTIVE,
            take_profit_type=TakeProfitType.RISK_REWARD_RATIO,
            max_drawdown_threshold=0.15,
            risk_reward_ratio=2.0,
            risk_check_interval=1.0
        )
        
        # 创建风险控制引擎
        self.risk_engine = RiskControlEngine(self.risk_config)
        
        # 生成测试数据
        self.test_data = self._generate_test_data()
        
        logger.info(f"Test case setup completed: {self._testMethodName}")
    
    def tearDown(self):
        """每个测试用例的清理"""
        if hasattr(self, 'risk_engine') and self.risk_engine.is_running:
            self.risk_engine.stop()
        
        test_duration = (datetime.now() - self.test_start_time).total_seconds()
        logger.info(f"Test case completed: {self._testMethodName}, Duration: {test_duration:.2f}s")
    
    def _generate_test_data(self) -> Dict[str, Any]:
        """生成测试数据"""
        try:
            # 生成价格数据
            btc_data = self.data_generator.generate_price_data(
                symbol='BTCUSDT',
                start_time=datetime.now() - timedelta(days=30),
                end_time=datetime.now(),
                interval='1h',
                initial_price=50000
            )
            
            eth_data = self.data_generator.generate_price_data(
                symbol='ETHUSDT',
                start_time=datetime.now() - timedelta(days=30),
                end_time=datetime.now(),
                interval='1h',
                initial_price=3000
            )
            
            # 生成投资组合数据
            portfolio_data = {
                'total_value': 100000,
                'balance': 100000,
                'available_balance': 80000,
                'positions': {
                    'BTCUSDT': {
                        'size': 1.5,
                        'value': 75000,
                        'side': 'long',
                        'entry_price': 50000,
                        'current_price': 50000,
                        'unrealized_pnl': 0
                    },
                    'ETHUSDT': {
                        'size': 8.0,
                        'value': 24000,
                        'side': 'long',
                        'entry_price': 3000,
                        'current_price': 3000,
                        'unrealized_pnl': 0
                    }
                }
            }
            
            return {
                'price_data': {
                    'BTCUSDT': btc_data,
                    'ETHUSDT': eth_data
                },
                'portfolio_data': portfolio_data,
                'market_indicators': {
                    'BTCUSDT': {
                        'atr': 1000,
                        'ma20': 49500,
                        'volatility': 0.25,
                        'support_level': 48000,
                        'resistance_level': 52000
                    },
                    'ETHUSDT': {
                        'atr': 80,
                        'ma20': 2950,
                        'volatility': 0.30,
                        'support_level': 2800,
                        'resistance_level': 3200
                    }
                }
            }
            
        except Exception as e:
            logger.error(f"Error generating test data: {e}")
            raise
    
    def test_01_risk_control_engine_initialization(self):
        """测试风险控制引擎初始化"""
        logger.info("Testing risk control engine initialization...")
        
        try:
            # 验证引擎初始化状态
            self.assertIsNotNone(self.risk_engine)
            self.assertFalse(self.risk_engine.is_running)
            self.assertFalse(self.risk_engine.emergency_mode)
            self.assertFalse(self.risk_engine.trading_suspended)
            
            # 验证组件初始化
            self.assertIsNotNone(self.risk_engine.risk_assessor)
            self.assertIsNotNone(self.risk_engine.position_manager)
            self.assertIsNotNone(self.risk_engine.stop_loss_take_profit_manager)
            self.assertIsNotNone(self.risk_engine.drawdown_controller)
            
            # 验证配置
            self.assertEqual(self.risk_engine.config, self.risk_config)
            
            # 验证统计信息初始化
            stats = self.risk_engine.statistics
            self.assertEqual(stats['total_events'], 0)
            self.assertEqual(stats['total_actions'], 0)
            self.assertEqual(stats['emergency_triggers'], 0)
            
            logger.info("✅ Risk control engine initialization test passed")
            
        except Exception as e:
            logger.error(f"❌ Risk control engine initialization test failed: {e}")
            raise
    
    def test_02_risk_control_engine_lifecycle(self):
        """测试风险控制引擎生命周期"""
        logger.info("Testing risk control engine lifecycle...")
        
        try:
            # 测试启动
            self.risk_engine.start()
            self.assertTrue(self.risk_engine.is_running)
            
            # 等待引擎启动
            time.sleep(1)
            
            # 获取引擎状态
            status = self.risk_engine.get_engine_status()
            self.assertTrue(status['is_running'])
            self.assertFalse(status['emergency_mode'])
            self.assertFalse(status['trading_suspended'])
            
            # 测试停止
            self.risk_engine.stop()
            self.assertFalse(self.risk_engine.is_running)
            
            logger.info("✅ Risk control engine lifecycle test passed")
            
        except Exception as e:
            logger.error(f"❌ Risk control engine lifecycle test failed: {e}")
            raise
    
    def test_03_market_data_integration(self):
        """测试市场数据集成"""
        logger.info("Testing market data integration...")
        
        try:
            # 启动引擎
            self.risk_engine.start()
            
            # 更新市场数据
            for symbol, price_data in self.test_data['price_data'].items():
                market_data = {
                    'price_df': price_data,
                    'indicators': self.test_data['market_indicators'][symbol]
                }
                self.risk_engine.update_market_data(symbol, market_data)
            
            # 验证数据更新
            # 这里可以通过检查内部组件状态来验证
            time.sleep(0.5)  # 等待数据处理
            
            # 停止引擎
            self.risk_engine.stop()
            
            logger.info("✅ Market data integration test passed")
            
        except Exception as e:
            logger.error(f"❌ Market data integration test failed: {e}")
            raise
    
    def test_04_portfolio_data_integration(self):
        """测试投资组合数据集成"""
        logger.info("Testing portfolio data integration...")
        
        try:
            # 启动引擎
            self.risk_engine.start()
            
            # 更新投资组合数据
            self.risk_engine.update_portfolio_data(self.test_data['portfolio_data'])
            
            # 验证数据更新
            time.sleep(0.5)  # 等待数据处理
            
            # 停止引擎
            self.risk_engine.stop()
            
            logger.info("✅ Portfolio data integration test passed")
            
        except Exception as e:
            logger.error(f"❌ Portfolio data integration test failed: {e}")
            raise
    
    def test_05_position_management_integration(self):
        """测试仓位管理集成"""
        logger.info("Testing position management integration...")
        
        try:
            # 启动引擎
            self.risk_engine.start()
            
            # 创建仓位信息
            btc_position = PositionInfo(
                symbol='BTCUSDT',
                current_size=1.5,
                suggested_size=1.5,
                max_allowed_size=2.0,
                entry_price=50000,
                current_price=50000,
                unrealized_pnl=0,
                stop_loss_price=48000,
                take_profit_price=53000,
                position_value=75000
            )
            
            # 更新仓位
            self.risk_engine.update_position('BTCUSDT', btc_position)
            
            # 验证仓位更新
            time.sleep(0.5)
            
            # 停止引擎
            self.risk_engine.stop()
            
            logger.info("✅ Position management integration test passed")
            
        except Exception as e:
            logger.error(f"❌ Position management integration test failed: {e}")
            raise


    def test_06_stop_loss_take_profit_integration(self):
        """测试止损止盈集成"""
        logger.info("Testing stop loss take profit integration...")

        try:
            # 启动引擎
            self.risk_engine.start()

            # 更新市场数据
            for symbol, price_data in self.test_data['price_data'].items():
                market_data = {
                    'price_df': price_data,
                    'indicators': self.test_data['market_indicators'][symbol]
                }
                self.risk_engine.update_market_data(symbol, market_data)

            # 创建仓位信息
            btc_position = PositionInfo(
                symbol='BTCUSDT',
                current_size=1.5,
                suggested_size=1.5,
                max_allowed_size=2.0,
                entry_price=50000,
                current_price=47000,  # 模拟价格下跌触发止损
                unrealized_pnl=-4500,
                stop_loss_price=48000,
                take_profit_price=53000,
                position_value=70500
            )

            # 更新仓位
            self.risk_engine.update_position('BTCUSDT', btc_position)

            # 等待风险检查
            time.sleep(2)

            # 检查是否生成了止损事件
            events = self.risk_engine.risk_events
            stop_loss_events = [e for e in events if e.event_type == "stop_loss_triggered"]

            # 验证止损事件
            self.assertGreater(len(stop_loss_events), 0, "Should generate stop loss event")

            # 停止引擎
            self.risk_engine.stop()

            logger.info("✅ Stop loss take profit integration test passed")

        except Exception as e:
            logger.error(f"❌ Stop loss take profit integration test failed: {e}")
            raise

    def test_07_drawdown_control_integration(self):
        """测试回撤控制集成"""
        logger.info("Testing drawdown control integration...")

        try:
            # 启动引擎
            self.risk_engine.start()

            # 模拟投资组合价值下跌
            portfolio_values = [100000, 95000, 90000, 85000, 80000]  # 20%回撤

            for value in portfolio_values:
                portfolio_data = self.test_data['portfolio_data'].copy()
                portfolio_data['total_value'] = value

                self.risk_engine.update_portfolio_data(portfolio_data)
                time.sleep(0.5)

            # 等待回撤检查
            time.sleep(1)

            # 检查是否生成了回撤事件
            events = self.risk_engine.risk_events
            drawdown_events = [e for e in events if 'drawdown' in e.event_type.lower()]

            # 验证回撤事件
            self.assertGreater(len(drawdown_events), 0, "Should generate drawdown events")

            # 停止引擎
            self.risk_engine.stop()

            logger.info("✅ Drawdown control integration test passed")

        except Exception as e:
            logger.error(f"❌ Drawdown control integration test failed: {e}")
            raise

    def test_08_emergency_risk_handling(self):
        """测试紧急风险处理"""
        logger.info("Testing emergency risk handling...")

        try:
            # 启动引擎
            self.risk_engine.start()

            # 创建紧急风险事件
            emergency_event = RiskEvent(
                event_type="emergency_stop_triggered",
                severity=RiskLevel.CRITICAL,
                message="Emergency stop triggered due to system anomaly",
                timestamp=datetime.now(),
                action_required=True
            )

            # 添加紧急事件
            self.risk_engine.add_risk_event(emergency_event)

            # 等待紧急处理
            time.sleep(2)

            # 验证紧急模式激活
            self.assertTrue(self.risk_engine.emergency_mode, "Emergency mode should be activated")

            # 检查是否生成了紧急措施
            pending_actions = self.risk_engine.pending_actions
            emergency_actions = [a for a in pending_actions if a.action_type == "emergency_liquidation"]

            # 验证紧急措施
            self.assertGreater(len(emergency_actions), 0, "Should generate emergency actions")

            # 停止引擎
            self.risk_engine.stop()

            logger.info("✅ Emergency risk handling test passed")

        except Exception as e:
            logger.error(f"❌ Emergency risk handling test failed: {e}")
            raise

    def test_09_risk_profile_configurations(self):
        """测试不同风险配置"""
        logger.info("Testing different risk profile configurations...")

        try:
            # 测试保守型风险管理器
            conservative_manager = create_conservative_risk_manager()
            self.assertIsNotNone(conservative_manager)

            conservative_config = conservative_manager.config
            self.assertLessEqual(conservative_config.max_single_position_ratio, 0.05)
            self.assertLessEqual(conservative_config.max_total_position_ratio, 0.6)

            # 测试激进型风险管理器
            aggressive_manager = create_aggressive_risk_manager()
            self.assertIsNotNone(aggressive_manager)

            aggressive_config = aggressive_manager.config
            self.assertGreaterEqual(aggressive_config.max_single_position_ratio, 0.15)
            self.assertGreaterEqual(aggressive_config.max_total_position_ratio, 0.9)

            # 测试默认风险管理器
            default_manager = create_risk_manager()
            self.assertIsNotNone(default_manager)

            logger.info("✅ Risk profile configurations test passed")

        except Exception as e:
            logger.error(f"❌ Risk profile configurations test failed: {e}")
            raise

    def test_10_comprehensive_risk_workflow(self):
        """测试完整风险管理工作流"""
        logger.info("Testing comprehensive risk management workflow...")

        try:
            # 启动引擎
            self.risk_engine.start()

            # 1. 更新市场数据
            for symbol, price_data in self.test_data['price_data'].items():
                market_data = {
                    'price_df': price_data,
                    'indicators': self.test_data['market_indicators'][symbol]
                }
                self.risk_engine.update_market_data(symbol, market_data)

            # 2. 更新投资组合数据
            self.risk_engine.update_portfolio_data(self.test_data['portfolio_data'])

            # 3. 更新仓位信息
            btc_position = PositionInfo(
                symbol='BTCUSDT',
                current_size=1.5,
                suggested_size=1.5,
                max_allowed_size=2.0,
                entry_price=50000,
                current_price=50000,
                unrealized_pnl=0,
                stop_loss_price=48000,
                take_profit_price=53000,
                position_value=75000
            )
            self.risk_engine.update_position('BTCUSDT', btc_position)

            # 4. 模拟市场波动和风险事件
            price_scenarios = [49000, 47000, 45000, 48000, 52000, 54000]

            for price in price_scenarios:
                # 更新价格
                updated_position = PositionInfo(
                    symbol='BTCUSDT',
                    current_size=1.5,
                    suggested_size=1.5,
                    max_allowed_size=2.0,
                    entry_price=50000,
                    current_price=price,
                    unrealized_pnl=(price - 50000) * 1.5,
                    stop_loss_price=48000,
                    take_profit_price=53000,
                    position_value=price * 1.5
                )
                self.risk_engine.update_position('BTCUSDT', updated_position)

                # 等待风险检查
                time.sleep(0.5)

            # 5. 等待所有风险检查完成
            time.sleep(2)

            # 6. 验证工作流结果
            status = self.risk_engine.get_engine_status()
            self.assertTrue(status['is_running'])

            # 检查事件生成
            events = self.risk_engine.risk_events
            self.assertGreater(len(events), 0, "Should generate risk events during workflow")

            # 检查统计信息
            stats = self.risk_engine.statistics
            self.assertGreater(stats['total_events'], 0)

            # 停止引擎
            self.risk_engine.stop()

            logger.info("✅ Comprehensive risk workflow test passed")

        except Exception as e:
            logger.error(f"❌ Comprehensive risk workflow test failed: {e}")
            raise


if __name__ == '__main__':
    unittest.main()
