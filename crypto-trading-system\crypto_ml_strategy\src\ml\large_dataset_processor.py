"""
Crypto ML Strategy - 大数据集处理器

该模块实现了针对大规模数据集的内存高效处理功能，包括分块处理、
流式聚合、内存映射文件操作、数据压缩管理和垃圾回收优化。

主要功能：
- ChunkedDataProcessor: 分块处理大数据集，内存高效
- StreamingDataAggregator: 实时数据聚合，最小内存使用
- MemoryMappedFileHandler: 大数据集的内存映射文件操作
- DataCompressionManager: 智能数据压缩/解压缩管理
- GarbageCollectionOptimizer: 数据密集型操作的GC优化

作者: Crypto ML Strategy Team
日期: 2024-01-20
版本: 1.0.0
"""

import gc
import mmap
import threading
import time
from contextlib import contextmanager
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Dict, List, Optional, Union, Iterator, Callable, Generator, Tuple
import numpy as np
import pandas as pd
from infrastructure.logging.logging_core_manager import get_logger
from benchmarks.performance_logging_core import get_global_performance_logger
from infrastructure.error_handling_system import get_global_error_handler
from .memory_efficient_data_structures import get_global_profiler, MemoryUsageStats
import lz4.frame
import zstandard as zstd
import pickle
import psutil


@dataclass
class ChunkProcessingStats:
    """分块处理统计信息"""
    total_chunks: int
    processed_chunks: int
    failed_chunks: int
    total_rows: int
    processing_time_seconds: float
    memory_peak_mb: float
    average_chunk_time_ms: float
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "total_chunks": self.total_chunks,
            "processed_chunks": self.processed_chunks,
            "failed_chunks": self.failed_chunks,
            "total_rows": self.total_rows,
            "processing_time_seconds": self.processing_time_seconds,
            "memory_peak_mb": self.memory_peak_mb,
            "average_chunk_time_ms": self.average_chunk_time_ms,
            "success_rate": self.processed_chunks / max(self.total_chunks, 1)
        }


class ChunkedDataProcessor:
    """分块数据处理器，用于内存高效的大数据集处理"""
    
    def __init__(self, chunk_size: int = 10000, max_memory_mb: int = 1000):
        self.chunk_size = chunk_size
        self.max_memory_mb = max_memory_mb
        self.logger = get_logger(f"{__name__}.ChunkedDataProcessor")
        self.perf_logger = get_global_performance_logger()
        self.error_handler = get_global_error_handler()
        self.profiler = get_global_profiler()
        
        self._processing_stats: Optional[ChunkProcessingStats] = None
        self._lock = threading.RLock()
    
    def process_file(self, file_path: Union[str, Path], 
                    processor_func: Callable[[pd.DataFrame], pd.DataFrame],
                    output_path: Optional[Union[str, Path]] = None) -> ChunkProcessingStats:
        """分块处理文件"""
        try:
            file_path = Path(file_path)
            if not file_path.exists():
                raise FileNotFoundError(f"File not found: {file_path}")
            
            start_time = time.time()
            total_chunks = 0
            processed_chunks = 0
            failed_chunks = 0
            total_rows = 0
            memory_peak_mb = 0
            chunk_times = []
            
            # 准备输出文件
            if output_path:
                output_path = Path(output_path)
                output_path.parent.mkdir(parents=True, exist_ok=True)
                write_header = True
            
            self.logger.info(f"Starting chunked processing of {file_path}")
            
            # 分块读取和处理
            for chunk_idx, chunk in enumerate(pd.read_csv(file_path, chunksize=self.chunk_size)):
                chunk_start = time.time()
                
                try:
                    # 内存检查
                    current_memory = self._get_current_memory_mb()
                    memory_peak_mb = max(memory_peak_mb, current_memory)
                    
                    if current_memory > self.max_memory_mb:
                        self.logger.warning(f"Memory usage {current_memory:.1f}MB exceeds limit {self.max_memory_mb}MB")
                        gc.collect()  # 强制垃圾回收
                    
                    # 处理数据块
                    processed_chunk = processor_func(chunk)
                    
                    # 保存结果
                    if output_path:
                        mode = 'w' if write_header else 'a'
                        processed_chunk.to_csv(output_path, mode=mode, 
                                             header=write_header, index=False)
                        write_header = False
                    
                    processed_chunks += 1
                    total_rows += len(chunk)
                    
                    chunk_time = (time.time() - chunk_start) * 1000
                    chunk_times.append(chunk_time)
                    
                    if chunk_idx % 100 == 0:
                        self.logger.debug(f"Processed chunk {chunk_idx}, rows: {len(chunk)}, "
                                        f"time: {chunk_time:.1f}ms")
                    
                except Exception as e:
                    failed_chunks += 1
                    self.error_handler.handle_error(e, context={
                        "chunk_index": chunk_idx,
                        "chunk_size": len(chunk) if 'chunk' in locals() else 0
                    })
                    self.logger.error(f"Failed to process chunk {chunk_idx}: {e}")
                
                total_chunks += 1
            
            processing_time = time.time() - start_time
            avg_chunk_time = np.mean(chunk_times) if chunk_times else 0
            
            stats = ChunkProcessingStats(
                total_chunks=total_chunks,
                processed_chunks=processed_chunks,
                failed_chunks=failed_chunks,
                total_rows=total_rows,
                processing_time_seconds=processing_time,
                memory_peak_mb=memory_peak_mb,
                average_chunk_time_ms=avg_chunk_time
            )
            
            with self._lock:
                self._processing_stats = stats
            
            self.logger.info(f"Chunked processing completed: {stats.to_dict()}")
            return stats
            
        except Exception as e:
            self.error_handler.handle_error(e, context={"file_path": str(file_path)})
            raise
    
    def process_dataframe(self, df: pd.DataFrame,
                         processor_func: Callable[[pd.DataFrame], pd.DataFrame]) -> pd.DataFrame:
        """分块处理DataFrame"""
        try:
            if len(df) <= self.chunk_size:
                return processor_func(df)
            
            results = []
            total_chunks = (len(df) + self.chunk_size - 1) // self.chunk_size
            
            for i in range(0, len(df), self.chunk_size):
                chunk = df.iloc[i:i + self.chunk_size]
                
                # 内存检查
                current_memory = self._get_current_memory_mb()
                if current_memory > self.max_memory_mb:
                    gc.collect()
                
                processed_chunk = processor_func(chunk)
                results.append(processed_chunk)
                
                if i // self.chunk_size % 50 == 0:
                    self.logger.debug(f"Processed {i // self.chunk_size + 1}/{total_chunks} chunks")
            
            return pd.concat(results, ignore_index=True)
            
        except Exception as e:
            self.error_handler.handle_error(e, context={"dataframe_shape": df.shape})
            raise
    
    def _get_current_memory_mb(self) -> float:
        """获取当前进程内存使用量（MB）"""
        try:
            process = psutil.Process()
            return process.memory_info().rss / 1024 / 1024
        except Exception:
            return 0.0
    
    def get_last_stats(self) -> Optional[ChunkProcessingStats]:
        """获取最后一次处理的统计信息"""
        with self._lock:
            return self._processing_stats


class StreamingDataAggregator:
    """流式数据聚合器，实时聚合数据，最小内存使用"""
    
    def __init__(self, window_size: int = 1000, aggregation_functions: Optional[Dict[str, str]] = None):
        self.window_size = window_size
        self.aggregation_functions = aggregation_functions or {
            'price': 'mean',
            'volume': 'sum',
            'high': 'max',
            'low': 'min'
        }
        
        self.logger = get_logger(f"{__name__}.StreamingDataAggregator")
        self.perf_logger = get_global_performance_logger()
        
        self._buffers: Dict[str, List[float]] = {}
        self._aggregated_results: Dict[str, List[float]] = {}
        self._lock = threading.RLock()
        self._total_processed = 0
    
    def add_data_point(self, data: Dict[str, float]) -> Optional[Dict[str, float]]:
        """添加数据点，返回聚合结果（如果窗口已满）"""
        with self._lock:
            # 添加数据到缓冲区
            for key, value in data.items():
                if key not in self._buffers:
                    self._buffers[key] = []
                    self._aggregated_results[key] = []
                
                self._buffers[key].append(value)
            
            self._total_processed += 1
            
            # 检查是否需要聚合
            if len(next(iter(self._buffers.values()))) >= self.window_size:
                return self._aggregate_and_reset()
            
            return None
    
    def _aggregate_and_reset(self) -> Dict[str, float]:
        """执行聚合并重置缓冲区"""
        results = {}
        
        for key, values in self._buffers.items():
            agg_func = self.aggregation_functions.get(key, 'mean')
            
            if agg_func == 'mean':
                result = np.mean(values)
            elif agg_func == 'sum':
                result = np.sum(values)
            elif agg_func == 'max':
                result = np.max(values)
            elif agg_func == 'min':
                result = np.min(values)
            elif agg_func == 'std':
                result = np.std(values)
            else:
                result = np.mean(values)  # 默认使用均值
            
            results[key] = result
            self._aggregated_results[key].append(result)
            
            # 重置缓冲区
            self._buffers[key] = []
        
        return results
    
    def get_aggregated_history(self, key: str, last_n: Optional[int] = None) -> List[float]:
        """获取聚合历史数据"""
        with self._lock:
            if key not in self._aggregated_results:
                return []
            
            history = self._aggregated_results[key]
            if last_n is not None:
                return history[-last_n:]
            return history.copy()
    
    def force_aggregate(self) -> Optional[Dict[str, float]]:
        """强制聚合当前缓冲区数据"""
        with self._lock:
            if not self._buffers or not any(self._buffers.values()):
                return None
            
            return self._aggregate_and_reset()
    
    def get_stats(self) -> Dict[str, Any]:
        """获取聚合器统计信息"""
        with self._lock:
            return {
                'total_processed': self._total_processed,
                'current_buffer_sizes': {k: len(v) for k, v in self._buffers.items()},
                'aggregated_counts': {k: len(v) for k, v in self._aggregated_results.items()},
                'window_size': self.window_size
            }


class MemoryMappedFileHandler:
    """内存映射文件处理器，用于大文件的高效访问"""
    
    def __init__(self, file_path: Union[str, Path], mode: str = 'r'):
        self.file_path = Path(file_path)
        self.mode = mode
        self.logger = get_logger(f"{__name__}.MemoryMappedFileHandler")
        
        self._file_handle = None
        self._mmap_handle = None
        self._lock = threading.RLock()
    
    def __enter__(self):
        """上下文管理器入口"""
        self.open()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.close()
    
    def open(self) -> None:
        """打开文件和内存映射"""
        try:
            with self._lock:
                if self._file_handle is not None:
                    return  # 已经打开
                
                # 打开文件
                if 'w' in self.mode or 'a' in self.mode:
                    self._file_handle = open(self.file_path, 'r+b')
                else:
                    self._file_handle = open(self.file_path, 'rb')
                
                # 创建内存映射
                if 'w' in self.mode or 'a' in self.mode:
                    self._mmap_handle = mmap.mmap(self._file_handle.fileno(), 0)
                else:
                    self._mmap_handle = mmap.mmap(self._file_handle.fileno(), 0, access=mmap.ACCESS_READ)
                
                self.logger.debug(f"Opened memory-mapped file: {self.file_path}")
                
        except Exception as e:
            self.logger.error(f"Failed to open memory-mapped file {self.file_path}: {e}")
            self.close()
            raise
    
    def close(self) -> None:
        """关闭文件和内存映射"""
        with self._lock:
            if self._mmap_handle:
                self._mmap_handle.close()
                self._mmap_handle = None
            
            if self._file_handle:
                self._file_handle.close()
                self._file_handle = None
    
    def read_chunk(self, offset: int, size: int) -> bytes:
        """读取指定位置和大小的数据块"""
        with self._lock:
            if self._mmap_handle is None:
                raise RuntimeError("File not opened")
            
            if offset + size > len(self._mmap_handle):
                size = len(self._mmap_handle) - offset
            
            return self._mmap_handle[offset:offset + size]
    
    def write_chunk(self, offset: int, data: bytes) -> None:
        """写入数据到指定位置"""
        with self._lock:
            if self._mmap_handle is None:
                raise RuntimeError("File not opened")
            
            if 'w' not in self.mode and 'a' not in self.mode:
                raise RuntimeError("File opened in read-only mode")
            
            self._mmap_handle[offset:offset + len(data)] = data
    
    def get_size(self) -> int:
        """获取文件大小"""
        with self._lock:
            if self._mmap_handle is None:
                raise RuntimeError("File not opened")
            
            return len(self._mmap_handle)
    
    def search_pattern(self, pattern: bytes, start: int = 0) -> List[int]:
        """搜索模式在文件中的位置"""
        with self._lock:
            if self._mmap_handle is None:
                raise RuntimeError("File not opened")
            
            positions = []
            pos = start
            
            while True:
                pos = self._mmap_handle.find(pattern, pos)
                if pos == -1:
                    break
                positions.append(pos)
                pos += 1
            
            return positions


class DataCompressionManager:
    """数据压缩管理器，智能选择压缩算法"""
    
    def __init__(self):
        self.logger = get_logger(f"{__name__}.DataCompressionManager")
        self.perf_logger = get_global_performance_logger()
        
        # 压缩算法配置
        self._algorithms = {
            'lz4': {
                'compress': lambda data: lz4.frame.compress(data, compression_level=4),
                'decompress': lz4.frame.decompress,
                'speed': 'fast',
                'ratio': 'medium'
            },
            'zstd': {
                'compress': lambda data: zstd.compress(data, level=3),
                'decompress': zstd.decompress,
                'speed': 'medium',
                'ratio': 'high'
            }
        }
    
    def compress_data(self, data: bytes, algorithm: str = 'auto') -> Tuple[bytes, str, Dict[str, Any]]:
        """压缩数据，返回压缩后的数据、使用的算法和统计信息"""
        try:
            if algorithm == 'auto':
                algorithm = self._select_best_algorithm(data)
            
            if algorithm not in self._algorithms:
                raise ValueError(f"Unsupported compression algorithm: {algorithm}")
            
            start_time = time.time()
            compressed_data = self._algorithms[algorithm]['compress'](data)
            compression_time = time.time() - start_time
            
            stats = {
                'original_size': len(data),
                'compressed_size': len(compressed_data),
                'compression_ratio': len(data) / len(compressed_data),
                'compression_time_ms': compression_time * 1000,
                'algorithm': algorithm
            }
            
            self.logger.debug(f"Compressed {len(data)} bytes to {len(compressed_data)} bytes "
                            f"using {algorithm} (ratio: {stats['compression_ratio']:.2f})")
            
            return compressed_data, algorithm, stats
            
        except Exception as e:
            self.logger.error(f"Compression failed: {e}")
            raise
    
    def decompress_data(self, compressed_data: bytes, algorithm: str) -> bytes:
        """解压缩数据"""
        try:
            if algorithm not in self._algorithms:
                raise ValueError(f"Unsupported compression algorithm: {algorithm}")
            
            return self._algorithms[algorithm]['decompress'](compressed_data)
            
        except Exception as e:
            self.logger.error(f"Decompression failed: {e}")
            raise
    
    def _select_best_algorithm(self, data: bytes) -> str:
        """根据数据特征选择最佳压缩算法"""
        # 简单的启发式选择
        if len(data) < 1024:  # 小数据使用快速算法
            return 'lz4'
        elif len(data) > 1024 * 1024:  # 大数据使用高压缩比算法
            return 'zstd'
        else:
            return 'lz4'  # 默认使用LZ4
    
    def benchmark_algorithms(self, test_data: bytes) -> Dict[str, Dict[str, Any]]:
        """对测试数据进行算法基准测试"""
        results = {}
        
        for algorithm in self._algorithms.keys():
            try:
                compressed_data, _, stats = self.compress_data(test_data, algorithm)
                
                # 测试解压缩时间
                start_time = time.time()
                decompressed_data = self.decompress_data(compressed_data, algorithm)
                decompression_time = (time.time() - start_time) * 1000
                
                # 验证数据完整性
                data_integrity = test_data == decompressed_data
                
                results[algorithm] = {
                    **stats,
                    'decompression_time_ms': decompression_time,
                    'total_time_ms': stats['compression_time_ms'] + decompression_time,
                    'data_integrity': data_integrity
                }
                
            except Exception as e:
                results[algorithm] = {'error': str(e)}
        
        return results


class GarbageCollectionOptimizer:
    """垃圾回收优化器，用于数据密集型操作"""
    
    def __init__(self):
        self.logger = get_logger(f"{__name__}.GarbageCollectionOptimizer")
        self._original_thresholds = None
        self._gc_disabled = False
        self._lock = threading.RLock()
    
    @contextmanager
    def optimized_gc_context(self, disable_gc: bool = False, 
                           custom_thresholds: Optional[Tuple[int, int, int]] = None):
        """优化的GC上下文管理器"""
        with self._lock:
            # 保存原始设置
            self._original_thresholds = gc.get_threshold()
            original_gc_enabled = gc.isenabled()
            
            try:
                if disable_gc:
                    gc.disable()
                    self._gc_disabled = True
                    self.logger.debug("Disabled garbage collection for optimization")
                
                if custom_thresholds:
                    gc.set_threshold(*custom_thresholds)
                    self.logger.debug(f"Set custom GC thresholds: {custom_thresholds}")
                
                yield
                
            finally:
                # 恢复原始设置
                if self._gc_disabled:
                    if original_gc_enabled:
                        gc.enable()
                    self._gc_disabled = False
                
                if self._original_thresholds:
                    gc.set_threshold(*self._original_thresholds)
                
                # 执行一次完整的垃圾回收
                collected = gc.collect()
                if collected > 0:
                    self.logger.debug(f"Collected {collected} objects during cleanup")
    
    def force_full_gc(self) -> Dict[str, int]:
        """强制执行完整的垃圾回收"""
        with self._lock:
            start_time = time.time()
            
            # 执行所有代的垃圾回收
            collected = [0, 0, 0]
            for generation in range(3):
                collected[generation] = gc.collect(generation)
            
            gc_time = (time.time() - start_time) * 1000
            
            stats = {
                'generation_0_collected': collected[0],
                'generation_1_collected': collected[1],
                'generation_2_collected': collected[2],
                'total_collected': sum(collected),
                'gc_time_ms': gc_time
            }
            
            self.logger.debug(f"Full GC completed: {stats}")
            return stats
    
    def get_gc_stats(self) -> Dict[str, Any]:
        """获取垃圾回收统计信息"""
        stats = gc.get_stats()
        
        return {
            'gc_enabled': gc.isenabled(),
            'current_thresholds': gc.get_threshold(),
            'generation_stats': stats,
            'total_collections': sum(stat['collections'] for stat in stats),
            'object_count': len(gc.get_objects())
        }


# 全局实例
_global_gc_optimizer: Optional[GarbageCollectionOptimizer] = None


def get_global_gc_optimizer() -> GarbageCollectionOptimizer:
    """获取全局垃圾回收优化器实例"""
    global _global_gc_optimizer
    
    if _global_gc_optimizer is None:
        _global_gc_optimizer = GarbageCollectionOptimizer()
    
    return _global_gc_optimizer


# 便捷函数
def create_chunked_processor(chunk_size: int = 10000, 
                           max_memory_mb: int = 1000) -> ChunkedDataProcessor:
    """创建分块数据处理器"""
    return ChunkedDataProcessor(chunk_size, max_memory_mb)


def create_streaming_aggregator(window_size: int = 1000,
                              agg_functions: Optional[Dict[str, str]] = None) -> StreamingDataAggregator:
    """创建流式数据聚合器"""
    return StreamingDataAggregator(window_size, agg_functions)


def create_compression_manager() -> DataCompressionManager:
    """创建数据压缩管理器"""
    return DataCompressionManager()


class LargeDatasetProcessor:
    """
    大数据集处理器主类
    
    这是大数据集处理系统的主要接口类，提供了完整的大数据集处理功能，
    包括分块处理、流式聚合、内存映射文件操作、数据压缩和垃圾回收优化。
    
    主要功能：
    - 分块数据处理
    - 流式数据聚合
    - 内存映射文件操作
    - 智能数据压缩
    - 垃圾回收优化
    
    使用示例：
        processor = LargeDatasetProcessor()
        await processor.initialize()
        stats = processor.process_large_file("data.csv", processing_func)
        aggregated = processor.aggregate_streaming_data(data_stream)
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化大数据集处理器
        
        Args:
            config: 配置字典，包含分块大小、内存限制等设置
        """
        self.config = config or {}
        self.logger = get_logger(f"{__name__}.LargeDatasetProcessor")
        self.perf_logger = get_global_performance_logger()
        self.error_handler = get_global_error_handler()
        
        # 初始化组件
        chunk_size = self.config.get("chunk_size", 10000)
        max_memory_mb = self.config.get("max_memory_mb", 1000)
        
        self.chunked_processor = ChunkedDataProcessor(chunk_size, max_memory_mb)
        
        window_size = self.config.get("window_size", 1000)
        agg_functions = self.config.get("aggregation_functions")
        self.streaming_aggregator = StreamingDataAggregator(window_size, agg_functions)
        
        self.compression_manager = DataCompressionManager()
        self.gc_optimizer = GarbageCollectionOptimizer()
        
        # 处理统计
        self._total_files_processed = 0
        self._total_data_processed_mb = 0
        self._lock = threading.RLock()
        
        logger.info("LargeDatasetProcessor initialized")
    
    async def initialize(self) -> bool:
        """
        初始化大数据集处理器
        
        Returns:
            是否初始化成功
        """
        try:
            self.logger.info("初始化大数据集处理器")
            
            # 配置垃圾回收优化器
            gc_config = self.config.get("garbage_collection", {})
            if "gc_threshold" in gc_config:
                self.gc_optimizer.set_gc_threshold(gc_config["gc_threshold"])
            
            # 启用内存监控
            if self.config.get("enable_memory_monitoring", True):
                self.gc_optimizer.enable_memory_monitoring()
            
            self.logger.info("大数据集处理器初始化完成")
            return True
            
        except Exception as e:
            self.logger.error(f"大数据集处理器初始化失败: {e}")
            return False
    
    def process_large_file(self, file_path: Union[str, Path],
                          processor_func: Callable[[pd.DataFrame], pd.DataFrame],
                          output_path: Optional[Union[str, Path]] = None,
                          use_compression: bool = False) -> ChunkProcessingStats:
        """
        处理大文件
        
        Args:
            file_path: 输入文件路径
            processor_func: 处理函数
            output_path: 输出文件路径（可选）
            use_compression: 是否使用压缩
            
        Returns:
            处理统计信息
        """
        try:
            self.logger.info(f"开始处理大文件: {file_path}")
            
            # 使用垃圾回收优化上下文
            with self.gc_optimizer.optimized_processing():
                stats = self.chunked_processor.process_file(
                    file_path, processor_func, output_path
                )
            
            # 如果需要压缩输出文件
            if use_compression and output_path:
                self._compress_output_file(output_path)
            
            # 更新统计信息
            with self._lock:
                self._total_files_processed += 1
                file_size_mb = Path(file_path).stat().st_size / 1024 / 1024
                self._total_data_processed_mb += file_size_mb
            
            self.logger.info(f"大文件处理完成: {stats.to_dict()}")
            return stats
            
        except Exception as e:
            self.logger.error(f"大文件处理失败: {e}")
            # 注意：这里不能使用await，因为这不是异步函数
            # self.error_handler.handle_error(e, context={
            #     "file_path": str(file_path),
            #     "use_compression": use_compression
            # })
            raise
    
    def process_dataframe_chunks(self, df: pd.DataFrame,
                               processor_func: Callable[[pd.DataFrame], pd.DataFrame]) -> pd.DataFrame:
        """
        分块处理DataFrame
        
        Args:
            df: 输入DataFrame
            processor_func: 处理函数
            
        Returns:
            处理后的DataFrame
        """
        try:
            with self.gc_optimizer.optimized_processing():
                return self.chunked_processor.process_dataframe(df, processor_func)
                
        except Exception as e:
            self.logger.error(f"DataFrame分块处理失败: {e}")
            raise
    
    def aggregate_streaming_data(self, data_stream: Iterator[Dict[str, float]]) -> List[Dict[str, float]]:
        """
        聚合流式数据
        
        Args:
            data_stream: 数据流迭代器
            
        Returns:
            聚合结果列表
        """
        try:
            self.logger.info("开始流式数据聚合")
            aggregated_results = []
            
            for data_point in data_stream:
                result = self.streaming_aggregator.add_data_point(data_point)
                if result is not None:
                    aggregated_results.append(result)
            
            # 强制聚合剩余数据
            final_result = self.streaming_aggregator.force_aggregate()
            if final_result is not None:
                aggregated_results.append(final_result)
            
            self.logger.info(f"流式数据聚合完成，生成 {len(aggregated_results)} 个聚合结果")
            return aggregated_results
            
        except Exception as e:
            self.logger.error(f"流式数据聚合失败: {e}")
            raise
    
    def process_memory_mapped_file(self, file_path: Union[str, Path],
                                 chunk_processor: Callable[[bytes], bytes],
                                 chunk_size: int = 1024 * 1024) -> None:
        """
        使用内存映射处理大文件
        
        Args:
            file_path: 文件路径
            chunk_processor: 数据块处理函数
            chunk_size: 数据块大小
        """
        try:
            self.logger.info(f"开始内存映射文件处理: {file_path}")
            
            with MemoryMappedFileHandler(file_path, 'r') as handler:
                file_size = handler.get_size()
                processed_bytes = 0
                
                for offset in range(0, file_size, chunk_size):
                    current_chunk_size = min(chunk_size, file_size - offset)
                    chunk_data = handler.read_chunk(offset, current_chunk_size)
                    
                    # 处理数据块
                    processed_chunk = chunk_processor(chunk_data)
                    processed_bytes += len(processed_chunk)
                    
                    if offset % (chunk_size * 100) == 0:
                        progress = (offset / file_size) * 100
                        self.logger.debug(f"内存映射处理进度: {progress:.1f}%")
            
            self.logger.info(f"内存映射文件处理完成，处理了 {processed_bytes} 字节")
            
        except Exception as e:
            self.logger.error(f"内存映射文件处理失败: {e}")
            raise
    
    def compress_data(self, data: bytes, algorithm: str = 'auto') -> Tuple[bytes, str, Dict[str, Any]]:
        """
        压缩数据
        
        Args:
            data: 要压缩的数据
            algorithm: 压缩算法
            
        Returns:
            压缩后的数据、使用的算法和统计信息
        """
        return self.compression_manager.compress_data(data, algorithm)
    
    def decompress_data(self, compressed_data: bytes, algorithm: str) -> bytes:
        """
        解压缩数据
        
        Args:
            compressed_data: 压缩的数据
            algorithm: 压缩算法
            
        Returns:
            解压缩后的数据
        """
        return self.compression_manager.decompress_data(compressed_data, algorithm)
    
    def _compress_output_file(self, file_path: Union[str, Path]) -> None:
        """压缩输出文件"""
        try:
            file_path = Path(file_path)
            
            # 读取文件数据
            with open(file_path, 'rb') as f:
                file_data = f.read()
            
            # 压缩数据
            compressed_data, algorithm, stats = self.compress_data(file_data)
            
            # 保存压缩文件
            compressed_path = file_path.with_suffix(f"{file_path.suffix}.{algorithm}")
            with open(compressed_path, 'wb') as f:
                f.write(compressed_data)
            
            # 删除原文件
            file_path.unlink()
            
            self.logger.info(f"文件压缩完成: {file_path} -> {compressed_path}, "
                           f"压缩比: {stats['compression_ratio']:.2f}")
            
        except Exception as e:
            self.logger.error(f"文件压缩失败: {e}")
    
    def get_processing_statistics(self) -> Dict[str, Any]:
        """
        获取处理统计信息
        
        Returns:
            统计信息字典
        """
        try:
            with self._lock:
                chunked_stats = self.chunked_processor.get_last_stats()
                streaming_stats = self.streaming_aggregator.get_stats()
                gc_stats = self.gc_optimizer.get_gc_stats()
                
                return {
                    "total_files_processed": self._total_files_processed,
                    "total_data_processed_mb": self._total_data_processed_mb,
                    "last_chunked_processing": chunked_stats.to_dict() if chunked_stats else None,
                    "streaming_aggregation": streaming_stats,
                    "garbage_collection": gc_stats,
                    "memory_usage": self._get_current_memory_usage()
                }
                
        except Exception as e:
            self.logger.error(f"获取处理统计失败: {e}")
            return {"error": "Failed to get processing statistics"}
    
    def _get_current_memory_usage(self) -> Dict[str, float]:
        """获取当前内存使用情况"""
        try:
            process = psutil.Process()
            memory_info = process.memory_info()
            
            return {
                "rss_mb": memory_info.rss / 1024 / 1024,
                "vms_mb": memory_info.vms / 1024 / 1024,
                "percent": process.memory_percent()
            }
            
        except Exception:
            return {"error": "Failed to get memory usage"}
    
    def optimize_memory_usage(self) -> Dict[str, Any]:
        """
        优化内存使用
        
        Returns:
            优化结果字典
        """
        try:
            self.logger.info("开始内存使用优化")
            
            # 强制垃圾回收
            collected_objects = self.gc_optimizer.force_gc()
            
            # 获取优化前后的内存使用
            memory_before = self._get_current_memory_usage()
            
            # 清理聚合器缓冲区
            self.streaming_aggregator._buffers.clear()
            
            # 再次垃圾回收
            additional_collected = self.gc_optimizer.force_gc()
            
            memory_after = self._get_current_memory_usage()
            
            optimization_result = {
                "collected_objects": collected_objects + additional_collected,
                "memory_before_mb": memory_before.get("rss_mb", 0),
                "memory_after_mb": memory_after.get("rss_mb", 0),
                "memory_freed_mb": memory_before.get("rss_mb", 0) - memory_after.get("rss_mb", 0),
                "optimization_actions": [
                    "强制垃圾回收",
                    "清理聚合器缓冲区",
                    "二次垃圾回收"
                ]
            }
            
            self.logger.info(f"内存优化完成: 释放 {optimization_result['memory_freed_mb']:.1f}MB")
            return optimization_result
            
        except Exception as e:
            self.logger.error(f"内存优化失败: {e}")
            return {"error": "Memory optimization failed"}
    
    def export_processing_report(self) -> Dict[str, Any]:
        """
        导出处理报告
        
        Returns:
            处理报告字典
        """
        try:
            report = {
                "timestamp": datetime.now().isoformat(),
                "configuration": self.config,
                "processing_statistics": self.get_processing_statistics(),
                "component_status": {
                    "chunked_processor": "active",
                    "streaming_aggregator": "active",
                    "compression_manager": "active",
                    "gc_optimizer": "active"
                },
                "performance_recommendations": self._generate_performance_recommendations()
            }
            
            self.logger.info("处理报告导出完成")
            return report
            
        except Exception as e:
            self.logger.error(f"导出处理报告失败: {e}")
            return {"error": "Failed to export processing report"}
    
    def _generate_performance_recommendations(self) -> List[str]:
        """生成性能优化建议"""
        try:
            recommendations = []
            stats = self.get_processing_statistics()
            
            if isinstance(stats, dict) and "error" not in stats:
                # 基于内存使用的建议
                memory_usage = stats.get("memory_usage", {})
                memory_percent = memory_usage.get("percent", 0)
                
                if memory_percent > 80:
                    recommendations.append("内存使用率较高，建议增加分块大小或启用压缩")
                
                # 基于垃圾回收的建议
                gc_stats = stats.get("garbage_collection", {})
                if isinstance(gc_stats, dict):
                    gc_frequency = gc_stats.get("collections_per_minute", 0)
                    if gc_frequency > 10:
                        recommendations.append("垃圾回收频率较高，建议优化对象创建和销毁")
                
                # 基于处理统计的建议
                total_data_mb = stats.get("total_data_processed_mb", 0)
                if total_data_mb > 1000:  # 超过1GB
                    recommendations.append("处理的数据量较大，建议启用内存映射文件处理")
            
            if not recommendations:
                recommendations.append("当前处理性能良好，无需特殊优化")
            
            return recommendations
            
        except Exception as e:
            self.logger.error(f"生成性能建议失败: {e}")
            return ["无法生成性能建议"]
    
    def shutdown(self) -> None:
        """关闭大数据集处理器"""
        try:
            self.logger.info("关闭大数据集处理器")
            
            # 强制聚合剩余数据
            self.streaming_aggregator.force_aggregate()
            
            # 最终垃圾回收
            self.gc_optimizer.force_gc()
            
            self.logger.info("大数据集处理器已关闭")
            
        except Exception as e:
            self.logger.error(f"关闭大数据集处理器失败: {e}")


# 全局大数据集处理器实例
_global_dataset_processor: Optional[LargeDatasetProcessor] = None


def get_global_dataset_processor() -> LargeDatasetProcessor:
    """获取全局大数据集处理器实例"""
    global _global_dataset_processor
    
    if _global_dataset_processor is None:
        _global_dataset_processor = LargeDatasetProcessor()
    
    return _global_dataset_processor


# 模块级别的日志器
module_logger = get_logger(__name__)