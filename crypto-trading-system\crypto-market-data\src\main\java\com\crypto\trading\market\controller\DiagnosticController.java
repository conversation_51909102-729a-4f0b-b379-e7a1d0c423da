package com.crypto.trading.market.controller;

import com.crypto.trading.market.monitor.DataFlowDiagnosticService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 诊断控制器
 * 提供数据流诊断和监控接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@RestController
@RequestMapping("/api/diagnostic")
public class DiagnosticController {

    private static final Logger log = LoggerFactory.getLogger(DiagnosticController.class);

    @Autowired
    private DataFlowDiagnosticService diagnosticService;

    /**
     * 执行完整的数据流诊断
     * 
     * @return 诊断报告
     */
    @GetMapping("/dataflow")
    public ResponseEntity<Map<String, Object>> diagnoseDataFlow() {
        try {
            log.info("收到数据流诊断请求");
            Map<String, Object> report = diagnosticService.performFullDiagnosis();
            return ResponseEntity.ok(report);
        } catch (Exception e) {
            log.error("数据流诊断失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError()
                .body(Map.of("error", "诊断失败: " + e.getMessage()));
        }
    }

    /**
     * 重置所有统计信息
     * 
     * @return 操作结果
     */
    @PostMapping("/reset-statistics")
    public ResponseEntity<Map<String, Object>> resetStatistics() {
        try {
            log.info("收到重置统计信息请求");
            diagnosticService.resetAllStatistics();
            return ResponseEntity.ok(Map.of("message", "统计信息已重置"));
        } catch (Exception e) {
            log.error("重置统计信息失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError()
                .body(Map.of("error", "重置失败: " + e.getMessage()));
        }
    }

    /**
     * 健康检查接口
     * 
     * @return 健康状态
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> healthCheck() {
        return ResponseEntity.ok(Map.of(
            "status", "UP",
            "service", "crypto-market-data",
            "timestamp", System.currentTimeMillis()
        ));
    }
}
