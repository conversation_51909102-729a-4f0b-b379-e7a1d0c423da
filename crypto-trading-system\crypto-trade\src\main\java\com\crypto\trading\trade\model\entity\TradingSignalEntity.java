package com.crypto.trading.trade.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.crypto.trading.trade.model.avro.OrderType;
import com.crypto.trading.trade.model.avro.SignalType;
import com.crypto.trading.trade.model.avro.TimeInForce;

import java.math.BigDecimal;

/**
 * 交易信号实体类
 * 
 * <p>对应数据库表t_trading_signal，存储从Kafka接收到的交易信号</p>
 */
@TableName("t_trading_signal")
public class TradingSignalEntity {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 信号唯一ID
     */
    private String signalId;

    /**
     * 交易对，如BTCUSDT
     */
    private String symbol;

    /**
     * 策略名称
     */
    private String strategy;

    /**
     * 信号类型 BUY/SELL/HOLD等
     */
    private String signalType;

    /**
     * 信号置信度 0-1
     */
    private BigDecimal confidence;

    /**
     * 目标价格
     */
    private BigDecimal price;

    /**
     * 建议交易数量
     */
    private BigDecimal quantity;

    /**
     * 止损价格
     */
    private BigDecimal stopLoss;

    /**
     * 止盈价格
     */
    private BigDecimal takeProfit;

    /**
     * 订单类型
     */
    private String orderType;

    /**
     * 订单有效期
     */
    private String timeInForce;

    /**
     * 杠杆倍数
     */
    private Integer leverageLevel;

    /**
     * 额外参数（JSON格式）
     */
    private String additionalParams;

    /**
     * 是否已处理 0:否 1:是
     */
    private Boolean processed;

    /**
     * 处理时间（毫秒时间戳）
     */
    private Long processedTime;

    /**
     * 处理结果
     */
    private String processResult;

    /**
     * 信号生成时间（毫秒时间戳）
     */
    private Long generatedTime;

    /**
     * 创建时间（毫秒时间戳）
     */
    private Long createdTime;

    /**
     * 是否删除 0:否 1:是
     */
    @TableLogic
    private Boolean isDeleted;

    /**
     * 默认构造函数
     */
    public TradingSignalEntity() {
    }

    /**
     * 从Avro对象创建实体
     *
     * @param avroSignal Avro格式交易信号
     */
    public TradingSignalEntity(com.crypto.trading.trade.model.avro.TradingSignal avroSignal) {
        this.signalId = avroSignal.getId();
        this.symbol = avroSignal.getSymbol();
        this.strategy = avroSignal.getStrategy();
        this.signalType = avroSignal.getSignalType().toString();
        this.confidence = BigDecimal.valueOf(avroSignal.getConfidence());
        this.price = avroSignal.getPrice() != null ? BigDecimal.valueOf(avroSignal.getPrice()) : null;
        this.quantity = avroSignal.getSuggestedQuantity() != null ? BigDecimal.valueOf(avroSignal.getSuggestedQuantity()) : null;
        this.stopLoss = avroSignal.getStopLoss() != null ? BigDecimal.valueOf(avroSignal.getStopLoss()) : null;
        this.takeProfit = avroSignal.getTakeProfit() != null ? BigDecimal.valueOf(avroSignal.getTakeProfit()) : null;
        this.orderType = avroSignal.getOrderType().toString();
        this.timeInForce = avroSignal.getTimeInForce().toString();
        this.leverageLevel = avroSignal.getLeverageLevel();
        this.additionalParams = avroSignal.getAdditionalParams() != null ? avroSignal.getAdditionalParams().toString() : null;
        this.processed = false;
        this.generatedTime = avroSignal.getGeneratedTime();
        this.createdTime = System.currentTimeMillis();
        this.isDeleted = false;
    }

    // Getters and Setters

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getSignalId() {
        return signalId;
    }

    public void setSignalId(String signalId) {
        this.signalId = signalId;
    }

    public String getSymbol() {
        return symbol;
    }

    public void setSymbol(String symbol) {
        this.symbol = symbol;
    }

    public String getStrategy() {
        return strategy;
    }

    public void setStrategy(String strategy) {
        this.strategy = strategy;
    }

    public String getSignalType() {
        return signalType;
    }

    public void setSignalType(String signalType) {
        this.signalType = signalType;
    }

    public BigDecimal getConfidence() {
        return confidence;
    }

    public void setConfidence(BigDecimal confidence) {
        this.confidence = confidence;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    public BigDecimal getStopLoss() {
        return stopLoss;
    }

    public void setStopLoss(BigDecimal stopLoss) {
        this.stopLoss = stopLoss;
    }

    public BigDecimal getTakeProfit() {
        return takeProfit;
    }

    public void setTakeProfit(BigDecimal takeProfit) {
        this.takeProfit = takeProfit;
    }

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public String getTimeInForce() {
        return timeInForce;
    }

    public void setTimeInForce(String timeInForce) {
        this.timeInForce = timeInForce;
    }

    public Integer getLeverageLevel() {
        return leverageLevel;
    }

    public void setLeverageLevel(Integer leverageLevel) {
        this.leverageLevel = leverageLevel;
    }

    public String getAdditionalParams() {
        return additionalParams;
    }

    public void setAdditionalParams(String additionalParams) {
        this.additionalParams = additionalParams;
    }

    public Boolean getProcessed() {
        return processed;
    }

    public void setProcessed(Boolean processed) {
        this.processed = processed;
    }

    public Long getProcessedTime() {
        return processedTime;
    }

    public void setProcessedTime(Long processedTime) {
        this.processedTime = processedTime;
    }

    public String getProcessResult() {
        return processResult;
    }

    public void setProcessResult(String processResult) {
        this.processResult = processResult;
    }

    public Long getGeneratedTime() {
        return generatedTime;
    }

    public void setGeneratedTime(Long generatedTime) {
        this.generatedTime = generatedTime;
    }

    public Long getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(Long createdTime) {
        this.createdTime = createdTime;
    }

    public Boolean getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    /**
     * 获取Avro信号类型枚举
     *
     * @return 信号类型枚举
     */
    public SignalType getSignalTypeEnum() {
        return SignalType.valueOf(this.signalType);
    }

    /**
     * 获取Avro订单类型枚举
     *
     * @return 订单类型枚举
     */
    public OrderType getOrderTypeEnum() {
        return OrderType.valueOf(this.orderType);
    }

    /**
     * 获取Avro订单有效期枚举
     *
     * @return 订单有效期枚举
     */
    public TimeInForce getTimeInForceEnum() {
        return TimeInForce.valueOf(this.timeInForce);
    }

    @Override
    public String toString() {
        return "TradingSignalEntity{" +
                "id=" + id +
                ", signalId='" + signalId + '\'' +
                ", symbol='" + symbol + '\'' +
                ", strategy='" + strategy + '\'' +
                ", signalType='" + signalType + '\'' +
                ", confidence=" + confidence +
                ", price=" + price +
                ", quantity=" + quantity +
                ", stopLoss=" + stopLoss +
                ", takeProfit=" + takeProfit +
                ", orderType='" + orderType + '\'' +
                ", timeInForce='" + timeInForce + '\'' +
                ", leverageLevel=" + leverageLevel +
                ", processed=" + processed +
                ", generatedTime=" + generatedTime +
                ", createdTime=" + createdTime +
                '}';
    }
}