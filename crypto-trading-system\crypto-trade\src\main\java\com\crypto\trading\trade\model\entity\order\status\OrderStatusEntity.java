package com.crypto.trading.trade.model.entity.order.status;

import com.baomidou.mybatisplus.annotation.*;

/**
 * 订单状态实体类
 * 
 * <p>对应数据库表t_order_status，记录订单状态变更历史</p>
 */
@TableName("t_order_status")
public class OrderStatusEntity {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 订单ID
     */
    private String orderId;

    /**
     * 客户端订单ID
     */
    private String clientOrderId;

    /**
     * 旧状态
     */
    private String oldStatus;

    /**
     * 新状态
     */
    private String newStatus;

    /**
     * 状态变更原因
     */
    private String reason;

    /**
     * 详细信息（JSON格式）
     */
    private String details;

    /**
     * 变更时间（毫秒时间戳）
     */
    private Long updateTime;

    /**
     * 创建时间（毫秒时间戳）
     */
    private Long createdTime;

    /**
     * 默认构造函数
     */
    public OrderStatusEntity() {
    }

    // Getters and Setters

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getClientOrderId() {
        return clientOrderId;
    }

    public void setClientOrderId(String clientOrderId) {
        this.clientOrderId = clientOrderId;
    }

    public String getOldStatus() {
        return oldStatus;
    }

    public void setOldStatus(String oldStatus) {
        this.oldStatus = oldStatus;
    }

    public String getNewStatus() {
        return newStatus;
    }

    public void setNewStatus(String newStatus) {
        this.newStatus = newStatus;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getDetails() {
        return details;
    }

    public void setDetails(String details) {
        this.details = details;
    }

    public Long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    public Long getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(Long createdTime) {
        this.createdTime = createdTime;
    }

    @Override
    public String toString() {
        return "OrderStatusEntity{" +
                "id=" + id +
                ", orderId='" + orderId + '\'' +
                ", clientOrderId='" + clientOrderId + '\'' +
                ", oldStatus='" + oldStatus + '\'' +
                ", newStatus='" + newStatus + '\'' +
                ", reason='" + reason + '\'' +
                ", updateTime=" + updateTime +
                '}';
    }

    /**
     * 构建者模式内部类
     */
    public static class Builder {
        private final OrderStatusEntity status;

        public Builder() {
            status = new OrderStatusEntity();
            status.setCreatedTime(System.currentTimeMillis());
            status.setUpdateTime(System.currentTimeMillis());
        }

        public Builder orderId(String orderId) {
            status.setOrderId(orderId);
            return this;
        }

        public Builder clientOrderId(String clientOrderId) {
            status.setClientOrderId(clientOrderId);
            return this;
        }

        public Builder oldStatus(String oldStatus) {
            status.setOldStatus(oldStatus);
            return this;
        }

        public Builder newStatus(String newStatus) {
            status.setNewStatus(newStatus);
            return this;
        }

        public Builder reason(String reason) {
            status.setReason(reason);
            return this;
        }

        public Builder details(String details) {
            status.setDetails(details);
            return this;
        }

        public Builder updateTime(Long updateTime) {
            status.setUpdateTime(updateTime);
            return this;
        }

        public OrderStatusEntity build() {
            return status;
        }
    }
}