package com.crypto.trading.bootstrap.config;

import com.crypto.trading.common.config.AbstractThreadPoolConfig;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

/**
 * Bootstrap模块线程配置类
 * 配置虚拟线程池和普通线程池
 */
@Configuration
@EnableAsync
@EnableScheduling
public class ThreadConfig extends AbstractThreadPoolConfig {

    private final AppConfig appConfig;
    
    /**
     * 构造函数，注入AppConfig
     * 
     * @param appConfig 应用配置
     */
    public ThreadConfig(AppConfig appConfig) {
        this.appConfig = appConfig;
    }

    /**
     * 创建虚拟线程任务执行器Bean
     * 使用AbstractThreadPoolConfig提供的标准方法
     * 
     * @return 虚拟线程任务执行器
     */
    @Bean
    @Override
    public AsyncTaskExecutor virtualThreadTaskExecutor() {
        return super.virtualThreadTaskExecutor();
    }
    
    /**
     * 创建平台线程池任务执行器Bean
     * 使用AbstractThreadPoolConfig提供的标准方法
     * 
     * @return 平台线程池任务执行器
     */
    @Bean
    @Override
    public ThreadPoolTaskExecutor platformThreadPoolTaskExecutor() {
        return super.platformThreadPoolTaskExecutor();
    }
    
    /**
     * 获取模块名称
     * 
     * @return 模块名称
     */
    @Override
    protected String getModuleName() {
        return "bootstrap";
    }
    
    /**
     * 配置平台线程池
     * 使用AppConfig中的配置
     * 
     * @param executor 线程池执行器
     */
    @Override
    protected void configurePlatformThreadPool(ThreadPoolTaskExecutor executor) {
        executor.setCorePoolSize(appConfig.getThreadPool().getCoreSize());
        executor.setMaxPoolSize(appConfig.getThreadPool().getMaxSize());
        executor.setQueueCapacity(appConfig.getThreadPool().getQueueCapacity());
        executor.setKeepAliveSeconds(appConfig.getThreadPool().getKeepAliveSeconds());
    }
}