#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
架构迁移指南模块

该模块提供从单体架构到依赖注入架构的迁移指南和工具。
"""

import json
import time
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, asdict
from datetime import datetime
from pathlib import Path
from loguru import logger


@dataclass
class MigrationStep:
    """迁移步骤"""
    step_number: int
    title: str
    description: str
    code_example: Optional[str] = None
    notes: List[str] = None
    
    def __post_init__(self):
        if self.notes is None:
            self.notes = []


@dataclass
class MigrationGuide:
    """迁移指南"""
    title: str
    overview: str
    prerequisites: List[str]
    steps: List[MigrationStep]
    benefits: List[str]
    troubleshooting: Dict[str, str]
    created_at: datetime


class ArchitectureMigrationGuide:
    """
    架构迁移指南生成器
    
    提供从单体架构到依赖注入架构的详细迁移指南。
    """
    
    def __init__(self):
        """初始化迁移指南生成器"""
        logger.info("架构迁移指南生成器初始化完成")
    
    def generate_migration_guide(self) -> MigrationGuide:
        """
        生成完整的迁移指南
        
        Returns:
            迁移指南对象
        """
        logger.info("生成架构迁移指南...")
        
        guide = MigrationGuide(
            title="crypto_ml_strategy 依赖注入架构迁移指南",
            overview=self._get_overview(),
            prerequisites=self._get_prerequisites(),
            steps=self._get_migration_steps(),
            benefits=self._get_benefits(),
            troubleshooting=self._get_troubleshooting(),
            created_at=datetime.now()
        )
        
        logger.info("架构迁移指南生成完成")
        return guide
    
    def _get_overview(self) -> str:
        """获取迁移概述"""
        return """
本指南将帮助您将 crypto_ml_strategy 项目从传统的单体架构迁移到现代的依赖注入架构。

## 架构对比

### 原有架构（单体架构）
- 所有组件在 TradingStrategyApp 类中直接实例化
- 紧耦合的组件关系
- 难以进行单元测试和模拟
- 配置硬编码在各个组件中
- 生命周期管理混乱

### 新架构（依赖注入架构）
- 使用 IoC 容器管理组件依赖
- 松耦合的组件关系
- 支持接口抽象和模拟测试
- 配置驱动的组件装配
- 统一的生命周期管理

## 迁移目标
1. 提高代码的可测试性和可维护性
2. 实现组件的松耦合
3. 支持配置驱动的组件装配
4. 建立统一的生命周期管理
5. 保持向后兼容性
        """
    
    def _get_prerequisites(self) -> List[str]:
        """获取迁移前提条件"""
        return [
            "确保当前代码库处于稳定状态",
            "备份现有的 main.py 文件",
            "确认所有现有功能正常工作",
            "准备测试环境进行迁移验证",
            "了解依赖注入的基本概念",
            "熟悉新的服务接口定义"
        ]
    
    def _get_migration_steps(self) -> List[MigrationStep]:
        """获取迁移步骤"""
        steps = []
        
        # 步骤1：备份现有代码
        steps.append(MigrationStep(
            step_number=1,
            title="备份现有代码",
            description="创建现有 main.py 的备份，确保可以回滚",
            code_example="""
# 备份原有文件
cp main.py main_original.py

# 或者使用 Git 创建分支
git checkout -b feature/dependency-injection
git add .
git commit -m "Backup before dependency injection migration"
            """,
            notes=[
                "建议使用版本控制系统进行备份",
                "确保备份包含所有相关配置文件",
                "记录当前系统的运行状态"
            ]
        ))
        
        # 步骤2：理解新架构组件
        steps.append(MigrationStep(
            step_number=2,
            title="理解新架构组件",
            description="熟悉依赖注入架构的核心组件",
            code_example="""
# 核心组件文件
dependency_container.py      # IoC 容器
service_interfaces.py        # 服务接口定义
service_factory.py          # 服务工厂
component_registry.py       # 组件注册表
lifecycle_manager.py        # 生命周期管理器
main_refactored.py         # 重构后的主应用
            """,
            notes=[
                "每个组件都有明确的职责",
                "组件间通过接口进行通信",
                "支持配置驱动的组件装配"
            ]
        ))
        
        # 步骤3：更新导入语句
        steps.append(MigrationStep(
            step_number=3,
            title="更新导入语句",
            description="在现有代码中添加新架构组件的导入",
            code_example="""
# 在需要使用新架构的文件中添加导入
from .dependency_container import DependencyContainer, get_container
from .service_interfaces import IConfigService, IKafkaService, IStrategyService
from .main_refactored import RefactoredTradingStrategyApp

# 示例：在测试文件中使用
def test_with_dependency_injection():
    container = get_container()
    config_service = container.resolve(IConfigService)
    assert config_service is not None
            """,
            notes=[
                "逐步引入新的导入语句",
                "避免一次性修改所有文件",
                "确保导入路径正确"
            ]
        ))
        
        # 步骤4：创建服务接口实现
        steps.append(MigrationStep(
            step_number=4,
            title="创建服务接口实现",
            description="为现有组件创建服务接口实现",
            code_example="""
# 示例：为现有的 Config 类实现 IConfigService 接口
class Config(IConfigService):
    def __init__(self, config_path: Optional[str] = None):
        # 现有的初始化代码
        pass
    
    def get(self, section: str, option: str, fallback: str = None) -> str:
        # 现有的 get 方法实现
        return self.config.get(section, option, fallback=fallback)
    
    # 实现其他接口方法...

# 示例：为现有的 KafkaClient 类实现 IKafkaService 接口
class KafkaClient(IKafkaService):
    # 现有的实现保持不变，只需确保实现了接口方法
    pass
            """,
            notes=[
                "现有类通常已经实现了接口方法",
                "主要是添加类型注解和接口继承",
                "确保方法签名与接口定义一致"
            ]
        ))
        
        # 步骤5：配置组件注册
        steps.append(MigrationStep(
            step_number=5,
            title="配置组件注册",
            description="在组件注册表中注册所有服务",
            code_example="""
# 在 component_registry.py 中添加自定义组件注册
def register_custom_components(self) -> None:
    \"\"\"注册自定义组件\"\"\"
    
    # 注册自定义服务
    self.container.register_singleton(
        ICustomService,
        factory=lambda factory: factory.create_custom_service()
    )
    
    # 注册带依赖的服务
    self.container.register_singleton(
        IComplexService,
        factory=lambda factory, config, logger: ComplexService(config, logger)
    )

# 在 service_factory.py 中添加工厂方法
def create_custom_service(self) -> ICustomService:
    \"\"\"创建自定义服务\"\"\"
    return CustomService(self.config)
            """,
            notes=[
                "按照依赖关系顺序注册组件",
                "使用工厂方法创建复杂的服务",
                "确保所有依赖都已注册"
            ]
        ))
        
        # 步骤6：迁移主应用逻辑
        steps.append(MigrationStep(
            step_number=6,
            title="迁移主应用逻辑",
            description="将原有的 TradingStrategyApp 逻辑迁移到新架构",
            code_example="""
# 原有的 main.py 使用方式
def main():
    app = TradingStrategyApp()
    app.run()

# 新架构的使用方式
def main():
    app = RefactoredTradingStrategyApp()
    app.run()

# 或者更细粒度的控制
def main():
    app = RefactoredTradingStrategyApp()
    app.initialize()
    app.start()
    
    try:
        # 主循环逻辑
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        app.stop()
    finally:
        app.cleanup()
            """,
            notes=[
                "新架构提供了更细粒度的控制",
                "支持优雅的启动和关闭",
                "保持了与原有接口的兼容性"
            ]
        ))
        
        # 步骤7：更新配置文件
        steps.append(MigrationStep(
            step_number=7,
            title="更新配置文件",
            description="根据需要更新配置文件以支持新架构",
            code_example="""
# config.ini 中可能需要添加的新配置
[dependency_injection]
container_validation = true
lifecycle_timeout = 30
health_check_interval = 60

[logging]
dependency_injection_level = INFO
component_startup_logging = true

# 示例：在代码中使用新配置
container_validation = config.get_bool('dependency_injection', 'container_validation', True)
if container_validation:
    validation_results = registry.validate_registrations()
    logger.info(f"容器验证结果: {validation_results}")
            """,
            notes=[
                "大部分现有配置保持不变",
                "只需添加新架构特定的配置",
                "确保配置的向后兼容性"
            ]
        ))
        
        # 步骤8：编写单元测试
        steps.append(MigrationStep(
            step_number=8,
            title="编写单元测试",
            description="为新架构编写全面的单元测试",
            code_example="""
# 示例：测试依赖注入容器
def test_container_registration():
    container = DependencyContainer()
    container.register_singleton(IConfigService, Config)
    
    config = container.resolve(IConfigService)
    assert config is not None
    assert isinstance(config, Config)

# 示例：测试服务模拟
def test_strategy_with_mock():
    container = DependencyContainer()
    
    # 注册模拟服务
    mock_prediction_engine = Mock(spec=IPredictionEngineService)
    container.register_singleton(IPredictionEngineService, instance=mock_prediction_engine)
    
    # 测试策略
    strategy = container.resolve(IStrategyService)
    strategy.set_prediction_engine(mock_prediction_engine)
    
    # 验证行为
    mock_prediction_engine.predict.assert_called()
            """,
            notes=[
                "新架构大大简化了单元测试",
                "支持轻松的依赖模拟",
                "可以独立测试每个组件"
            ]
        ))
        
        # 步骤9：集成测试
        steps.append(MigrationStep(
            step_number=9,
            title="集成测试",
            description="运行集成测试确保系统正常工作",
            code_example="""
# 运行架构验证测试
python -m src.test_dependency_injection

# 运行应用集成测试
python -m src.main_refactored --config config/test_config.ini

# 运行性能对比测试
python -m tests.performance_comparison

# 验证功能一致性
python -m tests.functional_tests
            """,
            notes=[
                "确保所有现有功能正常工作",
                "验证性能没有显著下降",
                "检查内存使用和启动时间"
            ]
        ))
        
        # 步骤10：生产部署
        steps.append(MigrationStep(
            step_number=10,
            title="生产部署",
            description="将新架构部署到生产环境",
            code_example="""
# 部署前检查清单
1. 备份生产环境配置
2. 准备回滚计划
3. 更新启动脚本
4. 配置监控和告警
5. 准备性能基准对比

# 更新启动脚本
#!/bin/bash
# 新的启动脚本
cd /path/to/crypto_ml_strategy
python -m src.main_refactored --config config/production.ini

# 监控关键指标
- 应用启动时间
- 内存使用情况
- CPU 使用率
- 消息处理延迟
- 错误率
            """,
            notes=[
                "采用蓝绿部署或滚动更新",
                "密切监控系统指标",
                "准备快速回滚方案",
                "逐步切换流量"
            ]
        ))
        
        return steps
    
    def _get_benefits(self) -> List[str]:
        """获取迁移收益"""
        return [
            "🧪 **提高可测试性**: 支持依赖模拟，简化单元测试编写",
            "🔧 **降低耦合度**: 组件间通过接口通信，减少直接依赖",
            "⚙️ **配置驱动**: 支持通过配置文件控制组件装配",
            "🔄 **生命周期管理**: 统一的组件启动、停止和清理流程",
            "📈 **可扩展性**: 易于添加新组件和功能",
            "🐛 **错误隔离**: 组件故障不会影响整个系统",
            "📊 **监控友好**: 内置健康检查和指标收集",
            "🚀 **性能优化**: 支持延迟加载和资源优化",
            "🔒 **类型安全**: 完整的类型注解和接口定义",
            "📚 **文档完善**: 清晰的架构文档和使用指南"
        ]
    
    def _get_troubleshooting(self) -> Dict[str, str]:
        """获取故障排除指南"""
        return {
            "服务解析失败": """
检查步骤：
1. 确认服务已在容器中注册
2. 检查依赖关系是否正确
3. 验证接口实现是否正确
4. 查看容器验证结果

解决方案：
- 使用 container.get_all_services() 查看已注册服务
- 检查 component_registry.py 中的注册逻辑
- 验证服务工厂方法是否正确
            """,
            
            "循环依赖错误": """
检查步骤：
1. 分析依赖关系图
2. 识别循环依赖的组件
3. 重新设计组件接口

解决方案：
- 使用事件驱动模式打破循环依赖
- 引入中介者模式
- 延迟初始化某些依赖
            """,
            
            "应用启动失败": """
检查步骤：
1. 查看启动日志
2. 检查配置文件
3. 验证数据库连接
4. 确认Kafka连接

解决方案：
- 使用 test_dependency_injection.py 验证架构
- 检查生命周期管理器状态
- 验证所有外部依赖可用
            """,
            
            "性能下降": """
检查步骤：
1. 对比迁移前后的性能指标
2. 分析服务解析开销
3. 检查内存使用情况

解决方案：
- 使用单例模式减少对象创建
- 优化服务解析路径
- 启用性能监控和分析
            """,
            
            "测试失败": """
检查步骤：
1. 确认测试环境配置
2. 检查模拟对象设置
3. 验证测试数据

解决方案：
- 使用依赖注入简化测试设置
- 创建测试专用的容器配置
- 使用工厂方法创建测试对象
            """
        }
    
    def save_guide_to_file(self, guide: MigrationGuide, output_path: str) -> None:
        """
        保存迁移指南到文件
        
        Args:
            guide: 迁移指南对象
            output_path: 输出文件路径
        """
        try:
            # 生成 Markdown 格式
            markdown_content = self._generate_markdown(guide)
            
            # 保存 Markdown 文件
            md_path = Path(output_path).with_suffix('.md')
            with open(md_path, 'w', encoding='utf-8') as f:
                f.write(markdown_content)
            
            # 保存 JSON 文件
            json_path = Path(output_path).with_suffix('.json')
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(asdict(guide), f, indent=2, ensure_ascii=False, default=str)
            
            logger.info(f"迁移指南已保存: {md_path}, {json_path}")
            
        except Exception as e:
            logger.error(f"保存迁移指南失败: {e}")
            raise
    
    def _generate_markdown(self, guide: MigrationGuide) -> str:
        """生成 Markdown 格式的迁移指南"""
        md_content = f"""# {guide.title}

{guide.overview}

## 迁移前提条件

"""
        
        for i, prereq in enumerate(guide.prerequisites, 1):
            md_content += f"{i}. {prereq}\n"
        
        md_content += "\n## 迁移步骤\n\n"
        
        for step in guide.steps:
            md_content += f"### 步骤 {step.step_number}: {step.title}\n\n"
            md_content += f"{step.description}\n\n"
            
            if step.code_example:
                md_content += "```python\n"
                md_content += step.code_example.strip()
                md_content += "\n```\n\n"
            
            if step.notes:
                md_content += "**注意事项:**\n"
                for note in step.notes:
                    md_content += f"- {note}\n"
                md_content += "\n"
        
        md_content += "## 迁移收益\n\n"
        for benefit in guide.benefits:
            md_content += f"- {benefit}\n"
        
        md_content += "\n## 故障排除\n\n"
        for problem, solution in guide.troubleshooting.items():
            md_content += f"### {problem}\n\n"
            md_content += f"{solution}\n\n"
        
        md_content += f"\n---\n*生成时间: {guide.created_at.strftime('%Y-%m-%d %H:%M:%S')}*\n"
        
        return md_content


def main():
    """主函数"""
    try:
        # 创建迁移指南生成器
        generator = ArchitectureMigrationGuide()
        
        # 生成迁移指南
        guide = generator.generate_migration_guide()
        
        # 保存到文件
        output_path = "architecture_migration_guide"
        generator.save_guide_to_file(guide, output_path)
        
        logger.info("架构迁移指南生成完成")
        
        # 输出摘要
        logger.info(f"迁移步骤数: {len(guide.steps)}")
        logger.info(f"收益数量: {len(guide.benefits)}")
        logger.info(f"故障排除项: {len(guide.troubleshooting)}")
        
    except Exception as e:
        logger.error(f"生成迁移指南失败: {e}")
        raise


if __name__ == "__main__":
    main()