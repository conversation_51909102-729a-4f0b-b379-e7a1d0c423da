package com.crypto.trading.bootstrap.config;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import com.crypto.trading.common.constant.KafkaConstants;
import com.crypto.trading.common.enums.KlineInterval;

import lombok.Data;

/**
 * 市场数据配置类
 * 将配置文件中的market前缀的配置项映射到此类
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "market")
public class MarketDataConfig {

    /**
     * 交易对列表
     */
    private String symbols = "BTCUSDT,ETHUSDT,BNBUSDT";
    
    /**
     * K线配置
     */
    private KlineConfig kline = new KlineConfig();
    
    /**
     * 深度配置
     */
    private DepthConfig depth = new DepthConfig();
    
    /**
     * 交易配置
     */
    private TradeConfig trade = new TradeConfig();
    
    /**
     * WebSocket配置
     */
    private WebSocketConfig websocket = new WebSocketConfig();
    
    /**
     * InfluxDB配置
     */
    private InfluxdbConfig influxdb = new InfluxdbConfig();
    
    /**
     * 获取交易对列表
     * 
     * @return 交易对列表
     */
    public List<String> getSymbolList() {
        return Arrays.asList(symbols.split(","));
    }
    
    /**
     * K线配置内部类
     */
    @Data
    public static class KlineConfig {
        /**
         * 是否启用
         */
        private boolean enabled = true;
        
        /**
         * K线间隔
         */
        private String intervals = "1m,5m,15m,30m,1h,4h,1d";
        
        /**
         * Kafka主题
         */
        private String topic = KafkaConstants.TOPIC_KLINE_DATA;
        
        /**
         * 获取K线间隔列表
         * 
         * @return K线间隔列表
         */
        public List<String> getIntervalList() {
            return Arrays.asList(intervals.split(","));
        }
    }
    
    /**
     * 深度配置内部类
     */
    @Data
    public static class DepthConfig {
        /**
         * 是否启用
         */
        private boolean enabled = true;
        
        /**
         * 深度级别
         */
        private int levels = 10;
        
        /**
         * 更新速度（毫秒）
         */
        private int speed = 1000;
        
        /**
         * Kafka主题
         */
        private String topic = KafkaConstants.TOPIC_DEPTH_DATA;
    }
    
    /**
     * 交易配置内部类
     */
    @Data
    public static class TradeConfig {
        /**
         * 是否启用
         */
        private boolean enabled = true;
        
        /**
         * Kafka主题
         */
        private String topic = KafkaConstants.TOPIC_TRADE_DATA;
    }
    
    /**
     * WebSocket配置内部类
     */
    @Data
    public static class WebSocketConfig {
        /**
         * 自动重连间隔（毫秒）
         */
        private int reconnectInterval = 5000;
        
        /**
         * 连接超时时间（毫秒）
         */
        private int connectTimeout = 5000;
    }
    
    /**
     * InfluxDB配置内部类
     */
    @Data
    public static class InfluxdbConfig {
        /**
         * 批处理大小
         */
        private Integer batchSize = 1000;
        
        /**
         * 刷新间隔（毫秒）
         */
        private Integer flushInterval = 5000;
    }
} 