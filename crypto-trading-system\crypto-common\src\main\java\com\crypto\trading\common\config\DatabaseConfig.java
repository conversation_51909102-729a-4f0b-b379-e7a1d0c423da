package com.crypto.trading.common.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 数据库配置类
 * <p>
 * 用于配置数据库连接、连接池、以及MyBatis相关参数。
 * 配置项从application-common.yml的spring.datasource前缀节点下读取。
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Configuration("commonDatabaseConfig")
@ConfigurationProperties(prefix = "spring.datasource")
public class DatabaseConfig {
    
    private static final Logger log = LoggerFactory.getLogger(DatabaseConfig.class);
    
    /**
     * 数据库URL
     */
    private String url;
    
    /**
     * 数据库用户名
     */
    private String username;
    
    /**
     * 数据库密码
     */
    private String password;
    
    /**
     * 数据库驱动类名
     */
    private String driverClassName;
    
    /**
     * 连接池配置
     */
    private HikariCp hikari = new HikariCp();
    
    /**
     * 当配置加载完成后的初始化操作
     */
    public void init() {
        log.info("数据库配置加载完成: url={}, username={}, driver={}", url, username, driverClassName);
        log.info("连接池配置: maxPoolSize={}, minIdle={}, connectionTimeout={}, idleTimeout={}",
                hikari.getMaximumPoolSize(), hikari.getMinimumIdle(),
                hikari.getConnectionTimeout(), hikari.getIdleTimeout());
    }
    
    /**
     * HikariCP连接池配置内部类
     */
    public static class HikariCp {
        /**
         * 最大池大小
         */
        private int maximumPoolSize = 10;
        
        /**
         * 最小空闲连接数
         */
        private int minimumIdle = 5;
        
        /**
         * 连接超时时间（毫秒）
         */
        private long connectionTimeout = 30000;
        
        /**
         * 空闲超时时间（毫秒）
         */
        private long idleTimeout = 600000;
        
        /**
         * 最大生命周期（毫秒）
         */
        private long maxLifetime = 1800000;
        
        /**
         * 连接测试查询
         */
        private String connectionTestQuery = "SELECT 1";
        
        /**
         * 连接池名称
         */
        private String poolName = "CryptoTradingHikariCP";

        public int getMaximumPoolSize() {
            return maximumPoolSize;
        }

        public void setMaximumPoolSize(int maximumPoolSize) {
            this.maximumPoolSize = maximumPoolSize;
        }

        public int getMinimumIdle() {
            return minimumIdle;
        }

        public void setMinimumIdle(int minimumIdle) {
            this.minimumIdle = minimumIdle;
        }

        public long getConnectionTimeout() {
            return connectionTimeout;
        }

        public void setConnectionTimeout(long connectionTimeout) {
            this.connectionTimeout = connectionTimeout;
        }

        public long getIdleTimeout() {
            return idleTimeout;
        }

        public void setIdleTimeout(long idleTimeout) {
            this.idleTimeout = idleTimeout;
        }

        public long getMaxLifetime() {
            return maxLifetime;
        }

        public void setMaxLifetime(long maxLifetime) {
            this.maxLifetime = maxLifetime;
        }

        public String getConnectionTestQuery() {
            return connectionTestQuery;
        }

        public void setConnectionTestQuery(String connectionTestQuery) {
            this.connectionTestQuery = connectionTestQuery;
        }

        public String getPoolName() {
            return poolName;
        }

        public void setPoolName(String poolName) {
            this.poolName = poolName;
        }
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getDriverClassName() {
        return driverClassName;
    }

    public void setDriverClassName(String driverClassName) {
        this.driverClassName = driverClassName;
    }

    public HikariCp getHikari() {
        return hikari;
    }

    public void setHikari(HikariCp hikari) {
        this.hikari = hikari;
    }
}