package com.crypto.trading.bootstrap.config;

import javax.sql.DataSource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.ClassPathResource;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.datasource.init.DataSourceInitializer;
import org.springframework.jdbc.datasource.init.ResourceDatabasePopulator;

/**
 * 数据库配置类
 */
@Configuration("bootstrapDatabaseConfig")
@Primary
public class DatabaseConfig {
    
    private static final Logger log = LoggerFactory.getLogger(DatabaseConfig.class);
    
    @Value("${spring.datasource.initialization-mode:always}")
    private String initializationMode;
    
    /**
     * 配置JdbcTemplate
     * 
     * @param dataSource 数据源
     * @return JdbcTemplate实例
     */
    @Bean
    public JdbcTemplate jdbcTemplate(DataSource dataSource) {
        return new JdbcTemplate(dataSource);
    }
    
    /**
     * 配置数据库初始化器，用于执行schema.sql创建表
     * 
     * @param dataSource 数据源
     * @return 数据库初始化器
     */
    @Bean
    @ConditionalOnProperty(name = "spring.datasource.initialization-mode", havingValue = "always", matchIfMissing = true)
    public DataSourceInitializer dataSourceInitializer(DataSource dataSource) {
        log.info("配置数据库初始化器，执行schema.sql创建表");
        DataSourceInitializer initializer = new DataSourceInitializer();
        initializer.setDataSource(dataSource);
        
        ResourceDatabasePopulator populator = new ResourceDatabasePopulator();
        
        try {
            ClassPathResource schemaResource = new ClassPathResource("schema.sql");
            if (schemaResource.exists()) {
                log.info("找到schema.sql文件，将用于初始化数据库表");
                populator.addScript(schemaResource);
            } else {
                log.warn("找不到schema.sql文件，无法初始化数据库表");
            }
        } catch (Exception e) {
            log.error("加载schema.sql文件时出错: {}", e.getMessage(), e);
        }
        
        populator.setContinueOnError(true); // 如果出错继续执行
        populator.setIgnoreFailedDrops(true); // 忽略DROP语句失败
        
        initializer.setDatabasePopulator(populator);
        return initializer;
    }
    
    /**
     * 验证数据库连接和表创建
     * 
     * @param jdbcTemplate JdbcTemplate
     * @return 验证结果
     */
    @Bean
    public boolean validateDatabaseConnection(JdbcTemplate jdbcTemplate) {
        try {
            log.info("验证数据库连接和表创建");
            String[] requiredTables = {"t_order", "t_trading_signal", "t_order_execution_log", "t_risk_control_log"};
            
            for (String tableName : requiredTables) {
                try {
                    Integer count = jdbcTemplate.queryForObject(
                            "SELECT COUNT(*) FROM information_schema.tables WHERE table_name = ?", 
                            Integer.class, 
                            tableName);
                    
                    if (count != null && count > 0) {
                        log.info("表 {} 已存在", tableName);
                    } else {
                        log.warn("表 {} 不存在，将通过schema.sql创建", tableName);
                    }
                } catch (Exception e) {
                    log.error("检查表 {} 时出错: {}", tableName, e.getMessage());
                }
            }
            
            return true;
        } catch (Exception e) {
            log.error("验证数据库连接时出错: {}", e.getMessage(), e);
            log.info("数据库连接验证失败，但应用程序将继续运行");
            return false;
        }
    }
}