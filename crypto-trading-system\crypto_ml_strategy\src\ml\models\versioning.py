#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
模型版本管理器模块

负责机器学习模型的版本控制、存储和管理。
支持模型的创建、更新、回滚和性能跟踪。
"""

import os
import json
import hashlib
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timedelta
import pandas as pd
from loguru import logger

from data.clients.mysql_client import MySQLClient


class ModelVersionManager:
    """
    模型版本管理器类
    
    支持功能：
    - 模型版本创建和更新
    - 模型性能跟踪
    - 模型回滚和恢复
    - 模型比较和选择
    - 自动模型清理
    - 模型部署状态管理
    """
    
    def __init__(self, mysql_client: MySQLClient):
        """
        初始化模型版本管理器
        
        Args:
            mysql_client: MySQL客户端
        """
        self.logger = logger.bind(component="ModelVersionManager")
        
        self.mysql_client = mysql_client
        
        # 版本管理配置
        self.max_versions_per_type = 10  # 每种模型类型最多保留的版本数
        self.auto_cleanup_days = 30  # 自动清理超过30天的旧版本
        self.performance_threshold = 0.6  # 性能阈值
        
        # 缓存
        self._active_models_cache = {}
        self._cache_ttl = 300  # 缓存5分钟
        self._last_cache_update = {}
        
        self.logger.info("模型版本管理器初始化完成")
    
    def save_model_version(self, version: str, model_type: str, model_path: str,
                          performance_metrics: Dict[str, float], training_config: Dict[str, Any],
                          is_active: bool = False, description: str = "") -> bool:
        """
        保存模型版本
        
        Args:
            version: 版本号
            model_type: 模型类型
            model_path: 模型文件路径
            performance_metrics: 性能指标
            training_config: 训练配置
            is_active: 是否设为活跃版本
            description: 版本描述
        
        Returns:
            保存是否成功
        """
        try:
            # 验证模型文件是否存在
            if not os.path.exists(model_path):
                self.logger.error(f"模型文件不存在: {model_path}")
                return False
            
            # 计算模型文件哈希值
            model_hash = self._calculate_file_hash(model_path)
            
            # 检查是否已存在相同的模型
            existing_model = self._check_existing_model(model_hash, model_type)
            if existing_model:
                self.logger.warning(f"模型已存在: {existing_model['version']}")
                return False
            
            # 如果设置为活跃版本，先将其他版本设为非活跃
            if is_active:
                self._deactivate_other_versions(model_type)
            
            # 保存到数据库
            success = self.mysql_client.save_model_version(
                version=version,
                model_type=model_type,
                model_path=model_path,
                performance_metrics=performance_metrics,
                training_config=training_config,
                is_active=is_active
            )
            
            if success:
                # 保存额外的版本信息
                self._save_version_metadata(version, model_type, model_hash, description)
                
                # 清理缓存
                self._clear_cache(model_type)
                
                # 自动清理旧版本
                self._auto_cleanup_old_versions(model_type)
                
                self.logger.info(f"模型版本保存成功: {version} ({model_type})")
                return True
            else:
                self.logger.error(f"模型版本保存失败: {version}")
                return False
            
        except Exception as e:
            self.logger.error(f"保存模型版本失败: {str(e)}")
            return False
    
    def get_active_model_version(self, model_type: str) -> Optional[Dict[str, Any]]:
        """
        获取活跃的模型版本
        
        Args:
            model_type: 模型类型
        
        Returns:
            活跃模型版本信息
        """
        try:
            # 检查缓存
            cached_model = self._get_cached_active_model(model_type)
            if cached_model:
                return cached_model
            
            # 从数据库获取
            model_info = self.mysql_client.get_active_model_version(model_type)
            
            if model_info:
                # 验证模型文件是否存在
                model_path = model_info.get('model_path')
                if model_path and os.path.exists(model_path):
                    # 缓存结果
                    self._cache_active_model(model_type, model_info)
                    return model_info
                else:
                    self.logger.warning(f"活跃模型文件不存在: {model_path}")
                    # 尝试自动选择下一个可用版本
                    return self._auto_select_fallback_model(model_type)
            
            return None
            
        except Exception as e:
            self.logger.error(f"获取活跃模型版本失败: {str(e)}")
            return None
    
    def list_model_versions(self, model_type: str, limit: int = 10) -> List[Dict[str, Any]]:
        """
        列出模型版本
        
        Args:
            model_type: 模型类型
            limit: 返回数量限制
        
        Returns:
            模型版本列表
        """
        try:
            query = """
                SELECT * FROM model_versions 
                WHERE model_type = %s 
                ORDER BY created_at DESC 
                LIMIT %s
            """
            
            result = self.mysql_client.execute_query(query, {'model_type': model_type, 'limit': limit})
            
            if result:
                # 添加额外的版本信息
                for model_info in result:
                    version = model_info.get('version')
                    if version:
                        metadata = self._get_version_metadata(version, model_type)
                        if metadata:
                            model_info.update(metadata)
                
                return result
            
            return []
            
        except Exception as e:
            self.logger.error(f"列出模型版本失败: {str(e)}")
            return []
    
    def activate_model_version(self, version: str, model_type: str) -> bool:
        """
        激活指定的模型版本
        
        Args:
            version: 版本号
            model_type: 模型类型
        
        Returns:
            激活是否成功
        """
        try:
            # 先将其他版本设为非活跃
            self._deactivate_other_versions(model_type)
            
            # 激活指定版本
            query = """
                UPDATE model_versions 
                SET is_active = 1, updated_at = NOW() 
                WHERE version = %s AND model_type = %s
            """
            
            success = self.mysql_client.execute_update(query, {'version': version, 'model_type': model_type})
            
            if success:
                # 清理缓存
                self._clear_cache(model_type)
                
                self.logger.info(f"模型版本激活成功: {version} ({model_type})")
                return True
            else:
                self.logger.error(f"模型版本激活失败: {version}")
                return False
            
        except Exception as e:
            self.logger.error(f"激活模型版本失败: {str(e)}")
            return False
    
    def delete_model_version(self, version: str, model_type: str, delete_file: bool = True) -> bool:
        """
        删除模型版本
        
        Args:
            version: 版本号
            model_type: 模型类型
            delete_file: 是否删除模型文件
        
        Returns:
            删除是否成功
        """
        try:
            # 获取模型信息
            query = "SELECT * FROM model_versions WHERE version = %s AND model_type = %s"
            result = self.mysql_client.execute_query(query, {'version': version, 'model_type': model_type})
            
            if not result:
                self.logger.warning(f"模型版本不存在: {version}")
                return False
            
            model_info = result[0]
            
            # 检查是否为活跃版本
            if model_info.get('is_active'):
                self.logger.error(f"无法删除活跃模型版本: {version}")
                return False
            
            # 删除数据库记录
            delete_query = "DELETE FROM model_versions WHERE version = %s AND model_type = %s"
            success = self.mysql_client.execute_update(delete_query, {'version': version, 'model_type': model_type})
            
            if success:
                # 删除模型文件
                if delete_file:
                    model_path = model_info.get('model_path')
                    if model_path and os.path.exists(model_path):
                        try:
                            os.remove(model_path)
                            self.logger.info(f"模型文件已删除: {model_path}")
                        except Exception as e:
                            self.logger.warning(f"删除模型文件失败: {str(e)}")
                
                # 删除版本元数据
                self._delete_version_metadata(version, model_type)
                
                self.logger.info(f"模型版本删除成功: {version} ({model_type})")
                return True
            else:
                self.logger.error(f"模型版本删除失败: {version}")
                return False
            
        except Exception as e:
            self.logger.error(f"删除模型版本失败: {str(e)}")
            return False
    
    def compare_model_versions(self, version1: str, version2: str, model_type: str) -> Optional[Dict[str, Any]]:
        """
        比较两个模型版本
        
        Args:
            version1: 版本1
            version2: 版本2
            model_type: 模型类型
        
        Returns:
            比较结果
        """
        try:
            # 获取两个版本的信息
            query = "SELECT * FROM model_versions WHERE version IN (%s, %s) AND model_type = %s"
            result = self.mysql_client.execute_query(query, {'version1': version1, 'version2': version2, 'model_type': model_type})
            
            if len(result) != 2:
                self.logger.error(f"无法找到要比较的模型版本")
                return None
            
            model1 = result[0] if result[0]['version'] == version1 else result[1]
            model2 = result[1] if result[1]['version'] == version1 else result[0]
            
            # 解析性能指标
            metrics1 = json.loads(model1.get('performance_metrics', '{}'))
            metrics2 = json.loads(model2.get('performance_metrics', '{}'))
            
            # 比较性能指标
            comparison = {
                'version1': {
                    'version': version1,
                    'metrics': metrics1,
                    'created_at': model1.get('created_at'),
                    'is_active': bool(model1.get('is_active'))
                },
                'version2': {
                    'version': version2,
                    'metrics': metrics2,
                    'created_at': model2.get('created_at'),
                    'is_active': bool(model2.get('is_active'))
                },
                'comparison': {}
            }
            
            # 计算性能差异
            for metric in set(metrics1.keys()) | set(metrics2.keys()):
                value1 = metrics1.get(metric, 0)
                value2 = metrics2.get(metric, 0)
                
                comparison['comparison'][metric] = {
                    'version1': value1,
                    'version2': value2,
                    'difference': value2 - value1,
                    'improvement': value2 > value1
                }
            
            return comparison
            
        except Exception as e:
            self.logger.error(f"比较模型版本失败: {str(e)}")
            return None
    
    def get_model_performance_history(self, model_type: str, days: int = 30) -> Optional[pd.DataFrame]:
        """
        获取模型性能历史
        
        Args:
            model_type: 模型类型
            days: 历史天数
        
        Returns:
            性能历史DataFrame
        """
        try:
            start_date = datetime.now() - timedelta(days=days)
            
            query = """
                SELECT version, performance_metrics, created_at 
                FROM model_versions 
                WHERE model_type = %s AND created_at >= %s 
                ORDER BY created_at
            """
            
            result = self.mysql_client.execute_query(query, {'model_type': model_type, 'start_date': start_date})
            
            if not result:
                return None
            
            # 转换为DataFrame
            data = []
            for row in result:
                metrics = json.loads(row.get('performance_metrics', '{}'))
                record = {
                    'version': row['version'],
                    'created_at': row['created_at'],
                    **metrics
                }
                data.append(record)
            
            df = pd.DataFrame(data)
            df['created_at'] = pd.to_datetime(df['created_at'])
            
            return df
            
        except Exception as e:
            self.logger.error(f"获取模型性能历史失败: {str(e)}")
            return None
    
    def has_active_model(self, model_type: str = 'unified_ml') -> bool:
        """
        检查是否有活跃模型
        
        Args:
            model_type: 模型类型
        
        Returns:
            是否有活跃模型
        """
        try:
            active_model = self.get_active_model_version(model_type)
            return active_model is not None
            
        except Exception as e:
            self.logger.error(f"检查活跃模型失败: {str(e)}")
            return False
    
    def get_current_model_version(self, model_type: str = 'unified_ml') -> Optional[str]:
        """
        获取当前模型版本号
        
        Args:
            model_type: 模型类型
        
        Returns:
            当前模型版本号
        """
        try:
            active_model = self.get_active_model_version(model_type)
            if active_model:
                return active_model.get('version')
            return None
            
        except Exception as e:
            self.logger.error(f"获取当前模型版本失败: {str(e)}")
            return None
    
    def _calculate_file_hash(self, file_path: str) -> str:
        """
        计算文件哈希值
        
        Args:
            file_path: 文件路径
        
        Returns:
            文件哈希值
        """
        try:
            hash_md5 = hashlib.md5()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
            
        except Exception as e:
            self.logger.error(f"计算文件哈希失败: {str(e)}")
            return ""
    
    def _check_existing_model(self, model_hash: str, model_type: str) -> Optional[Dict[str, Any]]:
        """
        检查是否存在相同的模型
        
        Args:
            model_hash: 模型哈希值
            model_type: 模型类型
        
        Returns:
            存在的模型信息
        """
        try:
            # 这里可以实现基于哈希值的重复检查
            # 暂时返回None，表示不存在重复模型
            return None
            
        except Exception as e:
            self.logger.error(f"检查现有模型失败: {str(e)}")
            return None
    
    def _deactivate_other_versions(self, model_type: str) -> bool:
        """
        将其他版本设为非活跃
        
        Args:
            model_type: 模型类型
        
        Returns:
            操作是否成功
        """
        try:
            query = "UPDATE model_versions SET is_active = 0 WHERE model_type = %s"
            return self.mysql_client.execute_update(query, {'model_type': model_type})
            
        except Exception as e:
            self.logger.error(f"停用其他版本失败: {str(e)}")
            return False
    
    def _save_version_metadata(self, version: str, model_type: str, model_hash: str, description: str) -> None:
        """
        保存版本元数据
        
        Args:
            version: 版本号
            model_type: 模型类型
            model_hash: 模型哈希值
            description: 描述
        """
        try:
            metadata = {
                'model_hash': model_hash,
                'description': description,
                'created_by': 'system',
                'tags': []
            }
            
            # 可以保存到配置表或单独的元数据表
            self.mysql_client.set_config_value(
                f"model_metadata_{model_type}_{version}",
                metadata,
                'json',
                f"Metadata for model {version}"
            )
            
        except Exception as e:
            self.logger.error(f"保存版本元数据失败: {str(e)}")
    
    def _get_version_metadata(self, version: str, model_type: str) -> Optional[Dict[str, Any]]:
        """
        获取版本元数据
        
        Args:
            version: 版本号
            model_type: 模型类型
        
        Returns:
            版本元数据
        """
        try:
            metadata = self.mysql_client.get_config_value(f"model_metadata_{model_type}_{version}")
            return metadata if isinstance(metadata, dict) else None
            
        except Exception as e:
            self.logger.error(f"获取版本元数据失败: {str(e)}")
            return None
    
    def _delete_version_metadata(self, version: str, model_type: str) -> None:
        """
        删除版本元数据
        
        Args:
            version: 版本号
            model_type: 模型类型
        """
        try:
            # 删除配置表中的元数据
            query = "DELETE FROM strategy_configs WHERE config_key = %s"
            self.mysql_client.execute_update(query, {'config_key': f"model_metadata_{model_type}_{version}"})
            
        except Exception as e:
            self.logger.error(f"删除版本元数据失败: {str(e)}")
    
    def _auto_cleanup_old_versions(self, model_type: str) -> None:
        """
        自动清理旧版本
        
        Args:
            model_type: 模型类型
        """
        try:
            # 获取所有非活跃版本
            query = """
                SELECT version, model_path, created_at 
                FROM model_versions 
                WHERE model_type = %s AND is_active = 0 
                ORDER BY created_at DESC
            """
            
            result = self.mysql_client.execute_query(query, {'model_type': model_type})
            
            if not result:
                return
            
            # 删除超过最大版本数的旧版本
            if len(result) > self.max_versions_per_type:
                versions_to_delete = result[self.max_versions_per_type:]
                
                for version_info in versions_to_delete:
                    version = version_info['version']
                    self.delete_model_version(version, model_type, delete_file=True)
                    self.logger.info(f"自动清理旧版本: {version}")
            
            # 删除超过指定天数的版本
            cutoff_date = datetime.now() - timedelta(days=self.auto_cleanup_days)
            
            for version_info in result:
                created_at = version_info['created_at']
                if isinstance(created_at, str):
                    created_at = datetime.fromisoformat(created_at)
                
                if created_at < cutoff_date:
                    version = version_info['version']
                    self.delete_model_version(version, model_type, delete_file=True)
                    self.logger.info(f"自动清理过期版本: {version}")
            
        except Exception as e:
            self.logger.error(f"自动清理旧版本失败: {str(e)}")
    
    def _get_cached_active_model(self, model_type: str) -> Optional[Dict[str, Any]]:
        """
        获取缓存的活跃模型
        
        Args:
            model_type: 模型类型
        
        Returns:
            缓存的模型信息
        """
        try:
            if model_type not in self._active_models_cache:
                return None
            
            last_update = self._last_cache_update.get(model_type)
            if not last_update:
                return None
            
            # 检查缓存是否过期
            if (datetime.now() - last_update).total_seconds() > self._cache_ttl:
                self._clear_cache(model_type)
                return None
            
            return self._active_models_cache[model_type]
            
        except Exception as e:
            self.logger.error(f"获取缓存模型失败: {str(e)}")
            return None
    
    def _cache_active_model(self, model_type: str, model_info: Dict[str, Any]) -> None:
        """
        缓存活跃模型
        
        Args:
            model_type: 模型类型
            model_info: 模型信息
        """
        try:
            self._active_models_cache[model_type] = model_info
            self._last_cache_update[model_type] = datetime.now()
            
        except Exception as e:
            self.logger.error(f"缓存模型失败: {str(e)}")
    
    def _clear_cache(self, model_type: str) -> None:
        """
        清理缓存
        
        Args:
            model_type: 模型类型
        """
        try:
            if model_type in self._active_models_cache:
                del self._active_models_cache[model_type]
            
            if model_type in self._last_cache_update:
                del self._last_cache_update[model_type]
                
        except Exception as e:
            self.logger.error(f"清理缓存失败: {str(e)}")
    
    def _auto_select_fallback_model(self, model_type: str) -> Optional[Dict[str, Any]]:
        """
        自动选择备用模型
        
        Args:
            model_type: 模型类型
        
        Returns:
            备用模型信息
        """
        try:
            # 获取性能最好的可用模型
            query = """
                SELECT * FROM model_versions 
                WHERE model_type = %s 
                ORDER BY created_at DESC 
                LIMIT 5
            """
            
            result = self.mysql_client.execute_query(query, {'model_type': model_type})
            
            if not result:
                return None
            
            # 选择文件存在且性能最好的模型
            for model_info in result:
                model_path = model_info.get('model_path')
                if model_path and os.path.exists(model_path):
                    # 激活这个模型
                    version = model_info['version']
                    if self.activate_model_version(version, model_type):
                        self.logger.info(f"自动选择备用模型: {version}")
                        return model_info
            
            return None
            
        except Exception as e:
            self.logger.error(f"自动选择备用模型失败: {str(e)}")
            return None