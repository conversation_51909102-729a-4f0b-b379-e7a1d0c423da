package com.crypto.trading.common.util;

import com.crypto.trading.common.exception.BusinessException;
import com.crypto.trading.common.exception.ErrorCode;
import com.crypto.trading.common.exception.SystemException;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ExceptionUtil单元测试类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
class ExceptionUtilTest {

    /**
     * 测试获取异常堆栈信息
     */
    @Test
    void testGetStackTrace() {
        // 创建异常
        Exception exception = new RuntimeException("测试异常");

        // 获取堆栈信息
        String stackTrace = ExceptionUtil.getStackTrace(exception);

        // 验证堆栈信息不为空且包含异常信息
        assertNotNull(stackTrace);
        assertTrue(stackTrace.contains("测试异常"));
        assertTrue(stackTrace.contains("RuntimeException"));
    }

    /**
     * 测试获取异常根因
     */
    @Test
    void testGetRootCause() {
        // 创建嵌套异常
        Exception rootCause = new IllegalArgumentException("根因异常");
        Exception middleCause = new RuntimeException("中间异常", rootCause);
        Exception topException = new Exception("顶层异常", middleCause);

        // 获取根因
        Throwable resultRootCause = ExceptionUtil.getRootCause(topException);

        // 验证获取的根因是正确的
        assertEquals(rootCause, resultRootCause);
    }

    /**
     * 测试获取异常根因消息
     */
    @Test
    void testGetRootCauseMessage() {
        // 创建嵌套异常
        Exception rootCause = new IllegalArgumentException("根因异常");
        Exception middleCause = new RuntimeException("中间异常", rootCause);
        Exception topException = new Exception("顶层异常", middleCause);

        // 获取根因消息
        String rootCauseMessage = ExceptionUtil.getRootCauseMessage(topException);

        // 验证获取的根因消息是正确的
        assertEquals("根因异常", rootCauseMessage);
    }

    /**
     * 测试抛出业务异常（使用错误码枚举）
     */
    @Test
    void testThrowBusinessExceptionWithErrorCode() {
        // 验证抛出的异常类型和属性
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            ExceptionUtil.throwBusinessException(ErrorCode.BUSINESS_ERROR);
        });

        assertEquals(String.valueOf(ErrorCode.BUSINESS_ERROR.getCode()), exception.getErrorCode());
        assertEquals(ErrorCode.BUSINESS_ERROR.getMessage(), exception.getMessage());
    }

    /**
     * 测试抛出业务异常（使用错误码枚举和自定义消息）
     */
    @Test
    void testThrowBusinessExceptionWithErrorCodeAndMessage() {
        // 自定义消息
        String customMessage = "自定义业务异常消息";

        // 验证抛出的异常类型和属性
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            ExceptionUtil.throwBusinessException(ErrorCode.BUSINESS_ERROR, customMessage);
        });

        assertEquals(String.valueOf(ErrorCode.BUSINESS_ERROR.getCode()), exception.getErrorCode());
        assertEquals(customMessage, exception.getMessage());
    }

    /**
     * 测试抛出系统异常（使用错误码枚举）
     */
    @Test
    void testThrowSystemExceptionWithErrorCode() {
        // 验证抛出的异常类型和属性
        SystemException exception = assertThrows(SystemException.class, () -> {
            ExceptionUtil.throwSystemException(ErrorCode.SYSTEM_ERROR);
        });

        assertEquals(ErrorCode.SYSTEM_ERROR.getCode(), exception.getCode());
        assertEquals(ErrorCode.SYSTEM_ERROR.getMessage(), exception.getMessage());
    }

    /**
     * 测试抛出系统异常（使用错误码枚举和自定义消息）
     */
    @Test
    void testThrowSystemExceptionWithErrorCodeAndMessage() {
        // 自定义消息
        String customMessage = "自定义系统异常消息";

        // 验证抛出的异常类型和属性
        SystemException exception = assertThrows(SystemException.class, () -> {
            ExceptionUtil.throwSystemException(ErrorCode.SYSTEM_ERROR, customMessage);
        });

        assertEquals(ErrorCode.SYSTEM_ERROR.getCode(), exception.getCode());
        assertEquals(customMessage, exception.getMessage());
    }

    /**
     * 测试抛出系统异常（使用错误码枚举和异常原因）
     */
    @Test
    void testThrowSystemExceptionWithErrorCodeAndCause() {
        // 异常原因
        Exception cause = new RuntimeException("原始异常");

        // 验证抛出的异常类型和属性
        SystemException exception = assertThrows(SystemException.class, () -> {
            ExceptionUtil.throwSystemException(ErrorCode.SYSTEM_ERROR, cause);
        });

        assertEquals(ErrorCode.SYSTEM_ERROR.getCode(), exception.getCode());
        assertEquals(ErrorCode.SYSTEM_ERROR.getMessage(), exception.getMessage());
        assertEquals(cause, exception.getCause());
    }

    /**
     * 测试抛出系统异常（使用错误码枚举、自定义消息和异常原因）
     */
    @Test
    void testThrowSystemExceptionWithErrorCodeMessageAndCause() {
        // 自定义消息和异常原因
        String customMessage = "自定义系统异常消息";
        Exception cause = new RuntimeException("原始异常");

        // 验证抛出的异常类型和属性
        SystemException exception = assertThrows(SystemException.class, () -> {
            ExceptionUtil.throwSystemException(ErrorCode.SYSTEM_ERROR, customMessage, cause);
        });

        assertEquals(ErrorCode.SYSTEM_ERROR.getCode(), exception.getCode());
        assertEquals(customMessage, exception.getMessage());
        assertEquals(cause, exception.getCause());
    }

    /**
     * 测试判断是否为业务异常
     */
    @Test
    void testIsBusinessException() {
        // 创建不同类型的异常
        BusinessException businessException = new BusinessException("BUSINESS_ERROR", "业务异常");
        SystemException systemException = new SystemException(ErrorCode.SYSTEM_ERROR);
        RuntimeException runtimeException = new RuntimeException();

        // 验证判断结果
        assertTrue(ExceptionUtil.isBusinessException(businessException));
        assertFalse(ExceptionUtil.isBusinessException(systemException));
        assertFalse(ExceptionUtil.isBusinessException(runtimeException));
    }

    /**
     * 测试判断是否为系统异常
     */
    @Test
    void testIsSystemException() {
        // 创建不同类型的异常
        BusinessException businessException = new BusinessException("BUSINESS_ERROR", "业务异常");
        SystemException systemException = new SystemException(ErrorCode.SYSTEM_ERROR);
        RuntimeException runtimeException = new RuntimeException();

        // 验证判断结果
        assertFalse(ExceptionUtil.isSystemException(businessException));
        assertTrue(ExceptionUtil.isSystemException(systemException));
        assertFalse(ExceptionUtil.isSystemException(runtimeException));
    }
}