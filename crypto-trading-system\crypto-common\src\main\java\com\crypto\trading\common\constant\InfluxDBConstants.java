package com.crypto.trading.common.constant;

/**
 * InfluxDB常量类
 * <p>
 * 定义InfluxDB相关的常量，如数据库名称、表名、字段名等
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public final class InfluxDBConstants {

    /**
     * 私有构造函数，防止实例化
     */
    private InfluxDBConstants() {
        throw new IllegalStateException("常量类不允许实例化");
    }

    /**
     * 默认数据库URL
     */
    public static final String DEFAULT_URL = "http://localhost:8086";

    /**
     * 默认数据库名称
     */
    public static final String DEFAULT_DATABASE = "crypto_trading";

    /**
     * 默认数据保留策略
     */
    public static final String DEFAULT_RETENTION_POLICY = "autogen";

    /**
     * 默认批处理大小
     */
    public static final int DEFAULT_BATCH_SIZE = 1000;

    /**
     * 默认批处理间隔（毫秒）
     */
    public static final int DEFAULT_BATCH_INTERVAL = 1000;

    /**
     * 默认一致性级别
     */
    public static final String DEFAULT_CONSISTENCY_LEVEL = "ONE";

    /**
     * 默认缓冲区大小
     */
    public static final int DEFAULT_BUFFER_SIZE = 10000;

    /**
     * 默认连接超时（毫秒）
     */
    public static final int DEFAULT_CONNECT_TIMEOUT = 10000;

    /**
     * 默认写入超时（毫秒）
     */
    public static final int DEFAULT_WRITE_TIMEOUT = 10000;

    /**
     * 默认读取超时（毫秒）
     */
    public static final int DEFAULT_READ_TIMEOUT = 10000;

    /**
     * 默认重试次数
     */
    public static final int DEFAULT_RETRY_COUNT = 3;

    /**
     * 默认重试间隔（毫秒）
     */
    public static final long DEFAULT_RETRY_INTERVAL = 1000L;

    /**
     * 时间戳精度 - 纳秒
     */
    public static final String PRECISION_NANOSECONDS = "ns";

    /**
     * 时间戳精度 - 微秒
     */
    public static final String PRECISION_MICROSECONDS = "u";

    /**
     * 时间戳精度 - 毫秒
     */
    public static final String PRECISION_MILLISECONDS = "ms";

    /**
     * 时间戳精度 - 秒
     */
    public static final String PRECISION_SECONDS = "s";

    /**
     * 时间戳精度 - 分钟
     */
    public static final String PRECISION_MINUTES = "m";

    /**
     * 时间戳精度 - 小时
     */
    public static final String PRECISION_HOURS = "h";

    /**
     * K线数据表名
     */
    public static final String MEASUREMENT_KLINE = "kline_data";

    /**
     * 深度数据表名
     */
    public static final String MEASUREMENT_DEPTH = "depth_data";

    /**
     * 交易数据表名
     */
    public static final String MEASUREMENT_TRADE = "trade_data";

    /**
     * 订单数据表名
     */
    public static final String MEASUREMENT_ORDER = "order_data";

    /**
     * 账户数据表名
     */
    public static final String MEASUREMENT_ACCOUNT = "account_data";

    /**
     * 持仓数据表名
     */
    public static final String MEASUREMENT_POSITION = "position_data";

    /**
     * 系统指标表名
     */
    public static final String MEASUREMENT_SYSTEM_METRICS = "system_metrics";

    /**
     * 标签 - 交易对
     */
    public static final String TAG_SYMBOL = "symbol";

    /**
     * 标签 - 时间周期
     */
    public static final String TAG_INTERVAL = "interval";

    /**
     * 标签 - 交易所
     */
    public static final String TAG_EXCHANGE = "exchange";

    /**
     * 标签 - 合约类型
     */
    public static final String TAG_CONTRACT_TYPE = "contract_type";

    /**
     * 标签 - 策略ID
     */
    public static final String TAG_STRATEGY_ID = "strategy_id";

    /**
     * 字段 - 开盘价
     */
    public static final String FIELD_OPEN = "open";

    /**
     * 字段 - 最高价
     */
    public static final String FIELD_HIGH = "high";

    /**
     * 字段 - 最低价
     */
    public static final String FIELD_LOW = "low";

    /**
     * 字段 - 收盘价
     */
    public static final String FIELD_CLOSE = "close";

    /**
     * 字段 - 成交量
     */
    public static final String FIELD_VOLUME = "volume";

    /**
     * 字段 - 成交额
     */
    public static final String FIELD_QUOTE_VOLUME = "quote_volume";

    /**
     * 字段 - 买一价
     */
    public static final String FIELD_BID_PRICE = "bid_price";

    /**
     * 字段 - 买一量
     */
    public static final String FIELD_BID_QUANTITY = "bid_quantity";

    /**
     * 字段 - 卖一价
     */
    public static final String FIELD_ASK_PRICE = "ask_price";

    /**
     * 字段 - 卖一量
     */
    public static final String FIELD_ASK_QUANTITY = "ask_quantity";
} 