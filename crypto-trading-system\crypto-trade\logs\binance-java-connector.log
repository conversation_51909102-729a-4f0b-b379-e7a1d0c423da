2025-06-22 02:43:57.885[1750531437885] | INFO  | main       | c.c.t.t.c.TradingSignalConsumer      - 收到交易信号: signalId=test-signal-1, symbol=BTCUSDT, type=BUY, topic=strategy.signal, partition=0, offset=0
2025-06-22 02:43:57.894[1750531437894] | INFO  | main       | c.c.t.t.c.TradingSignalConsumer      - 交易信号处理成功: signalId=test-signal-1, message=处理成功
2025-06-22 02:43:57.958[1750531437958] | INFO  | main       | c.c.t.t.c.TradingSignalConsumer      - 收到交易信号: signalId=test-signal-1, symbol=BTCUSDT, type=BUY, topic=strategy.signal, partition=0, offset=0
2025-06-22 02:43:57.959[1750531437959] | ERROR | main       | c.c.t.t.c.TradingSignalConsumer      - 处理交易信号异常: signalId=test-signal-1, error=处理异常, stacktrace=java.lang.RuntimeException: 处理异常
	at com.crypto.trading.trade.consumer.TradingSignalConsumer.consume(TradingSignalConsumer.java:108)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at com.crypto.trading.trade.consumer.TradingSignalConsumer.consume(TradingSignalConsumer.java:69)
	at com.crypto.trading.trade.consumer.TradingSignalConsumerTest.testConsumeWithProcessingException(TradingSignalConsumerTest.java:166)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.junit.platform.commons.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:728)
	at org.junit.jupiter.engine.execution.MethodInvocation.proceed(MethodInvocation.java:60)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$ValidatingInvocation.proceed(InvocationInterceptorChain.java:131)
	at org.junit.jupiter.engine.extension.TimeoutExtension.intercept(TimeoutExtension.java:156)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestableMethod(TimeoutExtension.java:147)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestMethod(TimeoutExtension.java:86)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker$ReflectiveInterceptorCall.lambda$ofVoidMethod$0(InterceptingExecutableInvoker.java:103)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.lambda$invoke$0(InterceptingExecutableInvoker.java:93)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$InterceptedInvocation.proceed(InvocationInterceptorChain.java:106)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.proceed(InvocationInterceptorChain.java:64)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.chainAndInvoke(InvocationInterceptorChain.java:45)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.invoke(InvocationInterceptorChain.java:37)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:92)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:86)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$invokeTestMethod$7(TestMethodTestDescriptor.java:218)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.invokeTestMethod(TestMethodTestDescriptor.java:214)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:139)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:69)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.apache.maven.surefire.junitplatform.LazyLauncher.execute(LazyLauncher.java:56)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.execute(JUnitPlatformProvider.java:184)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invokeAllTests(JUnitPlatformProvider.java:148)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invoke(JUnitPlatformProvider.java:122)
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:385)
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:162)
	at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:507)
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:495)

2025-06-22 02:43:57.968[1750531437968] | INFO  | main       | c.c.t.t.c.TradingSignalConsumer      - 收到交易信号: signalId=test-signal-1, symbol=BTCUSDT, type=BUY, topic=strategy.signal, partition=0, offset=0
2025-06-22 02:43:57.968[1750531437968] | WARN  | main       | c.c.t.t.c.TradingSignalConsumer      - 无效的交易信号: signalId=test-signal-1, error=无效的信号
2025-06-22 02:43:57.976[1750531437976] | WARN  | main       | c.c.t.t.c.TradingSignalConsumer      - 收到空的交易信号消息: topic=strategy.signal, partition=0, offset=0, key=key1
2025-06-22 02:43:58.494[1750531438494] | INFO  | main       | c.c.t.t.e.BinanceTradeExecutor       - 执行市价单: orderId=123456, symbol=BTCUSDT, side=BUY, quantity=1.0
2025-06-22 02:43:58.499[1750531438499] | INFO  | main       | c.c.trading.common.util.ThreadUtil   - 创建线程池成功: corePoolSize=4, maximumPoolSize=13, queueCapacity=1024, threadNamePrefix=thread-pool-
2025-06-22 02:43:58.546[1750531438546] | INFO  | main       | c.c.t.t.e.BinanceTradeExecutor       - 查询订单状态: symbol=BTCUSDT, orderId=123456, clientOrderId=test123
2025-06-22 02:43:58.546[1750531438546] | INFO  | main       | c.c.t.t.e.BinanceTradeExecutor       - 试运行模式，返回模拟订单状态
2025-06-22 02:43:58.552[1750531438552] | INFO  | main       | c.c.t.t.e.BinanceTradeExecutor       - 执行市价单: orderId=123456, symbol=BTCUSDT, side=BUY, quantity=1.0
2025-06-22 02:43:58.553[1750531438553] | INFO  | main       | c.c.t.t.e.BinanceTradeExecutor       - 试运行模式，不实际下单: OrderEntity(id=1, orderId=123456, clientOrderId=test123, symbol=BTCUSDT, side=BUY, positionSide=BOTH, orderType=MARKET, quantity=1.0, price=50000.0, executedQuantity=null, executedPrice=null, status=NEW, strategy=null, signalId=null, errorCode=null, errorMessage=null, createdTime=1750531438551, updatedTime=null, executedTime=null, deleted=null, timeInForce=null, remark=null)
2025-06-22 02:43:58.619[1750531438619] | INFO  | main       | c.c.t.t.e.BinanceTradeExecutor       - 查询订单状态: symbol=BTCUSDT, orderId=123456, clientOrderId=test123
2025-06-22 02:43:58.626[1750531438626] | INFO  | main       | c.c.t.t.e.BinanceTradeExecutor       - 执行市价单: orderId=123456, symbol=BTCUSDT, side=BUY, quantity=1.0
2025-06-22 02:43:58.633[1750531438633] | INFO  | main       | c.c.t.t.e.BinanceTradeExecutor       - 取消订单: orderId=123456, symbol=BTCUSDT, clientOrderId=test123
2025-06-22 02:43:58.788[1750531438788] | INFO  | main       | c.c.t.t.p.i.KafkaTradeResultProducer - 发送订单执行结果到Kafka: orderId=1, symbol=BTCUSDT, status=FILLED
2025-06-22 02:43:58.872[1750531438872] | ERROR |            | c.c.t.t.p.i.KafkaTradeResultProducer - 订单执行结果发送失败: orderId=1, error=java.lang.RuntimeException: 订单执行结果发送失败
java.util.concurrent.CompletionException: java.lang.RuntimeException: 订单执行结果发送失败
	at java.base/java.util.concurrent.CompletableFuture.reportJoin(CompletableFuture.java:413)
	at java.base/java.util.concurrent.CompletableFuture.join(CompletableFuture.java:2118)
	at com.crypto.trading.trade.producer.impl.KafkaTradeResultProducer.lambda$sendOrderExecutionResult$0(KafkaTradeResultProducer.java:84)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.VirtualThread.run(VirtualThread.java:329)
Caused by: java.lang.RuntimeException: 订单执行结果发送失败
	at com.crypto.trading.trade.producer.KafkaTradeResultProducerTest.testSendOrderExecutionResult_Failure(KafkaTradeResultProducerTest.java:158)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.junit.platform.commons.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:728)
	at org.junit.jupiter.engine.execution.MethodInvocation.proceed(MethodInvocation.java:60)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$ValidatingInvocation.proceed(InvocationInterceptorChain.java:131)
	at org.junit.jupiter.engine.extension.TimeoutExtension.intercept(TimeoutExtension.java:156)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestableMethod(TimeoutExtension.java:147)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestMethod(TimeoutExtension.java:86)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker$ReflectiveInterceptorCall.lambda$ofVoidMethod$0(InterceptingExecutableInvoker.java:103)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.lambda$invoke$0(InterceptingExecutableInvoker.java:93)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$InterceptedInvocation.proceed(InvocationInterceptorChain.java:106)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.proceed(InvocationInterceptorChain.java:64)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.chainAndInvoke(InvocationInterceptorChain.java:45)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.invoke(InvocationInterceptorChain.java:37)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:92)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:86)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$invokeTestMethod$7(TestMethodTestDescriptor.java:218)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.invokeTestMethod(TestMethodTestDescriptor.java:214)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:139)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:69)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.apache.maven.surefire.junitplatform.LazyLauncher.execute(LazyLauncher.java:56)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.execute(JUnitPlatformProvider.java:184)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invokeAllTests(JUnitPlatformProvider.java:148)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invoke(JUnitPlatformProvider.java:122)
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:385)
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:162)
	at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:507)
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:495)
2025-06-22 02:43:58.930[1750531438930] | INFO  | main       | c.c.t.t.p.i.KafkaTradeResultProducer - 发送订单执行错误到Kafka: clientOrderId=TEST123, symbol=BTCUSDT, errorCode=400, errorMessage=Invalid parameter
2025-06-22 02:43:58.974[1750531438974] | INFO  |            | c.c.t.t.p.i.KafkaTradeResultProducer - 订单执行错误发送成功: topic=null, partition=0, offset=0
2025-06-22 02:43:58.977[1750531438977] | INFO  | main       | c.c.t.t.p.i.KafkaTradeResultProducer - 发送订单状态更新到Kafka: orderId=1, symbol=BTCUSDT, status=FILLED
2025-06-22 02:43:58.986[1750531438986] | INFO  |            | c.c.t.t.p.i.KafkaTradeResultProducer - 订单状态更新发送成功: topic=null, partition=0, offset=0
2025-06-22 02:43:58.999[1750531438999] | INFO  | main       | c.c.t.t.p.i.KafkaTradeResultProducer - 批量发送订单执行结果到Kafka
2025-06-22 02:43:59.001[1750531439001] | INFO  |            | c.c.t.t.p.i.KafkaTradeResultProducer - 发送订单执行结果到Kafka: orderId=1, symbol=BTCUSDT, status=FILLED
2025-06-22 02:43:59.004[1750531439004] | INFO  |            | c.c.t.t.p.i.KafkaTradeResultProducer - 订单执行结果发送成功: topic=null, partition=0, offset=0
2025-06-22 02:43:59.004[1750531439004] | INFO  |            | c.c.t.t.p.i.KafkaTradeResultProducer - 发送订单执行结果到Kafka: orderId=2, symbol=BTCUSDT, status=FILLED
2025-06-22 02:43:59.007[1750531439007] | INFO  |            | c.c.t.t.p.i.KafkaTradeResultProducer - 订单执行结果发送成功: topic=null, partition=0, offset=0
2025-06-22 02:43:59.007[1750531439007] | INFO  |            | c.c.t.t.p.i.KafkaTradeResultProducer - 批量发送订单执行结果完成，共发送2条消息
2025-06-22 02:43:59.012[1750531439012] | INFO  | main       | c.c.t.t.p.i.KafkaTradeResultProducer - 发送订单执行结果到Kafka: orderId=1, symbol=BTCUSDT, status=FILLED
2025-06-22 02:43:59.014[1750531439014] | INFO  |            | c.c.t.t.p.i.KafkaTradeResultProducer - 订单执行结果发送成功: topic=null, partition=0, offset=0
2025-06-22 02:43:59.208[1750531439208] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 检查交易信号风险: TradeSignal{signalId='test-signal-003', symbol='BTCUSDT', strategy='null', signalType='null', side='null', positionSide='null', confidence=0.0, price=15000.0, quantity=1.0, stopPrice=null, takeProfit=null, orderType='null', timeInForce='null', leverageLevel=null, generatedTime=1750531439208}
2025-06-22 02:43:59.214[1750531439214] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 风控检查已禁用，跳过检查
2025-06-22 02:43:59.222[1750531439222] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 检查订单风险: orderId=test-order-002
2025-06-22 02:43:59.222[1750531439222] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 检查交易金额风险: symbol=BTCUSDT, amount=15000.00
2025-06-22 02:43:59.224[1750531439224] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 交易金额检查结果: amount=15000.00, limit=10000, pass=false
2025-06-22 02:43:59.225[1750531439225] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 记录风控检查: signalId=test-strategy-002, ruleId=AMOUNT, triggered=true
2025-06-22 02:43:59.235[1750531439235] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 检查订单风险: orderId=test-order-001
2025-06-22 02:43:59.235[1750531439235] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 检查交易金额风险: symbol=BTCUSDT, amount=5000.00
2025-06-22 02:43:59.235[1750531439235] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 交易金额检查结果: amount=5000.00, limit=10000, pass=true
2025-06-22 02:43:59.235[1750531439235] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 记录风控检查: signalId=test-strategy-001, ruleId=ALL_CHECKS, triggered=false
2025-06-22 02:43:59.235[1750531439235] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 订单通过风控检查
2025-06-22 02:43:59.238[1750531439238] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 检查交易频率: symbol=BTCUSDT, timeWindow=60000ms
2025-06-22 02:43:59.239[1750531439239] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 交易频率检查结果: count=1, limit=10, pass=true
2025-06-22 02:43:59.240[1750531439240] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 检查交易频率: symbol=BTCUSDT, timeWindow=60000ms
2025-06-22 02:43:59.240[1750531439240] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 交易频率检查结果: count=2, limit=10, pass=true
2025-06-22 02:43:59.240[1750531439240] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 检查交易频率: symbol=BTCUSDT, timeWindow=60000ms
2025-06-22 02:43:59.240[1750531439240] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 交易频率检查结果: count=3, limit=10, pass=true
2025-06-22 02:43:59.240[1750531439240] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 检查交易频率: symbol=BTCUSDT, timeWindow=60000ms
2025-06-22 02:43:59.240[1750531439240] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 交易频率检查结果: count=11, limit=10, pass=false
2025-06-22 02:43:59.243[1750531439243] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 检查交易金额风险: symbol=BTCUSDT, amount=5000.0
2025-06-22 02:43:59.243[1750531439243] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 交易金额检查结果: amount=5000.0, limit=10000, pass=true
2025-06-22 02:43:59.243[1750531439243] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 检查交易金额风险: symbol=BTCUSDT, amount=15000.0
2025-06-22 02:43:59.243[1750531439243] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 交易金额检查结果: amount=15000.0, limit=10000, pass=false
2025-06-22 02:43:59.329[1750531439329] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 记录风控检查: signalId=test-signal-001, ruleId=AMOUNT, triggered=true
2025-06-22 02:43:59.337[1750531439337] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 检查交易信号风险: TradeSignal{signalId='test-signal-001', symbol='BTCUSDT', strategy='null', signalType='null', side='null', positionSide='null', confidence=0.0, price=5000.0, quantity=1.0, stopPrice=null, takeProfit=null, orderType='null', timeInForce='null', leverageLevel=null, generatedTime=1750531439337}
2025-06-22 02:43:59.338[1750531439338] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 检查交易频率: symbol=BTCUSDT, timeWindow=60000ms
2025-06-22 02:43:59.338[1750531439338] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 交易频率检查结果: count=1, limit=10, pass=true
2025-06-22 02:43:59.338[1750531439338] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 检查交易金额风险: symbol=BTCUSDT, amount=5000.00
2025-06-22 02:43:59.338[1750531439338] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 交易金额检查结果: amount=5000.00, limit=10000, pass=true
2025-06-22 02:43:59.338[1750531439338] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 记录风控检查: signalId=test-signal-001, ruleId=ALL_CHECKS, triggered=false
2025-06-22 02:43:59.338[1750531439338] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 交易信号通过风控检查
2025-06-22 02:43:59.350[1750531439350] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 检查交易信号风险: TradeSignal{signalId='test-signal-002', symbol='BTCUSDT', strategy='null', signalType='null', side='null', positionSide='null', confidence=0.0, price=15000.0, quantity=1.0, stopPrice=null, takeProfit=null, orderType='null', timeInForce='null', leverageLevel=null, generatedTime=1750531439349}
2025-06-22 02:43:59.350[1750531439350] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 检查交易频率: symbol=BTCUSDT, timeWindow=60000ms
2025-06-22 02:43:59.350[1750531439350] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 交易频率检查结果: count=1, limit=10, pass=true
2025-06-22 02:43:59.350[1750531439350] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 检查交易金额风险: symbol=BTCUSDT, amount=15000.00
2025-06-22 02:43:59.350[1750531439350] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 交易金额检查结果: amount=15000.00, limit=10000, pass=false
2025-06-22 02:43:59.350[1750531439350] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 记录风控检查: signalId=test-signal-002, ruleId=AMOUNT, triggered=true
2025-06-22 02:43:59.518[1750531439518] | INFO  |            | c.c.t.t.s.i.SignalProcessingServiceImpl - 交易信号已保存到数据库 [test-validation-id]: signalId=test-signal-1, id=null
2025-06-22 02:43:59.519[1750531439519] | INFO  | main       | c.c.t.t.s.i.SignalProcessingServiceImpl - 交易执行功能未启用 [test-validation-id]: signalId=test-signal-1
2025-06-22 02:43:59.521[1750531439521] | INFO  | main       | c.c.t.c.u.DatabaseExecutorTestUtils  - 等待异步数据库操作完成，超时时间：5000ms，轮询间隔：100ms
2025-06-22 02:43:59.731[1750531439731] | INFO  |            | c.c.t.t.s.i.SignalProcessingServiceImpl - 信号处理状态已更新: signalId=test-signal-1, success=false, result=交易执行功能未启用
2025-06-22 02:43:59.745[1750531439745] | INFO  | main       | c.c.t.c.u.DatabaseExecutorTestUtils  - 所有异步数据库操作已完成，耗时：223ms
2025-06-22 02:43:59.757[1750531439757] | INFO  |            | c.c.t.t.s.i.SignalProcessingServiceImpl - 交易信号已保存到数据库 [test-validation-id]: signalId=test-signal-1, id=null
2025-06-22 02:43:59.758[1750531439758] | INFO  | main       | c.c.t.t.s.i.SignalProcessingServiceImpl - 交易信号已提交执行 [test-validation-id]: signalId=test-signal-1
2025-06-22 02:43:59.762[1750531439762] | INFO  | main       | c.c.t.c.u.DatabaseExecutorTestUtils  - 等待异步数据库操作完成，超时时间：5000ms，轮询间隔：100ms
2025-06-22 02:43:59.764[1750531439764] | INFO  |            | c.c.t.t.s.i.SignalProcessingServiceImpl - 信号处理状态已更新: signalId=test-signal-1, success=true, result=处理成功
2025-06-22 02:43:59.868[1750531439868] | INFO  | main       | c.c.t.c.u.DatabaseExecutorTestUtils  - 所有异步数据库操作已完成，耗时：106ms
2025-06-22 02:43:59.871[1750531439871] | WARN  | main       | c.c.t.t.s.i.SignalProcessingServiceImpl - 交易信号已存在 [test-validation-id]: signalId=test-signal-1
2025-06-22 02:43:59.879[1750531439879] | INFO  |            | c.c.t.t.s.i.SignalProcessingServiceImpl - 交易信号已保存到数据库 [test-validation-id]: signalId=test-signal-1, id=null
2025-06-22 02:43:59.879[1750531439879] | INFO  | main       | c.c.t.t.s.i.SignalProcessingServiceImpl - HOLD信号已处理 [test-validation-id]: signalId=test-signal-1
2025-06-22 02:43:59.879[1750531439879] | INFO  | main       | c.c.t.c.u.DatabaseExecutorTestUtils  - 等待异步数据库操作完成，超时时间：5000ms，轮询间隔：100ms
2025-06-22 02:43:59.880[1750531439880] | INFO  |            | c.c.t.t.s.i.SignalProcessingServiceImpl - 信号处理状态已更新: signalId=test-signal-1, success=true, result=HOLD信号，无需执行交易
2025-06-22 02:43:59.991[1750531439991] | INFO  | main       | c.c.t.c.u.DatabaseExecutorTestUtils  - 所有异步数据库操作已完成，耗时：112ms
2025-06-22 02:44:00.088[1750531440088] | INFO  | main       | c.c.t.t.s.SignalValidationService    - 开始验证交易信号 [e25eb9bd]: signalId=test-signal-1, symbol=BTCUSDT, type=BUY
2025-06-22 02:44:00.089[1750531440089] | WARN  | main       | c.c.t.t.s.SignalValidationService    - 交易信号验证失败 [e25eb9bd]: 置信度必须在0.0-1.0之间，当前值: 1.5, signalId=test-signal-1, symbol=BTCUSDT, type=BUY
2025-06-22 02:44:00.091[1750531440091] | INFO  | main       | c.c.t.t.s.SignalValidationService    - 开始验证交易信号 [191b8534]: signalId=test-signal-1, symbol=BTCUSDT, type=HOLD
2025-06-22 02:44:00.092[1750531440092] | WARN  | main       | c.c.t.t.s.SignalValidationService    - 交易信号验证失败 [191b8534]: 信号类型与订单类型不匹配: HOLD / MARKET, signalId=test-signal-1, symbol=BTCUSDT, type=HOLD
2025-06-22 02:44:00.094[1750531440094] | INFO  | main       | c.c.t.t.s.SignalValidationService    - 开始验证交易信号 [90db7a93]: signalId=test-signal-1, symbol=BTCUSDT, type=BUY
2025-06-22 02:44:00.094[1750531440094] | INFO  | main       | c.c.t.t.s.SignalValidationService    - 交易信号验证通过 [90db7a93]: signalId=test-signal-1, symbol=BTCUSDT, type=BUY
2025-06-22 02:44:00.096[1750531440096] | INFO  | main       | c.c.t.t.s.SignalValidationService    - 开始验证交易信号 [c8a070c3]: signalId=, symbol=BTCUSDT, type=BUY
2025-06-22 02:44:00.096[1750531440096] | WARN  | main       | c.c.t.t.s.SignalValidationService    - 交易信号验证失败 [c8a070c3]: 信号ID为空, signalId=, symbol=BTCUSDT, type=BUY
2025-06-22 02:44:00.098[1750531440098] | INFO  | main       | c.c.t.t.s.SignalValidationService    - 开始验证交易信号 [1dc71784]: signalId=test-signal-1, symbol=BTCUSDT, type=BUY
2025-06-22 02:44:00.098[1750531440098] | WARN  | main       | c.c.t.t.s.SignalValidationService    - 交易信号验证失败 [1dc71784]: 限价单必须指定价格, signalId=test-signal-1, symbol=BTCUSDT, type=BUY
2025-06-22 02:44:00.100[1750531440100] | INFO  | main       | c.c.t.t.s.SignalValidationService    - 开始验证交易信号 [e77110d5]: signalId=test-signal-1, symbol=UNSUPPORTED, type=BUY
2025-06-22 02:44:00.100[1750531440100] | WARN  | main       | c.c.t.t.s.SignalValidationService    - 交易信号验证失败 [e77110d5]: 不支持的交易对: UNSUPPORTED, signalId=test-signal-1, symbol=UNSUPPORTED, type=BUY
2025-06-22 02:44:00.102[1750531440102] | INFO  | main       | c.c.t.t.s.SignalValidationService    - 开始验证交易信号 [28a0cbf6]: signalId=test-signal-1, symbol=, type=BUY
2025-06-22 02:44:00.102[1750531440102] | WARN  | main       | c.c.t.t.s.SignalValidationService    - 交易信号验证失败 [28a0cbf6]: 交易对为空, signalId=test-signal-1, symbol=, type=BUY
2025-06-22 02:44:00.170[1750531440170] | INFO  | main       | c.c.t.t.s.TradeExecutionService      - 处理订单状态更新: orderId=123456, symbol=BTCUSDT, status=FILLED
2025-06-22 02:44:00.172[1750531440172] | INFO  | main       | c.c.t.t.s.TradeExecutionService      - 查询订单状态: symbol=BTCUSDT, orderId=123456, clientOrderId=test123
2025-06-22 02:44:00.177[1750531440177] | INFO  | main       | c.c.t.t.s.TradeExecutionService      - 处理订单执行结果: orderId=123456, symbol=BTCUSDT, status=NEW
2025-06-22 02:44:00.180[1750531440180] | INFO  | main       | c.c.t.t.s.TradeExecutionService      - 取消订单: orderId=123456, clientOrderId=test123
2025-06-22 02:44:00.184[1750531440184] | INFO  | main       | c.c.t.t.s.TradeExecutionService      - 执行市价单: orderId=123456, symbol=BTCUSDT, side=BUY, quantity=1.0
2025-06-22 03:50:51.463[1750535451463] | INFO  | main       | c.c.t.t.c.TradingSignalConsumer      - 收到交易信号: signalId=test-signal-1, symbol=BTCUSDT, type=BUY, topic=strategy.signal, partition=0, offset=0
2025-06-22 03:50:51.475[1750535451475] | INFO  | main       | c.c.t.t.c.TradingSignalConsumer      - 交易信号处理成功: signalId=test-signal-1, message=处理成功
2025-06-22 03:50:51.518[1750535451518] | INFO  | main       | c.c.t.t.c.TradingSignalConsumer      - 收到交易信号: signalId=test-signal-1, symbol=BTCUSDT, type=BUY, topic=strategy.signal, partition=0, offset=0
2025-06-22 03:50:51.520[1750535451520] | ERROR | main       | c.c.t.t.c.TradingSignalConsumer      - 处理交易信号异常: signalId=test-signal-1, error=处理异常, stacktrace=java.lang.RuntimeException: 处理异常
	at com.crypto.trading.trade.consumer.TradingSignalConsumer.consume(TradingSignalConsumer.java:108)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at com.crypto.trading.trade.consumer.TradingSignalConsumer.consume(TradingSignalConsumer.java:69)
	at com.crypto.trading.trade.consumer.TradingSignalConsumerTest.testConsumeWithProcessingException(TradingSignalConsumerTest.java:166)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.junit.platform.commons.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:728)
	at org.junit.jupiter.engine.execution.MethodInvocation.proceed(MethodInvocation.java:60)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$ValidatingInvocation.proceed(InvocationInterceptorChain.java:131)
	at org.junit.jupiter.engine.extension.TimeoutExtension.intercept(TimeoutExtension.java:156)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestableMethod(TimeoutExtension.java:147)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestMethod(TimeoutExtension.java:86)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker$ReflectiveInterceptorCall.lambda$ofVoidMethod$0(InterceptingExecutableInvoker.java:103)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.lambda$invoke$0(InterceptingExecutableInvoker.java:93)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$InterceptedInvocation.proceed(InvocationInterceptorChain.java:106)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.proceed(InvocationInterceptorChain.java:64)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.chainAndInvoke(InvocationInterceptorChain.java:45)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.invoke(InvocationInterceptorChain.java:37)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:92)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:86)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$invokeTestMethod$7(TestMethodTestDescriptor.java:218)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.invokeTestMethod(TestMethodTestDescriptor.java:214)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:139)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:69)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.apache.maven.surefire.junitplatform.LazyLauncher.execute(LazyLauncher.java:56)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.execute(JUnitPlatformProvider.java:184)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invokeAllTests(JUnitPlatformProvider.java:148)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invoke(JUnitPlatformProvider.java:122)
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:385)
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:162)
	at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:507)
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:495)

2025-06-22 03:50:51.528[1750535451528] | INFO  | main       | c.c.t.t.c.TradingSignalConsumer      - 收到交易信号: signalId=test-signal-1, symbol=BTCUSDT, type=BUY, topic=strategy.signal, partition=0, offset=0
2025-06-22 03:50:51.529[1750535451529] | WARN  | main       | c.c.t.t.c.TradingSignalConsumer      - 无效的交易信号: signalId=test-signal-1, error=无效的信号
2025-06-22 03:50:51.535[1750535451535] | WARN  | main       | c.c.t.t.c.TradingSignalConsumer      - 收到空的交易信号消息: topic=strategy.signal, partition=0, offset=0, key=key1
2025-06-22 03:50:51.934[1750535451934] | INFO  | main       | c.c.t.t.e.BinanceTradeExecutor       - 执行市价单: orderId=123456, symbol=BTCUSDT, side=BUY, quantity=1.0
2025-06-22 03:50:51.937[1750535451937] | INFO  | main       | c.c.trading.common.util.ThreadUtil   - 创建线程池成功: corePoolSize=4, maximumPoolSize=13, queueCapacity=1024, threadNamePrefix=thread-pool-
2025-06-22 03:50:51.963[1750535451963] | INFO  | main       | c.c.t.t.e.BinanceTradeExecutor       - 查询订单状态: symbol=BTCUSDT, orderId=123456, clientOrderId=test123
2025-06-22 03:50:51.963[1750535451963] | INFO  | main       | c.c.t.t.e.BinanceTradeExecutor       - 试运行模式，返回模拟订单状态
2025-06-22 03:50:51.968[1750535451968] | INFO  | main       | c.c.t.t.e.BinanceTradeExecutor       - 执行市价单: orderId=123456, symbol=BTCUSDT, side=BUY, quantity=1.0
2025-06-22 03:50:51.968[1750535451968] | INFO  | main       | c.c.t.t.e.BinanceTradeExecutor       - 试运行模式，不实际下单: OrderEntity(id=1, orderId=123456, clientOrderId=test123, symbol=BTCUSDT, side=BUY, positionSide=BOTH, orderType=MARKET, quantity=1.0, price=50000.0, executedQuantity=null, executedPrice=null, status=NEW, strategy=null, signalId=null, errorCode=null, errorMessage=null, createdTime=1750535451967, updatedTime=null, executedTime=null, deleted=null, timeInForce=null, remark=null)
2025-06-22 03:50:52.023[1750535452023] | INFO  | main       | c.c.t.t.e.BinanceTradeExecutor       - 查询订单状态: symbol=BTCUSDT, orderId=123456, clientOrderId=test123
2025-06-22 03:50:52.029[1750535452029] | INFO  | main       | c.c.t.t.e.BinanceTradeExecutor       - 执行市价单: orderId=123456, symbol=BTCUSDT, side=BUY, quantity=1.0
2025-06-22 03:50:52.034[1750535452034] | INFO  | main       | c.c.t.t.e.BinanceTradeExecutor       - 取消订单: orderId=123456, symbol=BTCUSDT, clientOrderId=test123
2025-06-22 03:50:52.152[1750535452152] | INFO  | main       | c.c.t.t.p.i.KafkaTradeResultProducer - 发送订单执行结果到Kafka: orderId=1, symbol=BTCUSDT, status=FILLED
2025-06-22 03:50:52.218[1750535452218] | ERROR |            | c.c.t.t.p.i.KafkaTradeResultProducer - 订单执行结果发送失败: orderId=1, error=java.lang.RuntimeException: 订单执行结果发送失败
java.util.concurrent.CompletionException: java.lang.RuntimeException: 订单执行结果发送失败
	at java.base/java.util.concurrent.CompletableFuture.reportJoin(CompletableFuture.java:413)
	at java.base/java.util.concurrent.CompletableFuture.join(CompletableFuture.java:2118)
	at com.crypto.trading.trade.producer.impl.KafkaTradeResultProducer.lambda$sendOrderExecutionResult$0(KafkaTradeResultProducer.java:84)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.VirtualThread.run(VirtualThread.java:329)
Caused by: java.lang.RuntimeException: 订单执行结果发送失败
	at com.crypto.trading.trade.producer.KafkaTradeResultProducerTest.testSendOrderExecutionResult_Failure(KafkaTradeResultProducerTest.java:158)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.junit.platform.commons.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:728)
	at org.junit.jupiter.engine.execution.MethodInvocation.proceed(MethodInvocation.java:60)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$ValidatingInvocation.proceed(InvocationInterceptorChain.java:131)
	at org.junit.jupiter.engine.extension.TimeoutExtension.intercept(TimeoutExtension.java:156)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestableMethod(TimeoutExtension.java:147)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestMethod(TimeoutExtension.java:86)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker$ReflectiveInterceptorCall.lambda$ofVoidMethod$0(InterceptingExecutableInvoker.java:103)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.lambda$invoke$0(InterceptingExecutableInvoker.java:93)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$InterceptedInvocation.proceed(InvocationInterceptorChain.java:106)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.proceed(InvocationInterceptorChain.java:64)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.chainAndInvoke(InvocationInterceptorChain.java:45)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.invoke(InvocationInterceptorChain.java:37)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:92)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:86)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$invokeTestMethod$7(TestMethodTestDescriptor.java:218)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.invokeTestMethod(TestMethodTestDescriptor.java:214)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:139)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:69)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.apache.maven.surefire.junitplatform.LazyLauncher.execute(LazyLauncher.java:56)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.execute(JUnitPlatformProvider.java:184)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invokeAllTests(JUnitPlatformProvider.java:148)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invoke(JUnitPlatformProvider.java:122)
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:385)
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:162)
	at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:507)
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:495)
2025-06-22 03:50:52.277[1750535452277] | INFO  | main       | c.c.t.t.p.i.KafkaTradeResultProducer - 发送订单执行错误到Kafka: clientOrderId=TEST123, symbol=BTCUSDT, errorCode=400, errorMessage=Invalid parameter
2025-06-22 03:50:52.286[1750535452286] | INFO  |            | c.c.t.t.p.i.KafkaTradeResultProducer - 订单执行错误发送成功: topic=null, partition=0, offset=0
2025-06-22 03:50:52.289[1750535452289] | INFO  | main       | c.c.t.t.p.i.KafkaTradeResultProducer - 发送订单状态更新到Kafka: orderId=1, symbol=BTCUSDT, status=FILLED
2025-06-22 03:50:52.292[1750535452292] | INFO  |            | c.c.t.t.p.i.KafkaTradeResultProducer - 订单状态更新发送成功: topic=null, partition=0, offset=0
2025-06-22 03:50:52.296[1750535452296] | INFO  | main       | c.c.t.t.p.i.KafkaTradeResultProducer - 批量发送订单执行结果到Kafka
2025-06-22 03:50:52.296[1750535452296] | INFO  |            | c.c.t.t.p.i.KafkaTradeResultProducer - 发送订单执行结果到Kafka: orderId=1, symbol=BTCUSDT, status=FILLED
2025-06-22 03:50:52.297[1750535452297] | INFO  |            | c.c.t.t.p.i.KafkaTradeResultProducer - 发送订单执行结果到Kafka: orderId=2, symbol=BTCUSDT, status=FILLED
2025-06-22 03:50:52.297[1750535452297] | INFO  |            | c.c.t.t.p.i.KafkaTradeResultProducer - 订单执行结果发送成功: topic=null, partition=0, offset=0
2025-06-22 03:50:52.298[1750535452298] | INFO  |            | c.c.t.t.p.i.KafkaTradeResultProducer - 订单执行结果发送成功: topic=null, partition=0, offset=0
2025-06-22 03:50:52.298[1750535452298] | INFO  |            | c.c.t.t.p.i.KafkaTradeResultProducer - 批量发送订单执行结果完成，共发送2条消息
2025-06-22 03:50:52.302[1750535452302] | INFO  | main       | c.c.t.t.p.i.KafkaTradeResultProducer - 发送订单执行结果到Kafka: orderId=1, symbol=BTCUSDT, status=FILLED
2025-06-22 03:50:52.303[1750535452303] | INFO  |            | c.c.t.t.p.i.KafkaTradeResultProducer - 订单执行结果发送成功: topic=null, partition=0, offset=0
2025-06-22 03:50:52.422[1750535452422] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 检查交易信号风险: TradeSignal{signalId='test-signal-003', symbol='BTCUSDT', strategy='null', signalType='null', side='null', positionSide='null', confidence=0.0, price=15000.0, quantity=1.0, stopPrice=null, takeProfit=null, orderType='null', timeInForce='null', leverageLevel=null, generatedTime=1750535452422}
2025-06-22 03:50:52.428[1750535452428] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 风控检查已禁用，跳过检查
2025-06-22 03:50:52.434[1750535452434] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 检查订单风险: orderId=test-order-002
2025-06-22 03:50:52.434[1750535452434] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 检查交易金额风险: symbol=BTCUSDT, amount=15000.00
2025-06-22 03:50:52.434[1750535452434] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 交易金额检查结果: amount=15000.00, limit=10000, pass=false
2025-06-22 03:50:52.435[1750535452435] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 记录风控检查: signalId=test-strategy-002, ruleId=AMOUNT, triggered=true
2025-06-22 03:50:52.446[1750535452446] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 检查订单风险: orderId=test-order-001
2025-06-22 03:50:52.446[1750535452446] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 检查交易金额风险: symbol=BTCUSDT, amount=5000.00
2025-06-22 03:50:52.446[1750535452446] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 交易金额检查结果: amount=5000.00, limit=10000, pass=true
2025-06-22 03:50:52.446[1750535452446] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 记录风控检查: signalId=test-strategy-001, ruleId=ALL_CHECKS, triggered=false
2025-06-22 03:50:52.446[1750535452446] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 订单通过风控检查
2025-06-22 03:50:52.449[1750535452449] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 检查交易频率: symbol=BTCUSDT, timeWindow=60000ms
2025-06-22 03:50:52.451[1750535452451] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 交易频率检查结果: count=1, limit=10, pass=true
2025-06-22 03:50:52.451[1750535452451] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 检查交易频率: symbol=BTCUSDT, timeWindow=60000ms
2025-06-22 03:50:52.451[1750535452451] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 交易频率检查结果: count=2, limit=10, pass=true
2025-06-22 03:50:52.451[1750535452451] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 检查交易频率: symbol=BTCUSDT, timeWindow=60000ms
2025-06-22 03:50:52.451[1750535452451] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 交易频率检查结果: count=3, limit=10, pass=true
2025-06-22 03:50:52.451[1750535452451] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 检查交易频率: symbol=BTCUSDT, timeWindow=60000ms
2025-06-22 03:50:52.451[1750535452451] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 交易频率检查结果: count=11, limit=10, pass=false
2025-06-22 03:50:52.455[1750535452455] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 检查交易金额风险: symbol=BTCUSDT, amount=5000.0
2025-06-22 03:50:52.455[1750535452455] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 交易金额检查结果: amount=5000.0, limit=10000, pass=true
2025-06-22 03:50:52.455[1750535452455] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 检查交易金额风险: symbol=BTCUSDT, amount=15000.0
2025-06-22 03:50:52.456[1750535452456] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 交易金额检查结果: amount=15000.0, limit=10000, pass=false
2025-06-22 03:50:52.458[1750535452458] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 记录风控检查: signalId=test-signal-001, ruleId=AMOUNT, triggered=true
2025-06-22 03:50:52.461[1750535452461] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 检查交易信号风险: TradeSignal{signalId='test-signal-001', symbol='BTCUSDT', strategy='null', signalType='null', side='null', positionSide='null', confidence=0.0, price=5000.0, quantity=1.0, stopPrice=null, takeProfit=null, orderType='null', timeInForce='null', leverageLevel=null, generatedTime=1750535452461}
2025-06-22 03:50:52.462[1750535452462] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 检查交易频率: symbol=BTCUSDT, timeWindow=60000ms
2025-06-22 03:50:52.462[1750535452462] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 交易频率检查结果: count=1, limit=10, pass=true
2025-06-22 03:50:52.462[1750535452462] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 检查交易金额风险: symbol=BTCUSDT, amount=5000.00
2025-06-22 03:50:52.462[1750535452462] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 交易金额检查结果: amount=5000.00, limit=10000, pass=true
2025-06-22 03:50:52.462[1750535452462] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 记录风控检查: signalId=test-signal-001, ruleId=ALL_CHECKS, triggered=false
2025-06-22 03:50:52.462[1750535452462] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 交易信号通过风控检查
2025-06-22 03:50:52.469[1750535452469] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 检查交易信号风险: TradeSignal{signalId='test-signal-002', symbol='BTCUSDT', strategy='null', signalType='null', side='null', positionSide='null', confidence=0.0, price=15000.0, quantity=1.0, stopPrice=null, takeProfit=null, orderType='null', timeInForce='null', leverageLevel=null, generatedTime=1750535452469}
2025-06-22 03:50:52.469[1750535452469] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 检查交易频率: symbol=BTCUSDT, timeWindow=60000ms
2025-06-22 03:50:52.469[1750535452469] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 交易频率检查结果: count=1, limit=10, pass=true
2025-06-22 03:50:52.469[1750535452469] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 检查交易金额风险: symbol=BTCUSDT, amount=15000.00
2025-06-22 03:50:52.470[1750535452470] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 交易金额检查结果: amount=15000.00, limit=10000, pass=false
2025-06-22 03:50:52.470[1750535452470] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 记录风控检查: signalId=test-signal-002, ruleId=AMOUNT, triggered=true
2025-06-22 03:50:52.599[1750535452599] | INFO  |            | c.c.t.t.s.i.SignalProcessingServiceImpl - 交易信号已保存到数据库 [test-validation-id]: signalId=test-signal-1, id=null
2025-06-22 03:50:52.601[1750535452601] | INFO  | main       | c.c.t.t.s.i.SignalProcessingServiceImpl - 交易执行功能未启用 [test-validation-id]: signalId=test-signal-1
2025-06-22 03:50:52.603[1750535452603] | INFO  | main       | c.c.t.c.u.DatabaseExecutorTestUtils  - 等待异步数据库操作完成，超时时间：5000ms，轮询间隔：100ms
2025-06-22 03:50:52.747[1750535452747] | INFO  |            | c.c.t.t.s.i.SignalProcessingServiceImpl - 信号处理状态已更新: signalId=test-signal-1, success=false, result=交易执行功能未启用
2025-06-22 03:50:52.753[1750535452753] | INFO  | main       | c.c.t.c.u.DatabaseExecutorTestUtils  - 所有异步数据库操作已完成，耗时：105ms
2025-06-22 03:50:52.762[1750535452762] | INFO  |            | c.c.t.t.s.i.SignalProcessingServiceImpl - 交易信号已保存到数据库 [test-validation-id]: signalId=test-signal-1, id=null
2025-06-22 03:50:52.762[1750535452762] | INFO  | main       | c.c.t.t.s.i.SignalProcessingServiceImpl - 交易信号已提交执行 [test-validation-id]: signalId=test-signal-1
2025-06-22 03:50:52.765[1750535452765] | INFO  | main       | c.c.t.c.u.DatabaseExecutorTestUtils  - 等待异步数据库操作完成，超时时间：5000ms，轮询间隔：100ms
2025-06-22 03:50:52.767[1750535452767] | INFO  |            | c.c.t.t.s.i.SignalProcessingServiceImpl - 信号处理状态已更新: signalId=test-signal-1, success=true, result=处理成功
2025-06-22 03:50:52.879[1750535452879] | INFO  | main       | c.c.t.c.u.DatabaseExecutorTestUtils  - 所有异步数据库操作已完成，耗时：114ms
2025-06-22 03:50:52.882[1750535452882] | WARN  | main       | c.c.t.t.s.i.SignalProcessingServiceImpl - 交易信号已存在 [test-validation-id]: signalId=test-signal-1
2025-06-22 03:50:52.888[1750535452888] | INFO  |            | c.c.t.t.s.i.SignalProcessingServiceImpl - 交易信号已保存到数据库 [test-validation-id]: signalId=test-signal-1, id=null
2025-06-22 03:50:52.888[1750535452888] | INFO  | main       | c.c.t.t.s.i.SignalProcessingServiceImpl - HOLD信号已处理 [test-validation-id]: signalId=test-signal-1
2025-06-22 03:50:52.888[1750535452888] | INFO  | main       | c.c.t.c.u.DatabaseExecutorTestUtils  - 等待异步数据库操作完成，超时时间：5000ms，轮询间隔：100ms
2025-06-22 03:50:52.889[1750535452889] | INFO  |            | c.c.t.t.s.i.SignalProcessingServiceImpl - 信号处理状态已更新: signalId=test-signal-1, success=true, result=HOLD信号，无需执行交易
2025-06-22 03:50:52.990[1750535452990] | INFO  | main       | c.c.t.c.u.DatabaseExecutorTestUtils  - 所有异步数据库操作已完成，耗时：102ms
2025-06-22 03:50:53.056[1750535453056] | INFO  | main       | c.c.t.t.s.SignalValidationService    - 开始验证交易信号 [140895aa]: signalId=test-signal-1, symbol=BTCUSDT, type=BUY
2025-06-22 03:50:53.057[1750535453057] | WARN  | main       | c.c.t.t.s.SignalValidationService    - 交易信号验证失败 [140895aa]: 置信度必须在0.0-1.0之间，当前值: 1.5, signalId=test-signal-1, symbol=BTCUSDT, type=BUY
2025-06-22 03:50:53.059[1750535453059] | INFO  | main       | c.c.t.t.s.SignalValidationService    - 开始验证交易信号 [8b0ca281]: signalId=test-signal-1, symbol=BTCUSDT, type=HOLD
2025-06-22 03:50:53.059[1750535453059] | WARN  | main       | c.c.t.t.s.SignalValidationService    - 交易信号验证失败 [8b0ca281]: 信号类型与订单类型不匹配: HOLD / MARKET, signalId=test-signal-1, symbol=BTCUSDT, type=HOLD
2025-06-22 03:50:53.062[1750535453062] | INFO  | main       | c.c.t.t.s.SignalValidationService    - 开始验证交易信号 [89e81b57]: signalId=test-signal-1, symbol=BTCUSDT, type=BUY
2025-06-22 03:50:53.062[1750535453062] | INFO  | main       | c.c.t.t.s.SignalValidationService    - 交易信号验证通过 [89e81b57]: signalId=test-signal-1, symbol=BTCUSDT, type=BUY
2025-06-22 03:50:53.064[1750535453064] | INFO  | main       | c.c.t.t.s.SignalValidationService    - 开始验证交易信号 [35342be2]: signalId=, symbol=BTCUSDT, type=BUY
2025-06-22 03:50:53.064[1750535453064] | WARN  | main       | c.c.t.t.s.SignalValidationService    - 交易信号验证失败 [35342be2]: 信号ID为空, signalId=, symbol=BTCUSDT, type=BUY
2025-06-22 03:50:53.066[1750535453066] | INFO  | main       | c.c.t.t.s.SignalValidationService    - 开始验证交易信号 [e58d0409]: signalId=test-signal-1, symbol=BTCUSDT, type=BUY
2025-06-22 03:50:53.067[1750535453067] | WARN  | main       | c.c.t.t.s.SignalValidationService    - 交易信号验证失败 [e58d0409]: 限价单必须指定价格, signalId=test-signal-1, symbol=BTCUSDT, type=BUY
2025-06-22 03:50:53.069[1750535453069] | INFO  | main       | c.c.t.t.s.SignalValidationService    - 开始验证交易信号 [a0cae9ec]: signalId=test-signal-1, symbol=UNSUPPORTED, type=BUY
2025-06-22 03:50:53.069[1750535453069] | WARN  | main       | c.c.t.t.s.SignalValidationService    - 交易信号验证失败 [a0cae9ec]: 不支持的交易对: UNSUPPORTED, signalId=test-signal-1, symbol=UNSUPPORTED, type=BUY
2025-06-22 03:50:53.072[1750535453072] | INFO  | main       | c.c.t.t.s.SignalValidationService    - 开始验证交易信号 [557bd39b]: signalId=test-signal-1, symbol=, type=BUY
2025-06-22 03:50:53.072[1750535453072] | WARN  | main       | c.c.t.t.s.SignalValidationService    - 交易信号验证失败 [557bd39b]: 交易对为空, signalId=test-signal-1, symbol=, type=BUY
2025-06-22 03:50:53.143[1750535453143] | INFO  | main       | c.c.t.t.s.TradeExecutionService      - 处理订单状态更新: orderId=123456, symbol=BTCUSDT, status=FILLED
2025-06-22 03:50:53.147[1750535453147] | INFO  | main       | c.c.t.t.s.TradeExecutionService      - 查询订单状态: symbol=BTCUSDT, orderId=123456, clientOrderId=test123
2025-06-22 03:50:53.152[1750535453152] | INFO  | main       | c.c.t.t.s.TradeExecutionService      - 处理订单执行结果: orderId=123456, symbol=BTCUSDT, status=NEW
2025-06-22 03:50:53.155[1750535453155] | INFO  | main       | c.c.t.t.s.TradeExecutionService      - 取消订单: orderId=123456, clientOrderId=test123
2025-06-22 03:50:53.159[1750535453159] | INFO  | main       | c.c.t.t.s.TradeExecutionService      - 执行市价单: orderId=123456, symbol=BTCUSDT, side=BUY, quantity=1.0
2025-06-22 18:50:31.077[1750589431077] | INFO  | main       | c.c.t.t.c.TradingSignalConsumer      - 收到交易信号: signalId=test-signal-1, symbol=BTCUSDT, type=BUY, topic=strategy.signal, partition=0, offset=0
2025-06-22 18:50:31.084[1750589431084] | INFO  | main       | c.c.t.t.c.TradingSignalConsumer      - 交易信号处理成功: signalId=test-signal-1, message=处理成功
2025-06-22 18:50:31.118[1750589431118] | INFO  | main       | c.c.t.t.c.TradingSignalConsumer      - 收到交易信号: signalId=test-signal-1, symbol=BTCUSDT, type=BUY, topic=strategy.signal, partition=0, offset=0
2025-06-22 18:50:31.120[1750589431120] | ERROR | main       | c.c.t.t.c.TradingSignalConsumer      - 处理交易信号异常: signalId=test-signal-1, error=处理异常, stacktrace=java.lang.RuntimeException: 处理异常
	at com.crypto.trading.trade.consumer.TradingSignalConsumer.consume(TradingSignalConsumer.java:108)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at com.crypto.trading.trade.consumer.TradingSignalConsumer.consume(TradingSignalConsumer.java:69)
	at com.crypto.trading.trade.consumer.TradingSignalConsumerTest.testConsumeWithProcessingException(TradingSignalConsumerTest.java:166)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.junit.platform.commons.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:728)
	at org.junit.jupiter.engine.execution.MethodInvocation.proceed(MethodInvocation.java:60)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$ValidatingInvocation.proceed(InvocationInterceptorChain.java:131)
	at org.junit.jupiter.engine.extension.TimeoutExtension.intercept(TimeoutExtension.java:156)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestableMethod(TimeoutExtension.java:147)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestMethod(TimeoutExtension.java:86)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker$ReflectiveInterceptorCall.lambda$ofVoidMethod$0(InterceptingExecutableInvoker.java:103)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.lambda$invoke$0(InterceptingExecutableInvoker.java:93)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$InterceptedInvocation.proceed(InvocationInterceptorChain.java:106)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.proceed(InvocationInterceptorChain.java:64)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.chainAndInvoke(InvocationInterceptorChain.java:45)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.invoke(InvocationInterceptorChain.java:37)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:92)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:86)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$invokeTestMethod$7(TestMethodTestDescriptor.java:218)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.invokeTestMethod(TestMethodTestDescriptor.java:214)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:139)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:69)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.apache.maven.surefire.junitplatform.LazyLauncher.execute(LazyLauncher.java:56)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.execute(JUnitPlatformProvider.java:184)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invokeAllTests(JUnitPlatformProvider.java:148)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invoke(JUnitPlatformProvider.java:122)
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:385)
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:162)
	at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:507)
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:495)

2025-06-22 18:50:31.128[1750589431128] | INFO  | main       | c.c.t.t.c.TradingSignalConsumer      - 收到交易信号: signalId=test-signal-1, symbol=BTCUSDT, type=BUY, topic=strategy.signal, partition=0, offset=0
2025-06-22 18:50:31.128[1750589431128] | WARN  | main       | c.c.t.t.c.TradingSignalConsumer      - 无效的交易信号: signalId=test-signal-1, error=无效的信号
2025-06-22 18:50:31.133[1750589431133] | WARN  | main       | c.c.t.t.c.TradingSignalConsumer      - 收到空的交易信号消息: topic=strategy.signal, partition=0, offset=0, key=key1
2025-06-22 18:50:31.542[1750589431542] | INFO  | main       | c.c.t.t.e.BinanceTradeExecutor       - 执行市价单: orderId=123456, symbol=BTCUSDT, side=BUY, quantity=1.0
2025-06-22 18:50:31.548[1750589431548] | INFO  | main       | c.c.trading.common.util.ThreadUtil   - 创建线程池成功: corePoolSize=4, maximumPoolSize=13, queueCapacity=1024, threadNamePrefix=thread-pool-
2025-06-22 18:50:31.577[1750589431577] | INFO  | main       | c.c.t.t.e.BinanceTradeExecutor       - 查询订单状态: symbol=BTCUSDT, orderId=123456, clientOrderId=test123
2025-06-22 18:50:31.577[1750589431577] | INFO  | main       | c.c.t.t.e.BinanceTradeExecutor       - 试运行模式，返回模拟订单状态
2025-06-22 18:50:31.585[1750589431585] | INFO  | main       | c.c.t.t.e.BinanceTradeExecutor       - 执行市价单: orderId=123456, symbol=BTCUSDT, side=BUY, quantity=1.0
2025-06-22 18:50:31.585[1750589431585] | INFO  | main       | c.c.t.t.e.BinanceTradeExecutor       - 试运行模式，不实际下单: OrderEntity(id=1, orderId=123456, clientOrderId=test123, symbol=BTCUSDT, side=BUY, positionSide=BOTH, orderType=MARKET, quantity=1.0, price=50000.0, executedQuantity=null, executedPrice=null, status=NEW, strategy=null, signalId=null, errorCode=null, errorMessage=null, createdTime=1750589431583, updatedTime=null, executedTime=null, deleted=null, timeInForce=null, remark=null)
2025-06-22 18:50:31.649[1750589431649] | INFO  | main       | c.c.t.t.e.BinanceTradeExecutor       - 查询订单状态: symbol=BTCUSDT, orderId=123456, clientOrderId=test123
2025-06-22 18:50:31.655[1750589431655] | INFO  | main       | c.c.t.t.e.BinanceTradeExecutor       - 执行市价单: orderId=123456, symbol=BTCUSDT, side=BUY, quantity=1.0
2025-06-22 18:50:31.661[1750589431661] | INFO  | main       | c.c.t.t.e.BinanceTradeExecutor       - 取消订单: orderId=123456, symbol=BTCUSDT, clientOrderId=test123
2025-06-22 18:50:31.776[1750589431776] | INFO  | main       | c.c.t.t.p.i.KafkaTradeResultProducer - 发送订单执行结果到Kafka: orderId=1, symbol=BTCUSDT, status=FILLED
2025-06-22 18:50:31.865[1750589431865] | ERROR |            | c.c.t.t.p.i.KafkaTradeResultProducer - 订单执行结果发送失败: orderId=1, error=java.lang.RuntimeException: 订单执行结果发送失败
java.util.concurrent.CompletionException: java.lang.RuntimeException: 订单执行结果发送失败
	at java.base/java.util.concurrent.CompletableFuture.reportJoin(CompletableFuture.java:413)
	at java.base/java.util.concurrent.CompletableFuture.join(CompletableFuture.java:2118)
	at com.crypto.trading.trade.producer.impl.KafkaTradeResultProducer.lambda$sendOrderExecutionResult$0(KafkaTradeResultProducer.java:84)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.VirtualThread.run(VirtualThread.java:329)
Caused by: java.lang.RuntimeException: 订单执行结果发送失败
	at com.crypto.trading.trade.producer.KafkaTradeResultProducerTest.testSendOrderExecutionResult_Failure(KafkaTradeResultProducerTest.java:158)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.junit.platform.commons.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:728)
	at org.junit.jupiter.engine.execution.MethodInvocation.proceed(MethodInvocation.java:60)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$ValidatingInvocation.proceed(InvocationInterceptorChain.java:131)
	at org.junit.jupiter.engine.extension.TimeoutExtension.intercept(TimeoutExtension.java:156)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestableMethod(TimeoutExtension.java:147)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestMethod(TimeoutExtension.java:86)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker$ReflectiveInterceptorCall.lambda$ofVoidMethod$0(InterceptingExecutableInvoker.java:103)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.lambda$invoke$0(InterceptingExecutableInvoker.java:93)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$InterceptedInvocation.proceed(InvocationInterceptorChain.java:106)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.proceed(InvocationInterceptorChain.java:64)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.chainAndInvoke(InvocationInterceptorChain.java:45)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.invoke(InvocationInterceptorChain.java:37)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:92)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:86)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$invokeTestMethod$7(TestMethodTestDescriptor.java:218)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.invokeTestMethod(TestMethodTestDescriptor.java:214)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:139)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:69)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.apache.maven.surefire.junitplatform.LazyLauncher.execute(LazyLauncher.java:56)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.execute(JUnitPlatformProvider.java:184)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invokeAllTests(JUnitPlatformProvider.java:148)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invoke(JUnitPlatformProvider.java:122)
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:385)
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:162)
	at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:507)
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:495)
2025-06-22 18:50:31.924[1750589431924] | INFO  | main       | c.c.t.t.p.i.KafkaTradeResultProducer - 发送订单执行错误到Kafka: clientOrderId=TEST123, symbol=BTCUSDT, errorCode=400, errorMessage=Invalid parameter
2025-06-22 18:50:31.932[1750589431932] | INFO  |            | c.c.t.t.p.i.KafkaTradeResultProducer - 订单执行错误发送成功: topic=null, partition=0, offset=0
2025-06-22 18:50:31.936[1750589431936] | INFO  | main       | c.c.t.t.p.i.KafkaTradeResultProducer - 发送订单状态更新到Kafka: orderId=1, symbol=BTCUSDT, status=FILLED
2025-06-22 18:50:31.939[1750589431939] | INFO  |            | c.c.t.t.p.i.KafkaTradeResultProducer - 订单状态更新发送成功: topic=null, partition=0, offset=0
2025-06-22 18:50:31.943[1750589431943] | INFO  | main       | c.c.t.t.p.i.KafkaTradeResultProducer - 批量发送订单执行结果到Kafka
2025-06-22 18:50:31.943[1750589431943] | INFO  |            | c.c.t.t.p.i.KafkaTradeResultProducer - 发送订单执行结果到Kafka: orderId=1, symbol=BTCUSDT, status=FILLED
2025-06-22 18:50:31.944[1750589431944] | INFO  |            | c.c.t.t.p.i.KafkaTradeResultProducer - 发送订单执行结果到Kafka: orderId=2, symbol=BTCUSDT, status=FILLED
2025-06-22 18:50:31.945[1750589431945] | INFO  |            | c.c.t.t.p.i.KafkaTradeResultProducer - 订单执行结果发送成功: topic=null, partition=0, offset=0
2025-06-22 18:50:31.946[1750589431946] | INFO  |            | c.c.t.t.p.i.KafkaTradeResultProducer - 订单执行结果发送成功: topic=null, partition=0, offset=0
2025-06-22 18:50:31.946[1750589431946] | INFO  |            | c.c.t.t.p.i.KafkaTradeResultProducer - 批量发送订单执行结果完成，共发送2条消息
2025-06-22 18:50:31.949[1750589431949] | INFO  | main       | c.c.t.t.p.i.KafkaTradeResultProducer - 发送订单执行结果到Kafka: orderId=1, symbol=BTCUSDT, status=FILLED
2025-06-22 18:50:31.951[1750589431951] | INFO  |            | c.c.t.t.p.i.KafkaTradeResultProducer - 订单执行结果发送成功: topic=null, partition=0, offset=0
2025-06-22 18:50:32.057[1750589432057] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 检查交易信号风险: TradeSignal{signalId='test-signal-003', symbol='BTCUSDT', strategy='null', signalType='null', side='null', positionSide='null', confidence=0.0, price=15000.0, quantity=1.0, stopPrice=null, takeProfit=null, orderType='null', timeInForce='null', leverageLevel=null, generatedTime=1750589432057}
2025-06-22 18:50:32.064[1750589432064] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 风控检查已禁用，跳过检查
2025-06-22 18:50:32.075[1750589432075] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 检查订单风险: orderId=test-order-002
2025-06-22 18:50:32.076[1750589432076] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 检查交易金额风险: symbol=BTCUSDT, amount=15000.00
2025-06-22 18:50:32.077[1750589432077] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 交易金额检查结果: amount=15000.00, limit=10000, pass=false
2025-06-22 18:50:32.077[1750589432077] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 记录风控检查: signalId=test-strategy-002, ruleId=AMOUNT, triggered=true
2025-06-22 18:50:32.083[1750589432083] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 检查订单风险: orderId=test-order-001
2025-06-22 18:50:32.083[1750589432083] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 检查交易金额风险: symbol=BTCUSDT, amount=5000.00
2025-06-22 18:50:32.083[1750589432083] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 交易金额检查结果: amount=5000.00, limit=10000, pass=true
2025-06-22 18:50:32.083[1750589432083] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 记录风控检查: signalId=test-strategy-001, ruleId=ALL_CHECKS, triggered=false
2025-06-22 18:50:32.084[1750589432084] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 订单通过风控检查
2025-06-22 18:50:32.087[1750589432087] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 检查交易频率: symbol=BTCUSDT, timeWindow=60000ms
2025-06-22 18:50:32.089[1750589432089] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 交易频率检查结果: count=1, limit=10, pass=true
2025-06-22 18:50:32.089[1750589432089] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 检查交易频率: symbol=BTCUSDT, timeWindow=60000ms
2025-06-22 18:50:32.089[1750589432089] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 交易频率检查结果: count=2, limit=10, pass=true
2025-06-22 18:50:32.089[1750589432089] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 检查交易频率: symbol=BTCUSDT, timeWindow=60000ms
2025-06-22 18:50:32.089[1750589432089] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 交易频率检查结果: count=3, limit=10, pass=true
2025-06-22 18:50:32.089[1750589432089] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 检查交易频率: symbol=BTCUSDT, timeWindow=60000ms
2025-06-22 18:50:32.089[1750589432089] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 交易频率检查结果: count=11, limit=10, pass=false
2025-06-22 18:50:32.092[1750589432092] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 检查交易金额风险: symbol=BTCUSDT, amount=5000.0
2025-06-22 18:50:32.093[1750589432093] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 交易金额检查结果: amount=5000.0, limit=10000, pass=true
2025-06-22 18:50:32.093[1750589432093] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 检查交易金额风险: symbol=BTCUSDT, amount=15000.0
2025-06-22 18:50:32.093[1750589432093] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 交易金额检查结果: amount=15000.0, limit=10000, pass=false
2025-06-22 18:50:32.095[1750589432095] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 记录风控检查: signalId=test-signal-001, ruleId=AMOUNT, triggered=true
2025-06-22 18:50:32.100[1750589432100] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 检查交易信号风险: TradeSignal{signalId='test-signal-001', symbol='BTCUSDT', strategy='null', signalType='null', side='null', positionSide='null', confidence=0.0, price=5000.0, quantity=1.0, stopPrice=null, takeProfit=null, orderType='null', timeInForce='null', leverageLevel=null, generatedTime=1750589432099}
2025-06-22 18:50:32.100[1750589432100] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 检查交易频率: symbol=BTCUSDT, timeWindow=60000ms
2025-06-22 18:50:32.100[1750589432100] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 交易频率检查结果: count=1, limit=10, pass=true
2025-06-22 18:50:32.100[1750589432100] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 检查交易金额风险: symbol=BTCUSDT, amount=5000.00
2025-06-22 18:50:32.100[1750589432100] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 交易金额检查结果: amount=5000.00, limit=10000, pass=true
2025-06-22 18:50:32.100[1750589432100] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 记录风控检查: signalId=test-signal-001, ruleId=ALL_CHECKS, triggered=false
2025-06-22 18:50:32.100[1750589432100] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 交易信号通过风控检查
2025-06-22 18:50:32.109[1750589432109] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 检查交易信号风险: TradeSignal{signalId='test-signal-002', symbol='BTCUSDT', strategy='null', signalType='null', side='null', positionSide='null', confidence=0.0, price=15000.0, quantity=1.0, stopPrice=null, takeProfit=null, orderType='null', timeInForce='null', leverageLevel=null, generatedTime=1750589432109}
2025-06-22 18:50:32.110[1750589432110] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 检查交易频率: symbol=BTCUSDT, timeWindow=60000ms
2025-06-22 18:50:32.110[1750589432110] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 交易频率检查结果: count=1, limit=10, pass=true
2025-06-22 18:50:32.110[1750589432110] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 检查交易金额风险: symbol=BTCUSDT, amount=15000.00
2025-06-22 18:50:32.110[1750589432110] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 交易金额检查结果: amount=15000.00, limit=10000, pass=false
2025-06-22 18:50:32.110[1750589432110] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 记录风控检查: signalId=test-signal-002, ruleId=AMOUNT, triggered=true
2025-06-22 18:50:32.241[1750589432241] | INFO  |            | c.c.t.t.s.i.SignalProcessingServiceImpl - 交易信号已保存到数据库 [test-validation-id]: signalId=test-signal-1, id=null
2025-06-22 18:50:32.242[1750589432242] | INFO  | main       | c.c.t.t.s.i.SignalProcessingServiceImpl - 交易执行功能未启用 [test-validation-id]: signalId=test-signal-1
2025-06-22 18:50:32.242[1750589432242] | INFO  | main       | c.c.t.c.u.DatabaseExecutorTestUtils  - 等待异步数据库操作完成，超时时间：5000ms，轮询间隔：100ms
2025-06-22 18:50:32.442[1750589432442] | INFO  |            | c.c.t.t.s.i.SignalProcessingServiceImpl - 信号处理状态已更新: signalId=test-signal-1, success=false, result=交易执行功能未启用
2025-06-22 18:50:32.457[1750589432457] | INFO  | main       | c.c.t.c.u.DatabaseExecutorTestUtils  - 所有异步数据库操作已完成，耗时：214ms
2025-06-22 18:50:32.465[1750589432465] | INFO  |            | c.c.t.t.s.i.SignalProcessingServiceImpl - 交易信号已保存到数据库 [test-validation-id]: signalId=test-signal-1, id=null
2025-06-22 18:50:32.465[1750589432465] | INFO  | main       | c.c.t.t.s.i.SignalProcessingServiceImpl - 交易信号已提交执行 [test-validation-id]: signalId=test-signal-1
2025-06-22 18:50:32.469[1750589432469] | INFO  | main       | c.c.t.c.u.DatabaseExecutorTestUtils  - 等待异步数据库操作完成，超时时间：5000ms，轮询间隔：100ms
2025-06-22 18:50:32.469[1750589432469] | INFO  |            | c.c.t.t.s.i.SignalProcessingServiceImpl - 信号处理状态已更新: signalId=test-signal-1, success=true, result=处理成功
2025-06-22 18:50:32.581[1750589432581] | INFO  | main       | c.c.t.c.u.DatabaseExecutorTestUtils  - 所有异步数据库操作已完成，耗时：112ms
2025-06-22 18:50:32.587[1750589432587] | WARN  | main       | c.c.t.t.s.i.SignalProcessingServiceImpl - 交易信号已存在 [test-validation-id]: signalId=test-signal-1
2025-06-22 18:50:32.594[1750589432594] | INFO  |            | c.c.t.t.s.i.SignalProcessingServiceImpl - 交易信号已保存到数据库 [test-validation-id]: signalId=test-signal-1, id=null
2025-06-22 18:50:32.594[1750589432594] | INFO  | main       | c.c.t.t.s.i.SignalProcessingServiceImpl - HOLD信号已处理 [test-validation-id]: signalId=test-signal-1
2025-06-22 18:50:32.594[1750589432594] | INFO  | main       | c.c.t.c.u.DatabaseExecutorTestUtils  - 等待异步数据库操作完成，超时时间：5000ms，轮询间隔：100ms
2025-06-22 18:50:32.597[1750589432597] | INFO  |            | c.c.t.t.s.i.SignalProcessingServiceImpl - 信号处理状态已更新: signalId=test-signal-1, success=true, result=HOLD信号，无需执行交易
2025-06-22 18:50:32.707[1750589432707] | INFO  | main       | c.c.t.c.u.DatabaseExecutorTestUtils  - 所有异步数据库操作已完成，耗时：113ms
2025-06-22 18:50:32.829[1750589432829] | INFO  | main       | c.c.t.t.s.SignalValidationService    - 开始验证交易信号 [f034a084]: signalId=test-signal-1, symbol=BTCUSDT, type=BUY
2025-06-22 18:50:32.831[1750589432831] | WARN  | main       | c.c.t.t.s.SignalValidationService    - 交易信号验证失败 [f034a084]: 置信度必须在0.0-1.0之间，当前值: 1.5, signalId=test-signal-1, symbol=BTCUSDT, type=BUY
2025-06-22 18:50:32.835[1750589432835] | INFO  | main       | c.c.t.t.s.SignalValidationService    - 开始验证交易信号 [eaaa13b1]: signalId=test-signal-1, symbol=BTCUSDT, type=HOLD
2025-06-22 18:50:32.835[1750589432835] | WARN  | main       | c.c.t.t.s.SignalValidationService    - 交易信号验证失败 [eaaa13b1]: 信号类型与订单类型不匹配: HOLD / MARKET, signalId=test-signal-1, symbol=BTCUSDT, type=HOLD
2025-06-22 18:50:32.839[1750589432839] | INFO  | main       | c.c.t.t.s.SignalValidationService    - 开始验证交易信号 [9842e208]: signalId=test-signal-1, symbol=BTCUSDT, type=BUY
2025-06-22 18:50:32.839[1750589432839] | INFO  | main       | c.c.t.t.s.SignalValidationService    - 交易信号验证通过 [9842e208]: signalId=test-signal-1, symbol=BTCUSDT, type=BUY
2025-06-22 18:50:32.841[1750589432841] | INFO  | main       | c.c.t.t.s.SignalValidationService    - 开始验证交易信号 [c092b145]: signalId=, symbol=BTCUSDT, type=BUY
2025-06-22 18:50:32.842[1750589432842] | WARN  | main       | c.c.t.t.s.SignalValidationService    - 交易信号验证失败 [c092b145]: 信号ID为空, signalId=, symbol=BTCUSDT, type=BUY
2025-06-22 18:50:32.851[1750589432851] | INFO  | main       | c.c.t.t.s.SignalValidationService    - 开始验证交易信号 [f7b5d690]: signalId=test-signal-1, symbol=BTCUSDT, type=BUY
2025-06-22 18:50:32.851[1750589432851] | WARN  | main       | c.c.t.t.s.SignalValidationService    - 交易信号验证失败 [f7b5d690]: 限价单必须指定价格, signalId=test-signal-1, symbol=BTCUSDT, type=BUY
2025-06-22 18:50:32.854[1750589432854] | INFO  | main       | c.c.t.t.s.SignalValidationService    - 开始验证交易信号 [f9b722af]: signalId=test-signal-1, symbol=UNSUPPORTED, type=BUY
2025-06-22 18:50:32.855[1750589432855] | WARN  | main       | c.c.t.t.s.SignalValidationService    - 交易信号验证失败 [f9b722af]: 不支持的交易对: UNSUPPORTED, signalId=test-signal-1, symbol=UNSUPPORTED, type=BUY
2025-06-22 18:50:32.861[1750589432861] | INFO  | main       | c.c.t.t.s.SignalValidationService    - 开始验证交易信号 [8b3a005a]: signalId=test-signal-1, symbol=, type=BUY
2025-06-22 18:50:32.861[1750589432861] | WARN  | main       | c.c.t.t.s.SignalValidationService    - 交易信号验证失败 [8b3a005a]: 交易对为空, signalId=test-signal-1, symbol=, type=BUY
2025-06-22 18:50:32.962[1750589432962] | INFO  | main       | c.c.t.t.s.TradeExecutionService      - 处理订单状态更新: orderId=123456, symbol=BTCUSDT, status=FILLED
2025-06-22 18:50:32.968[1750589432968] | INFO  | main       | c.c.t.t.s.TradeExecutionService      - 查询订单状态: symbol=BTCUSDT, orderId=123456, clientOrderId=test123
2025-06-22 18:50:32.976[1750589432976] | INFO  | main       | c.c.t.t.s.TradeExecutionService      - 处理订单执行结果: orderId=123456, symbol=BTCUSDT, status=NEW
2025-06-22 18:50:32.980[1750589432980] | INFO  | main       | c.c.t.t.s.TradeExecutionService      - 取消订单: orderId=123456, clientOrderId=test123
2025-06-22 18:50:32.987[1750589432987] | INFO  | main       | c.c.t.t.s.TradeExecutionService      - 执行市价单: orderId=123456, symbol=BTCUSDT, side=BUY, quantity=1.0
2025-06-22 18:53:22.422[1750589602422] | INFO  | main       | c.c.t.t.c.TradingSignalConsumer      - 收到交易信号: signalId=test-signal-1, symbol=BTCUSDT, type=BUY, topic=strategy.signal, partition=0, offset=0
2025-06-22 18:53:22.429[1750589602429] | INFO  | main       | c.c.t.t.c.TradingSignalConsumer      - 交易信号处理成功: signalId=test-signal-1, message=处理成功
2025-06-22 18:53:22.462[1750589602462] | INFO  | main       | c.c.t.t.c.TradingSignalConsumer      - 收到交易信号: signalId=test-signal-1, symbol=BTCUSDT, type=BUY, topic=strategy.signal, partition=0, offset=0
2025-06-22 18:53:22.464[1750589602464] | ERROR | main       | c.c.t.t.c.TradingSignalConsumer      - 处理交易信号异常: signalId=test-signal-1, error=处理异常, stacktrace=java.lang.RuntimeException: 处理异常
	at com.crypto.trading.trade.consumer.TradingSignalConsumer.consume(TradingSignalConsumer.java:108)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at com.crypto.trading.trade.consumer.TradingSignalConsumer.consume(TradingSignalConsumer.java:69)
	at com.crypto.trading.trade.consumer.TradingSignalConsumerTest.testConsumeWithProcessingException(TradingSignalConsumerTest.java:166)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.junit.platform.commons.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:728)
	at org.junit.jupiter.engine.execution.MethodInvocation.proceed(MethodInvocation.java:60)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$ValidatingInvocation.proceed(InvocationInterceptorChain.java:131)
	at org.junit.jupiter.engine.extension.TimeoutExtension.intercept(TimeoutExtension.java:156)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestableMethod(TimeoutExtension.java:147)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestMethod(TimeoutExtension.java:86)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker$ReflectiveInterceptorCall.lambda$ofVoidMethod$0(InterceptingExecutableInvoker.java:103)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.lambda$invoke$0(InterceptingExecutableInvoker.java:93)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$InterceptedInvocation.proceed(InvocationInterceptorChain.java:106)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.proceed(InvocationInterceptorChain.java:64)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.chainAndInvoke(InvocationInterceptorChain.java:45)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.invoke(InvocationInterceptorChain.java:37)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:92)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:86)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$invokeTestMethod$7(TestMethodTestDescriptor.java:218)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.invokeTestMethod(TestMethodTestDescriptor.java:214)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:139)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:69)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.apache.maven.surefire.junitplatform.LazyLauncher.execute(LazyLauncher.java:56)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.execute(JUnitPlatformProvider.java:184)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invokeAllTests(JUnitPlatformProvider.java:148)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invoke(JUnitPlatformProvider.java:122)
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:385)
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:162)
	at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:507)
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:495)

2025-06-22 18:53:22.475[1750589602475] | INFO  | main       | c.c.t.t.c.TradingSignalConsumer      - 收到交易信号: signalId=test-signal-1, symbol=BTCUSDT, type=BUY, topic=strategy.signal, partition=0, offset=0
2025-06-22 18:53:22.475[1750589602475] | WARN  | main       | c.c.t.t.c.TradingSignalConsumer      - 无效的交易信号: signalId=test-signal-1, error=无效的信号
2025-06-22 18:53:22.482[1750589602482] | WARN  | main       | c.c.t.t.c.TradingSignalConsumer      - 收到空的交易信号消息: topic=strategy.signal, partition=0, offset=0, key=key1
2025-06-22 18:53:22.912[1750589602912] | INFO  | main       | c.c.t.t.e.BinanceTradeExecutor       - 执行市价单: orderId=123456, symbol=BTCUSDT, side=BUY, quantity=1.0
2025-06-22 18:53:22.917[1750589602917] | INFO  | main       | c.c.trading.common.util.ThreadUtil   - 创建线程池成功: corePoolSize=4, maximumPoolSize=13, queueCapacity=1024, threadNamePrefix=thread-pool-
2025-06-22 18:53:22.945[1750589602945] | INFO  | main       | c.c.t.t.e.BinanceTradeExecutor       - 查询订单状态: symbol=BTCUSDT, orderId=123456, clientOrderId=test123
2025-06-22 18:53:22.945[1750589602945] | INFO  | main       | c.c.t.t.e.BinanceTradeExecutor       - 试运行模式，返回模拟订单状态
2025-06-22 18:53:22.951[1750589602951] | INFO  | main       | c.c.t.t.e.BinanceTradeExecutor       - 执行市价单: orderId=123456, symbol=BTCUSDT, side=BUY, quantity=1.0
2025-06-22 18:53:22.951[1750589602951] | INFO  | main       | c.c.t.t.e.BinanceTradeExecutor       - 试运行模式，不实际下单: OrderEntity(id=1, orderId=123456, clientOrderId=test123, symbol=BTCUSDT, side=BUY, positionSide=BOTH, orderType=MARKET, quantity=1.0, price=50000.0, executedQuantity=null, executedPrice=null, status=NEW, strategy=null, signalId=null, errorCode=null, errorMessage=null, createdTime=1750589602950, updatedTime=null, executedTime=null, deleted=null, timeInForce=null, remark=null)
2025-06-22 18:53:23.010[1750589603010] | INFO  | main       | c.c.t.t.e.BinanceTradeExecutor       - 查询订单状态: symbol=BTCUSDT, orderId=123456, clientOrderId=test123
2025-06-22 18:53:23.016[1750589603016] | INFO  | main       | c.c.t.t.e.BinanceTradeExecutor       - 执行市价单: orderId=123456, symbol=BTCUSDT, side=BUY, quantity=1.0
2025-06-22 18:53:23.023[1750589603023] | INFO  | main       | c.c.t.t.e.BinanceTradeExecutor       - 取消订单: orderId=123456, symbol=BTCUSDT, clientOrderId=test123
2025-06-22 18:53:23.145[1750589603145] | INFO  | main       | c.c.t.t.p.i.KafkaTradeResultProducer - 发送订单执行结果到Kafka: orderId=1, symbol=BTCUSDT, status=FILLED
2025-06-22 18:53:23.225[1750589603225] | ERROR |            | c.c.t.t.p.i.KafkaTradeResultProducer - 订单执行结果发送失败: orderId=1, error=java.lang.RuntimeException: 订单执行结果发送失败
java.util.concurrent.CompletionException: java.lang.RuntimeException: 订单执行结果发送失败
	at java.base/java.util.concurrent.CompletableFuture.reportJoin(CompletableFuture.java:413)
	at java.base/java.util.concurrent.CompletableFuture.join(CompletableFuture.java:2118)
	at com.crypto.trading.trade.producer.impl.KafkaTradeResultProducer.lambda$sendOrderExecutionResult$0(KafkaTradeResultProducer.java:84)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.VirtualThread.run(VirtualThread.java:329)
Caused by: java.lang.RuntimeException: 订单执行结果发送失败
	at com.crypto.trading.trade.producer.KafkaTradeResultProducerTest.testSendOrderExecutionResult_Failure(KafkaTradeResultProducerTest.java:158)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.junit.platform.commons.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:728)
	at org.junit.jupiter.engine.execution.MethodInvocation.proceed(MethodInvocation.java:60)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$ValidatingInvocation.proceed(InvocationInterceptorChain.java:131)
	at org.junit.jupiter.engine.extension.TimeoutExtension.intercept(TimeoutExtension.java:156)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestableMethod(TimeoutExtension.java:147)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestMethod(TimeoutExtension.java:86)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker$ReflectiveInterceptorCall.lambda$ofVoidMethod$0(InterceptingExecutableInvoker.java:103)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.lambda$invoke$0(InterceptingExecutableInvoker.java:93)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$InterceptedInvocation.proceed(InvocationInterceptorChain.java:106)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.proceed(InvocationInterceptorChain.java:64)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.chainAndInvoke(InvocationInterceptorChain.java:45)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.invoke(InvocationInterceptorChain.java:37)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:92)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:86)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$invokeTestMethod$7(TestMethodTestDescriptor.java:218)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.invokeTestMethod(TestMethodTestDescriptor.java:214)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:139)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:69)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.apache.maven.surefire.junitplatform.LazyLauncher.execute(LazyLauncher.java:56)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.execute(JUnitPlatformProvider.java:184)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invokeAllTests(JUnitPlatformProvider.java:148)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invoke(JUnitPlatformProvider.java:122)
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:385)
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:162)
	at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:507)
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:495)
2025-06-22 18:53:23.303[1750589603303] | INFO  | main       | c.c.t.t.p.i.KafkaTradeResultProducer - 发送订单执行错误到Kafka: clientOrderId=TEST123, symbol=BTCUSDT, errorCode=400, errorMessage=Invalid parameter
2025-06-22 18:53:23.313[1750589603313] | INFO  |            | c.c.t.t.p.i.KafkaTradeResultProducer - 订单执行错误发送成功: topic=null, partition=0, offset=0
2025-06-22 18:53:23.316[1750589603316] | INFO  | main       | c.c.t.t.p.i.KafkaTradeResultProducer - 发送订单状态更新到Kafka: orderId=1, symbol=BTCUSDT, status=FILLED
2025-06-22 18:53:23.319[1750589603319] | INFO  |            | c.c.t.t.p.i.KafkaTradeResultProducer - 订单状态更新发送成功: topic=null, partition=0, offset=0
2025-06-22 18:53:23.323[1750589603323] | INFO  | main       | c.c.t.t.p.i.KafkaTradeResultProducer - 批量发送订单执行结果到Kafka
2025-06-22 18:53:23.324[1750589603324] | INFO  |            | c.c.t.t.p.i.KafkaTradeResultProducer - 发送订单执行结果到Kafka: orderId=1, symbol=BTCUSDT, status=FILLED
2025-06-22 18:53:23.324[1750589603324] | INFO  |            | c.c.t.t.p.i.KafkaTradeResultProducer - 发送订单执行结果到Kafka: orderId=2, symbol=BTCUSDT, status=FILLED
2025-06-22 18:53:23.325[1750589603325] | INFO  |            | c.c.t.t.p.i.KafkaTradeResultProducer - 订单执行结果发送成功: topic=null, partition=0, offset=0
2025-06-22 18:53:23.326[1750589603326] | INFO  |            | c.c.t.t.p.i.KafkaTradeResultProducer - 订单执行结果发送成功: topic=null, partition=0, offset=0
2025-06-22 18:53:23.327[1750589603327] | INFO  |            | c.c.t.t.p.i.KafkaTradeResultProducer - 批量发送订单执行结果完成，共发送2条消息
2025-06-22 18:53:23.330[1750589603330] | INFO  | main       | c.c.t.t.p.i.KafkaTradeResultProducer - 发送订单执行结果到Kafka: orderId=1, symbol=BTCUSDT, status=FILLED
2025-06-22 18:53:23.331[1750589603331] | INFO  |            | c.c.t.t.p.i.KafkaTradeResultProducer - 订单执行结果发送成功: topic=null, partition=0, offset=0
2025-06-22 18:53:23.475[1750589603475] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 检查交易信号风险: TradeSignal{signalId='test-signal-003', symbol='BTCUSDT', strategy='null', signalType='null', side='null', positionSide='null', confidence=0.0, price=15000.0, quantity=1.0, stopPrice=null, takeProfit=null, orderType='null', timeInForce='null', leverageLevel=null, generatedTime=1750589603475}
2025-06-22 18:53:23.483[1750589603483] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 风控检查已禁用，跳过检查
2025-06-22 18:53:23.490[1750589603490] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 检查订单风险: orderId=test-order-002
2025-06-22 18:53:23.490[1750589603490] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 检查交易金额风险: symbol=BTCUSDT, amount=15000.00
2025-06-22 18:53:23.490[1750589603490] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 交易金额检查结果: amount=15000.00, limit=10000, pass=false
2025-06-22 18:53:23.490[1750589603490] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 记录风控检查: signalId=test-strategy-002, ruleId=AMOUNT, triggered=true
2025-06-22 18:53:23.495[1750589603495] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 检查订单风险: orderId=test-order-001
2025-06-22 18:53:23.496[1750589603496] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 检查交易金额风险: symbol=BTCUSDT, amount=5000.00
2025-06-22 18:53:23.496[1750589603496] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 交易金额检查结果: amount=5000.00, limit=10000, pass=true
2025-06-22 18:53:23.497[1750589603497] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 记录风控检查: signalId=test-strategy-001, ruleId=ALL_CHECKS, triggered=false
2025-06-22 18:53:23.497[1750589603497] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 订单通过风控检查
2025-06-22 18:53:23.500[1750589603500] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 检查交易频率: symbol=BTCUSDT, timeWindow=60000ms
2025-06-22 18:53:23.501[1750589603501] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 交易频率检查结果: count=1, limit=10, pass=true
2025-06-22 18:53:23.501[1750589603501] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 检查交易频率: symbol=BTCUSDT, timeWindow=60000ms
2025-06-22 18:53:23.501[1750589603501] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 交易频率检查结果: count=2, limit=10, pass=true
2025-06-22 18:53:23.501[1750589603501] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 检查交易频率: symbol=BTCUSDT, timeWindow=60000ms
2025-06-22 18:53:23.501[1750589603501] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 交易频率检查结果: count=3, limit=10, pass=true
2025-06-22 18:53:23.501[1750589603501] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 检查交易频率: symbol=BTCUSDT, timeWindow=60000ms
2025-06-22 18:53:23.502[1750589603502] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 交易频率检查结果: count=11, limit=10, pass=false
2025-06-22 18:53:23.505[1750589603505] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 检查交易金额风险: symbol=BTCUSDT, amount=5000.0
2025-06-22 18:53:23.506[1750589603506] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 交易金额检查结果: amount=5000.0, limit=10000, pass=true
2025-06-22 18:53:23.506[1750589603506] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 检查交易金额风险: symbol=BTCUSDT, amount=15000.0
2025-06-22 18:53:23.507[1750589603507] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 交易金额检查结果: amount=15000.0, limit=10000, pass=false
2025-06-22 18:53:23.510[1750589603510] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 记录风控检查: signalId=test-signal-001, ruleId=AMOUNT, triggered=true
2025-06-22 18:53:23.515[1750589603515] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 检查交易信号风险: TradeSignal{signalId='test-signal-001', symbol='BTCUSDT', strategy='null', signalType='null', side='null', positionSide='null', confidence=0.0, price=5000.0, quantity=1.0, stopPrice=null, takeProfit=null, orderType='null', timeInForce='null', leverageLevel=null, generatedTime=1750589603515}
2025-06-22 18:53:23.515[1750589603515] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 检查交易频率: symbol=BTCUSDT, timeWindow=60000ms
2025-06-22 18:53:23.515[1750589603515] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 交易频率检查结果: count=1, limit=10, pass=true
2025-06-22 18:53:23.515[1750589603515] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 检查交易金额风险: symbol=BTCUSDT, amount=5000.00
2025-06-22 18:53:23.515[1750589603515] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 交易金额检查结果: amount=5000.00, limit=10000, pass=true
2025-06-22 18:53:23.516[1750589603516] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 记录风控检查: signalId=test-signal-001, ruleId=ALL_CHECKS, triggered=false
2025-06-22 18:53:23.516[1750589603516] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 交易信号通过风控检查
2025-06-22 18:53:23.525[1750589603525] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 检查交易信号风险: TradeSignal{signalId='test-signal-002', symbol='BTCUSDT', strategy='null', signalType='null', side='null', positionSide='null', confidence=0.0, price=15000.0, quantity=1.0, stopPrice=null, takeProfit=null, orderType='null', timeInForce='null', leverageLevel=null, generatedTime=1750589603525}
2025-06-22 18:53:23.525[1750589603525] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 检查交易频率: symbol=BTCUSDT, timeWindow=60000ms
2025-06-22 18:53:23.525[1750589603525] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 交易频率检查结果: count=1, limit=10, pass=true
2025-06-22 18:53:23.525[1750589603525] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 检查交易金额风险: symbol=BTCUSDT, amount=15000.00
2025-06-22 18:53:23.525[1750589603525] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 交易金额检查结果: amount=15000.00, limit=10000, pass=false
2025-06-22 18:53:23.525[1750589603525] | INFO  | main       | c.c.t.t.s.r.i.RiskControlServiceImpl - 记录风控检查: signalId=test-signal-002, ruleId=AMOUNT, triggered=true
2025-06-22 18:53:23.657[1750589603657] | INFO  |            | c.c.t.t.s.i.SignalProcessingServiceImpl - 交易信号已保存到数据库 [test-validation-id]: signalId=test-signal-1, id=null
2025-06-22 18:53:23.658[1750589603658] | INFO  | main       | c.c.t.t.s.i.SignalProcessingServiceImpl - 交易执行功能未启用 [test-validation-id]: signalId=test-signal-1
2025-06-22 18:53:23.659[1750589603659] | INFO  | main       | c.c.t.c.u.DatabaseExecutorTestUtils  - 等待异步数据库操作完成，超时时间：5000ms，轮询间隔：100ms
2025-06-22 18:53:23.804[1750589603804] | INFO  |            | c.c.t.t.s.i.SignalProcessingServiceImpl - 信号处理状态已更新: signalId=test-signal-1, success=false, result=交易执行功能未启用
2025-06-22 18:53:23.868[1750589603868] | INFO  | main       | c.c.t.c.u.DatabaseExecutorTestUtils  - 所有异步数据库操作已完成，耗时：209ms
2025-06-22 18:53:23.880[1750589603880] | INFO  |            | c.c.t.t.s.i.SignalProcessingServiceImpl - 交易信号已保存到数据库 [test-validation-id]: signalId=test-signal-1, id=null
2025-06-22 18:53:23.880[1750589603880] | INFO  | main       | c.c.t.t.s.i.SignalProcessingServiceImpl - 交易信号已提交执行 [test-validation-id]: signalId=test-signal-1
2025-06-22 18:53:23.886[1750589603886] | INFO  | main       | c.c.t.c.u.DatabaseExecutorTestUtils  - 等待异步数据库操作完成，超时时间：5000ms，轮询间隔：100ms
2025-06-22 18:53:23.887[1750589603887] | INFO  |            | c.c.t.t.s.i.SignalProcessingServiceImpl - 信号处理状态已更新: signalId=test-signal-1, success=true, result=处理成功
2025-06-22 18:53:23.992[1750589603992] | INFO  | main       | c.c.t.c.u.DatabaseExecutorTestUtils  - 所有异步数据库操作已完成，耗时：106ms
2025-06-22 18:53:23.995[1750589603995] | WARN  | main       | c.c.t.t.s.i.SignalProcessingServiceImpl - 交易信号已存在 [test-validation-id]: signalId=test-signal-1
2025-06-22 18:53:24.001[1750589604001] | INFO  |            | c.c.t.t.s.i.SignalProcessingServiceImpl - 交易信号已保存到数据库 [test-validation-id]: signalId=test-signal-1, id=null
2025-06-22 18:53:24.002[1750589604002] | INFO  | main       | c.c.t.t.s.i.SignalProcessingServiceImpl - HOLD信号已处理 [test-validation-id]: signalId=test-signal-1
2025-06-22 18:53:24.002[1750589604002] | INFO  | main       | c.c.t.c.u.DatabaseExecutorTestUtils  - 等待异步数据库操作完成，超时时间：5000ms，轮询间隔：100ms
2025-06-22 18:53:24.002[1750589604002] | INFO  |            | c.c.t.t.s.i.SignalProcessingServiceImpl - 信号处理状态已更新: signalId=test-signal-1, success=true, result=HOLD信号，无需执行交易
2025-06-22 18:53:24.115[1750589604115] | INFO  | main       | c.c.t.c.u.DatabaseExecutorTestUtils  - 所有异步数据库操作已完成，耗时：113ms
2025-06-22 18:53:24.181[1750589604181] | INFO  | main       | c.c.t.t.s.SignalValidationService    - 开始验证交易信号 [dc0505a5]: signalId=test-signal-1, symbol=BTCUSDT, type=BUY
2025-06-22 18:53:24.182[1750589604182] | WARN  | main       | c.c.t.t.s.SignalValidationService    - 交易信号验证失败 [dc0505a5]: 置信度必须在0.0-1.0之间，当前值: 1.5, signalId=test-signal-1, symbol=BTCUSDT, type=BUY
2025-06-22 18:53:24.184[1750589604184] | INFO  | main       | c.c.t.t.s.SignalValidationService    - 开始验证交易信号 [ac5f37bc]: signalId=test-signal-1, symbol=BTCUSDT, type=HOLD
2025-06-22 18:53:24.185[1750589604185] | WARN  | main       | c.c.t.t.s.SignalValidationService    - 交易信号验证失败 [ac5f37bc]: 信号类型与订单类型不匹配: HOLD / MARKET, signalId=test-signal-1, symbol=BTCUSDT, type=HOLD
2025-06-22 18:53:24.187[1750589604187] | INFO  | main       | c.c.t.t.s.SignalValidationService    - 开始验证交易信号 [faf54067]: signalId=test-signal-1, symbol=BTCUSDT, type=BUY
2025-06-22 18:53:24.187[1750589604187] | INFO  | main       | c.c.t.t.s.SignalValidationService    - 交易信号验证通过 [faf54067]: signalId=test-signal-1, symbol=BTCUSDT, type=BUY
2025-06-22 18:53:24.189[1750589604189] | INFO  | main       | c.c.t.t.s.SignalValidationService    - 开始验证交易信号 [a4d464fc]: signalId=, symbol=BTCUSDT, type=BUY
2025-06-22 18:53:24.189[1750589604189] | WARN  | main       | c.c.t.t.s.SignalValidationService    - 交易信号验证失败 [a4d464fc]: 信号ID为空, signalId=, symbol=BTCUSDT, type=BUY
2025-06-22 18:53:24.191[1750589604191] | INFO  | main       | c.c.t.t.s.SignalValidationService    - 开始验证交易信号 [3e110f15]: signalId=test-signal-1, symbol=BTCUSDT, type=BUY
2025-06-22 18:53:24.191[1750589604191] | WARN  | main       | c.c.t.t.s.SignalValidationService    - 交易信号验证失败 [3e110f15]: 限价单必须指定价格, signalId=test-signal-1, symbol=BTCUSDT, type=BUY
2025-06-22 18:53:24.193[1750589604193] | INFO  | main       | c.c.t.t.s.SignalValidationService    - 开始验证交易信号 [df4d9a35]: signalId=test-signal-1, symbol=UNSUPPORTED, type=BUY
2025-06-22 18:53:24.193[1750589604193] | WARN  | main       | c.c.t.t.s.SignalValidationService    - 交易信号验证失败 [df4d9a35]: 不支持的交易对: UNSUPPORTED, signalId=test-signal-1, symbol=UNSUPPORTED, type=BUY
2025-06-22 18:53:24.197[1750589604197] | INFO  | main       | c.c.t.t.s.SignalValidationService    - 开始验证交易信号 [0f52891d]: signalId=test-signal-1, symbol=, type=BUY
2025-06-22 18:53:24.197[1750589604197] | WARN  | main       | c.c.t.t.s.SignalValidationService    - 交易信号验证失败 [0f52891d]: 交易对为空, signalId=test-signal-1, symbol=, type=BUY
2025-06-22 18:53:24.262[1750589604262] | INFO  | main       | c.c.t.t.s.TradeExecutionService      - 处理订单状态更新: orderId=123456, symbol=BTCUSDT, status=FILLED
2025-06-22 18:53:24.266[1750589604266] | INFO  | main       | c.c.t.t.s.TradeExecutionService      - 查询订单状态: symbol=BTCUSDT, orderId=123456, clientOrderId=test123
2025-06-22 18:53:24.271[1750589604271] | INFO  | main       | c.c.t.t.s.TradeExecutionService      - 处理订单执行结果: orderId=123456, symbol=BTCUSDT, status=NEW
2025-06-22 18:53:24.277[1750589604277] | INFO  | main       | c.c.t.t.s.TradeExecutionService      - 取消订单: orderId=123456, clientOrderId=test123
2025-06-22 18:53:24.283[1750589604283] | INFO  | main       | c.c.t.t.s.TradeExecutionService      - 执行市价单: orderId=123456, symbol=BTCUSDT, side=BUY, quantity=1.0
