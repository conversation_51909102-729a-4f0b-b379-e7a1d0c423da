package com.crypto.trading.trade.model.result;

import com.crypto.trading.trade.model.entity.TradingSignalEntity;
import java.io.Serializable;

/**
 * 信号处理结果
 * <p>
 * 用于表示信号处理的结果
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class SignalProcessResult implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 是否成功
     */
    private boolean success;

    /**
     * 订单ID
     */
    private String orderId;

    /**
     * 客户端订单ID
     */
    private String clientOrderId;

    /**
     * 错误码
     */
    private String errorCode;

    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 信号实体
     */
    private TradingSignalEntity signalEntity;
    
    /**
     * 消息
     */
    private String message;

    /**
     * 构造函数
     */
    public SignalProcessResult() {
    }

    /**
     * 构造函数
     *
     * @param success      是否成功
     * @param orderId      订单ID
     * @param clientOrderId 客户端订单ID
     * @param errorCode    错误码
     * @param errorMessage 错误信息
     */
    public SignalProcessResult(boolean success, String orderId, String clientOrderId, String errorCode, String errorMessage) {
        this.success = success;
        this.orderId = orderId;
        this.clientOrderId = clientOrderId;
        this.errorCode = errorCode;
        this.errorMessage = errorMessage;
    }
    
    /**
     * 构造函数
     *
     * @param success      是否成功
     * @param message      消息
     * @param signalEntity 信号实体
     */
    public SignalProcessResult(boolean success, String message, TradingSignalEntity signalEntity) {
        this.success = success;
        this.message = message;
        this.signalEntity = signalEntity;
    }

    /**
     * 创建成功结果
     *
     * @param orderId      订单ID
     * @param clientOrderId 客户端订单ID
     * @return 成功结果
     */
    public static SignalProcessResult success(String orderId, String clientOrderId) {
        return new SignalProcessResult(true, orderId, clientOrderId, null, null);
    }

    /**
     * 创建失败结果
     *
     * @param errorCode    错误码
     * @param errorMessage 错误信息
     * @return 失败结果
     */
    public static SignalProcessResult error(String errorCode, String errorMessage) {
        return new SignalProcessResult(false, null, null, errorCode, errorMessage);
    }
    
    /**
     * 获取是否成功
     *
     * @return 是否成功
     */
    public boolean isSuccess() {
        return success;
    }

    /**
     * 设置是否成功
     *
     * @param success 是否成功
     */
    public void setSuccess(boolean success) {
        this.success = success;
    }

    /**
     * 获取订单ID
     *
     * @return 订单ID
     */
    public String getOrderId() {
        return orderId;
    }

    /**
     * 设置订单ID
     *
     * @param orderId 订单ID
     */
    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    /**
     * 获取客户端订单ID
     *
     * @return 客户端订单ID
     */
    public String getClientOrderId() {
        return clientOrderId;
    }

    /**
     * 设置客户端订单ID
     *
     * @param clientOrderId 客户端订单ID
     */
    public void setClientOrderId(String clientOrderId) {
        this.clientOrderId = clientOrderId;
    }

    /**
     * 获取错误码
     *
     * @return 错误码
     */
    public String getErrorCode() {
        return errorCode;
    }

    /**
     * 设置错误码
     *
     * @param errorCode 错误码
     */
    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    /**
     * 获取错误信息
     *
     * @return 错误信息
     */
    public String getErrorMessage() {
        return errorMessage;
    }

    /**
     * 设置错误信息
     *
     * @param errorMessage 错误信息
     */
    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
    
    /**
     * 获取信号实体
     *
     * @return 信号实体
     */
    public TradingSignalEntity getSignalEntity() {
        return signalEntity;
    }

    /**
     * 设置信号实体
     *
     * @param signalEntity 信号实体
     */
    public void setSignalEntity(TradingSignalEntity signalEntity) {
        this.signalEntity = signalEntity;
    }
    
    /**
     * 获取消息
     *
     * @return 消息
     */
    public String getMessage() {
        return message;
    }

    /**
     * 设置消息
     *
     * @param message 消息
     */
    public void setMessage(String message) {
        this.message = message;
    }

    @Override
    public String toString() {
        return "SignalProcessResult{" +
                "success=" + success +
                ", orderId='" + orderId + '\'' +
                ", clientOrderId='" + clientOrderId + '\'' +
                ", errorCode='" + errorCode + '\'' +
                ", errorMessage='" + errorMessage + '\'' +
                ", message='" + message + '\'' +
                ", signalEntity=" + (signalEntity != null ? signalEntity.getSignalId() : "null") +
                '}';
    }


}