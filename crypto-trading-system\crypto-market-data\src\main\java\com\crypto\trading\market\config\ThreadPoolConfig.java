package com.crypto.trading.market.config;

import com.crypto.trading.common.config.AbstractThreadPoolConfig;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ExecutorService;

/**
 * 市场数据模块线程池配置
 * 使用JDK21虚拟线程提高性能
 */
@Configuration
public class ThreadPoolConfig extends AbstractThreadPoolConfig {

    /**
     * 创建虚拟线程执行器Bean
     * 使用AbstractThreadPoolConfig提供的标准方法
     * 
     * @return 虚拟线程执行器
     */
    @Bean
    @Override
    public ExecutorService virtualThreadExecutor() {
        return super.virtualThreadExecutor();
    }
    
    /**
     * 创建平台线程池任务执行器Bean
     * 市场数据模块需要处理大量并发请求
     * 使用AbstractThreadPoolConfig提供的标准方法
     * 
     * @return 平台线程池任务执行器
     */
    @Bean
    @Override
    public ThreadPoolTaskExecutor platformThreadPoolTaskExecutor() {
        return super.platformThreadPoolTaskExecutor();
    }
    
    /**
     * 创建市场数据专用线程池任务执行器Bean
     * 用于处理市场数据特定的高并发任务
     * 
     * @return 市场数据线程池任务执行器
     */
    @Bean
    public ThreadPoolTaskExecutor marketDataThreadPoolTaskExecutor() {
        ThreadPoolTaskExecutor executor = createPlatformThreadPoolTaskExecutor();
        executor.setThreadNamePrefix(getModuleName() + "-market-data-thread-");
        return executor;
    }
    
    /**
     * 获取模块名称
     * 
     * @return 模块名称
     */
    @Override
    protected String getModuleName() {
        return "market-data";
    }
    
    /**
     * 获取默认核心线程数
     * 市场数据模块需要更多线程处理并发数据
     * 
     * @return 默认核心线程数
     */
    @Override
    protected int getDefaultCorePoolSize() {
        return Runtime.getRuntime().availableProcessors() * 2;
    }
    
    /**
     * 获取默认最大线程数
     * 市场数据模块需要更多线程处理并发数据
     * 
     * @return 默认最大线程数
     */
    @Override
    protected int getDefaultMaxPoolSize() {
        return Runtime.getRuntime().availableProcessors() * 4;
    }
    
    /**
     * 获取默认队列容量
     * 市场数据模块需要更大的队列容量
     * 
     * @return 默认队列容量
     */
    @Override
    protected int getDefaultQueueCapacity() {
        return 1000;
    }
}