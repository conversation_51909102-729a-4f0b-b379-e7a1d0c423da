package com.crypto.trading.trade.producer;

import com.crypto.trading.common.dto.trade.OrderDTO;
import com.crypto.trading.common.dto.trade.OrderStatusDTO;

import java.util.concurrent.CompletableFuture;

/**
 * 交易结果生产者接口
 * <p>
 * 定义发送订单执行结果到Kafka的方法
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface TradeResultProducer {

    /**
     * 发送订单执行结果
     *
     * @param orderDTO 订单数据传输对象
     * @return 发送结果的CompletableFuture
     */
    CompletableFuture<Void> sendOrderExecutionResult(OrderDTO orderDTO);

    /**
     * 发送订单状态更新
     *
     * @param orderStatusDTO 订单状态数据传输对象
     * @return 发送结果的CompletableFuture
     */
    CompletableFuture<Void> sendOrderStatusUpdate(OrderStatusDTO orderStatusDTO);

    /**
     * 发送订单执行错误
     *
     * @param clientOrderId 客户端订单ID
     * @param symbol        交易对
     * @param errorCode     错误代码
     * @param errorMessage  错误消息
     * @return 发送结果的CompletableFuture
     */
    CompletableFuture<Void> sendOrderExecutionError(String clientOrderId, String symbol, int errorCode, String errorMessage);

    /**
     * 批量发送订单执行结果
     *
     * @param orderDTOs 订单数据传输对象列表
     * @return 发送结果的CompletableFuture
     */
    CompletableFuture<Void> sendBatchOrderExecutionResults(Iterable<OrderDTO> orderDTOs);
}