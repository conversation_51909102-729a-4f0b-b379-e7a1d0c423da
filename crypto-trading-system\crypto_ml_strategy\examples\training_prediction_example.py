"""
训练和预测模块使用示例

演示如何使用训练模块进行完整的端到端机器学习流程，
包括数据获取、模型训练、预测和服务部署等功能。

Author: Crypto ML Strategy Team
Date: 2024-12-19
"""

import sys
import os
import asyncio
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import time
import json

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

# 导入训练模块
from training import (
    create_training_pipeline,
    create_prediction_service,
    create_model_trainer,
    create_prediction_engine,
    TrainingPipelineConfig,
    DataSourceConfig,
    FeatureConfig,
    ModelConfig,
    TrainingConfig,
    PredictionConfig,
    ModelType,
    DataSource,
    FeatureType,
    TrainingMode,
    get_factory,
    run_integration_test,
    quick_start
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def create_sample_config() -> TrainingPipelineConfig:
    """创建示例配置"""
    try:
        # 数据源配置
        data_source_config = DataSourceConfig(
            source_type=DataSource.MOCK,  # 使用模拟数据进行演示
            api_endpoint="http://localhost:8080/api/data",
            timeout=30,
            retry_attempts=3
        )
        
        # 特征配置
        feature_config = FeatureConfig(
            feature_types=[
                FeatureType.TECHNICAL_INDICATORS,
                FeatureType.PRICE_FEATURES,
                FeatureType.VOLUME_FEATURES,
                FeatureType.TIME_FEATURES
            ],
            technical_indicators=['sma', 'ema', 'rsi', 'macd', 'bollinger', 'atr'],
            timeframes=['1h', '4h', '1d'],
            feature_selection=True,
            feature_importance_threshold=0.01
        )
        
        # 模型配置
        model_config = ModelConfig(
            model_type=ModelType.XGBOOST,
            hyperparameters={
                'n_estimators': 100,
                'max_depth': 6,
                'learning_rate': 0.1,
                'subsample': 0.8,
                'colsample_bytree': 0.8
            },
            use_gpu=False,
            random_state=42
        )
        
        # 训练配置
        training_config = TrainingConfig(
            training_mode=TrainingMode.BATCH,
            train_test_split=0.8,
            validation_split=0.2,
            cross_validation_folds=5,
            batch_size=1000,
            max_epochs=100,
            early_stopping_patience=10
        )
        
        # 预测配置
        prediction_config = PredictionConfig(
            model_path="models/",
            confidence_threshold=0.6,
            use_ensemble=False,
            include_confidence=True,
            cache_predictions=True,
            cache_ttl=300
        )
        
        # 创建完整配置
        config = TrainingPipelineConfig(
            data_source=data_source_config,
            feature_config=feature_config,
            model_config=model_config,
            training_config=training_config,
            prediction_config=prediction_config,
            enable_risk_management=True,
            enable_online_learning=True,
            enable_deepseek_distillation=False,  # 演示中暂时禁用
            enable_multi_timeframe=True
        )
        
        return config
        
    except Exception as e:
        logger.error(f"Error creating sample config: {e}")
        raise


async def demonstrate_data_pipeline():
    """演示数据管道功能"""
    try:
        logger.info("=== 数据管道演示 ===")
        
        # 创建配置和数据管道
        config = create_sample_config()
        factory = get_factory()
        data_pipeline = factory.create_data_pipeline(config)
        
        # 获取数据
        symbol = "BTCUSDT"
        end_time = datetime.now()
        start_time = end_time - timedelta(days=30)
        
        logger.info(f"获取 {symbol} 从 {start_time} 到 {end_time} 的数据")
        
        # 获取原始数据
        raw_data = await data_pipeline.get_data(symbol, start_time, end_time, "1h")
        logger.info(f"原始数据: {raw_data.shape}")
        
        # 准备特征
        features_df = await data_pipeline.prepare_features(symbol, start_time, end_time)
        logger.info(f"特征数据: {features_df.shape}")
        
        if not features_df.empty:
            logger.info(f"特征列: {list(features_df.columns)}")
            logger.info(f"数据预览:")
            logger.info(features_df.head().to_string())
        
        # 获取缓存信息
        cache_info = data_pipeline.get_cache_info()
        logger.info(f"缓存信息: {cache_info}")
        
        return data_pipeline
        
    except Exception as e:
        logger.error(f"数据管道演示失败: {e}")
        raise


async def demonstrate_model_training():
    """演示模型训练功能"""
    try:
        logger.info("=== 模型训练演示 ===")
        
        # 创建模型训练器
        config = create_sample_config()
        trainer = create_model_trainer(config)
        
        # 训练参数
        symbol = "BTCUSDT"
        end_time = datetime.now()
        start_time = end_time - timedelta(days=60)  # 使用60天数据训练
        
        logger.info(f"开始训练模型: {symbol}")
        logger.info(f"训练数据时间范围: {start_time} 到 {end_time}")
        logger.info(f"模型类型: {config.model_config.model_type.value}")
        
        # 执行训练
        training_result = await trainer.train_model(
            symbol=symbol,
            start_time=start_time,
            end_time=end_time,
            target_column="target",
            optimize_hyperparameters=True
        )
        
        # 显示训练结果
        logger.info(f"训练结果:")
        logger.info(f"  成功: {training_result.success}")
        logger.info(f"  模型ID: {training_result.model_id}")
        logger.info(f"  训练时间: {training_result.training_time:.2f}秒")
        
        if training_result.success:
            logger.info(f"  训练指标: {training_result.training_metrics}")
            logger.info(f"  验证指标: {training_result.validation_metrics}")
            
            if training_result.feature_importance:
                # 显示前10个重要特征
                sorted_features = sorted(
                    training_result.feature_importance.items(),
                    key=lambda x: x[1],
                    reverse=True
                )[:10]
                
                logger.info("  前10个重要特征:")
                for feature, importance in sorted_features:
                    logger.info(f"    {feature}: {importance:.4f}")
        else:
            logger.error(f"  训练失败: {training_result.error_message}")
        
        return training_result
        
    except Exception as e:
        logger.error(f"模型训练演示失败: {e}")
        raise


async def demonstrate_model_prediction():
    """演示模型预测功能"""
    try:
        logger.info("=== 模型预测演示 ===")
        
        # 首先训练一个模型
        training_result = await demonstrate_model_training()
        
        if not training_result.success:
            logger.error("无法进行预测演示，因为模型训练失败")
            return
        
        # 创建预测引擎
        config = create_sample_config()
        prediction_engine = create_prediction_engine(config)
        
        model_id = training_result.model_id
        symbol = "BTCUSDT"
        
        logger.info(f"使用模型 {model_id} 进行预测")
        
        # 单次预测
        logger.info("执行单次预测...")
        prediction_result = await prediction_engine.predict_single(
            model_id=model_id,
            symbol=symbol,
            timestamp=datetime.now()
        )
        
        logger.info(f"预测结果:")
        logger.info(f"  预测ID: {prediction_result.prediction_id}")
        logger.info(f"  预测值: {prediction_result.predictions}")
        logger.info(f"  置信度: {prediction_result.confidence_scores}")
        logger.info(f"  处理时间: {prediction_result.processing_time:.3f}秒")
        
        # 批量预测
        logger.info("执行批量预测...")
        timestamps = [
            datetime.now() - timedelta(hours=i) 
            for i in range(5, 0, -1)
        ]
        
        batch_results = await prediction_engine.predict_batch(
            model_id=model_id,
            symbol=symbol,
            timestamps=timestamps
        )
        
        logger.info(f"批量预测完成: {len(batch_results)} 个结果")
        for i, result in enumerate(batch_results):
            logger.info(f"  结果 {i+1}: 预测={result.predictions}, 置信度={result.confidence_scores}")
        
        # 带风险评估的预测
        logger.info("执行带风险评估的预测...")
        risk_prediction = await prediction_engine.predict_with_risk_assessment(
            model_id=model_id,
            symbol=symbol,
            timestamp=datetime.now()
        )
        
        logger.info(f"风险评估预测结果:")
        logger.info(f"  预测: {risk_prediction.get('predictions')}")
        logger.info(f"  风险评估: {risk_prediction.get('risk_assessment')}")
        
        # 获取预测统计
        stats = prediction_engine.get_prediction_statistics()
        logger.info(f"预测引擎统计: {stats}")
        
        return prediction_result
        
    except Exception as e:
        logger.error(f"模型预测演示失败: {e}")
        raise


async def demonstrate_prediction_service():
    """演示预测服务功能"""
    try:
        logger.info("=== 预测服务演示 ===")
        
        # 创建预测服务
        config = create_sample_config()
        service = create_prediction_service(config)
        
        # 启动服务
        logger.info("启动预测服务...")
        if not service.start():
            raise Exception("预测服务启动失败")
        
        # 等待服务启动
        await asyncio.sleep(2)
        
        # 获取服务状态
        status = service.get_service_status()
        logger.info(f"服务状态: {status}")
        
        # 首先训练一个模型（通过服务）
        logger.info("通过服务训练模型...")
        
        train_payload = {
            'symbol': 'BTCUSDT',
            'start_time': (datetime.now() - timedelta(days=30)).isoformat(),
            'end_time': datetime.now().isoformat(),
            'target_column': 'target',
            'optimize_hyperparameters': False  # 为了演示速度，禁用超参数优化
        }
        
        train_response = await service.submit_request_async(
            request_type='train',
            payload=train_payload,
            timeout=60.0
        )
        
        logger.info(f"训练响应: 成功={train_response.success}")
        
        if train_response.success and train_response.result:
            model_id = train_response.result.get('model_id')
            logger.info(f"训练完成，模型ID: {model_id}")
            
            # 通过服务进行预测
            logger.info("通过服务进行预测...")
            
            predict_payload = {
                'model_id': model_id,
                'symbol': 'BTCUSDT',
                'timestamp': datetime.now().isoformat(),
                'include_risk_assessment': True
            }
            
            predict_response = await service.submit_request_async(
                request_type='predict',
                payload=predict_payload,
                timeout=30.0
            )
            
            logger.info(f"预测响应: 成功={predict_response.success}")
            if predict_response.success:
                result = predict_response.result
                logger.info(f"预测结果: {result.get('predictions')}")
                logger.info(f"置信度: {result.get('confidence_scores')}")
                logger.info(f"风险评估: {result.get('risk_assessment')}")
        
        # 获取健康状态
        health = service.get_health_status()
        logger.info(f"健康状态: {health}")
        
        # 停止服务
        logger.info("停止预测服务...")
        service.stop()
        
        return service
        
    except Exception as e:
        logger.error(f"预测服务演示失败: {e}")
        raise


def demonstrate_model_management():
    """演示模型管理功能"""
    try:
        logger.info("=== 模型管理演示 ===")
        
        # 创建模型管理器
        config = create_sample_config()
        factory = get_factory()
        model_manager = factory.create_model_manager(config)
        
        # 列出现有模型
        models = model_manager.list_models()
        logger.info(f"现有模型数量: {len(models)}")
        
        if models:
            logger.info("模型列表:")
            for model in models[:5]:  # 显示前5个模型
                logger.info(f"  ID: {model.model_id}")
                logger.info(f"  类型: {model.model_type.value}")
                logger.info(f"  版本: {model.version}")
                logger.info(f"  创建时间: {model.created_at}")
                logger.info(f"  性能: {model.performance_metrics}")
                logger.info("  ---")
            
            # 获取最佳模型
            best_model = model_manager.get_best_model(metric='accuracy')
            if best_model:
                model_id, metadata = best_model
                logger.info(f"最佳模型: {model_id}")
                logger.info(f"准确率: {metadata.performance_metrics.get('accuracy', 'N/A')}")
            
            # 模型比较
            if len(models) >= 2:
                model_ids = [m.model_id for m in models[:3]]
                comparison_df = model_manager.compare_models(model_ids)
                logger.info("模型比较:")
                logger.info(comparison_df.to_string())
        
        # 获取模型统计
        stats = model_manager.get_model_statistics()
        logger.info(f"模型统计: {stats}")
        
        return model_manager
        
    except Exception as e:
        logger.error(f"模型管理演示失败: {e}")
        raise


async def demonstrate_complete_pipeline():
    """演示完整的训练和预测管道"""
    try:
        logger.info("=== 完整管道演示 ===")
        
        # 创建完整的训练管道
        config = create_sample_config()
        pipeline = create_training_pipeline(config)
        
        logger.info("训练管道组件:")
        for component_name, component in pipeline.items():
            logger.info(f"  {component_name}: {type(component).__name__}")
        
        # 使用管道进行端到端流程
        symbol = "BTCUSDT"
        end_time = datetime.now()
        start_time = end_time - timedelta(days=30)
        
        # 1. 数据准备
        logger.info("1. 准备数据...")
        features_df = await pipeline['data_pipeline'].prepare_features(
            symbol, start_time, end_time
        )
        logger.info(f"特征数据准备完成: {features_df.shape}")
        
        # 2. 模型训练
        logger.info("2. 训练模型...")
        training_result = await pipeline['trainer'].train_model(
            symbol, start_time, end_time, optimize_hyperparameters=False
        )
        
        if training_result.success:
            logger.info(f"模型训练成功: {training_result.model_id}")
            
            # 3. 模型预测
            logger.info("3. 执行预测...")
            prediction_result = await pipeline['predictor'].predict_single(
                training_result.model_id, symbol
            )
            logger.info(f"预测完成: {prediction_result.predictions}")
            
            # 4. 启动服务
            logger.info("4. 启动预测服务...")
            service = pipeline['service']
            service.start()
            
            # 等待服务启动
            await asyncio.sleep(1)
            
            # 通过服务进行预测
            predict_payload = {
                'model_id': training_result.model_id,
                'symbol': symbol,
                'include_risk_assessment': True
            }
            
            service_response = await service.submit_request_async(
                'predict', predict_payload, timeout=30.0
            )
            
            logger.info(f"服务预测结果: {service_response.success}")
            
            # 停止服务
            service.stop()
        
        logger.info("完整管道演示完成")
        return pipeline
        
    except Exception as e:
        logger.error(f"完整管道演示失败: {e}")
        raise


def demonstrate_quick_start():
    """演示快速启动功能"""
    try:
        logger.info("=== 快速启动演示 ===")
        
        # 快速启动
        result = quick_start(
            java_api_endpoint="http://localhost:8080/api/data",
            model_type=ModelType.RANDOM_FOREST
        )
        
        logger.info(f"快速启动结果: {result['status']}")
        
        if result['status'] == 'running':
            service = result['service']
            
            # 获取服务状态
            status = service.get_service_status()
            logger.info(f"服务运行状态: {status['is_running']}")
            
            # 停止服务
            service.stop()
            logger.info("服务已停止")
        
        return result
        
    except Exception as e:
        logger.error(f"快速启动演示失败: {e}")
        raise


async def main():
    """主函数"""
    try:
        logger.info("开始训练和预测模块完整演示")
        
        # 运行集成测试
        logger.info("运行集成测试...")
        test_result = run_integration_test()
        if not test_result:
            logger.error("集成测试失败，停止演示")
            return
        
        logger.info("集成测试通过，开始功能演示")
        
        # 演示各个功能模块
        await demonstrate_data_pipeline()
        print("\n" + "="*50 + "\n")
        
        await demonstrate_model_training()
        print("\n" + "="*50 + "\n")
        
        await demonstrate_model_prediction()
        print("\n" + "="*50 + "\n")
        
        demonstrate_model_management()
        print("\n" + "="*50 + "\n")
        
        await demonstrate_prediction_service()
        print("\n" + "="*50 + "\n")
        
        await demonstrate_complete_pipeline()
        print("\n" + "="*50 + "\n")
        
        demonstrate_quick_start()
        
        logger.info("训练和预测模块演示完成！")
        logger.info("所有功能组件运行正常，可以集成到主系统中使用。")
        
    except Exception as e:
        logger.error(f"演示过程中发生错误: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
