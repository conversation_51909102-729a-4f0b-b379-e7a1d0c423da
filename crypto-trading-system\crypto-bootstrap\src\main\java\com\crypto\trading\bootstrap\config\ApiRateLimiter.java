package com.crypto.trading.bootstrap.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * API请求速率限制器
 * 用于防止过度请求导致的API限流问题
 */
@Component
public class ApiRateLimiter {
    private static final Logger log = LoggerFactory.getLogger(ApiRateLimiter.class);
    
    /**
     * 特殊接口限流配置（每分钟最大请求数）
     */
    private static final Map<String, Integer> ENDPOINT_LIMITS = Map.of(
            // K线数据接口限流
            "klines", 60,
            // 交易数据批量下载接口限流
            "trades_batch", 10,
            // BTC交易数据接口限流
            "trades_BTCUSDT", 6,
            // ETH交易数据接口限流
            "trades_ETHUSDT", 10,
            // BNB交易数据接口限流
            "trades_BNBUSDT", 20,
            // 默认交易数据接口限流
            "trades_default", 30
    );
    
    /**
     * 默认每分钟最大请求数
     */
    private static final int DEFAULT_LIMIT_PER_MINUTE = 60;
    
    /**
     * 端点请求计数器Map
     */
    private final Map<String, AtomicInteger> counterMap = new ConcurrentHashMap<>();
    
    /**
     * 限流器启动时间
     */
    private final long startTimeMillis = System.currentTimeMillis();
    
    /**
     * 定时清零任务执行器
     */
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
    
    /**
     * 构造函数，启动定时清零任务
     */
    public ApiRateLimiter() {
        // 每分钟重置计数器
        scheduler.scheduleAtFixedRate(this::resetCounters, 1, 1, TimeUnit.MINUTES);
        log.info("API速率限制器已初始化");
    }
    
    /**
     * 检查请求是否被允许
     *
     * @param endpoint 请求端点标识
     * @return 是否允许请求
     */
    public boolean allowRequest(String endpoint) {
        // 获取当前计数
        AtomicInteger counter = counterMap.computeIfAbsent(endpoint, k -> new AtomicInteger(0));
        
        // 获取限制配置
        int limit;
        if (endpoint.startsWith("trades_")) {
            String symbol = endpoint.substring(7); // 去除"trades_"前缀
            limit = ENDPOINT_LIMITS.getOrDefault(endpoint, ENDPOINT_LIMITS.getOrDefault("trades_default", DEFAULT_LIMIT_PER_MINUTE));
        } else {
            limit = ENDPOINT_LIMITS.getOrDefault(endpoint, DEFAULT_LIMIT_PER_MINUTE);
        }
        
        // 检查是否超过限制
        int currentCount = counter.incrementAndGet();
        boolean allowed = currentCount <= limit;
        
        if (!allowed) {
            log.warn("API请求频率超限: endpoint={}, count={}, limit={}", endpoint, currentCount, limit);
        }
        
        return allowed;
    }
    
    /**
     * 重置所有计数器
     */
    private void resetCounters() {
        // 记录之前的计数
        if (!counterMap.isEmpty()) {
            counterMap.forEach((endpoint, counter) -> {
                int count = counter.get();
                if (count > 0) {
                    log.debug("重置计数器: endpoint={}, count={}", endpoint, count);
                }
            });
        }
        
        // 清空计数器
        counterMap.clear();
    }
    
    /**
     * 获取端点当前请求计数
     *
     * @param endpoint 请求端点标识
     * @return 当前计数
     */
    public int getCurrentCount(String endpoint) {
        AtomicInteger counter = counterMap.get(endpoint);
        return counter != null ? counter.get() : 0;
    }
    
    /**
     * 获取所有端点的当前请求计数
     *
     * @return 端点计数Map
     */
    public Map<String, Integer> getAllCounts() {
        Map<String, Integer> result = new ConcurrentHashMap<>();
        counterMap.forEach((endpoint, counter) -> result.put(endpoint, counter.get()));
        return result;
    }
    
    /**
     * 获取特定端点的最大请求限制
     *
     * @param endpoint 请求端点标识
     * @return 最大请求限制
     */
    public int getLimit(String endpoint) {
        if (endpoint.startsWith("trades_")) {
            String symbol = endpoint.substring(7); // 去除"trades_"前缀
            return ENDPOINT_LIMITS.getOrDefault(endpoint, ENDPOINT_LIMITS.getOrDefault("trades_default", DEFAULT_LIMIT_PER_MINUTE));
        } else {
            return ENDPOINT_LIMITS.getOrDefault(endpoint, DEFAULT_LIMIT_PER_MINUTE);
        }
    }
    
    /**
     * 获取限流器运行时间（毫秒）
     *
     * @return 运行时间
     */
    public long getUptimeMillis() {
        return System.currentTimeMillis() - startTimeMillis;
    }
} 