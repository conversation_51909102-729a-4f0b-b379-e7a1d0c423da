#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
风险管理集成测试运行器

专门用于执行风险管理相关的集成测试，包括风险控制系统、
仓位管理、止损止盈、回撤控制等功能的全面测试。

Author: Crypto ML Strategy Team
Date: 2024-12-19
"""

import sys
import os
import unittest
import argparse
import logging
import json
from datetime import datetime
from typing import Dict, List, Any, Optional
import time

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'src'))

# 导入测试套件和工具
from suites.risk_management_integration_test_suite import RiskManagementIntegrationTestSuite
from utils.risk_management_test_helpers import RiskTestExecutor, RiskScenarioGenerator
from utils.test_orchestrator import TestOrchestrator
from config.integration_test_config import IntegrationTestConfig
from reports.test_reporter import TestReporter

logger = logging.getLogger(__name__)


class RiskManagementTestRunner:
    """风险管理测试运行器"""
    
    def __init__(self, config: Optional[IntegrationTestConfig] = None):
        """
        初始化测试运行器
        
        Args:
            config: 集成测试配置
        """
        self.config = config or IntegrationTestConfig()
        self.test_orchestrator = TestOrchestrator(self.config)
        self.test_reporter = TestReporter()
        self.risk_test_executor = RiskTestExecutor()
        self.scenario_generator = RiskScenarioGenerator()
        
        # 测试结果
        self.test_results = {}
        self.start_time = None
        self.end_time = None
        
        logger.info("Risk Management Test Runner initialized")
    
    def setup_logging(self, log_level: str = "INFO") -> None:
        """设置日志配置"""
        log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        
        # 设置控制台日志
        logging.basicConfig(
            level=getattr(logging, log_level.upper()),
            format=log_format,
            handlers=[
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        # 设置文件日志
        log_file = os.path.join(
            self.config.test_output_dir,
            f"risk_management_tests_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        )
        
        file_handler = logging.FileHandler(log_file)
        file_handler.setFormatter(logging.Formatter(log_format))
        logging.getLogger().addHandler(file_handler)
        
        logger.info(f"Logging configured. Log file: {log_file}")
    
    def run_unit_tests(self) -> Dict[str, Any]:
        """运行单元测试"""
        logger.info("Running Risk Management Integration Tests...")
        
        # 创建测试套件
        test_suite = unittest.TestLoader().loadTestsFromTestCase(RiskManagementIntegrationTestSuite)
        
        # 运行测试
        test_runner = unittest.TextTestRunner(
            verbosity=2,
            stream=sys.stdout,
            buffer=True
        )
        
        start_time = time.time()
        test_result = test_runner.run(test_suite)
        execution_time = time.time() - start_time
        
        # 整理结果
        results = {
            'test_type': 'unit_tests',
            'total_tests': test_result.testsRun,
            'successful_tests': test_result.testsRun - len(test_result.failures) - len(test_result.errors),
            'failed_tests': len(test_result.failures),
            'error_tests': len(test_result.errors),
            'skipped_tests': len(test_result.skipped) if hasattr(test_result, 'skipped') else 0,
            'execution_time': execution_time,
            'success_rate': ((test_result.testsRun - len(test_result.failures) - len(test_result.errors)) / test_result.testsRun * 100) if test_result.testsRun > 0 else 0,
            'failures': [
                {
                    'test_name': str(test),
                    'error_message': error
                }
                for test, error in test_result.failures
            ],
            'errors': [
                {
                    'test_name': str(test),
                    'error_message': error
                }
                for test, error in test_result.errors
            ]
        }
        
        logger.info(f"Unit tests completed: {results['successful_tests']}/{results['total_tests']} passed")
        return results
    
    def run_scenario_tests(self) -> Dict[str, Any]:
        """运行风险场景测试"""
        logger.info("Running Risk Scenario Tests...")
        
        start_time = time.time()
        
        # 执行所有风险场景
        scenario_results = self.risk_test_executor.execute_all_scenarios()
        
        execution_time = time.time() - start_time
        
        # 生成场景测试报告
        scenario_report = self.risk_test_executor.generate_test_report()
        scenario_report['execution_time'] = execution_time
        scenario_report['test_type'] = 'scenario_tests'
        
        logger.info(f"Scenario tests completed: {scenario_report['summary']['successful_tests']}/{scenario_report['summary']['total_tests']} passed")
        return scenario_report
    
    def run_performance_tests(self) -> Dict[str, Any]:
        """运行性能测试"""
        logger.info("Running Risk Management Performance Tests...")
        
        start_time = time.time()
        
        # 性能测试配置
        performance_scenarios = [
            {
                'name': 'High Frequency Updates',
                'description': 'Test with high frequency price updates',
                'update_frequency': 0.1,  # 每100ms更新一次
                'duration': 30,  # 30秒
                'concurrent_symbols': 5
            },
            {
                'name': 'Large Portfolio',
                'description': 'Test with large number of positions',
                'update_frequency': 1.0,
                'duration': 60,
                'concurrent_symbols': 20
            },
            {
                'name': 'Stress Test',
                'description': 'Stress test with extreme conditions',
                'update_frequency': 0.05,  # 每50ms更新一次
                'duration': 120,  # 2分钟
                'concurrent_symbols': 10
            }
        ]
        
        performance_results = []
        
        for scenario in performance_scenarios:
            logger.info(f"Running performance scenario: {scenario['name']}")
            
            scenario_start = time.time()
            
            try:
                # 执行性能测试场景
                result = self._execute_performance_scenario(scenario)
                result['success'] = True
                result['error_message'] = None
                
            except Exception as e:
                logger.error(f"Performance scenario {scenario['name']} failed: {e}")
                result = {
                    'success': False,
                    'error_message': str(e),
                    'performance_metrics': {}
                }
            
            result['scenario_name'] = scenario['name']
            result['scenario_description'] = scenario['description']
            result['execution_time'] = time.time() - scenario_start
            
            performance_results.append(result)
        
        total_execution_time = time.time() - start_time
        
        # 整理性能测试结果
        successful_scenarios = [r for r in performance_results if r['success']]
        failed_scenarios = [r for r in performance_results if not r['success']]
        
        performance_report = {
            'test_type': 'performance_tests',
            'total_scenarios': len(performance_results),
            'successful_scenarios': len(successful_scenarios),
            'failed_scenarios': len(failed_scenarios),
            'success_rate': len(successful_scenarios) / len(performance_results) * 100,
            'total_execution_time': total_execution_time,
            'scenarios': performance_results,
            'summary_metrics': self._calculate_performance_summary(successful_scenarios)
        }
        
        logger.info(f"Performance tests completed: {len(successful_scenarios)}/{len(performance_results)} scenarios passed")
        return performance_report
    
    def _execute_performance_scenario(self, scenario: Dict[str, Any]) -> Dict[str, Any]:
        """执行单个性能测试场景"""
        from risk_management import RiskControlEngine, RiskConfig
        from utils.risk_management_test_helpers import RiskPerformanceMonitor
        
        # 创建风险控制引擎
        risk_config = RiskConfig(risk_check_interval=scenario['update_frequency'])
        risk_engine = RiskControlEngine(risk_config)
        
        # 创建性能监控器
        performance_monitor = RiskPerformanceMonitor()
        performance_monitor.start_monitoring(risk_engine)
        
        try:
            # 启动风险引擎
            risk_engine.start()
            
            # 模拟高频更新
            end_time = time.time() + scenario['duration']
            update_count = 0
            
            while time.time() < end_time:
                # 模拟价格更新
                for i in range(scenario['concurrent_symbols']):
                    symbol = f"TEST{i}USDT"
                    
                    # 生成随机价格数据
                    import random
                    price = 1000 + random.uniform(-100, 100)
                    
                    # 更新市场数据
                    market_data = {
                        'price_df': None,  # 简化测试
                        'indicators': {
                            'current_price': price,
                            'atr': 50,
                            'volatility': 0.2
                        }
                    }
                    
                    update_start = time.time()
                    risk_engine.update_market_data(symbol, market_data)
                    update_time = time.time() - update_start
                    
                    performance_monitor.record_response_time(update_time)
                    update_count += 1
                
                # 等待下次更新
                time.sleep(scenario['update_frequency'])
            
            # 停止监控
            performance_metrics = performance_monitor.stop_monitoring()
            
            # 停止风险引擎
            risk_engine.stop()
            
            # 添加额外的性能指标
            performance_metrics['total_updates'] = update_count
            performance_metrics['updates_per_second'] = update_count / scenario['duration']
            performance_metrics['scenario_duration'] = scenario['duration']
            
            return {
                'performance_metrics': performance_metrics
            }
            
        except Exception as e:
            # 确保清理资源
            if risk_engine.is_running:
                risk_engine.stop()
            raise e
    
    def _calculate_performance_summary(self, successful_scenarios: List[Dict[str, Any]]) -> Dict[str, Any]:
        """计算性能测试摘要"""
        if not successful_scenarios:
            return {}
        
        all_metrics = [s['performance_metrics'] for s in successful_scenarios]
        
        # 计算平均指标
        avg_response_times = [m.get('avg_response_time', 0) for m in all_metrics]
        max_response_times = [m.get('max_response_time', 0) for m in all_metrics]
        updates_per_second = [m.get('updates_per_second', 0) for m in all_metrics]
        
        import numpy as np
        
        return {
            'avg_response_time': np.mean(avg_response_times),
            'max_response_time': np.max(max_response_times),
            'avg_updates_per_second': np.mean(updates_per_second),
            'max_updates_per_second': np.max(updates_per_second),
            'total_scenarios_analyzed': len(successful_scenarios)
        }
    
    def run_all_tests(self) -> Dict[str, Any]:
        """运行所有风险管理测试"""
        logger.info("Starting comprehensive Risk Management testing...")
        
        self.start_time = datetime.now()
        
        # 运行各类测试
        unit_test_results = self.run_unit_tests()
        scenario_test_results = self.run_scenario_tests()
        performance_test_results = self.run_performance_tests()
        
        self.end_time = datetime.now()
        total_duration = (self.end_time - self.start_time).total_seconds()
        
        # 整合所有测试结果
        comprehensive_results = {
            'test_suite': 'Risk Management Integration Tests',
            'start_time': self.start_time.isoformat(),
            'end_time': self.end_time.isoformat(),
            'total_duration': total_duration,
            'unit_tests': unit_test_results,
            'scenario_tests': scenario_test_results,
            'performance_tests': performance_test_results,
            'overall_summary': {
                'total_test_categories': 3,
                'successful_categories': sum([
                    1 if unit_test_results.get('success_rate', 0) > 80 else 0,
                    1 if scenario_test_results.get('summary', {}).get('success_rate', 0) > 80 else 0,
                    1 if performance_test_results.get('success_rate', 0) > 80 else 0
                ]),
                'overall_success_rate': (
                    unit_test_results.get('success_rate', 0) +
                    scenario_test_results.get('summary', {}).get('success_rate', 0) +
                    performance_test_results.get('success_rate', 0)
                ) / 3
            }
        }
        
        self.test_results = comprehensive_results
        
        logger.info(f"All Risk Management tests completed in {total_duration:.2f} seconds")
        logger.info(f"Overall success rate: {comprehensive_results['overall_summary']['overall_success_rate']:.1f}%")
        
        return comprehensive_results
    
    def save_results(self, output_file: Optional[str] = None) -> str:
        """保存测试结果到文件"""
        if not self.test_results:
            logger.warning("No test results to save")
            return ""
        
        if not output_file:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_file = os.path.join(
                self.config.test_output_dir,
                f"risk_management_test_results_{timestamp}.json"
            )
        
        # 确保输出目录存在
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(self.test_results, f, indent=2, default=str, ensure_ascii=False)
            
            logger.info(f"Test results saved to: {output_file}")
            return output_file
            
        except Exception as e:
            logger.error(f"Error saving test results: {e}")
            raise
    
    def generate_html_report(self, output_file: Optional[str] = None) -> str:
        """生成HTML测试报告"""
        if not self.test_results:
            logger.warning("No test results to generate report")
            return ""
        
        if not output_file:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_file = os.path.join(
                self.config.test_output_dir,
                f"risk_management_test_report_{timestamp}.html"
            )
        
        try:
            html_report = self.test_reporter.generate_html_report(
                self.test_results,
                title="Risk Management Integration Test Report"
            )
            
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(html_report)
            
            logger.info(f"HTML report generated: {output_file}")
            return output_file
            
        except Exception as e:
            logger.error(f"Error generating HTML report: {e}")
            raise


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Risk Management Integration Test Runner')
    parser.add_argument('--log-level', default='INFO', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                        help='Set logging level')
    parser.add_argument('--output-dir', default='./test_results',
                        help='Output directory for test results')
    parser.add_argument('--test-type', choices=['unit', 'scenario', 'performance', 'all'], default='all',
                        help='Type of tests to run')
    parser.add_argument('--save-results', action='store_true',
                        help='Save test results to JSON file')
    parser.add_argument('--generate-report', action='store_true',
                        help='Generate HTML test report')
    
    args = parser.parse_args()
    
    # 创建配置
    config = IntegrationTestConfig()
    config.test_output_dir = args.output_dir
    
    # 创建测试运行器
    runner = RiskManagementTestRunner(config)
    runner.setup_logging(args.log_level)
    
    try:
        # 运行测试
        if args.test_type == 'unit':
            results = runner.run_unit_tests()
        elif args.test_type == 'scenario':
            results = runner.run_scenario_tests()
        elif args.test_type == 'performance':
            results = runner.run_performance_tests()
        else:  # all
            results = runner.run_all_tests()
        
        # 保存结果
        if args.save_results:
            runner.save_results()
        
        # 生成报告
        if args.generate_report:
            runner.generate_html_report()
        
        # 打印摘要
        if args.test_type == 'all':
            overall_success = results['overall_summary']['overall_success_rate']
            print(f"\n{'='*60}")
            print(f"RISK MANAGEMENT INTEGRATION TEST SUMMARY")
            print(f"{'='*60}")
            print(f"Overall Success Rate: {overall_success:.1f}%")
            print(f"Total Duration: {results['total_duration']:.2f} seconds")
            print(f"{'='*60}")
            
            if overall_success >= 90:
                print("🎉 EXCELLENT: All tests passed with high success rate!")
                sys.exit(0)
            elif overall_success >= 80:
                print("✅ GOOD: Most tests passed, minor issues detected")
                sys.exit(0)
            elif overall_success >= 60:
                print("⚠️  WARNING: Significant issues detected, review required")
                sys.exit(1)
            else:
                print("❌ CRITICAL: Major failures detected, immediate attention required")
                sys.exit(2)
        
    except Exception as e:
        logger.error(f"Test execution failed: {e}")
        sys.exit(3)


if __name__ == '__main__':
    main()
