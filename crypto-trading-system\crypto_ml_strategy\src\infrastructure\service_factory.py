#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
服务工厂模块

该模块提供服务工厂实现，负责创建和配置各种服务实例。
"""

from typing import Dict, Any, Optional, Type, TypeVar
from loguru import logger

from .service_interfaces import *
from .config import Config
from .logger import setup_logger
from .kafka_client import KafkaClient
from .data_processor import DataProcessor
from .data.influxdb_client import InfluxDBClient
from .data.mysql_client import MySQLClient
from .data.real_data_loader import RealDataLoader
from .model.model_trainer import ModelTrainer
from .model.prediction import PredictionEngine
from .strategy.unified_ml import UnifiedMLStrategy
from .model.online_learner import OnlineLearner
from .model.versioning import ModelVersionManager

T = TypeVar('T')


class ServiceFactory:
    """
    服务工厂类
    
    负责创建和配置各种服务实例，支持依赖注入。
    """
    
    def __init__(self, config: IConfigService):
        """
        初始化服务工厂
        
        Args:
            config: 配置服务
        """
        self.config = config
        self._logger = setup_logger()
        logger.info("服务工厂初始化完成")
    
    def create_config_service(self, config_path: Optional[str] = None) -> IConfigService:
        """
        创建配置服务
        
        Args:
            config_path: 配置文件路径
            
        Returns:
            配置服务实例
        """
        try:
            config = Config(config_path)
            logger.info("配置服务创建成功")
            return config
        except Exception as e:
            logger.error(f"创建配置服务失败: {e}")
            raise
    
    def create_logger_service(self) -> ILoggerService:
        """
        创建日志服务
        
        Returns:
            日志服务实例
        """
        try:
            logger_service = setup_logger()
            logger.info("日志服务创建成功")
            return logger_service
        except Exception as e:
            logger.error(f"创建日志服务失败: {e}")
            raise
    
    def create_influxdb_service(self) -> IInfluxDBService:
        """
        创建InfluxDB服务
        
        Returns:
            InfluxDB服务实例
        """
        try:
            influxdb_client = InfluxDBClient(
                url=self.config.get('influxdb', 'url'),
                token=self.config.get('influxdb', 'token'),
                org=self.config.get('influxdb', 'org'),
                bucket=self.config.get('influxdb', 'bucket')
            )
            logger.info("InfluxDB服务创建成功")
            return influxdb_client
        except Exception as e:
            logger.error(f"创建InfluxDB服务失败: {e}")
            raise
    
    def create_mysql_service(self) -> IMySQLService:
        """
        创建MySQL服务
        
        Returns:
            MySQL服务实例
        """
        try:
            mysql_client = MySQLClient(
                host=self.config.get('mysql', 'host'),
                port=self.config.get_int('mysql', 'port'),
                user=self.config.get('mysql', 'user'),
                password=self.config.get('mysql', 'password'),
                database=self.config.get('mysql', 'db')
            )
            logger.info("MySQL服务创建成功")
            return mysql_client
        except Exception as e:
            logger.error(f"创建MySQL服务失败: {e}")
            raise
    
    def create_kafka_service(self) -> IKafkaService:
        """
        创建Kafka服务
        
        Returns:
            Kafka服务实例
        """
        try:
            kafka_client = KafkaClient(self.config)
            logger.info("Kafka服务创建成功")
            return kafka_client
        except Exception as e:
            logger.error(f"创建Kafka服务失败: {e}")
            raise
    
    def create_data_processor_service(self) -> IDataProcessorService:
        """
        创建数据处理服务
        
        Returns:
            数据处理服务实例
        """
        try:
            data_processor = DataProcessor(self.config)
            logger.info("数据处理服务创建成功")
            return data_processor
        except Exception as e:
            logger.error(f"创建数据处理服务失败: {e}")
            raise
    
    def create_data_loader_service(self, influxdb_service: IInfluxDBService,
                                  mysql_service: IMySQLService) -> IDataLoaderService:
        """
        创建数据加载服务
        
        Args:
            influxdb_service: InfluxDB服务
            mysql_service: MySQL服务
            
        Returns:
            数据加载服务实例
        """
        try:
            data_loader = RealDataLoader(
                influxdb_client=influxdb_service,
                mysql_client=mysql_service,
                config=self.config
            )
            logger.info("数据加载服务创建成功")
            return data_loader
        except Exception as e:
            logger.error(f"创建数据加载服务失败: {e}")
            raise
    
    def create_model_version_service(self, mysql_service: IMySQLService) -> IModelVersionService:
        """
        创建模型版本管理服务
        
        Args:
            mysql_service: MySQL服务
            
        Returns:
            模型版本管理服务实例
        """
        try:
            model_version_manager = ModelVersionManager(mysql_service)
            logger.info("模型版本管理服务创建成功")
            return model_version_manager
        except Exception as e:
            logger.error(f"创建模型版本管理服务失败: {e}")
            raise
    
    def create_model_trainer_service(self, model_version_service: IModelVersionService) -> IModelTrainerService:
        """
        创建模型训练服务
        
        Args:
            model_version_service: 模型版本管理服务
            
        Returns:
            模型训练服务实例
        """
        try:
            model_trainer = ModelTrainer(self.config, model_version_service)
            logger.info("模型训练服务创建成功")
            return model_trainer
        except Exception as e:
            logger.error(f"创建模型训练服务失败: {e}")
            raise
    
    def create_prediction_engine_service(self, model_version_service: IModelVersionService) -> IPredictionEngineService:
        """
        创建预测引擎服务
        
        Args:
            model_version_service: 模型版本管理服务
            
        Returns:
            预测引擎服务实例
        """
        try:
            prediction_engine = PredictionEngine(model_version_service)
            logger.info("预测引擎服务创建成功")
            return prediction_engine
        except Exception as e:
            logger.error(f"创建预测引擎服务失败: {e}")
            raise
    
    def create_online_learner_service(self, model_version_service: IModelVersionService) -> IOnlineLearnerService:
        """
        创建在线学习服务
        
        Args:
            model_version_service: 模型版本管理服务
            
        Returns:
            在线学习服务实例
        """
        try:
            online_learner = OnlineLearner(model_version_service)
            logger.info("在线学习服务创建成功")
            return online_learner
        except Exception as e:
            logger.error(f"创建在线学习服务失败: {e}")
            raise
    
    def create_strategy_service(self) -> IStrategyService:
        """
        创建策略服务
        
        Returns:
            策略服务实例
        """
        try:
            strategy = UnifiedMLStrategy(self.config)
            logger.info("策略服务创建成功")
            return strategy
        except Exception as e:
            logger.error(f"创建策略服务失败: {e}")
            raise
    
    def create_health_check_service(self) -> IHealthCheckService:
        """
        创建健康检查服务
        
        Returns:
            健康检查服务实例
        """
        try:
            health_check_service = HealthCheckService()
            logger.info("健康检查服务创建成功")
            return health_check_service
        except Exception as e:
            logger.error(f"创建健康检查服务失败: {e}")
            raise
    
    def create_metrics_service(self) -> IMetricsService:
        """
        创建指标服务
        
        Returns:
            指标服务实例
        """
        try:
            metrics_service = MetricsService()
            logger.info("指标服务创建成功")
            return metrics_service
        except Exception as e:
            logger.error(f"创建指标服务失败: {e}")
            raise


class HealthCheckService:
    """健康检查服务实现"""
    
    def __init__(self):
        """初始化健康检查服务"""
        self._health_checks: Dict[str, callable] = {}
        self._last_check_results: Dict[str, Dict[str, Any]] = {}
    
    def check_health(self) -> Dict[str, Any]:
        """执行健康检查"""
        results = {}
        overall_healthy = True
        
        for name, check_func in self._health_checks.items():
            try:
                result = check_func()
                results[name] = result
                self._last_check_results[name] = result
                
                if not result.get('healthy', False):
                    overall_healthy = False
                    
            except Exception as e:
                error_result = {
                    'healthy': False,
                    'error': str(e),
                    'timestamp': time.time()
                }
                results[name] = error_result
                self._last_check_results[name] = error_result
                overall_healthy = False
        
        return {
            'overall_healthy': overall_healthy,
            'checks': results,
            'timestamp': time.time()
        }
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        return {
            'health_checks': self._last_check_results,
            'registered_checks': list(self._health_checks.keys()),
            'timestamp': time.time()
        }
    
    def register_health_check(self, name: str, check_func: callable) -> None:
        """注册健康检查"""
        self._health_checks[name] = check_func
        logger.info(f"注册健康检查: {name}")


class MetricsService:
    """指标服务实现"""
    
    def __init__(self):
        """初始化指标服务"""
        self._metrics: Dict[str, List[Dict[str, Any]]] = {}
        self._counters: Dict[str, int] = {}
    
    def record_metric(self, name: str, value: float, tags: Optional[Dict[str, str]] = None) -> None:
        """记录指标"""
        if name not in self._metrics:
            self._metrics[name] = []
        
        metric_entry = {
            'value': value,
            'timestamp': time.time(),
            'tags': tags or {}
        }
        
        self._metrics[name].append(metric_entry)
        
        # 保持最近1000个数据点
        if len(self._metrics[name]) > 1000:
            self._metrics[name] = self._metrics[name][-1000:]
    
    def increment_counter(self, name: str, tags: Optional[Dict[str, str]] = None) -> None:
        """递增计数器"""
        counter_key = f"{name}:{str(tags) if tags else ''}"
        self._counters[counter_key] = self._counters.get(counter_key, 0) + 1
    
    def record_timing(self, name: str, duration_ms: float, tags: Optional[Dict[str, str]] = None) -> None:
        """记录时间指标"""
        self.record_metric(f"{name}_duration_ms", duration_ms, tags)
    
    def get_metrics_summary(self) -> Dict[str, Any]:
        """获取指标摘要"""
        summary = {}
        
        for name, values in self._metrics.items():
            if values:
                recent_values = [v['value'] for v in values[-100:]]  # 最近100个值
                summary[name] = {
                    'count': len(values),
                    'latest': values[-1]['value'],
                    'avg': sum(recent_values) / len(recent_values),
                    'min': min(recent_values),
                    'max': max(recent_values)
                }
        
        summary['counters'] = self._counters.copy()
        return summary


if __name__ == "__main__":
    # 测试代码
    import time
    
    # 创建配置服务
    config = Config()
    
    # 创建服务工厂
    factory = ServiceFactory(config)
    
    # 测试创建各种服务
    try:
        logger_service = factory.create_logger_service()
        logger.info("日志服务测试成功")
        
        health_service = factory.create_health_check_service()
        metrics_service = factory.create_metrics_service()
        
        # 测试指标服务
        metrics_service.record_metric("test_metric", 123.45)
        metrics_service.increment_counter("test_counter")
        
        summary = metrics_service.get_metrics_summary()
        logger.info(f"指标摘要: {summary}")
        
    except Exception as e:
        logger.error(f"服务工厂测试失败: {e}")