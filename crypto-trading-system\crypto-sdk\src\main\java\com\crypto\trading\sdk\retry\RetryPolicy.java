package com.crypto.trading.sdk.retry;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.ThreadLocalRandom;
import java.util.function.Predicate;
import java.util.function.Supplier;

/**
 * 指数退避重试策略
 * 用于API调用失败时进行重试，支持自定义重试条件和最大重试次数
 */
public class RetryPolicy {

    private static final Logger log = LoggerFactory.getLogger(RetryPolicy.class);

    private final int maxRetries;
    private final long initialBackoffMs;
    private final long maxBackoffMs;
    private final double backoffMultiplier;
    private final boolean addJitter;
    private final Predicate<Exception> retryCondition;

    /**
     * 使用默认参数创建重试策略
     * 
     * @return 重试策略实例
     */
    public static RetryPolicy defaultPolicy() {
        return new RetryPolicy(3, 100, 10000, 2.0, true, e -> true);
    }
    
    /**
     * 创建重试策略构建器
     * 
     * @return 重试策略构建器
     */
    public static RetryPolicyBuilder builder() {
        return new RetryPolicyBuilder();
    }

    /**
     * 构造函数
     *
     * @param maxRetries        最大重试次数
     * @param initialBackoffMs  初始退避时间（毫秒）
     * @param maxBackoffMs      最大退避时间（毫秒）
     * @param backoffMultiplier 退避乘数
     * @param addJitter         是否添加随机抖动
     * @param retryCondition    重试条件
     */
    public RetryPolicy(int maxRetries, 
                      long initialBackoffMs, 
                      long maxBackoffMs, 
                      double backoffMultiplier, 
                      boolean addJitter, 
                      Predicate<Exception> retryCondition) {
        this.maxRetries = maxRetries;
        this.initialBackoffMs = initialBackoffMs;
        this.maxBackoffMs = maxBackoffMs;
        this.backoffMultiplier = backoffMultiplier;
        this.addJitter = addJitter;
        this.retryCondition = retryCondition;
    }

    /**
     * 执行带有重试机制的操作
     *
     * @param operation 要重试的操作
     * @param <T>       操作返回类型
     * @return 操作结果
     * @throws Exception 如果所有重试都失败，则抛出最后一次失败的异常
     */
    public <T> T execute(Supplier<T> operation) throws Exception {
        Exception lastException = null;
        int attempts = 0;

        while (attempts <= maxRetries) {
            try {
                if (attempts > 0) {
                    log.info("重试操作，尝试次数: {}/{}", attempts, maxRetries);
                }
                return operation.get();
            } catch (Exception e) {
                lastException = e;
                
                // 判断是否应该重试
                if (!retryCondition.test(e)) {
                    log.warn("不满足重试条件，立即抛出异常", e);
                    throw e;
                }
                
                // 已达到最大重试次数
                if (attempts == maxRetries) {
                    log.warn("已达到最大重试次数 {}，操作失败", maxRetries, e);
                    throw e;
                }
                
                // 计算下一次重试前的等待时间
                long backoffTime = calculateBackoffTime(attempts);
                log.info("操作失败，将在 {} ms 后重试，异常: {}", backoffTime, e.getMessage());
                
                try {
                    Thread.sleep(backoffTime);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException("重试过程被中断", ie);
                }
                
                attempts++;
            }
        }
        
        // 代码不应该执行到这里，但如果执行到这里则抛出最后一个异常
        throw new RuntimeException("所有重试均失败", lastException);
    }

    /**
     * 计算退避等待时间
     *
     * @param attempt 当前尝试次数
     * @return 等待时间（毫秒）
     */
    private long calculateBackoffTime(int attempt) {
        double expBackoff = initialBackoffMs * Math.pow(backoffMultiplier, attempt);
        long backoffTime = (long) Math.min(maxBackoffMs, expBackoff);
        
        if (addJitter) {
            // 添加 0-25% 的随机抖动
            backoffTime += ThreadLocalRandom.current().nextLong((long) (backoffTime * 0.25));
        }
        
        return backoffTime;
    }
    
    /**
     * 重试策略构建器
     */
    public static class RetryPolicyBuilder {
        private int maxRetries = 3;
        private long initialBackoffMs = 100;
        private long maxBackoffMs = 10000;
        private double backoffMultiplier = 2.0;
        private boolean addJitter = true;
        private Predicate<Exception> retryCondition = e -> true;
        
        public RetryPolicyBuilder maxRetries(int maxRetries) {
            this.maxRetries = maxRetries;
            return this;
        }
        
        public RetryPolicyBuilder initialBackoffMs(long initialBackoffMs) {
            this.initialBackoffMs = initialBackoffMs;
            return this;
        }
        
        public RetryPolicyBuilder maxBackoffMs(long maxBackoffMs) {
            this.maxBackoffMs = maxBackoffMs;
            return this;
        }
        
        public RetryPolicyBuilder backoffMultiplier(double backoffMultiplier) {
            this.backoffMultiplier = backoffMultiplier;
            return this;
        }
        
        public RetryPolicyBuilder addJitter(boolean addJitter) {
            this.addJitter = addJitter;
            return this;
        }
        
        public RetryPolicyBuilder retryCondition(Predicate<Exception> retryCondition) {
            this.retryCondition = retryCondition;
            return this;
        }
        
        public RetryPolicy build() {
            return new RetryPolicy(maxRetries, initialBackoffMs, maxBackoffMs, 
                                  backoffMultiplier, addJitter, retryCondition);
        }
    }
}