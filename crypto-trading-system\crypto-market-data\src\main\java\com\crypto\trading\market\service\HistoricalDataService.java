package com.crypto.trading.market.service;

import com.crypto.trading.common.dto.market.KlineDataDTO;
import com.crypto.trading.common.enums.KlineInterval;
import com.crypto.trading.market.service.impl.DownloadTaskTracker;

import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 历史数据服务接口
 * 负责从交易所获取历史市场数据，包括K线数据
 */
public interface HistoricalDataService {

    /**
     * 获取历史K线数据
     *
     * @param symbol       交易对
     * @param interval     K线间隔
     * @param startTime    开始时间
     * @param endTime      结束时间
     * @param storeToDb    是否存储到数据库
     * @param sendToKafka  是否发送到Kafka
     * @return K线数据列表
     */
    List<KlineDataDTO> getHistoricalKlines(String symbol, KlineInterval interval, 
                                        LocalDateTime startTime, LocalDateTime endTime, 
                                        boolean storeToDb, boolean sendToKafka);
    
    /**
     * 异步获取历史K线数据
     *
     * @param symbol       交易对
     * @param interval     K线间隔
     * @param startTime    开始时间
     * @param endTime      结束时间
     * @param storeToDb    是否存储到数据库
     * @param sendToKafka  是否发送到Kafka
     * @return 包含K线数据列表的CompletableFuture
     */
    CompletableFuture<List<KlineDataDTO>> getHistoricalKlinesAsync(String symbol, KlineInterval interval, 
                                                                LocalDateTime startTime, LocalDateTime endTime, 
                                                                boolean storeToDb, boolean sendToKafka);
    

    
    /**
     * 批量获取多个交易对的历史K线数据
     *
     * @param symbols      交易对列表
     * @param interval     K线间隔
     * @param startTime    开始时间
     * @param endTime      结束时间
     * @param storeToDb    是否存储到数据库
     * @param sendToKafka  是否发送到Kafka
     * @return 操作结果，包括成功和失败的交易对信息
     */
    String batchDownloadKlines(List<String> symbols, KlineInterval interval, 
                             LocalDateTime startTime, LocalDateTime endTime, 
                             boolean storeToDb, boolean sendToKafka);
    
    /**
     * 使用智能分块批量下载多个交易对的历史K线数据
     * 系统自动确定最优块大小和并发数
     *
     * @param symbols       交易对列表
     * @param interval      K线间隔
     * @param startTime     开始时间
     * @param endTime       结束时间
     * @param storeToDb     是否存储到数据库
     * @param sendToKafka   是否发送到Kafka
     * @return 任务ID
     */
    String batchDownloadKlinesWithSmartChunks(List<String> symbols, KlineInterval interval, 
                                           LocalDateTime startTime, LocalDateTime endTime, 
                                           boolean storeToDb, boolean sendToKafka);
    
    /**
     * 批量获取多个交易对的历史K线数据（增强版）
     *
     * @param symbols            交易对列表
     * @param interval           K线间隔
     * @param startTime          开始时间
     * @param endTime            结束时间
     * @param storeToDb          是否存储到数据库
     * @param sendToKafka        是否发送到Kafka
     * @param incrementalDownload 是否增量下载（只下载缺失部分）
     * @param priority           任务优先级
     * @param maxRetries         最大重试次数
     * @param maxConcurrent      最大并发请求数
     * @param chunkSizeHours     分片大小（小时）
     * @return 任务ID
     */
    String batchDownloadKlinesEnhanced(List<String> symbols, KlineInterval interval, 
                                     LocalDateTime startTime, LocalDateTime endTime, 
                                     boolean storeToDb, boolean sendToKafka,
                                     boolean incrementalDownload,
                                     DownloadTaskTracker.TaskPriority priority,
                                     int maxRetries, int maxConcurrent, int chunkSizeHours);
    

    
    /**
     * 检查是否存在给定时间范围内的历史数据
     *
     * @param symbol       交易对
     * @param dataType     数据类型（kline）
     * @param interval     K线间隔（仅适用于K线数据）
     * @param startTime    开始时间
     * @param endTime      结束时间
     * @return 是否存在数据
     */
    boolean hasHistoricalData(String symbol, String dataType, String interval, 
                            LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 获取下载任务的状态
     *
     * @param taskId 任务ID
     * @return 任务状态信息
     */
    String getTaskStatus(String taskId);
    
    /**
     * 获取所有下载任务的状态
     *
     * @return 所有任务状态信息
     */
    List<String> getAllTaskStatus();

    /**
     * 暂停下载任务
     *
     * @param taskId 任务ID
     * @return 是否成功暂停
     */
    boolean pauseTask(String taskId);
    
    /**
     * 恢复下载任务
     *
     * @param taskId 任务ID
     * @return 是否成功恢复
     */
    boolean resumeTask(String taskId);
    
    /**
     * 取消下载任务
     *
     * @param taskId 任务ID
     * @return 是否成功取消
     */
    boolean cancelTask(String taskId);
    
    /**
     * 智能分块下载K线数据
     * 使用动态调整的块大小和并行下载优化性能
     *
     * @param symbol       交易对
     * @param interval     K线间隔
     * @param startTime    开始时间
     * @param endTime      结束时间
     * @param storeToDb    是否存储到数据库
     * @param sendToKafka  是否发送到Kafka
     * @return K线数据列表
     */
    List<KlineDataDTO> getHistoricalKlinesWithSmartChunks(String symbol, KlineInterval interval, 
                                                       LocalDateTime startTime, LocalDateTime endTime, 
                                                       boolean storeToDb, boolean sendToKafka);
    
    /**
     * 增强型获取历史K线数据
     * 支持断点续传、请求限流控制、并发下载、数据验证等高级功能
     *
     * @param symbol              交易对
     * @param interval            K线间隔
     * @param startTime           开始时间
     * @param endTime             结束时间
     * @param batchSize           每批次请求的数据量（1-1000）
     * @param maxConcurrentBatches 最大并发批次数
     * @param retryLimit          重试限制次数
     * @param storeToDb           是否存储到数据库
     * @param sendToKafka         是否发送到Kafka
     * @return 获取到的K线数据
     */
    List<KlineDataDTO> getHistoricalKlinesEnhanced(String symbol, KlineInterval interval, 
                                               LocalDateTime startTime, LocalDateTime endTime,
                                               int batchSize, int maxConcurrentBatches, int retryLimit,
                                               boolean storeToDb, boolean sendToKafka);
                                               
    /**
     * 使用断点续传机制获取历史K线数据
     * 支持自动恢复失败的下载任务，确保数据完整性
     *
     * @param symbol       交易对
     * @param interval     K线间隔
     * @param startTime    开始时间
     * @param endTime      结束时间
     * @param storeToDb    是否存储到数据库
     * @param sendToKafka  是否发送到Kafka
     * @return K线数据列表
     */
    List<KlineDataDTO> getHistoricalKlinesWithResumption(String symbol, KlineInterval interval, 
                                                      LocalDateTime startTime, LocalDateTime endTime, 
                                                      boolean storeToDb, boolean sendToKafka);
                                                      
    /**
     * 使用断点续传批量下载多个交易对的历史K线数据
     *
     * @param symbols       交易对列表
     * @param interval      K线间隔
     * @param startTime     开始时间
     * @param endTime       结束时间
     * @param storeToDb     是否存储到数据库
     * @param sendToKafka   是否发送到Kafka
     * @return 任务ID
     */
    String batchDownloadKlinesWithResumption(List<String> symbols, KlineInterval interval, 
                                          LocalDateTime startTime, LocalDateTime endTime, 
                                          boolean storeToDb, boolean sendToKafka);
} 