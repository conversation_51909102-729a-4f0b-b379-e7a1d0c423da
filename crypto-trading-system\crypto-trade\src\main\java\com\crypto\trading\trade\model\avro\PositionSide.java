/**
 * Autogenerated by Avro
 *
 * DO NOT EDIT DIRECTLY
 */
package com.crypto.trading.trade.model.avro;
@org.apache.avro.specific.AvroGenerated
public enum PositionSide implements org.apache.avro.generic.GenericEnumSymbol<PositionSide> {
  BOTH, LONG, SHORT  ;
  public static final org.apache.avro.Schema SCHEMA$ = new org.apache.avro.Schema.Parser().parse("{\"type\":\"enum\",\"name\":\"PositionSide\",\"namespace\":\"com.crypto.trading.trade.model.avro\",\"symbols\":[\"BOTH\",\"LONG\",\"SHORT\"]}");
  public static org.apache.avro.Schema getClassSchema() { return SCHEMA$; }

  @Override
  public org.apache.avro.Schema getSchema() { return SCHEMA$; }
}
