package com.crypto.trading.common.dto.trade;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 订单请求数据传输对象
 * <p>
 * 用于接收前端或其他服务发送的订单请求
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class OrderRequestDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 交易对（例如：BTCUSDT）
     */
    private String symbol;

    /**
     * 买卖方向
     * <p>
     * BUY: 买入
     * SELL: 卖出
     * </p>
     */
    private String side;

    /**
     * 持仓方向
     * <p>
     * BOTH: 单向持仓
     * LONG: 多头
     * SHORT: 空头
     * </p>
     */
    private String positionSide;

    /**
     * 订单类型
     * <p>
     * LIMIT: 限价单
     * MARKET: 市价单
     * STOP: 止损单
     * STOP_LIMIT: 限价止损单
     * TAKE_PROFIT: 止盈单
     * TAKE_PROFIT_LIMIT: 限价止盈单
     * </p>
     */
    private String type;

    /**
     * 订单数量
     */
    private BigDecimal quantity;

    /**
     * 订单价格
     */
    private BigDecimal price;

    /**
     * 客户端订单ID
     */
    private String clientOrderId;

    /**
     * 策略ID
     */
    private String strategyId;

    /**
     * 备注
     */
    private String remark;
    
    /**
     * 关联的交易信号ID（可选）
     */
    private String signalId;
    
    /**
     * 有效期类型
     * <p>
     * GTC: 成交为止
     * IOC: 无法立即成交的部分就撤销
     * FOK: 无法全部立即成交就撤销
     * </p>
     */
    private String timeInForce;
    
    /**
     * 止损价（对于止损单必填）
     */
    private BigDecimal stopPrice;
    
    /**
     * 杠杆倍数
     */
    private Integer leverage;
    
    /**
     * 附加参数（JSON格式）
     */
    private String additionalParams;

    /**
     * 获取交易对
     *
     * @return 交易对
     */
    public String getSymbol() {
        return symbol;
    }

    /**
     * 设置交易对
     *
     * @param symbol 交易对
     */
    public void setSymbol(String symbol) {
        this.symbol = symbol;
    }

    /**
     * 获取买卖方向
     *
     * @return 买卖方向
     */
    public String getSide() {
        return side;
    }

    /**
     * 设置买卖方向
     *
     * @param side 买卖方向
     */
    public void setSide(String side) {
        this.side = side;
    }

    /**
     * 获取持仓方向
     *
     * @return 持仓方向
     */
    public String getPositionSide() {
        return positionSide;
    }

    /**
     * 设置持仓方向
     *
     * @param positionSide 持仓方向
     */
    public void setPositionSide(String positionSide) {
        this.positionSide = positionSide;
    }

    /**
     * 获取订单类型
     *
     * @return 订单类型
     */
    public String getType() {
        return type;
    }

    /**
     * 设置订单类型
     *
     * @param type 订单类型
     */
    public void setType(String type) {
        this.type = type;
    }

    /**
     * 获取订单数量
     *
     * @return 订单数量
     */
    public BigDecimal getQuantity() {
        return quantity;
    }

    /**
     * 设置订单数量
     *
     * @param quantity 订单数量
     */
    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    /**
     * 获取订单价格
     *
     * @return 订单价格
     */
    public BigDecimal getPrice() {
        return price;
    }

    /**
     * 设置订单价格
     *
     * @param price 订单价格
     */
    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    /**
     * 获取客户端订单ID
     *
     * @return 客户端订单ID
     */
    public String getClientOrderId() {
        return clientOrderId;
    }

    /**
     * 设置客户端订单ID
     *
     * @param clientOrderId 客户端订单ID
     */
    public void setClientOrderId(String clientOrderId) {
        this.clientOrderId = clientOrderId;
    }

    /**
     * 获取策略ID
     *
     * @return 策略ID
     */
    public String getStrategyId() {
        return strategyId;
    }

    /**
     * 设置策略ID
     *
     * @param strategyId 策略ID
     */
    public void setStrategyId(String strategyId) {
        this.strategyId = strategyId;
    }

    /**
     * 获取策略ID（别名方法，与getStrategyId功能相同）
     *
     * @return 策略ID
     */
    public String getStrategy() {
        return strategyId;
    }

    /**
     * 设置策略ID（别名方法，与setStrategyId功能相同）
     *
     * @param strategy 策略ID
     */
    public void setStrategy(String strategy) {
        this.strategyId = strategy;
    }

    /**
     * 获取备注
     *
     * @return 备注
     */
    public String getRemark() {
        return remark;
    }

    /**
     * 设置备注
     *
     * @param remark 备注
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }
    
    /**
     * 获取交易信号ID
     *
     * @return 交易信号ID
     */
    public String getSignalId() {
        return signalId;
    }

    /**
     * 设置交易信号ID
     *
     * @param signalId 交易信号ID
     */
    public void setSignalId(String signalId) {
        this.signalId = signalId;
    }

    /**
     * 获取有效期类型
     *
     * @return 有效期类型
     */
    public String getTimeInForce() {
        return timeInForce;
    }

    /**
     * 设置有效期类型
     *
     * @param timeInForce 有效期类型
     */
    public void setTimeInForce(String timeInForce) {
        this.timeInForce = timeInForce;
    }

    /**
     * 获取止损价
     *
     * @return 止损价
     */
    public BigDecimal getStopPrice() {
        return stopPrice;
    }

    /**
     * 设置止损价
     *
     * @param stopPrice 止损价
     */
    public void setStopPrice(BigDecimal stopPrice) {
        this.stopPrice = stopPrice;
    }

    /**
     * 获取杠杆倍数
     *
     * @return 杠杆倍数
     */
    public Integer getLeverage() {
        return leverage;
    }

    /**
     * 设置杠杆倍数
     *
     * @param leverage 杠杆倍数
     */
    public void setLeverage(Integer leverage) {
        this.leverage = leverage;
    }

    /**
     * 获取附加参数
     *
     * @return 附加参数（JSON格式）
     */
    public String getAdditionalParams() {
        return additionalParams;
    }

    /**
     * 设置附加参数
     *
     * @param additionalParams 附加参数（JSON格式）
     */
    public void setAdditionalParams(String additionalParams) {
        this.additionalParams = additionalParams;
    }
    
    @Override
    public String toString() {
        return "OrderRequestDTO{" +
                "symbol='" + symbol + '\'' +
                ", clientOrderId='" + clientOrderId + '\'' +
                ", signalId='" + signalId + '\'' +
                ", side='" + side + '\'' +
                ", positionSide='" + positionSide + '\'' +
                ", type='" + type + '\'' +
                ", timeInForce='" + timeInForce + '\'' +
                ", quantity=" + quantity +
                ", price=" + price +
                ", stopPrice=" + stopPrice +
                ", strategyId='" + strategyId + '\'' +
                ", leverage=" + leverage +
                ", remark='" + remark + '\'' +
                '}';
    }
}