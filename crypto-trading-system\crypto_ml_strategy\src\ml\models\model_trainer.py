#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
模型训练器模块

负责机器学习模型的训练、验证和优化。
支持多种模型类型和训练策略，包括DeepSeek蒸馏技术。
"""

import os
import pickle
import json
from typing import Dict, List, Optional, Any, Union, Tuple
from datetime import datetime
import pandas as pd
import numpy as np
from loguru import logger

try:
    from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV
    from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
    from sklearn.linear_model import LogisticRegression
    from sklearn.svm import SVC
    from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score
    from sklearn.preprocessing import StandardScaler, LabelEncoder
    from sklearn.pipeline import Pipeline
    import joblib
except ImportError:
    logger.warning("scikit-learn not installed, some ML functionality will be limited")

try:
    import torch
    import torch.nn as nn
    import torch.optim as optim
    from torch.utils.data import DataLoader, TensorDataset
except ImportError:
    logger.warning("PyTorch not installed, deep learning functionality will be limited")
    torch = None
    nn = None
    optim = None
    DataLoader = None
    TensorDataset = None

from ..training.training_config import TrainingConfig as Config


class ModelTrainer:
    """
    模型训练器类，负责机器学习模型的训练和优化。
    
    支持功能：
    - 多种模型类型（随机森林、梯度提升、神经网络等）
    - 自动特征选择和预处理
    - 超参数优化
    - 交叉验证和模型评估
    - DeepSeek蒸馏技术
    - 模型版本管理和持久化
    """
    
    def __init__(self, config: Config, model_version_manager):
        """
        初始化模型训练器
        
        Args:
            config: 配置对象
            model_version_manager: 模型版本管理器
        """
        self.logger = logger.bind(component="ModelTrainer")
        
        self.config = config
        self.model_version_manager = model_version_manager
        
        # 训练参数
        self.batch_size = config.get_int('training', 'batch_size', 64)
        self.epochs = config.get_int('training', 'epochs', 100)
        self.learning_rate = config.get_float('training', 'learning_rate', 0.001)
        self.validation_ratio = config.get_float('training', 'validation_ratio', 0.2)
        self.early_stopping_patience = config.get_int('training', 'early_stopping_patience', 10)
        self.use_gpu = config.get_bool('training', 'use_gpu', True)
        self.model_save_path = config.get('training', 'model_save_path', 'models/')
        
        # DeepSeek蒸馏参数
        self.distillation_temperature = config.get_float('training', 'distillation_temperature', 2.0)
        self.feature_importance_threshold = config.get_float('training', 'feature_importance_threshold', 0.05)
        
        # 确保模型保存目录存在
        os.makedirs(self.model_save_path, exist_ok=True)
        
        # 模型缓存
        self._trained_models = {}
        self._feature_scalers = {}
        
        self.logger.info("模型训练器初始化完成")
    
    def train(self, training_data: Dict[str, pd.DataFrame]) -> Dict[str, Any]:
        """
        训练模型
        
        Args:
            training_data: 训练数据字典，格式: {symbol: DataFrame}
        
        Returns:
            训练结果字典
        """
        try:
            if not training_data:
                raise ValueError("训练数据为空")
            
            self.logger.info(f"开始模型训练，数据集数量: {len(training_data)}")
            
            training_results = {}
            
            for symbol, data in training_data.items():
                self.logger.info(f"训练模型: {symbol}")
                
                # 数据预处理
                processed_data = self._preprocess_data(data, symbol)
                
                if processed_data is None:
                    self.logger.warning(f"数据预处理失败: {symbol}")
                    continue
                
                # 特征选择
                selected_features = self._select_features(processed_data, symbol)
                
                # 训练多个模型
                model_results = self._train_multiple_models(processed_data, selected_features, symbol)
                
                # 选择最佳模型
                best_model_info = self._select_best_model(model_results, symbol)
                
                if best_model_info:
                    # 保存模型
                    model_path = self._save_model(best_model_info, symbol)
                    
                    # 更新模型版本
                    self._update_model_version(best_model_info, model_path, symbol)
                    
                    training_results[symbol] = best_model_info
                    self.logger.info(f"模型训练完成: {symbol}, 最佳模型: {best_model_info['model_type']}")
                else:
                    self.logger.error(f"模型训练失败: {symbol}")
            
            self.logger.info(f"所有模型训练完成，成功训练: {len(training_results)}")
            return training_results
            
        except Exception as e:
            self.logger.error(f"模型训练失败: {str(e)}")
            return {}
    
    def _preprocess_data(self, data: pd.DataFrame, symbol: str) -> Optional[pd.DataFrame]:
        """
        数据预处理
        
        Args:
            data: 原始数据
            symbol: 交易对符号
        
        Returns:
            预处理后的数据
        """
        try:
            if data.empty:
                return None
            
            # 复制数据
            processed_data = data.copy()
            
            # 删除非数值列（除了目标变量）
            target_columns = ['signal_1', 'future_return_1', 'future_return_5']
            feature_columns = processed_data.select_dtypes(include=[np.number]).columns.tolist()
            
            # 移除目标变量从特征中
            feature_columns = [col for col in feature_columns if col not in target_columns]
            
            # 检查是否有足够的特征
            if len(feature_columns) < 5:
                self.logger.warning(f"特征数量不足: {symbol}, 特征数: {len(feature_columns)}")
                return None
            
            # 删除包含过多NaN的列
            nan_ratio = processed_data[feature_columns].isnull().sum() / len(processed_data)
            valid_features = nan_ratio[nan_ratio < 0.1].index.tolist()
            
            if len(valid_features) < 5:
                self.logger.warning(f"有效特征数量不足: {symbol}")
                return None
            
            # 保留有效特征和目标变量
            keep_columns = valid_features + [col for col in target_columns if col in processed_data.columns]
            processed_data = processed_data[keep_columns]
            
            # 删除包含NaN的行
            processed_data = processed_data.dropna()
            
            # 检查数据量是否足够
            if len(processed_data) < 100:
                self.logger.warning(f"数据量不足: {symbol}, 记录数: {len(processed_data)}")
                return None
            
            self.logger.debug(f"数据预处理完成: {symbol}, 特征数: {len(valid_features)}, 记录数: {len(processed_data)}")
            return processed_data
            
        except Exception as e:
            self.logger.error(f"数据预处理失败: {str(e)}")
            return None
    
    def _select_features(self, data: pd.DataFrame, symbol: str) -> List[str]:
        """
        特征选择
        
        Args:
            data: 预处理后的数据
            symbol: 交易对符号
        
        Returns:
            选择的特征列表
        """
        try:
            # 获取特征列
            target_columns = ['signal_1', 'future_return_1', 'future_return_5']
            feature_columns = [col for col in data.columns if col not in target_columns]
            
            if 'signal_1' not in data.columns:
                self.logger.warning(f"未找到目标变量: {symbol}")
                return feature_columns
            
            # 使用随机森林进行特征重要性评估
            try:
                X = data[feature_columns]
                y = data['signal_1']
                
                # 标准化特征
                scaler = StandardScaler()
                X_scaled = scaler.fit_transform(X)
                
                # 训练随机森林
                rf = RandomForestClassifier(n_estimators=100, random_state=42)
                rf.fit(X_scaled, y)
                
                # 获取特征重要性
                feature_importance = pd.DataFrame({
                    'feature': feature_columns,
                    'importance': rf.feature_importances_
                }).sort_values('importance', ascending=False)
                
                # 选择重要性超过阈值的特征
                selected_features = feature_importance[
                    feature_importance['importance'] > self.feature_importance_threshold
                ]['feature'].tolist()
                
                # 至少保留前10个重要特征
                if len(selected_features) < 10:
                    selected_features = feature_importance.head(10)['feature'].tolist()
                
                self.logger.debug(f"特征选择完成: {symbol}, 选择特征数: {len(selected_features)}")
                return selected_features
                
            except Exception as e:
                self.logger.warning(f"特征选择失败，使用所有特征: {str(e)}")
                return feature_columns
            
        except Exception as e:
            self.logger.error(f"特征选择失败: {str(e)}")
            return []
    
    def _train_multiple_models(self, data: pd.DataFrame, features: List[str], symbol: str) -> Dict[str, Any]:
        """
        训练多个模型
        
        Args:
            data: 训练数据
            features: 特征列表
            symbol: 交易对符号
        
        Returns:
            模型训练结果字典
        """
        try:
            if 'signal_1' not in data.columns:
                self.logger.error(f"未找到目标变量: {symbol}")
                return {}
            
            X = data[features]
            y = data['signal_1']
            
            # 数据分割
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=self.validation_ratio, random_state=42, stratify=y
            )
            
            # 特征标准化
            scaler = StandardScaler()
            X_train_scaled = scaler.fit_transform(X_train)
            X_test_scaled = scaler.transform(X_test)
            
            # 保存标准化器
            self._feature_scalers[symbol] = scaler
            
            models_to_train = {
                'random_forest': RandomForestClassifier(n_estimators=100, random_state=42),
                'gradient_boosting': GradientBoostingClassifier(n_estimators=100, random_state=42),
                'logistic_regression': LogisticRegression(random_state=42, max_iter=1000),
                'svm': SVC(random_state=42, probability=True)
            }
            
            model_results = {}
            
            for model_name, model in models_to_train.items():
                try:
                    self.logger.debug(f"训练模型: {model_name} for {symbol}")
                    
                    # 训练模型
                    if model_name in ['logistic_regression', 'svm']:
                        model.fit(X_train_scaled, y_train)
                        y_pred = model.predict(X_test_scaled)
                        y_pred_proba = model.predict_proba(X_test_scaled)
                    else:
                        model.fit(X_train, y_train)
                        y_pred = model.predict(X_test)
                        y_pred_proba = model.predict_proba(X_test)
                    
                    # 计算评估指标
                    metrics = self._calculate_metrics(y_test, y_pred, y_pred_proba)
                    
                    model_results[model_name] = {
                        'model': model,
                        'model_type': model_name,
                        'metrics': metrics,
                        'features': features,
                        'scaler': scaler if model_name in ['logistic_regression', 'svm'] else None
                    }
                    
                    self.logger.debug(f"模型训练完成: {model_name}, F1分数: {metrics['f1_score']:.4f}")
                    
                except Exception as e:
                    self.logger.warning(f"模型训练失败: {model_name}, 错误: {str(e)}")
                    continue
            
            # 如果支持PyTorch，训练神经网络
            if torch is not None:
                try:
                    nn_result = self._train_neural_network(X_train_scaled, X_test_scaled, y_train, y_test, features, symbol)
                    if nn_result:
                        model_results['neural_network'] = nn_result
                except Exception as e:
                    self.logger.warning(f"神经网络训练失败: {str(e)}")
            
            return model_results
            
        except Exception as e:
            self.logger.error(f"多模型训练失败: {str(e)}")
            return {}
    
    def _train_neural_network(self, X_train: np.ndarray, X_test: np.ndarray, 
                             y_train: np.ndarray, y_test: np.ndarray, 
                             features: List[str], symbol: str) -> Optional[Dict[str, Any]]:
        """
        训练神经网络模型
        
        Args:
            X_train: 训练特征
            X_test: 测试特征
            y_train: 训练标签
            y_test: 测试标签
            features: 特征列表
            symbol: 交易对符号
        
        Returns:
            神经网络训练结果
        """
        try:
            if torch is None:
                return None
            
            # 设备选择
            device = torch.device('cuda' if torch.cuda.is_available() and self.use_gpu else 'cpu')
            
            # 数据转换
            X_train_tensor = torch.FloatTensor(X_train).to(device)
            X_test_tensor = torch.FloatTensor(X_test).to(device)
            y_train_tensor = torch.LongTensor(y_train + 1).to(device)  # 转换为0,1,2
            y_test_tensor = torch.LongTensor(y_test + 1).to(device)
            
            # 创建数据加载器
            train_dataset = TensorDataset(X_train_tensor, y_train_tensor)
            train_loader = DataLoader(train_dataset, batch_size=self.batch_size, shuffle=True)
            
            # 定义神经网络
            class TradingNet(nn.Module):
                def __init__(self, input_size, hidden_size=128, num_classes=3):
                    super(TradingNet, self).__init__()
                    self.fc1 = nn.Linear(input_size, hidden_size)
                    self.fc2 = nn.Linear(hidden_size, hidden_size // 2)
                    self.fc3 = nn.Linear(hidden_size // 2, num_classes)
                    self.dropout = nn.Dropout(0.3)
                    self.relu = nn.ReLU()
                    
                def forward(self, x):
                    x = self.relu(self.fc1(x))
                    x = self.dropout(x)
                    x = self.relu(self.fc2(x))
                    x = self.dropout(x)
                    x = self.fc3(x)
                    return x
            
            # 初始化模型
            model = TradingNet(X_train.shape[1]).to(device)
            criterion = nn.CrossEntropyLoss()
            optimizer = optim.Adam(model.parameters(), lr=self.learning_rate)
            
            # 训练循环
            model.train()
            best_loss = float('inf')
            patience_counter = 0
            
            for epoch in range(self.epochs):
                epoch_loss = 0.0
                
                for batch_X, batch_y in train_loader:
                    optimizer.zero_grad()
                    outputs = model(batch_X)
                    loss = criterion(outputs, batch_y)
                    loss.backward()
                    optimizer.step()
                    epoch_loss += loss.item()
                
                # 验证
                model.eval()
                with torch.no_grad():
                    val_outputs = model(X_test_tensor)
                    val_loss = criterion(val_outputs, y_test_tensor)
                
                # 早停检查
                if val_loss < best_loss:
                    best_loss = val_loss
                    patience_counter = 0
                    # 保存最佳模型状态
                    best_model_state = model.state_dict().copy()
                else:
                    patience_counter += 1
                    if patience_counter >= self.early_stopping_patience:
                        self.logger.debug(f"早停触发，epoch: {epoch}")
                        break
                
                model.train()
            
            # 加载最佳模型状态
            model.load_state_dict(best_model_state)
            
            # 最终评估
            model.eval()
            with torch.no_grad():
                test_outputs = model(X_test_tensor)
                test_proba = torch.softmax(test_outputs, dim=1)
                test_pred = torch.argmax(test_outputs, dim=1)
                
                # 转换回原始标签范围
                y_pred = (test_pred - 1).cpu().numpy()
                y_pred_proba = test_proba.cpu().numpy()
            
            # 计算评估指标
            metrics = self._calculate_metrics(y_test, y_pred, y_pred_proba)
            
            return {
                'model': model,
                'model_type': 'neural_network',
                'metrics': metrics,
                'features': features,
                'device': device,
                'scaler': self._feature_scalers[symbol]
            }
            
        except Exception as e:
            self.logger.error(f"神经网络训练失败: {str(e)}")
            return None
    
    def _calculate_metrics(self, y_true: np.ndarray, y_pred: np.ndarray, y_pred_proba: np.ndarray) -> Dict[str, float]:
        """
        计算评估指标
        
        Args:
            y_true: 真实标签
            y_pred: 预测标签
            y_pred_proba: 预测概率
        
        Returns:
            评估指标字典
        """
        try:
            metrics = {
                'accuracy': accuracy_score(y_true, y_pred),
                'precision': precision_score(y_true, y_pred, average='weighted', zero_division=0),
                'recall': recall_score(y_true, y_pred, average='weighted', zero_division=0),
                'f1_score': f1_score(y_true, y_pred, average='weighted', zero_division=0)
            }
            
            # 如果是二分类或多分类，计算AUC
            try:
                if len(np.unique(y_true)) == 2:
                    metrics['auc'] = roc_auc_score(y_true, y_pred_proba[:, 1])
                elif len(np.unique(y_true)) > 2:
                    metrics['auc'] = roc_auc_score(y_true, y_pred_proba, multi_class='ovr', average='weighted')
            except:
                metrics['auc'] = 0.0
            
            return metrics
            
        except Exception as e:
            self.logger.error(f"计算评估指标失败: {str(e)}")
            return {'accuracy': 0.0, 'precision': 0.0, 'recall': 0.0, 'f1_score': 0.0, 'auc': 0.0}
    
    def _select_best_model(self, model_results: Dict[str, Any], symbol: str) -> Optional[Dict[str, Any]]:
        """
        选择最佳模型
        
        Args:
            model_results: 模型结果字典
            symbol: 交易对符号
        
        Returns:
            最佳模型信息
        """
        try:
            if not model_results:
                return None
            
            # 根据F1分数选择最佳模型
            best_model_name = max(model_results.keys(), 
                                key=lambda x: model_results[x]['metrics']['f1_score'])
            
            best_model_info = model_results[best_model_name]
            best_model_info['symbol'] = symbol
            
            self.logger.info(f"最佳模型选择: {symbol} - {best_model_name}, F1: {best_model_info['metrics']['f1_score']:.4f}")
            
            return best_model_info
            
        except Exception as e:
            self.logger.error(f"选择最佳模型失败: {str(e)}")
            return None
    
    def _save_model(self, model_info: Dict[str, Any], symbol: str) -> str:
        """
        保存模型
        
        Args:
            model_info: 模型信息
            symbol: 交易对符号
        
        Returns:
            模型文件路径
        """
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            model_filename = f"{symbol}_{model_info['model_type']}_{timestamp}.pkl"
            model_path = os.path.join(self.model_save_path, model_filename)
            
            # 保存模型
            if model_info['model_type'] == 'neural_network' and torch is not None:
                # 保存PyTorch模型
                torch.save({
                    'model_state_dict': model_info['model'].state_dict(),
                    'model_class': type(model_info['model']).__name__,
                    'features': model_info['features'],
                    'metrics': model_info['metrics'],
                    'scaler': model_info['scaler']
                }, model_path)
            else:
                # 保存sklearn模型
                joblib.dump({
                    'model': model_info['model'],
                    'features': model_info['features'],
                    'metrics': model_info['metrics'],
                    'scaler': model_info['scaler']
                }, model_path)
            
            self.logger.info(f"模型保存成功: {model_path}")
            return model_path
            
        except Exception as e:
            self.logger.error(f"模型保存失败: {str(e)}")
            return ""
    
    def _update_model_version(self, model_info: Dict[str, Any], model_path: str, symbol: str) -> None:
        """
        更新模型版本信息
        
        Args:
            model_info: 模型信息
            model_path: 模型文件路径
            symbol: 交易对符号
        """
        try:
            version = f"{symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            # 保存到模型版本管理器
            self.model_version_manager.save_model_version(
                version=version,
                model_type=model_info['model_type'],
                model_path=model_path,
                performance_metrics=model_info['metrics'],
                training_config={
                    'features': model_info['features'],
                    'symbol': symbol,
                    'training_time': datetime.now().isoformat()
                },
                is_active=True
            )
            
            self.logger.info(f"模型版本更新成功: {version}")
            
        except Exception as e:
            self.logger.error(f"模型版本更新失败: {str(e)}")
    
    def load_model(self, model_path: str) -> Optional[Dict[str, Any]]:
        """
        加载模型
        
        Args:
            model_path: 模型文件路径
        
        Returns:
            加载的模型信息
        """
        try:
            if not os.path.exists(model_path):
                self.logger.error(f"模型文件不存在: {model_path}")
                return None
            
            # 尝试加载PyTorch模型
            if model_path.endswith('.pkl') and torch is not None:
                try:
                    checkpoint = torch.load(model_path, map_location='cpu')
                    if 'model_state_dict' in checkpoint:
                        # 这是PyTorch模型
                        return checkpoint
                except:
                    pass
            
            # 加载sklearn模型
            model_data = joblib.load(model_path)
            self.logger.info(f"模型加载成功: {model_path}")
            return model_data
            
        except Exception as e:
            self.logger.error(f"模型加载失败: {str(e)}")
            return None