# Common模块配置文件
# 数据库相关配置
database:
  pool:
    initial-size: 10
    min-idle: 10
    max-active: 100
    max-wait: 60000
    time-between-eviction-runs-millis: 60000
    min-evictable-idle-time-millis: 300000
    validation-query: SELECT 1
    test-while-idle: true
    test-on-borrow: false
    test-on-return: false

# Kafka相关配置
kafka:
  retry:
    max-attempts: 3
    backoff:
      initial-interval: 1000
      multiplier: 2.0
      max-interval: 10000
  # Schema Registry配置
  schema-registry-url: http://localhost:8081
  avro-serialization-enabled: true
  # 优化生产者配置
  producer:
    batch-size: 65536
    linger-ms: 20
    compression-type: lz4
    acks: 1
    buffer-memory: 134217728
  ssl:
    enabled: false
    key-password: 
    keystore-location: 
    keystore-password: 
    truststore-location: 
    truststore-password: 

# 日志相关配置
logging:
  config:
    path: classpath:logback-common.xml
  appender:
    console: true
    file: true
    rolling-policy:
      max-history: 7
      max-file-size: 100MB