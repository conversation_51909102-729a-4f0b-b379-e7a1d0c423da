<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (21) on Fri Apr 04 16:06:08 CST 2025 -->
<title>UMMarket (io.github.binance:binance-futures-connector-java 3.0.5 API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-04-04">
<meta name="description" content="declaration: package: com.binance.connector.futures.client.impl.um_futures, class: UMMarket">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="切换导航链接"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="../../../../../../../index.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="nav-bar-cell1-rev">类</li>
<li><a href="class-use/UMMarket.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../../../index-all.html">索引</a></li>
<li><a href="../../../../../../../help-doc.html#class">帮助</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>概要:</p>
<ul>
<li>嵌套</li>
<li>字段</li>
<li><a href="#constructor-summary">构造器</a></li>
<li><a href="#method-summary">方法</a></li>
</ul>
</li>
<li>
<p>详细资料:</p>
<ul>
<li>字段</li>
<li><a href="#constructor-detail">构造器</a></li>
<li><a href="#method-detail">方法</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">方法</a></li>
</ul>
<ul class="sub-nav-list">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">方法</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="搜索">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">程序包</span>&nbsp;<a href="package-summary.html">com.binance.connector.futures.client.impl.um_futures</a></div>
<h1 title="类 UMMarket" class="title">类 UMMarket</h1>
</div>
<div class="inheritance" title="继承树"><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../futures/Market.html" title="com.binance.connector.futures.client.impl.futures中的类">com.binance.connector.futures.client.impl.futures.Market</a>
<div class="inheritance">com.binance.connector.futures.client.impl.um_futures.UMMarket</div>
</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">UMMarket</span>
<span class="extends-implements">extends <a href="../futures/Market.html" title="com.binance.connector.futures.client.impl.futures中的类">Market</a></span></div>
<div class="block"><h2 id="usd--margined-market-endpoints-heading">USDⓈ-Margined Market Endpoints</h2>
 All endpoints under the
 <a href="https://developers.binance.com/docs/derivatives/usds-margined-futures/general-info">Market Data Endpoint</a>
 section of the API documentation will be implemented in this class.
 <br>
 Response will be returned in <i>String format</i>.</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>构造器概要</h2>
<div class="caption"><span>构造器</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">构造器</div>
<div class="table-header col-last">说明</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(java.lang.String,java.lang.String,java.lang.String,boolean,com.binance.connector.futures.client.utils.ProxyAuth)" class="member-name-link">UMMarket</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;productUrl,
 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;baseUrl,
 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;apiKey,
 boolean&nbsp;showLimitUsage,
 com.binance.connector.futures.client.utils.ProxyAuth&nbsp;proxy)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>方法概要</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">所有方法</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">实例方法</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">具体方法</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">修饰符和类型</div>
<div class="table-header col-second">方法</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#assetIndex(java.util.LinkedHashMap)" class="member-name-link">assetIndex</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/util/LinkedHashMap.html" title="java.util中的类或接口" class="external-link">LinkedHashMap</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&nbsp;parameters)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">asset index for Multi-Assets mode
 <br><br>
 GET /v1/assetIndex
 <br>
 https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Multi-Assets-Mode-Asset-Index</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#bookTicker(java.util.LinkedHashMap)" class="member-name-link">bookTicker</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/util/LinkedHashMap.html" title="java.util中的类或接口" class="external-link">LinkedHashMap</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&nbsp;parameters)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Best price/qty on the order book for a symbol or symbols.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#historicalBlvt(java.util.LinkedHashMap)" class="member-name-link">historicalBlvt</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/util/LinkedHashMap.html" title="java.util中的类或接口" class="external-link">LinkedHashMap</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&nbsp;parameters)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">The BLVT NAV system is based on Binance Futures, so the endpoint is based on fapi
 <br><br>
 GET /v1/lvtKlines
 <br>
 https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Historical-BLVT-NAV-Kline-Candlestick</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#indexInfo(java.util.LinkedHashMap)" class="member-name-link">indexInfo</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/util/LinkedHashMap.html" title="java.util中的类或接口" class="external-link">LinkedHashMap</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&nbsp;parameters)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">GET /v1/indexInfo
 <br>
 https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Composite-Index-Symbol-Information</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#longShortRatio(java.util.LinkedHashMap)" class="member-name-link">longShortRatio</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/util/LinkedHashMap.html" title="java.util中的类或接口" class="external-link">LinkedHashMap</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&nbsp;parameters)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Long/Short Ratio
 <br><br>
 GET /futures/data/globalLongShortAccountRatio
 <br>
 https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Long-Short-Ratio</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#markPrice(java.util.LinkedHashMap)" class="member-name-link">markPrice</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/util/LinkedHashMap.html" title="java.util中的类或接口" class="external-link">LinkedHashMap</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&nbsp;parameters)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Mark Price and Funding Rate
 <br><br>
 GET /v1/premiumIndex
 <br></div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#openInterestStatistics(java.util.LinkedHashMap)" class="member-name-link">openInterestStatistics</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/util/LinkedHashMap.html" title="java.util中的类或接口" class="external-link">LinkedHashMap</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&nbsp;parameters)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Open Interest History
 <br><br>
 GET /futures/data/openInterestHist
 <br>
 https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Open-Interest-Statistics</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#takerBuySellVol(java.util.LinkedHashMap)" class="member-name-link">takerBuySellVol</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/util/LinkedHashMap.html" title="java.util中的类或接口" class="external-link">LinkedHashMap</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&nbsp;parameters)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Taker Buy/Sell Volume
 <br><br>
 GET /futures/data/takerlongshortRatio
 <br>
 https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Taker-BuySell-Volume</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#ticker24H(java.util.LinkedHashMap)" class="member-name-link">ticker24H</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/util/LinkedHashMap.html" title="java.util中的类或接口" class="external-link">LinkedHashMap</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&nbsp;parameters)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">24 hour rolling window price change statistics.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#tickerSymbol(java.util.LinkedHashMap)" class="member-name-link">tickerSymbol</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/util/LinkedHashMap.html" title="java.util中的类或接口" class="external-link">LinkedHashMap</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&nbsp;parameters)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Latest price for a symbol or symbols.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#topTraderLongShortAccs(java.util.LinkedHashMap)" class="member-name-link">topTraderLongShortAccs</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/util/LinkedHashMap.html" title="java.util中的类或接口" class="external-link">LinkedHashMap</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&nbsp;parameters)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Top Trader Long/Short Ratio (Accounts)
 <br><br>
 GET /futures/data/topLongShortAccountRatio
 <br>
 https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Top-Long-Short-Account-Ratio</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#topTraderLongShortPos(java.util.LinkedHashMap)" class="member-name-link">topTraderLongShortPos</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/util/LinkedHashMap.html" title="java.util中的类或接口" class="external-link">LinkedHashMap</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&nbsp;parameters)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Top Trader Long/Short Ratio (Positions)
 <br><br>
 GET /futures/data/topLongShortPositionRatio
 <br>
 https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Top-Trader-Long-Short-Ratio</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-com.binance.connector.futures.client.impl.futures.Market">从类继承的方法&nbsp;com.binance.connector.futures.client.impl.futures.<a href="../futures/Market.html" title="com.binance.connector.futures.client.impl.futures中的类">Market</a></h3>
<code><a href="../futures/Market.html#aggTrades(java.util.LinkedHashMap)">aggTrades</a>, <a href="../futures/Market.html#continuousKlines(java.util.LinkedHashMap)">continuousKlines</a>, <a href="../futures/Market.html#depth(java.util.LinkedHashMap)">depth</a>, <a href="../futures/Market.html#exchangeInfo()">exchangeInfo</a>, <a href="../futures/Market.html#fundingRate(java.util.LinkedHashMap)">fundingRate</a>, <a href="../futures/Market.html#getBaseUrl()">getBaseUrl</a>, <a href="../futures/Market.html#getProductUrl()">getProductUrl</a>, <a href="../futures/Market.html#getRequestHandler()">getRequestHandler</a>, <a href="../futures/Market.html#getShowLimitUsage()">getShowLimitUsage</a>, <a href="../futures/Market.html#historicalTrades(java.util.LinkedHashMap)">historicalTrades</a>, <a href="../futures/Market.html#indexPriceKlines(java.util.LinkedHashMap)">indexPriceKlines</a>, <a href="../futures/Market.html#klines(java.util.LinkedHashMap)">klines</a>, <a href="../futures/Market.html#markPriceKlines(java.util.LinkedHashMap)">markPriceKlines</a>, <a href="../futures/Market.html#openInterest(java.util.LinkedHashMap)">openInterest</a>, <a href="../futures/Market.html#ping()">ping</a>, <a href="../futures/Market.html#setBaseUrl(java.lang.String)">setBaseUrl</a>, <a href="../futures/Market.html#setProductUrl(java.lang.String)">setProductUrl</a>, <a href="../futures/Market.html#setRequestHandler(java.lang.String,java.lang.String,com.binance.connector.futures.client.utils.ProxyAuth)">setRequestHandler</a>, <a href="../futures/Market.html#setShowLimitUsage(boolean)">setShowLimitUsage</a>, <a href="../futures/Market.html#time()">time</a>, <a href="../futures/Market.html#trades(java.util.LinkedHashMap)">trades</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">从类继承的方法&nbsp;java.lang.<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a></h3>
<code><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html#clone--" title="java.lang中的类或接口" class="external-link">clone</a>, <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html#equals-java.lang.Object-" title="java.lang中的类或接口" class="external-link">equals</a>, <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html#finalize--" title="java.lang中的类或接口" class="external-link">finalize</a>, <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html#getClass--" title="java.lang中的类或接口" class="external-link">getClass</a>, <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html#hashCode--" title="java.lang中的类或接口" class="external-link">hashCode</a>, <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html#notify--" title="java.lang中的类或接口" class="external-link">notify</a>, <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html#notifyAll--" title="java.lang中的类或接口" class="external-link">notifyAll</a>, <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html#toString--" title="java.lang中的类或接口" class="external-link">toString</a>, <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html#wait--" title="java.lang中的类或接口" class="external-link">wait</a>, <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html#wait-long-" title="java.lang中的类或接口" class="external-link">wait</a>, <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html#wait-long-int-" title="java.lang中的类或接口" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>构造器详细资料</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;(java.lang.String,java.lang.String,java.lang.String,boolean,com.binance.connector.futures.client.utils.ProxyAuth)">
<h3>UMMarket</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">UMMarket</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;productUrl,
 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;baseUrl,
 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;apiKey,
 boolean&nbsp;showLimitUsage,
 com.binance.connector.futures.client.utils.ProxyAuth&nbsp;proxy)</span></div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>方法详细资料</h2>
<ul class="member-list">
<li>
<section class="detail" id="markPrice(java.util.LinkedHashMap)">
<h3>markPrice</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></span>&nbsp;<span class="element-name">markPrice</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/util/LinkedHashMap.html" title="java.util中的类或接口" class="external-link">LinkedHashMap</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&nbsp;parameters)</span></div>
<div class="block">Mark Price and Funding Rate
 <br><br>
 GET /v1/premiumIndex
 <br></div>
<dl class="notes">
<dt>覆盖:</dt>
<dd><code><a href="../futures/Market.html#markPrice(java.util.LinkedHashMap)">markPrice</a></code>&nbsp;在类中&nbsp;<code><a href="../futures/Market.html" title="com.binance.connector.futures.client.impl.futures中的类">Market</a></code></dd>
<dt>参数:</dt>
<dd><code>parameters</code> - LinkedHashedMap of String,Object pair
            where String is the name of the parameter and Object is the value of the parameter
 <br><br>
 symbol -- optional/string -- the trading symbol <br></dd>
<dt>返回:</dt>
<dd>String</dd>
<dt>另请参阅:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Mark-Price">
     https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Mark-Price</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="ticker24H(java.util.LinkedHashMap)">
<h3>ticker24H</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></span>&nbsp;<span class="element-name">ticker24H</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/util/LinkedHashMap.html" title="java.util中的类或接口" class="external-link">LinkedHashMap</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&nbsp;parameters)</span></div>
<div class="block">24 hour rolling window price change statistics. Careful when accessing this with no symbol.
 <br><br>
 GET /v1/ticker/24hr
 <br></div>
<dl class="notes">
<dt>覆盖:</dt>
<dd><code><a href="../futures/Market.html#ticker24H(java.util.LinkedHashMap)">ticker24H</a></code>&nbsp;在类中&nbsp;<code><a href="../futures/Market.html" title="com.binance.connector.futures.client.impl.futures中的类">Market</a></code></dd>
<dt>参数:</dt>
<dd><code>parameters</code> - LinkedHashedMap of String,Object pair
            where String is the name of the parameter and Object is the value of the parameter
 <br><br>
 symbol -- optional/string -- the trading symbol <br></dd>
<dt>返回:</dt>
<dd>String</dd>
<dt>另请参阅:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/24hr-Ticker-Price-Change-Statistics">
     https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/24hr-Ticker-Price-Change-Statistics</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="tickerSymbol(java.util.LinkedHashMap)">
<h3>tickerSymbol</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></span>&nbsp;<span class="element-name">tickerSymbol</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/util/LinkedHashMap.html" title="java.util中的类或接口" class="external-link">LinkedHashMap</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&nbsp;parameters)</span></div>
<div class="block">Latest price for a symbol or symbols.
 <br><br>
 GET /v1/ticker/price
 <br>
 https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Symbol-Price-Ticker</div>
<dl class="notes">
<dt>覆盖:</dt>
<dd><code><a href="../futures/Market.html#tickerSymbol(java.util.LinkedHashMap)">tickerSymbol</a></code>&nbsp;在类中&nbsp;<code><a href="../futures/Market.html" title="com.binance.connector.futures.client.impl.futures中的类">Market</a></code></dd>
<dt>参数:</dt>
<dd><code>parameters</code> - LinkedHashedMap of String,Object pair
            where String is the name of the parameter and Object is the value of the parameter
 <br><br>
 symbol -- optional/string -- the trading symbol <br></dd>
<dt>返回:</dt>
<dd>String</dd>
<dt>另请参阅:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Symbol-Price-Ticker">
     https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Symbol-Price-Ticker</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="bookTicker(java.util.LinkedHashMap)">
<h3>bookTicker</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></span>&nbsp;<span class="element-name">bookTicker</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/util/LinkedHashMap.html" title="java.util中的类或接口" class="external-link">LinkedHashMap</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&nbsp;parameters)</span></div>
<div class="block">Best price/qty on the order book for a symbol or symbols.
 <br><br>
 GET /v1/ticker/bookTicker
 <br>
 https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Symbol-Order-Book-Ticker</div>
<dl class="notes">
<dt>覆盖:</dt>
<dd><code><a href="../futures/Market.html#bookTicker(java.util.LinkedHashMap)">bookTicker</a></code>&nbsp;在类中&nbsp;<code><a href="../futures/Market.html" title="com.binance.connector.futures.client.impl.futures中的类">Market</a></code></dd>
<dt>参数:</dt>
<dd><code>parameters</code> - LinkedHashedMap of String,Object pair
            where String is the name of the parameter and Object is the value of the parameter
 <br><br>
 symbol -- optional/string -- the trading symbol <br></dd>
<dt>返回:</dt>
<dd>String</dd>
<dt>另请参阅:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Symbol-Order-Book-Ticker">
     https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Symbol-Order-Book-Ticker</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="openInterestStatistics(java.util.LinkedHashMap)">
<h3>openInterestStatistics</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></span>&nbsp;<span class="element-name">openInterestStatistics</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/util/LinkedHashMap.html" title="java.util中的类或接口" class="external-link">LinkedHashMap</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&nbsp;parameters)</span></div>
<div class="block">Open Interest History
 <br><br>
 GET /futures/data/openInterestHist
 <br>
 https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Open-Interest-Statistics</div>
<dl class="notes">
<dt>覆盖:</dt>
<dd><code><a href="../futures/Market.html#openInterestStatistics(java.util.LinkedHashMap)">openInterestStatistics</a></code>&nbsp;在类中&nbsp;<code><a href="../futures/Market.html" title="com.binance.connector.futures.client.impl.futures中的类">Market</a></code></dd>
<dt>参数:</dt>
<dd><code>parameters</code> - LinkedHashedMap of String,Object pair
            where String is the name of the parameter and Object is the value of the parameter
 <br><br>
 symbol -- mandatory/string -- the trading pair <br>
 period -- mandatory/enum -- "5m","15m","30m","1h","2h","4h","6h","12h","1d" <br>
 limit -- optional/long -- default 30, max 500 <br>
 startTime -- optional/long -- Start Time <br>
 endTime -- optional/long -- End Time <br></dd>
<dt>返回:</dt>
<dd>String</dd>
<dt>另请参阅:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Open-Interest-Statistics">
     https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Open-Interest-Statistics</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="topTraderLongShortPos(java.util.LinkedHashMap)">
<h3>topTraderLongShortPos</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></span>&nbsp;<span class="element-name">topTraderLongShortPos</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/util/LinkedHashMap.html" title="java.util中的类或接口" class="external-link">LinkedHashMap</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&nbsp;parameters)</span></div>
<div class="block">Top Trader Long/Short Ratio (Positions)
 <br><br>
 GET /futures/data/topLongShortPositionRatio
 <br>
 https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Top-Trader-Long-Short-Ratio</div>
<dl class="notes">
<dt>覆盖:</dt>
<dd><code><a href="../futures/Market.html#topTraderLongShortPos(java.util.LinkedHashMap)">topTraderLongShortPos</a></code>&nbsp;在类中&nbsp;<code><a href="../futures/Market.html" title="com.binance.connector.futures.client.impl.futures中的类">Market</a></code></dd>
<dt>参数:</dt>
<dd><code>parameters</code> - LinkedHashedMap of String,Object pair
            where String is the name of the parameter and Object is the value of the parameter
 <br><br>
 symbol -- mandatory/string -- the trading pair <br>
 period -- mandatory/enum -- "5m","15m","30m","1h","2h","4h","6h","12h","1d" <br>
 limit -- optional/long -- default 30, max 500 <br>
 startTime -- optional/long -- Start Time <br>
 endTime -- optional/long -- End Time <br></dd>
<dt>返回:</dt>
<dd>String</dd>
<dt>另请参阅:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Top-Trader-Long-Short-Ratio">
     https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Top-Trader-Long-Short-Ratio</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="topTraderLongShortAccs(java.util.LinkedHashMap)">
<h3>topTraderLongShortAccs</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></span>&nbsp;<span class="element-name">topTraderLongShortAccs</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/util/LinkedHashMap.html" title="java.util中的类或接口" class="external-link">LinkedHashMap</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&nbsp;parameters)</span></div>
<div class="block">Top Trader Long/Short Ratio (Accounts)
 <br><br>
 GET /futures/data/topLongShortAccountRatio
 <br>
 https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Top-Long-Short-Account-Ratio</div>
<dl class="notes">
<dt>覆盖:</dt>
<dd><code><a href="../futures/Market.html#topTraderLongShortAccs(java.util.LinkedHashMap)">topTraderLongShortAccs</a></code>&nbsp;在类中&nbsp;<code><a href="../futures/Market.html" title="com.binance.connector.futures.client.impl.futures中的类">Market</a></code></dd>
<dt>参数:</dt>
<dd><code>parameters</code> - LinkedHashedMap of String,Object pair
            where String is the name of the parameter and Object is the value of the parameter
 <br><br>
 symbol -- mandatory/string -- the trading pair <br>
 period -- mandatory/enum -- "5m","15m","30m","1h","2h","4h","6h","12h","1d" <br>
 limit -- optional/long -- default 30, max 500 <br>
 startTime -- optional/long -- Start Time <br>
 endTime -- optional/long -- End Time <br></dd>
<dt>返回:</dt>
<dd>String</dd>
<dt>另请参阅:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Top-Long-Short-Account-Ratio">
     https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Top-Long-Short-Account-Ratio</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="longShortRatio(java.util.LinkedHashMap)">
<h3>longShortRatio</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></span>&nbsp;<span class="element-name">longShortRatio</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/util/LinkedHashMap.html" title="java.util中的类或接口" class="external-link">LinkedHashMap</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&nbsp;parameters)</span></div>
<div class="block">Long/Short Ratio
 <br><br>
 GET /futures/data/globalLongShortAccountRatio
 <br>
 https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Long-Short-Ratio</div>
<dl class="notes">
<dt>覆盖:</dt>
<dd><code><a href="../futures/Market.html#longShortRatio(java.util.LinkedHashMap)">longShortRatio</a></code>&nbsp;在类中&nbsp;<code><a href="../futures/Market.html" title="com.binance.connector.futures.client.impl.futures中的类">Market</a></code></dd>
<dt>参数:</dt>
<dd><code>parameters</code> - LinkedHashedMap of String,Object pair
            where String is the name of the parameter and Object is the value of the parameter
 <br><br>
 symbol -- mandatory/string -- the trading pair <br>
 period -- mandatory/enum -- "5m","15m","30m","1h","2h","4h","6h","12h","1d" <br>
 limit -- optional/long -- default 30, max 500 <br>
 startTime -- optional/long -- Start Time <br>
 endTime -- optional/long -- End Time <br></dd>
<dt>返回:</dt>
<dd>String</dd>
<dt>另请参阅:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Long-Short-Ratio">
     https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Long-Short-Ratio</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="takerBuySellVol(java.util.LinkedHashMap)">
<h3>takerBuySellVol</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></span>&nbsp;<span class="element-name">takerBuySellVol</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/util/LinkedHashMap.html" title="java.util中的类或接口" class="external-link">LinkedHashMap</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&nbsp;parameters)</span></div>
<div class="block">Taker Buy/Sell Volume
 <br><br>
 GET /futures/data/takerlongshortRatio
 <br>
 https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Taker-BuySell-Volume</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>parameters</code> - LinkedHashedMap of String,Object pair
            where String is the name of the parameter and Object is the value of the parameter
 <br><br>
 symbol -- mandatory/string -- the trading pair <br>
 period -- mandatory/enum -- "5m","15m","30m","1h","2h","4h","6h","12h","1d" <br>
 limit -- optional/long -- default 30, max 500 <br>
 startTime -- optional/long -- Start Time <br>
 endTime -- optional/long -- End Time <br></dd>
<dt>返回:</dt>
<dd>String</dd>
<dt>另请参阅:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Taker-BuySell-Volume">
     https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Taker-BuySell-Volume</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="historicalBlvt(java.util.LinkedHashMap)">
<h3>historicalBlvt</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></span>&nbsp;<span class="element-name">historicalBlvt</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/util/LinkedHashMap.html" title="java.util中的类或接口" class="external-link">LinkedHashMap</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&nbsp;parameters)</span></div>
<div class="block">The BLVT NAV system is based on Binance Futures, so the endpoint is based on fapi
 <br><br>
 GET /v1/lvtKlines
 <br>
 https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Historical-BLVT-NAV-Kline-Candlestick</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>parameters</code> - LinkedHashedMap of String,Object pair
            where String is the name of the parameter and Object is the value of the parameter
 <br><br>
 symbol -- mandatory/string -- the trading pair <br>
 interval -- mandatory/enum -- interval <br>
 startTime -- optional/long -- Start Time <br>
 endTime -- optional/long -- End Time <br>
 limit -- optional/long -- default 500, max 1000 <br></dd>
<dt>返回:</dt>
<dd>String</dd>
<dt>另请参阅:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Historical-BLVT-NAV-Kline-Candlestick">
     https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Historical-BLVT-NAV-Kline-Candlestick</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="indexInfo(java.util.LinkedHashMap)">
<h3>indexInfo</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></span>&nbsp;<span class="element-name">indexInfo</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/util/LinkedHashMap.html" title="java.util中的类或接口" class="external-link">LinkedHashMap</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&nbsp;parameters)</span></div>
<div class="block">GET /v1/indexInfo
 <br>
 https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Composite-Index-Symbol-Information</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>parameters</code> - LinkedHashedMap of String,Object pair
            where String is the name of the parameter and Object is the value of the parameter
 <br><br>
 symbol -- optional/string -- the trading pair <br></dd>
<dt>返回:</dt>
<dd>String</dd>
<dt>另请参阅:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Composite-Index-Symbol-Information">
     https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Composite-Index-Symbol-Information</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="assetIndex(java.util.LinkedHashMap)">
<h3>assetIndex</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></span>&nbsp;<span class="element-name">assetIndex</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/util/LinkedHashMap.html" title="java.util中的类或接口" class="external-link">LinkedHashMap</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&nbsp;parameters)</span></div>
<div class="block">asset index for Multi-Assets mode
 <br><br>
 GET /v1/assetIndex
 <br>
 https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Multi-Assets-Mode-Asset-Index</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>parameters</code> - LinkedHashedMap of String,Object pair
            where String is the name of the parameter and Object is the value of the parameter
 <br><br>
 symbol -- optional/string -- the trading pair <br></dd>
<dt>返回:</dt>
<dd>String</dd>
<dt>另请参阅:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Multi-Assets-Mode-Asset-Index">
     https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Multi-Assets-Mode-Asset-Index</a></li>
</ul>
</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &#169; 2025. All rights reserved.</small></p>
</footer>
</div>
</div>
</body>
</html>
