package com.crypto.trading.common.dto.market;

import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

/**
 * TickerDTO单元测试类
 */
class TickerDTOTest {

    /**
     * 测试默认构造函数
     */
    @Test
    void testDefaultConstructor() {
        TickerDTO dto = new TickerDTO();
        assertNotNull(dto);
    }

    /**
     * 测试带参数构造函数
     */
    @Test
    void testParameterizedConstructor() {
        String symbol = "BTCUSDT";
        BigDecimal priceChange = new BigDecimal("1000.00");
        BigDecimal priceChangePercent = new BigDecimal("2.00");
        BigDecimal weightedAvgPrice = new BigDecimal("50500.00");
        BigDecimal prevClosePrice = new BigDecimal("49000.00");
        BigDecimal lastPrice = new BigDecimal("50000.00");
        BigDecimal lastQty = new BigDecimal("0.5");
        BigDecimal bidPrice = new BigDecimal("49900.00");
        BigDecimal bidQty = new BigDecimal("1.2");
        BigDecimal askPrice = new BigDecimal("50100.00");
        BigDecimal askQty = new BigDecimal("0.8");
        BigDecimal openPrice = new BigDecimal("49000.00");
        BigDecimal highPrice = new BigDecimal("51000.00");
        BigDecimal lowPrice = new BigDecimal("48500.00");
        BigDecimal volume = new BigDecimal("100.0");
        BigDecimal quoteVolume = new BigDecimal("5050000.00");
        LocalDateTime openTime = LocalDateTime.now().minusHours(24);
        LocalDateTime closeTime = LocalDateTime.now();
        Long firstId = 123456L;
        Long lastId = 789012L;
        Long count = 5000L;

        TickerDTO dto = new TickerDTO(
                symbol, priceChange, priceChangePercent, weightedAvgPrice, prevClosePrice,
                lastPrice, lastQty, bidPrice, bidQty, askPrice, askQty, openPrice,
                highPrice, lowPrice, volume, quoteVolume, openTime, closeTime,
                firstId, lastId, count
        );

        assertNotNull(dto);
        assertEquals(symbol, dto.getSymbol());
        assertEquals(priceChange, dto.getPriceChange());
        assertEquals(priceChangePercent, dto.getPriceChangePercent());
        assertEquals(weightedAvgPrice, dto.getWeightedAvgPrice());
        assertEquals(prevClosePrice, dto.getPrevClosePrice());
        assertEquals(lastPrice, dto.getLastPrice());
        assertEquals(lastQty, dto.getLastQty());
        assertEquals(bidPrice, dto.getBidPrice());
        assertEquals(bidQty, dto.getBidQty());
        assertEquals(askPrice, dto.getAskPrice());
        assertEquals(askQty, dto.getAskQty());
        assertEquals(openPrice, dto.getOpenPrice());
        assertEquals(highPrice, dto.getHighPrice());
        assertEquals(lowPrice, dto.getLowPrice());
        assertEquals(volume, dto.getVolume());
        assertEquals(quoteVolume, dto.getQuoteVolume());
        assertEquals(openTime, dto.getOpenTime());
        assertEquals(closeTime, dto.getCloseTime());
        assertEquals(firstId, dto.getFirstId());
        assertEquals(lastId, dto.getLastId());
        assertEquals(count, dto.getCount());
    }

    /**
     * 测试getter和setter方法（部分关键字段）
     */
    @Test
    void testGettersAndSetters() {
        TickerDTO dto = new TickerDTO();

        String symbol = "ETHUSDT";
        dto.setSymbol(symbol);
        assertEquals(symbol, dto.getSymbol());

        BigDecimal priceChange = new BigDecimal("100.00");
        dto.setPriceChange(priceChange);
        assertEquals(priceChange, dto.getPriceChange());

        BigDecimal priceChangePercent = new BigDecimal("3.33");
        dto.setPriceChangePercent(priceChangePercent);
        assertEquals(priceChangePercent, dto.getPriceChangePercent());

        BigDecimal lastPrice = new BigDecimal("3100.00");
        dto.setLastPrice(lastPrice);
        assertEquals(lastPrice, dto.getLastPrice());

        BigDecimal highPrice = new BigDecimal("3200.00");
        dto.setHighPrice(highPrice);
        assertEquals(highPrice, dto.getHighPrice());

        BigDecimal lowPrice = new BigDecimal("3000.00");
        dto.setLowPrice(lowPrice);
        assertEquals(lowPrice, dto.getLowPrice());

        BigDecimal volume = new BigDecimal("5000.0");
        dto.setVolume(volume);
        assertEquals(volume, dto.getVolume());

        Long count = 10000L;
        dto.setCount(count);
        assertEquals(count, dto.getCount());
    }

    /**
     * 测试toString方法
     */
    @Test
    void testToString() {
        TickerDTO dto = new TickerDTO();
        dto.setSymbol("BTCUSDT");
        dto.setLastPrice(new BigDecimal("50000.00"));
        dto.setPriceChangePercent(new BigDecimal("2.00"));
        dto.setVolume(new BigDecimal("100.0"));

        String toString = dto.toString();
        assertNotNull(toString);
        assertTrue(toString.contains("BTCUSDT"));
        assertTrue(toString.contains("50000.00"));
        assertTrue(toString.contains("2.00"));
        assertTrue(toString.contains("100.0"));
    }
} 