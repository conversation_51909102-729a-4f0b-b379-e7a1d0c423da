{"text": "2025-06-20 23:33:04.482 | INFO | infrastructure.logging.logging_core_manager:initialize:261 | Logging system initialized successfully\n", "record": {"elapsed": {"repr": "0:00:29.007138", "seconds": 29.007138}, "exception": null, "extra": {}, "file": {"name": "logging_core_manager.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\logging\\logging_core_manager.py"}, "function": "initialize", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 261, "message": "Logging system initialized successfully", "module": "logging_core_manager", "name": "infrastructure.logging.logging_core_manager", "process": {"id": 68144, "name": "MainProcess"}, "thread": {"id": 53444, "name": "MainThread"}, "time": {"repr": "2025-06-20 23:33:04.482646+08:00", "timestamp": 1750433584.482646}}}
{"text": "2025-06-20 23:50:41.603 | INFO | infrastructure.logging.logging_core_manager:initialize:261 | Logging system initialized successfully\n", "record": {"elapsed": {"repr": "0:00:06.114461", "seconds": 6.114461}, "exception": null, "extra": {}, "file": {"name": "logging_core_manager.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\logging\\logging_core_manager.py"}, "function": "initialize", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 261, "message": "Logging system initialized successfully", "module": "logging_core_manager", "name": "infrastructure.logging.logging_core_manager", "process": {"id": 60328, "name": "MainProcess"}, "thread": {"id": 10572, "name": "MainThread"}, "time": {"repr": "2025-06-20 23:50:41.603136+08:00", "timestamp": 1750434641.603136}}}
{"text": "2025-06-20 23:59:52.339 | INFO | infrastructure.logging.logging_core_manager:initialize:261 | Logging system initialized successfully\n", "record": {"elapsed": {"repr": "0:00:09.913726", "seconds": 9.913726}, "exception": null, "extra": {}, "file": {"name": "logging_core_manager.py", "path": "D:\\1_deep_bian\\crypto-trading-system\\crypto_ml_strategy\\src\\infrastructure\\logging\\logging_core_manager.py"}, "function": "initialize", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 261, "message": "Logging system initialized successfully", "module": "logging_core_manager", "name": "infrastructure.logging.logging_core_manager", "process": {"id": 16044, "name": "MainProcess"}, "thread": {"id": 64756, "name": "MainThread"}, "time": {"repr": "2025-06-20 23:59:52.339557+08:00", "timestamp": 1750435192.339557}}}
