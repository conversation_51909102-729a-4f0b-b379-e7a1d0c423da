package com.crypto.trading.trade.service.trade.impl;

import com.crypto.trading.common.constant.OrderEnums;
import com.crypto.trading.common.dto.trade.OrderDTO;
import com.crypto.trading.common.dto.trade.OrderRequestDTO;
import com.crypto.trading.common.dto.trade.OrderResponseDTO;
import com.crypto.trading.trade.model.entity.order.OrderEntity;
import com.crypto.trading.trade.service.trade.OrderConverterService;
import com.crypto.trading.trade.util.OrderConverter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.UUID;

/**
 * 订单转换服务实现类
 * <p>
 * 提供订单相关DTO和实体之间的转换功能
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
public class OrderConverterServiceImpl implements OrderConverterService {

    private static final Logger log = LoggerFactory.getLogger(OrderConverterServiceImpl.class);

    @Override
    public OrderEntity convertToEntity(OrderRequestDTO requestDTO) {
        if (requestDTO == null) {
            return null;
        }

        log.debug("将OrderRequestDTO转换为OrderEntity: {}", requestDTO);

        // 生成客户端订单ID（如果未提供）
        String clientOrderId = requestDTO.getClientOrderId();
        if (clientOrderId == null || clientOrderId.isEmpty()) {
            clientOrderId = "CID" + UUID.randomUUID().toString().replaceAll("-", "").substring(0, 16);
        }

        OrderEntity.Builder builder = new OrderEntity.Builder()
                .clientOrderId(clientOrderId)
                .symbol(requestDTO.getSymbol())
                .side(requestDTO.getSide())
                .positionSide(requestDTO.getPositionSide() != null ? requestDTO.getPositionSide() : "BOTH")
                .type(requestDTO.getType())
                .status(OrderEnums.OrderStatus.NEW.name())
                .strategyId(requestDTO.getStrategy());

        // 处理数值类型转换
        if (requestDTO.getQuantity() != null) {
            builder.quantity(requestDTO.getQuantity().doubleValue());
        }

        if (requestDTO.getPrice() != null) {
            builder.price(requestDTO.getPrice().doubleValue());
        }

        // 设置创建时间和更新时间
        long currentTime = System.currentTimeMillis();
        builder.createdTime(currentTime)
               .updatedTime(currentTime);

        return builder.build();
    }

    @Override
    public OrderDTO convertToDTO(OrderEntity entity) {
        return OrderConverter.toOrderDTO(entity);
    }

    @Override
    public OrderResponseDTO convertToResponseDTO(OrderEntity entity) {
        log.debug("将OrderEntity转换为OrderResponseDTO: {}", entity);
        // 使用OrderConverter工具类进行转换，避免重复代码
        return OrderConverter.toOrderResponseDTO(entity);
    }

    @Override
    public OrderResponseDTO convertToResponseDTO(OrderDTO dto) {
        if (dto == null) {
            return null;
        }

        log.debug("将OrderDTO转换为OrderResponseDTO: {}", dto);

        OrderResponseDTO responseDTO = new OrderResponseDTO();
        responseDTO.setOrderId(dto.getOrderId());
        responseDTO.setClientOrderId(dto.getClientOrderId());
        responseDTO.setSymbol(dto.getSymbol());
        responseDTO.setSide(dto.getSide());
        responseDTO.setPositionSide(dto.getPositionSide());
        responseDTO.setType(dto.getType());
        responseDTO.setQuantity(dto.getQuantity());
        responseDTO.setPrice(dto.getPrice());
        responseDTO.setExecutedQuantity(dto.getExecutedQuantity());
        responseDTO.setExecutedPrice(dto.getExecutedPrice());
        responseDTO.setStatus(dto.getStatus());
        responseDTO.setStrategy(dto.getStrategyId());
        responseDTO.setCreatedTime(dto.getCreatedTime());
        responseDTO.setUpdatedTime(dto.getUpdatedTime());
        responseDTO.setExecutedTime(dto.getExecutedTime());
        
        return responseDTO;
    }
}