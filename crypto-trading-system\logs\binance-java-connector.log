2025-06-21 02:51:21.206[1750445481206] | INFO  | main       | c.c.t.bootstrap.CryptoApplication    - 正在启动虚拟货币量化交易系统...
2025-06-21 02:51:21.210[1750445481210] | INFO  | main       | c.c.t.bootstrap.CryptoApplication    - JDK版本: 21.0.2
2025-06-21 02:51:21.215[1750445481215] | INFO  | main       | c.c.t.bootstrap.CryptoApplication    - JDK供应商: Oracle Corporation
2025-06-21 02:51:21.215[1750445481215] | INFO  | main       | c.c.t.bootstrap.CryptoApplication    - JDK路径: C:\Users\<USER>\.jdks\openjdk-21.0.2
2025-06-21 02:51:23.527[1750445483527] | INFO  | main       | c.c.t.bootstrap.CryptoApplication    - Starting CryptoApplication using Java 21.0.2 with PID 44136 (D:\1_deep_bian\crypto-trading-system\crypto-bootstrap\target\crypto-bootstrap-1.0.0-SNAPSHOT.jar started by 10438 in D:\1_deep_bian\crypto-trading-system)
2025-06-21 02:51:23.531[1750445483531] | INFO  | main       | c.c.t.bootstrap.CryptoApplication    - The following 1 profile is active: "dev"
2025-06-21 02:51:25.008[1750445485008] | INFO  | main       | c.c.t.c.c.YamlPropertySourceFactory  - 加载YAML配置文件: application-common.yml
2025-06-21 02:51:29.585[1750445489585] | WARN  | main       | o.m.s.mapper.ClassPathMapperScanner  - No MyBatis mapper was found in '[com.crypto.trading.bootstrap]' package. Please check your configuration.
2025-06-21 02:51:32.582[1750445492582] | INFO  | main       | o.s.b.w.e.tomcat.TomcatWebServer     - Tomcat initialized with port 9527 (http)
2025-06-21 02:51:32.602[1750445492602] | INFO  | main       | o.a.coyote.http11.Http11NioProtocol  - Initializing ProtocolHandler ["http-nio-9527"]
2025-06-21 02:51:32.610[1750445492610] | INFO  | main       | o.a.catalina.core.StandardService    - Starting service [Tomcat]
2025-06-21 02:51:32.612[1750445492612] | INFO  | main       | o.a.catalina.core.StandardEngine     - Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-06-21 02:51:32.752[1750445492752] | INFO  | main       | o.a.c.c.C.[.[localhost].[/api]       - Initializing Spring embedded WebApplicationContext
2025-06-21 02:51:32.756[1750445492756] | INFO  | main       | o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 8925 ms
2025-06-21 02:51:33.400[1750445493400] | INFO  | main       | c.c.t.b.discovery.ServiceRegistry    - 注册新服务: http://localhost:9530
2025-06-21 02:51:33.550[1750445493550] | WARN  | main       | c.c.t.b.discovery.ServiceRegistry    - 服务 http://localhost:9530 健康检查异常: I/O error on GET request for "http://localhost:9530/status": Connection refused: connect
2025-06-21 02:51:33.550[1750445493550] | WARN  | main       | c.c.t.b.discovery.ServiceRegistry    - 服务 http://localhost:9530 初始健康检查失败
2025-06-21 02:51:33.550[1750445493550] | INFO  | main       | c.c.t.b.discovery.ServiceRegistry    - 服务注册表初始化完成，已添加 1 个服务
2025-06-21 02:51:33.577[1750445493577] | INFO  | main       | c.c.t.bootstrap.config.JacksonConfig - 创建全局ObjectMapper实例
2025-06-21 02:51:34.309[1750445494309] | INFO  | main       | c.c.t.b.client.StrategyServiceClient - 策略服务客户端初始化完成，连接超时: 5000ms, 读取超时: 30000ms
2025-06-21 02:51:34.326[1750445494326] | INFO  | main       | c.c.t.c.c.AbstractThreadPoolConfig   - 创建虚拟线程任务执行器 - 模块: bootstrap
2025-06-21 02:51:34.373[1750445494373] | INFO  | main       | c.c.t.b.client.StrategyServiceClient - 策略服务客户端虚拟线程执行器已初始化
2025-06-21 02:51:34.514[1750445494514] | INFO  | main       | c.c.t.b.config.ApiRateLimiter        - API速率限制器已初始化
2025-06-21 02:51:34.591[1750445494591] | INFO  | main       | c.c.t.sdk.limiter.BinanceRateLimiter - 初始化币安API限流器，默认配置：最大请求数=1200, 时间窗口=60000ms
2025-06-21 02:51:34.604[1750445494604] | INFO  | main       | c.c.t.b.config.BootstrapConfig       - 加载启动模块配置...
2025-06-21 02:51:34.605[1750445494605] | INFO  | main       | c.c.t.b.config.BootstrapConfig       - 配置文件已整合至bootstrap模块
2025-06-21 02:51:34.605[1750445494605] | INFO  | main       | c.c.t.b.config.BootstrapConfig       - 已排除common模块中的冲突配置类
2025-06-21 02:51:34.605[1750445494605] | INFO  | main       | c.c.t.b.config.BootstrapConfig       - 已添加SDK配置类适配器
2025-06-21 02:51:34.657[1750445494657] | INFO  | main       | c.c.t.b.config.BinanceClientConfig   - 注册USDT保证金期货API客户端Bean(umFuturesApiClient)
2025-06-21 02:51:34.668[1750445494668] | WARN  | main       | c.c.t.s.c.UMFuturesApiClientImpl     - 无法设置自定义OkHttpClient，将使用SDK默认客户端: httpClient
2025-06-21 02:51:34.668[1750445494668] | INFO  | main       | c.c.t.s.c.UMFuturesApiClientImpl     - U本位保证金期货API客户端初始化完成，baseUrl: https://testnet.binancefuture.com, useTestnet: true
2025-06-21 02:51:34.692[1750445494692] | INFO  | main       | c.c.t.b.c.a.InfluxDBConfigAdapter    - 创建InfluxDBClient适配器: url=http://localhost:8086, org=crypto, bucket=market_data
2025-06-21 02:51:34.729[1750445494729] | INFO  | main       | c.c.t.b.c.a.InfluxDBConfigAdapter    - 使用Token认证模式连接InfluxDB
2025-06-21 02:51:34.925[1750445494925] | INFO  | main       | c.c.t.b.c.a.MarketDataConfigBridge   - 创建Market模块MarketDataConfig桥接对象
2025-06-21 02:51:34.950[1750445494950] | INFO  | main       | c.c.t.c.c.AbstractThreadPoolConfig   - 创建虚拟线程执行器 - 模块: market-data
2025-06-21 02:51:34.974[1750445494974] | INFO  | main       | c.c.t.m.r.InfluxDBRepository         - 初始化InfluxDBRepository...
2025-06-21 02:51:35.239[1750445495239] | INFO  | main       | c.c.t.m.r.InfluxDBRepository         - InfluxDB写入API创建成功
2025-06-21 02:51:35.266[1750445495266] | INFO  | main       | c.c.t.m.r.InfluxDBRepository         - InfluxDB查询API创建成功
2025-06-21 02:51:35.267[1750445495267] | INFO  | main       | c.c.t.m.r.InfluxDBRepository         - InfluxDBRepository初始化完成，批处理大小：1000，刷新间隔：5000ms
2025-06-21 02:51:35.316[1750445495316] | INFO  | main       | c.c.t.m.c.KafkaProducerConfig$$SpringCGLIB$$0 - 创建字符串序列化的Kafka生产者工厂: bootstrapServers=localhost:29092, batchSize=16384, lingerMs=50, compressionType=lz4
2025-06-21 02:51:35.423[1750445495423] | INFO  | main       | c.c.t.m.c.KafkaProducerConfig$$SpringCGLIB$$0 - 创建JSON序列化的Kafka生产者工厂: bootstrapServers=localhost:29092
2025-06-21 02:51:35.432[1750445495432] | INFO  | main       | c.c.t.m.c.KafkaProducerConfig$$SpringCGLIB$$0 - 创建Avro序列化的Kafka生产者工厂: bootstrapServers=localhost:29092, schemaRegistryUrl=http://localhost:8081
2025-06-21 02:51:35.440[1750445495440] | INFO  | main       | c.c.t.m.p.KafkaMessageProducer       - 初始化Kafka批处理发送模式: batchSize=100, batchWaitMs=1000ms
2025-06-21 02:51:35.474[1750445495474] | INFO  | main       | c.c.t.m.s.i.r.ResumableDownloadEngine - 创建断点续传目录: checkpoint
2025-06-21 02:51:35.475[1750445495475] | INFO  | main       | c.c.t.m.s.i.r.ResumableDownloadEngine - 创建下载状态目录: checkpoint\download_state
2025-06-21 02:51:35.511[1750445495511] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 注册模块初始化器: common, 优先级: 1
2025-06-21 02:51:35.512[1750445495512] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 注册模块初始化器: market-data, 优先级: 20
2025-06-21 02:51:35.512[1750445495512] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 注册模块初始化器: sdk, 优先级: 10
2025-06-21 02:51:35.517[1750445495517] | INFO  | main       | c.c.t.b.lifecycle.ShutdownHook       - 已注册JVM关闭钩子
2025-06-21 02:51:35.527[1750445495527] | INFO  | main       | c.c.t.c.c.ConfigAutoConfiguration    - 配置自动装配完成
2025-06-21 02:51:35.527[1750445495527] | INFO  | main       | c.c.t.c.c.ConfigAutoConfiguration    - 已导入common模块配置类: DatabaseConfig, KafkaConfig, LoggingConfig
2025-06-21 02:51:35.553[1750445495553] | WARN  | main       | c.c.t.s.c.CMFuturesApiClientImpl     - 无法设置自定义OkHttpClient，将使用SDK默认客户端: httpClient
2025-06-21 02:51:35.554[1750445495554] | INFO  | main       | c.c.t.s.c.CMFuturesApiClientImpl     - 币本位保证金期货API客户端初始化完成，baseUrl: https://testnet.binancefuture.com, useTestnet: true
2025-06-21 02:51:35.561[1750445495561] | WARN  | main       | c.c.t.s.c.UMFuturesApiClientImpl     - 无法设置自定义OkHttpClient，将使用SDK默认客户端: httpClient
2025-06-21 02:51:35.562[1750445495562] | INFO  | main       | c.c.t.s.c.UMFuturesApiClientImpl     - U本位保证金期货API客户端初始化完成，baseUrl: https://testnet.binancefuture.com, useTestnet: true
2025-06-21 02:51:35.699[1750445495699] | INFO  | main       | c.c.t.s.w.BinanceWebSocketClientImpl - 初始化WebSocket客户端: useTestnet=true
2025-06-21 02:51:35.699[1750445495699] | INFO  | main       | c.c.t.s.w.BinanceWebSocketClientImpl - 使用环境枚举选择的WebSocket URL: wss://stream.binancefuture.com
2025-06-21 02:51:35.706[1750445495706] | INFO  | main       | c.c.t.s.w.BinanceWebSocketClientImpl - WebSocket客户端初始化完成，使用URL: wss://stream.binancefuture.com
2025-06-21 02:51:35.880[1750445495880] | INFO  | main       | com.zaxxer.hikari.HikariDataSource   - HikariPool-1 - Starting...
2025-06-21 02:51:36.933[1750445496933] | INFO  | main       | com.zaxxer.hikari.pool.HikariPool    - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@51b41740
2025-06-21 02:51:36.935[1750445496935] | INFO  | main       | com.zaxxer.hikari.HikariDataSource   - HikariPool-1 - Start completed.
2025-06-21 02:51:37.041[1750445497041] | INFO  | main       | c.c.t.b.c.a.MarketDataConfigAdapter  - 适配器加载交易对列表: [BTCUSDT, ETHUSDT, BNBUSDT]
2025-06-21 02:51:37.099[1750445497099] | INFO  | main       | c.c.t.b.c.a.MarketDataConfigAdapter  - 适配器加载K线间隔列表: [1m, 5m, 15m, 30m, 1h, 4h, 1d]
2025-06-21 02:51:37.249[1750445497249] | INFO  | main       | c.c.t.m.w.WebSocketHealthChecker     - WebSocket健康检查器已启动，检查间隔: 60000毫秒
2025-06-21 02:51:37.256[1750445497256] | INFO  | main       | c.c.t.m.manager.WebSocketManager     - WebSocket管理器虚拟线程执行器已初始化
2025-06-21 02:51:37.256[1750445497256] | INFO  | main       | c.c.t.m.manager.WebSocketManager     - 开始启动所有WebSocket监听器...
2025-06-21 02:51:37.276[1750445497276] | INFO  | websocket-manager-1 | c.c.t.m.listener.DepthDataListener   - 开始订阅深度数据...
2025-06-21 02:51:37.292[1750445497292] | INFO  | websocket-manager-1 | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅深度数据: symbol=btcusdt, levels=10, speed=1000
2025-06-21 02:51:37.416[1750445497416] | INFO  | websocket-manager-2 | c.c.t.m.listener.KlineDataListener   - 开始订阅K线数据...
2025-06-21 02:51:37.416[1750445497416] | INFO  | websocket-manager-3 | c.c.t.m.listener.TradeDataListener   - 开始订阅交易数据...
2025-06-21 02:51:37.417[1750445497417] | INFO  | websocket-manager-2 | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=btcusdt, interval=1m
2025-06-21 02:51:37.417[1750445497417] | INFO  | websocket-manager-3 | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅聚合交易数据: symbol=btcusdt
2025-06-21 02:51:37.434[1750445497434] | INFO  | websocket-manager-1 | c.b.c.f.c.utils.WebSocketConnection  - [Connection 3] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:37.434[1750445497434] | INFO  | websocket-manager-2 | c.b.c.f.c.utils.WebSocketConnection  - [Connection 1] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:37.434[1750445497434] | INFO  | websocket-manager-3 | c.b.c.f.c.utils.WebSocketConnection  - [Connection 2] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:37.469[1750445497469] | INFO  | websocket-manager-3 | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅聚合交易数据: symbol=btcusdt, connectionId=2
2025-06-21 02:51:37.469[1750445497469] | INFO  | websocket-manager-3 | c.c.t.m.listener.TradeDataListener   - 已订阅交易数据: symbol=BTCUSDT, connectionId=2
2025-06-21 02:51:37.469[1750445497469] | INFO  | websocket-manager-3 | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅聚合交易数据: symbol=ethusdt
2025-06-21 02:51:37.469[1750445497469] | INFO  | websocket-manager-3 | c.b.c.f.c.utils.WebSocketConnection  - [Connection 4] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:37.471[1750445497471] | INFO  | websocket-manager-2 | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=btcusdt, interval=1m, connectionId=1
2025-06-21 02:51:37.472[1750445497472] | INFO  | websocket-manager-2 | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=BTCUSDT, interval=1m, connectionId=1
2025-06-21 02:51:37.472[1750445497472] | INFO  | websocket-manager-2 | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=btcusdt, interval=5m
2025-06-21 02:51:37.473[1750445497473] | INFO  | websocket-manager-2 | c.b.c.f.c.utils.WebSocketConnection  - [Connection 5] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:37.472[1750445497472] | INFO  | websocket-manager-1 | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅深度数据: symbol=btcusdt, levels=10, speed=1000, connectionId=3
2025-06-21 02:51:37.473[1750445497473] | INFO  | websocket-manager-1 | c.c.t.m.listener.DepthDataListener   - 已订阅深度数据: symbol=BTCUSDT, levels=10, speed=1000, connectionId=3
2025-06-21 02:51:37.473[1750445497473] | INFO  | websocket-manager-1 | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅深度数据: symbol=ethusdt, levels=10, speed=1000
2025-06-21 02:51:37.473[1750445497473] | INFO  | websocket-manager-3 | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅聚合交易数据: symbol=ethusdt, connectionId=4
2025-06-21 02:51:37.473[1750445497473] | INFO  | websocket-manager-3 | c.c.t.m.listener.TradeDataListener   - 已订阅交易数据: symbol=ETHUSDT, connectionId=4
2025-06-21 02:51:37.473[1750445497473] | INFO  | websocket-manager-3 | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅聚合交易数据: symbol=bnbusdt
2025-06-21 02:51:37.473[1750445497473] | INFO  | websocket-manager-1 | c.b.c.f.c.utils.WebSocketConnection  - [Connection 6] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:37.473[1750445497473] | INFO  | websocket-manager-2 | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=btcusdt, interval=5m, connectionId=5
2025-06-21 02:51:37.473[1750445497473] | INFO  | websocket-manager-2 | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=BTCUSDT, interval=5m, connectionId=5
2025-06-21 02:51:37.474[1750445497474] | INFO  | websocket-manager-2 | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=btcusdt, interval=15m
2025-06-21 02:51:37.474[1750445497474] | INFO  | websocket-manager-1 | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅深度数据: symbol=ethusdt, levels=10, speed=1000, connectionId=6
2025-06-21 02:51:37.474[1750445497474] | INFO  | websocket-manager-1 | c.c.t.m.listener.DepthDataListener   - 已订阅深度数据: symbol=ETHUSDT, levels=10, speed=1000, connectionId=6
2025-06-21 02:51:37.474[1750445497474] | INFO  | websocket-manager-1 | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅深度数据: symbol=bnbusdt, levels=10, speed=1000
2025-06-21 02:51:37.474[1750445497474] | INFO  | websocket-manager-1 | c.b.c.f.c.utils.WebSocketConnection  - [Connection 9] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:37.474[1750445497474] | INFO  | websocket-manager-3 | c.b.c.f.c.utils.WebSocketConnection  - [Connection 7] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:37.474[1750445497474] | INFO  | websocket-manager-2 | c.b.c.f.c.utils.WebSocketConnection  - [Connection 8] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:37.475[1750445497475] | INFO  | websocket-manager-3 | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅聚合交易数据: symbol=bnbusdt, connectionId=7
2025-06-21 02:51:37.475[1750445497475] | INFO  | websocket-manager-1 | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅深度数据: symbol=bnbusdt, levels=10, speed=1000, connectionId=9
2025-06-21 02:51:37.475[1750445497475] | INFO  | websocket-manager-1 | c.c.t.m.listener.DepthDataListener   - 已订阅深度数据: symbol=BNBUSDT, levels=10, speed=1000, connectionId=9
2025-06-21 02:51:37.475[1750445497475] | INFO  | websocket-manager-3 | c.c.t.m.listener.TradeDataListener   - 已订阅交易数据: symbol=BNBUSDT, connectionId=7
2025-06-21 02:51:37.475[1750445497475] | INFO  | websocket-manager-2 | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=btcusdt, interval=15m, connectionId=8
2025-06-21 02:51:37.475[1750445497475] | INFO  | websocket-manager-2 | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=BTCUSDT, interval=15m, connectionId=8
2025-06-21 02:51:37.476[1750445497476] | INFO  | websocket-manager-2 | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=btcusdt, interval=30m
2025-06-21 02:51:37.476[1750445497476] | INFO  | websocket-manager-2 | c.b.c.f.c.utils.WebSocketConnection  - [Connection 10] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:37.476[1750445497476] | INFO  | websocket-manager-2 | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=btcusdt, interval=30m, connectionId=10
2025-06-21 02:51:37.476[1750445497476] | INFO  | websocket-manager-2 | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=BTCUSDT, interval=30m, connectionId=10
2025-06-21 02:51:37.477[1750445497477] | INFO  | websocket-manager-2 | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=btcusdt, interval=1h
2025-06-21 02:51:37.477[1750445497477] | INFO  | websocket-manager-2 | c.b.c.f.c.utils.WebSocketConnection  - [Connection 11] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:37.480[1750445497480] | INFO  | websocket-manager-2 | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=btcusdt, interval=1h, connectionId=11
2025-06-21 02:51:37.480[1750445497480] | INFO  | websocket-manager-2 | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=BTCUSDT, interval=1h, connectionId=11
2025-06-21 02:51:37.480[1750445497480] | INFO  | websocket-manager-2 | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=btcusdt, interval=4h
2025-06-21 02:51:37.481[1750445497481] | INFO  | websocket-manager-2 | c.b.c.f.c.utils.WebSocketConnection  - [Connection 12] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:37.482[1750445497482] | INFO  | websocket-manager-2 | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=btcusdt, interval=4h, connectionId=12
2025-06-21 02:51:37.484[1750445497484] | INFO  | websocket-manager-2 | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=BTCUSDT, interval=4h, connectionId=12
2025-06-21 02:51:37.484[1750445497484] | INFO  | websocket-manager-2 | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=btcusdt, interval=1d
2025-06-21 02:51:37.484[1750445497484] | INFO  | websocket-manager-2 | c.b.c.f.c.utils.WebSocketConnection  - [Connection 13] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:37.485[1750445497485] | INFO  | websocket-manager-2 | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=btcusdt, interval=1d, connectionId=13
2025-06-21 02:51:37.485[1750445497485] | INFO  | websocket-manager-2 | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=BTCUSDT, interval=1d, connectionId=13
2025-06-21 02:51:37.485[1750445497485] | INFO  | websocket-manager-2 | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=ethusdt, interval=1m
2025-06-21 02:51:37.485[1750445497485] | INFO  | websocket-manager-2 | c.b.c.f.c.utils.WebSocketConnection  - [Connection 14] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:37.485[1750445497485] | INFO  | websocket-manager-2 | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=ethusdt, interval=1m, connectionId=14
2025-06-21 02:51:37.485[1750445497485] | INFO  | websocket-manager-2 | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=ETHUSDT, interval=1m, connectionId=14
2025-06-21 02:51:37.485[1750445497485] | INFO  | websocket-manager-2 | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=ethusdt, interval=5m
2025-06-21 02:51:37.485[1750445497485] | INFO  | websocket-manager-2 | c.b.c.f.c.utils.WebSocketConnection  - [Connection 15] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:37.486[1750445497486] | INFO  | websocket-manager-2 | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=ethusdt, interval=5m, connectionId=15
2025-06-21 02:51:37.487[1750445497487] | INFO  | websocket-manager-2 | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=ETHUSDT, interval=5m, connectionId=15
2025-06-21 02:51:37.487[1750445497487] | INFO  | websocket-manager-2 | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=ethusdt, interval=15m
2025-06-21 02:51:37.487[1750445497487] | INFO  | websocket-manager-2 | c.b.c.f.c.utils.WebSocketConnection  - [Connection 16] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:37.488[1750445497488] | INFO  | websocket-manager-2 | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=ethusdt, interval=15m, connectionId=16
2025-06-21 02:51:37.488[1750445497488] | INFO  | websocket-manager-2 | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=ETHUSDT, interval=15m, connectionId=16
2025-06-21 02:51:37.488[1750445497488] | INFO  | websocket-manager-2 | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=ethusdt, interval=30m
2025-06-21 02:51:37.489[1750445497489] | INFO  | websocket-manager-2 | c.b.c.f.c.utils.WebSocketConnection  - [Connection 17] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:37.493[1750445497493] | INFO  | websocket-manager-2 | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=ethusdt, interval=30m, connectionId=17
2025-06-21 02:51:37.493[1750445497493] | INFO  | websocket-manager-2 | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=ETHUSDT, interval=30m, connectionId=17
2025-06-21 02:51:37.493[1750445497493] | INFO  | websocket-manager-2 | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=ethusdt, interval=1h
2025-06-21 02:51:37.493[1750445497493] | INFO  | websocket-manager-2 | c.b.c.f.c.utils.WebSocketConnection  - [Connection 18] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:37.494[1750445497494] | INFO  | websocket-manager-2 | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=ethusdt, interval=1h, connectionId=18
2025-06-21 02:51:37.499[1750445497499] | INFO  | websocket-manager-2 | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=ETHUSDT, interval=1h, connectionId=18
2025-06-21 02:51:37.499[1750445497499] | INFO  | websocket-manager-2 | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=ethusdt, interval=4h
2025-06-21 02:51:37.500[1750445497500] | INFO  | websocket-manager-2 | c.b.c.f.c.utils.WebSocketConnection  - [Connection 19] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:37.501[1750445497501] | INFO  | websocket-manager-2 | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=ethusdt, interval=4h, connectionId=19
2025-06-21 02:51:37.501[1750445497501] | INFO  | websocket-manager-2 | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=ETHUSDT, interval=4h, connectionId=19
2025-06-21 02:51:37.501[1750445497501] | INFO  | websocket-manager-2 | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=ethusdt, interval=1d
2025-06-21 02:51:37.501[1750445497501] | INFO  | websocket-manager-2 | c.b.c.f.c.utils.WebSocketConnection  - [Connection 20] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:37.501[1750445497501] | INFO  | websocket-manager-2 | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=ethusdt, interval=1d, connectionId=20
2025-06-21 02:51:37.501[1750445497501] | INFO  | websocket-manager-2 | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=ETHUSDT, interval=1d, connectionId=20
2025-06-21 02:51:37.501[1750445497501] | INFO  | websocket-manager-2 | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=bnbusdt, interval=1m
2025-06-21 02:51:37.539[1750445497539] | INFO  | websocket-manager-2 | c.b.c.f.c.utils.WebSocketConnection  - [Connection 21] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:37.542[1750445497542] | INFO  | websocket-manager-2 | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=bnbusdt, interval=1m, connectionId=21
2025-06-21 02:51:37.542[1750445497542] | INFO  | websocket-manager-2 | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=BNBUSDT, interval=1m, connectionId=21
2025-06-21 02:51:37.542[1750445497542] | INFO  | websocket-manager-2 | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=bnbusdt, interval=5m
2025-06-21 02:51:37.542[1750445497542] | INFO  | websocket-manager-2 | c.b.c.f.c.utils.WebSocketConnection  - [Connection 22] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:37.543[1750445497543] | INFO  | websocket-manager-2 | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=bnbusdt, interval=5m, connectionId=22
2025-06-21 02:51:37.543[1750445497543] | INFO  | websocket-manager-2 | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=BNBUSDT, interval=5m, connectionId=22
2025-06-21 02:51:37.543[1750445497543] | INFO  | websocket-manager-2 | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=bnbusdt, interval=15m
2025-06-21 02:51:37.544[1750445497544] | INFO  | websocket-manager-2 | c.b.c.f.c.utils.WebSocketConnection  - [Connection 23] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:37.544[1750445497544] | INFO  | websocket-manager-2 | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=bnbusdt, interval=15m, connectionId=23
2025-06-21 02:51:37.544[1750445497544] | INFO  | websocket-manager-2 | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=BNBUSDT, interval=15m, connectionId=23
2025-06-21 02:51:37.544[1750445497544] | INFO  | websocket-manager-2 | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=bnbusdt, interval=30m
2025-06-21 02:51:37.545[1750445497545] | INFO  | websocket-manager-2 | c.b.c.f.c.utils.WebSocketConnection  - [Connection 24] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:37.546[1750445497546] | INFO  | websocket-manager-2 | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=bnbusdt, interval=30m, connectionId=24
2025-06-21 02:51:37.546[1750445497546] | INFO  | websocket-manager-2 | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=BNBUSDT, interval=30m, connectionId=24
2025-06-21 02:51:37.546[1750445497546] | INFO  | websocket-manager-2 | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=bnbusdt, interval=1h
2025-06-21 02:51:37.547[1750445497547] | INFO  | websocket-manager-2 | c.b.c.f.c.utils.WebSocketConnection  - [Connection 25] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:37.548[1750445497548] | INFO  | websocket-manager-2 | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=bnbusdt, interval=1h, connectionId=25
2025-06-21 02:51:37.548[1750445497548] | INFO  | websocket-manager-2 | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=BNBUSDT, interval=1h, connectionId=25
2025-06-21 02:51:37.548[1750445497548] | INFO  | websocket-manager-2 | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=bnbusdt, interval=4h
2025-06-21 02:51:37.549[1750445497549] | INFO  | websocket-manager-2 | c.b.c.f.c.utils.WebSocketConnection  - [Connection 26] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:37.550[1750445497550] | INFO  | websocket-manager-2 | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=bnbusdt, interval=4h, connectionId=26
2025-06-21 02:51:37.550[1750445497550] | INFO  | websocket-manager-2 | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=BNBUSDT, interval=4h, connectionId=26
2025-06-21 02:51:37.550[1750445497550] | INFO  | websocket-manager-2 | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=bnbusdt, interval=1d
2025-06-21 02:51:37.551[1750445497551] | INFO  | websocket-manager-2 | c.b.c.f.c.utils.WebSocketConnection  - [Connection 27] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:37.551[1750445497551] | INFO  | websocket-manager-2 | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=bnbusdt, interval=1d, connectionId=27
2025-06-21 02:51:37.551[1750445497551] | INFO  | websocket-manager-2 | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=BNBUSDT, interval=1d, connectionId=27
2025-06-21 02:51:37.551[1750445497551] | INFO  | main       | c.c.t.m.manager.WebSocketManager     - 所有WebSocket监听器已启动，耗时: 295毫秒
2025-06-21 02:51:37.560[1750445497560] | INFO  | main       | c.c.t.m.s.impl.MarketDataServiceImpl - 启动市场数据服务...
2025-06-21 02:51:37.560[1750445497560] | INFO  |            | c.c.t.m.s.impl.MarketDataServiceImpl - 启动K线数据监听器...
2025-06-21 02:51:37.560[1750445497560] | INFO  |            | c.c.t.m.listener.KlineDataListener   - 开始订阅K线数据...
2025-06-21 02:51:37.562[1750445497562] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=btcusdt, interval=1m
2025-06-21 02:51:37.564[1750445497564] | INFO  |            | c.b.c.f.c.utils.WebSocketConnection  - [Connection 28] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:37.565[1750445497565] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=btcusdt, interval=1m, connectionId=28
2025-06-21 02:51:37.566[1750445497566] | INFO  |            | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=BTCUSDT, interval=1m, connectionId=28
2025-06-21 02:51:37.566[1750445497566] | INFO  |            | c.c.t.m.s.impl.MarketDataServiceImpl - 启动深度数据监听器...
2025-06-21 02:51:37.566[1750445497566] | INFO  | main       | c.c.t.m.manager.WebSocketManager     - WebSocket监听器已经在运行中
2025-06-21 02:51:37.566[1750445497566] | INFO  |            | c.c.t.m.listener.DepthDataListener   - 开始订阅深度数据...
2025-06-21 02:51:37.566[1750445497566] | INFO  | main       | c.c.t.m.s.impl.MarketDataServiceImpl - 市场数据服务启动成功
2025-06-21 02:51:37.566[1750445497566] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅深度数据: symbol=btcusdt, levels=10, speed=1000
2025-06-21 02:51:37.566[1750445497566] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=btcusdt, interval=5m
2025-06-21 02:51:37.566[1750445497566] | INFO  |            | c.b.c.f.c.utils.WebSocketConnection  - [Connection 29] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:37.566[1750445497566] | INFO  |            | c.c.t.m.s.impl.MarketDataServiceImpl - 启动交易数据监听器...
2025-06-21 02:51:37.566[1750445497566] | INFO  |            | c.b.c.f.c.utils.WebSocketConnection  - [Connection 30] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:37.567[1750445497567] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=btcusdt, interval=5m, connectionId=29
2025-06-21 02:51:37.567[1750445497567] | INFO  |            | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=BTCUSDT, interval=5m, connectionId=29
2025-06-21 02:51:37.567[1750445497567] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=btcusdt, interval=15m
2025-06-21 02:51:37.567[1750445497567] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅深度数据: symbol=btcusdt, levels=10, speed=1000, connectionId=30
2025-06-21 02:51:37.567[1750445497567] | INFO  |            | c.c.t.m.listener.DepthDataListener   - 已订阅深度数据: symbol=BTCUSDT, levels=10, speed=1000, connectionId=30
2025-06-21 02:51:37.567[1750445497567] | INFO  |            | c.b.c.f.c.utils.WebSocketConnection  - [Connection 31] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:37.567[1750445497567] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅深度数据: symbol=ethusdt, levels=10, speed=1000
2025-06-21 02:51:37.567[1750445497567] | INFO  |            | c.c.t.m.listener.TradeDataListener   - 开始订阅交易数据...
2025-06-21 02:51:37.567[1750445497567] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅聚合交易数据: symbol=btcusdt
2025-06-21 02:51:37.567[1750445497567] | INFO  |            | c.b.c.f.c.utils.WebSocketConnection  - [Connection 32] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:37.567[1750445497567] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=btcusdt, interval=15m, connectionId=31
2025-06-21 02:51:37.567[1750445497567] | INFO  |            | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=BTCUSDT, interval=15m, connectionId=31
2025-06-21 02:51:37.567[1750445497567] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=btcusdt, interval=30m
2025-06-21 02:51:37.567[1750445497567] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅聚合交易数据: symbol=btcusdt, connectionId=32
2025-06-21 02:51:37.567[1750445497567] | INFO  |            | c.b.c.f.c.utils.WebSocketConnection  - [Connection 33] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:37.569[1750445497569] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=btcusdt, interval=30m, connectionId=33
2025-06-21 02:51:37.569[1750445497569] | INFO  |            | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=BTCUSDT, interval=30m, connectionId=33
2025-06-21 02:51:37.569[1750445497569] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=btcusdt, interval=1h
2025-06-21 02:51:37.569[1750445497569] | INFO  |            | c.b.c.f.c.utils.WebSocketConnection  - [Connection 34] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:37.568[1750445497568] | INFO  |            | c.c.t.m.listener.TradeDataListener   - 已订阅交易数据: symbol=BTCUSDT, connectionId=32
2025-06-21 02:51:37.569[1750445497569] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅聚合交易数据: symbol=ethusdt
2025-06-21 02:51:37.569[1750445497569] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=btcusdt, interval=1h, connectionId=34
2025-06-21 02:51:37.570[1750445497570] | INFO  |            | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=BTCUSDT, interval=1h, connectionId=34
2025-06-21 02:51:37.570[1750445497570] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=btcusdt, interval=4h
2025-06-21 02:51:37.571[1750445497571] | INFO  |            | c.b.c.f.c.utils.WebSocketConnection  - [Connection 37] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:37.572[1750445497572] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=btcusdt, interval=4h, connectionId=37
2025-06-21 02:51:37.572[1750445497572] | INFO  |            | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=BTCUSDT, interval=4h, connectionId=37
2025-06-21 02:51:37.572[1750445497572] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=btcusdt, interval=1d
2025-06-21 02:51:37.569[1750445497569] | INFO  |            | c.b.c.f.c.utils.WebSocketConnection  - [Connection 36] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:37.572[1750445497572] | INFO  |            | c.b.c.f.c.utils.WebSocketConnection  - [Connection 38] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:37.569[1750445497569] | INFO  |            | c.b.c.f.c.utils.WebSocketConnection  - [Connection 35] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:37.575[1750445497575] | INFO  | main       | c.c.t.m.s.RetentionPolicyService     - 检查并创建所需的存储桶...
2025-06-21 02:51:37.575[1750445497575] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅聚合交易数据: symbol=ethusdt, connectionId=36
2025-06-21 02:51:37.576[1750445497576] | INFO  |            | c.c.t.m.listener.TradeDataListener   - 已订阅交易数据: symbol=ETHUSDT, connectionId=36
2025-06-21 02:51:37.576[1750445497576] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅聚合交易数据: symbol=bnbusdt
2025-06-21 02:51:37.576[1750445497576] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅深度数据: symbol=ethusdt, levels=10, speed=1000, connectionId=35
2025-06-21 02:51:37.576[1750445497576] | INFO  |            | c.c.t.m.listener.DepthDataListener   - 已订阅深度数据: symbol=ETHUSDT, levels=10, speed=1000, connectionId=35
2025-06-21 02:51:37.576[1750445497576] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅深度数据: symbol=bnbusdt, levels=10, speed=1000
2025-06-21 02:51:37.576[1750445497576] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=btcusdt, interval=1d, connectionId=38
2025-06-21 02:51:37.576[1750445497576] | INFO  |            | c.b.c.f.c.utils.WebSocketConnection  - [Connection 39] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:37.576[1750445497576] | INFO  |            | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=BTCUSDT, interval=1d, connectionId=38
2025-06-21 02:51:37.576[1750445497576] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=ethusdt, interval=1m
2025-06-21 02:51:37.576[1750445497576] | INFO  |            | c.b.c.f.c.utils.WebSocketConnection  - [Connection 40] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:37.576[1750445497576] | INFO  |            | c.b.c.f.c.utils.WebSocketConnection  - [Connection 41] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:37.577[1750445497577] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅深度数据: symbol=bnbusdt, levels=10, speed=1000, connectionId=40
2025-06-21 02:51:37.577[1750445497577] | INFO  |            | c.c.t.m.listener.DepthDataListener   - 已订阅深度数据: symbol=BNBUSDT, levels=10, speed=1000, connectionId=40
2025-06-21 02:51:37.577[1750445497577] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=ethusdt, interval=1m, connectionId=41
2025-06-21 02:51:37.577[1750445497577] | INFO  |            | c.c.t.m.s.impl.MarketDataServiceImpl - 深度数据监听器启动成功
2025-06-21 02:51:37.577[1750445497577] | INFO  |            | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=ETHUSDT, interval=1m, connectionId=41
2025-06-21 02:51:37.577[1750445497577] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=ethusdt, interval=5m
2025-06-21 02:51:37.577[1750445497577] | INFO  |            | c.b.c.f.c.utils.WebSocketConnection  - [Connection 42] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:37.580[1750445497580] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅聚合交易数据: symbol=bnbusdt, connectionId=39
2025-06-21 02:51:37.580[1750445497580] | INFO  |            | c.c.t.m.listener.TradeDataListener   - 已订阅交易数据: symbol=BNBUSDT, connectionId=39
2025-06-21 02:51:37.581[1750445497581] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=ethusdt, interval=5m, connectionId=42
2025-06-21 02:51:37.582[1750445497582] | INFO  |            | c.c.t.m.s.impl.MarketDataServiceImpl - 交易数据监听器启动成功
2025-06-21 02:51:37.582[1750445497582] | INFO  |            | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=ETHUSDT, interval=5m, connectionId=42
2025-06-21 02:51:37.582[1750445497582] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=ethusdt, interval=15m
2025-06-21 02:51:37.582[1750445497582] | INFO  |            | c.b.c.f.c.utils.WebSocketConnection  - [Connection 43] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:37.582[1750445497582] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=ethusdt, interval=15m, connectionId=43
2025-06-21 02:51:37.582[1750445497582] | INFO  |            | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=ETHUSDT, interval=15m, connectionId=43
2025-06-21 02:51:37.582[1750445497582] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=ethusdt, interval=30m
2025-06-21 02:51:37.584[1750445497584] | INFO  |            | c.b.c.f.c.utils.WebSocketConnection  - [Connection 44] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:37.584[1750445497584] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=ethusdt, interval=30m, connectionId=44
2025-06-21 02:51:37.584[1750445497584] | INFO  |            | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=ETHUSDT, interval=30m, connectionId=44
2025-06-21 02:51:37.584[1750445497584] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=ethusdt, interval=1h
2025-06-21 02:51:37.584[1750445497584] | INFO  |            | c.b.c.f.c.utils.WebSocketConnection  - [Connection 45] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:37.584[1750445497584] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=ethusdt, interval=1h, connectionId=45
2025-06-21 02:51:37.584[1750445497584] | INFO  |            | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=ETHUSDT, interval=1h, connectionId=45
2025-06-21 02:51:37.584[1750445497584] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=ethusdt, interval=4h
2025-06-21 02:51:37.584[1750445497584] | INFO  |            | c.b.c.f.c.utils.WebSocketConnection  - [Connection 46] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:37.585[1750445497585] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=ethusdt, interval=4h, connectionId=46
2025-06-21 02:51:37.585[1750445497585] | INFO  |            | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=ETHUSDT, interval=4h, connectionId=46
2025-06-21 02:51:37.585[1750445497585] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=ethusdt, interval=1d
2025-06-21 02:51:37.585[1750445497585] | INFO  |            | c.b.c.f.c.utils.WebSocketConnection  - [Connection 47] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:37.585[1750445497585] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=ethusdt, interval=1d, connectionId=47
2025-06-21 02:51:37.585[1750445497585] | INFO  |            | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=ETHUSDT, interval=1d, connectionId=47
2025-06-21 02:51:37.585[1750445497585] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=bnbusdt, interval=1m
2025-06-21 02:51:37.585[1750445497585] | INFO  |            | c.b.c.f.c.utils.WebSocketConnection  - [Connection 48] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:37.585[1750445497585] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=bnbusdt, interval=1m, connectionId=48
2025-06-21 02:51:37.585[1750445497585] | INFO  |            | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=BNBUSDT, interval=1m, connectionId=48
2025-06-21 02:51:37.585[1750445497585] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=bnbusdt, interval=5m
2025-06-21 02:51:37.585[1750445497585] | INFO  |            | c.b.c.f.c.utils.WebSocketConnection  - [Connection 49] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:37.586[1750445497586] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=bnbusdt, interval=5m, connectionId=49
2025-06-21 02:51:37.586[1750445497586] | INFO  |            | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=BNBUSDT, interval=5m, connectionId=49
2025-06-21 02:51:37.586[1750445497586] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=bnbusdt, interval=15m
2025-06-21 02:51:37.587[1750445497587] | INFO  |            | c.b.c.f.c.utils.WebSocketConnection  - [Connection 50] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:37.588[1750445497588] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=bnbusdt, interval=15m, connectionId=50
2025-06-21 02:51:37.588[1750445497588] | INFO  |            | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=BNBUSDT, interval=15m, connectionId=50
2025-06-21 02:51:37.588[1750445497588] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=bnbusdt, interval=30m
2025-06-21 02:51:37.589[1750445497589] | INFO  |            | c.b.c.f.c.utils.WebSocketConnection  - [Connection 51] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:37.589[1750445497589] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=bnbusdt, interval=30m, connectionId=51
2025-06-21 02:51:37.589[1750445497589] | INFO  |            | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=BNBUSDT, interval=30m, connectionId=51
2025-06-21 02:51:37.589[1750445497589] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=bnbusdt, interval=1h
2025-06-21 02:51:37.590[1750445497590] | INFO  |            | c.b.c.f.c.utils.WebSocketConnection  - [Connection 52] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:37.590[1750445497590] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=bnbusdt, interval=1h, connectionId=52
2025-06-21 02:51:37.590[1750445497590] | INFO  |            | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=BNBUSDT, interval=1h, connectionId=52
2025-06-21 02:51:37.590[1750445497590] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=bnbusdt, interval=4h
2025-06-21 02:51:37.590[1750445497590] | INFO  |            | c.b.c.f.c.utils.WebSocketConnection  - [Connection 53] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:37.590[1750445497590] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=bnbusdt, interval=4h, connectionId=53
2025-06-21 02:51:37.590[1750445497590] | INFO  |            | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=BNBUSDT, interval=4h, connectionId=53
2025-06-21 02:51:37.590[1750445497590] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=bnbusdt, interval=1d
2025-06-21 02:51:37.591[1750445497591] | INFO  |            | c.b.c.f.c.utils.WebSocketConnection  - [Connection 54] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:37.591[1750445497591] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=bnbusdt, interval=1d, connectionId=54
2025-06-21 02:51:37.591[1750445497591] | INFO  |            | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=BNBUSDT, interval=1d, connectionId=54
2025-06-21 02:51:37.591[1750445497591] | INFO  |            | c.c.t.m.s.impl.MarketDataServiceImpl - K线数据监听器启动成功
2025-06-21 02:51:37.867[1750445497867] | INFO  | main       | c.c.t.m.s.RetentionPolicyService     - 存储桶已存在: market_data_medium, ID: 9853c33d3c50ea50
2025-06-21 02:51:37.871[1750445497871] | INFO  | main       | c.c.t.m.s.RetentionPolicyService     - 存储桶已存在: market_data_low, ID: 54893dbb9fca501c
2025-06-21 02:51:37.875[1750445497875] | INFO  | main       | c.c.t.m.s.RetentionPolicyService     - 存储桶已存在: kline_data, ID: 580f9bdc0bc1a058
2025-06-21 02:51:37.880[1750445497880] | INFO  | main       | c.c.t.m.s.RetentionPolicyService     - 存储桶已存在: trade_data, ID: 28f15e25305ff329
2025-06-21 02:51:38.242[1750445498242] | INFO  | main       | c.c.t.m.s.RetentionPolicyService     - 存储桶已存在: depth_data, ID: 7fb30439e71bbc5d
2025-06-21 02:51:38.242[1750445498242] | INFO  | main       | c.c.t.m.s.RetentionPolicyService     - 更新主数据桶 market_data 的保留策略...
2025-06-21 02:51:38.253[1750445498253] | INFO  | OkHttp https://stream.binancefuture.com/... | c.b.c.f.c.utils.WebSocketConnection  - [Connection 7] Connected to Server
2025-06-21 02:51:38.256[1750445498256] | INFO  | OkHttp https://stream.binancefuture.com/... | c.b.c.f.c.utils.WebSocketConnection  - [Connection 11] Connected to Server
2025-06-21 02:51:38.256[1750445498256] | INFO  | OkHttp https://stream.binancefuture.com/... | c.b.c.f.c.utils.WebSocketConnection  - [Connection 6] Connected to Server
2025-06-21 02:51:38.256[1750445498256] | INFO  | OkHttp https://stream.binancefuture.com/... | c.b.c.f.c.utils.WebSocketConnection  - [Connection 13] Connected to Server
2025-06-21 02:51:38.256[1750445498256] | INFO  | OkHttp https://stream.binancefuture.com/... | c.b.c.f.c.utils.WebSocketConnection  - [Connection 44] Connected to Server
2025-06-21 02:51:38.256[1750445498256] | INFO  | OkHttp https://stream.binancefuture.com/... | c.b.c.f.c.utils.WebSocketConnection  - [Connection 5] Connected to Server
2025-06-21 02:51:38.256[1750445498256] | INFO  | OkHttp https://stream.binancefuture.com/... | c.b.c.f.c.utils.WebSocketConnection  - [Connection 29] Connected to Server
2025-06-21 02:51:38.256[1750445498256] | INFO  | OkHttp https://stream.binancefuture.com/... | c.b.c.f.c.utils.WebSocketConnection  - [Connection 46] Connected to Server
2025-06-21 02:51:38.256[1750445498256] | INFO  | OkHttp https://stream.binancefuture.com/... | c.b.c.f.c.utils.WebSocketConnection  - [Connection 4] Connected to Server
2025-06-21 02:51:38.256[1750445498256] | INFO  | OkHttp https://stream.binancefuture.com/... | c.b.c.f.c.utils.WebSocketConnection  - [Connection 17] Connected to Server
2025-06-21 02:51:38.256[1750445498256] | INFO  | OkHttp https://stream.binancefuture.com/... | c.b.c.f.c.utils.WebSocketConnection  - [Connection 19] Connected to Server
2025-06-21 02:51:38.256[1750445498256] | INFO  | OkHttp https://stream.binancefuture.com/... | c.b.c.f.c.utils.WebSocketConnection  - [Connection 18] Connected to Server
2025-06-21 02:51:38.256[1750445498256] | INFO  | OkHttp https://stream.binancefuture.com/... | c.b.c.f.c.utils.WebSocketConnection  - [Connection 35] Connected to Server
2025-06-21 02:51:38.256[1750445498256] | INFO  | OkHttp https://stream.binancefuture.com/... | c.b.c.f.c.utils.WebSocketConnection  - [Connection 37] Connected to Server
2025-06-21 02:51:38.265[1750445498265] | INFO  | OkHttp https://stream.binancefuture.com/... | c.b.c.f.c.utils.WebSocketConnection  - [Connection 50] Connected to Server
2025-06-21 02:51:38.266[1750445498266] | INFO  | OkHttp https://stream.binancefuture.com/... | c.b.c.f.c.utils.WebSocketConnection  - [Connection 26] Connected to Server
2025-06-21 02:51:38.272[1750445498272] | INFO  | OkHttp https://stream.binancefuture.com/... | c.b.c.f.c.utils.WebSocketConnection  - [Connection 9] Connected to Server
2025-06-21 02:51:38.266[1750445498266] | INFO  | OkHttp https://stream.binancefuture.com/... | c.b.c.f.c.utils.WebSocketConnection  - [Connection 23] Connected to Server
2025-06-21 02:51:38.273[1750445498273] | INFO  | OkHttp https://stream.binancefuture.com/... | c.b.c.f.c.utils.WebSocketConnection  - [Connection 3] Connected to Server
2025-06-21 02:51:38.274[1750445498274] | INFO  | OkHttp https://stream.binancefuture.com/... | c.b.c.f.c.utils.WebSocketConnection  - [Connection 27] Connected to Server
2025-06-21 02:51:38.272[1750445498272] | INFO  | OkHttp https://stream.binancefuture.com/... | c.b.c.f.c.utils.WebSocketConnection  - [Connection 12] Connected to Server
2025-06-21 02:51:38.274[1750445498274] | INFO  | OkHttp https://stream.binancefuture.com/... | c.b.c.f.c.utils.WebSocketConnection  - [Connection 20] Connected to Server
2025-06-21 02:51:38.277[1750445498277] | INFO  | OkHttp https://stream.binancefuture.com/... | c.b.c.f.c.utils.WebSocketConnection  - [Connection 47] Connected to Server
2025-06-21 02:51:38.284[1750445498284] | INFO  | OkHttp https://stream.binancefuture.com/... | c.b.c.f.c.utils.WebSocketConnection  - [Connection 41] Connected to Server
2025-06-21 02:51:38.286[1750445498286] | INFO  | OkHttp https://stream.binancefuture.com/... | c.b.c.f.c.utils.WebSocketConnection  - [Connection 42] Connected to Server
2025-06-21 02:51:38.286[1750445498286] | INFO  | OkHttp https://stream.binancefuture.com/... | c.b.c.f.c.utils.WebSocketConnection  - [Connection 52] Connected to Server
2025-06-21 02:51:38.286[1750445498286] | INFO  | OkHttp https://stream.binancefuture.com/... | c.b.c.f.c.utils.WebSocketConnection  - [Connection 10] Connected to Server
2025-06-21 02:51:38.287[1750445498287] | INFO  | OkHttp https://stream.binancefuture.com/... | c.b.c.f.c.utils.WebSocketConnection  - [Connection 16] Connected to Server
2025-06-21 02:51:38.287[1750445498287] | INFO  | OkHttp https://stream.binancefuture.com/... | c.b.c.f.c.utils.WebSocketConnection  - [Connection 40] Connected to Server
2025-06-21 02:51:38.287[1750445498287] | INFO  | OkHttp https://stream.binancefuture.com/... | c.b.c.f.c.utils.WebSocketConnection  - [Connection 33] Connected to Server
2025-06-21 02:51:38.296[1750445498296] | INFO  | OkHttp https://stream.binancefuture.com/... | c.b.c.f.c.utils.WebSocketConnection  - [Connection 51] Connected to Server
2025-06-21 02:51:38.297[1750445498297] | ERROR | OkHttp https://stream.binancefuture.com/... | c.b.c.f.c.utils.WebSocketConnection  - [Connection 31] Failure
java.net.SocketException: Connection reset
	at java.base/sun.nio.ch.NioSocketImpl.implRead(NioSocketImpl.java:318)
	at java.base/sun.nio.ch.NioSocketImpl.read(NioSocketImpl.java:346)
	at java.base/sun.nio.ch.NioSocketImpl$1.read(NioSocketImpl.java:796)
	at java.base/java.net.Socket$SocketInputStream.read(Socket.java:1099)
	at java.base/sun.security.ssl.SSLSocketInputRecord.read(SSLSocketInputRecord.java:489)
	at java.base/sun.security.ssl.SSLSocketInputRecord.readHeader(SSLSocketInputRecord.java:483)
	at java.base/sun.security.ssl.SSLSocketInputRecord.decode(SSLSocketInputRecord.java:160)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:111)
	at java.base/sun.security.ssl.SSLSocketImpl.decode(SSLSocketImpl.java:1506)
	at java.base/sun.security.ssl.SSLSocketImpl.readHandshakeRecord(SSLSocketImpl.java:1421)
	at java.base/sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:455)
	at java.base/sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:426)
	at okhttp3.internal.connection.RealConnection.connectTls(RealConnection.kt:379)
	at okhttp3.internal.connection.RealConnection.establishProtocol(RealConnection.kt:337)
	at okhttp3.internal.connection.RealConnection.connect(RealConnection.kt:209)
	at okhttp3.internal.connection.ExchangeFinder.findConnection(ExchangeFinder.kt:226)
	at okhttp3.internal.connection.ExchangeFinder.findHealthyConnection(ExchangeFinder.kt:106)
	at okhttp3.internal.connection.ExchangeFinder.find(ExchangeFinder.kt:74)
	at okhttp3.internal.connection.RealCall.initExchange$okhttp(RealCall.kt:255)
	at okhttp3.internal.connection.ConnectInterceptor.intercept(ConnectInterceptor.kt:32)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
	at okhttp3.internal.cache.CacheInterceptor.intercept(CacheInterceptor.kt:95)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
	at okhttp3.internal.http.BridgeInterceptor.intercept(BridgeInterceptor.kt:83)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
	at okhttp3.internal.http.RetryAndFollowUpInterceptor.intercept(RetryAndFollowUpInterceptor.kt:76)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
	at okhttp3.internal.connection.RealCall.getResponseWithInterceptorChain$okhttp(RealCall.kt:201)
	at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:517)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
	Suppressed: java.net.SocketException: Connection reset
		... 32 common frames omitted
2025-06-21 02:51:38.306[1750445498306] | ERROR | OkHttp https://stream.binancefuture.com/... | c.b.c.f.c.utils.WebSocketConnection  - [Connection 25] Failure
java.net.SocketException: Connection reset
	at java.base/sun.nio.ch.NioSocketImpl.implRead(NioSocketImpl.java:318)
	at java.base/sun.nio.ch.NioSocketImpl.read(NioSocketImpl.java:346)
	at java.base/sun.nio.ch.NioSocketImpl$1.read(NioSocketImpl.java:796)
	at java.base/java.net.Socket$SocketInputStream.read(Socket.java:1099)
	at java.base/sun.security.ssl.SSLSocketInputRecord.read(SSLSocketInputRecord.java:489)
	at java.base/sun.security.ssl.SSLSocketInputRecord.readHeader(SSLSocketInputRecord.java:483)
	at java.base/sun.security.ssl.SSLSocketInputRecord.decode(SSLSocketInputRecord.java:160)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:111)
	at java.base/sun.security.ssl.SSLSocketImpl.decode(SSLSocketImpl.java:1506)
	at java.base/sun.security.ssl.SSLSocketImpl.readHandshakeRecord(SSLSocketImpl.java:1421)
	at java.base/sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:455)
	at java.base/sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:426)
	at okhttp3.internal.connection.RealConnection.connectTls(RealConnection.kt:379)
	at okhttp3.internal.connection.RealConnection.establishProtocol(RealConnection.kt:337)
	at okhttp3.internal.connection.RealConnection.connect(RealConnection.kt:209)
	at okhttp3.internal.connection.ExchangeFinder.findConnection(ExchangeFinder.kt:226)
	at okhttp3.internal.connection.ExchangeFinder.findHealthyConnection(ExchangeFinder.kt:106)
	at okhttp3.internal.connection.ExchangeFinder.find(ExchangeFinder.kt:74)
	at okhttp3.internal.connection.RealCall.initExchange$okhttp(RealCall.kt:255)
	at okhttp3.internal.connection.ConnectInterceptor.intercept(ConnectInterceptor.kt:32)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
	at okhttp3.internal.cache.CacheInterceptor.intercept(CacheInterceptor.kt:95)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
	at okhttp3.internal.http.BridgeInterceptor.intercept(BridgeInterceptor.kt:83)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
	at okhttp3.internal.http.RetryAndFollowUpInterceptor.intercept(RetryAndFollowUpInterceptor.kt:76)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
	at okhttp3.internal.connection.RealCall.getResponseWithInterceptorChain$okhttp(RealCall.kt:201)
	at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:517)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
	Suppressed: java.net.SocketException: Connection reset by peer
		at java.base/sun.nio.ch.SocketDispatcher.write0(Native Method)
		at java.base/sun.nio.ch.SocketDispatcher.write(SocketDispatcher.java:54)
		at java.base/sun.nio.ch.NioSocketImpl.tryWrite(NioSocketImpl.java:394)
		at java.base/sun.nio.ch.NioSocketImpl.implWrite(NioSocketImpl.java:410)
		at java.base/sun.nio.ch.NioSocketImpl.write(NioSocketImpl.java:440)
		at java.base/sun.nio.ch.NioSocketImpl$2.write(NioSocketImpl.java:819)
		at java.base/java.net.Socket$SocketOutputStream.write(Socket.java:1195)
		at java.base/sun.security.ssl.SSLSocketOutputRecord.encodeChangeCipherSpec(SSLSocketOutputRecord.java:231)
		at java.base/sun.security.ssl.OutputRecord.changeWriteCiphers(OutputRecord.java:187)
		at java.base/sun.security.ssl.ChangeCipherSpec$T10ChangeCipherSpecProducer.produce(ChangeCipherSpec.java:117)
		at java.base/sun.security.ssl.Finished$T12FinishedProducer.onProduceFinished(Finished.java:393)
		at java.base/sun.security.ssl.Finished$T12FinishedProducer.produce(Finished.java:377)
		at java.base/sun.security.ssl.SSLHandshake.produce(SSLHandshake.java:437)
		at java.base/sun.security.ssl.ServerHelloDone$ServerHelloDoneConsumer.consume(ServerHelloDone.java:182)
		at java.base/sun.security.ssl.SSLHandshake.consume(SSLHandshake.java:393)
		at java.base/sun.security.ssl.HandshakeContext.dispatch(HandshakeContext.java:476)
		at java.base/sun.security.ssl.HandshakeContext.dispatch(HandshakeContext.java:447)
		at java.base/sun.security.ssl.TransportContext.dispatch(TransportContext.java:201)
		at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:172)
		... 24 common frames omitted
2025-06-21 02:51:38.306[1750445498306] | ERROR | OkHttp https://stream.binancefuture.com/... | c.b.c.f.c.utils.WebSocketConnection  - [Connection 8] Failure
java.net.SocketException: Connection reset
	at java.base/sun.nio.ch.NioSocketImpl.implRead(NioSocketImpl.java:318)
	at java.base/sun.nio.ch.NioSocketImpl.read(NioSocketImpl.java:346)
	at java.base/sun.nio.ch.NioSocketImpl$1.read(NioSocketImpl.java:796)
	at java.base/java.net.Socket$SocketInputStream.read(Socket.java:1099)
	at java.base/sun.security.ssl.SSLSocketInputRecord.read(SSLSocketInputRecord.java:489)
	at java.base/sun.security.ssl.SSLSocketInputRecord.readHeader(SSLSocketInputRecord.java:483)
	at java.base/sun.security.ssl.SSLSocketInputRecord.decode(SSLSocketInputRecord.java:160)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:111)
	at java.base/sun.security.ssl.SSLSocketImpl.decode(SSLSocketImpl.java:1506)
	at java.base/sun.security.ssl.SSLSocketImpl.readHandshakeRecord(SSLSocketImpl.java:1421)
	at java.base/sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:455)
	at java.base/sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:426)
	at okhttp3.internal.connection.RealConnection.connectTls(RealConnection.kt:379)
	at okhttp3.internal.connection.RealConnection.establishProtocol(RealConnection.kt:337)
	at okhttp3.internal.connection.RealConnection.connect(RealConnection.kt:209)
	at okhttp3.internal.connection.ExchangeFinder.findConnection(ExchangeFinder.kt:226)
	at okhttp3.internal.connection.ExchangeFinder.findHealthyConnection(ExchangeFinder.kt:106)
	at okhttp3.internal.connection.ExchangeFinder.find(ExchangeFinder.kt:74)
	at okhttp3.internal.connection.RealCall.initExchange$okhttp(RealCall.kt:255)
	at okhttp3.internal.connection.ConnectInterceptor.intercept(ConnectInterceptor.kt:32)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
	at okhttp3.internal.cache.CacheInterceptor.intercept(CacheInterceptor.kt:95)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
	at okhttp3.internal.http.BridgeInterceptor.intercept(BridgeInterceptor.kt:83)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
	at okhttp3.internal.http.RetryAndFollowUpInterceptor.intercept(RetryAndFollowUpInterceptor.kt:76)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
	at okhttp3.internal.connection.RealCall.getResponseWithInterceptorChain$okhttp(RealCall.kt:201)
	at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:517)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
	Suppressed: java.net.SocketException: Connection reset
		... 32 common frames omitted
2025-06-21 02:51:38.317[1750445498317] | INFO  | OkHttp https://stream.binancefuture.com/... | c.b.c.f.c.utils.WebSocketConnection  - [Connection 39] Connected to Server
2025-06-21 02:51:38.327[1750445498327] | ERROR | OkHttp https://stream.binancefuture.com/... | c.b.c.f.c.utils.WebSocketConnection  - [Connection 36] Failure
java.net.SocketException: Connection reset
	at java.base/sun.nio.ch.NioSocketImpl.implRead(NioSocketImpl.java:318)
	at java.base/sun.nio.ch.NioSocketImpl.read(NioSocketImpl.java:346)
	at java.base/sun.nio.ch.NioSocketImpl$1.read(NioSocketImpl.java:796)
	at java.base/java.net.Socket$SocketInputStream.read(Socket.java:1099)
	at java.base/sun.security.ssl.SSLSocketInputRecord.read(SSLSocketInputRecord.java:489)
	at java.base/sun.security.ssl.SSLSocketInputRecord.readHeader(SSLSocketInputRecord.java:483)
	at java.base/sun.security.ssl.SSLSocketInputRecord.decode(SSLSocketInputRecord.java:160)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:111)
	at java.base/sun.security.ssl.SSLSocketImpl.decode(SSLSocketImpl.java:1506)
	at java.base/sun.security.ssl.SSLSocketImpl.readHandshakeRecord(SSLSocketImpl.java:1421)
	at java.base/sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:455)
	at java.base/sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:426)
	at okhttp3.internal.connection.RealConnection.connectTls(RealConnection.kt:379)
	at okhttp3.internal.connection.RealConnection.establishProtocol(RealConnection.kt:337)
	at okhttp3.internal.connection.RealConnection.connect(RealConnection.kt:209)
	at okhttp3.internal.connection.ExchangeFinder.findConnection(ExchangeFinder.kt:226)
	at okhttp3.internal.connection.ExchangeFinder.findHealthyConnection(ExchangeFinder.kt:106)
	at okhttp3.internal.connection.ExchangeFinder.find(ExchangeFinder.kt:74)
	at okhttp3.internal.connection.RealCall.initExchange$okhttp(RealCall.kt:255)
	at okhttp3.internal.connection.ConnectInterceptor.intercept(ConnectInterceptor.kt:32)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
	at okhttp3.internal.cache.CacheInterceptor.intercept(CacheInterceptor.kt:95)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
	at okhttp3.internal.http.BridgeInterceptor.intercept(BridgeInterceptor.kt:83)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
	at okhttp3.internal.http.RetryAndFollowUpInterceptor.intercept(RetryAndFollowUpInterceptor.kt:76)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
	at okhttp3.internal.connection.RealCall.getResponseWithInterceptorChain$okhttp(RealCall.kt:201)
	at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:517)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
	Suppressed: java.net.SocketException: Connection reset by peer
		at java.base/sun.nio.ch.SocketDispatcher.write0(Native Method)
		at java.base/sun.nio.ch.SocketDispatcher.write(SocketDispatcher.java:54)
		at java.base/sun.nio.ch.NioSocketImpl.tryWrite(NioSocketImpl.java:394)
		at java.base/sun.nio.ch.NioSocketImpl.implWrite(NioSocketImpl.java:410)
		at java.base/sun.nio.ch.NioSocketImpl.write(NioSocketImpl.java:440)
		at java.base/sun.nio.ch.NioSocketImpl$2.write(NioSocketImpl.java:819)
		at java.base/java.net.Socket$SocketOutputStream.write(Socket.java:1195)
		at java.base/sun.security.ssl.SSLSocketOutputRecord.encodeChangeCipherSpec(SSLSocketOutputRecord.java:231)
		at java.base/sun.security.ssl.OutputRecord.changeWriteCiphers(OutputRecord.java:187)
		at java.base/sun.security.ssl.ChangeCipherSpec$T10ChangeCipherSpecProducer.produce(ChangeCipherSpec.java:117)
		at java.base/sun.security.ssl.Finished$T12FinishedProducer.onProduceFinished(Finished.java:393)
		at java.base/sun.security.ssl.Finished$T12FinishedProducer.produce(Finished.java:377)
		at java.base/sun.security.ssl.SSLHandshake.produce(SSLHandshake.java:437)
		at java.base/sun.security.ssl.ServerHelloDone$ServerHelloDoneConsumer.consume(ServerHelloDone.java:182)
		at java.base/sun.security.ssl.SSLHandshake.consume(SSLHandshake.java:393)
		at java.base/sun.security.ssl.HandshakeContext.dispatch(HandshakeContext.java:476)
		at java.base/sun.security.ssl.HandshakeContext.dispatch(HandshakeContext.java:447)
		at java.base/sun.security.ssl.TransportContext.dispatch(TransportContext.java:201)
		at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:172)
		... 24 common frames omitted
2025-06-21 02:51:38.365[1750445498365] | ERROR | OkHttp https://stream.binancefuture.com/... | c.b.c.f.c.utils.WebSocketConnection  - [Connection 45] Failure
java.net.SocketException: Connection reset
	at java.base/sun.nio.ch.NioSocketImpl.implRead(NioSocketImpl.java:318)
	at java.base/sun.nio.ch.NioSocketImpl.read(NioSocketImpl.java:346)
	at java.base/sun.nio.ch.NioSocketImpl$1.read(NioSocketImpl.java:796)
	at java.base/java.net.Socket$SocketInputStream.read(Socket.java:1099)
	at java.base/sun.security.ssl.SSLSocketInputRecord.read(SSLSocketInputRecord.java:489)
	at java.base/sun.security.ssl.SSLSocketInputRecord.readHeader(SSLSocketInputRecord.java:483)
	at java.base/sun.security.ssl.SSLSocketInputRecord.decode(SSLSocketInputRecord.java:160)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:111)
	at java.base/sun.security.ssl.SSLSocketImpl.decode(SSLSocketImpl.java:1506)
	at java.base/sun.security.ssl.SSLSocketImpl.readHandshakeRecord(SSLSocketImpl.java:1421)
	at java.base/sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:455)
	at java.base/sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:426)
	at okhttp3.internal.connection.RealConnection.connectTls(RealConnection.kt:379)
	at okhttp3.internal.connection.RealConnection.establishProtocol(RealConnection.kt:337)
	at okhttp3.internal.connection.RealConnection.connect(RealConnection.kt:209)
	at okhttp3.internal.connection.ExchangeFinder.findConnection(ExchangeFinder.kt:226)
	at okhttp3.internal.connection.ExchangeFinder.findHealthyConnection(ExchangeFinder.kt:106)
	at okhttp3.internal.connection.ExchangeFinder.find(ExchangeFinder.kt:74)
	at okhttp3.internal.connection.RealCall.initExchange$okhttp(RealCall.kt:255)
	at okhttp3.internal.connection.ConnectInterceptor.intercept(ConnectInterceptor.kt:32)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
	at okhttp3.internal.cache.CacheInterceptor.intercept(CacheInterceptor.kt:95)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
	at okhttp3.internal.http.BridgeInterceptor.intercept(BridgeInterceptor.kt:83)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
	at okhttp3.internal.http.RetryAndFollowUpInterceptor.intercept(RetryAndFollowUpInterceptor.kt:76)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
	at okhttp3.internal.connection.RealCall.getResponseWithInterceptorChain$okhttp(RealCall.kt:201)
	at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:517)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
	Suppressed: java.net.SocketException: Connection reset
		... 32 common frames omitted
2025-06-21 02:51:38.372[1750445498372] | INFO  | OkHttp https://stream.binancefuture.com/... | c.b.c.f.c.utils.WebSocketConnection  - [Connection 53] Connected to Server
2025-06-21 02:51:38.400[1750445498400] | INFO  | OkHttp https://stream.binancefuture.com/... | c.b.c.f.c.utils.WebSocketConnection  - [Connection 21] Connected to Server
2025-06-21 02:51:38.400[1750445498400] | INFO  | OkHttp https://stream.binancefuture.com/... | c.b.c.f.c.utils.WebSocketConnection  - [Connection 14] Connected to Server
2025-06-21 02:51:38.400[1750445498400] | INFO  | OkHttp https://stream.binancefuture.com/... | c.b.c.f.c.utils.WebSocketConnection  - [Connection 49] Connected to Server
2025-06-21 02:51:38.413[1750445498413] | INFO  | OkHttp https://stream.binancefuture.com/... | c.b.c.f.c.utils.WebSocketConnection  - [Connection 15] Connected to Server
2025-06-21 02:51:38.413[1750445498413] | ERROR | OkHttp https://stream.binancefuture.com/... | c.b.c.f.c.utils.WebSocketConnection  - [Connection 43] Failure
java.net.SocketException: Connection reset
	at java.base/sun.nio.ch.NioSocketImpl.implRead(NioSocketImpl.java:318)
	at java.base/sun.nio.ch.NioSocketImpl.read(NioSocketImpl.java:346)
	at java.base/sun.nio.ch.NioSocketImpl$1.read(NioSocketImpl.java:796)
	at java.base/java.net.Socket$SocketInputStream.read(Socket.java:1099)
	at java.base/sun.security.ssl.SSLSocketInputRecord.read(SSLSocketInputRecord.java:489)
	at java.base/sun.security.ssl.SSLSocketInputRecord.readHeader(SSLSocketInputRecord.java:483)
	at java.base/sun.security.ssl.SSLSocketInputRecord.decode(SSLSocketInputRecord.java:160)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:111)
	at java.base/sun.security.ssl.SSLSocketImpl.decode(SSLSocketImpl.java:1506)
	at java.base/sun.security.ssl.SSLSocketImpl.readHandshakeRecord(SSLSocketImpl.java:1421)
	at java.base/sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:455)
	at java.base/sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:426)
	at okhttp3.internal.connection.RealConnection.connectTls(RealConnection.kt:379)
	at okhttp3.internal.connection.RealConnection.establishProtocol(RealConnection.kt:337)
	at okhttp3.internal.connection.RealConnection.connect(RealConnection.kt:209)
	at okhttp3.internal.connection.ExchangeFinder.findConnection(ExchangeFinder.kt:226)
	at okhttp3.internal.connection.ExchangeFinder.findHealthyConnection(ExchangeFinder.kt:106)
	at okhttp3.internal.connection.ExchangeFinder.find(ExchangeFinder.kt:74)
	at okhttp3.internal.connection.RealCall.initExchange$okhttp(RealCall.kt:255)
	at okhttp3.internal.connection.ConnectInterceptor.intercept(ConnectInterceptor.kt:32)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
	at okhttp3.internal.cache.CacheInterceptor.intercept(CacheInterceptor.kt:95)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
	at okhttp3.internal.http.BridgeInterceptor.intercept(BridgeInterceptor.kt:83)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
	at okhttp3.internal.http.RetryAndFollowUpInterceptor.intercept(RetryAndFollowUpInterceptor.kt:76)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
	at okhttp3.internal.connection.RealCall.getResponseWithInterceptorChain$okhttp(RealCall.kt:201)
	at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:517)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
	Suppressed: java.net.SocketException: Connection reset
		... 32 common frames omitted
2025-06-21 02:51:38.422[1750445498422] | INFO  | OkHttp https://stream.binancefuture.com/... | c.b.c.f.c.utils.WebSocketConnection  - [Connection 24] Connected to Server
2025-06-21 02:51:38.429[1750445498429] | INFO  | OkHttp https://stream.binancefuture.com/... | c.b.c.f.c.utils.WebSocketConnection  - [Connection 28] Connected to Server
2025-06-21 02:51:38.431[1750445498431] | ERROR | OkHttp https://stream.binancefuture.com/... | c.b.c.f.c.utils.WebSocketConnection  - [Connection 54] Failure
java.net.SocketException: Connection reset
	at java.base/sun.nio.ch.NioSocketImpl.implRead(NioSocketImpl.java:318)
	at java.base/sun.nio.ch.NioSocketImpl.read(NioSocketImpl.java:346)
	at java.base/sun.nio.ch.NioSocketImpl$1.read(NioSocketImpl.java:796)
	at java.base/java.net.Socket$SocketInputStream.read(Socket.java:1099)
	at java.base/sun.security.ssl.SSLSocketInputRecord.read(SSLSocketInputRecord.java:489)
	at java.base/sun.security.ssl.SSLSocketInputRecord.readHeader(SSLSocketInputRecord.java:483)
	at java.base/sun.security.ssl.SSLSocketInputRecord.decode(SSLSocketInputRecord.java:160)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:111)
	at java.base/sun.security.ssl.SSLSocketImpl.decode(SSLSocketImpl.java:1506)
	at java.base/sun.security.ssl.SSLSocketImpl.readHandshakeRecord(SSLSocketImpl.java:1421)
	at java.base/sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:455)
	at java.base/sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:426)
	at okhttp3.internal.connection.RealConnection.connectTls(RealConnection.kt:379)
	at okhttp3.internal.connection.RealConnection.establishProtocol(RealConnection.kt:337)
	at okhttp3.internal.connection.RealConnection.connect(RealConnection.kt:209)
	at okhttp3.internal.connection.ExchangeFinder.findConnection(ExchangeFinder.kt:226)
	at okhttp3.internal.connection.ExchangeFinder.findHealthyConnection(ExchangeFinder.kt:106)
	at okhttp3.internal.connection.ExchangeFinder.find(ExchangeFinder.kt:74)
	at okhttp3.internal.connection.RealCall.initExchange$okhttp(RealCall.kt:255)
	at okhttp3.internal.connection.ConnectInterceptor.intercept(ConnectInterceptor.kt:32)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
	at okhttp3.internal.cache.CacheInterceptor.intercept(CacheInterceptor.kt:95)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
	at okhttp3.internal.http.BridgeInterceptor.intercept(BridgeInterceptor.kt:83)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
	at okhttp3.internal.http.RetryAndFollowUpInterceptor.intercept(RetryAndFollowUpInterceptor.kt:76)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
	at okhttp3.internal.connection.RealCall.getResponseWithInterceptorChain$okhttp(RealCall.kt:201)
	at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:517)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
	Suppressed: java.net.SocketException: Connection reset
		... 32 common frames omitted
2025-06-21 02:51:38.452[1750445498452] | INFO  | OkHttp https://stream.binancefuture.com/... | c.b.c.f.c.utils.WebSocketConnection  - [Connection 22] Connected to Server
2025-06-21 02:51:38.456[1750445498456] | INFO  | OkHttp https://stream.binancefuture.com/... | c.b.c.f.c.utils.WebSocketConnection  - [Connection 1] Connected to Server
2025-06-21 02:51:38.489[1750445498489] | INFO  | OkHttp https://stream.binancefuture.com/... | c.b.c.f.c.utils.WebSocketConnection  - [Connection 2] Connected to Server
2025-06-21 02:51:38.491[1750445498491] | INFO  | OkHttp https://stream.binancefuture.com/... | c.b.c.f.c.utils.WebSocketConnection  - [Connection 48] Connected to Server
2025-06-21 02:51:38.494[1750445498494] | INFO  | OkHttp https://stream.binancefuture.com/... | c.b.c.f.c.utils.WebSocketConnection  - [Connection 32] Connected to Server
2025-06-21 02:51:38.502[1750445498502] | INFO  | OkHttp https://stream.binancefuture.com/... | c.b.c.f.c.utils.WebSocketConnection  - [Connection 34] Connected to Server
2025-06-21 02:51:38.503[1750445498503] | INFO  | OkHttp https://stream.binancefuture.com/... | c.b.c.f.c.utils.WebSocketConnection  - [Connection 38] Connected to Server
2025-06-21 02:51:38.604[1750445498604] | INFO  | main       | c.c.t.m.s.RetentionPolicyService     - 已更新主数据桶 market_data 的保留策略为 17520 小时
2025-06-21 02:51:38.604[1750445498604] | INFO  | main       | c.c.t.m.s.RetentionPolicyService     - 设置降采样任务...
2025-06-21 02:51:38.619[1750445498619] | INFO  | OkHttp https://stream.binancefuture.com/... | c.b.c.f.c.utils.WebSocketConnection  - [Connection 30] Connected to Server
2025-06-21 02:51:38.718[1750445498718] | INFO  | main       | c.c.t.m.s.RetentionPolicyService     - 创建降采样任务: hourly_downsample_task, 间隔: 1h, 源桶: market_data, 目标桶: market_data_medium
2025-06-21 02:51:38.730[1750445498730] | WARN  | main       | c.c.t.m.s.RetentionPolicyService     - 无法设置任务状态，可能需要更新InfluxDB客户端版本: com.influxdb.client.domain.Task.setStatus(java.lang.String)
2025-06-21 02:51:38.798[1750445498798] | INFO  | main       | c.c.t.m.s.RetentionPolicyService     - 已创建降采样任务: hourly_downsample_task
2025-06-21 02:51:38.836[1750445498836] | INFO  | main       | c.c.t.m.s.RetentionPolicyService     - 创建降采样任务: daily_downsample_task, 间隔: 1d, 源桶: market_data_medium, 目标桶: market_data_low
2025-06-21 02:51:38.837[1750445498837] | WARN  | main       | c.c.t.m.s.RetentionPolicyService     - 无法设置任务状态，可能需要更新InfluxDB客户端版本: com.influxdb.client.domain.Task.setStatus(java.lang.String)
2025-06-21 02:51:38.915[1750445498915] | INFO  | main       | c.c.t.m.s.RetentionPolicyService     - 已创建降采样任务: daily_downsample_task
2025-06-21 02:51:38.918[1750445498918] | INFO  | main       | c.c.t.b.c.a.InfluxDBConfigAdapter    - 创建InfluxDB WriteApi适配器
2025-06-21 02:51:38.935[1750445498935] | INFO  | main       | c.c.t.b.c.a.InfluxDBConfigBridge     - 提供Market模块InfluxDBConfig Bean - 直接使用已注入的实例
2025-06-21 02:51:38.935[1750445498935] | INFO  | main       | c.c.t.b.c.a.InfluxDBConfigBridge     - InfluxDB配置参数: url=http://localhost:8086, org=crypto, bucket=market_data
2025-06-21 02:51:38.946[1750445498946] | INFO  | main       | c.c.t.b.config.BinanceClientConfig   - 注册币本位保证金期货API客户端Bean(cmFuturesApiClient)
2025-06-21 02:51:38.948[1750445498948] | WARN  | main       | c.c.t.s.c.CMFuturesApiClientImpl     - 无法设置自定义OkHttpClient，将使用SDK默认客户端: httpClient
2025-06-21 02:51:38.948[1750445498948] | INFO  | main       | c.c.t.s.c.CMFuturesApiClientImpl     - 币本位保证金期货API客户端初始化完成，baseUrl: https://testnet.binancefuture.com, useTestnet: true
2025-06-21 02:51:38.997[1750445498997] | INFO  | main       | c.c.t.market.config.InfluxDBConfig   - 创建InfluxDB客户端 url=http://localhost:8086, org=crypto, bucket=market_data
2025-06-21 02:51:38.998[1750445498998] | INFO  | main       | c.c.t.market.config.InfluxDBConfig   - 使用Token认证模式连接InfluxDB
2025-06-21 02:51:39.015[1750445499015] | INFO  | main       | c.c.t.market.config.InfluxDBConfig   - InfluxDB连接测试: 成功
2025-06-21 02:51:39.021[1750445499021] | INFO  | main       | c.c.t.market.config.InfluxDBConfig   - 创建InfluxDB写入API, 批次大小: 5000, 刷新间隔: 1000ms
2025-06-21 02:51:39.119[1750445499119] | INFO  | main       | c.c.t.market.config.InfluxDBConfig   - 开始检查并创建必要的InfluxDB存储桶
2025-06-21 02:51:39.259[1750445499259] | INFO  | main       | c.c.t.market.config.InfluxDBConfig   - 获取到组织ID: 334d1c42c7cbc3a3
2025-06-21 02:51:39.348[1750445499348] | INFO  | main       | c.c.t.market.config.InfluxDBConfig   - 现有存储桶: [trade_data, market_data, market_data_lowres, _tasks, _monitoring, kline_data, market_data_medium, market_data_highres, depth_data, market_data_low, market_data_midres]
2025-06-21 02:51:39.348[1750445499348] | INFO  | main       | c.c.t.market.config.InfluxDBConfig   - 存储桶已存在: market_data
2025-06-21 02:51:39.348[1750445499348] | INFO  | main       | c.c.t.market.config.InfluxDBConfig   - 存储桶已存在: market_data_highres
2025-06-21 02:51:39.348[1750445499348] | INFO  | main       | c.c.t.market.config.InfluxDBConfig   - 存储桶已存在: market_data_midres
2025-06-21 02:51:39.348[1750445499348] | INFO  | main       | c.c.t.market.config.InfluxDBConfig   - 存储桶已存在: market_data_lowres
2025-06-21 02:51:39.348[1750445499348] | INFO  | main       | c.c.t.market.config.InfluxDBConfig   - InfluxDB存储桶检查和创建完成
2025-06-21 02:51:39.610[1750445499610] | INFO  | main       | c.c.t.c.c.AbstractThreadPoolConfig   - 创建平台线程池任务执行器 - 模块: bootstrap, 核心线程数: 20, 最大线程数: 200, 队列容量: 2000
2025-06-21 02:51:39.631[1750445499631] | INFO  | main       | c.c.t.c.c.AbstractThreadPoolConfig   - 创建平台线程池任务执行器 - 模块: market-data, 核心线程数: 12, 最大线程数: 24, 队列容量: 1000
2025-06-21 02:51:39.709[1750445499709] | INFO  | main       | c.c.t.b.config.DatabaseConfig        - 配置数据库初始化器，执行schema.sql创建表
2025-06-21 02:51:39.714[1750445499714] | INFO  | main       | c.c.t.b.config.DatabaseConfig        - 找到schema.sql文件，将用于初始化数据库表
2025-06-21 02:51:40.335[1750445500335] | INFO  |            | o.a.k.c.producer.ProducerConfig      - ProducerConfig values: 
	acks = -1
	auto.include.jmx.reporter = true
	batch.size = 16384
	bootstrap.servers = [localhost:29092]
	buffer.memory = 33554432
	client.dns.lookup = use_all_dns_ips
	client.id = crypto-trading-system-producer-1
	compression.type = lz4
	connections.max.idle.ms = 540000
	delivery.timeout.ms = 120000
	enable.idempotence = true
	interceptor.classes = []
	key.serializer = class org.apache.kafka.common.serialization.StringSerializer
	linger.ms = 50
	max.block.ms = 60000
	max.in.flight.requests.per.connection = 5
	max.request.size = 2097152
	metadata.max.age.ms = 300000
	metadata.max.idle.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partitioner.adaptive.partitioning.enable = true
	partitioner.availability.timeout.ms = 0
	partitioner.class = null
	partitioner.ignore.keys = false
	receive.buffer.bytes = 32768
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 60000
	retries = 3
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = GSSAPI
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	transaction.timeout.ms = 60000
	transactional.id = null
	value.serializer = class org.apache.kafka.common.serialization.StringSerializer

2025-06-21 02:51:40.467[1750445500467] | INFO  | main       | c.c.t.b.config.DatabaseConfig        - 验证数据库连接和表创建
2025-06-21 02:51:40.505[1750445500505] | INFO  | main       | c.c.t.b.config.DatabaseConfig        - 表 t_order 已存在
2025-06-21 02:51:40.507[1750445500507] | INFO  | main       | c.c.t.b.config.DatabaseConfig        - 表 t_trading_signal 已存在
2025-06-21 02:51:40.509[1750445500509] | INFO  | main       | c.c.t.b.config.DatabaseConfig        - 表 t_order_execution_log 已存在
2025-06-21 02:51:40.512[1750445500512] | INFO  | main       | c.c.t.b.config.DatabaseConfig        - 表 t_risk_control_log 已存在
2025-06-21 02:51:40.835[1750445500835] | INFO  |            | o.a.k.clients.producer.KafkaProducer - [Producer clientId=crypto-trading-system-producer-1] Instantiated an idempotent producer.
2025-06-21 02:51:41.787[1750445501787] | INFO  |            | o.a.k.c.producer.ProducerConfig      - These configurations '[socket.keepalive.enable]' were supplied but are not used yet.
2025-06-21 02:51:41.790[1750445501790] | INFO  |            | o.a.kafka.common.utils.AppInfoParser - Kafka version: 3.5.1
2025-06-21 02:51:41.790[1750445501790] | INFO  |            | o.a.kafka.common.utils.AppInfoParser - Kafka commitId: 2c6fb6c54472e90a
2025-06-21 02:51:41.790[1750445501790] | INFO  |            | o.a.kafka.common.utils.AppInfoParser - Kafka startTimeMs: 1750445501787
2025-06-21 02:51:45.718[1750445505718] | INFO  | kafka-producer-network-thread | crypto-trading-system-producer-1 | org.apache.kafka.clients.Metadata    - [Producer clientId=crypto-trading-system-producer-1] Cluster ID: T5S_ihwcSV2V2Io6u6QApg
2025-06-21 02:51:45.924[1750445505924] | INFO  | kafka-producer-network-thread | crypto-trading-system-producer-1 | o.a.k.c.p.i.TransactionManager       - [Producer clientId=crypto-trading-system-producer-1] ProducerId set to 10011 with epoch 0
2025-06-21 02:51:49.503[1750445509503] | INFO  | main       | o.s.b.a.e.web.EndpointLinksResolver  - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-06-21 02:51:49.873[1750445509873] | INFO  | main       | o.a.coyote.http11.Http11NioProtocol  - Starting ProtocolHandler ["http-nio-9527"]
2025-06-21 02:51:49.917[1750445509917] | INFO  | main       | o.s.b.w.e.tomcat.TomcatWebServer     - Tomcat started on port 9527 (http) with context path '/api'
2025-06-21 02:51:49.986[1750445509986] | INFO  | main       | c.c.t.bootstrap.CryptoApplication    - Started CryptoApplication in 28.349 seconds (process running for 30.754)
2025-06-21 02:51:50.016[1750445510016] | INFO  | main       | c.c.t.m.l.WebSocketStartupListener   - 应用启动完成，开始初始化WebSocket连接
2025-06-21 02:51:50.016[1750445510016] | INFO  | main       | c.c.t.m.l.WebSocketStartupListener   - 币安API配置: BinanceApiConfig{apiKey='******', secretKey='******', usdtFuturesBaseUrl='https://fapi.binance.com', coinFuturesBaseUrl='https://dapi.binance.com', usdtFuturesWsBaseUrl='wss://fstream.binance.com', coinFuturesWsBaseUrl='wss://dstream.binance.com', useTestnet=true, usdtFuturesTestnetBaseUrl='https://testnet.binancefuture.com', coinFuturesTestnetBaseUrl='https://testnet.binancefuture.com', usdtFuturesTestnetWsBaseUrl='wss://stream.binancefuture.com', coinFuturesTestnetWsBaseUrl='wss://dstream.binancefuture.com', hasProxy=false, rateLimit={weightLimit=1200, orderLimit=100}}
2025-06-21 02:51:50.038[1750445510038] | INFO  | main       | c.c.t.m.l.WebSocketStartupListener   - 使用测试网络: true
2025-06-21 02:51:50.038[1750445510038] | INFO  | main       | c.c.t.m.l.WebSocketStartupListener   - WebSocket基础URL: wss://stream.binancefuture.com
2025-06-21 02:51:50.038[1750445510038] | INFO  | main       | c.c.t.m.listener.KlineDataListener   - 开始订阅K线数据...
2025-06-21 02:51:50.039[1750445510039] | INFO  | main       | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=btcusdt, interval=1m
2025-06-21 02:51:50.040[1750445510040] | INFO  | main       | c.b.c.f.c.utils.WebSocketConnection  - [Connection 55] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:50.041[1750445510041] | INFO  | main       | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=btcusdt, interval=1m, connectionId=55
2025-06-21 02:51:50.042[1750445510042] | INFO  | main       | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=BTCUSDT, interval=1m, connectionId=55
2025-06-21 02:51:50.042[1750445510042] | INFO  | main       | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=btcusdt, interval=5m
2025-06-21 02:51:50.043[1750445510043] | INFO  | main       | c.b.c.f.c.utils.WebSocketConnection  - [Connection 56] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:50.044[1750445510044] | INFO  | main       | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=btcusdt, interval=5m, connectionId=56
2025-06-21 02:51:50.044[1750445510044] | INFO  | main       | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=BTCUSDT, interval=5m, connectionId=56
2025-06-21 02:51:50.044[1750445510044] | INFO  | main       | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=btcusdt, interval=15m
2025-06-21 02:51:50.044[1750445510044] | INFO  | main       | c.b.c.f.c.utils.WebSocketConnection  - [Connection 57] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:50.045[1750445510045] | INFO  | main       | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=btcusdt, interval=15m, connectionId=57
2025-06-21 02:51:50.045[1750445510045] | INFO  | main       | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=BTCUSDT, interval=15m, connectionId=57
2025-06-21 02:51:50.045[1750445510045] | INFO  | main       | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=btcusdt, interval=30m
2025-06-21 02:51:50.045[1750445510045] | INFO  | main       | c.b.c.f.c.utils.WebSocketConnection  - [Connection 58] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:50.045[1750445510045] | INFO  | main       | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=btcusdt, interval=30m, connectionId=58
2025-06-21 02:51:50.045[1750445510045] | INFO  | main       | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=BTCUSDT, interval=30m, connectionId=58
2025-06-21 02:51:50.045[1750445510045] | INFO  | main       | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=btcusdt, interval=1h
2025-06-21 02:51:50.046[1750445510046] | INFO  | main       | c.b.c.f.c.utils.WebSocketConnection  - [Connection 59] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:50.046[1750445510046] | INFO  | main       | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=btcusdt, interval=1h, connectionId=59
2025-06-21 02:51:50.046[1750445510046] | INFO  | main       | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=BTCUSDT, interval=1h, connectionId=59
2025-06-21 02:51:50.046[1750445510046] | INFO  | main       | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=btcusdt, interval=4h
2025-06-21 02:51:50.046[1750445510046] | INFO  | main       | c.b.c.f.c.utils.WebSocketConnection  - [Connection 60] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:50.048[1750445510048] | INFO  | main       | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=btcusdt, interval=4h, connectionId=60
2025-06-21 02:51:50.048[1750445510048] | INFO  | main       | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=BTCUSDT, interval=4h, connectionId=60
2025-06-21 02:51:50.049[1750445510049] | INFO  | main       | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=btcusdt, interval=1d
2025-06-21 02:51:50.049[1750445510049] | INFO  | main       | c.b.c.f.c.utils.WebSocketConnection  - [Connection 61] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:50.049[1750445510049] | INFO  | main       | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=btcusdt, interval=1d, connectionId=61
2025-06-21 02:51:50.049[1750445510049] | INFO  | main       | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=BTCUSDT, interval=1d, connectionId=61
2025-06-21 02:51:50.049[1750445510049] | INFO  | main       | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=ethusdt, interval=1m
2025-06-21 02:51:50.049[1750445510049] | INFO  | main       | c.b.c.f.c.utils.WebSocketConnection  - [Connection 62] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:50.049[1750445510049] | INFO  | main       | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=ethusdt, interval=1m, connectionId=62
2025-06-21 02:51:50.049[1750445510049] | INFO  | main       | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=ETHUSDT, interval=1m, connectionId=62
2025-06-21 02:51:50.049[1750445510049] | INFO  | main       | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=ethusdt, interval=5m
2025-06-21 02:51:50.049[1750445510049] | INFO  | main       | c.b.c.f.c.utils.WebSocketConnection  - [Connection 63] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:50.049[1750445510049] | INFO  | main       | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=ethusdt, interval=5m, connectionId=63
2025-06-21 02:51:50.050[1750445510050] | INFO  | main       | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=ETHUSDT, interval=5m, connectionId=63
2025-06-21 02:51:50.050[1750445510050] | INFO  | main       | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=ethusdt, interval=15m
2025-06-21 02:51:50.050[1750445510050] | INFO  | main       | c.b.c.f.c.utils.WebSocketConnection  - [Connection 64] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:50.051[1750445510051] | INFO  | main       | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=ethusdt, interval=15m, connectionId=64
2025-06-21 02:51:50.051[1750445510051] | INFO  | main       | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=ETHUSDT, interval=15m, connectionId=64
2025-06-21 02:51:50.051[1750445510051] | INFO  | main       | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=ethusdt, interval=30m
2025-06-21 02:51:50.051[1750445510051] | INFO  | main       | c.b.c.f.c.utils.WebSocketConnection  - [Connection 65] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:50.051[1750445510051] | INFO  | main       | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=ethusdt, interval=30m, connectionId=65
2025-06-21 02:51:50.051[1750445510051] | INFO  | main       | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=ETHUSDT, interval=30m, connectionId=65
2025-06-21 02:51:50.051[1750445510051] | INFO  | main       | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=ethusdt, interval=1h
2025-06-21 02:51:50.051[1750445510051] | INFO  | main       | c.b.c.f.c.utils.WebSocketConnection  - [Connection 66] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:50.098[1750445510098] | INFO  | main       | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=ethusdt, interval=1h, connectionId=66
2025-06-21 02:51:50.098[1750445510098] | INFO  | main       | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=ETHUSDT, interval=1h, connectionId=66
2025-06-21 02:51:50.098[1750445510098] | INFO  | main       | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=ethusdt, interval=4h
2025-06-21 02:51:50.104[1750445510104] | INFO  | main       | c.b.c.f.c.utils.WebSocketConnection  - [Connection 67] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:50.107[1750445510107] | INFO  | main       | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=ethusdt, interval=4h, connectionId=67
2025-06-21 02:51:50.107[1750445510107] | INFO  | main       | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=ETHUSDT, interval=4h, connectionId=67
2025-06-21 02:51:50.107[1750445510107] | INFO  | main       | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=ethusdt, interval=1d
2025-06-21 02:51:50.107[1750445510107] | INFO  | main       | c.b.c.f.c.utils.WebSocketConnection  - [Connection 68] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:50.108[1750445510108] | INFO  | main       | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=ethusdt, interval=1d, connectionId=68
2025-06-21 02:51:50.108[1750445510108] | INFO  | main       | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=ETHUSDT, interval=1d, connectionId=68
2025-06-21 02:51:50.108[1750445510108] | INFO  | main       | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=bnbusdt, interval=1m
2025-06-21 02:51:50.108[1750445510108] | INFO  | main       | c.b.c.f.c.utils.WebSocketConnection  - [Connection 69] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:50.109[1750445510109] | INFO  | main       | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=bnbusdt, interval=1m, connectionId=69
2025-06-21 02:51:50.109[1750445510109] | INFO  | main       | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=BNBUSDT, interval=1m, connectionId=69
2025-06-21 02:51:50.109[1750445510109] | INFO  | main       | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=bnbusdt, interval=5m
2025-06-21 02:51:50.109[1750445510109] | INFO  | main       | c.b.c.f.c.utils.WebSocketConnection  - [Connection 70] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:50.110[1750445510110] | INFO  | main       | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=bnbusdt, interval=5m, connectionId=70
2025-06-21 02:51:50.110[1750445510110] | INFO  | main       | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=BNBUSDT, interval=5m, connectionId=70
2025-06-21 02:51:50.110[1750445510110] | INFO  | main       | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=bnbusdt, interval=15m
2025-06-21 02:51:50.110[1750445510110] | INFO  | main       | c.b.c.f.c.utils.WebSocketConnection  - [Connection 71] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:50.110[1750445510110] | INFO  | main       | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=bnbusdt, interval=15m, connectionId=71
2025-06-21 02:51:50.112[1750445510112] | INFO  | main       | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=BNBUSDT, interval=15m, connectionId=71
2025-06-21 02:51:50.113[1750445510113] | INFO  | main       | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=bnbusdt, interval=30m
2025-06-21 02:51:50.114[1750445510114] | INFO  | main       | c.b.c.f.c.utils.WebSocketConnection  - [Connection 72] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:50.115[1750445510115] | INFO  | main       | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=bnbusdt, interval=30m, connectionId=72
2025-06-21 02:51:50.115[1750445510115] | INFO  | main       | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=BNBUSDT, interval=30m, connectionId=72
2025-06-21 02:51:50.115[1750445510115] | INFO  | main       | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=bnbusdt, interval=1h
2025-06-21 02:51:50.115[1750445510115] | INFO  | main       | c.b.c.f.c.utils.WebSocketConnection  - [Connection 73] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:50.115[1750445510115] | INFO  | main       | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=bnbusdt, interval=1h, connectionId=73
2025-06-21 02:51:50.115[1750445510115] | INFO  | main       | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=BNBUSDT, interval=1h, connectionId=73
2025-06-21 02:51:50.115[1750445510115] | INFO  | main       | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=bnbusdt, interval=4h
2025-06-21 02:51:50.115[1750445510115] | INFO  | main       | c.b.c.f.c.utils.WebSocketConnection  - [Connection 74] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:50.115[1750445510115] | INFO  | main       | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=bnbusdt, interval=4h, connectionId=74
2025-06-21 02:51:50.115[1750445510115] | INFO  | main       | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=BNBUSDT, interval=4h, connectionId=74
2025-06-21 02:51:50.115[1750445510115] | INFO  | main       | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=bnbusdt, interval=1d
2025-06-21 02:51:50.116[1750445510116] | INFO  | main       | c.b.c.f.c.utils.WebSocketConnection  - [Connection 75] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:50.116[1750445510116] | INFO  | main       | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=bnbusdt, interval=1d, connectionId=75
2025-06-21 02:51:50.116[1750445510116] | INFO  | main       | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=BNBUSDT, interval=1d, connectionId=75
2025-06-21 02:51:50.116[1750445510116] | INFO  | main       | c.c.t.m.listener.DepthDataListener   - 开始订阅深度数据...
2025-06-21 02:51:50.116[1750445510116] | INFO  | main       | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅深度数据: symbol=btcusdt, levels=10, speed=1000
2025-06-21 02:51:50.116[1750445510116] | INFO  | main       | c.b.c.f.c.utils.WebSocketConnection  - [Connection 76] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:50.116[1750445510116] | INFO  | main       | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅深度数据: symbol=btcusdt, levels=10, speed=1000, connectionId=76
2025-06-21 02:51:50.116[1750445510116] | INFO  | main       | c.c.t.m.listener.DepthDataListener   - 已订阅深度数据: symbol=BTCUSDT, levels=10, speed=1000, connectionId=76
2025-06-21 02:51:50.116[1750445510116] | INFO  | main       | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅深度数据: symbol=ethusdt, levels=10, speed=1000
2025-06-21 02:51:50.116[1750445510116] | INFO  | main       | c.b.c.f.c.utils.WebSocketConnection  - [Connection 77] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:50.118[1750445510118] | INFO  | main       | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅深度数据: symbol=ethusdt, levels=10, speed=1000, connectionId=77
2025-06-21 02:51:50.118[1750445510118] | INFO  | main       | c.c.t.m.listener.DepthDataListener   - 已订阅深度数据: symbol=ETHUSDT, levels=10, speed=1000, connectionId=77
2025-06-21 02:51:50.118[1750445510118] | INFO  | main       | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅深度数据: symbol=bnbusdt, levels=10, speed=1000
2025-06-21 02:51:50.118[1750445510118] | INFO  | main       | c.b.c.f.c.utils.WebSocketConnection  - [Connection 78] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:50.119[1750445510119] | INFO  | main       | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅深度数据: symbol=bnbusdt, levels=10, speed=1000, connectionId=78
2025-06-21 02:51:50.119[1750445510119] | INFO  | main       | c.c.t.m.listener.DepthDataListener   - 已订阅深度数据: symbol=BNBUSDT, levels=10, speed=1000, connectionId=78
2025-06-21 02:51:50.119[1750445510119] | INFO  | main       | c.c.t.m.listener.TradeDataListener   - 开始订阅交易数据...
2025-06-21 02:51:50.119[1750445510119] | INFO  | main       | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅聚合交易数据: symbol=btcusdt
2025-06-21 02:51:50.119[1750445510119] | INFO  | main       | c.b.c.f.c.utils.WebSocketConnection  - [Connection 79] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:50.119[1750445510119] | INFO  | main       | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅聚合交易数据: symbol=btcusdt, connectionId=79
2025-06-21 02:51:50.119[1750445510119] | INFO  | main       | c.c.t.m.listener.TradeDataListener   - 已订阅交易数据: symbol=BTCUSDT, connectionId=79
2025-06-21 02:51:50.119[1750445510119] | INFO  | main       | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅聚合交易数据: symbol=ethusdt
2025-06-21 02:51:50.119[1750445510119] | INFO  | main       | c.b.c.f.c.utils.WebSocketConnection  - [Connection 80] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:50.119[1750445510119] | INFO  | main       | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅聚合交易数据: symbol=ethusdt, connectionId=80
2025-06-21 02:51:50.119[1750445510119] | INFO  | main       | c.c.t.m.listener.TradeDataListener   - 已订阅交易数据: symbol=ETHUSDT, connectionId=80
2025-06-21 02:51:50.120[1750445510120] | INFO  | main       | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅聚合交易数据: symbol=bnbusdt
2025-06-21 02:51:50.120[1750445510120] | INFO  | main       | c.b.c.f.c.utils.WebSocketConnection  - [Connection 81] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:50.120[1750445510120] | INFO  | main       | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅聚合交易数据: symbol=bnbusdt, connectionId=81
2025-06-21 02:51:50.120[1750445510120] | INFO  | main       | c.c.t.m.listener.TradeDataListener   - 已订阅交易数据: symbol=BNBUSDT, connectionId=81
2025-06-21 02:51:50.120[1750445510120] | INFO  | main       | c.c.t.m.l.WebSocketStartupListener   - WebSocket连接初始化完成，当前活跃连接数: 81
2025-06-21 02:51:50.126[1750445510126] | INFO  | main       | c.c.t.b.l.ApplicationLifecycle       - 应用程序就绪事件触发，开始初始化模块...
2025-06-21 02:51:50.135[1750445510135] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 开始初始化 3 个模块，初始化顺序: common -> sdk -> market-data
2025-06-21 02:51:50.137[1750445510137] | INFO  | main       | c.c.t.b.i.AbstractModuleInitializer  - 模块[common]开始初始化...
2025-06-21 02:51:50.137[1750445510137] | INFO  | main       | c.c.t.b.i.AbstractModuleInitializer  - 模块[sdk]开始初始化...
2025-06-21 02:51:50.137[1750445510137] | INFO  | main       | c.c.t.b.i.AbstractModuleInitializer  - 模块[market-data]开始初始化...
2025-06-21 02:51:50.137[1750445510137] | INFO  | main       | c.c.t.b.i.AbstractModuleInitializer  - 模块[common]正在执行初始化...
2025-06-21 02:51:50.137[1750445510137] | INFO  | main       | c.c.t.b.i.CommonModuleInitializer    - 初始化公共模块...
2025-06-21 02:51:50.138[1750445510138] | INFO  | main       | c.c.t.b.i.CommonModuleInitializer    - 加载系统配置...
2025-06-21 02:51:50.138[1750445510138] | INFO  | main       | c.c.t.b.i.CommonModuleInitializer    - 当前激活的配置文件: dev
2025-06-21 02:51:50.138[1750445510138] | INFO  | main       | c.c.t.b.i.CommonModuleInitializer    - 初始化工具类...
2025-06-21 02:51:50.138[1750445510138] | INFO  | main       | c.c.t.b.i.CommonModuleInitializer    - 公共模块初始化完成
2025-06-21 02:51:50.138[1750445510138] | INFO  | main       | c.c.t.b.i.AbstractModuleInitializer  - 模块[common]初始化完成
2025-06-21 02:51:50.138[1750445510138] | INFO  | main       | c.c.t.b.i.AbstractModuleInitializer  - 模块[sdk]正在执行初始化...
2025-06-21 02:51:50.138[1750445510138] | INFO  | main       | c.c.t.b.i.SdkModuleInitializer       - 初始化SDK模块...
2025-06-21 02:51:50.138[1750445510138] | INFO  | main       | c.c.t.b.i.SdkModuleInitializer       - 初始化API客户端...
2025-06-21 02:51:50.138[1750445510138] | INFO  | main       | c.c.t.b.i.SdkModuleInitializer       - 初始化WebSocket客户端...
2025-06-21 02:51:50.138[1750445510138] | INFO  | main       | c.c.t.b.i.SdkModuleInitializer       - 初始化响应处理器...
2025-06-21 02:51:50.138[1750445510138] | INFO  | main       | c.c.t.b.i.SdkModuleInitializer       - SDK模块初始化完成
2025-06-21 02:51:50.138[1750445510138] | INFO  | main       | c.c.t.b.i.AbstractModuleInitializer  - 模块[sdk]初始化完成
2025-06-21 02:51:50.138[1750445510138] | INFO  | main       | c.c.t.b.i.AbstractModuleInitializer  - 模块[market-data]正在执行初始化...
2025-06-21 02:51:50.138[1750445510138] | INFO  | main       | c.c.t.b.i.MarketDataModuleInitializer - 初始化市场数据模块...
2025-06-21 02:51:50.139[1750445510139] | INFO  | main       | c.c.t.b.i.MarketDataModuleInitializer - 初始化WebSocket连接管理器...
2025-06-21 02:51:50.139[1750445510139] | INFO  | main       | c.c.t.b.i.MarketDataModuleInitializer - WebSocket连接管理器初始化完成
2025-06-21 02:51:50.139[1750445510139] | INFO  | main       | c.c.t.b.i.MarketDataModuleInitializer - 初始化数据处理器...
2025-06-21 02:51:50.144[1750445510144] | INFO  | main       | c.c.t.b.i.MarketDataModuleInitializer - 数据处理器初始化完成
2025-06-21 02:51:50.144[1750445510144] | INFO  | main       | c.c.t.b.i.MarketDataModuleInitializer - 启动市场数据监听...
2025-06-21 02:51:50.144[1750445510144] | INFO  | main       | c.c.t.m.s.impl.MarketDataServiceImpl - 启动市场数据服务...
2025-06-21 02:51:50.144[1750445510144] | INFO  | main       | c.c.t.m.manager.WebSocketManager     - WebSocket监听器已经在运行中
2025-06-21 02:51:50.144[1750445510144] | INFO  | main       | c.c.t.m.s.impl.MarketDataServiceImpl - 市场数据服务启动成功
2025-06-21 02:51:50.144[1750445510144] | INFO  | main       | c.c.t.b.i.MarketDataModuleInitializer - 市场数据监听启动完成
2025-06-21 02:51:50.144[1750445510144] | INFO  | main       | c.c.t.b.i.MarketDataModuleInitializer - 市场数据模块初始化完成
2025-06-21 02:51:50.144[1750445510144] | INFO  | main       | c.c.t.b.i.AbstractModuleInitializer  - 模块[market-data]初始化完成
2025-06-21 02:51:50.144[1750445510144] | INFO  | main       | c.c.t.b.i.AbstractModuleInitializer  - 模块[common]初始化后处理完成
2025-06-21 02:51:50.153[1750445510153] | INFO  | main       | c.c.t.b.i.AbstractModuleInitializer  - 模块[sdk]初始化后处理完成
2025-06-21 02:51:50.153[1750445510153] | INFO  | main       | c.c.t.b.i.AbstractModuleInitializer  - 模块[market-data]初始化后处理完成
2025-06-21 02:51:50.154[1750445510154] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 所有模块初始化完成
2025-06-21 02:51:50.154[1750445510154] | INFO  | main       | c.c.t.b.l.ApplicationLifecycle       - 模块初始化完成
2025-06-21 02:51:50.154[1750445510154] | INFO  | main       | c.c.t.b.l.ApplicationLifecycle       - 正在启动Python策略模块...
2025-06-21 02:51:50.155[1750445510155] | INFO  | main       | c.c.t.b.manager.StrategyManager      - 策略服务部署模式: service
2025-06-21 02:51:50.195[1750445510195] | WARN  | main       | c.c.t.b.discovery.ServiceRegistry    - 没有健康的服务可用
2025-06-21 02:51:50.208[1750445510208] | INFO  |            | c.c.t.m.s.impl.MarketDataServiceImpl - 启动K线数据监听器...
2025-06-21 02:51:50.208[1750445510208] | INFO  |            | c.c.t.m.listener.KlineDataListener   - 开始订阅K线数据...
2025-06-21 02:51:50.208[1750445510208] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=btcusdt, interval=1m
2025-06-21 02:51:50.209[1750445510209] | INFO  |            | c.b.c.f.c.utils.WebSocketConnection  - [Connection 82] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:50.209[1750445510209] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=btcusdt, interval=1m, connectionId=82
2025-06-21 02:51:50.209[1750445510209] | INFO  |            | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=BTCUSDT, interval=1m, connectionId=82
2025-06-21 02:51:50.209[1750445510209] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=btcusdt, interval=5m
2025-06-21 02:51:50.209[1750445510209] | INFO  |            | c.b.c.f.c.utils.WebSocketConnection  - [Connection 83] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:50.212[1750445510212] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=btcusdt, interval=5m, connectionId=83
2025-06-21 02:51:50.212[1750445510212] | INFO  |            | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=BTCUSDT, interval=5m, connectionId=83
2025-06-21 02:51:50.212[1750445510212] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=btcusdt, interval=15m
2025-06-21 02:51:50.212[1750445510212] | INFO  |            | c.b.c.f.c.utils.WebSocketConnection  - [Connection 84] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:50.212[1750445510212] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=btcusdt, interval=15m, connectionId=84
2025-06-21 02:51:50.212[1750445510212] | INFO  |            | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=BTCUSDT, interval=15m, connectionId=84
2025-06-21 02:51:50.212[1750445510212] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=btcusdt, interval=30m
2025-06-21 02:51:50.214[1750445510214] | INFO  |            | c.b.c.f.c.utils.WebSocketConnection  - [Connection 85] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:50.215[1750445510215] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=btcusdt, interval=30m, connectionId=85
2025-06-21 02:51:50.215[1750445510215] | INFO  |            | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=BTCUSDT, interval=30m, connectionId=85
2025-06-21 02:51:50.215[1750445510215] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=btcusdt, interval=1h
2025-06-21 02:51:50.216[1750445510216] | INFO  |            | c.b.c.f.c.utils.WebSocketConnection  - [Connection 86] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:50.216[1750445510216] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=btcusdt, interval=1h, connectionId=86
2025-06-21 02:51:50.216[1750445510216] | INFO  |            | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=BTCUSDT, interval=1h, connectionId=86
2025-06-21 02:51:50.216[1750445510216] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=btcusdt, interval=4h
2025-06-21 02:51:50.216[1750445510216] | INFO  |            | c.b.c.f.c.utils.WebSocketConnection  - [Connection 87] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:50.216[1750445510216] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=btcusdt, interval=4h, connectionId=87
2025-06-21 02:51:50.216[1750445510216] | INFO  |            | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=BTCUSDT, interval=4h, connectionId=87
2025-06-21 02:51:50.216[1750445510216] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=btcusdt, interval=1d
2025-06-21 02:51:50.216[1750445510216] | INFO  |            | c.b.c.f.c.utils.WebSocketConnection  - [Connection 88] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:50.216[1750445510216] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=btcusdt, interval=1d, connectionId=88
2025-06-21 02:51:50.216[1750445510216] | INFO  |            | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=BTCUSDT, interval=1d, connectionId=88
2025-06-21 02:51:50.217[1750445510217] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=ethusdt, interval=1m
2025-06-21 02:51:50.217[1750445510217] | INFO  |            | c.b.c.f.c.utils.WebSocketConnection  - [Connection 89] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:50.217[1750445510217] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=ethusdt, interval=1m, connectionId=89
2025-06-21 02:51:50.218[1750445510218] | INFO  |            | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=ETHUSDT, interval=1m, connectionId=89
2025-06-21 02:51:50.218[1750445510218] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=ethusdt, interval=5m
2025-06-21 02:51:50.218[1750445510218] | INFO  |            | c.b.c.f.c.utils.WebSocketConnection  - [Connection 90] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:50.218[1750445510218] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=ethusdt, interval=5m, connectionId=90
2025-06-21 02:51:50.218[1750445510218] | INFO  |            | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=ETHUSDT, interval=5m, connectionId=90
2025-06-21 02:51:50.218[1750445510218] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=ethusdt, interval=15m
2025-06-21 02:51:50.219[1750445510219] | INFO  |            | c.b.c.f.c.utils.WebSocketConnection  - [Connection 91] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:50.219[1750445510219] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=ethusdt, interval=15m, connectionId=91
2025-06-21 02:51:50.219[1750445510219] | INFO  |            | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=ETHUSDT, interval=15m, connectionId=91
2025-06-21 02:51:50.219[1750445510219] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=ethusdt, interval=30m
2025-06-21 02:51:50.219[1750445510219] | INFO  |            | c.b.c.f.c.utils.WebSocketConnection  - [Connection 92] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:50.219[1750445510219] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=ethusdt, interval=30m, connectionId=92
2025-06-21 02:51:50.219[1750445510219] | INFO  |            | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=ETHUSDT, interval=30m, connectionId=92
2025-06-21 02:51:50.219[1750445510219] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=ethusdt, interval=1h
2025-06-21 02:51:50.271[1750445510271] | INFO  |            | c.b.c.f.c.utils.WebSocketConnection  - [Connection 93] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:50.271[1750445510271] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=ethusdt, interval=1h, connectionId=93
2025-06-21 02:51:50.271[1750445510271] | INFO  |            | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=ETHUSDT, interval=1h, connectionId=93
2025-06-21 02:51:50.271[1750445510271] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=ethusdt, interval=4h
2025-06-21 02:51:50.271[1750445510271] | INFO  |            | c.b.c.f.c.utils.WebSocketConnection  - [Connection 94] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:50.272[1750445510272] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=ethusdt, interval=4h, connectionId=94
2025-06-21 02:51:50.272[1750445510272] | INFO  |            | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=ETHUSDT, interval=4h, connectionId=94
2025-06-21 02:51:50.272[1750445510272] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=ethusdt, interval=1d
2025-06-21 02:51:50.272[1750445510272] | INFO  |            | c.b.c.f.c.utils.WebSocketConnection  - [Connection 95] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:50.272[1750445510272] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=ethusdt, interval=1d, connectionId=95
2025-06-21 02:51:50.272[1750445510272] | INFO  |            | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=ETHUSDT, interval=1d, connectionId=95
2025-06-21 02:51:50.272[1750445510272] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=bnbusdt, interval=1m
2025-06-21 02:51:50.276[1750445510276] | INFO  |            | c.b.c.f.c.utils.WebSocketConnection  - [Connection 96] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:50.276[1750445510276] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=bnbusdt, interval=1m, connectionId=96
2025-06-21 02:51:50.278[1750445510278] | INFO  |            | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=BNBUSDT, interval=1m, connectionId=96
2025-06-21 02:51:50.278[1750445510278] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=bnbusdt, interval=5m
2025-06-21 02:51:50.281[1750445510281] | INFO  |            | c.b.c.f.c.utils.WebSocketConnection  - [Connection 97] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:50.282[1750445510282] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=bnbusdt, interval=5m, connectionId=97
2025-06-21 02:51:50.282[1750445510282] | INFO  |            | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=BNBUSDT, interval=5m, connectionId=97
2025-06-21 02:51:50.282[1750445510282] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=bnbusdt, interval=15m
2025-06-21 02:51:50.282[1750445510282] | INFO  |            | c.b.c.f.c.utils.WebSocketConnection  - [Connection 98] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:50.283[1750445510283] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=bnbusdt, interval=15m, connectionId=98
2025-06-21 02:51:50.283[1750445510283] | INFO  |            | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=BNBUSDT, interval=15m, connectionId=98
2025-06-21 02:51:50.283[1750445510283] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=bnbusdt, interval=30m
2025-06-21 02:51:50.283[1750445510283] | INFO  |            | c.b.c.f.c.utils.WebSocketConnection  - [Connection 99] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:50.283[1750445510283] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=bnbusdt, interval=30m, connectionId=99
2025-06-21 02:51:50.283[1750445510283] | INFO  |            | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=BNBUSDT, interval=30m, connectionId=99
2025-06-21 02:51:50.283[1750445510283] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=bnbusdt, interval=1h
2025-06-21 02:51:50.283[1750445510283] | INFO  |            | c.b.c.f.c.utils.WebSocketConnection  - [Connection 100] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:50.283[1750445510283] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=bnbusdt, interval=1h, connectionId=100
2025-06-21 02:51:50.283[1750445510283] | INFO  |            | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=BNBUSDT, interval=1h, connectionId=100
2025-06-21 02:51:50.284[1750445510284] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=bnbusdt, interval=4h
2025-06-21 02:51:50.284[1750445510284] | INFO  |            | c.b.c.f.c.utils.WebSocketConnection  - [Connection 101] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:50.284[1750445510284] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=bnbusdt, interval=4h, connectionId=101
2025-06-21 02:51:50.284[1750445510284] | INFO  |            | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=BNBUSDT, interval=4h, connectionId=101
2025-06-21 02:51:50.284[1750445510284] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅K线数据: symbol=bnbusdt, interval=1d
2025-06-21 02:51:50.284[1750445510284] | INFO  |            | c.b.c.f.c.utils.WebSocketConnection  - [Connection 102] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:50.288[1750445510288] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅K线数据: symbol=bnbusdt, interval=1d, connectionId=102
2025-06-21 02:51:50.289[1750445510289] | INFO  |            | c.c.t.m.listener.KlineDataListener   - 已订阅K线数据: symbol=BNBUSDT, interval=1d, connectionId=102
2025-06-21 02:51:50.289[1750445510289] | INFO  |            | c.c.t.m.s.impl.MarketDataServiceImpl - K线数据监听器启动成功
2025-06-21 02:51:50.289[1750445510289] | INFO  |            | c.c.t.m.s.impl.MarketDataServiceImpl - 启动深度数据监听器...
2025-06-21 02:51:50.289[1750445510289] | INFO  |            | c.c.t.m.listener.DepthDataListener   - 开始订阅深度数据...
2025-06-21 02:51:50.289[1750445510289] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅深度数据: symbol=btcusdt, levels=10, speed=1000
2025-06-21 02:51:50.289[1750445510289] | INFO  |            | c.b.c.f.c.utils.WebSocketConnection  - [Connection 103] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:50.289[1750445510289] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅深度数据: symbol=btcusdt, levels=10, speed=1000, connectionId=103
2025-06-21 02:51:50.290[1750445510290] | INFO  |            | c.c.t.m.listener.DepthDataListener   - 已订阅深度数据: symbol=BTCUSDT, levels=10, speed=1000, connectionId=103
2025-06-21 02:51:50.290[1750445510290] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅深度数据: symbol=ethusdt, levels=10, speed=1000
2025-06-21 02:51:50.290[1750445510290] | INFO  |            | c.b.c.f.c.utils.WebSocketConnection  - [Connection 104] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:50.290[1750445510290] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅深度数据: symbol=ethusdt, levels=10, speed=1000, connectionId=104
2025-06-21 02:51:50.290[1750445510290] | INFO  |            | c.c.t.m.listener.DepthDataListener   - 已订阅深度数据: symbol=ETHUSDT, levels=10, speed=1000, connectionId=104
2025-06-21 02:51:50.290[1750445510290] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅深度数据: symbol=bnbusdt, levels=10, speed=1000
2025-06-21 02:51:50.290[1750445510290] | INFO  |            | c.b.c.f.c.utils.WebSocketConnection  - [Connection 105] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:50.290[1750445510290] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅深度数据: symbol=bnbusdt, levels=10, speed=1000, connectionId=105
2025-06-21 02:51:50.290[1750445510290] | INFO  |            | c.c.t.m.listener.DepthDataListener   - 已订阅深度数据: symbol=BNBUSDT, levels=10, speed=1000, connectionId=105
2025-06-21 02:51:50.290[1750445510290] | INFO  |            | c.c.t.m.s.impl.MarketDataServiceImpl - 深度数据监听器启动成功
2025-06-21 02:51:50.291[1750445510291] | INFO  |            | c.c.t.m.s.impl.MarketDataServiceImpl - 启动交易数据监听器...
2025-06-21 02:51:50.291[1750445510291] | INFO  |            | c.c.t.m.listener.TradeDataListener   - 开始订阅交易数据...
2025-06-21 02:51:50.291[1750445510291] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅聚合交易数据: symbol=btcusdt
2025-06-21 02:51:50.291[1750445510291] | INFO  |            | c.b.c.f.c.utils.WebSocketConnection  - [Connection 106] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:50.291[1750445510291] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅聚合交易数据: symbol=btcusdt, connectionId=106
2025-06-21 02:51:50.291[1750445510291] | INFO  |            | c.c.t.m.listener.TradeDataListener   - 已订阅交易数据: symbol=BTCUSDT, connectionId=106
2025-06-21 02:51:50.291[1750445510291] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅聚合交易数据: symbol=ethusdt
2025-06-21 02:51:50.291[1750445510291] | INFO  |            | c.b.c.f.c.utils.WebSocketConnection  - [Connection 107] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:50.291[1750445510291] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅聚合交易数据: symbol=ethusdt, connectionId=107
2025-06-21 02:51:50.291[1750445510291] | INFO  |            | c.c.t.m.listener.TradeDataListener   - 已订阅交易数据: symbol=ETHUSDT, connectionId=107
2025-06-21 02:51:50.291[1750445510291] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 正在订阅聚合交易数据: symbol=bnbusdt
2025-06-21 02:51:50.291[1750445510291] | INFO  |            | c.b.c.f.c.utils.WebSocketConnection  - [Connection 108] Connecting to stream.binancefuture.com/stream
2025-06-21 02:51:50.292[1750445510292] | INFO  |            | c.c.t.s.w.BinanceWebSocketClientImpl - 成功订阅聚合交易数据: symbol=bnbusdt, connectionId=108
2025-06-21 02:51:50.292[1750445510292] | INFO  |            | c.c.t.m.listener.TradeDataListener   - 已订阅交易数据: symbol=BNBUSDT, connectionId=108
2025-06-21 02:51:50.292[1750445510292] | INFO  |            | c.c.t.m.s.impl.MarketDataServiceImpl - 交易数据监听器启动成功
2025-06-21 02:51:50.464[1750445510464] | INFO  | OkHttp https://stream.binancefuture.com/... | c.b.c.f.c.utils.WebSocketConnection  - [Connection 60] Connected to Server
2025-06-21 02:51:50.472[1750445510472] | INFO  | OkHttp https://stream.binancefuture.com/... | c.b.c.f.c.utils.WebSocketConnection  - [Connection 57] Connected to Server
2025-06-21 02:51:50.475[1750445510475] | INFO  | OkHttp https://stream.binancefuture.com/... | c.b.c.f.c.utils.WebSocketConnection  - [Connection 55] Connected to Server
2025-06-21 02:51:50.484[1750445510484] | INFO  | OkHttp https://stream.binancefuture.com/... | c.b.c.f.c.utils.WebSocketConnection  - [Connection 56] Connected to Server
2025-06-21 02:51:50.489[1750445510489] | INFO  | OkHttp https://stream.binancefuture.com/... | c.b.c.f.c.utils.WebSocketConnection  - [Connection 63] Connected to Server
2025-06-21 02:51:50.498[1750445510498] | INFO  | OkHttp https://stream.binancefuture.com/... | c.b.c.f.c.utils.WebSocketConnection  - [Connection 64] Connected to Server
2025-06-21 02:51:50.506[1750445510506] | INFO  | OkHttp https://stream.binancefuture.com/... | c.b.c.f.c.utils.WebSocketConnection  - [Connection 59] Connected to Server
2025-06-21 02:51:50.571[1750445510571] | INFO  | OkHttp https://stream.binancefuture.com/... | c.b.c.f.c.utils.WebSocketConnection  - [Connection 68] Connected to Server
2025-06-21 02:51:50.574[1750445510574] | INFO  | OkHttp https://stream.binancefuture.com/... | c.b.c.f.c.utils.WebSocketConnection  - [Connection 67] Connected to Server
2025-06-21 02:51:50.577[1750445510577] | INFO  | OkHttp https://stream.binancefuture.com/... | c.b.c.f.c.utils.WebSocketConnection  - [Connection 69] Connected to Server
2025-06-21 02:51:50.577[1750445510577] | INFO  | OkHttp https://stream.binancefuture.com/... | c.b.c.f.c.utils.WebSocketConnection  - [Connection 62] Connected to Server
2025-06-21 02:51:50.709[1750445510709] | ERROR | OkHttp https://stream.binancefuture.com/... | c.b.c.f.c.utils.WebSocketConnection  - [Connection 70] Failure
java.net.SocketException: Connection reset
	at java.base/sun.nio.ch.NioSocketImpl.implRead(NioSocketImpl.java:318)
	at java.base/sun.nio.ch.NioSocketImpl.read(NioSocketImpl.java:346)
	at java.base/sun.nio.ch.NioSocketImpl$1.read(NioSocketImpl.java:796)
	at java.base/java.net.Socket$SocketInputStream.read(Socket.java:1099)
	at java.base/sun.security.ssl.SSLSocketInputRecord.read(SSLSocketInputRecord.java:489)
	at java.base/sun.security.ssl.SSLSocketInputRecord.readHeader(SSLSocketInputRecord.java:483)
	at java.base/sun.security.ssl.SSLSocketInputRecord.decode(SSLSocketInputRecord.java:160)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:111)
	at java.base/sun.security.ssl.SSLSocketImpl.decode(SSLSocketImpl.java:1506)
	at java.base/sun.security.ssl.SSLSocketImpl.readHandshakeRecord(SSLSocketImpl.java:1421)
	at java.base/sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:455)
	at java.base/sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:426)
	at okhttp3.internal.connection.RealConnection.connectTls(RealConnection.kt:379)
	at okhttp3.internal.connection.RealConnection.establishProtocol(RealConnection.kt:337)
	at okhttp3.internal.connection.RealConnection.connect(RealConnection.kt:209)
	at okhttp3.internal.connection.ExchangeFinder.findConnection(ExchangeFinder.kt:226)
	at okhttp3.internal.connection.ExchangeFinder.findHealthyConnection(ExchangeFinder.kt:106)
	at okhttp3.internal.connection.ExchangeFinder.find(ExchangeFinder.kt:74)
	at okhttp3.internal.connection.RealCall.initExchange$okhttp(RealCall.kt:255)
	at okhttp3.internal.connection.ConnectInterceptor.intercept(ConnectInterceptor.kt:32)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
	at okhttp3.internal.cache.CacheInterceptor.intercept(CacheInterceptor.kt:95)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
	at okhttp3.internal.http.BridgeInterceptor.intercept(BridgeInterceptor.kt:83)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
	at okhttp3.internal.http.RetryAndFollowUpInterceptor.intercept(RetryAndFollowUpInterceptor.kt:76)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
	at okhttp3.internal.connection.RealCall.getResponseWithInterceptorChain$okhttp(RealCall.kt:201)
	at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:517)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
	Suppressed: java.net.SocketException: Connection reset
		... 32 common frames omitted
2025-06-21 02:51:50.710[1750445510710] | WARN  | scheduling-1 | c.c.t.b.discovery.ServiceRegistry    - 服务 http://localhost:9530 健康检查异常: I/O error on GET request for "http://localhost:9530/status": Connection refused: getsockopt
2025-06-21 02:51:50.710[1750445510710] | WARN  | scheduling-1 | c.c.t.b.discovery.ServiceRegistry    - 服务 http://localhost:9530 健康检查失败，连续失败次数: 1
2025-06-21 02:51:50.710[1750445510710] | INFO  | scheduling-1 | c.c.t.b.discovery.ServiceRegistry    - 服务状态摘要: 总数 1，健康 0，不健康 1
2025-06-21 02:51:50.726[1750445510726] | INFO  | OkHttp https://stream.binancefuture.com/... | c.b.c.f.c.utils.WebSocketConnection  - [Connection 58] Connected to Server
2025-06-21 02:51:50.739[1750445510739] | INFO  | OkHttp https://stream.binancefuture.com/... | c.b.c.f.c.utils.WebSocketConnection  - [Connection 65] Connected to Server
2025-06-21 02:51:50.744[1750445510744] | INFO  | OkHttp https://stream.binancefuture.com/... | c.b.c.f.c.utils.WebSocketConnection  - [Connection 61] Connected to Server
2025-06-21 02:51:50.827[1750445510827] | INFO  | OkHttp https://stream.binancefuture.com/... | c.b.c.f.c.utils.WebSocketConnection  - [Connection 71] Connected to Server
2025-06-21 02:51:50.881[1750445510881] | INFO  | OkHttp https://stream.binancefuture.com/... | c.b.c.f.c.utils.WebSocketConnection  - [Connection 66] Connected to Server
2025-06-21 02:51:51.165[1750445511165] | INFO  | OkHttp https://stream.binancefuture.com/... | c.b.c.f.c.utils.WebSocketConnection  - [Connection 72] Connected to Server
2025-06-21 02:51:51.215[1750445511215] | WARN  | main       | c.c.t.b.discovery.ServiceRegistry    - 没有健康的服务可用
2025-06-21 02:51:53.216[1750445513216] | WARN  | main       | c.c.t.b.discovery.ServiceRegistry    - 没有健康的服务可用
2025-06-21 02:51:53.217[1750445513217] | ERROR | main       | c.c.t.b.manager.StrategyManager      - 连接策略服务失败
com.crypto.trading.common.exception.StrategyServiceException: 没有健康的策略服务可用
	at com.crypto.trading.bootstrap.client.StrategyServiceClient.getNextServiceUrl(StrategyServiceClient.java:152)
	at com.crypto.trading.bootstrap.client.StrategyServiceClient.getStatus(StrategyServiceClient.java:182)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.retry.interceptor.RetryOperationsInterceptor$1.doWithRetry(RetryOperationsInterceptor.java:102)
	at org.springframework.retry.support.RetryTemplate.doExecute(RetryTemplate.java:329)
	at org.springframework.retry.support.RetryTemplate.execute(RetryTemplate.java:209)
	at org.springframework.retry.interceptor.RetryOperationsInterceptor.invoke(RetryOperationsInterceptor.java:135)
	at org.springframework.retry.annotation.AnnotationAwareRetryOperationsInterceptor.invoke(AnnotationAwareRetryOperationsInterceptor.java:160)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.crypto.trading.bootstrap.client.StrategyServiceClient$$SpringCGLIB$$0.getStatus(<generated>)
	at com.crypto.trading.bootstrap.manager.StrategyManager.connectToRemoteService(StrategyManager.java:187)
	at com.crypto.trading.bootstrap.manager.StrategyManager.startStrategy(StrategyManager.java:92)
	at com.crypto.trading.bootstrap.lifecycle.ApplicationLifecycle.startPythonStrategy(ApplicationLifecycle.java:93)
	at com.crypto.trading.bootstrap.lifecycle.ApplicationLifecycle.onApplicationEvent(ApplicationLifecycle.java:67)
	at com.crypto.trading.bootstrap.lifecycle.ApplicationLifecycle.onApplicationEvent(ApplicationLifecycle.java:15)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:149)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:445)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:378)
	at org.springframework.boot.context.event.EventPublishingRunListener.ready(EventPublishingRunListener.java:109)
	at org.springframework.boot.SpringApplicationRunListeners.lambda$ready$6(SpringApplicationRunListeners.java:80)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:118)
	at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:112)
	at org.springframework.boot.SpringApplicationRunListeners.ready(SpringApplicationRunListeners.java:80)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:341)
	at com.crypto.trading.bootstrap.CryptoApplication.main(CryptoApplication.java:37)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.boot.loader.launch.Launcher.launch(Launcher.java:91)
	at org.springframework.boot.loader.launch.Launcher.launch(Launcher.java:53)
	at org.springframework.boot.loader.launch.JarLauncher.main(JarLauncher.java:58)
2025-06-21 02:51:53.217[1750445513217] | WARN  | main       | c.c.t.b.l.ApplicationLifecycle       - 启动Python策略模块失败，但应用程序将继续运行。请检查相关日志获取详细信息。
2025-06-21 02:51:53.217[1750445513217] | INFO  | main       | c.c.t.b.l.ApplicationLifecycle       - 应用程序启动完成
2025-06-21 02:51:53.218[1750445513218] | INFO  | main       | c.c.t.b.l.ApplicationLifecycle       - 已注册虚拟线程关闭监听器: VirtualThreadShutdownListener
2025-06-21 02:51:53.218[1750445513218] | INFO  | main       | c.c.t.bootstrap.CryptoApplication    - 虚拟货币量化交易系统启动成功
2025-06-21 02:52:35.677[1750445555677] | INFO  | pool-5-thread-1 | c.c.t.sdk.limiter.RateLimitMonitor   - API限流监控数据 - 时间窗口: 1分钟
2025-06-21 02:52:35.678[1750445555678] | INFO  | pool-5-thread-1 | c.c.t.sdk.limiter.RateLimitMonitor   - 总请求数: 0, 总限流次数: 0, 总错误次数: 0
2025-06-21 02:52:50.727[1750445570727] | WARN  | scheduling-1 | c.c.t.b.discovery.ServiceRegistry    - 服务 http://localhost:9530 健康检查异常: I/O error on GET request for "http://localhost:9530/status": Connection refused: getsockopt
2025-06-21 02:52:50.728[1750445570728] | WARN  | scheduling-1 | c.c.t.b.discovery.ServiceRegistry    - 服务 http://localhost:9530 健康检查失败，连续失败次数: 2
2025-06-21 02:52:50.728[1750445570728] | INFO  | scheduling-1 | c.c.t.b.discovery.ServiceRegistry    - 服务状态摘要: 总数 1，健康 0，不健康 1
2025-06-21 02:53:35.677[1750445615677] | INFO  | pool-5-thread-1 | c.c.t.sdk.limiter.RateLimitMonitor   - API限流监控数据 - 时间窗口: 1分钟
2025-06-21 02:53:35.678[1750445615678] | INFO  | pool-5-thread-1 | c.c.t.sdk.limiter.RateLimitMonitor   - 总请求数: 0, 总限流次数: 0, 总错误次数: 0
2025-06-21 02:53:50.758[1750445630758] | WARN  | scheduling-1 | c.c.t.b.discovery.ServiceRegistry    - 服务 http://localhost:9530 健康检查异常: I/O error on GET request for "http://localhost:9530/status": Connection refused: getsockopt
2025-06-21 02:53:50.758[1750445630758] | WARN  | scheduling-1 | c.c.t.b.discovery.ServiceRegistry    - 服务 http://localhost:9530 健康检查失败，连续失败次数: 3
2025-06-21 02:53:50.758[1750445630758] | INFO  | scheduling-1 | c.c.t.b.discovery.ServiceRegistry    - 尝试重新初始化服务 http://localhost:9530
2025-06-21 02:53:50.759[1750445630759] | INFO  | scheduling-1 | c.c.t.b.discovery.ServiceRegistry    - 尝试初始化服务: http://localhost:9530/init
2025-06-21 02:53:50.767[1750445630767] | WARN  | scheduling-1 | c.c.t.b.discovery.ServiceRegistry    - 尝试初始化服务 http://localhost:9530 时发生异常: I/O error on POST request for "http://localhost:9530/init": Connection refused: getsockopt
2025-06-21 02:53:50.767[1750445630767] | INFO  | scheduling-1 | c.c.t.b.discovery.ServiceRegistry    - 服务状态摘要: 总数 1，健康 0，不健康 1
2025-06-21 02:54:35.676[1750445675676] | INFO  | pool-5-thread-1 | c.c.t.sdk.limiter.RateLimitMonitor   - API限流监控数据 - 时间窗口: 1分钟
2025-06-21 02:54:35.676[1750445675676] | INFO  | pool-5-thread-1 | c.c.t.sdk.limiter.RateLimitMonitor   - 总请求数: 0, 总限流次数: 0, 总错误次数: 0
