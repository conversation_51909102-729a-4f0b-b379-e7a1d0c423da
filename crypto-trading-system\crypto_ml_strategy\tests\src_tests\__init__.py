"""
Crypto ML Strategy - 测试模块

该包包含系统的所有测试文件，包括单元测试、集成测试、
性能测试、并发测试等各种测试类型。

主要测试类别:
- 单元测试: test_*.py
- 集成测试: integration_*.py
- 性能测试: *_performance_tests.py
- 并发测试: concurrent_*.py
- 框架测试: enhanced_testing_framework.py

作者: Crypto ML Strategy Team
版本: 1.0.0
"""

# 测试组件将在需要时导入
# from .enhanced_testing_framework import EnhancedTestingFramework
# from .integration_test_suite import IntegrationTestSuite

__all__ = []

__version__ = '1.0.0'
__author__ = 'Crypto ML Strategy Team'