package com.crypto.trading.market.monitor;

import com.crypto.trading.market.consumer.DepthDataConsumer;
import com.crypto.trading.market.consumer.KlineDataConsumer;
import com.crypto.trading.market.consumer.TradeDataConsumer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 数据处理监控器
 * 监控Kafka消费者的处理状态和性能指标
 * 提供健康检查和性能统计功能
 */
@Component
public class DataProcessingMonitor implements HealthIndicator {

    private static final Logger log = LoggerFactory.getLogger(DataProcessingMonitor.class);

    @Autowired
    private KlineDataConsumer klineDataConsumer;

    @Autowired
    private DepthDataConsumer depthDataConsumer;

    @Autowired
    private TradeDataConsumer tradeDataConsumer;

    /**
     * 上次统计时间
     */
    private LocalDateTime lastStatsTime = LocalDateTime.now();

    /**
     * 上次K线成功计数
     */
    private final AtomicLong lastKlineSuccessCount = new AtomicLong(0);

    /**
     * 上次深度成功计数
     */
    private final AtomicLong lastDepthSuccessCount = new AtomicLong(0);

    /**
     * 上次交易成功计数
     */
    private final AtomicLong lastTradeSuccessCount = new AtomicLong(0);

    /**
     * 健康检查
     */
    @Override
    public Health health() {
        try {
            Map<String, Object> details = new HashMap<>();
            
            // K线数据消费者状态
            details.put("kline.success.count", klineDataConsumer.getSuccessCount());
            details.put("kline.failure.count", klineDataConsumer.getFailureCount());
            
            // 深度数据消费者状态
            details.put("depth.success.count", depthDataConsumer.getSuccessCount());
            details.put("depth.failure.count", depthDataConsumer.getFailureCount());
            
            // 交易数据消费者状态
            details.put("trade.success.count", tradeDataConsumer.getSuccessCount());
            details.put("trade.failure.count", tradeDataConsumer.getFailureCount());
            
            // 计算总体健康状态
            long totalFailures = klineDataConsumer.getFailureCount() + 
                               depthDataConsumer.getFailureCount() + 
                               tradeDataConsumer.getFailureCount();
            
            long totalSuccess = klineDataConsumer.getSuccessCount() + 
                              depthDataConsumer.getSuccessCount() + 
                              tradeDataConsumer.getSuccessCount();
            
            details.put("total.success.count", totalSuccess);
            details.put("total.failure.count", totalFailures);
            
            // 计算成功率
            if (totalSuccess + totalFailures > 0) {
                double successRate = (double) totalSuccess / (totalSuccess + totalFailures) * 100;
                details.put("success.rate.percent", String.format("%.2f", successRate));
                
                // 如果成功率低于95%，标记为DOWN
                if (successRate < 95.0) {
                    return Health.down()
                            .withDetail("reason", "Success rate below 95%")
                            .withDetails(details)
                            .build();
                }
            }
            
            return Health.up().withDetails(details).build();
            
        } catch (Exception e) {
            log.error("健康检查异常", e);
            return Health.down()
                    .withDetail("error", e.getMessage())
                    .build();
        }
    }

    /**
     * 定期打印性能统计信息
     * 每分钟执行一次
     */
    @Scheduled(fixedRate = 60000) // 60秒
    public void printPerformanceStats() {
        try {
            LocalDateTime now = LocalDateTime.now();
            
            // 获取当前计数
            long currentKlineSuccess = klineDataConsumer.getSuccessCount();
            long currentDepthSuccess = depthDataConsumer.getSuccessCount();
            long currentTradeSuccess = tradeDataConsumer.getSuccessCount();
            
            // 计算增量
            long klineDelta = currentKlineSuccess - lastKlineSuccessCount.get();
            long depthDelta = currentDepthSuccess - lastDepthSuccessCount.get();
            long tradeDelta = currentTradeSuccess - lastTradeSuccessCount.get();
            
            // 计算处理速率（每分钟）
            log.info("=== 数据处理性能统计 [{}] ===", now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            log.info("K线数据: 总计={}, 本分钟={}, 失败={}", 
                    currentKlineSuccess, klineDelta, klineDataConsumer.getFailureCount());
            log.info("深度数据: 总计={}, 本分钟={}, 失败={}", 
                    currentDepthSuccess, depthDelta, depthDataConsumer.getFailureCount());
            log.info("交易数据: 总计={}, 本分钟={}, 失败={}", 
                    currentTradeSuccess, tradeDelta, tradeDataConsumer.getFailureCount());
            
            long totalDelta = klineDelta + depthDelta + tradeDelta;
            log.info("总计处理: 本分钟={} 条/分钟", totalDelta);
            
            // 更新上次计数
            lastKlineSuccessCount.set(currentKlineSuccess);
            lastDepthSuccessCount.set(currentDepthSuccess);
            lastTradeSuccessCount.set(currentTradeSuccess);
            lastStatsTime = now;
            
        } catch (Exception e) {
            log.error("打印性能统计异常", e);
        }
    }

    /**
     * 重置所有计数器
     */
    public void resetAllCounters() {
        klineDataConsumer.resetCounters();
        depthDataConsumer.resetCounters();
        tradeDataConsumer.resetCounters();
        
        lastKlineSuccessCount.set(0);
        lastDepthSuccessCount.set(0);
        lastTradeSuccessCount.set(0);
        lastStatsTime = LocalDateTime.now();
        
        log.info("所有计数器已重置");
    }

    /**
     * 获取详细统计信息
     */
    public Map<String, Object> getDetailedStats() {
        Map<String, Object> stats = new HashMap<>();
        
        // K线数据统计
        Map<String, Object> klineStats = new HashMap<>();
        klineStats.put("successCount", klineDataConsumer.getSuccessCount());
        klineStats.put("failureCount", klineDataConsumer.getFailureCount());
        stats.put("kline", klineStats);
        
        // 深度数据统计
        Map<String, Object> depthStats = new HashMap<>();
        depthStats.put("successCount", depthDataConsumer.getSuccessCount());
        depthStats.put("failureCount", depthDataConsumer.getFailureCount());
        stats.put("depth", depthStats);
        
        // 交易数据统计
        Map<String, Object> tradeStats = new HashMap<>();
        tradeStats.put("successCount", tradeDataConsumer.getSuccessCount());
        tradeStats.put("failureCount", tradeDataConsumer.getFailureCount());
        stats.put("trade", tradeStats);
        
        // 总体统计
        long totalSuccess = klineDataConsumer.getSuccessCount() + 
                          depthDataConsumer.getSuccessCount() + 
                          tradeDataConsumer.getSuccessCount();
        long totalFailure = klineDataConsumer.getFailureCount() + 
                          depthDataConsumer.getFailureCount() + 
                          tradeDataConsumer.getFailureCount();
        
        Map<String, Object> totalStats = new HashMap<>();
        totalStats.put("successCount", totalSuccess);
        totalStats.put("failureCount", totalFailure);
        totalStats.put("totalCount", totalSuccess + totalFailure);
        
        if (totalSuccess + totalFailure > 0) {
            double successRate = (double) totalSuccess / (totalSuccess + totalFailure) * 100;
            totalStats.put("successRate", String.format("%.2f%%", successRate));
        } else {
            totalStats.put("successRate", "N/A");
        }
        
        stats.put("total", totalStats);
        stats.put("lastStatsTime", lastStatsTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        
        return stats;
    }
}
