package com.crypto.trading.common.config;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Import;
import com.crypto.trading.common.TestConfig;

/**
 * BinanceConfig测试类
 * 注：此测试类已被禁用，因为BinanceConfig类已移至bootstrap模块
 */
@SpringBootTest
@Import(TestConfig.class)
class BinanceConfigTest {
    
    @Test
    void contextLoads() {
        // 测试上下文加载
    }
}