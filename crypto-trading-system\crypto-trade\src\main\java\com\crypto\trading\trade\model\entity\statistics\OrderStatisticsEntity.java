package com.crypto.trading.trade.model.entity.statistics;

import com.baomidou.mybatisplus.annotation.*;

import java.math.BigDecimal;

/**
 * 订单统计实体类
 * 
 * <p>对应数据库表t_order_statistics，存储订单统计数据</p>
 */
@TableName("t_order_statistics")
public class OrderStatisticsEntity {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 统计日期（格式：yyyyMMdd）
     */
    private String statsDate;

    /**
     * 统计小时（0-23）
     */
    private Integer statsHour;

    /**
     * 交易对
     */
    private String symbol;

    /**
     * 策略名称
     */
    private String strategy;

    /**
     * 总订单数
     */
    private Integer totalOrders;

    /**
     * 成功订单数
     */
    private Integer successOrders;

    /**
     * 失败订单数
     */
    private Integer failedOrders;

    /**
     * 取消订单数
     */
    private Integer canceledOrders;

    /**
     * 总交易金额
     */
    private BigDecimal totalAmount;

    /**
     * 平均执行时间（毫秒）
     */
    private Long avgExecutionTime;

    /**
     * 最小执行时间（毫秒）
     */
    private Long minExecutionTime;

    /**
     * 最大执行时间（毫秒）
     */
    private Long maxExecutionTime;

    /**
     * 成功率（百分比）
     */
    private BigDecimal successRate;

    /**
     * 平均价格偏差率（百分比）
     */
    private BigDecimal avgPriceDeviation;

    /**
     * 更新时间（毫秒时间戳）
     */
    private Long updatedTime;

    /**
     * 创建时间（毫秒时间戳）
     */
    private Long createdTime;

    /**
     * 默认构造函数
     */
    public OrderStatisticsEntity() {
    }

    // Getters and Setters

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getStatsDate() {
        return statsDate;
    }

    public void setStatsDate(String statsDate) {
        this.statsDate = statsDate;
    }

    public Integer getStatsHour() {
        return statsHour;
    }

    public void setStatsHour(Integer statsHour) {
        this.statsHour = statsHour;
    }

    public String getSymbol() {
        return symbol;
    }

    public void setSymbol(String symbol) {
        this.symbol = symbol;
    }

    public String getStrategy() {
        return strategy;
    }

    public void setStrategy(String strategy) {
        this.strategy = strategy;
    }

    public Integer getTotalOrders() {
        return totalOrders;
    }

    public void setTotalOrders(Integer totalOrders) {
        this.totalOrders = totalOrders;
    }

    public Integer getSuccessOrders() {
        return successOrders;
    }

    public void setSuccessOrders(Integer successOrders) {
        this.successOrders = successOrders;
    }

    public Integer getFailedOrders() {
        return failedOrders;
    }

    public void setFailedOrders(Integer failedOrders) {
        this.failedOrders = failedOrders;
    }

    public Integer getCanceledOrders() {
        return canceledOrders;
    }

    public void setCanceledOrders(Integer canceledOrders) {
        this.canceledOrders = canceledOrders;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public Long getAvgExecutionTime() {
        return avgExecutionTime;
    }

    public void setAvgExecutionTime(Long avgExecutionTime) {
        this.avgExecutionTime = avgExecutionTime;
    }

    public Long getMinExecutionTime() {
        return minExecutionTime;
    }

    public void setMinExecutionTime(Long minExecutionTime) {
        this.minExecutionTime = minExecutionTime;
    }

    public Long getMaxExecutionTime() {
        return maxExecutionTime;
    }

    public void setMaxExecutionTime(Long maxExecutionTime) {
        this.maxExecutionTime = maxExecutionTime;
    }

    public BigDecimal getSuccessRate() {
        return successRate;
    }

    public void setSuccessRate(BigDecimal successRate) {
        this.successRate = successRate;
    }

    public BigDecimal getAvgPriceDeviation() {
        return avgPriceDeviation;
    }

    public void setAvgPriceDeviation(BigDecimal avgPriceDeviation) {
        this.avgPriceDeviation = avgPriceDeviation;
    }

    public Long getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(Long updatedTime) {
        this.updatedTime = updatedTime;
    }

    public Long getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(Long createdTime) {
        this.createdTime = createdTime;
    }

    @Override
    public String toString() {
        return "OrderStatisticsEntity{" +
                "id=" + id +
                ", statsDate='" + statsDate + '\'' +
                ", statsHour=" + statsHour +
                ", symbol='" + symbol + '\'' +
                ", strategy='" + strategy + '\'' +
                ", totalOrders=" + totalOrders +
                ", successOrders=" + successOrders +
                ", failedOrders=" + failedOrders +
                ", successRate=" + successRate +
                ", avgExecutionTime=" + avgExecutionTime +
                '}';
    }

    /**
     * 构建者模式内部类
     */
    public static class Builder {
        private final OrderStatisticsEntity stats;

        public Builder() {
            stats = new OrderStatisticsEntity();
            stats.setCreatedTime(System.currentTimeMillis());
            stats.setUpdatedTime(System.currentTimeMillis());
            // 初始化默认值
            stats.setTotalOrders(0);
            stats.setSuccessOrders(0);
            stats.setFailedOrders(0);
            stats.setCanceledOrders(0);
            stats.setTotalAmount(BigDecimal.ZERO);
            stats.setSuccessRate(BigDecimal.ZERO);
            stats.setAvgPriceDeviation(BigDecimal.ZERO);
        }

        public Builder statsDate(String statsDate) {
            stats.setStatsDate(statsDate);
            return this;
        }

        public Builder statsHour(Integer statsHour) {
            stats.setStatsHour(statsHour);
            return this;
        }

        public Builder symbol(String symbol) {
            stats.setSymbol(symbol);
            return this;
        }

        public Builder strategy(String strategy) {
            stats.setStrategy(strategy);
            return this;
        }

        public Builder totalOrders(Integer totalOrders) {
            stats.setTotalOrders(totalOrders);
            return this;
        }

        public Builder successOrders(Integer successOrders) {
            stats.setSuccessOrders(successOrders);
            return this;
        }

        public Builder failedOrders(Integer failedOrders) {
            stats.setFailedOrders(failedOrders);
            return this;
        }

        public Builder canceledOrders(Integer canceledOrders) {
            stats.setCanceledOrders(canceledOrders);
            return this;
        }

        public Builder totalAmount(BigDecimal totalAmount) {
            stats.setTotalAmount(totalAmount);
            return this;
        }

        public Builder avgExecutionTime(Long avgExecutionTime) {
            stats.setAvgExecutionTime(avgExecutionTime);
            return this;
        }

        public Builder minExecutionTime(Long minExecutionTime) {
            stats.setMinExecutionTime(minExecutionTime);
            return this;
        }

        public Builder maxExecutionTime(Long maxExecutionTime) {
            stats.setMaxExecutionTime(maxExecutionTime);
            return this;
        }

        public Builder successRate(BigDecimal successRate) {
            stats.setSuccessRate(successRate);
            return this;
        }

        public Builder avgPriceDeviation(BigDecimal avgPriceDeviation) {
            stats.setAvgPriceDeviation(avgPriceDeviation);
            return this;
        }

        public OrderStatisticsEntity build() {
            // 计算成功率
            if (stats.getTotalOrders() > 0 && stats.getSuccessOrders() != null) {
                BigDecimal successRate = new BigDecimal(stats.getSuccessOrders())
                        .divide(new BigDecimal(stats.getTotalOrders()), 4, BigDecimal.ROUND_HALF_UP)
                        .multiply(new BigDecimal("100"));
                stats.setSuccessRate(successRate);
            }
            
            return stats;
        }
    }
}