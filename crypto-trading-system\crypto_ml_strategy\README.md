# 虚拟货币量化交易系统 - 机器学习策略模块

## 项目介绍

机器学习策略模块(crypto_ml_strategy)是虚拟货币量化交易系统的核心组件，负责使用统一机器学习策略生成交易信号。该模块整合了多种技术指标和特征，包括LPPL、Hematread、Bull Market Support Band、SuperTrend等，并使用DeepSeek蒸馏技术优化模型性能。模块通过Kafka与Java后端系统进行通信，接收市场数据并发布交易信号。

## 主要功能

1. **统一机器学习策略**：整合多种技术指标和特征，提供更准确的市场预测
2. **风险评估与控制**：内置风险评估机制，动态调整交易策略
3. **多时间周期整合**：同时分析多个时间周期的市场数据，捕捉不同级别的市场机会
4. **真实数据训练**：支持使用历史真实市场数据训练模型
5. **在线学习**：实时从市场数据中学习和优化模型
6. **DeepSeek蒸馏**：使用知识蒸馏技术优化模型大小和推理速度

## 目录结构

```
crypto_ml_strategy/
├── config/               # 配置文件
│   ├── config.ini        # 主配置文件
│   ├── logging.conf      # 日志配置
│   └── strategy_params.json  # 策略参数配置
├── models/               # 预训练模型存储目录
├── notebooks/            # Jupyter笔记本
├── src/                  # 源代码目录
│   ├── main.py           # 主入口文件
│   ├── config.py         # 配置管理
│   ├── logger.py         # 日志管理
│   ├── data_processor.py # 数据处理模块
│   ├── kafka_client.py   # Kafka通信客户端
│   ├── strategy/         # 策略实现
│   ├── utils/            # 工具函数
│   ├── indicators/       # 技术指标计算
│   ├── data/             # 数据访问
│   ├── model/            # 机器学习模型
│   └── evaluation/       # 模型评估
└── tests/                # 单元测试
```

## 安装与配置

### 依赖项

项目依赖详见`requirements.txt`文件。

### 安装步骤

1. 克隆项目代码库
```bash
git clone https://github.com/your-org/crypto-trading-system.git
```

2. 安装依赖
```bash
cd crypto-trading-system/crypto_ml_strategy
pip install -r requirements.txt
```

3. 安装为开发模式
```bash
pip install -e .
```

### 配置

配置文件位于`config/`目录下：
- `config.ini`: 主配置文件
- `logging.conf`: 日志配置
- `strategy_params.json`: 策略参数配置

根据您的环境修改配置文件中的相关参数。

## 使用方法

### 启动服务

```bash
python -m src.main
```

或使用安装后的entry point:

```bash
crypto_ml_strategy
```

### 开发与测试

运行单元测试:

```bash
pytest tests/
```

## 工作流程

1. 从Kafka接收市场数据
2. 进行数据预处理和特征工程
3. 运行统一机器学习策略模型
4. 风险评估和信号生成
5. 将交易信号发布到Kafka

## 技术指标说明

### LPPL (Log-Periodic Power Law)
用于检测市场泡沫和崩溃的数学模型，通过拟合价格时间序列识别不稳定的市场状态。

### Hematread
综合动量和趋势强度的技术指标，用于识别市场的力量变化和潜在转折点。

### Bull Market Support Band
基于长期移动平均线的支撑位指标，用于识别牛市中的重要支撑位置。

### SuperTrend
趋势跟踪指标，基于ATR计算动态支撑和阻力位，生成明确的买卖信号。

## 在线学习与知识蒸馏

### 在线学习
系统通过增量学习持续从最新市场数据中学习，自动适应市场变化。

### DeepSeek蒸馏
使用知识蒸馏技术优化模型大小和推理速度，在保持准确性的同时降低计算资源需求。

## 贡献者

- 交易团队

## 许可证

MIT