package com.crypto.trading.market.listener;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.crypto.trading.market.processor.TradeDataProcessor;
import com.crypto.trading.sdk.websocket.BinanceWebSocketClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Consumer;

/**
 * 交易数据监听器
 * 负责订阅和处理币安WebSocket交易数据
 */
@Component
public class TradeDataListener {

    private static final Logger log = LoggerFactory.getLogger(TradeDataListener.class);

    @Autowired
    private BinanceWebSocketClient webSocketClient;

    @Autowired
    private TradeDataProcessor tradeDataProcessor;

    @Autowired
    @org.springframework.beans.factory.annotation.Qualifier("symbolsList")
    private List<String> symbols;

    /**
     * 连接ID映射，用于跟踪WebSocket连接
     * 格式: symbol -> connectionId
     */
    private final Map<String, Integer> connectionIds = new ConcurrentHashMap<>();

    /**
     * 启动监听器
     */
    public void start() {
        startTradeDataSubscription();
    }

    /**
     * 启动交易数据订阅
     */
    public void startTradeDataSubscription() {
        log.info("开始订阅交易数据...");
        
        // 为每个交易对创建WebSocket连接
        for (String symbol : symbols) {
            // 创建消息处理器
            Consumer<String> messageHandler = createTradeMessageHandler(symbol);
            
            // 订阅交易数据
            int connectionId = webSocketClient.subscribeAggTrade(symbol.toLowerCase(), messageHandler);
            
            // 保存连接ID
            connectionIds.put(symbol, connectionId);
            
            log.info("已订阅交易数据: symbol={}, connectionId={}", symbol, connectionId);
        }
    }

    /**
     * 停止交易数据订阅
     */
    public void stopTradeDataSubscription() {
        log.info("停止订阅交易数据...");
        
        // 关闭所有WebSocket连接
        for (Map.Entry<String, Integer> entry : connectionIds.entrySet()) {
            webSocketClient.closeConnection(entry.getValue());
            log.info("已关闭交易数据连接: symbol={}, connectionId={}", entry.getKey(), entry.getValue());
        }
        
        // 清空连接ID映射
        connectionIds.clear();
    }

    /**
     * 创建交易消息处理器
     *
     * @param symbol 交易对
     * @return 消息处理器
     */
    private Consumer<String> createTradeMessageHandler(String symbol) {
        return message -> {
            try {
                // 解析消息
                JSONObject jsonObject = JSON.parseObject(message);

                // 检查消息是否包含data字段（币安WebSocket消息格式）
                JSONObject dataObject;
                if (jsonObject.containsKey("data")) {
                    // 嵌套格式：{"stream": "...", "data": {...}}
                    dataObject = jsonObject.getJSONObject("data");
                } else {
                    // 直接格式：{"e": "aggTrade", ...}
                    dataObject = jsonObject;
                }

                // 确保消息类型是聚合交易数据
                if (!"aggTrade".equals(dataObject.getString("e"))) {
                    return;
                }

                log.debug("收到聚合交易数据: symbol={}, tradeId={}, price={}, quantity={}",
                        symbol,
                        dataObject.getLongValue("a"),
                        dataObject.getString("p"),
                        dataObject.getString("q"));

                // 直接将原始消息传递给处理器，处理器将负责解析和转换
                tradeDataProcessor.processTradeData(message);

            } catch (Exception e) {
                log.error("处理交易数据异常: symbol={}, error={}", symbol, e.getMessage(), e);
            }
        };
    }
}