package com.crypto.trading.common.config;

import io.confluent.kafka.serializers.AbstractKafkaSchemaSerDeConfig;
import io.confluent.kafka.serializers.KafkaAvroSerializer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.serialization.StringSerializer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;
import org.springframework.kafka.support.serializer.JsonSerializer;

import java.util.HashMap;
import java.util.Map;

/**
 * 抽象Kafka生产者配置基类
 * <p>
 * 提供Kafka生产者的基础配置，子类可以通过覆盖特定方法来定制行为。
 * 这个基类提取了各模块中KafkaProducerConfig的共同配置逻辑，减少代码重复。
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public abstract class AbstractKafkaProducerConfig {

    protected final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    protected KafkaConfig kafkaConfig;

    /**
     * Kafka服务器地址
     */
    @Value("${spring.kafka.bootstrap-servers}")
    protected String bootstrapServers;

    /**
     * 创建基础的Kafka生产者配置
     *
     * @return 基础的Kafka生产者配置
     */
    protected Map<String, Object> createBaseProducerConfigs() {
        Map<String, Object> configProps = new HashMap<>();
        
        // 服务器地址
        configProps.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
        
        // 键序列化器
        configProps.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, getKeySerializerClass());
        
        // 确认模式
        configProps.put(ProducerConfig.ACKS_CONFIG, getAcks());
        
        // 重试次数
        configProps.put(ProducerConfig.RETRIES_CONFIG, getRetries());
        
        // 批处理大小
        configProps.put(ProducerConfig.BATCH_SIZE_CONFIG, getBatchSize());
        
        // 缓冲区大小
        configProps.put(ProducerConfig.BUFFER_MEMORY_CONFIG, getBufferMemory());
        
        // 压缩类型
        configProps.put(ProducerConfig.COMPRESSION_TYPE_CONFIG, getCompressionType());
        
        // 批处理延迟时间
        configProps.put(ProducerConfig.LINGER_MS_CONFIG, getLingerMs());
        
        // 请求超时时间
        configProps.put(ProducerConfig.REQUEST_TIMEOUT_MS_CONFIG, getRequestTimeoutMs());
        
        // 传递超时时间
        configProps.put(ProducerConfig.DELIVERY_TIMEOUT_MS_CONFIG, getDeliveryTimeoutMs());
        
        // 最大请求大小
        configProps.put(ProducerConfig.MAX_REQUEST_SIZE_CONFIG, getMaxRequestSize());
        
        // 启用Socket KeepAlive
        configProps.put(ProducerConfig.SOCKET_CONNECTION_SETUP_TIMEOUT_MS_CONFIG, 10000);
        configProps.put(ProducerConfig.SOCKET_CONNECTION_SETUP_TIMEOUT_MAX_MS_CONFIG, 30000);
        configProps.put("socket.keepalive.enable", true);
        
        // 启用幂等性发送
        configProps.put(ProducerConfig.ENABLE_IDEMPOTENCE_CONFIG, isEnableIdempotence());
        
        // 如果配置了事务ID前缀，则启用事务
        String transactionalId = getTransactionalId();
        if (transactionalId != null && !transactionalId.isEmpty()) {
            configProps.put(ProducerConfig.TRANSACTIONAL_ID_CONFIG, 
                    transactionalId + "-" + getModuleName() + "-" + System.currentTimeMillis());
        }
        
        // 添加自定义配置
        configProps.putAll(getAdditionalConfig());
        
        return configProps;
    }

    /**
     * 创建字符串序列化的Kafka生产者工厂
     *
     * @return 字符串序列化的Kafka生产者工厂
     */
    protected ProducerFactory<String, String> createStringProducerFactory() {
        Map<String, Object> configProps = createBaseProducerConfigs();
        
        // 值序列化器
        configProps.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        
        log.info("创建字符串序列化的Kafka生产者工厂: bootstrapServers={}, batchSize={}, lingerMs={}, compressionType={}", 
                bootstrapServers, getBatchSize(), getLingerMs(), getCompressionType());
        
        return new DefaultKafkaProducerFactory<>(configProps);
    }

    /**
     * 创建JSON序列化的Kafka生产者工厂
     *
     * @return JSON序列化的Kafka生产者工厂
     */
    protected ProducerFactory<String, Object> createJsonProducerFactory() {
        Map<String, Object> configProps = createBaseProducerConfigs();
        
        // JSON值序列化器
        configProps.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, JsonSerializer.class);
        
        log.info("创建JSON序列化的Kafka生产者工厂: bootstrapServers={}", bootstrapServers);
        
        return new DefaultKafkaProducerFactory<>(configProps);
    }

    /**
     * 创建Avro序列化的Kafka生产者工厂
     *
     * @return Avro序列化的Kafka生产者工厂
     */
    protected ProducerFactory<String, Object> createAvroProducerFactory() {
        Map<String, Object> configProps = createBaseProducerConfigs();
        
        // Avro值序列化器
        configProps.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, KafkaAvroSerializer.class);
        
        // Schema Registry URL
        configProps.put(AbstractKafkaSchemaSerDeConfig.SCHEMA_REGISTRY_URL_CONFIG, getSchemaRegistryUrl());
        
        log.info("创建Avro序列化的Kafka生产者工厂: bootstrapServers={}, schemaRegistryUrl={}", 
                bootstrapServers, getSchemaRegistryUrl());
        
        return new DefaultKafkaProducerFactory<>(configProps);
    }

    /**
     * 创建Kafka模板
     *
     * @param producerFactory 生产者工厂
     * @param defaultTopic    默认主题
     * @return Kafka模板
     */
    protected <K, V> KafkaTemplate<K, V> createKafkaTemplate(ProducerFactory<K, V> producerFactory, String defaultTopic) {
        KafkaTemplate<K, V> template = new KafkaTemplate<>(producerFactory);
        if (defaultTopic != null && !defaultTopic.isEmpty()) {
            template.setDefaultTopic(defaultTopic);
        }
        return template;
    }

    /**
     * 获取键序列化器类
     *
     * @return 键序列化器类
     */
    protected Class<?> getKeySerializerClass() {
        return StringSerializer.class;
    }

    /**
     * 获取确认模式
     *
     * @return 确认模式
     */
    protected String getAcks() {
        return kafkaConfig.getProducer().getAcks();
    }

    /**
     * 获取重试次数
     *
     * @return 重试次数
     */
    protected int getRetries() {
        return kafkaConfig.getProducer().getRetries();
    }

    /**
     * 获取批处理大小
     *
     * @return 批处理大小
     */
    protected int getBatchSize() {
        return kafkaConfig.getProducer().getBatchSize();
    }

    /**
     * 获取缓冲区大小
     *
     * @return 缓冲区大小
     */
    protected int getBufferMemory() {
        return kafkaConfig.getProducer().getBufferMemory();
    }

    /**
     * 获取压缩类型
     *
     * @return 压缩类型
     */
    protected String getCompressionType() {
        return kafkaConfig.getProducer().getCompressionType();
    }

    /**
     * 获取批处理延迟时间
     *
     * @return 批处理延迟时间
     */
    protected int getLingerMs() {
        return kafkaConfig.getProducer().getLingerMs();
    }

    /**
     * 获取请求超时时间
     *
     * @return 请求超时时间
     */
    protected int getRequestTimeoutMs() {
        return kafkaConfig.getProducer().getRequestTimeoutMs();
    }

    /**
     * 获取传递超时时间
     *
     * @return 传递超时时间
     */
    protected int getDeliveryTimeoutMs() {
        return kafkaConfig.getProducer().getDeliveryTimeoutMs();
    }

    /**
     * 获取最大请求大小
     *
     * @return 最大请求大小
     */
    protected int getMaxRequestSize() {
        return kafkaConfig.getProducer().getMaxRequestSize();
    }

    /**
     * 是否启用幂等性发送
     *
     * @return 是否启用幂等性发送
     */
    protected boolean isEnableIdempotence() {
        return kafkaConfig.getProducer().isEnableIdempotence();
    }

    /**
     * 获取事务ID前缀
     *
     * @return 事务ID前缀
     */
    protected String getTransactionalId() {
        return kafkaConfig.getProducer().getTransactionalId();
    }

    /**
     * 获取Schema Registry URL
     *
     * @return Schema Registry URL
     */
    protected String getSchemaRegistryUrl() {
        return kafkaConfig.getSchemaRegistryUrl();
    }

    /**
     * 获取模块名称，用于构建事务ID
     *
     * @return 模块名称
     */
    protected abstract String getModuleName();

    /**
     * 获取额外的配置
     *
     * @return 额外的配置
     */
    protected Map<String, Object> getAdditionalConfig() {
        return new HashMap<>();
    }
}