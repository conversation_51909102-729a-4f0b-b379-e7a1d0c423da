package com.crypto.trading.trade.service;

import com.crypto.trading.common.dto.trade.OrderDTO;
import com.crypto.trading.common.dto.trade.OrderStatusDTO;
import com.crypto.trading.common.enums.OrderStatus;
import com.crypto.trading.common.exception.BusinessException;
import com.crypto.trading.common.util.ThreadUtil;
import com.crypto.trading.trade.executor.BinanceTradeExecutor;
import com.crypto.trading.trade.model.entity.order.OrderEntity;
import com.crypto.trading.trade.producer.TradeResultProducer;
import com.crypto.trading.trade.service.order.OrderConverter;
import com.crypto.trading.trade.service.order.OrderManagementService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;

/**
 * 交易执行服务
 * <p>
 * 负责执行交易操作并发送交易结果
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
public class TradeExecutionService {

    private static final Logger log = LoggerFactory.getLogger(TradeExecutionService.class);

    /**
     * 交易结果生产者
     */
    @Autowired
    private TradeResultProducer tradeResultProducer;

    /**
     * 币安交易执行器
     */
    @Autowired
    private BinanceTradeExecutor binanceTradeExecutor;

    /**
     * 订单管理服务
     */
    @Autowired
    private OrderManagementService orderManagementService;

    /**
     * 执行市价单
     *
     * @param orderDTO 订单DTO
     * @return 执行结果的CompletableFuture
     */
    public CompletableFuture<OrderDTO> executeMarketOrder(OrderDTO orderDTO) {
        log.info("执行市价单: orderId={}, symbol={}, side={}, quantity={}",
                orderDTO.getOrderId(), orderDTO.getSymbol(), orderDTO.getSide(), orderDTO.getQuantity());

        // 转换为订单实体
        OrderEntity order = OrderConverter.toOrderEntity(orderDTO);

        // 使用虚拟线程执行
        ThreadFactory threadFactory = Thread.ofVirtual().factory();
        return CompletableFuture.supplyAsync(() -> {
            try {
                // 调用执行器执行市价单
                OrderEntity executedOrder = binanceTradeExecutor.executeMarketOrder(order)
                        .orTimeout(10, TimeUnit.SECONDS)
                        .join();

                // 转换为DTO返回
                return OrderConverter.toOrderDTO(executedOrder);
            } catch (Exception e) {
                log.error("执行市价单失败: orderId={}, error={}", orderDTO.getOrderId(), e.getMessage(), e);
                throw new BusinessException("执行市价单失败: " + e.getMessage());
            }
        }, ThreadUtil.toExecutor(threadFactory));
    }

    /**
     * 查询订单状态
     *
     * @param symbol        交易对
     * @param orderId       订单ID
     * @param clientOrderId 客户端订单ID
     * @return 订单状态的CompletableFuture
     */
    public CompletableFuture<OrderDTO> queryOrderStatus(String symbol, String orderId, String clientOrderId) {
        log.info("查询订单状态: symbol={}, orderId={}, clientOrderId={}", symbol, orderId, clientOrderId);

        // 使用虚拟线程执行
        ThreadFactory threadFactory = Thread.ofVirtual().factory();
        return CompletableFuture.supplyAsync(() -> {
            try {
                // 调用执行器查询订单状态
                Map<String, Object> orderStatus = binanceTradeExecutor.queryOrderStatus(symbol, orderId, clientOrderId)
                        .orTimeout(5, TimeUnit.SECONDS)
                        .join();

                // 根据查询结果更新订单
                OrderEntity orderEntity = orderManagementService.findOrder(orderId, clientOrderId);
                if (orderEntity == null) {
                    throw new BusinessException("订单不存在: orderId=" + orderId + ", clientOrderId=" + clientOrderId);
                }

                // 更新订单状态
                updateOrderStatusFromApiResponse(orderEntity, orderStatus);

                // 转换为DTO返回
                return OrderConverter.toOrderDTO(orderEntity);
            } catch (Exception e) {
                log.error("查询订单状态失败: symbol={}, orderId={}, clientOrderId={}, error={}",
                        symbol, orderId, clientOrderId, e.getMessage(), e);
                throw new BusinessException("查询订单状态失败: " + e.getMessage());
            }
        }, ThreadUtil.toExecutor(threadFactory));
    }

    /**
     * 取消订单
     *
     * @param orderId       订单ID
     * @param clientOrderId 客户端订单ID
     * @return 取消结果的CompletableFuture
     */
    public CompletableFuture<OrderDTO> cancelOrder(String orderId, String clientOrderId) {
        log.info("取消订单: orderId={}, clientOrderId={}", orderId, clientOrderId);

        // 使用虚拟线程执行
        ThreadFactory threadFactory = Thread.ofVirtual().factory();
        return CompletableFuture.supplyAsync(() -> {
            try {
                // 查询订单
                OrderEntity orderEntity = orderManagementService.findOrder(orderId, clientOrderId);
                if (orderEntity == null) {
                    throw new BusinessException("订单不存在: orderId=" + orderId + ", clientOrderId=" + clientOrderId);
                }

                // 检查订单状态是否可取消
                if (OrderStatus.isFinalStatus(orderEntity.getStatus())) {
                    throw new BusinessException("订单已处于最终状态，无法取消: orderId=" + orderId + ", status=" + orderEntity.getStatus());
                }

                // 调用执行器取消订单
                OrderEntity canceledOrder = binanceTradeExecutor.cancelOrder(orderEntity)
                        .orTimeout(5, TimeUnit.SECONDS)
                        .join();

                // 转换为DTO返回
                return OrderConverter.toOrderDTO(canceledOrder);
            } catch (Exception e) {
                log.error("取消订单失败: orderId={}, clientOrderId={}, error={}",
                        orderId, clientOrderId, e.getMessage(), e);
                throw new BusinessException("取消订单失败: " + e.getMessage());
            }
        }, ThreadUtil.toExecutor(threadFactory));
    }

    /**
     * 处理订单执行结果
     *
     * @param orderDTO 订单DTO
     * @return 处理结果的CompletableFuture
     */
    public CompletableFuture<Void> processOrderExecutionResult(OrderDTO orderDTO) {
        log.info("处理订单执行结果: orderId={}, symbol={}, status={}", 
                orderDTO.getOrderId(), orderDTO.getSymbol(), orderDTO.getStatus());
        
        // 发送订单执行结果到Kafka
        return tradeResultProducer.sendOrderExecutionResult(orderDTO)
                .exceptionally(ex -> {
                    log.error("发送订单执行结果失败: orderId={}, error={}", 
                            orderDTO.getOrderId(), ex.getMessage(), ex);
                    return null;
                });
    }

    /**
     * 处理订单状态更新
     *
     * @param orderStatusDTO 订单状态DTO
     * @return 处理结果的CompletableFuture
     */
    public CompletableFuture<Void> processOrderStatusUpdate(OrderStatusDTO orderStatusDTO) {
        log.info("处理订单状态更新: orderId={}, symbol={}, status={}", 
                orderStatusDTO.getOrderId(), orderStatusDTO.getSymbol(), orderStatusDTO.getStatus());
        
        // 发送订单状态更新到Kafka
        return tradeResultProducer.sendOrderStatusUpdate(orderStatusDTO)
                .exceptionally(ex -> {
                    log.error("发送订单状态更新失败: orderId={}, error={}", 
                            orderStatusDTO.getOrderId(), ex.getMessage(), ex);
                    return null;
                });
    }

    /**
     * 处理订单执行错误
     *
     * @param clientOrderId 客户端订单ID
     * @param symbol        交易对
     * @param errorCode     错误代码
     * @param errorMessage  错误消息
     * @return 处理结果的CompletableFuture
     */
    public CompletableFuture<Void> processOrderExecutionError(String clientOrderId, String symbol, int errorCode, String errorMessage) {
        log.error("处理订单执行错误: clientOrderId={}, symbol={}, errorCode={}, errorMessage={}", 
                clientOrderId, symbol, errorCode, errorMessage);
        
        // 发送订单执行错误到Kafka
        return tradeResultProducer.sendOrderExecutionError(clientOrderId, symbol, errorCode, errorMessage)
                .exceptionally(ex -> {
                    log.error("发送订单执行错误失败: clientOrderId={}, error={}", 
                            clientOrderId, ex.getMessage(), ex);
                    return null;
                });
    }

    /**
     * 根据API响应更新订单状态
     *
     * @param orderEntity 订单实体
     * @param orderStatus API返回的订单状态
     */
    private void updateOrderStatusFromApiResponse(OrderEntity orderEntity, Map<String, Object> orderStatus) {
        // 提取状态信息
        String status = (String) orderStatus.get("status");
        String executedQty = (String) orderStatus.get("executedQty");
        String price = (String) orderStatus.get("price");

        // 根据API返回的状态更新订单
        OrderStatus orderStatusEnum = OrderStatus.fromString(status);
        orderEntity.setStatus(orderStatusEnum.getCode());

        // 如果有成交数量和价格，更新订单
        if (executedQty != null && !executedQty.isEmpty()) {
            orderEntity.setExecutedQuantity(Double.parseDouble(executedQty));
        }

        if (price != null && !price.isEmpty()) {
            orderEntity.setExecutedPrice(Double.parseDouble(price));
        }

        // 更新时间
        orderEntity.setUpdatedTime(System.currentTimeMillis());

        // 更新数据库
        orderManagementService.updateOrderStatus(
                orderEntity.getOrderId(),
                orderEntity.getStatus(),
                "订单状态更新",
                orderStatus.toString());
    }
}