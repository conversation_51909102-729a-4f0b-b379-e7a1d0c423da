package com.crypto.trading.trade.consumer;

import com.crypto.trading.trade.model.avro.OrderType;
import com.crypto.trading.trade.model.avro.SignalType;
import com.crypto.trading.trade.model.avro.TimeInForce;
import com.crypto.trading.trade.model.avro.TradingSignal;
import com.crypto.trading.trade.model.result.SignalProcessResult;
import com.crypto.trading.trade.service.SignalProcessingService;
import com.crypto.trading.trade.service.SignalValidationService;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.kafka.support.Acknowledgment;

import java.util.HashMap;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * 交易信号消费者测试类
 */
public class TradingSignalConsumerTest {

    @Mock
    private SignalValidationService signalValidationService;

    @Mock
    private SignalProcessingService signalProcessingService;

    @Mock
    private Acknowledgment acknowledgment;

    @InjectMocks
    private TradingSignalConsumer tradingSignalConsumer;

    private TradingSignal createValidSignal() {
        return TradingSignal.newBuilder()
            .setId("test-signal-1")
            .setSymbol("BTCUSDT")
            .setStrategy("test-strategy")
            .setSignalType(SignalType.BUY)
            .setConfidence(0.8)
            .setOrderType(OrderType.MARKET)
            .setTimeInForce(TimeInForce.GTC)
            .setGeneratedTime(System.currentTimeMillis())
            .setAdditionalParams(new HashMap<>())
            .build();
    }

    @BeforeEach
    public void setup() {
        MockitoAnnotations.openMocks(this);
        tradingSignalConsumer = new TradingSignalConsumer();
        tradingSignalConsumer = spy(tradingSignalConsumer);

        // 注入Mock对象
        setField(tradingSignalConsumer, "signalValidationService", signalValidationService);
        setField(tradingSignalConsumer, "signalProcessingService", signalProcessingService);
        setField(tradingSignalConsumer, "strategySignalTopic", "strategy.signal");
        setField(tradingSignalConsumer, "consumerGroupId", "test-consumer-group");
    }

    @Test
    public void testConsumeValidSignal() {
        // 准备数据
        TradingSignal signal = createValidSignal();
        ConsumerRecord<String, TradingSignal> record = 
                new ConsumerRecord<>("strategy.signal", 0, 0, "key1", signal);

        // 配置Mock行为
        SignalValidationService.ValidationResult validationResult = 
                new SignalValidationService.ValidationResult(true, null, signal, "test-validation-id");
        when(signalValidationService.validateSignal(any())).thenReturn(validationResult);

        SignalProcessResult processResult = 
                new SignalProcessResult(true, "处理成功", null);
        when(signalProcessingService.processSignal(any())).thenReturn(processResult);

        // 执行
        tradingSignalConsumer.consume(record, acknowledgment);

        // 验证
        verify(signalValidationService, times(1)).validateSignal(signal);
        verify(signalProcessingService, times(1)).processSignal(validationResult);
        verify(acknowledgment, times(1)).acknowledge();
        
        // 验证计数器
        assertEquals(1, tradingSignalConsumer.getTotalMessages());
        assertEquals(1, tradingSignalConsumer.getValidMessages());
        assertEquals(0, tradingSignalConsumer.getInvalidMessages());
        assertEquals(1, tradingSignalConsumer.getProcessedMessages());
        assertEquals(0, tradingSignalConsumer.getFailedMessages());
    }

    @Test
    public void testConsumeInvalidSignal() {
        // 准备数据
        TradingSignal signal = createValidSignal();
        ConsumerRecord<String, TradingSignal> record = 
                new ConsumerRecord<>("strategy.signal", 0, 0, "key1", signal);

        // 配置Mock行为
        SignalValidationService.ValidationResult validationResult = 
                new SignalValidationService.ValidationResult(false, "无效的信号", signal, "test-validation-id");
        when(signalValidationService.validateSignal(any())).thenReturn(validationResult);

        // 执行
        tradingSignalConsumer.consume(record, acknowledgment);

        // 验证
        verify(signalValidationService, times(1)).validateSignal(signal);
        verify(signalProcessingService, never()).processSignal(any());
        verify(acknowledgment, times(1)).acknowledge();
        
        // 验证计数器
        assertEquals(1, tradingSignalConsumer.getTotalMessages());
        assertEquals(0, tradingSignalConsumer.getValidMessages());
        assertEquals(1, tradingSignalConsumer.getInvalidMessages());
        assertEquals(0, tradingSignalConsumer.getProcessedMessages());
        assertEquals(0, tradingSignalConsumer.getFailedMessages());
    }

    @Test
    public void testConsumeNullSignal() {
        // 准备数据
        ConsumerRecord<String, TradingSignal> record = 
                new ConsumerRecord<>("strategy.signal", 0, 0, "key1", null);

        // 执行
        tradingSignalConsumer.consume(record, acknowledgment);

        // 验证
        verify(signalValidationService, never()).validateSignal(any());
        verify(signalProcessingService, never()).processSignal(any());
        verify(acknowledgment, times(1)).acknowledge();
        
        // 验证计数器
        assertEquals(1, tradingSignalConsumer.getTotalMessages());
        assertEquals(0, tradingSignalConsumer.getValidMessages());
        assertEquals(1, tradingSignalConsumer.getInvalidMessages());
        assertEquals(0, tradingSignalConsumer.getProcessedMessages());
        assertEquals(0, tradingSignalConsumer.getFailedMessages());
    }

    @Test
    public void testConsumeWithProcessingException() {
        // 准备数据
        TradingSignal signal = createValidSignal();
        ConsumerRecord<String, TradingSignal> record = 
                new ConsumerRecord<>("strategy.signal", 0, 0, "key1", signal);

        // 配置Mock行为
        SignalValidationService.ValidationResult validationResult = 
                new SignalValidationService.ValidationResult(true, null, signal, "test-validation-id");
        when(signalValidationService.validateSignal(any())).thenReturn(validationResult);

        when(signalProcessingService.processSignal(any())).thenThrow(new RuntimeException("处理异常"));

        // 执行
        tradingSignalConsumer.consume(record, acknowledgment);

        // 验证
        verify(signalValidationService, times(1)).validateSignal(signal);
        verify(signalProcessingService, times(1)).processSignal(validationResult);
        verify(acknowledgment, times(1)).acknowledge();
        
        // 验证计数器
        assertEquals(1, tradingSignalConsumer.getTotalMessages());
        assertEquals(1, tradingSignalConsumer.getValidMessages());
        assertEquals(0, tradingSignalConsumer.getInvalidMessages());
        assertEquals(0, tradingSignalConsumer.getProcessedMessages());
        assertEquals(1, tradingSignalConsumer.getFailedMessages());
    }

    // 工具方法：通过反射设置私有字段
    private void setField(Object target, String fieldName, Object value) {
        try {
            java.lang.reflect.Field field;
            try {
                // 先尝试在当前类中查找字段
                field = target.getClass().getDeclaredField(fieldName);
            } catch (NoSuchFieldException e) {
                // 如果当前类中没有找到，尝试在父类中查找
                field = target.getClass().getSuperclass().getDeclaredField(fieldName);
            }
            field.setAccessible(true);
            field.set(target, value);
        } catch (Exception e) {
            throw new RuntimeException("无法设置字段: " + fieldName, e);
        }
    }
}