package com.crypto.trading.trade.producer;

import com.crypto.trading.common.avro.OrderExecutionResult;
import com.crypto.trading.common.dto.trade.OrderDTO;
import com.crypto.trading.common.dto.trade.OrderStatusDTO;
import com.crypto.trading.trade.producer.impl.KafkaTradeResultProducer;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.concurrent.CompletableFuture;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Kafka交易结果生产者测试类
 */
public class KafkaTradeResultProducerTest {

    @Mock
    private KafkaTemplate<String, String> kafkaTemplate;

    @InjectMocks
    private KafkaTradeResultProducer tradeResultProducer;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        ReflectionTestUtils.setField(tradeResultProducer, "orderExecutionResultTopic", "trade.result");
        ReflectionTestUtils.setField(tradeResultProducer, "orderStatusUpdateTopic", "order.status");
        ReflectionTestUtils.setField(tradeResultProducer, "orderExecutionErrorTopic", "trade.result");
    }

    @Test
    public void testSendOrderExecutionResult_Success() {
        // 准备测试数据
        OrderDTO orderDTO = createTestOrderDTO();
        
        // 模拟Kafka发送成功
        CompletableFuture<SendResult<String, String>> future = new CompletableFuture<>();
        SendResult<String, String> result = mock(SendResult.class);
        when(result.getRecordMetadata()).thenReturn(mock(org.apache.kafka.clients.producer.RecordMetadata.class));
        future.complete(result);
        when(kafkaTemplate.send(anyString(), anyString(), anyString())).thenReturn(future);
        
        // 执行测试
        CompletableFuture<Void> resultFuture = tradeResultProducer.sendOrderExecutionResult(orderDTO);
        
        // 等待异步操作完成
        try {
            resultFuture.get();
        } catch (Exception e) {
            fail("Should not throw exception: " + e.getMessage());
        }
        
        // 验证Kafka模板被正确调用
        verify(kafkaTemplate).send(eq("trade.result"), eq(orderDTO.getOrderId()), anyString());
    }

    @Test
    public void testSendOrderStatusUpdate_Success() {
        // 准备测试数据
        OrderStatusDTO orderStatusDTO = createTestOrderStatusDTO();
        
        // 模拟Kafka发送成功
        CompletableFuture<SendResult<String, String>> future = new CompletableFuture<>();
        SendResult<String, String> result = mock(SendResult.class);
        when(result.getRecordMetadata()).thenReturn(mock(org.apache.kafka.clients.producer.RecordMetadata.class));
        future.complete(result);
        when(kafkaTemplate.send(anyString(), anyString(), anyString())).thenReturn(future);
        
        // 执行测试
        CompletableFuture<Void> resultFuture = tradeResultProducer.sendOrderStatusUpdate(orderStatusDTO);
        
        // 等待异步操作完成
        try {
            resultFuture.get();
        } catch (Exception e) {
            fail("Should not throw exception: " + e.getMessage());
        }
        
        // 验证Kafka模板被正确调用
        verify(kafkaTemplate).send(eq("order.status"), eq(orderStatusDTO.getOrderId()), anyString());
    }

    @Test
    public void testSendOrderExecutionError_Success() {
        // 准备测试数据
        String clientOrderId = "TEST123";
        String symbol = "BTCUSDT";
        int errorCode = 400;
        String errorMessage = "Invalid parameter";
        
        // 模拟Kafka发送成功
        CompletableFuture<SendResult<String, String>> future = new CompletableFuture<>();
        SendResult<String, String> result = mock(SendResult.class);
        when(result.getRecordMetadata()).thenReturn(mock(org.apache.kafka.clients.producer.RecordMetadata.class));
        future.complete(result);
        when(kafkaTemplate.send(anyString(), anyString(), anyString())).thenReturn(future);
        
        // 执行测试
        CompletableFuture<Void> resultFuture = tradeResultProducer.sendOrderExecutionError(clientOrderId, symbol, errorCode, errorMessage);
        
        // 等待异步操作完成
        try {
            resultFuture.get();
        } catch (Exception e) {
            fail("Should not throw exception: " + e.getMessage());
        }
        
        // 验证Kafka模板被正确调用
        verify(kafkaTemplate).send(eq("trade.result"), eq(clientOrderId), anyString());
    }

    @Test
    public void testSendBatchOrderExecutionResults_Success() {
        // 准备测试数据
        OrderDTO orderDTO1 = createTestOrderDTO();
        OrderDTO orderDTO2 = createTestOrderDTO();
        orderDTO2.setOrderId("2");
        
        // 模拟Kafka发送成功
        CompletableFuture<SendResult<String, String>> future = new CompletableFuture<>();
        SendResult<String, String> result = mock(SendResult.class);
        when(result.getRecordMetadata()).thenReturn(mock(org.apache.kafka.clients.producer.RecordMetadata.class));
        future.complete(result);
        when(kafkaTemplate.send(anyString(), anyString(), anyString())).thenReturn(future);
        
        // 执行测试
        CompletableFuture<Void> resultFuture = tradeResultProducer.sendBatchOrderExecutionResults(Arrays.asList(orderDTO1, orderDTO2));
        
        // 等待异步操作完成
        try {
            resultFuture.get();
        } catch (Exception e) {
            fail("Should not throw exception: " + e.getMessage());
        }
        
        // 验证Kafka模板被正确调用两次
        verify(kafkaTemplate, times(2)).send(eq("trade.result"), anyString(), anyString());
    }

    @Test
    public void testSendOrderExecutionResult_Failure() {
        // 准备测试数据
        OrderDTO orderDTO = createTestOrderDTO();
        
        // 模拟Kafka发送失败
        CompletableFuture<SendResult<String, String>> future = new CompletableFuture<>();
        future.completeExceptionally(new RuntimeException("订单执行结果发送失败"));
        when(kafkaTemplate.send(anyString(), anyString(), anyString())).thenReturn(future);
        
        // 执行测试
        CompletableFuture<Void> resultFuture = tradeResultProducer.sendOrderExecutionResult(orderDTO);
        
        // 验证异步操作失败
        try {
            resultFuture.get();
            fail("Should throw exception");
        } catch (Exception e) {
            assertTrue(e.getCause() instanceof RuntimeException);
            assertEquals("订单执行结果发送失败", e.getCause().getMessage());
        }
    }

    /**
     * 创建测试用的OrderDTO
     *
     * @return OrderDTO
     */
    private OrderDTO createTestOrderDTO() {
        OrderDTO orderDTO = new OrderDTO();
        orderDTO.setOrderId("1");
        orderDTO.setSymbol("BTCUSDT");
        orderDTO.setClientOrderId("TEST123");
        orderDTO.setSide("BUY");
        orderDTO.setPositionSide("BOTH");
        orderDTO.setType("MARKET");
        orderDTO.setQuantity(0.1);
        orderDTO.setPrice(50000.0);
        orderDTO.setExecutedQuantity(0.1);
        orderDTO.setExecutedPrice(50000.0);
        orderDTO.setStatus("FILLED");
        orderDTO.setStrategyId("test_strategy_001");
        orderDTO.setErrorCode(null);
        orderDTO.setErrorMessage(null);
        orderDTO.setCreatedTime(System.currentTimeMillis());
        orderDTO.setUpdatedTime(System.currentTimeMillis());
        orderDTO.setExecutedTime(System.currentTimeMillis());
        orderDTO.setRemark("测试订单");
        return orderDTO;
    }

    /**
     * 创建测试用的OrderStatusDTO
     *
     * @return OrderStatusDTO
     */
    private OrderStatusDTO createTestOrderStatusDTO() {
        OrderStatusDTO orderStatusDTO = new OrderStatusDTO();
        orderStatusDTO.setOrderId("1");
        orderStatusDTO.setSymbol("BTCUSDT");
        orderStatusDTO.setClientOrderId("TEST123");
        orderStatusDTO.setStatus("FILLED");
        orderStatusDTO.setExecutedQuantity(0.1);
        orderStatusDTO.setExecutedPrice(50000.0);
        orderStatusDTO.setErrorCode(null);
        orderStatusDTO.setErrorMessage(null);
        orderStatusDTO.setUpdatedTime(System.currentTimeMillis());
        orderStatusDTO.setRawData("{\"orderId\":1,\"status\":\"FILLED\"}");
        return orderStatusDTO;
    }
}