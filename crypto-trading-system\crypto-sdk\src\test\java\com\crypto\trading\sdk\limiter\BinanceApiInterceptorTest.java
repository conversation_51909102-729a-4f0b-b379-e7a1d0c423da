package com.crypto.trading.sdk.limiter;

import com.crypto.trading.common.constant.APILimitConstants;
import okhttp3.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 币安API拦截器测试类
 */
@ExtendWith(MockitoExtension.class)
public class BinanceApiInterceptorTest {

    @Mock
    private RateLimiter rateLimiter;

    @Mock
    private ResponseHeaderRateLimitUpdater headerUpdater;

    @Mock
    private Interceptor.Chain chain;

    private BinanceApiInterceptor interceptor;

    @BeforeEach
    public void setUp() {
        interceptor = new BinanceApiInterceptor(rateLimiter, headerUpdater);
    }

    @Test
    public void testIntercept_MarketDataRequest() throws IOException {
        // 准备测试数据
        String url = "https://fapi.binance.com/fapi/v1/depth?symbol=BTCUSDT&limit=100";
        Request request = new Request.Builder().url(url).build();
        
        // 模拟链式调用
        when(chain.request()).thenReturn(request);
        Response mockResponse = new Response.Builder()
                .request(request)
                .protocol(Protocol.HTTP_1_1)
                .code(200)
                .message("OK")
                .body(ResponseBody.create("", MediaType.parse("application/json")))
                .build();
        when(chain.proceed(any(Request.class))).thenReturn(mockResponse);
        
        // 执行测试
        Response response = interceptor.intercept(chain);
        
        // 验证结果
        assertNotNull(response, "响应不应该为空");
        assertEquals(200, response.code(), "响应状态码应该是200");
        
        // 验证限流器被正确调用
        verify(rateLimiter).getUsageRate(eq("depth"));
        
        // 验证权重计算正确
        ArgumentCaptor<Integer> weightCaptor = ArgumentCaptor.forClass(Integer.class);
        verify(rateLimiter).getUsageRate(eq("depth"));
        
        // 验证响应头处理器被调用
        verify(headerUpdater).processResponseHeaders(anyMap(), eq("depth"));
    }

    @Test
    public void testIntercept_HeavyDepthRequest() throws IOException {
        // 准备测试数据 - 大深度请求
        String url = "https://fapi.binance.com/fapi/v1/depth?symbol=BTCUSDT&limit=1000";
        Request request = new Request.Builder().url(url).build();
        
        // 模拟链式调用
        when(chain.request()).thenReturn(request);
        Response mockResponse = new Response.Builder()
                .request(request)
                .protocol(Protocol.HTTP_1_1)
                .code(200)
                .message("OK")
                .body(ResponseBody.create("", MediaType.parse("application/json")))
                .build();
        when(chain.proceed(any(Request.class))).thenReturn(mockResponse);
        
        // 执行测试
        Response response = interceptor.intercept(chain);
        
        // 验证结果
        assertNotNull(response, "响应不应该为空");
        
        // 验证重型请求的权重计算
        verify(rateLimiter).getUsageRate(eq("depth"));
    }

    @Test
    public void testIntercept_OrderRequest() throws IOException {
        // 准备测试数据 - 下单请求
        String url = "https://fapi.binance.com/fapi/v1/order";
        Request request = new Request.Builder()
                .url(url)
                .method("POST", RequestBody.create("{}", MediaType.parse("application/json")))
                .build();
        
        // 模拟链式调用
        when(chain.request()).thenReturn(request);
        Response mockResponse = new Response.Builder()
                .request(request)
                .protocol(Protocol.HTTP_1_1)
                .code(200)
                .message("OK")
                .body(ResponseBody.create("", MediaType.parse("application/json")))
                .build();
        when(chain.proceed(any(Request.class))).thenReturn(mockResponse);
        
        // 执行测试
        Response response = interceptor.intercept(chain);
        
        // 验证结果
        assertNotNull(response, "响应不应该为空");
        
        // 验证下单请求的权重计算
        verify(rateLimiter).getUsageRate(eq("order"));
    }

    @Test
    public void testIntercept_HighUsageRate() throws IOException {
        // 准备测试数据
        String url = "https://fapi.binance.com/fapi/v1/depth?symbol=BTCUSDT";
        Request request = new Request.Builder().url(url).build();
        
        // 模拟高使用率
        when(rateLimiter.getUsageRate(anyString())).thenReturn(0.95); // 95%的使用率，超过警告阈值
        
        // 模拟链式调用
        when(chain.request()).thenReturn(request);
        Response mockResponse = new Response.Builder()
                .request(request)
                .protocol(Protocol.HTTP_1_1)
                .code(200)
                .message("OK")
                .body(ResponseBody.create("", MediaType.parse("application/json")))
                .build();
        when(chain.proceed(any(Request.class))).thenReturn(mockResponse);
        
        // 记录开始时间
        long startTime = System.currentTimeMillis();
        
        // 执行测试
        Response response = interceptor.intercept(chain);
        
        // 计算执行时间
        long executionTime = System.currentTimeMillis() - startTime;
        
        // 验证结果
        assertNotNull(response, "响应不应该为空");
        
        // 验证高使用率情况下应该有延迟
        assertTrue(executionTime > 0, "高使用率情况下应该有延迟");
    }

    @Test
    public void testDetermineRequestWeight_VariousEndpoints() throws IOException {
        // 测试不同端点的权重计算
        testEndpointWeight("https://fapi.binance.com/fapi/v1/depth?symbol=BTCUSDT&limit=100", "GET", 
                APILimitConstants.BINANCE_FUTURES_WEIGHT_DEPTH_DEFAULT);
        
        testEndpointWeight("https://fapi.binance.com/fapi/v1/depth?symbol=BTCUSDT&limit=1000", "GET", 
                APILimitConstants.BINANCE_FUTURES_WEIGHT_DEPTH_HEAVY);
        
        testEndpointWeight("https://fapi.binance.com/fapi/v1/klines?symbol=BTCUSDT&interval=1m", "GET", 
                APILimitConstants.BINANCE_FUTURES_WEIGHT_KLINES);
        
        testEndpointWeight("https://fapi.binance.com/fapi/v1/account", "GET", 
                APILimitConstants.BINANCE_FUTURES_WEIGHT_ACCOUNT_INFO);
        
        testEndpointWeight("https://fapi.binance.com/fapi/v1/order", "POST", 
                APILimitConstants.BINANCE_FUTURES_WEIGHT_ORDER);
        
        testEndpointWeight("https://fapi.binance.com/fapi/v1/order", "DELETE", 
                APILimitConstants.BINANCE_FUTURES_WEIGHT_CANCEL_ORDER);
        
        testEndpointWeight("https://fapi.binance.com/fapi/v1/allOpenOrders", "DELETE", 
                APILimitConstants.BINANCE_FUTURES_WEIGHT_CANCEL_ALL_ORDERS);
        
        testEndpointWeight("https://fapi.binance.com/fapi/v1/position", "GET", 
                APILimitConstants.BINANCE_FUTURES_WEIGHT_POSITION_INFO);
    }

    /**
     * 测试特定端点的权重计算
     * 
     * @param url 请求URL
     * @param method 请求方法
     * @param expectedWeight 预期权重
     * @throws IOException 如果发生IO异常
     */
    private void testEndpointWeight(String url, String method, int expectedWeight) throws IOException {
        // 准备测试数据
        Request.Builder requestBuilder = new Request.Builder().url(url);
        if ("POST".equals(method) || "DELETE".equals(method)) {
            requestBuilder.method(method, RequestBody.create("{}", MediaType.parse("application/json")));
        }
        Request request = requestBuilder.build();
        
        // 模拟链式调用
        when(chain.request()).thenReturn(request);
        Response mockResponse = new Response.Builder()
                .request(request)
                .protocol(Protocol.HTTP_1_1)
                .code(200)
                .message("OK")
                .body(ResponseBody.create("", MediaType.parse("application/json")))
                .build();
        when(chain.proceed(any(Request.class))).thenReturn(mockResponse);
        
        // 执行测试
        interceptor.intercept(chain);
        
        // 提取端点名称
        String endpoint = url.substring(url.lastIndexOf('/') + 1);
        if (endpoint.contains("?")) {
            endpoint = endpoint.substring(0, endpoint.indexOf('?'));
        }
        
        // 验证使用了正确的权重
        verify(rateLimiter, atLeastOnce()).getUsageRate(eq(endpoint));
    }

    @Test
    public void testProcessResponseHeaders() throws IOException {
        // 准备测试数据
        String url = "https://fapi.binance.com/fapi/v1/depth?symbol=BTCUSDT";
        Request request = new Request.Builder().url(url).build();
        
        // 创建带有限流头的响应
        Response mockResponse = new Response.Builder()
                .request(request)
                .protocol(Protocol.HTTP_1_1)
                .code(200)
                .message("OK")
                .header("X-MBX-USED-WEIGHT-1M", "100")
                .header("X-MBX-ORDER-COUNT-1M", "10")
                .body(ResponseBody.create("", MediaType.parse("application/json")))
                .build();
        
        // 模拟链式调用
        when(chain.request()).thenReturn(request);
        when(chain.proceed(any(Request.class))).thenReturn(mockResponse);
        
        // 执行测试
        interceptor.intercept(chain);
        
        // 验证响应头处理器被正确调用
        ArgumentCaptor<Map<String, String>> headersCaptor = ArgumentCaptor.forClass(Map.class);
        verify(headerUpdater).processResponseHeaders(headersCaptor.capture(), eq("depth"));
        
        // 验证捕获的响应头
        Map<String, String> capturedHeaders = headersCaptor.getValue();
        assertEquals("100", capturedHeaders.get("x-mbx-used-weight-1m"), "应该捕获到正确的权重头");
        assertEquals("10", capturedHeaders.get("x-mbx-order-count-1m"), "应该捕获到正确的订单计数头");
    }
}