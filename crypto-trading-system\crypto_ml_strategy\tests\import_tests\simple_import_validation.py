#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
简化导入验证测试

专门测试修复后的导入路径，避免实例化对象导致的阻塞问题
"""

import sys
import os
import time

# 添加src目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_import_only(module_path, class_name, test_name):
    """
    仅测试导入，不实例化对象
    
    Args:
        module_path: 模块路径
        class_name: 类名
        test_name: 测试名称
    
    Returns:
        (success, error_message)
    """
    try:
        start_time = time.time()
        
        # 动态导入
        module = __import__(module_path, fromlist=[class_name])
        cls = getattr(module, class_name)
        
        import_time = (time.time() - start_time) * 1000
        
        print(f"✅ {test_name}: 导入成功 ({import_time:.1f}ms)")
        return True, None
        
    except Exception as e:
        print(f"❌ {test_name}: 导入失败 - {str(e)}")
        return False, str(e)

def main():
    """主测试函数"""
    print("开始简化导入验证测试")
    print("=" * 60)
    
    # 定义测试用例
    test_cases = [
        # (模块路径, 类名, 测试名称)
        ("data.cache", "CacheIntegration", "CacheIntegration导入"),
        ("data.quality.data_validator", "DataValidator", "DataValidator导入"),
        ("data.quality", "DataQualityCore", "DataQualityCore导入"),
        ("data.clients.influxdb_client", "InfluxDBClient", "InfluxDBClient导入"),
        ("data.clients.mysql_client", "MySQLClient", "MySQLClient导入"),
        ("data.data_processor", "DataProcessor", "DataProcessor导入"),
        ("core.config", "Config", "Config导入"),
        ("api.kafka_client", "KafkaClient", "KafkaClient导入"),
    ]
    
    results = {}
    start_time = time.time()
    
    # 执行测试
    for module_path, class_name, test_name in test_cases:
        success, error = test_import_only(module_path, class_name, test_name)
        results[test_name] = {
            'success': success,
            'error': error,
            'module': module_path,
            'class': class_name
        }
    
    total_time = time.time() - start_time
    
    # 生成报告
    print("\n" + "=" * 60)
    print("导入验证测试报告")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result['success'] else "❌ 失败"
        print(f"{test_name}: {status}")
        if not result['success']:
            print(f"   错误: {result['error']}")
        if result['success']:
            passed += 1
    
    print(f"\n总体结果: {passed}/{total} 导入测试通过")
    print(f"测试耗时: {total_time:.2f}秒")
    
    # 详细分析
    if passed < total:
        print(f"\n失败分析:")
        for test_name, result in results.items():
            if not result['success']:
                print(f"- {test_name}: {result['module']}.{result['class']}")
                print(f"  错误: {result['error']}")
    
    success_rate = passed / total
    if success_rate >= 1.0:
        print("🎉 所有导入测试通过！导入路径修复完全成功！")
        return True
    elif success_rate >= 0.8:
        print("⚠️ 大部分导入测试通过，少数问题需要修复")
        return True
    else:
        print("❌ 多个导入问题需要修复")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)