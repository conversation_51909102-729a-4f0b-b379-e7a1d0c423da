package com.crypto.trading.market.processor;

import com.crypto.trading.common.dto.market.DepthDataDTO;
import com.crypto.trading.market.converter.MarketDataConverter;
import com.crypto.trading.market.producer.AsyncMarketDataProducer;
import com.crypto.trading.sdk.response.model.DepthData;
import com.crypto.trading.sdk.response.model.WebSocketMessage;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.quality.Strictness;
import org.mockito.junit.jupiter.MockitoSettings;
import org.springframework.kafka.support.SendResult;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * DepthDataProcessor单元测试
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class DepthDataProcessorTest {

    @Mock
    private AsyncMarketDataProducer asyncMarketDataProducer;

    @Mock
    private MarketDataConverter marketDataConverter;

    @InjectMocks
    private DepthDataProcessor depthDataProcessor;

    private WebSocketMessage<DepthData> depthMessage;
    private DepthDataDTO depthDataDTO;

    @BeforeEach
    void setUp() throws Exception {
        // 准备测试数据
        DepthData depthData = new DepthData();

        // 创建WebSocketMessage包装DepthData
        depthMessage = new WebSocketMessage<>("depth", 1622535659999L, "BTCUSDT", depthData);

        // 创建DepthDataDTO
        List<DepthDataDTO.PriceQuantity> bids = new ArrayList<>();
        bids.add(new DepthDataDTO.PriceQuantity(new BigDecimal("34950.00"), new BigDecimal("1.5")));
        bids.add(new DepthDataDTO.PriceQuantity(new BigDecimal("34900.00"), new BigDecimal("2.5")));

        List<DepthDataDTO.PriceQuantity> asks = new ArrayList<>();
        asks.add(new DepthDataDTO.PriceQuantity(new BigDecimal("35050.00"), new BigDecimal("1.0")));
        asks.add(new DepthDataDTO.PriceQuantity(new BigDecimal("35100.00"), new BigDecimal("2.0")));

        depthDataDTO = new DepthDataDTO(
            "BTCUSDT",
            123456789L,
            20,
            bids,
            asks,
            LocalDateTime.ofInstant(Instant.ofEpochMilli(1622535659999L), ZoneId.systemDefault())
        );

        // 配置mock行为
        doReturn(depthDataDTO).when(marketDataConverter).convertToOrderBookDTO(any(DepthData.class), eq("BTCUSDT"));

        // 配置AsyncMarketDataProducer返回成功的CompletableFuture
        CompletableFuture<SendResult<String, Object>> successFuture = CompletableFuture.completedFuture(null);
        doReturn(successFuture).when(asyncMarketDataProducer).sendDepthRawData(any(DepthDataDTO.class));
    }

    @Test
    void testProcessDepthData() throws Exception {
        // 调用被测试方法
        depthDataProcessor.processDepthData(depthMessage);

        // 验证发送到Kafka
        verify(asyncMarketDataProducer).sendDepthRawData(eq(depthDataDTO));
    }

    @Test
    void testProcessDepthDataWithKafkaException() throws Exception {
        // 配置mock抛出异常
        CompletableFuture<SendResult<String, Object>> failedFuture = new CompletableFuture<>();
        failedFuture.completeExceptionally(new RuntimeException("Kafka error"));
        doReturn(failedFuture).when(asyncMarketDataProducer).sendDepthRawData(any(DepthDataDTO.class));

        // 调用被测试方法
        depthDataProcessor.processDepthData(depthMessage);

        // 验证尝试发送到Kafka
        verify(asyncMarketDataProducer).sendDepthRawData(eq(depthDataDTO));
    }

    @Test
    void testProcessDepthDataWithConversionFailure() throws Exception {
        // 配置mock返回null，模拟转换失败
        doReturn(null).when(marketDataConverter).convertToOrderBookDTO(any(DepthData.class), eq("BTCUSDT"));

        // 调用被测试方法
        depthDataProcessor.processDepthData(depthMessage);

        // 验证不会尝试发送到Kafka
        verify(asyncMarketDataProducer, never()).sendDepthRawData(any(DepthDataDTO.class));
    }

    @Test
    void testProcessDepthDataWithNullMessage() throws Exception {
        // 调用被测试方法，传入null消息
        depthDataProcessor.processDepthData(null);

        // 验证不会尝试发送到Kafka
        verify(asyncMarketDataProducer, never()).sendDepthRawData(any(DepthDataDTO.class));
    }
} 