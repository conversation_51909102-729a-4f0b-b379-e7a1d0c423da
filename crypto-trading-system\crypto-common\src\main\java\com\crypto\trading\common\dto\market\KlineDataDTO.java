package com.crypto.trading.common.dto.market;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * K线数据传输对象
 * <p>
 * 用于在系统各层之间传递K线数据
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class KlineDataDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 交易对（例如：BTCUSDT）
     */
    private String symbol;

    /**
     * K线间隔
     * <p>
     * 可能的值: 1m, 3m, 5m, 15m, 30m, 1h, 2h, 4h, 6h, 8h, 12h, 1d, 3d, 1w, 1M
     * </p>
     */
    private String interval;

    /**
     * 开盘时间
     */
    private LocalDateTime openTime;

    /**
     * 收盘时间
     */
    private LocalDateTime closeTime;

    /**
     * 开盘价
     */
    private BigDecimal open;

    /**
     * 最高价
     */
    private BigDecimal high;

    /**
     * 最低价
     */
    private BigDecimal low;

    /**
     * 收盘价
     */
    private BigDecimal close;

    /**
     * 成交量
     */
    private BigDecimal volume;

    /**
     * 成交额
     */
    private BigDecimal quoteAssetVolume;

    /**
     * 成交笔数
     */
    private Long numberOfTrades;

    /**
     * 主动买入成交量
     */
    private BigDecimal takerBuyBaseAssetVolume;

    /**
     * 主动买入成交额
     */
    private BigDecimal takerBuyQuoteAssetVolume;

    /**
     * 是否完成
     */
    private boolean isClosed;

    /**
     * 构造函数
     */
    public KlineDataDTO() {
    }

    /**
     * 构造函数
     *
     * @param symbol                  交易对
     * @param interval                K线间隔
     * @param openTime                开盘时间
     * @param closeTime               收盘时间
     * @param open                    开盘价
     * @param high                    最高价
     * @param low                     最低价
     * @param close                   收盘价
     * @param volume                  成交量
     * @param quoteAssetVolume        成交额
     * @param numberOfTrades          成交笔数
     * @param takerBuyBaseAssetVolume 主动买入成交量
     * @param takerBuyQuoteAssetVolume 主动买入成交额
     * @param isClosed                是否完成
     */
    public KlineDataDTO(String symbol, String interval, LocalDateTime openTime, LocalDateTime closeTime,
                        BigDecimal open, BigDecimal high, BigDecimal low, BigDecimal close,
                        BigDecimal volume, BigDecimal quoteAssetVolume, Long numberOfTrades,
                        BigDecimal takerBuyBaseAssetVolume, BigDecimal takerBuyQuoteAssetVolume,
                        boolean isClosed) {
        this.symbol = symbol;
        this.interval = interval;
        this.openTime = openTime;
        this.closeTime = closeTime;
        this.open = open;
        this.high = high;
        this.low = low;
        this.close = close;
        this.volume = volume;
        this.quoteAssetVolume = quoteAssetVolume;
        this.numberOfTrades = numberOfTrades;
        this.takerBuyBaseAssetVolume = takerBuyBaseAssetVolume;
        this.takerBuyQuoteAssetVolume = takerBuyQuoteAssetVolume;
        this.isClosed = isClosed;
    }

    /**
     * 获取交易对
     *
     * @return 交易对
     */
    public String getSymbol() {
        return symbol;
    }

    /**
     * 设置交易对
     *
     * @param symbol 交易对
     */
    public void setSymbol(String symbol) {
        this.symbol = symbol;
    }

    /**
     * 获取K线间隔
     *
     * @return K线间隔
     */
    public String getInterval() {
        return interval;
    }

    /**
     * 设置K线间隔
     *
     * @param interval K线间隔
     */
    public void setInterval(String interval) {
        this.interval = interval;
    }

    /**
     * 获取开盘时间
     *
     * @return 开盘时间
     */
    public LocalDateTime getOpenTime() {
        return openTime;
    }

    /**
     * 设置开盘时间
     *
     * @param openTime 开盘时间
     */
    public void setOpenTime(LocalDateTime openTime) {
        this.openTime = openTime;
    }

    /**
     * 获取收盘时间
     *
     * @return 收盘时间
     */
    public LocalDateTime getCloseTime() {
        return closeTime;
    }

    /**
     * 设置收盘时间
     *
     * @param closeTime 收盘时间
     */
    public void setCloseTime(LocalDateTime closeTime) {
        this.closeTime = closeTime;
    }

    /**
     * 获取开盘价
     *
     * @return 开盘价
     */
    public BigDecimal getOpen() {
        return open;
    }

    /**
     * 设置开盘价
     *
     * @param open 开盘价
     */
    public void setOpen(BigDecimal open) {
        this.open = open;
    }

    /**
     * 获取最高价
     *
     * @return 最高价
     */
    public BigDecimal getHigh() {
        return high;
    }

    /**
     * 设置最高价
     *
     * @param high 最高价
     */
    public void setHigh(BigDecimal high) {
        this.high = high;
    }

    /**
     * 获取最低价
     *
     * @return 最低价
     */
    public BigDecimal getLow() {
        return low;
    }

    /**
     * 设置最低价
     *
     * @param low 最低价
     */
    public void setLow(BigDecimal low) {
        this.low = low;
    }

    /**
     * 获取收盘价
     *
     * @return 收盘价
     */
    public BigDecimal getClose() {
        return close;
    }

    /**
     * 设置收盘价
     *
     * @param close 收盘价
     */
    public void setClose(BigDecimal close) {
        this.close = close;
    }

    /**
     * 获取成交量
     *
     * @return 成交量
     */
    public BigDecimal getVolume() {
        return volume;
    }

    /**
     * 设置成交量
     *
     * @param volume 成交量
     */
    public void setVolume(BigDecimal volume) {
        this.volume = volume;
    }

    /**
     * 获取成交额
     *
     * @return 成交额
     */
    public BigDecimal getQuoteAssetVolume() {
        return quoteAssetVolume;
    }

    /**
     * 设置成交额
     *
     * @param quoteAssetVolume 成交额
     */
    public void setQuoteAssetVolume(BigDecimal quoteAssetVolume) {
        this.quoteAssetVolume = quoteAssetVolume;
    }

    /**
     * 获取成交笔数
     *
     * @return 成交笔数
     */
    public Long getNumberOfTrades() {
        return numberOfTrades;
    }

    /**
     * 设置成交笔数
     *
     * @param numberOfTrades 成交笔数
     */
    public void setNumberOfTrades(Long numberOfTrades) {
        this.numberOfTrades = numberOfTrades;
    }

    /**
     * 获取主动买入成交量
     *
     * @return 主动买入成交量
     */
    public BigDecimal getTakerBuyBaseAssetVolume() {
        return takerBuyBaseAssetVolume;
    }

    /**
     * 设置主动买入成交量
     *
     * @param takerBuyBaseAssetVolume 主动买入成交量
     */
    public void setTakerBuyBaseAssetVolume(BigDecimal takerBuyBaseAssetVolume) {
        this.takerBuyBaseAssetVolume = takerBuyBaseAssetVolume;
    }

    /**
     * 获取主动买入成交额
     *
     * @return 主动买入成交额
     */
    public BigDecimal getTakerBuyQuoteAssetVolume() {
        return takerBuyQuoteAssetVolume;
    }

    /**
     * 设置主动买入成交额
     *
     * @param takerBuyQuoteAssetVolume 主动买入成交额
     */
    public void setTakerBuyQuoteAssetVolume(BigDecimal takerBuyQuoteAssetVolume) {
        this.takerBuyQuoteAssetVolume = takerBuyQuoteAssetVolume;
    }

    /**
     * 是否完成
     *
     * @return 是否完成
     */
    public boolean isClosed() {
        return isClosed;
    }

    /**
     * 设置是否完成
     *
     * @param closed 是否完成
     */
    public void setClosed(boolean closed) {
        isClosed = closed;
    }

    @Override
    public String toString() {
        return "KlineDataDTO{" +
                "symbol='" + symbol + '\'' +
                ", interval='" + interval + '\'' +
                ", openTime=" + openTime +
                ", closeTime=" + closeTime +
                ", open=" + open +
                ", high=" + high +
                ", low=" + low +
                ", close=" + close +
                ", volume=" + volume +
                ", quoteAssetVolume=" + quoteAssetVolume +
                ", numberOfTrades=" + numberOfTrades +
                ", takerBuyBaseAssetVolume=" + takerBuyBaseAssetVolume +
                ", takerBuyQuoteAssetVolume=" + takerBuyQuoteAssetVolume +
                ", isClosed=" + isClosed +
                '}';
    }
} 