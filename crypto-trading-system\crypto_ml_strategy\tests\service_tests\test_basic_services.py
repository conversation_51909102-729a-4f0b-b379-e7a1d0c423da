#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
基础服务测试脚本

只测试核心的基础服务，避免复杂的data模块导入问题
"""

import os
import sys
import time
from loguru import logger

# 添加src目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_config_service():
    """测试配置服务"""
    logger.info("=== 测试配置服务 ===")
    try:
        from core.config import Config
        config = Config()
        
        # 测试基本配置读取
        kafka_servers = config.get('kafka', 'bootstrap_servers')
        logger.info(f"✓ Kafka服务器配置: {kafka_servers}")
        
        # 测试策略参数读取
        lppl_window = config.get_strategy_param('lppl.window_size')
        logger.info(f"✓ LPPL窗口大小: {lppl_window}")
        
        # 测试配置类型转换
        mysql_port = config.get_int('mysql', 'port')
        logger.info(f"✓ MySQL端口: {mysql_port}")
        
        logger.info("✅ 配置服务测试通过")
        return True
    except Exception as e:
        logger.error(f"❌ 配置服务测试失败: {e}")
        return False

def test_kafka_client():
    """测试Kafka客户端"""
    logger.info("=== 测试Kafka客户端 ===")
    try:
        from core.config import Config
        from api.kafka_client import KafkaClient
        
        config = Config()
        kafka_client = KafkaClient(config)
        logger.info("✓ KafkaClient创建成功")
        
        # 测试基本方法（不实际连接）
        if hasattr(kafka_client, 'clean_up'):
            logger.info("✓ KafkaClient具有clean_up方法")
        
        if hasattr(kafka_client, 'publish_signal'):
            logger.info("✓ KafkaClient具有publish_signal方法")
        
        kafka_client.clean_up()
        logger.info("✓ KafkaClient清理成功")
        
        logger.info("✅ Kafka客户端测试通过")
        return True
    except Exception as e:
        logger.error(f"❌ Kafka客户端测试失败: {e}")
        return False

def test_database_clients():
    """测试数据库客户端（单独导入）"""
    logger.info("=== 测试数据库客户端 ===")
    try:
        logger.info("测试InfluxDB客户端导入...")
        from data.clients.influxdb_client import InfluxDBClient
        logger.info("✓ InfluxDBClient导入成功")
        
        logger.info("测试MySQL客户端导入...")
        from data.clients.mysql_client import MySQLClient
        logger.info("✓ MySQLClient导入成功")
        
        logger.info("✅ 数据库客户端测试通过")
        return True
    except Exception as e:
        logger.error(f"❌ 数据库客户端测试失败: {e}")
        return False

def test_data_processor():
    """测试数据处理器"""
    logger.info("=== 测试数据处理器 ===")
    try:
        logger.info("测试DataProcessor导入...")
        from data.data_processor import DataProcessor
        logger.info("✓ DataProcessor导入成功")
        
        # 创建DataProcessor实例
        from core.config import Config
        config = Config()
        data_processor = DataProcessor(config)
        logger.info("✓ DataProcessor创建成功")
        
        # 测试基本方法
        if hasattr(data_processor, 'process_kline_data'):
            logger.info("✓ DataProcessor具有process_kline_data方法")
        
        if hasattr(data_processor, 'get_latest_market_data'):
            logger.info("✓ DataProcessor具有get_latest_market_data方法")
        
        logger.info("✅ 数据处理器测试通过")
        return True
    except Exception as e:
        logger.error(f"❌ 数据处理器测试失败: {e}")
        return False

def main():
    """主测试函数"""
    logger.info("开始基础服务测试")
    
    start_time = time.time()
    
    # 执行各项测试
    tests = [
        ("配置服务", test_config_service),
        ("Kafka客户端", test_kafka_client),
        ("数据库客户端", test_database_clients),
        ("数据处理器", test_data_processor)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n开始测试：{test_name}")
        try:
            result = test_func()
            results[test_name] = result
        except Exception as e:
            logger.error(f"测试 {test_name} 异常: {e}")
            results[test_name] = False
    
    # 生成测试报告
    end_time = time.time()
    total_time = end_time - start_time
    
    logger.info("\n" + "="*50)
    logger.info("基础服务测试报告")
    logger.info("="*50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"{test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\n总体结果: {passed}/{total} 测试通过")
    logger.info(f"测试耗时: {total_time:.2f}秒")
    
    if passed == total:
        logger.info("🎉 所有基础服务测试通过！")
        return True
    else:
        logger.warning(f"⚠️ {total - passed} 个测试需要进一步修复")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)