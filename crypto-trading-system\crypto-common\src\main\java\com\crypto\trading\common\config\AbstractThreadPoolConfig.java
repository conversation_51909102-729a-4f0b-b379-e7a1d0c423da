package com.crypto.trading.common.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.core.task.support.TaskExecutorAdapter;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 线程池配置抽象基类
 * 提供创建虚拟线程和平台线程池的通用方法
 * 
 * 子类可以继承此类并覆盖相关方法来自定义线程池配置
 */
public abstract class AbstractThreadPoolConfig {

    private static final Logger log = LoggerFactory.getLogger(AbstractThreadPoolConfig.class);

    /**
     * 创建虚拟线程任务执行器Bean
     * 用于轻量级任务，如IO操作
     * 子类可以覆盖此方法并添加@Bean注解
     * 
     * @return 虚拟线程任务执行器
     */
    public AsyncTaskExecutor virtualThreadTaskExecutor() {
        return createVirtualThreadTaskExecutor();
    }
    
    /**
     * 创建虚拟线程执行器Bean
     * 用于异步处理数据
     * 子类可以覆盖此方法并添加@Bean注解
     *
     * @return 虚拟线程执行器
     */
    public ExecutorService virtualThreadExecutor() {
        return createVirtualThreadExecutor();
    }
    
    /**
     * 创建平台线程池任务执行器Bean
     * 用于计算密集型任务
     * 子类可以覆盖此方法并添加@Bean注解
     * 
     * @return 平台线程池任务执行器
     */
    public ThreadPoolTaskExecutor platformThreadPoolTaskExecutor() {
        return createPlatformThreadPoolTaskExecutor();
    }

    /**
     * 创建虚拟线程任务执行器
     * 用于轻量级任务，如IO操作
     * 
     * @return 虚拟线程任务执行器
     */
    protected AsyncTaskExecutor createVirtualThreadTaskExecutor() {
        log.info("创建虚拟线程任务执行器 - 模块: {}", getModuleName());
        return new TaskExecutorAdapter(Executors.newVirtualThreadPerTaskExecutor());
    }
    
    /**
     * 创建虚拟线程执行器
     * 用于异步处理数据
     *
     * @return 虚拟线程执行器
     */
    protected ExecutorService createVirtualThreadExecutor() {
        log.info("创建虚拟线程执行器 - 模块: {}", getModuleName());
        return Executors.newVirtualThreadPerTaskExecutor();
    }
    
    /**
     * 创建平台线程池任务执行器
     * 用于计算密集型任务
     * 
     * @return 平台线程池任务执行器
     */
    protected ThreadPoolTaskExecutor createPlatformThreadPoolTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        configurePlatformThreadPool(executor);
        executor.setThreadNamePrefix(getModuleName() + "-platform-thread-");
        executor.initialize();
        log.info("创建平台线程池任务执行器 - 模块: {}, 核心线程数: {}, 最大线程数: {}, 队列容量: {}", 
                getModuleName(), executor.getCorePoolSize(), executor.getMaxPoolSize(), executor.getQueueCapacity());
        return executor;
    }
    
    /**
     * 配置平台线程池
     * 子类可以覆盖此方法以提供自定义配置
     * 
     * @param executor 线程池执行器
     */
    protected void configurePlatformThreadPool(ThreadPoolTaskExecutor executor) {
        executor.setCorePoolSize(getDefaultCorePoolSize());
        executor.setMaxPoolSize(getDefaultMaxPoolSize());
        executor.setQueueCapacity(getDefaultQueueCapacity());
        executor.setKeepAliveSeconds(getDefaultKeepAliveSeconds());
    }
    
    /**
     * 获取模块名称
     * 子类必须实现此方法以提供模块标识
     * 
     * @return 模块名称
     */
    protected abstract String getModuleName();
    
    /**
     * 获取默认核心线程数
     * 子类可以覆盖此方法以提供自定义值
     * 
     * @return 默认核心线程数
     */
    protected int getDefaultCorePoolSize() {
        return Runtime.getRuntime().availableProcessors();
    }
    
    /**
     * 获取默认最大线程数
     * 子类可以覆盖此方法以提供自定义值
     * 
     * @return 默认最大线程数
     */
    protected int getDefaultMaxPoolSize() {
        return Runtime.getRuntime().availableProcessors() * 2;
    }
    
    /**
     * 获取默认队列容量
     * 子类可以覆盖此方法以提供自定义值
     * 
     * @return 默认队列容量
     */
    protected int getDefaultQueueCapacity() {
        return 500;
    }
    
    /**
     * 获取默认线程保持活动时间（秒）
     * 子类可以覆盖此方法以提供自定义值
     * 
     * @return 默认线程保持活动时间
     */
    protected int getDefaultKeepAliveSeconds() {
        return 60;
    }
}