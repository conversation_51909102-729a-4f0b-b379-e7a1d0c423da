package com.crypto.trading.bootstrap.config.adapter;

import com.influxdb.client.InfluxDBClient;
import com.influxdb.client.InfluxDBClientFactory;
import com.influxdb.client.InfluxDBClientOptions;
import com.influxdb.client.WriteApi;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import com.crypto.trading.market.config.InfluxDBConfig;

import okhttp3.OkHttpClient;
import java.util.concurrent.TimeUnit;

/**
 * InfluxDB配置适配器
 * 负责将bootstrap模块的InfluxDB配置转换为market-data模块需要的格式
 */
@Configuration
public class InfluxDBConfigAdapter {
    
    private static final Logger log = LoggerFactory.getLogger(InfluxDBConfigAdapter.class);
    
    @Autowired
    private InfluxDBConfig influxDBConfig;
    
    /**
     * 创建InfluxDB客户端
     * 
     * @return InfluxDB客户端
     */
    @Bean
    @Primary
    public InfluxDBClient influxDBClient() {
        log.info("创建InfluxDBClient适配器: url={}, org={}, bucket={}", 
                influxDBConfig.getUrl(), influxDBConfig.getOrg(), influxDBConfig.getBucket());
                
        String token = influxDBConfig.getToken();
        
        // 构建客户端选项
        InfluxDBClientOptions.Builder optionsBuilder = InfluxDBClientOptions.builder()
                .url(influxDBConfig.getUrl())
                .org(influxDBConfig.getOrg())
                .bucket(influxDBConfig.getBucket())
                .okHttpClient(new OkHttpClient.Builder()
                    .connectTimeout(30000, TimeUnit.MILLISECONDS)
                    .readTimeout(120000, TimeUnit.MILLISECONDS)
                    .writeTimeout(60000, TimeUnit.MILLISECONDS));
        
        // 只有当token不为空时才添加token认证
        if (token != null && !token.isEmpty()) {
            optionsBuilder.authenticateToken(token.toCharArray());
            log.info("使用Token认证模式连接InfluxDB");
        } else {
            log.info("使用无认证模式连接InfluxDB - 注意：这可能会导致401未授权错误");
        }
        
        // 创建客户端
        return InfluxDBClientFactory.create(optionsBuilder.build());
    }
    
    /**
     * 创建InfluxDB写入API
     * 
     * @param influxDBClient InfluxDB客户端
     * @return InfluxDB写入API
     */
    @Bean
    @Primary
    public WriteApi writeApi(InfluxDBClient influxDBClient) {
        log.info("创建InfluxDB WriteApi适配器");
        return influxDBClient.makeWriteApi();
    }
}