package com.crypto.trading.common.constant;

/**
 * 异常常量类
 * <p>
 * 定义异常相关的常量，如错误码、错误消息等
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public final class ExceptionConstants {

    /**
     * 私有构造函数，防止实例化
     */
    private ExceptionConstants() {
        throw new IllegalStateException("常量类不允许实例化");
    }

    /**
     * 通用模块错误码前缀
     */
    public static final String ERROR_PREFIX_COMMON = "COMMON-";

    /**
     * 市场数据模块错误码前缀
     */
    public static final String ERROR_PREFIX_MARKET = "MARKET-";

    /**
     * 策略模块错误码前缀
     */
    public static final String ERROR_PREFIX_STRATEGY = "STRATEGY-";

    /**
     * 交易模块错误码前缀
     */
    public static final String ERROR_PREFIX_TRADE = "TRADE-";

    /**
     * 账户模块错误码前缀
     */
    public static final String ERROR_PREFIX_ACCOUNT = "ACCOUNT-";

    /**
     * 风控模块错误码前缀
     */
    public static final String ERROR_PREFIX_RISK = "RISK-";

    /**
     * 监控模块错误码前缀
     */
    public static final String ERROR_PREFIX_MONITOR = "MONITOR-";

    /**
     * 系统异常错误码
     */
    public static final String ERROR_CODE_SYSTEM = ERROR_PREFIX_COMMON + "00001";

    /**
     * 参数验证错误码
     */
    public static final String ERROR_CODE_PARAM_VALIDATION = ERROR_PREFIX_COMMON + "00002";

    /**
     * 数据库操作错误码
     */
    public static final String ERROR_CODE_DATABASE = ERROR_PREFIX_COMMON + "00003";

    /**
     * API调用错误码
     */
    public static final String ERROR_CODE_API_CALL = ERROR_PREFIX_COMMON + "00004";

    /**
     * WebSocket连接错误码
     */
    public static final String ERROR_CODE_WEBSOCKET = ERROR_PREFIX_COMMON + "00005";

    /**
     * Kafka消息发送错误码
     */
    public static final String ERROR_CODE_KAFKA_PRODUCER = ERROR_PREFIX_COMMON + "00006";

    /**
     * Kafka消息消费错误码
     */
    public static final String ERROR_CODE_KAFKA_CONSUMER = ERROR_PREFIX_COMMON + "00007";

    /**
     * InfluxDB操作错误码
     */
    public static final String ERROR_CODE_INFLUXDB = ERROR_PREFIX_COMMON + "00008";

    /**
     * 配置错误码
     */
    public static final String ERROR_CODE_CONFIG = ERROR_PREFIX_COMMON + "00009";

    /**
     * 未知异常错误码
     */
    public static final String ERROR_CODE_UNKNOWN = ERROR_PREFIX_COMMON + "99999";

    /**
     * 系统异常消息
     */
    public static final String ERROR_MSG_SYSTEM = "系统内部错误";

    /**
     * 参数验证异常消息
     */
    public static final String ERROR_MSG_PARAM_VALIDATION = "参数验证失败";

    /**
     * 数据库操作异常消息
     */
    public static final String ERROR_MSG_DATABASE = "数据库操作失败";

    /**
     * API调用异常消息
     */
    public static final String ERROR_MSG_API_CALL = "API调用失败";

    /**
     * WebSocket连接异常消息
     */
    public static final String ERROR_MSG_WEBSOCKET = "WebSocket连接异常";

    /**
     * Kafka消息发送异常消息
     */
    public static final String ERROR_MSG_KAFKA_PRODUCER = "Kafka消息发送失败";

    /**
     * Kafka消息消费异常消息
     */
    public static final String ERROR_MSG_KAFKA_CONSUMER = "Kafka消息消费失败";

    /**
     * InfluxDB操作异常消息
     */
    public static final String ERROR_MSG_INFLUXDB = "InfluxDB操作失败";

    /**
     * 配置异常消息
     */
    public static final String ERROR_MSG_CONFIG = "配置错误";

    /**
     * 未知异常消息
     */
    public static final String ERROR_MSG_UNKNOWN = "未知错误";

    /**
     * 交易所API调用错误码
     */
    public static final String ERROR_CODE_EXCHANGE_API = ERROR_PREFIX_TRADE + "00001";

    /**
     * 订单创建错误码
     */
    public static final String ERROR_CODE_ORDER_CREATE = ERROR_PREFIX_TRADE + "00002";

    /**
     * 订单取消错误码
     */
    public static final String ERROR_CODE_ORDER_CANCEL = ERROR_PREFIX_TRADE + "00003";

    /**
     * 订单查询错误码
     */
    public static final String ERROR_CODE_ORDER_QUERY = ERROR_PREFIX_TRADE + "00004";

    /**
     * 持仓查询错误码
     */
    public static final String ERROR_CODE_POSITION_QUERY = ERROR_PREFIX_TRADE + "00005";

    /**
     * 交易所API调用异常消息
     */
    public static final String ERROR_MSG_EXCHANGE_API = "交易所API调用失败";

    /**
     * 订单创建异常消息
     */
    public static final String ERROR_MSG_ORDER_CREATE = "订单创建失败";

    /**
     * 订单取消异常消息
     */
    public static final String ERROR_MSG_ORDER_CANCEL = "订单取消失败";

    /**
     * 订单查询异常消息
     */
    public static final String ERROR_MSG_ORDER_QUERY = "订单查询失败";

    /**
     * 持仓查询异常消息
     */
    public static final String ERROR_MSG_POSITION_QUERY = "持仓查询失败";

    /**
     * API限流错误码
     */
    public static final String ERROR_CODE_API_RATE_LIMIT = ERROR_PREFIX_COMMON + "00010";

    /**
     * API限流异常消息
     */
    public static final String ERROR_MSG_API_RATE_LIMIT = "API请求超过限流阈值";

    /**
     * 市场数据解析错误码
     */
    public static final String ERROR_CODE_MARKET_DATA_PARSE = ERROR_PREFIX_MARKET + "00001";

    /**
     * K线数据处理错误码
     */
    public static final String ERROR_CODE_KLINE_PROCESS = ERROR_PREFIX_MARKET + "00002";

    /**
     * 深度数据处理错误码
     */
    public static final String ERROR_CODE_DEPTH_PROCESS = ERROR_PREFIX_MARKET + "00003";

    /**
     * 交易数据处理错误码
     */
    public static final String ERROR_CODE_TRADE_PROCESS = ERROR_PREFIX_MARKET + "00004";

    /**
     * 市场数据解析异常消息
     */
    public static final String ERROR_MSG_MARKET_DATA_PARSE = "市场数据解析失败";

    /**
     * K线数据处理异常消息
     */
    public static final String ERROR_MSG_KLINE_PROCESS = "K线数据处理失败";

    /**
     * 深度数据处理异常消息
     */
    public static final String ERROR_MSG_DEPTH_PROCESS = "深度数据处理失败";

    /**
     * 交易数据处理异常消息
     */
    public static final String ERROR_MSG_TRADE_PROCESS = "交易数据处理失败";
} 