name: Publish package to the Maven Central Repository
on:
  push:
    branches: [ main ]
jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      # Checkout source code
      - name: Checkout source code
        uses: actions/checkout@v2
      - name: Set up Apache Maven Central
        uses: actions/setup-java@v3
        with: # running setup-java again overwrites the settings.xml
          distribution: 'adopt' # See 'Supported distributions' for available options
          java-version: '8'
          cache: 'maven'
          server-id: ossrh # Value of the distributionManagement/repository/id field of the pom.xml
          server-username: OSSRH_USERNAME # env variable for username in deploy
          server-password: OSSRH_TOKEN # env variable for token in deploy
          gpg-private-key: ${{ secrets.OSSRH_GPG_SECRET_KEY }} # Value of the GPG private key to import
          gpg-passphrase: MAVEN_GPG_PASSPHRASE # env variable for GPG private key passphrase
        env:
          MAVEN_USERNAME: ${{ secrets.OSSRH_USERNAME }}
          MAVEN_PASSWORD: ${{ secrets.OSSRH_TOKEN }}
          GPG_PASSPHRASE: ${{ secrets.OSSRH_GPG_SECRET_KEY_PASSWORD }}

      - name: Publish to Apache Maven Central
        run: mvn -Dgpg.passphrase=${{ secrets.OSSRH_GPG_SECRET_KEY_PASSWORD }} clean deploy
        env:
          OSSRH_USERNAME: ${{ secrets.OSSRH_USERNAME }}
          OSSRH_TOKEN: ${{ secrets.OSSRH_TOKEN }}
          MAVEN_GPG_PASSPHRASE: ${{ secrets.OSSRH_GPG_SECRET_KEY_PASSWORD }}
