package com.crypto.trading.market.config;

import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.core.ConsumerFactory;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;
import org.springframework.kafka.listener.ContainerProperties;
import org.springframework.kafka.support.serializer.JsonDeserializer;
import org.springframework.retry.annotation.EnableRetry;

import java.util.HashMap;
import java.util.Map;

/**
 * 异步Kafka消费者配置类
 * 专门用于异步数据处理架构，提供高可靠性的消息消费
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Configuration
@EnableKafka
@EnableRetry
public class AsyncKafkaConsumerConfig {

    private static final Logger log = LoggerFactory.getLogger(AsyncKafkaConsumerConfig.class);

    @Value("${spring.kafka.bootstrap-servers:localhost:9092}")
    private String bootstrapServers;

    /**
     * 配置数据库写入消费者工厂
     * 用于消费原始数据并写入数据库
     *
     * @return 数据库写入消费者工厂
     */
    @Bean("databaseConsumerFactory")
    public ConsumerFactory<String, Object> databaseConsumerFactory() {
        Map<String, Object> configProps = new HashMap<>();
        
        // 基础配置
        configProps.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
        configProps.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        configProps.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, JsonDeserializer.class);
        
        // 消费者组配置
        configProps.put(ConsumerConfig.GROUP_ID_CONFIG, "database-writer-group");
        configProps.put(ConsumerConfig.CLIENT_ID_CONFIG, "database-writer");
        
        // 可靠性配置
        configProps.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, false); // 手动提交
        configProps.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "earliest"); // 从最早的消息开始
        configProps.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, 100); // 每次拉取100条消息
        configProps.put(ConsumerConfig.SESSION_TIMEOUT_MS_CONFIG, 30000); // 30秒会话超时
        configProps.put(ConsumerConfig.HEARTBEAT_INTERVAL_MS_CONFIG, 3000); // 3秒心跳间隔
        configProps.put(ConsumerConfig.MAX_POLL_INTERVAL_MS_CONFIG, 600000); // 10分钟最大轮询间隔
        
        // 性能配置
        configProps.put(ConsumerConfig.FETCH_MIN_BYTES_CONFIG, 1024); // 最小拉取1KB
        configProps.put(ConsumerConfig.FETCH_MAX_WAIT_MS_CONFIG, 500); // 最大等待500ms
        configProps.put(ConsumerConfig.MAX_PARTITION_FETCH_BYTES_CONFIG, 1048576); // 每个分区最大1MB
        
        // JSON反序列化配置
        configProps.put(JsonDeserializer.TRUSTED_PACKAGES, "*");
        configProps.put(JsonDeserializer.USE_TYPE_INFO_HEADERS, false);
        configProps.put(JsonDeserializer.VALUE_DEFAULT_TYPE, Object.class);
        
        return new DefaultKafkaConsumerFactory<>(configProps);
    }

    /**
     * 配置数据库写入监听器容器工厂
     * 用于数据库写入的消费者监听器
     *
     * @return 数据库写入监听器容器工厂
     */
    @Bean("databaseKafkaListenerContainerFactory")
    public ConcurrentKafkaListenerContainerFactory<String, Object> databaseKafkaListenerContainerFactory() {
        ConcurrentKafkaListenerContainerFactory<String, Object> factory = 
            new ConcurrentKafkaListenerContainerFactory<>();
        
        factory.setConsumerFactory(databaseConsumerFactory());
        
        // 并发配置
        factory.setConcurrency(3); // 3个并发消费者
        
        // 容器属性配置
        ContainerProperties containerProps = factory.getContainerProperties();
        containerProps.setAckMode(ContainerProperties.AckMode.MANUAL_IMMEDIATE); // 手动立即确认
        containerProps.setPollTimeout(3000); // 3秒轮询超时
        containerProps.setIdleEventInterval(60000L); // 60秒空闲事件间隔
        
        // 错误处理
        factory.setCommonErrorHandler(new DatabaseConsumerErrorHandler());
        
        log.info("数据库写入Kafka监听器容器工厂配置完成");
        return factory;
    }

    /**
     * 配置策略数据消费者工厂
     * 用于消费处理后的数据供策略使用
     *
     * @return 策略数据消费者工厂
     */
    @Bean("strategyConsumerFactory")
    public ConsumerFactory<String, Object> strategyConsumerFactory() {
        Map<String, Object> configProps = new HashMap<>();
        
        // 基础配置
        configProps.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
        configProps.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        configProps.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, JsonDeserializer.class);
        
        // 消费者组配置
        configProps.put(ConsumerConfig.GROUP_ID_CONFIG, "strategy-data-group");
        configProps.put(ConsumerConfig.CLIENT_ID_CONFIG, "strategy-data-consumer");
        
        // 性能优先配置
        configProps.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, true); // 自动提交
        configProps.put(ConsumerConfig.AUTO_COMMIT_INTERVAL_MS_CONFIG, 1000); // 1秒自动提交间隔
        configProps.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "latest"); // 从最新的消息开始
        configProps.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, 500); // 每次拉取500条消息
        configProps.put(ConsumerConfig.SESSION_TIMEOUT_MS_CONFIG, 10000); // 10秒会话超时
        configProps.put(ConsumerConfig.HEARTBEAT_INTERVAL_MS_CONFIG, 1000); // 1秒心跳间隔
        
        // JSON反序列化配置
        configProps.put(JsonDeserializer.TRUSTED_PACKAGES, "*");
        configProps.put(JsonDeserializer.USE_TYPE_INFO_HEADERS, false);
        configProps.put(JsonDeserializer.VALUE_DEFAULT_TYPE, Object.class);
        
        return new DefaultKafkaConsumerFactory<>(configProps);
    }

    /**
     * 配置策略数据监听器容器工厂
     * 用于策略数据的消费者监听器
     *
     * @return 策略数据监听器容器工厂
     */
    @Bean("strategyKafkaListenerContainerFactory")
    public ConcurrentKafkaListenerContainerFactory<String, Object> strategyKafkaListenerContainerFactory() {
        ConcurrentKafkaListenerContainerFactory<String, Object> factory = 
            new ConcurrentKafkaListenerContainerFactory<>();
        
        factory.setConsumerFactory(strategyConsumerFactory());
        
        // 并发配置
        factory.setConcurrency(2); // 2个并发消费者
        
        // 容器属性配置
        ContainerProperties containerProps = factory.getContainerProperties();
        containerProps.setAckMode(ContainerProperties.AckMode.BATCH); // 批量确认
        containerProps.setPollTimeout(1000); // 1秒轮询超时
        
        log.info("策略数据Kafka监听器容器工厂配置完成");
        return factory;
    }

    /**
     * 数据库消费者错误处理器
     */
    private static class DatabaseConsumerErrorHandler implements org.springframework.kafka.listener.CommonErrorHandler {
        private static final Logger log = LoggerFactory.getLogger(DatabaseConsumerErrorHandler.class);

        @Override
        public boolean handleOne(Exception exception, 
                               org.apache.kafka.clients.consumer.ConsumerRecord<?, ?> record, 
                               org.apache.kafka.clients.consumer.Consumer<?, ?> consumer, 
                               org.springframework.kafka.listener.MessageListenerContainer container) {
            log.error("数据库消费者处理消息异常: topic={}, partition={}, offset={}, key={}, error={}", 
                record.topic(), record.partition(), record.offset(), record.key(), 
                exception.getMessage(), exception);
            
            // 这里可以实现重试逻辑或者将失败的消息发送到死信队列
            // 目前返回true表示继续处理下一条消息
            return true;
        }

        @Override
        public void handleOtherException(Exception exception, 
                                       org.apache.kafka.clients.consumer.Consumer<?, ?> consumer, 
                                       org.springframework.kafka.listener.MessageListenerContainer container, 
                                       boolean batchListener) {
            log.error("数据库消费者其他异常: error={}", exception.getMessage(), exception);
        }
    }
}
