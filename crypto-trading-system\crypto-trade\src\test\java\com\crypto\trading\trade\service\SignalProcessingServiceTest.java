package com.crypto.trading.trade.service;

import com.crypto.trading.common.util.DatabaseExecutorTestUtils;
import com.crypto.trading.trade.model.avro.OrderType;
import com.crypto.trading.trade.model.avro.SignalType;
import com.crypto.trading.trade.model.avro.TimeInForce;
import com.crypto.trading.trade.model.avro.TradingSignal;
import com.crypto.trading.trade.model.entity.TradingSignalEntity;
import com.crypto.trading.trade.model.result.SignalProcessResult;
import com.crypto.trading.trade.repository.mapper.TradingSignalMapper;
import com.crypto.trading.trade.service.impl.SignalProcessingServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.HashMap;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.mockito.ArgumentMatchers.any;

/**
 * 信号处理服务测试类
 */
public class SignalProcessingServiceTest {

    @InjectMocks
    private SignalProcessingServiceImpl signalProcessingService;

    @Mock
    private TradingSignalMapper tradingSignalMapper;
    
    @Mock
    private TransactionTemplate transactionTemplate;

    private TradingSignal createValidSignal() {
        return TradingSignal.newBuilder()
            .setId("test-signal-1")
            .setSymbol("BTCUSDT")
            .setStrategy("test-strategy")
            .setSignalType(SignalType.BUY)
            .setConfidence(0.8)
            .setOrderType(OrderType.MARKET)
            .setTimeInForce(TimeInForce.GTC)
            .setGeneratedTime(System.currentTimeMillis())
            .setAdditionalParams(new HashMap<>())
            .build();
    }

    @BeforeEach
    public void setup() {
        MockitoAnnotations.openMocks(this);
        ReflectionTestUtils.setField(signalProcessingService, "tradeExecutionEnabled", true);
        
        // 手动注入 transactionTemplate 到 signalProcessingService
        ReflectionTestUtils.setField(signalProcessingService, "transactionTemplate", transactionTemplate);
        
        // 配置 TransactionTemplate 的 execute 方法，直接执行回调函数并返回其结果
        when(transactionTemplate.execute(any())).thenAnswer(invocation -> {
            // 获取传入的回调函数
            Object callback = invocation.getArgument(0);
            
            // 如果是 TransactionCallback 类型，则调用其 doInTransaction 方法
            if (callback instanceof org.springframework.transaction.support.TransactionCallback) {
                return ((org.springframework.transaction.support.TransactionCallback<?>) callback)
                        .doInTransaction(null);
            }
            
            return null;
        });
    }

    @Test
    public void testProcessSignalWithInvalidValidationResult() {
        // 准备数据
        TradingSignal signal = createValidSignal();
        SignalValidationService.ValidationResult validationResult = 
                new SignalValidationService.ValidationResult(false, "无效的信号", signal, "test-validation-id");
        
        // 执行
        SignalProcessResult result = signalProcessingService.processSignal(validationResult);
        
        // 验证
        assertFalse(result.isSuccess());
        assertTrue(result.getMessage().contains("信号验证失败"));
        assertNull(result.getSignalEntity());
        
        // 确认没有调用mapper
        verify(tradingSignalMapper, never()).insert(any(TradingSignalEntity.class));
    }

    @Test
    public void testProcessSignalWithDuplicateSignal() {
        // 准备数据
        TradingSignal signal = createValidSignal();
        SignalValidationService.ValidationResult validationResult = 
                new SignalValidationService.ValidationResult(true, null, signal, "test-validation-id");
        
        // 模拟数据库中已存在该信号
        when(tradingSignalMapper.findBySignalId(signal.getId())).thenReturn(new TradingSignalEntity(signal));
        
        // 执行
        SignalProcessResult result = signalProcessingService.processSignal(validationResult);
        
        // 验证
        assertFalse(result.isSuccess());
        assertEquals("信号已存在", result.getMessage());
        assertNotNull(result.getSignalEntity());
        
        // 确认没有插入操作
        verify(tradingSignalMapper, never()).insert(any(TradingSignalEntity.class));
    }

    @Test
    public void testProcessSignalWithHoldSignal() {
        // 准备数据
        TradingSignal signal = TradingSignal.newBuilder(createValidSignal())
            .setSignalType(SignalType.HOLD)
            .build();
        SignalValidationService.ValidationResult validationResult = 
                new SignalValidationService.ValidationResult(true, null, signal, "test-validation-id");
        
        // 模拟数据库操作
        when(tradingSignalMapper.findBySignalId(signal.getId())).thenReturn(null);
        when(tradingSignalMapper.insert(any(TradingSignalEntity.class))).thenReturn(1);
        
        // 执行
        SignalProcessResult result = signalProcessingService.processSignal(validationResult);
        
        // 等待异步操作完成
        DatabaseExecutorTestUtils.waitForAsyncOperations();
        
        // 验证
        assertTrue(result.isSuccess());
        assertEquals("HOLD信号已处理", result.getMessage());
        assertNotNull(result.getSignalEntity());
        
        // 验证更新处理状态
        verify(tradingSignalMapper, times(1)).updateProcessStatus(
                eq(signal.getId()),
                eq(true),
                anyLong(),
                contains("HOLD信号，无需执行交易")
        );
    }

    @Test
    public void testProcessSignalWithExecutionDisabled() {
        // 准备数据
        TradingSignal signal = createValidSignal();
        SignalValidationService.ValidationResult validationResult = 
                new SignalValidationService.ValidationResult(true, null, signal, "test-validation-id");
        
        // 禁用交易执行
        ReflectionTestUtils.setField(signalProcessingService, "tradeExecutionEnabled", false);
        
        // 模拟数据库操作
        when(tradingSignalMapper.findBySignalId(signal.getId())).thenReturn(null);
        when(tradingSignalMapper.insert(any(TradingSignalEntity.class))).thenReturn(1);
        
        // 执行
        SignalProcessResult result = signalProcessingService.processSignal(validationResult);
        
        // 等待异步操作完成
        DatabaseExecutorTestUtils.waitForAsyncOperations();
        
        // 验证
        assertTrue(result.isSuccess());
        assertTrue(result.getMessage().contains("交易执行功能未启用"));
        assertNotNull(result.getSignalEntity());
        
        // 验证更新处理状态
        verify(tradingSignalMapper, times(1)).updateProcessStatus(
                eq(signal.getId()),
                eq(false),
                anyLong(),
                contains("交易执行功能未启用")
        );
    }

    @Test
    public void testProcessSignalWithExecutionEnabled() {
        // 准备数据
        TradingSignal signal = createValidSignal();
        SignalValidationService.ValidationResult validationResult = 
                new SignalValidationService.ValidationResult(true, null, signal, "test-validation-id");
        
        // 模拟数据库操作
        when(tradingSignalMapper.findBySignalId(signal.getId())).thenReturn(null);
        when(tradingSignalMapper.insert(any(TradingSignalEntity.class))).thenReturn(1);
        
        // 执行
        SignalProcessResult result = signalProcessingService.processSignal(validationResult);
        
        // 验证
        assertTrue(result.isSuccess());
        assertEquals("信号已提交执行", result.getMessage());
        assertNotNull(result.getSignalEntity());
        
        // 验证插入操作
        ArgumentCaptor<TradingSignalEntity> entityCaptor = ArgumentCaptor.forClass(TradingSignalEntity.class);
        verify(tradingSignalMapper, times(1)).insert(entityCaptor.capture());
        
        TradingSignalEntity capturedEntity = entityCaptor.getValue();
        assertEquals(signal.getId(), capturedEntity.getSignalId());
        assertEquals(signal.getSymbol(), capturedEntity.getSymbol());
        assertEquals(signal.getSignalType().toString(), capturedEntity.getSignalType());
    }

    @Test
    public void testMarkSignalAsProcessed() {
        // 准备数据
        TradingSignalEntity entity = new TradingSignalEntity();
        entity.setSignalId("test-signal-1");
        
        // 执行
        signalProcessingService.markSignalAsProcessed(entity, true, "处理成功");
        
        // 等待异步操作完成
        DatabaseExecutorTestUtils.waitForAsyncOperations();
        
        // 验证
        verify(tradingSignalMapper, times(1)).updateProcessStatus(
                eq("test-signal-1"),
                eq(true),
                anyLong(),
                contains("处理成功")
        );
    }
}