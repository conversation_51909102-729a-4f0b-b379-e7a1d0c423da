"""
Crypto ML Strategy - 日志安全和隐私保护

该模块实现了日志系统的安全和隐私保护功能，包括敏感数据过滤、
日志加密、访问控制和审计功能。

主要功能：
- LogSecurityManager: 日志安全管理器
- SensitiveDataFilter: 敏感数据过滤器
- LogEncryption: 日志加密处理器
- AccessControlManager: 日志访问控制
- AuditLogger: 审计日志记录器
- PrivacyComplianceChecker: 隐私合规检查器

作者: Crypto ML Strategy Team
日期: 2024-01-20
版本: 1.0.0
"""

import base64
import hashlib
import hmac
import json
import re
import threading
import time
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Union, Pattern, Callable
from loguru import logger
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC


class SecurityLevel(Enum):
    """安全级别"""
    PUBLIC = "public"
    INTERNAL = "internal"
    CONFIDENTIAL = "confidential"
    RESTRICTED = "restricted"


class AccessLevel(Enum):
    """访问级别"""
    READ = "read"
    WRITE = "write"
    ADMIN = "admin"


@dataclass
class SecurityPolicy:
    """安全策略"""
    encryption_enabled: bool = True
    access_control_enabled: bool = True
    audit_enabled: bool = True
    data_retention_days: int = 90
    max_log_size_mb: int = 100
    allowed_ip_ranges: List[str] = field(default_factory=list)
    sensitive_fields: List[str] = field(default_factory=lambda: [
        'password', 'passwd', 'secret', 'token', 'key', 'api_key',
        'access_token', 'refresh_token', 'private_key', 'credential',
        'authorization', 'auth', 'session_id', 'cookie'
    ])
    
    def validate(self) -> bool:
        """验证策略有效性"""
        try:
            if self.data_retention_days <= 0 or self.max_log_size_mb <= 0:
                return False
            
            # 验证IP范围格式
            for ip_range in self.allowed_ip_ranges:
                if not self._validate_ip_range(ip_range):
                    return False
            
            return True
        except Exception:
            return False
    
    def _validate_ip_range(self, ip_range: str) -> bool:
        """验证IP范围格式"""
        try:
            import ipaddress
            ipaddress.ip_network(ip_range, strict=False)
            return True
        except Exception:
            return False


@dataclass
class AuditEvent:
    """审计事件"""
    event_id: str
    timestamp: datetime
    event_type: str
    user_id: str
    source_ip: str
    action: str
    resource: str
    result: str
    details: Dict[str, Any]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "event_id": self.event_id,
            "timestamp": self.timestamp.isoformat(),
            "event_type": self.event_type,
            "user_id": self.user_id,
            "source_ip": self.source_ip,
            "action": self.action,
            "resource": self.resource,
            "result": self.result,
            "details": self.details
        }


class SensitiveDataFilter:
    """敏感数据过滤器"""
    
    def __init__(self, policy: SecurityPolicy):
        self.policy = policy
        self._patterns: List[Pattern] = []
        self._replacement = "***REDACTED***"
        self._lock = threading.RLock()
        
        self._compile_patterns()
    
    def _compile_patterns(self) -> None:
        """编译敏感数据匹配模式"""
        try:
            with self._lock:
                self._patterns.clear()
                
                # 基于字段名的模式
                for field in self.policy.sensitive_fields:
                    # JSON格式: "field": "value"
                    pattern = rf'"{field}"[\s]*:[\s]*"([^"]*)"'
                    self._patterns.append(re.compile(pattern, re.IGNORECASE))
                    
                    # 键值对格式: field=value
                    pattern = rf'{field}[\s]*=[\s]*([^\s,}}\]]+)'
                    self._patterns.append(re.compile(pattern, re.IGNORECASE))
                    
                    # URL参数格式: ?field=value&
                    pattern = rf'{field}=([^&\s]+)'
                    self._patterns.append(re.compile(pattern, re.IGNORECASE))
                
                # 通用敏感数据模式
                sensitive_patterns = [
                    # 信用卡号
                    r'\b(?:\d{4}[-\s]?){3}\d{4}\b',
                    # 社会保险号
                    r'\b\d{3}-\d{2}-\d{4}\b',
                    # 电子邮件地址（部分遮蔽）
                    r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b',
                    # IP地址（可选择性遮蔽）
                    r'\b(?:\d{1,3}\.){3}\d{1,3}\b',
                    # JWT令牌
                    r'eyJ[A-Za-z0-9_-]*\.eyJ[A-Za-z0-9_-]*\.[A-Za-z0-9_-]*',
                    # API密钥格式
                    r'[A-Za-z0-9]{32,}',
                ]
                
                for pattern in sensitive_patterns:
                    self._patterns.append(re.compile(pattern))
                    
        except Exception as e:
            logger.error(f"Failed to compile sensitive data patterns: {e}")
    
    def filter_message(self, message: str) -> str:
        """过滤消息中的敏感数据"""
        try:
            with self._lock:
                filtered_message = message
                
                for pattern in self._patterns:
                    filtered_message = pattern.sub(
                        lambda m: self._mask_match(m),
                        filtered_message
                    )
                
                return filtered_message
                
        except Exception as e:
            logger.error(f"Failed to filter sensitive data: {e}")
            return message
    
    def filter_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """过滤数据字典中的敏感信息"""
        try:
            return self._filter_dict_recursive(data, depth=0)
        except Exception as e:
            logger.error(f"Failed to filter sensitive data in dict: {e}")
            return data
    
    def _filter_dict_recursive(self, obj: Any, depth: int = 0) -> Any:
        """递归过滤字典中的敏感数据"""
        if depth > 10:  # 防止无限递归
            return "<max_depth_exceeded>"
        
        try:
            if isinstance(obj, dict):
                filtered = {}
                for key, value in obj.items():
                    if self._is_sensitive_key(key):
                        filtered[key] = self._replacement
                    else:
                        filtered[key] = self._filter_dict_recursive(value, depth + 1)
                return filtered
            
            elif isinstance(obj, (list, tuple)):
                return [self._filter_dict_recursive(item, depth + 1) for item in obj]
            
            elif isinstance(obj, str):
                return self.filter_message(obj)
            
            else:
                return obj
                
        except Exception:
            return "<filtering_error>"
    
    def _is_sensitive_key(self, key: str) -> bool:
        """检查键名是否为敏感字段"""
        key_lower = key.lower()
        return any(sensitive in key_lower for sensitive in self.policy.sensitive_fields)
    
    def _mask_match(self, match) -> str:
        """遮蔽匹配的敏感数据"""
        matched_text = match.group(0)
        
        # 对于较短的匹配，完全替换
        if len(matched_text) <= 8:
            return self._replacement
        
        # 对于较长的匹配，保留前后几个字符
        prefix = matched_text[:2]
        suffix = matched_text[-2:]
        return f"{prefix}***{suffix}"
    
    def add_sensitive_field(self, field_name: str) -> None:
        """添加敏感字段"""
        with self._lock:
            if field_name not in self.policy.sensitive_fields:
                self.policy.sensitive_fields.append(field_name)
                self._compile_patterns()
    
    def remove_sensitive_field(self, field_name: str) -> None:
        """移除敏感字段"""
        with self._lock:
            if field_name in self.policy.sensitive_fields:
                self.policy.sensitive_fields.remove(field_name)
                self._compile_patterns()


class LogEncryption:
    """日志加密处理器"""
    
    def __init__(self, password: str, salt: Optional[bytes] = None):
        self.password = password.encode('utf-8')
        self.salt = salt or b'crypto_ml_strategy_salt'
        self._cipher: Optional[Fernet] = None
        self._lock = threading.RLock()
        
        self._initialize_cipher()
    
    def _initialize_cipher(self) -> None:
        """初始化加密器"""
        try:
            # 使用PBKDF2生成密钥
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=self.salt,
                iterations=100000,
            )
            key = base64.urlsafe_b64encode(kdf.derive(self.password))
            self._cipher = Fernet(key)
            
        except Exception as e:
            logger.error(f"Failed to initialize encryption cipher: {e}")
            self._cipher = None
    
    def encrypt_message(self, message: str) -> str:
        """加密消息"""
        try:
            with self._lock:
                if self._cipher is None:
                    return message
                
                encrypted_bytes = self._cipher.encrypt(message.encode('utf-8'))
                return base64.b64encode(encrypted_bytes).decode('utf-8')
                
        except Exception as e:
            logger.error(f"Failed to encrypt message: {e}")
            return message
    
    def decrypt_message(self, encrypted_message: str) -> str:
        """解密消息"""
        try:
            with self._lock:
                if self._cipher is None:
                    return encrypted_message
                
                encrypted_bytes = base64.b64decode(encrypted_message.encode('utf-8'))
                decrypted_bytes = self._cipher.decrypt(encrypted_bytes)
                return decrypted_bytes.decode('utf-8')
                
        except Exception as e:
            logger.error(f"Failed to decrypt message: {e}")
            return encrypted_message
    
    def encrypt_data(self, data: Dict[str, Any]) -> str:
        """加密数据字典"""
        try:
            json_str = json.dumps(data, ensure_ascii=False)
            return self.encrypt_message(json_str)
        except Exception as e:
            logger.error(f"Failed to encrypt data: {e}")
            return str(data)
    
    def decrypt_data(self, encrypted_data: str) -> Dict[str, Any]:
        """解密数据字典"""
        try:
            json_str = self.decrypt_message(encrypted_data)
            return json.loads(json_str)
        except Exception as e:
            logger.error(f"Failed to decrypt data: {e}")
            return {"error": "decryption_failed"}


class AccessControlManager:
    """日志访问控制管理器"""
    
    def __init__(self, policy: SecurityPolicy):
        self.policy = policy
        self._user_permissions: Dict[str, Set[AccessLevel]] = {}
        self._ip_whitelist: Set[str] = set()
        self._access_log: List[Dict[str, Any]] = []
        self._lock = threading.RLock()
        
        self._initialize_ip_whitelist()
    
    def _initialize_ip_whitelist(self) -> None:
        """初始化IP白名单"""
        try:
            import ipaddress
            
            with self._lock:
                self._ip_whitelist.clear()
                
                for ip_range in self.policy.allowed_ip_ranges:
                    try:
                        network = ipaddress.ip_network(ip_range, strict=False)
                        for ip in network:
                            self._ip_whitelist.add(str(ip))
                    except Exception as e:
                        logger.warning(f"Invalid IP range {ip_range}: {e}")
                        
        except Exception as e:
            logger.error(f"Failed to initialize IP whitelist: {e}")
    
    def grant_permission(self, user_id: str, access_level: AccessLevel) -> None:
        """授予用户权限"""
        with self._lock:
            if user_id not in self._user_permissions:
                self._user_permissions[user_id] = set()
            
            self._user_permissions[user_id].add(access_level)
            
            self._log_access_event(
                user_id, "127.0.0.1", "grant_permission",
                f"access_level:{access_level.value}", "success"
            )
    
    def revoke_permission(self, user_id: str, access_level: AccessLevel) -> None:
        """撤销用户权限"""
        with self._lock:
            if user_id in self._user_permissions:
                self._user_permissions[user_id].discard(access_level)
                
                if not self._user_permissions[user_id]:
                    del self._user_permissions[user_id]
            
            self._log_access_event(
                user_id, "127.0.0.1", "revoke_permission",
                f"access_level:{access_level.value}", "success"
            )
    
    def check_permission(self, user_id: str, source_ip: str, 
                        required_level: AccessLevel) -> bool:
        """检查用户权限"""
        try:
            with self._lock:
                # 检查IP白名单
                if self.policy.allowed_ip_ranges and source_ip not in self._ip_whitelist:
                    self._log_access_event(
                        user_id, source_ip, "check_permission",
                        f"required_level:{required_level.value}", "denied_ip"
                    )
                    return False
                
                # 检查用户权限
                user_perms = self._user_permissions.get(user_id, set())
                
                # 管理员权限包含所有权限
                if AccessLevel.ADMIN in user_perms:
                    has_permission = True
                elif required_level == AccessLevel.READ:
                    has_permission = AccessLevel.READ in user_perms or AccessLevel.WRITE in user_perms
                elif required_level == AccessLevel.WRITE:
                    has_permission = AccessLevel.WRITE in user_perms
                else:
                    has_permission = required_level in user_perms
                
                result = "success" if has_permission else "denied_permission"
                self._log_access_event(
                    user_id, source_ip, "check_permission",
                    f"required_level:{required_level.value}", result
                )
                
                return has_permission
                
        except Exception as e:
            logger.error(f"Failed to check permission: {e}")
            return False
    
    def _log_access_event(self, user_id: str, source_ip: str, 
                         action: str, resource: str, result: str) -> None:
        """记录访问事件"""
        try:
            event = {
                "timestamp": datetime.now().isoformat(),
                "user_id": user_id,
                "source_ip": source_ip,
                "action": action,
                "resource": resource,
                "result": result
            }
            
            self._access_log.append(event)
            
            # 限制访问日志大小
            if len(self._access_log) > 10000:
                self._access_log = self._access_log[-5000:]
                
        except Exception as e:
            logger.error(f"Failed to log access event: {e}")
    
    def get_access_log(self, limit: int = 100) -> List[Dict[str, Any]]:
        """获取访问日志"""
        with self._lock:
            return self._access_log[-limit:]
    
    def get_user_permissions(self, user_id: str) -> List[str]:
        """获取用户权限"""
        with self._lock:
            perms = self._user_permissions.get(user_id, set())
            return [perm.value for perm in perms]


class AuditLogger:
    """审计日志记录器"""
    
    def __init__(self, audit_file: str = "logs/audit.log"):
        self.audit_file = Path(audit_file)
        self.audit_file.parent.mkdir(parents=True, exist_ok=True)
        self._lock = threading.RLock()
    
    def log_audit_event(self, event: AuditEvent) -> None:
        """记录审计事件"""
        try:
            with self._lock:
                audit_line = json.dumps(event.to_dict(), ensure_ascii=False)
                
                with open(self.audit_file, 'a', encoding='utf-8') as f:
                    f.write(audit_line + '\n')
                    
        except Exception as e:
            logger.error(f"Failed to log audit event: {e}")
    
    def create_audit_event(self, event_type: str, user_id: str, source_ip: str,
                          action: str, resource: str, result: str,
                          details: Optional[Dict[str, Any]] = None) -> AuditEvent:
        """创建审计事件"""
        import uuid
        
        return AuditEvent(
            event_id=str(uuid.uuid4()),
            timestamp=datetime.now(),
            event_type=event_type,
            user_id=user_id,
            source_ip=source_ip,
            action=action,
            resource=resource,
            result=result,
            details=details or {}
        )


class PrivacyComplianceChecker:
    """隐私合规检查器"""
    
    def __init__(self, policy: SecurityPolicy):
        self.policy = policy
        self._compliance_rules: List[Callable[[Dict[str, Any]], bool]] = []
        self._violations: List[Dict[str, Any]] = []
        self._lock = threading.RLock()
        
        self._initialize_default_rules()
    
    def _initialize_default_rules(self) -> None:
        """初始化默认合规规则"""
        # GDPR相关规则
        self._compliance_rules.extend([
            self._check_data_retention,
            self._check_sensitive_data_exposure,
            self._check_consent_tracking,
            self._check_data_minimization
        ])
    
    def check_compliance(self, log_data: Dict[str, Any]) -> bool:
        """检查日志数据的合规性"""
        try:
            with self._lock:
                violations = []
                
                for rule in self._compliance_rules:
                    try:
                        if not rule(log_data):
                            violations.append({
                                "rule": rule.__name__,
                                "timestamp": datetime.now().isoformat(),
                                "data_sample": str(log_data)[:200]
                            })
                    except Exception as e:
                        logger.error(f"Error in compliance rule {rule.__name__}: {e}")
                
                if violations:
                    self._violations.extend(violations)
                    return False
                
                return True
                
        except Exception as e:
            logger.error(f"Failed to check compliance: {e}")
            return True  # 默认允许
    
    def _check_data_retention(self, log_data: Dict[str, Any]) -> bool:
        """检查数据保留期限"""
        try:
            timestamp_str = log_data.get('timestamp')
            if not timestamp_str:
                return True
            
            log_time = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
            retention_limit = datetime.now() - timedelta(days=self.policy.data_retention_days)
            
            return log_time >= retention_limit
            
        except Exception:
            return True
    
    def _check_sensitive_data_exposure(self, log_data: Dict[str, Any]) -> bool:
        """检查敏感数据暴露"""
        try:
            message = str(log_data.get('message', ''))
            
            # 检查是否包含明文敏感信息
            for sensitive_field in self.policy.sensitive_fields:
                if sensitive_field in message.lower() and '***' not in message:
                    return False
            
            return True
            
        except Exception:
            return True
    
    def _check_consent_tracking(self, log_data: Dict[str, Any]) -> bool:
        """检查同意跟踪（GDPR要求）"""
        try:
            # 如果日志包含个人数据，应该有同意标识
            personal_data_indicators = ['user_id', 'email', 'phone', 'address']
            
            has_personal_data = any(
                indicator in str(log_data).lower()
                for indicator in personal_data_indicators
            )
            
            if has_personal_data:
                # 检查是否有同意标识
                consent_indicators = ['consent', 'agreed', 'opt_in']
                has_consent = any(
                    indicator in str(log_data).lower()
                    for indicator in consent_indicators
                )
                return has_consent
            
            return True
            
        except Exception:
            return True
    
    def _check_data_minimization(self, log_data: Dict[str, Any]) -> bool:
        """检查数据最小化原则"""
        try:
            # 检查日志大小是否合理
            log_size = len(json.dumps(log_data))
            max_size = self.policy.max_log_size_mb * 1024 * 1024
            
            return log_size <= max_size
            
        except Exception:
            return True
    
    def add_compliance_rule(self, rule: Callable[[Dict[str, Any]], bool]) -> None:
        """添加合规规则"""
        with self._lock:
            self._compliance_rules.append(rule)
    
    def get_violations(self, limit: int = 100) -> List[Dict[str, Any]]:
        """获取合规违规记录"""
        with self._lock:
            return self._violations[-limit:]
    
    def clear_violations(self) -> None:
        """清除违规记录"""
        with self._lock:
            self._violations.clear()


class LogSecurityManager:
    """日志安全管理器"""
    
    def __init__(self, policy: Optional[SecurityPolicy] = None, 
                 encryption_password: Optional[str] = None):
        self.policy = policy or SecurityPolicy()
        
        # 初始化组件
        self.data_filter = SensitiveDataFilter(self.policy)
        self.access_control = AccessControlManager(self.policy)
        self.audit_logger = AuditLogger()
        self.compliance_checker = PrivacyComplianceChecker(self.policy)
        
        # 初始化加密（如果启用）
        self.encryption: Optional[LogEncryption] = None
        if self.policy.encryption_enabled and encryption_password:
            self.encryption = LogEncryption(encryption_password)
        
        self._lock = threading.RLock()
    
    def process_log_entry(self, message: str, data: Dict[str, Any],
                         user_id: str = "system", source_ip: str = "127.0.0.1") -> Dict[str, Any]:
        """处理日志条目（应用安全策略）"""
        try:
            with self._lock:
                # 1. 过滤敏感数据
                filtered_message = self.data_filter.filter_message(message)
                filtered_data = self.data_filter.filter_data(data)
                
                # 2. 检查合规性
                log_entry = {
                    "message": filtered_message,
                    "data": filtered_data,
                    "timestamp": datetime.now().isoformat(),
                    "user_id": user_id,
                    "source_ip": source_ip
                }
                
                if not self.compliance_checker.check_compliance(log_entry):
                    logger.warning("Log entry failed compliance check")
                
                # 3. 加密（如果启用）
                if self.encryption:
                    log_entry["encrypted"] = True
                    log_entry["message"] = self.encryption.encrypt_message(filtered_message)
                    log_entry["data"] = self.encryption.encrypt_data(filtered_data)
                
                # 4. 记录审计事件
                if self.policy.audit_enabled:
                    audit_event = self.audit_logger.create_audit_event(
                        event_type="log_processing",
                        user_id=user_id,
                        source_ip=source_ip,
                        action="process_log",
                        resource="log_entry",
                        result="success",
                        details={"filtered": True, "encrypted": bool(self.encryption)}
                    )
                    self.audit_logger.log_audit_event(audit_event)
                
                return log_entry
                
        except Exception as e:
            logger.error(f"Failed to process log entry: {e}")
            return {"error": "processing_failed", "original_message": message}
    
    def check_access(self, user_id: str, source_ip: str, 
                    access_level: AccessLevel) -> bool:
        """检查访问权限"""
        if not self.policy.access_control_enabled:
            return True
        
        return self.access_control.check_permission(user_id, source_ip, access_level)
    
    def get_security_status(self) -> Dict[str, Any]:
        """获取安全状态"""
        with self._lock:
            return {
                "policy": {
                    "encryption_enabled": self.policy.encryption_enabled,
                    "access_control_enabled": self.policy.access_control_enabled,
                    "audit_enabled": self.policy.audit_enabled,
                    "data_retention_days": self.policy.data_retention_days
                },
                "components": {
                    "data_filter": "active",
                    "access_control": "active" if self.policy.access_control_enabled else "disabled",
                    "encryption": "active" if self.encryption else "disabled",
                    "audit_logger": "active" if self.policy.audit_enabled else "disabled",
                    "compliance_checker": "active"
                },
                "statistics": {
                    "compliance_violations": len(self.compliance_checker.get_violations()),
                    "access_events": len(self.access_control.get_access_log())
                }
            }


# 全局安全管理器实例
_global_security_manager: Optional[LogSecurityManager] = None


def get_global_security_manager() -> LogSecurityManager:
    """获取全局安全管理器实例"""
    global _global_security_manager
    
    if _global_security_manager is None:
        _global_security_manager = LogSecurityManager()
    
    return _global_security_manager


# 模块级别的日志器
module_logger = logger.bind(name=__name__)