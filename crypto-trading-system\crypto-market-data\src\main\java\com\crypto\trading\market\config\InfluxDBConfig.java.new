﻿package com.crypto.trading.market.config;

import com.influxdb.client.InfluxDBClient;
import com.influxdb.client.InfluxDBClientFactory;
import com.influxdb.client.InfluxDBClientOptions;
import com.influxdb.client.WriteApi;
import com.influxdb.client.WriteOptions;
import com.influxdb.client.domain.Bucket;
import com.influxdb.client.domain.BucketRetentionRules;
import com.influxdb.client.domain.WritePrecision;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import okhttp3.OkHttpClient;
import java.util.concurrent.TimeUnit;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.concurrent.TimeUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * InfluxDB閰嶇疆绫?
 * 璐熻矗鎻愪緵InfluxDB鐩稿叧鐨勯厤缃拰杩炴帴
 */
@Configuration(value = "marketInfluxDBConfig")
@Primary
public class InfluxDBConfig {

    private static final Logger log = LoggerFactory.getLogger(InfluxDBConfig.class);

    /**
     * InfluxDB URL
     */
    @Value("${market.influxdb.url:http://localhost:8086}")
    private String url;

    /**
     * InfluxDB 浠ょ墝
     */
    @Value("${market.influxdb.token:}")
    private String token;

    /**
     * InfluxDB 缁勭粐
     */
    @Value("${market.influxdb.org:crypto}")
    private String org;

    /**
     * InfluxDB 瀛樺偍妗?
     */
    @Value("${market.influxdb.bucket:market_data}")
    private String bucket;
    
    /**
     * 楂樼簿搴︽暟鎹繚鐣欐椂闂达紙灏忔椂锛?
     * 榛樿涓?68灏忔椂锛?澶╋級
     */
    @Value("${market.influxdb.retention.high-precision:168}")
    private int highPrecisionRetentionHours;
    
    /**
     * 涓簿搴︽暟鎹繚鐣欐椂闂达紙澶╋級
     * 榛樿涓?0澶?
     */
    @Value("${market.influxdb.retention.medium-precision:30}")
    private int mediumPrecisionRetentionDays;
    
    /**
     * 浣庣簿搴︽暟鎹繚鐣欐椂闂达紙澶╋級
     * 榛樿涓?65澶?
     */
    @Value("${market.influxdb.retention.low-precision:365}")
    private int lowPrecisionRetentionDays;
    
    /**
     * 鎵归噺鍐欏叆缂撳啿鍖哄ぇ灏?
     */
    @Value("${market.influxdb.write.buffer-size:10000}")
    private int writeBufferSize;
    
    /**
     * 鎵归噺鍐欏叆鎵规澶у皬
     */
    @Value("${market.influxdb.write.batch-size:5000}")
    private int writeBatchSize;
    
    /**
     * 鎵归噺鍐欏叆鍒锋柊闂撮殧锛堟绉掞級
     */
    @Value("${market.influxdb.write.flush-interval:1000}")
    private int writeFlushInterval;
    
    /**
     * 鍐欏叆绮惧害
     */
    @Value("${market.influxdb.write.precision:MS}")
    private String writePrecision;
    
    /**
     * 閲嶈瘯绛栫暐鏈€澶ч噸璇曟鏁?
     */
    @Value("${market.influxdb.write.retry-max:5}")
    private int writeRetryMax;
    
    /**
     * 閲嶈瘯绛栫暐鍒濆寤惰繜锛堟绉掞級
     */
    @Value("${market.influxdb.write.retry-delay:1000}")
    private int writeRetryDelay;
    
    /**
     * 鏄惁鍚敤鍘嬬缉
     */
    @Value("${market.influxdb.write.enable-gzip:true}")
    private boolean enableGzip;
    
    /**
     * 杩炴帴瓒呮椂锛堟绉掞級
     */
    @Value("${market.influxdb.connect-timeout:30000}")
    private int connectTimeout;
    
    /**
     * 璇诲彇瓒呮椂锛堟绉掞級
     */
    @Value("${market.influxdb.read-timeout:120000}")
    private int readTimeout;
    
    /**
     * 鍐欏叆瓒呮椂锛堟绉掞級
     */
    @Value("${market.influxdb.write-timeout:60000}")
    private int writeTimeout;

    /**
     * 閰嶇疆InfluxDB瀹㈡埛绔?
     *
     * @return InfluxDB瀹㈡埛绔?
     */
    @Bean(name = "marketInfluxDBClient")
    @ConditionalOnMissingBean(name = "marketInfluxDBClient")
    public InfluxDBClient influxDBClient() {
        log.info("鍒涘缓InfluxDB瀹㈡埛绔? url={}, org={}, bucket={}", url, org, bucket);
        
        try {
            InfluxDBClientOptions.Builder optionsBuilder = InfluxDBClientOptions.builder()
                    .url(url)
                    .org(org)
                    .bucket(bucket)
                    .okHttpClient(new OkHttpClient.Builder()
                        .connectTimeout(connectTimeout, TimeUnit.MILLISECONDS)
                        .readTimeout(readTimeout, TimeUnit.MILLISECONDS)
                        .writeTimeout(writeTimeout, TimeUnit.MILLISECONDS));
            
            // 鍙湁褰搕oken涓嶄负绌烘椂鎵嶆坊鍔爐oken璁よ瘉
            if (token != null && !token.isEmpty()) {
                optionsBuilder.authenticateToken(token.toCharArray());
                log.info("浣跨敤Token璁よ瘉妯″紡杩炴帴InfluxDB");
            } else {
                log.info("浣跨敤鏃犺璇佹ā寮忚繛鎺nfluxDB");
            }
            
            // 鍒涘缓瀹㈡埛绔?
            InfluxDBClient client = InfluxDBClientFactory.create(optionsBuilder.build());
            
            // 娴嬭瘯杩炴帴锛岀‘淇濋厤缃纭?
            try {
                // 灏濊瘯鑾峰彇鍋ュ悍鐘舵€?
                boolean isHealthy = client.health().getStatus() == com.influxdb.client.domain.HealthCheck.StatusEnum.PASS;
                log.info("InfluxDB杩炴帴娴嬭瘯: {}", isHealthy ? "鎴愬姛" : "澶辫触浣嗗凡鍒涘缓瀹㈡埛绔?);
            } catch (Exception e) {
                log.warn("InfluxDB杩炴帴娴嬭瘯澶辫触锛屼絾灏嗙户缁娇鐢ㄥ鎴风: {}", e.getMessage());
            }
            
            return client;
        } catch (Exception e) {
            log.error("鍒涘缓InfluxDB瀹㈡埛绔け璐? {}", e.getMessage(), e);
            // 鍒涘缓涓€涓棤鎿嶄綔鐨勫鎴风锛岄伩鍏嶅簲鐢ㄧ▼搴忓穿婧冿紝浣嗘墍鏈夋搷浣滈兘灏嗚蹇界暐
            log.warn("杩斿洖绌篒nfluxDB瀹㈡埛绔紝鏁版嵁灏嗕笉浼氬啓鍏nfluxDB锛屼絾搴旂敤绋嬪簭灏嗙户缁繍琛?);
            return null;
        }
    }

    /**
     * 閰嶇疆InfluxDB鍐欏叆API
     *
     * @param influxDBClient InfluxDB瀹㈡埛绔?
     * @return InfluxDB鍐欏叆API
     */
    @Bean(name = "marketWriteApi")
    @ConditionalOnMissingBean(name = "marketWriteApi")
    public WriteApi writeApi(InfluxDBClient influxDBClient) {
        log.info("鍒涘缓InfluxDB鍐欏叆API, 鎵规澶у皬: {}, 鍒锋柊闂撮殧: {}ms", writeBatchSize, writeFlushInterval);
        
        // 鍒涘缓楂樼骇鍐欏叆閫夐」
        WriteOptions writeOptions = WriteOptions.builder()
                .batchSize(writeBatchSize)
                .flushInterval(writeFlushInterval)
                .bufferLimit(writeBufferSize)
                .jitterInterval(500)
                .retryInterval(writeRetryDelay)
                .maxRetries(writeRetryMax)
                .maxRetryDelay(30_000) // 鏈€澶ч噸璇曞欢杩?0绉?
                .exponentialBase(2) // 鎸囨暟閫€閬垮熀鏁?
                .build();
                
        return influxDBClient.makeWriteApi(writeOptions);
    }
    
    /**
     * 鑾峰彇鍐欏叆绮惧害
     * 
     * @return 鍐欏叆绮惧害
     */
    @Bean(name = "marketWritePrecision")
    @ConditionalOnMissingBean(name = "marketWritePrecision")
    public WritePrecision writePrecision() {
        try {
            return WritePrecision.valueOf(writePrecision);
        } catch (IllegalArgumentException e) {
            log.warn("鏃犳晥鐨勫啓鍏ョ簿搴? {}, 浣跨敤榛樿鍊? MS", writePrecision);
            return WritePrecision.MS;
        }
    }

    /**
     * 鑾峰彇InfluxDB URL
     *
     * @return InfluxDB URL
     */
    public String getUrl() {
        return url;
    }

    /**
     * 鑾峰彇InfluxDB 浠ょ墝
     *
     * @return InfluxDB 浠ょ墝
     */
    public String getToken() {
        return token;
    }

    /**
     * 鑾峰彇InfluxDB 缁勭粐
     *
     * @return InfluxDB 缁勭粐
     */
    public String getOrg() {
        return org;
    }

    /**
     * 鑾峰彇InfluxDB 瀛樺偍妗?
     *
     * @return InfluxDB 瀛樺偍妗?
     */
    public String getBucket() {
        return bucket;
    }
    
    /**
     * 鑾峰彇楂樼簿搴︽暟鎹繚鐣欐椂闂达紙灏忔椂锛?
     *
     * @return 楂樼簿搴︽暟鎹繚鐣欐椂闂达紙灏忔椂锛?
     */
    public int getHighPrecisionRetentionHours() {
        return highPrecisionRetentionHours;
    }
    
    /**
     * 鑾峰彇涓簿搴︽暟鎹繚鐣欐椂闂达紙澶╋級
     *
     * @return 涓簿搴︽暟鎹繚鐣欐椂闂达紙澶╋級
     */
    public int getMediumPrecisionRetentionDays() {
        return mediumPrecisionRetentionDays;
    }
    
    /**
     * 鑾峰彇浣庣簿搴︽暟鎹繚鐣欐椂闂达紙澶╋級
     *
     * @return 浣庣簿搴︽暟鎹繚鐣欐椂闂达紙澶╋級
     */
    public int getLowPrecisionRetentionDays() {
        return lowPrecisionRetentionDays;
    }
    
    /**
     * 纭繚鎵€闇€鐨勫瓨鍌ㄦ《瀛樺湪
     * 
     * @param influxDBClient InfluxDB瀹㈡埛绔?
     * @return 鏄惁鎴愬姛鍒涘缓鎵€鏈夊瓨鍌ㄦ《
     */
    @Bean(name = "influxDBBucketsInitializer")
    @ConditionalOnMissingBean(name = "influxDBBucketsInitializer")
    public boolean ensureRequiredBucketsExist(InfluxDBClient influxDBClient) {
        try {
            log.info("寮€濮嬫鏌ュ苟鍒涘缓蹇呰鐨処nfluxDB瀛樺偍妗?);
            
            // 鑾峰彇缁勭粐ID
            String orgID = influxDBClient.getOrganizationsApi().findOrganizations().stream()
                    .filter(org -> org.getName().equals(this.org))
                    .findFirst()
                    .orElseThrow(() -> new RuntimeException("缁勭粐涓嶅瓨鍦? " + this.org))
                    .getId();
                    
            log.info("鑾峰彇鍒扮粍缁嘔D: {}", orgID);
            
            // 鑾峰彇鐜版湁瀛樺偍妗?
            List<Bucket> existingBuckets = influxDBClient.getBucketsApi().findBuckets();
            Set<String> existingBucketNames = existingBuckets.stream()
                    .map(Bucket::getName)
                    .collect(Collectors.toSet());
                    
            log.info("鐜版湁瀛樺偍妗? {}", existingBucketNames);
            
            // 纭繚瀛樺偍妗跺瓨鍦?
            ensureBucketExists(influxDBClient, existingBucketNames, bucket, orgID, 0); // 涓诲瓨鍌ㄦ《锛屾棤闄愭湡淇濈暀
            ensureBucketExists(influxDBClient, existingBucketNames, bucket + "_highres", orgID, highPrecisionRetentionHours * 3600);
            ensureBucketExists(influxDBClient, existingBucketNames, bucket + "_midres", orgID, mediumPrecisionRetentionDays * 86400);
            ensureBucketExists(influxDBClient, existingBucketNames, bucket + "_lowres", orgID, lowPrecisionRetentionDays * 86400);
            
            log.info("InfluxDB瀛樺偍妗舵鏌ュ拰鍒涘缓瀹屾垚");
            return true;
            
        } catch (Exception e) {
            log.error("鍒涘缓InfluxDB瀛樺偍妗舵椂鍑洪敊: {}", e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 纭繚鐗瑰畾鐨勫瓨鍌ㄦ《瀛樺湪
     * 
     * @param influxDBClient InfluxDB瀹㈡埛绔?
     * @param existingBuckets 鐜版湁瀛樺偍妗跺垪琛?
     * @param bucketName 瀛樺偍妗跺悕绉?
     * @param orgID 缁勭粐ID
     * @param retentionSeconds 淇濈暀绉掓暟 (0琛ㄧず鏃犻檺鏈熶繚鐣?
     */
    private void ensureBucketExists(InfluxDBClient influxDBClient, Set<String> existingBuckets, 
                                   String bucketName, String orgID, int retentionSeconds) {
        if (!existingBuckets.contains(bucketName)) {
            log.info("鍒涘缓瀛樺偍妗? {}, 淇濈暀鏃堕棿: {} 绉?, bucketName, retentionSeconds > 0 ? retentionSeconds : "鏃犻檺鏈?);
            
            try {
                if (retentionSeconds > 0) {
                    // 鍒涘缓鏈変繚鐣欑瓥鐣ョ殑瀛樺偍妗?
                    BucketRetentionRules retentionRules = new BucketRetentionRules();
                    retentionRules.setEverySeconds(Integer.valueOf(retentionSeconds));
                    
                    // 鍒涘缓瀛樺偍妗?
                    influxDBClient.getBucketsApi().createBucket(bucketName, retentionRules, orgID);
                } else {
                    // 鍒涘缓鏃犱繚鐣欑瓥鐣ョ殑瀛樺偍妗?
                    influxDBClient.getBucketsApi().createBucket(bucketName, orgID);
                }
                
                log.info("瀛樺偍妗跺垱寤烘垚鍔? {}", bucketName);
            } catch (Exception e) {
                log.error("鍒涘缓瀛樺偍妗跺け璐? {}, 閿欒: {}", bucketName, e.getMessage(), e);
            }
        } else {
            log.info("瀛樺偍妗跺凡瀛樺湪: {}", bucketName);
        }
    }
}
