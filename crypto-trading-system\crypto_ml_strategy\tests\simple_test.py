#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os

# 设置Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def test_all():
    success = 0
    total = 0
    
    # 基础模块
    try:
        from src.config import Config
        from src.logger import setup_logger
        from src.kafka_client import KafkaClient
        from src.data_processor import DataProcessor
        print("[OK] Basic modules")
        success += 1
    except Exception as e:
        print(f"[FAIL] Basic modules: {e}")
    total += 1
    
    # 数据模块
    try:
        from src.data.influxdb_client import InfluxDBClient
        from src.data.mysql_client import MySQLClient
        from src.data.real_data_loader import RealDataLoader
        print("[OK] Data modules")
        success += 1
    except Exception as e:
        print(f"[FAIL] Data modules: {e}")
    total += 1
    
    # 模型模块
    try:
        from src.model.feature_engineering import FeatureEngineering
        from src.model.model_trainer import ModelTrainer
        from src.model.prediction import PredictionEngine
        from src.model.online_learner import OnlineLearner
        from src.model.versioning import ModelVersionManager
        print("[OK] Model modules")
        success += 1
    except Exception as e:
        print(f"[FAIL] Model modules: {e}")
    total += 1
    
    # 策略模块
    try:
        from src.strategy.base import BaseStrategy
        from src.strategy.unified_ml import UnifiedMLStrategy
        print("[OK] Strategy modules")
        success += 1
    except Exception as e:
        print(f"[FAIL] Strategy modules: {e}")
    total += 1
    
    # 指标模块
    try:
        from src.indicators.lppl_features import LPPLFeatureExtractor
        from src.indicators.hematread_features import HematreadFeatureExtractor
        from src.indicators.bull_market_support import BullMarketSupportFeatureExtractor
        from src.indicators.super_trend import SuperTrendCalculator
        print("[OK] Indicators modules")
        success += 1
    except Exception as e:
        print(f"[FAIL] Indicators modules: {e}")
    total += 1
    
    # 主模块
    try:
        from src.main import TradingStrategyApp
        print("[OK] Main module")
        success += 1
    except Exception as e:
        print(f"[FAIL] Main module: {e}")
    total += 1
    
    print(f"\nResult: {success}/{total} passed")
    return success == total

if __name__ == "__main__":
    test_all()