"""
Data Cache Module - 数据缓存模块

主要组件：
- CacheCore：核心缓存功能
- CacheManager：缓存管理器
- CacheStorage：缓存存储
- CacheStrategies：缓存策略

Author: Crypto ML Strategy Team
Version: 1.0.0
"""

from .cache_core import RealTimeCacheManager as CacheCore
from .cache_manager import CacheManager
from .cache_storage import MemoryBackend as CacheStorage
from .cache_strategies import CacheStrategyFactory as CacheStrategies
from .cache_integration import IntegratedCacheManager as CacheIntegration

__all__ = [
    'CacheCore',
    'CacheManager', 
    'CacheStorage',
    'CacheStrategies',
    'CacheIntegration'
]

__version__ = '1.0.0'
__author__ = 'Crypto ML Strategy Team'