package com.crypto.trading.common.util;

import com.crypto.trading.common.constant.SystemConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Date;

/**
 * 日期时间工具类
 * <p>
 * 提供日期时间相关的工具方法，如格式化、解析、计算等
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public final class DateUtil {
    private static final Logger log = LoggerFactory.getLogger(DateUtil.class);

    /**
     * 私有构造函数，防止实例化
     */
    private DateUtil() {
        throw new IllegalStateException("工具类不允许实例化");
    }

    /**
     * 标准日期时间格式
     */
    private static final DateTimeFormatter DATETIME_FORMATTER = DateTimeFormatter.ofPattern(SystemConstants.DATETIME_FORMAT);

    /**
     * 标准日期格式
     */
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern(SystemConstants.DATE_FORMAT);

    /**
     * 标准时间格式
     */
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern(SystemConstants.TIME_FORMAT);

    /**
     * 毫秒级时间戳格式
     */
    private static final DateTimeFormatter TIMESTAMP_FORMATTER = DateTimeFormatter.ofPattern(SystemConstants.TIMESTAMP_FORMAT);

    /**
     * 获取当前日期时间
     *
     * @return 当前日期时间字符串，格式：yyyy-MM-dd HH:mm:ss
     */
    public static String now() {
        return LocalDateTime.now().format(DATETIME_FORMATTER);
    }

    /**
     * 获取当前日期时间
     *
     * @return 当前日期时间字符串，格式：yyyy-MM-dd HH:mm:ss
     */
    public static String getCurrentDateTime() {
        return now();
    }

    /**
     * 获取当前日期
     *
     * @return 当前日期字符串，格式：yyyy-MM-dd
     */
    public static String today() {
        return LocalDate.now().format(DATE_FORMATTER);
    }

    /**
     * 获取当前日期
     *
     * @return 当前日期字符串，格式：yyyy-MM-dd
     */
    public static String getCurrentDate() {
        return today();
    }

    /**
     * 获取当前时间
     *
     * @return 当前时间字符串，格式：HH:mm:ss
     */
    public static String currentTime() {
        return LocalTime.now().format(TIME_FORMATTER);
    }

    /**
     * 获取当前时间
     *
     * @return 当前时间字符串，格式：HH:mm:ss
     */
    public static String getCurrentTime() {
        return currentTime();
    }

    /**
     * 获取当前时间戳
     *
     * @return 当前时间戳（毫秒）
     */
    public static long currentTimeMillis() {
        return System.currentTimeMillis();
    }

    /**
     * 获取当前时间戳
     *
     * @return 当前时间戳（毫秒）
     */
    public static long getCurrentTimestamp() {
        return currentTimeMillis();
    }

    /**
     * 获取当前时间戳（秒）
     *
     * @return 当前时间戳（秒）
     */
    public static long getCurrentTimestampSeconds() {
        return currentTimeMillis() / 1000;
    }

    /**
     * 获取当前UTC时间戳
     *
     * @return 当前UTC时间戳（毫秒）
     */
    public static long currentUtcTimeMillis() {
        return Instant.now().toEpochMilli();
    }

    /**
     * 格式化日期时间
     *
     * @param dateTime 日期时间
     * @return 格式化后的日期时间字符串，格式：yyyy-MM-dd HH:mm:ss
     */
    public static String formatDateTime(LocalDateTime dateTime) {
        return dateTime.format(DATETIME_FORMATTER);
    }

    /**
     * 格式化日期时间
     *
     * @param dateTime 日期时间
     * @param pattern  格式模式
     * @return 格式化后的日期时间字符串
     */
    public static String formatDateTime(LocalDateTime dateTime, String pattern) {
        return dateTime.format(DateTimeFormatter.ofPattern(pattern));
    }

    /**
     * 格式化日期
     *
     * @param date 日期
     * @return 格式化后的日期字符串，格式：yyyy-MM-dd
     */
    public static String formatDate(LocalDate date) {
        return date.format(DATE_FORMATTER);
    }

    /**
     * 格式化时间
     *
     * @param time 时间
     * @return 格式化后的时间字符串，格式：HH:mm:ss
     */
    public static String formatTime(LocalTime time) {
        return time.format(TIME_FORMATTER);
    }

    /**
     * 格式化时间戳
     *
     * @param timestamp 时间戳（毫秒）
     * @return 格式化后的日期时间字符串，格式：yyyy-MM-dd HH:mm:ss
     */
    public static String formatTimestamp(long timestamp) {
        return formatDateTime(LocalDateTime.ofInstant(Instant.ofEpochMilli(timestamp), ZoneId.systemDefault()));
    }
    
    /**
     * 格式化时间戳为指定格式
     *
     * @param timestamp 时间戳（毫秒）
     * @param pattern 日期格式
     * @return 格式化后的日期时间字符串
     */
    public static String formatTimestamp(long timestamp, String pattern) {
        return LocalDateTime.ofInstant(Instant.ofEpochMilli(timestamp), ZoneId.systemDefault())
                .format(DateTimeFormatter.ofPattern(pattern));
    }

    /**
     * 格式化时间戳（毫秒级）
     *
     * @param timestamp 时间戳（毫秒）
     * @return 格式化后的日期时间字符串，格式：yyyy-MM-dd HH:mm:ss.SSS
     */
    public static String formatTimestampWithMillis(long timestamp) {
        return LocalDateTime.ofInstant(Instant.ofEpochMilli(timestamp), ZoneId.systemDefault())
                .format(TIMESTAMP_FORMATTER);
    }

    /**
     * 解析日期时间字符串
     *
     * @param dateTimeStr 日期时间字符串，格式：yyyy-MM-dd HH:mm:ss
     * @return 日期时间对象
     */
    public static LocalDateTime parseDateTime(String dateTimeStr) {
        return LocalDateTime.parse(dateTimeStr, DATETIME_FORMATTER);
    }

    /**
     * 解析日期字符串
     *
     * @param dateStr 日期字符串，格式：yyyy-MM-dd
     * @return 日期对象
     */
    public static LocalDate parseDate(String dateStr) {
        return LocalDate.parse(dateStr, DATE_FORMATTER);
    }

    /**
     * 解析时间字符串
     *
     * @param timeStr 时间字符串，格式：HH:mm:ss
     * @return 时间对象
     */
    public static LocalTime parseTime(String timeStr) {
        return LocalTime.parse(timeStr, TIME_FORMATTER);
    }

    /**
     * 解析日期时间字符串为时间戳
     *
     * @param dateTimeStr 日期时间字符串，格式：yyyy-MM-dd HH:mm:ss
     * @return 时间戳（毫秒）
     */
    public static long parseTimestamp(String dateTimeStr) {
        return parseDateTime(dateTimeStr).atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }

    /**
     * 字符串转时间戳
     *
     * @param dateTimeStr 日期时间字符串，格式：yyyy-MM-dd HH:mm:ss
     * @return 时间戳（毫秒）
     */
    public static long toTimestamp(String dateTimeStr) {
        return parseDateTime(dateTimeStr).atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }

    /**
     * Date对象转LocalDateTime
     *
     * @param date Date对象
     * @return LocalDateTime对象
     */
    public static LocalDateTime toLocalDateTime(Date date) {
        return LocalDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault());
    }

    /**
     * LocalDateTime对象转Date
     *
     * @param dateTime LocalDateTime对象
     * @return Date对象
     */
    public static Date toDate(LocalDateTime dateTime) {
        return Date.from(dateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 计算两个日期之间的天数差
     *
     * @param start 开始日期
     * @param end   结束日期
     * @return 天数差
     */
    public static long daysBetween(LocalDate start, LocalDate end) {
        return ChronoUnit.DAYS.between(start, end);
    }

    /**
     * 计算两个日期时间之间的小时差
     *
     * @param start 开始日期时间
     * @param end   结束日期时间
     * @return 小时差
     */
    public static long hoursBetween(LocalDateTime start, LocalDateTime end) {
        return ChronoUnit.HOURS.between(start, end);
    }

    /**
     * 计算两个日期时间之间的分钟差
     *
     * @param start 开始日期时间
     * @param end   结束日期时间
     * @return 分钟差
     */
    public static long minutesBetween(LocalDateTime start, LocalDateTime end) {
        return ChronoUnit.MINUTES.between(start, end);
    }

    /**
     * 计算两个日期时间之间的秒差
     *
     * @param start 开始日期时间
     * @param end   结束日期时间
     * @return 秒差
     */
    public static long secondsBetween(LocalDateTime start, LocalDateTime end) {
        return ChronoUnit.SECONDS.between(start, end);
    }

    /**
     * 计算两个日期时间之间的毫秒差
     *
     * @param start 开始日期时间
     * @param end   结束日期时间
     * @return 毫秒差
     */
    public static long millisBetween(LocalDateTime start, LocalDateTime end) {
        return ChronoUnit.MILLIS.between(start, end);
    }

    /**
     * 计算两个时间戳之间的毫秒差
     *
     * @param start 开始时间戳（毫秒）
     * @param end   结束时间戳（毫秒）
     * @return 毫秒差
     */
    public static long millisBetween(long start, long end) {
        return end - start;
    }

    /**
     * 日期时间加上指定的天数
     *
     * @param dateTime 日期时间
     * @param days     天数
     * @return 加上天数后的日期时间
     */
    public static LocalDateTime plusDays(LocalDateTime dateTime, int days) {
        return dateTime.plusDays(days);
    }

    /**
     * 日期时间减去指定的天数
     *
     * @param dateTime 日期时间
     * @param days     天数
     * @return 减去天数后的日期时间
     */
    public static LocalDateTime minusDays(LocalDateTime dateTime, int days) {
        return dateTime.minusDays(days);
    }

    /**
     * 获取指定日期的开始时间
     *
     * @param date 日期
     * @return 该日期的开始时间（00:00:00）
     */
    public static LocalDateTime startOfDay(LocalDate date) {
        return date.atStartOfDay();
    }

    /**
     * 获取指定日期的结束时间
     *
     * @param date 日期
     * @return 该日期的结束时间（23:59:59.999999999）
     */
    public static LocalDateTime endOfDay(LocalDate date) {
        return date.atTime(LocalTime.MAX);
    }

    /**
     * 获取当前时区
     *
     * @return 当前时区
     */
    public static ZoneId currentZone() {
        return ZoneId.systemDefault();
    }

    /**
     * 获取UTC时区
     *
     * @return UTC时区
     */
    public static ZoneId utcZone() {
        return ZoneId.of("UTC");
    }

    /**
     * 将日期时间转换为指定时区
     *
     * @param dateTime 日期时间
     * @param zone     目标时区
     * @return 转换后的日期时间
     */
    public static LocalDateTime toZone(LocalDateTime dateTime, ZoneId zone) {
        return dateTime.atZone(ZoneId.systemDefault()).withZoneSameInstant(zone).toLocalDateTime();
    }

    /**
     * 将日期时间转换为UTC时间
     *
     * @param dateTime 日期时间
     * @return UTC时间
     */
    public static LocalDateTime toUtc(LocalDateTime dateTime) {
        return toZone(dateTime, utcZone());
    }

    /**
     * 将UTC时间转换为本地时间
     *
     * @param utcDateTime UTC日期时间
     * @return 本地时间
     */
    public static LocalDateTime fromUtc(LocalDateTime utcDateTime) {
        return toZone(utcDateTime, currentZone());
    }

    /**
     * 字符串日期转Date对象
     *
     * @param dateStr 日期字符串
     * @param format  日期格式
     * @return Date对象
     */
    public static Date parseDate(String dateStr, String format) {
        try {
            return new SimpleDateFormat(format).parse(dateStr);
        } catch (ParseException e) {
            log.error("解析日期字符串失败: {}", dateStr, e);
            throw new IllegalArgumentException("日期格式不正确: " + dateStr);
        }
    }

    /**
     * 将LocalDateTime转换为毫秒时间戳
     *
     * @param dateTime 日期时间对象
     * @return 毫秒时间戳
     */
    public static long toEpochMilli(LocalDateTime dateTime) {
        return dateTime.atZone(ZoneOffset.UTC).toInstant().toEpochMilli();
    }
}