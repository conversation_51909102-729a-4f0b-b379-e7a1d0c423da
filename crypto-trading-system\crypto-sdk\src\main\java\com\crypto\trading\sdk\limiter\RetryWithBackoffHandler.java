package com.crypto.trading.sdk.limiter;

import com.crypto.trading.common.constant.APILimitConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.concurrent.ThreadLocalRandom;
import java.util.function.Supplier;

/**
 * 带退避策略的重试处理器
 * <p>
 * 实现指数退避重试策略，处理API限流和临时错误
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
public class RetryWithBackoffHandler {

    private static final Logger log = LoggerFactory.getLogger(RetryWithBackoffHandler.class);
    
    /**
     * 使用退避策略执行操作
     *
     * @param operation    要执行的操作
     * @param errorMessage 错误消息前缀
     * @param <T>          返回类型
     * @return 操作结果
     */
    public <T> T executeWithRetry(Supplier<T> operation, String errorMessage) {
        int retries = 0;
        long delay = APILimitConstants.RATE_LIMIT_BACKOFF_INITIAL_DELAY_MS;
        
        while (retries < APILimitConstants.RATE_LIMIT_MAX_RETRIES) {
            try {
                return operation.get();
            } catch (Exception e) {
                retries++;
                
                // 判断是否是限流错误或网络错误
                if (isRateLimitError(e) || isNetworkError(e)) {
                    if (retries >= APILimitConstants.RATE_LIMIT_MAX_RETRIES) {
                        log.error("{}，已达到最大重试次数: {}", errorMessage, retries, e);
                        throw e;
                    }
                    
                    // 计算下一次重试的延迟时间
                    delay = calculateBackoffDelay(delay, retries);
                    
                    log.warn("{}，将在{}ms后进行第{}次重试: {}", errorMessage, delay, retries, e.getMessage());
                    
                    try {
                        Thread.sleep(delay);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        log.warn("重试等待被中断");
                        throw e;
                    }
                } else {
                    // 不是限流错误，直接抛出
                    log.error("{}，非限流或网络错误，不进行重试: {}", errorMessage, e.getMessage(), e);
                    throw e;
                }
            }
        }
        
        // 不应该到达这里，但为了编译器满意
        throw new RuntimeException(errorMessage + "，重试失败");
    }
    
    /**
     * 判断是否是限流错误
     *
     * @param e 异常
     * @return 是否是限流错误
     */
    private boolean isRateLimitError(Exception e) {
        String message = e.getMessage();
        if (message == null) {
            return false;
        }
        
        // 检查常见的限流错误消息
        return message.contains("429") || // HTTP 429 Too Many Requests
               message.contains("too many requests") ||
               message.contains("rate limit") ||
               message.contains("too frequent") ||
               message.contains("WAF Limit") || // Binance WAF限制
               message.contains("IP banned"); // IP被封禁
    }
    
    /**
     * 判断是否是网络错误
     *
     * @param e 异常
     * @return 是否是网络错误
     */
    private boolean isNetworkError(Exception e) {
        String message = e.getMessage();
        if (message == null) {
            return false;
        }
        
        // 检查常见的网络错误消息
        return message.contains("timeout") ||
               message.contains("connection") ||
               message.contains("socket") ||
               message.contains("reset") ||
               message.contains("refused") ||
               message.contains("unavailable");
    }
    
    /**
     * 计算退避延迟时间
     *
     * @param currentDelay 当前延迟时间
     * @param retryCount   重试次数
     * @return 计算后的延迟时间
     */
    private long calculateBackoffDelay(long currentDelay, int retryCount) {
        // 指数退避
        double exponentialDelay = currentDelay * Math.pow(APILimitConstants.RATE_LIMIT_BACKOFF_FACTOR, retryCount - 1);
        
        // 添加随机抖动，避免多个客户端同时重试
        double jitter = ThreadLocalRandom.current().nextDouble() * APILimitConstants.RATE_LIMIT_BACKOFF_JITTER * exponentialDelay;
        
        // 计算最终延迟时间，并确保不超过最大延迟
        return Math.min((long) (exponentialDelay + jitter), APILimitConstants.RATE_LIMIT_BACKOFF_MAX_DELAY_MS);
    }
}