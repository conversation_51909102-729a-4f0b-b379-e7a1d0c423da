"""
数据管道模块

实现从Java模块获取数据、数据预处理、特征工程等功能，
支持多种数据源和实时数据处理。

Author: Crypto ML Strategy Team
Date: 2024-12-19
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Any, Union
from datetime import datetime, timedelta
import logging
import requests
import json
import asyncio
import aiohttp
from concurrent.futures import ThreadPoolExecutor
import threading

from .training_config import (
    DataSourceConfig, FeatureConfig, DataSource, FeatureType,
    TrainingPipelineConfig
)

logger = logging.getLogger(__name__)


class DataPipeline:
    """数据管道类"""
    
    def __init__(self, config: TrainingPipelineConfig):
        """
        初始化数据管道
        
        Args:
            config: 训练管道配置
        """
        self.config = config
        self.data_source_config = config.data_source
        self.feature_config = config.feature_config
        
        # 数据缓存
        self.data_cache: Dict[str, pd.DataFrame] = {}
        self.feature_cache: Dict[str, np.ndarray] = {}
        
        # 线程池
        self.executor = ThreadPoolExecutor(max_workers=4)
        
        # 锁
        self._lock = threading.RLock()
        
        logger.info("Data pipeline initialized")
    
    async def fetch_data_from_java_api(
        self,
        symbol: str,
        start_time: datetime,
        end_time: datetime,
        timeframe: str = "1h"
    ) -> pd.DataFrame:
        """
        从Java API获取数据
        
        Args:
            symbol: 交易对符号
            start_time: 开始时间
            end_time: 结束时间
            timeframe: 时间框架
            
        Returns:
            价格数据DataFrame
        """
        try:
            if not self.data_source_config.api_endpoint:
                raise ValueError("Java API endpoint not configured")
            
            # 构建API请求参数
            params = {
                'symbol': symbol,
                'start_time': start_time.isoformat(),
                'end_time': end_time.isoformat(),
                'timeframe': timeframe
            }
            
            # 添加连接参数
            params.update(self.data_source_config.connection_params)
            
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    self.data_source_config.api_endpoint,
                    params=params,
                    timeout=aiohttp.ClientTimeout(total=self.data_source_config.timeout)
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        # 转换为DataFrame
                        df = pd.DataFrame(data)
                        
                        # 确保必要的列存在
                        required_columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume']
                        if not all(col in df.columns for col in required_columns):
                            raise ValueError(f"Missing required columns: {required_columns}")
                        
                        # 转换时间戳
                        df['timestamp'] = pd.to_datetime(df['timestamp'])
                        df.set_index('timestamp', inplace=True)
                        
                        # 数据类型转换
                        for col in ['open', 'high', 'low', 'close', 'volume']:
                            df[col] = pd.to_numeric(df[col], errors='coerce')
                        
                        logger.info(f"Fetched {len(df)} records for {symbol} from Java API")
                        return df
                    
                    else:
                        raise Exception(f"API request failed with status {response.status}")
                        
        except Exception as e:
            logger.error(f"Error fetching data from Java API: {e}")
            
            # 重试机制
            for attempt in range(self.data_source_config.retry_attempts):
                try:
                    logger.info(f"Retrying API request, attempt {attempt + 1}")
                    await asyncio.sleep(2 ** attempt)  # 指数退避
                    
                    # 重复请求逻辑
                    async with aiohttp.ClientSession() as session:
                        async with session.get(
                            self.data_source_config.api_endpoint,
                            params=params,
                            timeout=aiohttp.ClientTimeout(total=self.data_source_config.timeout)
                        ) as response:
                            if response.status == 200:
                                data = await response.json()
                                df = pd.DataFrame(data)
                                df['timestamp'] = pd.to_datetime(df['timestamp'])
                                df.set_index('timestamp', inplace=True)
                                return df
                                
                except Exception as retry_error:
                    logger.warning(f"Retry attempt {attempt + 1} failed: {retry_error}")
                    continue
            
            # 如果所有重试都失败，返回空DataFrame
            logger.error("All retry attempts failed, returning empty DataFrame")
            return pd.DataFrame()
    
    def fetch_data_from_file(self, file_path: str) -> pd.DataFrame:
        """
        从文件获取数据
        
        Args:
            file_path: 文件路径
            
        Returns:
            价格数据DataFrame
        """
        try:
            if file_path.endswith('.csv'):
                df = pd.read_csv(file_path)
            elif file_path.endswith('.parquet'):
                df = pd.read_parquet(file_path)
            elif file_path.endswith('.json'):
                df = pd.read_json(file_path)
            else:
                raise ValueError(f"Unsupported file format: {file_path}")
            
            # 处理时间戳列
            if 'timestamp' in df.columns:
                df['timestamp'] = pd.to_datetime(df['timestamp'])
                df.set_index('timestamp', inplace=True)
            
            logger.info(f"Loaded {len(df)} records from file: {file_path}")
            return df
            
        except Exception as e:
            logger.error(f"Error loading data from file {file_path}: {e}")
            return pd.DataFrame()
    
    def generate_mock_data(
        self,
        symbol: str,
        start_time: datetime,
        end_time: datetime,
        timeframe: str = "1h"
    ) -> pd.DataFrame:
        """
        生成模拟数据
        
        Args:
            symbol: 交易对符号
            start_time: 开始时间
            end_time: 结束时间
            timeframe: 时间框架
            
        Returns:
            模拟价格数据DataFrame
        """
        try:
            # 生成时间序列
            freq_map = {
                '1m': '1T', '5m': '5T', '15m': '15T', '30m': '30T',
                '1h': '1H', '4h': '4H', '1d': '1D'
            }
            freq = freq_map.get(timeframe, '1H')
            
            timestamps = pd.date_range(start=start_time, end=end_time, freq=freq)
            
            # 生成价格数据（随机游走）
            np.random.seed(42)  # 确保可重复性
            returns = np.random.normal(0, 0.02, len(timestamps))
            
            # 初始价格
            initial_price = 50000 if 'BTC' in symbol else 3000
            
            # 计算价格序列
            prices = [initial_price]
            for ret in returns[1:]:
                new_price = prices[-1] * (1 + ret)
                prices.append(new_price)
            
            # 创建OHLCV数据
            df = pd.DataFrame({
                'open': prices,
                'high': [p * (1 + np.random.uniform(0, 0.01)) for p in prices],
                'low': [p * (1 - np.random.uniform(0, 0.01)) for p in prices],
                'close': prices,
                'volume': np.random.uniform(100, 1000, len(timestamps))
            }, index=timestamps)
            
            logger.info(f"Generated {len(df)} mock records for {symbol}")
            return df
            
        except Exception as e:
            logger.error(f"Error generating mock data: {e}")
            return pd.DataFrame()
    
    async def get_data(
        self,
        symbol: str,
        start_time: datetime,
        end_time: datetime,
        timeframe: str = "1h"
    ) -> pd.DataFrame:
        """
        获取数据（统一接口）
        
        Args:
            symbol: 交易对符号
            start_time: 开始时间
            end_time: 结束时间
            timeframe: 时间框架
            
        Returns:
            价格数据DataFrame
        """
        try:
            # 检查缓存
            cache_key = f"{symbol}_{timeframe}_{start_time}_{end_time}"
            if cache_key in self.data_cache:
                logger.debug(f"Using cached data for {cache_key}")
                return self.data_cache[cache_key].copy()
            
            # 根据数据源类型获取数据
            if self.data_source_config.source_type == DataSource.JAVA_API:
                df = await self.fetch_data_from_java_api(symbol, start_time, end_time, timeframe)
            
            elif self.data_source_config.source_type == DataSource.FILE:
                if self.data_source_config.file_path:
                    df = self.fetch_data_from_file(self.data_source_config.file_path)
                else:
                    raise ValueError("File path not specified")
            
            elif self.data_source_config.source_type == DataSource.MOCK:
                df = self.generate_mock_data(symbol, start_time, end_time, timeframe)
            
            else:
                raise ValueError(f"Unsupported data source: {self.data_source_config.source_type}")
            
            # 数据质量检查
            df = self.validate_and_clean_data(df)
            
            # 缓存数据
            with self._lock:
                self.data_cache[cache_key] = df.copy()
                
                # 限制缓存大小
                if len(self.data_cache) > 100:
                    # 删除最旧的缓存项
                    oldest_key = next(iter(self.data_cache))
                    del self.data_cache[oldest_key]
            
            return df
            
        except Exception as e:
            logger.error(f"Error getting data for {symbol}: {e}")
            return pd.DataFrame()
    
    def validate_and_clean_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        验证和清洗数据
        
        Args:
            df: 原始数据DataFrame
            
        Returns:
            清洗后的数据DataFrame
        """
        try:
            if df.empty:
                return df
            
            # 删除重复行
            df = df.drop_duplicates()
            
            # 处理缺失值
            df = df.dropna()
            
            # 检查价格数据的合理性
            price_columns = ['open', 'high', 'low', 'close']
            for col in price_columns:
                if col in df.columns:
                    # 删除负价格或零价格
                    df = df[df[col] > 0]
                    
                    # 删除异常值（超过3个标准差）
                    mean_val = df[col].mean()
                    std_val = df[col].std()
                    df = df[abs(df[col] - mean_val) <= 3 * std_val]
            
            # 检查OHLC逻辑
            if all(col in df.columns for col in price_columns):
                # High应该是最高价
                df = df[df['high'] >= df[['open', 'close']].max(axis=1)]
                # Low应该是最低价
                df = df[df['low'] <= df[['open', 'close']].min(axis=1)]
            
            # 检查成交量
            if 'volume' in df.columns:
                df = df[df['volume'] >= 0]
            
            # 按时间排序
            df = df.sort_index()
            
            logger.debug(f"Data validation completed, {len(df)} records remaining")
            return df
            
        except Exception as e:
            logger.error(f"Error validating and cleaning data: {e}")
            return df
    
    def extract_technical_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        提取技术指标特征
        
        Args:
            df: 价格数据DataFrame
            
        Returns:
            包含技术指标的DataFrame
        """
        try:
            if df.empty:
                return df
            
            features_df = df.copy()
            
            # 导入技术指标模块
            try:
                import sys
                import os
                sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
                
                from indicators.sma import SMAIndicator
                from indicators.ema import EMAIndicator
                from indicators.rsi import RSIIndicator
                from indicators.macd import MACDIndicator
                from indicators.bollinger_bands import BollingerBandsIndicator
                from indicators.atr import ATRIndicator
                
                # 计算技术指标
                if 'sma' in self.feature_config.technical_indicators:
                    sma = SMAIndicator()
                    features_df['sma_20'] = sma.calculate(df['close'], period=20)
                    features_df['sma_50'] = sma.calculate(df['close'], period=50)
                
                if 'ema' in self.feature_config.technical_indicators:
                    ema = EMAIndicator()
                    features_df['ema_12'] = ema.calculate(df['close'], period=12)
                    features_df['ema_26'] = ema.calculate(df['close'], period=26)
                
                if 'rsi' in self.feature_config.technical_indicators:
                    rsi = RSIIndicator()
                    features_df['rsi'] = rsi.calculate(df['close'])
                
                if 'macd' in self.feature_config.technical_indicators:
                    macd = MACDIndicator()
                    macd_result = macd.calculate(df['close'])
                    features_df['macd'] = macd_result['macd']
                    features_df['macd_signal'] = macd_result['signal']
                    features_df['macd_histogram'] = macd_result['histogram']
                
                if 'bollinger' in self.feature_config.technical_indicators:
                    bb = BollingerBandsIndicator()
                    bb_result = bb.calculate(df['close'])
                    features_df['bb_upper'] = bb_result['upper']
                    features_df['bb_middle'] = bb_result['middle']
                    features_df['bb_lower'] = bb_result['lower']
                
                if 'atr' in self.feature_config.technical_indicators:
                    atr = ATRIndicator()
                    features_df['atr'] = atr.calculate(df['high'], df['low'], df['close'])
                
            except ImportError as e:
                logger.warning(f"Could not import technical indicators: {e}")
                # 使用简单的技术指标计算
                features_df['sma_20'] = df['close'].rolling(window=20).mean()
                features_df['ema_12'] = df['close'].ewm(span=12).mean()
                features_df['rsi'] = self._calculate_simple_rsi(df['close'])
            
            logger.debug(f"Extracted technical indicators, {features_df.shape[1]} features")
            return features_df
            
        except Exception as e:
            logger.error(f"Error extracting technical indicators: {e}")
            return df
    
    def _calculate_simple_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """计算简单RSI"""
        try:
            delta = prices.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            return rsi
        except Exception as e:
            logger.error(f"Error calculating simple RSI: {e}")
            return pd.Series(index=prices.index, dtype=float)
    
    def extract_price_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        提取价格特征
        
        Args:
            df: 价格数据DataFrame
            
        Returns:
            包含价格特征的DataFrame
        """
        try:
            if df.empty:
                return df
            
            features_df = df.copy()
            
            # 价格变化特征
            features_df['price_change'] = df['close'].pct_change()
            features_df['price_change_abs'] = features_df['price_change'].abs()
            
            # 价格位置特征
            features_df['hl_ratio'] = (df['high'] - df['low']) / df['close']
            features_df['oc_ratio'] = (df['open'] - df['close']) / df['close']
            
            # 价格动量特征
            for period in [5, 10, 20]:
                features_df[f'momentum_{period}'] = df['close'] / df['close'].shift(period) - 1
                features_df[f'volatility_{period}'] = df['close'].pct_change().rolling(window=period).std()
            
            # 价格趋势特征
            features_df['trend_5'] = (df['close'] > df['close'].shift(5)).astype(int)
            features_df['trend_20'] = (df['close'] > df['close'].shift(20)).astype(int)
            
            logger.debug(f"Extracted price features, {features_df.shape[1]} features")
            return features_df
            
        except Exception as e:
            logger.error(f"Error extracting price features: {e}")
            return df
    
    def extract_volume_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        提取成交量特征
        
        Args:
            df: 价格数据DataFrame
            
        Returns:
            包含成交量特征的DataFrame
        """
        try:
            if df.empty or 'volume' not in df.columns:
                return df
            
            features_df = df.copy()
            
            # 成交量变化特征
            features_df['volume_change'] = df['volume'].pct_change()
            features_df['volume_ma_5'] = df['volume'].rolling(window=5).mean()
            features_df['volume_ma_20'] = df['volume'].rolling(window=20).mean()
            
            # 成交量相对特征
            features_df['volume_ratio'] = df['volume'] / features_df['volume_ma_20']
            
            # 价量关系特征
            features_df['price_volume_trend'] = (
                (df['close'] > df['close'].shift(1)) & 
                (df['volume'] > features_df['volume_ma_5'])
            ).astype(int)
            
            logger.debug(f"Extracted volume features, {features_df.shape[1]} features")
            return features_df
            
        except Exception as e:
            logger.error(f"Error extracting volume features: {e}")
            return df
    
    def extract_time_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        提取时间特征
        
        Args:
            df: 价格数据DataFrame
            
        Returns:
            包含时间特征的DataFrame
        """
        try:
            if df.empty:
                return df
            
            features_df = df.copy()
            
            # 时间周期特征
            features_df['hour'] = df.index.hour
            features_df['day_of_week'] = df.index.dayofweek
            features_df['day_of_month'] = df.index.day
            features_df['month'] = df.index.month
            
            # 周期性特征（正弦余弦编码）
            features_df['hour_sin'] = np.sin(2 * np.pi * features_df['hour'] / 24)
            features_df['hour_cos'] = np.cos(2 * np.pi * features_df['hour'] / 24)
            features_df['day_sin'] = np.sin(2 * np.pi * features_df['day_of_week'] / 7)
            features_df['day_cos'] = np.cos(2 * np.pi * features_df['day_of_week'] / 7)
            
            logger.debug(f"Extracted time features, {features_df.shape[1]} features")
            return features_df
            
        except Exception as e:
            logger.error(f"Error extracting time features: {e}")
            return df
    
    async def prepare_features(
        self,
        symbol: str,
        start_time: datetime,
        end_time: datetime,
        timeframes: Optional[List[str]] = None
    ) -> pd.DataFrame:
        """
        准备特征数据
        
        Args:
            symbol: 交易对符号
            start_time: 开始时间
            end_time: 结束时间
            timeframes: 时间框架列表
            
        Returns:
            特征数据DataFrame
        """
        try:
            if timeframes is None:
                timeframes = self.feature_config.timeframes
            
            all_features = []
            
            # 为每个时间框架提取特征
            for timeframe in timeframes:
                df = await self.get_data(symbol, start_time, end_time, timeframe)
                
                if df.empty:
                    continue
                
                # 根据配置提取不同类型的特征
                if FeatureType.TECHNICAL_INDICATORS in self.feature_config.feature_types:
                    df = self.extract_technical_indicators(df)
                
                if FeatureType.PRICE_FEATURES in self.feature_config.feature_types:
                    df = self.extract_price_features(df)
                
                if FeatureType.VOLUME_FEATURES in self.feature_config.feature_types:
                    df = self.extract_volume_features(df)
                
                if FeatureType.TIME_FEATURES in self.feature_config.feature_types:
                    df = self.extract_time_features(df)
                
                # 添加时间框架前缀
                if len(timeframes) > 1:
                    df.columns = [f"{timeframe}_{col}" for col in df.columns]
                
                all_features.append(df)
            
            # 合并多时间框架特征
            if len(all_features) > 1:
                # 使用最高频率的时间框架作为基准
                base_df = all_features[0]
                for feature_df in all_features[1:]:
                    # 重采样到基准时间框架
                    resampled_df = feature_df.resample(base_df.index.freq).last()
                    base_df = base_df.join(resampled_df, how='left')
                
                features_df = base_df
            else:
                features_df = all_features[0] if all_features else pd.DataFrame()
            
            # 删除缺失值
            features_df = features_df.dropna()
            
            logger.info(f"Prepared features for {symbol}: {features_df.shape}")
            return features_df
            
        except Exception as e:
            logger.error(f"Error preparing features for {symbol}: {e}")
            return pd.DataFrame()
    
    def clear_cache(self) -> None:
        """清理缓存"""
        try:
            with self._lock:
                self.data_cache.clear()
                self.feature_cache.clear()
            logger.info("Data pipeline cache cleared")
        except Exception as e:
            logger.error(f"Error clearing cache: {e}")
    
    def get_cache_info(self) -> Dict[str, Any]:
        """获取缓存信息"""
        try:
            with self._lock:
                return {
                    'data_cache_size': len(self.data_cache),
                    'feature_cache_size': len(self.feature_cache),
                    'data_cache_keys': list(self.data_cache.keys()),
                    'feature_cache_keys': list(self.feature_cache.keys())
                }
        except Exception as e:
            logger.error(f"Error getting cache info: {e}")
            return {'error': str(e)}
