#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
性能基准测试模块

该模块提供跨语言通信性能基准测试功能，包括延迟测试、吞吐量测试、
资源使用测试和可扩展性测试。
"""

import time
import asyncio
import threading
import psutil
import gc
from typing import Dict, List, Any, Optional, Callable, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime, timezone
from concurrent.futures import ThreadPoolExecutor, as_completed
import statistics
import json
import uuid
from loguru import logger

from .config import Config
from .kafka_client import KafkaClient
from .java_api_core import ValidationResult, PerformanceMetrics


@dataclass
class BenchmarkConfig:
    """基准测试配置"""
    test_duration_seconds: int = 60
    warmup_duration_seconds: int = 10
    concurrent_operations: int = 10
    message_size_bytes: int = 1024
    target_throughput_ops_per_sec: int = 100
    max_latency_ms: float = 100.0
    memory_limit_mb: int = 512


@dataclass
class ResourceUsageSnapshot:
    """资源使用快照"""
    timestamp: datetime
    cpu_percent: float
    memory_mb: float
    memory_percent: float
    network_bytes_sent: int
    network_bytes_recv: int
    thread_count: int
    gc_collections: int


class CrossLanguagePerformanceBenchmark:
    """
    跨语言通信性能基准测试器
    
    测试Python和Java之间的通信性能，包括延迟、吞吐量和资源使用。
    """
    
    def __init__(self, config: Config):
        """
        初始化性能基准测试器
        
        Args:
            config: 配置对象
        """
        self.config = config
        self.kafka_client = KafkaClient(config)
        self.benchmark_config = BenchmarkConfig()
        self.test_results: List[ValidationResult] = []
        self.performance_metrics: List[PerformanceMetrics] = []
        self.resource_snapshots: List[ResourceUsageSnapshot] = []
        
        # 性能监控
        self.process = psutil.Process()
        self.initial_network_stats = psutil.net_io_counters()
        
        logger.info("跨语言性能基准测试器初始化完成")
    
    async def run_comprehensive_benchmark(self) -> Dict[str, Any]:
        """
        运行综合性能基准测试
        
        Returns:
            测试结果摘要
        """
        logger.info("开始综合性能基准测试")
        
        # 预热
        await self._warmup()
        
        # 延迟测试
        latency_results = await self.benchmark_latency()
        
        # 吞吐量测试
        throughput_results = await self.benchmark_throughput()
        
        # 资源使用测试
        resource_results = await self.benchmark_resource_usage()
        
        # 可扩展性测试
        scalability_results = await self.benchmark_scalability()
        
        # 生成综合报告
        summary = self._generate_benchmark_summary(
            latency_results, throughput_results, 
            resource_results, scalability_results
        )
        
        logger.info("综合性能基准测试完成")
        return summary
    
    async def _warmup(self) -> None:
        """预热系统"""
        logger.info(f"开始预热，持续{self.benchmark_config.warmup_duration_seconds}秒")
        
        end_time = time.time() + self.benchmark_config.warmup_duration_seconds
        
        while time.time() < end_time:
            # 发送预热消息
            test_message = self._create_test_message()
            await self._send_kafka_message(test_message)
            await asyncio.sleep(0.1)
        
        # 强制垃圾回收
        gc.collect()
        logger.info("预热完成")
    
    async def benchmark_latency(self) -> PerformanceMetrics:
        """
        延迟性能基准测试
        
        Returns:
            延迟性能指标
        """
        logger.info("开始延迟性能基准测试")
        
        latencies = []
        success_count = 0
        total_operations = 100
        
        for i in range(total_operations):
            try:
                start_time = time.time()
                
                # 创建测试消息
                test_message = self._create_test_message()
                
                # 发送消息并等待确认
                await self._send_kafka_message(test_message)
                
                end_time = time.time()
                latency_ms = (end_time - start_time) * 1000
                latencies.append(latency_ms)
                success_count += 1
                
            except Exception as e:
                logger.warning(f"延迟测试操作{i+1}失败: {e}")
                latencies.append(float('inf'))
        
        # 过滤无效延迟
        valid_latencies = [lat for lat in latencies if lat != float('inf')]
        
        if valid_latencies:
            avg_latency = statistics.mean(valid_latencies)
            max_latency = max(valid_latencies)
            min_latency = min(valid_latencies)
            p95_latency = statistics.quantiles(valid_latencies, n=20)[18]  # 95th percentile
            p99_latency = statistics.quantiles(valid_latencies, n=100)[98]  # 99th percentile
        else:
            avg_latency = max_latency = min_latency = p95_latency = p99_latency = float('inf')
        
        success_rate = success_count / total_operations
        
        metrics = PerformanceMetrics(
            operation="kafka_message_latency",
            avg_latency_ms=avg_latency,
            max_latency_ms=max_latency,
            min_latency_ms=min_latency,
            throughput_ops_per_sec=0,  # 延迟测试不关注吞吐量
            success_rate=success_rate,
            total_operations=total_operations
        )
        
        self.performance_metrics.append(metrics)
        
        logger.info(f"延迟测试完成: 平均{avg_latency:.2f}ms, "
                   f"P95: {p95_latency:.2f}ms, P99: {p99_latency:.2f}ms")
        
        return metrics
    
    async def benchmark_throughput(self) -> PerformanceMetrics:
        """
        吞吐量性能基准测试
        
        Returns:
            吞吐量性能指标
        """
        logger.info("开始吞吐量性能基准测试")
        
        start_time = time.time()
        end_time = start_time + self.benchmark_config.test_duration_seconds
        
        success_count = 0
        total_operations = 0
        latencies = []
        
        # 并发发送消息
        async def send_messages():
            nonlocal success_count, total_operations
            
            while time.time() < end_time:
                try:
                    op_start = time.time()
                    test_message = self._create_test_message()
                    await self._send_kafka_message(test_message)
                    op_end = time.time()
                    
                    latencies.append((op_end - op_start) * 1000)
                    success_count += 1
                    
                except Exception as e:
                    logger.warning(f"吞吐量测试操作失败: {e}")
                
                total_operations += 1
                
                # 控制发送速率
                await asyncio.sleep(0.01)
        
        # 启动并发任务
        tasks = [send_messages() for _ in range(self.benchmark_config.concurrent_operations)]
        await asyncio.gather(*tasks)
        
        actual_duration = time.time() - start_time
        throughput = success_count / actual_duration if actual_duration > 0 else 0
        success_rate = success_count / total_operations if total_operations > 0 else 0
        
        avg_latency = statistics.mean(latencies) if latencies else 0
        max_latency = max(latencies) if latencies else 0
        min_latency = min(latencies) if latencies else 0
        
        metrics = PerformanceMetrics(
            operation="kafka_message_throughput",
            avg_latency_ms=avg_latency,
            max_latency_ms=max_latency,
            min_latency_ms=min_latency,
            throughput_ops_per_sec=throughput,
            success_rate=success_rate,
            total_operations=total_operations
        )
        
        self.performance_metrics.append(metrics)
        
        logger.info(f"吞吐量测试完成: {throughput:.2f} ops/s, "
                   f"成功率: {success_rate:.2%}")
        
        return metrics
    
    async def benchmark_resource_usage(self) -> Dict[str, Any]:
        """
        资源使用性能基准测试
        
        Returns:
            资源使用指标
        """
        logger.info("开始资源使用性能基准测试")
        
        # 开始监控资源使用
        monitoring_task = asyncio.create_task(self._monitor_resource_usage())
        
        # 运行负载测试
        start_time = time.time()
        end_time = start_time + self.benchmark_config.test_duration_seconds
        
        operations = 0
        
        while time.time() < end_time:
            try:
                test_message = self._create_test_message()
                await self._send_kafka_message(test_message)
                operations += 1
                await asyncio.sleep(0.05)
                
            except Exception as e:
                logger.warning(f"资源测试操作失败: {e}")
        
        # 停止监控
        monitoring_task.cancel()
        
        # 分析资源使用
        resource_analysis = self._analyze_resource_usage()
        
        logger.info(f"资源使用测试完成，执行{operations}次操作")
        return resource_analysis
    
    async def _monitor_resource_usage(self) -> None:
        """监控资源使用"""
        while True:
            try:
                # 获取当前资源使用情况
                cpu_percent = self.process.cpu_percent()
                memory_info = self.process.memory_info()
                memory_mb = memory_info.rss / 1024 / 1024
                memory_percent = self.process.memory_percent()
                
                # 网络统计
                current_network = psutil.net_io_counters()
                network_sent = current_network.bytes_sent - self.initial_network_stats.bytes_sent
                network_recv = current_network.bytes_recv - self.initial_network_stats.bytes_recv
                
                # 线程数
                thread_count = self.process.num_threads()
                
                # GC统计
                gc_stats = gc.get_stats()
                gc_collections = sum(stat['collections'] for stat in gc_stats)
                
                snapshot = ResourceUsageSnapshot(
                    timestamp=datetime.now(timezone.utc),
                    cpu_percent=cpu_percent,
                    memory_mb=memory_mb,
                    memory_percent=memory_percent,
                    network_bytes_sent=network_sent,
                    network_bytes_recv=network_recv,
                    thread_count=thread_count,
                    gc_collections=gc_collections
                )
                
                self.resource_snapshots.append(snapshot)
                
                await asyncio.sleep(1)  # 每秒采样一次
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"资源监控异常: {e}")
                await asyncio.sleep(1)
    
    def _analyze_resource_usage(self) -> Dict[str, Any]:
        """分析资源使用情况"""
        if not self.resource_snapshots:
            return {"error": "没有资源使用数据"}
        
        cpu_values = [s.cpu_percent for s in self.resource_snapshots]
        memory_values = [s.memory_mb for s in self.resource_snapshots]
        
        return {
            "cpu_usage": {
                "avg_percent": statistics.mean(cpu_values),
                "max_percent": max(cpu_values),
                "min_percent": min(cpu_values)
            },
            "memory_usage": {
                "avg_mb": statistics.mean(memory_values),
                "max_mb": max(memory_values),
                "min_mb": min(memory_values),
                "peak_percent": max(s.memory_percent for s in self.resource_snapshots)
            },
            "network_usage": {
                "total_sent_mb": self.resource_snapshots[-1].network_bytes_sent / 1024 / 1024,
                "total_recv_mb": self.resource_snapshots[-1].network_bytes_recv / 1024 / 1024
            },
            "thread_count": {
                "avg": statistics.mean([s.thread_count for s in self.resource_snapshots]),
                "max": max(s.thread_count for s in self.resource_snapshots)
            },
            "gc_collections": self.resource_snapshots[-1].gc_collections,
            "sample_count": len(self.resource_snapshots)
        }
    
    async def benchmark_scalability(self) -> Dict[str, PerformanceMetrics]:
        """
        可扩展性性能基准测试
        
        Returns:
            不同并发级别的性能指标
        """
        logger.info("开始可扩展性性能基准测试")
        
        concurrency_levels = [1, 5, 10, 20, 50]
        scalability_results = {}
        
        for concurrency in concurrency_levels:
            logger.info(f"测试并发级别: {concurrency}")
            
            # 临时调整并发配置
            original_concurrency = self.benchmark_config.concurrent_operations
            self.benchmark_config.concurrent_operations = concurrency
            
            # 运行吞吐量测试
            metrics = await self.benchmark_throughput()
            scalability_results[f"concurrency_{concurrency}"] = metrics
            
            # 恢复原始配置
            self.benchmark_config.concurrent_operations = original_concurrency
            
            # 短暂休息，避免系统过载
            await asyncio.sleep(2)
        
        logger.info("可扩展性测试完成")
        return scalability_results
    
    def _create_test_message(self) -> Dict[str, Any]:
        """创建测试消息"""
        return {
            "messageId": str(uuid.uuid4()),
            "messageType": "performance_test",
            "timestamp": int(time.time() * 1000),
            "data": {
                "test": True,
                "payload": "x" * self.benchmark_config.message_size_bytes
            }
        }
    
    async def _send_kafka_message(self, message: Dict[str, Any]) -> None:
        """发送Kafka消息"""
        topic = self.config.get('kafka', 'signal_topic')
        message_json = json.dumps(message)
        
        self.kafka_client.producer.produce(
            topic,
            value=message_json.encode('utf-8'),
            key=message['messageId'].encode('utf-8')
        )
        self.kafka_client.producer.flush()
    
    def _generate_benchmark_summary(self, 
                                   latency_results: PerformanceMetrics,
                                   throughput_results: PerformanceMetrics,
                                   resource_results: Dict[str, Any],
                                   scalability_results: Dict[str, PerformanceMetrics]) -> Dict[str, Any]:
        """生成基准测试摘要"""
        return {
            "test_timestamp": datetime.now(timezone.utc).isoformat(),
            "latency_performance": {
                "avg_latency_ms": latency_results.avg_latency_ms,
                "max_latency_ms": latency_results.max_latency_ms,
                "min_latency_ms": latency_results.min_latency_ms,
                "success_rate": latency_results.success_rate,
                "target_met": latency_results.avg_latency_ms < self.benchmark_config.max_latency_ms
            },
            "throughput_performance": {
                "throughput_ops_per_sec": throughput_results.throughput_ops_per_sec,
                "success_rate": throughput_results.success_rate,
                "target_met": throughput_results.throughput_ops_per_sec >= self.benchmark_config.target_throughput_ops_per_sec
            },
            "resource_usage": resource_results,
            "scalability": {
                name: {
                    "throughput": metrics.throughput_ops_per_sec,
                    "avg_latency": metrics.avg_latency_ms,
                    "success_rate": metrics.success_rate
                }
                for name, metrics in scalability_results.items()
            },
            "overall_assessment": self._assess_overall_performance(
                latency_results, throughput_results, resource_results
            )
        }
    
    def _assess_overall_performance(self, 
                                   latency_results: PerformanceMetrics,
                                   throughput_results: PerformanceMetrics,
                                   resource_results: Dict[str, Any]) -> Dict[str, Any]:
        """评估整体性能"""
        # 延迟评估
        latency_score = 100 if latency_results.avg_latency_ms < 10 else \
                       80 if latency_results.avg_latency_ms < 50 else \
                       60 if latency_results.avg_latency_ms < 100 else 40
        
        # 吞吐量评估
        throughput_score = 100 if throughput_results.throughput_ops_per_sec > 200 else \
                          80 if throughput_results.throughput_ops_per_sec > 100 else \
                          60 if throughput_results.throughput_ops_per_sec > 50 else 40
        
        # 资源使用评估
        memory_usage = resource_results.get('memory_usage', {}).get('max_mb', 0)
        cpu_usage = resource_results.get('cpu_usage', {}).get('avg_percent', 0)
        
        resource_score = 100 if memory_usage < 256 and cpu_usage < 50 else \
                        80 if memory_usage < 512 and cpu_usage < 70 else \
                        60 if memory_usage < 1024 and cpu_usage < 90 else 40
        
        overall_score = (latency_score + throughput_score + resource_score) / 3
        
        return {
            "latency_score": latency_score,
            "throughput_score": throughput_score,
            "resource_score": resource_score,
            "overall_score": overall_score,
            "grade": "A" if overall_score >= 90 else \
                    "B" if overall_score >= 80 else \
                    "C" if overall_score >= 70 else \
                    "D" if overall_score >= 60 else "F",
            "recommendations": self._generate_recommendations(
                latency_score, throughput_score, resource_score
            )
        }
    
    def _generate_recommendations(self, latency_score: int, 
                                 throughput_score: int, 
                                 resource_score: int) -> List[str]:
        """生成性能优化建议"""
        recommendations = []
        
        if latency_score < 80:
            recommendations.append("考虑优化消息序列化/反序列化性能")
            recommendations.append("检查网络延迟和Kafka配置")
        
        if throughput_score < 80:
            recommendations.append("增加并发处理能力")
            recommendations.append("优化批处理大小和频率")
        
        if resource_score < 80:
            recommendations.append("优化内存使用，考虑对象池和缓存策略")
            recommendations.append("检查CPU密集型操作，考虑异步处理")
        
        if not recommendations:
            recommendations.append("性能表现良好，继续保持当前配置")
        
        return recommendations


if __name__ == "__main__":
    # 测试代码
    import asyncio
    from .config import Config
    
    async def main():
        config = Config()
        benchmark = CrossLanguagePerformanceBenchmark(config)
        
        # 运行综合基准测试
        results = await benchmark.run_comprehensive_benchmark()
        
        logger.info("基准测试结果:")
        logger.info(json.dumps(results, indent=2, ensure_ascii=False))
    
    asyncio.run(main())