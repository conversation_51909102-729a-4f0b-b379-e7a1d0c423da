<?xml version="1.0" encoding="UTF-8"?>
<!-- Do not edit this file with editors other than diagrams.net -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="996px" height="907px" viewBox="-0.5 -0.5 996 907" content="&lt;mxfile host=&quot;app.diagrams.net&quot; modified=&quot;2022-10-28T01:37:18.298Z&quot; agent=&quot;5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/106.0.0.0 Safari/537.36&quot; version=&quot;20.5.1&quot; etag=&quot;O0NEUjOfG2g9B_EYoM8d&quot; type=&quot;google&quot;&gt;&lt;diagram id=&quot;TpP8zUZyJPd6VWPL6ub1&quot;&gt;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&lt;/diagram&gt;&lt;/mxfile&gt;"><defs/><g><path d="M 45.19 431 L 229.19 431 L 229.19 444 L 219.19 459 L 45.19 459 Z" fill="#d5e8d4" stroke="#82b366" stroke-miterlimit="10" pointer-events="all"/><path d="M 229.19 431 L 797 431 L 797 620 L 45.19 620 L 45.19 459" fill="none" stroke="#82b366" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 182px; height: 1px; padding-top: 438px; margin-left: 46px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style=""><span style="font-size: 14px;"><b>Reinforcement  Learning</b></span></font></div></div></div></foreignObject><text x="137" y="450" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Reinforcement  Learning</text></switch></g><rect x="428" y="437" width="316" height="177" fill="#d5e8d4" stroke="#82b366" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 314px; height: 1px; padding-top: 444px; margin-left: 429px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><b><span style="font-size: 16px;">Environment</span><br /></b></div></div></div></foreignObject><text x="586" y="456" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Environment&#xa;</text></switch></g><rect x="448.27" y="488" width="281.73" height="98" fill="#c5d6c4" stroke="#82b366" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 288px; height: 1px; padding-top: 491px; margin-left: 445px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><b><font style="font-size: 14px;">Simulator</font><br /></b></div></div></div></foreignObject><text x="589" y="503" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Simulator&#xa;</text></switch></g><rect x="45.19" y="112" width="618" height="296" fill="#fff2cc" stroke="#d6b656" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 616px; height: 1px; padding-top: 119px; margin-left: 46px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><b>Strategy</b></div></div></div></foreignObject><text x="354" y="133" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="14px" text-anchor="middle">Strategy</text></switch></g><path d="M 76 141.43 L 364 141.43 L 364 155.43 L 354 170.43 L 76 170.43 Z" fill="#f5e8c4" stroke="#d6b656" stroke-miterlimit="10" pointer-events="all"/><path d="M 364 141.43 L 583 141.43 L 583 271 L 76 271 L 76 170.43" fill="none" stroke="#d6b656" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 277px; height: 1px; padding-top: 148px; margin-left: 87px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><b><font style="font-size: 14px;">Supervised-Learning</font></b><span style="font-size: 14px;"><b>-based Strategy</b></span></div></div></div></foreignObject><text x="87" y="160" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Supervised-Learning-based Strategy</text></switch></g><rect x="92" y="507" width="228" height="74" fill="#d5e8d4" stroke="#82b366" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 226px; height: 1px; padding-top: 514px; margin-left: 93px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><b style="font-size: 12px;"><font style="font-size: 14px;">Policy</font></b></div></div></div></foreignObject><text x="206" y="528" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="14px" text-anchor="middle">Policy</text></switch></g><path d="M 45.19 628 L 209.19 628 L 209.19 640 L 199.19 655 L 45.19 655 Z" fill="#d5e8d4" stroke="#82b366" stroke-miterlimit="10" pointer-events="all"/><path d="M 209.19 628 L 798.28 628 L 798.28 767 L 45.19 767 L 45.19 655" fill="none" stroke="#82b366" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 162px; height: 1px; padding-top: 635px; margin-left: 46px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><b><font style="font-size: 14px">Supervised Learning</font></b></div></div></div></foreignObject><text x="127" y="647" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Supervised Learning</text></switch></g><rect x="-71" y="262" width="261" height="18.44" fill="none" stroke="none" transform="rotate(90,59.5,271.22)" pointer-events="all"/><path d="M -71.31 262.35 C -71.31 262.35 -71.31 262.35 -71.31 262.35 M -71.31 262.35 C -71.31 262.35 -71.31 262.35 -71.31 262.35 M -70.91 268 C -68.68 265.49 -66.31 263.04 -65.66 261.96 M -70.91 268 C -69.33 265.86 -67.64 264.71 -65.66 261.96 M -71.17 274.39 C -66.54 268.68 -62.21 264.57 -60.68 262.32 M -71.17 274.39 C -68.29 270.73 -65.56 267.3 -60.68 262.32 M -70.78 280.04 C -64.14 271.47 -56.49 267.15 -55.03 261.92 M -70.78 280.04 C -64.65 274.37 -60.12 268.07 -55.03 261.92 M -67.76 282.66 C -62.17 277.4 -57.92 267.7 -50.05 262.29 M -67.76 282.66 C -59.81 273.91 -54.48 265.95 -50.05 262.29 M -62.77 283.02 C -56.54 273.64 -49.19 264.9 -44.4 261.89 M -62.77 283.02 C -58.36 277.05 -53.01 271.12 -44.4 261.89 M -57.13 282.63 C -52.24 276.45 -44.88 270.45 -39.42 262.25 M -57.13 282.63 C -50.95 274.32 -44.16 267.28 -39.42 262.25 M -52.14 282.99 C -46.52 276.9 -39.06 268.5 -33.77 261.86 M -52.14 282.99 C -47.17 278.41 -43.76 273.13 -33.77 261.86 M -47.16 283.35 C -42.01 276.98 -38.49 274.9 -28.79 262.22 M -47.16 283.35 C -41.1 276.33 -35.09 267.8 -28.79 262.22 M -41.51 282.95 C -37.5 277.16 -32.34 269.05 -23.14 261.82 M -41.51 282.95 C -36.22 278.28 -33.52 273.31 -23.14 261.82 M -36.53 283.31 C -32.31 280.21 -28 275.61 -18.16 262.18 M -36.53 283.31 C -29.21 275.46 -23.41 267.55 -18.16 262.18 M -30.88 282.92 C -23.95 276.23 -17.97 267.76 -12.51 261.79 M -30.88 282.92 C -25.41 276.02 -20.33 270.52 -12.51 261.79 M -25.9 283.28 C -18.7 275.67 -11.84 269.66 -7.53 262.15 M -25.9 283.28 C -21.06 276.1 -14.1 269.82 -7.53 262.15 M -20.25 282.88 C -13.44 276.21 -8.3 269.4 -1.88 261.75 M -20.25 282.88 C -13.79 274.61 -6.25 266.56 -1.88 261.75 M -15.27 283.24 C -7.1 274.15 -1.28 266.87 3.1 262.11 M -15.27 283.24 C -9.07 275.89 -2.64 268.32 3.1 262.11 M -9.62 282.85 C -5.98 274.7 -0.09 272.39 8.75 261.72 M -9.62 282.85 C -3.14 275.59 2.71 268.85 8.75 261.72 M -4.64 283.21 C -0.65 276.94 7.9 270.11 13.73 262.08 M -4.64 283.21 C 2.21 274.74 8.89 268.57 13.73 262.08 M 1.01 282.82 C 6.93 277.85 8.43 275.13 19.38 261.68 M 1.01 282.82 C 5.55 278.82 10.17 272.21 19.38 261.68 M 5.99 283.18 C 10.88 278.61 14.32 274.39 24.36 262.04 M 5.99 283.18 C 12.74 276.86 17.36 269.22 24.36 262.04 M 11.64 282.78 C 17.22 274.77 22.87 269.43 30.01 261.65 M 11.64 282.78 C 15.07 278.1 19.99 274.27 30.01 261.65 M 16.62 283.14 C 22.72 276.12 26.6 271.88 34.99 262.01 M 16.62 283.14 C 23.01 274.81 30.96 268.34 34.99 262.01 M 22.27 282.75 C 29.57 276.82 31.36 269 39.98 262.37 M 22.27 282.75 C 29.52 274.83 36.21 265.82 39.98 262.37 M 27.25 283.11 C 30.65 276.17 35.66 271.79 45.62 261.97 M 27.25 283.11 C 30.53 277.75 36.39 274.14 45.62 261.97 M 32.9 282.71 C 36.71 276.23 43.07 270.13 50.61 262.33 M 32.9 282.71 C 37.27 277.89 41.78 271.92 50.61 262.33 M 37.88 283.07 C 41.95 275.75 48.23 267.94 56.25 261.94 M 37.88 283.07 C 41.97 277.6 47.29 271.98 56.25 261.94 M 43.53 282.68 C 49.74 275.8 58.28 267.42 61.24 262.3 M 43.53 282.68 C 50.83 275.38 57.86 266.08 61.24 262.3 M 48.51 283.04 C 51.94 277.81 57.16 271.64 66.88 261.91 M 48.51 283.04 C 55.33 275.66 60.48 268.36 66.88 261.91 M 54.16 282.64 C 63.02 273.33 70.21 265.14 71.87 262.27 M 54.16 282.64 C 61.14 274.5 67.26 268.21 71.87 262.27 M 59.14 283 C 63.95 275.37 71.3 266.37 77.51 261.87 M 59.14 283 C 65.15 276.49 70.08 270.13 77.51 261.87 M 64.13 283.36 C 70.58 279.03 72.91 270.66 82.5 262.23 M 64.13 283.36 C 67.79 278.22 73.41 272.76 82.5 262.23 M 69.77 282.97 C 72.84 278.56 81.41 272.46 88.14 261.84 M 69.77 282.97 C 74.49 278.02 79.19 272.33 88.14 261.84 M 74.76 283.33 C 82.48 273.84 88.1 265.25 93.13 262.2 M 74.76 283.33 C 80.62 277.65 85.93 271.88 93.13 262.2 M 80.4 282.93 C 83.16 277.38 88 272.23 98.77 261.8 M 80.4 282.93 C 87.62 274.74 92.95 267.65 98.77 261.8 M 85.39 283.29 C 94.33 276.05 98.15 266.37 103.76 262.16 M 85.39 283.29 C 90.48 277.26 93.9 274.01 103.76 262.16 M 91.03 282.9 C 97.08 277.77 98.84 275.19 109.4 261.77 M 91.03 282.9 C 95.05 277.59 100.53 272.97 109.4 261.77 M 96.02 283.26 C 99.59 279.82 103.77 274.24 114.39 262.13 M 96.02 283.26 C 101.22 277.87 105.31 270.85 114.39 262.13 M 101.66 282.87 C 106.69 277.26 116.38 269.06 120.03 261.73 M 101.66 282.87 C 108.09 275.31 113.38 269.17 120.03 261.73 M 106.65 283.23 C 112.58 274.87 118.55 266.73 125.02 262.09 M 106.65 283.23 C 110.99 279.46 113.95 274.74 125.02 262.09 M 112.29 282.83 C 118.43 277.89 123.4 269.55 130.66 261.7 M 112.29 282.83 C 117.74 275.34 124.76 269.8 130.66 261.7 M 117.28 283.19 C 123.39 278.6 126.86 269.63 135.65 262.06 M 117.28 283.19 C 122.98 277.34 128.74 270.05 135.65 262.06 M 122.92 282.8 C 128.87 277.61 132.87 270.38 141.29 261.66 M 122.92 282.8 C 130.02 276.5 135.09 268.38 141.29 261.66 M 127.91 283.16 C 131.04 277.95 137.72 272.11 146.28 262.02 M 127.91 283.16 C 131.75 278.21 137.81 272.22 146.28 262.02 M 133.55 282.76 C 140.19 274.26 149.06 268.46 151.92 261.63 M 133.55 282.76 C 141.37 274.85 146.35 267.02 151.92 261.63 M 138.54 283.12 C 145.12 273.68 152.07 268.23 156.91 261.99 M 138.54 283.12 C 143.32 279.2 146.85 273.8 156.91 261.99 M 144.18 282.73 C 148.95 277.98 155.78 272.25 161.9 262.35 M 144.18 282.73 C 149.24 278.69 152.5 273.53 161.9 262.35 M 149.17 283.09 C 153.15 280.19 158.82 273.17 167.54 261.96 M 149.17 283.09 C 156.92 275.79 163.6 266.74 167.54 261.96 M 154.81 282.69 C 161.02 278.36 167.12 271.35 172.53 262.32 M 154.81 282.69 C 161.28 275.01 167.9 266.36 172.53 262.32 M 159.8 283.05 C 163.62 279.52 167.54 271.67 178.17 261.92 M 159.8 283.05 C 166.04 274.31 173.82 267.58 178.17 261.92 M 165.44 282.66 C 173 276.87 176.99 271.17 183.16 262.28 M 165.44 282.66 C 170.72 277.16 176.77 268.73 183.16 262.28 M 170.43 283.02 C 176.62 276.76 185.46 267 188.8 261.89 M 170.43 283.02 C 176.75 276.05 182.02 270.47 188.8 261.89 M 176.07 282.62 C 183.07 276.47 185.7 269.71 192.48 263.76 M 176.07 282.62 C 178.62 277.74 182.55 273.99 192.48 263.76 M 181.06 282.98 C 183.65 277.62 186.52 276.08 192.21 270.15 M 181.06 282.98 C 184.73 279.59 187.26 276.15 192.21 270.15 M 186.05 283.34 C 187.93 280.04 190.28 278.04 191.95 276.55 M 186.05 283.34 C 187.18 281.83 188.7 279.73 191.95 276.55" fill="none" stroke="#f5f5f5" stroke-opacity="0.9" stroke-width="0.5" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" transform="rotate(90,59.5,271.22)" pointer-events="all"/><path d="M -71 262 C -11.86 262.94 47.31 263.61 190 262 M -71 262 C 14.96 261.3 101.33 260.35 190 262 M 190 262 C 190.69 264.96 191.6 268.73 190 280.44 M 190 262 C 189.46 265.8 189.92 268.89 190 280.44 M 190 280.44 C 93.18 280.44 -4.38 279.09 -71 280.44 M 190 280.44 C 86.28 279.7 -17.59 280.72 -71 280.44 M -71 280.44 C -70.37 273.28 -69.51 269.55 -71 262 M -71 280.44 C -70.53 276.33 -71.42 272.63 -71 262" fill="none" stroke="#666666" stroke-opacity="0.9" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" stroke-dasharray="3 3" transform="rotate(90,59.5,271.22)" pointer-events="all"/><g transform="translate(-0.5 -0.5)rotate(90 61 271.22)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 259px; height: 1px; padding-top: 271px; margin-left: -68px;"><div data-drawio-colors="color: #333333; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 18px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><b><i><font face="Comic Sans MS" style="font-size: 12px">Meta Controller</font></i></b></div></div></div></foreignObject><text x="61" y="277" fill="#333333" font-family="Helvetica" font-size="18px" text-anchor="middle">Meta Controller</text></switch></g><rect x="45.19" y="4" width="303.4" height="90" fill="#e1d5e7" stroke="#9673a6" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 301px; height: 1px; padding-top: 11px; margin-left: 46px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 18px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><b><font style="font-size: 17px;">Analyser</font></b></div></div></div></foreignObject><text x="197" y="29" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="18px" text-anchor="middle">Analyser</text></switch></g><path d="M -36.54 38.5 L 74.46 38.5 L 74.46 38.5 L 74.46 56.5 L 74.46 74.5 L 74.46 74.5 L -36.54 74.5 Z" fill="#e1d5e7" stroke="none" transform="rotate(270,18.96,56.5)" pointer-events="all"/><g transform="translate(-0.5 -0.5)rotate(-90 18.960000000000036 56.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 113px; height: 1px; padding-top: 57px; margin-left: -38px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 19px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Interface</div></div></div></foreignObject><text x="19" y="62" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="19px" text-anchor="middle">Interface</text></switch></g><path d="M -136.75 249.5 L 174.25 249.5 L 174.25 249.5 L 174.25 267.5 L 174.25 285.5 L 174.25 285.5 L -136.75 285.5 Z" fill="#fff2cc" stroke="none" transform="rotate(270,18.75,267.5)" pointer-events="all"/><g transform="translate(-0.5 -0.5)rotate(-90 18.75 267.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 313px; height: 1px; padding-top: 268px; margin-left: -138px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 19px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Multi-level Workflow</div></div></div></foreignObject><text x="19" y="273" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="19px" text-anchor="middle">Multi-level Workflow</text></switch></g><path d="M -41.75 802.5 L 79.25 802.5 L 79.25 802.5 L 79.25 820.5 L 79.25 838.5 L 79.25 838.5 L -41.75 838.5 Z" fill="#dae8fc" stroke="none" transform="rotate(270,18.75,820.5)" pointer-events="all"/><g transform="translate(-0.5 -0.5)rotate(-90 18.75 820.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 123px; height: 1px; padding-top: 821px; margin-left: -43px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 19px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Infrastracture</div></div></div></foreignObject><text x="19" y="826" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="19px" text-anchor="middle">Infrastracture</text></switch></g><rect x="58.68" y="38" width="89.32" height="48.45" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-dasharray="3 3" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 91px; height: 1px; padding-top: 43px; margin-left: 58px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 15px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 15px;">Forecasting Analyser</font></div></div></div></foreignObject><text x="103" y="58" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="15px" text-anchor="middle">Forecasting...</text></switch></g><rect x="159.96" y="37.92" width="82.47" height="48.53" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-dasharray="3 3" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 84px; height: 1px; padding-top: 43px; margin-left: 159px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 15px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 15px;">Portfolio Analyser</font></div></div></div></foreignObject><text x="201" y="58" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="15px" text-anchor="middle">Portfolio A...</text></switch></g><rect x="260.72" y="38" width="76.21" height="48.53" fill="none" stroke="none" pointer-events="all"/><path d="M 260.62 38.11 C 260.62 38.11 260.62 38.11 260.62 38.11 M 260.62 38.11 C 260.62 38.11 260.62 38.11 260.62 38.11 M 261.01 43.76 C 262.69 41.98 264.85 39.82 266.26 37.72 M 261.01 43.76 C 262.52 41.7 263.77 40.06 266.26 37.72 M 260.75 50.16 C 264.46 46.48 266.31 45.62 271.25 38.08 M 260.75 50.16 C 263.57 46.9 266.36 44.55 271.25 38.08 M 260.49 56.55 C 265.36 50.98 269.06 47.76 276.89 37.69 M 260.49 56.55 C 265.13 50.93 270.54 43.88 276.89 37.69 M 260.89 62.2 C 266.3 55.56 275.35 48.36 281.88 38.05 M 260.89 62.2 C 265.62 56.51 270.81 51.14 281.88 38.05 M 260.63 68.59 C 266.51 60.23 275.37 55.25 287.52 37.65 M 260.63 68.59 C 270.81 57.74 280.02 45.75 287.52 37.65 M 261.02 74.24 C 266.1 66.27 272.17 58.38 292.51 38.01 M 261.02 74.24 C 271.23 61.74 281.75 49.65 292.51 38.01 M 260.76 80.64 C 273.53 64.84 287.56 52.44 297.5 38.37 M 260.76 80.64 C 269.56 68.87 280.26 58.94 297.5 38.37 M 261.15 86.28 C 274.47 72.52 285.92 57.29 303.14 37.98 M 261.15 86.28 C 275.91 70.68 288.65 55.65 303.14 37.98 M 266.14 86.64 C 280.01 71.21 297.44 52.3 308.13 38.34 M 266.14 86.64 C 278.12 73.95 288.16 60.34 308.13 38.34 M 271.13 87 C 286 72.21 296.81 54.66 313.77 37.94 M 271.13 87 C 283.01 72.76 294.96 58.9 313.77 37.94 M 276.77 86.6 C 285.82 76.12 294.72 65.68 318.76 38.3 M 276.77 86.6 C 286.24 75.61 294.49 64.57 318.76 38.3 M 281.76 86.96 C 290.28 74.7 303.82 61.71 324.4 37.91 M 281.76 86.96 C 294.03 72.14 306.15 59 324.4 37.91 M 287.4 86.57 C 298.56 72.18 315.28 57.78 329.39 38.27 M 287.4 86.57 C 297.11 74.43 307.25 62.74 329.39 38.27 M 292.39 86.93 C 307.52 69.22 325.77 46.63 335.03 37.87 M 292.39 86.93 C 306.59 70.31 321.85 53.61 335.03 37.87 M 298.03 86.53 C 306.38 77.26 317.66 66.33 338.71 39.74 M 298.03 86.53 C 310.26 74.3 321.88 59.39 338.71 39.74 M 303.02 86.89 C 316.84 72.4 329.88 56.39 339.1 45.39 M 303.02 86.89 C 316.81 69.72 330.43 53.76 339.1 45.39 M 308.66 86.5 C 318.64 76.45 326.47 62.8 338.84 51.78 M 308.66 86.5 C 320.65 74.15 329.7 61.71 338.84 51.78 M 313.65 86.86 C 323.3 75.11 335.18 65.57 338.58 58.18 M 313.65 86.86 C 322.45 77.03 332.54 65.89 338.58 58.18 M 319.29 86.47 C 326.98 78.6 334.79 71.19 338.97 63.82 M 319.29 86.47 C 325.7 78.06 332.54 71.86 338.97 63.82 M 324.28 86.83 C 328.9 80.3 335.27 77.64 338.71 70.22 M 324.28 86.83 C 330.19 81.15 334.46 74.76 338.71 70.22 M 329.92 86.43 C 332.61 84.56 335.8 80.04 339.11 75.87 M 329.92 86.43 C 333.68 82.79 336.03 79.18 339.11 75.87 M 334.91 86.79 C 336.15 85.31 336.18 85.2 338.84 82.26 M 334.91 86.79 C 336.13 85.51 337.54 83.57 338.84 82.26" fill="none" stroke="#adadad" stroke-width="0.5" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" pointer-events="all"/><path d="M 260.72 38 C 289.21 36.64 317.36 35.93 336.93 38 M 260.72 38 C 281.15 38.64 300.28 37.07 336.93 38 M 336.93 38 C 335.53 50.44 335.31 62.58 336.93 86.53 M 336.93 38 C 336.5 49.45 337.79 60.86 336.93 86.53 M 336.93 86.53 C 312.9 87.91 286.5 84.5 260.72 86.53 M 336.93 86.53 C 318.57 85.19 302.52 86.5 260.72 86.53 M 260.72 86.53 C 259.92 77.35 262.11 64.37 260.72 38 M 260.72 86.53 C 261.14 74.73 261.17 61.94 260.72 38" fill="none" stroke="#666666" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 74px; height: 1px; padding-top: 62px; margin-left: 262px;"><div data-drawio-colors="color: #333333; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 15px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><i style="font-size: 15px;"><font style="font-size: 15px ; line-height: 100%" face="Comic Sans MS">Execution Analyser</font></i></div></div></div></foreignObject><text x="299" y="67" fill="#333333" font-family="Helvetica" font-size="15px" text-anchor="middle">Execution...</text></switch></g><path d="M 256.23 219 L 264.68 219 L 266.76 219" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 272.01 219 L 265.01 222.5 L 266.76 219 L 265.01 215.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><rect x="83.85" y="176" width="172.38" height="88.53" fill="#f5e8c4" stroke="#d6b656" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 170px; height: 1px; padding-top: 183px; margin-left: 85px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><b><font style="font-size: 16px">Information Extractor</font><br /></b></div></div></div></foreignObject><text x="170" y="195" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Information Extractor&#xa;</text></switch></g><path d="M 662.57 152.55 L 681.5 152.24" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 686.75 152.15 L 679.81 155.77 L 681.5 152.24 L 679.7 148.77 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 49.56 105 L 799.32 105" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="2 6" pointer-events="stroke"/><path d="M 48.82 776.26 L 797.32 776" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="2 6" pointer-events="stroke"/><rect x="590.51" y="4" width="207" height="91" fill="#e1d5e7" stroke="#9673a6" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 205px; height: 1px; padding-top: 50px; margin-left: 592px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 18px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><span><font size="1"><b style="font-size: 16px;">Online Serving</b></font></span></div></div></div></foreignObject><text x="694" y="55" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="18px" text-anchor="middle">Online Serving</text></switch></g><rect x="108.52" y="234.14" width="53.84" height="22.47" fill="none" stroke="none" pointer-events="all"/><path d="M 108.34 234.34 C 108.34 234.34 108.34 234.34 108.34 234.34 M 108.34 234.34 C 108.34 234.34 108.34 234.34 108.34 234.34 M 108.74 239.99 C 109.98 236.96 112.88 235.24 113.99 233.95 M 108.74 239.99 C 110.25 238.05 111.5 237.19 113.99 233.95 M 108.48 246.39 C 112.02 244.69 114.73 238.55 118.97 234.31 M 108.48 246.39 C 111.93 240.92 116.09 236.83 118.97 234.31 M 108.21 252.78 C 110.47 247.01 115.53 246.74 124.62 233.92 M 108.21 252.78 C 113.28 246.77 118.14 242.05 124.62 233.92 M 109.26 257.67 C 114.86 252.73 117.21 245.6 129.6 234.28 M 109.26 257.67 C 115.51 250.29 122.76 242.44 129.6 234.28 M 114.91 257.28 C 120.77 250.4 125.5 243.87 135.25 233.88 M 114.91 257.28 C 120.11 251.96 126.11 244.62 135.25 233.88 M 119.89 257.64 C 126.57 250.36 129.82 246.36 140.23 234.24 M 119.89 257.64 C 124.55 252.36 129.57 245.64 140.23 234.24 M 125.54 257.24 C 131.09 249.72 136.81 240.63 145.88 233.85 M 125.54 257.24 C 131.57 251.27 136.23 245.01 145.88 233.85 M 130.52 257.6 C 137.47 250.82 143.85 244.49 150.86 234.21 M 130.52 257.6 C 136.97 250.05 144.69 242.11 150.86 234.21 M 136.17 257.21 C 140.98 251.11 144.18 248.59 156.51 233.81 M 136.17 257.21 C 140 252.28 144.24 247.21 156.51 233.81 M 141.15 257.57 C 147.17 246.75 158.3 237.96 161.49 234.17 M 141.15 257.57 C 144.84 252.41 149.43 246.34 161.49 234.17 M 146.8 257.17 C 151.56 248.86 157.64 244.55 165.17 236.04 M 146.8 257.17 C 153.41 251.16 158.93 243.77 165.17 236.04 M 151.78 257.53 C 156.68 253.81 160.35 249.47 164.91 242.44 M 151.78 257.53 C 154.32 253.38 158.65 250.67 164.91 242.44 M 157.43 257.14 C 160.38 253.89 161.59 251.36 164.64 248.84 M 157.43 257.14 C 159.06 254.49 161.5 252.02 164.64 248.84" fill="none" stroke="#f5f5f5" stroke-width="0.5" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" pointer-events="all"/><path d="M 108.52 234.14 C 128.48 232.07 151.14 232.94 162.36 234.14 M 108.52 234.14 C 123.51 235.43 137.82 233.98 162.36 234.14 M 162.36 234.14 C 162.99 240.8 162.88 244.98 162.36 256.61 M 162.36 234.14 C 162.55 241.18 162.97 250.54 162.36 256.61 M 162.36 256.61 C 148.72 258.28 137.13 256.84 108.52 256.61 M 162.36 256.61 C 142.14 257.08 122.11 255.24 108.52 256.61 M 108.52 256.61 C 106.6 249.64 107.65 242.1 108.52 234.14 M 108.52 256.61 C 108.14 250.19 108.83 242.6 108.52 234.14" fill="none" stroke="#666666" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 52px; height: 1px; padding-top: 245px; margin-left: 110px;"><div data-drawio-colors="color: #333333; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: &quot;Comic Sans MS&quot;; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: all; font-style: italic; white-space: normal; overflow-wrap: normal;"><font style="font-size: 12px;"><span style="font-size: 12px;">Graph</span></font></div></div></div></foreignObject><text x="135" y="249" fill="#333333" font-family="Comic Sans MS" font-size="12px" text-anchor="middle" font-style="italic">Graph</text></switch></g><rect x="184.64" y="234.14" width="53.84" height="22.47" fill="none" stroke="none" pointer-events="all"/><path d="M 184.5 234.3 C 184.5 234.3 184.5 234.3 184.5 234.3 M 184.5 234.3 C 184.5 234.3 184.5 234.3 184.5 234.3 M 184.9 239.94 C 186.18 238.57 187.31 236.98 190.15 233.9 M 184.9 239.94 C 185.93 238.2 187.39 236.9 190.15 233.9 M 184.64 246.34 C 188.91 240.94 190.34 239.24 195.13 234.26 M 184.64 246.34 C 189.27 242.6 192.35 238.11 195.13 234.26 M 184.37 252.74 C 186.44 248.02 189.54 243 200.78 233.87 M 184.37 252.74 C 188.48 247.76 192.57 244.73 200.78 233.87 M 185.42 257.63 C 191.21 252.86 195.64 243.25 205.76 234.23 M 185.42 257.63 C 190.56 253.24 193.72 247.26 205.76 234.23 M 191.07 257.23 C 197.12 253.66 200.59 247.53 211.41 233.84 M 191.07 257.23 C 197.33 251.34 203.03 244.83 211.41 233.84 M 196.05 257.59 C 200.82 252.13 207.47 246.06 216.39 234.2 M 196.05 257.59 C 202.29 251.19 207.4 245.55 216.39 234.2 M 201.7 257.2 C 205.74 249.44 213.5 242.8 222.04 233.8 M 201.7 257.2 C 207.77 250.48 212.94 244.48 222.04 233.8 M 206.68 257.56 C 215.79 248.71 223.24 241.71 227.02 234.16 M 206.68 257.56 C 210.82 252.54 215.07 247.79 227.02 234.16 M 212.33 257.16 C 218.26 250.28 223.97 242.33 232.67 233.77 M 212.33 257.16 C 216.86 251.52 221.75 245.11 232.67 233.77 M 217.31 257.52 C 223.18 252.97 223.81 248.38 237.65 234.13 M 217.31 257.52 C 223.13 251.31 228.21 244.62 237.65 234.13 M 222.96 257.13 C 227.07 252.52 231.4 247.76 241.33 236 M 222.96 257.13 C 227.32 253.07 230.65 248.84 241.33 236 M 227.94 257.49 C 229.87 252.47 235.95 248.78 241.07 242.39 M 227.94 257.49 C 230.13 254.3 232.99 250.58 241.07 242.39 M 233.59 257.09 C 236.23 253.6 237.68 251.28 240.8 248.79 M 233.59 257.09 C 236.06 253.77 239.11 251.04 240.8 248.79" fill="none" stroke="#f5f5f5" stroke-width="0.5" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" pointer-events="all"/><path d="M 184.64 234.14 C 203.46 235.47 224.94 232.53 238.48 234.14 M 184.64 234.14 C 202.7 233.32 222.47 235.08 238.48 234.14 M 238.48 234.14 C 238.58 239.76 237.32 247.4 238.48 256.61 M 238.48 234.14 C 237.94 242.4 239.25 248.98 238.48 256.61 M 238.48 256.61 C 226.3 255.51 214.76 255.56 184.64 256.61 M 238.48 256.61 C 226.06 255.33 215.62 256.37 184.64 256.61 M 184.64 256.61 C 185.39 250.95 182.97 242.55 184.64 234.14 M 184.64 256.61 C 185.14 251.2 183.99 244.54 184.64 234.14" fill="none" stroke="#666666" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 52px; height: 1px; padding-top: 245px; margin-left: 186px;"><div data-drawio-colors="color: #333333; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: &quot;Comic Sans MS&quot;; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: all; font-style: italic; white-space: normal; overflow-wrap: normal;"><font style="font-size: 12px;"><span style="font-size: 12px;">Event</span></font></div></div></div></foreignObject><text x="212" y="249" fill="#333333" font-family="Comic Sans MS" font-size="12px" text-anchor="middle" font-style="italic">Event</text></switch></g><rect x="107.52" y="206.14" width="53.84" height="22.47" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 52px; height: 1px; padding-top: 217px; margin-left: 109px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 12px;">Factor</font></div></div></div></foreignObject><text x="134" y="221" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Factor</text></switch></g><rect x="184.64" y="206.14" width="54.84" height="22.47" fill="none" stroke="none" pointer-events="all"/><path d="M 184.42 206.4 C 184.42 206.4 184.42 206.4 184.42 206.4 M 184.42 206.4 C 184.42 206.4 184.42 206.4 184.42 206.4 M 184.81 212.04 C 185.61 210.96 187.44 210.03 190.06 206 M 184.81 212.04 C 186.61 210.3 188.75 207.7 190.06 206 M 184.55 218.44 C 188.2 214.24 191.38 211.02 195.05 206.36 M 184.55 218.44 C 188.09 214.53 192.23 209.23 195.05 206.36 M 184.94 224.08 C 186.87 221.95 189.94 215.58 200.69 205.97 M 184.94 224.08 C 190.15 217.68 195.73 210.55 200.69 205.97 M 185.34 229.73 C 192.45 221.73 201.02 211.15 205.68 206.33 M 185.34 229.73 C 190.34 223.84 195.37 218.91 205.68 206.33 M 190.98 229.33 C 197.01 223.27 202.04 217.28 211.32 205.93 M 190.98 229.33 C 198.45 221.47 205.53 212.63 211.32 205.93 M 195.97 229.69 C 200.44 224.69 205.91 218.47 216.31 206.29 M 195.97 229.69 C 200.91 223.02 206.18 217.55 216.31 206.29 M 201.61 229.3 C 211.17 220.79 219.07 210.38 221.95 205.9 M 201.61 229.3 C 207.3 223.49 211.48 218.02 221.95 205.9 M 206.6 229.66 C 212.41 225.36 217.93 217.7 226.94 206.26 M 206.6 229.66 C 212.72 221.93 220.16 214.2 226.94 206.26 M 212.24 229.26 C 215.88 225.08 222.53 218.96 232.58 205.87 M 212.24 229.26 C 218.16 223.01 224.36 213.91 232.58 205.87 M 217.23 229.62 C 223.39 223.86 227.14 218.3 237.57 206.23 M 217.23 229.62 C 222.3 224 226.29 220.72 237.57 206.23 M 222.87 229.23 C 228.94 223.06 233.43 217.18 241.24 208.1 M 222.87 229.23 C 227.24 224.5 231.89 220.36 241.24 208.1 M 227.86 229.59 C 230.66 222.99 236.38 221.35 241.64 213.74 M 227.86 229.59 C 232.2 223.3 236.98 218.27 241.64 213.74 M 233.5 229.19 C 235.71 227.27 237.81 222.77 241.37 220.14 M 233.5 229.19 C 236.29 225.91 239.35 222.98 241.37 220.14 M 238.49 229.55 C 239.84 228.39 240.24 227.77 241.77 225.78 M 238.49 229.55 C 239.65 228.29 241.1 226.76 241.77 225.78" fill="none" stroke="#f5f5f5" stroke-width="0.5" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" pointer-events="all"/><path d="M 184.64 206.14 C 205.55 205.18 225.07 206.64 239.48 206.14 M 184.64 206.14 C 200.37 206.19 216.56 206.38 239.48 206.14 M 239.48 206.14 C 238.95 213.69 239.42 220.24 239.48 228.61 M 239.48 206.14 C 239.02 212.82 240.13 217.86 239.48 228.61 M 239.48 228.61 C 223.12 228.88 204.59 227.93 184.64 228.61 M 239.48 228.61 C 222.84 227.82 205.74 228.64 184.64 228.61 M 184.64 228.61 C 186.27 221.08 184.91 210.47 184.64 206.14 M 184.64 228.61 C 184.45 220.72 185.63 213.43 184.64 206.14" fill="none" stroke="#666666" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 53px; height: 1px; padding-top: 217px; margin-left: 186px;"><div data-drawio-colors="color: #333333; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: &quot;Comic Sans MS&quot;; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: all; font-style: italic; white-space: normal; overflow-wrap: normal;"><font style="font-size: 12px;"><span style="font-size: 12px;">Text</span></font></div></div></div></foreignObject><text x="212" y="221" fill="#333333" font-family="Comic Sans MS" font-size="12px" text-anchor="middle" font-style="italic">Text</text></switch></g><rect x="45.19" y="787" width="175.56" height="93" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 170px; height: 1px; padding-top: 796px; margin-left: 48px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 18px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><b style="font-size: 18px">Data Server<br style="font-size: 18px" /></b></div></div></div></foreignObject><text x="133" y="814" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="18px" text-anchor="middle">Data Server&#xa;</text></switch></g><rect x="67.16" y="841.46" width="58.84" height="25.47" rx="3.82" ry="3.82" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 57px; height: 1px; padding-top: 854px; margin-left: 68px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 16px">local</font></div></div></div></foreignObject><text x="97" y="859" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="16px" text-anchor="middle">local</text></switch></g><rect x="140.48" y="841.46" width="58.84" height="25.47" rx="3.82" ry="3.82" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 57px; height: 1px; padding-top: 854px; margin-left: 141px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 16px">remote</font></div></div></div></foreignObject><text x="170" y="859" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="16px" text-anchor="middle">remote</text></switch></g><rect x="250.75" y="787" width="220" height="93" fill="#dae8fc" stroke="#6c8ebf" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 796px; margin-left: 361px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; font-weight: bold; white-space: nowrap;"><span style="font-size: 18px">Trainer</span></div></div></div></foreignObject><text x="361" y="808" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle" font-weight="bold">Trainer</text></switch></g><path d="M 260.06 837.08 L 353.49 836.05 L 356.02 862.36 L 260.79 864.65" fill="rgb(255, 255, 255)" stroke="none" pointer-events="none"/><path d="M 260.88 837.08 C 297.68 835.76 332.15 838.6 354.3 837.08 M 260.88 837.08 C 282.86 836.99 305.55 838.08 354.3 837.08 M 354.3 837.08 C 355.77 843.25 353.47 853.75 354.3 863.56 M 354.3 837.08 C 355.16 842.83 354.49 847.28 354.3 863.56 M 354.3 863.56 C 328.34 862.85 298.05 862.37 260.88 863.56 M 354.3 863.56 C 334.53 861.92 316.4 862.7 260.88 863.56 M 260.88 863.56 C 262.97 853.93 259.94 842.61 260.88 837.08 M 260.88 863.56 C 261.67 856.93 261.35 850.82 260.88 837.08" fill="none" stroke="rgb(0, 0, 0)" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 91px; height: 1px; padding-top: 850px; margin-left: 262px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: &quot;Comic Sans MS&quot;; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; font-style: italic; white-space: normal; overflow-wrap: normal;"><font style="font-size: 16px">Algorithms</font></div></div></div></foreignObject><text x="308" y="854" fill="rgb(0, 0, 0)" font-family="Comic Sans MS" font-size="12px" text-anchor="middle" font-style="italic">Algorithms</text></switch></g><path d="M 367.77 835.3 L 460.89 837.77 L 461.13 864.89 L 366.57 862.64" fill="rgb(255, 255, 255)" stroke="none" pointer-events="none"/><path d="M 367.06 837.08 C 403.14 839.47 440.86 838.51 461.58 837.08 M 367.06 837.08 C 395.3 836.64 424.63 836.95 461.58 837.08 M 461.58 837.08 C 462.48 846.35 463.05 852.45 461.58 863.56 M 461.58 837.08 C 461.72 847.24 461.95 858.12 461.58 863.56 M 461.58 863.56 C 438.33 864.15 413.33 865.17 367.06 863.56 M 461.58 863.56 C 438.96 862.24 415.76 863.9 367.06 863.56 M 367.06 863.56 C 367.32 855.47 368.82 847.54 367.06 837.08 M 367.06 863.56 C 366.24 852.84 366.09 842.56 367.06 837.08" fill="none" stroke="rgb(0, 0, 0)" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 93px; height: 1px; padding-top: 850px; margin-left: 368px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: &quot;Comic Sans MS&quot;; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; font-style: italic; white-space: normal; overflow-wrap: normal;"><span style="font-size: 16px">Auto-ML</span></div></div></div></foreignObject><text x="414" y="854" fill="rgb(0, 0, 0)" font-family="Comic Sans MS" font-size="12px" text-anchor="middle" font-style="italic">Auto-ML</text></switch></g><rect x="487.49" y="787" width="311.78" height="93" fill="#dae8fc" stroke="#6c8ebf" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 796px; margin-left: 643px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; font-weight: bold; white-space: nowrap;"><span style="font-size: 18px">Model Manager</span></div></div></div></foreignObject><text x="643" y="808" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle" font-weight="bold">Model Manager</text></switch></g><rect x="511.03" y="821" width="89.72" height="41.11" rx="6.17" ry="6.17" fill="#f5f5f5" stroke="#666666" stroke-dasharray="3 3" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 88px; height: 1px; padding-top: 842px; margin-left: 512px;"><div data-drawio-colors="color: #333333; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><b>Model</b></div></div></div></foreignObject><text x="556" y="845" fill="#333333" font-family="Helvetica" font-size="12px" text-anchor="middle">Model</text></switch></g><rect x="505.58" y="825.52" width="89.72" height="41.11" rx="6.17" ry="6.17" fill="#f5f5f5" stroke="#666666" stroke-dasharray="3 3" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 88px; height: 1px; padding-top: 846px; margin-left: 507px;"><div data-drawio-colors="color: #333333; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><b>Model</b></div></div></div></foreignObject><text x="550" y="850" fill="#333333" font-family="Helvetica" font-size="12px" text-anchor="middle">Model</text></switch></g><rect x="501.21" y="829.89" width="89.72" height="41.11" rx="6.17" ry="6.17" fill="#f5f5f5" stroke="#666666" stroke-dasharray="3 3" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 88px; height: 1px; padding-top: 850px; margin-left: 502px;"><div data-drawio-colors="color: #333333; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 17px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><b><font style="font-size: 16px">Models</font></b></div></div></div></foreignObject><text x="546" y="856" fill="#333333" font-family="Helvetica" font-size="17px" text-anchor="middle">Models</text></switch></g><rect x="626.3" y="819" width="164.45" height="42.59" rx="6.39" ry="6.39" fill="#f5f5f5" stroke="#666666" stroke-dasharray="3 3" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 162px; height: 1px; padding-top: 840px; margin-left: 627px;"><div data-drawio-colors="color: #333333; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><b>Model</b></div></div></div></foreignObject><text x="709" y="844" fill="#333333" font-family="Helvetica" font-size="12px" text-anchor="middle">Model</text></switch></g><rect x="617.66" y="823.78" width="164.45" height="42.59" rx="6.39" ry="6.39" fill="#f5f5f5" stroke="#666666" stroke-dasharray="3 3" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 162px; height: 1px; padding-top: 845px; margin-left: 619px;"><div data-drawio-colors="color: #333333; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><b>Model</b></div></div></div></foreignObject><text x="700" y="849" fill="#333333" font-family="Helvetica" font-size="12px" text-anchor="middle">Model</text></switch></g><rect x="610.75" y="828.41" width="164.45" height="42.59" rx="6.39" ry="6.39" fill="#f5f5f5" stroke="#666666" stroke-dasharray="3 3" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 162px; height: 1px; padding-top: 850px; margin-left: 612px;"><div data-drawio-colors="color: #333333; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 17px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><b style="font-size: 16px"><font style="font-size: 16px"><i><font face="Comic Sans MS">Decision Generators</font></i><br /></font></b></div></div></div></foreignObject><text x="693" y="855" fill="#333333" font-family="Helvetica" font-size="17px" text-anchor="middle">Decision Generators&#xa;</text></switch></g><path d="M 388.67 3.68 C 388.67 3.68 388.67 3.68 388.67 3.68 M 388.67 3.68 C 388.67 3.68 388.67 3.68 388.67 3.68 M 388.41 10.07 C 390.43 8.82 392.13 6.76 393.66 4.04 M 388.41 10.07 C 389.48 9.15 390.64 7.6 393.66 4.04 M 388.15 16.47 C 392.05 10.37 397.74 5.65 399.3 3.64 M 388.15 16.47 C 390.35 14.42 393.17 10.63 399.3 3.64 M 388.54 22.11 C 392.25 15.41 399.37 9 404.29 4 M 388.54 22.11 C 394.64 15.92 400.11 8.74 404.29 4 M 388.28 28.51 C 397.44 20.1 403.49 9.47 409.28 4.36 M 388.28 28.51 C 395.57 20.14 403.04 12.55 409.28 4.36 M 388.68 34.16 C 400.29 21.49 410.87 10.57 414.92 3.97 M 388.68 34.16 C 394.97 27.91 400.71 19.94 414.92 3.97 M 388.41 40.55 C 396.88 29.83 406.71 20.2 419.91 4.33 M 388.41 40.55 C 397.75 29.61 409.49 16.63 419.91 4.33 M 388.15 46.95 C 401.77 29.54 417.58 11.32 425.55 3.93 M 388.15 46.95 C 396.25 36.46 404.44 26.68 425.55 3.93 M 388.55 52.59 C 397.08 41.07 405.47 33.34 430.54 4.29 M 388.55 52.59 C 404.66 35.83 419.77 17.2 430.54 4.29 M 388.29 58.99 C 407.5 37.6 422.46 18.34 436.18 3.9 M 388.29 58.99 C 402.08 42.75 414.01 28.45 436.18 3.9 M 388.68 64.64 C 402.81 49.2 414.18 34.68 441.17 4.26 M 388.68 64.64 C 402.71 49.5 416.66 33.51 441.17 4.26 M 388.42 71.03 C 399.8 55.58 412.13 43.06 446.81 3.86 M 388.42 71.03 C 406.71 50.44 424.35 28.05 446.81 3.86 M 388.16 77.43 C 404.59 61.94 415.84 45.57 451.8 4.22 M 388.16 77.43 C 412.07 49.53 437.43 21.96 451.8 4.22 M 388.55 83.07 C 414.8 53.39 442.51 20.62 457.44 3.83 M 388.55 83.07 C 406.1 61.63 424.78 40.5 457.44 3.83 M 388.29 89.47 C 409.55 67.26 428.95 42.43 462.43 4.19 M 388.29 89.47 C 410.97 63.88 433.43 36.58 462.43 4.19 M 389.34 94.36 C 407.06 74.12 423.16 54.56 468.07 3.79 M 389.34 94.36 C 409.57 71.54 428.23 47.93 468.07 3.79 M 394.33 94.72 C 412.42 73.1 431.53 52.73 473.06 4.16 M 394.33 94.72 C 424 59.72 455.81 24.03 473.06 4.16 M 399.97 94.33 C 427.61 63.85 454.34 32.1 478.7 3.76 M 399.97 94.33 C 431.71 58.9 461.72 22.92 478.7 3.76 M 404.96 94.69 C 426.82 68.67 447.98 46.22 483.69 4.12 M 404.96 94.69 C 421.68 74.44 440.07 55.19 483.69 4.12 M 410.6 94.29 C 437.33 62.63 462.94 33.07 489.33 3.73 M 410.6 94.29 C 442.2 59.15 473.43 22.98 489.33 3.73 M 415.59 94.65 C 431.74 75.09 450.62 53.24 494.32 4.09 M 415.59 94.65 C 441.69 65.05 466.8 34.82 494.32 4.09 M 421.23 94.26 C 439.23 72.65 458.02 49.92 499.96 3.69 M 421.23 94.26 C 451.06 59.91 480.29 27.1 499.96 3.69 M 426.22 94.62 C 447.73 69.69 468.19 50.04 504.95 4.05 M 426.22 94.62 C 449.03 67.73 471.37 41.56 504.95 4.05 M 431.21 94.98 C 447.72 74.79 464.57 56 510.59 3.66 M 431.21 94.98 C 447.28 75.52 464.07 57.52 510.59 3.66 M 436.85 94.58 C 457.58 73.66 476.5 51.34 515.58 4.02 M 436.85 94.58 C 455.94 72.01 475.11 51.65 515.58 4.02 M 441.84 94.94 C 469.36 63.65 495.75 35.55 520.56 4.38 M 441.84 94.94 C 458.4 77.11 473.54 60.23 520.56 4.38 M 447.48 94.55 C 472.87 64.92 500.32 32.86 526.21 3.98 M 447.48 94.55 C 472.08 67.55 496.26 40.01 526.21 3.98 M 452.47 94.91 C 480.19 62.19 511.97 26.75 531.19 4.34 M 452.47 94.91 C 470.87 75.09 489.26 52.37 531.19 4.34 M 458.11 94.51 C 475.68 75.38 496.01 53 536.84 3.95 M 458.11 94.51 C 477.02 72.49 495.02 52.86 536.84 3.95 M 463.1 94.87 C 478.86 76.19 495.99 53.66 541.82 4.31 M 463.1 94.87 C 494.47 58.58 525.77 22.86 541.82 4.31 M 468.74 94.48 C 493.8 64.7 517.25 37.99 547.47 3.91 M 468.74 94.48 C 495.2 63.51 521.19 33.13 547.47 3.91 M 473.73 94.84 C 494.09 70.57 512.7 49.48 552.45 4.27 M 473.73 94.84 C 499.38 64.08 526.2 34.64 552.45 4.27 M 479.37 94.44 C 498.53 69.4 518.63 45.7 558.1 3.88 M 479.37 94.44 C 507.04 62.55 534.8 31.99 558.1 3.88 M 484.36 94.8 C 510.63 60.38 539.37 31.05 561.12 6.5 M 484.36 94.8 C 511.04 64.74 537 34.29 561.12 6.5 M 490 94.41 C 510.38 70.43 529.52 47.23 560.85 12.9 M 490 94.41 C 517.25 65.65 542.3 34.33 560.85 12.9 M 494.99 94.77 C 519.96 69.05 542.95 38.08 561.25 18.54 M 494.99 94.77 C 511.59 74.48 530.72 54.68 561.25 18.54 M 500.63 94.38 C 516.31 73.86 534.37 53.06 560.99 24.94 M 500.63 94.38 C 520.55 71.52 539.73 49.71 560.99 24.94 M 505.62 94.74 C 527.68 72.2 546.28 48.19 561.38 30.59 M 505.62 94.74 C 525.9 70.34 546.52 47.11 561.38 30.59 M 511.26 94.34 C 525.8 78.02 540.17 64.74 561.12 36.98 M 511.26 94.34 C 524.16 79.43 538.43 61.5 561.12 36.98 M 516.25 94.7 C 534.32 75.74 548.02 58.07 560.86 43.38 M 516.25 94.7 C 529.31 80.39 540.86 65.27 560.86 43.38 M 521.89 94.31 C 537.03 76.55 549.64 59.58 561.25 49.02 M 521.89 94.31 C 536.9 78.69 549.97 61.33 561.25 49.02 M 526.88 94.67 C 538.12 81.23 545.5 71.57 560.99 55.42 M 526.88 94.67 C 535.73 83.53 544.81 74.25 560.99 55.42 M 532.52 94.27 C 541.83 80.83 553.84 68.03 561.39 61.06 M 532.52 94.27 C 539.02 86.34 547.62 76.6 561.39 61.06 M 537.51 94.63 C 544.52 85.62 553.14 72.52 561.13 67.46 M 537.51 94.63 C 546.23 85.57 552.8 76.18 561.13 67.46 M 542.49 94.99 C 546.69 89.3 551.83 84.77 560.86 73.86 M 542.49 94.99 C 547.45 90.3 551.36 86 560.86 73.86 M 548.14 94.6 C 554.56 90.16 557.26 83.17 561.26 79.5 M 548.14 94.6 C 552.86 89.31 556.32 84.74 561.26 79.5 M 553.12 94.96 C 554.6 91.95 556.49 89.9 561 85.9 M 553.12 94.96 C 556.11 91.62 558.83 88.59 561 85.9" fill="none" stroke="#e1d5e7" stroke-width="0.5" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" pointer-events="none"/><path d="M 388.39 4 C 453.87 4.45 519.93 1.67 558.32 4 M 388.39 4 C 446.22 3.03 502.89 3.1 558.32 4 M 558.32 4 C 558.97 22.52 556.21 41.17 558.32 94 M 558.32 4 C 558.14 24.48 558.18 44.07 558.32 94 M 558.32 94 C 515.84 93.1 467.8 92.59 388.39 94 M 558.32 94 C 504.09 92.94 452.5 94.11 388.39 94 M 388.39 94 C 386.5 67.85 388.44 42.57 388.39 4 M 388.39 94 C 388.8 66.98 387.81 42 388.39 4" fill="none" stroke="#9673a6" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 168px; height: 1px; padding-top: 49px; margin-left: 389px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 18px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><i><font face="Comic Sans MS" size="1"><b style="font-size: 16px;">Model Interpreter</b></font></i></div></div></div></foreignObject><text x="473" y="54" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="18px" text-anchor="middle">Model Interpreter</text></switch></g><path d="M 708 380.03 L 528.37 380.03" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/><path d="M 523.12 380.03 L 530.12 376.53 L 528.37 380.03 L 530.12 383.53 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/><rect x="674" y="249.29" width="127.27" height="84.24" fill="#fff2cc" stroke="#d6b656" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 125px; height: 1px; padding-top: 256px; margin-left: 675px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><span style="font-size: 16px;"><b>Executor</b></span></div></div></div></foreignObject><text x="738" y="268" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Executor</text></switch></g><rect x="685" y="286.19" width="105.91" height="40.34" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-dasharray="3 3" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 104px; height: 1px; padding-top: 306px; margin-left: 686px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><font color="#cc0000">Sub-workflow<br /><font style="font-size: 9px;">(NestedExecutor)</font><br /></font></div></div></div></foreignObject><text x="738" y="310" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Sub-workflow...</text></switch></g><rect x="820.75" y="727" width="161" height="155" fill="#f5f5f5" stroke="none" pointer-events="none"/><rect x="825.32" y="766" width="150" height="49.02" fill="none" stroke="#666666" stroke-dasharray="3 3" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 148px; height: 1px; padding-top: 791px; margin-left: 826px;"><div data-drawio-colors="color: #333333; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><font style="font-size: 15px"><b><span style="color: rgb(67 , 67 , 67) ; font-family: &quot;arial&quot; , sans-serif">Highly Customizable</span><br />Module</b></font></div></div></div></foreignObject><text x="900" y="797" fill="#333333" font-family="Helvetica" font-size="20px" text-anchor="middle">Highly Customiz...</text></switch></g><path d="M 825.61 826.06 C 825.61 826.06 825.61 826.06 825.61 826.06 M 825.61 826.06 C 825.61 826.06 825.61 826.06 825.61 826.06 M 825.35 832.45 C 825.81 830.47 828.01 830.39 830.6 826.42 M 825.35 832.45 C 827.12 830.43 829.25 827.81 830.6 826.42 M 825.09 838.85 C 826.99 836.79 831.44 834.04 836.24 826.02 M 825.09 838.85 C 829.45 833.69 832.76 829.86 836.24 826.02 M 825.48 844.49 C 829.54 836.75 836.96 833.83 841.23 826.38 M 825.48 844.49 C 831.45 838.75 835.55 831.97 841.23 826.38 M 825.22 850.89 C 832.76 842.62 835.06 838.5 846.21 826.74 M 825.22 850.89 C 833.67 842.37 840.81 831.89 846.21 826.74 M 825.62 856.54 C 834.46 846.04 845.53 832.32 851.86 826.35 M 825.62 856.54 C 834.82 845.79 845.61 833.77 851.86 826.35 M 825.35 862.93 C 833.61 853.21 843.5 843.58 856.84 826.71 M 825.35 862.93 C 837.01 850.86 847.54 838.06 856.84 826.71 M 825.09 869.33 C 838.33 855.74 849.36 840.81 862.49 826.31 M 825.09 869.33 C 840.02 852.87 854.01 834.9 862.49 826.31 M 826.14 874.22 C 837.71 861.41 849.58 849.03 867.47 826.67 M 826.14 874.22 C 835.73 865.08 842.39 853.94 867.47 826.67 M 831.13 874.58 C 846.5 853.78 863.12 836.77 873.12 826.28 M 831.13 874.58 C 843.37 860.63 853.53 846.83 873.12 826.28 M 836.77 874.18 C 851.52 855.24 868.49 837.56 878.1 826.64 M 836.77 874.18 C 845.52 863.95 856.71 851.6 878.1 826.64 M 841.76 874.55 C 852.45 861.72 862.42 853.11 883.75 826.24 M 841.76 874.55 C 857.67 856.31 873.46 838.52 883.75 826.24 M 847.4 874.15 C 857.48 863.7 863.05 855.38 888.73 826.6 M 847.4 874.15 C 856.6 862.57 866.65 851.3 888.73 826.6 M 852.39 874.51 C 867.53 855.89 882.44 842.42 894.38 826.21 M 852.39 874.51 C 867.75 856.22 884.16 836.5 894.38 826.21 M 857.38 874.87 C 870.99 860.83 879.85 849.37 899.36 826.57 M 857.38 874.87 C 870.64 858.66 884.06 844.17 899.36 826.57 M 863.02 874.48 C 876.58 861.17 888.33 847.63 905.01 826.17 M 863.02 874.48 C 872.9 863.54 882.19 851.42 905.01 826.17 M 868.01 874.84 C 881.75 859.99 893.98 844.95 910 826.53 M 868.01 874.84 C 876.96 862.4 888.56 851.7 910 826.53 M 873.65 874.44 C 882.18 863.53 892.14 851.48 915.64 826.14 M 873.65 874.44 C 884.57 861.77 894.23 849.8 915.64 826.14 M 878.64 874.8 C 892.32 858.43 906.24 844.32 920.63 826.5 M 878.64 874.8 C 892.3 858.72 907.75 842.26 920.63 826.5 M 884.28 874.41 C 894 860.64 904.58 849.84 926.27 826.11 M 884.28 874.41 C 898.82 857.84 913.85 839.73 926.27 826.11 M 889.27 874.77 C 901.6 857.32 917.4 842.33 931.26 826.47 M 889.27 874.77 C 905.75 855.83 923.59 836.71 931.26 826.47 M 894.91 874.37 C 901.88 864.41 912.08 854.22 936.9 826.07 M 894.91 874.37 C 906.64 862.29 916.35 849.45 936.9 826.07 M 899.9 874.73 C 910.39 863.93 921.89 848.5 941.89 826.43 M 899.9 874.73 C 914.48 859.66 927.44 843.64 941.89 826.43 M 905.54 874.34 C 916.46 861.48 926.27 848.9 947.53 826.04 M 905.54 874.34 C 920.61 858.14 932.88 841.5 947.53 826.04 M 910.53 874.7 C 918.57 865.19 929.41 854.91 952.52 826.4 M 910.53 874.7 C 924.17 858.33 938.9 842.17 952.52 826.4 M 916.17 874.3 C 933.43 856.16 947.13 837.55 957.5 826.76 M 916.17 874.3 C 927.32 859.88 938.4 848 957.5 826.76 M 921.16 874.66 C 940.32 857.38 956.11 837.89 963.15 826.36 M 921.16 874.66 C 935.64 858.34 949.24 843.15 963.15 826.36 M 926.8 874.27 C 934.92 862.8 945.11 851.92 968.13 826.72 M 926.8 874.27 C 937.99 861.46 948.99 849.33 968.13 826.72 M 931.79 874.63 C 939.09 866.18 949.6 854.39 973.78 826.33 M 931.79 874.63 C 945.88 858.87 961.21 842.14 973.78 826.33 M 937.43 874.23 C 947.62 863.33 955.25 854.23 976.79 828.95 M 937.43 874.23 C 952.85 857.17 969.36 838.82 976.79 828.95 M 942.42 874.59 C 956.99 860.6 967.97 844.79 977.19 834.6 M 942.42 874.59 C 950.61 863.11 961.32 852.65 977.19 834.6 M 948.06 874.2 C 956.54 863.39 967.7 849.41 976.93 840.99 M 948.06 874.2 C 954.81 866.17 961.55 857.69 976.93 840.99 M 953.05 874.56 C 963.5 862.93 970.45 856.14 977.32 846.64 M 953.05 874.56 C 961.42 865.12 969.26 855.5 977.32 846.64 M 958.69 874.17 C 963.02 868.03 971.34 859.29 977.06 853.03 M 958.69 874.17 C 966.18 866 970.88 859.52 977.06 853.03 M 963.68 874.53 C 969.81 868.22 973.52 860.41 976.8 859.43 M 963.68 874.53 C 968.51 870.71 971.89 865.22 976.8 859.43 M 969.32 874.13 C 971.16 872.74 973.31 870.87 977.19 865.07 M 969.32 874.13 C 970.84 871.82 973.71 868.83 977.19 865.07" fill="none" stroke="#adadad" stroke-width="0.5" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" pointer-events="none"/><path d="M 825.32 826.39 C 883.95 825.96 938.95 824.38 974.32 826.39 M 825.32 826.39 C 880.01 825.41 935.82 826.79 974.32 826.39 M 974.32 826.39 C 973.82 837.9 976.23 848.5 974.32 874 M 974.32 826.39 C 974.17 838.67 975.59 852.12 974.32 874 M 974.32 874 C 938.25 873.53 904.15 874.01 825.32 874 M 974.32 874 C 921.34 875.65 867.69 876.15 825.32 874 M 825.32 874 C 826.75 857.92 823.88 841.67 825.32 826.39 M 825.32 874 C 826.03 865.16 826.04 855.89 825.32 826.39" fill="none" stroke="#666666" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 147px; height: 1px; padding-top: 850px; margin-left: 826px;"><div data-drawio-colors="color: #333333; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><b><font size="1" face="Comic Sans MS"><i style="line-height: 100% ; font-size: 16px">Module in development</i></font></b></div></div></div></foreignObject><text x="900" y="856" fill="#333333" font-family="Helvetica" font-size="20px" text-anchor="middle">Module in devel...</text></switch></g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 746px; margin-left: 870px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: nowrap;"><font style="font-size: 15px"><b>Explanation</b></font></div></div></div></foreignObject><text x="870" y="749" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Explanation</text></switch></g><rect x="810.61" y="214" width="184.39" height="173" fill="none" stroke="rgb(0, 0, 0)" stroke-dasharray="3 3" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 170px; height: 1px; padding-top: 220px; margin-left: 819px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><font color="#cc0000" style="">Sub-workflow<sup style="">(1) </sup>(E.g. High-frequency order execution nested in portfolio management)</font></div></div></div></foreignObject><text x="819" y="232" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Sub-workflow(1) (E.g. High-fr...</text></switch></g><path d="M 953.6 352 L 953.6 377 L 853 377 L 853 358.37" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/><path d="M 853 353.12 L 856.5 360.12 L 853 358.37 L 849.5 360.12 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/><path d="M 810.61 214 L 790.91 286.19" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="2 6" pointer-events="none"/><path d="M 810.61 387 L 790.91 326.53" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="2 6" pointer-events="none"/><path d="M 851 302 L 851 283 L 953.6 283 L 953.61 295.63" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/><path d="M 953.61 300.88 L 950.11 293.88 L 953.61 295.63 L 957.11 293.88 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/><rect x="920.61" y="302" width="66" height="50" fill="#fff2cc" stroke="#d6b656" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 64px; height: 1px; padding-top: 304px; margin-left: 922px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; font-weight: bold; white-space: normal; overflow-wrap: normal;">Executor</div></div></div></foreignObject><text x="954" y="316" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle" font-weight="bold">Executor</text></switch></g><rect x="938.79" y="328.75" width="29.65" height="18.24" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-dasharray="3 3" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 28px; height: 1px; padding-top: 333px; margin-left: 940px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">...</div></div></div></foreignObject><text x="954" y="338" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="16px" text-anchor="middle">...</text></switch></g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 965px; height: 1px; padding-top: 896px; margin-left: 6px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><span>(1)  The sub-workflow will make more f</span>ine-grained decisions according to the decision from the upper-level trading agent</div></div></div></foreignObject><text x="6" y="900" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">(1)  The sub-workflow will make more fine-grained decisions according to the decision from the upper-level trading agent</text></switch></g><path d="M 271.19 707.3 L 291.2 707.3 L 304.37 707.28" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/><path d="M 309.62 707.27 L 302.63 710.78 L 304.37 707.28 L 302.62 703.78 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/><rect x="98.81" y="663" width="172.38" height="88.53" fill="#d5e8d4" stroke="#82b366" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 170px; height: 1px; padding-top: 670px; margin-left: 100px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><b><font style="font-size: 16px">Supervised signal</font><br /></b></div></div></div></foreignObject><text x="185" y="682" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Supervised signal&#xa;</text></switch></g><path d="M 535 707.27 L 555.5 707.3 L 569.52 707.3" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/><path d="M 574.77 707.3 L 567.77 710.8 L 569.52 707.3 L 567.77 703.8 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/><rect x="310.74" y="663" width="224.26" height="88.53" fill="#d5e8d4" stroke="#82b366" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 222px; height: 1px; padding-top: 707px; margin-left: 312px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><b><span style="font-size: 16px;">Model Traning</span><br /></b></div></div></div></foreignObject><text x="423" y="711" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Model Traning&#xa;</text></switch></g><rect x="575.89" y="663" width="172.38" height="88.53" fill="#d5e8d4" stroke="#82b366" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 170px; height: 1px; padding-top: 670px; margin-left: 577px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><b><span style="font-size: 16px;">Forecast Model</span><br /></b></div></div></div></foreignObject><text x="662" y="682" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Forecast Model&#xa;</text></switch></g><rect x="666.79" y="708.48" width="50.27" height="30.23" rx="4.53" ry="4.53" fill="#f5f5f5" stroke="#666666" stroke-dasharray="3 3" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 48px; height: 1px; padding-top: 724px; margin-left: 668px;"><div data-drawio-colors="color: #333333; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 17px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><span style="font-size: 14px ; line-height: 100%">Risk<br /></span></div></div></div></foreignObject><text x="692" y="729" fill="#333333" font-family="Helvetica" font-size="17px" text-anchor="middle">Risk&#xa;</text></switch></g><rect x="604.63" y="708" width="50.27" height="31.2" rx="4.68" ry="4.68" fill-opacity="0.99" fill="#f5f5f5" stroke="#666666" stroke-opacity="0.99" stroke-dasharray="3 3" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 48px; height: 1px; padding-top: 724px; margin-left: 606px;"><div data-drawio-colors="color: #333333; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 17px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><span style="font-size: 14px">Alpha<br /></span></div></div></div></foreignObject><text x="630" y="729" fill="#333333" font-family="Helvetica" font-size="17px" text-anchor="middle">Alpha&#xa;</text></switch></g><path d="M 206 507 L 206 472.5 L 437.63 472.5" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/><path d="M 442.88 472.5 L 435.88 476 L 437.63 472.5 L 435.88 469 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 473px; margin-left: 308px;"><div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; background-color: rgb(255, 255, 255); white-space: nowrap;">Action (Decision)</div></div></div></foreignObject><text x="308" y="476" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">Action (Decision)</text></switch></g><path d="M -149.54 573.5 L 187.46 573.5 L 187.46 573.5 L 187.46 591.5 L 187.46 609.5 L 187.46 609.5 L -149.54 609.5 Z" fill="#d5e8d4" stroke="none" transform="rotate(270,18.96,591.5)" pointer-events="none"/><g transform="translate(-0.5 -0.5)rotate(-90 18.960000000000036 591.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 339px; height: 1px; padding-top: 592px; margin-left: -151px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 19px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">Learning Framework</div></div></div></foreignObject><text x="19" y="597" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="19px" text-anchor="middle">Learning Framework</text></switch></g><rect x="133" y="694" width="99" height="51.13" fill="#fff2cc" stroke="#d6b656" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 107px; height: 1px; padding-top: 696px; margin-left: 129px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><b><font style="font-size: 7px; line-height: 1.2;">Information Extractor</font><br /></b></div></div></div></foreignObject><text x="183" y="708" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Information Extrac...</text></switch></g><path d="M 147.3 727.99 C 147.3 727.99 147.3 727.99 147.3 727.99 M 147.3 727.99 C 147.3 727.99 147.3 727.99 147.3 727.99 M 147.04 734.39 C 148.78 732.3 150.88 730.87 152.29 728.35 M 147.04 734.39 C 149.04 732.8 150.57 730.49 152.29 728.35 M 147.44 740.03 C 151.06 736.34 151.23 734.37 157.93 727.96 M 147.44 740.03 C 150.21 735.32 153.75 730.92 157.93 727.96 M 149.8 743.41 C 153.31 741.05 154.34 738.8 162.92 728.32 M 149.8 743.41 C 153.81 738.63 158.02 733.66 162.92 728.32 M 154.79 743.77 C 158.3 740.76 161.4 735.16 168.56 727.92 M 154.79 743.77 C 158.95 737.84 164.08 732.8 168.56 727.92 M 160.43 743.38 C 165.05 739.61 167.08 733.53 173.55 728.28 M 160.43 743.38 C 164.69 737.8 169.97 733.17 173.55 728.28 M 165.42 743.74 C 168.53 736.36 174.6 730.88 178.54 728.64 M 165.42 743.74 C 169.32 737.95 172.65 733.73 178.54 728.64 M 171.06 743.34 C 173.23 739.65 176.1 736.57 178.28 735.04 M 171.06 743.34 C 174.04 740.34 176.23 736.59 178.28 735.04" fill="none" stroke="#f5f5f5" stroke-width="0.5" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" pointer-events="none"/><path d="M 147.17 728.15 C 159.56 727.61 169.41 726.96 178.09 728.15 M 147.17 728.15 C 155.35 727.85 164.89 728.78 178.09 728.15 M 178.09 728.15 C 177.76 732.14 178.79 739.47 178.09 741.35 M 178.09 728.15 C 177.98 732.83 177.77 737.69 178.09 741.35 M 178.09 741.35 C 165.42 739.69 154.59 739.58 147.17 741.35 M 178.09 741.35 C 170.91 741.63 162.48 741.57 147.17 741.35 M 147.17 741.35 C 147.32 738.81 146.14 733.19 147.17 728.15 M 147.17 741.35 C 147.33 737.49 147.43 734.28 147.17 728.15" fill="none" stroke="#666666" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 29px; height: 1px; padding-top: 735px; margin-left: 148px;"><div data-drawio-colors="color: #333333; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 7px; font-family: &quot;Comic Sans MS&quot;; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: none; font-style: italic; white-space: normal; overflow-wrap: normal;"><font style="font-size: 7px;"><span style="font-size: 7px;">Graph</span></font></div></div></div></foreignObject><text x="163" y="737" fill="#333333" font-family="Comic Sans MS" font-size="7px" text-anchor="middle" font-style="italic">Graph</text></switch></g><path d="M 190.57 728.51 C 190.57 728.51 190.57 728.51 190.57 728.51 M 190.57 728.51 C 190.57 728.51 190.57 728.51 190.57 728.51 M 190.97 734.15 C 192.74 733.71 193.07 732.41 196.22 728.11 M 190.97 734.15 C 192.37 732.76 194.11 730.8 196.22 728.11 M 190.71 740.55 C 195.07 736.35 197.16 732.42 201.2 728.47 M 190.71 740.55 C 194.66 735.75 197.12 732.55 201.2 728.47 M 193.73 743.17 C 197.09 737.87 200.17 734.87 206.85 728.08 M 193.73 743.17 C 196.89 739.31 200.43 733.92 206.85 728.08 M 198.71 743.53 C 201.96 737.7 206.57 734.26 211.83 728.44 M 198.71 743.53 C 201.86 740 205.33 736.33 211.83 728.44 M 203.7 743.89 C 207.89 739.5 216.05 729.93 217.48 728.04 M 203.7 743.89 C 208.65 737.29 213.67 733.44 217.48 728.04 M 209.34 743.5 C 214.55 737.99 220.07 730.39 222.46 728.4 M 209.34 743.5 C 212.72 738.61 218.1 733.51 222.46 728.4 M 214.33 743.86 C 216.02 739.5 220.56 737.39 222.2 734.8 M 214.33 743.86 C 217.61 740.12 220.33 737.34 222.2 734.8" fill="none" stroke="#f5f5f5" stroke-width="0.5" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" pointer-events="none"/><path d="M 190.88 728.15 C 202.13 726.82 214.81 726.37 221.81 728.15 M 190.88 728.15 C 202.49 727.88 213.83 728.02 221.81 728.15 M 221.81 728.15 C 221.14 731.4 221.42 733.03 221.81 741.35 M 221.81 728.15 C 221.21 732.35 221.59 736.74 221.81 741.35 M 221.81 741.35 C 213.72 740.88 202.06 742.27 190.88 741.35 M 221.81 741.35 C 212.6 741.84 203.94 740.66 190.88 741.35 M 190.88 741.35 C 189.88 739.57 191.69 735.94 190.88 728.15 M 190.88 741.35 C 190.29 738.07 190.5 735.4 190.88 728.15" fill="none" stroke="#666666" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 29px; height: 1px; padding-top: 735px; margin-left: 192px;"><div data-drawio-colors="color: #333333; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 7px; font-family: &quot;Comic Sans MS&quot;; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: none; font-style: italic; white-space: normal; overflow-wrap: normal;"><font style="font-size: 7px;"><span style="font-size: 7px;">Event</span></font></div></div></div></foreignObject><text x="206" y="737" fill="#333333" font-family="Comic Sans MS" font-size="7px" text-anchor="middle" font-style="italic">Event</text></switch></g><rect x="146.59" y="711.7" width="30.92" height="13.2" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 29px; height: 1px; padding-top: 718px; margin-left: 148px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 7px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><font style="font-size: 7px;">Factor</font></div></div></div></foreignObject><text x="162" y="720" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="7px" text-anchor="middle">Factor</text></switch></g><path d="M 190.96 711.62 C 190.96 711.62 190.96 711.62 190.96 711.62 M 190.96 711.62 C 190.96 711.62 190.96 711.62 190.96 711.62 M 190.7 718.01 C 192.92 715.06 195 713.21 195.95 711.98 M 190.7 718.01 C 191.94 717.02 192.51 715.79 195.95 711.98 M 191.09 723.66 C 194.83 720.65 195.46 718.41 201.59 711.58 M 191.09 723.66 C 193.95 720.5 197.47 716.49 201.59 711.58 M 193.46 727.04 C 196.84 724.13 200.03 719.61 206.58 711.94 M 193.46 727.04 C 196.93 723.01 199.12 720.77 206.58 711.94 M 198.44 727.4 C 203.62 722.1 205.12 721.44 212.22 711.55 M 198.44 727.4 C 201.43 722.39 206.81 719.73 212.22 711.55 M 204.09 727 C 209.38 724.3 211.77 718.69 217.21 711.91 M 204.09 727 C 207.39 724.46 210.58 719.98 217.21 711.91 M 209.07 727.36 C 214.38 721.86 219.03 717.75 222.85 711.51 M 209.07 727.36 C 213.32 722.04 219.02 716.93 222.85 711.51 M 214.72 726.97 C 217.63 724.51 217.79 722.51 222.59 717.91 M 214.72 726.97 C 217.87 723.65 220.06 719.93 222.59 717.91 M 219.7 727.33 C 220.63 726.49 221.61 725.4 222.33 724.31 M 219.7 727.33 C 220.37 726.24 221.24 725.48 222.33 724.31" fill="none" stroke="#f5f5f5" stroke-width="0.5" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" pointer-events="none"/><path d="M 190.88 711.7 C 204.07 712.18 214.61 712.13 222.38 711.7 M 190.88 711.7 C 201.32 712.11 210.26 712.69 222.38 711.7 M 222.38 711.7 C 221.29 716.11 222.7 719.55 222.38 724.9 M 222.38 711.7 C 222.13 714.95 222.38 719.71 222.38 724.9 M 222.38 724.9 C 215.31 726.81 205.85 723.2 190.88 724.9 M 222.38 724.9 C 210.92 725.8 199.57 724.39 190.88 724.9 M 190.88 724.9 C 190.74 722.03 190.62 717.06 190.88 711.7 M 190.88 724.9 C 191.15 720.16 191.58 715.92 190.88 711.7" fill="none" stroke="#666666" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 29px; height: 1px; padding-top: 718px; margin-left: 192px;"><div data-drawio-colors="color: #333333; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 7px; font-family: &quot;Comic Sans MS&quot;; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: none; font-style: italic; white-space: normal; overflow-wrap: normal;"><font style="font-size: 7px;"><span style="font-size: 7px;">Text</span></font></div></div></div></foreignObject><text x="207" y="720" fill="#333333" font-family="Comic Sans MS" font-size="7px" text-anchor="middle" font-style="italic">Text</text></switch></g><path d="M 73 282 L 653 282" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="2 6" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 282px; margin-left: 363px;"><div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; background-color: rgb(255, 255, 255); white-space: nowrap;">OR</div></div></div></foreignObject><text x="363" y="285" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">OR</text></switch></g><path d="M 738 191 L 738 209 L 738 242.92" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/><path d="M 738 248.17 L 734.5 241.17 L 738 242.92 L 741.5 241.17 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/><path d="M 577 219 L 586.26 219 L 589.14 219" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/><path d="M 594.39 219 L 587.39 222.5 L 589.14 219 L 587.39 215.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/><rect x="417.63" y="179" width="159.37" height="80" fill="#f5e8c4" stroke="#d6b656" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 157px; height: 1px; padding-top: 186px; margin-left: 419px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><font style="font-size: 16px;"><b>Decision Generator</b></font></div></div></div></foreignObject><text x="497" y="198" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Decision Generator</text></switch></g><path d="M 47.49 425 L 797.25 425" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="2 6" pointer-events="none"/><path d="M 332.45 350.28 L 366.8 350.3 L 394.63 350.54" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/><path d="M 399.88 350.59 L 392.85 354.03 L 394.63 350.54 L 392.91 347.03 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/><rect x="120" y="335.53" width="212.45" height="59" fill="#d5e8d4" stroke="#82b366" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 210px; height: 1px; padding-top: 365px; margin-left: 121px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><b style="font-size: 12px;"><font style="font-size: 14px;">Policy</font></b></div></div></div></foreignObject><text x="226" y="369" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="14px" text-anchor="middle">Policy</text></switch></g><path d="M 455 590.24 L 632 590.24 Q 612 599.74 632 609.24 L 455 609.24 Q 435 599.74 455 590.24 Z" fill="#f5f5f5" stroke="#666666" stroke-miterlimit="10" transform="translate(0,599.74)scale(1,-1)translate(0,-599.74)" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 185px; height: 1px; padding-top: 600px; margin-left: 446px;"><div data-drawio-colors="color: #333333; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><font style="font-size: 10px;">State Intepreter</font></div></div></div></foreignObject><text x="539" y="603" fill="#333333" font-family="Helvetica" font-size="12px" text-anchor="middle">State Intepreter</text></switch></g><path d="M 454 463 L 633 463 Q 613 472.5 633 482 L 454 482 Q 434 472.5 454 463 Z" fill="#f5f5f5" stroke="#666666" stroke-miterlimit="10" transform="rotate(180,538.5,472.5)" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 187px; height: 1px; padding-top: 473px; margin-left: 445px;"><div data-drawio-colors="color: #333333; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><font style="font-size: 10px;">Action Intepreter</font></div></div></div></foreignObject><text x="539" y="476" fill="#333333" font-family="Helvetica" font-size="12px" text-anchor="middle">Action Intepreter</text></switch></g><path d="M 445 599.74 L 382.5 599.7 L 382.5 601 L 206 601 L 206 587.37" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/><path d="M 206 582.12 L 209.5 589.12 L 206 587.37 L 202.5 589.12 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 601px; margin-left: 316px;"><div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; background-color: rgb(255, 255, 255); white-space: nowrap;">State/Reward (Execution Results)</div></div></div></foreignObject><text x="316" y="604" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">State/Reward (Execution Results)</text></switch></g><rect x="104.46" y="538" width="96.39" height="39" rx="5.85" ry="5.85" fill-opacity="0.99" fill="#f5f5f5" stroke="#666666" stroke-opacity="0.99" stroke-dasharray="3 3" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 94px; height: 1px; padding-top: 558px; margin-left: 105px;"><div data-drawio-colors="color: #333333; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 17px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><b style="font-size: 14px; line-height: 1;">Order Execution<br /></b></div></div></div></foreignObject><text x="153" y="563" fill="#333333" font-family="Helvetica" font-size="17px" text-anchor="middle">Order Execu...</text></switch></g><rect x="211.98" y="538" width="96.39" height="39" rx="5.85" ry="5.85" fill-opacity="0.99" fill="#f5f5f5" stroke="#666666" stroke-opacity="0.99" stroke-dasharray="3 3" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 94px; height: 1px; padding-top: 558px; margin-left: 213px;"><div data-drawio-colors="color: #333333; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 17px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><b style="font-size: 14px; line-height: 1;">Portfolio Management<br /></b></div></div></div></foreignObject><text x="260" y="563" fill="#333333" font-family="Helvetica" font-size="17px" text-anchor="middle">Portfolio M...</text></switch></g><rect x="594.16" y="509" width="125.91" height="72.24" fill="#fff2cc" stroke="#d6b656" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 124px; height: 1px; padding-top: 516px; margin-left: 595px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><b><font style="font-size: 16px">Executor</font></b></div></div></div></foreignObject><text x="657" y="528" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Executor</text></switch></g><rect x="610.59" y="539" width="91.39" height="36" rx="5.4" ry="5.4" fill-opacity="0.99" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-opacity="0.99" stroke-dasharray="3 3" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 99px; height: 1px; padding-top: 557px; margin-left: 607px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><b style="font-size: 14px; line-height: 1;">Portfolio Management<br /></b></div></div></div></foreignObject><text x="656" y="560" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="10px" text-anchor="middle">Portfolio Management&#xa;</text></switch></g><path d="M 401 380 L 366.8 380 L 338.82 379.82" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/><path d="M 333.57 379.79 L 340.59 376.33 L 338.82 379.82 L 340.55 383.33 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/><path d="M 75 294.14 L 371 294.14 L 371 311.14 L 361 326.14 L 75 326.14 Z" fill="#f5e8c4" stroke="#d6b656" stroke-miterlimit="10" pointer-events="none"/><path d="M 371 294.14 L 583 294.14 L 583 402 L 75 402 L 75 326.14" fill="none" stroke="#d6b656" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 285px; height: 1px; padding-top: 301px; margin-left: 86px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><font style=""><b style="font-size: 14px;">Reinforcement-Learning-based </b><span style="font-size: 14px;"><b>Strategy</b></span></font></div></div></div></foreignObject><text x="86" y="313" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Reinforcement-Learning-based Strategy</text></switch></g><path d="M 411 370.53 L 522 370.53 Q 502 380.03 522 389.53 L 411 389.53 Q 391 380.03 411 370.53 Z" fill="#f5f5f5" stroke="#666666" stroke-miterlimit="10" transform="translate(0,380.03)scale(1,-1)translate(0,-380.03)" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 119px; height: 1px; padding-top: 380px; margin-left: 402px;"><div data-drawio-colors="color: #333333; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><font style="font-size: 10px;">State Intepreter</font></div></div></div></foreignObject><text x="462" y="384" fill="#333333" font-family="Helvetica" font-size="12px" text-anchor="middle">State Intepreter</text></switch></g><path d="M 522 350.6 L 559.3 350.6 L 590.14 349.94" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/><path d="M 595.39 349.82 L 588.47 353.47 L 590.14 349.94 L 588.32 346.48 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/><path d="M 411 341.14 L 522 341.14 Q 502 350.64 522 360.14 L 411 360.14 Q 391 350.64 411 341.14 Z" fill="#f5f5f5" stroke="#666666" stroke-miterlimit="10" transform="rotate(180,461.5,350.64)" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 119px; height: 1px; padding-top: 351px; margin-left: 402px;"><div data-drawio-colors="color: #333333; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><font style="font-size: 10px;">Action Intepreter</font></div></div></div></foreignObject><text x="462" y="354" fill="#333333" font-family="Helvetica" font-size="12px" text-anchor="middle">Action Intepreter</text></switch></g><rect x="596.51" y="333.64" width="54.72" height="32.36" rx="4.85" ry="4.85" fill="#f5e8c4" stroke="#666666" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 53px; height: 1px; padding-top: 350px; margin-left: 598px;"><div data-drawio-colors="color: #333333; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">Decision</div></div></div></foreignObject><text x="624" y="353" fill="#333333" font-family="Helvetica" font-size="12px" text-anchor="middle">Decision</text></switch></g><rect x="520" y="222.96" width="50.27" height="30.23" rx="4.53" ry="4.53" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-dasharray="3 3" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 238px; margin-left: 516px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><b style="font-size: 14px ; line-height: 100%"><span style="font-size: 12px; font-weight: 400;">Rule-based</span><br /></b></div></div></div></foreignObject><text x="545" y="241" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="10px" text-anchor="middle">Rule-based&#xa;</text></switch></g><rect x="424" y="222.96" width="79.27" height="31.2" rx="4.68" ry="4.68" fill-opacity="0.99" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-opacity="0.99" stroke-dasharray="3 3" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 87px; height: 1px; padding-top: 239px; margin-left: 420px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><span style="font-size: 12px;">Portfolio Optimiaztion</span><b style="font-size: 14px"><br /></b></div></div></div></foreignObject><text x="464" y="242" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="10px" text-anchor="middle">Portfolio Optimiaz...</text></switch></g><rect x="595.51" y="203" width="55.72" height="32.36" rx="4.85" ry="4.85" fill="#f5e8c4" stroke="#666666" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 54px; height: 1px; padding-top: 219px; margin-left: 597px;"><div data-drawio-colors="color: #333333; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">Decision</div></div></div></foreignObject><text x="623" y="223" fill="#333333" font-family="Helvetica" font-size="12px" text-anchor="middle">Decision</text></switch></g><rect x="818" y="302" width="66" height="50" fill="#fff2cc" stroke="#d6b656" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 64px; height: 1px; padding-top: 304px; margin-left: 819px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; font-weight: bold; white-space: normal; overflow-wrap: normal;">Strategy</div></div></div></foreignObject><text x="851" y="316" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle" font-weight="bold">Strategy</text></switch></g><rect x="836.18" y="327.75" width="29.65" height="18.24" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-dasharray="3 3" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 28px; height: 1px; padding-top: 332px; margin-left: 837px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">...</div></div></div></foreignObject><text x="851" y="337" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="16px" text-anchor="middle">...</text></switch></g><rect x="689" y="112" width="94" height="79" rx="11.85" ry="11.85" fill="#fff2cc" stroke="#d6b656" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 96px; height: 1px; padding-top: 117px; margin-left: 688px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><b style=""><font style="font-size: 12px;">Decision</font><br /></b></div></div></div></foreignObject><text x="736" y="129" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Decision&#xa;</text></switch></g><rect x="691.33" y="135" width="89.33" height="14" rx="2.1" ry="2.1" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-dasharray="3 3" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 97px; height: 1px; padding-top: 142px; margin-left: 687px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 8px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><span style="font-size: 8px;"><font style="line-height: 0%; font-size: 8px;">Portfolio management<br style="font-size: 8px;" /></font></span></div></div></div></foreignObject><text x="736" y="144" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="8px" text-anchor="middle">Portfolio management&#xa;</text></switch></g><rect x="691.33" y="153" width="89.33" height="14" rx="2.1" ry="2.1" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-dasharray="3 3" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 97px; height: 1px; padding-top: 160px; margin-left: 687px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 8px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><font style="line-height: 0%; font-size: 8px;">Order execution<br style="font-size: 8px;" /></font></div></div></div></foreignObject><text x="736" y="162" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="8px" text-anchor="middle">Order execution&#xa;</text></switch></g><path d="M 691.03 172.44 C 691.03 172.44 691.03 172.44 691.03 172.44 M 691.03 172.44 C 691.03 172.44 691.03 172.44 691.03 172.44 M 691.43 178.08 C 692.78 176.1 694.77 174.39 696.02 172.8 M 691.43 178.08 C 692.2 176.6 693.58 175.49 696.02 172.8 M 692.48 182.97 C 695.94 179.34 698.34 174.29 701.66 172.41 M 692.48 182.97 C 695.61 179.68 699.28 174.9 701.66 172.41 M 695.5 185.6 C 700.24 182.08 702.1 177.7 706.65 172.77 M 695.5 185.6 C 698.79 181.09 702.92 177.51 706.65 172.77 M 700.49 185.96 C 705.38 180.52 706.27 178.65 712.29 172.37 M 700.49 185.96 C 704.44 180.16 708.73 175.38 712.29 172.37 M 706.13 185.56 C 710.42 182.28 711.53 178.2 717.28 172.73 M 706.13 185.56 C 708.37 182.96 711.93 179.59 717.28 172.73 M 711.12 185.92 C 712.86 182.59 716.11 178.98 722.92 172.34 M 711.12 185.92 C 714.66 183.6 717.42 178.9 722.92 172.34 M 716.76 185.53 C 719.75 181.9 723.22 179.17 727.91 172.7 M 716.76 185.53 C 720.41 181.48 723.25 177.1 727.91 172.7 M 721.75 185.89 C 724.65 181.58 727.53 178.68 733.55 172.3 M 721.75 185.89 C 725.58 181.99 729.05 176.6 733.55 172.3 M 727.39 185.49 C 728.33 182.96 732.63 178.66 738.54 172.66 M 727.39 185.49 C 730.61 182.81 732.44 179.3 738.54 172.66 M 732.38 185.85 C 737.33 183.8 737.82 177.97 744.18 172.27 M 732.38 185.85 C 736.33 180.75 740.55 177.79 744.18 172.27 M 738.02 185.46 C 740.71 183.11 744.27 179.01 749.17 172.63 M 738.02 185.46 C 742.11 182.09 744.45 176.69 749.17 172.63 M 743.01 185.82 C 747.31 180.36 750.78 176.24 754.81 172.23 M 743.01 185.82 C 745.53 182.31 749.19 179.92 754.81 172.23 M 748.65 185.42 C 754.44 181.28 756.93 175.97 759.8 172.59 M 748.65 185.42 C 751.48 180.83 754.65 178.42 759.8 172.59 M 753.64 185.78 C 756.42 181.18 759.61 179.54 765.44 172.2 M 753.64 185.78 C 755.3 182.26 757.87 180.46 765.44 172.2 M 759.28 185.39 C 760.89 182.72 764.59 177.76 770.43 172.56 M 759.28 185.39 C 762.47 181.6 763.55 179.32 770.43 172.56 M 764.27 185.75 C 768.84 180.8 770.11 178.42 776.07 172.16 M 764.27 185.75 C 768.24 181.14 773.23 174.8 776.07 172.16 M 769.91 185.35 C 772.86 181.56 778.03 177.75 781.06 172.52 M 769.91 185.35 C 772.64 182.95 775.98 179.87 781.06 172.52 M 774.9 185.71 C 775.93 183.71 778.42 181.51 780.8 178.92 M 774.9 185.71 C 776.77 183.25 778.19 181.09 780.8 178.92" fill="none" stroke="rgb(255, 255, 255)" stroke-width="0.5" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" pointer-events="none"/><path d="M 693.43 170 M 693.43 170 C 713.77 168.38 738.79 170.59 778.56 170 M 693.43 170 C 721.03 169.36 746.27 170.7 778.56 170 M 778.56 170 C 781.82 169.78 782.33 169.05 780.66 172.1 M 778.56 170 C 779.12 169.07 780.21 170.06 780.66 172.1 M 780.66 172.1 C 781.4 176.6 780.35 179.37 780.66 181.9 M 780.66 172.1 C 780.62 174.75 780.69 177.33 780.66 181.9 M 780.66 181.9 C 779.11 182.21 779.74 185.42 778.56 184 M 780.66 181.9 C 781.65 182.03 780.11 184.16 778.56 184 M 778.56 184 C 759.48 185.79 740.61 185.01 693.43 184 M 778.56 184 C 746.74 183.89 712.44 182.51 693.43 184 M 693.43 184 C 691.85 183.94 692.64 182.81 691.33 181.9 M 693.43 184 C 693.49 183.66 690.95 185.53 691.33 181.9 M 691.33 181.9 C 690.74 178.4 691.49 175.26 691.33 172.1 M 691.33 181.9 C 691.55 178.71 691.85 175.31 691.33 172.1 M 691.33 172.1 C 689.85 169.92 692.52 171.19 693.43 170 M 691.33 172.1 C 690.25 169.26 694.14 170.95 693.43 170" fill="none" stroke="rgb(0, 0, 0)" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 97px; height: 1px; padding-top: 177px; margin-left: 687px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 8px; font-family: &quot;Comic Sans MS&quot;; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><font style="line-height: 0%; font-size: 8px;"><i>Asset allocation</i><br style="font-size: 8px;" /></font></div></div></div></foreignObject><text x="736" y="179" fill="rgb(0, 0, 0)" font-family="Comic Sans MS" font-size="8px" text-anchor="middle">Asset allocation&#xa;</text></switch></g><rect x="639" y="463" width="88" height="19" rx="2.85" ry="2.85" fill="#fff2cc" stroke="#d6b656" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 90px; height: 1px; padding-top: 473px; margin-left: 638px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><font style="font-size: 11px;">Decision</font></div></div></div></foreignObject><text x="683" y="476" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Decision</text></switch></g><rect x="636.79" y="590.24" width="91.21" height="19" rx="2.85" ry="2.85" fill="#fff2cc" stroke="#d6b656" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 93px; height: 1px; padding-top: 600px; margin-left: 636px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><span style="font-size: 11px;">Execution Results</span><b style=""><br /></b></div></div></div></foreignObject><text x="682" y="603" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Execution Results</text></switch></g><rect x="708" y="366" width="60.21" height="28" rx="4.2" ry="4.2" fill="#fff2cc" stroke="#d6b656" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 62px; height: 1px; padding-top: 380px; margin-left: 707px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><span style="font-size: 11px;">Execution<br />Results</span><b style=""><br /></b></div></div></div></foreignObject><text x="738" y="384" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Execution...</text></switch></g><path d="M 737.86 333.53 L 738.03 366" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/><rect x="454" y="510" width="125.91" height="72.24" fill="#fff2cc" stroke="#d6b656" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 124px; height: 1px; padding-top: 517px; margin-left: 455px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><b><font style="font-size: 16px">Executor</font></b></div></div></div></foreignObject><text x="517" y="529" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Executor</text></switch></g><rect x="472.43" y="540" width="91.39" height="35" rx="5.25" ry="5.25" fill-opacity="0.99" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-opacity="0.99" stroke-dasharray="3 3" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 99px; height: 1px; padding-top: 558px; margin-left: 468px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><b style="font-size: 14px; line-height: 1;">Order Execution<br /></b></div></div></div></foreignObject><text x="518" y="561" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="10px" text-anchor="middle">Order Execution&#xa;</text></switch></g><path d="M 401.95 219.08 L 411.26 219.08" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/><path d="M 416.51 219.07 L 409.52 222.58 L 411.26 219.08 L 409.51 215.58 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/><rect x="273.13" y="177.14" width="128.82" height="84" fill="#d5e8d4" stroke="#82b366" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 127px; height: 1px; padding-top: 184px; margin-left: 274px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><b><font style="font-size: 16px">Forecast Model</font><br /></b></div></div></div></foreignObject><text x="338" y="196" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Forecast Model&#xa;</text></switch></g><rect x="344.29" y="224.12" width="50.27" height="30.23" rx="4.53" ry="4.53" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-dasharray="3 3" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 239px; margin-left: 340px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><span style="font-size: 14px ; line-height: 100%">Risk<br /></span></div></div></div></foreignObject><text x="369" y="242" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="10px" text-anchor="middle">Risk&#xa;</text></switch></g><rect x="282.13" y="223.64" width="50.27" height="31.2" rx="4.68" ry="4.68" fill-opacity="0.99" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-opacity="0.99" stroke-dasharray="3 3" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 239px; margin-left: 278px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><span style="font-size: 14px">Alpha<br /></span></div></div></div></foreignObject><text x="307" y="242" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="10px" text-anchor="middle">Alpha&#xa;</text></switch></g></g><switch><g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/><a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank"><text text-anchor="middle" font-size="10px" x="50%" y="100%">Text is not SVG - cannot display</text></a></switch></svg>
