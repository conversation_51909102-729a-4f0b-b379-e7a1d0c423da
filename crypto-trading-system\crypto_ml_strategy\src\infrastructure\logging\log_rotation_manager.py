"""
Crypto ML Strategy - 日志轮转管理器

该模块实现了多种日志轮转策略，包括基于文件大小、时间和数量的轮转，
以及日志文件压缩和过期清理功能。

主要功能：
- LogRotationManager: 日志轮转主管理器
- SizeBasedRotation: 基于文件大小的轮转
- TimeBasedRotation: 基于时间的轮转
- CountBasedRotation: 基于文件数量的轮转
- CompressionManager: 日志文件压缩管理
- CleanupScheduler: 过期日志清理调度器

作者: Crypto ML Strategy Team
日期: 2024-01-20
版本: 1.0.0
"""

import asyncio
import gzip
import os
import shutil
import threading
import time
import zipfile
from abc import ABC, abstractmethod
from dataclasses import dataclass
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Dict, List, Optional, Union, Callable
from loguru import logger
import schedule
from concurrent.futures import ThreadPoolExecutor


@dataclass
class RotationConfig:
    """轮转配置"""
    strategy: str = "size"  # size, time, count
    max_size_mb: int = 100
    rotation_time: str = "daily"  # daily, hourly, weekly, monthly
    max_files: int = 30
    compression: str = "zip"  # zip, gzip, none
    retention_days: int = 30
    cleanup_enabled: bool = True
    async_compression: bool = True
    
    def validate(self) -> bool:
        """验证配置有效性"""
        try:
            if self.strategy not in ["size", "time", "count"]:
                return False
            
            if self.max_size_mb <= 0 or self.max_files <= 0 or self.retention_days <= 0:
                return False
            
            if self.rotation_time not in ["daily", "hourly", "weekly", "monthly"]:
                return False
            
            if self.compression not in ["zip", "gzip", "none"]:
                return False
            
            return True
        except Exception:
            return False


class RotationStrategy(ABC):
    """轮转策略基类"""
    
    def __init__(self, config: RotationConfig):
        self.config = config
        self._lock = threading.RLock()
    
    @abstractmethod
    def should_rotate(self, file_path: Path) -> bool:
        """判断是否需要轮转"""
        pass
    
    @abstractmethod
    def get_rotated_filename(self, file_path: Path) -> str:
        """获取轮转后的文件名"""
        pass
    
    def rotate_file(self, file_path: Path) -> Optional[Path]:
        """执行文件轮转"""
        try:
            with self._lock:
                if not file_path.exists():
                    return None
                
                # 生成轮转后的文件名
                rotated_name = self.get_rotated_filename(file_path)
                rotated_path = file_path.parent / rotated_name
                
                # 确保目标文件不存在
                counter = 1
                original_rotated_path = rotated_path
                while rotated_path.exists():
                    stem = original_rotated_path.stem
                    suffix = original_rotated_path.suffix
                    rotated_path = original_rotated_path.parent / f"{stem}_{counter}{suffix}"
                    counter += 1
                
                # 移动文件
                shutil.move(str(file_path), str(rotated_path))
                
                logger.info(f"File rotated: {file_path} -> {rotated_path}")
                return rotated_path
                
        except Exception as e:
            logger.error(f"Failed to rotate file {file_path}: {e}")
            return None


class SizeBasedRotation(RotationStrategy):
    """基于文件大小的轮转策略"""
    
    def should_rotate(self, file_path: Path) -> bool:
        """检查文件大小是否超过限制"""
        try:
            if not file_path.exists():
                return False
            
            file_size_mb = file_path.stat().st_size / (1024 * 1024)
            return file_size_mb >= self.config.max_size_mb
            
        except Exception as e:
            logger.error(f"Failed to check file size for {file_path}: {e}")
            return False
    
    def get_rotated_filename(self, file_path: Path) -> str:
        """生成基于时间戳的轮转文件名"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        stem = file_path.stem
        suffix = file_path.suffix
        return f"{stem}_{timestamp}{suffix}"


class TimeBasedRotation(RotationStrategy):
    """基于时间的轮转策略"""
    
    def __init__(self, config: RotationConfig):
        super().__init__(config)
        self._last_rotation_time = {}
    
    def should_rotate(self, file_path: Path) -> bool:
        """检查是否到了轮转时间"""
        try:
            if not file_path.exists():
                return False
            
            file_key = str(file_path)
            current_time = datetime.now()
            
            # 获取上次轮转时间
            last_rotation = self._last_rotation_time.get(file_key)
            if last_rotation is None:
                # 使用文件修改时间作为初始时间
                last_rotation = datetime.fromtimestamp(file_path.stat().st_mtime)
                self._last_rotation_time[file_key] = last_rotation
            
            # 计算轮转间隔
            if self.config.rotation_time == "hourly":
                interval = timedelta(hours=1)
            elif self.config.rotation_time == "daily":
                interval = timedelta(days=1)
            elif self.config.rotation_time == "weekly":
                interval = timedelta(weeks=1)
            elif self.config.rotation_time == "monthly":
                interval = timedelta(days=30)
            else:
                interval = timedelta(days=1)
            
            should_rotate = current_time - last_rotation >= interval
            
            if should_rotate:
                self._last_rotation_time[file_key] = current_time
            
            return should_rotate
            
        except Exception as e:
            logger.error(f"Failed to check rotation time for {file_path}: {e}")
            return False
    
    def get_rotated_filename(self, file_path: Path) -> str:
        """生成基于轮转时间的文件名"""
        if self.config.rotation_time == "hourly":
            timestamp = datetime.now().strftime("%Y%m%d_%H")
        elif self.config.rotation_time == "daily":
            timestamp = datetime.now().strftime("%Y%m%d")
        elif self.config.rotation_time == "weekly":
            timestamp = datetime.now().strftime("%Y_W%U")
        elif self.config.rotation_time == "monthly":
            timestamp = datetime.now().strftime("%Y%m")
        else:
            timestamp = datetime.now().strftime("%Y%m%d")
        
        stem = file_path.stem
        suffix = file_path.suffix
        return f"{stem}_{timestamp}{suffix}"


class CountBasedRotation(RotationStrategy):
    """基于文件数量的轮转策略"""
    
    def should_rotate(self, file_path: Path) -> bool:
        """检查同类型文件数量是否超过限制"""
        try:
            if not file_path.exists():
                return False
            
            # 查找同类型的文件
            pattern = f"{file_path.stem}*{file_path.suffix}"
            similar_files = list(file_path.parent.glob(pattern))
            
            return len(similar_files) >= self.config.max_files
            
        except Exception as e:
            logger.error(f"Failed to check file count for {file_path}: {e}")
            return False
    
    def get_rotated_filename(self, file_path: Path) -> str:
        """生成基于序号的轮转文件名"""
        # 查找现有文件的最大序号
        pattern = f"{file_path.stem}_*{file_path.suffix}"
        similar_files = list(file_path.parent.glob(pattern))
        
        max_number = 0
        for similar_file in similar_files:
            try:
                # 提取序号
                name_part = similar_file.stem.replace(file_path.stem + "_", "")
                if name_part.isdigit():
                    max_number = max(max_number, int(name_part))
            except Exception:
                continue
        
        next_number = max_number + 1
        stem = file_path.stem
        suffix = file_path.suffix
        return f"{stem}_{next_number:03d}{suffix}"


class CompressionManager:
    """日志文件压缩管理器"""
    
    def __init__(self, config: RotationConfig):
        self.config = config
        self._executor = ThreadPoolExecutor(max_workers=2, thread_name_prefix="compression")
        self._compression_queue = asyncio.Queue()
        self._lock = threading.RLock()
    
    def compress_file(self, file_path: Path) -> Optional[Path]:
        """压缩文件"""
        try:
            if not file_path.exists():
                return None
            
            if self.config.compression == "none":
                return file_path
            
            compressed_path = self._get_compressed_path(file_path)
            
            if self.config.compression == "gzip":
                self._compress_gzip(file_path, compressed_path)
            elif self.config.compression == "zip":
                self._compress_zip(file_path, compressed_path)
            else:
                return file_path
            
            # 删除原文件
            file_path.unlink()
            
            logger.info(f"File compressed: {file_path} -> {compressed_path}")
            return compressed_path
            
        except Exception as e:
            logger.error(f"Failed to compress file {file_path}: {e}")
            return None
    
    def compress_file_async(self, file_path: Path) -> None:
        """异步压缩文件"""
        if self.config.async_compression:
            self._executor.submit(self.compress_file, file_path)
        else:
            self.compress_file(file_path)
    
    def _get_compressed_path(self, file_path: Path) -> Path:
        """获取压缩后的文件路径"""
        if self.config.compression == "gzip":
            return file_path.with_suffix(file_path.suffix + ".gz")
        elif self.config.compression == "zip":
            return file_path.with_suffix(file_path.suffix + ".zip")
        else:
            return file_path
    
    def _compress_gzip(self, source_path: Path, target_path: Path) -> None:
        """使用gzip压缩"""
        with open(source_path, 'rb') as f_in:
            with gzip.open(target_path, 'wb') as f_out:
                shutil.copyfileobj(f_in, f_out)
    
    def _compress_zip(self, source_path: Path, target_path: Path) -> None:
        """使用zip压缩"""
        with zipfile.ZipFile(target_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            zipf.write(source_path, source_path.name)
    
    def shutdown(self) -> None:
        """关闭压缩管理器"""
        try:
            self._executor.shutdown(wait=True, timeout=30)
        except Exception as e:
            logger.error(f"Error shutting down compression manager: {e}")


class CleanupScheduler:
    """过期日志清理调度器"""
    
    def __init__(self, config: RotationConfig):
        self.config = config
        self._running = False
        self._thread: Optional[threading.Thread] = None
        self._lock = threading.RLock()
    
    def start(self) -> None:
        """启动清理调度器"""
        try:
            with self._lock:
                if self._running:
                    return
                
                self._running = True
                
                # 设置清理任务
                schedule.every().day.at("02:00").do(self._cleanup_expired_logs)
                
                # 启动调度线程
                self._thread = threading.Thread(
                    target=self._run_scheduler,
                    name="log_cleanup_scheduler",
                    daemon=True
                )
                self._thread.start()
                
                logger.info("Log cleanup scheduler started")
                
        except Exception as e:
            logger.error(f"Failed to start cleanup scheduler: {e}")
    
    def stop(self) -> None:
        """停止清理调度器"""
        try:
            with self._lock:
                if not self._running:
                    return
                
                self._running = False
                schedule.clear()
                
                if self._thread and self._thread.is_alive():
                    self._thread.join(timeout=5)
                
                logger.info("Log cleanup scheduler stopped")
                
        except Exception as e:
            logger.error(f"Error stopping cleanup scheduler: {e}")
    
    def _run_scheduler(self) -> None:
        """运行调度器"""
        while self._running:
            try:
                schedule.run_pending()
                time.sleep(60)  # 每分钟检查一次
            except Exception as e:
                logger.error(f"Error in cleanup scheduler: {e}")
                time.sleep(60)
    
    def _cleanup_expired_logs(self) -> None:
        """清理过期日志"""
        try:
            if not self.config.cleanup_enabled:
                return
            
            cutoff_date = datetime.now() - timedelta(days=self.config.retention_days)
            
            # 查找日志目录
            log_dirs = [Path("logs"), Path("./logs"), Path("../logs")]
            
            for log_dir in log_dirs:
                if log_dir.exists() and log_dir.is_dir():
                    self._cleanup_directory(log_dir, cutoff_date)
            
        except Exception as e:
            logger.error(f"Error during log cleanup: {e}")
    
    def _cleanup_directory(self, directory: Path, cutoff_date: datetime) -> None:
        """清理指定目录"""
        try:
            deleted_count = 0
            deleted_size = 0
            
            for file_path in directory.rglob("*.log*"):
                try:
                    if file_path.is_file():
                        file_mtime = datetime.fromtimestamp(file_path.stat().st_mtime)
                        
                        if file_mtime < cutoff_date:
                            file_size = file_path.stat().st_size
                            file_path.unlink()
                            deleted_count += 1
                            deleted_size += file_size
                            
                            logger.debug(f"Deleted expired log file: {file_path}")
                            
                except Exception as e:
                    logger.warning(f"Failed to delete file {file_path}: {e}")
            
            if deleted_count > 0:
                deleted_size_mb = deleted_size / (1024 * 1024)
                logger.info(f"Cleanup completed: deleted {deleted_count} files, "
                           f"freed {deleted_size_mb:.2f} MB from {directory}")
            
        except Exception as e:
            logger.error(f"Error cleaning directory {directory}: {e}")


class LogRotationManager:
    """日志轮转主管理器"""
    
    def __init__(self, config: Optional[RotationConfig] = None):
        self.config = config or RotationConfig()
        self._strategies: Dict[str, RotationStrategy] = {}
        self._compression_manager = CompressionManager(self.config)
        self._cleanup_scheduler = CleanupScheduler(self.config)
        self._monitored_files: Dict[str, Path] = {}
        self._lock = threading.RLock()
        self._initialized = False
    
    def initialize(self) -> bool:
        """初始化轮转管理器"""
        try:
            with self._lock:
                if self._initialized:
                    return True
                
                # 验证配置
                if not self.config.validate():
                    raise ValueError("Invalid rotation configuration")
                
                # 初始化轮转策略
                self._strategies = {
                    "size": SizeBasedRotation(self.config),
                    "time": TimeBasedRotation(self.config),
                    "count": CountBasedRotation(self.config)
                }
                
                # 启动清理调度器
                if self.config.cleanup_enabled:
                    self._cleanup_scheduler.start()
                
                self._initialized = True
                logger.info("Log rotation manager initialized successfully")
                return True
                
        except Exception as e:
            logger.error(f"Failed to initialize rotation manager: {e}")
            return False
    
    def shutdown(self) -> None:
        """关闭轮转管理器"""
        try:
            with self._lock:
                if not self._initialized:
                    return
                
                # 停止清理调度器
                self._cleanup_scheduler.stop()
                
                # 关闭压缩管理器
                self._compression_manager.shutdown()
                
                self._initialized = False
                logger.info("Log rotation manager shutdown completed")
                
        except Exception as e:
            logger.error(f"Error during rotation manager shutdown: {e}")
    
    def add_monitored_file(self, file_path: Union[str, Path], name: Optional[str] = None) -> None:
        """添加监控的日志文件"""
        try:
            with self._lock:
                path_obj = Path(file_path)
                file_name = name or path_obj.name
                self._monitored_files[file_name] = path_obj
                
                logger.debug(f"Added monitored file: {file_name} -> {path_obj}")
                
        except Exception as e:
            logger.error(f"Failed to add monitored file {file_path}: {e}")
    
    def check_and_rotate(self, file_path: Optional[Union[str, Path]] = None) -> List[Path]:
        """检查并执行轮转"""
        try:
            with self._lock:
                rotated_files = []
                
                # 确定要检查的文件
                if file_path:
                    files_to_check = [Path(file_path)]
                else:
                    files_to_check = list(self._monitored_files.values())
                
                # 获取轮转策略
                strategy = self._strategies.get(self.config.strategy)
                if not strategy:
                    logger.error(f"Unknown rotation strategy: {self.config.strategy}")
                    return rotated_files
                
                # 检查每个文件
                for file_path_obj in files_to_check:
                    if strategy.should_rotate(file_path_obj):
                        rotated_path = strategy.rotate_file(file_path_obj)
                        
                        if rotated_path:
                            rotated_files.append(rotated_path)
                            
                            # 异步压缩
                            self._compression_manager.compress_file_async(rotated_path)
                
                return rotated_files
                
        except Exception as e:
            logger.error(f"Error during rotation check: {e}")
            return []
    
    def force_rotate(self, file_path: Union[str, Path]) -> Optional[Path]:
        """强制轮转指定文件"""
        try:
            with self._lock:
                path_obj = Path(file_path)
                strategy = self._strategies.get(self.config.strategy)
                
                if not strategy:
                    logger.error(f"Unknown rotation strategy: {self.config.strategy}")
                    return None
                
                rotated_path = strategy.rotate_file(path_obj)
                
                if rotated_path:
                    # 异步压缩
                    self._compression_manager.compress_file_async(rotated_path)
                
                return rotated_path
                
        except Exception as e:
            logger.error(f"Failed to force rotate file {file_path}: {e}")
            return None
    
    def get_rotation_statistics(self) -> Dict[str, Any]:
        """获取轮转统计信息"""
        try:
            with self._lock:
                stats = {
                    "initialized": self._initialized,
                    "strategy": self.config.strategy,
                    "monitored_files": len(self._monitored_files),
                    "config": {
                        "max_size_mb": self.config.max_size_mb,
                        "rotation_time": self.config.rotation_time,
                        "max_files": self.config.max_files,
                        "compression": self.config.compression,
                        "retention_days": self.config.retention_days,
                        "cleanup_enabled": self.config.cleanup_enabled
                    }
                }
                
                return stats
                
        except Exception as e:
            logger.error(f"Failed to get rotation statistics: {e}")
            return {}


# 全局轮转管理器实例
_global_rotation_manager: Optional[LogRotationManager] = None


def get_global_rotation_manager() -> LogRotationManager:
    """获取全局轮转管理器实例"""
    global _global_rotation_manager
    
    if _global_rotation_manager is None:
        _global_rotation_manager = LogRotationManager()
        _global_rotation_manager.initialize()
    
    return _global_rotation_manager


# 模块级别的日志器
module_logger = logger.bind(name=__name__)