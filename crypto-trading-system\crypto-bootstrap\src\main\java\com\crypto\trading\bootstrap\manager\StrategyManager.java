package com.crypto.trading.bootstrap.manager;

import com.crypto.trading.bootstrap.client.StrategyServiceClient;
import com.crypto.trading.bootstrap.config.AppConfig;
import com.fasterxml.jackson.databind.JsonNode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Optional;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 策略管理器，负责管理策略服务。
 * 支持两种部署模式：
 * 1. 本地进程模式（local）：直接启动Python进程
 * 2. 远程服务模式（service）：通过HTTP客户端调用远程策略服务
 */
@Component
public class StrategyManager {

    private static final Logger log = LoggerFactory.getLogger(StrategyManager.class);

    /**
     * 应用配置，用于获取Python策略的相关设置。
     */
    private final AppConfig appConfig;
    
    /**
     * 策略服务客户端，用于与远程策略服务通信
     */
    private final StrategyServiceClient strategyServiceClient;

    /**
     * 持有对外部Python策略进程的引用。
     */
    private Process strategyProcess;
    
    /**
     * 健康检查调度器
     */
    private ScheduledExecutorService healthCheckScheduler;
    
    /**
     * 服务是否已启动
     */
    private volatile boolean serviceStarted = false;

    /**
     * 构造函数，注入应用配置和策略服务客户端。
     *
     * @param appConfig 应用配置
     * @param strategyServiceClient 策略服务客户端
     */
    @Autowired
    public StrategyManager(AppConfig appConfig, StrategyServiceClient strategyServiceClient) {
        this.appConfig = appConfig;
        this.strategyServiceClient = strategyServiceClient;
    }

    /**
     * 启动策略服务。
     * 根据配置的deployMode选择不同的启动方式：
     * - local：启动本地Python进程（兼容旧代码）
     * - service：使用HTTP客户端连接远程策略服务
     * 
     * @return 如果策略成功启动或被禁用，返回true；如果启动失败，返回false。
     */
    public boolean startStrategy() {
        // 检查策略是否在配置中启用
        if (!appConfig.getPythonStrategy().isEnabled()) {
            log.info("策略服务未启用，跳过启动。");
            return true;
        }
        
        String deployMode = appConfig.getPythonStrategy().getDeployMode();
        log.info("策略服务部署模式: {}", deployMode);
        
        // 根据部署模式选择不同的启动方式
        if ("local".equalsIgnoreCase(deployMode)) {
            return startLocalProcess();
        } else if ("service".equalsIgnoreCase(deployMode)) {
            return connectToRemoteService();
        } else {
            log.error("不支持的策略服务部署模式: {}", deployMode);
            return false;
        }
    }
    
    /**
     * 启动本地Python进程
     * 
     * @return 如果进程成功启动，返回true；否则返回false
     */
    private boolean startLocalProcess() {
        try {
            // 确保旧进程已停止，避免僵尸进程
            stopStrategy();

            // 从配置中获取Python可执行文件路径、工作目录和策略脚本路径
            String pythonPath = appConfig.getPythonStrategy().getPythonPath();
            String workingDirStr = appConfig.getPythonStrategy().getWorkingDir();
            String strategyPath = appConfig.getPythonStrategy().getStrategyPath();

            // 使用NIO Path API来健壮地处理路径，确保跨平台兼容性和正确性
            // 1. 获取工作目录的Path对象，并将其转换为绝对和规范化的路径
            Path workingDirPath = Paths.get(workingDirStr).toAbsolutePath().normalize();
            // 2. 获取策略脚本的绝对路径
            Path strategyAbsolutePath = Paths.get(strategyPath).toAbsolutePath().normalize();

            log.info("Python路径: {}, 策略绝对路径: {}, 工作目录: {}", pythonPath, strategyAbsolutePath, workingDirPath);

            // 验证工作目录和策略文件是否存在，增加系统的健壮性
            if (!Files.exists(workingDirPath) || !Files.isDirectory(workingDirPath)) {
                log.error("工作目录不存在或不是一个目录: {}", workingDirPath);
                return false;
            }
            if (!Files.exists(strategyAbsolutePath) || !Files.isRegularFile(strategyAbsolutePath)) {
                log.error("策略脚本文件不存在或不是一个文件: {}", strategyAbsolutePath);
                return false;
            }

            // 创建进程构建器，使用绝对路径
            ProcessBuilder processBuilder = new ProcessBuilder(pythonPath, strategyAbsolutePath.toString());
            processBuilder.directory(workingDirPath.toFile());

            // 重定向错误流到标准输出流，方便统一查看日志
            processBuilder.redirectErrorStream(true);

            // 启动进程
            strategyProcess = processBuilder.start();
            log.info("Python策略进程已启动，PID: {}", strategyProcess.pid());

            // 异步读取Python脚本的输出
            // 使用虚拟线程来处理IO密集型任务，避免阻塞平台线程
            Thread.ofVirtual().start(() -> {
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(strategyProcess.getInputStream()))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        log.info("[Python策略] {}", line);
                    }
                } catch (IOException e) {
                    // 当进程被外部终止时，readLine会抛出IOException，这里进行判断
                    if (strategyProcess != null && strategyProcess.isAlive()) {
                        log.error("读取Python策略进程输出时出错", e);
                    } else {
                        log.info("Python策略进程已终止，输出读取结束。");
                    }
                } finally {
                     if (strategyProcess != null) {
                        log.info("Python策略进程 (PID: {}) 已终止，退出码: {}", strategyProcess.pid(), strategyProcess.exitValue());
                    }
                }
            });

            // 注册一个JVM关闭钩子，确保在应用退出时Python进程也能被优雅地关闭
            Runtime.getRuntime().addShutdownHook(new Thread(this::stopStrategy));
            serviceStarted = true;
            return true;

        } catch (IOException e) {
            log.error("启动Python策略进程失败，请检查Python环境和脚本路径是否正确。", e);
            return false;
        }
    }
    
    /**
     * 连接到远程策略服务
     * 
     * @return 如果连接成功，返回true；否则返回false
     */
    private boolean connectToRemoteService() {
        try {
            // 停止已有的健康检查调度器
            stopHealthCheck();
            
            // 尝试连接服务并获取状态
            JsonNode statusNode = strategyServiceClient.getStatus();
            if (statusNode == null) {
                log.error("无法连接到策略服务，请检查serviceUrls配置");
                return false;
            }
            
            String status = statusNode.path("status").asText("unknown");
            log.info("策略服务状态: {}", status);
            
            // 如果服务未运行，尝试初始化
            if (!"running".equals(status)) {
                log.info("策略服务未处于运行状态，尝试初始化");
                boolean initResult = strategyServiceClient.initializeService();
                if (!initResult) {
                    log.error("无法初始化策略服务");
                    return false;
                }
                log.info("策略服务初始化成功");
            }
            
            // 启动定期健康检查
            startHealthCheck();
            
            // 服务成功连接
            serviceStarted = true;
            log.info("成功连接到策略服务");
            return true;
            
        } catch (Exception e) {
            log.error("连接策略服务失败", e);
            return false;
        }
    }

    /**
     * 启动定期健康检查
     */
    private void startHealthCheck() {
        // 如果已经有健康检查调度器，先停止它
        stopHealthCheck();
        
        // 创建新的调度器
        healthCheckScheduler = Executors.newSingleThreadScheduledExecutor();
        
        // 定期执行健康检查
        int interval = appConfig.getPythonStrategy().getHealthCheckInterval();
        healthCheckScheduler.scheduleAtFixedRate(() -> {
            try {
                JsonNode statusNode = strategyServiceClient.getStatus();
                if (statusNode != null) {
                    String status = statusNode.path("status").asText();
                    log.debug("策略服务定期健康检查: {}", status);
                } else {
                    log.warn("策略服务健康检查失败");
                }
            } catch (Exception e) {
                log.error("执行健康检查时发生错误", e);
            }
        }, interval, interval, TimeUnit.MILLISECONDS);
        
        log.info("已启动策略服务健康检查，间隔: {}毫秒", interval);
    }
    
    /**
     * 停止健康检查调度器
     */
    private void stopHealthCheck() {
        if (healthCheckScheduler != null) {
            healthCheckScheduler.shutdown();
            try {
                if (!healthCheckScheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                    healthCheckScheduler.shutdownNow();
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                healthCheckScheduler.shutdownNow();
            }
            healthCheckScheduler = null;
        }
    }

    /**
     * 停止当前正在运行的策略服务。
     * 根据部署模式选择不同的停止方式。
     */
    public void stopStrategy() {
        String deployMode = appConfig.getPythonStrategy().getDeployMode();
        
        if ("local".equalsIgnoreCase(deployMode)) {
            stopLocalProcess();
        } else if ("service".equalsIgnoreCase(deployMode)) {
            disconnectFromRemoteService();
        }
        
        serviceStarted = false;
    }
    
    /**
     * 停止本地Python进程
     */
    private void stopLocalProcess() {
        if (strategyProcess != null) {
            long pid = -1;
            try {
                pid = strategyProcess.pid();
            } catch (UnsupportedOperationException e) {
                // Process has already terminated
            }
            if (pid != -1) {
                log.info("正在停止Python策略进程 (PID: {})...", pid);
            }
            // 先尝试正常终止
            strategyProcess.destroy();
            try {
                // 等待一段时间让进程退出
                boolean exited = strategyProcess.waitFor(5, TimeUnit.SECONDS);
                if (!exited) {
                    // 如果无法正常终止，则强制终止
                    log.warn("Python策略进程无法在5秒内正常终止，将强制终止。");
                    strategyProcess.destroyForcibly();
                }
                 if (pid != -1) {
                    log.info("Python策略进程 (PID: {}) 已停止。", pid);
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("等待Python策略进程终止时被中断", e);
                // 确保即使在中断时也强制关闭
                strategyProcess.destroyForcibly();
            }
            strategyProcess = null;
        }
    }
    
    /**
     * 断开与远程服务的连接
     */
    private void disconnectFromRemoteService() {
        // 停止健康检查
        stopHealthCheck();
        
        // 尝试发送关闭请求（这是可选的，因为策略服务可能被其他客户端使用）
        try {
            boolean shutdownResult = strategyServiceClient.shutdownService();
            if (shutdownResult) {
                log.info("已发送关闭请求到策略服务");
            } else {
                log.warn("发送关闭请求到策略服务失败");
            }
        } catch (Exception e) {
            log.warn("尝试关闭策略服务时发生错误", e);
        }
    }

    /**
     * 检查策略服务是否正在运行。
     *
     * @return 如果服务正在运行，返回true，否则返回false。
     */
    public boolean isRunning() {
        if (!serviceStarted) {
            return false;
        }
        
        String deployMode = appConfig.getPythonStrategy().getDeployMode();
        
        if ("local".equalsIgnoreCase(deployMode)) {
            return strategyProcess != null && strategyProcess.isAlive();
        } else if ("service".equalsIgnoreCase(deployMode)) {
            try {
                JsonNode statusNode = strategyServiceClient.getStatus();
                if (statusNode != null) {
                    String status = statusNode.path("status").asText();
                    return "running".equals(status);
                }
            } catch (Exception e) {
                log.error("检查策略服务状态时发生错误", e);
            }
            return false;
        } else {
            return false;
        }
    }
    
    /**
     * 发送市场数据到策略服务（仅在service模式下可用）
     * 
     * @param symbol 交易对符号
     * @param interval 间隔
     * @param data 市场数据
     * @return 如果发送成功则返回true，否则返回false
     */
    public boolean sendMarketData(String symbol, String interval, Object data) {
        if (!serviceStarted) {
            log.error("策略服务未启动，无法发送市场数据");
            return false;
        }
        
        String deployMode = appConfig.getPythonStrategy().getDeployMode();
        if (!"service".equalsIgnoreCase(deployMode)) {
            log.error("发送市场数据仅在service模式下可用");
            return false;
        }
        
        try {
            return strategyServiceClient.sendMarketData(symbol, interval, data);
        } catch (Exception e) {
            log.error("发送市场数据时发生错误", e);
            return false;
        }
    }
    
    /**
     * 获取交易信号预测（仅在service模式下可用）
     * 
     * @param symbol 交易对符号
     * @param interval 间隔
     * @param data 市场数据
     * @return 预测的交易信号，如果请求失败则返回null
     */
    public Optional<JsonNode> predictSignal(String symbol, String interval, Object data) {
        if (!serviceStarted) {
            log.error("策略服务未启动，无法获取交易信号预测");
            return Optional.empty();
        }
        
        String deployMode = appConfig.getPythonStrategy().getDeployMode();
        if (!"service".equalsIgnoreCase(deployMode)) {
            log.error("获取交易信号预测仅在service模式下可用");
            return Optional.empty();
        }
        
        try {
            JsonNode result = strategyServiceClient.predictSignal(symbol, interval, data);
            return Optional.ofNullable(result);
        } catch (Exception e) {
            log.error("获取交易信号预测时发生错误", e);
            return Optional.empty();
        }
    }
}