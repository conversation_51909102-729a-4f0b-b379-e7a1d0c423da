package com.crypto.trading.market.debug;

import com.crypto.trading.market.processor.TradeDataProcessor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;

/**
 * Kafka数据流测试器
 * 用于测试从WebSocket到Kafka再到数据库的完整数据流
 */
@SpringBootApplication
@ComponentScan(basePackages = "com.crypto.trading")
public class KafkaDataFlowTester implements CommandLineRunner {

    private static final Logger log = LoggerFactory.getLogger(KafkaDataFlowTester.class);

    @Autowired
    private TradeDataProcessor tradeDataProcessor;

    public static void main(String[] args) {
        SpringApplication.run(KafkaDataFlowTester.class, args);
    }

    @Override
    public void run(String... args) throws Exception {
        log.info("🚀 开始Kafka数据流测试...");

        // 测试嵌套格式的WebSocket消息
        String nestedMessage = """
            {
              "stream" : "ethusdt@aggTrade",
              "data" : {
                "e" : "aggTrade",
                "E" : 1750514562285,
                "a" : 111236374,
                "s" : "ETHUSDT",
                "p" : "2418.79",
                "q" : "0.133",
                "f" : 171164350,
                "l" : 171164350,
                "T" : 1750514562131,
                "m" : true
              }
            }
            """;

        log.info("📨 测试嵌套格式消息...");
        tradeDataProcessor.processTradeData(nestedMessage);

        // 等待一段时间让异步处理完成
        Thread.sleep(2000);

        // 测试直接格式的WebSocket消息
        String directMessage = """
            {
              "e" : "aggTrade",
              "E" : 1750514562285,
              "a" : 111236375,
              "s" : "BTCUSDT",
              "p" : "43250.50",
              "q" : "0.025",
              "f" : 171164351,
              "l" : 171164351,
              "T" : 1750514562131,
              "m" : false
            }
            """;

        log.info("📨 测试直接格式消息...");
        tradeDataProcessor.processTradeData(directMessage);

        // 等待一段时间让异步处理完成
        Thread.sleep(2000);

        // 测试多条消息
        log.info("📨 测试批量消息...");
        for (int i = 0; i < 5; i++) {
            String batchMessage = String.format("""
                {
                  "stream" : "btcusdt@aggTrade",
                  "data" : {
                    "e" : "aggTrade",
                    "E" : %d,
                    "a" : %d,
                    "s" : "BTCUSDT",
                    "p" : "%.2f",
                    "q" : "0.001",
                    "f" : %d,
                    "l" : %d,
                    "T" : %d,
                    "m" : %s
                  }
                }
                """, 
                System.currentTimeMillis(),
                111236376L + i,
                43250.50 + i * 0.1,
                171164352L + i,
                171164352L + i,
                System.currentTimeMillis() - 1000,
                i % 2 == 0 ? "true" : "false"
            );

            log.info("📨 发送批量消息 {}/5", i + 1);
            tradeDataProcessor.processTradeData(batchMessage);
            
            // 短暂延迟
            Thread.sleep(500);
        }

        log.info("⏳ 等待所有消息处理完成...");
        Thread.sleep(5000);

        log.info("✅ Kafka数据流测试完成！");
        log.info("📊 请检查以下内容：");
        log.info("   1. Kafka主题中是否有消息");
        log.info("   2. Kafka消费者是否正常消费");
        log.info("   3. InfluxDB中是否有数据");
        log.info("   4. MySQL中是否有数据");
        log.info("   5. 日志中是否有完整的数据流追踪");

        // 保持应用运行一段时间以观察Kafka消费
        log.info("🔄 应用将继续运行30秒以观察Kafka消费情况...");
        Thread.sleep(30000);

        log.info("🏁 测试结束，应用即将退出");
        System.exit(0);
    }
}
