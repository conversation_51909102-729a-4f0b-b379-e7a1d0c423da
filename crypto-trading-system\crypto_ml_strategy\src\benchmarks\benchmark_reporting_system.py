"""
Crypto ML Strategy - 基准测试报告系统

该模块实现了完整的基准测试报告生成功能，包括性能分析、
趋势分析、对比分析和多格式报告生成。

主要功能：
- BenchmarkReporter: 基准测试报告生成器
- PerformanceAnalyzer: 性能分析器
- TrendAnalyzer: 性能趋势分析器
- ComparisonAnalyzer: 性能对比分析器
- ReportGenerator: 多格式报告生成器

作者: Crypto ML Strategy Team
日期: 2024-01-20
版本: 1.0.0
"""

import json
import statistics
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Dict, List, Optional, Union
from dataclasses import dataclass, field
from benchmarks.benchmark_core_framework import BenchmarkResult, BenchmarkMetrics, BenchmarkStatus
from infrastructure.logging.logging_core_manager import get_logger
import matplotlib.pyplot as plt
import pandas as pd
import seaborn as sns


@dataclass
class PerformanceAnalysis:
    """性能分析结果"""
    test_name: str
    analysis_timestamp: datetime
    total_tests: int
    successful_tests: int
    failed_tests: int
    success_rate: float
    avg_execution_time_ms: float
    avg_throughput_ops_per_sec: float
    avg_latency_p95_ms: float
    avg_memory_usage_mb: float
    avg_cpu_usage_percent: float
    performance_grade: str  # A, B, C, D, F
    bottlenecks: List[str] = field(default_factory=list)
    recommendations: List[str] = field(default_factory=list)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "test_name": self.test_name,
            "analysis_timestamp": self.analysis_timestamp.isoformat(),
            "total_tests": self.total_tests,
            "successful_tests": self.successful_tests,
            "failed_tests": self.failed_tests,
            "success_rate": self.success_rate,
            "avg_execution_time_ms": self.avg_execution_time_ms,
            "avg_throughput_ops_per_sec": self.avg_throughput_ops_per_sec,
            "avg_latency_p95_ms": self.avg_latency_p95_ms,
            "avg_memory_usage_mb": self.avg_memory_usage_mb,
            "avg_cpu_usage_percent": self.avg_cpu_usage_percent,
            "performance_grade": self.performance_grade,
            "bottlenecks": self.bottlenecks,
            "recommendations": self.recommendations
        }


@dataclass
class TrendAnalysis:
    """趋势分析结果"""
    metric_name: str
    time_period: str
    trend_direction: str  # improving, degrading, stable
    trend_strength: float  # 0-1
    change_percentage: float
    data_points: int
    correlation_coefficient: float
    statistical_significance: bool
    forecast_next_period: Optional[float] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "metric_name": self.metric_name,
            "time_period": self.time_period,
            "trend_direction": self.trend_direction,
            "trend_strength": self.trend_strength,
            "change_percentage": self.change_percentage,
            "data_points": self.data_points,
            "correlation_coefficient": self.correlation_coefficient,
            "statistical_significance": self.statistical_significance,
            "forecast_next_period": self.forecast_next_period
        }


@dataclass
class ComparisonResult:
    """对比分析结果"""
    baseline_name: str
    comparison_name: str
    metric_name: str
    baseline_value: float
    comparison_value: float
    difference: float
    percentage_change: float
    improvement: bool
    significance_level: str  # high, medium, low, none
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "baseline_name": self.baseline_name,
            "comparison_name": self.comparison_name,
            "metric_name": self.metric_name,
            "baseline_value": self.baseline_value,
            "comparison_value": self.comparison_value,
            "difference": self.difference,
            "percentage_change": self.percentage_change,
            "improvement": self.improvement,
            "significance_level": self.significance_level
        }


class PerformanceAnalyzer:
    """性能分析器"""
    
    def __init__(self):
        self.logger = get_logger(f"{__name__}.PerformanceAnalyzer")
        
        # 性能等级阈值
        self.grade_thresholds = {
            "execution_time_ms": {"A": 50, "B": 100, "C": 200, "D": 500},
            "throughput_ops_per_sec": {"A": 1000, "B": 500, "C": 100, "D": 50},
            "latency_p95_ms": {"A": 10, "B": 50, "C": 100, "D": 200},
            "memory_usage_mb": {"A": 100, "B": 200, "C": 500, "D": 1000},
            "cpu_usage_percent": {"A": 20, "B": 40, "C": 60, "D": 80}
        }
    
    def analyze_results(self, results: List[BenchmarkResult]) -> PerformanceAnalysis:
        """分析基准测试结果"""
        if not results:
            raise ValueError("No results to analyze")
        
        # 过滤成功的结果
        successful_results = [r for r in results if r.status == BenchmarkStatus.COMPLETED and r.metrics]
        failed_results = [r for r in results if r.status == BenchmarkStatus.FAILED]
        
        if not successful_results:
            raise ValueError("No successful results to analyze")
        
        # 计算基础统计
        total_tests = len(results)
        successful_tests = len(successful_results)
        failed_tests = len(failed_results)
        success_rate = successful_tests / total_tests
        
        # 提取指标
        metrics = [r.metrics for r in successful_results]
        
        avg_execution_time = statistics.mean([m.execution_time_ms for m in metrics])
        avg_throughput = statistics.mean([m.throughput_ops_per_sec for m in metrics])
        avg_latency_p95 = statistics.mean([m.latency_p95_ms for m in metrics])
        avg_memory = statistics.mean([m.memory_usage_mb for m in metrics])
        avg_cpu = statistics.mean([m.cpu_usage_percent for m in metrics])
        
        # 计算性能等级
        performance_grade = self._calculate_performance_grade({
            "execution_time_ms": avg_execution_time,
            "throughput_ops_per_sec": avg_throughput,
            "latency_p95_ms": avg_latency_p95,
            "memory_usage_mb": avg_memory,
            "cpu_usage_percent": avg_cpu
        })
        
        # 识别瓶颈
        bottlenecks = self._identify_bottlenecks({
            "execution_time_ms": avg_execution_time,
            "throughput_ops_per_sec": avg_throughput,
            "latency_p95_ms": avg_latency_p95,
            "memory_usage_mb": avg_memory,
            "cpu_usage_percent": avg_cpu
        })
        
        # 生成建议
        recommendations = self._generate_recommendations(bottlenecks, {
            "execution_time_ms": avg_execution_time,
            "throughput_ops_per_sec": avg_throughput,
            "latency_p95_ms": avg_latency_p95,
            "memory_usage_mb": avg_memory,
            "cpu_usage_percent": avg_cpu
        })
        
        return PerformanceAnalysis(
            test_name=results[0].test_name,
            analysis_timestamp=datetime.now(),
            total_tests=total_tests,
            successful_tests=successful_tests,
            failed_tests=failed_tests,
            success_rate=success_rate,
            avg_execution_time_ms=avg_execution_time,
            avg_throughput_ops_per_sec=avg_throughput,
            avg_latency_p95_ms=avg_latency_p95,
            avg_memory_usage_mb=avg_memory,
            avg_cpu_usage_percent=avg_cpu,
            performance_grade=performance_grade,
            bottlenecks=bottlenecks,
            recommendations=recommendations
        )
    
    def _calculate_performance_grade(self, metrics: Dict[str, float]) -> str:
        """计算性能等级"""
        grades = []
        
        for metric_name, value in metrics.items():
            if metric_name in self.grade_thresholds:
                thresholds = self.grade_thresholds[metric_name]
                
                if metric_name == "throughput_ops_per_sec":
                    # 吞吐量越高越好
                    if value >= thresholds["A"]:
                        grades.append("A")
                    elif value >= thresholds["B"]:
                        grades.append("B")
                    elif value >= thresholds["C"]:
                        grades.append("C")
                    elif value >= thresholds["D"]:
                        grades.append("D")
                    else:
                        grades.append("F")
                else:
                    # 其他指标越低越好
                    if value <= thresholds["A"]:
                        grades.append("A")
                    elif value <= thresholds["B"]:
                        grades.append("B")
                    elif value <= thresholds["C"]:
                        grades.append("C")
                    elif value <= thresholds["D"]:
                        grades.append("D")
                    else:
                        grades.append("F")
        
        # 计算总体等级（取最差的等级）
        grade_order = ["A", "B", "C", "D", "F"]
        worst_grade_index = max([grade_order.index(grade) for grade in grades])
        
        return grade_order[worst_grade_index]
    
    def _identify_bottlenecks(self, metrics: Dict[str, float]) -> List[str]:
        """识别性能瓶颈"""
        bottlenecks = []
        
        # 检查各项指标是否超过阈值
        if metrics["execution_time_ms"] > self.grade_thresholds["execution_time_ms"]["C"]:
            bottlenecks.append("High execution time")
        
        if metrics["throughput_ops_per_sec"] < self.grade_thresholds["throughput_ops_per_sec"]["C"]:
            bottlenecks.append("Low throughput")
        
        if metrics["latency_p95_ms"] > self.grade_thresholds["latency_p95_ms"]["C"]:
            bottlenecks.append("High latency")
        
        if metrics["memory_usage_mb"] > self.grade_thresholds["memory_usage_mb"]["C"]:
            bottlenecks.append("High memory usage")
        
        if metrics["cpu_usage_percent"] > self.grade_thresholds["cpu_usage_percent"]["C"]:
            bottlenecks.append("High CPU usage")
        
        return bottlenecks
    
    def _generate_recommendations(self, bottlenecks: List[str], metrics: Dict[str, float]) -> List[str]:
        """生成优化建议"""
        recommendations = []
        
        if "High execution time" in bottlenecks:
            recommendations.append("Consider optimizing algorithm complexity or using parallel processing")
        
        if "Low throughput" in bottlenecks:
            recommendations.append("Implement connection pooling or increase worker threads")
        
        if "High latency" in bottlenecks:
            recommendations.append("Optimize network communication or implement caching")
        
        if "High memory usage" in bottlenecks:
            recommendations.append("Implement memory pooling or optimize data structures")
        
        if "High CPU usage" in bottlenecks:
            recommendations.append("Profile CPU-intensive operations and optimize hot paths")
        
        if not bottlenecks:
            recommendations.append("Performance is within acceptable ranges")
        
        return recommendations


class TrendAnalyzer:
    """性能趋势分析器"""
    
    def __init__(self):
        self.logger = get_logger(f"{__name__}.TrendAnalyzer")
    
    def analyze_trend(self, historical_data: List[Dict[str, Any]], metric_name: str, 
                     time_period: str = "7d") -> TrendAnalysis:
        """分析性能趋势"""
        if len(historical_data) < 2:
            raise ValueError("Need at least 2 data points for trend analysis")
        
        # 提取指标值和时间戳
        values = []
        timestamps = []
        
        for data_point in historical_data:
            if metric_name in data_point:
                values.append(data_point[metric_name])
                timestamps.append(data_point.get("timestamp", datetime.now()))
        
        if len(values) < 2:
            raise ValueError(f"Insufficient data for metric {metric_name}")
        
        # 计算趋势
        trend_direction, trend_strength, correlation = self._calculate_trend(values)
        
        # 计算变化百分比
        change_percentage = ((values[-1] - values[0]) / values[0]) * 100 if values[0] != 0 else 0
        
        # 统计显著性检验
        statistical_significance = abs(correlation) > 0.5 and len(values) >= 10
        
        # 预测下一期值
        forecast = self._forecast_next_value(values) if len(values) >= 3 else None
        
        return TrendAnalysis(
            metric_name=metric_name,
            time_period=time_period,
            trend_direction=trend_direction,
            trend_strength=trend_strength,
            change_percentage=change_percentage,
            data_points=len(values),
            correlation_coefficient=correlation,
            statistical_significance=statistical_significance,
            forecast_next_period=forecast
        )
    
    def _calculate_trend(self, values: List[float]) -> tuple[str, float, float]:
        """计算趋势方向和强度"""
        if len(values) < 2:
            return "stable", 0.0, 0.0
        
        # 计算线性回归
        n = len(values)
        x = list(range(n))
        
        # 计算相关系数
        mean_x = statistics.mean(x)
        mean_y = statistics.mean(values)
        
        numerator = sum((x[i] - mean_x) * (values[i] - mean_y) for i in range(n))
        denominator_x = sum((x[i] - mean_x) ** 2 for i in range(n))
        denominator_y = sum((values[i] - mean_y) ** 2 for i in range(n))
        
        if denominator_x == 0 or denominator_y == 0:
            correlation = 0.0
        else:
            correlation = numerator / (denominator_x * denominator_y) ** 0.5
        
        # 确定趋势方向
        if correlation > 0.1:
            trend_direction = "improving" if self._is_improvement_metric(values) else "degrading"
        elif correlation < -0.1:
            trend_direction = "degrading" if self._is_improvement_metric(values) else "improving"
        else:
            trend_direction = "stable"
        
        # 趋势强度
        trend_strength = abs(correlation)
        
        return trend_direction, trend_strength, correlation
    
    def _is_improvement_metric(self, values: List[float]) -> bool:
        """判断指标是否为改善型（值越大越好）"""
        # 这里简化处理，实际应该根据具体指标类型判断
        # 例如：throughput是改善型，latency是恶化型
        return False  # 默认认为值越小越好
    
    def _forecast_next_value(self, values: List[float]) -> float:
        """预测下一个值"""
        if len(values) < 3:
            return values[-1]
        
        # 简单的线性预测
        recent_values = values[-3:]
        trend = (recent_values[-1] - recent_values[0]) / 2
        
        return recent_values[-1] + trend


class ComparisonAnalyzer:
    """性能对比分析器"""
    
    def __init__(self):
        self.logger = get_logger(f"{__name__}.ComparisonAnalyzer")
        
        # 显著性阈值
        self.significance_thresholds = {
            "high": 20.0,    # 20%以上变化
            "medium": 10.0,  # 10-20%变化
            "low": 5.0       # 5-10%变化
        }
    
    def compare_results(self, baseline_results: List[BenchmarkResult], 
                       comparison_results: List[BenchmarkResult]) -> List[ComparisonResult]:
        """对比两组基准测试结果"""
        if not baseline_results or not comparison_results:
            raise ValueError("Both baseline and comparison results are required")
        
        # 计算平均指标
        baseline_metrics = self._calculate_average_metrics(baseline_results)
        comparison_metrics = self._calculate_average_metrics(comparison_results)
        
        # 生成对比结果
        comparison_results_list = []
        
        for metric_name in baseline_metrics.keys():
            if metric_name in comparison_metrics:
                baseline_value = baseline_metrics[metric_name]
                comparison_value = comparison_metrics[metric_name]
                
                difference = comparison_value - baseline_value
                percentage_change = (difference / baseline_value * 100) if baseline_value != 0 else 0
                
                # 判断是否为改善
                improvement = self._is_improvement(metric_name, difference)
                
                # 判断显著性
                significance_level = self._calculate_significance(abs(percentage_change))
                
                comparison_result = ComparisonResult(
                    baseline_name="baseline",
                    comparison_name="comparison",
                    metric_name=metric_name,
                    baseline_value=baseline_value,
                    comparison_value=comparison_value,
                    difference=difference,
                    percentage_change=percentage_change,
                    improvement=improvement,
                    significance_level=significance_level
                )
                
                comparison_results_list.append(comparison_result)
        
        return comparison_results_list
    
    def _calculate_average_metrics(self, results: List[BenchmarkResult]) -> Dict[str, float]:
        """计算平均指标"""
        successful_results = [r for r in results if r.status == BenchmarkStatus.COMPLETED and r.metrics]
        
        if not successful_results:
            return {}
        
        metrics = [r.metrics for r in successful_results]
        
        return {
            "execution_time_ms": statistics.mean([m.execution_time_ms for m in metrics]),
            "throughput_ops_per_sec": statistics.mean([m.throughput_ops_per_sec for m in metrics]),
            "latency_p50_ms": statistics.mean([m.latency_p50_ms for m in metrics]),
            "latency_p95_ms": statistics.mean([m.latency_p95_ms for m in metrics]),
            "latency_p99_ms": statistics.mean([m.latency_p99_ms for m in metrics]),
            "memory_usage_mb": statistics.mean([m.memory_usage_mb for m in metrics]),
            "cpu_usage_percent": statistics.mean([m.cpu_usage_percent for m in metrics]),
            "error_rate": statistics.mean([m.error_rate for m in metrics])
        }
    
    def _is_improvement(self, metric_name: str, difference: float) -> bool:
        """判断变化是否为改善"""
        # 吞吐量增加是改善
        if metric_name == "throughput_ops_per_sec":
            return difference > 0
        
        # 其他指标减少是改善
        return difference < 0
    
    def _calculate_significance(self, percentage_change: float) -> str:
        """计算变化显著性"""
        if percentage_change >= self.significance_thresholds["high"]:
            return "high"
        elif percentage_change >= self.significance_thresholds["medium"]:
            return "medium"
        elif percentage_change >= self.significance_thresholds["low"]:
            return "low"
        else:
            return "none"


class ReportGenerator:
    """多格式报告生成器"""
    
    def __init__(self, output_dir: str = "logs/benchmark_reports"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        self.logger = get_logger(f"{__name__}.ReportGenerator")
    
    def generate_json_report(self, analysis: PerformanceAnalysis, 
                           trends: List[TrendAnalysis] = None,
                           comparisons: List[ComparisonResult] = None) -> Path:
        """生成JSON格式报告"""
        report_data = {
            "report_metadata": {
                "generated_at": datetime.now().isoformat(),
                "report_type": "benchmark_performance_analysis",
                "version": "1.0"
            },
            "performance_analysis": analysis.to_dict(),
            "trend_analysis": [trend.to_dict() for trend in (trends or [])],
            "comparison_analysis": [comp.to_dict() for comp in (comparisons or [])]
        }
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"benchmark_report_{timestamp}.json"
        file_path = self.output_dir / filename
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"JSON report generated: {file_path}")
        return file_path
    
    def generate_html_report(self, analysis: PerformanceAnalysis,
                           trends: List[TrendAnalysis] = None,
                           comparisons: List[ComparisonResult] = None) -> Path:
        """生成HTML格式报告"""
        html_content = self._create_html_content(analysis, trends or [], comparisons or [])
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"benchmark_report_{timestamp}.html"
        file_path = self.output_dir / filename
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        self.logger.info(f"HTML report generated: {file_path}")
        return file_path
    
    def _create_html_content(self, analysis: PerformanceAnalysis,
                           trends: List[TrendAnalysis],
                           comparisons: List[ComparisonResult]) -> str:
        """创建HTML报告内容"""
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Benchmark Performance Report</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .header {{ background-color: #f0f0f0; padding: 20px; border-radius: 5px; }}
                .section {{ margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }}
                .metric {{ display: inline-block; margin: 10px; padding: 10px; background-color: #f9f9f9; border-radius: 3px; }}
                .grade-A {{ color: green; font-weight: bold; }}
                .grade-B {{ color: blue; font-weight: bold; }}
                .grade-C {{ color: orange; font-weight: bold; }}
                .grade-D {{ color: red; font-weight: bold; }}
                .grade-F {{ color: darkred; font-weight: bold; }}
                table {{ border-collapse: collapse; width: 100%; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                th {{ background-color: #f2f2f2; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>Benchmark Performance Report</h1>
                <p>Test: {analysis.test_name}</p>
                <p>Generated: {analysis.analysis_timestamp.strftime('%Y-%m-%d %H:%M:%S')}</p>
                <p>Performance Grade: <span class="grade-{analysis.performance_grade}">{analysis.performance_grade}</span></p>
            </div>
            
            <div class="section">
                <h2>Performance Summary</h2>
                <div class="metric">Total Tests: {analysis.total_tests}</div>
                <div class="metric">Success Rate: {analysis.success_rate:.1%}</div>
                <div class="metric">Avg Execution Time: {analysis.avg_execution_time_ms:.2f}ms</div>
                <div class="metric">Avg Throughput: {analysis.avg_throughput_ops_per_sec:.2f} ops/sec</div>
                <div class="metric">Avg Latency P95: {analysis.avg_latency_p95_ms:.2f}ms</div>
                <div class="metric">Avg Memory Usage: {analysis.avg_memory_usage_mb:.2f}MB</div>
                <div class="metric">Avg CPU Usage: {analysis.avg_cpu_usage_percent:.1f}%</div>
            </div>
        """
        
        if analysis.bottlenecks:
            html += f"""
            <div class="section">
                <h2>Identified Bottlenecks</h2>
                <ul>
                    {''.join(f'<li>{bottleneck}</li>' for bottleneck in analysis.bottlenecks)}
                </ul>
            </div>
            """
        
        if analysis.recommendations:
            html += f"""
            <div class="section">
                <h2>Recommendations</h2>
                <ul>
                    {''.join(f'<li>{rec}</li>' for rec in analysis.recommendations)}
                </ul>
            </div>
            """
        
        html += "</body></html>"
        return html


class BenchmarkReporter:
    """基准测试报告生成器主管理器"""
    
    def __init__(self, output_dir: str = "logs/benchmark_reports"):
        self.output_dir = output_dir
        self.logger = get_logger(f"{__name__}.BenchmarkReporter")
        
        # 初始化分析器
        self.performance_analyzer = PerformanceAnalyzer()
        self.trend_analyzer = TrendAnalyzer()
        self.comparison_analyzer = ComparisonAnalyzer()
        self.report_generator = ReportGenerator(output_dir)
    
    def generate_comprehensive_report(self, results: List[BenchmarkResult],
                                    historical_data: List[Dict[str, Any]] = None,
                                    baseline_results: List[BenchmarkResult] = None,
                                    formats: List[str] = None) -> Dict[str, Path]:
        """生成综合报告"""
        formats = formats or ["json", "html"]
        
        try:
            # 性能分析
            analysis = self.performance_analyzer.analyze_results(results)
            
            # 趋势分析
            trends = []
            if historical_data:
                for metric in ["execution_time_ms", "throughput_ops_per_sec", "latency_p95_ms"]:
                    try:
                        trend = self.trend_analyzer.analyze_trend(historical_data, metric)
                        trends.append(trend)
                    except Exception as e:
                        self.logger.warning(f"Failed to analyze trend for {metric}: {e}")
            
            # 对比分析
            comparisons = []
            if baseline_results:
                try:
                    comparisons = self.comparison_analyzer.compare_results(baseline_results, results)
                except Exception as e:
                    self.logger.warning(f"Failed to perform comparison analysis: {e}")
            
            # 生成报告
            generated_reports = {}
            
            if "json" in formats:
                json_path = self.report_generator.generate_json_report(analysis, trends, comparisons)
                generated_reports["json"] = json_path
            
            if "html" in formats:
                html_path = self.report_generator.generate_html_report(analysis, trends, comparisons)
                generated_reports["html"] = html_path
            
            self.logger.info(f"Generated {len(generated_reports)} report formats")
            return generated_reports
            
        except Exception as e:
            self.logger.error(f"Failed to generate comprehensive report: {e}")
            raise


# 全局报告生成器实例
_global_benchmark_reporter: Optional[BenchmarkReporter] = None


def get_global_benchmark_reporter() -> BenchmarkReporter:
    """获取全局基准测试报告生成器实例"""
    global _global_benchmark_reporter
    
    if _global_benchmark_reporter is None:
        _global_benchmark_reporter = BenchmarkReporter()
    
    return _global_benchmark_reporter


# 模块级别的日志器
module_logger = get_logger(__name__)