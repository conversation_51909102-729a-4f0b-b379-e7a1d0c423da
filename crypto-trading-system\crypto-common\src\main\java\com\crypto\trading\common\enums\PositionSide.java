package com.crypto.trading.common.enums;

/**
 * 仓位方向枚举
 * <p>
 * 表示持仓的方向，如多仓或空仓
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public enum PositionSide {

    /**
     * 多仓
     */
    LONG("LONG", "多仓"),

    /**
     * 空仓
     */
    SHORT("SHORT", "空仓"),

    /**
     * 双向（多空双持）
     */
    BOTH("BOTH", "双向");

    /**
     * 代码
     */
    private final String code;

    /**
     * 描述
     */
    private final String desc;

    /**
     * 构造函数
     *
     * @param code 代码
     * @param desc 描述
     */
    PositionSide(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 获取代码
     *
     * @return 代码
     */
    public String getCode() {
        return code;
    }

    /**
     * 获取描述
     *
     * @return 描述
     */
    public String getDesc() {
        return desc;
    }

    /**
     * 根据代码获取枚举值
     *
     * @param code 代码
     * @return 枚举值
     */
    public static PositionSide fromCode(String code) {
        for (PositionSide side : PositionSide.values()) {
            if (side.getCode().equals(code)) {
                return side;
            }
        }
        throw new IllegalArgumentException("未知的仓位方向代码: " + code);
    }

    /**
     * 获取相反的仓位方向
     *
     * @return 相反的仓位方向
     */
    public PositionSide opposite() {
        if (this == LONG) {
            return SHORT;
        } else if (this == SHORT) {
            return LONG;
        }
        return BOTH;
    }
}