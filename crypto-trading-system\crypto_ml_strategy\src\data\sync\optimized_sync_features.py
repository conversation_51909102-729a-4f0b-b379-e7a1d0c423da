#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
优化同步器特征融合模块

包含特征融合、性能监控和工具方法的实现。
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any, Union
from datetime import datetime
import time
from .optimized_timeframe_sync import TimeframeType, PerformanceMetrics


class OptimizedSyncFeatures:
    """优化同步器特征融合类"""
    
    @staticmethod
    def fuse_features(sync_instance, synchronized_df: pd.DataFrame, 
                     feature_columns: Optional[List[str]] = None) -> pd.DataFrame:
        """
        融合多时间框架特征
        
        Args:
            sync_instance: OptimizedTimeframeSync实例
            synchronized_df: 同步后的数据
            feature_columns: 要融合的特征列
            
        Returns:
            融合后的DataFrame
        """
        try:
            if synchronized_df is None or synchronized_df.empty:
                return synchronized_df
            
            result_df = synchronized_df.copy()
            
            # 默认特征列
            if feature_columns is None:
                feature_columns = ['open', 'high', 'low', 'close', 'volume']
            
            # 加权平均融合
            for feature in feature_columns:
                OptimizedSyncFeatures._weighted_average_fusion(
                    sync_instance, result_df, feature
                )
            
            # 动态权重融合（基于波动率）
            for feature in ['close', 'volume']:
                OptimizedSyncFeatures._dynamic_weight_fusion(
                    sync_instance, result_df, feature
                )
            
            # 趋势一致性特征
            OptimizedSyncFeatures._add_trend_consistency_features(result_df)
            
            # 波动率特征
            OptimizedSyncFeatures._add_volatility_features(result_df)
            
            return result_df
            
        except Exception as e:
            sync_instance.logger.error(f"特征融合失败: {e}")
            return synchronized_df
    
    @staticmethod
    def _weighted_average_fusion(sync_instance, df: pd.DataFrame, feature: str):
        """加权平均特征融合"""
        try:
            # 找到所有时间框架的该特征列
            feature_cols = [col for col in df.columns if col.startswith(f'{feature}_')]
            
            if len(feature_cols) > 1:
                weighted_sum = 0
                total_weight = 0
                
                for col in feature_cols:
                    # 提取时间框架名称
                    tf_name = col.split('_')[-1]
                    try:
                        tf_type = TimeframeType(tf_name)
                        if tf_type in sync_instance.enabled_timeframes:
                            weight = sync_instance.timeframe_specs[tf_type].weight
                            weighted_sum += df[col] * weight
                            total_weight += weight
                    except ValueError:
                        continue
                
                if total_weight > 0:
                    df[f'{feature}_weighted'] = weighted_sum / total_weight
                    
        except Exception as e:
            sync_instance.logger.error(f"加权平均融合失败: {e}")
    
    @staticmethod
    def _dynamic_weight_fusion(sync_instance, df: pd.DataFrame, feature: str):
        """动态权重特征融合（基于波动率）"""
        try:
            feature_cols = [col for col in df.columns if col.startswith(f'{feature}_')]
            
            if len(feature_cols) > 1:
                # 计算每个时间框架的波动率
                volatilities = {}
                for col in feature_cols:
                    pct_change = df[col].pct_change().fillna(0)
                    volatility = pct_change.rolling(window=20, min_periods=1).std()
                    volatilities[col] = volatility
                
                # 动态权重：波动率越低权重越高
                dynamic_weighted = pd.Series(index=df.index, dtype=float)
                
                for idx in df.index:
                    weights = {}
                    total_weight = 0
                    
                    for col in feature_cols:
                        vol = volatilities[col].loc[idx] if idx in volatilities[col].index else 0.1
                        # 反向权重：波动率越低权重越高
                        weight = 1.0 / (vol + 0.001)  # 避免除零
                        weights[col] = weight
                        total_weight += weight
                    
                    if total_weight > 0:
                        weighted_value = sum(df.loc[idx, col] * weights[col] / total_weight 
                                           for col in feature_cols)
                        dynamic_weighted.loc[idx] = weighted_value
                
                df[f'{feature}_dynamic'] = dynamic_weighted
                
        except Exception as e:
            sync_instance.logger.error(f"动态权重融合失败: {e}")
    
    @staticmethod
    def _add_trend_consistency_features(df: pd.DataFrame):
        """添加趋势一致性特征"""
        try:
            close_cols = [col for col in df.columns if col.startswith('close_')]
            
            if len(close_cols) > 1:
                # 计算各时间框架的趋势方向
                trends = pd.DataFrame(index=df.index)
                
                for col in close_cols:
                    # 短期趋势（5期移动平均）
                    ma5 = df[col].rolling(window=5, min_periods=1).mean()
                    trend = (df[col] > ma5).astype(int)
                    trends[col] = trend
                
                # 趋势一致性分数
                df['trend_consistency'] = trends.mean(axis=1)
                
                # 趋势强度
                trend_strength = trends.std(axis=1)
                df['trend_strength'] = 1.0 - trend_strength  # 标准差越小，一致性越强
                
        except Exception as e:
            logging.getLogger('cryptoMlStrategy.data.optimized_sync').error(f"趋势一致性特征失败: {e}")
    
    @staticmethod
    def _add_volatility_features(df: pd.DataFrame):
        """添加波动率特征"""
        try:
            close_cols = [col for col in df.columns if col.startswith('close_')]
            
            if close_cols:
                # 多时间框架波动率
                volatilities = []
                
                for col in close_cols:
                    pct_change = df[col].pct_change().fillna(0)
                    vol = pct_change.rolling(window=20, min_periods=1).std()
                    volatilities.append(vol)
                
                if volatilities:
                    # 平均波动率
                    df['avg_volatility'] = pd.concat(volatilities, axis=1).mean(axis=1)
                    
                    # 波动率差异
                    vol_df = pd.concat(volatilities, axis=1)
                    df['volatility_spread'] = vol_df.max(axis=1) - vol_df.min(axis=1)
                    
        except Exception as e:
            logging.getLogger('cryptoMlStrategy.data.optimized_sync').error(f"波动率特征失败: {e}")
    
    @staticmethod
    def get_performance_metrics(sync_instance) -> Dict[str, Any]:
        """获取性能指标"""
        try:
            # 计算吞吐量
            elapsed_time = time.time() - sync_instance._stats['start_time']
            throughput = sync_instance._stats['total_syncs'] / max(1, elapsed_time)
            sync_instance.metrics.throughput_records_per_sec = throughput
            
            # 计算缓存命中率
            if sync_instance._stats['total_syncs'] > 0:
                hit_ratio = sync_instance._stats['cache_hits'] / sync_instance._stats['total_syncs']
                sync_instance.metrics.cache_hit_ratio = hit_ratio
            
            return {
                'performance_metrics': {
                    'sync_latency_ms': sync_instance.metrics.sync_latency_ms,
                    'memory_usage_mb': sync_instance.metrics.memory_usage_mb,
                    'cache_hit_ratio': sync_instance.metrics.cache_hit_ratio,
                    'data_quality_score': sync_instance.metrics.data_quality_score,
                    'throughput_records_per_sec': sync_instance.metrics.throughput_records_per_sec,
                    'last_update': sync_instance.metrics.last_update.isoformat()
                },
                'statistics': sync_instance._stats.copy(),
                'enabled_timeframes': [tf.value for tf in sync_instance.enabled_timeframes.keys()],
                'config': {
                    'strategy': sync_instance.sync_config.strategy.value,
                    'tolerance_ms': sync_instance.sync_config.tolerance_ms,
                    'max_gap_ms': sync_instance.sync_config.max_gap_ms,
                    'memory_limit_mb': sync_instance.sync_config.memory_limit_mb,
                    'cache_ttl_seconds': sync_instance.sync_config.cache_ttl_seconds
                }
            }
            
        except Exception as e:
            sync_instance.logger.error(f"性能指标获取失败: {e}")
            return {}
    
    @staticmethod
    def validate_sync_quality(sync_instance, synchronized_df: pd.DataFrame, 
                            symbol: str) -> Dict[str, Any]:
        """验证同步质量"""
        try:
            if synchronized_df is None or synchronized_df.empty:
                return {
                    'is_valid': False,
                    'quality_score': 0.0,
                    'issues': ['同步数据为空'],
                    'recommendations': ['检查数据源和时间框架配置']
                }
            
            issues = []
            recommendations = []
            
            # 检查数据完整性
            missing_ratio = synchronized_df.isnull().sum().sum() / (
                len(synchronized_df) * len(synchronized_df.columns)
            )
            
            if missing_ratio > 0.1:
                issues.append(f'缺失值比例过高: {missing_ratio:.2%}')
                recommendations.append('调整同步策略或增加数据源')
            
            # 检查时间连续性
            time_diffs = synchronized_df.index.to_series().diff().dt.total_seconds()
            max_gap = time_diffs.max() if len(time_diffs) > 1 else 0
            
            if max_gap > sync_instance.sync_config.max_gap_ms / 1000:
                issues.append(f'时间间隙过大: {max_gap:.0f}秒')
                recommendations.append('检查数据源时间同步')
            
            # 检查数据范围合理性
            numeric_cols = synchronized_df.select_dtypes(include=[np.number]).columns
            for col in numeric_cols:
                if 'close' in col.lower():
                    if (synchronized_df[col] <= 0).any():
                        issues.append(f'发现非正价格数据: {col}')
                        recommendations.append('检查数据源质量')
            
            # 计算综合质量分数
            quality_score = 1.0 - missing_ratio
            if max_gap > 300:  # 5分钟
                quality_score *= 0.8
            if len(issues) > 0:
                quality_score *= 0.9
            
            return {
                'is_valid': len(issues) == 0,
                'quality_score': quality_score,
                'issues': issues,
                'recommendations': recommendations,
                'statistics': {
                    'total_records': len(synchronized_df),
                    'missing_ratio': missing_ratio,
                    'max_time_gap_seconds': max_gap,
                    'timeframe_coverage': len([col for col in synchronized_df.columns 
                                             if any(tf.value in col for tf in sync_instance.enabled_timeframes.keys())])
                }
            }
            
        except Exception as e:
            sync_instance.logger.error(f"同步质量验证失败: {e}")
            return {
                'is_valid': False,
                'quality_score': 0.0,
                'issues': [f'验证过程出错: {str(e)}'],
                'recommendations': ['检查系统配置和数据格式']
            }
    
    @staticmethod
    def cleanup_resources(sync_instance):
        """清理资源"""
        try:
            sync_instance._memory_monitor_active = False
            
            with sync_instance._lock:
                sync_instance._data_store.clear()
                sync_instance._sync_cache.clear()
            
            if hasattr(sync_instance, '_executor'):
                sync_instance._executor.shutdown(wait=True)
            
            sync_instance.logger.info("资源清理完成")
            
        except Exception as e:
            sync_instance.logger.error(f"资源清理失败: {e}")
    
    @staticmethod
    def export_sync_data(sync_instance, symbol: str, timeframe: Optional[str] = None,
                        format: str = 'csv') -> Optional[str]:
        """导出同步数据"""
        try:
            from pathlib import Path
            
            # 获取同步数据
            if timeframe:
                data = OptimizedSyncCore.sync_timeframes(sync_instance, symbol, timeframe)
            else:
                data = OptimizedSyncCore.sync_timeframes(sync_instance, symbol)
            
            if data is None or data.empty:
                sync_instance.logger.warning(f"没有数据可导出: {symbol}")
                return None
            
            # 创建导出目录
            export_dir = Path('exports/optimized_sync')
            export_dir.mkdir(parents=True, exist_ok=True)
            
            # 生成文件名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            tf_suffix = f"_{timeframe}" if timeframe else "_all"
            filename = f"{symbol}{tf_suffix}_{timestamp}.{format}"
            filepath = export_dir / filename
            
            # 导出数据
            if format == 'csv':
                data.to_csv(filepath)
            elif format == 'json':
                data.to_json(filepath, orient='records', date_format='iso')
            elif format == 'parquet':
                data.to_parquet(filepath)
            else:
                raise ValueError(f"不支持的导出格式: {format}")
            
            sync_instance.logger.info(f"数据已导出到: {filepath}")
            return str(filepath)
            
        except Exception as e:
            sync_instance.logger.error(f"数据导出失败: {e}")
            return None


# 导入核心方法以便使用
try:
    from .optimized_sync_core import OptimizedSyncCore
except ImportError:
    # 如果导入失败，创建一个占位符类
    class OptimizedSyncCore:
        @staticmethod
        def sync_timeframes(*args, **kwargs):
            return None