package com.crypto.trading.bootstrap.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import lombok.Data;

/**
 * 应用全局配置类
 * 将配置文件中的app前缀的配置项映射到此类
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "app")
public class AppConfig {

    /**
     * 应用名称
     */
    private String name;
    
    /**
     * 应用版本
     */
    private String version;
    
    /**
     * 是否启用调试模式
     */
    private boolean debug;
    
    /**
     * 线程池配置
     */
    private ThreadPoolConfig threadPool = new ThreadPoolConfig();
    
    /**
     * Python策略配置
     */
    private PythonStrategyConfig pythonStrategy = new PythonStrategyConfig();
    
    /**
     * 线程池配置内部类
     */
    @Data
    public static class ThreadPoolConfig {
        /**
         * 最大线程数
         */
        private int maxSize = 100;
        
        /**
         * 核心线程数
         */
        private int coreSize = 10;
        
        /**
         * 队列容量
         */
        private int queueCapacity = 1000;
        
        /**
         * 线程保活时间（秒）
         */
        private int keepAliveSeconds = 60;
    }
    
    /**
     * Python策略配置内部类
     */
    @Data
    public static class PythonStrategyConfig {
        /**
         * 是否启用Python策略
         */
        private boolean enabled = true;
        
        /**
         * 部署模式：local（本地进程）或service（远程服务）
         */
        private String deployMode = "service";
        
        /**
         * 仅在local模式下使用：Python解释器路径
         */
        private String pythonPath = "python";
        
        /**
         * 仅在local模式下使用：Python策略模块路径, 相对于workingDir
         */
        private String strategyPath = "src/main.py";
        
        /**
         * 仅在local模式下使用：Python策略工作目录
         */
        private String workingDir = "../crypto-ml-strategy";
        
        /**
         * 仅在service模式下使用：策略服务URL列表
         * 支持多服务器部署时的负载均衡和故障转移
         */
        private String[] serviceUrls = {"http://localhost:8000"};
        
        /**
         * 仅在service模式下使用：服务连接超时（毫秒）
         */
        private int connectTimeout = 5000;
        
        /**
         * 仅在service模式下使用：服务读取超时（毫秒）
         */
        private int readTimeout = 30000;
        
        /**
         * 仅在service模式下使用：健康检查间隔（毫秒）
         */
        private int healthCheckInterval = 60000;
    }
} 