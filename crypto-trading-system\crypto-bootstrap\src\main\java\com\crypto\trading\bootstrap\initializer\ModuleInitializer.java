package com.crypto.trading.bootstrap.initializer;

/**
 * 模块初始化器接口
 * 所有需要参与系统启动初始化的模块都需要实现此接口
 */
public interface ModuleInitializer {
    
    /**
     * 获取模块名称
     *
     * @return 模块名称
     */
    String getName();
    
    /**
     * 获取模块优先级
     * 优先级数值越小，越先初始化
     *
     * @return 优先级
     */
    int getPriority();
    
    /**
     * 获取模块依赖
     * 返回该模块依赖的其他模块名称列表
     *
     * @return 依赖模块名称数组
     */
    String[] getDependencies();
    
    /**
     * 初始化前执行的操作
     */
    void beforeInitialize();
    
    /**
     * 执行初始化
     * 
     * @throws Exception 初始化过程中可能抛出的异常
     */
    void initialize() throws Exception;
    
    /**
     * 初始化后执行的操作
     */
    void afterInitialize();
    
    /**
     * 模块关闭时执行的操作
     */
    void shutdown();
    
    /**
     * 检查模块是否已初始化
     *
     * @return 如果模块已初始化，返回true；否则返回false
     */
    boolean isInitialized();
}