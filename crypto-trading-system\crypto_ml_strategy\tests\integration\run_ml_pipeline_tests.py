#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
ML管道集成测试专用运行器

专门用于执行ML管道端到端测试的运行器，包括DeepSeek蒸馏模型和在线学习功能测试。
"""

import argparse
import asyncio
import sys
from pathlib import Path
from typing import Dict, Any, List
from datetime import datetime
from loguru import logger

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from tests.integration.config.integration_test_config import get_test_config
from tests.integration.utils.test_orchestrator import create_test_orchestrator, TestCase, TestSuite, TestPriority
from tests.integration.reports.test_reporter import create_test_reporter, ReportConfig
from tests.integration.fixtures.mock_services import setup_mock_environment, teardown_mock_environment
from tests.integration.suites.ml_pipeline_integration_test_suite import create_ml_pipeline_integration_test_suite
from tests.integration.suites.test_deepseek_distillation import (
    test_deepseek_distillation_training,
    test_deepseek_inference_performance,
    test_knowledge_transfer_effectiveness,
    test_model_compression_analysis
)
from tests.integration.suites.test_online_learning_integration import (
    test_incremental_learning_mechanism,
    test_model_version_management,
    test_online_learning_performance_monitoring,
    test_adaptive_learning_strategies
)


class MLPipelineTestRunner:
    """ML管道测试专用运行器"""
    
    def __init__(self, environment: str = "dev"):
        """
        初始化ML管道测试运行器
        
        Args:
            environment: 测试环境
        """
        self.environment = environment
        self.config = get_test_config(environment)
        self.orchestrator = None
        self.reporter = None
        
        logger.info(f"ML管道测试运行器已初始化 - 环境: {environment}")
    
    async def setup_test_environment(self) -> None:
        """设置测试环境"""
        logger.info("设置ML管道测试环境...")
        
        # 设置Mock服务
        await setup_mock_environment()
        
        # 创建测试编排器
        self.orchestrator = create_test_orchestrator(
            max_workers=1,  # ML管道测试通常需要顺序执行
            default_timeout=self.config.test_env.test_timeout_seconds
        )
        
        # 创建报告生成器
        report_config = ReportConfig(
            output_dir=str(Path(__file__).parent / "reports" / "ml_pipeline"),
            include_charts=True,
            include_logs=True,
            include_artifacts=True
        )
        self.reporter = create_test_reporter(report_config)
        
        logger.info("ML管道测试环境设置完成")
    
    async def run_ml_pipeline_tests(self, test_suites: List[str] = None) -> Dict[str, Any]:
        """
        运行ML管道集成测试
        
        Args:
            test_suites: 要运行的测试套件列表
        
        Returns:
            Dict[str, Any]: 测试结果
        """
        logger.info("开始运行ML管道集成测试...")
        
        try:
            # 设置环境
            await self.setup_test_environment()
            
            # 注册测试套件
            self._register_test_suites(test_suites)
            
            # 添加进度回调
            def progress_callback(progress_info):
                logger.info(f"ML管道测试进度: {progress_info['progress_percentage']:.1f}% "
                          f"({progress_info['completed_tests']}/{progress_info['total_tests']})")
            
            self.orchestrator.add_progress_callback(progress_callback)
            
            # 执行测试
            test_results = await self.orchestrator.execute_all_tests()
            
            # 获取执行摘要
            execution_summary = self.orchestrator.get_execution_summary()
            
            # 添加ML特定的分析
            ml_analysis = self._analyze_ml_test_results(test_results)
            execution_summary['ml_analysis'] = ml_analysis
            
            # 生成专门的ML管道报告
            report_files = await self._generate_ml_pipeline_reports(
                test_results, execution_summary
            )
            
            logger.info(f"ML管道集成测试完成 - 成功率: {execution_summary['success_rate']:.1f}%")
            
            return {
                'test_results': test_results,
                'execution_summary': execution_summary,
                'ml_analysis': ml_analysis,
                'report_files': report_files,
                'success': execution_summary['failed_tests'] == 0
            }
            
        except Exception as e:
            logger.error(f"ML管道测试执行失败: {str(e)}")
            raise
        
        finally:
            # 清理环境
            await teardown_mock_environment()
    
    def _register_test_suites(self, suite_names: List[str] = None) -> None:
        """注册测试套件"""
        if not self.orchestrator:
            raise RuntimeError("测试编排器未初始化")
        
        # 注册主要ML管道测试套件
        if not suite_names or "ml_pipeline" in suite_names:
            ml_pipeline_suite = create_ml_pipeline_integration_test_suite()
            self.orchestrator.register_test_suite(ml_pipeline_suite)
            logger.info("已注册ML管道集成测试套件")
        
        # 注册DeepSeek蒸馏测试套件
        if not suite_names or "deepseek" in suite_names:
            deepseek_suite = self._create_deepseek_test_suite()
            self.orchestrator.register_test_suite(deepseek_suite)
            logger.info("已注册DeepSeek蒸馏测试套件")
        
        # 注册在线学习测试套件
        if not suite_names or "online_learning" in suite_names:
            online_learning_suite = self._create_online_learning_test_suite()
            self.orchestrator.register_test_suite(online_learning_suite)
            logger.info("已注册在线学习测试套件")
    
    def _create_deepseek_test_suite(self) -> TestSuite:
        """创建DeepSeek测试套件"""
        test_cases = [
            TestCase(
                test_id="deepseek.distillation_training",
                name="DeepSeek蒸馏模型训练测试",
                description="测试DeepSeek知识蒸馏模型的训练过程",
                test_function=test_deepseek_distillation_training,
                priority=TestPriority.HIGH,
                timeout=300,
                tags=["deepseek", "distillation", "training"]
            ),
            
            TestCase(
                test_id="deepseek.inference_performance",
                name="DeepSeek推理性能测试",
                description="测试DeepSeek蒸馏模型的推理性能",
                test_function=test_deepseek_inference_performance,
                dependencies=["deepseek.distillation_training"],
                priority=TestPriority.HIGH,
                timeout=180,
                tags=["deepseek", "inference", "performance"]
            ),
            
            TestCase(
                test_id="deepseek.knowledge_transfer",
                name="知识转移有效性测试",
                description="测试DeepSeek模型的知识转移效果",
                test_function=test_knowledge_transfer_effectiveness,
                dependencies=["deepseek.distillation_training"],
                priority=TestPriority.NORMAL,
                timeout=240,
                tags=["deepseek", "knowledge_transfer"]
            ),
            
            TestCase(
                test_id="deepseek.compression_analysis",
                name="模型压缩分析测试",
                description="分析不同压缩比对模型性能的影响",
                test_function=test_model_compression_analysis,
                dependencies=["deepseek.inference_performance"],
                priority=TestPriority.NORMAL,
                timeout=300,
                tags=["deepseek", "compression", "analysis"]
            )
        ]
        
        return TestSuite(
            suite_id="deepseek_distillation_suite",
            name="DeepSeek蒸馏模型测试套件",
            description="测试DeepSeek知识蒸馏模型的训练、推理和优化功能",
            test_cases=test_cases,
            parallel_execution=False,
            max_workers=1
        )
    
    def _create_online_learning_test_suite(self) -> TestSuite:
        """创建在线学习测试套件"""
        test_cases = [
            TestCase(
                test_id="online_learning.incremental_mechanism",
                name="增量学习机制测试",
                description="测试在线学习的增量更新机制",
                test_function=test_incremental_learning_mechanism,
                priority=TestPriority.HIGH,
                timeout=180,
                tags=["online_learning", "incremental"]
            ),
            
            TestCase(
                test_id="online_learning.version_management",
                name="模型版本管理测试",
                description="测试模型版本的创建和管理",
                test_function=test_model_version_management,
                dependencies=["online_learning.incremental_mechanism"],
                priority=TestPriority.HIGH,
                timeout=150,
                tags=["online_learning", "version_management"]
            ),
            
            TestCase(
                test_id="online_learning.performance_monitoring",
                name="性能监控测试",
                description="测试在线学习的性能监控功能",
                test_function=test_online_learning_performance_monitoring,
                dependencies=["online_learning.incremental_mechanism"],
                priority=TestPriority.NORMAL,
                timeout=200,
                tags=["online_learning", "monitoring"]
            ),
            
            TestCase(
                test_id="online_learning.adaptive_strategies",
                name="自适应学习策略测试",
                description="测试不同的自适应学习策略",
                test_function=test_adaptive_learning_strategies,
                dependencies=["online_learning.performance_monitoring"],
                priority=TestPriority.NORMAL,
                timeout=250,
                tags=["online_learning", "adaptive", "strategies"]
            )
        ]
        
        return TestSuite(
            suite_id="online_learning_suite",
            name="在线学习测试套件",
            description="测试在线学习引擎的增量学习、版本管理和自适应策略",
            test_cases=test_cases,
            parallel_execution=False,
            max_workers=1
        )
    
    def _analyze_ml_test_results(self, test_results: Dict[str, Any]) -> Dict[str, Any]:
        """分析ML测试结果"""
        ml_analysis = {
            "pipeline_performance": {},
            "model_quality": {},
            "learning_effectiveness": {},
            "overall_assessment": {}
        }
        
        try:
            # 分析管道性能
            pipeline_tests = [r for test_id, r in test_results.items() 
                            if "ml_pipeline" in test_id]
            
            if pipeline_tests:
                avg_execution_time = sum(r.duration for r in pipeline_tests) / len(pipeline_tests)
                ml_analysis["pipeline_performance"] = {
                    "avg_execution_time": avg_execution_time,
                    "pipeline_tests_count": len(pipeline_tests),
                    "pipeline_success_rate": sum(1 for r in pipeline_tests 
                                                if r.status.value == "passed") / len(pipeline_tests)
                }
            
            # 分析模型质量
            deepseek_tests = [r for test_id, r in test_results.items() 
                            if "deepseek" in test_id]
            
            if deepseek_tests:
                ml_analysis["model_quality"] = {
                    "deepseek_tests_count": len(deepseek_tests),
                    "deepseek_success_rate": sum(1 for r in deepseek_tests 
                                               if r.status.value == "passed") / len(deepseek_tests)
                }
            
            # 分析学习效果
            online_learning_tests = [r for test_id, r in test_results.items() 
                                   if "online_learning" in test_id]
            
            if online_learning_tests:
                ml_analysis["learning_effectiveness"] = {
                    "online_learning_tests_count": len(online_learning_tests),
                    "online_learning_success_rate": sum(1 for r in online_learning_tests 
                                                       if r.status.value == "passed") / len(online_learning_tests)
                }
            
            # 总体评估
            total_tests = len(test_results)
            passed_tests = sum(1 for r in test_results.values() 
                             if r.status.value == "passed")
            
            ml_analysis["overall_assessment"] = {
                "total_ml_tests": total_tests,
                "overall_success_rate": passed_tests / total_tests if total_tests > 0 else 0,
                "ml_pipeline_ready": passed_tests / total_tests >= 0.8 if total_tests > 0 else False
            }
            
        except Exception as e:
            logger.error(f"ML测试结果分析失败: {str(e)}")
            ml_analysis["analysis_error"] = str(e)
        
        return ml_analysis
    
    async def _generate_ml_pipeline_reports(self, test_results: Dict[str, Any], 
                                          execution_summary: Dict[str, Any]) -> Dict[str, str]:
        """生成ML管道专用报告"""
        # 生成标准报告
        report_files = self.reporter.generate_all_reports(
            test_results=test_results,
            execution_summary=execution_summary,
            report_name=f"ml_pipeline_integration_{self.environment}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        )
        
        # 生成ML特定分析报告
        ml_analysis_report = await self._generate_ml_analysis_report(
            execution_summary.get('ml_analysis', {}),
            test_results
        )
        
        report_files['ml_analysis'] = ml_analysis_report
        
        return report_files
    
    async def _generate_ml_analysis_report(self, ml_analysis: Dict[str, Any], 
                                         test_results: Dict[str, Any]) -> str:
        """生成ML分析报告"""
        report_content = f"""
# ML管道集成测试分析报告

## 生成时间
{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 测试环境
- 环境: {self.environment}
- 测试配置: {self.config.test_env.max_workers} 工作线程

## ML管道性能分析

### 管道执行性能
"""
        
        pipeline_perf = ml_analysis.get('pipeline_performance', {})
        if pipeline_perf:
            report_content += f"""
- 平均执行时间: {pipeline_perf.get('avg_execution_time', 0):.2f} 秒
- 管道测试数量: {pipeline_perf.get('pipeline_tests_count', 0)}
- 管道成功率: {pipeline_perf.get('pipeline_success_rate', 0):.1%}
"""
        
        report_content += """

### 模型质量评估
"""
        
        model_quality = ml_analysis.get('model_quality', {})
        if model_quality:
            report_content += f"""
- DeepSeek测试数量: {model_quality.get('deepseek_tests_count', 0)}
- DeepSeek成功率: {model_quality.get('deepseek_success_rate', 0):.1%}
"""
        
        report_content += """

### 在线学习效果
"""
        
        learning_eff = ml_analysis.get('learning_effectiveness', {})
        if learning_eff:
            report_content += f"""
- 在线学习测试数量: {learning_eff.get('online_learning_tests_count', 0)}
- 在线学习成功率: {learning_eff.get('online_learning_success_rate', 0):.1%}
"""
        
        report_content += """

## 总体评估
"""
        
        overall = ml_analysis.get('overall_assessment', {})
        if overall:
            report_content += f"""
- 总测试数量: {overall.get('total_ml_tests', 0)}
- 总体成功率: {overall.get('overall_success_rate', 0):.1%}
- ML管道就绪状态: {'✅ 就绪' if overall.get('ml_pipeline_ready', False) else '❌ 未就绪'}
"""
        
        report_content += """

## 详细测试结果

### 按测试类型分组
"""
        
        # 按测试类型分组显示结果
        test_groups = {}
        for test_id, result in test_results.items():
            if "ml_pipeline" in test_id:
                group = "ML管道"
            elif "deepseek" in test_id:
                group = "DeepSeek蒸馏"
            elif "online_learning" in test_id:
                group = "在线学习"
            else:
                group = "其他"
            
            if group not in test_groups:
                test_groups[group] = []
            test_groups[group].append((test_id, result))
        
        for group_name, group_tests in test_groups.items():
            report_content += f"\n#### {group_name}\n"
            for test_id, result in group_tests:
                status = "✅ 通过" if result.status.value == "passed" else "❌ 失败"
                report_content += f"- {result.name}: {status} ({result.duration:.2f}s)\n"
        
        report_content += """

## 建议和改进

### 性能优化建议
"""
        
        # 根据测试结果提供建议
        if overall.get('overall_success_rate', 0) < 0.9:
            report_content += "- 部分测试失败，建议检查失败的测试用例并优化相关功能\n"
        
        if pipeline_perf.get('avg_execution_time', 0) > 30:
            report_content += "- ML管道执行时间较长，建议优化数据处理和模型训练流程\n"
        
        if model_quality.get('deepseek_success_rate', 0) < 0.8:
            report_content += "- DeepSeek蒸馏模型测试成功率偏低，建议检查模型压缩和知识转移逻辑\n"
        
        if learning_eff.get('online_learning_success_rate', 0) < 0.8:
            report_content += "- 在线学习测试成功率偏低，建议优化增量学习算法和版本管理机制\n"
        
        # 保存报告
        report_dir = Path(self.reporter.output_dir)
        report_file = report_dir / f"ml_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        logger.info(f"ML分析报告已生成: {report_file}")
        return str(report_file)


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Crypto ML Strategy ML管道集成测试运行器")
    
    parser.add_argument(
        "--environment", "-e",
        choices=["dev", "staging", "prod-like"],
        default="dev",
        help="测试环境"
    )
    
    parser.add_argument(
        "--suites", "-s",
        nargs="*",
        choices=["ml_pipeline", "deepseek", "online_learning"],
        help="要运行的测试套件"
    )
    
    parser.add_argument(
        "--log-level", "-l",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="日志级别"
    )
    
    args = parser.parse_args()
    
    # 配置日志
    logger.remove()
    logger.add(
        sys.stdout,
        level=args.log_level,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
    )
    
    # 创建ML管道测试运行器
    runner = MLPipelineTestRunner(environment=args.environment)
    
    try:
        # 运行ML管道测试
        result = await runner.run_ml_pipeline_tests(test_suites=args.suites)
        
        # 输出结果摘要
        summary = result['execution_summary']
        ml_analysis = result['ml_analysis']
        
        print("\n" + "="*60)
        print("ML管道集成测试结果摘要")
        print("="*60)
        print(f"总测试数: {summary['total_tests']}")
        print(f"通过测试: {summary['passed_tests']}")
        print(f"失败测试: {summary['failed_tests']}")
        print(f"成功率: {summary['success_rate']:.1f}%")
        print(f"总耗时: {summary['total_duration']:.2f}秒")
        print()
        print("ML管道分析:")
        
        overall = ml_analysis.get('overall_assessment', {})
        if overall:
            print(f"  ML管道就绪: {'是' if overall.get('ml_pipeline_ready', False) else '否'}")
            print(f"  总体成功率: {overall.get('overall_success_rate', 0):.1f}%")
        
        print("="*60)
        
        # 输出报告文件路径
        print("\n报告文件:")
        for format_type, file_path in result['report_files'].items():
            print(f"  {format_type.upper()}: {file_path}")
        
        # 根据测试结果设置退出码
        sys.exit(0 if result['success'] else 1)
    
    except KeyboardInterrupt:
        logger.warning("ML管道测试被用户中断")
        sys.exit(130)
    
    except Exception as e:
        logger.error(f"ML管道测试运行失败: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())