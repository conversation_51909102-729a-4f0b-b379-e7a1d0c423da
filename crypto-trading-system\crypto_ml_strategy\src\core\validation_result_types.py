"""
Crypto ML Strategy - 验证结果数据类型

该模块定义了Task 12性能验证系统的核心数据结构，包括验证结果
和集成测试结果的数据类型定义。

主要功能：
- ValidationResult: 验证结果数据结构
- IntegrationTestResult: 集成测试结果数据结构
- 数据序列化和转换方法

作者: Crypto ML Strategy Team
日期: 2024-01-20
版本: 1.0.0
"""

from dataclasses import dataclass, field
from datetime import datetime
from typing import Any, Dict, Tuple


@dataclass
class ValidationResult:
    """验证结果数据结构"""
    validator_name: str
    target_value: float
    actual_value: float
    passed: bool
    confidence_interval: Tuple[float, float]
    sample_size: int
    p_value: float
    effect_size: float
    timestamp: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "validator_name": self.validator_name,
            "target_value": self.target_value,
            "actual_value": self.actual_value,
            "passed": self.passed,
            "confidence_interval": list(self.confidence_interval),
            "sample_size": self.sample_size,
            "p_value": self.p_value,
            "effect_size": self.effect_size,
            "statistical_significance": self.p_value < 0.05,
            "timestamp": self.timestamp.isoformat()
        }


@dataclass
class IntegrationTestResult:
    """集成测试结果数据结构"""
    test_name: str
    duration_seconds: float
    success_rate: float
    average_latency_ms: float
    peak_memory_mb: float
    throughput_ops_per_sec: float
    error_count: int
    passed: bool
    confidence_interval: Tuple[float, float]
    statistical_significance: bool
    timestamp: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "test_name": self.test_name,
            "duration_seconds": self.duration_seconds,
            "success_rate": self.success_rate,
            "average_latency_ms": self.average_latency_ms,
            "peak_memory_mb": self.peak_memory_mb,
            "throughput_ops_per_sec": self.throughput_ops_per_sec,
            "error_count": self.error_count,
            "passed": self.passed,
            "confidence_interval": list(self.confidence_interval),
            "statistical_significance": self.statistical_significance,
            "timestamp": self.timestamp.isoformat()
        }


def calculate_confidence_interval(data_points: list, confidence_level: float = 0.95) -> Tuple[float, float]:
    """计算置信区间的通用函数"""
    import numpy as np
    import scipy.stats as stats
    
    if len(data_points) < 2:
        return (0.0, 0.0)
    
    mean_value = np.mean(data_points)
    std_value = np.std(data_points, ddof=1)
    n = len(data_points)
    
    alpha = 1 - confidence_level
    t_critical = stats.t.ppf(1 - alpha/2, n - 1)
    margin_error = t_critical * (std_value / np.sqrt(n))
    
    return (mean_value - margin_error, mean_value + margin_error)


def test_statistical_significance(data_points: list, target_value: float, 
                                alternative: str = "less") -> bool:
    """测试统计显著性的通用函数"""
    import numpy as np
    import scipy.stats as stats
    
    if len(data_points) < 2:
        return False
    
    mean_value = np.mean(data_points)
    std_value = np.std(data_points, ddof=1)
    n = len(data_points)
    
    # 单样本t检验
    t_statistic = (mean_value - target_value) / (std_value / np.sqrt(n))
    
    if alternative == "less":
        p_value = stats.t.cdf(t_statistic, n - 1)
    elif alternative == "greater":
        p_value = 1 - stats.t.cdf(t_statistic, n - 1)
    else:  # two-sided
        p_value = 2 * (1 - stats.t.cdf(abs(t_statistic), n - 1))
    
    return p_value < 0.05


def calculate_effect_size(actual_value: float, target_value: float, 
                         std_value: float, effect_type: str = "cohens_d") -> float:
    """计算效应量的通用函数"""
    import numpy as np
    
    if effect_type == "cohens_d":
        # Cohen's d for continuous variables
        return (target_value - actual_value) / std_value if std_value > 0 else 0
    elif effect_type == "cohens_h":
        # Cohen's h for proportions
        return 2 * (np.arcsin(np.sqrt(actual_value)) - np.arcsin(np.sqrt(target_value)))
    else:
        return 0.0