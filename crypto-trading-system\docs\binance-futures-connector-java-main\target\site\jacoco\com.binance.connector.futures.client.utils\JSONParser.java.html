<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>JSONParser.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">io.github.binance:binance-futures-connector-java</a> &gt; <a href="index.source.html" class="el_package">com.binance.connector.futures.client.utils</a> &gt; <span class="el_source">JSONParser.java</span></div><h1>JSONParser.java</h1><pre class="source lang-java linenums">package com.binance.connector.futures.client.utils;

import java.util.ArrayList;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

public final class JSONParser {

    private JSONParser() {
    }

    public static String getJSONStringValue(String json, String key) {
        try {
<span class="fc" id="L15">            JSONObject obj = new JSONObject(json);</span>
<span class="fc" id="L16">            return obj.getString(key);</span>
<span class="fc" id="L17">        } catch (JSONException e) {</span>
<span class="fc" id="L18">            throw new JSONException(String.format(&quot;[JSONParser] Failed to get \&quot;%s\&quot;  from JSON object&quot;, key));</span>
        }
    }

    public static int getJSONIntValue(String json, String key) {
        try {
<span class="fc" id="L24">            JSONObject obj = new JSONObject(json);</span>
<span class="fc" id="L25">            return obj.getInt(key);</span>
<span class="fc" id="L26">        } catch (JSONException e) {</span>
<span class="fc" id="L27">            throw new JSONException(String.format(&quot;[JSONParser] Failed to get \&quot;%s\&quot; from JSON object&quot;, key));</span>
        }
    }

    public static String getJSONArray(ArrayList&lt;?&gt; symbols, String key) {
        try {
<span class="nc" id="L33">            JSONArray arr = new JSONArray(symbols);</span>
<span class="nc" id="L34">            return arr.toString();</span>
<span class="nc" id="L35">        } catch (JSONException e) {</span>
<span class="nc" id="L36">            throw new JSONException(String.format(&quot;[JSONParser] Failed to convert \&quot;%s\&quot; to JSON array&quot;, key));</span>
        }
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.7.202105040129</span></div></body></html>