package com.crypto.trading.common.util;

import org.junit.jupiter.api.Test;

import java.time.Duration;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 线程工具类测试
 * <p>
 * 测试线程和并发操作相关的工具方法
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
class ThreadUtilTest {

    /**
     * 测试创建线程池
     */
    @Test
    void testCreateThreadPool() {
        // 创建线程池
        ThreadPoolExecutor executor = ThreadUtil.createThreadPool(
                2, 4, 60, 100, "test-pool-");
        
        assertNotNull(executor);
        assertEquals(2, executor.getCorePoolSize());
        assertEquals(4, executor.getMaximumPoolSize());
        assertEquals(60, executor.getKeepAliveTime(TimeUnit.SECONDS));
        
        // 关闭线程池
        executor.shutdown();
    }

    /**
     * 测试创建虚拟线程池
     */
    @Test
    void testCreateVirtualThreadPool() {
        ExecutorService executor = ThreadUtil.createVirtualThreadPool();
        assertNotNull(executor);
        executor.shutdown();
    }

    /**
     * 测试获取默认线程池
     */
    @Test
    void testGetDefaultExecutor() {
        ThreadPoolExecutor executor = ThreadUtil.getDefaultExecutor();
        assertNotNull(executor);
        assertFalse(executor.isShutdown());
    }

    /**
     * 测试获取默认虚拟线程池
     */
    @Test
    void testGetDefaultVirtualExecutor() {
        ExecutorService executor = ThreadUtil.getDefaultVirtualExecutor();
        assertNotNull(executor);
        assertFalse(executor.isShutdown());
    }

    /**
     * 测试异步执行任务
     */
    @Test
    void testExecute() throws InterruptedException, ExecutionException {
        AtomicInteger value = new AtomicInteger(0);
        
        // 执行任务
        Future<?> future = ThreadUtil.execute(() -> {
            value.set(1);
        });
        
        // 等待任务完成
        future.get();
        
        assertEquals(1, value.get());
    }

    /**
     * 测试异步执行虚拟线程任务
     */
    @Test
    void testExecuteVirtual() throws InterruptedException, ExecutionException {
        AtomicInteger value = new AtomicInteger(0);
        
        // 执行任务
        Future<?> future = ThreadUtil.executeVirtual(() -> {
            value.set(1);
        });
        
        // 等待任务完成
        future.get();
        
        assertEquals(1, value.get());
    }

    /**
     * 测试异步执行有返回值的任务
     */
    @Test
    void testSubmit() throws InterruptedException, ExecutionException {
        // 执行有返回值的任务
        Future<Integer> future = ThreadUtil.submit(() -> 42);
        
        // 等待任务完成并获取结果
        Integer result = future.get();
        
        assertEquals(42, result);
    }

    /**
     * 测试异步执行有返回值的虚拟线程任务
     */
    @Test
    void testSubmitVirtual() throws InterruptedException, ExecutionException {
        // 执行有返回值的任务
        Future<Integer> future = ThreadUtil.submitVirtual(() -> 42);
        
        // 等待任务完成并获取结果
        Integer result = future.get();
        
        assertEquals(42, result);
    }

    /**
     * 测试异步执行有返回值的任务（使用CompletableFuture）
     */
    @Test
    void testSupplyAsync() throws InterruptedException, ExecutionException {
        // 执行有返回值的任务
        CompletableFuture<Integer> future = ThreadUtil.supplyAsync(() -> 42);
        
        // 等待任务完成并获取结果
        Integer result = future.get();
        
        assertEquals(42, result);
    }

    /**
     * 测试异步执行有返回值的虚拟线程任务（使用CompletableFuture）
     */
    @Test
    void testSupplyAsyncVirtual() throws InterruptedException, ExecutionException {
        // 执行有返回值的任务
        CompletableFuture<Integer> future = ThreadUtil.supplyAsyncVirtual(() -> 42);
        
        // 等待任务完成并获取结果
        Integer result = future.get();
        
        assertEquals(42, result);
    }

    /**
     * 测试异步执行无返回值的任务（使用CompletableFuture）
     */
    @Test
    void testRunAsync() throws InterruptedException, ExecutionException {
        AtomicInteger value = new AtomicInteger(0);
        
        // 执行无返回值的任务
        CompletableFuture<Void> future = ThreadUtil.runAsync(() -> {
            value.set(1);
        });
        
        // 等待任务完成
        future.get();
        
        assertEquals(1, value.get());
    }

    /**
     * 测试异步执行无返回值的虚拟线程任务（使用CompletableFuture）
     */
    @Test
    void testRunAsyncVirtual() throws InterruptedException, ExecutionException {
        AtomicInteger value = new AtomicInteger(0);
        
        // 执行无返回值的任务
        CompletableFuture<Void> future = ThreadUtil.runAsyncVirtual(() -> {
            value.set(1);
        });
        
        // 等待任务完成
        future.get();
        
        assertEquals(1, value.get());
    }

    /**
     * 测试使用虚拟线程执行任务
     */
    @Test
    void testStartVirtualThread() throws InterruptedException {
        AtomicInteger value = new AtomicInteger(0);
        CountDownLatch latch = new CountDownLatch(1);
        
        // 启动虚拟线程
        Thread thread = ThreadUtil.startVirtualThread(() -> {
            value.set(1);
            latch.countDown();
        });
        
        // 等待任务完成
        latch.await(1, TimeUnit.SECONDS);
        
        assertEquals(1, value.get());
        assertTrue(thread.isVirtual());
    }

    /**
     * 测试使用命名虚拟线程执行任务
     */
    @Test
    void testStartVirtualThreadWithName() throws InterruptedException {
        AtomicInteger value = new AtomicInteger(0);
        CountDownLatch latch = new CountDownLatch(1);
        
        // 启动命名虚拟线程
        Thread thread = ThreadUtil.startVirtualThread("test-virtual-thread", () -> {
            value.set(1);
            latch.countDown();
        });
        
        // 等待任务完成
        latch.await(1, TimeUnit.SECONDS);
        
        assertEquals(1, value.get());
        assertTrue(thread.isVirtual());
        assertEquals("test-virtual-thread", thread.getName());
    }

    /**
     * 测试并行处理集合元素
     */
    @Test
    void testParallelForEach() {
        List<Integer> list = Arrays.asList(1, 2, 3, 4, 5);
        AtomicInteger sum = new AtomicInteger(0);
        
        // 并行处理集合元素
        ThreadUtil.parallelForEach(list, sum::addAndGet);
        
        assertEquals(15, sum.get());
    }

    /**
     * 测试虚拟线程并行处理集合元素
     */
    @Test
    void testVirtualParallelForEach() throws InterruptedException {
        List<Integer> list = Arrays.asList(1, 2, 3, 4, 5);
        AtomicInteger sum = new AtomicInteger(0);
        
        // 使用虚拟线程并行处理集合元素
        ThreadUtil.virtualParallelForEach(list, sum::addAndGet);
        
        assertEquals(15, sum.get());
    }

    /**
     * 测试并行转换集合元素
     */
    @Test
    void testParallelMap() {
        List<Integer> list = Arrays.asList(1, 2, 3, 4, 5);
        
        // 并行转换集合元素
        List<Integer> result = ThreadUtil.parallelMap(list, i -> i * 2);
        
        assertEquals(Arrays.asList(2, 4, 6, 8, 10), result);
    }

    /**
     * 测试虚拟线程并行转换集合元素
     */
    @Test
    void testVirtualParallelMap() throws InterruptedException {
        List<Integer> list = Arrays.asList(1, 2, 3, 4, 5);
        
        // 使用虚拟线程并行转换集合元素
        List<Integer> result = ThreadUtil.virtualParallelMap(list, i -> i * 2);
        
        assertEquals(Arrays.asList(2, 4, 6, 8, 10), result);
    }

    /**
     * 测试休眠
     */
    @Test
    void testSleep() {
        long start = System.currentTimeMillis();
        
        // 休眠100毫秒
        ThreadUtil.sleep(100);
        
        long elapsed = System.currentTimeMillis() - start;
        assertTrue(elapsed >= 90, "休眠时间不足: " + elapsed);
    }

    /**
     * 测试使用Duration休眠
     */
    @Test
    void testSleepWithDuration() {
        long start = System.currentTimeMillis();
        
        // 休眠100毫秒
        ThreadUtil.sleep(Duration.ofMillis(100));
        
        long elapsed = System.currentTimeMillis() - start;
        assertTrue(elapsed >= 90, "休眠时间不足: " + elapsed);
    }

    /**
     * 测试等待Future完成
     */
    @Test
    void testWaitFor() throws TimeoutException, InterruptedException, ExecutionException {
        // 创建测试Future
        CompletableFuture<Integer> future = CompletableFuture.supplyAsync(() -> {
            ThreadUtil.sleep(100);
            return 42;
        });
        
        // 等待Future完成
        Integer result = ThreadUtil.waitFor(future, 1000);
        
        assertEquals(42, result);
    }

    /**
     * 测试使用Duration等待Future完成
     */
    @Test
    void testWaitForWithDuration() throws TimeoutException, InterruptedException, ExecutionException {
        // 创建测试Future
        CompletableFuture<Integer> future = CompletableFuture.supplyAsync(() -> {
            ThreadUtil.sleep(100);
            return 42;
        });
        
        // 等待Future完成
        Integer result = ThreadUtil.waitFor(future, Duration.ofMillis(1000));
        
        assertEquals(42, result);
    }

    /**
     * 测试关闭线程池
     */
    @Test
    void testShutdown() {
        // 创建测试线程池
        ExecutorService executor = Executors.newFixedThreadPool(2);
        
        // 关闭线程池
        ThreadUtil.shutdown(executor, 1);
        
        assertTrue(executor.isShutdown());
        assertTrue(executor.isTerminated());
    }

    /**
     * 测试获取当前线程信息
     */
    @Test
    void testGetCurrentThreadInfo() {
        // 获取当前线程名称
        String threadName = ThreadUtil.getCurrentThreadName();
        assertNotNull(threadName);
        
        // 获取当前线程ID
        long threadId = ThreadUtil.getCurrentThreadId();
        assertTrue(threadId > 0);
        
        // 获取当前线程堆栈跟踪
        StackTraceElement[] stackTrace = ThreadUtil.getCurrentStackTrace();
        assertNotNull(stackTrace);
        assertTrue(stackTrace.length > 0);
    }
} 