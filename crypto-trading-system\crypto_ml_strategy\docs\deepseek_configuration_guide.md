# DeepSeek配置指南

## 概述

crypto_ml_strategy项目现已完整支持DeepSeek蒸馏模型配置。本指南将帮助您正确配置DeepSeek相关参数。

## 配置文件结构

### 1. 主配置文件 (config/config.ini)

#### [deepseek] 节
```ini
[deepseek]
# DeepSeek模型配置
api_key = ***********************************
api_base_url = https://api.deepseek.com/v1
model_name = deepseek-chat
max_tokens = 4096
temperature = 0.7
top_p = 0.9
frequency_penalty = 0.0
presence_penalty = 0.0
timeout = 30
max_retries = 3
enable_local_distillation = True
local_model_path = models/deepseek_distilled.pkl
teacher_model_path = models/deepseek_teacher.pth
student_model_path = models/deepseek_student.pth
distillation_config_path = config/deepseek_distillation.json
```

**参数说明**：
- `api_key`: DeepSeek API密钥（如果使用在线API）
- `api_base_url`: DeepSeek API基础URL
- `model_name`: 使用的DeepSeek模型名称
- `max_tokens`: 最大token数量
- `temperature`: 生成温度（0.0-1.0）
- `top_p`: 核采样参数
- `frequency_penalty`: 频率惩罚
- `presence_penalty`: 存在惩罚
- `timeout`: API请求超时时间（秒）
- `max_retries`: 最大重试次数
- `enable_local_distillation`: 是否启用本地蒸馏模型
- `local_model_path`: 本地蒸馏模型路径
- `teacher_model_path`: 教师模型路径
- `student_model_path`: 学生模型路径
- `distillation_config_path`: 蒸馏配置文件路径

#### [deepseek_distillation] 节
```ini
[deepseek_distillation]
# DeepSeek知识蒸馏配置
teacher_hidden_dims = 512,256,128,64
student_hidden_dims = 128,64,32
teacher_dropout = 0.3
student_dropout = 0.2
distillation_temperature = 4.0
alpha = 0.7
beta = 0.3
feature_loss_weight = 0.1
compression_target_ratio = 4.0
enable_attention_transfer = True
enable_feature_matching = True
```

**参数说明**：
- `teacher_hidden_dims`: 教师模型隐藏层维度（逗号分隔）
- `student_hidden_dims`: 学生模型隐藏层维度（逗号分隔）
- `teacher_dropout`: 教师模型Dropout率
- `student_dropout`: 学生模型Dropout率
- `distillation_temperature`: 蒸馏温度
- `alpha`: 蒸馏损失权重
- `beta`: 硬标签损失权重
- `feature_loss_weight`: 特征匹配损失权重
- `compression_target_ratio`: 目标压缩比例
- `enable_attention_transfer`: 是否启用注意力转移
- `enable_feature_matching`: 是否启用特征匹配

### 2. 详细蒸馏配置文件 (config/deepseek_distillation.json)

这个JSON文件包含更详细的蒸馏配置，包括：
- 模型架构配置
- 蒸馏参数
- 训练配置
- 数据配置
- 模型路径
- 性能目标
- 日志配置
- API集成配置
- 部署配置

### 3. 环境变量配置 (.env)

复制 `.env.example` 为 `.env` 并填入实际配置：

```bash
# DeepSeek API Configuration
DEEPSEEK_API_KEY=your_actual_deepseek_api_key
DEEPSEEK_API_BASE_URL=https://api.deepseek.com/v1
DEEPSEEK_MODEL_NAME=deepseek-chat
```

## 配置获取方法

### 在代码中使用配置

```python
from core.config import Config

# 初始化配置
config = Config()

# 获取DeepSeek配置
deepseek_config = config.get_deepseek_config()
print(f"API Key: {deepseek_config['api_key']}")
print(f"Model Name: {deepseek_config['model_name']}")

# 获取蒸馏配置
distillation_config = config.get_deepseek_distillation_config()
print(f"Teacher Hidden Dims: {distillation_config['teacher_hidden_dims']}")
print(f"Compression Ratio: {distillation_config['compression_target_ratio']}")

# 获取单个配置项
api_key = config.get('deepseek', 'api_key')
temperature = config.get_float('deepseek', 'temperature', 0.7)
enable_distillation = config.get_bool('deepseek', 'enable_local_distillation', True)
```

## 配置优先级

配置值的获取优先级如下：
1. **环境变量** (最高优先级)
2. **config.ini文件**
3. **默认值** (最低优先级)

环境变量命名规则：`CRYPTO_ML_{SECTION}_{OPTION}`
例如：`CRYPTO_ML_DEEPSEEK_API_KEY`

## 使用场景

### 1. 仅使用本地蒸馏模型
```ini
[deepseek]
enable_local_distillation = True
api_key = # 可以留空
```

### 2. 使用在线API + 本地蒸馏
```ini
[deepseek]
api_key = your_actual_api_key
enable_local_distillation = True
```

### 3. 仅使用在线API
```ini
[deepseek]
api_key = your_actual_api_key
enable_local_distillation = False
```

## 安全建议

1. **不要在代码中硬编码API密钥**
2. **使用环境变量存储敏感信息**
3. **将 `.env` 文件添加到 `.gitignore`**
4. **定期轮换API密钥**
5. **在生产环境中使用专用的API密钥**

## 性能调优

### 蒸馏模型性能目标
- **推理时间**: <100ms
- **模型大小**: <50MB
- **准确率保持**: >95%
- **压缩比例**: 4:1
- **内存使用**: <500MB

### 调优参数
- `distillation_temperature`: 影响知识转移效果
- `alpha/beta`: 平衡蒸馏损失和硬标签损失
- `compression_target_ratio`: 控制模型压缩程度
- `teacher_hidden_dims/student_hidden_dims`: 控制模型复杂度

## 故障排除

### 常见问题

1. **API密钥无效**
   - 检查API密钥是否正确
   - 确认API密钥是否有效且未过期

2. **模型文件不存在**
   - 检查模型路径配置
   - 确保模型文件已正确生成

3. **配置文件格式错误**
   - 检查INI文件语法
   - 确认JSON文件格式正确

4. **环境变量未生效**
   - 检查环境变量命名是否正确
   - 确认环境变量已正确设置

### 调试方法

```python
# 检查配置加载状态
config = Config()
print("DeepSeek Config:", config.get_deepseek_config())
print("Distillation Config:", config.get_deepseek_distillation_config())

# 检查特定配置项
api_key = config.get('deepseek', 'api_key')
if not api_key or api_key == 'your_deepseek_api_key_here':
    print("警告：DeepSeek API密钥未配置")
```

## 更新历史

- **2025-06-20**: 初始版本，添加完整的DeepSeek配置支持
- 支持本地蒸馏模型和在线API两种模式
- 提供详细的配置参数和使用指南

## 相关文档

- [DeepSeek蒸馏模块文档](../src/ml/distillation/README.md)
- [配置管理模块文档](../src/core/README.md)
- [项目完整性报告](final_project_completion_report.md)