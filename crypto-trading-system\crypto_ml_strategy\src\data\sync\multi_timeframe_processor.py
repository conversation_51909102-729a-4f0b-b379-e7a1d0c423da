#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
多时间周期数据处理器

实现多时间周期数据整合功能，支持同时处理不同时间框架的市场数据进行特征工程。
包括数据对齐、重采样、特征融合和时间序列同步等功能。
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any, Union
from datetime import datetime, timedelta
import threading
from collections import defaultdict, deque
from dataclasses import dataclass
import time


@dataclass
class TimeframeConfig:
    """时间周期配置"""
    name: str  # 时间周期名称，如 '1m', '5m', '1h'
    minutes: int  # 对应的分钟数
    weight: float  # 在多时间周期融合中的权重
    max_cache_size: int  # 最大缓存大小
    enabled: bool = True  # 是否启用


class MultiTimeframeProcessor:
    """多时间周期数据处理器"""
    
    def __init__(self, config: Dict = None):
        """
        初始化多时间周期数据处理器
        
        Args:
            config: 配置参数字典
        """
        self.logger = logging.getLogger('cryptoMlStrategy.data.multi_timeframe')
        
        # 默认配置
        self.config = {
            'timeframes': {
                '1m': {'minutes': 1, 'weight': 0.1, 'max_cache_size': 1440},  # 1天数据
                '5m': {'minutes': 5, 'weight': 0.2, 'max_cache_size': 576},   # 2天数据
                '15m': {'minutes': 15, 'weight': 0.3, 'max_cache_size': 384}, # 4天数据
                '1h': {'minutes': 60, 'weight': 0.4, 'max_cache_size': 168},  # 1周数据
                '4h': {'minutes': 240, 'weight': 0.0, 'max_cache_size': 42},  # 1周数据（可选）
                '1d': {'minutes': 1440, 'weight': 0.0, 'max_cache_size': 30}  # 1月数据（可选）
            },
            'alignment': {
                'method': 'forward_fill',  # 'forward_fill', 'backward_fill', 'interpolate'
                'tolerance_seconds': 300,  # 时间对齐容差（秒）
                'min_data_points': 50     # 最少数据点数
            },
            'resampling': {
                'method': 'ohlc',  # 'ohlc', 'mean', 'last'
                'volume_method': 'sum',  # 'sum', 'mean'
                'label': 'right',  # 'left', 'right'
                'closed': 'right'  # 'left', 'right'
            },
            'feature_fusion': {
                'enabled': True,
                'fusion_method': 'weighted_average',  # 'weighted_average', 'concatenate', 'attention'
                'normalize_weights': True,
                'feature_selection_threshold': 0.01
            },
            'cache': {
                'enable_persistence': False,
                'persistence_interval': 3600,  # 持久化间隔（秒）
                'max_memory_usage_mb': 500
            }
        }
        
        # 更新用户配置
        if config:
            self._deep_update(self.config, config)
        
        # 初始化时间周期配置
        self.timeframes = {}
        for tf_name, tf_config in self.config['timeframes'].items():
            if tf_config.get('weight', 0) > 0:  # 只启用有权重的时间周期
                self.timeframes[tf_name] = TimeframeConfig(
                    name=tf_name,
                    minutes=tf_config['minutes'],
                    weight=tf_config['weight'],
                    max_cache_size=tf_config['max_cache_size'],
                    enabled=True
                )
        
        # 数据缓存：{symbol: {timeframe: deque}}
        self.data_cache = defaultdict(lambda: defaultdict(lambda: deque()))
        
        # 对齐后的数据缓存：{symbol: pd.DataFrame}
        self.aligned_cache = {}
        
        # 特征缓存：{symbol: pd.DataFrame}
        self.feature_cache = {}
        
        # 线程锁
        self.cache_lock = threading.RLock()
        
        # 统计信息
        self.stats = {
            'processed_records': 0,
            'alignment_operations': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'last_update_time': None
        }
        
        self.logger.info(f"多时间周期数据处理器初始化完成，启用时间周期: {list(self.timeframes.keys())}")
    
    def _deep_update(self, base_dict: Dict, update_dict: Dict):
        """深度更新字典"""
        for key, value in update_dict.items():
            if key in base_dict and isinstance(base_dict[key], dict) and isinstance(value, dict):
                self._deep_update(base_dict[key], value)
            else:
                base_dict[key] = value
    
    def add_data_point(self, symbol: str, timeframe: str, data: Dict[str, Any]) -> bool:
        """
        添加数据点到指定时间周期
        
        Args:
            symbol: 交易对符号
            timeframe: 时间周期
            data: 数据字典，必须包含timestamp和OHLCV数据
            
        Returns:
            是否成功添加
        """
        try:
            if timeframe not in self.timeframes:
                self.logger.warning(f"不支持的时间周期: {timeframe}")
                return False
            
            # 验证数据完整性
            required_fields = ['timestamp', 'open', 'high', 'low', 'close', 'volume']
            if not all(field in data for field in required_fields):
                self.logger.warning(f"数据缺少必要字段: {required_fields}")
                return False
            
            # 标准化时间戳
            timestamp = self._normalize_timestamp(data['timestamp'], timeframe)
            
            # 创建标准化数据点
            data_point = {
                'timestamp': timestamp,
                'datetime': pd.to_datetime(timestamp, unit='ms'),
                'open': float(data['open']),
                'high': float(data['high']),
                'low': float(data['low']),
                'close': float(data['close']),
                'volume': float(data['volume']),
                'timeframe': timeframe,
                'symbol': symbol
            }
            
            # 添加到缓存
            with self.cache_lock:
                cache = self.data_cache[symbol][timeframe]
                
                # 检查是否已存在相同时间戳的数据
                existing_idx = None
                for i, existing_point in enumerate(cache):
                    if existing_point['timestamp'] == timestamp:
                        existing_idx = i
                        break
                
                if existing_idx is not None:
                    # 更新现有数据点
                    cache[existing_idx] = data_point
                else:
                    # 添加新数据点
                    cache.append(data_point)
                    
                    # 保持缓存大小
                    max_size = self.timeframes[timeframe].max_cache_size
                    while len(cache) > max_size:
                        cache.popleft()
                
                # 清除相关的对齐缓存
                if symbol in self.aligned_cache:
                    del self.aligned_cache[symbol]
                if symbol in self.feature_cache:
                    del self.feature_cache[symbol]
            
            self.stats['processed_records'] += 1
            self.stats['last_update_time'] = datetime.now()
            
            return True
            
        except Exception as e:
            self.logger.error(f"添加数据点失败: {e}")
            return False
    
    def _normalize_timestamp(self, timestamp: Union[int, float, str], timeframe: str) -> int:
        """
        标准化时间戳到时间周期边界
        
        Args:
            timestamp: 原始时间戳
            timeframe: 时间周期
            
        Returns:
            标准化后的时间戳（毫秒）
        """
        try:
            # 转换为毫秒时间戳
            if isinstance(timestamp, str):
                dt = pd.to_datetime(timestamp)
                ts_ms = int(dt.timestamp() * 1000)
            elif isinstance(timestamp, (int, float)):
                # 判断是秒还是毫秒
                if timestamp > 1e12:  # 毫秒
                    ts_ms = int(timestamp)
                else:  # 秒
                    ts_ms = int(timestamp * 1000)
            else:
                raise ValueError(f"不支持的时间戳格式: {type(timestamp)}")
            
            # 获取时间周期的分钟数
            minutes = self.timeframes[timeframe].minutes
            
            # 对齐到时间周期边界
            dt = pd.to_datetime(ts_ms, unit='ms')
            
            if minutes < 60:  # 分钟级别
                aligned_dt = dt.replace(second=0, microsecond=0)
                aligned_dt = aligned_dt.replace(minute=(aligned_dt.minute // minutes) * minutes)
            elif minutes < 1440:  # 小时级别
                hours = minutes // 60
                aligned_dt = dt.replace(minute=0, second=0, microsecond=0)
                aligned_dt = aligned_dt.replace(hour=(aligned_dt.hour // hours) * hours)
            else:  # 日级别
                aligned_dt = dt.replace(hour=0, minute=0, second=0, microsecond=0)
            
            return int(aligned_dt.timestamp() * 1000)
            
        except Exception as e:
            self.logger.error(f"时间戳标准化失败: {e}")
            return int(timestamp) if isinstance(timestamp, (int, float)) else int(time.time() * 1000)
    
    def get_aligned_data(self, symbol: str, target_timeframe: str = None) -> Optional[pd.DataFrame]:
        """
        获取时间对齐后的多时间周期数据
        
        Args:
            symbol: 交易对符号
            target_timeframe: 目标时间周期，如果为None则使用最小时间周期
            
        Returns:
            对齐后的数据DataFrame
        """
        try:
            # 检查缓存
            cache_key = f"{symbol}_{target_timeframe or 'auto'}"
            if cache_key in self.aligned_cache:
                self.stats['cache_hits'] += 1
                return self.aligned_cache[cache_key].copy()
            
            self.stats['cache_misses'] += 1
            
            # 确定目标时间周期
            if target_timeframe is None:
                target_timeframe = min(self.timeframes.keys(), 
                                     key=lambda x: self.timeframes[x].minutes)
            
            if target_timeframe not in self.timeframes:
                self.logger.error(f"不支持的目标时间周期: {target_timeframe}")
                return None
            
            # 收集所有时间周期的数据
            timeframe_data = {}
            
            with self.cache_lock:
                for tf_name in self.timeframes.keys():
                    cache = self.data_cache[symbol][tf_name]
                    if len(cache) >= self.config['alignment']['min_data_points']:
                        df = pd.DataFrame(list(cache))
                        df.set_index('datetime', inplace=True)
                        df.sort_index(inplace=True)
                        timeframe_data[tf_name] = df
            
            if not timeframe_data:
                self.logger.warning(f"没有足够的数据进行对齐: {symbol}")
                return None
            
            # 执行数据对齐
            aligned_df = self._align_timeframes(timeframe_data, target_timeframe)
            
            if aligned_df is not None:
                # 缓存结果
                self.aligned_cache[cache_key] = aligned_df.copy()
                self.stats['alignment_operations'] += 1
            
            return aligned_df
            
        except Exception as e:
            self.logger.error(f"数据对齐失败: {e}")
            return None
    
    def _align_timeframes(self, timeframe_data: Dict[str, pd.DataFrame], 
                         target_timeframe: str) -> Optional[pd.DataFrame]:
        """
        对齐多个时间周期的数据
        
        Args:
            timeframe_data: 各时间周期的数据字典
            target_timeframe: 目标时间周期
            
        Returns:
            对齐后的DataFrame
        """
        try:
            if target_timeframe not in timeframe_data:
                self.logger.error(f"目标时间周期数据不存在: {target_timeframe}")
                return None
            
            # 以目标时间周期为基准
            base_df = timeframe_data[target_timeframe].copy()
            base_df = base_df.add_suffix(f'_{target_timeframe}')
            
            # 对齐其他时间周期的数据
            for tf_name, tf_df in timeframe_data.items():
                if tf_name == target_timeframe:
                    continue
                
                # 重采样到目标时间周期
                aligned_tf_df = self._resample_to_target(tf_df, target_timeframe, tf_name)
                
                if aligned_tf_df is not None:
                    # 添加时间周期后缀
                    aligned_tf_df = aligned_tf_df.add_suffix(f'_{tf_name}')
                    
                    # 合并到基准DataFrame
                    base_df = base_df.join(aligned_tf_df, how='left')
            
            # 处理缺失值
            alignment_method = self.config['alignment']['method']
            if alignment_method == 'forward_fill':
                base_df = base_df.fillna(method='ffill')
            elif alignment_method == 'backward_fill':
                base_df = base_df.fillna(method='bfill')
            elif alignment_method == 'interpolate':
                base_df = base_df.interpolate(method='linear')
            
            # 删除仍有缺失值的行
            base_df = base_df.dropna()
            
            return base_df
            
        except Exception as e:
            self.logger.error(f"时间周期对齐失败: {e}")
            return None
    
    def _resample_to_target(self, source_df: pd.DataFrame, target_timeframe: str, 
                           source_timeframe: str) -> Optional[pd.DataFrame]:
        """
        将源时间周期数据重采样到目标时间周期
        
        Args:
            source_df: 源数据DataFrame
            target_timeframe: 目标时间周期
            source_timeframe: 源时间周期
            
        Returns:
            重采样后的DataFrame
        """
        try:
            target_minutes = self.timeframes[target_timeframe].minutes
            source_minutes = self.timeframes[source_timeframe].minutes
            
            # 确定重采样规则
            if target_minutes <= source_minutes:
                # 目标时间周期更细，使用前向填充
                rule = f'{target_minutes}T'
                resampled = source_df.resample(rule, label=self.config['resampling']['label'],
                                             closed=self.config['resampling']['closed']).ffill()
            else:
                # 目标时间周期更粗，使用聚合
                rule = f'{target_minutes}T'
                
                # OHLC数据聚合
                ohlc_agg = {
                    'open': 'first',
                    'high': 'max',
                    'low': 'min',
                    'close': 'last'
                }
                
                # 成交量聚合
                volume_method = self.config['resampling']['volume_method']
                if volume_method == 'sum':
                    ohlc_agg['volume'] = 'sum'
                else:
                    ohlc_agg['volume'] = 'mean'
                
                resampled = source_df.resample(rule, label=self.config['resampling']['label'],
                                             closed=self.config['resampling']['closed']).agg(ohlc_agg)
            
            return resampled
            
        except Exception as e:
            self.logger.error(f"重采样失败: {e}")
            return None
    
    def get_fused_features(self, symbol: str, feature_columns: List[str] = None) -> Optional[pd.DataFrame]:
        """
        获取融合后的多时间周期特征
        
        Args:
            symbol: 交易对符号
            feature_columns: 要融合的特征列名列表
            
        Returns:
            融合后的特征DataFrame
        """
        try:
            # 检查特征缓存
            if symbol in self.feature_cache:
                cached_features = self.feature_cache[symbol]
                if feature_columns is None:
                    return cached_features.copy()
                else:
                    available_cols = [col for col in feature_columns if col in cached_features.columns]
                    return cached_features[available_cols].copy() if available_cols else None
            
            # 获取对齐后的数据
            aligned_data = self.get_aligned_data(symbol)
            if aligned_data is None or aligned_data.empty:
                return None
            
            # 执行特征融合
            fused_features = self._fuse_features(aligned_data, feature_columns)
            
            if fused_features is not None:
                # 缓存特征
                self.feature_cache[symbol] = fused_features.copy()
            
            return fused_features
            
        except Exception as e:
            self.logger.error(f"特征融合失败: {e}")
            return None
    
    def _fuse_features(self, aligned_data: pd.DataFrame, 
                      feature_columns: List[str] = None) -> Optional[pd.DataFrame]:
        """
        融合多时间周期特征
        
        Args:
            aligned_data: 对齐后的数据
            feature_columns: 要融合的特征列
            
        Returns:
            融合后的特征DataFrame
        """
        try:
            if not self.config['feature_fusion']['enabled']:
                return aligned_data
            
            fusion_method = self.config['feature_fusion']['fusion_method']
            
            if fusion_method == 'concatenate':
                # 简单拼接所有特征
                return aligned_data
            
            elif fusion_method == 'weighted_average':
                # 加权平均融合
                return self._weighted_average_fusion(aligned_data, feature_columns)
            
            elif fusion_method == 'attention':
                # 注意力机制融合（简化版）
                return self._attention_fusion(aligned_data, feature_columns)
            
            else:
                self.logger.warning(f"不支持的融合方法: {fusion_method}")
                return aligned_data
                
        except Exception as e:
            self.logger.error(f"特征融合处理失败: {e}")
            return aligned_data
    
    def _weighted_average_fusion(self, aligned_data: pd.DataFrame, 
                                feature_columns: List[str] = None) -> pd.DataFrame:
        """
        加权平均特征融合
        
        Args:
            aligned_data: 对齐后的数据
            feature_columns: 要融合的特征列
            
        Returns:
            融合后的DataFrame
        """
        try:
            result_df = aligned_data.copy()
            
            # 确定要融合的特征
            if feature_columns is None:
                feature_columns = ['open', 'high', 'low', 'close', 'volume']
            
            # 为每个基础特征创建融合列
            for feature in feature_columns:
                # 找到所有时间周期的该特征列
                feature_cols = [col for col in aligned_data.columns 
                               if col.startswith(feature + '_') and col != feature]
                
                if len(feature_cols) > 1:
                    # 计算加权平均
                    weighted_sum = 0
                    total_weight = 0
                    
                    for col in feature_cols:
                        # 提取时间周期名称
                        tf_name = col.split('_')[-1]
                        if tf_name in self.timeframes:
                            weight = self.timeframes[tf_name].weight
                            weighted_sum += aligned_data[col] * weight
                            total_weight += weight
                    
                    if total_weight > 0:
                        result_df[f'{feature}_fused'] = weighted_sum / total_weight
            
            return result_df
            
        except Exception as e:
            self.logger.error(f"加权平均融合失败: {e}")
            return aligned_data
    
    def _attention_fusion(self, aligned_data: pd.DataFrame, 
                         feature_columns: List[str] = None) -> pd.DataFrame:
        """
        注意力机制特征融合（简化版）
        
        Args:
            aligned_data: 对齐后的数据
            feature_columns: 要融合的特征列
            
        Returns:
            融合后的DataFrame
        """
        try:
            result_df = aligned_data.copy()
            
            # 确定要融合的特征
            if feature_columns is None:
                feature_columns = ['open', 'high', 'low', 'close', 'volume']
            
            # 简化的注意力机制：基于特征变化率计算注意力权重
            for feature in feature_columns:
                feature_cols = [col for col in aligned_data.columns 
                               if col.startswith(feature + '_') and col != feature]
                
                if len(feature_cols) > 1:
                    # 计算每个时间周期特征的变化率
                    attention_weights = {}
                    
                    for col in feature_cols:
                        # 计算变化率的标准差作为注意力权重
                        pct_change = aligned_data[col].pct_change().fillna(0)
                        volatility = pct_change.rolling(window=20).std().fillna(0)
                        attention_weights[col] = volatility
                    
                    # 归一化注意力权重
                    for idx in aligned_data.index:
                        weights = {col: attention_weights[col].loc[idx] for col in feature_cols}
                        total_weight = sum(weights.values())
                        
                        if total_weight > 0:
                            normalized_weights = {col: w/total_weight for col, w in weights.items()}
                            
                            # 计算注意力加权特征
                            fused_value = sum(aligned_data.loc[idx, col] * normalized_weights[col] 
                                            for col in feature_cols)
                            result_df.loc[idx, f'{feature}_attention'] = fused_value
            
            return result_df
            
        except Exception as e:
            self.logger.error(f"注意力融合失败: {e}")
            return aligned_data
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取处理器统计信息"""
        with self.cache_lock:
            cache_stats = {}
            for symbol, timeframes in self.data_cache.items():
                cache_stats[symbol] = {tf: len(cache) for tf, cache in timeframes.items()}
        
        return {
            'config': self.config,
            'enabled_timeframes': list(self.timeframes.keys()),
            'cache_statistics': cache_stats,
            'processing_stats': self.stats.copy(),
            'memory_usage': self._estimate_memory_usage()
        }
    
    def _estimate_memory_usage(self) -> Dict[str, float]:
        """估算内存使用量"""
        try:
            import sys
            
            data_cache_size = 0
            for symbol_cache in self.data_cache.values():
                for tf_cache in symbol_cache.values():
                    data_cache_size += sys.getsizeof(tf_cache)
            
            aligned_cache_size = sum(sys.getsizeof(df) for df in self.aligned_cache.values())
            feature_cache_size = sum(sys.getsizeof(df) for df in self.feature_cache.values())
            
            total_mb = (data_cache_size + aligned_cache_size + feature_cache_size) / (1024 * 1024)
            
            return {
                'data_cache_mb': data_cache_size / (1024 * 1024),
                'aligned_cache_mb': aligned_cache_size / (1024 * 1024),
                'feature_cache_mb': feature_cache_size / (1024 * 1024),
                'total_mb': total_mb
            }
            
        except Exception as e:
            self.logger.error(f"内存使用量估算失败: {e}")
            return {'total_mb': 0}
    
    def clear_cache(self, symbol: str = None, timeframe: str = None):
        """
        清除缓存
        
        Args:
            symbol: 要清除的交易对，如果为None则清除所有
            timeframe: 要清除的时间周期，如果为None则清除所有
        """
        with self.cache_lock:
            if symbol is None:
                # 清除所有缓存
                self.data_cache.clear()
                self.aligned_cache.clear()
                self.feature_cache.clear()
            else:
                # 清除指定交易对的缓存
                if timeframe is None:
                    if symbol in self.data_cache:
                        del self.data_cache[symbol]
                    if symbol in self.aligned_cache:
                        del self.aligned_cache[symbol]
                    if symbol in self.feature_cache:
                        del self.feature_cache[symbol]
                else:
                    # 清除指定交易对和时间周期的缓存
                    if symbol in self.data_cache and timeframe in self.data_cache[symbol]:
                        self.data_cache[symbol][timeframe].clear()
                    
                    # 清除相关的对齐和特征缓存
                    cache_keys_to_remove = [key for key in self.aligned_cache.keys() 
                                          if key.startswith(symbol)]
                    for key in cache_keys_to_remove:
                        del self.aligned_cache[key]
                    
                    if symbol in self.feature_cache:
                        del self.feature_cache[symbol]
        
        self.logger.info(f"缓存已清除: symbol={symbol}, timeframe={timeframe}")
    
    def export_data(self, symbol: str, timeframe: str = None, 
                   format: str = 'csv') -> Optional[str]:
        """
        导出数据到文件
        
        Args:
            symbol: 交易对符号
            timeframe: 时间周期，如果为None则导出融合特征
            format: 导出格式 ('csv', 'json', 'parquet')
            
        Returns:
            导出文件路径
        """
        try:
            from pathlib import Path
            import json
            
            # 创建导出目录
            export_dir = Path('exports/multi_timeframe')
            export_dir.mkdir(parents=True, exist_ok=True)
            
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            
            if timeframe is None:
                # 导出融合特征
                data = self.get_fused_features(symbol)
                filename = f"{symbol}_fused_features_{timestamp}.{format}"
            else:
                # 导出指定时间周期数据
                aligned_data = self.get_aligned_data(symbol, timeframe)
                data = aligned_data
                filename = f"{symbol}_{timeframe}_{timestamp}.{format}"
            
            if data is None or data.empty:
                self.logger.warning(f"没有数据可导出: {symbol} {timeframe}")
                return None
            
            filepath = export_dir / filename
            
            if format == 'csv':
                data.to_csv(filepath)
            elif format == 'json':
                data.to_json(filepath, orient='records', date_format='iso')
            elif format == 'parquet':
                data.to_parquet(filepath)
            else:
                raise ValueError(f"不支持的导出格式: {format}")
            
            self.logger.info(f"数据已导出到: {filepath}")
            return str(filepath)
            
        except Exception as e:
            self.logger.error(f"数据导出失败: {e}")
            return None