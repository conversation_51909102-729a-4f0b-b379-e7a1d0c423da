#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
配置管理模块

负责加载和解析配置文件，提供统一的配置访问接口。
支持从配置文件、环境变量和MySQL数据库获取配置。
"""

import os
import json
import configparser
from pathlib import Path
from typing import List, Dict, Any, Optional, Union
from loguru import logger


class Config:
    """配置管理类"""
    
    def __init__(self, config_path: str = None):
        """
        初始化配置管理器
        
        Args:
            config_path: 配置文件路径，默认为'config/config.ini'
        """
        if config_path is None:
            # 默认配置文件路径 - 从src目录向上一级到项目根目录
            base_dir = Path(__file__).parent.parent.parent
            config_path = str(base_dir / "config" / "config.ini")
        
        self.config_path = config_path
        self.config = configparser.ConfigParser()
        
        # 加载配置
        self._load_config()
        
        # 策略参数
        self.strategy_params = self._load_strategy_params()
        
        logger.info(f"配置已加载: {self.config_path}")
    
    def _load_config(self) -> None:
        """加载配置文件"""
        try:
            self.config.read(self.config_path, encoding="utf-8")
        except Exception as e:
            logger.error(f"加载配置文件失败: {str(e)}")
            raise
    
    def _load_strategy_params(self) -> Dict[str, Any]:
        """加载策略参数文件"""
        try:
            params_path = self.get("strategy", "params_file", 
                                 fallback="config/strategy_params.json")
            
            # 如果是相对路径，转换为绝对路径 - 从src目录向上一级到项目根目录
            if not os.path.isabs(params_path):
                base_dir = Path(__file__).parent.parent.parent
                params_path = str(base_dir / params_path)
            
            with open(params_path, "r", encoding="utf-8") as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"加载策略参数文件失败: {str(e)}")
            return {}
    
    def reload(self) -> None:
        """重新加载配置"""
        self._load_config()
        self.strategy_params = self._load_strategy_params()
        logger.info("配置已重新加载")
    
    def get(self, section: str, option: str, fallback: str = None) -> str:
        """
        获取配置项值（字符串）
        
        Args:
            section: 配置节
            option: 配置项
            fallback: 默认值
        
        Returns:
            配置值（字符串）
        """
        # 首先尝试从环境变量获取
        env_var = f"CRYPTO_ML_{section.upper()}_{option.upper()}"
        env_value = os.environ.get(env_var)
        if env_value is not None:
            return env_value
        
        # 然后从配置文件获取
        return self.config.get(section, option, fallback=fallback)
    
    def get_int(self, section: str, option: str, fallback: int = None) -> int:
        """获取整型配置值"""
        value = self.get(section, option, fallback=str(fallback) if fallback is not None else None)
        return int(value) if value is not None else None
    
    def get_float(self, section: str, option: str, fallback: float = None) -> float:
        """获取浮点型配置值"""
        value = self.get(section, option, fallback=str(fallback) if fallback is not None else None)
        return float(value) if value is not None else None
    
    def get_bool(self, section: str, option: str, fallback: bool = None) -> bool:
        """获取布尔型配置值"""
        value = self.get(section, option, fallback=str(fallback) if fallback is not None else None)
        if value is None:
            return None
        return value.lower() in ('true', 'yes', '1', 'y', 't')
    
    def get_list(self, section: str, option: str, fallback: List = None) -> List:
        """获取列表配置值（逗号分隔）"""
        value = self.get(section, option)
        if value is None:
            return fallback or []
        return [item.strip() for item in value.split(',')]
    
    def get_strategy_param(self, param_name: str, fallback: Any = None) -> Any:
        """
        获取策略参数
        
        Args:
            param_name: 参数名称，支持嵌套访问，如'lppl.window_size'
            fallback: 默认值
        
        Returns:
            策略参数值
        """
        if not self.strategy_params:
            return fallback
        
        # 处理嵌套参数
        if '.' in param_name:
            parts = param_name.split('.')
            current = self.strategy_params
            for part in parts:
                if part not in current:
                    return fallback
                current = current[part]
            return current
        
        return self.strategy_params.get(param_name, fallback)
    
    def get_deepseek_config(self) -> Dict[str, Any]:
        """
        获取DeepSeek配置
        
        Returns:
            DeepSeek配置字典
        """
        return {
            'api_key': self.get('deepseek', 'api_key'),
            'api_base_url': self.get('deepseek', 'api_base_url', 'https://api.deepseek.com/v1'),
            'model_name': self.get('deepseek', 'model_name', 'deepseek-chat'),
            'max_tokens': self.get_int('deepseek', 'max_tokens', 4096),
            'temperature': self.get_float('deepseek', 'temperature', 0.7),
            'top_p': self.get_float('deepseek', 'top_p', 0.9),
            'frequency_penalty': self.get_float('deepseek', 'frequency_penalty', 0.0),
            'presence_penalty': self.get_float('deepseek', 'presence_penalty', 0.0),
            'timeout': self.get_int('deepseek', 'timeout', 30),
            'max_retries': self.get_int('deepseek', 'max_retries', 3),
            'enable_local_distillation': self.get_bool('deepseek', 'enable_local_distillation', True),
            'local_model_path': self.get('deepseek', 'local_model_path', 'models/deepseek_distilled.pkl'),
            'teacher_model_path': self.get('deepseek', 'teacher_model_path', 'models/deepseek_teacher.pth'),
            'student_model_path': self.get('deepseek', 'student_model_path', 'models/deepseek_student.pth'),
            'distillation_config_path': self.get('deepseek', 'distillation_config_path', 'config/deepseek_distillation.json')
        }
    
    def get_deepseek_distillation_config(self) -> Dict[str, Any]:
        """
        获取DeepSeek蒸馏配置
        
        Returns:
            DeepSeek蒸馏配置字典
        """
        return {
            'teacher_hidden_dims': self.get_list('deepseek_distillation', 'teacher_hidden_dims', [512, 256, 128, 64]),
            'student_hidden_dims': self.get_list('deepseek_distillation', 'student_hidden_dims', [128, 64, 32]),
            'teacher_dropout': self.get_float('deepseek_distillation', 'teacher_dropout', 0.3),
            'student_dropout': self.get_float('deepseek_distillation', 'student_dropout', 0.2),
            'distillation_temperature': self.get_float('deepseek_distillation', 'distillation_temperature', 4.0),
            'alpha': self.get_float('deepseek_distillation', 'alpha', 0.7),
            'beta': self.get_float('deepseek_distillation', 'beta', 0.3),
            'feature_loss_weight': self.get_float('deepseek_distillation', 'feature_loss_weight', 0.1),
            'compression_target_ratio': self.get_float('deepseek_distillation', 'compression_target_ratio', 4.0),
            'enable_attention_transfer': self.get_bool('deepseek_distillation', 'enable_attention_transfer', True),
            'enable_feature_matching': self.get_bool('deepseek_distillation', 'enable_feature_matching', True)
        }
    
    def update_strategy_param(self, param_name: str, value: Any) -> None:
        """
        更新策略参数
        
        Args:
            param_name: 参数名称，支持嵌套访问，如'lppl.window_size'
            value: 参数值
        """
        if not self.strategy_params:
            self.strategy_params = {}
        
        # 处理嵌套参数
        if '.' in param_name:
            parts = param_name.split('.')
            current = self.strategy_params
            for i, part in enumerate(parts[:-1]):
                if part not in current:
                    current[part] = {}
                current = current[part]
            current[parts[-1]] = value
        else:
            self.strategy_params[param_name] = value
        
        # 保存策略参数
        self._save_strategy_params()
    
    def _save_strategy_params(self) -> None:
        """保存策略参数到文件"""
        try:
            params_path = self.get("strategy", "params_file", 
                                 fallback="config/strategy_params.json")
            
            # 如果是相对路径，转换为绝对路径 - 从src目录向上一级到项目根目录
            if not os.path.isabs(params_path):
                base_dir = Path(__file__).parent.parent.parent
                params_path = str(base_dir / params_path)
            
            # 确保目录存在
            os.makedirs(os.path.dirname(params_path), exist_ok=True)
            
            with open(params_path, "w", encoding="utf-8") as f:
                json.dump(self.strategy_params, f, indent=2, ensure_ascii=False)
            
            logger.info(f"策略参数已保存: {params_path}")
        except Exception as e:
            logger.error(f"保存策略参数失败: {str(e)}")
            raise