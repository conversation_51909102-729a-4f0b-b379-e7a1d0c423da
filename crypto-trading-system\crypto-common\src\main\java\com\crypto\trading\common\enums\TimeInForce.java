package com.crypto.trading.common.enums;

/**
 * 订单有效期类型枚举
 * <p>
 * 表示订单在市场中的有效期类型
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public enum TimeInForce {

    /**
     * 成交为止（Good Till Cancelled）
     * 订单会一直有效，直到被成交或取消
     */
    GTC("GTC", "成交为止"),

    /**
     * 立即成交或取消（Immediate or Cancel）
     * 订单会尝试以指定价格成交尽可能多的数量，然后取消剩余数量
     */
    IOC("IOC", "立即成交或取消"),

    /**
     * 全部成交或取消（Fill or Kill）
     * 订单要么完全成交，要么完全取消
     */
    FOK("FOK", "全部成交或取消"),

    /**
     * 限价成交或取消（Good Till Crossing）
     * 订单会尝试以指定价格或更好的价格成交，如果无法立即成交，则取消
     */
    GTX("GTX", "限价成交或取消");

    /**
     * 代码
     */
    private final String code;

    /**
     * 描述
     */
    private final String desc;

    /**
     * 构造函数
     *
     * @param code 代码
     * @param desc 描述
     */
    TimeInForce(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 获取代码
     *
     * @return 代码
     */
    public String getCode() {
        return code;
    }

    /**
     * 获取描述
     *
     * @return 描述
     */
    public String getDesc() {
        return desc;
    }

    /**
     * 根据代码获取枚举值
     *
     * @param code 代码
     * @return 枚举值
     */
    public static TimeInForce fromCode(String code) {
        for (TimeInForce tif : TimeInForce.values()) {
            if (tif.getCode().equals(code)) {
                return tif;
            }
        }
        throw new IllegalArgumentException("未知的订单有效期类型代码: " + code);
    }
}