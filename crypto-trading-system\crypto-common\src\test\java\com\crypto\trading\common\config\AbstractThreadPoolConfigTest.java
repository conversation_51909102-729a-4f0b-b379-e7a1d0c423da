package com.crypto.trading.common.config;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;

import static org.junit.jupiter.api.Assertions.*;

/**
 * AbstractThreadPoolConfig单元测试类
 */
class AbstractThreadPoolConfigTest {

    private TestThreadPoolConfig testConfig;

    @BeforeEach
    void setUp() {
        testConfig = new TestThreadPoolConfig();
    }

    @Test
    void testVirtualThreadTaskExecutor() {
        // 测试标准Bean方法：virtualThreadTaskExecutor
        AsyncTaskExecutor executor = testConfig.virtualThreadTaskExecutor();
        assertNotNull(executor, "虚拟线程任务执行器不应为空");
    }
    
    @Test
    void testVirtualThreadExecutor() {
        // 测试标准Bean方法：virtualThreadExecutor
        ExecutorService executor = testConfig.virtualThreadExecutor();
        assertNotNull(executor, "虚拟线程执行器不应为空");
        assertFalse(executor.isShutdown(), "虚拟线程执行器不应处于关闭状态");
        
        // 清理资源
        executor.shutdown();
    }
    
    @Test
    void testPlatformThreadPoolTaskExecutor() {
        // 测试标准Bean方法：platformThreadPoolTaskExecutor
        ThreadPoolTaskExecutor executor = testConfig.platformThreadPoolTaskExecutor();
        assertNotNull(executor, "平台线程池任务执行器不应为空");
        
        // 验证线程池配置
        assertEquals(testConfig.getDefaultCorePoolSize(), executor.getCorePoolSize(), "核心线程数应与默认值相匹配");
        assertEquals(testConfig.getDefaultMaxPoolSize(), executor.getMaxPoolSize(), "最大线程数应与默认值相匹配");
        assertEquals(testConfig.getDefaultQueueCapacity(), executor.getQueueCapacity(), "队列容量应与默认值相匹配");
        assertEquals(testConfig.getDefaultKeepAliveSeconds(), ((ThreadPoolExecutor) executor.getThreadPoolExecutor()).getKeepAliveTime(java.util.concurrent.TimeUnit.SECONDS), "线程保活时间应与默认值相匹配");
        assertTrue(executor.getThreadNamePrefix().contains("test"), "线程名前缀应包含模块名称");
        
        // 清理资源
        executor.shutdown();
    }

    @Test
    void testCreateVirtualThreadTaskExecutor() {
        // 测试创建虚拟线程任务执行器
        AsyncTaskExecutor executor = testConfig.createVirtualThreadTaskExecutorForTest();
        assertNotNull(executor, "虚拟线程任务执行器不应为空");
    }

    @Test
    void testCreateVirtualThreadExecutor() {
        // 测试创建虚拟线程执行器
        ExecutorService executor = testConfig.createVirtualThreadExecutorForTest();
        assertNotNull(executor, "虚拟线程执行器不应为空");
        assertFalse(executor.isShutdown(), "虚拟线程执行器不应处于关闭状态");
        
        // 清理资源
        executor.shutdown();
    }

    @Test
    void testCreatePlatformThreadPoolTaskExecutor() {
        // 测试创建平台线程池任务执行器
        ThreadPoolTaskExecutor executor = testConfig.createPlatformThreadPoolTaskExecutorForTest();
        assertNotNull(executor, "平台线程池任务执行器不应为空");
        
        // 验证线程池配置
        assertEquals(testConfig.getDefaultCorePoolSize(), executor.getCorePoolSize(), "核心线程数应与默认值相匹配");
        assertEquals(testConfig.getDefaultMaxPoolSize(), executor.getMaxPoolSize(), "最大线程数应与默认值相匹配");
        assertEquals(testConfig.getDefaultQueueCapacity(), executor.getQueueCapacity(), "队列容量应与默认值相匹配");
        assertEquals(testConfig.getDefaultKeepAliveSeconds(), ((ThreadPoolExecutor) executor.getThreadPoolExecutor()).getKeepAliveTime(java.util.concurrent.TimeUnit.SECONDS), "线程保活时间应与默认值相匹配");
        assertTrue(executor.getThreadNamePrefix().contains("test"), "线程名前缀应包含模块名称");
        
        // 清理资源
        executor.shutdown();
    }

    @Test
    void testCustomConfiguration() {
        // 测试自定义配置
        CustomThreadPoolConfig customConfig = new CustomThreadPoolConfig();
        ThreadPoolTaskExecutor executor = customConfig.platformThreadPoolTaskExecutor();
        
        // 验证自定义配置
        assertEquals(16, executor.getCorePoolSize(), "核心线程数应与自定义值相匹配");
        assertEquals(32, executor.getMaxPoolSize(), "最大线程数应与自定义值相匹配");
        assertEquals(2000, executor.getQueueCapacity(), "队列容量应与自定义值相匹配");
        assertEquals(120, ((ThreadPoolExecutor) executor.getThreadPoolExecutor()).getKeepAliveTime(java.util.concurrent.TimeUnit.SECONDS), "线程保活时间应与自定义值相匹配");
        assertTrue(executor.getThreadNamePrefix().contains("custom"), "线程名前缀应包含自定义模块名称");
        
        // 清理资源
        executor.shutdown();
    }

    /**
     * 测试用线程池配置类
     * 用于测试AbstractThreadPoolConfig的方法
     */
    private static class TestThreadPoolConfig extends AbstractThreadPoolConfig {
        
        @Override
        protected String getModuleName() {
            return "test";
        }
        
        // 暴露受保护的方法以便测试
        public AsyncTaskExecutor createVirtualThreadTaskExecutorForTest() {
            return createVirtualThreadTaskExecutor();
        }
        
        public ExecutorService createVirtualThreadExecutorForTest() {
            return createVirtualThreadExecutor();
        }
        
        public ThreadPoolTaskExecutor createPlatformThreadPoolTaskExecutorForTest() {
            return createPlatformThreadPoolTaskExecutor();
        }
    }
    
    /**
     * 自定义线程池配置类
     * 用于测试覆盖默认配置
     */
    private static class CustomThreadPoolConfig extends AbstractThreadPoolConfig {
        
        @Override
        protected String getModuleName() {
            return "custom";
        }
        
        @Override
        protected int getDefaultCorePoolSize() {
            return 16;
        }
        
        @Override
        protected int getDefaultMaxPoolSize() {
            return 32;
        }
        
        @Override
        protected int getDefaultQueueCapacity() {
            return 2000;
        }
        
        @Override
        protected int getDefaultKeepAliveSeconds() {
            return 120;
        }
    }
}