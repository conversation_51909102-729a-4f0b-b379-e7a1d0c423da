package com.crypto.trading.common.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.io.Serializable;

/**
 * @name: ParametersDTO
 * @author: zeek
 * @description: 策略参数数据传输对象，包含了更详细的策略内部信息
 * @create: 2024-07-16 00:22
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ParametersDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Double confidence;
    private FeatureContributionDTO featureContribution;
    private OnlineLearningDTO onlineLearning;
}
