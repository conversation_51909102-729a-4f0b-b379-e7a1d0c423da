#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
依赖注入架构测试验证模块

该模块提供依赖注入架构的全面测试验证功能。
"""

import sys
import time
import asyncio
from typing import Dict, Any, List
from loguru import logger

from .dependency_container import DependencyContainer
from .component_registry import ComponentRegistry
from .lifecycle_manager import AdvancedLifecycleManager
from .main_refactored import RefactoredTradingStrategyApp
from .service_interfaces import *


class DependencyInjectionTester:
    """
    依赖注入架构测试器
    
    提供全面的依赖注入架构测试和验证功能。
    """
    
    def __init__(self):
        """初始化测试器"""
        self.test_results: List[Dict[str, Any]] = []
        self.container: Optional[DependencyContainer] = None
        self.registry: Optional[ComponentRegistry] = None
        self.lifecycle_manager: Optional[AdvancedLifecycleManager] = None
        
        logger.info("依赖注入架构测试器初始化完成")
    
    def run_all_tests(self) -> Dict[str, Any]:
        """
        运行所有测试
        
        Returns:
            测试结果摘要
        """
        logger.info("🚀 开始依赖注入架构全面测试")
        
        test_summary = {
            "start_time": time.time(),
            "tests": [],
            "total_tests": 0,
            "passed_tests": 0,
            "failed_tests": 0,
            "success_rate": 0.0
        }
        
        # 1. 容器基础功能测试
        test_summary["tests"].append(self._test_container_basic_functionality())
        
        # 2. 服务接口测试
        test_summary["tests"].append(self._test_service_interfaces())
        
        # 3. 组件注册测试
        test_summary["tests"].append(self._test_component_registration())
        
        # 4. 依赖解析测试
        test_summary["tests"].append(self._test_dependency_resolution())
        
        # 5. 生命周期管理测试
        test_summary["tests"].append(self._test_lifecycle_management())
        
        # 6. 应用集成测试
        test_summary["tests"].append(self._test_application_integration())
        
        # 7. 性能测试
        test_summary["tests"].append(self._test_performance())
        
        # 8. 错误处理测试
        test_summary["tests"].append(self._test_error_handling())
        
        # 计算测试摘要
        test_summary["end_time"] = time.time()
        test_summary["duration"] = test_summary["end_time"] - test_summary["start_time"]
        test_summary["total_tests"] = len(test_summary["tests"])
        test_summary["passed_tests"] = sum(1 for test in test_summary["tests"] if test["passed"])
        test_summary["failed_tests"] = test_summary["total_tests"] - test_summary["passed_tests"]
        test_summary["success_rate"] = test_summary["passed_tests"] / test_summary["total_tests"] if test_summary["total_tests"] > 0 else 0
        
        logger.info(f"✅ 依赖注入架构测试完成: {test_summary['success_rate']:.2%} 成功率")
        
        return test_summary
    
    def _test_container_basic_functionality(self) -> Dict[str, Any]:
        """测试容器基础功能"""
        test_result = {
            "test_name": "容器基础功能测试",
            "passed": False,
            "details": {},
            "errors": []
        }
        
        try:
            logger.info("📦 测试容器基础功能...")
            
            # 创建容器
            container = DependencyContainer()
            
            # 测试单例注册和解析
            class TestService:
                def __init__(self):
                    self.value = "test"
            
            container.register_singleton(TestService)
            
            # 解析服务
            service1 = container.resolve(TestService)
            service2 = container.resolve(TestService)
            
            # 验证单例
            if service1 is service2:
                test_result["details"]["singleton_test"] = "通过"
            else:
                test_result["errors"].append("单例测试失败")
            
            # 测试瞬态注册
            container.register_transient(TestService)
            service3 = container.resolve(TestService)
            service4 = container.resolve(TestService)
            
            # 验证瞬态
            if service3 is not service4:
                test_result["details"]["transient_test"] = "通过"
            else:
                test_result["errors"].append("瞬态测试失败")
            
            # 测试服务信息
            service_info = container.get_service_info(TestService)
            if service_info:
                test_result["details"]["service_info_test"] = "通过"
            else:
                test_result["errors"].append("服务信息测试失败")
            
            test_result["passed"] = len(test_result["errors"]) == 0
            
        except Exception as e:
            test_result["errors"].append(f"容器测试异常: {e}")
            test_result["passed"] = False
        
        return test_result
    
    def _test_service_interfaces(self) -> Dict[str, Any]:
        """测试服务接口"""
        test_result = {
            "test_name": "服务接口测试",
            "passed": False,
            "details": {},
            "errors": []
        }
        
        try:
            logger.info("🔌 测试服务接口...")
            
            from .service_interfaces import get_service_interface, get_all_service_interfaces
            
            # 测试获取特定接口
            config_interface = get_service_interface('config')
            if config_interface == IConfigService:
                test_result["details"]["specific_interface_test"] = "通过"
            else:
                test_result["errors"].append("特定接口获取失败")
            
            # 测试获取所有接口
            all_interfaces = get_all_service_interfaces()
            expected_interfaces = [
                'config', 'logger', 'influxdb', 'mysql', 'kafka',
                'data_processor', 'data_loader', 'model', 'model_trainer',
                'model_version', 'online_learner', 'strategy', 'prediction_engine',
                'health_check', 'metrics', 'application', 'lifecycle'
            ]
            
            missing_interfaces = [name for name in expected_interfaces if name not in all_interfaces]
            if not missing_interfaces:
                test_result["details"]["all_interfaces_test"] = "通过"
                test_result["details"]["interface_count"] = len(all_interfaces)
            else:
                test_result["errors"].append(f"缺少接口: {missing_interfaces}")
            
            test_result["passed"] = len(test_result["errors"]) == 0
            
        except Exception as e:
            test_result["errors"].append(f"服务接口测试异常: {e}")
            test_result["passed"] = False
        
        return test_result
    
    def _test_component_registration(self) -> Dict[str, Any]:
        """测试组件注册"""
        test_result = {
            "test_name": "组件注册测试",
            "passed": False,
            "details": {},
            "errors": []
        }
        
        try:
            logger.info("📋 测试组件注册...")
            
            # 创建容器和注册表
            self.container = DependencyContainer()
            self.registry = ComponentRegistry(self.container)
            
            # 注册所有组件
            self.registry.register_all_components()
            
            # 验证注册
            validation_results = self.registry.validate_registrations()
            
            test_result["details"]["total_registered"] = validation_results["total_registered"]
            test_result["details"]["successful_resolutions"] = validation_results["successful_resolutions"]
            test_result["details"]["failed_resolutions"] = validation_results["failed_resolutions"]
            test_result["details"]["success_rate"] = validation_results["success_rate"]
            
            if validation_results["success_rate"] >= 0.8:
                test_result["details"]["registration_validation"] = "通过"
            else:
                test_result["errors"].extend(validation_results["errors"])
            
            # 测试已注册组件列表
            registered_components = self.registry.get_registered_components()
            if len(registered_components) > 10:  # 期望至少10个组件
                test_result["details"]["component_count_test"] = "通过"
                test_result["details"]["registered_components"] = len(registered_components)
            else:
                test_result["errors"].append(f"注册组件数量不足: {len(registered_components)}")
            
            test_result["passed"] = len(test_result["errors"]) == 0
            
        except Exception as e:
            test_result["errors"].append(f"组件注册测试异常: {e}")
            test_result["passed"] = False
        
        return test_result
    
    def _test_dependency_resolution(self) -> Dict[str, Any]:
        """测试依赖解析"""
        test_result = {
            "test_name": "依赖解析测试",
            "passed": False,
            "details": {},
            "errors": []
        }
        
        try:
            logger.info("🔗 测试依赖解析...")
            
            if not self.container:
                test_result["errors"].append("容器未初始化")
                return test_result
            
            # 测试核心服务解析
            core_services = [
                (IConfigService, "配置服务"),
                (ILoggerService, "日志服务"),
                (IKafkaService, "Kafka服务"),
                (IHealthCheckService, "健康检查服务"),
                (IMetricsService, "指标服务")
            ]
            
            resolved_services = 0
            for service_type, service_name in core_services:
                try:
                    service = self.container.resolve(service_type)
                    if service is not None:
                        resolved_services += 1
                        test_result["details"][f"{service_name}_resolution"] = "成功"
                    else:
                        test_result["errors"].append(f"{service_name}解析返回None")
                except Exception as e:
                    test_result["errors"].append(f"{service_name}解析失败: {e}")
            
            test_result["details"]["resolved_core_services"] = resolved_services
            test_result["details"]["total_core_services"] = len(core_services)
            
            # 测试依赖链解析
            try:
                # 这应该触发整个依赖链的解析
                strategy_service = self.container.resolve(IStrategyService)
                if strategy_service is not None:
                    test_result["details"]["dependency_chain_test"] = "通过"
                else:
                    test_result["errors"].append("依赖链解析失败")
            except Exception as e:
                test_result["errors"].append(f"依赖链解析异常: {e}")
            
            test_result["passed"] = len(test_result["errors"]) == 0
            
        except Exception as e:
            test_result["errors"].append(f"依赖解析测试异常: {e}")
            test_result["passed"] = False
        
        return test_result
    
    def _test_lifecycle_management(self) -> Dict[str, Any]:
        """测试生命周期管理"""
        test_result = {
            "test_name": "生命周期管理测试",
            "passed": False,
            "details": {},
            "errors": []
        }
        
        try:
            logger.info("🔄 测试生命周期管理...")
            
            # 创建生命周期管理器
            self.lifecycle_manager = AdvancedLifecycleManager()
            
            # 创建测试组件
            class TestComponent:
                def __init__(self, name: str):
                    self.name = name
                    self.initialized = False
                    self.started = False
                
                def initialize(self):
                    self.initialized = True
                
                def start(self):
                    self.started = True
                
                def stop(self):
                    self.started = False
                
                def cleanup(self):
                    self.initialized = False
            
            # 注册测试组件
            comp1 = TestComponent("Component1")
            comp2 = TestComponent("Component2")
            
            self.lifecycle_manager.register_component("comp1", comp1, priority=1)
            self.lifecycle_manager.register_component("comp2", comp2, priority=2, dependencies=["comp1"])
            
            # 测试初始化
            self.lifecycle_manager.initialize()
            if comp1.initialized and comp2.initialized:
                test_result["details"]["initialization_test"] = "通过"
            else:
                test_result["errors"].append("初始化测试失败")
            
            # 测试启动
            self.lifecycle_manager.start()
            if comp1.started and comp2.started:
                test_result["details"]["startup_test"] = "通过"
            else:
                test_result["errors"].append("启动测试失败")
            
            # 测试状态获取
            status = self.lifecycle_manager.get_component_status()
            if status["total_components"] == 2:
                test_result["details"]["status_test"] = "通过"
            else:
                test_result["errors"].append("状态测试失败")
            
            # 测试健康检查
            health = self.lifecycle_manager.perform_health_check()
            if health["total_components"] == 2:
                test_result["details"]["health_check_test"] = "通过"
            else:
                test_result["errors"].append("健康检查测试失败")
            
            # 测试停止
            self.lifecycle_manager.stop()
            if not comp1.started and not comp2.started:
                test_result["details"]["shutdown_test"] = "通过"
            else:
                test_result["errors"].append("停止测试失败")
            
            # 测试清理
            self.lifecycle_manager.cleanup()
            if not comp1.initialized and not comp2.initialized:
                test_result["details"]["cleanup_test"] = "通过"
            else:
                test_result["errors"].append("清理测试失败")
            
            test_result["passed"] = len(test_result["errors"]) == 0
            
        except Exception as e:
            test_result["errors"].append(f"生命周期管理测试异常: {e}")
            test_result["passed"] = False
        
        return test_result
    
    def _test_application_integration(self) -> Dict[str, Any]:
        """测试应用集成"""
        test_result = {
            "test_name": "应用集成测试",
            "passed": False,
            "details": {},
            "errors": []
        }
        
        try:
            logger.info("🔧 测试应用集成...")
            
            # 创建重构后的应用（不启动）
            app = RefactoredTradingStrategyApp()
            
            # 测试初始化
            app.initialize()
            if app._initialized:
                test_result["details"]["app_initialization"] = "通过"
            else:
                test_result["errors"].append("应用初始化失败")
            
            # 测试状态获取
            status = app.get_status()
            if status["initialized"]:
                test_result["details"]["app_status_test"] = "通过"
                test_result["details"]["status_keys"] = list(status.keys())
            else:
                test_result["errors"].append("应用状态测试失败")
            
            # 测试清理
            app.cleanup()
            test_result["details"]["app_cleanup"] = "通过"
            
            test_result["passed"] = len(test_result["errors"]) == 0
            
        except Exception as e:
            test_result["errors"].append(f"应用集成测试异常: {e}")
            test_result["passed"] = False
        
        return test_result
    
    def _test_performance(self) -> Dict[str, Any]:
        """测试性能"""
        test_result = {
            "test_name": "性能测试",
            "passed": False,
            "details": {},
            "errors": []
        }
        
        try:
            logger.info("⚡ 测试性能...")
            
            if not self.container:
                test_result["errors"].append("容器未初始化")
                return test_result
            
            # 测试服务解析性能
            start_time = time.time()
            iterations = 1000
            
            for _ in range(iterations):
                service = self.container.resolve(IConfigService)
            
            resolution_time = (time.time() - start_time) * 1000  # 毫秒
            avg_resolution_time = resolution_time / iterations
            
            test_result["details"]["resolution_iterations"] = iterations
            test_result["details"]["total_resolution_time_ms"] = resolution_time
            test_result["details"]["avg_resolution_time_ms"] = avg_resolution_time
            
            # 性能要求：平均解析时间 < 1ms
            if avg_resolution_time < 1.0:
                test_result["details"]["performance_test"] = "通过"
            else:
                test_result["errors"].append(f"性能不达标: {avg_resolution_time:.2f}ms > 1ms")
            
            test_result["passed"] = len(test_result["errors"]) == 0
            
        except Exception as e:
            test_result["errors"].append(f"性能测试异常: {e}")
            test_result["passed"] = False
        
        return test_result
    
    def _test_error_handling(self) -> Dict[str, Any]:
        """测试错误处理"""
        test_result = {
            "test_name": "错误处理测试",
            "passed": False,
            "details": {},
            "errors": []
        }
        
        try:
            logger.info("🚨 测试错误处理...")
            
            container = DependencyContainer()
            
            # 测试未注册服务解析
            class UnregisteredService:
                pass
            
            try:
                container.resolve(UnregisteredService)
                test_result["errors"].append("未注册服务解析应该失败")
            except ValueError:
                test_result["details"]["unregistered_service_test"] = "通过"
            except Exception as e:
                test_result["errors"].append(f"未注册服务测试异常: {e}")
            
            # 测试循环依赖检测
            class ServiceA:
                def __init__(self, service_b: 'ServiceB'):
                    self.service_b = service_b
            
            class ServiceB:
                def __init__(self, service_a: ServiceA):
                    self.service_a = service_a
            
            container.register_transient(ServiceA)
            container.register_transient(ServiceB)
            
            try:
                container.resolve(ServiceA)
                test_result["errors"].append("循环依赖检测应该失败")
            except ValueError as e:
                if "循环依赖" in str(e):
                    test_result["details"]["circular_dependency_test"] = "通过"
                else:
                    test_result["errors"].append(f"循环依赖错误消息不正确: {e}")
            except Exception as e:
                test_result["errors"].append(f"循环依赖测试异常: {e}")
            
            test_result["passed"] = len(test_result["errors"]) == 0
            
        except Exception as e:
            test_result["errors"].append(f"错误处理测试异常: {e}")
            test_result["passed"] = False
        
        return test_result


def main():
    """主函数"""
    try:
        # 创建测试器
        tester = DependencyInjectionTester()
        
        # 运行所有测试
        results = tester.run_all_tests()
        
        # 输出测试结果
        logger.info("📊 测试结果摘要:")
        logger.info(f"总测试数: {results['total_tests']}")
        logger.info(f"通过测试: {results['passed_tests']}")
        logger.info(f"失败测试: {results['failed_tests']}")
        logger.info(f"成功率: {results['success_rate']:.2%}")
        logger.info(f"测试耗时: {results['duration']:.2f}秒")
        
        # 输出详细结果
        for test in results["tests"]:
            status = "✅" if test["passed"] else "❌"
            logger.info(f"{status} {test['test_name']}")
            
            if not test["passed"] and test["errors"]:
                for error in test["errors"]:
                    logger.error(f"   错误: {error}")
        
        # 返回退出码
        return 0 if results["success_rate"] >= 0.8 else 1
        
    except Exception as e:
        logger.error(f"测试执行失败: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())