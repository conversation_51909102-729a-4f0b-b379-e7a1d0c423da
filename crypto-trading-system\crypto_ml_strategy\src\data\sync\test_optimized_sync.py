#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
优化时间框架同步器测试

测试OptimizedTimeframeSync的功能和性能。
"""

import logging
import time
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def generate_test_data(symbol: str, timeframe: str, count: int = 100) -> List[Dict]:
    """生成测试数据"""
    try:
        # 时间框架到分钟的映射
        tf_minutes = {
            '1m': 1, '5m': 5, '15m': 15, 
            '1h': 60, '4h': 240, '1d': 1440
        }
        
        minutes = tf_minutes.get(timeframe, 1)
        start_time = datetime.now() - timedelta(minutes=minutes * count)
        
        data_points = []
        base_price = 50000.0  # BTC基础价格
        
        for i in range(count):
            timestamp = start_time + timedelta(minutes=minutes * i)
            
            # 模拟价格波动
            price_change = np.random.normal(0, 0.02)  # 2%标准差
            price = base_price * (1 + price_change)
            
            # 生成OHLCV数据
            high = price * (1 + abs(np.random.normal(0, 0.01)))
            low = price * (1 - abs(np.random.normal(0, 0.01)))
            volume = np.random.uniform(100, 1000)
            
            data_point = {
                'timestamp': int(timestamp.timestamp() * 1000),
                'open': price,
                'high': max(price, high),
                'low': min(price, low),
                'close': price,
                'volume': volume
            }
            
            data_points.append(data_point)
            base_price = price  # 下一个周期的基础价格
        
        return data_points
        
    except Exception as e:
        logger.error(f"测试数据生成失败: {e}")
        return []


def test_basic_functionality():
    """测试基本功能"""
    logger.info("开始基本功能测试...")
    
    try:
        from .optimized_timeframe_sync import OptimizedTimeframeSync
        
        # 创建同步器实例
        config = {
            'sync': {
                'strategy': 'nearest',
                'tolerance_ms': 5000,
                'enable_async': False
            },
            'timeframes': {
                '1m': {'weight': 0.1, 'enabled': True},
                '5m': {'weight': 0.2, 'enabled': True},
                '15m': {'weight': 0.3, 'enabled': True},
                '1h': {'weight': 0.4, 'enabled': True}
            }
        }
        
        sync = OptimizedTimeframeSync(config)
        
        # 生成测试数据
        symbol = "BTCUSDT"
        timeframes = ['1m', '5m', '15m', '1h']
        
        for tf in timeframes:
            test_data = generate_test_data(symbol, tf, 50)
            logger.info(f"为 {tf} 生成了 {len(test_data)} 个数据点")
            
            # 添加数据点
            for data_point in test_data:
                success = sync.add_data_point(symbol, tf, data_point)
                if not success:
                    logger.warning(f"数据点添加失败: {tf}")
        
        # 测试同步
        logger.info("测试数据同步...")
        synchronized_df = sync.sync_timeframes(symbol, '1m')
        
        if synchronized_df is not None:
            logger.info(f"同步成功，数据形状: {synchronized_df.shape}")
            logger.info(f"列名: {list(synchronized_df.columns)}")
            
            # 测试特征融合
            logger.info("测试特征融合...")
            fused_df = sync.fuse_features(synchronized_df)
            logger.info(f"融合后数据形状: {fused_df.shape}")
            
            # 测试质量验证
            logger.info("测试质量验证...")
            quality_result = sync.validate_sync_quality(synchronized_df, symbol)
            logger.info(f"质量验证结果: {quality_result}")
            
            # 测试性能指标
            logger.info("测试性能指标...")
            metrics = sync.get_performance_metrics()
            logger.info(f"性能指标: {metrics}")
            
        else:
            logger.error("数据同步失败")
        
        # 清理资源
        sync.cleanup_resources()
        logger.info("基本功能测试完成")
        
        return True
        
    except Exception as e:
        logger.error(f"基本功能测试失败: {e}")
        return False


def test_performance():
    """测试性能"""
    logger.info("开始性能测试...")
    
    try:
        from .optimized_timeframe_sync import OptimizedTimeframeSync
        
        sync = OptimizedTimeframeSync()
        symbol = "BTCUSDT"
        
        # 性能测试：大量数据添加
        start_time = time.time()
        
        for tf in ['1m', '5m', '15m', '1h']:
            test_data = generate_test_data(symbol, tf, 1000)  # 1000个数据点
            
            for data_point in test_data:
                sync.add_data_point(symbol, tf, data_point)
        
        add_time = time.time() - start_time
        logger.info(f"添加4000个数据点耗时: {add_time:.3f}秒")
        
        # 性能测试：同步操作
        start_time = time.time()
        
        for _ in range(10):  # 执行10次同步
            synchronized_df = sync.sync_timeframes(symbol, '1m')
        
        sync_time = time.time() - start_time
        logger.info(f"执行10次同步操作耗时: {sync_time:.3f}秒")
        logger.info(f"平均同步延迟: {sync_time * 100:.1f}ms")
        
        # 获取性能指标
        metrics = sync.get_performance_metrics()
        logger.info(f"最终性能指标: {metrics}")
        
        # 验证性能目标
        avg_latency = sync_time * 100  # 转换为毫秒
        if avg_latency < 100:  # 目标：<100ms
            logger.info(f"✅ 性能目标达成：同步延迟 {avg_latency:.1f}ms < 100ms")
        else:
            logger.warning(f"❌ 性能目标未达成：同步延迟 {avg_latency:.1f}ms >= 100ms")
        
        sync.cleanup_resources()
        logger.info("性能测试完成")
        
        return avg_latency < 100
        
    except Exception as e:
        logger.error(f"性能测试失败: {e}")
        return False


def test_memory_efficiency():
    """测试内存效率"""
    logger.info("开始内存效率测试...")
    
    try:
        import psutil
        from .optimized_timeframe_sync import OptimizedTimeframeSync
        
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        sync = OptimizedTimeframeSync()
        symbol = "BTCUSDT"
        
        # 添加大量数据
        for tf in ['1m', '5m', '15m', '1h']:
            test_data = generate_test_data(symbol, tf, 2000)  # 2000个数据点
            
            for data_point in test_data:
                sync.add_data_point(symbol, tf, data_point)
        
        # 执行多次同步操作
        for _ in range(50):
            sync.sync_timeframes(symbol, '1m')
        
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory
        
        logger.info(f"初始内存: {initial_memory:.1f}MB")
        logger.info(f"最终内存: {final_memory:.1f}MB")
        logger.info(f"内存增长: {memory_increase:.1f}MB")
        
        # 验证内存目标
        if memory_increase < 100:  # 目标：增长<100MB
            logger.info(f"✅ 内存效率目标达成：增长 {memory_increase:.1f}MB < 100MB")
        else:
            logger.warning(f"❌ 内存效率目标未达成：增长 {memory_increase:.1f}MB >= 100MB")
        
        sync.cleanup_resources()
        logger.info("内存效率测试完成")
        
        return memory_increase < 100
        
    except Exception as e:
        logger.error(f"内存效率测试失败: {e}")
        return False


def run_all_tests():
    """运行所有测试"""
    logger.info("=" * 50)
    logger.info("开始优化时间框架同步器测试套件")
    logger.info("=" * 50)
    
    test_results = {}
    
    # 基本功能测试
    test_results['basic_functionality'] = test_basic_functionality()
    
    # 性能测试
    test_results['performance'] = test_performance()
    
    # 内存效率测试
    test_results['memory_efficiency'] = test_memory_efficiency()
    
    # 汇总结果
    logger.info("=" * 50)
    logger.info("测试结果汇总:")
    logger.info("=" * 50)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"{test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\n总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        logger.info("🎉 所有测试通过！优化时间框架同步器工作正常。")
    else:
        logger.warning("⚠️ 部分测试失败，需要进一步优化。")
    
    return passed == total


if __name__ == "__main__":
    run_all_tests()