package com.crypto.trading.common.exception;

/**
 * 策略服务异常类
 * 用于封装与策略服务通信过程中发生的异常
 */
public class StrategyServiceException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    /**
     * 构造一个新的策略服务异常
     */
    public StrategyServiceException() {
        super();
    }

    /**
     * 构造一个新的策略服务异常，带有指定的详细消息
     * 
     * @param message 详细消息
     */
    public StrategyServiceException(String message) {
        super(message);
    }

    /**
     * 构造一个新的策略服务异常，带有指定的详细消息和原因
     * 
     * @param message 详细消息
     * @param cause 原因
     */
    public StrategyServiceException(String message, Throwable cause) {
        super(message, cause);
    }

    /**
     * 构造一个新的策略服务异常，带有指定的原因
     * 
     * @param cause 原因
     */
    public StrategyServiceException(Throwable cause) {
        super(cause);
    }
}