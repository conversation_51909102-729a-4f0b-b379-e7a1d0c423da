package com.crypto.trading.bootstrap.config.adapter;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.crypto.trading.bootstrap.config.MarketDataConfig;

/**
 * MarketDataConfig桥接类
 * 负责将bootstrap模块的MarketDataConfig配置转换为market-data模块的MarketDataConfig对象
 */
@Configuration
public class MarketDataConfigBridge {
    
    private static final Logger log = LoggerFactory.getLogger(MarketDataConfigBridge.class);
    
    @Autowired
    private MarketDataConfig marketDataConfig;
    
    /**
     * 创建Market模块的MarketDataConfig对象
     * 
     * @return Market模块的MarketDataConfig对象
     */
    @Bean
    public com.crypto.trading.market.config.MarketDataConfig marketMarketDataConfig() {
        log.info("创建Market模块MarketDataConfig桥接对象");
        
        // 创建一个新的MarketMarketDataConfig实例
        return new com.crypto.trading.market.config.MarketDataConfig() {
            @Override
            public boolean isKlineEnabled() {
                return marketDataConfig.getKline().isEnabled();
            }
            
            @Override
            public boolean isDepthEnabled() {
                return marketDataConfig.getDepth().isEnabled();
            }
            
            @Override
            public boolean isTradeEnabled() {
                return marketDataConfig.getTrade().isEnabled();
            }
            
            @Override
            public int getDepthLevels() {
                return marketDataConfig.getDepth().getLevels();
            }
            
            @Override
            public int getDepthSpeed() {
                return marketDataConfig.getDepth().getSpeed();
            }
            
            @Override
            public int getInfluxDbBatchSize() {
                // 从配置中获取值，而不是使用硬编码默认值
                return marketDataConfig.getInfluxdb() != null && marketDataConfig.getInfluxdb().getBatchSize() != null ? 
                       marketDataConfig.getInfluxdb().getBatchSize() : 1000;
            }
            
            @Override
            public int getInfluxDbFlushInterval() {
                // 从配置中获取值，而不是使用硬编码默认值
                return marketDataConfig.getInfluxdb() != null && marketDataConfig.getInfluxdb().getFlushInterval() != null ? 
                       marketDataConfig.getInfluxdb().getFlushInterval() : 5000;
            }
        };
    }
}