package com.crypto.trading.sdk.exception;

import com.crypto.trading.common.exception.BaseException;
import com.crypto.trading.common.exception.ErrorCode;

/**
 * API异常类
 * 表示API调用过程中发生的异常
 */
public class ApiException extends BaseException {

    private static final long serialVersionUID = 1L;

    /**
     * 构造函数
     *
     * @param code 错误代码
     * @param message 错误消息
     */
    public ApiException(int code, String message) {
        super(code, message);
    }

    /**
     * 构造函数
     *
     * @param errorCode 错误码枚举
     * @param message 错误消息
     */
    public ApiException(ErrorCode errorCode, String message) {
        super(errorCode, message);
    }

    /**
     * 构造函数
     *
     * @param code 错误代码
     * @param message 错误消息
     * @param cause 异常原因
     */
    public ApiException(int code, String message, Throwable cause) {
        super(code, message, cause);
    }

    /**
     * 构造函数
     *
     * @param errorCode 错误码枚举
     * @param message 错误消息
     * @param cause 异常原因
     */
    public ApiException(ErrorCode errorCode, String message, Throwable cause) {
        super(errorCode, message, cause);
    }

    /**
     * 构造函数
     *
     * @param message 错误消息
     * @param cause 异常原因
     */
    public ApiException(String message, Throwable cause) {
        super(ErrorCode.BINANCE_API_ERROR, message, cause);
    }
    
    /**
     * 获取错误消息
     *
     * @return 错误消息
     */
    public String getErrorMessage() {
        return getMessage();
    }
} 