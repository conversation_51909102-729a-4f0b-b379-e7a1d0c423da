package com.crypto.trading.common.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 日志配置类
 * <p>
 * 用于配置系统的日志参数，包括日志级别、日志格式、日志文件等。
 * 配置项从application-common.yml的logging前缀节点下读取。
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Configuration
@ConfigurationProperties(prefix = "logging")
public class LoggingConfig {
    
    private static final Logger log = LoggerFactory.getLogger(LoggingConfig.class);
    
    /**
     * 日志级别配置
     */
    private Level level = new Level();
    
    /**
     * 日志格式配置
     */
    private Pattern pattern = new Pattern();
    
    /**
     * 日志文件配置
     */
    private LogFile file = new LogFile();
    
    /**
     * 当配置加载完成后的初始化操作
     */
    public void init() {
        log.info("日志配置加载完成");
        log.info("日志级别: root={}, 应用级别={}", level.getRoot(), level.getAppLevel());
        log.info("日志格式: console={}, file={}", pattern.getConsole(), pattern.getFile());
        log.info("日志文件: name={}, maxSize={}, maxHistory={}",
                file.getName(), file.getMaxSize(), file.getMaxHistory());
    }
    
    /**
     * 日志级别配置内部类
     */
    public static class Level {
        /**
         * 根日志级别
         */
        private String root = "INFO";
        
        /**
         * 应用日志级别
         */
        private String appLevel = "INFO";

        public String getRoot() {
            return root;
        }

        public void setRoot(String root) {
            this.root = root;
        }

        public String getAppLevel() {
            return appLevel;
        }

        public void setAppLevel(String appLevel) {
            this.appLevel = appLevel;
        }
    }
    
    /**
     * 日志格式配置内部类
     */
    public static class Pattern {
        /**
         * 控制台日志格式
         */
        private String console = "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n";
        
        /**
         * 文件日志格式
         */
        private String file = "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n";

        public String getConsole() {
            return console;
        }

        public void setConsole(String console) {
            this.console = console;
        }

        public String getFile() {
            return file;
        }

        public void setFile(String file) {
            this.file = file;
        }
    }
    
    /**
     * 日志文件配置内部类
     */
    public static class LogFile {
        /**
         * 日志文件名
         */
        private String name;
        
        /**
         * 单个日志文件最大大小
         */
        private String maxSize = "100MB";
        
        /**
         * 日志文件保留天数
         */
        private int maxHistory = 30;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getMaxSize() {
            return maxSize;
        }

        public void setMaxSize(String maxSize) {
            this.maxSize = maxSize;
        }

        public int getMaxHistory() {
            return maxHistory;
        }

        public void setMaxHistory(int maxHistory) {
            this.maxHistory = maxHistory;
        }
    }

    public Level getLevel() {
        return level;
    }

    public void setLevel(Level level) {
        this.level = level;
    }

    public Pattern getPattern() {
        return pattern;
    }

    public void setPattern(Pattern pattern) {
        this.pattern = pattern;
    }

    public LogFile getFile() {
        return file;
    }

    public void setFile(LogFile file) {
        this.file = file;
    }
}