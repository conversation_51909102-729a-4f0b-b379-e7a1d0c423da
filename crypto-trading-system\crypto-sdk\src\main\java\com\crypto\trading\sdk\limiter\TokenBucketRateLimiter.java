package com.crypto.trading.sdk.limiter;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

/**
 * @name: TokenBucketRateLimiter
 * @author: zeek
 * @description: 使用令牌桶算法实现的API请求限流器。
 *               这个类是线程安全的。
 * @create: 2024-07-16 05:45
 */
@Slf4j
public class TokenBucketRateLimiter implements ApiRateLimiter {

    private final long capacity; // 桶的容量
    private final long refillRate; // 每秒补充的令牌数
    private final AtomicLong currentTokens; // 当前令牌数
    private final Lock lock = new ReentrantLock();
    private ScheduledExecutorService scheduler;
    private volatile boolean isRunning = false;
    private volatile long lastRefillTime;

    /**
     * 创建一个令牌桶限流器
     *
     * @param capacity   桶的容量
     * @param refillRate 每秒补充的令牌数
     */
    public TokenBucketRateLimiter(long capacity, long refillRate) {
        this.capacity = capacity;
        this.refillRate = refillRate;
        this.currentTokens = new AtomicLong(capacity); // 初始时桶是满的
        this.lastRefillTime = System.currentTimeMillis();
        startRefillTask();
    }

    /**
     * 尝试获取一个令牌
     *
     * @return 是否成功获取令牌
     */
    @Override
    public boolean tryAcquire() {
        return tryAcquire(1);
    }

    /**
     * 尝试获取指定数量的令牌
     *
     * @param permits 要获取的令牌数
     * @return 是否成功获取令牌
     */
    @Override
    public boolean tryAcquire(int permits) {
        // 获取0或负数个令牌总是成功的
        if (permits <= 0) {
            return true;
        }

        // 获取超过容量的令牌总是失败的
        if (permits > capacity) {
            return false;
        }

        // 先尝试补充令牌
        refill();

        lock.lock();
        try {
            long current = currentTokens.get();
            if (current >= permits) {
                // 有足够的令牌，减少令牌数
                currentTokens.set(current - permits);
                log.trace("成功获取{}个令牌，剩余令牌数：{}", permits, current - permits);
                return true;
            }
            // 没有足够的令牌
            log.trace("获取{}个令牌失败，当前令牌数：{}", permits, current);
            return false;
        } finally {
            lock.unlock();
        }
    }

    /**
     * 获取一个令牌，如果没有可用的令牌，则阻塞等待
     */
    @Override
    public void acquire() {
        acquire(1);
    }

    /**
     * 获取指定数量的令牌，如果没有足够的令牌，则阻塞等待
     *
     * @param permits 要获取的令牌数
     */
    @Override
    public void acquire(int permits) {
        // 获取0或负数个令牌总是成功的
        if (permits <= 0) {
            return;
        }

        // 获取超过容量的令牌总是失败的
        if (permits > capacity) {
            throw new IllegalArgumentException("请求的令牌数超过了容量");
        }

        boolean acquired = false;
        while (!acquired) {
            acquired = tryAcquire(permits);
            if (!acquired) {
                // 等待一段时间再重试
            try {
                    TimeUnit.MILLISECONDS.sleep(100);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                    log.warn("获取令牌的等待过程被中断");
                    throw new RuntimeException("获取令牌的等待过程被中断", e);
                }
            }
        }
    }

    /**
     * 补充令牌
     */
    private void refill() {
        lock.lock();
        try {
            long now = System.currentTimeMillis();
            long elapsedTimeMs = now - lastRefillTime;
            
            // 计算需要补充的令牌数 - 修复计算逻辑
            // 使用浮点数计算以避免整除截断问题
            double elapsedSeconds = elapsedTimeMs / 1000.0;
            long tokensToAdd = Math.round(elapsedSeconds * refillRate); // 使用Math.round而不是简单的转换
            
            if (tokensToAdd > 0) {
                long current = currentTokens.get();
                long newTokens = Math.min(capacity, current + tokensToAdd);
                
                // 确保令牌被正确补充
                currentTokens.set(newTokens);
                
                log.debug("补充令牌{}个，当前数量: {}, 经过时间: {}ms", tokensToAdd, newTokens, elapsedTimeMs);
            }
            
            // 无论是否添加了令牌，都更新最后补充时间，避免时间累积问题
            lastRefillTime = now;
            
        } finally {
            lock.unlock();
        }
    }

    /**
     * 启动令牌补充任务
     */
    private void startRefillTask() {
        if (isRunning) {
            return;
        }

        isRunning = true;
        scheduler = Executors.newSingleThreadScheduledExecutor(
                new ThreadFactoryBuilder().setNameFormat("token-bucket-refill-%d").build());
        
        // 每秒执行一次补充任务
        scheduler.scheduleAtFixedRate(this::refill, 0, 1, TimeUnit.SECONDS);
    }

    /**
     * 停止令牌补充任务
     */
    public void stop() {
        if (scheduler != null && !scheduler.isShutdown()) {
            scheduler.shutdown();
            isRunning = false;
            try {
                if (!scheduler.awaitTermination(1, TimeUnit.SECONDS)) {
                    scheduler.shutdownNow();
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                scheduler.shutdownNow();
            }
        }
    }
    
    /**
     * 获取当前令牌数量（仅用于测试）
     */
    long getCurrentTokens() {
        return currentTokens.get();
    }
    
    /**
     * 强制刷新令牌（用于测试）
     */
    void forceRefill() {
        refill();
    }
}
