#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
消息处理扩展模块

包含深度和交易消息处理逻辑，
实现完整数据流：Kafka消息 → 数据处理 → 技术指标计算 → ML预测 → 信号生成
"""

import time
from typing import Dict, Any
from loguru import logger

from .infrastructure.async_error_recovery import AsyncTask


class MessageHandlersExtended:
    """
    消息处理器扩展类
    
    处理深度和交易消息，集成完整的数据流处理。
    """
    
    def __init__(self, core_handler):
        """
        初始化扩展消息处理器
        
        Args:
            core_handler: 核心消息处理器实例
        """
        self.core_handler = core_handler
        logger.info("扩展消息处理器初始化完成")
    
    async def handle_depth_message(self, message: Dict[str, Any]) -> None:
        """
        处理深度消息
        
        Args:
            message: 深度消息数据
        """
        start_time = time.time()
        
        try:
            self.core_handler.metrics_service.increment_counter("depth_messages_received")
            
            # 1. 检查缓存
            cache_key = f"depth_{message.get('symbol')}_{message.get('timestamp')}"
            cached_result = self.core_handler.cache_service.get(cache_key)
            
            if cached_result:
                logger.debug(f"使用缓存的深度处理结果: {cache_key}")
                if cached_result.get('signal'):
                    self.core_handler.kafka_service.publish_signal(cached_result['signal'])
                return
            
            # 2. 数据处理
            processed_data = self.core_handler.data_processor.process_depth_data(message)
            
            if not processed_data:
                logger.warning("深度数据处理失败，跳过后续处理")
                return
            
            # 3. 技术指标计算（深度数据相关指标）
            indicator_result = self.core_handler.technical_indicator_service.calculate_all_indicators(
                processed_data,
                symbol=message.get('symbol', 'BTCUSDT'),
                timeframe=message.get('timeframe', '1m')
            )
            
            # 4. ML预测
            prediction_task = AsyncTask(
                task_id=f"predict_depth_{message.get('symbol')}_{time.time()}",
                func=self.core_handler.prediction_engine.predict,
                kwargs={
                    'features': {
                        'processed_data': processed_data,
                        'technical_indicators': indicator_result,
                        'message_type': 'depth'
                    },
                    'symbol': message.get('symbol', 'BTCUSDT'),
                    'timeframe': message.get('timeframe', '1m')
                }
            )
            
            prediction_result = await self.core_handler.async_error_recovery.execute_task(prediction_task)
            
            # 5. 信号生成
            signal = self.core_handler.strategy_service.on_market_data({
                'processed_data': processed_data,
                'indicators': indicator_result,
                'prediction': prediction_result,
                'message_type': 'depth'
            })
            
            # 6. 缓存结果和发布信号
            result_data = {
                'processed_data': processed_data,
                'indicators': indicator_result,
                'prediction': prediction_result,
                'signal': signal
            }
            
            self.core_handler.cache_service.put(cache_key, result_data, ttl=30.0)  # 深度数据缓存时间较短
            
            if signal:
                self.core_handler.kafka_service.publish_signal(signal)
                self.core_handler.metrics_service.increment_counter("signals_generated")
            
            # 记录处理时间
            processing_time = (time.time() - start_time) * 1000
            self.core_handler.metrics_service.record_histogram("depth_processing_time_ms", processing_time)
            
            logger.debug(f"深度消息处理完成 - {message.get('symbol')}, 处理时间: {processing_time:.1f}ms")
            
        except Exception as e:
            logger.error(f"深度消息处理失败: {e}")
            self.core_handler.metrics_service.increment_counter("depth_processing_errors")
    
    async def handle_trade_message(self, message: Dict[str, Any]) -> None:
        """
        处理交易消息
        
        Args:
            message: 交易消息数据
        """
        start_time = time.time()
        
        try:
            self.core_handler.metrics_service.increment_counter("trade_messages_received")
            
            # 1. 检查缓存
            cache_key = f"trade_{message.get('symbol')}_{message.get('timestamp')}"
            cached_result = self.core_handler.cache_service.get(cache_key)
            
            if cached_result:
                logger.debug(f"使用缓存的交易处理结果: {cache_key}")
                if cached_result.get('signal'):
                    self.core_handler.kafka_service.publish_signal(cached_result['signal'])
                return
            
            # 2. 数据处理
            processed_data = self.core_handler.data_processor.process_trade_data(message)
            
            if not processed_data:
                logger.warning("交易数据处理失败，跳过后续处理")
                return
            
            # 3. 技术指标计算
            indicator_result = self.core_handler.technical_indicator_service.calculate_all_indicators(
                processed_data,
                symbol=message.get('symbol', 'BTCUSDT'),
                timeframe=message.get('timeframe', '1m')
            )
            
            # 4. ML预测
            prediction_task = AsyncTask(
                task_id=f"predict_trade_{message.get('symbol')}_{time.time()}",
                func=self.core_handler.prediction_engine.predict,
                kwargs={
                    'features': {
                        'processed_data': processed_data,
                        'technical_indicators': indicator_result,
                        'message_type': 'trade'
                    },
                    'symbol': message.get('symbol', 'BTCUSDT'),
                    'timeframe': message.get('timeframe', '1m')
                }
            )
            
            prediction_result = await self.core_handler.async_error_recovery.execute_task(prediction_task)
            
            # 5. 信号生成
            signal = self.core_handler.strategy_service.on_market_data({
                'processed_data': processed_data,
                'indicators': indicator_result,
                'prediction': prediction_result,
                'message_type': 'trade'
            })
            
            # 6. 缓存结果和发布信号
            result_data = {
                'processed_data': processed_data,
                'indicators': indicator_result,
                'prediction': prediction_result,
                'signal': signal
            }
            
            self.core_handler.cache_service.put(cache_key, result_data, ttl=45.0)
            
            if signal:
                self.core_handler.kafka_service.publish_signal(signal)
                self.core_handler.metrics_service.increment_counter("signals_generated")
            
            # 记录处理时间
            processing_time = (time.time() - start_time) * 1000
            self.core_handler.metrics_service.record_histogram("trade_processing_time_ms", processing_time)
            
            logger.debug(f"交易消息处理完成 - {message.get('symbol')}, 处理时间: {processing_time:.1f}ms")
            
        except Exception as e:
            logger.error(f"交易消息处理失败: {e}")
            self.core_handler.metrics_service.increment_counter("trade_processing_errors")