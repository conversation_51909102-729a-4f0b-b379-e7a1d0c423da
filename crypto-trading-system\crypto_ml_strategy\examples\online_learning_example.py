#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
在线学习模块使用示例

演示如何使用OnlineLearningEngine及其相关组件进行在线学习，
包括模型训练、性能监控、版本管理等完整流程。

使用场景：
1. 实时模型训练和更新
2. 自适应学习策略管理
3. 模型性能监控和告警
4. 模型版本管理和回滚
5. 与DeepSeek蒸馏模型和多时间周期数据处理的集成

作者：Crypto ML Strategy Team
版本：1.0.0
日期：2025-06-19
"""

import sys
import os
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any
import time
import json
from sklearn.ensemble import RandomForestRegressor
from sklearn.linear_model import SGDRegressor
from sklearn.metrics import mean_squared_error, r2_score

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入在线学习组件
from src.online_learning import (
    OnlineLearningEngine,
    LearningStrategyManager,
    PerformanceMonitor,
    ModelVersionManager,
    IncrementalTrainer,
    EngineStatus,
    LearningPhase,
    create_online_learning_engine,
    get_default_strategy_config,
    get_default_monitoring_config
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('online_learning_example.log')
    ]
)

logger = logging.getLogger('OnlineLearningExample')


class OnlineLearningExample:
    """在线学习模块使用示例类"""
    
    def __init__(self):
        """初始化示例"""
        self.logger = logger
        self.engine = None
        self.sample_data = {}
        self.models = {}
        
        # 示例配置
        self.config = {
            'engine': {
                'model_name': 'crypto_prediction_model',
                'learning_phase': 'initialization',
                'auto_version_management': True,
                'auto_strategy_selection': True,
                'performance_monitoring': True,
                'enable_model_persistence': True
            },
            'learning': {
                'warm_up_samples': 100,
                'min_samples_for_update': 20,
                'performance_evaluation_interval': 30,  # 30秒用于演示
                'model_save_interval': 180,             # 3分钟用于演示
                'strategy_adaptation_interval': 300     # 5分钟用于演示
            },
            'thresholds': {
                'min_performance_score': 0.6,
                'performance_degradation_threshold': 0.1,
                'model_update_threshold': 0.05,
                'version_creation_threshold': 0.15
            }
        }
        
        self.logger.info("在线学习模块使用示例初始化完成")
    
    def setup_models(self):
        """设置示例模型"""
        try:
            self.logger.info("正在设置示例模型...")
            
            # 创建支持增量学习的模型
            self.models['sgd_regressor'] = SGDRegressor(
                learning_rate='adaptive',
                eta0=0.01,
                random_state=42
            )
            
            # 创建随机森林模型（用于比较）
            self.models['random_forest'] = RandomForestRegressor(
                n_estimators=10,
                random_state=42
            )
            
            self.logger.info("示例模型设置完成")
            return True
            
        except Exception as e:
            self.logger.error(f"设置示例模型失败: {e}")
            return False
    
    def setup_engine(self):
        """设置在线学习引擎"""
        try:
            self.logger.info("正在初始化在线学习引擎...")
            
            # 创建引擎
            self.engine = create_online_learning_engine(self.config)
            
            self.logger.info("在线学习引擎初始化成功")
            return True
            
        except Exception as e:
            self.logger.error(f"在线学习引擎初始化失败: {e}")
            return False
    
    def generate_sample_data(self, samples: int = 1000) -> Dict[str, np.ndarray]:
        """
        生成示例数据
        
        Args:
            samples: 样本数量
            
        Returns:
            生成的数据字典
        """
        try:
            self.logger.info(f"正在生成 {samples} 个示例数据...")
            
            # 生成特征数据（模拟技术指标）
            np.random.seed(42)
            
            # 模拟价格相关特征
            price_trend = np.cumsum(np.random.normal(0, 0.01, samples))
            volatility = np.abs(np.random.normal(0.02, 0.005, samples))
            volume = np.random.lognormal(10, 0.5, samples)
            
            # 模拟技术指标
            rsi = 50 + 30 * np.sin(np.linspace(0, 4*np.pi, samples)) + np.random.normal(0, 5, samples)
            rsi = np.clip(rsi, 0, 100)
            
            macd = np.random.normal(0, 0.1, samples)
            bollinger_position = np.random.uniform(-1, 1, samples)
            
            # 组合特征
            features = np.column_stack([
                price_trend,
                volatility,
                np.log(volume),
                rsi / 100.0,  # 归一化
                macd,
                bollinger_position
            ])
            
            # 生成目标值（模拟未来价格变化）
            # 基于特征的线性组合加上噪声
            true_weights = np.array([0.5, -0.3, 0.1, 0.2, 0.4, 0.3])
            targets = np.dot(features, true_weights) + np.random.normal(0, 0.05, samples)
            
            # 生成时间戳
            start_time = datetime.now() - timedelta(hours=samples//60)
            timestamps = [start_time + timedelta(minutes=i) for i in range(samples)]
            
            data = {
                'features': features,
                'targets': targets,
                'timestamps': timestamps,
                'feature_names': ['price_trend', 'volatility', 'log_volume', 'rsi', 'macd', 'bollinger_pos']
            }
            
            self.sample_data = data
            self.logger.info(f"示例数据生成完成: 特征维度={features.shape}, 目标维度={targets.shape}")
            return data
            
        except Exception as e:
            self.logger.error(f"生成示例数据失败: {e}")
            return {}
    
    def demonstrate_basic_online_learning(self) -> bool:
        """演示基础在线学习流程"""
        try:
            self.logger.info("=== 基础在线学习流程演示 ===")
            
            # 启动引擎
            initial_model = self.models['sgd_regressor']
            success = self.engine.start_engine(initial_model)
            
            if not success:
                self.logger.error("引擎启动失败")
                return False
            
            # 获取数据
            data = self.sample_data
            features = data['features']
            targets = data['targets']
            timestamps = data['timestamps']
            
            # 分批处理数据（模拟实时数据流）
            batch_size = 50
            total_batches = len(features) // batch_size
            
            self.logger.info(f"开始处理 {total_batches} 个批次的数据...")
            
            for i in range(total_batches):
                start_idx = i * batch_size
                end_idx = start_idx + batch_size
                
                batch_features = features[start_idx:end_idx]
                batch_targets = targets[start_idx:end_idx]
                batch_timestamps = timestamps[start_idx:end_idx]
                
                # 处理数据
                success = self.engine.process_new_data(
                    batch_features, 
                    batch_targets, 
                    batch_timestamps,
                    {'batch_id': i, 'source': 'demo'}
                )
                
                if success:
                    self.logger.info(f"批次 {i+1}/{total_batches} 处理成功")
                else:
                    self.logger.warning(f"批次 {i+1}/{total_batches} 处理失败")
                
                # 短暂等待（模拟实时处理间隔）
                time.sleep(0.1)
                
                # 每10个批次显示一次状态
                if (i + 1) % 10 == 0:
                    self._display_engine_status()
            
            self.logger.info("基础在线学习流程演示完成")
            return True
            
        except Exception as e:
            self.logger.error(f"基础在线学习流程演示失败: {e}")
            return False
    
    def demonstrate_strategy_management(self) -> bool:
        """演示学习策略管理"""
        try:
            self.logger.info("=== 学习策略管理演示 ===")
            
            # 获取策略管理器
            strategy_manager = self.engine.strategy_manager
            
            # 显示当前策略
            current_strategy = strategy_manager.get_current_strategy()
            if current_strategy:
                self.logger.info(f"当前策略: {current_strategy.name}")
                self.logger.info(f"学习率: {current_strategy.learning_rate}")
                self.logger.info(f"批次大小: {current_strategy.batch_size}")
                self.logger.info(f"更新频率: {current_strategy.update_frequency}秒")
            
            # 模拟市场条件变化
            from src.online_learning.learning_strategy import MarketCondition
            
            market_conditions = [
                MarketCondition.STABLE,
                MarketCondition.VOLATILE,
                MarketCondition.TRENDING,
                MarketCondition.RANGING
            ]
            
            for condition in market_conditions:
                self.logger.info(f"\n模拟市场条件: {condition.value}")
                
                # 选择适合的策略
                selected_strategy = strategy_manager.select_strategy(condition)
                
                if selected_strategy:
                    self.logger.info(f"选择的策略: {selected_strategy}")
                    
                    # 获取策略配置
                    strategy_config = strategy_manager.get_current_strategy()
                    if strategy_config:
                        self.logger.info(f"策略配置: 学习率={strategy_config.learning_rate}, "
                                       f"批次大小={strategy_config.batch_size}")
                
                time.sleep(1)  # 短暂等待
            
            # 显示策略统计
            strategy_stats = strategy_manager.get_strategy_statistics()
            self.logger.info(f"\n策略统计:")
            self.logger.info(f"策略切换次数: {strategy_stats['stats']['strategy_switches']}")
            self.logger.info(f"自适应调整次数: {strategy_stats['stats']['adaptations_performed']}")
            self.logger.info(f"当前市场条件: {strategy_stats['current_market_condition']}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"学习策略管理演示失败: {e}")
            return False
    
    def demonstrate_performance_monitoring(self) -> bool:
        """演示性能监控"""
        try:
            self.logger.info("=== 性能监控演示 ===")
            
            # 获取性能监控器
            performance_monitor = self.engine.performance_monitor
            
            # 等待一段时间让监控器收集数据
            self.logger.info("等待性能数据收集...")
            time.sleep(5)
            
            # 获取当前性能分数
            current_score = performance_monitor.get_current_performance_score()
            self.logger.info(f"当前性能分数: {current_score:.3f}")
            
            # 获取性能趋势
            accuracy_trend = performance_monitor.get_performance_trend('model_accuracy', 1)
            if accuracy_trend:
                self.logger.info(f"准确率趋势 (最近{len(accuracy_trend)}个点): "
                               f"平均={np.mean(accuracy_trend):.3f}, "
                               f"最新={accuracy_trend[-1]:.3f}")
            
            # 生成性能报告
            report = performance_monitor.generate_performance_report(1)  # 最近1小时
            if report:
                self.logger.info(f"\n性能报告 (ID: {report.report_id}):")
                self.logger.info(f"时间范围: {report.start_time} - {report.end_time}")
                self.logger.info(f"总体分数: {report.overall_score:.3f}")
                self.logger.info(f"告警数量: {len(report.alerts)}")
                
                if report.recommendations:
                    self.logger.info("改进建议:")
                    for rec in report.recommendations:
                        self.logger.info(f"  - {rec}")
            
            # 获取监控统计
            monitoring_stats = performance_monitor.get_monitoring_statistics()
            self.logger.info(f"\n监控统计:")
            self.logger.info(f"监控活跃: {monitoring_stats['monitoring_active']}")
            self.logger.info(f"当前指标数量: {monitoring_stats['current_metrics_count']}")
            self.logger.info(f"活跃告警数量: {monitoring_stats['active_alerts_count']}")
            self.logger.info(f"总指标收集次数: {monitoring_stats['stats']['total_metrics_collected']}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"性能监控演示失败: {e}")
            return False
    
    def demonstrate_version_management(self) -> bool:
        """演示模型版本管理"""
        try:
            self.logger.info("=== 模型版本管理演示 ===")
            
            # 获取版本管理器
            version_manager = self.engine.version_manager
            
            # 创建一个新的模型版本
            test_model = self.models['random_forest']
            
            # 训练模型以获取性能指标
            data = self.sample_data
            train_size = len(data['features']) // 2
            train_features = data['features'][:train_size]
            train_targets = data['targets'][:train_size]
            test_features = data['features'][train_size:]
            test_targets = data['targets'][train_size:]
            
            test_model.fit(train_features, train_targets)
            predictions = test_model.predict(test_features)
            
            # 计算性能指标
            mse = mean_squared_error(test_targets, predictions)
            r2 = r2_score(test_targets, predictions)
            
            performance_metrics = {
                'mse': float(mse),
                'r2_score': float(r2),
                'accuracy': float(max(0, r2))  # 使用R²作为准确率的代理
            }
            
            hyperparameters = {
                'n_estimators': test_model.n_estimators,
                'random_state': test_model.random_state
            }
            
            # 保存模型版本
            version_id = version_manager.save_model_version(
                model=test_model,
                model_name='demo_random_forest',
                performance_metrics=performance_metrics,
                hyperparameters=hyperparameters,
                training_samples=train_size,
                training_duration=1.0,
                tags=['demo', 'random_forest'],
                description="演示用随机森林模型"
            )
            
            if version_id:
                self.logger.info(f"新模型版本已保存: {version_id}")
                
                # 获取版本信息
                if version_id in version_manager.versions:
                    metadata = version_manager.versions[version_id]
                    self.logger.info(f"版本元数据:")
                    self.logger.info(f"  创建时间: {metadata.creation_time}")
                    self.logger.info(f"  训练样本: {metadata.training_samples}")
                    self.logger.info(f"  模型大小: {metadata.model_size_mb:.2f} MB")
                    self.logger.info(f"  性能指标: {metadata.performance_metrics}")
                    self.logger.info(f"  标签: {metadata.tags}")
            
            # 显示所有版本
            version_stats = version_manager.get_version_statistics()
            self.logger.info(f"\n版本统计:")
            self.logger.info(f"总版本数: {version_stats['total_versions']}")
            self.logger.info(f"活跃版本: {version_stats['active_version']}")
            self.logger.info(f"存储大小: {version_stats['storage_info']['total_size_mb']:.2f} MB")
            
            # 如果有多个版本，演示版本比较
            if len(version_manager.versions) >= 2:
                version_list = list(version_manager.versions.keys())
                if len(version_list) >= 2:
                    comparison = version_manager.compare_versions(
                        version_list[0], version_list[1]
                    )
                    
                    if comparison:
                        self.logger.info(f"\n版本比较:")
                        self.logger.info(f"版本A: {comparison.version_a}")
                        self.logger.info(f"版本B: {comparison.version_b}")
                        self.logger.info(f"更好的版本: {comparison.better_version}")
                        self.logger.info(f"建议: {comparison.recommendation}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"模型版本管理演示失败: {e}")
            return False
    
    def demonstrate_integration_with_existing_modules(self) -> bool:
        """演示与现有模块的集成"""
        try:
            self.logger.info("=== 与现有模块集成演示 ===")
            
            # 模拟与多时间周期数据处理的集成
            self.logger.info("1. 与多时间周期数据处理集成:")
            
            # 模拟多时间周期特征
            timeframes = ['1m', '5m', '15m', '1h']
            multi_timeframe_features = {}
            
            for tf in timeframes:
                # 为每个时间周期生成特征
                tf_features = np.random.normal(0, 1, (100, 3))  # 100样本，3个特征
                multi_timeframe_features[tf] = tf_features
                self.logger.info(f"  {tf} 时间周期特征: {tf_features.shape}")
            
            # 模拟特征融合
            fused_features = np.concatenate([
                multi_timeframe_features[tf] for tf in timeframes
            ], axis=1)
            
            self.logger.info(f"  融合后特征维度: {fused_features.shape}")
            
            # 模拟与技术指标模块的集成
            self.logger.info("\n2. 与技术指标模块集成:")
            
            technical_indicators = {
                'LPPL': np.random.uniform(0, 1, 100),
                'Hematread': np.random.uniform(-1, 1, 100),
                'BMSB': np.random.uniform(0, 1, 100),
                'SuperTrend': np.random.choice([0, 1], 100)
            }
            
            for indicator, values in technical_indicators.items():
                self.logger.info(f"  {indicator}: 均值={np.mean(values):.3f}, "
                               f"标准差={np.std(values):.3f}")
            
            # 模拟与DeepSeek蒸馏模型的集成
            self.logger.info("\n3. 与DeepSeek蒸馏模型集成:")
            
            # 模拟蒸馏学习过程
            teacher_predictions = np.random.normal(0, 1, 100)
            student_predictions = teacher_predictions + np.random.normal(0, 0.1, 100)
            
            distillation_loss = np.mean((teacher_predictions - student_predictions) ** 2)
            self.logger.info(f"  蒸馏损失: {distillation_loss:.4f}")
            self.logger.info(f"  教师模型预测范围: [{teacher_predictions.min():.3f}, {teacher_predictions.max():.3f}]")
            self.logger.info(f"  学生模型预测范围: [{student_predictions.min():.3f}, {student_predictions.max():.3f}]")
            
            # 模拟Kafka消息处理
            self.logger.info("\n4. Kafka消息处理集成:")
            
            kafka_message_example = {
                "symbol": "BTCUSDT",
                "timestamp": int(time.time() * 1000),
                "timeframe": "1m",
                "features": {
                    "price_data": [50000.0, 50100.0, 49900.0, 50050.0],
                    "volume": 125.5,
                    "technical_indicators": {
                        "rsi": 65.2,
                        "macd": 0.15,
                        "bollinger_position": 0.3
                    }
                },
                "target": 0.02  # 预期价格变化
            }
            
            self.logger.info("  Kafka消息示例:")
            self.logger.info(f"    交易对: {kafka_message_example['symbol']}")
            self.logger.info(f"    时间周期: {kafka_message_example['timeframe']}")
            self.logger.info(f"    特征数量: {len(kafka_message_example['features'])}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"与现有模块集成演示失败: {e}")
            return False
    
    def _display_engine_status(self):
        """显示引擎状态"""
        try:
            status = self.engine.get_engine_status()
            
            self.logger.info(f"引擎状态: {status['status']}")
            self.logger.info(f"学习阶段: {status['current_phase']}")
            self.logger.info(f"已处理样本: {status['stats']['total_samples_processed']}")
            self.logger.info(f"模型更新次数: {status['stats']['total_model_updates']}")
            self.logger.info(f"最新性能分数: {status['stats']['last_performance_score']:.3f}")
            
        except Exception as e:
            self.logger.error(f"显示引擎状态失败: {e}")
    
    def cleanup(self):
        """清理资源"""
        try:
            if self.engine:
                self.engine.stop_engine()
            self.logger.info("资源清理完成")
            
        except Exception as e:
            self.logger.error(f"资源清理失败: {e}")
    
    def run_complete_example(self):
        """运行完整示例"""
        try:
            self.logger.info("开始运行在线学习模块完整示例")
            self.logger.info("=" * 60)
            
            # 1. 设置模型
            if not self.setup_models():
                return False
            
            # 2. 设置引擎
            if not self.setup_engine():
                return False
            
            # 3. 生成示例数据
            if not self.generate_sample_data(500):  # 减少样本数量以加快演示
                return False
            
            # 4. 演示基础在线学习
            if not self.demonstrate_basic_online_learning():
                return False
            
            # 5. 演示策略管理
            if not self.demonstrate_strategy_management():
                return False
            
            # 6. 演示性能监控
            if not self.demonstrate_performance_monitoring():
                return False
            
            # 7. 演示版本管理
            if not self.demonstrate_version_management():
                return False
            
            # 8. 演示模块集成
            if not self.demonstrate_integration_with_existing_modules():
                return False
            
            # 9. 最终状态显示
            self.logger.info("=" * 60)
            self.logger.info("最终引擎状态:")
            self._display_engine_status()
            
            self.logger.info("=" * 60)
            self.logger.info("在线学习模块完整示例运行成功!")
            
            return True
            
        except Exception as e:
            self.logger.error(f"运行完整示例失败: {e}")
            return False
        
        finally:
            # 清理资源
            self.cleanup()


def main():
    """主函数"""
    try:
        print("在线学习模块使用示例")
        print("=" * 50)
        
        # 创建示例实例
        example = OnlineLearningExample()
        
        # 运行完整示例
        success = example.run_complete_example()
        
        if success:
            print("\n✅ 示例运行成功!")
            print("请查看日志文件 'online_learning_example.log' 获取详细信息")
        else:
            print("\n❌ 示例运行失败!")
            print("请查看日志文件了解错误详情")
        
        return 0 if success else 1
        
    except Exception as e:
        print(f"示例运行出错: {e}")
        return 1


if __name__ == "__main__":
    exit(main())