"""
仓位管理器模块

实现智能仓位计算和管理功能，支持多种仓位计算方法，
包括固定比例、<PERSON>公式、风险平价、波动率调整等策略。

Author: Crypto ML Strategy Team
Date: 2024-12-19
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime
import logging
import math

from .risk_config import RiskConfig, PositionInfo, PositionSizeMethod, RiskLevel

logger = logging.getLogger(__name__)


class PositionManager:
    """仓位管理器类"""
    
    def __init__(self, config: RiskConfig):
        """
        初始化仓位管理器
        
        Args:
            config: 风险管理配置
        """
        self.config = config
        self.current_positions: Dict[str, PositionInfo] = {}
        self.account_balance: float = 0.0
        self.available_balance: float = 0.0
        self.total_exposure: float = 0.0
        
        logger.info("Position manager initialized")
    
    def update_account_info(self, balance: float, available_balance: float) -> None:
        """
        更新账户信息
        
        Args:
            balance: 总余额
            available_balance: 可用余额
        """
        try:
            self.account_balance = balance
            self.available_balance = available_balance
            
            # 重新计算总敞口
            self.total_exposure = sum(
                abs(pos.position_value) for pos in self.current_positions.values()
            )
            
            logger.debug(f"Updated account info - Balance: {balance}, Available: {available_balance}")
            
        except Exception as e:
            logger.error(f"Error updating account info: {e}")
            raise
    
    def update_position(self, symbol: str, position_info: PositionInfo) -> None:
        """
        更新仓位信息
        
        Args:
            symbol: 交易对符号
            position_info: 仓位信息
        """
        try:
            self.current_positions[symbol] = position_info
            
            # 重新计算总敞口
            self.total_exposure = sum(
                abs(pos.position_value) for pos in self.current_positions.values()
            )
            
            logger.debug(f"Updated position for {symbol}")
            
        except Exception as e:
            logger.error(f"Error updating position for {symbol}: {e}")
            raise
    
    def calculate_position_size(
        self,
        symbol: str,
        entry_price: float,
        stop_loss_price: Optional[float] = None,
        market_data: Optional[Dict[str, Any]] = None,
        signal_confidence: float = 1.0
    ) -> float:
        """
        计算建议仓位大小
        
        Args:
            symbol: 交易对符号
            entry_price: 入场价格
            stop_loss_price: 止损价格
            market_data: 市场数据（包含波动率、收益率等）
            signal_confidence: 信号置信度 (0-1)
            
        Returns:
            建议仓位大小（基础货币数量）
        """
        try:
            if self.account_balance <= 0:
                logger.warning("Account balance is zero or negative")
                return 0.0
            
            # 根据配置的方法计算仓位
            if self.config.position_size_method == PositionSizeMethod.FIXED_RATIO:
                position_size = self._calculate_fixed_ratio_position(entry_price, signal_confidence)
            
            elif self.config.position_size_method == PositionSizeMethod.KELLY_FORMULA:
                position_size = self._calculate_kelly_position(
                    symbol, entry_price, stop_loss_price, market_data, signal_confidence
                )
            
            elif self.config.position_size_method == PositionSizeMethod.RISK_PARITY:
                position_size = self._calculate_risk_parity_position(
                    symbol, entry_price, market_data, signal_confidence
                )
            
            elif self.config.position_size_method == PositionSizeMethod.VOLATILITY_ADJUSTED:
                position_size = self._calculate_volatility_adjusted_position(
                    symbol, entry_price, market_data, signal_confidence
                )
            
            elif self.config.position_size_method == PositionSizeMethod.ADAPTIVE:
                position_size = self._calculate_adaptive_position(
                    symbol, entry_price, stop_loss_price, market_data, signal_confidence
                )
            
            else:
                position_size = self._calculate_fixed_ratio_position(entry_price, signal_confidence)
            
            # 应用风险限制
            position_size = self._apply_risk_limits(symbol, position_size, entry_price)
            
            logger.info(f"Calculated position size for {symbol}: {position_size}")
            
            return position_size
            
        except Exception as e:
            logger.error(f"Error calculating position size for {symbol}: {e}")
            return 0.0
    
    def _calculate_fixed_ratio_position(self, entry_price: float, signal_confidence: float) -> float:
        """计算固定比例仓位"""
        try:
            # 基础仓位比例
            base_ratio = self.config.default_position_ratio
            
            # 根据信号置信度调整
            adjusted_ratio = base_ratio * signal_confidence
            
            # 计算仓位大小
            position_value = self.account_balance * adjusted_ratio
            position_size = position_value / entry_price
            
            return position_size
            
        except Exception as e:
            logger.error(f"Error calculating fixed ratio position: {e}")
            return 0.0
    
    def _calculate_kelly_position(
        self,
        symbol: str,
        entry_price: float,
        stop_loss_price: Optional[float],
        market_data: Optional[Dict[str, Any]],
        signal_confidence: float
    ) -> float:
        """计算Kelly公式仓位"""
        try:
            if not market_data or not stop_loss_price:
                # 如果没有足够数据，回退到固定比例
                return self._calculate_fixed_ratio_position(entry_price, signal_confidence)
            
            # 获取历史胜率和平均收益
            win_rate = market_data.get('win_rate', 0.5)
            avg_win = market_data.get('avg_win', 0.02)
            avg_loss = market_data.get('avg_loss', 0.01)
            
            # 计算赔率
            if avg_loss <= 0:
                avg_loss = abs(entry_price - stop_loss_price) / entry_price
            
            odds = avg_win / avg_loss if avg_loss > 0 else 1.0
            
            # Kelly公式: f = (bp - q) / b
            # b = 赔率, p = 胜率, q = 败率
            kelly_fraction = (odds * win_rate - (1 - win_rate)) / odds
            
            # 限制Kelly比例（通常使用1/4 Kelly）
            kelly_fraction = max(0, min(kelly_fraction * 0.25, self.config.max_single_position_ratio))
            
            # 根据信号置信度调整
            adjusted_fraction = kelly_fraction * signal_confidence
            
            # 计算仓位大小
            position_value = self.account_balance * adjusted_fraction
            position_size = position_value / entry_price
            
            return position_size
            
        except Exception as e:
            logger.error(f"Error calculating Kelly position: {e}")
            return self._calculate_fixed_ratio_position(entry_price, signal_confidence)
    
    def _calculate_risk_parity_position(
        self,
        symbol: str,
        entry_price: float,
        market_data: Optional[Dict[str, Any]],
        signal_confidence: float
    ) -> float:
        """计算风险平价仓位"""
        try:
            if not market_data:
                return self._calculate_fixed_ratio_position(entry_price, signal_confidence)
            
            # 获取波动率
            volatility = market_data.get('volatility', 0.2)
            
            # 目标风险贡献
            target_risk_contribution = self.config.default_position_ratio
            
            # 根据波动率调整仓位
            # 风险贡献 = 仓位权重 * 波动率
            # 仓位权重 = 目标风险贡献 / 波动率
            position_weight = target_risk_contribution / volatility if volatility > 0 else target_risk_contribution
            
            # 限制最大权重
            position_weight = min(position_weight, self.config.max_single_position_ratio)
            
            # 根据信号置信度调整
            adjusted_weight = position_weight * signal_confidence
            
            # 计算仓位大小
            position_value = self.account_balance * adjusted_weight
            position_size = position_value / entry_price
            
            return position_size
            
        except Exception as e:
            logger.error(f"Error calculating risk parity position: {e}")
            return self._calculate_fixed_ratio_position(entry_price, signal_confidence)
    
    def _calculate_volatility_adjusted_position(
        self,
        symbol: str,
        entry_price: float,
        market_data: Optional[Dict[str, Any]],
        signal_confidence: float
    ) -> float:
        """计算波动率调整仓位"""
        try:
            if not market_data:
                return self._calculate_fixed_ratio_position(entry_price, signal_confidence)
            
            # 获取波动率
            volatility = market_data.get('volatility', 0.2)
            
            # 基准波动率（用于标准化）
            base_volatility = 0.2
            
            # 波动率调整因子（反向调整）
            volatility_factor = base_volatility / volatility if volatility > 0 else 1.0
            
            # 限制调整因子范围
            volatility_factor = max(0.5, min(volatility_factor, 2.0))
            
            # 基础仓位比例
            base_ratio = self.config.default_position_ratio
            
            # 调整后的仓位比例
            adjusted_ratio = base_ratio * volatility_factor * signal_confidence
            
            # 计算仓位大小
            position_value = self.account_balance * adjusted_ratio
            position_size = position_value / entry_price
            
            return position_size
            
        except Exception as e:
            logger.error(f"Error calculating volatility adjusted position: {e}")
            return self._calculate_fixed_ratio_position(entry_price, signal_confidence)
    
    def _calculate_adaptive_position(
        self,
        symbol: str,
        entry_price: float,
        stop_loss_price: Optional[float],
        market_data: Optional[Dict[str, Any]],
        signal_confidence: float
    ) -> float:
        """计算自适应仓位"""
        try:
            # 综合多种方法
            positions = []
            
            # 固定比例方法
            fixed_pos = self._calculate_fixed_ratio_position(entry_price, signal_confidence)
            positions.append(fixed_pos)
            
            # 如果有足够数据，使用其他方法
            if market_data:
                # 波动率调整方法
                vol_pos = self._calculate_volatility_adjusted_position(
                    symbol, entry_price, market_data, signal_confidence
                )
                positions.append(vol_pos)
                
                # 风险平价方法
                risk_parity_pos = self._calculate_risk_parity_position(
                    symbol, entry_price, market_data, signal_confidence
                )
                positions.append(risk_parity_pos)
                
                # Kelly方法（如果有止损价格）
                if stop_loss_price:
                    kelly_pos = self._calculate_kelly_position(
                        symbol, entry_price, stop_loss_price, market_data, signal_confidence
                    )
                    positions.append(kelly_pos)
            
            # 取平均值或中位数
            if len(positions) > 1:
                # 使用中位数减少极端值影响
                adaptive_position = np.median(positions)
            else:
                adaptive_position = positions[0]
            
            return adaptive_position
            
        except Exception as e:
            logger.error(f"Error calculating adaptive position: {e}")
            return self._calculate_fixed_ratio_position(entry_price, signal_confidence)
    
    def _apply_risk_limits(self, symbol: str, position_size: float, entry_price: float) -> float:
        """应用风险限制"""
        try:
            # 计算仓位价值
            position_value = position_size * entry_price
            
            # 检查单笔仓位限制
            max_single_value = self.account_balance * self.config.max_single_position_ratio
            if position_value > max_single_value:
                position_size = max_single_value / entry_price
                logger.info(f"Position size limited by max single position ratio for {symbol}")
            
            # 检查总仓位限制
            current_exposure = self.total_exposure
            new_total_exposure = current_exposure + position_value
            max_total_value = self.account_balance * self.config.max_total_position_ratio
            
            if new_total_exposure > max_total_value:
                available_exposure = max_total_value - current_exposure
                if available_exposure > 0:
                    position_size = available_exposure / entry_price
                else:
                    position_size = 0.0
                logger.info(f"Position size limited by max total position ratio for {symbol}")
            
            # 检查可用余额
            if position_value > self.available_balance:
                position_size = self.available_balance / entry_price
                logger.info(f"Position size limited by available balance for {symbol}")
            
            # 确保仓位大小为正数
            position_size = max(0.0, position_size)
            
            return position_size
            
        except Exception as e:
            logger.error(f"Error applying risk limits for {symbol}: {e}")
            return 0.0
    
    def get_position_info(self, symbol: str) -> Optional[PositionInfo]:
        """获取仓位信息"""
        return self.current_positions.get(symbol)
    
    def get_all_positions(self) -> Dict[str, PositionInfo]:
        """获取所有仓位信息"""
        return self.current_positions.copy()
    
    def calculate_portfolio_exposure(self) -> Dict[str, float]:
        """计算投资组合敞口分布"""
        try:
            if self.account_balance <= 0:
                return {}
            
            exposure_ratios = {}
            for symbol, position in self.current_positions.items():
                exposure_ratio = abs(position.position_value) / self.account_balance
                exposure_ratios[symbol] = exposure_ratio
            
            return exposure_ratios
            
        except Exception as e:
            logger.error(f"Error calculating portfolio exposure: {e}")
            return {}
    
    def check_position_limits(self) -> List[Dict[str, Any]]:
        """检查仓位限制违规"""
        violations = []
        
        try:
            # 检查单笔仓位限制
            for symbol, position in self.current_positions.items():
                exposure_ratio = abs(position.position_value) / self.account_balance if self.account_balance > 0 else 0
                
                if exposure_ratio > self.config.max_single_position_ratio:
                    violations.append({
                        'type': 'single_position_limit',
                        'symbol': symbol,
                        'current_ratio': exposure_ratio,
                        'limit_ratio': self.config.max_single_position_ratio,
                        'severity': 'high'
                    })
            
            # 检查总仓位限制
            total_exposure_ratio = self.total_exposure / self.account_balance if self.account_balance > 0 else 0
            if total_exposure_ratio > self.config.max_total_position_ratio:
                violations.append({
                    'type': 'total_position_limit',
                    'current_ratio': total_exposure_ratio,
                    'limit_ratio': self.config.max_total_position_ratio,
                    'severity': 'high'
                })
            
            logger.info(f"Position limit check completed, {len(violations)} violations found")
            
        except Exception as e:
            logger.error(f"Error checking position limits: {e}")
        
        return violations
    
    def suggest_position_adjustments(self) -> List[Dict[str, Any]]:
        """建议仓位调整"""
        suggestions = []
        
        try:
            violations = self.check_position_limits()
            
            for violation in violations:
                if violation['type'] == 'single_position_limit':
                    symbol = violation['symbol']
                    current_position = self.current_positions.get(symbol)
                    
                    if current_position:
                        # 计算建议的新仓位大小
                        max_allowed_value = self.account_balance * self.config.max_single_position_ratio
                        suggested_size = max_allowed_value / current_position.current_price
                        
                        suggestions.append({
                            'type': 'reduce_position',
                            'symbol': symbol,
                            'current_size': current_position.current_size,
                            'suggested_size': suggested_size,
                            'reason': 'Single position limit exceeded'
                        })
                
                elif violation['type'] == 'total_position_limit':
                    # 建议按比例减少所有仓位
                    target_ratio = self.config.max_total_position_ratio * 0.9  # 留10%缓冲
                    reduction_factor = target_ratio / violation['current_ratio']
                    
                    for symbol, position in self.current_positions.items():
                        suggested_size = position.current_size * reduction_factor
                        
                        suggestions.append({
                            'type': 'reduce_position',
                            'symbol': symbol,
                            'current_size': position.current_size,
                            'suggested_size': suggested_size,
                            'reason': 'Total position limit exceeded'
                        })
            
            logger.info(f"Generated {len(suggestions)} position adjustment suggestions")
            
        except Exception as e:
            logger.error(f"Error generating position adjustment suggestions: {e}")
        
        return suggestions
    
    def get_position_summary(self) -> Dict[str, Any]:
        """获取仓位摘要"""
        try:
            total_positions = len(self.current_positions)
            total_exposure_ratio = self.total_exposure / self.account_balance if self.account_balance > 0 else 0
            
            # 计算最大单笔仓位比例
            max_single_exposure = 0.0
            if self.current_positions and self.account_balance > 0:
                max_single_exposure = max(
                    abs(pos.position_value) / self.account_balance 
                    for pos in self.current_positions.values()
                )
            
            return {
                'total_positions': total_positions,
                'account_balance': self.account_balance,
                'available_balance': self.available_balance,
                'total_exposure': self.total_exposure,
                'total_exposure_ratio': total_exposure_ratio,
                'max_single_exposure_ratio': max_single_exposure,
                'position_method': self.config.position_size_method.value,
                'within_limits': total_exposure_ratio <= self.config.max_total_position_ratio
            }
            
        except Exception as e:
            logger.error(f"Error getting position summary: {e}")
            return {'status': 'error', 'message': str(e)}
