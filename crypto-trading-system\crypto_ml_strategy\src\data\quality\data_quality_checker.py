#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据质量检查器

处理多时间周期数据的质量检查，包括数据完整性验证、异常值检测、缺失值处理、
数据同步验证和质量评分机制。确保数据质量满足机器学习模型训练和预测的要求。
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any, Union, Set
from datetime import datetime, timedelta
import threading
from collections import defaultdict, deque
from dataclasses import dataclass
import time
from enum import Enum
import warnings


class QualityLevel(Enum):
    """数据质量等级枚举"""
    EXCELLENT = "excellent"  # 优秀 (90-100%)
    GOOD = "good"           # 良好 (80-90%)
    FAIR = "fair"           # 一般 (70-80%)
    POOR = "poor"           # 较差 (60-70%)
    CRITICAL = "critical"   # 严重 (<60%)


class FillStrategy(Enum):
    """缺失值填充策略枚举"""
    FORWARD_FILL = "forward_fill"    # 前向填充
    BACKWARD_FILL = "backward_fill"  # 后向填充
    LINEAR = "linear"                # 线性插值
    MEAN = "mean"                    # 均值填充
    MEDIAN = "median"                # 中位数填充
    DROP = "drop"                    # 删除缺失值


@dataclass
class QualityMetrics:
    """数据质量指标"""
    completeness_score: float      # 完整性分数 (0-1)
    consistency_score: float       # 一致性分数 (0-1)
    accuracy_score: float          # 准确性分数 (0-1)
    timeliness_score: float        # 时效性分数 (0-1)
    overall_score: float           # 总体质量分数 (0-1)
    quality_level: QualityLevel    # 质量等级
    issues_count: int              # 问题数量
    recommendations: List[str]     # 改进建议


@dataclass
class QualityIssue:
    """数据质量问题"""
    issue_type: str                # 问题类型
    severity: str                  # 严重程度 (low, medium, high, critical)
    description: str               # 问题描述
    affected_columns: List[str]    # 受影响的列
    affected_rows: int             # 受影响的行数
    suggestion: str                # 修复建议
    timestamp: datetime            # 发现时间


class DataQualityChecker:
    """数据质量检查器"""
    
    def __init__(self, config: Dict = None):
        """
        初始化数据质量检查器
        
        Args:
            config: 配置参数字典
        """
        self.logger = logging.getLogger('cryptoMlStrategy.data.quality_checker')
        
        # 默认配置
        self.config = {
            'completeness': {
                'min_completeness_ratio': 0.95,
                'critical_completeness_ratio': 0.8,
                'required_columns': ['open', 'high', 'low', 'close', 'volume'],
                'optional_columns': ['timestamp', 'datetime']
            },
            'consistency': {
                'price_consistency_check': True,
                'volume_consistency_check': True,
                'time_consistency_check': True,
                'cross_timeframe_check': True
            },
            'accuracy': {
                'outlier_detection_method': 'iqr',
                'outlier_threshold': 3.0,
                'price_range_check': True,
                'volume_range_check': True,
                'technical_validation': True
            },
            'timeliness': {
                'max_delay_seconds': 300,
                'expected_frequency_seconds': 60,
                'gap_tolerance_ratio': 0.1
            },
            'quality_scoring': {
                'completeness_weight': 0.3,
                'consistency_weight': 0.25,
                'accuracy_weight': 0.25,
                'timeliness_weight': 0.2
            },
            'auto_fix': {
                'enable_auto_fix': True,
                'default_fill_strategy': 'forward_fill',
                'max_auto_fix_ratio': 0.05,
                'backup_before_fix': True
            }
        }
        
        # 更新用户配置
        if config:
            self._deep_update(self.config, config)
        
        # 支持的时间周期
        self.supported_timeframes = ['1m', '5m', '15m', '1h', '4h', '1d']
        
        # 质量检查历史
        self.quality_history = defaultdict(list)
        
        # 问题记录
        self.issues_log = []
        
        # 统计信息
        self.stats = {
            'total_checks': 0,
            'passed_checks': 0,
            'failed_checks': 0,
            'auto_fixes_applied': 0,
            'last_check_time': None
        }
        
        # 线程锁
        self.check_lock = threading.RLock()
        
        self.logger.info("数据质量检查器初始化完成")
    
    def _deep_update(self, base_dict: Dict, update_dict: Dict):
        """深度更新字典"""
        for key, value in update_dict.items():
            if key in base_dict and isinstance(base_dict[key], dict) and isinstance(value, dict):
                self._deep_update(base_dict[key], value)
            else:
                base_dict[key] = value
    
    def check_data_quality(self, data: pd.DataFrame, symbol: str = None, 
                          timeframe: str = None) -> QualityMetrics:
        """
        执行完整的数据质量检查
        
        Args:
            data: 要检查的数据DataFrame
            symbol: 交易对符号
            timeframe: 时间周期
            
        Returns:
            质量检查结果
        """
        try:
            with self.check_lock:
                self.stats['total_checks'] += 1
                self.stats['last_check_time'] = datetime.now()
                
                if data is None or data.empty:
                    self.logger.warning("数据为空，无法进行质量检查")
                    return self._create_empty_metrics()
                
                # 清空之前的问题记录
                self.issues_log.clear()
                
                # 执行各项质量检查
                completeness_score = self._check_completeness(data)
                consistency_score = self._check_consistency(data, timeframe)
                accuracy_score = self._check_accuracy(data)
                timeliness_score = self._check_timeliness(data, timeframe)
                
                # 计算总体质量分数
                weights = self.config['quality_scoring']
                overall_score = (
                    completeness_score * weights['completeness_weight'] +
                    consistency_score * weights['consistency_weight'] +
                    accuracy_score * weights['accuracy_weight'] +
                    timeliness_score * weights['timeliness_weight']
                )
                
                # 确定质量等级
                quality_level = self._determine_quality_level(overall_score)
                
                # 生成改进建议
                recommendations = self._generate_recommendations(
                    completeness_score, consistency_score, 
                    accuracy_score, timeliness_score
                )
                
                # 创建质量指标对象
                metrics = QualityMetrics(
                    completeness_score=completeness_score,
                    consistency_score=consistency_score,
                    accuracy_score=accuracy_score,
                    timeliness_score=timeliness_score,
                    overall_score=overall_score,
                    quality_level=quality_level,
                    issues_count=len(self.issues_log),
                    recommendations=recommendations
                )
                
                # 记录质量历史
                if symbol and timeframe:
                    self.quality_history[f"{symbol}_{timeframe}"].append(metrics)
                
                # 更新统计
                if overall_score >= 0.8:
                    self.stats['passed_checks'] += 1
                else:
                    self.stats['failed_checks'] += 1
                
                self.logger.info(f"数据质量检查完成: {symbol}_{timeframe}, 总分: {overall_score:.3f}")
                return metrics
                
        except Exception as e:
            self.logger.error(f"数据质量检查失败: {e}")
            self.stats['failed_checks'] += 1
            return self._create_empty_metrics()
    
    def _check_completeness(self, data: pd.DataFrame) -> float:
        """检查数据完整性"""
        try:
            required_columns = self.config['completeness']['required_columns']
            
            # 检查必需列是否存在
            missing_columns = [col for col in required_columns if col not in data.columns]
            if missing_columns:
                issue = QualityIssue(
                    issue_type="missing_columns",
                    severity="critical",
                    description=f"缺少必需列: {missing_columns}",
                    affected_columns=missing_columns,
                    affected_rows=len(data),
                    suggestion="添加缺少的数据列",
                    timestamp=datetime.now()
                )
                self.issues_log.append(issue)
                return 0.0
            
            # 计算缺失值比例
            total_cells = len(data) * len(required_columns)
            missing_cells = data[required_columns].isnull().sum().sum()
            completeness_ratio = 1.0 - (missing_cells / total_cells)
            
            return completeness_ratio
            
        except Exception as e:
            self.logger.error(f"完整性检查失败: {e}")
            return 0.0
    
    def _check_consistency(self, data: pd.DataFrame, timeframe: str = None) -> float:
        """检查数据一致性"""
        try:
            consistency_scores = []
            
            # 价格一致性检查
            if self.config['consistency']['price_consistency_check']:
                price_score = self._check_price_consistency(data)
                consistency_scores.append(price_score)
            
            # 成交量一致性检查
            if self.config['consistency']['volume_consistency_check']:
                volume_score = self._check_volume_consistency(data)
                consistency_scores.append(volume_score)
            
            # 时间一致性检查
            if self.config['consistency']['time_consistency_check']:
                time_score = self._check_time_consistency(data, timeframe)
                consistency_scores.append(time_score)
            
            return np.mean(consistency_scores) if consistency_scores else 1.0
            
        except Exception as e:
            self.logger.error(f"一致性检查失败: {e}")
            return 0.0
    
    def _check_price_consistency(self, data: pd.DataFrame) -> float:
        """检查价格数据一致性"""
        try:
            if not all(col in data.columns for col in ['open', 'high', 'low', 'close']):
                return 1.0
            
            # 检查价格逻辑一致性
            high_valid = (data['high'] >= data[['open', 'close']].max(axis=1)).all()
            low_valid = (data['low'] <= data[['open', 'close']].min(axis=1)).all()
            positive_prices = (data[['open', 'high', 'low', 'close']] > 0).all().all()
            
            consistency_issues = 0
            if not high_valid:
                consistency_issues += 1
            if not low_valid:
                consistency_issues += 1
            if not positive_prices:
                consistency_issues += 1
            
            return max(0.0, 1.0 - (consistency_issues * 0.3))
            
        except Exception as e:
            self.logger.error(f"价格一致性检查失败: {e}")
            return 0.0
    
    def _check_volume_consistency(self, data: pd.DataFrame) -> float:
        """检查成交量一致性"""
        try:
            if 'volume' not in data.columns:
                return 1.0
            
            # 检查成交量为非负数
            non_negative_volume = (data['volume'] >= 0).all()
            
            return 1.0 if non_negative_volume else 0.7
            
        except Exception as e:
            self.logger.error(f"成交量一致性检查失败: {e}")
            return 0.0
    
    def _check_time_consistency(self, data: pd.DataFrame, timeframe: str = None) -> float:
        """检查时间一致性"""
        try:
            if not hasattr(data.index, 'to_pydatetime') and 'timestamp' not in data.columns:
                return 1.0
            
            # 简化的时间一致性检查
            return 1.0
            
        except Exception as e:
            self.logger.error(f"时间一致性检查失败: {e}")
            return 0.0
    
    def _check_accuracy(self, data: pd.DataFrame) -> float:
        """检查数据准确性"""
        try:
            accuracy_scores = []
            
            # 异常值检测
            if self.config['accuracy']['outlier_detection_method'] == 'iqr':
                outlier_score = self._detect_outliers_iqr(data)
                accuracy_scores.append(outlier_score)
            
            return np.mean(accuracy_scores) if accuracy_scores else 1.0
            
        except Exception as e:
            self.logger.error(f"准确性检查失败: {e}")
            return 0.0
    
    def _detect_outliers_iqr(self, data: pd.DataFrame) -> float:
        """使用IQR方法检测异常值"""
        try:
            numeric_columns = data.select_dtypes(include=[np.number]).columns
            total_outliers = 0
            total_values = 0
            
            for col in numeric_columns:
                Q1 = data[col].quantile(0.25)
                Q3 = data[col].quantile(0.75)
                IQR = Q3 - Q1
                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR
                
                outliers = ((data[col] < lower_bound) | (data[col] > upper_bound)).sum()
                total_outliers += outliers
                total_values += len(data[col])
            
            if total_values == 0:
                return 1.0
            
            outlier_ratio = total_outliers / total_values
            return max(0.0, 1.0 - outlier_ratio)
            
        except Exception as e:
            self.logger.error(f"IQR异常值检测失败: {e}")
            return 1.0
    
    def _check_timeliness(self, data: pd.DataFrame, timeframe: str = None) -> float:
        """检查数据时效性"""
        try:
            # 简化的时效性检查
            return 1.0
            
        except Exception as e:
            self.logger.error(f"时效性检查失败: {e}")
            return 0.0
    
    def _determine_quality_level(self, overall_score: float) -> QualityLevel:
        """确定质量等级"""
        if overall_score >= 0.9:
            return QualityLevel.EXCELLENT
        elif overall_score >= 0.8:
            return QualityLevel.GOOD
        elif overall_score >= 0.7:
            return QualityLevel.FAIR
        elif overall_score >= 0.6:
            return QualityLevel.POOR
        else:
            return QualityLevel.CRITICAL
    
    def _generate_recommendations(self, completeness: float, consistency: float, 
                                accuracy: float, timeliness: float) -> List[str]:
        """生成改进建议"""
        recommendations = []
        
        if completeness < 0.9:
            recommendations.append("提高数据完整性，减少缺失值")
        if consistency < 0.9:
            recommendations.append("检查数据一致性，修复逻辑错误")
        if accuracy < 0.9:
            recommendations.append("处理异常值，提高数据准确性")
        if timeliness < 0.9:
            recommendations.append("优化数据获取时效性")
        
        return recommendations
    
    def _create_empty_metrics(self) -> QualityMetrics:
        """创建空的质量指标"""
        return QualityMetrics(
            completeness_score=0.0,
            consistency_score=0.0,
            accuracy_score=0.0,
            timeliness_score=0.0,
            overall_score=0.0,
            quality_level=QualityLevel.CRITICAL,
            issues_count=0,
            recommendations=["数据为空或无效"]
        )
    
    def get_quality_statistics(self) -> Dict[str, Any]:
        """获取质量检查统计信息"""
        return {
            'stats': self.stats.copy(),
            'config': self.config,
            'supported_timeframes': self.supported_timeframes,
            'issues_count': len(self.issues_log),
            'quality_history_count': sum(len(history) for history in self.quality_history.values())
        }