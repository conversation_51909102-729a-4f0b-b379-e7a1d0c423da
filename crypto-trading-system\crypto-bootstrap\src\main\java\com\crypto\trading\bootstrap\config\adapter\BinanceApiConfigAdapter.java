package com.crypto.trading.bootstrap.config.adapter;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import com.crypto.trading.sdk.config.BinanceApiConfig;

/**
 * 币安API配置适配器
 * <p>
 * 将Bootstrap模块中的BinanceApiConfig转换为SDK模块需要的配置类
 * 解决不同模块间配置类冲突问题
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Configuration
public class BinanceApiConfigAdapter {
    
    private static final Logger log = LoggerFactory.getLogger(BinanceApiConfigAdapter.class);
    
    // 不再需要注入bootstrapConfig，因为BinanceApiConfig现在已经统一为SDK的版本
    
    /**
     * 创建SDK模块需要的BinanceApiConfig实例
     * 使用@Primary注解确保优先使用这个Bean
     *
     * @return SDK模块需要的BinanceApiConfig实例
     * @deprecated 现在已经统一使用SDK的BinanceApiConfig，此适配器将在下一版本移除
     */
    @Bean(name = "binanceApiConfigBean")
    @Primary
    @Deprecated
    public BinanceApiConfig sdkBinanceApiConfig() {
        log.info("创建SDK模块币安API适配器 - 由于配置已统一，此适配器已不再需要");
        // 配置已统一，不再需要通过反射设置字段
        log.info("配置已统一到SDK模块，不再需要适配");
        return new BinanceApiConfig();
    }
} 