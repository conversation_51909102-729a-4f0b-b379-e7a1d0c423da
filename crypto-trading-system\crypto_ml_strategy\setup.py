from setuptools import setup, find_packages

with open("requirements.txt") as f:
    required = f.read().splitlines()

with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

setup(
    name="crypto_ml_strategy",
    version="1.0.0",
    author="Trading Team",
    author_email="<EMAIL>",
    description="机器学习策略模块，用于虚拟货币量化交易系统",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/your-org/crypto-trading-system",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Financial and Insurance Industry",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
    ],
    python_requires=">=3.8",
    install_requires=required,
    entry_points={
        "console_scripts": [
            "crypto_ml_strategy=src.main:main",
        ],
    },
    include_package_data=True,
    package_data={
        "crypto_ml_strategy": ["config/*.ini", "config/*.json", "config/*.conf"],
    },
)