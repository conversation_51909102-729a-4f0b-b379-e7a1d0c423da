package com.crypto.trading.bootstrap.order;

import com.crypto.trading.bootstrap.initializer.ModuleInitializer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 启动顺序管理器
 * 负责解析模块间的依赖关系，确定模块的初始化顺序
 */
@Component
public class StartupOrderManager {
    
    private static final Logger log = LoggerFactory.getLogger(StartupOrderManager.class);
    
    /**
     * 模块初始化器映射表
     * key: 模块名称, value: 模块初始化器
     */
    private final Map<String, ModuleInitializer> initializerMap = new ConcurrentHashMap<>();
    
    /**
     * 已初始化的模块集合
     */
    private final Set<String> initializedModules = ConcurrentHashMap.newKeySet();
    
    /**
     * 当前正在初始化的模块路径，用于检测循环依赖
     */
    private final ThreadLocal<Set<String>> initializing = ThreadLocal.withInitial(HashSet::new);
    
    /**
     * 注册所有模块初始化器
     *
     * @param initializers 模块初始化器列表
     */
    @Autowired
    public void registerInitializers(List<ModuleInitializer> initializers) {
        for (ModuleInitializer initializer : initializers) {
            initializerMap.put(initializer.getName(), initializer);
            log.info("注册模块初始化器: {}, 优先级: {}", initializer.getName(), initializer.getPriority());
        }
    }
    
    /**
     * 获取排序后的模块初始化器列表
     * 
     * @return 排序后的初始化器列表
     */
    public List<ModuleInitializer> getSortedInitializers() {
        // 首先按优先级排序
        List<ModuleInitializer> initializers = new ArrayList<>(initializerMap.values());
        initializers.sort(Comparator.comparingInt(ModuleInitializer::getPriority));
        
        // 然后根据依赖关系进行拓扑排序
        List<ModuleInitializer> result = new ArrayList<>();
        Set<String> visited = new HashSet<>();
        
        try {
            // 清空初始化中的模块集合
            initializing.get().clear();
            
            for (ModuleInitializer initializer : initializers) {
                if (!visited.contains(initializer.getName())) {
                    visitInitializer(initializer, visited, result);
                }
            }
            
            return result;
        } finally {
            // 确保清理ThreadLocal
            clearThreadLocal();
        }
    }
    
    /**
     * 深度优先遍历模块依赖关系
     * 
     * @param initializer 当前模块初始化器
     * @param visited 已访问模块集合
     * @param result 排序结果列表
     */
    private void visitInitializer(ModuleInitializer initializer, Set<String> visited, List<ModuleInitializer> result) {
        String moduleName = initializer.getName();
        
        // 检测循环依赖
        Set<String> currentInitializing = initializing.get();
        if (currentInitializing.contains(moduleName)) {
            // 构建循环依赖路径
            String path = new ArrayList<>(currentInitializing).stream()
                    .collect(Collectors.joining(" -> ")) + 
                    " -> " + moduleName;
            throw new IllegalStateException("检测到循环依赖: " + path);
        }
        
        // 标记当前模块为正在初始化
        currentInitializing.add(moduleName);
        
        try {
            visited.add(moduleName);
            
            // 先初始化依赖模块
            for (String dependency : initializer.getDependencies()) {
                if (!visited.contains(dependency)) {
                    ModuleInitializer dependencyInitializer = initializerMap.get(dependency);
                    if (dependencyInitializer == null) {
                        throw new IllegalStateException("模块 [" + moduleName + "] 依赖的模块 [" + dependency + "] 不存在");
                    }
                    visitInitializer(dependencyInitializer, visited, result);
                } else if (currentInitializing.contains(dependency)) {
                    // 如果依赖已在初始化中，说明存在循环依赖
                    String path = new ArrayList<>(currentInitializing).stream()
                            .collect(Collectors.joining(" -> ")) + 
                            " -> " + dependency;
                    throw new IllegalStateException("检测到循环依赖: " + path);
                }
            }
            
            result.add(initializer);
        } finally {
            // 移除当前模块的初始化标记
            currentInitializing.remove(moduleName);
        }
    }
    
    /**
     * 初始化所有模块
     * 
     * @throws Exception 初始化过程中可能抛出的异常
     */
    public void initializeAll() throws Exception {
        List<ModuleInitializer> sortedInitializers = getSortedInitializers();
        
        log.info("开始初始化 {} 个模块，初始化顺序: {}", 
                sortedInitializers.size(), 
                sortedInitializers.stream().map(ModuleInitializer::getName).collect(Collectors.joining(" -> ")));
        
        // 初始化前操作
        for (ModuleInitializer initializer : sortedInitializers) {
            initializer.beforeInitialize();
        }
        
        // 执行初始化
        for (ModuleInitializer initializer : sortedInitializers) {
            String moduleName = initializer.getName();
            if (!initializedModules.contains(moduleName)) {
                initializer.initialize();
                initializedModules.add(moduleName);
            }
        }
        
        // 初始化后操作
        for (ModuleInitializer initializer : sortedInitializers) {
            initializer.afterInitialize();
        }
        
        log.info("所有模块初始化完成");
    }
    
    /**
     * 关闭所有模块
     * 以初始化的相反顺序关闭模块
     */
    public void shutdownAll() {
        List<ModuleInitializer> sortedInitializers = getSortedInitializers();
        Collections.reverse(sortedInitializers);
        
        log.info("开始关闭所有模块，关闭顺序: {}", 
                sortedInitializers.stream().map(ModuleInitializer::getName).collect(Collectors.joining(" -> ")));
        
        for (ModuleInitializer initializer : sortedInitializers) {
            String moduleName = initializer.getName();
            if (initializedModules.contains(moduleName)) {
                initializer.shutdown();
                initializedModules.remove(moduleName);
            }
        }
        
        log.info("所有模块已关闭");
    }
    
    /**
     * 清除线程本地变量
     */
    public void clearThreadLocal() {
        initializing.remove();
    }
}