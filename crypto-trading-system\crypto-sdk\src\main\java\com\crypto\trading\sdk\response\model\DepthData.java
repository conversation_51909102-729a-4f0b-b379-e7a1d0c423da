package com.crypto.trading.sdk.response.model;

import java.math.BigDecimal;
import java.util.List;

/**
 * 深度数据模型
 * 表示市场深度数据
 */
public class DepthData {
    
    /**
     * 交易对
     */
    private String symbol;

    /**
     * 最后更新ID
     */
    private Long lastUpdateId;

    /**
     * 买单列表 [价格, 数量]
     */
    private List<PriceQuantity> bids;

    /**
     * 卖单列表 [价格, 数量]
     */
    private List<PriceQuantity> asks;

    /**
     * 事件时间
     */
    private Long eventTime;

    /**
     * 默认构造函数
     */
    public DepthData() {
    }

    /**
     * 构造函数
     *
     * @param lastUpdateId 最后更新ID
     * @param bids         买单列表
     * @param asks         卖单列表
     * @param eventTime    事件时间
     */
    public DepthData(Long lastUpdateId, List<PriceQuantity> bids, List<PriceQuantity> asks, Long eventTime) {
        this.lastUpdateId = lastUpdateId;
        this.bids = bids;
        this.asks = asks;
        this.eventTime = eventTime;
    }

    /**
     * 获取最后更新ID
     *
     * @return 最后更新ID
     */
    public Long getLastUpdateId() {
        return lastUpdateId;
    }

    /**
     * 设置最后更新ID
     *
     * @param lastUpdateId 最后更新ID
     */
    public void setLastUpdateId(Long lastUpdateId) {
        this.lastUpdateId = lastUpdateId;
    }

    /**
     * 获取买单列表
     *
     * @return 买单列表
     */
    public List<PriceQuantity> getBids() {
        return bids;
    }

    /**
     * 设置买单列表
     *
     * @param bids 买单列表
     */
    public void setBids(List<PriceQuantity> bids) {
        this.bids = bids;
    }

    /**
     * 获取卖单列表
     *
     * @return 卖单列表
     */
    public List<PriceQuantity> getAsks() {
        return asks;
    }

    /**
     * 设置卖单列表
     *
     * @param asks 卖单列表
     */
    public void setAsks(List<PriceQuantity> asks) {
        this.asks = asks;
    }

    /**
     * 获取事件时间
     *
     * @return 事件时间
     */
    public Long getEventTime() {
        return eventTime;
    }

    /**
     * 设置事件时间
     *
     * @param eventTime 事件时间
     */
    public void setEventTime(Long eventTime) {
        this.eventTime = eventTime;
    }
    
    /**
     * 获取交易对
     *
     * @return 交易对
     */
    public String getSymbol() {
        return symbol;
    }

    /**
     * 设置交易对
     *
     * @param symbol 交易对
     */
    public void setSymbol(String symbol) {
        this.symbol = symbol;
    }

    @Override
    public String toString() {
        return "DepthData{" +
                "symbol='" + symbol + '\'' +
                ", lastUpdateId=" + lastUpdateId +
                ", bids=" + bids +
                ", asks=" + asks +
                ", eventTime=" + eventTime +
                '}';
    }

    /**
     * 价格和数量对
     */
    public static class PriceQuantity {
        /**
         * 价格
         */
        private BigDecimal price;

        /**
         * 数量
         */
        private BigDecimal quantity;

        /**
         * 默认构造函数
         */
        public PriceQuantity() {
        }

        /**
         * 构造函数
         *
         * @param price    价格
         * @param quantity 数量
         */
        public PriceQuantity(BigDecimal price, BigDecimal quantity) {
            this.price = price;
            this.quantity = quantity;
        }

        /**
         * 从数组创建价格和数量对
         *
         * @param array 价格和数量数组
         * @return 价格和数量对
         */
        public static PriceQuantity fromArray(Object[] array) {
            if (array == null || array.length < 2) {
                throw new IllegalArgumentException("价格和数量数组格式不正确");
            }
            return new PriceQuantity(
                    new BigDecimal(array[0].toString()),
                    new BigDecimal(array[1].toString())
            );
        }

        /**
         * 获取价格
         *
         * @return 价格
         */
        public BigDecimal getPrice() {
            return price;
        }

        /**
         * 设置价格
         *
         * @param price 价格
         */
        public void setPrice(BigDecimal price) {
            this.price = price;
        }

        /**
         * 获取数量
         *
         * @return 数量
         */
        public BigDecimal getQuantity() {
            return quantity;
        }

        /**
         * 设置数量
         *
         * @param quantity 数量
         */
        public void setQuantity(BigDecimal quantity) {
            this.quantity = quantity;
        }

        @Override
        public String toString() {
            return "[" + price + ", " + quantity + "]";
        }
    }
} 