package com.crypto.trading.market.config;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;

/**
 * WebSocketConfig单元测试
 */
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = {WebSocketConfig.class})
@TestPropertySource(properties = {
        "market.websocket.reconnect-interval=3000",
        "market.websocket.connect-timeout=4000",
        "market.websocket.read-timeout=25000",
        "market.websocket.ping-interval=20000",
        "market.websocket.health-check-interval=30000",
        "market.websocket.max-fail-count=5",
        "market.websocket.pool-initial-capacity=32",
        "market.websocket.max-concurrent-connections=200"
})
class WebSocketConfigTest {

    @Autowired
    private WebSocketConfig webSocketConfig;

    @Test
    void testGetReconnectInterval() {
        assertEquals(3000, webSocketConfig.getReconnectInterval());
    }

    @Test
    void testGetConnectTimeout() {
        assertEquals(4000, webSocketConfig.getConnectTimeout());
    }

    @Test
    void testGetReadTimeout() {
        assertEquals(25000, webSocketConfig.getReadTimeout());
    }

    @Test
    void testGetPingInterval() {
        assertEquals(20000, webSocketConfig.getPingInterval());
    }

    @Test
    void testGetHealthCheckInterval() {
        assertEquals(30000, webSocketConfig.getHealthCheckInterval());
    }

    @Test
    void testGetMaxFailCount() {
        assertEquals(5, webSocketConfig.getMaxFailCount());
    }

    @Test
    void testGetPoolInitialCapacity() {
        assertEquals(32, webSocketConfig.getPoolInitialCapacity());
    }

    @Test
    void testGetMaxConcurrentConnections() {
        assertEquals(200, webSocketConfig.getMaxConcurrentConnections());
    }
}