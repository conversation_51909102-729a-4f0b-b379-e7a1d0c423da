package com.crypto.trading.common.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.management.ManagementFactory;
import java.lang.management.ThreadInfo;
import java.lang.management.ThreadMXBean;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 虚拟线程监控器
 * <p>
 * 监控和收集虚拟线程的使用情况和性能指标
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class VirtualThreadMonitor {

    private static final Logger log = LoggerFactory.getLogger(VirtualThreadMonitor.class);

    /**
     * 单例实例
     */
    private static volatile VirtualThreadMonitor instance;

    /**
     * 线程MX Bean
     */
    private final ThreadMXBean threadMXBean;

    /**
     * 调度执行器
     */
    private final ScheduledExecutorService scheduler;

    /**
     * 虚拟线程计数器
     */
    private final AtomicLong virtualThreadCount = new AtomicLong(0);

    /**
     * 虚拟线程统计信息
     */
    private final Map<String, ThreadStats> threadStatsMap = new ConcurrentHashMap<>();

    /**
     * 是否已启动
     */
    private boolean started = false;

    /**
     * 获取单例实例
     *
     * @return 虚拟线程监控器实例
     */
    public static VirtualThreadMonitor getInstance() {
        if (instance == null) {
            synchronized (VirtualThreadMonitor.class) {
                if (instance == null) {
                    instance = new VirtualThreadMonitor();
                }
            }
        }
        return instance;
    }

    /**
     * 私有构造函数
     */
    private VirtualThreadMonitor() {
        threadMXBean = ManagementFactory.getThreadMXBean();
        scheduler = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread thread = new Thread(r, "virtual-thread-monitor");
            thread.setDaemon(true);
            return thread;
        });
    }

    /**
     * 启动监控
     *
     * @param intervalSeconds 监控间隔（秒）
     */
    public synchronized void start(int intervalSeconds) {
        if (started) {
            return;
        }

        scheduler.scheduleAtFixedRate(this::collectMetrics, 0, intervalSeconds, TimeUnit.SECONDS);
        log.info("虚拟线程监控器已启动，监控间隔: {}秒", intervalSeconds);
        started = true;
    }

    /**
     * 停止监控
     */
    public synchronized void stop() {
        if (!started) {
            return;
        }

        scheduler.shutdown();
        try {
            if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            scheduler.shutdownNow();
            Thread.currentThread().interrupt();
        }
        log.info("虚拟线程监控器已停止");
        started = false;
    }

    /**
     * 注册虚拟线程
     *
     * @param category 线程类别
     * @return 线程ID
     */
    public long registerVirtualThread(String category) {
        long threadId = virtualThreadCount.incrementAndGet();
        threadStatsMap.computeIfAbsent(category, k -> new ThreadStats()).incrementCreated();
        return threadId;
    }

    /**
     * 记录虚拟线程完成
     *
     * @param category 线程类别
     * @param durationMs 执行时长（毫秒）
     */
    public void recordVirtualThreadCompletion(String category, long durationMs) {
        threadStatsMap.computeIfAbsent(category, k -> new ThreadStats()).recordCompletion(durationMs);
    }

    /**
     * 记录虚拟线程异常
     *
     * @param category 线程类别
     * @param exception 异常
     */
    public void recordVirtualThreadException(String category, Throwable exception) {
        threadStatsMap.computeIfAbsent(category, k -> new ThreadStats()).incrementErrors();
        log.warn("虚拟线程异常: category={}, error={}", category, exception.getMessage(), exception);
    }

    /**
     * 收集指标
     */
    private void collectMetrics() {
        try {
            // 获取所有线程信息
            ThreadInfo[] threadInfos = threadMXBean.dumpAllThreads(false, false);
            
            // 统计虚拟线程数量
            long virtualThreads = 0;
            for (ThreadInfo threadInfo : threadInfos) {
                if (threadInfo.getThreadName().contains("VirtualThread")) {
                    virtualThreads++;
                }
            }
            
            // 记录指标
            log.info("虚拟线程监控 - 当前虚拟线程数: {}, 总创建数: {}", virtualThreads, virtualThreadCount.get());
            
            // 记录各类别线程统计信息
            for (Map.Entry<String, ThreadStats> entry : threadStatsMap.entrySet()) {
                ThreadStats stats = entry.getValue();
                log.info("虚拟线程监控 - 类别: {}, 创建数: {}, 完成数: {}, 错误数: {}, 平均执行时间: {}ms",
                        entry.getKey(), stats.getCreated(), stats.getCompleted(), stats.getErrors(), stats.getAverageExecutionTime());
            }
            
            // 将指标发送到InfluxDB（如果需要）
            // sendMetricsToInfluxDB();
        } catch (Exception e) {
            log.error("收集虚拟线程指标异常", e);
        }
    }

    /**
     * 获取虚拟线程统计信息
     *
     * @return 虚拟线程统计信息
     */
    public Map<String, Map<String, Object>> getThreadStats() {
        Map<String, Map<String, Object>> result = new HashMap<>();
        
        for (Map.Entry<String, ThreadStats> entry : threadStatsMap.entrySet()) {
            ThreadStats stats = entry.getValue();
            Map<String, Object> statsMap = new HashMap<>();
            statsMap.put("created", stats.getCreated());
            statsMap.put("completed", stats.getCompleted());
            statsMap.put("errors", stats.getErrors());
            statsMap.put("averageExecutionTime", stats.getAverageExecutionTime());
            result.put(entry.getKey(), statsMap);
        }
        
        return result;
    }

    /**
     * 获取总虚拟线程创建数
     *
     * @return 总虚拟线程创建数
     */
    public long getTotalVirtualThreadCount() {
        return virtualThreadCount.get();
    }

    /**
     * 线程统计信息类
     */
    private static class ThreadStats {
        private final AtomicLong created = new AtomicLong(0);
        private final AtomicLong completed = new AtomicLong(0);
        private final AtomicLong errors = new AtomicLong(0);
        private final AtomicLong totalExecutionTime = new AtomicLong(0);
        
        public void incrementCreated() {
            created.incrementAndGet();
        }
        
        public void recordCompletion(long durationMs) {
            completed.incrementAndGet();
            totalExecutionTime.addAndGet(durationMs);
        }
        
        public void incrementErrors() {
            errors.incrementAndGet();
        }
        
        public long getCreated() {
            return created.get();
        }
        
        public long getCompleted() {
            return completed.get();
        }
        
        public long getErrors() {
            return errors.get();
        }
        
        public double getAverageExecutionTime() {
            long completedCount = completed.get();
            if (completedCount == 0) {
                return 0;
            }
            return (double) totalExecutionTime.get() / completedCount;
        }
    }
    
    /**
     * 创建一个包装的虚拟线程执行器，用于监控虚拟线程的执行
     *
     * @param category 线程类别
     * @return 包装的虚拟线程执行器
     */
    public ExecutorService createMonitoredVirtualThreadExecutor(String category) {
        return Executors.newThreadPerTaskExecutor(task -> {
            long startTime = System.currentTimeMillis();
            long threadId = registerVirtualThread(category);
            Thread thread = Thread.ofVirtual().name(category + "-" + threadId).unstarted(() -> {
                try {
                    task.run();
                    recordVirtualThreadCompletion(category, System.currentTimeMillis() - startTime);
                } catch (Throwable e) {
                    recordVirtualThreadException(category, e);
                    throw e;
                }
            });
            return thread;
        });
    }
}