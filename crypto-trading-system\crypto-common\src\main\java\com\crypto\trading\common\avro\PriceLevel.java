/**
 * Autogenerated by Avro
 *
 * DO NOT EDIT DIRECTLY
 */
package com.crypto.trading.common.avro;

import org.apache.avro.generic.GenericArray;
import org.apache.avro.specific.SpecificData;
import org.apache.avro.util.Utf8;
import org.apache.avro.message.BinaryMessageEncoder;
import org.apache.avro.message.BinaryMessageDecoder;
import org.apache.avro.message.SchemaStore;

@org.apache.avro.specific.AvroGenerated
public class PriceLevel extends org.apache.avro.specific.SpecificRecordBase implements org.apache.avro.specific.SpecificRecord {
  private static final long serialVersionUID = -885696348551693622L;


  public static final org.apache.avro.Schema SCHEMA$ = new org.apache.avro.Schema.Parser().parse("{\"type\":\"record\",\"name\":\"PriceLevel\",\"namespace\":\"com.crypto.trading.common.avro\",\"fields\":[{\"name\":\"price\",\"type\":\"double\",\"doc\":\"价格\"},{\"name\":\"quantity\",\"type\":\"double\",\"doc\":\"数量\"}]}");
  public static org.apache.avro.Schema getClassSchema() { return SCHEMA$; }

  private static final SpecificData MODEL$ = new SpecificData();

  private static final BinaryMessageEncoder<PriceLevel> ENCODER =
      new BinaryMessageEncoder<>(MODEL$, SCHEMA$);

  private static final BinaryMessageDecoder<PriceLevel> DECODER =
      new BinaryMessageDecoder<>(MODEL$, SCHEMA$);

  /**
   * Return the BinaryMessageEncoder instance used by this class.
   * @return the message encoder used by this class
   */
  public static BinaryMessageEncoder<PriceLevel> getEncoder() {
    return ENCODER;
  }

  /**
   * Return the BinaryMessageDecoder instance used by this class.
   * @return the message decoder used by this class
   */
  public static BinaryMessageDecoder<PriceLevel> getDecoder() {
    return DECODER;
  }

  /**
   * Create a new BinaryMessageDecoder instance for this class that uses the specified {@link SchemaStore}.
   * @param resolver a {@link SchemaStore} used to find schemas by fingerprint
   * @return a BinaryMessageDecoder instance for this class backed by the given SchemaStore
   */
  public static BinaryMessageDecoder<PriceLevel> createDecoder(SchemaStore resolver) {
    return new BinaryMessageDecoder<>(MODEL$, SCHEMA$, resolver);
  }

  /**
   * Serializes this PriceLevel to a ByteBuffer.
   * @return a buffer holding the serialized data for this instance
   * @throws java.io.IOException if this instance could not be serialized
   */
  public java.nio.ByteBuffer toByteBuffer() throws java.io.IOException {
    return ENCODER.encode(this);
  }

  /**
   * Deserializes a PriceLevel from a ByteBuffer.
   * @param b a byte buffer holding serialized data for an instance of this class
   * @return a PriceLevel instance decoded from the given buffer
   * @throws java.io.IOException if the given bytes could not be deserialized into an instance of this class
   */
  public static PriceLevel fromByteBuffer(
      java.nio.ByteBuffer b) throws java.io.IOException {
    return DECODER.decode(b);
  }

  /** 价格 */
  private double price;
  /** 数量 */
  private double quantity;

  /**
   * Default constructor.  Note that this does not initialize fields
   * to their default values from the schema.  If that is desired then
   * one should use <code>newBuilder()</code>.
   */
  public PriceLevel() {}

  /**
   * All-args constructor.
   * @param price 价格
   * @param quantity 数量
   */
  public PriceLevel(java.lang.Double price, java.lang.Double quantity) {
    this.price = price;
    this.quantity = quantity;
  }

  @Override
  public org.apache.avro.specific.SpecificData getSpecificData() { return MODEL$; }

  @Override
  public org.apache.avro.Schema getSchema() { return SCHEMA$; }

  // Used by DatumWriter.  Applications should not call.
  @Override
  public java.lang.Object get(int field$) {
    switch (field$) {
    case 0: return price;
    case 1: return quantity;
    default: throw new IndexOutOfBoundsException("Invalid index: " + field$);
    }
  }

  // Used by DatumReader.  Applications should not call.
  @Override
  @SuppressWarnings(value="unchecked")
  public void put(int field$, java.lang.Object value$) {
    switch (field$) {
    case 0: price = (java.lang.Double)value$; break;
    case 1: quantity = (java.lang.Double)value$; break;
    default: throw new IndexOutOfBoundsException("Invalid index: " + field$);
    }
  }

  /**
   * Gets the value of the 'price' field.
   * @return 价格
   */
  public double getPrice() {
    return price;
  }


  /**
   * Sets the value of the 'price' field.
   * 价格
   * @param value the value to set.
   */
  public void setPrice(double value) {
    this.price = value;
  }

  /**
   * Gets the value of the 'quantity' field.
   * @return 数量
   */
  public double getQuantity() {
    return quantity;
  }


  /**
   * Sets the value of the 'quantity' field.
   * 数量
   * @param value the value to set.
   */
  public void setQuantity(double value) {
    this.quantity = value;
  }

  /**
   * Creates a new PriceLevel RecordBuilder.
   * @return A new PriceLevel RecordBuilder
   */
  public static com.crypto.trading.common.avro.PriceLevel.Builder newBuilder() {
    return new com.crypto.trading.common.avro.PriceLevel.Builder();
  }

  /**
   * Creates a new PriceLevel RecordBuilder by copying an existing Builder.
   * @param other The existing builder to copy.
   * @return A new PriceLevel RecordBuilder
   */
  public static com.crypto.trading.common.avro.PriceLevel.Builder newBuilder(com.crypto.trading.common.avro.PriceLevel.Builder other) {
    if (other == null) {
      return new com.crypto.trading.common.avro.PriceLevel.Builder();
    } else {
      return new com.crypto.trading.common.avro.PriceLevel.Builder(other);
    }
  }

  /**
   * Creates a new PriceLevel RecordBuilder by copying an existing PriceLevel instance.
   * @param other The existing instance to copy.
   * @return A new PriceLevel RecordBuilder
   */
  public static com.crypto.trading.common.avro.PriceLevel.Builder newBuilder(com.crypto.trading.common.avro.PriceLevel other) {
    if (other == null) {
      return new com.crypto.trading.common.avro.PriceLevel.Builder();
    } else {
      return new com.crypto.trading.common.avro.PriceLevel.Builder(other);
    }
  }

  /**
   * RecordBuilder for PriceLevel instances.
   */
  @org.apache.avro.specific.AvroGenerated
  public static class Builder extends org.apache.avro.specific.SpecificRecordBuilderBase<PriceLevel>
    implements org.apache.avro.data.RecordBuilder<PriceLevel> {

    /** 价格 */
    private double price;
    /** 数量 */
    private double quantity;

    /** Creates a new Builder */
    private Builder() {
      super(SCHEMA$, MODEL$);
    }

    /**
     * Creates a Builder by copying an existing Builder.
     * @param other The existing Builder to copy.
     */
    private Builder(com.crypto.trading.common.avro.PriceLevel.Builder other) {
      super(other);
      if (isValidValue(fields()[0], other.price)) {
        this.price = data().deepCopy(fields()[0].schema(), other.price);
        fieldSetFlags()[0] = other.fieldSetFlags()[0];
      }
      if (isValidValue(fields()[1], other.quantity)) {
        this.quantity = data().deepCopy(fields()[1].schema(), other.quantity);
        fieldSetFlags()[1] = other.fieldSetFlags()[1];
      }
    }

    /**
     * Creates a Builder by copying an existing PriceLevel instance
     * @param other The existing instance to copy.
     */
    private Builder(com.crypto.trading.common.avro.PriceLevel other) {
      super(SCHEMA$, MODEL$);
      if (isValidValue(fields()[0], other.price)) {
        this.price = data().deepCopy(fields()[0].schema(), other.price);
        fieldSetFlags()[0] = true;
      }
      if (isValidValue(fields()[1], other.quantity)) {
        this.quantity = data().deepCopy(fields()[1].schema(), other.quantity);
        fieldSetFlags()[1] = true;
      }
    }

    /**
      * Gets the value of the 'price' field.
      * 价格
      * @return The value.
      */
    public double getPrice() {
      return price;
    }


    /**
      * Sets the value of the 'price' field.
      * 价格
      * @param value The value of 'price'.
      * @return This builder.
      */
    public com.crypto.trading.common.avro.PriceLevel.Builder setPrice(double value) {
      validate(fields()[0], value);
      this.price = value;
      fieldSetFlags()[0] = true;
      return this;
    }

    /**
      * Checks whether the 'price' field has been set.
      * 价格
      * @return True if the 'price' field has been set, false otherwise.
      */
    public boolean hasPrice() {
      return fieldSetFlags()[0];
    }


    /**
      * Clears the value of the 'price' field.
      * 价格
      * @return This builder.
      */
    public com.crypto.trading.common.avro.PriceLevel.Builder clearPrice() {
      fieldSetFlags()[0] = false;
      return this;
    }

    /**
      * Gets the value of the 'quantity' field.
      * 数量
      * @return The value.
      */
    public double getQuantity() {
      return quantity;
    }


    /**
      * Sets the value of the 'quantity' field.
      * 数量
      * @param value The value of 'quantity'.
      * @return This builder.
      */
    public com.crypto.trading.common.avro.PriceLevel.Builder setQuantity(double value) {
      validate(fields()[1], value);
      this.quantity = value;
      fieldSetFlags()[1] = true;
      return this;
    }

    /**
      * Checks whether the 'quantity' field has been set.
      * 数量
      * @return True if the 'quantity' field has been set, false otherwise.
      */
    public boolean hasQuantity() {
      return fieldSetFlags()[1];
    }


    /**
      * Clears the value of the 'quantity' field.
      * 数量
      * @return This builder.
      */
    public com.crypto.trading.common.avro.PriceLevel.Builder clearQuantity() {
      fieldSetFlags()[1] = false;
      return this;
    }

    @Override
    @SuppressWarnings("unchecked")
    public PriceLevel build() {
      try {
        PriceLevel record = new PriceLevel();
        record.price = fieldSetFlags()[0] ? this.price : (java.lang.Double) defaultValue(fields()[0]);
        record.quantity = fieldSetFlags()[1] ? this.quantity : (java.lang.Double) defaultValue(fields()[1]);
        return record;
      } catch (org.apache.avro.AvroMissingFieldException e) {
        throw e;
      } catch (java.lang.Exception e) {
        throw new org.apache.avro.AvroRuntimeException(e);
      }
    }
  }

  @SuppressWarnings("unchecked")
  private static final org.apache.avro.io.DatumWriter<PriceLevel>
    WRITER$ = (org.apache.avro.io.DatumWriter<PriceLevel>)MODEL$.createDatumWriter(SCHEMA$);

  @Override public void writeExternal(java.io.ObjectOutput out)
    throws java.io.IOException {
    WRITER$.write(this, SpecificData.getEncoder(out));
  }

  @SuppressWarnings("unchecked")
  private static final org.apache.avro.io.DatumReader<PriceLevel>
    READER$ = (org.apache.avro.io.DatumReader<PriceLevel>)MODEL$.createDatumReader(SCHEMA$);

  @Override public void readExternal(java.io.ObjectInput in)
    throws java.io.IOException {
    READER$.read(this, SpecificData.getDecoder(in));
  }

  @Override protected boolean hasCustomCoders() { return true; }

  @Override public void customEncode(org.apache.avro.io.Encoder out)
    throws java.io.IOException
  {
    out.writeDouble(this.price);

    out.writeDouble(this.quantity);

  }

  @Override public void customDecode(org.apache.avro.io.ResolvingDecoder in)
    throws java.io.IOException
  {
    org.apache.avro.Schema.Field[] fieldOrder = in.readFieldOrderIfDiff();
    if (fieldOrder == null) {
      this.price = in.readDouble();

      this.quantity = in.readDouble();

    } else {
      for (int i = 0; i < 2; i++) {
        switch (fieldOrder[i].pos()) {
        case 0:
          this.price = in.readDouble();
          break;

        case 1:
          this.quantity = in.readDouble();
          break;

        default:
          throw new java.io.IOException("Corrupt ResolvingDecoder.");
        }
      }
    }
  }
}










