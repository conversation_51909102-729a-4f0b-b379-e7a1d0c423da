#!/usr/bin/env python3
"""
测试重组后的目录结构和导入功能

该脚本验证Task 12的8个模块化组件在重组后仍然可以正常导入和使用。
"""

import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_core_imports():
    """测试核心模块导入"""
    print("🔍 测试核心模块导入...")
    try:
        from src.core.validation_result_types import (
            ValidationResult, 
            IntegrationTestResult,
            calculate_confidence_interval,
            test_statistical_significance,
            calculate_effect_size
        )
        print("✅ 核心模块导入成功")
        return True
    except ImportError as e:
        print(f"❌ 核心模块导入失败: {e}")
        return False

def test_validators_imports():
    """测试验证器模块导入"""
    print("🔍 测试验证器模块导入...")
    try:
        from src.validators.latency_throughput_validators import LatencyValidator, ThroughputValidator
        from src.validators.memory_accuracy_validators import MemoryValidator, AccuracyValidator
        print("✅ 验证器模块导入成功")
        return True
    except ImportError as e:
        print(f"❌ 验证器模块导入失败: {e}")
        return False

def test_coordinators_imports():
    """测试协调器模块导入"""
    print("🔍 测试协调器模块导入...")
    try:
        from src.coordinators.system_performance_coordinator import SystemPerformanceCoordinator
        print("✅ 协调器模块导入成功")
        return True
    except ImportError as e:
        print(f"❌ 协调器模块导入失败: {e}")
        return False

def test_performance_tests_imports():
    """测试性能测试模块导入"""
    print("🔍 测试性能测试模块导入...")
    try:
        from src.performance_tests.timeframe_tests_core import TimeframeTestsCore
        from src.performance_tests.indicator_tests_core import IndicatorTestsCore
        from src.performance_tests.deepseek_tests_core import DeepSeekTestsCore
        from src.performance_tests.concurrent_load_tests import ConcurrentLoadTests
        print("✅ 性能测试模块导入成功")
        return True
    except ImportError as e:
        print(f"❌ 性能测试模块导入失败: {e}")
        return False

def test_package_imports():
    """测试包级别导入"""
    print("🔍 测试包级别导入...")
    try:
        from src.core import ValidationResult, IntegrationTestResult
        from src.validators import LatencyValidator, ThroughputValidator, MemoryValidator, AccuracyValidator
        from src.coordinators import SystemPerformanceCoordinator
        from src.performance_tests import TimeframeTestsCore, IndicatorTestsCore, DeepSeekTestsCore, ConcurrentLoadTests
        print("✅ 包级别导入成功")
        return True
    except ImportError as e:
        print(f"❌ 包级别导入失败: {e}")
        return False

def test_functionality():
    """测试基本功能"""
    print("🔍 测试基本功能...")
    try:
        from src.validators import LatencyValidator
        
        # 创建验证器实例
        validator = LatencyValidator(target_latency_ms=100.0)
        print("✅ 验证器实例创建成功")
        
        # 注意：不运行实际验证以避免长时间等待
        print("✅ 基本功能测试通过")
        return True
    except Exception as e:
        print(f"❌ 基本功能测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试重组后的目录结构...")
    print("=" * 50)
    
    tests = [
        test_core_imports,
        test_validators_imports, 
        test_coordinators_imports,
        test_performance_tests_imports,
        test_package_imports,
        test_functionality
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！目录重组成功！")
        return True
    else:
        print("⚠️ 部分测试失败，需要检查导入问题")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)