#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
依赖注入容器模块

该模块实现IoC (Inversion of Control) 容器，提供依赖注入功能，
支持服务注册、解析和生命周期管理。
"""

import inspect
from typing import Dict, Any, Type, TypeVar, Callable, Optional, List, Union
from dataclasses import dataclass
from enum import Enum
from loguru import logger

T = TypeVar('T')


class ServiceLifetime(Enum):
    """服务生命周期枚举"""
    SINGLETON = "singleton"  # 单例模式
    TRANSIENT = "transient"  # 每次创建新实例
    SCOPED = "scoped"       # 作用域内单例


@dataclass
class ServiceDescriptor:
    """服务描述符"""
    service_type: Type
    implementation_type: Optional[Type] = None
    factory: Optional[Callable] = None
    instance: Optional[Any] = None
    lifetime: ServiceLifetime = ServiceLifetime.TRANSIENT
    dependencies: List[Type] = None
    
    def __post_init__(self):
        if self.dependencies is None:
            self.dependencies = []


class DependencyContainer:
    """
    依赖注入容器
    
    提供服务注册、解析和生命周期管理功能。
    """
    
    def __init__(self):
        """初始化依赖容器"""
        self._services: Dict[Type, ServiceDescriptor] = {}
        self._singletons: Dict[Type, Any] = {}
        self._scoped_instances: Dict[Type, Any] = {}
        self._building_stack: List[Type] = []
        
        logger.info("依赖注入容器初始化完成")
    
    def register_singleton(self, service_type: Type[T], 
                          implementation_type: Optional[Type[T]] = None,
                          factory: Optional[Callable[[], T]] = None,
                          instance: Optional[T] = None) -> 'DependencyContainer':
        """
        注册单例服务
        
        Args:
            service_type: 服务类型
            implementation_type: 实现类型
            factory: 工厂函数
            instance: 已创建的实例
            
        Returns:
            容器实例（支持链式调用）
        """
        return self._register_service(
            service_type, implementation_type, factory, instance, ServiceLifetime.SINGLETON
        )
    
    def register_transient(self, service_type: Type[T],
                          implementation_type: Optional[Type[T]] = None,
                          factory: Optional[Callable[[], T]] = None) -> 'DependencyContainer':
        """
        注册瞬态服务
        
        Args:
            service_type: 服务类型
            implementation_type: 实现类型
            factory: 工厂函数
            
        Returns:
            容器实例（支持链式调用）
        """
        return self._register_service(
            service_type, implementation_type, factory, None, ServiceLifetime.TRANSIENT
        )
    
    def register_scoped(self, service_type: Type[T],
                       implementation_type: Optional[Type[T]] = None,
                       factory: Optional[Callable[[], T]] = None) -> 'DependencyContainer':
        """
        注册作用域服务
        
        Args:
            service_type: 服务类型
            implementation_type: 实现类型
            factory: 工厂函数
            
        Returns:
            容器实例（支持链式调用）
        """
        return self._register_service(
            service_type, implementation_type, factory, None, ServiceLifetime.SCOPED
        )
    
    def _register_service(self, service_type: Type[T],
                         implementation_type: Optional[Type[T]],
                         factory: Optional[Callable[[], T]],
                         instance: Optional[T],
                         lifetime: ServiceLifetime) -> 'DependencyContainer':
        """
        内部服务注册方法
        
        Args:
            service_type: 服务类型
            implementation_type: 实现类型
            factory: 工厂函数
            instance: 实例
            lifetime: 生命周期
            
        Returns:
            容器实例
        """
        if service_type in self._services:
            logger.warning(f"服务 {service_type.__name__} 已注册，将被覆盖")
        
        # 分析依赖关系
        dependencies = []
        if implementation_type:
            dependencies = self._analyze_dependencies(implementation_type)
        elif factory:
            dependencies = self._analyze_factory_dependencies(factory)
        
        descriptor = ServiceDescriptor(
            service_type=service_type,
            implementation_type=implementation_type,
            factory=factory,
            instance=instance,
            lifetime=lifetime,
            dependencies=dependencies
        )
        
        self._services[service_type] = descriptor
        
        logger.debug(f"注册服务: {service_type.__name__} -> {lifetime.value}")
        return self
    
    def resolve(self, service_type: Type[T]) -> T:
        """
        解析服务实例
        
        Args:
            service_type: 服务类型
            
        Returns:
            服务实例
            
        Raises:
            ValueError: 服务未注册或存在循环依赖
        """
        if service_type not in self._services:
            raise ValueError(f"服务 {service_type.__name__} 未注册")
        
        # 检查循环依赖
        if service_type in self._building_stack:
            cycle = " -> ".join([t.__name__ for t in self._building_stack] + [service_type.__name__])
            raise ValueError(f"检测到循环依赖: {cycle}")
        
        descriptor = self._services[service_type]
        
        # 单例模式
        if descriptor.lifetime == ServiceLifetime.SINGLETON:
            if service_type in self._singletons:
                return self._singletons[service_type]
            
            instance = self._create_instance(descriptor)
            self._singletons[service_type] = instance
            return instance
        
        # 作用域模式
        elif descriptor.lifetime == ServiceLifetime.SCOPED:
            if service_type in self._scoped_instances:
                return self._scoped_instances[service_type]
            
            instance = self._create_instance(descriptor)
            self._scoped_instances[service_type] = instance
            return instance
        
        # 瞬态模式
        else:
            return self._create_instance(descriptor)
    
    def _create_instance(self, descriptor: ServiceDescriptor) -> Any:
        """
        创建服务实例
        
        Args:
            descriptor: 服务描述符
            
        Returns:
            服务实例
        """
        # 如果已有实例，直接返回
        if descriptor.instance is not None:
            return descriptor.instance
        
        # 使用工厂函数创建
        if descriptor.factory is not None:
            return self._create_from_factory(descriptor)
        
        # 使用实现类型创建
        if descriptor.implementation_type is not None:
            return self._create_from_type(descriptor)
        
        raise ValueError(f"无法创建服务实例: {descriptor.service_type.__name__}")
    
    def _create_from_factory(self, descriptor: ServiceDescriptor) -> Any:
        """使用工厂函数创建实例"""
        try:
            self._building_stack.append(descriptor.service_type)
            
            # 解析工厂函数的依赖
            factory_args = []
            for dep_type in descriptor.dependencies:
                dep_instance = self.resolve(dep_type)
                factory_args.append(dep_instance)
            
            instance = descriptor.factory(*factory_args)
            return instance
            
        finally:
            self._building_stack.pop()
    
    def _create_from_type(self, descriptor: ServiceDescriptor) -> Any:
        """使用类型创建实例"""
        try:
            self._building_stack.append(descriptor.service_type)
            
            # 解析构造函数依赖
            constructor_args = []
            for dep_type in descriptor.dependencies:
                dep_instance = self.resolve(dep_type)
                constructor_args.append(dep_instance)
            
            instance = descriptor.implementation_type(*constructor_args)
            return instance
            
        finally:
            self._building_stack.pop()
    
    def _analyze_dependencies(self, implementation_type: Type) -> List[Type]:
        """
        分析类型的依赖关系
        
        Args:
            implementation_type: 实现类型
            
        Returns:
            依赖类型列表
        """
        try:
            signature = inspect.signature(implementation_type.__init__)
            dependencies = []
            
            for param_name, param in signature.parameters.items():
                if param_name == 'self':
                    continue
                
                if param.annotation != inspect.Parameter.empty:
                    dependencies.append(param.annotation)
            
            return dependencies
            
        except Exception as e:
            logger.warning(f"分析依赖关系失败 {implementation_type.__name__}: {e}")
            return []
    
    def _analyze_factory_dependencies(self, factory: Callable) -> List[Type]:
        """
        分析工厂函数的依赖关系
        
        Args:
            factory: 工厂函数
            
        Returns:
            依赖类型列表
        """
        try:
            signature = inspect.signature(factory)
            dependencies = []
            
            for param_name, param in signature.parameters.items():
                if param.annotation != inspect.Parameter.empty:
                    dependencies.append(param.annotation)
            
            return dependencies
            
        except Exception as e:
            logger.warning(f"分析工厂函数依赖关系失败: {e}")
            return []
    
    def clear_scoped(self) -> None:
        """清除作用域实例"""
        self._scoped_instances.clear()
        logger.debug("已清除作用域实例")
    
    def is_registered(self, service_type: Type) -> bool:
        """
        检查服务是否已注册
        
        Args:
            service_type: 服务类型
            
        Returns:
            是否已注册
        """
        return service_type in self._services
    
    def get_service_info(self, service_type: Type) -> Optional[Dict[str, Any]]:
        """
        获取服务信息
        
        Args:
            service_type: 服务类型
            
        Returns:
            服务信息字典
        """
        if service_type not in self._services:
            return None
        
        descriptor = self._services[service_type]
        return {
            "service_type": descriptor.service_type.__name__,
            "implementation_type": descriptor.implementation_type.__name__ if descriptor.implementation_type else None,
            "lifetime": descriptor.lifetime.value,
            "has_factory": descriptor.factory is not None,
            "has_instance": descriptor.instance is not None,
            "dependencies": [dep.__name__ for dep in descriptor.dependencies]
        }
    
    def get_all_services(self) -> Dict[str, Dict[str, Any]]:
        """
        获取所有注册的服务信息
        
        Returns:
            所有服务信息
        """
        return {
            service_type.__name__: self.get_service_info(service_type)
            for service_type in self._services.keys()
        }


# 全局容器实例
_global_container: Optional[DependencyContainer] = None


def get_container() -> DependencyContainer:
    """
    获取全局容器实例
    
    Returns:
        全局容器实例
    """
    global _global_container
    if _global_container is None:
        _global_container = DependencyContainer()
    return _global_container


def configure_container() -> DependencyContainer:
    """
    配置全局容器
    
    Returns:
        配置后的容器实例
    """
    global _global_container
    _global_container = DependencyContainer()
    return _global_container


if __name__ == "__main__":
    # 测试代码
    from typing import Protocol
    
    class ITestService(Protocol):
        def test_method(self) -> str: ...
    
    class TestService:
        def test_method(self) -> str:
            return "test"
    
    # 创建容器并注册服务
    container = DependencyContainer()
    container.register_singleton(ITestService, TestService)
    
    # 解析服务
    service = container.resolve(ITestService)
    result = service.test_method()
    
    logger.info(f"测试结果: {result}")
    logger.info(f"服务信息: {container.get_all_services()}")