"""
启动依赖验证模块

提供crypto_ml_strategy项目的启动依赖验证功能，包括外部服务检查、
文件系统验证、资源可用性检查和依赖关系验证。

Author: Crypto ML Strategy Team
Date: 2024-12-20
Version: 1.0.0
"""

import asyncio
import os
import psutil
import socket
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple
from loguru import logger

from .error_handler_core import (
    CryptoMLException, ErrorCode, ErrorSeverity, ErrorCategory,
    ErrorContext, handle_exception
)


@dataclass
class DependencyCheckResult:
    """依赖检查结果"""
    name: str
    success: bool
    message: str
    details: Dict[str, Any] = field(default_factory=dict)
    check_time: datetime = field(default_factory=datetime.now)
    duration: float = 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "name": self.name,
            "success": self.success,
            "message": self.message,
            "details": self.details,
            "check_time": self.check_time.isoformat(),
            "duration": self.duration
        }


class DependencyChecker(ABC):
    """依赖检查器抽象基类"""
    
    @abstractmethod
    async def check(self) -> DependencyCheckResult:
        """执行依赖检查"""
        pass
    
    @abstractmethod
    def get_name(self) -> str:
        """获取检查器名称"""
        pass
    
    @abstractmethod
    def get_timeout(self) -> float:
        """获取检查超时时间"""
        pass


class ExternalServiceChecker(DependencyChecker):
    """外部服务检查器"""
    
    def __init__(
        self,
        service_name: str,
        host: str,
        port: int,
        timeout: float = 5.0,
        protocol: str = "tcp"
    ):
        self.service_name = service_name
        self.host = host
        self.port = port
        self.timeout = timeout
        self.protocol = protocol.lower()
    
    async def check(self) -> DependencyCheckResult:
        """检查外部服务连接"""
        start_time = datetime.now()
        
        try:
            if self.protocol == "tcp":
                result = await self._check_tcp_connection()
            elif self.protocol == "http":
                result = await self._check_http_connection()
            else:
                raise ValueError(f"不支持的协议: {self.protocol}")
            
            duration = (datetime.now() - start_time).total_seconds()
            result.duration = duration
            
            return result
            
        except Exception as e:
            duration = (datetime.now() - start_time).total_seconds()
            logger.error(f"外部服务检查失败 {self.service_name}: {e}")
            
            return DependencyCheckResult(
                name=self.service_name,
                success=False,
                message=f"服务连接失败: {str(e)}",
                details={
                    "host": self.host,
                    "port": self.port,
                    "protocol": self.protocol,
                    "error": str(e)
                },
                duration=duration
            )
    
    async def _check_tcp_connection(self) -> DependencyCheckResult:
        """检查TCP连接"""
        try:
            reader, writer = await asyncio.wait_for(
                asyncio.open_connection(self.host, self.port),
                timeout=self.timeout
            )
            
            writer.close()
            await writer.wait_closed()
            
            return DependencyCheckResult(
                name=self.service_name,
                success=True,
                message=f"TCP连接成功",
                details={
                    "host": self.host,
                    "port": self.port,
                    "protocol": "tcp"
                }
            )
            
        except asyncio.TimeoutError:
            raise CryptoMLException(
                f"连接超时: {self.host}:{self.port}",
                ErrorCode.NETWORK_TIMEOUT,
                ErrorSeverity.ERROR,
                ErrorCategory.NETWORK_ERROR
            )
        except Exception as e:
            raise CryptoMLException(
                f"TCP连接失败: {str(e)}",
                ErrorCode.NETWORK_CONNECTION_FAILED,
                ErrorSeverity.ERROR,
                ErrorCategory.NETWORK_ERROR
            )
    
    async def _check_http_connection(self) -> DependencyCheckResult:
        """检查HTTP连接"""
        try:
            import aiohttp
            
            url = f"http://{self.host}:{self.port}"
            
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
                async with session.get(url) as response:
                    status_code = response.status
                    
                    return DependencyCheckResult(
                        name=self.service_name,
                        success=status_code < 500,  # 5xx错误认为服务不可用
                        message=f"HTTP连接成功 (状态码: {status_code})",
                        details={
                            "host": self.host,
                            "port": self.port,
                            "protocol": "http",
                            "status_code": status_code,
                            "url": url
                        }
                    )
                    
        except Exception as e:
            raise CryptoMLException(
                f"HTTP连接失败: {str(e)}",
                ErrorCode.NETWORK_CONNECTION_FAILED,
                ErrorSeverity.ERROR,
                ErrorCategory.NETWORK_ERROR
            )
    
    def get_name(self) -> str:
        return self.service_name
    
    def get_timeout(self) -> float:
        return self.timeout


class FileSystemValidator(DependencyChecker):
    """文件系统验证器"""
    
    def __init__(
        self,
        name: str,
        required_paths: List[str],
        required_files: List[str] = None,
        required_directories: List[str] = None,
        check_permissions: bool = True
    ):
        self.name = name
        self.required_paths = required_paths
        self.required_files = required_files or []
        self.required_directories = required_directories or []
        self.check_permissions = check_permissions
    
    async def check(self) -> DependencyCheckResult:
        """检查文件系统依赖"""
        start_time = datetime.now()
        
        try:
            missing_paths = []
            permission_errors = []
            details = {
                "checked_paths": [],
                "missing_paths": [],
                "permission_errors": []
            }
            
            # 检查所有路径
            all_paths = self.required_paths + self.required_files + self.required_directories
            
            for path_str in all_paths:
                path = Path(path_str)
                details["checked_paths"].append(str(path))
                
                # 检查路径是否存在
                if not path.exists():
                    missing_paths.append(str(path))
                    details["missing_paths"].append(str(path))
                    continue
                
                # 检查文件类型
                if path_str in self.required_files and not path.is_file():
                    missing_paths.append(f"{path} (不是文件)")
                    continue
                
                if path_str in self.required_directories and not path.is_dir():
                    missing_paths.append(f"{path} (不是目录)")
                    continue
                
                # 检查权限
                if self.check_permissions:
                    try:
                        if path.is_file():
                            # 检查文件读权限
                            with open(path, 'r') as f:
                                pass
                        elif path.is_dir():
                            # 检查目录访问权限
                            list(path.iterdir())
                    except PermissionError:
                        permission_errors.append(str(path))
                        details["permission_errors"].append(str(path))
            
            # 生成结果
            success = len(missing_paths) == 0 and len(permission_errors) == 0
            
            if success:
                message = f"文件系统检查通过 ({len(all_paths)} 个路径)"
            else:
                error_parts = []
                if missing_paths:
                    error_parts.append(f"{len(missing_paths)} 个路径缺失")
                if permission_errors:
                    error_parts.append(f"{len(permission_errors)} 个权限错误")
                message = f"文件系统检查失败: {', '.join(error_parts)}"
            
            duration = (datetime.now() - start_time).total_seconds()
            
            return DependencyCheckResult(
                name=self.name,
                success=success,
                message=message,
                details=details,
                duration=duration
            )
            
        except Exception as e:
            duration = (datetime.now() - start_time).total_seconds()
            logger.error(f"文件系统检查异常 {self.name}: {e}")
            
            return DependencyCheckResult(
                name=self.name,
                success=False,
                message=f"文件系统检查异常: {str(e)}",
                details={"error": str(e)},
                duration=duration
            )
    
    def get_name(self) -> str:
        return self.name
    
    def get_timeout(self) -> float:
        return 10.0  # 文件系统检查通常很快


class ResourceAvailabilityChecker(DependencyChecker):
    """资源可用性检查器"""
    
    def __init__(
        self,
        name: str = "system_resources",
        min_memory_mb: int = 512,
        min_disk_space_mb: int = 1024,
        max_cpu_usage: float = 90.0,
        check_network: bool = True
    ):
        self.name = name
        self.min_memory_mb = min_memory_mb
        self.min_disk_space_mb = min_disk_space_mb
        self.max_cpu_usage = max_cpu_usage
        self.check_network = check_network
    
    async def check(self) -> DependencyCheckResult:
        """检查系统资源可用性"""
        start_time = datetime.now()
        
        try:
            issues = []
            details = {}
            
            # 检查内存
            memory = psutil.virtual_memory()
            available_memory_mb = memory.available / (1024 * 1024)
            details["memory"] = {
                "available_mb": available_memory_mb,
                "total_mb": memory.total / (1024 * 1024),
                "usage_percent": memory.percent
            }
            
            if available_memory_mb < self.min_memory_mb:
                issues.append(f"可用内存不足: {available_memory_mb:.0f}MB < {self.min_memory_mb}MB")
            
            # 检查磁盘空间
            disk = psutil.disk_usage('/')
            available_disk_mb = disk.free / (1024 * 1024)
            details["disk"] = {
                "available_mb": available_disk_mb,
                "total_mb": disk.total / (1024 * 1024),
                "usage_percent": (disk.used / disk.total) * 100
            }
            
            if available_disk_mb < self.min_disk_space_mb:
                issues.append(f"可用磁盘空间不足: {available_disk_mb:.0f}MB < {self.min_disk_space_mb}MB")
            
            # 检查CPU使用率
            cpu_usage = psutil.cpu_percent(interval=1)
            details["cpu"] = {
                "usage_percent": cpu_usage,
                "core_count": psutil.cpu_count()
            }
            
            if cpu_usage > self.max_cpu_usage:
                issues.append(f"CPU使用率过高: {cpu_usage:.1f}% > {self.max_cpu_usage}%")
            
            # 检查网络连接
            if self.check_network:
                network_ok = await self._check_network_connectivity()
                details["network"] = {"connectivity": network_ok}
                
                if not network_ok:
                    issues.append("网络连接不可用")
            
            # 生成结果
            success = len(issues) == 0
            
            if success:
                message = "系统资源检查通过"
            else:
                message = f"系统资源检查失败: {'; '.join(issues)}"
            
            duration = (datetime.now() - start_time).total_seconds()
            
            return DependencyCheckResult(
                name=self.name,
                success=success,
                message=message,
                details=details,
                duration=duration
            )
            
        except Exception as e:
            duration = (datetime.now() - start_time).total_seconds()
            logger.error(f"资源可用性检查异常: {e}")
            
            return DependencyCheckResult(
                name=self.name,
                success=False,
                message=f"资源检查异常: {str(e)}",
                details={"error": str(e)},
                duration=duration
            )
    
    async def _check_network_connectivity(self) -> bool:
        """检查网络连接性"""
        try:
            # 尝试连接到公共DNS服务器
            reader, writer = await asyncio.wait_for(
                asyncio.open_connection("8.8.8.8", 53),
                timeout=3.0
            )
            writer.close()
            await writer.wait_closed()
            return True
        except:
            return False
    
    def get_name(self) -> str:
        return self.name
    
    def get_timeout(self) -> float:
        return 15.0


class EnvironmentValidator(DependencyChecker):
    """环境变量验证器"""
    
    def __init__(
        self,
        name: str = "environment_variables",
        required_vars: List[str] = None,
        optional_vars: List[str] = None
    ):
        self.name = name
        self.required_vars = required_vars or []
        self.optional_vars = optional_vars or []
    
    async def check(self) -> DependencyCheckResult:
        """检查环境变量"""
        start_time = datetime.now()
        
        try:
            missing_vars = []
            present_vars = []
            details = {
                "required_vars": {},
                "optional_vars": {},
                "missing_vars": []
            }
            
            # 检查必需的环境变量
            for var in self.required_vars:
                value = os.getenv(var)
                if value is None:
                    missing_vars.append(var)
                    details["missing_vars"].append(var)
                else:
                    present_vars.append(var)
                    details["required_vars"][var] = "***" if "password" in var.lower() or "secret" in var.lower() else value
            
            # 检查可选的环境变量
            for var in self.optional_vars:
                value = os.getenv(var)
                if value is not None:
                    details["optional_vars"][var] = "***" if "password" in var.lower() or "secret" in var.lower() else value
            
            # 生成结果
            success = len(missing_vars) == 0
            
            if success:
                message = f"环境变量检查通过 ({len(present_vars)} 个必需变量)"
            else:
                message = f"环境变量检查失败: 缺少 {len(missing_vars)} 个必需变量"
            
            duration = (datetime.now() - start_time).total_seconds()
            
            return DependencyCheckResult(
                name=self.name,
                success=success,
                message=message,
                details=details,
                duration=duration
            )
            
        except Exception as e:
            duration = (datetime.now() - start_time).total_seconds()
            logger.error(f"环境变量检查异常: {e}")
            
            return DependencyCheckResult(
                name=self.name,
                success=False,
                message=f"环境变量检查异常: {str(e)}",
                details={"error": str(e)},
                duration=duration
            )
    
    def get_name(self) -> str:
        return self.name
    
    def get_timeout(self) -> float:
        return 5.0


class DependencyValidator:
    """依赖验证器主类"""
    
    def __init__(self):
        self.checkers: List[DependencyChecker] = []
        self.results: Dict[str, DependencyCheckResult] = {}
    
    def add_checker(self, checker: DependencyChecker) -> None:
        """添加依赖检查器"""
        self.checkers.append(checker)
        logger.debug(f"添加依赖检查器: {checker.get_name()}")
    
    def add_service_checker(self, service_name: str, host: str, port: int, **kwargs) -> None:
        """添加外部服务检查器"""
        checker = ExternalServiceChecker(service_name, host, port, **kwargs)
        self.add_checker(checker)
    
    def add_filesystem_checker(self, name: str, **kwargs) -> None:
        """添加文件系统检查器"""
        checker = FileSystemValidator(name, **kwargs)
        self.add_checker(checker)
    
    def add_resource_checker(self, **kwargs) -> None:
        """添加资源可用性检查器"""
        checker = ResourceAvailabilityChecker(**kwargs)
        self.add_checker(checker)
    
    def add_environment_checker(self, **kwargs) -> None:
        """添加环境变量检查器"""
        checker = EnvironmentValidator(**kwargs)
        self.add_checker(checker)
    
    @handle_exception(component="dependency_validator", operation="validate_all")
    async def validate_all(self, parallel: bool = True) -> Dict[str, DependencyCheckResult]:
        """验证所有依赖"""
        logger.info(f"开始依赖验证 ({len(self.checkers)} 个检查器)")
        
        if parallel:
            # 并行执行检查
            results = await asyncio.gather(
                *[self._run_checker_with_timeout(checker) for checker in self.checkers],
                return_exceptions=True
            )
            
            for checker, result in zip(self.checkers, results):
                if isinstance(result, Exception):
                    self.results[checker.get_name()] = DependencyCheckResult(
                        name=checker.get_name(),
                        success=False,
                        message=f"检查器异常: {str(result)}",
                        details={"error": str(result)}
                    )
                else:
                    self.results[checker.get_name()] = result
        else:
            # 串行执行检查
            for checker in self.checkers:
                try:
                    result = await self._run_checker_with_timeout(checker)
                    self.results[checker.get_name()] = result
                except Exception as e:
                    self.results[checker.get_name()] = DependencyCheckResult(
                        name=checker.get_name(),
                        success=False,
                        message=f"检查器异常: {str(e)}",
                        details={"error": str(e)}
                    )
        
        # 统计结果
        total_checks = len(self.results)
        successful_checks = len([r for r in self.results.values() if r.success])
        
        logger.info(f"依赖验证完成: {successful_checks}/{total_checks} 通过")
        
        return self.results
    
    async def _run_checker_with_timeout(self, checker: DependencyChecker) -> DependencyCheckResult:
        """带超时的检查器执行"""
        try:
            return await asyncio.wait_for(
                checker.check(),
                timeout=checker.get_timeout()
            )
        except asyncio.TimeoutError:
            return DependencyCheckResult(
                name=checker.get_name(),
                success=False,
                message=f"检查超时 ({checker.get_timeout()}s)",
                details={"timeout": checker.get_timeout()}
            )
    
    def get_validation_summary(self) -> Dict[str, Any]:
        """获取验证摘要"""
        if not self.results:
            return {"status": "no_validation_performed"}
        
        total_checks = len(self.results)
        successful_checks = len([r for r in self.results.values() if r.success])
        failed_checks = total_checks - successful_checks
        
        total_duration = sum(r.duration for r in self.results.values())
        
        return {
            "total_checks": total_checks,
            "successful_checks": successful_checks,
            "failed_checks": failed_checks,
            "success_rate": successful_checks / total_checks if total_checks > 0 else 0,
            "total_duration": total_duration,
            "all_passed": failed_checks == 0,
            "failed_checkers": [
                r.name for r in self.results.values() if not r.success
            ]
        }


# 全局依赖验证器
global_dependency_validator = DependencyValidator()