"""
错误恢复机制模块

提供crypto_ml_strategy项目的错误恢复功能，包括重试策略、
自动恢复、错误分析和恢复动作执行。

Author: Crypto ML Strategy Team
Date: 2024-12-20
Version: 1.0.0
"""

import asyncio
import random
import time
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Callable, Dict, List, Optional, Type, Union
from loguru import logger

from .error_handler_core import (
    ErrorSeverity, ErrorCategory, ErrorCode, ErrorContext, 
    CryptoMLException, ErrorHandler
)


class RetryStrategy(Enum):
    """重试策略枚举"""
    FIXED_DELAY = "fixed_delay"
    EXPONENTIAL_BACKOFF = "exponential_backoff"
    LINEAR_BACKOFF = "linear_backoff"
    RANDOM_JITTER = "random_jitter"


class RecoveryAction(Enum):
    """恢复动作枚举"""
    RETRY = "retry"
    FALLBACK = "fallback"
    CIRCUIT_BREAK = "circuit_break"
    RESTART_COMPONENT = "restart_component"
    CLEAR_CACHE = "clear_cache"
    SWITCH_ENDPOINT = "switch_endpoint"
    REDUCE_LOAD = "reduce_load"
    MANUAL_INTERVENTION = "manual_intervention"


@dataclass
class RetryPolicy:
    """重试策略配置"""
    max_attempts: int = 3
    base_delay: float = 1.0  # 基础延迟（秒）
    max_delay: float = 60.0  # 最大延迟（秒）
    strategy: RetryStrategy = RetryStrategy.EXPONENTIAL_BACKOFF
    jitter: bool = True  # 是否添加随机抖动
    backoff_multiplier: float = 2.0  # 退避乘数
    retryable_exceptions: List[Type[Exception]] = field(default_factory=list)
    
    def __post_init__(self):
        if not self.retryable_exceptions:
            self.retryable_exceptions = [
                ConnectionError,
                TimeoutError,
                CryptoMLException
            ]
    
    def calculate_delay(self, attempt: int) -> float:
        """计算延迟时间"""
        if self.strategy == RetryStrategy.FIXED_DELAY:
            delay = self.base_delay
        elif self.strategy == RetryStrategy.EXPONENTIAL_BACKOFF:
            delay = self.base_delay * (self.backoff_multiplier ** (attempt - 1))
        elif self.strategy == RetryStrategy.LINEAR_BACKOFF:
            delay = self.base_delay * attempt
        elif self.strategy == RetryStrategy.RANDOM_JITTER:
            delay = random.uniform(self.base_delay, self.max_delay)
        else:
            delay = self.base_delay
        
        # 限制最大延迟
        delay = min(delay, self.max_delay)
        
        # 添加随机抖动
        if self.jitter and self.strategy != RetryStrategy.RANDOM_JITTER:
            jitter_range = delay * 0.1  # 10%的抖动
            delay += random.uniform(-jitter_range, jitter_range)
        
        return max(0, delay)
    
    def is_retryable(self, exception: Exception) -> bool:
        """判断异常是否可重试"""
        return any(
            isinstance(exception, exc_type) 
            for exc_type in self.retryable_exceptions
        )


@dataclass
class RecoveryResult:
    """恢复结果"""
    success: bool
    action: RecoveryAction
    message: str
    timestamp: datetime = field(default_factory=datetime.now)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "success": self.success,
            "action": self.action.value,
            "message": self.message,
            "timestamp": self.timestamp.isoformat(),
            "metadata": self.metadata
        }


class RecoveryStrategy(ABC):
    """恢复策略抽象基类"""
    
    @abstractmethod
    def can_recover(self, error: Exception, context: ErrorContext) -> bool:
        """判断是否可以恢复"""
        pass
    
    @abstractmethod
    async def recover(
        self, 
        error: Exception, 
        context: ErrorContext
    ) -> RecoveryResult:
        """执行恢复"""
        pass
    
    @abstractmethod
    def get_priority(self) -> int:
        """获取优先级"""
        pass


class RetryRecoveryStrategy(RecoveryStrategy):
    """重试恢复策略"""
    
    def __init__(self, retry_policy: Optional[RetryPolicy] = None):
        self.retry_policy = retry_policy or RetryPolicy()
    
    def can_recover(self, error: Exception, context: ErrorContext) -> bool:
        """判断是否可以通过重试恢复"""
        return self.retry_policy.is_retryable(error)
    
    async def recover(
        self, 
        error: Exception, 
        context: ErrorContext
    ) -> RecoveryResult:
        """执行重试恢复"""
        try:
            # 从上下文中获取原始函数和参数
            original_func = context.user_data.get('original_func')
            args = context.user_data.get('args', ())
            kwargs = context.user_data.get('kwargs', {})
            
            if not original_func:
                return RecoveryResult(
                    success=False,
                    action=RecoveryAction.RETRY,
                    message="无法获取原始函数，无法重试"
                )
            
            # 执行重试
            for attempt in range(1, self.retry_policy.max_attempts + 1):
                try:
                    if attempt > 1:
                        delay = self.retry_policy.calculate_delay(attempt)
                        logger.info(f"重试第 {attempt} 次，延迟 {delay:.2f} 秒")
                        await asyncio.sleep(delay)
                    
                    # 执行函数
                    if asyncio.iscoroutinefunction(original_func):
                        result = await original_func(*args, **kwargs)
                    else:
                        result = original_func(*args, **kwargs)
                    
                    return RecoveryResult(
                        success=True,
                        action=RecoveryAction.RETRY,
                        message=f"重试成功，第 {attempt} 次尝试",
                        metadata={"attempts": attempt, "result": str(result)}
                    )
                    
                except Exception as retry_error:
                    if attempt == self.retry_policy.max_attempts:
                        return RecoveryResult(
                            success=False,
                            action=RecoveryAction.RETRY,
                            message=f"重试失败，已达到最大重试次数 {self.retry_policy.max_attempts}",
                            metadata={"attempts": attempt, "last_error": str(retry_error)}
                        )
                    
                    logger.warning(f"重试第 {attempt} 次失败: {retry_error}")
                    continue
            
        except Exception as e:
            logger.error(f"重试恢复策略执行失败: {e}")
            return RecoveryResult(
                success=False,
                action=RecoveryAction.RETRY,
                message=f"重试恢复策略执行失败: {str(e)}"
            )
    
    def get_priority(self) -> int:
        return 100  # 中等优先级


class FallbackRecoveryStrategy(RecoveryStrategy):
    """降级恢复策略"""
    
    def __init__(self, fallback_func: Optional[Callable] = None):
        self.fallback_func = fallback_func
    
    def can_recover(self, error: Exception, context: ErrorContext) -> bool:
        """判断是否可以通过降级恢复"""
        # 检查是否有降级函数或降级数据
        return (
            self.fallback_func is not None or
            'fallback_func' in context.user_data or
            'fallback_data' in context.user_data
        )
    
    async def recover(
        self, 
        error: Exception, 
        context: ErrorContext
    ) -> RecoveryResult:
        """执行降级恢复"""
        try:
            # 获取降级函数
            fallback_func = (
                self.fallback_func or 
                context.user_data.get('fallback_func')
            )
            
            if fallback_func:
                # 执行降级函数
                args = context.user_data.get('args', ())
                kwargs = context.user_data.get('kwargs', {})
                
                if asyncio.iscoroutinefunction(fallback_func):
                    result = await fallback_func(*args, **kwargs)
                else:
                    result = fallback_func(*args, **kwargs)
                
                return RecoveryResult(
                    success=True,
                    action=RecoveryAction.FALLBACK,
                    message="降级函数执行成功",
                    metadata={"result": str(result)}
                )
            
            # 使用降级数据
            fallback_data = context.user_data.get('fallback_data')
            if fallback_data is not None:
                return RecoveryResult(
                    success=True,
                    action=RecoveryAction.FALLBACK,
                    message="使用降级数据",
                    metadata={"fallback_data": fallback_data}
                )
            
            return RecoveryResult(
                success=False,
                action=RecoveryAction.FALLBACK,
                message="没有可用的降级方案"
            )
            
        except Exception as e:
            logger.error(f"降级恢复策略执行失败: {e}")
            return RecoveryResult(
                success=False,
                action=RecoveryAction.FALLBACK,
                message=f"降级恢复策略执行失败: {str(e)}"
            )
    
    def get_priority(self) -> int:
        return 200  # 较低优先级


class CacheRecoveryStrategy(RecoveryStrategy):
    """缓存恢复策略"""
    
    def __init__(self, cache_manager: Optional[Any] = None):
        self.cache_manager = cache_manager
    
    def can_recover(self, error: Exception, context: ErrorContext) -> bool:
        """判断是否可以通过缓存恢复"""
        return (
            self.cache_manager is not None or
            'cache_key' in context.user_data
        )
    
    async def recover(
        self, 
        error: Exception, 
        context: ErrorContext
    ) -> RecoveryResult:
        """执行缓存恢复"""
        try:
            cache_key = context.user_data.get('cache_key')
            if not cache_key:
                return RecoveryResult(
                    success=False,
                    action=RecoveryAction.CLEAR_CACHE,
                    message="没有缓存键，无法使用缓存恢复"
                )
            
            # 尝试从缓存获取数据
            if self.cache_manager and hasattr(self.cache_manager, 'get'):
                cached_data = await self.cache_manager.get(cache_key)
                if cached_data is not None:
                    return RecoveryResult(
                        success=True,
                        action=RecoveryAction.CLEAR_CACHE,
                        message="从缓存恢复数据成功",
                        metadata={"cache_key": cache_key, "data": str(cached_data)}
                    )
            
            return RecoveryResult(
                success=False,
                action=RecoveryAction.CLEAR_CACHE,
                message="缓存中没有可用数据"
            )
            
        except Exception as e:
            logger.error(f"缓存恢复策略执行失败: {e}")
            return RecoveryResult(
                success=False,
                action=RecoveryAction.CLEAR_CACHE,
                message=f"缓存恢复策略执行失败: {str(e)}"
            )
    
    def get_priority(self) -> int:
        return 50  # 高优先级


class ComponentRestartStrategy(RecoveryStrategy):
    """组件重启恢复策略"""
    
    def __init__(self, component_manager: Optional[Any] = None):
        self.component_manager = component_manager
    
    def can_recover(self, error: Exception, context: ErrorContext) -> bool:
        """判断是否可以通过重启组件恢复"""
        return (
            isinstance(error, CryptoMLException) and
            error.severity == ErrorSeverity.CRITICAL and
            context.component != ""
        )
    
    async def recover(
        self, 
        error: Exception, 
        context: ErrorContext
    ) -> RecoveryResult:
        """执行组件重启恢复"""
        try:
            component_name = context.component
            
            if self.component_manager and hasattr(self.component_manager, 'restart_component'):
                success = await self.component_manager.restart_component(component_name)
                
                if success:
                    return RecoveryResult(
                        success=True,
                        action=RecoveryAction.RESTART_COMPONENT,
                        message=f"组件 {component_name} 重启成功",
                        metadata={"component": component_name}
                    )
                else:
                    return RecoveryResult(
                        success=False,
                        action=RecoveryAction.RESTART_COMPONENT,
                        message=f"组件 {component_name} 重启失败"
                    )
            
            return RecoveryResult(
                success=False,
                action=RecoveryAction.RESTART_COMPONENT,
                message="没有可用的组件管理器"
            )
            
        except Exception as e:
            logger.error(f"组件重启恢复策略执行失败: {e}")
            return RecoveryResult(
                success=False,
                action=RecoveryAction.RESTART_COMPONENT,
                message=f"组件重启恢复策略执行失败: {str(e)}"
            )
    
    def get_priority(self) -> int:
        return 300  # 低优先级


class ErrorRecoveryManager:
    """错误恢复管理器"""
    
    def __init__(self):
        self.recovery_strategies: List[RecoveryStrategy] = []
        self.recovery_history: List[Dict[str, Any]] = []
        self.max_history = 1000
        
        # 注册默认恢复策略
        self.register_strategy(CacheRecoveryStrategy())
        self.register_strategy(RetryRecoveryStrategy())
        self.register_strategy(FallbackRecoveryStrategy())
        self.register_strategy(ComponentRestartStrategy())
    
    def register_strategy(self, strategy: RecoveryStrategy) -> None:
        """注册恢复策略"""
        self.recovery_strategies.append(strategy)
        # 按优先级排序
        self.recovery_strategies.sort(key=lambda s: s.get_priority())
        logger.info(f"注册恢复策略: {strategy.__class__.__name__}")
    
    def unregister_strategy(self, strategy_type: Type[RecoveryStrategy]) -> None:
        """注销恢复策略"""
        self.recovery_strategies = [
            s for s in self.recovery_strategies 
            if not isinstance(s, strategy_type)
        ]
        logger.info(f"注销恢复策略: {strategy_type.__name__}")
    
    async def attempt_recovery(
        self, 
        error: Exception, 
        context: ErrorContext
    ) -> Optional[RecoveryResult]:
        """尝试错误恢复"""
        for strategy in self.recovery_strategies:
            if strategy.can_recover(error, context):
                try:
                    logger.info(
                        f"尝试使用策略 {strategy.__class__.__name__} 恢复错误"
                    )
                    
                    result = await strategy.recover(error, context)
                    
                    # 记录恢复历史
                    self._record_recovery(error, context, result)
                    
                    if result.success:
                        logger.info(f"错误恢复成功: {result.message}")
                        return result
                    else:
                        logger.warning(f"错误恢复失败: {result.message}")
                        continue
                        
                except Exception as recovery_error:
                    logger.error(
                        f"恢复策略 {strategy.__class__.__name__} 执行异常: {recovery_error}"
                    )
                    continue
        
        logger.warning("所有恢复策略都失败了")
        return None
    
    def _record_recovery(
        self, 
        error: Exception, 
        context: ErrorContext, 
        result: RecoveryResult
    ) -> None:
        """记录恢复历史"""
        record = {
            "timestamp": datetime.now().isoformat(),
            "error_type": error.__class__.__name__,
            "error_message": str(error),
            "component": context.component,
            "operation": context.operation,
            "recovery_result": result.to_dict()
        }
        
        self.recovery_history.append(record)
        if len(self.recovery_history) > self.max_history:
            self.recovery_history.pop(0)
    
    def get_recovery_statistics(self) -> Dict[str, Any]:
        """获取恢复统计"""
        if not self.recovery_history:
            return {}
        
        total_attempts = len(self.recovery_history)
        successful_recoveries = len([
            r for r in self.recovery_history 
            if r["recovery_result"]["success"]
        ])
        
        # 按恢复动作分组统计
        action_stats = {}
        for record in self.recovery_history:
            action = record["recovery_result"]["action"]
            if action not in action_stats:
                action_stats[action] = {"total": 0, "success": 0}
            
            action_stats[action]["total"] += 1
            if record["recovery_result"]["success"]:
                action_stats[action]["success"] += 1
        
        return {
            "total_attempts": total_attempts,
            "successful_recoveries": successful_recoveries,
            "success_rate": successful_recoveries / total_attempts if total_attempts > 0 else 0,
            "action_statistics": action_stats,
            "recent_recoveries": self.recovery_history[-10:]
        }


class RecoveryErrorHandler(ErrorHandler):
    """恢复错误处理器"""
    
    def __init__(self, recovery_manager: ErrorRecoveryManager):
        self.recovery_manager = recovery_manager
    
    def can_handle(self, error: Exception) -> bool:
        """判断是否可以处理该错误"""
        return True  # 尝试恢复所有错误
    
    async def handle_error(
        self, 
        error: Exception, 
        context: Optional[ErrorContext] = None
    ) -> bool:
        """处理错误"""
        if context is None:
            context = ErrorContext()
        
        result = await self.recovery_manager.attempt_recovery(error, context)
        return result is not None and result.success
    
    def get_priority(self) -> int:
        return 10  # 高优先级


# 全局错误恢复管理器
global_recovery_manager = ErrorRecoveryManager()


def with_recovery(
    retry_policy: Optional[RetryPolicy] = None,
    fallback_func: Optional[Callable] = None,
    cache_key: Optional[str] = None
) -> Callable:
    """
    错误恢复装饰器
    
    Args:
        retry_policy: 重试策略
        fallback_func: 降级函数
        cache_key: 缓存键
    """
    def decorator(func: Callable) -> Callable:
        async def async_wrapper(*args, **kwargs):
            context = ErrorContext()
            context.add_context('original_func', func)
            context.add_context('args', args)
            context.add_context('kwargs', kwargs)
            
            if fallback_func:
                context.add_context('fallback_func', fallback_func)
            if cache_key:
                context.add_context('cache_key', cache_key)
            
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                result = await global_recovery_manager.attempt_recovery(e, context)
                if result and result.success:
                    return result.metadata.get('result')
                raise e
        
        def sync_wrapper(*args, **kwargs):
            context = ErrorContext()
            context.add_context('original_func', func)
            context.add_context('args', args)
            context.add_context('kwargs', kwargs)
            
            if fallback_func:
                context.add_context('fallback_func', fallback_func)
            if cache_key:
                context.add_context('cache_key', cache_key)
            
            try:
                return func(*args, **kwargs)
            except Exception as e:
                async def _async_recovery():
                    return await global_recovery_manager.attempt_recovery(e, context)
                
                try:
                    loop = asyncio.get_event_loop()
                    result = loop.run_until_complete(_async_recovery())
                except RuntimeError:
                    result = asyncio.run(_async_recovery())
                
                if result and result.success:
                    return result.metadata.get('result')
                raise e
        
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator