<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.binance.connector.futures.client.impl</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.html" class="el_class">Classes</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">io.github.binance:binance-futures-connector-java</a> &gt; <span class="el_package">com.binance.connector.futures.client.impl</span></div><h1>com.binance.connector.futures.client.impl</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">1,292 of 1,481</td><td class="ctr2">12%</td><td class="bar">20 of 20</td><td class="ctr2">0%</td><td class="ctr1">78</td><td class="ctr2">100</td><td class="ctr1">186</td><td class="ctr2">223</td><td class="ctr1">68</td><td class="ctr2">90</td><td class="ctr1">3</td><td class="ctr2">6</td></tr></tfoot><tbody><tr><td id="a5"><a href="WebsocketClientImpl.java.html" class="el_source">WebsocketClientImpl.java</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="860" alt="860"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="14" alt="14"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f0">47</td><td class="ctr2" id="g0">47</td><td class="ctr1" id="h0">114</td><td class="ctr2" id="i0">114</td><td class="ctr1" id="j0">40</td><td class="ctr2" id="k0">40</td><td class="ctr1" id="l0">1</td><td class="ctr2" id="m0">1</td></tr><tr><td id="a1"><a href="CMWebsocketClientImpl.java.html" class="el_source">CMWebsocketClientImpl.java</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="35" height="10" title="255" alt="255"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="34" height="10" title="4" alt="4"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f1">12</td><td class="ctr2" id="g2">12</td><td class="ctr1" id="h1">32</td><td class="ctr2" id="i1">32</td><td class="ctr1" id="j1">10</td><td class="ctr2" id="k3">10</td><td class="ctr1" id="l1">1</td><td class="ctr2" id="m1">1</td></tr><tr><td id="a4"><a href="UMWebsocketClientImpl.java.html" class="el_source">UMWebsocketClientImpl.java</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="14" height="10" title="105" alt="105"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="17" height="10" title="2" alt="2"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f2">7</td><td class="ctr2" id="g5">7</td><td class="ctr1" id="h2">16</td><td class="ctr2" id="i5">16</td><td class="ctr1" id="j2">6</td><td class="ctr2" id="k5">6</td><td class="ctr1" id="l2">1</td><td class="ctr2" id="m2">1</td></tr><tr><td id="a0"><a href="CMFuturesClientImpl.java.html" class="el_source">CMFuturesClientImpl.java</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="26" alt="26"/><img src="../jacoco-resources/greenbar.gif" width="9" height="10" title="71" alt="71"/></td><td class="ctr2" id="c1">73%</td><td class="bar" id="d3"/><td class="ctr2" id="e3">n/a</td><td class="ctr1" id="f3">4</td><td class="ctr2" id="g3">11</td><td class="ctr1" id="h3">8</td><td class="ctr2" id="i3">18</td><td class="ctr1" id="j3">4</td><td class="ctr2" id="k2">11</td><td class="ctr1" id="l3">0</td><td class="ctr2" id="m3">1</td></tr><tr><td id="a3"><a href="UMFuturesClientImpl.java.html" class="el_source">UMFuturesClientImpl.java</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="26" alt="26"/><img src="../jacoco-resources/greenbar.gif" width="7" height="10" title="57" alt="57"/></td><td class="ctr2" id="c2">68%</td><td class="bar" id="d4"/><td class="ctr2" id="e4">n/a</td><td class="ctr1" id="f4">4</td><td class="ctr2" id="g4">10</td><td class="ctr1" id="h4">8</td><td class="ctr2" id="i4">17</td><td class="ctr1" id="j4">4</td><td class="ctr2" id="k4">10</td><td class="ctr1" id="l4">0</td><td class="ctr2" id="m4">1</td></tr><tr><td id="a2"><a href="FuturesClientImpl.java.html" class="el_source">FuturesClientImpl.java</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="20" alt="20"/><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="61" alt="61"/></td><td class="ctr2" id="c0">75%</td><td class="bar" id="d5"/><td class="ctr2" id="e5">n/a</td><td class="ctr1" id="f5">4</td><td class="ctr2" id="g1">13</td><td class="ctr1" id="h5">8</td><td class="ctr2" id="i2">26</td><td class="ctr1" id="j5">4</td><td class="ctr2" id="k1">13</td><td class="ctr1" id="l5">0</td><td class="ctr2" id="m5">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.7.202105040129</span></div></body></html>