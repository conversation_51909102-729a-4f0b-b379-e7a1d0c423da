package com.crypto.trading.sdk.interceptor;

import lombok.extern.slf4j.Slf4j;
import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.ResponseBody;
import okio.Buffer;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * @name: LoggingInterceptor
 * @author: zeek
 * @description: 一个OkHttp拦截器，用于详细记录API请求和响应日志。
 *               它会记录请求的URL、方法、头部信息、请求体，以及响应的状态码、头部信息和响应体。
 *               这对于调试和追踪与币安API的交互至关重要。
 * @create: 2024-07-16 02:00
 */
@Slf4j
public class LoggingInterceptor implements Interceptor {

    @Override
    public Response intercept(Chain chain) throws IOException {
        Request request = chain.request();

        long startTime = System.nanoTime();
        log.info("发送请求: {} {}，连接: {}", request.method(), request.url(), chain.connection());
        log.debug("请求头: {}", request.headers());

        // 记录请求体
        if (request.body() != null) {
            Buffer buffer = new Buffer();
            request.body().writeTo(buffer);
            String requestBody = buffer.readString(StandardCharsets.UTF_8);
            log.debug("请求体: {}", requestBody);
        }

        Response response = chain.proceed(request);

        long endTime = System.nanoTime();
        log.info("收到响应: {} {}，耗时: {}ms", response.code(), response.request().url(), (endTime - startTime) / 1e6d);
        log.debug("响应头: {}", response.headers());

        // 记录响应体
        ResponseBody responseBody = response.body();
        if (responseBody != null) {
            // 注意：response.body().string()只能被调用一次。
            // 为了日志记录和后续处理都能使用响应体，我们需要克隆它。
            String responseBodyString = responseBody.string();
            log.debug("响应体: {}", responseBodyString);

            // 创建一个新的响应体包装原始数据，以便上层调用者可以继续使用
            return response.newBuilder()
                    .body(ResponseBody.create(responseBody.contentType(), responseBodyString))
                    .build();
        }

        return response;
    }
}
