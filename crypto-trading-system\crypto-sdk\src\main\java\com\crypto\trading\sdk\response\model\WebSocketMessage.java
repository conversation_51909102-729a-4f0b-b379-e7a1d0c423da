package com.crypto.trading.sdk.response.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Objects;

/**
 * WebSocket消息基类
 * 定义通用的WebSocket消息属性
 */
public class WebSocketMessage<T> {

    /**
     * 事件类型
     */
    @JsonProperty("e")
    private String e;

    /**
     * 事件时间
     */
    @JsonProperty("E")
    private long E;

    /**
     * 交易对
     */
    @JsonProperty("s")
    private String s;

    /**
     * 消息数据
     */
    private T data;

    /**
     * 默认构造函数
     */
    public WebSocketMessage() {
    }

    /**
     * 构造函数
     *
     * @param e    事件类型
     * @param E    事件时间
     * @param s    交易对
     * @param data 消息数据
     */
    public WebSocketMessage(String e, long E, String s, T data) {
        this.e = e;
        this.E = E;
        this.s = s;
        this.data = data;
    }

    /**
     * 获取事件类型
     *
     * @return 事件类型
     */
    public String getEventType() {
        return e;
    }

    /**
     * 设置事件类型
     *
     * @param e 事件类型
     */
    public void setEventType(String e) {
        this.e = e;
    }

    /**
     * 获取事件时间
     *
     * @return 事件时间
     */
    public long getEventTime() {
        return E;
    }

    /**
     * 设置事件时间
     *
     * @param E 事件时间
     */
    public void setEventTime(long E) {
        this.E = E;
    }

    /**
     * 获取交易对
     *
     * @return 交易对
     */
    public String getSymbol() {
        return s;
    }

    /**
     * 设置交易对
     *
     * @param s 交易对
     */
    public void setSymbol(String s) {
        this.s = s;
    }

    /**
     * 获取消息数据
     *
     * @return 消息数据
     */
    public T getData() {
        return data;
    }

    /**
     * 设置消息数据
     *
     * @param data 消息数据
     */
    public void setData(T data) {
        this.data = data;
    }

    @Override
    public String toString() {
        return "WebSocketMessage{" +
                "e='" + e + '\'' +
                ", E=" + E +
                ", s='" + s + '\'' +
                ", data=" + data +
                '}';
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        WebSocketMessage<?> that = (WebSocketMessage<?>) o;
        return E == that.E &&
                Objects.equals(e, that.e) &&
                Objects.equals(s, that.s) &&
                Objects.equals(data, that.data);
    }

    @Override
    public int hashCode() {
        return Objects.hash(e, E, s, data);
    }
} 