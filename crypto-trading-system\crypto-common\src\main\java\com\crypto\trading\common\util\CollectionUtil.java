package com.crypto.trading.common.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * 集合工具类
 * <p>
 * 提供集合操作相关的工具方法，如判空、转换、过滤等
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public final class CollectionUtil {
    private static final Logger log = LoggerFactory.getLogger(CollectionUtil.class);

    /**
     * 私有构造函数，防止实例化
     */
    private CollectionUtil() {
        throw new IllegalStateException("工具类不允许实例化");
    }

    /**
     * 判断集合是否为空
     *
     * @param collection 集合
     * @param <T>        元素类型
     * @return 是否为空
     */
    public static <T> boolean isEmpty(Collection<T> collection) {
        return collection == null || collection.isEmpty();
    }

    /**
     * 判断集合是否不为空
     *
     * @param collection 集合
     * @param <T>        元素类型
     * @return 是否不为空
     */
    public static <T> boolean isNotEmpty(Collection<T> collection) {
        return !isEmpty(collection);
    }

    /**
     * 判断Map是否为空
     *
     * @param map Map
     * @param <K> 键类型
     * @param <V> 值类型
     * @return 是否为空
     */
    public static <K, V> boolean isEmpty(Map<K, V> map) {
        return map == null || map.isEmpty();
    }

    /**
     * 判断Map是否不为空
     *
     * @param map Map
     * @param <K> 键类型
     * @param <V> 值类型
     * @return 是否不为空
     */
    public static <K, V> boolean isNotEmpty(Map<K, V> map) {
        return !isEmpty(map);
    }

    /**
     * 判断数组是否为空
     *
     * @param array 数组
     * @param <T>   元素类型
     * @return 是否为空
     */
    public static <T> boolean isEmpty(T[] array) {
        return array == null || array.length == 0;
    }

    /**
     * 判断数组是否不为空
     *
     * @param array 数组
     * @param <T>   元素类型
     * @return 是否不为空
     */
    public static <T> boolean isNotEmpty(T[] array) {
        return !isEmpty(array);
    }

    /**
     * 获取集合的大小
     *
     * @param collection 集合
     * @param <T>        元素类型
     * @return 集合大小，如果集合为空则返回0
     */
    public static <T> int size(Collection<T> collection) {
        return collection == null ? 0 : collection.size();
    }

    /**
     * 获取Map的大小
     *
     * @param map Map
     * @param <K> 键类型
     * @param <V> 值类型
     * @return Map大小，如果Map为空则返回0
     */
    public static <K, V> int size(Map<K, V> map) {
        return map == null ? 0 : map.size();
    }

    /**
     * 获取数组的大小
     *
     * @param array 数组
     * @param <T>   元素类型
     * @return 数组大小，如果数组为空则返回0
     */
    public static <T> int size(T[] array) {
        return array == null ? 0 : array.length;
    }

    /**
     * 将集合转换为List
     *
     * @param collection 集合
     * @param <T>        元素类型
     * @return List，如果集合为空则返回空List
     */
    public static <T> List<T> toList(Collection<T> collection) {
        return isEmpty(collection) ? new ArrayList<>() : new ArrayList<>(collection);
    }

    /**
     * 将数组转换为List
     *
     * @param array 数组
     * @param <T>   元素类型
     * @return List，如果数组为空则返回空List
     */
    @SafeVarargs
    public static <T> List<T> toList(T... array) {
        if (isEmpty(array)) {
            return new ArrayList<>();
        }
        return Arrays.stream(array).collect(Collectors.toList());
    }

    /**
     * 将集合转换为Set
     *
     * @param collection 集合
     * @param <T>        元素类型
     * @return Set，如果集合为空则返回空Set
     */
    public static <T> Set<T> toSet(Collection<T> collection) {
        return isEmpty(collection) ? new HashSet<>() : new HashSet<>(collection);
    }

    /**
     * 将数组转换为Set
     *
     * @param array 数组
     * @param <T>   元素类型
     * @return Set，如果数组为空则返回空Set
     */
    @SafeVarargs
    public static <T> Set<T> toSet(T... array) {
        if (isEmpty(array)) {
            return new HashSet<>();
        }
        return Arrays.stream(array).collect(Collectors.toSet());
    }

    /**
     * 集合转Map
     *
     * @param collection    集合
     * @param keyExtractor  键提取器
     * @param valueExtractor 值提取器
     * @param <T>           元素类型
     * @param <K>           键类型
     * @param <V>           值类型
     * @return Map，如果集合为空则返回空Map
     */
    public static <T, K, V> Map<K, V> toMap(Collection<T> collection,
                                           Function<T, K> keyExtractor,
                                           Function<T, V> valueExtractor) {
        if (isEmpty(collection)) {
            return new HashMap<>();
        }
        return collection.stream()
                .collect(Collectors.toMap(keyExtractor, valueExtractor, (v1, v2) -> v1));
    }

    /**
     * 过滤集合元素
     *
     * @param collection 集合
     * @param predicate  条件
     * @param <T>        元素类型
     * @return 过滤后的集合
     */
    public static <T> List<T> filter(Collection<T> collection, Predicate<T> predicate) {
        if (isEmpty(collection)) {
            return new ArrayList<>();
        }
        return collection.stream()
                .filter(predicate)
                .collect(Collectors.toList());
    }

    /**
     * 集合元素转换
     *
     * @param collection 集合
     * @param mapper     转换函数
     * @param <T>        原元素类型
     * @param <R>        目标元素类型
     * @return 转换后的集合
     */
    public static <T, R> List<R> map(Collection<T> collection, Function<T, R> mapper) {
        if (isEmpty(collection)) {
            return new ArrayList<>();
        }
        return collection.stream()
                .map(mapper)
                .collect(Collectors.toList());
    }

    /**
     * 集合元素分组
     *
     * @param collection   集合
     * @param keyExtractor 分组键提取器
     * @param <T>          元素类型
     * @param <K>          分组键类型
     * @return 分组后的Map
     */
    public static <T, K> Map<K, List<T>> groupBy(Collection<T> collection, Function<T, K> keyExtractor) {
        if (isEmpty(collection)) {
            return new HashMap<>();
        }
        return collection.stream()
                .collect(Collectors.groupingBy(keyExtractor));
    }

    /**
     * 获取集合第一个元素
     *
     * @param collection 集合
     * @param <T>        元素类型
     * @return 第一个元素，如果集合为空则返回null
     */
    public static <T> T getFirst(Collection<T> collection) {
        if (isEmpty(collection)) {
            return null;
        }
        return collection.iterator().next();
    }

    /**
     * 获取集合最后一个元素
     *
     * @param list 列表
     * @param <T>  元素类型
     * @return 最后一个元素，如果列表为空则返回null
     */
    public static <T> T getLast(List<T> list) {
        if (isEmpty(list)) {
            return null;
        }
        return list.get(list.size() - 1);
    }

    /**
     * 集合并集
     *
     * @param collection1 集合1
     * @param collection2 集合2
     * @param <T>         元素类型
     * @return 并集
     */
    public static <T> Set<T> union(Collection<T> collection1, Collection<T> collection2) {
        Set<T> result = new HashSet<>();
        if (isNotEmpty(collection1)) {
            result.addAll(collection1);
        }
        if (isNotEmpty(collection2)) {
            result.addAll(collection2);
        }
        return result;
    }

    /**
     * 集合交集
     *
     * @param collection1 集合1
     * @param collection2 集合2
     * @param <T>         元素类型
     * @return 交集
     */
    public static <T> Set<T> intersection(Collection<T> collection1, Collection<T> collection2) {
        if (isEmpty(collection1) || isEmpty(collection2)) {
            return new HashSet<>();
        }
        Set<T> result = new HashSet<>(collection1);
        result.retainAll(collection2);
        return result;
    }

    /**
     * 集合差集（collection1 - collection2）
     *
     * @param collection1 集合1
     * @param collection2 集合2
     * @param <T>         元素类型
     * @return 差集
     */
    public static <T> Set<T> difference(Collection<T> collection1, Collection<T> collection2) {
        if (isEmpty(collection1)) {
            return new HashSet<>();
        }
        Set<T> result = new HashSet<>(collection1);
        if (isNotEmpty(collection2)) {
            result.removeAll(collection2);
        }
        return result;
    }
} 