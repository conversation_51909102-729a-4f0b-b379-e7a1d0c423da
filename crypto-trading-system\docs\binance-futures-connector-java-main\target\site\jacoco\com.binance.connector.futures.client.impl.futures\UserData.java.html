<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>UserData.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">io.github.binance:binance-futures-connector-java</a> &gt; <a href="index.source.html" class="el_package">com.binance.connector.futures.client.impl.futures</a> &gt; <span class="el_source">UserData.java</span></div><h1>UserData.java</h1><pre class="source lang-java linenums">package com.binance.connector.futures.client.impl.futures;

import com.binance.connector.futures.client.enums.HttpMethod;
import com.binance.connector.futures.client.utils.ProxyAuth;
import com.binance.connector.futures.client.utils.RequestHandler;

/**
 * &lt;h2&gt;User Data Streams Endpoints&lt;/h2&gt;
 * Response will be returned in &lt;i&gt;String format&lt;/i&gt;.
 */
public abstract class UserData {
    private String productUrl;
    private RequestHandler requestHandler;
    private boolean showLimitUsage;

<span class="fc" id="L16">    public UserData(String productUrl, String apiKey, boolean showLimitUsage, ProxyAuth proxy) {</span>
<span class="fc" id="L17">        this.productUrl = productUrl;</span>
<span class="fc" id="L18">        this.requestHandler = new RequestHandler(apiKey, proxy);</span>
<span class="fc" id="L19">        this.showLimitUsage = showLimitUsage;</span>
<span class="fc" id="L20">    }</span>

    public String getProductUrl() {
<span class="nc" id="L23">        return this.productUrl;</span>
    }

    public RequestHandler getRequestHandler() {
<span class="nc" id="L27">        return this.requestHandler;</span>
    }

    public boolean getShowLimitUsage() {
<span class="nc" id="L31">        return this.showLimitUsage;</span>
    }

    public void setProductUrl(String productUrl) {
<span class="nc" id="L35">        this.productUrl = productUrl;</span>
<span class="nc" id="L36">    }</span>

    public void setRequestHandler(String apiKey, ProxyAuth proxy) {
<span class="nc" id="L39">        this.requestHandler = new RequestHandler(apiKey, proxy);</span>
<span class="nc" id="L40">    }</span>

    public void setShowLimitUsage(boolean showLimitUsage) {
<span class="nc" id="L43">        this.showLimitUsage = showLimitUsage;</span>
<span class="nc" id="L44">    }</span>

<span class="fc" id="L46">    private final String LISTEN_KEY = &quot;/v1/listenKey&quot;;</span>
    /**
     * Start a new user data stream. The stream will close after 60 minutes unless a keepalive is sent.
     * If the account has an active listenKey, that listenKey will be returned and its validity will be extended for 60 minutes.
     * &lt;br&gt;&lt;br&gt;
     * POST /v1/listenKey
     * &lt;br&gt;
     * @return String
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/usds-margined-futures/user-data-streams/Start-User-Data-Stream&quot;&gt;
     *     https://developers.binance.com/docs/derivatives/usds-margined-futures/user-data-streams/Start-User-Data-Stream&lt;/a&gt;
     */
    public String createListenKey() {
<span class="fc" id="L58">        return requestHandler.sendWithApiKeyRequest(productUrl, LISTEN_KEY, null, HttpMethod.POST, showLimitUsage);</span>
    }

    /**
     * Keepalive a user data stream to prevent a time out. User data streams will close after 60 minutes.
     * It's recommended to send a ping about every 60 minutes.
     * &lt;br&gt;&lt;br&gt;
     * PUT /v1/listenKey
     * &lt;br&gt;
     * @return String
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/usds-margined-futures/user-data-streams/Keepalive-User-Data-Stream&quot;&gt;
     *     https://developers.binance.com/docs/derivatives/usds-margined-futures/user-data-streams/Keepalive-User-Data-Stream&lt;/a&gt;
     */
    public String extendListenKey() {
<span class="fc" id="L72">        return requestHandler.sendWithApiKeyRequest(productUrl, LISTEN_KEY, null, HttpMethod.PUT, showLimitUsage);</span>
    }

    /**
     * Close out a user data stream.
     * &lt;br&gt;&lt;br&gt;
     * DELETE /v1/listenKey
     * &lt;br&gt;
     * @return String
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/usds-margined-futures/user-data-streams/Close-User-Data-Stream&quot;&gt;
     *     https://developers.binance.com/docs/derivatives/usds-margined-futures/user-data-streams/Close-User-Data-Stream&lt;/a&gt;
     */
    public String closeListenKey() {
<span class="fc" id="L85">        return requestHandler.sendWithApiKeyRequest(productUrl, LISTEN_KEY, null, HttpMethod.DELETE, showLimitUsage);</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.7.202105040129</span></div></body></html>