package com.crypto.trading.trade.model.entity.order;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 订单执行日志实体类
 * <p>
 * 记录订单执行过程中的各种操作和结果
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@TableName("t_order_execution_log")
public class OrderExecutionLogEntity {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 执行ID
     */
    private String executionId;

    /**
     * 订单ID
     */
    private String orderId;

    /**
     * 客户端订单ID
     */
    private String clientOrderId;

    /**
     * 操作类型
     */
    private String action;

    /**
     * 请求数据
     */
    private String requestData;

    /**
     * 响应数据
     */
    private String responseData;

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 错误代码
     */
    private String errorCode;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    @TableField(fill = FieldFill.INSERT)
    private Long createdTime;

    /**
     * 获取主键ID
     *
     * @return 主键ID
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置主键ID
     *
     * @param id 主键ID
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取执行ID
     *
     * @return 执行ID
     */
    public String getExecutionId() {
        return executionId;
    }

    /**
     * 设置执行ID
     *
     * @param executionId 执行ID
     */
    public void setExecutionId(String executionId) {
        this.executionId = executionId;
    }

    /**
     * 获取订单ID
     *
     * @return 订单ID
     */
    public String getOrderId() {
        return orderId;
    }

    /**
     * 设置订单ID
     *
     * @param orderId 订单ID
     */
    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    /**
     * 获取客户端订单ID
     *
     * @return 客户端订单ID
     */
    public String getClientOrderId() {
        return clientOrderId;
    }

    /**
     * 设置客户端订单ID
     *
     * @param clientOrderId 客户端订单ID
     */
    public void setClientOrderId(String clientOrderId) {
        this.clientOrderId = clientOrderId;
    }

    /**
     * 获取操作类型
     *
     * @return 操作类型
     */
    public String getAction() {
        return action;
    }

    /**
     * 设置操作类型
     *
     * @param action 操作类型
     */
    public void setAction(String action) {
        this.action = action;
    }

    /**
     * 获取请求数据
     *
     * @return 请求数据
     */
    public String getRequestData() {
        return requestData;
    }

    /**
     * 设置请求数据
     *
     * @param requestData 请求数据
     */
    public void setRequestData(String requestData) {
        this.requestData = requestData;
    }

    /**
     * 获取响应数据
     *
     * @return 响应数据
     */
    public String getResponseData() {
        return responseData;
    }

    /**
     * 设置响应数据
     *
     * @param responseData 响应数据
     */
    public void setResponseData(String responseData) {
        this.responseData = responseData;
    }

    /**
     * 获取是否成功
     *
     * @return 是否成功
     */
    public Boolean getSuccess() {
        return success;
    }

    /**
     * 设置是否成功
     *
     * @param success 是否成功
     */
    public void setSuccess(Boolean success) {
        this.success = success;
    }

    /**
     * 获取错误代码
     *
     * @return 错误代码
     */
    public String getErrorCode() {
        return errorCode;
    }

    /**
     * 设置错误代码
     *
     * @param errorCode 错误代码
     */
    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    /**
     * 获取错误信息
     *
     * @return 错误信息
     */
    public String getErrorMessage() {
        return errorMessage;
    }

    /**
     * 设置错误信息
     *
     * @param errorMessage 错误信息
     */
    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    /**
     * 获取创建时间
     *
     * @return 创建时间
     */
    public Long getCreatedTime() {
        return createdTime;
    }

    /**
     * 设置创建时间
     *
     * @param createdTime 创建时间
     */
    public void setCreatedTime(Long createdTime) {
        this.createdTime = createdTime;
    }

    /**
     * 构建器类
     */
    public static class Builder {
        private final OrderExecutionLogEntity entity;

        /**
         * 构造函数
         */
        public Builder() {
            entity = new OrderExecutionLogEntity();
        }

        /**
         * 设置执行ID
         *
         * @param executionId 执行ID
         * @return Builder实例
         */
        public Builder executionId(String executionId) {
            entity.setExecutionId(executionId);
            return this;
        }

        /**
         * 设置订单ID
         *
         * @param orderId 订单ID
         * @return Builder实例
         */
        public Builder orderId(String orderId) {
            entity.setOrderId(orderId);
            return this;
        }

        /**
         * 设置客户端订单ID
         *
         * @param clientOrderId 客户端订单ID
         * @return Builder实例
         */
        public Builder clientOrderId(String clientOrderId) {
            entity.setClientOrderId(clientOrderId);
            return this;
        }

        /**
         * 设置操作类型
         *
         * @param action 操作类型
         * @return Builder实例
         */
        public Builder action(String action) {
            entity.setAction(action);
            return this;
        }

        /**
         * 设置请求数据
         *
         * @param requestData 请求数据
         * @return Builder实例
         */
        public Builder requestData(String requestData) {
            entity.setRequestData(requestData);
            return this;
        }

        /**
         * 设置响应数据
         *
         * @param responseData 响应数据
         * @return Builder实例
         */
        public Builder responseData(String responseData) {
            entity.setResponseData(responseData);
            return this;
        }

        /**
         * 设置是否成功
         *
         * @param success 是否成功
         * @return Builder实例
         */
        public Builder success(Boolean success) {
            entity.setSuccess(success);
            return this;
        }

        /**
         * 设置错误代码
         *
         * @param errorCode 错误代码
         * @return Builder实例
         */
        public Builder errorCode(String errorCode) {
            entity.setErrorCode(errorCode);
            return this;
        }

        /**
         * 设置错误信息
         *
         * @param errorMessage 错误信息
         * @return Builder实例
         */
        public Builder errorMessage(String errorMessage) {
            entity.setErrorMessage(errorMessage);
            return this;
        }

        /**
         * 设置创建时间
         *
         * @param createdTime 创建时间
         * @return Builder实例
         */
        public Builder createdTime(Long createdTime) {
            entity.setCreatedTime(createdTime);
            return this;
        }

        /**
         * 构建OrderExecutionLogEntity实例
         *
         * @return OrderExecutionLogEntity实例
         */
        public OrderExecutionLogEntity build() {
            return entity;
        }
    }
}