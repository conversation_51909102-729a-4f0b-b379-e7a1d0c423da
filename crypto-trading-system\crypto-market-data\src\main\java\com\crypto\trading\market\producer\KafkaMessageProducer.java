package com.crypto.trading.market.producer;

import com.crypto.trading.common.config.KafkaConfig;
import com.crypto.trading.market.config.KafkaProducerConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.SmartLifecycle;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Kafka消息生产者
 * 负责向Kafka发送消息，支持批处理和异步发送，提高传输性能
 * 实现SmartLifecycle接口以控制Bean的启动和关闭顺序
 */
@Component
public class KafkaMessageProducer implements SmartLifecycle {

    private static final Logger log = LoggerFactory.getLogger(KafkaMessageProducer.class);

    @Autowired
    private KafkaTemplate<String, String> kafkaTemplate;
    
    @Autowired
    private KafkaTemplate<String, Object> jsonKafkaTemplate;
    
    @Autowired
    private KafkaTemplate<String, Object> avroKafkaTemplate;

    @Autowired
    private KafkaProducerConfig kafkaProducerConfig;
    
    @Autowired
    private KafkaConfig kafkaConfig;
    
    /**
     * 是否启用批处理
     */
    @Value("${market.kafka.enable-batch:true}")
    private boolean enableBatch;
    
    /**
     * 批处理大小
     */
    @Value("${market.kafka.batch-size:100}")
    private int batchSize;
    
    /**
     * 批处理最大等待时间（毫秒）
     */
    @Value("${market.kafka.batch-wait-ms:1000}")
    private int batchWaitMs;
    
    /**
     * 消息计数器
     */
    private final AtomicLong messageCounter = new AtomicLong(0);
    
    /**
     * 消息批处理缓存（按主题和键分组）
     */
    private final Map<String, Map<String, List<String>>> batchCache = new ConcurrentHashMap<>();
    
    /**
     * 批处理调度器
     */
    private ScheduledExecutorService scheduler;
    
    /**
     * 关闭标志，使用volatile确保多线程可见性
     */
    private volatile boolean isShuttingDown = false;
    
    /**
     * 操作计数器，用于跟踪正在进行的发送操作
     */
    private final AtomicInteger pendingOperations = new AtomicInteger(0);
    
    /**
     * 关闭锁，用于等待所有操作完成
     */
    private final CountDownLatch shutdownLatch = new CountDownLatch(1);
    
    /**
     * 运行状态标志，用于SmartLifecycle接口
     */
    private boolean running = false;
    
    /**
     * 初始化
     */
    @PostConstruct
    public void init() {
        if (enableBatch) {
            log.info("初始化Kafka批处理发送模式: batchSize={}, batchWaitMs={}ms", batchSize, batchWaitMs);
            
            // 创建虚拟线程调度器
            scheduler = Executors.newScheduledThreadPool(
                1, 
                r -> Thread.ofVirtual().name("kafka-batch-processor").unstarted(r)
            );
            
            // 定期刷新批处理缓存
            scheduler.scheduleAtFixedRate(
                this::flushAllBatches, 
                batchWaitMs, 
                batchWaitMs, 
                TimeUnit.MILLISECONDS
            );
        } else {
            log.info("Kafka批处理发送模式未启用，将使用即时发送模式");
        }
    }
    
    /**
     * 销毁时清理资源
     */
    @PreDestroy
    public void destroy() {
        log.info("Kafka消息生产者开始关闭，当前处理{}条消息，等待所有发送操作完成...", messageCounter.get());
        
        // 设置关闭标志
        isShuttingDown = true;
        
        try {
            // 先等待批处理操作完成
            if (scheduler != null && !scheduler.isShutdown()) {
                // 停止接收新的批处理任务
                scheduler.shutdown();
                
                // 等待批处理任务完成
                if (!scheduler.awaitTermination(3, TimeUnit.SECONDS)) {
                    log.warn("批处理调度器未能在超时时间内完成，强制关闭");
                    scheduler.shutdownNow();
                }
            }
            
            // 等待所有发送操作完成
            int pendingOps = pendingOperations.get();
            if (pendingOps > 0) {
                log.info("等待{}个发送操作完成...", pendingOps);
                
                // 设置最大等待时间
                long startTime = System.currentTimeMillis();
                long maxWaitTime = 30000; // 增加到30秒
                
                while (pendingOperations.get() > 0 && (System.currentTimeMillis() - startTime) < maxWaitTime) {
                    try {
                        // 每100ms检查一次
                        Thread.sleep(100);
                        
                        // 每秒记录一次日志
                        if ((System.currentTimeMillis() - startTime) % 1000 < 100) {
                            log.info("仍在等待{}个发送操作完成...", pendingOperations.get());
                        }
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        log.warn("等待发送操作完成时被中断");
                        break;
                    }
                }
                
                if (pendingOperations.get() > 0) {
                    log.warn("等待发送操作完成超时，仍有{}个操作未完成", pendingOperations.get());
                }
            }
        } catch (InterruptedException e) {
            log.warn("等待发送操作完成时被中断", e);
            Thread.currentThread().interrupt();
        } finally {
            // 释放关闭锁，通知所有等待的线程
            shutdownLatch.countDown();
            
            log.info("Kafka消息生产者已关闭，共处理{}条消息，未完成{}个操作", 
                    messageCounter.get(), pendingOperations.get());
        }
    }

    /**
     * 发送K线数据到Kafka
     *
     * @param key     消息键
     * @param message 消息内容
     */
    public void sendKlineData(String key, String message) {
        String topic = kafkaProducerConfig.getKlineTopic();
        sendMessage(topic, key, message);
    }

    /**
     * 发送深度数据到Kafka
     *
     * @param key     消息键
     * @param message 消息内容
     */
    public void sendDepthData(String key, String message) {
        String topic = kafkaProducerConfig.getDepthTopic();
        sendMessage(topic, key, message);
    }

    /**
     * 发送交易数据到Kafka
     *
     * @param key     消息键
     * @param message 消息内容
     */
    public void sendTradeData(String key, String message) {
        String topic = kafkaProducerConfig.getTradeTopic();
        sendMessage(topic, key, message);
    }

    /**
     * 发送原始K线数据到Kafka进行异步数据库存储
     *
     * @param key     消息键
     * @param message 消息内容
     */
    public void sendRawKlineData(String key, String message) {
        String topic = "kline.raw.data";
        sendMessage(topic, key, message);
    }

    /**
     * 发送原始深度数据到Kafka进行异步数据库存储
     *
     * @param key     消息键
     * @param message 消息内容
     */
    public void sendRawDepthData(String key, String message) {
        String topic = "depth.raw.data";
        sendMessage(topic, key, message);
    }

    /**
     * 发送原始交易数据到Kafka进行异步数据库存储
     *
     * @param key     消息键
     * @param message 消息内容
     */
    public void sendRawTradeData(String key, String message) {
        String topic = "trade.raw.data";
        sendMessage(topic, key, message);
    }
    
    /**
     * 批量发送K线数据到Kafka
     * 
     * @param key 消息键
     * @param messages 消息内容列表
     */
    public void sendKlineDataBatch(String key, List<String> messages) {
        String topic = kafkaProducerConfig.getKlineTopic();
        sendMessageBatch(topic, key, messages);
    }
    
    /**
     * 批量发送深度数据到Kafka
     * 
     * @param key 消息键
     * @param messages 消息内容列表
     */
    public void sendDepthDataBatch(String key, List<String> messages) {
        String topic = kafkaProducerConfig.getDepthTopic();
        sendMessageBatch(topic, key, messages);
    }
    
    /**
     * 批量发送交易数据到Kafka
     * 
     * @param key 消息键
     * @param messages 消息内容列表
     */
    public void sendTradeDataBatch(String key, List<String> messages) {
        String topic = kafkaProducerConfig.getTradeTopic();
        sendMessageBatch(topic, key, messages);
    }
    
    /**
     * 发送对象数据到Kafka（使用JSON序列化）
     *
     * @param topic 主题
     * @param key   消息键
     * @param data  对象数据
     * @param <T>   对象类型
     */
    public <T> void sendObjectData(String topic, String key, T data) {
        // 如果正在关闭，则不发送新消息
        if (isShuttingDown) {
            log.warn("Kafka消息生产者正在关闭，忽略对象发送请求: topic={}, key={}, type={}", 
                    topic, key, data.getClass().getSimpleName());
            return;
        }
        
        // 再次检查是否正在关闭（防止在上一次检查后状态发生变化）
        if (isShuttingDown) {
            log.warn("Kafka消息生产者正在关闭，忽略对象发送请求: topic={}, key={}, type={}", 
                    topic, key, data.getClass().getSimpleName());
            return;
        }
        
        try {
            // 增加待处理操作计数
            pendingOperations.incrementAndGet();
            
            // 使用虚拟线程异步发送消息
            Thread.startVirtualThread(() -> {
                try {
                    // 再次检查是否正在关闭
                    if (isShuttingDown) {
                        log.warn("Kafka消息生产者正在关闭，取消对象发送: topic={}, key={}, type={}", 
                                topic, key, data.getClass().getSimpleName());
                        pendingOperations.decrementAndGet();
                        return;
                    }
                    
                    // 第三次检查是否正在关闭（防止在启动虚拟线程后状态发生变化）
                    if (isShuttingDown) {
                        log.warn("Kafka消息生产者正在关闭，取消对象发送: topic={}, key={}, type={}", 
                                topic, key, data.getClass().getSimpleName());
                        pendingOperations.decrementAndGet();
                        return;
                    }
                    
                    CompletableFuture<SendResult<String, Object>> future = jsonKafkaTemplate.send(topic, key, data);
                    
                    future.whenComplete((result, ex) -> {
                        try {
                            if (ex == null) {
                                if (log.isDebugEnabled()) {
                                    log.debug("发送对象数据到Kafka成功: topic={}, key={}, type={}, partition={}, offset={}", 
                                            topic, key, data.getClass().getSimpleName(),
                                            result.getRecordMetadata().partition(), 
                                            result.getRecordMetadata().offset());
                                }
                            } else {
                                // 如果是因为关闭导致的异常，使用警告级别而不是错误级别
                                if (isShuttingDown && 
                                    (ex instanceof org.apache.kafka.common.KafkaException || 
                                     ex.getCause() instanceof org.apache.kafka.common.KafkaException ||
                                     ex instanceof org.springframework.kafka.KafkaException ||
                                     ex.getCause() instanceof org.springframework.kafka.KafkaException) &&
                                    (ex.getMessage().contains("Producer closed") || 
                                     (ex.getCause() != null && ex.getCause().getMessage() != null && 
                                      ex.getCause().getMessage().contains("Producer closed")))) {
                                    log.warn("发送对象数据时Kafka生产者已关闭: topic={}, key={}, type={}", 
                                            topic, key, data.getClass().getSimpleName());
                                } else {
                                    log.error("发送对象数据到Kafka失败: topic={}, key={}, type={}", 
                                            topic, key, data.getClass().getSimpleName(), ex);
                                }
                            }
                        } finally {
                            // 减少待处理操作计数
                            pendingOperations.decrementAndGet();
                        }
                    });
                    
                    messageCounter.incrementAndGet();
                } catch (Exception e) {
                    // 如果是因为关闭导致的异常，使用警告级别而不是错误级别
                    if (isShuttingDown && 
                        (e instanceof org.apache.kafka.common.KafkaException || 
                         e.getCause() instanceof org.apache.kafka.common.KafkaException ||
                         e instanceof org.springframework.kafka.KafkaException ||
                         e.getCause() instanceof org.springframework.kafka.KafkaException) &&
                        (e.getMessage().contains("Producer closed") || 
                         (e.getCause() != null && e.getCause().getMessage() != null && 
                          e.getCause().getMessage().contains("Producer closed")))) {
                        log.warn("发送对象数据时Kafka生产者已关闭: topic={}, key={}, type={}", 
                                topic, key, data.getClass().getSimpleName());
                    } else {
                        log.error("发送对象数据到Kafka异常: topic={}, key={}, type={}", 
                                topic, key, data.getClass().getSimpleName(), e);
                    }
                    
                    // 减少待处理操作计数
                    pendingOperations.decrementAndGet();
                }
            });
        } catch (Exception e) {
            log.error("创建虚拟线程异常: topic={}, key={}, type={}", 
                    topic, key, data.getClass().getSimpleName(), e);
            // 减少待处理操作计数
            pendingOperations.decrementAndGet();
            throw e;
        }
    }
    
    /**
     * 发送对象数据到Kafka（使用Avro序列化）
     *
     * @param topic 主题
     * @param key   消息键
     * @param data  Avro对象数据
     * @param <T>   对象类型
     */
    public <T> void sendAvroData(String topic, String key, T data) {
        // 如果未启用Avro序列化，则使用JSON序列化发送
        if (!kafkaConfig.isAvroSerializationEnabled()) {
            sendObjectData(topic, key, data);
            return;
        }
        
        // 如果正在关闭，则不发送新消息
        if (isShuttingDown) {
            log.warn("Kafka消息生产者正在关闭，忽略Avro发送请求: topic={}, key={}, type={}", 
                    topic, key, data.getClass().getSimpleName());
            return;
        }
        
        // 再次检查是否正在关闭（防止在上一次检查后状态发生变化）
        if (isShuttingDown) {
            log.warn("Kafka消息生产者正在关闭，忽略Avro发送请求: topic={}, key={}, type={}", 
                    topic, key, data.getClass().getSimpleName());
            return;
        }
        
        try {
            // 增加待处理操作计数
            pendingOperations.incrementAndGet();
            
            // 使用虚拟线程异步发送消息
            Thread.startVirtualThread(() -> {
                try {
                    // 再次检查是否正在关闭
                    if (isShuttingDown) {
                        log.warn("Kafka消息生产者正在关闭，取消Avro发送: topic={}, key={}, type={}", 
                                topic, key, data.getClass().getSimpleName());
                        pendingOperations.decrementAndGet();
                        return;
                    }
                    
                    // 第三次检查是否正在关闭（防止在启动虚拟线程后状态发生变化）
                    if (isShuttingDown) {
                        log.warn("Kafka消息生产者正在关闭，取消Avro发送: topic={}, key={}, type={}", 
                                topic, key, data.getClass().getSimpleName());
                        pendingOperations.decrementAndGet();
                        return;
                    }
                    
                    CompletableFuture<SendResult<String, Object>> future = avroKafkaTemplate.send(topic, key, data);
                    
                    future.whenComplete((result, ex) -> {
                        try {
                            if (ex == null) {
                                if (log.isDebugEnabled()) {
                                    log.debug("发送Avro数据到Kafka成功: topic={}, key={}, type={}, partition={}, offset={}", 
                                            topic, key, data.getClass().getSimpleName(),
                                            result.getRecordMetadata().partition(), 
                                            result.getRecordMetadata().offset());
                                }
                            } else {
                                // 如果是因为关闭导致的异常，使用警告级别而不是错误级别
                                if (isShuttingDown && 
                                    (ex instanceof org.apache.kafka.common.KafkaException || 
                                     ex.getCause() instanceof org.apache.kafka.common.KafkaException ||
                                     ex instanceof org.springframework.kafka.KafkaException ||
                                     ex.getCause() instanceof org.springframework.kafka.KafkaException) &&
                                    (ex.getMessage().contains("Producer closed") || 
                                     (ex.getCause() != null && ex.getCause().getMessage() != null && 
                                      ex.getCause().getMessage().contains("Producer closed")))) {
                                    log.warn("发送Avro数据时Kafka生产者已关闭: topic={}, key={}, type={}", 
                                            topic, key, data.getClass().getSimpleName());
                                } else {
                                    log.error("发送Avro数据到Kafka失败: topic={}, key={}, type={}", 
                                            topic, key, data.getClass().getSimpleName(), ex);
                                }
                            }
                        } finally {
                            // 减少待处理操作计数
                            pendingOperations.decrementAndGet();
                        }
                    });
                    
                    messageCounter.incrementAndGet();
                } catch (Exception e) {
                    // 如果是因为关闭导致的异常，使用警告级别而不是错误级别
                    if (isShuttingDown && 
                        (e instanceof org.apache.kafka.common.KafkaException || 
                         e.getCause() instanceof org.apache.kafka.common.KafkaException ||
                         e instanceof org.springframework.kafka.KafkaException ||
                         e.getCause() instanceof org.springframework.kafka.KafkaException) &&
                        (e.getMessage().contains("Producer closed") || 
                         (e.getCause() != null && e.getCause().getMessage() != null && 
                          e.getCause().getMessage().contains("Producer closed")))) {
                        log.warn("发送Avro数据时Kafka生产者已关闭: topic={}, key={}, type={}", 
                                topic, key, data.getClass().getSimpleName());
                    } else {
                        log.error("发送Avro数据到Kafka异常: topic={}, key={}, type={}", 
                                topic, key, data.getClass().getSimpleName(), e);
                    }
                    
                    // 减少待处理操作计数
                    pendingOperations.decrementAndGet();
                }
            });
        } catch (Exception e) {
            log.error("创建虚拟线程异常: topic={}, key={}, type={}", 
                    topic, key, data.getClass().getSimpleName(), e);
            // 减少待处理操作计数
            pendingOperations.decrementAndGet();
            throw e;
        }
    }
    
    /**
     * 使用Avro序列化发送K线数据
     *
     * @param key       消息键（通常是交易对）
     * @param klineData K线数据对象
     */
    public void sendKlineAvroData(String key, Object klineData) {
        String topic = kafkaProducerConfig.getKlineTopic();
        sendAvroData(topic, key, klineData);
    }
    
    /**
     * 使用Avro序列化发送深度数据
     *
     * @param key       消息键（通常是交易对）
     * @param depthData 深度数据对象
     */
    public void sendDepthAvroData(String key, Object depthData) {
        String topic = kafkaProducerConfig.getDepthTopic();
        sendAvroData(topic, key, depthData);
    }
    
    /**
     * 使用Avro序列化发送交易数据
     *
     * @param key       消息键（通常是交易对）
     * @param tradeData 交易数据对象
     */
    public void sendTradeAvroData(String key, Object tradeData) {
        String topic = kafkaProducerConfig.getTradeTopic();
        sendAvroData(topic, key, tradeData);
    }

    /**
     * 发送消息到Kafka
     *
     * @param topic   主题
     * @param key     消息键
     * @param message 消息内容
     */
    private void sendMessage(String topic, String key, String message) {
        // 增加消息计数
        messageCounter.incrementAndGet();
        
        // 如果启用了批处理，则添加到批处理缓存
        if (enableBatch) {
            addToBatch(topic, key, message);
            return;
        }
        
        // 未启用批处理，直接发送
        sendMessageDirectly(topic, key, message);
    }
    
    /**
     * 批量发送消息到Kafka
     * 
     * @param topic 主题
     * @param key 消息键
     * @param messages 消息内容列表
     */
    private void sendMessageBatch(String topic, String key, List<String> messages) {
        if (messages == null || messages.isEmpty()) {
            return;
        }
        
        // 如果正在关闭，则不发送新消息
        if (isShuttingDown) {
            log.warn("Kafka消息生产者正在关闭，忽略批量发送请求: topic={}, key={}, count={}", topic, key, messages.size());
            return;
        }
        
        // 再次检查是否正在关闭（防止在上一次检查后状态发生变化）
        if (isShuttingDown) {
            log.warn("Kafka消息生产者正在关闭，忽略批量发送请求: topic={}, key={}, count={}", topic, key, messages.size());
            return;
        }
        
        // 增加消息计数
        messageCounter.addAndGet(messages.size());
        
        try {
            // 增加待处理操作计数
            pendingOperations.incrementAndGet();
            
            // 使用虚拟线程异步发送消息
            Thread.startVirtualThread(() -> {
                try {
                    // 再次检查是否正在关闭
                    if (isShuttingDown) {
                        log.warn("Kafka消息生产者正在关闭，取消批量发送: topic={}, key={}, count={}", topic, key, messages.size());
                        pendingOperations.decrementAndGet();
                        return;
                    }
                    
                    // 第三次检查是否正在关闭（防止在启动虚拟线程后状态发生变化）
                    if (isShuttingDown) {
                        log.warn("Kafka消息生产者正在关闭，取消批量发送: topic={}, key={}, count={}", topic, key, messages.size());
                        pendingOperations.decrementAndGet();
                        return;
                    }
                    
                    // 不使用事务，直接批量发送
                    for (String message : messages) {
                        kafkaTemplate.send(topic, key, message);
                    }
                    
                    log.debug("批量发送消息到Kafka成功: topic={}, key={}, count={}", topic, key, messages.size());
                } catch (Exception e) {
                    // 如果是因为关闭导致的异常，使用警告级别而不是错误级别
                    if (isShuttingDown && 
                        (e instanceof org.apache.kafka.common.KafkaException || 
                         e.getCause() instanceof org.apache.kafka.common.KafkaException ||
                         e instanceof org.springframework.kafka.KafkaException ||
                         e.getCause() instanceof org.springframework.kafka.KafkaException) &&
                        (e.getMessage().contains("Producer closed") || 
                         (e.getCause() != null && e.getCause().getMessage() != null && 
                          e.getCause().getMessage().contains("Producer closed")))) {
                        log.warn("批量发送消息时Kafka生产者已关闭: topic={}, key={}, count={}", topic, key, messages.size());
                    } else {
                        log.error("批量发送消息到Kafka异常: topic={}, key={}, count={}", topic, key, messages.size(), e);
                    }
                } finally {
                    // 减少待处理操作计数
                    pendingOperations.decrementAndGet();
                }
            });
        } catch (Exception e) {
            log.error("创建虚拟线程异常: topic={}, key={}", topic, key, e);
            // 减少待处理操作计数
            pendingOperations.decrementAndGet();
            throw e;
        }
    }
    
    /**
     * 直接发送消息到Kafka（不使用批处理）
     * 
     * @param topic 主题
     * @param key 消息键
     * @param message 消息内容
     */
    private void sendMessageDirectly(String topic, String key, String message) {
        // 如果正在关闭，则不发送新消息
        if (isShuttingDown) {
            log.warn("Kafka消息生产者正在关闭，忽略发送请求: topic={}, key={}", topic, key);
            return;
        }
        
        // 再次检查是否正在关闭（防止在上一次检查后状态发生变化）
        if (isShuttingDown) {
            log.warn("Kafka消息生产者正在关闭，忽略发送请求: topic={}, key={}", topic, key);
            return;
        }
        
        try {
            // 增加待处理操作计数
            pendingOperations.incrementAndGet();
            
            // 使用虚拟线程异步发送消息
            Thread.startVirtualThread(() -> {
                try {
                    // 再次检查是否正在关闭
                    if (isShuttingDown) {
                        log.warn("Kafka消息生产者正在关闭，取消发送: topic={}, key={}", topic, key);
                        pendingOperations.decrementAndGet();
                        return;
                    }
                    
                    // 第三次检查是否正在关闭（防止在启动虚拟线程后状态发生变化）
                    if (isShuttingDown) {
                        log.warn("Kafka消息生产者正在关闭，取消发送: topic={}, key={}", topic, key);
                        pendingOperations.decrementAndGet();
                        return;
                    }
                    
                    // 发送消息到Kafka
                    CompletableFuture<SendResult<String, String>> future = kafkaTemplate.send(topic, key, message);
                    
                    // 处理发送结果
                    future.whenComplete((result, ex) -> {
                        try {
                            if (ex == null) {
                                // 发送成功
                                if (log.isDebugEnabled()) {
                                    log.debug("发送消息到Kafka成功: topic={}, key={}, partition={}, offset={}", 
                                            topic, key, result.getRecordMetadata().partition(), 
                                            result.getRecordMetadata().offset());
                                }
                            } else {
                                // 发送失败
                                // 如果是因为关闭导致的异常，使用警告级别而不是错误级别
                                if (isShuttingDown && 
                                    (ex instanceof org.apache.kafka.common.KafkaException || 
                                     ex.getCause() instanceof org.apache.kafka.common.KafkaException ||
                                     ex instanceof org.springframework.kafka.KafkaException ||
                                     ex.getCause() instanceof org.springframework.kafka.KafkaException) &&
                                    (ex.getMessage().contains("Producer closed") || 
                                     (ex.getCause() != null && ex.getCause().getMessage() != null && 
                                      ex.getCause().getMessage().contains("Producer closed")))) {
                                    log.warn("发送消息时Kafka生产者已关闭: topic={}, key={}", topic, key);
                                } else {
                                    log.error("发送消息到Kafka失败: topic={}, key={}", topic, key, ex);
                                }
                            }
                        } finally {
                            // 减少待处理操作计数
                            pendingOperations.decrementAndGet();
                        }
                    });
                } catch (Exception e) {
                    // 如果是因为关闭导致的异常，使用警告级别而不是错误级别
                    if (isShuttingDown && 
                        (e instanceof org.apache.kafka.common.KafkaException || 
                         e.getCause() instanceof org.apache.kafka.common.KafkaException ||
                         e instanceof org.springframework.kafka.KafkaException ||
                         e.getCause() instanceof org.springframework.kafka.KafkaException) &&
                        (e.getMessage().contains("Producer closed") || 
                         (e.getCause() != null && e.getCause().getMessage() != null && 
                          e.getCause().getMessage().contains("Producer closed")))) {
                        log.warn("发送消息时Kafka生产者已关闭: topic={}, key={}", topic, key);
                    } else {
                        log.error("发送消息到Kafka异常: topic={}, key={}", topic, key, e);
                    }
                    
                    // 减少待处理操作计数
                    pendingOperations.decrementAndGet();
                }
            });
        } catch (Exception e) {
            log.error("创建虚拟线程异常: topic={}, key={}", topic, key, e);
            // 减少待处理操作计数
            pendingOperations.decrementAndGet();
            throw e;
        }
    }
    
    /**
     * 添加消息到批处理缓存
     *
     * @param topic   主题
     * @param key     消息键
     * @param message 消息内容
     */
    private synchronized void addToBatch(String topic, String key, String message) {
        // 获取或创建主题缓存
        Map<String, List<String>> topicCache = batchCache.computeIfAbsent(topic, k -> new ConcurrentHashMap<>());
        
        // 获取或创建键缓存
        List<String> keyMessages = topicCache.computeIfAbsent(key, k -> new ArrayList<>());
        
        // 添加消息到缓存
        keyMessages.add(message);
        
        // 如果达到批处理大小，则立即刷新
        if (keyMessages.size() >= batchSize) {
            flushBatch(topic, key, keyMessages);
        }
    }
    
    /**
     * 刷新所有批处理缓存
     */
    private synchronized void flushAllBatches() {
        if (batchCache.isEmpty()) {
            return;
        }
        
        // 如果正在关闭，使用警告级别日志
        if (isShuttingDown) {
            log.warn("正在关闭过程中刷新批处理缓存: 共{}个主题", batchCache.size());
        } else {
            log.debug("开始刷新所有批处理缓存: 共{}个主题", batchCache.size());
        }
        
        // 遍历主题
        for (Map.Entry<String, Map<String, List<String>>> topicEntry : batchCache.entrySet()) {
            String topic = topicEntry.getKey();
            Map<String, List<String>> keyMap = topicEntry.getValue();
            
            if (keyMap.isEmpty()) {
                continue;
            }
            
            // 遍历键
            for (Map.Entry<String, List<String>> keyEntry : keyMap.entrySet()) {
                String key = keyEntry.getKey();
                List<String> messages = keyEntry.getValue();
                
                if (!messages.isEmpty()) {
                    flushBatch(topic, key, messages);
                }
            }
        }
    }
    
    /**
     * 刷新指定批处理缓存
     * 
     * @param topic 主题
     * @param key 消息键
     * @param messages 消息内容列表
     */
    private void flushBatch(String topic, String key, List<String> messages) {
        if (messages.isEmpty()) {
            return;
        }
        
        // 创建消息副本
        List<String> messagesCopy = new ArrayList<>(messages);
        
        // 清空原始列表
        messages.clear();
        
        // 批量发送消息
        sendMessageBatch(topic, key, messagesCopy);
    }
    
    /**
     * 获取当前处理的消息总数
     * 
     * @return 消息总数
     */
    public long getMessageCount() {
        return messageCounter.get();
    }
    
    /**
     * 获取当前待处理的操作数
     * 
     * @return 待处理操作数
     */
    public int getPendingOperationCount() {
        return pendingOperations.get();
    }
    
    /**
     * 检查生产者是否正在关闭
     * 
     * @return 如果正在关闭，则返回true
     */
    public boolean isShuttingDown() {
        return isShuttingDown;
    }
    
    // SmartLifecycle接口实现
    
    @Override
    public void start() {
        running = true;
        // 启动逻辑已在init()方法中实现
    }
    
    @Override
    public void stop() {
        if (running) {
            // 确保调用destroy方法
            destroy();
            running = false;
        }
    }
    
    @Override
    public boolean isRunning() {
        return running;
    }
    
    @Override
    public int getPhase() {
        // 返回一个较小的值，确保此Bean在KafkaTemplate之前停止
        // KafkaTemplate的默认phase是0，我们使用-100确保先于它停止
        return -100;
    }
    
    @Override
    public boolean isAutoStartup() {
        return true;
    }
    
    @Override
    public void stop(Runnable callback) {
        stop();
        callback.run();
    }

    /**
     * 发送K线数据到Kafka（接收DTO对象）
     *
     * @param klineDataDTO K线数据DTO对象
     */
    public void sendKlineData(com.crypto.trading.common.dto.market.KlineDataDTO klineDataDTO) {
        if (klineDataDTO == null) {
            return;
        }
        
        try {
            String topic = kafkaProducerConfig.getKlineTopic();
            String key = klineDataDTO.getSymbol() + "-" + klineDataDTO.getInterval();
            sendObjectData(topic, key, klineDataDTO);
        } catch (Exception e) {
            log.error("发送K线数据到Kafka失败，但应用程序将继续运行: symbol={}, interval={}, error={}", 
                    klineDataDTO.getSymbol(), klineDataDTO.getInterval(), e.getMessage());
        }
    }

    /**
     * 发送交易数据到Kafka（接收DTO对象）
     *
     * @param tradeDTO 交易数据DTO对象
     */
    public void sendTradeData(com.crypto.trading.common.dto.market.TradeDTO tradeDTO) {
        if (tradeDTO == null) {
            return;
        }
        
        try {
            String topic = kafkaProducerConfig.getTradeTopic();
            String key = tradeDTO.getSymbol();
            sendObjectData(topic, key, tradeDTO);
        } catch (Exception e) {
            log.error("发送交易数据到Kafka失败，但应用程序将继续运行: symbol={}, tradeId={}, error={}", 
                    tradeDTO.getSymbol(), tradeDTO.getId(), e.getMessage());
        }
    }

    /**
     * 发送深度数据到Kafka（接收DTO对象）
     *
     * @param depthDataDTO 深度数据DTO对象
     */
    public void sendDepthData(com.crypto.trading.common.dto.market.DepthDataDTO depthDataDTO) {
        if (depthDataDTO == null) {
            return;
        }
        
        try {
            String topic = kafkaProducerConfig.getDepthTopic();
            String key = depthDataDTO.getSymbol();
            sendObjectData(topic, key, depthDataDTO);
        } catch (Exception e) {
            log.error("发送深度数据到Kafka失败，但应用程序将继续运行: symbol={}, error={}", 
                    depthDataDTO.getSymbol(), e.getMessage());
        }
    }
} 