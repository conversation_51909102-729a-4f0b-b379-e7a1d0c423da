package com.crypto.trading.trade.service;

import com.crypto.trading.trade.model.avro.TradingSignal;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.UUID;

/**
 * 信号验证服务
 * 
 * <p>负责验证接收到的交易信号是否有效，包括交易对验证、信号类型验证等</p>
 */
@Service
public class SignalValidationService {

    private static final Logger log = LoggerFactory.getLogger(SignalValidationService.class);

    @Autowired
    @Qualifier("supportedSymbols")
    private List<String> supportedSymbols;

    /**
     * 验证交易信号
     *
     * @param signal 交易信号
     * @return 验证结果
     */
    public ValidationResult validateSignal(TradingSignal signal) {
        // 生成验证ID，用于日志跟踪
        String validationId = UUID.randomUUID().toString().substring(0, 8);
        log.info("开始验证交易信号 [{}]: signalId={}, symbol={}, type={}", 
                validationId, signal.getId(), signal.getSymbol(), signal.getSignalType());
        
        // 验证必填字段
        if (signal.getId() == null || signal.getId().isEmpty()) {
            return createErrorResult(validationId, "信号ID为空", signal);
        }
        
        if (signal.getSymbol() == null || signal.getSymbol().isEmpty()) {
            return createErrorResult(validationId, "交易对为空", signal);
        }
        
        if (signal.getStrategy() == null || signal.getStrategy().isEmpty()) {
            return createErrorResult(validationId, "策略名称为空", signal);
        }
        
        if (signal.getSignalType() == null) {
            return createErrorResult(validationId, "信号类型为空", signal);
        }
        
        // 验证交易对是否支持
        if (!isSymbolSupported(signal.getSymbol())) {
            return createErrorResult(validationId, "不支持的交易对: " + signal.getSymbol(), signal);
        }
        
        // 验证价格和数量
        if (signal.getOrderType() != null) {
            switch (signal.getOrderType()) {
                case LIMIT:
                    if (signal.getPrice() == null) {
                        return createErrorResult(validationId, "限价单必须指定价格", signal);
                    }
                    break;
                case STOP:
                case STOP_MARKET:
                    if (signal.getPrice() == null) {
                        return createErrorResult(validationId, "止损单必须指定触发价格", signal);
                    }
                    break;
                case TAKE_PROFIT:
                case TAKE_PROFIT_MARKET:
                    if (signal.getPrice() == null) {
                        return createErrorResult(validationId, "止盈单必须指定触发价格", signal);
                    }
                    break;
            }
        }
        
        // 验证置信度
        if (signal.getConfidence() < 0.0 || signal.getConfidence() > 1.0) {
            return createErrorResult(validationId, "置信度必须在0.0-1.0之间，当前值: " + signal.getConfidence(), signal);
        }
        
        // 验证信号类型与订单类型的一致性
        if (signal.getSignalType() != null && signal.getOrderType() != null) {
            boolean isValid = true;
            switch (signal.getSignalType()) {
                case BUY:
                case SELL:
                    // 这些信号类型可以使用任何订单类型
                    break;
                case CLOSE_LONG:
                case CLOSE_SHORT:
                    // 平仓操作通常使用市价单或限价单
                    if (signal.getOrderType() != com.crypto.trading.trade.model.avro.OrderType.MARKET
                            && signal.getOrderType() != com.crypto.trading.trade.model.avro.OrderType.LIMIT) {
                        isValid = false;
                    }
                    break;
                case HOLD:
                    // HOLD信号不应该有订单类型
                    isValid = false;
                    break;
            }
            
            if (!isValid) {
                return createErrorResult(validationId, "信号类型与订单类型不匹配: " + signal.getSignalType() + " / " + signal.getOrderType(), signal);
            }
        }
        
        log.info("交易信号验证通过 [{}]: signalId={}, symbol={}, type={}", 
                validationId, signal.getId(), signal.getSymbol(), signal.getSignalType());
        
        return new ValidationResult(true, null, signal, validationId);
    }

    /**
     * 检查交易对是否支持
     *
     * @param symbol 交易对
     * @return 是否支持
     */
    private boolean isSymbolSupported(String symbol) {
        return supportedSymbols.contains(symbol.toUpperCase());
    }

    /**
     * 创建错误验证结果
     *
     * @param validationId 验证ID
     * @param errorMessage 错误信息
     * @param signal 交易信号
     * @return 验证结果
     */
    private ValidationResult createErrorResult(String validationId, String errorMessage, TradingSignal signal) {
        log.warn("交易信号验证失败 [{}]: {}, signalId={}, symbol={}, type={}", 
                validationId, errorMessage, signal.getId(), signal.getSymbol(), signal.getSignalType());
        return new ValidationResult(false, errorMessage, signal, validationId);
    }

    /**
     * 验证结果类
     */
    public static class ValidationResult {
        private final boolean valid;
        private final String errorMessage;
        private final TradingSignal signal;
        private final String validationId;

        public ValidationResult(boolean valid, String errorMessage, TradingSignal signal, String validationId) {
            this.valid = valid;
            this.errorMessage = errorMessage;
            this.signal = signal;
            this.validationId = validationId;
        }

        public boolean isValid() {
            return valid;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public TradingSignal getSignal() {
            return signal;
        }

        public String getValidationId() {
            return validationId;
        }
    }
}