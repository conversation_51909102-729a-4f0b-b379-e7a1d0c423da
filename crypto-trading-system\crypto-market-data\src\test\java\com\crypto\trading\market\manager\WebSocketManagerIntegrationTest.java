package com.crypto.trading.market.manager;

import com.crypto.trading.market.config.MarketDataConfig;
import com.crypto.trading.market.config.WebSocketConfig;
import com.crypto.trading.market.listener.DepthDataListener;
import com.crypto.trading.market.listener.KlineDataListener;
import com.crypto.trading.market.listener.TradeDataListener;
import com.crypto.trading.market.repository.InfluxDBRepository;
import com.crypto.trading.market.websocket.WebSocketHealthChecker;
import com.crypto.trading.sdk.websocket.WebSocketConnectionPool;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.lang.reflect.Field;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * WebSocketManager集成测试
 * 简化版本，专注于基本功能测试
 */
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = {WebSocketManagerIntegrationTest.TestConfig.class})
@TestPropertySource(properties = {
        "market.websocket.reconnect-interval=100",
        "market.websocket.connect-timeout=500"
})
class WebSocketManagerIntegrationTest {

    /**
     * 测试配置类
     */
    @Configuration
    static class TestConfig {
        @Bean
        public WebSocketConfig webSocketConfig() {
            WebSocketConfig config = mock(WebSocketConfig.class);
            when(config.getReconnectInterval()).thenReturn(100L);
            when(config.getConnectTimeout()).thenReturn(500);
            return config;
        }
        
        @Bean
        public WebSocketManager webSocketManager() {
            // 使用默认构造函数，依赖将通过@Autowired注入
            return new WebSocketManager();
        }
    }

    @MockBean
    private MarketDataConfig marketDataConfig;

    @MockBean
    private DepthDataListener depthDataListener;

    @MockBean
    private KlineDataListener klineDataListener;

    @MockBean
    private TradeDataListener tradeDataListener;

    @MockBean
    private WebSocketConnectionPool connectionPool;

    @MockBean
    private WebSocketHealthChecker healthChecker;
    
    @MockBean
    private InfluxDBRepository influxDBRepository;

    @Autowired
    private WebSocketManager webSocketManager;

    @BeforeEach
    void setUp() throws Exception {
        // 配置市场数据
        when(marketDataConfig.isDepthEnabled()).thenReturn(true);
        when(marketDataConfig.isKlineEnabled()).thenReturn(true);
        when(marketDataConfig.isTradeEnabled()).thenReturn(true);
        
        // 手动设置虚拟线程执行器，避免空指针异常
        Field executorField = WebSocketManager.class.getDeclaredField("virtualThreadExecutor");
        executorField.setAccessible(true);
        executorField.set(webSocketManager, Executors.newVirtualThreadPerTaskExecutor());
    }

    @Test
    void testInitialization() {
        // 验证WebSocketManager初始化后状态正确
        webSocketManager.init();
        
        // 验证虚拟线程执行器已创建
        ExecutorService executor = webSocketManager.getVirtualThreadExecutor();
        assertNotNull(executor);
        assertFalse(executor.isShutdown());
        
        // 验证运行状态
        assertTrue(webSocketManager.isRunning());
    }

    @Test
    void testStartListenersByType() {
        // 初始化WebSocketManager
        webSocketManager.init();
        
        // 测试按类型启动监听器
        boolean result = webSocketManager.startListenersByType("depth");
        
        // 验证深度数据监听器已启动
        verify(depthDataListener, timeout(1000)).startDepthDataSubscription();
        
        // 验证结果
        assertTrue(result);
    }

    @Test
    void testStopListenersByType() {
        // 初始化WebSocketManager
        webSocketManager.init();
        
        // 测试按类型停止监听器
        boolean result = webSocketManager.stopListenersByType("depth");
        
        // 验证深度数据监听器已停止
        verify(depthDataListener, timeout(1000)).stopDepthDataSubscription();
        
        // 验证结果
        assertTrue(result);
    }

    @Test
    void testConnectionRegistration() {
        // 初始化WebSocketManager
        webSocketManager.init();
        
        // 注册连接
        webSocketManager.registerConnection(1, "test-connection");
        
        // 验证健康检查器已注册连接
        verify(healthChecker).registerConnection(1, "test-connection");
        
        // 注销连接
        webSocketManager.unregisterConnection("test-connection");
        
        // 验证健康检查器已注销连接
        verify(healthChecker).unregisterConnection(1);
    }
}