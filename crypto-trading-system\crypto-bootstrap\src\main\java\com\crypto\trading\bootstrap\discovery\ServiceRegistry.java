package com.crypto.trading.bootstrap.discovery;

import com.crypto.trading.bootstrap.config.AppConfig;
import com.fasterxml.jackson.databind.JsonNode;
import jakarta.annotation.PostConstruct;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;


import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantReadWriteLock;

/**
 * 服务注册表
 * 负责管理策略服务的注册、健康检查和服务发现
 */
@Component
@EnableScheduling
public class ServiceRegistry {

    private static final Logger log = LoggerFactory.getLogger(ServiceRegistry.class);
    
    /**
     * 服务信息，键为服务URL，值为服务状态
     */
    private final Map<String, ServiceInfo> services = new ConcurrentHashMap<>();
    
    /**
     * 读写锁，用于保护服务列表的并发访问
     */
    private final ReentrantReadWriteLock rwl = new ReentrantReadWriteLock();
    
    /**
     * 应用配置
     */
    private final AppConfig appConfig;
    
    /**
     * REST客户端
     */
    private final RestTemplate restTemplate;
    
    /**
     * 最后一次负载均衡索引
     */
    private int lastIndex = -1;

    /**
     * 构造函数
     * 
     * @param appConfig 应用配置
     */
    @Autowired
    public ServiceRegistry(AppConfig appConfig) {
        this.appConfig = appConfig;
        this.restTemplate = new RestTemplate();
    }
    
    /**
     * 初始化服务注册表
     */
    @PostConstruct
    public void init() {
        // 从配置中加载初始服务URL
        String[] urls = appConfig.getPythonStrategy().getServiceUrls();
        if (urls != null && urls.length > 0) {
            for (String url : urls) {
                registerService(url);
                // 立即执行健康检查
                try {
                    boolean isHealthy = checkServiceHealth(url);
                    ServiceInfo info = services.get(url);
                    if (info != null) {
                        info.setHealthy(isHealthy);
                        info.setLastChecked(System.currentTimeMillis());
                        if (isHealthy) {
                            log.info("服务 {} 初始健康检查通过", url);
                        } else {
                            log.warn("服务 {} 初始健康检查失败", url);
                        }
                    }
                } catch (Exception e) {
                    log.error("服务 {} 初始健康检查异常", url, e);
                }
            }
            log.info("服务注册表初始化完成，已添加 {} 个服务", urls.length);
        } else {
            log.warn("未配置任何策略服务URL");
        }
    }
    
    /**
     * 注册新的服务
     * 
     * @param serviceUrl 服务URL
     * @return 如果服务是新注册的，则返回true；如果服务已经存在，则返回false
     */
    public boolean registerService(String serviceUrl) {
        try {
            rwl.writeLock().lock();
            
            // 检查服务是否已经存在
            if (services.containsKey(serviceUrl)) {
                log.debug("服务 {} 已经在注册表中", serviceUrl);
                return false;
            }
            
            // 创建新的服务信息
            ServiceInfo info = new ServiceInfo(serviceUrl);
            info.setLastChecked(System.currentTimeMillis());
            services.put(serviceUrl, info);
            
            log.info("注册新服务: {}", serviceUrl);
            return true;
            
        } finally {
            rwl.writeLock().unlock();
        }
    }
    
    /**
     * 取消注册服务
     * 
     * @param serviceUrl 服务URL
     * @return 如果服务成功从注册表中移除，则返回true；如果服务不存在于注册表中，则返回false
     */
    public boolean unregisterService(String serviceUrl) {
        try {
            rwl.writeLock().lock();
            
            if (!services.containsKey(serviceUrl)) {
                return false;
            }
            
            services.remove(serviceUrl);
            log.info("取消注册服务: {}", serviceUrl);
            return true;
            
        } finally {
            rwl.writeLock().unlock();
        }
    }
    
    /**
     * 定期执行健康检查
     */
    @Scheduled(fixedDelayString = "${app.python-strategy.health-check-interval:60000}")
    public void healthCheck() {
        log.debug("开始执行健康检查...");
        
        try {
            rwl.readLock().lock();
            
            // 对每个服务执行健康检查
            for (Map.Entry<String, ServiceInfo> entry : services.entrySet()) {
                String url = entry.getKey();
                ServiceInfo info = entry.getValue();
                
                try {
                    boolean isHealthy = checkServiceHealth(url);
                    
                    // 更新服务状态
                    info.setHealthy(isHealthy);
                    info.setLastChecked(System.currentTimeMillis());
                    
                    if (isHealthy) {
                        // 健康的服务，重置失败计数
                        info.resetFailureCount();
                        log.debug("服务 {} 健康检查通过", url);
                    } else {
                        // 不健康的服务，增加失败计数
                        info.incrementFailureCount();
                        log.warn("服务 {} 健康检查失败，连续失败次数: {}", url, info.getFailureCount());
                        
                        // 超过阈值，尝试重新初始化服务
                        if (info.getFailureCount() == 3) {
                            log.info("尝试重新初始化服务 {}", url);
                            tryInitializeService(url);
                        }
                        
                        // 如果失败次数过多，移除该服务
                        if (info.getFailureCount() > 5) {
                            log.error("服务 {} 连续失败次数过多，将从注册表中移除", url);
                            unregisterService(url);
                        }
                    }
                } catch (Exception e) {
                    log.error("对服务 {} 执行健康检查时发生错误", url, e);
                    
                    // 更新服务状态为不健康
                    info.setHealthy(false);
                    info.setLastChecked(System.currentTimeMillis());
                    info.incrementFailureCount();
                }
            }
            
        } finally {
            rwl.readLock().unlock();
        }
        
        // 打印当前服务状态摘要
        printServiceStatusSummary();
    }
    
    /**
     * 检查单个服务的健康状态
     * 
     * @param serviceUrl 服务URL
     * @return 如果服务健康，则返回true；否则返回false
     */
    private boolean checkServiceHealth(String serviceUrl) {
        try {
            String url = serviceUrl + "/status";
            log.debug("检查服务健康状态: {}", url);
            
            ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);
            
            if (response.getStatusCode().is2xxSuccessful()) {
                log.info("服务 {} 健康检查成功，响应状态码：{}", serviceUrl, response.getStatusCodeValue());
                return true;
            } else {
                log.warn("服务 {} 健康检查失败，HTTP状态码: {}", serviceUrl, response.getStatusCodeValue());
                return false;
            }
        } catch (Exception e) {
            log.warn("服务 {} 健康检查异常: {}", serviceUrl, e.getMessage());
            return false;
        }
    }
    
    /**
     * 尝试初始化服务
     * 
     * @param serviceUrl 服务URL
     */
    private void tryInitializeService(String serviceUrl) {
        try {
            String url = serviceUrl + "/init";
            log.info("尝试初始化服务: {}", url);
            
            ResponseEntity<String> response = restTemplate.postForEntity(url, null, String.class);
            
            if (response.getStatusCode().is2xxSuccessful()) {
                log.info("服务 {} 初始化成功", serviceUrl);
            } else {
                log.warn("服务 {} 初始化失败，HTTP状态码: {}", serviceUrl, response.getStatusCodeValue());
            }
        } catch (Exception e) {
            log.warn("尝试初始化服务 {} 时发生异常: {}", serviceUrl, e.getMessage());
        }
    }
    
    /**
     * 打印服务状态摘要
     */
    private void printServiceStatusSummary() {
        try {
            rwl.readLock().lock();
            
            int totalServices = services.size();
            int healthyServices = 0;
            
            for (ServiceInfo info : services.values()) {
                if (info.isHealthy()) {
                    healthyServices++;
                }
            }
            
            log.info("服务状态摘要: 总数 {}，健康 {}，不健康 {}", 
                    totalServices, healthyServices, totalServices - healthyServices);
            
        } finally {
            rwl.readLock().unlock();
        }
    }
    
    /**
     * 使用轮询策略获取一个健康的服务URL
     * 
     * @return 健康的服务URL，如果没有健康的服务，则返回null
     */
    public String getHealthyServiceUrl() {
        try {
            rwl.readLock().lock();
            
            int healthyCount = 0;
            for (ServiceInfo info : services.values()) {
                if (info.isHealthy()) {
                    healthyCount++;
                }
            }
            
            if (healthyCount == 0) {
                log.warn("没有健康的服务可用");
                return null;
            }
            
            // 创建健康服务的列表
            List<String> healthyServices = new ArrayList<>(healthyCount);
            for (Map.Entry<String, ServiceInfo> entry : services.entrySet()) {
                if (entry.getValue().isHealthy()) {
                    healthyServices.add(entry.getKey());
                }
            }
            
            // 使用轮询策略选择服务
            lastIndex = (lastIndex + 1) % healthyServices.size();
            return healthyServices.get(lastIndex);
            
        } finally {
            rwl.readLock().unlock();
        }
    }
    
    /**
     * 获取所有服务的URL
     * 
     * @param healthyOnly 如果为true，则只返回健康的服务；如果为false，则返回所有服务
     * @return 服务URL列表
     */
    public List<String> getServiceUrls(boolean healthyOnly) {
        try {
            rwl.readLock().lock();
            
            List<String> result = new ArrayList<>();
            
            for (Map.Entry<String, ServiceInfo> entry : services.entrySet()) {
                if (!healthyOnly || entry.getValue().isHealthy()) {
                    result.add(entry.getKey());
                }
            }
            
            return result;
            
        } finally {
            rwl.readLock().unlock();
        }
    }
    
    /**
     * 获取服务总数
     * 
     * @param healthyOnly 如果为true，则只计算健康的服务；如果为false，则计算所有服务
     * @return 服务数量
     */
    public int getServiceCount(boolean healthyOnly) {
        try {
            rwl.readLock().lock();
            
            if (!healthyOnly) {
                return services.size();
            }
            
            int count = 0;
            for (ServiceInfo info : services.values()) {
                if (info.isHealthy()) {
                    count++;
                }
            }
            
            return count;
            
        } finally {
            rwl.readLock().unlock();
        }
    }
    
    /**
     * 服务信息类
     * 封装与服务相关的信息和状态
     */
    public static class ServiceInfo {
        /**
         * 服务URL
         */
        private final String url;
        
        /**
         * 服务是否健康
         */
        private boolean healthy = false;
        
        /**
         * 上次检查时间
         */
        private long lastChecked = 0;
        
        /**
         * 连续失败次数
         */
        private int failureCount = 0;
        
        /**
         * 构造函数
         * 
         * @param url 服务URL
         */
        public ServiceInfo(String url) {
            this.url = url;
        }
        
        public String getUrl() {
            return url;
        }
        
        public boolean isHealthy() {
            return healthy;
        }
        
        public void setHealthy(boolean healthy) {
            this.healthy = healthy;
        }
        
        public long getLastChecked() {
            return lastChecked;
        }
        
        public void setLastChecked(long lastChecked) {
            this.lastChecked = lastChecked;
        }
        
        public int getFailureCount() {
            return failureCount;
        }
        
        public void incrementFailureCount() {
            this.failureCount++;
        }
        
        public void resetFailureCount() {
            this.failureCount = 0;
        }
        
        @Override
        public String toString() {
            return "ServiceInfo{" +
                    "url='" + url + '\'' +
                    ", healthy=" + healthy +
                    ", lastChecked=" + new Date(lastChecked) +
                    ", failureCount=" + failureCount +
                    '}';
        }
    }
}