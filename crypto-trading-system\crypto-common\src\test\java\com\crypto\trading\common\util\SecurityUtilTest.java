package com.crypto.trading.common.util;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * 安全工具类测试
 * <p>
 * 测试加密、解密和签名相关的工具方法
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
class SecurityUtilTest {

    /**
     * 测试MD5加密
     */
    @Test
    void testMd5() {
        String input = "test123";
        String expected = "cc03e747a6afbbcbf8be7668acfebee5";
        assertEquals(expected, SecurityUtil.md5(input));
    }

    /**
     * 测试SHA-256加密
     */
    @Test
    void testSha256() {
        String input = "test123";
        String expected = "ecd71870d1963316a97e3ac3408c9835ad8cf0f3c1bc703527c30265534f75ae";
        assertEquals(expected, SecurityUtil.sha256(input));
    }

    /**
     * 测试HMAC-SHA256签名
     */
    @Test
    void testHmacSha256() {
        String input = "symbol=BTCUSDT&side=BUY&type=LIMIT&timeInForce=GTC&quantity=1&price=9000&timestamp=1591702613943";
        String key = "NhqPtmdSJYdKjVHjA7PZj4Mge3R5YNiP1e3UZjInClVN65XAbvqqM6A7H5fATj0j";
        String expected = "71047745f2dc7637295506537657cb60ef76a0ef077704e3276c1c7f82612b81";
        assertEquals(expected, SecurityUtil.hmacSha256(input, key));
    }

    /**
     * 测试Base64编码和解码
     */
    @Test
    void testBase64() {
        String input = "Hello, World!";
        String encoded = SecurityUtil.base64Encode(input);
        String decoded = SecurityUtil.base64Decode(encoded);
        assertEquals(input, decoded);
    }

    /**
     * 测试URL编码和解码
     */
    @Test
    void testUrlEncode() {
        String input = "key=value&key2=value 2";
        String encoded = SecurityUtil.urlEncode(input);
        String decoded = SecurityUtil.urlDecode(encoded);
        assertEquals(input, decoded);
    }

    /**
     * 测试AES加密和解密
     */
    @Test
    void testAes() {
        String input = "Sensitive data to encrypt";
        String key = "1234567890123456"; // 16字节密钥
        String iv = "1234567890123456";  // 16字节初始化向量
        
        String encrypted = SecurityUtil.aesEncrypt(input, key, iv);
        String decrypted = SecurityUtil.aesDecrypt(encrypted, key, iv);
        
        assertEquals(input, decrypted);
    }

    /**
     * 测试字节数组与十六进制字符串转换
     */
    @Test
    void testHexConversion() {
        byte[] bytes = {0x01, 0x02, 0x03, 0x04};
        String hex = SecurityUtil.bytesToHex(bytes);
        assertEquals("01020304", hex);
        byte[] convertedBytes = SecurityUtil.hexToBytes(hex);
        assertArrayEquals(bytes, convertedBytes);
    }
} 