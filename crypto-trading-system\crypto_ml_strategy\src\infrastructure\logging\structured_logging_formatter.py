"""
Crypto ML Strategy - 结构化日志格式化器

该模块实现了多种专业的日志格式化器，支持JSON结构化输出、性能监控格式、
调试信息格式和日志字段提取标准化功能。

主要功能：
- StructuredFormatter: 结构化日志格式化器基类
- JSONLogFormatter: JSON格式日志输出
- PerformanceLogFormatter: 性能监控专用格式化器
- DebugLogFormatter: 调试信息格式化器
- LogFieldExtractor: 日志字段提取和标准化

作者: Crypto ML Strategy Team
日期: 2024-01-20
版本: 1.0.0
"""

import json
import re
import traceback
from abc import ABC, abstractmethod
from dataclasses import asdict, dataclass
from datetime import datetime
from typing import Any, Dict, List, Optional, Union, Callable
from loguru import logger
import psutil
import threading
import time


@dataclass
class LogRecord:
    """标准化日志记录结构"""
    timestamp: str
    level: str
    logger_name: str
    function_name: str
    line_number: int
    message: str
    extra: Dict[str, Any]
    exception: Optional[str] = None
    stack_trace: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)


class LogFieldExtractor:
    """日志字段提取和标准化器"""
    
    def __init__(self):
        self._sensitive_patterns = [
            re.compile(r'password["\s]*[:=]["\s]*([^"\s,}]+)', re.IGNORECASE),
            re.compile(r'api[_-]?key["\s]*[:=]["\s]*([^"\s,}]+)', re.IGNORECASE),
            re.compile(r'secret["\s]*[:=]["\s]*([^"\s,}]+)', re.IGNORECASE),
            re.compile(r'token["\s]*[:=]["\s]*([^"\s,}]+)', re.IGNORECASE),
        ]
        self._replacement = "***REDACTED***"
    
    def extract_standard_fields(self, record: Any) -> Dict[str, Any]:
        """提取标准日志字段"""
        try:
            # 基础字段
            fields = {
                "timestamp": record.get("time", datetime.now().isoformat()),
                "level": record.get("level", {}).get("name", "INFO"),
                "logger_name": record.get("name", "unknown"),
                "function_name": record.get("function", "unknown"),
                "line_number": record.get("line", 0),
                "message": record.get("message", ""),
                "module": record.get("module", "unknown"),
                "process_id": record.get("process", {}).get("id", 0),
                "thread_id": record.get("thread", {}).get("id", 0),
            }
            
            # 额外字段
            extra = record.get("extra", {})
            if extra:
                fields["extra"] = self._sanitize_extra_fields(extra)
            
            # 异常信息
            exception = record.get("exception")
            if exception:
                fields["exception"] = {
                    "type": exception.get("type", {}).get("name", ""),
                    "value": str(exception.get("value", "")),
                    "traceback": exception.get("traceback", "")
                }
            
            return fields
            
        except Exception as e:
            logger.error(f"Failed to extract standard fields: {e}")
            return {"error": "field_extraction_failed"}
    
    def _sanitize_extra_fields(self, extra: Dict[str, Any]) -> Dict[str, Any]:
        """清理敏感信息"""
        try:
            sanitized = {}
            
            for key, value in extra.items():
                if isinstance(value, str):
                    sanitized[key] = self._mask_sensitive_data(value)
                elif isinstance(value, dict):
                    sanitized[key] = self._sanitize_extra_fields(value)
                elif isinstance(value, (list, tuple)):
                    sanitized[key] = [
                        self._mask_sensitive_data(item) if isinstance(item, str) else item
                        for item in value
                    ]
                else:
                    sanitized[key] = value
            
            return sanitized
            
        except Exception:
            return {"error": "sanitization_failed"}
    
    def _mask_sensitive_data(self, text: str) -> str:
        """屏蔽敏感数据"""
        try:
            for pattern in self._sensitive_patterns:
                text = pattern.sub(lambda m: m.group(0).replace(m.group(1), self._replacement), text)
            return text
        except Exception:
            return text
    
    def extract_performance_fields(self, record: Any) -> Dict[str, Any]:
        """提取性能相关字段"""
        try:
            extra = record.get("extra", {})
            
            performance_fields = {
                "execution_time_ms": extra.get("execution_time_ms", 0),
                "memory_usage_mb": extra.get("memory_usage_mb", 0),
                "cpu_usage_percent": extra.get("cpu_usage_percent", 0),
                "io_read_bytes": extra.get("io_read_bytes", 0),
                "io_write_bytes": extra.get("io_write_bytes", 0),
                "network_sent_bytes": extra.get("network_sent_bytes", 0),
                "network_recv_bytes": extra.get("network_recv_bytes", 0),
                "gc_count": extra.get("gc_count", 0),
                "active_threads": extra.get("active_threads", 0),
                "open_files": extra.get("open_files", 0)
            }
            
            # 添加系统性能指标
            try:
                process = psutil.Process()
                performance_fields.update({
                    "system_memory_percent": psutil.virtual_memory().percent,
                    "system_cpu_percent": psutil.cpu_percent(),
                    "process_memory_mb": process.memory_info().rss / 1024 / 1024,
                    "process_cpu_percent": process.cpu_percent()
                })
            except Exception:
                pass
            
            return performance_fields
            
        except Exception as e:
            logger.error(f"Failed to extract performance fields: {e}")
            return {}
    
    def extract_debug_fields(self, record: Any) -> Dict[str, Any]:
        """提取调试相关字段"""
        try:
            extra = record.get("extra", {})
            
            debug_fields = {
                "variables": extra.get("variables", {}),
                "call_stack": extra.get("call_stack", []),
                "context": extra.get("context", {}),
                "debug_level": extra.get("debug_level", "INFO"),
                "trace_id": extra.get("trace_id", ""),
                "span_id": extra.get("span_id", ""),
                "user_id": extra.get("user_id", ""),
                "session_id": extra.get("session_id", ""),
                "request_id": extra.get("request_id", "")
            }
            
            return debug_fields
            
        except Exception as e:
            logger.error(f"Failed to extract debug fields: {e}")
            return {}


class StructuredFormatter(ABC):
    """结构化日志格式化器基类"""
    
    def __init__(self, field_extractor: Optional[LogFieldExtractor] = None):
        self.field_extractor = field_extractor or LogFieldExtractor()
        self._lock = threading.RLock()
    
    @abstractmethod
    def format(self, record: Any) -> str:
        """格式化日志记录"""
        pass
    
    def _safe_format(self, record: Any, formatter_func: Callable) -> str:
        """安全格式化，包含错误处理"""
        try:
            with self._lock:
                return formatter_func(record)
        except Exception as e:
            error_msg = {
                "timestamp": datetime.now().isoformat(),
                "level": "ERROR",
                "message": "Log formatting failed",
                "error": str(e),
                "original_record": str(record)[:500]  # 限制长度
            }
            return json.dumps(error_msg, ensure_ascii=False)


class JSONLogFormatter(StructuredFormatter):
    """JSON格式日志格式化器"""
    
    def __init__(self, field_extractor: Optional[LogFieldExtractor] = None, 
                 include_extra: bool = True, pretty_print: bool = False):
        super().__init__(field_extractor)
        self.include_extra = include_extra
        self.pretty_print = pretty_print
    
    def format(self, record: Any) -> str:
        """格式化为JSON格式"""
        return self._safe_format(record, self._format_json)
    
    def _format_json(self, record: Any) -> str:
        """内部JSON格式化逻辑"""
        # 提取标准字段
        fields = self.field_extractor.extract_standard_fields(record)
        
        # 构建JSON结构
        json_record = {
            "timestamp": fields.get("timestamp"),
            "level": fields.get("level"),
            "logger": fields.get("logger_name"),
            "function": fields.get("function_name"),
            "line": fields.get("line_number"),
            "message": fields.get("message"),
            "module": fields.get("module"),
            "process_id": fields.get("process_id"),
            "thread_id": fields.get("thread_id")
        }
        
        # 添加额外字段
        if self.include_extra and "extra" in fields:
            json_record["extra"] = fields["extra"]
        
        # 添加异常信息
        if "exception" in fields:
            json_record["exception"] = fields["exception"]
        
        # 序列化
        if self.pretty_print:
            return json.dumps(json_record, ensure_ascii=False, indent=2)
        else:
            return json.dumps(json_record, ensure_ascii=False, separators=(',', ':'))


class PerformanceLogFormatter(StructuredFormatter):
    """性能监控专用格式化器"""
    
    def __init__(self, field_extractor: Optional[LogFieldExtractor] = None,
                 include_system_metrics: bool = True):
        super().__init__(field_extractor)
        self.include_system_metrics = include_system_metrics
    
    def format(self, record: Any) -> str:
        """格式化性能日志"""
        return self._safe_format(record, self._format_performance)
    
    def _format_performance(self, record: Any) -> str:
        """内部性能格式化逻辑"""
        # 提取基础字段
        fields = self.field_extractor.extract_standard_fields(record)
        
        # 提取性能字段
        perf_fields = self.field_extractor.extract_performance_fields(record)
        
        # 构建性能日志结构
        perf_record = {
            "timestamp": fields.get("timestamp"),
            "level": "PERFORMANCE",
            "logger": fields.get("logger_name"),
            "function": fields.get("function_name"),
            "message": fields.get("message"),
            "metrics": {
                "execution_time_ms": perf_fields.get("execution_time_ms", 0),
                "memory_usage_mb": perf_fields.get("memory_usage_mb", 0),
                "cpu_usage_percent": perf_fields.get("cpu_usage_percent", 0)
            }
        }
        
        # 添加系统指标
        if self.include_system_metrics:
            perf_record["system_metrics"] = {
                "system_memory_percent": perf_fields.get("system_memory_percent", 0),
                "system_cpu_percent": perf_fields.get("system_cpu_percent", 0),
                "process_memory_mb": perf_fields.get("process_memory_mb", 0),
                "process_cpu_percent": perf_fields.get("process_cpu_percent", 0)
            }
        
        # 添加I/O指标
        io_metrics = {
            "io_read_bytes": perf_fields.get("io_read_bytes", 0),
            "io_write_bytes": perf_fields.get("io_write_bytes", 0),
            "network_sent_bytes": perf_fields.get("network_sent_bytes", 0),
            "network_recv_bytes": perf_fields.get("network_recv_bytes", 0)
        }
        
        if any(io_metrics.values()):
            perf_record["io_metrics"] = io_metrics
        
        return json.dumps(perf_record, ensure_ascii=False, separators=(',', ':'))


class DebugLogFormatter(StructuredFormatter):
    """调试信息格式化器"""
    
    def __init__(self, field_extractor: Optional[LogFieldExtractor] = None,
                 include_variables: bool = True, include_stack: bool = True):
        super().__init__(field_extractor)
        self.include_variables = include_variables
        self.include_stack = include_stack
    
    def format(self, record: Any) -> str:
        """格式化调试日志"""
        return self._safe_format(record, self._format_debug)
    
    def _format_debug(self, record: Any) -> str:
        """内部调试格式化逻辑"""
        # 提取基础字段
        fields = self.field_extractor.extract_standard_fields(record)
        
        # 提取调试字段
        debug_fields = self.field_extractor.extract_debug_fields(record)
        
        # 构建调试日志结构
        debug_record = {
            "timestamp": fields.get("timestamp"),
            "level": "DEBUG",
            "logger": fields.get("logger_name"),
            "function": fields.get("function_name"),
            "line": fields.get("line_number"),
            "message": fields.get("message"),
            "context": {
                "trace_id": debug_fields.get("trace_id", ""),
                "span_id": debug_fields.get("span_id", ""),
                "request_id": debug_fields.get("request_id", ""),
                "session_id": debug_fields.get("session_id", ""),
                "user_id": debug_fields.get("user_id", "")
            }
        }
        
        # 添加变量信息
        if self.include_variables and debug_fields.get("variables"):
            debug_record["variables"] = debug_fields["variables"]
        
        # 添加调用栈
        if self.include_stack and debug_fields.get("call_stack"):
            debug_record["call_stack"] = debug_fields["call_stack"]
        
        # 添加异常信息
        if "exception" in fields:
            debug_record["exception"] = fields["exception"]
        
        return json.dumps(debug_record, ensure_ascii=False, indent=2)


class CompactFormatter(StructuredFormatter):
    """紧凑格式化器（用于控制台输出）"""
    
    def __init__(self, field_extractor: Optional[LogFieldExtractor] = None,
                 show_thread: bool = False, show_process: bool = False):
        super().__init__(field_extractor)
        self.show_thread = show_thread
        self.show_process = show_process
    
    def format(self, record: Any) -> str:
        """格式化为紧凑格式"""
        return self._safe_format(record, self._format_compact)
    
    def _format_compact(self, record: Any) -> str:
        """内部紧凑格式化逻辑"""
        # 提取基础字段
        fields = self.field_extractor.extract_standard_fields(record)
        
        # 构建紧凑格式
        parts = [
            fields.get("timestamp", "")[:23],  # 截取到毫秒
            f"[{fields.get('level', 'INFO'):>8}]",
            f"{fields.get('logger_name', 'unknown')}:{fields.get('function_name', 'unknown')}:{fields.get('line_number', 0)}",
        ]
        
        # 添加进程/线程信息
        if self.show_process:
            parts.append(f"PID:{fields.get('process_id', 0)}")
        
        if self.show_thread:
            parts.append(f"TID:{fields.get('thread_id', 0)}")
        
        # 添加消息
        parts.append(f"- {fields.get('message', '')}")
        
        # 添加关键额外信息
        extra = fields.get("extra", {})
        if extra.get("request_id"):
            parts.append(f"[REQ:{extra['request_id'][:8]}]")
        
        if extra.get("execution_time_ms"):
            parts.append(f"[{extra['execution_time_ms']:.1f}ms]")
        
        return " ".join(parts)


class StructuredLoggingFormatter:
    """
    结构化日志格式化器主类
    
    这是结构化日志格式化系统的主要接口类，提供了多种格式化选项
    和统一的格式化管理功能。
    
    主要功能：
    - 多种格式化器管理（JSON、性能、调试、紧凑）
    - 动态格式化器选择
    - 格式化器配置管理
    - 字段提取和清理
    
    使用示例：
        formatter = StructuredLoggingFormatter()
        formatter.set_default_format("json")
        formatted_log = formatter.format(record, format_type="debug")
    """
    
    def __init__(self, field_extractor: Optional[LogFieldExtractor] = None):
        """
        初始化结构化日志格式化器
        
        Args:
            field_extractor: 字段提取器实例
        """
        self.field_extractor = field_extractor or LogFieldExtractor()
        
        # 初始化各种格式化器
        self._formatters = {
            "json": JSONLogFormatter(self.field_extractor),
            "performance": PerformanceLogFormatter(self.field_extractor),
            "debug": DebugLogFormatter(self.field_extractor),
            "compact": CompactFormatter(self.field_extractor)
        }
        
        self._default_format = "json"
        self._lock = threading.RLock()
        
        logger.info("StructuredLoggingFormatter initialized")
    
    def format(self, record: Any, format_type: Optional[str] = None) -> str:
        """
        格式化日志记录
        
        Args:
            record: 日志记录对象
            format_type: 格式化类型 ("json", "performance", "debug", "compact")
            
        Returns:
            格式化后的日志字符串
        """
        try:
            with self._lock:
                format_type = format_type or self._default_format
                
                formatter = self._formatters.get(format_type)
                if not formatter:
                    logger.warning(f"Unknown format type: {format_type}, using default")
                    formatter = self._formatters[self._default_format]
                
                return formatter.format(record)
                
        except Exception as e:
            logger.error(f"Failed to format log record: {e}")
            return self._create_error_log(record, str(e))
    
    def set_default_format(self, format_type: str) -> None:
        """
        设置默认格式化类型
        
        Args:
            format_type: 格式化类型
        """
        with self._lock:
            if format_type in self._formatters:
                self._default_format = format_type
                logger.info(f"Default format set to: {format_type}")
            else:
                logger.warning(f"Invalid format type: {format_type}")
    
    def get_default_format(self) -> str:
        """
        获取默认格式化类型
        
        Returns:
            当前默认格式化类型
        """
        with self._lock:
            return self._default_format
    
    def add_custom_formatter(self, name: str, formatter: StructuredFormatter) -> None:
        """
        添加自定义格式化器
        
        Args:
            name: 格式化器名称
            formatter: 格式化器实例
        """
        with self._lock:
            self._formatters[name] = formatter
            logger.info(f"Custom formatter added: {name}")
    
    def remove_formatter(self, name: str) -> bool:
        """
        移除格式化器
        
        Args:
            name: 格式化器名称
            
        Returns:
            是否成功移除
        """
        with self._lock:
            if name in self._formatters and name not in ["json", "performance", "debug", "compact"]:
                del self._formatters[name]
                logger.info(f"Formatter removed: {name}")
                return True
            else:
                logger.warning(f"Cannot remove formatter: {name}")
                return False
    
    def get_available_formats(self) -> List[str]:
        """
        获取可用的格式化类型列表
        
        Returns:
            格式化类型列表
        """
        with self._lock:
            return list(self._formatters.keys())
    
    def configure_formatter(self, format_type: str, **kwargs) -> bool:
        """
        配置指定格式化器
        
        Args:
            format_type: 格式化类型
            **kwargs: 配置参数
            
        Returns:
            是否配置成功
        """
        try:
            with self._lock:
                formatter = self._formatters.get(format_type)
                if not formatter:
                    logger.warning(f"Formatter not found: {format_type}")
                    return False
                
                # 根据格式化器类型应用配置
                if isinstance(formatter, JSONLogFormatter):
                    if "include_extra" in kwargs:
                        formatter.include_extra = kwargs["include_extra"]
                    if "pretty_print" in kwargs:
                        formatter.pretty_print = kwargs["pretty_print"]
                
                elif isinstance(formatter, PerformanceLogFormatter):
                    if "include_system_metrics" in kwargs:
                        formatter.include_system_metrics = kwargs["include_system_metrics"]
                
                elif isinstance(formatter, DebugLogFormatter):
                    if "include_variables" in kwargs:
                        formatter.include_variables = kwargs["include_variables"]
                    if "include_stack" in kwargs:
                        formatter.include_stack = kwargs["include_stack"]
                
                elif isinstance(formatter, CompactFormatter):
                    if "show_thread" in kwargs:
                        formatter.show_thread = kwargs["show_thread"]
                    if "show_process" in kwargs:
                        formatter.show_process = kwargs["show_process"]
                
                logger.info(f"Formatter configured: {format_type}")
                return True
                
        except Exception as e:
            logger.error(f"Failed to configure formatter {format_type}: {e}")
            return False
    
    def get_formatter_info(self, format_type: str) -> Dict[str, Any]:
        """
        获取格式化器信息
        
        Args:
            format_type: 格式化类型
            
        Returns:
            格式化器信息字典
        """
        try:
            with self._lock:
                formatter = self._formatters.get(format_type)
                if not formatter:
                    return {"error": "formatter_not_found"}
                
                info = {
                    "name": format_type,
                    "class": formatter.__class__.__name__,
                    "description": formatter.__class__.__doc__ or "No description available"
                }
                
                # 添加特定配置信息
                if isinstance(formatter, JSONLogFormatter):
                    info.update({
                        "include_extra": formatter.include_extra,
                        "pretty_print": formatter.pretty_print
                    })
                elif isinstance(formatter, PerformanceLogFormatter):
                    info.update({
                        "include_system_metrics": formatter.include_system_metrics
                    })
                elif isinstance(formatter, DebugLogFormatter):
                    info.update({
                        "include_variables": formatter.include_variables,
                        "include_stack": formatter.include_stack
                    })
                elif isinstance(formatter, CompactFormatter):
                    info.update({
                        "show_thread": formatter.show_thread,
                        "show_process": formatter.show_process
                    })
                
                return info
                
        except Exception as e:
            logger.error(f"Failed to get formatter info for {format_type}: {e}")
            return {"error": "info_retrieval_failed"}
    
    def get_status(self) -> Dict[str, Any]:
        """
        获取格式化器状态
        
        Returns:
            状态信息字典
        """
        try:
            with self._lock:
                status = {
                    "default_format": self._default_format,
                    "available_formats": list(self._formatters.keys()),
                    "formatter_count": len(self._formatters),
                    "field_extractor_class": self.field_extractor.__class__.__name__
                }
                
                return status
                
        except Exception as e:
            logger.error(f"Failed to get formatter status: {e}")
            return {"error": "status_retrieval_failed"}
    
    def _create_error_log(self, record: Any, error_message: str) -> str:
        """
        创建错误日志
        
        Args:
            record: 原始日志记录
            error_message: 错误消息
            
        Returns:
            错误日志字符串
        """
        try:
            error_log = {
                "timestamp": datetime.now().isoformat(),
                "level": "ERROR",
                "logger": "StructuredLoggingFormatter",
                "message": "Log formatting failed",
                "error": error_message,
                "original_record": str(record)[:200]  # 限制长度
            }
            
            return json.dumps(error_log, ensure_ascii=False)
            
        except Exception:
            return f"CRITICAL: Log formatting completely failed - {error_message}"
    
    def test_format(self, format_type: str, sample_data: Optional[Dict[str, Any]] = None) -> str:
        """
        测试格式化器
        
        Args:
            format_type: 格式化类型
            sample_data: 测试数据
            
        Returns:
            格式化测试结果
        """
        try:
            # 创建测试记录
            test_record = sample_data or {
                "time": datetime.now().isoformat(),
                "level": {"name": "INFO"},
                "name": "test_logger",
                "function": "test_function",
                "line": 123,
                "message": "This is a test message",
                "module": "test_module",
                "process": {"id": 12345},
                "thread": {"id": 67890},
                "extra": {
                    "test_variable": "test_value",
                    "execution_time_ms": 42.5,
                    "request_id": "test-req-123"
                }
            }
            
            return self.format(test_record, format_type)
            
        except Exception as e:
            logger.error(f"Failed to test formatter {format_type}: {e}")
            return f"Test failed: {e}"


# 预定义格式化器实例
json_formatter = JSONLogFormatter()
performance_formatter = PerformanceLogFormatter()
debug_formatter = DebugLogFormatter()
compact_formatter = CompactFormatter()

# 全局结构化格式化器实例
_global_structured_formatter: Optional[StructuredLoggingFormatter] = None


def get_global_structured_formatter() -> StructuredLoggingFormatter:
    """获取全局结构化格式化器实例"""
    global _global_structured_formatter
    
    if _global_structured_formatter is None:
        _global_structured_formatter = StructuredLoggingFormatter()
    
    return _global_structured_formatter


# 模块级别的日志器
module_logger = logger.bind(name=__name__)