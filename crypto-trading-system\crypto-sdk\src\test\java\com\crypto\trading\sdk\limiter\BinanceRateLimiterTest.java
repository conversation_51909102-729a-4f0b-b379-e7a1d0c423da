package com.crypto.trading.sdk.limiter;

import com.crypto.trading.sdk.config.BinanceApiConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * 币安限流器测试类
 */
public class BinanceRateLimiterTest {

    private BinanceRateLimiter rateLimiter;
    private BinanceApiConfig mockConfig;

    @BeforeEach
    public void setUp() {
        // 创建模拟的配置对象
        mockConfig = Mockito.mock(BinanceApiConfig.class);
        // 设置最大重试次数
        when(mockConfig.getMaxRetries()).thenReturn(5);
        
        // 创建限流器实例
        rateLimiter = new BinanceRateLimiter(mockConfig);
    }

    @Test
    public void testExecute_Success() {
        // 准备测试数据
        String endpoint = "test_endpoint";
        int weight = 5;
        String expectedResult = "success";
        
        // 创建模拟的请求执行函数
        @SuppressWarnings("unchecked")
        Supplier<String> supplier = Mockito.mock(Supplier.class);
        when(supplier.get()).thenReturn(expectedResult);
        
        // 执行测试
        String result = rateLimiter.execute(weight, endpoint, supplier);
        
        // 验证结果
        assertEquals(expectedResult, result, "执行结果应该与预期一致");
        verify(supplier, times(1)).get();
    }

    @Test
    public void testExecute_Exception() {
        // 准备测试数据
        String endpoint = "test_endpoint";
        int weight = 5;
        RuntimeException expectedException = new RuntimeException("测试异常");
        
        // 创建模拟的请求执行函数
        @SuppressWarnings("unchecked")
        Supplier<String> supplier = Mockito.mock(Supplier.class);
        when(supplier.get()).thenThrow(expectedException);
        
        // 执行测试并验证异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            rateLimiter.execute(weight, endpoint, supplier);
        });
        
        // 验证异常
        assertEquals(expectedException, exception, "应该抛出预期的异常");
        verify(supplier, times(1)).get();
    }

    @Test
    public void testExecuteAsync_Success() throws ExecutionException, InterruptedException {
        // 准备测试数据
        String endpoint = "test_endpoint";
        int weight = 5;
        String expectedResult = "async_success";
        
        // 创建模拟的请求执行函数
        @SuppressWarnings("unchecked")
        Supplier<String> supplier = Mockito.mock(Supplier.class);
        when(supplier.get()).thenReturn(expectedResult);
        
        // 执行测试
        CompletableFuture<String> future = rateLimiter.executeAsync(weight, endpoint, supplier);
        
        // 等待异步操作完成
        String result = future.get();
        
        // 验证结果
        assertEquals(expectedResult, result, "异步执行结果应该与预期一致");
        verify(supplier, times(1)).get();
    }

    @Test
    public void testExecuteAsyncString_Success() throws ExecutionException, InterruptedException {
        // 准备测试数据
        String endpoint = "test_endpoint";
        int weight = 5;
        String expectedResult = "async_string_success";
        
        // 创建模拟的请求执行函数
        @SuppressWarnings("unchecked")
        Supplier<String> supplier = Mockito.mock(Supplier.class);
        when(supplier.get()).thenReturn(expectedResult);
        
        // 执行测试
        CompletableFuture<String> future = rateLimiter.executeAsyncString(weight, endpoint, supplier);
        
        // 等待异步操作完成
        String result = future.get();
        
        // 验证结果
        assertEquals(expectedResult, result, "异步执行结果应该与预期一致");
        verify(supplier, times(1)).get();
    }

    @Test
    public void testGetAvailablePermits() {
        // 准备测试数据
        String endpoint = "test_endpoint";
        
        // 验证初始状态
        int initialPermits = rateLimiter.getAvailablePermits(endpoint);
        assertEquals(1200, initialPermits, "初始可用令牌数应该是默认值1200");
        
        // 执行一个请求
        rateLimiter.execute(5, endpoint, () -> "test");
        
        // 验证剩余令牌数
        int remainingPermits = rateLimiter.getAvailablePermits(endpoint);
        assertEquals(1195, remainingPermits, "剩余令牌数应该是1195");
    }

    @Test
    public void testGetUsageRate() {
        // 准备测试数据
        String endpoint = "test_endpoint";
        
        // 验证初始使用率
        double initialRate = rateLimiter.getUsageRate(endpoint);
        assertEquals(0.0, initialRate, 0.001, "初始使用率应该是0");
        
        // 执行一个请求
        rateLimiter.execute(600, endpoint, () -> "test");
        
        // 验证使用率
        double usageRate = rateLimiter.getUsageRate(endpoint);
        assertEquals(0.5, usageRate, 0.001, "使用率应该是0.5");
    }

    @Test
    public void testReset() {
        // 准备测试数据
        String endpoint = "test_endpoint";
        
        // 执行一些请求
        rateLimiter.execute(600, endpoint, () -> "test1");
        rateLimiter.execute(300, endpoint, () -> "test2");
        
        // 验证使用率
        double usageRate = rateLimiter.getUsageRate(endpoint);
        assertEquals(0.75, usageRate, 0.001, "使用率应该是0.75");
        
        // 重置限流器
        rateLimiter.reset(endpoint);
        
        // 验证重置后的使用率
        double resetRate = rateLimiter.getUsageRate(endpoint);
        assertEquals(0.0, resetRate, 0.001, "重置后使用率应该是0");
    }

    @Test
    public void testUpdateConfig() {
        // 准备测试数据
        String endpoint = "test_endpoint";
        
        // 更新配置
        rateLimiter.updateConfig(endpoint, 2000, 120000);
        
        // 验证配置更新后的可用令牌数
        int availablePermits = rateLimiter.getAvailablePermits(endpoint);
        assertEquals(2000, availablePermits, "更新配置后可用令牌数应该是2000");
        
        // 执行一些请求
        rateLimiter.execute(1000, endpoint, () -> "test");
        
        // 验证使用率
        double usageRate = rateLimiter.getUsageRate(endpoint);
        assertEquals(0.5, usageRate, 0.001, "使用率应该是0.5");
    }

    @Test
    public void testRateLimiting() throws InterruptedException {
        // 准备测试数据
        String endpoint = "test_endpoint";
        int maxRequests = 10;
        long timeWindow = 1000; // 1秒
        
        // 更新配置为更小的限制，便于测试
        rateLimiter.updateConfig(endpoint, maxRequests, timeWindow);
        
        // 执行10个请求，应该能够立即完成
        for (int i = 0; i < maxRequests; i++) {
            rateLimiter.execute(1, endpoint, () -> "test");
        }
        
        // 验证所有令牌都已使用
        assertEquals(0, rateLimiter.getAvailablePermits(endpoint), "所有令牌都应该已使用");
        
        // 创建一个计时器
        long startTime = System.currentTimeMillis();
        
        // 再执行一个请求，这应该会被限流
        rateLimiter.execute(1, endpoint, () -> "test");
        
        // 计算执行时间
        long executionTime = System.currentTimeMillis() - startTime;
        
        // 验证执行时间应该接近或超过时间窗口
        assertTrue(executionTime >= timeWindow * 0.9, 
                "执行时间应该接近或超过时间窗口，实际执行时间: " + executionTime + "ms");
    }

    @Test
    public void testMultipleEndpoints() {
        // 准备测试数据
        String endpoint1 = "endpoint1";
        String endpoint2 = "endpoint2";
        
        // 更新不同端点的配置
        rateLimiter.updateConfig(endpoint1, 100, 60000);
        rateLimiter.updateConfig(endpoint2, 200, 60000);
        
        // 验证不同端点的配置是独立的
        assertEquals(100, rateLimiter.getAvailablePermits(endpoint1), "endpoint1的可用令牌数应该是100");
        assertEquals(200, rateLimiter.getAvailablePermits(endpoint2), "endpoint2的可用令牌数应该是200");
        
        // 执行请求
        rateLimiter.execute(50, endpoint1, () -> "test1");
        rateLimiter.execute(100, endpoint2, () -> "test2");
        
        // 验证不同端点的令牌消耗是独立的
        assertEquals(50, rateLimiter.getAvailablePermits(endpoint1), "endpoint1的剩余令牌数应该是50");
        assertEquals(100, rateLimiter.getAvailablePermits(endpoint2), "endpoint2的剩余令牌数应该是100");
    }
}