package com.crypto.trading.sdk.config;

import okhttp3.Authenticator;
import okhttp3.Credentials;
import okhttp3.OkHttpClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.InetSocketAddress;
import java.net.Proxy;
import java.util.concurrent.TimeUnit;

/**
 * OkHttp客户端工厂类
 * 负责创建和配置OkHttpClient实例
 */
public class OkHttpClientFactory {
    
    private static final Logger log = LoggerFactory.getLogger(OkHttpClientFactory.class);

    /**
     * 根据配置创建OkHttpClient实例
     *
     * @param config 币安API配置
     * @return 配置好的OkHttpClient实例
     */
    public static OkHttpClient createClient(BinanceApiConfig config) {
        OkHttpClient.Builder builder = new OkHttpClient.Builder()
                .connectTimeout(config.getConnectTimeout(), TimeUnit.MILLISECONDS)
                .readTimeout(config.getReadTimeout(), TimeUnit.MILLISECONDS)
                .writeTimeout(config.getWriteTimeout(), TimeUnit.MILLISECONDS)
                .retryOnConnectionFailure(true);
                
        // 配置代理
        if (config.hasProxy()) {
            configureProxy(builder, config);
        }
        
        OkHttpClient client = builder.build();
        
        log.info("创建OkHttpClient: connectTimeout={}ms, readTimeout={}ms, writeTimeout={}ms, proxy={}",
                config.getConnectTimeout(), config.getReadTimeout(), config.getWriteTimeout(),
                config.hasProxy() ? config.getProxyHost() + ":" + config.getProxyPort() : "none");
                
        return client;
    }
    
    /**
     * 配置OkHttpClient的代理设置
     *
     * @param builder OkHttpClient.Builder实例
     * @param config 币安API配置
     */
    private static void configureProxy(OkHttpClient.Builder builder, BinanceApiConfig config) {
        Proxy proxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress(config.getProxyHost(), config.getProxyPort()));
        builder.proxy(proxy);
        
        // 如果配置了代理认证，添加认证
        if (config.getProxyUsername() != null && !config.getProxyUsername().isEmpty() 
                && config.getProxyPassword() != null) {
            Authenticator proxyAuthenticator = (route, response) -> {
                String credential = Credentials.basic(config.getProxyUsername(), config.getProxyPassword());
                return response.request().newBuilder()
                        .header("Proxy-Authorization", credential)
                        .build();
            };
            builder.proxyAuthenticator(proxyAuthenticator);
        }
    }
}