"""
错误处理系统集成器

提供crypto_ml_strategy项目的错误处理系统统一入口，包括系统初始化、
组件集成、配置管理和系统监控。

Author: Crypto ML Strategy Team
Date: 2024-12-20
Version: 1.0.0
"""

import asyncio
from datetime import datetime
from typing import Any, Dict, List, Optional, Callable
from loguru import logger

# 导入所有错误处理组件
from .error_handler_core import (
    global_error_registry, global_error_metrics, ErrorHandler,
    CryptoMLException, ErrorCode, ErrorSeverity, ErrorCategory
)
from .system_health_monitor import (
    global_health_monitor, SystemHealthMonitor, HealthCheck,
    AlertLevel, HealthStatus
)
from .circuit_breaker import (
    global_circuit_registry, global_circuit_monitor, CircuitBreakerConfig
)
from .error_recovery import (
    global_recovery_manager, ErrorRecoveryManager, RecoveryErrorHandler
)
from .graceful_degradation import (
    global_degradation_manager, GracefulDegradationManager,
    DegradationLevel, ServiceDegradationConfig
)
from .failover_manager import (
    global_failover_manager, FailoverManager, ServiceCluster,
    ServiceEndpoint, FailoverTrigger
)
from .error_handling_config import (
    global_config_manager, ErrorHandlingConfig, ConfigManager
)
from .test_error_handling_core import global_test_runner
from .test_error_handling_integration import global_integration_test_runner


class ErrorHandlingSystem:
    """错误处理系统主类"""
    
    def __init__(self):
        self.initialized = False
        self.running = False
        self.config: Optional[ErrorHandlingConfig] = None
        self.start_time: Optional[datetime] = None
        self.initialization_callbacks: List[Callable] = []
        self.shutdown_callbacks: List[Callable] = []
    
    async def initialize(self, config_file: Optional[str] = None) -> bool:
        """初始化错误处理系统"""
        try:
            logger.info("开始初始化错误处理系统")
            
            # 1. 加载配置
            self.config = global_config_manager.load_config(config_file)
            if not self.config.enabled:
                logger.info("错误处理系统已禁用")
                return True
            
            # 2. 初始化错误恢复管理器
            await self._initialize_error_recovery()
            
            # 3. 初始化健康监控
            await self._initialize_health_monitoring()
            
            # 4. 初始化熔断器监控
            await self._initialize_circuit_breaker_monitoring()
            
            # 5. 初始化优雅降级
            await self._initialize_graceful_degradation()
            
            # 6. 初始化故障转移
            await self._initialize_failover_management()
            
            # 7. 注册错误处理器
            await self._register_error_handlers()
            
            # 8. 执行初始化回调
            for callback in self.initialization_callbacks:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback()
                    else:
                        callback()
                except Exception as e:
                    logger.error(f"初始化回调执行失败: {e}")
            
            self.initialized = True
            logger.info("错误处理系统初始化完成")
            return True
            
        except Exception as e:
            logger.error(f"错误处理系统初始化失败: {e}")
            return False
    
    async def start(self) -> bool:
        """启动错误处理系统"""
        if not self.initialized:
            logger.error("系统未初始化，无法启动")
            return False
        
        if self.running:
            logger.warning("错误处理系统已在运行")
            return True
        
        try:
            logger.info("启动错误处理系统")
            self.start_time = datetime.now()
            
            # 启动各个组件的监控
            if self.config.health_check.enabled:
                await global_health_monitor.start_monitoring(
                    self.config.health_check.interval_seconds
                )
            
            if self.config.circuit_breaker.enabled:
                await global_circuit_monitor.start_monitoring(
                    self.config.circuit_breaker.monitor_window_seconds // 10
                )
            
            if self.config.degradation.enabled:
                await global_degradation_manager.start_monitoring(
                    self.config.degradation.monitor_interval_seconds
                )
            
            if self.config.failover.enabled:
                await global_failover_manager.start_monitoring(
                    self.config.failover.monitor_interval_seconds
                )
            
            self.running = True
            logger.info("错误处理系统启动成功")
            return True
            
        except Exception as e:
            logger.error(f"错误处理系统启动失败: {e}")
            return False
    
    async def stop(self) -> bool:
        """停止错误处理系统"""
        if not self.running:
            logger.warning("错误处理系统未在运行")
            return True
        
        try:
            logger.info("停止错误处理系统")
            
            # 停止各个组件的监控
            await global_health_monitor.stop_monitoring()
            await global_circuit_monitor.stop_monitoring()
            await global_degradation_manager.stop_monitoring()
            await global_failover_manager.stop_monitoring()
            
            # 执行关闭回调
            for callback in self.shutdown_callbacks:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback()
                    else:
                        callback()
                except Exception as e:
                    logger.error(f"关闭回调执行失败: {e}")
            
            self.running = False
            logger.info("错误处理系统已停止")
            return True
            
        except Exception as e:
            logger.error(f"错误处理系统停止失败: {e}")
            return False
    
    async def _initialize_error_recovery(self) -> None:
        """初始化错误恢复管理器"""
        logger.debug("初始化错误恢复管理器")
        
        # 配置重试策略
        retry_policy = global_config_manager.to_retry_policy()
        
        # 这里可以添加更多的恢复策略配置
        logger.debug("错误恢复管理器初始化完成")
    
    async def _initialize_health_monitoring(self) -> None:
        """初始化健康监控"""
        logger.debug("初始化健康监控")
        
        # 配置告警管理器
        if self.config.alert.enabled:
            def alert_callback(alert):
                logger.warning(f"系统告警: [{alert.level.value}] {alert.component} - {alert.message}")
            
            global_health_monitor.alert_manager.add_alert_handler(alert_callback)
        
        logger.debug("健康监控初始化完成")
    
    async def _initialize_circuit_breaker_monitoring(self) -> None:
        """初始化熔断器监控"""
        logger.debug("初始化熔断器监控")
        
        # 这里可以预创建一些常用的熔断器
        common_services = ["kafka", "database", "java_api", "model_inference"]
        
        for service in common_services:
            config = global_config_manager.to_circuit_breaker_config()
            await global_circuit_registry.get_or_create(service, config)
        
        logger.debug("熔断器监控初始化完成")
    
    async def _initialize_graceful_degradation(self) -> None:
        """初始化优雅降级"""
        logger.debug("初始化优雅降级")
        
        # 注册核心服务的降级配置
        services_config = {
            "data_processing": {
                "normal_features": {"real_time_processing", "batch_processing", "data_validation", "caching"},
                "partial_features": {"real_time_processing", "data_validation"},
                "minimal_features": {"real_time_processing"}
            },
            "ml_inference": {
                "normal_features": {"deep_learning", "technical_indicators", "feature_engineering", "model_ensemble"},
                "partial_features": {"technical_indicators", "basic_features"},
                "minimal_features": {"technical_indicators"}
            },
            "kafka_communication": {
                "normal_features": {"producer", "consumer", "batch_processing", "error_handling"},
                "partial_features": {"producer", "consumer"},
                "minimal_features": {"producer"}
            }
        }
        
        for service_name, features in services_config.items():
            config = ServiceDegradationConfig(
                service_name=service_name,
                normal_features=set(features["normal_features"]),
                partial_features=set(features["partial_features"]),
                minimal_features=set(features["minimal_features"])
            )
            global_degradation_manager.register_service(service_name, config)
        
        logger.debug("优雅降级初始化完成")
    
    async def _initialize_failover_management(self) -> None:
        """初始化故障转移管理"""
        logger.debug("初始化故障转移管理")
        
        # 这里可以配置一些默认的服务集群
        # 实际使用时应该从配置文件读取
        
        logger.debug("故障转移管理初始化完成")
    
    async def _register_error_handlers(self) -> None:
        """注册错误处理器"""
        logger.debug("注册错误处理器")
        
        # 注册恢复错误处理器
        recovery_handler = RecoveryErrorHandler(global_recovery_manager)
        global_error_registry.register(recovery_handler)
        
        logger.debug("错误处理器注册完成")
    
    def add_initialization_callback(self, callback: Callable) -> None:
        """添加初始化回调"""
        self.initialization_callbacks.append(callback)
    
    def add_shutdown_callback(self, callback: Callable) -> None:
        """添加关闭回调"""
        self.shutdown_callbacks.append(callback)
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        if not self.initialized:
            return {"status": "not_initialized"}
        
        uptime = None
        if self.start_time:
            uptime = (datetime.now() - self.start_time).total_seconds()
        
        return {
            "status": "running" if self.running else "stopped",
            "initialized": self.initialized,
            "uptime_seconds": uptime,
            "config_enabled": self.config.enabled if self.config else False,
            "health_monitoring": global_health_monitor.get_system_status(),
            "circuit_breakers": global_circuit_monitor.get_summary(),
            "degradation": global_degradation_manager.get_system_status(),
            "failover": global_failover_manager.get_system_status(),
            "error_metrics": global_error_metrics.get_error_summary(),
            "recovery_stats": global_recovery_manager.get_recovery_statistics()
        }
    
    async def run_health_check(self) -> Dict[str, Any]:
        """运行系统健康检查"""
        logger.info("运行错误处理系统健康检查")
        
        health_results = {
            "overall_status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "components": {}
        }
        
        try:
            # 检查各个组件状态
            components = {
                "error_registry": len(global_error_registry.handlers) > 0,
                "health_monitor": global_health_monitor.running,
                "circuit_monitor": global_circuit_monitor.monitoring,
                "degradation_manager": global_degradation_manager.monitoring_enabled,
                "failover_manager": global_failover_manager.monitoring_enabled,
                "config_manager": global_config_manager.get_config() is not None
            }
            
            for component, status in components.items():
                health_results["components"][component] = {
                    "status": "healthy" if status else "unhealthy",
                    "details": f"组件{'正常' if status else '异常'}"
                }
                
                if not status:
                    health_results["overall_status"] = "degraded"
            
            # 检查错误率
            error_summary = global_error_metrics.get_error_summary()
            if error_summary.get("total_errors", 0) > 100:  # 错误数量阈值
                health_results["overall_status"] = "warning"
                health_results["components"]["error_rate"] = {
                    "status": "warning",
                    "details": f"错误数量较高: {error_summary['total_errors']}"
                }
            
        except Exception as e:
            logger.error(f"健康检查执行失败: {e}")
            health_results["overall_status"] = "error"
            health_results["error"] = str(e)
        
        return health_results
    
    async def run_comprehensive_test(self) -> Dict[str, Any]:
        """运行综合测试"""
        logger.info("运行错误处理系统综合测试")
        
        test_results = {
            "start_time": datetime.now().isoformat(),
            "core_tests": {},
            "integration_tests": {},
            "end_to_end_test": {},
            "overall_success": False
        }
        
        try:
            # 运行核心测试
            logger.info("运行核心组件测试")
            core_results = await global_test_runner.run_all_tests()
            test_results["core_tests"] = core_results
            
            # 运行集成测试
            logger.info("运行集成测试")
            integration_results = await global_integration_test_runner.run_integration_tests()
            test_results["integration_tests"] = integration_results
            
            # 运行端到端测试
            logger.info("运行端到端测试")
            e2e_results = await global_integration_test_runner.run_end_to_end_test()
            test_results["end_to_end_test"] = e2e_results
            
            # 计算总体成功率
            total_tests = (
                core_results.get("total_tests", 0) + 
                integration_results.get("total_tests", 0)
            )
            passed_tests = (
                core_results.get("passed_tests", 0) + 
                integration_results.get("passed_tests", 0)
            )
            
            if total_tests > 0:
                success_rate = passed_tests / total_tests
                test_results["overall_success"] = success_rate >= 0.9  # 90%通过率
                test_results["success_rate"] = success_rate
            
            test_results["end_time"] = datetime.now().isoformat()
            
        except Exception as e:
            logger.error(f"综合测试执行失败: {e}")
            test_results["error"] = str(e)
            test_results["overall_success"] = False
        
        return test_results
    
    async def create_test_error(self, error_type: str = "data") -> bool:
        """创建测试错误（用于测试错误处理流程）"""
        try:
            if error_type == "data":
                error = CryptoMLException(
                    message="测试数据错误",
                    error_code=ErrorCode.DATA_MISSING,
                    severity=ErrorSeverity.WARNING,
                    category=ErrorCategory.DATA_ERROR
                )
            elif error_type == "model":
                error = CryptoMLException(
                    message="测试模型错误",
                    error_code=ErrorCode.MODEL_LOAD_FAILED,
                    severity=ErrorSeverity.ERROR,
                    category=ErrorCategory.MODEL_ERROR
                )
            elif error_type == "network":
                error = CryptoMLException(
                    message="测试网络错误",
                    error_code=ErrorCode.NETWORK_CONNECTION_FAILED,
                    severity=ErrorSeverity.CRITICAL,
                    category=ErrorCategory.NETWORK_ERROR
                )
            else:
                error = CryptoMLException(
                    message="测试系统错误",
                    error_code=ErrorCode.MEMORY_INSUFFICIENT,
                    severity=ErrorSeverity.CRITICAL,
                    category=ErrorCategory.SYSTEM_ERROR
                )
            
            # 记录错误
            global_error_metrics.record_error(error)
            
            # 尝试处理错误
            from .error_handler_core import ErrorContext
            context = ErrorContext(component="test_system", operation="create_test_error")
            handled = await global_error_registry.handle_error(error, context)
            
            logger.info(f"测试错误已创建并{'处理' if handled else '未处理'}: {error_type}")
            return True
            
        except Exception as e:
            logger.error(f"创建测试错误失败: {e}")
            return False


# 全局错误处理系统实例
global_error_handling_system = ErrorHandlingSystem()


def get_global_error_handler() -> ErrorHandlingSystem:
    """
    获取全局错误处理系统实例
    
    这是一个便捷函数，用于获取全局的错误处理系统实例。
    该实例提供了完整的错误处理功能，包括错误恢复、
    健康监控、熔断器、降级和故障转移等功能。
    
    Returns:
        ErrorHandlingSystem: 全局错误处理系统实例
        
    使用示例:
        error_handler = get_global_error_handler()
        await error_handler.initialize()
        status = error_handler.get_system_status()
    """
    return global_error_handling_system


def get_global_error_handling_system() -> ErrorHandlingSystem:
    """
    获取全局错误处理系统实例（别名函数）
    
    这是get_global_error_handler的别名函数，提供更明确的命名。
    
    Returns:
        ErrorHandlingSystem: 全局错误处理系统实例
    """
    return global_error_handling_system