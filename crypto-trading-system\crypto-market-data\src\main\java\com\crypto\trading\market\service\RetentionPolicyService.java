package com.crypto.trading.market.service;

import com.crypto.trading.market.config.InfluxDBConfig;
import com.influxdb.client.InfluxDBClient;
import com.influxdb.client.QueryApi;
import com.influxdb.client.TasksApi;
import com.influxdb.client.domain.Bucket;
import com.influxdb.client.domain.BucketRetentionRules;
import com.influxdb.client.domain.Task;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.time.Instant;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.List;

/**
 * InfluxDB数据保留策略服务
 * 负责管理InfluxDB数据保留、降采样和归档
 */
@Service
public class RetentionPolicyService {

    private static final Logger log = LoggerFactory.getLogger(RetentionPolicyService.class);

    @Autowired
    private InfluxDBClient influxDBClient;

    @Autowired
    private InfluxDBConfig influxDBConfig;

    /**
     * 中精度数据存储桶
     */
    @Value("${market.influxdb.medium-bucket:market_data_medium}")
    private String mediumBucket;

    /**
     * 低精度数据存储桶
     */
    @Value("${market.influxdb.low-bucket:market_data_low}")
    private String lowBucket;
    
    /**
     * 自动创建下采样任务
     */
    @Value("${market.influxdb.auto-create-tasks:true}")
    private boolean autoCreateTasks;
    
    /**
     * 自动创建桶
     */
    @Value("${market.influxdb.auto-create-buckets:true}")
    private boolean autoCreateBuckets;

    /**
     * 初始化方法
     * 设置数据保留策略和降采样任务
     */
    @PostConstruct
    public void init() {
        if (autoCreateBuckets) {
            createBucketsIfNeeded();
        } else {
            // 即使不创建新桶，也要确保现有桶的保留策略符合配置
            updateAllRetentionPolicies();
        }
        
        if (autoCreateTasks) {
            setupDownsamplingTasks();
        }
    }

    /**
     * 创建所需的存储桶（如果不存在）
     */
    public void createBucketsIfNeeded() {
        log.info("检查并创建所需的存储桶...");
        
        try {
            // 检查或创建中精度数据桶
            createBucketIfNotExists(
                    mediumBucket,
                    "中精度市场数据桶", 
                    influxDBConfig.getMediumPrecisionRetentionDays() * 24 * 3600);

            // 检查或创建低精度数据桶
            createBucketIfNotExists(
                    lowBucket, 
                    "低精度市场数据桶", 
                    influxDBConfig.getLowPrecisionRetentionDays() * 24 * 3600);

            // 创建专用数据类型桶
            // K线数据桶
            createBucketIfNotExists(
                    "kline_data",
                    "K线数据桶",
                    influxDBConfig.getHighPrecisionRetentionHours() * 3600);
            
            // 交易数据桶
            createBucketIfNotExists(
                    "trade_data",
                    "交易数据桶",
                    influxDBConfig.getHighPrecisionRetentionHours() * 3600);
            
            // 深度数据桶
            createBucketIfNotExists(
                    "depth_data",
                    "深度数据桶",
                    influxDBConfig.getHighPrecisionRetentionHours() * 3600);

            // 更新原始数据桶的保留策略
            updateMainBucketRetention();
        } catch (Exception e) {
            log.error("创建存储桶时出错: {}", e.getMessage(), e);
        }
    }

    /**
     * 更新主数据桶的保留策略
     */
    private void updateMainBucketRetention() {
        log.info("更新主数据桶 {} 的保留策略...", influxDBConfig.getBucket());
        
        try {
            // 查找主数据桶
            Bucket bucket = influxDBClient.getBucketsApi().findBucketByName(influxDBConfig.getBucket());
            if (bucket != null) {
                // 设置保留策略
                BucketRetentionRules retentionRules = new BucketRetentionRules();
                retentionRules.setEverySeconds(influxDBConfig.getHighPrecisionRetentionHours() * 3600);
                
                bucket.setRetentionRules(List.of(retentionRules));
                
                // 更新桶
                influxDBClient.getBucketsApi().updateBucket(bucket);
                log.info("已更新主数据桶 {} 的保留策略为 {} 小时", influxDBConfig.getBucket(), 
                        influxDBConfig.getHighPrecisionRetentionHours());
            } else {
                log.warn("未找到主数据桶: {}", influxDBConfig.getBucket());
            }
        } catch (Exception e) {
            log.error("更新主数据桶保留策略时出错: {}", e.getMessage(), e);
        }
    }

    /**
     * 如果存储桶不存在，则创建
     *
     * @param bucketName 存储桶名称
     * @param description 存储桶描述
     * @param retentionSeconds 保留时间（秒）
     */
    private void createBucketIfNotExists(String bucketName, String description, int retentionSeconds) {
        try {
            // 检查桶是否存在
            Bucket bucket = influxDBClient.getBucketsApi().findBucketByName(bucketName);
            
            if (bucket == null) {
                log.info("创建存储桶: {}, 保留时间: {} 秒", bucketName, retentionSeconds);
                
                // 创建保留规则
                BucketRetentionRules retentionRules = new BucketRetentionRules();
                retentionRules.setEverySeconds(retentionSeconds);
                
                // 获取组织ID
                String orgID = influxDBClient.getOrganizationsApi()
                    .findOrganizations()
                    .stream()
                    .filter(org -> org.getName().equals(influxDBConfig.getOrg()))
                    .findFirst()
                    .orElseThrow(() -> new RuntimeException("未找到组织: " + influxDBConfig.getOrg()))
                    .getId();
                
                // 创建新桶
                bucket = influxDBClient.getBucketsApi().createBucket(
                        bucketName,
                        retentionRules,
                        orgID);
                
                log.info("已创建存储桶: {}, ID: {}", bucketName, bucket.getId());
            } else {
                log.info("存储桶已存在: {}, ID: {}", bucketName, bucket.getId());
                
                // 更新保留规则
                if (bucket.getRetentionRules().isEmpty() || 
                        bucket.getRetentionRules().get(0).getEverySeconds() != retentionSeconds) {
                    BucketRetentionRules rules = new BucketRetentionRules();
                    rules.setEverySeconds(retentionSeconds);
                    
                    bucket.setRetentionRules(List.of(rules));
                    influxDBClient.getBucketsApi().updateBucket(bucket);
                    
                    log.info("已更新存储桶 {} 的保留策略为 {} 秒", bucketName, retentionSeconds);
                }
            }
        } catch (Exception e) {
            log.error("检查或创建存储桶 {} 时出错: {}", bucketName, e.getMessage(), e);
        }
    }

    /**
     * 设置降采样任务
     */
    public void setupDownsamplingTasks() {
        log.info("设置降采样任务...");
        
        try {
            // 创建小时级下采样任务
            createDownsampleTask(
                    "hourly_downsample_task",
                    "1h", 
                    influxDBConfig.getBucket(), 
                    mediumBucket,
                    "每小时降采样任务");
            
            // 创建日级下采样任务
            createDownsampleTask(
                    "daily_downsample_task",
                    "1d",
                    mediumBucket, 
                    lowBucket,
                    "每天降采样任务");
            
        } catch (Exception e) {
            log.error("设置降采样任务时出错: {}", e.getMessage(), e);
        }
    }

    /**
     * 创建下采样任务
     *
     * @param taskName      任务名称
     * @param interval      聚合间隔
     * @param sourceBucket  源存储桶
     * @param targetBucket  目标存储桶
     * @param description   任务描述
     */
    private void createDownsampleTask(String taskName, String interval, String sourceBucket, 
                                     String targetBucket, String description) {
        try {
            TasksApi tasksApi = influxDBClient.getTasksApi();
            
            // 检查任务是否已存在
            List<Task> existingTasks = tasksApi.findTasks();
            boolean taskExists = existingTasks.stream()
                    .anyMatch(task -> task.getName().equals(taskName));
            
            if (!taskExists) {
                log.info("创建降采样任务: {}, 间隔: {}, 源桶: {}, 目标桶: {}", 
                        taskName, interval, sourceBucket, targetBucket);
                
                // 创建Flux查询
                String flux = createDownsampleFluxQuery(interval, sourceBucket, targetBucket);
                
                // 创建任务
                Task task = new Task();
                task.setName(taskName);
                task.setFlux(flux);
                task.setDescription(description);
                // 使用反射设置status字段，因为API版本不兼容
                try {
                    java.lang.reflect.Method setStatusMethod = Task.class.getMethod("setStatus", String.class);
                    setStatusMethod.invoke(task, "active");
                } catch (Exception e) {
                    log.warn("无法设置任务状态，可能需要更新InfluxDB客户端版本: {}", e.getMessage());
                }
                task.setOrgID(influxDBClient.getOrganizationsApi().findOrganizations().get(0).getId());
                // 设置每小时执行一次
                task.setEvery(interval.equals("1h") ? "1h" : "24h");
                
                tasksApi.createTask(task);
                
                log.info("已创建降采样任务: {}", taskName);
            } else {
                log.info("降采样任务已存在: {}", taskName);
            }
        } catch (Exception e) {
            log.error("创建降采样任务 {} 时出错: {}", taskName, e.getMessage(), e);
        }
    }

    /**
     * 创建降采样Flux查询
     *
     * @param interval      聚合间隔
     * @param sourceBucket  源存储桶
     * @param targetBucket  目标存储桶
     * @return Flux查询字符串
     */
    private String createDownsampleFluxQuery(String interval, String sourceBucket, String targetBucket) {
        return "option task = {name: \"" + sourceBucket + "_to_" + targetBucket + "\", " +
                "every: " + interval + "}\n\n" +
                "data = from(bucket:\"" + sourceBucket + "\")\n" +
                "  |> range(start: -task.every)\n" +
                "  |> filter(fn: (r) => r._measurement == \"kline\" or " +
                        "r._measurement == \"depth\" or " +
                        "r._measurement == \"trade\")\n\n" +
                "data\n" +
                "  |> aggregateWindow(every: " + interval + ", fn: mean, createEmpty: false)\n" +
                "  |> to(bucket:\"" + targetBucket + "\", org:\"" + influxDBConfig.getOrg() + "\")\n\n" +
                "data\n" +
                "  |> filter(fn: (r) => exists r._value)\n" +
                "  |> aggregateWindow(every: " + interval + ", fn: max, createEmpty: false)\n" +
                "  |> set(key: \"aggregation\", value: \"max\")\n" +
                "  |> to(bucket:\"" + targetBucket + "\", org:\"" + influxDBConfig.getOrg() + "\")\n\n" +
                "data\n" +
                "  |> filter(fn: (r) => exists r._value)\n" +
                "  |> aggregateWindow(every: " + interval + ", fn: min, createEmpty: false)\n" +
                "  |> set(key: \"aggregation\", value: \"min\")\n" +
                "  |> to(bucket:\"" + targetBucket + "\", org:\"" + influxDBConfig.getOrg() + "\")\n\n";
    }

    /**
     * 更新所有存储桶的保留策略
     * 确保即使现有的桶也遵循最新的保留策略配置
     */
    private void updateAllRetentionPolicies() {
        log.info("更新所有存储桶的保留策略...");
        
        try {
            // 更新主数据桶
            updateMainBucketRetention();
            
            // 更新K线数据桶 - 如果不存在则创建
            try {
                updateBucketRetention("kline_data", influxDBConfig.getHighPrecisionRetentionHours() * 3600);
            } catch (Exception e) {
                log.info("K线数据桶不存在，将创建: {}", e.getMessage());
                createBucketIfNotExists("kline_data", "K线数据桶", influxDBConfig.getHighPrecisionRetentionHours() * 3600);
            }
            
            // 更新交易数据桶 - 如果不存在则创建
            try {
                updateBucketRetention("trade_data", influxDBConfig.getHighPrecisionRetentionHours() * 3600);
            } catch (Exception e) {
                log.info("交易数据桶不存在，将创建: {}", e.getMessage());
                createBucketIfNotExists("trade_data", "交易数据桶", influxDBConfig.getHighPrecisionRetentionHours() * 3600);
            }
            
            // 更新深度数据桶 - 如果不存在则创建
            try {
                updateBucketRetention("depth_data", influxDBConfig.getHighPrecisionRetentionHours() * 3600);
            } catch (Exception e) {
                log.info("深度数据桶不存在，将创建: {}", e.getMessage());
                createBucketIfNotExists("depth_data", "深度数据桶", influxDBConfig.getHighPrecisionRetentionHours() * 3600);
            }
            
            // 更新中精度和低精度桶
            updateBucketRetention(mediumBucket, influxDBConfig.getMediumPrecisionRetentionDays() * 24 * 3600);
            updateBucketRetention(lowBucket, influxDBConfig.getLowPrecisionRetentionDays() * 24 * 3600);
            
            log.info("所有存储桶的保留策略已更新");
        } catch (Exception e) {
            log.error("更新存储桶保留策略时出错: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 更新指定存储桶的保留策略
     *
     * @param bucketName 存储桶名称
     * @param retentionSeconds 保留时间（秒）
     */
    private void updateBucketRetention(String bucketName, int retentionSeconds) {
        try {
            Bucket bucket = influxDBClient.getBucketsApi().findBucketByName(bucketName);
            if (bucket != null) {
                // 设置保留策略
                BucketRetentionRules retentionRules = new BucketRetentionRules();
                retentionRules.setEverySeconds(retentionSeconds);
                
                bucket.setRetentionRules(List.of(retentionRules));
                
                // 更新桶
                influxDBClient.getBucketsApi().updateBucket(bucket);
                log.info("已更新存储桶 {} 的保留策略为 {} 秒", bucketName, retentionSeconds);
            } else {
                log.warn("未找到存储桶: {}", bucketName);
            }
        } catch (Exception e) {
            log.error("更新存储桶 {} 保留策略时出错: {}", bucketName, e.getMessage(), e);
        }
    }
} 