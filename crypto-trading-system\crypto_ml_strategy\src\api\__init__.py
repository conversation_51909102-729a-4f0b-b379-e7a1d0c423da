"""
Crypto ML Strategy - API和兼容性模块

该包包含API接口、Java兼容性、Kafka通信、序列化兼容性
等外部接口和通信相关的所有组件。

主要组件:
- java_api_core: Java API核心接口
- kafka_client: Kafka客户端
- serialization_compatibility: 序列化兼容性
- api_compatibility_docs: API兼容性文档
- java_api_compatibility_runner: Java API兼容性运行器

作者: Crypto ML Strategy Team
版本: 1.0.0
"""

# API组件将在需要时导入
# from .java_api_core import JavaApiCore
# from .kafka_client import KafkaClient
# from .serialization_compatibility import SerializationCompatibility

__all__ = []

__version__ = '1.0.0'
__author__ = 'Crypto ML Strategy Team'