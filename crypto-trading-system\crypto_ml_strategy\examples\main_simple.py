#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
简化版主入口文件，用于测试基本功能
"""

import os
import sys
import signal
import time
from typing import Dict, Any, Optional
from loguru import logger

# 添加src目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 只导入最基本的模块进行测试
from core.config import Config

class SimpleTradingStrategyApp:
    """简化版交易策略应用"""
    
    def __init__(self):
        """初始化应用"""
        logger.info("初始化简化版交易策略应用")
        self.config = Config()
        self.running = False
        
        # 注册信号处理器
        signal.signal(signal.SIGINT, self.handle_signal)
        signal.signal(signal.SIGTERM, self.handle_signal)
    
    def start(self):
        """启动应用"""
        logger.info("启动简化版交易策略应用")
        self.running = True
        
        # 简单的主循环
        while self.running:
            try:
                logger.info("应用运行中...")
                time.sleep(5)  # 每5秒输出一次状态
                
            except Exception as e:
                logger.error(f"主循环异常: {str(e)}")
                time.sleep(1)
    
    def handle_signal(self, signum, frame):
        """处理终止信号"""
        logger.info(f"接收到信号 {signum}，准备关闭应用...")
        self.running = False
    
    def stop(self):
        """停止应用"""
        logger.info("关闭简化版交易策略应用")
        self.running = False


def main():
    """程序入口"""
    app = SimpleTradingStrategyApp()
    
    try:
        app.start()
    except KeyboardInterrupt:
        logger.info("收到键盘中断")
    except Exception as e:
        logger.error(f"应用运行异常: {str(e)}")
    finally:
        app.stop()


if __name__ == "__main__":
    main()