package com.crypto.trading.sdk.response.model;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * API响应模型测试类
 */
public class ApiResponseTest {

    @Test
    public void testDefaultConstructor() {
        // 测试默认构造函数
        ApiResponse<String> response = new ApiResponse<>();
        
        // 验证默认值
        assertNull(response.getData());
        assertTrue(response.isSuccess());
        assertEquals(0, response.getCode());
        assertNull(response.getMsg());
    }

    @Test
    public void testConstructorWithParameters() {
        // 测试带参数的构造函数
        String data = "test data";
        int code = 200;
        String msg = "操作成功";
        
        ApiResponse<String> response = new ApiResponse<>(code, msg, data);
        
        // 验证属性
        assertTrue(response.isSuccess());
        assertEquals(data, response.getData());
        assertEquals(code, response.getCode());
        assertEquals(msg, response.getMsg());
    }

    @Test
    public void testErrorResponse() {
        // 测试错误响应
        int code = 400;
        String msg = "测试错误消息";
        
        ApiResponse<String> response = new ApiResponse<>(code, msg, null);
        
        // 验证属性
        assertFalse(response.isSuccess());
        assertNull(response.getData());
        assertEquals(code, response.getCode());
        assertEquals(msg, response.getMsg());
    }

    @Test
    public void testSettersAndGetters() {
        // 测试设置器和获取器
        ApiResponse<Integer> response = new ApiResponse<>();
        
        // 设置属性
        response.setCode(201);
        response.setData(123);
        response.setMsg("测试消息");
        
        // 验证属性
        assertEquals(201, response.getCode());
        assertEquals(Integer.valueOf(123), response.getData());
        assertEquals("测试消息", response.getMsg());
        assertTrue(response.isSuccess());
    }

    @Test
    public void testToString() {
        // 测试toString方法
        ApiResponse<String> response = new ApiResponse<>(200, "成功", "test data");
        
        String toString = response.toString();
        assertNotNull(toString);
        assertTrue(toString.contains("code=200"));
        assertTrue(toString.contains("msg='成功'"));
        assertTrue(toString.contains("data=test data"));
    }

    @Test
    public void testIsSuccess() {
        // 测试isSuccess方法 - 当code为0时
        ApiResponse<String> response1 = new ApiResponse<>(0, "成功", "data1");
        assertTrue(response1.isSuccess());
        
        // 测试isSuccess方法 - 当code为200时
        ApiResponse<String> response2 = new ApiResponse<>(200, "成功", "data2");
        assertTrue(response2.isSuccess());
        
        // 测试isSuccess方法 - 当code为其他值时
        ApiResponse<String> response3 = new ApiResponse<>(400, "失败", null);
        assertFalse(response3.isSuccess());
    }
} 