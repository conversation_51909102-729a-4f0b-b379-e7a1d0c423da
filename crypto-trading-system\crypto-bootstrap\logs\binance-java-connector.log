2025-06-22 02:43:41.527[1750531421527] | INFO  | main       | c.c.t.b.i.AbstractModuleInitializer  - 模块[test-module]开始初始化...
2025-06-22 02:43:41.530[1750531421530] | INFO  | main       | c.c.t.b.i.AbstractModuleInitializer  - 模块[test-module]正在执行初始化...
2025-06-22 02:43:41.530[1750531421530] | INFO  | main       | c.c.t.b.i.AbstractModuleInitializer  - 模块[test-module]初始化完成
2025-06-22 02:43:41.530[1750531421530] | INFO  | main       | c.c.t.b.i.AbstractModuleInitializer  - 模块[test-module]初始化后处理完成
2025-06-22 02:43:41.545[1750531421545] | INFO  | main       | c.c.t.b.i.AbstractModuleInitializer  - 模块[test-module]正在执行初始化...
2025-06-22 02:43:41.545[1750531421545] | INFO  | main       | c.c.t.b.i.AbstractModuleInitializer  - 模块[test-module]初始化完成
2025-06-22 02:43:41.546[1750531421546] | INFO  | main       | c.c.t.b.i.AbstractModuleInitializer  - 模块[test-module]正在关闭...
2025-06-22 02:43:41.546[1750531421546] | INFO  | main       | c.c.t.b.i.AbstractModuleInitializer  - 模块[test-module]已关闭
2025-06-22 02:43:41.551[1750531421551] | INFO  | main       | c.c.t.b.i.AbstractModuleInitializer  - 模块[test-module]开始初始化...
2025-06-22 02:43:41.552[1750531421552] | INFO  | main       | c.c.t.b.i.AbstractModuleInitializer  - 模块[test-module]正在执行初始化...
2025-06-22 02:43:41.552[1750531421552] | ERROR | main       | c.c.t.b.i.AbstractModuleInitializer  - 模块[test-module]初始化失败: 测试异常
java.lang.RuntimeException: 测试异常
	at com.crypto.trading.bootstrap.initializer.AbstractModuleInitializerTest.testInitializeWithException(AbstractModuleInitializerTest.java:85)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.junit.platform.commons.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:728)
	at org.junit.jupiter.engine.execution.MethodInvocation.proceed(MethodInvocation.java:60)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$ValidatingInvocation.proceed(InvocationInterceptorChain.java:131)
	at org.junit.jupiter.engine.extension.TimeoutExtension.intercept(TimeoutExtension.java:156)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestableMethod(TimeoutExtension.java:147)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestMethod(TimeoutExtension.java:86)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker$ReflectiveInterceptorCall.lambda$ofVoidMethod$0(InterceptingExecutableInvoker.java:103)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.lambda$invoke$0(InterceptingExecutableInvoker.java:93)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$InterceptedInvocation.proceed(InvocationInterceptorChain.java:106)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.proceed(InvocationInterceptorChain.java:64)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.chainAndInvoke(InvocationInterceptorChain.java:45)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.invoke(InvocationInterceptorChain.java:37)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:92)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:86)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$invokeTestMethod$7(TestMethodTestDescriptor.java:218)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.invokeTestMethod(TestMethodTestDescriptor.java:214)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:139)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:69)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.apache.maven.surefire.junitplatform.LazyLauncher.execute(LazyLauncher.java:56)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.execute(JUnitPlatformProvider.java:184)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invokeAllTests(JUnitPlatformProvider.java:148)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invoke(JUnitPlatformProvider.java:122)
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:385)
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:162)
	at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:507)
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:495)
2025-06-22 02:43:42.238[1750531422238] | INFO  | main       | c.c.t.b.lifecycle.ShutdownHook       - 应用程序正在关闭...
2025-06-22 02:43:42.241[1750531422241] | ERROR | main       | c.c.t.b.lifecycle.ShutdownHook       - 应用程序关闭过程中发生错误: 测试异常
java.lang.RuntimeException: 测试异常
	at com.crypto.trading.bootstrap.lifecycle.ShutdownHook.performShutdown(ShutdownHook.java:61)
	at com.crypto.trading.bootstrap.lifecycle.ShutdownHookTest.lambda$testPerformShutdownWithException$0(ShutdownHookTest.java:83)
	at org.junit.jupiter.api.AssertDoesNotThrow.assertDoesNotThrow(AssertDoesNotThrow.java:49)
	at org.junit.jupiter.api.AssertDoesNotThrow.assertDoesNotThrow(AssertDoesNotThrow.java:36)
	at org.junit.jupiter.api.Assertions.assertDoesNotThrow(Assertions.java:3164)
	at com.crypto.trading.bootstrap.lifecycle.ShutdownHookTest.testPerformShutdownWithException(ShutdownHookTest.java:83)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.junit.platform.commons.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:728)
	at org.junit.jupiter.engine.execution.MethodInvocation.proceed(MethodInvocation.java:60)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$ValidatingInvocation.proceed(InvocationInterceptorChain.java:131)
	at org.junit.jupiter.engine.extension.TimeoutExtension.intercept(TimeoutExtension.java:156)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestableMethod(TimeoutExtension.java:147)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestMethod(TimeoutExtension.java:86)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker$ReflectiveInterceptorCall.lambda$ofVoidMethod$0(InterceptingExecutableInvoker.java:103)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.lambda$invoke$0(InterceptingExecutableInvoker.java:93)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$InterceptedInvocation.proceed(InvocationInterceptorChain.java:106)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.proceed(InvocationInterceptorChain.java:64)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.chainAndInvoke(InvocationInterceptorChain.java:45)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.invoke(InvocationInterceptorChain.java:37)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:92)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:86)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$invokeTestMethod$7(TestMethodTestDescriptor.java:218)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.invokeTestMethod(TestMethodTestDescriptor.java:214)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:139)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:69)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.apache.maven.surefire.junitplatform.LazyLauncher.execute(LazyLauncher.java:56)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.execute(JUnitPlatformProvider.java:184)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invokeAllTests(JUnitPlatformProvider.java:148)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invoke(JUnitPlatformProvider.java:122)
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:385)
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:162)
	at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:507)
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:495)
2025-06-22 02:43:42.252[1750531422252] | INFO  | main       | c.c.t.b.lifecycle.ShutdownHook       - 应用程序正在关闭...
2025-06-22 02:43:42.255[1750531422255] | INFO  | main       | c.c.t.b.lifecycle.ShutdownHook       - 应用程序已关闭
2025-06-22 02:43:42.258[1750531422258] | INFO  | main       | c.c.t.b.lifecycle.ShutdownHook       - 应用程序正在关闭...
2025-06-22 02:43:42.258[1750531422258] | INFO  | main       | c.c.t.b.lifecycle.ShutdownHook       - 应用程序已关闭
2025-06-22 02:43:42.261[1750531422261] | INFO  | main       | c.c.t.b.lifecycle.ShutdownHook       - 已注册JVM关闭钩子
2025-06-22 02:43:42.278[1750531422278] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 注册模块初始化器: A, 优先级: 10
2025-06-22 02:43:42.278[1750531422278] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 注册模块初始化器: B, 优先级: 20
2025-06-22 02:43:42.279[1750531422279] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 注册模块初始化器: C, 优先级: 30
2025-06-22 02:43:42.280[1750531422280] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 开始初始化 3 个模块，初始化顺序: A -> B -> C
2025-06-22 02:43:42.280[1750531422280] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 所有模块初始化完成
2025-06-22 02:43:42.281[1750531422281] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 注册模块初始化器: A, 优先级: 10
2025-06-22 02:43:42.281[1750531422281] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 注册模块初始化器: B, 优先级: 20
2025-06-22 02:43:42.282[1750531422282] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 注册模块初始化器: C, 优先级: 30
2025-06-22 02:43:42.283[1750531422283] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 注册模块初始化器: A, 优先级: 10
2025-06-22 02:43:42.283[1750531422283] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 注册模块初始化器: B, 优先级: 20
2025-06-22 02:43:42.283[1750531422283] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 注册模块初始化器: C, 优先级: 30
2025-06-22 02:43:42.283[1750531422283] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 注册模块初始化器: A, 优先级: 10
2025-06-22 02:43:42.283[1750531422283] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 注册模块初始化器: D, 优先级: 40
2025-06-22 02:43:42.286[1750531422286] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 注册模块初始化器: A, 优先级: 10
2025-06-22 02:43:42.286[1750531422286] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 注册模块初始化器: B, 优先级: 20
2025-06-22 02:43:42.286[1750531422286] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 注册模块初始化器: C, 优先级: 30
2025-06-22 02:43:42.287[1750531422287] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 开始初始化 3 个模块，初始化顺序: A -> B -> C
2025-06-22 02:43:42.287[1750531422287] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 所有模块初始化完成
2025-06-22 02:43:42.287[1750531422287] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 开始关闭所有模块，关闭顺序: C -> B -> A
2025-06-22 02:43:42.288[1750531422288] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 所有模块已关闭
2025-06-22 02:43:42.291[1750531422291] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 注册模块初始化器: A, 优先级: 10
2025-06-22 02:43:42.291[1750531422291] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 注册模块初始化器: B, 优先级: 20
2025-06-22 02:43:42.291[1750531422291] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 注册模块初始化器: C, 优先级: 30
2025-06-22 02:43:42.291[1750531422291] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 开始初始化 3 个模块，初始化顺序: A -> B -> C
2025-06-22 02:43:42.385[1750531422385] | INFO  | ShutdownHookThread | c.c.t.b.lifecycle.ShutdownHook       - 应用程序正在关闭...
2025-06-22 02:43:42.385[1750531422385] | INFO  | ShutdownHookThread | c.c.t.b.lifecycle.ShutdownHook       - 应用程序已关闭
2025-06-22 03:50:34.178[1750535434178] | INFO  | main       | c.c.t.b.i.AbstractModuleInitializer  - 模块[test-module]开始初始化...
2025-06-22 03:50:34.181[1750535434181] | INFO  | main       | c.c.t.b.i.AbstractModuleInitializer  - 模块[test-module]正在执行初始化...
2025-06-22 03:50:34.181[1750535434181] | INFO  | main       | c.c.t.b.i.AbstractModuleInitializer  - 模块[test-module]初始化完成
2025-06-22 03:50:34.181[1750535434181] | INFO  | main       | c.c.t.b.i.AbstractModuleInitializer  - 模块[test-module]初始化后处理完成
2025-06-22 03:50:34.195[1750535434195] | INFO  | main       | c.c.t.b.i.AbstractModuleInitializer  - 模块[test-module]正在执行初始化...
2025-06-22 03:50:34.196[1750535434196] | INFO  | main       | c.c.t.b.i.AbstractModuleInitializer  - 模块[test-module]初始化完成
2025-06-22 03:50:34.196[1750535434196] | INFO  | main       | c.c.t.b.i.AbstractModuleInitializer  - 模块[test-module]正在关闭...
2025-06-22 03:50:34.197[1750535434197] | INFO  | main       | c.c.t.b.i.AbstractModuleInitializer  - 模块[test-module]已关闭
2025-06-22 03:50:34.201[1750535434201] | INFO  | main       | c.c.t.b.i.AbstractModuleInitializer  - 模块[test-module]开始初始化...
2025-06-22 03:50:34.203[1750535434203] | INFO  | main       | c.c.t.b.i.AbstractModuleInitializer  - 模块[test-module]正在执行初始化...
2025-06-22 03:50:34.203[1750535434203] | ERROR | main       | c.c.t.b.i.AbstractModuleInitializer  - 模块[test-module]初始化失败: 测试异常
java.lang.RuntimeException: 测试异常
	at com.crypto.trading.bootstrap.initializer.AbstractModuleInitializerTest.testInitializeWithException(AbstractModuleInitializerTest.java:85)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.junit.platform.commons.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:728)
	at org.junit.jupiter.engine.execution.MethodInvocation.proceed(MethodInvocation.java:60)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$ValidatingInvocation.proceed(InvocationInterceptorChain.java:131)
	at org.junit.jupiter.engine.extension.TimeoutExtension.intercept(TimeoutExtension.java:156)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestableMethod(TimeoutExtension.java:147)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestMethod(TimeoutExtension.java:86)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker$ReflectiveInterceptorCall.lambda$ofVoidMethod$0(InterceptingExecutableInvoker.java:103)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.lambda$invoke$0(InterceptingExecutableInvoker.java:93)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$InterceptedInvocation.proceed(InvocationInterceptorChain.java:106)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.proceed(InvocationInterceptorChain.java:64)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.chainAndInvoke(InvocationInterceptorChain.java:45)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.invoke(InvocationInterceptorChain.java:37)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:92)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:86)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$invokeTestMethod$7(TestMethodTestDescriptor.java:218)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.invokeTestMethod(TestMethodTestDescriptor.java:214)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:139)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:69)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.apache.maven.surefire.junitplatform.LazyLauncher.execute(LazyLauncher.java:56)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.execute(JUnitPlatformProvider.java:184)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invokeAllTests(JUnitPlatformProvider.java:148)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invoke(JUnitPlatformProvider.java:122)
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:385)
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:162)
	at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:507)
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:495)
2025-06-22 03:50:34.947[1750535434947] | INFO  | main       | c.c.t.b.lifecycle.ShutdownHook       - 应用程序正在关闭...
2025-06-22 03:50:34.950[1750535434950] | ERROR | main       | c.c.t.b.lifecycle.ShutdownHook       - 应用程序关闭过程中发生错误: 测试异常
java.lang.RuntimeException: 测试异常
	at com.crypto.trading.bootstrap.lifecycle.ShutdownHook.performShutdown(ShutdownHook.java:61)
	at com.crypto.trading.bootstrap.lifecycle.ShutdownHookTest.lambda$testPerformShutdownWithException$0(ShutdownHookTest.java:83)
	at org.junit.jupiter.api.AssertDoesNotThrow.assertDoesNotThrow(AssertDoesNotThrow.java:49)
	at org.junit.jupiter.api.AssertDoesNotThrow.assertDoesNotThrow(AssertDoesNotThrow.java:36)
	at org.junit.jupiter.api.Assertions.assertDoesNotThrow(Assertions.java:3164)
	at com.crypto.trading.bootstrap.lifecycle.ShutdownHookTest.testPerformShutdownWithException(ShutdownHookTest.java:83)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.junit.platform.commons.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:728)
	at org.junit.jupiter.engine.execution.MethodInvocation.proceed(MethodInvocation.java:60)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$ValidatingInvocation.proceed(InvocationInterceptorChain.java:131)
	at org.junit.jupiter.engine.extension.TimeoutExtension.intercept(TimeoutExtension.java:156)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestableMethod(TimeoutExtension.java:147)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestMethod(TimeoutExtension.java:86)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker$ReflectiveInterceptorCall.lambda$ofVoidMethod$0(InterceptingExecutableInvoker.java:103)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.lambda$invoke$0(InterceptingExecutableInvoker.java:93)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$InterceptedInvocation.proceed(InvocationInterceptorChain.java:106)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.proceed(InvocationInterceptorChain.java:64)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.chainAndInvoke(InvocationInterceptorChain.java:45)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.invoke(InvocationInterceptorChain.java:37)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:92)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:86)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$invokeTestMethod$7(TestMethodTestDescriptor.java:218)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.invokeTestMethod(TestMethodTestDescriptor.java:214)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:139)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:69)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.apache.maven.surefire.junitplatform.LazyLauncher.execute(LazyLauncher.java:56)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.execute(JUnitPlatformProvider.java:184)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invokeAllTests(JUnitPlatformProvider.java:148)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invoke(JUnitPlatformProvider.java:122)
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:385)
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:162)
	at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:507)
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:495)
2025-06-22 03:50:34.960[1750535434960] | INFO  | main       | c.c.t.b.lifecycle.ShutdownHook       - 应用程序正在关闭...
2025-06-22 03:50:34.962[1750535434962] | INFO  | main       | c.c.t.b.lifecycle.ShutdownHook       - 应用程序已关闭
2025-06-22 03:50:34.965[1750535434965] | INFO  | main       | c.c.t.b.lifecycle.ShutdownHook       - 应用程序正在关闭...
2025-06-22 03:50:34.965[1750535434965] | INFO  | main       | c.c.t.b.lifecycle.ShutdownHook       - 应用程序已关闭
2025-06-22 03:50:34.968[1750535434968] | INFO  | main       | c.c.t.b.lifecycle.ShutdownHook       - 已注册JVM关闭钩子
2025-06-22 03:50:34.978[1750535434978] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 注册模块初始化器: A, 优先级: 10
2025-06-22 03:50:34.978[1750535434978] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 注册模块初始化器: B, 优先级: 20
2025-06-22 03:50:34.978[1750535434978] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 注册模块初始化器: C, 优先级: 30
2025-06-22 03:50:34.980[1750535434980] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 开始初始化 3 个模块，初始化顺序: A -> B -> C
2025-06-22 03:50:34.980[1750535434980] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 所有模块初始化完成
2025-06-22 03:50:34.984[1750535434984] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 注册模块初始化器: A, 优先级: 10
2025-06-22 03:50:34.984[1750535434984] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 注册模块初始化器: B, 优先级: 20
2025-06-22 03:50:34.984[1750535434984] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 注册模块初始化器: C, 优先级: 30
2025-06-22 03:50:34.985[1750535434985] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 注册模块初始化器: A, 优先级: 10
2025-06-22 03:50:34.985[1750535434985] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 注册模块初始化器: B, 优先级: 20
2025-06-22 03:50:34.985[1750535434985] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 注册模块初始化器: C, 优先级: 30
2025-06-22 03:50:34.985[1750535434985] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 注册模块初始化器: A, 优先级: 10
2025-06-22 03:50:34.985[1750535434985] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 注册模块初始化器: D, 优先级: 40
2025-06-22 03:50:34.988[1750535434988] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 注册模块初始化器: A, 优先级: 10
2025-06-22 03:50:34.989[1750535434989] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 注册模块初始化器: B, 优先级: 20
2025-06-22 03:50:34.989[1750535434989] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 注册模块初始化器: C, 优先级: 30
2025-06-22 03:50:34.989[1750535434989] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 开始初始化 3 个模块，初始化顺序: A -> B -> C
2025-06-22 03:50:34.989[1750535434989] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 所有模块初始化完成
2025-06-22 03:50:34.990[1750535434990] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 开始关闭所有模块，关闭顺序: C -> B -> A
2025-06-22 03:50:34.990[1750535434990] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 所有模块已关闭
2025-06-22 03:50:34.992[1750535434992] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 注册模块初始化器: A, 优先级: 10
2025-06-22 03:50:34.992[1750535434992] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 注册模块初始化器: B, 优先级: 20
2025-06-22 03:50:34.992[1750535434992] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 注册模块初始化器: C, 优先级: 30
2025-06-22 03:50:34.995[1750535434995] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 开始初始化 3 个模块，初始化顺序: A -> B -> C
2025-06-22 03:50:35.065[1750535435065] | INFO  | ShutdownHookThread | c.c.t.b.lifecycle.ShutdownHook       - 应用程序正在关闭...
2025-06-22 03:50:35.066[1750535435066] | INFO  | ShutdownHookThread | c.c.t.b.lifecycle.ShutdownHook       - 应用程序已关闭
2025-06-22 18:05:14.682[1750586714682] | INFO  | main       | c.c.t.bootstrap.CryptoApplication    - 正在启动虚拟货币量化交易系统...
2025-06-22 18:05:14.691[1750586714691] | INFO  | main       | c.c.t.bootstrap.CryptoApplication    - JDK版本: 21.0.2
2025-06-22 18:05:14.693[1750586714693] | INFO  | main       | c.c.t.bootstrap.CryptoApplication    - JDK供应商: Oracle Corporation
2025-06-22 18:05:14.693[1750586714693] | INFO  | main       | c.c.t.bootstrap.CryptoApplication    - JDK路径: C:\Users\<USER>\.jdks\openjdk-21.0.2
2025-06-22 18:05:18.078[1750586718078] | INFO  | main       | c.c.t.bootstrap.CryptoApplication    - Starting CryptoApplication using Java 21.0.2 with PID 9256 (D:\1_deep_bian\crypto-trading-system\crypto-bootstrap\target\classes started by 10438 in D:\1_deep_bian\crypto-trading-system\crypto-bootstrap)
2025-06-22 18:05:18.081[1750586718081] | DEBUG | main       | c.c.t.bootstrap.CryptoApplication    - Running with Spring Boot v3.2.0, Spring v6.1.1
2025-06-22 18:05:18.082[1750586718082] | INFO  | main       | c.c.t.bootstrap.CryptoApplication    - The following 1 profile is active: "dev"
2025-06-22 18:05:18.084[1750586718084] | DEBUG | main       | o.s.boot.SpringApplication           - Loading source class com.crypto.trading.bootstrap.CryptoApplication
2025-06-22 18:05:18.210[1750586718210] | DEBUG | main       | o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Refreshing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@2756c0a7
2025-06-22 18:05:18.364[1750586718364] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.context.annotation.internalConfigurationAnnotationProcessor'
2025-06-22 18:05:18.442[1750586718442] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.internalCachingMetadataReaderFactory'
2025-06-22 18:05:18.749[1750586718749] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\1_deep_bian\crypto-trading-system\crypto-bootstrap\target\classes\com\crypto\trading\bootstrap\client\StrategyServiceClient.class]
2025-06-22 18:05:18.768[1750586718768] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\1_deep_bian\crypto-trading-system\crypto-bootstrap\target\classes\com\crypto\trading\bootstrap\config\adapter\BinanceApiConfigAdapter.class]
2025-06-22 18:05:18.776[1750586718776] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\1_deep_bian\crypto-trading-system\crypto-bootstrap\target\classes\com\crypto\trading\bootstrap\config\adapter\InfluxDBConfigAdapter.class]
2025-06-22 18:05:18.818[1750586718818] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\1_deep_bian\crypto-trading-system\crypto-bootstrap\target\classes\com\crypto\trading\bootstrap\config\adapter\InfluxDBConfigBridge.class]
2025-06-22 18:05:18.826[1750586718826] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\1_deep_bian\crypto-trading-system\crypto-bootstrap\target\classes\com\crypto\trading\bootstrap\config\adapter\MarketDataConfigAdapter.class]
2025-06-22 18:05:18.832[1750586718832] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\1_deep_bian\crypto-trading-system\crypto-bootstrap\target\classes\com\crypto\trading\bootstrap\config\adapter\MarketDataConfigBridge.class]
2025-06-22 18:05:18.834[1750586718834] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\1_deep_bian\crypto-trading-system\crypto-bootstrap\target\classes\com\crypto\trading\bootstrap\config\ApiRateLimiter.class]
2025-06-22 18:05:18.841[1750586718841] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\1_deep_bian\crypto-trading-system\crypto-bootstrap\target\classes\com\crypto\trading\bootstrap\config\AppConfig.class]
2025-06-22 18:05:18.848[1750586718848] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\1_deep_bian\crypto-trading-system\crypto-bootstrap\target\classes\com\crypto\trading\bootstrap\config\BinanceApiConfig.class]
2025-06-22 18:05:18.850[1750586718850] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\1_deep_bian\crypto-trading-system\crypto-bootstrap\target\classes\com\crypto\trading\bootstrap\config\BinanceClientConfig.class]
2025-06-22 18:05:18.861[1750586718861] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\1_deep_bian\crypto-trading-system\crypto-bootstrap\target\classes\com\crypto\trading\bootstrap\config\BootstrapConfig.class]
2025-06-22 18:05:18.870[1750586718870] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\1_deep_bian\crypto-trading-system\crypto-bootstrap\target\classes\com\crypto\trading\bootstrap\config\DatabaseConfig.class]
2025-06-22 18:05:18.876[1750586718876] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\1_deep_bian\crypto-trading-system\crypto-bootstrap\target\classes\com\crypto\trading\bootstrap\config\JacksonConfig.class]
2025-06-22 18:05:18.882[1750586718882] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\1_deep_bian\crypto-trading-system\crypto-bootstrap\target\classes\com\crypto\trading\bootstrap\config\MarketDataConfig.class]
2025-06-22 18:05:18.888[1750586718888] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\1_deep_bian\crypto-trading-system\crypto-bootstrap\target\classes\com\crypto\trading\bootstrap\config\ThreadConfig.class]
2025-06-22 18:05:18.925[1750586718925] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\1_deep_bian\crypto-trading-system\crypto-bootstrap\target\classes\com\crypto\trading\bootstrap\controller\HistoricalDataController.class]
2025-06-22 18:05:18.929[1750586718929] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\1_deep_bian\crypto-trading-system\crypto-bootstrap\target\classes\com\crypto\trading\bootstrap\controller\ServiceController.class]
2025-06-22 18:05:18.935[1750586718935] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\1_deep_bian\crypto-trading-system\crypto-bootstrap\target\classes\com\crypto\trading\bootstrap\discovery\ServiceRegistry.class]
2025-06-22 18:05:18.959[1750586718959] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\1_deep_bian\crypto-trading-system\crypto-bootstrap\target\classes\com\crypto\trading\bootstrap\initializer\CommonModuleInitializer.class]
2025-06-22 18:05:18.968[1750586718968] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\1_deep_bian\crypto-trading-system\crypto-bootstrap\target\classes\com\crypto\trading\bootstrap\initializer\MarketDataModuleInitializer.class]
2025-06-22 18:05:18.971[1750586718971] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\1_deep_bian\crypto-trading-system\crypto-bootstrap\target\classes\com\crypto\trading\bootstrap\initializer\SdkModuleInitializer.class]
2025-06-22 18:05:18.974[1750586718974] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\1_deep_bian\crypto-trading-system\crypto-bootstrap\target\classes\com\crypto\trading\bootstrap\lifecycle\ApplicationLifecycle.class]
2025-06-22 18:05:18.977[1750586718977] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\1_deep_bian\crypto-trading-system\crypto-bootstrap\target\classes\com\crypto\trading\bootstrap\lifecycle\ShutdownHook.class]
2025-06-22 18:05:18.985[1750586718985] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\1_deep_bian\crypto-trading-system\crypto-bootstrap\target\classes\com\crypto\trading\bootstrap\manager\StrategyManager.class]
2025-06-22 18:05:18.986[1750586718986] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\1_deep_bian\crypto-trading-system\crypto-bootstrap\target\classes\com\crypto\trading\bootstrap\order\StartupOrderManager.class]
2025-06-22 18:05:19.127[1750586719127] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\1_deep_bian\crypto-trading-system\crypto-bootstrap\target\classes\com\crypto\trading\bootstrap\client\StrategyServiceClient.class]
2025-06-22 18:05:19.127[1750586719127] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\1_deep_bian\crypto-trading-system\crypto-bootstrap\target\classes\com\crypto\trading\bootstrap\config\adapter\BinanceApiConfigAdapter.class]
2025-06-22 18:05:19.129[1750586719129] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\1_deep_bian\crypto-trading-system\crypto-bootstrap\target\classes\com\crypto\trading\bootstrap\config\adapter\InfluxDBConfigAdapter.class]
2025-06-22 18:05:19.131[1750586719131] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\1_deep_bian\crypto-trading-system\crypto-bootstrap\target\classes\com\crypto\trading\bootstrap\config\adapter\InfluxDBConfigBridge.class]
2025-06-22 18:05:19.134[1750586719134] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\1_deep_bian\crypto-trading-system\crypto-bootstrap\target\classes\com\crypto\trading\bootstrap\config\adapter\MarketDataConfigAdapter.class]
2025-06-22 18:05:19.134[1750586719134] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\1_deep_bian\crypto-trading-system\crypto-bootstrap\target\classes\com\crypto\trading\bootstrap\config\adapter\MarketDataConfigBridge.class]
2025-06-22 18:05:19.134[1750586719134] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\1_deep_bian\crypto-trading-system\crypto-bootstrap\target\classes\com\crypto\trading\bootstrap\config\ApiRateLimiter.class]
2025-06-22 18:05:19.135[1750586719135] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\1_deep_bian\crypto-trading-system\crypto-bootstrap\target\classes\com\crypto\trading\bootstrap\config\AppConfig.class]
2025-06-22 18:05:19.135[1750586719135] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\1_deep_bian\crypto-trading-system\crypto-bootstrap\target\classes\com\crypto\trading\bootstrap\config\BinanceApiConfig.class]
2025-06-22 18:05:19.135[1750586719135] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\1_deep_bian\crypto-trading-system\crypto-bootstrap\target\classes\com\crypto\trading\bootstrap\config\BinanceClientConfig.class]
2025-06-22 18:05:19.135[1750586719135] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\1_deep_bian\crypto-trading-system\crypto-bootstrap\target\classes\com\crypto\trading\bootstrap\config\DatabaseConfig.class]
2025-06-22 18:05:19.138[1750586719138] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\1_deep_bian\crypto-trading-system\crypto-bootstrap\target\classes\com\crypto\trading\bootstrap\config\JacksonConfig.class]
2025-06-22 18:05:19.140[1750586719140] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\1_deep_bian\crypto-trading-system\crypto-bootstrap\target\classes\com\crypto\trading\bootstrap\config\MarketDataConfig.class]
2025-06-22 18:05:19.158[1750586719158] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\1_deep_bian\crypto-trading-system\crypto-bootstrap\target\classes\com\crypto\trading\bootstrap\config\ThreadConfig.class]
2025-06-22 18:05:19.160[1750586719160] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\1_deep_bian\crypto-trading-system\crypto-bootstrap\target\classes\com\crypto\trading\bootstrap\controller\HistoricalDataController.class]
2025-06-22 18:05:19.160[1750586719160] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\1_deep_bian\crypto-trading-system\crypto-bootstrap\target\classes\com\crypto\trading\bootstrap\controller\ServiceController.class]
2025-06-22 18:05:19.161[1750586719161] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\1_deep_bian\crypto-trading-system\crypto-bootstrap\target\classes\com\crypto\trading\bootstrap\CryptoApplication.class]
2025-06-22 18:05:19.167[1750586719167] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\1_deep_bian\crypto-trading-system\crypto-bootstrap\target\classes\com\crypto\trading\bootstrap\discovery\ServiceRegistry.class]
2025-06-22 18:05:19.176[1750586719176] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\1_deep_bian\crypto-trading-system\crypto-bootstrap\target\classes\com\crypto\trading\bootstrap\initializer\CommonModuleInitializer.class]
2025-06-22 18:05:19.177[1750586719177] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\1_deep_bian\crypto-trading-system\crypto-bootstrap\target\classes\com\crypto\trading\bootstrap\initializer\MarketDataModuleInitializer.class]
2025-06-22 18:05:19.184[1750586719184] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\1_deep_bian\crypto-trading-system\crypto-bootstrap\target\classes\com\crypto\trading\bootstrap\initializer\SdkModuleInitializer.class]
2025-06-22 18:05:19.192[1750586719192] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\1_deep_bian\crypto-trading-system\crypto-bootstrap\target\classes\com\crypto\trading\bootstrap\lifecycle\ApplicationLifecycle.class]
2025-06-22 18:05:19.211[1750586719211] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\1_deep_bian\crypto-trading-system\crypto-bootstrap\target\classes\com\crypto\trading\bootstrap\lifecycle\ShutdownHook.class]
2025-06-22 18:05:19.214[1750586719214] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\1_deep_bian\crypto-trading-system\crypto-bootstrap\target\classes\com\crypto\trading\bootstrap\manager\StrategyManager.class]
2025-06-22 18:05:19.217[1750586719217] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\1_deep_bian\crypto-trading-system\crypto-bootstrap\target\classes\com\crypto\trading\bootstrap\order\StartupOrderManager.class]
2025-06-22 18:05:19.267[1750586719267] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/maven_repository/com/crypto/trading/crypto-common/1.0.0-SNAPSHOT/crypto-common-1.0.0-SNAPSHOT.jar!/com/crypto/trading/common/config/ConfigAutoConfiguration.class]
2025-06-22 18:05:19.275[1750586719275] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/maven_repository/com/crypto/trading/crypto-common/1.0.0-SNAPSHOT/crypto-common-1.0.0-SNAPSHOT.jar!/com/crypto/trading/common/config/DatabaseConfig.class]
2025-06-22 18:05:19.284[1750586719284] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/maven_repository/com/crypto/trading/crypto-common/1.0.0-SNAPSHOT/crypto-common-1.0.0-SNAPSHOT.jar!/com/crypto/trading/common/config/KafkaConfig.class]
2025-06-22 18:05:19.290[1750586719290] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/maven_repository/com/crypto/trading/crypto-common/1.0.0-SNAPSHOT/crypto-common-1.0.0-SNAPSHOT.jar!/com/crypto/trading/common/config/LoggingConfig.class]
2025-06-22 18:05:19.635[1750586719635] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/maven_repository/com/crypto/trading/crypto-sdk/1.0.0-SNAPSHOT/crypto-sdk-1.0.0-SNAPSHOT.jar!/com/crypto/trading/sdk/client/CMFuturesApiClientImpl.class]
2025-06-22 18:05:19.644[1750586719644] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/maven_repository/com/crypto/trading/crypto-sdk/1.0.0-SNAPSHOT/crypto-sdk-1.0.0-SNAPSHOT.jar!/com/crypto/trading/sdk/client/UMFuturesApiClientImpl.class]
2025-06-22 18:05:19.649[1750586719649] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/maven_repository/com/crypto/trading/crypto-sdk/1.0.0-SNAPSHOT/crypto-sdk-1.0.0-SNAPSHOT.jar!/com/crypto/trading/sdk/config/ApiClientConfig.class]
2025-06-22 18:05:19.658[1750586719658] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/maven_repository/com/crypto/trading/crypto-sdk/1.0.0-SNAPSHOT/crypto-sdk-1.0.0-SNAPSHOT.jar!/com/crypto/trading/sdk/converter/JacksonJsonConverter.class]
2025-06-22 18:05:19.659[1750586719659] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/maven_repository/com/crypto/trading/crypto-sdk/1.0.0-SNAPSHOT/crypto-sdk-1.0.0-SNAPSHOT.jar!/com/crypto/trading/sdk/converter/ModelConverterImpl.class]
2025-06-22 18:05:19.680[1750586719680] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/maven_repository/com/crypto/trading/crypto-sdk/1.0.0-SNAPSHOT/crypto-sdk-1.0.0-SNAPSHOT.jar!/com/crypto/trading/sdk/limiter/BinanceApiInterceptor.class]
2025-06-22 18:05:19.683[1750586719683] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/maven_repository/com/crypto/trading/crypto-sdk/1.0.0-SNAPSHOT/crypto-sdk-1.0.0-SNAPSHOT.jar!/com/crypto/trading/sdk/limiter/BinanceRateLimiter.class]
2025-06-22 18:05:19.683[1750586719683] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/maven_repository/com/crypto/trading/crypto-sdk/1.0.0-SNAPSHOT/crypto-sdk-1.0.0-SNAPSHOT.jar!/com/crypto/trading/sdk/limiter/RateLimitMonitor.class]
2025-06-22 18:05:19.687[1750586719687] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/maven_repository/com/crypto/trading/crypto-sdk/1.0.0-SNAPSHOT/crypto-sdk-1.0.0-SNAPSHOT.jar!/com/crypto/trading/sdk/limiter/ResponseHeaderRateLimitUpdater.class]
2025-06-22 18:05:19.690[1750586719690] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/maven_repository/com/crypto/trading/crypto-sdk/1.0.0-SNAPSHOT/crypto-sdk-1.0.0-SNAPSHOT.jar!/com/crypto/trading/sdk/limiter/RetryWithBackoffHandler.class]
2025-06-22 18:05:19.699[1750586719699] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/maven_repository/com/crypto/trading/crypto-sdk/1.0.0-SNAPSHOT/crypto-sdk-1.0.0-SNAPSHOT.jar!/com/crypto/trading/sdk/response/ResponseHandlerImpl.class]
2025-06-22 18:05:19.708[1750586719708] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/maven_repository/com/crypto/trading/crypto-sdk/1.0.0-SNAPSHOT/crypto-sdk-1.0.0-SNAPSHOT.jar!/com/crypto/trading/sdk/websocket/BinanceWebSocketClientImpl.class]
2025-06-22 18:05:19.722[1750586719722] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/maven_repository/com/crypto/trading/crypto-sdk/1.0.0-SNAPSHOT/crypto-sdk-1.0.0-SNAPSHOT.jar!/com/crypto/trading/sdk/websocket/WebSocketConnectionPool.class]
2025-06-22 18:05:19.735[1750586719735] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/maven_repository/com/crypto/trading/crypto-market-data/1.0.0-SNAPSHOT/crypto-market-data-1.0.0-SNAPSHOT.jar!/com/crypto/trading/market/config/InfluxDBConfig.class]
2025-06-22 18:05:19.740[1750586719740] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/maven_repository/com/crypto/trading/crypto-market-data/1.0.0-SNAPSHOT/crypto-market-data-1.0.0-SNAPSHOT.jar!/com/crypto/trading/market/config/KafkaConsumerConfig.class]
2025-06-22 18:05:19.750[1750586719750] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/maven_repository/com/crypto/trading/crypto-market-data/1.0.0-SNAPSHOT/crypto-market-data-1.0.0-SNAPSHOT.jar!/com/crypto/trading/market/config/KafkaProducerConfig.class]
2025-06-22 18:05:19.759[1750586719759] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/maven_repository/com/crypto/trading/crypto-market-data/1.0.0-SNAPSHOT/crypto-market-data-1.0.0-SNAPSHOT.jar!/com/crypto/trading/market/config/ResumableDownloadConfig.class]
2025-06-22 18:05:19.767[1750586719767] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/maven_repository/com/crypto/trading/crypto-market-data/1.0.0-SNAPSHOT/crypto-market-data-1.0.0-SNAPSHOT.jar!/com/crypto/trading/market/config/ThreadPoolConfig.class]
2025-06-22 18:05:19.768[1750586719768] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/maven_repository/com/crypto/trading/crypto-market-data/1.0.0-SNAPSHOT/crypto-market-data-1.0.0-SNAPSHOT.jar!/com/crypto/trading/market/config/WebSocketConfig.class]
2025-06-22 18:05:19.769[1750586719769] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/maven_repository/com/crypto/trading/crypto-market-data/1.0.0-SNAPSHOT/crypto-market-data-1.0.0-SNAPSHOT.jar!/com/crypto/trading/market/converter/MarketDataConverter.class]
2025-06-22 18:05:19.773[1750586719773] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/maven_repository/com/crypto/trading/crypto-market-data/1.0.0-SNAPSHOT/crypto-market-data-1.0.0-SNAPSHOT.jar!/com/crypto/trading/market/listener/DepthDataListener.class]
2025-06-22 18:05:19.775[1750586719775] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/maven_repository/com/crypto/trading/crypto-market-data/1.0.0-SNAPSHOT/crypto-market-data-1.0.0-SNAPSHOT.jar!/com/crypto/trading/market/listener/KlineDataListener.class]
2025-06-22 18:05:19.782[1750586719782] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/maven_repository/com/crypto/trading/crypto-market-data/1.0.0-SNAPSHOT/crypto-market-data-1.0.0-SNAPSHOT.jar!/com/crypto/trading/market/listener/TradeDataListener.class]
2025-06-22 18:05:19.788[1750586719788] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/maven_repository/com/crypto/trading/crypto-market-data/1.0.0-SNAPSHOT/crypto-market-data-1.0.0-SNAPSHOT.jar!/com/crypto/trading/market/listener/WebSocketStartupListener.class]
2025-06-22 18:05:19.791[1750586719791] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/maven_repository/com/crypto/trading/crypto-market-data/1.0.0-SNAPSHOT/crypto-market-data-1.0.0-SNAPSHOT.jar!/com/crypto/trading/market/manager/WebSocketManager.class]
2025-06-22 18:05:19.792[1750586719792] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/maven_repository/com/crypto/trading/crypto-market-data/1.0.0-SNAPSHOT/crypto-market-data-1.0.0-SNAPSHOT.jar!/com/crypto/trading/market/processor/DepthDataProcessor.class]
2025-06-22 18:05:19.793[1750586719793] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/maven_repository/com/crypto/trading/crypto-market-data/1.0.0-SNAPSHOT/crypto-market-data-1.0.0-SNAPSHOT.jar!/com/crypto/trading/market/processor/KlineDataProcessor.class]
2025-06-22 18:05:19.795[1750586719795] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/maven_repository/com/crypto/trading/crypto-market-data/1.0.0-SNAPSHOT/crypto-market-data-1.0.0-SNAPSHOT.jar!/com/crypto/trading/market/processor/TradeDataProcessor.class]
2025-06-22 18:05:19.807[1750586719807] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/maven_repository/com/crypto/trading/crypto-market-data/1.0.0-SNAPSHOT/crypto-market-data-1.0.0-SNAPSHOT.jar!/com/crypto/trading/market/producer/KafkaMessageProducer.class]
2025-06-22 18:05:19.810[1750586719810] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/maven_repository/com/crypto/trading/crypto-market-data/1.0.0-SNAPSHOT/crypto-market-data-1.0.0-SNAPSHOT.jar!/com/crypto/trading/market/repository/InfluxDBRepository.class]
2025-06-22 18:05:19.815[1750586719815] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/maven_repository/com/crypto/trading/crypto-market-data/1.0.0-SNAPSHOT/crypto-market-data-1.0.0-SNAPSHOT.jar!/com/crypto/trading/market/repository/MySQLMarketDataRepository.class]
2025-06-22 18:05:19.819[1750586719819] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/maven_repository/com/crypto/trading/crypto-market-data/1.0.0-SNAPSHOT/crypto-market-data-1.0.0-SNAPSHOT.jar!/com/crypto/trading/market/service/impl/DownloadTaskTracker.class]
2025-06-22 18:05:19.825[1750586719825] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/maven_repository/com/crypto/trading/crypto-market-data/1.0.0-SNAPSHOT/crypto-market-data-1.0.0-SNAPSHOT.jar!/com/crypto/trading/market/service/impl/HistoricalDataServiceImpl.class]
2025-06-22 18:05:19.834[1750586719834] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/maven_repository/com/crypto/trading/crypto-market-data/1.0.0-SNAPSHOT/crypto-market-data-1.0.0-SNAPSHOT.jar!/com/crypto/trading/market/service/impl/MarketDataServiceImpl.class]
2025-06-22 18:05:19.835[1750586719835] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/maven_repository/com/crypto/trading/crypto-market-data/1.0.0-SNAPSHOT/crypto-market-data-1.0.0-SNAPSHOT.jar!/com/crypto/trading/market/service/impl/optimizer/SmartChunkDownloader.class]
2025-06-22 18:05:19.837[1750586719837] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/maven_repository/com/crypto/trading/crypto-market-data/1.0.0-SNAPSHOT/crypto-market-data-1.0.0-SNAPSHOT.jar!/com/crypto/trading/market/service/impl/optimizer/SmartDownloadOptimizer.class]
2025-06-22 18:05:19.838[1750586719838] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/maven_repository/com/crypto/trading/crypto-market-data/1.0.0-SNAPSHOT/crypto-market-data-1.0.0-SNAPSHOT.jar!/com/crypto/trading/market/service/impl/resumable/ResumableDownloadEngine.class]
2025-06-22 18:05:19.839[1750586719839] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/maven_repository/com/crypto/trading/crypto-market-data/1.0.0-SNAPSHOT/crypto-market-data-1.0.0-SNAPSHOT.jar!/com/crypto/trading/market/service/RetentionPolicyService.class]
2025-06-22 18:05:19.841[1750586719841] | DEBUG | main       | o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/maven_repository/com/crypto/trading/crypto-market-data/1.0.0-SNAPSHOT/crypto-market-data-1.0.0-SNAPSHOT.jar!/com/crypto/trading/market/websocket/WebSocketHealthChecker.class]
2025-06-22 18:05:19.891[1750586719891] | INFO  | main       | c.c.t.c.c.YamlPropertySourceFactory  - 加载YAML配置文件: application-common.yml
2025-06-22 18:05:19.935[1750586719935] | DEBUG | main       | o.s.b.f.c.YamlPropertiesFactoryBean  - Loading from YAML: class path resource [application-common.yml]
2025-06-22 18:05:19.944[1750586719944] | DEBUG | main       | o.s.b.f.c.YamlPropertiesFactoryBean  - Merging document (no matchers set): {database={pool={initial-size=10, min-idle=10, max-active=100, max-wait=60000, time-between-eviction-runs-millis=60000, min-evictable-idle-time-millis=300000, validation-query=SELECT 1, test-while-idle=true, test-on-borrow=false, test-on-return=false}}, kafka={retry={max-attempts=3, backoff={initial-interval=1000, multiplier=2.0, max-interval=10000}}, schema-registry-url=http://localhost:8081, avro-serialization-enabled=true, producer={batch-size=65536, linger-ms=20, compression-type=lz4, acks=1, buffer-memory=134217728}, ssl={enabled=false, key-password=null, keystore-location=null, keystore-password=null, truststore-location=null, truststore-password=null}}, logging={config={path=classpath:logback-common.xml}, appender={console=true, file=true, rolling-policy={max-history=7, max-file-size=100MB}}}}
2025-06-22 18:05:19.957[1750586719957] | DEBUG | main       | o.s.b.f.c.YamlPropertiesFactoryBean  - Loaded 1 document from YAML resource: class path resource [application-common.yml]
2025-06-22 18:05:23.618[1750586723618] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Overriding bean definition for bean 'binanceApiConfigBean' with a different definition: replacing [Root bean: class [null]; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=true; factoryBeanName=binanceApiConfigAdapter; factoryMethodName=sdkBinanceApiConfig; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/crypto/trading/bootstrap/config/adapter/BinanceApiConfigAdapter.class]] with [Root bean: class [null]; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=true; factoryBeanName=binanceApiConfig; factoryMethodName=binanceApiConfigBean; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/crypto/trading/bootstrap/config/BinanceApiConfig.class]]
2025-06-22 18:05:23.638[1750586723638] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Overriding bean definition for bean 'objectMapper' with a different definition: replacing [Root bean: class [null]; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=kafkaConsumerConfig; factoryMethodName=objectMapper; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/crypto/trading/market/config/KafkaConsumerConfig.class]] with [Root bean: class [null]; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=true; factoryBeanName=jacksonConfig; factoryMethodName=objectMapper; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/crypto/trading/bootstrap/config/JacksonConfig.class]]
2025-06-22 18:05:23.641[1750586723641] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Overriding bean definition for bean 'platformThreadPoolTaskExecutor' with a different definition: replacing [Root bean: class [null]; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=threadPoolConfig; factoryMethodName=platformThreadPoolTaskExecutor; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/crypto/trading/market/config/ThreadPoolConfig.class]] with [Root bean: class [null]; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=threadConfig; factoryMethodName=platformThreadPoolTaskExecutor; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/crypto/trading/bootstrap/config/ThreadConfig.class]]
2025-06-22 18:05:24.151[1750586724151] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.AutoConfigurationPackages'
2025-06-22 18:05:24.193[1750586724193] | DEBUG | main       | o.s.b.a.AutoConfigurationPackages    - @EnableAutoConfiguration was declared on a class in the package 'com.crypto.trading.bootstrap'. Automatic @Repository and @Entity scanning is enabled.
2025-06-22 18:05:24.200[1750586724200] | DEBUG | main       | c.b.m.a.MybatisPlusAutoConfiguration - Searching for mappers annotated with @Mapper
2025-06-22 18:05:24.201[1750586724201] | DEBUG | main       | c.b.m.a.MybatisPlusAutoConfiguration - Using auto-configuration base package 'com.crypto.trading.bootstrap'
2025-06-22 18:05:25.669[1750586725669] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.mybatis.spring.mapper.MapperScannerConfigurer'
2025-06-22 18:05:25.685[1750586725685] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'propertySourcesPlaceholderConfigurer'
2025-06-22 18:05:25.710[1750586725710] | DEBUG | main       | o.apache.ibatis.logging.LogFactory   - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
2025-06-22 18:05:25.748[1750586725748] | WARN  | main       | o.m.s.mapper.ClassPathMapperScanner  - No MyBatis mapper was found in '[com.crypto.trading.bootstrap]' package. Please check your configuration.
2025-06-22 18:05:26.510[1750586726510] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.sql.init.dependency.DatabaseInitializationDependencyConfigurer$DependsOnDatabaseInitializationPostProcessor'
2025-06-22 18:05:26.769[1750586726769] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.context.event.internalEventListenerProcessor'
2025-06-22 18:05:26.786[1750586726786] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'preserveErrorControllerTargetClassPostProcessor'
2025-06-22 18:05:26.799[1750586726799] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.context.event.internalEventListenerFactory'
2025-06-22 18:05:26.801[1750586726801] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.transaction.config.internalTransactionalEventListenerFactory'
2025-06-22 18:05:26.811[1750586726811] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.context.annotation.internalAutowiredAnnotationProcessor'
2025-06-22 18:05:26.824[1750586726824] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.context.annotation.internalCommonAnnotationProcessor'
2025-06-22 18:05:26.843[1750586726843] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.context.properties.ConfigurationPropertiesBindingPostProcessor'
2025-06-22 18:05:26.847[1750586726847] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.context.internalConfigurationPropertiesBinder'
2025-06-22 18:05:26.860[1750586726860] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'jdbcConnectionDetailsHikariBeanPostProcessor'
2025-06-22 18:05:26.875[1750586726875] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.context.annotation.internalAsyncAnnotationProcessor'
2025-06-22 18:05:26.884[1750586726884] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.scheduling.annotation.ProxyAsyncConfiguration'
2025-06-22 18:05:26.940[1750586726940] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.context.annotation.internalScheduledAnnotationProcessor'
2025-06-22 18:05:26.941[1750586726941] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.scheduling.annotation.SchedulingConfiguration'
2025-06-22 18:05:26.950[1750586726950] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.aop.config.internalAutoProxyCreator'
2025-06-22 18:05:27.036[1750586727036] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'persistenceExceptionTranslationPostProcessor'
2025-06-22 18:05:27.043[1750586727043] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'persistenceExceptionTranslationPostProcessor' via factory method to bean named 'environment'
2025-06-22 18:05:27.050[1750586727050] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.kafka.config.internalKafkaListenerAnnotationProcessor'
2025-06-22 18:05:27.082[1750586727082] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'webServerFactoryCustomizerBeanPostProcessor'
2025-06-22 18:05:27.084[1750586727084] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'errorPageRegistrarBeanPostProcessor'
2025-06-22 18:05:27.085[1750586727085] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'healthEndpointGroupsBeanPostProcessor'
2025-06-22 18:05:27.089[1750586727089] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.retry.annotation.RetryConfiguration'
2025-06-22 18:05:27.125[1750586727125] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.transaction.config.internalTransactionAdvisor'
2025-06-22 18:05:27.125[1750586727125] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration'
2025-06-22 18:05:27.168[1750586727168] | DEBUG | main       | o.s.a.a.a.ReflectiveAspectJAdvisorFactory - Found AspectJ method: public java.lang.Object io.micrometer.core.aop.CountedAspect.interceptAndRecord(org.aspectj.lang.ProceedingJoinPoint,io.micrometer.core.annotation.Counted) throws java.lang.Throwable
2025-06-22 18:05:27.176[1750586727176] | DEBUG | main       | o.s.a.a.a.ReflectiveAspectJAdvisorFactory - Found AspectJ method: public java.lang.Object io.micrometer.core.aop.TimedAspect.timedClass(org.aspectj.lang.ProceedingJoinPoint) throws java.lang.Throwable
2025-06-22 18:05:27.177[1750586727177] | DEBUG | main       | o.s.a.a.a.ReflectiveAspectJAdvisorFactory - Found AspectJ method: public java.lang.Object io.micrometer.core.aop.TimedAspect.timedMethod(org.aspectj.lang.ProceedingJoinPoint) throws java.lang.Throwable
2025-06-22 18:05:27.182[1750586727182] | DEBUG | main       | o.s.a.a.a.ReflectiveAspectJAdvisorFactory - Found AspectJ method: public java.lang.Object io.micrometer.observation.aop.ObservedAspect.observeClass(org.aspectj.lang.ProceedingJoinPoint) throws java.lang.Throwable
2025-06-22 18:05:27.183[1750586727183] | DEBUG | main       | o.s.a.a.a.ReflectiveAspectJAdvisorFactory - Found AspectJ method: public java.lang.Object io.micrometer.observation.aop.ObservedAspect.observeMethod(org.aspectj.lang.ProceedingJoinPoint) throws java.lang.Throwable
2025-06-22 18:05:27.968[1750586727968] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'transactionAttributeSource'
2025-06-22 18:05:28.010[1750586728010] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'transactionInterceptor'
2025-06-22 18:05:28.016[1750586728016] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'transactionInterceptor' via factory method to bean named 'transactionAttributeSource'
2025-06-22 18:05:28.739[1750586728739] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.transaction.config.internalTransactionAdvisor' via factory method to bean named 'transactionAttributeSource'
2025-06-22 18:05:28.750[1750586728750] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.transaction.config.internalTransactionAdvisor' via factory method to bean named 'transactionInterceptor'
2025-06-22 18:05:28.785[1750586728785] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'meterRegistryPostProcessor'
2025-06-22 18:05:28.789[1750586728789] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'meterRegistryPostProcessor' via factory method to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@2756c0a7'
2025-06-22 18:05:28.852[1750586728852] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'observationRegistryPostProcessor'
2025-06-22 18:05:28.898[1750586728898] | DEBUG | main       | o.s.u.c.s.UiApplicationContextUtils  - Unable to locate ThemeSource with name 'themeSource': using default [org.springframework.ui.context.support.ResourceBundleThemeSource@19002b34]
2025-06-22 18:05:28.949[1750586728949] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'tomcatServletWebServerFactory'
2025-06-22 18:05:28.950[1750586728950] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.ServletWebServerFactoryConfiguration$EmbeddedTomcat'
2025-06-22 18:05:29.148[1750586729148] | DEBUG | main       | o.a.c.core.AprLifecycleListener      - The Apache Tomcat Native library could not be found using names [tcnative-2, libtcnative-2, tcnative-1, libtcnative-1] on the java.library.path [C:\Users\<USER>\.jdks\openjdk-21.0.2\bin;C:\Windows\Sun\Java\bin;C:\Windows\system32;C:\Windows;d:\Software\cursor\resources\app\bin;d:\Software\cursor\resources\app\bin;d:\Software\cursor\resources\app\bin;d:\Software\cursor\resources\app\bin;d:\Software\cursor\resources\app\bin;d:\Software\cursor\resources\app\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.6\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.6\libnvvp;D:\Software\vm\bin\;D:\jdk\flutter_windows_3.22.2-stable\flutter\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\MinGW\bin;D:\Software\apache-maven-3.6.0\bin;D:\Software\Git\cmd;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;D:\jdk\Android\Sdk\platform-tools;D:\jdk\Android\Sdk\build-tools;C:\Program Files\NVIDIA Corporation\NVIDIA app\NvDLISR;C:\Program Files\NVIDIA Corporation\Nsight Compute 2024.3.2\;C:\Program Files\CursorModifier;C:\Program Files\Redis\;C:\Program Files\MySQL\MySQL Server 9.0\bin;C:\Users\<USER>\;C:\Program Files\Docker\Docker\resources\bin;C:\Program Files\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\;C:\Program Files\Google\Chrome\Application;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.6\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.6\libnvvp;D:\Software\vm\bin\;D:\jdk\flutter_windows_3.22.2-stable\flutter\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;D:\Software\MySQL Server 8.0\bin;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\MinGW\bin;D:\Software\apache-maven-3.6.0\bin;C:\Program Files\Docker\Docker\resources\bin;D:\Software\Git\cmd;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;C:\ProgramData\miniconda3;C:\ProgramData\miniconda3\Scripts;D:\Software\dartsdk-windows-x64-release\dart-sdk\bin;D:\jdk\Android\Sdk\platform-tools;D:\jdk\Andro;C:\Users\<USER>\.jdks\openjdk-21.0.2\bin;D:\Software\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand;.]. The errors reported were [Can't load library: D:\1_deep_bian\crypto-trading-system\crypto-bootstrap\bin\tcnative-2.dll, Can't load library: D:\1_deep_bian\crypto-trading-system\crypto-bootstrap\bin\libtcnative-2.dll, Can't load library: D:\1_deep_bian\crypto-trading-system\crypto-bootstrap\bin\tcnative-1.dll, Can't load library: D:\1_deep_bian\crypto-trading-system\crypto-bootstrap\bin\libtcnative-1.dll, no tcnative-2 in java.library.path: C:\Users\<USER>\.jdks\openjdk-21.0.2\bin;C:\Windows\Sun\Java\bin;C:\Windows\system32;C:\Windows;d:\Software\cursor\resources\app\bin;d:\Software\cursor\resources\app\bin;d:\Software\cursor\resources\app\bin;d:\Software\cursor\resources\app\bin;d:\Software\cursor\resources\app\bin;d:\Software\cursor\resources\app\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.6\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.6\libnvvp;D:\Software\vm\bin\;D:\jdk\flutter_windows_3.22.2-stable\flutter\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\MinGW\bin;D:\Software\apache-maven-3.6.0\bin;D:\Software\Git\cmd;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;D:\jdk\Android\Sdk\platform-tools;D:\jdk\Android\Sdk\build-tools;C:\Program Files\NVIDIA Corporation\NVIDIA app\NvDLISR;C:\Program Files\NVIDIA Corporation\Nsight Compute 2024.3.2\;C:\Program Files\CursorModifier;C:\Program Files\Redis\;C:\Program Files\MySQL\MySQL Server 9.0\bin;C:\Users\<USER>\;C:\Program Files\Docker\Docker\resources\bin;C:\Program Files\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\;C:\Program Files\Google\Chrome\Application;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.6\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.6\libnvvp;D:\Software\vm\bin\;D:\jdk\flutter_windows_3.22.2-stable\flutter\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;D:\Software\MySQL Server 8.0\bin;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\MinGW\bin;D:\Software\apache-maven-3.6.0\bin;C:\Program Files\Docker\Docker\resources\bin;D:\Software\Git\cmd;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;C:\ProgramData\miniconda3;C:\ProgramData\miniconda3\Scripts;D:\Software\dartsdk-windows-x64-release\dart-sdk\bin;D:\jdk\Android\Sdk\platform-tools;D:\jdk\Andro;C:\Users\<USER>\.jdks\openjdk-21.0.2\bin;D:\Software\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand;., no libtcnative-2 in java.library.path: C:\Users\<USER>\.jdks\openjdk-21.0.2\bin;C:\Windows\Sun\Java\bin;C:\Windows\system32;C:\Windows;d:\Software\cursor\resources\app\bin;d:\Software\cursor\resources\app\bin;d:\Software\cursor\resources\app\bin;d:\Software\cursor\resources\app\bin;d:\Software\cursor\resources\app\bin;d:\Software\cursor\resources\app\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.6\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.6\libnvvp;D:\Software\vm\bin\;D:\jdk\flutter_windows_3.22.2-stable\flutter\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\MinGW\bin;D:\Software\apache-maven-3.6.0\bin;D:\Software\Git\cmd;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;D:\jdk\Android\Sdk\platform-tools;D:\jdk\Android\Sdk\build-tools;C:\Program Files\NVIDIA Corporation\NVIDIA app\NvDLISR;C:\Program Files\NVIDIA Corporation\Nsight Compute 2024.3.2\;C:\Program Files\CursorModifier;C:\Program Files\Redis\;C:\Program Files\MySQL\MySQL Server 9.0\bin;C:\Users\<USER>\;C:\Program Files\Docker\Docker\resources\bin;C:\Program Files\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\;C:\Program Files\Google\Chrome\Application;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.6\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.6\libnvvp;D:\Software\vm\bin\;D:\jdk\flutter_windows_3.22.2-stable\flutter\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;D:\Software\MySQL Server 8.0\bin;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\MinGW\bin;D:\Software\apache-maven-3.6.0\bin;C:\Program Files\Docker\Docker\resources\bin;D:\Software\Git\cmd;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;C:\ProgramData\miniconda3;C:\ProgramData\miniconda3\Scripts;D:\Software\dartsdk-windows-x64-release\dart-sdk\bin;D:\jdk\Android\Sdk\platform-tools;D:\jdk\Andro;C:\Users\<USER>\.jdks\openjdk-21.0.2\bin;D:\Software\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand;., no tcnative-1 in java.library.path: C:\Users\<USER>\.jdks\openjdk-21.0.2\bin;C:\Windows\Sun\Java\bin;C:\Windows\system32;C:\Windows;d:\Software\cursor\resources\app\bin;d:\Software\cursor\resources\app\bin;d:\Software\cursor\resources\app\bin;d:\Software\cursor\resources\app\bin;d:\Software\cursor\resources\app\bin;d:\Software\cursor\resources\app\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.6\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.6\libnvvp;D:\Software\vm\bin\;D:\jdk\flutter_windows_3.22.2-stable\flutter\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\MinGW\bin;D:\Software\apache-maven-3.6.0\bin;D:\Software\Git\cmd;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;D:\jdk\Android\Sdk\platform-tools;D:\jdk\Android\Sdk\build-tools;C:\Program Files\NVIDIA Corporation\NVIDIA app\NvDLISR;C:\Program Files\NVIDIA Corporation\Nsight Compute 2024.3.2\;C:\Program Files\CursorModifier;C:\Program Files\Redis\;C:\Program Files\MySQL\MySQL Server 9.0\bin;C:\Users\<USER>\;C:\Program Files\Docker\Docker\resources\bin;C:\Program Files\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\;C:\Program Files\Google\Chrome\Application;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.6\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.6\libnvvp;D:\Software\vm\bin\;D:\jdk\flutter_windows_3.22.2-stable\flutter\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;D:\Software\MySQL Server 8.0\bin;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\MinGW\bin;D:\Software\apache-maven-3.6.0\bin;C:\Program Files\Docker\Docker\resources\bin;D:\Software\Git\cmd;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;C:\ProgramData\miniconda3;C:\ProgramData\miniconda3\Scripts;D:\Software\dartsdk-windows-x64-release\dart-sdk\bin;D:\jdk\Android\Sdk\platform-tools;D:\jdk\Andro;C:\Users\<USER>\.jdks\openjdk-21.0.2\bin;D:\Software\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand;., no libtcnative-1 in java.library.path: C:\Users\<USER>\.jdks\openjdk-21.0.2\bin;C:\Windows\Sun\Java\bin;C:\Windows\system32;C:\Windows;d:\Software\cursor\resources\app\bin;d:\Software\cursor\resources\app\bin;d:\Software\cursor\resources\app\bin;d:\Software\cursor\resources\app\bin;d:\Software\cursor\resources\app\bin;d:\Software\cursor\resources\app\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.6\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.6\libnvvp;D:\Software\vm\bin\;D:\jdk\flutter_windows_3.22.2-stable\flutter\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\MinGW\bin;D:\Software\apache-maven-3.6.0\bin;D:\Software\Git\cmd;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;D:\jdk\Android\Sdk\platform-tools;D:\jdk\Android\Sdk\build-tools;C:\Program Files\NVIDIA Corporation\NVIDIA app\NvDLISR;C:\Program Files\NVIDIA Corporation\Nsight Compute 2024.3.2\;C:\Program Files\CursorModifier;C:\Program Files\Redis\;C:\Program Files\MySQL\MySQL Server 9.0\bin;C:\Users\<USER>\;C:\Program Files\Docker\Docker\resources\bin;C:\Program Files\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\;C:\Program Files\Google\Chrome\Application;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.6\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.6\libnvvp;D:\Software\vm\bin\;D:\jdk\flutter_windows_3.22.2-stable\flutter\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;D:\Software\MySQL Server 8.0\bin;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\MinGW\bin;D:\Software\apache-maven-3.6.0\bin;C:\Program Files\Docker\Docker\resources\bin;D:\Software\Git\cmd;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;C:\ProgramData\miniconda3;C:\ProgramData\miniconda3\Scripts;D:\Software\dartsdk-windows-x64-release\dart-sdk\bin;D:\jdk\Android\Sdk\platform-tools;D:\jdk\Andro;C:\Users\<USER>\.jdks\openjdk-21.0.2\bin;D:\Software\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand;.]
org.apache.tomcat.jni.LibraryNotFoundError: Can't load library: D:\1_deep_bian\crypto-trading-system\crypto-bootstrap\bin\tcnative-2.dll, Can't load library: D:\1_deep_bian\crypto-trading-system\crypto-bootstrap\bin\libtcnative-2.dll, Can't load library: D:\1_deep_bian\crypto-trading-system\crypto-bootstrap\bin\tcnative-1.dll, Can't load library: D:\1_deep_bian\crypto-trading-system\crypto-bootstrap\bin\libtcnative-1.dll, no tcnative-2 in java.library.path: C:\Users\<USER>\.jdks\openjdk-21.0.2\bin;C:\Windows\Sun\Java\bin;C:\Windows\system32;C:\Windows;d:\Software\cursor\resources\app\bin;d:\Software\cursor\resources\app\bin;d:\Software\cursor\resources\app\bin;d:\Software\cursor\resources\app\bin;d:\Software\cursor\resources\app\bin;d:\Software\cursor\resources\app\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.6\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.6\libnvvp;D:\Software\vm\bin\;D:\jdk\flutter_windows_3.22.2-stable\flutter\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\MinGW\bin;D:\Software\apache-maven-3.6.0\bin;D:\Software\Git\cmd;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;D:\jdk\Android\Sdk\platform-tools;D:\jdk\Android\Sdk\build-tools;C:\Program Files\NVIDIA Corporation\NVIDIA app\NvDLISR;C:\Program Files\NVIDIA Corporation\Nsight Compute 2024.3.2\;C:\Program Files\CursorModifier;C:\Program Files\Redis\;C:\Program Files\MySQL\MySQL Server 9.0\bin;C:\Users\<USER>\;C:\Program Files\Docker\Docker\resources\bin;C:\Program Files\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\;C:\Program Files\Google\Chrome\Application;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.6\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.6\libnvvp;D:\Software\vm\bin\;D:\jdk\flutter_windows_3.22.2-stable\flutter\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;D:\Software\MySQL Server 8.0\bin;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\MinGW\bin;D:\Software\apache-maven-3.6.0\bin;C:\Program Files\Docker\Docker\resources\bin;D:\Software\Git\cmd;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;C:\ProgramData\miniconda3;C:\ProgramData\miniconda3\Scripts;D:\Software\dartsdk-windows-x64-release\dart-sdk\bin;D:\jdk\Android\Sdk\platform-tools;D:\jdk\Andro;C:\Users\<USER>\.jdks\openjdk-21.0.2\bin;D:\Software\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand;., no libtcnative-2 in java.library.path: C:\Users\<USER>\.jdks\openjdk-21.0.2\bin;C:\Windows\Sun\Java\bin;C:\Windows\system32;C:\Windows;d:\Software\cursor\resources\app\bin;d:\Software\cursor\resources\app\bin;d:\Software\cursor\resources\app\bin;d:\Software\cursor\resources\app\bin;d:\Software\cursor\resources\app\bin;d:\Software\cursor\resources\app\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.6\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.6\libnvvp;D:\Software\vm\bin\;D:\jdk\flutter_windows_3.22.2-stable\flutter\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\MinGW\bin;D:\Software\apache-maven-3.6.0\bin;D:\Software\Git\cmd;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;D:\jdk\Android\Sdk\platform-tools;D:\jdk\Android\Sdk\build-tools;C:\Program Files\NVIDIA Corporation\NVIDIA app\NvDLISR;C:\Program Files\NVIDIA Corporation\Nsight Compute 2024.3.2\;C:\Program Files\CursorModifier;C:\Program Files\Redis\;C:\Program Files\MySQL\MySQL Server 9.0\bin;C:\Users\<USER>\;C:\Program Files\Docker\Docker\resources\bin;C:\Program Files\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\;C:\Program Files\Google\Chrome\Application;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.6\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.6\libnvvp;D:\Software\vm\bin\;D:\jdk\flutter_windows_3.22.2-stable\flutter\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;D:\Software\MySQL Server 8.0\bin;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\MinGW\bin;D:\Software\apache-maven-3.6.0\bin;C:\Program Files\Docker\Docker\resources\bin;D:\Software\Git\cmd;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;C:\ProgramData\miniconda3;C:\ProgramData\miniconda3\Scripts;D:\Software\dartsdk-windows-x64-release\dart-sdk\bin;D:\jdk\Android\Sdk\platform-tools;D:\jdk\Andro;C:\Users\<USER>\.jdks\openjdk-21.0.2\bin;D:\Software\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand;., no tcnative-1 in java.library.path: C:\Users\<USER>\.jdks\openjdk-21.0.2\bin;C:\Windows\Sun\Java\bin;C:\Windows\system32;C:\Windows;d:\Software\cursor\resources\app\bin;d:\Software\cursor\resources\app\bin;d:\Software\cursor\resources\app\bin;d:\Software\cursor\resources\app\bin;d:\Software\cursor\resources\app\bin;d:\Software\cursor\resources\app\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.6\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.6\libnvvp;D:\Software\vm\bin\;D:\jdk\flutter_windows_3.22.2-stable\flutter\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\MinGW\bin;D:\Software\apache-maven-3.6.0\bin;D:\Software\Git\cmd;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;D:\jdk\Android\Sdk\platform-tools;D:\jdk\Android\Sdk\build-tools;C:\Program Files\NVIDIA Corporation\NVIDIA app\NvDLISR;C:\Program Files\NVIDIA Corporation\Nsight Compute 2024.3.2\;C:\Program Files\CursorModifier;C:\Program Files\Redis\;C:\Program Files\MySQL\MySQL Server 9.0\bin;C:\Users\<USER>\;C:\Program Files\Docker\Docker\resources\bin;C:\Program Files\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\;C:\Program Files\Google\Chrome\Application;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.6\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.6\libnvvp;D:\Software\vm\bin\;D:\jdk\flutter_windows_3.22.2-stable\flutter\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;D:\Software\MySQL Server 8.0\bin;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\MinGW\bin;D:\Software\apache-maven-3.6.0\bin;C:\Program Files\Docker\Docker\resources\bin;D:\Software\Git\cmd;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;C:\ProgramData\miniconda3;C:\ProgramData\miniconda3\Scripts;D:\Software\dartsdk-windows-x64-release\dart-sdk\bin;D:\jdk\Android\Sdk\platform-tools;D:\jdk\Andro;C:\Users\<USER>\.jdks\openjdk-21.0.2\bin;D:\Software\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand;., no libtcnative-1 in java.library.path: C:\Users\<USER>\.jdks\openjdk-21.0.2\bin;C:\Windows\Sun\Java\bin;C:\Windows\system32;C:\Windows;d:\Software\cursor\resources\app\bin;d:\Software\cursor\resources\app\bin;d:\Software\cursor\resources\app\bin;d:\Software\cursor\resources\app\bin;d:\Software\cursor\resources\app\bin;d:\Software\cursor\resources\app\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.6\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.6\libnvvp;D:\Software\vm\bin\;D:\jdk\flutter_windows_3.22.2-stable\flutter\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\MinGW\bin;D:\Software\apache-maven-3.6.0\bin;D:\Software\Git\cmd;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;D:\jdk\Android\Sdk\platform-tools;D:\jdk\Android\Sdk\build-tools;C:\Program Files\NVIDIA Corporation\NVIDIA app\NvDLISR;C:\Program Files\NVIDIA Corporation\Nsight Compute 2024.3.2\;C:\Program Files\CursorModifier;C:\Program Files\Redis\;C:\Program Files\MySQL\MySQL Server 9.0\bin;C:\Users\<USER>\;C:\Program Files\Docker\Docker\resources\bin;C:\Program Files\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\;C:\Program Files\Google\Chrome\Application;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.6\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.6\libnvvp;D:\Software\vm\bin\;D:\jdk\flutter_windows_3.22.2-stable\flutter\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;D:\Software\MySQL Server 8.0\bin;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\MinGW\bin;D:\Software\apache-maven-3.6.0\bin;C:\Program Files\Docker\Docker\resources\bin;D:\Software\Git\cmd;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;C:\ProgramData\miniconda3;C:\ProgramData\miniconda3\Scripts;D:\Software\dartsdk-windows-x64-release\dart-sdk\bin;D:\jdk\Android\Sdk\platform-tools;D:\jdk\Andro;C:\Users\<USER>\.jdks\openjdk-21.0.2\bin;D:\Software\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand;.
	at org.apache.tomcat.jni.Library.<init>(Library.java:91)
	at org.apache.tomcat.jni.Library.initialize(Library.java:146)
	at org.apache.catalina.core.AprLifecycleListener.init(AprLifecycleListener.java:193)
	at org.apache.catalina.core.AprLifecycleListener.isAprAvailable(AprLifecycleListener.java:107)
	at org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory.getDefaultServerLifecycleListeners(TomcatServletWebServerFactory.java:189)
	at org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory.<init>(TomcatServletWebServerFactory.java:136)
	at org.springframework.boot.autoconfigure.web.servlet.ServletWebServerFactoryConfiguration$EmbeddedTomcat.tomcatServletWebServerFactory(ServletWebServerFactoryConfiguration.java:73)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:140)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:651)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:643)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1336)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1166)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:563)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:204)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.getWebServerFactory(ServletWebServerApplicationContext.java:223)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.createWebServer(ServletWebServerApplicationContext.java:186)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:162)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:610)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:753)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:455)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:323)
	at com.crypto.trading.bootstrap.CryptoApplication.main(CryptoApplication.java:37)
2025-06-22 18:05:29.202[1750586729202] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'websocketServletWebServerCustomizer'
2025-06-22 18:05:29.202[1750586729202] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.websocket.servlet.WebSocketServletAutoConfiguration$TomcatWebSocketConfiguration'
2025-06-22 18:05:29.224[1750586729224] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'servletWebServerFactoryCustomizer'
2025-06-22 18:05:29.225[1750586729225] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.ServletWebServerFactoryAutoConfiguration'
2025-06-22 18:05:29.235[1750586729235] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
2025-06-22 18:05:29.252[1750586729252] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.context.properties.BoundConfigurationProperties'
2025-06-22 18:05:29.334[1750586729334] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'servletWebServerFactoryCustomizer' via factory method to bean named 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
2025-06-22 18:05:29.349[1750586729349] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'sslBundleRegistry'
2025-06-22 18:05:29.350[1750586729350] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.ssl.SslAutoConfiguration'
2025-06-22 18:05:29.352[1750586729352] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.ssl-org.springframework.boot.autoconfigure.ssl.SslProperties'
2025-06-22 18:05:29.374[1750586729374] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.ssl.SslAutoConfiguration' via constructor to bean named 'spring.ssl-org.springframework.boot.autoconfigure.ssl.SslProperties'
2025-06-22 18:05:29.389[1750586729389] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'sslPropertiesSslBundleRegistrar'
2025-06-22 18:05:29.390[1750586729390] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'fileWatcher'
2025-06-22 18:05:29.402[1750586729402] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'sslPropertiesSslBundleRegistrar' via factory method to bean named 'fileWatcher'
2025-06-22 18:05:29.446[1750586729446] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'tomcatServletWebServerFactoryCustomizer'
2025-06-22 18:05:29.449[1750586729449] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'tomcatServletWebServerFactoryCustomizer' via factory method to bean named 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
2025-06-22 18:05:29.459[1750586729459] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'tomcatWebServerFactoryCustomizer'
2025-06-22 18:05:29.461[1750586729461] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.embedded.EmbeddedWebServerFactoryCustomizerAutoConfiguration$TomcatWebServerFactoryCustomizerConfiguration'
2025-06-22 18:05:29.470[1750586729470] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'tomcatWebServerFactoryCustomizer' via factory method to bean named 'environment'
2025-06-22 18:05:29.470[1750586729470] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'tomcatWebServerFactoryCustomizer' via factory method to bean named 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
2025-06-22 18:05:29.491[1750586729491] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'tomcatVirtualThreadsProtocolHandlerCustomizer'
2025-06-22 18:05:29.497[1750586729497] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'localeCharsetMappingsCustomizer'
2025-06-22 18:05:29.498[1750586729498] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.HttpEncodingAutoConfiguration'
2025-06-22 18:05:29.500[1750586729500] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.HttpEncodingAutoConfiguration' via constructor to bean named 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
2025-06-22 18:05:29.595[1750586729595] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'errorPageCustomizer'
2025-06-22 18:05:29.598[1750586729598] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration'
2025-06-22 18:05:29.600[1750586729600] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration' via constructor to bean named 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
2025-06-22 18:05:29.617[1750586729617] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'dispatcherServletRegistration'
2025-06-22 18:05:29.618[1750586729618] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletRegistrationConfiguration'
2025-06-22 18:05:29.634[1750586729634] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'dispatcherServlet'
2025-06-22 18:05:29.634[1750586729634] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletConfiguration'
2025-06-22 18:05:29.650[1750586729650] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties'
2025-06-22 18:05:29.690[1750586729690] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'dispatcherServlet' via factory method to bean named 'spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties'
2025-06-22 18:05:29.819[1750586729819] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'dispatcherServletRegistration' via factory method to bean named 'dispatcherServlet'
2025-06-22 18:05:29.831[1750586729831] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'dispatcherServletRegistration' via factory method to bean named 'spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties'
2025-06-22 18:05:29.837[1750586729837] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'multipartConfigElement'
2025-06-22 18:05:29.841[1750586729841] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.MultipartAutoConfiguration'
2025-06-22 18:05:29.842[1750586729842] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.servlet.multipart-org.springframework.boot.autoconfigure.web.servlet.MultipartProperties'
2025-06-22 18:05:29.860[1750586729860] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.MultipartAutoConfiguration' via constructor to bean named 'spring.servlet.multipart-org.springframework.boot.autoconfigure.web.servlet.MultipartProperties'
2025-06-22 18:05:29.923[1750586729923] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'errorPageCustomizer' via factory method to bean named 'dispatcherServletRegistration'
2025-06-22 18:05:30.049[1750586730049] | DEBUG | main       | o.a.tomcat.util.compat.Jre22Compat   - Failed to create references to Java 22 classes and methods
java.lang.NoSuchMethodException: java.lang.foreign.MemorySegment.getString(long)
	at java.base/java.lang.Class.getMethod(Class.java:2395)
	at org.apache.tomcat.util.compat.Jre22Compat.<clinit>(Jre22Compat.java:42)
	at org.apache.tomcat.util.compat.JreCompat.<clinit>(JreCompat.java:55)
	at org.apache.catalina.startup.Tomcat.<clinit>(Tomcat.java:1279)
	at org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory.getWebServer(TomcatServletWebServerFactory.java:201)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.createWebServer(ServletWebServerApplicationContext.java:188)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:162)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:610)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:753)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:455)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:323)
	at com.crypto.trading.bootstrap.CryptoApplication.main(CryptoApplication.java:37)
2025-06-22 18:05:30.400[1750586730400] | DEBUG | main       | o.a.tomcat.util.IntrospectionUtils   - IntrospectionUtils: setProperty(class org.apache.coyote.http11.Http11NioProtocol port=9527)
2025-06-22 18:05:30.465[1750586730465] | DEBUG | main       | o.a.tomcat.util.IntrospectionUtils   - IntrospectionUtils: setProperty(class org.apache.coyote.http11.Http11NioProtocol bindOnInit=false)
2025-06-22 18:05:30.479[1750586730479] | DEBUG | main       | o.a.tomcat.util.IntrospectionUtils   - IntrospectionUtils: setProperty(class org.apache.tomcat.util.net.NioEndpoint bindOnInit=false)
2025-06-22 18:05:30.586[1750586730586] | DEBUG | main       | o.apache.catalina.core.ContainerBase - Add child StandardHost[localhost] StandardEngine[Tomcat]
2025-06-22 18:05:30.593[1750586730593] | DEBUG | main       | o.s.b.w.e.t.TomcatServletWebServerFactory - Code archive: D:\maven_repository\org\springframework\boot\spring-boot\3.2.0\spring-boot-3.2.0.jar
2025-06-22 18:05:30.594[1750586730594] | DEBUG | main       | o.s.b.w.e.t.TomcatServletWebServerFactory - Code archive: D:\maven_repository\org\springframework\boot\spring-boot\3.2.0\spring-boot-3.2.0.jar
2025-06-22 18:05:30.595[1750586730595] | DEBUG | main       | o.s.b.w.e.t.TomcatServletWebServerFactory - None of the document roots [src/main/webapp, public, static] point to a directory and will be ignored.
2025-06-22 18:05:30.715[1750586730715] | DEBUG | main       | o.apache.catalina.core.ContainerBase - Add child TomcatEmbeddedContext[/api] StandardEngine[Tomcat].StandardHost[localhost]
2025-06-22 18:05:30.747[1750586730747] | INFO  | main       | o.s.b.w.e.tomcat.TomcatWebServer     - Tomcat initialized with port 9527 (http)
2025-06-22 18:05:30.757[1750586730757] | DEBUG | main       | o.apache.catalina.util.LifecycleBase - Setting state for [StandardServer[-1]] to [INITIALIZING]
2025-06-22 18:05:30.775[1750586730775] | DEBUG | main       | o.apache.catalina.util.LifecycleBase - Setting state for [org.apache.catalina.deploy.NamingResourcesImpl@60fc7f43] to [INITIALIZING]
2025-06-22 18:05:30.776[1750586730776] | DEBUG | main       | o.apache.catalina.util.LifecycleBase - Setting state for [org.apache.catalina.deploy.NamingResourcesImpl@60fc7f43] to [INITIALIZED]
2025-06-22 18:05:30.779[1750586730779] | DEBUG | main       | o.apache.catalina.util.LifecycleBase - Setting state for [StandardService[Tomcat]] to [INITIALIZING]
2025-06-22 18:05:30.782[1750586730782] | DEBUG | main       | o.apache.catalina.util.LifecycleBase - Setting state for [StandardEngine[Tomcat]] to [INITIALIZING]
2025-06-22 18:05:30.782[1750586730782] | DEBUG | main       | o.apache.catalina.util.LifecycleBase - Setting state for [StandardEngine[Tomcat]] to [INITIALIZED]
2025-06-22 18:05:30.785[1750586730785] | DEBUG | main       | o.apache.catalina.util.LifecycleBase - Setting state for [org.apache.catalina.mapper.MapperListener@74c7522c] to [INITIALIZING]
2025-06-22 18:05:30.790[1750586730790] | DEBUG | main       | o.apache.catalina.util.LifecycleBase - Setting state for [org.apache.catalina.mapper.MapperListener@74c7522c] to [INITIALIZED]
2025-06-22 18:05:30.792[1750586730792] | DEBUG | main       | o.apache.catalina.util.LifecycleBase - Setting state for [Connector["http-nio-9527"]] to [INITIALIZING]
2025-06-22 18:05:30.800[1750586730800] | INFO  | main       | o.a.coyote.http11.Http11NioProtocol  - Initializing ProtocolHandler ["http-nio-9527"]
2025-06-22 18:05:30.801[1750586730801] | DEBUG | main       | o.apache.catalina.util.LifecycleBase - Setting state for [Connector["http-nio-9527"]] to [INITIALIZED]
2025-06-22 18:05:30.802[1750586730802] | DEBUG | main       | o.apache.catalina.util.LifecycleBase - Setting state for [StandardService[Tomcat]] to [INITIALIZED]
2025-06-22 18:05:30.804[1750586730804] | DEBUG | main       | o.apache.catalina.util.LifecycleBase - Setting state for [StandardServer[-1]] to [INITIALIZED]
2025-06-22 18:05:30.804[1750586730804] | DEBUG | main       | o.apache.catalina.util.LifecycleBase - Setting state for [StandardServer[-1]] to [STARTING_PREP]
2025-06-22 18:05:30.804[1750586730804] | DEBUG | main       | o.apache.catalina.util.LifecycleBase - Setting state for [StandardServer[-1]] to [STARTING]
2025-06-22 18:05:30.809[1750586730809] | DEBUG | main       | o.apache.catalina.util.LifecycleBase - Setting state for [org.apache.catalina.deploy.NamingResourcesImpl@60fc7f43] to [STARTING_PREP]
2025-06-22 18:05:30.817[1750586730817] | DEBUG | main       | o.apache.catalina.util.LifecycleBase - Setting state for [org.apache.catalina.deploy.NamingResourcesImpl@60fc7f43] to [STARTING]
2025-06-22 18:05:30.817[1750586730817] | DEBUG | main       | o.apache.catalina.util.LifecycleBase - Setting state for [org.apache.catalina.deploy.NamingResourcesImpl@60fc7f43] to [STARTED]
2025-06-22 18:05:30.817[1750586730817] | DEBUG | main       | o.apache.catalina.util.LifecycleBase - Setting state for [StandardService[Tomcat]] to [STARTING_PREP]
2025-06-22 18:05:30.817[1750586730817] | INFO  | main       | o.a.catalina.core.StandardService    - Starting service [Tomcat]
2025-06-22 18:05:30.820[1750586730820] | DEBUG | main       | o.apache.catalina.util.LifecycleBase - Setting state for [StandardService[Tomcat]] to [STARTING]
2025-06-22 18:05:30.824[1750586730824] | DEBUG | main       | o.apache.catalina.util.LifecycleBase - Setting state for [StandardEngine[Tomcat]] to [STARTING_PREP]
2025-06-22 18:05:30.826[1750586730826] | INFO  | main       | o.a.catalina.core.StandardEngine     - Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-06-22 18:05:30.832[1750586730832] | DEBUG | main       | o.apache.catalina.util.LifecycleBase - Setting state for [SimpleRealm[StandardEngine[Tomcat]]] to [INITIALIZING]
2025-06-22 18:05:30.840[1750586730840] | DEBUG | main       | o.apache.catalina.util.LifecycleBase - Setting state for [SimpleRealm[StandardEngine[Tomcat]]] to [INITIALIZED]
2025-06-22 18:05:30.848[1750586730848] | DEBUG | main       | o.apache.catalina.util.LifecycleBase - Setting state for [SimpleRealm[StandardEngine[Tomcat]]] to [STARTING_PREP]
2025-06-22 18:05:30.853[1750586730853] | DEBUG | main       | o.apache.catalina.util.LifecycleBase - Setting state for [SimpleRealm[StandardEngine[Tomcat]]] to [STARTING]
2025-06-22 18:05:30.856[1750586730856] | DEBUG | main       | o.apache.catalina.util.LifecycleBase - Setting state for [SimpleRealm[StandardEngine[Tomcat]]] to [STARTED]
2025-06-22 18:05:30.859[1750586730859] | DEBUG | main       | o.apache.catalina.util.LifecycleBase - Setting state for [StandardEngine[Tomcat].StandardHost[localhost]] to [INITIALIZING]
2025-06-22 18:05:30.859[1750586730859] | DEBUG | main       | o.apache.catalina.util.LifecycleBase - Setting state for [StandardEngine[Tomcat].StandardHost[localhost]] to [INITIALIZED]
2025-06-22 18:05:30.866[1750586730866] | DEBUG | main       | o.apache.catalina.util.LifecycleBase - Setting state for [StandardEngine[Tomcat].StandardHost[localhost]] to [STARTING_PREP]
2025-06-22 18:05:30.867[1750586730867] | DEBUG | main       | o.apache.catalina.util.LifecycleBase - Setting state for [StandardEngine[Tomcat].StandardHost[localhost].TomcatEmbeddedContext[/api]] to [INITIALIZING]
2025-06-22 18:05:30.875[1750586730875] | DEBUG | main       | o.apache.catalina.util.LifecycleBase - Setting state for [StandardEngine[Tomcat].StandardHost[localhost].TomcatEmbeddedContext[/api]] to [INITIALIZED]
2025-06-22 18:05:30.886[1750586730886] | DEBUG | main       | o.apache.catalina.util.LifecycleBase - Setting state for [StandardEngine[Tomcat].StandardHost[localhost].TomcatEmbeddedContext[/api]] to [STARTING_PREP]
2025-06-22 18:05:30.894[1750586730894] | DEBUG | main       | o.a.catalina.core.StandardContext    - Starting api
2025-06-22 18:05:30.931[1750586730931] | DEBUG | main       | o.a.catalina.core.StandardContext    - Configuring default Resources
2025-06-22 18:05:30.959[1750586730959] | DEBUG | main       | o.apache.catalina.util.LifecycleBase - Setting state for [org.apache.catalina.webresources.StandardRoot@27d73d22] to [INITIALIZING]
2025-06-22 18:05:30.969[1750586730969] | DEBUG | main       | o.apache.catalina.util.LifecycleBase - Setting state for [org.apache.catalina.webresources.StandardRoot@27d73d22] to [INITIALIZED]
2025-06-22 18:05:30.969[1750586730969] | DEBUG | main       | o.apache.catalina.util.LifecycleBase - Setting state for [org.apache.catalina.webresources.StandardRoot@27d73d22] to [STARTING_PREP]
2025-06-22 18:05:30.976[1750586730976] | DEBUG | main       | o.apache.catalina.util.LifecycleBase - Setting state for [org.apache.catalina.webresources.DirResourceSet@6ad6ae45] to [INITIALIZING]
2025-06-22 18:05:30.981[1750586730981] | DEBUG | main       | o.apache.catalina.util.LifecycleBase - Setting state for [org.apache.catalina.webresources.DirResourceSet@6ad6ae45] to [INITIALIZED]
2025-06-22 18:05:30.983[1750586730983] | DEBUG | main       | o.apache.catalina.util.LifecycleBase - Setting state for [org.apache.catalina.webresources.DirResourceSet@6ad6ae45] to [STARTING_PREP]
2025-06-22 18:05:30.984[1750586730984] | DEBUG | main       | o.apache.catalina.util.LifecycleBase - Setting state for [org.apache.catalina.webresources.DirResourceSet@6ad6ae45] to [STARTING]
2025-06-22 18:05:30.984[1750586730984] | DEBUG | main       | o.apache.catalina.util.LifecycleBase - Setting state for [org.apache.catalina.webresources.DirResourceSet@6ad6ae45] to [STARTED]
2025-06-22 18:05:30.984[1750586730984] | DEBUG | main       | o.apache.catalina.util.LifecycleBase - Setting state for [org.apache.catalina.webresources.StandardRoot@27d73d22] to [STARTING]
2025-06-22 18:05:30.985[1750586730985] | DEBUG | main       | o.apache.catalina.util.LifecycleBase - Setting state for [org.apache.catalina.webresources.StandardRoot@27d73d22] to [STARTED]
2025-06-22 18:05:30.991[1750586730991] | DEBUG | main       | o.a.catalina.core.StandardContext    - Processing standard container startup
2025-06-22 18:05:30.994[1750586730994] | DEBUG | main       | o.apache.catalina.util.LifecycleBase - Setting state for [WebappLoader[StandardEngine[Tomcat].StandardHost[localhost].TomcatEmbeddedContext[/api]]] to [INITIALIZING]
2025-06-22 18:05:30.995[1750586730995] | DEBUG | main       | o.apache.catalina.util.LifecycleBase - Setting state for [WebappLoader[StandardEngine[Tomcat].StandardHost[localhost].TomcatEmbeddedContext[/api]]] to [INITIALIZED]
2025-06-22 18:05:30.996[1750586730996] | DEBUG | main       | o.apache.catalina.util.LifecycleBase - Setting state for [WebappLoader[StandardEngine[Tomcat].StandardHost[localhost].TomcatEmbeddedContext[/api]]] to [STARTING_PREP]
2025-06-22 18:05:30.996[1750586730996] | DEBUG | main       | o.a.catalina.loader.WebappLoader     - Starting this Loader
2025-06-22 18:05:31.076[1750586731076] | DEBUG | main       | o.apache.catalina.util.LifecycleBase - Setting state for [WebappLoader[StandardEngine[Tomcat].StandardHost[localhost].TomcatEmbeddedContext[/api]]] to [STARTING]
2025-06-22 18:05:31.082[1750586731082] | DEBUG | main       | o.apache.catalina.util.LifecycleBase - Setting state for [WebappLoader[StandardEngine[Tomcat].StandardHost[localhost].TomcatEmbeddedContext[/api]]] to [STARTED]
2025-06-22 18:05:31.167[1750586731167] | DEBUG | main       | o.apache.catalina.util.LifecycleBase - Setting state for [StandardPipeline[StandardEngine[Tomcat].StandardHost[localhost].TomcatEmbeddedContext[/api]]] to [INITIALIZING]
2025-06-22 18:05:31.167[1750586731167] | DEBUG | main       | o.apache.catalina.util.LifecycleBase - Setting state for [StandardPipeline[StandardEngine[Tomcat].StandardHost[localhost].TomcatEmbeddedContext[/api]]] to [INITIALIZED]
2025-06-22 18:05:31.167[1750586731167] | DEBUG | main       | o.apache.catalina.util.LifecycleBase - Setting state for [StandardPipeline[StandardEngine[Tomcat].StandardHost[localhost].TomcatEmbeddedContext[/api]]] to [STARTING_PREP]
2025-06-22 18:05:31.167[1750586731167] | DEBUG | main       | o.apache.catalina.util.LifecycleBase - Setting state for [NonLoginAuthenticator[StandardEngine[Tomcat].StandardHost[localhost].TomcatEmbeddedContext[/api]]] to [INITIALIZING]
2025-06-22 18:05:31.168[1750586731168] | DEBUG | main       | o.apache.catalina.util.LifecycleBase - Setting state for [NonLoginAuthenticator[StandardEngine[Tomcat].StandardHost[localhost].TomcatEmbeddedContext[/api]]] to [INITIALIZED]
2025-06-22 18:05:31.168[1750586731168] | DEBUG | main       | o.apache.catalina.util.LifecycleBase - Setting state for [NonLoginAuthenticator[StandardEngine[Tomcat].StandardHost[localhost].TomcatEmbeddedContext[/api]]] to [STARTING_PREP]
2025-06-22 18:05:31.172[1750586731172] | DEBUG | main       | o.a.c.a.AuthenticatorBase            - No SingleSignOn Valve is present
2025-06-22 18:05:31.174[1750586731174] | DEBUG | main       | o.apache.catalina.util.LifecycleBase - Setting state for [NonLoginAuthenticator[StandardEngine[Tomcat].StandardHost[localhost].TomcatEmbeddedContext[/api]]] to [STARTING]
2025-06-22 18:05:31.175[1750586731175] | DEBUG | main       | o.apache.catalina.util.LifecycleBase - Setting state for [NonLoginAuthenticator[StandardEngine[Tomcat].StandardHost[localhost].TomcatEmbeddedContext[/api]]] to [STARTED]
2025-06-22 18:05:31.175[1750586731175] | DEBUG | main       | o.apache.catalina.util.LifecycleBase - Setting state for [StandardContextValve[StandardEngine[Tomcat].StandardHost[localhost].TomcatEmbeddedContext[/api]]] to [INITIALIZING]
2025-06-22 18:05:31.175[1750586731175] | DEBUG | main       | o.apache.catalina.util.LifecycleBase - Setting state for [StandardContextValve[StandardEngine[Tomcat].StandardHost[localhost].TomcatEmbeddedContext[/api]]] to [INITIALIZED]
2025-06-22 18:05:31.175[1750586731175] | DEBUG | main       | o.apache.catalina.util.LifecycleBase - Setting state for [StandardContextValve[StandardEngine[Tomcat].StandardHost[localhost].TomcatEmbeddedContext[/api]]] to [STARTING_PREP]
2025-06-22 18:05:31.176[1750586731176] | DEBUG | main       | o.apache.catalina.util.LifecycleBase - Setting state for [StandardContextValve[StandardEngine[Tomcat].StandardHost[localhost].TomcatEmbeddedContext[/api]]] to [STARTING]
2025-06-22 18:05:31.176[1750586731176] | DEBUG | main       | o.apache.catalina.util.LifecycleBase - Setting state for [StandardContextValve[StandardEngine[Tomcat].StandardHost[localhost].TomcatEmbeddedContext[/api]]] to [STARTED]
2025-06-22 18:05:31.176[1750586731176] | DEBUG | main       | o.apache.catalina.util.LifecycleBase - Setting state for [StandardPipeline[StandardEngine[Tomcat].StandardHost[localhost].TomcatEmbeddedContext[/api]]] to [STARTING]
2025-06-22 18:05:31.176[1750586731176] | DEBUG | main       | o.apache.catalina.util.LifecycleBase - Setting state for [StandardPipeline[StandardEngine[Tomcat].StandardHost[localhost].TomcatEmbeddedContext[/api]]] to [STARTED]
2025-06-22 18:05:31.176[1750586731176] | DEBUG | main       | o.a.catalina.core.StandardContext    - No manager found. Checking if cluster manager should be used. Cluster configured: [false], Application distributable: [false]
2025-06-22 18:05:31.199[1750586731199] | DEBUG | main       | o.a.catalina.core.StandardContext    - Configured a manager of class [org.apache.catalina.session.StandardManager]
2025-06-22 18:05:31.201[1750586731201] | DEBUG | main       | o.apache.catalina.util.LifecycleBase - Setting state for [org.apache.catalina.deploy.NamingResourcesImpl@2e3cd732] to [INITIALIZING]
2025-06-22 18:05:31.201[1750586731201] | DEBUG | main       | o.apache.catalina.util.LifecycleBase - Setting state for [org.apache.catalina.deploy.NamingResourcesImpl@2e3cd732] to [INITIALIZED]
2025-06-22 18:05:31.203[1750586731203] | DEBUG | main       | o.apache.catalina.util.LifecycleBase - Setting state for [org.apache.catalina.deploy.NamingResourcesImpl@2e3cd732] to [STARTING_PREP]
2025-06-22 18:05:31.203[1750586731203] | DEBUG | main       | o.apache.catalina.util.LifecycleBase - Setting state for [org.apache.catalina.deploy.NamingResourcesImpl@2e3cd732] to [STARTING]
2025-06-22 18:05:31.203[1750586731203] | DEBUG | main       | o.apache.catalina.util.LifecycleBase - Setting state for [org.apache.catalina.deploy.NamingResourcesImpl@2e3cd732] to [STARTED]
2025-06-22 18:05:31.233[1750586731233] | INFO  | main       | o.a.c.c.C.[.[localhost].[/api]       - Initializing Spring embedded WebApplicationContext
2025-06-22 18:05:31.235[1750586731235] | DEBUG | main       | o.s.b.w.s.c.ServletWebServerApplicationContext - Published root WebApplicationContext as ServletContext attribute with name [org.springframework.web.context.WebApplicationContext.ROOT]
2025-06-22 18:05:31.240[1750586731240] | INFO  | main       | o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 13025 ms
2025-06-22 18:05:31.244[1750586731244] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'webMvcObservationFilter'
2025-06-22 18:05:31.256[1750586731256] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.actuate.autoconfigure.observation.web.servlet.WebMvcObservationAutoConfiguration'
2025-06-22 18:05:31.264[1750586731264] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'observationRegistry'
2025-06-22 18:05:31.267[1750586731267] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.actuate.autoconfigure.observation.ObservationAutoConfiguration'
2025-06-22 18:05:31.302[1750586731302] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'propertiesObservationFilter'
2025-06-22 18:05:31.308[1750586731308] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'management.observations-org.springframework.boot.actuate.autoconfigure.observation.ObservationProperties'
2025-06-22 18:05:31.343[1750586731343] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'propertiesObservationFilter' via factory method to bean named 'management.observations-org.springframework.boot.actuate.autoconfigure.observation.ObservationProperties'
2025-06-22 18:05:31.446[1750586731446] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'metricsObservationHandlerGrouping'
2025-06-22 18:05:31.460[1750586731460] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.actuate.autoconfigure.observation.ObservationAutoConfiguration$OnlyMetricsConfiguration'
2025-06-22 18:05:31.519[1750586731519] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'defaultMeterObservationHandler'
2025-06-22 18:05:31.525[1750586731525] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.actuate.autoconfigure.observation.ObservationAutoConfiguration$MeterObservationHandlerConfiguration$OnlyMetricsMeterObservationHandlerConfiguration'
2025-06-22 18:05:31.543[1750586731543] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'simpleMeterRegistry'
2025-06-22 18:05:31.547[1750586731547] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.actuate.autoconfigure.metrics.export.simple.SimpleMetricsExportAutoConfiguration'
2025-06-22 18:05:31.559[1750586731559] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'simpleConfig'
2025-06-22 18:05:31.576[1750586731576] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'management.simple.metrics.export-org.springframework.boot.actuate.autoconfigure.metrics.export.simple.SimpleProperties'
2025-06-22 18:05:31.607[1750586731607] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'simpleConfig' via factory method to bean named 'management.simple.metrics.export-org.springframework.boot.actuate.autoconfigure.metrics.export.simple.SimpleProperties'
2025-06-22 18:05:31.644[1750586731644] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'micrometerClock'
2025-06-22 18:05:31.644[1750586731644] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.actuate.autoconfigure.metrics.MetricsAutoConfiguration'
2025-06-22 18:05:31.659[1750586731659] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'simpleMeterRegistry' via factory method to bean named 'simpleConfig'
2025-06-22 18:05:31.668[1750586731668] | DEBUG | main       | o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'simpleMeterRegistry' via factory method to bean named 'micrometerClock'
2025-06-22 18:50:23.211[1750589423211] | INFO  | main       | c.c.t.b.i.AbstractModuleInitializer  - 模块[test-module]开始初始化...
2025-06-22 18:50:23.213[1750589423213] | INFO  | main       | c.c.t.b.i.AbstractModuleInitializer  - 模块[test-module]正在执行初始化...
2025-06-22 18:50:23.213[1750589423213] | INFO  | main       | c.c.t.b.i.AbstractModuleInitializer  - 模块[test-module]初始化完成
2025-06-22 18:50:23.213[1750589423213] | INFO  | main       | c.c.t.b.i.AbstractModuleInitializer  - 模块[test-module]初始化后处理完成
2025-06-22 18:50:23.226[1750589423226] | INFO  | main       | c.c.t.b.i.AbstractModuleInitializer  - 模块[test-module]正在执行初始化...
2025-06-22 18:50:23.226[1750589423226] | INFO  | main       | c.c.t.b.i.AbstractModuleInitializer  - 模块[test-module]初始化完成
2025-06-22 18:50:23.226[1750589423226] | INFO  | main       | c.c.t.b.i.AbstractModuleInitializer  - 模块[test-module]正在关闭...
2025-06-22 18:50:23.226[1750589423226] | INFO  | main       | c.c.t.b.i.AbstractModuleInitializer  - 模块[test-module]已关闭
2025-06-22 18:50:23.231[1750589423231] | INFO  | main       | c.c.t.b.i.AbstractModuleInitializer  - 模块[test-module]开始初始化...
2025-06-22 18:50:23.233[1750589423233] | INFO  | main       | c.c.t.b.i.AbstractModuleInitializer  - 模块[test-module]正在执行初始化...
2025-06-22 18:50:23.234[1750589423234] | ERROR | main       | c.c.t.b.i.AbstractModuleInitializer  - 模块[test-module]初始化失败: 测试异常
java.lang.RuntimeException: 测试异常
	at com.crypto.trading.bootstrap.initializer.AbstractModuleInitializerTest.testInitializeWithException(AbstractModuleInitializerTest.java:85)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.junit.platform.commons.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:728)
	at org.junit.jupiter.engine.execution.MethodInvocation.proceed(MethodInvocation.java:60)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$ValidatingInvocation.proceed(InvocationInterceptorChain.java:131)
	at org.junit.jupiter.engine.extension.TimeoutExtension.intercept(TimeoutExtension.java:156)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestableMethod(TimeoutExtension.java:147)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestMethod(TimeoutExtension.java:86)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker$ReflectiveInterceptorCall.lambda$ofVoidMethod$0(InterceptingExecutableInvoker.java:103)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.lambda$invoke$0(InterceptingExecutableInvoker.java:93)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$InterceptedInvocation.proceed(InvocationInterceptorChain.java:106)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.proceed(InvocationInterceptorChain.java:64)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.chainAndInvoke(InvocationInterceptorChain.java:45)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.invoke(InvocationInterceptorChain.java:37)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:92)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:86)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$invokeTestMethod$7(TestMethodTestDescriptor.java:218)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.invokeTestMethod(TestMethodTestDescriptor.java:214)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:139)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:69)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.apache.maven.surefire.junitplatform.LazyLauncher.execute(LazyLauncher.java:56)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.execute(JUnitPlatformProvider.java:184)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invokeAllTests(JUnitPlatformProvider.java:148)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invoke(JUnitPlatformProvider.java:122)
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:385)
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:162)
	at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:507)
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:495)
2025-06-22 18:50:23.839[1750589423839] | INFO  | main       | c.c.t.b.lifecycle.ShutdownHook       - 应用程序正在关闭...
2025-06-22 18:50:23.842[1750589423842] | ERROR | main       | c.c.t.b.lifecycle.ShutdownHook       - 应用程序关闭过程中发生错误: 测试异常
java.lang.RuntimeException: 测试异常
	at com.crypto.trading.bootstrap.lifecycle.ShutdownHook.performShutdown(ShutdownHook.java:61)
	at com.crypto.trading.bootstrap.lifecycle.ShutdownHookTest.lambda$testPerformShutdownWithException$0(ShutdownHookTest.java:83)
	at org.junit.jupiter.api.AssertDoesNotThrow.assertDoesNotThrow(AssertDoesNotThrow.java:49)
	at org.junit.jupiter.api.AssertDoesNotThrow.assertDoesNotThrow(AssertDoesNotThrow.java:36)
	at org.junit.jupiter.api.Assertions.assertDoesNotThrow(Assertions.java:3164)
	at com.crypto.trading.bootstrap.lifecycle.ShutdownHookTest.testPerformShutdownWithException(ShutdownHookTest.java:83)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.junit.platform.commons.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:728)
	at org.junit.jupiter.engine.execution.MethodInvocation.proceed(MethodInvocation.java:60)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$ValidatingInvocation.proceed(InvocationInterceptorChain.java:131)
	at org.junit.jupiter.engine.extension.TimeoutExtension.intercept(TimeoutExtension.java:156)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestableMethod(TimeoutExtension.java:147)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestMethod(TimeoutExtension.java:86)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker$ReflectiveInterceptorCall.lambda$ofVoidMethod$0(InterceptingExecutableInvoker.java:103)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.lambda$invoke$0(InterceptingExecutableInvoker.java:93)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$InterceptedInvocation.proceed(InvocationInterceptorChain.java:106)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.proceed(InvocationInterceptorChain.java:64)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.chainAndInvoke(InvocationInterceptorChain.java:45)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.invoke(InvocationInterceptorChain.java:37)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:92)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:86)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$invokeTestMethod$7(TestMethodTestDescriptor.java:218)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.invokeTestMethod(TestMethodTestDescriptor.java:214)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:139)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:69)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.apache.maven.surefire.junitplatform.LazyLauncher.execute(LazyLauncher.java:56)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.execute(JUnitPlatformProvider.java:184)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invokeAllTests(JUnitPlatformProvider.java:148)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invoke(JUnitPlatformProvider.java:122)
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:385)
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:162)
	at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:507)
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:495)
2025-06-22 18:50:23.851[1750589423851] | INFO  | main       | c.c.t.b.lifecycle.ShutdownHook       - 应用程序正在关闭...
2025-06-22 18:50:23.852[1750589423852] | INFO  | main       | c.c.t.b.lifecycle.ShutdownHook       - 应用程序已关闭
2025-06-22 18:50:23.855[1750589423855] | INFO  | main       | c.c.t.b.lifecycle.ShutdownHook       - 应用程序正在关闭...
2025-06-22 18:50:23.856[1750589423856] | INFO  | main       | c.c.t.b.lifecycle.ShutdownHook       - 应用程序已关闭
2025-06-22 18:50:23.858[1750589423858] | INFO  | main       | c.c.t.b.lifecycle.ShutdownHook       - 已注册JVM关闭钩子
2025-06-22 18:50:23.866[1750589423866] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 注册模块初始化器: A, 优先级: 10
2025-06-22 18:50:23.866[1750589423866] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 注册模块初始化器: B, 优先级: 20
2025-06-22 18:50:23.866[1750589423866] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 注册模块初始化器: C, 优先级: 30
2025-06-22 18:50:23.867[1750589423867] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 开始初始化 3 个模块，初始化顺序: A -> B -> C
2025-06-22 18:50:23.867[1750589423867] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 所有模块初始化完成
2025-06-22 18:50:23.869[1750589423869] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 注册模块初始化器: A, 优先级: 10
2025-06-22 18:50:23.869[1750589423869] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 注册模块初始化器: B, 优先级: 20
2025-06-22 18:50:23.869[1750589423869] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 注册模块初始化器: C, 优先级: 30
2025-06-22 18:50:23.871[1750589423871] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 注册模块初始化器: A, 优先级: 10
2025-06-22 18:50:23.871[1750589423871] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 注册模块初始化器: B, 优先级: 20
2025-06-22 18:50:23.871[1750589423871] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 注册模块初始化器: C, 优先级: 30
2025-06-22 18:50:23.871[1750589423871] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 注册模块初始化器: A, 优先级: 10
2025-06-22 18:50:23.871[1750589423871] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 注册模块初始化器: D, 优先级: 40
2025-06-22 18:50:23.873[1750589423873] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 注册模块初始化器: A, 优先级: 10
2025-06-22 18:50:23.874[1750589423874] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 注册模块初始化器: B, 优先级: 20
2025-06-22 18:50:23.874[1750589423874] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 注册模块初始化器: C, 优先级: 30
2025-06-22 18:50:23.874[1750589423874] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 开始初始化 3 个模块，初始化顺序: A -> B -> C
2025-06-22 18:50:23.875[1750589423875] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 所有模块初始化完成
2025-06-22 18:50:23.875[1750589423875] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 开始关闭所有模块，关闭顺序: C -> B -> A
2025-06-22 18:50:23.875[1750589423875] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 所有模块已关闭
2025-06-22 18:50:23.877[1750589423877] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 注册模块初始化器: A, 优先级: 10
2025-06-22 18:50:23.877[1750589423877] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 注册模块初始化器: B, 优先级: 20
2025-06-22 18:50:23.877[1750589423877] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 注册模块初始化器: C, 优先级: 30
2025-06-22 18:50:23.878[1750589423878] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 开始初始化 3 个模块，初始化顺序: A -> B -> C
2025-06-22 18:50:24.058[1750589424058] | INFO  | ShutdownHookThread | c.c.t.b.lifecycle.ShutdownHook       - 应用程序正在关闭...
2025-06-22 18:50:24.059[1750589424059] | INFO  | ShutdownHookThread | c.c.t.b.lifecycle.ShutdownHook       - 应用程序已关闭
2025-06-22 18:53:13.770[1750589593770] | INFO  | main       | c.c.t.b.i.AbstractModuleInitializer  - 模块[test-module]开始初始化...
2025-06-22 18:53:13.774[1750589593774] | INFO  | main       | c.c.t.b.i.AbstractModuleInitializer  - 模块[test-module]正在执行初始化...
2025-06-22 18:53:13.774[1750589593774] | INFO  | main       | c.c.t.b.i.AbstractModuleInitializer  - 模块[test-module]初始化完成
2025-06-22 18:53:13.774[1750589593774] | INFO  | main       | c.c.t.b.i.AbstractModuleInitializer  - 模块[test-module]初始化后处理完成
2025-06-22 18:53:13.787[1750589593787] | INFO  | main       | c.c.t.b.i.AbstractModuleInitializer  - 模块[test-module]正在执行初始化...
2025-06-22 18:53:13.787[1750589593787] | INFO  | main       | c.c.t.b.i.AbstractModuleInitializer  - 模块[test-module]初始化完成
2025-06-22 18:53:13.787[1750589593787] | INFO  | main       | c.c.t.b.i.AbstractModuleInitializer  - 模块[test-module]正在关闭...
2025-06-22 18:53:13.787[1750589593787] | INFO  | main       | c.c.t.b.i.AbstractModuleInitializer  - 模块[test-module]已关闭
2025-06-22 18:53:13.792[1750589593792] | INFO  | main       | c.c.t.b.i.AbstractModuleInitializer  - 模块[test-module]开始初始化...
2025-06-22 18:53:13.793[1750589593793] | INFO  | main       | c.c.t.b.i.AbstractModuleInitializer  - 模块[test-module]正在执行初始化...
2025-06-22 18:53:13.794[1750589593794] | ERROR | main       | c.c.t.b.i.AbstractModuleInitializer  - 模块[test-module]初始化失败: 测试异常
java.lang.RuntimeException: 测试异常
	at com.crypto.trading.bootstrap.initializer.AbstractModuleInitializerTest.testInitializeWithException(AbstractModuleInitializerTest.java:85)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.junit.platform.commons.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:728)
	at org.junit.jupiter.engine.execution.MethodInvocation.proceed(MethodInvocation.java:60)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$ValidatingInvocation.proceed(InvocationInterceptorChain.java:131)
	at org.junit.jupiter.engine.extension.TimeoutExtension.intercept(TimeoutExtension.java:156)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestableMethod(TimeoutExtension.java:147)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestMethod(TimeoutExtension.java:86)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker$ReflectiveInterceptorCall.lambda$ofVoidMethod$0(InterceptingExecutableInvoker.java:103)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.lambda$invoke$0(InterceptingExecutableInvoker.java:93)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$InterceptedInvocation.proceed(InvocationInterceptorChain.java:106)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.proceed(InvocationInterceptorChain.java:64)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.chainAndInvoke(InvocationInterceptorChain.java:45)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.invoke(InvocationInterceptorChain.java:37)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:92)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:86)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$invokeTestMethod$7(TestMethodTestDescriptor.java:218)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.invokeTestMethod(TestMethodTestDescriptor.java:214)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:139)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:69)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.apache.maven.surefire.junitplatform.LazyLauncher.execute(LazyLauncher.java:56)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.execute(JUnitPlatformProvider.java:184)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invokeAllTests(JUnitPlatformProvider.java:148)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invoke(JUnitPlatformProvider.java:122)
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:385)
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:162)
	at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:507)
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:495)
2025-06-22 18:53:14.412[1750589594412] | INFO  | main       | c.c.t.b.lifecycle.ShutdownHook       - 应用程序正在关闭...
2025-06-22 18:53:14.416[1750589594416] | ERROR | main       | c.c.t.b.lifecycle.ShutdownHook       - 应用程序关闭过程中发生错误: 测试异常
java.lang.RuntimeException: 测试异常
	at com.crypto.trading.bootstrap.lifecycle.ShutdownHook.performShutdown(ShutdownHook.java:61)
	at com.crypto.trading.bootstrap.lifecycle.ShutdownHookTest.lambda$testPerformShutdownWithException$0(ShutdownHookTest.java:83)
	at org.junit.jupiter.api.AssertDoesNotThrow.assertDoesNotThrow(AssertDoesNotThrow.java:49)
	at org.junit.jupiter.api.AssertDoesNotThrow.assertDoesNotThrow(AssertDoesNotThrow.java:36)
	at org.junit.jupiter.api.Assertions.assertDoesNotThrow(Assertions.java:3164)
	at com.crypto.trading.bootstrap.lifecycle.ShutdownHookTest.testPerformShutdownWithException(ShutdownHookTest.java:83)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.junit.platform.commons.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:728)
	at org.junit.jupiter.engine.execution.MethodInvocation.proceed(MethodInvocation.java:60)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$ValidatingInvocation.proceed(InvocationInterceptorChain.java:131)
	at org.junit.jupiter.engine.extension.TimeoutExtension.intercept(TimeoutExtension.java:156)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestableMethod(TimeoutExtension.java:147)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestMethod(TimeoutExtension.java:86)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker$ReflectiveInterceptorCall.lambda$ofVoidMethod$0(InterceptingExecutableInvoker.java:103)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.lambda$invoke$0(InterceptingExecutableInvoker.java:93)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$InterceptedInvocation.proceed(InvocationInterceptorChain.java:106)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.proceed(InvocationInterceptorChain.java:64)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.chainAndInvoke(InvocationInterceptorChain.java:45)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.invoke(InvocationInterceptorChain.java:37)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:92)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:86)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$invokeTestMethod$7(TestMethodTestDescriptor.java:218)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.invokeTestMethod(TestMethodTestDescriptor.java:214)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:139)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:69)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.apache.maven.surefire.junitplatform.LazyLauncher.execute(LazyLauncher.java:56)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.execute(JUnitPlatformProvider.java:184)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invokeAllTests(JUnitPlatformProvider.java:148)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invoke(JUnitPlatformProvider.java:122)
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:385)
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:162)
	at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:507)
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:495)
2025-06-22 18:53:14.427[1750589594427] | INFO  | main       | c.c.t.b.lifecycle.ShutdownHook       - 应用程序正在关闭...
2025-06-22 18:53:14.430[1750589594430] | INFO  | main       | c.c.t.b.lifecycle.ShutdownHook       - 应用程序已关闭
2025-06-22 18:53:14.433[1750589594433] | INFO  | main       | c.c.t.b.lifecycle.ShutdownHook       - 应用程序正在关闭...
2025-06-22 18:53:14.433[1750589594433] | INFO  | main       | c.c.t.b.lifecycle.ShutdownHook       - 应用程序已关闭
2025-06-22 18:53:14.436[1750589594436] | INFO  | main       | c.c.t.b.lifecycle.ShutdownHook       - 已注册JVM关闭钩子
2025-06-22 18:53:14.445[1750589594445] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 注册模块初始化器: A, 优先级: 10
2025-06-22 18:53:14.445[1750589594445] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 注册模块初始化器: B, 优先级: 20
2025-06-22 18:53:14.445[1750589594445] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 注册模块初始化器: C, 优先级: 30
2025-06-22 18:53:14.446[1750589594446] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 开始初始化 3 个模块，初始化顺序: A -> B -> C
2025-06-22 18:53:14.446[1750589594446] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 所有模块初始化完成
2025-06-22 18:53:14.448[1750589594448] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 注册模块初始化器: A, 优先级: 10
2025-06-22 18:53:14.448[1750589594448] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 注册模块初始化器: B, 优先级: 20
2025-06-22 18:53:14.448[1750589594448] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 注册模块初始化器: C, 优先级: 30
2025-06-22 18:53:14.451[1750589594451] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 注册模块初始化器: A, 优先级: 10
2025-06-22 18:53:14.451[1750589594451] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 注册模块初始化器: B, 优先级: 20
2025-06-22 18:53:14.451[1750589594451] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 注册模块初始化器: C, 优先级: 30
2025-06-22 18:53:14.452[1750589594452] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 注册模块初始化器: A, 优先级: 10
2025-06-22 18:53:14.452[1750589594452] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 注册模块初始化器: D, 优先级: 40
2025-06-22 18:53:14.456[1750589594456] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 注册模块初始化器: A, 优先级: 10
2025-06-22 18:53:14.456[1750589594456] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 注册模块初始化器: B, 优先级: 20
2025-06-22 18:53:14.456[1750589594456] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 注册模块初始化器: C, 优先级: 30
2025-06-22 18:53:14.456[1750589594456] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 开始初始化 3 个模块，初始化顺序: A -> B -> C
2025-06-22 18:53:14.457[1750589594457] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 所有模块初始化完成
2025-06-22 18:53:14.457[1750589594457] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 开始关闭所有模块，关闭顺序: C -> B -> A
2025-06-22 18:53:14.457[1750589594457] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 所有模块已关闭
2025-06-22 18:53:14.459[1750589594459] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 注册模块初始化器: A, 优先级: 10
2025-06-22 18:53:14.459[1750589594459] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 注册模块初始化器: B, 优先级: 20
2025-06-22 18:53:14.459[1750589594459] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 注册模块初始化器: C, 优先级: 30
2025-06-22 18:53:14.460[1750589594460] | INFO  | main       | c.c.t.b.order.StartupOrderManager    - 开始初始化 3 个模块，初始化顺序: A -> B -> C
2025-06-22 18:53:14.534[1750589594534] | INFO  | ShutdownHookThread | c.c.t.b.lifecycle.ShutdownHook       - 应用程序正在关闭...
2025-06-22 18:53:14.536[1750589594536] | INFO  | ShutdownHookThread | c.c.t.b.lifecycle.ShutdownHook       - 应用程序已关闭
