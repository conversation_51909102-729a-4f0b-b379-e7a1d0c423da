package com.crypto.trading.sdk.client;

import com.alibaba.fastjson2.JSONObject;
import com.binance.connector.futures.client.impl.UMFuturesClientImpl;
import com.crypto.trading.sdk.config.BinanceApiConfig;
import com.crypto.trading.sdk.converter.ModelConverter;
import com.crypto.trading.sdk.exception.ApiException;
import com.crypto.trading.sdk.interceptor.LoggingInterceptor;
import com.crypto.trading.sdk.interceptor.RetryInterceptor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * @name: UMFuturesApiClientImpl
 * @author: zeek
 * @description: 币安U本位合约API客户端的实现，封装了原始的binance-futures-connector-java
 * @create: 2024-07-16 01:35
 */
@Slf4j
@Component
public class UMFuturesApiClientImpl implements BinanceApiClient {

    private final UMFuturesClientImpl client;
    private final BinanceApiConfig config;
    private final ModelConverter modelConverter;

    @Autowired
    public UMFuturesApiClientImpl(BinanceApiConfig config, ModelConverter modelConverter) {
        this.config = config;
        this.modelConverter = modelConverter;
        
        // 使用BinanceEnvironment枚举获取环境相关URL
        com.crypto.trading.common.enums.BinanceEnvironment environment = 
            com.crypto.trading.common.enums.BinanceEnvironment.fromUseTestnet(config.isUseTestnet());
        
        // 获取USDT合约API基础URL
        String baseUrl = environment.getUsdtFuturesApiBaseUrl();

        // 从配置中获取超时参数，而不是从常量类中直接转换
        int connectTimeout = config.getConnectTimeout();
        int readTimeout = config.getReadTimeout();
        int writeTimeout = config.getWriteTimeout();
        
        OkHttpClient okHttpClient = new OkHttpClient.Builder()
                .addInterceptor(new LoggingInterceptor())
                // 注意：重试拦截器需要在日志拦截器之后，以避免记录重试的请求
                .addInterceptor(new RetryInterceptor(config.getMaxRetries()))
                .connectTimeout(connectTimeout, TimeUnit.MILLISECONDS)
                .readTimeout(readTimeout, TimeUnit.MILLISECONDS)
                .writeTimeout(writeTimeout, TimeUnit.MILLISECONDS)
                .build();
        
        // 币安 futures-connector-java v3.x 移除了 setOkHttpClient 方法
        // 由于SDK限制，我们需要使用创建一个适配器类或者使用反射来设置OkHttpClient
        // 目前我们先使用默认的HttpClient，未来可以考虑扩展SDK或降级使用兼容版本
        this.client = new UMFuturesClientImpl(config.getApiKey(), config.getSecretKey(), baseUrl);
        
        // 尝试使用反射设置OkHttpClient（此代码在未来版本可能会失效，需要持续关注SDK更新）
        try {
            java.lang.reflect.Field field = UMFuturesClientImpl.class.getDeclaredField("httpClient");
            field.setAccessible(true);
            field.set(this.client, okHttpClient);
            log.info("成功通过反射设置自定义OkHttpClient");
        } catch (Exception e) {
            log.warn("无法设置自定义OkHttpClient，将使用SDK默认客户端: {}", e.getMessage());
        }

        log.info("U本位保证金期货API客户端初始化完成，baseUrl: {}, useTestnet: {}", baseUrl, config.isUseTestnet());
    }

    @Override
    public String placeMarketOrder(String symbol, String side, String positionSide, BigDecimal quantity, String clientOrderId) {
        LinkedHashMap<String, Object> params = new LinkedHashMap<>();
        params.put("symbol", symbol);
        params.put("side", side);
        params.put("positionSide", positionSide);
        params.put("type", "MARKET");
        params.put("quantity", quantity.toPlainString());
        if (clientOrderId != null && !clientOrderId.isEmpty()) {
            params.put("newClientOrderId", clientOrderId);
        }

        String responseJson = executeRequest(() -> client.account().newOrder(params), "/fapi/v1/order");
        JSONObject responseObj = JSONObject.parseObject(responseJson);

        return responseObj.getString("orderId");
    }

    @Override
    public Map<String, Object> queryOrder(String symbol, String orderId, String clientOrderId) {
        LinkedHashMap<String, Object> params = new LinkedHashMap<>();
        params.put("symbol", symbol);
        if (orderId != null && !orderId.isEmpty()) {
            params.put("orderId", orderId);
        } else if (clientOrderId != null && !clientOrderId.isEmpty()) {
            params.put("origClientOrderId", clientOrderId);
        } else {
            throw new IllegalArgumentException("必须提供 orderId 或 clientOrderId 中的一个");
        }

        String responseJson = executeRequest(() -> client.account().queryOrder(params), "/fapi/v1/order");
        return JSONObject.parseObject(responseJson);
    }
    
    /**
     * 获取订单信息
     *
     * @param parameters 查询参数
     * @return API响应JSON字符串
     */
    public String getOrder(LinkedHashMap<String, Object> parameters) {
        return executeRequest(() -> client.account().queryOrder(parameters), "/fapi/v1/order");
    }
    
    /**
     * 创建新订单
     *
     * @param parameters 订单参数
     * @return API响应JSON字符串
     */
    public String newOrder(LinkedHashMap<String, Object> parameters) {
        return executeRequest(() -> client.account().newOrder(parameters), "/fapi/v1/order");
    }
    
    /**
     * 取消订单
     *
     * @param parameters 取消参数
     * @return API响应JSON字符串
     */
    public String cancelOrder(LinkedHashMap<String, Object> parameters) {
        return executeRequest(() -> client.account().cancelOrder(parameters), "/fapi/v1/order");
    }
    
    /**
     * 取消所有未平仓订单
     *
     * @param parameters 取消参数
     * @return API响应JSON字符串
     */
    public String cancelAllOpenOrders(LinkedHashMap<String, Object> parameters) {
        return executeRequest(() -> client.account().cancelAllOpenOrders(parameters), "/fapi/v1/allOpenOrders");
    }

    @Override
    public String startUserDataStream() {
        // 币安 futures-connector-java v3.x 的 createListenKey 方法变成了无参，需要手动构建请求
        try {
            // 直接调用SDK的无参createListenKey方法
            String responseJson = executeRequest(() -> client.userData().createListenKey(), "/fapi/v1/listenKey");
            JSONObject responseObj = JSONObject.parseObject(responseJson);
            String listenKey = responseObj.getString("listenKey");
            log.info("成功创建用户数据流listenKey: {}", listenKey);
            return listenKey;
        } catch (Exception e) {
            log.error("创建用户数据流失败，尝试手动发送HTTP请求", e);
            // 如果SDK方法失败，尝试手动构建HTTP请求
            try {
                okhttp3.OkHttpClient httpClient = new okhttp3.OkHttpClient();
                okhttp3.Request request = new okhttp3.Request.Builder()
                    .url(config.getUsdtFuturesBaseUrl() + "/fapi/v1/listenKey")
                    .addHeader(com.crypto.trading.common.constant.BinanceConstants.API_KEY_HEADER, config.getApiKey())
                    .post(okhttp3.RequestBody.create(new byte[0], null))
                    .build();
                
                okhttp3.Response response = httpClient.newCall(request).execute();
                if (response.isSuccessful() && response.body() != null) {
                    String responseBody = response.body().string();
                    JSONObject responseObj = JSONObject.parseObject(responseBody);
                    String listenKey = responseObj.getString("listenKey");
                    log.info("通过手动HTTP请求成功创建用户数据流listenKey: {}", listenKey);
                    return listenKey;
                } else {
                    throw new ApiException(com.crypto.trading.common.exception.ErrorCode.BINANCE_API_ERROR, "手动创建用户数据流请求失败，状态码: " + response.code());
                }
            } catch (Exception ex) {
                log.error("手动创建用户数据流失败", ex);
                throw new ApiException("无法创建用户数据流", ex);
            }
        }
    }

    @Override
    public void keepAliveUserDataStream(String listenKey) {
        // 由于SDK限制，我们需要手动发送HTTP请求来延长listenKey的有效期
        if (listenKey == null || listenKey.isEmpty()) {
            log.error("无法延长用户数据流，listenKey为空");
            return;
        }
        
        try {
            log.debug("延长用户数据流listenKey有效期: {}", listenKey);
            okhttp3.OkHttpClient httpClient = new okhttp3.OkHttpClient();
            
            // 构建请求URL，添加listenKey参数
            okhttp3.HttpUrl url = okhttp3.HttpUrl.parse(config.getUsdtFuturesBaseUrl() + "/fapi/v1/listenKey")
                .newBuilder()
                .addQueryParameter("listenKey", listenKey)
                .build();
            
            okhttp3.Request request = new okhttp3.Request.Builder()
                .url(url)
                .addHeader(com.crypto.trading.common.constant.BinanceConstants.API_KEY_HEADER, config.getApiKey())
                .put(okhttp3.RequestBody.create(new byte[0], null))
                .build();
            
            okhttp3.Response response = httpClient.newCall(request).execute();
            if (response.isSuccessful()) {
                log.debug("成功延长用户数据流listenKey有效期: {}", listenKey);
            } else {
                log.warn("延长用户数据流listenKey有效期失败，状态码: {}, 响应: {}", 
                         response.code(), response.body() != null ? response.body().string() : "null");
            }
        } catch (Exception e) {
            log.warn("延长用户数据流listenKey有效期时发生异常: {}", e.getMessage());
        }
    }

    @Override
    public String getKlines(Map<String, Object> parameters) {
        return executeRequest(() -> client.market().klines(new LinkedHashMap<>(parameters)), "/fapi/v1/klines");
    }

    @Override
    public String getTrades(Map<String, Object> parameters) {
        return executeRequest(() -> client.market().trades(new LinkedHashMap<>(parameters)), "/fapi/v1/trades");
    }

    @Override
    public String getDepth(Map<String, Object> parameters) {
        return executeRequest(() -> client.market().depth(new LinkedHashMap<>(parameters)), "/fapi/v1/depth");
    }


    /**
     * 执行API请求并处理异常的通用方法
     *
     * @param apiCall    一个提供API调用的lambda表达式，它返回一个JSON字符串
     * @param apiPath    API的路径，用于日志记录
     * @return 成功时的API响应JSON字符串
     * @throws BinanceApiException 如果API调用失败
     */
    private String executeRequest(Supplier<String> apiCall, String apiPath) {
        try {
            log.debug("准备执行API请求: {}", apiPath);
            String response = apiCall.get();
            log.debug("API请求成功, path: {}, response: {}", apiPath, response);
            return response;
        } catch (Exception e) {
            log.error("币安API请求失败, path: {}, 错误信息: {}", apiPath, e.getMessage(), e);
            throw new ApiException("API请求失败: " + apiPath, e);
        }
    }

    /**
     * 执行一个API请求，但只记录异常而不向上抛出。
     * 适用于一些非关键性操作，如listenKey的续期。
     *
     * @param apiCall 一个提供API调用的lambda表达式
     * @param apiPath API的路径，用于日志记录
     */
    private void handleApiExceptionWithoutThrow(Supplier<String> apiCall, String apiPath) {
        try {
            log.debug("准备执行非关键API请求: {}", apiPath);
            String response = apiCall.get();
            log.debug("非关键API请求成功, path: {}, response: {}", apiPath, response);
        } catch (Exception e) {
            log.warn("一个非关键的币安API请求失败, path: {}, 错误信息: {}. 这可能不会影响核心流程。", apiPath, e.getMessage());
        }
    }
}
