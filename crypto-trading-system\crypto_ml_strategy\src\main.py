#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
主入口文件，负责启动策略模块并处理生命周期管理。

流程：
1. 项目启动后，如果没有模型，则调用Java模块接口获取数据进行模型训练
2. 模型训练完成后，消费Kafka消息进行在线学习并生成交易信号
3. 通过Kafka将交易信号发送到Java的订单执行模块
"""

import os
import sys
import signal
import time
from typing import Dict, Any, Optional
from loguru import logger

import sys
import os
# 添加src目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.config import Config
# 使用简化的日志设置，避免复杂的日志系统冲突
# from infrastructure.logging.logger import setup_logger
from api.kafka_client import KafkaClient
from data.data_processor import DataProcessor
from data.clients.influxdb_client import InfluxDBClient
from data.clients.mysql_client import MySQLClient
from data.real_data_loader import RealDataLoader
from ml.models.model_trainer import ModelTrainer
from ml.models.prediction import PredictionEngine
from strategy.unified_ml import UnifiedMLStrategy
from ml.models.online_learner import OnlineLearner
from ml.models.versioning import ModelVersionManager


class TradingStrategyApp:
    """交易策略应用主类"""
    
    def __init__(self):
        """初始化应用"""
        self.config = Config()
        self.running = False
        # 使用简化的日志设置
        self.logger = logger
        
        # 初始化数据库连接
        self.influxdb_client = InfluxDBClient(
            url=self.config.get('influxdb', 'url'),
            token=self.config.get('influxdb', 'token'),
            org=self.config.get('influxdb', 'org'),
            bucket=self.config.get('influxdb', 'bucket')
        )
        
        self.mysql_client = MySQLClient(
            host=self.config.get('mysql', 'host'),
            port=self.config.get_int('mysql', 'port'),
            user=self.config.get('mysql', 'user'),
            password=self.config.get('mysql', 'password'),
            database=self.config.get('mysql', 'db')
        )
        
        # 初始化Kafka客户端
        self.kafka_client = KafkaClient(self.config)
        
        # 初始化数据处理器
        self.data_processor = DataProcessor(self.config)
        
        # 初始化模型相关组件
        self.model_version_manager = ModelVersionManager(self.mysql_client)
        self.online_learner = OnlineLearner(self.model_version_manager)
        
        # 初始化策略
        self.strategy = UnifiedMLStrategy(self.config)
        
        # 注册信号处理器
        signal.signal(signal.SIGINT, self.handle_signal)
        signal.signal(signal.SIGTERM, self.handle_signal)
    
    def start(self):
        """启动应用"""
        self.logger.info("启动交易策略应用")
        self.running = True
        
        # 检查是否存在有效模型，如果不存在则训练
        if not self.model_version_manager.has_active_model():
            self.logger.info("未找到有效模型，开始训练...")
            self.train_model()
        else:
            self.logger.info(f"使用已有模型版本: {self.model_version_manager.get_current_model_version()}")
            
        # 初始化预测引擎
        prediction_engine = PredictionEngine(self.model_version_manager)
        self.strategy.set_prediction_engine(prediction_engine)
        
        # 启动Kafka消费者
        topics = self.config.get_list('kafka', 'consumer_topics', ['kline.data', 'depth.data', 'trade.data'])
        for topic in topics:
            self.kafka_client.start_consuming(topic, self._process_kafka_message)
        
        # 主循环：保持应用运行
        while self.running:
            try:
                time.sleep(1)  # 主线程休眠，让消费者线程处理消息
                
            except Exception as e:
                self.logger.error(f"主循环异常: {str(e)}")
                time.sleep(1)
    
    def _process_kafka_message(self, message: Dict[str, Any]) -> None:
        """
        处理Kafka消息的回调函数
        
        Args:
            message: Kafka消息
        """
        try:
            # 处理接收的市场数据
            market_data = self._determine_message_type_and_process(message)
            
            if market_data:
                # 在线学习
                if self.config.get_bool('online_learning', 'enabled'):
                    self.online_learner.update(market_data)
                
                # 生成交易信号
                signal = self.strategy.on_market_data(market_data)
                
                # 如果有信号，发布到Kafka
                if signal:
                    self.kafka_client.publish_signal(signal)
                    signal_data = signal.get('data', {})
                    self.logger.info(f"生成交易信号: {signal_data.get('signalType')} {signal_data.get('symbol')} 强度: {signal_data.get('signalStrength')}")
            
        except Exception as e:
            self.logger.error(f"处理Kafka消息异常: {str(e)}")
    
    def _determine_message_type_and_process(self, message: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        根据消息类型处理数据
        
        Args:
            message: Kafka消息
        
        Returns:
            处理后的市场数据
        """
        try:
            message_type = message.get('messageType', '')
            
            if 'kline' in message_type.lower():
                return self.data_processor.process_kline_data(message)
            elif 'depth' in message_type.lower():
                return self.data_processor.process_depth_data(message)
            elif 'trade' in message_type.lower():
                return self.data_processor.process_trade_data(message)
            else:
                # 尝试通用处理
                return self.data_processor.get_latest_market_data(message.get('data', {}).get('symbol', ''))
                
        except Exception as e:
            self.logger.error(f"消息类型判断和处理失败: {str(e)}")
            return None
    
    def train_model(self):
        """训练初始模型"""
        try:
            # 创建数据加载器
            data_loader = RealDataLoader(
                influxdb_client=self.influxdb_client,
                mysql_client=self.mysql_client,
                config=self.config
            )
            
            # 加载训练数据
            self.logger.info("加载训练数据...")
            train_data = data_loader.load_training_data()
            
            # 初始化模型训练器
            model_trainer = ModelTrainer(self.config, self.model_version_manager)
            
            # 训练模型
            self.logger.info("开始训练模型...")
            model_trainer.train(train_data)
            
            self.logger.info("模型训练完成")
            
        except Exception as e:
            self.logger.error(f"模型训练失败: {str(e)}")
            raise
    
    def handle_signal(self, signum, frame):
        """处理终止信号"""
        self.logger.info(f"接收到信号 {signum}，准备关闭应用...")
        self.running = False
    
    def stop(self):
        """停止应用"""
        self.logger.info("关闭交易策略应用")
        self.running = False
        self.kafka_client.clean_up()
        self.mysql_client.close()
        self.influxdb_client.close()


def main():
    """程序入口"""
    app = TradingStrategyApp()
    
    try:
        app.start()
    except KeyboardInterrupt:
        pass
    except Exception as e:
        logger.error(f"应用运行异常: {str(e)}")
    finally:
        app.stop()


if __name__ == "__main__":
    main()