#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Java API兼容性验证核心框架

该模块提供统一的Java API兼容性验证框架，包括消息格式验证、
序列化兼容性测试、性能基准测试和集成测试功能。
"""

import json
import time
import asyncio
from typing import Dict, List, Any, Optional, Tuple, Union
from dataclasses import dataclass, asdict
from datetime import datetime, timezone
import uuid
from loguru import logger

from .config import Config
from .kafka_client import KafkaClient


@dataclass
class ValidationResult:
    """验证结果数据类"""
    test_name: str
    success: bool
    message: str
    details: Dict[str, Any]
    execution_time_ms: float
    timestamp: datetime


@dataclass
class PerformanceMetrics:
    """性能指标数据类"""
    operation: str
    avg_latency_ms: float
    max_latency_ms: float
    min_latency_ms: float
    throughput_ops_per_sec: float
    success_rate: float
    total_operations: int


class JavaApiValidator:
    """
    Java API兼容性验证器
    
    提供统一的Java API兼容性验证框架，确保Python ML组件
    与Java模块API的完全兼容性。
    """
    
    def __init__(self, config: Config):
        """
        初始化Java API验证器
        
        Args:
            config: 配置对象
        """
        self.config = config
        self.kafka_client = KafkaClient(config)
        self.validation_results: List[ValidationResult] = []
        self.performance_metrics: List[PerformanceMetrics] = []
        
        logger.info("Java API验证器初始化完成")
    
    def validate_message_format(self, message: Dict[str, Any], 
                               expected_schema: Dict[str, Any]) -> ValidationResult:
        """
        验证消息格式兼容性
        
        Args:
            message: 待验证的消息
            expected_schema: 期望的消息模式
            
        Returns:
            验证结果
        """
        start_time = time.time()
        
        try:
            # 验证必需字段
            required_fields = expected_schema.get('required', [])
            missing_fields = [field for field in required_fields 
                            if field not in message]
            
            if missing_fields:
                return ValidationResult(
                    test_name="message_format_validation",
                    success=False,
                    message=f"缺少必需字段: {missing_fields}",
                    details={"missing_fields": missing_fields},
                    execution_time_ms=(time.time() - start_time) * 1000,
                    timestamp=datetime.now(timezone.utc)
                )
            
            # 验证字段类型
            field_types = expected_schema.get('properties', {})
            type_errors = []
            
            for field, expected_type in field_types.items():
                if field in message:
                    actual_value = message[field]
                    if not self._validate_field_type(actual_value, expected_type):
                        type_errors.append({
                            'field': field,
                            'expected_type': expected_type,
                            'actual_type': type(actual_value).__name__
                        })
            
            if type_errors:
                return ValidationResult(
                    test_name="message_format_validation",
                    success=False,
                    message=f"字段类型错误: {len(type_errors)}个",
                    details={"type_errors": type_errors},
                    execution_time_ms=(time.time() - start_time) * 1000,
                    timestamp=datetime.now(timezone.utc)
                )
            
            return ValidationResult(
                test_name="message_format_validation",
                success=True,
                message="消息格式验证通过",
                details={"validated_fields": len(message)},
                execution_time_ms=(time.time() - start_time) * 1000,
                timestamp=datetime.now(timezone.utc)
            )
            
        except Exception as e:
            logger.error(f"消息格式验证异常: {e}")
            return ValidationResult(
                test_name="message_format_validation",
                success=False,
                message=f"验证异常: {str(e)}",
                details={"exception": str(e)},
                execution_time_ms=(time.time() - start_time) * 1000,
                timestamp=datetime.now(timezone.utc)
            )
    
    def _validate_field_type(self, value: Any, expected_type: str) -> bool:
        """
        验证字段类型
        
        Args:
            value: 字段值
            expected_type: 期望类型
            
        Returns:
            是否类型匹配
        """
        type_mapping = {
            'string': str,
            'integer': int,
            'number': (int, float),
            'boolean': bool,
            'array': list,
            'object': dict
        }
        
        expected_python_type = type_mapping.get(expected_type)
        if expected_python_type is None:
            return True  # 未知类型，跳过验证
        
        return isinstance(value, expected_python_type)
    
    async def benchmark_api_performance(self, operation_name: str,
                                      operation_func: callable,
                                      iterations: int = 100) -> PerformanceMetrics:
        """
        API性能基准测试
        
        Args:
            operation_name: 操作名称
            operation_func: 操作函数
            iterations: 迭代次数
            
        Returns:
            性能指标
        """
        logger.info(f"开始{operation_name}性能基准测试，迭代次数: {iterations}")
        
        latencies = []
        success_count = 0
        
        start_time = time.time()
        
        for i in range(iterations):
            try:
                op_start = time.time()
                await operation_func() if asyncio.iscoroutinefunction(operation_func) else operation_func()
                op_end = time.time()
                
                latency_ms = (op_end - op_start) * 1000
                latencies.append(latency_ms)
                success_count += 1
                
            except Exception as e:
                logger.warning(f"操作{i+1}失败: {e}")
                latencies.append(float('inf'))
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # 过滤无效延迟
        valid_latencies = [lat for lat in latencies if lat != float('inf')]
        
        if not valid_latencies:
            avg_latency = float('inf')
            max_latency = float('inf')
            min_latency = float('inf')
        else:
            avg_latency = sum(valid_latencies) / len(valid_latencies)
            max_latency = max(valid_latencies)
            min_latency = min(valid_latencies)
        
        throughput = success_count / total_time if total_time > 0 else 0
        success_rate = success_count / iterations
        
        metrics = PerformanceMetrics(
            operation=operation_name,
            avg_latency_ms=avg_latency,
            max_latency_ms=max_latency,
            min_latency_ms=min_latency,
            throughput_ops_per_sec=throughput,
            success_rate=success_rate,
            total_operations=iterations
        )
        
        self.performance_metrics.append(metrics)
        
        logger.info(f"{operation_name}性能测试完成: "
                   f"平均延迟{avg_latency:.2f}ms, "
                   f"吞吐量{throughput:.2f}ops/s, "
                   f"成功率{success_rate:.2%}")
        
        return metrics


class MessageFormatValidator:
    """
    Kafka消息格式验证器
    
    验证Kafka消息格式是否符合Java模块的期望格式。
    """
    
    def __init__(self):
        """初始化消息格式验证器"""
        self.java_message_schemas = self._load_java_message_schemas()
        logger.info("消息格式验证器初始化完成")
    
    def _load_java_message_schemas(self) -> Dict[str, Dict[str, Any]]:
        """
        加载Java消息模式定义
        
        Returns:
            消息模式字典
        """
        return {
            'signal': {
                'required': ['messageId', 'messageType', 'timestamp', 'data'],
                'properties': {
                    'messageId': 'string',
                    'messageType': 'string',
                    'timestamp': 'integer',
                    'data': 'object'
                }
            },
            'kline': {
                'required': ['messageId', 'messageType', 'timestamp', 'data'],
                'properties': {
                    'messageId': 'string',
                    'messageType': 'string',
                    'timestamp': 'integer',
                    'data': 'object'
                }
            },
            'order': {
                'required': ['messageId', 'messageType', 'timestamp', 'data'],
                'properties': {
                    'messageId': 'string',
                    'messageType': 'string',
                    'timestamp': 'integer',
                    'data': 'object'
                }
            }
        }
    
    def validate_signal_message(self, signal: Dict[str, Any]) -> ValidationResult:
        """
        验证策略信号消息格式
        
        Args:
            signal: 策略信号消息
            
        Returns:
            验证结果
        """
        start_time = time.time()
        
        try:
            # 基本格式验证
            schema = self.java_message_schemas['signal']
            basic_validation = self._validate_basic_format(signal, schema)
            
            if not basic_validation.success:
                return basic_validation
            
            # 信号特定字段验证
            data = signal.get('data', {})
            required_signal_fields = [
                'strategyId', 'symbol', 'signalType', 'signalStrength',
                'timeFrame', 'riskAssessment', 'parameters'
            ]
            
            missing_signal_fields = [field for field in required_signal_fields 
                                   if field not in data]
            
            if missing_signal_fields:
                return ValidationResult(
                    test_name="signal_message_validation",
                    success=False,
                    message=f"信号数据缺少字段: {missing_signal_fields}",
                    details={"missing_signal_fields": missing_signal_fields},
                    execution_time_ms=(time.time() - start_time) * 1000,
                    timestamp=datetime.now(timezone.utc)
                )
            
            # 验证信号类型
            valid_signal_types = ['BUY', 'SELL', 'HOLD']
            signal_type = data.get('signalType')
            if signal_type not in valid_signal_types:
                return ValidationResult(
                    test_name="signal_message_validation",
                    success=False,
                    message=f"无效的信号类型: {signal_type}",
                    details={"invalid_signal_type": signal_type,
                            "valid_types": valid_signal_types},
                    execution_time_ms=(time.time() - start_time) * 1000,
                    timestamp=datetime.now(timezone.utc)
                )
            
            return ValidationResult(
                test_name="signal_message_validation",
                success=True,
                message="信号消息格式验证通过",
                details={"signal_type": signal_type, "symbol": data.get('symbol')},
                execution_time_ms=(time.time() - start_time) * 1000,
                timestamp=datetime.now(timezone.utc)
            )
            
        except Exception as e:
            logger.error(f"信号消息验证异常: {e}")
            return ValidationResult(
                test_name="signal_message_validation",
                success=False,
                message=f"验证异常: {str(e)}",
                details={"exception": str(e)},
                execution_time_ms=(time.time() - start_time) * 1000,
                timestamp=datetime.now(timezone.utc)
            )
    
    def _validate_basic_format(self, message: Dict[str, Any], 
                              schema: Dict[str, Any]) -> ValidationResult:
        """
        验证基本消息格式
        
        Args:
            message: 消息
            schema: 消息模式
            
        Returns:
            验证结果
        """
        start_time = time.time()
        
        # 验证必需字段
        required_fields = schema.get('required', [])
        missing_fields = [field for field in required_fields 
                         if field not in message]
        
        if missing_fields:
            return ValidationResult(
                test_name="basic_format_validation",
                success=False,
                message=f"缺少必需字段: {missing_fields}",
                details={"missing_fields": missing_fields},
                execution_time_ms=(time.time() - start_time) * 1000,
                timestamp=datetime.now(timezone.utc)
            )
        
        return ValidationResult(
            test_name="basic_format_validation",
            success=True,
            message="基本格式验证通过",
            details={"validated_fields": len(message)},
            execution_time_ms=(time.time() - start_time) * 1000,
            timestamp=datetime.now(timezone.utc)
        )


def create_test_signal_message() -> Dict[str, Any]:
    """
    创建测试用的策略信号消息
    
    Returns:
        测试信号消息
    """
    return {
        "messageId": str(uuid.uuid4()),
        "messageType": "signal",
        "timestamp": int(time.time() * 1000),
        "data": {
            "strategyId": "unified-ml-strategy-001",
            "symbol": "BTCUSDT",
            "signalType": "BUY",
            "signalStrength": 0.85,
            "timeFrame": "1h",
            "riskAssessment": {
                "marketRisk": 0.35,
                "volatilityRisk": 0.42,
                "drawdownRisk": 0.28,
                "overallRisk": 0.38
            },
            "parameters": {
                "confidence": 0.92,
                "featureContribution": {
                    "lppl_bubble_probability": 0.25,
                    "hematread_momentum": 0.30,
                    "bmsb_support_level": 0.15,
                    "super_trend_signal": 0.30
                },
                "onlineLearning": {
                    "recentAccuracy": 0.87,
                    "adaptationRate": 0.05,
                    "lastUpdateTime": int(time.time() * 1000)
                }
            }
        }
    }


if __name__ == "__main__":
    # 测试代码
    from .config import Config
    
    config = Config()
    validator = JavaApiValidator(config)
    message_validator = MessageFormatValidator()
    
    # 测试信号消息验证
    test_signal = create_test_signal_message()
    result = message_validator.validate_signal_message(test_signal)
    
    logger.info(f"测试结果: {result.success}, 消息: {result.message}")