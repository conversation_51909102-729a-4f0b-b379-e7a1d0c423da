#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
SuperTrend 指标计算模块

实现SuperTrend技术指标，该指标是一种趋势跟踪指标，基于ATR计算动态支撑和阻力位，生成明确的买卖信号。
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Union


class SuperTrendCalculator:
    """SuperTrend指标计算器，用于生成趋势跟踪信号"""

    def __init__(self, config: Dict = None):
        """
        初始化SuperTrend指标计算器
        
        Args:
            config: 配置参数字典，包括以下可选参数：
                - atr_period: ATR周期，默认10
                - atr_multiplier: ATR乘数，默认3.0
        """
        self.logger = logging.getLogger('cryptoMlStrategy.indicators.supertrend')
        
        # 默认配置
        self.config = {
            'atr_period': 10,
            'atr_multiplier': 3.0
        }
        
        # 更新用户配置
        if config:
            self.config.update(config)
            
        self.logger.info("SuperTrend计算器初始化完成")

    def calculate_atr(self, df: pd.DataFrame, period: int = None, 
                      high_col: str = 'high', low_col: str = 'low', close_col: str = 'close') -> pd.DataFrame:
        """
        计算平均真实范围(ATR)
        
        Args:
            df: 价格数据DataFrame
            period: ATR周期
            high_col: 最高价列名称
            low_col: 最低价列名称
            close_col: 收盘价列名称
            
        Returns:
            添加了ATR的DataFrame
        """
        result_df = df.copy()
        period = period or self.config['atr_period']
        
        # 确保有足够的数据计算ATR
        if len(result_df) < period + 1:
            self.logger.warning(f"数据长度不足以计算{period}周期ATR")
            return df
        
        # 计算真实范围(TR)
        result_df['tr0'] = abs(result_df[high_col] - result_df[low_col])
        result_df['tr1'] = abs(result_df[high_col] - result_df[close_col].shift())
        result_df['tr2'] = abs(result_df[low_col] - result_df[close_col].shift())
        result_df['tr'] = result_df[['tr0', 'tr1', 'tr2']].max(axis=1)
        
        # 计算ATR
        result_df[f'atr{period}'] = result_df['tr'].rolling(window=period).mean()
        
        # 删除中间计算列
        result_df.drop(['tr0', 'tr1', 'tr2', 'tr'], axis=1, inplace=True)
        
        return result_df

    def calculate_supertrend(self, df: pd.DataFrame, 
                            atr_period: int = None, 
                            atr_multiplier: float = None,
                            high_col: str = 'high', 
                            low_col: str = 'low', 
                            close_col: str = 'close') -> pd.DataFrame:
        """
        计算SuperTrend指标
        
        Args:
            df: 价格数据DataFrame
            atr_period: ATR周期
            atr_multiplier: ATR乘数
            high_col: 最高价列名称
            low_col: 最低价列名称
            close_col: 收盘价列名称
            
        Returns:
            添加了SuperTrend指标的DataFrame
        """
        result_df = df.copy()
        atr_period = atr_period or self.config['atr_period']
        atr_multiplier = atr_multiplier or self.config['atr_multiplier']
        
        # 先计算ATR
        if f'atr{atr_period}' not in result_df.columns:
            result_df = self.calculate_atr(result_df, atr_period, high_col, low_col, close_col)
        
        # 检查ATR是否成功计算
        if f'atr{atr_period}' not in result_df.columns:
            self.logger.error("ATR计算失败，无法计算SuperTrend")
            return df
        
        # 计算上轨和下轨的基础值
        result_df['basic_upper'] = (result_df[high_col] + result_df[low_col]) / 2 + atr_multiplier * result_df[f'atr{atr_period}']
        result_df['basic_lower'] = (result_df[high_col] + result_df[low_col]) / 2 - atr_multiplier * result_df[f'atr{atr_period}']
        
        # 初始化SuperTrend列
        st_prefix = f'supertrend{atr_period}_{atr_multiplier}'
        cols = [f'{st_prefix}_upper', f'{st_prefix}_lower', f'{st_prefix}_direction']
        for col in cols:
            result_df[col] = np.nan
        
        # 计算第一个有效值
        for i in range(1, len(result_df)):
            if pd.isna(result_df[f'atr{atr_period}'].iloc[i-1]):
                continue
                
            # 方向初始化为1(看多)，如果收盘价低于下轨则为-1(看空)
            if i == 1:
                result_df[f'{st_prefix}_direction'].iloc[i] = 1
                if result_df[close_col].iloc[i] <= result_df['basic_lower'].iloc[i]:
                    result_df[f'{st_prefix}_direction'].iloc[i] = -1
            else:
                # 更新方向
                prev_direction = result_df[f'{st_prefix}_direction'].iloc[i-1]
                
                if prev_direction == 1:
                    # 如果前一个方向是看多，当收盘价低于前一个下轨时转为看空
                    if result_df[close_col].iloc[i] <= result_df[f'{st_prefix}_lower'].iloc[i-1]:
                        result_df[f'{st_prefix}_direction'].iloc[i] = -1
                    else:
                        result_df[f'{st_prefix}_direction'].iloc[i] = 1
                else:
                    # 如果前一个方向是看空，当收盘价高于前一个上轨时转为看多
                    if result_df[close_col].iloc[i] >= result_df[f'{st_prefix}_upper'].iloc[i-1]:
                        result_df[f'{st_prefix}_direction'].iloc[i] = 1
                    else:
                        result_df[f'{st_prefix}_direction'].iloc[i] = -1
            
            # 更新上下轨
            if result_df[f'{st_prefix}_direction'].iloc[i] == 1:
                # 看多状态，使用下轨
                if result_df['basic_lower'].iloc[i] > result_df[f'{st_prefix}_lower'].iloc[i-1] or pd.isna(result_df[f'{st_prefix}_lower'].iloc[i-1]):
                    result_df[f'{st_prefix}_lower'].iloc[i] = result_df['basic_lower'].iloc[i]
                else:
                    result_df[f'{st_prefix}_lower'].iloc[i] = result_df[f'{st_prefix}_lower'].iloc[i-1]
                    
                result_df[f'{st_prefix}_upper'].iloc[i] = np.nan
            else:
                # 看空状态，使用上轨
                if result_df['basic_upper'].iloc[i] < result_df[f'{st_prefix}_upper'].iloc[i-1] or pd.isna(result_df[f'{st_prefix}_upper'].iloc[i-1]):
                    result_df[f'{st_prefix}_upper'].iloc[i] = result_df['basic_upper'].iloc[i]
                else:
                    result_df[f'{st_prefix}_upper'].iloc[i] = result_df[f'{st_prefix}_upper'].iloc[i-1]
                    
                result_df[f'{st_prefix}_lower'].iloc[i] = np.nan
        
        # 计算超级趋势值
        result_df[f'{st_prefix}'] = np.where(
            result_df[f'{st_prefix}_direction'] == 1,
            result_df[f'{st_prefix}_lower'],
            result_df[f'{st_prefix}_upper']
        )
        
        # 计算趋势变化点
        result_df[f'{st_prefix}_change'] = result_df[f'{st_prefix}_direction'].diff()
        
        # 计算价格与SuperTrend的距离（用于判断势头强度）
        result_df[f'{st_prefix}_distance'] = (result_df[close_col] - result_df[f'{st_prefix}']) / result_df[close_col]
        
        # 删除中间计算列
        result_df.drop(['basic_upper', 'basic_lower'], axis=1, inplace=True)
        
        return result_df

    def calculate_multi_supertrend(self, df: pd.DataFrame, 
                                   param_sets: List[Tuple[int, float]] = None,
                                   high_col: str = 'high', 
                                   low_col: str = 'low', 
                                   close_col: str = 'close') -> pd.DataFrame:
        """
        计算多个参数设置的SuperTrend指标
        
        Args:
            df: 价格数据DataFrame
            param_sets: 参数集列表，每个元素为(atr_period, atr_multiplier)
            high_col: 最高价列名称
            low_col: 最低价列名称
            close_col: 收盘价列名称
            
        Returns:
            添加了多个SuperTrend指标的DataFrame
        """
        result_df = df.copy()
        
        # 默认参数设置
        if param_sets is None:
            param_sets = [
                (10, 1.0),  # 短期，灵敏
                (10, 3.0),  # 中期，标准
                (14, 4.0)   # 长期，保守
            ]
        
        # 依次计算每组参数的SuperTrend
        for period, multiplier in param_sets:
            result_df = self.calculate_supertrend(
                result_df, period, multiplier, high_col, low_col, close_col)
        
        return result_df

    def combine_supertrend_signals(self, df: pd.DataFrame, 
                                   param_sets: List[Tuple[int, float]] = None) -> pd.DataFrame:
        """
        组合多个SuperTrend信号，生成综合信号
        
        Args:
            df: 包含多个SuperTrend指标的DataFrame
            param_sets: 参数集列表，每个元素为(atr_period, atr_multiplier)
            
        Returns:
            添加了综合信号的DataFrame
        """
        result_df = df.copy()
        
        # 默认参数设置
        if param_sets is None:
            param_sets = [
                (10, 1.0),  # 短期，灵敏
                (10, 3.0),  # 中期，标准
                (14, 4.0)   # 长期，保守
            ]
        
        # 检查必要的列是否存在
        st_cols = [f'supertrend{period}_{multiplier}_direction' for period, multiplier in param_sets]
        if not all(col in result_df.columns for col in st_cols):
            missing_cols = [col for col in st_cols if col not in result_df.columns]
            self.logger.warning(f"缺少必要的SuperTrend方向列: {missing_cols}，无法生成综合信号")
            return df
        
        # 计算综合信号
        # 1: 所有指标都看多
        # -1: 所有指标都看空
        # 0: 信号不一致
        
        # 计算看多和看空的SuperTrend指标数量
        result_df['st_bullish_count'] = 0
        result_df['st_bearish_count'] = 0
        
        for col in st_cols:
            result_df['st_bullish_count'] += (result_df[col] == 1).astype(int)
            result_df['st_bearish_count'] += (result_df[col] == -1).astype(int)
        
        # 根据多空指标数量计算方向强度(-1到1)
        total_indicators = len(st_cols)
        result_df['st_direction_strength'] = (result_df['st_bullish_count'] - result_df['st_bearish_count']) / total_indicators
        
        # 生成综合信号
        # 使用加权方案：较保守的信号权重更高
        weights = [0.2, 0.3, 0.5]  # 从灵敏到保守的权重递增
        
        if len(param_sets) == len(weights):
            result_df['st_weighted_signal'] = 0
            
            for i, (period, multiplier) in enumerate(param_sets):
                col = f'supertrend{period}_{multiplier}_direction'
                result_df['st_weighted_signal'] += result_df[col] * weights[i]
        
        # 生成最终信号
        result_df['supertrend_signal'] = np.where(
            result_df['st_direction_strength'] >= 0.7,  # 至少70%的指标看多
            1,
            np.where(
                result_df['st_direction_strength'] <= -0.7,  # 至少70%的指标看空
                -1,
                0
            )
        )
        
        # 计算信号强度
        result_df['supertrend_strength'] = abs(result_df['st_direction_strength'])
        
        # 删除中间计算列
        result_df.drop(['st_bullish_count', 'st_bearish_count'], axis=1, inplace=True)
        
        return result_df

    def extract_features(self, df: pd.DataFrame, 
                        high_col: str = 'high', 
                        low_col: str = 'low', 
                        close_col: str = 'close') -> pd.DataFrame:
        """
        从DataFrame中提取SuperTrend特征
        
        Args:
            df: 包含价格数据的DataFrame
            high_col: 最高价列名称
            low_col: 最低价列名称
            close_col: 收盘价列名称
            
        Returns:
            添加了SuperTrend特征的DataFrame
        """
        if df is None or df.empty:
            return df
            
        # 检查必要的列是否存在
        required_cols = [high_col, low_col, close_col]
        if not all(col in df.columns for col in required_cols):
            missing_cols = [col for col in required_cols if col not in df.columns]
            self.logger.error(f"缺少必要的价格列: {missing_cols}，无法计算SuperTrend")
            return df
            
        try:
            # 1. 计算多个参数设置的SuperTrend
            param_sets = [
                (10, 1.0),  # 短期，灵敏
                (10, 3.0),  # 中期，标准
                (14, 4.0)   # 长期，保守
            ]
            
            result_df = self.calculate_multi_supertrend(
                df, param_sets, high_col, low_col, close_col)
            
            # 2. 组合多个SuperTrend信号
            result_df = self.combine_supertrend_signals(result_df, param_sets)
            
            return result_df
            
        except Exception as e:
            self.logger.error(f"SuperTrend特征提取失败: {e}")
            return df