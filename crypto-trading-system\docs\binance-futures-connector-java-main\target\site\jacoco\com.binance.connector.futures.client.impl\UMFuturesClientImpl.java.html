<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>UMFuturesClientImpl.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">io.github.binance:binance-futures-connector-java</a> &gt; <a href="index.source.html" class="el_package">com.binance.connector.futures.client.impl</a> &gt; <span class="el_source">UMFuturesClientImpl.java</span></div><h1>UMFuturesClientImpl.java</h1><pre class="source lang-java linenums">package com.binance.connector.futures.client.impl;

import com.binance.connector.futures.client.enums.DefaultUrls;
import com.binance.connector.futures.client.impl.um_futures.UMAccount;
import com.binance.connector.futures.client.impl.um_futures.UMMarket;
import com.binance.connector.futures.client.impl.um_futures.UMUserData;

public class UMFuturesClientImpl extends FuturesClientImpl {
<span class="fc" id="L9">    private static String defaultBaseUrl = DefaultUrls.USDM_PROD_URL;</span>
<span class="fc" id="L10">    private static String umProduct = &quot;/fapi&quot;;</span>

    public UMFuturesClientImpl() {
<span class="nc" id="L13">        super(defaultBaseUrl, umProduct);</span>
<span class="nc" id="L14">    }</span>

    public UMFuturesClientImpl(String baseUrl) {
<span class="fc" id="L17">        super(baseUrl, umProduct);</span>
<span class="fc" id="L18">    }</span>

    public UMFuturesClientImpl(String apiKey, String secretKey) {
<span class="nc" id="L21">        super(apiKey, secretKey, defaultBaseUrl, umProduct);</span>
<span class="nc" id="L22">    }</span>

    public UMFuturesClientImpl(String baseUrl, boolean showLimitUsage) {
<span class="nc" id="L25">        super(baseUrl, umProduct, showLimitUsage);</span>
<span class="nc" id="L26">    }</span>

    public UMFuturesClientImpl(String apiKey, String secretKey, boolean showLimitUsage) {
<span class="nc" id="L29">        super(apiKey, secretKey, defaultBaseUrl, umProduct, showLimitUsage);</span>
<span class="nc" id="L30">    }</span>

    public UMFuturesClientImpl(String apiKey, String secretKey, String baseUrl) {
<span class="fc" id="L33">        super(apiKey, secretKey, baseUrl, umProduct);</span>
<span class="fc" id="L34">    }</span>

    @Override
    public UMMarket market() {
<span class="fc" id="L38">        return new UMMarket(getProductUrl(), getBaseUrl(), getApiKey(), getShowLimitUsage(), getProxy());</span>
    }

    @Override
    public UMAccount account() {
<span class="fc" id="L43">        return new UMAccount(getProductUrl(), getApiKey(), getSecretKey(), getShowLimitUsage(), getProxy());</span>
    }

    @Override
    public UMUserData userData() {
<span class="fc" id="L48">        return new UMUserData(getProductUrl(), getApiKey(), getShowLimitUsage(), getProxy());</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.7.************</span></div></body></html>