/**
 * Autogenerated by Avro
 *
 * DO NOT EDIT DIRECTLY
 */
package com.crypto.trading.common.avro;

import org.apache.avro.generic.GenericArray;
import org.apache.avro.specific.SpecificData;
import org.apache.avro.util.Utf8;
import org.apache.avro.message.BinaryMessageEncoder;
import org.apache.avro.message.BinaryMessageDecoder;
import org.apache.avro.message.SchemaStore;

/** 订单执行结果Avro模式定义 */
@org.apache.avro.specific.AvroGenerated
public class OrderExecutionResult extends org.apache.avro.specific.SpecificRecordBase implements org.apache.avro.specific.SpecificRecord {
  private static final long serialVersionUID = 4745480346455982380L;


  public static final org.apache.avro.Schema SCHEMA$ = new org.apache.avro.Schema.Parser().parse("{\"type\":\"record\",\"name\":\"OrderExecutionResult\",\"namespace\":\"com.crypto.trading.common.avro\",\"doc\":\"订单执行结果Avro模式定义\",\"fields\":[{\"name\":\"id\",\"type\":{\"type\":\"string\",\"avro.java.string\":\"String\"},\"doc\":\"唯一消息ID\"},{\"name\":\"timestamp\",\"type\":\"long\",\"doc\":\"消息时间戳(毫秒)\"},{\"name\":\"type\",\"type\":{\"type\":\"string\",\"avro.java.string\":\"String\"},\"doc\":\"消息类型，固定为ORDER_EXECUTION_RESULT\"},{\"name\":\"source\",\"type\":{\"type\":\"string\",\"avro.java.string\":\"String\"},\"doc\":\"消息来源模块\"},{\"name\":\"version\",\"type\":{\"type\":\"string\",\"avro.java.string\":\"String\"},\"doc\":\"消息版本\"},{\"name\":\"data\",\"type\":{\"type\":\"record\",\"name\":\"OrderExecutionResultData\",\"fields\":[{\"name\":\"orderId\",\"type\":\"long\",\"doc\":\"订单ID\"},{\"name\":\"clientOrderId\",\"type\":[{\"type\":\"string\",\"avro.java.string\":\"String\"},\"null\"],\"doc\":\"客户端订单ID\"},{\"name\":\"symbol\",\"type\":{\"type\":\"string\",\"avro.java.string\":\"String\"},\"doc\":\"交易对符号\"},{\"name\":\"accountId\",\"type\":{\"type\":\"string\",\"avro.java.string\":\"String\"},\"doc\":\"账户ID\"},{\"name\":\"price\",\"type\":[\"double\",\"null\"],\"doc\":\"订单价格\"},{\"name\":\"origQty\",\"type\":\"double\",\"doc\":\"原始数量\"},{\"name\":\"executedQty\",\"type\":\"double\",\"doc\":\"已执行数量\"},{\"name\":\"cummulativeQuoteQty\",\"type\":[\"double\",\"null\"],\"doc\":\"累计成交金额\"},{\"name\":\"status\",\"type\":{\"type\":\"string\",\"avro.java.string\":\"String\"},\"doc\":\"订单状态\"},{\"name\":\"type\",\"type\":{\"type\":\"string\",\"avro.java.string\":\"String\"},\"doc\":\"订单类型\"},{\"name\":\"side\",\"type\":{\"type\":\"string\",\"avro.java.string\":\"String\"},\"doc\":\"订单方向\"},{\"name\":\"positionSide\",\"type\":[{\"type\":\"string\",\"avro.java.string\":\"String\"},\"null\"],\"doc\":\"持仓方向\"},{\"name\":\"stopPrice\",\"type\":[\"double\",\"null\"],\"doc\":\"止损价格\"},{\"name\":\"avgPrice\",\"type\":[\"double\",\"null\"],\"doc\":\"平均成交价格\"},{\"name\":\"commissionAsset\",\"type\":[{\"type\":\"string\",\"avro.java.string\":\"String\"},\"null\"],\"doc\":\"手续费资产\"},{\"name\":\"commissionAmount\",\"type\":[\"double\",\"null\"],\"doc\":\"手续费金额\"},{\"name\":\"time\",\"type\":\"long\",\"doc\":\"订单时间(毫秒)\"},{\"name\":\"updateTime\",\"type\":\"long\",\"doc\":\"更新时间(毫秒)\"},{\"name\":\"isWorking\",\"type\":\"boolean\",\"doc\":\"订单是否生效中\"},{\"name\":\"fills\",\"type\":[{\"type\":\"string\",\"avro.java.string\":\"String\"},\"null\"],\"doc\":\"成交明细JSON\"},{\"name\":\"errorCode\",\"type\":[\"int\",\"null\"],\"doc\":\"错误代码\"},{\"name\":\"errorMessage\",\"type\":[{\"type\":\"string\",\"avro.java.string\":\"String\"},\"null\"],\"doc\":\"错误信息\"}]},\"doc\":\"订单执行结果数据\"}]}");
  public static org.apache.avro.Schema getClassSchema() { return SCHEMA$; }

  private static final SpecificData MODEL$ = new SpecificData();

  private static final BinaryMessageEncoder<OrderExecutionResult> ENCODER =
      new BinaryMessageEncoder<>(MODEL$, SCHEMA$);

  private static final BinaryMessageDecoder<OrderExecutionResult> DECODER =
      new BinaryMessageDecoder<>(MODEL$, SCHEMA$);

  /**
   * Return the BinaryMessageEncoder instance used by this class.
   * @return the message encoder used by this class
   */
  public static BinaryMessageEncoder<OrderExecutionResult> getEncoder() {
    return ENCODER;
  }

  /**
   * Return the BinaryMessageDecoder instance used by this class.
   * @return the message decoder used by this class
   */
  public static BinaryMessageDecoder<OrderExecutionResult> getDecoder() {
    return DECODER;
  }

  /**
   * Create a new BinaryMessageDecoder instance for this class that uses the specified {@link SchemaStore}.
   * @param resolver a {@link SchemaStore} used to find schemas by fingerprint
   * @return a BinaryMessageDecoder instance for this class backed by the given SchemaStore
   */
  public static BinaryMessageDecoder<OrderExecutionResult> createDecoder(SchemaStore resolver) {
    return new BinaryMessageDecoder<>(MODEL$, SCHEMA$, resolver);
  }

  /**
   * Serializes this OrderExecutionResult to a ByteBuffer.
   * @return a buffer holding the serialized data for this instance
   * @throws java.io.IOException if this instance could not be serialized
   */
  public java.nio.ByteBuffer toByteBuffer() throws java.io.IOException {
    return ENCODER.encode(this);
  }

  /**
   * Deserializes a OrderExecutionResult from a ByteBuffer.
   * @param b a byte buffer holding serialized data for an instance of this class
   * @return a OrderExecutionResult instance decoded from the given buffer
   * @throws java.io.IOException if the given bytes could not be deserialized into an instance of this class
   */
  public static OrderExecutionResult fromByteBuffer(
      java.nio.ByteBuffer b) throws java.io.IOException {
    return DECODER.decode(b);
  }

  /** 唯一消息ID */
  private java.lang.String id;
  /** 消息时间戳(毫秒) */
  private long timestamp;
  /** 消息类型，固定为ORDER_EXECUTION_RESULT */
  private java.lang.String type;
  /** 消息来源模块 */
  private java.lang.String source;
  /** 消息版本 */
  private java.lang.String version;
  /** 订单执行结果数据 */
  private com.crypto.trading.common.avro.OrderExecutionResultData data;

  /**
   * Default constructor.  Note that this does not initialize fields
   * to their default values from the schema.  If that is desired then
   * one should use <code>newBuilder()</code>.
   */
  public OrderExecutionResult() {}

  /**
   * All-args constructor.
   * @param id 唯一消息ID
   * @param timestamp 消息时间戳(毫秒)
   * @param type 消息类型，固定为ORDER_EXECUTION_RESULT
   * @param source 消息来源模块
   * @param version 消息版本
   * @param data 订单执行结果数据
   */
  public OrderExecutionResult(java.lang.String id, java.lang.Long timestamp, java.lang.String type, java.lang.String source, java.lang.String version, com.crypto.trading.common.avro.OrderExecutionResultData data) {
    this.id = id;
    this.timestamp = timestamp;
    this.type = type;
    this.source = source;
    this.version = version;
    this.data = data;
  }

  @Override
  public org.apache.avro.specific.SpecificData getSpecificData() { return MODEL$; }

  @Override
  public org.apache.avro.Schema getSchema() { return SCHEMA$; }

  // Used by DatumWriter.  Applications should not call.
  @Override
  public java.lang.Object get(int field$) {
    switch (field$) {
    case 0: return id;
    case 1: return timestamp;
    case 2: return type;
    case 3: return source;
    case 4: return version;
    case 5: return data;
    default: throw new IndexOutOfBoundsException("Invalid index: " + field$);
    }
  }

  // Used by DatumReader.  Applications should not call.
  @Override
  @SuppressWarnings(value="unchecked")
  public void put(int field$, java.lang.Object value$) {
    switch (field$) {
    case 0: id = value$ != null ? value$.toString() : null; break;
    case 1: timestamp = (java.lang.Long)value$; break;
    case 2: type = value$ != null ? value$.toString() : null; break;
    case 3: source = value$ != null ? value$.toString() : null; break;
    case 4: version = value$ != null ? value$.toString() : null; break;
    case 5: data = (com.crypto.trading.common.avro.OrderExecutionResultData)value$; break;
    default: throw new IndexOutOfBoundsException("Invalid index: " + field$);
    }
  }

  /**
   * Gets the value of the 'id' field.
   * @return 唯一消息ID
   */
  public java.lang.String getId() {
    return id;
  }


  /**
   * Sets the value of the 'id' field.
   * 唯一消息ID
   * @param value the value to set.
   */
  public void setId(java.lang.String value) {
    this.id = value;
  }

  /**
   * Gets the value of the 'timestamp' field.
   * @return 消息时间戳(毫秒)
   */
  public long getTimestamp() {
    return timestamp;
  }


  /**
   * Sets the value of the 'timestamp' field.
   * 消息时间戳(毫秒)
   * @param value the value to set.
   */
  public void setTimestamp(long value) {
    this.timestamp = value;
  }

  /**
   * Gets the value of the 'type' field.
   * @return 消息类型，固定为ORDER_EXECUTION_RESULT
   */
  public java.lang.String getType() {
    return type;
  }


  /**
   * Sets the value of the 'type' field.
   * 消息类型，固定为ORDER_EXECUTION_RESULT
   * @param value the value to set.
   */
  public void setType(java.lang.String value) {
    this.type = value;
  }

  /**
   * Gets the value of the 'source' field.
   * @return 消息来源模块
   */
  public java.lang.String getSource() {
    return source;
  }


  /**
   * Sets the value of the 'source' field.
   * 消息来源模块
   * @param value the value to set.
   */
  public void setSource(java.lang.String value) {
    this.source = value;
  }

  /**
   * Gets the value of the 'version' field.
   * @return 消息版本
   */
  public java.lang.String getVersion() {
    return version;
  }


  /**
   * Sets the value of the 'version' field.
   * 消息版本
   * @param value the value to set.
   */
  public void setVersion(java.lang.String value) {
    this.version = value;
  }

  /**
   * Gets the value of the 'data' field.
   * @return 订单执行结果数据
   */
  public com.crypto.trading.common.avro.OrderExecutionResultData getData() {
    return data;
  }


  /**
   * Sets the value of the 'data' field.
   * 订单执行结果数据
   * @param value the value to set.
   */
  public void setData(com.crypto.trading.common.avro.OrderExecutionResultData value) {
    this.data = value;
  }

  /**
   * Creates a new OrderExecutionResult RecordBuilder.
   * @return A new OrderExecutionResult RecordBuilder
   */
  public static com.crypto.trading.common.avro.OrderExecutionResult.Builder newBuilder() {
    return new com.crypto.trading.common.avro.OrderExecutionResult.Builder();
  }

  /**
   * Creates a new OrderExecutionResult RecordBuilder by copying an existing Builder.
   * @param other The existing builder to copy.
   * @return A new OrderExecutionResult RecordBuilder
   */
  public static com.crypto.trading.common.avro.OrderExecutionResult.Builder newBuilder(com.crypto.trading.common.avro.OrderExecutionResult.Builder other) {
    if (other == null) {
      return new com.crypto.trading.common.avro.OrderExecutionResult.Builder();
    } else {
      return new com.crypto.trading.common.avro.OrderExecutionResult.Builder(other);
    }
  }

  /**
   * Creates a new OrderExecutionResult RecordBuilder by copying an existing OrderExecutionResult instance.
   * @param other The existing instance to copy.
   * @return A new OrderExecutionResult RecordBuilder
   */
  public static com.crypto.trading.common.avro.OrderExecutionResult.Builder newBuilder(com.crypto.trading.common.avro.OrderExecutionResult other) {
    if (other == null) {
      return new com.crypto.trading.common.avro.OrderExecutionResult.Builder();
    } else {
      return new com.crypto.trading.common.avro.OrderExecutionResult.Builder(other);
    }
  }

  /**
   * RecordBuilder for OrderExecutionResult instances.
   */
  @org.apache.avro.specific.AvroGenerated
  public static class Builder extends org.apache.avro.specific.SpecificRecordBuilderBase<OrderExecutionResult>
    implements org.apache.avro.data.RecordBuilder<OrderExecutionResult> {

    /** 唯一消息ID */
    private java.lang.String id;
    /** 消息时间戳(毫秒) */
    private long timestamp;
    /** 消息类型，固定为ORDER_EXECUTION_RESULT */
    private java.lang.String type;
    /** 消息来源模块 */
    private java.lang.String source;
    /** 消息版本 */
    private java.lang.String version;
    /** 订单执行结果数据 */
    private com.crypto.trading.common.avro.OrderExecutionResultData data;
    private com.crypto.trading.common.avro.OrderExecutionResultData.Builder dataBuilder;

    /** Creates a new Builder */
    private Builder() {
      super(SCHEMA$, MODEL$);
    }

    /**
     * Creates a Builder by copying an existing Builder.
     * @param other The existing Builder to copy.
     */
    private Builder(com.crypto.trading.common.avro.OrderExecutionResult.Builder other) {
      super(other);
      if (isValidValue(fields()[0], other.id)) {
        this.id = data().deepCopy(fields()[0].schema(), other.id);
        fieldSetFlags()[0] = other.fieldSetFlags()[0];
      }
      if (isValidValue(fields()[1], other.timestamp)) {
        this.timestamp = data().deepCopy(fields()[1].schema(), other.timestamp);
        fieldSetFlags()[1] = other.fieldSetFlags()[1];
      }
      if (isValidValue(fields()[2], other.type)) {
        this.type = data().deepCopy(fields()[2].schema(), other.type);
        fieldSetFlags()[2] = other.fieldSetFlags()[2];
      }
      if (isValidValue(fields()[3], other.source)) {
        this.source = data().deepCopy(fields()[3].schema(), other.source);
        fieldSetFlags()[3] = other.fieldSetFlags()[3];
      }
      if (isValidValue(fields()[4], other.version)) {
        this.version = data().deepCopy(fields()[4].schema(), other.version);
        fieldSetFlags()[4] = other.fieldSetFlags()[4];
      }
      if (isValidValue(fields()[5], other.data)) {
        this.data = data().deepCopy(fields()[5].schema(), other.data);
        fieldSetFlags()[5] = other.fieldSetFlags()[5];
      }
      if (other.hasDataBuilder()) {
        this.dataBuilder = com.crypto.trading.common.avro.OrderExecutionResultData.newBuilder(other.getDataBuilder());
      }
    }

    /**
     * Creates a Builder by copying an existing OrderExecutionResult instance
     * @param other The existing instance to copy.
     */
    private Builder(com.crypto.trading.common.avro.OrderExecutionResult other) {
      super(SCHEMA$, MODEL$);
      if (isValidValue(fields()[0], other.id)) {
        this.id = data().deepCopy(fields()[0].schema(), other.id);
        fieldSetFlags()[0] = true;
      }
      if (isValidValue(fields()[1], other.timestamp)) {
        this.timestamp = data().deepCopy(fields()[1].schema(), other.timestamp);
        fieldSetFlags()[1] = true;
      }
      if (isValidValue(fields()[2], other.type)) {
        this.type = data().deepCopy(fields()[2].schema(), other.type);
        fieldSetFlags()[2] = true;
      }
      if (isValidValue(fields()[3], other.source)) {
        this.source = data().deepCopy(fields()[3].schema(), other.source);
        fieldSetFlags()[3] = true;
      }
      if (isValidValue(fields()[4], other.version)) {
        this.version = data().deepCopy(fields()[4].schema(), other.version);
        fieldSetFlags()[4] = true;
      }
      if (isValidValue(fields()[5], other.data)) {
        this.data = data().deepCopy(fields()[5].schema(), other.data);
        fieldSetFlags()[5] = true;
      }
      this.dataBuilder = null;
    }

    /**
      * Gets the value of the 'id' field.
      * 唯一消息ID
      * @return The value.
      */
    public java.lang.String getId() {
      return id;
    }


    /**
      * Sets the value of the 'id' field.
      * 唯一消息ID
      * @param value The value of 'id'.
      * @return This builder.
      */
    public com.crypto.trading.common.avro.OrderExecutionResult.Builder setId(java.lang.String value) {
      validate(fields()[0], value);
      this.id = value;
      fieldSetFlags()[0] = true;
      return this;
    }

    /**
      * Checks whether the 'id' field has been set.
      * 唯一消息ID
      * @return True if the 'id' field has been set, false otherwise.
      */
    public boolean hasId() {
      return fieldSetFlags()[0];
    }


    /**
      * Clears the value of the 'id' field.
      * 唯一消息ID
      * @return This builder.
      */
    public com.crypto.trading.common.avro.OrderExecutionResult.Builder clearId() {
      id = null;
      fieldSetFlags()[0] = false;
      return this;
    }

    /**
      * Gets the value of the 'timestamp' field.
      * 消息时间戳(毫秒)
      * @return The value.
      */
    public long getTimestamp() {
      return timestamp;
    }


    /**
      * Sets the value of the 'timestamp' field.
      * 消息时间戳(毫秒)
      * @param value The value of 'timestamp'.
      * @return This builder.
      */
    public com.crypto.trading.common.avro.OrderExecutionResult.Builder setTimestamp(long value) {
      validate(fields()[1], value);
      this.timestamp = value;
      fieldSetFlags()[1] = true;
      return this;
    }

    /**
      * Checks whether the 'timestamp' field has been set.
      * 消息时间戳(毫秒)
      * @return True if the 'timestamp' field has been set, false otherwise.
      */
    public boolean hasTimestamp() {
      return fieldSetFlags()[1];
    }


    /**
      * Clears the value of the 'timestamp' field.
      * 消息时间戳(毫秒)
      * @return This builder.
      */
    public com.crypto.trading.common.avro.OrderExecutionResult.Builder clearTimestamp() {
      fieldSetFlags()[1] = false;
      return this;
    }

    /**
      * Gets the value of the 'type' field.
      * 消息类型，固定为ORDER_EXECUTION_RESULT
      * @return The value.
      */
    public java.lang.String getType() {
      return type;
    }


    /**
      * Sets the value of the 'type' field.
      * 消息类型，固定为ORDER_EXECUTION_RESULT
      * @param value The value of 'type'.
      * @return This builder.
      */
    public com.crypto.trading.common.avro.OrderExecutionResult.Builder setType(java.lang.String value) {
      validate(fields()[2], value);
      this.type = value;
      fieldSetFlags()[2] = true;
      return this;
    }

    /**
      * Checks whether the 'type' field has been set.
      * 消息类型，固定为ORDER_EXECUTION_RESULT
      * @return True if the 'type' field has been set, false otherwise.
      */
    public boolean hasType() {
      return fieldSetFlags()[2];
    }


    /**
      * Clears the value of the 'type' field.
      * 消息类型，固定为ORDER_EXECUTION_RESULT
      * @return This builder.
      */
    public com.crypto.trading.common.avro.OrderExecutionResult.Builder clearType() {
      type = null;
      fieldSetFlags()[2] = false;
      return this;
    }

    /**
      * Gets the value of the 'source' field.
      * 消息来源模块
      * @return The value.
      */
    public java.lang.String getSource() {
      return source;
    }


    /**
      * Sets the value of the 'source' field.
      * 消息来源模块
      * @param value The value of 'source'.
      * @return This builder.
      */
    public com.crypto.trading.common.avro.OrderExecutionResult.Builder setSource(java.lang.String value) {
      validate(fields()[3], value);
      this.source = value;
      fieldSetFlags()[3] = true;
      return this;
    }

    /**
      * Checks whether the 'source' field has been set.
      * 消息来源模块
      * @return True if the 'source' field has been set, false otherwise.
      */
    public boolean hasSource() {
      return fieldSetFlags()[3];
    }


    /**
      * Clears the value of the 'source' field.
      * 消息来源模块
      * @return This builder.
      */
    public com.crypto.trading.common.avro.OrderExecutionResult.Builder clearSource() {
      source = null;
      fieldSetFlags()[3] = false;
      return this;
    }

    /**
      * Gets the value of the 'version' field.
      * 消息版本
      * @return The value.
      */
    public java.lang.String getVersion() {
      return version;
    }


    /**
      * Sets the value of the 'version' field.
      * 消息版本
      * @param value The value of 'version'.
      * @return This builder.
      */
    public com.crypto.trading.common.avro.OrderExecutionResult.Builder setVersion(java.lang.String value) {
      validate(fields()[4], value);
      this.version = value;
      fieldSetFlags()[4] = true;
      return this;
    }

    /**
      * Checks whether the 'version' field has been set.
      * 消息版本
      * @return True if the 'version' field has been set, false otherwise.
      */
    public boolean hasVersion() {
      return fieldSetFlags()[4];
    }


    /**
      * Clears the value of the 'version' field.
      * 消息版本
      * @return This builder.
      */
    public com.crypto.trading.common.avro.OrderExecutionResult.Builder clearVersion() {
      version = null;
      fieldSetFlags()[4] = false;
      return this;
    }

    /**
      * Gets the value of the 'data' field.
      * 订单执行结果数据
      * @return The value.
      */
    public com.crypto.trading.common.avro.OrderExecutionResultData getData() {
      return data;
    }


    /**
      * Sets the value of the 'data' field.
      * 订单执行结果数据
      * @param value The value of 'data'.
      * @return This builder.
      */
    public com.crypto.trading.common.avro.OrderExecutionResult.Builder setData(com.crypto.trading.common.avro.OrderExecutionResultData value) {
      validate(fields()[5], value);
      this.dataBuilder = null;
      this.data = value;
      fieldSetFlags()[5] = true;
      return this;
    }

    /**
      * Checks whether the 'data' field has been set.
      * 订单执行结果数据
      * @return True if the 'data' field has been set, false otherwise.
      */
    public boolean hasData() {
      return fieldSetFlags()[5];
    }

    /**
     * Gets the Builder instance for the 'data' field and creates one if it doesn't exist yet.
     * 订单执行结果数据
     * @return This builder.
     */
    public com.crypto.trading.common.avro.OrderExecutionResultData.Builder getDataBuilder() {
      if (dataBuilder == null) {
        if (hasData()) {
          setDataBuilder(com.crypto.trading.common.avro.OrderExecutionResultData.newBuilder(data));
        } else {
          setDataBuilder(com.crypto.trading.common.avro.OrderExecutionResultData.newBuilder());
        }
      }
      return dataBuilder;
    }

    /**
     * Sets the Builder instance for the 'data' field
     * 订单执行结果数据
     * @param value The builder instance that must be set.
     * @return This builder.
     */

    public com.crypto.trading.common.avro.OrderExecutionResult.Builder setDataBuilder(com.crypto.trading.common.avro.OrderExecutionResultData.Builder value) {
      clearData();
      dataBuilder = value;
      return this;
    }

    /**
     * Checks whether the 'data' field has an active Builder instance
     * 订单执行结果数据
     * @return True if the 'data' field has an active Builder instance
     */
    public boolean hasDataBuilder() {
      return dataBuilder != null;
    }

    /**
      * Clears the value of the 'data' field.
      * 订单执行结果数据
      * @return This builder.
      */
    public com.crypto.trading.common.avro.OrderExecutionResult.Builder clearData() {
      data = null;
      dataBuilder = null;
      fieldSetFlags()[5] = false;
      return this;
    }

    @Override
    @SuppressWarnings("unchecked")
    public OrderExecutionResult build() {
      try {
        OrderExecutionResult record = new OrderExecutionResult();
        record.id = fieldSetFlags()[0] ? this.id : (java.lang.String) defaultValue(fields()[0]);
        record.timestamp = fieldSetFlags()[1] ? this.timestamp : (java.lang.Long) defaultValue(fields()[1]);
        record.type = fieldSetFlags()[2] ? this.type : (java.lang.String) defaultValue(fields()[2]);
        record.source = fieldSetFlags()[3] ? this.source : (java.lang.String) defaultValue(fields()[3]);
        record.version = fieldSetFlags()[4] ? this.version : (java.lang.String) defaultValue(fields()[4]);
        if (dataBuilder != null) {
          try {
            record.data = this.dataBuilder.build();
          } catch (org.apache.avro.AvroMissingFieldException e) {
            e.addParentField(record.getSchema().getField("data"));
            throw e;
          }
        } else {
          record.data = fieldSetFlags()[5] ? this.data : (com.crypto.trading.common.avro.OrderExecutionResultData) defaultValue(fields()[5]);
        }
        return record;
      } catch (org.apache.avro.AvroMissingFieldException e) {
        throw e;
      } catch (java.lang.Exception e) {
        throw new org.apache.avro.AvroRuntimeException(e);
      }
    }
  }

  @SuppressWarnings("unchecked")
  private static final org.apache.avro.io.DatumWriter<OrderExecutionResult>
    WRITER$ = (org.apache.avro.io.DatumWriter<OrderExecutionResult>)MODEL$.createDatumWriter(SCHEMA$);

  @Override public void writeExternal(java.io.ObjectOutput out)
    throws java.io.IOException {
    WRITER$.write(this, SpecificData.getEncoder(out));
  }

  @SuppressWarnings("unchecked")
  private static final org.apache.avro.io.DatumReader<OrderExecutionResult>
    READER$ = (org.apache.avro.io.DatumReader<OrderExecutionResult>)MODEL$.createDatumReader(SCHEMA$);

  @Override public void readExternal(java.io.ObjectInput in)
    throws java.io.IOException {
    READER$.read(this, SpecificData.getDecoder(in));
  }

  @Override protected boolean hasCustomCoders() { return true; }

  @Override public void customEncode(org.apache.avro.io.Encoder out)
    throws java.io.IOException
  {
    out.writeString(this.id);

    out.writeLong(this.timestamp);

    out.writeString(this.type);

    out.writeString(this.source);

    out.writeString(this.version);

    this.data.customEncode(out);

  }

  @Override public void customDecode(org.apache.avro.io.ResolvingDecoder in)
    throws java.io.IOException
  {
    org.apache.avro.Schema.Field[] fieldOrder = in.readFieldOrderIfDiff();
    if (fieldOrder == null) {
      this.id = in.readString();

      this.timestamp = in.readLong();

      this.type = in.readString();

      this.source = in.readString();

      this.version = in.readString();

      if (this.data == null) {
        this.data = new com.crypto.trading.common.avro.OrderExecutionResultData();
      }
      this.data.customDecode(in);

    } else {
      for (int i = 0; i < 6; i++) {
        switch (fieldOrder[i].pos()) {
        case 0:
          this.id = in.readString();
          break;

        case 1:
          this.timestamp = in.readLong();
          break;

        case 2:
          this.type = in.readString();
          break;

        case 3:
          this.source = in.readString();
          break;

        case 4:
          this.version = in.readString();
          break;

        case 5:
          if (this.data == null) {
            this.data = new com.crypto.trading.common.avro.OrderExecutionResultData();
          }
          this.data.customDecode(in);
          break;

        default:
          throw new java.io.IOException("Corrupt ResolvingDecoder.");
        }
      }
    }
  }
}










