#!/usr/bin/env python3
"""
Crypto ML Strategy - 综合导入测试脚本

该脚本验证重组后的目录结构中所有模块的导入功能，包括：
- Task 12核心组件的相对导入
- 跨目录引用的功能性
- 包级别导入测试
- 循环依赖检测

作者: Crypto ML Strategy Team
版本: 1.0.0
"""

import sys
import os
import traceback
from typing import Dict, List, Tuple, Any
from datetime import datetime

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))


class ImportTestResult:
    """导入测试结果"""
    
    def __init__(self):
        self.total_tests = 0
        self.passed_tests = 0
        self.failed_tests = 0
        self.test_results: List[Dict[str, Any]] = []
        self.start_time = datetime.now()
    
    def add_test(self, module_name: str, success: bool, error: str = None):
        """添加测试结果"""
        self.total_tests += 1
        if success:
            self.passed_tests += 1
        else:
            self.failed_tests += 1
        
        self.test_results.append({
            'module': module_name,
            'success': success,
            'error': error,
            'timestamp': datetime.now()
        })
    
    def get_summary(self) -> Dict[str, Any]:
        """获取测试摘要"""
        duration = datetime.now() - self.start_time
        return {
            'total_tests': self.total_tests,
            'passed_tests': self.passed_tests,
            'failed_tests': self.failed_tests,
            'success_rate': self.passed_tests / max(self.total_tests, 1) * 100,
            'duration_seconds': duration.total_seconds(),
            'test_time': self.start_time.isoformat()
        }


def test_task12_core_components(result: ImportTestResult) -> None:
    """测试Task 12核心组件导入"""
    print("[INFO] 测试Task 12核心组件导入...")
    
    # 测试core模块
    try:
        from src.core.validation_result_types import (
            ValidationResult, 
            IntegrationTestResult,
            calculate_confidence_interval,
            test_statistical_significance,
            calculate_effect_size
        )
        result.add_test("src.core.validation_result_types", True)
        print("[PASS] core.validation_result_types 导入成功")
    except Exception as e:
        result.add_test("src.core.validation_result_types", False, str(e))
        print(f"❌ core.validation_result_types 导入失败: {e}")
    
    # 测试validators模块
    try:
        from src.validators.latency_throughput_validators import LatencyValidator, ThroughputValidator
        from src.validators.memory_accuracy_validators import MemoryValidator, AccuracyValidator
        result.add_test("src.validators", True)
        print("✅ validators 模块导入成功")
    except Exception as e:
        result.add_test("src.validators", False, str(e))
        print(f"❌ validators 模块导入失败: {e}")
    
    # 测试coordinators模块
    try:
        from src.coordinators.system_performance_coordinator import SystemPerformanceCoordinator
        result.add_test("src.coordinators", True)
        print("✅ coordinators 模块导入成功")
    except Exception as e:
        result.add_test("src.coordinators", False, str(e))
        print(f"❌ coordinators 模块导入失败: {e}")
    
    # 注意：performance_tests已移动到外部tests/目录，这里测试benchmarks模块
    try:
        from src.benchmarks.benchmark_core_framework import BenchmarkFramework
        from src.benchmarks.performance_metrics_collector import MetricsSnapshot
        result.add_test("src.benchmarks", True)
        print("✅ benchmarks 模块导入成功")
    except Exception as e:
        result.add_test("src.benchmarks", False, str(e))
        print(f"❌ benchmarks 模块导入失败: {e}")


def test_package_level_imports(result: ImportTestResult) -> None:
    """测试包级别导入"""
    print("\n🔍 测试包级别导入...")
    
    packages_to_test = [
        ("src.core", ["ValidationResult", "IntegrationTestResult"]),
        ("src.validators", ["LatencyValidator", "ThroughputValidator", "MemoryValidator", "AccuracyValidator"]),
        ("src.coordinators", ["SystemPerformanceCoordinator"]),
        ("src.benchmarks", ["BenchmarkFramework", "MetricsSnapshot"]),
        ("src.ml", ["MLInferenceOptimizer", "ModelOptimizationPipeline"]),
        ("src.infrastructure", []),
        ("src.api", []),
        ("src.docs", []),
        ("src.data", []),
        ("src.indicators", []),
        ("src.strategy", []),
        ("src.risk_management", []),
        ("src.utils", [])
    ]
    
    for package_name, expected_exports in packages_to_test:
        try:
            package = __import__(package_name, fromlist=[''])
            
            # 检查__all__属性
            if hasattr(package, '__all__'):
                all_exports = package.__all__
                print(f"✅ {package_name} 包导入成功，导出: {all_exports}")
            else:
                print(f"✅ {package_name} 包导入成功（无__all__定义）")
            
            result.add_test(f"package.{package_name}", True)
            
        except Exception as e:
            result.add_test(f"package.{package_name}", False, str(e))
            print(f"❌ {package_name} 包导入失败: {e}")


def test_cross_directory_references(result: ImportTestResult) -> None:
    """测试跨目录引用"""
    print("\n🔍 测试跨目录引用...")
    
    # 测试validators引用core
    try:
        from src.validators.latency_throughput_validators import LatencyValidator
        validator = LatencyValidator(target_latency_ms=100.0)
        result.add_test("cross_ref.validators_to_core", True)
        print("✅ validators → core 引用成功")
    except Exception as e:
        result.add_test("cross_ref.validators_to_core", False, str(e))
        print(f"❌ validators → core 引用失败: {e}")
    
    # 测试coordinators引用validators和core
    try:
        from src.coordinators.system_performance_coordinator import SystemPerformanceCoordinator
        coordinator = SystemPerformanceCoordinator()
        result.add_test("cross_ref.coordinators_to_validators_core", True)
        print("✅ coordinators → validators/core 引用成功")
    except Exception as e:
        result.add_test("cross_ref.coordinators_to_validators_core", False, str(e))
        print(f"❌ coordinators → validators/core 引用失败: {e}")


def test_existing_modules(result: ImportTestResult) -> None:
    """测试现有模块的导入"""
    print("\n🔍 测试现有模块导入...")
    
    existing_modules = [
        "src.data",
        "src.indicators", 
        "src.strategy",
        "src.risk_management",
        "src.utils"
    ]
    
    for module_name in existing_modules:
        try:
            module = __import__(module_name, fromlist=[''])
            result.add_test(f"existing.{module_name}", True)
            print(f"✅ {module_name} 导入成功")
        except Exception as e:
            result.add_test(f"existing.{module_name}", False, str(e))
            print(f"❌ {module_name} 导入失败: {e}")


def test_main_config_imports(result: ImportTestResult) -> None:
    """测试主要配置文件导入"""
    print("\n🔍 测试主要配置文件导入...")
    
    try:
        import src.config
        result.add_test("src.config", True)
        print("✅ config 模块导入成功")
    except Exception as e:
        result.add_test("src.config", False, str(e))
        print(f"❌ config 模块导入失败: {e}")


def detect_circular_imports(result: ImportTestResult) -> None:
    """检测循环导入"""
    print("\n🔍 检测循环导入...")
    
    # 这是一个简化的循环导入检测
    # 在实际环境中，循环导入通常在模块加载时就会被发现
    try:
        # 尝试导入所有主要模块
        import src.core
        import src.validators  
        import src.coordinators
        import src.ml
        import src.infrastructure
        import src.benchmarks
        import src.api
        import src.docs
        import src.data
        import src.indicators
        import src.strategy
        import src.risk_management
        import src.utils
        
        result.add_test("circular_import_check", True)
        print("✅ 未检测到循环导入")
        
    except ImportError as e:
        if "circular import" in str(e).lower():
            result.add_test("circular_import_check", False, str(e))
            print(f"❌ 检测到循环导入: {e}")
        else:
            result.add_test("circular_import_check", False, f"导入错误: {e}")
            print(f"❌ 导入错误: {e}")


def generate_test_report(result: ImportTestResult) -> str:
    """生成测试报告"""
    summary = result.get_summary()
    
    report = f"""
# Crypto ML Strategy - 综合导入测试报告

## 测试摘要
- **测试时间**: {summary['test_time']}
- **测试总数**: {summary['total_tests']}
- **通过测试**: {summary['passed_tests']}
- **失败测试**: {summary['failed_tests']}
- **成功率**: {summary['success_rate']:.1f}%
- **测试耗时**: {summary['duration_seconds']:.2f}秒

## 详细结果

### 通过的测试
"""
    
    for test in result.test_results:
        if test['success']:
            report += f"- ✅ {test['module']}\n"
    
    report += "\n### 失败的测试\n"
    
    failed_tests = [test for test in result.test_results if not test['success']]
    if failed_tests:
        for test in failed_tests:
            report += f"- ❌ {test['module']}: {test['error']}\n"
    else:
        report += "- 无失败测试\n"
    
    report += f"""
## 结论

{'🎉 所有导入测试通过！目录重组成功！' if summary['failed_tests'] == 0 else '⚠️ 存在导入问题，需要进一步修复。'}

---
生成时间: {datetime.now().isoformat()}
"""
    
    return report


def main() -> bool:
    """主测试函数"""
    print("🚀 开始综合导入测试...")
    print("=" * 60)
    
    result = ImportTestResult()
    
    # 执行各项测试
    test_task12_core_components(result)
    test_package_level_imports(result)
    test_cross_directory_references(result)
    test_existing_modules(result)
    test_main_config_imports(result)
    detect_circular_imports(result)
    
    # 生成报告
    print("\n" + "=" * 60)
    summary = result.get_summary()
    print(f"📊 测试完成: {summary['passed_tests']}/{summary['total_tests']} 通过 "
          f"({summary['success_rate']:.1f}%)")
    
    # 保存详细报告
    report = generate_test_report(result)
    
    try:
        with open('import_test_report.md', 'w', encoding='utf-8') as f:
            f.write(report)
        print("📄 详细报告已保存到 import_test_report.md")
    except Exception as e:
        print(f"⚠️ 无法保存报告: {e}")
    
    if summary['failed_tests'] == 0:
        print("🎉 所有导入测试通过！目录重组成功！")
        return True
    else:
        print("⚠️ 存在导入问题，需要进一步修复。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)