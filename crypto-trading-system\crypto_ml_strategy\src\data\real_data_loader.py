#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
真实数据加载器模块

负责从各种数据源加载真实的市场数据，用于模型训练和回测。
支持从InfluxDB、MySQL、外部API等多种数据源加载历史数据。
"""

import logging
from typing import Dict, List, Optional, Any, Union, Tuple
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
from loguru import logger

from .clients.influxdb_client import InfluxDBClient
from .clients.mysql_client import MySQLClient
from core.config import Config


class RealDataLoader:
    """
    真实数据加载器类，负责从多种数据源加载市场数据。
    
    支持功能：
    - 多数据源整合（InfluxDB、MySQL、API）
    - 历史数据批量加载
    - 数据质量检查和清洗
    - 多时间周期数据对齐
    - 特征工程预处理
    - 数据缓存和增量更新
    """
    
    def __init__(self, influxdb_client: InfluxDBClient, mysql_client: MySQLClient, config: Config):
        """
        初始化数据加载器
        
        Args:
            influxdb_client: InfluxDB客户端
            mysql_client: MySQL客户端
            config: 配置对象
        """
        self.logger = logger.bind(component="RealDataLoader")
        
        self.influxdb_client = influxdb_client
        self.mysql_client = mysql_client
        self.config = config
        
        # 从配置获取参数
        self.symbols = config.get_list('strategy', 'symbols')
        self.timeframes = config.get_list('strategy', 'timeframes')
        self.train_data_days = config.get_int('training', 'train_data_days', 90)
        
        # 数据缓存
        self._data_cache = {}
        self._last_update = {}
        
        self.logger.info(f"数据加载器初始化完成，支持交易对: {self.symbols}, 时间周期: {self.timeframes}")
    
    def load_training_data(self, symbols: Optional[List[str]] = None, 
                          timeframes: Optional[List[str]] = None,
                          days: Optional[int] = None) -> Dict[str, pd.DataFrame]:
        """
        加载训练数据
        
        Args:
            symbols: 交易对列表，默认使用配置中的symbols
            timeframes: 时间周期列表，默认使用配置中的timeframes
            days: 数据天数，默认使用配置中的train_data_days
        
        Returns:
            训练数据字典，格式: {symbol_timeframe: DataFrame}
        """
        try:
            symbols = symbols or self.symbols
            timeframes = timeframes or self.timeframes
            days = days or self.train_data_days
            
            end_time = datetime.now()
            start_time = end_time - timedelta(days=days)
            
            training_data = {}
            
            self.logger.info(f"开始加载训练数据: {start_time} 到 {end_time}")
            
            for symbol in symbols:
                for timeframe in timeframes:
                    key = f"{symbol}_{timeframe}"
                    
                    # 加载K线数据
                    kline_data = self._load_kline_data(symbol, timeframe, start_time, end_time)
                    
                    if kline_data is not None and not kline_data.empty:
                        # 数据质量检查
                        cleaned_data = self._clean_data(kline_data)
                        
                        # 特征工程
                        feature_data = self._engineer_features(cleaned_data, symbol, timeframe)
                        
                        if feature_data is not None and not feature_data.empty:
                            training_data[key] = feature_data
                            self.logger.info(f"加载训练数据成功: {key}, 记录数: {len(feature_data)}")
                        else:
                            self.logger.warning(f"特征工程失败: {key}")
                    else:
                        self.logger.warning(f"未找到K线数据: {key}")
            
            # 数据对齐和合并
            if training_data:
                aligned_data = self._align_multi_timeframe_data(training_data)
                self.logger.info(f"训练数据加载完成，总数据集: {len(aligned_data)}")
                return aligned_data
            else:
                self.logger.error("未加载到任何训练数据")
                return {}
            
        except Exception as e:
            self.logger.error(f"加载训练数据失败: {str(e)}")
            return {}
    
    def _load_kline_data(self, symbol: str, timeframe: str, 
                        start_time: datetime, end_time: datetime) -> Optional[pd.DataFrame]:
        """
        从InfluxDB加载K线数据
        
        Args:
            symbol: 交易对
            timeframe: 时间周期
            start_time: 开始时间
            end_time: 结束时间
        
        Returns:
            K线数据DataFrame
        """
        try:
            # 首先尝试从InfluxDB加载
            kline_data = self.influxdb_client.query_kline_data(symbol, timeframe, start_time, end_time)
            
            if kline_data is not None and not kline_data.empty:
                return kline_data
            
            # 如果InfluxDB没有数据，尝试从其他数据源加载
            self.logger.warning(f"InfluxDB中未找到数据，尝试其他数据源: {symbol} {timeframe}")
            
            # 这里可以添加其他数据源的加载逻辑
            # 例如：从外部API、文件等加载数据
            
            return None
            
        except Exception as e:
            self.logger.error(f"加载K线数据失败: {str(e)}")
            return None
    
    def _clean_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        数据清洗
        
        Args:
            data: 原始数据
        
        Returns:
            清洗后的数据
        """
        try:
            if data.empty:
                return data
            
            # 复制数据避免修改原始数据
            cleaned_data = data.copy()
            
            # 删除重复行
            cleaned_data = cleaned_data.drop_duplicates()
            
            # 处理缺失值
            cleaned_data = cleaned_data.fillna(method='ffill').fillna(method='bfill')
            
            # 删除异常值（使用IQR方法）
            numeric_columns = cleaned_data.select_dtypes(include=[np.number]).columns
            
            for col in numeric_columns:
                if col in ['open', 'high', 'low', 'close', 'volume']:
                    Q1 = cleaned_data[col].quantile(0.25)
                    Q3 = cleaned_data[col].quantile(0.75)
                    IQR = Q3 - Q1
                    
                    # 定义异常值边界
                    lower_bound = Q1 - 1.5 * IQR
                    upper_bound = Q3 + 1.5 * IQR
                    
                    # 替换异常值为边界值
                    cleaned_data[col] = cleaned_data[col].clip(lower=lower_bound, upper=upper_bound)
            
            # 确保价格数据的逻辑一致性
            if all(col in cleaned_data.columns for col in ['open', 'high', 'low', 'close']):
                # high应该是最高价
                cleaned_data['high'] = cleaned_data[['open', 'high', 'low', 'close']].max(axis=1)
                # low应该是最低价
                cleaned_data['low'] = cleaned_data[['open', 'high', 'low', 'close']].min(axis=1)
            
            self.logger.debug(f"数据清洗完成，原始记录: {len(data)}, 清洗后记录: {len(cleaned_data)}")
            return cleaned_data
            
        except Exception as e:
            self.logger.error(f"数据清洗失败: {str(e)}")
            return data
    
    def _engineer_features(self, data: pd.DataFrame, symbol: str, timeframe: str) -> Optional[pd.DataFrame]:
        """
        特征工程
        
        Args:
            data: 清洗后的数据
            symbol: 交易对
            timeframe: 时间周期
        
        Returns:
            特征工程后的数据
        """
        try:
            if data.empty:
                return None
            
            # 复制数据
            feature_data = data.copy()
            
            # 基础价格特征
            if all(col in feature_data.columns for col in ['open', 'high', 'low', 'close']):
                # 价格变化
                feature_data['price_change'] = feature_data['close'] - feature_data['open']
                feature_data['price_change_pct'] = feature_data['price_change'] / feature_data['open']
                
                # 价格范围
                feature_data['price_range'] = feature_data['high'] - feature_data['low']
                feature_data['price_range_pct'] = feature_data['price_range'] / feature_data['open']
                
                # 上下影线
                feature_data['upper_shadow'] = feature_data['high'] - feature_data[['open', 'close']].max(axis=1)
                feature_data['lower_shadow'] = feature_data[['open', 'close']].min(axis=1) - feature_data['low']
                
                # 实体大小
                feature_data['body_size'] = abs(feature_data['close'] - feature_data['open'])
                feature_data['body_size_pct'] = feature_data['body_size'] / feature_data['open']
            
            # 技术指标
            if 'close' in feature_data.columns:
                # 移动平均线
                for period in [5, 10, 20, 50]:
                    feature_data[f'ma_{period}'] = feature_data['close'].rolling(window=period).mean()
                    feature_data[f'ma_{period}_ratio'] = feature_data['close'] / feature_data[f'ma_{period}']
                
                # RSI
                delta = feature_data['close'].diff()
                gain = delta.where(delta > 0, 0)
                loss = -delta.where(delta < 0, 0)
                avg_gain = gain.rolling(window=14).mean()
                avg_loss = loss.rolling(window=14).mean()
                rs = avg_gain / avg_loss
                feature_data['rsi'] = 100 - (100 / (1 + rs))
                
                # 布林带
                feature_data['bb_middle'] = feature_data['close'].rolling(window=20).mean()
                bb_std = feature_data['close'].rolling(window=20).std()
                feature_data['bb_upper'] = feature_data['bb_middle'] + (bb_std * 2)
                feature_data['bb_lower'] = feature_data['bb_middle'] - (bb_std * 2)
                feature_data['bb_position'] = (feature_data['close'] - feature_data['bb_lower']) / (feature_data['bb_upper'] - feature_data['bb_lower'])
                
                # MACD
                exp1 = feature_data['close'].ewm(span=12, adjust=False).mean()
                exp2 = feature_data['close'].ewm(span=26, adjust=False).mean()
                feature_data['macd'] = exp1 - exp2
                feature_data['macd_signal'] = feature_data['macd'].ewm(span=9, adjust=False).mean()
                feature_data['macd_histogram'] = feature_data['macd'] - feature_data['macd_signal']
            
            # 成交量特征
            if 'volume' in feature_data.columns:
                # 成交量移动平均
                feature_data['volume_ma_10'] = feature_data['volume'].rolling(window=10).mean()
                feature_data['volume_ratio'] = feature_data['volume'] / feature_data['volume_ma_10']
                
                # 成交量价格趋势（VPT）
                if 'close' in feature_data.columns:
                    feature_data['vpt'] = (feature_data['volume'] * feature_data['price_change_pct']).cumsum()
            
            # 时间特征
            if '_time' in feature_data.columns:
                feature_data['hour'] = pd.to_datetime(feature_data['_time']).dt.hour
                feature_data['day_of_week'] = pd.to_datetime(feature_data['_time']).dt.dayofweek
                feature_data['day_of_month'] = pd.to_datetime(feature_data['_time']).dt.day
            
            # 标签生成（用于监督学习）
            if 'close' in feature_data.columns:
                # 未来价格变化（预测目标）
                feature_data['future_return_1'] = feature_data['close'].shift(-1) / feature_data['close'] - 1
                feature_data['future_return_5'] = feature_data['close'].shift(-5) / feature_data['close'] - 1
                
                # 分类标签
                feature_data['signal_1'] = np.where(feature_data['future_return_1'] > 0.01, 1,
                                                   np.where(feature_data['future_return_1'] < -0.01, -1, 0))
            
            # 删除包含NaN的行
            feature_data = feature_data.dropna()
            
            self.logger.debug(f"特征工程完成: {symbol} {timeframe}, 特征数: {len(feature_data.columns)}")
            return feature_data
            
        except Exception as e:
            self.logger.error(f"特征工程失败: {str(e)}")
            return None
    
    def _align_multi_timeframe_data(self, data_dict: Dict[str, pd.DataFrame]) -> Dict[str, pd.DataFrame]:
        """
        多时间周期数据对齐
        
        Args:
            data_dict: 多时间周期数据字典
        
        Returns:
            对齐后的数据字典
        """
        try:
            aligned_data = {}
            
            # 按交易对分组
            symbols = set(key.split('_')[0] for key in data_dict.keys())
            
            for symbol in symbols:
                symbol_data = {key: df for key, df in data_dict.items() if key.startswith(symbol)}
                
                if not symbol_data:
                    continue
                
                # 选择基准时间周期（通常选择最小的时间周期）
                base_key = min(symbol_data.keys(), key=lambda x: self._timeframe_to_minutes(x.split('_')[1]))
                base_data = symbol_data[base_key].copy()
                
                # 将其他时间周期的数据重采样到基准时间周期
                for key, df in symbol_data.items():
                    if key == base_key:
                        continue
                    
                    timeframe = key.split('_')[1]
                    
                    # 重采样关键特征
                    resampled_features = ['close', 'volume', 'rsi', 'macd', 'bb_position']
                    
                    for feature in resampled_features:
                        if feature in df.columns:
                            # 使用前向填充进行重采样
                            resampled_col = f"{feature}_{timeframe}"
                            
                            # 简单的时间对齐（实际应用中可能需要更复杂的对齐逻辑）
                            if '_time' in base_data.columns and '_time' in df.columns:
                                base_data[resampled_col] = np.nan
                                
                                for idx, row in base_data.iterrows():
                                    base_time = row['_time']
                                    # 找到最近的时间点
                                    time_diff = abs(df['_time'] - base_time)
                                    nearest_idx = time_diff.idxmin()
                                    
                                    if time_diff[nearest_idx] < pd.Timedelta(hours=1):  # 1小时内的数据才使用
                                        base_data.at[idx, resampled_col] = df.at[nearest_idx, feature]
                
                # 删除包含过多NaN的行
                base_data = base_data.dropna(thresh=len(base_data.columns) * 0.8)
                
                aligned_data[symbol] = base_data
            
            self.logger.info(f"多时间周期数据对齐完成，交易对数: {len(aligned_data)}")
            return aligned_data
            
        except Exception as e:
            self.logger.error(f"多时间周期数据对齐失败: {str(e)}")
            return data_dict
    
    def _timeframe_to_minutes(self, timeframe: str) -> int:
        """
        将时间周期转换为分钟数
        
        Args:
            timeframe: 时间周期字符串
        
        Returns:
            分钟数
        """
        timeframe_map = {
            '1m': 1,
            '5m': 5,
            '15m': 15,
            '1h': 60,
            '4h': 240,
            '1d': 1440
        }
        return timeframe_map.get(timeframe, 60)
    
    def load_latest_data(self, symbol: str, timeframe: str, limit: int = 100) -> Optional[pd.DataFrame]:
        """
        加载最新数据
        
        Args:
            symbol: 交易对
            timeframe: 时间周期
            limit: 数据条数限制
        
        Returns:
            最新数据DataFrame
        """
        try:
            # 从InfluxDB获取最新数据
            latest_data = self.influxdb_client.query_latest_kline(symbol, timeframe, limit)
            
            if latest_data is not None and not latest_data.empty:
                # 数据清洗和特征工程
                cleaned_data = self._clean_data(latest_data)
                feature_data = self._engineer_features(cleaned_data, symbol, timeframe)
                
                return feature_data
            
            return None
            
        except Exception as e:
            self.logger.error(f"加载最新数据失败: {str(e)}")
            return None
    
    def get_data_statistics(self, data: pd.DataFrame) -> Dict[str, Any]:
        """
        获取数据统计信息
        
        Args:
            data: 数据DataFrame
        
        Returns:
            统计信息字典
        """
        try:
            if data.empty:
                return {}
            
            stats = {
                'total_records': len(data),
                'date_range': {
                    'start': data.index.min() if hasattr(data.index, 'min') else None,
                    'end': data.index.max() if hasattr(data.index, 'max') else None
                },
                'missing_values': data.isnull().sum().to_dict(),
                'numeric_stats': data.describe().to_dict() if len(data.select_dtypes(include=[np.number]).columns) > 0 else {}
            }
            
            return stats
            
        except Exception as e:
            self.logger.error(f"获取数据统计信息失败: {str(e)}")
            return {}
    
    def validate_data_quality(self, data: pd.DataFrame) -> Dict[str, Any]:
        """
        验证数据质量
        
        Args:
            data: 数据DataFrame
        
        Returns:
            数据质量报告
        """
        try:
            quality_report = {
                'is_valid': True,
                'issues': [],
                'warnings': [],
                'statistics': {}
            }
            
            if data.empty:
                quality_report['is_valid'] = False
                quality_report['issues'].append("数据为空")
                return quality_report
            
            # 检查缺失值
            missing_ratio = data.isnull().sum() / len(data)
            high_missing_cols = missing_ratio[missing_ratio > 0.1].index.tolist()
            
            if high_missing_cols:
                quality_report['warnings'].append(f"高缺失值列: {high_missing_cols}")
            
            # 检查重复行
            duplicate_count = data.duplicated().sum()
            if duplicate_count > 0:
                quality_report['warnings'].append(f"重复行数: {duplicate_count}")
            
            # 检查数据范围
            if 'close' in data.columns:
                if (data['close'] <= 0).any():
                    quality_report['issues'].append("存在非正价格数据")
                    quality_report['is_valid'] = False
            
            # 统计信息
            quality_report['statistics'] = self.get_data_statistics(data)
            
            return quality_report
            
        except Exception as e:
            self.logger.error(f"数据质量验证失败: {str(e)}")
            return {'is_valid': False, 'issues': [str(e)], 'warnings': [], 'statistics': {}}