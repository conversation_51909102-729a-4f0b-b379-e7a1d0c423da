"""
启动健康检查模块

提供crypto_ml_strategy项目的启动健康检查功能，包括系统就绪验证、
启动后验证、健康状态监控和问题诊断。

Author: Crypto ML Strategy Team
Date: 2024-12-20
Version: 1.0.0
"""

import asyncio
import time
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Dict, List, Optional, Callable, Set
from loguru import logger

from .startup_sequence_core import StartupPhase, StartupResult
from .error_handler_core import (
    CryptoMLException, ErrorCode, ErrorSeverity, ErrorCategory,
    ErrorContext, handle_exception
)


class HealthCheckType(Enum):
    """健康检查类型枚举"""
    SYSTEM_READINESS = "system_readiness"
    SERVICE_AVAILABILITY = "service_availability"
    PERFORMANCE_VALIDATION = "performance_validation"
    INTEGRATION_TEST = "integration_test"
    SMOKE_TEST = "smoke_test"
    STRESS_TEST = "stress_test"


class HealthStatus(Enum):
    """健康状态枚举"""
    HEALTHY = "healthy"
    WARNING = "warning"
    CRITICAL = "critical"
    UNKNOWN = "unknown"


@dataclass
class HealthCheckResult:
    """健康检查结果"""
    check_name: str
    check_type: HealthCheckType
    status: HealthStatus
    message: str
    duration: float = 0.0
    details: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)
    
    def is_healthy(self) -> bool:
        """判断是否健康"""
        return self.status == HealthStatus.HEALTHY
    
    def needs_attention(self) -> bool:
        """判断是否需要关注"""
        return self.status in [HealthStatus.WARNING, HealthStatus.CRITICAL]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "check_name": self.check_name,
            "check_type": self.check_type.value,
            "status": self.status.value,
            "message": self.message,
            "duration": self.duration,
            "details": self.details,
            "timestamp": self.timestamp.isoformat()
        }


class StartupHealthCheck(ABC):
    """启动健康检查抽象基类"""
    
    @abstractmethod
    async def check(self) -> HealthCheckResult:
        """执行健康检查"""
        pass
    
    @abstractmethod
    def get_name(self) -> str:
        """获取检查名称"""
        pass
    
    @abstractmethod
    def get_type(self) -> HealthCheckType:
        """获取检查类型"""
        pass
    
    @abstractmethod
    def get_timeout(self) -> float:
        """获取检查超时时间"""
        pass
    
    @abstractmethod
    def is_critical(self) -> bool:
        """判断是否为关键检查"""
        pass


class SystemReadinessCheck(StartupHealthCheck):
    """系统就绪检查"""
    
    def __init__(self, required_services: List[str] = None):
        self.required_services = required_services or []
    
    async def check(self) -> HealthCheckResult:
        """检查系统就绪状态"""
        start_time = time.time()
        
        try:
            issues = []
            details = {}
            
            # 检查系统资源
            resource_check = await self._check_system_resources()
            details["system_resources"] = resource_check
            if not resource_check["healthy"]:
                issues.extend(resource_check["issues"])
            
            # 检查必需服务
            if self.required_services:
                service_check = await self._check_required_services()
                details["required_services"] = service_check
                if not service_check["all_available"]:
                    issues.extend(service_check["unavailable_services"])
            
            # 检查网络连接
            network_check = await self._check_network_connectivity()
            details["network"] = network_check
            if not network_check["connected"]:
                issues.append("网络连接不可用")
            
            # 确定健康状态
            if not issues:
                status = HealthStatus.HEALTHY
                message = "系统就绪检查通过"
            elif len(issues) <= 2:
                status = HealthStatus.WARNING
                message = f"系统就绪检查发现 {len(issues)} 个警告"
            else:
                status = HealthStatus.CRITICAL
                message = f"系统就绪检查发现 {len(issues)} 个严重问题"
            
            duration = time.time() - start_time
            
            return HealthCheckResult(
                check_name="system_readiness",
                check_type=HealthCheckType.SYSTEM_READINESS,
                status=status,
                message=message,
                duration=duration,
                details=details
            )
            
        except Exception as e:
            duration = time.time() - start_time
            logger.error(f"系统就绪检查异常: {e}")
            
            return HealthCheckResult(
                check_name="system_readiness",
                check_type=HealthCheckType.SYSTEM_READINESS,
                status=HealthStatus.UNKNOWN,
                message=f"检查异常: {str(e)}",
                duration=duration,
                details={"error": str(e)}
            )
    
    async def _check_system_resources(self) -> Dict[str, Any]:
        """检查系统资源"""
        try:
            import psutil
            
            # 检查内存
            memory = psutil.virtual_memory()
            memory_available_gb = memory.available / (1024 ** 3)
            
            # 检查磁盘空间
            disk = psutil.disk_usage('/')
            disk_free_gb = disk.free / (1024 ** 3)
            
            # 检查CPU
            cpu_count = psutil.cpu_count()
            cpu_usage = psutil.cpu_percent(interval=1)
            
            issues = []
            if memory_available_gb < 0.5:  # 少于500MB可用内存
                issues.append(f"可用内存不足: {memory_available_gb:.1f}GB")
            
            if disk_free_gb < 1.0:  # 少于1GB可用磁盘空间
                issues.append(f"可用磁盘空间不足: {disk_free_gb:.1f}GB")
            
            if cpu_usage > 90:  # CPU使用率超过90%
                issues.append(f"CPU使用率过高: {cpu_usage:.1f}%")
            
            return {
                "healthy": len(issues) == 0,
                "issues": issues,
                "memory_available_gb": memory_available_gb,
                "disk_free_gb": disk_free_gb,
                "cpu_count": cpu_count,
                "cpu_usage": cpu_usage
            }
            
        except Exception as e:
            return {
                "healthy": False,
                "issues": [f"资源检查异常: {str(e)}"],
                "error": str(e)
            }
    
    async def _check_required_services(self) -> Dict[str, Any]:
        """检查必需服务"""
        available_services = []
        unavailable_services = []
        
        for service in self.required_services:
            try:
                # 这里应该实现具体的服务检查逻辑
                # 暂时模拟检查结果
                if service in ["logger", "config", "error_handler"]:
                    available_services.append(service)
                else:
                    unavailable_services.append(f"服务 {service} 不可用")
            except Exception as e:
                unavailable_services.append(f"服务 {service} 检查失败: {str(e)}")
        
        return {
            "all_available": len(unavailable_services) == 0,
            "available_services": available_services,
            "unavailable_services": unavailable_services,
            "total_services": len(self.required_services)
        }
    
    async def _check_network_connectivity(self) -> Dict[str, Any]:
        """检查网络连接"""
        try:
            # 尝试连接到公共DNS服务器
            reader, writer = await asyncio.wait_for(
                asyncio.open_connection("*******", 53),
                timeout=3.0
            )
            writer.close()
            await writer.wait_closed()
            
            return {"connected": True, "test_host": "*******:53"}
            
        except Exception as e:
            return {
                "connected": False,
                "error": str(e),
                "test_host": "*******:53"
            }
    
    def get_name(self) -> str:
        return "system_readiness"
    
    def get_type(self) -> HealthCheckType:
        return HealthCheckType.SYSTEM_READINESS
    
    def get_timeout(self) -> float:
        return 10.0
    
    def is_critical(self) -> bool:
        return True


class ServiceAvailabilityCheck(StartupHealthCheck):
    """服务可用性检查"""
    
    def __init__(self, service_endpoints: Dict[str, str]):
        self.service_endpoints = service_endpoints
    
    async def check(self) -> HealthCheckResult:
        """检查服务可用性"""
        start_time = time.time()
        
        try:
            available_services = []
            unavailable_services = []
            service_details = {}
            
            for service_name, endpoint in self.service_endpoints.items():
                try:
                    check_result = await self._check_service_endpoint(service_name, endpoint)
                    service_details[service_name] = check_result
                    
                    if check_result["available"]:
                        available_services.append(service_name)
                    else:
                        unavailable_services.append(service_name)
                        
                except Exception as e:
                    unavailable_services.append(service_name)
                    service_details[service_name] = {
                        "available": False,
                        "error": str(e)
                    }
            
            # 确定健康状态
            total_services = len(self.service_endpoints)
            available_count = len(available_services)
            
            if available_count == total_services:
                status = HealthStatus.HEALTHY
                message = f"所有服务可用 ({available_count}/{total_services})"
            elif available_count >= total_services * 0.8:
                status = HealthStatus.WARNING
                message = f"部分服务不可用 ({available_count}/{total_services})"
            else:
                status = HealthStatus.CRITICAL
                message = f"多数服务不可用 ({available_count}/{total_services})"
            
            duration = time.time() - start_time
            
            return HealthCheckResult(
                check_name="service_availability",
                check_type=HealthCheckType.SERVICE_AVAILABILITY,
                status=status,
                message=message,
                duration=duration,
                details={
                    "total_services": total_services,
                    "available_services": available_services,
                    "unavailable_services": unavailable_services,
                    "service_details": service_details
                }
            )
            
        except Exception as e:
            duration = time.time() - start_time
            logger.error(f"服务可用性检查异常: {e}")
            
            return HealthCheckResult(
                check_name="service_availability",
                check_type=HealthCheckType.SERVICE_AVAILABILITY,
                status=HealthStatus.UNKNOWN,
                message=f"检查异常: {str(e)}",
                duration=duration,
                details={"error": str(e)}
            )
    
    async def _check_service_endpoint(self, service_name: str, endpoint: str) -> Dict[str, Any]:
        """检查服务端点"""
        try:
            # 解析端点
            if "://" in endpoint:
                # HTTP端点
                return await self._check_http_endpoint(endpoint)
            elif ":" in endpoint:
                # TCP端点
                host, port = endpoint.split(":")
                return await self._check_tcp_endpoint(host, int(port))
            else:
                return {"available": False, "error": "无效的端点格式"}
                
        except Exception as e:
            return {"available": False, "error": str(e)}
    
    async def _check_http_endpoint(self, url: str) -> Dict[str, Any]:
        """检查HTTP端点"""
        try:
            import aiohttp
            
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=5.0)) as session:
                async with session.get(url) as response:
                    return {
                        "available": response.status < 500,
                        "status_code": response.status,
                        "response_time": response.headers.get("X-Response-Time", "unknown")
                    }
                    
        except Exception as e:
            return {"available": False, "error": str(e)}
    
    async def _check_tcp_endpoint(self, host: str, port: int) -> Dict[str, Any]:
        """检查TCP端点"""
        try:
            start_time = time.time()
            reader, writer = await asyncio.wait_for(
                asyncio.open_connection(host, port),
                timeout=5.0
            )
            
            writer.close()
            await writer.wait_closed()
            
            response_time = (time.time() - start_time) * 1000
            
            return {
                "available": True,
                "response_time_ms": response_time
            }
            
        except Exception as e:
            return {"available": False, "error": str(e)}
    
    def get_name(self) -> str:
        return "service_availability"
    
    def get_type(self) -> HealthCheckType:
        return HealthCheckType.SERVICE_AVAILABILITY
    
    def get_timeout(self) -> float:
        return 15.0
    
    def is_critical(self) -> bool:
        return True


class PerformanceValidationCheck(StartupHealthCheck):
    """性能验证检查"""
    
    def __init__(
        self,
        max_startup_time: float = 10.0,
        max_memory_usage_mb: float = 2048.0,
        min_cpu_cores: int = 1
    ):
        self.max_startup_time = max_startup_time
        self.max_memory_usage_mb = max_memory_usage_mb
        self.min_cpu_cores = min_cpu_cores
    
    async def check(self) -> HealthCheckResult:
        """检查性能指标"""
        start_time = time.time()
        
        try:
            issues = []
            details = {}
            
            # 检查启动时间（需要从外部传入）
            # 这里暂时模拟
            startup_time = 8.5  # 模拟启动时间
            details["startup_time"] = startup_time
            
            if startup_time > self.max_startup_time:
                issues.append(f"启动时间过长: {startup_time:.1f}s > {self.max_startup_time}s")
            
            # 检查内存使用
            import psutil
            memory = psutil.virtual_memory()
            memory_used_mb = (memory.total - memory.available) / (1024 * 1024)
            details["memory_usage_mb"] = memory_used_mb
            
            if memory_used_mb > self.max_memory_usage_mb:
                issues.append(f"内存使用过高: {memory_used_mb:.0f}MB > {self.max_memory_usage_mb}MB")
            
            # 检查CPU核心数
            cpu_cores = psutil.cpu_count()
            details["cpu_cores"] = cpu_cores
            
            if cpu_cores < self.min_cpu_cores:
                issues.append(f"CPU核心数不足: {cpu_cores} < {self.min_cpu_cores}")
            
            # 检查CPU性能
            cpu_usage = psutil.cpu_percent(interval=1)
            details["cpu_usage"] = cpu_usage
            
            if cpu_usage > 95:
                issues.append(f"CPU使用率过高: {cpu_usage:.1f}%")
            
            # 确定健康状态
            if not issues:
                status = HealthStatus.HEALTHY
                message = "性能验证通过"
            elif len(issues) == 1:
                status = HealthStatus.WARNING
                message = f"性能验证发现1个问题: {issues[0]}"
            else:
                status = HealthStatus.CRITICAL
                message = f"性能验证发现{len(issues)}个问题"
            
            duration = time.time() - start_time
            
            return HealthCheckResult(
                check_name="performance_validation",
                check_type=HealthCheckType.PERFORMANCE_VALIDATION,
                status=status,
                message=message,
                duration=duration,
                details=details
            )
            
        except Exception as e:
            duration = time.time() - start_time
            logger.error(f"性能验证检查异常: {e}")
            
            return HealthCheckResult(
                check_name="performance_validation",
                check_type=HealthCheckType.PERFORMANCE_VALIDATION,
                status=HealthStatus.UNKNOWN,
                message=f"检查异常: {str(e)}",
                duration=duration,
                details={"error": str(e)}
            )
    
    def get_name(self) -> str:
        return "performance_validation"
    
    def get_type(self) -> HealthCheckType:
        return HealthCheckType.PERFORMANCE_VALIDATION
    
    def get_timeout(self) -> float:
        return 10.0
    
    def is_critical(self) -> bool:
        return False


class SmokeTestCheck(StartupHealthCheck):
    """冒烟测试检查"""
    
    def __init__(self, test_functions: List[Callable] = None):
        self.test_functions = test_functions or []
    
    async def check(self) -> HealthCheckResult:
        """执行冒烟测试"""
        start_time = time.time()
        
        try:
            passed_tests = []
            failed_tests = []
            test_details = {}
            
            for i, test_func in enumerate(self.test_functions):
                test_name = getattr(test_func, '__name__', f'test_{i}')
                
                try:
                    test_start = time.time()
                    
                    if asyncio.iscoroutinefunction(test_func):
                        result = await test_func()
                    else:
                        result = test_func()
                    
                    test_duration = time.time() - test_start
                    
                    if result:
                        passed_tests.append(test_name)
                        test_details[test_name] = {
                            "passed": True,
                            "duration": test_duration
                        }
                    else:
                        failed_tests.append(test_name)
                        test_details[test_name] = {
                            "passed": False,
                            "duration": test_duration,
                            "error": "测试返回False"
                        }
                        
                except Exception as e:
                    failed_tests.append(test_name)
                    test_details[test_name] = {
                        "passed": False,
                        "duration": time.time() - test_start,
                        "error": str(e)
                    }
            
            # 确定健康状态
            total_tests = len(self.test_functions)
            passed_count = len(passed_tests)
            
            if total_tests == 0:
                status = HealthStatus.HEALTHY
                message = "没有配置冒烟测试"
            elif passed_count == total_tests:
                status = HealthStatus.HEALTHY
                message = f"所有冒烟测试通过 ({passed_count}/{total_tests})"
            elif passed_count >= total_tests * 0.8:
                status = HealthStatus.WARNING
                message = f"部分冒烟测试失败 ({passed_count}/{total_tests})"
            else:
                status = HealthStatus.CRITICAL
                message = f"多数冒烟测试失败 ({passed_count}/{total_tests})"
            
            duration = time.time() - start_time
            
            return HealthCheckResult(
                check_name="smoke_test",
                check_type=HealthCheckType.SMOKE_TEST,
                status=status,
                message=message,
                duration=duration,
                details={
                    "total_tests": total_tests,
                    "passed_tests": passed_tests,
                    "failed_tests": failed_tests,
                    "test_details": test_details
                }
            )
            
        except Exception as e:
            duration = time.time() - start_time
            logger.error(f"冒烟测试检查异常: {e}")
            
            return HealthCheckResult(
                check_name="smoke_test",
                check_type=HealthCheckType.SMOKE_TEST,
                status=HealthStatus.UNKNOWN,
                message=f"检查异常: {str(e)}",
                duration=duration,
                details={"error": str(e)}
            )
    
    def get_name(self) -> str:
        return "smoke_test"
    
    def get_type(self) -> HealthCheckType:
        return HealthCheckType.SMOKE_TEST
    
    def get_timeout(self) -> float:
        return 30.0
    
    def is_critical(self) -> bool:
        return False


class StartupHealthChecker:
    """启动健康检查器主类"""
    
    def __init__(self):
        self.health_checks: List[StartupHealthCheck] = []
        self.results: Dict[str, HealthCheckResult] = {}
        self.last_check_time: Optional[datetime] = None
    
    def add_health_check(self, health_check: StartupHealthCheck) -> None:
        """添加健康检查"""
        self.health_checks.append(health_check)
        logger.debug(f"添加健康检查: {health_check.get_name()}")
    
    def add_system_readiness_check(self, **kwargs) -> None:
        """添加系统就绪检查"""
        check = SystemReadinessCheck(**kwargs)
        self.add_health_check(check)
    
    def add_service_availability_check(self, service_endpoints: Dict[str, str]) -> None:
        """添加服务可用性检查"""
        check = ServiceAvailabilityCheck(service_endpoints)
        self.add_health_check(check)
    
    def add_performance_validation_check(self, **kwargs) -> None:
        """添加性能验证检查"""
        check = PerformanceValidationCheck(**kwargs)
        self.add_health_check(check)
    
    def add_smoke_test_check(self, test_functions: List[Callable]) -> None:
        """添加冒烟测试检查"""
        check = SmokeTestCheck(test_functions)
        self.add_health_check(check)
    
    @handle_exception(component="startup_health_checker", operation="run_all_checks")
    async def run_all_checks(self, parallel: bool = True) -> Dict[str, HealthCheckResult]:
        """运行所有健康检查"""
        logger.info(f"开始启动健康检查 ({len(self.health_checks)} 个检查)")
        
        self.last_check_time = datetime.now()
        
        if parallel:
            # 并行执行检查
            results = await asyncio.gather(
                *[self._run_check_with_timeout(check) for check in self.health_checks],
                return_exceptions=True
            )
            
            for check, result in zip(self.health_checks, results):
                if isinstance(result, Exception):
                    self.results[check.get_name()] = HealthCheckResult(
                        check_name=check.get_name(),
                        check_type=check.get_type(),
                        status=HealthStatus.UNKNOWN,
                        message=f"检查异常: {str(result)}",
                        details={"error": str(result)}
                    )
                else:
                    self.results[check.get_name()] = result
        else:
            # 串行执行检查
            for check in self.health_checks:
                try:
                    result = await self._run_check_with_timeout(check)
                    self.results[check.get_name()] = result
                except Exception as e:
                    self.results[check.get_name()] = HealthCheckResult(
                        check_name=check.get_name(),
                        check_type=check.get_type(),
                        status=HealthStatus.UNKNOWN,
                        message=f"检查异常: {str(e)}",
                        details={"error": str(e)}
                    )
        
        # 统计结果
        total_checks = len(self.results)
        healthy_checks = len([r for r in self.results.values() if r.is_healthy()])
        critical_issues = len([r for r in self.results.values() if r.status == HealthStatus.CRITICAL])
        
        logger.info(f"启动健康检查完成: {healthy_checks}/{total_checks} 健康, {critical_issues} 个严重问题")
        
        return self.results
    
    async def _run_check_with_timeout(self, check: StartupHealthCheck) -> HealthCheckResult:
        """带超时的健康检查执行"""
        try:
            return await asyncio.wait_for(
                check.check(),
                timeout=check.get_timeout()
            )
        except asyncio.TimeoutError:
            return HealthCheckResult(
                check_name=check.get_name(),
                check_type=check.get_type(),
                status=HealthStatus.CRITICAL,
                message=f"健康检查超时 ({check.get_timeout()}s)",
                details={"timeout": check.get_timeout()}
            )
    
    def get_health_summary(self) -> Dict[str, Any]:
        """获取健康摘要"""
        if not self.results:
            return {"status": "no_checks_performed"}
        
        total_checks = len(self.results)
        healthy_checks = len([r for r in self.results.values() if r.is_healthy()])
        warning_checks = len([r for r in self.results.values() if r.status == HealthStatus.WARNING])
        critical_checks = len([r for r in self.results.values() if r.status == HealthStatus.CRITICAL])
        unknown_checks = len([r for r in self.results.values() if r.status == HealthStatus.UNKNOWN])
        
        # 确定整体健康状态
        if critical_checks > 0:
            overall_status = "critical"
        elif warning_checks > 0:
            overall_status = "warning"
        elif unknown_checks > 0:
            overall_status = "unknown"
        else:
            overall_status = "healthy"
        
        return {
            "overall_status": overall_status,
            "total_checks": total_checks,
            "healthy_checks": healthy_checks,
            "warning_checks": warning_checks,
            "critical_checks": critical_checks,
            "unknown_checks": unknown_checks,
            "health_percentage": (healthy_checks / total_checks * 100) if total_checks > 0 else 0,
            "last_check_time": self.last_check_time.isoformat() if self.last_check_time else None,
            "critical_issues": [
                {"name": r.check_name, "message": r.message}
                for r in self.results.values()
                if r.status == HealthStatus.CRITICAL
            ]
        }
    
    def get_detailed_report(self) -> Dict[str, Any]:
        """获取详细报告"""
        return {
            "summary": self.get_health_summary(),
            "check_results": {
                name: result.to_dict()
                for name, result in self.results.items()
            },
            "recommendations": self._generate_recommendations()
        }
    
    def _generate_recommendations(self) -> List[str]:
        """生成改进建议"""
        recommendations = []
        
        for result in self.results.values():
            if result.status == HealthStatus.CRITICAL:
                recommendations.append(f"立即修复 {result.check_name}: {result.message}")
            elif result.status == HealthStatus.WARNING:
                recommendations.append(f"关注 {result.check_name}: {result.message}")
        
        # 通用建议
        critical_count = len([r for r in self.results.values() if r.status == HealthStatus.CRITICAL])
        if critical_count > 0:
            recommendations.append("建议在生产环境部署前解决所有严重问题")
        
        warning_count = len([r for r in self.results.values() if r.status == HealthStatus.WARNING])
        if warning_count > 2:
            recommendations.append("建议优化系统配置以减少警告数量")
        
        return recommendations


# 全局启动健康检查器
global_startup_health_checker = StartupHealthChecker()