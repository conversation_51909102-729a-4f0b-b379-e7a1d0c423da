package com.crypto.trading.market.debug;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.crypto.trading.common.dto.market.DepthDataDTO;
import com.crypto.trading.market.converter.MarketDataConverter;
import com.crypto.trading.market.processor.DepthDataProcessor;
import com.crypto.trading.market.repository.InfluxDBRepository;
import com.crypto.trading.market.repository.MySQLMarketDataRepository;
import com.crypto.trading.sdk.response.model.DepthData;
import com.crypto.trading.sdk.response.model.WebSocketMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 深度数据流转测试器
 * 测试从消息解析到数据库存储的完整流程
 */
@SpringBootApplication
@ComponentScan(basePackages = "com.crypto.trading")
public class DepthDataFlowTester implements CommandLineRunner {

    private static final Logger log = LoggerFactory.getLogger(DepthDataFlowTester.class);

    @Autowired(required = false)
    private DepthDataProcessor depthDataProcessor;

    @Autowired(required = false)
    private MarketDataConverter marketDataConverter;

    @Autowired(required = false)
    private InfluxDBRepository influxDBRepository;

    @Autowired(required = false)
    private MySQLMarketDataRepository mySQLRepository;

    public static void main(String[] args) {
        SpringApplication.run(DepthDataFlowTester.class, args);
    }

    @Override
    public void run(String... args) throws Exception {
        log.info("开始深度数据流转测试...");

        // 测试消息解析
        testMessageParsing();

        // 测试数据转换
        testDataConversion();

        // 测试数据库存储
        testDatabaseStorage();

        // 测试完整流程
        testCompleteFlow();

        log.info("深度数据流转测试完成");
        System.exit(0);
    }

    private void testMessageParsing() {
        log.info("=== 测试消息解析 ===");
        
        // 使用真实的币安深度更新消息格式
        String testMessage = """
            {
              "e": "depthUpdate",
              "E": 1750503378654,
              "T": 1750503378624,
              "s": "ETHUSDT",
              "U": 53820091771,
              "u": 53820091842,
              "pu": 53820091131,
              "b": [
                ["2437.68", "1.804"],
                ["2437.67", "32.925"],
                ["2437.66", "80.409"]
              ],
              "a": [
                ["2437.82", "1.637"],
                ["2437.91", "2.052"],
                ["2437.92", "0.015"]
              ]
            }
            """;

        try {
            JSONObject jsonObject = JSON.parseObject(testMessage);
            log.info("✅ JSON解析成功");
            log.info("消息类型: {}", jsonObject.getString("e"));
            log.info("交易对: {}", jsonObject.getString("s"));
            log.info("lastUpdateId (u): {}", jsonObject.getLongValue("u"));
            log.info("买盘数量: {}", jsonObject.getJSONArray("b").size());
            log.info("卖盘数量: {}", jsonObject.getJSONArray("a").size());

            // 检查字段匹配逻辑
            boolean hasDepthFields = jsonObject.containsKey("u") || jsonObject.containsKey("lastUpdateId");
            log.info("深度字段检查: {}", hasDepthFields ? "✅ 通过" : "❌ 失败");

        } catch (Exception e) {
            log.error("❌ 消息解析失败", e);
        }
    }

    private void testDataConversion() {
        log.info("=== 测试数据转换 ===");
        
        if (marketDataConverter == null) {
            log.error("❌ MarketDataConverter未注入");
            return;
        }

        try {
            // 创建测试深度数据
            DepthData depthData = new DepthData();
            depthData.setLastUpdateId(53820091842L);
            depthData.setEventTime(System.currentTimeMillis());

            List<DepthData.PriceQuantity> bids = new ArrayList<>();
            bids.add(new DepthData.PriceQuantity(new BigDecimal("2437.68"), new BigDecimal("1.804")));
            bids.add(new DepthData.PriceQuantity(new BigDecimal("2437.67"), new BigDecimal("32.925")));

            List<DepthData.PriceQuantity> asks = new ArrayList<>();
            asks.add(new DepthData.PriceQuantity(new BigDecimal("2437.82"), new BigDecimal("1.637")));
            asks.add(new DepthData.PriceQuantity(new BigDecimal("2437.91"), new BigDecimal("2.052")));

            depthData.setBids(bids);
            depthData.setAsks(asks);

            // 转换为DTO
            DepthDataDTO dto = marketDataConverter.convertToOrderBookDTO(depthData, "ETHUSDT");
            
            if (dto != null) {
                log.info("✅ 数据转换成功");
                log.info("交易对: {}", dto.getSymbol());
                log.info("lastUpdateId: {}", dto.getLastUpdateId());
                log.info("买盘数量: {}", dto.getBids().size());
                log.info("卖盘数量: {}", dto.getAsks().size());
            } else {
                log.error("❌ 数据转换返回null");
            }

        } catch (Exception e) {
            log.error("❌ 数据转换失败", e);
        }
    }

    private void testDatabaseStorage() {
        log.info("=== 测试数据库存储 ===");

        // 测试MySQL Repository
        if (mySQLRepository != null) {
            log.info("✅ MySQL Repository已注入");

            // 测试实际存储
            try {
                DepthDataDTO testDTO = createTestDepthDataDTO();
                boolean result = mySQLRepository.saveDepthData(testDTO);
                log.info("MySQL存储测试结果: {}", result ? "✅ 成功" : "❌ 失败");

                // 等待异步处理完成
                Thread.sleep(1000);

            } catch (Exception e) {
                log.error("❌ MySQL存储测试失败", e);
            }
        } else {
            log.error("❌ MySQL Repository未注入");
        }

        // 测试InfluxDB Repository
        if (influxDBRepository != null) {
            log.info("✅ InfluxDB Repository已注入");

            // 测试实际存储
            try {
                DepthDataDTO testDTO = createTestDepthDataDTO();
                influxDBRepository.saveOrderBookData(testDTO);
                log.info("✅ InfluxDB存储测试完成");

                // 等待异步处理完成
                Thread.sleep(1000);

            } catch (Exception e) {
                log.error("❌ InfluxDB存储测试失败", e);
            }
        } else {
            log.error("❌ InfluxDB Repository未注入");
        }
    }

    private DepthDataDTO createTestDepthDataDTO() {
        DepthDataDTO dto = new DepthDataDTO();
        dto.setSymbol("ETHUSDT");
        dto.setLastUpdateId(53820091842L);
        dto.setLimit(5);
        dto.setUpdateTime(java.time.LocalDateTime.now());

        // 创建测试买盘数据
        List<DepthDataDTO.PriceQuantity> bids = new ArrayList<>();
        bids.add(new DepthDataDTO.PriceQuantity(new BigDecimal("2437.68"), new BigDecimal("1.804")));
        bids.add(new DepthDataDTO.PriceQuantity(new BigDecimal("2437.67"), new BigDecimal("32.925")));
        dto.setBids(bids);

        // 创建测试卖盘数据
        List<DepthDataDTO.PriceQuantity> asks = new ArrayList<>();
        asks.add(new DepthDataDTO.PriceQuantity(new BigDecimal("2437.82"), new BigDecimal("1.637")));
        asks.add(new DepthDataDTO.PriceQuantity(new BigDecimal("2437.91"), new BigDecimal("2.052")));
        dto.setAsks(asks);

        return dto;
    }

    private void testCompleteFlow() {
        log.info("=== 测试完整流程 ===");
        
        if (depthDataProcessor == null) {
            log.error("❌ DepthDataProcessor未注入");
            return;
        }

        try {
            // 创建测试WebSocket消息
            WebSocketMessage<DepthData> message = new WebSocketMessage<>();
            message.setSymbol("ETHUSDT");
            message.setEventType("depth");
            message.setEventTime(System.currentTimeMillis());

            DepthData depthData = new DepthData();
            depthData.setLastUpdateId(53820091842L);
            depthData.setEventTime(System.currentTimeMillis());

            List<DepthData.PriceQuantity> bids = new ArrayList<>();
            bids.add(new DepthData.PriceQuantity(new BigDecimal("2437.68"), new BigDecimal("1.804")));

            List<DepthData.PriceQuantity> asks = new ArrayList<>();
            asks.add(new DepthData.PriceQuantity(new BigDecimal("2437.82"), new BigDecimal("1.637")));

            depthData.setBids(bids);
            depthData.setAsks(asks);
            message.setData(depthData);

            log.info("开始处理测试深度数据...");
            depthDataProcessor.processDepthData(message);
            log.info("✅ 深度数据处理完成");

            // 等待异步处理完成
            Thread.sleep(2000);

        } catch (Exception e) {
            log.error("❌ 完整流程测试失败", e);
        }
    }
}
