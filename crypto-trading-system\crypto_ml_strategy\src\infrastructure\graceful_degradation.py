"""
优雅降级管理模块

提供crypto_ml_strategy项目的优雅降级功能，包括降级策略管理、
服务降级级别控制、降级决策引擎和自动降级恢复。

Author: Crypto ML Strategy Team
Date: 2024-12-20
Version: 1.0.0
"""

import asyncio
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Dict, List, Optional, Set, Callable
from loguru import logger

from .error_handler_core import ErrorSeverity, ErrorCategory, ErrorCode, ErrorContext


class DegradationLevel(Enum):
    """降级级别枚举"""
    NORMAL = "normal"          # 正常运行
    PARTIAL = "partial"        # 部分降级
    SIGNIFICANT = "significant"  # 显著降级
    MINIMAL = "minimal"        # 最小功能
    EMERGENCY = "emergency"    # 紧急模式


class ServiceStatus(Enum):
    """服务状态枚举"""
    ACTIVE = "active"
    DEGRADED = "degraded"
    DISABLED = "disabled"
    MAINTENANCE = "maintenance"


@dataclass
class DegradationRule:
    """降级规则"""
    service_name: str
    trigger_condition: str  # 触发条件描述
    target_level: DegradationLevel
    priority: int = 100  # 优先级，数字越小优先级越高
    enabled: bool = True
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def matches_condition(self, metrics: Dict[str, Any]) -> bool:
        """检查是否匹配触发条件"""
        try:
            # 简单的条件评估（实际应用中可以使用更复杂的规则引擎）
            return eval(self.trigger_condition, {"__builtins__": {}}, metrics)
        except Exception as e:
            logger.error(f"降级规则条件评估失败: {e}")
            return False


@dataclass
class ServiceDegradationConfig:
    """服务降级配置"""
    service_name: str
    normal_features: Set[str] = field(default_factory=set)
    partial_features: Set[str] = field(default_factory=set)
    significant_features: Set[str] = field(default_factory=set)
    minimal_features: Set[str] = field(default_factory=set)
    emergency_features: Set[str] = field(default_factory=set)
    
    def get_available_features(self, level: DegradationLevel) -> Set[str]:
        """获取指定降级级别下的可用功能"""
        if level == DegradationLevel.NORMAL:
            return self.normal_features
        elif level == DegradationLevel.PARTIAL:
            return self.partial_features
        elif level == DegradationLevel.SIGNIFICANT:
            return self.significant_features
        elif level == DegradationLevel.MINIMAL:
            return self.minimal_features
        elif level == DegradationLevel.EMERGENCY:
            return self.emergency_features
        else:
            return set()


@dataclass
class DegradationEvent:
    """降级事件"""
    service_name: str
    from_level: DegradationLevel
    to_level: DegradationLevel
    reason: str
    timestamp: datetime = field(default_factory=datetime.now)
    triggered_by: str = ""
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "service_name": self.service_name,
            "from_level": self.from_level.value,
            "to_level": self.to_level.value,
            "reason": self.reason,
            "timestamp": self.timestamp.isoformat(),
            "triggered_by": self.triggered_by,
            "metadata": self.metadata
        }


class DegradationStrategy(ABC):
    """降级策略抽象基类"""
    
    @abstractmethod
    async def should_degrade(
        self, 
        service_name: str, 
        current_level: DegradationLevel,
        metrics: Dict[str, Any]
    ) -> Optional[DegradationLevel]:
        """判断是否应该降级"""
        pass
    
    @abstractmethod
    async def execute_degradation(
        self,
        service_name: str,
        target_level: DegradationLevel,
        context: Dict[str, Any]
    ) -> bool:
        """执行降级"""
        pass
    
    @abstractmethod
    def get_strategy_name(self) -> str:
        """获取策略名称"""
        pass


class PerformanceDegradationStrategy(DegradationStrategy):
    """性能降级策略"""
    
    def __init__(
        self,
        response_time_threshold: float = 1000.0,  # 响应时间阈值（毫秒）
        error_rate_threshold: float = 0.1,        # 错误率阈值
        cpu_threshold: float = 80.0,              # CPU使用率阈值
        memory_threshold: float = 85.0            # 内存使用率阈值
    ):
        self.response_time_threshold = response_time_threshold
        self.error_rate_threshold = error_rate_threshold
        self.cpu_threshold = cpu_threshold
        self.memory_threshold = memory_threshold
    
    async def should_degrade(
        self, 
        service_name: str, 
        current_level: DegradationLevel,
        metrics: Dict[str, Any]
    ) -> Optional[DegradationLevel]:
        """基于性能指标判断是否应该降级"""
        response_time = metrics.get('response_time_ms', 0)
        error_rate = metrics.get('error_rate', 0)
        cpu_usage = metrics.get('cpu_usage', 0)
        memory_usage = metrics.get('memory_usage', 0)
        
        # 计算降级分数
        degradation_score = 0
        
        if response_time > self.response_time_threshold:
            degradation_score += 1
        if error_rate > self.error_rate_threshold:
            degradation_score += 2
        if cpu_usage > self.cpu_threshold:
            degradation_score += 1
        if memory_usage > self.memory_threshold:
            degradation_score += 1
        
        # 根据分数确定降级级别
        if degradation_score >= 4:
            return DegradationLevel.EMERGENCY
        elif degradation_score >= 3:
            return DegradationLevel.MINIMAL
        elif degradation_score >= 2:
            return DegradationLevel.SIGNIFICANT
        elif degradation_score >= 1:
            return DegradationLevel.PARTIAL
        else:
            return None  # 不需要降级
    
    async def execute_degradation(
        self,
        service_name: str,
        target_level: DegradationLevel,
        context: Dict[str, Any]
    ) -> bool:
        """执行性能降级"""
        try:
            logger.info(f"执行服务 {service_name} 性能降级到 {target_level.value}")
            
            # 根据降级级别执行相应的降级操作
            if target_level == DegradationLevel.PARTIAL:
                # 减少并发处理
                context['max_concurrent_requests'] = context.get('max_concurrent_requests', 100) // 2
            elif target_level == DegradationLevel.SIGNIFICANT:
                # 进一步减少并发，禁用非核心功能
                context['max_concurrent_requests'] = context.get('max_concurrent_requests', 100) // 4
                context['disable_analytics'] = True
            elif target_level == DegradationLevel.MINIMAL:
                # 只保留核心功能
                context['max_concurrent_requests'] = 10
                context['disable_analytics'] = True
                context['disable_logging'] = True
            elif target_level == DegradationLevel.EMERGENCY:
                # 紧急模式，最小资源使用
                context['max_concurrent_requests'] = 1
                context['disable_all_non_essential'] = True
            
            return True
            
        except Exception as e:
            logger.error(f"性能降级执行失败: {e}")
            return False
    
    def get_strategy_name(self) -> str:
        return "performance_degradation"


class LoadBasedDegradationStrategy(DegradationStrategy):
    """负载降级策略"""
    
    def __init__(
        self,
        request_rate_threshold: float = 1000.0,  # 请求率阈值（请求/秒）
        queue_size_threshold: int = 1000,        # 队列大小阈值
        connection_threshold: int = 500          # 连接数阈值
    ):
        self.request_rate_threshold = request_rate_threshold
        self.queue_size_threshold = queue_size_threshold
        self.connection_threshold = connection_threshold
    
    async def should_degrade(
        self, 
        service_name: str, 
        current_level: DegradationLevel,
        metrics: Dict[str, Any]
    ) -> Optional[DegradationLevel]:
        """基于负载指标判断是否应该降级"""
        request_rate = metrics.get('request_rate', 0)
        queue_size = metrics.get('queue_size', 0)
        active_connections = metrics.get('active_connections', 0)
        
        # 计算负载压力
        load_pressure = 0
        
        if request_rate > self.request_rate_threshold:
            load_pressure += request_rate / self.request_rate_threshold
        if queue_size > self.queue_size_threshold:
            load_pressure += queue_size / self.queue_size_threshold
        if active_connections > self.connection_threshold:
            load_pressure += active_connections / self.connection_threshold
        
        # 根据负载压力确定降级级别
        if load_pressure >= 3.0:
            return DegradationLevel.EMERGENCY
        elif load_pressure >= 2.5:
            return DegradationLevel.MINIMAL
        elif load_pressure >= 2.0:
            return DegradationLevel.SIGNIFICANT
        elif load_pressure >= 1.5:
            return DegradationLevel.PARTIAL
        else:
            return None
    
    async def execute_degradation(
        self,
        service_name: str,
        target_level: DegradationLevel,
        context: Dict[str, Any]
    ) -> bool:
        """执行负载降级"""
        try:
            logger.info(f"执行服务 {service_name} 负载降级到 {target_level.value}")
            
            # 根据降级级别调整负载处理
            if target_level == DegradationLevel.PARTIAL:
                context['enable_request_throttling'] = True
                context['throttle_rate'] = 0.8  # 80%的请求通过
            elif target_level == DegradationLevel.SIGNIFICANT:
                context['throttle_rate'] = 0.5  # 50%的请求通过
                context['enable_queue_limiting'] = True
            elif target_level == DegradationLevel.MINIMAL:
                context['throttle_rate'] = 0.2  # 20%的请求通过
                context['max_queue_size'] = 100
            elif target_level == DegradationLevel.EMERGENCY:
                context['throttle_rate'] = 0.1  # 10%的请求通过
                context['max_queue_size'] = 10
                context['reject_new_connections'] = True
            
            return True
            
        except Exception as e:
            logger.error(f"负载降级执行失败: {e}")
            return False
    
    def get_strategy_name(self) -> str:
        return "load_based_degradation"


class GracefulDegradationManager:
    """优雅降级管理器"""
    
    def __init__(self):
        self.service_configs: Dict[str, ServiceDegradationConfig] = {}
        self.current_levels: Dict[str, DegradationLevel] = {}
        self.degradation_strategies: List[DegradationStrategy] = []
        self.degradation_rules: List[DegradationRule] = []
        self.degradation_history: List[DegradationEvent] = []
        self.max_history = 1000
        self.monitoring_enabled = False
        self.monitor_task: Optional[asyncio.Task] = None
        
        # 注册默认降级策略
        self.register_strategy(PerformanceDegradationStrategy())
        self.register_strategy(LoadBasedDegradationStrategy())
    
    def register_service(
        self, 
        service_name: str, 
        config: ServiceDegradationConfig
    ) -> None:
        """注册服务降级配置"""
        self.service_configs[service_name] = config
        self.current_levels[service_name] = DegradationLevel.NORMAL
        logger.info(f"注册服务降级配置: {service_name}")
    
    def register_strategy(self, strategy: DegradationStrategy) -> None:
        """注册降级策略"""
        self.degradation_strategies.append(strategy)
        logger.info(f"注册降级策略: {strategy.get_strategy_name()}")
    
    def add_degradation_rule(self, rule: DegradationRule) -> None:
        """添加降级规则"""
        self.degradation_rules.append(rule)
        # 按优先级排序
        self.degradation_rules.sort(key=lambda r: r.priority)
        logger.info(f"添加降级规则: {rule.service_name} - {rule.trigger_condition}")
    
    async def evaluate_degradation(
        self, 
        service_name: str, 
        metrics: Dict[str, Any]
    ) -> Optional[DegradationLevel]:
        """评估是否需要降级"""
        if service_name not in self.service_configs:
            return None
        
        current_level = self.current_levels.get(service_name, DegradationLevel.NORMAL)
        
        # 检查降级规则
        for rule in self.degradation_rules:
            if (rule.service_name == service_name and 
                rule.enabled and 
                rule.matches_condition(metrics)):
                return rule.target_level
        
        # 使用降级策略评估
        for strategy in self.degradation_strategies:
            suggested_level = await strategy.should_degrade(
                service_name, current_level, metrics
            )
            if suggested_level and suggested_level != current_level:
                return suggested_level
        
        return None
    
    async def degrade_service(
        self,
        service_name: str,
        target_level: DegradationLevel,
        reason: str = "",
        triggered_by: str = ""
    ) -> bool:
        """降级服务"""
        if service_name not in self.service_configs:
            logger.error(f"服务 {service_name} 未注册降级配置")
            return False
        
        current_level = self.current_levels.get(service_name, DegradationLevel.NORMAL)
        
        if current_level == target_level:
            logger.info(f"服务 {service_name} 已处于目标降级级别 {target_level.value}")
            return True
        
        try:
            # 记录降级事件
            event = DegradationEvent(
                service_name=service_name,
                from_level=current_level,
                to_level=target_level,
                reason=reason,
                triggered_by=triggered_by
            )
            
            # 执行降级策略
            context = {"service_name": service_name, "target_level": target_level}
            
            for strategy in self.degradation_strategies:
                success = await strategy.execute_degradation(
                    service_name, target_level, context
                )
                if not success:
                    logger.warning(
                        f"降级策略 {strategy.get_strategy_name()} 执行失败"
                    )
            
            # 更新当前级别
            self.current_levels[service_name] = target_level
            
            # 记录历史
            self.degradation_history.append(event)
            if len(self.degradation_history) > self.max_history:
                self.degradation_history.pop(0)
            
            logger.info(
                f"服务 {service_name} 降级成功: {current_level.value} -> {target_level.value}"
            )
            return True
            
        except Exception as e:
            logger.error(f"服务 {service_name} 降级失败: {e}")
            return False
    
    async def restore_service(self, service_name: str) -> bool:
        """恢复服务到正常级别"""
        return await self.degrade_service(
            service_name, 
            DegradationLevel.NORMAL, 
            "手动恢复", 
            "manual_restore"
        )
    
    def get_service_status(self, service_name: str) -> Dict[str, Any]:
        """获取服务状态"""
        if service_name not in self.service_configs:
            return {}
        
        current_level = self.current_levels.get(service_name, DegradationLevel.NORMAL)
        config = self.service_configs[service_name]
        available_features = config.get_available_features(current_level)
        
        return {
            "service_name": service_name,
            "current_level": current_level.value,
            "available_features": list(available_features),
            "total_features": len(config.normal_features),
            "degradation_percentage": (
                1 - len(available_features) / len(config.normal_features)
                if config.normal_features else 0
            )
        }
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统降级状态"""
        service_statuses = {
            name: self.get_service_status(name)
            for name in self.service_configs.keys()
        }
        
        # 计算整体降级程度
        total_services = len(service_statuses)
        degraded_services = len([
            s for s in service_statuses.values()
            if s.get("current_level") != "normal"
        ])
        
        return {
            "total_services": total_services,
            "degraded_services": degraded_services,
            "degradation_percentage": degraded_services / total_services if total_services > 0 else 0,
            "service_statuses": service_statuses,
            "recent_events": [
                event.to_dict() for event in self.degradation_history[-10:]
            ]
        }
    
    async def start_monitoring(self, interval: int = 30) -> None:
        """开始降级监控"""
        if self.monitoring_enabled:
            return
        
        self.monitoring_enabled = True
        self.monitor_task = asyncio.create_task(self._monitor_loop(interval))
        logger.info("降级监控已启动")
    
    async def stop_monitoring(self) -> None:
        """停止降级监控"""
        self.monitoring_enabled = False
        if self.monitor_task:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass
        logger.info("降级监控已停止")
    
    async def _monitor_loop(self, interval: int) -> None:
        """监控循环"""
        while self.monitoring_enabled:
            try:
                # 这里应该从实际的监控系统获取指标
                # 为了演示，我们使用模拟数据
                for service_name in self.service_configs.keys():
                    metrics = await self._collect_service_metrics(service_name)
                    
                    suggested_level = await self.evaluate_degradation(
                        service_name, metrics
                    )
                    
                    if suggested_level:
                        current_level = self.current_levels.get(
                            service_name, DegradationLevel.NORMAL
                        )
                        
                        if suggested_level != current_level:
                            await self.degrade_service(
                                service_name,
                                suggested_level,
                                "自动降级监控触发",
                                "auto_monitor"
                            )
                
                await asyncio.sleep(interval)
                
            except Exception as e:
                logger.error(f"降级监控循环异常: {e}")
                await asyncio.sleep(5)
    
    async def _collect_service_metrics(self, service_name: str) -> Dict[str, Any]:
        """收集服务指标（模拟实现）"""
        # 实际实现中应该从监控系统获取真实指标
        return {
            "response_time_ms": 500,
            "error_rate": 0.05,
            "cpu_usage": 60,
            "memory_usage": 70,
            "request_rate": 800,
            "queue_size": 50,
            "active_connections": 200
        }


# 全局降级管理器
global_degradation_manager = GracefulDegradationManager()