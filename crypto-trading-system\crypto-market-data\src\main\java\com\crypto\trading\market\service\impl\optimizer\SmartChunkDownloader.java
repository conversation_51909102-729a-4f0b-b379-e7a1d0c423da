package com.crypto.trading.market.service.impl.optimizer;

import com.crypto.trading.common.dto.market.KlineDataDTO;
import com.crypto.trading.common.enums.KlineInterval;
import com.crypto.trading.market.service.impl.DownloadTaskTracker;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.BiFunction;

/**
 * 智能分块下载器
 * 负责实现智能分块和并行下载功能，优化数据获取性能
 */
@Component
public class SmartChunkDownloader {

    private static final Logger log = LoggerFactory.getLogger(SmartChunkDownloader.class);
    
    @Autowired
    private SmartDownloadOptimizer optimizer;
    
    @Autowired
    private ExecutorService downloadExecutor;
    
    /**
     * 智能分块下载K线数据
     *
     * @param symbol         交易对
     * @param interval       K线间隔
     * @param startTime      开始时间
     * @param endTime        结束时间
     * @param downloadFunc   实际执行下载的函数
     * @param taskId         任务ID（如果是批量任务的一部分）
     * @param taskInfo       任务信息（如果是批量任务的一部分）
     * @return 下载的K线数据
     */
    public List<KlineDataDTO> downloadWithSmartChunks(
            String symbol,
            KlineInterval interval,
            LocalDateTime startTime,
            LocalDateTime endTime,
            BiFunction<TimeChunk, Integer, List<KlineDataDTO>> downloadFunc,
            String taskId,
            DownloadTaskTracker.TaskInfo taskInfo) {
            
        // 计算最优分块大小（小时）
        int chunkSizeHours = optimizer.calculateOptimalChunkSize(symbol, interval, startTime, endTime);
        
        // 计算最优并发请求数
        int concurrentRequests = optimizer.calculateOptimalConcurrentRequests(symbol, interval);
        
        // 如果任务信息中已经指定了这些参数，使用任务信息中的设置
        if (taskInfo != null) {
            chunkSizeHours = taskInfo.getChunkSizeHours();
            concurrentRequests = taskInfo.getMaxConcurrentRequests();
        }
        
        // 创建分块
        List<TimeChunk> chunks = createTimeChunks(startTime, endTime, chunkSizeHours);
        
        log.info("开始智能分块下载: 交易对={}, 间隔={}, 开始={}, 结束={}, 分块数量={}, 块大小={}小时, 并发数={}",
                symbol, interval.getCode(), startTime, endTime, chunks.size(), chunkSizeHours, concurrentRequests);
        
        // 并行下载所有分块
        List<KlineDataDTO> allData = Collections.synchronizedList(new ArrayList<>());
        CompletableFuture<Void> allDownloads = downloadChunksInParallel(chunks, concurrentRequests, downloadFunc, allData);
        
        // 等待所有下载完成
        try {
            allDownloads.get();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("并行下载被中断: {}", e.getMessage());
        } catch (ExecutionException e) {
            log.error("并行下载出错: {}", e.getMessage(), e);
            throw new RuntimeException("并行下载失败", e.getCause());
        }
        
        // 排序数据（并行下载可能导致数据次序混乱）
        allData.sort(Comparator.comparing(KlineDataDTO::getOpenTime));
        
        log.info("智能分块下载完成: 交易对={}, 间隔={}, 总数据点={}", symbol, interval.getCode(), allData.size());
        
        return allData;
    }
    
    /**
     * 创建时间分块
     *
     * @param startTime      开始时间
     * @param endTime        结束时间
     * @param chunkSizeHours 分块大小（小时）
     * @return 时间分块列表
     */
    private List<TimeChunk> createTimeChunks(LocalDateTime startTime, LocalDateTime endTime, int chunkSizeHours) {
        List<TimeChunk> chunks = new ArrayList<>();
        
        LocalDateTime chunkStart = startTime;
        int index = 0;
        
        while (chunkStart.isBefore(endTime)) {
            LocalDateTime chunkEnd = chunkStart.plusHours(chunkSizeHours);
            
            // 确保不超过总的结束时间
            if (chunkEnd.isAfter(endTime)) {
                chunkEnd = endTime;
            }
            
            chunks.add(new TimeChunk(index++, chunkStart, chunkEnd));
            chunkStart = chunkEnd;
            
            // 防止无限循环（如果chunkEnd等于chunkStart）
            if (chunkStart.equals(endTime)) {
                break;
            }
        }
        
        return chunks;
    }
    
    /**
     * 并行下载所有分块
     *
     * @param chunks            分块列表
     * @param concurrentRequests 并发请求数
     * @param downloadFunc      下载函数
     * @param allData           所有下载的数据（结果列表）
     * @return 表示所有下载任务的CompletableFuture
     */
    private CompletableFuture<Void> downloadChunksInParallel(
            List<TimeChunk> chunks,
            int concurrentRequests,
            BiFunction<TimeChunk, Integer, List<KlineDataDTO>> downloadFunc,
            List<KlineDataDTO> allData) {
        
        // 使用限流器控制并发量
        Semaphore semaphore = new Semaphore(concurrentRequests);
        
        // 为每个分块创建一个CompletableFuture
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        
        for (TimeChunk chunk : chunks) {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                try {
                    // 获取许可
                    semaphore.acquire();
                    try {
                        // 下载数据
                        List<KlineDataDTO> chunkData = downloadFunc.apply(chunk, 0);
                        if (chunkData != null && !chunkData.isEmpty()) {
                            allData.addAll(chunkData);
                        }
                    } finally {
                        // 释放许可
                        semaphore.release();
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.error("分块下载被中断: 分块={}", chunk, e);
                } catch (Exception e) {
                    log.error("分块下载异常: 分块={}, 错误={}", chunk, e.getMessage(), e);
                }
            }, downloadExecutor);
            
            futures.add(future);
        }
        
        // 组合所有CompletableFuture
        return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
    }
    
    /**
     * 时间分块类
     * 表示一个时间范围
     */
    public static class TimeChunk {
        private final int index;
        private final LocalDateTime startTime;
        private final LocalDateTime endTime;
        
        public TimeChunk(int index, LocalDateTime startTime, LocalDateTime endTime) {
            this.index = index;
            this.startTime = startTime;
            this.endTime = endTime;
        }
        
        public int getIndex() {
            return index;
        }
        
        public LocalDateTime getStartTime() {
            return startTime;
        }
        
        public LocalDateTime getEndTime() {
            return endTime;
        }
        
        public Duration getDuration() {
            return Duration.between(startTime, endTime);
        }
        
        public long getDurationHours() {
            return ChronoUnit.HOURS.between(startTime, endTime);
        }
        
        @Override
        public String toString() {
            return String.format("TimeChunk[index=%d, start=%s, end=%s, hours=%d]",
                    index, startTime, endTime, getDurationHours());
        }
    }
}