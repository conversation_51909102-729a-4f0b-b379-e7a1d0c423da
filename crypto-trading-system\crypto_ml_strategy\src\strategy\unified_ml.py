#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
统一机器学习策略模块

整合多种技术指标和机器学习模型的统一交易策略。
集成LPPL、Hematread、BMSB、SuperTrend等技术指标，
结合DeepSeek蒸馏模型进行智能交易决策。
"""

import uuid
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
import pandas as pd
import numpy as np
from loguru import logger

from .base import BaseStrategy
from core.config import Config
from indicators.lppl_features import LPPLFeatureExtractor
from indicators.hematread_features import HematreadFeatureExtractor
from indicators.bull_market_support import BullMarketSupportFeatureExtractor
from indicators.super_trend import SuperTrendCalculator


class UnifiedMLStrategy(BaseStrategy):
    """
    统一机器学习策略类
    
    整合多种技术指标和机器学习模型：
    - LPPL (Log-Periodic Power Law) 泡沫检测
    - Hematread 动量和趋势分析
    - Bull Market Support Band 支撑位识别
    - SuperTrend 趋势跟踪
    - DeepSeek蒸馏模型预测
    - 风险管理和资金管理
    """
    
    def __init__(self, config: Config):
        """
        初始化统一机器学习策略
        
        Args:
            config: 配置对象
        """
        super().__init__(config)
        
        self.logger = logger.bind(component="UnifiedMLStrategy")
        
        # 策略参数
        self.confidence_threshold = config.get_float('strategy', 'confidence_threshold', 0.75)
        self.risk_threshold = config.get_float('strategy', 'risk_threshold', 0.5)
        self.symbols = config.get_list('strategy', 'symbols')
        self.timeframes = config.get_list('strategy', 'timeframes')
        
        # 技术指标实例
        self.lppl_features = LPPLFeatureExtractor()
        self.hematread_features = HematreadFeatureExtractor()
        self.bmsb = BullMarketSupportFeatureExtractor()
        self.super_trend = SuperTrendCalculator()
        
        # 预测引擎（将在外部设置）
        self.prediction_engine = None
        
        # 策略状态
        self.last_signals = {}  # {symbol: last_signal_info}
        self.position_tracker = {}  # {symbol: position_info}
        self.risk_metrics = {}  # {symbol: risk_info}
        
        # 信号历史
        self.signal_history = []
        self.max_history_size = 1000
        
        # 性能统计
        self.performance_stats = {
            'total_signals': 0,
            'buy_signals': 0,
            'sell_signals': 0,
            'hold_signals': 0,
            'high_confidence_signals': 0,
            'successful_predictions': 0,
            'failed_predictions': 0
        }
        
        self.logger.info(f"统一ML策略初始化完成，支持交易对: {self.symbols}")
    
    def set_prediction_engine(self, prediction_engine) -> None:
        """
        设置预测引擎
        
        Args:
            prediction_engine: 预测引擎实例
        """
        self.prediction_engine = prediction_engine
        self.logger.info("预测引擎已设置")
    
    def on_market_data(self, market_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        处理市场数据并生成交易信号
        
        Args:
            market_data: 市场数据字典
        
        Returns:
            交易信号字典，如果没有信号则返回None
        """
        try:
            symbol = market_data.get('symbol')
            
            if not symbol or symbol not in self.symbols:
                return None
            
            # 计算技术指标特征
            technical_features = self._calculate_technical_features(market_data)
            
            if not technical_features:
                self.logger.warning(f"技术指标计算失败: {symbol}")
                return None
            
            # 使用机器学习模型预测
            ml_prediction = self._get_ml_prediction(symbol, technical_features)
            
            if not ml_prediction:
                self.logger.debug(f"ML预测失败: {symbol}")
                return None
            
            # 风险评估
            risk_assessment = self._assess_risk(symbol, market_data, ml_prediction)
            
            # 生成最终信号
            final_signal = self._generate_final_signal(
                symbol, market_data, technical_features, ml_prediction, risk_assessment
            )
            
            if final_signal:
                # 更新策略状态
                self._update_strategy_state(symbol, final_signal)
                
                # 记录信号历史
                self._record_signal_history(final_signal)
                
                # 更新性能统计
                self._update_performance_stats(final_signal)
                
                self.logger.info(f"生成交易信号: {symbol} - {final_signal['signalType']} (置信度: {final_signal['confidence']:.3f})")
                
                return final_signal
            
            return None
            
        except Exception as e:
            self.logger.error(f"处理市场数据失败: {str(e)}")
            return None
    
    def _calculate_technical_features(self, market_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        计算技术指标特征
        
        Args:
            market_data: 市场数据
        
        Returns:
            技术指标特征字典
        """
        try:
            symbol = market_data['symbol']
            features = {}
            
            # 获取K线数据
            kline_data = market_data.get('kline', {})
            
            if not kline_data:
                return None
            
            # 转换为DataFrame格式（简化处理）
            df_data = []
            for timeframe, kline in kline_data.items():
                if isinstance(kline, dict):
                    df_data.append({
                        'timeframe': timeframe,
                        'open': kline.get('open', 0),
                        'high': kline.get('high', 0),
                        'low': kline.get('low', 0),
                        'close': kline.get('close', 0),
                        'volume': kline.get('volume', 0),
                        'timestamp': kline.get('open_time', datetime.now().timestamp() * 1000)
                    })
            
            if not df_data:
                return None
            
            df = pd.DataFrame(df_data)
            
            # LPPL特征
            try:
                df_with_lppl = self.lppl_features.extract_features(df)
                if not df_with_lppl.empty and len(df_with_lppl) > 0:
                    # 提取最新行的LPPL特征
                    latest_row = df_with_lppl.iloc[-1]
                    lppl_cols = [col for col in df_with_lppl.columns if col.startswith('lppl_')]
                    for col in lppl_cols:
                        if not pd.isna(latest_row[col]):
                            features[col] = latest_row[col]
            except Exception as e:
                self.logger.warning(f"LPPL特征计算失败: {str(e)}")
            
            # Hematread特征
            try:
                df_with_hematread = self.hematread_features.extract_features(df)
                if not df_with_hematread.empty and len(df_with_hematread) > 0:
                    # 提取最新行的Hematread特征
                    latest_row = df_with_hematread.iloc[-1]
                    hematread_cols = [col for col in df_with_hematread.columns if col.startswith('hema_')]
                    for col in hematread_cols:
                        if not pd.isna(latest_row[col]):
                            features[col] = latest_row[col]
            except Exception as e:
                self.logger.warning(f"Hematread特征计算失败: {str(e)}")
            
            # Bull Market Support Band
            try:
                df_with_bmsb = self.bmsb.extract_features(df)
                if not df_with_bmsb.empty and len(df_with_bmsb) > 0:
                    # 提取最新行的BMSB特征
                    latest_row = df_with_bmsb.iloc[-1]
                    bmsb_cols = [col for col in df_with_bmsb.columns if col.startswith('bmsb_')]
                    for col in bmsb_cols:
                        if not pd.isna(latest_row[col]):
                            features[col] = latest_row[col]
            except Exception as e:
                self.logger.warning(f"BMSB特征计算失败: {str(e)}")
            
            # SuperTrend
            try:
                df_with_supertrend = self.super_trend.extract_features(df)
                if not df_with_supertrend.empty and len(df_with_supertrend) > 0:
                    # 提取最新行的SuperTrend特征
                    latest_row = df_with_supertrend.iloc[-1]
                    supertrend_cols = [col for col in df_with_supertrend.columns if col.startswith('supertrend_')]
                    for col in supertrend_cols:
                        if not pd.isna(latest_row[col]):
                            features[col] = latest_row[col]
            except Exception as e:
                self.logger.warning(f"SuperTrend特征计算失败: {str(e)}")
            
            # 基础技术指标
            try:
                basic_features = self._calculate_basic_features(df)
                if basic_features:
                    features.update(basic_features)
            except Exception as e:
                self.logger.warning(f"基础技术指标计算失败: {str(e)}")
            
            # 深度数据特征
            depth_data = market_data.get('depth')
            if depth_data:
                try:
                    depth_features = self._calculate_depth_features(depth_data)
                    if depth_features:
                        features.update(depth_features)
                except Exception as e:
                    self.logger.warning(f"深度数据特征计算失败: {str(e)}")
            
            return features if features else None
            
        except Exception as e:
            self.logger.error(f"技术指标特征计算失败: {str(e)}")
            return None
    
    def _calculate_basic_features(self, df: pd.DataFrame) -> Dict[str, float]:
        """
        计算基础技术指标
        
        Args:
            df: K线数据DataFrame
        
        Returns:
            基础技术指标字典
        """
        try:
            features = {}
            
            if df.empty or 'close' not in df.columns:
                return features
            
            # 使用最新的价格数据
            latest_close = df['close'].iloc[-1]
            
            # 价格变化率
            if len(df) > 1:
                prev_close = df['close'].iloc[-2]
                features['price_change_pct'] = (latest_close - prev_close) / prev_close
            
            # 简单移动平均
            if len(df) >= 5:
                features['ma5_ratio'] = latest_close / df['close'].tail(5).mean()
            
            if len(df) >= 10:
                features['ma10_ratio'] = latest_close / df['close'].tail(10).mean()
            
            # 波动率
            if len(df) >= 10:
                returns = df['close'].pct_change().dropna()
                if len(returns) > 0:
                    features['volatility'] = returns.std()
            
            # 成交量特征
            if 'volume' in df.columns and len(df) >= 5:
                latest_volume = df['volume'].iloc[-1]
                avg_volume = df['volume'].tail(5).mean()
                if avg_volume > 0:
                    features['volume_ratio'] = latest_volume / avg_volume
            
            return features
            
        except Exception as e:
            self.logger.error(f"基础技术指标计算失败: {str(e)}")
            return {}
    
    def _calculate_depth_features(self, depth_data: Dict[str, Any]) -> Dict[str, float]:
        """
        计算深度数据特征
        
        Args:
            depth_data: 深度数据字典
        
        Returns:
            深度特征字典
        """
        try:
            features = {}
            
            # 买卖价差
            spread = depth_data.get('spread', 0)
            features['spread'] = spread
            
            # 买卖比例
            bid_ask_ratio = depth_data.get('bid_ask_ratio', 1.0)
            features['bid_ask_ratio'] = bid_ask_ratio
            
            # 中间价
            mid_price = depth_data.get('mid_price', 0)
            features['mid_price'] = mid_price
            
            # 最优买卖价格和数量
            features['best_bid_price'] = depth_data.get('best_bid_price', 0)
            features['best_ask_price'] = depth_data.get('best_ask_price', 0)
            features['best_bid_qty'] = depth_data.get('best_bid_qty', 0)
            features['best_ask_qty'] = depth_data.get('best_ask_qty', 0)
            
            return features
            
        except Exception as e:
            self.logger.error(f"深度特征计算失败: {str(e)}")
            return {}
    
    def _get_ml_prediction(self, symbol: str, features: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        获取机器学习预测
        
        Args:
            symbol: 交易对符号
            features: 特征字典
        
        Returns:
            ML预测结果
        """
        try:
            if not self.prediction_engine:
                self.logger.warning("预测引擎未设置")
                return None
            
            prediction = self.prediction_engine.predict(symbol, features)
            
            if prediction and prediction.get('confidence', 0) > 0.1:  # 最低置信度阈值
                return prediction
            
            return None
            
        except Exception as e:
            self.logger.error(f"ML预测失败: {str(e)}")
            return None
    
    def _assess_risk(self, symbol: str, market_data: Dict[str, Any], 
                    ml_prediction: Dict[str, Any]) -> Dict[str, Any]:
        """
        风险评估
        
        Args:
            symbol: 交易对符号
            market_data: 市场数据
            ml_prediction: ML预测结果
        
        Returns:
            风险评估结果
        """
        try:
            risk_assessment = {
                'risk_level': 'medium',
                'risk_score': 0.5,
                'risk_factors': [],
                'position_size_multiplier': 1.0
            }
            
            risk_factors = []
            risk_score = 0.5
            
            # 预测置信度风险
            confidence = ml_prediction.get('confidence', 0)
            if confidence < 0.6:
                risk_factors.append('低预测置信度')
                risk_score += 0.2
            elif confidence > 0.9:
                risk_score -= 0.1
            
            # 市场波动性风险
            kline_data = market_data.get('kline', {})
            if kline_data:
                # 计算价格波动
                prices = []
                for timeframe_data in kline_data.values():
                    if isinstance(timeframe_data, dict):
                        high = timeframe_data.get('high', 0)
                        low = timeframe_data.get('low', 0)
                        if high > 0 and low > 0:
                            volatility = (high - low) / low
                            prices.append(volatility)
                
                if prices:
                    avg_volatility = np.mean(prices)
                    if avg_volatility > 0.05:  # 5%以上波动
                        risk_factors.append('高市场波动性')
                        risk_score += 0.15
            
            # 深度数据风险
            depth_data = market_data.get('depth')
            if depth_data:
                spread = depth_data.get('spread', 0)
                mid_price = depth_data.get('mid_price', 1)
                
                if mid_price > 0:
                    spread_ratio = spread / mid_price
                    if spread_ratio > 0.001:  # 0.1%以上价差
                        risk_factors.append('大买卖价差')
                        risk_score += 0.1
            
            # 历史信号表现
            if symbol in self.last_signals:
                last_signal = self.last_signals[symbol]
                time_since_last = (datetime.now() - last_signal.get('timestamp', datetime.now())).total_seconds()
                
                if time_since_last < 300:  # 5分钟内有信号
                    risk_factors.append('频繁交易')
                    risk_score += 0.1
            
            # 确定风险等级
            if risk_score < 0.3:
                risk_level = 'low'
                position_multiplier = 1.2
            elif risk_score < 0.7:
                risk_level = 'medium'
                position_multiplier = 1.0
            else:
                risk_level = 'high'
                position_multiplier = 0.5
            
            risk_assessment.update({
                'risk_level': risk_level,
                'risk_score': min(risk_score, 1.0),
                'risk_factors': risk_factors,
                'position_size_multiplier': position_multiplier
            })
            
            return risk_assessment
            
        except Exception as e:
            self.logger.error(f"风险评估失败: {str(e)}")
            return {'risk_level': 'high', 'risk_score': 1.0, 'risk_factors': ['评估失败'], 'position_size_multiplier': 0.1}
    
    def _generate_final_signal(self, symbol: str, market_data: Dict[str, Any],
                              technical_features: Dict[str, Any], ml_prediction: Dict[str, Any],
                              risk_assessment: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        生成最终交易信号
        
        Args:
            symbol: 交易对符号
            market_data: 市场数据
            technical_features: 技术指标特征
            ml_prediction: ML预测结果
            risk_assessment: 风险评估结果
        
        Returns:
            最终交易信号
        """
        try:
            # 基础信号来自ML预测
            ml_signal = ml_prediction.get('signal', 0)
            ml_confidence = ml_prediction.get('confidence', 0)
            
            # 风险调整
            risk_score = risk_assessment.get('risk_score', 0.5)
            position_multiplier = risk_assessment.get('position_size_multiplier', 1.0)
            
            # 置信度调整
            adjusted_confidence = ml_confidence * (1 - risk_score * 0.5)
            
            # 信号强度计算
            signal_strength = abs(ml_signal) * adjusted_confidence * position_multiplier
            
            # 信号过滤
            if adjusted_confidence < self.confidence_threshold:
                return None
            
            if risk_score > self.risk_threshold:
                return None
            
            # 确定信号类型
            if ml_signal > 0:
                signal_type = 'BUY'
            elif ml_signal < 0:
                signal_type = 'SELL'
            else:
                signal_type = 'HOLD'
            
            # 如果是HOLD信号且置信度不够高，不发送信号
            if signal_type == 'HOLD' and adjusted_confidence < 0.8:
                return None
            
            # 获取当前价格
            current_price = 0
            kline_data = market_data.get('kline', {})
            if kline_data:
                for timeframe_data in kline_data.values():
                    if isinstance(timeframe_data, dict) and 'close' in timeframe_data:
                        current_price = timeframe_data['close']
                        break
            
            # 构建最终信号
            final_signal = {
                'messageType': 'TRADING_SIGNAL',
                'signalId': str(uuid.uuid4()),
                'timestamp': datetime.now().isoformat(),
                'data': {
                    'symbol': symbol,
                    'signalType': signal_type,
                    'signalStrength': round(signal_strength, 4),
                    'confidence': round(adjusted_confidence, 4),
                    'price': current_price,
                    'riskLevel': risk_assessment.get('risk_level', 'medium'),
                    'riskScore': round(risk_score, 4),
                    'positionSizeMultiplier': round(position_multiplier, 4),
                    'mlPrediction': {
                        'signal': ml_signal,
                        'confidence': ml_confidence,
                        'probabilities': ml_prediction.get('probabilities', {}),
                        'modelType': ml_prediction.get('model_type', 'unknown')
                    },
                    'technicalIndicators': {
                        'lppl': {k: v for k, v in technical_features.items() if k.startswith('lppl_')},
                        'hematread': {k: v for k, v in technical_features.items() if k.startswith('hema_')},
                        'bmsb': {k: v for k, v in technical_features.items() if k.startswith('bmsb_')},
                        'supertrend': {k: v for k, v in technical_features.items() if k.startswith('supertrend_')}
                    },
                    'riskFactors': risk_assessment.get('risk_factors', [])
                }
            }
            
            return final_signal
            
        except Exception as e:
            self.logger.error(f"生成最终信号失败: {str(e)}")
            return None
    
    def _update_strategy_state(self, symbol: str, signal: Dict[str, Any]) -> None:
        """
        更新策略状态
        
        Args:
            symbol: 交易对符号
            signal: 交易信号
        """
        try:
            self.last_signals[symbol] = {
                'signal': signal,
                'timestamp': datetime.now()
            }
            
            # 更新风险指标
            signal_data = signal.get('data', {})
            self.risk_metrics[symbol] = {
                'risk_level': signal_data.get('riskLevel', 'medium'),
                'risk_score': signal_data.get('riskScore', 0.5),
                'last_update': datetime.now()
            }
            
        except Exception as e:
            self.logger.error(f"更新策略状态失败: {str(e)}")
    
    def _record_signal_history(self, signal: Dict[str, Any]) -> None:
        """
        记录信号历史
        
        Args:
            signal: 交易信号
        """
        try:
            self.signal_history.append({
                'signal': signal,
                'timestamp': datetime.now()
            })
            
            # 限制历史记录大小
            if len(self.signal_history) > self.max_history_size:
                self.signal_history = self.signal_history[-self.max_history_size:]
                
        except Exception as e:
            self.logger.error(f"记录信号历史失败: {str(e)}")
    
    def _update_performance_stats(self, signal: Dict[str, Any]) -> None:
        """
        更新性能统计
        
        Args:
            signal: 交易信号
        """
        try:
            self.performance_stats['total_signals'] += 1
            
            signal_type = signal.get('data', {}).get('signalType', 'HOLD')
            confidence = signal.get('data', {}).get('confidence', 0)
            
            if signal_type == 'BUY':
                self.performance_stats['buy_signals'] += 1
            elif signal_type == 'SELL':
                self.performance_stats['sell_signals'] += 1
            else:
                self.performance_stats['hold_signals'] += 1
            
            if confidence > 0.8:
                self.performance_stats['high_confidence_signals'] += 1
                
        except Exception as e:
            self.logger.error(f"更新性能统计失败: {str(e)}")
    
    def get_strategy_status(self) -> Dict[str, Any]:
        """
        获取策略状态
        
        Returns:
            策略状态字典
        """
        try:
            return {
                'strategy_name': 'UnifiedMLStrategy',
                'supported_symbols': self.symbols,
                'last_signals': {symbol: info['timestamp'].isoformat() 
                               for symbol, info in self.last_signals.items()},
                'risk_metrics': self.risk_metrics,
                'performance_stats': self.performance_stats,
                'signal_history_size': len(self.signal_history),
                'prediction_engine_loaded': self.prediction_engine is not None
            }
            
        except Exception as e:
            self.logger.error(f"获取策略状态失败: {str(e)}")
            return {}
    
    def reset_strategy(self) -> None:
        """重置策略状态"""
        try:
            self.last_signals.clear()
            self.position_tracker.clear()
            self.risk_metrics.clear()
            self.signal_history.clear()
            
            # 重置性能统计
            for key in self.performance_stats:
                self.performance_stats[key] = 0
            
            self.logger.info("策略状态已重置")
            
        except Exception as e:
            self.logger.error(f"重置策略失败: {str(e)}")