"""
模型训练器模块

实现多种机器学习模型的训练功能，支持超参数优化、
模型选择和与DeepSeek蒸馏模型的集成。

Author: Crypto ML Strategy Team
Date: 2024-12-19
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any, Union
from datetime import datetime, timedelta
import logging
import time
import asyncio
from concurrent.futures import ThreadPoolExecutor
import threading

# 机器学习库
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV, RandomizedSearchCV
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score
from sklearn.linear_model import LinearRegression, LogisticRegression, Ridge, Lasso
from sklearn.ensemble import RandomForestClassifier, RandomForestRegressor
from sklearn.svm import SVC, SVR
from sklearn.neural_network import MLPClassifier, MLPRegressor

from .training_config import (
    ModelType, TrainingMode, TrainingConfig, ModelConfig, FeatureConfig,
    TrainingResult, PerformanceMetrics, TrainingPipelineConfig
)
from .data_pipeline import DataPipeline
from .model_manager import ModelManager

logger = logging.getLogger(__name__)


class ModelTrainer:
    """模型训练器类"""
    
    def __init__(self, config: TrainingPipelineConfig):
        """
        初始化模型训练器
        
        Args:
            config: 训练管道配置
        """
        self.config = config
        self.training_config = config.training_config
        self.model_config = config.model_config
        
        # 初始化组件
        self.data_pipeline = DataPipeline(config)
        self.model_manager = ModelManager(config)
        
        # 训练状态
        self.is_training = False
        self.current_training_id: Optional[str] = None
        self.training_progress: Dict[str, Any] = {}
        
        # 线程池
        self.executor = ThreadPoolExecutor(max_workers=2)
        
        # 锁
        self._lock = threading.RLock()
        
        logger.info("Model trainer initialized")
    
    def _create_model(self, model_type: ModelType, hyperparameters: Dict[str, Any]) -> Any:
        """
        创建模型实例
        
        Args:
            model_type: 模型类型
            hyperparameters: 超参数
            
        Returns:
            模型实例
        """
        try:
            random_state = self.model_config.random_state
            
            if model_type == ModelType.LINEAR_REGRESSION:
                return LinearRegression(**hyperparameters)
            
            elif model_type == ModelType.LOGISTIC_REGRESSION:
                return LogisticRegression(random_state=random_state, **hyperparameters)
            
            elif model_type == ModelType.RANDOM_FOREST:
                if 'n_estimators' not in hyperparameters:
                    hyperparameters['n_estimators'] = 100
                return RandomForestClassifier(random_state=random_state, **hyperparameters)
            
            elif model_type == ModelType.XGBOOST:
                try:
                    import xgboost as xgb
                    if 'random_state' not in hyperparameters:
                        hyperparameters['random_state'] = random_state
                    return xgb.XGBClassifier(**hyperparameters)
                except ImportError:
                    logger.warning("XGBoost not available, falling back to RandomForest")
                    return RandomForestClassifier(random_state=random_state, n_estimators=100)
            
            elif model_type == ModelType.LIGHTGBM:
                try:
                    import lightgbm as lgb
                    if 'random_state' not in hyperparameters:
                        hyperparameters['random_state'] = random_state
                    return lgb.LGBMClassifier(**hyperparameters)
                except ImportError:
                    logger.warning("LightGBM not available, falling back to RandomForest")
                    return RandomForestClassifier(random_state=random_state, n_estimators=100)
            
            elif model_type == ModelType.CATBOOST:
                try:
                    from catboost import CatBoostClassifier
                    if 'random_state' not in hyperparameters:
                        hyperparameters['random_state'] = random_state
                    if 'verbose' not in hyperparameters:
                        hyperparameters['verbose'] = False
                    return CatBoostClassifier(**hyperparameters)
                except ImportError:
                    logger.warning("CatBoost not available, falling back to RandomForest")
                    return RandomForestClassifier(random_state=random_state, n_estimators=100)
            
            elif model_type == ModelType.SVM:
                return SVC(random_state=random_state, **hyperparameters)
            
            elif model_type == ModelType.MLP:
                return MLPClassifier(random_state=random_state, **hyperparameters)
            
            else:
                logger.warning(f"Unsupported model type: {model_type}, using RandomForest")
                return RandomForestClassifier(random_state=random_state, n_estimators=100)
                
        except Exception as e:
            logger.error(f"Error creating model {model_type}: {e}")
            # 回退到简单模型
            return RandomForestClassifier(random_state=random_state, n_estimators=50)
    
    def _prepare_data(
        self,
        features_df: pd.DataFrame,
        target_column: str = "target"
    ) -> Tuple[np.ndarray, np.ndarray, List[str], List[str]]:
        """
        准备训练数据
        
        Args:
            features_df: 特征数据DataFrame
            target_column: 目标列名
            
        Returns:
            (X, y, feature_names, target_names)
        """
        try:
            # 分离特征和目标
            if target_column not in features_df.columns:
                # 如果没有目标列，创建一个简单的分类目标（价格上涨/下跌）
                if 'close' in features_df.columns:
                    features_df[target_column] = (
                        features_df['close'].shift(-1) > features_df['close']
                    ).astype(int)
                else:
                    raise ValueError(f"Target column '{target_column}' not found and cannot create default target")
            
            # 删除包含NaN的行
            features_df = features_df.dropna()
            
            if features_df.empty:
                raise ValueError("No valid data after removing NaN values")
            
            # 分离特征和目标
            feature_columns = [col for col in features_df.columns if col != target_column]
            X = features_df[feature_columns].values
            y = features_df[target_column].values
            
            # 特征名称
            feature_names = feature_columns
            
            # 目标名称
            unique_targets = np.unique(y)
            if len(unique_targets) == 2:
                target_names = ["down", "up"]
            else:
                target_names = [f"class_{i}" for i in unique_targets]
            
            logger.info(f"Prepared data: {X.shape[0]} samples, {X.shape[1]} features")
            return X, y, feature_names, target_names
            
        except Exception as e:
            logger.error(f"Error preparing data: {e}")
            raise
    
    def _evaluate_model(
        self,
        model: Any,
        X_test: np.ndarray,
        y_test: np.ndarray,
        X_train: Optional[np.ndarray] = None,
        y_train: Optional[np.ndarray] = None
    ) -> PerformanceMetrics:
        """
        评估模型性能
        
        Args:
            model: 训练好的模型
            X_test: 测试特征
            y_test: 测试目标
            X_train: 训练特征（可选）
            y_train: 训练目标（可选）
            
        Returns:
            性能指标
        """
        try:
            # 预测
            y_pred = model.predict(X_test)
            
            # 基本指标
            accuracy = accuracy_score(y_test, y_pred)
            precision = precision_score(y_test, y_pred, average='weighted', zero_division=0)
            recall = recall_score(y_test, y_pred, average='weighted', zero_division=0)
            f1 = f1_score(y_test, y_pred, average='weighted', zero_division=0)
            
            # AUC指标（如果是二分类）
            auc_roc = None
            auc_pr = None
            if len(np.unique(y_test)) == 2:
                try:
                    if hasattr(model, 'predict_proba'):
                        y_proba = model.predict_proba(X_test)[:, 1]
                        auc_roc = roc_auc_score(y_test, y_proba)
                    elif hasattr(model, 'decision_function'):
                        y_scores = model.decision_function(X_test)
                        auc_roc = roc_auc_score(y_test, y_scores)
                except Exception as e:
                    logger.warning(f"Could not calculate AUC: {e}")
            
            # 回归指标（如果适用）
            mse = None
            mae = None
            r2_score = None
            
            # 交易相关指标（模拟）
            sharpe_ratio = None
            max_drawdown = None
            win_rate = None
            
            if len(np.unique(y_test)) == 2:
                # 计算胜率
                win_rate = accuracy
                
                # 模拟夏普比率和最大回撤
                if X_train is not None and y_train is not None:
                    try:
                        # 简单的回测模拟
                        returns = self._simulate_trading_returns(model, X_test, y_test)
                        if len(returns) > 0:
                            sharpe_ratio = np.mean(returns) / np.std(returns) * np.sqrt(252) if np.std(returns) > 0 else 0
                            
                            # 计算最大回撤
                            cumulative_returns = np.cumprod(1 + returns)
                            peak = np.maximum.accumulate(cumulative_returns)
                            drawdown = (cumulative_returns - peak) / peak
                            max_drawdown = abs(np.min(drawdown))
                    except Exception as e:
                        logger.warning(f"Could not calculate trading metrics: {e}")
            
            return PerformanceMetrics(
                accuracy=accuracy,
                precision=precision,
                recall=recall,
                f1_score=f1,
                auc_roc=auc_roc,
                auc_pr=auc_pr,
                mse=mse,
                mae=mae,
                r2_score=r2_score,
                sharpe_ratio=sharpe_ratio,
                max_drawdown=max_drawdown,
                win_rate=win_rate
            )
            
        except Exception as e:
            logger.error(f"Error evaluating model: {e}")
            # 返回默认指标
            return PerformanceMetrics(
                accuracy=0.0,
                precision=0.0,
                recall=0.0,
                f1_score=0.0
            )
    
    def _simulate_trading_returns(self, model: Any, X: np.ndarray, y: np.ndarray) -> np.ndarray:
        """模拟交易收益"""
        try:
            predictions = model.predict(X)
            
            # 简单策略：预测上涨时买入，预测下跌时卖出
            # 这里使用随机收益作为示例
            np.random.seed(42)
            base_returns = np.random.normal(0.001, 0.02, len(predictions))
            
            # 根据预测调整收益方向
            trading_returns = []
            for i, (pred, actual) in enumerate(zip(predictions, y)):
                if pred == actual:
                    # 预测正确，获得正收益
                    trading_returns.append(abs(base_returns[i]))
                else:
                    # 预测错误，获得负收益
                    trading_returns.append(-abs(base_returns[i]))
            
            return np.array(trading_returns)
            
        except Exception as e:
            logger.error(f"Error simulating trading returns: {e}")
            return np.array([])
    
    def _optimize_hyperparameters(
        self,
        model_type: ModelType,
        X_train: np.ndarray,
        y_train: np.ndarray,
        cv_folds: int = 5
    ) -> Dict[str, Any]:
        """
        超参数优化
        
        Args:
            model_type: 模型类型
            X_train: 训练特征
            y_train: 训练目标
            cv_folds: 交叉验证折数
            
        Returns:
            最优超参数
        """
        try:
            # 定义超参数搜索空间
            param_grids = {
                ModelType.RANDOM_FOREST: {
                    'n_estimators': [50, 100, 200],
                    'max_depth': [5, 10, 15, None],
                    'min_samples_split': [2, 5, 10],
                    'min_samples_leaf': [1, 2, 4]
                },
                ModelType.XGBOOST: {
                    'n_estimators': [50, 100, 200],
                    'max_depth': [3, 6, 9],
                    'learning_rate': [0.01, 0.1, 0.2],
                    'subsample': [0.8, 0.9, 1.0]
                },
                ModelType.LIGHTGBM: {
                    'n_estimators': [50, 100, 200],
                    'max_depth': [3, 6, 9],
                    'learning_rate': [0.01, 0.1, 0.2],
                    'num_leaves': [31, 50, 100]
                },
                ModelType.SVM: {
                    'C': [0.1, 1, 10],
                    'kernel': ['rbf', 'linear'],
                    'gamma': ['scale', 'auto']
                },
                ModelType.MLP: {
                    'hidden_layer_sizes': [(50,), (100,), (50, 50)],
                    'activation': ['relu', 'tanh'],
                    'alpha': [0.0001, 0.001, 0.01]
                }
            }
            
            param_grid = param_grids.get(model_type, {})
            
            if not param_grid:
                logger.info(f"No hyperparameter grid defined for {model_type}, using defaults")
                return self.model_config.hyperparameters
            
            # 创建基础模型
            base_model = self._create_model(model_type, {})
            
            # 使用RandomizedSearchCV进行快速搜索
            search = RandomizedSearchCV(
                base_model,
                param_grid,
                n_iter=10,  # 限制搜索次数以节省时间
                cv=min(cv_folds, 3),  # 限制CV折数
                scoring='accuracy',
                random_state=self.model_config.random_state,
                n_jobs=-1
            )
            
            search.fit(X_train, y_train)
            
            logger.info(f"Best hyperparameters for {model_type}: {search.best_params_}")
            return search.best_params_
            
        except Exception as e:
            logger.error(f"Error optimizing hyperparameters for {model_type}: {e}")
            return self.model_config.hyperparameters
    
    async def train_model(
        self,
        symbol: str,
        start_time: datetime,
        end_time: datetime,
        target_column: str = "target",
        optimize_hyperparameters: bool = True
    ) -> TrainingResult:
        """
        训练模型
        
        Args:
            symbol: 交易对符号
            start_time: 开始时间
            end_time: 结束时间
            target_column: 目标列名
            optimize_hyperparameters: 是否优化超参数
            
        Returns:
            训练结果
        """
        training_start_time = time.time()
        training_id = f"training_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        try:
            with self._lock:
                self.is_training = True
                self.current_training_id = training_id
                self.training_progress[training_id] = {
                    'status': 'started',
                    'progress': 0,
                    'stage': 'data_preparation'
                }
            
            logger.info(f"Starting model training: {training_id}")
            
            # 1. 准备数据
            logger.info("Preparing features...")
            features_df = await self.data_pipeline.prepare_features(
                symbol, start_time, end_time
            )
            
            if features_df.empty:
                raise ValueError("No features data available")
            
            self.training_progress[training_id].update({
                'progress': 20,
                'stage': 'data_splitting'
            })
            
            # 2. 准备训练数据
            X, y, feature_names, target_names = self._prepare_data(features_df, target_column)
            
            # 3. 数据分割
            X_train, X_test, y_train, y_test = train_test_split(
                X, y,
                test_size=1 - self.training_config.train_test_split,
                random_state=self.model_config.random_state,
                shuffle=self.training_config.shuffle_data,
                stratify=y if len(np.unique(y)) > 1 else None
            )
            
            self.training_progress[training_id].update({
                'progress': 40,
                'stage': 'hyperparameter_optimization'
            })
            
            # 4. 超参数优化
            hyperparameters = self.model_config.hyperparameters.copy()
            if optimize_hyperparameters:
                logger.info("Optimizing hyperparameters...")
                hyperparameters = self._optimize_hyperparameters(
                    self.model_config.model_type,
                    X_train, y_train,
                    self.training_config.cross_validation_folds
                )
            
            self.training_progress[training_id].update({
                'progress': 60,
                'stage': 'model_training'
            })
            
            # 5. 训练模型
            logger.info("Training model...")
            model = self._create_model(self.model_config.model_type, hyperparameters)
            
            # 特征缩放（如果需要）
            scaler = None
            if self.model_config.model_type in [ModelType.SVM, ModelType.MLP, ModelType.LOGISTIC_REGRESSION]:
                scaler = StandardScaler()
                X_train_scaled = scaler.fit_transform(X_train)
                X_test_scaled = scaler.transform(X_test)
                model.fit(X_train_scaled, y_train)
            else:
                model.fit(X_train, y_train)
                X_train_scaled = X_train
                X_test_scaled = X_test
            
            self.training_progress[training_id].update({
                'progress': 80,
                'stage': 'model_evaluation'
            })
            
            # 6. 评估模型
            logger.info("Evaluating model...")
            performance_metrics = self._evaluate_model(
                model, X_test_scaled, y_test, X_train_scaled, y_train
            )
            
            # 训练集性能
            y_train_pred = model.predict(X_train_scaled)
            train_accuracy = accuracy_score(y_train, y_train_pred)
            training_metrics = {'accuracy': train_accuracy}
            
            # 验证集性能
            validation_metrics = {
                'accuracy': performance_metrics.accuracy,
                'precision': performance_metrics.precision,
                'recall': performance_metrics.recall,
                'f1_score': performance_metrics.f1_score
            }
            
            self.training_progress[training_id].update({
                'progress': 90,
                'stage': 'model_saving'
            })
            
            # 7. 保存模型
            training_duration = time.time() - training_start_time
            
            # 如果使用了缩放器，将其与模型一起保存
            model_to_save = {'model': model, 'scaler': scaler} if scaler else model
            
            model_id = self.model_manager.save_model(
                model=model_to_save,
                model_type=self.model_config.model_type,
                performance_metrics=performance_metrics,
                feature_names=feature_names,
                target_names=target_names,
                hyperparameters=hyperparameters,
                training_duration=training_duration,
                description=f"Model trained on {symbol} from {start_time} to {end_time}"
            )
            
            self.training_progress[training_id].update({
                'progress': 100,
                'stage': 'completed'
            })
            
            # 8. 创建训练结果
            result = TrainingResult(
                model_id=model_id,
                success=True,
                training_metrics=training_metrics,
                validation_metrics=validation_metrics,
                test_metrics=validation_metrics,  # 使用测试集作为最终评估
                feature_importance=self._get_feature_importance(model, feature_names),
                training_time=training_duration,
                model_path=str(self.model_manager.model_save_path / model_id)
            )
            
            logger.info(f"Model training completed successfully: {model_id}")
            logger.info(f"Training accuracy: {train_accuracy:.4f}")
            logger.info(f"Test accuracy: {performance_metrics.accuracy:.4f}")
            
            return result
            
        except Exception as e:
            logger.error(f"Error training model: {e}")
            
            # 更新训练进度
            self.training_progress[training_id].update({
                'status': 'failed',
                'error': str(e)
            })
            
            return TrainingResult(
                model_id="",
                success=False,
                training_metrics={},
                validation_metrics={},
                error_message=str(e)
            )
            
        finally:
            with self._lock:
                self.is_training = False
                self.current_training_id = None
    
    def _get_feature_importance(self, model: Any, feature_names: List[str]) -> Optional[Dict[str, float]]:
        """获取特征重要性"""
        try:
            # 处理包含scaler的模型
            if isinstance(model, dict) and 'model' in model:
                actual_model = model['model']
            else:
                actual_model = model
            
            if hasattr(actual_model, 'feature_importances_'):
                importances = actual_model.feature_importances_
                return dict(zip(feature_names, importances.tolist()))
            elif hasattr(actual_model, 'coef_'):
                # 线性模型的系数
                coef = actual_model.coef_
                if coef.ndim > 1:
                    coef = coef[0]  # 取第一行（二分类情况）
                return dict(zip(feature_names, np.abs(coef).tolist()))
            else:
                return None
                
        except Exception as e:
            logger.error(f"Error getting feature importance: {e}")
            return None
    
    def get_training_status(self, training_id: Optional[str] = None) -> Dict[str, Any]:
        """获取训练状态"""
        try:
            if training_id:
                return self.training_progress.get(training_id, {'status': 'not_found'})
            else:
                return {
                    'is_training': self.is_training,
                    'current_training_id': self.current_training_id,
                    'active_trainings': len([
                        t for t in self.training_progress.values() 
                        if t.get('status') not in ['completed', 'failed']
                    ])
                }
        except Exception as e:
            logger.error(f"Error getting training status: {e}")
            return {'error': str(e)}
    
    def stop_training(self, training_id: Optional[str] = None) -> bool:
        """停止训练"""
        try:
            # 注意：这是一个简化的实现
            # 实际的停止训练需要更复杂的逻辑
            with self._lock:
                if training_id and training_id in self.training_progress:
                    self.training_progress[training_id]['status'] = 'stopped'
                    return True
                elif self.current_training_id:
                    self.training_progress[self.current_training_id]['status'] = 'stopped'
                    self.is_training = False
                    self.current_training_id = None
                    return True
                else:
                    return False
        except Exception as e:
            logger.error(f"Error stopping training: {e}")
            return False
