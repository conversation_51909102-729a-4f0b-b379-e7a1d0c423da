package com.crypto.trading.sdk.converter;

import com.crypto.trading.common.util.JsonUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Jackson实现的JSON转换器
 * <p>
 * 使用JsonUtil工具类实现JSON和Java对象之间的转换。
 * 该实现基于Jackson库，提供了JSON序列化和反序列化的功能。
 * 注意：与JsonUtil不同，该实现在遇到异常时会抛出RuntimeException，
 * 而不是返回null或空集合，这是为了确保调用方能够感知并处理转换错误。
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
public class JacksonJsonConverter implements JsonConverter {

    private static final Logger log = LoggerFactory.getLogger(JacksonJsonConverter.class);

    /**
     * 将JSON字符串转换为Java对象
     * <p>
     * 使用JsonUtil.parseJson方法实现转换。
     * 如果JSON字符串为null或空，则返回null。
     * 如果转换过程中发生异常，会记录错误日志并抛出RuntimeException。
     * </p>
     *
     * @param json  JSON字符串
     * @param clazz 目标类型
     * @param <T>   返回类型
     * @return 转换后的对象
     * @throws RuntimeException 如果转换过程中发生异常
     */
    @Override
    public <T> T fromJson(String json, Class<T> clazz) {
        try {
            if (json == null || json.isEmpty()) {
                return null;
            }
            return JsonUtil.parseJson(json, clazz);
        } catch (Exception e) {
            log.error("JSON转换异常: {} -> {}", json, clazz.getName(), e);
            throw new RuntimeException("JSON转换异常", e);
        }
    }

    /**
     * 将JSON字符串转换为Java对象列表
     * <p>
     * 使用JsonUtil.parseJsonToList方法实现转换。
     * 如果JSON字符串为null或空，则返回空列表。
     * 如果转换过程中发生异常，会记录错误日志并抛出RuntimeException。
     * </p>
     *
     * @param json  JSON字符串
     * @param clazz 列表元素类型
     * @param <T>   元素类型
     * @return 转换后的对象列表
     * @throws RuntimeException 如果转换过程中发生异常
     */
    @Override
    public <T> List<T> fromJsonArray(String json, Class<T> clazz) {
        try {
            if (json == null || json.isEmpty()) {
                return new ArrayList<>();
            }
            return JsonUtil.parseJsonToList(json, clazz);
        } catch (Exception e) {
            log.error("JSON数组转换异常: {} -> {}", json, clazz.getName(), e);
            throw new RuntimeException("JSON数组转换异常", e);
        }
    }

    /**
     * 将Java对象转换为JSON字符串
     * <p>
     * 使用JsonUtil.toJsonString方法实现转换。
     * 如果对象为null，则返回"{}"。
     * 如果转换过程中发生异常，会记录错误日志并抛出RuntimeException。
     * </p>
     *
     * @param object 要转换的对象
     * @return JSON字符串
     * @throws RuntimeException 如果转换过程中发生异常
     */
    @Override
    public String toJson(Object object) {
        try {
            if (object == null) {
                return "{}";
            }
            return JsonUtil.toJsonString(object);
        } catch (Exception e) {
            log.error("对象转JSON异常: {}", object.getClass().getName(), e);
            throw new RuntimeException("对象转JSON异常", e);
        }
    }

    /**
     * 将JSON字符串转换为Map
     * <p>
     * 使用JsonUtil.parseJsonToMap方法实现转换。
     * 如果JSON字符串为null或空，则返回空Map。
     * 如果转换过程中发生异常，会记录错误日志并抛出RuntimeException。
     * </p>
     *
     * @param json JSON字符串
     * @return 转换后的Map对象，键为String类型，值为Object类型
     * @throws RuntimeException 如果转换过程中发生异常
     */
    @Override
    public Map<String, Object> jsonToMap(String json) {
        try {
            if (json == null || json.isEmpty()) {
                return Map.of();
            }
            return JsonUtil.parseJsonToMap(json);
        } catch (Exception e) {
            log.error("JSON转Map异常: {}", json, e);
            throw new RuntimeException("JSON转Map异常", e);
        }
    }

    /**
     * 将对象转换为美化的JSON字符串
     *
     * @param obj 要转换的对象
     * @return 美化的JSON字符串
     */
    public String toPrettyJson(Object obj) {
        try {
            return JsonUtil.getObjectMapper().writerWithDefaultPrettyPrinter().writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            log.error("对象转美化的JSON字符串失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 将JSON字符串转换为指定TypeReference类型的对象
     *
     * @param json          JSON字符串
     * @param typeReference 类型引用
     * @param <T>           目标类型
     * @return 转换后的对象
     */
    public <T> T fromJson(String json, TypeReference<T> typeReference) {
        return JsonUtil.parseJson(json, typeReference);
    }

    /**
     * 将源对象转换为目标类型的对象
     *
     * @param source      源对象
     * @param targetClass 目标类型
     * @param <T>         目标类型
     * @return 转换后的对象
     */
    public <T> T convert(Object source, Class<T> targetClass) {
        if (source == null) {
            return null;
        }
        try {
            String json = toJson(source);
            return fromJson(json, targetClass);
        } catch (Exception e) {
            log.error("对象转换失败: {}", e.getMessage(), e);
            return null;
        }
    }
}