<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.crypto.trading.trade.repository.mapper.order.status.OrderStatusMapper">

    <!-- 基础映射结果集 -->
    <resultMap id="BaseResultMap" type="com.crypto.trading.trade.model.entity.order.status.OrderStatusEntity">
        <id column="id" property="id"/>
        <result column="order_id" property="orderId"/>
        <result column="client_order_id" property="clientOrderId"/>
        <result column="old_status" property="oldStatus"/>
        <result column="new_status" property="newStatus"/>
        <result column="reason" property="reason"/>
        <result column="details" property="details"/>
        <result column="update_time" property="updateTime"/>
        <result column="created_time" property="createdTime"/>
    </resultMap>

    <!-- 基础字段列表 -->
    <sql id="Base_Column_List">
        id, order_id, client_order_id, old_status, new_status, reason, details, update_time, created_time
    </sql>

    <!-- 分页查询订单状态历史 -->
    <select id="pageOrderStatus" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_order_status
        <where>
            <if test="orderId != null and orderId != ''">
                AND order_id = #{orderId}
            </if>
            <if test="newStatus != null and newStatus != ''">
                AND new_status = #{newStatus}
            </if>
            <if test="startTime != null">
                AND update_time &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                AND update_time &lt;= #{endTime}
            </if>
        </where>
        ORDER BY update_time DESC
    </select>
    
    <!-- 批量插入订单状态 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO t_order_status (
            order_id, client_order_id, old_status, new_status, reason, details, update_time, created_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.orderId},
                #{item.clientOrderId},
                #{item.oldStatus},
                #{item.newStatus},
                #{item.reason},
                #{item.details},
                #{item.updateTime},
                #{item.createdTime}
            )
        </foreach>
    </insert>
    
    <!-- 根据多个订单ID查询最新状态 -->
    <select id="findLatestStatusByOrderIds" resultMap="BaseResultMap">
        SELECT t.*
        FROM t_order_status t
        INNER JOIN (
            SELECT order_id, MAX(update_time) as max_time
            FROM t_order_status
            WHERE order_id IN
            <foreach collection="orderIds" item="orderId" open="(" separator="," close=")">
                #{orderId}
            </foreach>
            GROUP BY order_id
        ) m ON t.order_id = m.order_id AND t.update_time = m.max_time
        ORDER BY t.update_time DESC
    </select>
    
    <!-- 统计一段时间内各状态的订单数量 -->
    <select id="countStatusDistribution" resultType="java.util.Map">
        SELECT 
            new_status as status,
            COUNT(DISTINCT order_id) as count
        FROM t_order_status
        WHERE update_time BETWEEN #{startTime} AND #{endTime}
        GROUP BY new_status
    </select>
    
    <!-- 查询状态变更频繁的订单 -->
    <select id="findFrequentStatusChangeOrders" resultType="java.util.Map">
        SELECT 
            order_id,
            COUNT(*) as change_count
        FROM t_order_status
        WHERE update_time BETWEEN #{startTime} AND #{endTime}
        GROUP BY order_id
        HAVING COUNT(*) >= #{minChangeCount}
        ORDER BY change_count DESC
        LIMIT #{limit}
    </select>
    
    <!-- 查询特定状态转换的订单 -->
    <select id="findStatusTransitionOrders" resultMap="BaseResultMap">
        SELECT 
            <include refid="Base_Column_List"/>
        FROM t_order_status
        WHERE old_status = #{fromStatus}
        AND new_status = #{toStatus}
        AND update_time BETWEEN #{startTime} AND #{endTime}
        ORDER BY update_time DESC
        LIMIT #{limit}
    </select>
</mapper>