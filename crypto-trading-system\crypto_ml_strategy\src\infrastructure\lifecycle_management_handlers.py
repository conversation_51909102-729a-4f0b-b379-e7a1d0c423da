#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
生命周期管理处理器模块

包含信号处理、内存告警处理和辅助方法，
支持生命周期管理的完整功能。
"""

import signal
import sys
import asyncio
from typing import Dict, Any
from loguru import logger


class LifecycleHandlers:
    """
    生命周期处理器类
    
    提供信号处理、内存告警处理等辅助功能。
    """
    
    def __init__(self, core_manager):
        """
        初始化生命周期处理器
        
        Args:
            core_manager: 核心生命周期管理器实例
        """
        self.core_manager = core_manager
        logger.info("生命周期处理器初始化完成")
    
    def setup_signal_handlers(self) -> None:
        """设置信号处理器"""
        def signal_handler(signum, frame):
            logger.info(f"收到信号 {signum}，开始优雅关闭...")
            
            # 创建异步任务来处理关闭
            asyncio.create_task(self._graceful_shutdown())
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        logger.info("信号处理器已设置")
    
    async def _graceful_shutdown(self) -> None:
        """优雅关闭"""
        try:
            # 设置关闭超时
            shutdown_task = asyncio.create_task(self.core_manager.stop_application())
            
            try:
                await asyncio.wait_for(shutdown_task, timeout=30.0)
            except asyncio.TimeoutError:
                logger.warning("优雅关闭超时，强制退出")
            
            sys.exit(0)
            
        except Exception as e:
            logger.error(f"优雅关闭失败: {e}")
            sys.exit(1)
    
    async def start_memory_monitoring(self) -> None:
        """启动内存监控"""
        try:
            # 添加内存告警回调
            self.core_manager.memory_monitor.add_alert_callback(self.handle_memory_alert)
            
            # 启动内存监控
            self.core_manager.memory_monitor.start_monitoring()
            
            logger.info("内存监控已启动")
            
        except Exception as e:
            logger.error(f"内存监控启动失败: {e}")
            raise
    
    async def stop_memory_monitoring(self) -> None:
        """停止内存监控"""
        try:
            self.core_manager.memory_monitor.stop_monitoring()
            logger.info("内存监控已停止")
            
        except Exception as e:
            logger.warning(f"内存监控停止失败: {e}")
    
    async def start_health_checks(self) -> None:
        """启动健康检查"""
        try:
            # 注册内存健康检查
            def memory_health_check():
                status = self.core_manager.memory_monitor.get_current_status()
                return {
                    "healthy": status["system_memory"]["used_percent"] < 90,
                    "memory_usage_percent": status["system_memory"]["used_percent"],
                    "process_memory_mb": status["process_memory"]["used_mb"]
                }
            
            self.core_manager.health_check_service.register_health_check("memory", memory_health_check)
            
            logger.info("健康检查已启动")
            
        except Exception as e:
            logger.warning(f"健康检查启动失败: {e}")
    
    async def start_base_lifecycle_manager(self) -> None:
        """启动基础生命周期管理器"""
        try:
            self.core_manager.base_lifecycle_manager.start()
            logger.info("基础生命周期管理器已启动")
            
        except Exception as e:
            logger.error(f"基础生命周期管理器启动失败: {e}")
            raise
    
    async def stop_base_lifecycle_manager(self) -> None:
        """停止基础生命周期管理器"""
        try:
            self.core_manager.base_lifecycle_manager.stop()
            logger.info("基础生命周期管理器已停止")
            
        except Exception as e:
            logger.warning(f"基础生命周期管理器停止失败: {e}")
    
    async def cleanup_async_services(self) -> None:
        """清理异步服务"""
        try:
            # 清理已完成的任务
            cleaned_count = self.core_manager.async_error_recovery.cleanup_completed_tasks(max_age_hours=1)
            logger.info(f"清理了 {cleaned_count} 个已完成的异步任务")
            
        except Exception as e:
            logger.warning(f"异步服务清理失败: {e}")
    
    def handle_memory_alert(self, alert: Dict[str, Any]) -> None:
        """
        处理内存告警
        
        Args:
            alert: 内存告警信息
        """
        logger.warning(f"收到内存告警: {alert['message']}")
        
        try:
            if alert['type'] == 'high_memory_usage':
                # 触发内存优化
                optimization_result = self.core_manager.memory_monitor.optimize_memory()
                logger.info(f"内存优化完成: {optimization_result}")
                
                # 记录内存告警指标
                self.core_manager.metrics_service.increment_counter("memory_alerts_high_usage")
                
            elif alert['type'] == 'memory_leak_detected':
                # 记录内存泄漏告警
                self.core_manager.metrics_service.increment_counter("memory_alerts_leak_detected")
                logger.error(f"检测到内存泄漏: {alert['details']}")
                
        except Exception as e:
            logger.error(f"内存告警处理失败: {e}")