"""
Crypto ML Strategy - 性能基准测试框架核心

该模块实现了性能基准测试框架的核心功能，提供完整的基准测试管理、
配置管理、测试执行和结果处理能力。

主要功能：
- BenchmarkFramework: 性能基准测试框架主管理器
- BenchmarkSuite: 基准测试套件，管理多个测试用例
- BenchmarkTest: 单个基准测试用例基类
- BenchmarkConfig: 基准测试配置数据结构
- BenchmarkResult: 基准测试结果数据结构

作者: Crypto ML Strategy Team
日期: 2024-01-20
版本: 1.0.0
"""

import asyncio
import time
import threading
import uuid
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional, Union, Callable, Type
from infrastructure.logging.logging_core_manager import get_logger
from benchmarks.performance_logging_core import get_global_performance_logger
from infrastructure.error_handling_system import get_global_error_handler
import yaml
import json


class BenchmarkStatus(Enum):
    """基准测试状态"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class BenchmarkPriority(Enum):
    """基准测试优先级"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4


@dataclass
class BenchmarkConfig:
    """基准测试配置"""
    name: str
    description: str = ""
    timeout_seconds: int = 300
    warmup_iterations: int = 100
    measurement_iterations: int = 1000
    parallel_execution: bool = True
    max_workers: int = 4
    priority: BenchmarkPriority = BenchmarkPriority.NORMAL
    
    # 测试套件配置
    test_suites: Dict[str, Dict[str, Any]] = field(default_factory=dict)
    
    # 指标配置
    metrics_config: Dict[str, Any] = field(default_factory=lambda: {
        "collection_interval_ms": 100,
        "storage_retention_days": 30,
        "aggregation_windows": ["1m", "5m", "15m", "1h"]
    })
    
    # 报告配置
    reporting_config: Dict[str, Any] = field(default_factory=lambda: {
        "formats": ["json", "html"],
        "include_charts": True,
        "include_trends": True,
        "include_comparisons": True
    })
    
    # 告警配置
    alert_config: Dict[str, Any] = field(default_factory=lambda: {
        "performance_degradation_threshold": 20,  # 20%
        "error_rate_threshold": 5,  # 5%
        "latency_threshold_ms": 100,
        "memory_usage_threshold_mb": 1000
    })
    
    def validate(self) -> bool:
        """验证配置有效性"""
        try:
            if self.timeout_seconds <= 0 or self.measurement_iterations <= 0:
                return False
            
            if self.max_workers <= 0:
                return False
            
            if not self.name:
                return False
            
            return True
        except Exception:
            return False
    
    @classmethod
    def from_file(cls, config_path: Union[str, Path]) -> 'BenchmarkConfig':
        """从文件加载配置"""
        config_file = Path(config_path)
        
        if not config_file.exists():
            raise FileNotFoundError(f"Config file not found: {config_path}")
        
        with open(config_file, 'r', encoding='utf-8') as f:
            if config_path.endswith('.yaml') or config_path.endswith('.yml'):
                data = yaml.safe_load(f)
            elif config_path.endswith('.json'):
                data = json.load(f)
            else:
                raise ValueError("Unsupported config file format")
        
        benchmark_data = data.get('benchmark', {})
        
        return cls(
            name=benchmark_data.get('name', 'default'),
            description=benchmark_data.get('description', ''),
            timeout_seconds=benchmark_data.get('global_settings', {}).get('timeout_seconds', 300),
            warmup_iterations=benchmark_data.get('global_settings', {}).get('warmup_iterations', 100),
            measurement_iterations=benchmark_data.get('global_settings', {}).get('measurement_iterations', 1000),
            parallel_execution=benchmark_data.get('global_settings', {}).get('parallel_execution', True),
            max_workers=benchmark_data.get('global_settings', {}).get('max_workers', 4),
            test_suites=benchmark_data.get('test_suites', {}),
            metrics_config=benchmark_data.get('metrics', {}),
            reporting_config=benchmark_data.get('reporting', {}),
            alert_config=benchmark_data.get('alerts', {})
        )


@dataclass
class BenchmarkMetrics:
    """基准测试指标"""
    execution_time_ms: float
    memory_usage_mb: float
    cpu_usage_percent: float
    throughput_ops_per_sec: float
    latency_p50_ms: float
    latency_p95_ms: float
    latency_p99_ms: float
    error_rate: float
    success_rate: float
    concurrent_connections: int
    custom_metrics: Dict[str, float] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "execution_time_ms": self.execution_time_ms,
            "memory_usage_mb": self.memory_usage_mb,
            "cpu_usage_percent": self.cpu_usage_percent,
            "throughput_ops_per_sec": self.throughput_ops_per_sec,
            "latency_p50_ms": self.latency_p50_ms,
            "latency_p95_ms": self.latency_p95_ms,
            "latency_p99_ms": self.latency_p99_ms,
            "error_rate": self.error_rate,
            "success_rate": self.success_rate,
            "concurrent_connections": self.concurrent_connections,
            "custom_metrics": self.custom_metrics
        }


@dataclass
class BenchmarkResult:
    """基准测试结果"""
    test_id: str
    test_name: str
    status: BenchmarkStatus
    start_time: datetime
    end_time: Optional[datetime]
    duration_seconds: float
    metrics: Optional[BenchmarkMetrics]
    error_message: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "test_id": self.test_id,
            "test_name": self.test_name,
            "status": self.status.value,
            "start_time": self.start_time.isoformat(),
            "end_time": self.end_time.isoformat() if self.end_time else None,
            "duration_seconds": self.duration_seconds,
            "metrics": self.metrics.to_dict() if self.metrics else None,
            "error_message": self.error_message,
            "metadata": self.metadata
        }


class BenchmarkTest(ABC):
    """基准测试用例基类"""
    
    def __init__(self, name: str, description: str = "", 
                 timeout_seconds: int = 60, priority: BenchmarkPriority = BenchmarkPriority.NORMAL):
        self.name = name
        self.description = description
        self.timeout_seconds = timeout_seconds
        self.priority = priority
        self.test_id = str(uuid.uuid4())
        
        # 日志和性能监控
        self.logger = get_logger(f"benchmark.{self.__class__.__name__}")
        self.perf_logger = get_global_performance_logger()
        self.error_handler = get_global_error_handler()
        
        # 测试状态
        self._status = BenchmarkStatus.PENDING
        self._start_time: Optional[datetime] = None
        self._end_time: Optional[datetime] = None
        self._metrics: Optional[BenchmarkMetrics] = None
        self._error_message: Optional[str] = None
    
    @abstractmethod
    async def setup(self) -> None:
        """测试前设置"""
        pass
    
    @abstractmethod
    async def run_test(self) -> BenchmarkMetrics:
        """执行测试"""
        pass
    
    @abstractmethod
    async def teardown(self) -> None:
        """测试后清理"""
        pass
    
    async def execute(self) -> BenchmarkResult:
        """执行完整的测试流程"""
        self._status = BenchmarkStatus.RUNNING
        self._start_time = datetime.now()
        
        try:
            self.logger.info(f"Starting benchmark test: {self.name}")
            
            # 设置阶段
            await self.setup()
            
            # 执行测试
            self._metrics = await asyncio.wait_for(
                self.run_test(), 
                timeout=self.timeout_seconds
            )
            
            # 清理阶段
            await self.teardown()
            
            self._status = BenchmarkStatus.COMPLETED
            self.logger.info(f"Benchmark test completed: {self.name}")
            
        except asyncio.TimeoutError:
            self._status = BenchmarkStatus.FAILED
            self._error_message = f"Test timeout after {self.timeout_seconds} seconds"
            self.logger.error(f"Benchmark test timeout: {self.name}")
            
        except Exception as e:
            self._status = BenchmarkStatus.FAILED
            self._error_message = str(e)
            self.error_handler.handle_error(e, context={"test_name": self.name})
            self.logger.error(f"Benchmark test failed: {self.name}, error: {e}")
            
        finally:
            self._end_time = datetime.now()
        
        duration = (self._end_time - self._start_time).total_seconds()
        
        return BenchmarkResult(
            test_id=self.test_id,
            test_name=self.name,
            status=self._status,
            start_time=self._start_time,
            end_time=self._end_time,
            duration_seconds=duration,
            metrics=self._metrics,
            error_message=self._error_message,
            metadata={
                "description": self.description,
                "timeout_seconds": self.timeout_seconds,
                "priority": self.priority.value
            }
        )


class BenchmarkSuite:
    """基准测试套件"""
    
    def __init__(self, name: str, description: str = ""):
        self.name = name
        self.description = description
        self.tests: List[BenchmarkTest] = []
        self.logger = get_logger(f"benchmark.suite.{name}")
        self._lock = threading.RLock()
    
    def add_test(self, test: BenchmarkTest) -> None:
        """添加测试用例"""
        with self._lock:
            self.tests.append(test)
            self.logger.debug(f"Added test to suite {self.name}: {test.name}")
    
    def remove_test(self, test_name: str) -> bool:
        """移除测试用例"""
        with self._lock:
            for i, test in enumerate(self.tests):
                if test.name == test_name:
                    removed_test = self.tests.pop(i)
                    self.logger.debug(f"Removed test from suite {self.name}: {removed_test.name}")
                    return True
            return False
    
    def get_test(self, test_name: str) -> Optional[BenchmarkTest]:
        """获取测试用例"""
        with self._lock:
            for test in self.tests:
                if test.name == test_name:
                    return test
            return None
    
    def list_tests(self) -> List[str]:
        """列出所有测试用例名称"""
        with self._lock:
            return [test.name for test in self.tests]
    
    async def run_all_tests(self, parallel: bool = True, max_workers: int = 4) -> List[BenchmarkResult]:
        """运行所有测试用例"""
        if not self.tests:
            self.logger.warning(f"No tests in suite: {self.name}")
            return []
        
        self.logger.info(f"Running benchmark suite: {self.name} ({len(self.tests)} tests)")
        
        if parallel and len(self.tests) > 1:
            return await self._run_tests_parallel(max_workers)
        else:
            return await self._run_tests_sequential()
    
    async def _run_tests_sequential(self) -> List[BenchmarkResult]:
        """顺序执行测试"""
        results = []
        
        for test in self.tests:
            result = await test.execute()
            results.append(result)
        
        return results
    
    async def _run_tests_parallel(self, max_workers: int) -> List[BenchmarkResult]:
        """并行执行测试"""
        semaphore = asyncio.Semaphore(max_workers)
        
        async def run_with_semaphore(test: BenchmarkTest) -> BenchmarkResult:
            async with semaphore:
                return await test.execute()
        
        tasks = [run_with_semaphore(test) for test in self.tests]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理异常结果
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                # 创建失败结果
                test = self.tests[i]
                processed_results.append(BenchmarkResult(
                    test_id=test.test_id,
                    test_name=test.name,
                    status=BenchmarkStatus.FAILED,
                    start_time=datetime.now(),
                    end_time=datetime.now(),
                    duration_seconds=0,
                    metrics=None,
                    error_message=str(result)
                ))
            else:
                processed_results.append(result)
        
        return processed_results


class BenchmarkFramework:
    """性能基准测试框架主管理器"""
    
    def __init__(self, config: Optional[BenchmarkConfig] = None):
        self.config = config or BenchmarkConfig(name="default")
        self.suites: Dict[str, BenchmarkSuite] = {}
        self.results_history: List[BenchmarkResult] = []
        
        # 日志和监控
        self.logger = get_logger("benchmark.framework")
        self.perf_logger = get_global_performance_logger()
        self.error_handler = get_global_error_handler()
        
        # 线程安全
        self._lock = threading.RLock()
        
        # 初始化
        self._initialize()
    
    def _initialize(self) -> None:
        """初始化框架"""
        try:
            if not self.config.validate():
                raise ValueError("Invalid benchmark configuration")
            
            self.logger.info(f"Benchmark framework initialized: {self.config.name}")
            
        except Exception as e:
            self.error_handler.handle_error(e, context={"config": self.config.name})
            raise
    
    def add_suite(self, suite: BenchmarkSuite) -> None:
        """添加测试套件"""
        with self._lock:
            self.suites[suite.name] = suite
            self.logger.info(f"Added benchmark suite: {suite.name}")
    
    def remove_suite(self, suite_name: str) -> bool:
        """移除测试套件"""
        with self._lock:
            if suite_name in self.suites:
                del self.suites[suite_name]
                self.logger.info(f"Removed benchmark suite: {suite_name}")
                return True
            return False
    
    def get_suite(self, suite_name: str) -> Optional[BenchmarkSuite]:
        """获取测试套件"""
        with self._lock:
            return self.suites.get(suite_name)
    
    def list_suites(self) -> List[str]:
        """列出所有测试套件"""
        with self._lock:
            return list(self.suites.keys())
    
    @perf_logger.monitor
    async def run_suite(self, suite_name: str) -> List[BenchmarkResult]:
        """运行指定测试套件"""
        suite = self.get_suite(suite_name)
        if not suite:
            raise ValueError(f"Suite not found: {suite_name}")
        
        self.logger.info(f"Running benchmark suite: {suite_name}")
        
        results = await suite.run_all_tests(
            parallel=self.config.parallel_execution,
            max_workers=self.config.max_workers
        )
        
        # 保存结果
        with self._lock:
            self.results_history.extend(results)
        
        self.logger.info(f"Completed benchmark suite: {suite_name} ({len(results)} tests)")
        
        return results
    
    @perf_logger.monitor
    async def run_all_suites(self) -> Dict[str, List[BenchmarkResult]]:
        """运行所有测试套件"""
        all_results = {}
        
        for suite_name in self.list_suites():
            try:
                results = await self.run_suite(suite_name)
                all_results[suite_name] = results
            except Exception as e:
                self.error_handler.handle_error(e, context={"suite_name": suite_name})
                self.logger.error(f"Failed to run suite {suite_name}: {e}")
        
        return all_results
    
    def get_results_summary(self) -> Dict[str, Any]:
        """获取结果摘要"""
        with self._lock:
            if not self.results_history:
                return {"summary": "no_results"}
            
            total_tests = len(self.results_history)
            completed_tests = sum(1 for r in self.results_history if r.status == BenchmarkStatus.COMPLETED)
            failed_tests = sum(1 for r in self.results_history if r.status == BenchmarkStatus.FAILED)
            
            avg_duration = sum(r.duration_seconds for r in self.results_history) / total_tests
            
            return {
                "total_tests": total_tests,
                "completed_tests": completed_tests,
                "failed_tests": failed_tests,
                "success_rate": completed_tests / total_tests if total_tests > 0 else 0,
                "average_duration_seconds": avg_duration,
                "total_suites": len(self.suites)
            }


# 全局基准测试框架实例
_global_benchmark_framework: Optional[BenchmarkFramework] = None


def get_global_benchmark_framework() -> BenchmarkFramework:
    """获取全局基准测试框架实例"""
    global _global_benchmark_framework
    
    if _global_benchmark_framework is None:
        _global_benchmark_framework = BenchmarkFramework()
    
    return _global_benchmark_framework


# 便捷装饰器
def benchmark_test(name: str, description: str = "", timeout_seconds: int = 60,
                  priority: BenchmarkPriority = BenchmarkPriority.NORMAL):
    """基准测试装饰器"""
    def decorator(test_class: Type[BenchmarkTest]) -> Type[BenchmarkTest]:
        # 设置测试属性
        test_class._benchmark_name = name
        test_class._benchmark_description = description
        test_class._benchmark_timeout = timeout_seconds
        test_class._benchmark_priority = priority
        
        return test_class
    
    return decorator


# 模块级别的日志器
module_logger = get_logger(__name__)