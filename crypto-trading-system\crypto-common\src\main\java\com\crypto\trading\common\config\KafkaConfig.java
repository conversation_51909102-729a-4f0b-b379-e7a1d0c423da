package com.crypto.trading.common.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

import io.confluent.kafka.serializers.AbstractKafkaSchemaSerDeConfig;

/**
 * Kafka配置类
 * <p>
 * 用于配置Kafka的相关参数，包括生产者和消费者配置。
 * 配置项从application-common.yml的spring.kafka前缀节点下读取。
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Configuration
@ConfigurationProperties(prefix = "spring.kafka")
public class KafkaConfig {
    
    private static final Logger log = LoggerFactory.getLogger(KafkaConfig.class);
    
    /**
     * Kafka服务器地址，多个地址用逗号分隔
     */
    private String bootstrapServers;

    /**
     * Schema Registry URL
     */
    private String schemaRegistryUrl = "http://localhost:8081";
    
    /**
     * 是否启用Avro序列化
     */
    private boolean avroSerializationEnabled = true;
    
    /**
     * 默认主题分区数
     */
    private int defaultPartitions = 3;
    
    /**
     * 默认副本因子
     */
    private short defaultReplicationFactor = 1;
    
    /**
     * 生产者配置
     */
    private Producer producer = new Producer();
    
    /**
     * 消费者配置
     */
    private Consumer consumer = new Consumer();
    
    /**
     * 当配置加载完成后的初始化操作
     */
    public void init() {
        log.info("Kafka配置加载完成: bootstrapServers={}", bootstrapServers);
        log.info("Schema Registry配置: schemaRegistryUrl={}, avroSerializationEnabled={}", 
                schemaRegistryUrl, avroSerializationEnabled);
        log.info("分区和副本配置: defaultPartitions={}, defaultReplicationFactor={}", 
                defaultPartitions, defaultReplicationFactor);
        log.info("生产者配置: keySerializer={}, valueSerializer={}, acks={}, retries={}, batchSize={}, lingerMs={}, compressionType={}, enableIdempotence={}",
                producer.getKeySerializer(), producer.getValueSerializer(), 
                producer.getAcks(), producer.getRetries(), producer.getBatchSize(), 
                producer.getLingerMs(), producer.getCompressionType(), producer.isEnableIdempotence());
        log.info("消费者配置: groupId={}, autoOffsetReset={}, keyDeserializer={}, valueDeserializer={}, maxPollRecords={}, fetchMaxBytes={}, maxPartitionFetchBytes={}",
                consumer.getGroupId(), consumer.getAutoOffsetReset(), 
                consumer.getKeyDeserializer(), consumer.getValueDeserializer(),
                consumer.getMaxPollRecords(), consumer.getFetchMaxBytes(), consumer.getMaxPartitionFetchBytes());
    }
    
    /**
     * 生产者配置内部类
     */
    public static class Producer {
        /**
         * 键序列化器
         */
        private String keySerializer = "org.apache.kafka.common.serialization.StringSerializer";
        
        /**
         * 值序列化器
         */
        private String valueSerializer = "org.apache.kafka.common.serialization.StringSerializer";
        
        /**
         * 确认机制（0：不确认，1：leader确认，all：所有副本确认）
         */
        private String acks = "all";
        
        /**
         * 重试次数
         * 增加到5次，提高消息发送的可靠性
         */
        private int retries = 5;
        
        /**
         * 批量大小（字节）
         * 增加批处理大小，提高吞吐量
         * 从64KB增加到128KB，进一步提高批处理效率
         */
        private int batchSize = 131072; // 128KB，提高批处理大小
        
        /**
         * 缓冲区大小（字节）
         * 增加缓冲区大小，提高生产者性能
         * 从128MB增加到256MB，提高生产者缓冲能力
         */
        private int bufferMemory = 268435456; // 256MB，提高缓冲区大小
        
        /**
         * 压缩类型（none, gzip, snappy, lz4, zstd）
         * zstd提供更高的压缩率和较好的性能，适合大量数据传输
         */
        private String compressionType = "zstd";
        
        /**
         * 批处理延迟时间（毫秒）
         * 较高的值会增加延迟但提高批处理效率
         * 增加到50ms，进一步提高批处理效率和吞吐量
         */
        private int lingerMs = 50;
        
        /**
         * 请求超时时间（毫秒）
         * 增加到60秒，处理较慢的网络环境
         */
        private int requestTimeoutMs = 60000;
        
        /**
         * 传递超时时间（毫秒）
         * 设置为120秒，更长的消息传递时间
         */
        private int deliveryTimeoutMs = 120000;
        
        /**
         * 最大请求大小（字节）
         * 增加到2MB，处理更大的消息
         */
        private int maxRequestSize = 2097152;
        
        /**
         * 是否启用幂等性发送
         * 确保消息不会重复
         */
        private boolean enableIdempotence = true;
        
        /**
         * 事务ID前缀
         * 如果设置了事务ID，则启用事务
         */
        private String transactionalId = "";
        
        /**
         * 其他属性
         */
        private Map<String, String> properties = new HashMap<>();

        public String getKeySerializer() {
            return keySerializer;
        }

        public void setKeySerializer(String keySerializer) {
            this.keySerializer = keySerializer;
        }

        public String getValueSerializer() {
            return valueSerializer;
        }

        public void setValueSerializer(String valueSerializer) {
            this.valueSerializer = valueSerializer;
        }

        public String getAcks() {
            return acks;
        }

        public void setAcks(String acks) {
            this.acks = acks;
        }

        public int getRetries() {
            return retries;
        }

        public void setRetries(int retries) {
            this.retries = retries;
        }

        public int getBatchSize() {
            return batchSize;
        }

        public void setBatchSize(int batchSize) {
            this.batchSize = batchSize;
        }

        public int getBufferMemory() {
            return bufferMemory;
        }

        public void setBufferMemory(int bufferMemory) {
            this.bufferMemory = bufferMemory;
        }

        public String getCompressionType() {
            return compressionType;
        }

        public void setCompressionType(String compressionType) {
            this.compressionType = compressionType;
        }
        
        public int getLingerMs() {
            return lingerMs;
        }

        public void setLingerMs(int lingerMs) {
            this.lingerMs = lingerMs;
        }

        public int getRequestTimeoutMs() {
            return requestTimeoutMs;
        }

        public void setRequestTimeoutMs(int requestTimeoutMs) {
            this.requestTimeoutMs = requestTimeoutMs;
        }
        
        public int getDeliveryTimeoutMs() {
            return deliveryTimeoutMs;
        }

        public void setDeliveryTimeoutMs(int deliveryTimeoutMs) {
            this.deliveryTimeoutMs = deliveryTimeoutMs;
        }

        public int getMaxRequestSize() {
            return maxRequestSize;
        }

        public void setMaxRequestSize(int maxRequestSize) {
            this.maxRequestSize = maxRequestSize;
        }

        public boolean isEnableIdempotence() {
            return enableIdempotence;
        }

        public void setEnableIdempotence(boolean enableIdempotence) {
            this.enableIdempotence = enableIdempotence;
        }

        public String getTransactionalId() {
            return transactionalId;
        }

        public void setTransactionalId(String transactionalId) {
            this.transactionalId = transactionalId;
        }

        public Map<String, String> getProperties() {
            return properties;
        }

        public void setProperties(Map<String, String> properties) {
            this.properties = properties;
        }
    }
    
    /**
     * 消费者配置内部类
     */
    public static class Consumer {
        /**
         * 消费者组ID
         */
        private String groupId;
        
        /**
         * 自动提交偏移量的时间间隔（毫秒）
         * 增加到10秒，减少提交频率，提高性能
         */
        private int autoCommitInterval = 10000;
        
        /**
         * 是否自动提交偏移量
         */
        private boolean enableAutoCommit = true;
        
        /**
         * 当没有初始偏移量或当前偏移量服务器上不存在时的重置策略（earliest, latest, none）
         */
        private String autoOffsetReset = "latest";
        
        /**
         * 键反序列化器
         */
        private String keyDeserializer = "org.apache.kafka.common.serialization.StringDeserializer";
        
        /**
         * 值反序列化器
         */
        private String valueDeserializer = "org.apache.kafka.common.serialization.StringDeserializer";
        
        /**
         * 一次拉取的最大记录数
         * 增加到2000，进一步提高单次拉取效率
         */
        private int maxPollRecords = 2000;
        
        /**
         * 一次拉取的最大字节数
         * 增加到150MB，提高拉取容量
         */
        private int fetchMaxBytes = 157286400; // 150MB
        
        /**
         * 单个分区一次拉取的最大字节数
         * 增加到10MB，提高单分区拉取容量
         */
        private int maxPartitionFetchBytes = 10485760; // 10MB
        
        /**
         * 最小拉取字节数
         * 设置为8KB，减少小批次请求
         */
        private int fetchMinBytes = 8192; // 8KB
        
        /**
         * 最长拉取等待时间（毫秒）
         */
        private int fetchMaxWaitMs = 500;
        
        /**
         * 心跳间隔（毫秒）
         */
        private int heartbeatIntervalMs = 3000;
        
        /**
         * 会话超时时间（毫秒）
         */
        private int sessionTimeoutMs = 45000;
        
        /**
         * 最大拉取间隔（毫秒）
         */
        private int maxPollIntervalMs = 300000;
        
        /**
         * 并发消费者数量
         * 增加到5，提高并行处理能力
         */
        private int concurrency = 5;
        
        /**
         * 是否使用批量消费
         */
        private boolean batchListener = true;
        
        /**
         * 消费者拦截器类列表
         */
        private String interceptorClasses = "";

        public String getGroupId() {
            return groupId;
        }

        public void setGroupId(String groupId) {
            this.groupId = groupId;
        }

        public int getAutoCommitInterval() {
            return autoCommitInterval;
        }

        public void setAutoCommitInterval(int autoCommitInterval) {
            this.autoCommitInterval = autoCommitInterval;
        }

        public boolean isEnableAutoCommit() {
            return enableAutoCommit;
        }

        public void setEnableAutoCommit(boolean enableAutoCommit) {
            this.enableAutoCommit = enableAutoCommit;
        }

        public String getAutoOffsetReset() {
            return autoOffsetReset;
        }

        public void setAutoOffsetReset(String autoOffsetReset) {
            this.autoOffsetReset = autoOffsetReset;
        }

        public String getKeyDeserializer() {
            return keyDeserializer;
        }

        public void setKeyDeserializer(String keyDeserializer) {
            this.keyDeserializer = keyDeserializer;
        }

        public String getValueDeserializer() {
            return valueDeserializer;
        }

        public void setValueDeserializer(String valueDeserializer) {
            this.valueDeserializer = valueDeserializer;
        }

        public int getMaxPollRecords() {
            return maxPollRecords;
        }

        public void setMaxPollRecords(int maxPollRecords) {
            this.maxPollRecords = maxPollRecords;
        }
        
        public int getFetchMaxBytes() {
            return fetchMaxBytes;
        }

        public void setFetchMaxBytes(int fetchMaxBytes) {
            this.fetchMaxBytes = fetchMaxBytes;
        }

        public int getMaxPartitionFetchBytes() {
            return maxPartitionFetchBytes;
        }

        public void setMaxPartitionFetchBytes(int maxPartitionFetchBytes) {
            this.maxPartitionFetchBytes = maxPartitionFetchBytes;
        }
        
        public int getFetchMinBytes() {
            return fetchMinBytes;
        }

        public void setFetchMinBytes(int fetchMinBytes) {
            this.fetchMinBytes = fetchMinBytes;
        }
        
        public int getFetchMaxWaitMs() {
            return fetchMaxWaitMs;
        }

        public void setFetchMaxWaitMs(int fetchMaxWaitMs) {
            this.fetchMaxWaitMs = fetchMaxWaitMs;
        }

        public int getHeartbeatIntervalMs() {
            return heartbeatIntervalMs;
        }

        public void setHeartbeatIntervalMs(int heartbeatIntervalMs) {
            this.heartbeatIntervalMs = heartbeatIntervalMs;
        }

        public int getSessionTimeoutMs() {
            return sessionTimeoutMs;
        }

        public void setSessionTimeoutMs(int sessionTimeoutMs) {
            this.sessionTimeoutMs = sessionTimeoutMs;
        }

        public int getMaxPollIntervalMs() {
            return maxPollIntervalMs;
        }

        public void setMaxPollIntervalMs(int maxPollIntervalMs) {
            this.maxPollIntervalMs = maxPollIntervalMs;
        }
        
        public int getConcurrency() {
            return concurrency;
        }

        public void setConcurrency(int concurrency) {
            this.concurrency = concurrency;
        }

        public boolean isBatchListener() {
            return batchListener;
        }

        public void setBatchListener(boolean batchListener) {
            this.batchListener = batchListener;
        }

        public String getInterceptorClasses() {
            return interceptorClasses;
        }

        public void setInterceptorClasses(String interceptorClasses) {
            this.interceptorClasses = interceptorClasses;
        }
    }

    public String getBootstrapServers() {
        return bootstrapServers;
    }

    public void setBootstrapServers(String bootstrapServers) {
        this.bootstrapServers = bootstrapServers;
    }
    
    public int getDefaultPartitions() {
        return defaultPartitions;
    }

    public void setDefaultPartitions(int defaultPartitions) {
        this.defaultPartitions = defaultPartitions;
    }

    public short getDefaultReplicationFactor() {
        return defaultReplicationFactor;
    }

    public void setDefaultReplicationFactor(short defaultReplicationFactor) {
        this.defaultReplicationFactor = defaultReplicationFactor;
    }
    
    public String getSchemaRegistryUrl() {
        return schemaRegistryUrl;
    }
    
    public void setSchemaRegistryUrl(String schemaRegistryUrl) {
        this.schemaRegistryUrl = schemaRegistryUrl;
    }
    
    public boolean isAvroSerializationEnabled() {
        return avroSerializationEnabled;
    }
    
    public void setAvroSerializationEnabled(boolean avroSerializationEnabled) {
        this.avroSerializationEnabled = avroSerializationEnabled;
    }

    public Producer getProducer() {
        return producer;
    }

    public void setProducer(Producer producer) {
        this.producer = producer;
    }

    public Consumer getConsumer() {
        return consumer;
    }

    public void setConsumer(Consumer consumer) {
        this.consumer = consumer;
    }
}