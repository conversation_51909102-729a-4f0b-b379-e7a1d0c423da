<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (21) on Fri Apr 04 16:06:08 CST 2025 -->
<title>类 com.binance.connector.futures.client.impl.futures.Market的使用 (io.github.binance:binance-futures-connector-java 3.0.5 API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-04-04">
<meta name="description" content="use: package: com.binance.connector.futures.client.impl.futures, class: Market">
<meta name="generator" content="javadoc/ClassUseWriter">
<link rel="stylesheet" type="text/css" href="../../../../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-use-page">
<script type="text/javascript">var pathtoroot = "../../../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="切换导航链接"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="../../../../../../../../index.html">概览</a></li>
<li><a href="../package-summary.html">程序包</a></li>
<li><a href="../Market.html" title="com.binance.connector.futures.client.impl.futures中的类">类</a></li>
<li class="nav-bar-cell1-rev">使用</li>
<li><a href="../package-tree.html">树</a></li>
<li><a href="../../../../../../../../index-all.html">索引</a></li>
<li><a href="../../../../../../../../help-doc.html#use">帮助</a></li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list"></div>
<div class="nav-list-search"><a href="../../../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="搜索">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 title="类的使用 com.binance.connector.futures.client.impl.futures.Market" class="title">类的使用<br>com.binance.connector.futures.client.impl.futures.Market</h1>
</div>
<div class="caption"><span>使用<a href="../Market.html" title="com.binance.connector.futures.client.impl.futures中的类">Market</a>的程序包</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">程序包</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color"><a href="#com.binance.connector.futures.client">com.binance.connector.futures.client</a></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><a href="#com.binance.connector.futures.client.impl.cm_futures">com.binance.connector.futures.client.impl.cm_futures</a></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><a href="#com.binance.connector.futures.client.impl.um_futures">com.binance.connector.futures.client.impl.um_futures</a></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
<section class="class-uses">
<ul class="block-list">
<li>
<section class="detail" id="com.binance.connector.futures.client">
<h2><a href="../../../package-summary.html">com.binance.connector.futures.client</a>中<a href="../Market.html" title="com.binance.connector.futures.client.impl.futures中的类">Market</a>的使用</h2>
<div class="caption"><span>返回<a href="../Market.html" title="com.binance.connector.futures.client.impl.futures中的类">Market</a>的<a href="../../../package-summary.html">com.binance.connector.futures.client</a>中的方法</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">修饰符和类型</div>
<div class="table-header col-second">方法</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color"><code><a href="../Market.html" title="com.binance.connector.futures.client.impl.futures中的类">Market</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">FuturesClient.</span><code><a href="../../../FuturesClient.html#market()" class="member-name-link">market</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<li>
<section class="detail" id="com.binance.connector.futures.client.impl.cm_futures">
<h2><a href="../../cm_futures/package-summary.html">com.binance.connector.futures.client.impl.cm_futures</a>中<a href="../Market.html" title="com.binance.connector.futures.client.impl.futures中的类">Market</a>的使用</h2>
<div class="caption"><span><a href="../../cm_futures/package-summary.html">com.binance.connector.futures.client.impl.cm_futures</a>中<a href="../Market.html" title="com.binance.connector.futures.client.impl.futures中的类">Market</a>的子类</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">修饰符和类型</div>
<div class="table-header col-second">类</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color"><code>class&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="../../cm_futures/CMMarket.html" class="type-name-link" title="com.binance.connector.futures.client.impl.cm_futures中的类">CMMarket</a></code></div>
<div class="col-last even-row-color">
<div class="block">Coin-Margined Market Endpoints</div>
</div>
</div>
</section>
</li>
<li>
<section class="detail" id="com.binance.connector.futures.client.impl.um_futures">
<h2><a href="../../um_futures/package-summary.html">com.binance.connector.futures.client.impl.um_futures</a>中<a href="../Market.html" title="com.binance.connector.futures.client.impl.futures中的类">Market</a>的使用</h2>
<div class="caption"><span><a href="../../um_futures/package-summary.html">com.binance.connector.futures.client.impl.um_futures</a>中<a href="../Market.html" title="com.binance.connector.futures.client.impl.futures中的类">Market</a>的子类</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">修饰符和类型</div>
<div class="table-header col-second">类</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color"><code>class&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="../../um_futures/UMMarket.html" class="type-name-link" title="com.binance.connector.futures.client.impl.um_futures中的类">UMMarket</a></code></div>
<div class="col-last even-row-color">
<div class="block">USDⓈ-Margined Market Endpoints</div>
</div>
</div>
</section>
</li>
</ul>
</section>
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &#169; 2025. All rights reserved.</small></p>
</footer>
</div>
</div>
</body>
</html>
