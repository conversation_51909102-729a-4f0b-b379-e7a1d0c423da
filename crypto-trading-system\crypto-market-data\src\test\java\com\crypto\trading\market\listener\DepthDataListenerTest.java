package com.crypto.trading.market.listener;

import com.crypto.trading.market.config.MarketDataConfig;
import com.crypto.trading.market.processor.DepthDataProcessor;
import com.crypto.trading.sdk.response.model.DepthData;
import com.crypto.trading.sdk.response.model.WebSocketMessage;
import com.crypto.trading.sdk.websocket.BinanceWebSocketClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.function.Consumer;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * DepthDataListener单元测试
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class DepthDataListenerTest {

    @Mock
    private BinanceWebSocketClient webSocketClient;

    @Mock
    private DepthDataProcessor depthDataProcessor;

    @Mock
    private MarketDataConfig marketDataConfig;

    @InjectMocks
    private DepthDataListener depthDataListener;

    private List<String> symbols;

    @BeforeEach
    void setUp() {
        // 在每次测试前重置所有mock
        clearInvocations(webSocketClient, depthDataProcessor, marketDataConfig);
        
        symbols = Arrays.asList("BTCUSDT", "ETHUSDT");
        ReflectionTestUtils.setField(depthDataListener, "symbols", symbols);
        
        // 设置mock行为，使用lenient mock，并确保使用eq匹配器正确处理大小写不敏感的字符串
        lenient().doReturn(10).when(marketDataConfig).getDepthLevels();
        lenient().doReturn(1000).when(marketDataConfig).getDepthSpeed();
        lenient().doAnswer(invocation -> {
            String symbol = invocation.getArgument(0);
            if ("BTCUSDT".equalsIgnoreCase(symbol)) return 1;
            if ("ETHUSDT".equalsIgnoreCase(symbol)) return 2;
            return 0;
        }).when(webSocketClient).subscribeDepth(anyString(), anyInt(), anyInt(), any(Consumer.class));
    }

    @Test
    void testStartDepthDataSubscription() {
        // 调用被测试方法
        depthDataListener.startDepthDataSubscription();

        // 验证每个符号都订阅了
        verify(webSocketClient, times(2)).subscribeDepth(anyString(), eq(10), eq(1000), any(Consumer.class));
    }

    @Test
    void testStopDepthDataSubscription() {
        // 首先启动订阅
        depthDataListener.startDepthDataSubscription();

        // 清除之前的调用记录，只关注stopDepthDataSubscription的效果
        clearInvocations(webSocketClient);
        
        // 调用被测试方法
        depthDataListener.stopDepthDataSubscription();

        // 验证每个连接都被关闭
        verify(webSocketClient).closeConnection(1);
        verify(webSocketClient).closeConnection(2);
    }

    @Test
    void testDepthMessageHandler() {
        // 捕获订阅时传递的消息处理器
        ArgumentCaptor<Consumer<String>> handlerCaptor = ArgumentCaptor.forClass(Consumer.class);
        
        // 订阅深度数据
        depthDataListener.startDepthDataSubscription();
        verify(webSocketClient, times(2)).subscribeDepth(anyString(), anyInt(), anyInt(), handlerCaptor.capture());
        
        // 获取捕获的消息处理器
        Consumer<String> handler = handlerCaptor.getAllValues().get(0);
        
        // 测试有效的深度消息
        String validMessage = "{\"lastUpdateId\":1027024,\"bids\":[[\"4.00000000\",\"431.00000000\"],[\"3.99000000\",\"2.38000000\"]],\"asks\":[[\"4.00000200\",\"12.00000000\"],[\"4.01000000\",\"1.00000000\"]]}";
        
        // 执行处理器
        handler.accept(validMessage);
        
        // 验证处理器被调用
        verify(depthDataProcessor, times(1)).processDepthData(any(WebSocketMessage.class));
        
        // 清除之前的调用记录
        clearInvocations(depthDataProcessor);
        
        // 测试格式错误的消息
        String malformedMessage = "not a json message";
        
        // 执行处理器，不应该抛出异常
        try {
            handler.accept(malformedMessage);
        } catch (Exception e) {
            fail("处理器不应该抛出异常: " + e.getMessage());
        }
        
        // 验证处理器没有被调用（因为格式错误的消息被忽略）
        verify(depthDataProcessor, never()).processDepthData(any(WebSocketMessage.class));
    }

    @Test
    void testParsePriceQuantities() {
        // 测试parsePriceQuantities方法
        com.alibaba.fastjson2.JSONArray jsonArray = com.alibaba.fastjson2.JSON.parseArray("[[\"4.00000000\",\"431.00000000\"],[\"3.99000000\",\"2.38000000\"]]");
        List<DepthData.PriceQuantity> priceQuantities = ReflectionTestUtils.invokeMethod(depthDataListener, "parsePriceQuantities", jsonArray);
        
        assertNotNull(priceQuantities);
        assertEquals(2, priceQuantities.size());
        assertEquals(new BigDecimal("4.00000000"), priceQuantities.get(0).getPrice());
        assertEquals(new BigDecimal("431.00000000"), priceQuantities.get(0).getQuantity());
        assertEquals(new BigDecimal("3.99000000"), priceQuantities.get(1).getPrice());
        assertEquals(new BigDecimal("2.38000000"), priceQuantities.get(1).getQuantity());
    }
} 