<?xml version="1.0" encoding="UTF-8" standalone="yes"?><!DOCTYPE report PUBLIC "-//JACOCO//DTD Report 1.1//EN" "report.dtd"><report name="io.github.binance:binance-futures-connector-java"><sessioninfo id="IAENO-1c9e02d9" start="1743753866631" dump="1743753874841"/><sessioninfo id="IAENO-306a0246" start="1743753953610" dump="1743753959697"/><package name="com/binance/connector/futures/client/impl"><class name="com/binance/connector/futures/client/impl/UMFuturesClientImpl" sourcefilename="UMFuturesClientImpl.java"><method name="&lt;init&gt;" desc="()V" line="13"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="&lt;init&gt;" desc="(Ljava/lang/String;)V" line="17"><counter type="INSTRUCTION" missed="0" covered="5"/><counter type="LINE" missed="0" covered="2"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="&lt;init&gt;" desc="(Ljava/lang/String;Ljava/lang/String;)V" line="21"><counter type="INSTRUCTION" missed="7" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="&lt;init&gt;" desc="(Ljava/lang/String;Z)V" line="25"><counter type="INSTRUCTION" missed="6" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="&lt;init&gt;" desc="(Ljava/lang/String;Ljava/lang/String;Z)V" line="29"><counter type="INSTRUCTION" missed="8" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="&lt;init&gt;" desc="(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V" line="33"><counter type="INSTRUCTION" missed="0" covered="7"/><counter type="LINE" missed="0" covered="2"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="market" desc="()Lcom/binance/connector/futures/client/impl/um_futures/UMMarket;" line="38"><counter type="INSTRUCTION" missed="0" covered="14"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="account" desc="()Lcom/binance/connector/futures/client/impl/um_futures/UMAccount;" line="43"><counter type="INSTRUCTION" missed="0" covered="14"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="userData" desc="()Lcom/binance/connector/futures/client/impl/um_futures/UMUserData;" line="48"><counter type="INSTRUCTION" missed="0" covered="12"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="&lt;clinit&gt;" desc="()V" line="9"><counter type="INSTRUCTION" missed="0" covered="5"/><counter type="LINE" missed="0" covered="2"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="26" covered="57"/><counter type="LINE" missed="8" covered="9"/><counter type="COMPLEXITY" missed="4" covered="6"/><counter type="METHOD" missed="4" covered="6"/><counter type="CLASS" missed="0" covered="1"/></class><class name="com/binance/connector/futures/client/impl/FuturesClientImpl" sourcefilename="FuturesClientImpl.java"><method name="&lt;init&gt;" desc="(Ljava/lang/String;Ljava/lang/String;)V" line="15"><counter type="INSTRUCTION" missed="0" covered="7"/><counter type="LINE" missed="0" covered="2"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="&lt;init&gt;" desc="(Ljava/lang/String;Ljava/lang/String;Z)V" line="19"><counter type="INSTRUCTION" missed="8" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="&lt;init&gt;" desc="(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V" line="23"><counter type="INSTRUCTION" missed="0" covered="8"/><counter type="LINE" missed="0" covered="2"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="&lt;init&gt;" desc="(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Z)V" line="12"><counter type="INSTRUCTION" missed="0" covered="28"/><counter type="LINE" missed="0" covered="8"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getApiKey" desc="()Ljava/lang/String;" line="35"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getSecretKey" desc="()Ljava/lang/String;" line="39"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getBaseUrl" desc="()Ljava/lang/String;" line="43"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getProductUrl" desc="()Ljava/lang/String;" line="47"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getShowLimitUsage" desc="()Z" line="51"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="setShowLimitUsage" desc="(Z)V" line="55"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setProxy" desc="(Lcom/binance/connector/futures/client/utils/ProxyAuth;)V" line="59"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getProxy" desc="()Lcom/binance/connector/futures/client/utils/ProxyAuth;" line="63"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="unsetProxy" desc="()V" line="67"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="20" covered="61"/><counter type="LINE" missed="8" covered="18"/><counter type="COMPLEXITY" missed="4" covered="9"/><counter type="METHOD" missed="4" covered="9"/><counter type="CLASS" missed="0" covered="1"/></class><class name="com/binance/connector/futures/client/impl/UMWebsocketClientImpl" sourcefilename="UMWebsocketClientImpl.java"><method name="&lt;init&gt;" desc="()V" line="21"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="&lt;init&gt;" desc="(Ljava/lang/String;)V" line="25"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="allMarkPriceStream" desc="(ILcom/binance/connector/futures/client/utils/WebSocketCallback;)I" line="42"><counter type="INSTRUCTION" missed="11" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="allMarkPriceStream" desc="(ILcom/binance/connector/futures/client/utils/WebSocketCallback;Lcom/binance/connector/futures/client/utils/WebSocketCallback;Lcom/binance/connector/futures/client/utils/WebSocketCallback;Lcom/binance/connector/futures/client/utils/WebSocketCallback;)I" line="56"><counter type="INSTRUCTION" missed="43" covered="0"/><counter type="BRANCH" missed="2" covered="0"/><counter type="LINE" missed="6" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="compositeIndexSymbolInfo" desc="(Ljava/lang/String;Lcom/binance/connector/futures/client/utils/WebSocketCallback;)I" line="80"><counter type="INSTRUCTION" missed="15" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="compositeIndexSymbolInfo" desc="(Ljava/lang/String;Lcom/binance/connector/futures/client/utils/WebSocketCallback;Lcom/binance/connector/futures/client/utils/WebSocketCallback;Lcom/binance/connector/futures/client/utils/WebSocketCallback;Lcom/binance/connector/futures/client/utils/WebSocketCallback;)I" line="95"><counter type="INSTRUCTION" missed="28" covered="0"/><counter type="LINE" missed="3" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="105" covered="0"/><counter type="BRANCH" missed="2" covered="0"/><counter type="LINE" missed="16" covered="0"/><counter type="COMPLEXITY" missed="7" covered="0"/><counter type="METHOD" missed="6" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><class name="com/binance/connector/futures/client/impl/CMWebsocketClientImpl" sourcefilename="CMWebsocketClientImpl.java"><method name="&lt;init&gt;" desc="()V" line="20"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="&lt;init&gt;" desc="(Ljava/lang/String;)V" line="24"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="indexPriceStream" desc="(Ljava/lang/String;ILcom/binance/connector/futures/client/utils/WebSocketCallback;)I" line="42"><counter type="INSTRUCTION" missed="16" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="indexPriceStream" desc="(Ljava/lang/String;ILcom/binance/connector/futures/client/utils/WebSocketCallback;Lcom/binance/connector/futures/client/utils/WebSocketCallback;Lcom/binance/connector/futures/client/utils/WebSocketCallback;Lcom/binance/connector/futures/client/utils/WebSocketCallback;)I" line="58"><counter type="INSTRUCTION" missed="62" covered="0"/><counter type="BRANCH" missed="2" covered="0"/><counter type="LINE" missed="7" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="markPriceSymbolsPairStream" desc="(Ljava/lang/String;ILcom/binance/connector/futures/client/utils/WebSocketCallback;)I" line="84"><counter type="INSTRUCTION" missed="16" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="markPriceSymbolsPairStream" desc="(Ljava/lang/String;ILcom/binance/connector/futures/client/utils/WebSocketCallback;Lcom/binance/connector/futures/client/utils/WebSocketCallback;Lcom/binance/connector/futures/client/utils/WebSocketCallback;Lcom/binance/connector/futures/client/utils/WebSocketCallback;)I" line="100"><counter type="INSTRUCTION" missed="57" covered="0"/><counter type="BRANCH" missed="2" covered="0"/><counter type="LINE" missed="7" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="indexKlineCandlestick" desc="(Ljava/lang/String;Ljava/lang/String;Lcom/binance/connector/futures/client/utils/WebSocketCallback;)I" line="126"><counter type="INSTRUCTION" missed="16" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="indexKlineCandlestick" desc="(Ljava/lang/String;Ljava/lang/String;Lcom/binance/connector/futures/client/utils/WebSocketCallback;Lcom/binance/connector/futures/client/utils/WebSocketCallback;Lcom/binance/connector/futures/client/utils/WebSocketCallback;Lcom/binance/connector/futures/client/utils/WebSocketCallback;)I" line="142"><counter type="INSTRUCTION" missed="32" covered="0"/><counter type="LINE" missed="3" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="markKlineCandlestick" desc="(Ljava/lang/String;Ljava/lang/String;Lcom/binance/connector/futures/client/utils/WebSocketCallback;)I" line="162"><counter type="INSTRUCTION" missed="16" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="markKlineCandlestick" desc="(Ljava/lang/String;Ljava/lang/String;Lcom/binance/connector/futures/client/utils/WebSocketCallback;Lcom/binance/connector/futures/client/utils/WebSocketCallback;Lcom/binance/connector/futures/client/utils/WebSocketCallback;Lcom/binance/connector/futures/client/utils/WebSocketCallback;)I" line="178"><counter type="INSTRUCTION" missed="32" covered="0"/><counter type="LINE" missed="3" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="255" covered="0"/><counter type="BRANCH" missed="4" covered="0"/><counter type="LINE" missed="32" covered="0"/><counter type="COMPLEXITY" missed="12" covered="0"/><counter type="METHOD" missed="10" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><class name="com/binance/connector/futures/client/impl/CMFuturesClientImpl" sourcefilename="CMFuturesClientImpl.java"><method name="&lt;init&gt;" desc="()V" line="14"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="&lt;init&gt;" desc="(Ljava/lang/String;)V" line="18"><counter type="INSTRUCTION" missed="0" covered="5"/><counter type="LINE" missed="0" covered="2"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="&lt;init&gt;" desc="(Ljava/lang/String;Ljava/lang/String;)V" line="22"><counter type="INSTRUCTION" missed="7" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="&lt;init&gt;" desc="(Ljava/lang/String;Z)V" line="26"><counter type="INSTRUCTION" missed="6" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="&lt;init&gt;" desc="(Ljava/lang/String;Ljava/lang/String;Z)V" line="30"><counter type="INSTRUCTION" missed="8" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="&lt;init&gt;" desc="(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V" line="34"><counter type="INSTRUCTION" missed="0" covered="7"/><counter type="LINE" missed="0" covered="2"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="market" desc="()Lcom/binance/connector/futures/client/impl/cm_futures/CMMarket;" line="39"><counter type="INSTRUCTION" missed="0" covered="14"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="account" desc="()Lcom/binance/connector/futures/client/impl/cm_futures/CMAccount;" line="44"><counter type="INSTRUCTION" missed="0" covered="14"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="userData" desc="()Lcom/binance/connector/futures/client/impl/cm_futures/CMUserData;" line="49"><counter type="INSTRUCTION" missed="0" covered="12"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="portfolioMargin" desc="()Lcom/binance/connector/futures/client/impl/cm_futures/CMPortfolioMargin;" line="54"><counter type="INSTRUCTION" missed="0" covered="14"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="&lt;clinit&gt;" desc="()V" line="10"><counter type="INSTRUCTION" missed="0" covered="5"/><counter type="LINE" missed="0" covered="2"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="26" covered="71"/><counter type="LINE" missed="8" covered="10"/><counter type="COMPLEXITY" missed="4" covered="7"/><counter type="METHOD" missed="4" covered="7"/><counter type="CLASS" missed="0" covered="1"/></class><class name="com/binance/connector/futures/client/impl/WebsocketClientImpl" sourcefilename="WebsocketClientImpl.java"><method name="&lt;init&gt;" desc="(Ljava/lang/String;)V" line="31"><counter type="INSTRUCTION" missed="14" covered="0"/><counter type="LINE" missed="5" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getNoopCallback" desc="()Lcom/binance/connector/futures/client/utils/WebSocketCallback;" line="41"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getBaseUrl" desc="()Ljava/lang/String;" line="45"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="aggTradeStream" desc="(Ljava/lang/String;Lcom/binance/connector/futures/client/utils/WebSocketCallback;)I" line="64"><counter type="INSTRUCTION" missed="15" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="aggTradeStream" desc="(Ljava/lang/String;Lcom/binance/connector/futures/client/utils/WebSocketCallback;Lcom/binance/connector/futures/client/utils/WebSocketCallback;Lcom/binance/connector/futures/client/utils/WebSocketCallback;Lcom/binance/connector/futures/client/utils/WebSocketCallback;)I" line="80"><counter type="INSTRUCTION" missed="28" covered="0"/><counter type="LINE" missed="3" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="markPriceStream" desc="(Ljava/lang/String;ILcom/binance/connector/futures/client/utils/WebSocketCallback;)I" line="103"><counter type="INSTRUCTION" missed="16" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="markPriceStream" desc="(Ljava/lang/String;ILcom/binance/connector/futures/client/utils/WebSocketCallback;Lcom/binance/connector/futures/client/utils/WebSocketCallback;Lcom/binance/connector/futures/client/utils/WebSocketCallback;Lcom/binance/connector/futures/client/utils/WebSocketCallback;)I" line="120"><counter type="INSTRUCTION" missed="53" covered="0"/><counter type="BRANCH" missed="2" covered="0"/><counter type="LINE" missed="6" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="klineStream" desc="(Ljava/lang/String;Ljava/lang/String;Lcom/binance/connector/futures/client/utils/WebSocketCallback;)I" line="148"><counter type="INSTRUCTION" missed="16" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="klineStream" desc="(Ljava/lang/String;Ljava/lang/String;Lcom/binance/connector/futures/client/utils/WebSocketCallback;Lcom/binance/connector/futures/client/utils/WebSocketCallback;Lcom/binance/connector/futures/client/utils/WebSocketCallback;Lcom/binance/connector/futures/client/utils/WebSocketCallback;)I" line="165"><counter type="INSTRUCTION" missed="32" covered="0"/><counter type="LINE" missed="3" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="continuousKlineStream" desc="(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lcom/binance/connector/futures/client/utils/WebSocketCallback;)I" line="187"><counter type="INSTRUCTION" missed="17" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="continuousKlineStream" desc="(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lcom/binance/connector/futures/client/utils/WebSocketCallback;Lcom/binance/connector/futures/client/utils/WebSocketCallback;Lcom/binance/connector/futures/client/utils/WebSocketCallback;Lcom/binance/connector/futures/client/utils/WebSocketCallback;)I" line="205"><counter type="INSTRUCTION" missed="44" covered="0"/><counter type="LINE" missed="5" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="miniTickerStream" desc="(Ljava/lang/String;Lcom/binance/connector/futures/client/utils/WebSocketCallback;)I" line="231"><counter type="INSTRUCTION" missed="15" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="miniTickerStream" desc="(Ljava/lang/String;Lcom/binance/connector/futures/client/utils/WebSocketCallback;Lcom/binance/connector/futures/client/utils/WebSocketCallback;Lcom/binance/connector/futures/client/utils/WebSocketCallback;Lcom/binance/connector/futures/client/utils/WebSocketCallback;)I" line="247"><counter type="INSTRUCTION" missed="28" covered="0"/><counter type="LINE" missed="3" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="allMiniTickerStream" desc="(Lcom/binance/connector/futures/client/utils/WebSocketCallback;)I" line="270"><counter type="INSTRUCTION" missed="10" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="allMiniTickerStream" desc="(Lcom/binance/connector/futures/client/utils/WebSocketCallback;Lcom/binance/connector/futures/client/utils/WebSocketCallback;Lcom/binance/connector/futures/client/utils/WebSocketCallback;Lcom/binance/connector/futures/client/utils/WebSocketCallback;)I" line="284"><counter type="INSTRUCTION" missed="19" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="symbolTicker" desc="(Ljava/lang/String;Lcom/binance/connector/futures/client/utils/WebSocketCallback;)I" line="306"><counter type="INSTRUCTION" missed="15" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="symbolTicker" desc="(Ljava/lang/String;Lcom/binance/connector/futures/client/utils/WebSocketCallback;Lcom/binance/connector/futures/client/utils/WebSocketCallback;Lcom/binance/connector/futures/client/utils/WebSocketCallback;Lcom/binance/connector/futures/client/utils/WebSocketCallback;)I" line="322"><counter type="INSTRUCTION" missed="28" covered="0"/><counter type="LINE" missed="3" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="allTickerStream" desc="(Lcom/binance/connector/futures/client/utils/WebSocketCallback;)I" line="345"><counter type="INSTRUCTION" missed="10" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="allTickerStream" desc="(Lcom/binance/connector/futures/client/utils/WebSocketCallback;Lcom/binance/connector/futures/client/utils/WebSocketCallback;Lcom/binance/connector/futures/client/utils/WebSocketCallback;Lcom/binance/connector/futures/client/utils/WebSocketCallback;)I" line="359"><counter type="INSTRUCTION" missed="19" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="bookTicker" desc="(Ljava/lang/String;Lcom/binance/connector/futures/client/utils/WebSocketCallback;)I" line="380"><counter type="INSTRUCTION" missed="15" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="bookTicker" desc="(Ljava/lang/String;Lcom/binance/connector/futures/client/utils/WebSocketCallback;Lcom/binance/connector/futures/client/utils/WebSocketCallback;Lcom/binance/connector/futures/client/utils/WebSocketCallback;Lcom/binance/connector/futures/client/utils/WebSocketCallback;)I" line="396"><counter type="INSTRUCTION" missed="28" covered="0"/><counter type="LINE" missed="3" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="allBookTickerStream" desc="(Lcom/binance/connector/futures/client/utils/WebSocketCallback;)I" line="417"><counter type="INSTRUCTION" missed="10" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="allBookTickerStream" desc="(Lcom/binance/connector/futures/client/utils/WebSocketCallback;Lcom/binance/connector/futures/client/utils/WebSocketCallback;Lcom/binance/connector/futures/client/utils/WebSocketCallback;Lcom/binance/connector/futures/client/utils/WebSocketCallback;)I" line="431"><counter type="INSTRUCTION" missed="19" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="forceOrderStream" desc="(Ljava/lang/String;Lcom/binance/connector/futures/client/utils/WebSocketCallback;)I" line="454"><counter type="INSTRUCTION" missed="15" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="forceOrderStream" desc="(Ljava/lang/String;Lcom/binance/connector/futures/client/utils/WebSocketCallback;Lcom/binance/connector/futures/client/utils/WebSocketCallback;Lcom/binance/connector/futures/client/utils/WebSocketCallback;Lcom/binance/connector/futures/client/utils/WebSocketCallback;)I" line="470"><counter type="INSTRUCTION" missed="28" covered="0"/><counter type="LINE" missed="3" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="allForceOrderStream" desc="(Lcom/binance/connector/futures/client/utils/WebSocketCallback;)I" line="493"><counter type="INSTRUCTION" missed="10" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="allForceOrderStream" desc="(Lcom/binance/connector/futures/client/utils/WebSocketCallback;Lcom/binance/connector/futures/client/utils/WebSocketCallback;Lcom/binance/connector/futures/client/utils/WebSocketCallback;Lcom/binance/connector/futures/client/utils/WebSocketCallback;)I" line="507"><counter type="INSTRUCTION" missed="19" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="partialDepthStream" desc="(Ljava/lang/String;IILcom/binance/connector/futures/client/utils/WebSocketCallback;)I" line="530"><counter type="INSTRUCTION" missed="17" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="partialDepthStream" desc="(Ljava/lang/String;IILcom/binance/connector/futures/client/utils/WebSocketCallback;Lcom/binance/connector/futures/client/utils/WebSocketCallback;Lcom/binance/connector/futures/client/utils/WebSocketCallback;Lcom/binance/connector/futures/client/utils/WebSocketCallback;)I" line="548"><counter type="INSTRUCTION" missed="67" covered="0"/><counter type="BRANCH" missed="2" covered="0"/><counter type="LINE" missed="7" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="diffDepthStream" desc="(Ljava/lang/String;ILcom/binance/connector/futures/client/utils/WebSocketCallback;)I" line="579"><counter type="INSTRUCTION" missed="16" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="diffDepthStream" desc="(Ljava/lang/String;ILcom/binance/connector/futures/client/utils/WebSocketCallback;Lcom/binance/connector/futures/client/utils/WebSocketCallback;Lcom/binance/connector/futures/client/utils/WebSocketCallback;Lcom/binance/connector/futures/client/utils/WebSocketCallback;)I" line="596"><counter type="INSTRUCTION" missed="62" covered="0"/><counter type="BRANCH" missed="2" covered="0"/><counter type="LINE" missed="7" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="listenUserStream" desc="(Ljava/lang/String;Lcom/binance/connector/futures/client/utils/WebSocketCallback;)I" line="622"><counter type="INSTRUCTION" missed="11" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="listenUserStream" desc="(Ljava/lang/String;Lcom/binance/connector/futures/client/utils/WebSocketCallback;Lcom/binance/connector/futures/client/utils/WebSocketCallback;Lcom/binance/connector/futures/client/utils/WebSocketCallback;Lcom/binance/connector/futures/client/utils/WebSocketCallback;)I" line="637"><counter type="INSTRUCTION" missed="23" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="combineStreams" desc="(Ljava/util/ArrayList;Lcom/binance/connector/futures/client/utils/WebSocketCallback;)I" line="654"><counter type="INSTRUCTION" missed="11" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="combineStreams" desc="(Ljava/util/ArrayList;Lcom/binance/connector/futures/client/utils/WebSocketCallback;Lcom/binance/connector/futures/client/utils/WebSocketCallback;Lcom/binance/connector/futures/client/utils/WebSocketCallback;Lcom/binance/connector/futures/client/utils/WebSocketCallback;)I" line="669"><counter type="INSTRUCTION" missed="23" covered="0"/><counter type="LINE" missed="3" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="closeConnection" desc="(I)V" line="681"><counter type="INSTRUCTION" missed="31" covered="0"/><counter type="BRANCH" missed="2" covered="0"/><counter type="LINE" missed="6" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="closeAllConnections" desc="()V" line="695"><counter type="INSTRUCTION" missed="42" covered="0"/><counter type="BRANCH" missed="6" covered="0"/><counter type="LINE" missed="12" covered="0"/><counter type="COMPLEXITY" missed="4" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="createConnection" desc="(Lcom/binance/connector/futures/client/utils/WebSocketCallback;Lcom/binance/connector/futures/client/utils/WebSocketCallback;Lcom/binance/connector/futures/client/utils/WebSocketCallback;Lcom/binance/connector/futures/client/utils/WebSocketCallback;Lokhttp3/Request;)I" line="718"><counter type="INSTRUCTION" missed="23" covered="0"/><counter type="LINE" missed="5" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="lambda$new$0" desc="(Ljava/lang/String;)V" line="33"><counter type="INSTRUCTION" missed="1" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="&lt;clinit&gt;" desc="()V" line="34"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="860" covered="0"/><counter type="BRANCH" missed="14" covered="0"/><counter type="LINE" missed="114" covered="0"/><counter type="COMPLEXITY" missed="47" covered="0"/><counter type="METHOD" missed="40" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><sourcefile name="UMFuturesClientImpl.java"><line nr="9" mi="0" ci="2" mb="0" cb="0"/><line nr="10" mi="0" ci="3" mb="0" cb="0"/><line nr="13" mi="4" ci="0" mb="0" cb="0"/><line nr="14" mi="1" ci="0" mb="0" cb="0"/><line nr="17" mi="0" ci="4" mb="0" cb="0"/><line nr="18" mi="0" ci="1" mb="0" cb="0"/><line nr="21" mi="6" ci="0" mb="0" cb="0"/><line nr="22" mi="1" ci="0" mb="0" cb="0"/><line nr="25" mi="5" ci="0" mb="0" cb="0"/><line nr="26" mi="1" ci="0" mb="0" cb="0"/><line nr="29" mi="7" ci="0" mb="0" cb="0"/><line nr="30" mi="1" ci="0" mb="0" cb="0"/><line nr="33" mi="0" ci="6" mb="0" cb="0"/><line nr="34" mi="0" ci="1" mb="0" cb="0"/><line nr="38" mi="0" ci="14" mb="0" cb="0"/><line nr="43" mi="0" ci="14" mb="0" cb="0"/><line nr="48" mi="0" ci="12" mb="0" cb="0"/><counter type="INSTRUCTION" missed="26" covered="57"/><counter type="LINE" missed="8" covered="9"/><counter type="COMPLEXITY" missed="4" covered="6"/><counter type="METHOD" missed="4" covered="6"/><counter type="CLASS" missed="0" covered="1"/></sourcefile><sourcefile name="CMWebsocketClientImpl.java"><line nr="20" mi="3" ci="0" mb="0" cb="0"/><line nr="21" mi="1" ci="0" mb="0" cb="0"/><line nr="24" mi="3" ci="0" mb="0" cb="0"/><line nr="25" mi="1" ci="0" mb="0" cb="0"/><line nr="42" mi="4" ci="0" mb="0" cb="0"/><line nr="43" mi="12" ci="0" mb="0" cb="0"/><line nr="58" mi="4" ci="0" mb="0" cb="0"/><line nr="59" mi="2" ci="0" mb="0" cb="0"/><line nr="60" mi="2" ci="0" mb="0" cb="0"/><line nr="61" mi="3" ci="0" mb="2" cb="0"/><line nr="62" mi="22" ci="0" mb="0" cb="0"/><line nr="64" mi="21" ci="0" mb="0" cb="0"/><line nr="66" mi="8" ci="0" mb="0" cb="0"/><line nr="84" mi="4" ci="0" mb="0" cb="0"/><line nr="85" mi="12" ci="0" mb="0" cb="0"/><line nr="100" mi="4" ci="0" mb="0" cb="0"/><line nr="101" mi="2" ci="0" mb="0" cb="0"/><line nr="102" mi="2" ci="0" mb="0" cb="0"/><line nr="103" mi="3" ci="0" mb="2" cb="0"/><line nr="104" mi="17" ci="0" mb="0" cb="0"/><line nr="106" mi="21" ci="0" mb="0" cb="0"/><line nr="108" mi="8" ci="0" mb="0" cb="0"/><line nr="126" mi="4" ci="0" mb="0" cb="0"/><line nr="127" mi="12" ci="0" mb="0" cb="0"/><line nr="142" mi="4" ci="0" mb="0" cb="0"/><line nr="143" mi="20" ci="0" mb="0" cb="0"/><line nr="144" mi="8" ci="0" mb="0" cb="0"/><line nr="162" mi="4" ci="0" mb="0" cb="0"/><line nr="163" mi="12" ci="0" mb="0" cb="0"/><line nr="178" mi="4" ci="0" mb="0" cb="0"/><line nr="179" mi="20" ci="0" mb="0" cb="0"/><line nr="180" mi="8" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="255" covered="0"/><counter type="BRANCH" missed="4" covered="0"/><counter type="LINE" missed="32" covered="0"/><counter type="COMPLEXITY" missed="12" covered="0"/><counter type="METHOD" missed="10" covered="0"/><counter type="CLASS" missed="1" covered="0"/></sourcefile><sourcefile name="FuturesClientImpl.java"><line nr="12" mi="0" ci="3" mb="0" cb="0"/><line nr="15" mi="0" ci="6" mb="0" cb="0"/><line nr="16" mi="0" ci="1" mb="0" cb="0"/><line nr="19" mi="7" ci="0" mb="0" cb="0"/><line nr="20" mi="1" ci="0" mb="0" cb="0"/><line nr="23" mi="0" ci="7" mb="0" cb="0"/><line nr="24" mi="0" ci="1" mb="0" cb="0"/><line nr="26" mi="0" ci="2" mb="0" cb="0"/><line nr="27" mi="0" ci="3" mb="0" cb="0"/><line nr="28" mi="0" ci="3" mb="0" cb="0"/><line nr="29" mi="0" ci="3" mb="0" cb="0"/><line nr="30" mi="0" ci="10" mb="0" cb="0"/><line nr="31" mi="0" ci="3" mb="0" cb="0"/><line nr="32" mi="0" ci="1" mb="0" cb="0"/><line nr="35" mi="0" ci="3" mb="0" cb="0"/><line nr="39" mi="0" ci="3" mb="0" cb="0"/><line nr="43" mi="0" ci="3" mb="0" cb="0"/><line nr="47" mi="0" ci="3" mb="0" cb="0"/><line nr="51" mi="0" ci="3" mb="0" cb="0"/><line nr="55" mi="3" ci="0" mb="0" cb="0"/><line nr="56" mi="1" ci="0" mb="0" cb="0"/><line nr="59" mi="3" ci="0" mb="0" cb="0"/><line nr="60" mi="1" ci="0" mb="0" cb="0"/><line nr="63" mi="0" ci="3" mb="0" cb="0"/><line nr="67" mi="3" ci="0" mb="0" cb="0"/><line nr="68" mi="1" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="20" covered="61"/><counter type="LINE" missed="8" covered="18"/><counter type="COMPLEXITY" missed="4" covered="9"/><counter type="METHOD" missed="4" covered="9"/><counter type="CLASS" missed="0" covered="1"/></sourcefile><sourcefile name="WebsocketClientImpl.java"><line nr="31" mi="5" ci="0" mb="0" cb="0"/><line nr="32" mi="3" ci="0" mb="0" cb="0"/><line nr="33" mi="1" ci="0" mb="0" cb="0"/><line nr="34" mi="4" ci="0" mb="0" cb="0"/><line nr="36" mi="2" ci="0" mb="0" cb="0"/><line nr="37" mi="3" ci="0" mb="0" cb="0"/><line nr="38" mi="1" ci="0" mb="0" cb="0"/><line nr="41" mi="3" ci="0" mb="0" cb="0"/><line nr="45" mi="3" ci="0" mb="0" cb="0"/><line nr="64" mi="4" ci="0" mb="0" cb="0"/><line nr="65" mi="11" ci="0" mb="0" cb="0"/><line nr="80" mi="4" ci="0" mb="0" cb="0"/><line nr="81" mi="16" ci="0" mb="0" cb="0"/><line nr="82" mi="8" ci="0" mb="0" cb="0"/><line nr="103" mi="4" ci="0" mb="0" cb="0"/><line nr="104" mi="12" ci="0" mb="0" cb="0"/><line nr="120" mi="2" ci="0" mb="0" cb="0"/><line nr="121" mi="2" ci="0" mb="0" cb="0"/><line nr="122" mi="3" ci="0" mb="2" cb="0"/><line nr="123" mi="17" ci="0" mb="0" cb="0"/><line nr="125" mi="21" ci="0" mb="0" cb="0"/><line nr="127" mi="8" ci="0" mb="0" cb="0"/><line nr="148" mi="4" ci="0" mb="0" cb="0"/><line nr="149" mi="12" ci="0" mb="0" cb="0"/><line nr="165" mi="4" ci="0" mb="0" cb="0"/><line nr="166" mi="20" ci="0" mb="0" cb="0"/><line nr="167" mi="8" ci="0" mb="0" cb="0"/><line nr="187" mi="4" ci="0" mb="0" cb="0"/><line nr="188" mi="13" ci="0" mb="0" cb="0"/><line nr="205" mi="4" ci="0" mb="0" cb="0"/><line nr="206" mi="4" ci="0" mb="0" cb="0"/><line nr="207" mi="4" ci="0" mb="0" cb="0"/><line nr="209" mi="24" ci="0" mb="0" cb="0"/><line nr="210" mi="8" ci="0" mb="0" cb="0"/><line nr="231" mi="4" ci="0" mb="0" cb="0"/><line nr="232" mi="11" ci="0" mb="0" cb="0"/><line nr="247" mi="4" ci="0" mb="0" cb="0"/><line nr="248" mi="16" ci="0" mb="0" cb="0"/><line nr="249" mi="8" ci="0" mb="0" cb="0"/><line nr="270" mi="10" ci="0" mb="0" cb="0"/><line nr="284" mi="11" ci="0" mb="0" cb="0"/><line nr="285" mi="8" ci="0" mb="0" cb="0"/><line nr="306" mi="4" ci="0" mb="0" cb="0"/><line nr="307" mi="11" ci="0" mb="0" cb="0"/><line nr="322" mi="4" ci="0" mb="0" cb="0"/><line nr="323" mi="16" ci="0" mb="0" cb="0"/><line nr="324" mi="8" ci="0" mb="0" cb="0"/><line nr="345" mi="10" ci="0" mb="0" cb="0"/><line nr="359" mi="11" ci="0" mb="0" cb="0"/><line nr="360" mi="8" ci="0" mb="0" cb="0"/><line nr="380" mi="4" ci="0" mb="0" cb="0"/><line nr="381" mi="11" ci="0" mb="0" cb="0"/><line nr="396" mi="4" ci="0" mb="0" cb="0"/><line nr="397" mi="16" ci="0" mb="0" cb="0"/><line nr="398" mi="8" ci="0" mb="0" cb="0"/><line nr="417" mi="10" ci="0" mb="0" cb="0"/><line nr="431" mi="11" ci="0" mb="0" cb="0"/><line nr="432" mi="8" ci="0" mb="0" cb="0"/><line nr="454" mi="4" ci="0" mb="0" cb="0"/><line nr="455" mi="11" ci="0" mb="0" cb="0"/><line nr="470" mi="4" ci="0" mb="0" cb="0"/><line nr="471" mi="16" ci="0" mb="0" cb="0"/><line nr="472" mi="8" ci="0" mb="0" cb="0"/><line nr="493" mi="10" ci="0" mb="0" cb="0"/><line nr="507" mi="11" ci="0" mb="0" cb="0"/><line nr="508" mi="8" ci="0" mb="0" cb="0"/><line nr="530" mi="4" ci="0" mb="0" cb="0"/><line nr="531" mi="13" ci="0" mb="0" cb="0"/><line nr="548" mi="4" ci="0" mb="0" cb="0"/><line nr="550" mi="2" ci="0" mb="0" cb="0"/><line nr="551" mi="2" ci="0" mb="0" cb="0"/><line nr="552" mi="3" ci="0" mb="2" cb="0"/><line nr="553" mi="22" ci="0" mb="0" cb="0"/><line nr="555" mi="26" ci="0" mb="0" cb="0"/><line nr="558" mi="8" ci="0" mb="0" cb="0"/><line nr="579" mi="4" ci="0" mb="0" cb="0"/><line nr="580" mi="12" ci="0" mb="0" cb="0"/><line nr="596" mi="4" ci="0" mb="0" cb="0"/><line nr="598" mi="2" ci="0" mb="0" cb="0"/><line nr="599" mi="2" ci="0" mb="0" cb="0"/><line nr="600" mi="3" ci="0" mb="2" cb="0"/><line nr="601" mi="22" ci="0" mb="0" cb="0"/><line nr="603" mi="21" ci="0" mb="0" cb="0"/><line nr="605" mi="8" ci="0" mb="0" cb="0"/><line nr="622" mi="11" ci="0" mb="0" cb="0"/><line nr="637" mi="15" ci="0" mb="0" cb="0"/><line nr="638" mi="8" ci="0" mb="0" cb="0"/><line nr="654" mi="11" ci="0" mb="0" cb="0"/><line nr="669" mi="12" ci="0" mb="0" cb="0"/><line nr="670" mi="3" ci="0" mb="0" cb="0"/><line nr="671" mi="8" ci="0" mb="0" cb="0"/><line nr="681" mi="6" ci="0" mb="2" cb="0"/><line nr="682" mi="7" ci="0" mb="0" cb="0"/><line nr="683" mi="5" ci="0" mb="0" cb="0"/><line nr="684" mi="7" ci="0" mb="0" cb="0"/><line nr="686" mi="5" ci="0" mb="0" cb="0"/><line nr="688" mi="1" ci="0" mb="0" cb="0"/><line nr="695" mi="4" ci="0" mb="2" cb="0"/><line nr="696" mi="7" ci="0" mb="0" cb="0"/><line nr="697" mi="5" ci="0" mb="0" cb="0"/><line nr="698" mi="3" ci="0" mb="2" cb="0"/><line nr="699" mi="6" ci="0" mb="0" cb="0"/><line nr="700" mi="2" ci="0" mb="0" cb="0"/><line nr="701" mi="2" ci="0" mb="0" cb="0"/><line nr="702" mi="1" ci="0" mb="0" cb="0"/><line nr="705" mi="4" ci="0" mb="2" cb="0"/><line nr="706" mi="4" ci="0" mb="0" cb="0"/><line nr="707" mi="3" ci="0" mb="0" cb="0"/><line nr="709" mi="1" ci="0" mb="0" cb="0"/><line nr="718" mi="9" ci="0" mb="0" cb="0"/><line nr="719" mi="2" ci="0" mb="0" cb="0"/><line nr="720" mi="3" ci="0" mb="0" cb="0"/><line nr="721" mi="7" ci="0" mb="0" cb="0"/><line nr="722" mi="2" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="860" covered="0"/><counter type="BRANCH" missed="14" covered="0"/><counter type="LINE" missed="114" covered="0"/><counter type="COMPLEXITY" missed="47" covered="0"/><counter type="METHOD" missed="40" covered="0"/><counter type="CLASS" missed="1" covered="0"/></sourcefile><sourcefile name="UMWebsocketClientImpl.java"><line nr="21" mi="3" ci="0" mb="0" cb="0"/><line nr="22" mi="1" ci="0" mb="0" cb="0"/><line nr="25" mi="3" ci="0" mb="0" cb="0"/><line nr="26" mi="1" ci="0" mb="0" cb="0"/><line nr="42" mi="11" ci="0" mb="0" cb="0"/><line nr="56" mi="2" ci="0" mb="0" cb="0"/><line nr="57" mi="2" ci="0" mb="0" cb="0"/><line nr="58" mi="3" ci="0" mb="2" cb="0"/><line nr="59" mi="12" ci="0" mb="0" cb="0"/><line nr="61" mi="16" ci="0" mb="0" cb="0"/><line nr="63" mi="8" ci="0" mb="0" cb="0"/><line nr="80" mi="4" ci="0" mb="0" cb="0"/><line nr="81" mi="11" ci="0" mb="0" cb="0"/><line nr="95" mi="4" ci="0" mb="0" cb="0"/><line nr="96" mi="16" ci="0" mb="0" cb="0"/><line nr="97" mi="8" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="105" covered="0"/><counter type="BRANCH" missed="2" covered="0"/><counter type="LINE" missed="16" covered="0"/><counter type="COMPLEXITY" missed="7" covered="0"/><counter type="METHOD" missed="6" covered="0"/><counter type="CLASS" missed="1" covered="0"/></sourcefile><sourcefile name="CMFuturesClientImpl.java"><line nr="10" mi="0" ci="2" mb="0" cb="0"/><line nr="11" mi="0" ci="3" mb="0" cb="0"/><line nr="14" mi="4" ci="0" mb="0" cb="0"/><line nr="15" mi="1" ci="0" mb="0" cb="0"/><line nr="18" mi="0" ci="4" mb="0" cb="0"/><line nr="19" mi="0" ci="1" mb="0" cb="0"/><line nr="22" mi="6" ci="0" mb="0" cb="0"/><line nr="23" mi="1" ci="0" mb="0" cb="0"/><line nr="26" mi="5" ci="0" mb="0" cb="0"/><line nr="27" mi="1" ci="0" mb="0" cb="0"/><line nr="30" mi="7" ci="0" mb="0" cb="0"/><line nr="31" mi="1" ci="0" mb="0" cb="0"/><line nr="34" mi="0" ci="6" mb="0" cb="0"/><line nr="35" mi="0" ci="1" mb="0" cb="0"/><line nr="39" mi="0" ci="14" mb="0" cb="0"/><line nr="44" mi="0" ci="14" mb="0" cb="0"/><line nr="49" mi="0" ci="12" mb="0" cb="0"/><line nr="54" mi="0" ci="14" mb="0" cb="0"/><counter type="INSTRUCTION" missed="26" covered="71"/><counter type="LINE" missed="8" covered="10"/><counter type="COMPLEXITY" missed="4" covered="7"/><counter type="METHOD" missed="4" covered="7"/><counter type="CLASS" missed="0" covered="1"/></sourcefile><counter type="INSTRUCTION" missed="1292" covered="189"/><counter type="BRANCH" missed="20" covered="0"/><counter type="LINE" missed="186" covered="37"/><counter type="COMPLEXITY" missed="78" covered="22"/><counter type="METHOD" missed="68" covered="22"/><counter type="CLASS" missed="3" covered="3"/></package><package name="com/binance/connector/futures/client/exceptions"><class name="com/binance/connector/futures/client/exceptions/BinanceConnectorException" sourcefilename="BinanceConnectorException.java"><method name="&lt;init&gt;" desc="(Ljava/lang/String;)V" line="17"><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="2"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="2"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/><counter type="CLASS" missed="0" covered="1"/></class><class name="com/binance/connector/futures/client/exceptions/BinanceClientException" sourcefilename="BinanceClientException.java"><method name="&lt;init&gt;" desc="(Ljava/lang/String;I)V" line="7"><counter type="INSTRUCTION" missed="0" covered="13"/><counter type="LINE" missed="0" covered="5"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="&lt;init&gt;" desc="(Ljava/lang/String;Ljava/lang/String;II)V" line="7"><counter type="INSTRUCTION" missed="0" covered="16"/><counter type="LINE" missed="0" covered="6"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getErrorCode" desc="()I" line="45"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getHttpStatusCode" desc="()I" line="53"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getErrMsg" desc="()Ljava/lang/String;" line="61"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="9" covered="29"/><counter type="LINE" missed="3" covered="10"/><counter type="COMPLEXITY" missed="3" covered="2"/><counter type="METHOD" missed="3" covered="2"/><counter type="CLASS" missed="0" covered="1"/></class><class name="com/binance/connector/futures/client/exceptions/BinanceServerException" sourcefilename="BinanceServerException.java"><method name="&lt;init&gt;" desc="(Ljava/lang/String;)V" line="21"><counter type="INSTRUCTION" missed="7" covered="0"/><counter type="LINE" missed="3" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="&lt;init&gt;" desc="(Ljava/lang/String;I)V" line="31"><counter type="INSTRUCTION" missed="0" covered="7"/><counter type="LINE" missed="0" covered="3"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getHttpStatusCode" desc="()I" line="40"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="10" covered="7"/><counter type="LINE" missed="4" covered="3"/><counter type="COMPLEXITY" missed="2" covered="1"/><counter type="METHOD" missed="2" covered="1"/><counter type="CLASS" missed="0" covered="1"/></class><sourcefile name="BinanceConnectorException.java"><line nr="17" mi="0" ci="3" mb="0" cb="0"/><line nr="18" mi="0" ci="1" mb="0" cb="0"/><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="2"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/><counter type="CLASS" missed="0" covered="1"/></sourcefile><sourcefile name="BinanceServerException.java"><line nr="21" mi="3" ci="0" mb="0" cb="0"/><line nr="22" mi="3" ci="0" mb="0" cb="0"/><line nr="23" mi="1" ci="0" mb="0" cb="0"/><line nr="31" mi="0" ci="3" mb="0" cb="0"/><line nr="32" mi="0" ci="3" mb="0" cb="0"/><line nr="33" mi="0" ci="1" mb="0" cb="0"/><line nr="40" mi="3" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="10" covered="7"/><counter type="LINE" missed="4" covered="3"/><counter type="COMPLEXITY" missed="2" covered="1"/><counter type="METHOD" missed="2" covered="1"/><counter type="CLASS" missed="0" covered="1"/></sourcefile><sourcefile name="BinanceClientException.java"><line nr="7" mi="0" ci="6" mb="0" cb="0"/><line nr="21" mi="0" ci="3" mb="0" cb="0"/><line nr="22" mi="0" ci="3" mb="0" cb="0"/><line nr="23" mi="0" ci="3" mb="0" cb="0"/><line nr="24" mi="0" ci="1" mb="0" cb="0"/><line nr="34" mi="0" ci="3" mb="0" cb="0"/><line nr="35" mi="0" ci="3" mb="0" cb="0"/><line nr="36" mi="0" ci="3" mb="0" cb="0"/><line nr="37" mi="0" ci="3" mb="0" cb="0"/><line nr="38" mi="0" ci="1" mb="0" cb="0"/><line nr="45" mi="3" ci="0" mb="0" cb="0"/><line nr="53" mi="3" ci="0" mb="0" cb="0"/><line nr="61" mi="3" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="9" covered="29"/><counter type="LINE" missed="3" covered="10"/><counter type="COMPLEXITY" missed="3" covered="2"/><counter type="METHOD" missed="3" covered="2"/><counter type="CLASS" missed="0" covered="1"/></sourcefile><counter type="INSTRUCTION" missed="19" covered="40"/><counter type="LINE" missed="7" covered="15"/><counter type="COMPLEXITY" missed="5" covered="4"/><counter type="METHOD" missed="5" covered="4"/><counter type="CLASS" missed="0" covered="3"/></package><package name="com/binance/connector/futures/client/enums"><class name="com/binance/connector/futures/client/enums/HttpMethod" sourcefilename="HttpMethod.java"><method name="&lt;clinit&gt;" desc="()V" line="3"><counter type="INSTRUCTION" missed="0" covered="33"/><counter type="LINE" missed="0" covered="6"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="0" covered="33"/><counter type="LINE" missed="0" covered="6"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/><counter type="CLASS" missed="0" covered="1"/></class><class name="com/binance/connector/futures/client/enums/DefaultUrls" sourcefilename="DefaultUrls.java"/><class name="com/binance/connector/futures/client/enums/RequestType" sourcefilename="RequestType.java"><method name="&lt;clinit&gt;" desc="()V" line="3"><counter type="INSTRUCTION" missed="0" covered="21"/><counter type="LINE" missed="0" covered="4"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="0" covered="21"/><counter type="LINE" missed="0" covered="4"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/><counter type="CLASS" missed="0" covered="1"/></class><sourcefile name="RequestType.java"><line nr="3" mi="0" ci="3" mb="0" cb="0"/><line nr="4" mi="0" ci="6" mb="0" cb="0"/><line nr="5" mi="0" ci="6" mb="0" cb="0"/><line nr="6" mi="0" ci="6" mb="0" cb="0"/><counter type="INSTRUCTION" missed="0" covered="21"/><counter type="LINE" missed="0" covered="4"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/><counter type="CLASS" missed="0" covered="1"/></sourcefile><sourcefile name="HttpMethod.java"><line nr="3" mi="0" ci="3" mb="0" cb="0"/><line nr="4" mi="0" ci="6" mb="0" cb="0"/><line nr="5" mi="0" ci="6" mb="0" cb="0"/><line nr="6" mi="0" ci="6" mb="0" cb="0"/><line nr="7" mi="0" ci="6" mb="0" cb="0"/><line nr="8" mi="0" ci="6" mb="0" cb="0"/><counter type="INSTRUCTION" missed="0" covered="33"/><counter type="LINE" missed="0" covered="6"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/><counter type="CLASS" missed="0" covered="1"/></sourcefile><sourcefile name="DefaultUrls.java"/><counter type="INSTRUCTION" missed="0" covered="54"/><counter type="LINE" missed="0" covered="10"/><counter type="COMPLEXITY" missed="0" covered="2"/><counter type="METHOD" missed="0" covered="2"/><counter type="CLASS" missed="0" covered="2"/></package><package name="com/binance/connector/futures/logging/util"><class name="com/binance/connector/futures/logging/util/MsEpochConverter" sourcefilename="MsEpochConverter.java"><method name="&lt;init&gt;" desc="()V" line="6"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="convert" desc="(Lch/qos/logback/classic/spi/ILoggingEvent;)Ljava/lang/String;" line="10"><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="0" covered="7"/><counter type="LINE" missed="0" covered="2"/><counter type="COMPLEXITY" missed="0" covered="2"/><counter type="METHOD" missed="0" covered="2"/><counter type="CLASS" missed="0" covered="1"/></class><sourcefile name="MsEpochConverter.java"><line nr="6" mi="0" ci="3" mb="0" cb="0"/><line nr="10" mi="0" ci="4" mb="0" cb="0"/><counter type="INSTRUCTION" missed="0" covered="7"/><counter type="LINE" missed="0" covered="2"/><counter type="COMPLEXITY" missed="0" covered="2"/><counter type="METHOD" missed="0" covered="2"/><counter type="CLASS" missed="0" covered="1"/></sourcefile><counter type="INSTRUCTION" missed="0" covered="7"/><counter type="LINE" missed="0" covered="2"/><counter type="COMPLEXITY" missed="0" covered="2"/><counter type="METHOD" missed="0" covered="2"/><counter type="CLASS" missed="0" covered="1"/></package><package name="com/binance/connector/futures/client/impl/cm_futures"><class name="com/binance/connector/futures/client/impl/cm_futures/CMPortfolioMargin" sourcefilename="CMPortfolioMargin.java"><method name="&lt;init&gt;" desc="(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ZLcom/binance/connector/futures/client/utils/ProxyAuth;)V" line="17"><counter type="INSTRUCTION" missed="0" covered="8"/><counter type="LINE" missed="0" covered="2"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="portfolioMarginExchangeInfo" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="35"><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="0" covered="12"/><counter type="LINE" missed="0" covered="3"/><counter type="COMPLEXITY" missed="0" covered="2"/><counter type="METHOD" missed="0" covered="2"/><counter type="CLASS" missed="0" covered="1"/></class><class name="com/binance/connector/futures/client/impl/cm_futures/CMAccount" sourcefilename="CMAccount.java"><method name="&lt;init&gt;" desc="(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ZLcom/binance/connector/futures/client/utils/ProxyAuth;)V" line="19"><counter type="INSTRUCTION" missed="0" covered="26"/><counter type="LINE" missed="0" covered="8"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="modifyOrder" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="44"><counter type="INSTRUCTION" missed="0" covered="19"/><counter type="LINE" missed="0" covered="3"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="orderModifyHistory" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="71"><counter type="INSTRUCTION" missed="0" covered="15"/><counter type="LINE" missed="0" covered="2"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="currentAllOpenOrders" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="93"><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="allOrders" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="117"><counter type="INSTRUCTION" missed="0" covered="8"/><counter type="LINE" missed="0" covered="2"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="futuresAccountBalance" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="137"><counter type="INSTRUCTION" missed="0" covered="11"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="accountInformation" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="156"><counter type="INSTRUCTION" missed="0" covered="11"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="positionInformation" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="177"><counter type="INSTRUCTION" missed="0" covered="11"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="accountTradeList" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="201"><counter type="INSTRUCTION" missed="0" covered="8"/><counter type="LINE" missed="0" covered="2"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getLeverageBracket" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="221"><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getLeverageBracketForPair" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="241"><counter type="INSTRUCTION" missed="0" covered="11"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="0" covered="128"/><counter type="LINE" missed="0" covered="23"/><counter type="COMPLEXITY" missed="0" covered="11"/><counter type="METHOD" missed="0" covered="11"/><counter type="CLASS" missed="0" covered="1"/></class><class name="com/binance/connector/futures/client/impl/cm_futures/CMUserData" sourcefilename="CMUserData.java"><method name="&lt;init&gt;" desc="(Ljava/lang/String;Ljava/lang/String;ZLcom/binance/connector/futures/client/utils/ProxyAuth;)V" line="16"><counter type="INSTRUCTION" missed="0" covered="7"/><counter type="LINE" missed="0" covered="2"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="0" covered="7"/><counter type="LINE" missed="0" covered="2"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/><counter type="CLASS" missed="0" covered="1"/></class><class name="com/binance/connector/futures/client/impl/cm_futures/CMMarket" sourcefilename="CMMarket.java"><method name="&lt;init&gt;" desc="(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ZLcom/binance/connector/futures/client/utils/ProxyAuth;)V" line="19"><counter type="INSTRUCTION" missed="0" covered="11"/><counter type="LINE" missed="0" covered="3"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="markPrice" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="38"><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="ticker24H" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="56"><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="tickerSymbol" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="75"><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="bookTicker" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="94"><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="openInterestStatistics" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="117"><counter type="INSTRUCTION" missed="0" covered="12"/><counter type="LINE" missed="0" covered="3"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="topTraderLongShortPos" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="142"><counter type="INSTRUCTION" missed="0" covered="12"/><counter type="LINE" missed="0" covered="3"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="topTraderLongShortAccs" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="167"><counter type="INSTRUCTION" missed="0" covered="12"/><counter type="LINE" missed="0" covered="3"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="longShortRatio" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="192"><counter type="INSTRUCTION" missed="0" covered="12"/><counter type="LINE" missed="0" covered="3"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="basis" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="219"><counter type="INSTRUCTION" missed="0" covered="23"/><counter type="LINE" missed="0" covered="4"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="0" covered="98"/><counter type="LINE" missed="0" covered="23"/><counter type="COMPLEXITY" missed="0" covered="10"/><counter type="METHOD" missed="0" covered="10"/><counter type="CLASS" missed="0" covered="1"/></class><sourcefile name="CMPortfolioMargin.java"><line nr="17" mi="0" ci="7" mb="0" cb="0"/><line nr="18" mi="0" ci="1" mb="0" cb="0"/><line nr="35" mi="0" ci="4" mb="0" cb="0"/><counter type="INSTRUCTION" missed="0" covered="12"/><counter type="LINE" missed="0" covered="3"/><counter type="COMPLEXITY" missed="0" covered="2"/><counter type="METHOD" missed="0" covered="2"/><counter type="CLASS" missed="0" covered="1"/></sourcefile><sourcefile name="CMAccount.java"><line nr="19" mi="0" ci="7" mb="0" cb="0"/><line nr="20" mi="0" ci="1" mb="0" cb="0"/><line nr="22" mi="0" ci="3" mb="0" cb="0"/><line nr="44" mi="0" ci="4" mb="0" cb="0"/><line nr="45" mi="0" ci="4" mb="0" cb="0"/><line nr="46" mi="0" ci="11" mb="0" cb="0"/><line nr="49" mi="0" ci="3" mb="0" cb="0"/><line nr="71" mi="0" ci="4" mb="0" cb="0"/><line nr="72" mi="0" ci="11" mb="0" cb="0"/><line nr="93" mi="0" ci="4" mb="0" cb="0"/><line nr="117" mi="0" ci="4" mb="0" cb="0"/><line nr="118" mi="0" ci="4" mb="0" cb="0"/><line nr="121" mi="0" ci="3" mb="0" cb="0"/><line nr="137" mi="0" ci="11" mb="0" cb="0"/><line nr="140" mi="0" ci="3" mb="0" cb="0"/><line nr="156" mi="0" ci="11" mb="0" cb="0"/><line nr="159" mi="0" ci="3" mb="0" cb="0"/><line nr="177" mi="0" ci="11" mb="0" cb="0"/><line nr="201" mi="0" ci="4" mb="0" cb="0"/><line nr="202" mi="0" ci="4" mb="0" cb="0"/><line nr="221" mi="0" ci="4" mb="0" cb="0"/><line nr="224" mi="0" ci="3" mb="0" cb="0"/><line nr="241" mi="0" ci="11" mb="0" cb="0"/><counter type="INSTRUCTION" missed="0" covered="128"/><counter type="LINE" missed="0" covered="23"/><counter type="COMPLEXITY" missed="0" covered="11"/><counter type="METHOD" missed="0" covered="11"/><counter type="CLASS" missed="0" covered="1"/></sourcefile><sourcefile name="CMUserData.java"><line nr="16" mi="0" ci="6" mb="0" cb="0"/><line nr="17" mi="0" ci="1" mb="0" cb="0"/><counter type="INSTRUCTION" missed="0" covered="7"/><counter type="LINE" missed="0" covered="2"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/><counter type="CLASS" missed="0" covered="1"/></sourcefile><sourcefile name="CMMarket.java"><line nr="19" mi="0" ci="7" mb="0" cb="0"/><line nr="20" mi="0" ci="1" mb="0" cb="0"/><line nr="38" mi="0" ci="4" mb="0" cb="0"/><line nr="56" mi="0" ci="4" mb="0" cb="0"/><line nr="75" mi="0" ci="4" mb="0" cb="0"/><line nr="94" mi="0" ci="4" mb="0" cb="0"/><line nr="117" mi="0" ci="4" mb="0" cb="0"/><line nr="118" mi="0" ci="4" mb="0" cb="0"/><line nr="119" mi="0" ci="4" mb="0" cb="0"/><line nr="142" mi="0" ci="4" mb="0" cb="0"/><line nr="143" mi="0" ci="4" mb="0" cb="0"/><line nr="144" mi="0" ci="4" mb="0" cb="0"/><line nr="167" mi="0" ci="4" mb="0" cb="0"/><line nr="168" mi="0" ci="4" mb="0" cb="0"/><line nr="169" mi="0" ci="4" mb="0" cb="0"/><line nr="192" mi="0" ci="4" mb="0" cb="0"/><line nr="193" mi="0" ci="4" mb="0" cb="0"/><line nr="194" mi="0" ci="4" mb="0" cb="0"/><line nr="197" mi="0" ci="3" mb="0" cb="0"/><line nr="219" mi="0" ci="4" mb="0" cb="0"/><line nr="220" mi="0" ci="4" mb="0" cb="0"/><line nr="221" mi="0" ci="4" mb="0" cb="0"/><line nr="222" mi="0" ci="11" mb="0" cb="0"/><counter type="INSTRUCTION" missed="0" covered="98"/><counter type="LINE" missed="0" covered="23"/><counter type="COMPLEXITY" missed="0" covered="10"/><counter type="METHOD" missed="0" covered="10"/><counter type="CLASS" missed="0" covered="1"/></sourcefile><counter type="INSTRUCTION" missed="0" covered="245"/><counter type="LINE" missed="0" covered="51"/><counter type="COMPLEXITY" missed="0" covered="24"/><counter type="METHOD" missed="0" covered="24"/><counter type="CLASS" missed="0" covered="4"/></package><package name="com/binance/connector/futures/client"><class name="com/binance/connector/futures/client/WebsocketClient" sourcefilename="WebsocketClient.java"/><class name="com/binance/connector/futures/client/FuturesClient" sourcefilename="FuturesClient.java"><method name="portfolioMargin" desc="()Lcom/binance/connector/futures/client/impl/futures/PortfolioMargin;" line="14"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><sourcefile name="WebsocketClient.java"/><sourcefile name="FuturesClient.java"><line nr="14" mi="5" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/><counter type="CLASS" missed="1" covered="0"/></sourcefile><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/><counter type="CLASS" missed="1" covered="0"/></package><package name="com/binance/connector/futures/client/impl/futures"><class name="com/binance/connector/futures/client/impl/futures/PortfolioMargin" sourcefilename="PortfolioMargin.java"><method name="&lt;init&gt;" desc="(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ZLcom/binance/connector/futures/client/utils/ProxyAuth;)V" line="18"><counter type="INSTRUCTION" missed="0" covered="23"/><counter type="LINE" missed="0" covered="7"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getProductUrl" desc="()Ljava/lang/String;" line="25"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getRequestHandler" desc="()Lcom/binance/connector/futures/client/utils/RequestHandler;" line="29"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getShowLimitUsage" desc="()Z" line="33"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setProductUrl" desc="(Ljava/lang/String;)V" line="37"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setRequestHandler" desc="(Ljava/lang/String;Ljava/lang/String;Lcom/binance/connector/futures/client/utils/ProxyAuth;)V" line="41"><counter type="INSTRUCTION" missed="9" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setShowLimitUsage" desc="(Z)V" line="45"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="portfolioMarginExchangeInfo" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="50"><counter type="INSTRUCTION" missed="0" covered="11"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="portfolioMarginAccountInfo" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="69"><counter type="INSTRUCTION" missed="0" covered="15"/><counter type="LINE" missed="0" covered="2"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="26" covered="49"/><counter type="LINE" missed="9" covered="10"/><counter type="COMPLEXITY" missed="6" covered="3"/><counter type="METHOD" missed="6" covered="3"/><counter type="CLASS" missed="0" covered="1"/></class><class name="com/binance/connector/futures/client/impl/futures/Market" sourcefilename="Market.java"><method name="&lt;init&gt;" desc="(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ZLcom/binance/connector/futures/client/utils/ProxyAuth;)V" line="19"><counter type="INSTRUCTION" missed="0" covered="82"/><counter type="LINE" missed="0" covered="27"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getBaseUrl" desc="()Ljava/lang/String;" line="27"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getProductUrl" desc="()Ljava/lang/String;" line="31"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getRequestHandler" desc="()Lcom/binance/connector/futures/client/utils/RequestHandler;" line="35"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getShowLimitUsage" desc="()Z" line="39"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="setBaseUrl" desc="(Ljava/lang/String;)V" line="43"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setProductUrl" desc="(Ljava/lang/String;)V" line="47"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setRequestHandler" desc="(Ljava/lang/String;Ljava/lang/String;Lcom/binance/connector/futures/client/utils/ProxyAuth;)V" line="51"><counter type="INSTRUCTION" missed="8" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setShowLimitUsage" desc="(Z)V" line="55"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="markPrice" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="60"><counter type="INSTRUCTION" missed="0" covered="11"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="ticker24H" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="65"><counter type="INSTRUCTION" missed="0" covered="11"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="tickerSymbol" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="70"><counter type="INSTRUCTION" missed="0" covered="11"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="bookTicker" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="75"><counter type="INSTRUCTION" missed="0" covered="11"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="openInterestStatistics" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="80"><counter type="INSTRUCTION" missed="0" covered="11"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="topTraderLongShortPos" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="85"><counter type="INSTRUCTION" missed="0" covered="11"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="topTraderLongShortAccs" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="90"><counter type="INSTRUCTION" missed="0" covered="11"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="longShortRatio" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="95"><counter type="INSTRUCTION" missed="0" covered="11"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="ping" desc="()Ljava/lang/String;" line="112"><counter type="INSTRUCTION" missed="0" covered="11"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="time" desc="()Ljava/lang/String;" line="128"><counter type="INSTRUCTION" missed="0" covered="11"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="exchangeInfo" desc="()Ljava/lang/String;" line="144"><counter type="INSTRUCTION" missed="0" covered="11"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="depth" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="165"><counter type="INSTRUCTION" missed="0" covered="15"/><counter type="LINE" missed="0" covered="2"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="trades" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="188"><counter type="INSTRUCTION" missed="0" covered="15"/><counter type="LINE" missed="0" covered="2"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="historicalTrades" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="213"><counter type="INSTRUCTION" missed="0" covered="15"/><counter type="LINE" missed="0" covered="2"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="aggTrades" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="240"><counter type="INSTRUCTION" missed="0" covered="15"/><counter type="LINE" missed="0" covered="2"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="klines" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="267"><counter type="INSTRUCTION" missed="0" covered="19"/><counter type="LINE" missed="0" covered="3"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="continuousKlines" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="296"><counter type="INSTRUCTION" missed="0" covered="23"/><counter type="LINE" missed="0" covered="4"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="indexPriceKlines" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="325"><counter type="INSTRUCTION" missed="0" covered="19"/><counter type="LINE" missed="0" covered="3"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="markPriceKlines" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="353"><counter type="INSTRUCTION" missed="0" covered="19"/><counter type="LINE" missed="0" covered="3"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="fundingRate" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="379"><counter type="INSTRUCTION" missed="0" covered="11"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="openInterest" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="401"><counter type="INSTRUCTION" missed="0" covered="15"/><counter type="LINE" missed="0" covered="2"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="20" covered="381"/><counter type="LINE" missed="8" covered="66"/><counter type="COMPLEXITY" missed="4" covered="26"/><counter type="METHOD" missed="4" covered="26"/><counter type="CLASS" missed="0" covered="1"/></class><class name="com/binance/connector/futures/client/impl/futures/Account" sourcefilename="Account.java"><method name="&lt;init&gt;" desc="(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ZLcom/binance/connector/futures/client/utils/ProxyAuth;)V" line="18"><counter type="INSTRUCTION" missed="0" covered="71"/><counter type="LINE" missed="0" covered="23"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getProductUrl" desc="()Ljava/lang/String;" line="25"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getRequestHandler" desc="()Lcom/binance/connector/futures/client/utils/RequestHandler;" line="29"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getShowLimitUsage" desc="()Z" line="33"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="setProductUrl" desc="(Ljava/lang/String;)V" line="37"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setRequestHandler" desc="(Ljava/lang/String;Ljava/lang/String;Lcom/binance/connector/futures/client/utils/ProxyAuth;)V" line="41"><counter type="INSTRUCTION" missed="9" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setShowLimitUsage" desc="(Z)V" line="45"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="currentAllOpenOrders" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="50"><counter type="INSTRUCTION" missed="0" covered="11"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="allOrders" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="55"><counter type="INSTRUCTION" missed="0" covered="11"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="accountTradeList" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="60"><counter type="INSTRUCTION" missed="0" covered="11"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getLeverageBracket" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="65"><counter type="INSTRUCTION" missed="0" covered="11"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="changePositionModeTrade" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="87"><counter type="INSTRUCTION" missed="0" covered="15"/><counter type="LINE" missed="0" covered="2"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getCurrentPositionMode" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="108"><counter type="INSTRUCTION" missed="0" covered="11"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="newOrder" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="145"><counter type="INSTRUCTION" missed="0" covered="23"/><counter type="LINE" missed="0" covered="4"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="placeMultipleOrders" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="170"><counter type="INSTRUCTION" missed="14" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="queryOrder" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="194"><counter type="INSTRUCTION" missed="0" covered="19"/><counter type="LINE" missed="0" covered="3"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="cancelOrder" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="219"><counter type="INSTRUCTION" missed="0" covered="19"/><counter type="LINE" missed="0" covered="3"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="cancelAllOpenOrders" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="243"><counter type="INSTRUCTION" missed="0" covered="15"/><counter type="LINE" missed="0" covered="2"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="cancelMultipleOrders" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="267"><counter type="INSTRUCTION" missed="11" covered="4"/><counter type="LINE" missed="1" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="autoCancelOpen" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="291"><counter type="INSTRUCTION" missed="0" covered="19"/><counter type="LINE" missed="0" covered="3"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="queryCurrentOpenOrder" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="317"><counter type="INSTRUCTION" missed="0" covered="19"/><counter type="LINE" missed="0" covered="3"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="changeInitialLeverage" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="342"><counter type="INSTRUCTION" missed="0" covered="19"/><counter type="LINE" missed="0" covered="3"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="changeMarginType" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="367"><counter type="INSTRUCTION" missed="0" covered="19"/><counter type="LINE" missed="0" covered="3"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="modifyIsolatedPositionMargin" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="394"><counter type="INSTRUCTION" missed="0" covered="22"/><counter type="LINE" missed="0" covered="4"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getPositionMarginChangeHistory" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="423"><counter type="INSTRUCTION" missed="0" covered="15"/><counter type="LINE" missed="0" covered="2"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getIncomeHistory" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="450"><counter type="INSTRUCTION" missed="0" covered="11"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getAdlQuantile" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="472"><counter type="INSTRUCTION" missed="0" covered="11"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getForceOrders" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="498"><counter type="INSTRUCTION" missed="0" covered="11"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getCommissionRate" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="520"><counter type="INSTRUCTION" missed="0" covered="15"/><counter type="LINE" missed="0" covered="2"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="42" covered="391"/><counter type="LINE" missed="9" covered="69"/><counter type="COMPLEXITY" missed="4" covered="25"/><counter type="METHOD" missed="4" covered="25"/><counter type="CLASS" missed="0" covered="1"/></class><class name="com/binance/connector/futures/client/impl/futures/UserData" sourcefilename="UserData.java"><method name="&lt;init&gt;" desc="(Ljava/lang/String;Ljava/lang/String;ZLcom/binance/connector/futures/client/utils/ProxyAuth;)V" line="16"><counter type="INSTRUCTION" missed="0" covered="19"/><counter type="LINE" missed="0" covered="6"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getProductUrl" desc="()Ljava/lang/String;" line="23"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getRequestHandler" desc="()Lcom/binance/connector/futures/client/utils/RequestHandler;" line="27"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getShowLimitUsage" desc="()Z" line="31"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setProductUrl" desc="(Ljava/lang/String;)V" line="35"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setRequestHandler" desc="(Ljava/lang/String;Lcom/binance/connector/futures/client/utils/ProxyAuth;)V" line="39"><counter type="INSTRUCTION" missed="8" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setShowLimitUsage" desc="(Z)V" line="43"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="createListenKey" desc="()Ljava/lang/String;" line="58"><counter type="INSTRUCTION" missed="0" covered="11"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="extendListenKey" desc="()Ljava/lang/String;" line="72"><counter type="INSTRUCTION" missed="0" covered="11"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="closeListenKey" desc="()Ljava/lang/String;" line="85"><counter type="INSTRUCTION" missed="0" covered="11"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="25" covered="52"/><counter type="LINE" missed="9" covered="9"/><counter type="COMPLEXITY" missed="6" covered="4"/><counter type="METHOD" missed="6" covered="4"/><counter type="CLASS" missed="0" covered="1"/></class><sourcefile name="UserData.java"><line nr="16" mi="0" ci="2" mb="0" cb="0"/><line nr="17" mi="0" ci="3" mb="0" cb="0"/><line nr="18" mi="0" ci="7" mb="0" cb="0"/><line nr="19" mi="0" ci="3" mb="0" cb="0"/><line nr="20" mi="0" ci="1" mb="0" cb="0"/><line nr="23" mi="3" ci="0" mb="0" cb="0"/><line nr="27" mi="3" ci="0" mb="0" cb="0"/><line nr="31" mi="3" ci="0" mb="0" cb="0"/><line nr="35" mi="3" ci="0" mb="0" cb="0"/><line nr="36" mi="1" ci="0" mb="0" cb="0"/><line nr="39" mi="7" ci="0" mb="0" cb="0"/><line nr="40" mi="1" ci="0" mb="0" cb="0"/><line nr="43" mi="3" ci="0" mb="0" cb="0"/><line nr="44" mi="1" ci="0" mb="0" cb="0"/><line nr="46" mi="0" ci="3" mb="0" cb="0"/><line nr="58" mi="0" ci="11" mb="0" cb="0"/><line nr="72" mi="0" ci="11" mb="0" cb="0"/><line nr="85" mi="0" ci="11" mb="0" cb="0"/><counter type="INSTRUCTION" missed="25" covered="52"/><counter type="LINE" missed="9" covered="9"/><counter type="COMPLEXITY" missed="6" covered="4"/><counter type="METHOD" missed="6" covered="4"/><counter type="CLASS" missed="0" covered="1"/></sourcefile><sourcefile name="Account.java"><line nr="18" mi="0" ci="2" mb="0" cb="0"/><line nr="19" mi="0" ci="3" mb="0" cb="0"/><line nr="20" mi="0" ci="8" mb="0" cb="0"/><line nr="21" mi="0" ci="3" mb="0" cb="0"/><line nr="22" mi="0" ci="1" mb="0" cb="0"/><line nr="25" mi="0" ci="3" mb="0" cb="0"/><line nr="29" mi="0" ci="3" mb="0" cb="0"/><line nr="33" mi="0" ci="3" mb="0" cb="0"/><line nr="37" mi="3" ci="0" mb="0" cb="0"/><line nr="38" mi="1" ci="0" mb="0" cb="0"/><line nr="41" mi="8" ci="0" mb="0" cb="0"/><line nr="42" mi="1" ci="0" mb="0" cb="0"/><line nr="45" mi="3" ci="0" mb="0" cb="0"/><line nr="46" mi="1" ci="0" mb="0" cb="0"/><line nr="48" mi="0" ci="3" mb="0" cb="0"/><line nr="50" mi="0" ci="11" mb="0" cb="0"/><line nr="53" mi="0" ci="3" mb="0" cb="0"/><line nr="55" mi="0" ci="11" mb="0" cb="0"/><line nr="58" mi="0" ci="3" mb="0" cb="0"/><line nr="60" mi="0" ci="11" mb="0" cb="0"/><line nr="63" mi="0" ci="3" mb="0" cb="0"/><line nr="65" mi="0" ci="11" mb="0" cb="0"/><line nr="68" mi="0" ci="3" mb="0" cb="0"/><line nr="87" mi="0" ci="4" mb="0" cb="0"/><line nr="88" mi="0" ci="11" mb="0" cb="0"/><line nr="108" mi="0" ci="11" mb="0" cb="0"/><line nr="111" mi="0" ci="3" mb="0" cb="0"/><line nr="145" mi="0" ci="4" mb="0" cb="0"/><line nr="146" mi="0" ci="4" mb="0" cb="0"/><line nr="147" mi="0" ci="4" mb="0" cb="0"/><line nr="148" mi="0" ci="11" mb="0" cb="0"/><line nr="151" mi="0" ci="3" mb="0" cb="0"/><line nr="170" mi="3" ci="0" mb="0" cb="0"/><line nr="171" mi="11" ci="0" mb="0" cb="0"/><line nr="194" mi="0" ci="4" mb="0" cb="0"/><line nr="195" mi="0" ci="4" mb="0" cb="0"/><line nr="196" mi="0" ci="11" mb="0" cb="0"/><line nr="219" mi="0" ci="4" mb="0" cb="0"/><line nr="220" mi="0" ci="4" mb="0" cb="0"/><line nr="221" mi="0" ci="11" mb="0" cb="0"/><line nr="224" mi="0" ci="3" mb="0" cb="0"/><line nr="243" mi="0" ci="4" mb="0" cb="0"/><line nr="244" mi="0" ci="11" mb="0" cb="0"/><line nr="267" mi="0" ci="4" mb="0" cb="0"/><line nr="268" mi="11" ci="0" mb="0" cb="0"/><line nr="271" mi="0" ci="3" mb="0" cb="0"/><line nr="291" mi="0" ci="4" mb="0" cb="0"/><line nr="292" mi="0" ci="4" mb="0" cb="0"/><line nr="293" mi="0" ci="11" mb="0" cb="0"/><line nr="296" mi="0" ci="3" mb="0" cb="0"/><line nr="317" mi="0" ci="4" mb="0" cb="0"/><line nr="318" mi="0" ci="4" mb="0" cb="0"/><line nr="319" mi="0" ci="11" mb="0" cb="0"/><line nr="322" mi="0" ci="3" mb="0" cb="0"/><line nr="342" mi="0" ci="4" mb="0" cb="0"/><line nr="343" mi="0" ci="4" mb="0" cb="0"/><line nr="344" mi="0" ci="11" mb="0" cb="0"/><line nr="347" mi="0" ci="3" mb="0" cb="0"/><line nr="367" mi="0" ci="4" mb="0" cb="0"/><line nr="368" mi="0" ci="4" mb="0" cb="0"/><line nr="369" mi="0" ci="11" mb="0" cb="0"/><line nr="372" mi="0" ci="3" mb="0" cb="0"/><line nr="394" mi="0" ci="4" mb="0" cb="0"/><line nr="395" mi="0" ci="3" mb="0" cb="0"/><line nr="396" mi="0" ci="4" mb="0" cb="0"/><line nr="397" mi="0" ci="11" mb="0" cb="0"/><line nr="400" mi="0" ci="3" mb="0" cb="0"/><line nr="423" mi="0" ci="4" mb="0" cb="0"/><line nr="424" mi="0" ci="11" mb="0" cb="0"/><line nr="427" mi="0" ci="3" mb="0" cb="0"/><line nr="450" mi="0" ci="11" mb="0" cb="0"/><line nr="453" mi="0" ci="3" mb="0" cb="0"/><line nr="472" mi="0" ci="11" mb="0" cb="0"/><line nr="475" mi="0" ci="3" mb="0" cb="0"/><line nr="498" mi="0" ci="11" mb="0" cb="0"/><line nr="501" mi="0" ci="3" mb="0" cb="0"/><line nr="520" mi="0" ci="4" mb="0" cb="0"/><line nr="521" mi="0" ci="11" mb="0" cb="0"/><counter type="INSTRUCTION" missed="42" covered="391"/><counter type="LINE" missed="9" covered="69"/><counter type="COMPLEXITY" missed="4" covered="25"/><counter type="METHOD" missed="4" covered="25"/><counter type="CLASS" missed="0" covered="1"/></sourcefile><sourcefile name="Market.java"><line nr="19" mi="0" ci="2" mb="0" cb="0"/><line nr="20" mi="0" ci="3" mb="0" cb="0"/><line nr="21" mi="0" ci="3" mb="0" cb="0"/><line nr="22" mi="0" ci="7" mb="0" cb="0"/><line nr="23" mi="0" ci="3" mb="0" cb="0"/><line nr="24" mi="0" ci="1" mb="0" cb="0"/><line nr="27" mi="0" ci="3" mb="0" cb="0"/><line nr="31" mi="0" ci="3" mb="0" cb="0"/><line nr="35" mi="0" ci="3" mb="0" cb="0"/><line nr="39" mi="0" ci="3" mb="0" cb="0"/><line nr="43" mi="3" ci="0" mb="0" cb="0"/><line nr="44" mi="1" ci="0" mb="0" cb="0"/><line nr="47" mi="3" ci="0" mb="0" cb="0"/><line nr="48" mi="1" ci="0" mb="0" cb="0"/><line nr="51" mi="7" ci="0" mb="0" cb="0"/><line nr="52" mi="1" ci="0" mb="0" cb="0"/><line nr="55" mi="3" ci="0" mb="0" cb="0"/><line nr="56" mi="1" ci="0" mb="0" cb="0"/><line nr="58" mi="0" ci="3" mb="0" cb="0"/><line nr="60" mi="0" ci="11" mb="0" cb="0"/><line nr="63" mi="0" ci="3" mb="0" cb="0"/><line nr="65" mi="0" ci="11" mb="0" cb="0"/><line nr="68" mi="0" ci="3" mb="0" cb="0"/><line nr="70" mi="0" ci="11" mb="0" cb="0"/><line nr="73" mi="0" ci="3" mb="0" cb="0"/><line nr="75" mi="0" ci="11" mb="0" cb="0"/><line nr="78" mi="0" ci="3" mb="0" cb="0"/><line nr="80" mi="0" ci="11" mb="0" cb="0"/><line nr="83" mi="0" ci="3" mb="0" cb="0"/><line nr="85" mi="0" ci="11" mb="0" cb="0"/><line nr="88" mi="0" ci="3" mb="0" cb="0"/><line nr="90" mi="0" ci="11" mb="0" cb="0"/><line nr="93" mi="0" ci="3" mb="0" cb="0"/><line nr="95" mi="0" ci="11" mb="0" cb="0"/><line nr="99" mi="0" ci="3" mb="0" cb="0"/><line nr="112" mi="0" ci="11" mb="0" cb="0"/><line nr="115" mi="0" ci="3" mb="0" cb="0"/><line nr="128" mi="0" ci="11" mb="0" cb="0"/><line nr="131" mi="0" ci="3" mb="0" cb="0"/><line nr="144" mi="0" ci="11" mb="0" cb="0"/><line nr="147" mi="0" ci="3" mb="0" cb="0"/><line nr="165" mi="0" ci="4" mb="0" cb="0"/><line nr="166" mi="0" ci="11" mb="0" cb="0"/><line nr="169" mi="0" ci="3" mb="0" cb="0"/><line nr="188" mi="0" ci="4" mb="0" cb="0"/><line nr="189" mi="0" ci="11" mb="0" cb="0"/><line nr="192" mi="0" ci="3" mb="0" cb="0"/><line nr="213" mi="0" ci="4" mb="0" cb="0"/><line nr="214" mi="0" ci="11" mb="0" cb="0"/><line nr="217" mi="0" ci="3" mb="0" cb="0"/><line nr="240" mi="0" ci="4" mb="0" cb="0"/><line nr="241" mi="0" ci="11" mb="0" cb="0"/><line nr="244" mi="0" ci="3" mb="0" cb="0"/><line nr="267" mi="0" ci="4" mb="0" cb="0"/><line nr="268" mi="0" ci="4" mb="0" cb="0"/><line nr="269" mi="0" ci="11" mb="0" cb="0"/><line nr="272" mi="0" ci="3" mb="0" cb="0"/><line nr="296" mi="0" ci="4" mb="0" cb="0"/><line nr="297" mi="0" ci="4" mb="0" cb="0"/><line nr="298" mi="0" ci="4" mb="0" cb="0"/><line nr="299" mi="0" ci="11" mb="0" cb="0"/><line nr="302" mi="0" ci="3" mb="0" cb="0"/><line nr="325" mi="0" ci="4" mb="0" cb="0"/><line nr="326" mi="0" ci="4" mb="0" cb="0"/><line nr="327" mi="0" ci="11" mb="0" cb="0"/><line nr="330" mi="0" ci="3" mb="0" cb="0"/><line nr="353" mi="0" ci="4" mb="0" cb="0"/><line nr="354" mi="0" ci="4" mb="0" cb="0"/><line nr="355" mi="0" ci="11" mb="0" cb="0"/><line nr="358" mi="0" ci="3" mb="0" cb="0"/><line nr="379" mi="0" ci="11" mb="0" cb="0"/><line nr="382" mi="0" ci="3" mb="0" cb="0"/><line nr="401" mi="0" ci="4" mb="0" cb="0"/><line nr="402" mi="0" ci="11" mb="0" cb="0"/><counter type="INSTRUCTION" missed="20" covered="381"/><counter type="LINE" missed="8" covered="66"/><counter type="COMPLEXITY" missed="4" covered="26"/><counter type="METHOD" missed="4" covered="26"/><counter type="CLASS" missed="0" covered="1"/></sourcefile><sourcefile name="PortfolioMargin.java"><line nr="18" mi="0" ci="2" mb="0" cb="0"/><line nr="19" mi="0" ci="3" mb="0" cb="0"/><line nr="20" mi="0" ci="8" mb="0" cb="0"/><line nr="21" mi="0" ci="3" mb="0" cb="0"/><line nr="22" mi="0" ci="1" mb="0" cb="0"/><line nr="25" mi="3" ci="0" mb="0" cb="0"/><line nr="29" mi="3" ci="0" mb="0" cb="0"/><line nr="33" mi="3" ci="0" mb="0" cb="0"/><line nr="37" mi="3" ci="0" mb="0" cb="0"/><line nr="38" mi="1" ci="0" mb="0" cb="0"/><line nr="41" mi="8" ci="0" mb="0" cb="0"/><line nr="42" mi="1" ci="0" mb="0" cb="0"/><line nr="45" mi="3" ci="0" mb="0" cb="0"/><line nr="46" mi="1" ci="0" mb="0" cb="0"/><line nr="48" mi="0" ci="3" mb="0" cb="0"/><line nr="50" mi="0" ci="11" mb="0" cb="0"/><line nr="53" mi="0" ci="3" mb="0" cb="0"/><line nr="69" mi="0" ci="4" mb="0" cb="0"/><line nr="70" mi="0" ci="11" mb="0" cb="0"/><counter type="INSTRUCTION" missed="26" covered="49"/><counter type="LINE" missed="9" covered="10"/><counter type="COMPLEXITY" missed="6" covered="3"/><counter type="METHOD" missed="6" covered="3"/><counter type="CLASS" missed="0" covered="1"/></sourcefile><counter type="INSTRUCTION" missed="113" covered="873"/><counter type="LINE" missed="35" covered="154"/><counter type="COMPLEXITY" missed="20" covered="58"/><counter type="METHOD" missed="20" covered="58"/><counter type="CLASS" missed="0" covered="4"/></package><package name="com/binance/connector/futures/client/impl/um_futures"><class name="com/binance/connector/futures/client/impl/um_futures/UMUserData" sourcefilename="UMUserData.java"><method name="&lt;init&gt;" desc="(Ljava/lang/String;Ljava/lang/String;ZLcom/binance/connector/futures/client/utils/ProxyAuth;)V" line="16"><counter type="INSTRUCTION" missed="0" covered="7"/><counter type="LINE" missed="0" covered="2"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="0" covered="7"/><counter type="LINE" missed="0" covered="2"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/><counter type="CLASS" missed="0" covered="1"/></class><class name="com/binance/connector/futures/client/impl/um_futures/UMMarket" sourcefilename="UMMarket.java"><method name="&lt;init&gt;" desc="(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ZLcom/binance/connector/futures/client/utils/ProxyAuth;)V" line="19"><counter type="INSTRUCTION" missed="0" covered="20"/><counter type="LINE" missed="0" covered="6"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="markPrice" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="37"><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="ticker24H" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="55"><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="tickerSymbol" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="74"><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="bookTicker" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="93"><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="openInterestStatistics" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="116"><counter type="INSTRUCTION" missed="0" covered="12"/><counter type="LINE" missed="0" covered="3"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="topTraderLongShortPos" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="141"><counter type="INSTRUCTION" missed="0" covered="12"/><counter type="LINE" missed="0" covered="3"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="topTraderLongShortAccs" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="166"><counter type="INSTRUCTION" missed="0" covered="12"/><counter type="LINE" missed="0" covered="3"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="longShortRatio" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="191"><counter type="INSTRUCTION" missed="0" covered="12"/><counter type="LINE" missed="0" covered="3"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="takerBuySellVol" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="217"><counter type="INSTRUCTION" missed="0" covered="19"/><counter type="LINE" missed="0" covered="3"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="historicalBlvt" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="243"><counter type="INSTRUCTION" missed="0" covered="19"/><counter type="LINE" missed="0" covered="3"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="indexInfo" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="263"><counter type="INSTRUCTION" missed="0" covered="11"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="assetIndex" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="283"><counter type="INSTRUCTION" missed="0" covered="11"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="0" covered="144"/><counter type="LINE" missed="0" covered="30"/><counter type="COMPLEXITY" missed="0" covered="13"/><counter type="METHOD" missed="0" covered="13"/><counter type="CLASS" missed="0" covered="1"/></class><class name="com/binance/connector/futures/client/impl/um_futures/UMAccount" sourcefilename="UMAccount.java"><method name="&lt;init&gt;" desc="(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ZLcom/binance/connector/futures/client/utils/ProxyAuth;)V" line="19"><counter type="INSTRUCTION" missed="0" covered="29"/><counter type="LINE" missed="0" covered="9"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="changeMultiAssetsMode" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="39"><counter type="INSTRUCTION" missed="0" covered="15"/><counter type="LINE" missed="0" covered="2"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getCurrentMultiAssetMode" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="58"><counter type="INSTRUCTION" missed="0" covered="11"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="currentAllOpenOrders" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="78"><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="allOrders" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="101"><counter type="INSTRUCTION" missed="0" covered="8"/><counter type="LINE" missed="0" covered="2"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="futuresAccountBalance" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="121"><counter type="INSTRUCTION" missed="0" covered="11"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="accountInformation" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="140"><counter type="INSTRUCTION" missed="0" covered="11"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="positionInformation" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="160"><counter type="INSTRUCTION" missed="0" covered="11"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="accountTradeList" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="183"><counter type="INSTRUCTION" missed="0" covered="8"/><counter type="LINE" missed="0" covered="2"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getLeverageBracket" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="203"><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getTradingRulesIndicators" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="224"><counter type="INSTRUCTION" missed="0" covered="11"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="futuresDownloadId" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="245"><counter type="INSTRUCTION" missed="0" covered="11"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="futuresDownloadLink" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="265"><counter type="INSTRUCTION" missed="0" covered="15"/><counter type="LINE" missed="0" covered="2"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="0" covered="149"/><counter type="LINE" missed="0" covered="25"/><counter type="COMPLEXITY" missed="0" covered="13"/><counter type="METHOD" missed="0" covered="13"/><counter type="CLASS" missed="0" covered="1"/></class><sourcefile name="UMMarket.java"><line nr="19" mi="0" ci="7" mb="0" cb="0"/><line nr="20" mi="0" ci="1" mb="0" cb="0"/><line nr="37" mi="0" ci="4" mb="0" cb="0"/><line nr="55" mi="0" ci="4" mb="0" cb="0"/><line nr="74" mi="0" ci="4" mb="0" cb="0"/><line nr="93" mi="0" ci="4" mb="0" cb="0"/><line nr="116" mi="0" ci="4" mb="0" cb="0"/><line nr="117" mi="0" ci="4" mb="0" cb="0"/><line nr="118" mi="0" ci="4" mb="0" cb="0"/><line nr="141" mi="0" ci="4" mb="0" cb="0"/><line nr="142" mi="0" ci="4" mb="0" cb="0"/><line nr="143" mi="0" ci="4" mb="0" cb="0"/><line nr="166" mi="0" ci="4" mb="0" cb="0"/><line nr="167" mi="0" ci="4" mb="0" cb="0"/><line nr="168" mi="0" ci="4" mb="0" cb="0"/><line nr="191" mi="0" ci="4" mb="0" cb="0"/><line nr="192" mi="0" ci="4" mb="0" cb="0"/><line nr="193" mi="0" ci="4" mb="0" cb="0"/><line nr="196" mi="0" ci="3" mb="0" cb="0"/><line nr="217" mi="0" ci="4" mb="0" cb="0"/><line nr="218" mi="0" ci="4" mb="0" cb="0"/><line nr="219" mi="0" ci="11" mb="0" cb="0"/><line nr="222" mi="0" ci="3" mb="0" cb="0"/><line nr="243" mi="0" ci="4" mb="0" cb="0"/><line nr="244" mi="0" ci="4" mb="0" cb="0"/><line nr="245" mi="0" ci="11" mb="0" cb="0"/><line nr="248" mi="0" ci="3" mb="0" cb="0"/><line nr="263" mi="0" ci="11" mb="0" cb="0"/><line nr="266" mi="0" ci="3" mb="0" cb="0"/><line nr="283" mi="0" ci="11" mb="0" cb="0"/><counter type="INSTRUCTION" missed="0" covered="144"/><counter type="LINE" missed="0" covered="30"/><counter type="COMPLEXITY" missed="0" covered="13"/><counter type="METHOD" missed="0" covered="13"/><counter type="CLASS" missed="0" covered="1"/></sourcefile><sourcefile name="UMUserData.java"><line nr="16" mi="0" ci="6" mb="0" cb="0"/><line nr="17" mi="0" ci="1" mb="0" cb="0"/><counter type="INSTRUCTION" missed="0" covered="7"/><counter type="LINE" missed="0" covered="2"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/><counter type="CLASS" missed="0" covered="1"/></sourcefile><sourcefile name="UMAccount.java"><line nr="19" mi="0" ci="7" mb="0" cb="0"/><line nr="20" mi="0" ci="1" mb="0" cb="0"/><line nr="22" mi="0" ci="3" mb="0" cb="0"/><line nr="39" mi="0" ci="4" mb="0" cb="0"/><line nr="40" mi="0" ci="11" mb="0" cb="0"/><line nr="58" mi="0" ci="11" mb="0" cb="0"/><line nr="78" mi="0" ci="4" mb="0" cb="0"/><line nr="101" mi="0" ci="4" mb="0" cb="0"/><line nr="102" mi="0" ci="4" mb="0" cb="0"/><line nr="105" mi="0" ci="3" mb="0" cb="0"/><line nr="121" mi="0" ci="11" mb="0" cb="0"/><line nr="124" mi="0" ci="3" mb="0" cb="0"/><line nr="140" mi="0" ci="11" mb="0" cb="0"/><line nr="143" mi="0" ci="3" mb="0" cb="0"/><line nr="160" mi="0" ci="11" mb="0" cb="0"/><line nr="183" mi="0" ci="4" mb="0" cb="0"/><line nr="184" mi="0" ci="4" mb="0" cb="0"/><line nr="203" mi="0" ci="4" mb="0" cb="0"/><line nr="206" mi="0" ci="3" mb="0" cb="0"/><line nr="224" mi="0" ci="11" mb="0" cb="0"/><line nr="227" mi="0" ci="3" mb="0" cb="0"/><line nr="245" mi="0" ci="11" mb="0" cb="0"/><line nr="248" mi="0" ci="3" mb="0" cb="0"/><line nr="265" mi="0" ci="4" mb="0" cb="0"/><line nr="266" mi="0" ci="11" mb="0" cb="0"/><counter type="INSTRUCTION" missed="0" covered="149"/><counter type="LINE" missed="0" covered="25"/><counter type="COMPLEXITY" missed="0" covered="13"/><counter type="METHOD" missed="0" covered="13"/><counter type="CLASS" missed="0" covered="1"/></sourcefile><counter type="INSTRUCTION" missed="0" covered="300"/><counter type="LINE" missed="0" covered="57"/><counter type="COMPLEXITY" missed="0" covered="27"/><counter type="METHOD" missed="0" covered="27"/><counter type="CLASS" missed="0" covered="3"/></package><package name="com/binance/connector/futures/client/utils"><class name="com/binance/connector/futures/client/utils/SignatureGenerator" sourcefilename="SignatureGenerator.java"><method name="getSignature" desc="(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;" line="16"><counter type="INSTRUCTION" missed="7" covered="22"/><counter type="LINE" missed="2" covered="6"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="7" covered="22"/><counter type="LINE" missed="2" covered="6"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/><counter type="CLASS" missed="0" covered="1"/></class><class name="com/binance/connector/futures/client/utils/JSONParser" sourcefilename="JSONParser.java"><method name="getJSONStringValue" desc="(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;" line="15"><counter type="INSTRUCTION" missed="0" covered="22"/><counter type="LINE" missed="0" covered="4"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getJSONIntValue" desc="(Ljava/lang/String;Ljava/lang/String;)I" line="24"><counter type="INSTRUCTION" missed="0" covered="22"/><counter type="LINE" missed="0" covered="4"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getJSONArray" desc="(Ljava/util/ArrayList;Ljava/lang/String;)Ljava/lang/String;" line="33"><counter type="INSTRUCTION" missed="21" covered="0"/><counter type="LINE" missed="4" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="21" covered="44"/><counter type="LINE" missed="4" covered="8"/><counter type="COMPLEXITY" missed="1" covered="2"/><counter type="METHOD" missed="1" covered="2"/><counter type="CLASS" missed="0" covered="1"/></class><class name="com/binance/connector/futures/client/utils/RequestHandler" sourcefilename="RequestHandler.java"><method name="&lt;init&gt;" desc="(Ljava/lang/String;Lcom/binance/connector/futures/client/utils/ProxyAuth;)V" line="17"><counter type="INSTRUCTION" missed="0" covered="12"/><counter type="LINE" missed="0" covered="5"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="&lt;init&gt;" desc="(Ljava/lang/String;Ljava/lang/String;Lcom/binance/connector/futures/client/utils/ProxyAuth;)V" line="23"><counter type="INSTRUCTION" missed="0" covered="12"/><counter type="LINE" missed="0" covered="5"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="sendApiRequest" desc="(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/util/LinkedHashMap;Lcom/binance/connector/futures/client/enums/HttpMethod;Lcom/binance/connector/futures/client/enums/RequestType;Z)Ljava/lang/String;" line="42"><counter type="INSTRUCTION" missed="12" covered="34"/><counter type="BRANCH" missed="1" covered="2"/><counter type="LINE" missed="1" covered="8"/><counter type="COMPLEXITY" missed="1" covered="2"/><counter type="METHOD" missed="0" covered="1"/></method><method name="sendPublicRequest" desc="(Ljava/lang/String;Ljava/lang/String;Ljava/util/LinkedHashMap;Lcom/binance/connector/futures/client/enums/HttpMethod;Z)Ljava/lang/String;" line="61"><counter type="INSTRUCTION" missed="0" covered="10"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="sendWithApiKeyRequest" desc="(Ljava/lang/String;Ljava/lang/String;Ljava/util/LinkedHashMap;Lcom/binance/connector/futures/client/enums/HttpMethod;Z)Ljava/lang/String;" line="66"><counter type="INSTRUCTION" missed="5" covered="18"/><counter type="BRANCH" missed="2" covered="2"/><counter type="LINE" missed="1" covered="2"/><counter type="COMPLEXITY" missed="2" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="sendSignedRequest" desc="(Ljava/lang/String;Ljava/lang/String;Ljava/util/LinkedHashMap;Lcom/binance/connector/futures/client/enums/HttpMethod;Z)Ljava/lang/String;" line="74"><counter type="INSTRUCTION" missed="5" covered="39"/><counter type="BRANCH" missed="4" covered="4"/><counter type="LINE" missed="1" covered="5"/><counter type="COMPLEXITY" missed="4" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="&lt;clinit&gt;" desc="()V" line="14"><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="22" covered="129"/><counter type="BRANCH" missed="7" covered="8"/><counter type="LINE" missed="3" covered="27"/><counter type="COMPLEXITY" missed="7" covered="8"/><counter type="METHOD" missed="0" covered="7"/><counter type="CLASS" missed="0" covered="1"/></class><class name="com/binance/connector/futures/client/utils/WebSocketConnection" sourcefilename="WebSocketConnection.java"><method name="&lt;init&gt;" desc="(Lcom/binance/connector/futures/client/utils/WebSocketCallback;Lcom/binance/connector/futures/client/utils/WebSocketCallback;Lcom/binance/connector/futures/client/utils/WebSocketCallback;Lcom/binance/connector/futures/client/utils/WebSocketCallback;Lokhttp3/Request;)V" line="36"><counter type="INSTRUCTION" missed="44" covered="0"/><counter type="LINE" missed="11" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="connect" desc="()V" line="49"><counter type="INSTRUCTION" missed="37" covered="0"/><counter type="BRANCH" missed="2" covered="0"/><counter type="LINE" missed="7" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getConnectionId" desc="()I" line="60"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="close" desc="()V" line="65"><counter type="INSTRUCTION" missed="19" covered="0"/><counter type="BRANCH" missed="2" covered="0"/><counter type="LINE" missed="4" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="onOpen" desc="(Lokhttp3/WebSocket;Lokhttp3/Response;)V" line="73"><counter type="INSTRUCTION" missed="11" covered="0"/><counter type="LINE" missed="3" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="onClosing" desc="(Lokhttp3/WebSocket;ILjava/lang/String;)V" line="79"><counter type="INSTRUCTION" missed="10" covered="0"/><counter type="LINE" missed="3" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="onMessage" desc="(Lokhttp3/WebSocket;Ljava/lang/String;)V" line="85"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="onFailure" desc="(Lokhttp3/WebSocket;Ljava/lang/Throwable;Lokhttp3/Response;)V" line="90"><counter type="INSTRUCTION" missed="12" covered="0"/><counter type="LINE" missed="3" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="&lt;clinit&gt;" desc="()V" line="13"><counter type="INSTRUCTION" missed="11" covered="0"/><counter type="LINE" missed="3" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="152" covered="0"/><counter type="BRANCH" missed="4" covered="0"/><counter type="LINE" missed="37" covered="0"/><counter type="COMPLEXITY" missed="11" covered="0"/><counter type="METHOD" missed="9" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><class name="com/binance/connector/futures/client/utils/ResponseHandler" sourcefilename="ResponseHandler.java"><method name="handleResponse" desc="(Lokhttp3/Request;ZLcom/binance/connector/futures/client/utils/ProxyAuth;)Ljava/lang/String;" line="26"><counter type="INSTRUCTION" missed="50" covered="45"/><counter type="BRANCH" missed="10" covered="8"/><counter type="LINE" missed="10" covered="10"/><counter type="COMPLEXITY" missed="6" covered="4"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getlimitUsage" desc="(Lokhttp3/Response;Ljava/lang/String;)Ljava/lang/String;" line="59"><counter type="INSTRUCTION" missed="26" covered="0"/><counter type="LINE" missed="5" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="handleErrorResponse" desc="(Ljava/lang/String;I)Lcom/binance/connector/futures/client/exceptions/BinanceClientException;" line="69"><counter type="INSTRUCTION" missed="0" covered="23"/><counter type="LINE" missed="0" covered="5"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getResponseBodyAsString" desc="(Lokhttp3/ResponseBody;)Ljava/lang/String;" line="78"><counter type="INSTRUCTION" missed="2" covered="6"/><counter type="BRANCH" missed="1" covered="1"/><counter type="LINE" missed="1" covered="2"/><counter type="COMPLEXITY" missed="1" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="78" covered="74"/><counter type="BRANCH" missed="11" covered="9"/><counter type="LINE" missed="16" covered="17"/><counter type="COMPLEXITY" missed="8" covered="6"/><counter type="METHOD" missed="1" covered="3"/><counter type="CLASS" missed="0" covered="1"/></class><class name="com/binance/connector/futures/client/utils/UrlBuilder" sourcefilename="UrlBuilder.java"><method name="buildFullUrl" desc="(Ljava/lang/String;Ljava/lang/String;Ljava/util/LinkedHashMap;Ljava/lang/String;)Ljava/lang/String;" line="23"><counter type="INSTRUCTION" missed="0" covered="41"/><counter type="BRANCH" missed="0" covered="6"/><counter type="LINE" missed="0" covered="8"/><counter type="COMPLEXITY" missed="0" covered="4"/><counter type="METHOD" missed="0" covered="1"/></method><method name="buildStreamUrl" desc="(Ljava/lang/String;Ljava/util/ArrayList;)Ljava/lang/String;" line="37"><counter type="INSTRUCTION" missed="0" covered="13"/><counter type="LINE" missed="0" covered="3"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="joinQueryParameters" desc="(Ljava/util/LinkedHashMap;)Ljava/lang/String;" line="44"><counter type="INSTRUCTION" missed="0" covered="7"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="joinQueryParameters" desc="(Ljava/lang/StringBuilder;Ljava/util/LinkedHashMap;)Ljava/lang/StringBuilder;" line="48"><counter type="INSTRUCTION" missed="20" covered="64"/><counter type="BRANCH" missed="4" covered="10"/><counter type="LINE" missed="6" covered="15"/><counter type="COMPLEXITY" missed="3" covered="5"/><counter type="METHOD" missed="0" covered="1"/></method><method name="joinArrayListParameters" desc="(Ljava/lang/String;Ljava/lang/StringBuilder;Ljava/util/ArrayList;Z)V" line="81"><counter type="INSTRUCTION" missed="30" covered="0"/><counter type="BRANCH" missed="4" covered="0"/><counter type="LINE" missed="9" covered="0"/><counter type="COMPLEXITY" missed="3" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="joinStreamUrls" desc="(Ljava/lang/StringBuilder;Ljava/util/ArrayList;)Ljava/lang/String;" line="95"><counter type="INSTRUCTION" missed="0" covered="29"/><counter type="BRANCH" missed="0" covered="4"/><counter type="LINE" missed="0" covered="8"/><counter type="COMPLEXITY" missed="0" covered="3"/><counter type="METHOD" missed="0" covered="1"/></method><method name="urlEncode" desc="(Ljava/lang/String;)Ljava/lang/String;" line="110"><counter type="INSTRUCTION" missed="15" covered="5"/><counter type="LINE" missed="2" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getFormatter" desc="()Ljava/text/DecimalFormat;" line="119"><counter type="INSTRUCTION" missed="0" covered="22"/><counter type="BRANCH" missed="0" covered="2"/><counter type="LINE" missed="0" covered="6"/><counter type="COMPLEXITY" missed="0" covered="2"/><counter type="METHOD" missed="0" covered="1"/></method><method name="buildTimestamp" desc="()Ljava/lang/String;" line="130"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="65" covered="184"/><counter type="BRANCH" missed="8" covered="22"/><counter type="LINE" missed="17" covered="43"/><counter type="COMPLEXITY" missed="6" covered="18"/><counter type="METHOD" missed="1" covered="8"/><counter type="CLASS" missed="0" covered="1"/></class><class name="com/binance/connector/futures/client/utils/ParameterChecker" sourcefilename="ParameterChecker.java"><method name="checkParameter" desc="(Ljava/util/LinkedHashMap;Ljava/lang/String;Ljava/lang/Class;)V" line="12"><counter type="INSTRUCTION" missed="0" covered="10"/><counter type="LINE" missed="0" covered="3"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="checkOrParameters" desc="(Ljava/util/LinkedHashMap;Ljava/lang/String;Ljava/lang/String;)V" line="17"><counter type="INSTRUCTION" missed="0" covered="25"/><counter type="BRANCH" missed="0" covered="4"/><counter type="LINE" missed="0" covered="3"/><counter type="COMPLEXITY" missed="0" covered="3"/><counter type="METHOD" missed="0" covered="1"/></method><method name="checkRequiredParameter" desc="(Ljava/util/LinkedHashMap;Ljava/lang/String;)V" line="23"><counter type="INSTRUCTION" missed="0" covered="17"/><counter type="BRANCH" missed="0" covered="2"/><counter type="LINE" missed="0" covered="3"/><counter type="COMPLEXITY" missed="0" covered="2"/><counter type="METHOD" missed="0" covered="1"/></method><method name="checkParameterType" desc="(Ljava/lang/Object;Ljava/lang/Class;Ljava/lang/String;)V" line="29"><counter type="INSTRUCTION" missed="0" covered="42"/><counter type="BRANCH" missed="0" covered="6"/><counter type="LINE" missed="0" covered="5"/><counter type="COMPLEXITY" missed="0" covered="4"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="0" covered="94"/><counter type="BRANCH" missed="0" covered="12"/><counter type="LINE" missed="0" covered="14"/><counter type="COMPLEXITY" missed="0" covered="10"/><counter type="METHOD" missed="0" covered="4"/><counter type="CLASS" missed="0" covered="1"/></class><class name="com/binance/connector/futures/client/utils/HttpClientSingleton" sourcefilename="HttpClientSingleton.java"><method name="getHttpClient" desc="()Lokhttp3/OkHttpClient;" line="13"><counter type="INSTRUCTION" missed="6" covered="0"/><counter type="BRANCH" missed="2" covered="0"/><counter type="LINE" missed="3" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getHttpClient" desc="(Lcom/binance/connector/futures/client/utils/ProxyAuth;)Lokhttp3/OkHttpClient;" line="20"><counter type="INSTRUCTION" missed="0" covered="9"/><counter type="BRANCH" missed="0" covered="2"/><counter type="LINE" missed="0" covered="4"/><counter type="COMPLEXITY" missed="0" covered="2"/><counter type="METHOD" missed="0" covered="1"/></method><method name="createHttpClient" desc="(Lcom/binance/connector/futures/client/utils/ProxyAuth;)V" line="29"><counter type="INSTRUCTION" missed="23" covered="8"/><counter type="BRANCH" missed="3" covered="1"/><counter type="LINE" missed="3" covered="3"/><counter type="COMPLEXITY" missed="2" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="verifyHttpClient" desc="(Lcom/binance/connector/futures/client/utils/ProxyAuth;)V" line="41"><counter type="INSTRUCTION" missed="7" covered="10"/><counter type="BRANCH" missed="5" covered="3"/><counter type="LINE" missed="1" covered="3"/><counter type="COMPLEXITY" missed="4" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="&lt;clinit&gt;" desc="()V" line="7"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="36" covered="30"/><counter type="BRANCH" missed="10" covered="6"/><counter type="LINE" missed="7" covered="11"/><counter type="COMPLEXITY" missed="8" covered="5"/><counter type="METHOD" missed="1" covered="4"/><counter type="CLASS" missed="0" covered="1"/></class><class name="com/binance/connector/futures/client/utils/WebSocketCallback" sourcefilename="WebSocketCallback.java"/><class name="com/binance/connector/futures/client/utils/ProxyAuth" sourcefilename="ProxyAuth.java"><method name="&lt;init&gt;" desc="(Ljava/net/Proxy;Lokhttp3/Authenticator;)V" line="10"><counter type="INSTRUCTION" missed="9" covered="0"/><counter type="LINE" missed="4" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getProxy" desc="()Ljava/net/Proxy;" line="16"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getAuth" desc="()Lokhttp3/Authenticator;" line="20"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="15" covered="0"/><counter type="LINE" missed="6" covered="0"/><counter type="COMPLEXITY" missed="3" covered="0"/><counter type="METHOD" missed="3" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><class name="com/binance/connector/futures/client/utils/RequestBuilder" sourcefilename="RequestBuilder.java"><method name="buildPublicRequest" desc="(Ljava/lang/String;Lcom/binance/connector/futures/client/enums/HttpMethod;)Lokhttp3/Request;" line="18"><counter type="INSTRUCTION" missed="0" covered="65"/><counter type="BRANCH" missed="0" covered="5"/><counter type="LINE" missed="0" covered="9"/><counter type="COMPLEXITY" missed="0" covered="5"/><counter type="METHOD" missed="0" covered="1"/></method><method name="buildApiKeyRequest" desc="(Ljava/lang/String;Lcom/binance/connector/futures/client/enums/HttpMethod;Ljava/lang/String;)Lokhttp3/Request;" line="38"><counter type="INSTRUCTION" missed="0" covered="68"/><counter type="BRANCH" missed="0" covered="5"/><counter type="LINE" missed="0" covered="9"/><counter type="COMPLEXITY" missed="0" covered="5"/><counter type="METHOD" missed="0" covered="1"/></method><method name="buildWebsocketRequest" desc="(Ljava/lang/String;)Lokhttp3/Request;" line="57"><counter type="INSTRUCTION" missed="0" covered="7"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="&lt;clinit&gt;" desc="()V" line="10"><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="0" covered="144"/><counter type="BRANCH" missed="0" covered="10"/><counter type="LINE" missed="0" covered="20"/><counter type="COMPLEXITY" missed="0" covered="12"/><counter type="METHOD" missed="0" covered="4"/><counter type="CLASS" missed="0" covered="1"/></class><sourcefile name="UrlBuilder.java"><line nr="23" mi="0" ci="5" mb="0" cb="4"/><line nr="24" mi="0" ci="5" mb="0" cb="0"/><line nr="25" mi="0" ci="6" mb="0" cb="0"/><line nr="26" mi="0" ci="4" mb="0" cb="0"/><line nr="27" mi="0" ci="3" mb="0" cb="2"/><line nr="28" mi="0" ci="6" mb="0" cb="0"/><line nr="30" mi="0" ci="3" mb="0" cb="0"/><line nr="32" mi="0" ci="9" mb="0" cb="0"/><line nr="37" mi="0" ci="5" mb="0" cb="0"/><line nr="38" mi="0" ci="4" mb="0" cb="0"/><line nr="39" mi="0" ci="4" mb="0" cb="0"/><line nr="44" mi="0" ci="7" mb="0" cb="0"/><line nr="48" mi="0" ci="5" mb="1" cb="3"/><line nr="49" mi="0" ci="2" mb="0" cb="0"/><line nr="52" mi="0" ci="2" mb="0" cb="0"/><line nr="53" mi="0" ci="11" mb="0" cb="2"/><line nr="55" mi="0" ci="4" mb="0" cb="2"/><line nr="56" mi="0" ci="11" mb="0" cb="0"/><line nr="57" mi="0" ci="4" mb="1" cb="1"/><line nr="58" mi="5" ci="0" mb="2" cb="0"/><line nr="59" mi="1" ci="0" mb="0" cb="0"/><line nr="61" mi="4" ci="0" mb="0" cb="0"/><line nr="62" mi="7" ci="0" mb="0" cb="0"/><line nr="63" mi="2" ci="0" mb="0" cb="0"/><line nr="64" mi="1" ci="0" mb="0" cb="0"/><line nr="67" mi="0" ci="2" mb="0" cb="2"/><line nr="68" mi="0" ci="3" mb="0" cb="0"/><line nr="70" mi="0" ci="4" mb="0" cb="0"/><line nr="73" mi="0" ci="6" mb="0" cb="0"/><line nr="74" mi="0" ci="2" mb="0" cb="0"/><line nr="75" mi="0" ci="5" mb="0" cb="0"/><line nr="76" mi="0" ci="1" mb="0" cb="0"/><line nr="77" mi="0" ci="2" mb="0" cb="0"/><line nr="81" mi="9" ci="0" mb="2" cb="0"/><line nr="82" mi="2" ci="0" mb="2" cb="0"/><line nr="83" mi="3" ci="0" mb="0" cb="0"/><line nr="85" mi="4" ci="0" mb="0" cb="0"/><line nr="88" mi="4" ci="0" mb="0" cb="0"/><line nr="89" mi="2" ci="0" mb="0" cb="0"/><line nr="90" mi="4" ci="0" mb="0" cb="0"/><line nr="91" mi="1" ci="0" mb="0" cb="0"/><line nr="92" mi="1" ci="0" mb="0" cb="0"/><line nr="95" mi="0" ci="2" mb="0" cb="0"/><line nr="96" mi="0" ci="10" mb="0" cb="2"/><line nr="97" mi="0" ci="2" mb="0" cb="2"/><line nr="98" mi="0" ci="3" mb="0" cb="0"/><line nr="100" mi="0" ci="4" mb="0" cb="0"/><line nr="102" mi="0" ci="4" mb="0" cb="0"/><line nr="103" mi="0" ci="1" mb="0" cb="0"/><line nr="104" mi="0" ci="3" mb="0" cb="0"/><line nr="110" mi="0" ci="5" mb="0" cb="0"/><line nr="111" mi="1" ci="0" mb="0" cb="0"/><line nr="114" mi="14" ci="0" mb="0" cb="0"/><line nr="119" mi="0" ci="3" mb="0" cb="2"/><line nr="121" mi="0" ci="5" mb="0" cb="0"/><line nr="122" mi="0" ci="6" mb="0" cb="0"/><line nr="123" mi="0" ci="3" mb="0" cb="0"/><line nr="124" mi="0" ci="3" mb="0" cb="0"/><line nr="126" mi="0" ci="2" mb="0" cb="0"/><line nr="130" mi="0" ci="3" mb="0" cb="0"/><counter type="INSTRUCTION" missed="65" covered="184"/><counter type="BRANCH" missed="8" covered="22"/><counter type="LINE" missed="17" covered="43"/><counter type="COMPLEXITY" missed="6" covered="18"/><counter type="METHOD" missed="1" covered="8"/><counter type="CLASS" missed="0" covered="1"/></sourcefile><sourcefile name="ParameterChecker.java"><line nr="12" mi="0" ci="3" mb="0" cb="0"/><line nr="13" mi="0" ci="6" mb="0" cb="0"/><line nr="14" mi="0" ci="1" mb="0" cb="0"/><line nr="17" mi="0" ci="8" mb="0" cb="4"/><line nr="18" mi="0" ci="16" mb="0" cb="0"/><line nr="20" mi="0" ci="1" mb="0" cb="0"/><line nr="23" mi="0" ci="4" mb="0" cb="2"/><line nr="24" mi="0" ci="12" mb="0" cb="0"/><line nr="26" mi="0" ci="1" mb="0" cb="0"/><line nr="29" mi="0" ci="4" mb="0" cb="2"/><line nr="30" mi="0" ci="16" mb="0" cb="0"/><line nr="31" mi="0" ci="9" mb="0" cb="4"/><line nr="32" mi="0" ci="12" mb="0" cb="0"/><line nr="34" mi="0" ci="1" mb="0" cb="0"/><counter type="INSTRUCTION" missed="0" covered="94"/><counter type="BRANCH" missed="0" covered="12"/><counter type="LINE" missed="0" covered="14"/><counter type="COMPLEXITY" missed="0" covered="10"/><counter type="METHOD" missed="0" covered="4"/><counter type="CLASS" missed="0" covered="1"/></sourcefile><sourcefile name="SignatureGenerator.java"><line nr="16" mi="0" ci="7" mb="0" cb="0"/><line nr="17" mi="0" ci="3" mb="0" cb="0"/><line nr="18" mi="0" ci="3" mb="0" cb="0"/><line nr="19" mi="0" ci="5" mb="0" cb="0"/><line nr="20" mi="1" ci="0" mb="0" cb="0"/><line nr="21" mi="6" ci="0" mb="0" cb="0"/><line nr="22" mi="0" ci="1" mb="0" cb="0"/><line nr="23" mi="0" ci="3" mb="0" cb="0"/><counter type="INSTRUCTION" missed="7" covered="22"/><counter type="LINE" missed="2" covered="6"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/><counter type="CLASS" missed="0" covered="1"/></sourcefile><sourcefile name="ProxyAuth.java"><line nr="10" mi="2" ci="0" mb="0" cb="0"/><line nr="11" mi="3" ci="0" mb="0" cb="0"/><line nr="12" mi="3" ci="0" mb="0" cb="0"/><line nr="13" mi="1" ci="0" mb="0" cb="0"/><line nr="16" mi="3" ci="0" mb="0" cb="0"/><line nr="20" mi="3" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="15" covered="0"/><counter type="LINE" missed="6" covered="0"/><counter type="COMPLEXITY" missed="3" covered="0"/><counter type="METHOD" missed="3" covered="0"/><counter type="CLASS" missed="1" covered="0"/></sourcefile><sourcefile name="HttpClientSingleton.java"><line nr="7" mi="0" ci="3" mb="0" cb="0"/><line nr="13" mi="2" ci="0" mb="2" cb="0"/><line nr="14" mi="2" ci="0" mb="0" cb="0"/><line nr="16" mi="2" ci="0" mb="0" cb="0"/><line nr="20" mi="0" ci="2" mb="0" cb="2"/><line nr="21" mi="0" ci="3" mb="0" cb="0"/><line nr="23" mi="0" ci="2" mb="0" cb="0"/><line nr="25" mi="0" ci="2" mb="0" cb="0"/><line nr="29" mi="0" ci="2" mb="1" cb="1"/><line nr="30" mi="0" ci="5" mb="0" cb="0"/><line nr="32" mi="3" ci="0" mb="2" cb="0"/><line nr="33" mi="9" ci="0" mb="0" cb="0"/><line nr="35" mi="11" ci="0" mb="0" cb="0"/><line nr="38" mi="0" ci="1" mb="0" cb="0"/><line nr="41" mi="0" ci="3" mb="0" cb="0"/><line nr="43" mi="5" ci="6" mb="5" cb="3"/><line nr="44" mi="2" ci="0" mb="0" cb="0"/><line nr="46" mi="0" ci="1" mb="0" cb="0"/><counter type="INSTRUCTION" missed="36" covered="30"/><counter type="BRANCH" missed="10" covered="6"/><counter type="LINE" missed="7" covered="11"/><counter type="COMPLEXITY" missed="8" covered="5"/><counter type="METHOD" missed="1" covered="4"/><counter type="CLASS" missed="0" covered="1"/></sourcefile><sourcefile name="RequestHandler.java"><line nr="14" mi="0" ci="4" mb="0" cb="0"/><line nr="17" mi="0" ci="2" mb="0" cb="0"/><line nr="18" mi="0" ci="3" mb="0" cb="0"/><line nr="19" mi="0" ci="3" mb="0" cb="0"/><line nr="20" mi="0" ci="3" mb="0" cb="0"/><line nr="21" mi="0" ci="1" mb="0" cb="0"/><line nr="23" mi="0" ci="2" mb="0" cb="0"/><line nr="24" mi="0" ci="3" mb="0" cb="0"/><line nr="25" mi="0" ci="3" mb="0" cb="0"/><line nr="26" mi="0" ci="3" mb="0" cb="0"/><line nr="27" mi="0" ci="1" mb="0" cb="0"/><line nr="42" mi="0" ci="6" mb="0" cb="0"/><line nr="43" mi="0" ci="5" mb="0" cb="0"/><line nr="45" mi="0" ci="5" mb="1" cb="2"/><line nr="47" mi="0" ci="4" mb="0" cb="0"/><line nr="48" mi="0" ci="1" mb="0" cb="0"/><line nr="51" mi="0" ci="6" mb="0" cb="0"/><line nr="52" mi="0" ci="1" mb="0" cb="0"/><line nr="54" mi="12" ci="0" mb="0" cb="0"/><line nr="56" mi="0" ci="6" mb="0" cb="0"/><line nr="61" mi="0" ci="10" mb="0" cb="0"/><line nr="66" mi="0" ci="8" mb="2" cb="2"/><line nr="67" mi="5" ci="0" mb="0" cb="0"/><line nr="69" mi="0" ci="10" mb="0" cb="0"/><line nr="74" mi="0" ci="16" mb="4" cb="4"/><line nr="75" mi="5" ci="0" mb="0" cb="0"/><line nr="77" mi="0" ci="5" mb="0" cb="0"/><line nr="78" mi="0" ci="3" mb="0" cb="0"/><line nr="79" mi="0" ci="5" mb="0" cb="0"/><line nr="80" mi="0" ci="10" mb="0" cb="0"/><counter type="INSTRUCTION" missed="22" covered="129"/><counter type="BRANCH" missed="7" covered="8"/><counter type="LINE" missed="3" covered="27"/><counter type="COMPLEXITY" missed="7" covered="8"/><counter type="METHOD" missed="0" covered="7"/><counter type="CLASS" missed="0" covered="1"/></sourcefile><sourcefile name="ResponseHandler.java"><line nr="26" mi="0" ci="3" mb="0" cb="0"/><line nr="27" mi="0" ci="5" mb="0" cb="0"/><line nr="28" mi="0" ci="3" mb="1" cb="1"/><line nr="29" mi="5" ci="0" mb="0" cb="0"/><line nr="32" mi="0" ci="4" mb="0" cb="0"/><line nr="34" mi="0" ci="8" mb="0" cb="4"/><line nr="35" mi="0" ci="5" mb="0" cb="0"/><line nr="36" mi="0" ci="4" mb="0" cb="2"/><line nr="37" mi="0" ci="7" mb="0" cb="0"/><line nr="40" mi="0" ci="2" mb="1" cb="1"/><line nr="41" mi="6" ci="0" mb="0" cb="0"/><line nr="43" mi="0" ci="4" mb="0" cb="0"/><line nr="45" mi="5" ci="0" mb="2" cb="0"/><line nr="46" mi="2" ci="0" mb="0" cb="0"/><line nr="47" mi="2" ci="0" mb="2" cb="0"/><line nr="48" mi="5" ci="0" mb="2" cb="0"/><line nr="49" mi="3" ci="0" mb="0" cb="0"/><line nr="50" mi="5" ci="0" mb="2" cb="0"/><line nr="51" mi="2" ci="0" mb="0" cb="0"/><line nr="54" mi="15" ci="0" mb="0" cb="0"/><line nr="59" mi="4" ci="0" mb="0" cb="0"/><line nr="60" mi="7" ci="0" mb="0" cb="0"/><line nr="61" mi="7" ci="0" mb="0" cb="0"/><line nr="62" mi="5" ci="0" mb="0" cb="0"/><line nr="64" mi="3" ci="0" mb="0" cb="0"/><line nr="69" mi="0" ci="4" mb="0" cb="0"/><line nr="70" mi="0" ci="4" mb="0" cb="0"/><line nr="71" mi="0" ci="8" mb="0" cb="0"/><line nr="72" mi="0" ci="1" mb="0" cb="0"/><line nr="73" mi="0" ci="6" mb="0" cb="0"/><line nr="78" mi="0" ci="3" mb="1" cb="1"/><line nr="79" mi="0" ci="3" mb="0" cb="0"/><line nr="81" mi="2" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="78" covered="74"/><counter type="BRANCH" missed="11" covered="9"/><counter type="LINE" missed="16" covered="17"/><counter type="COMPLEXITY" missed="8" covered="6"/><counter type="METHOD" missed="1" covered="3"/><counter type="CLASS" missed="0" covered="1"/></sourcefile><sourcefile name="WebSocketConnection.java"><line nr="13" mi="5" ci="0" mb="0" cb="0"/><line nr="15" mi="2" ci="0" mb="0" cb="0"/><line nr="16" mi="4" ci="0" mb="0" cb="0"/><line nr="36" mi="2" ci="0" mb="0" cb="0"/><line nr="37" mi="3" ci="0" mb="0" cb="0"/><line nr="38" mi="3" ci="0" mb="0" cb="0"/><line nr="39" mi="3" ci="0" mb="0" cb="0"/><line nr="40" mi="3" ci="0" mb="0" cb="0"/><line nr="41" mi="4" ci="0" mb="0" cb="0"/><line nr="42" mi="3" ci="0" mb="0" cb="0"/><line nr="43" mi="14" ci="0" mb="0" cb="0"/><line nr="44" mi="3" ci="0" mb="0" cb="0"/><line nr="45" mi="5" ci="0" mb="0" cb="0"/><line nr="46" mi="1" ci="0" mb="0" cb="0"/><line nr="49" mi="5" ci="0" mb="0" cb="0"/><line nr="50" mi="4" ci="0" mb="2" cb="0"/><line nr="51" mi="8" ci="0" mb="0" cb="0"/><line nr="52" mi="8" ci="0" mb="0" cb="0"/><line nr="54" mi="8" ci="0" mb="0" cb="0"/><line nr="56" mi="3" ci="0" mb="0" cb="0"/><line nr="57" mi="1" ci="0" mb="0" cb="0"/><line nr="60" mi="3" ci="0" mb="0" cb="0"/><line nr="65" mi="4" ci="0" mb="2" cb="0"/><line nr="66" mi="8" ci="0" mb="0" cb="0"/><line nr="67" mi="6" ci="0" mb="0" cb="0"/><line nr="69" mi="1" ci="0" mb="0" cb="0"/><line nr="73" mi="6" ci="0" mb="0" cb="0"/><line nr="74" mi="4" ci="0" mb="0" cb="0"/><line nr="75" mi="1" ci="0" mb="0" cb="0"/><line nr="79" mi="5" ci="0" mb="0" cb="0"/><line nr="80" mi="4" ci="0" mb="0" cb="0"/><line nr="81" mi="1" ci="0" mb="0" cb="0"/><line nr="85" mi="4" ci="0" mb="0" cb="0"/><line nr="86" mi="1" ci="0" mb="0" cb="0"/><line nr="90" mi="7" ci="0" mb="0" cb="0"/><line nr="91" mi="4" ci="0" mb="0" cb="0"/><line nr="92" mi="1" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="152" covered="0"/><counter type="BRANCH" missed="4" covered="0"/><counter type="LINE" missed="37" covered="0"/><counter type="COMPLEXITY" missed="11" covered="0"/><counter type="METHOD" missed="9" covered="0"/><counter type="CLASS" missed="1" covered="0"/></sourcefile><sourcefile name="WebSocketCallback.java"/><sourcefile name="RequestBuilder.java"><line nr="10" mi="0" ci="4" mb="0" cb="0"/><line nr="18" mi="0" ci="12" mb="0" cb="0"/><line nr="19" mi="0" ci="5" mb="0" cb="5"/><line nr="21" mi="0" ci="7" mb="0" cb="0"/><line nr="23" mi="0" ci="4" mb="0" cb="0"/><line nr="25" mi="0" ci="7" mb="0" cb="0"/><line nr="27" mi="0" ci="4" mb="0" cb="0"/><line nr="29" mi="0" ci="12" mb="0" cb="0"/><line nr="31" mi="0" ci="1" mb="0" cb="0"/><line nr="32" mi="0" ci="13" mb="0" cb="0"/><line nr="38" mi="0" ci="15" mb="0" cb="0"/><line nr="39" mi="0" ci="5" mb="0" cb="5"/><line nr="41" mi="0" ci="7" mb="0" cb="0"/><line nr="43" mi="0" ci="4" mb="0" cb="0"/><line nr="45" mi="0" ci="7" mb="0" cb="0"/><line nr="47" mi="0" ci="4" mb="0" cb="0"/><line nr="49" mi="0" ci="12" mb="0" cb="0"/><line nr="51" mi="0" ci="1" mb="0" cb="0"/><line nr="52" mi="0" ci="13" mb="0" cb="0"/><line nr="57" mi="0" ci="7" mb="0" cb="0"/><counter type="INSTRUCTION" missed="0" covered="144"/><counter type="BRANCH" missed="0" covered="10"/><counter type="LINE" missed="0" covered="20"/><counter type="COMPLEXITY" missed="0" covered="12"/><counter type="METHOD" missed="0" covered="4"/><counter type="CLASS" missed="0" covered="1"/></sourcefile><sourcefile name="JSONParser.java"><line nr="15" mi="0" ci="5" mb="0" cb="0"/><line nr="16" mi="0" ci="4" mb="0" cb="0"/><line nr="17" mi="0" ci="1" mb="0" cb="0"/><line nr="18" mi="0" ci="12" mb="0" cb="0"/><line nr="24" mi="0" ci="5" mb="0" cb="0"/><line nr="25" mi="0" ci="4" mb="0" cb="0"/><line nr="26" mi="0" ci="1" mb="0" cb="0"/><line nr="27" mi="0" ci="12" mb="0" cb="0"/><line nr="33" mi="5" ci="0" mb="0" cb="0"/><line nr="34" mi="3" ci="0" mb="0" cb="0"/><line nr="35" mi="1" ci="0" mb="0" cb="0"/><line nr="36" mi="12" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="21" covered="44"/><counter type="LINE" missed="4" covered="8"/><counter type="COMPLEXITY" missed="1" covered="2"/><counter type="METHOD" missed="1" covered="2"/><counter type="CLASS" missed="0" covered="1"/></sourcefile><counter type="INSTRUCTION" missed="396" covered="721"/><counter type="BRANCH" missed="40" covered="67"/><counter type="LINE" missed="92" covered="146"/><counter type="COMPLEXITY" missed="44" covered="62"/><counter type="METHOD" missed="16" covered="33"/><counter type="CLASS" missed="2" covered="8"/></package><counter type="INSTRUCTION" missed="1825" covered="2429"/><counter type="BRANCH" missed="60" covered="67"/><counter type="LINE" missed="321" covered="472"/><counter type="COMPLEXITY" missed="148" covered="201"/><counter type="METHOD" missed="110" covered="172"/><counter type="CLASS" missed="6" covered="28"/></report>