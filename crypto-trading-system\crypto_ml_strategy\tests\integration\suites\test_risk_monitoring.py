#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
实时风险监控测试

专门测试实时风险监控系统的核心功能，包括实时数据处理、
风险指标计算、预警机制和监控性能等关键功能。

Author: Crypto ML Strategy Team
Date: 2024-12-19
"""

import sys
import os
import unittest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import logging
import time
import threading
import asyncio
from unittest.mock import Mock, patch, MagicMock

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..', 'src'))

# 导入风险管理模块
from risk_management import (
    RiskConfig, RiskEvent, RiskLevel, PositionInfo,
    RiskControlEngine, RiskAssessor,
    PositionSizeMethod, StopLossType, TakeProfitType
)

# 导入测试工具
from ..utils.risk_management_test_helpers import RiskPerformanceMonitor, MockDataFactory
from ..config.integration_test_config import IntegrationTestConfig
from ..data.test_data_generator import TestDataGenerator

logger = logging.getLogger(__name__)


class RiskMonitoringTest(unittest.TestCase):
    """实时风险监控测试"""
    
    @classmethod
    def setUpClass(cls):
        """测试类初始化"""
        cls.config = IntegrationTestConfig()
        cls.data_generator = TestDataGenerator()
        cls.mock_factory = MockDataFactory()
        
        # 设置日志
        logging.basicConfig(level=logging.INFO)
        
        logger.info("Risk Monitoring Test initialized")
    
    def setUp(self):
        """每个测试用例的初始化"""
        self.test_start_time = datetime.now()
        
        # 创建实时监控配置
        self.monitoring_config = RiskConfig(
            max_single_position_ratio=0.1,
            max_total_position_ratio=0.8,
            position_size_method=PositionSizeMethod.ADAPTIVE,
            stop_loss_type=StopLossType.ADAPTIVE,
            take_profit_type=TakeProfitType.RISK_REWARD_RATIO,
            max_drawdown_threshold=0.15,
            risk_check_interval=0.1  # 100ms检查间隔
        )
        
        # 创建风险控制引擎
        self.risk_engine = RiskControlEngine(self.monitoring_config)
        
        # 创建性能监控器
        self.performance_monitor = RiskPerformanceMonitor()
        
        logger.info(f"Test case setup: {self._testMethodName}")
    
    def tearDown(self):
        """每个测试用例的清理"""
        if hasattr(self, 'risk_engine') and self.risk_engine.is_running:
            self.risk_engine.stop()
        
        test_duration = (datetime.now() - self.test_start_time).total_seconds()
        logger.info(f"Test case completed: {self._testMethodName}, Duration: {test_duration:.2f}s")
    
    def test_real_time_data_processing(self):
        """测试实时数据处理"""
        logger.info("Testing real-time data processing...")
        
        try:
            # 启动风险引擎
            self.risk_engine.start()
            self.performance_monitor.start_monitoring(self.risk_engine)
            
            # 模拟高频数据流
            symbols = ['BTCUSDT', 'ETHUSDT', 'ADAUSDT']
            update_count = 0
            test_duration = 5  # 5秒测试
            end_time = time.time() + test_duration
            
            while time.time() < end_time:
                for symbol in symbols:
                    # 生成模拟市场数据
                    market_data = self.mock_factory.create_mock_market_data(symbol)
                    
                    # 记录处理时间
                    start_time = time.time()
                    self.risk_engine.update_market_data(symbol, market_data)
                    processing_time = time.time() - start_time
                    
                    self.performance_monitor.record_response_time(processing_time)
                    update_count += 1
                
                time.sleep(0.05)  # 50ms间隔
            
            # 停止监控
            performance_metrics = self.performance_monitor.stop_monitoring()
            
            # 验证处理性能
            self.assertGreater(update_count, 0, "Should process market data updates")
            self.assertLess(performance_metrics['avg_response_time'], 0.01, "Avg processing time should be < 10ms")
            self.assertLess(performance_metrics['max_response_time'], 0.05, "Max processing time should be < 50ms")
            
            # 停止引擎
            self.risk_engine.stop()
            
            logger.info("✅ Real-time data processing test passed")
            logger.info(f"   Processed {update_count} updates in {test_duration}s")
            logger.info(f"   Avg processing time: {performance_metrics['avg_response_time']*1000:.2f}ms")
            
        except Exception as e:
            logger.error(f"❌ Real-time data processing test failed: {e}")
            raise
    
    def test_risk_indicator_calculation(self):
        """测试风险指标计算"""
        logger.info("Testing risk indicator calculation...")
        
        try:
            # 启动风险引擎
            self.risk_engine.start()
            
            # 创建测试投资组合
            portfolio_data = self.mock_factory.create_mock_portfolio_data(100000)
            self.risk_engine.update_portfolio_data(portfolio_data)
            
            # 更新市场数据
            for symbol in ['BTCUSDT', 'ETHUSDT']:
                market_data = self.mock_factory.create_mock_market_data(symbol, volatility=0.3)
                self.risk_engine.update_market_data(symbol, market_data)
            
            # 等待风险指标计算
            time.sleep(1)
            
            # 获取风险评估器
            risk_assessor = self.risk_engine.risk_assessor
            
            # 执行风险评估
            risk_metrics = risk_assessor.assess_portfolio_risk(portfolio_data)
            
            # 验证风险指标
            self.assertIsNotNone(risk_metrics, "Risk metrics should be calculated")
            self.assertGreater(risk_metrics.portfolio_value, 0, "Portfolio value should be positive")
            self.assertIsInstance(risk_metrics.risk_level, RiskLevel, "Risk level should be valid")
            self.assertGreaterEqual(risk_metrics.var_95, 0, "VaR should be non-negative")
            self.assertGreaterEqual(risk_metrics.cvar_95, 0, "CVaR should be non-negative")
            self.assertGreaterEqual(risk_metrics.volatility, 0, "Volatility should be non-negative")
            
            # 验证风险指标的合理性
            self.assertLessEqual(risk_metrics.var_95, risk_metrics.portfolio_value, "VaR should not exceed portfolio value")
            self.assertGreaterEqual(risk_metrics.cvar_95, risk_metrics.var_95, "CVaR should be >= VaR")
            
            # 停止引擎
            self.risk_engine.stop()
            
            logger.info("✅ Risk indicator calculation test passed")
            logger.info(f"   Portfolio Value: ${risk_metrics.portfolio_value:,.2f}")
            logger.info(f"   Risk Level: {risk_metrics.risk_level.value}")
            logger.info(f"   VaR 95%: ${risk_metrics.var_95:,.2f}")
            logger.info(f"   CVaR 95%: ${risk_metrics.cvar_95:,.2f}")
            
        except Exception as e:
            logger.error(f"❌ Risk indicator calculation test failed: {e}")
            raise
    
    def test_alert_mechanism(self):
        """测试预警机制"""
        logger.info("Testing alert mechanism...")
        
        try:
            # 启动风险引擎
            self.risk_engine.start()
            
            # 设置预警回调
            alert_events = []
            
            def alert_callback(event: RiskEvent):
                alert_events.append(event)
                logger.info(f"Alert triggered: {event.event_type} - {event.severity.value}")
            
            self.risk_engine.register_event_callback('alert', alert_callback)
            
            # 模拟触发预警的条件
            warning_scenarios = [
                {
                    'name': 'High Volatility',
                    'market_data': self.mock_factory.create_mock_market_data('BTCUSDT', volatility=0.8),
                    'expected_alert': 'high_volatility'
                },
                {
                    'name': 'Large Position',
                    'position': self.mock_factory.create_mock_position_info(
                        'BTCUSDT', 
                        current_size=5.0,  # 大仓位
                        position_value=250000  # 超过限制
                    ),
                    'expected_alert': 'position_limit'
                },
                {
                    'name': 'Drawdown Warning',
                    'portfolio': self.mock_factory.create_mock_portfolio_data(80000),  # 20%回撤
                    'expected_alert': 'drawdown'
                }
            ]
            
            initial_alert_count = len(alert_events)
            
            for scenario in warning_scenarios:
                if 'market_data' in scenario:
                    self.risk_engine.update_market_data('BTCUSDT', scenario['market_data'])
                elif 'position' in scenario:
                    self.risk_engine.update_position('BTCUSDT', scenario['position'])
                elif 'portfolio' in scenario:
                    self.risk_engine.update_portfolio_data(scenario['portfolio'])
                
                # 等待预警处理
                time.sleep(0.5)
            
            # 验证预警生成
            final_alert_count = len(alert_events)
            self.assertGreater(final_alert_count, initial_alert_count, "Should generate alerts")
            
            # 验证预警内容
            for alert in alert_events[initial_alert_count:]:
                self.assertIsInstance(alert, RiskEvent, "Alert should be RiskEvent")
                self.assertIn(alert.severity, [RiskLevel.MEDIUM, RiskLevel.HIGH, RiskLevel.CRITICAL], 
                            "Alert should have appropriate severity")
                self.assertTrue(alert.action_required, "Alert should require action")
            
            # 停止引擎
            self.risk_engine.stop()
            
            logger.info("✅ Alert mechanism test passed")
            logger.info(f"   Generated {final_alert_count - initial_alert_count} alerts")
            
        except Exception as e:
            logger.error(f"❌ Alert mechanism test failed: {e}")
            raise
    
    def test_monitoring_performance(self):
        """测试监控性能"""
        logger.info("Testing monitoring performance...")
        
        try:
            # 启动风险引擎
            self.risk_engine.start()
            self.performance_monitor.start_monitoring(self.risk_engine)
            
            # 高负载测试
            symbols = ['BTCUSDT', 'ETHUSDT', 'ADAUSDT', 'BNBUSDT', 'SOLUSDT']
            test_duration = 10  # 10秒压力测试
            update_frequency = 0.02  # 20ms间隔
            
            end_time = time.time() + test_duration
            total_operations = 0
            
            while time.time() < end_time:
                for symbol in symbols:
                    # 市场数据更新
                    market_data = self.mock_factory.create_mock_market_data(symbol)
                    self.risk_engine.update_market_data(symbol, market_data)
                    
                    # 仓位更新
                    position = self.mock_factory.create_mock_position_info(symbol)
                    self.risk_engine.update_position(symbol, position)
                    
                    total_operations += 2
                
                time.sleep(update_frequency)
            
            # 停止监控
            performance_metrics = self.performance_monitor.stop_monitoring()
            
            # 验证性能指标
            operations_per_second = total_operations / test_duration
            self.assertGreater(operations_per_second, 100, "Should handle > 100 operations/second")
            
            avg_response_time = performance_metrics.get('avg_response_time', 0)
            self.assertLess(avg_response_time, 0.01, "Average response time should be < 10ms")
            
            error_rate = performance_metrics.get('error_rate', 1)
            self.assertLess(error_rate, 0.01, "Error rate should be < 1%")
            
            # 停止引擎
            self.risk_engine.stop()
            
            logger.info("✅ Monitoring performance test passed")
            logger.info(f"   Operations/second: {operations_per_second:.1f}")
            logger.info(f"   Avg response time: {avg_response_time*1000:.2f}ms")
            logger.info(f"   Error rate: {error_rate:.2%}")
            
        except Exception as e:
            logger.error(f"❌ Monitoring performance test failed: {e}")
            raise
    
    def test_concurrent_monitoring(self):
        """测试并发监控"""
        logger.info("Testing concurrent monitoring...")
        
        try:
            # 启动风险引擎
            self.risk_engine.start()
            
            # 创建多个并发监控线程
            def market_data_updater():
                for i in range(50):
                    symbol = f'TEST{i%5}USDT'
                    market_data = self.mock_factory.create_mock_market_data(symbol)
                    self.risk_engine.update_market_data(symbol, market_data)
                    time.sleep(0.01)
            
            def position_updater():
                for i in range(30):
                    symbol = f'TEST{i%3}USDT'
                    position = self.mock_factory.create_mock_position_info(symbol)
                    self.risk_engine.update_position(symbol, position)
                    time.sleep(0.02)
            
            def portfolio_updater():
                for i in range(20):
                    portfolio_value = 100000 + np.random.uniform(-10000, 10000)
                    portfolio_data = self.mock_factory.create_mock_portfolio_data(portfolio_value)
                    self.risk_engine.update_portfolio_data(portfolio_data)
                    time.sleep(0.05)
            
            # 启动并发线程
            threads = [
                threading.Thread(target=market_data_updater),
                threading.Thread(target=position_updater),
                threading.Thread(target=portfolio_updater)
            ]
            
            for thread in threads:
                thread.start()
            
            # 等待所有线程完成
            for thread in threads:
                thread.join(timeout=30)
            
            # 等待处理完成
            time.sleep(2)
            
            # 验证系统状态
            self.assertTrue(self.risk_engine.is_running, "Engine should still be running")
            
            # 验证统计信息
            stats = self.risk_engine.statistics
            self.assertGreaterEqual(stats['total_events'], 0, "Should have processed events")
            
            # 停止引擎
            self.risk_engine.stop()
            
            logger.info("✅ Concurrent monitoring test passed")
            logger.info(f"   Total events processed: {stats['total_events']}")
            
        except Exception as e:
            logger.error(f"❌ Concurrent monitoring test failed: {e}")
            raise


if __name__ == '__main__':
    unittest.main()
