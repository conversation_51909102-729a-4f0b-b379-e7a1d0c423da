package com.crypto.trading.sdk.response;

import java.util.function.Consumer;

/**
 * 响应处理器接口
 * 定义API响应和WebSocket消息的处理方法
 */
public interface ResponseHandler {

    /**
     * 处理API响应
     *
     * @param response JSON响应字符串
     * @param clazz    目标类型
     * @param <T>      返回类型
     * @return 转换后的对象
     */
    <T> T handleApiResponse(String response, Class<T> clazz);

    /**
     * 异步处理API响应
     *
     * @param response  JSON响应字符串
     * @param clazz     目标类型
     * @param onSuccess 成功回调
     * @param onError   错误回调
     * @param <T>       返回类型
     */
    <T> void handleApiResponseAsync(String response, Class<T> clazz, Consumer<T> onSuccess, Consumer<Exception> onError);

    /**
     * 处理WebSocket消息
     *
     * @param message JSON消息字符串
     * @param clazz   目标类型
     * @param <T>     返回类型
     * @return 转换后的对象
     */
    <T> T handleWebSocketMessage(String message, Class<T> clazz);
} 