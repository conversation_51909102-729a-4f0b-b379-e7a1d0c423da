"""
实时数据处理器

处理来自Kafka、WebSocket等实时数据流，提供数据缓存、分发、
监控和异常处理功能。支持多种数据源的实时数据处理。

Author: Crypto ML Strategy Team
Date: 2024-12-19
"""

import asyncio
import json
import logging
import time
import threading
from typing import Dict, List, Optional, Any, Union, Callable, Set
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import pandas as pd
from collections import deque, defaultdict
import websockets
import aiohttp

from .data_config import KafkaConfig
from .cache.cache_manager import CacheManager

logger = logging.getLogger(__name__)


class StreamType(Enum):
    """数据流类型枚举"""
    KAFKA = "kafka"
    WEBSOCKET = "websocket"
    HTTP_STREAM = "http_stream"
    TCP_STREAM = "tcp_stream"


class MessageType(Enum):
    """消息类型枚举"""
    MARKET_DATA = "market_data"
    TRADING_SIGNAL = "trading_signal"
    ORDER_UPDATE = "order_update"
    PORTFOLIO_UPDATE = "portfolio_update"
    SYSTEM_EVENT = "system_event"
    HEARTBEAT = "heartbeat"


@dataclass
class StreamMessage:
    """流消息数据类"""
    message_id: str
    message_type: MessageType
    source: str
    timestamp: datetime
    data: Any
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'message_id': self.message_id,
            'message_type': self.message_type.value,
            'source': self.source,
            'timestamp': self.timestamp.isoformat(),
            'data': self.data,
            'metadata': self.metadata
        }


@dataclass
class StreamStats:
    """流统计信息"""
    total_messages: int = 0
    successful_messages: int = 0
    failed_messages: int = 0
    bytes_received: int = 0
    last_message_time: Optional[datetime] = None
    connection_time: Optional[datetime] = None
    disconnection_count: int = 0
    
    @property
    def success_rate(self) -> float:
        """消息处理成功率"""
        if self.total_messages == 0:
            return 0.0
        return self.successful_messages / self.total_messages
    
    @property
    def uptime(self) -> float:
        """连接时长（秒）"""
        if self.connection_time is None:
            return 0.0
        return (datetime.now() - self.connection_time).total_seconds()


class MessageHandler:
    """消息处理器基类"""
    
    def __init__(self, handler_id: str):
        self.handler_id = handler_id
        self.message_queue = asyncio.Queue()
        self.is_running = False
        self.stats = StreamStats()
    
    async def handle_message(self, message: StreamMessage) -> bool:
        """
        处理消息
        
        Args:
            message: 流消息
            
        Returns:
            是否处理成功
        """
        try:
            await self.message_queue.put(message)
            return True
        except Exception as e:
            logger.error(f"消息处理失败: {e}")
            return False
    
    async def process_messages(self) -> None:
        """处理消息队列"""
        while self.is_running:
            try:
                message = await asyncio.wait_for(
                    self.message_queue.get(), timeout=1.0
                )
                await self._process_single_message(message)
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                logger.error(f"消息队列处理失败: {e}")
    
    async def _process_single_message(self, message: StreamMessage) -> None:
        """处理单个消息"""
        try:
            self.stats.total_messages += 1
            self.stats.last_message_time = datetime.now()
            
            # 子类实现具体处理逻辑
            success = await self.process_message_data(message)
            
            if success:
                self.stats.successful_messages += 1
            else:
                self.stats.failed_messages += 1
                
        except Exception as e:
            logger.error(f"处理单个消息失败: {e}")
            self.stats.failed_messages += 1
    
    async def process_message_data(self, message: StreamMessage) -> bool:
        """
        处理消息数据（子类重写）
        
        Args:
            message: 流消息
            
        Returns:
            是否处理成功
        """
        return True
    
    async def start(self) -> None:
        """启动消息处理器"""
        self.is_running = True
        asyncio.create_task(self.process_messages())
    
    async def stop(self) -> None:
        """停止消息处理器"""
        self.is_running = False


class KafkaProcessor:
    """Kafka数据处理器"""
    
    def __init__(self, config: KafkaConfig, cache_manager: Optional[CacheManager] = None):
        """
        初始化Kafka处理器
        
        Args:
            config: Kafka配置
            cache_manager: 缓存管理器
        """
        self.config = config
        self.cache_manager = cache_manager
        self.consumer = None
        self.producer = None
        self.message_handlers: Dict[str, MessageHandler] = {}
        self.stats = StreamStats()
        self.is_running = False
        self.lock = threading.RLock()
        
        logger.info("Kafka处理器初始化完成")
    
    async def initialize(self) -> bool:
        """初始化Kafka连接"""
        try:
            from aiokafka import AIOKafkaConsumer, AIOKafkaProducer
            
            # 创建消费者
            self.consumer = AIOKafkaConsumer(
                *self.config.topics,
                bootstrap_servers=self.config.bootstrap_servers,
                group_id=self.config.consumer_group,
                auto_offset_reset=self.config.auto_offset_reset,
                enable_auto_commit=self.config.enable_auto_commit,
                auto_commit_interval_ms=self.config.auto_commit_interval_ms,
                session_timeout_ms=self.config.session_timeout_ms,
                heartbeat_interval_ms=self.config.heartbeat_interval_ms,
                max_poll_records=self.config.max_poll_records,
                security_protocol=self.config.security_protocol,
                value_deserializer=lambda x: json.loads(x.decode('utf-8')) if x else None
            )
            
            # 创建生产者
            self.producer = AIOKafkaProducer(
                bootstrap_servers=self.config.bootstrap_servers,
                security_protocol=self.config.security_protocol,
                value_serializer=lambda x: json.dumps(x).encode('utf-8')
            )
            
            # 启动连接
            await self.consumer.start()
            await self.producer.start()
            
            self.stats.connection_time = datetime.now()
            logger.info("Kafka连接建立成功")
            return True
            
        except ImportError:
            logger.error("aiokafka库未安装")
            return False
        except Exception as e:
            logger.error(f"Kafka连接失败: {e}")
            return False
    
    async def start_consuming(self) -> None:
        """开始消费消息"""
        if not self.consumer:
            logger.error("Kafka消费者未初始化")
            return
        
        self.is_running = True
        
        try:
            async for message in self.consumer:
                if not self.is_running:
                    break
                
                await self._process_kafka_message(message)
                
        except Exception as e:
            logger.error(f"Kafka消息消费失败: {e}")
            self.stats.disconnection_count += 1
        finally:
            self.is_running = False
    
    async def _process_kafka_message(self, kafka_message) -> None:
        """处理Kafka消息"""
        try:
            self.stats.total_messages += 1
            self.stats.bytes_received += len(kafka_message.value) if kafka_message.value else 0
            self.stats.last_message_time = datetime.now()
            
            # 解析消息
            message_data = kafka_message.value
            if not message_data:
                return
            
            # 创建流消息对象
            stream_message = StreamMessage(
                message_id=f"kafka_{kafka_message.offset}_{kafka_message.partition}",
                message_type=MessageType(message_data.get('type', 'market_data')),
                source=f"kafka_{kafka_message.topic}",
                timestamp=datetime.fromtimestamp(kafka_message.timestamp / 1000),
                data=message_data.get('data', {}),
                metadata={
                    'topic': kafka_message.topic,
                    'partition': kafka_message.partition,
                    'offset': kafka_message.offset,
                    'key': kafka_message.key
                }
            )
            
            # 缓存消息
            if self.cache_manager:
                cache_key = f"kafka_message_{stream_message.message_id}"
                await self.cache_manager.put(cache_key, stream_message.to_dict(), ttl=3600)
            
            # 分发给消息处理器
            await self._distribute_message(stream_message)
            
            self.stats.successful_messages += 1
            
        except Exception as e:
            logger.error(f"处理Kafka消息失败: {e}")
            self.stats.failed_messages += 1
    
    async def _distribute_message(self, message: StreamMessage) -> None:
        """分发消息给处理器"""
        for handler in self.message_handlers.values():
            try:
                await handler.handle_message(message)
            except Exception as e:
                logger.error(f"消息分发失败: {e}")
    
    async def send_message(self, topic: str, message: Dict[str, Any]) -> bool:
        """
        发送消息到Kafka
        
        Args:
            topic: 主题
            message: 消息内容
            
        Returns:
            是否发送成功
        """
        try:
            if not self.producer:
                logger.error("Kafka生产者未初始化")
                return False
            
            await self.producer.send_and_wait(topic, message)
            logger.debug(f"消息发送成功到主题: {topic}")
            return True
            
        except Exception as e:
            logger.error(f"发送Kafka消息失败: {e}")
            return False
    
    def add_message_handler(self, handler: MessageHandler) -> None:
        """添加消息处理器"""
        self.message_handlers[handler.handler_id] = handler
        logger.info(f"添加消息处理器: {handler.handler_id}")
    
    def remove_message_handler(self, handler_id: str) -> None:
        """移除消息处理器"""
        if handler_id in self.message_handlers:
            del self.message_handlers[handler_id]
            logger.info(f"移除消息处理器: {handler_id}")
    
    async def close(self) -> None:
        """关闭Kafka连接"""
        try:
            self.is_running = False
            
            if self.consumer:
                await self.consumer.stop()
            
            if self.producer:
                await self.producer.stop()
            
            logger.info("Kafka连接已关闭")
            
        except Exception as e:
            logger.error(f"关闭Kafka连接失败: {e}")


class WebSocketProcessor:
    """WebSocket数据处理器"""
    
    def __init__(self, url: str, cache_manager: Optional[CacheManager] = None):
        """
        初始化WebSocket处理器
        
        Args:
            url: WebSocket URL
            cache_manager: 缓存管理器
        """
        self.url = url
        self.cache_manager = cache_manager
        self.websocket = None
        self.message_handlers: Dict[str, MessageHandler] = {}
        self.stats = StreamStats()
        self.is_running = False
        self.reconnect_interval = 5  # 重连间隔（秒）
        
        logger.info(f"WebSocket处理器初始化: {url}")
    
    async def connect(self) -> bool:
        """建立WebSocket连接"""
        try:
            self.websocket = await websockets.connect(self.url)
            self.stats.connection_time = datetime.now()
            logger.info(f"WebSocket连接建立成功: {self.url}")
            return True
            
        except Exception as e:
            logger.error(f"WebSocket连接失败: {e}")
            self.stats.disconnection_count += 1
            return False
    
    async def start_listening(self) -> None:
        """开始监听WebSocket消息"""
        self.is_running = True
        
        while self.is_running:
            try:
                if not self.websocket:
                    if not await self.connect():
                        await asyncio.sleep(self.reconnect_interval)
                        continue
                
                async for message in self.websocket:
                    if not self.is_running:
                        break
                    
                    await self._process_websocket_message(message)
                    
            except websockets.exceptions.ConnectionClosed:
                logger.warning("WebSocket连接断开，尝试重连...")
                self.websocket = None
                self.stats.disconnection_count += 1
                await asyncio.sleep(self.reconnect_interval)
            except Exception as e:
                logger.error(f"WebSocket消息处理失败: {e}")
                await asyncio.sleep(self.reconnect_interval)
    
    async def _process_websocket_message(self, message: str) -> None:
        """处理WebSocket消息"""
        try:
            self.stats.total_messages += 1
            self.stats.bytes_received += len(message.encode('utf-8'))
            self.stats.last_message_time = datetime.now()
            
            # 解析JSON消息
            message_data = json.loads(message)
            
            # 创建流消息对象
            stream_message = StreamMessage(
                message_id=f"ws_{int(time.time() * 1000000)}",
                message_type=MessageType(message_data.get('type', 'market_data')),
                source=f"websocket_{self.url}",
                timestamp=datetime.now(),
                data=message_data.get('data', {}),
                metadata={
                    'url': self.url,
                    'raw_message': message
                }
            )
            
            # 缓存消息
            if self.cache_manager:
                cache_key = f"ws_message_{stream_message.message_id}"
                await self.cache_manager.put(cache_key, stream_message.to_dict(), ttl=1800)
            
            # 分发给消息处理器
            await self._distribute_message(stream_message)
            
            self.stats.successful_messages += 1
            
        except json.JSONDecodeError:
            logger.warning(f"WebSocket消息JSON解析失败: {message}")
            self.stats.failed_messages += 1
        except Exception as e:
            logger.error(f"处理WebSocket消息失败: {e}")
            self.stats.failed_messages += 1
    
    async def _distribute_message(self, message: StreamMessage) -> None:
        """分发消息给处理器"""
        for handler in self.message_handlers.values():
            try:
                await handler.handle_message(message)
            except Exception as e:
                logger.error(f"WebSocket消息分发失败: {e}")
    
    async def send_message(self, message: Dict[str, Any]) -> bool:
        """
        发送消息到WebSocket
        
        Args:
            message: 消息内容
            
        Returns:
            是否发送成功
        """
        try:
            if not self.websocket:
                logger.error("WebSocket连接未建立")
                return False
            
            await self.websocket.send(json.dumps(message))
            logger.debug("WebSocket消息发送成功")
            return True
            
        except Exception as e:
            logger.error(f"发送WebSocket消息失败: {e}")
            return False
    
    def add_message_handler(self, handler: MessageHandler) -> None:
        """添加消息处理器"""
        self.message_handlers[handler.handler_id] = handler
        logger.info(f"添加WebSocket消息处理器: {handler.handler_id}")
    
    def remove_message_handler(self, handler_id: str) -> None:
        """移除消息处理器"""
        if handler_id in self.message_handlers:
            del self.message_handlers[handler_id]
            logger.info(f"移除WebSocket消息处理器: {handler_id}")
    
    async def close(self) -> None:
        """关闭WebSocket连接"""
        try:
            self.is_running = False
            
            if self.websocket:
                await self.websocket.close()
            
            logger.info("WebSocket连接已关闭")
            
        except Exception as e:
            logger.error(f"关闭WebSocket连接失败: {e}")


class RealtimeProcessor:
    """实时数据处理器主类"""
    
    def __init__(self, cache_manager: Optional[CacheManager] = None):
        """
        初始化实时数据处理器
        
        Args:
            cache_manager: 缓存管理器
        """
        self.cache_manager = cache_manager
        self.kafka_processors: Dict[str, KafkaProcessor] = {}
        self.websocket_processors: Dict[str, WebSocketProcessor] = {}
        self.message_handlers: Dict[str, MessageHandler] = {}
        self.global_stats = StreamStats()
        self.lock = threading.RLock()
        
        logger.info("实时数据处理器初始化完成")
    
    async def add_kafka_processor(self, name: str, config: KafkaConfig) -> bool:
        """
        添加Kafka处理器
        
        Args:
            name: 处理器名称
            config: Kafka配置
            
        Returns:
            是否添加成功
        """
        try:
            processor = KafkaProcessor(config, self.cache_manager)
            success = await processor.initialize()
            
            if success:
                with self.lock:
                    self.kafka_processors[name] = processor
                logger.info(f"Kafka处理器添加成功: {name}")
                return True
            else:
                logger.error(f"Kafka处理器添加失败: {name}")
                return False
                
        except Exception as e:
            logger.error(f"添加Kafka处理器失败: {e}")
            return False
    
    async def add_websocket_processor(self, name: str, url: str) -> bool:
        """
        添加WebSocket处理器
        
        Args:
            name: 处理器名称
            url: WebSocket URL
            
        Returns:
            是否添加成功
        """
        try:
            processor = WebSocketProcessor(url, self.cache_manager)
            success = await processor.connect()
            
            if success:
                with self.lock:
                    self.websocket_processors[name] = processor
                logger.info(f"WebSocket处理器添加成功: {name}")
                return True
            else:
                logger.error(f"WebSocket处理器添加失败: {name}")
                return False
                
        except Exception as e:
            logger.error(f"添加WebSocket处理器失败: {e}")
            return False
    
    def add_message_handler(self, handler: MessageHandler) -> None:
        """添加全局消息处理器"""
        with self.lock:
            self.message_handlers[handler.handler_id] = handler
            
            # 添加到所有处理器
            for processor in self.kafka_processors.values():
                processor.add_message_handler(handler)
            
            for processor in self.websocket_processors.values():
                processor.add_message_handler(handler)
        
        logger.info(f"添加全局消息处理器: {handler.handler_id}")
    
    async def start_all_processors(self) -> None:
        """启动所有处理器"""
        tasks = []
        
        # 启动Kafka处理器
        for name, processor in self.kafka_processors.items():
            task = asyncio.create_task(processor.start_consuming())
            tasks.append(task)
            logger.info(f"启动Kafka处理器: {name}")
        
        # 启动WebSocket处理器
        for name, processor in self.websocket_processors.items():
            task = asyncio.create_task(processor.start_listening())
            tasks.append(task)
            logger.info(f"启动WebSocket处理器: {name}")
        
        # 启动消息处理器
        for handler in self.message_handlers.values():
            await handler.start()
        
        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        try:
            stats = {
                'global_stats': {
                    'total_messages': self.global_stats.total_messages,
                    'successful_messages': self.global_stats.successful_messages,
                    'failed_messages': self.global_stats.failed_messages,
                    'success_rate': self.global_stats.success_rate,
                    'bytes_received': self.global_stats.bytes_received,
                    'last_message_time': self.global_stats.last_message_time.isoformat() if self.global_stats.last_message_time else None
                },
                'kafka_processors': {},
                'websocket_processors': {},
                'message_handlers': {}
            }
            
            # Kafka处理器统计
            for name, processor in self.kafka_processors.items():
                stats['kafka_processors'][name] = {
                    'total_messages': processor.stats.total_messages,
                    'successful_messages': processor.stats.successful_messages,
                    'failed_messages': processor.stats.failed_messages,
                    'success_rate': processor.stats.success_rate,
                    'bytes_received': processor.stats.bytes_received,
                    'uptime': processor.stats.uptime,
                    'disconnection_count': processor.stats.disconnection_count,
                    'is_running': processor.is_running
                }
            
            # WebSocket处理器统计
            for name, processor in self.websocket_processors.items():
                stats['websocket_processors'][name] = {
                    'total_messages': processor.stats.total_messages,
                    'successful_messages': processor.stats.successful_messages,
                    'failed_messages': processor.stats.failed_messages,
                    'success_rate': processor.stats.success_rate,
                    'bytes_received': processor.stats.bytes_received,
                    'uptime': processor.stats.uptime,
                    'disconnection_count': processor.stats.disconnection_count,
                    'is_running': processor.is_running,
                    'url': processor.url
                }
            
            # 消息处理器统计
            for handler_id, handler in self.message_handlers.items():
                stats['message_handlers'][handler_id] = {
                    'total_messages': handler.stats.total_messages,
                    'successful_messages': handler.stats.successful_messages,
                    'failed_messages': handler.stats.failed_messages,
                    'success_rate': handler.stats.success_rate,
                    'is_running': handler.is_running,
                    'queue_size': handler.message_queue.qsize()
                }
            
            return stats
            
        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return {'error': str(e)}
    
    async def health_check(self) -> Dict[str, bool]:
        """健康检查"""
        health = {}
        
        # 检查Kafka处理器
        for name, processor in self.kafka_processors.items():
            health[f"kafka_{name}"] = processor.is_running and processor.consumer is not None
        
        # 检查WebSocket处理器
        for name, processor in self.websocket_processors.items():
            health[f"websocket_{name}"] = processor.is_running and processor.websocket is not None
        
        # 检查消息处理器
        for handler_id, handler in self.message_handlers.items():
            health[f"handler_{handler_id}"] = handler.is_running
        
        return health
    
    async def close_all(self) -> None:
        """关闭所有处理器"""
        try:
            # 停止消息处理器
            for handler in self.message_handlers.values():
                await handler.stop()
            
            # 关闭Kafka处理器
            for processor in self.kafka_processors.values():
                await processor.close()
            
            # 关闭WebSocket处理器
            for processor in self.websocket_processors.values():
                await processor.close()
            
            logger.info("所有实时数据处理器已关闭")
            
        except Exception as e:
            logger.error(f"关闭实时数据处理器失败: {e}")