# 虚拟货币量化交易系统设计文档

## 文档目录

本目录包含虚拟货币量化交易系统的设计文档，提供了系统的架构设计、数据库设计、项目结构和数据流转说明。

### 文档列表

1. [系统架构设计](./system-architecture.md)
   - 系统概述
   - 核心模块设计
   - 数据流转
   - 接口设计
   - 订单追踪机制
   - 系统启动流程
   - 虚拟线程应用
   - API限流处理

2. [数据库设计](./database-design.md)
   - MySQL表设计
   - InfluxDB设计
   - 索引设计
   - 查询优化策略
   - 数据同步与备份策略

3. [项目结构设计](./project-structure.md)
   - 项目总体结构
   - 模块详细说明
   - 父POM文件
   - 启动类设计
   - 模块依赖关系
   - 虚拟线程应用

4. [数据流转图](./data-flow-diagram.md)
   - 市场数据流转路径
   - 订单追踪流程
   - 全系统数据流转概览

## 系统概览

虚拟货币量化交易系统是一个基于Java+Python的高性能量化交易平台，通过集成币安期货API，实现自动化交易策略的执行和管理。系统采用模块化设计，包括市场数据采集、机器学习策略、交易执行、账户管理、风险控制等功能模块，通过Kafka消息队列实现各模块间的解耦和高效数据流转。

### 核心特点

- **高性能**：利用JDK21虚拟线程提升系统性能，确保满足实时交易需求
- **低延迟**：最小化从数据采集到交易执行的延迟，提高交易竞争力
- **可靠性**：完善的订单追踪机制、异常处理和灾备恢复策略
- **可扩展性**：模块化设计，易于添加新的交易策略和支持新的交易品种
- **可监控性**：全方位的监控和告警机制，确保系统稳定运行
- **高级策略**：基于Python的机器学习策略模块，支持复杂的量化模型

### 技术栈

- **编程语言**：Java 21（后端核心）, Python 3.8+（机器学习策略）
- **构建工具**：Maven（Java），Pip（Python）
- **消息队列**：Kafka
- **数据库**：MySQL
- **时序数据库**：InfluxDB
- **交易API**：币安期货API (binance-futures-connector-java 3.0.5)
- **数据持久化**：MyBatis-Plus
- **机器学习框架**：PyTorch, scikit-learn, NumPy, Pandas

## 快速开始

要启动系统，需要按照以下步骤操作：

1. 确保已安装JDK 21+、Maven 3.8+、Python 3.8+、MySQL 8.0+、Kafka 3.5+、InfluxDB 2.0+
2. 克隆项目代码
3. 配置数据库连接和API密钥
4. 安装Python依赖：`pip install -r crypto-ml-strategy/requirements.txt`
5. 使用Maven构建Java模块：`mvn clean install`
6. 启动Java应用：`java -jar crypto-bootstrap/target/crypto-bootstrap-1.0.0-SNAPSHOT.jar`
7. 启动Python策略服务：`python crypto-ml-strategy/src/main.py`

## 注意事项

- 由于币安API在大陆无法访问，需要使用海外服务器部署系统
- 确保账户中有足够的资金进行交易
- 建议先使用测试网进行验证，再在实盘环境中使用

## 开发计划

虚拟货币量化交易系统的开发分为以下阶段：

### 开发周期

- **第1周**：项目搭建和公共模块开发
- **第2周**：SDK集成模块和市场数据模块开发
- **第3周**：Python机器学习策略模块开发
- **第4周**：交易模块和账户模块开发
- **第5周**：风控模块和监控模块开发
- **第6周**：集成测试和性能优化

### 任务分解

每个模块的开发任务分解为核心类的实现，包括接口定义、实现类编写和单元测试等。特别关注高性能和低延迟的实现，如使用虚拟线程、异步数据处理等。

## 任务追踪

开发任务将通过以下任务追踪表进行管理：

### 公共模块 (crypto-common)

| 任务ID | 描述 | 优先级 | 状态 | 预估工时 |
|--------|------|--------|------|----------|
| COMMON-001 | 创建多模块Maven项目并配置父POM文件 | 高 | 待办 | 4h |
| COMMON-002 | 实现配置管理类（AppConfig等） | 高 | 待办 | 8h |
| COMMON-003 | 定义常量和枚举类 | 中 | 待办 | 4h |
| COMMON-004 | 实现工具类（DateTimeUtils等） | 中 | 待办 | 8h |
| COMMON-005 | 定义异常类 | 中 | 待办 | 4h |
| COMMON-006 | 定义数据传输对象 | 中 | 待办 | 6h |
| COMMON-007 | 编写单元测试 | 中 | 待办 | 8h |

### SDK集成模块 (crypto-sdk)

| 任务ID | 描述 | 优先级 | 状态 | 预估工时 |
|--------|------|--------|------|----------|
| SDK-001 | 实现币安API客户端 | 高 | 待办 | 16h |
| SDK-002 | 实现WebSocket客户端 | 高 | 待办 | 12h |
| SDK-003 | 实现API限流器 | 高 | 待办 | 8h |
| SDK-004 | 实现响应处理器和数据转换器 | 中 | 待办 | 8h |
| SDK-005 | 编写单元测试 | 中 | 待办 | 12h |

### 市场数据模块 (crypto-market-data)

| 任务ID | 描述 | 优先级 | 状态 | 预估工时 |
|--------|------|--------|------|----------|
| MARKET-001 | 实现WebSocket监听器 | 高 | 待办 | 16h |
| MARKET-002 | 实现数据处理器 | 高 | 待办 | 12h |
| MARKET-003 | 实现Kafka生产者 | 高 | 待办 | 8h |
| MARKET-004 | 实现数据存储（InfluxDB） | 中 | 待办 | 12h |
| MARKET-005 | 编写单元测试 | 中 | 待办 | 12h |

### 机器学习策略模块 (crypto-ml-strategy)

| 任务ID | 描述 | 优先级 | 状态 | 预估工时 |
|--------|------|--------|------|----------|
| ML-STRATEGY-001 | 搭建Python开发环境和项目结构 | 高 | 待办 | 8h |
| ML-STRATEGY-002 | 实现Kafka客户端（消费者和生产者） | 高 | 待办 | 12h |
| ML-STRATEGY-003 | 实现基础策略框架和策略接口 | 高 | 待办 | 8h |
| ML-STRATEGY-004 | 实现数据处理和特征工程 | 高 | 待办 | 16h |
| ML-STRATEGY-005 | 实现LPPL泡沫检测策略 | 中 | 待办 | 12h |
| ML-STRATEGY-006 | 实现情绪分析策略 | 中 | 待办 | 12h |
| ML-STRATEGY-007 | 实现趋势分析策略 | 中 | 待办 | 12h |
| ML-STRATEGY-008 | 实现策略集成和信号生成 | 高 | 待办 | 8h |
| ML-STRATEGY-009 | 实现模型训练和保存功能 | 中 | 待办 | 12h |
| ML-STRATEGY-010 | 实现日志和监控功能 | 中 | 待办 | 8h |
| ML-STRATEGY-011 | 编写单元测试 | 中 | 待办 | 16h |
| ML-STRATEGY-012 | 创建策略开发Jupyter笔记本 | 低 | 待办 | 8h |

### 交易模块 (crypto-trade)

| 任务ID | 描述 | 优先级 | 状态 | 预估工时 |
|--------|------|--------|------|----------|
| TRADE-001 | 实现订单服务 | 高 | 待办 | 12h |
| TRADE-002 | 实现交易执行器 | 高 | 待办 | 12h |
| TRADE-003 | 实现订单追踪器 | 高 | 待办 | 16h |
| TRADE-004 | 实现Kafka消费者 | 中 | 待办 | 8h |
| TRADE-005 | 实现用户数据流处理器 | 中 | 待办 | 8h |
| TRADE-006 | 实现API限流处理器 | 中 | 待办 | 12h |
| TRADE-007 | 编写单元测试 | 中 | 待办 | 16h |

### 账户模块 (crypto-account)

| 任务ID | 描述 | 优先级 | 状态 | 预估工时 |
|--------|------|--------|------|----------|
| ACCOUNT-001 | 实现账户服务 | 高 | 待办 | 12h |
| ACCOUNT-002 | 实现持仓服务 | 高 | 待办 | 12h |
| ACCOUNT-003 | 实现盈亏计算服务 | 高 | 待办 | 8h |
| ACCOUNT-004 | 实现Kafka消费者 | 中 | 待办 | 8h |
| ACCOUNT-005 | 实现数据持久化 | 中 | 待办 | 12h |
| ACCOUNT-006 | 编写单元测试 | 中 | 待办 | 12h |

### 风控模块 (crypto-risk)

| 任务ID | 描述 | 优先级 | 状态 | 预估工时 |
|--------|------|--------|------|----------|
| RISK-001 | 实现风险服务 | 高 | 待办 | 12h |
| RISK-002 | 实现风险限制器 | 高 | 待办 | 12h |
| RISK-003 | 实现风险监控器 | 高 | 待办 | 8h |
| RISK-004 | 实现预警触发器 | 中 | 待办 | 8h |
| RISK-005 | 编写单元测试 | 中 | 待办 | 12h |

### 监控模块 (crypto-monitor)

| 任务ID | 描述 | 优先级 | 状态 | 预估工时 |
|--------|------|--------|------|----------|
| MONITOR-001 | 实现监控服务 | 高 | 待办 | 12h |
| MONITOR-002 | 实现健康检查服务 | 高 | 待办 | 8h |
| MONITOR-003 | 实现性能监控器 | 高 | 待办 | 12h |
| MONITOR-004 | 实现自动恢复机制 | 中 | 待办 | 12h |
| MONITOR-005 | 编写单元测试 | 中 | 待办 | 12h |

### 应用启动模块 (crypto-bootstrap)

| 任务ID | 描述 | 优先级 | 状态 | 预估工时 |
|--------|------|--------|------|----------|
| BOOTSTRAP-001 | 实现应用启动类 | 高 | 待办 | 8h |
| BOOTSTRAP-002 | 实现模块初始化器 | 高 | 待办 | 12h |
| BOOTSTRAP-003 | 实现启动顺序管理器 | 高 | 待办 | 8h |
| BOOTSTRAP-004 | 实现关闭钩子 | 中 | 待办 | 8h |
| BOOTSTRAP-005 | 编写单元测试 | 中 | 待办 | 12h |

### 性能优化

| 任务ID | 描述 | 优先级 | 状态 | 预估工时 |
|--------|------|--------|------|----------|
| PERF-001 | 在WebSocketManager中实现虚拟线程处理 | 高 | 待办 | 8h |
| PERF-002 | 优化KlineData和DepthData的内存表示 | 高 | 待办 | 8h |
| PERF-003 | 实现市场数据异步写入InfluxDB | 高 | 待办 | 12h |
| PERF-004 | 实现自适应API限流器 | 高 | 待办 | 16h |
| PERF-005 | 优化Kafka生产者和消费者配置 | 高 | 待办 | 8h |
| PERF-006 | 设计并执行系统性能测试 | 高 | 待办 | 16h |

## 虚拟线程应用场景

系统充分利用JDK21虚拟线程特性，主要应用在以下场景：

1. **市场数据处理**：使用虚拟线程处理大量WebSocket连接和市场数据
2. **订单追踪**：使用虚拟线程异步追踪订单状态变化
3. **API请求处理**：使用虚拟线程池处理API请求，避免阻塞主线程
4. **数据异步写入**：使用虚拟线程异步将数据写入数据库，减少数据持久化对主流程的影响
5. **与Python通信**：使用虚拟线程处理与Python策略模块的消息交换

## Python机器学习策略优化

Python机器学习策略模块的性能优化策略：

1. **Numba JIT编译**：使用Numba加速计算密集型函数
2. **NumPy向量化操作**：使用NumPy的向量化操作替代Python循环
3. **并行处理**：使用多进程并行处理不同的策略和数据
4. **PyTorch优化**：利用GPU加速机器学习模型（如可用）
5. **数据缓存**：实现高效的数据缓存机制减少重复计算

## Java与Python通信机制

系统使用以下机制实现Java与Python模块之间的高效通信：

1. **Kafka消息队列**：作为主要通信渠道，传递市场数据和策略信号
2. **标准化JSON消息格式**：确保跨语言兼容性
3. **批量消息处理**：提高通信效率
4. **心跳检测**：监控服务健康状态
5. **故障恢复机制**：确保系统稳定性和可靠性