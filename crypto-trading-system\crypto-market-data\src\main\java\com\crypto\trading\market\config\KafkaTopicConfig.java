package com.crypto.trading.market.config;

import org.apache.kafka.clients.admin.AdminClientConfig;
import org.apache.kafka.clients.admin.NewTopic;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.core.KafkaAdmin;

import java.util.HashMap;
import java.util.Map;

/**
 * Kafka主题配置
 * 自动创建所需的Kafka主题，包括原始数据主题和死信队列主题
 */
@Configuration
public class KafkaTopicConfig {

    @Value("${spring.kafka.bootstrap-servers}")
    private String bootstrapServers;

    /**
     * Kafka管理客户端配置
     */
    @Bean
    public KafkaAdmin kafkaAdmin() {
        Map<String, Object> configs = new HashMap<>();
        configs.put(AdminClientConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
        return new KafkaAdmin(configs);
    }

    // ==================== 原始数据主题 ====================

    /**
     * K线原始数据主题
     * 用于异步数据库存储
     */
    @Bean
    public NewTopic klineRawDataTopic() {
        return new NewTopic("kline.raw.data", 6, (short) 2)
                .configs(Map.of(
                        "retention.ms", "604800000", // 7天保留期
                        "compression.type", "lz4",   // 压缩类型
                        "cleanup.policy", "delete"   // 清理策略
                ));
    }

    /**
     * 深度原始数据主题
     * 用于异步数据库存储
     */
    @Bean
    public NewTopic depthRawDataTopic() {
        return new NewTopic("depth.raw.data", 6, (short) 2)
                .configs(Map.of(
                        "retention.ms", "604800000", // 7天保留期
                        "compression.type", "lz4",   // 压缩类型
                        "cleanup.policy", "delete"   // 清理策略
                ));
    }

    /**
     * 交易原始数据主题
     * 用于异步数据库存储
     */
    @Bean
    public NewTopic tradeRawDataTopic() {
        return new NewTopic("trade.raw.data", 6, (short) 2)
                .configs(Map.of(
                        "retention.ms", "604800000", // 7天保留期
                        "compression.type", "lz4",   // 压缩类型
                        "cleanup.policy", "delete"   // 清理策略
                ));
    }

    // ==================== 死信队列主题 ====================

    /**
     * K线数据死信队列主题
     */
    @Bean
    public NewTopic klineRawDataDltTopic() {
        return new NewTopic("kline.raw.data-dlt", 3, (short) 2)
                .configs(Map.of(
                        "retention.ms", "2592000000", // 30天保留期
                        "compression.type", "lz4",    // 压缩类型
                        "cleanup.policy", "delete"    // 清理策略
                ));
    }

    /**
     * 深度数据死信队列主题
     */
    @Bean
    public NewTopic depthRawDataDltTopic() {
        return new NewTopic("depth.raw.data-dlt", 3, (short) 2)
                .configs(Map.of(
                        "retention.ms", "2592000000", // 30天保留期
                        "compression.type", "lz4",    // 压缩类型
                        "cleanup.policy", "delete"    // 清理策略
                ));
    }

    /**
     * 交易数据死信队列主题
     */
    @Bean
    public NewTopic tradeRawDataDltTopic() {
        return new NewTopic("trade.raw.data-dlt", 3, (short) 2)
                .configs(Map.of(
                        "retention.ms", "2592000000", // 30天保留期
                        "compression.type", "lz4",    // 压缩类型
                        "cleanup.policy", "delete"    // 清理策略
                ));
    }

    // ==================== 现有主题（保持兼容性）====================

    /**
     * K线数据主题（供Python策略模块使用）
     */
    @Bean
    public NewTopic klineDataTopic() {
        return new NewTopic("kline.data", 3, (short) 2)
                .configs(Map.of(
                        "retention.ms", "604800000", // 7天保留期
                        "compression.type", "lz4",   // 压缩类型
                        "cleanup.policy", "delete"   // 清理策略
                ));
    }

    /**
     * 深度数据主题（供Python策略模块使用）
     */
    @Bean
    public NewTopic depthDataTopic() {
        return new NewTopic("depth.data", 3, (short) 2)
                .configs(Map.of(
                        "retention.ms", "604800000", // 7天保留期
                        "compression.type", "lz4",   // 压缩类型
                        "cleanup.policy", "delete"   // 清理策略
                ));
    }

    /**
     * 交易数据主题（供Python策略模块使用）
     */
    @Bean
    public NewTopic tradeDataTopic() {
        return new NewTopic("trade.data", 3, (short) 2)
                .configs(Map.of(
                        "retention.ms", "604800000", // 7天保留期
                        "compression.type", "lz4",   // 压缩类型
                        "cleanup.policy", "delete"   // 清理策略
                ));
    }

    // ==================== 重试主题（自动创建）====================
    // Spring Kafka的@RetryableTopic注解会自动创建重试主题
    // 格式：{原主题名}-retry-{索引}
    // 例如：kline.raw.data-retry-0, kline.raw.data-retry-1, kline.raw.data-retry-2
}
