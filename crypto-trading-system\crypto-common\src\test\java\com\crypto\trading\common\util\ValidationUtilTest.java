package com.crypto.trading.common.util;

import org.junit.jupiter.api.Test;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 验证工具类测试
 * <p>
 * 测试参数验证相关的工具方法
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
class ValidationUtilTest {

    /**
     * 测试空值检查
     */
    @Test
    void testIsEmpty() {
        // 测试null
        assertTrue(ValidationUtil.isEmpty((Object)null));
        
        // 测试字符串
        assertTrue(ValidationUtil.isEmpty(""));
        assertFalse(ValidationUtil.isEmpty("test"));
        
        // 测试集合
        List<String> emptyList = new ArrayList<>();
        List<String> nonEmptyList = new ArrayList<>();
        nonEmptyList.add("item");
        assertTrue(ValidationUtil.isCollectionEmpty(emptyList));
        assertFalse(ValidationUtil.isCollectionEmpty(nonEmptyList));
        
        // 测试Map
        Map<String, Object> emptyMap = new HashMap<>();
        Map<String, Object> nonEmptyMap = new HashMap<>();
        nonEmptyMap.put("key", "value");
        assertTrue(ValidationUtil.isMapEmpty(emptyMap));
        assertFalse(ValidationUtil.isMapEmpty(nonEmptyMap));
        
        // 测试数组
        String[] emptyArray = new String[0];
        String[] nonEmptyArray = new String[]{"item"};
        assertTrue(ValidationUtil.isArrayEmpty(emptyArray));
        assertFalse(ValidationUtil.isArrayEmpty(nonEmptyArray));
    }

    /**
     * 测试空白字符串检查
     */
    @Test
    void testIsBlank() {
        assertTrue(ValidationUtil.isBlank(null));
        assertTrue(ValidationUtil.isBlank(""));
        assertTrue(ValidationUtil.isBlank(" "));
        assertTrue(ValidationUtil.isBlank("\t"));
        assertTrue(ValidationUtil.isBlank("\n"));
        assertFalse(ValidationUtil.isBlank("test"));
        assertFalse(ValidationUtil.isBlank(" test "));
    }

    /**
     * 测试范围检查
     */
    @Test
    void testIsInRange() {
        // 测试整数
        assertTrue(ValidationUtil.isInRange(5, 1, 10));
        assertTrue(ValidationUtil.isInRange(1, 1, 10));
        assertTrue(ValidationUtil.isInRange(10, 1, 10));
        assertFalse(ValidationUtil.isInRange(0, 1, 10));
        assertFalse(ValidationUtil.isInRange(11, 1, 10));
        
        // 测试长整数
        assertTrue(ValidationUtil.isInRange(5L, 1L, 10L));
        assertFalse(ValidationUtil.isInRange(0L, 1L, 10L));
        
        // 测试浮点数
        assertTrue(ValidationUtil.isInRange(5.5, 1.0, 10.0));
        assertFalse(ValidationUtil.isInRange(10.1, 1.0, 10.0));
        
        // 测试BigDecimal
        assertTrue(ValidationUtil.isInRange(new BigDecimal("5.5"), new BigDecimal("1.0"), new BigDecimal("10.0")));
        assertFalse(ValidationUtil.isInRange(new BigDecimal("0.5"), new BigDecimal("1.0"), new BigDecimal("10.0")));
    }

    /**
     * 测试字符串长度范围检查
     */
    @Test
    void testIsLengthInRange() {
        assertTrue(ValidationUtil.isLengthInRange("test", 1, 10));
        assertTrue(ValidationUtil.isLengthInRange("t", 1, 10));
        assertTrue(ValidationUtil.isLengthInRange("1234567890", 1, 10));
        assertFalse(ValidationUtil.isLengthInRange("", 1, 10));
        assertFalse(ValidationUtil.isLengthInRange("12345678901", 1, 10));
        assertFalse(ValidationUtil.isLengthInRange(null, 1, 10));
    }

    /**
     * 测试正则表达式匹配
     */
    @Test
    void testMatches() {
        assertTrue(ValidationUtil.matches("<EMAIL>", ValidationUtil.isEmail("<EMAIL>") ? "^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\\.[a-zA-Z0-9_-]+)+$" : ""));
        assertFalse(ValidationUtil.matches("test", ValidationUtil.isEmail("test") ? "^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\\.[a-zA-Z0-9_-]+)+$" : ""));
        assertFalse(ValidationUtil.matches(null, ".*"));
    }

    /**
     * 测试邮箱格式检查
     */
    @Test
    void testIsEmail() {
        assertTrue(ValidationUtil.isEmail("<EMAIL>"));
        assertTrue(ValidationUtil.isEmail("<EMAIL>"));
        assertTrue(ValidationUtil.isEmail("<EMAIL>"));
        assertFalse(ValidationUtil.isEmail("test"));
        assertFalse(ValidationUtil.isEmail("test@"));
        assertFalse(ValidationUtil.isEmail("@example.com"));
        assertFalse(ValidationUtil.isEmail("test@example"));
        assertFalse(ValidationUtil.isEmail(null));
    }

    /**
     * 测试手机号格式检查
     */
    @Test
    void testIsMobile() {
        assertTrue(ValidationUtil.isMobile("13800138000"));
        assertTrue(ValidationUtil.isMobile("15900000000"));
        assertFalse(ValidationUtil.isMobile("1380013800"));  // 长度不够
        assertFalse(ValidationUtil.isMobile("23800138000")); // 不是1开头
        assertFalse(ValidationUtil.isMobile("12800138000")); // 不是13-19开头
        assertFalse(ValidationUtil.isMobile(null));
    }

    /**
     * 测试数字格式检查
     */
    @Test
    void testNumberFormat() {
        // 测试整数
        assertTrue(ValidationUtil.isInteger("123"));
        assertFalse(ValidationUtil.isInteger("123.45"));
        assertFalse(ValidationUtil.isInteger("abc"));
        assertFalse(ValidationUtil.isInteger(null));
        
        // 测试长整数
        assertTrue(ValidationUtil.isLong("1234567890123456789"));
        assertFalse(ValidationUtil.isLong("1234567890123456789.0"));
        assertFalse(ValidationUtil.isLong("abc"));
        assertFalse(ValidationUtil.isLong(null));
        
        // 测试浮点数
        assertTrue(ValidationUtil.isDouble("123.45"));
        assertTrue(ValidationUtil.isDouble("123"));
        assertFalse(ValidationUtil.isDouble("abc"));
        assertFalse(ValidationUtil.isDouble(null));
        
        // 测试BigDecimal
        assertTrue(ValidationUtil.isBigDecimal("123.45"));
        assertTrue(ValidationUtil.isBigDecimal("123"));
        assertFalse(ValidationUtil.isBigDecimal("abc"));
        assertFalse(ValidationUtil.isBigDecimal(null));
    }

    /**
     * 测试日期格式检查
     */
    @Test
    void testDateFormat() {
        // 测试日期时间格式
        assertTrue(ValidationUtil.isValidDateTime("2023-05-01 12:30:45"));
        assertFalse(ValidationUtil.isValidDateTime("2023-05-32 12:30:45")); // 无效日期
        assertFalse(ValidationUtil.isValidDateTime("2023-05-01 25:30:45")); // 无效时间
        assertFalse(ValidationUtil.isValidDateTime("abc"));
        assertFalse(ValidationUtil.isValidDateTime(null));
        
        // 测试日期格式
        assertTrue(ValidationUtil.isValidDate("2023-05-01"));
        assertFalse(ValidationUtil.isValidDate("2023-05-32")); // 无效日期
        assertFalse(ValidationUtil.isValidDate("abc"));
        assertFalse(ValidationUtil.isValidDate(null));
        
        // 测试时间格式
        assertTrue(ValidationUtil.isValidTime("12:30:45"));
        assertFalse(ValidationUtil.isValidTime("25:30:45")); // 无效时间
        assertFalse(ValidationUtil.isValidTime("abc"));
        assertFalse(ValidationUtil.isValidTime(null));
    }

    /**
     * 测试价格和数量精度检查
     */
    @Test
    void testPriceAndQuantityScale() {
        // 测试价格精度
        assertTrue(ValidationUtil.isValidPriceScale(new BigDecimal("123.45"), 2));
        assertTrue(ValidationUtil.isValidPriceScale(new BigDecimal("123.4"), 2));
        assertTrue(ValidationUtil.isValidPriceScale(new BigDecimal("123"), 2));
        assertFalse(ValidationUtil.isValidPriceScale(new BigDecimal("123.456"), 2));
        assertFalse(ValidationUtil.isValidPriceScale(null, 2));
        
        // 测试数量精度
        assertTrue(ValidationUtil.isValidQuantityScale(new BigDecimal("123.45"), 2));
        assertFalse(ValidationUtil.isValidQuantityScale(new BigDecimal("123.456"), 2));
    }

    /**
     * 测试验证方法
     */
    @Test
    void testValidate() {
        // 测试验证条件
        assertDoesNotThrow(() -> ValidationUtil.validate(true, "不会抛出异常"));
        assertThrows(IllegalArgumentException.class, () -> ValidationUtil.validate(false, "会抛出异常"));
        
        // 测试验证非空
        assertDoesNotThrow(() -> ValidationUtil.validateNotEmpty("test", "不会抛出异常"));
        assertThrows(IllegalArgumentException.class, () -> ValidationUtil.validateNotEmpty("", "会抛出异常"));
        
        // 测试验证非null
        assertDoesNotThrow(() -> ValidationUtil.validateNotNull("test", "不会抛出异常"));
        assertThrows(IllegalArgumentException.class, () -> ValidationUtil.validateNotNull(null, "会抛出异常"));
        
        // 测试验证范围
        assertDoesNotThrow(() -> ValidationUtil.validateInRange(5, 1, 10, "不会抛出异常"));
        assertThrows(IllegalArgumentException.class, () -> ValidationUtil.validateInRange(0, 1, 10, "会抛出异常"));
    }
} 