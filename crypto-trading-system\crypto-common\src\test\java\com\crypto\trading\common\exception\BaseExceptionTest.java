package com.crypto.trading.common.exception;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * BusinessException单元测试类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
class BaseExceptionTest {

    /**
     * 测试使用默认构造函数
     */
    @Test
    void testDefaultConstructor() {
        // 构造异常
        BusinessException exception = new BusinessException("自定义业务异常消息");

        // 验证属性
        assertEquals("BUSINESS_ERROR", exception.getErrorCode());
        assertEquals("自定义业务异常消息", exception.getMessage());
    }

    /**
     * 测试使用错误码和消息构造异常
     */
    @Test
    void testConstructWithCodeAndMessage() {
        // 自定义错误码和消息
        String errorCode = "CUSTOM_ERROR";
        String customMessage = "自定义异常消息";

        // 构造异常
        BusinessException exception = new BusinessException(errorCode, customMessage);

        // 验证属性
        assertEquals(errorCode, exception.getErrorCode());
        assertEquals(customMessage, exception.getMessage());
    }

    /**
     * 测试使用消息和异常原因构造异常
     */
    @Test
    void testConstructWithMessageAndCause() {
        // 自定义消息和异常原因
        String customMessage = "自定义业务异常消息";
        Exception cause = new RuntimeException("原始异常");

        // 构造异常
        BusinessException exception = new BusinessException(customMessage, cause);

        // 验证属性
        assertEquals("BUSINESS_ERROR", exception.getErrorCode());
        assertEquals(customMessage, exception.getMessage());
        assertEquals(cause, exception.getCause());
    }

    /**
     * 测试使用错误码枚举和消息构造异常
     */
    @Test
    void testConstructWithErrorCodeEnumAndMessage() {
        // 自定义消息
        String customMessage = "自定义业务异常消息";

        // 构造异常
        BusinessException exception = new BusinessException(ErrorCode.BUSINESS_ERROR, customMessage);

        // 验证属性
        assertEquals(String.valueOf(ErrorCode.BUSINESS_ERROR.getCode()), exception.getErrorCode());
        assertEquals(customMessage, exception.getMessage());
    }

    /**
     * 测试使用错误码、消息和异常原因构造异常
     */
    @Test
    void testConstructWithCodeMessageAndCause() {
        // 自定义错误码、消息和异常原因
        String errorCode = "CUSTOM_ERROR";
        String customMessage = "自定义异常消息";
        Exception cause = new RuntimeException("原始异常");

        // 构造异常
        BusinessException exception = new BusinessException(errorCode, customMessage, cause);

        // 验证属性
        assertEquals(errorCode, exception.getErrorCode());
        assertEquals(customMessage, exception.getMessage());
        assertEquals(cause, exception.getCause());
    }
    
    /**
     * 测试使用错误码枚举、消息和异常原因构造异常
     */
    @Test
    void testConstructWithErrorCodeEnumMessageAndCause() {
        // 自定义消息和异常原因
        String customMessage = "自定义业务异常消息";
        Exception cause = new RuntimeException("原始异常");

        // 构造异常
        BusinessException exception = new BusinessException(ErrorCode.BUSINESS_ERROR, customMessage, cause);

        // 验证属性
        assertEquals(String.valueOf(ErrorCode.BUSINESS_ERROR.getCode()), exception.getErrorCode());
        assertEquals(customMessage, exception.getMessage());
        assertEquals(cause, exception.getCause());
    }
} 