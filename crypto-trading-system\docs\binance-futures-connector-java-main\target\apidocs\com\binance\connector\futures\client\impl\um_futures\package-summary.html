<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (21) on Fri Apr 04 16:06:08 CST 2025 -->
<title>com.binance.connector.futures.client.impl.um_futures (io.github.binance:binance-futures-connector-java 3.0.5 API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-04-04">
<meta name="description" content="declaration: package: com.binance.connector.futures.client.impl.um_futures">
<meta name="generator" content="javadoc/PackageWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="package-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="切换导航链接"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="../../../../../../../index.html">概览</a></li>
<li class="nav-bar-cell1-rev">程序包</li>
<li>类</li>
<li><a href="package-use.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../../../index-all.html">索引</a></li>
<li><a href="../../../../../../../help-doc.html#package">帮助</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>程序包：</p>
<ul>
<li>说明</li>
<li><a href="#related-package-summary">相关程序包</a></li>
<li><a href="#class-summary">类和接口</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>程序包：&nbsp;</li>
<li>说明&nbsp;|&nbsp;</li>
<li><a href="#related-package-summary">相关程序包</a>&nbsp;|&nbsp;</li>
<li><a href="#class-summary">类和接口</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="搜索">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 title="程序包 com.binance.connector.futures.client.impl.um_futures" class="title">程序包 com.binance.connector.futures.client.impl.um_futures</h1>
</div>
<hr>
<div class="package-signature">package <span class="element-name">com.binance.connector.futures.client.impl.um_futures</span></div>
<section class="summary">
<ul class="summary-list">
<li>
<div id="related-package-summary">
<div class="caption"><span>相关程序包</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">程序包</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color"><a href="../package-summary.html">com.binance.connector.futures.client.impl</a></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><a href="../cm_futures/package-summary.html">com.binance.connector.futures.client.impl.cm_futures</a></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><a href="../futures/package-summary.html">com.binance.connector.futures.client.impl.futures</a></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</div>
</li>
<li>
<div id="class-summary">
<div class="caption"><span>类</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">类</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="UMAccount.html" title="com.binance.connector.futures.client.impl.um_futures中的类">UMAccount</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">USDⓈ-Margined Trade Endpoints</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="UMMarket.html" title="com.binance.connector.futures.client.impl.um_futures中的类">UMMarket</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">USDⓈ-Margined Market Endpoints</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="UMUserData.html" title="com.binance.connector.futures.client.impl.um_futures中的类">UMUserData</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">USDⓈ-Margined User Data Streams Endpoints</div>
</div>
</div>
</div>
</li>
</ul>
</section>
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &#169; 2025. All rights reserved.</small></p>
</footer>
</div>
</div>
</body>
</html>
