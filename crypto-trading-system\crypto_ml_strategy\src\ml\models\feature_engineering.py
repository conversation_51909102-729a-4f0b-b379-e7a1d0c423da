#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
特征工程模块，用于从原始数据计算和提取机器学习所需的特征。
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Union, Any

from indicators.lppl_features import LPPLFeatureExtractor
from ..indicators.hematread_features import HematreadFeatureExtractor
from ..indicators.bull_market_support import BullMarketSupportFeatureExtractor
from ..indicators.super_trend import SuperTrendCalculator
from ..config import Config


class FeatureEngineering:
    """
    特征工程类，负责处理和构建机器学习模型的输入特征。
    综合多种技术指标和市场特征，以提供更准确的预测信号。
    """

    def __init__(self, config: Config):
        """
        初始化特征工程模块。
        
        Args:
            config: 配置对象
        """
        self.logger = logging.getLogger('cryptoMlStrategy.model.feature_engineering')
        self.config = config
        self.strategy_params = config.get_strategy_params()
        
        # 初始化各特征提取器
        self.feature_extractors = {
            'lppl': LPPLFeatureExtractor(self.strategy_params.get('feature_config', {}).get('lppl_features', {}).get('parameters', {})),
            'hematread': HematreadFeatureExtractor(self.strategy_params.get('feature_config', {}).get('hematread_features', {}).get('parameters', {})),
            'bmsb': BullMarketSupportFeatureExtractor(self.strategy_params.get('feature_config', {}).get('bull_market_support_band', {}).get('parameters', {})),
            'supertrend': SuperTrendCalculator(self.strategy_params.get('feature_config', {}).get('super_trend', {}).get('parameters', {}))
        }
        
        # 特征配置
        self.feature_config = self.strategy_params.get('feature_config', {})
        
        # 特征重要性阈值，用于特征选择
        self.feature_importance_threshold = config.getfloat(
            'training', 'feature_importance_threshold', fallback=0.05
        )
        
        # 记录已计算的特征列，用于避免重复计算
        self.calculated_features = set()
        
        self.logger.info("特征工程模块初始化完成")
    
    def extract_all_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        提取所有特征
        
        Args:
            df: 包含OHLCV数据的DataFrame
            
        Returns:
            包含所有特征的DataFrame
        """
        if df is None or df.empty:
            self.logger.warning("输入DataFrame为空，无法提取特征")
            return df
        
        result_df = df.copy()
        
        try:
            # 1. 提取LPPL特征
            if self.feature_config.get('lppl_features', {}).get('enabled', True):
                self.logger.info("提取LPPL特征...")
                result_df = self.feature_extractors['lppl'].extract_features(result_df)
                
            # 2. 提取Hematread特征
            if self.feature_config.get('hematread_features', {}).get('enabled', True):
                self.logger.info("提取Hematread特征...")
                result_df = self.feature_extractors['hematread'].extract_features(result_df)
                
            # 3. 提取Bull Market Support Band特征
            if self.feature_config.get('bull_market_support_band', {}).get('enabled', True):
                self.logger.info("提取Bull Market Support Band特征...")
                result_df = self.feature_extractors['bmsb'].extract_features(result_df)
                
            # 4. 提取SuperTrend特征
            if self.feature_config.get('super_trend', {}).get('enabled', True):
                self.logger.info("提取SuperTrend特征...")
                result_df = self.feature_extractors['supertrend'].extract_features(result_df)
                
            # 5. 提取时间特征
            if self.feature_config.get('time_features', {}).get('enabled', True):
                self.logger.info("提取时间特征...")
                result_df = self._extract_time_features(result_df)
            
            # 更新计算的特征列
            self.calculated_features = set(result_df.columns)
            
            # 6. 特征选择（去除原始价格数据，只保留特征）
            result_df = self._select_features(result_df)
            
            return result_df
            
        except Exception as e:
            self.logger.error(f"特征提取失败: {e}")
            return df
    
    def _extract_time_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        提取时间特征
        
        Args:
            df: 输入DataFrame
            
        Returns:
            添加了时间特征的DataFrame
        """
        result_df = df.copy()
        time_config = self.feature_config.get('time_features', {})
        
        try:
            # 确保有日期时间列
            if 'datetime' not in result_df.columns:
                if 'open_time' in result_df.columns:
                    try:
                        result_df['datetime'] = pd.to_datetime(result_df['open_time'], unit='ms')
                    except:
                        self.logger.warning("无法从open_time转换日期时间")
                        return result_df
                else:
                    self.logger.warning("缺少时间列，无法提取时间特征")
                    return result_df
            
            # 小时特征
            if time_config.get('include_hour_of_day', True):
                result_df['hour_of_day'] = result_df['datetime'].dt.hour
                # 将小时转换为sin和cos特征，以表示周期性
                hours = result_df['hour_of_day']
                result_df['hour_sin'] = np.sin(2 * np.pi * hours / 24.0)
                result_df['hour_cos'] = np.cos(2 * np.pi * hours / 24.0)
            
            # 星期特征
            if time_config.get('include_day_of_week', True):
                result_df['day_of_week'] = result_df['datetime'].dt.dayofweek
                # 将星期转换为sin和cos特征
                days = result_df['day_of_week']
                result_df['day_sin'] = np.sin(2 * np.pi * days / 7.0)
                result_df['day_cos'] = np.cos(2 * np.pi * days / 7.0)
            
            # 月份特征
            if time_config.get('include_month', True):
                result_df['month'] = result_df['datetime'].dt.month
                # 将月份转换为sin和cos特征
                months = result_df['month']
                result_df['month_sin'] = np.sin(2 * np.pi * months / 12.0)
                result_df['month_cos'] = np.cos(2 * np.pi * months / 12.0)
            
            # 波动率特征
            if time_config.get('include_volatility_regime', True) and 'close' in result_df.columns:
                # 计算20日滚动波动率
                result_df['volatility_20d'] = result_df['close'].pct_change().rolling(window=20).std()
                # 波动率区间分类
                result_df['volatility_regime'] = pd.qcut(
                    result_df['volatility_20d'].fillna(0), 
                    q=3, 
                    labels=['low', 'medium', 'high']
                )
                # 将波动率区间转换为one-hot编码
                volatility_dummies = pd.get_dummies(result_df['volatility_regime'], prefix='vol_regime')
                result_df = pd.concat([result_df, volatility_dummies], axis=1)
            
            return result_df
            
        except Exception as e:
            self.logger.error(f"时间特征提取失败: {e}")
            return df
    
    def _select_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        选择特征，去除原始价格数据和中间计算值
        
        Args:
            df: 包含所有特征的DataFrame
            
        Returns:
            仅包含选定特征的DataFrame
        """
        # 需要排除的列类型
        exclude_cols = [
            'open', 'high', 'low', 'close', 'volume',  # 原始OHLCV
            'open_time', 'close_time', 'trades',       # 其他原始数据
            'quote_volume', 'taker_buy_base_volume', 'taker_buy_quote_volume'  # 其他成交量数据
        ]
        
        # 保留时间列，用于后续处理
        keep_cols = ['datetime']
        
        # 选择特征列
        feature_cols = [col for col in df.columns 
                      if col not in exclude_cols or col in keep_cols]
        
        # 返回特征子集
        return df[feature_cols]
    
    def combine_multi_timeframe_features(self, dfs: Dict[str, pd.DataFrame]) -> pd.DataFrame:
        """
        组合多个时间周期的特征
        
        Args:
            dfs: 不同时间周期的DataFrame字典，格式: {timeframe: df}
            
        Returns:
            合并后的特征DataFrame
        """
        if not dfs:
            self.logger.warning("没有提供时间周期数据，无法合并特征")
            return pd.DataFrame()
        
        # 获取多时间周期配置
        mtf_config = self.feature_config.get('multi_timeframe', {})
        if not mtf_config.get('enabled', True):
            # 如果未启用多时间周期特征，则返回第一个DataFrame
            return next(iter(dfs.values()))
        
        # 获取时间周期权重
        timeframes = list(dfs.keys())
        weights = {tf: mtf_config.get('weights', {}).get(tf, 1.0/len(timeframes)) for tf in timeframes}
        
        # 选择基准时间周期（通常是较短的时间周期）
        base_tf = min(timeframes, key=lambda x: self._timeframe_to_minutes(x))
        base_df = dfs[base_tf]
        
        try:
            # 为每个特征列创建加权平均列
            # 首先识别所有特征列
            all_feature_cols = set()
            for tf, df in dfs.items():
                feature_cols = [col for col in df.columns if col != 'datetime']
                all_feature_cols.update(feature_cols)
            
            # 使用基准DataFrame的索引和datetime
            result_df = pd.DataFrame(index=base_df.index)
            if 'datetime' in base_df.columns:
                result_df['datetime'] = base_df['datetime']
            
            # 对每个特征列进行加权平均
            for col in all_feature_cols:
                # 收集所有时间周期中存在此列的数据
                weighted_values = []
                total_weight = 0
                
                for tf, df in dfs.items():
                    if col in df.columns:
                        if len(df) == len(base_df):  # 确保长度一致
                            weighted_values.append(df[col] * weights[tf])
                            total_weight += weights[tf]
                
                # 计算加权平均
                if weighted_values and total_weight > 0:
                    weighted_sum = sum(weighted_values)
                    result_df[col] = weighted_sum / total_weight
            
            return result_df
            
        except Exception as e:
            self.logger.error(f"组合多时间周期特征失败: {e}")
            # 出错时返回基准时间周期的特征
            return base_df
    
    def _timeframe_to_minutes(self, timeframe: str) -> int:
        """
        将时间周期字符串转换为分钟数
        
        Args:
            timeframe: 时间周期字符串，如 '1m', '1h', '1d'
            
        Returns:
            分钟数
        """
        unit = timeframe[-1].lower()
        value = int(timeframe[:-1])
        
        if unit == 'm':
            return value
        elif unit == 'h':
            return value * 60
        elif unit == 'd':
            return value * 60 * 24
        elif unit == 'w':
            return value * 60 * 24 * 7
        else:
            return 0
    
    def normalize_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        标准化特征
        
        Args:
            df: 包含特征的DataFrame
            
        Returns:
            标准化后的DataFrame
        """
        if df is None or df.empty:
            return df
        
        result_df = df.copy()
        
        try:
            # 跳过非数值列和datetime列
            numeric_cols = result_df.select_dtypes(include=['number']).columns
            numeric_cols = [col for col in numeric_cols if col != 'datetime']
            
            # Min-Max标准化到[0,1]区间
            for col in numeric_cols:
                min_val = result_df[col].min()
                max_val = result_df[col].max()
                if max_val > min_val:
                    result_df[col] = (result_df[col] - min_val) / (max_val - min_val)
            
            return result_df
            
        except Exception as e:
            self.logger.error(f"特征标准化失败: {e}")
            return df
    
    def prepare_model_input(self, df: pd.DataFrame, 
                           target_col: Optional[str] = None, 
                           is_training: bool = False) -> Tuple[np.ndarray, Optional[np.ndarray]]:
        """
        准备模型输入数据
        
        Args:
            df: 特征DataFrame
            target_col: 目标列名称（可选）
            is_training: 是否用于训练
            
        Returns:
            模型输入特征数组和目标值数组（如果目标列存在）的元组
        """
        if df is None or df.empty:
            self.logger.warning("输入DataFrame为空，无法准备模型输入")
            return np.array([]), None
        
        try:
            # 移除非特征列
            feature_df = df.copy()
            non_feature_cols = ['datetime']
            if target_col:
                non_feature_cols.append(target_col)
                
            feature_cols = [col for col in feature_df.columns if col not in non_feature_cols]
            
            # 确保所有特征都是数值类型
            for col in feature_cols:
                if feature_df[col].dtype == 'object':
                    self.logger.warning(f"列 {col} 不是数值类型，尝试转换")
                    try:
                        feature_df[col] = pd.to_numeric(feature_df[col])
                    except:
                        self.logger.warning(f"无法将列 {col} 转换为数值类型，将其删除")
                        feature_cols.remove(col)
            
            # 准备特征数组
            X = feature_df[feature_cols].values
            
            # 如果存在目标列，准备目标数组
            y = None
            if target_col and target_col in feature_df.columns:
                y = feature_df[target_col].values
            
            return X, y
            
        except Exception as e:
            self.logger.error(f"准备模型输入失败: {e}")
            return np.array([]), None