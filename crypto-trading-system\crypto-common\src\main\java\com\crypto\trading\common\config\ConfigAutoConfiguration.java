package com.crypto.trading.common.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.PropertySource;
import org.springframework.context.support.PropertySourcesPlaceholderConfigurer;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Primary;

/**
 * 配置自动装配类
 * <p>
 * 用于导入common模块中的配置类，确保系统可以正常使用这些配置。
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Configuration
@Import({
    DatabaseConfig.class,
    KafkaConfig.class,
    LoggingConfig.class
})
@PropertySource(value = "classpath:application-common.yml", factory = YamlPropertySourceFactory.class)
public class ConfigAutoConfiguration implements InitializingBean {
    
    private static final Logger log = LoggerFactory.getLogger(ConfigAutoConfiguration.class);

    /**
     * 创建属性源占位符配置器，用于处理配置文件中的占位符
     *
     * @return PropertySourcesPlaceholderConfigurer 属性源占位符配置器
     */
    @Bean
    public static PropertySourcesPlaceholderConfigurer propertySourcesPlaceholderConfigurer() {
        return new PropertySourcesPlaceholderConfigurer();
    }
    
    /**
     * 为commonDatabaseConfig提供一个databaseConfig的别名
     * 这样测试可以继续使用databaseConfig名称来引用Bean
     *
     * @param databaseConfig 数据库配置Bean
     * @return DatabaseConfig 数据库配置Bean（别名）
     */
    @Bean(name = "databaseConfig")
    public DatabaseConfig databaseConfig(@Qualifier("commonDatabaseConfig") DatabaseConfig databaseConfig) {
        return databaseConfig;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        log.info("配置自动装配完成");
        log.info("已导入common模块配置类: DatabaseConfig, KafkaConfig, LoggingConfig");
    }
}