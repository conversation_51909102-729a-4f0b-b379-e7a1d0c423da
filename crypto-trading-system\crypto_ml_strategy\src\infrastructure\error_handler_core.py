"""
错误处理核心框架模块

提供crypto_ml_strategy项目的核心错误处理基础设施，包括错误分类、
异常定义、错误上下文管理和基础错误处理接口。

Author: Crypto ML Strategy Team
Date: 2024-12-20
Version: 1.0.0
"""

import asyncio
import traceback
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional, Type, Union, Callable
from loguru import logger


class ErrorSeverity(Enum):
    """错误严重程度枚举"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


class ErrorCategory(Enum):
    """错误分类枚举"""
    DATA_ERROR = "data_error"
    MODEL_ERROR = "model_error"
    NETWORK_ERROR = "network_error"
    SYSTEM_ERROR = "system_error"
    CONFIGURATION_ERROR = "configuration_error"
    KAFKA_ERROR = "kafka_error"
    JAVA_API_ERROR = "java_api_error"
    UNKNOWN_ERROR = "unknown_error"


class ErrorCode(Enum):
    """标准化错误代码"""
    # 数据相关错误 (1000-1999)
    DATA_MISSING = 1001
    DATA_CORRUPTED = 1002
    DATA_TIMEOUT = 1003
    DATA_VALIDATION_FAILED = 1004
    
    # 模型相关错误 (2000-2999)
    MODEL_LOAD_FAILED = 2001
    MODEL_INFERENCE_FAILED = 2002
    MODEL_TIMEOUT = 2003
    FEATURE_CALCULATION_FAILED = 2004
    
    # 网络相关错误 (3000-3999)
    NETWORK_CONNECTION_FAILED = 3001
    NETWORK_TIMEOUT = 3002
    API_CALL_FAILED = 3003
    
    # 系统相关错误 (4000-4999)
    MEMORY_INSUFFICIENT = 4001
    DISK_SPACE_INSUFFICIENT = 4002
    CPU_OVERLOAD = 4003
    RESOURCE_EXHAUSTED = 4004
    
    # Kafka相关错误 (5000-5999)
    KAFKA_CONNECTION_FAILED = 5001
    KAFKA_PRODUCER_FAILED = 5002
    KAFKA_CONSUMER_FAILED = 5003
    KAFKA_SERIALIZATION_FAILED = 5004


@dataclass
class ErrorContext:
    """错误上下文信息"""
    timestamp: datetime = field(default_factory=datetime.now)
    component: str = ""
    operation: str = ""
    user_data: Dict[str, Any] = field(default_factory=dict)
    system_state: Dict[str, Any] = field(default_factory=dict)
    stack_trace: str = ""
    correlation_id: Optional[str] = None
    
    def add_context(self, key: str, value: Any) -> None:
        """添加上下文信息"""
        self.user_data[key] = value
    
    def add_system_state(self, key: str, value: Any) -> None:
        """添加系统状态信息"""
        self.system_state[key] = value


class CryptoMLException(Exception):
    """crypto_ml_strategy项目基础异常类"""
    
    def __init__(
        self,
        message: str,
        error_code: ErrorCode,
        severity: ErrorSeverity = ErrorSeverity.ERROR,
        category: ErrorCategory = ErrorCategory.UNKNOWN_ERROR,
        context: Optional[ErrorContext] = None,
        cause: Optional[Exception] = None
    ):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.severity = severity
        self.category = category
        self.context = context or ErrorContext()
        self.cause = cause
        self.timestamp = datetime.now()
        
        # 自动捕获堆栈跟踪
        if not self.context.stack_trace:
            self.context.stack_trace = traceback.format_exc()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "message": self.message,
            "error_code": self.error_code.value,
            "severity": self.severity.value,
            "category": self.category.value,
            "timestamp": self.timestamp.isoformat(),
            "context": {
                "component": self.context.component,
                "operation": self.context.operation,
                "user_data": self.context.user_data,
                "system_state": self.context.system_state,
                "correlation_id": self.context.correlation_id
            },
            "cause": str(self.cause) if self.cause else None
        }


class DataError(CryptoMLException):
    """数据相关错误"""
    def __init__(self, message: str, error_code: ErrorCode, **kwargs):
        super().__init__(
            message, error_code, 
            category=ErrorCategory.DATA_ERROR, 
            **kwargs
        )


class ModelError(CryptoMLException):
    """模型相关错误"""
    def __init__(self, message: str, error_code: ErrorCode, **kwargs):
        super().__init__(
            message, error_code, 
            category=ErrorCategory.MODEL_ERROR, 
            **kwargs
        )


class NetworkError(CryptoMLException):
    """网络相关错误"""
    def __init__(self, message: str, error_code: ErrorCode, **kwargs):
        super().__init__(
            message, error_code, 
            category=ErrorCategory.NETWORK_ERROR, 
            **kwargs
        )


class SystemError(CryptoMLException):
    """系统相关错误"""
    def __init__(self, message: str, error_code: ErrorCode, **kwargs):
        super().__init__(
            message, error_code, 
            category=ErrorCategory.SYSTEM_ERROR, 
            **kwargs
        )


class KafkaError(CryptoMLException):
    """Kafka相关错误"""
    def __init__(self, message: str, error_code: ErrorCode, **kwargs):
        super().__init__(
            message, error_code, 
            category=ErrorCategory.KAFKA_ERROR, 
            **kwargs
        )


class ErrorHandler(ABC):
    """错误处理器抽象基类"""
    
    @abstractmethod
    def can_handle(self, error: Exception) -> bool:
        """判断是否可以处理该错误"""
        pass
    
    @abstractmethod
    async def handle_error(
        self, 
        error: Exception, 
        context: Optional[ErrorContext] = None
    ) -> bool:
        """
        处理错误
        
        Args:
            error: 要处理的错误
            context: 错误上下文
            
        Returns:
            bool: 是否成功处理错误
        """
        pass
    
    @abstractmethod
    def get_priority(self) -> int:
        """获取处理器优先级（数字越小优先级越高）"""
        pass


@dataclass
class ErrorHandlerRegistry:
    """错误处理器注册表"""
    handlers: List[ErrorHandler] = field(default_factory=list)
    
    def register(self, handler: ErrorHandler) -> None:
        """注册错误处理器"""
        self.handlers.append(handler)
        # 按优先级排序
        self.handlers.sort(key=lambda h: h.get_priority())
        logger.info(f"注册错误处理器: {handler.__class__.__name__}")
    
    def unregister(self, handler_type: Type[ErrorHandler]) -> None:
        """注销错误处理器"""
        self.handlers = [h for h in self.handlers if not isinstance(h, handler_type)]
        logger.info(f"注销错误处理器: {handler_type.__name__}")
    
    async def handle_error(
        self, 
        error: Exception, 
        context: Optional[ErrorContext] = None
    ) -> bool:
        """
        使用注册的处理器处理错误
        
        Args:
            error: 要处理的错误
            context: 错误上下文
            
        Returns:
            bool: 是否有处理器成功处理了错误
        """
        for handler in self.handlers:
            if handler.can_handle(error):
                try:
                    success = await handler.handle_error(error, context)
                    if success:
                        logger.info(
                            f"错误已被处理器 {handler.__class__.__name__} 成功处理"
                        )
                        return True
                except Exception as handler_error:
                    logger.error(
                        f"错误处理器 {handler.__class__.__name__} 处理失败: {handler_error}"
                    )
                    continue
        
        logger.warning(f"没有找到合适的错误处理器处理错误: {error}")
        return False


class ErrorMetrics:
    """错误指标收集器"""
    
    def __init__(self):
        self.error_counts: Dict[str, int] = {}
        self.error_rates: Dict[str, float] = {}
        self.last_errors: List[Dict[str, Any]] = []
        self.max_history = 1000
    
    def record_error(self, error: CryptoMLException) -> None:
        """记录错误"""
        error_key = f"{error.category.value}:{error.error_code.value}"
        self.error_counts[error_key] = self.error_counts.get(error_key, 0) + 1
        
        # 记录错误详情
        error_record = {
            "timestamp": error.timestamp.isoformat(),
            "category": error.category.value,
            "code": error.error_code.value,
            "severity": error.severity.value,
            "message": error.message,
            "component": error.context.component
        }
        
        self.last_errors.append(error_record)
        if len(self.last_errors) > self.max_history:
            self.last_errors.pop(0)
    
    def get_error_summary(self) -> Dict[str, Any]:
        """获取错误统计摘要"""
        return {
            "total_errors": sum(self.error_counts.values()),
            "error_counts_by_type": self.error_counts.copy(),
            "recent_errors": self.last_errors[-10:],  # 最近10个错误
            "error_categories": list(set(
                error["category"] for error in self.last_errors
            ))
        }


# 全局错误处理器注册表和指标收集器
global_error_registry = ErrorHandlerRegistry()
global_error_metrics = ErrorMetrics()


def handle_exception(
    component: str = "",
    operation: str = "",
    reraise: bool = True
) -> Callable:
    """
    错误处理装饰器
    
    Args:
        component: 组件名称
        operation: 操作名称
        reraise: 是否重新抛出异常
    """
    def decorator(func: Callable) -> Callable:
        async def async_wrapper(*args, **kwargs):
            context = ErrorContext(component=component, operation=operation)
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                # 转换为标准化异常
                if not isinstance(e, CryptoMLException):
                    e = CryptoMLException(
                        message=str(e),
                        error_code=ErrorCode.UNKNOWN_ERROR,
                        context=context,
                        cause=e
                    )
                
                # 记录错误
                global_error_metrics.record_error(e)
                
                # 尝试处理错误
                handled = await global_error_registry.handle_error(e, context)
                
                if not handled and reraise:
                    raise e
                
                return None
        
        def sync_wrapper(*args, **kwargs):
            context = ErrorContext(component=component, operation=operation)
            try:
                return func(*args, **kwargs)
            except Exception as e:
                # 转换为标准化异常
                if not isinstance(e, CryptoMLException):
                    e = CryptoMLException(
                        message=str(e),
                        error_code=ErrorCode.UNKNOWN_ERROR,
                        context=context,
                        cause=e
                    )
                
                # 记录错误
                global_error_metrics.record_error(e)
                
                # 对于同步函数，创建事件循环处理错误
                try:
                    loop = asyncio.get_event_loop()
                    handled = loop.run_until_complete(
                        global_error_registry.handle_error(e, context)
                    )
                except RuntimeError:
                    # 如果没有事件循环，创建新的
                    handled = asyncio.run(
                        global_error_registry.handle_error(e, context)
                    )
                
                if not handled and reraise:
                    raise e
                
                return None
        
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator