package com.crypto.trading.trade.service.order;

import com.crypto.trading.common.dto.trade.OrderDTO;
import com.crypto.trading.common.dto.trade.OrderRequestDTO;
import com.crypto.trading.common.dto.trade.OrderResponseDTO;
import com.crypto.trading.trade.model.entity.order.OrderEntity;
import org.springframework.beans.BeanUtils;

/**
 * 订单转换器
 * <p>
 * 负责各种订单对象之间的转换
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class OrderConverter {

    /**
     * 将OrderEntity转换为OrderDTO
     *
     * @param entity 订单实体
     * @return 订单DTO
     */
    public static OrderDTO toOrderDTO(OrderEntity entity) {
        if (entity == null) {
            return null;
        }

        OrderDTO dto = new OrderDTO();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }

    /**
     * 将OrderDTO转换为OrderEntity
     *
     * @param dto 订单DTO
     * @return 订单实体
     */
    public static OrderEntity toOrderEntity(OrderDTO dto) {
        if (dto == null) {
            return null;
        }

        OrderEntity entity = new OrderEntity();
        BeanUtils.copyProperties(dto, entity);
        return entity;
    }

    /**
     * 将OrderRequestDTO转换为OrderEntity
     *
     * @param request 订单请求DTO
     * @return 订单实体
     */
    public static OrderEntity toOrderEntity(OrderRequestDTO request) {
        if (request == null) {
            return null;
        }

        OrderEntity entity = new OrderEntity();
        entity.setSymbol(request.getSymbol());
        entity.setSide(request.getSide());
        entity.setPositionSide(request.getPositionSide());
        entity.setType(request.getType());
        entity.setQuantity(request.getQuantity().doubleValue());
        entity.setPrice(request.getPrice() != null ? request.getPrice().doubleValue() : null);
        entity.setClientOrderId(request.getClientOrderId());
        entity.setStrategyId(request.getStrategyId());
        entity.setRemark(request.getRemark());
        return entity;
    }

    /**
     * 将OrderDTO转换为OrderResponseDTO
     *
     * @param dto 订单DTO
     * @return 订单响应DTO
     */
    public static OrderResponseDTO toOrderResponseDTO(OrderDTO dto) {
        if (dto == null) {
            return null;
        }

        OrderResponseDTO response = new OrderResponseDTO();
        BeanUtils.copyProperties(dto, response);
        return response;
    }

    /**
     * 将OrderEntity转换为OrderResponseDTO
     *
     * @param entity 订单实体
     * @return 订单响应DTO
     */
    public static OrderResponseDTO toOrderResponseDTO(OrderEntity entity) {
        if (entity == null) {
            return null;
        }

        return toOrderResponseDTO(toOrderDTO(entity));
    }

    /**
     * 更新OrderEntity
     *
     * @param source 源订单实体
     * @param target 目标订单实体
     */
    public static void updateOrderEntity(OrderEntity source, OrderEntity target) {
        if (source == null || target == null) {
            return;
        }

        // 只更新非空字段
        if (source.getStatus() != null) {
            target.setStatus(source.getStatus());
        }
        if (source.getExecutedQuantity() != null) {
            target.setExecutedQuantity(source.getExecutedQuantity());
        }
        if (source.getExecutedPrice() != null) {
            target.setExecutedPrice(source.getExecutedPrice());
        }
        if (source.getErrorCode() != null) {
            target.setErrorCode(source.getErrorCode());
        }
        if (source.getErrorMessage() != null) {
            target.setErrorMessage(source.getErrorMessage());
        }
        if (source.getRemark() != null) {
            target.setRemark(source.getRemark());
        }
        target.setUpdatedTime(System.currentTimeMillis());
    }
}