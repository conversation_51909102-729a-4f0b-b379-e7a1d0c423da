package com.crypto.trading.common.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.*;
import java.nio.file.attribute.BasicFileAttributes;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;
import java.util.zip.ZipOutputStream;

/**
 * 文件工具类
 * <p>
 * 提供文件操作相关的工具方法，如读写、复制、删除等
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public final class FileUtil {
    private static final Logger log = LoggerFactory.getLogger(FileUtil.class);

    /**
     * 默认缓冲区大小
     */
    private static final int DEFAULT_BUFFER_SIZE = 8192;

    /**
     * 私有构造函数，防止实例化
     */
    private FileUtil() {
        throw new IllegalStateException("工具类不允许实例化");
    }

    /**
     * 读取文件内容为字符串
     *
     * @param filePath 文件路径
     * @return 文件内容
     * @throws IOException IO异常
     */
    public static String readToString(String filePath) throws IOException {
        return readToString(Paths.get(filePath));
    }

    /**
     * 读取文件内容为字符串
     *
     * @param file 文件对象
     * @return 文件内容
     * @throws IOException IO异常
     */
    public static String readToString(File file) throws IOException {
        return readToString(file.toPath());
    }

    /**
     * 读取文件内容为字符串
     *
     * @param path 文件路径
     * @return 文件内容
     * @throws IOException IO异常
     */
    public static String readToString(Path path) throws IOException {
        return Files.readString(path, StandardCharsets.UTF_8);
    }

    /**
     * 读取文件内容为字节数组
     *
     * @param filePath 文件路径
     * @return 文件内容
     * @throws IOException IO异常
     */
    public static byte[] readToByteArray(String filePath) throws IOException {
        return readToByteArray(Paths.get(filePath));
    }

    /**
     * 读取文件内容为字节数组
     *
     * @param file 文件对象
     * @return 文件内容
     * @throws IOException IO异常
     */
    public static byte[] readToByteArray(File file) throws IOException {
        return readToByteArray(file.toPath());
    }

    /**
     * 读取文件内容为字节数组
     *
     * @param path 文件路径
     * @return 文件内容
     * @throws IOException IO异常
     */
    public static byte[] readToByteArray(Path path) throws IOException {
        return Files.readAllBytes(path);
    }

    /**
     * 读取文件内容为行列表
     *
     * @param filePath 文件路径
     * @return 行列表
     * @throws IOException IO异常
     */
    public static List<String> readLines(String filePath) throws IOException {
        return readLines(Paths.get(filePath));
    }

    /**
     * 读取文件内容为行列表
     *
     * @param file 文件对象
     * @return 行列表
     * @throws IOException IO异常
     */
    public static List<String> readLines(File file) throws IOException {
        return readLines(file.toPath());
    }

    /**
     * 读取文件内容为行列表
     *
     * @param path 文件路径
     * @return 行列表
     * @throws IOException IO异常
     */
    public static List<String> readLines(Path path) throws IOException {
        return Files.readAllLines(path, StandardCharsets.UTF_8);
    }

    /**
     * 写入字符串到文件
     *
     * @param content  内容
     * @param filePath 文件路径
     * @throws IOException IO异常
     */
    public static void writeString(String content, String filePath) throws IOException {
        writeString(content, Paths.get(filePath));
    }

    /**
     * 写入字符串到文件
     *
     * @param content 内容
     * @param file    文件对象
     * @throws IOException IO异常
     */
    public static void writeString(String content, File file) throws IOException {
        writeString(content, file.toPath());
    }

    /**
     * 写入字符串到文件
     *
     * @param content 内容
     * @param path    文件路径
     * @throws IOException IO异常
     */
    public static void writeString(String content, Path path) throws IOException {
        Files.writeString(path, content, StandardCharsets.UTF_8);
    }

    /**
     * 写入字节数组到文件
     *
     * @param data     数据
     * @param filePath 文件路径
     * @throws IOException IO异常
     */
    public static void writeBytes(byte[] data, String filePath) throws IOException {
        writeBytes(data, Paths.get(filePath));
    }

    /**
     * 写入字节数组到文件
     *
     * @param data 数据
     * @param file 文件对象
     * @throws IOException IO异常
     */
    public static void writeBytes(byte[] data, File file) throws IOException {
        writeBytes(data, file.toPath());
    }

    /**
     * 写入字节数组到文件
     *
     * @param data 数据
     * @param path 文件路径
     * @throws IOException IO异常
     */
    public static void writeBytes(byte[] data, Path path) throws IOException {
        Files.write(path, data);
    }

    /**
     * 写入行列表到文件
     *
     * @param lines    行列表
     * @param filePath 文件路径
     * @throws IOException IO异常
     */
    public static void writeLines(List<String> lines, String filePath) throws IOException {
        writeLines(lines, Paths.get(filePath));
    }

    /**
     * 写入行列表到文件
     *
     * @param lines 行列表
     * @param file  文件对象
     * @throws IOException IO异常
     */
    public static void writeLines(List<String> lines, File file) throws IOException {
        writeLines(lines, file.toPath());
    }

    /**
     * 写入行列表到文件
     *
     * @param lines 行列表
     * @param path  文件路径
     * @throws IOException IO异常
     */
    public static void writeLines(List<String> lines, Path path) throws IOException {
        Files.write(path, lines, StandardCharsets.UTF_8);
    }

    /**
     * 追加字符串到文件
     *
     * @param content  内容
     * @param filePath 文件路径
     * @throws IOException IO异常
     */
    public static void appendString(String content, String filePath) throws IOException {
        appendString(content, Paths.get(filePath));
    }

    /**
     * 追加字符串到文件
     *
     * @param content 内容
     * @param file    文件对象
     * @throws IOException IO异常
     */
    public static void appendString(String content, File file) throws IOException {
        appendString(content, file.toPath());
    }

    /**
     * 追加字符串到文件
     *
     * @param content 内容
     * @param path    文件路径
     * @throws IOException IO异常
     */
    public static void appendString(String content, Path path) throws IOException {
        Files.writeString(path, content, StandardCharsets.UTF_8, StandardOpenOption.CREATE, StandardOpenOption.APPEND);
    }

    /**
     * 复制文件
     *
     * @param sourcePath 源文件路径
     * @param targetPath 目标文件路径
     * @throws IOException IO异常
     */
    public static void copy(String sourcePath, String targetPath) throws IOException {
        copy(Paths.get(sourcePath), Paths.get(targetPath));
    }

    /**
     * 复制文件
     *
     * @param sourceFile 源文件
     * @param targetFile 目标文件
     * @throws IOException IO异常
     */
    public static void copy(File sourceFile, File targetFile) throws IOException {
        copy(sourceFile.toPath(), targetFile.toPath());
    }

    /**
     * 复制文件
     *
     * @param source 源文件路径
     * @param target 目标文件路径
     * @throws IOException IO异常
     */
    public static void copy(Path source, Path target) throws IOException {
        Files.copy(source, target, StandardCopyOption.REPLACE_EXISTING);
    }

    /**
     * 移动文件
     *
     * @param sourcePath 源文件路径
     * @param targetPath 目标文件路径
     * @throws IOException IO异常
     */
    public static void move(String sourcePath, String targetPath) throws IOException {
        move(Paths.get(sourcePath), Paths.get(targetPath));
    }

    /**
     * 移动文件
     *
     * @param sourceFile 源文件
     * @param targetFile 目标文件
     * @throws IOException IO异常
     */
    public static void move(File sourceFile, File targetFile) throws IOException {
        move(sourceFile.toPath(), targetFile.toPath());
    }

    /**
     * 移动文件
     *
     * @param source 源文件路径
     * @param target 目标文件路径
     * @throws IOException IO异常
     */
    public static void move(Path source, Path target) throws IOException {
        Files.move(source, target, StandardCopyOption.REPLACE_EXISTING);
    }

    /**
     * 删除文件
     *
     * @param filePath 文件路径
     * @throws IOException IO异常
     */
    public static void delete(String filePath) throws IOException {
        delete(Paths.get(filePath));
    }

    /**
     * 删除文件
     *
     * @param file 文件对象
     * @throws IOException IO异常
     */
    public static void delete(File file) throws IOException {
        delete(file.toPath());
    }

    /**
     * 删除文件
     *
     * @param path 文件路径
     * @throws IOException IO异常
     */
    public static void delete(Path path) throws IOException {
        Files.delete(path);
    }

    /**
     * 创建目录
     *
     * @param dirPath 目录路径
     * @throws IOException IO异常
     */
    public static void createDirectory(String dirPath) throws IOException {
        createDirectory(Paths.get(dirPath));
    }

    /**
     * 创建目录
     *
     * @param dir 目录对象
     * @throws IOException IO异常
     */
    public static void createDirectory(File dir) throws IOException {
        createDirectory(dir.toPath());
    }

    /**
     * 创建目录
     *
     * @param dir 目录路径
     * @throws IOException IO异常
     */
    public static void createDirectory(Path dir) throws IOException {
        Files.createDirectories(dir);
    }

    /**
     * 获取文件大小
     *
     * @param filePath 文件路径
     * @return 文件大小（字节）
     * @throws IOException IO异常
     */
    public static long size(String filePath) throws IOException {
        return size(Paths.get(filePath));
    }

    /**
     * 获取文件大小
     *
     * @param file 文件对象
     * @return 文件大小（字节）
     * @throws IOException IO异常
     */
    public static long size(File file) throws IOException {
        return size(file.toPath());
    }

    /**
     * 获取文件大小
     *
     * @param path 文件路径
     * @return 文件大小（字节）
     * @throws IOException IO异常
     */
    public static long size(Path path) throws IOException {
        return Files.size(path);
    }

    /**
     * 文件是否存在
     *
     * @param filePath 文件路径
     * @return 是否存在
     */
    public static boolean exists(String filePath) {
        return exists(Paths.get(filePath));
    }

    /**
     * 文件是否存在
     *
     * @param file 文件对象
     * @return 是否存在
     */
    public static boolean exists(File file) {
        return exists(file.toPath());
    }

    /**
     * 文件是否存在
     *
     * @param path 文件路径
     * @return 是否存在
     */
    public static boolean exists(Path path) {
        return Files.exists(path);
    }

    /**
     * 是否为目录
     *
     * @param path 路径
     * @return 是否为目录
     */
    public static boolean isDirectory(String path) {
        return isDirectory(Paths.get(path));
    }

    /**
     * 是否为目录
     *
     * @param file 文件对象
     * @return 是否为目录
     */
    public static boolean isDirectory(File file) {
        return isDirectory(file.toPath());
    }

    /**
     * 是否为目录
     *
     * @param path 路径
     * @return 是否为目录
     */
    public static boolean isDirectory(Path path) {
        return Files.isDirectory(path);
    }

    /**
     * 是否为普通文件
     *
     * @param path 路径
     * @return 是否为普通文件
     */
    public static boolean isRegularFile(String path) {
        return isRegularFile(Paths.get(path));
    }

    /**
     * 是否为普通文件
     *
     * @param file 文件对象
     * @return 是否为普通文件
     */
    public static boolean isRegularFile(File file) {
        return isRegularFile(file.toPath());
    }

    /**
     * 是否为普通文件
     *
     * @param path 路径
     * @return 是否为普通文件
     */
    public static boolean isRegularFile(Path path) {
        return Files.isRegularFile(path);
    }

    /**
     * 获取文件名（不含路径）
     *
     * @param path 路径
     * @return 文件名
     */
    public static String getFileName(String path) {
        return Paths.get(path).getFileName().toString();
    }

    /**
     * 获取文件扩展名
     *
     * @param fileName 文件名
     * @return 扩展名
     */
    public static String getFileExtension(String fileName) {
        int dotIndex = fileName.lastIndexOf('.');
        return (dotIndex == -1) ? "" : fileName.substring(dotIndex + 1);
    }

    /**
     * 获取文件名（不含扩展名）
     *
     * @param fileName 文件名
     * @return 文件名（不含扩展名）
     */
    public static String getFileNameWithoutExtension(String fileName) {
        int dotIndex = fileName.lastIndexOf('.');
        return (dotIndex == -1) ? fileName : fileName.substring(0, dotIndex);
    }

    /**
     * 列出目录下的文件
     *
     * @param dirPath 目录路径
     * @return 文件列表
     * @throws IOException IO异常
     */
    public static List<Path> listFiles(String dirPath) throws IOException {
        return listFiles(Paths.get(dirPath));
    }

    /**
     * 列出目录下的文件
     *
     * @param dir 目录对象
     * @return 文件列表
     * @throws IOException IO异常
     */
    public static List<Path> listFiles(File dir) throws IOException {
        return listFiles(dir.toPath());
    }

    /**
     * 列出目录下的文件
     *
     * @param dir 目录路径
     * @return 文件列表
     * @throws IOException IO异常
     */
    public static List<Path> listFiles(Path dir) throws IOException {
        try (Stream<Path> stream = Files.list(dir)) {
            return stream.collect(Collectors.toList());
        }
    }

    /**
     * 递归列出目录下的所有文件
     *
     * @param dirPath 目录路径
     * @return 文件列表
     * @throws IOException IO异常
     */
    public static List<Path> listFilesRecursively(String dirPath) throws IOException {
        return listFilesRecursively(Paths.get(dirPath));
    }

    /**
     * 递归列出目录下的所有文件
     *
     * @param dir 目录对象
     * @return 文件列表
     * @throws IOException IO异常
     */
    public static List<Path> listFilesRecursively(File dir) throws IOException {
        return listFilesRecursively(dir.toPath());
    }

    /**
     * 递归列出目录下的所有文件
     *
     * @param dir 目录路径
     * @return 文件列表
     * @throws IOException IO异常
     */
    public static List<Path> listFilesRecursively(Path dir) throws IOException {
        List<Path> result = new ArrayList<>();
        Files.walkFileTree(dir, new SimpleFileVisitor<>() {
            @Override
            public FileVisitResult visitFile(Path file, BasicFileAttributes attrs) {
                result.add(file);
                return FileVisitResult.CONTINUE;
            }
        });
        return result;
    }

    /**
     * 压缩文件或目录
     *
     * @param sourcePath 源文件或目录路径
     * @param zipPath    目标压缩文件路径
     * @throws IOException IO异常
     */
    public static void zip(String sourcePath, String zipPath) throws IOException {
        zip(Paths.get(sourcePath), Paths.get(zipPath));
    }

    /**
     * 压缩文件或目录
     *
     * @param sourceFile 源文件或目录
     * @param zipFile    目标压缩文件
     * @throws IOException IO异常
     */
    public static void zip(File sourceFile, File zipFile) throws IOException {
        zip(sourceFile.toPath(), zipFile.toPath());
    }

    /**
     * 压缩文件或目录
     *
     * @param source 源文件或目录路径
     * @param zip    目标压缩文件路径
     * @throws IOException IO异常
     */
    public static void zip(Path source, Path zip) throws IOException {
        try (ZipOutputStream zos = new ZipOutputStream(Files.newOutputStream(zip))) {
            if (Files.isDirectory(source)) {
                Files.walkFileTree(source, new SimpleFileVisitor<>() {
                    @Override
                    public FileVisitResult visitFile(Path file, BasicFileAttributes attrs) throws IOException {
                        String relativePath = source.relativize(file).toString().replace('\\', '/');
                        ZipEntry zipEntry = new ZipEntry(relativePath);
                        zos.putNextEntry(zipEntry);
                        Files.copy(file, zos);
                        zos.closeEntry();
                        return FileVisitResult.CONTINUE;
                    }
                });
            } else {
                ZipEntry zipEntry = new ZipEntry(source.getFileName().toString());
                zos.putNextEntry(zipEntry);
                Files.copy(source, zos);
                zos.closeEntry();
            }
        }
    }

    /**
     * 解压缩文件
     *
     * @param zipPath       压缩文件路径
     * @param extractPath   解压目标路径
     * @throws IOException IO异常
     */
    public static void unzip(String zipPath, String extractPath) throws IOException {
        unzip(Paths.get(zipPath), Paths.get(extractPath));
    }

    /**
     * 解压缩文件
     *
     * @param zipFile       压缩文件
     * @param extractDir    解压目标目录
     * @throws IOException IO异常
     */
    public static void unzip(File zipFile, File extractDir) throws IOException {
        unzip(zipFile.toPath(), extractDir.toPath());
    }

    /**
     * 解压缩文件
     *
     * @param zip         压缩文件路径
     * @param extractDir  解压目标目录路径
     * @throws IOException IO异常
     */
    public static void unzip(Path zip, Path extractDir) throws IOException {
        Files.createDirectories(extractDir);
        
        try (ZipInputStream zis = new ZipInputStream(Files.newInputStream(zip))) {
            ZipEntry zipEntry;
            byte[] buffer = new byte[DEFAULT_BUFFER_SIZE];
            
            while ((zipEntry = zis.getNextEntry()) != null) {
                Path entryPath = extractDir.resolve(zipEntry.getName());
                
                // 创建父目录
                Files.createDirectories(entryPath.getParent());
                
                if (!zipEntry.isDirectory()) {
                    try (OutputStream os = Files.newOutputStream(entryPath)) {
                        int len;
                        while ((len = zis.read(buffer)) > 0) {
                            os.write(buffer, 0, len);
                        }
                    }
                }
                
                zis.closeEntry();
            }
        }
    }
} 