[DEFAULT]
# 默认配置
log_level = INFO
environment = development

[kafka]
# Kafka相关配置
bootstrap_servers = localhost:29092
kline_topic = kline.data
depth_topic = depth.data
trade_topic = trade.data
signal_topic = strategy.signal
group_id = crypto_ml_strategy
auto_offset_reset = earliest
enable_auto_commit = True
auto_commit_interval_ms = 5000
max_poll_records = 500
session_timeout_ms = 30000

[influxdb]
# InfluxDB相关配置
url = http://localhost:8086
token = 1mLmSyBqUYH5OKaSkFdybeS34Z6DyLlR3H3d802mmVvFVCiJ4GwOQzyT8QULaES96tBfPGI9ycDHkseYvPhGoA==
org = crypto
bucket = market_data
kline_measurement = kline
depth_measurement = depth
trade_measurement = trade

[mysql]
# MySQL相关配置
host = localhost
port = 3306
user = root
password = root
db = crypto_trading
charset = utf8mb4

[strategy]
# 策略相关配置
strategy_id = unified_ml_strategy
symbols = BTCUSDT,ETHUSDT
timeframes = 1m,5m,15m,1h,4h,1d
confidence_threshold = 0.75
risk_threshold = 0.5
parameters_file = strategy_params.json
model_file = unified_ml_model.pkl

[training]
# 训练相关配置
train_data_days = 90
validation_ratio = 0.2
batch_size = 64
epochs = 100
learning_rate = 0.001
early_stopping_patience = 10
model_save_path = models/
use_gpu = True
distillation_temperature = 2.0
online_learning_rate = 0.0001
feature_importance_threshold = 0.05

[deepseek]
# DeepSeek模型配置
api_key = ***********************************
api_base_url = https://api.deepseek.com/v1
model_name = deepseek-chat
max_tokens = 4096
temperature = 0.7
top_p = 0.9
frequency_penalty = 0.0
presence_penalty = 0.0
timeout = 30
max_retries = 3
enable_local_distillation = True
local_model_path = models/deepseek_distilled.pkl
teacher_model_path = models/deepseek_teacher.pth
student_model_path = models/deepseek_student.pth
distillation_config_path = config/deepseek_distillation.json

[deepseek_distillation]
# DeepSeek知识蒸馏配置
teacher_hidden_dims = 512,256,128,64
student_hidden_dims = 128,64,32
teacher_dropout = 0.3
student_dropout = 0.2
distillation_temperature = 4.0
alpha = 0.7
beta = 0.3
feature_loss_weight = 0.1
compression_target_ratio = 4.0
enable_attention_transfer = True
enable_feature_matching = True

[system]
# 系统配置
threads = 8
memory_limit_mb = 4096
log_file = logs/crypto_ml_strategy.log
log_config = logging.conf
max_retry_attempts = 3
retry_delay_seconds = 5
health_check_interval = 60