#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
MySQL客户端模块

提供MySQL数据库的连接和操作功能，支持关系型数据的存储和查询。
主要用于存储配置信息、模型元数据、交易记录等结构化数据。
"""

import logging
from typing import Dict, List, Optional, Any, Union, Tuple
from datetime import datetime
import pandas as pd
from loguru import logger

try:
    import pymysql
    from sqlalchemy import create_engine, text, MetaData, Table, Column, Integer, String, DateTime, Float, Text
    from sqlalchemy.orm import sessionmaker
    from sqlalchemy.exc import SQLAlchemyError
    from sqlalchemy.pool import QueuePool
except ImportError:
    logger.warning("pymysql or sqlalchemy not installed, MySQL functionality will be limited")
    pymysql = None
    create_engine = None
    text = None
    MetaData = None
    Table = None
    Column = None
    Integer = None
    String = None
    DateTime = None
    Float = None
    Text = None
    sessionmaker = None
    SQLAlchemyError = Exception
    QueuePool = None


class MySQLClient:
    """
    MySQL客户端类，提供关系型数据库的连接和操作功能。
    
    支持功能：
    - 连接池管理和自动重连
    - SQL查询和事务处理
    - 模型元数据存储
    - 配置信息管理
    - 批量数据操作
    - 数据库迁移支持
    """
    
    def __init__(self, host: str, port: int, user: str, password: str, database: str, 
                 charset: str = 'utf8mb4', pool_size: int = 10, max_overflow: int = 20):
        """
        初始化MySQL客户端
        
        Args:
            host: 数据库主机地址
            port: 数据库端口
            user: 用户名
            password: 密码
            database: 数据库名
            charset: 字符集，默认utf8mb4
            pool_size: 连接池大小
            max_overflow: 最大溢出连接数
        
        Raises:
            ConnectionError: 连接失败时抛出
            ValueError: 参数无效时抛出
        """
        self.logger = logger.bind(component="MySQLClient")
        
        if not all([host, port, user, password, database]):
            raise ValueError("MySQL连接参数不能为空")
        
        self.host = host
        self.port = port
        self.user = user
        self.password = password
        self.database = database
        self.charset = charset
        
        # 构建连接字符串
        self.connection_string = (
            f"mysql+pymysql://{user}:{password}@{host}:{port}/{database}"
            f"?charset={charset}&autocommit=true"
        )
        
        # 初始化连接引擎
        self._engine = None
        self._session_factory = None
        self._metadata = None
        self._connected = False
        
        # 建立连接
        self._connect(pool_size, max_overflow)
        
        # 初始化数据库表
        self._init_tables()
        
        self.logger.info(f"MySQL客户端初始化完成: {host}:{port}/{database}")
    
    def _connect(self, pool_size: int, max_overflow: int) -> None:
        """建立数据库连接"""
        try:
            if create_engine is None:
                raise ImportError("sqlalchemy package not available")
            
            self._engine = create_engine(
                self.connection_string,
                poolclass=QueuePool,
                pool_size=pool_size,
                max_overflow=max_overflow,
                pool_pre_ping=True,  # 连接前检查
                pool_recycle=3600,   # 1小时回收连接
                echo=False  # 设为True可以看到SQL语句
            )
            
            # 测试连接
            with self._engine.connect() as conn:
                conn.execute(text("SELECT 1"))
            
            # 创建会话工厂
            self._session_factory = sessionmaker(bind=self._engine)
            self._metadata = MetaData()
            
            self._connected = True
            self.logger.info("MySQL连接建立成功")
            
        except Exception as e:
            self.logger.error(f"MySQL连接失败: {str(e)}")
            self._connected = False
            raise ConnectionError(f"无法连接到MySQL: {str(e)}")
    
    def _init_tables(self) -> None:
        """初始化数据库表结构"""
        try:
            if not self._connected:
                return
            
            # 模型版本表
            self.model_versions_table = Table(
                'model_versions', self._metadata,
                Column('id', Integer, primary_key=True, autoincrement=True),
                Column('version', String(50), nullable=False, unique=True),
                Column('model_type', String(50), nullable=False),
                Column('model_path', String(255), nullable=False),
                Column('performance_metrics', Text),
                Column('training_config', Text),
                Column('is_active', Integer, default=0),
                Column('created_at', DateTime, default=datetime.now),
                Column('updated_at', DateTime, default=datetime.now, onupdate=datetime.now)
            )
            
            # 策略配置表
            self.strategy_configs_table = Table(
                'strategy_configs', self._metadata,
                Column('id', Integer, primary_key=True, autoincrement=True),
                Column('config_key', String(100), nullable=False, unique=True),
                Column('config_value', Text),
                Column('config_type', String(20), default='string'),
                Column('description', String(255)),
                Column('created_at', DateTime, default=datetime.now),
                Column('updated_at', DateTime, default=datetime.now, onupdate=datetime.now)
            )
            
            # 交易信号表
            self.trading_signals_table = Table(
                'trading_signals', self._metadata,
                Column('id', Integer, primary_key=True, autoincrement=True),
                Column('signal_id', String(50), nullable=False, unique=True),
                Column('symbol', String(20), nullable=False),
                Column('signal_type', String(10), nullable=False),  # BUY, SELL, HOLD
                Column('signal_strength', Float, nullable=False),
                Column('confidence', Float, nullable=False),
                Column('price', Float),
                Column('volume', Float),
                Column('model_version', String(50)),
                Column('features', Text),
                Column('created_at', DateTime, default=datetime.now)
            )
            
            # 创建表
            self._metadata.create_all(self._engine)
            self.logger.info("数据库表初始化完成")
            
        except Exception as e:
            self.logger.error(f"数据库表初始化失败: {str(e)}")
    
    def is_connected(self) -> bool:
        """检查连接状态"""
        if not self._connected or not self._engine:
            return False
        
        try:
            with self._engine.connect() as conn:
                conn.execute(text("SELECT 1"))
            return True
        except Exception:
            self._connected = False
            return False
    
    def execute_query(self, query: str, params: Optional[Dict] = None) -> Optional[List[Dict]]:
        """
        执行查询语句
        
        Args:
            query: SQL查询语句
            params: 查询参数
        
        Returns:
            查询结果列表，失败返回None
        """
        try:
            if not self.is_connected():
                raise ConnectionError("数据库连接已断开")
            
            with self._engine.connect() as conn:
                result = conn.execute(text(query), params or {})
                
                if result.returns_rows:
                    columns = result.keys()
                    rows = result.fetchall()
                    return [dict(zip(columns, row)) for row in rows]
                else:
                    return []
            
        except Exception as e:
            self.logger.error(f"查询执行失败: {str(e)}")
            return None
    
    def execute_update(self, query: str, params: Optional[Dict] = None) -> bool:
        """
        执行更新语句
        
        Args:
            query: SQL更新语句
            params: 更新参数
        
        Returns:
            执行是否成功
        """
        try:
            if not self.is_connected():
                raise ConnectionError("数据库连接已断开")
            
            with self._engine.connect() as conn:
                with conn.begin():  # 事务处理
                    conn.execute(text(query), params or {})
            
            return True
            
        except Exception as e:
            self.logger.error(f"更新执行失败: {str(e)}")
            return False
    
    def save_model_version(self, version: str, model_type: str, model_path: str,
                          performance_metrics: Dict, training_config: Dict,
                          is_active: bool = False) -> bool:
        """
        保存模型版本信息
        
        Args:
            version: 模型版本号
            model_type: 模型类型
            model_path: 模型文件路径
            performance_metrics: 性能指标
            training_config: 训练配置
            is_active: 是否为活跃版本
        
        Returns:
            保存是否成功
        """
        try:
            import json
            
            # 如果设置为活跃版本，先将其他版本设为非活跃
            if is_active:
                self.execute_update(
                    "UPDATE model_versions SET is_active = 0 WHERE model_type = :model_type",
                    {"model_type": model_type}
                )
            
            # 插入新版本
            query = """
                INSERT INTO model_versions 
                (version, model_type, model_path, performance_metrics, training_config, is_active)
                VALUES (:version, :model_type, :model_path, :performance_metrics, :training_config, :is_active)
                ON DUPLICATE KEY UPDATE
                model_path = VALUES(model_path),
                performance_metrics = VALUES(performance_metrics),
                training_config = VALUES(training_config),
                is_active = VALUES(is_active),
                updated_at = NOW()
            """
            
            params = {
                "version": version,
                "model_type": model_type,
                "model_path": model_path,
                "performance_metrics": json.dumps(performance_metrics),
                "training_config": json.dumps(training_config),
                "is_active": 1 if is_active else 0
            }
            
            return self.execute_update(query, params)
            
        except Exception as e:
            self.logger.error(f"保存模型版本失败: {str(e)}")
            return False
    
    def get_active_model_version(self, model_type: str) -> Optional[Dict]:
        """
        获取活跃的模型版本
        
        Args:
            model_type: 模型类型
        
        Returns:
            模型版本信息字典
        """
        try:
            query = """
                SELECT * FROM model_versions 
                WHERE model_type = :model_type AND is_active = 1
                ORDER BY created_at DESC LIMIT 1
            """
            
            result = self.execute_query(query, {"model_type": model_type})
            
            if result and len(result) > 0:
                import json
                model_info = result[0]
                
                # 解析JSON字段
                if model_info.get('performance_metrics'):
                    model_info['performance_metrics'] = json.loads(model_info['performance_metrics'])
                if model_info.get('training_config'):
                    model_info['training_config'] = json.loads(model_info['training_config'])
                
                return model_info
            
            return None
            
        except Exception as e:
            self.logger.error(f"获取活跃模型版本失败: {str(e)}")
            return None
    
    def save_trading_signal(self, signal_data: Dict) -> bool:
        """
        保存交易信号
        
        Args:
            signal_data: 信号数据字典
        
        Returns:
            保存是否成功
        """
        try:
            import json
            
            query = """
                INSERT INTO trading_signals 
                (signal_id, symbol, signal_type, signal_strength, confidence, price, volume, model_version, features)
                VALUES (:signal_id, :symbol, :signal_type, :signal_strength, :confidence, :price, :volume, :model_version, :features)
            """
            
            params = {
                "signal_id": signal_data.get("signal_id"),
                "symbol": signal_data.get("symbol"),
                "signal_type": signal_data.get("signal_type"),
                "signal_strength": signal_data.get("signal_strength"),
                "confidence": signal_data.get("confidence"),
                "price": signal_data.get("price"),
                "volume": signal_data.get("volume"),
                "model_version": signal_data.get("model_version"),
                "features": json.dumps(signal_data.get("features", {}))
            }
            
            return self.execute_update(query, params)
            
        except Exception as e:
            self.logger.error(f"保存交易信号失败: {str(e)}")
            return False
    
    def get_config_value(self, config_key: str, default_value: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            config_key: 配置键
            default_value: 默认值
        
        Returns:
            配置值
        """
        try:
            query = "SELECT config_value, config_type FROM strategy_configs WHERE config_key = :config_key"
            result = self.execute_query(query, {"config_key": config_key})
            
            if result and len(result) > 0:
                config_value = result[0]['config_value']
                config_type = result[0]['config_type']
                
                # 根据类型转换值
                if config_type == 'int':
                    return int(config_value)
                elif config_type == 'float':
                    return float(config_value)
                elif config_type == 'bool':
                    return config_value.lower() in ('true', '1', 'yes')
                elif config_type == 'json':
                    import json
                    return json.loads(config_value)
                else:
                    return config_value
            
            return default_value
            
        except Exception as e:
            self.logger.error(f"获取配置值失败: {str(e)}")
            return default_value
    
    def set_config_value(self, config_key: str, config_value: Any, 
                        config_type: str = 'string', description: str = '') -> bool:
        """
        设置配置值
        
        Args:
            config_key: 配置键
            config_value: 配置值
            config_type: 配置类型
            description: 描述
        
        Returns:
            设置是否成功
        """
        try:
            # 转换值为字符串
            if config_type == 'json':
                import json
                value_str = json.dumps(config_value)
            else:
                value_str = str(config_value)
            
            query = """
                INSERT INTO strategy_configs (config_key, config_value, config_type, description)
                VALUES (:config_key, :config_value, :config_type, :description)
                ON DUPLICATE KEY UPDATE
                config_value = VALUES(config_value),
                config_type = VALUES(config_type),
                description = VALUES(description),
                updated_at = NOW()
            """
            
            params = {
                "config_key": config_key,
                "config_value": value_str,
                "config_type": config_type,
                "description": description
            }
            
            return self.execute_update(query, params)
            
        except Exception as e:
            self.logger.error(f"设置配置值失败: {str(e)}")
            return False
    
    def get_dataframe(self, query: str, params: Optional[Dict] = None) -> Optional[pd.DataFrame]:
        """
        执行查询并返回DataFrame
        
        Args:
            query: SQL查询语句
            params: 查询参数
        
        Returns:
            查询结果DataFrame
        """
        try:
            if not self.is_connected():
                raise ConnectionError("数据库连接已断开")
            
            return pd.read_sql(query, self._engine, params=params)
            
        except Exception as e:
            self.logger.error(f"DataFrame查询失败: {str(e)}")
            return None
    
    def close(self) -> None:
        """关闭连接"""
        try:
            if self._engine:
                self._engine.dispose()
            
            self._connected = False
            self.logger.info("MySQL连接已关闭")
            
        except Exception as e:
            self.logger.error(f"关闭MySQL连接时出错: {str(e)}")
    
    def __enter__(self):
        """上下文管理器入口"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.close()