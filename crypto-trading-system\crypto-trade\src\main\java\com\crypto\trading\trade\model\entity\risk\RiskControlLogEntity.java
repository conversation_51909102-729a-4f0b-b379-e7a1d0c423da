package com.crypto.trading.trade.model.entity.risk;

import com.baomidou.mybatisplus.annotation.*;

/**
 * 风控日志实体类
 * 
 * <p>对应数据库表t_risk_control_log，记录风控规则触发情况</p>
 */
@TableName("t_risk_control_log")
public class RiskControlLogEntity {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 交易信号ID
     */
    private String signalId;

    /**
     * 订单ID
     */
    private String orderId;

    /**
     * 风控规则ID
     */
    private String ruleId;

    /**
     * 风控规则名称
     */
    private String ruleName;

    /**
     * 风险类型
     */
    private String riskType;

    /**
     * 风险等级 LOW/MEDIUM/HIGH
     */
    private String riskLevel;

    /**
     * 是否触发 0:否 1:是
     */
    private Boolean triggered;

    /**
     * 采取的行动
     */
    private String actionTaken;

    /**
     * 风控详细信息（JSON格式）
     */
    private String details;

    /**
     * 触发时间（毫秒时间戳）
     */
    private Long triggeredTime;

    /**
     * 创建时间（毫秒时间戳）
     */
    private Long createdTime;

    /**
     * 默认构造函数
     */
    public RiskControlLogEntity() {
    }

    // Getters and Setters

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getSignalId() {
        return signalId;
    }

    public void setSignalId(String signalId) {
        this.signalId = signalId;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getRuleId() {
        return ruleId;
    }

    public void setRuleId(String ruleId) {
        this.ruleId = ruleId;
    }

    public String getRuleName() {
        return ruleName;
    }

    public void setRuleName(String ruleName) {
        this.ruleName = ruleName;
    }

    public String getRiskType() {
        return riskType;
    }

    public void setRiskType(String riskType) {
        this.riskType = riskType;
    }

    public String getRiskLevel() {
        return riskLevel;
    }

    public void setRiskLevel(String riskLevel) {
        this.riskLevel = riskLevel;
    }

    public Boolean getTriggered() {
        return triggered;
    }

    public void setTriggered(Boolean triggered) {
        this.triggered = triggered;
    }

    public String getActionTaken() {
        return actionTaken;
    }

    public void setActionTaken(String actionTaken) {
        this.actionTaken = actionTaken;
    }

    public String getDetails() {
        return details;
    }

    public void setDetails(String details) {
        this.details = details;
    }

    public Long getTriggeredTime() {
        return triggeredTime;
    }

    public void setTriggeredTime(Long triggeredTime) {
        this.triggeredTime = triggeredTime;
    }

    public Long getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(Long createdTime) {
        this.createdTime = createdTime;
    }

    @Override
    public String toString() {
        return "RiskControlLogEntity{" +
                "id=" + id +
                ", signalId='" + signalId + '\'' +
                ", orderId='" + orderId + '\'' +
                ", ruleId='" + ruleId + '\'' +
                ", ruleName='" + ruleName + '\'' +
                ", riskType='" + riskType + '\'' +
                ", riskLevel='" + riskLevel + '\'' +
                ", triggered=" + triggered +
                ", actionTaken='" + actionTaken + '\'' +
                ", triggeredTime=" + triggeredTime +
                '}';
    }

    /**
     * 构建者模式内部类
     */
    public static class Builder {
        private final RiskControlLogEntity log;

        public Builder() {
            log = new RiskControlLogEntity();
            log.setCreatedTime(System.currentTimeMillis());
            log.setTriggeredTime(System.currentTimeMillis());
        }

        public Builder signalId(String signalId) {
            log.setSignalId(signalId);
            return this;
        }

        public Builder orderId(String orderId) {
            log.setOrderId(orderId);
            return this;
        }

        public Builder ruleId(String ruleId) {
            log.setRuleId(ruleId);
            return this;
        }

        public Builder ruleName(String ruleName) {
            log.setRuleName(ruleName);
            return this;
        }

        public Builder riskType(String riskType) {
            log.setRiskType(riskType);
            return this;
        }

        public Builder riskLevel(String riskLevel) {
            log.setRiskLevel(riskLevel);
            return this;
        }

        public Builder triggered(Boolean triggered) {
            log.setTriggered(triggered);
            return this;
        }

        public Builder actionTaken(String actionTaken) {
            log.setActionTaken(actionTaken);
            return this;
        }

        public Builder details(String details) {
            log.setDetails(details);
            return this;
        }

        public Builder triggeredTime(Long triggeredTime) {
            log.setTriggeredTime(triggeredTime);
            return this;
        }

        public RiskControlLogEntity build() {
            return log;
        }
    }
}