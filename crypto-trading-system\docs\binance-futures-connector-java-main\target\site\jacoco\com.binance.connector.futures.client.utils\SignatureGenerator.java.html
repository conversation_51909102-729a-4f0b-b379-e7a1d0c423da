<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>SignatureGenerator.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">io.github.binance:binance-futures-connector-java</a> &gt; <a href="index.source.html" class="el_package">com.binance.connector.futures.client.utils</a> &gt; <span class="el_source">SignatureGenerator.java</span></div><h1>SignatureGenerator.java</h1><pre class="source lang-java linenums">package com.binance.connector.futures.client.utils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import org.apache.commons.codec.binary.Hex;

public final class SignatureGenerator {
    private static final String HMAC_SHA256 = &quot;HmacSHA256&quot;;

    private SignatureGenerator() {
    }

    public static String getSignature(String data, String key) {
        byte[] hmacSha256;
        try {
<span class="fc" id="L16">            SecretKeySpec secretKeySpec = new SecretKeySpec(key.getBytes(), HMAC_SHA256);</span>
<span class="fc" id="L17">            Mac mac = Mac.getInstance(HMAC_SHA256);</span>
<span class="fc" id="L18">            mac.init(secretKeySpec);</span>
<span class="fc" id="L19">            hmacSha256 = mac.doFinal(data.getBytes());</span>
<span class="nc" id="L20">        } catch (Exception e) {</span>
<span class="nc" id="L21">            throw new RuntimeException(&quot;Failed to calculate hmac-sha256&quot;, e);</span>
<span class="fc" id="L22">        }</span>
<span class="fc" id="L23">        return Hex.encodeHexString(hmacSha256);</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.7.202105040129</span></div></body></html>