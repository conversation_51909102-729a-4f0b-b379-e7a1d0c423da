package com.crypto.trading.common.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 基于路径的限流器工具类
 * <p>
 * 基于令牌桶算法实现的限流器，用于控制API请求频率
 * 按照路径进行分组限流，每个路径有独立的计数器
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class PathBasedRateLimiter {

    private static final Logger log = LoggerFactory.getLogger(PathBasedRateLimiter.class);

    /**
     * 默认最大请求数（每秒）
     */
    private static final int DEFAULT_MAX_REQUESTS = 2400;

    /**
     * 路径对应的最大请求数映射
     */
    private final Map<String, Integer> maxRequestsMap = new ConcurrentHashMap<>();

    /**
     * 路径对应的当前计数映射
     */
    private final Map<String, AtomicInteger> currentCountMap = new ConcurrentHashMap<>();

    /**
     * 重置计数的调度器
     */
    private final ScheduledExecutorService scheduler;

    /**
     * 重置间隔（毫秒）
     */
    private final long resetIntervalMillis;

    /**
     * 构造函数
     *
     * @param resetIntervalMillis 重置间隔（毫秒）
     */
    public PathBasedRateLimiter(long resetIntervalMillis) {
        this.resetIntervalMillis = resetIntervalMillis;
        this.scheduler = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread thread = Executors.defaultThreadFactory().newThread(r);
            thread.setName("PathBasedRateLimiter-Reset-Thread");
            thread.setDaemon(true);
            return thread;
        });

        // 定期重置计数器
        this.scheduler.scheduleAtFixedRate(this::resetAllCounters, 
                this.resetIntervalMillis, this.resetIntervalMillis, TimeUnit.MILLISECONDS);
        
        log.info("PathBasedRateLimiter已初始化，重置间隔: {}ms", resetIntervalMillis);
    }

    /**
     * 设置指定路径的最大请求数
     *
     * @param path 路径
     * @param maxRequests 最大请求数
     */
    public void setMaxRequests(String path, int maxRequests) {
        maxRequestsMap.put(path, maxRequests);
        log.debug("设置路径[{}]的最大请求数为: {}", path, maxRequests);
    }

    /**
     * 获取指定路径的最大请求数
     *
     * @param path 路径
     * @return 最大请求数
     */
    public int getMaxRequests(String path) {
        return maxRequestsMap.getOrDefault(path, DEFAULT_MAX_REQUESTS);
    }

    /**
     * 获取指定路径的当前计数
     *
     * @param path 路径
     * @return 当前计数
     */
    public int getCurrentCount(String path) {
        AtomicInteger counter = currentCountMap.get(path);
        return counter != null ? counter.get() : 0;
    }

    /**
     * 尝试获取令牌
     *
     * @param path 路径
     * @return 是否获取成功
     */
    public boolean tryAcquire(String path) {
        // 获取路径对应的最大请求数
        int maxRequests = getMaxRequests(path);
        
        // 获取或创建当前计数器
        AtomicInteger counter = currentCountMap.computeIfAbsent(path, k -> new AtomicInteger(0));
        
        // 当前计数值
        int currentCount = counter.get();
        
        // 如果当前计数小于最大请求数，则增加计数并返回成功
        if (currentCount < maxRequests) {
            if (counter.compareAndSet(currentCount, currentCount + 1)) {
                log.debug("路径[{}]获取令牌成功，当前计数: {}/{}", path, currentCount + 1, maxRequests);
                return true;
            }
            // 如果CAS失败，说明有其他线程修改了计数，递归重试
            return tryAcquire(path);
        }
        
        log.debug("路径[{}]获取令牌失败，已达到最大请求数: {}", path, maxRequests);
        return false;
    }

    /**
     * 重置所有计数器
     */
    private void resetAllCounters() {
        log.debug("重置所有计数器");
        currentCountMap.clear();
    }

    /**
     * 关闭限流器，释放资源
     */
    public void shutdown() {
        log.info("关闭PathBasedRateLimiter");
        if (scheduler != null && !scheduler.isShutdown()) {
            scheduler.shutdown();
            try {
                if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                    scheduler.shutdownNow();
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                scheduler.shutdownNow();
            }
        }
    }
} 