"""
Infrastructure Logging Module - 基础设施日志模块

主要组件：
- Logger：日志记录器
- LoggingCoreManager：日志核心管理器
- DebugLoggingCore：调试日志核心
- StructuredLoggingFormatter：结构化日志格式器
- LogRotationManager：日志轮转管理器

Author: Crypto ML Strategy Team
Version: 1.0.0
"""

from .logger import setup_logger, setup_standard_logging
from .logging_core_manager import LoggingManager as LoggingCoreManager
from .debug_logging_core import DebugLoggingCore
from .structured_logging_formatter import StructuredLoggingFormatter
from .log_rotation_manager import LogRotationManager
from .log_output_handlers import LogOutputHandlers

__all__ = [
    'setup_logger',
    'setup_standard_logging',
    'LoggingCoreManager',
    'DebugLoggingCore',
    'StructuredLoggingFormatter',
    'LogRotationManager',
    'LogOutputHandlers'
]

__version__ = '1.0.0'
__author__ = 'Crypto ML Strategy Team'