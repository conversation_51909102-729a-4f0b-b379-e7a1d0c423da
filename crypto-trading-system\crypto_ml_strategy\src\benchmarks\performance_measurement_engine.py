"""
Crypto ML Strategy - 性能测量引擎

该模块实现了高精度的性能测量功能，包括计时测量、资源监控、
吞吐量测量、延迟测量和并发性能测量。

主要功能：
- MeasurementEngine: 性能测量引擎主管理器
- TimingMeasurement: 精确计时测量
- ResourceMeasurement: 资源使用测量
- ThroughputMeasurement: 吞吐量测量
- LatencyMeasurement: 延迟测量
- ConcurrencyMeasurement: 并发性能测量

作者: Crypto ML Strategy Team
日期: 2024-01-20
版本: 1.0.0
"""

import asyncio
import time
import threading
import statistics
from collections import deque, defaultdict
from contextlib import contextmanager, asynccontextmanager
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Union, Callable, AsyncGenerator
from infrastructure.logging.logging_core_manager import get_logger
from performance_metrics_collector import get_global_metrics_collector
import psutil
import gc


@dataclass
class TimingResult:
    """计时结果"""
    operation_name: str
    start_time: float
    end_time: float
    duration_seconds: float
    duration_ms: float
    thread_id: int
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "operation_name": self.operation_name,
            "start_time": self.start_time,
            "end_time": self.end_time,
            "duration_seconds": self.duration_seconds,
            "duration_ms": self.duration_ms,
            "thread_id": self.thread_id,
            "metadata": self.metadata
        }


@dataclass
class ResourceSnapshot:
    """资源快照"""
    timestamp: float
    cpu_percent: float
    memory_mb: float
    memory_percent: float
    io_read_bytes: int
    io_write_bytes: int
    network_sent_bytes: int
    network_recv_bytes: int
    thread_count: int
    file_descriptors: int
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "timestamp": self.timestamp,
            "cpu_percent": self.cpu_percent,
            "memory_mb": self.memory_mb,
            "memory_percent": self.memory_percent,
            "io_read_bytes": self.io_read_bytes,
            "io_write_bytes": self.io_write_bytes,
            "network_sent_bytes": self.network_sent_bytes,
            "network_recv_bytes": self.network_recv_bytes,
            "thread_count": self.thread_count,
            "file_descriptors": self.file_descriptors
        }


class TimingMeasurement:
    """精确计时测量器"""
    
    def __init__(self):
        self.logger = get_logger(f"{__name__}.TimingMeasurement")
        self._active_timers: Dict[str, float] = {}
        self._timing_history: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        self._lock = threading.RLock()
    
    def start_timer(self, operation_name: str) -> str:
        """开始计时"""
        timer_id = f"{operation_name}_{threading.current_thread().ident}_{time.time()}"
        
        with self._lock:
            self._active_timers[timer_id] = time.perf_counter()
        
        return timer_id
    
    def end_timer(self, timer_id: str, operation_name: str, metadata: Optional[Dict[str, Any]] = None) -> TimingResult:
        """结束计时"""
        end_time = time.perf_counter()
        
        with self._lock:
            start_time = self._active_timers.pop(timer_id, None)
            
            if start_time is None:
                raise ValueError(f"Timer not found: {timer_id}")
            
            duration_seconds = end_time - start_time
            duration_ms = duration_seconds * 1000
            
            result = TimingResult(
                operation_name=operation_name,
                start_time=start_time,
                end_time=end_time,
                duration_seconds=duration_seconds,
                duration_ms=duration_ms,
                thread_id=threading.current_thread().ident,
                metadata=metadata or {}
            )
            
            # 保存到历史记录
            self._timing_history[operation_name].append(result)
            
            return result
    
    @contextmanager
    def time_operation(self, operation_name: str, metadata: Optional[Dict[str, Any]] = None):
        """计时上下文管理器"""
        timer_id = self.start_timer(operation_name)
        
        try:
            yield timer_id
        finally:
            result = self.end_timer(timer_id, operation_name, metadata)
            self.logger.debug(f"Operation {operation_name} took {result.duration_ms:.2f}ms")
    
    @asynccontextmanager
    async def async_time_operation(self, operation_name: str, metadata: Optional[Dict[str, Any]] = None):
        """异步计时上下文管理器"""
        timer_id = self.start_timer(operation_name)
        
        try:
            yield timer_id
        finally:
            result = self.end_timer(timer_id, operation_name, metadata)
            self.logger.debug(f"Async operation {operation_name} took {result.duration_ms:.2f}ms")
    
    def get_timing_statistics(self, operation_name: str) -> Dict[str, float]:
        """获取计时统计信息"""
        with self._lock:
            history = self._timing_history.get(operation_name, deque())
            
            if not history:
                return {}
            
            durations = [result.duration_ms for result in history]
            
            return {
                "count": len(durations),
                "min_ms": min(durations),
                "max_ms": max(durations),
                "avg_ms": statistics.mean(durations),
                "median_ms": statistics.median(durations),
                "std_dev_ms": statistics.stdev(durations) if len(durations) > 1 else 0,
                "p95_ms": sorted(durations)[int(len(durations) * 0.95)],
                "p99_ms": sorted(durations)[int(len(durations) * 0.99)]
            }
    
    def get_all_statistics(self) -> Dict[str, Dict[str, float]]:
        """获取所有操作的统计信息"""
        with self._lock:
            return {
                operation: self.get_timing_statistics(operation)
                for operation in self._timing_history.keys()
            }


class ResourceMeasurement:
    """资源使用测量器"""
    
    def __init__(self, sampling_interval: float = 0.1):
        self.sampling_interval = sampling_interval
        self.logger = get_logger(f"{__name__}.ResourceMeasurement")
        self._process = psutil.Process()
        self._monitoring = False
        self._monitor_task: Optional[asyncio.Task] = None
        self._snapshots: deque = deque(maxlen=10000)
        self._lock = threading.RLock()
    
    async def start_monitoring(self) -> None:
        """开始资源监控"""
        with self._lock:
            if self._monitoring:
                return
            
            self._monitoring = True
            self._monitor_task = asyncio.create_task(self._monitoring_loop())
            
            self.logger.info("Resource monitoring started")
    
    async def stop_monitoring(self) -> None:
        """停止资源监控"""
        with self._lock:
            if not self._monitoring:
                return
            
            self._monitoring = False
            
            if self._monitor_task and not self._monitor_task.done():
                self._monitor_task.cancel()
                try:
                    await self._monitor_task
                except asyncio.CancelledError:
                    pass
            
            self.logger.info("Resource monitoring stopped")
    
    async def _monitoring_loop(self) -> None:
        """监控循环"""
        while self._monitoring:
            try:
                snapshot = await self._take_snapshot()
                
                with self._lock:
                    self._snapshots.append(snapshot)
                
                await asyncio.sleep(self.sampling_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error in resource monitoring: {e}")
                await asyncio.sleep(self.sampling_interval)
    
    async def _take_snapshot(self) -> ResourceSnapshot:
        """获取资源快照"""
        try:
            # 系统资源
            cpu_percent = psutil.cpu_percent()
            memory = psutil.virtual_memory()
            
            # 进程资源
            process_memory = self._process.memory_info()
            process_cpu = self._process.cpu_percent()
            
            # I/O统计
            try:
                io_counters = self._process.io_counters()
                io_read_bytes = io_counters.read_bytes
                io_write_bytes = io_counters.write_bytes
            except (psutil.AccessDenied, AttributeError):
                io_read_bytes = io_write_bytes = 0
            
            # 网络统计
            try:
                net_io = psutil.net_io_counters()
                network_sent = net_io.bytes_sent
                network_recv = net_io.bytes_recv
            except (psutil.AccessDenied, AttributeError):
                network_sent = network_recv = 0
            
            # 线程和文件描述符
            try:
                thread_count = self._process.num_threads()
                file_descriptors = self._process.num_fds() if hasattr(self._process, 'num_fds') else 0
            except (psutil.AccessDenied, AttributeError):
                thread_count = file_descriptors = 0
            
            return ResourceSnapshot(
                timestamp=time.time(),
                cpu_percent=max(cpu_percent, process_cpu),  # 取较大值
                memory_mb=process_memory.rss / 1024 / 1024,
                memory_percent=memory.percent,
                io_read_bytes=io_read_bytes,
                io_write_bytes=io_write_bytes,
                network_sent_bytes=network_sent,
                network_recv_bytes=network_recv,
                thread_count=thread_count,
                file_descriptors=file_descriptors
            )
            
        except Exception as e:
            self.logger.error(f"Failed to take resource snapshot: {e}")
            return ResourceSnapshot(
                timestamp=time.time(),
                cpu_percent=0, memory_mb=0, memory_percent=0,
                io_read_bytes=0, io_write_bytes=0,
                network_sent_bytes=0, network_recv_bytes=0,
                thread_count=0, file_descriptors=0
            )
    
    def get_current_snapshot(self) -> Optional[ResourceSnapshot]:
        """获取当前资源快照"""
        with self._lock:
            return self._snapshots[-1] if self._snapshots else None
    
    def get_resource_statistics(self, duration_seconds: int = 60) -> Dict[str, float]:
        """获取资源统计信息"""
        cutoff_time = time.time() - duration_seconds
        
        with self._lock:
            recent_snapshots = [
                snapshot for snapshot in self._snapshots
                if snapshot.timestamp >= cutoff_time
            ]
            
            if not recent_snapshots:
                return {}
            
            # 计算统计信息
            cpu_values = [s.cpu_percent for s in recent_snapshots]
            memory_values = [s.memory_mb for s in recent_snapshots]
            
            return {
                "avg_cpu_percent": statistics.mean(cpu_values),
                "max_cpu_percent": max(cpu_values),
                "avg_memory_mb": statistics.mean(memory_values),
                "max_memory_mb": max(memory_values),
                "sample_count": len(recent_snapshots),
                "duration_seconds": duration_seconds
            }


class ThroughputMeasurement:
    """吞吐量测量器"""
    
    def __init__(self, window_size: int = 1000):
        self.window_size = window_size
        self.logger = get_logger(f"{__name__}.ThroughputMeasurement")
        self._operation_timestamps: Dict[str, deque] = defaultdict(lambda: deque(maxlen=window_size))
        self._lock = threading.RLock()
    
    def record_operation(self, operation_name: str, count: int = 1) -> None:
        """记录操作"""
        timestamp = time.time()
        
        with self._lock:
            for _ in range(count):
                self._operation_timestamps[operation_name].append(timestamp)
    
    def get_throughput(self, operation_name: str, window_seconds: int = 60) -> float:
        """获取吞吐量（操作数/秒）"""
        cutoff_time = time.time() - window_seconds
        
        with self._lock:
            timestamps = self._operation_timestamps.get(operation_name, deque())
            
            # 计算时间窗口内的操作数
            recent_operations = sum(1 for ts in timestamps if ts >= cutoff_time)
            
            return recent_operations / window_seconds if window_seconds > 0 else 0
    
    def get_all_throughputs(self, window_seconds: int = 60) -> Dict[str, float]:
        """获取所有操作的吞吐量"""
        with self._lock:
            return {
                operation: self.get_throughput(operation, window_seconds)
                for operation in self._operation_timestamps.keys()
            }


class LatencyMeasurement:
    """延迟测量器"""
    
    def __init__(self, percentiles: List[float] = None):
        self.percentiles = percentiles or [50, 90, 95, 99, 99.9]
        self.logger = get_logger(f"{__name__}.LatencyMeasurement")
        self._latencies: Dict[str, deque] = defaultdict(lambda: deque(maxlen=10000))
        self._lock = threading.RLock()
    
    def record_latency(self, operation_name: str, latency_ms: float) -> None:
        """记录延迟"""
        with self._lock:
            self._latencies[operation_name].append(latency_ms)
    
    def get_latency_percentiles(self, operation_name: str) -> Dict[str, float]:
        """获取延迟百分位数"""
        with self._lock:
            latencies = list(self._latencies.get(operation_name, deque()))
            
            if not latencies:
                return {}
            
            sorted_latencies = sorted(latencies)
            result = {}
            
            for percentile in self.percentiles:
                index = int(len(sorted_latencies) * percentile / 100)
                index = min(index, len(sorted_latencies) - 1)
                result[f"p{percentile}"] = sorted_latencies[index]
            
            result.update({
                "min": min(latencies),
                "max": max(latencies),
                "avg": statistics.mean(latencies),
                "count": len(latencies)
            })
            
            return result
    
    def get_all_latency_percentiles(self) -> Dict[str, Dict[str, float]]:
        """获取所有操作的延迟百分位数"""
        with self._lock:
            return {
                operation: self.get_latency_percentiles(operation)
                for operation in self._latencies.keys()
            }


class ConcurrencyMeasurement:
    """并发性能测量器"""
    
    def __init__(self):
        self.logger = get_logger(f"{__name__}.ConcurrencyMeasurement")
        self._active_operations: Dict[str, int] = defaultdict(int)
        self._max_concurrent: Dict[str, int] = defaultdict(int)
        self._total_operations: Dict[str, int] = defaultdict(int)
        self._lock = threading.RLock()
    
    @contextmanager
    def track_concurrent_operation(self, operation_name: str):
        """跟踪并发操作"""
        with self._lock:
            self._active_operations[operation_name] += 1
            self._total_operations[operation_name] += 1
            
            # 更新最大并发数
            current_concurrent = self._active_operations[operation_name]
            if current_concurrent > self._max_concurrent[operation_name]:
                self._max_concurrent[operation_name] = current_concurrent
        
        try:
            yield
        finally:
            with self._lock:
                self._active_operations[operation_name] -= 1
    
    @asynccontextmanager
    async def async_track_concurrent_operation(self, operation_name: str):
        """异步跟踪并发操作"""
        with self._lock:
            self._active_operations[operation_name] += 1
            self._total_operations[operation_name] += 1
            
            # 更新最大并发数
            current_concurrent = self._active_operations[operation_name]
            if current_concurrent > self._max_concurrent[operation_name]:
                self._max_concurrent[operation_name] = current_concurrent
        
        try:
            yield
        finally:
            with self._lock:
                self._active_operations[operation_name] -= 1
    
    def get_concurrency_statistics(self, operation_name: str) -> Dict[str, int]:
        """获取并发统计信息"""
        with self._lock:
            return {
                "current_concurrent": self._active_operations.get(operation_name, 0),
                "max_concurrent": self._max_concurrent.get(operation_name, 0),
                "total_operations": self._total_operations.get(operation_name, 0)
            }
    
    def get_all_concurrency_statistics(self) -> Dict[str, Dict[str, int]]:
        """获取所有操作的并发统计信息"""
        with self._lock:
            all_operations = set(self._active_operations.keys()) | set(self._max_concurrent.keys()) | set(self._total_operations.keys())
            
            return {
                operation: self.get_concurrency_statistics(operation)
                for operation in all_operations
            }


class MeasurementEngine:
    """性能测量引擎主管理器"""
    
    def __init__(self):
        self.logger = get_logger(f"{__name__}.MeasurementEngine")
        self.metrics_collector = get_global_metrics_collector()
        
        # 初始化测量组件
        self.timing = TimingMeasurement()
        self.resource = ResourceMeasurement()
        self.throughput = ThroughputMeasurement()
        self.latency = LatencyMeasurement()
        self.concurrency = ConcurrencyMeasurement()
        
        self._initialized = False
        self._lock = threading.RLock()
    
    async def initialize(self) -> None:
        """初始化测量引擎"""
        with self._lock:
            if self._initialized:
                return
            
            # 启动资源监控
            await self.resource.start_monitoring()
            
            self._initialized = True
            self.logger.info("Performance measurement engine initialized")
    
    async def shutdown(self) -> None:
        """关闭测量引擎"""
        with self._lock:
            if not self._initialized:
                return
            
            # 停止资源监控
            await self.resource.stop_monitoring()
            
            self._initialized = False
            self.logger.info("Performance measurement engine shutdown")
    
    def measure_operation(self, operation_name: str, metadata: Optional[Dict[str, Any]] = None):
        """测量操作装饰器"""
        def decorator(func):
            if asyncio.iscoroutinefunction(func):
                async def async_wrapper(*args, **kwargs):
                    async with self.timing.async_time_operation(operation_name, metadata):
                        async with self.concurrency.async_track_concurrent_operation(operation_name):
                            start_time = time.perf_counter()
                            
                            try:
                                result = await func(*args, **kwargs)
                                
                                # 记录成功操作
                                self.throughput.record_operation(operation_name)
                                
                                return result
                                
                            except Exception as e:
                                # 记录失败操作
                                self.throughput.record_operation(f"{operation_name}_error")
                                raise
                            finally:
                                # 记录延迟
                                latency_ms = (time.perf_counter() - start_time) * 1000
                                self.latency.record_latency(operation_name, latency_ms)
                
                return async_wrapper
            else:
                def sync_wrapper(*args, **kwargs):
                    with self.timing.time_operation(operation_name, metadata):
                        with self.concurrency.track_concurrent_operation(operation_name):
                            start_time = time.perf_counter()
                            
                            try:
                                result = func(*args, **kwargs)
                                
                                # 记录成功操作
                                self.throughput.record_operation(operation_name)
                                
                                return result
                                
                            except Exception as e:
                                # 记录失败操作
                                self.throughput.record_operation(f"{operation_name}_error")
                                raise
                            finally:
                                # 记录延迟
                                latency_ms = (time.perf_counter() - start_time) * 1000
                                self.latency.record_latency(operation_name, latency_ms)
                
                return sync_wrapper
        
        return decorator
    
    def get_comprehensive_metrics(self, operation_name: str) -> Dict[str, Any]:
        """获取综合性能指标"""
        return {
            "timing": self.timing.get_timing_statistics(operation_name),
            "throughput": self.throughput.get_throughput(operation_name),
            "latency": self.latency.get_latency_percentiles(operation_name),
            "concurrency": self.concurrency.get_concurrency_statistics(operation_name),
            "resource": self.resource.get_resource_statistics()
        }
    
    def get_all_metrics(self) -> Dict[str, Any]:
        """获取所有性能指标"""
        return {
            "timing": self.timing.get_all_statistics(),
            "throughput": self.throughput.get_all_throughputs(),
            "latency": self.latency.get_all_latency_percentiles(),
            "concurrency": self.concurrency.get_all_concurrency_statistics(),
            "resource": self.resource.get_resource_statistics()
        }


# 全局测量引擎实例
_global_measurement_engine: Optional[MeasurementEngine] = None


def get_global_measurement_engine() -> MeasurementEngine:
    """获取全局测量引擎实例"""
    global _global_measurement_engine
    
    if _global_measurement_engine is None:
        _global_measurement_engine = MeasurementEngine()
    
    return _global_measurement_engine


# 便捷装饰器
def measure_performance(operation_name: str, metadata: Optional[Dict[str, Any]] = None):
    """性能测量装饰器"""
    engine = get_global_measurement_engine()
    return engine.measure_operation(operation_name, metadata)


# 模块级别的日志器
module_logger = get_logger(__name__)