"""
数据库连接器

提供多种数据库的统一访问接口，支持MySQL、PostgreSQL、InfluxDB等。
包含连接池管理、事务处理、健康检查和自动重连功能。

Author: Crypto ML Strategy Team
Date: 2024-12-19
"""

import asyncio
import logging
import time
import threading
from typing import Dict, List, Optional, Any, Union, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import pandas as pd
from contextlib import asynccontextmanager
import json

from data.data_config import DatabaseConfig, DatabaseType

logger = logging.getLogger(__name__)


@dataclass
class ConnectionStats:
    """连接统计信息"""
    total_connections: int = 0
    active_connections: int = 0
    idle_connections: int = 0
    failed_connections: int = 0
    total_queries: int = 0
    successful_queries: int = 0
    failed_queries: int = 0
    total_query_time: float = 0.0
    last_connection_time: Optional[datetime] = None
    last_query_time: Optional[datetime] = None
    
    @property
    def success_rate(self) -> float:
        """查询成功率"""
        if self.total_queries == 0:
            return 0.0
        return self.successful_queries / self.total_queries
    
    @property
    def average_query_time(self) -> float:
        """平均查询时间"""
        if self.successful_queries == 0:
            return 0.0
        return self.total_query_time / self.successful_queries


class DatabaseConnector:
    """数据库连接器基类"""
    
    def __init__(self, config: DatabaseConfig):
        """
        初始化数据库连接器
        
        Args:
            config: 数据库配置
        """
        self.config = config
        self.connection_pool = None
        self.engine = None
        self.stats = ConnectionStats()
        self.lock = threading.RLock()
        self._health_check_task = None
        
        logger.info(f"数据库连接器初始化: {config.db_type.value}")
    
    async def initialize(self) -> bool:
        """初始化连接"""
        try:
            if self.config.db_type == DatabaseType.MYSQL:
                return await self._initialize_mysql()
            elif self.config.db_type == DatabaseType.POSTGRESQL:
                return await self._initialize_postgresql()
            elif self.config.db_type == DatabaseType.INFLUXDB:
                return await self._initialize_influxdb()
            elif self.config.db_type == DatabaseType.SQLITE:
                return await self._initialize_sqlite()
            else:
                logger.error(f"不支持的数据库类型: {self.config.db_type}")
                return False
                
        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
            return False
    
    async def _initialize_mysql(self) -> bool:
        """初始化MySQL连接"""
        try:
            import aiomysql
            from sqlalchemy import create_engine
            from sqlalchemy.pool import QueuePool
            
            # 创建连接池
            self.connection_pool = await aiomysql.create_pool(
                host=self.config.host,
                port=self.config.port,
                user=self.config.username,
                password=self.config.password,
                db=self.config.database,
                minsize=1,
                maxsize=self.config.pool_size,
                pool_recycle=self.config.pool_recycle,
                autocommit=False
            )
            
            # 创建SQLAlchemy引擎（用于DataFrame操作）
            self.engine = create_engine(
                self.config.get_connection_string(),
                poolclass=QueuePool,
                pool_size=self.config.pool_size,
                max_overflow=self.config.max_overflow,
                pool_timeout=self.config.pool_timeout,
                pool_recycle=self.config.pool_recycle
            )
            
            self.stats.last_connection_time = datetime.now()
            logger.info("MySQL连接池创建成功")
            return True
            
        except ImportError:
            logger.error("aiomysql库未安装")
            return False
        except Exception as e:
            logger.error(f"MySQL连接失败: {e}")
            return False
    
    async def _initialize_postgresql(self) -> bool:
        """初始化PostgreSQL连接"""
        try:
            import asyncpg
            from sqlalchemy import create_engine
            from sqlalchemy.pool import QueuePool
            
            # 创建连接池
            self.connection_pool = await asyncpg.create_pool(
                host=self.config.host,
                port=self.config.port,
                user=self.config.username,
                password=self.config.password,
                database=self.config.database,
                min_size=1,
                max_size=self.config.pool_size,
                command_timeout=self.config.pool_timeout
            )
            
            # 创建SQLAlchemy引擎
            self.engine = create_engine(
                self.config.get_connection_string(),
                poolclass=QueuePool,
                pool_size=self.config.pool_size,
                max_overflow=self.config.max_overflow,
                pool_timeout=self.config.pool_timeout,
                pool_recycle=self.config.pool_recycle
            )
            
            self.stats.last_connection_time = datetime.now()
            logger.info("PostgreSQL连接池创建成功")
            return True
            
        except ImportError:
            logger.error("asyncpg库未安装")
            return False
        except Exception as e:
            logger.error(f"PostgreSQL连接失败: {e}")
            return False
    
    async def _initialize_influxdb(self) -> bool:
        """初始化InfluxDB连接"""
        try:
            from influxdb_client.client.influxdb_client_async import InfluxDBClientAsync
            
            # 创建InfluxDB客户端
            self.connection_pool = InfluxDBClientAsync(
                url=f"http://{self.config.host}:{self.config.port}",
                token=self.config.password,  # InfluxDB 2.x使用token
                org=self.config.connection_params.get('org', 'default'),
                timeout=self.config.pool_timeout * 1000  # 转换为毫秒
            )
            
            self.stats.last_connection_time = datetime.now()
            logger.info("InfluxDB连接创建成功")
            return True
            
        except ImportError:
            logger.error("influxdb-client库未安装")
            return False
        except Exception as e:
            logger.error(f"InfluxDB连接失败: {e}")
            return False
    
    async def _initialize_sqlite(self) -> bool:
        """初始化SQLite连接"""
        try:
            import aiosqlite
            from sqlalchemy import create_engine
            
            # SQLite不需要连接池，但我们可以创建引擎
            self.engine = create_engine(
                self.config.get_connection_string(),
                pool_timeout=self.config.pool_timeout,
                pool_recycle=self.config.pool_recycle
            )
            
            self.stats.last_connection_time = datetime.now()
            logger.info("SQLite连接创建成功")
            return True
            
        except ImportError:
            logger.error("aiosqlite库未安装")
            return False
        except Exception as e:
            logger.error(f"SQLite连接失败: {e}")
            return False
    
    async def execute_query(self, query: str, params: Optional[Dict] = None) -> Optional[List[Dict]]:
        """
        执行查询
        
        Args:
            query: SQL查询语句
            params: 查询参数
            
        Returns:
            查询结果
        """
        start_time = time.time()
        
        try:
            with self.lock:
                self.stats.total_queries += 1
                self.stats.last_query_time = datetime.now()
            
            if self.config.db_type == DatabaseType.MYSQL:
                result = await self._execute_mysql_query(query, params)
            elif self.config.db_type == DatabaseType.POSTGRESQL:
                result = await self._execute_postgresql_query(query, params)
            elif self.config.db_type == DatabaseType.INFLUXDB:
                result = await self._execute_influxdb_query(query, params)
            elif self.config.db_type == DatabaseType.SQLITE:
                result = await self._execute_sqlite_query(query, params)
            else:
                raise ValueError(f"不支持的数据库类型: {self.config.db_type}")
            
            query_time = time.time() - start_time
            
            with self.lock:
                self.stats.successful_queries += 1
                self.stats.total_query_time += query_time
            
            logger.debug(f"查询执行成功，耗时: {query_time:.3f}s")
            return result
            
        except Exception as e:
            query_time = time.time() - start_time
            
            with self.lock:
                self.stats.failed_queries += 1
                self.stats.total_query_time += query_time
            
            logger.error(f"查询执行失败: {e}")
            return None
    
    async def _execute_mysql_query(self, query: str, params: Optional[Dict] = None) -> List[Dict]:
        """执行MySQL查询"""
        if not self.connection_pool:
            raise RuntimeError("MySQL连接池未初始化")
        
        async with self.connection_pool.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(query, params or {})
                
                if query.strip().upper().startswith('SELECT'):
                    rows = await cursor.fetchall()
                    columns = [desc[0] for desc in cursor.description]
                    return [dict(zip(columns, row)) for row in rows]
                else:
                    await conn.commit()
                    return [{'affected_rows': cursor.rowcount}]
    
    async def _execute_postgresql_query(self, query: str, params: Optional[Dict] = None) -> List[Dict]:
        """执行PostgreSQL查询"""
        if not self.connection_pool:
            raise RuntimeError("PostgreSQL连接池未初始化")
        
        async with self.connection_pool.acquire() as conn:
            if query.strip().upper().startswith('SELECT'):
                rows = await conn.fetch(query, *(params.values() if params else []))
                return [dict(row) for row in rows]
            else:
                result = await conn.execute(query, *(params.values() if params else []))
                return [{'result': result}]
    
    async def _execute_influxdb_query(self, query: str, params: Optional[Dict] = None) -> List[Dict]:
        """执行InfluxDB查询"""
        if not self.connection_pool:
            raise RuntimeError("InfluxDB客户端未初始化")
        
        query_api = self.connection_pool.query_api()
        
        # InfluxDB使用Flux查询语言
        tables = await query_api.query(query)
        
        results = []
        for table in tables:
            for record in table.records:
                results.append(record.values)
        
        return results
    
    async def _execute_sqlite_query(self, query: str, params: Optional[Dict] = None) -> List[Dict]:
        """执行SQLite查询"""
        import aiosqlite
        
        async with aiosqlite.connect(self.config.database) as conn:
            conn.row_factory = aiosqlite.Row
            async with conn.execute(query, params or {}) as cursor:
                if query.strip().upper().startswith('SELECT'):
                    rows = await cursor.fetchall()
                    return [dict(row) for row in rows]
                else:
                    await conn.commit()
                    return [{'affected_rows': cursor.rowcount}]
    
    async def execute_dataframe_query(self, query: str, params: Optional[Dict] = None) -> Optional[pd.DataFrame]:
        """
        执行查询并返回DataFrame
        
        Args:
            query: SQL查询语句
            params: 查询参数
            
        Returns:
            查询结果DataFrame
        """
        try:
            if not self.engine:
                logger.error("SQLAlchemy引擎未初始化")
                return None
            
            # 使用pandas读取SQL
            df = pd.read_sql(query, self.engine, params=params)
            
            logger.debug(f"DataFrame查询成功，返回 {len(df)} 行数据")
            return df
            
        except Exception as e:
            logger.error(f"DataFrame查询失败: {e}")
            return None
    
    async def insert_dataframe(self, df: pd.DataFrame, table_name: str, 
                             if_exists: str = 'append') -> bool:
        """
        插入DataFrame到数据库
        
        Args:
            df: 要插入的DataFrame
            table_name: 表名
            if_exists: 如果表存在的处理方式 ('fail', 'replace', 'append')
            
        Returns:
            是否成功
        """
        try:
            if not self.engine:
                logger.error("SQLAlchemy引擎未初始化")
                return False
            
            # 使用pandas写入SQL
            df.to_sql(table_name, self.engine, if_exists=if_exists, index=False)
            
            logger.info(f"DataFrame插入成功: {len(df)} 行数据到表 {table_name}")
            return True
            
        except Exception as e:
            logger.error(f"DataFrame插入失败: {e}")
            return False
    
    @asynccontextmanager
    async def transaction(self):
        """事务上下文管理器"""
        if self.config.db_type == DatabaseType.MYSQL:
            async with self._mysql_transaction() as trans:
                yield trans
        elif self.config.db_type == DatabaseType.POSTGRESQL:
            async with self._postgresql_transaction() as trans:
                yield trans
        elif self.config.db_type == DatabaseType.SQLITE:
            async with self._sqlite_transaction() as trans:
                yield trans
        else:
            # 对于不支持事务的数据库，直接yield None
            yield None
    
    @asynccontextmanager
    async def _mysql_transaction(self):
        """MySQL事务管理器"""
        if not self.connection_pool:
            raise RuntimeError("MySQL连接池未初始化")
        
        async with self.connection_pool.acquire() as conn:
            async with conn.cursor() as cursor:
                try:
                    await conn.begin()
                    yield cursor
                    await conn.commit()
                except Exception:
                    await conn.rollback()
                    raise
    
    @asynccontextmanager
    async def _postgresql_transaction(self):
        """PostgreSQL事务管理器"""
        if not self.connection_pool:
            raise RuntimeError("PostgreSQL连接池未初始化")
        
        async with self.connection_pool.acquire() as conn:
            async with conn.transaction():
                yield conn
    
    @asynccontextmanager
    async def _sqlite_transaction(self):
        """SQLite事务管理器"""
        import aiosqlite
        
        async with aiosqlite.connect(self.config.database) as conn:
            try:
                yield conn
                await conn.commit()
            except Exception:
                await conn.rollback()
                raise
    
    async def health_check(self) -> bool:
        """健康检查"""
        try:
            if self.config.db_type == DatabaseType.MYSQL:
                return await self._mysql_health_check()
            elif self.config.db_type == DatabaseType.POSTGRESQL:
                return await self._postgresql_health_check()
            elif self.config.db_type == DatabaseType.INFLUXDB:
                return await self._influxdb_health_check()
            elif self.config.db_type == DatabaseType.SQLITE:
                return await self._sqlite_health_check()
            else:
                return False
                
        except Exception as e:
            logger.error(f"健康检查失败: {e}")
            return False
    
    async def _mysql_health_check(self) -> bool:
        """MySQL健康检查"""
        try:
            result = await self.execute_query("SELECT 1")
            return result is not None and len(result) > 0
        except:
            return False
    
    async def _postgresql_health_check(self) -> bool:
        """PostgreSQL健康检查"""
        try:
            result = await self.execute_query("SELECT 1")
            return result is not None and len(result) > 0
        except:
            return False
    
    async def _influxdb_health_check(self) -> bool:
        """InfluxDB健康检查"""
        try:
            if not self.connection_pool:
                return False
            
            health = await self.connection_pool.health()
            return health.status == "pass"
        except:
            return False
    
    async def _sqlite_health_check(self) -> bool:
        """SQLite健康检查"""
        try:
            result = await self.execute_query("SELECT 1")
            return result is not None and len(result) > 0
        except:
            return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取连接统计信息"""
        try:
            return {
                'database_type': self.config.db_type.value,
                'connection_stats': {
                    'total_connections': self.stats.total_connections,
                    'active_connections': self.stats.active_connections,
                    'idle_connections': self.stats.idle_connections,
                    'failed_connections': self.stats.failed_connections,
                    'last_connection_time': self.stats.last_connection_time.isoformat() if self.stats.last_connection_time else None
                },
                'query_stats': {
                    'total_queries': self.stats.total_queries,
                    'successful_queries': self.stats.successful_queries,
                    'failed_queries': self.stats.failed_queries,
                    'success_rate': self.stats.success_rate,
                    'average_query_time': self.stats.average_query_time,
                    'total_query_time': self.stats.total_query_time,
                    'last_query_time': self.stats.last_query_time.isoformat() if self.stats.last_query_time else None
                },
                'config': self.config.to_dict()
            }
            
        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return {'error': str(e)}
    
    async def close(self) -> None:
        """关闭数据库连接"""
        try:
            if self._health_check_task:
                self._health_check_task.cancel()
            
            if self.config.db_type == DatabaseType.MYSQL and self.connection_pool:
                self.connection_pool.close()
                await self.connection_pool.wait_closed()
            elif self.config.db_type == DatabaseType.POSTGRESQL and self.connection_pool:
                await self.connection_pool.close()
            elif self.config.db_type == DatabaseType.INFLUXDB and self.connection_pool:
                await self.connection_pool.close()
            
            if self.engine:
                self.engine.dispose()
            
            logger.info("数据库连接已关闭")
            
        except Exception as e:
            logger.error(f"关闭数据库连接失败: {e}")
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.initialize()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.close()


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self):
        self.connectors: Dict[str, DatabaseConnector] = {}
        self.lock = threading.RLock()
    
    async def add_database(self, name: str, config: DatabaseConfig) -> bool:
        """
        添加数据库连接
        
        Args:
            name: 连接名称
            config: 数据库配置
            
        Returns:
            是否成功
        """
        try:
            connector = DatabaseConnector(config)
            success = await connector.initialize()
            
            if success:
                with self.lock:
                    self.connectors[name] = connector
                logger.info(f"数据库连接添加成功: {name}")
                return True
            else:
                logger.error(f"数据库连接添加失败: {name}")
                return False
                
        except Exception as e:
            logger.error(f"添加数据库连接失败: {e}")
            return False
    
    def get_connector(self, name: str) -> Optional[DatabaseConnector]:
        """获取数据库连接器"""
        return self.connectors.get(name)
    
    async def remove_database(self, name: str) -> bool:
        """移除数据库连接"""
        try:
            with self.lock:
                if name in self.connectors:
                    connector = self.connectors.pop(name)
                    await connector.close()
                    logger.info(f"数据库连接移除成功: {name}")
                    return True
                else:
                    logger.warning(f"数据库连接不存在: {name}")
                    return False
                    
        except Exception as e:
            logger.error(f"移除数据库连接失败: {e}")
            return False
    
    async def health_check_all(self) -> Dict[str, bool]:
        """检查所有数据库连接健康状态"""
        health_status = {}
        
        for name, connector in self.connectors.items():
            try:
                health_status[name] = await connector.health_check()
            except Exception as e:
                logger.error(f"数据库 {name} 健康检查失败: {e}")
                health_status[name] = False
        
        return health_status
    
    def get_all_statistics(self) -> Dict[str, Any]:
        """获取所有数据库统计信息"""
        all_stats = {}
        
        for name, connector in self.connectors.items():
            try:
                all_stats[name] = connector.get_statistics()
            except Exception as e:
                logger.error(f"获取数据库 {name} 统计信息失败: {e}")
                all_stats[name] = {'error': str(e)}
        
        return all_stats
    
    async def close_all(self) -> None:
        """关闭所有数据库连接"""
        for name, connector in list(self.connectors.items()):
            try:
                await connector.close()
                logger.info(f"数据库连接 {name} 已关闭")
            except Exception as e:
                logger.error(f"关闭数据库连接 {name} 失败: {e}")
        
        self.connectors.clear()
        logger.info("所有数据库连接已关闭")