package com.crypto.trading.bootstrap.client;

import com.crypto.trading.bootstrap.config.AppConfig;
import com.crypto.trading.bootstrap.discovery.ServiceRegistry;
import com.crypto.trading.common.exception.StrategyServiceException;
import com.crypto.trading.common.util.ThreadUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;
import org.springframework.web.client.ResourceAccessException;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.net.URI;
import java.time.Duration;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * 策略服务客户端，用于与Python策略微服务通信
 * 提供对策略服务API的封装，支持重试、负载均衡和故障转移机制
 */
@Component
public class StrategyServiceClient {

    private static final Logger log = LoggerFactory.getLogger(StrategyServiceClient.class);
    
    /**
     * 应用配置
     */
    private final AppConfig appConfig;
    
    /**
     * 服务注册表
     */
    private final ServiceRegistry serviceRegistry;
    
    /**
     * JSON解析器
     */
    private final ObjectMapper objectMapper;
    
    /**
     * REST请求客户端
     */
    private final RestTemplate restTemplate;
    
    /**
     * 请求统计信息，键为服务URL，值为请求计数
     */
    private final Map<String, Long> requestStats = new ConcurrentHashMap<>();
    
    /**
     * 虚拟线程执行器，用于异步处理API请求
     */
    private ExecutorService virtualThreadExecutor;
    
    /**
     * Spring异步任务执行器
     */
    @Autowired(required = false)
    @org.springframework.beans.factory.annotation.Qualifier("virtualThreadTaskExecutor")
    private AsyncTaskExecutor asyncTaskExecutor;
    
    /**
     * 构造函数，注入所需依赖
     * 
     * @param appConfig 应用配置
     * @param serviceRegistry 服务注册表
     * @param objectMapper JSON解析器
     */
    @Autowired
    public StrategyServiceClient(AppConfig appConfig, ServiceRegistry serviceRegistry, ObjectMapper objectMapper) {
        this.appConfig = appConfig;
        this.serviceRegistry = serviceRegistry;
        this.objectMapper = objectMapper;
        
        // 配置RestTemplate，设置连接和读取超时
        int connectTimeout = appConfig.getPythonStrategy().getConnectTimeout();
        int readTimeout = appConfig.getPythonStrategy().getReadTimeout();
        
        org.springframework.boot.web.client.RestTemplateBuilder builder = 
                new org.springframework.boot.web.client.RestTemplateBuilder()
                    .setConnectTimeout(Duration.ofMillis(connectTimeout))
                    .setReadTimeout(Duration.ofMillis(readTimeout));
        this.restTemplate = builder.build();
        
        log.info("策略服务客户端初始化完成，连接超时: {}ms, 读取超时: {}ms", connectTimeout, readTimeout);
    }
    
    /**
     * 初始化虚拟线程执行器
     */
    @PostConstruct
    public void init() {
        // 创建虚拟线程执行器
        this.virtualThreadExecutor = Executors.newVirtualThreadPerTaskExecutor();
        log.info("策略服务客户端虚拟线程执行器已初始化");
    }
    
    /**
     * 销毁虚拟线程执行器
     */
    @PreDestroy
    public void destroy() {
        if (virtualThreadExecutor != null) {
            virtualThreadExecutor.shutdown();
            try {
                if (!virtualThreadExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                    virtualThreadExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                virtualThreadExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
            log.info("策略服务客户端虚拟线程执行器已关闭");
        }
    }
    
    /**
     * 获取下一个可用的服务URL
     * 使用ServiceRegistry获取健康的服务
     * 
     * @return 可用的服务URL，如果没有可用服务则返回null
     * @throws StrategyServiceException 如果没有可用的服务
     */
    private String getNextServiceUrl() throws StrategyServiceException {
        // 从服务注册表中获取健康的服务URL
        String serviceUrl = serviceRegistry.getHealthyServiceUrl();
        
        if (serviceUrl == null) {
            // 如果没有健康的服务，抛出异常
            throw new StrategyServiceException("没有健康的策略服务可用");
        }
        
        // 更新请求统计
        requestStats.compute(serviceUrl, (k, v) -> (v == null) ? 1L : v + 1L);
        
        return serviceUrl;
    }
    
    /**
     * 记录服务请求失败
     * 
     * @param serviceUrl 失败的服务URL
     */
    private void recordRequestFailure(String serviceUrl) {
        // 服务注册表会处理健康检查和故障转移
        // 这里只是为了记录失败的请求
        log.warn("服务请求失败: {}", serviceUrl);
    }
    
    /**
     * 获取策略服务状态
     * 
     * @return 服务状态信息
     * @throws StrategyServiceException 如果请求失败
     */
    @Retryable(value = {ResourceAccessException.class, StrategyServiceException.class}, 
               maxAttempts = 3, 
               backoff = @Backoff(delay = 1000, multiplier = 2))
    public JsonNode getStatus() throws StrategyServiceException {
        String serviceUrl = getNextServiceUrl();
        
        try {
            URI uri = UriComponentsBuilder.fromHttpUrl(serviceUrl)
                    .path("/status")
                    .build()
                    .toUri();
                    
            ResponseEntity<String> response = restTemplate.getForEntity(uri, String.class);
            
            if (response.getStatusCode().is2xxSuccessful()) {
                return objectMapper.readTree(response.getBody());
            } else {
                recordRequestFailure(serviceUrl);
                throw new StrategyServiceException("获取策略服务状态失败: " + response.getStatusCode());
            }
        } catch (ResourceAccessException e) {
            recordRequestFailure(serviceUrl);
            throw new StrategyServiceException("无法连接到策略服务: " + serviceUrl, e);
        } catch (StrategyServiceException e) {
            throw e;
        } catch (Exception e) {
            recordRequestFailure(serviceUrl);
            throw new StrategyServiceException("获取策略服务状态时发生错误", e);
        }
    }
    
    /**
     * 异步获取策略服务状态
     * 
     * @return 包含服务状态信息的CompletableFuture
     */
    public CompletableFuture<JsonNode> getStatusAsync() {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return getStatus();
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }, virtualThreadExecutor);
    }
    
    /**
     * 初始化策略服务
     * 
     * @return 初始化结果，如果成功则返回true，否则返回false
     * @throws StrategyServiceException 如果请求失败
     */
    @Retryable(value = {ResourceAccessException.class, StrategyServiceException.class}, 
               maxAttempts = 3, 
               backoff = @Backoff(delay = 1000, multiplier = 2))
    public boolean initializeService() throws StrategyServiceException {
        String serviceUrl = getNextServiceUrl();
        
        try {
            URI uri = UriComponentsBuilder.fromHttpUrl(serviceUrl)
                    .path("/init")
                    .build()
                    .toUri();
                    
            ResponseEntity<String> response = restTemplate.postForEntity(uri, null, String.class);
            
            if (response.getStatusCode().is2xxSuccessful()) {
                JsonNode jsonResponse = objectMapper.readTree(response.getBody());
                String status = jsonResponse.path("status").asText();
                
                return "success".equals(status);
            } else {
                recordRequestFailure(serviceUrl);
                throw new StrategyServiceException("初始化策略服务失败: " + response.getStatusCode());
            }
        } catch (ResourceAccessException e) {
            recordRequestFailure(serviceUrl);
            throw new StrategyServiceException("无法连接到策略服务: " + serviceUrl, e);
        } catch (StrategyServiceException e) {
            throw e;
        } catch (Exception e) {
            recordRequestFailure(serviceUrl);
            throw new StrategyServiceException("初始化策略服务时发生错误", e);
        }
    }
    
    /**
     * 异步初始化策略服务
     * 
     * @return 包含初始化结果的CompletableFuture
     */
    public CompletableFuture<Boolean> initializeServiceAsync() {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return initializeService();
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }, virtualThreadExecutor);
    }
    
    /**
     * 发送市场数据到策略服务
     * 
     * @param symbol 交易对符号
     * @param interval 间隔
     * @param data 市场数据
     * @return 如果数据发送成功则返回true，否则返回false
     * @throws StrategyServiceException 如果请求失败
     */
    @Retryable(value = {ResourceAccessException.class, StrategyServiceException.class}, 
               maxAttempts = 3, 
               backoff = @Backoff(delay = 1000, multiplier = 2))
    public boolean sendMarketData(String symbol, String interval, Object data) throws StrategyServiceException {
        String serviceUrl = getNextServiceUrl();
        
        try {
            URI uri = UriComponentsBuilder.fromHttpUrl(serviceUrl)
                    .path("/market-data")
                    .build()
                    .toUri();
                    
            // 构建请求体
            ObjectNode requestBody = objectMapper.createObjectNode();
            requestBody.put("symbol", symbol);
            requestBody.put("interval", interval);
            requestBody.set("data", objectMapper.valueToTree(data));
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<String> entity = new HttpEntity<>(objectMapper.writeValueAsString(requestBody), headers);
            
            ResponseEntity<String> response = restTemplate.postForEntity(uri, entity, String.class);
            
            if (response.getStatusCode().is2xxSuccessful()) {
                JsonNode jsonResponse = objectMapper.readTree(response.getBody());
                String status = jsonResponse.path("status").asText();
                
                if ("queue_full".equals(status)) {
                    log.warn("策略服务队列已满，可能需要扩展服务实例");
                }
                
                return "success".equals(status) || "queue_full".equals(status);
            } else {
                recordRequestFailure(serviceUrl);
                throw new StrategyServiceException("发送市场数据失败: " + response.getStatusCode());
            }
        } catch (ResourceAccessException e) {
            recordRequestFailure(serviceUrl);
            throw new StrategyServiceException("无法连接到策略服务: " + serviceUrl, e);
        } catch (StrategyServiceException e) {
            throw e;
        } catch (Exception e) {
            recordRequestFailure(serviceUrl);
            throw new StrategyServiceException("发送市场数据时发生错误", e);
        }
    }
    
    /**
     * 异步发送市场数据到策略服务
     * 
     * @param symbol 交易对符号
     * @param interval 间隔
     * @param data 市场数据
     * @return 包含发送结果的CompletableFuture
     */
    public CompletableFuture<Boolean> sendMarketDataAsync(String symbol, String interval, Object data) {
        // 使用虚拟线程执行器异步发送市场数据
        return CompletableFuture.supplyAsync(() -> {
            try {
                return sendMarketData(symbol, interval, data);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }, virtualThreadExecutor);
    }
    
    /**
     * 批量异步发送市场数据到策略服务
     * 
     * @param symbol 交易对符号
     * @param interval 间隔
     * @param dataList 市场数据列表
     * @return 包含发送结果的CompletableFuture列表
     */
    public java.util.List<CompletableFuture<Boolean>> sendMarketDataBatchAsync(String symbol, String interval, java.util.List<Object> dataList) {
        return dataList.stream()
                .map(data -> sendMarketDataAsync(symbol, interval, data))
                .collect(java.util.stream.Collectors.toList());
    }
    
    /**
     * 直接获取交易信号预测
     * 
     * @param symbol 交易对符号
     * @param interval 间隔
     * @param data 市场数据
     * @return 预测的交易信号
     * @throws StrategyServiceException 如果请求失败
     */
    @Retryable(value = {ResourceAccessException.class, StrategyServiceException.class}, 
               maxAttempts = 3, 
               backoff = @Backoff(delay = 1000, multiplier = 2))
    public JsonNode predictSignal(String symbol, String interval, Object data) throws StrategyServiceException {
        String serviceUrl = getNextServiceUrl();
        
        try {
            URI uri = UriComponentsBuilder.fromHttpUrl(serviceUrl)
                    .path("/predict")
                    .build()
                    .toUri();
                    
            // 构建请求体
            ObjectNode requestBody = objectMapper.createObjectNode();
            requestBody.put("symbol", symbol);
            requestBody.put("interval", interval);
            requestBody.set("data", objectMapper.valueToTree(data));
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<String> entity = new HttpEntity<>(objectMapper.writeValueAsString(requestBody), headers);
            
            ResponseEntity<String> response = restTemplate.postForEntity(uri, entity, String.class);
            
            if (response.getStatusCode().is2xxSuccessful()) {
                return objectMapper.readTree(response.getBody());
            } else {
                recordRequestFailure(serviceUrl);
                throw new StrategyServiceException("获取交易信号预测失败: " + response.getStatusCode());
            }
        } catch (ResourceAccessException e) {
            recordRequestFailure(serviceUrl);
            throw new StrategyServiceException("无法连接到策略服务: " + serviceUrl, e);
        } catch (StrategyServiceException e) {
            throw e;
        } catch (Exception e) {
            recordRequestFailure(serviceUrl);
            throw new StrategyServiceException("获取交易信号预测时发生错误", e);
        }
    }
    
    /**
     * 异步获取交易信号预测
     * 
     * @param symbol 交易对符号
     * @param interval 间隔
     * @param data 市场数据
     * @return 包含预测交易信号的CompletableFuture
     */
    public CompletableFuture<JsonNode> predictSignalAsync(String symbol, String interval, Object data) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return predictSignal(symbol, interval, data);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }, virtualThreadExecutor);
    }
    
    /**
     * 获取模型信息
     * 
     * @return 模型信息
     * @throws StrategyServiceException 如果请求失败
     */
    @Retryable(value = {ResourceAccessException.class, StrategyServiceException.class}, 
               maxAttempts = 3, 
               backoff = @Backoff(delay = 1000, multiplier = 2))
    public JsonNode getModelInfo() throws StrategyServiceException {
        String serviceUrl = getNextServiceUrl();
        
        try {
            URI uri = UriComponentsBuilder.fromHttpUrl(serviceUrl)
                    .path("/model-info")
                    .build()
                    .toUri();
                    
            ResponseEntity<String> response = restTemplate.getForEntity(uri, String.class);
            
            if (response.getStatusCode().is2xxSuccessful()) {
                return objectMapper.readTree(response.getBody());
            } else {
                recordRequestFailure(serviceUrl);
                throw new StrategyServiceException("获取模型信息失败: " + response.getStatusCode());
            }
        } catch (ResourceAccessException e) {
            recordRequestFailure(serviceUrl);
            throw new StrategyServiceException("无法连接到策略服务: " + serviceUrl, e);
        } catch (StrategyServiceException e) {
            throw e;
        } catch (Exception e) {
            recordRequestFailure(serviceUrl);
            throw new StrategyServiceException("获取模型信息时发生错误", e);
        }
    }
    
    /**
     * 异步获取模型信息
     * 
     * @return 包含模型信息的CompletableFuture
     */
    public CompletableFuture<JsonNode> getModelInfoAsync() {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return getModelInfo();
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }, virtualThreadExecutor);
    }
    
    /**
     * 关闭策略服务
     * 
     * @return 如果关闭请求发送成功则返回true，否则返回false
     * @throws StrategyServiceException 如果请求失败
     */
    @Retryable(value = {ResourceAccessException.class, StrategyServiceException.class}, 
               maxAttempts = 2, 
               backoff = @Backoff(delay = 1000))
    public boolean shutdownService() throws StrategyServiceException {
        String serviceUrl = getNextServiceUrl();
        
        try {
            URI uri = UriComponentsBuilder.fromHttpUrl(serviceUrl)
                    .path("/shutdown")
                    .build()
                    .toUri();
                    
            ResponseEntity<String> response = restTemplate.postForEntity(uri, null, String.class);
            
            return response.getStatusCode().is2xxSuccessful();
        } catch (ResourceAccessException e) {
            // 如果是连接拒绝，可能是服务已经关闭
            log.info("策略服务似乎已关闭: {}", serviceUrl);
            return true;
        } catch (StrategyServiceException e) {
            throw e;
        } catch (Exception e) {
            recordRequestFailure(serviceUrl);
            throw new StrategyServiceException("关闭策略服务时发生错误", e);
        }
    }
    
    /**
     * 异步关闭策略服务
     * 
     * @return 包含关闭结果的CompletableFuture
     */
    public CompletableFuture<Boolean> shutdownServiceAsync() {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return shutdownService();
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }, virtualThreadExecutor);
    }
    
    /**
     * 获取虚拟线程执行器
     * 
     * @return 虚拟线程执行器
     */
    public ExecutorService getVirtualThreadExecutor() {
        return virtualThreadExecutor;
    }
}