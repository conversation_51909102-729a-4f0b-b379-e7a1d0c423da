package com.crypto.trading.trade.controller;

import com.crypto.trading.trade.consumer.TradingSignalConsumer;
import com.crypto.trading.trade.model.entity.TradingSignalEntity;
import com.crypto.trading.trade.repository.mapper.TradingSignalMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 交易信号状态控制器
 * 
 * <p>提供查询交易信号状态的API接口</p>
 */
@RestController
@RequestMapping("/api/signals")
public class SignalStatusController {

    private static final Logger log = LoggerFactory.getLogger(SignalStatusController.class);

    @Autowired
    private TradingSignalMapper tradingSignalMapper;

    @Autowired
    private TradingSignalConsumer tradingSignalConsumer;

    /**
     * 获取交易信号消费统计
     *
     * @return 消费统计
     */
    @GetMapping("/stats")
    public Map<String, Object> getSignalStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalMessages", tradingSignalConsumer.getTotalMessages());
        stats.put("validMessages", tradingSignalConsumer.getValidMessages());
        stats.put("invalidMessages", tradingSignalConsumer.getInvalidMessages());
        stats.put("processedMessages", tradingSignalConsumer.getProcessedMessages());
        stats.put("failedMessages", tradingSignalConsumer.getFailedMessages());
        
        return stats;
    }

    /**
     * 根据信号ID查询交易信号
     *
     * @param signalId 信号ID
     * @return 交易信号
     */
    @GetMapping("/{signalId}")
    public TradingSignalEntity getSignalById(@PathVariable("signalId") String signalId) {
        TradingSignalEntity signal = tradingSignalMapper.findBySignalId(signalId);
        if (signal == null) {
            log.warn("未找到指定的交易信号: signalId={}", signalId);
        }
        return signal;
    }

    /**
     * 根据交易对和时间范围查询交易信号
     *
     * @param symbol 交易对
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 交易信号列表
     */
    @GetMapping("/search")
    public List<TradingSignalEntity> searchSignals(
            @RequestParam("symbol") String symbol,
            @RequestParam("startTime") long startTime,
            @RequestParam("endTime") long endTime) {
        
        log.info("查询交易信号: symbol={}, startTime={}, endTime={}", symbol, startTime, endTime);
        return tradingSignalMapper.findBySymbolAndTimeRange(symbol, startTime, endTime);
    }

    /**
     * 查询未处理的交易信号
     *
     * @param limit 限制数量
     * @return 未处理的交易信号列表
     */
    @GetMapping("/unprocessed")
    public List<TradingSignalEntity> getUnprocessedSignals(
            @RequestParam(value = "limit", defaultValue = "10") int limit) {
        
        log.info("查询未处理的交易信号: limit={}", limit);
        return tradingSignalMapper.findUnprocessedSignals(limit);
    }
}