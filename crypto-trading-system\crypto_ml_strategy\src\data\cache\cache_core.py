#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
实时缓存机制核心框架

为crypto_ml_strategy项目第3阶段任务3实现的高性能实时缓存系统核心组件。
提供内存高效的多时间框架数据缓存，支持快速访问和实时更新。

主要功能：
1. 缓存核心接口和基础类
2. 缓存键管理和数据结构
3. 性能监控和统计
4. 并发访问控制
5. 内存管理和优化
"""

import logging
import threading
import time
import hashlib
from typing import Dict, List, Tuple, Optional, Any, Union, Protocol, Generic, TypeVar
from datetime import datetime, timedelta
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from enum import Enum
import weakref
from collections import defaultdict, OrderedDict
import psutil
import gc

T = TypeVar('T')


class CacheStrategy(Enum):
    """缓存策略枚举"""
    LRU = "lru"          # 最近最少使用
    LFU = "lfu"          # 最少使用频率
    TTL = "ttl"          # 生存时间
    FIFO = "fifo"        # 先进先出
    ADAPTIVE = "adaptive" # 自适应策略


class CacheLevel(Enum):
    """缓存级别枚举"""
    L1_MEMORY = "l1_memory"      # L1内存缓存
    L2_PERSISTENT = "l2_persistent"  # L2持久化缓存
    L3_DISTRIBUTED = "l3_distributed"  # L3分布式缓存


@dataclass
class CacheKey:
    """缓存键"""
    symbol: str
    timeframe: str
    data_type: str  # 'ohlcv', 'indicators', 'features', 'quality'
    timestamp: Optional[int] = None
    version: str = "v1"
    
    def __post_init__(self):
        """验证缓存键"""
        if not self.symbol or not self.timeframe or not self.data_type:
            raise ValueError("缓存键的symbol、timeframe和data_type不能为空")
    
    def to_string(self) -> str:
        """转换为字符串键"""
        parts = [self.symbol, self.timeframe, self.data_type, self.version]
        if self.timestamp:
            parts.append(str(self.timestamp))
        return ":".join(parts)
    
    def to_hash(self) -> str:
        """转换为哈希键"""
        return hashlib.md5(self.to_string().encode()).hexdigest()
    
    @classmethod
    def from_string(cls, key_str: str) -> 'CacheKey':
        """从字符串创建缓存键"""
        parts = key_str.split(":")
        if len(parts) < 4:
            raise ValueError(f"无效的缓存键格式: {key_str}")
        
        timestamp = int(parts[4]) if len(parts) > 4 and parts[4].isdigit() else None
        
        return cls(
            symbol=parts[0],
            timeframe=parts[1],
            data_type=parts[2],
            version=parts[3],
            timestamp=timestamp
        )


@dataclass
class CacheEntry(Generic[T]):
    """缓存条目"""
    key: CacheKey
    value: T
    size_bytes: int
    created_at: datetime
    last_accessed: datetime
    access_count: int = 0
    ttl_seconds: Optional[int] = None
    
    def __post_init__(self):
        """初始化后处理"""
        if self.last_accessed is None:
            self.last_accessed = self.created_at
    
    def is_expired(self) -> bool:
        """检查是否过期"""
        if self.ttl_seconds is None:
            return False
        
        elapsed = (datetime.now() - self.created_at).total_seconds()
        return elapsed > self.ttl_seconds
    
    def touch(self):
        """更新访问时间和计数"""
        self.last_accessed = datetime.now()
        self.access_count += 1
    
    def age_seconds(self) -> float:
        """获取条目年龄（秒）"""
        return (datetime.now() - self.created_at).total_seconds()


@dataclass
class CacheStats:
    """缓存统计信息"""
    total_requests: int = 0
    cache_hits: int = 0
    cache_misses: int = 0
    evictions: int = 0
    memory_usage_bytes: int = 0
    entry_count: int = 0
    avg_access_time_ms: float = 0.0
    hit_ratio: float = 0.0
    last_update: datetime = field(default_factory=datetime.now)
    
    def update_hit_ratio(self):
        """更新命中率"""
        total = self.cache_hits + self.cache_misses
        self.hit_ratio = self.cache_hits / max(1, total)
        self.last_update = datetime.now()


class CacheBackend(Protocol[T]):
    """缓存后端协议"""
    
    def get(self, key: CacheKey) -> Optional[CacheEntry[T]]:
        """获取缓存条目"""
        ...
    
    def put(self, entry: CacheEntry[T]) -> bool:
        """存储缓存条目"""
        ...
    
    def remove(self, key: CacheKey) -> bool:
        """移除缓存条目"""
        ...
    
    def clear(self) -> int:
        """清空缓存"""
        ...
    
    def size(self) -> int:
        """获取缓存大小"""
        ...
    
    def keys(self) -> List[CacheKey]:
        """获取所有键"""
        ...


class CacheEvictionPolicy(Protocol):
    """缓存淘汰策略协议"""
    
    def should_evict(self, entry: CacheEntry, cache_size: int, max_size: int) -> bool:
        """判断是否应该淘汰条目"""
        ...
    
    def select_victims(self, entries: List[CacheEntry], count: int) -> List[CacheEntry]:
        """选择要淘汰的条目"""
        ...


@dataclass
class CacheConfig:
    """缓存配置"""
    # 基础配置
    max_memory_mb: int = 512
    max_entries: int = 10000
    default_ttl_seconds: int = 3600
    
    # 策略配置
    eviction_strategy: CacheStrategy = CacheStrategy.LRU
    cache_levels: List[CacheLevel] = field(default_factory=lambda: [CacheLevel.L1_MEMORY])
    
    # 性能配置
    enable_compression: bool = True
    enable_statistics: bool = True
    cleanup_interval_seconds: int = 300
    
    # 并发配置
    enable_concurrent_access: bool = True
    max_concurrent_operations: int = 100
    
    # 预加载配置
    enable_preloading: bool = True
    preload_popular_data: bool = True
    preload_threshold_access_count: int = 10


class RealTimeCacheManager:
    """实时缓存管理器"""
    
    def __init__(self, config: Optional[CacheConfig] = None):
        """
        初始化实时缓存管理器
        
        Args:
            config: 缓存配置
        """
        self.logger = logging.getLogger('cryptoMlStrategy.data.cache_core')
        self.config = config or CacheConfig()
        
        # 缓存后端存储
        self._backends: Dict[CacheLevel, CacheBackend] = {}
        
        # 淘汰策略
        self._eviction_policies: Dict[CacheStrategy, CacheEvictionPolicy] = {}
        
        # 统计信息
        self.stats = CacheStats()
        
        # 性能监控
        self._access_times = []
        self._last_cleanup = time.time()
        
        # 并发控制
        self._lock = threading.RLock()
        self._operation_semaphore = threading.Semaphore(self.config.max_concurrent_operations)
        
        # 内存监控
        self._memory_monitor_active = True
        self._start_memory_monitor()
        
        # 预加载缓存
        self._preload_cache: Dict[str, int] = defaultdict(int)  # 访问计数
        
        self.logger.info("实时缓存管理器初始化完成")
    
    def register_backend(self, level: CacheLevel, backend: CacheBackend):
        """
        注册缓存后端
        
        Args:
            level: 缓存级别
            backend: 缓存后端实例
        """
        try:
            self._backends[level] = backend
            self.logger.info(f"缓存后端注册成功: {level.value}")
        except Exception as e:
            self.logger.error(f"缓存后端注册失败: {e}")
    
    def register_eviction_policy(self, strategy: CacheStrategy, policy: CacheEvictionPolicy):
        """
        注册淘汰策略
        
        Args:
            strategy: 缓存策略
            policy: 淘汰策略实例
        """
        try:
            self._eviction_policies[strategy] = policy
            self.logger.info(f"淘汰策略注册成功: {strategy.value}")
        except Exception as e:
            self.logger.error(f"淘汰策略注册失败: {e}")
    
    def get(self, key: CacheKey, default: Optional[T] = None) -> Optional[T]:
        """
        获取缓存数据
        
        Args:
            key: 缓存键
            default: 默认值
            
        Returns:
            缓存的数据或默认值
        """
        start_time = time.time()
        
        try:
            with self._operation_semaphore:
                with self._lock:
                    self.stats.total_requests += 1
                
                # 按优先级查找缓存级别
                for level in self.config.cache_levels:
                    if level in self._backends:
                        backend = self._backends[level]
                        entry = backend.get(key)
                        
                        if entry is not None:
                            # 检查是否过期
                            if entry.is_expired():
                                backend.remove(key)
                                continue
                            
                            # 更新访问信息
                            entry.touch()
                            
                            # 更新统计
                            with self._lock:
                                self.stats.cache_hits += 1
                                self.stats.update_hit_ratio()
                            
                            # 更新预加载计数
                            self._update_preload_stats(key)
                            
                            # 记录访问时间
                            access_time = (time.time() - start_time) * 1000
                            self._record_access_time(access_time)
                            
                            self.logger.debug(f"缓存命中: {key.to_string()}, 级别: {level.value}")
                            return entry.value
                
                # 缓存未命中
                with self._lock:
                    self.stats.cache_misses += 1
                    self.stats.update_hit_ratio()
                
                self.logger.debug(f"缓存未命中: {key.to_string()}")
                return default
                
        except Exception as e:
            self.logger.error(f"缓存获取失败: {e}")
            return default
        finally:
            # 记录访问时间（即使失败）
            access_time = (time.time() - start_time) * 1000
            self._record_access_time(access_time)
    
    def put(self, key: CacheKey, value: T, ttl_seconds: Optional[int] = None) -> bool:
        """
        存储缓存数据
        
        Args:
            key: 缓存键
            value: 要缓存的数据
            ttl_seconds: 生存时间（秒）
            
        Returns:
            是否存储成功
        """
        try:
            with self._operation_semaphore:
                # 计算数据大小
                size_bytes = self._calculate_size(value)
                
                # 检查内存限制
                if not self._check_memory_limit(size_bytes):
                    self._evict_entries()
                
                # 创建缓存条目
                entry = CacheEntry(
                    key=key,
                    value=value,
                    size_bytes=size_bytes,
                    created_at=datetime.now(),
                    last_accessed=datetime.now(),
                    ttl_seconds=ttl_seconds or self.config.default_ttl_seconds
                )
                
                # 存储到所有配置的缓存级别
                success = False
                for level in self.config.cache_levels:
                    if level in self._backends:
                        backend = self._backends[level]
                        if backend.put(entry):
                            success = True
                
                if success:
                    with self._lock:
                        self.stats.entry_count += 1
                        self.stats.memory_usage_bytes += size_bytes
                    
                    self.logger.debug(f"缓存存储成功: {key.to_string()}")
                
                return success
                
        except Exception as e:
            self.logger.error(f"缓存存储失败: {e}")
            return False
    
    def remove(self, key: CacheKey) -> bool:
        """
        移除缓存数据
        
        Args:
            key: 缓存键
            
        Returns:
            是否移除成功
        """
        try:
            with self._operation_semaphore:
                success = False
                
                for level in self.config.cache_levels:
                    if level in self._backends:
                        backend = self._backends[level]
                        if backend.remove(key):
                            success = True
                
                if success:
                    with self._lock:
                        self.stats.entry_count = max(0, self.stats.entry_count - 1)
                    
                    self.logger.debug(f"缓存移除成功: {key.to_string()}")
                
                return success
                
        except Exception as e:
            self.logger.error(f"缓存移除失败: {e}")
            return False
    
    def clear(self) -> int:
        """
        清空所有缓存
        
        Returns:
            清除的条目数量
        """
        try:
            with self._operation_semaphore:
                total_cleared = 0
                
                for level in self.config.cache_levels:
                    if level in self._backends:
                        backend = self._backends[level]
                        cleared = backend.clear()
                        total_cleared += cleared
                
                with self._lock:
                    self.stats.entry_count = 0
                    self.stats.memory_usage_bytes = 0
                
                self.logger.info(f"缓存清空完成，清除了 {total_cleared} 个条目")
                return total_cleared
                
        except Exception as e:
            self.logger.error(f"缓存清空失败: {e}")
            return 0
    
    def _calculate_size(self, value: Any) -> int:
        """计算数据大小"""
        try:
            import sys
            return sys.getsizeof(value)
        except Exception:
            return 1024  # 默认1KB
    
    def _check_memory_limit(self, additional_bytes: int) -> bool:
        """检查内存限制"""
        max_bytes = self.config.max_memory_mb * 1024 * 1024
        return (self.stats.memory_usage_bytes + additional_bytes) <= max_bytes
    
    def _evict_entries(self):
        """淘汰缓存条目"""
        try:
            strategy = self.config.eviction_strategy
            if strategy not in self._eviction_policies:
                self.logger.warning(f"未找到淘汰策略: {strategy.value}")
                return
            
            policy = self._eviction_policies[strategy]
            
            # 收集所有条目
            all_entries = []
            for level in self.config.cache_levels:
                if level in self._backends:
                    backend = self._backends[level]
                    keys = backend.keys()
                    for key in keys:
                        entry = backend.get(key)
                        if entry:
                            all_entries.append(entry)
            
            # 选择要淘汰的条目
            evict_count = max(1, len(all_entries) // 10)  # 淘汰10%
            victims = policy.select_victims(all_entries, evict_count)
            
            # 执行淘汰
            evicted = 0
            for victim in victims:
                if self.remove(victim.key):
                    evicted += 1
            
            with self._lock:
                self.stats.evictions += evicted
            
            self.logger.info(f"缓存淘汰完成，淘汰了 {evicted} 个条目")
            
        except Exception as e:
            self.logger.error(f"缓存淘汰失败: {e}")
    
    def _update_preload_stats(self, key: CacheKey):
        """更新预加载统计"""
        if self.config.enable_preloading:
            key_str = key.to_string()
            self._preload_cache[key_str] += 1
    
    def _record_access_time(self, time_ms: float):
        """记录访问时间"""
        if self.config.enable_statistics:
            self._access_times.append(time_ms)
            
            # 保持最近1000次访问的记录
            if len(self._access_times) > 1000:
                self._access_times = self._access_times[-1000:]
            
            # 更新平均访问时间
            if self._access_times:
                self.stats.avg_access_time_ms = sum(self._access_times) / len(self._access_times)
    
    def _start_memory_monitor(self):
        """启动内存监控线程"""
        def monitor():
            while self._memory_monitor_active:
                try:
                    # 检查内存使用
                    process = psutil.Process()
                    memory_mb = process.memory_info().rss / 1024 / 1024
                    
                    # 如果超过限制，执行清理
                    if memory_mb > self.config.max_memory_mb * 1.2:  # 超过20%阈值
                        self._cleanup_expired_entries()
                        gc.collect()
                    
                    # 定期清理
                    if time.time() - self._last_cleanup > self.config.cleanup_interval_seconds:
                        self._cleanup_expired_entries()
                        self._last_cleanup = time.time()
                    
                    time.sleep(60)  # 每分钟检查一次
                    
                except Exception as e:
                    self.logger.error(f"内存监控错误: {e}")
                    time.sleep(300)  # 出错后5分钟重试
        
        monitor_thread = threading.Thread(target=monitor, daemon=True)
        monitor_thread.start()
    
    def _cleanup_expired_entries(self):
        """清理过期条目"""
        try:
            cleaned = 0
            
            for level in self.config.cache_levels:
                if level in self._backends:
                    backend = self._backends[level]
                    keys = backend.keys()
                    
                    for key in keys:
                        entry = backend.get(key)
                        if entry and entry.is_expired():
                            if backend.remove(key):
                                cleaned += 1
            
            if cleaned > 0:
                self.logger.info(f"清理了 {cleaned} 个过期缓存条目")
                
        except Exception as e:
            self.logger.error(f"清理过期条目失败: {e}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self._lock:
            return {
                'cache_stats': {
                    'total_requests': self.stats.total_requests,
                    'cache_hits': self.stats.cache_hits,
                    'cache_misses': self.stats.cache_misses,
                    'hit_ratio': self.stats.hit_ratio,
                    'evictions': self.stats.evictions,
                    'entry_count': self.stats.entry_count,
                    'memory_usage_mb': self.stats.memory_usage_bytes / 1024 / 1024,
                    'avg_access_time_ms': self.stats.avg_access_time_ms
                },
                'config': {
                    'max_memory_mb': self.config.max_memory_mb,
                    'max_entries': self.config.max_entries,
                    'eviction_strategy': self.config.eviction_strategy.value,
                    'cache_levels': [level.value for level in self.config.cache_levels]
                },
                'preload_stats': dict(self._preload_cache) if self.config.enable_preloading else {}
            }
    
    def shutdown(self):
        """关闭缓存管理器"""
        try:
            self._memory_monitor_active = False
            self.clear()
            self.logger.info("实时缓存管理器已关闭")
        except Exception as e:
            self.logger.error(f"关闭缓存管理器失败: {e}")