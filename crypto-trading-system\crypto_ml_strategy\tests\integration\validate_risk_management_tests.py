#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
风险管理测试验证脚本

验证所有风险管理测试组件是否正确安装和配置，
确保测试可以正常运行。

Author: Crypto ML Strategy Team
Date: 2024-12-19
"""

import sys
import os
import unittest
import logging
from datetime import datetime
from typing import Dict, List, Any

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'src'))

logger = logging.getLogger(__name__)


def validate_imports() -> Dict[str, bool]:
    """验证所有必要的导入"""
    validation_results = {}
    
    try:
        # 验证风险管理核心模块
        from risk_management import (
            RiskConfig, RiskEvent, RiskLevel, PositionInfo,
            RiskControlEngine, RiskAssessor, PositionManager,
            StopLossTakeProfitManager, DrawdownController,
            PositionSizeMethod, StopLossType, TakeProfitType
        )
        validation_results['risk_management_core'] = True
        logger.info("✅ 风险管理核心模块导入成功")
    except Exception as e:
        validation_results['risk_management_core'] = False
        logger.error(f"❌ 风险管理核心模块导入失败: {e}")
    
    try:
        # 验证测试套件
        from suites.risk_management_integration_test_suite import RiskManagementIntegrationTestSuite
        validation_results['risk_management_test_suite'] = True
        logger.info("✅ 风险管理集成测试套件导入成功")
    except Exception as e:
        validation_results['risk_management_test_suite'] = False
        logger.error(f"❌ 风险管理集成测试套件导入失败: {e}")
    
    try:
        # 验证专项测试
        from suites.test_risk_control_system import RiskControlSystemTest
        from suites.test_position_management import PositionManagementTest
        validation_results['specialized_tests'] = True
        logger.info("✅ 风险管理专项测试导入成功")
    except Exception as e:
        validation_results['specialized_tests'] = False
        logger.error(f"❌ 风险管理专项测试导入失败: {e}")
    
    try:
        # 验证测试辅助工具
        from utils.risk_management_test_helpers import (
            RiskScenarioGenerator, RiskPerformanceMonitor, RiskTestExecutor
        )
        validation_results['test_helpers'] = True
        logger.info("✅ 风险管理测试辅助工具导入成功")
    except Exception as e:
        validation_results['test_helpers'] = False
        logger.error(f"❌ 风险管理测试辅助工具导入失败: {e}")
    
    try:
        # 验证测试运行器
        from run_risk_management_tests import RiskManagementTestRunner
        validation_results['test_runner'] = True
        logger.info("✅ 风险管理测试运行器导入成功")
    except Exception as e:
        validation_results['test_runner'] = False
        logger.error(f"❌ 风险管理测试运行器导入失败: {e}")
    
    return validation_results


def validate_test_suite_structure() -> Dict[str, bool]:
    """验证测试套件结构"""
    validation_results = {}
    
    try:
        from suites.risk_management_integration_test_suite import RiskManagementIntegrationTestSuite
        
        # 检查测试方法
        test_methods = [method for method in dir(RiskManagementIntegrationTestSuite) 
                       if method.startswith('test_')]
        
        expected_methods = [
            'test_01_risk_control_engine_initialization',
            'test_02_risk_control_engine_lifecycle',
            'test_03_market_data_integration',
            'test_04_portfolio_data_integration',
            'test_05_position_management_integration',
            'test_06_stop_loss_take_profit_integration',
            'test_07_drawdown_control_integration',
            'test_08_emergency_risk_handling',
            'test_09_risk_profile_configurations',
            'test_10_comprehensive_risk_workflow'
        ]
        
        missing_methods = [method for method in expected_methods if method not in test_methods]
        
        if not missing_methods:
            validation_results['test_suite_methods'] = True
            logger.info(f"✅ 风险管理测试套件包含所有预期方法 ({len(test_methods)} 个)")
        else:
            validation_results['test_suite_methods'] = False
            logger.error(f"❌ 风险管理测试套件缺少方法: {missing_methods}")
    
    except Exception as e:
        validation_results['test_suite_methods'] = False
        logger.error(f"❌ 验证测试套件结构失败: {e}")
    
    return validation_results


def validate_specialized_tests() -> Dict[str, bool]:
    """验证专项测试"""
    validation_results = {}
    
    try:
        from suites.test_risk_control_system import RiskControlSystemTest
        
        risk_control_methods = [method for method in dir(RiskControlSystemTest) 
                               if method.startswith('test_')]
        
        if len(risk_control_methods) >= 5:
            validation_results['risk_control_tests'] = True
            logger.info(f"✅ 风险控制系统测试包含 {len(risk_control_methods)} 个测试方法")
        else:
            validation_results['risk_control_tests'] = False
            logger.error(f"❌ 风险控制系统测试方法不足: {len(risk_control_methods)}")
    
    except Exception as e:
        validation_results['risk_control_tests'] = False
        logger.error(f"❌ 验证风险控制系统测试失败: {e}")
    
    try:
        from suites.test_position_management import PositionManagementTest
        
        position_mgmt_methods = [method for method in dir(PositionManagementTest) 
                                if method.startswith('test_')]
        
        if len(position_mgmt_methods) >= 5:
            validation_results['position_management_tests'] = True
            logger.info(f"✅ 仓位管理测试包含 {len(position_mgmt_methods)} 个测试方法")
        else:
            validation_results['position_management_tests'] = False
            logger.error(f"❌ 仓位管理测试方法不足: {len(position_mgmt_methods)}")
    
    except Exception as e:
        validation_results['position_management_tests'] = False
        logger.error(f"❌ 验证仓位管理测试失败: {e}")
    
    return validation_results


def validate_test_helpers() -> Dict[str, bool]:
    """验证测试辅助工具"""
    validation_results = {}
    
    try:
        from utils.risk_management_test_helpers import RiskScenarioGenerator
        
        generator = RiskScenarioGenerator()
        scenarios = generator.get_all_scenarios()
        
        if len(scenarios) >= 4:
            validation_results['scenario_generator'] = True
            logger.info(f"✅ 风险场景生成器包含 {len(scenarios)} 个场景")
        else:
            validation_results['scenario_generator'] = False
            logger.error(f"❌ 风险场景生成器场景不足: {len(scenarios)}")
    
    except Exception as e:
        validation_results['scenario_generator'] = False
        logger.error(f"❌ 验证风险场景生成器失败: {e}")
    
    try:
        from utils.risk_management_test_helpers import RiskPerformanceMonitor
        
        monitor = RiskPerformanceMonitor()
        # 验证基本方法存在
        required_methods = ['start_monitoring', 'stop_monitoring', 'record_response_time']
        
        missing_methods = [method for method in required_methods 
                          if not hasattr(monitor, method)]
        
        if not missing_methods:
            validation_results['performance_monitor'] = True
            logger.info("✅ 风险性能监控器包含所有必要方法")
        else:
            validation_results['performance_monitor'] = False
            logger.error(f"❌ 风险性能监控器缺少方法: {missing_methods}")
    
    except Exception as e:
        validation_results['performance_monitor'] = False
        logger.error(f"❌ 验证风险性能监控器失败: {e}")
    
    try:
        from utils.risk_management_test_helpers import RiskTestExecutor
        
        executor = RiskTestExecutor()
        # 验证基本方法存在
        required_methods = ['execute_scenario', 'execute_all_scenarios', 'generate_test_report']
        
        missing_methods = [method for method in required_methods 
                          if not hasattr(executor, method)]
        
        if not missing_methods:
            validation_results['test_executor'] = True
            logger.info("✅ 风险测试执行器包含所有必要方法")
        else:
            validation_results['test_executor'] = False
            logger.error(f"❌ 风险测试执行器缺少方法: {missing_methods}")
    
    except Exception as e:
        validation_results['test_executor'] = False
        logger.error(f"❌ 验证风险测试执行器失败: {e}")
    
    return validation_results


def run_quick_test() -> Dict[str, bool]:
    """运行快速测试验证"""
    validation_results = {}
    
    try:
        # 快速测试风险管理配置
        from risk_management import RiskConfig, PositionSizeMethod, StopLossType, TakeProfitType
        
        config = RiskConfig(
            max_single_position_ratio=0.1,
            max_total_position_ratio=0.8,
            position_size_method=PositionSizeMethod.ADAPTIVE,
            stop_loss_type=StopLossType.ADAPTIVE,
            take_profit_type=TakeProfitType.RISK_REWARD_RATIO
        )
        
        validation_results['risk_config_creation'] = True
        logger.info("✅ 风险管理配置创建成功")
    
    except Exception as e:
        validation_results['risk_config_creation'] = False
        logger.error(f"❌ 风险管理配置创建失败: {e}")
    
    try:
        # 快速测试风险控制引擎
        from risk_management import RiskControlEngine, RiskConfig
        
        config = RiskConfig()
        engine = RiskControlEngine(config)
        
        # 验证基本属性
        assert hasattr(engine, 'is_running')
        assert hasattr(engine, 'emergency_mode')
        assert hasattr(engine, 'trading_suspended')
        assert hasattr(engine, 'start')
        assert hasattr(engine, 'stop')
        
        validation_results['risk_engine_creation'] = True
        logger.info("✅ 风险控制引擎创建成功")
    
    except Exception as e:
        validation_results['risk_engine_creation'] = False
        logger.error(f"❌ 风险控制引擎创建失败: {e}")
    
    return validation_results


def main():
    """主函数"""
    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    logger.info("开始验证风险管理测试组件...")
    logger.info("="*60)
    
    # 执行各项验证
    all_results = {}
    
    # 验证导入
    logger.info("1. 验证模块导入...")
    import_results = validate_imports()
    all_results.update(import_results)
    
    # 验证测试套件结构
    logger.info("\n2. 验证测试套件结构...")
    structure_results = validate_test_suite_structure()
    all_results.update(structure_results)
    
    # 验证专项测试
    logger.info("\n3. 验证专项测试...")
    specialized_results = validate_specialized_tests()
    all_results.update(specialized_results)
    
    # 验证测试辅助工具
    logger.info("\n4. 验证测试辅助工具...")
    helper_results = validate_test_helpers()
    all_results.update(helper_results)
    
    # 运行快速测试
    logger.info("\n5. 运行快速测试...")
    quick_test_results = run_quick_test()
    all_results.update(quick_test_results)
    
    # 生成验证报告
    logger.info("\n" + "="*60)
    logger.info("验证结果摘要:")
    logger.info("="*60)
    
    passed_count = sum(1 for result in all_results.values() if result)
    total_count = len(all_results)
    success_rate = (passed_count / total_count) * 100 if total_count > 0 else 0
    
    for test_name, result in all_results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"{test_name}: {status}")
    
    logger.info("="*60)
    logger.info(f"总计: {passed_count}/{total_count} 通过 ({success_rate:.1f}%)")
    
    if success_rate >= 90:
        logger.info("🎉 验证成功！风险管理测试组件已准备就绪。")
        return 0
    elif success_rate >= 70:
        logger.warning("⚠️  验证部分通过，存在一些问题需要修复。")
        return 1
    else:
        logger.error("❌ 验证失败，存在严重问题需要立即修复。")
        return 2


if __name__ == '__main__':
    sys.exit(main())
