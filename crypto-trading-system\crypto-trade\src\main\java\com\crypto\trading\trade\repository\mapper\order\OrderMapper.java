package com.crypto.trading.trade.repository.mapper.order;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.crypto.trading.trade.model.entity.order.OrderEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 订单数据访问接口
 * 
 * <p>提供订单的CRUD操作和自定义查询</p>
 */
@Mapper
public interface OrderMapper extends BaseMapper<OrderEntity> {

    /**
     * 根据订单ID查询订单
     *
     * @param orderId 订单ID
     * @return 订单
     */
    @Select("SELECT * FROM t_order WHERE order_id = #{orderId} AND is_deleted = 0")
    OrderEntity findByOrderId(@Param("orderId") String orderId);

    /**
     * 根据客户端订单ID查询订单
     *
     * @param clientOrderId 客户端订单ID
     * @return 订单
     */
    @Select("SELECT * FROM t_order WHERE client_order_id = #{clientOrderId} AND is_deleted = 0")
    OrderEntity findByClientOrderId(@Param("clientOrderId") String clientOrderId);

    /**
     * 根据信号ID查询订单
     *
     * @param signalId 信号ID
     * @return 订单列表
     */
    @Select("SELECT * FROM t_order WHERE signal_id = #{signalId} AND is_deleted = 0")
    List<OrderEntity> findBySignalId(@Param("signalId") String signalId);

    /**
     * 根据交易对和时间范围查询订单
     *
     * @param symbol 交易对
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 订单列表
     */
    @Select("SELECT * FROM t_order WHERE symbol = #{symbol} AND created_time BETWEEN #{startTime} AND #{endTime} AND is_deleted = 0")
    List<OrderEntity> findBySymbolAndTimeRange(
            @Param("symbol") String symbol, 
            @Param("startTime") long startTime, 
            @Param("endTime") long endTime);

    /**
     * 根据状态查询订单
     *
     * @param status 订单状态
     * @param limit 限制数量
     * @return 订单列表
     */
    @Select("SELECT * FROM t_order WHERE status = #{status} AND is_deleted = 0 ORDER BY created_time DESC LIMIT #{limit}")
    List<OrderEntity> findByStatus(@Param("status") String status, @Param("limit") int limit);

    /**
     * 更新订单状态
     *
     * @param orderId 订单ID
     * @param status 订单状态
     * @param updatedTime 更新时间
     * @return 影响的行数
     */
    @Update("UPDATE t_order SET status = #{status}, updated_time = #{updatedTime} WHERE order_id = #{orderId}")
    int updateOrderStatus(
            @Param("orderId") String orderId, 
            @Param("status") String status, 
            @Param("updatedTime") long updatedTime);

    /**
     * 更新订单执行结果
     *
     * @param orderId 订单ID
     * @param executedQuantity 已执行数量
     * @param executedPrice 执行价格
     * @param status 订单状态
     * @param executedTime 执行时间
     * @param updatedTime 更新时间
     * @return 影响的行数
     */
    @Update("UPDATE t_order SET executed_quantity = #{executedQuantity}, executed_price = #{executedPrice}, " +
            "status = #{status}, executed_time = #{executedTime}, updated_time = #{updatedTime} " +
            "WHERE order_id = #{orderId}")
    int updateOrderExecution(
            @Param("orderId") String orderId, 
            @Param("executedQuantity") java.math.BigDecimal executedQuantity, 
            @Param("executedPrice") java.math.BigDecimal executedPrice, 
            @Param("status") String status, 
            @Param("executedTime") long executedTime, 
            @Param("updatedTime") long updatedTime);

    /**
     * 更新订单错误信息
     *
     * @param orderId 订单ID
     * @param errorCode 错误代码
     * @param errorMessage 错误信息
     * @param status 订单状态
     * @param updatedTime 更新时间
     * @return 影响的行数
     */
    @Update("UPDATE t_order SET error_code = #{errorCode}, error_message = #{errorMessage}, " +
            "status = #{status}, updated_time = #{updatedTime} " +
            "WHERE order_id = #{orderId}")
    int updateOrderError(
            @Param("orderId") String orderId, 
            @Param("errorCode") String errorCode, 
            @Param("errorMessage") String errorMessage, 
            @Param("status") String status, 
            @Param("updatedTime") long updatedTime);
            
    /**
     * 根据时间范围查询订单
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 订单列表
     */
    @Select("SELECT * FROM t_order WHERE created_time BETWEEN #{startTime} AND #{endTime} AND is_deleted = 0 ORDER BY created_time")
    List<OrderEntity> findByTimeRange(
            @Param("startTime") long startTime, 
            @Param("endTime") long endTime);
            
    /**
     * 查询指定时间之前创建的订单
     *
     * @param createdTimeBefore 创建时间上限
     * @return 订单列表
     */
    @Select("SELECT * FROM t_order WHERE created_time < #{createdTimeBefore} AND is_deleted = 0")
    List<OrderEntity> findByCreatedTimeBefore(@Param("createdTimeBefore") long createdTimeBefore);
}