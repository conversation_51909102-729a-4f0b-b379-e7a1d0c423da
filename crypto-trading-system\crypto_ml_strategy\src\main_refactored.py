#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
重构后的主应用模块

该模块使用依赖注入架构重构原有的TradingStrategyApp，
提供更好的模块化、测试能力和可扩展性。

现已重构为外观模式协调器，委托给新创建的模块处理具体逻辑，
保持100%向后兼容性。
"""

import sys
import os
import time
import asyncio
from typing import Dict, Any, Optional
from loguru import logger

# 添加src目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from infrastructure.dependency_setup import EnhancedDependencySetup
from services.message_handlers import UnifiedMessageHandlers
from infrastructure.lifecycle_management import EnhancedLifecycleManager
from core.main_app import TradingStrategyApp
from infrastructure.dependency_container import get_container
from infrastructure.service_interfaces import *


class RefactoredTradingStrategyApp:
    """重构后的交易策略应用（外观模式协调器），委托给新创建的模块处理具体逻辑，保持向后兼容性。"""
    
    def __init__(self, config_path: Optional[str] = None):
        """初始化交易策略应用"""
        self.config_path = config_path
        self.core_app = TradingStrategyApp(config_path)
        
        # 模块实例（延迟初始化）
        self.dependency_setup: Optional[EnhancedDependencySetup] = None
        self.message_handlers: Optional[UnifiedMessageHandlers] = None
        self.lifecycle_manager: Optional[EnhancedLifecycleManager] = None
        
        # 应用状态（向后兼容）
        self._running = False
        self._initialized = False
        
        # 服务引用（向后兼容）
        self.container = get_container()
        self.config_service: Optional[IConfigService] = None
        self.logger_service: Optional[ILoggerService] = None
        self.kafka_service: Optional[IKafkaService] = None
        self.data_processor: Optional[IDataProcessorService] = None
        self.strategy_service: Optional[IStrategyService] = None
        self.prediction_engine: Optional[IPredictionEngineService] = None
        self.health_check_service: Optional[IHealthCheckService] = None
        self.metrics_service: Optional[IMetricsService] = None
        
        logger.info("重构后的交易策略应用创建完成")
    
    def initialize(self) -> None:
        """初始化应用"""
        if self._initialized:
            logger.warning("应用已经初始化")
            return
        
        try:
            logger.info("开始初始化交易策略应用...")
            
            # 设置增强依赖服务
            self.dependency_setup = EnhancedDependencySetup(self.container)
            services = self.dependency_setup.setup_enhanced_services()
            
            # 解析核心服务（向后兼容）
            self._resolve_core_services()
            
            # 创建消息处理器
            self.message_handlers = UnifiedMessageHandlers(
                cache_service=services['cache_service'],
                technical_indicator_service=services['technical_indicator_service'],
                prediction_engine_service=services['prediction_engine_service'],
                async_error_recovery=services['async_error_recovery'],
                data_processor=self.data_processor,
                strategy_service=self.strategy_service,
                kafka_service=self.kafka_service,
                metrics_service=self.metrics_service
            )
            
            # 创建增强生命周期管理器
            self.lifecycle_manager = EnhancedLifecycleManager(
                memory_monitor=services['memory_monitor'],
                async_error_recovery=services['async_error_recovery'],
                health_check_service=self.health_check_service,
                metrics_service=self.metrics_service
            )
            
            self._initialized = True
            logger.info("交易策略应用初始化完成")
            
        except Exception as e:
            logger.error(f"应用初始化失败: {e}")
            raise
    
    def _resolve_core_services(self) -> None:
        """解析核心服务（向后兼容）"""
        try:
            self.config_service = self.container.resolve(IConfigService)
            self.logger_service = self.container.resolve(ILoggerService)
            self.kafka_service = self.container.resolve(IKafkaService)
            self.data_processor = self.container.resolve(IDataProcessorService)
            self.strategy_service = self.container.resolve(IStrategyService)
            self.prediction_engine = self.container.resolve(IPredictionEngineService)
            self.health_check_service = self.container.resolve(IHealthCheckService)
            self.metrics_service = self.container.resolve(IMetricsService)
            logger.info("核心服务解析完成")
        except Exception as e:
            logger.error(f"核心服务解析失败: {e}")
            raise
    
    def start(self) -> None:
        """启动应用"""
        if not self._initialized:
            raise RuntimeError("应用未初始化，请先调用 initialize()")
        if self._running:
            logger.warning("应用已经在运行")
            return
        
        try:
            logger.info("启动交易策略应用...")
            
            # 委托给生命周期管理器启动
            asyncio.create_task(self.lifecycle_manager.start_application())
            
            # 启动Kafka消费者（集成新的消息处理器）
            self._start_kafka_consumers()
            
            self._running = True
            logger.info("交易策略应用启动成功")
            self.metrics_service.increment_counter("app_startup_count")
            
        except Exception as e:
            logger.error(f"应用启动失败: {e}")
            self._running = False
            raise
    
    def _start_kafka_consumers(self) -> None:
        """启动Kafka消费者（集成新的消息处理器）"""
        try:
            # 获取Kafka主题配置
            kline_topic = self.config_service.get('kafka', 'kline_topic')
            depth_topic = self.config_service.get('kafka', 'depth_topic')
            trade_topic = self.config_service.get('kafka', 'trade_topic')
            
            # 启动消费者，使用新的消息处理器
            self.kafka_service.start_consuming(kline_topic, self.message_handlers.handle_kline_message)
            self.kafka_service.start_consuming(depth_topic, self.message_handlers.handle_depth_message)
            self.kafka_service.start_consuming(trade_topic, self.message_handlers.handle_trade_message)
            
            logger.info("Kafka消费者启动完成（集成增强消息处理器）")
        except Exception as e:
            logger.error(f"Kafka消费者启动失败: {e}")
            raise
    
    def stop(self) -> None:
        """停止应用"""
        if not self._running:
            logger.warning("应用未在运行")
            return
        
        try:
            logger.info("停止交易策略应用...")
            
            # 委托给生命周期管理器停止
            if self.lifecycle_manager:
                asyncio.create_task(self.lifecycle_manager.stop_application())
            
            # 停止Kafka消费者
            if self.kafka_service:
                self.kafka_service.stop_consuming()
            
            self._running = False
            logger.info("交易策略应用已停止")
            self.metrics_service.increment_counter("app_shutdown_count")
            
        except Exception as e:
            logger.error(f"应用停止失败: {e}")
            raise
    
    def cleanup(self) -> None:
        """清理应用资源"""
        try:
            logger.info("清理应用资源...")
            if self.kafka_service:
                self.kafka_service.clean_up()
            logger.info("应用资源清理完成")
        except Exception as e:
            logger.error(f"应用资源清理失败: {e}")
    
    def get_status(self) -> Dict[str, Any]:
        """获取应用状态（聚合各模块状态）"""
        status = {
            "initialized": self._initialized,
            "running": self._running,
            "timestamp": time.time()
        }
        
        # 聚合生命周期管理器状态
        if self.lifecycle_manager:
            try:
                status["lifecycle"] = self.lifecycle_manager.get_status()
            except Exception as e:
                status["lifecycle_error"] = str(e)
        
        # 聚合健康检查状态
        if self.health_check_service:
            try:
                status["health"] = self.health_check_service.check_health()
            except Exception as e:
                status["health_error"] = str(e)
        
        # 聚合指标服务状态
        if self.metrics_service:
            try:
                status["metrics"] = self.metrics_service.get_metrics_summary()
            except Exception as e:
                status["metrics_error"] = str(e)
        
        return status
    
    async def run(self) -> None:
        """运行应用（异步版本，委托给核心应用）"""
        await self.core_app.run()
    
    def run_sync(self) -> None:
        """运行应用（同步版本，向后兼容）"""
        try:
            self.initialize()
            self.start()
            logger.info("应用运行中，按 Ctrl+C 停止...")
            
            # 主循环
            while self._running:
                try:
                    time.sleep(1)
                    # 定期记录健康状态
                    if int(time.time()) % 60 == 0:  # 每分钟一次
                        health_status = self.health_check_service.check_health()
                        if health_status.get("overall_healthy", False):
                            self.metrics_service.increment_counter("health_check_passed")
                        else:
                            self.metrics_service.increment_counter("health_check_failed")
                            logger.warning(f"健康检查失败: {health_status}")
                
                except KeyboardInterrupt:
                    logger.info("收到键盘中断信号")
                    break
                except Exception as e:
                    logger.error(f"主循环异常: {e}")
                    self.metrics_service.increment_counter("main_loop_errors")
        finally:
            self.stop()
            self.cleanup()


def main():
    """主函数"""
    try:
        config_path = None
        if len(sys.argv) > 1:
            config_path = sys.argv[1]
        
        app = RefactoredTradingStrategyApp(config_path)
        app.run_sync()
        
    except Exception as e:
        logger.error(f"应用运行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()