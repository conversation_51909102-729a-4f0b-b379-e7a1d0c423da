package com.crypto.trading.bootstrap.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.crypto.trading.sdk.client.BinanceApiClient;
import com.crypto.trading.sdk.client.UMFuturesApiClientImpl;
import com.crypto.trading.sdk.client.CMFuturesApiClientImpl;
import com.crypto.trading.sdk.config.BinanceApiConfig;
import com.crypto.trading.sdk.converter.ModelConverter;
import com.crypto.trading.sdk.limiter.BinanceRateLimiter;

/**
 * 币安API客户端配置类
 * 负责注册各种币安API客户端的Bean
 */
@Configuration
public class BinanceClientConfig {
    
    private static final Logger log = LoggerFactory.getLogger(BinanceClientConfig.class);
    
    @Autowired
    private BinanceApiConfig binanceApiConfig;
    
    @Autowired
    private BinanceRateLimiter binanceRateLimiter;
    
    @Autowired
    private ModelConverter modelConverter;
    
    /**
     * 注册USDT保证金期货API客户端Bean
     * 
     * @return USDT保证金期货API客户端实例
     */
    @Bean(name = "umFuturesApiClient")
    public BinanceApiClient umFuturesApiClient() {
        log.info("注册USDT保证金期货API客户端Bean(umFuturesApiClient)");
        return new UMFuturesApiClientImpl(binanceApiConfig, modelConverter);
    }
    
    /**
     * 注册币本位保证金期货API客户端Bean
     * 
     * @return 币本位保证金期货API客户端实例
     */
    @Bean(name = "cmFuturesApiClient")
    public BinanceApiClient cmFuturesApiClient() {
        log.info("注册币本位保证金期货API客户端Bean(cmFuturesApiClient)");
        return new CMFuturesApiClientImpl(binanceApiConfig);
    }
} 