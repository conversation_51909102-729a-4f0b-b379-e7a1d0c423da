<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report-3.0.xsd" version="3.0" name="com.crypto.trading.trade.executor.BinanceTradeExecutorTest" time="0.525" tests="6" errors="0" skipped="0" failures="0">
  <properties>
    <property name="java.specification.version" value="21"/>
    <property name="sun.cpu.isalist" value="amd64"/>
    <property name="sun.jnu.encoding" value="GBK"/>
    <property name="java.class.path" value="D:\1_deep_bian\crypto-trading-system\crypto-trade\target\test-classes;D:\1_deep_bian\crypto-trading-system\crypto-trade\target\classes;D:\1_deep_bian\crypto-trading-system\crypto-common\target\crypto-common-1.0.0-SNAPSHOT.jar;D:\maven_repository\org\springframework\boot\spring-boot-starter-jdbc\3.2.0\spring-boot-starter-jdbc-3.2.0.jar;D:\maven_repository\com\zaxxer\HikariCP\5.0.1\HikariCP-5.0.1.jar;D:\maven_repository\org\springframework\spring-jdbc\6.1.1\spring-jdbc-6.1.1.jar;D:\maven_repository\io\confluent\kafka-schema-registry-client\7.5.3\kafka-schema-registry-client-7.5.3.jar;D:\maven_repository\io\swagger\core\v3\swagger-annotations\2.1.10\swagger-annotations-2.1.10.jar;D:\maven_repository\com\fasterxml\jackson\dataformat\jackson-dataformat-yaml\2.15.3\jackson-dataformat-yaml-2.15.3.jar;D:\maven_repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.15.3\jackson-datatype-jsr310-2.15.3.jar;D:\1_deep_bian\crypto-trading-system\crypto-sdk\target\crypto-sdk-1.0.0-SNAPSHOT.jar;D:\maven_repository\org\java-websocket\Java-WebSocket\1.5.3\Java-WebSocket-1.5.3.jar;D:\maven_repository\javax\annotation\javax.annotation-api\1.3.2\javax.annotation-api-1.3.2.jar;D:\maven_repository\org\springframework\boot\spring-boot-starter-web\3.2.0\spring-boot-starter-web-3.2.0.jar;D:\maven_repository\org\springframework\boot\spring-boot-starter-json\3.2.0\spring-boot-starter-json-3.2.0.jar;D:\maven_repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.15.3\jackson-datatype-jdk8-2.15.3.jar;D:\maven_repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.15.3\jackson-module-parameter-names-2.15.3.jar;D:\maven_repository\org\springframework\boot\spring-boot-starter-tomcat\3.2.0\spring-boot-starter-tomcat-3.2.0.jar;D:\maven_repository\org\apache\tomcat\embed\tomcat-embed-core\10.1.16\tomcat-embed-core-10.1.16.jar;D:\maven_repository\org\apache\tomcat\embed\tomcat-embed-websocket\10.1.16\tomcat-embed-websocket-10.1.16.jar;D:\maven_repository\org\springframework\spring-web\6.1.1\spring-web-6.1.1.jar;D:\maven_repository\org\springframework\spring-beans\6.1.1\spring-beans-6.1.1.jar;D:\maven_repository\org\springframework\spring-webmvc\6.1.1\spring-webmvc-6.1.1.jar;D:\maven_repository\org\springframework\spring-aop\6.1.1\spring-aop-6.1.1.jar;D:\maven_repository\org\springframework\spring-expression\6.1.1\spring-expression-6.1.1.jar;D:\maven_repository\org\springframework\boot\spring-boot-starter-actuator\3.2.0\spring-boot-starter-actuator-3.2.0.jar;D:\maven_repository\org\springframework\boot\spring-boot-actuator-autoconfigure\3.2.0\spring-boot-actuator-autoconfigure-3.2.0.jar;D:\maven_repository\org\springframework\boot\spring-boot-actuator\3.2.0\spring-boot-actuator-3.2.0.jar;D:\maven_repository\io\micrometer\micrometer-observation\1.12.0\micrometer-observation-1.12.0.jar;D:\maven_repository\io\micrometer\micrometer-commons\1.12.0\micrometer-commons-1.12.0.jar;D:\maven_repository\io\micrometer\micrometer-jakarta9\1.12.0\micrometer-jakarta9-1.12.0.jar;D:\maven_repository\io\micrometer\micrometer-core\1.12.0\micrometer-core-1.12.0.jar;D:\maven_repository\org\hdrhistogram\HdrHistogram\2.1.12\HdrHistogram-2.1.12.jar;D:\maven_repository\org\latencyutils\LatencyUtils\2.0.3\LatencyUtils-2.0.3.jar;D:\maven_repository\org\springframework\boot\spring-boot-starter-validation\3.2.0\spring-boot-starter-validation-3.2.0.jar;D:\maven_repository\org\apache\tomcat\embed\tomcat-embed-el\10.1.16\tomcat-embed-el-10.1.16.jar;D:\maven_repository\org\hibernate\validator\hibernate-validator\8.0.1.Final\hibernate-validator-8.0.1.Final.jar;D:\maven_repository\jakarta\validation\jakarta.validation-api\3.0.2\jakarta.validation-api-3.0.2.jar;D:\maven_repository\org\jboss\logging\jboss-logging\3.5.3.Final\jboss-logging-3.5.3.Final.jar;D:\maven_repository\com\fasterxml\classmate\1.6.0\classmate-1.6.0.jar;D:\maven_repository\javax\validation\validation-api\2.0.1.Final\validation-api-2.0.1.Final.jar;D:\maven_repository\org\springframework\kafka\spring-kafka\3.2.0\spring-kafka-3.2.0.jar;D:\maven_repository\org\springframework\spring-context\6.1.1\spring-context-6.1.1.jar;D:\maven_repository\org\springframework\spring-messaging\6.1.1\spring-messaging-6.1.1.jar;D:\maven_repository\org\springframework\spring-tx\6.1.1\spring-tx-6.1.1.jar;D:\maven_repository\org\springframework\retry\spring-retry\2.0.4\spring-retry-2.0.4.jar;D:\maven_repository\org\apache\kafka\kafka-clients\3.5.1\kafka-clients-3.5.1.jar;D:\maven_repository\com\github\luben\zstd-jni\1.5.5-1\zstd-jni-1.5.5-1.jar;D:\maven_repository\org\lz4\lz4-java\1.8.0\lz4-java-1.8.0.jar;D:\maven_repository\org\xerial\snappy\snappy-java\1.1.10.1\snappy-java-1.1.10.1.jar;D:\maven_repository\com\mysql\mysql-connector-j\8.0.33\mysql-connector-j-8.0.33.jar;D:\maven_repository\com\google\protobuf\protobuf-java\3.21.9\protobuf-java-3.21.9.jar;D:\maven_repository\com\github\jsqlparser\jsqlparser\4.6\jsqlparser-4.6.jar;D:\maven_repository\io\github\binance\binance-futures-connector-java\3.0.5\binance-futures-connector-java-3.0.5.jar;D:\maven_repository\com\squareup\okhttp3\okhttp\4.12.0\okhttp-4.12.0.jar;D:\maven_repository\com\squareup\okio\okio\3.6.0\okio-3.6.0.jar;D:\maven_repository\com\squareup\okio\okio-jvm\3.6.0\okio-jvm-3.6.0.jar;D:\maven_repository\org\jetbrains\kotlin\kotlin-stdlib-common\1.9.20\kotlin-stdlib-common-1.9.20.jar;D:\maven_repository\org\jetbrains\kotlin\kotlin-stdlib-jdk8\1.9.20\kotlin-stdlib-jdk8-1.9.20.jar;D:\maven_repository\org\jetbrains\kotlin\kotlin-stdlib\1.9.20\kotlin-stdlib-1.9.20.jar;D:\maven_repository\org\jetbrains\annotations\13.0\annotations-13.0.jar;D:\maven_repository\org\jetbrains\kotlin\kotlin-stdlib-jdk7\1.9.20\kotlin-stdlib-jdk7-1.9.20.jar;D:\maven_repository\commons-codec\commons-codec\1.16.0\commons-codec-1.16.0.jar;D:\maven_repository\org\json\json\20231013\json-20231013.jar;D:\maven_repository\io\github\resilience4j\resilience4j-ratelimiter\2.1.0\resilience4j-ratelimiter-2.1.0.jar;D:\maven_repository\io\github\resilience4j\resilience4j-core\2.1.0\resilience4j-core-2.1.0.jar;D:\maven_repository\io\github\resilience4j\resilience4j-spring-boot3\2.1.0\resilience4j-spring-boot3-2.1.0.jar;D:\maven_repository\io\github\resilience4j\resilience4j-spring6\2.1.0\resilience4j-spring6-2.1.0.jar;D:\maven_repository\io\github\resilience4j\resilience4j-annotations\2.1.0\resilience4j-annotations-2.1.0.jar;D:\maven_repository\io\github\resilience4j\resilience4j-consumer\2.1.0\resilience4j-consumer-2.1.0.jar;D:\maven_repository\io\github\resilience4j\resilience4j-circularbuffer\2.1.0\resilience4j-circularbuffer-2.1.0.jar;D:\maven_repository\io\github\resilience4j\resilience4j-framework-common\2.1.0\resilience4j-framework-common-2.1.0.jar;D:\maven_repository\io\github\resilience4j\resilience4j-micrometer\2.1.0\resilience4j-micrometer-2.1.0.jar;D:\maven_repository\io\github\resilience4j\resilience4j-bulkhead\2.1.0\resilience4j-bulkhead-2.1.0.jar;D:\maven_repository\io\github\resilience4j\resilience4j-circuitbreaker\2.1.0\resilience4j-circuitbreaker-2.1.0.jar;D:\maven_repository\io\github\resilience4j\resilience4j-retry\2.1.0\resilience4j-retry-2.1.0.jar;D:\maven_repository\io\github\resilience4j\resilience4j-timelimiter\2.1.0\resilience4j-timelimiter-2.1.0.jar;D:\maven_repository\org\apache\avro\avro\1.11.3\avro-1.11.3.jar;D:\maven_repository\com\fasterxml\jackson\core\jackson-core\2.15.3\jackson-core-2.15.3.jar;D:\maven_repository\org\apache\commons\commons-compress\1.22\commons-compress-1.22.jar;D:\maven_repository\io\confluent\kafka-avro-serializer\7.5.3\kafka-avro-serializer-7.5.3.jar;D:\maven_repository\io\confluent\kafka-schema-serializer\7.5.3\kafka-schema-serializer-7.5.3.jar;D:\maven_repository\com\fasterxml\jackson\dataformat\jackson-dataformat-csv\2.15.3\jackson-dataformat-csv-2.15.3.jar;D:\maven_repository\io\confluent\logredactor\1.0.12\logredactor-1.0.12.jar;D:\maven_repository\com\google\re2j\re2j\1.6\re2j-1.6.jar;D:\maven_repository\io\confluent\logredactor-metrics\1.0.12\logredactor-metrics-1.0.12.jar;D:\maven_repository\com\eclipsesource\minimal-json\minimal-json\0.9.5\minimal-json-0.9.5.jar;D:\maven_repository\io\confluent\common-utils\7.5.3\common-utils-7.5.3.jar;D:\maven_repository\com\alibaba\fastjson2\fastjson2\2.0.41\fastjson2-2.0.41.jar;D:\maven_repository\org\springframework\kafka\spring-kafka-test\3.1.0\spring-kafka-test-3.1.0.jar;D:\maven_repository\org\springframework\spring-test\6.1.1\spring-test-6.1.1.jar;D:\maven_repository\org\apache\zookeeper\zookeeper\3.8.3\zookeeper-3.8.3.jar;D:\maven_repository\org\apache\zookeeper\zookeeper-jute\3.8.3\zookeeper-jute-3.8.3.jar;D:\maven_repository\org\apache\yetus\audience-annotations\0.12.0\audience-annotations-0.12.0.jar;D:\maven_repository\io\netty\netty-handler\4.1.101.Final\netty-handler-4.1.101.Final.jar;D:\maven_repository\io\netty\netty-common\4.1.101.Final\netty-common-4.1.101.Final.jar;D:\maven_repository\io\netty\netty-resolver\4.1.101.Final\netty-resolver-4.1.101.Final.jar;D:\maven_repository\io\netty\netty-buffer\4.1.101.Final\netty-buffer-4.1.101.Final.jar;D:\maven_repository\io\netty\netty-transport\4.1.101.Final\netty-transport-4.1.101.Final.jar;D:\maven_repository\io\netty\netty-transport-native-unix-common\4.1.101.Final\netty-transport-native-unix-common-4.1.101.Final.jar;D:\maven_repository\io\netty\netty-codec\4.1.101.Final\netty-codec-4.1.101.Final.jar;D:\maven_repository\io\netty\netty-transport-native-epoll\4.1.101.Final\netty-transport-native-epoll-4.1.101.Final.jar;D:\maven_repository\io\netty\netty-transport-classes-epoll\4.1.101.Final\netty-transport-classes-epoll-4.1.101.Final.jar;D:\maven_repository\org\apache\kafka\kafka-clients\3.6.0\kafka-clients-3.6.0-test.jar;D:\maven_repository\org\apache\kafka\kafka-metadata\3.6.0\kafka-metadata-3.6.0.jar;D:\maven_repository\org\apache\kafka\kafka-raft\3.6.0\kafka-raft-3.6.0.jar;D:\maven_repository\com\yammer\metrics\metrics-core\2.2.0\metrics-core-2.2.0.jar;D:\maven_repository\org\apache\kafka\kafka-server-common\3.6.0\kafka-server-common-3.6.0.jar;D:\maven_repository\net\sf\jopt-simple\jopt-simple\5.0.4\jopt-simple-5.0.4.jar;D:\maven_repository\org\pcollections\pcollections\4.0.1\pcollections-4.0.1.jar;D:\maven_repository\org\apache\kafka\kafka-server-common\3.6.0\kafka-server-common-3.6.0-test.jar;D:\maven_repository\org\apache\kafka\kafka-streams-test-utils\3.6.0\kafka-streams-test-utils-3.6.0.jar;D:\maven_repository\org\apache\kafka\kafka-streams\3.6.0\kafka-streams-3.6.0.jar;D:\maven_repository\org\rocksdb\rocksdbjni\7.9.2\rocksdbjni-7.9.2.jar;D:\maven_repository\org\apache\kafka\kafka_2.13\3.6.0\kafka_2.13-3.6.0.jar;D:\maven_repository\org\scala-lang\scala-library\2.13.11\scala-library-2.13.11.jar;D:\maven_repository\org\apache\kafka\kafka-group-coordinator\3.6.0\kafka-group-coordinator-3.6.0.jar;D:\maven_repository\org\apache\kafka\kafka-storage-api\3.6.0\kafka-storage-api-3.6.0.jar;D:\maven_repository\org\apache\kafka\kafka-tools-api\3.6.0\kafka-tools-api-3.6.0.jar;D:\maven_repository\org\apache\kafka\kafka-storage\3.6.0\kafka-storage-3.6.0.jar;D:\maven_repository\com\github\ben-manes\caffeine\caffeine\3.1.8\caffeine-3.1.8.jar;D:\maven_repository\net\sourceforge\argparse4j\argparse4j\0.7.0\argparse4j-0.7.0.jar;D:\maven_repository\commons-validator\commons-validator\1.7\commons-validator-1.7.jar;D:\maven_repository\commons-beanutils\commons-beanutils\1.9.4\commons-beanutils-1.9.4.jar;D:\maven_repository\commons-digester\commons-digester\2.1\commons-digester-2.1.jar;D:\maven_repository\commons-collections\commons-collections\3.2.2\commons-collections-3.2.2.jar;D:\maven_repository\com\fasterxml\jackson\module\jackson-module-scala_2.13\2.15.3\jackson-module-scala_2.13-2.15.3.jar;D:\maven_repository\com\thoughtworks\paranamer\paranamer\2.8\paranamer-2.8.jar;D:\maven_repository\org\bitbucket\b_c\jose4j\0.9.3\jose4j-0.9.3.jar;D:\maven_repository\org\scala-lang\modules\scala-collection-compat_2.13\2.10.0\scala-collection-compat_2.13-2.10.0.jar;D:\maven_repository\org\scala-lang\modules\scala-java8-compat_2.13\1.0.2\scala-java8-compat_2.13-1.0.2.jar;D:\maven_repository\org\scala-lang\scala-reflect\2.13.11\scala-reflect-2.13.11.jar;D:\maven_repository\com\typesafe\scala-logging\scala-logging_2.13\3.9.4\scala-logging_2.13-3.9.4.jar;D:\maven_repository\io\dropwizard\metrics\metrics-core\4.2.22\metrics-core-4.2.22.jar;D:\maven_repository\commons-cli\commons-cli\1.4\commons-cli-1.4.jar;D:\maven_repository\org\apache\kafka\kafka_2.13\3.6.0\kafka_2.13-3.6.0-test.jar;D:\maven_repository\org\junit\platform\junit-platform-launcher\1.10.1\junit-platform-launcher-1.10.1.jar;D:\maven_repository\com\google\code\findbugs\jsr305\3.0.2\jsr305-3.0.2.jar;D:\maven_repository\org\mockito\mockito-junit-jupiter\5.7.0\mockito-junit-jupiter-5.7.0.jar;D:\maven_repository\org\springframework\boot\spring-boot-starter\3.2.0\spring-boot-starter-3.2.0.jar;D:\maven_repository\org\springframework\boot\spring-boot\3.2.0\spring-boot-3.2.0.jar;D:\maven_repository\org\springframework\boot\spring-boot-autoconfigure\3.2.0\spring-boot-autoconfigure-3.2.0.jar;D:\maven_repository\org\springframework\boot\spring-boot-starter-logging\3.2.0\spring-boot-starter-logging-3.2.0.jar;D:\maven_repository\org\apache\logging\log4j\log4j-to-slf4j\2.21.1\log4j-to-slf4j-2.21.1.jar;D:\maven_repository\org\apache\logging\log4j\log4j-api\2.21.1\log4j-api-2.21.1.jar;D:\maven_repository\org\slf4j\jul-to-slf4j\2.0.9\jul-to-slf4j-2.0.9.jar;D:\maven_repository\org\springframework\spring-core\6.1.1\spring-core-6.1.1.jar;D:\maven_repository\org\springframework\spring-jcl\6.1.1\spring-jcl-6.1.1.jar;D:\maven_repository\org\yaml\snakeyaml\2.2\snakeyaml-2.2.jar;D:\maven_repository\com\baomidou\mybatis-plus-spring-boot3-starter\3.5.11\mybatis-plus-spring-boot3-starter-3.5.11.jar;D:\maven_repository\com\baomidou\mybatis-plus\3.5.11\mybatis-plus-3.5.11.jar;D:\maven_repository\com\baomidou\mybatis-plus-annotation\3.5.11\mybatis-plus-annotation-3.5.11.jar;D:\maven_repository\com\baomidou\mybatis-plus-spring\3.5.11\mybatis-plus-spring-3.5.11.jar;D:\maven_repository\org\mybatis\mybatis\3.5.19\mybatis-3.5.19.jar;D:\maven_repository\org\mybatis\mybatis-spring\3.0.4\mybatis-spring-3.0.4.jar;D:\maven_repository\com\baomidou\mybatis-plus-spring-boot-autoconfigure\3.5.11\mybatis-plus-spring-boot-autoconfigure-3.5.11.jar;D:\maven_repository\com\baomidou\mybatis-plus-extension\3.5.11\mybatis-plus-extension-3.5.11.jar;D:\maven_repository\com\baomidou\mybatis-plus-core\3.5.11\mybatis-plus-core-3.5.11.jar;D:\maven_repository\org\slf4j\slf4j-api\2.0.9\slf4j-api-2.0.9.jar;D:\maven_repository\ch\qos\logback\logback-classic\1.4.11\logback-classic-1.4.11.jar;D:\maven_repository\ch\qos\logback\logback-core\1.4.11\logback-core-1.4.11.jar;D:\maven_repository\org\apache\commons\commons-lang3\3.13.0\commons-lang3-3.13.0.jar;D:\maven_repository\commons-io\commons-io\2.15.0\commons-io-2.15.0.jar;D:\maven_repository\com\google\guava\guava\32.1.2-jre\guava-32.1.2-jre.jar;D:\maven_repository\com\google\guava\failureaccess\1.0.1\failureaccess-1.0.1.jar;D:\maven_repository\com\google\guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar;D:\maven_repository\org\checkerframework\checker-qual\3.33.0\checker-qual-3.33.0.jar;D:\maven_repository\com\google\errorprone\error_prone_annotations\2.18.0\error_prone_annotations-2.18.0.jar;D:\maven_repository\com\google\j2objc\j2objc-annotations\2.8\j2objc-annotations-2.8.jar;D:\maven_repository\com\fasterxml\jackson\core\jackson-databind\2.15.3\jackson-databind-2.15.3.jar;D:\maven_repository\com\fasterxml\jackson\core\jackson-annotations\2.15.3\jackson-annotations-2.15.3.jar;D:\maven_repository\jakarta\annotation\jakarta.annotation-api\2.1.1\jakarta.annotation-api-2.1.1.jar;D:\maven_repository\org\projectlombok\lombok\1.18.30\lombok-1.18.30.jar;D:\maven_repository\org\junit\jupiter\junit-jupiter-api\5.10.0\junit-jupiter-api-5.10.0.jar;D:\maven_repository\org\opentest4j\opentest4j\1.3.0\opentest4j-1.3.0.jar;D:\maven_repository\org\junit\platform\junit-platform-commons\1.10.1\junit-platform-commons-1.10.1.jar;D:\maven_repository\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;D:\maven_repository\org\junit\jupiter\junit-jupiter-engine\5.10.0\junit-jupiter-engine-5.10.0.jar;D:\maven_repository\org\junit\platform\junit-platform-engine\1.10.1\junit-platform-engine-1.10.1.jar;D:\maven_repository\org\springframework\boot\spring-boot-starter-test\3.2.0\spring-boot-starter-test-3.2.0.jar;D:\maven_repository\org\springframework\boot\spring-boot-test\3.2.0\spring-boot-test-3.2.0.jar;D:\maven_repository\org\springframework\boot\spring-boot-test-autoconfigure\3.2.0\spring-boot-test-autoconfigure-3.2.0.jar;D:\maven_repository\com\jayway\jsonpath\json-path\2.8.0\json-path-2.8.0.jar;D:\maven_repository\jakarta\xml\bind\jakarta.xml.bind-api\4.0.1\jakarta.xml.bind-api-4.0.1.jar;D:\maven_repository\jakarta\activation\jakarta.activation-api\2.1.2\jakarta.activation-api-2.1.2.jar;D:\maven_repository\net\minidev\json-smart\2.5.0\json-smart-2.5.0.jar;D:\maven_repository\net\minidev\accessors-smart\2.5.0\accessors-smart-2.5.0.jar;D:\maven_repository\org\ow2\asm\asm\9.3\asm-9.3.jar;D:\maven_repository\org\assertj\assertj-core\3.24.2\assertj-core-3.24.2.jar;D:\maven_repository\org\awaitility\awaitility\4.2.0\awaitility-4.2.0.jar;D:\maven_repository\org\hamcrest\hamcrest\2.2\hamcrest-2.2.jar;D:\maven_repository\org\junit\jupiter\junit-jupiter\5.10.1\junit-jupiter-5.10.1.jar;D:\maven_repository\org\junit\jupiter\junit-jupiter-params\5.10.1\junit-jupiter-params-5.10.1.jar;D:\maven_repository\org\skyscreamer\jsonassert\1.5.1\jsonassert-1.5.1.jar;D:\maven_repository\com\vaadin\external\google\android-json\0.0.20131108.vaadin1\android-json-0.0.20131108.vaadin1.jar;D:\maven_repository\org\xmlunit\xmlunit-core\2.9.1\xmlunit-core-2.9.1.jar;D:\maven_repository\org\mockito\mockito-core\4.8.0\mockito-core-4.8.0.jar;D:\maven_repository\net\bytebuddy\byte-buddy\1.14.10\byte-buddy-1.14.10.jar;D:\maven_repository\net\bytebuddy\byte-buddy-agent\1.14.10\byte-buddy-agent-1.14.10.jar;D:\maven_repository\org\objenesis\objenesis\3.2\objenesis-3.2.jar;"/>
    <property name="java.vm.vendor" value="Amazon.com Inc."/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="user.variant" value=""/>
    <property name="java.vendor.url" value="https://aws.amazon.com/corretto/"/>
    <property name="user.timezone" value="Asia/Shanghai"/>
    <property name="os.name" value="Windows 11"/>
    <property name="java.vm.specification.version" value="21"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="user.country" value="CN"/>
    <property name="sun.boot.library.path" value="C:\Users\<USER>\.jdks\corretto-21.0.7\bin"/>
    <property name="sun.java.command" value="C:\Users\<USER>\AppData\Local\Temp\surefire10965909004826728704\surefirebooter-20250622185319706_123.jar C:\Users\<USER>\AppData\Local\Temp\surefire10965909004826728704 2025-06-22T18-51-45_188-jvmRun1 surefire-20250622185319706_121tmp surefire_4-20250622185319706_122tmp"/>
    <property name="jdk.debug" value="release"/>
    <property name="surefire.test.class.path" value="D:\1_deep_bian\crypto-trading-system\crypto-trade\target\test-classes;D:\1_deep_bian\crypto-trading-system\crypto-trade\target\classes;D:\1_deep_bian\crypto-trading-system\crypto-common\target\crypto-common-1.0.0-SNAPSHOT.jar;D:\maven_repository\org\springframework\boot\spring-boot-starter-jdbc\3.2.0\spring-boot-starter-jdbc-3.2.0.jar;D:\maven_repository\com\zaxxer\HikariCP\5.0.1\HikariCP-5.0.1.jar;D:\maven_repository\org\springframework\spring-jdbc\6.1.1\spring-jdbc-6.1.1.jar;D:\maven_repository\io\confluent\kafka-schema-registry-client\7.5.3\kafka-schema-registry-client-7.5.3.jar;D:\maven_repository\io\swagger\core\v3\swagger-annotations\2.1.10\swagger-annotations-2.1.10.jar;D:\maven_repository\com\fasterxml\jackson\dataformat\jackson-dataformat-yaml\2.15.3\jackson-dataformat-yaml-2.15.3.jar;D:\maven_repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.15.3\jackson-datatype-jsr310-2.15.3.jar;D:\1_deep_bian\crypto-trading-system\crypto-sdk\target\crypto-sdk-1.0.0-SNAPSHOT.jar;D:\maven_repository\org\java-websocket\Java-WebSocket\1.5.3\Java-WebSocket-1.5.3.jar;D:\maven_repository\javax\annotation\javax.annotation-api\1.3.2\javax.annotation-api-1.3.2.jar;D:\maven_repository\org\springframework\boot\spring-boot-starter-web\3.2.0\spring-boot-starter-web-3.2.0.jar;D:\maven_repository\org\springframework\boot\spring-boot-starter-json\3.2.0\spring-boot-starter-json-3.2.0.jar;D:\maven_repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.15.3\jackson-datatype-jdk8-2.15.3.jar;D:\maven_repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.15.3\jackson-module-parameter-names-2.15.3.jar;D:\maven_repository\org\springframework\boot\spring-boot-starter-tomcat\3.2.0\spring-boot-starter-tomcat-3.2.0.jar;D:\maven_repository\org\apache\tomcat\embed\tomcat-embed-core\10.1.16\tomcat-embed-core-10.1.16.jar;D:\maven_repository\org\apache\tomcat\embed\tomcat-embed-websocket\10.1.16\tomcat-embed-websocket-10.1.16.jar;D:\maven_repository\org\springframework\spring-web\6.1.1\spring-web-6.1.1.jar;D:\maven_repository\org\springframework\spring-beans\6.1.1\spring-beans-6.1.1.jar;D:\maven_repository\org\springframework\spring-webmvc\6.1.1\spring-webmvc-6.1.1.jar;D:\maven_repository\org\springframework\spring-aop\6.1.1\spring-aop-6.1.1.jar;D:\maven_repository\org\springframework\spring-expression\6.1.1\spring-expression-6.1.1.jar;D:\maven_repository\org\springframework\boot\spring-boot-starter-actuator\3.2.0\spring-boot-starter-actuator-3.2.0.jar;D:\maven_repository\org\springframework\boot\spring-boot-actuator-autoconfigure\3.2.0\spring-boot-actuator-autoconfigure-3.2.0.jar;D:\maven_repository\org\springframework\boot\spring-boot-actuator\3.2.0\spring-boot-actuator-3.2.0.jar;D:\maven_repository\io\micrometer\micrometer-observation\1.12.0\micrometer-observation-1.12.0.jar;D:\maven_repository\io\micrometer\micrometer-commons\1.12.0\micrometer-commons-1.12.0.jar;D:\maven_repository\io\micrometer\micrometer-jakarta9\1.12.0\micrometer-jakarta9-1.12.0.jar;D:\maven_repository\io\micrometer\micrometer-core\1.12.0\micrometer-core-1.12.0.jar;D:\maven_repository\org\hdrhistogram\HdrHistogram\2.1.12\HdrHistogram-2.1.12.jar;D:\maven_repository\org\latencyutils\LatencyUtils\2.0.3\LatencyUtils-2.0.3.jar;D:\maven_repository\org\springframework\boot\spring-boot-starter-validation\3.2.0\spring-boot-starter-validation-3.2.0.jar;D:\maven_repository\org\apache\tomcat\embed\tomcat-embed-el\10.1.16\tomcat-embed-el-10.1.16.jar;D:\maven_repository\org\hibernate\validator\hibernate-validator\8.0.1.Final\hibernate-validator-8.0.1.Final.jar;D:\maven_repository\jakarta\validation\jakarta.validation-api\3.0.2\jakarta.validation-api-3.0.2.jar;D:\maven_repository\org\jboss\logging\jboss-logging\3.5.3.Final\jboss-logging-3.5.3.Final.jar;D:\maven_repository\com\fasterxml\classmate\1.6.0\classmate-1.6.0.jar;D:\maven_repository\javax\validation\validation-api\2.0.1.Final\validation-api-2.0.1.Final.jar;D:\maven_repository\org\springframework\kafka\spring-kafka\3.2.0\spring-kafka-3.2.0.jar;D:\maven_repository\org\springframework\spring-context\6.1.1\spring-context-6.1.1.jar;D:\maven_repository\org\springframework\spring-messaging\6.1.1\spring-messaging-6.1.1.jar;D:\maven_repository\org\springframework\spring-tx\6.1.1\spring-tx-6.1.1.jar;D:\maven_repository\org\springframework\retry\spring-retry\2.0.4\spring-retry-2.0.4.jar;D:\maven_repository\org\apache\kafka\kafka-clients\3.5.1\kafka-clients-3.5.1.jar;D:\maven_repository\com\github\luben\zstd-jni\1.5.5-1\zstd-jni-1.5.5-1.jar;D:\maven_repository\org\lz4\lz4-java\1.8.0\lz4-java-1.8.0.jar;D:\maven_repository\org\xerial\snappy\snappy-java\1.1.10.1\snappy-java-1.1.10.1.jar;D:\maven_repository\com\mysql\mysql-connector-j\8.0.33\mysql-connector-j-8.0.33.jar;D:\maven_repository\com\google\protobuf\protobuf-java\3.21.9\protobuf-java-3.21.9.jar;D:\maven_repository\com\github\jsqlparser\jsqlparser\4.6\jsqlparser-4.6.jar;D:\maven_repository\io\github\binance\binance-futures-connector-java\3.0.5\binance-futures-connector-java-3.0.5.jar;D:\maven_repository\com\squareup\okhttp3\okhttp\4.12.0\okhttp-4.12.0.jar;D:\maven_repository\com\squareup\okio\okio\3.6.0\okio-3.6.0.jar;D:\maven_repository\com\squareup\okio\okio-jvm\3.6.0\okio-jvm-3.6.0.jar;D:\maven_repository\org\jetbrains\kotlin\kotlin-stdlib-common\1.9.20\kotlin-stdlib-common-1.9.20.jar;D:\maven_repository\org\jetbrains\kotlin\kotlin-stdlib-jdk8\1.9.20\kotlin-stdlib-jdk8-1.9.20.jar;D:\maven_repository\org\jetbrains\kotlin\kotlin-stdlib\1.9.20\kotlin-stdlib-1.9.20.jar;D:\maven_repository\org\jetbrains\annotations\13.0\annotations-13.0.jar;D:\maven_repository\org\jetbrains\kotlin\kotlin-stdlib-jdk7\1.9.20\kotlin-stdlib-jdk7-1.9.20.jar;D:\maven_repository\commons-codec\commons-codec\1.16.0\commons-codec-1.16.0.jar;D:\maven_repository\org\json\json\20231013\json-20231013.jar;D:\maven_repository\io\github\resilience4j\resilience4j-ratelimiter\2.1.0\resilience4j-ratelimiter-2.1.0.jar;D:\maven_repository\io\github\resilience4j\resilience4j-core\2.1.0\resilience4j-core-2.1.0.jar;D:\maven_repository\io\github\resilience4j\resilience4j-spring-boot3\2.1.0\resilience4j-spring-boot3-2.1.0.jar;D:\maven_repository\io\github\resilience4j\resilience4j-spring6\2.1.0\resilience4j-spring6-2.1.0.jar;D:\maven_repository\io\github\resilience4j\resilience4j-annotations\2.1.0\resilience4j-annotations-2.1.0.jar;D:\maven_repository\io\github\resilience4j\resilience4j-consumer\2.1.0\resilience4j-consumer-2.1.0.jar;D:\maven_repository\io\github\resilience4j\resilience4j-circularbuffer\2.1.0\resilience4j-circularbuffer-2.1.0.jar;D:\maven_repository\io\github\resilience4j\resilience4j-framework-common\2.1.0\resilience4j-framework-common-2.1.0.jar;D:\maven_repository\io\github\resilience4j\resilience4j-micrometer\2.1.0\resilience4j-micrometer-2.1.0.jar;D:\maven_repository\io\github\resilience4j\resilience4j-bulkhead\2.1.0\resilience4j-bulkhead-2.1.0.jar;D:\maven_repository\io\github\resilience4j\resilience4j-circuitbreaker\2.1.0\resilience4j-circuitbreaker-2.1.0.jar;D:\maven_repository\io\github\resilience4j\resilience4j-retry\2.1.0\resilience4j-retry-2.1.0.jar;D:\maven_repository\io\github\resilience4j\resilience4j-timelimiter\2.1.0\resilience4j-timelimiter-2.1.0.jar;D:\maven_repository\org\apache\avro\avro\1.11.3\avro-1.11.3.jar;D:\maven_repository\com\fasterxml\jackson\core\jackson-core\2.15.3\jackson-core-2.15.3.jar;D:\maven_repository\org\apache\commons\commons-compress\1.22\commons-compress-1.22.jar;D:\maven_repository\io\confluent\kafka-avro-serializer\7.5.3\kafka-avro-serializer-7.5.3.jar;D:\maven_repository\io\confluent\kafka-schema-serializer\7.5.3\kafka-schema-serializer-7.5.3.jar;D:\maven_repository\com\fasterxml\jackson\dataformat\jackson-dataformat-csv\2.15.3\jackson-dataformat-csv-2.15.3.jar;D:\maven_repository\io\confluent\logredactor\1.0.12\logredactor-1.0.12.jar;D:\maven_repository\com\google\re2j\re2j\1.6\re2j-1.6.jar;D:\maven_repository\io\confluent\logredactor-metrics\1.0.12\logredactor-metrics-1.0.12.jar;D:\maven_repository\com\eclipsesource\minimal-json\minimal-json\0.9.5\minimal-json-0.9.5.jar;D:\maven_repository\io\confluent\common-utils\7.5.3\common-utils-7.5.3.jar;D:\maven_repository\com\alibaba\fastjson2\fastjson2\2.0.41\fastjson2-2.0.41.jar;D:\maven_repository\org\springframework\kafka\spring-kafka-test\3.1.0\spring-kafka-test-3.1.0.jar;D:\maven_repository\org\springframework\spring-test\6.1.1\spring-test-6.1.1.jar;D:\maven_repository\org\apache\zookeeper\zookeeper\3.8.3\zookeeper-3.8.3.jar;D:\maven_repository\org\apache\zookeeper\zookeeper-jute\3.8.3\zookeeper-jute-3.8.3.jar;D:\maven_repository\org\apache\yetus\audience-annotations\0.12.0\audience-annotations-0.12.0.jar;D:\maven_repository\io\netty\netty-handler\4.1.101.Final\netty-handler-4.1.101.Final.jar;D:\maven_repository\io\netty\netty-common\4.1.101.Final\netty-common-4.1.101.Final.jar;D:\maven_repository\io\netty\netty-resolver\4.1.101.Final\netty-resolver-4.1.101.Final.jar;D:\maven_repository\io\netty\netty-buffer\4.1.101.Final\netty-buffer-4.1.101.Final.jar;D:\maven_repository\io\netty\netty-transport\4.1.101.Final\netty-transport-4.1.101.Final.jar;D:\maven_repository\io\netty\netty-transport-native-unix-common\4.1.101.Final\netty-transport-native-unix-common-4.1.101.Final.jar;D:\maven_repository\io\netty\netty-codec\4.1.101.Final\netty-codec-4.1.101.Final.jar;D:\maven_repository\io\netty\netty-transport-native-epoll\4.1.101.Final\netty-transport-native-epoll-4.1.101.Final.jar;D:\maven_repository\io\netty\netty-transport-classes-epoll\4.1.101.Final\netty-transport-classes-epoll-4.1.101.Final.jar;D:\maven_repository\org\apache\kafka\kafka-clients\3.6.0\kafka-clients-3.6.0-test.jar;D:\maven_repository\org\apache\kafka\kafka-metadata\3.6.0\kafka-metadata-3.6.0.jar;D:\maven_repository\org\apache\kafka\kafka-raft\3.6.0\kafka-raft-3.6.0.jar;D:\maven_repository\com\yammer\metrics\metrics-core\2.2.0\metrics-core-2.2.0.jar;D:\maven_repository\org\apache\kafka\kafka-server-common\3.6.0\kafka-server-common-3.6.0.jar;D:\maven_repository\net\sf\jopt-simple\jopt-simple\5.0.4\jopt-simple-5.0.4.jar;D:\maven_repository\org\pcollections\pcollections\4.0.1\pcollections-4.0.1.jar;D:\maven_repository\org\apache\kafka\kafka-server-common\3.6.0\kafka-server-common-3.6.0-test.jar;D:\maven_repository\org\apache\kafka\kafka-streams-test-utils\3.6.0\kafka-streams-test-utils-3.6.0.jar;D:\maven_repository\org\apache\kafka\kafka-streams\3.6.0\kafka-streams-3.6.0.jar;D:\maven_repository\org\rocksdb\rocksdbjni\7.9.2\rocksdbjni-7.9.2.jar;D:\maven_repository\org\apache\kafka\kafka_2.13\3.6.0\kafka_2.13-3.6.0.jar;D:\maven_repository\org\scala-lang\scala-library\2.13.11\scala-library-2.13.11.jar;D:\maven_repository\org\apache\kafka\kafka-group-coordinator\3.6.0\kafka-group-coordinator-3.6.0.jar;D:\maven_repository\org\apache\kafka\kafka-storage-api\3.6.0\kafka-storage-api-3.6.0.jar;D:\maven_repository\org\apache\kafka\kafka-tools-api\3.6.0\kafka-tools-api-3.6.0.jar;D:\maven_repository\org\apache\kafka\kafka-storage\3.6.0\kafka-storage-3.6.0.jar;D:\maven_repository\com\github\ben-manes\caffeine\caffeine\3.1.8\caffeine-3.1.8.jar;D:\maven_repository\net\sourceforge\argparse4j\argparse4j\0.7.0\argparse4j-0.7.0.jar;D:\maven_repository\commons-validator\commons-validator\1.7\commons-validator-1.7.jar;D:\maven_repository\commons-beanutils\commons-beanutils\1.9.4\commons-beanutils-1.9.4.jar;D:\maven_repository\commons-digester\commons-digester\2.1\commons-digester-2.1.jar;D:\maven_repository\commons-collections\commons-collections\3.2.2\commons-collections-3.2.2.jar;D:\maven_repository\com\fasterxml\jackson\module\jackson-module-scala_2.13\2.15.3\jackson-module-scala_2.13-2.15.3.jar;D:\maven_repository\com\thoughtworks\paranamer\paranamer\2.8\paranamer-2.8.jar;D:\maven_repository\org\bitbucket\b_c\jose4j\0.9.3\jose4j-0.9.3.jar;D:\maven_repository\org\scala-lang\modules\scala-collection-compat_2.13\2.10.0\scala-collection-compat_2.13-2.10.0.jar;D:\maven_repository\org\scala-lang\modules\scala-java8-compat_2.13\1.0.2\scala-java8-compat_2.13-1.0.2.jar;D:\maven_repository\org\scala-lang\scala-reflect\2.13.11\scala-reflect-2.13.11.jar;D:\maven_repository\com\typesafe\scala-logging\scala-logging_2.13\3.9.4\scala-logging_2.13-3.9.4.jar;D:\maven_repository\io\dropwizard\metrics\metrics-core\4.2.22\metrics-core-4.2.22.jar;D:\maven_repository\commons-cli\commons-cli\1.4\commons-cli-1.4.jar;D:\maven_repository\org\apache\kafka\kafka_2.13\3.6.0\kafka_2.13-3.6.0-test.jar;D:\maven_repository\org\junit\platform\junit-platform-launcher\1.10.1\junit-platform-launcher-1.10.1.jar;D:\maven_repository\com\google\code\findbugs\jsr305\3.0.2\jsr305-3.0.2.jar;D:\maven_repository\org\mockito\mockito-junit-jupiter\5.7.0\mockito-junit-jupiter-5.7.0.jar;D:\maven_repository\org\springframework\boot\spring-boot-starter\3.2.0\spring-boot-starter-3.2.0.jar;D:\maven_repository\org\springframework\boot\spring-boot\3.2.0\spring-boot-3.2.0.jar;D:\maven_repository\org\springframework\boot\spring-boot-autoconfigure\3.2.0\spring-boot-autoconfigure-3.2.0.jar;D:\maven_repository\org\springframework\boot\spring-boot-starter-logging\3.2.0\spring-boot-starter-logging-3.2.0.jar;D:\maven_repository\org\apache\logging\log4j\log4j-to-slf4j\2.21.1\log4j-to-slf4j-2.21.1.jar;D:\maven_repository\org\apache\logging\log4j\log4j-api\2.21.1\log4j-api-2.21.1.jar;D:\maven_repository\org\slf4j\jul-to-slf4j\2.0.9\jul-to-slf4j-2.0.9.jar;D:\maven_repository\org\springframework\spring-core\6.1.1\spring-core-6.1.1.jar;D:\maven_repository\org\springframework\spring-jcl\6.1.1\spring-jcl-6.1.1.jar;D:\maven_repository\org\yaml\snakeyaml\2.2\snakeyaml-2.2.jar;D:\maven_repository\com\baomidou\mybatis-plus-spring-boot3-starter\3.5.11\mybatis-plus-spring-boot3-starter-3.5.11.jar;D:\maven_repository\com\baomidou\mybatis-plus\3.5.11\mybatis-plus-3.5.11.jar;D:\maven_repository\com\baomidou\mybatis-plus-annotation\3.5.11\mybatis-plus-annotation-3.5.11.jar;D:\maven_repository\com\baomidou\mybatis-plus-spring\3.5.11\mybatis-plus-spring-3.5.11.jar;D:\maven_repository\org\mybatis\mybatis\3.5.19\mybatis-3.5.19.jar;D:\maven_repository\org\mybatis\mybatis-spring\3.0.4\mybatis-spring-3.0.4.jar;D:\maven_repository\com\baomidou\mybatis-plus-spring-boot-autoconfigure\3.5.11\mybatis-plus-spring-boot-autoconfigure-3.5.11.jar;D:\maven_repository\com\baomidou\mybatis-plus-extension\3.5.11\mybatis-plus-extension-3.5.11.jar;D:\maven_repository\com\baomidou\mybatis-plus-core\3.5.11\mybatis-plus-core-3.5.11.jar;D:\maven_repository\org\slf4j\slf4j-api\2.0.9\slf4j-api-2.0.9.jar;D:\maven_repository\ch\qos\logback\logback-classic\1.4.11\logback-classic-1.4.11.jar;D:\maven_repository\ch\qos\logback\logback-core\1.4.11\logback-core-1.4.11.jar;D:\maven_repository\org\apache\commons\commons-lang3\3.13.0\commons-lang3-3.13.0.jar;D:\maven_repository\commons-io\commons-io\2.15.0\commons-io-2.15.0.jar;D:\maven_repository\com\google\guava\guava\32.1.2-jre\guava-32.1.2-jre.jar;D:\maven_repository\com\google\guava\failureaccess\1.0.1\failureaccess-1.0.1.jar;D:\maven_repository\com\google\guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar;D:\maven_repository\org\checkerframework\checker-qual\3.33.0\checker-qual-3.33.0.jar;D:\maven_repository\com\google\errorprone\error_prone_annotations\2.18.0\error_prone_annotations-2.18.0.jar;D:\maven_repository\com\google\j2objc\j2objc-annotations\2.8\j2objc-annotations-2.8.jar;D:\maven_repository\com\fasterxml\jackson\core\jackson-databind\2.15.3\jackson-databind-2.15.3.jar;D:\maven_repository\com\fasterxml\jackson\core\jackson-annotations\2.15.3\jackson-annotations-2.15.3.jar;D:\maven_repository\jakarta\annotation\jakarta.annotation-api\2.1.1\jakarta.annotation-api-2.1.1.jar;D:\maven_repository\org\projectlombok\lombok\1.18.30\lombok-1.18.30.jar;D:\maven_repository\org\junit\jupiter\junit-jupiter-api\5.10.0\junit-jupiter-api-5.10.0.jar;D:\maven_repository\org\opentest4j\opentest4j\1.3.0\opentest4j-1.3.0.jar;D:\maven_repository\org\junit\platform\junit-platform-commons\1.10.1\junit-platform-commons-1.10.1.jar;D:\maven_repository\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;D:\maven_repository\org\junit\jupiter\junit-jupiter-engine\5.10.0\junit-jupiter-engine-5.10.0.jar;D:\maven_repository\org\junit\platform\junit-platform-engine\1.10.1\junit-platform-engine-1.10.1.jar;D:\maven_repository\org\springframework\boot\spring-boot-starter-test\3.2.0\spring-boot-starter-test-3.2.0.jar;D:\maven_repository\org\springframework\boot\spring-boot-test\3.2.0\spring-boot-test-3.2.0.jar;D:\maven_repository\org\springframework\boot\spring-boot-test-autoconfigure\3.2.0\spring-boot-test-autoconfigure-3.2.0.jar;D:\maven_repository\com\jayway\jsonpath\json-path\2.8.0\json-path-2.8.0.jar;D:\maven_repository\jakarta\xml\bind\jakarta.xml.bind-api\4.0.1\jakarta.xml.bind-api-4.0.1.jar;D:\maven_repository\jakarta\activation\jakarta.activation-api\2.1.2\jakarta.activation-api-2.1.2.jar;D:\maven_repository\net\minidev\json-smart\2.5.0\json-smart-2.5.0.jar;D:\maven_repository\net\minidev\accessors-smart\2.5.0\accessors-smart-2.5.0.jar;D:\maven_repository\org\ow2\asm\asm\9.3\asm-9.3.jar;D:\maven_repository\org\assertj\assertj-core\3.24.2\assertj-core-3.24.2.jar;D:\maven_repository\org\awaitility\awaitility\4.2.0\awaitility-4.2.0.jar;D:\maven_repository\org\hamcrest\hamcrest\2.2\hamcrest-2.2.jar;D:\maven_repository\org\junit\jupiter\junit-jupiter\5.10.1\junit-jupiter-5.10.1.jar;D:\maven_repository\org\junit\jupiter\junit-jupiter-params\5.10.1\junit-jupiter-params-5.10.1.jar;D:\maven_repository\org\skyscreamer\jsonassert\1.5.1\jsonassert-1.5.1.jar;D:\maven_repository\com\vaadin\external\google\android-json\0.0.20131108.vaadin1\android-json-0.0.20131108.vaadin1.jar;D:\maven_repository\org\xmlunit\xmlunit-core\2.9.1\xmlunit-core-2.9.1.jar;D:\maven_repository\org\mockito\mockito-core\4.8.0\mockito-core-4.8.0.jar;D:\maven_repository\net\bytebuddy\byte-buddy\1.14.10\byte-buddy-1.14.10.jar;D:\maven_repository\net\bytebuddy\byte-buddy-agent\1.14.10\byte-buddy-agent-1.14.10.jar;D:\maven_repository\org\objenesis\objenesis\3.2\objenesis-3.2.jar;"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="C:\Users\<USER>\Users\10438\.jdks\corretto-21.0.7"/>
    <property name="file.separator" value="\"/>
    <property name="basedir" value="D:\1_deep_bian\crypto-trading-system\crypto-trade"/>
    <property name="java.vm.compressedOopsMode" value="Zero based"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="surefire.real.class.path" value="C:\Users\<USER>\AppData\Local\Temp\surefire10965909004826728704\surefirebooter-20250622185319706_123.jar"/>
    <property name="user.script" value=""/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="java.runtime.version" value="21.0.7+6-LTS"/>
    <property name="user.name" value="10438"/>
    <property name="stdout.encoding" value="GBK"/>
    <property name="path.separator" value=";"/>
    <property name="os.version" value="10.0"/>
    <property name="java.runtime.name" value="OpenJDK Runtime Environment"/>
    <property name="file.encoding" value="UTF-8"/>
    <property name="java.vm.name" value="OpenJDK 64-Bit Server VM"/>
    <property name="java.vendor.version" value="Corretto-********.1"/>
    <property name="localRepository" value="D:\maven_repository"/>
    <property name="java.vendor.url.bug" value="https://github.com/corretto/corretto-21/issues/"/>
    <property name="java.io.tmpdir" value="C:\Users\<USER>\AppData\Local\Temp\"/>
    <property name="idea.version" value="2025.1.2"/>
    <property name="java.version" value="21.0.7"/>
    <property name="user.dir" value="D:\1_deep_bian\crypto-trading-system\crypto-trade"/>
    <property name="os.arch" value="amd64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="sun.os.patch.level" value=""/>
    <property name="native.encoding" value="GBK"/>
    <property name="java.library.path" value="C:\Users\<USER>\.jdks\corretto-21.0.7\bin;C:\Windows\Sun\Java\bin;C:\Windows\system32;C:\Windows;d:\Software\cursor\resources\app\bin;d:\Software\cursor\resources\app\bin;d:\Software\cursor\resources\app\bin;d:\Software\cursor\resources\app\bin;d:\Software\cursor\resources\app\bin;d:\Software\cursor\resources\app\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.6\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.6\libnvvp;D:\Software\vm\bin\;D:\jdk\flutter_windows_3.22.2-stable\flutter\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\MinGW\bin;D:\Software\apache-maven-3.6.0\bin;D:\Software\Git\cmd;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;D:\jdk\Android\Sdk\platform-tools;D:\jdk\Android\Sdk\build-tools;C:\Program Files\NVIDIA Corporation\NVIDIA app\NvDLISR;C:\Program Files\NVIDIA Corporation\Nsight Compute 2024.3.2\;C:\Program Files\CursorModifier;C:\Program Files\Redis\;C:\Program Files\MySQL\MySQL Server 9.0\bin;C:\Users\<USER>\;C:\Program Files\Docker\Docker\resources\bin;C:\Program Files\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\;C:\Program Files\Google\Chrome\Application;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.6\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.6\libnvvp;D:\Software\vm\bin\;D:\jdk\flutter_windows_3.22.2-stable\flutter\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;D:\Software\MySQL Server 8.0\bin;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\MinGW\bin;D:\Software\apache-maven-3.6.0\bin;C:\Program Files\Docker\Docker\resources\bin;D:\Software\Git\cmd;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;C:\ProgramData\miniconda3;C:\ProgramData\miniconda3\Scripts;D:\Software\dartsdk-windows-x64-release\dart-sdk\bin;D:\jdk\Android\Sdk\platform-tools;D:\jdk\Andro;C:\Users\<USER>\.jdks\openjdk-21.0.2\bin;D:\Software\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;."/>
    <property name="java.vm.info" value="mixed mode, sharing"/>
    <property name="stderr.encoding" value="GBK"/>
    <property name="java.vendor" value="Amazon.com Inc."/>
    <property name="java.vm.version" value="21.0.7+6-LTS"/>
    <property name="sun.io.unicode.encoding" value="UnicodeLittle"/>
    <property name="java.class.version" value="65.0"/>
  </properties>
  <testcase name="testExecuteMarketOrder_ApiException" classname="com.crypto.trading.trade.executor.BinanceTradeExecutorTest" time="0.44">
    <system-out><![CDATA[2025-06-22 18:53:22.912[1750589602912] | INFO  | main       | c.c.t.t.e.BinanceTradeExecutor       - 执行市价单: orderId=123456, symbol=BTCUSDT, side=BUY, quantity=1.0
2025-06-22 18:53:22.917[1750589602917] | INFO  | main       | c.c.trading.common.util.ThreadUtil   - 创建线程池成功: corePoolSize=4, maximumPoolSize=13, queueCapacity=1024, threadNamePrefix=thread-pool-
]]></system-out>
  </testcase>
  <testcase name="testQueryOrderStatus_DryRun" classname="com.crypto.trading.trade.executor.BinanceTradeExecutorTest" time="0.004">
    <system-out><![CDATA[2025-06-22 18:53:22.945[1750589602945] | INFO  | main       | c.c.t.t.e.BinanceTradeExecutor       - 查询订单状态: symbol=BTCUSDT, orderId=123456, clientOrderId=test123
2025-06-22 18:53:22.945[1750589602945] | INFO  | main       | c.c.t.t.e.BinanceTradeExecutor       - 试运行模式，返回模拟订单状态
]]></system-out>
  </testcase>
  <testcase name="testExecuteMarketOrder_DryRun" classname="com.crypto.trading.trade.executor.BinanceTradeExecutorTest" time="0.06">
    <system-out><![CDATA[2025-06-22 18:53:22.951[1750589602951] | INFO  | main       | c.c.t.t.e.BinanceTradeExecutor       - 执行市价单: orderId=123456, symbol=BTCUSDT, side=BUY, quantity=1.0
2025-06-22 18:53:22.951[1750589602951] | INFO  | main       | c.c.t.t.e.BinanceTradeExecutor       - 试运行模式，不实际下单: OrderEntity(id=1, orderId=123456, clientOrderId=test123, symbol=BTCUSDT, side=BUY, positionSide=BOTH, orderType=MARKET, quantity=1.0, price=50000.0, executedQuantity=null, executedPrice=null, status=NEW, strategy=null, signalId=null, errorCode=null, errorMessage=null, createdTime=1750589602950, updatedTime=null, executedTime=null, deleted=null, timeInForce=null, remark=null)
]]></system-out>
  </testcase>
  <testcase name="testQueryOrderStatus" classname="com.crypto.trading.trade.executor.BinanceTradeExecutorTest" time="0.005">
    <system-out><![CDATA[2025-06-22 18:53:23.010[1750589603010] | INFO  | main       | c.c.t.t.e.BinanceTradeExecutor       - 查询订单状态: symbol=BTCUSDT, orderId=123456, clientOrderId=test123
]]></system-out>
  </testcase>
  <testcase name="testExecuteMarketOrder_Success" classname="com.crypto.trading.trade.executor.BinanceTradeExecutorTest" time="0.006">
    <system-out><![CDATA[2025-06-22 18:53:23.016[1750589603016] | INFO  | main       | c.c.t.t.e.BinanceTradeExecutor       - 执行市价单: orderId=123456, symbol=BTCUSDT, side=BUY, quantity=1.0
]]></system-out>
  </testcase>
  <testcase name="testCancelOrder" classname="com.crypto.trading.trade.executor.BinanceTradeExecutorTest" time="0.004">
    <system-out><![CDATA[2025-06-22 18:53:23.023[1750589603023] | INFO  | main       | c.c.t.t.e.BinanceTradeExecutor       - 取消订单: orderId=123456, symbol=BTCUSDT, clientOrderId=test123
]]></system-out>
  </testcase>
</testsuite>