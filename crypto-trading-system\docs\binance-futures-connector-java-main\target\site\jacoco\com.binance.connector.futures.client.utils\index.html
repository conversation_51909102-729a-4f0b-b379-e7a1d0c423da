<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.binance.connector.futures.client.utils</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.source.html" class="el_source">Source Files</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">io.github.binance:binance-futures-connector-java</a> &gt; <span class="el_package">com.binance.connector.futures.client.utils</span></div><h1>com.binance.connector.futures.client.utils</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">396 of 1,117</td><td class="ctr2">64%</td><td class="bar">40 of 107</td><td class="ctr2">62%</td><td class="ctr1">44</td><td class="ctr2">106</td><td class="ctr1">92</td><td class="ctr2">238</td><td class="ctr1">16</td><td class="ctr2">49</td><td class="ctr1">2</td><td class="ctr2">10</td></tr></tfoot><tbody><tr><td id="a9"><a href="WebSocketConnection.html" class="el_class">WebSocketConnection</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="73" height="10" title="152" alt="152"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="16" height="10" title="4" alt="4"/></td><td class="ctr2" id="e6">0%</td><td class="ctr1" id="f0">11</td><td class="ctr2" id="g5">11</td><td class="ctr1" id="h0">37</td><td class="ctr2" id="i1">37</td><td class="ctr1" id="j0">9</td><td class="ctr2" id="k0">9</td><td class="ctr1" id="l0">1</td><td class="ctr2" id="m0">1</td></tr><tr><td id="a6"><a href="ResponseHandler.html" class="el_class">ResponseHandler</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="37" height="10" title="78" alt="78"/><img src="../jacoco-resources/greenbar.gif" width="35" height="10" title="74" alt="74"/></td><td class="ctr2" id="c6">48%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="44" height="10" title="11" alt="11"/><img src="../jacoco-resources/greenbar.gif" width="36" height="10" title="9" alt="9"/></td><td class="ctr2" id="e4">45%</td><td class="ctr1" id="f1">8</td><td class="ctr2" id="g2">14</td><td class="ctr1" id="h2">16</td><td class="ctr2" id="i2">33</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k4">4</td><td class="ctr1" id="l2">0</td><td class="ctr2" id="m1">1</td></tr><tr><td id="a8"><a href="UrlBuilder.html" class="el_class">UrlBuilder</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="31" height="10" title="65" alt="65"/><img src="../jacoco-resources/greenbar.gif" width="88" height="10" title="184" alt="184"/></td><td class="ctr2" id="c4">73%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="32" height="10" title="8" alt="8"/><img src="../jacoco-resources/greenbar.gif" width="88" height="10" title="22" alt="22"/></td><td class="ctr2" id="e2">73%</td><td class="ctr1" id="f4">6</td><td class="ctr2" id="g0">24</td><td class="ctr1" id="h1">17</td><td class="ctr2" id="i0">60</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k1">9</td><td class="ctr1" id="l3">0</td><td class="ctr2" id="m2">1</td></tr><tr><td id="a0"><a href="HttpClientSingleton.html" class="el_class">HttpClientSingleton</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="17" height="10" title="36" alt="36"/><img src="../jacoco-resources/greenbar.gif" width="14" height="10" title="30" alt="30"/></td><td class="ctr2" id="c7">45%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="40" height="10" title="10" alt="10"/><img src="../jacoco-resources/greenbar.gif" width="24" height="10" title="6" alt="6"/></td><td class="ctr2" id="e5">37%</td><td class="ctr1" id="f2">8</td><td class="ctr2" id="g3">13</td><td class="ctr1" id="h3">7</td><td class="ctr2" id="i5">18</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k3">5</td><td class="ctr1" id="l4">0</td><td class="ctr2" id="m3">1</td></tr><tr><td id="a5"><a href="RequestHandler.html" class="el_class">RequestHandler</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="22" alt="22"/><img src="../jacoco-resources/greenbar.gif" width="62" height="10" title="129" alt="129"/></td><td class="ctr2" id="c2">85%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="28" height="10" title="7" alt="7"/><img src="../jacoco-resources/greenbar.gif" width="32" height="10" title="8" alt="8"/></td><td class="ctr2" id="e3">53%</td><td class="ctr1" id="f3">7</td><td class="ctr2" id="g1">15</td><td class="ctr1" id="h6">3</td><td class="ctr2" id="i3">30</td><td class="ctr1" id="j6">0</td><td class="ctr2" id="k2">7</td><td class="ctr1" id="l5">0</td><td class="ctr2" id="m4">1</td></tr><tr><td id="a1"><a href="JSONParser.html" class="el_class">JSONParser</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="21" alt="21"/><img src="../jacoco-resources/greenbar.gif" width="21" height="10" title="44" alt="44"/></td><td class="ctr2" id="c5">67%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f6">1</td><td class="ctr2" id="g7">3</td><td class="ctr1" id="h5">4</td><td class="ctr2" id="i7">12</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k7">3</td><td class="ctr1" id="l6">0</td><td class="ctr2" id="m5">1</td></tr><tr><td id="a3"><a href="ProxyAuth.html" class="el_class">ProxyAuth</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="15" alt="15"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f5">3</td><td class="ctr2" id="g8">3</td><td class="ctr1" id="h4">6</td><td class="ctr2" id="i9">6</td><td class="ctr1" id="j1">3</td><td class="ctr2" id="k8">3</td><td class="ctr1" id="l1">1</td><td class="ctr2" id="m6">1</td></tr><tr><td id="a7"><a href="SignatureGenerator.html" class="el_class">SignatureGenerator</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="7" alt="7"/><img src="../jacoco-resources/greenbar.gif" width="10" height="10" title="22" alt="22"/></td><td class="ctr2" id="c3">75%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f7">0</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h7">2</td><td class="ctr2" id="i8">8</td><td class="ctr1" id="j7">0</td><td class="ctr2" id="k9">1</td><td class="ctr1" id="l7">0</td><td class="ctr2" id="m7">1</td></tr><tr><td id="a4"><a href="RequestBuilder.html" class="el_class">RequestBuilder</a></td><td class="bar" id="b8"><img src="../jacoco-resources/greenbar.gif" width="69" height="10" title="144" alt="144"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d6"><img src="../jacoco-resources/greenbar.gif" width="40" height="10" title="10" alt="10"/></td><td class="ctr2" id="e0">100%</td><td class="ctr1" id="f8">0</td><td class="ctr2" id="g4">12</td><td class="ctr1" id="h8">0</td><td class="ctr2" id="i4">20</td><td class="ctr1" id="j8">0</td><td class="ctr2" id="k5">4</td><td class="ctr1" id="l8">0</td><td class="ctr2" id="m8">1</td></tr><tr><td id="a2"><a href="ParameterChecker.html" class="el_class">ParameterChecker</a></td><td class="bar" id="b9"><img src="../jacoco-resources/greenbar.gif" width="45" height="10" title="94" alt="94"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d5"><img src="../jacoco-resources/greenbar.gif" width="48" height="10" title="12" alt="12"/></td><td class="ctr2" id="e1">100%</td><td class="ctr1" id="f9">0</td><td class="ctr2" id="g6">10</td><td class="ctr1" id="h9">0</td><td class="ctr2" id="i6">14</td><td class="ctr1" id="j9">0</td><td class="ctr2" id="k6">4</td><td class="ctr1" id="l9">0</td><td class="ctr2" id="m9">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.7.202105040129</span></div></body></html>