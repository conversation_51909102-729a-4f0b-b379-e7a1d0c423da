"""
熔断器模式实现模块

提供crypto_ml_strategy项目的熔断器模式实现，用于防止级联故障，
包括熔断状态管理、故障检测、自动恢复和性能监控。

Author: Crypto ML Strategy Team
Date: 2024-12-20
Version: 1.0.0
"""

import asyncio
import time
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Callable, Dict, List, Optional, TypeVar, Generic
from loguru import logger

from .error_handler_core import ErrorSeverity, ErrorContext, CryptoMLException, ErrorCode

T = TypeVar('T')


class CircuitBreakerState(Enum):
    """熔断器状态枚举"""
    CLOSED = "closed"      # 关闭状态，正常工作
    OPEN = "open"          # 开启状态，拒绝请求
    HALF_OPEN = "half_open"  # 半开状态，试探性恢复


@dataclass
class CircuitBreakerConfig:
    """熔断器配置"""
    failure_threshold: int = 5          # 失败阈值
    recovery_timeout: int = 60          # 恢复超时时间（秒）
    expected_exception: type = Exception  # 预期异常类型
    success_threshold: int = 3          # 半开状态成功阈值
    timeout: float = 30.0               # 操作超时时间（秒）
    monitor_window: int = 300           # 监控窗口时间（秒）


@dataclass
class CircuitBreakerMetrics:
    """熔断器指标"""
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    rejected_requests: int = 0
    last_failure_time: Optional[datetime] = None
    last_success_time: Optional[datetime] = None
    state_change_time: datetime = field(default_factory=datetime.now)
    consecutive_failures: int = 0
    consecutive_successes: int = 0
    
    def success_rate(self) -> float:
        """计算成功率"""
        if self.total_requests == 0:
            return 1.0
        return self.successful_requests / self.total_requests
    
    def failure_rate(self) -> float:
        """计算失败率"""
        return 1.0 - self.success_rate()
    
    def reset_window(self) -> None:
        """重置监控窗口"""
        self.total_requests = 0
        self.successful_requests = 0
        self.failed_requests = 0
        self.rejected_requests = 0


class CircuitBreakerOpenException(CryptoMLException):
    """熔断器开启异常"""
    def __init__(self, circuit_name: str, **kwargs):
        super().__init__(
            f"熔断器 {circuit_name} 处于开启状态，拒绝请求",
            ErrorCode.RESOURCE_EXHAUSTED,
            **kwargs
        )


class CircuitBreakerTimeoutException(CryptoMLException):
    """熔断器超时异常"""
    def __init__(self, circuit_name: str, timeout: float, **kwargs):
        super().__init__(
            f"熔断器 {circuit_name} 操作超时: {timeout}秒",
            ErrorCode.NETWORK_TIMEOUT,
            **kwargs
        )


class CircuitBreaker(Generic[T]):
    """熔断器实现"""
    
    def __init__(
        self,
        name: str,
        config: Optional[CircuitBreakerConfig] = None
    ):
        self.name = name
        self.config = config or CircuitBreakerConfig()
        self.state = CircuitBreakerState.CLOSED
        self.metrics = CircuitBreakerMetrics()
        self._lock = asyncio.Lock()
        self._last_window_reset = datetime.now()
        
        logger.info(f"创建熔断器: {name}")
    
    async def call(self, func: Callable[..., T], *args, **kwargs) -> T:
        """
        通过熔断器调用函数
        
        Args:
            func: 要调用的函数
            *args: 函数参数
            **kwargs: 函数关键字参数
            
        Returns:
            函数返回值
            
        Raises:
            CircuitBreakerOpenException: 熔断器开启时
            CircuitBreakerTimeoutException: 操作超时时
        """
        async with self._lock:
            # 检查是否需要重置监控窗口
            await self._check_window_reset()
            
            # 检查熔断器状态
            if self.state == CircuitBreakerState.OPEN:
                if await self._should_attempt_reset():
                    await self._transition_to_half_open()
                else:
                    self.metrics.rejected_requests += 1
                    raise CircuitBreakerOpenException(self.name)
            
            # 执行函数调用
            return await self._execute_call(func, *args, **kwargs)
    
    async def _execute_call(self, func: Callable[..., T], *args, **kwargs) -> T:
        """执行函数调用"""
        self.metrics.total_requests += 1
        start_time = time.time()
        
        try:
            # 设置超时
            if asyncio.iscoroutinefunction(func):
                result = await asyncio.wait_for(
                    func(*args, **kwargs),
                    timeout=self.config.timeout
                )
            else:
                # 对于同步函数，在线程池中执行
                loop = asyncio.get_event_loop()
                result = await asyncio.wait_for(
                    loop.run_in_executor(None, func, *args, **kwargs),
                    timeout=self.config.timeout
                )
            
            # 记录成功
            await self._record_success()
            return result
            
        except asyncio.TimeoutError:
            # 超时处理
            await self._record_failure()
            raise CircuitBreakerTimeoutException(
                self.name,
                self.config.timeout
            )
            
        except Exception as e:
            # 检查是否是预期异常
            if isinstance(e, self.config.expected_exception):
                await self._record_failure()
            else:
                await self._record_success()
            raise e
        
        finally:
            # 记录执行时间
            execution_time = (time.time() - start_time) * 1000
            logger.debug(f"熔断器 {self.name} 执行时间: {execution_time:.2f}ms")
    
    async def _record_success(self) -> None:
        """记录成功"""
        self.metrics.successful_requests += 1
        self.metrics.consecutive_successes += 1
        self.metrics.consecutive_failures = 0
        self.metrics.last_success_time = datetime.now()
        
        # 在半开状态下检查是否可以关闭熔断器
        if self.state == CircuitBreakerState.HALF_OPEN:
            if self.metrics.consecutive_successes >= self.config.success_threshold:
                await self._transition_to_closed()
    
    async def _record_failure(self) -> None:
        """记录失败"""
        self.metrics.failed_requests += 1
        self.metrics.consecutive_failures += 1
        self.metrics.consecutive_successes = 0
        self.metrics.last_failure_time = datetime.now()
        
        # 检查是否需要开启熔断器
        if (self.state == CircuitBreakerState.CLOSED and
            self.metrics.consecutive_failures >= self.config.failure_threshold):
            await self._transition_to_open()
        elif (self.state == CircuitBreakerState.HALF_OPEN):
            await self._transition_to_open()
    
    async def _should_attempt_reset(self) -> bool:
        """判断是否应该尝试重置"""
        if self.state != CircuitBreakerState.OPEN:
            return False
        
        time_since_open = datetime.now() - self.metrics.state_change_time
        return time_since_open.total_seconds() >= self.config.recovery_timeout
    
    async def _transition_to_open(self) -> None:
        """转换到开启状态"""
        self.state = CircuitBreakerState.OPEN
        self.metrics.state_change_time = datetime.now()
        logger.warning(f"熔断器 {self.name} 转换到开启状态")
    
    async def _transition_to_half_open(self) -> None:
        """转换到半开状态"""
        self.state = CircuitBreakerState.HALF_OPEN
        self.metrics.state_change_time = datetime.now()
        self.metrics.consecutive_successes = 0
        logger.info(f"熔断器 {self.name} 转换到半开状态")
    
    async def _transition_to_closed(self) -> None:
        """转换到关闭状态"""
        self.state = CircuitBreakerState.CLOSED
        self.metrics.state_change_time = datetime.now()
        self.metrics.consecutive_failures = 0
        logger.info(f"熔断器 {self.name} 转换到关闭状态")
    
    async def _check_window_reset(self) -> None:
        """检查是否需要重置监控窗口"""
        now = datetime.now()
        time_since_reset = now - self._last_window_reset
        
        if time_since_reset.total_seconds() >= self.config.monitor_window:
            self.metrics.reset_window()
            self._last_window_reset = now
            logger.debug(f"熔断器 {self.name} 重置监控窗口")
    
    def get_state(self) -> CircuitBreakerState:
        """获取当前状态"""
        return self.state
    
    def get_metrics(self) -> Dict[str, Any]:
        """获取指标"""
        return {
            "name": self.name,
            "state": self.state.value,
            "total_requests": self.metrics.total_requests,
            "successful_requests": self.metrics.successful_requests,
            "failed_requests": self.metrics.failed_requests,
            "rejected_requests": self.metrics.rejected_requests,
            "success_rate": self.metrics.success_rate(),
            "failure_rate": self.metrics.failure_rate(),
            "consecutive_failures": self.metrics.consecutive_failures,
            "consecutive_successes": self.metrics.consecutive_successes,
            "last_failure_time": (
                self.metrics.last_failure_time.isoformat()
                if self.metrics.last_failure_time else None
            ),
            "last_success_time": (
                self.metrics.last_success_time.isoformat()
                if self.metrics.last_success_time else None
            ),
            "state_change_time": self.metrics.state_change_time.isoformat()
        }
    
    async def force_open(self) -> None:
        """强制开启熔断器"""
        async with self._lock:
            await self._transition_to_open()
            logger.warning(f"熔断器 {self.name} 被强制开启")
    
    async def force_close(self) -> None:
        """强制关闭熔断器"""
        async with self._lock:
            await self._transition_to_closed()
            logger.info(f"熔断器 {self.name} 被强制关闭")
    
    async def reset(self) -> None:
        """重置熔断器"""
        async with self._lock:
            self.state = CircuitBreakerState.CLOSED
            self.metrics = CircuitBreakerMetrics()
            self._last_window_reset = datetime.now()
            logger.info(f"熔断器 {self.name} 已重置")


class CircuitBreakerRegistry:
    """熔断器注册表"""
    
    def __init__(self):
        self.circuit_breakers: Dict[str, CircuitBreaker] = {}
        self._lock = asyncio.Lock()
    
    async def get_or_create(
        self,
        name: str,
        config: Optional[CircuitBreakerConfig] = None
    ) -> CircuitBreaker:
        """获取或创建熔断器"""
        async with self._lock:
            if name not in self.circuit_breakers:
                self.circuit_breakers[name] = CircuitBreaker(name, config)
                logger.info(f"创建新熔断器: {name}")
            
            return self.circuit_breakers[name]
    
    async def remove(self, name: str) -> bool:
        """移除熔断器"""
        async with self._lock:
            if name in self.circuit_breakers:
                del self.circuit_breakers[name]
                logger.info(f"移除熔断器: {name}")
                return True
            return False
    
    def get_all_metrics(self) -> Dict[str, Dict[str, Any]]:
        """获取所有熔断器指标"""
        return {
            name: cb.get_metrics()
            for name, cb in self.circuit_breakers.items()
        }
    
    async def reset_all(self) -> None:
        """重置所有熔断器"""
        for cb in self.circuit_breakers.values():
            await cb.reset()
        logger.info("所有熔断器已重置")


def circuit_breaker(
    name: str,
    config: Optional[CircuitBreakerConfig] = None
) -> Callable:
    """
    熔断器装饰器
    
    Args:
        name: 熔断器名称
        config: 熔断器配置
    """
    def decorator(func: Callable) -> Callable:
        async def async_wrapper(*args, **kwargs):
            cb = await global_circuit_registry.get_or_create(name, config)
            return await cb.call(func, *args, **kwargs)
        
        def sync_wrapper(*args, **kwargs):
            async def _async_call():
                cb = await global_circuit_registry.get_or_create(name, config)
                return await cb.call(func, *args, **kwargs)
            
            try:
                loop = asyncio.get_event_loop()
                return loop.run_until_complete(_async_call())
            except RuntimeError:
                return asyncio.run(_async_call())
        
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator


class CircuitBreakerMonitor:
    """熔断器监控器"""
    
    def __init__(self, registry: CircuitBreakerRegistry):
        self.registry = registry
        self.monitoring = False
        self.monitor_task: Optional[asyncio.Task] = None
    
    async def start_monitoring(self, interval: int = 30) -> None:
        """开始监控"""
        if self.monitoring:
            return
        
        self.monitoring = True
        self.monitor_task = asyncio.create_task(
            self._monitor_loop(interval)
        )
        logger.info("熔断器监控已启动")
    
    async def stop_monitoring(self) -> None:
        """停止监控"""
        self.monitoring = False
        if self.monitor_task:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass
        logger.info("熔断器监控已停止")
    
    async def _monitor_loop(self, interval: int) -> None:
        """监控循环"""
        while self.monitoring:
            try:
                metrics = self.registry.get_all_metrics()
                
                for name, metric in metrics.items():
                    # 检查异常状态
                    if metric["state"] == "open":
                        logger.warning(
                            f"熔断器 {name} 处于开启状态，"
                            f"失败率: {metric['failure_rate']:.2%}"
                        )
                    elif metric["failure_rate"] > 0.5:  # 失败率超过50%
                        logger.warning(
                            f"熔断器 {name} 失败率过高: {metric['failure_rate']:.2%}"
                        )
                
                await asyncio.sleep(interval)
                
            except Exception as e:
                logger.error(f"熔断器监控异常: {e}")
                await asyncio.sleep(5)
    
    def get_summary(self) -> Dict[str, Any]:
        """获取监控摘要"""
        metrics = self.registry.get_all_metrics()
        
        total_circuits = len(metrics)
        open_circuits = len([m for m in metrics.values() if m["state"] == "open"])
        half_open_circuits = len([m for m in metrics.values() if m["state"] == "half_open"])
        
        return {
            "total_circuits": total_circuits,
            "open_circuits": open_circuits,
            "half_open_circuits": half_open_circuits,
            "healthy_circuits": total_circuits - open_circuits - half_open_circuits,
            "monitoring_active": self.monitoring,
            "circuit_details": metrics
        }


# 全局熔断器注册表和监控器
global_circuit_registry = CircuitBreakerRegistry()
global_circuit_monitor = CircuitBreakerMonitor(global_circuit_registry)