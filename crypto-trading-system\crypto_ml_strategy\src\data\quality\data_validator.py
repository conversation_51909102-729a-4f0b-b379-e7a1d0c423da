"""
数据验证器

提供全面的数据验证功能，包括数据格式验证、类型检查、完整性验证、
异常数据检测、数据质量报告和统计分析。与现有数据质量检查器集成，
确保数据满足机器学习模型和交易策略的要求。

Author: Crypto ML Strategy Team
Date: 2024-12-19
"""

import asyncio
import json
import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any, Union, Set, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import threading
from collections import defaultdict, deque
import re
import warnings
from pathlib import Path

from data.data_config import DataSourceType, DataFormat
from .data_quality_checker import DataQualityChecker, QualityMetrics, QualityLevel, QualityIssue

logger = logging.getLogger(__name__)


class ValidationLevel(Enum):
    """验证级别枚举"""
    BASIC = "basic"           # 基础验证
    STANDARD = "standard"     # 标准验证
    STRICT = "strict"         # 严格验证
    COMPREHENSIVE = "comprehensive"  # 全面验证


class ValidationResult(Enum):
    """验证结果枚举"""
    PASSED = "passed"         # 通过
    WARNING = "warning"       # 警告
    FAILED = "failed"         # 失败
    CRITICAL = "critical"     # 严重错误


@dataclass
class ValidationRule:
    """验证规则"""
    name: str
    description: str
    validator_func: Callable
    level: ValidationLevel
    enabled: bool = True
    parameters: Dict[str, Any] = field(default_factory=dict)
    
    def validate(self, data: Any) -> Tuple[ValidationResult, str]:
        """执行验证"""
        try:
            return self.validator_func(data, **self.parameters)
        except Exception as e:
            return ValidationResult.CRITICAL, f"验证规则执行失败: {e}"


@dataclass
class ValidationReport:
    """验证报告"""
    timestamp: datetime
    data_source: str
    data_type: str
    total_rules: int
    passed_rules: int
    warning_rules: int
    failed_rules: int
    critical_rules: int
    validation_time: float
    overall_result: ValidationResult
    quality_metrics: Optional[QualityMetrics] = None
    issues: List[QualityIssue] = field(default_factory=list)
    recommendations: List[str] = field(default_factory=list)
    
    @property
    def success_rate(self) -> float:
        """成功率"""
        if self.total_rules == 0:
            return 0.0
        return self.passed_rules / self.total_rules
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'timestamp': self.timestamp.isoformat(),
            'data_source': self.data_source,
            'data_type': self.data_type,
            'total_rules': self.total_rules,
            'passed_rules': self.passed_rules,
            'warning_rules': self.warning_rules,
            'failed_rules': self.failed_rules,
            'critical_rules': self.critical_rules,
            'validation_time': self.validation_time,
            'overall_result': self.overall_result.value,
            'success_rate': self.success_rate,
            'quality_metrics': self.quality_metrics.__dict__ if self.quality_metrics else None,
            'issues_count': len(self.issues),
            'recommendations_count': len(self.recommendations)
        }


class DataValidator:
    """数据验证器类"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化数据验证器
        
        Args:
            config: 验证器配置
        """
        self.config = config or {}
        self.quality_checker = DataQualityChecker(self.config.get('quality_checker', {}))
        
        # 验证规则注册表
        self.validation_rules: Dict[str, ValidationRule] = {}
        
        # 验证历史
        self.validation_history: List[ValidationReport] = []
        
        # 统计信息
        self.stats = {
            'total_validations': 0,
            'successful_validations': 0,
            'failed_validations': 0,
            'total_validation_time': 0.0,
            'last_validation_time': None
        }
        
        # 线程锁
        self.validation_lock = threading.RLock()
        
        # 注册默认验证规则
        self._register_default_rules()
        
        logger.info("数据验证器初始化完成")
    
    def _register_default_rules(self) -> None:
        """注册默认验证规则"""
        try:
            # 基础数据格式验证规则
            self.register_rule(ValidationRule(
                name="dataframe_structure",
                description="验证DataFrame基本结构",
                validator_func=self._validate_dataframe_structure,
                level=ValidationLevel.BASIC
            ))
            
            self.register_rule(ValidationRule(
                name="required_columns",
                description="验证必需列是否存在",
                validator_func=self._validate_required_columns,
                level=ValidationLevel.BASIC,
                parameters={'required_columns': ['open', 'high', 'low', 'close', 'volume']}
            ))
            
            self.register_rule(ValidationRule(
                name="data_types",
                description="验证数据类型",
                validator_func=self._validate_data_types,
                level=ValidationLevel.STANDARD
            ))
            
            self.register_rule(ValidationRule(
                name="price_logic",
                description="验证价格逻辑关系",
                validator_func=self._validate_price_logic,
                level=ValidationLevel.STANDARD
            ))
            
            self.register_rule(ValidationRule(
                name="volume_validation",
                description="验证成交量数据",
                validator_func=self._validate_volume_data,
                level=ValidationLevel.STANDARD
            ))
            
            self.register_rule(ValidationRule(
                name="timestamp_validation",
                description="验证时间戳数据",
                validator_func=self._validate_timestamp_data,
                level=ValidationLevel.STANDARD
            ))
            
            self.register_rule(ValidationRule(
                name="outlier_detection",
                description="检测异常值",
                validator_func=self._validate_outliers,
                level=ValidationLevel.STRICT,
                parameters={'method': 'iqr', 'threshold': 3.0}
            ))
            
            self.register_rule(ValidationRule(
                name="data_completeness",
                description="验证数据完整性",
                validator_func=self._validate_data_completeness,
                level=ValidationLevel.STRICT,
                parameters={'min_completeness': 0.95}
            ))
            
            self.register_rule(ValidationRule(
                name="time_series_continuity",
                description="验证时间序列连续性",
                validator_func=self._validate_time_series_continuity,
                level=ValidationLevel.COMPREHENSIVE
            ))
            
            self.register_rule(ValidationRule(
                name="statistical_consistency",
                description="验证统计一致性",
                validator_func=self._validate_statistical_consistency,
                level=ValidationLevel.COMPREHENSIVE
            ))
            
            logger.info(f"注册了 {len(self.validation_rules)} 个默认验证规则")
            
        except Exception as e:
            logger.error(f"注册默认验证规则失败: {e}")
    
    def register_rule(self, rule: ValidationRule) -> None:
        """
        注册验证规则
        
        Args:
            rule: 验证规则
        """
        try:
            self.validation_rules[rule.name] = rule
            logger.debug(f"注册验证规则: {rule.name}")
        except Exception as e:
            logger.error(f"注册验证规则失败: {e}")
    
    def unregister_rule(self, rule_name: str) -> None:
        """
        注销验证规则
        
        Args:
            rule_name: 规则名称
        """
        try:
            if rule_name in self.validation_rules:
                del self.validation_rules[rule_name]
                logger.debug(f"注销验证规则: {rule_name}")
        except Exception as e:
            logger.error(f"注销验证规则失败: {e}")
    
    def enable_rule(self, rule_name: str) -> None:
        """启用验证规则"""
        if rule_name in self.validation_rules:
            self.validation_rules[rule_name].enabled = True
    
    def disable_rule(self, rule_name: str) -> None:
        """禁用验证规则"""
        if rule_name in self.validation_rules:
            self.validation_rules[rule_name].enabled = False
    
    async def validate_data(
        self,
        data: pd.DataFrame,
        data_source: str = "unknown",
        data_type: str = "market_data",
        validation_level: ValidationLevel = ValidationLevel.STANDARD,
        include_quality_check: bool = True
    ) -> ValidationReport:
        """
        验证数据
        
        Args:
            data: 要验证的数据
            data_source: 数据源名称
            data_type: 数据类型
            validation_level: 验证级别
            include_quality_check: 是否包含质量检查
            
        Returns:
            验证报告
        """
        start_time = datetime.now()
        
        try:
            with self.validation_lock:
                self.stats['total_validations'] += 1
                
                # 获取适用的验证规则
                applicable_rules = self._get_applicable_rules(validation_level)
                
                # 执行验证规则
                validation_results = []
                issues = []
                
                for rule in applicable_rules:
                    if rule.enabled:
                        result, message = rule.validate(data)
                        validation_results.append((rule.name, result, message))
                        
                        if result in [ValidationResult.FAILED, ValidationResult.CRITICAL]:
                            issue = QualityIssue(
                                issue_type=rule.name,
                                severity="high" if result == ValidationResult.FAILED else "critical",
                                description=message,
                                affected_columns=[],
                                affected_rows=len(data) if data is not None else 0,
                                suggestion=f"检查 {rule.description}",
                                timestamp=datetime.now()
                            )
                            issues.append(issue)
                
                # 统计验证结果
                total_rules = len(validation_results)
                passed_rules = sum(1 for _, result, _ in validation_results if result == ValidationResult.PASSED)
                warning_rules = sum(1 for _, result, _ in validation_results if result == ValidationResult.WARNING)
                failed_rules = sum(1 for _, result, _ in validation_results if result == ValidationResult.FAILED)
                critical_rules = sum(1 for _, result, _ in validation_results if result == ValidationResult.CRITICAL)
                
                # 确定总体结果
                if critical_rules > 0:
                    overall_result = ValidationResult.CRITICAL
                elif failed_rules > 0:
                    overall_result = ValidationResult.FAILED
                elif warning_rules > 0:
                    overall_result = ValidationResult.WARNING
                else:
                    overall_result = ValidationResult.PASSED
                
                # 执行质量检查
                quality_metrics = None
                if include_quality_check and data is not None and not data.empty:
                    try:
                        quality_metrics = self.quality_checker.check_data_quality(
                            data, symbol=data_source, timeframe=data_type
                        )
                        # 合并质量检查的问题
                        issues.extend(self.quality_checker.issues_log)
                    except Exception as e:
                        logger.warning(f"质量检查失败: {e}")
                
                # 生成建议
                recommendations = self._generate_recommendations(
                    validation_results, quality_metrics
                )
                
                # 计算验证时间
                validation_time = (datetime.now() - start_time).total_seconds()
                
                # 创建验证报告
                report = ValidationReport(
                    timestamp=start_time,
                    data_source=data_source,
                    data_type=data_type,
                    total_rules=total_rules,
                    passed_rules=passed_rules,
                    warning_rules=warning_rules,
                    failed_rules=failed_rules,
                    critical_rules=critical_rules,
                    validation_time=validation_time,
                    overall_result=overall_result,
                    quality_metrics=quality_metrics,
                    issues=issues,
                    recommendations=recommendations
                )
                
                # 更新统计信息
                self.stats['total_validation_time'] += validation_time
                self.stats['last_validation_time'] = start_time
                
                if overall_result in [ValidationResult.PASSED, ValidationResult.WARNING]:
                    self.stats['successful_validations'] += 1
                else:
                    self.stats['failed_validations'] += 1
                
                # 保存验证历史
                self.validation_history.append(report)
                
                # 限制历史记录数量
                if len(self.validation_history) > 1000:
                    self.validation_history = self.validation_history[-500:]
                
                logger.info(f"数据验证完成: {data_source} - {overall_result.value}")
                return report
                
        except Exception as e:
            logger.error(f"数据验证失败: {e}")
            self.stats['failed_validations'] += 1
            
            # 返回错误报告
            return ValidationReport(
                timestamp=start_time,
                data_source=data_source,
                data_type=data_type,
                total_rules=0,
                passed_rules=0,
                warning_rules=0,
                failed_rules=0,
                critical_rules=1,
                validation_time=(datetime.now() - start_time).total_seconds(),
                overall_result=ValidationResult.CRITICAL,
                issues=[QualityIssue(
                    issue_type="validation_error",
                    severity="critical",
                    description=f"验证过程失败: {e}",
                    affected_columns=[],
                    affected_rows=0,
                    suggestion="检查数据格式和验证器配置",
                    timestamp=datetime.now()
                )],
                recommendations=["检查数据格式", "检查验证器配置", "查看日志获取详细错误信息"]
            )
    
    def _get_applicable_rules(self, validation_level: ValidationLevel) -> List[ValidationRule]:
        """获取适用的验证规则"""
        level_hierarchy = {
            ValidationLevel.BASIC: [ValidationLevel.BASIC],
            ValidationLevel.STANDARD: [ValidationLevel.BASIC, ValidationLevel.STANDARD],
            ValidationLevel.STRICT: [ValidationLevel.BASIC, ValidationLevel.STANDARD, ValidationLevel.STRICT],
            ValidationLevel.COMPREHENSIVE: [ValidationLevel.BASIC, ValidationLevel.STANDARD, 
                                          ValidationLevel.STRICT, ValidationLevel.COMPREHENSIVE]
        }
        
        applicable_levels = level_hierarchy.get(validation_level, [ValidationLevel.BASIC])
        
        return [
            rule for rule in self.validation_rules.values()
            if rule.level in applicable_levels and rule.enabled
        ]
    
    def _generate_recommendations(
        self,
        validation_results: List[Tuple[str, ValidationResult, str]],
        quality_metrics: Optional[QualityMetrics]
    ) -> List[str]:
        """生成改进建议"""
        recommendations = []
        
        # 基于验证结果的建议
        failed_rules = [name for name, result, _ in validation_results 
                       if result in [ValidationResult.FAILED, ValidationResult.CRITICAL]]
        
        if 'dataframe_structure' in failed_rules:
            recommendations.append("检查数据格式，确保为有效的DataFrame")
        if 'required_columns' in failed_rules:
            recommendations.append("添加缺失的必需列")
        if 'data_types' in failed_rules:
            recommendations.append("检查并修正数据类型")
        if 'price_logic' in failed_rules:
            recommendations.append("修正价格逻辑错误")
        if 'volume_validation' in failed_rules:
            recommendations.append("检查成交量数据的有效性")
        if 'outlier_detection' in failed_rules:
            recommendations.append("处理检测到的异常值")
        
        # 基于质量指标的建议
        if quality_metrics:
            recommendations.extend(quality_metrics.recommendations)
        
        return list(set(recommendations))  # 去重
    
    # 验证规则实现
    def _validate_dataframe_structure(self, data: Any) -> Tuple[ValidationResult, str]:
        """验证DataFrame基本结构"""
        try:
            if data is None:
                return ValidationResult.CRITICAL, "数据为None"
            
            if not isinstance(data, pd.DataFrame):
                return ValidationResult.CRITICAL, f"数据类型错误，期望DataFrame，实际为{type(data)}"
            
            if data.empty:
                return ValidationResult.WARNING, "DataFrame为空"
            
            return ValidationResult.PASSED, "DataFrame结构验证通过"
            
        except Exception as e:
            return ValidationResult.CRITICAL, f"DataFrame结构验证失败: {e}"
    
    def _validate_required_columns(self, data: pd.DataFrame, required_columns: List[str]) -> Tuple[ValidationResult, str]:
        """验证必需列"""
        try:
            if data is None or data.empty:
                return ValidationResult.CRITICAL, "数据为空"
            
            missing_columns = [col for col in required_columns if col not in data.columns]
            
            if missing_columns:
                return ValidationResult.FAILED, f"缺少必需列: {missing_columns}"
            
            return ValidationResult.PASSED, "必需列验证通过"
            
        except Exception as e:
            return ValidationResult.CRITICAL, f"必需列验证失败: {e}"
    
    def _validate_data_types(self, data: pd.DataFrame) -> Tuple[ValidationResult, str]:
        """验证数据类型"""
        try:
            if data is None or data.empty:
                return ValidationResult.CRITICAL, "数据为空"
            
            numeric_columns = ['open', 'high', 'low', 'close', 'volume']
            existing_numeric_columns = [col for col in numeric_columns if col in data.columns]
            
            for col in existing_numeric_columns:
                if not pd.api.types.is_numeric_dtype(data[col]):
                    return ValidationResult.FAILED, f"列 {col} 应为数值类型"
            
            return ValidationResult.PASSED, "数据类型验证通过"
            
        except Exception as e:
            return ValidationResult.CRITICAL, f"数据类型验证失败: {e}"
    
    def _validate_price_logic(self, data: pd.DataFrame) -> Tuple[ValidationResult, str]:
        """验证价格逻辑"""
        try:
            if data is None or data.empty:
                return ValidationResult.CRITICAL, "数据为空"
            
            price_columns = ['open', 'high', 'low', 'close']
            if not all(col in data.columns for col in price_columns):
                return ValidationResult.WARNING, "缺少价格列，跳过价格逻辑验证"
            
            # 检查价格为正数
            if (data[price_columns] <= 0).any().any():
                return ValidationResult.FAILED, "存在非正数价格"
            
            # 检查高价 >= 开盘价和收盘价
            high_valid = (data['high'] >= data[['open', 'close']].max(axis=1)).all()
            if not high_valid:
                return ValidationResult.FAILED, "高价小于开盘价或收盘价"
            
            # 检查低价 <= 开盘价和收盘价
            low_valid = (data['low'] <= data[['open', 'close']].min(axis=1)).all()
            if not low_valid:
                return ValidationResult.FAILED, "低价大于开盘价或收盘价"
            
            return ValidationResult.PASSED, "价格逻辑验证通过"
            
        except Exception as e:
            return ValidationResult.CRITICAL, f"价格逻辑验证失败: {e}"
    
    def _validate_volume_data(self, data: pd.DataFrame) -> Tuple[ValidationResult, str]:
        """验证成交量数据"""
        try:
            if data is None or data.empty:
                return ValidationResult.CRITICAL, "数据为空"
            
            if 'volume' not in data.columns:
                return ValidationResult.WARNING, "缺少成交量列"
            
            # 检查成交量为非负数
            if (data['volume'] < 0).any():
                return ValidationResult.FAILED, "存在负数成交量"
            
            return ValidationResult.PASSED, "成交量数据验证通过"
            
        except Exception as e:
            return ValidationResult.CRITICAL, f"成交量数据验证失败: {e}"
    
    def _validate_timestamp_data(self, data: pd.DataFrame) -> Tuple[ValidationResult, str]:
        """验证时间戳数据"""
        try:
            if data is None or data.empty:
                return ValidationResult.CRITICAL, "数据为空"
            
            # 检查索引是否为时间类型
            if hasattr(data.index, 'to_pydatetime'):
                return ValidationResult.PASSED, "时间戳数据验证通过"
            
            # 检查是否有timestamp列
            if 'timestamp' in data.columns:
                return ValidationResult.PASSED, "时间戳数据验证通过"
            
            return ValidationResult.WARNING, "未找到时间戳数据"
            
        except Exception as e:
            return ValidationResult.CRITICAL, f"时间戳数据验证失败: {e}"
    
    def _validate_outliers(self, data: pd.DataFrame, method: str = 'iqr', threshold: float = 3.0) -> Tuple[ValidationResult, str]:
        """验证异常值"""
        try:
            if data is None or data.empty:
                return ValidationResult.CRITICAL, "数据为空"
            
            numeric_columns = data.select_dtypes(include=[np.number]).columns
            total_outliers = 0
            total_values = 0
            
            for col in numeric_columns:
                if method == 'iqr':
                    Q1 = data[col].quantile(0.25)
                    Q3 = data[col].quantile(0.75)
                    IQR = Q3 - Q1
                    lower_bound = Q1 - 1.5 * IQR
                    upper_bound = Q3 + 1.5 * IQR
                    outliers = ((data[col] < lower_bound) | (data[col] > upper_bound)).sum()
                else:
                    # Z-score方法
                    z_scores = np.abs((data[col] - data[col].mean()) / data[col].std())
                    outliers = (z_scores > threshold).sum()
                
                total_outliers += outliers
                total_values += len(data[col])
            
            if total_values == 0:
                return ValidationResult.WARNING, "无数值数据进行异常值检测"
            
            outlier_ratio = total_outliers / total_values
            
            if outlier_ratio > 0.1:
                return ValidationResult.FAILED, f"异常值比例过高: {outlier_ratio:.2%}"
            elif outlier_ratio > 0.05:
                return ValidationResult.WARNING, f"存在异常值: {outlier_ratio:.2%}"
            else:
                return ValidationResult.PASSED, "异常值检测通过"
            
        except Exception as e:
            return ValidationResult.CRITICAL, f"异常值验证失败: {e}"
    
    def _validate_data_completeness(self, data: pd.DataFrame, min_completeness: float = 0.95) -> Tuple[ValidationResult, str]:
        """验证数据完整性"""
        try:
            if data is None or data.empty:
                return ValidationResult.CRITICAL, "数据为空"
            
            total_cells = data.size
            missing_cells = data.isnull().sum().sum()
            completeness = 1.0 - (missing_cells / total_cells)
            
            if completeness < min_completeness:
                return ValidationResult.FAILED, f"数据完整性不足: {completeness:.2%}"
            elif completeness < 0.99:
                return ValidationResult.WARNING, f"数据完整性: {completeness:.2%}"
            else:
                return ValidationResult.PASSED, f"数据完整性验证通过: {completeness:.2%}"
            
        except Exception as e:
            return ValidationResult.CRITICAL, f"数据完整性验证失败: {e}"
    
    def _validate_time_series_continuity(self, data: pd.DataFrame) -> Tuple[ValidationResult, str]:
        """验证时间序列连续性"""
        try:
            if data is None or data.empty:
                return ValidationResult.CRITICAL, "数据为空"
            
            # 简化的时间序列连续性检查
            if len(data) < 2:
                return ValidationResult.WARNING, "数据点不足，无法验证时间序列连续性"
            
            return ValidationResult.PASSED, "时间序列连续性验证通过"
            
        except Exception as e:
            return ValidationResult.CRITICAL, f"时间序列连续性验证失败: {e}"
    
    def _validate_statistical_consistency(self, data: pd.DataFrame) -> Tuple[ValidationResult, str]:
        """验证统计一致性"""
        try:
            if data is None or data.empty:
                return ValidationResult.CRITICAL, "数据为空"
            
            numeric_columns = data.select_dtypes(include=[np.number]).columns
            
            for col in numeric_columns:
                # 检查是否有无穷大值
                if np.isinf(data[col]).any():
                    return ValidationResult.FAILED, f"列 {col} 包含无穷大值"
                
                # 检查标准差是否为0（常数列）
                if data[col].std() == 0 and len(data) > 1:
                    return ValidationResult.WARNING, f"列 {col} 为常数列"
            
            return ValidationResult.PASSED, "统计一致性验证通过"
            
        except Exception as e:
            return ValidationResult.CRITICAL, f"统计一致性验证失败: {e}"
    
    def get_validation_statistics(self) -> Dict[str, Any]:
        """获取验证统计信息"""
        try:
            avg_validation_time = 0.0
            if self.stats['total_validations'] > 0:
                avg_validation_time = self.stats['total_validation_time'] / self.stats['total_validations']
            
            success_rate = 0.0
            if self.stats['total_validations'] > 0:
                success_rate = self.stats['successful_validations'] / self.stats['total_validations']
            
            return {
                'stats': {
                    **self.stats,
                    'average_validation_time': avg_validation_time,
                    'success_rate': success_rate
                },
                'registered_rules': len(self.validation_rules),
                'enabled_rules': sum(1 for rule in self.validation_rules.values() if rule.enabled),
                'validation_history_count': len(self.validation_history),
                'quality_checker_stats': self.quality_checker.get_quality_statistics()
            }
            
        except Exception as e:
            logger.error(f"获取验证统计信息失败: {e}")
            return {'error': str(e)}
    
    def get_recent_reports(self, limit: int = 10) -> List[ValidationReport]:
        """获取最近的验证报告"""
        return self.validation_history[-limit:] if self.validation_history else []
    
    def export_validation_report(self, report: ValidationReport, file_path: str) -> bool:
        """导出验证报告"""
        try:
            report_data = report.to_dict()
            
            # 添加详细信息
            report_data['issues'] = [
                {
                    'type': issue.issue_type,
                    'severity': issue.severity,
                    'description': issue.description,
                    'affected_columns': issue.affected_columns,
                    'affected_rows': issue.affected_rows,
                    'suggestion': issue.suggestion,
                    'timestamp': issue.timestamp.isoformat()
                }
                for issue in report.issues
            ]
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"验证报告已导出到: {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"导出验证报告失败: {e}")
            return False