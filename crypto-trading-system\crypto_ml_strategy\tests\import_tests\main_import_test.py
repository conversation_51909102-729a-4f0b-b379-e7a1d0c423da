#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
main.py导入测试

测试main.py的基础导入功能，不实际运行主程序
"""

import sys
import os
import time

# 添加src目录到Python路径
src_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 'src')
sys.path.insert(0, src_path)

def test_main_imports():
    """测试main.py的导入"""
    try:
        # 测试主要导入
        from core.config import Config
        from data.data_processor import DataProcessor
        from api.kafka_client import KafkaClient
        
        print("PASS main.py core imports: Config, DataProcessor, KafkaClient")
        
        # 测试配置初始化
        config = Config()
        print("PASS Config initialization")
        
        # 测试数据处理器初始化
        processor = DataProcessor(config)
        print("PASS DataProcessor initialization")
        
        # 测试Kafka客户端初始化
        kafka_client = KafkaClient(config)
        print("PASS KafkaClient initialization")
        
        return True
        
    except Exception as e:
        print(f"FAIL main.py imports: {e}")
        return False

def test_main_refactored_imports():
    """测试main_refactored.py的导入"""
    try:
        # 检查main_refactored.py是否存在
        main_refactored_path = os.path.join(os.path.dirname(__file__), 'main_refactored.py')
        if not os.path.exists(main_refactored_path):
            print("INFO main_refactored.py not found, skipping test")
            return True
        
        # 尝试导入main_refactored模块
        import importlib.util
        spec = importlib.util.spec_from_file_location("main_refactored", main_refactored_path)
        main_refactored = importlib.util.module_from_spec(spec)
        
        # 仅检查语法，不执行
        with open(main_refactored_path, 'r', encoding='utf-8') as f:
            code = f.read()
        
        compile(code, main_refactored_path, 'exec')
        print("PASS main_refactored.py syntax check")
        
        return True
        
    except Exception as e:
        print(f"FAIL main_refactored.py: {e}")
        return False

def main():
    """主测试函数"""
    print("Main Import Test")
    print("=" * 60)
    
    test_functions = [
        ("main.py imports", test_main_imports),
        ("main_refactored.py", test_main_refactored_imports),
    ]
    
    results = {}
    start_time = time.time()
    
    # 执行测试
    for test_name, test_func in test_functions:
        print(f"\nTesting {test_name}...")
        try:
            success = test_func()
            results[test_name] = success
        except Exception as e:
            print(f"FAIL {test_name}: {e}")
            results[test_name] = False
    
    total_time = time.time() - start_time
    
    # 生成报告
    print("\n" + "=" * 60)
    print("Main Import Test Results")
    print("=" * 60)
    
    passed = sum(1 for success in results.values() if success)
    total = len(results)
    
    for test_name, success in results.items():
        status = "PASS" if success else "FAIL"
        print(f"{test_name}: {status}")
    
    print(f"\nTotal: {passed}/{total} main tests passed")
    print(f"Time: {total_time:.2f}s")
    
    if passed == total:
        print("SUCCESS: All main import tests passed!")
        return True
    else:
        print(f"WARNING: {total - passed} main tests need attention")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)