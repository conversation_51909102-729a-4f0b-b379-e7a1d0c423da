package com.crypto.trading.common.enums;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * 枚举类测试
 * <p>
 * 测试枚举类是否可以正常使用
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class EnumsTest {

    /**
     * 测试KlineInterval枚举
     */
    @Test
    public void testKlineInterval() {
        assertEquals("1m", KlineInterval.MINUTE_1.getCode());
        assertEquals("15m", KlineInterval.MINUTE_15.getCode());
        assertEquals("1h", KlineInterval.HOUR_1.getCode());
        assertEquals("1d", KlineInterval.DAY_1.getCode());
        
        assertEquals(KlineInterval.MINUTE_1, KlineInterval.fromCode("1m"));
        assertEquals(KlineInterval.HOUR_1, KlineInterval.fromCode("1h"));
        
        assertThrows(IllegalArgumentException.class, () -> KlineInterval.fromCode("invalid"));
    }

    /**
     * 测试MarketDataType枚举
     */
    @Test
    public void testMarketDataType() {
        assertEquals("kline", MarketDataType.KLINE.getCode());
        assertEquals("depth", MarketDataType.DEPTH.getCode());
        assertEquals("trade", MarketDataType.TRADE.getCode());
        
        assertEquals(MarketDataType.KLINE, MarketDataType.fromCode("kline"));
        assertEquals(MarketDataType.DEPTH, MarketDataType.fromCode("depth"));
        
        assertThrows(IllegalArgumentException.class, () -> MarketDataType.fromCode("invalid"));
    }

    /**
     * 测试RiskLevel枚举
     */
    @Test
    public void testRiskLevel() {
        assertEquals(1, RiskLevel.VERY_LOW.getLevel());
        assertEquals(3, RiskLevel.MEDIUM.getLevel());
        assertEquals(5, RiskLevel.VERY_HIGH.getLevel());
        
        assertEquals(RiskLevel.VERY_LOW, RiskLevel.fromLevel(1));
        assertEquals(RiskLevel.MEDIUM, RiskLevel.fromLevel(3));
        
        assertTrue(RiskLevel.HIGH.isHigherThan(RiskLevel.MEDIUM));
        assertTrue(RiskLevel.LOW.isLowerThan(RiskLevel.MEDIUM));
        assertFalse(RiskLevel.MEDIUM.isHigherThan(RiskLevel.HIGH));
        
        assertThrows(IllegalArgumentException.class, () -> RiskLevel.fromLevel(10));
    }

    /**
     * 测试ExchangeType枚举
     */
    @Test
    public void testExchangeType() {
        assertEquals("binance", ExchangeType.BINANCE.getCode());
        assertEquals("binance_testnet", ExchangeType.BINANCE_TESTNET.getCode());
        assertEquals("okex", ExchangeType.OKEX.getCode());
        
        assertTrue(ExchangeType.BINANCE.isSupported());
        assertTrue(ExchangeType.BINANCE_TESTNET.isSupported());
        assertFalse(ExchangeType.OKEX.isSupported());
        
        assertEquals(ExchangeType.BINANCE, ExchangeType.fromCode("binance"));
        assertEquals(ExchangeType.OKEX, ExchangeType.fromCode("okex"));
        
        ExchangeType[] supported = ExchangeType.getSupportedExchanges();
        assertTrue(supported.length > 0);
        assertTrue(java.util.Arrays.stream(supported).allMatch(ExchangeType::isSupported));
        
        assertThrows(IllegalArgumentException.class, () -> ExchangeType.fromCode("invalid"));
    }

    /**
     * 测试StrategyType枚举
     */
    @Test
    public void testStrategyType() {
        assertEquals("sma", StrategyType.SMA.getCode());
        assertEquals("ema", StrategyType.EMA.getCode());
        assertEquals("bollingerBands", StrategyType.BOLLINGER_BANDS.getCode());
        
        assertEquals(StrategyType.SMA, StrategyType.fromCode("sma"));
        assertEquals(StrategyType.EMA, StrategyType.fromCode("ema"));
        
        assertEquals(RiskLevel.MEDIUM, StrategyType.SMA.getRiskLevel());
        assertEquals(RiskLevel.HIGH, StrategyType.RSI.getRiskLevel());
        
        StrategyType[] mediumRisk = StrategyType.getStrategiesByRiskLevel(RiskLevel.MEDIUM);
        assertTrue(mediumRisk.length > 0);
        assertTrue(java.util.Arrays.stream(mediumRisk).allMatch(s -> s.getRiskLevel() == RiskLevel.MEDIUM));
        
        StrategyType[] notHighRisk = StrategyType.getStrategiesNotHigherThan(RiskLevel.MEDIUM);
        assertTrue(notHighRisk.length > 0);
        assertTrue(java.util.Arrays.stream(notHighRisk).noneMatch(s -> s.getRiskLevel().isHigherThan(RiskLevel.MEDIUM)));
        
        assertThrows(IllegalArgumentException.class, () -> StrategyType.fromCode("invalid"));
    }
} 