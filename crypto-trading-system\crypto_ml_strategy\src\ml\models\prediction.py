#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
预测引擎模块

负责使用训练好的模型进行实时预测和信号生成。
支持多模型集成预测和置信度评估。
"""

import os
import pickle
from typing import Dict, List, Optional, Any, Union, Tuple
from datetime import datetime
import pandas as pd
import numpy as np
from loguru import logger

try:
    from sklearn.preprocessing import StandardScaler
    import joblib
except ImportError:
    logger.warning("scikit-learn not installed, some prediction functionality will be limited")
    StandardScaler = None
    joblib = None

try:
    import torch
    import torch.nn as nn
except ImportError:
    logger.warning("PyTorch not installed, neural network prediction will be limited")
    torch = None
    nn = None


class PredictionEngine:
    """
    预测引擎类，负责使用训练好的模型进行实时预测。
    
    支持功能：
    - 多模型加载和管理
    - 实时特征预处理
    - 单模型和集成预测
    - 置信度评估
    - 预测结果缓存
    - 性能监控
    """
    
    def __init__(self, model_version_manager):
        """
        初始化预测引擎
        
        Args:
            model_version_manager: 模型版本管理器
        """
        self.logger = logger.bind(component="PredictionEngine")
        
        self.model_version_manager = model_version_manager
        
        # 模型缓存
        self._loaded_models = {}  # {symbol: model_info}
        self._feature_scalers = {}  # {symbol: scaler}
        self._model_features = {}  # {symbol: feature_list}
        
        # 预测缓存
        self._prediction_cache = {}
        self._cache_ttl = 60  # 缓存60秒
        
        # 性能统计
        self._prediction_stats = {
            'total_predictions': 0,
            'cache_hits': 0,
            'prediction_times': []
        }
        
        self.logger.info("预测引擎初始化完成")
    
    def load_models(self, symbols: Optional[List[str]] = None) -> bool:
        """
        加载模型
        
        Args:
            symbols: 要加载的交易对列表，None表示加载所有活跃模型
        
        Returns:
            加载是否成功
        """
        try:
            success_count = 0
            
            if symbols is None:
                # 获取所有活跃模型
                symbols = self._get_active_model_symbols()
            
            for symbol in symbols:
                if self._load_model_for_symbol(symbol):
                    success_count += 1
                else:
                    self.logger.warning(f"模型加载失败: {symbol}")
            
            self.logger.info(f"模型加载完成，成功: {success_count}/{len(symbols)}")
            return success_count > 0
            
        except Exception as e:
            self.logger.error(f"模型加载失败: {str(e)}")
            return False
    
    def _get_active_model_symbols(self) -> List[str]:
        """
        获取所有有活跃模型的交易对
        
        Returns:
            交易对列表
        """
        try:
            # 这里应该从模型版本管理器获取所有活跃模型的交易对
            # 暂时返回常用的交易对
            return ['BTCUSDT', 'ETHUSDT']
            
        except Exception as e:
            self.logger.error(f"获取活跃模型交易对失败: {str(e)}")
            return []
    
    def _load_model_for_symbol(self, symbol: str) -> bool:
        """
        为指定交易对加载模型
        
        Args:
            symbol: 交易对符号
        
        Returns:
            加载是否成功
        """
        try:
            # 从模型版本管理器获取活跃模型信息
            model_info = self.model_version_manager.get_active_model_version('unified_ml')
            
            if not model_info:
                self.logger.warning(f"未找到活跃模型: {symbol}")
                return False
            
            model_path = model_info.get('model_path')
            if not model_path or not os.path.exists(model_path):
                self.logger.warning(f"模型文件不存在: {model_path}")
                return False
            
            # 加载模型文件
            loaded_model = self._load_model_file(model_path)
            
            if loaded_model:
                self._loaded_models[symbol] = loaded_model
                
                # 提取特征列表和标准化器
                if 'features' in loaded_model:
                    self._model_features[symbol] = loaded_model['features']
                
                if 'scaler' in loaded_model and loaded_model['scaler'] is not None:
                    self._feature_scalers[symbol] = loaded_model['scaler']
                
                self.logger.info(f"模型加载成功: {symbol}, 类型: {loaded_model.get('model_type', 'unknown')}")
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"加载模型失败 {symbol}: {str(e)}")
            return False
    
    def _load_model_file(self, model_path: str) -> Optional[Dict[str, Any]]:
        """
        加载模型文件
        
        Args:
            model_path: 模型文件路径
        
        Returns:
            加载的模型信息
        """
        try:
            # 尝试加载PyTorch模型
            if torch is not None:
                try:
                    checkpoint = torch.load(model_path, map_location='cpu')
                    if 'model_state_dict' in checkpoint:
                        # 重建神经网络模型
                        model = self._rebuild_neural_network(checkpoint)
                        if model:
                            checkpoint['model'] = model
                            return checkpoint
                except:
                    pass
            
            # 加载sklearn模型
            if joblib is not None:
                try:
                    model_data = joblib.load(model_path)
                    return model_data
                except:
                    pass
            
            # 尝试pickle加载
            try:
                with open(model_path, 'rb') as f:
                    model_data = pickle.load(f)
                return model_data
            except:
                pass
            
            self.logger.error(f"无法加载模型文件: {model_path}")
            return None
            
        except Exception as e:
            self.logger.error(f"加载模型文件失败: {str(e)}")
            return None
    
    def _rebuild_neural_network(self, checkpoint: Dict[str, Any]) -> Optional[nn.Module]:
        """
        重建神经网络模型
        
        Args:
            checkpoint: 模型检查点
        
        Returns:
            重建的模型
        """
        try:
            if torch is None:
                return None
            
            # 简单的神经网络重建（实际应用中可能需要更复杂的逻辑）
            features = checkpoint.get('features', [])
            input_size = len(features)
            
            class TradingNet(nn.Module):
                def __init__(self, input_size, hidden_size=128, num_classes=3):
                    super(TradingNet, self).__init__()
                    self.fc1 = nn.Linear(input_size, hidden_size)
                    self.fc2 = nn.Linear(hidden_size, hidden_size // 2)
                    self.fc3 = nn.Linear(hidden_size // 2, num_classes)
                    self.dropout = nn.Dropout(0.3)
                    self.relu = nn.ReLU()
                    
                def forward(self, x):
                    x = self.relu(self.fc1(x))
                    x = self.dropout(x)
                    x = self.relu(self.fc2(x))
                    x = self.dropout(x)
                    x = self.fc3(x)
                    return x
            
            model = TradingNet(input_size)
            model.load_state_dict(checkpoint['model_state_dict'])
            model.eval()
            
            return model
            
        except Exception as e:
            self.logger.error(f"重建神经网络失败: {str(e)}")
            return None
    
    def predict(self, symbol: str, features: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        进行预测
        
        Args:
            symbol: 交易对符号
            features: 特征字典
        
        Returns:
            预测结果字典
        """
        try:
            start_time = datetime.now()
            
            # 检查缓存
            cache_key = f"{symbol}_{hash(str(sorted(features.items())))}"
            cached_result = self._get_cached_prediction(cache_key)
            
            if cached_result:
                self._prediction_stats['cache_hits'] += 1
                return cached_result
            
            # 检查模型是否已加载
            if symbol not in self._loaded_models:
                if not self._load_model_for_symbol(symbol):
                    self.logger.error(f"无法加载模型: {symbol}")
                    return None
            
            model_info = self._loaded_models[symbol]
            
            # 预处理特征
            processed_features = self._preprocess_features(symbol, features)
            
            if processed_features is None:
                self.logger.error(f"特征预处理失败: {symbol}")
                return None
            
            # 执行预测
            prediction_result = self._execute_prediction(symbol, model_info, processed_features)
            
            if prediction_result:
                # 缓存结果
                self._cache_prediction(cache_key, prediction_result)
                
                # 更新统计
                prediction_time = (datetime.now() - start_time).total_seconds() * 1000
                self._prediction_stats['total_predictions'] += 1
                self._prediction_stats['prediction_times'].append(prediction_time)
                
                # 保持统计列表大小
                if len(self._prediction_stats['prediction_times']) > 1000:
                    self._prediction_stats['prediction_times'] = self._prediction_stats['prediction_times'][-1000:]
                
                self.logger.debug(f"预测完成: {symbol}, 耗时: {prediction_time:.2f}ms")
                
                return prediction_result
            
            return None
            
        except Exception as e:
            self.logger.error(f"预测失败: {str(e)}")
            return None
    
    def _preprocess_features(self, symbol: str, features: Dict[str, Any]) -> Optional[np.ndarray]:
        """
        预处理特征
        
        Args:
            symbol: 交易对符号
            features: 原始特征字典
        
        Returns:
            预处理后的特征数组
        """
        try:
            # 获取模型需要的特征列表
            required_features = self._model_features.get(symbol, [])
            
            if not required_features:
                self.logger.error(f"未找到模型特征列表: {symbol}")
                return None
            
            # 提取特征值
            feature_values = []
            missing_features = []
            
            for feature_name in required_features:
                if feature_name in features:
                    value = features[feature_name]
                    # 处理NaN值
                    if pd.isna(value):
                        value = 0.0
                    feature_values.append(float(value))
                else:
                    missing_features.append(feature_name)
                    feature_values.append(0.0)  # 用0填充缺失特征
            
            if missing_features:
                self.logger.warning(f"缺失特征: {symbol}, {missing_features[:5]}...")  # 只显示前5个
            
            # 转换为numpy数组
            feature_array = np.array(feature_values).reshape(1, -1)
            
            # 应用标准化
            if symbol in self._feature_scalers:
                scaler = self._feature_scalers[symbol]
                feature_array = scaler.transform(feature_array)
            
            return feature_array
            
        except Exception as e:
            self.logger.error(f"特征预处理失败: {str(e)}")
            return None
    
    def _execute_prediction(self, symbol: str, model_info: Dict[str, Any], 
                           features: np.ndarray) -> Optional[Dict[str, Any]]:
        """
        执行模型预测
        
        Args:
            symbol: 交易对符号
            model_info: 模型信息
            features: 预处理后的特征
        
        Returns:
            预测结果
        """
        try:
            model = model_info['model']
            model_type = model_info.get('model_type', 'unknown')
            
            if model_type == 'neural_network' and torch is not None:
                # PyTorch模型预测
                return self._predict_neural_network(model, features, symbol)
            else:
                # sklearn模型预测
                return self._predict_sklearn_model(model, features, symbol)
            
        except Exception as e:
            self.logger.error(f"模型预测执行失败: {str(e)}")
            return None
    
    def _predict_neural_network(self, model: nn.Module, features: np.ndarray, symbol: str) -> Dict[str, Any]:
        """
        神经网络预测
        
        Args:
            model: PyTorch模型
            features: 特征数组
            symbol: 交易对符号
        
        Returns:
            预测结果
        """
        try:
            model.eval()
            
            with torch.no_grad():
                # 转换为tensor
                features_tensor = torch.FloatTensor(features)
                
                # 预测
                outputs = model(features_tensor)
                probabilities = torch.softmax(outputs, dim=1)
                predicted_class = torch.argmax(outputs, dim=1)
                
                # 转换回原始标签范围 (-1, 0, 1)
                signal = predicted_class.item() - 1
                confidence = probabilities.max().item()
                
                # 获取各类别概率
                proba_dict = {
                    'sell_prob': probabilities[0][0].item(),
                    'hold_prob': probabilities[0][1].item(),
                    'buy_prob': probabilities[0][2].item()
                }
                
                return {
                    'signal': signal,
                    'confidence': confidence,
                    'probabilities': proba_dict,
                    'model_type': 'neural_network',
                    'symbol': symbol,
                    'timestamp': datetime.now().isoformat()
                }
                
        except Exception as e:
            self.logger.error(f"神经网络预测失败: {str(e)}")
            return None
    
    def _predict_sklearn_model(self, model, features: np.ndarray, symbol: str) -> Dict[str, Any]:
        """
        sklearn模型预测
        
        Args:
            model: sklearn模型
            features: 特征数组
            symbol: 交易对符号
        
        Returns:
            预测结果
        """
        try:
            # 预测类别
            predicted_class = model.predict(features)[0]
            
            # 预测概率
            if hasattr(model, 'predict_proba'):
                probabilities = model.predict_proba(features)[0]
                confidence = probabilities.max()
                
                # 构建概率字典（假设类别顺序为 -1, 0, 1）
                proba_dict = {}
                classes = getattr(model, 'classes_', [-1, 0, 1])
                
                for i, cls in enumerate(classes):
                    if cls == -1:
                        proba_dict['sell_prob'] = probabilities[i]
                    elif cls == 0:
                        proba_dict['hold_prob'] = probabilities[i]
                    elif cls == 1:
                        proba_dict['buy_prob'] = probabilities[i]
            else:
                confidence = 0.5  # 默认置信度
                proba_dict = {'sell_prob': 0.33, 'hold_prob': 0.34, 'buy_prob': 0.33}
            
            return {
                'signal': int(predicted_class),
                'confidence': float(confidence),
                'probabilities': proba_dict,
                'model_type': type(model).__name__,
                'symbol': symbol,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"sklearn模型预测失败: {str(e)}")
            return None
    
    def _get_cached_prediction(self, cache_key: str) -> Optional[Dict[str, Any]]:
        """
        获取缓存的预测结果
        
        Args:
            cache_key: 缓存键
        
        Returns:
            缓存的预测结果
        """
        try:
            if cache_key in self._prediction_cache:
                cached_item = self._prediction_cache[cache_key]
                
                # 检查是否过期
                if (datetime.now() - cached_item['timestamp']).total_seconds() < self._cache_ttl:
                    return cached_item['result']
                else:
                    # 删除过期缓存
                    del self._prediction_cache[cache_key]
            
            return None
            
        except Exception as e:
            self.logger.error(f"获取缓存预测失败: {str(e)}")
            return None
    
    def _cache_prediction(self, cache_key: str, result: Dict[str, Any]) -> None:
        """
        缓存预测结果
        
        Args:
            cache_key: 缓存键
            result: 预测结果
        """
        try:
            self._prediction_cache[cache_key] = {
                'result': result,
                'timestamp': datetime.now()
            }
            
            # 限制缓存大小
            if len(self._prediction_cache) > 1000:
                # 删除最旧的缓存项
                oldest_key = min(self._prediction_cache.keys(), 
                               key=lambda k: self._prediction_cache[k]['timestamp'])
                del self._prediction_cache[oldest_key]
                
        except Exception as e:
            self.logger.error(f"缓存预测结果失败: {str(e)}")
    
    def predict_batch(self, predictions_data: List[Dict[str, Any]]) -> List[Optional[Dict[str, Any]]]:
        """
        批量预测
        
        Args:
            predictions_data: 预测数据列表，每个元素包含symbol和features
        
        Returns:
            预测结果列表
        """
        try:
            results = []
            
            for data in predictions_data:
                symbol = data.get('symbol')
                features = data.get('features')
                
                if symbol and features:
                    result = self.predict(symbol, features)
                    results.append(result)
                else:
                    results.append(None)
            
            return results
            
        except Exception as e:
            self.logger.error(f"批量预测失败: {str(e)}")
            return [None] * len(predictions_data)
    
    def get_prediction_stats(self) -> Dict[str, Any]:
        """
        获取预测统计信息
        
        Returns:
            统计信息字典
        """
        try:
            stats = self._prediction_stats.copy()
            
            if stats['prediction_times']:
                stats['avg_prediction_time'] = np.mean(stats['prediction_times'])
                stats['max_prediction_time'] = np.max(stats['prediction_times'])
                stats['min_prediction_time'] = np.min(stats['prediction_times'])
            else:
                stats['avg_prediction_time'] = 0
                stats['max_prediction_time'] = 0
                stats['min_prediction_time'] = 0
            
            if stats['total_predictions'] > 0:
                stats['cache_hit_rate'] = stats['cache_hits'] / stats['total_predictions']
            else:
                stats['cache_hit_rate'] = 0
            
            stats['loaded_models'] = list(self._loaded_models.keys())
            stats['cache_size'] = len(self._prediction_cache)
            
            return stats
            
        except Exception as e:
            self.logger.error(f"获取预测统计失败: {str(e)}")
            return {}
    
    def clear_cache(self) -> None:
        """清空预测缓存"""
        try:
            self._prediction_cache.clear()
            self.logger.info("预测缓存已清空")
            
        except Exception as e:
            self.logger.error(f"清空缓存失败: {str(e)}")
    
    def reload_model(self, symbol: str) -> bool:
        """
        重新加载指定交易对的模型
        
        Args:
            symbol: 交易对符号
        
        Returns:
            重新加载是否成功
        """
        try:
            # 清除现有模型
            if symbol in self._loaded_models:
                del self._loaded_models[symbol]
            if symbol in self._feature_scalers:
                del self._feature_scalers[symbol]
            if symbol in self._model_features:
                del self._model_features[symbol]
            
            # 重新加载
            success = self._load_model_for_symbol(symbol)
            
            if success:
                self.logger.info(f"模型重新加载成功: {symbol}")
            else:
                self.logger.error(f"模型重新加载失败: {symbol}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"重新加载模型失败: {str(e)}")
            return False