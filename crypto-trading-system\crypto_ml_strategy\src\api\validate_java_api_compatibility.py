#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Java API兼容性组件验证脚本

验证所有Java API兼容性相关组件的语法和导入正确性。
"""

import sys
import ast
import importlib.util
from pathlib import Path
from typing import List, Dict, Any
from loguru import logger


def validate_python_syntax(file_path: Path) -> Dict[str, Any]:
    """
    验证Python文件语法
    
    Args:
        file_path: 文件路径
        
    Returns:
        验证结果
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 语法检查
        ast.parse(content)
        
        return {
            "file": str(file_path),
            "syntax_valid": True,
            "error": None,
            "lines": len(content.splitlines())
        }
        
    except SyntaxError as e:
        return {
            "file": str(file_path),
            "syntax_valid": False,
            "error": f"语法错误: {e}",
            "line": e.lineno,
            "lines": 0
        }
    except Exception as e:
        return {
            "file": str(file_path),
            "syntax_valid": False,
            "error": f"其他错误: {e}",
            "lines": 0
        }


def validate_imports(file_path: Path) -> Dict[str, Any]:
    """
    验证文件导入
    
    Args:
        file_path: 文件路径
        
    Returns:
        验证结果
    """
    try:
        spec = importlib.util.spec_from_file_location("test_module", file_path)
        if spec is None:
            return {
                "file": str(file_path),
                "import_valid": False,
                "error": "无法创建模块规范"
            }
        
        module = importlib.util.module_from_spec(spec)
        
        # 尝试执行模块（这会触发导入）
        spec.loader.exec_module(module)
        
        return {
            "file": str(file_path),
            "import_valid": True,
            "error": None
        }
        
    except ImportError as e:
        return {
            "file": str(file_path),
            "import_valid": False,
            "error": f"导入错误: {e}"
        }
    except Exception as e:
        return {
            "file": str(file_path),
            "import_valid": False,
            "error": f"其他错误: {e}"
        }


def main():
    """主验证函数"""
    logger.info("🔍 开始验证Java API兼容性组件")
    
    # 要验证的文件列表
    files_to_validate = [
        "java_api_core.py",
        "kafka_compatibility_tests.py", 
        "serialization_compatibility.py",
        "performance_benchmarks.py",
        "integration_test_suite.py",
        "api_compatibility_docs.py",
        "java_api_compatibility_runner.py"
    ]
    
    src_dir = Path(__file__).parent
    
    validation_results = {
        "syntax_results": [],
        "import_results": [],
        "summary": {
            "total_files": len(files_to_validate),
            "syntax_passed": 0,
            "syntax_failed": 0,
            "import_passed": 0,
            "import_failed": 0,
            "total_lines": 0
        }
    }
    
    # 语法验证
    logger.info("📝 执行语法验证...")
    for file_name in files_to_validate:
        file_path = src_dir / file_name
        
        if file_path.exists():
            syntax_result = validate_python_syntax(file_path)
            validation_results["syntax_results"].append(syntax_result)
            
            if syntax_result["syntax_valid"]:
                validation_results["summary"]["syntax_passed"] += 1
                validation_results["summary"]["total_lines"] += syntax_result["lines"]
                logger.info(f"✅ {file_name}: 语法正确 ({syntax_result['lines']} 行)")
            else:
                validation_results["summary"]["syntax_failed"] += 1
                logger.error(f"❌ {file_name}: {syntax_result['error']}")
        else:
            logger.warning(f"⚠️ 文件不存在: {file_name}")
    
    # 导入验证（仅对语法正确的文件）
    logger.info("📦 执行导入验证...")
    for syntax_result in validation_results["syntax_results"]:
        if syntax_result["syntax_valid"]:
            file_path = Path(syntax_result["file"])
            import_result = validate_imports(file_path)
            validation_results["import_results"].append(import_result)
            
            if import_result["import_valid"]:
                validation_results["summary"]["import_passed"] += 1
                logger.info(f"✅ {file_path.name}: 导入成功")
            else:
                validation_results["summary"]["import_failed"] += 1
                logger.error(f"❌ {file_path.name}: {import_result['error']}")
    
    # 输出摘要
    summary = validation_results["summary"]
    logger.info("📊 验证摘要:")
    logger.info(f"总文件数: {summary['total_files']}")
    logger.info(f"语法验证: {summary['syntax_passed']}/{summary['total_files']} 通过")
    logger.info(f"导入验证: {summary['import_passed']}/{summary['syntax_passed']} 通过")
    logger.info(f"总代码行数: {summary['total_lines']}")
    
    # 计算成功率
    syntax_success_rate = summary['syntax_passed'] / summary['total_files'] if summary['total_files'] > 0 else 0
    import_success_rate = summary['import_passed'] / summary['syntax_passed'] if summary['syntax_passed'] > 0 else 0
    
    logger.info(f"语法成功率: {syntax_success_rate:.2%}")
    logger.info(f"导入成功率: {import_success_rate:.2%}")
    
    # 整体评估
    if syntax_success_rate == 1.0 and import_success_rate == 1.0:
        logger.info("🎉 所有Java API兼容性组件验证通过！")
        return True
    elif syntax_success_rate >= 0.8 and import_success_rate >= 0.8:
        logger.warning("⚠️ 大部分组件验证通过，但存在少量问题")
        return False
    else:
        logger.error("❌ 验证失败，存在严重问题需要修复")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)