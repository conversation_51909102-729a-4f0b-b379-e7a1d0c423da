package com.crypto.trading.sdk.websocket;

import org.java_websocket.client.WebSocketClient;
import org.java_websocket.handshake.ServerHandshake;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.URI;
import java.time.Duration;
import java.time.Instant;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Consumer;

/**
 * WebSocket连接类
 * 负责管理与交易所的WebSocket连接，包括连接建立、消息处理、心跳保持和自动重连功能
 */
public class WebSocketConnection {
    
    private static final Logger log = LoggerFactory.getLogger(WebSocketConnection.class);
    
    /**
     * 默认重连间隔（毫秒）
     */
    private static final long DEFAULT_RECONNECT_INTERVAL = 3000;
    
    /**
     * 最大重连次数
     */
    private static final int MAX_RECONNECT_ATTEMPTS = 10;
    
    /**
     * 心跳间隔（毫秒）
     */
    private static final long HEARTBEAT_INTERVAL = 30000;
    
    /**
     * 连接ID
     */
    private final int connectionId;
    
    /**
     * WebSocket连接URI
     */
    private final URI uri;
    
    /**
     * 消息处理器
     */
    private final Consumer<String> messageHandler;
    
    /**
     * 连接打开回调
     */
    private final Runnable onOpenCallback;
    
    /**
     * 连接关闭回调
     */
    private final Consumer<Integer> onCloseCallback;
    
    /**
     * 连接失败回调
     */
    private final Consumer<Throwable> onFailureCallback;
    
    /**
     * WebSocket客户端
     */
    private WebSocketClient client;
    
    /**
     * 是否连接中
     */
    private final AtomicBoolean isConnected = new AtomicBoolean(false);
    
    /**
     * 是否正在重连
     */
    private final AtomicBoolean isReconnecting = new AtomicBoolean(false);
    
    /**
     * 重连尝试次数
     */
    private final AtomicInteger reconnectAttempts = new AtomicInteger(0);
    
    /**
     * 最后一次接收消息的时间
     */
    private final AtomicLong lastMessageTime = new AtomicLong(System.currentTimeMillis());
    
    /**
     * 心跳调度器
     */
    private ScheduledExecutorService heartbeatScheduler;
    
    /**
     * 重连调度器
     */
    private ScheduledExecutorService reconnectScheduler;
    
    /**
     * 创建WebSocket连接
     *
     * @param connectionId      连接ID
     * @param uri               WebSocket连接URI
     * @param messageHandler    消息处理器
     * @param onOpenCallback    连接打开回调
     * @param onCloseCallback   连接关闭回调
     * @param onFailureCallback 连接失败回调
     * @return WebSocket连接实例
     */
    public static WebSocketConnection create(int connectionId, URI uri, Consumer<String> messageHandler,
                                           Runnable onOpenCallback, Consumer<Integer> onCloseCallback,
                                           Consumer<Throwable> onFailureCallback) {
        return new WebSocketConnection(connectionId, uri, messageHandler, onOpenCallback, onCloseCallback, onFailureCallback);
    }
    
    /**
     * 构造函数
     *
     * @param connectionId      连接ID
     * @param uri               WebSocket连接URI
     * @param messageHandler    消息处理器
     * @param onOpenCallback    连接打开回调
     * @param onCloseCallback   连接关闭回调
     * @param onFailureCallback 连接失败回调
     */
    public WebSocketConnection(int connectionId, URI uri, Consumer<String> messageHandler,
                             Runnable onOpenCallback, Consumer<Integer> onCloseCallback,
                             Consumer<Throwable> onFailureCallback) {
        this.connectionId = connectionId;
        this.uri = uri;
        this.messageHandler = messageHandler;
        this.onOpenCallback = onOpenCallback;
        this.onCloseCallback = onCloseCallback;
        this.onFailureCallback = onFailureCallback;
        initializeClient();
    }
    
    /**
     * 初始化WebSocket客户端
     */
    private void initializeClient() {
        client = new WebSocketClient(uri) {
            @Override
            public void onOpen(ServerHandshake handshake) {
                log.info("WebSocket连接已打开: {}, 状态: {}", uri, handshake.getHttpStatus());
                isConnected.set(true);
                reconnectAttempts.set(0);
                lastMessageTime.set(System.currentTimeMillis());
                startHeartbeat();
                
                if (onOpenCallback != null) {
                    // 使用虚拟线程执行回调，避免阻塞WebSocket线程
                    Thread.startVirtualThread(() -> {
                        try {
                            onOpenCallback.run();
                        } catch (Exception e) {
                            log.error("执行onOpen回调时发生异常", e);
                        }
                    });
                }
            }
            
            @Override
            public void onMessage(String message) {
                lastMessageTime.set(System.currentTimeMillis());

                // 添加详细的消息接收日志
                log.debug("WebSocket收到消息: connectionId={}, uri={}, messageLength={}",
                        connectionId, uri, message != null ? message.length() : 0);
                log.trace("WebSocket消息内容: connectionId={}, message={}", connectionId, message);

                if (messageHandler != null) {
                    // 使用虚拟线程处理消息，避免阻塞WebSocket线程
                    Thread.startVirtualThread(() -> {
                        try {
                            log.debug("开始处理WebSocket消息: connectionId={}", connectionId);
                            messageHandler.accept(message);
                            log.debug("WebSocket消息处理完成: connectionId={}", connectionId);
                        } catch (Exception e) {
                            log.error("处理WebSocket消息时发生异常: connectionId={}, message={}",
                                    connectionId, message, e);
                        }
                    });
                } else {
                    log.warn("WebSocket消息处理器为空: connectionId={}, uri={}", connectionId, uri);
                }
            }
            
            @Override
            public void onClose(int code, String reason, boolean remote) {
                log.info("WebSocket连接已关闭: {}, 代码: {}, 原因: {}, 远程关闭: {}", uri, code, reason, remote);
                isConnected.set(false);
                stopHeartbeat();
                
                if (onCloseCallback != null) {
                    // 使用虚拟线程执行回调，避免阻塞WebSocket线程
                    Thread.startVirtualThread(() -> {
                        try {
                            onCloseCallback.accept(code);
                        } catch (Exception e) {
                            log.error("执行onClose回调时发生异常", e);
                        }
                    });
                }
                
                // 如果是远程关闭且不是正常关闭，尝试重连
                if (remote && code != 1000 && !isReconnecting.get()) {
                    scheduleReconnect();
                }
            }
            
            @Override
            public void onError(Exception ex) {
                log.error("WebSocket连接错误: {}", uri, ex);
                
                if (onFailureCallback != null) {
                    // 使用虚拟线程执行回调，避免阻塞WebSocket线程
                    Thread.startVirtualThread(() -> {
                        try {
                            onFailureCallback.accept(ex);
                        } catch (Exception e) {
                            log.error("执行onError回调时发生异常", e);
                        }
                    });
                }
                
                // 如果连接断开且不是正在重连，尝试重连
                if (!isConnected.get() && !isReconnecting.get()) {
                    scheduleReconnect();
                }
            }
        };
    }
    
    /**
     * 连接到WebSocket服务器
     */
    public void connect() {
        if (!isConnected.get() && !client.isOpen()) {
            try {
                log.info("正在连接到WebSocket服务器: {}", uri);
                client.connect();
            } catch (Exception e) {
                log.error("连接WebSocket服务器时发生异常: {}", uri, e);
                if (onFailureCallback != null) {
                    onFailureCallback.accept(e);
                }
                scheduleReconnect();
            }
        }
    }
    
    /**
     * 关闭WebSocket连接
     */
    public void close() {
        log.info("正在关闭WebSocket连接: {}", uri);
        stopHeartbeat();
        stopReconnect();
        
        if (client != null && (client.isOpen() || isConnected.get())) {
            try {
                client.close();
            } catch (Exception e) {
                log.error("关闭WebSocket连接时发生异常: {}", uri, e);
            }
        }
        isConnected.set(false);
    }
    
    /**
     * 发送消息
     *
     * @param message 消息内容
     * @return 是否发送成功
     */
    public boolean sendMessage(String message) {
        if (client != null && client.isOpen()) {
            try {
                client.send(message);
                return true;
            } catch (Exception e) {
                log.error("发送WebSocket消息时发生异常: {}", message, e);
                return false;
            }
        }
        return false;
    }
    
    /**
     * 启动心跳检测
     */
    private void startHeartbeat() {
        stopHeartbeat();
        heartbeatScheduler = Executors.newSingleThreadScheduledExecutor();
        heartbeatScheduler.scheduleAtFixedRate(() -> {
            if (client != null && client.isOpen()) {
                // 检查最后一次消息时间，如果超过2倍心跳间隔没有消息，认为连接已断开
                long elapsed = System.currentTimeMillis() - lastMessageTime.get();
                if (elapsed > HEARTBEAT_INTERVAL * 2) {
                    log.warn("WebSocket连接心跳超时: {}, 已经{}ms没有收到消息", uri, elapsed);
                    reconnect();
                } else {
                    // 发送心跳消息
                    try {
                        client.sendPing();
                    } catch (Exception e) {
                        log.error("发送WebSocket心跳时发生异常", e);
                    }
                }
            }
        }, HEARTBEAT_INTERVAL, HEARTBEAT_INTERVAL, TimeUnit.MILLISECONDS);
    }
    
    /**
     * 停止心跳检测
     */
    private void stopHeartbeat() {
        if (heartbeatScheduler != null && !heartbeatScheduler.isShutdown()) {
            heartbeatScheduler.shutdownNow();
            heartbeatScheduler = null;
        }
    }
    
    /**
     * 安排重连
     */
    private void scheduleReconnect() {
        if (isReconnecting.compareAndSet(false, true)) {
            stopReconnect();
            reconnectScheduler = Executors.newSingleThreadScheduledExecutor();
            reconnectScheduler.schedule(this::reconnect, DEFAULT_RECONNECT_INTERVAL, TimeUnit.MILLISECONDS);
        }
    }
    
    /**
     * 停止重连
     */
    private void stopReconnect() {
        if (reconnectScheduler != null && !reconnectScheduler.isShutdown()) {
            reconnectScheduler.shutdownNow();
            reconnectScheduler = null;
        }
    }
    
    /**
     * 重新连接
     */
    private void reconnect() {
        if (!isConnected.get() && reconnectAttempts.incrementAndGet() <= MAX_RECONNECT_ATTEMPTS) {
            log.info("正在尝试重新连接WebSocket: {}, 尝试次数: {}/{}", uri, reconnectAttempts.get(), MAX_RECONNECT_ATTEMPTS);
            close();
            initializeClient();
            connect();
            isReconnecting.set(false);
        } else if (reconnectAttempts.get() > MAX_RECONNECT_ATTEMPTS) {
            log.error("WebSocket重连失败，已达到最大重试次数: {}", uri);
            isReconnecting.set(false);
            close();
        }
    }
    
    /**
     * 获取连接ID
     *
     * @return 连接ID
     */
    public int getConnectionId() {
        return connectionId;
    }
    
    /**
     * 获取WebSocket URI
     *
     * @return WebSocket URI
     */
    public URI getUri() {
        return uri;
    }
    
    /**
     * 检查连接是否活跃
     *
     * @return 连接是否活跃
     */
    public boolean isActive() {
        return client != null && client.isOpen() && isConnected.get();
    }
    
    /**
     * 获取连接状态信息
     *
     * @return 连接状态信息
     */
    @Override
    public String toString() {
        return "WebSocketConnection{" +
                "connectionId=" + connectionId +
                ", uri=" + uri +
                ", connected=" + isConnected.get() +
                ", reconnecting=" + isReconnecting.get() +
                ", reconnectAttempts=" + reconnectAttempts.get() +
                '}';
    }
} 