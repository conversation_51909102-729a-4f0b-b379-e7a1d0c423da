package com.crypto.trading.common.dto.account;

import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

/**
 * BalanceDTO单元测试类
 */
class BalanceDTOTest {

    /**
     * 测试默认构造函数
     */
    @Test
    void testDefaultConstructor() {
        BalanceDTO dto = new BalanceDTO();
        assertNotNull(dto);
    }

    /**
     * 测试带参数构造函数
     */
    @Test
    void testParameterizedConstructor() {
        String accountId = "account-001";
        String asset = "BTC";
        BigDecimal total = new BigDecimal("1.5");
        BigDecimal free = new BigDecimal("1.0");
        BigDecimal locked = new BigDecimal("0.5");
        LocalDateTime updateTime = LocalDateTime.now();

        BalanceDTO dto = new BalanceDTO(
                accountId, asset, total, free, locked, updateTime
        );

        assertNotNull(dto);
        assertEquals(accountId, dto.getAccountId());
        assertEquals(asset, dto.getAsset());
        assertEquals(total, dto.getTotal());
        assertEquals(free, dto.getFree());
        assertEquals(locked, dto.getLocked());
        assertEquals(updateTime, dto.getUpdateTime());
    }

    /**
     * 测试getter和setter方法
     */
    @Test
    void testGettersAndSetters() {
        BalanceDTO dto = new BalanceDTO();

        String accountId = "account-002";
        dto.setAccountId(accountId);
        assertEquals(accountId, dto.getAccountId());

        String asset = "ETH";
        dto.setAsset(asset);
        assertEquals(asset, dto.getAsset());

        BigDecimal total = new BigDecimal("10.0");
        dto.setTotal(total);
        assertEquals(total, dto.getTotal());

        BigDecimal free = new BigDecimal("8.0");
        dto.setFree(free);
        assertEquals(free, dto.getFree());

        BigDecimal locked = new BigDecimal("2.0");
        dto.setLocked(locked);
        assertEquals(locked, dto.getLocked());

        LocalDateTime updateTime = LocalDateTime.now();
        dto.setUpdateTime(updateTime);
        assertEquals(updateTime, dto.getUpdateTime());
    }

    /**
     * 测试toString方法
     */
    @Test
    void testToString() {
        BalanceDTO dto = new BalanceDTO();
        dto.setAccountId("account-003");
        dto.setAsset("USDT");
        dto.setTotal(new BigDecimal("5000.00"));
        dto.setFree(new BigDecimal("4000.00"));
        dto.setLocked(new BigDecimal("1000.00"));

        String toString = dto.toString();
        assertNotNull(toString);
        assertTrue(toString.contains("account-003"));
        assertTrue(toString.contains("USDT"));
        assertTrue(toString.contains("5000.00"));
        assertTrue(toString.contains("4000.00"));
        assertTrue(toString.contains("1000.00"));
    }
} 