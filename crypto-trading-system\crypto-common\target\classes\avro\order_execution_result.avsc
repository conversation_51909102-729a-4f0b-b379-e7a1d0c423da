{"namespace": "com.crypto.trading.common.avro", "type": "record", "name": "OrderExecutionResult", "doc": "订单执行结果Avro模式定义", "fields": [{"name": "id", "type": "string", "doc": "唯一消息ID"}, {"name": "timestamp", "type": "long", "doc": "消息时间戳(毫秒)"}, {"name": "type", "type": "string", "doc": "消息类型，固定为ORDER_EXECUTION_RESULT"}, {"name": "source", "type": "string", "doc": "消息来源模块"}, {"name": "version", "type": "string", "doc": "消息版本"}, {"name": "data", "type": {"type": "record", "name": "OrderExecutionResultData", "fields": [{"name": "orderId", "type": "long", "doc": "订单ID"}, {"name": "clientOrderId", "type": ["string", "null"], "doc": "客户端订单ID"}, {"name": "symbol", "type": "string", "doc": "交易对符号"}, {"name": "accountId", "type": "string", "doc": "账户ID"}, {"name": "price", "type": ["double", "null"], "doc": "订单价格"}, {"name": "origQty", "type": "double", "doc": "原始数量"}, {"name": "executedQty", "type": "double", "doc": "已执行数量"}, {"name": "cummulative<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": ["double", "null"], "doc": "累计成交金额"}, {"name": "status", "type": "string", "doc": "订单状态"}, {"name": "type", "type": "string", "doc": "订单类型"}, {"name": "side", "type": "string", "doc": "订单方向"}, {"name": "positionSide", "type": ["string", "null"], "doc": "持仓方向"}, {"name": "stopPrice", "type": ["double", "null"], "doc": "止损价格"}, {"name": "avgPrice", "type": ["double", "null"], "doc": "平均成交价格"}, {"name": "commissionAsset", "type": ["string", "null"], "doc": "手续费资产"}, {"name": "commissionAmount", "type": ["double", "null"], "doc": "手续费金额"}, {"name": "time", "type": "long", "doc": "订单时间(毫秒)"}, {"name": "updateTime", "type": "long", "doc": "更新时间(毫秒)"}, {"name": "isWorking", "type": "boolean", "doc": "订单是否生效中"}, {"name": "fills", "type": ["string", "null"], "doc": "成交明细JSON"}, {"name": "errorCode", "type": ["int", "null"], "doc": "错误代码"}, {"name": "errorMessage", "type": ["string", "null"], "doc": "错误信息"}]}, "doc": "订单执行结果数据"}]}