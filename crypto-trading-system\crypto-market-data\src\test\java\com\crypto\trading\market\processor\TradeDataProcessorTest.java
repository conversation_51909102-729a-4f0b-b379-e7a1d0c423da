package com.crypto.trading.market.processor;

import com.crypto.trading.common.dto.market.TradeDTO;
import com.crypto.trading.market.converter.MarketDataConverter;
import com.crypto.trading.market.producer.AsyncMarketDataProducer;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.kafka.support.SendResult;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.concurrent.CompletableFuture;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * TradeDataProcessor单元测试
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class TradeDataProcessorTest {

    @Mock
    private AsyncMarketDataProducer asyncMarketDataProducer;

    @Mock
    private MarketDataConverter marketDataConverter;

    @InjectMocks
    private TradeDataProcessor tradeDataProcessor;

    private TradeDTO tradeDTO;
    private String tradeMessage;

    @BeforeEach
    void setUp() throws Exception {
        // 准备测试数据
        tradeDTO = new TradeDTO(
                987654321L,
                "BTCUSDT",
                new BigDecimal("35000.00"),
                new BigDecimal("0.5"),
                new BigDecimal("17500.00"),
                LocalDateTime.now(),
                false,
                true
        );
        
        tradeMessage = "{\"symbol\":\"BTCUSDT\",\"price\":\"35000.00\",\"quantity\":\"0.5\"}";

        // 配置mock行为
        doReturn(tradeDTO).when(marketDataConverter).convertAggTradeMessageToTradeDTO(anyString());

        // 配置AsyncMarketDataProducer返回成功的CompletableFuture
        CompletableFuture<SendResult<String, Object>> successFuture = CompletableFuture.completedFuture(null);
        doReturn(successFuture).when(asyncMarketDataProducer).sendTradeRawData(any(TradeDTO.class));
    }

    @Test
    void testProcessTradeData() throws Exception {
        // 调用被测试方法
        tradeDataProcessor.processTradeData(tradeMessage);

        // 验证发送到Kafka
        verify(asyncMarketDataProducer).sendTradeRawData(eq(tradeDTO));
    }

    @Test
    void testProcessTradeDataWithKafkaException() throws Exception {
        // 配置mock抛出异常
        CompletableFuture<SendResult<String, Object>> failedFuture = new CompletableFuture<>();
        failedFuture.completeExceptionally(new RuntimeException("Kafka error"));
        doReturn(failedFuture).when(asyncMarketDataProducer).sendTradeRawData(any(TradeDTO.class));

        // 调用被测试方法
        tradeDataProcessor.processTradeData(tradeMessage);

        // 验证尝试发送到Kafka
        verify(asyncMarketDataProducer).sendTradeRawData(eq(tradeDTO));
    }

    @Test
    void testProcessTradeDataWithConversionFailure() throws Exception {
        // 配置mock返回null，模拟转换失败
        doReturn(null).when(marketDataConverter).convertAggTradeMessageToTradeDTO(anyString());

        // 调用被测试方法
        tradeDataProcessor.processTradeData(tradeMessage);

        // 验证不会尝试发送到Kafka
        verify(asyncMarketDataProducer, never()).sendTradeRawData(any(TradeDTO.class));
    }

    @Test
    void testProcessTradeDataWithNullMessage() throws Exception {
        // 调用被测试方法，传入null消息
        tradeDataProcessor.processTradeData(null);

        // 验证不会尝试发送到Kafka
        verify(asyncMarketDataProducer, never()).sendTradeRawData(any(TradeDTO.class));
    }
} 