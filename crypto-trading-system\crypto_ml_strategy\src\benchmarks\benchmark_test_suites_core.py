"""
Crypto ML Strategy - 基准测试套件核心组件

该模块实现了专业的基准测试套件，包括数据处理、ML推理、
Kafka通信等核心业务场景的性能测试。

主要功能：
- DataProcessingBenchmark: 数据处理性能基准测试
- MLInferenceBenchmark: ML模型推理性能基准测试
- KafkaCommunicationBenchmark: Kafka通信性能基准测试

作者: Crypto ML Strategy Team
日期: 2024-01-20
版本: 1.0.0
"""

import asyncio
import time
import random
import numpy as np
from typing import Any, Dict, List, Optional
from .benchmark_core_framework import BenchmarkTest, BenchmarkMetrics, BenchmarkPriority
from performance_metrics_collector import get_global_metrics_collector
from infrastructure.logging.logging_core_manager import get_logger
import pandas as pd


class DataProcessingBenchmark(BenchmarkTest):
    """数据处理性能基准测试"""
    
    def __init__(self, timeframes: List[str] = None, data_sizes: List[int] = None):
        super().__init__(
            name="data_processing_benchmark",
            description="测试多时间框架数据处理性能",
            timeout_seconds=120,
            priority=BenchmarkPriority.HIGH
        )
        
        self.timeframes = timeframes or ["1m", "5m", "15m", "1h", "4h", "1d"]
        self.data_sizes = data_sizes or [1000, 5000, 10000]  # 数据点数量
        self.test_data: Dict[str, pd.DataFrame] = {}
        self.metrics_collector = get_global_metrics_collector()
    
    async def setup(self) -> None:
        """准备测试数据"""
        self.logger.info("Setting up data processing benchmark")
        
        # 生成测试数据
        for timeframe in self.timeframes:
            for size in self.data_sizes:
                key = f"{timeframe}_{size}"
                self.test_data[key] = self._generate_market_data(size)
        
        self.logger.info(f"Generated test data for {len(self.test_data)} scenarios")
    
    def _generate_market_data(self, size: int) -> pd.DataFrame:
        """生成模拟市场数据"""
        timestamps = pd.date_range(start='2024-01-01', periods=size, freq='1min')
        
        # 生成OHLCV数据
        base_price = 50000.0
        data = []
        
        for i in range(size):
            # 模拟价格波动
            change = random.uniform(-0.02, 0.02)
            price = base_price * (1 + change)
            
            open_price = price
            high_price = price * (1 + random.uniform(0, 0.01))
            low_price = price * (1 - random.uniform(0, 0.01))
            close_price = price * (1 + random.uniform(-0.005, 0.005))
            volume = random.uniform(100, 1000)
            
            data.append({
                'timestamp': timestamps[i],
                'open': open_price,
                'high': high_price,
                'low': low_price,
                'close': close_price,
                'volume': volume
            })
            
            base_price = close_price
        
        return pd.DataFrame(data)
    
    async def run_test(self) -> BenchmarkMetrics:
        """执行数据处理性能测试"""
        self.logger.info("Running data processing benchmark")
        
        total_operations = 0
        total_time = 0
        error_count = 0
        
        start_time = time.perf_counter()
        
        for key, data in self.test_data.items():
            try:
                # 测试数据处理操作
                operation_start = time.perf_counter()
                
                # 模拟数据处理操作
                await self._process_data(data)
                
                operation_time = time.perf_counter() - operation_start
                total_time += operation_time
                total_operations += 1
                
                # 记录操作指标
                self.metrics_collector.record_timing(f"data_processing_{key}", operation_time * 1000)
                
            except Exception as e:
                error_count += 1
                self.logger.error(f"Error processing data {key}: {e}")
        
        end_time = time.perf_counter()
        total_duration = end_time - start_time
        
        # 计算性能指标
        throughput = total_operations / total_duration if total_duration > 0 else 0
        avg_latency = (total_time / total_operations * 1000) if total_operations > 0 else 0
        error_rate = (error_count / len(self.test_data)) if self.test_data else 0
        
        return BenchmarkMetrics(
            execution_time_ms=total_duration * 1000,
            memory_usage_mb=self._get_memory_usage(),
            cpu_usage_percent=self._get_cpu_usage(),
            throughput_ops_per_sec=throughput,
            latency_p50_ms=avg_latency,
            latency_p95_ms=avg_latency * 1.2,
            latency_p99_ms=avg_latency * 1.5,
            error_rate=error_rate,
            success_rate=1.0 - error_rate,
            concurrent_connections=1,
            custom_metrics={
                "total_operations": total_operations,
                "total_data_points": sum(len(data) for data in self.test_data.values()),
                "timeframes_tested": len(self.timeframes),
                "data_sizes_tested": len(self.data_sizes)
            }
        )
    
    async def _process_data(self, data: pd.DataFrame) -> None:
        """模拟数据处理操作"""
        # 模拟数据清洗
        cleaned_data = data.dropna()
        
        # 模拟技术指标计算
        cleaned_data['sma_20'] = cleaned_data['close'].rolling(window=20).mean()
        cleaned_data['ema_12'] = cleaned_data['close'].ewm(span=12).mean()
        cleaned_data['rsi'] = self._calculate_rsi(cleaned_data['close'])
        
        # 模拟数据验证
        await self._validate_data(cleaned_data)
        
        # 模拟异步I/O操作
        await asyncio.sleep(0.001)  # 1ms模拟I/O延迟
    
    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """计算RSI指标"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    async def _validate_data(self, data: pd.DataFrame) -> None:
        """验证数据质量"""
        # 检查数据完整性
        if data.isnull().any().any():
            raise ValueError("Data contains null values")
        
        # 检查价格逻辑
        if (data['high'] < data['low']).any():
            raise ValueError("High price is less than low price")
        
        if (data['high'] < data['close']).any() or (data['low'] > data['close']).any():
            raise ValueError("Close price is outside high-low range")
    
    def _get_memory_usage(self) -> float:
        """获取内存使用量"""
        try:
            import psutil
            process = psutil.Process()
            return process.memory_info().rss / 1024 / 1024
        except Exception:
            return 0.0
    
    def _get_cpu_usage(self) -> float:
        """获取CPU使用率"""
        try:
            import psutil
            return psutil.cpu_percent()
        except Exception:
            return 0.0
    
    async def teardown(self) -> None:
        """清理测试数据"""
        self.test_data.clear()
        self.logger.info("Data processing benchmark teardown completed")


class MLInferenceBenchmark(BenchmarkTest):
    """ML模型推理性能基准测试"""
    
    def __init__(self, model_types: List[str] = None, batch_sizes: List[int] = None):
        super().__init__(
            name="ml_inference_benchmark",
            description="测试ML模型推理性能",
            timeout_seconds=180,
            priority=BenchmarkPriority.CRITICAL
        )
        
        self.model_types = model_types or ["deepseek", "lppl", "hematread", "bmsb", "supertrend"]
        self.batch_sizes = batch_sizes or [1, 10, 50, 100]
        self.test_inputs: Dict[str, np.ndarray] = {}
        self.mock_models: Dict[str, Any] = {}
        self.metrics_collector = get_global_metrics_collector()
    
    async def setup(self) -> None:
        """准备测试模型和数据"""
        self.logger.info("Setting up ML inference benchmark")
        
        # 生成测试输入数据
        for batch_size in self.batch_sizes:
            # 模拟特征向量 (batch_size, feature_dim)
            feature_dim = 100
            self.test_inputs[f"batch_{batch_size}"] = np.random.randn(batch_size, feature_dim).astype(np.float32)
        
        # 创建模拟模型
        for model_type in self.model_types:
            self.mock_models[model_type] = self._create_mock_model(model_type)
        
        self.logger.info(f"Prepared {len(self.mock_models)} models and {len(self.test_inputs)} input batches")
    
    def _create_mock_model(self, model_type: str) -> Dict[str, Any]:
        """创建模拟模型"""
        # 模拟不同模型的计算复杂度
        complexity_map = {
            "deepseek": {"layers": 12, "params": 1000000, "compute_factor": 1.5},
            "lppl": {"layers": 3, "params": 50000, "compute_factor": 0.3},
            "hematread": {"layers": 5, "params": 100000, "compute_factor": 0.5},
            "bmsb": {"layers": 4, "params": 75000, "compute_factor": 0.4},
            "supertrend": {"layers": 2, "params": 25000, "compute_factor": 0.2}
        }
        
        return complexity_map.get(model_type, {"layers": 3, "params": 50000, "compute_factor": 0.3})
    
    async def run_test(self) -> BenchmarkMetrics:
        """执行ML推理性能测试"""
        self.logger.info("Running ML inference benchmark")
        
        total_inferences = 0
        total_time = 0
        error_count = 0
        latencies = []
        
        start_time = time.perf_counter()
        
        for model_type, model in self.mock_models.items():
            for batch_key, inputs in self.test_inputs.items():
                try:
                    # 测试模型推理
                    inference_start = time.perf_counter()
                    
                    # 模拟推理过程
                    await self._run_inference(model, inputs)
                    
                    inference_time = time.perf_counter() - inference_start
                    latencies.append(inference_time * 1000)  # 转换为毫秒
                    total_time += inference_time
                    total_inferences += 1
                    
                    # 记录推理指标
                    self.metrics_collector.record_timing(
                        f"ml_inference_{model_type}_{batch_key}", 
                        inference_time * 1000
                    )
                    
                except Exception as e:
                    error_count += 1
                    self.logger.error(f"Error in inference {model_type}_{batch_key}: {e}")
        
        end_time = time.perf_counter()
        total_duration = end_time - start_time
        
        # 计算性能指标
        throughput = total_inferences / total_duration if total_duration > 0 else 0
        error_rate = (error_count / (len(self.mock_models) * len(self.test_inputs))) if self.mock_models and self.test_inputs else 0
        
        # 计算延迟百分位数
        if latencies:
            latencies.sort()
            p50 = latencies[len(latencies) // 2]
            p95 = latencies[int(len(latencies) * 0.95)]
            p99 = latencies[int(len(latencies) * 0.99)]
        else:
            p50 = p95 = p99 = 0
        
        return BenchmarkMetrics(
            execution_time_ms=total_duration * 1000,
            memory_usage_mb=self._get_memory_usage(),
            cpu_usage_percent=self._get_cpu_usage(),
            throughput_ops_per_sec=throughput,
            latency_p50_ms=p50,
            latency_p95_ms=p95,
            latency_p99_ms=p99,
            error_rate=error_rate,
            success_rate=1.0 - error_rate,
            concurrent_connections=1,
            custom_metrics={
                "total_inferences": total_inferences,
                "models_tested": len(self.model_types),
                "batch_sizes_tested": len(self.batch_sizes),
                "avg_latency_ms": sum(latencies) / len(latencies) if latencies else 0
            }
        )
    
    async def _run_inference(self, model: Dict[str, Any], inputs: np.ndarray) -> np.ndarray:
        """模拟模型推理"""
        batch_size = inputs.shape[0]
        feature_dim = inputs.shape[1]
        
        # 模拟计算延迟（基于模型复杂度）
        compute_factor = model["compute_factor"]
        base_delay = 0.001  # 1ms基础延迟
        complexity_delay = compute_factor * batch_size * 0.0001  # 基于批次大小的延迟
        
        await asyncio.sleep(base_delay + complexity_delay)
        
        # 模拟矩阵运算
        weights = np.random.randn(feature_dim, 64).astype(np.float32)
        hidden = np.dot(inputs, weights)
        
        # 模拟激活函数
        activated = np.maximum(0, hidden)  # ReLU
        
        # 模拟输出层
        output_weights = np.random.randn(64, 1).astype(np.float32)
        outputs = np.dot(activated, output_weights)
        
        return outputs
    
    def _get_memory_usage(self) -> float:
        """获取内存使用量"""
        try:
            import psutil
            process = psutil.Process()
            return process.memory_info().rss / 1024 / 1024
        except Exception:
            return 0.0
    
    def _get_cpu_usage(self) -> float:
        """获取CPU使用率"""
        try:
            import psutil
            return psutil.cpu_percent()
        except Exception:
            return 0.0
    
    async def teardown(self) -> None:
        """清理测试资源"""
        self.test_inputs.clear()
        self.mock_models.clear()
        self.logger.info("ML inference benchmark teardown completed")


class KafkaCommunicationBenchmark(BenchmarkTest):
    """Kafka通信性能基准测试"""
    
    def __init__(self, message_sizes: List[int] = None, thread_counts: List[int] = None):
        super().__init__(
            name="kafka_communication_benchmark",
            description="测试Kafka通信性能",
            timeout_seconds=90,
            priority=BenchmarkPriority.HIGH
        )
        
        self.message_sizes = message_sizes or [1024, 10240, 102400]  # 1KB, 10KB, 100KB
        self.thread_counts = thread_counts or [1, 5, 10]
        self.test_messages: Dict[str, bytes] = {}
        self.metrics_collector = get_global_metrics_collector()
    
    async def setup(self) -> None:
        """准备测试消息"""
        self.logger.info("Setting up Kafka communication benchmark")
        
        # 生成不同大小的测试消息
        for size in self.message_sizes:
            message_data = {
                "timestamp": time.time(),
                "symbol": "BTCUSDT",
                "price": 50000.0,
                "volume": 1.5,
                "data": "x" * (size - 200)  # 填充数据到指定大小
            }
            
            import json
            message_json = json.dumps(message_data)
            self.test_messages[f"size_{size}"] = message_json.encode('utf-8')
        
        self.logger.info(f"Generated {len(self.test_messages)} test messages")
    
    async def run_test(self) -> BenchmarkMetrics:
        """执行Kafka通信性能测试"""
        self.logger.info("Running Kafka communication benchmark")
        
        total_messages = 0
        total_time = 0
        error_count = 0
        latencies = []
        
        start_time = time.perf_counter()
        
        for thread_count in self.thread_counts:
            for message_key, message_data in self.test_messages.items():
                try:
                    # 测试消息发送和接收
                    comm_start = time.perf_counter()
                    
                    # 模拟Kafka通信
                    await self._simulate_kafka_communication(message_data, thread_count)
                    
                    comm_time = time.perf_counter() - comm_start
                    latencies.append(comm_time * 1000)  # 转换为毫秒
                    total_time += comm_time
                    total_messages += 1
                    
                    # 记录通信指标
                    self.metrics_collector.record_timing(
                        f"kafka_comm_{message_key}_threads_{thread_count}", 
                        comm_time * 1000
                    )
                    
                except Exception as e:
                    error_count += 1
                    self.logger.error(f"Error in Kafka communication {message_key}_threads_{thread_count}: {e}")
        
        end_time = time.perf_counter()
        total_duration = end_time - start_time
        
        # 计算性能指标
        throughput = total_messages / total_duration if total_duration > 0 else 0
        error_rate = (error_count / (len(self.thread_counts) * len(self.test_messages))) if self.thread_counts and self.test_messages else 0
        
        # 计算延迟百分位数
        if latencies:
            latencies.sort()
            p50 = latencies[len(latencies) // 2]
            p95 = latencies[int(len(latencies) * 0.95)]
            p99 = latencies[int(len(latencies) * 0.99)]
        else:
            p50 = p95 = p99 = 0
        
        return BenchmarkMetrics(
            execution_time_ms=total_duration * 1000,
            memory_usage_mb=self._get_memory_usage(),
            cpu_usage_percent=self._get_cpu_usage(),
            throughput_ops_per_sec=throughput,
            latency_p50_ms=p50,
            latency_p95_ms=p95,
            latency_p99_ms=p99,
            error_rate=error_rate,
            success_rate=1.0 - error_rate,
            concurrent_connections=max(self.thread_counts),
            custom_metrics={
                "total_messages": total_messages,
                "message_sizes_tested": len(self.message_sizes),
                "thread_counts_tested": len(self.thread_counts),
                "total_bytes_transferred": sum(len(msg) for msg in self.test_messages.values()) * len(self.thread_counts)
            }
        )
    
    async def _simulate_kafka_communication(self, message_data: bytes, thread_count: int) -> None:
        """模拟Kafka通信"""
        # 模拟网络延迟
        base_latency = 0.005  # 5ms基础网络延迟
        message_size_factor = len(message_data) / 1024 * 0.001  # 基于消息大小的延迟
        thread_overhead = thread_count * 0.001  # 线程开销
        
        total_delay = base_latency + message_size_factor + thread_overhead
        await asyncio.sleep(total_delay)
        
        # 模拟序列化/反序列化
        import json
        try:
            # 模拟反序列化
            decoded_message = message_data.decode('utf-8')
            parsed_data = json.loads(decoded_message)
            
            # 模拟处理
            processed_data = {
                **parsed_data,
                "processed_timestamp": time.time(),
                "thread_count": thread_count
            }
            
            # 模拟序列化
            response_message = json.dumps(processed_data).encode('utf-8')
            
        except Exception as e:
            raise RuntimeError(f"Message processing failed: {e}")
    
    def _get_memory_usage(self) -> float:
        """获取内存使用量"""
        try:
            import psutil
            process = psutil.Process()
            return process.memory_info().rss / 1024 / 1024
        except Exception:
            return 0.0
    
    def _get_cpu_usage(self) -> float:
        """获取CPU使用率"""
        try:
            import psutil
            return psutil.cpu_percent()
        except Exception:
            return 0.0
    
    async def teardown(self) -> None:
        """清理测试资源"""
        self.test_messages.clear()
        self.logger.info("Kafka communication benchmark teardown completed")


# 模块级别的日志器
module_logger = get_logger(__name__)