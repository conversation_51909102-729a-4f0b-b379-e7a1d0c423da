#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
集成测试框架

crypto_ml_strategy模块的集成测试框架，提供完整的端到端测试能力。
包含配置管理、数据生成、测试执行、报告生成和Mock服务等功能。
"""

from .config.integration_test_config import (
    IntegrationTestConfig,
    get_test_config,
    reset_test_config
)

from .data.test_data_generator import (
    TestDataGenerator,
    create_test_data_generator
)

from .utils.test_orchestrator import (
    TestOrchestrator,
    TestCase,
    TestSuite,
    TestStatus,
    TestPriority,
    TestResult,
    create_test_orchestrator
)

from .reports.test_reporter import (
    TestReporter,
    ReportConfig,
    create_test_reporter
)

from .fixtures.mock_services import (
    MockServiceManager,
    MockJavaAPI,
    MockKafkaService,
    TestFixtures,
    get_mock_manager,
    setup_mock_environment,
    teardown_mock_environment,
    with_mock_environment
)

from .utils.data_flow_test_helpers import (
    DataFlowMetrics,
    TimeframeData,
    DataFlowValidator,
    DataFlowPerformanceMonitor,
    TimeframeDataGenerator,
    DataFlowTestScenario,
    create_standard_data_flow_scenarios
)

from .suites.data_flow_integration_test_suite import (
    create_data_flow_integration_test_suite
)

from .suites.ml_pipeline_integration_test_suite import (
    create_ml_pipeline_integration_test_suite
)

from .suites.risk_management_integration_test_suite import (
    RiskManagementIntegrationTestSuite
)

from .suites.test_risk_control_system import (
    RiskControlSystemTest
)

from .suites.test_position_management import (
    PositionManagementTest
)

from .suites.test_risk_monitoring import (
    RiskMonitoringTest
)

from .utils.risk_management_test_helpers import (
    RiskScenarioGenerator,
    RiskPerformanceMonitor,
    RiskTestExecutor
)

from .run_risk_management_tests import (
    RiskManagementTestRunner
)

__version__ = "1.0.0"
__author__ = "Crypto ML Strategy Team"

# 导出主要类和函数
__all__ = [
    # 配置管理
    "IntegrationTestConfig",
    "get_test_config",
    "reset_test_config",
    
    # 数据生成
    "TestDataGenerator",
    "create_test_data_generator",
    
    # 测试执行
    "TestOrchestrator",
    "TestCase",
    "TestSuite",
    "TestStatus",
    "TestPriority",
    "TestResult",
    "create_test_orchestrator",
    
    # 报告生成
    "TestReporter",
    "ReportConfig",
    "create_test_reporter",
    
    # Mock服务
    "MockServiceManager",
    "MockJavaAPI",
    "MockKafkaService",
    "TestFixtures",
    "get_mock_manager",
    "setup_mock_environment",
    "teardown_mock_environment",
    "with_mock_environment",
    
    # 数据流测试工具
    "DataFlowMetrics",
    "TimeframeData",
    "DataFlowValidator",
    "DataFlowPerformanceMonitor",
    "TimeframeDataGenerator",
    "DataFlowTestScenario",
    "create_standard_data_flow_scenarios",
    
    # 测试套件
    "create_data_flow_integration_test_suite",
    "create_ml_pipeline_integration_test_suite",

    # 风险管理测试
    "RiskManagementIntegrationTestSuite",
    "RiskControlSystemTest",
    "PositionManagementTest",
    "RiskMonitoringTest",
    "RiskScenarioGenerator",
    "RiskPerformanceMonitor",
    "RiskTestExecutor",
    "RiskManagementTestRunner"
]