package com.crypto.trading.common.dto.market;

import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

/**
 * TradeDTO单元测试类
 */
class TradeDTOTest {

    /**
     * 测试默认构造函数
     */
    @Test
    void testDefaultConstructor() {
        TradeDTO dto = new TradeDTO();
        assertNotNull(dto);
    }

    /**
     * 测试带参数构造函数
     */
    @Test
    void testParameterizedConstructor() {
        Long id = 123456789L;
        String symbol = "BTCUSDT";
        BigDecimal price = new BigDecimal("50000.00");
        BigDecimal quantity = new BigDecimal("1.5");
        BigDecimal quoteQuantity = new BigDecimal("75000.00");
        LocalDateTime time = LocalDateTime.now();
        boolean isBuyerMaker = true;
        boolean isBestMatch = true;

        TradeDTO dto = new TradeDTO(
                id, symbol, price, quantity, quoteQuantity, time, isBuyerMaker, isBestMatch
        );

        assertNotNull(dto);
        assertEquals(id, dto.getId());
        assertEquals(symbol, dto.getSymbol());
        assertEquals(price, dto.getPrice());
        assertEquals(quantity, dto.getQuantity());
        assertEquals(quoteQuantity, dto.getQuoteQuantity());
        assertEquals(time, dto.getTime());
        assertEquals(isBuyerMaker, dto.isBuyerMaker());
        assertEquals(isBestMatch, dto.isBestMatch());
    }

    /**
     * 测试getter和setter方法
     */
    @Test
    void testGettersAndSetters() {
        TradeDTO dto = new TradeDTO();

        Long id = 987654321L;
        dto.setId(id);
        assertEquals(id, dto.getId());

        String symbol = "ETHUSDT";
        dto.setSymbol(symbol);
        assertEquals(symbol, dto.getSymbol());

        BigDecimal price = new BigDecimal("3000.00");
        dto.setPrice(price);
        assertEquals(price, dto.getPrice());

        BigDecimal quantity = new BigDecimal("2.5");
        dto.setQuantity(quantity);
        assertEquals(quantity, dto.getQuantity());

        BigDecimal quoteQuantity = new BigDecimal("7500.00");
        dto.setQuoteQuantity(quoteQuantity);
        assertEquals(quoteQuantity, dto.getQuoteQuantity());

        LocalDateTime time = LocalDateTime.now();
        dto.setTime(time);
        assertEquals(time, dto.getTime());

        boolean isBuyerMaker = false;
        dto.setBuyerMaker(isBuyerMaker);
        assertEquals(isBuyerMaker, dto.isBuyerMaker());

        boolean isBestMatch = false;
        dto.setBestMatch(isBestMatch);
        assertEquals(isBestMatch, dto.isBestMatch());
    }

    /**
     * 测试toString方法
     */
    @Test
    void testToString() {
        TradeDTO dto = new TradeDTO();
        dto.setId(123456789L);
        dto.setSymbol("BTCUSDT");
        dto.setPrice(new BigDecimal("50000.00"));
        dto.setQuantity(new BigDecimal("1.5"));

        String toString = dto.toString();
        assertNotNull(toString);
        assertTrue(toString.contains("123456789"));
        assertTrue(toString.contains("BTCUSDT"));
        assertTrue(toString.contains("50000.00"));
        assertTrue(toString.contains("1.5"));
    }
} 