"""
启动优化器模块

提供crypto_ml_strategy项目的启动优化功能，包括并行初始化、
资源调度、性能调优和启动策略优化。

Author: Crypto ML Strategy Team
Date: 2024-12-20
Version: 1.0.0
"""

import asyncio
import time
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Dict, List, Optional, Set, Callable, Tuple
from loguru import logger

from .startup_sequence_core import StartupTask, StartupPhase, StartupTaskPriority
from .error_handler_core import (
    CryptoMLException, ErrorCode, ErrorSeverity, ErrorCategory,
    ErrorContext, handle_exception
)


class OptimizationStrategy(Enum):
    """优化策略枚举"""
    PARALLEL_FIRST = "parallel_first"      # 优先并行执行
    PRIORITY_FIRST = "priority_first"      # 优先级优先
    RESOURCE_AWARE = "resource_aware"      # 资源感知
    ADAPTIVE = "adaptive"                  # 自适应
    CONSERVATIVE = "conservative"          # 保守策略


@dataclass
class OptimizationConfig:
    """优化配置"""
    strategy: OptimizationStrategy = OptimizationStrategy.ADAPTIVE
    max_parallel_tasks: int = 5
    max_memory_usage_mb: int = 2048
    max_cpu_usage_percent: float = 80.0
    enable_resource_monitoring: bool = True
    enable_adaptive_scheduling: bool = True
    task_timeout_multiplier: float = 1.5
    retry_failed_tasks: bool = True
    preload_critical_resources: bool = True


@dataclass
class ResourceUsage:
    """资源使用情况"""
    memory_mb: float = 0.0
    cpu_percent: float = 0.0
    disk_io_mb_per_sec: float = 0.0
    network_io_mb_per_sec: float = 0.0
    active_tasks: int = 0
    timestamp: datetime = field(default_factory=datetime.now)
    
    def is_resource_available(self, config: OptimizationConfig) -> bool:
        """检查资源是否可用"""
        return (
            self.memory_mb < config.max_memory_usage_mb and
            self.cpu_percent < config.max_cpu_usage_percent and
            self.active_tasks < config.max_parallel_tasks
        )


class ResourceMonitor:
    """资源监控器"""
    
    def __init__(self, monitoring_interval: float = 1.0):
        self.monitoring_interval = monitoring_interval
        self.current_usage = ResourceUsage()
        self.usage_history: List[ResourceUsage] = []
        self.max_history = 100
        self.monitoring = False
        self.monitor_task: Optional[asyncio.Task] = None
    
    async def start_monitoring(self) -> None:
        """开始资源监控"""
        if self.monitoring:
            return
        
        self.monitoring = True
        self.monitor_task = asyncio.create_task(self._monitor_loop())
        logger.debug("资源监控已启动")
    
    async def stop_monitoring(self) -> None:
        """停止资源监控"""
        self.monitoring = False
        if self.monitor_task:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass
        logger.debug("资源监控已停止")
    
    async def _monitor_loop(self) -> None:
        """监控循环"""
        while self.monitoring:
            try:
                await self._update_resource_usage()
                await asyncio.sleep(self.monitoring_interval)
            except Exception as e:
                logger.error(f"资源监控异常: {e}")
                await asyncio.sleep(self.monitoring_interval)
    
    async def _update_resource_usage(self) -> None:
        """更新资源使用情况"""
        try:
            import psutil
            
            # 获取内存使用
            memory = psutil.virtual_memory()
            memory_used_mb = (memory.total - memory.available) / (1024 * 1024)
            
            # 获取CPU使用率
            cpu_percent = psutil.cpu_percent(interval=None)
            
            # 获取磁盘IO
            disk_io = psutil.disk_io_counters()
            disk_io_mb_per_sec = 0.0
            if hasattr(self, '_last_disk_io') and hasattr(self, '_last_disk_time'):
                time_diff = time.time() - self._last_disk_time
                if time_diff > 0:
                    bytes_diff = (disk_io.read_bytes + disk_io.write_bytes) - self._last_disk_io
                    disk_io_mb_per_sec = (bytes_diff / time_diff) / (1024 * 1024)
            
            self._last_disk_io = disk_io.read_bytes + disk_io.write_bytes
            self._last_disk_time = time.time()
            
            # 获取网络IO
            network_io = psutil.net_io_counters()
            network_io_mb_per_sec = 0.0
            if hasattr(self, '_last_network_io') and hasattr(self, '_last_network_time'):
                time_diff = time.time() - self._last_network_time
                if time_diff > 0:
                    bytes_diff = (network_io.bytes_sent + network_io.bytes_recv) - self._last_network_io
                    network_io_mb_per_sec = (bytes_diff / time_diff) / (1024 * 1024)
            
            self._last_network_io = network_io.bytes_sent + network_io.bytes_recv
            self._last_network_time = time.time()
            
            # 更新当前使用情况
            self.current_usage = ResourceUsage(
                memory_mb=memory_used_mb,
                cpu_percent=cpu_percent,
                disk_io_mb_per_sec=disk_io_mb_per_sec,
                network_io_mb_per_sec=network_io_mb_per_sec,
                active_tasks=self.current_usage.active_tasks  # 由外部更新
            )
            
            # 保存历史记录
            self.usage_history.append(self.current_usage)
            if len(self.usage_history) > self.max_history:
                self.usage_history.pop(0)
                
        except Exception as e:
            logger.debug(f"更新资源使用情况失败: {e}")
    
    def get_current_usage(self) -> ResourceUsage:
        """获取当前资源使用情况"""
        return self.current_usage
    
    def get_average_usage(self, window_size: int = 10) -> ResourceUsage:
        """获取平均资源使用情况"""
        if not self.usage_history:
            return self.current_usage
        
        recent_usage = self.usage_history[-window_size:]
        
        avg_memory = sum(u.memory_mb for u in recent_usage) / len(recent_usage)
        avg_cpu = sum(u.cpu_percent for u in recent_usage) / len(recent_usage)
        avg_disk_io = sum(u.disk_io_mb_per_sec for u in recent_usage) / len(recent_usage)
        avg_network_io = sum(u.network_io_mb_per_sec for u in recent_usage) / len(recent_usage)
        
        return ResourceUsage(
            memory_mb=avg_memory,
            cpu_percent=avg_cpu,
            disk_io_mb_per_sec=avg_disk_io,
            network_io_mb_per_sec=avg_network_io,
            active_tasks=self.current_usage.active_tasks
        )
    
    def update_active_tasks(self, count: int) -> None:
        """更新活跃任务数"""
        self.current_usage.active_tasks = count


class TaskScheduler:
    """任务调度器"""
    
    def __init__(self, config: OptimizationConfig):
        self.config = config
        self.resource_monitor = ResourceMonitor()
        self.scheduled_tasks: List[StartupTask] = []
        self.running_tasks: Dict[str, asyncio.Task] = {}
        self.completed_tasks: Set[str] = set()
        self.failed_tasks: Set[str] = set()
    
    async def start(self) -> None:
        """启动调度器"""
        if self.config.enable_resource_monitoring:
            await self.resource_monitor.start_monitoring()
    
    async def stop(self) -> None:
        """停止调度器"""
        await self.resource_monitor.stop_monitoring()
        
        # 取消所有运行中的任务
        for task in self.running_tasks.values():
            task.cancel()
        
        if self.running_tasks:
            await asyncio.gather(*self.running_tasks.values(), return_exceptions=True)
    
    def add_tasks(self, tasks: List[StartupTask]) -> None:
        """添加任务到调度队列"""
        self.scheduled_tasks.extend(tasks)
        # 按优先级和依赖关系排序
        self._sort_tasks()
    
    def _sort_tasks(self) -> None:
        """排序任务"""
        if self.config.strategy == OptimizationStrategy.PRIORITY_FIRST:
            self.scheduled_tasks.sort(key=lambda t: (t.priority.value, t.name))
        elif self.config.strategy == OptimizationStrategy.PARALLEL_FIRST:
            # 优先选择可以并行的任务
            self.scheduled_tasks.sort(key=lambda t: (len(t.dependencies), t.priority.value))
        else:
            # 默认按优先级排序
            self.scheduled_tasks.sort(key=lambda t: t.priority.value)
    
    async def schedule_next_tasks(self) -> List[StartupTask]:
        """调度下一批任务"""
        if not self.scheduled_tasks:
            return []
        
        # 获取当前资源使用情况
        current_usage = self.resource_monitor.get_current_usage()
        
        # 找到可以执行的任务
        ready_tasks = []
        for task in self.scheduled_tasks[:]:
            # 检查依赖是否满足
            if not task.is_ready_to_run(self.completed_tasks):
                continue
            
            # 检查资源是否可用
            if not self._can_schedule_task(task, current_usage):
                break
            
            ready_tasks.append(task)
            self.scheduled_tasks.remove(task)
            
            # 检查是否达到并行限制
            if len(ready_tasks) >= self._get_max_parallel_tasks(current_usage):
                break
        
        return ready_tasks
    
    def _can_schedule_task(self, task: StartupTask, current_usage: ResourceUsage) -> bool:
        """检查是否可以调度任务"""
        if self.config.strategy == OptimizationStrategy.CONSERVATIVE:
            # 保守策略：只有在资源充足时才调度
            return (
                current_usage.memory_mb < self.config.max_memory_usage_mb * 0.7 and
                current_usage.cpu_percent < self.config.max_cpu_usage_percent * 0.7 and
                current_usage.active_tasks < self.config.max_parallel_tasks * 0.7
            )
        elif self.config.strategy == OptimizationStrategy.RESOURCE_AWARE:
            # 资源感知策略：根据任务类型和当前资源使用情况决定
            return self._is_resource_aware_schedulable(task, current_usage)
        else:
            # 其他策略：基本资源检查
            return current_usage.is_resource_available(self.config)
    
    def _is_resource_aware_schedulable(self, task: StartupTask, current_usage: ResourceUsage) -> bool:
        """资源感知调度检查"""
        # 根据任务元数据估算资源需求
        estimated_memory = task.metadata.get('estimated_memory_mb', 100)
        estimated_cpu = task.metadata.get('estimated_cpu_percent', 10)
        
        # 检查是否有足够资源
        memory_available = self.config.max_memory_usage_mb - current_usage.memory_mb
        cpu_available = self.config.max_cpu_usage_percent - current_usage.cpu_percent
        
        return (
            memory_available >= estimated_memory and
            cpu_available >= estimated_cpu and
            current_usage.active_tasks < self.config.max_parallel_tasks
        )
    
    def _get_max_parallel_tasks(self, current_usage: ResourceUsage) -> int:
        """获取最大并行任务数"""
        if self.config.strategy == OptimizationStrategy.ADAPTIVE:
            # 自适应策略：根据当前资源使用情况调整
            if current_usage.cpu_percent > 70:
                return max(1, self.config.max_parallel_tasks // 2)
            elif current_usage.memory_mb > self.config.max_memory_usage_mb * 0.8:
                return max(1, self.config.max_parallel_tasks // 2)
            else:
                return self.config.max_parallel_tasks
        else:
            return self.config.max_parallel_tasks
    
    def mark_task_completed(self, task_name: str, success: bool) -> None:
        """标记任务完成"""
        if success:
            self.completed_tasks.add(task_name)
        else:
            self.failed_tasks.add(task_name)
        
        # 更新活跃任务数
        if task_name in self.running_tasks:
            del self.running_tasks[task_name]
        
        self.resource_monitor.update_active_tasks(len(self.running_tasks))
    
    def add_running_task(self, task_name: str, task_handle: asyncio.Task) -> None:
        """添加运行中的任务"""
        self.running_tasks[task_name] = task_handle
        self.resource_monitor.update_active_tasks(len(self.running_tasks))
    
    def get_scheduling_stats(self) -> Dict[str, Any]:
        """获取调度统计"""
        return {
            "scheduled_tasks": len(self.scheduled_tasks),
            "running_tasks": len(self.running_tasks),
            "completed_tasks": len(self.completed_tasks),
            "failed_tasks": len(self.failed_tasks),
            "current_resource_usage": self.resource_monitor.get_current_usage().memory_mb,
            "strategy": self.config.strategy.value
        }


class PerformanceTuner:
    """性能调优器"""
    
    def __init__(self):
        self.performance_history: List[Dict[str, Any]] = []
        self.optimization_suggestions: List[str] = []
    
    def record_performance(
        self,
        phase: StartupPhase,
        duration: float,
        task_count: int,
        resource_usage: ResourceUsage
    ) -> None:
        """记录性能数据"""
        performance_record = {
            "phase": phase.value,
            "duration": duration,
            "task_count": task_count,
            "memory_usage": resource_usage.memory_mb,
            "cpu_usage": resource_usage.cpu_percent,
            "timestamp": datetime.now().isoformat()
        }
        
        self.performance_history.append(performance_record)
    
    def analyze_performance(self) -> Dict[str, Any]:
        """分析性能"""
        if not self.performance_history:
            return {"status": "no_data"}
        
        # 计算各阶段平均性能
        phase_stats = {}
        for record in self.performance_history:
            phase = record["phase"]
            if phase not in phase_stats:
                phase_stats[phase] = {
                    "durations": [],
                    "memory_usage": [],
                    "cpu_usage": []
                }
            
            phase_stats[phase]["durations"].append(record["duration"])
            phase_stats[phase]["memory_usage"].append(record["memory_usage"])
            phase_stats[phase]["cpu_usage"].append(record["cpu_usage"])
        
        # 生成统计信息
        analysis = {}
        for phase, stats in phase_stats.items():
            analysis[phase] = {
                "avg_duration": sum(stats["durations"]) / len(stats["durations"]),
                "max_duration": max(stats["durations"]),
                "min_duration": min(stats["durations"]),
                "avg_memory": sum(stats["memory_usage"]) / len(stats["memory_usage"]),
                "avg_cpu": sum(stats["cpu_usage"]) / len(stats["cpu_usage"])
            }
        
        return analysis
    
    def generate_optimization_suggestions(self, analysis: Dict[str, Any]) -> List[str]:
        """生成优化建议"""
        suggestions = []
        
        for phase, stats in analysis.items():
            if phase == "no_data":
                continue
                
            # 基于持续时间的建议
            if stats["avg_duration"] > 3.0:
                suggestions.append(f"阶段 {phase} 平均耗时过长 ({stats['avg_duration']:.2f}s)，考虑优化")
            
            # 基于资源使用的建议
            if stats["avg_memory"] > 1024:
                suggestions.append(f"阶段 {phase} 内存使用过高 ({stats['avg_memory']:.0f}MB)，考虑优化内存使用")
            
            if stats["avg_cpu"] > 80:
                suggestions.append(f"阶段 {phase} CPU使用率过高 ({stats['avg_cpu']:.1f}%)，考虑减少并行度")
        
        self.optimization_suggestions = suggestions
        return suggestions


class StartupOptimizer:
    """启动优化器主类"""
    
    def __init__(self, config: Optional[OptimizationConfig] = None):
        self.config = config or OptimizationConfig()
        self.scheduler = TaskScheduler(self.config)
        self.tuner = PerformanceTuner()
        self.optimization_enabled = True
    
    async def start(self) -> None:
        """启动优化器"""
        await self.scheduler.start()
        logger.info(f"启动优化器已启动 (策略: {self.config.strategy.value})")
    
    async def stop(self) -> None:
        """停止优化器"""
        await self.scheduler.stop()
        logger.info("启动优化器已停止")
    
    def optimize_task_execution_order(self, tasks: List[StartupTask]) -> List[StartupTask]:
        """优化任务执行顺序"""
        if not self.optimization_enabled:
            return tasks
        
        # 根据优化策略重新排序任务
        if self.config.strategy == OptimizationStrategy.PARALLEL_FIRST:
            return self._optimize_for_parallelism(tasks)
        elif self.config.strategy == OptimizationStrategy.PRIORITY_FIRST:
            return self._optimize_for_priority(tasks)
        elif self.config.strategy == OptimizationStrategy.RESOURCE_AWARE:
            return self._optimize_for_resources(tasks)
        else:
            return tasks
    
    def _optimize_for_parallelism(self, tasks: List[StartupTask]) -> List[StartupTask]:
        """为并行性优化任务顺序"""
        # 将任务分组：可并行的任务放在前面
        parallel_tasks = []
        sequential_tasks = []
        
        for task in tasks:
            if task.parallel_group or len(task.dependencies) == 0:
                parallel_tasks.append(task)
            else:
                sequential_tasks.append(task)
        
        # 按优先级排序每个组
        parallel_tasks.sort(key=lambda t: t.priority.value)
        sequential_tasks.sort(key=lambda t: t.priority.value)
        
        return parallel_tasks + sequential_tasks
    
    def _optimize_for_priority(self, tasks: List[StartupTask]) -> List[StartupTask]:
        """为优先级优化任务顺序"""
        return sorted(tasks, key=lambda t: (t.priority.value, len(t.dependencies)))
    
    def _optimize_for_resources(self, tasks: List[StartupTask]) -> List[StartupTask]:
        """为资源使用优化任务顺序"""
        # 将资源密集型任务分散开
        memory_intensive = []
        cpu_intensive = []
        io_intensive = []
        normal_tasks = []
        
        for task in tasks:
            estimated_memory = task.metadata.get('estimated_memory_mb', 0)
            estimated_cpu = task.metadata.get('estimated_cpu_percent', 0)
            is_io_intensive = task.metadata.get('io_intensive', False)
            
            if estimated_memory > 500:
                memory_intensive.append(task)
            elif estimated_cpu > 50:
                cpu_intensive.append(task)
            elif is_io_intensive:
                io_intensive.append(task)
            else:
                normal_tasks.append(task)
        
        # 交错排列不同类型的任务
        optimized_tasks = []
        max_len = max(len(memory_intensive), len(cpu_intensive), len(io_intensive), len(normal_tasks))
        
        for i in range(max_len):
            if i < len(normal_tasks):
                optimized_tasks.append(normal_tasks[i])
            if i < len(io_intensive):
                optimized_tasks.append(io_intensive[i])
            if i < len(cpu_intensive):
                optimized_tasks.append(cpu_intensive[i])
            if i < len(memory_intensive):
                optimized_tasks.append(memory_intensive[i])
        
        return optimized_tasks
    
    async def schedule_tasks(self, tasks: List[StartupTask]) -> None:
        """调度任务执行"""
        self.scheduler.add_tasks(tasks)
    
    async def get_next_tasks(self) -> List[StartupTask]:
        """获取下一批要执行的任务"""
        return await self.scheduler.schedule_next_tasks()
    
    def mark_task_completed(self, task_name: str, success: bool) -> None:
        """标记任务完成"""
        self.scheduler.mark_task_completed(task_name, success)
    
    def add_running_task(self, task_name: str, task_handle: asyncio.Task) -> None:
        """添加运行中的任务"""
        self.scheduler.add_running_task(task_name, task_handle)
    
    def record_phase_performance(
        self,
        phase: StartupPhase,
        duration: float,
        task_count: int
    ) -> None:
        """记录阶段性能"""
        current_usage = self.scheduler.resource_monitor.get_current_usage()
        self.tuner.record_performance(phase, duration, task_count, current_usage)
    
    def get_optimization_report(self) -> Dict[str, Any]:
        """获取优化报告"""
        performance_analysis = self.tuner.analyze_performance()
        suggestions = self.tuner.generate_optimization_suggestions(performance_analysis)
        scheduling_stats = self.scheduler.get_scheduling_stats()
        
        return {
            "config": {
                "strategy": self.config.strategy.value,
                "max_parallel_tasks": self.config.max_parallel_tasks,
                "max_memory_usage_mb": self.config.max_memory_usage_mb,
                "max_cpu_usage_percent": self.config.max_cpu_usage_percent
            },
            "performance_analysis": performance_analysis,
            "optimization_suggestions": suggestions,
            "scheduling_stats": scheduling_stats,
            "current_resource_usage": self.scheduler.resource_monitor.get_current_usage().memory_mb
        }


# 全局启动优化器
global_startup_optimizer = StartupOptimizer()