package com.crypto.trading.sdk.websocket;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.net.URI;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;

/**
 * WebSocket连接池
 * 管理多个WebSocket连接，提供连接创建、关闭和状态查询功能
 */
@Component
public class WebSocketConnectionPool {

    private static final Logger log = LoggerFactory.getLogger(WebSocketConnectionPool.class);

    /**
     * 连接ID生成器
     */
    private final AtomicInteger connectionIdGenerator = new AtomicInteger(1);

    /**
     * 连接映射表，键为连接ID，值为WebSocket连接
     */
    private final Map<Integer, WebSocketConnection> connections = new ConcurrentHashMap<>();

    /**
     * 创建WebSocket连接
     *
     * @param uri            WebSocket URI
     * @param messageHandler 消息处理器
     * @return 连接ID
     */
    public int createConnection(URI uri, Consumer<String> messageHandler) {
        return createConnection(uri, messageHandler, null, null, null);
    }

    /**
     * 创建WebSocket连接
     *
     * @param uri               WebSocket URI
     * @param messageHandler    消息处理器
     * @param onOpenCallback    连接打开回调
     * @param onCloseCallback   连接关闭回调
     * @param onFailureCallback 连接失败回调
     * @return 连接ID
     */
    public int createConnection(URI uri, Consumer<String> messageHandler,
                                Runnable onOpenCallback, Consumer<Integer> onCloseCallback,
                                Consumer<Throwable> onFailureCallback) {
        int connectionId = connectionIdGenerator.getAndIncrement();
        
        WebSocketConnection connection = WebSocketConnection.create(
                connectionId, uri, messageHandler, onOpenCallback,
                statusCode -> {
                    if (onCloseCallback != null) {
                        onCloseCallback.accept(statusCode);
                    }
                    connections.remove(connectionId);
                },
                onFailureCallback);
        
        connections.put(connectionId, connection);
        
        connection.connect();
        
        return connectionId;
    }

    /**
     * 关闭指定连接
     *
     * @param connectionId 连接ID
     * @return 是否成功关闭
     */
    public boolean closeConnection(int connectionId) {
        WebSocketConnection connection = connections.remove(connectionId);
        if (connection != null) {
            connection.close();
            log.info("已关闭WebSocket连接: ID={}", connectionId);
            return true;
        } else {
            log.warn("未找到WebSocket连接: ID={}", connectionId);
            return false;
        }
    }

    /**
     * 关闭所有连接
     */
    public void closeAllConnections() {
        log.info("正在关闭所有WebSocket连接，总数: {}", connections.size());
        connections.values().forEach(WebSocketConnection::close);
        connections.clear();
        log.info("已关闭所有WebSocket连接");
    }

    /**
     * 获取活跃连接数
     *
     * @return 活跃连接数
     */
    public int getActiveConnectionCount() {
        return (int) connections.values().stream()
                .filter(WebSocketConnection::isActive)
                .count();
    }

    /**
     * 获取总连接数
     *
     * @return 总连接数
     */
    public int getTotalConnectionCount() {
        return connections.size();
    }

    /**
     * 检查连接是否活跃
     *
     * @param connectionId 连接ID
     * @return 连接是否活跃
     */
    public boolean isConnectionActive(int connectionId) {
        WebSocketConnection connection = connections.get(connectionId);
        return connection != null && connection.isActive();
    }

    /**
     * 检查连接是否存在
     *
     * @param connectionId 连接ID
     * @return 连接是否存在
     */
    public boolean hasConnection(int connectionId) {
        return connections.containsKey(connectionId);
    }

    /**
     * 获取连接
     *
     * @param connectionId 连接ID
     * @return WebSocket连接，如果不存在则返回null
     */
    public WebSocketConnection getConnection(int connectionId) {
        return connections.get(connectionId);
    }

    /**
     * 向指定连接发送消息
     *
     * @param connectionId 连接ID
     * @param message      消息内容
     * @return 是否发送成功
     */
    public boolean sendMessage(int connectionId, String message) {
        WebSocketConnection connection = connections.get(connectionId);
        if (connection != null && connection.isActive()) {
            return connection.sendMessage(message);
        } else {
            log.warn("无法发送消息，连接不存在或未打开: ID={}", connectionId);
            return false;
        }
    }
} 