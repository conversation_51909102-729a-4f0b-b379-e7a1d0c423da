package com.crypto.trading.bootstrap.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * 币安API配置类
 * 
 * 该类仅提供兼容性，实际配置已移至sdk模块中的BinanceApiConfig
 * 
 * @deprecated 请使用 com.crypto.trading.sdk.config.BinanceApiConfig
 */
@Configuration
@Deprecated
public class BinanceApiConfig {

    /**
     * 创建一个名为binanceApiConfigBean的Bean
     * 这将防止sdk模块中的BinanceApiConfig被创建
     * 
     * @return sdk模块中的BinanceApiConfig实例
     */
    @Bean(name = "binanceApiConfigBean")
    @Primary
    public com.crypto.trading.sdk.config.BinanceApiConfig binanceApiConfigBean() {
        return new com.crypto.trading.sdk.config.BinanceApiConfig();
    }
}