#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
语法检查脚本

检查所有Python文件的语法是否正确
"""

import os
import py_compile
import sys

def check_file_syntax(file_path):
    """检查单个文件的语法"""
    try:
        py_compile.compile(file_path, doraise=True)
        return True, None
    except py_compile.PyCompileError as e:
        return False, str(e)
    except Exception as e:
        return False, str(e)

def check_all_syntax():
    """检查所有Python文件的语法"""
    
    project_root = os.path.dirname(os.path.abspath(__file__))
    src_dir = os.path.join(project_root, 'src')
    
    print("Checking Python file syntax...")
    
    # 要检查的文件列表
    files_to_check = [
        'src/config.py',
        'src/logger.py',
        'src/kafka_client.py',
        'src/data_processor.py',
        'src/main.py',
        'src/data/influxdb_client.py',
        'src/data/mysql_client.py',
        'src/data/real_data_loader.py',
        'src/model/model_trainer.py',
        'src/model/prediction.py',
        'src/model/online_learner.py',
        'src/model/versioning.py',
        'src/strategy/base.py',
        'src/strategy/unified_ml.py',
        'src/indicators/lppl_features.py',
        'src/indicators/hematread_features.py',
        'src/indicators/bull_market_support.py',
        'src/indicators/super_trend.py'
    ]
    
    success_count = 0
    total_count = len(files_to_check)
    
    for file_path in files_to_check:
        full_path = os.path.join(project_root, file_path)
        
        if os.path.exists(full_path):
            success, error = check_file_syntax(full_path)
            if success:
                print(f"[OK] {file_path}")
                success_count += 1
            else:
                print(f"[FAIL] {file_path}: {error}")
        else:
            print(f"[MISSING] {file_path}")
    
    print(f"\nSyntax check completed: {success_count}/{total_count} files passed")
    
    if success_count == total_count:
        print("All files have correct syntax!")
        return True
    else:
        print("Some files have syntax errors!")
        return False

if __name__ == "__main__":
    success = check_all_syntax()
    sys.exit(0 if success else 1)