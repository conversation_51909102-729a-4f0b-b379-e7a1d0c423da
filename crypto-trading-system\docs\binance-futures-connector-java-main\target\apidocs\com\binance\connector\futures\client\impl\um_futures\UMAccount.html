<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (21) on Fri Apr 04 16:06:08 CST 2025 -->
<title>UMAccount (io.github.binance:binance-futures-connector-java 3.0.5 API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-04-04">
<meta name="description" content="declaration: package: com.binance.connector.futures.client.impl.um_futures, class: UMAccount">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="切换导航链接"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="../../../../../../../index.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="nav-bar-cell1-rev">类</li>
<li><a href="class-use/UMAccount.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../../../index-all.html">索引</a></li>
<li><a href="../../../../../../../help-doc.html#class">帮助</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>概要:</p>
<ul>
<li>嵌套</li>
<li>字段</li>
<li><a href="#constructor-summary">构造器</a></li>
<li><a href="#method-summary">方法</a></li>
</ul>
</li>
<li>
<p>详细资料:</p>
<ul>
<li>字段</li>
<li><a href="#constructor-detail">构造器</a></li>
<li><a href="#method-detail">方法</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">方法</a></li>
</ul>
<ul class="sub-nav-list">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">方法</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="搜索">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">程序包</span>&nbsp;<a href="package-summary.html">com.binance.connector.futures.client.impl.um_futures</a></div>
<h1 title="类 UMAccount" class="title">类 UMAccount</h1>
</div>
<div class="inheritance" title="继承树"><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../futures/Account.html" title="com.binance.connector.futures.client.impl.futures中的类">com.binance.connector.futures.client.impl.futures.Account</a>
<div class="inheritance">com.binance.connector.futures.client.impl.um_futures.UMAccount</div>
</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">UMAccount</span>
<span class="extends-implements">extends <a href="../futures/Account.html" title="com.binance.connector.futures.client.impl.futures中的类">Account</a></span></div>
<div class="block"><h2 id="usd--margined-trade-endpoints-heading">USDⓈ-Margined Trade Endpoints</h2>
 All endpoints under the
 <a href="https://developers.binance.com/docs/derivatives/usds-margined-futures/account/rest-api/New-Future-Account-Transfer">Futures Account/Trade Endpoint</a>
 section of the API documentation will be implemented in this class.
 <br>
 Response will be returned in <i>String format</i>.</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>构造器概要</h2>
<div class="caption"><span>构造器</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">构造器</div>
<div class="table-header col-last">说明</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(java.lang.String,java.lang.String,java.lang.String,boolean,com.binance.connector.futures.client.utils.ProxyAuth)" class="member-name-link">UMAccount</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;productUrl,
 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;apiKey,
 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;secretKey,
 boolean&nbsp;showLimitUsage,
 com.binance.connector.futures.client.utils.ProxyAuth&nbsp;proxy)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>方法概要</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">所有方法</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">实例方法</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">具体方法</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">修饰符和类型</div>
<div class="table-header col-second">方法</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#accountInformation(java.util.LinkedHashMap)" class="member-name-link">accountInformation</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/util/LinkedHashMap.html" title="java.util中的类或接口" class="external-link">LinkedHashMap</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&nbsp;parameters)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get current account information.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#accountTradeList(java.util.LinkedHashMap)" class="member-name-link">accountTradeList</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/util/LinkedHashMap.html" title="java.util中的类或接口" class="external-link">LinkedHashMap</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&nbsp;parameters)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get trades for a specific account and symbol.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#allOrders(java.util.LinkedHashMap)" class="member-name-link">allOrders</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/util/LinkedHashMap.html" title="java.util中的类或接口" class="external-link">LinkedHashMap</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&nbsp;parameters)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get all open orders on a symbol.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#changeMultiAssetsMode(java.util.LinkedHashMap)" class="member-name-link">changeMultiAssetsMode</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/util/LinkedHashMap.html" title="java.util中的类或接口" class="external-link">LinkedHashMap</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&nbsp;parameters)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Change user's Multi-Assets mode (Multi-Assets Mode or Single-Asset Mode) on Every symbol
 <br><br>
 POST /v1/multiAssetsMargin
 <br></div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#currentAllOpenOrders(java.util.LinkedHashMap)" class="member-name-link">currentAllOpenOrders</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/util/LinkedHashMap.html" title="java.util中的类或接口" class="external-link">LinkedHashMap</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&nbsp;parameters)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get all open orders on a symbol.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#futuresAccountBalance(java.util.LinkedHashMap)" class="member-name-link">futuresAccountBalance</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/util/LinkedHashMap.html" title="java.util中的类或接口" class="external-link">LinkedHashMap</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&nbsp;parameters)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get Futures Account Balance
 <br><br>
 GET /v2/balance
 <br></div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#futuresDownloadId(java.util.LinkedHashMap)" class="member-name-link">futuresDownloadId</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/util/LinkedHashMap.html" title="java.util中的类或接口" class="external-link">LinkedHashMap</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&nbsp;parameters)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get Download Id For Futures Transaction History
 <br><br>
 GET /v1/income/asyn
 <br></div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#futuresDownloadLink(java.util.LinkedHashMap)" class="member-name-link">futuresDownloadLink</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/util/LinkedHashMap.html" title="java.util中的类或接口" class="external-link">LinkedHashMap</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&nbsp;parameters)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get Futures Transaction History Download Link by Id
 <br><br>
 GET /v1/income/asyn/id
 <br></div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getCurrentMultiAssetMode(java.util.LinkedHashMap)" class="member-name-link">getCurrentMultiAssetMode</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/util/LinkedHashMap.html" title="java.util中的类或接口" class="external-link">LinkedHashMap</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&nbsp;parameters)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get user's Multi-Assets mode (Multi-Assets Mode or Single-Asset Mode) on Every symbol
 <br><br>
 GET /v1/multiAssetsMargin
 <br></div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getLeverageBracket(java.util.LinkedHashMap)" class="member-name-link">getLeverageBracket</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/util/LinkedHashMap.html" title="java.util中的类或接口" class="external-link">LinkedHashMap</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&nbsp;parameters)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Notional and Leverage Brackets
 <br><br>
 GET /v1/leverageBracket
 <br></div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getTradingRulesIndicators(java.util.LinkedHashMap)" class="member-name-link">getTradingRulesIndicators</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/util/LinkedHashMap.html" title="java.util中的类或接口" class="external-link">LinkedHashMap</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&nbsp;parameters)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Futures Trading Quantitative Rules Indicators
 For more information on this, please refer to the <a href="https://www.binance.com/en/support/faq/4f462ebe6ff445d4a170be7d9e897272">Futures Trading Quantitative Rules</a>
 <br><br>
 GET /v1/apiTradingStatus
 <br></div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#positionInformation(java.util.LinkedHashMap)" class="member-name-link">positionInformation</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/util/LinkedHashMap.html" title="java.util中的类或接口" class="external-link">LinkedHashMap</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&nbsp;parameters)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get current position information.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-com.binance.connector.futures.client.impl.futures.Account">从类继承的方法&nbsp;com.binance.connector.futures.client.impl.futures.<a href="../futures/Account.html" title="com.binance.connector.futures.client.impl.futures中的类">Account</a></h3>
<code><a href="../futures/Account.html#autoCancelOpen(java.util.LinkedHashMap)">autoCancelOpen</a>, <a href="../futures/Account.html#cancelAllOpenOrders(java.util.LinkedHashMap)">cancelAllOpenOrders</a>, <a href="../futures/Account.html#cancelMultipleOrders(java.util.LinkedHashMap)">cancelMultipleOrders</a>, <a href="../futures/Account.html#cancelOrder(java.util.LinkedHashMap)">cancelOrder</a>, <a href="../futures/Account.html#changeInitialLeverage(java.util.LinkedHashMap)">changeInitialLeverage</a>, <a href="../futures/Account.html#changeMarginType(java.util.LinkedHashMap)">changeMarginType</a>, <a href="../futures/Account.html#changePositionModeTrade(java.util.LinkedHashMap)">changePositionModeTrade</a>, <a href="../futures/Account.html#getAdlQuantile(java.util.LinkedHashMap)">getAdlQuantile</a>, <a href="../futures/Account.html#getCommissionRate(java.util.LinkedHashMap)">getCommissionRate</a>, <a href="../futures/Account.html#getCurrentPositionMode(java.util.LinkedHashMap)">getCurrentPositionMode</a>, <a href="../futures/Account.html#getForceOrders(java.util.LinkedHashMap)">getForceOrders</a>, <a href="../futures/Account.html#getIncomeHistory(java.util.LinkedHashMap)">getIncomeHistory</a>, <a href="../futures/Account.html#getPositionMarginChangeHistory(java.util.LinkedHashMap)">getPositionMarginChangeHistory</a>, <a href="../futures/Account.html#getProductUrl()">getProductUrl</a>, <a href="../futures/Account.html#getRequestHandler()">getRequestHandler</a>, <a href="../futures/Account.html#getShowLimitUsage()">getShowLimitUsage</a>, <a href="../futures/Account.html#modifyIsolatedPositionMargin(java.util.LinkedHashMap)">modifyIsolatedPositionMargin</a>, <a href="../futures/Account.html#newOrder(java.util.LinkedHashMap)">newOrder</a>, <a href="../futures/Account.html#placeMultipleOrders(java.util.LinkedHashMap)">placeMultipleOrders</a>, <a href="../futures/Account.html#queryCurrentOpenOrder(java.util.LinkedHashMap)">queryCurrentOpenOrder</a>, <a href="../futures/Account.html#queryOrder(java.util.LinkedHashMap)">queryOrder</a>, <a href="../futures/Account.html#setProductUrl(java.lang.String)">setProductUrl</a>, <a href="../futures/Account.html#setRequestHandler(java.lang.String,java.lang.String,com.binance.connector.futures.client.utils.ProxyAuth)">setRequestHandler</a>, <a href="../futures/Account.html#setShowLimitUsage(boolean)">setShowLimitUsage</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">从类继承的方法&nbsp;java.lang.<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a></h3>
<code><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html#clone--" title="java.lang中的类或接口" class="external-link">clone</a>, <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html#equals-java.lang.Object-" title="java.lang中的类或接口" class="external-link">equals</a>, <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html#finalize--" title="java.lang中的类或接口" class="external-link">finalize</a>, <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html#getClass--" title="java.lang中的类或接口" class="external-link">getClass</a>, <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html#hashCode--" title="java.lang中的类或接口" class="external-link">hashCode</a>, <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html#notify--" title="java.lang中的类或接口" class="external-link">notify</a>, <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html#notifyAll--" title="java.lang中的类或接口" class="external-link">notifyAll</a>, <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html#toString--" title="java.lang中的类或接口" class="external-link">toString</a>, <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html#wait--" title="java.lang中的类或接口" class="external-link">wait</a>, <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html#wait-long-" title="java.lang中的类或接口" class="external-link">wait</a>, <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html#wait-long-int-" title="java.lang中的类或接口" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>构造器详细资料</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;(java.lang.String,java.lang.String,java.lang.String,boolean,com.binance.connector.futures.client.utils.ProxyAuth)">
<h3>UMAccount</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">UMAccount</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;productUrl,
 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;apiKey,
 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;secretKey,
 boolean&nbsp;showLimitUsage,
 com.binance.connector.futures.client.utils.ProxyAuth&nbsp;proxy)</span></div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>方法详细资料</h2>
<ul class="member-list">
<li>
<section class="detail" id="changeMultiAssetsMode(java.util.LinkedHashMap)">
<h3>changeMultiAssetsMode</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></span>&nbsp;<span class="element-name">changeMultiAssetsMode</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/util/LinkedHashMap.html" title="java.util中的类或接口" class="external-link">LinkedHashMap</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&nbsp;parameters)</span></div>
<div class="block">Change user's Multi-Assets mode (Multi-Assets Mode or Single-Asset Mode) on Every symbol
 <br><br>
 POST /v1/multiAssetsMargin
 <br></div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>parameters</code> - LinkedHashedMap of String,Object pair
            where String is the name of the parameter and Object is the value of the parameter
 <br><br>
 multiAssetsMargin -- mandatory/string <br>
 recvWindow -- optional/long <br></dd>
<dt>返回:</dt>
<dd>String</dd>
<dt>另请参阅:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://developers.binance.com/docs/derivatives/usds-margined-futures/trade/rest-api/Change-Multi-Assets-Mode">
     https://developers.binance.com/docs/derivatives/usds-margined-futures/trade/rest-api/Change-Multi-Assets-Mode</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getCurrentMultiAssetMode(java.util.LinkedHashMap)">
<h3>getCurrentMultiAssetMode</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></span>&nbsp;<span class="element-name">getCurrentMultiAssetMode</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/util/LinkedHashMap.html" title="java.util中的类或接口" class="external-link">LinkedHashMap</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&nbsp;parameters)</span></div>
<div class="block">Get user's Multi-Assets mode (Multi-Assets Mode or Single-Asset Mode) on Every symbol
 <br><br>
 GET /v1/multiAssetsMargin
 <br></div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>parameters</code> - LinkedHashedMap of String,Object pair
            where String is the name of the parameter and Object is the value of the parameter
 <br><br>
 recvWindow -- optional/long <br></dd>
<dt>返回:</dt>
<dd>String</dd>
<dt>另请参阅:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://developers.binance.com/docs/derivatives/usds-margined-futures/account/rest-api/Get-Current-Multi-Assets-Mode">
     https://developers.binance.com/docs/derivatives/usds-margined-futures/account/rest-api/Get-Current-Multi-Assets-Mode</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="currentAllOpenOrders(java.util.LinkedHashMap)">
<h3>currentAllOpenOrders</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></span>&nbsp;<span class="element-name">currentAllOpenOrders</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/util/LinkedHashMap.html" title="java.util中的类或接口" class="external-link">LinkedHashMap</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&nbsp;parameters)</span></div>
<div class="block">Get all open orders on a symbol. Careful when accessing this with no symbol.
 <br><br>
 GET /v1/openOrders
 <br></div>
<dl class="notes">
<dt>覆盖:</dt>
<dd><code><a href="../futures/Account.html#currentAllOpenOrders(java.util.LinkedHashMap)">currentAllOpenOrders</a></code>&nbsp;在类中&nbsp;<code><a href="../futures/Account.html" title="com.binance.connector.futures.client.impl.futures中的类">Account</a></code></dd>
<dt>参数:</dt>
<dd><code>parameters</code> - LinkedHashedMap of String,Object pair
            where String is the name of the parameter and Object is the value of the parameter
 <br><br>
 symbol -- optional/string <br>
 recvWindow -- optional/long <br></dd>
<dt>返回:</dt>
<dd>String</dd>
<dt>另请参阅:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://developers.binance.com/docs/derivatives/usds-margined-futures/trade/rest-api/Current-All-Open-Orders">
    https://developers.binance.com/docs/derivatives/usds-margined-futures/trade/rest-api/Current-All-Open-Orders</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="allOrders(java.util.LinkedHashMap)">
<h3>allOrders</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></span>&nbsp;<span class="element-name">allOrders</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/util/LinkedHashMap.html" title="java.util中的类或接口" class="external-link">LinkedHashMap</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&nbsp;parameters)</span></div>
<div class="block">Get all open orders on a symbol. Careful when accessing this with no symbol.
 <br><br>
 GET /v1/allOrders
 <br></div>
<dl class="notes">
<dt>覆盖:</dt>
<dd><code><a href="../futures/Account.html#allOrders(java.util.LinkedHashMap)">allOrders</a></code>&nbsp;在类中&nbsp;<code><a href="../futures/Account.html" title="com.binance.connector.futures.client.impl.futures中的类">Account</a></code></dd>
<dt>参数:</dt>
<dd><code>parameters</code> - LinkedHashedMap of String,Object pair
            where String is the name of the parameter and Object is the value of the parameter
 <br><br>
 symbol -- mandatory/string <br>
 orderId -- optional/long <br>
 startTime -- optional/long <br>
 endTime -- optional/long <br>
 limit -- optional/integer <br>
 recvWindow -- optional/long <br></dd>
<dt>返回:</dt>
<dd>String</dd>
<dt>另请参阅:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://developers.binance.com/docs/derivatives/usds-margined-futures/trade/rest-api/All-Orders">
    https://developers.binance.com/docs/derivatives/usds-margined-futures/trade/rest-api/All-Orders</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="futuresAccountBalance(java.util.LinkedHashMap)">
<h3>futuresAccountBalance</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></span>&nbsp;<span class="element-name">futuresAccountBalance</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/util/LinkedHashMap.html" title="java.util中的类或接口" class="external-link">LinkedHashMap</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&nbsp;parameters)</span></div>
<div class="block">Get Futures Account Balance
 <br><br>
 GET /v2/balance
 <br></div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>parameters</code> - LinkedHashedMap of String,Object pair
            where String is the name of the parameter and Object is the value of the parameter
 <br><br>
 recvWindow -- optional/long <br></dd>
<dt>返回:</dt>
<dd>String</dd>
<dt>另请参阅:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://developers.binance.com/docs/derivatives/usds-margined-futures/account/rest-api/Futures-Account-Balance-V2">
    https://developers.binance.com/docs/derivatives/usds-margined-futures/account/rest-api/Futures-Account-Balance-V2</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="accountInformation(java.util.LinkedHashMap)">
<h3>accountInformation</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></span>&nbsp;<span class="element-name">accountInformation</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/util/LinkedHashMap.html" title="java.util中的类或接口" class="external-link">LinkedHashMap</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&nbsp;parameters)</span></div>
<div class="block">Get current account information. User in single-asset/ multi-assets mode will see different value, see comments in response section for detail.
 <br><br>
 GET /v2/account
 <br></div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>parameters</code> - LinkedHashedMap of String,Object pair
            where String is the name of the parameter and Object is the value of the parameter
 <br><br>
 recvWindow -- optional/long <br></dd>
<dt>返回:</dt>
<dd>String</dd>
<dt>另请参阅:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://developers.binance.com/docs/derivatives/usds-margined-futures/account/rest-api/Account-Information-V2">
    https://developers.binance.com/docs/derivatives/usds-margined-futures/account/rest-api/Account-Information-V2</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="positionInformation(java.util.LinkedHashMap)">
<h3>positionInformation</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></span>&nbsp;<span class="element-name">positionInformation</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/util/LinkedHashMap.html" title="java.util中的类或接口" class="external-link">LinkedHashMap</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&nbsp;parameters)</span></div>
<div class="block">Get current position information.
 <br><br>
 GET /v2/positionRisk
 <br></div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>parameters</code> - LinkedHashedMap of String,Object pair
            where String is the name of the parameter and Object is the value of the parameter
 <br><br>
 symbol -- optional/string <br>
 recvWindow -- optional/long <br></dd>
<dt>返回:</dt>
<dd>String</dd>
<dt>另请参阅:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://developers.binance.com/docs/derivatives/usds-margined-futures/trade/rest-api/Position-Information-V2">
    https://developers.binance.com/docs/derivatives/usds-margined-futures/trade/rest-api/Position-Information-V2</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="accountTradeList(java.util.LinkedHashMap)">
<h3>accountTradeList</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></span>&nbsp;<span class="element-name">accountTradeList</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/util/LinkedHashMap.html" title="java.util中的类或接口" class="external-link">LinkedHashMap</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&nbsp;parameters)</span></div>
<div class="block">Get trades for a specific account and symbol.
 <br><br>
 GET /v1/userTrades
 <br></div>
<dl class="notes">
<dt>覆盖:</dt>
<dd><code><a href="../futures/Account.html#accountTradeList(java.util.LinkedHashMap)">accountTradeList</a></code>&nbsp;在类中&nbsp;<code><a href="../futures/Account.html" title="com.binance.connector.futures.client.impl.futures中的类">Account</a></code></dd>
<dt>参数:</dt>
<dd><code>parameters</code> - LinkedHashedMap of String,Object pair
            where String is the name of the parameter and Object is the value of the parameter
 <br><br>
 symbol -- mandatory/string <br>
 startTime -- optional/long <br>
 endTime -- optional/long <br>
 fromId -- optional/long <br>
 limit -- optional/integer <br>
 recvWindow -- optional/long <br></dd>
<dt>返回:</dt>
<dd>String</dd>
<dt>另请参阅:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://developers.binance.com/docs/derivatives/usds-margined-futures/trade/rest-api/Account-Trade-List">
    https://developers.binance.com/docs/derivatives/usds-margined-futures/trade/rest-api/Account-Trade-List</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getLeverageBracket(java.util.LinkedHashMap)">
<h3>getLeverageBracket</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></span>&nbsp;<span class="element-name">getLeverageBracket</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/util/LinkedHashMap.html" title="java.util中的类或接口" class="external-link">LinkedHashMap</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&nbsp;parameters)</span></div>
<div class="block">Notional and Leverage Brackets
 <br><br>
 GET /v1/leverageBracket
 <br></div>
<dl class="notes">
<dt>覆盖:</dt>
<dd><code><a href="../futures/Account.html#getLeverageBracket(java.util.LinkedHashMap)">getLeverageBracket</a></code>&nbsp;在类中&nbsp;<code><a href="../futures/Account.html" title="com.binance.connector.futures.client.impl.futures中的类">Account</a></code></dd>
<dt>参数:</dt>
<dd><code>parameters</code> - LinkedHashedMap of String,Object pair
            where String is the name of the parameter and Object is the value of the parameter
 <br><br>
 symbol -- optional/string <br>
 recvWindow -- optional/long <br></dd>
<dt>返回:</dt>
<dd>String</dd>
<dt>另请参阅:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://developers.binance.com/docs/derivatives/usds-margined-futures/account/rest-api/Notional-and-Leverage-Brackets">
    https://developers.binance.com/docs/derivatives/usds-margined-futures/account/rest-api/Notional-and-Leverage-Brackets</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getTradingRulesIndicators(java.util.LinkedHashMap)">
<h3>getTradingRulesIndicators</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></span>&nbsp;<span class="element-name">getTradingRulesIndicators</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/util/LinkedHashMap.html" title="java.util中的类或接口" class="external-link">LinkedHashMap</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&nbsp;parameters)</span></div>
<div class="block">Futures Trading Quantitative Rules Indicators
 For more information on this, please refer to the <a href="https://www.binance.com/en/support/faq/4f462ebe6ff445d4a170be7d9e897272">Futures Trading Quantitative Rules</a>
 <br><br>
 GET /v1/apiTradingStatus
 <br></div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>parameters</code> - LinkedHashedMap of String,Object pair
            where String is the name of the parameter and Object is the value of the parameter
 <br><br>
 symbol -- optional/string <br>
 recvWindow -- optional/long <br></dd>
<dt>返回:</dt>
<dd>String</dd>
<dt>另请参阅:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://developers.binance.com/docs/derivatives/usds-margined-futures/account/rest-api/Futures-Trading-Quantitative-Rules-Indicators">
    https://developers.binance.com/docs/derivatives/usds-margined-futures/account/rest-api/Futures-Trading-Quantitative-Rules-Indicators</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="futuresDownloadId(java.util.LinkedHashMap)">
<h3>futuresDownloadId</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></span>&nbsp;<span class="element-name">futuresDownloadId</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/util/LinkedHashMap.html" title="java.util中的类或接口" class="external-link">LinkedHashMap</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&nbsp;parameters)</span></div>
<div class="block">Get Download Id For Futures Transaction History
 <br><br>
 GET /v1/income/asyn
 <br></div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>parameters</code> - LinkedHashedMap of String,Object pair
            where String is the name of the parameter and Object is the value of the parameter
 <br><br>
 startTime -- optional/long <br>
 endTime -- optional/long <br>
 recvWindow -- optional/long <br></dd>
<dt>返回:</dt>
<dd>String</dd>
<dt>另请参阅:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://developers.binance.com/docs/derivatives/usds-margined-futures/account/rest-api/Get-Download-Id-For-Futures-Transaction-History">
    https://developers.binance.com/docs/derivatives/usds-margined-futures/account/rest-api/Get-Download-Id-For-Futures-Transaction-History</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="futuresDownloadLink(java.util.LinkedHashMap)">
<h3>futuresDownloadLink</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></span>&nbsp;<span class="element-name">futuresDownloadLink</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/util/LinkedHashMap.html" title="java.util中的类或接口" class="external-link">LinkedHashMap</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&nbsp;parameters)</span></div>
<div class="block">Get Futures Transaction History Download Link by Id
 <br><br>
 GET /v1/income/asyn/id
 <br></div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>parameters</code> - LinkedHashedMap of String,Object pair
            where String is the name of the parameter and Object is the value of the parameter
 <br><br>
 downloadId -- mandatory/string <br>
 recvWindow -- optional/long <br></dd>
<dt>返回:</dt>
<dd>String</dd>
<dt>另请参阅:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://developers.binance.com/docs/derivatives/usds-margined-futures/account/rest-api/Get-Futures-Transaction-History-Download-Link-by-Id">
    https://developers.binance.com/docs/derivatives/usds-margined-futures/account/rest-api/Get-Futures-Transaction-History-Download-Link-by-Id</a></li>
</ul>
</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &#169; 2025. All rights reserved.</small></p>
</footer>
</div>
</div>
</body>
</html>
