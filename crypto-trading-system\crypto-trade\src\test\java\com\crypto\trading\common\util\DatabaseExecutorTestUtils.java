package com.crypto.trading.common.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 数据库执行器测试工具类
 * <p>
 * 提供用于测试环境的工具方法，如等待异步操作完成
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class DatabaseExecutorTestUtils {

    private static final Logger log = LoggerFactory.getLogger(DatabaseExecutorTestUtils.class);

    /**
     * 默认超时时间（毫秒）
     */
    private static final long DEFAULT_TIMEOUT_MS = 5000;

    /**
     * 默认轮询间隔（毫秒）
     */
    private static final long DEFAULT_POLL_INTERVAL_MS = 100;

    /**
     * 私有构造函数，防止实例化
     */
    private DatabaseExecutorTestUtils() {
        throw new IllegalStateException("工具类不允许实例化");
    }

    /**
     * 等待所有异步数据库操作完成
     * <p>
     * 此方法会轮询DatabaseExecutor的活跃操作数，直到所有操作完成或超时
     * </p>
     *
     * @return 是否在超时前所有操作都已完成
     */
    public static boolean waitForAsyncOperations() {
        return waitForAsyncOperations(DEFAULT_TIMEOUT_MS);
    }

    /**
     * 等待所有异步数据库操作完成
     * <p>
     * 此方法会轮询DatabaseExecutor的活跃操作数，直到所有操作完成或超时
     * </p>
     *
     * @param timeoutMs 超时时间（毫秒）
     * @return 是否在超时前所有操作都已完成
     */
    public static boolean waitForAsyncOperations(long timeoutMs) {
        return waitForAsyncOperations(timeoutMs, DEFAULT_POLL_INTERVAL_MS);
    }

    /**
     * 等待所有异步数据库操作完成
     * <p>
     * 此方法会轮询DatabaseExecutor的活跃操作数，直到所有操作完成或超时
     * </p>
     *
     * @param timeoutMs 超时时间（毫秒）
     * @param pollIntervalMs 轮询间隔（毫秒）
     * @return 是否在超时前所有操作都已完成
     */
    public static boolean waitForAsyncOperations(long timeoutMs, long pollIntervalMs) {
        log.info("等待异步数据库操作完成，超时时间：{}ms，轮询间隔：{}ms", timeoutMs, pollIntervalMs);
        
        long startTime = System.currentTimeMillis();
        long endTime = startTime + timeoutMs;
        
        while (System.currentTimeMillis() < endTime) {
            Map<String, Object> statistics = DatabaseExecutor.getStatistics();
            int activeOperations = (int) statistics.get("activeOperations");
            
            if (activeOperations == 0) {
                long elapsedTime = System.currentTimeMillis() - startTime;
                log.info("所有异步数据库操作已完成，耗时：{}ms", elapsedTime);
                return true;
            }
            
            log.debug("当前活跃操作数：{}，继续等待...", activeOperations);
            
            try {
                TimeUnit.MILLISECONDS.sleep(pollIntervalMs);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.warn("等待异步操作完成时被中断", e);
                return false;
            }
        }
        
        Map<String, Object> statistics = DatabaseExecutor.getStatistics();
        int activeOperations = (int) statistics.get("activeOperations");
        log.warn("等待异步数据库操作超时，仍有{}个活跃操作", activeOperations);
        return false;
    }
}