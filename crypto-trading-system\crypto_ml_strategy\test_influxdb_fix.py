#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试InfluxDB修复是否有效
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from datetime import datetime, timedelta
from data.clients.influxdb_client import InfluxDBClient

def test_influxdb_query():
    """测试InfluxDB查询是否能正常工作"""
    try:
        # 初始化InfluxDB客户端
        client = InfluxDBClient(
            url="http://localhost:8086",
            token="1mLmSyBqUYH5OKaSkFdybeS34Z6DyLlR3H3d802mmVvFVCiJ4GwOQzyT8QULaES96tBfPGI9ycDHkseYvPhGoA==",
            org="crypto",
            bucket="market_data"
        )
        
        print("InfluxDB客户端初始化成功")
        
        # 测试查询BTCUSDT 1m数据
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=1)  # 查询最近1小时的数据
        
        print(f"查询时间范围: {start_time} 到 {end_time}")
        
        result = client.query_kline_data("BTCUSDT", "1m", start_time, end_time)
        
        if result is not None and not result.empty:
            print(f"✅ 成功查询到BTCUSDT 1m数据: {len(result)}条记录")
            print("前5条记录:")
            print(result.head())
            return True
        else:
            print("❌ 未查询到BTCUSDT 1m数据")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False
    finally:
        if 'client' in locals():
            client.close()

if __name__ == "__main__":
    print("开始测试InfluxDB修复...")
    success = test_influxdb_query()
    if success:
        print("🎉 InfluxDB修复成功！")
    else:
        print("💥 InfluxDB修复失败！")