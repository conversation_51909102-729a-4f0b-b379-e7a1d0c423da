package com.crypto.trading.market.config;

import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.clients.producer.RecordMetadata;
import org.apache.kafka.common.serialization.StringSerializer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;
import org.springframework.kafka.support.ProducerListener;
import org.springframework.kafka.support.serializer.JsonSerializer;

import java.util.HashMap;
import java.util.Map;

/**
 * 异步Kafka生产者配置类
 * 专门用于异步数据处理架构，提供高可靠性的消息发送
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Configuration
public class AsyncKafkaProducerConfig {

    @Value("${spring.kafka.bootstrap-servers:localhost:9092}")
    private String bootstrapServers;

    /**
     * K线原始数据主题
     */
    @Value("${market.kline.raw.topic:kline.raw.data}")
    private String klineRawTopic;

    /**
     * 深度原始数据主题
     */
    @Value("${market.depth.raw.topic:depth.raw.data}")
    private String depthRawTopic;

    /**
     * 交易原始数据主题
     */
    @Value("${market.trade.raw.topic:trade.raw.data}")
    private String tradeRawTopic;

    /**
     * K线处理后数据主题
     */
    @Value("${market.kline.processed.topic:kline.processed.data}")
    private String klineProcessedTopic;

    /**
     * 深度处理后数据主题
     */
    @Value("${market.depth.processed.topic:depth.processed.data}")
    private String depthProcessedTopic;

    /**
     * 交易处理后数据主题
     */
    @Value("${market.trade.processed.topic:trade.processed.data}")
    private String tradeProcessedTopic;

    /**
     * 配置高可靠性的Kafka生产者工厂
     * 专门用于原始数据发送，确保数据不丢失
     *
     * @return 高可靠性的Kafka生产者工厂
     */
    @Bean("reliableProducerFactory")
    public ProducerFactory<String, Object> reliableProducerFactory() {
        Map<String, Object> configProps = new HashMap<>();
        
        // 基础配置
        configProps.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
        configProps.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        configProps.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, JsonSerializer.class);
        
        // 可靠性配置 - 确保数据不丢失
        configProps.put(ProducerConfig.ACKS_CONFIG, "all"); // 等待所有副本确认
        configProps.put(ProducerConfig.RETRIES_CONFIG, Integer.MAX_VALUE); // 无限重试
        configProps.put(ProducerConfig.RETRY_BACKOFF_MS_CONFIG, 1000); // 重试间隔1秒
        configProps.put(ProducerConfig.DELIVERY_TIMEOUT_MS_CONFIG, 300000); // 5分钟超时
        configProps.put(ProducerConfig.REQUEST_TIMEOUT_MS_CONFIG, 30000); // 30秒请求超时
        
        // 性能配置
        configProps.put(ProducerConfig.BATCH_SIZE_CONFIG, 32768); // 32KB批次大小
        configProps.put(ProducerConfig.LINGER_MS_CONFIG, 10); // 10ms等待时间
        configProps.put(ProducerConfig.BUFFER_MEMORY_CONFIG, 67108864); // 64MB缓冲区
        configProps.put(ProducerConfig.COMPRESSION_TYPE_CONFIG, "lz4"); // LZ4压缩
        
        // 幂等性配置 - 防止重复消息
        configProps.put(ProducerConfig.ENABLE_IDEMPOTENCE_CONFIG, true);
        configProps.put(ProducerConfig.MAX_IN_FLIGHT_REQUESTS_PER_CONNECTION, 5);
        
        return new DefaultKafkaProducerFactory<>(configProps);
    }

    /**
     * 配置高可靠性的Kafka模板
     * 用于发送原始数据到Kafka
     *
     * @return 高可靠性的Kafka模板
     */
    @Bean("reliableKafkaTemplate")
    public KafkaTemplate<String, Object> reliableKafkaTemplate() {
        KafkaTemplate<String, Object> template = new KafkaTemplate<>(reliableProducerFactory());
        
        // 设置生产者监听器
        template.setProducerListener(new ReliableProducerListener());
        
        return template;
    }

    /**
     * 配置高性能的Kafka生产者工厂
     * 用于处理后数据的快速发送
     *
     * @return 高性能的Kafka生产者工厂
     */
    @Bean("performanceProducerFactory")
    public ProducerFactory<String, Object> performanceProducerFactory() {
        Map<String, Object> configProps = new HashMap<>();
        
        // 基础配置
        configProps.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
        configProps.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        configProps.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, JsonSerializer.class);
        
        // 性能优先配置
        configProps.put(ProducerConfig.ACKS_CONFIG, "1"); // 只等待leader确认
        configProps.put(ProducerConfig.RETRIES_CONFIG, 3); // 3次重试
        configProps.put(ProducerConfig.RETRY_BACKOFF_MS_CONFIG, 500); // 重试间隔500ms
        configProps.put(ProducerConfig.DELIVERY_TIMEOUT_MS_CONFIG, 60000); // 1分钟超时
        configProps.put(ProducerConfig.REQUEST_TIMEOUT_MS_CONFIG, 10000); // 10秒请求超时
        
        // 高性能配置
        configProps.put(ProducerConfig.BATCH_SIZE_CONFIG, 65536); // 64KB批次大小
        configProps.put(ProducerConfig.LINGER_MS_CONFIG, 5); // 5ms等待时间
        configProps.put(ProducerConfig.BUFFER_MEMORY_CONFIG, 134217728); // 128MB缓冲区
        configProps.put(ProducerConfig.COMPRESSION_TYPE_CONFIG, "snappy"); // Snappy压缩
        
        return new DefaultKafkaProducerFactory<>(configProps);
    }

    /**
     * 配置高性能的Kafka模板
     * 用于发送处理后数据到Kafka
     *
     * @return 高性能的Kafka模板
     */
    @Bean("performanceKafkaTemplate")
    public KafkaTemplate<String, Object> performanceKafkaTemplate() {
        KafkaTemplate<String, Object> template = new KafkaTemplate<>(performanceProducerFactory());
        
        // 设置生产者监听器
        template.setProducerListener(new PerformanceProducerListener());
        
        return template;
    }

    // Getter方法
    public String getKlineRawTopic() {
        return klineRawTopic;
    }

    public String getDepthRawTopic() {
        return depthRawTopic;
    }

    public String getTradeRawTopic() {
        return tradeRawTopic;
    }

    public String getKlineProcessedTopic() {
        return klineProcessedTopic;
    }

    public String getDepthProcessedTopic() {
        return depthProcessedTopic;
    }

    public String getTradeProcessedTopic() {
        return tradeProcessedTopic;
    }

    /**
     * 可靠性优先的生产者监听器
     * 记录详细的发送结果和错误信息
     */
    private static class ReliableProducerListener implements ProducerListener<String, Object> {
        private static final Logger log = LoggerFactory.getLogger(ReliableProducerListener.class);

        @Override
        public void onSuccess(ProducerRecord<String, Object> producerRecord, RecordMetadata recordMetadata) {
            // 记录成功发送的消息
            if (log.isDebugEnabled()) {
                log.debug("消息发送成功: topic={}, partition={}, offset={}, key={}",
                    recordMetadata.topic(), recordMetadata.partition(),
                    recordMetadata.offset(), producerRecord.key());
            }
        }

        @Override
        public void onError(ProducerRecord<String, Object> producerRecord, RecordMetadata recordMetadata, Exception exception) {
            // 记录发送失败的消息
            log.error("消息发送失败: topic={}, key={}, error={}",
                producerRecord.topic(), producerRecord.key(), exception.getMessage(), exception);
        }
    }

    /**
     * 性能优先的生产者监听器
     * 只记录关键错误信息
     */
    private static class PerformanceProducerListener implements ProducerListener<String, Object> {
        private static final Logger log = LoggerFactory.getLogger(PerformanceProducerListener.class);

        @Override
        public void onSuccess(ProducerRecord<String, Object> producerRecord, RecordMetadata recordMetadata) {
            // 性能模式下不记录成功消息
        }

        @Override
        public void onError(ProducerRecord<String, Object> producerRecord, RecordMetadata recordMetadata, Exception exception) {
            // 只记录错误信息
            log.warn("高性能消息发送失败: topic={}, key={}, error={}",
                producerRecord.topic(), producerRecord.key(), exception.getMessage());
        }
    }
}
