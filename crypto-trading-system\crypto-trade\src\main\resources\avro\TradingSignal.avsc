{"namespace": "com.crypto.trading.trade.model.avro", "type": "record", "name": "TradingSignal", "doc": "交易信号，由策略生成，包含交易决策信息", "fields": [{"name": "id", "type": "string", "doc": "信号唯一ID"}, {"name": "symbol", "type": "string", "doc": "交易对，如BTCUSDT"}, {"name": "strategy", "type": "string", "doc": "策略名称"}, {"name": "signalType", "type": {"type": "enum", "name": "SignalType", "symbols": ["BUY", "SELL", "CLOSE_LONG", "CLOSE_SHORT", "HOLD"]}, "doc": "信号类型"}, {"name": "confidence", "type": "double", "doc": "信号置信度，0.0-1.0"}, {"name": "price", "type": ["null", "double"], "default": null, "doc": "目标价格，可选"}, {"name": "suggestedQuantity", "type": ["null", "double"], "default": null, "doc": "建议交易数量，可选"}, {"name": "stopLoss", "type": ["null", "double"], "default": null, "doc": "止损价格，可选"}, {"name": "takeProfit", "type": ["null", "double"], "default": null, "doc": "止盈价格，可选"}, {"name": "orderType", "type": {"type": "enum", "name": "OrderType", "symbols": ["MARKET", "LIMIT", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET"]}, "doc": "订单类型"}, {"name": "timeInForce", "type": {"type": "enum", "name": "TimeInForce", "symbols": ["GTC", "IOC", "FOK", "GTX"]}, "default": "GTC", "doc": "订单有效期"}, {"name": "leverageLevel", "type": ["null", "int"], "default": null, "doc": "杠杆倍数，可选"}, {"name": "generatedTime", "type": "long", "doc": "信号生成时间（毫秒时间戳）"}, {"name": "additionalParams", "type": {"type": "map", "values": "string"}, "doc": "额外参数，键值对形式"}]}