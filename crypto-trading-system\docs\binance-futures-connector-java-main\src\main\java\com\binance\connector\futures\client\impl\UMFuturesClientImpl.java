package com.binance.connector.futures.client.impl;

import com.binance.connector.futures.client.enums.DefaultUrls;
import com.binance.connector.futures.client.utils.ParameterChecker;
import com.binance.connector.futures.client.utils.ProxyAuth;
import okhttp3.OkHttpClient;
import okhttp3.Request;

import java.util.LinkedHashMap;

public class UMFuturesClientImpl extends FuturesClientImpl {

    private final String product = "um_futures";

    public UMFuturesClientImpl() {
        super("um_futures", DefaultUrls.USDT_PROD_URL);
    }

    public UMFuturesClientImpl(OkHttpClient client) {
        super("um_futures", DefaultUrls.USDT_PROD_URL, client);
    }

    public UMFuturesClientImpl(String baseUrl) {
        super("um_futures", baseUrl);
    }

    public UMFuturesClientImpl(String baseUrl, OkHttpClient client) {
        super("um_futures", baseUrl, client);
    }

    public UMFuturesClientImpl(String apiKey, String secretKey) {
        super("um_futures", apiKey, secretKey, DefaultUrls.USDT_PROD_URL);
    }

    public UMFuturesClientImpl(String apiKey, String secretKey, OkHttpClient client) {
        super("um_futures", apiKey, secretKey, DefaultUrls.USDT_PROD_URL, client);
    }

    public UMFuturesClientImpl(String apiKey, String secretKey, String baseUrl) {
        super("um_futures", apiKey, secretKey, baseUrl);
    }

    public UMFuturesClientImpl(String apiKey, String secretKey, String baseUrl, OkHttpClient client) {
        super("um_futures", apiKey, secretKey, baseUrl, client);
    }

    @Override
    public String getProduct() {
        return product;
    }

    /**
     * Create a new user data stream.
     * @return String
     */
    @Override
    public String createListenKey() {
        return signedRequest(userDataStreamEndpoint, null, "POST");
    }

    /**
     * Keep alive a user data stream to prevent a time out.
     * @return String
     */
    @Override
    public String extendListenKey() {
        return signedRequest(userDataStreamEndpoint, null, "PUT");
    }

    /**
     * Close out a user data stream.
     * @return String
     */
    @Override
    public String closeListenKey() {
        return signedRequest(userDataStreamEndpoint, null, "DELETE");
    }

    // Market
    private final String serverTimeEndpoint = "/fapi/v1/time";
    private final String exchangeInfoEndpoint = "/fapi/v1/exchangeInfo";
    private final String orderBookEndpoint = "/fapi/v1/depth";
    private final String recentTradesEndpoint = "/fapi/v1/trades";
    private final String historicalTradesEndpoint = "/fapi/v1/historicalTrades";
    private final String aggTradesEndpoint = "/fapi/v1/aggTrades";
    private final String klinesEndpoint = "/fapi/v1/klines";
    private final String continuousKlinesEndpoint = "/fapi/v1/continuousKlines";
    private final String indexPriceKlinesEndpoint = "/fapi/v1/indexPriceKlines";
    private final String markPriceKlinesEndpoint = "/fapi/v1/markPriceKlines";
    private final String ticker24hrEndpoint = "/fapi/v1/ticker/24hr";
    private final String tickerPriceEndpoint = "/fapi/v1/ticker/price";
    private final String bookTickerEndpoint = "/fapi/v1/ticker/bookTicker";
    private final String openInterestEndpoint = "/fapi/v1/openInterest";
    private final String openInterestHistEndpoint = "/fapi/v1/openInterestHist";
    private final String topLongShortAccountRatioEndpoint = "/futures/data/topLongShortAccountRatio";
    private final String topLongShortPositionRatioEndpoint = "/futures/data/topLongShortPositionRatio";
    private final String globalLongShortAccountRatioEndpoint = "/futures/data/globalLongShortAccountRatio";
    private final String takerBuySellVolEndpoint = "/futures/data/takerlongshortRatio";
    private final String basisEndpoint = "/futures/data/basis";
    private final String fundingRateEndpoint = "/fapi/v1/fundingRate";
    private final String apiTradingStatusEndpoint = "/fapi/v1/apiTradingStatus";
    private final String lvtKlinesEndpoint = "/fapi/v1/lvtKlines";
    private final String indexInfoEndpoint = "/fapi/v1/indexInfo";
    private final String assetIndexEndpoint = "/fapi/v1/assetIndex";

    // Account
    private final String positionSideDualEndpoint = "/fapi/v1/positionSide/dual";
    private final String multiAssetsMarginEndpoint = "/fapi/v1/multiAssetsMargin";
    private final String newOrderEndpoint = "/fapi/v1/order";
    private final String newBatchOrderEndpoint = "/fapi/v1/batchOrders";
    private final String getOrderEndpoint = "/fapi/v1/order";
    private final String cancelOrderEndpoint = "/fapi/v1/order";
    private final String cancelAllOpenOrdersEndpoint = "/fapi/v1/allOpenOrders";
    private final String cancelBatchOrderEndpoint = "/fapi/v1/batchOrders";
    private final String autoCancelAllOpenOrdersEndpoint = "/fapi/v1/countdown";
    private final String getCurrentOpenOrderEndpoint = "/fapi/v1/openOrder";
    private final String getAllOpenOrdersEndpoint = "/fapi/v1/openOrders";
    private final String getAllOrdersEndpoint = "/fapi/v1/allOrders";
    private final String balanceEndpoint = "/fapi/v2/balance";
    private final String accountEndpoint = "/fapi/v2/account";
    private final String changeInitialLeverageEndpoint = "/fapi/v1/leverage";
    private final String changeMarginTypeEndpoint = "/fapi/v1/marginType";
    private final String modifyIsolatedPositionMarginEndpoint = "/fapi/v1/positionMargin";
    private final String getPositionMarginHistoryEndpoint = "/fapi/v1/positionMargin/history";
    private final String getPositionRiskEndpoint = "/fapi/v2/positionRisk";
    private final String getUserTradesEndpoint = "/fapi/v1/userTrades";
    private final String getIncomeHistoryEndpoint = "/fapi/v1/income";
    private final String notionalBracketEndpoint = "/fapi/v1/leverageBracket";
    private final String adlQuantileEndpoint = "/fapi/v1/adlQuantile";
    private final String forceOrdersEndpoint = "/fapi/v1/forceOrders";
    private final String userCommissionRateEndpoint = "/fapi/v1/commissionRate";
    private final String downloadLinkEndpoint = "/fapi/v1/futuresHistDataId";
    private final String transactionHistoryLinkEndpoint = "/fapi/v1/futuresHistDataLink";

    // User Data Stream
    private final String userDataStreamEndpoint = "/fapi/v1/listenKey";

    /**
     * Test connectivity to the Rest API.
     * @return String
     */
    @Override
    public String ping() {
        return publicRequest(pingEndpoint, null, "GET");
    }
    private final String pingEndpoint = "/fapi/v1/ping";

    /**
     * Test connectivity to the Rest API and get the current server time.
     * @return String
     */
    @Override
    public String getServerTime() {
        return publicRequest(serverTimeEndpoint, null, "GET");
    }

    /**
     * Current exchange trading rules and symbol information.
     * @return String
     */
    @Override
    public String getExchangeInfo() {
        return publicRequest(exchangeInfoEndpoint, null, "GET");
    }

    /**
     * @param parameters LinkedHashedMap of String,Object pair
     *                   where String is the name of the parameter and Object is the value of the parameter
     * <br><br>
     * symbol -- mandatory/string <br>
     * limit -- optional/int -- limit the results. Default 500; max 5000 <br>
     * @return String
     */
    @Override
    public String getOrderBook(LinkedHashMap<String, Object> parameters) {
        ParameterChecker.checkParameter(parameters, "symbol", String.class);
        return publicRequest(orderBookEndpoint, parameters, "GET");
    }

    /**
     * Get recent trades.
     * @param parameters LinkedHashedMap of String,Object pair
     *                   where String is the name of the parameter and Object is the value of the parameter
     * <br><br>
     * symbol -- mandatory/string <br>
     * limit -- optional/int -- limit the results. Default 500; max 1000 <br>
     * @return String
     */
    @Override
    public String getRecentTrades(LinkedHashMap<String, Object> parameters) {
        ParameterChecker.checkParameter(parameters, "symbol", String.class);
        return publicRequest(recentTradesEndpoint, parameters, "GET");
    }

    /**
     * Get older market trades.
     * @param parameters LinkedHashedMap of String,Object pair
     *                   where String is the name of the parameter and Object is the value of the parameter
     * <br><br>
     * symbol -- mandatory/string <br>
     * limit -- optional/int -- limit the results. Default 500; max 1000 <br>
     * fromId -- optional/long -- trade id to fetch from. Default gets most recent trades <br>
     * @return String
     */
    @Override
    public String getHistoricalTrades(LinkedHashMap<String, Object> parameters) {
        ParameterChecker.checkParameter(parameters, "symbol", String.class);
        return publicRequest(historicalTradesEndpoint, parameters, "GET");
    }

    /**
     * Get compressed, aggregate trades. Trades that fill at the time, from the same order, with the same price will have the quantity aggregated.
     * @param parameters LinkedHashedMap of String,Object pair
     *                   where String is the name of the parameter and Object is the value of the parameter
     * <br><br>
     * symbol -- mandatory/string <br>
     * fromId -- optional/long -- id to get aggregate trades from INCLUSIVE <br>
     * startTime -- optional/long -- timestamp in ms to get aggregate trades from INCLUSIVE <br>
     * endTime -- optional/long -- timestamp in ms to get aggregate trades until INCLUSIVE <br>
     * limit -- optional/int -- limit the results. Default 500; max 1000 <br>
     * @return String
     */
    @Override
    public String getAggTrades(LinkedHashMap<String, Object> parameters) {
        ParameterChecker.checkParameter(parameters, "symbol", String.class);
        return publicRequest(aggTradesEndpoint, parameters, "GET");
    }

    /**
     * Kline/candlestick bars for a symbol. Klines are uniquelyidentified by their open time.
     * @param parameters LinkedHashedMap of String,Object pair
     *                   where String is the name of the parameter and Object is the value of the parameter
     * <br><br>
     * symbol -- mandatory/string <br>
     * interval -- mandatory/string <br>
     * startTime -- optional/long <br>
     * endTime -- optional/long <br>
     * limit -- optional/int -- limit the results. Default 500; max 1500 <br>
     * @return String
     */
    @Override
    public String getKlines(LinkedHashMap<String, Object> parameters) {
        ParameterChecker.checkParameter(parameters, "symbol", String.class);
        ParameterChecker.checkParameter(parameters, "interval", String.class);
        return publicRequest(klinesEndpoint, parameters, "GET");
    }

    /**
     * Kline/candlestick bars for a specific contract type.
     * @param parameters LinkedHashedMap of String,Object pair
     *                   where String is the name of the parameter and Object is the value of the parameter
     * <br><br>
     * pair -- mandatory/string <br>
     * contractType -- mandatory/string <br>
     * interval -- mandatory/string <br>
     * startTime -- optional/long <br>
     * endTime -- optional/long <br>
     * limit -- optional/int -- limit the results. Default 500; max 1500 <br>
     * @return String
     */
    @Override
    public String getContinuousKlines(LinkedHashMap<String, Object> parameters) {
        ParameterChecker.checkParameter(parameters, "pair", String.class);
        ParameterChecker.checkParameter(parameters, "contractType", String.class);
        ParameterChecker.checkParameter(parameters, "interval", String.class);
        return publicRequest(continuousKlinesEndpoint, parameters, "GET");
    }

    /**
     * Kline/candlestick bars for the index price of a pair.
     * @param parameters LinkedHashedMap of String,Object pair
     *                   where String is the name of the parameter and Object is the value of the parameter
     * <br><br>
     * pair -- mandatory/string <br>
     * interval -- mandatory/string <br>
     * startTime -- optional/long <br>
     * endTime -- optional/long <br>
     * limit -- optional/int -- limit the results. Default 500; max 1500 <br>
     * @return String
     */
    @Override
    public String getIndexPriceKlines(LinkedHashMap<String, Object> parameters) {
        ParameterChecker.checkParameter(parameters, "pair", String.class);
        ParameterChecker.checkParameter(parameters, "interval", String.class);
        return publicRequest(indexPriceKlinesEndpoint, parameters, "GET");
    }

    /**
     * Kline/candlestick bars for the mark price of a symbol.
     * @param parameters LinkedHashedMap of String,Object pair
     *                   where String is the name of the parameter and Object is the value of the parameter
     * <br><br>
     * symbol -- mandatory/string <br>
     * interval -- mandatory/string <br>
     * startTime -- optional/long <br>
     * endTime -- optional/long <br>
     * limit -- optional/int -- limit the results. Default 500; max 1500 <br>
     * @return String
     */
    @Override
    public String getMarkPriceKlines(LinkedHashMap<String, Object> parameters) {
        ParameterChecker.checkParameter(parameters, "symbol", String.class);
        ParameterChecker.checkParameter(parameters, "interval", String.class);
        return publicRequest(markPriceKlinesEndpoint, parameters, "GET");
    }

    /**
     * Mark Price and Funding Rate.
     * @param parameters LinkedHashedMap of String,Object pair
     *                   where String is the name of the parameter and Object is the value of the parameter
     * <br><br>
     * symbol -- optional/string <br>
     * @return String
     */
    @Override
    public String getFundingRate(LinkedHashMap<String, Object> parameters) {
        return publicRequest(fundingRateEndpoint, parameters, "GET");
    }

    /**
     * 24 hour rolling window price change statistics.
     * @param parameters LinkedHashedMap of String,Object pair
     *                   where String is the name of the parameter and Object is the value of the parameter
     * <br><br>
     * symbol -- optional/string <br>
     * @return String
     */
    @Override
    public String getTicker24hr(LinkedHashMap<String, Object> parameters) {
        return publicRequest(ticker24hrEndpoint, parameters, "GET");
    }

    /**
     * Latest price for a symbol or symbols.
     * @param parameters LinkedHashedMap of String,Object pair
     *                   where String is the name of the parameter and Object is the value of the parameter
     * <br><br>
     * symbol -- optional/string <br>
     * @return String
     */
    @Override
    public String getTickerPrice(LinkedHashMap<String, Object> parameters) {
        return publicRequest(tickerPriceEndpoint, parameters, "GET");
    }

    /**
     * Best price/qty on the order book for a symbol or symbols.
     * @param parameters LinkedHashedMap of String,Object pair
     *                   where String is the name of the parameter and Object is the value of the parameter
     * <br><br>
     * symbol -- optional/string <br>
     * @return String
     */
    @Override
    public String getBookTicker(LinkedHashMap<String, Object> parameters) {
        return publicRequest(bookTickerEndpoint, parameters, "GET");
    }

    /**
     * Get present open interest of a specific symbol.
     * @param parameters LinkedHashedMap of String,Object pair
     *                   where String is the name of the parameter and Object is the value of the parameter
     * <br><br>
     * symbol -- mandatory/string <br>
     * @return String
     */
    @Override
    public String getOpenInterest(LinkedHashMap<String, Object> parameters) {
        ParameterChecker.checkParameter(parameters, "symbol", String.class);
        return publicRequest(openInterestEndpoint, parameters, "GET");
    }

    /**
     * @param parameters LinkedHashedMap of String,Object pair
     *                   where String is the name of the parameter and Object is the value of the parameter
     * <br><br>
     * pair -- mandatory/string <br>
     * contractType -- mandatory/string <br>
     * period -- mandatory/string -- "5m","15m","30m","1h","2h","4h","6h","12h","1d" <br>
     * limit -- optional/int -- limit the results. Default 30; max 500 <br>
     * startTime -- optional/long <br>
     * endTime -- optional/long <br>
     * @return String
     */
    @Override
    public String getOpenInterestHist(LinkedHashMap<String, Object> parameters) {
        ParameterChecker.checkParameter(parameters, "pair", String.class);
        ParameterChecker.checkParameter(parameters, "contractType", String.class);
        ParameterChecker.checkParameter(parameters, "period", String.class);
        return publicRequest(openInterestHistEndpoint, parameters, "GET");
    }

    /**
     * @param parameters LinkedHashedMap of String,Object pair
     *                   where String is the name of the parameter and Object is the value of the parameter
     * <br><br>
     * pair -- mandatory/string <br>
     * period -- mandatory/string -- "5m","15m","30m","1h","2h","4h","6h","12h","1d" <br>
     * limit -- optional/int -- limit the results. Default 30; max 500 <br>
     * startTime -- optional/long <br>
     * endTime -- optional/long <br>
     * @return String
     */
    @Override
    public String getTopLongShortAccountRatio(LinkedHashMap<String, Object> parameters) {
        ParameterChecker.checkParameter(parameters, "pair", String.class);
        ParameterChecker.checkParameter(parameters, "period", String.class);
        return publicRequest(topLongShortAccountRatioEndpoint, parameters, "GET");
    }

    /**
     * @param parameters LinkedHashedMap of String,Object pair
     *                   where String is the name of the parameter and Object is the value of the parameter
     * <br><br>
     * pair -- mandatory/string <br>
     * period -- mandatory/string -- "5m","15m","30m","1h","2h","4h","6h","12h","1d" <br>
     * limit -- optional/int -- limit the results. Default 30; max 500 <br>
     * startTime -- optional/long <br>
     * endTime -- optional/long <br>
     * @return String
     */
    @Override
    public String getTopLongShortPositionRatio(LinkedHashMap<String, Object> parameters) {
        ParameterChecker.checkParameter(parameters, "pair", String.class);
        ParameterChecker.checkParameter(parameters, "period", String.class);
        return publicRequest(topLongShortPositionRatioEndpoint, parameters, "GET");
    }

    /**
     * @param parameters LinkedHashedMap of String,Object pair
     *                   where String is the name of the parameter and Object is the value of the parameter
     * <br><br>
     * pair -- mandatory/string <br>
     * period -- mandatory/string -- "5m","15m","30m","1h","2h","4h","6h","12h","1d" <br>
     * limit -- optional/int -- limit the results. Default 30; max 500 <br>
     * startTime -- optional/long <br>
     * endTime -- optional/long <br>
     * @return String
     */
    @Override
    public String getGlobalLongShortAccountRatio(LinkedHashMap<String, Object> parameters) {
        ParameterChecker.checkParameter(parameters, "pair", String.class);
        ParameterChecker.checkParameter(parameters, "period", String.class);
        return publicRequest(globalLongShortAccountRatioEndpoint, parameters, "GET");
    }

    /**
     * @param parameters LinkedHashedMap of String,Object pair
     *                   where String is the name of the parameter and Object is the value of the parameter
     * <br><br>
     * pair -- mandatory/string <br>
     * period -- mandatory/string -- "5m","15m","30m","1h","2h","4h","6h","12h","1d" <br>
     * limit -- optional/int -- limit the results. Default 30; max 500 <br>
     * startTime -- optional/long <br>
     * endTime -- optional/long <br>
     * @return String
     */
    @Override
    public String getTakerBuySellVol(LinkedHashMap<String, Object> parameters) {
        ParameterChecker.checkParameter(parameters, "pair", String.class);
        ParameterChecker.checkParameter(parameters, "period", String.class);
        return publicRequest(takerBuySellVolEndpoint, parameters, "GET");
    }

    /**
     * @param parameters LinkedHashedMap of String,Object pair
     *                   where String is the name of the parameter and Object is the value of the parameter
     * <br><br>
     * pair -- mandatory/string <br>
     * contractType -- mandatory/string <br>
     * period -- mandatory/string -- "5m","15m","30m","1h","2h","4h","6h","12h","1d" <br>
     * limit -- optional/int -- limit the results. Default 30; max 500 <br>
     * startTime -- optional/long <br>
     * endTime -- optional/long <br>
     * @return String
     */
    @Override
    public String getBasis(LinkedHashMap<String, Object> parameters) {
        ParameterChecker.checkParameter(parameters, "pair", String.class);
        ParameterChecker.checkParameter(parameters, "contractType", String.class);
        ParameterChecker.checkParameter(parameters, "period", String.class);
        return publicRequest(basisEndpoint, parameters, "GET");
    }

    /**
     * @param parameters LinkedHashedMap of String,Object pair
     *                   where String is the name of the parameter and Object is the value of the parameter
     * <br><br>
     * symbol -- optional/string <br>
     * recvWindow -- optional/long <br>
     * @return String
     */
    @Override
    public String getApiTradingStatus(LinkedHashMap<String, Object> parameters) {
        return signedRequest(apiTradingStatusEndpoint, parameters, "GET");
    }

    /**
     * Get LVT Klines
     * @param parameters LinkedHashedMap of String,Object pair
     *                   where String is the name of the parameter and Object is the value of the parameter
     * <br><br>
     * symbol -- mandatory/string <br>
     * interval -- mandatory/string <br>
     * startTime -- optional/long <br>
     * endTime -- optional/long <br>
     * limit -- optional/int -- limit the results. Default 500; max 1000 <br>
     * @return String
     */
    @Override
    public String getLvtKlines(LinkedHashMap<String, Object> parameters) {
        ParameterChecker.checkParameter(parameters, "symbol", String.class);
        ParameterChecker.checkParameter(parameters, "interval", String.class);
        return publicRequest(lvtKlinesEndpoint, parameters, "GET");
    }

    /**
     * Get Index Info
     * @param parameters LinkedHashedMap of String,Object pair
     *                   where String is the name of the parameter and Object is the value of the parameter
     * <br><br>
     * symbol -- optional/string <br>
     * @return String
     */
    @Override
    public String getIndexInfo(LinkedHashMap<String, Object> parameters) {
        return publicRequest(indexInfoEndpoint, parameters, "GET");
    }

    /**
     * Get Asset Index
     * @param parameters LinkedHashedMap of String,Object pair
     *                   where String is the name of the parameter and Object is the value of the parameter
     * <br><br>
     * symbol -- optional/string <br>
     * @return String
     */
    @Override
    public String getAssetIndex(LinkedHashMap<String, Object> parameters) {
        return publicRequest(assetIndexEndpoint, parameters, "GET");
    }

    /**
     * Change user's position mode (Hedge Mode or One-way Mode ) on EVERY symbol
     * @param parameters LinkedHashedMap of String,Object pair
     *                   where String is the name of the parameter and Object is the value of the parameter
     * <br><br>
     * dualSidePosition -- mandatory/string -- "true": Hedge Mode; "false": One-way Mode <br>
     * recvWindow -- optional/long <br>
     * @return String
     */
    @Override
    public String changePositionSideDual(LinkedHashMap<String, Object> parameters) {
        ParameterChecker.checkParameter(parameters, "dualSidePosition", String.class);
        return signedRequest(positionSideDualEndpoint, parameters, "POST");
    }

    /**
     * Get user's position mode (Hedge Mode or One-way Mode ) on EVERY symbol
     * @param parameters LinkedHashedMap of String,Object pair
     *                   where String is the name of the parameter and Object is the value of the parameter
     * <br><br>
     * recvWindow -- optional/long <br>
     * @return String
     */
    @Override
    public String getCurrentPositionSideDual(LinkedHashMap<String, Object> parameters) {
        return signedRequest(positionSideDualEndpoint, parameters, "GET");
    }

    /**
     * Change user's Multi-Assets mode (Multi-Assets Mode or Single-Asset Mode) on Every symbol
     * @param parameters LinkedHashedMap of String,Object pair
     *                   where String is the name of the parameter and Object is the value of the parameter
     * <br><br>
     * multiAssetsMargin -- mandatory/string -- "true": Multi-Assets Mode; "false": Single-Asset Mode <br>
     * recvWindow -- optional/long <br>
     * @return String
     */
    @Override
    public String changeMultiAssetsMargin(LinkedHashMap<String, Object> parameters) {
        ParameterChecker.checkParameter(parameters, "multiAssetsMargin", String.class);
        return signedRequest(multiAssetsMarginEndpoint, parameters, "POST");
    }

    /**
     * Get user's Multi-Assets mode (Multi-Assets Mode or Single-Asset Mode) on Every symbol
     * @param parameters LinkedHashedMap of String,Object pair
     *                   where String is the name of the parameter and Object is the value of the parameter
     * <br><br>
     * recvWindow -- optional/long <br>
     * @return String
     */
    @Override
    public String getCurrentMultiAssetsMargin(LinkedHashMap<String, Object> parameters) {
        return signedRequest(multiAssetsMarginEndpoint, parameters, "GET");
    }

    /**
     * Send in a new order.
     * @param parameters LinkedHashedMap of String,Object pair
     *                   where String is the name of the parameter and Object is the value of the parameter
     * <br><br>
     * symbol -- mandatory/string <br>
     * side -- mandatory/string <br>
     * positionSide -- optional/string -- Default BOTH for One-way Mode ; LONG or SHORT for Hedge Mode. It must be sent with Hedge Mode. <br>
     * type -- mandatory/string <br>
     * timeInForce -- optional/string <br>
     * quantity -- optional/decimal <br>
     * reduceOnly -- optional/string -- "true" or "false". default "false". Cannot be sent in Hedge Mode; cannot be sent with closePosition=true <br>
     * price -- optional/decimal <br>
     * newClientOrderId -- optional/string -- A unique id among open orders. Automatically generated if not sent. Can only be string following the rule: ^[\.A-Z\-0-9]{1,36}$ <br>
     * stopPrice -- optional/decimal -- Used with STOP/STOP_MARKET or TAKE_PROFIT/TAKE_PROFIT_MARKET orders. <br>
     * closePosition -- optional/string -- true, false；Close-All，used with STOP_MARKET or TAKE_PROFIT_MARKET. <br>
     * activationPrice -- optional/decimal -- Used with TRAILING_STOP_MARKET orders, default as the latest price（supporting different טrites areas） <br>
     * callbackRate -- optional/decimal -- Used with TRAILING_STOP_MARKET orders, min 0.1, max 5 precision 1 <br>
     * workingType -- optional/string -- stopPrice triggered by: "MARK_PRICE", "CONTRACT_PRICE". Default "CONTRACT_PRICE" <br>
     * priceProtect -- optional/string -- "TRUE" or "FALSE", default "FALSE". Used with STOP/STOP_MARKET or TAKE_PROFIT/TAKE_PROFIT_MARKET orders. <br>
     * newOrderRespType -- optional/string -- "ACK", "RESULT", default "ACK" <br>
     * recvWindow -- optional/long <br>
     * @return String
     */
    @Override
    public String newOrder(LinkedHashMap<String, Object> parameters) {
        ParameterChecker.checkParameter(parameters, "symbol", String.class);
        ParameterChecker.checkRequiredParameter(parameters, "side");
        ParameterChecker.checkRequiredParameter(parameters, "type");
        return signedRequest(newOrderEndpoint, parameters, "POST");
    }

    /**
     * Send in a new batch order.
     * @param parameters LinkedHashedMap of String,Object pair
     *                   where String is the name of the parameter and Object is the value of the parameter
     * <br><br>
     * batchOrders -- mandatory/list -- max 5 <br>
     * recvWindow -- optional/long <br>
     * @return String
     */
    @Override
    public String newBatchOrder(LinkedHashMap<String, Object> parameters) {
        ParameterChecker.checkRequiredParameter(parameters, "batchOrders");
        return signedRequest(newBatchOrderEndpoint, parameters, "POST");
    }

    /**
     * Check an order's status.
     * @param parameters LinkedHashedMap of String,Object pair
     *                   where String is the name of the parameter and Object is the value of the parameter
     * <br><br>
     * symbol -- mandatory/string <br>
     * orderId -- optional/long <br>
     * origClientOrderId -- optional/string <br>
     * recvWindow -- optional/long <br>
     * @return String
     */
    @Override
    public String getOrder(LinkedHashMap<String, Object> parameters) {
        ParameterChecker.checkParameter(parameters, "symbol", String.class);
        return signedRequest(getOrderEndpoint, parameters, "GET");
    }

    /**
     * Cancel an active order.
     * @param parameters LinkedHashedMap of String,Object pair
     *                   where String is the name of the parameter and Object is the value of the parameter
     * <br><br>
     * symbol -- mandatory/string <br>
     * orderId -- optional/long <br>
     * origClientOrderId -- optional/string <br>
     * recvWindow -- optional/long <br>
     * @return String
     */
    @Override
    public String cancelOrder(LinkedHashMap<String, Object> parameters) {
        ParameterChecker.checkParameter(parameters, "symbol", String.class);
        return signedRequest(cancelOrderEndpoint, parameters, "DELETE");
    }

    /**
     * Cancel all open orders.
     * @param parameters LinkedHashedMap of String,Object pair
     *                   where String is the name of the parameter and Object is the value of the parameter
     * <br><br>
     * symbol -- mandatory/string <br>
     * recvWindow -- optional/long <br>
     * @return String
     */
    @Override
    public String cancelAllOpenOrders(LinkedHashMap<String, Object> parameters) {
        ParameterChecker.checkParameter(parameters, "symbol", String.class);
        return signedRequest(cancelAllOpenOrdersEndpoint, parameters, "DELETE");
    }

    /**
     * @param parameters LinkedHashedMap of String,Object pair
     *                   where String is the name of the parameter and Object is the value of the parameter
     * <br><br>
     * symbol -- mandatory/string <br>
     * orderIdList -- optional/list -- max length 10 e.g. [1234567,2345678] <br>
     * origClientOrderIdList -- optional/list -- max length 10 e.g. ["myOrder1","myOrder2"]. As an alternative to orderIdList. <br>
     * recvWindow -- optional/long <br>
     * @return String
     */
    @Override
    public String cancelBatchOrder(LinkedHashMap<String, Object> parameters) {
        ParameterChecker.checkParameter(parameters, "symbol", String.class);
        return signedRequest(cancelBatchOrderEndpoint, parameters, "DELETE");
    }

    /**
     * Countdown cancel all orders.
     * @param parameters LinkedHashedMap of String,Object pair
     *                   where String is the name of the parameter and Object is the value of the parameter
     * <br><br>
     * symbol -- mandatory/string <br>
     * countdownTime -- mandatory/long -- millisecond. 0 means cancel immediately. Max 300000. <br>
     * recvWindow -- optional/long <br>
     * @return String
     */
    @Override
    public String autoCancelAllOpenOrders(LinkedHashMap<String, Object> parameters) {
        ParameterChecker.checkParameter(parameters, "symbol", String.class);
        ParameterChecker.checkRequiredParameter(parameters, "countdownTime");
        return signedRequest(autoCancelAllOpenOrdersEndpoint, parameters, "POST");
    }

    /**
     * Query current open order.
     * @param parameters LinkedHashedMap of String,Object pair
     *                   where String is the name of the parameter and Object is the value of the parameter
     * <br><br>
     * symbol -- mandatory/string <br>
     * orderId -- optional/long <br>
     * origClientOrderId -- optional/string <br>
     * recvWindow -- optional/long <br>
     * @return String
     */
    @Override
    public String getCurrentOpenOrder(LinkedHashMap<String, Object> parameters) {
        ParameterChecker.checkParameter(parameters, "symbol", String.class);
        return signedRequest(getCurrentOpenOrderEndpoint, parameters, "GET");
    }

    /**
     * Get all open orders on a symbol. Careful when accessing this with no symbol.
     * @param parameters LinkedHashedMap of String,Object pair
     *                   where String is the name of the parameter and Object is the value of the parameter
     * <br><br>
     * symbol -- optional/string <br>
     * recvWindow -- optional/long <br>
     * @return String
     */
    @Override
    public String getAllOpenOrders(LinkedHashMap<String, Object> parameters) {
        return signedRequest(getAllOpenOrdersEndpoint, parameters, "GET");
    }

    /**
     * Get all account orders; active, canceled, or filled.
     * @param parameters LinkedHashedMap of String,Object pair
     *                   where String is the name of the parameter and Object is the value of the parameter
     * <br><br>
     * symbol -- mandatory/string <br>
     * orderId -- optional/long <br>
     * startTime -- optional/long <br>
     * endTime -- optional/long <br>
     * limit -- optional/int -- limit the results. Default 500; max 1000 <br>
     * recvWindow -- optional/long <br>
     * @return String
     */
    @Override
    public String getAllOrders(LinkedHashMap<String, Object> parameters) {
        ParameterChecker.checkParameter(parameters, "symbol", String.class);
        return signedRequest(getAllOrdersEndpoint, parameters, "GET");
    }

    /**
     * Get current account balance.
     * @param parameters LinkedHashedMap of String,Object pair
     *                   where String is the name of the parameter and Object is the value of the parameter
     * <br><br>
     * recvWindow -- optional/long <br>
     * @return String
     */
    @Override
    public String getBalance(LinkedHashMap<String, Object> parameters) {
        return signedRequest(balanceEndpoint, parameters, "GET");
    }

    /**
     * Get current account information.
     * @param parameters LinkedHashedMap of String,Object pair
     *                   where String is the name of the parameter and Object is the value of the parameter
     * <br><br>
     * recvWindow -- optional/long <br>
     * @return String
     */
    @Override
    public String getAccount(LinkedHashMap<String, Object> parameters) {
        return signedRequest(accountEndpoint, parameters, "GET");
    }

    /**
     * Change initial leverage.
     * @param parameters LinkedHashedMap of String,Object pair
     *                   where String is the name of the parameter and Object is the value of the parameter
     * <br><br>
     * symbol -- mandatory/string <br>
     * leverage -- mandatory/int -- target initial leverage: int from 1 to 125 <br>
     * recvWindow -- optional/long <br>
     * @return String
     */
    @Override
    public String changeInitialLeverage(LinkedHashMap<String, Object> parameters) {
        ParameterChecker.checkParameter(parameters, "symbol", String.class);
        ParameterChecker.checkRequiredParameter(parameters, "leverage");
        return signedRequest(changeInitialLeverageEndpoint, parameters, "POST");
    }

    /**
     * Change margin type.
     * @param parameters LinkedHashedMap of String,Object pair
     *                   where String is the name of the parameter and Object is the value of the parameter
     * <br><br>
     * symbol -- mandatory/string <br>
     * marginType -- mandatory/string -- ISOLATED, CROSSED <br>
     * recvWindow -- optional/long <br>
     * @return String
     */
    @Override
    public String changeMarginType(LinkedHashMap<String, Object> parameters) {
        ParameterChecker.checkParameter(parameters, "symbol", String.class);
        ParameterChecker.checkRequiredParameter(parameters, "marginType");
        return signedRequest(changeMarginTypeEndpoint, parameters, "POST");
    }

    /**
     * Modify isolated position margin.
     * @param parameters LinkedHashedMap of String,Object pair
     *                   where String is the name of the parameter and Object is the value of the parameter
     * <br><br>
     * symbol -- mandatory/string <br>
     * positionSide -- optional/string -- Default BOTH for One-way Mode ; LONG or SHORT for Hedge Mode. It must be sent with Hedge Mode. <br>
     * amount -- mandatory/decimal <br>
     * type -- mandatory/int -- 1: Add position margin，2: Reduce position margin <br>
     * recvWindow -- optional/long <br>
     * @return String
     */
    @Override
    public String modifyIsolatedPositionMargin(LinkedHashMap<String, Object> parameters) {
        ParameterChecker.checkParameter(parameters, "symbol", String.class);
        ParameterChecker.checkRequiredParameter(parameters, "amount");
        ParameterChecker.checkRequiredParameter(parameters, "type");
        return signedRequest(modifyIsolatedPositionMarginEndpoint, parameters, "POST");
    }

    /**
     * Get position margin change history.
     * @param parameters LinkedHashedMap of String,Object pair
     *                   where String is the name of the parameter and Object is the value of the parameter
     * <br><br>
     * symbol -- mandatory/string <br>
     * type -- optional/int -- 1: Add position margin，2: Reduce position margin <br>
     * startTime -- optional/long <br>
     * endTime -- optional/long <br>
     * limit -- optional/int -- limit the results. Default 500 <br>
     * recvWindow -- optional/long <br>
     * @return String
     */
    @Override
    public String getPositionMarginHistory(LinkedHashMap<String, Object> parameters) {
        ParameterChecker.checkParameter(parameters, "symbol", String.class);
        return signedRequest(getPositionMarginHistoryEndpoint, parameters, "GET");
    }

    /**
     * Get current position information.
     * @param parameters LinkedHashedMap of String,Object pair
     *                   where String is the name of the parameter and Object is the value of the parameter
     * <br><br>
     * symbol -- optional/string <br>
     * recvWindow -- optional/long <br>
     * @return String
     */
    @Override
    public String getPositionRisk(LinkedHashMap<String, Object> parameters) {
        return signedRequest(getPositionRiskEndpoint, parameters, "GET");
    }

    /**
     * Get trades for a specific account and symbol.
     * @param parameters LinkedHashedMap of String,Object pair
     *                   where String is the name of the parameter and Object is the value of the parameter
     * <br><br>
     * symbol -- mandatory/string <br>
     * startTime -- optional/long <br>
     * endTime -- optional/long <br>
     * fromId -- optional/long -- trade id to fetch from. Default gets most recent trades <br>
     * limit -- optional/int -- limit the results. Default 500; max 1000 <br>
     * recvWindow -- optional/long <br>
     * @return String
     */
    @Override
    public String getUserTrades(LinkedHashMap<String, Object> parameters) {
        ParameterChecker.checkParameter(parameters, "symbol", String.class);
        return signedRequest(getUserTradesEndpoint, parameters, "GET");
    }

    /**
     * Get income history.
     * @param parameters LinkedHashedMap of String,Object pair
     *                   where String is the name of the parameter and Object is the value of the parameter
     * <br><br>
     * symbol -- optional/string <br>
     * incomeType -- optional/string -- "TRANSFER", "WELCOME_BONUS", "REALIZED_PNL", "FUNDING_FEE", "COMMISSION", or "INSURANCE_CLEAR" <br>
     * startTime -- optional/long <br>
     * endTime -- optional/long <br>
     * limit -- optional/int -- limit the results. Default 100; max 1000 <br>
     * recvWindow -- optional/long <br>
     * @return String
     */
    @Override
    public String getIncomeHistory(LinkedHashMap<String, Object> parameters) {
        return signedRequest(getIncomeHistoryEndpoint, parameters, "GET");
    }

    /**
     * Notional and Leverage Brackets.
     * @param parameters LinkedHashedMap of String,Object pair
     *                   where String is the name of the parameter and Object is the value of the parameter
     * <br><br>
     * symbol -- optional/string <br>
     * recvWindow -- optional/long <br>
     * @return String
     */
    @Override
    public String getNotionalBracket(LinkedHashMap<String, Object> parameters) {
        return signedRequest(notionalBracketEndpoint, parameters, "GET");
    }

    /**
     * Position ADL Quantile Estimations.
     * @param parameters LinkedHashedMap of String,Object pair
     *                   where String is the name of the parameter and Object is the value of the parameter
     * <br><br>
     * symbol -- optional/string <br>
     * recvWindow -- optional/long <br>
     * @return String
     */
    @Override
    public String getAdlQuantile(LinkedHashMap<String, Object> parameters) {
        return signedRequest(adlQuantileEndpoint, parameters, "GET");
    }

    /**
     * User's Force Orders.
     * @param parameters LinkedHashedMap of String,Object pair
     *                   where String is the name of the parameter and Object is the value of the parameter
     * <br><br>
     * symbol -- optional/string <br>
     * autoCloseType -- optional/string -- "LIQUIDATION", "ADL" <br>
     * startTime -- optional/long <br>
     * endTime -- optional/long <br>
     * limit -- optional/int -- limit the results. Default 50; max 100 <br>
     * recvWindow -- optional/long <br>
     * @return String
     */
    @Override
    public String getForceOrders(LinkedHashMap<String, Object> parameters) {
        return signedRequest(forceOrdersEndpoint, parameters, "GET");
    }

    /**
     * User's current commission rate for a symbol.
     * @param parameters LinkedHashedMap of String,Object pair
     *                   where String is the name of the parameter and Object is the value of the parameter
     * <br><br>
     * symbol -- mandatory/string <br>
     * recvWindow -- optional/long <br>
     * @return String
     */
    @Override
    public String getUserCommissionRate(LinkedHashMap<String, Object> parameters) {
        ParameterChecker.checkParameter(parameters, "symbol", String.class);
        return signedRequest(userCommissionRateEndpoint, parameters, "GET");
    }

    /**
     * Download Id Create.
     * @param parameters LinkedHashedMap of String,Object pair
     *                   where String is the name of the parameter and Object is the value of the parameter
     * <br><br>
     * recvWindow -- optional/long <br>
     * @return String
     */
    @Override
    public String getDownloadLink(LinkedHashMap<String, Object> parameters) {
        return signedRequest(downloadLinkEndpoint, parameters, "GET");
    }

    /**
     * Get Transaction History Download Link by Id.
     * @param parameters LinkedHashedMap of String,Object pair
     *                   where String is the name of the parameter and Object is the value of the parameter
     * <br><br>
     * downloadId -- mandatory/string <br>
     * recvWindow -- optional/long <br>
     * @return String
     */
    @Override
    public String getTransactionHistoryLink(LinkedHashMap<String, Object> parameters) {
        ParameterChecker.checkRequiredParameter(parameters, "downloadId");
        return signedRequest(transactionHistoryLinkEndpoint, parameters, "GET");
    }

}
