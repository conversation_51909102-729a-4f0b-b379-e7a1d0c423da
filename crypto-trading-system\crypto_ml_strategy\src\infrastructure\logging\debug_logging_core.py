"""
Crypto ML Strategy - 调试日志系统核心组件

该模块实现了调试日志系统的核心功能，包括调试模式管理、
函数调用跟踪和变量状态检查。

主要功能：
- DebugLogger: 调试日志系统
- DebugModeManager: 调试模式管理器
- TraceLogger: 函数调用跟踪日志器
- VariableInspector: 变量状态检查器

作者: Crypto ML Strategy Team
日期: 2024-01-20
版本: 1.0.0
"""

import functools
import inspect
import sys
import threading
import traceback
from contextlib import contextmanager
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional, Union, Callable, Set
from loguru import logger
import json


class DebugLevel(Enum):
    """调试级别"""
    NONE = 0
    BASIC = 1
    DETAILED = 2
    VERBOSE = 3


@dataclass
class DebugContext:
    """调试上下文"""
    trace_id: str
    span_id: str
    request_id: str
    session_id: str
    user_id: str
    debug_level: DebugLevel
    start_time: datetime
    variables: Dict[str, Any] = field(default_factory=dict)
    call_stack: List[str] = field(default_factory=list)
    custom_data: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "trace_id": self.trace_id,
            "span_id": self.span_id,
            "request_id": self.request_id,
            "session_id": self.session_id,
            "user_id": self.user_id,
            "debug_level": self.debug_level.name,
            "start_time": self.start_time.isoformat(),
            "variables": self._serialize_variables(),
            "call_stack": self.call_stack,
            "custom_data": self.custom_data
        }
    
    def _serialize_variables(self) -> Dict[str, str]:
        """序列化变量（安全处理）"""
        serialized = {}
        for key, value in self.variables.items():
            try:
                if isinstance(value, (str, int, float, bool, type(None))):
                    serialized[key] = value
                elif isinstance(value, (list, tuple, dict)):
                    serialized[key] = str(value)[:500]  # 限制长度
                else:
                    serialized[key] = f"<{type(value).__name__}>"
            except Exception:
                serialized[key] = "<serialization_error>"
        
        return serialized


class DebugModeManager:
    """调试模式管理器"""
    
    def __init__(self):
        self._debug_level = DebugLevel.NONE
        self._enabled_modules: Set[str] = set()
        self._disabled_modules: Set[str] = set()
        self._debug_filters: List[Callable[[str], bool]] = []
        self._lock = threading.RLock()
    
    def set_debug_level(self, level: DebugLevel) -> None:
        """设置全局调试级别"""
        with self._lock:
            self._debug_level = level
            logger.info(f"Debug level set to: {level.name}")
    
    def get_debug_level(self) -> DebugLevel:
        """获取当前调试级别"""
        with self._lock:
            return self._debug_level
    
    def enable_module(self, module_name: str) -> None:
        """启用模块调试"""
        with self._lock:
            self._enabled_modules.add(module_name)
            self._disabled_modules.discard(module_name)
            logger.debug(f"Debug enabled for module: {module_name}")
    
    def disable_module(self, module_name: str) -> None:
        """禁用模块调试"""
        with self._lock:
            self._disabled_modules.add(module_name)
            self._enabled_modules.discard(module_name)
            logger.debug(f"Debug disabled for module: {module_name}")
    
    def is_debug_enabled(self, module_name: str) -> bool:
        """检查模块是否启用调试"""
        with self._lock:
            # 如果全局调试级别为NONE，则不启用
            if self._debug_level == DebugLevel.NONE:
                return False
            
            # 如果模块被明确禁用，则不启用
            if module_name in self._disabled_modules:
                return False
            
            # 如果有启用的模块列表，检查是否在列表中
            if self._enabled_modules:
                return module_name in self._enabled_modules
            
            # 应用过滤器
            for filter_func in self._debug_filters:
                if not filter_func(module_name):
                    return False
            
            return True
    
    def add_filter(self, filter_func: Callable[[str], bool]) -> None:
        """添加调试过滤器"""
        with self._lock:
            self._debug_filters.append(filter_func)
    
    def clear_filters(self) -> None:
        """清除所有过滤器"""
        with self._lock:
            self._debug_filters.clear()
    
    def get_status(self) -> Dict[str, Any]:
        """获取调试状态"""
        with self._lock:
            return {
                "debug_level": self._debug_level.name,
                "enabled_modules": list(self._enabled_modules),
                "disabled_modules": list(self._disabled_modules),
                "filter_count": len(self._debug_filters)
            }


class VariableInspector:
    """变量状态检查器"""
    
    def __init__(self, max_depth: int = 3, max_length: int = 1000):
        self.max_depth = max_depth
        self.max_length = max_length
        self._sensitive_names = {
            'password', 'passwd', 'secret', 'token', 'key', 'api_key',
            'access_token', 'refresh_token', 'private_key', 'credential'
        }
    
    def inspect_frame(self, frame) -> Dict[str, Any]:
        """检查栈帧中的变量"""
        try:
            variables = {}
            
            # 检查局部变量
            for name, value in frame.f_locals.items():
                if not name.startswith('_'):  # 跳过私有变量
                    variables[f"local.{name}"] = self._safe_inspect_value(name, value)
            
            # 检查全局变量（仅限重要的）
            for name, value in frame.f_globals.items():
                if (not name.startswith('_') and 
                    name in ['config', 'settings', 'state', 'context']):
                    variables[f"global.{name}"] = self._safe_inspect_value(name, value)
            
            return variables
            
        except Exception as e:
            logger.error(f"Failed to inspect frame: {e}")
            return {"error": "frame_inspection_failed"}
    
    def inspect_variables(self, var_dict: Dict[str, Any]) -> Dict[str, Any]:
        """检查变量字典"""
        try:
            result = {}
            
            for name, value in var_dict.items():
                result[name] = self._safe_inspect_value(name, value)
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to inspect variables: {e}")
            return {"error": "variable_inspection_failed"}
    
    def _safe_inspect_value(self, name: str, value: Any, depth: int = 0) -> Any:
        """安全地检查变量值"""
        try:
            # 检查深度限制
            if depth > self.max_depth:
                return f"<max_depth_exceeded:{type(value).__name__}>"
            
            # 检查敏感变量
            if any(sensitive in name.lower() for sensitive in self._sensitive_names):
                return "***REDACTED***"
            
            # 基础类型
            if isinstance(value, (str, int, float, bool, type(None))):
                if isinstance(value, str) and len(value) > self.max_length:
                    return value[:self.max_length] + "..."
                return value
            
            # 列表和元组
            elif isinstance(value, (list, tuple)):
                if len(value) > 10:  # 限制元素数量
                    inspected = [
                        self._safe_inspect_value(f"{name}[{i}]", item, depth + 1)
                        for i, item in enumerate(value[:10])
                    ]
                    inspected.append(f"... and {len(value) - 10} more items")
                    return inspected
                else:
                    return [
                        self._safe_inspect_value(f"{name}[{i}]", item, depth + 1)
                        for i, item in enumerate(value)
                    ]
            
            # 字典
            elif isinstance(value, dict):
                if len(value) > 10:  # 限制键数量
                    keys = list(value.keys())[:10]
                    inspected = {
                        key: self._safe_inspect_value(f"{name}.{key}", value[key], depth + 1)
                        for key in keys
                    }
                    inspected["..."] = f"and {len(value) - 10} more keys"
                    return inspected
                else:
                    return {
                        key: self._safe_inspect_value(f"{name}.{key}", val, depth + 1)
                        for key, val in value.items()
                    }
            
            # 对象属性
            elif hasattr(value, '__dict__'):
                obj_dict = {}
                for attr_name in dir(value):
                    if (not attr_name.startswith('_') and 
                        not callable(getattr(value, attr_name, None))):
                        try:
                            attr_value = getattr(value, attr_name)
                            obj_dict[attr_name] = self._safe_inspect_value(
                                f"{name}.{attr_name}", attr_value, depth + 1
                            )
                        except Exception:
                            obj_dict[attr_name] = "<access_error>"
                        
                        if len(obj_dict) >= 5:  # 限制属性数量
                            break
                
                return f"<{type(value).__name__}>: {obj_dict}"
            
            # 其他类型
            else:
                return f"<{type(value).__name__}>"
                
        except Exception:
            return f"<inspection_error:{type(value).__name__}>"


class TraceLogger:
    """函数调用跟踪日志器"""
    
    def __init__(self, debug_mode_manager: DebugModeManager):
        self.debug_mode_manager = debug_mode_manager
        self.variable_inspector = VariableInspector()
        self._trace_stack: Dict[int, List[str]] = {}
        self._lock = threading.RLock()
    
    def trace_function(self, func: Optional[Callable] = None, *,
                      include_args: bool = True,
                      include_locals: bool = False,
                      include_result: bool = True):
        """函数跟踪装饰器"""
        def decorator(f: Callable) -> Callable:
            @functools.wraps(f)
            def wrapper(*args, **kwargs):
                return self._trace_sync_function(
                    f, args, kwargs, include_args, include_locals, include_result
                )
            
            return wrapper
        
        if func is None:
            return decorator
        else:
            return decorator(func)
    
    def _trace_sync_function(self, func: Callable, args: tuple, kwargs: dict,
                           include_args: bool, include_locals: bool,
                           include_result: bool) -> Any:
        """跟踪同步函数"""
        module_name = func.__module__
        function_name = f"{module_name}.{func.__qualname__}"
        
        # 检查是否启用调试
        if not self.debug_mode_manager.is_debug_enabled(module_name):
            return func(*args, **kwargs)
        
        thread_id = threading.current_thread().ident
        
        # 记录函数进入
        self._log_function_entry(
            function_name, args if include_args else None,
            kwargs if include_args else None, thread_id
        )
        
        try:
            # 记录局部变量（如果启用）
            if include_locals:
                frame = inspect.currentframe()
                if frame:
                    variables = self.variable_inspector.inspect_frame(frame)
                    self._log_variables(function_name, variables, "entry")
            
            # 执行函数
            result = func(*args, **kwargs)
            
            # 记录函数退出
            self._log_function_exit(
                function_name, result if include_result else None, thread_id
            )
            
            return result
            
        except Exception as e:
            # 记录异常
            self._log_function_exception(function_name, e, thread_id)
            raise
        finally:
            # 清理跟踪栈
            self._cleanup_trace_stack(thread_id, function_name)
    
    def _log_function_entry(self, function_name: str, args: Optional[tuple],
                           kwargs: Optional[dict], thread_id: int) -> None:
        """记录函数进入"""
        try:
            with self._lock:
                if thread_id not in self._trace_stack:
                    self._trace_stack[thread_id] = []
                
                self._trace_stack[thread_id].append(function_name)
                depth = len(self._trace_stack[thread_id])
            
            indent = "  " * (depth - 1)
            
            log_data = {
                "trace_event": "function_entry",
                "function_name": function_name,
                "depth": depth,
                "thread_id": thread_id,
                "call_stack": self._trace_stack[thread_id].copy()
            }
            
            if args is not None:
                log_data["args"] = self._safe_serialize_args(args)
            
            if kwargs is not None:
                log_data["kwargs"] = self._safe_serialize_args(kwargs)
            
            logger.bind(**log_data).debug(f"{indent}→ ENTER {function_name}")
            
        except Exception as e:
            logger.error(f"Failed to log function entry: {e}")
    
    def _log_function_exit(self, function_name: str, result: Any, thread_id: int) -> None:
        """记录函数退出"""
        try:
            with self._lock:
                stack = self._trace_stack.get(thread_id, [])
                depth = len(stack)
            
            indent = "  " * max(0, depth - 1)
            
            log_data = {
                "trace_event": "function_exit",
                "function_name": function_name,
                "depth": depth,
                "thread_id": thread_id
            }
            
            if result is not None:
                log_data["result"] = self._safe_serialize_result(result)
            
            logger.bind(**log_data).debug(f"{indent}← EXIT {function_name}")
            
        except Exception as e:
            logger.error(f"Failed to log function exit: {e}")
    
    def _log_function_exception(self, function_name: str, exception: Exception, thread_id: int) -> None:
        """记录函数异常"""
        try:
            with self._lock:
                stack = self._trace_stack.get(thread_id, [])
                depth = len(stack)
            
            indent = "  " * max(0, depth - 1)
            
            log_data = {
                "trace_event": "function_exception",
                "function_name": function_name,
                "depth": depth,
                "thread_id": thread_id,
                "exception_type": type(exception).__name__,
                "exception_message": str(exception),
                "traceback": traceback.format_exc()
            }
            
            logger.bind(**log_data).error(f"{indent}✗ EXCEPTION {function_name}: {exception}")
            
        except Exception as e:
            logger.error(f"Failed to log function exception: {e}")
    
    def _log_variables(self, function_name: str, variables: Dict[str, Any], stage: str) -> None:
        """记录变量状态"""
        try:
            log_data = {
                "trace_event": "variables",
                "function_name": function_name,
                "stage": stage,
                "variables": variables
            }
            
            logger.bind(**log_data).debug(f"Variables at {stage}: {function_name}")
            
        except Exception as e:
            logger.error(f"Failed to log variables: {e}")
    
    def _cleanup_trace_stack(self, thread_id: int, function_name: str) -> None:
        """清理跟踪栈"""
        try:
            with self._lock:
                stack = self._trace_stack.get(thread_id, [])
                if stack and stack[-1] == function_name:
                    stack.pop()
                
                if not stack:
                    self._trace_stack.pop(thread_id, None)
                    
        except Exception as e:
            logger.error(f"Failed to cleanup trace stack: {e}")
    
    def _safe_serialize_args(self, args: Union[tuple, dict]) -> str:
        """安全序列化参数"""
        try:
            if isinstance(args, dict):
                serialized = self.variable_inspector.inspect_variables(args)
            else:
                serialized = [
                    self.variable_inspector._safe_inspect_value(f"arg_{i}", arg)
                    for i, arg in enumerate(args)
                ]
            
            return str(serialized)[:500]  # 限制长度
            
        except Exception:
            return "<serialization_error>"
    
    def _safe_serialize_result(self, result: Any) -> str:
        """安全序列化结果"""
        try:
            serialized = self.variable_inspector._safe_inspect_value("result", result)
            return str(serialized)[:500]  # 限制长度
        except Exception:
            return "<serialization_error>"
    
    def get_current_call_stack(self, thread_id: Optional[int] = None) -> List[str]:
        """获取当前调用栈"""
        if thread_id is None:
            thread_id = threading.current_thread().ident
        
        with self._lock:
            return self._trace_stack.get(thread_id, []).copy()


class DebugLogger:
    """调试日志系统主类"""
    
    def __init__(self):
        self.mode_manager = DebugModeManager()
        self.trace_logger = TraceLogger(self.mode_manager)
        self.variable_inspector = VariableInspector()
        self._debug_logger = logger.bind(name="debug")
        self._lock = threading.RLock()
    
    def set_debug_level(self, level: DebugLevel) -> None:
        """设置调试级别"""
        self.mode_manager.set_debug_level(level)
    
    def enable_module_debug(self, module_name: str) -> None:
        """启用模块调试"""
        self.mode_manager.enable_module(module_name)
    
    def disable_module_debug(self, module_name: str) -> None:
        """禁用模块调试"""
        self.mode_manager.disable_module(module_name)
    
    def trace(self, func: Optional[Callable] = None, **kwargs):
        """函数跟踪装饰器"""
        return self.trace_logger.trace_function(func, **kwargs)
    
    def debug_point(self, message: str, variables: Optional[Dict[str, Any]] = None,
                   level: DebugLevel = DebugLevel.BASIC) -> None:
        """调试点记录"""
        try:
            # 获取调用者信息
            frame = inspect.currentframe().f_back
            if frame:
                module_name = frame.f_globals.get('__name__', 'unknown')
                
                if not self.mode_manager.is_debug_enabled(module_name):
                    return
                
                if self.mode_manager.get_debug_level().value < level.value:
                    return
                
                # 构建调试信息
                debug_info = {
                    "debug_point": True,
                    "message": message,
                    "module": module_name,
                    "function": frame.f_code.co_name,
                    "line": frame.f_lineno,
                    "level": level.name
                }
                
                # 添加变量信息
                if variables:
                    debug_info["variables"] = self.variable_inspector.inspect_variables(variables)
                elif level.value >= DebugLevel.DETAILED.value:
                    debug_info["frame_variables"] = self.variable_inspector.inspect_frame(frame)
                
                self._debug_logger.debug(f"DEBUG POINT: {message}", **debug_info)
                
        except Exception as e:
            logger.error(f"Failed to log debug point: {e}")
    
    @contextmanager
    def debug_context(self, context_name: str, variables: Optional[Dict[str, Any]] = None):
        """调试上下文管理器"""
        try:
            # 记录上下文开始
            self.debug_point(f"Context START: {context_name}", variables, DebugLevel.DETAILED)
            
            yield
            
            # 记录上下文结束
            self.debug_point(f"Context END: {context_name}", None, DebugLevel.DETAILED)
            
        except Exception as e:
            # 记录上下文异常
            self.debug_point(f"Context EXCEPTION: {context_name}", {"exception": str(e)}, DebugLevel.BASIC)
            raise
    
    def get_debug_status(self) -> Dict[str, Any]:
        """获取调试状态"""
        return self.mode_manager.get_status()


# 全局调试日志器实例
_global_debug_logger: Optional[DebugLogger] = None


def get_global_debug_logger() -> DebugLogger:
    """获取全局调试日志器实例"""
    global _global_debug_logger
    
    if _global_debug_logger is None:
        _global_debug_logger = DebugLogger()
    
    return _global_debug_logger


class DebugLoggingCore:
    """
    调试日志核心类
    
    这是调试日志系统的核心接口类，提供了完整的调试日志功能，
    包括调试级别管理、函数跟踪、变量检查和调试上下文管理。
    
    主要功能：
    - 调试级别控制
    - 模块级调试开关
    - 函数调用跟踪
    - 变量状态检查
    - 调试上下文管理
    - 性能监控
    
    使用示例：
        debug_core = DebugLoggingCore()
        debug_core.set_debug_level(DebugLevel.DETAILED)
        debug_core.enable_module_debug("ml.models")
        
        @debug_core.trace
        def my_function():
            debug_core.debug_point("Processing data", {"count": 100})
            return "result"
    """
    
    def __init__(self, name: Optional[str] = None):
        """
        初始化调试日志核心
        
        Args:
            name: 调试器名称，用于标识不同的调试实例
        """
        self.name = name or "default"
        self._debug_logger = DebugLogger()
        self._performance_stats: Dict[str, Any] = {}
        self._start_time = datetime.now()
        
        logger.info(f"DebugLoggingCore initialized: {self.name}")
    
    def set_debug_level(self, level: DebugLevel) -> None:
        """
        设置调试级别
        
        Args:
            level: 调试级别 (NONE, BASIC, DETAILED, VERBOSE)
        """
        self._debug_logger.set_debug_level(level)
        logger.info(f"Debug level set to {level.name} for {self.name}")
    
    def get_debug_level(self) -> DebugLevel:
        """
        获取当前调试级别
        
        Returns:
            当前的调试级别
        """
        return self._debug_logger.mode_manager.get_debug_level()
    
    def enable_module_debug(self, module_name: str) -> None:
        """
        启用指定模块的调试
        
        Args:
            module_name: 模块名称
        """
        self._debug_logger.enable_module_debug(module_name)
        logger.debug(f"Debug enabled for module: {module_name}")
    
    def disable_module_debug(self, module_name: str) -> None:
        """
        禁用指定模块的调试
        
        Args:
            module_name: 模块名称
        """
        self._debug_logger.disable_module_debug(module_name)
        logger.debug(f"Debug disabled for module: {module_name}")
    
    def is_debug_enabled(self, module_name: str) -> bool:
        """
        检查指定模块是否启用调试
        
        Args:
            module_name: 模块名称
            
        Returns:
            是否启用调试
        """
        return self._debug_logger.mode_manager.is_debug_enabled(module_name)
    
    def trace(self, func: Optional[Callable] = None, **kwargs):
        """
        函数跟踪装饰器
        
        Args:
            func: 要跟踪的函数
            **kwargs: 跟踪选项
            
        Returns:
            装饰器函数
        """
        return self._debug_logger.trace(func, **kwargs)
    
    def debug_point(self, message: str, variables: Optional[Dict[str, Any]] = None,
                   level: DebugLevel = DebugLevel.BASIC) -> None:
        """
        记录调试点
        
        Args:
            message: 调试消息
            variables: 要记录的变量
            level: 调试级别
        """
        self._debug_logger.debug_point(message, variables, level)
    
    def debug_context(self, context_name: str, variables: Optional[Dict[str, Any]] = None):
        """
        调试上下文管理器
        
        Args:
            context_name: 上下文名称
            variables: 上下文变量
            
        Returns:
            上下文管理器
        """
        return self._debug_logger.debug_context(context_name, variables)
    
    def log_performance(self, operation_name: str, duration: float, 
                       metadata: Optional[Dict[str, Any]] = None) -> None:
        """
        记录性能信息
        
        Args:
            operation_name: 操作名称
            duration: 执行时间（秒）
            metadata: 额外的元数据
        """
        try:
            perf_data = {
                "operation": operation_name,
                "duration_ms": round(duration * 1000, 2),
                "timestamp": datetime.now().isoformat(),
                "debug_core": self.name
            }
            
            if metadata:
                perf_data.update(metadata)
            
            # 更新统计信息
            if operation_name not in self._performance_stats:
                self._performance_stats[operation_name] = {
                    "count": 0,
                    "total_duration": 0.0,
                    "min_duration": float('inf'),
                    "max_duration": 0.0
                }
            
            stats = self._performance_stats[operation_name]
            stats["count"] += 1
            stats["total_duration"] += duration
            stats["min_duration"] = min(stats["min_duration"], duration)
            stats["max_duration"] = max(stats["max_duration"], duration)
            stats["avg_duration"] = stats["total_duration"] / stats["count"]
            
            logger.bind(**perf_data).info(f"Performance: {operation_name}")
            
        except Exception as e:
            logger.error(f"Failed to log performance data: {e}")
    
    @contextmanager
    def performance_context(self, operation_name: str, 
                          metadata: Optional[Dict[str, Any]] = None):
        """
        性能监控上下文管理器
        
        Args:
            operation_name: 操作名称
            metadata: 额外的元数据
        """
        start_time = datetime.now()
        try:
            yield
        finally:
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            self.log_performance(operation_name, duration, metadata)
    
    def get_debug_status(self) -> Dict[str, Any]:
        """
        获取调试状态信息
        
        Returns:
            调试状态字典
        """
        status = self._debug_logger.get_debug_status()
        status.update({
            "debug_core_name": self.name,
            "uptime_seconds": (datetime.now() - self._start_time).total_seconds(),
            "performance_stats": self._performance_stats.copy()
        })
        return status
    
    def reset_performance_stats(self) -> None:
        """重置性能统计信息"""
        self._performance_stats.clear()
        logger.info(f"Performance stats reset for {self.name}")
    
    def export_debug_report(self) -> Dict[str, Any]:
        """
        导出调试报告
        
        Returns:
            包含完整调试信息的报告
        """
        try:
            report = {
                "debug_core_name": self.name,
                "timestamp": datetime.now().isoformat(),
                "status": self.get_debug_status(),
                "performance_summary": self._generate_performance_summary(),
                "system_info": {
                    "python_version": sys.version,
                    "thread_count": threading.active_count(),
                    "current_thread": threading.current_thread().name
                }
            }
            
            return report
            
        except Exception as e:
            logger.error(f"Failed to export debug report: {e}")
            return {"error": "report_generation_failed"}
    
    def _generate_performance_summary(self) -> Dict[str, Any]:
        """生成性能摘要"""
        try:
            if not self._performance_stats:
                return {"message": "No performance data available"}
            
            summary = {
                "total_operations": len(self._performance_stats),
                "operations": {}
            }
            
            for op_name, stats in self._performance_stats.items():
                summary["operations"][op_name] = {
                    "count": stats["count"],
                    "avg_duration_ms": round(stats["avg_duration"] * 1000, 2),
                    "min_duration_ms": round(stats["min_duration"] * 1000, 2),
                    "max_duration_ms": round(stats["max_duration"] * 1000, 2),
                    "total_duration_ms": round(stats["total_duration"] * 1000, 2)
                }
            
            return summary
            
        except Exception as e:
            logger.error(f"Failed to generate performance summary: {e}")
            return {"error": "summary_generation_failed"}


# 便捷函数
def debug_trace(func: Optional[Callable] = None, **kwargs):
    """便捷的调试跟踪装饰器"""
    debug_logger = get_global_debug_logger()
    return debug_logger.trace(func, **kwargs)


def debug_point(message: str, variables: Optional[Dict[str, Any]] = None,
               level: DebugLevel = DebugLevel.BASIC) -> None:
    """便捷的调试点函数"""
    debug_logger = get_global_debug_logger()
    debug_logger.debug_point(message, variables, level)


# 全局调试核心实例
_global_debug_core: Optional[DebugLoggingCore] = None


def get_global_debug_core() -> DebugLoggingCore:
    """获取全局调试核心实例"""
    global _global_debug_core
    
    if _global_debug_core is None:
        _global_debug_core = DebugLoggingCore("global")
    
    return _global_debug_core


# 模块级别的日志器
module_logger = logger.bind(name=__name__)