#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
学习策略管理器

管理不同的在线学习策略和参数配置，支持自适应调整、策略切换、参数优化等功能。
根据市场条件和模型性能动态选择最优的学习策略。
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any, Union, Callable
from datetime import datetime, timedelta
import threading
from collections import defaultdict, deque
from dataclasses import dataclass
import time
from enum import Enum
import json


class StrategyType(Enum):
    """学习策略类型枚举"""
    CONSERVATIVE = "conservative"    # 保守策略
    AGGRESSIVE = "aggressive"       # 激进策略
    ADAPTIVE = "adaptive"           # 自适应策略
    ENSEMBLE = "ensemble"           # 集成策略
    CUSTOM = "custom"               # 自定义策略


class MarketCondition(Enum):
    """市场条件枚举"""
    TRENDING = "trending"           # 趋势市场
    RANGING = "ranging"             # 震荡市场
    VOLATILE = "volatile"           # 高波动市场
    STABLE = "stable"               # 稳定市场
    UNKNOWN = "unknown"             # 未知状态


@dataclass
class StrategyConfig:
    """策略配置"""
    name: str
    strategy_type: StrategyType
    learning_rate: float
    batch_size: int
    update_frequency: int           # 更新频率（秒）
    momentum: float = 0.9
    weight_decay: float = 1e-4
    gradient_clip: float = 1.0
    patience: int = 10              # 早停耐心值
    min_samples: int = 100          # 最小样本数
    max_samples: int = 10000        # 最大样本数
    performance_threshold: float = 0.8  # 性能阈值
    enabled: bool = True
    market_conditions: List[MarketCondition] = None


@dataclass
class StrategyPerformance:
    """策略性能指标"""
    strategy_name: str
    accuracy: float
    loss: float
    learning_speed: float           # 学习速度
    stability: float                # 稳定性
    adaptability: float             # 适应性
    resource_usage: float           # 资源使用率
    last_update: datetime
    sample_count: int
    success_rate: float


class LearningStrategyManager:
    """学习策略管理器"""
    
    def __init__(self, config: Dict = None):
        """
        初始化学习策略管理器
        
        Args:
            config: 配置参数字典
        """
        self.logger = logging.getLogger('cryptoMlStrategy.online_learning.strategy_manager')
        
        # 默认配置
        self.config = {
            'strategies': {
                'conservative': {
                    'learning_rate': 0.001,
                    'batch_size': 32,
                    'update_frequency': 300,  # 5分钟
                    'momentum': 0.9,
                    'weight_decay': 1e-4,
                    'gradient_clip': 0.5,
                    'patience': 20,
                    'market_conditions': ['stable', 'ranging']
                },
                'aggressive': {
                    'learning_rate': 0.01,
                    'batch_size': 16,
                    'update_frequency': 60,   # 1分钟
                    'momentum': 0.95,
                    'weight_decay': 1e-5,
                    'gradient_clip': 2.0,
                    'patience': 5,
                    'market_conditions': ['trending', 'volatile']
                },
                'adaptive': {
                    'learning_rate': 0.005,
                    'batch_size': 24,
                    'update_frequency': 120,  # 2分钟
                    'momentum': 0.92,
                    'weight_decay': 5e-5,
                    'gradient_clip': 1.0,
                    'patience': 10,
                    'market_conditions': ['unknown']
                }
            },
            'adaptation': {
                'enable_auto_adaptation': True,
                'adaptation_interval': 3600,    # 1小时
                'performance_window': 24,       # 24小时
                'min_performance_samples': 100,
                'adaptation_threshold': 0.05    # 5%性能差异
            },
            'ensemble': {
                'enable_ensemble': True,
                'max_strategies': 3,
                'weight_update_frequency': 1800,  # 30分钟
                'diversity_threshold': 0.1
            },
            'optimization': {
                'enable_hyperparameter_optimization': True,
                'optimization_frequency': 86400,  # 24小时
                'optimization_trials': 50,
                'optimization_timeout': 3600      # 1小时
            }
        }
        
        # 更新用户配置
        if config:
            self._deep_update(self.config, config)
        
        # 初始化策略
        self.strategies = {}
        self._initialize_strategies()
        
        # 当前活跃策略
        self.active_strategy = None
        self.strategy_weights = {}  # 集成策略权重
        
        # 性能历史
        self.performance_history = defaultdict(list)
        
        # 市场条件检测
        self.current_market_condition = MarketCondition.UNKNOWN
        self.market_condition_history = deque(maxlen=100)
        
        # 统计信息
        self.stats = {
            'strategy_switches': 0,
            'adaptations_performed': 0,
            'optimizations_performed': 0,
            'total_updates': 0,
            'last_adaptation_time': None,
            'last_optimization_time': None
        }
        
        # 线程锁
        self.strategy_lock = threading.RLock()
        
        self.logger.info("学习策略管理器初始化完成")
    
    def _deep_update(self, base_dict: Dict, update_dict: Dict):
        """深度更新字典"""
        for key, value in update_dict.items():
            if key in base_dict and isinstance(base_dict[key], dict) and isinstance(value, dict):
                self._deep_update(base_dict[key], value)
            else:
                base_dict[key] = value
    
    def _initialize_strategies(self):
        """初始化预定义策略"""
        try:
            for strategy_name, strategy_config in self.config['strategies'].items():
                # 确定策略类型
                if strategy_name == 'conservative':
                    strategy_type = StrategyType.CONSERVATIVE
                elif strategy_name == 'aggressive':
                    strategy_type = StrategyType.AGGRESSIVE
                elif strategy_name == 'adaptive':
                    strategy_type = StrategyType.ADAPTIVE
                else:
                    strategy_type = StrategyType.CUSTOM
                
                # 解析市场条件
                market_conditions = []
                for condition_str in strategy_config.get('market_conditions', []):
                    try:
                        market_conditions.append(MarketCondition(condition_str))
                    except ValueError:
                        self.logger.warning(f"未知的市场条件: {condition_str}")
                
                # 创建策略配置
                config = StrategyConfig(
                    name=strategy_name,
                    strategy_type=strategy_type,
                    learning_rate=strategy_config['learning_rate'],
                    batch_size=strategy_config['batch_size'],
                    update_frequency=strategy_config['update_frequency'],
                    momentum=strategy_config.get('momentum', 0.9),
                    weight_decay=strategy_config.get('weight_decay', 1e-4),
                    gradient_clip=strategy_config.get('gradient_clip', 1.0),
                    patience=strategy_config.get('patience', 10),
                    market_conditions=market_conditions
                )
                
                self.strategies[strategy_name] = config
                self.strategy_weights[strategy_name] = 1.0 / len(self.config['strategies'])
            
            # 设置默认活跃策略
            if 'adaptive' in self.strategies:
                self.active_strategy = 'adaptive'
            else:
                self.active_strategy = list(self.strategies.keys())[0]
            
            self.logger.info(f"初始化了 {len(self.strategies)} 个策略，活跃策略: {self.active_strategy}")
            
        except Exception as e:
            self.logger.error(f"策略初始化失败: {e}")
    
    def get_current_strategy(self) -> Optional[StrategyConfig]:
        """
        获取当前活跃策略
        
        Returns:
            当前策略配置
        """
        try:
            with self.strategy_lock:
                if self.active_strategy and self.active_strategy in self.strategies:
                    return self.strategies[self.active_strategy]
                return None
                
        except Exception as e:
            self.logger.error(f"获取当前策略失败: {e}")
            return None
    
    def select_strategy(self, market_condition: MarketCondition = None, 
                       performance_metrics: Dict[str, float] = None) -> Optional[str]:
        """
        选择最适合的策略
        
        Args:
            market_condition: 当前市场条件
            performance_metrics: 性能指标
            
        Returns:
            选择的策略名称
        """
        try:
            with self.strategy_lock:
                if market_condition:
                    self.current_market_condition = market_condition
                    self.market_condition_history.append(market_condition)
                
                # 如果没有指定市场条件，使用当前检测到的条件
                if market_condition is None:
                    market_condition = self.current_market_condition
                
                # 根据市场条件筛选合适的策略
                suitable_strategies = []
                for strategy_name, strategy_config in self.strategies.items():
                    if not strategy_config.enabled:
                        continue
                    
                    if (strategy_config.market_conditions is None or 
                        market_condition in strategy_config.market_conditions):
                        suitable_strategies.append(strategy_name)
                
                if not suitable_strategies:
                    self.logger.warning(f"没有找到适合市场条件 {market_condition} 的策略")
                    return self.active_strategy
                
                # 如果有性能指标，选择性能最好的策略
                if performance_metrics and len(suitable_strategies) > 1:
                    best_strategy = self._select_best_performing_strategy(
                        suitable_strategies, performance_metrics
                    )
                    if best_strategy:
                        suitable_strategies = [best_strategy]
                
                # 选择策略
                selected_strategy = suitable_strategies[0]
                
                # 如果策略发生变化，记录切换
                if selected_strategy != self.active_strategy:
                    self.logger.info(f"策略切换: {self.active_strategy} -> {selected_strategy}")
                    self.active_strategy = selected_strategy
                    self.stats['strategy_switches'] += 1
                
                return selected_strategy
                
        except Exception as e:
            self.logger.error(f"策略选择失败: {e}")
            return self.active_strategy
    
    def _select_best_performing_strategy(self, candidates: List[str], 
                                       performance_metrics: Dict[str, float]) -> Optional[str]:
        """根据性能指标选择最佳策略"""
        try:
            best_strategy = None
            best_score = -float('inf')
            
            for strategy_name in candidates:
                if strategy_name not in self.performance_history:
                    continue
                
                # 计算综合性能分数
                recent_performances = self.performance_history[strategy_name][-10:]  # 最近10次
                if not recent_performances:
                    continue
                
                avg_accuracy = np.mean([p.accuracy for p in recent_performances])
                avg_stability = np.mean([p.stability for p in recent_performances])
                avg_adaptability = np.mean([p.adaptability for p in recent_performances])
                
                # 综合分数计算
                score = (avg_accuracy * 0.4 + avg_stability * 0.3 + avg_adaptability * 0.3)
                
                if score > best_score:
                    best_score = score
                    best_strategy = strategy_name
            
            return best_strategy
            
        except Exception as e:
            self.logger.error(f"性能策略选择失败: {e}")
            return None
    
    def update_strategy_performance(self, strategy_name: str, 
                                  performance: StrategyPerformance):
        """
        更新策略性能指标
        
        Args:
            strategy_name: 策略名称
            performance: 性能指标
        """
        try:
            with self.strategy_lock:
                self.performance_history[strategy_name].append(performance)
                
                # 限制历史记录长度
                max_history = 1000
                if len(self.performance_history[strategy_name]) > max_history:
                    self.performance_history[strategy_name] = \
                        self.performance_history[strategy_name][-max_history:]
                
                self.logger.debug(f"更新策略性能: {strategy_name}, 准确率: {performance.accuracy:.3f}")
                
        except Exception as e:
            self.logger.error(f"更新策略性能失败: {e}")
    
    def adapt_strategy_parameters(self, strategy_name: str, 
                                performance_trend: List[float]) -> bool:
        """
        自适应调整策略参数
        
        Args:
            strategy_name: 策略名称
            performance_trend: 性能趋势
            
        Returns:
            是否成功调整
        """
        try:
            if not self.config['adaptation']['enable_auto_adaptation']:
                return False
            
            with self.strategy_lock:
                if strategy_name not in self.strategies:
                    return False
                
                strategy = self.strategies[strategy_name]
                
                # 分析性能趋势
                if len(performance_trend) < 5:
                    return False
                
                recent_trend = np.mean(performance_trend[-5:])
                overall_trend = np.mean(performance_trend)
                
                # 如果性能下降，调整参数
                if recent_trend < overall_trend * 0.95:  # 性能下降超过5%
                    self._adjust_strategy_parameters(strategy, 'decrease_performance')
                elif recent_trend > overall_trend * 1.05:  # 性能提升超过5%
                    self._adjust_strategy_parameters(strategy, 'increase_performance')
                
                self.stats['adaptations_performed'] += 1
                self.stats['last_adaptation_time'] = datetime.now()
                
                self.logger.info(f"策略参数自适应调整完成: {strategy_name}")
                return True
                
        except Exception as e:
            self.logger.error(f"策略参数自适应调整失败: {e}")
            return False
    
    def _adjust_strategy_parameters(self, strategy: StrategyConfig, adjustment_type: str):
        """调整策略参数"""
        try:
            if adjustment_type == 'decrease_performance':
                # 性能下降时的调整
                strategy.learning_rate *= 0.9  # 降低学习率
                strategy.batch_size = min(strategy.batch_size + 4, 64)  # 增加批次大小
                strategy.update_frequency = min(strategy.update_frequency + 30, 600)  # 降低更新频率
                
            elif adjustment_type == 'increase_performance':
                # 性能提升时的调整
                strategy.learning_rate *= 1.05  # 适度提高学习率
                strategy.batch_size = max(strategy.batch_size - 2, 8)  # 减少批次大小
                strategy.update_frequency = max(strategy.update_frequency - 15, 30)  # 提高更新频率
            
            # 确保参数在合理范围内
            strategy.learning_rate = np.clip(strategy.learning_rate, 1e-5, 0.1)
            strategy.batch_size = np.clip(strategy.batch_size, 8, 128)
            strategy.update_frequency = np.clip(strategy.update_frequency, 30, 3600)
            
        except Exception as e:
            self.logger.error(f"参数调整失败: {e}")
    
    def get_ensemble_weights(self) -> Dict[str, float]:
        """
        获取集成策略权重
        
        Returns:
            策略权重字典
        """
        try:
            if not self.config['ensemble']['enable_ensemble']:
                return {self.active_strategy: 1.0}
            
            with self.strategy_lock:
                # 基于最近性能计算权重
                total_weight = 0
                strategy_scores = {}
                
                for strategy_name in self.strategies.keys():
                    if strategy_name not in self.performance_history:
                        strategy_scores[strategy_name] = 0.1  # 默认最小权重
                        continue
                    
                    recent_performances = self.performance_history[strategy_name][-5:]
                    if not recent_performances:
                        strategy_scores[strategy_name] = 0.1
                        continue
                    
                    # 计算综合分数
                    avg_accuracy = np.mean([p.accuracy for p in recent_performances])
                    avg_stability = np.mean([p.stability for p in recent_performances])
                    score = avg_accuracy * avg_stability
                    
                    strategy_scores[strategy_name] = max(score, 0.1)
                    total_weight += strategy_scores[strategy_name]
                
                # 归一化权重
                if total_weight > 0:
                    for strategy_name in strategy_scores:
                        self.strategy_weights[strategy_name] = strategy_scores[strategy_name] / total_weight
                
                return self.strategy_weights.copy()
                
        except Exception as e:
            self.logger.error(f"获取集成权重失败: {e}")
            return {self.active_strategy: 1.0}
    
    def detect_market_condition(self, market_data: pd.DataFrame) -> MarketCondition:
        """
        检测市场条件
        
        Args:
            market_data: 市场数据
            
        Returns:
            检测到的市场条件
        """
        try:
            if market_data is None or market_data.empty:
                return MarketCondition.UNKNOWN
            
            # 计算市场指标
            if 'close' not in market_data.columns:
                return MarketCondition.UNKNOWN
            
            prices = market_data['close'].values
            if len(prices) < 20:
                return MarketCondition.UNKNOWN
            
            # 计算波动率
            returns = np.diff(prices) / prices[:-1]
            volatility = np.std(returns)
            
            # 计算趋势强度
            trend_strength = abs(np.corrcoef(np.arange(len(prices)), prices)[0, 1])
            
            # 判断市场条件
            if volatility > 0.03:  # 高波动
                condition = MarketCondition.VOLATILE
            elif trend_strength > 0.7:  # 强趋势
                condition = MarketCondition.TRENDING
            elif trend_strength < 0.3:  # 弱趋势
                condition = MarketCondition.RANGING
            else:  # 稳定
                condition = MarketCondition.STABLE
            
            self.current_market_condition = condition
            self.market_condition_history.append(condition)
            
            return condition
            
        except Exception as e:
            self.logger.error(f"市场条件检测失败: {e}")
            return MarketCondition.UNKNOWN
    
    def optimize_hyperparameters(self, strategy_name: str, 
                                objective_function: Callable) -> bool:
        """
        优化策略超参数
        
        Args:
            strategy_name: 策略名称
            objective_function: 目标函数
            
        Returns:
            是否优化成功
        """
        try:
            if not self.config['optimization']['enable_hyperparameter_optimization']:
                return False
            
            # 简化的超参数优化（实际项目中可以使用Optuna等库）
            self.logger.info(f"开始优化策略超参数: {strategy_name}")
            
            # 这里实现简化的网格搜索
            best_params = self._grid_search_optimization(strategy_name, objective_function)
            
            if best_params:
                # 更新策略参数
                strategy = self.strategies[strategy_name]
                for param_name, param_value in best_params.items():
                    if hasattr(strategy, param_name):
                        setattr(strategy, param_name, param_value)
                
                self.stats['optimizations_performed'] += 1
                self.stats['last_optimization_time'] = datetime.now()
                
                self.logger.info(f"超参数优化完成: {strategy_name}")
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"超参数优化失败: {e}")
            return False
    
    def _grid_search_optimization(self, strategy_name: str, 
                                objective_function: Callable) -> Optional[Dict[str, Any]]:
        """简化的网格搜索优化"""
        try:
            # 定义搜索空间
            param_grid = {
                'learning_rate': [0.001, 0.005, 0.01, 0.05],
                'batch_size': [16, 32, 64],
                'momentum': [0.9, 0.95, 0.99]
            }
            
            best_score = -float('inf')
            best_params = None
            
            # 简化的网格搜索（实际应该更复杂）
            for lr in param_grid['learning_rate']:
                for bs in param_grid['batch_size']:
                    for momentum in param_grid['momentum']:
                        params = {
                            'learning_rate': lr,
                            'batch_size': bs,
                            'momentum': momentum
                        }
                        
                        # 评估参数组合
                        score = objective_function(params)
                        
                        if score > best_score:
                            best_score = score
                            best_params = params
            
            return best_params
            
        except Exception as e:
            self.logger.error(f"网格搜索优化失败: {e}")
            return None
    
    def get_strategy_statistics(self) -> Dict[str, Any]:
        """获取策略统计信息"""
        try:
            with self.strategy_lock:
                stats = {
                    'active_strategy': self.active_strategy,
                    'current_market_condition': self.current_market_condition.value,
                    'available_strategies': list(self.strategies.keys()),
                    'strategy_weights': self.strategy_weights.copy(),
                    'performance_history_length': {
                        name: len(history) for name, history in self.performance_history.items()
                    },
                    'market_condition_history': [c.value for c in list(self.market_condition_history)],
                    'stats': self.stats.copy(),
                    'config': self.config
                }
                
                # 添加最近性能指标
                recent_performance = {}
                for strategy_name, history in self.performance_history.items():
                    if history:
                        latest = history[-1]
                        recent_performance[strategy_name] = {
                            'accuracy': latest.accuracy,
                            'loss': latest.loss,
                            'stability': latest.stability,
                            'last_update': latest.last_update.isoformat()
                        }
                
                stats['recent_performance'] = recent_performance
                
                return stats
                
        except Exception as e:
            self.logger.error(f"获取策略统计失败: {e}")
            return {}
    
    def export_strategy_config(self, filepath: str) -> bool:
        """导出策略配置"""
        try:
            export_data = {
                'strategies': {},
                'active_strategy': self.active_strategy,
                'strategy_weights': self.strategy_weights,
                'config': self.config,
                'export_time': datetime.now().isoformat()
            }
            
            # 导出策略配置
            for name, strategy in self.strategies.items():
                export_data['strategies'][name] = {
                    'name': strategy.name,
                    'strategy_type': strategy.strategy_type.value,
                    'learning_rate': strategy.learning_rate,
                    'batch_size': strategy.batch_size,
                    'update_frequency': strategy.update_frequency,
                    'momentum': strategy.momentum,
                    'weight_decay': strategy.weight_decay,
                    'gradient_clip': strategy.gradient_clip,
                    'patience': strategy.patience,
                    'enabled': strategy.enabled
                }
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"策略配置已导出到: {filepath}")
            return True
            
        except Exception as e:
            self.logger.error(f"导出策略配置失败: {e}")
            return False