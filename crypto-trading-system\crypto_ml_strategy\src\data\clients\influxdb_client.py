#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
InfluxDB客户端模块

提供InfluxDB数据库的连接和操作功能，支持时间序列数据的读写操作。
用于存储和查询市场数据，包括K线数据、深度数据和交易数据。
"""

import logging
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timedelta
import pandas as pd
from loguru import logger

try:
    from influxdb_client import InfluxDBClient as InfluxClient, Point, WritePrecision
    from influxdb_client.client.write_api import SYNCHRONOUS
    from influxdb_client.client.exceptions import InfluxDBError
except ImportError:
    logger.warning("influxdb-client not installed, InfluxDB functionality will be limited")
    InfluxClient = None
    Point = None
    WritePrecision = None
    SYNCHRONOUS = None
    InfluxDBError = Exception


class InfluxDBClient:
    """
    InfluxDB客户端类，提供时间序列数据的存储和查询功能。
    
    支持功能：
    - 连接管理和健康检查
    - 市场数据写入（K线、深度、交易数据）
    - 历史数据查询和聚合
    - 批量数据操作
    - 自动重连和错误处理
    """
    
    def __init__(self, url: str, token: str, org: str, bucket: str):
        """
        初始化InfluxDB客户端
        
        Args:
            url: InfluxDB服务器URL
            token: 访问令牌
            org: 组织名称
            bucket: 存储桶名称
        
        Raises:
            ConnectionError: 连接失败时抛出
            ValueError: 参数无效时抛出
        """
        self.logger = logger.bind(component="InfluxDBClient")
        
        if not all([url, token, org, bucket]):
            raise ValueError("InfluxDB连接参数不能为空")
        
        self.url = url
        self.token = token
        self.org = org
        self.bucket = bucket
        
        # 初始化客户端
        self._client: Optional[InfluxClient] = None
        self._write_api = None
        self._query_api = None
        self._connected = False
        
        # 连接到InfluxDB
        self._connect()
        
        self.logger.info(f"InfluxDB客户端初始化完成: {url}, 组织: {org}, 存储桶: {bucket}")
    
    def _connect(self) -> None:
        """建立InfluxDB连接"""
        try:
            if InfluxClient is None:
                raise ImportError("influxdb-client package not available")
            
            self._client = InfluxClient(
                url=self.url,
                token=self.token,
                org=self.org,
                timeout=30000  # 30秒超时
            )
            
            # 测试连接
            health = self._client.health()
            if health.status != "pass":
                raise ConnectionError(f"InfluxDB健康检查失败: {health.message}")
            
            # 初始化API
            self._write_api = self._client.write_api(write_options=SYNCHRONOUS)
            self._query_api = self._client.query_api()
            
            self._connected = True
            self.logger.info("InfluxDB连接建立成功")
            
        except Exception as e:
            self.logger.error(f"InfluxDB连接失败: {str(e)}")
            self._connected = False
            raise ConnectionError(f"无法连接到InfluxDB: {str(e)}")
    
    def is_connected(self) -> bool:
        """检查连接状态"""
        if not self._connected or not self._client:
            return False
        
        try:
            health = self._client.health()
            return health.status == "pass"
        except Exception:
            self._connected = False
            return False
    
    def reconnect(self) -> None:
        """重新连接"""
        self.logger.info("尝试重新连接InfluxDB...")
        self.close()
        self._connect()
    
    def write_kline_data(self, symbol: str, timeframe: str, kline_data: Dict[str, Any]) -> bool:
        """
        写入K线数据
        
        Args:
            symbol: 交易对符号
            timeframe: 时间周期
            kline_data: K线数据字典
        
        Returns:
            写入是否成功
        """
        try:
            if not self.is_connected():
                self.reconnect()
            
            point = Point("kline") \
                .tag("symbol", symbol) \
                .tag("timeframe", timeframe) \
                .field("open", float(kline_data.get("open", 0))) \
                .field("high", float(kline_data.get("high", 0))) \
                .field("low", float(kline_data.get("low", 0))) \
                .field("close", float(kline_data.get("close", 0))) \
                .field("volume", float(kline_data.get("volume", 0))) \
                .field("quote_volume", float(kline_data.get("quote_volume", 0))) \
                .field("trades", int(kline_data.get("trades", 0))) \
                .time(kline_data.get("open_time", datetime.now()), WritePrecision.MS)
            
            self._write_api.write(bucket=self.bucket, org=self.org, record=point)
            self.logger.debug(f"K线数据写入成功: {symbol} {timeframe}")
            return True
            
        except Exception as e:
            self.logger.error(f"K线数据写入失败: {str(e)}")
            return False
    
    def write_depth_data(self, symbol: str, depth_data: Dict[str, Any]) -> bool:
        """
        写入深度数据
        
        Args:
            symbol: 交易对符号
            depth_data: 深度数据字典
        
        Returns:
            写入是否成功
        """
        try:
            if not self.is_connected():
                self.reconnect()
            
            point = Point("depth") \
                .tag("symbol", symbol) \
                .field("best_bid_price", float(depth_data.get("best_bid_price", 0))) \
                .field("best_bid_qty", float(depth_data.get("best_bid_qty", 0))) \
                .field("best_ask_price", float(depth_data.get("best_ask_price", 0))) \
                .field("best_ask_qty", float(depth_data.get("best_ask_qty", 0))) \
                .field("spread", float(depth_data.get("spread", 0))) \
                .field("mid_price", float(depth_data.get("mid_price", 0))) \
                .time(depth_data.get("timestamp", datetime.now()), WritePrecision.MS)
            
            self._write_api.write(bucket=self.bucket, org=self.org, record=point)
            self.logger.debug(f"深度数据写入成功: {symbol}")
            return True
            
        except Exception as e:
            self.logger.error(f"深度数据写入失败: {str(e)}")
            return False
    
    def query_kline_data(self, symbol: str, timeframe: str, 
                        start_time: datetime, end_time: datetime) -> Optional[pd.DataFrame]:
        """
        查询K线数据
        
        Args:
            symbol: 交易对符号
            timeframe: 时间周期
            start_time: 开始时间
            end_time: 结束时间
        
        Returns:
            K线数据DataFrame，查询失败返回None
        """
        try:
            if not self.is_connected():
                self.reconnect()
            
            query = f'''
                from(bucket: "{self.bucket}")
                |> range(start: {start_time.isoformat()}Z, stop: {end_time.isoformat()}Z)
                |> filter(fn: (r) => r._measurement == "kline")
                |> filter(fn: (r) => r.symbol == "{symbol}")
                |> filter(fn: (r) => r.interval == "{timeframe}")
                |> pivot(rowKey:["_time"], columnKey: ["_field"], valueColumn: "_value")
            '''
            
            result = self._query_api.query_data_frame(query, org=self.org)
            
            if result.empty:
                self.logger.warning(f"未找到K线数据: {symbol} {timeframe}")
                return None
            
            # 处理数据格式
            result['_time'] = pd.to_datetime(result['_time'])
            result = result.sort_values('_time')
            
            self.logger.debug(f"K线数据查询成功: {symbol} {timeframe}, 记录数: {len(result)}")
            return result
            
        except Exception as e:
            self.logger.error(f"K线数据查询失败: {str(e)}")
            return None
    
    def query_latest_kline(self, symbol: str, timeframe: str, limit: int = 100) -> Optional[pd.DataFrame]:
        """
        查询最新的K线数据
        
        Args:
            symbol: 交易对符号
            timeframe: 时间周期
            limit: 返回记录数限制
        
        Returns:
            最新K线数据DataFrame
        """
        try:
            if not self.is_connected():
                self.reconnect()
            
            query = f'''
                from(bucket: "{self.bucket}")
                |> range(start: -30d)
                |> filter(fn: (r) => r._measurement == "kline")
                |> filter(fn: (r) => r.symbol == "{symbol}")
                |> filter(fn: (r) => r.interval == "{timeframe}")
                |> pivot(rowKey:["_time"], columnKey: ["_field"], valueColumn: "_value")
                |> sort(columns: ["_time"], desc: true)
                |> limit(n: {limit})
            '''
            
            result = self._query_api.query_data_frame(query, org=self.org)
            
            if result.empty:
                return None
            
            result['_time'] = pd.to_datetime(result['_time'])
            result = result.sort_values('_time')
            
            return result
            
        except Exception as e:
            self.logger.error(f"最新K线数据查询失败: {str(e)}")
            return None
    
    def write_batch_data(self, points: List[Point]) -> bool:
        """
        批量写入数据
        
        Args:
            points: Point对象列表
        
        Returns:
            写入是否成功
        """
        try:
            if not self.is_connected():
                self.reconnect()
            
            self._write_api.write(bucket=self.bucket, org=self.org, record=points)
            self.logger.debug(f"批量数据写入成功，记录数: {len(points)}")
            return True
            
        except Exception as e:
            self.logger.error(f"批量数据写入失败: {str(e)}")
            return False
    
    def get_bucket_info(self) -> Optional[Dict[str, Any]]:
        """
        获取存储桶信息
        
        Returns:
            存储桶信息字典
        """
        try:
            if not self.is_connected():
                self.reconnect()
            
            buckets_api = self._client.buckets_api()
            bucket = buckets_api.find_bucket_by_name(self.bucket)
            
            if bucket:
                return {
                    "id": bucket.id,
                    "name": bucket.name,
                    "org_id": bucket.org_id,
                    "retention_rules": bucket.retention_rules,
                    "created_at": bucket.created_at,
                    "updated_at": bucket.updated_at
                }
            
            return None
            
        except Exception as e:
            self.logger.error(f"获取存储桶信息失败: {str(e)}")
            return None
    
    def close(self) -> None:
        """关闭连接"""
        try:
            if self._write_api:
                self._write_api.close()
            if self._client:
                self._client.close()
            
            self._connected = False
            self.logger.info("InfluxDB连接已关闭")
            
        except Exception as e:
            self.logger.error(f"关闭InfluxDB连接时出错: {str(e)}")
    
    def __enter__(self):
        """上下文管理器入口"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.close()