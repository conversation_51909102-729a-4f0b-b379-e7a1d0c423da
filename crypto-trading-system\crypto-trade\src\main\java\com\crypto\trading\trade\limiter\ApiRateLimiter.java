package com.crypto.trading.trade.limiter;

import io.github.resilience4j.ratelimiter.RateLimiter;
import io.github.resilience4j.ratelimiter.RequestNotPermitted;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.concurrent.Callable;
import java.util.concurrent.CompletableFuture;
import java.util.function.Supplier;

/**
 * API速率限制器
 * 
 * <p>根据Binance API的限制规则实现请求限速，避免API请求被拒绝</p>
 */
@Component
public class ApiRateLimiter {

    private static final Logger log = LoggerFactory.getLogger(ApiRateLimiter.class);

    @Autowired
    @Qualifier("orderRateLimiter")
    private RateLimiter orderRateLimiter;

    @Autowired
    @Qualifier("queryRateLimiter")
    private RateLimiter queryRateLimiter;

    /**
     * 执行订单相关的API调用
     *
     * @param operation 操作函数
     * @param <T> 返回类型
     * @return 操作结果
     * @throws Exception 如果操作执行失败
     */
    public <T> T executeOrderOperation(Callable<T> operation) throws Exception {
        return executeOperation(operation, orderRateLimiter, "订单");
    }

    /**
     * 执行查询相关的API调用
     *
     * @param operation 操作函数
     * @param <T> 返回类型
     * @return 操作结果
     * @throws Exception 如果操作执行失败
     */
    public <T> T executeQueryOperation(Callable<T> operation) throws Exception {
        return executeOperation(operation, queryRateLimiter, "查询");
    }

    /**
     * 异步执行订单相关的API调用
     *
     * @param operation 操作供应商
     * @param <T> 返回类型
     * @return 包含操作结果的CompletableFuture
     */
    public <T> CompletableFuture<T> executeOrderOperationAsync(Supplier<T> operation) {
        return executeOperationAsync(operation, orderRateLimiter, "订单");
    }

    /**
     * 异步执行查询相关的API调用
     *
     * @param operation 操作供应商
     * @param <T> 返回类型
     * @return 包含操作结果的CompletableFuture
     */
    public <T> CompletableFuture<T> executeQueryOperationAsync(Supplier<T> operation) {
        return executeOperationAsync(operation, queryRateLimiter, "查询");
    }

    /**
     * 执行受速率限制的操作
     *
     * @param operation 操作函数
     * @param limiter 速率限制器
     * @param operationType 操作类型（用于日志）
     * @param <T> 返回类型
     * @return 操作结果
     * @throws Exception 如果操作执行失败
     */
    private <T> T executeOperation(Callable<T> operation, RateLimiter limiter, String operationType) throws Exception {
        try {
            return limiter.executeCallable(operation);
        } catch (RequestNotPermitted e) {
            log.warn("API {}请求被限流", operationType);
            // 尝试睡眠一段时间后重试
            try {
                Thread.sleep(1000); // 等待1秒
                return limiter.executeCallable(operation);
            } catch (RequestNotPermitted retryEx) {
                log.error("API {}请求重试后仍被限流", operationType);
                throw new RateLimitedException("API请求已达到速率限制，请稍后重试", retryEx);
            }
        }
    }

    /**
     * 异步执行受速率限制的操作
     *
     * @param operation 操作供应商
     * @param limiter 速率限制器
     * @param operationType 操作类型（用于日志）
     * @param <T> 返回类型
     * @return 包含操作结果的CompletableFuture
     */
    private <T> CompletableFuture<T> executeOperationAsync(Supplier<T> operation, RateLimiter limiter, String operationType) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return limiter.executeSupplier(operation);
            } catch (RequestNotPermitted e) {
                log.warn("API {}异步请求被限流", operationType);
                try {
                    Thread.sleep(1000); // 等待1秒
                    return limiter.executeSupplier(operation);
                } catch (RequestNotPermitted retryEx) {
                    log.error("API {}异步请求重试后仍被限流", operationType);
                    throw new RateLimitedException("API异步请求已达到速率限制，请稍后重试", retryEx);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new RateLimitedException("API异步请求等待中断", ie);
                }
            }
        });
    }
}