package com.crypto.trading.market.debug;

import com.crypto.trading.sdk.websocket.BinanceWebSocketClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;

/**
 * WebSocket消息调试器
 * 用于验证是否能够正常接收深度数据消息
 */
@SpringBootApplication
@ComponentScan(basePackages = "com.crypto.trading")
public class WebSocketMessageDebugger implements CommandLineRunner {

    private static final Logger log = LoggerFactory.getLogger(WebSocketMessageDebugger.class);

    @Autowired
    private BinanceWebSocketClient webSocketClient;

    public static void main(String[] args) {
        SpringApplication.run(WebSocketMessageDebugger.class, args);
    }

    @Override
    public void run(String... args) throws Exception {
        log.info("开始WebSocket消息调试...");
        
        // 创建消息计数器
        final int[] messageCount = {0};
        final CountDownLatch latch = new CountDownLatch(1);
        
        // 创建简单的消息处理器
        Consumer<String> messageHandler = message -> {
            messageCount[0]++;
            log.info("收到消息 #{}: length={}", messageCount[0], message.length());
            log.debug("消息内容: {}", message);
            
            // 收到5条消息后停止
            if (messageCount[0] >= 5) {
                latch.countDown();
            }
        };
        
        try {
            // 订阅BTCUSDT深度数据
            log.info("订阅BTCUSDT深度数据...");
            int connectionId = webSocketClient.subscribeDepth("btcusdt", 5, 100, messageHandler);
            log.info("订阅成功，连接ID: {}", connectionId);
            
            // 等待消息或超时
            boolean received = latch.await(30, TimeUnit.SECONDS);
            
            if (received) {
                log.info("成功接收到{}条消息", messageCount[0]);
            } else {
                log.warn("30秒内未收到任何消息");
            }
            
            // 关闭连接
            webSocketClient.closeConnection(connectionId);
            log.info("连接已关闭");
            
        } catch (Exception e) {
            log.error("WebSocket调试过程中发生异常", e);
        }
        
        log.info("WebSocket消息调试完成");
        System.exit(0);
    }
}
