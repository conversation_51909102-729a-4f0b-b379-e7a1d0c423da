package com.crypto.trading.market.consumer;

import com.crypto.trading.common.dto.market.KlineDataDTO;
import com.crypto.trading.market.repository.InfluxDBRepository;
import com.crypto.trading.market.repository.MarketDataRepository;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.annotation.RetryableTopic;
import org.springframework.kafka.retrytopic.TopicSuffixingStrategy;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.retry.annotation.Backoff;
import org.springframework.stereotype.Component;

import java.util.concurrent.atomic.AtomicLong;

/**
 * K线数据Kafka消费者
 * 专门负责消费原始K线数据并写入数据库
 * 使用Kafka的重试机制和死信队列确保数据可靠性
 */
@Component
public class KlineDataConsumer {

    private static final Logger log = LoggerFactory.getLogger(KlineDataConsumer.class);

    @Autowired
    private InfluxDBRepository influxDBRepository;

    @Autowired
    private MarketDataRepository mySQLMarketDataRepository;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 处理成功计数器
     */
    private final AtomicLong successCount = new AtomicLong(0);

    /**
     * 处理失败计数器
     */
    private final AtomicLong failureCount = new AtomicLong(0);

    /**
     * 消费K线原始数据并写入数据库
     * 使用重试机制和死信队列确保数据可靠性
     */
    @RetryableTopic(
            attempts = "3",
            backoff = @Backoff(delay = 1000, multiplier = 2.0),
            autoCreateTopics = "true",
            topicSuffixingStrategy = TopicSuffixingStrategy.SUFFIX_WITH_INDEX_VALUE,
            dltStrategy = org.springframework.kafka.retrytopic.DltStrategy.FAIL_ON_ERROR,
            include = {Exception.class}
    )
    @KafkaListener(
            topics = "kline.raw.data",
            groupId = "kline-database-consumer-group",
            containerFactory = "kafkaListenerContainerFactory"
    )
    public void consumeKlineData(
            @Payload String message,
            @Header(KafkaHeaders.RECEIVED_TOPIC) String topic,
            @Header(KafkaHeaders.RECEIVED_PARTITION) int partition,
            @Header(KafkaHeaders.OFFSET) long offset,
            ConsumerRecord<String, String> record,
            Acknowledgment acknowledgment) {

        String key = record.key();
        
        log.debug("接收到K线数据: topic={}, partition={}, offset={}, key={}", 
                topic, partition, offset, key);

        try {
            // 解析JSON消息
            KlineDataDTO klineDataDTO = objectMapper.readValue(message, KlineDataDTO.class);
            
            if (klineDataDTO == null) {
                log.warn("K线数据解析为空: key={}, message={}", key, message);
                acknowledgment.acknowledge();
                return;
            }

            // 写入InfluxDB
            boolean influxSuccess = writeToInfluxDB(klineDataDTO);
            
            // 写入MySQL
            boolean mysqlSuccess = writeToMySQL(klineDataDTO);

            // 只有两个数据库都写入成功才确认消息
            if (influxSuccess && mysqlSuccess) {
                acknowledgment.acknowledge();
                successCount.incrementAndGet();
                
                log.debug("K线数据写入数据库成功: symbol={}, interval={}, openTime={}", 
                        klineDataDTO.getSymbol(), klineDataDTO.getInterval(), klineDataDTO.getOpenTime());
            } else {
                // 如果任何一个数据库写入失败，抛出异常触发重试
                String errorMsg = String.format("K线数据写入数据库失败: symbol=%s, interval=%s, influxSuccess=%s, mysqlSuccess=%s",
                        klineDataDTO.getSymbol(), klineDataDTO.getInterval(), influxSuccess, mysqlSuccess);
                throw new RuntimeException(errorMsg);
            }

        } catch (Exception e) {
            failureCount.incrementAndGet();
            log.error("处理K线数据异常: topic={}, partition={}, offset={}, key={}, error={}", 
                    topic, partition, offset, key, e.getMessage(), e);
            
            // 重新抛出异常，触发重试机制
            throw new RuntimeException("处理K线数据失败", e);
        }
    }

    /**
     * 写入InfluxDB
     */
    private boolean writeToInfluxDB(KlineDataDTO klineDataDTO) {
        try {
            influxDBRepository.saveKlineData(klineDataDTO);
            return true;
        } catch (Exception e) {
            log.error("写入K线数据到InfluxDB失败: symbol={}, interval={}, error={}", 
                    klineDataDTO.getSymbol(), klineDataDTO.getInterval(), e.getMessage(), e);
            return false;
        }
    }

    /**
     * 写入MySQL
     */
    private boolean writeToMySQL(KlineDataDTO klineDataDTO) {
        try {
            mySQLMarketDataRepository.saveKlineData(klineDataDTO);
            return true;
        } catch (Exception e) {
            log.error("写入K线数据到MySQL失败: symbol={}, interval={}, error={}", 
                    klineDataDTO.getSymbol(), klineDataDTO.getInterval(), e.getMessage(), e);
            return false;
        }
    }

    /**
     * 处理死信队列消息
     */
    @KafkaListener(
            topics = "kline.raw.data-dlt",
            groupId = "kline-database-consumer-group-dlt"
    )
    public void handleDltMessage(
            @Payload String message,
            @Header(KafkaHeaders.RECEIVED_TOPIC) String topic,
            @Header(KafkaHeaders.RECEIVED_PARTITION) int partition,
            @Header(KafkaHeaders.OFFSET) long offset,
            ConsumerRecord<String, String> record,
            Acknowledgment acknowledgment) {

        String key = record.key();
        
        log.error("K线数据进入死信队列: topic={}, partition={}, offset={}, key={}, message={}", 
                topic, partition, offset, key, message);

        // 这里可以实现特殊的处理逻辑，比如：
        // 1. 发送告警通知
        // 2. 写入特殊的错误日志表
        // 3. 尝试其他恢复策略
        
        // 确认死信队列消息，避免重复处理
        acknowledgment.acknowledge();
    }

    /**
     * 获取处理成功计数
     */
    public long getSuccessCount() {
        return successCount.get();
    }

    /**
     * 获取处理失败计数
     */
    public long getFailureCount() {
        return failureCount.get();
    }

    /**
     * 重置计数器
     */
    public void resetCounters() {
        successCount.set(0);
        failureCount.set(0);
    }
}
