package com.crypto.trading.trade.repository.mapper.risk;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.crypto.trading.trade.model.entity.risk.RiskControlLogEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 风控日志数据访问接口
 * 
 * <p>提供风控日志的CRUD操作和自定义查询</p>
 */
@Mapper
public interface RiskControlLogMapper extends BaseMapper<RiskControlLogEntity> {

    /**
     * 根据信号ID查询风控日志
     *
     * @param signalId 信号ID
     * @return 风控日志列表
     */
    @Select("SELECT * FROM t_risk_control_log WHERE signal_id = #{signalId} ORDER BY triggered_time DESC")
    List<RiskControlLogEntity> findBySignalId(@Param("signalId") String signalId);

    /**
     * 根据订单ID查询风控日志
     *
     * @param orderId 订单ID
     * @return 风控日志列表
     */
    @Select("SELECT * FROM t_risk_control_log WHERE order_id = #{orderId} ORDER BY triggered_time DESC")
    List<RiskControlLogEntity> findByOrderId(@Param("orderId") String orderId);

    /**
     * 根据规则ID查询风控日志
     *
     * @param ruleId 规则ID
     * @param limit 限制数量
     * @return 风控日志列表
     */
    @Select("SELECT * FROM t_risk_control_log WHERE rule_id = #{ruleId} ORDER BY triggered_time DESC LIMIT #{limit}")
    List<RiskControlLogEntity> findByRuleId(@Param("ruleId") String ruleId, @Param("limit") int limit);

    /**
     * 根据风险类型查询风控日志
     *
     * @param riskType 风险类型
     * @param limit 限制数量
     * @return 风控日志列表
     */
    @Select("SELECT * FROM t_risk_control_log WHERE risk_type = #{riskType} ORDER BY triggered_time DESC LIMIT #{limit}")
    List<RiskControlLogEntity> findByRiskType(@Param("riskType") String riskType, @Param("limit") int limit);

    /**
     * 根据风险等级查询风控日志
     *
     * @param riskLevel 风险等级
     * @param limit 限制数量
     * @return 风控日志列表
     */
    @Select("SELECT * FROM t_risk_control_log WHERE risk_level = #{riskLevel} ORDER BY triggered_time DESC LIMIT #{limit}")
    List<RiskControlLogEntity> findByRiskLevel(@Param("riskLevel") String riskLevel, @Param("limit") int limit);

    /**
     * 查询触发的风控日志
     *
     * @param limit 限制数量
     * @return 风控日志列表
     */
    @Select("SELECT * FROM t_risk_control_log WHERE triggered = true ORDER BY triggered_time DESC LIMIT #{limit}")
    List<RiskControlLogEntity> findTriggeredRules(@Param("limit") int limit);

    /**
     * 根据时间范围查询风控日志
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param limit 限制数量
     * @return 风控日志列表
     */
    @Select("SELECT * FROM t_risk_control_log WHERE triggered_time BETWEEN #{startTime} AND #{endTime} " +
            "ORDER BY triggered_time DESC LIMIT #{limit}")
    List<RiskControlLogEntity> findByTimeRange(
            @Param("startTime") long startTime, 
            @Param("endTime") long endTime, 
            @Param("limit") int limit);
}