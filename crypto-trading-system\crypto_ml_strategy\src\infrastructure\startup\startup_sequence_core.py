"""
启动序列核心管理模块

提供crypto_ml_strategy项目的启动序列核心管理功能，包括启动阶段定义、
任务编排、流程控制和启动状态管理。

Author: Crypto ML Strategy Team
Date: 2024-12-20
Version: 1.0.0
"""

import asyncio
import time
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Dict, List, Optional, Callable, Set
from loguru import logger

from .error_handler_core import (
    CryptoMLException, ErrorCode, ErrorSeverity, ErrorCategory,
    ErrorContext, handle_exception
)


class StartupPhase(Enum):
    """启动阶段枚举"""
    PRE_STARTUP = "pre_startup"
    DEPENDENCY_CHECK = "dependency_check"
    CORE_INITIALIZATION = "core_initialization"
    SERVICE_STARTUP = "service_startup"
    POST_STARTUP = "post_startup"
    READY = "ready"
    FAILED = "failed"


class StartupTaskStatus(Enum):
    """启动任务状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"


class StartupTaskPriority(Enum):
    """启动任务优先级枚举"""
    CRITICAL = 1    # 关键任务，失败则启动失败
    HIGH = 2        # 高优先级任务
    NORMAL = 3      # 普通任务
    LOW = 4         # 低优先级任务
    OPTIONAL = 5    # 可选任务，失败不影响启动


@dataclass
class StartupTask:
    """启动任务定义"""
    name: str
    phase: StartupPhase
    priority: StartupTaskPriority
    task_func: Callable
    dependencies: Set[str] = field(default_factory=set)
    timeout: float = 30.0
    retry_count: int = 3
    parallel_group: Optional[str] = None
    description: str = ""
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    # 运行时状态
    status: StartupTaskStatus = StartupTaskStatus.PENDING
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    duration: Optional[float] = None
    error: Optional[Exception] = None
    result: Any = None
    
    def __post_init__(self):
        if not self.description:
            self.description = f"启动任务: {self.name}"
    
    def is_ready_to_run(self, completed_tasks: Set[str]) -> bool:
        """检查任务是否准备好运行"""
        return self.dependencies.issubset(completed_tasks)
    
    def can_run_in_parallel(self, other: 'StartupTask') -> bool:
        """检查是否可以与其他任务并行运行"""
        if self.parallel_group and other.parallel_group:
            return self.parallel_group == other.parallel_group
        return not (self.name in other.dependencies or other.name in self.dependencies)
    
    def get_duration(self) -> float:
        """获取任务执行时长"""
        if self.duration is not None:
            return self.duration
        if self.start_time and self.end_time:
            return (self.end_time - self.start_time).total_seconds()
        return 0.0


@dataclass
class StartupResult:
    """启动结果"""
    success: bool
    total_duration: float
    phase_durations: Dict[StartupPhase, float]
    task_results: Dict[str, StartupTask]
    error: Optional[Exception] = None
    warnings: List[str] = field(default_factory=list)
    
    def get_summary(self) -> Dict[str, Any]:
        """获取启动结果摘要"""
        total_tasks = len(self.task_results)
        completed_tasks = len([t for t in self.task_results.values() if t.status == StartupTaskStatus.COMPLETED])
        failed_tasks = len([t for t in self.task_results.values() if t.status == StartupTaskStatus.FAILED])
        
        return {
            "success": self.success,
            "total_duration": self.total_duration,
            "total_tasks": total_tasks,
            "completed_tasks": completed_tasks,
            "failed_tasks": failed_tasks,
            "success_rate": completed_tasks / total_tasks if total_tasks > 0 else 0,
            "phase_durations": {phase.value: duration for phase, duration in self.phase_durations.items()},
            "warnings_count": len(self.warnings),
            "error": str(self.error) if self.error else None
        }


class StartupTaskExecutor:
    """启动任务执行器"""
    
    def __init__(self, max_parallel_tasks: int = 5):
        self.max_parallel_tasks = max_parallel_tasks
        self.running_tasks: Dict[str, asyncio.Task] = {}
        self.semaphore = asyncio.Semaphore(max_parallel_tasks)
    
    async def execute_task(self, startup_task: StartupTask) -> bool:
        """执行单个启动任务"""
        async with self.semaphore:
            startup_task.start_time = datetime.now()
            startup_task.status = StartupTaskStatus.RUNNING
            
            logger.info(f"开始执行启动任务: {startup_task.name}")
            
            try:
                # 设置超时
                result = await asyncio.wait_for(
                    self._run_task_with_retry(startup_task),
                    timeout=startup_task.timeout
                )
                
                startup_task.result = result
                startup_task.status = StartupTaskStatus.COMPLETED
                startup_task.end_time = datetime.now()
                startup_task.duration = (startup_task.end_time - startup_task.start_time).total_seconds()
                
                logger.info(f"启动任务完成: {startup_task.name} ({startup_task.duration:.2f}s)")
                return True
                
            except asyncio.TimeoutError:
                startup_task.error = CryptoMLException(
                    f"启动任务超时: {startup_task.name}",
                    ErrorCode.NETWORK_TIMEOUT,
                    ErrorSeverity.ERROR,
                    ErrorCategory.SYSTEM_ERROR
                )
                startup_task.status = StartupTaskStatus.FAILED
                logger.error(f"启动任务超时: {startup_task.name}")
                return False
                
            except Exception as e:
                startup_task.error = e
                startup_task.status = StartupTaskStatus.FAILED
                startup_task.end_time = datetime.now()
                startup_task.duration = (startup_task.end_time - startup_task.start_time).total_seconds()
                
                logger.error(f"启动任务失败: {startup_task.name} - {e}")
                return False
    
    async def _run_task_with_retry(self, startup_task: StartupTask) -> Any:
        """带重试的任务执行"""
        last_error = None
        
        for attempt in range(startup_task.retry_count + 1):
            try:
                if asyncio.iscoroutinefunction(startup_task.task_func):
                    return await startup_task.task_func()
                else:
                    # 在线程池中执行同步函数
                    loop = asyncio.get_event_loop()
                    return await loop.run_in_executor(None, startup_task.task_func)
                    
            except Exception as e:
                last_error = e
                if attempt < startup_task.retry_count:
                    wait_time = 2 ** attempt  # 指数退避
                    logger.warning(f"启动任务重试 {attempt + 1}/{startup_task.retry_count}: {startup_task.name} - {e}")
                    await asyncio.sleep(wait_time)
                else:
                    logger.error(f"启动任务重试失败: {startup_task.name} - {e}")
        
        raise last_error


class StartupSequenceManager:
    """启动序列管理器"""
    
    def __init__(self, max_parallel_tasks: int = 5):
        self.tasks: Dict[str, StartupTask] = {}
        self.executor = StartupTaskExecutor(max_parallel_tasks)
        self.current_phase = StartupPhase.PRE_STARTUP
        self.start_time: Optional[datetime] = None
        self.end_time: Optional[datetime] = None
        self.phase_start_times: Dict[StartupPhase, datetime] = {}
        self.phase_durations: Dict[StartupPhase, float] = {}
        self.progress_callbacks: List[Callable] = []
        self.phase_callbacks: Dict[StartupPhase, List[Callable]] = {}
    
    def register_task(self, task: StartupTask) -> None:
        """注册启动任务"""
        if task.name in self.tasks:
            logger.warning(f"启动任务已存在，将被覆盖: {task.name}")
        
        self.tasks[task.name] = task
        logger.debug(f"注册启动任务: {task.name} (阶段: {task.phase.value})")
    
    def register_progress_callback(self, callback: Callable) -> None:
        """注册进度回调"""
        self.progress_callbacks.append(callback)
    
    def register_phase_callback(self, phase: StartupPhase, callback: Callable) -> None:
        """注册阶段回调"""
        if phase not in self.phase_callbacks:
            self.phase_callbacks[phase] = []
        self.phase_callbacks[phase].append(callback)
    
    async def start_sequence(self) -> StartupResult:
        """启动序列"""
        self.start_time = datetime.now()
        logger.info("🚀 开始启动序列")
        
        try:
            # 按阶段执行任务
            for phase in StartupPhase:
                if phase in [StartupPhase.READY, StartupPhase.FAILED]:
                    continue
                
                await self._execute_phase(phase)
                
                # 检查关键任务是否失败
                if self._has_critical_failures(phase):
                    self.current_phase = StartupPhase.FAILED
                    raise CryptoMLException(
                        f"启动阶段 {phase.value} 关键任务失败",
                        ErrorCode.RESOURCE_EXHAUSTED,
                        ErrorSeverity.CRITICAL,
                        ErrorCategory.SYSTEM_ERROR
                    )
            
            # 启动成功
            self.current_phase = StartupPhase.READY
            self.end_time = datetime.now()
            total_duration = (self.end_time - self.start_time).total_seconds()
            
            logger.info(f"✅ 启动序列完成 ({total_duration:.2f}s)")
            
            return StartupResult(
                success=True,
                total_duration=total_duration,
                phase_durations=self.phase_durations.copy(),
                task_results=self.tasks.copy()
            )
            
        except Exception as e:
            # 启动失败
            self.current_phase = StartupPhase.FAILED
            self.end_time = datetime.now()
            total_duration = (self.end_time - self.start_time).total_seconds()
            
            logger.error(f"❌ 启动序列失败 ({total_duration:.2f}s): {e}")
            
            return StartupResult(
                success=False,
                total_duration=total_duration,
                phase_durations=self.phase_durations.copy(),
                task_results=self.tasks.copy(),
                error=e
            )
    
    async def _execute_phase(self, phase: StartupPhase) -> None:
        """执行启动阶段"""
        self.current_phase = phase
        self.phase_start_times[phase] = datetime.now()
        
        logger.info(f"📋 执行启动阶段: {phase.value}")
        
        # 获取该阶段的任务
        phase_tasks = [task for task in self.tasks.values() if task.phase == phase]
        
        if not phase_tasks:
            logger.debug(f"阶段 {phase.value} 没有任务")
            self.phase_durations[phase] = 0.0
            return
        
        # 执行阶段回调
        for callback in self.phase_callbacks.get(phase, []):
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(phase)
                else:
                    callback(phase)
            except Exception as e:
                logger.error(f"阶段回调执行失败: {e}")
        
        # 按优先级和依赖关系执行任务
        await self._execute_tasks_in_phase(phase_tasks)
        
        # 计算阶段耗时
        phase_end_time = datetime.now()
        self.phase_durations[phase] = (phase_end_time - self.phase_start_times[phase]).total_seconds()
        
        logger.info(f"✅ 阶段完成: {phase.value} ({self.phase_durations[phase]:.2f}s)")
    
    async def _execute_tasks_in_phase(self, phase_tasks: List[StartupTask]) -> None:
        """执行阶段内的任务"""
        completed_tasks: Set[str] = set()
        remaining_tasks = {task.name: task for task in phase_tasks}
        
        while remaining_tasks:
            # 找到可以执行的任务
            ready_tasks = [
                task for task in remaining_tasks.values()
                if task.is_ready_to_run(completed_tasks)
            ]
            
            if not ready_tasks:
                # 检查是否有循环依赖
                logger.error(f"检测到循环依赖或无法满足的依赖: {list(remaining_tasks.keys())}")
                break
            
            # 按优先级排序
            ready_tasks.sort(key=lambda t: t.priority.value)
            
            # 并行执行任务
            tasks_to_run = []
            for task in ready_tasks:
                if len(tasks_to_run) < self.executor.max_parallel_tasks:
                    tasks_to_run.append(task)
                else:
                    break
            
            # 执行任务
            results = await asyncio.gather(
                *[self.executor.execute_task(task) for task in tasks_to_run],
                return_exceptions=True
            )
            
            # 处理结果
            for task, success in zip(tasks_to_run, results):
                if isinstance(success, Exception):
                    task.error = success
                    task.status = StartupTaskStatus.FAILED
                elif success:
                    completed_tasks.add(task.name)
                
                remaining_tasks.pop(task.name, None)
                
                # 调用进度回调
                for callback in self.progress_callbacks:
                    try:
                        if asyncio.iscoroutinefunction(callback):
                            await callback(task)
                        else:
                            callback(task)
                    except Exception as e:
                        logger.error(f"进度回调执行失败: {e}")
    
    def _has_critical_failures(self, phase: StartupPhase) -> bool:
        """检查是否有关键任务失败"""
        phase_tasks = [task for task in self.tasks.values() if task.phase == phase]
        
        for task in phase_tasks:
            if (task.priority == StartupTaskPriority.CRITICAL and 
                task.status == StartupTaskStatus.FAILED):
                return True
        
        return False
    
    def get_progress(self) -> Dict[str, Any]:
        """获取启动进度"""
        total_tasks = len(self.tasks)
        completed_tasks = len([t for t in self.tasks.values() if t.status == StartupTaskStatus.COMPLETED])
        running_tasks = len([t for t in self.tasks.values() if t.status == StartupTaskStatus.RUNNING])
        failed_tasks = len([t for t in self.tasks.values() if t.status == StartupTaskStatus.FAILED])
        
        progress_percentage = (completed_tasks / total_tasks * 100) if total_tasks > 0 else 0
        
        return {
            "current_phase": self.current_phase.value,
            "total_tasks": total_tasks,
            "completed_tasks": completed_tasks,
            "running_tasks": running_tasks,
            "failed_tasks": failed_tasks,
            "progress_percentage": progress_percentage,
            "phase_durations": {phase.value: duration for phase, duration in self.phase_durations.items()},
            "elapsed_time": (datetime.now() - self.start_time).total_seconds() if self.start_time else 0
        }


# 全局启动序列管理器
global_startup_manager = StartupSequenceManager()