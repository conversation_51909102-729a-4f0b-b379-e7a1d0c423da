"""
启动配置管理模块

提供crypto_ml_strategy项目的启动配置管理功能，包括配置加载、
验证、热更新和启动参数管理。

Author: Crypto ML Strategy Team
Date: 2024-12-20
Version: 1.0.0
"""

import json
import yaml
from dataclasses import dataclass, field, asdict
from pathlib import Path
from typing import Any, Dict, List, Optional, Set
from loguru import logger

from .startup_optimizer import OptimizationStrategy
from .error_handler_core import (
    CryptoMLException, ErrorCode, ErrorSeverity, ErrorCategory,
    ErrorContext, handle_exception
)


@dataclass
class StartupTimingConfig:
    """启动时序配置"""
    max_startup_time_seconds: float = 10.0
    dependency_check_timeout: float = 1.0
    resource_preload_timeout: float = 3.0
    service_startup_timeout: float = 3.0
    health_check_timeout: float = 2.0
    phase_transition_delay: float = 0.1


@dataclass
class ParallelizationConfig:
    """并行化配置"""
    max_parallel_tasks: int = 5
    max_parallel_preloaders: int = 3
    max_parallel_health_checks: int = 4
    enable_phase_parallelization: bool = True
    enable_task_parallelization: bool = True


@dataclass
class ResourceConfig:
    """资源配置"""
    max_memory_usage_mb: int = 2048
    max_cpu_usage_percent: float = 80.0
    min_available_memory_mb: int = 512
    min_available_disk_gb: float = 1.0
    enable_resource_monitoring: bool = True
    resource_check_interval: float = 1.0


@dataclass
class OptimizationConfig:
    """优化配置"""
    strategy: str = "adaptive"
    enable_startup_optimization: bool = True
    enable_performance_tuning: bool = True
    enable_adaptive_scheduling: bool = True
    task_timeout_multiplier: float = 1.5
    retry_failed_tasks: bool = True
    preload_critical_resources: bool = True


@dataclass
class MonitoringConfig:
    """监控配置"""
    enable_progress_monitoring: bool = True
    enable_performance_monitoring: bool = True
    enable_health_monitoring: bool = True
    progress_report_interval: float = 1.0
    performance_analysis_enabled: bool = True
    generate_startup_report: bool = True


@dataclass
class DependencyConfig:
    """依赖配置"""
    required_services: List[str] = field(default_factory=lambda: [
        "logger", "config", "error_handler"
    ])
    external_services: Dict[str, str] = field(default_factory=dict)
    required_files: List[str] = field(default_factory=list)
    required_directories: List[str] = field(default_factory=list)
    environment_variables: List[str] = field(default_factory=list)


@dataclass
class PreloadConfig:
    """预加载配置"""
    enable_model_preloading: bool = True
    enable_cache_prewarming: bool = True
    enable_config_preloading: bool = True
    model_paths: Dict[str, str] = field(default_factory=dict)
    cache_configs: Dict[str, Dict[str, Any]] = field(default_factory=dict)
    config_paths: List[str] = field(default_factory=list)


@dataclass
class HealthCheckConfig:
    """健康检查配置"""
    enable_system_readiness_check: bool = True
    enable_service_availability_check: bool = True
    enable_performance_validation: bool = True
    enable_smoke_tests: bool = True
    service_endpoints: Dict[str, str] = field(default_factory=dict)
    performance_thresholds: Dict[str, float] = field(default_factory=lambda: {
        "max_startup_time": 10.0,
        "max_memory_usage_mb": 2048.0,
        "min_cpu_cores": 1
    })


@dataclass
class StartupConfig:
    """启动总配置"""
    version: str = "1.0.0"
    enabled: bool = True
    timing: StartupTimingConfig = field(default_factory=StartupTimingConfig)
    parallelization: ParallelizationConfig = field(default_factory=ParallelizationConfig)
    resource: ResourceConfig = field(default_factory=ResourceConfig)
    optimization: OptimizationConfig = field(default_factory=OptimizationConfig)
    monitoring: MonitoringConfig = field(default_factory=MonitoringConfig)
    dependency: DependencyConfig = field(default_factory=DependencyConfig)
    preload: PreloadConfig = field(default_factory=PreloadConfig)
    health_check: HealthCheckConfig = field(default_factory=HealthCheckConfig)
    custom_settings: Dict[str, Any] = field(default_factory=dict)


class StartupConfigValidator:
    """启动配置验证器"""
    
    @staticmethod
    def validate_config(config: StartupConfig) -> List[str]:
        """验证配置"""
        errors = []
        
        # 验证时序配置
        if config.timing.max_startup_time_seconds <= 0:
            errors.append("最大启动时间必须大于0")
        
        if config.timing.dependency_check_timeout <= 0:
            errors.append("依赖检查超时时间必须大于0")
        
        # 验证并行化配置
        if config.parallelization.max_parallel_tasks <= 0:
            errors.append("最大并行任务数必须大于0")
        
        if config.parallelization.max_parallel_preloaders <= 0:
            errors.append("最大并行预加载器数必须大于0")
        
        # 验证资源配置
        if config.resource.max_memory_usage_mb <= 0:
            errors.append("最大内存使用量必须大于0")
        
        if config.resource.max_cpu_usage_percent <= 0 or config.resource.max_cpu_usage_percent > 100:
            errors.append("最大CPU使用率必须在0-100之间")
        
        if config.resource.min_available_memory_mb <= 0:
            errors.append("最小可用内存必须大于0")
        
        # 验证优化配置
        valid_strategies = ["parallel_first", "priority_first", "resource_aware", "adaptive", "conservative"]
        if config.optimization.strategy not in valid_strategies:
            errors.append(f"不支持的优化策略: {config.optimization.strategy}")
        
        if config.optimization.task_timeout_multiplier <= 0:
            errors.append("任务超时倍数必须大于0")
        
        # 验证监控配置
        if config.monitoring.progress_report_interval <= 0:
            errors.append("进度报告间隔必须大于0")
        
        # 验证预加载配置
        for model_name, model_path in config.preload.model_paths.items():
            if not model_path:
                errors.append(f"模型 {model_name} 的路径不能为空")
        
        # 验证健康检查配置
        for threshold_name, threshold_value in config.health_check.performance_thresholds.items():
            if threshold_value <= 0:
                errors.append(f"性能阈值 {threshold_name} 必须大于0")
        
        return errors


class StartupConfigManager:
    """启动配置管理器"""
    
    def __init__(self, config_file: Optional[str] = None):
        self.config_file = config_file or "config/startup.yaml"
        self.config: Optional[StartupConfig] = None
        self.config_watchers: List[callable] = []
        self._last_modified: Optional[float] = None
    
    def load_config(self, config_file: Optional[str] = None) -> StartupConfig:
        """加载配置"""
        file_path = config_file or self.config_file
        
        try:
            path = Path(file_path)
            
            if not path.exists():
                logger.warning(f"启动配置文件不存在: {file_path}，使用默认配置")
                self.config = StartupConfig()
                self._create_default_config_file(path)
                return self.config
            
            # 读取配置文件
            with open(path, 'r', encoding='utf-8') as f:
                if path.suffix.lower() in ['.yaml', '.yml']:
                    config_data = yaml.safe_load(f)
                elif path.suffix.lower() == '.json':
                    config_data = json.load(f)
                else:
                    raise ValueError(f"不支持的配置文件格式: {path.suffix}")
            
            # 转换为配置对象
            self.config = self._dict_to_config(config_data)
            
            # 验证配置
            errors = StartupConfigValidator.validate_config(self.config)
            if errors:
                logger.error(f"启动配置验证失败: {errors}")
                raise ValueError(f"配置验证失败: {'; '.join(errors)}")
            
            # 记录文件修改时间
            self._last_modified = path.stat().st_mtime
            
            logger.info(f"成功加载启动配置文件: {file_path}")
            return self.config
            
        except Exception as e:
            logger.error(f"加载启动配置文件失败: {e}")
            # 使用默认配置
            self.config = StartupConfig()
            return self.config
    
    def save_config(self, config: Optional[StartupConfig] = None, file_path: Optional[str] = None) -> bool:
        """保存配置"""
        try:
            config_to_save = config or self.config
            if not config_to_save:
                logger.error("没有配置可保存")
                return False
            
            file_path = file_path or self.config_file
            path = Path(file_path)
            
            # 确保目录存在
            path.parent.mkdir(parents=True, exist_ok=True)
            
            # 转换为字典
            config_dict = asdict(config_to_save)
            
            # 保存文件
            with open(path, 'w', encoding='utf-8') as f:
                if path.suffix.lower() in ['.yaml', '.yml']:
                    yaml.dump(config_dict, f, default_flow_style=False, allow_unicode=True)
                elif path.suffix.lower() == '.json':
                    json.dump(config_dict, f, indent=2, ensure_ascii=False)
                else:
                    raise ValueError(f"不支持的配置文件格式: {path.suffix}")
            
            logger.info(f"启动配置已保存到: {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"保存启动配置失败: {e}")
            return False
    
    def _dict_to_config(self, config_data: Dict[str, Any]) -> StartupConfig:
        """将字典转换为配置对象"""
        # 处理嵌套配置
        timing_data = config_data.get('timing', {})
        parallelization_data = config_data.get('parallelization', {})
        resource_data = config_data.get('resource', {})
        optimization_data = config_data.get('optimization', {})
        monitoring_data = config_data.get('monitoring', {})
        dependency_data = config_data.get('dependency', {})
        preload_data = config_data.get('preload', {})
        health_check_data = config_data.get('health_check', {})
        
        return StartupConfig(
            version=config_data.get('version', '1.0.0'),
            enabled=config_data.get('enabled', True),
            timing=StartupTimingConfig(**timing_data),
            parallelization=ParallelizationConfig(**parallelization_data),
            resource=ResourceConfig(**resource_data),
            optimization=OptimizationConfig(**optimization_data),
            monitoring=MonitoringConfig(**monitoring_data),
            dependency=DependencyConfig(**dependency_data),
            preload=PreloadConfig(**preload_data),
            health_check=HealthCheckConfig(**health_check_data),
            custom_settings=config_data.get('custom_settings', {})
        )
    
    def _create_default_config_file(self, path: Path) -> None:
        """创建默认配置文件"""
        try:
            # 确保目录存在
            path.parent.mkdir(parents=True, exist_ok=True)
            
            # 创建默认配置
            default_config = StartupConfig()
            
            # 添加示例配置
            default_config.dependency.external_services = {
                "kafka": "localhost:9092",
                "mysql": "localhost:3306",
                "influxdb": "localhost:8086"
            }
            
            default_config.preload.model_paths = {
                "deepseek_model": "models/deepseek_distilled.pkl",
                "technical_indicators": "models/technical_indicators.pkl"
            }
            
            default_config.health_check.service_endpoints = {
                "kafka": "localhost:9092",
                "mysql": "localhost:3306"
            }
            
            # 保存默认配置
            self.save_config(default_config, str(path))
            
            logger.info(f"已创建默认启动配置文件: {path}")
            
        except Exception as e:
            logger.error(f"创建默认启动配置文件失败: {e}")
    
    def get_config(self) -> StartupConfig:
        """获取当前配置"""
        if not self.config:
            return self.load_config()
        return self.config
    
    def update_config(self, updates: Dict[str, Any]) -> bool:
        """更新配置"""
        try:
            if not self.config:
                self.load_config()
            
            # 应用更新
            config_dict = asdict(self.config)
            self._deep_update(config_dict, updates)
            
            # 重新创建配置对象
            self.config = self._dict_to_config(config_dict)
            
            # 验证配置
            errors = StartupConfigValidator.validate_config(self.config)
            if errors:
                logger.error(f"配置更新验证失败: {errors}")
                return False
            
            # 保存配置
            return self.save_config()
            
        except Exception as e:
            logger.error(f"更新启动配置失败: {e}")
            return False
    
    def _deep_update(self, base_dict: Dict[str, Any], update_dict: Dict[str, Any]) -> None:
        """深度更新字典"""
        for key, value in update_dict.items():
            if key in base_dict and isinstance(base_dict[key], dict) and isinstance(value, dict):
                self._deep_update(base_dict[key], value)
            else:
                base_dict[key] = value
    
    def get_optimization_strategy(self) -> OptimizationStrategy:
        """获取优化策略枚举"""
        strategy_map = {
            "parallel_first": OptimizationStrategy.PARALLEL_FIRST,
            "priority_first": OptimizationStrategy.PRIORITY_FIRST,
            "resource_aware": OptimizationStrategy.RESOURCE_AWARE,
            "adaptive": OptimizationStrategy.ADAPTIVE,
            "conservative": OptimizationStrategy.CONSERVATIVE
        }
        
        strategy_name = self.get_config().optimization.strategy
        return strategy_map.get(strategy_name, OptimizationStrategy.ADAPTIVE)
    
    def get_timing_config(self) -> Dict[str, float]:
        """获取时序配置"""
        timing = self.get_config().timing
        return {
            "max_startup_time": timing.max_startup_time_seconds,
            "dependency_check_timeout": timing.dependency_check_timeout,
            "resource_preload_timeout": timing.resource_preload_timeout,
            "service_startup_timeout": timing.service_startup_timeout,
            "health_check_timeout": timing.health_check_timeout
        }
    
    def get_parallelization_config(self) -> Dict[str, Any]:
        """获取并行化配置"""
        parallel = self.get_config().parallelization
        return {
            "max_parallel_tasks": parallel.max_parallel_tasks,
            "max_parallel_preloaders": parallel.max_parallel_preloaders,
            "max_parallel_health_checks": parallel.max_parallel_health_checks,
            "enable_phase_parallelization": parallel.enable_phase_parallelization,
            "enable_task_parallelization": parallel.enable_task_parallelization
        }
    
    def get_resource_limits(self) -> Dict[str, Any]:
        """获取资源限制"""
        resource = self.get_config().resource
        return {
            "max_memory_usage_mb": resource.max_memory_usage_mb,
            "max_cpu_usage_percent": resource.max_cpu_usage_percent,
            "min_available_memory_mb": resource.min_available_memory_mb,
            "min_available_disk_gb": resource.min_available_disk_gb
        }
    
    def export_config_template(self, file_path: str) -> bool:
        """导出配置模板"""
        try:
            template_config = StartupConfig()
            
            # 添加注释信息
            config_dict = asdict(template_config)
            config_dict['_comments'] = {
                'version': '配置文件版本',
                'enabled': '是否启用启动优化',
                'timing': '启动时序配置',
                'parallelization': '并行化配置',
                'resource': '资源配置',
                'optimization': '优化配置',
                'monitoring': '监控配置',
                'dependency': '依赖配置',
                'preload': '预加载配置',
                'health_check': '健康检查配置',
                'custom_settings': '自定义设置'
            }
            
            path = Path(file_path)
            path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(path, 'w', encoding='utf-8') as f:
                yaml.dump(config_dict, f, default_flow_style=False, allow_unicode=True)
            
            logger.info(f"启动配置模板已导出到: {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"导出启动配置模板失败: {e}")
            return False
    
    def validate_current_config(self) -> Dict[str, Any]:
        """验证当前配置"""
        if not self.config:
            return {"valid": False, "errors": ["配置未加载"]}
        
        errors = StartupConfigValidator.validate_config(self.config)
        
        return {
            "valid": len(errors) == 0,
            "errors": errors,
            "config_version": self.config.version,
            "validation_time": datetime.now().isoformat()
        }
    
    def get_config_summary(self) -> Dict[str, Any]:
        """获取配置摘要"""
        if not self.config:
            return {"status": "not_loaded"}
        
        return {
            "version": self.config.version,
            "enabled": self.config.enabled,
            "max_startup_time": self.config.timing.max_startup_time_seconds,
            "max_parallel_tasks": self.config.parallelization.max_parallel_tasks,
            "optimization_strategy": self.config.optimization.strategy,
            "monitoring_enabled": self.config.monitoring.enable_progress_monitoring,
            "health_checks_enabled": self.config.health_check.enable_system_readiness_check,
            "preloading_enabled": self.config.preload.enable_model_preloading,
            "total_external_services": len(self.config.dependency.external_services),
            "total_model_paths": len(self.config.preload.model_paths)
        }


# 全局启动配置管理器
global_startup_config_manager = StartupConfigManager()