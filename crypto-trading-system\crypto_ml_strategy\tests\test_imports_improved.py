#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
改进的导入测试脚本

逐个测试每个模块的导入，提供详细的错误信息和诊断。
"""

import sys
import os
import traceback

def setup_python_path():
    """设置Python路径"""
    project_root = os.path.dirname(os.path.abspath(__file__))
    if project_root not in sys.path:
        sys.path.insert(0, project_root)
    
    print(f"项目根目录: {project_root}")
    print(f"Python路径: {sys.path[:3]}...")  # 只显示前3个路径
    print()

def test_single_import(module_name, class_name=None):
    """测试单个模块导入"""
    try:
        if class_name:
            exec(f"from {module_name} import {class_name}")
            print(f"[OK] {module_name}.{class_name}")
        else:
            exec(f"import {module_name}")
            print(f"[OK] {module_name}")
        return True
    except ImportError as e:
        print(f"[FAIL] {module_name}: ImportError - {e}")
        return False
    except Exception as e:
        print(f"[ERROR] {module_name}: {type(e).__name__} - {e}")
        return False

def test_imports_step_by_step():
    """分步测试导入"""
    
    print("=== 分步导入测试 ===")
    setup_python_path()
    
    success_count = 0
    total_count = 0
    
    # 测试列表：(模块名, 类名)
    test_modules = [
        # 基础模块
        ("src.config", "Config"),
        ("src.logger", "setup_logger"),
        
        # 工具模块
        ("src.kafka_client", "KafkaClient"),
        ("src.data_processor", "DataProcessor"),
        
        # 数据模块
        ("src.data.influxdb_client", "InfluxDBClient"),
        ("src.data.mysql_client", "MySQLClient"),
        ("src.data.real_data_loader", "RealDataLoader"),
        
        # 模型模块
        ("src.model.model_trainer", "ModelTrainer"),
        ("src.model.prediction", "PredictionEngine"),
        ("src.model.online_learner", "OnlineLearner"),
        ("src.model.versioning", "ModelVersionManager"),
        
        # 策略模块
        ("src.strategy.base", "BaseStrategy"),
        ("src.strategy.unified_ml", "UnifiedMLStrategy"),
        
        # 指标模块
        ("src.indicators.lppl_features", "LPPLFeatureExtractor"),
        ("src.indicators.hematread_features", "HematreadFeatureExtractor"),
        ("src.indicators.bull_market_support", "BullMarketSupportFeatureExtractor"),
        ("src.indicators.super_trend", "SuperTrendCalculator"),
        
        # 主模块
        ("src.main", "TradingStrategyApp"),
    ]
    
    for module_name, class_name in test_modules:
        total_count += 1
        if test_single_import(module_name, class_name):
            success_count += 1
    
    print()
    print(f"=== 测试结果 ===")
    print(f"成功: {success_count}/{total_count}")
    print(f"成功率: {success_count/total_count*100:.1f}%")
    
    return success_count == total_count

def test_main_instantiation():
    """测试主应用类实例化"""
    print("\n=== 主应用实例化测试 ===")
    
    try:
        from src.main import TradingStrategyApp
        from src.config import Config
        
        # 创建配置对象
        config = Config()
        print("[OK] Config对象创建成功")
        
        # 尝试创建TradingStrategyApp实例（可能会因为数据库连接失败）
        try:
            app = TradingStrategyApp()
            print("[OK] TradingStrategyApp实例化成功")
            return True
        except Exception as e:
            print(f"[EXPECTED_FAIL] TradingStrategyApp实例化失败（预期，因为数据库连接）: {e}")
            return True  # 这是预期的失败，因为没有真实的数据库连接
            
    except Exception as e:
        print(f"[FAIL] 主应用测试失败: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始导入测试...")
    
    # 分步测试
    imports_ok = test_imports_step_by_step()
    
    # 主应用测试
    main_ok = test_main_instantiation()
    
    print(f"\n=== 最终结果 ===")
    print(f"导入测试: {'通过' if imports_ok else '失败'}")
    print(f"主应用测试: {'通过' if main_ok else '失败'}")
    
    if imports_ok and main_ok:
        print("✅ 所有测试通过！")
        sys.exit(0)
    else:
        print("❌ 部分测试失败！")
        sys.exit(1)