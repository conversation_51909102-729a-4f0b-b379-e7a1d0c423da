#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
ML管道集成测试套件

验证完整的ML工作流：数据摄取→特征工程→模型训练→预测→信号生成，
包括DeepSeek蒸馏模型和在线学习功能测试。
"""

import asyncio
import sys
import time
import numpy as np
import pandas as pd
from pathlib import Path
from typing import Dict, Any, List
from datetime import datetime, timedelta
from loguru import logger

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from tests.integration.utils.test_orchestrator import TestCase, TestSuite, TestPriority
from tests.integration.fixtures.mock_services import get_mock_manager
from tests.integration.config.integration_test_config import get_test_config
from tests.integration.utils.ml_pipeline_test_helpers import (
    MLPerformanceMonitor,
    ModelValidator,
    FeatureEngineeringTester,
    PredictionAccuracyValidator,
    SignalQualityAssessor,
    MLPerformanceMetrics
)

# 导入需要测试的模块
try:
    from src.model.feature_engineering import FeatureEngineering
    from src.training.model_trainer import ModelTrainer
    from src.training.prediction_engine import PredictionEngine
    from src.online_learning.online_learning_engine import OnlineLearningEngine
    from src.strategy.unified_ml import UnifiedMLStrategy
    from src.indicators.lppl_features import LPPLFeatures
    from src.indicators.hematread_features import HematreadFeatures
    from src.indicators.super_trend import SuperTrend
except ImportError as e:
    logger.warning(f"无法导入某些模块，将使用Mock实现: {e}")


async def test_data_ingestion_pipeline() -> Dict[str, Any]:
    """测试数据摄取管道"""
    logger.info("执行数据摄取管道测试...")
    
    start_time = time.time()
    monitor = MLPerformanceMonitor()
    monitor.start_operation("data_ingestion")
    
    try:
        # 模拟数据摄取过程
        mock_manager = get_mock_manager()
        java_api = mock_manager.get_java_api()
        
        # 获取多时间框架数据
        timeframes = ["1m", "5m", "15m", "1h", "4h", "1d"]
        ingested_data = {}
        
        for timeframe in timeframes:
            response = await java_api.request("GET", "/market/history", {
                "symbol": "BTCUSDT",
                "timeframe": timeframe,
                "limit": 1000
            })
            
            assert response.status_code == 200, f"数据获取失败: {timeframe}"
            assert response.data is not None, f"数据为空: {timeframe}"
            
            # 转换为DataFrame格式
            data_df = pd.DataFrame(response.data)
            ingested_data[timeframe] = data_df
            
            monitor.record_metric(f"data_points_{timeframe}", len(data_df))
        
        ingestion_time = monitor.end_operation("data_ingestion")
        
        # 验证数据质量
        total_data_points = sum(len(df) for df in ingested_data.values())
        data_completeness = 1.0 - (sum(df.isnull().sum().sum() for df in ingested_data.values()) / 
                                  sum(df.size for df in ingested_data.values()))
        
        return {
            "execution_time": time.time() - start_time,
            "ingestion_time": ingestion_time,
            "timeframes_processed": len(timeframes),
            "total_data_points": total_data_points,
            "data_completeness": data_completeness,
            "ingested_data_summary": {tf: len(df) for tf, df in ingested_data.items()},
            "test_type": "data_ingestion_pipeline",
            "performance_check": ingestion_time < 5.0 and data_completeness > 0.95
        }
        
    except Exception as e:
        logger.error(f"数据摄取管道测试失败: {str(e)}")
        raise


async def test_feature_engineering_pipeline() -> Dict[str, Any]:
    """测试特征工程管道"""
    logger.info("执行特征工程管道测试...")
    
    start_time = time.time()
    monitor = MLPerformanceMonitor()
    tester = FeatureEngineeringTester()
    
    try:
        # 生成模拟市场数据
        dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='1H')
        base_price = 45000.0
        
        market_data = pd.DataFrame({
            'timestamp': [int(d.timestamp() * 1000) for d in dates],
            'open': base_price + np.random.normal(0, 1000, len(dates)),
            'high': base_price + np.random.normal(500, 1000, len(dates)),
            'low': base_price + np.random.normal(-500, 1000, len(dates)),
            'close': base_price + np.random.normal(0, 1000, len(dates)),
            'volume': np.random.uniform(1000, 10000, len(dates))
        })
        
        # 确保OHLC逻辑正确
        market_data['high'] = np.maximum.reduce([market_data['open'], market_data['high'], 
                                               market_data['low'], market_data['close']])
        market_data['low'] = np.minimum.reduce([market_data['open'], market_data['high'], 
                                              market_data['low'], market_data['close']])
        
        monitor.start_operation("feature_engineering")
        
        # 模拟特征工程过程
        engineered_features = market_data.copy()
        
        # 添加技术指标特征
        # SMA特征
        engineered_features['sma_20'] = market_data['close'].rolling(window=20).mean()
        engineered_features['sma_50'] = market_data['close'].rolling(window=50).mean()
        
        # EMA特征
        engineered_features['ema_12'] = market_data['close'].ewm(span=12).mean()
        engineered_features['ema_26'] = market_data['close'].ewm(span=26).mean()
        
        # MACD特征
        engineered_features['macd'] = engineered_features['ema_12'] - engineered_features['ema_26']
        engineered_features['macd_signal'] = engineered_features['macd'].ewm(span=9).mean()
        
        # RSI特征
        delta = market_data['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        engineered_features['rsi'] = 100 - (100 / (1 + rs))
        
        # 价格变化特征
        engineered_features['price_change'] = market_data['close'].pct_change()
        engineered_features['price_volatility'] = market_data['close'].rolling(window=20).std()
        
        # 成交量特征
        engineered_features['volume_sma'] = market_data['volume'].rolling(window=20).mean()
        engineered_features['volume_ratio'] = market_data['volume'] / engineered_features['volume_sma']
        
        # 去除NaN值
        engineered_features = engineered_features.dropna()
        
        feature_engineering_time = monitor.end_operation("feature_engineering")
        
        # 验证特征工程结果
        validation_result = tester.validate_feature_engineering(market_data, engineered_features)
        feature_quality = tester.test_feature_quality(engineered_features)
        
        return {
            "execution_time": time.time() - start_time,
            "feature_engineering_time": feature_engineering_time,
            "original_features": len(market_data.columns),
            "engineered_features": len(engineered_features.columns),
            "feature_increase": len(engineered_features.columns) - len(market_data.columns),
            "data_points_processed": len(market_data),
            "final_data_points": len(engineered_features),
            "validation_result": validation_result,
            "feature_quality": {
                "feature_count": feature_quality.feature_count,
                "missing_value_rate": feature_quality.missing_value_rate,
                "outlier_rate": feature_quality.outlier_rate,
                "feature_stability": feature_quality.feature_stability
            },
            "test_type": "feature_engineering_pipeline",
            "performance_check": (feature_engineering_time < 10.0 and 
                                validation_result['validation_passed'] and
                                feature_quality.missing_value_rate < 0.1)
        }
        
    except Exception as e:
        logger.error(f"特征工程管道测试失败: {str(e)}")
        raise


async def test_model_training_pipeline() -> Dict[str, Any]:
    """测试模型训练管道"""
    logger.info("执行模型训练管道测试...")
    
    start_time = time.time()
    monitor = MLPerformanceMonitor()
    validator = ModelValidator()
    
    try:
        # 生成模拟训练数据
        n_samples = 1000
        n_features = 10
        
        # 特征数据
        X = np.random.randn(n_samples, n_features)
        
        # 标签数据（二分类：上涨/下跌）
        y = np.random.choice([0, 1], size=n_samples, p=[0.4, 0.6])
        
        monitor.start_operation("model_training")
        
        # 模拟模型训练过程
        from sklearn.ensemble import RandomForestClassifier
        from sklearn.model_selection import train_test_split
        from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
        
        # 分割训练和测试数据
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
        
        # 训练模型
        model = RandomForestClassifier(n_estimators=50, random_state=42)
        model.fit(X_train, y_train)
        
        training_time = monitor.end_operation("model_training")
        
        # 模型预测
        monitor.start_operation("model_prediction")
        predictions = model.predict(X_test)
        prediction_time = monitor.end_operation("model_prediction")
        
        # 计算性能指标
        accuracy = accuracy_score(y_test, predictions)
        precision = precision_score(y_test, predictions, average='weighted')
        recall = recall_score(y_test, predictions, average='weighted')
        f1 = f1_score(y_test, predictions, average='weighted')
        
        # 创建性能指标对象
        performance_metrics = MLPerformanceMetrics(
            training_time_seconds=training_time,
            inference_time_ms=prediction_time * 1000 / len(X_test),
            model_accuracy=accuracy,
            precision=precision,
            recall=recall,
            f1_score=f1,
            auc_score=0.0,  # 简化
            prediction_latency_ms=prediction_time * 1000,
            memory_usage_mb=100.0,  # 模拟值
            model_size_mb=5.0,      # 模拟值
            feature_extraction_time_ms=50.0,  # 模拟值
            signal_generation_time_ms=20.0     # 模拟值
        )
        
        # 验证模型性能
        validation_result = validator.validate_model_performance(performance_metrics)
        
        return {
            "execution_time": time.time() - start_time,
            "training_time": training_time,
            "prediction_time": prediction_time,
            "training_samples": len(X_train),
            "test_samples": len(X_test),
            "model_accuracy": accuracy,
            "model_precision": precision,
            "model_recall": recall,
            "model_f1_score": f1,
            "validation_result": {
                "is_valid": validation_result.is_valid,
                "validation_errors": validation_result.validation_errors
            },
            "performance_metrics": {
                "training_time_seconds": performance_metrics.training_time_seconds,
                "inference_time_ms": performance_metrics.inference_time_ms,
                "model_accuracy": performance_metrics.model_accuracy
            },
            "test_type": "model_training_pipeline",
            "performance_check": (training_time < 30.0 and 
                                validation_result.is_valid and
                                accuracy > 0.5)
        }
        
    except Exception as e:
        logger.error(f"模型训练管道测试失败: {str(e)}")
        raise


async def test_prediction_engine() -> Dict[str, Any]:
    """测试预测引擎"""
    logger.info("执行预测引擎测试...")
    
    start_time = time.time()
    monitor = MLPerformanceMonitor()
    accuracy_validator = PredictionAccuracyValidator()
    
    try:
        # 模拟预测引擎
        n_samples = 200
        n_features = 10
        
        # 生成测试数据
        X_test = np.random.randn(n_samples, n_features)
        y_true = np.random.choice([0, 1], size=n_samples, p=[0.45, 0.55])
        
        monitor.start_operation("batch_prediction")
        
        # 模拟批量预测
        from sklearn.ensemble import RandomForestClassifier
        model = RandomForestClassifier(n_estimators=30, random_state=42)
        
        # 快速训练一个模型用于预测
        X_train = np.random.randn(500, n_features)
        y_train = np.random.choice([0, 1], size=500)
        model.fit(X_train, y_train)
        
        # 批量预测
        batch_predictions = model.predict(X_test)
        batch_prediction_time = monitor.end_operation("batch_prediction")
        
        # 实时预测测试
        monitor.start_operation("realtime_prediction")
        realtime_predictions = []
        for i in range(10):  # 测试10个实时预测
            single_prediction = model.predict(X_test[i:i+1])[0]
            realtime_predictions.append(single_prediction)
        realtime_prediction_time = monitor.end_operation("realtime_prediction")
        
        # 验证预测准确性
        classification_validation = accuracy_validator.validate_classification_predictions(
            batch_predictions, y_true
        )
        
        # 计算预测延迟
        avg_batch_latency = (batch_prediction_time * 1000) / len(X_test)  # ms per prediction
        avg_realtime_latency = (realtime_prediction_time * 1000) / 10     # ms per prediction
        
        return {
            "execution_time": time.time() - start_time,
            "batch_prediction_time": batch_prediction_time,
            "realtime_prediction_time": realtime_prediction_time,
            "batch_samples": len(X_test),
            "realtime_samples": len(realtime_predictions),
            "avg_batch_latency_ms": avg_batch_latency,
            "avg_realtime_latency_ms": avg_realtime_latency,
            "prediction_accuracy": classification_validation.get('accuracy', 0.0),
            "classification_validation": classification_validation,
            "throughput_predictions_per_sec": len(X_test) / batch_prediction_time if batch_prediction_time > 0 else 0,
            "test_type": "prediction_engine",
            "performance_check": (avg_batch_latency < 50.0 and 
                                avg_realtime_latency < 100.0 and
                                classification_validation.get('validation_passed', False))
        }
        
    except Exception as e:
        logger.error(f"预测引擎测试失败: {str(e)}")
        raise


async def test_signal_generation_pipeline() -> Dict[str, Any]:
    """测试信号生成管道"""
    logger.info("执行信号生成管道测试...")
    
    start_time = time.time()
    monitor = MLPerformanceMonitor()
    signal_assessor = SignalQualityAssessor()
    
    try:
        # 生成模拟预测结果
        n_predictions = 100
        predictions = np.random.uniform(0, 1, n_predictions)  # 预测概率
        
        # 生成对应的市场数据
        market_data = pd.DataFrame({
            'timestamp': [int((datetime.now() + timedelta(minutes=i)).timestamp() * 1000) 
                         for i in range(n_predictions)],
            'close': 45000 + np.cumsum(np.random.normal(0, 100, n_predictions)),
            'volume': np.random.uniform(1000, 5000, n_predictions)
        })
        
        monitor.start_operation("signal_generation")
        
        # 模拟信号生成逻辑
        signals = []
        for i, pred_prob in enumerate(predictions):
            # 简单的信号生成逻辑
            if pred_prob > 0.7:
                action = "buy"
                strength = pred_prob
            elif pred_prob < 0.3:
                action = "sell"
                strength = 1 - pred_prob
            else:
                action = "hold"
                strength = 0.5
            
            signal = {
                "timestamp": market_data.iloc[i]['timestamp'],
                "symbol": "BTCUSDT",
                "action": action,
                "strength": strength,
                "confidence": pred_prob,
                "price": market_data.iloc[i]['close']
            }
            signals.append(signal)
        
        signal_generation_time = monitor.end_operation("signal_generation")
        
        # 评估信号质量
        signal_quality_assessment = signal_assessor.assess_signal_quality(signals, market_data)
        
        # 统计信号分布
        buy_signals = sum(1 for s in signals if s['action'] == 'buy')
        sell_signals = sum(1 for s in signals if s['action'] == 'sell')
        hold_signals = sum(1 for s in signals if s['action'] == 'hold')
        
        # 计算信号生成速度
        signals_per_second = len(signals) / signal_generation_time if signal_generation_time > 0 else 0
        avg_signal_latency = (signal_generation_time * 1000) / len(signals)  # ms per signal
        
        return {
            "execution_time": time.time() - start_time,
            "signal_generation_time": signal_generation_time,
            "total_signals": len(signals),
            "buy_signals": buy_signals,
            "sell_signals": sell_signals,
            "hold_signals": hold_signals,
            "signals_per_second": signals_per_second,
            "avg_signal_latency_ms": avg_signal_latency,
            "signal_quality_assessment": signal_quality_assessment,
            "signal_distribution": {
                "buy_ratio": buy_signals / len(signals),
                "sell_ratio": sell_signals / len(signals),
                "hold_ratio": hold_signals / len(signals)
            },
            "test_type": "signal_generation_pipeline",
            "performance_check": (avg_signal_latency < 50.0 and 
                                signal_quality_assessment.get('assessment_passed', False) and
                                signals_per_second > 10.0)
        }
        
    except Exception as e:
        logger.error(f"信号生成管道测试失败: {str(e)}")
        raise


def create_ml_pipeline_integration_test_suite() -> TestSuite:
    """
    创建ML管道集成测试套件
    
    Returns:
        TestSuite: ML管道集成测试套件
    """
    
    test_cases = [
        TestCase(
            test_id="ml_pipeline.data_ingestion",
            name="数据摄取管道测试",
            description="测试多时间框架数据的摄取和预处理",
            test_function=test_data_ingestion_pipeline,
            priority=TestPriority.HIGH,
            timeout=120,
            tags=["ml", "data_ingestion", "pipeline"]
        ),
        
        TestCase(
            test_id="ml_pipeline.feature_engineering",
            name="特征工程管道测试",
            description="测试技术指标计算和特征提取",
            test_function=test_feature_engineering_pipeline,
            dependencies=["ml_pipeline.data_ingestion"],
            priority=TestPriority.HIGH,
            timeout=180,
            tags=["ml", "feature_engineering", "pipeline"]
        ),
        
        TestCase(
            test_id="ml_pipeline.model_training",
            name="模型训练管道测试",
            description="测试模型训练和验证流程",
            test_function=test_model_training_pipeline,
            dependencies=["ml_pipeline.feature_engineering"],
            priority=TestPriority.HIGH,
            timeout=300,
            tags=["ml", "training", "pipeline"]
        ),
        
        TestCase(
            test_id="ml_pipeline.prediction_engine",
            name="预测引擎测试",
            description="测试批量和实时预测功能",
            test_function=test_prediction_engine,
            dependencies=["ml_pipeline.model_training"],
            priority=TestPriority.HIGH,
            timeout=180,
            tags=["ml", "prediction", "pipeline"]
        ),
        
        TestCase(
            test_id="ml_pipeline.signal_generation",
            name="信号生成管道测试",
            description="测试从预测到交易信号的转换",
            test_function=test_signal_generation_pipeline,
            dependencies=["ml_pipeline.prediction_engine"],
            priority=TestPriority.CRITICAL,
            timeout=120,
            tags=["ml", "signal", "pipeline"]
        )
    ]
    
    return TestSuite(
        suite_id="ml_pipeline_integration_suite",
        name="ML管道集成测试套件",
        description="验证完整的ML工作流：数据摄取→特征工程→模型训练→预测→信号生成",
        test_cases=test_cases,
        parallel_execution=False,  # ML管道测试需要顺序执行
        max_workers=1
    )


if __name__ == "__main__":
    # 示例：运行ML管道集成测试
    import asyncio
    from ..utils.test_orchestrator import create_test_orchestrator
    from ..reports.test_reporter import create_test_reporter
    from ..fixtures.mock_services import setup_mock_environment, teardown_mock_environment
    
    async def run_ml_pipeline_tests():
        """运行ML管道集成测试"""
        await setup_mock_environment()
        
        try:
            orchestrator = create_test_orchestrator(max_workers=1)
            test_suite = create_ml_pipeline_integration_test_suite()
            orchestrator.register_test_suite(test_suite)
            
            results = await orchestrator.execute_all_tests()
            
            reporter = create_test_reporter()
            summary = orchestrator.get_execution_summary()
            
            report_files = reporter.generate_all_reports(
                test_results=results,
                execution_summary=summary,
                report_name="ml_pipeline_integration_report"
            )
            
            logger.info(f"ML管道集成测试完成，报告已生成: {report_files}")
            
        finally:
            await teardown_mock_environment()
    
    asyncio.run(run_ml_pipeline_tests())