package com.crypto.trading.common.model.signal;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * 交易信号模型，包含交易决策信息
 * 用于在策略模块和交易执行模块之间传递交易信号
 */
public class TradeSignal implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    private String signalId;          // 信号唯一ID
    private String symbol;            // 交易对，如BTCUSDT
    private String strategy;          // 策略名称
    private String signalType;        // 信号类型（BUY, SELL, CLOSE_LONG, CLOSE_SHORT, HOLD）
    private String side;              // 交易方向（BUY, SELL）
    private String positionSide;      // 仓位方向（LONG, SHORT, BOTH）
    private double confidence;        // 信号置信度，0.0-1.0
    private BigDecimal price;         // 目标价格
    private BigDecimal quantity;      // 交易数量
    private BigDecimal stopPrice;     // 止损价格
    private BigDecimal takeProfit;    // 止盈价格
    private String orderType;         // 订单类型（MARKET, LIMIT, STOP, STOP_MARKET, TAKE_PROFIT, TAKE_PROFIT_MARKET）
    private String timeInForce;       // 订单有效期（GTC, IOC, FOK, GTX）
    private Integer leverageLevel;    // 杠杆倍数
    private long generatedTime;       // 信号生成时间（毫秒时间戳）
    private Map<String, String> additionalParams; // 额外参数，键值对形式
    
    /**
     * 默认构造函数
     */
    public TradeSignal() {
        this.additionalParams = new HashMap<>();
        this.generatedTime = System.currentTimeMillis();
    }
    
    /**
     * 获取信号ID
     * 
     * @return 信号ID
     */
    public String getSignalId() {
        return signalId;
    }
    
    /**
     * 设置信号ID
     * 
     * @param signalId 信号ID
     */
    public void setSignalId(String signalId) {
        this.signalId = signalId;
    }
    
    /**
     * 获取交易对符号
     * 
     * @return 交易对符号
     */
    public String getSymbol() {
        return symbol;
    }
    
    /**
     * 设置交易对符号
     * 
     * @param symbol 交易对符号
     */
    public void setSymbol(String symbol) {
        this.symbol = symbol;
    }
    
    /**
     * 获取策略名称
     * 
     * @return 策略名称
     */
    public String getStrategy() {
        return strategy;
    }
    
    /**
     * 设置策略名称
     * 
     * @param strategy 策略名称
     */
    public void setStrategy(String strategy) {
        this.strategy = strategy;
    }
    
    /**
     * 获取信号类型
     * 
     * @return 信号类型
     */
    public String getSignalType() {
        return signalType;
    }
    
    /**
     * 设置信号类型
     * 
     * @param signalType 信号类型
     */
    public void setSignalType(String signalType) {
        this.signalType = signalType;
    }
    
    /**
     * 获取交易方向
     * 
     * @return 交易方向
     */
    public String getSide() {
        return side;
    }
    
    /**
     * 设置交易方向
     * 
     * @param side 交易方向
     */
    public void setSide(String side) {
        this.side = side;
    }
    
    /**
     * 获取仓位方向
     * 
     * @return 仓位方向
     */
    public String getPositionSide() {
        return positionSide;
    }
    
    /**
     * 设置仓位方向
     * 
     * @param positionSide 仓位方向
     */
    public void setPositionSide(String positionSide) {
        this.positionSide = positionSide;
    }
    
    /**
     * 获取信号置信度
     * 
     * @return 信号置信度
     */
    public double getConfidence() {
        return confidence;
    }
    
    /**
     * 设置信号置信度
     * 
     * @param confidence 信号置信度
     */
    public void setConfidence(double confidence) {
        this.confidence = confidence;
    }
    
    /**
     * 获取价格
     * 
     * @return 价格
     */
    public BigDecimal getPrice() {
        return price;
    }
    
    /**
     * 设置价格
     * 
     * @param price 价格
     */
    public void setPrice(BigDecimal price) {
        this.price = price;
    }
    
    /**
     * 获取交易数量
     * 
     * @return 交易数量
     */
    public BigDecimal getQuantity() {
        return quantity;
    }
    
    /**
     * 设置交易数量
     * 
     * @param quantity 交易数量
     */
    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }
    
    /**
     * 获取止损价格
     * 
     * @return 止损价格
     */
    public BigDecimal getStopPrice() {
        return stopPrice;
    }
    
    /**
     * 设置止损价格
     * 
     * @param stopPrice 止损价格
     */
    public void setStopPrice(BigDecimal stopPrice) {
        this.stopPrice = stopPrice;
    }
    
    /**
     * 获取止盈价格
     * 
     * @return 止盈价格
     */
    public BigDecimal getTakeProfit() {
        return takeProfit;
    }
    
    /**
     * 设置止盈价格
     * 
     * @param takeProfit 止盈价格
     */
    public void setTakeProfit(BigDecimal takeProfit) {
        this.takeProfit = takeProfit;
    }
    
    /**
     * 获取订单类型
     * 
     * @return 订单类型
     */
    public String getOrderType() {
        return orderType;
    }
    
    /**
     * 设置订单类型
     * 
     * @param orderType 订单类型
     */
    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }
    
    /**
     * 获取订单有效期
     * 
     * @return 订单有效期
     */
    public String getTimeInForce() {
        return timeInForce;
    }
    
    /**
     * 设置订单有效期
     * 
     * @param timeInForce 订单有效期
     */
    public void setTimeInForce(String timeInForce) {
        this.timeInForce = timeInForce;
    }
    
    /**
     * 获取杠杆倍数
     * 
     * @return 杠杆倍数
     */
    public Integer getLeverageLevel() {
        return leverageLevel;
    }
    
    /**
     * 设置杠杆倍数
     * 
     * @param leverageLevel 杠杆倍数
     */
    public void setLeverageLevel(Integer leverageLevel) {
        this.leverageLevel = leverageLevel;
    }
    
    /**
     * 获取信号生成时间
     * 
     * @return 信号生成时间
     */
    public long getGeneratedTime() {
        return generatedTime;
    }
    
    /**
     * 设置信号生成时间
     * 
     * @param generatedTime 信号生成时间
     */
    public void setGeneratedTime(long generatedTime) {
        this.generatedTime = generatedTime;
    }
    
    /**
     * 获取额外参数
     * 
     * @return 额外参数
     */
    public Map<String, String> getAdditionalParams() {
        return additionalParams;
    }
    
    /**
     * 设置额外参数
     * 
     * @param additionalParams 额外参数
     */
    public void setAdditionalParams(Map<String, String> additionalParams) {
        this.additionalParams = additionalParams;
    }
    
    /**
     * 添加额外参数
     * 
     * @param key 参数键
     * @param value 参数值
     */
    public void addAdditionalParam(String key, String value) {
        if (this.additionalParams == null) {
            this.additionalParams = new HashMap<>();
        }
        this.additionalParams.put(key, value);
    }
    
    @Override
    public String toString() {
        return "TradeSignal{" +
                "signalId='" + signalId + '\'' +
                ", symbol='" + symbol + '\'' +
                ", strategy='" + strategy + '\'' +
                ", signalType='" + signalType + '\'' +
                ", side='" + side + '\'' +
                ", positionSide='" + positionSide + '\'' +
                ", confidence=" + confidence +
                ", price=" + price +
                ", quantity=" + quantity +
                ", stopPrice=" + stopPrice +
                ", takeProfit=" + takeProfit +
                ", orderType='" + orderType + '\'' +
                ", timeInForce='" + timeInForce + '\'' +
                ", leverageLevel=" + leverageLevel +
                ", generatedTime=" + generatedTime +
                '}';
    }
    
    /**
     * TradeSignal构建器
     */
    public static class Builder {
        private final TradeSignal signal;
        
        public Builder() {
            signal = new TradeSignal();
        }
        
        public Builder signalId(String signalId) {
            signal.signalId = signalId;
            return this;
        }
        
        public Builder symbol(String symbol) {
            signal.symbol = symbol;
            return this;
        }
        
        public Builder strategy(String strategy) {
            signal.strategy = strategy;
            return this;
        }
        
        public Builder signalType(String signalType) {
            signal.signalType = signalType;
            return this;
        }
        
        public Builder side(String side) {
            signal.side = side;
            return this;
        }
        
        public Builder positionSide(String positionSide) {
            signal.positionSide = positionSide;
            return this;
        }
        
        public Builder confidence(double confidence) {
            signal.confidence = confidence;
            return this;
        }
        
        public Builder price(BigDecimal price) {
            signal.price = price;
            return this;
        }
        
        public Builder quantity(BigDecimal quantity) {
            signal.quantity = quantity;
            return this;
        }
        
        public Builder stopPrice(BigDecimal stopPrice) {
            signal.stopPrice = stopPrice;
            return this;
        }
        
        public Builder takeProfit(BigDecimal takeProfit) {
            signal.takeProfit = takeProfit;
            return this;
        }
        
        public Builder orderType(String orderType) {
            signal.orderType = orderType;
            return this;
        }
        
        public Builder timeInForce(String timeInForce) {
            signal.timeInForce = timeInForce;
            return this;
        }
        
        public Builder leverageLevel(Integer leverageLevel) {
            signal.leverageLevel = leverageLevel;
            return this;
        }
        
        public Builder generatedTime(long generatedTime) {
            signal.generatedTime = generatedTime;
            return this;
        }
        
        public Builder additionalParam(String key, String value) {
            signal.addAdditionalParam(key, value);
            return this;
        }
        
        public Builder additionalParams(Map<String, String> additionalParams) {
            signal.additionalParams = additionalParams;
            return this;
        }
        
        public TradeSignal build() {
            return signal;
        }
    }
}