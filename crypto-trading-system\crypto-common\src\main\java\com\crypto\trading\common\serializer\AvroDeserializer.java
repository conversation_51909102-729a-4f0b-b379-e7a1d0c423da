package com.crypto.trading.common.serializer;

import io.confluent.kafka.serializers.KafkaAvroDeserializer;
import org.apache.kafka.common.serialization.Deserializer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * Avro格式反序列化器
 * <p>
 * 使用Apache Avro格式反序列化数据，提供高效的二进制反序列化和模式演化支持。
 * 底层使用Confluent的KafkaAvroDeserializer实现，支持Schema Registry集成。
 * </p>
 *
 * @param <T> 反序列化的目标数据类型
 */
public class AvroDeserializer<T> implements Deserializer<T> {

    private static final Logger log = LoggerFactory.getLogger(AvroDeserializer.class);
    
    private final KafkaAvroDeserializer avroDeserializer;
    private final Class<T> targetType;

    /**
     * 构造函数
     *
     * @param targetType 反序列化的目标数据类型
     */
    public AvroDeserializer(Class<T> targetType) {
        this.avroDeserializer = new KafkaAvroDeserializer();
        this.targetType = targetType;
    }

    /**
     * 配置反序列化器
     *
     * @param configs 配置参数
     * @param isKey   是否为键反序列化器
     */
    @Override
    public void configure(Map<String, ?> configs, boolean isKey) {
        avroDeserializer.configure(configs, isKey);
        log.info("Avro反序列化器已配置: targetType={}, isKey={}", targetType.getName(), isKey);
    }

    /**
     * 反序列化数据
     *
     * @param topic 主题
     * @param data  待反序列化的字节数组
     * @return 反序列化后的对象
     */
    @Override
    @SuppressWarnings("unchecked")
    public T deserialize(String topic, byte[] data) {
        try {
            if (data == null) {
                return null;
            }
            
            Object result = avroDeserializer.deserialize(topic, data);
            
            if (result == null) {
                return null;
            }
            
            if (targetType.isInstance(result)) {
                if (log.isDebugEnabled()) {
                    log.debug("反序列化数据成功: topic={}, targetType={}, size={}bytes", 
                            topic, targetType.getSimpleName(), data.length);
                }
                return (T) result;
            } else {
                log.error("反序列化类型不匹配: expected={}, actual={}", 
                        targetType.getName(), result.getClass().getName());
                return null;
            }
        } catch (Exception e) {
            log.error("反序列化数据失败: topic={}, targetType={}", 
                    topic, targetType.getSimpleName(), e);
            return null;
        }
    }

    /**
     * 关闭反序列化器资源
     */
    @Override
    public void close() {
        avroDeserializer.close();
        log.info("Avro反序列化器已关闭");
    }
}