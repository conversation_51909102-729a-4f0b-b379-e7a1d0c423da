package com.crypto.trading.bootstrap.initializer;

import com.crypto.trading.market.service.MarketDataService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

/**
 * 市场数据模块初始化器
 * 负责初始化市场数据模块，启动WebSocket连接和数据处理器
 */
@Component
public class MarketDataModuleInitializer extends AbstractModuleInitializer {
    
    private static final Logger log = LoggerFactory.getLogger(MarketDataModuleInitializer.class);
    
    /**
     * 模块名称
     */
    private static final String MODULE_NAME = "market-data";
    
    /**
     * 模块优先级
     */
    private static final int PRIORITY = 20;
    
    /**
     * 模块依赖
     */
    private static final String[] DEPENDENCIES = {"common", "sdk"};
    
    @Autowired
    private ApplicationContext applicationContext;
    
    private MarketDataService marketDataService;
    
    @Override
    public String getName() {
        return MODULE_NAME;
    }
    
    @Override
    public int getPriority() {
        return PRIORITY;
    }
    
    @Override
    public String[] getDependencies() {
        return DEPENDENCIES;
    }
    
    @Override
    protected void doInitialize() throws Exception {
        log.info("初始化市场数据模块...");
        
        try {
            // 获取MarketDataService
            marketDataService = applicationContext.getBean(MarketDataService.class);
            
            // 初始化WebSocket连接管理器
            initializeWebSocketManager();
            
            // 初始化数据处理器
            initializeDataProcessors();
            
            // 启动数据监听
            startDataListeners();
            
            log.info("市场数据模块初始化完成");
        } catch (Exception e) {
            log.error("市场数据模块初始化失败: {}", e.getMessage(), e);
            throw e;
        }
    }
    
    /**
     * 初始化WebSocket连接管理器
     */
    private void initializeWebSocketManager() {
        log.info("初始化WebSocket连接管理器...");
        // WebSocket连接管理器会在MarketDataService中自动初始化
        log.info("WebSocket连接管理器初始化完成");
    }
    
    /**
     * 初始化数据处理器
     */
    private void initializeDataProcessors() {
        log.info("初始化数据处理器...");
        // 数据处理器会在MarketDataService中自动初始化
        log.info("数据处理器初始化完成");
    }
    
    /**
     * 启动数据监听
     */
    private void startDataListeners() {
        log.info("启动市场数据监听...");
        // 调用市场数据服务的start方法启动监听器
        if (marketDataService != null) {
            marketDataService.start();
        } else {
            log.error("市场数据服务为null，无法启动市场数据监听");
            throw new RuntimeException("市场数据服务未正确初始化");
        }
        log.info("市场数据监听启动完成");
    }
    
    @Override
    protected void doShutdown() {
        log.info("关闭市场数据模块...");
        
        try {
            // 停止数据监听
            stopDataListeners();
            
            log.info("市场数据模块已关闭");
        } catch (Exception e) {
            log.error("关闭市场数据模块时发生错误: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 停止数据监听
     */
    private void stopDataListeners() {
        log.info("停止市场数据监听...");
        if (marketDataService != null) {
            marketDataService.stop();
        }
        log.info("市场数据监听已停止");
    }
}