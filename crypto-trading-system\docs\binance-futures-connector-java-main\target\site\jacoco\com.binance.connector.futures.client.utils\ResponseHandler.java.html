<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ResponseHandler.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">io.github.binance:binance-futures-connector-java</a> &gt; <a href="index.source.html" class="el_package">com.binance.connector.futures.client.utils</a> &gt; <span class="el_source">ResponseHandler.java</span></div><h1>ResponseHandler.java</h1><pre class="source lang-java linenums">package com.binance.connector.futures.client.utils;

import com.binance.connector.futures.client.exceptions.BinanceClientException;
import com.binance.connector.futures.client.exceptions.BinanceConnectorException;
import com.binance.connector.futures.client.exceptions.BinanceServerException;
import java.io.IOException;
import java.net.ConnectException;
import java.net.UnknownHostException;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.ResponseBody;
import org.json.JSONException;
import org.json.JSONObject;

public final class ResponseHandler {
    private static OkHttpClient client;
    private static final int HTTP_STATUS_CODE_400 = 400;
    private static final int HTTP_STATUS_CODE_499 = 499;
    private static final int HTTP_STATUS_CODE_500 = 500;

    private ResponseHandler() {
    }

    public static String handleResponse(Request request, boolean showLimitUsage, ProxyAuth proxy) {
<span class="fc" id="L26">        client = HttpClientSingleton.getHttpClient(proxy);</span>
<span class="fc" id="L27">        try (Response response = client.newCall(request).execute()) {</span>
<span class="pc bpc" id="L28" title="1 of 2 branches missed.">            if (null == response) {</span>
<span class="nc" id="L29">                throw new BinanceServerException(&quot;[ResponseHandler] No response from server&quot;);</span>
            }

<span class="fc" id="L32">            String responseAsString = getResponseBodyAsString(response.body());</span>

<span class="fc bfc" id="L34" title="All 4 branches covered.">            if (response.code() &gt;= HTTP_STATUS_CODE_400 &amp;&amp; response.code() &lt;= HTTP_STATUS_CODE_499) {</span>
<span class="fc" id="L35">                throw handleErrorResponse(responseAsString, response.code());</span>
<span class="fc bfc" id="L36" title="All 2 branches covered.">            } else if (response.code() &gt;= HTTP_STATUS_CODE_500) {</span>
<span class="fc" id="L37">                throw new BinanceServerException(responseAsString, response.code());</span>
            }

<span class="pc bpc" id="L40" title="1 of 2 branches missed.">            if (showLimitUsage) {</span>
<span class="nc" id="L41">                return getlimitUsage(response, responseAsString);</span>
            } else {
<span class="fc" id="L43">                return responseAsString;</span>
            }
<span class="nc bnc" id="L45" title="All 2 branches missed.">        } catch (IOException | IllegalStateException e) {</span>
<span class="nc" id="L46">            String exceptionMsg = &quot;OKHTTP Error: &quot;;</span>
<span class="nc bnc" id="L47" title="All 2 branches missed.">            if (proxy != null) {</span>
<span class="nc bnc" id="L48" title="All 2 branches missed.">                if ((e.getClass().equals(ConnectException.class))) {</span>
<span class="nc" id="L49">                    exceptionMsg = &quot;Proxy Connection Error: &quot;;</span>
<span class="nc bnc" id="L50" title="All 2 branches missed.">                } else if ((e.getClass().equals(UnknownHostException.class))) {</span>
<span class="nc" id="L51">                    exceptionMsg = &quot;Proxy Unknown Host Error: &quot;;</span>
                }
            }
<span class="nc" id="L54">            throw new BinanceConnectorException(&quot;[ResponseHandler] &quot; + exceptionMsg + e.getMessage());</span>
        }
    }

    private static String getlimitUsage(Response response, String resposeBodyAsString) {
<span class="nc" id="L59">        JSONObject json = new JSONObject();</span>
<span class="nc" id="L60">        json.put(&quot;x-mbx-used-weight&quot;, response.header(&quot;x-mbx-used-weight&quot;));</span>
<span class="nc" id="L61">        json.put(&quot;x-mbx-used-weight-1m&quot;, response.header(&quot;x-mbx-used-weight-1m&quot;));</span>
<span class="nc" id="L62">        json.put(&quot;data&quot;, resposeBodyAsString);</span>

<span class="nc" id="L64">        return json.toString();</span>
    }

    private static BinanceClientException handleErrorResponse(String responseBody, int responseCode) {
        try {
<span class="fc" id="L69">            String errorMsg = JSONParser.getJSONStringValue(responseBody, &quot;msg&quot;);</span>
<span class="fc" id="L70">            int errorCode = JSONParser.getJSONIntValue(responseBody, &quot;code&quot;);</span>
<span class="fc" id="L71">            return new BinanceClientException(responseBody, errorMsg, responseCode, errorCode);</span>
<span class="fc" id="L72">        } catch (JSONException e) {</span>
<span class="fc" id="L73">            throw new BinanceClientException(responseBody, responseCode);</span>
        }
    }

    private static String getResponseBodyAsString(ResponseBody body) throws IOException {
<span class="pc bpc" id="L78" title="1 of 2 branches missed.">        if (null != body) {</span>
<span class="fc" id="L79">            return body.string();</span>
        } else {
<span class="nc" id="L81">            return &quot;&quot;;</span>
        }
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.7.202105040129</span></div></body></html>