package com.crypto.trading.sdk.websocket;

import com.binance.connector.futures.client.impl.UMFuturesClientImpl;
import com.binance.connector.futures.client.impl.UMWebsocketClientImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;

/**
 * @name: BinanceWebSocketClientImpl
 * @author: zeek
 * @description: BinanceWebSocketClient的实现类。
 *               使用官方SDK的UMWebsocketClientImpl来处理WebSocket连接。
 * @create: 2024-07-16 04:30
 */
@Component
public class BinanceWebSocketClientImpl implements BinanceWebSocketClient {

    private static final Logger log = LoggerFactory.getLogger(BinanceWebSocketClientImpl.class);

    private final UMWebsocketClientImpl websocketClient;
    private final Map<Integer, String> activeConnections = new ConcurrentHashMap<>();
    private final AtomicInteger connectionCounter = new AtomicInteger(0);

    @Autowired
    public BinanceWebSocketClientImpl(
            @org.springframework.beans.factory.annotation.Value("${binance.api.use-testnet:false}") boolean useTestnet) {
        
        // 使用BinanceEnvironment枚举获取环境相关URL
        com.crypto.trading.common.enums.BinanceEnvironment environment = 
            com.crypto.trading.common.enums.BinanceEnvironment.fromUseTestnet(useTestnet);
        
        // 获取USDT合约WebSocket URL
        String wsUrl = environment.getUsdtFuturesWsBaseUrl();
        
        log.info("初始化WebSocket客户端: useTestnet={}", useTestnet);
        log.info("使用环境枚举选择的WebSocket URL: {}", wsUrl);
        
        // 使用选择的WebSocket URL初始化WebSocket客户端
        this.websocketClient = new UMWebsocketClientImpl(wsUrl);
        log.info("WebSocket客户端初始化完成，使用URL: {}", wsUrl);
    }

    /**
     * 连接到用户数据流。
     *
     * @param listenKey 币安API返回的 listenKey
     * @param handler   用于处理接收到的消息的回调处理器
     * @return 返回一个WebSocket连接的唯一ID，可用于后续关闭连接等操作。
     */
    @Override
    public int connectUserStream(String listenKey, WebSocketMessageHandler handler) {
        log.info("正在连接到用户数据流，listenKey: {}", listenKey);
        try {
            int connectionId = websocketClient.listenUserStream(listenKey, ((event) -> {
                try {
                    handler.onMessage(event);
                } catch (Exception e) {
                    log.error("处理WebSocket消息时发生异常: {}", event, e);
                }
            }));
            log.info("成功连接到用户数据流，connectionId: {}", connectionId);
            activeConnections.put(connectionId, "userStream_" + listenKey);
            return connectionId;
        } catch (Exception e) {
            log.error("连接到用户数据流失败, listenKey: {}", listenKey, e);
            // 在实际应用中，这里应该抛出一个自定义的SDK异常
            throw new RuntimeException("无法连接到用户数据流", e);
        }
    }
    
    /**
     * 连接到用户数据流（Consumer<String>版本）。
     * 此方法是为了允许直接传递Java 8的Consumer<String>接口作为回调。
     *
     * @param listenKey 币安API返回的 listenKey
     * @param messageConsumer 用于处理接收到的消息的消费者函数
     * @return 返回一个WebSocket连接的唯一ID，可用于后续关闭连接等操作。
     */
    public int connectUserStream(String listenKey, Consumer<String> messageConsumer) {
        // 将Consumer<String>适配为WebSocketMessageHandler
        WebSocketMessageHandler handler = new WebSocketMessageHandler() {
            @Override
            public void onMessage(String message) {
                messageConsumer.accept(message);
            }
        };
        
        // 调用原有方法
        return connectUserStream(listenKey, handler);
    }

    /**
     * 关闭指定的WebSocket连接。
     *
     * @param connectionId 要关闭的连接的ID
     */
    @Override
    public void closeConnection(int connectionId) {
        log.info("正在关闭WebSocket连接, connectionId: {}", connectionId);
        try {
            websocketClient.closeConnection(connectionId);
            activeConnections.remove(connectionId);
            log.info("成功关闭WebSocket连接, connectionId: {}", connectionId);
        } catch (Exception e) {
            log.error("关闭WebSocket连接失败, connectionId: {}", connectionId, e);
        }
    }
    
    /**
     * 将Consumer<String>转换为WebSocketCallback接口
     * 
     * @param consumer 消费者函数
     * @return WebSocketCallback 回调接口
     */
    private com.binance.connector.futures.client.utils.WebSocketCallback adaptCallback(Consumer<String> consumer) {
        return new com.binance.connector.futures.client.utils.WebSocketCallback() {
            @Override
            public void onReceive(String data) {
                consumer.accept(data);
            }
        };
    }

    /**
     * 订阅深度数据（使用币安官方partialDepthStream方法）。
     *
     * @param symbol 交易对，例如 "btcusdt"
     * @param levels 深度级别，例如 5, 10, 20
     * @param speed 更新速度（毫秒），例如 100, 250, 500
     * @param messageHandler 消息处理器
     * @return 返回一个WebSocket连接的唯一ID
     */
    @Override
    public int subscribeDepth(String symbol, int levels, int speed, Consumer<String> messageHandler) {
        log.info("正在订阅深度数据: symbol={}, levels={}, speed={}", symbol, levels, speed);
        try {
            // 使用币安官方的partialDepthStream方法
            int connectionId = websocketClient.partialDepthStream(
                symbol.toLowerCase(), 
                levels, 
                speed, 
                adaptCallback(messageHandler)
            );
            activeConnections.put(connectionId, "depth_" + symbol);
            log.info("成功订阅深度数据: symbol={}, levels={}, speed={}, connectionId={}", 
                    symbol, levels, speed, connectionId);
            return connectionId;
        } catch (Exception e) {
            log.error("订阅深度数据失败: symbol={}, levels={}, speed={}", symbol, levels, speed, e);
            throw new RuntimeException("无法订阅深度数据", e);
        }
    }
    
    /**
     * 订阅部分深度数据（币安官方方法）。
     *
     * @param symbol 交易对，例如 "btcusdt"
     * @param levels 深度级别，例如 5, 10, 20
     * @param speed 更新速度（毫秒），例如 100, 250, 500
     * @param messageHandler 消息处理器
     * @return 返回一个WebSocket连接的唯一ID
     */
    public int subscribePartialDepth(String symbol, int levels, int speed, Consumer<String> messageHandler) {
        log.info("正在订阅部分深度数据: symbol={}, levels={}, speed={}", symbol, levels, speed);
        try {
            int connectionId = websocketClient.partialDepthStream(
                symbol.toLowerCase(), 
                levels, 
                speed, 
                adaptCallback(messageHandler)
            );
            activeConnections.put(connectionId, "partialDepth_" + symbol);
            log.info("成功订阅部分深度数据: symbol={}, levels={}, speed={}, connectionId={}", 
                    symbol, levels, speed, connectionId);
            return connectionId;
        } catch (Exception e) {
            log.error("订阅部分深度数据失败: symbol={}, levels={}, speed={}", symbol, levels, speed, e);
            throw new RuntimeException("无法订阅部分深度数据", e);
        }
    }
    
    /**
     * 订阅差分深度数据（币安官方方法）。
     *
     * @param symbol 交易对，例如 "btcusdt"
     * @param speed 更新速度（毫秒），例如 100, 250, 500
     * @param messageHandler 消息处理器
     * @return 返回一个WebSocket连接的唯一ID
     */
    public int subscribeDiffDepth(String symbol, int speed, Consumer<String> messageHandler) {
        log.info("正在订阅差分深度数据: symbol={}, speed={}", symbol, speed);
        try {
            int connectionId = websocketClient.diffDepthStream(
                symbol.toLowerCase(), 
                speed, 
                adaptCallback(messageHandler)
            );
            activeConnections.put(connectionId, "diffDepth_" + symbol);
            log.info("成功订阅差分深度数据: symbol={}, speed={}, connectionId={}", 
                    symbol, speed, connectionId);
            return connectionId;
        } catch (Exception e) {
            log.error("订阅差分深度数据失败: symbol={}, speed={}", symbol, speed, e);
            throw new RuntimeException("无法订阅差分深度数据", e);
        }
    }
    
    /**
     * 订阅K线数据。
     *
     * @param symbol 交易对，例如 "btcusdt"
     * @param interval K线间隔，例如 "1m", "5m", "1h", "1d"
     * @param messageHandler 消息处理器
     * @return 返回一个WebSocket连接的唯一ID
     */
    @Override
    public int subscribeKline(String symbol, String interval, Consumer<String> messageHandler) {
        log.info("正在订阅K线数据: symbol={}, interval={}", symbol, interval);
        try {
            String stream = String.format("%s@kline_%s", symbol.toLowerCase(), interval);
            java.util.ArrayList<String> streams = new java.util.ArrayList<>();
            streams.add(stream);
            int connectionId = websocketClient.combineStreams(streams, adaptCallback(messageHandler));
            activeConnections.put(connectionId, "kline_" + symbol + "_" + interval);
            log.info("成功订阅K线数据: symbol={}, interval={}, connectionId={}", 
                    symbol, interval, connectionId);
            return connectionId;
        } catch (Exception e) {
            log.error("订阅K线数据失败: symbol={}, interval={}", symbol, interval, e);
            throw new RuntimeException("无法订阅K线数据", e);
        }
    }
    
    /**
     * 订阅聚合交易数据。
     *
     * @param symbol 交易对，例如 "btcusdt"
     * @param messageHandler 消息处理器
     * @return 返回一个WebSocket连接的唯一ID
     */
    @Override
    public int subscribeAggTrade(String symbol, Consumer<String> messageHandler) {
        log.info("正在订阅聚合交易数据: symbol={}", symbol);
        try {
            String stream = String.format("%s@aggTrade", symbol.toLowerCase());
            java.util.ArrayList<String> streams = new java.util.ArrayList<>();
            streams.add(stream);
            int connectionId = websocketClient.combineStreams(streams, adaptCallback(messageHandler));
            activeConnections.put(connectionId, "aggTrade_" + symbol);
            log.info("成功订阅聚合交易数据: symbol={}, connectionId={}", symbol, connectionId);
            return connectionId;
        } catch (Exception e) {
            log.error("订阅聚合交易数据失败: symbol={}", symbol, e);
            throw new RuntimeException("无法订阅聚合交易数据", e);
        }
    }
    
    /**
     * 获取当前活跃的WebSocket连接数。
     *
     * @return 活跃连接数
     */
    @Override
    public int getActiveConnectionCount() {
        return activeConnections.size();
    }
}
