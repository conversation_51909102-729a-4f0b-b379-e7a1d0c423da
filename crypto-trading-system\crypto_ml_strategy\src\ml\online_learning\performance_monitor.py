#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
性能监控器

实时监控在线学习模型的性能指标，包括准确率、损失函数、预测延迟、业务指标等。
提供性能趋势分析、异常检测、告警机制和可视化支持。
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any, Union, Callable
from datetime import datetime, timedelta
import threading
from collections import defaultdict, deque
from dataclasses import dataclass, field
import time
from enum import Enum
import json
import psutil
import gc


class MetricType(Enum):
    """指标类型枚举"""
    ACCURACY = "accuracy"           # 准确性指标
    LOSS = "loss"                   # 损失指标
    LATENCY = "latency"             # 延迟指标
    THROUGHPUT = "throughput"       # 吞吐量指标
    RESOURCE = "resource"           # 资源使用指标
    BUSINESS = "business"           # 业务指标


class AlertLevel(Enum):
    """告警级别枚举"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


@dataclass
class PerformanceMetric:
    """性能指标"""
    name: str
    value: float
    metric_type: MetricType
    timestamp: datetime
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class Alert:
    """告警信息"""
    alert_id: str
    level: AlertLevel
    message: str
    metric_name: str
    current_value: float
    threshold: float
    timestamp: datetime
    resolved: bool = False


@dataclass
class PerformanceReport:
    """性能报告"""
    report_id: str
    start_time: datetime
    end_time: datetime
    metrics_summary: Dict[str, Dict[str, float]]
    alerts: List[Alert]
    recommendations: List[str]
    overall_score: float


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, config: Dict = None):
        """
        初始化性能监控器
        
        Args:
            config: 配置参数字典
        """
        self.logger = logging.getLogger('cryptoMlStrategy.online_learning.performance_monitor')
        
        # 默认配置
        self.config = {
            'monitoring': {
                'enable_real_time_monitoring': True,
                'monitoring_interval': 30,      # 监控间隔（秒）
                'metrics_retention_hours': 168, # 7天
                'alert_check_interval': 60      # 告警检查间隔（秒）
            },
            'thresholds': {
                'accuracy_min': 0.7,           # 最低准确率
                'loss_max': 1.0,               # 最大损失
                'latency_max': 1000,           # 最大延迟（毫秒）
                'memory_usage_max': 0.8,       # 最大内存使用率
                'cpu_usage_max': 0.8,          # 最大CPU使用率
                'throughput_min': 10           # 最小吞吐量（样本/秒）
            },
            'business_metrics': {
                'enable_business_monitoring': True,
                'signal_accuracy_min': 0.6,    # 信号准确率
                'profit_factor_min': 1.2,      # 盈利因子
                'max_drawdown_max': 0.1,       # 最大回撤
                'sharpe_ratio_min': 1.0        # 夏普比率
            },
            'alerts': {
                'enable_alerts': True,
                'alert_cooldown': 300,          # 告警冷却时间（秒）
                'max_alerts_per_hour': 10,
                'email_notifications': False,
                'webhook_url': None
            },
            'reporting': {
                'enable_auto_reporting': True,
                'report_frequency': 3600,       # 报告频率（秒）
                'report_retention_days': 30
            }
        }
        
        # 更新用户配置
        if config:
            self._deep_update(self.config, config)
        
        # 指标存储
        self.metrics_history = defaultdict(lambda: deque())
        self.current_metrics = {}
        
        # 告警管理
        self.active_alerts = {}
        self.alert_history = deque()
        self.alert_counters = defaultdict(int)
        
        # 性能报告
        self.reports = deque()
        
        # 监控状态
        self.monitoring_active = False
        self.last_monitoring_time = None
        self.last_alert_check_time = None
        
        # 统计信息
        self.stats = {
            'total_metrics_collected': 0,
            'alerts_generated': 0,
            'reports_generated': 0,
            'monitoring_uptime': 0,
            'last_performance_score': 0.0
        }
        
        # 线程锁
        self.monitor_lock = threading.RLock()
        
        # 启动监控
        self._start_monitoring()
        
        self.logger.info("性能监控器初始化完成")
    
    def _deep_update(self, base_dict: Dict, update_dict: Dict):
        """深度更新字典"""
        for key, value in update_dict.items():
            if key in base_dict and isinstance(base_dict[key], dict) and isinstance(value, dict):
                self._deep_update(base_dict[key], value)
            else:
                base_dict[key] = value
    
    def _start_monitoring(self):
        """启动监控线程"""
        try:
            if self.config['monitoring']['enable_real_time_monitoring']:
                self.monitoring_active = True
                
                # 启动监控线程
                monitoring_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
                monitoring_thread.start()
                
                # 启动告警检查线程
                alert_thread = threading.Thread(target=self._alert_check_loop, daemon=True)
                alert_thread.start()
                
                self.logger.info("性能监控线程已启动")
            
        except Exception as e:
            self.logger.error(f"启动监控失败: {e}")
    
    def _monitoring_loop(self):
        """监控循环"""
        while self.monitoring_active:
            try:
                # 收集系统指标
                self._collect_system_metrics()
                
                # 更新监控时间
                self.last_monitoring_time = datetime.now()
                
                # 等待下次监控
                time.sleep(self.config['monitoring']['monitoring_interval'])
                
            except Exception as e:
                self.logger.error(f"监控循环错误: {e}")
                time.sleep(5)  # 错误时短暂等待
    
    def _alert_check_loop(self):
        """告警检查循环"""
        while self.monitoring_active:
            try:
                # 检查告警条件
                self._check_alert_conditions()
                
                # 更新告警检查时间
                self.last_alert_check_time = datetime.now()
                
                # 等待下次检查
                time.sleep(self.config['monitoring']['alert_check_interval'])
                
            except Exception as e:
                self.logger.error(f"告警检查循环错误: {e}")
                time.sleep(10)  # 错误时等待更长时间
    
    def record_metric(self, name: str, value: float, metric_type: MetricType,
                     metadata: Dict[str, Any] = None) -> bool:
        """
        记录性能指标
        
        Args:
            name: 指标名称
            value: 指标值
            metric_type: 指标类型
            metadata: 元数据
            
        Returns:
            是否记录成功
        """
        try:
            with self.monitor_lock:
                metric = PerformanceMetric(
                    name=name,
                    value=value,
                    metric_type=metric_type,
                    timestamp=datetime.now(),
                    metadata=metadata or {}
                )
                
                # 存储指标
                self.metrics_history[name].append(metric)
                self.current_metrics[name] = metric
                
                # 限制历史长度
                max_history = self._calculate_max_history()
                if len(self.metrics_history[name]) > max_history:
                    self.metrics_history[name].popleft()
                
                self.stats['total_metrics_collected'] += 1
                
                self.logger.debug(f"记录指标: {name} = {value}")
                return True
                
        except Exception as e:
            self.logger.error(f"记录指标失败: {e}")
            return False
    
    def _calculate_max_history(self) -> int:
        """计算最大历史记录数"""
        retention_hours = self.config['monitoring']['metrics_retention_hours']
        monitoring_interval = self.config['monitoring']['monitoring_interval']
        return int((retention_hours * 3600) / monitoring_interval)
    
    def _collect_system_metrics(self):
        """收集系统指标"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            self.record_metric('cpu_usage', cpu_percent / 100.0, MetricType.RESOURCE)
            
            # 内存使用率
            memory = psutil.virtual_memory()
            self.record_metric('memory_usage', memory.percent / 100.0, MetricType.RESOURCE)
            
            # 磁盘使用率
            disk = psutil.disk_usage('/')
            self.record_metric('disk_usage', disk.percent / 100.0, MetricType.RESOURCE)
            
            # Python进程内存
            process = psutil.Process()
            process_memory = process.memory_info().rss / (1024 * 1024 * 1024)  # GB
            self.record_metric('process_memory_gb', process_memory, MetricType.RESOURCE)
            
        except Exception as e:
            self.logger.error(f"收集系统指标失败: {e}")
    
    def record_model_performance(self, accuracy: float, loss: float, 
                                prediction_time: float, sample_count: int) -> bool:
        """
        记录模型性能指标
        
        Args:
            accuracy: 准确率
            loss: 损失值
            prediction_time: 预测时间（毫秒）
            sample_count: 样本数量
            
        Returns:
            是否记录成功
        """
        try:
            # 记录各项指标
            self.record_metric('model_accuracy', accuracy, MetricType.ACCURACY)
            self.record_metric('model_loss', loss, MetricType.LOSS)
            self.record_metric('prediction_latency', prediction_time, MetricType.LATENCY)
            
            # 计算吞吐量
            if prediction_time > 0:
                throughput = (sample_count * 1000) / prediction_time  # 样本/秒
                self.record_metric('prediction_throughput', throughput, MetricType.THROUGHPUT)
            
            return True
            
        except Exception as e:
            self.logger.error(f"记录模型性能失败: {e}")
            return False
    
    def record_business_metrics(self, signal_accuracy: float, profit_factor: float,
                              max_drawdown: float, sharpe_ratio: float) -> bool:
        """
        记录业务指标
        
        Args:
            signal_accuracy: 信号准确率
            profit_factor: 盈利因子
            max_drawdown: 最大回撤
            sharpe_ratio: 夏普比率
            
        Returns:
            是否记录成功
        """
        try:
            if not self.config['business_metrics']['enable_business_monitoring']:
                return True
            
            self.record_metric('signal_accuracy', signal_accuracy, MetricType.BUSINESS)
            self.record_metric('profit_factor', profit_factor, MetricType.BUSINESS)
            self.record_metric('max_drawdown', max_drawdown, MetricType.BUSINESS)
            self.record_metric('sharpe_ratio', sharpe_ratio, MetricType.BUSINESS)
            
            return True
            
        except Exception as e:
            self.logger.error(f"记录业务指标失败: {e}")
            return False
    
    def _check_alert_conditions(self):
        """检查告警条件"""
        try:
            if not self.config['alerts']['enable_alerts']:
                return
            
            thresholds = self.config['thresholds']
            business_thresholds = self.config['business_metrics']
            
            # 检查各项阈值
            self._check_threshold('model_accuracy', thresholds['accuracy_min'], 'min')
            self._check_threshold('model_loss', thresholds['loss_max'], 'max')
            self._check_threshold('prediction_latency', thresholds['latency_max'], 'max')
            self._check_threshold('memory_usage', thresholds['memory_usage_max'], 'max')
            self._check_threshold('cpu_usage', thresholds['cpu_usage_max'], 'max')
            self._check_threshold('prediction_throughput', thresholds['throughput_min'], 'min')
            
            # 检查业务指标
            if self.config['business_metrics']['enable_business_monitoring']:
                self._check_threshold('signal_accuracy', business_thresholds['signal_accuracy_min'], 'min')
                self._check_threshold('profit_factor', business_thresholds['profit_factor_min'], 'min')
                self._check_threshold('max_drawdown', business_thresholds['max_drawdown_max'], 'max')
                self._check_threshold('sharpe_ratio', business_thresholds['sharpe_ratio_min'], 'min')
            
        except Exception as e:
            self.logger.error(f"检查告警条件失败: {e}")
    
    def _check_threshold(self, metric_name: str, threshold: float, threshold_type: str):
        """检查单个指标阈值"""
        try:
            if metric_name not in self.current_metrics:
                return
            
            current_metric = self.current_metrics[metric_name]
            current_value = current_metric.value
            
            # 判断是否触发告警
            alert_triggered = False
            if threshold_type == 'min' and current_value < threshold:
                alert_triggered = True
            elif threshold_type == 'max' and current_value > threshold:
                alert_triggered = True
            
            if alert_triggered:
                self._generate_alert(metric_name, current_value, threshold, threshold_type)
            else:
                # 检查是否需要解除告警
                self._resolve_alert(metric_name)
                
        except Exception as e:
            self.logger.error(f"检查阈值失败: {e}")
    
    def _generate_alert(self, metric_name: str, current_value: float, 
                       threshold: float, threshold_type: str):
        """生成告警"""
        try:
            # 检查告警冷却时间
            alert_key = f"{metric_name}_{threshold_type}"
            now = datetime.now()
            
            if alert_key in self.active_alerts:
                last_alert_time = self.active_alerts[alert_key].timestamp
                cooldown = self.config['alerts']['alert_cooldown']
                if (now - last_alert_time).total_seconds() < cooldown:
                    return
            
            # 检查每小时告警限制
            hour_key = now.strftime('%Y%m%d%H')
            if self.alert_counters[hour_key] >= self.config['alerts']['max_alerts_per_hour']:
                return
            
            # 确定告警级别
            if metric_name in ['model_accuracy', 'signal_accuracy']:
                level = AlertLevel.ERROR if current_value < threshold * 0.8 else AlertLevel.WARNING
            elif metric_name in ['model_loss', 'max_drawdown']:
                level = AlertLevel.ERROR if current_value > threshold * 1.5 else AlertLevel.WARNING
            else:
                level = AlertLevel.WARNING
            
            # 生成告警消息
            if threshold_type == 'min':
                message = f"{metric_name} 低于阈值: {current_value:.3f} < {threshold:.3f}"
            else:
                message = f"{metric_name} 超过阈值: {current_value:.3f} > {threshold:.3f}"
            
            # 创建告警
            alert = Alert(
                alert_id=f"{alert_key}_{int(now.timestamp())}",
                level=level,
                message=message,
                metric_name=metric_name,
                current_value=current_value,
                threshold=threshold,
                timestamp=now
            )
            
            # 存储告警
            self.active_alerts[alert_key] = alert
            self.alert_history.append(alert)
            self.alert_counters[hour_key] += 1
            self.stats['alerts_generated'] += 1
            
            # 限制告警历史长度
            if len(self.alert_history) > 1000:
                self.alert_history.popleft()
            
            self.logger.warning(f"生成告警: {message}")
            
            # 发送通知（如果配置了）
            self._send_alert_notification(alert)
            
        except Exception as e:
            self.logger.error(f"生成告警失败: {e}")
    
    def _resolve_alert(self, metric_name: str):
        """解除告警"""
        try:
            alert_keys_to_remove = []
            for alert_key, alert in self.active_alerts.items():
                if alert.metric_name == metric_name and not alert.resolved:
                    alert.resolved = True
                    alert_keys_to_remove.append(alert_key)
                    self.logger.info(f"告警已解除: {alert.message}")
            
            # 移除已解除的告警
            for key in alert_keys_to_remove:
                del self.active_alerts[key]
                
        except Exception as e:
            self.logger.error(f"解除告警失败: {e}")
    
    def _send_alert_notification(self, alert: Alert):
        """发送告警通知"""
        try:
            # 这里可以实现邮件、webhook等通知方式
            if self.config['alerts']['webhook_url']:
                # 实现webhook通知
                pass
            
            if self.config['alerts']['email_notifications']:
                # 实现邮件通知
                pass
                
        except Exception as e:
            self.logger.error(f"发送告警通知失败: {e}")
    
    def get_current_performance_score(self) -> float:
        """
        计算当前综合性能分数
        
        Returns:
            性能分数 (0-1)
        """
        try:
            if not self.current_metrics:
                return 0.0
            
            scores = []
            weights = []
            
            # 准确率分数
            if 'model_accuracy' in self.current_metrics:
                accuracy = self.current_metrics['model_accuracy'].value
                scores.append(accuracy)
                weights.append(0.3)
            
            # 损失分数（反向）
            if 'model_loss' in self.current_metrics:
                loss = self.current_metrics['model_loss'].value
                loss_score = max(0, 1 - loss)  # 损失越小分数越高
                scores.append(loss_score)
                weights.append(0.2)
            
            # 延迟分数（反向）
            if 'prediction_latency' in self.current_metrics:
                latency = self.current_metrics['prediction_latency'].value
                max_latency = self.config['thresholds']['latency_max']
                latency_score = max(0, 1 - (latency / max_latency))
                scores.append(latency_score)
                weights.append(0.2)
            
            # 资源使用分数
            resource_scores = []
            if 'cpu_usage' in self.current_metrics:
                cpu_usage = self.current_metrics['cpu_usage'].value
                cpu_score = max(0, 1 - cpu_usage)
                resource_scores.append(cpu_score)
            
            if 'memory_usage' in self.current_metrics:
                memory_usage = self.current_metrics['memory_usage'].value
                memory_score = max(0, 1 - memory_usage)
                resource_scores.append(memory_score)
            
            if resource_scores:
                avg_resource_score = np.mean(resource_scores)
                scores.append(avg_resource_score)
                weights.append(0.15)
            
            # 业务指标分数
            if 'signal_accuracy' in self.current_metrics:
                signal_accuracy = self.current_metrics['signal_accuracy'].value
                scores.append(signal_accuracy)
                weights.append(0.15)
            
            # 计算加权平均分数
            if scores and weights:
                total_weight = sum(weights)
                weighted_score = sum(s * w for s, w in zip(scores, weights)) / total_weight
                self.stats['last_performance_score'] = weighted_score
                return weighted_score
            
            return 0.0
            
        except Exception as e:
            self.logger.error(f"计算性能分数失败: {e}")
            return 0.0
    
    def get_performance_trend(self, metric_name: str, hours: int = 24) -> List[float]:
        """
        获取性能趋势
        
        Args:
            metric_name: 指标名称
            hours: 时间范围（小时）
            
        Returns:
            性能趋势列表
        """
        try:
            if metric_name not in self.metrics_history:
                return []
            
            cutoff_time = datetime.now() - timedelta(hours=hours)
            recent_metrics = [
                m for m in self.metrics_history[metric_name] 
                if m.timestamp >= cutoff_time
            ]
            
            return [m.value for m in recent_metrics]
            
        except Exception as e:
            self.logger.error(f"获取性能趋势失败: {e}")
            return []
    
    def generate_performance_report(self, hours: int = 24) -> Optional[PerformanceReport]:
        """
        生成性能报告
        
        Args:
            hours: 报告时间范围（小时）
            
        Returns:
            性能报告
        """
        try:
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=hours)
            
            # 收集指标摘要
            metrics_summary = {}
            for metric_name, history in self.metrics_history.items():
                recent_metrics = [
                    m for m in history if start_time <= m.timestamp <= end_time
                ]
                
                if recent_metrics:
                    values = [m.value for m in recent_metrics]
                    metrics_summary[metric_name] = {
                        'mean': np.mean(values),
                        'std': np.std(values),
                        'min': np.min(values),
                        'max': np.max(values),
                        'count': len(values)
                    }
            
            # 收集告警
            recent_alerts = [
                alert for alert in self.alert_history 
                if start_time <= alert.timestamp <= end_time
            ]
            
            # 生成建议
            recommendations = self._generate_recommendations(metrics_summary)
            
            # 计算总体分数
            overall_score = self.get_current_performance_score()
            
            # 创建报告
            report = PerformanceReport(
                report_id=f"report_{int(end_time.timestamp())}",
                start_time=start_time,
                end_time=end_time,
                metrics_summary=metrics_summary,
                alerts=recent_alerts,
                recommendations=recommendations,
                overall_score=overall_score
            )
            
            # 存储报告
            self.reports.append(report)
            if len(self.reports) > 100:  # 限制报告数量
                self.reports.popleft()
            
            self.stats['reports_generated'] += 1
            
            self.logger.info(f"性能报告生成完成: {report.report_id}")
            return report
            
        except Exception as e:
            self.logger.error(f"生成性能报告失败: {e}")
            return None
    
    def _generate_recommendations(self, metrics_summary: Dict[str, Dict[str, float]]) -> List[str]:
        """生成性能改进建议"""
        recommendations = []
        
        try:
            # 准确率建议
            if 'model_accuracy' in metrics_summary:
                accuracy_mean = metrics_summary['model_accuracy']['mean']
                if accuracy_mean < 0.8:
                    recommendations.append("模型准确率偏低，建议调整学习率或增加训练数据")
            
            # 损失建议
            if 'model_loss' in metrics_summary:
                loss_std = metrics_summary['model_loss']['std']
                if loss_std > 0.1:
                    recommendations.append("模型损失波动较大，建议使用更稳定的学习策略")
            
            # 延迟建议
            if 'prediction_latency' in metrics_summary:
                latency_mean = metrics_summary['prediction_latency']['mean']
                if latency_mean > 500:
                    recommendations.append("预测延迟较高，建议优化模型结构或使用更快的硬件")
            
            # 资源使用建议
            if 'memory_usage' in metrics_summary:
                memory_mean = metrics_summary['memory_usage']['mean']
                if memory_mean > 0.8:
                    recommendations.append("内存使用率过高，建议优化数据缓存或增加内存")
            
            if 'cpu_usage' in metrics_summary:
                cpu_mean = metrics_summary['cpu_usage']['mean']
                if cpu_mean > 0.8:
                    recommendations.append("CPU使用率过高，建议优化算法或使用更多CPU核心")
            
        except Exception as e:
            self.logger.error(f"生成建议失败: {e}")
        
        return recommendations
    
    def get_monitoring_statistics(self) -> Dict[str, Any]:
        """获取监控统计信息"""
        try:
            with self.monitor_lock:
                # 计算运行时间
                if self.last_monitoring_time:
                    uptime = (datetime.now() - self.last_monitoring_time).total_seconds()
                    self.stats['monitoring_uptime'] = uptime
                
                return {
                    'monitoring_active': self.monitoring_active,
                    'last_monitoring_time': self.last_monitoring_time.isoformat() if self.last_monitoring_time else None,
                    'last_alert_check_time': self.last_alert_check_time.isoformat() if self.last_alert_check_time else None,
                    'current_metrics_count': len(self.current_metrics),
                    'active_alerts_count': len(self.active_alerts),
                    'total_alerts_in_history': len(self.alert_history),
                    'reports_count': len(self.reports),
                    'current_performance_score': self.get_current_performance_score(),
                    'stats': self.stats.copy(),
                    'config': self.config
                }
                
        except Exception as e:
            self.logger.error(f"获取监控统计失败: {e}")
            return {}
    
    def stop_monitoring(self):
        """停止监控"""
        try:
            self.monitoring_active = False
            self.logger.info("性能监控已停止")
            
        except Exception as e:
            self.logger.error(f"停止监控失败: {e}")