/**
 * Autogenerated by Avro
 *
 * DO NOT EDIT DIRECTLY
 */
package com.crypto.trading.common.avro;

import org.apache.avro.generic.GenericArray;
import org.apache.avro.specific.SpecificData;
import org.apache.avro.util.Utf8;
import org.apache.avro.message.BinaryMessageEncoder;
import org.apache.avro.message.BinaryMessageDecoder;
import org.apache.avro.message.SchemaStore;

@org.apache.avro.specific.AvroGenerated
public class TradeDataContent extends org.apache.avro.specific.SpecificRecordBase implements org.apache.avro.specific.SpecificRecord {
  private static final long serialVersionUID = 8981198705916565463L;


  public static final org.apache.avro.Schema SCHEMA$ = new org.apache.avro.Schema.Parser().parse("{\"type\":\"record\",\"name\":\"TradeDataContent\",\"namespace\":\"com.crypto.trading.common.avro\",\"fields\":[{\"name\":\"symbol\",\"type\":{\"type\":\"string\",\"avro.java.string\":\"String\"},\"doc\":\"交易对符号\"},{\"name\":\"tradeId\",\"type\":\"long\",\"doc\":\"交易ID\"},{\"name\":\"price\",\"type\":\"double\",\"doc\":\"成交价格\"},{\"name\":\"quantity\",\"type\":\"double\",\"doc\":\"成交数量\"},{\"name\":\"quoteQuantity\",\"type\":\"double\",\"doc\":\"报价资产成交数量\"},{\"name\":\"time\",\"type\":\"long\",\"doc\":\"成交时间(毫秒)\"},{\"name\":\"isBuyerMaker\",\"type\":\"boolean\",\"doc\":\"是否买方挂单成交\"},{\"name\":\"isBestMatch\",\"type\":\"boolean\",\"doc\":\"是否最优匹配\"}]}");
  public static org.apache.avro.Schema getClassSchema() { return SCHEMA$; }

  private static final SpecificData MODEL$ = new SpecificData();

  private static final BinaryMessageEncoder<TradeDataContent> ENCODER =
      new BinaryMessageEncoder<>(MODEL$, SCHEMA$);

  private static final BinaryMessageDecoder<TradeDataContent> DECODER =
      new BinaryMessageDecoder<>(MODEL$, SCHEMA$);

  /**
   * Return the BinaryMessageEncoder instance used by this class.
   * @return the message encoder used by this class
   */
  public static BinaryMessageEncoder<TradeDataContent> getEncoder() {
    return ENCODER;
  }

  /**
   * Return the BinaryMessageDecoder instance used by this class.
   * @return the message decoder used by this class
   */
  public static BinaryMessageDecoder<TradeDataContent> getDecoder() {
    return DECODER;
  }

  /**
   * Create a new BinaryMessageDecoder instance for this class that uses the specified {@link SchemaStore}.
   * @param resolver a {@link SchemaStore} used to find schemas by fingerprint
   * @return a BinaryMessageDecoder instance for this class backed by the given SchemaStore
   */
  public static BinaryMessageDecoder<TradeDataContent> createDecoder(SchemaStore resolver) {
    return new BinaryMessageDecoder<>(MODEL$, SCHEMA$, resolver);
  }

  /**
   * Serializes this TradeDataContent to a ByteBuffer.
   * @return a buffer holding the serialized data for this instance
   * @throws java.io.IOException if this instance could not be serialized
   */
  public java.nio.ByteBuffer toByteBuffer() throws java.io.IOException {
    return ENCODER.encode(this);
  }

  /**
   * Deserializes a TradeDataContent from a ByteBuffer.
   * @param b a byte buffer holding serialized data for an instance of this class
   * @return a TradeDataContent instance decoded from the given buffer
   * @throws java.io.IOException if the given bytes could not be deserialized into an instance of this class
   */
  public static TradeDataContent fromByteBuffer(
      java.nio.ByteBuffer b) throws java.io.IOException {
    return DECODER.decode(b);
  }

  /** 交易对符号 */
  private java.lang.String symbol;
  /** 交易ID */
  private long tradeId;
  /** 成交价格 */
  private double price;
  /** 成交数量 */
  private double quantity;
  /** 报价资产成交数量 */
  private double quoteQuantity;
  /** 成交时间(毫秒) */
  private long time;
  /** 是否买方挂单成交 */
  private boolean isBuyerMaker;
  /** 是否最优匹配 */
  private boolean isBestMatch;

  /**
   * Default constructor.  Note that this does not initialize fields
   * to their default values from the schema.  If that is desired then
   * one should use <code>newBuilder()</code>.
   */
  public TradeDataContent() {}

  /**
   * All-args constructor.
   * @param symbol 交易对符号
   * @param tradeId 交易ID
   * @param price 成交价格
   * @param quantity 成交数量
   * @param quoteQuantity 报价资产成交数量
   * @param time 成交时间(毫秒)
   * @param isBuyerMaker 是否买方挂单成交
   * @param isBestMatch 是否最优匹配
   */
  public TradeDataContent(java.lang.String symbol, java.lang.Long tradeId, java.lang.Double price, java.lang.Double quantity, java.lang.Double quoteQuantity, java.lang.Long time, java.lang.Boolean isBuyerMaker, java.lang.Boolean isBestMatch) {
    this.symbol = symbol;
    this.tradeId = tradeId;
    this.price = price;
    this.quantity = quantity;
    this.quoteQuantity = quoteQuantity;
    this.time = time;
    this.isBuyerMaker = isBuyerMaker;
    this.isBestMatch = isBestMatch;
  }

  @Override
  public org.apache.avro.specific.SpecificData getSpecificData() { return MODEL$; }

  @Override
  public org.apache.avro.Schema getSchema() { return SCHEMA$; }

  // Used by DatumWriter.  Applications should not call.
  @Override
  public java.lang.Object get(int field$) {
    switch (field$) {
    case 0: return symbol;
    case 1: return tradeId;
    case 2: return price;
    case 3: return quantity;
    case 4: return quoteQuantity;
    case 5: return time;
    case 6: return isBuyerMaker;
    case 7: return isBestMatch;
    default: throw new IndexOutOfBoundsException("Invalid index: " + field$);
    }
  }

  // Used by DatumReader.  Applications should not call.
  @Override
  @SuppressWarnings(value="unchecked")
  public void put(int field$, java.lang.Object value$) {
    switch (field$) {
    case 0: symbol = value$ != null ? value$.toString() : null; break;
    case 1: tradeId = (java.lang.Long)value$; break;
    case 2: price = (java.lang.Double)value$; break;
    case 3: quantity = (java.lang.Double)value$; break;
    case 4: quoteQuantity = (java.lang.Double)value$; break;
    case 5: time = (java.lang.Long)value$; break;
    case 6: isBuyerMaker = (java.lang.Boolean)value$; break;
    case 7: isBestMatch = (java.lang.Boolean)value$; break;
    default: throw new IndexOutOfBoundsException("Invalid index: " + field$);
    }
  }

  /**
   * Gets the value of the 'symbol' field.
   * @return 交易对符号
   */
  public java.lang.String getSymbol() {
    return symbol;
  }


  /**
   * Sets the value of the 'symbol' field.
   * 交易对符号
   * @param value the value to set.
   */
  public void setSymbol(java.lang.String value) {
    this.symbol = value;
  }

  /**
   * Gets the value of the 'tradeId' field.
   * @return 交易ID
   */
  public long getTradeId() {
    return tradeId;
  }


  /**
   * Sets the value of the 'tradeId' field.
   * 交易ID
   * @param value the value to set.
   */
  public void setTradeId(long value) {
    this.tradeId = value;
  }

  /**
   * Gets the value of the 'price' field.
   * @return 成交价格
   */
  public double getPrice() {
    return price;
  }


  /**
   * Sets the value of the 'price' field.
   * 成交价格
   * @param value the value to set.
   */
  public void setPrice(double value) {
    this.price = value;
  }

  /**
   * Gets the value of the 'quantity' field.
   * @return 成交数量
   */
  public double getQuantity() {
    return quantity;
  }


  /**
   * Sets the value of the 'quantity' field.
   * 成交数量
   * @param value the value to set.
   */
  public void setQuantity(double value) {
    this.quantity = value;
  }

  /**
   * Gets the value of the 'quoteQuantity' field.
   * @return 报价资产成交数量
   */
  public double getQuoteQuantity() {
    return quoteQuantity;
  }


  /**
   * Sets the value of the 'quoteQuantity' field.
   * 报价资产成交数量
   * @param value the value to set.
   */
  public void setQuoteQuantity(double value) {
    this.quoteQuantity = value;
  }

  /**
   * Gets the value of the 'time' field.
   * @return 成交时间(毫秒)
   */
  public long getTime() {
    return time;
  }


  /**
   * Sets the value of the 'time' field.
   * 成交时间(毫秒)
   * @param value the value to set.
   */
  public void setTime(long value) {
    this.time = value;
  }

  /**
   * Gets the value of the 'isBuyerMaker' field.
   * @return 是否买方挂单成交
   */
  public boolean getIsBuyerMaker() {
    return isBuyerMaker;
  }


  /**
   * Sets the value of the 'isBuyerMaker' field.
   * 是否买方挂单成交
   * @param value the value to set.
   */
  public void setIsBuyerMaker(boolean value) {
    this.isBuyerMaker = value;
  }

  /**
   * Gets the value of the 'isBestMatch' field.
   * @return 是否最优匹配
   */
  public boolean getIsBestMatch() {
    return isBestMatch;
  }


  /**
   * Sets the value of the 'isBestMatch' field.
   * 是否最优匹配
   * @param value the value to set.
   */
  public void setIsBestMatch(boolean value) {
    this.isBestMatch = value;
  }

  /**
   * Creates a new TradeDataContent RecordBuilder.
   * @return A new TradeDataContent RecordBuilder
   */
  public static com.crypto.trading.common.avro.TradeDataContent.Builder newBuilder() {
    return new com.crypto.trading.common.avro.TradeDataContent.Builder();
  }

  /**
   * Creates a new TradeDataContent RecordBuilder by copying an existing Builder.
   * @param other The existing builder to copy.
   * @return A new TradeDataContent RecordBuilder
   */
  public static com.crypto.trading.common.avro.TradeDataContent.Builder newBuilder(com.crypto.trading.common.avro.TradeDataContent.Builder other) {
    if (other == null) {
      return new com.crypto.trading.common.avro.TradeDataContent.Builder();
    } else {
      return new com.crypto.trading.common.avro.TradeDataContent.Builder(other);
    }
  }

  /**
   * Creates a new TradeDataContent RecordBuilder by copying an existing TradeDataContent instance.
   * @param other The existing instance to copy.
   * @return A new TradeDataContent RecordBuilder
   */
  public static com.crypto.trading.common.avro.TradeDataContent.Builder newBuilder(com.crypto.trading.common.avro.TradeDataContent other) {
    if (other == null) {
      return new com.crypto.trading.common.avro.TradeDataContent.Builder();
    } else {
      return new com.crypto.trading.common.avro.TradeDataContent.Builder(other);
    }
  }

  /**
   * RecordBuilder for TradeDataContent instances.
   */
  @org.apache.avro.specific.AvroGenerated
  public static class Builder extends org.apache.avro.specific.SpecificRecordBuilderBase<TradeDataContent>
    implements org.apache.avro.data.RecordBuilder<TradeDataContent> {

    /** 交易对符号 */
    private java.lang.String symbol;
    /** 交易ID */
    private long tradeId;
    /** 成交价格 */
    private double price;
    /** 成交数量 */
    private double quantity;
    /** 报价资产成交数量 */
    private double quoteQuantity;
    /** 成交时间(毫秒) */
    private long time;
    /** 是否买方挂单成交 */
    private boolean isBuyerMaker;
    /** 是否最优匹配 */
    private boolean isBestMatch;

    /** Creates a new Builder */
    private Builder() {
      super(SCHEMA$, MODEL$);
    }

    /**
     * Creates a Builder by copying an existing Builder.
     * @param other The existing Builder to copy.
     */
    private Builder(com.crypto.trading.common.avro.TradeDataContent.Builder other) {
      super(other);
      if (isValidValue(fields()[0], other.symbol)) {
        this.symbol = data().deepCopy(fields()[0].schema(), other.symbol);
        fieldSetFlags()[0] = other.fieldSetFlags()[0];
      }
      if (isValidValue(fields()[1], other.tradeId)) {
        this.tradeId = data().deepCopy(fields()[1].schema(), other.tradeId);
        fieldSetFlags()[1] = other.fieldSetFlags()[1];
      }
      if (isValidValue(fields()[2], other.price)) {
        this.price = data().deepCopy(fields()[2].schema(), other.price);
        fieldSetFlags()[2] = other.fieldSetFlags()[2];
      }
      if (isValidValue(fields()[3], other.quantity)) {
        this.quantity = data().deepCopy(fields()[3].schema(), other.quantity);
        fieldSetFlags()[3] = other.fieldSetFlags()[3];
      }
      if (isValidValue(fields()[4], other.quoteQuantity)) {
        this.quoteQuantity = data().deepCopy(fields()[4].schema(), other.quoteQuantity);
        fieldSetFlags()[4] = other.fieldSetFlags()[4];
      }
      if (isValidValue(fields()[5], other.time)) {
        this.time = data().deepCopy(fields()[5].schema(), other.time);
        fieldSetFlags()[5] = other.fieldSetFlags()[5];
      }
      if (isValidValue(fields()[6], other.isBuyerMaker)) {
        this.isBuyerMaker = data().deepCopy(fields()[6].schema(), other.isBuyerMaker);
        fieldSetFlags()[6] = other.fieldSetFlags()[6];
      }
      if (isValidValue(fields()[7], other.isBestMatch)) {
        this.isBestMatch = data().deepCopy(fields()[7].schema(), other.isBestMatch);
        fieldSetFlags()[7] = other.fieldSetFlags()[7];
      }
    }

    /**
     * Creates a Builder by copying an existing TradeDataContent instance
     * @param other The existing instance to copy.
     */
    private Builder(com.crypto.trading.common.avro.TradeDataContent other) {
      super(SCHEMA$, MODEL$);
      if (isValidValue(fields()[0], other.symbol)) {
        this.symbol = data().deepCopy(fields()[0].schema(), other.symbol);
        fieldSetFlags()[0] = true;
      }
      if (isValidValue(fields()[1], other.tradeId)) {
        this.tradeId = data().deepCopy(fields()[1].schema(), other.tradeId);
        fieldSetFlags()[1] = true;
      }
      if (isValidValue(fields()[2], other.price)) {
        this.price = data().deepCopy(fields()[2].schema(), other.price);
        fieldSetFlags()[2] = true;
      }
      if (isValidValue(fields()[3], other.quantity)) {
        this.quantity = data().deepCopy(fields()[3].schema(), other.quantity);
        fieldSetFlags()[3] = true;
      }
      if (isValidValue(fields()[4], other.quoteQuantity)) {
        this.quoteQuantity = data().deepCopy(fields()[4].schema(), other.quoteQuantity);
        fieldSetFlags()[4] = true;
      }
      if (isValidValue(fields()[5], other.time)) {
        this.time = data().deepCopy(fields()[5].schema(), other.time);
        fieldSetFlags()[5] = true;
      }
      if (isValidValue(fields()[6], other.isBuyerMaker)) {
        this.isBuyerMaker = data().deepCopy(fields()[6].schema(), other.isBuyerMaker);
        fieldSetFlags()[6] = true;
      }
      if (isValidValue(fields()[7], other.isBestMatch)) {
        this.isBestMatch = data().deepCopy(fields()[7].schema(), other.isBestMatch);
        fieldSetFlags()[7] = true;
      }
    }

    /**
      * Gets the value of the 'symbol' field.
      * 交易对符号
      * @return The value.
      */
    public java.lang.String getSymbol() {
      return symbol;
    }


    /**
      * Sets the value of the 'symbol' field.
      * 交易对符号
      * @param value The value of 'symbol'.
      * @return This builder.
      */
    public com.crypto.trading.common.avro.TradeDataContent.Builder setSymbol(java.lang.String value) {
      validate(fields()[0], value);
      this.symbol = value;
      fieldSetFlags()[0] = true;
      return this;
    }

    /**
      * Checks whether the 'symbol' field has been set.
      * 交易对符号
      * @return True if the 'symbol' field has been set, false otherwise.
      */
    public boolean hasSymbol() {
      return fieldSetFlags()[0];
    }


    /**
      * Clears the value of the 'symbol' field.
      * 交易对符号
      * @return This builder.
      */
    public com.crypto.trading.common.avro.TradeDataContent.Builder clearSymbol() {
      symbol = null;
      fieldSetFlags()[0] = false;
      return this;
    }

    /**
      * Gets the value of the 'tradeId' field.
      * 交易ID
      * @return The value.
      */
    public long getTradeId() {
      return tradeId;
    }


    /**
      * Sets the value of the 'tradeId' field.
      * 交易ID
      * @param value The value of 'tradeId'.
      * @return This builder.
      */
    public com.crypto.trading.common.avro.TradeDataContent.Builder setTradeId(long value) {
      validate(fields()[1], value);
      this.tradeId = value;
      fieldSetFlags()[1] = true;
      return this;
    }

    /**
      * Checks whether the 'tradeId' field has been set.
      * 交易ID
      * @return True if the 'tradeId' field has been set, false otherwise.
      */
    public boolean hasTradeId() {
      return fieldSetFlags()[1];
    }


    /**
      * Clears the value of the 'tradeId' field.
      * 交易ID
      * @return This builder.
      */
    public com.crypto.trading.common.avro.TradeDataContent.Builder clearTradeId() {
      fieldSetFlags()[1] = false;
      return this;
    }

    /**
      * Gets the value of the 'price' field.
      * 成交价格
      * @return The value.
      */
    public double getPrice() {
      return price;
    }


    /**
      * Sets the value of the 'price' field.
      * 成交价格
      * @param value The value of 'price'.
      * @return This builder.
      */
    public com.crypto.trading.common.avro.TradeDataContent.Builder setPrice(double value) {
      validate(fields()[2], value);
      this.price = value;
      fieldSetFlags()[2] = true;
      return this;
    }

    /**
      * Checks whether the 'price' field has been set.
      * 成交价格
      * @return True if the 'price' field has been set, false otherwise.
      */
    public boolean hasPrice() {
      return fieldSetFlags()[2];
    }


    /**
      * Clears the value of the 'price' field.
      * 成交价格
      * @return This builder.
      */
    public com.crypto.trading.common.avro.TradeDataContent.Builder clearPrice() {
      fieldSetFlags()[2] = false;
      return this;
    }

    /**
      * Gets the value of the 'quantity' field.
      * 成交数量
      * @return The value.
      */
    public double getQuantity() {
      return quantity;
    }


    /**
      * Sets the value of the 'quantity' field.
      * 成交数量
      * @param value The value of 'quantity'.
      * @return This builder.
      */
    public com.crypto.trading.common.avro.TradeDataContent.Builder setQuantity(double value) {
      validate(fields()[3], value);
      this.quantity = value;
      fieldSetFlags()[3] = true;
      return this;
    }

    /**
      * Checks whether the 'quantity' field has been set.
      * 成交数量
      * @return True if the 'quantity' field has been set, false otherwise.
      */
    public boolean hasQuantity() {
      return fieldSetFlags()[3];
    }


    /**
      * Clears the value of the 'quantity' field.
      * 成交数量
      * @return This builder.
      */
    public com.crypto.trading.common.avro.TradeDataContent.Builder clearQuantity() {
      fieldSetFlags()[3] = false;
      return this;
    }

    /**
      * Gets the value of the 'quoteQuantity' field.
      * 报价资产成交数量
      * @return The value.
      */
    public double getQuoteQuantity() {
      return quoteQuantity;
    }


    /**
      * Sets the value of the 'quoteQuantity' field.
      * 报价资产成交数量
      * @param value The value of 'quoteQuantity'.
      * @return This builder.
      */
    public com.crypto.trading.common.avro.TradeDataContent.Builder setQuoteQuantity(double value) {
      validate(fields()[4], value);
      this.quoteQuantity = value;
      fieldSetFlags()[4] = true;
      return this;
    }

    /**
      * Checks whether the 'quoteQuantity' field has been set.
      * 报价资产成交数量
      * @return True if the 'quoteQuantity' field has been set, false otherwise.
      */
    public boolean hasQuoteQuantity() {
      return fieldSetFlags()[4];
    }


    /**
      * Clears the value of the 'quoteQuantity' field.
      * 报价资产成交数量
      * @return This builder.
      */
    public com.crypto.trading.common.avro.TradeDataContent.Builder clearQuoteQuantity() {
      fieldSetFlags()[4] = false;
      return this;
    }

    /**
      * Gets the value of the 'time' field.
      * 成交时间(毫秒)
      * @return The value.
      */
    public long getTime() {
      return time;
    }


    /**
      * Sets the value of the 'time' field.
      * 成交时间(毫秒)
      * @param value The value of 'time'.
      * @return This builder.
      */
    public com.crypto.trading.common.avro.TradeDataContent.Builder setTime(long value) {
      validate(fields()[5], value);
      this.time = value;
      fieldSetFlags()[5] = true;
      return this;
    }

    /**
      * Checks whether the 'time' field has been set.
      * 成交时间(毫秒)
      * @return True if the 'time' field has been set, false otherwise.
      */
    public boolean hasTime() {
      return fieldSetFlags()[5];
    }


    /**
      * Clears the value of the 'time' field.
      * 成交时间(毫秒)
      * @return This builder.
      */
    public com.crypto.trading.common.avro.TradeDataContent.Builder clearTime() {
      fieldSetFlags()[5] = false;
      return this;
    }

    /**
      * Gets the value of the 'isBuyerMaker' field.
      * 是否买方挂单成交
      * @return The value.
      */
    public boolean getIsBuyerMaker() {
      return isBuyerMaker;
    }


    /**
      * Sets the value of the 'isBuyerMaker' field.
      * 是否买方挂单成交
      * @param value The value of 'isBuyerMaker'.
      * @return This builder.
      */
    public com.crypto.trading.common.avro.TradeDataContent.Builder setIsBuyerMaker(boolean value) {
      validate(fields()[6], value);
      this.isBuyerMaker = value;
      fieldSetFlags()[6] = true;
      return this;
    }

    /**
      * Checks whether the 'isBuyerMaker' field has been set.
      * 是否买方挂单成交
      * @return True if the 'isBuyerMaker' field has been set, false otherwise.
      */
    public boolean hasIsBuyerMaker() {
      return fieldSetFlags()[6];
    }


    /**
      * Clears the value of the 'isBuyerMaker' field.
      * 是否买方挂单成交
      * @return This builder.
      */
    public com.crypto.trading.common.avro.TradeDataContent.Builder clearIsBuyerMaker() {
      fieldSetFlags()[6] = false;
      return this;
    }

    /**
      * Gets the value of the 'isBestMatch' field.
      * 是否最优匹配
      * @return The value.
      */
    public boolean getIsBestMatch() {
      return isBestMatch;
    }


    /**
      * Sets the value of the 'isBestMatch' field.
      * 是否最优匹配
      * @param value The value of 'isBestMatch'.
      * @return This builder.
      */
    public com.crypto.trading.common.avro.TradeDataContent.Builder setIsBestMatch(boolean value) {
      validate(fields()[7], value);
      this.isBestMatch = value;
      fieldSetFlags()[7] = true;
      return this;
    }

    /**
      * Checks whether the 'isBestMatch' field has been set.
      * 是否最优匹配
      * @return True if the 'isBestMatch' field has been set, false otherwise.
      */
    public boolean hasIsBestMatch() {
      return fieldSetFlags()[7];
    }


    /**
      * Clears the value of the 'isBestMatch' field.
      * 是否最优匹配
      * @return This builder.
      */
    public com.crypto.trading.common.avro.TradeDataContent.Builder clearIsBestMatch() {
      fieldSetFlags()[7] = false;
      return this;
    }

    @Override
    @SuppressWarnings("unchecked")
    public TradeDataContent build() {
      try {
        TradeDataContent record = new TradeDataContent();
        record.symbol = fieldSetFlags()[0] ? this.symbol : (java.lang.String) defaultValue(fields()[0]);
        record.tradeId = fieldSetFlags()[1] ? this.tradeId : (java.lang.Long) defaultValue(fields()[1]);
        record.price = fieldSetFlags()[2] ? this.price : (java.lang.Double) defaultValue(fields()[2]);
        record.quantity = fieldSetFlags()[3] ? this.quantity : (java.lang.Double) defaultValue(fields()[3]);
        record.quoteQuantity = fieldSetFlags()[4] ? this.quoteQuantity : (java.lang.Double) defaultValue(fields()[4]);
        record.time = fieldSetFlags()[5] ? this.time : (java.lang.Long) defaultValue(fields()[5]);
        record.isBuyerMaker = fieldSetFlags()[6] ? this.isBuyerMaker : (java.lang.Boolean) defaultValue(fields()[6]);
        record.isBestMatch = fieldSetFlags()[7] ? this.isBestMatch : (java.lang.Boolean) defaultValue(fields()[7]);
        return record;
      } catch (org.apache.avro.AvroMissingFieldException e) {
        throw e;
      } catch (java.lang.Exception e) {
        throw new org.apache.avro.AvroRuntimeException(e);
      }
    }
  }

  @SuppressWarnings("unchecked")
  private static final org.apache.avro.io.DatumWriter<TradeDataContent>
    WRITER$ = (org.apache.avro.io.DatumWriter<TradeDataContent>)MODEL$.createDatumWriter(SCHEMA$);

  @Override public void writeExternal(java.io.ObjectOutput out)
    throws java.io.IOException {
    WRITER$.write(this, SpecificData.getEncoder(out));
  }

  @SuppressWarnings("unchecked")
  private static final org.apache.avro.io.DatumReader<TradeDataContent>
    READER$ = (org.apache.avro.io.DatumReader<TradeDataContent>)MODEL$.createDatumReader(SCHEMA$);

  @Override public void readExternal(java.io.ObjectInput in)
    throws java.io.IOException {
    READER$.read(this, SpecificData.getDecoder(in));
  }

  @Override protected boolean hasCustomCoders() { return true; }

  @Override public void customEncode(org.apache.avro.io.Encoder out)
    throws java.io.IOException
  {
    out.writeString(this.symbol);

    out.writeLong(this.tradeId);

    out.writeDouble(this.price);

    out.writeDouble(this.quantity);

    out.writeDouble(this.quoteQuantity);

    out.writeLong(this.time);

    out.writeBoolean(this.isBuyerMaker);

    out.writeBoolean(this.isBestMatch);

  }

  @Override public void customDecode(org.apache.avro.io.ResolvingDecoder in)
    throws java.io.IOException
  {
    org.apache.avro.Schema.Field[] fieldOrder = in.readFieldOrderIfDiff();
    if (fieldOrder == null) {
      this.symbol = in.readString();

      this.tradeId = in.readLong();

      this.price = in.readDouble();

      this.quantity = in.readDouble();

      this.quoteQuantity = in.readDouble();

      this.time = in.readLong();

      this.isBuyerMaker = in.readBoolean();

      this.isBestMatch = in.readBoolean();

    } else {
      for (int i = 0; i < 8; i++) {
        switch (fieldOrder[i].pos()) {
        case 0:
          this.symbol = in.readString();
          break;

        case 1:
          this.tradeId = in.readLong();
          break;

        case 2:
          this.price = in.readDouble();
          break;

        case 3:
          this.quantity = in.readDouble();
          break;

        case 4:
          this.quoteQuantity = in.readDouble();
          break;

        case 5:
          this.time = in.readLong();
          break;

        case 6:
          this.isBuyerMaker = in.readBoolean();
          break;

        case 7:
          this.isBestMatch = in.readBoolean();
          break;

        default:
          throw new java.io.IOException("Corrupt ResolvingDecoder.");
        }
      }
    }
  }
}










