#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
在线学习集成测试

测试在线学习引擎的增量学习、模型版本管理、性能监控和学习策略功能。
"""

import asyncio
import sys
import time
import numpy as np
import pandas as pd
from pathlib import Path
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from loguru import logger

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from tests.integration.utils.ml_pipeline_test_helpers import (
    MLPerformanceMonitor,
    ModelValidator,
    PredictionAccuracyValidator
)


class MockOnlineLearningEngine:
    """Mock在线学习引擎"""
    
    def __init__(self, learning_rate: float = 0.01, adaptation_threshold: float = 0.1):
        """
        初始化Mock在线学习引擎
        
        Args:
            learning_rate: 学习率
            adaptation_threshold: 适应阈值
        """
        self.learning_rate = learning_rate
        self.adaptation_threshold = adaptation_threshold
        self.model_versions = []
        self.performance_history = []
        self.current_model_id = None
        self.learning_statistics = {
            "total_updates": 0,
            "successful_adaptations": 0,
            "failed_adaptations": 0,
            "performance_improvements": 0
        }
        
        logger.info(f"Mock在线学习引擎已初始化 - 学习率: {learning_rate}")
    
    def initialize_model(self, initial_data: np.ndarray, initial_labels: np.ndarray) -> str:
        """
        初始化模型
        
        Args:
            initial_data: 初始训练数据
            initial_labels: 初始标签
        
        Returns:
            str: 模型ID
        """
        model_id = f"model_v{len(self.model_versions) + 1}_{int(time.time())}"
        
        # 模拟初始训练
        from sklearn.ensemble import RandomForestClassifier
        model = RandomForestClassifier(n_estimators=20, random_state=42)
        model.fit(initial_data, initial_labels)
        
        # 计算初始性能
        initial_accuracy = model.score(initial_data, initial_labels)
        
        model_version = {
            "model_id": model_id,
            "version": 1,
            "created_at": datetime.now(),
            "model": model,
            "accuracy": initial_accuracy,
            "training_samples": len(initial_data),
            "is_active": True
        }
        
        self.model_versions.append(model_version)
        self.current_model_id = model_id
        
        logger.info(f"模型已初始化: {model_id}, 初始准确率: {initial_accuracy:.3f}")
        return model_id
    
    def incremental_update(self, new_data: np.ndarray, new_labels: np.ndarray) -> Dict[str, Any]:
        """
        增量更新模型
        
        Args:
            new_data: 新数据
            new_labels: 新标签
        
        Returns:
            Dict[str, Any]: 更新结果
        """
        if not self.model_versions:
            raise RuntimeError("模型未初始化")
        
        current_model = self._get_current_model()
        
        # 评估当前模型在新数据上的性能
        current_predictions = current_model["model"].predict(new_data)
        current_accuracy = np.mean(current_predictions == new_labels)
        
        # 决定是否需要更新
        performance_drop = current_model["accuracy"] - current_accuracy
        needs_update = performance_drop > self.adaptation_threshold
        
        update_result = {
            "update_triggered": needs_update,
            "performance_drop": performance_drop,
            "current_accuracy": current_accuracy,
            "previous_accuracy": current_model["accuracy"],
            "new_samples": len(new_data)
        }
        
        if needs_update:
            # 执行增量学习
            new_model_id = self._perform_incremental_learning(
                current_model, new_data, new_labels
            )
            update_result["new_model_id"] = new_model_id
            update_result["update_successful"] = True
            self.learning_statistics["successful_adaptations"] += 1
        else:
            update_result["update_successful"] = False
            self.learning_statistics["failed_adaptations"] += 1
        
        self.learning_statistics["total_updates"] += 1
        
        # 记录性能历史
        self.performance_history.append({
            "timestamp": datetime.now(),
            "accuracy": current_accuracy,
            "model_id": self.current_model_id,
            "update_triggered": needs_update
        })
        
        return update_result
    
    def _perform_incremental_learning(self, current_model: Dict[str, Any], 
                                    new_data: np.ndarray, new_labels: np.ndarray) -> str:
        """执行增量学习"""
        # 创建新模型版本
        new_model_id = f"model_v{len(self.model_versions) + 1}_{int(time.time())}"
        
        # 模拟增量学习（重新训练）
        from sklearn.ensemble import RandomForestClassifier
        new_model = RandomForestClassifier(n_estimators=25, random_state=42)
        
        # 合并旧数据和新数据（简化实现）
        # 在实际实现中，这里会使用更复杂的增量学习算法
        new_model.fit(new_data, new_labels)
        
        # 评估新模型
        new_accuracy = new_model.score(new_data, new_labels)
        
        # 创建新模型版本
        new_version = {
            "model_id": new_model_id,
            "version": len(self.model_versions) + 1,
            "created_at": datetime.now(),
            "model": new_model,
            "accuracy": new_accuracy,
            "training_samples": len(new_data),
            "is_active": True,
            "parent_model_id": current_model["model_id"]
        }
        
        # 停用旧模型
        current_model["is_active"] = False
        
        # 添加新模型
        self.model_versions.append(new_version)
        self.current_model_id = new_model_id
        
        # 检查性能改进
        if new_accuracy > current_model["accuracy"]:
            self.learning_statistics["performance_improvements"] += 1
        
        logger.info(f"增量学习完成: {new_model_id}, 新准确率: {new_accuracy:.3f}")
        return new_model_id
    
    def _get_current_model(self) -> Dict[str, Any]:
        """获取当前活跃模型"""
        for model in self.model_versions:
            if model["is_active"]:
                return model
        raise RuntimeError("没有活跃模型")
    
    def predict(self, data: np.ndarray) -> np.ndarray:
        """使用当前模型进行预测"""
        current_model = self._get_current_model()
        return current_model["model"].predict(data)
    
    def get_learning_statistics(self) -> Dict[str, Any]:
        """获取学习统计信息"""
        return {
            **self.learning_statistics,
            "model_versions_count": len(self.model_versions),
            "current_model_id": self.current_model_id,
            "adaptation_rate": (self.learning_statistics["successful_adaptations"] / 
                              max(1, self.learning_statistics["total_updates"])),
            "improvement_rate": (self.learning_statistics["performance_improvements"] / 
                               max(1, self.learning_statistics["successful_adaptations"]))
        }
    
    def get_performance_history(self) -> List[Dict[str, Any]]:
        """获取性能历史"""
        return self.performance_history.copy()


async def test_incremental_learning_mechanism() -> Dict[str, Any]:
    """测试增量学习机制"""
    logger.info("执行增量学习机制测试...")
    
    start_time = time.time()
    monitor = MLPerformanceMonitor()
    
    try:
        # 创建在线学习引擎
        online_engine = MockOnlineLearningEngine(
            learning_rate=0.02,
            adaptation_threshold=0.05
        )
        
        # 生成初始训练数据
        n_initial = 500
        n_features = 20
        
        initial_data = np.random.randn(n_initial, n_features)
        initial_labels = np.random.choice([0, 1], size=n_initial, p=[0.4, 0.6])
        
        monitor.start_operation("model_initialization")
        
        # 初始化模型
        initial_model_id = online_engine.initialize_model(initial_data, initial_labels)
        
        initialization_time = monitor.end_operation("model_initialization")
        
        # 模拟数据流和增量学习
        update_results = []
        n_updates = 5
        
        for i in range(n_updates):
            # 生成新数据批次
            batch_size = 100
            new_data = np.random.randn(batch_size, n_features)
            new_labels = np.random.choice([0, 1], size=batch_size, p=[0.45, 0.55])
            
            monitor.start_operation(f"incremental_update_{i}")
            
            # 执行增量更新
            update_result = online_engine.incremental_update(new_data, new_labels)
            
            update_time = monitor.end_operation(f"incremental_update_{i}")
            
            update_result["update_time"] = update_time
            update_result["batch_index"] = i
            update_results.append(update_result)
            
            logger.info(f"批次 {i}: 更新触发={update_result['update_triggered']}, "
                       f"准确率={update_result['current_accuracy']:.3f}")
        
        # 获取学习统计
        learning_stats = online_engine.get_learning_statistics()
        performance_history = online_engine.get_performance_history()
        
        # 计算总体指标
        total_update_time = sum(r["update_time"] for r in update_results)
        avg_update_time = total_update_time / len(update_results)
        successful_updates = sum(1 for r in update_results if r["update_triggered"])
        
        return {
            "execution_time": time.time() - start_time,
            "initialization_time": initialization_time,
            "total_update_time": total_update_time,
            "avg_update_time": avg_update_time,
            "initial_model_id": initial_model_id,
            "update_batches": n_updates,
            "successful_updates": successful_updates,
            "update_success_rate": successful_updates / n_updates,
            "update_results": update_results,
            "learning_statistics": learning_stats,
            "performance_history_length": len(performance_history),
            "final_accuracy": performance_history[-1]["accuracy"] if performance_history else 0.0,
            "test_type": "incremental_learning_mechanism",
            "performance_check": (
                avg_update_time < 5.0 and
                learning_stats["adaptation_rate"] > 0.0 and
                len(performance_history) > 0
            )
        }
        
    except Exception as e:
        logger.error(f"增量学习机制测试失败: {str(e)}")
        raise


async def test_model_version_management() -> Dict[str, Any]:
    """测试模型版本管理"""
    logger.info("执行模型版本管理测试...")
    
    start_time = time.time()
    monitor = MLPerformanceMonitor()
    
    try:
        # 创建在线学习引擎
        online_engine = MockOnlineLearningEngine(
            learning_rate=0.01,
            adaptation_threshold=0.08
        )
        
        # 初始化模型
        initial_data = np.random.randn(300, 15)
        initial_labels = np.random.choice([0, 1], size=300)
        
        initial_model_id = online_engine.initialize_model(initial_data, initial_labels)
        
        # 强制触发多次模型更新
        version_history = []
        
        for i in range(3):
            # 生成会导致性能下降的数据
            degraded_data = np.random.randn(150, 15) * 2  # 增加噪声
            degraded_labels = np.random.choice([0, 1], size=150, p=[0.3, 0.7])  # 改变分布
            
            monitor.start_operation(f"version_update_{i}")
            
            # 执行更新
            update_result = online_engine.incremental_update(degraded_data, degraded_labels)
            
            update_time = monitor.end_operation(f"version_update_{i}")
            
            if update_result["update_triggered"]:
                version_info = {
                    "version_index": i,
                    "new_model_id": update_result["new_model_id"],
                    "performance_drop": update_result["performance_drop"],
                    "update_time": update_time
                }
                version_history.append(version_info)
        
        # 获取所有模型版本信息
        all_versions = []
        for model_version in online_engine.model_versions:
            version_info = {
                "model_id": model_version["model_id"],
                "version": model_version["version"],
                "created_at": model_version["created_at"].isoformat(),
                "accuracy": model_version["accuracy"],
                "training_samples": model_version["training_samples"],
                "is_active": model_version["is_active"],
                "parent_model_id": model_version.get("parent_model_id")
            }
            all_versions.append(version_info)
        
        # 验证版本管理功能
        active_models = [v for v in all_versions if v["is_active"]]
        inactive_models = [v for v in all_versions if not v["is_active"]]
        
        # 检查版本链
        version_chain_valid = True
        for version in all_versions[1:]:  # 跳过第一个版本
            if not version.get("parent_model_id"):
                version_chain_valid = False
                break
        
        return {
            "execution_time": time.time() - start_time,
            "initial_model_id": initial_model_id,
            "total_versions_created": len(all_versions),
            "active_models_count": len(active_models),
            "inactive_models_count": len(inactive_models),
            "version_updates_triggered": len(version_history),
            "version_history": version_history,
            "all_versions": all_versions,
            "current_model_id": online_engine.current_model_id,
            "version_chain_valid": version_chain_valid,
            "test_type": "model_version_management",
            "performance_check": (
                len(all_versions) > 1 and
                len(active_models) == 1 and
                version_chain_valid
            )
        }
        
    except Exception as e:
        logger.error(f"模型版本管理测试失败: {str(e)}")
        raise


async def test_online_learning_performance_monitoring() -> Dict[str, Any]:
    """测试在线学习性能监控"""
    logger.info("执行在线学习性能监控测试...")
    
    start_time = time.time()
    monitor = MLPerformanceMonitor()
    accuracy_validator = PredictionAccuracyValidator()
    
    try:
        # 创建在线学习引擎
        online_engine = MockOnlineLearningEngine(
            learning_rate=0.015,
            adaptation_threshold=0.06
        )
        
        # 初始化模型
        initial_data = np.random.randn(400, 18)
        initial_labels = np.random.choice([0, 1], size=400)
        
        online_engine.initialize_model(initial_data, initial_labels)
        
        # 模拟连续的数据流和性能监控
        monitoring_results = []
        n_monitoring_cycles = 8
        
        for cycle in range(n_monitoring_cycles):
            # 生成新数据
            new_data = np.random.randn(80, 18)
            new_labels = np.random.choice([0, 1], size=80)
            
            monitor.start_operation(f"monitoring_cycle_{cycle}")
            
            # 预测性能评估
            predictions = online_engine.predict(new_data)
            prediction_accuracy = np.mean(predictions == new_labels)
            
            # 执行增量更新
            update_result = online_engine.incremental_update(new_data, new_labels)
            
            cycle_time = monitor.end_operation(f"monitoring_cycle_{cycle}")
            
            # 验证预测准确性
            validation_result = accuracy_validator.validate_classification_predictions(
                predictions, new_labels
            )
            
            cycle_result = {
                "cycle": cycle,
                "cycle_time": cycle_time,
                "prediction_accuracy": prediction_accuracy,
                "update_triggered": update_result["update_triggered"],
                "performance_drop": update_result["performance_drop"],
                "validation_passed": validation_result.get("validation_passed", False),
                "samples_processed": len(new_data)
            }
            
            monitoring_results.append(cycle_result)
        
        # 获取完整的性能历史
        performance_history = online_engine.get_performance_history()
        learning_stats = online_engine.get_learning_statistics()
        
        # 分析性能趋势
        accuracies = [r["prediction_accuracy"] for r in monitoring_results]
        accuracy_trend = "improving" if accuracies[-1] > accuracies[0] else "declining"
        accuracy_variance = np.var(accuracies)
        
        # 计算监控指标
        total_monitoring_time = sum(r["cycle_time"] for r in monitoring_results)
        avg_cycle_time = total_monitoring_time / len(monitoring_results)
        total_samples_processed = sum(r["samples_processed"] for r in monitoring_results)
        
        return {
            "execution_time": time.time() - start_time,
            "total_monitoring_time": total_monitoring_time,
            "avg_cycle_time": avg_cycle_time,
            "monitoring_cycles": n_monitoring_cycles,
            "total_samples_processed": total_samples_processed,
            "monitoring_results": monitoring_results,
            "performance_history_length": len(performance_history),
            "learning_statistics": learning_stats,
            "accuracy_trend": accuracy_trend,
            "accuracy_variance": float(accuracy_variance),
            "avg_accuracy": float(np.mean(accuracies)),
            "min_accuracy": float(np.min(accuracies)),
            "max_accuracy": float(np.max(accuracies)),
            "test_type": "online_learning_performance_monitoring",
            "performance_check": (
                avg_cycle_time < 3.0 and
                learning_stats["adaptation_rate"] >= 0.0 and
                accuracy_variance < 0.1
            )
        }
        
    except Exception as e:
        logger.error(f"在线学习性能监控测试失败: {str(e)}")
        raise


async def test_adaptive_learning_strategies() -> Dict[str, Any]:
    """测试自适应学习策略"""
    logger.info("执行自适应学习策略测试...")
    
    start_time = time.time()
    monitor = MLPerformanceMonitor()
    
    try:
        # 测试不同的学习策略
        learning_strategies = [
            {"learning_rate": 0.005, "adaptation_threshold": 0.03, "name": "conservative"},
            {"learning_rate": 0.02, "adaptation_threshold": 0.08, "name": "moderate"},
            {"learning_rate": 0.05, "adaptation_threshold": 0.15, "name": "aggressive"}
        ]
        
        strategy_results = []
        
        for strategy in learning_strategies:
            logger.info(f"测试学习策略: {strategy['name']}")
            
            # 创建引擎
            engine = MockOnlineLearningEngine(
                learning_rate=strategy["learning_rate"],
                adaptation_threshold=strategy["adaptation_threshold"]
            )
            
            # 初始化
            initial_data = np.random.randn(300, 16)
            initial_labels = np.random.choice([0, 1], size=300)
            engine.initialize_model(initial_data, initial_labels)
            
            monitor.start_operation(f"strategy_{strategy['name']}")
            
            # 模拟数据流
            strategy_updates = 0
            strategy_accuracy_sum = 0
            n_batches = 6
            
            for batch in range(n_batches):
                # 生成变化的数据分布
                noise_level = 1 + (batch * 0.2)  # 逐渐增加噪声
                batch_data = np.random.randn(100, 16) * noise_level
                batch_labels = np.random.choice([0, 1], size=100)
                
                update_result = engine.incremental_update(batch_data, batch_labels)
                
                if update_result["update_triggered"]:
                    strategy_updates += 1
                
                strategy_accuracy_sum += update_result["current_accuracy"]
            
            strategy_time = monitor.end_operation(f"strategy_{strategy['name']}")
            
            # 获取策略统计
            stats = engine.get_learning_statistics()
            
            result = {
                "strategy_name": strategy["name"],
                "learning_rate": strategy["learning_rate"],
                "adaptation_threshold": strategy["adaptation_threshold"],
                "execution_time": strategy_time,
                "updates_triggered": strategy_updates,
                "avg_accuracy": strategy_accuracy_sum / n_batches,
                "adaptation_rate": stats["adaptation_rate"],
                "improvement_rate": stats["improvement_rate"],
                "total_model_versions": stats["model_versions_count"]
            }
            
            strategy_results.append(result)
        
        # 分析最佳策略
        best_strategy = max(strategy_results, 
                          key=lambda x: x["avg_accuracy"] * x["adaptation_rate"])
        
        # 策略比较分析
        strategy_comparison = {
            "best_strategy": best_strategy["strategy_name"],
            "accuracy_comparison": [(r["strategy_name"], r["avg_accuracy"]) 
                                   for r in strategy_results],
            "adaptation_comparison": [(r["strategy_name"], r["adaptation_rate"]) 
                                     for r in strategy_results],
            "efficiency_comparison": [(r["strategy_name"], r["avg_accuracy"] / r["execution_time"]) 
                                     for r in strategy_results]
        }
        
        return {
            "execution_time": time.time() - start_time,
            "strategies_tested": len(learning_strategies),
            "strategy_results": strategy_results,
            "best_strategy_config": best_strategy,
            "strategy_comparison": strategy_comparison,
            "test_type": "adaptive_learning_strategies",
            "performance_check": (
                best_strategy["avg_accuracy"] > 0.5 and
                best_strategy["adaptation_rate"] > 0.0 and
                len(strategy_results) == len(learning_strategies)
            )
        }
        
    except Exception as e:
        logger.error(f"自适应学习策略测试失败: {str(e)}")
        raise


# 导出测试函数
__all__ = [
    'test_incremental_learning_mechanism',
    'test_model_version_management',
    'test_online_learning_performance_monitoring',
    'test_adaptive_learning_strategies',
    'MockOnlineLearningEngine'
]