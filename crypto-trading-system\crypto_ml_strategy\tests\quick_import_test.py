#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
快速导入测试脚本
"""

import sys
import os

# 设置Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def test_basic_imports():
    """测试基础导入"""
    try:
        from src.config import Config
        print("[OK] Config")
        
        from src.logger import setup_logger
        print("[OK] setup_logger")
        
        from src.kafka_client import KafkaClient
        print("[OK] KafkaClient")
        
        from src.data_processor import DataProcessor
        print("[OK] DataProcessor")
        
        return True
    except Exception as e:
        print(f"[FAIL] Basic imports: {e}")
        return False

def test_data_imports():
    """测试数据模块导入"""
    try:
        from src.data.influxdb_client import InfluxDBClient
        print("[OK] InfluxDBClient")
        
        from src.data.mysql_client import MySQLClient
        print("[OK] MySQLClient")
        
        from src.data.real_data_loader import RealDataLoader
        print("[OK] RealDataLoader")
        
        return True
    except Exception as e:
        print(f"[FAIL] Data imports: {e}")
        return False

def test_model_imports():
    """测试模型模块导入"""
    try:
        from src.model.feature_engineering import FeatureEngineering
        print("[OK] FeatureEngineering")
        
        from src.model.model_trainer import ModelTrainer
        print("[OK] ModelTrainer")
        
        from src.model.prediction import PredictionEngine
        print("[OK] PredictionEngine")
        
        from src.model.online_learner import OnlineLearner
        print("[OK] OnlineLearner")
        
        from src.model.versioning import ModelVersionManager
        print("[OK] ModelVersionManager")
        
        return True
    except Exception as e:
        print(f"[FAIL] Model imports: {e}")
        return False

def test_strategy_imports():
    """测试策略模块导入"""
    try:
        from src.strategy.base import BaseStrategy
        print("[OK] BaseStrategy")
        
        from src.strategy.unified_ml import UnifiedMLStrategy
        print("[OK] UnifiedMLStrategy")
        
        return True
    except Exception as e:
        print(f"[FAIL] Strategy imports: {e}")
        return False

def test_indicators_imports():
    """测试指标模块导入"""
    try:
        from src.indicators.lppl_features import LPPLFeatureExtractor
        print("[OK] LPPLFeatureExtractor")
        
        from src.indicators.hematread_features import HematreadFeatureExtractor
        print("[OK] HematreadFeatureExtractor")
        
        from src.indicators.bull_market_support import BullMarketSupportFeatureExtractor
        print("[OK] BullMarketSupportFeatureExtractor")
        
        from src.indicators.super_trend import SuperTrendCalculator
        print("[OK] SuperTrendCalculator")
        
        return True
    except Exception as e:
        print(f"[FAIL] Indicators imports: {e}")
        return False

def test_main_import():
    """测试主模块导入"""
    try:
        from src.main import TradingStrategyApp
        print("[OK] TradingStrategyApp")
        return True
    except Exception as e:
        print(f"[FAIL] Main import: {e}")
        return False

if __name__ == "__main__":
    print("=== 快速导入测试 ===")
    
    tests = [
        ("基础模块", test_basic_imports),
        ("数据模块", test_data_imports),
        ("模型模块", test_model_imports),
        ("策略模块", test_strategy_imports),
        ("指标模块", test_indicators_imports),
        ("主模块", test_main_import),
    ]
    
    passed = 0
    total = len(tests)
    
    for name, test_func in tests:
        print(f"\n--- {name} ---")
        if test_func():
            passed += 1
            print(f"✓ {name} 通过")
        else:
            print(f"✗ {name} 失败")
    
    print(f"\n=== 结果 ===")
    print(f"通过: {passed}/{total}")
    print(f"成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("✅ 所有导入测试通过！")
    else:
        print("❌ 部分导入测试失败！")