package com.crypto.trading.common.dto.account;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 账户信息传输对象
 * <p>
 * 用于在系统各层之间传递账户信息
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class AccountDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 账户ID
     */
    private String accountId;

    /**
     * 账户类型
     * <p>
     * SPOT: 现货账户
     * MARGIN: 杠杆账户
     * FUTURES: 期货账户
     * </p>
     */
    private String accountType;

    /**
     * 账户状态
     * <p>
     * NORMAL: 正常
     * SUSPENDED: 已暂停
     * LOCKED: 已锁定
     * </p>
     */
    private String status;

    /**
     * 是否可以交易
     */
    private boolean canTrade;

    /**
     * 是否可以提现
     */
    private boolean canWithdraw;

    /**
     * 是否可以充值
     */
    private boolean canDeposit;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 构造函数
     */
    public AccountDTO() {
    }

    /**
     * 构造函数
     *
     * @param accountId   账户ID
     * @param accountType 账户类型
     * @param status      账户状态
     * @param canTrade    是否可以交易
     * @param canWithdraw 是否可以提现
     * @param canDeposit  是否可以充值
     * @param createTime  创建时间
     * @param updateTime  更新时间
     */
    public AccountDTO(String accountId, String accountType, String status, boolean canTrade,
                      boolean canWithdraw, boolean canDeposit, LocalDateTime createTime, LocalDateTime updateTime) {
        this.accountId = accountId;
        this.accountType = accountType;
        this.status = status;
        this.canTrade = canTrade;
        this.canWithdraw = canWithdraw;
        this.canDeposit = canDeposit;
        this.createTime = createTime;
        this.updateTime = updateTime;
    }

    /**
     * 获取账户ID
     *
     * @return 账户ID
     */
    public String getAccountId() {
        return accountId;
    }

    /**
     * 设置账户ID
     *
     * @param accountId 账户ID
     */
    public void setAccountId(String accountId) {
        this.accountId = accountId;
    }

    /**
     * 获取账户类型
     *
     * @return 账户类型
     */
    public String getAccountType() {
        return accountType;
    }

    /**
     * 设置账户类型
     *
     * @param accountType 账户类型
     */
    public void setAccountType(String accountType) {
        this.accountType = accountType;
    }

    /**
     * 获取账户状态
     *
     * @return 账户状态
     */
    public String getStatus() {
        return status;
    }

    /**
     * 设置账户状态
     *
     * @param status 账户状态
     */
    public void setStatus(String status) {
        this.status = status;
    }

    /**
     * 获取是否可以交易
     *
     * @return 是否可以交易
     */
    public boolean isCanTrade() {
        return canTrade;
    }

    /**
     * 设置是否可以交易
     *
     * @param canTrade 是否可以交易
     */
    public void setCanTrade(boolean canTrade) {
        this.canTrade = canTrade;
    }

    /**
     * 获取是否可以提现
     *
     * @return 是否可以提现
     */
    public boolean isCanWithdraw() {
        return canWithdraw;
    }

    /**
     * 设置是否可以提现
     *
     * @param canWithdraw 是否可以提现
     */
    public void setCanWithdraw(boolean canWithdraw) {
        this.canWithdraw = canWithdraw;
    }

    /**
     * 获取是否可以充值
     *
     * @return 是否可以充值
     */
    public boolean isCanDeposit() {
        return canDeposit;
    }

    /**
     * 设置是否可以充值
     *
     * @param canDeposit 是否可以充值
     */
    public void setCanDeposit(boolean canDeposit) {
        this.canDeposit = canDeposit;
    }

    /**
     * 获取创建时间
     *
     * @return 创建时间
     */
    public LocalDateTime getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新时间
     *
     * @return 更新时间
     */
    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "AccountDTO{" +
                "accountId='" + accountId + '\'' +
                ", accountType='" + accountType + '\'' +
                ", status='" + status + '\'' +
                ", canTrade=" + canTrade +
                ", canWithdraw=" + canWithdraw +
                ", canDeposit=" + canDeposit +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                '}';
    }
} 