---
name: "\U0001F41B Bug Report"
about: Submit a bug report to help us improve Qlib
labels: bug

---

## 🐛 Bug Description

<!-- A clear and concise description of what the bug is. -->

## To Reproduce

Steps to reproduce the behavior:

1.
1.
1.


## Expected Behavior

<!-- A clear and concise description of what you expected to happen. -->

## Screenshot

<!-- A screenshot of the error message or anything shouldn't appear-->

## Environment

**Note**: User could run `cd scripts && python collect_info.py all` under project directory to get system information
and paste them here directly.

 - Qlib version:
 - Python version:
 - OS (`Windows`, `Linux`, `MacOS`):
 - Commit number (optional, please provide it if you are using the dev version):

## Additional Notes

<!-- Add any other information about the problem here. -->
