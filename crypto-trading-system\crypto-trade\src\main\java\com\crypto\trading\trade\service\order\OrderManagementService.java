package com.crypto.trading.trade.service.order;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.crypto.trading.common.dto.trade.OrderDTO;
import com.crypto.trading.common.dto.trade.OrderRequestDTO;
import com.crypto.trading.common.dto.trade.OrderResponseDTO;
import com.crypto.trading.trade.model.entity.order.OrderEntity;
import com.crypto.trading.trade.model.entity.order.status.OrderStatusEntity;

import java.util.List;
import java.util.Map;

/**
 * 订单管理服务接口
 * <p>
 * 负责订单的创建、查询、更新等操作
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface OrderManagementService {

    /**
     * 创建订单
     *
     * @param request 订单请求DTO
     * @return 订单DTO
     */
    OrderDTO createOrder(OrderRequestDTO request);

    /**
     * 查找订单
     *
     * @param orderId       订单ID
     * @param clientOrderId 客户端订单ID
     * @return 订单实体
     */
    OrderEntity findOrder(String orderId, String clientOrderId);

    /**
     * 批量查询订单
     *
     * @param symbol 交易对
     * @param status 订单状态
     * @param limit  返回数量限制
     * @return 订单响应DTO列表
     */
    List<OrderResponseDTO> findOrders(String symbol, String status, int limit);

    /**
     * 更新订单状态
     *
     * @param orderId      订单ID
     * @param status       状态
     * @param reason       原因
     * @param responseData 响应数据
     * @return 更新后的订单实体
     */
    OrderEntity updateOrderStatus(String orderId, String status, String reason, String responseData);

    /**
     * 取消订单
     *
     * @param orderId       订单ID
     * @param clientOrderId 客户端订单ID
     * @return 取消后的订单DTO
     */
    OrderDTO cancelOrder(String orderId, String clientOrderId);

    /**
     * 根据策略ID查询订单
     *
     * @param strategyId 策略ID
     * @param limit      返回数量限制
     * @return 订单响应DTO列表
     */
    List<OrderResponseDTO> findOrdersByStrategyId(String strategyId, int limit);

    /**
     * 删除订单
     *
     * @param orderId 订单ID
     * @return 是否删除成功
     */
    boolean deleteOrder(String orderId);

    /**
     * 统计订单数量
     *
     * @param symbol 交易对
     * @param status 订单状态
     * @return 订单数量
     */
    long countOrders(String symbol, String status);
    
    /**
     * 获取订单状态历史
     *
     * @param orderId 订单ID
     * @return 状态历史列表
     */
    List<OrderStatusEntity> getOrderStatusHistory(String orderId);
    
    /**
     * 获取订单统计数据
     *
     * @param startDate 开始日期（格式：yyyyMMdd）
     * @param endDate   结束日期（格式：yyyyMMdd）
     * @param symbol    交易对（可选）
     * @param strategy  策略名称（可选）
     * @return 统计数据
     */
    Map<String, Object> getOrderStatistics(String startDate, String endDate, String symbol, String strategy);
    
    /**
     * 重新计算统计数据
     *
     * @param statsDate 统计日期（格式：yyyyMMdd）
     * @return 是否成功
     */
    boolean recalculateStatistics(String statsDate);
    
    /**
     * 根据ID获取订单
     *
     * @param orderId 订单ID
     * @return 订单实体
     */
    OrderEntity getOrderById(String orderId);
    
    /**
     * 根据客户端订单ID获取订单
     *
     * @param clientOrderId 客户端订单ID
     * @return 订单实体
     */
    OrderEntity getOrderByClientOrderId(String clientOrderId);
    
    /**
     * 根据信号ID查询订单
     *
     * @param signalId 信号ID
     * @return 订单列表
     */
    List<OrderEntity> getOrdersBySignalId(String signalId);
    
    /**
     * 根据交易对和时间范围查询订单
     *
     * @param symbol    交易对
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param page      页码
     * @param size      每页大小
     * @return 分页订单列表
     */
    IPage<OrderEntity> getOrdersBySymbolAndTimeRange(String symbol, long startTime, long endTime, int page, int size);
    
    /**
     * 根据状态分页查询订单
     *
     * @param status 订单状态
     * @param page   页码
     * @param size   每页大小
     * @return 分页订单列表
     */
    IPage<OrderEntity> getOrdersByStatus(String status, int page, int size);
    
    /**
     * 批量更新订单状态
     *
     * @param orderIds     订单ID列表
     * @param newStatus    新状态
     * @param reason       原因
     * @param details      详情
     * @return 更新的订单数量
     */
    int batchUpdateOrderStatus(List<String> orderIds, String newStatus, String reason, String details);
    
    /**
     * 从订单更新统计数据
     *
     * @param order 订单实体
     * @return 是否成功
     */
    boolean updateStatisticsFromOrder(OrderEntity order);
    
    /**
     * 清理过期数据
     *
     * @param retentionDays 保留天数
     * @return 清理的记录数
     */
    int cleanupExpiredData(int retentionDays);
}