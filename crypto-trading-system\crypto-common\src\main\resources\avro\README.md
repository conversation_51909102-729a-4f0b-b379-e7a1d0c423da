# Avro 序列化优化

## 概述

本优化通过引入 Apache Avro 序列化格式替代 JSON，显著提升了 Kafka 消息传输的性能和效率。Avro 是一种紧凑的二进制序列化格式，相比 JSON 具有以下优势：

1. **更小的消息体积**：Avro 二进制格式比 JSON 文本格式更紧凑，通常可减少 30-40% 的数据大小
2. **更快的序列化/反序列化速度**：二进制格式处理速度更快，减少 CPU 使用率
3. **模式演化支持**：Avro 支持向前兼容和向后兼容的模式演化，便于系统升级
4. **类型安全**：Avro 提供严格的类型检查，减少运行时错误

## 模式定义

本项目定义了三种核心数据类型的 Avro 模式：

1. **K线数据**：`kline.avsc`
2. **深度数据**：`depth.avsc`
3. **交易数据**：`trade.avsc`

这些模式文件位于 `crypto-common/src/main/resources/avro/` 目录下。

## 使用方法

### Java 端

1. **生成 Avro 类**：
   ```bash
   mvn clean generate-sources
   ```
   这将根据 `.avsc` 文件生成对应的 Java 类

2. **使用 Avro 序列化**：
   ```java
   // 创建 Avro 对象
   KlineData klineData = new KlineData();
   klineData.setSymbol("BTCUSDT");
   // ... 设置其他字段
   
   // 使用 Avro 序列化发送
   kafkaTemplate.send("kline.data", klineData.getSymbol(), klineData);
   ```

3. **配置**：
   在 `application.yml` 中启用 Avro 序列化：
   ```yaml
   spring:
     kafka:
       avro-serialization-enabled: true
       schema-registry-url: http://localhost:8081
   ```

### Python 端

1. **安装依赖**：
   ```bash
   pip install confluent-kafka[avro] fastavro
   ```

2. **使用 Avro 反序列化**：
   ```python
   from src.data.utils.kafka_utils import AvroKafkaConsumer
   
   consumer = AvroKafkaConsumer(
       bootstrap_servers='localhost:9092',
       group_id='ml-strategy-group',
       topics=['kline.data'],
       schema_registry_url='http://localhost:8081'
   )
   
   message = consumer.consume()
   if message:
       # message 已经被反序列化为 Python 字典
       print(message['value'])
   ```

## 性能优化

除了引入 Avro 序列化外，本次优化还包括：

1. **增加批处理大小**：从 32KB 提升到 64KB
2. **增加缓冲区大小**：从 64MB 提升到 128MB
3. **优化批处理延迟**：从 10ms 提升到 20ms
4. **增加消费者拉取记录数**：从 500 条提升到 1000 条
5. **增加最大拉取字节数**：从 50MB 提升到 100MB

这些优化显著提高了系统的吞吐量和性能。

## 注意事项

1. 首次使用时需要运行 Schema Registry 服务
2. 修改 Avro 模式时需要注意向前兼容性
3. 生产环境中建议使用集群化的 Schema Registry