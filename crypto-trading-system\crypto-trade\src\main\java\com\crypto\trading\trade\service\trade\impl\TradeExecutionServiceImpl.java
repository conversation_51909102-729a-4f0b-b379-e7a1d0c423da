package com.crypto.trading.trade.service.trade.impl;

import com.crypto.trading.common.constant.OrderEnums;
import com.crypto.trading.common.exception.ApiException;
import com.crypto.trading.common.model.signal.TradeSignal;
import com.crypto.trading.common.util.IdGenerator;
import com.crypto.trading.common.util.JsonUtil;
import com.crypto.trading.sdk.client.UMFuturesApiClientImpl;
import com.crypto.trading.trade.model.entity.order.OrderEntity;
import com.crypto.trading.trade.model.entity.order.OrderExecutionLogEntity;
import com.crypto.trading.trade.repository.mapper.order.OrderExecutionLogMapper;
import com.crypto.trading.trade.repository.mapper.order.OrderMapper;
import com.crypto.trading.trade.service.risk.RiskControlService;
import com.crypto.trading.trade.service.trade.OrderConverterService;
import com.crypto.trading.trade.service.trade.TradeExecutionService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 交易执行服务实现类
 */
@Service
public class TradeExecutionServiceImpl implements TradeExecutionService {
    
    private static final Logger log = LoggerFactory.getLogger(TradeExecutionServiceImpl.class);
    
    /**
     * 最大重试次数
     */
    private static final int MAX_RETRY_COUNT = 3;
    
    /**
     * 重试等待时间(毫秒)
     */
    private static final long RETRY_WAIT_MS = 500;
    
    @Autowired
    private UMFuturesApiClientImpl umFuturesClient;
    
    @Autowired
    private OrderMapper orderMapper;
    
    @Autowired
    private OrderExecutionLogMapper orderExecutionLogMapper;
    
    @Autowired
    private OrderConverterService orderConverterService;
    
    @Autowired
    private RiskControlService riskControlService;
    
    @Value("${binance.futures.dry-run:false}")
    private boolean dryRun;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OrderEntity processTradeSignal(TradeSignal signal) throws ApiException {
        log.info("处理交易信号: {}", signal);
        
        // 风控检查
        if (!riskControlService.checkSignal(signal)) {
            log.warn("交易信号未通过风控检查，信号ID: {}", signal.getSignalId());
            throw new ApiException("交易信号未通过风控检查");
        }
        
        // 根据信号类型创建不同类型的订单
        OrderEntity order;
        
        switch (signal.getOrderType()) {
            case "MARKET":
                order = createMarketOrder(
                        signal.getSymbol(),
                        signal.getSide(),
                        signal.getPositionSide(),
                        signal.getQuantity(),
                        generateClientOrderId(signal),
                        signal.getSignalId()
                );
                break;
            case "LIMIT":
                order = createLimitOrder(
                        signal.getSymbol(),
                        signal.getSide(),
                        signal.getPositionSide(),
                        signal.getQuantity(),
                        signal.getPrice(),
                        signal.getTimeInForce(),
                        generateClientOrderId(signal),
                        signal.getSignalId()
                );
                break;
            case "STOP":
            case "STOP_MARKET":
                order = createStopOrder(
                        signal.getSymbol(),
                        signal.getSide(),
                        signal.getPositionSide(),
                        signal.getQuantity(),
                        signal.getStopPrice(),
                        signal.getPrice(),
                        generateClientOrderId(signal),
                        signal.getSignalId()
                );
                break;
            default:
                log.error("不支持的订单类型: {}", signal.getOrderType());
                throw new ApiException("不支持的订单类型: " + signal.getOrderType());
        }
        
        log.info("订单创建成功: {}", order);
        return order;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OrderEntity createMarketOrder(
            String symbol,
            String side,
            String positionSide,
            BigDecimal quantity,
            String clientOrderId,
            String signalId) throws ApiException {
        
        log.info("创建市价单: symbol={}, side={}, positionSide={}, quantity={}, clientOrderId={}",
                symbol, side, positionSide, quantity, clientOrderId);
        
        // 构建订单参数
        Map<String, Object> parameters = new LinkedHashMap<>();
        parameters.put("symbol", symbol);
        parameters.put("side", side);
        parameters.put("positionSide", positionSide);
        parameters.put("type", OrderEnums.OrderType.MARKET.name());
        parameters.put("quantity", quantity.toString());
        parameters.put("newClientOrderId", clientOrderId);
        
        // 创建订单实体并保存，将BigDecimal转换为Double
        Double quantityDouble = quantity != null ? quantity.doubleValue() : null;
        OrderEntity order = new OrderEntity.Builder()
                .clientOrderId(clientOrderId)
                .signalId(signalId)
                .symbol(symbol)
                .side(side)
                .positionSide(positionSide)
                .orderType(OrderEnums.OrderType.MARKET.name())
                .quantity(quantityDouble)
                .status(OrderEnums.OrderStatus.NEW.name())
                .strategy(extractStrategyFromSignalId(signalId))
                .build();
        
        orderMapper.insert(order);
        
        // 执行API调用
        return executeOrderApiCall(order, "POST", "/fapi/v1/order", parameters, "CREATE");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OrderEntity createLimitOrder(
            String symbol,
            String side,
            String positionSide,
            BigDecimal quantity,
            BigDecimal price,
            String timeInForce,
            String clientOrderId,
            String signalId) throws ApiException {
        
        log.info("创建限价单: symbol={}, side={}, positionSide={}, quantity={}, price={}, timeInForce={}, clientOrderId={}",
                symbol, side, positionSide, quantity, price, timeInForce, clientOrderId);
        
        // 构建订单参数
        Map<String, Object> parameters = new LinkedHashMap<>();
        parameters.put("symbol", symbol);
        parameters.put("side", side);
        parameters.put("positionSide", positionSide);
        parameters.put("type", OrderEnums.OrderType.LIMIT.name());
        parameters.put("quantity", quantity.toString());
        parameters.put("price", price.toString());
        parameters.put("timeInForce", timeInForce);
        parameters.put("newClientOrderId", clientOrderId);
        
        // 创建订单实体并保存，将BigDecimal转换为Double
        Double quantityDouble = quantity != null ? quantity.doubleValue() : null;
        Double priceDouble = price != null ? price.doubleValue() : null;
        
        OrderEntity order = new OrderEntity.Builder()
                .clientOrderId(clientOrderId)
                .signalId(signalId)
                .symbol(symbol)
                .side(side)
                .positionSide(positionSide)
                .orderType(OrderEnums.OrderType.LIMIT.name())
                .quantity(quantityDouble)
                .price(priceDouble)
                .timeInForce(timeInForce)
                .status(OrderEnums.OrderStatus.NEW.name())
                .strategy(extractStrategyFromSignalId(signalId))
                .build();
        
        orderMapper.insert(order);
        
        // 执行API调用
        return executeOrderApiCall(order, "POST", "/fapi/v1/order", parameters, "CREATE");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OrderEntity createStopOrder(
            String symbol,
            String side,
            String positionSide,
            BigDecimal quantity,
            BigDecimal stopPrice,
            BigDecimal price,
            String clientOrderId,
            String signalId) throws ApiException {
        
        log.info("创建止损单: symbol={}, side={}, positionSide={}, quantity={}, stopPrice={}, price={}, clientOrderId={}",
                symbol, side, positionSide, quantity, stopPrice, price, clientOrderId);
        
        // 构建订单参数
        Map<String, Object> parameters = new LinkedHashMap<>();
        parameters.put("symbol", symbol);
        parameters.put("side", side);
        parameters.put("positionSide", positionSide);
        parameters.put("type", OrderEnums.OrderType.STOP.name());
        parameters.put("quantity", quantity.toString());
        parameters.put("stopPrice", stopPrice.toString());
        
        // 如果提供了价格，则为限价止损单，否则为市价止损单
        if (price != null) {
            parameters.put("price", price.toString());
            parameters.put("timeInForce", "GTC");
        }
        
        parameters.put("newClientOrderId", clientOrderId);
        
        // 创建订单实体并保存，将BigDecimal转换为Double
        Double quantityDouble = quantity != null ? quantity.doubleValue() : null;
        Double priceDouble = price != null ? price.doubleValue() : null;
        Double stopPriceDouble = stopPrice != null ? stopPrice.doubleValue() : null;
        
        OrderEntity order = new OrderEntity.Builder()
                .clientOrderId(clientOrderId)
                .signalId(signalId)
                .symbol(symbol)
                .side(side)
                .positionSide(positionSide)
                .orderType(OrderEnums.OrderType.STOP.name())
                .quantity(quantityDouble)
                .price(priceDouble)
                .status(OrderEnums.OrderStatus.NEW.name())
                .strategy(extractStrategyFromSignalId(signalId))
                .build();
        
        orderMapper.insert(order);
        
        // 执行API调用
        return executeOrderApiCall(order, "POST", "/fapi/v1/order", parameters, "CREATE");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean cancelOrder(String symbol, String orderId) throws ApiException {
        log.info("取消订单: symbol={}, orderId={}", symbol, orderId);
        
        // 查询订单信息
        OrderEntity order = orderMapper.findByOrderId(orderId);
        if (order == null) {
            log.error("找不到订单: orderId={}", orderId);
            throw new ApiException("找不到订单: " + orderId);
        }
        
        // 构建取消参数
        Map<String, Object> parameters = new LinkedHashMap<>();
        parameters.put("symbol", symbol);
        parameters.put("orderId", orderId);
        
        // 执行API调用
        try {
            executeOrderApiCall(order, "DELETE", "/fapi/v1/order", parameters, "CANCEL");
            return true;
        } catch (ApiException e) {
            log.error("取消订单失败: {}", e.getMessage());
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean cancelAllOrders(String symbol) throws ApiException {
        log.info("取消所有订单: symbol={}", symbol);
        
        // 构建取消参数
        Map<String, Object> parameters = new LinkedHashMap<>();
        parameters.put("symbol", symbol);
        
        // 创建执行日志
        String executionId = IdGenerator.generateUuid();
        OrderExecutionLogEntity executionLog = new OrderExecutionLogEntity.Builder()
                .executionId(executionId)
                .action("CANCEL_ALL")
                .requestData(JsonUtil.toJsonString(parameters))
                .build();
        
        orderExecutionLogMapper.insert(executionLog);
        
        // 执行API调用
        try {
            if (dryRun) {
                log.info("干运行模式，跳过API调用: cancelAllOrders, symbol={}", symbol);
                executionLog.setSuccess(true);
                executionLog.setResponseData("{\"dryrun\":true}");
            } else {
                // 使用正确的API调用方式
                // 确保参数是LinkedHashMap类型
                LinkedHashMap<String, Object> linkedParams = new LinkedHashMap<>(parameters);
                String response = umFuturesClient.cancelAllOpenOrders(linkedParams);
                executionLog.setSuccess(true);
                executionLog.setResponseData(response);
                log.info("取消所有订单成功: {}", response);
            }
            
            orderExecutionLogMapper.updateById(executionLog);
            return true;
        } catch (Exception e) {
            executionLog.setSuccess(false);
            executionLog.setErrorCode("API_ERROR");
            executionLog.setErrorMessage(e.getMessage());
            orderExecutionLogMapper.updateById(executionLog);
            
            log.error("取消所有订单失败: {}", e.getMessage());
            throw new ApiException("取消所有订单失败: " + e.getMessage());
        }
    }

    @Override
    public OrderEntity getOrderStatus(String symbol, String orderId) throws ApiException {
        log.info("查询订单状态: symbol={}, orderId={}", symbol, orderId);
        
        // 查询订单信息
        OrderEntity order = orderMapper.findByOrderId(orderId);
        if (order == null) {
            log.error("找不到订单: orderId={}", orderId);
            throw new ApiException("找不到订单: " + orderId);
        }
        
        // 构建查询参数
        Map<String, Object> parameters = new LinkedHashMap<>();
        parameters.put("symbol", symbol);
        parameters.put("orderId", orderId);
        
        // 执行API调用
        return executeOrderApiCall(order, "GET", "/fapi/v1/order", parameters, "QUERY");
    }

    @Override
    public OrderEntity getOrderStatusByClientOrderId(String symbol, String clientOrderId) throws ApiException {
        log.info("根据客户端订单ID查询订单状态: symbol={}, clientOrderId={}", symbol, clientOrderId);
        
        // 查询订单信息
        OrderEntity order = orderMapper.findByClientOrderId(clientOrderId);
        if (order == null) {
            log.error("找不到订单: clientOrderId={}", clientOrderId);
            throw new ApiException("找不到订单: " + clientOrderId);
        }
        
        // 构建查询参数
        Map<String, Object> parameters = new LinkedHashMap<>();
        parameters.put("symbol", symbol);
        parameters.put("origClientOrderId", clientOrderId);
        
        // 执行API调用
        return executeOrderApiCall(order, "GET", "/fapi/v1/order", parameters, "QUERY");
    }

    /**
     * 执行订单API调用
     *
     * @param order 订单实体
     * @param method HTTP方法
     * @param endpoint API端点
     * @param parameters 请求参数
     * @param action 操作类型
     * @return 更新后的订单实体
     * @throws ApiException 如果API调用失败
     */
    private OrderEntity executeOrderApiCall(
            OrderEntity order,
            String method,
            String endpoint,
            Map<String, Object> parameters,
            String action) throws ApiException {
        
        String executionId = IdGenerator.generateUuid();
        String requestData = JsonUtil.toJsonString(parameters);
        
        // 创建执行日志
        OrderExecutionLogEntity executionLog = new OrderExecutionLogEntity.Builder()
                .executionId(executionId)
                .orderId(order.getOrderId())
                .clientOrderId(order.getClientOrderId())
                .action(action)
                .requestData(requestData)
                .build();
        
        orderExecutionLogMapper.insert(executionLog);
        
        // 执行API调用（带重试）
        Exception lastException = null;
        String response = null;
        
        for (int retryCount = 0; retryCount <= MAX_RETRY_COUNT; retryCount++) {
            try {
                if (dryRun) {
                    log.info("干运行模式，跳过API调用: {}, {}, {}", method, endpoint, requestData);
                    response = generateDryRunResponse(order, action);
                } else {
                    // 使用正确的API调用方式，确保参数是LinkedHashMap类型
                    LinkedHashMap<String, Object> linkedParams = new LinkedHashMap<>(parameters);
                    switch (method) {
                        case "GET":
                            if (endpoint.equals("/fapi/v1/order")) {
                                response = umFuturesClient.getOrder(linkedParams);
                            } else {
                                throw new ApiException("不支持的GET端点: " + endpoint);
                            }
                            break;
                        case "POST":
                            if (endpoint.equals("/fapi/v1/order")) {
                                response = umFuturesClient.newOrder(linkedParams);
                            } else {
                                throw new ApiException("不支持的POST端点: " + endpoint);
                            }
                            break;
                        case "DELETE":
                            if (endpoint.equals("/fapi/v1/order")) {
                                response = umFuturesClient.cancelOrder(linkedParams);
                            } else if (endpoint.equals("/fapi/v1/allOpenOrders")) {
                                response = umFuturesClient.cancelAllOpenOrders(linkedParams);
                            } else {
                                throw new ApiException("不支持的DELETE端点: " + endpoint);
                            }
                            break;
                        default:
                            throw new ApiException("不支持的HTTP方法: " + method);
                    }
                }
                
                // 如果成功，更新执行日志和订单状态
                executionLog.setSuccess(true);
                executionLog.setResponseData(response);
                orderExecutionLogMapper.updateById(executionLog);
                
                // 更新订单信息
                updateOrderFromResponse(order, response, action);
                orderMapper.updateById(order);
                
                log.info("API调用成功: {}, 响应: {}", action, response);
                return order;
                
            } catch (Exception e) {
                lastException = e;
                log.warn("API调用失败(重试 {}/{}): {}, {}", retryCount + 1, MAX_RETRY_COUNT, action, e.getMessage());
                
                // 如果不是最后一次重试，等待一段时间后重试
                if (retryCount < MAX_RETRY_COUNT) {
                    try {
                        TimeUnit.MILLISECONDS.sleep(RETRY_WAIT_MS);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new ApiException("重试等待被中断");
                    }
                }
            }
        }
        
        // 所有重试都失败了，更新执行日志和订单状态
        executionLog.setSuccess(false);
        executionLog.setErrorCode("API_ERROR");
        executionLog.setErrorMessage(lastException != null ? lastException.getMessage() : "未知错误");
        orderExecutionLogMapper.updateById(executionLog);
        
        order.setStatus(OrderEnums.OrderStatus.REJECTED.name());
        order.setErrorCode("API_ERROR");
        order.setErrorMessage(lastException != null ? lastException.getMessage() : "未知错误");
        order.setUpdatedTime(System.currentTimeMillis());
        orderMapper.updateById(order);
        
        log.error("API调用最终失败: {}, {}", action, lastException != null ? lastException.getMessage() : "未知错误");
        throw new ApiException("API调用失败: " + (lastException != null ? lastException.getMessage() : "未知错误"));
    }

    /**
     * 从响应更新订单信息
     *
     * @param order 订单实体
     * @param response API响应
     * @param action 操作类型
     */
    private void updateOrderFromResponse(OrderEntity order, String response, String action) {
        Map<String, Object> responseMap = JsonUtil.parseJsonToMap(response);
        
        if ("CREATE".equals(action)) {
            // 处理创建订单响应
            if (responseMap.containsKey("orderId")) {
                order.setOrderId(responseMap.get("orderId").toString());
            }
            
            if (responseMap.containsKey("status")) {
                order.setStatus(responseMap.get("status").toString());
            }
            
            if (responseMap.containsKey("executedQty")) {
                String executedQty = responseMap.get("executedQty").toString();
                // 直接使用Double而不是BigDecimal
                order.setExecutedQuantity(Double.parseDouble(executedQty));
            }
            
            if (responseMap.containsKey("avgPrice")) {
                String avgPrice = responseMap.get("avgPrice").toString();
                Double price = Double.parseDouble(avgPrice);
                if (price > 0) {
                    order.setExecutedPrice(price);
                }
            }
            
            if (responseMap.containsKey("updateTime")) {
                order.setExecutedTime(Long.parseLong(responseMap.get("updateTime").toString()));
            }
        } else if ("QUERY".equals(action)) {
            // 处理查询订单响应
            if (responseMap.containsKey("status")) {
                order.setStatus(responseMap.get("status").toString());
            }
            
            if (responseMap.containsKey("executedQty")) {
                String executedQty = responseMap.get("executedQty").toString();
                // 直接使用Double而不是BigDecimal
                order.setExecutedQuantity(Double.parseDouble(executedQty));
            }
            
            if (responseMap.containsKey("avgPrice")) {
                String avgPrice = responseMap.get("avgPrice").toString();
                Double price = Double.parseDouble(avgPrice);
                if (price > 0) {
                    order.setExecutedPrice(price);
                }
            }
            
            if (responseMap.containsKey("updateTime")) {
                order.setExecutedTime(Long.parseLong(responseMap.get("updateTime").toString()));
            }
        } else if ("CANCEL".equals(action)) {
            // 处理取消订单响应
            order.setStatus(OrderEnums.OrderStatus.CANCELED.name());
        }
        
        order.setUpdatedTime(System.currentTimeMillis());
    }

    /**
     * 生成干运行模式下的响应
     *
     * @param order 订单实体
     * @param action 操作类型
     * @return 模拟响应
     */
    private String generateDryRunResponse(OrderEntity order, String action) {
        Map<String, Object> response = new LinkedHashMap<>();
        
        if ("CREATE".equals(action)) {
            response.put("orderId", "123456789");
            response.put("symbol", order.getSymbol());
            response.put("status", OrderEnums.OrderStatus.FILLED.name());
            response.put("clientOrderId", order.getClientOrderId());
            response.put("price", order.getPrice() != null ? order.getPrice().toString() : "0");
            response.put("avgPrice", order.getPrice() != null ? order.getPrice().toString() : "0");
            response.put("origQty", order.getQuantity().toString());
            response.put("executedQty", order.getQuantity().toString());
            response.put("side", order.getSide());
            response.put("positionSide", order.getPositionSide());
            response.put("type", order.getOrderType());
            response.put("updateTime", System.currentTimeMillis());
        } else if ("QUERY".equals(action)) {
            response.put("orderId", order.getOrderId());
            response.put("symbol", order.getSymbol());
            response.put("status", OrderEnums.OrderStatus.FILLED.name());
            response.put("clientOrderId", order.getClientOrderId());
            response.put("price", order.getPrice() != null ? order.getPrice().toString() : "0");
            response.put("avgPrice", order.getPrice() != null ? order.getPrice().toString() : "0");
            response.put("origQty", order.getQuantity().toString());
            response.put("executedQty", order.getQuantity().toString());
            response.put("side", order.getSide());
            response.put("positionSide", order.getPositionSide());
            response.put("type", order.getOrderType());
            response.put("updateTime", System.currentTimeMillis());
        } else if ("CANCEL".equals(action)) {
            response.put("orderId", order.getOrderId());
            response.put("symbol", order.getSymbol());
            response.put("status", OrderEnums.OrderStatus.CANCELED.name());
            response.put("clientOrderId", order.getClientOrderId());
        }
        
        response.put("dryRun", true);
        return JsonUtil.toJsonString(response);
    }

    /**
     * 生成客户端订单ID
     *
     * @param signal 交易信号
     * @return 客户端订单ID
     */
    private String generateClientOrderId(TradeSignal signal) {
        // 生成格式: 策略_时间戳_随机数
        String strategy = extractStrategyFromSignalId(signal.getSignalId());
        String timestamp = String.valueOf(System.currentTimeMillis());
        String random = IdGenerator.generateRandomAlphanumeric(4);
        return strategy + "_" + timestamp + "_" + random;
    }

    /**
     * 从信号ID提取策略名称
     *
     * @param signalId 信号ID
     * @return 策略名称
     */
    private String extractStrategyFromSignalId(String signalId) {
        if (signalId == null || signalId.isEmpty()) {
            return "UNKNOWN";
        }
        
        // 假设信号ID格式为: 策略名称_时间戳_随机数
        String[] parts = signalId.split("_");
        if (parts.length > 0) {
            return parts[0];
        }
        
        return "UNKNOWN";
    }
}