package com.crypto.trading.trade.service;

import com.crypto.trading.common.dto.trade.OrderDTO;
import com.crypto.trading.common.dto.trade.OrderStatusDTO;
import com.crypto.trading.common.enums.OrderSide;
import com.crypto.trading.common.enums.OrderStatus;
import com.crypto.trading.common.enums.OrderType;
import com.crypto.trading.sdk.client.BinanceApiClient;
import com.crypto.trading.trade.config.TradeExecutionConfig;
import com.crypto.trading.trade.executor.BinanceTradeExecutor;
import com.crypto.trading.trade.model.entity.order.OrderEntity;
import com.crypto.trading.trade.producer.TradeResultProducer;
import com.crypto.trading.trade.service.order.OrderManagementService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 交易执行服务测试类
 * <p>
 * 测试交易执行服务的功能
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class TradeExecutionServiceTest {

    @Mock
    private TradeResultProducer tradeResultProducer;

    @Mock
    private BinanceTradeExecutor binanceTradeExecutor;

    @Mock
    private OrderManagementService orderManagementService;

    @InjectMocks
    private TradeExecutionService tradeExecutionService;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.openMocks(this);
    }

    /**
     * 测试执行市价单
     */
    @Test
    public void testExecuteMarketOrder() {
        // 准备测试数据
        OrderDTO orderDTO = createTestOrderDTO();
        OrderEntity orderEntity = createTestOrderEntity();
        OrderEntity executedOrderEntity = createExecutedOrderEntity();

        // 模拟依赖服务的行为
        when(binanceTradeExecutor.executeMarketOrder(any(OrderEntity.class)))
                .thenReturn(CompletableFuture.completedFuture(executedOrderEntity));
        when(tradeResultProducer.sendOrderExecutionResult(any(OrderDTO.class)))
                .thenReturn(CompletableFuture.completedFuture(null));

        // 执行测试
        OrderDTO result = tradeExecutionService.executeMarketOrder(orderDTO).join();

        // 验证结果
        assertNotNull(result);
        assertEquals(OrderStatus.FILLED.getCode(), result.getStatus());
        assertEquals(1.0, result.getExecutedQuantity());
        assertEquals(50000.0, result.getExecutedPrice());

        // 验证依赖服务的调用
        verify(binanceTradeExecutor, times(1)).executeMarketOrder(any(OrderEntity.class));
    }

    /**
     * 测试查询订单状态
     */
    @Test
    public void testQueryOrderStatus() {
        // 准备测试数据
        String symbol = "BTCUSDT";
        String orderId = "123456";
        String clientOrderId = "test123";
        OrderEntity orderEntity = createTestOrderEntity();
        Map<String, Object> orderStatus = new HashMap<>();
        orderStatus.put("symbol", symbol);
        orderStatus.put("orderId", orderId);
        orderStatus.put("clientOrderId", clientOrderId);
        orderStatus.put("status", "FILLED");
        orderStatus.put("executedQty", "1.0");
        orderStatus.put("price", "50000.0");

        // 模拟依赖服务的行为
        when(binanceTradeExecutor.queryOrderStatus(eq(symbol), eq(orderId), eq(clientOrderId)))
                .thenReturn(CompletableFuture.completedFuture(orderStatus));
        when(orderManagementService.findOrder(eq(orderId), eq(clientOrderId)))
                .thenReturn(orderEntity);

        // 执行测试
        OrderDTO result = tradeExecutionService.queryOrderStatus(symbol, orderId, clientOrderId).join();

        // 验证结果
        assertNotNull(result);
        assertEquals(orderId, result.getOrderId());
        assertEquals(symbol, result.getSymbol());

        // 验证依赖服务的调用
        verify(binanceTradeExecutor, times(1)).queryOrderStatus(eq(symbol), eq(orderId), eq(clientOrderId));
        verify(orderManagementService, times(1)).findOrder(eq(orderId), eq(clientOrderId));
    }

    /**
     * 测试取消订单
     */
    @Test
    public void testCancelOrder() {
        // 准备测试数据
        String orderId = "123456";
        String clientOrderId = "test123";
        OrderEntity orderEntity = createTestOrderEntity();
        OrderEntity canceledOrderEntity = createCanceledOrderEntity();

        // 模拟依赖服务的行为
        when(orderManagementService.findOrder(eq(orderId), eq(clientOrderId)))
                .thenReturn(orderEntity);
        when(binanceTradeExecutor.cancelOrder(any(OrderEntity.class)))
                .thenReturn(CompletableFuture.completedFuture(canceledOrderEntity));

        // 执行测试
        OrderDTO result = tradeExecutionService.cancelOrder(orderId, clientOrderId).join();

        // 验证结果
        assertNotNull(result);
        assertEquals(OrderStatus.CANCELED.getCode(), result.getStatus());

        // 验证依赖服务的调用
        verify(orderManagementService, times(1)).findOrder(eq(orderId), eq(clientOrderId));
        verify(binanceTradeExecutor, times(1)).cancelOrder(any(OrderEntity.class));
    }

    /**
     * 测试处理订单执行结果
     */
    @Test
    public void testProcessOrderExecutionResult() {
        // 准备测试数据
        OrderDTO orderDTO = createTestOrderDTO();

        // 模拟依赖服务的行为
        when(tradeResultProducer.sendOrderExecutionResult(any(OrderDTO.class)))
                .thenReturn(CompletableFuture.completedFuture(null));

        // 执行测试
        CompletableFuture<Void> result = tradeExecutionService.processOrderExecutionResult(orderDTO);

        // 验证结果
        assertNotNull(result);

        // 验证依赖服务的调用
        verify(tradeResultProducer, times(1)).sendOrderExecutionResult(eq(orderDTO));
    }

    /**
     * 测试处理订单状态更新
     */
    @Test
    public void testProcessOrderStatusUpdate() {
        // 准备测试数据
        OrderStatusDTO orderStatusDTO = new OrderStatusDTO();
        orderStatusDTO.setOrderId("123456");
        orderStatusDTO.setSymbol("BTCUSDT");
        orderStatusDTO.setStatus(OrderStatus.FILLED.getCode());

        // 模拟依赖服务的行为
        when(tradeResultProducer.sendOrderStatusUpdate(any(OrderStatusDTO.class)))
                .thenReturn(CompletableFuture.completedFuture(null));

        // 执行测试
        CompletableFuture<Void> result = tradeExecutionService.processOrderStatusUpdate(orderStatusDTO);

        // 验证结果
        assertNotNull(result);

        // 验证依赖服务的调用
        verify(tradeResultProducer, times(1)).sendOrderStatusUpdate(eq(orderStatusDTO));
    }

    /**
     * 创建测试用的OrderDTO
     *
     * @return OrderDTO实例
     */
    private OrderDTO createTestOrderDTO() {
        OrderDTO orderDTO = new OrderDTO();
        orderDTO.setOrderId("123456");
        orderDTO.setClientOrderId("test123");
        orderDTO.setSymbol("BTCUSDT");
        orderDTO.setSide(OrderSide.BUY.getCode());
        orderDTO.setType(OrderType.MARKET.getCode());
        orderDTO.setQuantity(1.0);
        orderDTO.setPrice(50000.0);
        orderDTO.setStatus(OrderStatus.NEW.getCode());
        orderDTO.setCreatedTime(System.currentTimeMillis());
        return orderDTO;
    }

    /**
     * 创建测试用的OrderEntity
     *
     * @return OrderEntity实例
     */
    private OrderEntity createTestOrderEntity() {
        OrderEntity orderEntity = new OrderEntity();
        orderEntity.setId(1L);
        orderEntity.setOrderId("123456");
        orderEntity.setClientOrderId("test123");
        orderEntity.setSymbol("BTCUSDT");
        orderEntity.setSide(OrderSide.BUY.getCode());
        orderEntity.setPositionSide("BOTH");
        orderEntity.setType(OrderType.MARKET.getCode());
        orderEntity.setQuantity(1.0);
        orderEntity.setPrice(50000.0);
        orderEntity.setStatus(OrderStatus.NEW.getCode());
        orderEntity.setCreatedTime(System.currentTimeMillis());
        return orderEntity;
    }

    /**
     * 创建已执行的OrderEntity
     *
     * @return OrderEntity实例
     */
    private OrderEntity createExecutedOrderEntity() {
        OrderEntity orderEntity = createTestOrderEntity();
        orderEntity.setStatus(OrderStatus.FILLED.getCode());
        orderEntity.setExecutedQuantity(1.0);
        orderEntity.setExecutedPrice(50000.0);
        orderEntity.setExecutedTime(System.currentTimeMillis());
        orderEntity.setUpdatedTime(System.currentTimeMillis());
        return orderEntity;
    }

    /**
     * 创建已取消的OrderEntity
     *
     * @return OrderEntity实例
     */
    private OrderEntity createCanceledOrderEntity() {
        OrderEntity orderEntity = createTestOrderEntity();
        orderEntity.setStatus(OrderStatus.CANCELED.getCode());
        orderEntity.setUpdatedTime(System.currentTimeMillis());
        return orderEntity;
    }
}