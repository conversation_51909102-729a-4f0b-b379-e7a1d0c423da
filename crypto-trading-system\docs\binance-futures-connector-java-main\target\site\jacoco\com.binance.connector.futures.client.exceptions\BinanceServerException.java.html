<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>BinanceServerException.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">io.github.binance:binance-futures-connector-java</a> &gt; <a href="index.source.html" class="el_package">com.binance.connector.futures.client.exceptions</a> &gt; <span class="el_source">BinanceServerException.java</span></div><h1>BinanceServerException.java</h1><pre class="source lang-java linenums">package com.binance.connector.futures.client.exceptions;

/**
 * 币安期货API服务器异常类
 * 用于处理服务器相关的运行时异常，包含HTTP状态码信息
 */
public class BinanceServerException extends RuntimeException {

    // 序列化版本号
    private static final long serialVersionUID = 1L;
    // 无效状态码标识
    private static final int invalidStatusCode = -1;
    // HTTP状态码
    private final int httpStatusCode;

    /**
     * 构造函数，使用无效状态码
     * @param fullErrMsg 完整错误信息
     */
    public BinanceServerException(String fullErrMsg) {
<span class="nc" id="L21">        super(fullErrMsg);</span>
<span class="nc" id="L22">        this.httpStatusCode = invalidStatusCode;</span>
<span class="nc" id="L23">    }</span>

    /**
     * 构造函数，使用指定HTTP状态码
     * @param fullErrMsg 完整错误信息
     * @param httpStatusCode HTTP状态码
     */
    public BinanceServerException(String fullErrMsg, int httpStatusCode) {
<span class="fc" id="L31">        super(fullErrMsg);</span>
<span class="fc" id="L32">        this.httpStatusCode = httpStatusCode;</span>
<span class="fc" id="L33">    }</span>

    /**
     * 获取HTTP状态码
     * @return HTTP状态码
     */
    public int getHttpStatusCode() {
<span class="nc" id="L40">        return httpStatusCode;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.7.202105040129</span></div></body></html>