package com.crypto.trading.common.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.util.Locale;

/**
 * 数值工具类
 * <p>
 * 提供数值处理相关的工具方法，如格式化、精度处理、舍入等
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public final class NumberUtil {
    private static final Logger log = LoggerFactory.getLogger(NumberUtil.class);

    /**
     * 私有构造函数，防止实例化
     */
    private NumberUtil() {
        throw new IllegalStateException("工具类不允许实例化");
    }

    /**
     * 默认精度（小数位数）
     */
    private static final int DEFAULT_SCALE = 8;

    /**
     * 默认舍入模式
     */
    private static final RoundingMode DEFAULT_ROUNDING_MODE = RoundingMode.HALF_UP;

    /**
     * 比较容差，用于浮点数比较
     */
    private static final double EPSILON = 1e-10;

    /**
     * 设置精度（小数位数）
     *
     * @param value 数值
     * @param scale 精度（小数位数）
     * @return 处理后的BigDecimal
     */
    public static BigDecimal setScale(BigDecimal value, int scale) {
        return setScale(value, scale, DEFAULT_ROUNDING_MODE);
    }

    /**
     * 设置精度（小数位数）
     *
     * @param value        数值
     * @param scale        精度（小数位数）
     * @param roundingMode 舍入模式
     * @return 处理后的BigDecimal
     */
    public static BigDecimal setScale(BigDecimal value, int scale, RoundingMode roundingMode) {
        if (value == null) {
            return null;
        }
        return value.setScale(scale, roundingMode);
    }

    /**
     * 格式化为指定小数位数的字符串
     *
     * @param value 数值
     * @param scale 精度（小数位数）
     * @return 格式化后的字符串
     */
    public static String format(BigDecimal value, int scale) {
        if (value == null) {
            return null;
        }
        return setScale(value, scale).toPlainString();
    }

    /**
     * 格式化为指定格式的字符串
     *
     * @param value  数值
     * @param pattern 格式模式，如"#,##0.00"表示带千分位且保留两位小数
     * @return 格式化后的字符串
     */
    public static String format(BigDecimal value, String pattern) {
        if (value == null) {
            return null;
        }
        DecimalFormat df = new DecimalFormat(pattern);
        return df.format(value);
    }

    /**
     * 格式化为带千分位的字符串
     *
     * @param value 数值
     * @param scale 精度（小数位数）
     * @return 格式化后的字符串
     */
    public static String formatWithGrouping(BigDecimal value, int scale) {
        if (value == null) {
            return null;
        }
        NumberFormat nf = NumberFormat.getNumberInstance(Locale.US);
        nf.setMaximumFractionDigits(scale);
        nf.setMinimumFractionDigits(scale);
        nf.setGroupingUsed(true);
        return nf.format(value);
    }

    /**
     * 格式化为百分比字符串
     *
     * @param value 数值（如0.1234表示12.34%）
     * @param scale 精度（小数位数）
     * @return 格式化后的百分比字符串
     */
    public static String formatPercent(BigDecimal value, int scale) {
        if (value == null) {
            return null;
        }
        NumberFormat nf = NumberFormat.getPercentInstance(Locale.US);
        nf.setMaximumFractionDigits(scale);
        nf.setMinimumFractionDigits(scale);
        return nf.format(value);
    }

    /**
     * 四舍五入
     *
     * @param value 数值
     * @param scale 精度（小数位数）
     * @return 四舍五入后的BigDecimal
     */
    public static BigDecimal round(BigDecimal value, int scale) {
        return setScale(value, scale, RoundingMode.HALF_UP);
    }

    /**
     * 向上取整
     *
     * @param value 数值
     * @param scale 精度（小数位数）
     * @return 向上取整后的BigDecimal
     */
    public static BigDecimal ceil(BigDecimal value, int scale) {
        return setScale(value, scale, RoundingMode.CEILING);
    }

    /**
     * 向下取整
     *
     * @param value 数值
     * @param scale 精度（小数位数）
     * @return 向下取整后的BigDecimal
     */
    public static BigDecimal floor(BigDecimal value, int scale) {
        return setScale(value, scale, RoundingMode.FLOOR);
    }

    /**
     * 截断（不进行舍入）
     *
     * @param value 数值
     * @param scale 精度（小数位数）
     * @return 截断后的BigDecimal
     */
    public static BigDecimal truncate(BigDecimal value, int scale) {
        return setScale(value, scale, RoundingMode.DOWN);
    }

    /**
     * 比较两个数值是否相等（考虑精度误差）
     *
     * @param value1 数值1
     * @param value2 数值2
     * @return 是否相等
     */
    public static boolean equals(double value1, double value2) {
        return Math.abs(value1 - value2) < EPSILON;
    }

    /**
     * 比较两个数值是否相等（考虑精度误差）
     *
     * @param value1 数值1
     * @param value2 数值2
     * @param scale  精度（小数位数）
     * @return 是否相等
     */
    public static boolean equals(BigDecimal value1, BigDecimal value2, int scale) {
        if (value1 == null && value2 == null) {
            return true;
        }
        if (value1 == null || value2 == null) {
            return false;
        }
        return value1.setScale(scale, DEFAULT_ROUNDING_MODE)
                .compareTo(value2.setScale(scale, DEFAULT_ROUNDING_MODE)) == 0;
    }

    /**
     * 比较两个数值大小（考虑精度误差）
     *
     * @param value1 数值1
     * @param value2 数值2
     * @param scale  精度（小数位数）
     * @return 比较结果（-1：小于，0：等于，1：大于）
     */
    public static int compare(BigDecimal value1, BigDecimal value2, int scale) {
        if (value1 == null && value2 == null) {
            return 0;
        }
        if (value1 == null) {
            return -1;
        }
        if (value2 == null) {
            return 1;
        }
        return value1.setScale(scale, DEFAULT_ROUNDING_MODE)
                .compareTo(value2.setScale(scale, DEFAULT_ROUNDING_MODE));
    }

    /**
     * 判断数值是否为0或接近0（考虑精度误差）
     *
     * @param value 数值
     * @return 是否为0或接近0
     */
    public static boolean isZero(double value) {
        return equals(value, 0.0);
    }

    /**
     * 判断数值是否为0或接近0（考虑精度误差）
     *
     * @param value 数值
     * @param scale 精度（小数位数）
     * @return 是否为0或接近0
     */
    public static boolean isZero(BigDecimal value, int scale) {
        if (value == null) {
            return false;
        }
        return equals(value, BigDecimal.ZERO, scale);
    }

    /**
     * 获取精度（小数位数）
     *
     * @param value 数值
     * @return 精度（小数位数）
     */
    public static int getScale(BigDecimal value) {
        if (value == null) {
            return 0;
        }
        
        // 对于BigDecimal，直接使用scale()方法获取精度
        // 这会返回实际存储的精度，不受stripTrailingZeros影响
        return value.scale();
    }

    /**
     * 计算涨跌幅
     *
     * @param current 当前价格
     * @param base    基准价格
     * @return 涨跌幅（小数形式，如0.1234表示上涨12.34%）
     */
    public static BigDecimal calcChangeRate(BigDecimal current, BigDecimal base) {
        if (current == null || base == null || isZero(base, DEFAULT_SCALE)) {
            return BigDecimal.ZERO;
        }
        return current.subtract(base).divide(base, DEFAULT_SCALE, DEFAULT_ROUNDING_MODE);
    }

    /**
     * 计算涨跌幅并格式化为百分比字符串
     *
     * @param current 当前价格
     * @param base    基准价格
     * @param scale   精度（小数位数）
     * @return 涨跌幅百分比字符串
     */
    public static String calcChangeRatePercent(BigDecimal current, BigDecimal base, int scale) {
        return formatPercent(calcChangeRate(current, base), scale);
    }

    /**
     * 计算涨跌额
     *
     * @param current 当前价格
     * @param base    基准价格
     * @return 涨跌额
     */
    public static BigDecimal calcChangeAmount(BigDecimal current, BigDecimal base) {
        if (current == null || base == null) {
            return BigDecimal.ZERO;
        }
        return current.subtract(base);
    }

    /**
     * 按照交易对规则处理价格精度
     *
     * @param price       价格
     * @param pricePrecision 价格精度
     * @return 处理后的价格
     */
    public static BigDecimal processPrice(BigDecimal price, int pricePrecision) {
        return round(price, pricePrecision);
    }

    /**
     * 按照交易对规则处理数量精度
     *
     * @param quantity       数量
     * @param quantityPrecision 数量精度
     * @return 处理后的数量
     */
    public static BigDecimal processQuantity(BigDecimal quantity, int quantityPrecision) {
        return truncate(quantity, quantityPrecision);
    }

    /**
     * 解析字符串为BigDecimal
     *
     * @param str 字符串
     * @return BigDecimal对象
     */
    public static BigDecimal toBigDecimal(String str) {
        if (ValidationUtil.isBlank(str)) {
            return null;
        }
        try {
            return new BigDecimal(str.trim());
        } catch (NumberFormatException e) {
            log.error("字符串转BigDecimal失败: {}", str, e);
            return null;
        }
    }

    /**
     * 解析字符串为BigDecimal，如果解析失败则返回默认值
     *
     * @param str          字符串
     * @param defaultValue 默认值
     * @return BigDecimal对象
     */
    public static BigDecimal toBigDecimal(String str, BigDecimal defaultValue) {
        BigDecimal result = toBigDecimal(str);
        return result != null ? result : defaultValue;
    }

    /**
     * 解析对象为BigDecimal
     *
     * @param obj 对象
     * @return BigDecimal对象
     */
    public static BigDecimal toBigDecimal(Object obj) {
        if (obj == null) {
            return null;
        }
        if (obj instanceof BigDecimal) {
            return (BigDecimal) obj;
        }
        if (obj instanceof Number) {
            return new BigDecimal(obj.toString());
        }
        return toBigDecimal(obj.toString());
    }

    /**
     * 解析对象为BigDecimal，如果解析失败则返回默认值
     *
     * @param obj          对象
     * @param defaultValue 默认值
     * @return BigDecimal对象
     */
    public static BigDecimal toBigDecimal(Object obj, BigDecimal defaultValue) {
        BigDecimal result = toBigDecimal(obj);
        return result != null ? result : defaultValue;
    }
}