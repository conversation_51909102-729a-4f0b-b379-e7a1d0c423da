package com.crypto.trading.common.util;

import com.crypto.trading.common.constant.SystemConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Collection;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * 验证工具类
 * <p>
 * 提供参数验证相关的工具方法
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public final class ValidationUtil {
    private static final Logger log = LoggerFactory.getLogger(ValidationUtil.class);

    /**
     * 私有构造函数，防止实例化
     */
    private ValidationUtil() {
        throw new IllegalStateException("工具类不允许实例化");
    }

    /**
     * 邮箱正则表达式
     */
    private static final Pattern EMAIL_PATTERN = Pattern.compile("^[a-zA-Z0-9_+&*-]+(?:\\.[a-zA-Z0-9_+&*-]+)*@(?:[a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,7}$");

    /**
     * 手机号正则表达式（中国大陆）
     */
    private static final Pattern MOBILE_PATTERN = Pattern.compile("^1[3-9]\\d{9}$");

    /**
     * 价格正则表达式（匹配数字，包括小数）
     */
    private static final Pattern PRICE_PATTERN = Pattern.compile("^\\d+(\\.\\d+)?$");

    /**
     * 判断对象是否为空
     *
     * @param obj 对象
     * @return 是否为空
     */
    public static <T> boolean isEmpty(T obj) {
        if (obj == null) {
            return true;
        }
        if (obj instanceof String) {
            return ((String) obj).isEmpty();
        }
        if (obj instanceof Collection) {
            return ((Collection<?>) obj).isEmpty();
        }
        if (obj instanceof Map) {
            return ((Map<?, ?>) obj).isEmpty();
        }
        if (obj.getClass().isArray()) {
            return java.lang.reflect.Array.getLength(obj) == 0;
        }
        return false;
    }

    /**
     * 判断对象是否不为空
     *
     * @param obj 对象
     * @return 是否不为空
     */
    public static <T> boolean isNotEmpty(T obj) {
        return !isEmpty(obj);
    }

    /**
     * 判断字符串是否为空白
     *
     * @param str 字符串
     * @return 是否为空白
     */
    public static boolean isBlank(String str) {
        return str == null || str.trim().isEmpty();
    }

    /**
     * 判断字符串是否不为空白
     *
     * @param str 字符串
     * @return 是否不为空白
     */
    public static boolean isNotBlank(String str) {
        return !isBlank(str);
    }

    /**
     * 判断对象是否为null
     *
     * @param obj 对象
     * @return 是否为null
     */
    public static boolean isNull(Object obj) {
        return obj == null;
    }

    /**
     * 判断对象是否不为null
     *
     * @param obj 对象
     * @return 是否不为null
     */
    public static boolean isNotNull(Object obj) {
        return obj != null;
    }

    /**
     * 判断集合是否为空
     *
     * @param collection 集合
     * @param <E>        元素类型
     * @return 是否为空
     */
    public static <E> boolean isCollectionEmpty(Collection<E> collection) {
        return collection == null || collection.isEmpty();
    }

    /**
     * 判断集合是否不为空
     *
     * @param collection 集合
     * @param <E>        元素类型
     * @return 是否不为空
     */
    public static <E> boolean isCollectionNotEmpty(Collection<E> collection) {
        return !isCollectionEmpty(collection);
    }

    /**
     * 判断Map是否为空
     *
     * @param map Map
     * @param <K> 键类型
     * @param <V> 值类型
     * @return 是否为空
     */
    public static <K, V> boolean isMapEmpty(Map<K, V> map) {
        return map == null || map.isEmpty();
    }

    /**
     * 判断Map是否不为空
     *
     * @param map Map
     * @param <K> 键类型
     * @param <V> 值类型
     * @return 是否不为空
     */
    public static <K, V> boolean isMapNotEmpty(Map<K, V> map) {
        return !isMapEmpty(map);
    }

    /**
     * 判断数组是否为空
     *
     * @param array 数组
     * @param <T>   元素类型
     * @return 是否为空
     */
    public static <T> boolean isArrayEmpty(T[] array) {
        return array == null || array.length == 0;
    }

    /**
     * 判断数组是否不为空
     *
     * @param array 数组
     * @param <T>   元素类型
     * @return 是否不为空
     */
    public static <T> boolean isArrayNotEmpty(T[] array) {
        return !isArrayEmpty(array);
    }

    /**
     * 判断数字是否在范围内
     *
     * @param value 数字
     * @param min   最小值（包含）
     * @param max   最大值（包含）
     * @return 是否在范围内
     */
    public static boolean isInRange(int value, int min, int max) {
        return value >= min && value <= max;
    }

    /**
     * 判断数字是否在范围内
     *
     * @param value 数字
     * @param min   最小值（包含）
     * @param max   最大值（包含）
     * @return 是否在范围内
     */
    public static boolean isInRange(long value, long min, long max) {
        return value >= min && value <= max;
    }

    /**
     * 判断数字是否在范围内
     *
     * @param value 数字
     * @param min   最小值（包含）
     * @param max   最大值（包含）
     * @return 是否在范围内
     */
    public static boolean isInRange(double value, double min, double max) {
        return value >= min && value <= max;
    }

    /**
     * 判断数字是否在范围内
     *
     * @param value 数字
     * @param min   最小值（包含）
     * @param max   最大值（包含）
     * @return 是否在范围内
     */
    public static boolean isInRange(BigDecimal value, BigDecimal min, BigDecimal max) {
        return value.compareTo(min) >= 0 && value.compareTo(max) <= 0;
    }

    /**
     * 判断字符串长度是否在范围内
     *
     * @param str    字符串
     * @param minLen 最小长度（包含）
     * @param maxLen 最大长度（包含）
     * @return 是否在范围内
     */
    public static boolean isLengthInRange(String str, int minLen, int maxLen) {
        if (str == null) {
            return false;
        }
        int length = str.length();
        return length >= minLen && length <= maxLen;
    }

    /**
     * 判断字符串是否匹配正则表达式
     *
     * @param str     字符串
     * @param pattern 正则表达式
     * @return 是否匹配
     */
    public static boolean matches(String str, String pattern) {
        return str != null && Pattern.matches(pattern, str);
    }

    /**
     * 判断字符串是否匹配正则表达式
     *
     * @param str     字符串
     * @param pattern 正则表达式对象
     * @return 是否匹配
     */
    public static boolean matches(String str, Pattern pattern) {
        return str != null && pattern.matcher(str).matches();
    }

    /**
     * 判断字符串是否为邮箱格式
     *
     * @param email 邮箱字符串
     * @return 是否为邮箱格式
     */
    public static boolean isEmail(String email) {
        return matches(email, EMAIL_PATTERN);
    }

    /**
     * 判断字符串是否为手机号格式（中国大陆）
     *
     * @param mobile 手机号字符串
     * @return 是否为手机号格式
     */
    public static boolean isMobile(String mobile) {
        return matches(mobile, MOBILE_PATTERN);
    }

    /**
     * 判断字符串是否为价格格式
     *
     * @param price 价格字符串
     * @return 是否为价格格式
     */
    public static boolean isPrice(String price) {
        return matches(price, PRICE_PATTERN);
    }

    /**
     * 判断字符串是否为整数
     *
     * @param str 字符串
     * @return 是否为整数
     */
    public static boolean isInteger(String str) {
        if (isBlank(str)) {
            return false;
        }
        try {
            Integer.parseInt(str);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 判断字符串是否为长整数
     *
     * @param str 字符串
     * @return 是否为长整数
     */
    public static boolean isLong(String str) {
        if (isBlank(str)) {
            return false;
        }
        try {
            Long.parseLong(str);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 判断字符串是否为浮点数
     *
     * @param str 字符串
     * @return 是否为浮点数
     */
    public static boolean isDouble(String str) {
        if (isBlank(str)) {
            return false;
        }
        try {
            Double.parseDouble(str);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 判断字符串是否为BigDecimal
     *
     * @param str 字符串
     * @return 是否为BigDecimal
     */
    public static boolean isBigDecimal(String str) {
        if (isBlank(str)) {
            return false;
        }
        try {
            new BigDecimal(str);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 判断日期字符串是否合法
     *
     * @param dateStr 日期字符串
     * @param pattern 日期格式
     * @return 是否合法
     */
    public static boolean isValidDate(String dateStr, String pattern) {
        if (isBlank(dateStr)) {
            return false;
        }
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(pattern);
            sdf.setLenient(false);
            sdf.parse(dateStr);
            return true;
        } catch (ParseException e) {
            return false;
        }
    }

    /**
     * 判断日期时间字符串是否合法
     *
     * @param dateTimeStr 日期时间字符串
     * @return 是否合法
     */
    public static boolean isValidDateTime(String dateTimeStr) {
        return isValidDate(dateTimeStr, SystemConstants.DATETIME_FORMAT);
    }

    /**
     * 判断日期字符串是否合法
     *
     * @param dateStr 日期字符串
     * @return 是否合法
     */
    public static boolean isValidDate(String dateStr) {
        return isValidDate(dateStr, SystemConstants.DATE_FORMAT);
    }

    /**
     * 判断时间字符串是否合法
     *
     * @param timeStr 时间字符串
     * @return 是否合法
     */
    public static boolean isValidTime(String timeStr) {
        return isValidDate(timeStr, SystemConstants.TIME_FORMAT);
    }

    /**
     * 判断LocalDateTime是否合法
     *
     * @param dateTimeStr 日期时间字符串
     * @param pattern     日期时间格式
     * @return 是否合法
     */
    public static boolean isValidLocalDateTime(String dateTimeStr, String pattern) {
        if (isBlank(dateTimeStr)) {
            return false;
        }
        try {
            LocalDateTime.parse(dateTimeStr, DateTimeFormatter.ofPattern(pattern));
            return true;
        } catch (DateTimeParseException e) {
            return false;
        }
    }

    /**
     * 判断LocalDate是否合法
     *
     * @param dateStr 日期字符串
     * @param pattern 日期格式
     * @return 是否合法
     */
    public static boolean isValidLocalDate(String dateStr, String pattern) {
        if (isBlank(dateStr)) {
            return false;
        }
        try {
            LocalDate.parse(dateStr, DateTimeFormatter.ofPattern(pattern));
            return true;
        } catch (DateTimeParseException e) {
            return false;
        }
    }

    /**
     * 验证价格精度是否符合要求
     *
     * @param price      价格
     * @param maxDecimals 最大小数位数
     * @return 是否符合要求
     */
    public static boolean isValidPriceScale(BigDecimal price, int maxDecimals) {
        if (price == null) {
            return false;
        }
        String priceStr = price.stripTrailingZeros().toPlainString();
        int decimalIndex = priceStr.indexOf('.');
        if (decimalIndex == -1) {
            return true;
        }
        int scale = priceStr.length() - decimalIndex - 1;
        return scale <= maxDecimals;
    }

    /**
     * 验证数量精度是否符合要求
     *
     * @param quantity    数量
     * @param maxDecimals 最大小数位数
     * @return 是否符合要求
     */
    public static boolean isValidQuantityScale(BigDecimal quantity, int maxDecimals) {
        return isValidPriceScale(quantity, maxDecimals);
    }

    /**
     * 验证参数是否满足所有条件，如果有一个条件不满足，则抛出异常
     *
     * @param expression 条件表达式
     * @param message    错误消息
     */
    public static void validate(boolean expression, String message) {
        if (!expression) {
            throw new IllegalArgumentException(message);
        }
    }

    /**
     * 验证参数不为空，如果为空则抛出异常
     *
     * @param obj     对象
     * @param message 错误消息
     */
    public static void validateNotEmpty(Object obj, String message) {
        validate(isNotEmpty(obj), message);
    }

    /**
     * 验证参数不为null，如果为null则抛出异常
     *
     * @param obj     对象
     * @param message 错误消息
     */
    public static void validateNotNull(Object obj, String message) {
        validate(isNotNull(obj), message);
    }

    /**
     * 验证参数在范围内，如果不在范围内则抛出异常
     *
     * @param value   值
     * @param min     最小值
     * @param max     最大值
     * @param message 错误消息
     */
    public static void validateInRange(int value, int min, int max, String message) {
        validate(isInRange(value, min, max), message);
    }
} 