package com.crypto.trading.common.enums;

/**
 * 交易所类型枚举
 * <p>
 * 表示不同的加密货币交易所
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public enum ExchangeType {

    /**
     * 币安
     */
    BINANCE("binance", "币安", true),

    /**
     * 币安测试网
     */
    BINANCE_TESTNET("binance_testnet", "币安测试网", true),

    /**
     * 欧易
     */
    OKEX("okex", "欧易", false),

    /**
     * 火币
     */
    HUOBI("huobi", "火币", false),

    /**
     * FTX
     */
    FTX("ftx", "FTX", false),

    /**
     * BitMEX
     */
    BITMEX("bitmex", "BitMEX", false),

    /**
     * Bybit
     */
    BYBIT("bybit", "Bybit", false),

    /**
     * Coinbase
     */
    COINBASE("coinbase", "Coinbase", false),

    /**
     * Kraken
     */
    KRAKEN("kraken", "Kraken", false),

    /**
     * 德利
     */
    DERIBIT("deribit", "德利", false),

    /**
     * Bitfinex
     */
    BITFINEX("bitfinex", "Bitfinex", false),

    /**
     * 芝麻开门
     */
    GATE("gate", "芝麻开门", false),

    /**
     * KuCoin
     */
    KUCOIN("kucoin", "KuCoin", false);

    /**
     * 代码
     */
    private final String code;

    /**
     * 描述
     */
    private final String desc;

    /**
     * 是否已支持
     */
    private final boolean supported;

    /**
     * 构造函数
     *
     * @param code      代码
     * @param desc      描述
     * @param supported 是否已支持
     */
    ExchangeType(String code, String desc, boolean supported) {
        this.code = code;
        this.desc = desc;
        this.supported = supported;
    }

    /**
     * 获取代码
     *
     * @return 代码
     */
    public String getCode() {
        return code;
    }

    /**
     * 获取描述
     *
     * @return 描述
     */
    public String getDesc() {
        return desc;
    }

    /**
     * 是否已支持
     *
     * @return 如果已支持，则返回true，否则返回false
     */
    public boolean isSupported() {
        return supported;
    }

    /**
     * 根据代码获取枚举值
     *
     * @param code 代码
     * @return 枚举值
     */
    public static ExchangeType fromCode(String code) {
        for (ExchangeType type : ExchangeType.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知的交易所类型代码: " + code);
    }

    /**
     * 获取所有已支持的交易所类型
     *
     * @return 已支持的交易所类型数组
     */
    public static ExchangeType[] getSupportedExchanges() {
        return java.util.Arrays.stream(values())
                .filter(ExchangeType::isSupported)
                .toArray(ExchangeType[]::new);
    }
} 