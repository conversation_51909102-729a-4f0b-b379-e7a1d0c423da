{"namespace": "com.crypto.trading.common.avro", "type": "record", "name": "KlineData", "doc": "K线数据的Avro模式定义", "fields": [{"name": "symbol", "type": "string", "doc": "交易对符号，如BTCUSDT"}, {"name": "interval", "type": "string", "doc": "K线间隔，如1m、5m、1h等"}, {"name": "openTime", "type": "long", "doc": "开盘时间（毫秒时间戳）"}, {"name": "closeTime", "type": "long", "doc": "收盘时间（毫秒时间戳）"}, {"name": "open", "type": "double", "doc": "开盘价"}, {"name": "high", "type": "double", "doc": "最高价"}, {"name": "low", "type": "double", "doc": "最低价"}, {"name": "close", "type": "double", "doc": "收盘价"}, {"name": "volume", "type": "double", "doc": "交易量"}, {"name": "quoteVolume", "type": "double", "doc": "报价资产交易量"}, {"name": "trades", "type": "int", "doc": "交易笔数"}, {"name": "takerBuyBaseVolume", "type": "double", "doc": "主动买入交易量"}, {"name": "takerBuyQuoteVolume", "type": "double", "doc": "主动买入报价资产交易量"}, {"name": "completed", "type": "boolean", "doc": "K线是否已完成"}, {"name": "timestamp", "type": "long", "doc": "消息生成时间（毫秒时间戳）"}, {"name": "source", "type": "string", "doc": "数据来源", "default": "binance"}]}