package com.binance.connector.futures.client.exceptions;

/**
 * 币安期货API服务器异常类
 * 用于处理服务器相关的运行时异常，包含HTTP状态码信息
 */
public class BinanceServerException extends RuntimeException {

    // 序列化版本号
    private static final long serialVersionUID = 1L;
    // 无效状态码标识
    private static final int invalidStatusCode = -1;
    // HTTP状态码
    private final int httpStatusCode;

    /**
     * 构造函数，使用无效状态码
     * @param fullErrMsg 完整错误信息
     */
    public BinanceServerException(String fullErrMsg) {
        super(fullErrMsg);
        this.httpStatusCode = invalidStatusCode;
    }

    /**
     * 构造函数，使用指定HTTP状态码
     * @param fullErrMsg 完整错误信息
     * @param httpStatusCode HTTP状态码
     */
    public BinanceServerException(String fullErrMsg, int httpStatusCode) {
        super(fullErrMsg);
        this.httpStatusCode = httpStatusCode;
    }

    /**
     * 获取HTTP状态码
     * @return HTTP状态码
     */
    public int getHttpStatusCode() {
        return httpStatusCode;
    }
}