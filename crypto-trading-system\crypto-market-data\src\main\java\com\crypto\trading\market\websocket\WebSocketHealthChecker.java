package com.crypto.trading.market.websocket;

import com.crypto.trading.common.util.VirtualThreadMonitor;
import com.crypto.trading.market.config.WebSocketConfig;
import com.crypto.trading.sdk.websocket.WebSocketConnection;
import com.crypto.trading.sdk.websocket.WebSocketConnectionPool;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;

/**
 * WebSocket健康检查器
 * 负责定期检查WebSocket连接的健康状态，并在必要时自动重连
 */
@Component
public class WebSocketHealthChecker {

    private static final Logger log = LoggerFactory.getLogger(WebSocketHealthChecker.class);

    /**
     * 连接状态映射表
     * 键为连接ID，值为连接状态信息
     */
    private final Map<Integer, ConnectionStatus> connectionStatusMap = new ConcurrentHashMap<>();

    /**
     * 虚拟线程监控器
     */
    private final VirtualThreadMonitor virtualThreadMonitor = VirtualThreadMonitor.getInstance();

    /**
     * 健康检查调度器
     */
    private ScheduledExecutorService healthCheckScheduler;

    /**
     * 运行状态标志
     */
    private final AtomicBoolean running = new AtomicBoolean(false);

    /**
     * 总检查次数
     */
    private final AtomicLong totalChecks = new AtomicLong(0);

    /**
     * 总修复次数
     */
    private final AtomicLong totalRepairs = new AtomicLong(0);

    @Autowired
    private WebSocketConnectionPool connectionPool;

    @Autowired
    private WebSocketConfig webSocketConfig;

    /**
     * 初始化方法
     * 在应用启动时启动健康检查
     */
    @PostConstruct
    public void init() {
        // 启动健康检查
        start();
    }

    /**
     * 销毁方法
     * 在应用关闭时停止健康检查
     */
    @PreDestroy
    public void destroy() {
        // 停止健康检查
        stop();
    }

    /**
     * 启动健康检查
     */
    public synchronized void start() {
        if (running.get()) {
            log.info("WebSocket健康检查器已经在运行中");
            return;
        }

        // 创建虚拟线程调度器
        this.healthCheckScheduler = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread thread = Thread.ofVirtual().name("websocket-health-checker").unstarted(r);
            return thread;
        });

        // 获取健康检查间隔时间（毫秒）
        long healthCheckInterval = webSocketConfig.getHealthCheckInterval();
        
        // 启动定期健康检查
        healthCheckScheduler.scheduleAtFixedRate(
                this::checkAllConnections,
                healthCheckInterval,
                healthCheckInterval,
                TimeUnit.MILLISECONDS);

        running.set(true);
        log.info("WebSocket健康检查器已启动，检查间隔: {}毫秒", healthCheckInterval);
    }

    /**
     * 停止健康检查
     */
    public synchronized void stop() {
        if (!running.get()) {
            return;
        }

        if (healthCheckScheduler != null) {
            healthCheckScheduler.shutdown();
            try {
                if (!healthCheckScheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                    healthCheckScheduler.shutdownNow();
                }
            } catch (InterruptedException e) {
                healthCheckScheduler.shutdownNow();
                Thread.currentThread().interrupt();
            }
            healthCheckScheduler = null;
        }

        running.set(false);
        log.info("WebSocket健康检查器已停止");
    }

    /**
     * 注册连接
     *
     * @param connectionId 连接ID
     * @param description  连接描述
     */
    public void registerConnection(int connectionId, String description) {
        ConnectionStatus status = new ConnectionStatus(connectionId, description);
        connectionStatusMap.put(connectionId, status);
        log.info("已注册WebSocket连接到健康检查器: ID={}, 描述={}", connectionId, description);
    }

    /**
     * 注销连接
     *
     * @param connectionId 连接ID
     */
    public void unregisterConnection(int connectionId) {
        connectionStatusMap.remove(connectionId);
        log.info("已从健康检查器注销WebSocket连接: ID={}", connectionId);
    }

    /**
     * 检查所有连接
     */
    private void checkAllConnections() {
        try {
            long startTime = System.currentTimeMillis();
            long threadId = virtualThreadMonitor.registerVirtualThread("websocket-health-check");
            
            log.debug("开始WebSocket连接健康检查，当前注册连接数: {}", connectionStatusMap.size());
            
            // 增加总检查次数
            totalChecks.incrementAndGet();
            
            // 并行检查所有连接
            connectionStatusMap.forEach((connectionId, status) -> {
                Thread.ofVirtual()
                      .name("check-connection-" + connectionId)
                      .start(() -> {
                    try {
                        checkConnection(connectionId, status);
                    } catch (Exception e) {
                        log.error("检查WebSocket连接时发生异常: ID={}", connectionId, e);
                    }
                });
            });
            
            long duration = System.currentTimeMillis() - startTime;
            virtualThreadMonitor.recordVirtualThreadCompletion("websocket-health-check", duration);
            
            log.debug("WebSocket连接健康检查完成，耗时: {}毫秒", duration);
        } catch (Exception e) {
            log.error("执行WebSocket连接健康检查时发生异常", e);
        }
    }

    /**
     * 检查单个连接
     *
     * @param connectionId 连接ID
     * @param status       连接状态
     */
    private void checkConnection(int connectionId, ConnectionStatus status) {
        try {
            // 检查连接是否存在
            if (!connectionPool.hasConnection(connectionId)) {
                log.warn("WebSocket连接不存在: ID={}", connectionId);
                status.incrementFailCount();
                return;
            }

            // 检查连接是否活跃
            boolean isActive = connectionPool.isConnectionActive(connectionId);
            status.setLastCheckTime(System.currentTimeMillis());

            if (isActive) {
                // 连接正常，重置失败计数
                status.resetFailCount();
                log.debug("WebSocket连接健康: ID={}, 描述={}", connectionId, status.getDescription());
            } else {
                // 连接异常，增加失败计数
                status.incrementFailCount();
                log.warn("WebSocket连接异常: ID={}, 描述={}, 连续失败次数={}", 
                        connectionId, status.getDescription(), status.getFailCount());

                // 如果连续失败次数超过阈值，尝试修复
                if (status.getFailCount() >= webSocketConfig.getMaxFailCount()) {
                    repairConnection(connectionId);
                }
            }
        } catch (Exception e) {
            log.error("检查WebSocket连接状态时发生异常: ID={}", connectionId, e);
            status.incrementFailCount();
        }
    }

    /**
     * 修复连接
     *
     * @param connectionId 连接ID
     */
    private void repairConnection(int connectionId) {
        try {
            log.info("尝试修复WebSocket连接: ID={}", connectionId);
            
            // 增加总修复次数
            totalRepairs.incrementAndGet();
            
            // 获取连接对象
            WebSocketConnection connection = connectionPool.getConnection(connectionId);
            if (connection == null) {
                log.warn("无法修复WebSocket连接，连接不存在: ID={}", connectionId);
                return;
            }
            
            // 使用虚拟线程执行修复操作
            Thread.ofVirtual()
                  .name("repair-connection-" + connectionId)
                  .start(() -> {
                try {
                    // 关闭连接
                    connection.close();
                    
                    // 等待一段时间
                    Thread.sleep(webSocketConfig.getReconnectInterval());
                    
                    // 重新连接
                    connection.connect();
                    
                    log.info("成功修复WebSocket连接: ID={}", connectionId);
                    
                    // 重置连接状态
                    ConnectionStatus status = connectionStatusMap.get(connectionId);
                    if (status != null) {
                        status.resetFailCount();
                    }
                } catch (Exception e) {
                    log.error("修复WebSocket连接时发生异常: ID={}", connectionId, e);
                }
            });
        } catch (Exception e) {
            log.error("修复WebSocket连接时发生异常: ID={}", connectionId, e);
        }
    }

    /**
     * 获取总检查次数
     *
     * @return 总检查次数
     */
    public long getTotalChecks() {
        return totalChecks.get();
    }

    /**
     * 获取总修复次数
     *
     * @return 总修复次数
     */
    public long getTotalRepairs() {
        return totalRepairs.get();
    }

    /**
     * 获取连接状态映射
     *
     * @return 连接状态映射
     */
    public Map<Integer, ConnectionStatus> getConnectionStatusMap() {
        return new ConcurrentHashMap<>(connectionStatusMap);
    }

    /**
     * 连接状态类
     * 记录WebSocket连接的状态信息
     */
    public static class ConnectionStatus {
        /**
         * 连接ID
         */
        private final int connectionId;
        
        /**
         * 连接描述
         */
        private final String description;
        
        /**
         * 连续失败次数
         */
        private final AtomicLong failCount = new AtomicLong(0);
        
        /**
         * 最后检查时间
         */
        private final AtomicLong lastCheckTime = new AtomicLong(System.currentTimeMillis());
        
        /**
         * 构造函数
         *
         * @param connectionId 连接ID
         * @param description  连接描述
         */
        public ConnectionStatus(int connectionId, String description) {
            this.connectionId = connectionId;
            this.description = description;
        }
        
        /**
         * 获取连接ID
         *
         * @return 连接ID
         */
        public int getConnectionId() {
            return connectionId;
        }
        
        /**
         * 获取连接描述
         *
         * @return 连接描述
         */
        public String getDescription() {
            return description;
        }
        
        /**
         * 获取连续失败次数
         *
         * @return 连续失败次数
         */
        public long getFailCount() {
            return failCount.get();
        }
        
        /**
         * 增加失败计数
         */
        public void incrementFailCount() {
            failCount.incrementAndGet();
        }
        
        /**
         * 重置失败计数
         */
        public void resetFailCount() {
            failCount.set(0);
        }
        
        /**
         * 获取最后检查时间
         *
         * @return 最后检查时间
         */
        public long getLastCheckTime() {
            return lastCheckTime.get();
        }
        
        /**
         * 设置最后检查时间
         *
         * @param time 时间戳
         */
        public void setLastCheckTime(long time) {
            lastCheckTime.set(time);
        }
    }
}