#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
增强的单元测试框架模块

该模块提供支持依赖注入架构的增强单元测试框架，
包括模拟对象管理、测试容器和测试工具。
"""

import unittest
import asyncio
from typing import Dict, Any, List, Optional, Type, TypeVar, Callable
from unittest.mock import Mock, MagicMock, patch
from contextlib import contextmanager
from dataclasses import dataclass
from loguru import logger

from .dependency_container import DependencyContainer
from .service_interfaces import *
from .component_registry import ComponentRegistry

T = TypeVar('T')


@dataclass
class TestConfiguration:
    """测试配置"""
    use_real_services: List[str] = None
    mock_services: List[str] = None
    test_data_path: Optional[str] = None
    enable_logging: bool = False
    
    def __post_init__(self):
        if self.use_real_services is None:
            self.use_real_services = []
        if self.mock_services is None:
            self.mock_services = []


class TestContainer:
    """
    测试专用的依赖注入容器
    
    提供测试环境下的服务注册和模拟对象管理。
    """
    
    def __init__(self, config: TestConfiguration = None):
        """
        初始化测试容器
        
        Args:
            config: 测试配置
        """
        self.config = config or TestConfiguration()
        self.container = DependencyContainer()
        self.mocks: Dict[Type, Mock] = {}
        self._setup_test_services()
        
        logger.info("测试容器初始化完成")
    
    def _setup_test_services(self) -> None:
        """设置测试服务"""
        # 注册测试专用的配置服务
        test_config = self._create_test_config_service()
        self.container.register_singleton(IConfigService, instance=test_config)
        
        # 注册测试专用的日志服务
        if self.config.enable_logging:
            test_logger = self._create_test_logger_service()
            self.container.register_singleton(ILoggerService, instance=test_logger)
        else:
            self.register_mock(ILoggerService)
        
        # 根据配置注册真实服务或模拟服务
        self._register_configured_services()
    
    def _create_test_config_service(self) -> IConfigService:
        """创建测试配置服务"""
        class TestConfigService:
            def __init__(self):
                self._config = {
                    'kafka': {
                        'bootstrap_servers': 'localhost:9092',
                        'kline_topic': 'test_kline',
                        'depth_topic': 'test_depth',
                        'trade_topic': 'test_trade',
                        'signal_topic': 'test_signal'
                    },
                    'mysql': {
                        'host': 'localhost',
                        'port': '3306',
                        'user': 'test_user',
                        'password': 'test_password',
                        'db': 'test_db'
                    },
                    'influxdb': {
                        'url': 'http://localhost:8086',
                        'token': 'test_token',
                        'org': 'test_org',
                        'bucket': 'test_bucket'
                    }
                }
            
            def get(self, section: str, option: str, fallback: str = None) -> str:
                return self._config.get(section, {}).get(option, fallback)
            
            def get_int(self, section: str, option: str, fallback: int = None) -> int:
                value = self.get(section, option, str(fallback) if fallback is not None else None)
                return int(value) if value is not None else fallback
            
            def get_bool(self, section: str, option: str, fallback: bool = None) -> bool:
                value = self.get(section, option, str(fallback) if fallback is not None else None)
                return value.lower() in ('true', '1', 'yes') if value is not None else fallback
            
            def get_list(self, section: str, option: str, fallback: List = None) -> List:
                value = self.get(section, option)
                return value.split(',') if value else (fallback or [])
        
        return TestConfigService()
    
    def _create_test_logger_service(self) -> ILoggerService:
        """创建测试日志服务"""
        class TestLoggerService:
            def info(self, message: str, **kwargs) -> None:
                logger.info(f"[TEST] {message}", **kwargs)
            
            def error(self, message: str, **kwargs) -> None:
                logger.error(f"[TEST] {message}", **kwargs)
            
            def warning(self, message: str, **kwargs) -> None:
                logger.warning(f"[TEST] {message}", **kwargs)
            
            def debug(self, message: str, **kwargs) -> None:
                logger.debug(f"[TEST] {message}", **kwargs)
        
        return TestLoggerService()
    
    def _register_configured_services(self) -> None:
        """根据配置注册服务"""
        # 默认模拟所有外部服务
        external_services = [
            IKafkaService, IInfluxDBService, IMySQLService,
            IDataLoaderService, IModelTrainerService
        ]
        
        for service_type in external_services:
            service_name = service_type.__name__
            if service_name in self.config.use_real_services:
                # 使用真实服务（需要外部依赖）
                continue
            else:
                # 使用模拟服务
                self.register_mock(service_type)
    
    def register_mock(self, service_type: Type[T], mock_instance: Optional[Mock] = None) -> Mock:
        """
        注册模拟服务
        
        Args:
            service_type: 服务类型
            mock_instance: 自定义模拟实例
            
        Returns:
            模拟对象
        """
        if mock_instance is None:
            mock_instance = Mock(spec=service_type)
        
        self.mocks[service_type] = mock_instance
        self.container.register_singleton(service_type, instance=mock_instance)
        
        logger.debug(f"注册模拟服务: {service_type.__name__}")
        return mock_instance
    
    def get_mock(self, service_type: Type[T]) -> Optional[Mock]:
        """
        获取模拟对象
        
        Args:
            service_type: 服务类型
            
        Returns:
            模拟对象
        """
        return self.mocks.get(service_type)
    
    def resolve(self, service_type: Type[T]) -> T:
        """
        解析服务
        
        Args:
            service_type: 服务类型
            
        Returns:
            服务实例
        """
        return self.container.resolve(service_type)
    
    def reset_mocks(self) -> None:
        """重置所有模拟对象"""
        for mock in self.mocks.values():
            mock.reset_mock()
        
        logger.debug("所有模拟对象已重置")
    
    def cleanup(self) -> None:
        """清理测试容器"""
        self.mocks.clear()
        logger.debug("测试容器已清理")


class EnhancedTestCase(unittest.TestCase):
    """
    增强的测试用例基类
    
    提供依赖注入支持和常用测试工具。
    """
    
    def setUp(self) -> None:
        """设置测试环境"""
        super().setUp()
        self.test_container = TestContainer()
        self.addCleanup(self.test_container.cleanup)
    
    def get_service(self, service_type: Type[T]) -> T:
        """
        获取服务实例
        
        Args:
            service_type: 服务类型
            
        Returns:
            服务实例
        """
        return self.test_container.resolve(service_type)
    
    def get_mock(self, service_type: Type[T]) -> Optional[Mock]:
        """
        获取模拟对象
        
        Args:
            service_type: 服务类型
            
        Returns:
            模拟对象
        """
        return self.test_container.get_mock(service_type)
    
    def register_mock(self, service_type: Type[T], mock_instance: Optional[Mock] = None) -> Mock:
        """
        注册模拟服务
        
        Args:
            service_type: 服务类型
            mock_instance: 自定义模拟实例
            
        Returns:
            模拟对象
        """
        return self.test_container.register_mock(service_type, mock_instance)
    
    def reset_mocks(self) -> None:
        """重置所有模拟对象"""
        self.test_container.reset_mocks()
    
    @contextmanager
    def assert_logs(self, level: str = 'INFO'):
        """断言日志记录"""
        with self.assertLogs(level=level) as log_context:
            yield log_context
    
    def assert_mock_called_with_timeout(self, mock_obj: Mock, timeout: float = 1.0):
        """断言模拟对象在超时时间内被调用"""
        import time
        start_time = time.time()
        while time.time() - start_time < timeout:
            if mock_obj.called:
                return
            time.sleep(0.01)
        
        self.fail(f"Mock was not called within {timeout} seconds")
    
    def create_test_market_data(self, symbol: str = "BTCUSDT") -> Dict[str, Any]:
        """创建测试市场数据"""
        return {
            "symbol": symbol,
            "timestamp": int(time.time() * 1000),
            "open": 50000.0,
            "high": 50100.0,
            "low": 49900.0,
            "close": 50050.0,
            "volume": 1000.0
        }
    
    def create_test_signal(self, signal_type: str = "BUY") -> Dict[str, Any]:
        """创建测试交易信号"""
        return {
            "messageId": "test-signal-001",
            "messageType": "signal",
            "timestamp": int(time.time() * 1000),
            "data": {
                "strategyId": "test-strategy",
                "symbol": "BTCUSDT",
                "signalType": signal_type,
                "signalStrength": 0.8,
                "timeFrame": "1h",
                "riskAssessment": {
                    "overallRisk": 0.3
                }
            }
        }


class AsyncTestCase(EnhancedTestCase):
    """
    异步测试用例基类
    
    支持异步测试方法。
    """
    
    def setUp(self) -> None:
        """设置异步测试环境"""
        super().setUp()
        self.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop)
        self.addCleanup(self.loop.close)
    
    def run_async(self, coro):
        """运行异步协程"""
        return self.loop.run_until_complete(coro)


class TestDataFactory:
    """
    测试数据工厂
    
    提供各种测试数据的创建方法。
    """
    
    @staticmethod
    def create_kline_data(symbol: str = "BTCUSDT", count: int = 100) -> List[Dict[str, Any]]:
        """创建K线测试数据"""
        import time
        
        data = []
        base_time = int(time.time() * 1000)
        base_price = 50000.0
        
        for i in range(count):
            price_change = (i % 10 - 5) * 10  # 简单的价格变化
            data.append({
                "symbol": symbol,
                "interval": "1m",
                "openTime": base_time + i * 60000,
                "closeTime": base_time + (i + 1) * 60000,
                "open": base_price + price_change,
                "high": base_price + price_change + 50,
                "low": base_price + price_change - 50,
                "close": base_price + price_change + (i % 3 - 1) * 20,
                "volume": 1000 + i * 10
            })
        
        return data
    
    @staticmethod
    def create_depth_data(symbol: str = "BTCUSDT") -> Dict[str, Any]:
        """创建深度测试数据"""
        return {
            "symbol": symbol,
            "timestamp": int(time.time() * 1000),
            "bids": [
                [49990.0, 10.0],
                [49980.0, 15.0],
                [49970.0, 20.0]
            ],
            "asks": [
                [50010.0, 8.0],
                [50020.0, 12.0],
                [50030.0, 18.0]
            ]
        }
    
    @staticmethod
    def create_trade_data(symbol: str = "BTCUSDT", count: int = 50) -> List[Dict[str, Any]]:
        """创建交易测试数据"""
        import time
        import random
        
        data = []
        base_time = int(time.time() * 1000)
        base_price = 50000.0
        
        for i in range(count):
            data.append({
                "symbol": symbol,
                "timestamp": base_time + i * 1000,
                "price": base_price + random.uniform(-100, 100),
                "quantity": random.uniform(0.1, 10.0),
                "side": random.choice(["BUY", "SELL"])
            })
        
        return data


class MockServiceBuilder:
    """
    模拟服务构建器
    
    提供预配置的模拟服务创建方法。
    """
    
    @staticmethod
    def create_kafka_mock() -> Mock:
        """创建Kafka服务模拟"""
        kafka_mock = Mock(spec=IKafkaService)
        kafka_mock.start_consuming = Mock()
        kafka_mock.stop_consuming = Mock()
        kafka_mock.publish_signal = Mock()
        kafka_mock.clean_up = Mock()
        return kafka_mock
    
    @staticmethod
    def create_prediction_engine_mock() -> Mock:
        """创建预测引擎模拟"""
        prediction_mock = Mock(spec=IPredictionEngineService)
        prediction_mock.predict.return_value = {
            "signal_type": "BUY",
            "confidence": 0.85,
            "features": {"trend": "bullish"}
        }
        prediction_mock.get_prediction_confidence.return_value = 0.85
        return prediction_mock
    
    @staticmethod
    def create_data_processor_mock() -> Mock:
        """创建数据处理器模拟"""
        processor_mock = Mock(spec=IDataProcessorService)
        processor_mock.process_kline_data.return_value = {"processed": True}
        processor_mock.process_depth_data.return_value = {"processed": True}
        processor_mock.process_trade_data.return_value = {"processed": True}
        return processor_mock
    
    @staticmethod
    def create_strategy_mock() -> Mock:
        """创建策略服务模拟"""
        strategy_mock = Mock(spec=IStrategyService)
        strategy_mock.on_market_data.return_value = {
            "signal_type": "BUY",
            "symbol": "BTCUSDT",
            "strength": 0.8
        }
        strategy_mock.get_strategy_info.return_value = {
            "name": "test_strategy",
            "version": "1.0.0"
        }
        return strategy_mock


# 测试装饰器
def with_test_container(config: TestConfiguration = None):
    """测试容器装饰器"""
    def decorator(test_func):
        def wrapper(self, *args, **kwargs):
            if not hasattr(self, 'test_container'):
                self.test_container = TestContainer(config)
                self.addCleanup(self.test_container.cleanup)
            return test_func(self, *args, **kwargs)
        return wrapper
    return decorator


def mock_service(service_type: Type[T], mock_instance: Optional[Mock] = None):
    """模拟服务装饰器"""
    def decorator(test_func):
        def wrapper(self, *args, **kwargs):
            if hasattr(self, 'test_container'):
                self.test_container.register_mock(service_type, mock_instance)
            return test_func(self, *args, **kwargs)
        return wrapper
    return decorator


# 示例测试用例
class ExampleTestCase(EnhancedTestCase):
    """示例测试用例"""
    
    def test_config_service(self):
        """测试配置服务"""
        config = self.get_service(IConfigService)
        self.assertIsNotNone(config)
        
        # 测试配置获取
        kafka_servers = config.get('kafka', 'bootstrap_servers')
        self.assertEqual(kafka_servers, 'localhost:9092')
    
    @mock_service(IKafkaService)
    def test_kafka_service_mock(self):
        """测试Kafka服务模拟"""
        kafka_mock = self.get_mock(IKafkaService)
        self.assertIsNotNone(kafka_mock)
        
        # 测试模拟调用
        kafka_mock.publish_signal({"test": "signal"})
        kafka_mock.publish_signal.assert_called_once_with({"test": "signal"})
    
    def test_market_data_creation(self):
        """测试市场数据创建"""
        market_data = self.create_test_market_data("ETHUSDT")
        self.assertEqual(market_data["symbol"], "ETHUSDT")
        self.assertIn("timestamp", market_data)
        self.assertIn("open", market_data)


class ExampleAsyncTestCase(AsyncTestCase):
    """示例异步测试用例"""
    
    def test_async_operation(self):
        """测试异步操作"""
        async def async_operation():
            await asyncio.sleep(0.01)
            return "success"
        
        result = self.run_async(async_operation())
        self.assertEqual(result, "success")


if __name__ == "__main__":
    # 运行示例测试
    unittest.main(verbosity=2)