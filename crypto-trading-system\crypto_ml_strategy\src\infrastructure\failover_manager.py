"""
故障转移管理模块

提供crypto_ml_strategy项目的故障转移功能，包括故障检测、
自动切换、备份服务管理和故障恢复。

Author: Crypto ML Strategy Team
Date: 2024-12-20
Version: 1.0.0
"""

import asyncio
import time
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Dict, List, Optional, Callable, Set
from loguru import logger

from .error_handler_core import ErrorSeverity, ErrorCategory, ErrorCode, ErrorContext


class FailoverTrigger(Enum):
    """故障转移触发器枚举"""
    HEALTH_CHECK_FAILED = "health_check_failed"
    RESPONSE_TIMEOUT = "response_timeout"
    ERROR_RATE_EXCEEDED = "error_rate_exceeded"
    MANUAL_TRIGGER = "manual_trigger"
    CIRCUIT_BREAKER_OPEN = "circuit_breaker_open"
    RESOURCE_EXHAUSTED = "resource_exhausted"


class FailoverStatus(Enum):
    """故障转移状态枚举"""
    ACTIVE = "active"      # 主服务活跃
    FAILED_OVER = "failed_over"  # 已故障转移
    RECOVERING = "recovering"    # 恢复中
    MAINTENANCE = "maintenance"  # 维护模式


@dataclass
class ServiceEndpoint:
    """服务端点"""
    name: str
    url: str
    priority: int = 100  # 优先级，数字越小优先级越高
    weight: int = 100    # 权重，用于负载均衡
    enabled: bool = True
    health_check_url: Optional[str] = None
    timeout: float = 30.0
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __hash__(self):
        return hash(self.name)


@dataclass
class FailoverEvent:
    """故障转移事件"""
    service_name: str
    from_endpoint: str
    to_endpoint: str
    trigger: FailoverTrigger
    reason: str
    timestamp: datetime = field(default_factory=datetime.now)
    success: bool = True
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "service_name": self.service_name,
            "from_endpoint": self.from_endpoint,
            "to_endpoint": self.to_endpoint,
            "trigger": self.trigger.value,
            "reason": self.reason,
            "timestamp": self.timestamp.isoformat(),
            "success": self.success,
            "metadata": self.metadata
        }


class HealthChecker(ABC):
    """健康检查器抽象基类"""
    
    @abstractmethod
    async def check_health(self, endpoint: ServiceEndpoint) -> bool:
        """检查端点健康状态"""
        pass


class HttpHealthChecker(HealthChecker):
    """HTTP健康检查器"""
    
    def __init__(self, timeout: float = 10.0):
        self.timeout = timeout
    
    async def check_health(self, endpoint: ServiceEndpoint) -> bool:
        """检查HTTP端点健康状态"""
        try:
            import aiohttp
            
            health_url = endpoint.health_check_url or f"{endpoint.url}/health"
            
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
                async with session.get(health_url) as response:
                    return response.status == 200
                    
        except Exception as e:
            logger.warning(f"健康检查失败 {endpoint.name}: {e}")
            return False


class PingHealthChecker(HealthChecker):
    """Ping健康检查器"""
    
    def __init__(self, timeout: float = 5.0):
        self.timeout = timeout
    
    async def check_health(self, endpoint: ServiceEndpoint) -> bool:
        """检查网络连通性"""
        try:
            import subprocess
            import platform
            
            # 提取主机名
            host = endpoint.url.split("://")[-1].split("/")[0].split(":")[0]
            
            # 根据操作系统选择ping命令
            if platform.system().lower() == "windows":
                cmd = ["ping", "-n", "1", "-w", str(int(self.timeout * 1000)), host]
            else:
                cmd = ["ping", "-c", "1", "-W", str(int(self.timeout)), host]
            
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            await asyncio.wait_for(process.wait(), timeout=self.timeout)
            return process.returncode == 0
            
        except Exception as e:
            logger.warning(f"Ping检查失败 {endpoint.name}: {e}")
            return False


class FailoverStrategy(ABC):
    """故障转移策略抽象基类"""
    
    @abstractmethod
    async def select_backup(
        self, 
        failed_endpoint: ServiceEndpoint,
        available_endpoints: List[ServiceEndpoint]
    ) -> Optional[ServiceEndpoint]:
        """选择备份端点"""
        pass
    
    @abstractmethod
    def get_strategy_name(self) -> str:
        """获取策略名称"""
        pass


class PriorityFailoverStrategy(FailoverStrategy):
    """优先级故障转移策略"""
    
    async def select_backup(
        self, 
        failed_endpoint: ServiceEndpoint,
        available_endpoints: List[ServiceEndpoint]
    ) -> Optional[ServiceEndpoint]:
        """选择优先级最高的可用端点"""
        if not available_endpoints:
            return None
        
        # 按优先级排序（数字越小优先级越高）
        sorted_endpoints = sorted(available_endpoints, key=lambda ep: ep.priority)
        return sorted_endpoints[0]
    
    def get_strategy_name(self) -> str:
        return "priority_failover"


class RoundRobinFailoverStrategy(FailoverStrategy):
    """轮询故障转移策略"""
    
    def __init__(self):
        self.last_selected: Dict[str, int] = {}
    
    async def select_backup(
        self, 
        failed_endpoint: ServiceEndpoint,
        available_endpoints: List[ServiceEndpoint]
    ) -> Optional[ServiceEndpoint]:
        """轮询选择端点"""
        if not available_endpoints:
            return None
        
        service_key = f"service_{id(available_endpoints)}"
        last_index = self.last_selected.get(service_key, -1)
        next_index = (last_index + 1) % len(available_endpoints)
        
        self.last_selected[service_key] = next_index
        return available_endpoints[next_index]
    
    def get_strategy_name(self) -> str:
        return "round_robin_failover"


class WeightedFailoverStrategy(FailoverStrategy):
    """加权故障转移策略"""
    
    async def select_backup(
        self, 
        failed_endpoint: ServiceEndpoint,
        available_endpoints: List[ServiceEndpoint]
    ) -> Optional[ServiceEndpoint]:
        """基于权重选择端点"""
        if not available_endpoints:
            return None
        
        # 计算总权重
        total_weight = sum(ep.weight for ep in available_endpoints)
        if total_weight == 0:
            return available_endpoints[0]
        
        # 随机选择
        import random
        random_value = random.randint(1, total_weight)
        
        current_weight = 0
        for endpoint in available_endpoints:
            current_weight += endpoint.weight
            if random_value <= current_weight:
                return endpoint
        
        return available_endpoints[-1]
    
    def get_strategy_name(self) -> str:
        return "weighted_failover"


class ServiceCluster:
    """服务集群"""
    
    def __init__(
        self,
        name: str,
        health_checker: Optional[HealthChecker] = None,
        failover_strategy: Optional[FailoverStrategy] = None
    ):
        self.name = name
        self.endpoints: List[ServiceEndpoint] = []
        self.current_endpoint: Optional[ServiceEndpoint] = None
        self.health_checker = health_checker or HttpHealthChecker()
        self.failover_strategy = failover_strategy or PriorityFailoverStrategy()
        self.health_status: Dict[str, bool] = {}
        self.last_health_check: Dict[str, datetime] = {}
        self.health_check_interval = 30  # 秒
        self.failover_cooldown = 60  # 故障转移冷却时间（秒）
        self.last_failover_time: Optional[datetime] = None
    
    def add_endpoint(self, endpoint: ServiceEndpoint) -> None:
        """添加端点"""
        self.endpoints.append(endpoint)
        # 按优先级排序
        self.endpoints.sort(key=lambda ep: ep.priority)
        
        # 如果没有当前端点，选择第一个
        if not self.current_endpoint and endpoint.enabled:
            self.current_endpoint = endpoint
        
        logger.info(f"添加端点到集群 {self.name}: {endpoint.name}")
    
    def remove_endpoint(self, endpoint_name: str) -> bool:
        """移除端点"""
        for i, endpoint in enumerate(self.endpoints):
            if endpoint.name == endpoint_name:
                removed_endpoint = self.endpoints.pop(i)
                
                # 如果移除的是当前端点，需要故障转移
                if self.current_endpoint and self.current_endpoint.name == endpoint_name:
                    self.current_endpoint = None
                    asyncio.create_task(self._select_new_endpoint())
                
                logger.info(f"从集群 {self.name} 移除端点: {endpoint_name}")
                return True
        
        return False
    
    async def check_endpoint_health(self, endpoint: ServiceEndpoint) -> bool:
        """检查端点健康状态"""
        try:
            is_healthy = await self.health_checker.check_health(endpoint)
            self.health_status[endpoint.name] = is_healthy
            self.last_health_check[endpoint.name] = datetime.now()
            return is_healthy
        except Exception as e:
            logger.error(f"健康检查异常 {endpoint.name}: {e}")
            self.health_status[endpoint.name] = False
            return False
    
    async def _select_new_endpoint(self) -> Optional[ServiceEndpoint]:
        """选择新的端点"""
        available_endpoints = []
        
        for endpoint in self.endpoints:
            if not endpoint.enabled:
                continue
            
            # 检查健康状态
            is_healthy = await self.check_endpoint_health(endpoint)
            if is_healthy:
                available_endpoints.append(endpoint)
        
        if not available_endpoints:
            logger.error(f"集群 {self.name} 没有可用的健康端点")
            return None
        
        # 使用故障转移策略选择端点
        selected = await self.failover_strategy.select_backup(
            self.current_endpoint, available_endpoints
        )
        
        if selected:
            self.current_endpoint = selected
            logger.info(f"集群 {self.name} 选择新端点: {selected.name}")
        
        return selected
    
    async def trigger_failover(
        self, 
        trigger: FailoverTrigger, 
        reason: str = ""
    ) -> bool:
        """触发故障转移"""
        # 检查冷却时间
        if (self.last_failover_time and 
            datetime.now() - self.last_failover_time < timedelta(seconds=self.failover_cooldown)):
            logger.warning(f"集群 {self.name} 故障转移冷却中，跳过")
            return False
        
        old_endpoint = self.current_endpoint
        new_endpoint = await self._select_new_endpoint()
        
        if new_endpoint and new_endpoint != old_endpoint:
            self.last_failover_time = datetime.now()
            
            logger.info(
                f"集群 {self.name} 故障转移: {old_endpoint.name if old_endpoint else 'None'} -> {new_endpoint.name}"
            )
            return True
        
        return False
    
    def get_current_endpoint(self) -> Optional[ServiceEndpoint]:
        """获取当前端点"""
        return self.current_endpoint
    
    def get_cluster_status(self) -> Dict[str, Any]:
        """获取集群状态"""
        healthy_endpoints = sum(1 for status in self.health_status.values() if status)
        
        return {
            "name": self.name,
            "total_endpoints": len(self.endpoints),
            "healthy_endpoints": healthy_endpoints,
            "current_endpoint": self.current_endpoint.name if self.current_endpoint else None,
            "health_status": self.health_status.copy(),
            "last_failover": self.last_failover_time.isoformat() if self.last_failover_time else None
        }


class FailoverManager:
    """故障转移管理器"""
    
    def __init__(self):
        self.clusters: Dict[str, ServiceCluster] = {}
        self.failover_history: List[FailoverEvent] = []
        self.max_history = 1000
        self.monitoring_enabled = False
        self.monitor_task: Optional[asyncio.Task] = None
        self.failover_callbacks: List[Callable[[FailoverEvent], None]] = []
    
    def register_cluster(self, cluster: ServiceCluster) -> None:
        """注册服务集群"""
        self.clusters[cluster.name] = cluster
        logger.info(f"注册服务集群: {cluster.name}")
    
    def get_cluster(self, cluster_name: str) -> Optional[ServiceCluster]:
        """获取服务集群"""
        return self.clusters.get(cluster_name)
    
    def add_failover_callback(self, callback: Callable[[FailoverEvent], None]) -> None:
        """添加故障转移回调"""
        self.failover_callbacks.append(callback)
    
    async def trigger_failover(
        self,
        cluster_name: str,
        trigger: FailoverTrigger,
        reason: str = ""
    ) -> bool:
        """触发指定集群的故障转移"""
        cluster = self.clusters.get(cluster_name)
        if not cluster:
            logger.error(f"集群 {cluster_name} 不存在")
            return False
        
        old_endpoint = cluster.get_current_endpoint()
        success = await cluster.trigger_failover(trigger, reason)
        
        if success:
            new_endpoint = cluster.get_current_endpoint()
            
            # 记录故障转移事件
            event = FailoverEvent(
                service_name=cluster_name,
                from_endpoint=old_endpoint.name if old_endpoint else "None",
                to_endpoint=new_endpoint.name if new_endpoint else "None",
                trigger=trigger,
                reason=reason,
                success=success
            )
            
            self.failover_history.append(event)
            if len(self.failover_history) > self.max_history:
                self.failover_history.pop(0)
            
            # 触发回调
            for callback in self.failover_callbacks:
                try:
                    callback(event)
                except Exception as e:
                    logger.error(f"故障转移回调执行失败: {e}")
        
        return success
    
    async def start_monitoring(self, interval: int = 30) -> None:
        """开始故障转移监控"""
        if self.monitoring_enabled:
            return
        
        self.monitoring_enabled = True
        self.monitor_task = asyncio.create_task(self._monitor_loop(interval))
        logger.info("故障转移监控已启动")
    
    async def stop_monitoring(self) -> None:
        """停止故障转移监控"""
        self.monitoring_enabled = False
        if self.monitor_task:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass
        logger.info("故障转移监控已停止")
    
    async def _monitor_loop(self, interval: int) -> None:
        """监控循环"""
        while self.monitoring_enabled:
            try:
                for cluster_name, cluster in self.clusters.items():
                    current_endpoint = cluster.get_current_endpoint()
                    
                    if current_endpoint:
                        # 检查当前端点健康状态
                        is_healthy = await cluster.check_endpoint_health(current_endpoint)
                        
                        if not is_healthy:
                            logger.warning(f"检测到端点 {current_endpoint.name} 不健康")
                            await self.trigger_failover(
                                cluster_name,
                                FailoverTrigger.HEALTH_CHECK_FAILED,
                                f"端点 {current_endpoint.name} 健康检查失败"
                            )
                
                await asyncio.sleep(interval)
                
            except Exception as e:
                logger.error(f"故障转移监控循环异常: {e}")
                await asyncio.sleep(5)
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统故障转移状态"""
        cluster_statuses = {
            name: cluster.get_cluster_status()
            for name, cluster in self.clusters.items()
        }
        
        total_clusters = len(cluster_statuses)
        healthy_clusters = len([
            status for status in cluster_statuses.values()
            if status["healthy_endpoints"] > 0
        ])
        
        return {
            "total_clusters": total_clusters,
            "healthy_clusters": healthy_clusters,
            "monitoring_enabled": self.monitoring_enabled,
            "cluster_statuses": cluster_statuses,
            "recent_failovers": [
                event.to_dict() for event in self.failover_history[-10:]
            ]
        }
    
    def get_failover_statistics(self) -> Dict[str, Any]:
        """获取故障转移统计"""
        if not self.failover_history:
            return {}
        
        total_failovers = len(self.failover_history)
        successful_failovers = len([
            event for event in self.failover_history if event.success
        ])
        
        # 按触发器分组统计
        trigger_stats = {}
        for event in self.failover_history:
            trigger = event.trigger.value
            if trigger not in trigger_stats:
                trigger_stats[trigger] = {"total": 0, "success": 0}
            
            trigger_stats[trigger]["total"] += 1
            if event.success:
                trigger_stats[trigger]["success"] += 1
        
        return {
            "total_failovers": total_failovers,
            "successful_failovers": successful_failovers,
            "success_rate": successful_failovers / total_failovers if total_failovers > 0 else 0,
            "trigger_statistics": trigger_stats,
            "recent_events": [
                event.to_dict() for event in self.failover_history[-20:]
            ]
        }


# 全局故障转移管理器
global_failover_manager = FailoverManager()