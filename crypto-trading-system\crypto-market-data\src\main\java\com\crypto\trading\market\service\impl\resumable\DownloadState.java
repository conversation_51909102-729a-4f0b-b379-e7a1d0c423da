package com.crypto.trading.market.service.impl.resumable;

import com.crypto.trading.common.dto.market.KlineDataDTO;
import com.crypto.trading.common.enums.KlineInterval;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 下载状态类
 * 用于记录和管理下载任务的状态，支持断点续传
 */
public class DownloadState implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    // 任务基本信息
    private final String taskId;
    private final String symbol;
    private final KlineInterval interval;
    private final LocalDateTime startTime;
    private final LocalDateTime endTime;
    
    // 任务状态
    private boolean completed;
    private final Map<Integer, Boolean> chunkStatuses;  // 分块索引 -> 是否完成
    private final Map<Integer, List<KlineDataDTO>> chunkData;  // 分块索引 -> 下载的数据
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
    
    /**
     * 构造函数
     *
     * @param taskId    任务ID
     * @param symbol    交易对
     * @param interval  K线间隔
     * @param startTime 开始时间
     * @param endTime   结束时间
     */
    public DownloadState(String taskId, String symbol, KlineInterval interval, 
                       LocalDateTime startTime, LocalDateTime endTime) {
        this.taskId = taskId;
        this.symbol = symbol;
        this.interval = interval;
        this.startTime = startTime;
        this.endTime = endTime;
        this.completed = false;
        this.chunkStatuses = new ConcurrentHashMap<>();
        this.chunkData = new ConcurrentHashMap<>();
        this.createTime = LocalDateTime.now();
        this.updateTime = this.createTime;
    }    
    /**
     * 获取任务ID
     *
     * @return 任务ID
     */
    public String getTaskId() {
        return taskId;
    }
    
    /**
     * 获取交易对
     *
     * @return 交易对
     */
    public String getSymbol() {
        return symbol;
    }
    
    /**
     * 获取K线间隔
     *
     * @return K线间隔
     */
    public KlineInterval getInterval() {
        return interval;
    }
    
    /**
     * 获取开始时间
     *
     * @return 开始时间
     */
    public LocalDateTime getStartTime() {
        return startTime;
    }
    
    /**
     * 获取结束时间
     *
     * @return 结束时间
     */
    public LocalDateTime getEndTime() {
        return endTime;
    }
    
    /**
     * 任务是否完成
     *
     * @return true表示任务已完成，false表示任务未完成
     */
    public boolean isCompleted() {
        return completed;
    }
    
    /**
     * 设置任务完成状态
     *
     * @param completed 任务是否完成
     */
    public void setCompleted(boolean completed) {
        this.completed = completed;
        this.updateTime = LocalDateTime.now();
    }    
    /**
     * 获取分块状态
     *
     * @return 分块状态映射（分块索引 -> 是否完成）
     */
    public Map<Integer, Boolean> getChunkStatuses() {
        return Collections.unmodifiableMap(chunkStatuses);
    }
    
    /**
     * 设置分块状态
     *
     * @param chunkIndex 分块索引
     * @param completed  是否完成
     */
    public void setChunkStatus(int chunkIndex, boolean completed) {
        chunkStatuses.put(chunkIndex, completed);
        this.updateTime = LocalDateTime.now();
    }
    
    /**
     * 分块是否完成
     *
     * @param chunkIndex 分块索引
     * @return true表示分块已完成，false表示分块未完成
     */
    public boolean isChunkCompleted(int chunkIndex) {
        return Boolean.TRUE.equals(chunkStatuses.get(chunkIndex));
    }
    
    /**
     * 设置分块数据
     *
     * @param chunkIndex 分块索引
     * @param data       分块数据
     */
    public void setChunkData(int chunkIndex, List<KlineDataDTO> data) {
        if (data != null) {
            chunkData.put(chunkIndex, new ArrayList<>(data));
        }
    }
    
    /**
     * 获取分块数据
     *
     * @param chunkIndex 分块索引
     * @return 分块数据
     */
    public List<KlineDataDTO> getChunkData(int chunkIndex) {
        return chunkData.getOrDefault(chunkIndex, Collections.emptyList());
    }    
    /**
     * 所有分块是否都已完成
     *
     * @return true表示所有分块都已完成，false表示还有未完成的分块
     */
    public boolean allChunksCompleted() {
        // 如果没有分块，返回false
        if (chunkStatuses.isEmpty()) {
            return false;
        }
        
        // 检查是否所有分块都已完成
        for (Boolean completed : chunkStatuses.values()) {
            if (!completed) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 获取所有数据并按时间排序
     *
     * @return 排序后的所有K线数据
     */
    public List<KlineDataDTO> getAllDataSorted() {
        List<KlineDataDTO> allData = new ArrayList<>();
        
        // 收集所有分块的数据
        for (List<KlineDataDTO> dataList : chunkData.values()) {
            allData.addAll(dataList);
        }
        
        // 按开盘时间排序
        allData.sort(Comparator.comparing(KlineDataDTO::getOpenTime));
        
        return allData;
    }
    
    /**
     * 获取创建时间
     *
     * @return 创建时间
     */
    public LocalDateTime getCreateTime() {
        return createTime;
    }
    
    /**
     * 获取更新时间
     *
     * @return 更新时间
     */
    public LocalDateTime getUpdateTime() {
        return updateTime;
    }
}