package com.crypto.trading.bootstrap.initializer;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

/**
 * 公共模块初始化器
 * 负责初始化系统的公共组件和配置
 */
@Component
public class CommonModuleInitializer extends AbstractModuleInitializer {
    
    private static final Logger log = LoggerFactory.getLogger(CommonModuleInitializer.class);
    
    /**
     * 模块名称
     */
    private static final String MODULE_NAME = "common";
    
    /**
     * 模块优先级，最高优先级
     */
    private static final int PRIORITY = 1;
    
    private final Environment environment;
    
    /**
     * 构造函数
     * 
     * @param environment Spring环境配置
     */
    public CommonModuleInitializer(Environment environment) {
        this.environment = environment;
    }
    
    @Override
    public String getName() {
        return MODULE_NAME;
    }
    
    @Override
    public int getPriority() {
        return PRIORITY;
    }
    
    @Override
    protected void doInitialize() throws Exception {
        log.info("初始化公共模块...");
        
        // 加载系统配置
        loadConfigurations();
        
        // 初始化工具类
        initializeUtils();
        
        log.info("公共模块初始化完成");
    }
    
    /**
     * 加载系统配置
     */
    private void loadConfigurations() {
        log.info("加载系统配置...");
        // 读取配置文件中的配置项
        String activeProfiles = String.join(", ", environment.getActiveProfiles());
        log.info("当前激活的配置文件: {}", activeProfiles.isEmpty() ? "default" : activeProfiles);
        
        // 这里可以添加更多配置加载逻辑
    }
    
    /**
     * 初始化工具类
     */
    private void initializeUtils() {
        log.info("初始化工具类...");
        // 初始化各种工具类
        // 例如：日期工具、加密工具、字符串工具等
        
        // 这里可以添加更多工具类初始化逻辑
    }
    
    @Override
    protected void doShutdown() {
        log.info("关闭公共模块...");
        // 执行公共模块的关闭操作
        // 例如：释放资源、关闭连接等
        
        log.info("公共模块已关闭");
    }
}