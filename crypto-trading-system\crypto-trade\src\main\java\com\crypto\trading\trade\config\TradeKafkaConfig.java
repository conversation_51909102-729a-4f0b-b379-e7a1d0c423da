package com.crypto.trading.trade.config;

import org.apache.kafka.clients.admin.AdminClientConfig;
import org.apache.kafka.clients.admin.NewTopic;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.core.*;
import org.springframework.kafka.support.serializer.JsonDeserializer;
import org.springframework.kafka.support.serializer.JsonSerializer;

import java.util.HashMap;
import java.util.Map;

/**
 * 交易模块Kafka配置类
 * <p>
 * 配置Kafka生产者和消费者，专用于交易模块
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Configuration
public class TradeKafkaConfig {

    /**
     * Kafka服务器地址
     */
    @Value("${spring.kafka.bootstrap-servers:localhost:9092}")
    private String bootstrapServers;

    /**
     * 订单执行结果主题
     */
    @Value("${kafka.topic.order.execution.result:order.execution.result}")
    private String orderExecutionResultTopic;

    /**
     * 订单状态更新主题
     */
    @Value("${kafka.topic.order.status.update:order.status.update}")
    private String orderStatusUpdateTopic;

    /**
     * 订单执行错误主题
     */
    @Value("${kafka.topic.order.execution.error:order.execution.error}")
    private String orderExecutionErrorTopic;

    /**
     * 主题分区数
     */
    @Value("${kafka.topic.partitions:3}")
    private int partitions;

    /**
     * 主题副本因子
     */
    @Value("${kafka.topic.replication-factor:1}")
    private short replicationFactor;

    /**
     * 配置Kafka管理客户端
     *
     * @return KafkaAdmin
     */
    @Bean
    public KafkaAdmin kafkaAdmin() {
        Map<String, Object> configs = new HashMap<>();
        configs.put(AdminClientConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
        return new KafkaAdmin(configs);
    }

    /**
     * 创建订单执行结果主题
     *
     * @return NewTopic
     */
    @Bean
    public NewTopic orderExecutionResultTopic() {
        return new NewTopic(orderExecutionResultTopic, partitions, replicationFactor);
    }

    /**
     * 创建订单状态更新主题
     *
     * @return NewTopic
     */
    @Bean
    public NewTopic orderStatusUpdateTopic() {
        return new NewTopic(orderStatusUpdateTopic, partitions, replicationFactor);
    }

    /**
     * 创建订单执行错误主题
     *
     * @return NewTopic
     */
    @Bean
    public NewTopic orderExecutionErrorTopic() {
        return new NewTopic(orderExecutionErrorTopic, partitions, replicationFactor);
    }

    /**
     * 配置生产者工厂
     *
     * @return ProducerFactory
     */
    @Bean
    public ProducerFactory<String, String> producerFactory() {
        Map<String, Object> configProps = new HashMap<>();
        configProps.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
        configProps.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        configProps.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        configProps.put(ProducerConfig.ACKS_CONFIG, "all");
        configProps.put(ProducerConfig.RETRIES_CONFIG, 3);
        configProps.put(ProducerConfig.BATCH_SIZE_CONFIG, 16384);
        configProps.put(ProducerConfig.LINGER_MS_CONFIG, 1);
        configProps.put(ProducerConfig.BUFFER_MEMORY_CONFIG, 33554432);
        return new DefaultKafkaProducerFactory<>(configProps);
    }

    /**
     * 配置Kafka模板
     *
     * @return KafkaTemplate
     */
    @Bean
    public KafkaTemplate<String, String> kafkaTemplate() {
        return new KafkaTemplate<>(producerFactory());
    }

    /**
     * 配置消费者工厂
     *
     * @return ConsumerFactory
     */
    @Bean
    public ConsumerFactory<String, String> consumerFactory() {
        Map<String, Object> props = new HashMap<>();
        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
        props.put(ConsumerConfig.GROUP_ID_CONFIG, "crypto-trade");
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        props.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "earliest");
        props.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, false);
        props.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, 100);
        return new DefaultKafkaConsumerFactory<>(props);
    }

    /**
     * 配置Kafka监听器容器工厂
     *
     * @return ConcurrentKafkaListenerContainerFactory
     */
    @Bean
    public ConcurrentKafkaListenerContainerFactory<String, String> kafkaListenerContainerFactory() {
        ConcurrentKafkaListenerContainerFactory<String, String> factory = new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConsumerFactory(consumerFactory());
        factory.setConcurrency(3);
        factory.getContainerProperties().setPollTimeout(3000);
        return factory;
    }
}