#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
风险管理测试辅助工具

提供风险管理集成测试所需的辅助功能，包括测试数据生成、
风险场景模拟、性能监控和结果验证等工具。

Author: Crypto ML Strategy Team
Date: 2024-12-19
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
import logging
import time
import threading
from dataclasses import dataclass, field
import json

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..', 'src'))

from risk_management import (
    RiskConfig, RiskEvent, RiskLevel, PositionInfo,
    RiskControlEngine, PositionSizeMethod, StopLossType, TakeProfitType
)

logger = logging.getLogger(__name__)


@dataclass
class RiskTestScenario:
    """风险测试场景数据类"""
    scenario_id: str
    name: str
    description: str
    initial_portfolio_value: float
    price_changes: Dict[str, List[float]]
    expected_events: List[str]
    expected_actions: List[str]
    max_drawdown_expected: float
    duration_seconds: int = 10
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class RiskTestResult:
    """风险测试结果数据类"""
    scenario_id: str
    success: bool
    execution_time: float
    events_generated: List[RiskEvent]
    actions_executed: List[Dict[str, Any]]
    final_portfolio_value: float
    max_drawdown_observed: float
    performance_metrics: Dict[str, float]
    error_message: Optional[str] = None


class RiskScenarioGenerator:
    """风险场景生成器"""
    
    def __init__(self):
        """初始化风险场景生成器"""
        self.scenarios = []
        logger.info("Risk scenario generator initialized")
    
    def generate_market_crash_scenario(self) -> RiskTestScenario:
        """生成市场崩盘场景"""
        return RiskTestScenario(
            scenario_id="market_crash_001",
            name="Market Crash Scenario",
            description="Simulate sudden market crash with 30% price drop",
            initial_portfolio_value=100000,
            price_changes={
                'BTCUSDT': [50000, 45000, 40000, 35000, 37000, 39000],
                'ETHUSDT': [3000, 2700, 2400, 2100, 2250, 2400]
            },
            expected_events=["stop_loss_triggered", "max_drawdown_exceeded"],
            expected_actions=["emergency_liquidation", "reduce_positions"],
            max_drawdown_expected=0.25,
            duration_seconds=15
        )
    
    def generate_volatility_spike_scenario(self) -> RiskTestScenario:
        """生成波动率激增场景"""
        return RiskTestScenario(
            scenario_id="volatility_spike_001",
            name="Volatility Spike Scenario",
            description="Simulate high volatility with rapid price swings",
            initial_portfolio_value=100000,
            price_changes={
                'BTCUSDT': [50000, 52000, 48000, 54000, 46000, 51000, 49000],
                'ETHUSDT': [3000, 3150, 2850, 3300, 2700, 3100, 2950]
            },
            expected_events=["high_volatility_detected", "position_adjustment_needed"],
            expected_actions=["tighten_stops", "conservative_mode"],
            max_drawdown_expected=0.12,
            duration_seconds=20
        )
    
    def generate_gradual_decline_scenario(self) -> RiskTestScenario:
        """生成缓慢下跌场景"""
        return RiskTestScenario(
            scenario_id="gradual_decline_001",
            name="Gradual Decline Scenario",
            description="Simulate gradual market decline over time",
            initial_portfolio_value=100000,
            price_changes={
                'BTCUSDT': [50000, 49500, 49000, 48500, 48000, 47500, 47000],
                'ETHUSDT': [3000, 2970, 2940, 2910, 2880, 2850, 2820]
            },
            expected_events=["drawdown_warning", "trend_reversal_needed"],
            expected_actions=["strategy_pause", "position_reduction"],
            max_drawdown_expected=0.08,
            duration_seconds=25
        )
    
    def generate_recovery_scenario(self) -> RiskTestScenario:
        """生成市场恢复场景"""
        return RiskTestScenario(
            scenario_id="market_recovery_001",
            name="Market Recovery Scenario",
            description="Simulate market recovery after drawdown",
            initial_portfolio_value=85000,  # 已经有15%回撤
            price_changes={
                'BTCUSDT': [42500, 44000, 46000, 48000, 50000, 52000],
                'ETHUSDT': [2550, 2640, 2760, 2880, 3000, 3120]
            },
            expected_events=["recovery_detected", "drawdown_reduced"],
            expected_actions=["resume_trading", "increase_positions"],
            max_drawdown_expected=0.05,
            duration_seconds=18
        )
    
    def get_all_scenarios(self) -> List[RiskTestScenario]:
        """获取所有测试场景"""
        return [
            self.generate_market_crash_scenario(),
            self.generate_volatility_spike_scenario(),
            self.generate_gradual_decline_scenario(),
            self.generate_recovery_scenario()
        ]


class RiskPerformanceMonitor:
    """风险管理性能监控器"""
    
    def __init__(self):
        """初始化性能监控器"""
        self.metrics = {}
        self.start_time = None
        self.monitoring = False
        logger.info("Risk performance monitor initialized")
    
    def start_monitoring(self, risk_engine: RiskControlEngine) -> None:
        """开始监控"""
        self.start_time = datetime.now()
        self.monitoring = True
        self.risk_engine = risk_engine
        
        # 初始化指标
        self.metrics = {
            'response_times': [],
            'event_processing_times': [],
            'action_execution_times': [],
            'memory_usage': [],
            'cpu_usage': [],
            'error_count': 0,
            'total_events': 0,
            'total_actions': 0
        }
        
        logger.info("Risk performance monitoring started")
    
    def stop_monitoring(self) -> Dict[str, Any]:
        """停止监控并返回结果"""
        if not self.monitoring:
            return {}
        
        self.monitoring = False
        end_time = datetime.now()
        total_duration = (end_time - self.start_time).total_seconds()
        
        # 计算性能指标
        performance_summary = {
            'total_duration': total_duration,
            'avg_response_time': np.mean(self.metrics['response_times']) if self.metrics['response_times'] else 0,
            'max_response_time': np.max(self.metrics['response_times']) if self.metrics['response_times'] else 0,
            'min_response_time': np.min(self.metrics['response_times']) if self.metrics['response_times'] else 0,
            'avg_event_processing_time': np.mean(self.metrics['event_processing_times']) if self.metrics['event_processing_times'] else 0,
            'avg_action_execution_time': np.mean(self.metrics['action_execution_times']) if self.metrics['action_execution_times'] else 0,
            'events_per_second': self.metrics['total_events'] / total_duration if total_duration > 0 else 0,
            'actions_per_second': self.metrics['total_actions'] / total_duration if total_duration > 0 else 0,
            'error_rate': self.metrics['error_count'] / max(self.metrics['total_events'], 1),
            'raw_metrics': self.metrics
        }
        
        logger.info(f"Risk performance monitoring stopped. Duration: {total_duration:.2f}s")
        return performance_summary
    
    def record_response_time(self, response_time: float) -> None:
        """记录响应时间"""
        if self.monitoring:
            self.metrics['response_times'].append(response_time)
    
    def record_event_processing_time(self, processing_time: float) -> None:
        """记录事件处理时间"""
        if self.monitoring:
            self.metrics['event_processing_times'].append(processing_time)
            self.metrics['total_events'] += 1
    
    def record_action_execution_time(self, execution_time: float) -> None:
        """记录动作执行时间"""
        if self.monitoring:
            self.metrics['action_execution_times'].append(execution_time)
            self.metrics['total_actions'] += 1
    
    def record_error(self) -> None:
        """记录错误"""
        if self.monitoring:
            self.metrics['error_count'] += 1


class RiskTestExecutor:
    """风险测试执行器"""
    
    def __init__(self, risk_config: Optional[RiskConfig] = None):
        """
        初始化风险测试执行器
        
        Args:
            risk_config: 风险管理配置
        """
        self.risk_config = risk_config or RiskConfig()
        self.scenario_generator = RiskScenarioGenerator()
        self.performance_monitor = RiskPerformanceMonitor()
        self.results = []
        
        logger.info("Risk test executor initialized")
    
    def execute_scenario(self, scenario: RiskTestScenario) -> RiskTestResult:
        """
        执行单个风险测试场景
        
        Args:
            scenario: 风险测试场景
            
        Returns:
            测试结果
        """
        logger.info(f"Executing risk scenario: {scenario.name}")
        
        start_time = time.time()
        result = RiskTestResult(
            scenario_id=scenario.scenario_id,
            success=False,
            execution_time=0,
            events_generated=[],
            actions_executed=[],
            final_portfolio_value=scenario.initial_portfolio_value,
            max_drawdown_observed=0,
            performance_metrics={}
        )
        
        try:
            # 创建风险控制引擎
            risk_engine = RiskControlEngine(self.risk_config)
            
            # 开始性能监控
            self.performance_monitor.start_monitoring(risk_engine)
            
            # 启动风险引擎
            risk_engine.start()
            
            # 执行价格变化场景
            self._execute_price_scenario(risk_engine, scenario)
            
            # 等待处理完成
            time.sleep(2)
            
            # 收集结果
            result.events_generated = risk_engine.risk_events.copy()
            result.actions_executed = [action.to_dict() for action in risk_engine.action_history]
            result.final_portfolio_value = self._calculate_final_portfolio_value(scenario)
            result.max_drawdown_observed = self._calculate_max_drawdown(scenario)
            
            # 停止监控
            result.performance_metrics = self.performance_monitor.stop_monitoring()
            
            # 停止风险引擎
            risk_engine.stop()
            
            # 验证结果
            result.success = self._validate_scenario_result(scenario, result)
            
        except Exception as e:
            logger.error(f"Error executing scenario {scenario.scenario_id}: {e}")
            result.error_message = str(e)
        
        finally:
            result.execution_time = time.time() - start_time
        
        logger.info(f"Scenario {scenario.name} completed in {result.execution_time:.2f}s")
        return result
    
    def _execute_price_scenario(self, risk_engine: RiskControlEngine, scenario: RiskTestScenario) -> None:
        """执行价格变化场景"""
        try:
            # 初始化投资组合
            initial_portfolio = {
                'total_value': scenario.initial_portfolio_value,
                'balance': scenario.initial_portfolio_value,
                'available_balance': scenario.initial_portfolio_value * 0.8,
                'positions': {}
            }
            
            # 为每个交易对创建初始仓位
            for symbol, prices in scenario.price_changes.items():
                if prices:
                    initial_price = prices[0]
                    position_size = scenario.initial_portfolio_value * 0.4 / initial_price  # 40%仓位
                    
                    initial_portfolio['positions'][symbol] = {
                        'size': position_size,
                        'value': position_size * initial_price,
                        'side': 'long',
                        'entry_price': initial_price,
                        'current_price': initial_price,
                        'unrealized_pnl': 0
                    }
            
            risk_engine.update_portfolio_data(initial_portfolio)
            
            # 逐步更新价格
            time_interval = scenario.duration_seconds / max(len(list(scenario.price_changes.values())[0]) - 1, 1)
            
            for i in range(1, len(list(scenario.price_changes.values())[0])):
                for symbol, prices in scenario.price_changes.items():
                    if i < len(prices):
                        current_price = prices[i]
                        
                        # 更新仓位信息
                        if symbol in initial_portfolio['positions']:
                            position_data = initial_portfolio['positions'][symbol]
                            entry_price = position_data['entry_price']
                            position_size = position_data['size']
                            
                            position_info = PositionInfo(
                                symbol=symbol,
                                current_size=position_size,
                                suggested_size=position_size,
                                max_allowed_size=position_size * 1.5,
                                entry_price=entry_price,
                                current_price=current_price,
                                unrealized_pnl=(current_price - entry_price) * position_size,
                                stop_loss_price=entry_price * 0.95,  # 5%止损
                                take_profit_price=entry_price * 1.1,  # 10%止盈
                                position_value=current_price * position_size
                            )
                            
                            risk_engine.update_position(symbol, position_info)
                
                # 等待下一个时间点
                time.sleep(time_interval)
                
        except Exception as e:
            logger.error(f"Error executing price scenario: {e}")
            raise
    
    def _calculate_final_portfolio_value(self, scenario: RiskTestScenario) -> float:
        """计算最终投资组合价值"""
        try:
            total_value = 0
            for symbol, prices in scenario.price_changes.items():
                if prices:
                    final_price = prices[-1]
                    initial_price = prices[0]
                    position_size = scenario.initial_portfolio_value * 0.4 / initial_price
                    position_value = position_size * final_price
                    total_value += position_value
            
            # 加上现金部分
            cash_portion = scenario.initial_portfolio_value * 0.2
            total_value += cash_portion
            
            return total_value
            
        except Exception as e:
            logger.error(f"Error calculating final portfolio value: {e}")
            return scenario.initial_portfolio_value
    
    def _calculate_max_drawdown(self, scenario: RiskTestScenario) -> float:
        """计算最大回撤"""
        try:
            portfolio_values = []
            
            for i in range(len(list(scenario.price_changes.values())[0])):
                total_value = 0
                for symbol, prices in scenario.price_changes.items():
                    if i < len(prices):
                        current_price = prices[i]
                        initial_price = prices[0]
                        position_size = scenario.initial_portfolio_value * 0.4 / initial_price
                        position_value = position_size * current_price
                        total_value += position_value
                
                # 加上现金部分
                cash_portion = scenario.initial_portfolio_value * 0.2
                total_value += cash_portion
                portfolio_values.append(total_value)
            
            # 计算最大回撤
            peak = portfolio_values[0]
            max_drawdown = 0
            
            for value in portfolio_values:
                if value > peak:
                    peak = value
                drawdown = (peak - value) / peak
                if drawdown > max_drawdown:
                    max_drawdown = drawdown
            
            return max_drawdown
            
        except Exception as e:
            logger.error(f"Error calculating max drawdown: {e}")
            return 0
    
    def _validate_scenario_result(self, scenario: RiskTestScenario, result: RiskTestResult) -> bool:
        """验证场景结果"""
        try:
            # 检查是否生成了预期的事件
            event_types = [event.event_type for event in result.events_generated]
            
            for expected_event in scenario.expected_events:
                if expected_event not in event_types:
                    logger.warning(f"Expected event {expected_event} not found in results")
                    return False
            
            # 检查最大回撤是否在预期范围内
            if result.max_drawdown_observed > scenario.max_drawdown_expected * 1.2:  # 允许20%误差
                logger.warning(f"Max drawdown {result.max_drawdown_observed:.2%} exceeds expected {scenario.max_drawdown_expected:.2%}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error validating scenario result: {e}")
            return False
    
    def execute_all_scenarios(self) -> List[RiskTestResult]:
        """执行所有风险测试场景"""
        logger.info("Executing all risk test scenarios...")
        
        scenarios = self.scenario_generator.get_all_scenarios()
        results = []
        
        for scenario in scenarios:
            result = self.execute_scenario(scenario)
            results.append(result)
            self.results.append(result)
        
        logger.info(f"Completed {len(results)} risk test scenarios")
        return results
    
    def generate_test_report(self) -> Dict[str, Any]:
        """生成测试报告"""
        if not self.results:
            return {'error': 'No test results available'}
        
        successful_tests = [r for r in self.results if r.success]
        failed_tests = [r for r in self.results if not r.success]
        
        report = {
            'summary': {
                'total_tests': len(self.results),
                'successful_tests': len(successful_tests),
                'failed_tests': len(failed_tests),
                'success_rate': len(successful_tests) / len(self.results) * 100,
                'total_execution_time': sum(r.execution_time for r in self.results)
            },
            'performance_metrics': {
                'avg_execution_time': np.mean([r.execution_time for r in self.results]),
                'max_execution_time': np.max([r.execution_time for r in self.results]),
                'min_execution_time': np.min([r.execution_time for r in self.results])
            },
            'detailed_results': [
                {
                    'scenario_id': r.scenario_id,
                    'success': r.success,
                    'execution_time': r.execution_time,
                    'events_count': len(r.events_generated),
                    'actions_count': len(r.actions_executed),
                    'max_drawdown': r.max_drawdown_observed,
                    'error_message': r.error_message
                }
                for r in self.results
            ]
        }
        
        return report


if __name__ == '__main__':
    # 示例用法
    executor = RiskTestExecutor()
    results = executor.execute_all_scenarios()
    report = executor.generate_test_report()
    print(json.dumps(report, indent=2, default=str))
