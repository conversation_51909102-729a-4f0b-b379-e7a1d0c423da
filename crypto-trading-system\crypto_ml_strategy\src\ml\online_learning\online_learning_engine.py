#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
在线学习引擎

统一管理在线学习的核心流程，协调学习策略管理器、性能监控器、模型版本管理器和增量训练器。
提供完整的在线学习解决方案，支持实时模型更新和性能优化。
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any, Union, Callable
from datetime import datetime, timedelta
import threading
from collections import defaultdict, deque
from dataclasses import dataclass, field
import time
from enum import Enum
import json
import asyncio

# 导入相关组件
from .learning_strategy import LearningStrategyManager, StrategyConfig, StrategyPerformance
from .performance_monitor import PerformanceMonitor, PerformanceMetric, MetricType
from .model_version_manager import ModelVersionManager, ModelMetadata, VersionStatus
from .incremental_trainer import IncrementalTrainer, TrainingResult, TrainingBatch


class EngineStatus(Enum):
    """引擎状态枚举"""
    STOPPED = "stopped"
    STARTING = "starting"
    RUNNING = "running"
    PAUSED = "paused"
    ERROR = "error"


class LearningPhase(Enum):
    """学习阶段枚举"""
    INITIALIZATION = "initialization"
    WARM_UP = "warm_up"
    ACTIVE_LEARNING = "active_learning"
    FINE_TUNING = "fine_tuning"
    MAINTENANCE = "maintenance"


@dataclass
class LearningSession:
    """学习会话"""
    session_id: str
    start_time: datetime
    end_time: Optional[datetime]
    model_version: str
    strategy_used: str
    samples_processed: int
    performance_improvement: float
    status: str


@dataclass
class EngineConfig:
    """引擎配置"""
    model_name: str = "crypto_ml_model"
    learning_phase: LearningPhase = LearningPhase.INITIALIZATION
    auto_version_management: bool = True
    auto_strategy_selection: bool = True
    performance_monitoring: bool = True
    enable_model_persistence: bool = True


class OnlineLearningEngine:
    """在线学习引擎"""
    
    def __init__(self, config: Dict = None):
        """
        初始化在线学习引擎
        
        Args:
            config: 配置参数字典
        """
        self.logger = logging.getLogger('cryptoMlStrategy.online_learning.engine')
        
        # 默认配置
        self.config = {
            'engine': {
                'model_name': 'crypto_ml_model',
                'learning_phase': 'initialization',
                'auto_version_management': True,
                'auto_strategy_selection': True,
                'performance_monitoring': True,
                'enable_model_persistence': True
            },
            'learning': {
                'warm_up_samples': 1000,
                'min_samples_for_update': 50,
                'performance_evaluation_interval': 300,  # 5分钟
                'model_save_interval': 1800,             # 30分钟
                'strategy_adaptation_interval': 3600     # 1小时
            },
            'thresholds': {
                'min_performance_score': 0.7,
                'performance_degradation_threshold': 0.05,
                'model_update_threshold': 0.02,
                'version_creation_threshold': 0.1
            },
            'integration': {
                'kafka_enabled': True,
                'multi_timeframe_enabled': True,
                'technical_indicators_enabled': True,
                'quality_check_enabled': True
            }
        }
        
        # 更新用户配置
        if config:
            self._deep_update(self.config, config)
        
        # 初始化组件
        self.strategy_manager = LearningStrategyManager(config.get('strategy_manager', {}))
        self.performance_monitor = PerformanceMonitor(config.get('performance_monitor', {}))
        self.version_manager = ModelVersionManager(config.get('version_manager', {}))
        self.incremental_trainer = IncrementalTrainer(config.get('incremental_trainer', {}))
        
        # 引擎状态
        self.status = EngineStatus.STOPPED
        self.current_phase = LearningPhase.INITIALIZATION
        self.current_model = None
        self.current_session = None
        
        # 学习历史
        self.learning_sessions = deque(maxlen=1000)
        self.performance_history = deque(maxlen=10000)
        
        # 统计信息
        self.stats = {
            'total_sessions': 0,
            'total_samples_processed': 0,
            'total_model_updates': 0,
            'total_version_created': 0,
            'engine_uptime': 0,
            'last_performance_score': 0.0,
            'start_time': None
        }
        
        # 线程管理
        self.engine_lock = threading.RLock()
        self.monitoring_active = False
        
        self.logger.info("在线学习引擎初始化完成")
    
    def _deep_update(self, base_dict: Dict, update_dict: Dict):
        """深度更新字典"""
        for key, value in update_dict.items():
            if key in base_dict and isinstance(base_dict[key], dict) and isinstance(value, dict):
                self._deep_update(base_dict[key], value)
            else:
                base_dict[key] = value
    
    def start_engine(self, initial_model: Any = None) -> bool:
        """
        启动在线学习引擎
        
        Args:
            initial_model: 初始模型
            
        Returns:
            是否启动成功
        """
        try:
            with self.engine_lock:
                if self.status == EngineStatus.RUNNING:
                    self.logger.warning("引擎已在运行中")
                    return True
                
                self.status = EngineStatus.STARTING
                self.logger.info("正在启动在线学习引擎...")
                
                # 设置初始模型
                if initial_model is not None:
                    self.current_model = initial_model
                    self.incremental_trainer.set_model(initial_model)
                else:
                    # 尝试加载最新版本的模型
                    self.current_model = self._load_latest_model()
                    if self.current_model:
                        self.incremental_trainer.set_model(self.current_model)
                
                # 启动监控循环
                self._start_monitoring_loops()
                
                # 创建初始学习会话
                self._start_new_session()
                
                self.status = EngineStatus.RUNNING
                self.stats['start_time'] = datetime.now()
                
                self.logger.info("在线学习引擎启动成功")
                return True
                
        except Exception as e:
            self.logger.error(f"启动引擎失败: {e}")
            self.status = EngineStatus.ERROR
            return False
    
    def stop_engine(self) -> bool:
        """
        停止在线学习引擎
        
        Returns:
            是否停止成功
        """
        try:
            with self.engine_lock:
                if self.status == EngineStatus.STOPPED:
                    return True
                
                self.logger.info("正在停止在线学习引擎...")
                
                # 停止监控循环
                self.monitoring_active = False
                
                # 结束当前会话
                if self.current_session:
                    self._end_current_session()
                
                # 保存当前模型
                if self.current_model and self.config['engine']['enable_model_persistence']:
                    self._save_current_model()
                
                # 停止组件
                self.incremental_trainer.stop_training()
                self.performance_monitor.stop_monitoring()
                
                self.status = EngineStatus.STOPPED
                
                self.logger.info("在线学习引擎已停止")
                return True
                
        except Exception as e:
            self.logger.error(f"停止引擎失败: {e}")
            return False
    
    def _load_latest_model(self) -> Optional[Any]:
        """加载最新模型"""
        try:
            # 获取版本管理器的活跃版本
            active_version = self.version_manager.active_version
            if active_version:
                model = self.version_manager.load_model_version(active_version)
                if model:
                    self.logger.info(f"加载活跃模型版本: {active_version}")
                    return model
            
            # 如果没有活跃版本，尝试加载最新版本
            versions = self.version_manager.versions
            if versions:
                latest_version = max(versions.keys(), 
                                   key=lambda v: versions[v].creation_time)
                model = self.version_manager.load_model_version(latest_version)
                if model:
                    self.version_manager.set_active_version(latest_version)
                    self.logger.info(f"加载最新模型版本: {latest_version}")
                    return model
            
            self.logger.warning("没有找到可用的模型版本")
            return None
            
        except Exception as e:
            self.logger.error(f"加载最新模型失败: {e}")
            return None
    
    def _start_monitoring_loops(self):
        """启动监控循环"""
        try:
            self.monitoring_active = True
            
            # 启动性能监控循环
            performance_thread = threading.Thread(target=self._performance_monitoring_loop, daemon=True)
            performance_thread.start()
            
            # 启动策略适应循环
            strategy_thread = threading.Thread(target=self._strategy_adaptation_loop, daemon=True)
            strategy_thread.start()
            
            # 启动模型保存循环
            save_thread = threading.Thread(target=self._model_save_loop, daemon=True)
            save_thread.start()
            
            self.logger.info("监控循环已启动")
            
        except Exception as e:
            self.logger.error(f"启动监控循环失败: {e}")
    
    def _performance_monitoring_loop(self):
        """性能监控循环"""
        while self.monitoring_active:
            try:
                # 收集性能指标
                self._collect_performance_metrics()
                
                # 检查性能阈值
                self._check_performance_thresholds()
                
                # 等待下次监控
                time.sleep(self.config['learning']['performance_evaluation_interval'])
                
            except Exception as e:
                self.logger.error(f"性能监控循环错误: {e}")
                time.sleep(30)
    
    def _strategy_adaptation_loop(self):
        """策略适应循环"""
        while self.monitoring_active:
            try:
                # 检查是否需要调整策略
                self._adapt_learning_strategy()
                
                # 等待下次检查
                time.sleep(self.config['learning']['strategy_adaptation_interval'])
                
            except Exception as e:
                self.logger.error(f"策略适应循环错误: {e}")
                time.sleep(60)
    
    def _model_save_loop(self):
        """模型保存循环"""
        while self.monitoring_active:
            try:
                # 定期保存模型
                if self.current_model and self.config['engine']['enable_model_persistence']:
                    self._save_current_model()
                
                # 等待下次保存
                time.sleep(self.config['learning']['model_save_interval'])
                
            except Exception as e:
                self.logger.error(f"模型保存循环错误: {e}")
                time.sleep(300)
    
    def process_new_data(self, features: np.ndarray, targets: np.ndarray,
                        timestamps: List[datetime] = None,
                        metadata: Dict[str, Any] = None) -> bool:
        """
        处理新数据
        
        Args:
            features: 特征数据
            targets: 目标数据
            timestamps: 时间戳列表
            metadata: 元数据
            
        Returns:
            是否处理成功
        """
        try:
            if self.status != EngineStatus.RUNNING:
                self.logger.warning("引擎未运行，无法处理数据")
                return False
            
            # 添加数据到增量训练器
            success = self.incremental_trainer.add_training_data(
                features, targets, timestamps, metadata
            )
            
            if success:
                # 更新统计
                self.stats['total_samples_processed'] += len(features)
                
                # 更新当前会话
                if self.current_session:
                    self.current_session.samples_processed += len(features)
                
                # 检查学习阶段
                self._check_learning_phase()
                
            return success
            
        except Exception as e:
            self.logger.error(f"处理新数据失败: {e}")
            return False
    
    def _collect_performance_metrics(self):
        """收集性能指标"""
        try:
            # 获取训练器统计
            trainer_stats = self.incremental_trainer.get_training_statistics()
            
            # 记录性能指标
            if 'performance_metrics' in trainer_stats:
                metrics = trainer_stats['performance_metrics']
                
                # 记录准确率
                if 'current_accuracy' in metrics:
                    self.performance_monitor.record_metric(
                        'model_accuracy', 
                        metrics['current_accuracy'], 
                        MetricType.ACCURACY
                    )
                
                # 记录损失
                if 'current_loss' in metrics:
                    self.performance_monitor.record_metric(
                        'model_loss', 
                        metrics['current_loss'], 
                        MetricType.LOSS
                    )
            
            # 记录训练统计
            if 'stats' in trainer_stats:
                stats = trainer_stats['stats']
                
                # 记录训练时间
                if 'average_training_time' in stats:
                    self.performance_monitor.record_metric(
                        'training_time', 
                        stats['average_training_time'], 
                        MetricType.LATENCY
                    )
            
            # 更新引擎统计
            self._update_engine_stats()
            
        except Exception as e:
            self.logger.error(f"收集性能指标失败: {e}")
    
    def _check_performance_thresholds(self):
        """检查性能阈值"""
        try:
            current_score = self.performance_monitor.get_current_performance_score()
            self.stats['last_performance_score'] = current_score
            
            min_score = self.config['thresholds']['min_performance_score']
            degradation_threshold = self.config['thresholds']['performance_degradation_threshold']
            
            # 检查性能是否低于最低阈值
            if current_score < min_score:
                self.logger.warning(f"性能低于最低阈值: {current_score:.3f} < {min_score}")
                self._handle_low_performance()
            
            # 检查性能是否显著下降
            recent_scores = self.performance_monitor.get_performance_trend('model_accuracy', 1)
            if len(recent_scores) > 10:
                recent_avg = np.mean(recent_scores[-5:])
                overall_avg = np.mean(recent_scores)
                
                if recent_avg < overall_avg * (1 - degradation_threshold):
                    self.logger.warning("检测到性能下降")
                    self._handle_performance_degradation()
            
        except Exception as e:
            self.logger.error(f"检查性能阈值失败: {e}")
    
    def _handle_low_performance(self):
        """处理低性能情况"""
        try:
            self.logger.info("处理低性能情况...")
            
            # 尝试切换学习策略
            if self.config['engine']['auto_strategy_selection']:
                current_metrics = {
                    'accuracy': self.stats['last_performance_score']
                }
                self.strategy_manager.select_strategy(performance_metrics=current_metrics)
            
            # 考虑回滚到之前的模型版本
            if self.config['engine']['auto_version_management']:
                self._consider_model_rollback()
                
        except Exception as e:
            self.logger.error(f"处理低性能失败: {e}")
    
    def _handle_performance_degradation(self):
        """处理性能下降"""
        try:
            self.logger.info("处理性能下降...")
            
            # 调整学习策略参数
            current_strategy = self.strategy_manager.get_current_strategy()
            if current_strategy:
                # 获取性能趋势
                trend = self.performance_monitor.get_performance_trend('model_accuracy', 2)
                self.strategy_manager.adapt_strategy_parameters(
                    current_strategy.name, trend
                )
                
        except Exception as e:
            self.logger.error(f"处理性能下降失败: {e}")
    
    def _consider_model_rollback(self):
        """考虑模型回滚"""
        try:
            # 获取当前活跃版本
            active_version = self.version_manager.active_version
            if not active_version:
                return
            
            # 获取版本历史
            versions = self.version_manager.versions
            if len(versions) < 2:
                return
            
            # 找到性能更好的历史版本
            best_version = None
            best_score = 0.0
            
            for version_id, metadata in versions.items():
                if version_id != active_version and 'accuracy' in metadata.performance_metrics:
                    accuracy = metadata.performance_metrics['accuracy']
                    if accuracy > best_score:
                        best_score = accuracy
                        best_version = version_id
            
            # 如果找到更好的版本，考虑回滚
            current_score = self.stats['last_performance_score']
            improvement_threshold = self.config['thresholds']['model_update_threshold']
            
            if best_version and best_score > current_score + improvement_threshold:
                self.logger.info(f"考虑回滚到版本: {best_version} (分数: {best_score:.3f})")
                # 这里可以实现自动回滚逻辑或发送告警
                
        except Exception as e:
            self.logger.error(f"考虑模型回滚失败: {e}")
    
    def _adapt_learning_strategy(self):
        """适应学习策略"""
        try:
            if not self.config['engine']['auto_strategy_selection']:
                return
            
            # 获取当前性能指标
            current_score = self.stats['last_performance_score']
            
            # 检测市场条件（如果有市场数据）
            # 这里可以集成多时间周期数据处理模块
            
            # 选择最适合的策略
            performance_metrics = {
                'accuracy': current_score,
                'sample_count': self.stats['total_samples_processed']
            }
            
            selected_strategy = self.strategy_manager.select_strategy(
                performance_metrics=performance_metrics
            )
            
            if selected_strategy:
                self.logger.info(f"策略适应完成，当前策略: {selected_strategy}")
                
        except Exception as e:
            self.logger.error(f"适应学习策略失败: {e}")
    
    def _save_current_model(self):
        """保存当前模型"""
        try:
            if not self.current_model:
                return
            
            # 获取当前性能指标
            trainer_stats = self.incremental_trainer.get_training_statistics()
            performance_metrics = trainer_stats.get('performance_metrics', {})
            
            # 获取当前策略
            current_strategy = self.strategy_manager.get_current_strategy()
            hyperparameters = {}
            if current_strategy:
                hyperparameters = {
                    'learning_rate': current_strategy.learning_rate,
                    'batch_size': current_strategy.batch_size,
                    'momentum': current_strategy.momentum
                }
            
            # 保存模型版本
            version_id = self.version_manager.save_model_version(
                model=self.current_model,
                model_name=self.config['engine']['model_name'],
                performance_metrics=performance_metrics,
                hyperparameters=hyperparameters,
                training_samples=self.stats['total_samples_processed'],
                training_duration=self._calculate_training_duration(),
                description=f"Auto-saved model at {datetime.now().isoformat()}"
            )
            
            if version_id:
                self.stats['total_version_created'] += 1
                self.logger.info(f"模型已保存，版本: {version_id}")
                
        except Exception as e:
            self.logger.error(f"保存当前模型失败: {e}")
    
    def _calculate_training_duration(self) -> float:
        """计算训练持续时间"""
        if self.stats['start_time']:
            return (datetime.now() - self.stats['start_time']).total_seconds()
        return 0.0
    
    def _check_learning_phase(self):
        """检查学习阶段"""
        try:
            samples_processed = self.stats['total_samples_processed']
            warm_up_samples = self.config['learning']['warm_up_samples']
            
            if self.current_phase == LearningPhase.INITIALIZATION and samples_processed > 0:
                self.current_phase = LearningPhase.WARM_UP
                self.logger.info("进入预热阶段")
            
            elif self.current_phase == LearningPhase.WARM_UP and samples_processed >= warm_up_samples:
                self.current_phase = LearningPhase.ACTIVE_LEARNING
                self.logger.info("进入主动学习阶段")
                
        except Exception as e:
            self.logger.error(f"检查学习阶段失败: {e}")
    
    def _start_new_session(self):
        """开始新的学习会话"""
        try:
            session_id = f"session_{int(datetime.now().timestamp())}"
            
            current_strategy = self.strategy_manager.get_current_strategy()
            strategy_name = current_strategy.name if current_strategy else "unknown"
            
            active_version = self.version_manager.active_version or "none"
            
            self.current_session = LearningSession(
                session_id=session_id,
                start_time=datetime.now(),
                end_time=None,
                model_version=active_version,
                strategy_used=strategy_name,
                samples_processed=0,
                performance_improvement=0.0,
                status="active"
            )
            
            self.stats['total_sessions'] += 1
            self.logger.info(f"新学习会话已开始: {session_id}")
            
        except Exception as e:
            self.logger.error(f"开始新会话失败: {e}")
    
    def _end_current_session(self):
        """结束当前学习会话"""
        try:
            if not self.current_session:
                return
            
            self.current_session.end_time = datetime.now()
            self.current_session.status = "completed"
            
            # 计算性能改进
            # 这里可以实现更复杂的性能改进计算逻辑
            
            # 保存会话历史
            self.learning_sessions.append(self.current_session)
            
            self.logger.info(f"学习会话已结束: {self.current_session.session_id}")
            self.current_session = None
            
        except Exception as e:
            self.logger.error(f"结束当前会话失败: {e}")
    
    def _update_engine_stats(self):
        """更新引擎统计"""
        try:
            if self.stats['start_time']:
                self.stats['engine_uptime'] = (
                    datetime.now() - self.stats['start_time']
                ).total_seconds()
                
        except Exception as e:
            self.logger.error(f"更新引擎统计失败: {e}")
    
    def get_engine_status(self) -> Dict[str, Any]:
        """获取引擎状态"""
        try:
            with self.engine_lock:
                return {
                    'status': self.status.value,
                    'current_phase': self.current_phase.value,
                    'current_model_available': self.current_model is not None,
                    'current_session': {
                        'session_id': self.current_session.session_id,
                        'start_time': self.current_session.start_time.isoformat(),
                        'samples_processed': self.current_session.samples_processed,
                        'strategy_used': self.current_session.strategy_used
                    } if self.current_session else None,
                    'stats': self.stats.copy(),
                    'component_status': {
                        'strategy_manager': self.strategy_manager.get_strategy_statistics(),
                        'performance_monitor': self.performance_monitor.get_monitoring_statistics(),
                        'version_manager': self.version_manager.get_version_statistics(),
                        'incremental_trainer': self.incremental_trainer.get_training_statistics()
                    },
                    'config': self.config
                }
                
        except Exception as e:
            self.logger.error(f"获取引擎状态失败: {e}")
            return {'status': 'error', 'error': str(e)}