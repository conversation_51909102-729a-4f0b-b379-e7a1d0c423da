#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
风险控制系统专项测试

专门测试风险控制系统的核心功能，包括风险评估、
风险事件处理、紧急风险控制等关键功能。

Author: Crypto ML Strategy Team
Date: 2024-12-19
"""

import sys
import os
import unittest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import logging
import time
import threading
from unittest.mock import Mock, patch, MagicMock

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..', 'src'))

# 导入风险管理模块
from risk_management import (
    RiskConfig, RiskEvent, RiskLevel, PositionInfo,
    RiskControlEngine, RiskAssessor, 
    PositionSizeMethod, StopLossType, TakeProfitType
)

# 导入测试工具
from ..utils.risk_management_test_helpers import RiskScenarioGenerator, RiskPerformanceMonitor
from ..config.integration_test_config import IntegrationTestConfig
from ..data.test_data_generator import TestDataGenerator

logger = logging.getLogger(__name__)


class RiskControlSystemTest(unittest.TestCase):
    """风险控制系统专项测试"""
    
    @classmethod
    def setUpClass(cls):
        """测试类初始化"""
        cls.config = IntegrationTestConfig()
        cls.data_generator = TestDataGenerator()
        cls.scenario_generator = RiskScenarioGenerator()
        
        # 设置日志
        logging.basicConfig(level=logging.INFO)
        
        logger.info("Risk Control System Test initialized")
    
    def setUp(self):
        """每个测试用例的初始化"""
        self.test_start_time = datetime.now()
        
        # 创建风险管理配置
        self.risk_config = RiskConfig(
            max_single_position_ratio=0.1,
            max_total_position_ratio=0.8,
            position_size_method=PositionSizeMethod.ADAPTIVE,
            stop_loss_type=StopLossType.ADAPTIVE,
            take_profit_type=TakeProfitType.RISK_REWARD_RATIO,
            max_drawdown_threshold=0.15,
            risk_reward_ratio=2.0,
            risk_check_interval=0.5  # 更频繁的检查
        )
        
        # 创建风险控制引擎
        self.risk_engine = RiskControlEngine(self.risk_config)
        
        logger.info(f"Test case setup: {self._testMethodName}")
    
    def tearDown(self):
        """每个测试用例的清理"""
        if hasattr(self, 'risk_engine') and self.risk_engine.is_running:
            self.risk_engine.stop()
        
        test_duration = (datetime.now() - self.test_start_time).total_seconds()
        logger.info(f"Test case completed: {self._testMethodName}, Duration: {test_duration:.2f}s")
    
    def test_risk_assessor_functionality(self):
        """测试风险评估器功能"""
        logger.info("Testing risk assessor functionality...")
        
        try:
            # 创建独立的风险评估器
            risk_assessor = RiskAssessor(self.risk_config)
            
            # 生成测试数据
            price_data = self.data_generator.generate_price_data(
                symbol='BTCUSDT',
                start_time=datetime.now() - timedelta(days=30),
                end_time=datetime.now(),
                interval='1h',
                initial_price=50000
            )
            
            portfolio_data = {
                'total_value': 100000,
                'balance': 100000,
                'available_balance': 80000,
                'positions': {
                    'BTCUSDT': {
                        'size': 1.5,
                        'value': 75000,
                        'side': 'long',
                        'entry_price': 50000,
                        'current_price': 50000,
                        'unrealized_pnl': 0
                    }
                }
            }
            
            # 更新数据
            risk_assessor.update_price_data('BTCUSDT', price_data)
            risk_assessor.update_portfolio_data(portfolio_data)
            
            # 执行风险评估
            risk_metrics = risk_assessor.assess_portfolio_risk(portfolio_data)
            
            # 验证风险指标
            self.assertIsNotNone(risk_metrics)
            self.assertGreater(risk_metrics.portfolio_value, 0)
            self.assertIsInstance(risk_metrics.risk_level, RiskLevel)
            self.assertGreaterEqual(risk_metrics.var_95, 0)
            self.assertGreaterEqual(risk_metrics.cvar_95, 0)
            self.assertGreaterEqual(risk_metrics.volatility, 0)
            
            # 生成风险事件
            risk_events = risk_assessor.generate_risk_events(risk_metrics)
            self.assertIsInstance(risk_events, list)
            
            logger.info("✅ Risk assessor functionality test passed")
            
        except Exception as e:
            logger.error(f"❌ Risk assessor functionality test failed: {e}")
            raise
    
    def test_risk_event_processing(self):
        """测试风险事件处理"""
        logger.info("Testing risk event processing...")
        
        try:
            # 启动风险引擎
            self.risk_engine.start()
            
            # 创建不同级别的风险事件
            events = [
                RiskEvent(
                    event_type="position_limit_exceeded",
                    severity=RiskLevel.MEDIUM,
                    message="Position limit exceeded for BTCUSDT",
                    timestamp=datetime.now(),
                    symbol="BTCUSDT",
                    action_required=True
                ),
                RiskEvent(
                    event_type="high_volatility_detected",
                    severity=RiskLevel.HIGH,
                    message="High volatility detected in market",
                    timestamp=datetime.now(),
                    action_required=True
                ),
                RiskEvent(
                    event_type="emergency_stop_triggered",
                    severity=RiskLevel.CRITICAL,
                    message="Emergency stop triggered",
                    timestamp=datetime.now(),
                    action_required=True
                )
            ]
            
            # 添加事件并验证处理
            for event in events:
                initial_event_count = len(self.risk_engine.risk_events)
                self.risk_engine.add_risk_event(event)
                
                # 验证事件被添加
                self.assertEqual(len(self.risk_engine.risk_events), initial_event_count + 1)
                
                # 等待事件处理
                time.sleep(1)
            
            # 验证紧急事件触发了紧急模式
            critical_events = [e for e in self.risk_engine.risk_events if e.severity == RiskLevel.CRITICAL]
            if critical_events:
                self.assertTrue(self.risk_engine.emergency_mode, "Critical events should trigger emergency mode")
            
            # 验证统计信息更新
            stats = self.risk_engine.statistics
            self.assertGreater(stats['total_events'], 0)
            
            # 停止引擎
            self.risk_engine.stop()
            
            logger.info("✅ Risk event processing test passed")
            
        except Exception as e:
            logger.error(f"❌ Risk event processing test failed: {e}")
            raise
    
    def test_emergency_risk_control(self):
        """测试紧急风险控制"""
        logger.info("Testing emergency risk control...")
        
        try:
            # 启动风险引擎
            self.risk_engine.start()
            
            # 模拟极端市场条件
            extreme_portfolio_data = {
                'total_value': 70000,  # 30%损失
                'balance': 70000,
                'available_balance': 50000,
                'positions': {
                    'BTCUSDT': {
                        'size': 1.5,
                        'value': 52500,  # 大幅亏损
                        'side': 'long',
                        'entry_price': 50000,
                        'current_price': 35000,  # 价格大跌
                        'unrealized_pnl': -22500
                    }
                }
            }
            
            # 更新投资组合数据
            self.risk_engine.update_portfolio_data(extreme_portfolio_data)
            
            # 创建紧急风险事件
            emergency_event = RiskEvent(
                event_type="max_drawdown_exceeded",
                severity=RiskLevel.CRITICAL,
                message="Maximum drawdown threshold exceeded",
                timestamp=datetime.now(),
                current_value=0.3,  # 30%回撤
                threshold_value=0.15,  # 15%阈值
                action_required=True
            )
            
            # 添加紧急事件
            self.risk_engine.add_risk_event(emergency_event)
            
            # 等待紧急处理
            time.sleep(3)
            
            # 验证紧急模式激活
            self.assertTrue(self.risk_engine.emergency_mode, "Emergency mode should be activated")
            
            # 验证紧急措施生成
            pending_actions = self.risk_engine.pending_actions
            emergency_actions = [a for a in pending_actions if a.priority <= 2]  # 高优先级动作
            self.assertGreater(len(emergency_actions), 0, "Should generate emergency actions")
            
            # 验证统计信息
            stats = self.risk_engine.statistics
            self.assertGreater(stats['emergency_triggers'], 0)
            
            # 停止引擎
            self.risk_engine.stop()
            
            logger.info("✅ Emergency risk control test passed")
            
        except Exception as e:
            logger.error(f"❌ Emergency risk control test failed: {e}")
            raise
    
    def test_risk_control_performance(self):
        """测试风险控制性能"""
        logger.info("Testing risk control performance...")
        
        try:
            # 创建性能监控器
            performance_monitor = RiskPerformanceMonitor()
            performance_monitor.start_monitoring(self.risk_engine)
            
            # 启动风险引擎
            self.risk_engine.start()
            
            # 高频更新测试
            test_duration = 10  # 10秒测试
            update_interval = 0.1  # 100ms间隔
            end_time = time.time() + test_duration
            update_count = 0
            
            while time.time() < end_time:
                # 模拟价格更新
                current_price = 50000 + np.random.uniform(-5000, 5000)
                
                market_data = {
                    'price_df': None,  # 简化测试
                    'indicators': {
                        'current_price': current_price,
                        'atr': 1000,
                        'volatility': 0.25
                    }
                }
                
                update_start = time.time()
                self.risk_engine.update_market_data('BTCUSDT', market_data)
                update_time = time.time() - update_start
                
                performance_monitor.record_response_time(update_time)
                update_count += 1
                
                time.sleep(update_interval)
            
            # 停止监控
            performance_metrics = performance_monitor.stop_monitoring()
            
            # 验证性能指标
            self.assertGreater(performance_metrics['total_duration'], 0)
            self.assertGreater(update_count, 0)
            
            # 验证响应时间合理
            avg_response_time = performance_metrics.get('avg_response_time', 0)
            max_response_time = performance_metrics.get('max_response_time', 0)
            
            self.assertLess(avg_response_time, 0.1, "Average response time should be < 100ms")
            self.assertLess(max_response_time, 0.5, "Max response time should be < 500ms")
            
            # 停止引擎
            self.risk_engine.stop()
            
            logger.info(f"✅ Risk control performance test passed")
            logger.info(f"   Updates: {update_count}, Avg Response: {avg_response_time*1000:.2f}ms")
            
        except Exception as e:
            logger.error(f"❌ Risk control performance test failed: {e}")
            raise
    
    def test_concurrent_risk_operations(self):
        """测试并发风险操作"""
        logger.info("Testing concurrent risk operations...")
        
        try:
            # 启动风险引擎
            self.risk_engine.start()
            
            # 创建多个线程同时进行风险操作
            def update_market_data_worker():
                for i in range(20):
                    price = 50000 + np.random.uniform(-2000, 2000)
                    market_data = {
                        'price_df': None,
                        'indicators': {
                            'current_price': price,
                            'atr': 1000,
                            'volatility': 0.2
                        }
                    }
                    self.risk_engine.update_market_data(f'TEST{i%5}USDT', market_data)
                    time.sleep(0.05)
            
            def add_risk_events_worker():
                for i in range(10):
                    event = RiskEvent(
                        event_type=f"test_event_{i}",
                        severity=RiskLevel.MEDIUM,
                        message=f"Test event {i}",
                        timestamp=datetime.now(),
                        action_required=False
                    )
                    self.risk_engine.add_risk_event(event)
                    time.sleep(0.1)
            
            def update_portfolio_worker():
                for i in range(15):
                    portfolio_value = 100000 + np.random.uniform(-10000, 10000)
                    portfolio_data = {
                        'total_value': portfolio_value,
                        'balance': portfolio_value,
                        'available_balance': portfolio_value * 0.8,
                        'positions': {}
                    }
                    self.risk_engine.update_portfolio_data(portfolio_data)
                    time.sleep(0.08)
            
            # 启动并发线程
            threads = [
                threading.Thread(target=update_market_data_worker),
                threading.Thread(target=add_risk_events_worker),
                threading.Thread(target=update_portfolio_worker)
            ]
            
            for thread in threads:
                thread.start()
            
            # 等待所有线程完成
            for thread in threads:
                thread.join(timeout=30)
            
            # 等待处理完成
            time.sleep(2)
            
            # 验证系统状态
            self.assertTrue(self.risk_engine.is_running, "Engine should still be running")
            
            # 验证事件处理
            stats = self.risk_engine.statistics
            self.assertGreater(stats['total_events'], 0, "Should have processed events")
            
            # 停止引擎
            self.risk_engine.stop()
            
            logger.info("✅ Concurrent risk operations test passed")
            
        except Exception as e:
            logger.error(f"❌ Concurrent risk operations test failed: {e}")
            raise
    
    def test_risk_control_recovery(self):
        """测试风险控制恢复机制"""
        logger.info("Testing risk control recovery mechanism...")
        
        try:
            # 启动风险引擎
            self.risk_engine.start()
            
            # 模拟系统进入紧急模式
            emergency_event = RiskEvent(
                event_type="system_anomaly",
                severity=RiskLevel.CRITICAL,
                message="System anomaly detected",
                timestamp=datetime.now(),
                action_required=True
            )
            
            self.risk_engine.add_risk_event(emergency_event)
            time.sleep(2)
            
            # 验证紧急模式激活
            self.assertTrue(self.risk_engine.emergency_mode, "Emergency mode should be activated")
            
            # 模拟市场恢复
            recovery_portfolio_data = {
                'total_value': 95000,  # 轻微恢复
                'balance': 95000,
                'available_balance': 76000,
                'positions': {
                    'BTCUSDT': {
                        'size': 1.0,  # 减少仓位
                        'value': 47500,
                        'side': 'long',
                        'entry_price': 50000,
                        'current_price': 47500,
                        'unrealized_pnl': -2500
                    }
                }
            }
            
            # 更新恢复数据
            self.risk_engine.update_portfolio_data(recovery_portfolio_data)
            
            # 添加恢复事件
            recovery_event = RiskEvent(
                event_type="market_stabilized",
                severity=RiskLevel.LOW,
                message="Market conditions stabilized",
                timestamp=datetime.now(),
                action_required=False
            )
            
            self.risk_engine.add_risk_event(recovery_event)
            time.sleep(2)
            
            # 验证系统状态
            stats = self.risk_engine.statistics
            self.assertGreater(stats['total_events'], 1, "Should have processed multiple events")
            
            # 停止引擎
            self.risk_engine.stop()
            
            logger.info("✅ Risk control recovery test passed")
            
        except Exception as e:
            logger.error(f"❌ Risk control recovery test failed: {e}")
            raise


if __name__ == '__main__':
    unittest.main()
