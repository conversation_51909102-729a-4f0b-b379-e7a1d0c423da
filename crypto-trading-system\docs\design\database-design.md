# 数据库设计文档

## 1. 概述

本文档描述了虚拟货币量化交易系统的数据库设计，包括MySQL关系型数据库和InfluxDB时序数据库的表结构设计、索引设计和查询优化策略。

## 2. MySQL数据库设计

### 2.1 表设计

#### 2.1.1 订单表（t_order）

存储所有交易订单的基本信息。

```sql
CREATE TABLE `t_order` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_id` varchar(64) NOT NULL COMMENT '订单ID',
  `client_order_id` varchar(64) NOT NULL COMMENT '客户端订单ID',
  `symbol` varchar(20) NOT NULL COMMENT '交易对',
  `side` varchar(10) NOT NULL COMMENT '买卖方向(BUY/SELL)',
  `position_side` varchar(10) DEFAULT NULL COMMENT '持仓方向(LONG/SHORT/BOTH)',
  `type` varchar(20) NOT NULL COMMENT '订单类型',
  `price` decimal(20,8) DEFAULT NULL COMMENT '价格',
  `quantity` decimal(20,8) NOT NULL COMMENT '数量',
  `status` varchar(20) NOT NULL COMMENT '订单状态',
  `time_in_force` varchar(10) DEFAULT NULL COMMENT '有效方式',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `strategy_id` varchar(50) DEFAULT NULL COMMENT '策略ID',
  `avg_price` decimal(20,8) DEFAULT NULL COMMENT '成交均价',
  `executed_qty` decimal(20,8) DEFAULT '0.00000000' COMMENT '已成交数量',
  `cum_quote` decimal(20,8) DEFAULT '0.00000000' COMMENT '成交金额',
  `reduce_only` tinyint(1) DEFAULT '0' COMMENT '是否仅减仓',
  `close_position` tinyint(1) DEFAULT '0' COMMENT '是否平仓',
  `working_type` varchar(20) DEFAULT NULL COMMENT '条件价格类型',
  `is_isolated` tinyint(1) DEFAULT '0' COMMENT '是否逐仓',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_id` (`order_id`),
  KEY `idx_client_order_id` (`client_order_id`),
  KEY `idx_symbol` (`symbol`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_strategy_id` (`strategy_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单表';
```

#### 2.1.2 订单更新表（t_order_update）

记录订单状态的所有变更，用于追踪订单的完整生命周期。

```sql
CREATE TABLE `t_order_update` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_id` varchar(64) NOT NULL COMMENT '订单ID',
  `event_type` varchar(50) NOT NULL COMMENT '事件类型',
  `event_time` bigint NOT NULL COMMENT '事件时间',
  `transaction_time` bigint NOT NULL COMMENT '事务时间',
  `status` varchar(20) NOT NULL COMMENT '订单状态',
  `executed_qty` decimal(20,8) NOT NULL COMMENT '已成交数量',
  `avg_price` decimal(20,8) DEFAULT NULL COMMENT '成交均价',
  `cum_quote` decimal(20,8) DEFAULT NULL COMMENT '成交金额',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `raw_data` text COMMENT '原始数据',
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_event_time` (`event_time`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单更新表';
```

#### 2.1.3 交易表（t_trade）

存储所有已成交的交易记录。

```sql
CREATE TABLE `t_trade` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_id` varchar(64) NOT NULL COMMENT '订单ID',
  `trade_id` varchar(64) NOT NULL COMMENT '成交ID',
  `symbol` varchar(20) NOT NULL COMMENT '交易对',
  `side` varchar(10) NOT NULL COMMENT '买卖方向',
  `price` decimal(20,8) NOT NULL COMMENT '成交价格',
  `qty` decimal(20,8) NOT NULL COMMENT '成交数量',
  `commission` decimal(20,8) DEFAULT NULL COMMENT '手续费',
  `commission_asset` varchar(20) DEFAULT NULL COMMENT '手续费资产',
  `trade_time` bigint NOT NULL COMMENT '成交时间',
  `is_maker` tinyint(1) DEFAULT '0' COMMENT '是否是做市商',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `strategy_id` varchar(50) DEFAULT NULL COMMENT '策略ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_trade_id` (`trade_id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_symbol` (`symbol`),
  KEY `idx_trade_time` (`trade_time`),
  KEY `idx_strategy_id` (`strategy_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='成交表';
```

#### 2.1.4 策略表（t_strategy）

存储所有交易策略的配置信息。

```sql
CREATE TABLE `t_strategy` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `strategy_id` varchar(50) NOT NULL COMMENT '策略ID',
  `strategy_name` varchar(100) NOT NULL COMMENT '策略名称',
  `strategy_type` varchar(50) NOT NULL COMMENT '策略类型',
  `symbols` varchar(500) NOT NULL COMMENT '交易对列表',
  `parameters` text COMMENT '策略参数(JSON格式)',
  `status` varchar(20) NOT NULL COMMENT '策略状态(ACTIVE/INACTIVE)',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `creator` varchar(50) DEFAULT NULL COMMENT '创建者',
  `description` text COMMENT '策略描述',
  `language` varchar(10) NOT NULL DEFAULT 'JAVA' COMMENT '策略实现语言(JAVA/PYTHON)',
  `ml_model_path` varchar(255) DEFAULT NULL COMMENT '机器学习模型路径',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_strategy_id` (`strategy_id`),
  KEY `idx_strategy_type` (`strategy_type`),
  KEY `idx_status` (`status`),
  KEY `idx_language` (`language`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='策略表';
```

#### 2.1.5 策略执行记录表（t_strategy_execution）

记录策略的执行情况和结果。

```sql
CREATE TABLE `t_strategy_execution` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `strategy_id` varchar(50) NOT NULL COMMENT '策略ID',
  `execution_time` datetime NOT NULL COMMENT '执行时间',
  `execution_result` varchar(20) NOT NULL COMMENT '执行结果(SUCCESS/FAILURE)',
  `signal_type` varchar(20) DEFAULT NULL COMMENT '信号类型(BUY/SELL/NONE)',
  `signal_strength` decimal(10,4) DEFAULT NULL COMMENT '信号强度',
  `parameters_used` text COMMENT '使用的参数(JSON格式)',
  `error_message` text COMMENT '错误信息',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_strategy_id` (`strategy_id`),
  KEY `idx_execution_time` (`execution_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='策略执行记录表';
```

#### 2.1.6 账户快照表（t_account_snapshot）

定期记录账户的余额、持仓等信息，用于账户分析和风险管理。

```sql
CREATE TABLE `t_account_snapshot` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `total_balance` decimal(20,8) NOT NULL COMMENT '总余额(USDT)',
  `total_unrealized_profit` decimal(20,8) NOT NULL COMMENT '总未实现盈亏',
  `total_margin` decimal(20,8) NOT NULL COMMENT '总保证金',
  `available_balance` decimal(20,8) NOT NULL COMMENT '可用余额',
  `snapshot_time` datetime NOT NULL COMMENT '快照时间',
  `positions_json` text COMMENT '持仓信息(JSON格式)',
  `assets_json` text COMMENT '资产信息(JSON格式)',
  PRIMARY KEY (`id`),
  KEY `idx_snapshot_time` (`snapshot_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='账户快照表';
```

#### 2.1.7 持仓表（t_position）

记录当前持有的所有仓位信息。

```sql
CREATE TABLE `t_position` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `symbol` varchar(20) NOT NULL COMMENT '交易对',
  `position_side` varchar(10) NOT NULL COMMENT '持仓方向(LONG/SHORT/BOTH)',
  `position_amt` decimal(20,8) NOT NULL COMMENT '持仓数量',
  `entry_price` decimal(20,8) NOT NULL COMMENT '开仓均价',
  `mark_price` decimal(20,8) NOT NULL COMMENT '标记价格',
  `unrealized_profit` decimal(20,8) NOT NULL COMMENT '未实现盈亏',
  `leverage` int NOT NULL COMMENT '杠杆倍数',
  `is_isolated` tinyint(1) NOT NULL COMMENT '是否逐仓',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `strategy_id` varchar(50) DEFAULT NULL COMMENT '关联策略ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_symbol_position_side` (`symbol`, `position_side`),
  KEY `idx_update_time` (`update_time`),
  KEY `idx_strategy_id` (`strategy_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='持仓表';
```

#### 2.1.8 系统配置表（t_system_config）

存储系统的各项配置参数。

```sql
CREATE TABLE `t_system_config` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `config_key` varchar(100) NOT NULL COMMENT '配置键',
  `config_value` text COMMENT '配置值',
  `description` varchar(255) DEFAULT NULL COMMENT '配置描述',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `creator` varchar(50) DEFAULT NULL COMMENT '创建者',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_key` (`config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';
```

#### 2.1.9 系统日志表（t_system_log）

记录系统的关键操作日志，用于审计和问题排查。

```sql
CREATE TABLE `t_system_log` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `log_level` varchar(10) NOT NULL COMMENT '日志级别',
  `log_content` text NOT NULL COMMENT '日志内容',
  `operation_type` varchar(50) DEFAULT NULL COMMENT '操作类型',
  `operator` varchar(50) DEFAULT NULL COMMENT '操作者',
  `operation_time` datetime NOT NULL COMMENT '操作时间',
  `ip_address` varchar(50) DEFAULT NULL COMMENT 'IP地址',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_operation_time` (`operation_time`),
  KEY `idx_log_level` (`log_level`),
  KEY `idx_operation_type` (`operation_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统日志表';
```

#### 2.1.10 机器学习模型表（t_ml_model）

存储机器学习模型的元数据信息。

```sql
CREATE TABLE `t_ml_model` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `model_id` varchar(50) NOT NULL COMMENT '模型ID',
  `model_name` varchar(100) NOT NULL COMMENT '模型名称',
  `model_type` varchar(50) NOT NULL COMMENT '模型类型(LPPL/SENTIMENT/TREND/ENSEMBLE)',
  `model_version` varchar(20) NOT NULL COMMENT '模型版本',
  `model_path` varchar(255) NOT NULL COMMENT '模型文件路径',
  `parameters` text COMMENT '模型参数(JSON格式)',
  `metrics` text COMMENT '模型评估指标(JSON格式)',
  `training_data_info` text COMMENT '训练数据信息',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `creator` varchar(50) DEFAULT NULL COMMENT '创建者',
  `description` text COMMENT '模型描述',
  `status` varchar(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '模型状态(ACTIVE/INACTIVE)',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_model_id` (`model_id`),
  KEY `idx_model_type` (`model_type`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='机器学习模型表';
```

#### 2.1.11 模型训练记录表（t_model_training）

记录机器学习模型的训练历史。

```sql
CREATE TABLE `t_model_training` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `model_id` varchar(50) NOT NULL COMMENT '模型ID',
  `training_id` varchar(50) NOT NULL COMMENT '训练ID',
  `start_time` datetime NOT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `status` varchar(20) NOT NULL COMMENT '状态(PENDING/RUNNING/COMPLETED/FAILED)',
  `parameters` text COMMENT '训练参数(JSON格式)',
  `metrics` text COMMENT '训练指标(JSON格式)',
  `error_message` text COMMENT '错误信息',
  `training_data_range` varchar(100) DEFAULT NULL COMMENT '训练数据时间范围',
  `validation_data_range` varchar(100) DEFAULT NULL COMMENT '验证数据时间范围',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_training_id` (`training_id`),
  KEY `idx_model_id` (`model_id`),
  KEY `idx_status` (`status`),
  KEY `idx_start_time` (`start_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='模型训练记录表';
```

#### 2.1.12 特征存储表（t_feature_store）

存储预计算的特征数据，用于机器学习模型训练和推理。

```sql
CREATE TABLE `t_feature_store` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `feature_id` varchar(50) NOT NULL COMMENT '特征ID',
  `symbol` varchar(20) NOT NULL COMMENT '交易对',
  `timeframe` varchar(10) NOT NULL COMMENT '时间框架(1m/5m/15m/1h/4h/1d)',
  `feature_type` varchar(50) NOT NULL COMMENT '特征类型',
  `feature_data` longtext NOT NULL COMMENT '特征数据(JSON格式)',
  `start_time` datetime NOT NULL COMMENT '开始时间',
  `end_time` datetime NOT NULL COMMENT '结束时间',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_feature_id` (`feature_id`),
  KEY `idx_symbol` (`symbol`),
  KEY `idx_timeframe` (`timeframe`),
  KEY `idx_feature_type` (`feature_type`),
  KEY `idx_time_range` (`start_time`, `end_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='特征存储表';
```

#### 2.1.13 模型预测记录表（t_model_prediction）

记录模型的预测结果和相关信息。

```sql
CREATE TABLE `t_model_prediction` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `prediction_id` varchar(50) NOT NULL COMMENT '预测ID',
  `model_id` varchar(50) NOT NULL COMMENT '模型ID',
  `symbol` varchar(20) NOT NULL COMMENT '交易对',
  `timeframe` varchar(10) NOT NULL COMMENT '时间框架',
  `prediction_time` datetime NOT NULL COMMENT '预测时间',
  `prediction_target` varchar(50) NOT NULL COMMENT '预测目标',
  `prediction_value` decimal(20,8) NOT NULL COMMENT '预测值',
  `confidence` decimal(10,4) NOT NULL COMMENT '置信度',
  `features_used` text COMMENT '使用的特征(JSON格式)',
  `actual_value` decimal(20,8) DEFAULT NULL COMMENT '实际值',
  `error` decimal(20,8) DEFAULT NULL COMMENT '误差',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_prediction_id` (`prediction_id`),
  KEY `idx_model_id` (`model_id`),
  KEY `idx_symbol` (`symbol`),
  KEY `idx_prediction_time` (`prediction_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='模型预测记录表';
```

### 2.2 索引设计原则

1. **主键索引**：每个表都设置了自增主键作为聚集索引
2. **唯一索引**：对于需要保证唯一性的字段，如订单ID、交易ID等，建立唯一索引
3. **普通索引**：对常用于查询条件的字段，如交易对、创建时间等建立普通索引
4. **组合索引**：对于经常一起使用的查询条件，建立组合索引
5. **覆盖索引**：对于频繁查询的字段，尽量包含在索引中，减少回表操作

### 2.3 数据分区策略

对于大表（如订单表、交易表、日志表等），采用按时间范围分区的策略：

```sql
-- 以订单表为例
ALTER TABLE t_order PARTITION BY RANGE (TO_DAYS(create_time)) (
    PARTITION p202301 VALUES LESS THAN (TO_DAYS('2023-02-01')),
    PARTITION p202302 VALUES LESS THAN (TO_DAYS('2023-03-01')),
    PARTITION p202303 VALUES LESS THAN (TO_DAYS('2023-04-01')),
    -- 更多分区...
    PARTITION pmax VALUES LESS THAN MAXVALUE
);
```

### 2.4 数据清理策略

为了防止数据量过大影响性能，设计了以下数据清理策略：

1. **归档策略**：历史数据（如90天前的订单记录）定期归档到历史表
2. **删除策略**：非关键数据（如调试日志）定期删除
3. **汇总策略**：将历史详细数据汇总为统计数据，保留统计结果而删除原始数据

## 3. InfluxDB设计

### 3.1 数据库和保留策略

```sql
-- 创建数据库
CREATE DATABASE crypto_trading;

-- 创建保留策略
CREATE RETENTION POLICY "realtime" ON "crypto_trading" DURATION 7d REPLICATION 1 DEFAULT;
CREATE RETENTION POLICY "short_term" ON "crypto_trading" DURATION 30d REPLICATION 1;
CREATE RETENTION POLICY "long_term" ON "crypto_trading" DURATION INF REPLICATION 1;
```

### 3.2 测量（Measurement）设计

#### 3.2.1 K线数据（kline）

```
name: kline
tags: symbol, interval
fields: open, high, low, close, volume, quote_volume, trades, taker_buy_volume, taker_buy_quote_volume
time: timestamp
```

#### 3.2.2 深度数据（depth）

```
name: depth
tags: symbol
fields: bids_json, asks_json, update_id
time: timestamp
```

#### 3.2.3 交易数据（trade）

```
name: trade
tags: symbol, side, maker
fields: price, quantity, quote_quantity, trade_id
time: timestamp
```

#### 3.2.4 系统指标（system_metrics）

```
name: system_metrics
tags: component, host
fields: cpu_usage, memory_usage, thread_count, gc_time, request_count, error_count, latency
time: timestamp
```

#### 3.2.5 API指标（api_metrics）

```
name: api_metrics
tags: endpoint, method
fields: request_count, success_count, error_count, avg_response_time, max_response_time, min_response_time
time: timestamp
```

#### 3.2.6 策略指标（strategy_metrics）

```
name: strategy_metrics
tags: strategy_id, symbol
fields: signal_value, position_size, unrealized_pnl, realized_pnl
time: timestamp
```

#### 3.2.7 模型性能指标（model_metrics）

```
name: model_metrics
tags: model_id, model_type, symbol, timeframe
fields: accuracy, precision, recall, f1_score, mse, mae, prediction_time, confidence
time: timestamp
```

#### 3.2.8 模型预测结果（model_predictions）

```
name: model_predictions
tags: model_id, model_type, symbol, timeframe, prediction_target
fields: prediction_value, confidence, feature_importance_json
time: timestamp
```

#### 3.2.9 特征数据（features）

```
name: features
tags: symbol, timeframe, feature_type
fields: feature_value, is_normalized, window_size
time: timestamp
```

#### 3.2.10 模型信号（ml_signals）

```
name: ml_signals
tags: model_id, strategy_id, symbol, timeframe, signal_type
fields: signal_strength, confidence, feature_contribution_json, ensemble_weights_json
time: timestamp
```

### 3.3 连续查询（Continuous Query）

为了优化存储和查询性能，设计了以下连续查询：

#### 3.3.1 K线数据聚合

```sql
CREATE CONTINUOUS QUERY "cq_kline_1h" ON "crypto_trading"
BEGIN
  SELECT mean(close) AS close, max(high) AS high, min(low) AS low, first(open) AS open, sum(volume) AS volume
  INTO "crypto_trading"."short_term"."kline_1h"
  FROM "crypto_trading"."realtime"."kline"
  GROUP BY time(1h), symbol
END
```

#### 3.3.2 系统指标聚合

```sql
CREATE CONTINUOUS QUERY "cq_system_metrics_1h" ON "crypto_trading"
BEGIN
  SELECT mean(cpu_usage) AS cpu_usage, mean(memory_usage) AS memory_usage, sum(request_count) AS request_count, sum(error_count) AS error_count
  INTO "crypto_trading"."short_term"."system_metrics_1h"
  FROM "crypto_trading"."realtime"."system_metrics"
  GROUP BY time(1h), component, host
END
```

#### 3.3.3 模型指标聚合

```sql
CREATE CONTINUOUS QUERY "cq_model_metrics_1d" ON "crypto_trading"
BEGIN
  SELECT mean(accuracy) AS accuracy, mean(precision) AS precision, mean(recall) AS recall, 
         mean(f1_score) AS f1_score, mean(mse) AS mse, mean(mae) AS mae, 
         mean(confidence) AS confidence
  INTO "crypto_trading"."short_term"."model_metrics_1d"
  FROM "crypto_trading"."realtime"."model_metrics"
  GROUP BY time(1d), model_id, model_type
END
```

#### 3.3.4 信号强度聚合

```sql
CREATE CONTINUOUS QUERY "cq_ml_signals_1h" ON "crypto_trading"
BEGIN
  SELECT mean(signal_strength) AS signal_strength, mean(confidence) AS confidence
  INTO "crypto_trading"."short_term"."ml_signals_1h"
  FROM "crypto_trading"."realtime"."ml_signals"
  GROUP BY time(1h), model_id, strategy_id, symbol, signal_type
END
```

### 3.4 保留策略与降采样

为了平衡存储空间和查询性能，设计了以下降采样策略：

1. **实时数据**：保留最近7天的原始数据（1分钟粒度）
2. **短期数据**：保留最近30天的小时级聚合数据
3. **长期数据**：保留永久的天级聚合数据

## 4. 数据同步与备份策略

### 4.1 数据同步策略

1. **实时同步**：关键业务数据（如订单、交易）实时写入MySQL
2. **批量同步**：非关键数据（如市场数据）批量写入InfluxDB
3. **异步写入**：日志、监控数据等通过消息队列异步写入数据库

### 4.2 数据备份策略

1. **MySQL备份**：每日全量备份 + 实时binlog备份
2. **InfluxDB备份**：每日全量备份
3. **备份验证**：定期进行备份恢复测试
4. **异地备份**：重要数据异地备份

## 5. 查询优化策略

### 5.1 MySQL查询优化

1. **索引优化**：根据查询模式优化索引设计
2. **查询重写**：优化复杂查询的SQL语句
3. **分页优化**：使用"延迟关联"技术优化分页查询
4. **读写分离**：将读操作分流到备库

### 5.2 InfluxDB查询优化

1. **时间范围限制**：始终在查询中指定时间范围
2. **标签过滤**：优先使用标签过滤而非字段过滤
3. **预聚合**：使用连续查询预先聚合数据
4. **降采样**：根据查询时间跨度选择合适粒度的数据

### 5.3 Python数据访问优化

为了优化Python机器学习策略模块的数据访问性能，采用以下策略：

1. **连接池优化**：使用SQLAlchemy连接池管理MySQL连接
2. **批量操作**：使用批量读写代替单条操作
3. **异步I/O**：使用异步I/O库进行数据库操作
4. **数据缓存**：实现本地缓存减少数据库访问
5. **数据预加载**：预加载常用数据减少实时查询

## 6. 数据访问层设计

### 6.1 DAO层设计

系统使用MyBatis-Plus作为ORM框架，为每个表设计对应的DAO接口：

```java
public interface OrderDao extends BaseMapper<OrderEntity> {
    // 自定义查询方法
    List<OrderEntity> findBySymbolAndStatus(String symbol, String status);
    
    // 批量插入
    int batchInsert(List<OrderEntity> orders);
    
    // 复杂统计查询
    List<OrderStatisticsVO> getOrderStatistics(Date startTime, Date endTime, String symbol);
}
```

### 6.2 XML配置方式

所有复杂查询和自定义SQL使用XML配置方式：

```xml
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trading.dao.OrderDao">
    
    <select id="findBySymbolAndStatus" resultType="com.trading.entity.OrderEntity">
        SELECT * FROM t_order 
        WHERE symbol = #{symbol} AND status = #{status}
        ORDER BY create_time DESC
    </select>
    
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO t_order 
        (order_id, client_order_id, symbol, side, position_side, type, price, 
        quantity, status, time_in_force, create_time, update_time, strategy_id)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.orderId}, #{item.clientOrderId}, #{item.symbol}, #{item.side}, 
            #{item.positionSide}, #{item.type}, #{item.price}, #{item.quantity}, 
            #{item.status}, #{item.timeInForce}, #{item.createTime}, #{item.updateTime}, 
            #{item.strategyId})
        </foreach>
    </insert>
    
    <select id="getOrderStatistics" resultType="com.trading.vo.OrderStatisticsVO">
        SELECT 
            symbol, 
            COUNT(*) AS total_orders,
            SUM(CASE WHEN status = 'FILLED' THEN 1 ELSE 0 END) AS filled_orders,
            SUM(CASE WHEN status = 'CANCELED' THEN 1 ELSE 0 END) AS canceled_orders,
            SUM(CASE WHEN status = 'REJECTED' THEN 1 ELSE 0 END) AS rejected_orders,
            SUM(executed_qty) AS total_executed_qty,
            SUM(executed_qty * avg_price) AS total_executed_value
        FROM 
            t_order
        WHERE 
            create_time BETWEEN #{startTime} AND #{endTime}
            <if test="symbol != null and symbol != ''">
                AND symbol = #{symbol}
            </if>
        GROUP BY 
            symbol
    </select>
    
</mapper>
```

### 6.3 分页查询

使用MyBatis-Plus提供的分页插件：

```java
@Configuration
public class MybatisPlusConfig {
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.MYSQL));
        return interceptor;
    }
}
```

服务层代码：

```java
public IPage<OrderEntity> getOrderPage(OrderQueryParam param) {
    Page<OrderEntity> page = new Page<>(param.getCurrentPage(), param.getPageSize());
    
    LambdaQueryWrapper<OrderEntity> queryWrapper = new LambdaQueryWrapper<>();
    if (StringUtils.hasText(param.getSymbol())) {
        queryWrapper.eq(OrderEntity::getSymbol, param.getSymbol());
    }
    if (StringUtils.hasText(param.getStatus())) {
        queryWrapper.eq(OrderEntity::getStatus, param.getStatus());
    }
    if (param.getStartTime() != null) {
        queryWrapper.ge(OrderEntity::getCreateTime, param.getStartTime());
    }
    if (param.getEndTime() != null) {
        queryWrapper.le(OrderEntity::getCreateTime, param.getEndTime());
    }
    
    queryWrapper.orderByDesc(OrderEntity::getCreateTime);
    
    return orderDao.selectPage(page, queryWrapper);
}
```

### 6.4 Python数据访问层

Python机器学习策略模块使用专门的数据访问层进行数据操作：

```python
# DAO基类
class BaseDAO:
    def __init__(self, engine):
        self.engine = engine
        self.metadata = MetaData()
        self.session_factory = sessionmaker(bind=engine)
        
    def get_session(self):
        return self.session_factory()

# 模型DAO
class ModelDAO(BaseDAO):
    def __init__(self, engine):
        super().__init__(engine)
        self.model_table = Table('t_ml_model', self.metadata, autoload=True, autoload_with=engine)
        
    def get_active_models(self, model_type=None):
        """获取所有活跃模型"""
        with self.get_session() as session:
            query = select([self.model_table]).where(self.model_table.c.status == 'ACTIVE')
            if model_type:
                query = query.where(self.model_table.c.model_type == model_type)
            return session.execute(query).fetchall()
            
    def update_model_metrics(self, model_id, metrics):
        """更新模型评估指标"""
        with self.get_session() as session:
            try:
                session.execute(
                    update(self.model_table)
                    .where(self.model_table.c.model_id == model_id)
                    .values(metrics=json.dumps(metrics), update_time=datetime.now())
                )
                session.commit()
                return True
            except Exception as e:
                session.rollback()
                logger.error(f"Update model metrics failed: {e}")
                return False

# InfluxDB访问层
class InfluxDBClient:
    def __init__(self, url, token, org, bucket):
        self.client = influxdb_client.InfluxDBClient(url=url, token=token, org=org)
        self.write_api = self.client.write_api(write_options=SYNCHRONOUS)
        self.query_api = self.client.query_api()
        self.bucket = bucket
        
    def write_prediction(self, model_id, model_type, symbol, timeframe, prediction_target, 
                        prediction_value, confidence, feature_importance=None):
        """写入预测结果"""
        point = Point("model_predictions") \
            .tag("model_id", model_id) \
            .tag("model_type", model_type) \
            .tag("symbol", symbol) \
            .tag("timeframe", timeframe) \
            .tag("prediction_target", prediction_target) \
            .field("prediction_value", float(prediction_value)) \
            .field("confidence", float(confidence))
            
        if feature_importance:
            point = point.field("feature_importance_json", json.dumps(feature_importance))
            
        self.write_api.write(bucket=self.bucket, record=point)
        
    def get_recent_klines(self, symbol, interval, limit=100):
        """获取最近的K线数据"""
        query = f'''
        from(bucket: "{self.bucket}")
            |> range(start: -30d)
            |> filter(fn: (r) => r._measurement == "kline")
            |> filter(fn: (r) => r.symbol == "{symbol}")
            |> filter(fn: (r) => r.interval == "{interval}")
            |> pivot(rowKey:["_time"], columnKey: ["_field"], valueColumn: "_value")
            |> sort(columns: ["_time"], desc: true)
            |> limit(n: {limit})
        '''
        return self.query_api.query_data_frame(query=query)
```

## 7. 数据安全策略

### 7.1 数据加密

1. **敏感数据加密**：API密钥等敏感信息使用AES加密存储
2. **传输加密**：所有数据传输采用HTTPS/TLS加密
3. **配置文件加密**：使用Jasypt对配置文件中的敏感信息加密

### 7.2 数据访问控制

1. **最小权限原则**：数据库账户仅分配必要的权限
2. **访问审计**：记录所有数据库访问操作
3. **IP白名单**：限制数据库访问来源

### 7.3 数据完整性保护

1. **事务控制**：确保关键数据操作的原子性
2. **数据校验**：业务层和数据库层双重校验
3. **乐观锁**：使用版本号控制并发修改

## 8. 总结

本数据库设计方案充分考虑了量化交易系统的特点，通过合理的表结构设计、索引优化、分区策略和查询优化，满足了系统高性能、可靠性和可维护性的需求。MySQL用于存储关系型数据，InfluxDB用于存储时序数据，二者结合提供了全面的数据存储和分析能力。特别是针对Python机器学习策略模块，设计了专门的表结构和数据访问层，确保机器学习模型训练、评估和预测的数据需求得到满足。

设计遵循了数据库最佳实践，包括规范化、适当的冗余、完善的索引和良好的查询性能。同时，通过数据同步、备份和安全策略，确保了系统数据的安全性和可靠性。