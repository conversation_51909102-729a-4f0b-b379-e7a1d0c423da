#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
优化同步器核心方法

包含OptimizedTimeframeSync类的核心同步方法实现。
分离为独立文件以避免单文件过大问题。
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any, Union
from datetime import datetime, timedelta
import time
import hashlib
from .optimized_timeframe_sync import TimeframeType, SyncStrategy, PerformanceMetrics


class OptimizedSyncCore:
    """优化同步器核心方法类"""
    
    @staticmethod
    def add_data_point(sync_instance, symbol: str, timeframe: str, data: Dict[str, Any]) -> bool:
        """
        添加数据点到指定时间框架
        
        Args:
            sync_instance: OptimizedTimeframeSync实例
            symbol: 交易对符号
            timeframe: 时间框架
            data: 数据字典
            
        Returns:
            是否成功添加
        """
        try:
            # 验证时间框架
            tf_type = TimeframeType(timeframe)
            if tf_type not in sync_instance.enabled_timeframes:
                sync_instance.logger.warning(f"时间框架未启用: {timeframe}")
                return False
            
            # 验证数据完整性
            required_fields = ['timestamp', 'open', 'high', 'low', 'close', 'volume']
            if not all(field in data for field in required_fields):
                sync_instance.logger.warning(f"数据缺少必要字段: {required_fields}")
                return False
            
            # 标准化时间戳
            normalized_ts = OptimizedSyncCore._normalize_timestamp(
                data['timestamp'], sync_instance.timeframe_specs[tf_type].minutes
            )
            
            # 创建优化的数据点
            data_point = {
                'ts': normalized_ts,
                'o': float(data['open']),
                'h': float(data['high']),
                'l': float(data['low']),
                'c': float(data['close']),
                'v': float(data['volume'])
            }
            
            # 添加到存储
            with sync_instance._lock:
                data_deque = sync_instance._data_store[symbol][tf_type]
                
                # 检查重复时间戳
                if data_deque and data_deque[-1]['ts'] == normalized_ts:
                    data_deque[-1] = data_point  # 更新最新数据
                else:
                    data_deque.append(data_point)
                
                # 维护缓存大小
                max_size = sync_instance.timeframe_specs[tf_type].max_cache_size
                while len(data_deque) > max_size:
                    data_deque.popleft()
                
                # 清除相关同步缓存
                OptimizedSyncCore._invalidate_sync_cache(sync_instance, symbol)
            
            return True
            
        except Exception as e:
            sync_instance.logger.error(f"添加数据点失败: {e}")
            return False
    
    @staticmethod
    def _normalize_timestamp(timestamp: Union[int, float, str], minutes: int) -> int:
        """
        标准化时间戳到时间框架边界
        
        Args:
            timestamp: 原始时间戳
            minutes: 时间框架分钟数
            
        Returns:
            标准化后的时间戳（毫秒）
        """
        try:
            # 转换为毫秒时间戳
            if isinstance(timestamp, str):
                dt = pd.to_datetime(timestamp)
                ts_ms = int(dt.timestamp() * 1000)
            elif isinstance(timestamp, (int, float)):
                ts_ms = int(timestamp * 1000) if timestamp < 1e12 else int(timestamp)
            else:
                raise ValueError(f"不支持的时间戳格式: {type(timestamp)}")
            
            # 对齐到时间框架边界
            dt = pd.to_datetime(ts_ms, unit='ms')
            
            if minutes < 60:  # 分钟级别
                aligned_dt = dt.replace(second=0, microsecond=0)
                aligned_dt = aligned_dt.replace(minute=(aligned_dt.minute // minutes) * minutes)
            elif minutes < 1440:  # 小时级别
                hours = minutes // 60
                aligned_dt = dt.replace(minute=0, second=0, microsecond=0)
                aligned_dt = aligned_dt.replace(hour=(aligned_dt.hour // hours) * hours)
            else:  # 日级别
                aligned_dt = dt.replace(hour=0, minute=0, second=0, microsecond=0)
            
            return int(aligned_dt.timestamp() * 1000)
            
        except Exception:
            return int(timestamp) if isinstance(timestamp, (int, float)) else int(time.time() * 1000)
    
    @staticmethod
    def sync_timeframes(sync_instance, symbol: str, target_timeframe: Optional[str] = None) -> Optional[pd.DataFrame]:
        """
        同步多时间框架数据
        
        Args:
            sync_instance: OptimizedTimeframeSync实例
            symbol: 交易对符号
            target_timeframe: 目标时间框架
            
        Returns:
            同步后的DataFrame
        """
        start_time = time.time()
        
        try:
            # 确定目标时间框架
            if target_timeframe is None:
                target_tf_type = min(sync_instance.enabled_timeframes.keys(),
                                   key=lambda x: sync_instance.timeframe_specs[x].minutes)
            else:
                target_tf_type = TimeframeType(target_timeframe)
            
            # 检查缓存
            cache_key = OptimizedSyncCore._generate_cache_key(sync_instance, symbol, target_tf_type)
            if cache_key in sync_instance._sync_cache:
                cached_data, cache_time = sync_instance._sync_cache[cache_key]
                if time.time() - cache_time < sync_instance.sync_config.cache_ttl_seconds:
                    sync_instance._stats['cache_hits'] += 1
                    sync_instance.metrics.cache_hit_ratio = (
                        sync_instance._stats['cache_hits'] / max(1, sync_instance._stats['total_syncs'])
                    )
                    return cached_data.copy()
            
            # 收集数据
            timeframe_data = OptimizedSyncCore._collect_timeframe_data(sync_instance, symbol)
            if not timeframe_data:
                return None
            
            # 执行同步
            synchronized_df = OptimizedSyncCore._perform_sync(
                sync_instance, timeframe_data, target_tf_type
            )
            
            if synchronized_df is not None:
                # 缓存结果
                sync_instance._sync_cache[cache_key] = (synchronized_df.copy(), time.time())
                
                # 更新性能指标
                sync_latency = (time.time() - start_time) * 1000
                sync_instance.metrics.sync_latency_ms = sync_latency
                sync_instance._stats['successful_syncs'] += 1
                
                # 计算数据质量分数
                quality_score = OptimizedSyncCore._calculate_quality_score(synchronized_df)
                sync_instance.metrics.data_quality_score = quality_score
            
            sync_instance._stats['total_syncs'] += 1
            sync_instance.metrics.last_update = datetime.now()
            
            return synchronized_df
            
        except Exception as e:
            sync_instance.logger.error(f"时间框架同步失败: {e}")
            return None
    
    @staticmethod
    def _collect_timeframe_data(sync_instance, symbol: str) -> Dict[TimeframeType, pd.DataFrame]:
        """收集各时间框架数据"""
        timeframe_data = {}
        
        with sync_instance._lock:
            for tf_type in sync_instance.enabled_timeframes.keys():
                data_deque = sync_instance._data_store[symbol][tf_type]
                
                if len(data_deque) >= 10:  # 最少10个数据点
                    # 转换为DataFrame
                    data_list = list(data_deque)
                    df = pd.DataFrame(data_list)
                    
                    # 设置时间索引
                    df['datetime'] = pd.to_datetime(df['ts'], unit='ms')
                    df.set_index('datetime', inplace=True)
                    df.sort_index(inplace=True)
                    
                    # 重命名列
                    df.rename(columns={'o': 'open', 'h': 'high', 'l': 'low', 
                                     'c': 'close', 'v': 'volume'}, inplace=True)
                    
                    timeframe_data[tf_type] = df
        
        return timeframe_data
    
    @staticmethod
    def _perform_sync(sync_instance, timeframe_data: Dict[TimeframeType, pd.DataFrame], 
                     target_tf_type: TimeframeType) -> Optional[pd.DataFrame]:
        """执行数据同步"""
        try:
            if target_tf_type not in timeframe_data:
                sync_instance.logger.error(f"目标时间框架数据不存在: {target_tf_type}")
                return None
            
            # 以目标时间框架为基准
            base_df = timeframe_data[target_tf_type].copy()
            base_df = base_df.add_suffix(f'_{target_tf_type.value}')
            
            # 同步其他时间框架
            for tf_type, tf_df in timeframe_data.items():
                if tf_type == target_tf_type:
                    continue
                
                # 重采样到目标时间框架
                resampled_df = OptimizedSyncCore._resample_data(
                    sync_instance, tf_df, target_tf_type, tf_type
                )
                
                if resampled_df is not None:
                    resampled_df = resampled_df.add_suffix(f'_{tf_type.value}')
                    base_df = base_df.join(resampled_df, how='left')
            
            # 处理缺失值
            base_df = OptimizedSyncCore._handle_missing_values(sync_instance, base_df)
            
            return base_df
            
        except Exception as e:
            sync_instance.logger.error(f"数据同步执行失败: {e}")
            return None
    
    @staticmethod
    def _resample_data(sync_instance, source_df: pd.DataFrame, 
                      target_tf_type: TimeframeType, source_tf_type: TimeframeType) -> Optional[pd.DataFrame]:
        """重采样数据到目标时间框架"""
        try:
            target_minutes = sync_instance.timeframe_specs[target_tf_type].minutes
            source_minutes = sync_instance.timeframe_specs[source_tf_type].minutes
            
            rule = f'{target_minutes}T'
            
            if target_minutes <= source_minutes:
                # 目标时间框架更细，使用前向填充
                resampled = source_df.resample(rule, label='right', closed='right').ffill()
            else:
                # 目标时间框架更粗，使用OHLC聚合
                agg_dict = {
                    'open': 'first',
                    'high': 'max',
                    'low': 'min',
                    'close': 'last',
                    'volume': 'sum'
                }
                resampled = source_df.resample(rule, label='right', closed='right').agg(agg_dict)
            
            return resampled.dropna()
            
        except Exception as e:
            sync_instance.logger.error(f"数据重采样失败: {e}")
            return None
    
    @staticmethod
    def _handle_missing_values(sync_instance, df: pd.DataFrame) -> pd.DataFrame:
        """处理缺失值"""
        try:
            strategy = sync_instance.sync_config.strategy
            
            if strategy == SyncStrategy.FORWARD_FILL:
                df = df.fillna(method='ffill')
            elif strategy == SyncStrategy.BACKWARD_FILL:
                df = df.fillna(method='bfill')
            elif strategy == SyncStrategy.LINEAR_INTERPOLATION:
                df = df.interpolate(method='linear')
            elif strategy == SyncStrategy.NEAREST_NEIGHBOR:
                df = df.fillna(method='ffill').fillna(method='bfill')
            
            return df.dropna()
            
        except Exception as e:
            sync_instance.logger.error(f"缺失值处理失败: {e}")
            return df
    
    @staticmethod
    def _generate_cache_key(sync_instance, symbol: str, target_tf_type: TimeframeType) -> str:
        """生成缓存键"""
        try:
            # 收集数据特征用于缓存键
            data_signatures = []
            
            with sync_instance._lock:
                for tf_type in sync_instance.enabled_timeframes.keys():
                    data_deque = sync_instance._data_store[symbol][tf_type]
                    if data_deque:
                        # 使用最后几个数据点的哈希
                        last_points = list(data_deque)[-5:]  # 最后5个点
                        signature = hashlib.md5(str(last_points).encode()).hexdigest()[:8]
                        data_signatures.append(f"{tf_type.value}:{signature}")
            
            cache_key = f"{symbol}_{target_tf_type.value}_{'_'.join(data_signatures)}"
            return hashlib.md5(cache_key.encode()).hexdigest()
            
        except Exception:
            return f"{symbol}_{target_tf_type.value}_{int(time.time())}"
    
    @staticmethod
    def _invalidate_sync_cache(sync_instance, symbol: str):
        """使同步缓存失效"""
        try:
            keys_to_remove = []
            for key in sync_instance._sync_cache.keys():
                if symbol in str(key):
                    keys_to_remove.append(key)
            
            for key in keys_to_remove:
                if key in sync_instance._sync_cache:
                    del sync_instance._sync_cache[key]
                    
        except Exception as e:
            sync_instance.logger.error(f"缓存失效处理失败: {e}")
    
    @staticmethod
    def _calculate_quality_score(df: pd.DataFrame) -> float:
        """计算数据质量分数"""
        try:
            if df is None or df.empty:
                return 0.0
            
            # 计算缺失值比例
            missing_ratio = df.isnull().sum().sum() / (len(df) * len(df.columns))
            
            # 计算数据连续性
            time_gaps = df.index.to_series().diff().dt.total_seconds()
            max_gap = time_gaps.max() if len(time_gaps) > 1 else 0
            gap_score = 1.0 if max_gap < 300 else max(0.0, 1.0 - max_gap / 3600)  # 1小时内为满分
            
            # 综合质量分数
            quality_score = (1.0 - missing_ratio) * 0.7 + gap_score * 0.3
            
            return min(1.0, max(0.0, quality_score))
            
        except Exception:
            return 0.5  # 默认中等质量