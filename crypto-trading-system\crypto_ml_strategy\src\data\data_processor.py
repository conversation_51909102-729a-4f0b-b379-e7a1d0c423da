#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据处理模块，负责处理和转换市场数据。
"""

import logging
from typing import Dict, List, Tuple, Optional, Any, Union
import numpy as np
import pandas as pd
from datetime import datetime

from core.config import Config


class DataProcessor:
    """
    数据处理类，负责处理和转换市场数据，包括K线数据、深度数据和交易数据。
    支持多时间周期数据整合和特征预处理。
    """

    def __init__(self, config: Config):
        """
        初始化数据处理器。

        Args:
            config: 配置对象
        """
        self.logger = logging.getLogger('cryptoMlStrategy.data_processor')
        self.config = config
        self.timeframes = config.get_list('strategy', 'timeframes')
        self.symbols = config.get_list('strategy', 'symbols')
        
        # 数据缓存
        self._kline_cache = {}  # 格式: {symbol: {timeframe: pd.DataFrame}}
        self._depth_cache = {}  # 格式: {symbol: pd.DataFrame}
        self._trade_cache = {}  # 格式: {symbol: pd.DataFrame}
        
        # 初始化缓存
        for symbol in self.symbols:
            self._kline_cache[symbol] = {}
            for timeframe in self.timeframes:
                self._kline_cache[symbol][timeframe] = pd.DataFrame()
            self._depth_cache[symbol] = pd.DataFrame()
            self._trade_cache[symbol] = pd.DataFrame()
        
        self.logger.info(f"数据处理器初始化完成，支持的交易对: {self.symbols}，时间周期: {self.timeframes}")

    def process_kline_data(self, message: Dict[str, Any]) -> Optional[pd.DataFrame]:
        """
        处理K线数据消息。

        Args:
            message: Kafka消息，包含K线数据

        Returns:
            处理后的K线数据DataFrame，如果数据无效则返回None
        """
        try:
            data = message.get('data', {})
            symbol = data.get('symbol')
            timeframe = data.get('interval')
            
            if not symbol or not timeframe or symbol not in self.symbols or timeframe not in self.timeframes:
                return None
            
            # 转换为DataFrame格式
            kline = {
                'open_time': data.get('openTime', 0),
                'open': float(data.get('open', 0)),
                'high': float(data.get('high', 0)),
                'low': float(data.get('low', 0)),
                'close': float(data.get('close', 0)),
                'volume': float(data.get('volume', 0)),
                'close_time': data.get('closeTime', 0),
                'quote_volume': float(data.get('quoteVolume', 0)),
                'trades': int(data.get('trades', 0)),
                'taker_buy_base_volume': float(data.get('takerBuyBaseVolume', 0)),
                'taker_buy_quote_volume': float(data.get('takerBuyQuoteVolume', 0)),
                'datetime': datetime.fromtimestamp(data.get('openTime', 0) / 1000)
            }
            
            # 更新缓存
            df = pd.DataFrame([kline])
            df.set_index('open_time', inplace=True)
            
            if self._kline_cache[symbol][timeframe].empty:
                self._kline_cache[symbol][timeframe] = df
            else:
                # 如果已存在该时间戳的数据，则更新它，否则添加新行
                self._kline_cache[symbol][timeframe].loc[df.index] = df
                
            # 保持缓存大小适中，避免内存溢出
            max_cache_size = 1000  # 最多保存1000条K线
            if len(self._kline_cache[symbol][timeframe]) > max_cache_size:
                self._kline_cache[symbol][timeframe] = self._kline_cache[symbol][timeframe].iloc[-max_cache_size:]
            
            self.logger.debug(f"处理K线数据: {symbol} {timeframe}, 时间戳: {kline['datetime']}")
            return self._kline_cache[symbol][timeframe].copy()
        
        except Exception as e:
            self.logger.error(f"处理K线数据失败: {e}")
            return None

    def process_depth_data(self, message: Dict[str, Any]) -> Optional[pd.DataFrame]:
        """
        处理深度数据消息。

        Args:
            message: Kafka消息，包含深度数据

        Returns:
            处理后的深度数据DataFrame，如果数据无效则返回None
        """
        try:
            data = message.get('data', {})
            symbol = data.get('symbol')
            
            if not symbol or symbol not in self.symbols:
                return None
            
            timestamp = data.get('time', int(datetime.now().timestamp() * 1000))
            
            # 处理买单和卖单
            bids = data.get('bids', [])
            asks = data.get('asks', [])
            
            best_bid_price = float(bids[0][0]) if bids else 0
            best_bid_qty = float(bids[0][1]) if bids else 0
            best_ask_price = float(asks[0][0]) if asks else 0
            best_ask_qty = float(asks[0][1]) if asks else 0
            
            # 计算订单簿指标
            bid_total_qty = sum(float(bid[1]) for bid in bids[:10])
            ask_total_qty = sum(float(ask[1]) for ask in asks[:10])
            bid_ask_ratio = bid_total_qty / ask_total_qty if ask_total_qty > 0 else 1.0
            spread = best_ask_price - best_bid_price if best_bid_price > 0 and best_ask_price > 0 else 0
            mid_price = (best_bid_price + best_ask_price) / 2 if best_bid_price > 0 and best_ask_price > 0 else 0
            
            depth_data = {
                'timestamp': timestamp,
                'best_bid_price': best_bid_price,
                'best_bid_qty': best_bid_qty,
                'best_ask_price': best_ask_price,
                'best_ask_qty': best_ask_qty,
                'bid_total_qty': bid_total_qty,
                'ask_total_qty': ask_total_qty,
                'bid_ask_ratio': bid_ask_ratio,
                'spread': spread,
                'mid_price': mid_price,
                'datetime': datetime.fromtimestamp(timestamp / 1000)
            }
            
            # 更新缓存
            df = pd.DataFrame([depth_data])
            df.set_index('timestamp', inplace=True)
            
            if self._depth_cache[symbol].empty:
                self._depth_cache[symbol] = df
            else:
                # 如果已存在该时间戳的数据，则更新它，否则添加新行
                self._depth_cache[symbol].loc[df.index] = df
                
            # 保持缓存大小适中
            max_cache_size = 100  # 最多保存100条深度数据
            if len(self._depth_cache[symbol]) > max_cache_size:
                self._depth_cache[symbol] = self._depth_cache[symbol].iloc[-max_cache_size:]
                
            self.logger.debug(f"处理深度数据: {symbol}, 时间戳: {depth_data['datetime']}")
            return self._depth_cache[symbol].copy()
        
        except Exception as e:
            self.logger.error(f"处理深度数据失败: {e}")
            return None

    def process_trade_data(self, message: Dict[str, Any]) -> Optional[pd.DataFrame]:
        """
        处理交易数据消息。

        Args:
            message: Kafka消息，包含交易数据

        Returns:
            处理后的交易数据DataFrame，如果数据无效则返回None
        """
        try:
            data = message.get('data', {})
            symbol = data.get('symbol')
            
            if not symbol or symbol not in self.symbols:
                return None
            
            trade = {
                'trade_id': data.get('tradeId', ''),
                'price': float(data.get('price', 0)),
                'qty': float(data.get('qty', 0)),
                'timestamp': data.get('time', 0),
                'is_buyer_maker': data.get('isBuyerMaker', False),
                'is_best_match': data.get('isBestMatch', True),
                'datetime': datetime.fromtimestamp(data.get('time', 0) / 1000)
            }
            
            # 更新缓存
            df = pd.DataFrame([trade])
            df.set_index('trade_id', inplace=True)
            
            if self._trade_cache[symbol].empty:
                self._trade_cache[symbol] = df
            else:
                # 添加新的交易
                self._trade_cache[symbol] = pd.concat([self._trade_cache[symbol], df])
                
            # 保持缓存大小适中
            max_cache_size = 500  # 最多保存500条交易
            if len(self._trade_cache[symbol]) > max_cache_size:
                self._trade_cache[symbol] = self._trade_cache[symbol].iloc[-max_cache_size:]
            
            self.logger.debug(f"处理交易数据: {symbol}, 时间戳: {trade['datetime']}")
            return self._trade_cache[symbol].copy()
        
        except Exception as e:
            self.logger.error(f"处理交易数据失败: {e}")
            return None

    def get_multi_timeframe_data(self, symbol: str) -> Dict[str, pd.DataFrame]:
        """
        获取指定交易对的多时间周期K线数据。

        Args:
            symbol: 交易对

        Returns:
            多时间周期K线数据字典，格式: {timeframe: pd.DataFrame}
        """
        if symbol not in self.symbols:
            self.logger.warning(f"不支持的交易对: {symbol}")
            return {}
        
        return {tf: df.copy() for tf, df in self._kline_cache[symbol].items() if not df.empty}

    def get_latest_market_data(self, symbol: str) -> Dict[str, Any]:
        """
        获取指定交易对的最新市场数据，包括K线、深度和交易数据。

        Args:
            symbol: 交易对

        Returns:
            最新市场数据字典
        """
        if symbol not in self.symbols:
            self.logger.warning(f"不支持的交易对: {symbol}")
            return {}
        
        result = {
            'symbol': symbol,
            'kline': {},
            'depth': None,
            'trades': None,
            'timestamp': int(datetime.now().timestamp() * 1000)
        }
        
        # 获取各时间周期最新K线
        for tf, df in self._kline_cache[symbol].items():
            if not df.empty:
                result['kline'][tf] = df.iloc[-1].to_dict()
        
        # 获取最新深度数据
        if not self._depth_cache[symbol].empty:
            result['depth'] = self._depth_cache[symbol].iloc[-1].to_dict()
        
        # 获取最新交易数据
        if not self._trade_cache[symbol].empty:
            result['trades'] = self._trade_cache[symbol].tail(10).to_dict('records')
        
        return result

    def preprocess_features(self, symbol: str, timeframe: str = '1h') -> Optional[pd.DataFrame]:
        """
        预处理特征数据，为模型输入做准备。

        Args:
            symbol: 交易对
            timeframe: 时间周期

        Returns:
            预处理后的特征DataFrame，如果数据不足则返回None
        """
        if symbol not in self.symbols or timeframe not in self.timeframes:
            self.logger.warning(f"不支持的交易对或时间周期: {symbol} {timeframe}")
            return None
        
        kline_df = self._kline_cache[symbol][timeframe]
        if len(kline_df) < 50:  # 确保有足够的数据进行特征计算
            self.logger.warning(f"数据不足，无法计算特征: {symbol} {timeframe}")
            return None
        
        try:
            # 复制数据，避免修改原始缓存
            df = kline_df.copy()
            
            # 示例：计算基本特征
            # 移动平均线
            df['ma7'] = df['close'].rolling(window=7).mean()
            df['ma25'] = df['close'].rolling(window=25).mean()
            df['ma99'] = df['close'].rolling(window=99).mean()
            
            # 相对强弱指标(RSI)
            delta = df['close'].diff()
            gain = delta.where(delta > 0, 0)
            loss = -delta.where(delta < 0, 0)
            avg_gain = gain.rolling(window=14).mean()
            avg_loss = loss.rolling(window=14).mean()
            rs = avg_gain / avg_loss
            df['rsi'] = 100 - (100 / (1 + rs))
            
            # 布林带
            df['middle_band'] = df['close'].rolling(window=20).mean()
            std = df['close'].rolling(window=20).std()
            df['upper_band'] = df['middle_band'] + (std * 2)
            df['lower_band'] = df['middle_band'] - (std * 2)
            
            # 移动平均收敛散度(MACD)
            exp1 = df['close'].ewm(span=12, adjust=False).mean()
            exp2 = df['close'].ewm(span=26, adjust=False).mean()
            df['macd'] = exp1 - exp2
            df['signal'] = df['macd'].ewm(span=9, adjust=False).mean()
            df['hist'] = df['macd'] - df['signal']
            
            # 删除包含NaN的行
            df = df.dropna()
            
            return df
        
        except Exception as e:
            self.logger.error(f"特征预处理失败: {e}")
            return None
    
    def normalize_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        标准化特征。

        Args:
            df: 包含特征的DataFrame

        Returns:
            标准化后的DataFrame
        """
        if df is None or df.empty:
            return None
        
        # 复制数据，避免修改原始数据
        normalized_df = df.copy()
        
        # 需要标准化的数值列
        numeric_columns = df.select_dtypes(include=['float64', 'int64']).columns
        
        # 使用Min-Max标准化
        for col in numeric_columns:
            min_val = normalized_df[col].min()
            max_val = normalized_df[col].max()
            if max_val > min_val:
                normalized_df[col] = (normalized_df[col] - min_val) / (max_val - min_val)
        
        return normalized_df
    
    def merge_features(self, symbol: str) -> Optional[pd.DataFrame]:
        """
        合并多个时间周期和数据源的特征。

        Args:
            symbol: 交易对

        Returns:
            合并后的特征DataFrame，如果数据不足则返回None
        """
        if symbol not in self.symbols:
            self.logger.warning(f"不支持的交易对: {symbol}")
            return None
        
        # 选择一个基础时间周期作为主DataFrame
        base_tf = '1h'  # 假设1小时作为基础时间周期
        base_df = self.preprocess_features(symbol, base_tf)
        
        if base_df is None or base_df.empty:
            return None
        
        # 获取其他时间周期的特征，并重采样到基础时间周期
        for tf in self.timeframes:
            if tf == base_tf:
                continue
                
            tf_df = self.preprocess_features(symbol, tf)
            if tf_df is not None and not tf_df.empty:
                # 对特征进行重采样和重命名，以区分不同时间周期
                # 这里仅作示例，实际实现可能需要更复杂的重采样逻辑
                for col in tf_df.columns:
                    if col in ['open', 'high', 'low', 'close', 'volume', 'rsi', 'macd']:
                        # 重命名列，添加时间周期前缀
                        resampled_col = f"{col}_{tf}"
                        # 使用最近邻插值重采样到基础时间周期
                        base_df[resampled_col] = np.nan
                        # 简单匹配最近的时间戳
                        # 注意：实际应用中可能需要更精确的时间对齐
                        for idx in base_df.index:
                            nearest_idx = tf_df.index[tf_df.index <= idx].max() if len(tf_df.index[tf_df.index <= idx]) > 0 else None
                            if nearest_idx is not None:
                                base_df.at[idx, resampled_col] = tf_df.at[nearest_idx, col]
        
        # 可选：添加深度数据特征
        if not self._depth_cache[symbol].empty:
            depth_df = self._depth_cache[symbol].copy()
            # 重采样深度数据到基础时间周期
            for idx in base_df.index:
                nearest_idx = depth_df.index[depth_df.index <= idx].max() if len(depth_df.index[depth_df.index <= idx]) > 0 else None
                if nearest_idx is not None:
                    base_df.at[idx, 'bid_ask_ratio'] = depth_df.at[nearest_idx, 'bid_ask_ratio']
                    base_df.at[idx, 'spread'] = depth_df.at[nearest_idx, 'spread']
        
        # 去除NaN值
        base_df = base_df.fillna(method='ffill').dropna()
        
        return base_df