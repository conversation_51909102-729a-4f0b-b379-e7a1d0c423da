<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (21) on Fri Apr 04 16:06:08 CST 2025 -->
<title>WebsocketClientImpl (io.github.binance:binance-futures-connector-java 3.0.5 API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-04-04">
<meta name="description" content="declaration: package: com.binance.connector.futures.client.impl, class: WebsocketClientImpl">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="切换导航链接"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="../../../../../../index.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="nav-bar-cell1-rev">类</li>
<li><a href="class-use/WebsocketClientImpl.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../../index-all.html">索引</a></li>
<li><a href="../../../../../../help-doc.html#class">帮助</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>概要:</p>
<ul>
<li>嵌套</li>
<li>字段</li>
<li><a href="#constructor-summary">构造器</a></li>
<li><a href="#method-summary">方法</a></li>
</ul>
</li>
<li>
<p>详细资料:</p>
<ul>
<li>字段</li>
<li><a href="#constructor-detail">构造器</a></li>
<li><a href="#method-detail">方法</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">方法</a></li>
</ul>
<ul class="sub-nav-list">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">方法</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="搜索">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">程序包</span>&nbsp;<a href="package-summary.html">com.binance.connector.futures.client.impl</a></div>
<h1 title="类 WebsocketClientImpl" class="title">类 WebsocketClientImpl</h1>
</div>
<div class="inheritance" title="继承树"><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">java.lang.Object</a>
<div class="inheritance">com.binance.connector.futures.client.impl.WebsocketClientImpl</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>所有已实现的接口:</dt>
<dd><code><a href="../WebsocketClient.html" title="com.binance.connector.futures.client中的接口">WebsocketClient</a></code></dd>
</dl>
<dl class="notes">
<dt>直接已知子类:</dt>
<dd><code><a href="CMWebsocketClientImpl.html" title="com.binance.connector.futures.client.impl中的类">CMWebsocketClientImpl</a></code>, <code><a href="UMWebsocketClientImpl.html" title="com.binance.connector.futures.client.impl中的类">UMWebsocketClientImpl</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public abstract class </span><span class="element-name type-name-label">WebsocketClientImpl</span>
<span class="extends-implements">extends <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>
implements <a href="../WebsocketClient.html" title="com.binance.connector.futures.client中的接口">WebsocketClient</a></span></div>
<div class="block"><h2 id="futures-websocket-streams-heading">Futures Websocket Streams</h2>
 All stream endpoints under the
 <a href="https://developers.binance.com/docs/derivatives/usds-margined-futures/websocket-market-streams/Connect"> USDⓈ-M Websocket Market Streams</a> and
 <a href="https://developers.binance.com/docs/derivatives/coin-margined-futures/websocket-market-streams/Connect"> COIN-M Websocket Market Streams</a> and
 <a href="https://developers.binance.com/docs/derivatives/usds-margined-futures/user-data-streams/Connect"> USDⓈ-M User Data Streams</a> and
 <a href="https://developers.binance.com/docs/derivatives/coin-margined-futures/user-data-streams/Connect"> COIN-M User Data Streams</a>
 section of the API documentation will be implemented in this class.
 <br>
 Response will be returned as callback.</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>构造器概要</h2>
<div class="caption"><span>构造器</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">构造器</div>
<div class="table-header col-last">说明</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(java.lang.String)" class="member-name-link">WebsocketClientImpl</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;baseUrl)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>方法概要</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">所有方法</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">实例方法</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">具体方法</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">修饰符和类型</div>
<div class="table-header col-second">方法</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#aggTradeStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">aggTradeStream</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;symbol,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">The Aggregate Trade Streams push market trade information that is aggregated for fills with same price and taking side every 100 milliseconds.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#aggTradeStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">aggTradeStream</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;symbol,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onOpenCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onClosingCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onFailureCallback)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Same as <a href="#aggTradeStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)"><code>aggTradeStream(String, WebSocketCallback)</code></a> plus accepts callbacks for all major websocket connection events.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#allBookTickerStream(com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">allBookTickerStream</a><wbr>(com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Pushes any update to the best bid or ask's price or quantity in real-time for all symbols.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#allBookTickerStream(com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">allBookTickerStream</a><wbr>(com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onOpenCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onClosingCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onFailureCallback)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Same as <a href="#allBookTickerStream(com.binance.connector.futures.client.utils.WebSocketCallback)"><code>allBookTickerStream(WebSocketCallback)</code></a> plus accepts callbacks for all major websocket connection events.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#allForceOrderStream(com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">allForceOrderStream</a><wbr>(com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">The All Liquidation Order Snapshot Streams push force liquidation order information for all symbols in the market.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#allForceOrderStream(com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">allForceOrderStream</a><wbr>(com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onOpenCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onClosingCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onFailureCallback)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Same as <a href="#allForceOrderStream(com.binance.connector.futures.client.utils.WebSocketCallback)"><code>allForceOrderStream(WebSocketCallback)</code></a> plus accepts callbacks for all major websocket connection events.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#allMiniTickerStream(com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">allMiniTickerStream</a><wbr>(com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">24hr rolling window mini-ticker statistics for all symbols that changed in an array.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#allMiniTickerStream(com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">allMiniTickerStream</a><wbr>(com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onOpenCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onClosingCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onFailureCallback)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Same as <a href="#allMiniTickerStream(com.binance.connector.futures.client.utils.WebSocketCallback)"><code>allMiniTickerStream(WebSocketCallback)</code></a> plus accepts callbacks for all major websocket connection events.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#allTickerStream(com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">allTickerStream</a><wbr>(com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">24hr rolling window ticker statistics for all symbols.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#allTickerStream(com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">allTickerStream</a><wbr>(com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onOpenCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onClosingCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onFailureCallback)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Same as <a href="#allTickerStream(com.binance.connector.futures.client.utils.WebSocketCallback)"><code>allTickerStream(WebSocketCallback)</code></a> plus accepts callbacks for all major websocket connection events.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#bookTicker(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">bookTicker</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;symbol,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Pushes any update to the best bid or ask's price or quantity in real-time for a specified symbol.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#bookTicker(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">bookTicker</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;symbol,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onOpenCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onClosingCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onFailureCallback)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Same as <a href="#bookTicker(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)"><code>bookTicker(String, WebSocketCallback)</code></a> plus accepts callbacks for all major websocket connection events.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#closeAllConnections()" class="member-name-link">closeAllConnections</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Closes all streams</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#closeConnection(int)" class="member-name-link">closeConnection</a><wbr>(int&nbsp;connectionId)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Closes a specific stream based on stream Id.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#combineStreams(java.util.ArrayList,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">combineStreams</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/util/ArrayList.html" title="java.util中的类或接口" class="external-link">ArrayList</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&gt;&nbsp;streams,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Combined streams are accessed at /stream?</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#combineStreams(java.util.ArrayList,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">combineStreams</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/util/ArrayList.html" title="java.util中的类或接口" class="external-link">ArrayList</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&gt;&nbsp;streams,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onOpenCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onClosingCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onFailureCallback)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Same as <a href="#combineStreams(java.util.ArrayList,com.binance.connector.futures.client.utils.WebSocketCallback)"><code>combineStreams(ArrayList, WebSocketCallback)</code></a> plus accepts callbacks for all major websocket connection events.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#continuousKlineStream(java.lang.String,java.lang.String,java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">continuousKlineStream</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;pair,
 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;contractType,
 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;interval,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">The Kline/Candlestick Stream push updates to the current klines/candlestick every 250 milliseconds (if existing).</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#continuousKlineStream(java.lang.String,java.lang.String,java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">continuousKlineStream</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;pair,
 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;contractType,
 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;interval,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onOpenCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onClosingCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onFailureCallback)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Same as <a href="#continuousKlineStream(java.lang.String,java.lang.String,java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)"><code>continuousKlineStream(String, String, String, WebSocketCallback)</code></a> plus accepts callbacks for all major websocket connection events.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createConnection(com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,okhttp3.Request)" class="member-name-link">createConnection</a><wbr>(com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onOpenCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onClosingCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onFailureCallback,
 okhttp3.Request&nbsp;request)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#diffDepthStream(java.lang.String,int,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">diffDepthStream</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;symbol,
 int&nbsp;speed,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Bids and asks, pushed every 250 milliseconds, 500 milliseconds, 100 milliseconds (if existing)
 <br><br>
 &lt;symbol&gt;@depth@&lt;speed&gt;ms
 <br><br>
 Update Speed: 250ms, 500ms, 100ms</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#diffDepthStream(java.lang.String,int,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">diffDepthStream</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;symbol,
 int&nbsp;speed,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onOpenCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onClosingCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onFailureCallback)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Same as <a href="#diffDepthStream(java.lang.String,int,com.binance.connector.futures.client.utils.WebSocketCallback)"><code>diffDepthStream(String, int, WebSocketCallback)</code></a> plus accepts callbacks for all major websocket connection events.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#forceOrderStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">forceOrderStream</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;symbol,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">The Liquidation Order Snapshot Streams push force liquidation order information for specific symbol.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#forceOrderStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">forceOrderStream</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;symbol,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onOpenCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onClosingCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onFailureCallback)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Same as <a href="#forceOrderStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)"><code>forceOrderStream(String, WebSocketCallback)</code></a> plus accepts callbacks for all major websocket connection events.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getBaseUrl()" class="member-name-link">getBaseUrl</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>com.binance.connector.futures.client.utils.WebSocketCallback</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getNoopCallback()" class="member-name-link">getNoopCallback</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#klineStream(java.lang.String,java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">klineStream</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;symbol,
 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;interval,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">The Kline/Candlestick Stream push updates to the current klines/candlestick every 250 milliseconds (if existing).</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#klineStream(java.lang.String,java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">klineStream</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;symbol,
 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;interval,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onOpenCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onClosingCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onFailureCallback)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Same as <a href="#klineStream(java.lang.String,java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)"><code>klineStream(String, String, WebSocketCallback)</code></a> plus accepts callbacks for all major websocket connection events.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#listenUserStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">listenUserStream</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;listenKey,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">User Data Streams are accessed at /ws/&lt;listenKey&gt;</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#listenUserStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">listenUserStream</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;listenKey,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onOpenCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onClosingCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onFailureCallback)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Same as <a href="#listenUserStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)"><code>listenUserStream(String, WebSocketCallback)</code></a> plus accepts callbacks for all major websocket connection events.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#markPriceStream(java.lang.String,int,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">markPriceStream</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;symbol,
 int&nbsp;speed,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Mark price and funding rate for a single symbol pushed every 3 seconds or every second.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#markPriceStream(java.lang.String,int,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">markPriceStream</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;symbol,
 int&nbsp;speed,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onOpenCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onClosingCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onFailureCallback)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Same as <a href="#markPriceStream(java.lang.String,int,com.binance.connector.futures.client.utils.WebSocketCallback)"><code>markPriceStream(String, int, WebSocketCallback)</code></a> plus accepts callbacks for all major websocket connection events.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#miniTickerStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">miniTickerStream</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;symbol,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">24hr rolling window mini-ticker statistics.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#miniTickerStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">miniTickerStream</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;symbol,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onOpenCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onClosingCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onFailureCallback)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Same as <a href="#miniTickerStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)"><code>miniTickerStream(String, WebSocketCallback)</code></a> plus accepts callbacks for all major websocket connection events.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#partialDepthStream(java.lang.String,int,int,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">partialDepthStream</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;symbol,
 int&nbsp;levels,
 int&nbsp;speed,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Top bids and asks, Valid are 5, 10, or 20.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#partialDepthStream(java.lang.String,int,int,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">partialDepthStream</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;symbol,
 int&nbsp;levels,
 int&nbsp;speed,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onOpenCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onClosingCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onFailureCallback)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Same as <a href="#partialDepthStream(java.lang.String,int,int,com.binance.connector.futures.client.utils.WebSocketCallback)"><code>partialDepthStream(String, int, int, WebSocketCallback)</code></a> plus accepts callbacks for all major websocket connection events.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#symbolTicker(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">symbolTicker</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;symbol,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">24hr rolling window ticker statistics for a single symbol.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#symbolTicker(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)" class="member-name-link">symbolTicker</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;symbol,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onOpenCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onClosingCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onFailureCallback)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Same as <a href="#symbolTicker(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)"><code>symbolTicker(String, WebSocketCallback)</code></a> plus accepts callbacks for all major websocket connection events.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">从类继承的方法&nbsp;java.lang.<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a></h3>
<code><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html#clone--" title="java.lang中的类或接口" class="external-link">clone</a>, <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html#equals-java.lang.Object-" title="java.lang中的类或接口" class="external-link">equals</a>, <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html#finalize--" title="java.lang中的类或接口" class="external-link">finalize</a>, <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html#getClass--" title="java.lang中的类或接口" class="external-link">getClass</a>, <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html#hashCode--" title="java.lang中的类或接口" class="external-link">hashCode</a>, <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html#notify--" title="java.lang中的类或接口" class="external-link">notify</a>, <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html#notifyAll--" title="java.lang中的类或接口" class="external-link">notifyAll</a>, <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html#toString--" title="java.lang中的类或接口" class="external-link">toString</a>, <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html#wait--" title="java.lang中的类或接口" class="external-link">wait</a>, <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html#wait-long-" title="java.lang中的类或接口" class="external-link">wait</a>, <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html#wait-long-int-" title="java.lang中的类或接口" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>构造器详细资料</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;(java.lang.String)">
<h3>WebsocketClientImpl</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">WebsocketClientImpl</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;baseUrl)</span></div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>方法详细资料</h2>
<ul class="member-list">
<li>
<section class="detail" id="getNoopCallback()">
<h3>getNoopCallback</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">com.binance.connector.futures.client.utils.WebSocketCallback</span>&nbsp;<span class="element-name">getNoopCallback</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getBaseUrl()">
<h3>getBaseUrl</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></span>&nbsp;<span class="element-name">getBaseUrl</span>()</div>
</section>
</li>
<li>
<section class="detail" id="aggTradeStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)">
<h3>aggTradeStream</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">aggTradeStream</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;symbol,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback)</span></div>
<div class="block">The Aggregate Trade Streams push market trade information that is aggregated for fills with same price and taking side every 100 milliseconds.
 Only market trades will be aggregated, which means the insurance fund trades and ADL trades won't be aggregated.
 <br><br>
 &lt;symbol&gt;@aggTrade
 <br><br>
 Update Speed: 100ms</div>
<dl class="notes">
<dt>指定者:</dt>
<dd><code><a href="../WebsocketClient.html#aggTradeStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)">aggTradeStream</a></code>&nbsp;在接口中&nbsp;<code><a href="../WebsocketClient.html" title="com.binance.connector.futures.client中的接口">WebsocketClient</a></code></dd>
<dt>参数:</dt>
<dd><code>symbol</code> - trading symbol</dd>
<dd><code>onMessageCallback</code> - onMessageCallback</dd>
<dt>返回:</dt>
<dd>int - Connection ID</dd>
<dt>另请参阅:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://developers.binance.com/docs/derivatives/usds-margined-futures/websocket-market-streams/Aggregate-Trade-Streams">
 https://developers.binance.com/docs/derivatives/usds-margined-futures/websocket-market-streams/Aggregate-Trade-Streams</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="aggTradeStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)">
<h3>aggTradeStream</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">aggTradeStream</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;symbol,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onOpenCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onClosingCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onFailureCallback)</span></div>
<div class="block">Same as <a href="#aggTradeStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)"><code>aggTradeStream(String, WebSocketCallback)</code></a> plus accepts callbacks for all major websocket connection events.</div>
<dl class="notes">
<dt>指定者:</dt>
<dd><code><a href="../WebsocketClient.html#aggTradeStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)">aggTradeStream</a></code>&nbsp;在接口中&nbsp;<code><a href="../WebsocketClient.html" title="com.binance.connector.futures.client中的接口">WebsocketClient</a></code></dd>
<dt>参数:</dt>
<dd><code>symbol</code> - trading symbol</dd>
<dd><code>onOpenCallback</code> - onOpenCallback</dd>
<dd><code>onMessageCallback</code> - onMessageCallback</dd>
<dd><code>onClosingCallback</code> - onClosingCallback</dd>
<dd><code>onFailureCallback</code> - onFailureCallback</dd>
<dt>返回:</dt>
<dd>int - Connection ID</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="markPriceStream(java.lang.String,int,com.binance.connector.futures.client.utils.WebSocketCallback)">
<h3>markPriceStream</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">markPriceStream</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;symbol,
 int&nbsp;speed,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback)</span></div>
<div class="block">Mark price and funding rate for a single symbol pushed every 3 seconds or every second.
 <br><br>
 &lt;symbol&gt;@markPrice or &lt;symbol&gt;@markPrice@1s
 <br><br>
 Update Speed: 3000ms or 1000ms</div>
<dl class="notes">
<dt>指定者:</dt>
<dd><code><a href="../WebsocketClient.html#markPriceStream(java.lang.String,int,com.binance.connector.futures.client.utils.WebSocketCallback)">markPriceStream</a></code>&nbsp;在接口中&nbsp;<code><a href="../WebsocketClient.html" title="com.binance.connector.futures.client中的接口">WebsocketClient</a></code></dd>
<dt>参数:</dt>
<dd><code>symbol</code> - trading symbol</dd>
<dd><code>speed</code> - speed in seconds, can be 1 or 3</dd>
<dd><code>onMessageCallback</code> - onMessageCallback</dd>
<dt>返回:</dt>
<dd>int - Connection ID</dd>
<dt>另请参阅:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://developers.binance.com/docs/derivatives/usds-margined-futures/websocket-market-streams/Mark-Price-Stream">
 https://developers.binance.com/docs/derivatives/usds-margined-futures/websocket-market-streams/Mark-Price-Stream</a></li>
<li><a href="https://developers.binance.com/docs/derivatives/coin-margined-futures/websocket-market-streams/Mark-Price-Stream">
 https://developers.binance.com/docs/derivatives/coin-margined-futures/websocket-market-streams/Mark-Price-Stream</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="markPriceStream(java.lang.String,int,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)">
<h3>markPriceStream</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">markPriceStream</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;symbol,
 int&nbsp;speed,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onOpenCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onClosingCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onFailureCallback)</span></div>
<div class="block">Same as <a href="#markPriceStream(java.lang.String,int,com.binance.connector.futures.client.utils.WebSocketCallback)"><code>markPriceStream(String, int, WebSocketCallback)</code></a> plus accepts callbacks for all major websocket connection events.</div>
<dl class="notes">
<dt>指定者:</dt>
<dd><code><a href="../WebsocketClient.html#markPriceStream(java.lang.String,int,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)">markPriceStream</a></code>&nbsp;在接口中&nbsp;<code><a href="../WebsocketClient.html" title="com.binance.connector.futures.client中的接口">WebsocketClient</a></code></dd>
<dt>参数:</dt>
<dd><code>symbol</code> - trading symbol</dd>
<dd><code>speed</code> - speed in seconds, can be 1 or 3</dd>
<dd><code>onOpenCallback</code> - onOpenCallback</dd>
<dd><code>onMessageCallback</code> - onMessageCallback</dd>
<dd><code>onClosingCallback</code> - onClosingCallback</dd>
<dd><code>onFailureCallback</code> - onFailureCallback</dd>
<dt>返回:</dt>
<dd>int - Connection ID</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="klineStream(java.lang.String,java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)">
<h3>klineStream</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">klineStream</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;symbol,
 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;interval,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback)</span></div>
<div class="block">The Kline/Candlestick Stream push updates to the current klines/candlestick every 250 milliseconds (if existing).
 <br><br>
 &lt;symbol&gt;@kline_&lt;interval&gt;
 <br><br>
 Update Speed: 250ms</div>
<dl class="notes">
<dt>指定者:</dt>
<dd><code><a href="../WebsocketClient.html#klineStream(java.lang.String,java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)">klineStream</a></code>&nbsp;在接口中&nbsp;<code><a href="../WebsocketClient.html" title="com.binance.connector.futures.client中的接口">WebsocketClient</a></code></dd>
<dt>参数:</dt>
<dd><code>symbol</code> - trading symbol</dd>
<dd><code>interval</code> - kline interval - 1m 3m 5m 15m 30m 1h 2h 4h 6h 8h 12h 1d 3d 1w 1M</dd>
<dd><code>onMessageCallback</code> - onMessageCallback</dd>
<dt>返回:</dt>
<dd>int - Connection ID</dd>
<dt>另请参阅:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://developers.binance.com/docs/derivatives/usds-margined-futures/websocket-market-streams/Kline-Candlestick-Streams">
 https://developers.binance.com/docs/derivatives/usds-margined-futures/websocket-market-streams/Kline-Candlestick-Streams</a></li>
<li><a href="https://developers.binance.com/docs/derivatives/coin-margined-futures/websocket-market-streams/Kline-Candlestick-Streams">
 https://developers.binance.com/docs/derivatives/coin-margined-futures/websocket-market-streams/Kline-Candlestick-Streams</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="klineStream(java.lang.String,java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)">
<h3>klineStream</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">klineStream</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;symbol,
 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;interval,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onOpenCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onClosingCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onFailureCallback)</span></div>
<div class="block">Same as <a href="#klineStream(java.lang.String,java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)"><code>klineStream(String, String, WebSocketCallback)</code></a> plus accepts callbacks for all major websocket connection events.</div>
<dl class="notes">
<dt>指定者:</dt>
<dd><code><a href="../WebsocketClient.html#klineStream(java.lang.String,java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)">klineStream</a></code>&nbsp;在接口中&nbsp;<code><a href="../WebsocketClient.html" title="com.binance.connector.futures.client中的接口">WebsocketClient</a></code></dd>
<dt>参数:</dt>
<dd><code>symbol</code> - trading symbol</dd>
<dd><code>interval</code> - kline interval - 1m 3m 5m 15m 30m 1h 2h 4h 6h 8h 12h 1d 3d 1w 1M</dd>
<dd><code>onOpenCallback</code> - onOpenCallback</dd>
<dd><code>onMessageCallback</code> - onMessageCallback</dd>
<dd><code>onClosingCallback</code> - onClosingCallback</dd>
<dd><code>onFailureCallback</code> - onFailureCallback</dd>
<dt>返回:</dt>
<dd>int - Connection ID</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="continuousKlineStream(java.lang.String,java.lang.String,java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)">
<h3>continuousKlineStream</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">continuousKlineStream</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;pair,
 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;contractType,
 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;interval,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback)</span></div>
<div class="block">The Kline/Candlestick Stream push updates to the current klines/candlestick every 250 milliseconds (if existing). Contract Types are: perpetual, current_quarter, next_quarter
 <br><br>
  &lt;pair&gt;_&lt;contractType&gt;@continuousKline_&lt;interval&gt;
 <br><br>
 Update Speed: 250ms</div>
<dl class="notes">
<dt>指定者:</dt>
<dd><code><a href="../WebsocketClient.html#continuousKlineStream(java.lang.String,java.lang.String,java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)">continuousKlineStream</a></code>&nbsp;在接口中&nbsp;<code><a href="../WebsocketClient.html" title="com.binance.connector.futures.client中的接口">WebsocketClient</a></code></dd>
<dt>参数:</dt>
<dd><code>pair</code> - trading pair</dd>
<dd><code>contractType</code> - perpetual, current_quarter, next_quarter</dd>
<dd><code>interval</code> - kline interval - 1m 3m 5m 15m 30m 1h 2h 4h 6h 8h 12h 1d 3d 1w 1M</dd>
<dd><code>onMessageCallback</code> - onMessageCallback</dd>
<dt>返回:</dt>
<dd>int - Connection ID</dd>
<dt>另请参阅:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://developers.binance.com/docs/derivatives/coin-margined-futures/websocket-market-streams/Continuous-Contract-Kline-Candlestick-Streams">
 https://developers.binance.com/docs/derivatives/coin-margined-futures/websocket-market-streams/Continuous-Contract-Kline-Candlestick-Streams</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="continuousKlineStream(java.lang.String,java.lang.String,java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)">
<h3>continuousKlineStream</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">continuousKlineStream</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;pair,
 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;contractType,
 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;interval,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onOpenCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onClosingCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onFailureCallback)</span></div>
<div class="block">Same as <a href="#continuousKlineStream(java.lang.String,java.lang.String,java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)"><code>continuousKlineStream(String, String, String, WebSocketCallback)</code></a> plus accepts callbacks for all major websocket connection events.</div>
<dl class="notes">
<dt>指定者:</dt>
<dd><code><a href="../WebsocketClient.html#continuousKlineStream(java.lang.String,java.lang.String,java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)">continuousKlineStream</a></code>&nbsp;在接口中&nbsp;<code><a href="../WebsocketClient.html" title="com.binance.connector.futures.client中的接口">WebsocketClient</a></code></dd>
<dt>参数:</dt>
<dd><code>pair</code> - trading pair</dd>
<dd><code>contractType</code> - perpetual, current_quarter, next_quarter</dd>
<dd><code>interval</code> - kline interval - 1m 3m 5m 15m 30m 1h 2h 4h 6h 8h 12h 1d 3d 1w 1M</dd>
<dd><code>onOpenCallback</code> - onOpenCallback</dd>
<dd><code>onMessageCallback</code> - onMessageCallback</dd>
<dd><code>onClosingCallback</code> - onClosingCallback</dd>
<dd><code>onFailureCallback</code> - onFailureCallback</dd>
<dt>返回:</dt>
<dd>int - Connection ID</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="miniTickerStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)">
<h3>miniTickerStream</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">miniTickerStream</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;symbol,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback)</span></div>
<div class="block">24hr rolling window mini-ticker statistics.
 These are NOT the statistics of the UTC day, but a 24hr rolling window for the previous 24hrs.
 <br><br>
 &lt;symbol&gt;@miniTicker
 <br><br>
 Update Speed: 500ms</div>
<dl class="notes">
<dt>指定者:</dt>
<dd><code><a href="../WebsocketClient.html#miniTickerStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)">miniTickerStream</a></code>&nbsp;在接口中&nbsp;<code><a href="../WebsocketClient.html" title="com.binance.connector.futures.client中的接口">WebsocketClient</a></code></dd>
<dt>参数:</dt>
<dd><code>symbol</code> - trading symbol</dd>
<dd><code>onMessageCallback</code> - onMessageCallback</dd>
<dt>返回:</dt>
<dd>int - Connection ID</dd>
<dt>另请参阅:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://developers.binance.com/docs/derivatives/usds-margined-futures/websocket-market-streams/Individual-Symbol-Mini-Ticker-Stream">
 https://developers.binance.com/docs/derivatives/usds-margined-futures/websocket-market-streams/Individual-Symbol-Mini-Ticker-Stream</a></li>
<li><a href="https://developers.binance.com/docs/derivatives/coin-margined-futures/websocket-market-streams/Individual-Symbol-Mini-Ticker-Stream">
 https://developers.binance.com/docs/derivatives/coin-margined-futures/websocket-market-streams/Individual-Symbol-Mini-Ticker-Stream</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="miniTickerStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)">
<h3>miniTickerStream</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">miniTickerStream</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;symbol,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onOpenCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onClosingCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onFailureCallback)</span></div>
<div class="block">Same as <a href="#miniTickerStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)"><code>miniTickerStream(String, WebSocketCallback)</code></a> plus accepts callbacks for all major websocket connection events.</div>
<dl class="notes">
<dt>指定者:</dt>
<dd><code><a href="../WebsocketClient.html#miniTickerStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)">miniTickerStream</a></code>&nbsp;在接口中&nbsp;<code><a href="../WebsocketClient.html" title="com.binance.connector.futures.client中的接口">WebsocketClient</a></code></dd>
<dt>参数:</dt>
<dd><code>symbol</code> - trading symbol</dd>
<dd><code>onOpenCallback</code> - onOpenCallback</dd>
<dd><code>onMessageCallback</code> - onMessageCallback</dd>
<dd><code>onClosingCallback</code> - onClosingCallback</dd>
<dd><code>onFailureCallback</code> - onFailureCallback</dd>
<dt>返回:</dt>
<dd>int - Connection ID</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="allMiniTickerStream(com.binance.connector.futures.client.utils.WebSocketCallback)">
<h3>allMiniTickerStream</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">allMiniTickerStream</span><wbr><span class="parameters">(com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback)</span></div>
<div class="block">24hr rolling window mini-ticker statistics for all symbols that changed in an array.
 These are NOT the statistics of the UTC day, but a 24hr rolling window for the previous 24hrs.
 Note that only tickers that have changed will be present in the array.
 <br><br>
 !miniTicker@arr
 <br><br>
 Update Speed: 1000ms</div>
<dl class="notes">
<dt>指定者:</dt>
<dd><code><a href="../WebsocketClient.html#allMiniTickerStream(com.binance.connector.futures.client.utils.WebSocketCallback)">allMiniTickerStream</a></code>&nbsp;在接口中&nbsp;<code><a href="../WebsocketClient.html" title="com.binance.connector.futures.client中的接口">WebsocketClient</a></code></dd>
<dt>参数:</dt>
<dd><code>onMessageCallback</code> - onMessageCallback</dd>
<dt>返回:</dt>
<dd>int - Connection ID</dd>
<dt>另请参阅:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://developers.binance.com/docs/derivatives/usds-margined-futures/websocket-market-streams/All-Market-Mini-Tickers-Stream">
 https://developers.binance.com/docs/derivatives/usds-margined-futures/websocket-market-streams/All-Market-Mini-Tickers-Stream</a></li>
<li><a href="https://developers.binance.com/docs/derivatives/coin-margined-futures/websocket-market-streams/All-Market-Mini-Tickers-Stream">
 https://developers.binance.com/docs/derivatives/coin-margined-futures/websocket-market-streams/All-Market-Mini-Tickers-Stream</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="allMiniTickerStream(com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)">
<h3>allMiniTickerStream</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">allMiniTickerStream</span><wbr><span class="parameters">(com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onOpenCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onClosingCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onFailureCallback)</span></div>
<div class="block">Same as <a href="#allMiniTickerStream(com.binance.connector.futures.client.utils.WebSocketCallback)"><code>allMiniTickerStream(WebSocketCallback)</code></a> plus accepts callbacks for all major websocket connection events.</div>
<dl class="notes">
<dt>指定者:</dt>
<dd><code><a href="../WebsocketClient.html#allMiniTickerStream(com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)">allMiniTickerStream</a></code>&nbsp;在接口中&nbsp;<code><a href="../WebsocketClient.html" title="com.binance.connector.futures.client中的接口">WebsocketClient</a></code></dd>
<dt>参数:</dt>
<dd><code>onOpenCallback</code> - onOpenCallback</dd>
<dd><code>onMessageCallback</code> - onMessageCallback</dd>
<dd><code>onClosingCallback</code> - onClosingCallback</dd>
<dd><code>onFailureCallback</code> - onFailureCallback</dd>
<dt>返回:</dt>
<dd>int - Connection ID</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="symbolTicker(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)">
<h3>symbolTicker</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">symbolTicker</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;symbol,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback)</span></div>
<div class="block">24hr rolling window ticker statistics for a single symbol.
 These are NOT the statistics of the UTC day, but a 24hr rolling window for the previous 24hrs.
 <br><br>
 &lt;symbol&gt;@ticker
 <br><br>
 Update Speed: 500ms</div>
<dl class="notes">
<dt>指定者:</dt>
<dd><code><a href="../WebsocketClient.html#symbolTicker(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)">symbolTicker</a></code>&nbsp;在接口中&nbsp;<code><a href="../WebsocketClient.html" title="com.binance.connector.futures.client中的接口">WebsocketClient</a></code></dd>
<dt>参数:</dt>
<dd><code>symbol</code> - trading symbol</dd>
<dd><code>onMessageCallback</code> - onMessageCallback</dd>
<dt>返回:</dt>
<dd>int - Connection ID</dd>
<dt>另请参阅:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://developers.binance.com/docs/derivatives/usds-margined-futures/websocket-market-streams/Individual-Symbol-Ticker-Streams">
 https://developers.binance.com/docs/derivatives/usds-margined-futures/websocket-market-streams/Individual-Symbol-Ticker-Streams</a></li>
<li><a href="https://developers.binance.com/docs/derivatives/coin-margined-futures/websocket-market-streams/Individual-Symbol-Ticker-Streams">
 https://developers.binance.com/docs/derivatives/coin-margined-futures/websocket-market-streams/Individual-Symbol-Ticker-Streams</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="symbolTicker(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)">
<h3>symbolTicker</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">symbolTicker</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;symbol,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onOpenCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onClosingCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onFailureCallback)</span></div>
<div class="block">Same as <a href="#symbolTicker(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)"><code>symbolTicker(String, WebSocketCallback)</code></a> plus accepts callbacks for all major websocket connection events.</div>
<dl class="notes">
<dt>指定者:</dt>
<dd><code><a href="../WebsocketClient.html#symbolTicker(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)">symbolTicker</a></code>&nbsp;在接口中&nbsp;<code><a href="../WebsocketClient.html" title="com.binance.connector.futures.client中的接口">WebsocketClient</a></code></dd>
<dt>参数:</dt>
<dd><code>symbol</code> - trading symbol</dd>
<dd><code>onOpenCallback</code> - onOpenCallback</dd>
<dd><code>onMessageCallback</code> - onMessageCallback</dd>
<dd><code>onClosingCallback</code> - onClosingCallback</dd>
<dd><code>onFailureCallback</code> - onFailureCallback</dd>
<dt>返回:</dt>
<dd>int - Connection ID</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="allTickerStream(com.binance.connector.futures.client.utils.WebSocketCallback)">
<h3>allTickerStream</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">allTickerStream</span><wbr><span class="parameters">(com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback)</span></div>
<div class="block">24hr rolling window ticker statistics for all symbols.
 These are NOT the statistics of the UTC day, but a 24hr rolling window from requestTime to 24hrs before.
 Note that only tickers that have changed will be present in the array.
 <br><br>
 !ticker@arr
 <br><br>
 Update Speed: 1000ms</div>
<dl class="notes">
<dt>指定者:</dt>
<dd><code><a href="../WebsocketClient.html#allTickerStream(com.binance.connector.futures.client.utils.WebSocketCallback)">allTickerStream</a></code>&nbsp;在接口中&nbsp;<code><a href="../WebsocketClient.html" title="com.binance.connector.futures.client中的接口">WebsocketClient</a></code></dd>
<dt>参数:</dt>
<dd><code>onMessageCallback</code> - onMessageCallback</dd>
<dt>返回:</dt>
<dd>int - Connection ID</dd>
<dt>另请参阅:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://developers.binance.com/docs/derivatives/usds-margined-futures/websocket-market-streams/All-Market-Tickers-Streams">
 https://developers.binance.com/docs/derivatives/usds-margined-futures/websocket-market-streams/All-Market-Tickers-Streams</a></li>
<li><a href="https://developers.binance.com/docs/derivatives/coin-margined-futures/websocket-market-streams/All-Market-Tickers-Streams">
 https://developers.binance.com/docs/derivatives/coin-margined-futures/websocket-market-streams/All-Market-Tickers-Streams</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="allTickerStream(com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)">
<h3>allTickerStream</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">allTickerStream</span><wbr><span class="parameters">(com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onOpenCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onClosingCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onFailureCallback)</span></div>
<div class="block">Same as <a href="#allTickerStream(com.binance.connector.futures.client.utils.WebSocketCallback)"><code>allTickerStream(WebSocketCallback)</code></a> plus accepts callbacks for all major websocket connection events.</div>
<dl class="notes">
<dt>指定者:</dt>
<dd><code><a href="../WebsocketClient.html#allTickerStream(com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)">allTickerStream</a></code>&nbsp;在接口中&nbsp;<code><a href="../WebsocketClient.html" title="com.binance.connector.futures.client中的接口">WebsocketClient</a></code></dd>
<dt>参数:</dt>
<dd><code>onOpenCallback</code> - onOpenCallback</dd>
<dd><code>onMessageCallback</code> - onMessageCallback</dd>
<dd><code>onClosingCallback</code> - onClosingCallback</dd>
<dd><code>onFailureCallback</code> - onFailureCallback</dd>
<dt>返回:</dt>
<dd>int - Connection ID</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="bookTicker(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)">
<h3>bookTicker</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">bookTicker</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;symbol,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback)</span></div>
<div class="block">Pushes any update to the best bid or ask's price or quantity in real-time for a specified symbol.
 <br><br>
 &lt;symbol&gt;@bookTicker
 <br><br>
 Update Speed: Real-time</div>
<dl class="notes">
<dt>指定者:</dt>
<dd><code><a href="../WebsocketClient.html#bookTicker(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)">bookTicker</a></code>&nbsp;在接口中&nbsp;<code><a href="../WebsocketClient.html" title="com.binance.connector.futures.client中的接口">WebsocketClient</a></code></dd>
<dt>参数:</dt>
<dd><code>symbol</code> - trading symbol</dd>
<dd><code>onMessageCallback</code> - onMessageCallback</dd>
<dt>返回:</dt>
<dd>int - Connection ID</dd>
<dt>另请参阅:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://developers.binance.com/docs/derivatives/usds-margined-futures/websocket-market-streams/Individual-Symbol-Book-Ticker-Streams">
 https://developers.binance.com/docs/derivatives/usds-margined-futures/websocket-market-streams/Individual-Symbol-Book-Ticker-Streams</a></li>
<li><a href="https://developers.binance.com/docs/derivatives/coin-margined-futures/websocket-market-streams/Individual-Symbol-Book-Ticker-Streams">
 https://developers.binance.com/docs/derivatives/coin-margined-futures/websocket-market-streams/Individual-Symbol-Book-Ticker-Streams</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="bookTicker(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)">
<h3>bookTicker</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">bookTicker</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;symbol,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onOpenCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onClosingCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onFailureCallback)</span></div>
<div class="block">Same as <a href="#bookTicker(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)"><code>bookTicker(String, WebSocketCallback)</code></a> plus accepts callbacks for all major websocket connection events.</div>
<dl class="notes">
<dt>指定者:</dt>
<dd><code><a href="../WebsocketClient.html#bookTicker(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)">bookTicker</a></code>&nbsp;在接口中&nbsp;<code><a href="../WebsocketClient.html" title="com.binance.connector.futures.client中的接口">WebsocketClient</a></code></dd>
<dt>参数:</dt>
<dd><code>symbol</code> - trading symbol</dd>
<dd><code>onOpenCallback</code> - onOpenCallback</dd>
<dd><code>onMessageCallback</code> - onMessageCallback</dd>
<dd><code>onClosingCallback</code> - onClosingCallback</dd>
<dd><code>onFailureCallback</code> - onFailureCallback</dd>
<dt>返回:</dt>
<dd>int - Connection ID</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="allBookTickerStream(com.binance.connector.futures.client.utils.WebSocketCallback)">
<h3>allBookTickerStream</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">allBookTickerStream</span><wbr><span class="parameters">(com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback)</span></div>
<div class="block">Pushes any update to the best bid or ask's price or quantity in real-time for all symbols.
 <br><br>
 !bookTicker
 <br><br>
 Update Speed: Real-time</div>
<dl class="notes">
<dt>指定者:</dt>
<dd><code><a href="../WebsocketClient.html#allBookTickerStream(com.binance.connector.futures.client.utils.WebSocketCallback)">allBookTickerStream</a></code>&nbsp;在接口中&nbsp;<code><a href="../WebsocketClient.html" title="com.binance.connector.futures.client中的接口">WebsocketClient</a></code></dd>
<dt>参数:</dt>
<dd><code>onMessageCallback</code> - onMessageCallback</dd>
<dt>返回:</dt>
<dd>int - Connection ID</dd>
<dt>另请参阅:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://developers.binance.com/docs/derivatives/usds-margined-futures/websocket-market-streams/All-Book-Tickers-Stream">
 https://developers.binance.com/docs/derivatives/usds-margined-futures/websocket-market-streams/All-Book-Tickers-Stream</a></li>
<li><a href="https://developers.binance.com/docs/derivatives/coin-margined-futures/websocket-market-streams/All-Book-Tickers-Stream">
 https://developers.binance.com/docs/derivatives/coin-margined-futures/websocket-market-streams/All-Book-Tickers-Stream</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="allBookTickerStream(com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)">
<h3>allBookTickerStream</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">allBookTickerStream</span><wbr><span class="parameters">(com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onOpenCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onClosingCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onFailureCallback)</span></div>
<div class="block">Same as <a href="#allBookTickerStream(com.binance.connector.futures.client.utils.WebSocketCallback)"><code>allBookTickerStream(WebSocketCallback)</code></a> plus accepts callbacks for all major websocket connection events.</div>
<dl class="notes">
<dt>指定者:</dt>
<dd><code><a href="../WebsocketClient.html#allBookTickerStream(com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)">allBookTickerStream</a></code>&nbsp;在接口中&nbsp;<code><a href="../WebsocketClient.html" title="com.binance.connector.futures.client中的接口">WebsocketClient</a></code></dd>
<dt>参数:</dt>
<dd><code>onOpenCallback</code> - onOpenCallback</dd>
<dd><code>onMessageCallback</code> - onMessageCallback</dd>
<dd><code>onClosingCallback</code> - onClosingCallback</dd>
<dd><code>onFailureCallback</code> - onFailureCallback</dd>
<dt>返回:</dt>
<dd>int - Connection ID</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="forceOrderStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)">
<h3>forceOrderStream</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">forceOrderStream</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;symbol,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback)</span></div>
<div class="block">The Liquidation Order Snapshot Streams push force liquidation order information for specific symbol.
 For each symbol，only the latest one liquidation order within 1000ms will be pushed as the snapshot.
 If no liquidation happens in the interval of 1000ms, no stream will be pushed.
 <br><br>
 &lt;symbol&gt;@forceOrder
 <br><br>
 Update Speed: 1000ms</div>
<dl class="notes">
<dt>指定者:</dt>
<dd><code><a href="../WebsocketClient.html#forceOrderStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)">forceOrderStream</a></code>&nbsp;在接口中&nbsp;<code><a href="../WebsocketClient.html" title="com.binance.connector.futures.client中的接口">WebsocketClient</a></code></dd>
<dt>参数:</dt>
<dd><code>symbol</code> - trading symbol</dd>
<dd><code>onMessageCallback</code> - onMessageCallback</dd>
<dt>返回:</dt>
<dd>int - Connection ID</dd>
<dt>另请参阅:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://developers.binance.com/docs/derivatives/usds-margined-futures/websocket-market-streams/Liquidation-Order-Streams">
 https://developers.binance.com/docs/derivatives/usds-margined-futures/websocket-market-streams/Liquidation-Order-Streams</a></li>
<li><a href="https://developers.binance.com/docs/derivatives/coin-margined-futures/websocket-market-streams/Liquidation-Order-Streams">
 https://developers.binance.com/docs/derivatives/coin-margined-futures/websocket-market-streams/Liquidation-Order-Streams</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="forceOrderStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)">
<h3>forceOrderStream</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">forceOrderStream</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;symbol,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onOpenCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onClosingCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onFailureCallback)</span></div>
<div class="block">Same as <a href="#forceOrderStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)"><code>forceOrderStream(String, WebSocketCallback)</code></a> plus accepts callbacks for all major websocket connection events.</div>
<dl class="notes">
<dt>指定者:</dt>
<dd><code><a href="../WebsocketClient.html#forceOrderStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)">forceOrderStream</a></code>&nbsp;在接口中&nbsp;<code><a href="../WebsocketClient.html" title="com.binance.connector.futures.client中的接口">WebsocketClient</a></code></dd>
<dt>参数:</dt>
<dd><code>symbol</code> - trading symbol</dd>
<dd><code>onOpenCallback</code> - onOpenCallback</dd>
<dd><code>onMessageCallback</code> - onMessageCallback</dd>
<dd><code>onClosingCallback</code> - onClosingCallback</dd>
<dd><code>onFailureCallback</code> - onFailureCallback</dd>
<dt>返回:</dt>
<dd>int - Connection ID</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="allForceOrderStream(com.binance.connector.futures.client.utils.WebSocketCallback)">
<h3>allForceOrderStream</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">allForceOrderStream</span><wbr><span class="parameters">(com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback)</span></div>
<div class="block">The All Liquidation Order Snapshot Streams push force liquidation order information for all symbols in the market.
 For each symbol，only the latest one liquidation order within 1000ms will be pushed as the snapshot.
 If no liquidation happens in the interval of 1000ms, no stream will be pushed.
 <br><br>
 !forceOrder@arr
 <br><br>
 Update Speed: 1000ms</div>
<dl class="notes">
<dt>指定者:</dt>
<dd><code><a href="../WebsocketClient.html#allForceOrderStream(com.binance.connector.futures.client.utils.WebSocketCallback)">allForceOrderStream</a></code>&nbsp;在接口中&nbsp;<code><a href="../WebsocketClient.html" title="com.binance.connector.futures.client中的接口">WebsocketClient</a></code></dd>
<dt>参数:</dt>
<dd><code>onMessageCallback</code> - onMessageCallback</dd>
<dt>返回:</dt>
<dd>int - Connection ID</dd>
<dt>另请参阅:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://developers.binance.com/docs/derivatives/usds-margined-futures/websocket-market-streams/All-Market-Liquidation-Order-Streams">
 https://developers.binance.com/docs/derivatives/usds-margined-futures/websocket-market-streams/All-Market-Liquidation-Order-Streams</a></li>
<li><a href="https://developers.binance.com/docs/derivatives/coin-margined-futures/websocket-market-streams/All-Market-Liquidation-Order-Streams">
 https://developers.binance.com/docs/derivatives/coin-margined-futures/websocket-market-streams/All-Market-Liquidation-Order-Streams</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="allForceOrderStream(com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)">
<h3>allForceOrderStream</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">allForceOrderStream</span><wbr><span class="parameters">(com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onOpenCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onClosingCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onFailureCallback)</span></div>
<div class="block">Same as <a href="#allForceOrderStream(com.binance.connector.futures.client.utils.WebSocketCallback)"><code>allForceOrderStream(WebSocketCallback)</code></a> plus accepts callbacks for all major websocket connection events.</div>
<dl class="notes">
<dt>指定者:</dt>
<dd><code><a href="../WebsocketClient.html#allForceOrderStream(com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)">allForceOrderStream</a></code>&nbsp;在接口中&nbsp;<code><a href="../WebsocketClient.html" title="com.binance.connector.futures.client中的接口">WebsocketClient</a></code></dd>
<dt>参数:</dt>
<dd><code>onOpenCallback</code> - onOpenCallback</dd>
<dd><code>onMessageCallback</code> - onMessageCallback</dd>
<dd><code>onClosingCallback</code> - onClosingCallback</dd>
<dd><code>onFailureCallback</code> - onFailureCallback</dd>
<dt>返回:</dt>
<dd>int - Connection ID</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="partialDepthStream(java.lang.String,int,int,com.binance.connector.futures.client.utils.WebSocketCallback)">
<h3>partialDepthStream</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">partialDepthStream</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;symbol,
 int&nbsp;levels,
 int&nbsp;speed,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback)</span></div>
<div class="block">Top bids and asks, Valid are 5, 10, or 20.
 <br><br>
 &lt;symbol&gt;@depth&lt;levels&gt;@&lt;speed&gt;ms
 <br><br>
 Update Speed: 250ms, 500ms or 100ms</div>
<dl class="notes">
<dt>指定者:</dt>
<dd><code><a href="../WebsocketClient.html#partialDepthStream(java.lang.String,int,int,com.binance.connector.futures.client.utils.WebSocketCallback)">partialDepthStream</a></code>&nbsp;在接口中&nbsp;<code><a href="../WebsocketClient.html" title="com.binance.connector.futures.client中的接口">WebsocketClient</a></code></dd>
<dt>参数:</dt>
<dd><code>symbol</code> - trading symbol</dd>
<dd><code>levels</code> - order book depth level, can be 5, 10, or 20</dd>
<dd><code>speed</code> - update speed  in ms, can be 250, 500 or 100</dd>
<dd><code>onMessageCallback</code> - onMessageCallback</dd>
<dt>返回:</dt>
<dd>int - Connection ID</dd>
<dt>另请参阅:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://developers.binance.com/docs/derivatives/usds-margined-futures/websocket-market-streams/Partial-Book-Depth-Streams">
 https://developers.binance.com/docs/derivatives/usds-margined-futures/websocket-market-streams/Partial-Book-Depth-Streams</a></li>
<li><a href="https://developers.binance.com/docs/derivatives/coin-margined-futures/websocket-market-streams/Partial-Book-Depth-Streams">
 https://developers.binance.com/docs/derivatives/coin-margined-futures/websocket-market-streams/Partial-Book-Depth-Streams</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="partialDepthStream(java.lang.String,int,int,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)">
<h3>partialDepthStream</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">partialDepthStream</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;symbol,
 int&nbsp;levels,
 int&nbsp;speed,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onOpenCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onClosingCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onFailureCallback)</span></div>
<div class="block">Same as <a href="#partialDepthStream(java.lang.String,int,int,com.binance.connector.futures.client.utils.WebSocketCallback)"><code>partialDepthStream(String, int, int, WebSocketCallback)</code></a> plus accepts callbacks for all major websocket connection events.</div>
<dl class="notes">
<dt>指定者:</dt>
<dd><code><a href="../WebsocketClient.html#partialDepthStream(java.lang.String,int,int,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)">partialDepthStream</a></code>&nbsp;在接口中&nbsp;<code><a href="../WebsocketClient.html" title="com.binance.connector.futures.client中的接口">WebsocketClient</a></code></dd>
<dt>参数:</dt>
<dd><code>symbol</code> - trading symbol</dd>
<dd><code>levels</code> - order book depth level, can be 5, 10, or 20</dd>
<dd><code>speed</code> - update speed in ms, can be 250, 500 or 100</dd>
<dd><code>onOpenCallback</code> - onOpenCallback</dd>
<dd><code>onMessageCallback</code> - onMessageCallback</dd>
<dd><code>onClosingCallback</code> - onClosingCallback</dd>
<dd><code>onFailureCallback</code> - onFailureCallback</dd>
<dt>返回:</dt>
<dd>int - Connection ID</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="diffDepthStream(java.lang.String,int,com.binance.connector.futures.client.utils.WebSocketCallback)">
<h3>diffDepthStream</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">diffDepthStream</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;symbol,
 int&nbsp;speed,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback)</span></div>
<div class="block">Bids and asks, pushed every 250 milliseconds, 500 milliseconds, 100 milliseconds (if existing)
 <br><br>
 &lt;symbol&gt;@depth@&lt;speed&gt;ms
 <br><br>
 Update Speed: 250ms, 500ms, 100ms</div>
<dl class="notes">
<dt>指定者:</dt>
<dd><code><a href="../WebsocketClient.html#diffDepthStream(java.lang.String,int,com.binance.connector.futures.client.utils.WebSocketCallback)">diffDepthStream</a></code>&nbsp;在接口中&nbsp;<code><a href="../WebsocketClient.html" title="com.binance.connector.futures.client中的接口">WebsocketClient</a></code></dd>
<dt>参数:</dt>
<dd><code>symbol</code> - trading symbol</dd>
<dd><code>speed</code> - update speed in ms, can be 250, 500 or 100</dd>
<dd><code>onMessageCallback</code> - onMessageCallback</dd>
<dt>返回:</dt>
<dd>int - Connection ID</dd>
<dt>另请参阅:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://developers.binance.com/docs/derivatives/usds-margined-futures/websocket-market-streams/Diff-Book-Depth-Streams">
 https://developers.binance.com/docs/derivatives/usds-margined-futures/websocket-market-streams/Diff-Book-Depth-Streams</a></li>
<li><a href="https://developers.binance.com/docs/derivatives/coin-margined-futures/websocket-market-streams/Diff-Book-Depth-Streams">
 https://developers.binance.com/docs/derivatives/coin-margined-futures/websocket-market-streams/Diff-Book-Depth-Streams</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="diffDepthStream(java.lang.String,int,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)">
<h3>diffDepthStream</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">diffDepthStream</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;symbol,
 int&nbsp;speed,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onOpenCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onClosingCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onFailureCallback)</span></div>
<div class="block">Same as <a href="#diffDepthStream(java.lang.String,int,com.binance.connector.futures.client.utils.WebSocketCallback)"><code>diffDepthStream(String, int, WebSocketCallback)</code></a> plus accepts callbacks for all major websocket connection events.</div>
<dl class="notes">
<dt>指定者:</dt>
<dd><code><a href="../WebsocketClient.html#diffDepthStream(java.lang.String,int,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)">diffDepthStream</a></code>&nbsp;在接口中&nbsp;<code><a href="../WebsocketClient.html" title="com.binance.connector.futures.client中的接口">WebsocketClient</a></code></dd>
<dt>参数:</dt>
<dd><code>symbol</code> - trading symbol</dd>
<dd><code>speed</code> - update speed in ms, can be 250, 500 or 100</dd>
<dd><code>onOpenCallback</code> - onOpenCallback</dd>
<dd><code>onMessageCallback</code> - onMessageCallback</dd>
<dd><code>onClosingCallback</code> - onClosingCallback</dd>
<dd><code>onFailureCallback</code> - onFailureCallback</dd>
<dt>返回:</dt>
<dd>int - Connection ID</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="listenUserStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)">
<h3>listenUserStream</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">listenUserStream</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;listenKey,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback)</span></div>
<div class="block">User Data Streams are accessed at /ws/&lt;listenKey&gt;</div>
<dl class="notes">
<dt>指定者:</dt>
<dd><code><a href="../WebsocketClient.html#listenUserStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)">listenUserStream</a></code>&nbsp;在接口中&nbsp;<code><a href="../WebsocketClient.html" title="com.binance.connector.futures.client中的接口">WebsocketClient</a></code></dd>
<dt>参数:</dt>
<dd><code>listenKey</code> - listen key</dd>
<dd><code>onMessageCallback</code> - onMessageCallback</dd>
<dt>返回:</dt>
<dd>int - Connection ID</dd>
<dt>另请参阅:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://developers.binance.com/docs/derivatives/usds-margined-futures/user-data-streams/Connect">
 https://developers.binance.com/docs/derivatives/usds-margined-futures/user-data-streams/Connect</a></li>
<li><a href="https://developers.binance.com/docs/derivatives/coin-margined-futures/user-data-streams/Connect">
 https://developers.binance.com/docs/derivatives/coin-margined-futures/user-data-streams/Connect</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="listenUserStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)">
<h3>listenUserStream</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">listenUserStream</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;listenKey,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onOpenCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onClosingCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onFailureCallback)</span></div>
<div class="block">Same as <a href="#listenUserStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback)"><code>listenUserStream(String, WebSocketCallback)</code></a> plus accepts callbacks for all major websocket connection events.</div>
<dl class="notes">
<dt>指定者:</dt>
<dd><code><a href="../WebsocketClient.html#listenUserStream(java.lang.String,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)">listenUserStream</a></code>&nbsp;在接口中&nbsp;<code><a href="../WebsocketClient.html" title="com.binance.connector.futures.client中的接口">WebsocketClient</a></code></dd>
<dt>参数:</dt>
<dd><code>listenKey</code> - listen key</dd>
<dd><code>onOpenCallback</code> - onOpenCallback</dd>
<dd><code>onMessageCallback</code> - onMessageCallback</dd>
<dd><code>onClosingCallback</code> - onClosingCallback</dd>
<dd><code>onFailureCallback</code> - onFailureCallback</dd>
<dt>返回:</dt>
<dd>int - Connection ID</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="combineStreams(java.util.ArrayList,com.binance.connector.futures.client.utils.WebSocketCallback)">
<h3>combineStreams</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">combineStreams</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/util/ArrayList.html" title="java.util中的类或接口" class="external-link">ArrayList</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&gt;&nbsp;streams,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback)</span></div>
<div class="block">Combined streams are accessed at /stream?streams=&lt;streamName1&gt;/&lt;streamName2&gt;/&lt;streamName3&gt;</div>
<dl class="notes">
<dt>指定者:</dt>
<dd><code><a href="../WebsocketClient.html#combineStreams(java.util.ArrayList,com.binance.connector.futures.client.utils.WebSocketCallback)">combineStreams</a></code>&nbsp;在接口中&nbsp;<code><a href="../WebsocketClient.html" title="com.binance.connector.futures.client中的接口">WebsocketClient</a></code></dd>
<dt>参数:</dt>
<dd><code>streams</code> - A list of stream names to be combined <br></dd>
<dd><code>onMessageCallback</code> - onMessageCallback</dd>
<dt>返回:</dt>
<dd>int - Connection ID</dd>
<dt>另请参阅:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://developers.binance.com/docs/derivatives/usds-margined-futures/websocket-market-streams/Connect">
 https://developers.binance.com/docs/derivatives/usds-margined-futures/websocket-market-streams/Connect</a></li>
<li><a href="https://developers.binance.com/docs/derivatives/coin-margined-futures/websocket-market-streams/Connect">
 https://developers.binance.com/docs/derivatives/coin-margined-futures/websocket-market-streams/Connect</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="combineStreams(java.util.ArrayList,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)">
<h3>combineStreams</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">combineStreams</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/util/ArrayList.html" title="java.util中的类或接口" class="external-link">ArrayList</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&gt;&nbsp;streams,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onOpenCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onClosingCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onFailureCallback)</span></div>
<div class="block">Same as <a href="#combineStreams(java.util.ArrayList,com.binance.connector.futures.client.utils.WebSocketCallback)"><code>combineStreams(ArrayList, WebSocketCallback)</code></a> plus accepts callbacks for all major websocket connection events.</div>
<dl class="notes">
<dt>指定者:</dt>
<dd><code><a href="../WebsocketClient.html#combineStreams(java.util.ArrayList,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback)">combineStreams</a></code>&nbsp;在接口中&nbsp;<code><a href="../WebsocketClient.html" title="com.binance.connector.futures.client中的接口">WebsocketClient</a></code></dd>
<dt>参数:</dt>
<dd><code>streams</code> - stream name list</dd>
<dd><code>onOpenCallback</code> - onOpenCallback</dd>
<dd><code>onMessageCallback</code> - onMessageCallback</dd>
<dd><code>onClosingCallback</code> - onClosingCallback</dd>
<dd><code>onFailureCallback</code> - onFailureCallback</dd>
<dt>返回:</dt>
<dd>int - Connection ID</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="closeConnection(int)">
<h3>closeConnection</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">closeConnection</span><wbr><span class="parameters">(int&nbsp;connectionId)</span></div>
<div class="block">Closes a specific stream based on stream Id.</div>
<dl class="notes">
<dt>指定者:</dt>
<dd><code><a href="../WebsocketClient.html#closeConnection(int)">closeConnection</a></code>&nbsp;在接口中&nbsp;<code><a href="../WebsocketClient.html" title="com.binance.connector.futures.client中的接口">WebsocketClient</a></code></dd>
<dt>参数:</dt>
<dd><code>connectionId</code> - Connection ID</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="closeAllConnections()">
<h3>closeAllConnections</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">closeAllConnections</span>()</div>
<div class="block">Closes all streams</div>
<dl class="notes">
<dt>指定者:</dt>
<dd><code><a href="../WebsocketClient.html#closeAllConnections()">closeAllConnections</a></code>&nbsp;在接口中&nbsp;<code><a href="../WebsocketClient.html" title="com.binance.connector.futures.client中的接口">WebsocketClient</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createConnection(com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,com.binance.connector.futures.client.utils.WebSocketCallback,okhttp3.Request)">
<h3>createConnection</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">createConnection</span><wbr><span class="parameters">(com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onOpenCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onMessageCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onClosingCallback,
 com.binance.connector.futures.client.utils.WebSocketCallback&nbsp;onFailureCallback,
 okhttp3.Request&nbsp;request)</span></div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &#169; 2025. All rights reserved.</small></p>
</footer>
</div>
</div>
</body>
</html>
