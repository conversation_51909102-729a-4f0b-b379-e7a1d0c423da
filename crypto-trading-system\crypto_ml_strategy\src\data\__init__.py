"""
数据访问模块

包含完整的数据访问层组件，支持多种数据源、缓存、实时处理和数据验证。

模块组织：
- 配置管理：DataAccessConfig, DataSourceConfig, APIConfig, DatabaseConfig, KafkaConfig, CacheConfig
- 数据访问：JavaAPIClient, DatabaseConnector, DatabaseManager
- 缓存管理：CacheManager, MemoryCache
- 实时处理：RealtimeProcessor, KafkaProcessor, WebSocketProcessor, MessageHandler
- 数据验证：DataValidator, ValidationRule, ValidationReport
- 统一服务：DataAccessService
- 多时间周期处理：MultiTimeframeProcessor, TimeSeriesSynchronizer, DataQualityChecker, MultiTimeframeManager

版本：2.0.0
更新日期：2024-12-19
"""

# 配置管理
from .data_config import (
    DataAccessConfig,
    DataSourceConfig, 
    APIConfig,
    DatabaseConfig,
    KafkaConfig,
    CacheConfig,
    DataSourceType,
    DatabaseType,
    CacheType,
    DataFormat,
    DEFAULT_API_CONFIG,
    DEFAULT_DATABASE_CONFIG,
    DEFAULT_KAFKA_CONFIG,
    DEFAULT_CACHE_CONFIG,
    DEFAULT_DATA_ACCESS_CONFIG
)

# API客户端
from .clients.api_client import JavaAPIClient
from .clients.api_client import JavaAPIClient as ApiClient

# 数据库连接
from .clients.database_connector import DatabaseConnector, DatabaseManager, ConnectionStats

# 缓存管理
from .cache.cache_manager import (
    CacheManager,
    MemoryCache,
    CacheStrategy,
    CacheEntry,
    CacheStats
)

# 实时数据处理
from .realtime_processor import (
    RealtimeProcessor,
    KafkaProcessor,
    WebSocketProcessor,
    MessageHandler,
    StreamMessage,
    StreamType,
    MessageType,
    StreamStats
)

# 数据验证
from .quality.data_validator import (
    DataValidator,
    ValidationRule,
    ValidationReport,
    ValidationLevel,
    ValidationResult
)

# 数据质量检查（现有组件）
from .quality.data_quality_checker import (
    DataQualityChecker,
    QualityMetrics,
    QualityIssue,
    QualityLevel,
    FillStrategy
)

# 多时间周期处理（现有组件）
from .sync.multi_timeframe_processor import MultiTimeframeProcessor, TimeframeConfig
from .sync.time_series_synchronizer import (
    TimeSeriesSynchronizer,
    SyncConfig,
    SyncMethod,
    DataSource
)
from .sync.multi_timeframe_manager import (
    MultiTimeframeManager,
    ProcessingConfig,
    ProcessingResult,
    ProcessingMode,
    FusionMethod
)

# 统一数据访问服务
from .data_access_service import (
    DataAccessService,
    ServiceStatus,
    CircuitBreaker,
    CircuitBreakerState,
    ServiceMetrics
)

# 导出所有公共接口
__all__ = [
    # 配置管理
    'DataAccessConfig',
    'DataSourceConfig',
    'APIConfig', 
    'DatabaseConfig',
    'KafkaConfig',
    'CacheConfig',
    'DataSourceType',
    'DatabaseType',
    'CacheType',
    'DataFormat',
    'DEFAULT_API_CONFIG',
    'DEFAULT_DATABASE_CONFIG', 
    'DEFAULT_KAFKA_CONFIG',
    'DEFAULT_CACHE_CONFIG',
    'DEFAULT_DATA_ACCESS_CONFIG',
    
    # 数据访问层
    'JavaAPIClient',
    'ApiClient',
    'DatabaseConnector',
    'DatabaseManager',
    'ConnectionStats',
    
    # 缓存管理
    'CacheManager',
    'MemoryCache',
    'CacheStrategy',
    'CacheEntry',
    'CacheStats',
    
    # 实时数据处理
    'RealtimeProcessor',
    'KafkaProcessor',
    'WebSocketProcessor',
    'MessageHandler',
    'StreamMessage',
    'StreamType',
    'MessageType',
    'StreamStats',
    
    # 数据验证
    'DataValidator',
    'ValidationRule',
    'ValidationReport',
    'ValidationLevel',
    'ValidationResult',
    
    # 数据质量检查
    'DataQualityChecker',
    'QualityMetrics',
    'QualityIssue',
    'QualityLevel',
    'FillStrategy',
    
    # 多时间周期处理
    'MultiTimeframeProcessor',
    'TimeframeConfig',
    'TimeSeriesSynchronizer',
    'SyncConfig',
    'SyncMethod',
    'DataSource',
    'MultiTimeframeManager',
    'ProcessingConfig',
    'ProcessingResult',
    'ProcessingMode',
    'FusionMethod',
    
    # 统一数据访问服务
    'DataAccessService',
    'ServiceStatus',
    'CircuitBreaker',
    'CircuitBreakerState',
    'ServiceMetrics'
]

# 便捷创建函数
def create_data_access_service(config: DataAccessConfig = None) -> DataAccessService:
    """
    创建数据访问服务实例
    
    Args:
        config: 数据访问配置，如果为None则使用默认配置
        
    Returns:
        数据访问服务实例
    """
    if config is None:
        config = DEFAULT_DATA_ACCESS_CONFIG
    
    return DataAccessService(config)


def create_default_config() -> DataAccessConfig:
    """
    创建默认的数据访问配置
    
    Returns:
        默认数据访问配置
    """
    return DEFAULT_DATA_ACCESS_CONFIG


def create_api_client(config: APIConfig = None) -> JavaAPIClient:
    """
    创建API客户端
    
    Args:
        config: API配置，如果为None则使用默认配置
        
    Returns:
        API客户端实例
    """
    if config is None:
        config = DEFAULT_API_CONFIG
    
    return JavaAPIClient(config)


def create_cache_manager(config: CacheConfig = None) -> CacheManager:
    """
    创建缓存管理器
    
    Args:
        config: 缓存配置，如果为None则使用默认配置
        
    Returns:
        缓存管理器实例
    """
    if config is None:
        config = DEFAULT_CACHE_CONFIG
    
    return CacheManager(config)


def create_data_validator(config: dict = None) -> DataValidator:
    """
    创建数据验证器
    
    Args:
        config: 验证器配置
        
    Returns:
        数据验证器实例
    """
    return DataValidator(config)


# 版本信息
__version__ = '2.0.0'
__author__ = 'Crypto ML Strategy Team'
__description__ = 'Comprehensive data access layer with multi-source support, caching, real-time processing, and validation'

# 模块初始化检查
def _check_dependencies():
    """检查依赖项"""
    try:
        import pandas as pd
        import numpy as np
        import asyncio
        import aiohttp
        return True
    except ImportError as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.warning(f"某些依赖项缺失: {e}")
        return False

# 执行依赖检查
_dependencies_ok = _check_dependencies()