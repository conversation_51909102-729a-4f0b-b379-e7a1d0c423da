#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Kafka客户端模块，负责与Kafka进行通信，包括消费市场数据和发布策略信号。
"""

import json
import logging
import threading
import time
from typing import Callable, Dict, List, Optional, Any, Union

from confluent_kafka import Consumer, Producer, KafkaError, KafkaException

from core.config import Config


class KafkaClient:
    """
    Kafka客户端类，负责与Kafka进行通信，包括消费市场数据和发布策略信号。
    """

    def __init__(self, config: Config):
        """
        初始化Kafka客户端。

        Args:
            config: 配置对象，包含Kafka连接和主题配置
        """
        self.logger = logging.getLogger('cryptoMlStrategy.kafka_client')
        self.config = config
        self.bootstrap_servers = config.get('kafka', 'bootstrap_servers')
        self.kline_topic = config.get('kafka', 'kline_topic')
        self.depth_topic = config.get('kafka', 'depth_topic')
        self.trade_topic = config.get('kafka', 'trade_topic')
        self.signal_topic = config.get('kafka', 'signal_topic')
        self.group_id = config.get('kafka', 'group_id')
        self.auto_offset_reset = config.get('kafka', 'auto_offset_reset')
        self.enable_auto_commit = config.get_bool('kafka', 'enable_auto_commit')
        self.auto_commit_interval_ms = config.get_int('kafka', 'auto_commit_interval_ms')
        self.max_poll_records = config.get_int('kafka', 'max_poll_records')
        self.session_timeout_ms = config.get_int('kafka', 'session_timeout_ms')
        
        # Kafka生产者
        self.producer_config = {
            'bootstrap.servers': self.bootstrap_servers,
            'client.id': f'{self.group_id}-producer',
        }
        self.producer = self._create_producer()

        # Kafka消费者
        self.consumer_config = {
            'bootstrap.servers': self.bootstrap_servers,
            'group.id': self.group_id,
            'auto.offset.reset': self.auto_offset_reset,
            'enable.auto.commit': self.enable_auto_commit,
            'auto.commit.interval.ms': self.auto_commit_interval_ms,
            'session.timeout.ms': self.session_timeout_ms,
        }
        self.consumers = {}
        self.consumer_threads = {}
        self.running = False
    
    def _create_producer(self) -> Producer:
        """
        创建Kafka生产者。

        Returns:
            Kafka Producer实例
        """
        try:
            return Producer(self.producer_config)
        except KafkaException as e:
            self.logger.error(f"创建Kafka生产者失败: {e}")
            raise

    def _create_consumer(self, topics: List[str]) -> Consumer:
        """
        创建Kafka消费者。

        Args:
            topics: 需要订阅的主题列表

        Returns:
            Kafka Consumer实例
        """
        try:
            consumer = Consumer(self.consumer_config)
            consumer.subscribe(topics)
            return consumer
        except KafkaException as e:
            self.logger.error(f"创建Kafka消费者失败: {e}")
            raise

    def start_consuming(self, 
                        topic: str, 
                        callback: Callable[[Dict], None], 
                        error_callback: Optional[Callable[[KafkaError], None]] = None) -> None:
        """
        开始消费指定主题的消息。

        Args:
            topic: 要消费的主题
            callback: 处理消息的回调函数
            error_callback: 处理错误的回调函数（可选）
        """
        if topic in self.consumers:
            self.logger.warning(f"已经在消费主题: {topic}")
            return
        
        try:
            consumer = self._create_consumer([topic])
            self.consumers[topic] = consumer
            self.running = True
            
            def consume_loop():
                while self.running:
                    try:
                        msg = consumer.poll(1.0)
                        if msg is None:
                            continue
                        if msg.error():
                            if error_callback:
                                error_callback(msg.error())
                            else:
                                self.logger.error(f"消费消息错误: {msg.error()}")
                            continue
                        
                        try:
                            value = json.loads(msg.value().decode('utf-8'))
                            callback(value)
                        except json.JSONDecodeError as e:
                            self.logger.error(f"JSON解析错误: {e}, 消息: {msg.value()[:100]}")
                        except Exception as e:
                            self.logger.error(f"消息处理错误: {e}")
                        
                    except KafkaException as e:
                        self.logger.error(f"Kafka异常: {e}")
                        time.sleep(1)  # 避免在错误情况下无限循环消耗CPU
            
            # 创建消费者线程
            consumer_thread = threading.Thread(
                target=consume_loop,
                name=f"kafka-consumer-{topic}",
                daemon=True
            )
            consumer_thread.start()
            self.consumer_threads[topic] = consumer_thread
            self.logger.info(f"开始消费主题: {topic}")
        
        except Exception as e:
            self.logger.error(f"启动Kafka消费者失败: {e}")
            raise

    def stop_consuming(self, topic: Optional[str] = None) -> None:
        """
        停止消费指定主题的消息，如果不指定主题，则停止所有消费。

        Args:
            topic: 要停止消费的主题（可选）
        """
        if topic:
            if topic in self.consumers:
                self.logger.info(f"停止消费主题: {topic}")
                self.consumers[topic].close()
                del self.consumers[topic]
                if topic in self.consumer_threads:
                    del self.consumer_threads[topic]
        else:
            self.logger.info("停止所有Kafka消费者")
            self.running = False
            for t, consumer in self.consumers.items():
                consumer.close()
            self.consumers = {}
            self.consumer_threads = {}

    def publish_signal(self, signal: Dict[str, Any]) -> None:
        """
        发布策略信号到Kafka。

        Args:
            signal: 策略信号字典
        """
        try:
            message = json.dumps(signal).encode('utf-8')
            self.producer.produce(
                self.signal_topic,
                value=message,
                key=signal.get('data', {}).get('symbol', '').encode('utf-8'),
                callback=self._delivery_report
            )
            # 立即刷新生产者
            self.producer.flush()
            self.logger.info(f"发布信号到主题 {self.signal_topic}: {signal['messageType']} - {signal.get('data', {}).get('symbol', '')}")
        
        except Exception as e:
            self.logger.error(f"发布信号失败: {e}")
    
    def _delivery_report(self, err, msg):
        """
        消息发送回调函数。

        Args:
            err: 错误对象，如果没有错误则为None
            msg: 消息对象
        """
        if err is not None:
            self.logger.error(f"消息发送失败: {err}")
        else:
            self.logger.debug(f"消息发送成功: {msg.topic()} [{msg.partition()}] @ {msg.offset()}")
    
    def clean_up(self) -> None:
        """
        清理资源，关闭所有连接。
        """
        self.logger.info("清理Kafka资源")
        self.stop_consuming()
        if self.producer:
            self.producer.flush()