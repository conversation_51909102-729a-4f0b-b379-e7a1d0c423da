package com.crypto.trading.common.enums;

/**
 * K线周期枚举
 * <p>
 * 表示K线图的时间周期
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public enum KlineInterval {

    /**
     * 1分钟
     */
    MINUTE_1("1m", "1分钟", 60 * 1000L),

    /**
     * 3分钟
     */
    MINUTE_3("3m", "3分钟", 3 * 60 * 1000L),

    /**
     * 5分钟
     */
    MINUTE_5("5m", "5分钟", 5 * 60 * 1000L),

    /**
     * 15分钟
     */
    MINUTE_15("15m", "15分钟", 15 * 60 * 1000L),

    /**
     * 30分钟
     */
    MINUTE_30("30m", "30分钟", 30 * 60 * 1000L),

    /**
     * 1小时
     */
    HOUR_1("1h", "1小时", 60 * 60 * 1000L),

    /**
     * 2小时
     */
    HOUR_2("2h", "2小时", 2 * 60 * 60 * 1000L),

    /**
     * 4小时
     */
    HOUR_4("4h", "4小时", 4 * 60 * 60 * 1000L),

    /**
     * 6小时
     */
    HOUR_6("6h", "6小时", 6 * 60 * 60 * 1000L),

    /**
     * 8小时
     */
    HOUR_8("8h", "8小时", 8 * 60 * 60 * 1000L),

    /**
     * 12小时
     */
    HOUR_12("12h", "12小时", 12 * 60 * 60 * 1000L),

    /**
     * 1天
     */
    DAY_1("1d", "1天", 24 * 60 * 60 * 1000L),

    /**
     * 3天
     */
    DAY_3("3d", "3天", 3 * 24 * 60 * 60 * 1000L),

    /**
     * 1周
     */
    WEEK_1("1w", "1周", 7 * 24 * 60 * 60 * 1000L),

    /**
     * 1月
     */
    MONTH_1("1M", "1月", 30 * 24 * 60 * 60 * 1000L);

    /**
     * 代码
     */
    private final String code;

    /**
     * 描述
     */
    private final String desc;

    /**
     * 毫秒数
     */
    private final long milliseconds;

    /**
     * 构造函数
     *
     * @param code         代码
     * @param desc         描述
     * @param milliseconds 毫秒数
     */
    KlineInterval(String code, String desc, long milliseconds) {
        this.code = code;
        this.desc = desc;
        this.milliseconds = milliseconds;
    }

    /**
     * 获取代码
     *
     * @return 代码
     */
    public String getCode() {
        return code;
    }

    /**
     * 获取描述
     *
     * @return 描述
     */
    public String getDesc() {
        return desc;
    }

    /**
     * 获取毫秒数
     *
     * @return 毫秒数
     */
    public long getMilliseconds() {
        return milliseconds;
    }

    /**
     * 获取秒数
     *
     * @return 秒数
     */
    public long getSeconds() {
        return milliseconds / 1000;
    }

    /**
     * 根据代码获取枚举值
     *
     * @param code 代码
     * @return 枚举值
     */
    public static KlineInterval fromCode(String code) {
        for (KlineInterval interval : KlineInterval.values()) {
            if (interval.getCode().equals(code)) {
                return interval;
            }
        }
        throw new IllegalArgumentException("未知的K线周期代码: " + code);
    }
}