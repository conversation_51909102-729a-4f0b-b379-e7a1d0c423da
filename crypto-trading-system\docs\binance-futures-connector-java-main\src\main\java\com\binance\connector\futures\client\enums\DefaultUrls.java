package com.binance.connector.futures.client.enums;

public final class DefaultUrls {
    // 测试网络环境REST API地址
    public static final String TESTNET_URL = "https://testnet.binancefuture.com";
    // 测试网络环境WebSocket流地址
    public static final String TESTNET_WSS_URL = "wss://stream.binancefuture.com";

    //USD-M Futures
    // USD保证金期货REST API生产环境地址
    public static final String USDM_PROD_URL = "https://fapi.binance.com";
    // USD保证金期货WebSocket流生产环境地址
    public static final String USDM_WS_URL = "wss://fstream.binance.com";

    //COIN-M Futures
    // 币本位期货REST API生产环境地址
    public static final String COINM_PROD_URL = "https://dapi.binance.com";
    // 币本位期货WebSocket流生产环境地址
    public static final String COINM_WS_URL = "wss://dstream.binance.com";

    // 私有构造函数防止类实例化
    private DefaultUrls() {
    }
}
