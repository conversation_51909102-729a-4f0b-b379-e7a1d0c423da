package com.crypto.trading.sdk.websocket;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;

import java.net.URI;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Consumer;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * WebSocket连接池测试类
 */
public class WebSocketConnectionPoolTest {

    private WebSocketConnectionPool connectionPool;
    
    @Mock
    private WebSocketConnection mockConnection;
    
    @Mock
    private Consumer<String> messageHandler;
    
    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        connectionPool = new WebSocketConnectionPool();
    }
    
    @Test
    public void testAddConnectionToPool() throws Exception {
        // 获取connections字段
        Map<Integer, WebSocketConnection> connections = 
            (Map<Integer, WebSocketConnection>) ReflectionTestUtils.getField(connectionPool, "connections");
        
        // 添加连接
        connections.put(1, mockConnection);
        
        // 验证连接是否被添加
        WebSocketConnection retrievedConnection = connectionPool.getConnection(1);
        assertSame(mockConnection, retrievedConnection);
    }
    
    @Test
    public void testCloseAndRemoveConnection() throws Exception {
        // 获取connections字段
        Map<Integer, WebSocketConnection> connections = 
            (Map<Integer, WebSocketConnection>) ReflectionTestUtils.getField(connectionPool, "connections");
        
        // 添加连接
        connections.put(1, mockConnection);
        
        // 验证连接是否被添加
        WebSocketConnection retrievedConnection = connectionPool.getConnection(1);
        assertSame(mockConnection, retrievedConnection);
        
        // 关闭连接
        connectionPool.closeConnection(1);
        
        // 验证连接被关闭和移除
        verify(mockConnection, times(1)).close();
        assertNull(connectionPool.getConnection(1));
    }
    
    @Test
    public void testGetNonExistentConnection() {
        // 测试获取不存在的连接
        WebSocketConnection connection = connectionPool.getConnection(999);
        assertNull(connection);
    }
    
    @Test
    public void testCreateConnection() throws Exception {
        // 使用try-with-resources块确保资源释放
        try (MockedStatic<WebSocketConnection> mockedStatic = Mockito.mockStatic(WebSocketConnection.class)) {
            // 创建模拟连接对象
            WebSocketConnection mockConnection = mock(WebSocketConnection.class);
            
            // 配置静态mock返回我们的mock对象
            mockedStatic.when(() -> WebSocketConnection.create(
                    anyInt(), any(URI.class), any(Consumer.class), 
                    any(Runnable.class), any(Consumer.class), any(Consumer.class)))
                .thenReturn(mockConnection);
            
            // 创建测试使用的URI
            URI uri = new URI("ws://localhost:8080/ws");
            
            // 执行测试方法
            int connectionId = connectionPool.createConnection(
                    uri, message -> {}, 
                    () -> {}, statusCode -> {}, 
                    throwable -> {});
            
            // 验证结果
            assertEquals(1, connectionId);
            
            // 验证是否调用了正确的方法
            mockedStatic.verify(() -> WebSocketConnection.create(
                    eq(1), eq(uri), any(Consumer.class), 
                    any(Runnable.class), any(Consumer.class), any(Consumer.class)));
            
            // 验证连接是否被添加到池中
            assertTrue(connectionPool.hasConnection(connectionId));
        }
    }
    
    @Test
    public void testCloseConnection() throws Exception {
        // 获取connections字段
        Map<Integer, WebSocketConnection> connections = 
            (Map<Integer, WebSocketConnection>) ReflectionTestUtils.getField(connectionPool, "connections");
        
        // 添加连接
        connections.put(1, mockConnection);
        
        // 关闭连接
        connectionPool.closeConnection(1);
        
        // 验证连接的close方法被调用
        verify(mockConnection, times(1)).close();
    }
    
    @Test
    public void testCloseAllConnections() throws Exception {
        // 获取connections字段
        Map<Integer, WebSocketConnection> connections = 
            (Map<Integer, WebSocketConnection>) ReflectionTestUtils.getField(connectionPool, "connections");
        
        // 创建mock连接
        WebSocketConnection mockConnection1 = mock(WebSocketConnection.class);
        WebSocketConnection mockConnection2 = mock(WebSocketConnection.class);
        
        // 添加连接
        connections.put(1, mockConnection1);
        connections.put(2, mockConnection2);
        
        // 关闭所有连接
        connectionPool.closeAllConnections();
        
        // 验证所有连接的close方法被调用
        verify(mockConnection1, times(1)).close();
        verify(mockConnection2, times(1)).close();
        
        // 验证连接池已清空
        assertEquals(0, connections.size());
    }
    
    @Test
    public void testConnectionIdGenerator() throws Exception {
        // 重新创建connectionPool，确保连接ID生成器从1开始
        connectionPool = new WebSocketConnectionPool();
        
        // 使用try-with-resources块确保资源释放
        try (MockedStatic<WebSocketConnection> mockedStatic = Mockito.mockStatic(WebSocketConnection.class)) {
            // 创建模拟连接对象
            WebSocketConnection mockConnection1 = mock(WebSocketConnection.class);
            WebSocketConnection mockConnection2 = mock(WebSocketConnection.class);
            
            // 设置连接为活跃状态
            when(mockConnection1.isActive()).thenReturn(true);
            when(mockConnection2.isActive()).thenReturn(true);
            
            // 配置静态mock返回我们的mock对象
            mockedStatic.when(() -> WebSocketConnection.create(
                    anyInt(), any(URI.class), any(Consumer.class), 
                    any(Runnable.class), any(Consumer.class), any(Consumer.class)))
                .thenReturn(mockConnection1, mockConnection2);
            
            // 创建测试使用的URI
            URI uri = new URI("ws://localhost:8080/ws");
            
            // 执行测试方法 - 创建两个连接
            int id1 = connectionPool.createConnection(
                    uri, message -> {}, 
                    () -> {}, statusCode -> {}, 
                    throwable -> {});
            
            int id2 = connectionPool.createConnection(
                    uri, message -> {}, 
                    () -> {}, statusCode -> {}, 
                    throwable -> {});
            
            // 验证连接ID是递增的
            assertEquals(1, id1);
            assertEquals(2, id2);
            
            // 验证连接池中有这两个连接
            assertTrue(connectionPool.hasConnection(id1));
            assertTrue(connectionPool.hasConnection(id2));
            
            // 获取连接总数
            int totalConnections = connectionPool.getTotalConnectionCount();
            assertEquals(2, totalConnections);
            
            // 获取活跃连接数
            int activeConnections = connectionPool.getActiveConnectionCount();
            assertEquals(2, activeConnections);
        }
    }
    
    @Test
    public void testGetActiveConnectionCount() throws Exception {
        // 获取connections字段
        Map<Integer, WebSocketConnection> connections = 
            (Map<Integer, WebSocketConnection>) ReflectionTestUtils.getField(connectionPool, "connections");
        
        // 创建mock连接
        WebSocketConnection mockConnection1 = mock(WebSocketConnection.class);
        WebSocketConnection mockConnection2 = mock(WebSocketConnection.class);
        
        // 设置连接状态
        when(mockConnection1.isActive()).thenReturn(true);
        when(mockConnection2.isActive()).thenReturn(false);
        
        // 添加连接
        connections.put(1, mockConnection1);
        connections.put(2, mockConnection2);
        
        // 验证活跃连接数
        assertEquals(1, connectionPool.getActiveConnectionCount());
    }
    
    @Test
    public void testGetTotalConnectionCount() throws Exception {
        // 获取connections字段
        Map<Integer, WebSocketConnection> connections = 
            (Map<Integer, WebSocketConnection>) ReflectionTestUtils.getField(connectionPool, "connections");
        
        // 添加连接
        connections.put(1, mock(WebSocketConnection.class));
        connections.put(2, mock(WebSocketConnection.class));
        
        // 验证总连接数
        assertEquals(2, connectionPool.getTotalConnectionCount());
    }
    
    @Test
    public void testSendMessage() throws Exception {
        // 获取connections字段
        Map<Integer, WebSocketConnection> connections = 
            (Map<Integer, WebSocketConnection>) ReflectionTestUtils.getField(connectionPool, "connections");
        
        // 设置mock连接行为
        when(mockConnection.isActive()).thenReturn(true);
        when(mockConnection.sendMessage(anyString())).thenReturn(true);
        
        // 添加连接
        connections.put(1, mockConnection);
        
        // 发送消息
        boolean result = connectionPool.sendMessage(1, "test message");
        
        // 验证消息发送成功
        assertTrue(result);
        verify(mockConnection, times(1)).sendMessage("test message");
    }
} 