<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (21) on Fri Apr 04 16:06:08 CST 2025 -->
<title>CMFuturesClientImpl (io.github.binance:binance-futures-connector-java 3.0.5 API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-04-04">
<meta name="description" content="declaration: package: com.binance.connector.futures.client.impl, class: CMFuturesClientImpl">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="切换导航链接"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="../../../../../../index.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="nav-bar-cell1-rev">类</li>
<li><a href="class-use/CMFuturesClientImpl.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../../index-all.html">索引</a></li>
<li><a href="../../../../../../help-doc.html#class">帮助</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>概要:</p>
<ul>
<li>嵌套</li>
<li>字段</li>
<li><a href="#constructor-summary">构造器</a></li>
<li><a href="#method-summary">方法</a></li>
</ul>
</li>
<li>
<p>详细资料:</p>
<ul>
<li>字段</li>
<li><a href="#constructor-detail">构造器</a></li>
<li><a href="#method-detail">方法</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">方法</a></li>
</ul>
<ul class="sub-nav-list">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">方法</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="搜索">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">程序包</span>&nbsp;<a href="package-summary.html">com.binance.connector.futures.client.impl</a></div>
<h1 title="类 CMFuturesClientImpl" class="title">类 CMFuturesClientImpl</h1>
</div>
<div class="inheritance" title="继承树"><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="FuturesClientImpl.html" title="com.binance.connector.futures.client.impl中的类">com.binance.connector.futures.client.impl.FuturesClientImpl</a>
<div class="inheritance">com.binance.connector.futures.client.impl.CMFuturesClientImpl</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>所有已实现的接口:</dt>
<dd><code><a href="../FuturesClient.html" title="com.binance.connector.futures.client中的接口">FuturesClient</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">CMFuturesClientImpl</span>
<span class="extends-implements">extends <a href="FuturesClientImpl.html" title="com.binance.connector.futures.client.impl中的类">FuturesClientImpl</a></span></div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>构造器概要</h2>
<div class="caption"><span>构造器</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">构造器</div>
<div class="table-header col-last">说明</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">CMFuturesClientImpl</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(java.lang.String)" class="member-name-link">CMFuturesClientImpl</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;baseUrl)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(java.lang.String,boolean)" class="member-name-link">CMFuturesClientImpl</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;baseUrl,
 boolean&nbsp;showLimitUsage)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(java.lang.String,java.lang.String)" class="member-name-link">CMFuturesClientImpl</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;apiKey,
 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;secretKey)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(java.lang.String,java.lang.String,boolean)" class="member-name-link">CMFuturesClientImpl</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;apiKey,
 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;secretKey,
 boolean&nbsp;showLimitUsage)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(java.lang.String,java.lang.String,java.lang.String)" class="member-name-link">CMFuturesClientImpl</a><wbr>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;apiKey,
 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;secretKey,
 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;baseUrl)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>方法概要</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">所有方法</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">实例方法</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">具体方法</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">修饰符和类型</div>
<div class="table-header col-second">方法</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="cm_futures/CMAccount.html" title="com.binance.connector.futures.client.impl.cm_futures中的类">CMAccount</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#account()" class="member-name-link">account</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="cm_futures/CMMarket.html" title="com.binance.connector.futures.client.impl.cm_futures中的类">CMMarket</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#market()" class="member-name-link">market</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="cm_futures/CMPortfolioMargin.html" title="com.binance.connector.futures.client.impl.cm_futures中的类">CMPortfolioMargin</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#portfolioMargin()" class="member-name-link">portfolioMargin</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="cm_futures/CMUserData.html" title="com.binance.connector.futures.client.impl.cm_futures中的类">CMUserData</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#userData()" class="member-name-link">userData</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-com.binance.connector.futures.client.impl.FuturesClientImpl">从类继承的方法&nbsp;com.binance.connector.futures.client.impl.<a href="FuturesClientImpl.html" title="com.binance.connector.futures.client.impl中的类">FuturesClientImpl</a></h3>
<code><a href="FuturesClientImpl.html#getApiKey()">getApiKey</a>, <a href="FuturesClientImpl.html#getBaseUrl()">getBaseUrl</a>, <a href="FuturesClientImpl.html#getProductUrl()">getProductUrl</a>, <a href="FuturesClientImpl.html#getProxy()">getProxy</a>, <a href="FuturesClientImpl.html#getSecretKey()">getSecretKey</a>, <a href="FuturesClientImpl.html#getShowLimitUsage()">getShowLimitUsage</a>, <a href="FuturesClientImpl.html#setProxy(com.binance.connector.futures.client.utils.ProxyAuth)">setProxy</a>, <a href="FuturesClientImpl.html#setShowLimitUsage(boolean)">setShowLimitUsage</a>, <a href="FuturesClientImpl.html#unsetProxy()">unsetProxy</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">从类继承的方法&nbsp;java.lang.<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a></h3>
<code><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html#clone--" title="java.lang中的类或接口" class="external-link">clone</a>, <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html#equals-java.lang.Object-" title="java.lang中的类或接口" class="external-link">equals</a>, <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html#finalize--" title="java.lang中的类或接口" class="external-link">finalize</a>, <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html#getClass--" title="java.lang中的类或接口" class="external-link">getClass</a>, <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html#hashCode--" title="java.lang中的类或接口" class="external-link">hashCode</a>, <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html#notify--" title="java.lang中的类或接口" class="external-link">notify</a>, <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html#notifyAll--" title="java.lang中的类或接口" class="external-link">notifyAll</a>, <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html#toString--" title="java.lang中的类或接口" class="external-link">toString</a>, <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html#wait--" title="java.lang中的类或接口" class="external-link">wait</a>, <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html#wait-long-" title="java.lang中的类或接口" class="external-link">wait</a>, <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html#wait-long-int-" title="java.lang中的类或接口" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>构造器详细资料</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>CMFuturesClientImpl</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">CMFuturesClientImpl</span>()</div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(java.lang.String)">
<h3>CMFuturesClientImpl</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">CMFuturesClientImpl</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;baseUrl)</span></div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(java.lang.String,java.lang.String)">
<h3>CMFuturesClientImpl</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">CMFuturesClientImpl</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;apiKey,
 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;secretKey)</span></div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(java.lang.String,boolean)">
<h3>CMFuturesClientImpl</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">CMFuturesClientImpl</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;baseUrl,
 boolean&nbsp;showLimitUsage)</span></div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(java.lang.String,java.lang.String,boolean)">
<h3>CMFuturesClientImpl</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">CMFuturesClientImpl</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;apiKey,
 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;secretKey,
 boolean&nbsp;showLimitUsage)</span></div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(java.lang.String,java.lang.String,java.lang.String)">
<h3>CMFuturesClientImpl</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">CMFuturesClientImpl</span><wbr><span class="parameters">(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;apiKey,
 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;secretKey,
 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;baseUrl)</span></div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>方法详细资料</h2>
<ul class="member-list">
<li>
<section class="detail" id="market()">
<h3>market</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="cm_futures/CMMarket.html" title="com.binance.connector.futures.client.impl.cm_futures中的类">CMMarket</a></span>&nbsp;<span class="element-name">market</span>()</div>
</section>
</li>
<li>
<section class="detail" id="account()">
<h3>account</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="cm_futures/CMAccount.html" title="com.binance.connector.futures.client.impl.cm_futures中的类">CMAccount</a></span>&nbsp;<span class="element-name">account</span>()</div>
</section>
</li>
<li>
<section class="detail" id="userData()">
<h3>userData</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="cm_futures/CMUserData.html" title="com.binance.connector.futures.client.impl.cm_futures中的类">CMUserData</a></span>&nbsp;<span class="element-name">userData</span>()</div>
</section>
</li>
<li>
<section class="detail" id="portfolioMargin()">
<h3>portfolioMargin</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="cm_futures/CMPortfolioMargin.html" title="com.binance.connector.futures.client.impl.cm_futures中的类">CMPortfolioMargin</a></span>&nbsp;<span class="element-name">portfolioMargin</span>()</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &#169; 2025. All rights reserved.</small></p>
</footer>
</div>
</div>
</body>
</html>
