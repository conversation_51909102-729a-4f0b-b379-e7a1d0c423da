package com.crypto.trading.bootstrap.initializer;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 抽象模块初始化器
 * 提供模块初始化器的基本实现
 */
public abstract class AbstractModuleInitializer implements ModuleInitializer {
    
    private static final Logger log = LoggerFactory.getLogger(AbstractModuleInitializer.class);
    
    /**
     * 模块初始化状态
     */
    private volatile boolean initialized = false;
    
    /**
     * 默认优先级
     */
    private static final int DEFAULT_PRIORITY = 100;
    
    /**
     * 空依赖数组
     */
    private static final String[] NO_DEPENDENCIES = new String[0];
    
    @Override
    public int getPriority() {
        return DEFAULT_PRIORITY;
    }
    
    @Override
    public String[] getDependencies() {
        return NO_DEPENDENCIES;
    }
    
    @Override
    public void beforeInitialize() {
        log.info("模块[{}]开始初始化...", getName());
    }
    
    @Override
    public void initialize() throws Exception {
        try {
            log.info("模块[{}]正在执行初始化...", getName());
            doInitialize();
            initialized = true;
            log.info("模块[{}]初始化完成", getName());
        } catch (Exception e) {
            log.error("模块[{}]初始化失败: {}", getName(), e.getMessage(), e);
            throw e;
        }
    }
    
    /**
     * 执行具体的初始化逻辑
     * 子类需要实现此方法以提供特定的初始化行为
     * 
     * @throws Exception 初始化过程中可能抛出的异常
     */
    protected abstract void doInitialize() throws Exception;
    
    @Override
    public void afterInitialize() {
        log.info("模块[{}]初始化后处理完成", getName());
    }
    
    @Override
    public void shutdown() {
        try {
            log.info("模块[{}]正在关闭...", getName());
            doShutdown();
            initialized = false;
            log.info("模块[{}]已关闭", getName());
        } catch (Exception e) {
            log.error("模块[{}]关闭过程中发生错误: {}", getName(), e.getMessage(), e);
        }
    }
    
    /**
     * 执行具体的关闭逻辑
     * 子类可以覆盖此方法以提供特定的关闭行为
     */
    protected void doShutdown() {
        // 默认不执行任何操作
    }
    
    @Override
    public boolean isInitialized() {
        return initialized;
    }
}