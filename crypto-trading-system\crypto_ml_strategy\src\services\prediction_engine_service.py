#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
预测引擎服务实现模块

集成DeepSeek蒸馏模型、在线学习引擎和ML推理优化器，
提供统一的ML预测服务接口。
"""

import pandas as pd
import numpy as np
import asyncio
from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass
from loguru import logger

from ..ml.distillation.deepseek_distiller import DeepSeekDistiller
from ..ml.online_learning.online_learning_engine import OnlineLearningEngine
from ..ml.ml_inference_optimizer import MLInferenceOptimizer
from ..infrastructure.service_interfaces import IPredictionEngineService
from ..infrastructure.intelligent_cache import cache_result, get_cache_service
from ..infrastructure.async_error_recovery import async_retry, RetryConfig, CircuitBreakerConfig
from ..infrastructure.memory_monitor import MemoryMonitor


@dataclass
class PredictionResult:
    """预测结果数据类"""
    symbol: str
    timeframe: str
    timestamp: float
    prediction: Dict[str, Any]
    confidence: float
    model_version: str
    processing_time_ms: float
    features_used: List[str]
    metadata: Dict[str, Any]


class PredictionEngineService(IPredictionEngineService):
    """
    预测引擎服务实现
    
    集成DeepSeek蒸馏模型、在线学习引擎和ML推理优化器，
    提供统一的ML预测服务接口。
    """
    
    def __init__(self):
        """初始化预测引擎服务"""
        # 初始化ML组件
        self.deepseek_distiller = DeepSeekDistiller()
        self.online_learning_engine = OnlineLearningEngine()
        self.ml_optimizer = MLInferenceOptimizer()
        
        # 缓存和监控服务
        self.cache_service = get_cache_service()
        self.memory_monitor = MemoryMonitor(
            monitoring_interval=60.0,
            memory_threshold=85.0
        )
        
        # 服务状态
        self._initialized = False
        self._model_loaded = False
        self._online_learning_active = False
        
        # 性能统计
        self.stats = {
            'total_predictions': 0,
            'successful_predictions': 0,
            'failed_predictions': 0,
            'cache_hits': 0,
            'avg_processing_time_ms': 0.0,
            'model_updates': 0
        }
        
        logger.info("预测引擎服务初始化完成")
    
    async def initialize(self) -> None:
        """初始化预测引擎"""
        if self._initialized:
            logger.warning("预测引擎已经初始化")
            return
        
        try:
            logger.info("开始初始化预测引擎...")
            
            # 1. 启动内存监控
            self.memory_monitor.start_monitoring()
            self.memory_monitor.add_alert_callback(self._handle_memory_alert)
            
            # 2. 初始化DeepSeek蒸馏模型
            await self._initialize_deepseek_model()
            
            # 3. 初始化在线学习引擎
            await self._initialize_online_learning()
            
            # 4. 初始化ML优化器
            await self._initialize_ml_optimizer()
            
            self._initialized = True
            logger.info("预测引擎初始化完成")
            
        except Exception as e:
            logger.error(f"预测引擎初始化失败: {e}")
            raise
    
    @async_retry(
        retry_config=RetryConfig(max_retries=3, base_delay=1.0),
        circuit_breaker_config=CircuitBreakerConfig(failure_threshold=5, recovery_timeout=60.0)
    )
    async def predict(self, 
                     features: Dict[str, Any], 
                     symbol: str = "BTCUSDT",
                     timeframe: str = "1m") -> PredictionResult:
        """
        执行预测
        
        Args:
            features: 输入特征
            symbol: 交易对符号
            timeframe: 时间框架
            
        Returns:
            预测结果
        """
        start_time = asyncio.get_event_loop().time()
        
        try:
            self.stats['total_predictions'] += 1
            
            # 检查缓存
            cache_key = self._generate_cache_key(features, symbol, timeframe)
            cached_result = self.cache_service.get(cache_key)
            
            if cached_result:
                self.stats['cache_hits'] += 1
                logger.debug(f"使用缓存预测结果: {symbol} {timeframe}")
                return cached_result
            
            # 预处理特征
            processed_features = await self._preprocess_features(features)
            
            # 执行预测
            prediction = await self._execute_prediction(processed_features)
            
            # 计算置信度
            confidence = await self._calculate_confidence(prediction, processed_features)
            
            # 创建预测结果
            processing_time = (asyncio.get_event_loop().time() - start_time) * 1000
            
            result = PredictionResult(
                symbol=symbol,
                timeframe=timeframe,
                timestamp=asyncio.get_event_loop().time(),
                prediction=prediction,
                confidence=confidence,
                model_version=self.deepseek_distiller.get_model_version(),
                processing_time_ms=processing_time,
                features_used=list(processed_features.keys()),
                metadata={
                    'model_type': 'deepseek_distilled',
                    'online_learning_active': self._online_learning_active,
                    'optimization_enabled': True
                }
            )
            
            # 缓存结果
            self.cache_service.put(cache_key, result, ttl=30.0)  # 缓存30秒
            
            # 更新统计
            self.stats['successful_predictions'] += 1
            self._update_avg_processing_time(processing_time)
            
            # 触发在线学习
            if self._online_learning_active:
                asyncio.create_task(self._trigger_online_learning(processed_features, prediction))
            
            logger.debug(f"预测完成 - {symbol} {timeframe}, 置信度: {confidence:.3f}, "
                        f"处理时间: {processing_time:.1f}ms")
            
            return result
            
        except Exception as e:
            self.stats['failed_predictions'] += 1
            logger.error(f"预测失败: {e}")
            raise
    
    async def _initialize_deepseek_model(self) -> None:
        """初始化DeepSeek蒸馏模型"""
        try:
            logger.info("初始化DeepSeek蒸馏模型...")
            
            # 检查是否有预训练模型
            if await self.deepseek_distiller.has_pretrained_model():
                await self.deepseek_distiller.load_model()
                logger.info("已加载预训练的DeepSeek模型")
            else:
                logger.warning("未找到预训练模型，将在首次预测时训练")
            
            self._model_loaded = True
            
        except Exception as e:
            logger.error(f"DeepSeek模型初始化失败: {e}")
            raise
    
    async def _initialize_online_learning(self) -> None:
        """初始化在线学习引擎"""
        try:
            logger.info("初始化在线学习引擎...")
            
            await self.online_learning_engine.initialize()
            self._online_learning_active = True
            
            logger.info("在线学习引擎初始化完成")
            
        except Exception as e:
            logger.error(f"在线学习引擎初始化失败: {e}")
            # 在线学习失败不应该阻止整个服务
            self._online_learning_active = False
    
    async def _initialize_ml_optimizer(self) -> None:
        """初始化ML优化器"""
        try:
            logger.info("初始化ML推理优化器...")
            
            # 配置优化器
            optimizer_config = {
                'enable_caching': True,
                'enable_batching': True,
                'batch_size': 32,
                'cache_size': 1000,
                'enable_quantization': True
            }
            
            await self.ml_optimizer.configure(optimizer_config)
            
            logger.info("ML推理优化器初始化完成")
            
        except Exception as e:
            logger.error(f"ML优化器初始化失败: {e}")
            # 优化器失败不应该阻止核心功能
    
    async def _preprocess_features(self, features: Dict[str, Any]) -> Dict[str, Any]:
        """预处理特征"""
        try:
            # 特征验证
            if not features:
                raise ValueError("特征数据为空")
            
            # 特征标准化
            processed_features = {}
            
            for key, value in features.items():
                if isinstance(value, (int, float)):
                    # 数值特征标准化
                    processed_features[key] = float(value)
                elif isinstance(value, dict):
                    # 嵌套字典特征
                    processed_features[key] = value
                elif isinstance(value, list):
                    # 列表特征转换为numpy数组
                    processed_features[key] = np.array(value)
                else:
                    # 其他类型保持原样
                    processed_features[key] = value
            
            # 添加时间特征
            processed_features['timestamp'] = asyncio.get_event_loop().time()
            
            return processed_features
            
        except Exception as e:
            logger.error(f"特征预处理失败: {e}")
            raise
    
    async def _execute_prediction(self, features: Dict[str, Any]) -> Dict[str, Any]:
        """执行预测"""
        try:
            # 使用ML优化器进行预测
            if self.ml_optimizer.is_available():
                prediction = await self.ml_optimizer.predict_async(features)
            else:
                # 直接使用DeepSeek模型
                prediction = await self.deepseek_distiller.predict_async(features)
            
            return prediction
            
        except Exception as e:
            logger.error(f"预测执行失败: {e}")
            raise
    
    async def _calculate_confidence(self, 
                                   prediction: Dict[str, Any], 
                                   features: Dict[str, Any]) -> float:
        """计算预测置信度"""
        try:
            # 基于模型输出计算置信度
            confidence = prediction.get('confidence', 0.5)
            
            # 基于特征质量调整置信度
            feature_quality = self._assess_feature_quality(features)
            adjusted_confidence = confidence * feature_quality
            
            # 基于历史性能调整置信度
            historical_accuracy = self._get_historical_accuracy()
            final_confidence = adjusted_confidence * historical_accuracy
            
            return np.clip(final_confidence, 0.0, 1.0)
            
        except Exception as e:
            logger.warning(f"置信度计算失败: {e}")
            return 0.5  # 默认置信度
    
    def _assess_feature_quality(self, features: Dict[str, Any]) -> float:
        """评估特征质量"""
        try:
            quality_score = 1.0
            
            # 检查特征完整性
            expected_features = ['price', 'volume', 'technical_indicators']
            missing_features = [f for f in expected_features if f not in features]
            
            if missing_features:
                quality_score *= (1.0 - len(missing_features) / len(expected_features) * 0.3)
            
            # 检查数据新鲜度
            timestamp = features.get('timestamp', 0)
            current_time = asyncio.get_event_loop().time()
            age_seconds = current_time - timestamp
            
            if age_seconds > 300:  # 超过5分钟
                quality_score *= 0.8
            elif age_seconds > 60:  # 超过1分钟
                quality_score *= 0.9
            
            return np.clip(quality_score, 0.1, 1.0)
            
        except Exception as e:
            logger.warning(f"特征质量评估失败: {e}")
            return 0.8  # 默认质量分数
    
    def _get_historical_accuracy(self) -> float:
        """获取历史准确率"""
        try:
            total = self.stats['successful_predictions'] + self.stats['failed_predictions']
            if total == 0:
                return 0.8  # 默认准确率
            
            accuracy = self.stats['successful_predictions'] / total
            return np.clip(accuracy, 0.1, 1.0)
            
        except Exception as e:
            logger.warning(f"历史准确率计算失败: {e}")
            return 0.8
    
    async def _trigger_online_learning(self, 
                                      features: Dict[str, Any], 
                                      prediction: Dict[str, Any]) -> None:
        """触发在线学习"""
        try:
            if not self._online_learning_active:
                return
            
            # 异步触发在线学习
            await self.online_learning_engine.process_new_data(features, prediction)
            self.stats['model_updates'] += 1
            
            logger.debug("在线学习已触发")
            
        except Exception as e:
            logger.warning(f"在线学习触发失败: {e}")
    
    def _generate_cache_key(self, 
                           features: Dict[str, Any], 
                           symbol: str, 
                           timeframe: str) -> str:
        """生成缓存键"""
        import hashlib
        
        # 创建特征的哈希值
        feature_str = str(sorted(features.items()))
        feature_hash = hashlib.md5(feature_str.encode()).hexdigest()[:8]
        
        return f"prediction_{symbol}_{timeframe}_{feature_hash}"
    
    def _update_avg_processing_time(self, processing_time: float) -> None:
        """更新平均处理时间"""
        current_avg = self.stats['avg_processing_time_ms']
        total_predictions = self.stats['successful_predictions']
        
        if total_predictions == 1:
            self.stats['avg_processing_time_ms'] = processing_time
        else:
            # 指数移动平均
            alpha = 0.1
            self.stats['avg_processing_time_ms'] = (
                alpha * processing_time + (1 - alpha) * current_avg
            )
    
    def _handle_memory_alert(self, alert: Dict[str, Any]) -> None:
        """处理内存告警"""
        logger.warning(f"收到内存告警: {alert['message']}")
        
        if alert['type'] == 'high_memory_usage':
            # 清理缓存
            self.cache_service.optimize_cache()
            
            # 触发垃圾回收
            import gc
            gc.collect()
            
        elif alert['type'] == 'memory_leak_detected':
            # 重启在线学习引擎
            asyncio.create_task(self._restart_online_learning())
    
    async def _restart_online_learning(self) -> None:
        """重启在线学习引擎"""
        try:
            logger.info("重启在线学习引擎...")
            
            self._online_learning_active = False
            await self.online_learning_engine.cleanup()
            await self._initialize_online_learning()
            
            logger.info("在线学习引擎重启完成")
            
        except Exception as e:
            logger.error(f"在线学习引擎重启失败: {e}")
    
    async def cleanup(self) -> None:
        """清理资源"""
        try:
            logger.info("清理预测引擎资源...")
            
            # 停止内存监控
            self.memory_monitor.stop_monitoring()
            
            # 清理ML组件
            if self.deepseek_distiller:
                await self.deepseek_distiller.cleanup()
            
            if self.online_learning_engine:
                await self.online_learning_engine.cleanup()
            
            if self.ml_optimizer:
                await self.ml_optimizer.cleanup()
            
            logger.info("预测引擎资源清理完成")
            
        except Exception as e:
            logger.error(f"预测引擎资源清理失败: {e}")
    
    def get_service_status(self) -> Dict[str, Any]:
        """获取服务状态"""
        return {
            'service_name': 'PredictionEngineService',
            'initialized': self._initialized,
            'model_loaded': self._model_loaded,
            'online_learning_active': self._online_learning_active,
            'stats': self.stats,
            'memory_status': self.memory_monitor.get_current_status(),
            'cache_stats': self.cache_service.get_stats(),
            'model_version': self.deepseek_distiller.get_model_version() if self._model_loaded else None
        }
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """获取性能指标"""
        total_requests = self.stats['total_predictions']
        success_rate = (
            self.stats['successful_predictions'] / total_requests 
            if total_requests > 0 else 0.0
        )
        
        cache_hit_rate = (
            self.stats['cache_hits'] / total_requests 
            if total_requests > 0 else 0.0
        )
        
        return {
            'total_predictions': total_requests,
            'success_rate': success_rate,
            'avg_processing_time_ms': self.stats['avg_processing_time_ms'],
            'cache_hit_rate': cache_hit_rate,
            'model_updates': self.stats['model_updates'],
            'memory_usage_mb': self.memory_monitor.get_current_status()['process_memory']['used_mb']
        }