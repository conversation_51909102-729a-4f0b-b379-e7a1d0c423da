#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
服务接口定义模块

该模块定义所有主要组件的服务接口，支持依赖注入和测试模拟。
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List, Protocol
from datetime import datetime


class IConfigService(Protocol):
    """配置服务接口"""
    
    def get(self, section: str, option: str, fallback: str = None) -> str:
        """获取配置项值"""
        ...
    
    def get_int(self, section: str, option: str, fallback: int = None) -> int:
        """获取整型配置值"""
        ...
    
    def get_bool(self, section: str, option: str, fallback: bool = None) -> bool:
        """获取布尔型配置值"""
        ...
    
    def get_list(self, section: str, option: str, fallback: List = None) -> List:
        """获取列表配置值"""
        ...


class ILoggerService(Protocol):
    """日志服务接口"""
    
    def info(self, message: str, **kwargs) -> None:
        """记录信息日志"""
        ...
    
    def error(self, message: str, **kwargs) -> None:
        """记录错误日志"""
        ...
    
    def warning(self, message: str, **kwargs) -> None:
        """记录警告日志"""
        ...
    
    def debug(self, message: str, **kwargs) -> None:
        """记录调试日志"""
        ...


class IDatabaseService(Protocol):
    """数据库服务接口"""
    
    def connect(self) -> None:
        """连接数据库"""
        ...
    
    def close(self) -> None:
        """关闭数据库连接"""
        ...
    
    def execute_query(self, query: str, params: Optional[Dict] = None) -> List[Dict[str, Any]]:
        """执行查询"""
        ...
    
    def execute_command(self, command: str, params: Optional[Dict] = None) -> int:
        """执行命令"""
        ...


class IInfluxDBService(IDatabaseService):
    """InfluxDB服务接口"""
    
    def write_points(self, points: List[Dict[str, Any]]) -> None:
        """写入数据点"""
        ...
    
    def query_data(self, query: str, start_time: datetime, end_time: datetime) -> List[Dict[str, Any]]:
        """查询时序数据"""
        ...


class IMySQLService(IDatabaseService):
    """MySQL服务接口"""
    
    def get_connection(self):
        """获取数据库连接"""
        ...
    
    def begin_transaction(self) -> None:
        """开始事务"""
        ...
    
    def commit_transaction(self) -> None:
        """提交事务"""
        ...
    
    def rollback_transaction(self) -> None:
        """回滚事务"""
        ...


class IKafkaService(Protocol):
    """Kafka服务接口"""
    
    def start_consuming(self, topic: str, callback: callable) -> None:
        """开始消费消息"""
        ...
    
    def stop_consuming(self, topic: Optional[str] = None) -> None:
        """停止消费消息"""
        ...
    
    def publish_signal(self, signal: Dict[str, Any]) -> None:
        """发布交易信号"""
        ...
    
    def clean_up(self) -> None:
        """清理资源"""
        ...


class IDataProcessorService(Protocol):
    """数据处理服务接口"""
    
    def process_kline_data(self, message: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """处理K线数据"""
        ...
    
    def process_depth_data(self, message: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """处理深度数据"""
        ...
    
    def process_trade_data(self, message: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """处理交易数据"""
        ...
    
    def get_latest_market_data(self, symbol: str) -> Optional[Dict[str, Any]]:
        """获取最新市场数据"""
        ...


class IDataLoaderService(Protocol):
    """数据加载服务接口"""
    
    def load_training_data(self, symbols: Optional[List[str]] = None, 
                          days: Optional[int] = None) -> Dict[str, Any]:
        """加载训练数据"""
        ...
    
    def load_historical_data(self, symbol: str, start_time: datetime, 
                           end_time: datetime) -> List[Dict[str, Any]]:
        """加载历史数据"""
        ...


class IModelService(Protocol):
    """模型服务接口"""
    
    def predict(self, features: Dict[str, Any]) -> Dict[str, Any]:
        """模型预测"""
        ...
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        ...
    
    def is_model_ready(self) -> bool:
        """检查模型是否就绪"""
        ...


class IModelTrainerService(Protocol):
    """模型训练服务接口"""
    
    def train(self, training_data: Dict[str, Any]) -> Dict[str, Any]:
        """训练模型"""
        ...
    
    def validate_model(self, validation_data: Dict[str, Any]) -> Dict[str, Any]:
        """验证模型"""
        ...
    
    def save_model(self, model_path: str) -> None:
        """保存模型"""
        ...


class IModelVersionService(Protocol):
    """模型版本管理服务接口"""
    
    def has_active_model(self) -> bool:
        """检查是否有活跃模型"""
        ...
    
    def get_current_model_version(self) -> Optional[str]:
        """获取当前模型版本"""
        ...
    
    def create_new_version(self, model_data: Dict[str, Any]) -> str:
        """创建新版本"""
        ...
    
    def activate_version(self, version: str) -> None:
        """激活版本"""
        ...


class IOnlineLearnerService(Protocol):
    """在线学习服务接口"""
    
    def update(self, market_data: Dict[str, Any]) -> None:
        """更新模型"""
        ...
    
    def get_learning_stats(self) -> Dict[str, Any]:
        """获取学习统计"""
        ...
    
    def is_learning_enabled(self) -> bool:
        """检查是否启用学习"""
        ...


class IStrategyService(Protocol):
    """策略服务接口"""
    
    def on_market_data(self, market_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """处理市场数据并生成信号"""
        ...
    
    def set_prediction_engine(self, prediction_engine) -> None:
        """设置预测引擎"""
        ...
    
    def get_strategy_info(self) -> Dict[str, Any]:
        """获取策略信息"""
        ...


class IPredictionEngineService(Protocol):
    """预测引擎服务接口"""
    
    def predict(self, features: Dict[str, Any]) -> Dict[str, Any]:
        """执行预测"""
        ...
    
    def get_prediction_confidence(self) -> float:
        """获取预测置信度"""
        ...
    
    def update_model(self, model_version: str) -> None:
        """更新模型"""
        ...


class IHealthCheckService(Protocol):
    """健康检查服务接口"""
    
    def check_health(self) -> Dict[str, Any]:
        """执行健康检查"""
        ...
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        ...
    
    def register_health_check(self, name: str, check_func: callable) -> None:
        """注册健康检查"""
        ...


class IMetricsService(Protocol):
    """指标服务接口"""
    
    def record_metric(self, name: str, value: float, tags: Optional[Dict[str, str]] = None) -> None:
        """记录指标"""
        ...
    
    def increment_counter(self, name: str, tags: Optional[Dict[str, str]] = None) -> None:
        """递增计数器"""
        ...
    
    def record_timing(self, name: str, duration_ms: float, tags: Optional[Dict[str, str]] = None) -> None:
        """记录时间指标"""
        ...
    
    def get_metrics_summary(self) -> Dict[str, Any]:
        """获取指标摘要"""
        ...


class IApplicationService(Protocol):
    """应用服务接口"""
    
    def start(self) -> None:
        """启动应用"""
        ...
    
    def stop(self) -> None:
        """停止应用"""
        ...
    
    def get_status(self) -> Dict[str, Any]:
        """获取应用状态"""
        ...
    
    def handle_signal(self, signum: int, frame) -> None:
        """处理系统信号"""
        ...


class ILifecycleService(Protocol):
    """生命周期管理服务接口"""
    
    def initialize(self) -> None:
        """初始化"""
        ...
    
    def start(self) -> None:
        """启动"""
        ...
    
    def stop(self) -> None:
        """停止"""
        ...
    
    def cleanup(self) -> None:
        """清理资源"""
        ...
    
    def get_lifecycle_state(self) -> str:
        """获取生命周期状态"""
        ...


class IErrorHandlerService(Protocol):
    """错误处理服务接口"""
    
    def handle_error(self, error: Exception, context: Optional[Dict[str, Any]] = None) -> bool:
        """处理错误"""
        ...
    
    def register_handler(self, handler) -> None:
        """注册错误处理器"""
        ...
    
    def get_error_metrics(self) -> Dict[str, Any]:
        """获取错误指标"""
        ...


class ICircuitBreakerService(Protocol):
    """熔断器服务接口"""
    
    def call(self, func: callable, *args, **kwargs) -> Any:
        """通过熔断器调用函数"""
        ...
    
    def get_state(self) -> str:
        """获取熔断器状态"""
        ...
    
    def force_open(self) -> None:
        """强制开启熔断器"""
        ...
    
    def force_close(self) -> None:
        """强制关闭熔断器"""
        ...


class IRecoveryService(Protocol):
    """错误恢复服务接口"""
    
    def attempt_recovery(self, error: Exception, context: Optional[Dict[str, Any]] = None) -> Optional[Dict[str, Any]]:
        """尝试错误恢复"""
        ...
    
    def register_strategy(self, strategy) -> None:
        """注册恢复策略"""
        ...
    
    def get_recovery_statistics(self) -> Dict[str, Any]:
        """获取恢复统计"""
        ...


class IDegradationService(Protocol):
    """优雅降级服务接口"""
    
    def degrade_service(self, service_name: str, level: str, reason: str = "") -> bool:
        """降级服务"""
        ...
    
    def restore_service(self, service_name: str) -> bool:
        """恢复服务"""
        ...
    
    def get_service_status(self, service_name: str) -> Dict[str, Any]:
        """获取服务状态"""
        ...
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统降级状态"""
        ...


class IFailoverService(Protocol):
    """故障转移服务接口"""
    
    def trigger_failover(self, cluster_name: str, trigger: str, reason: str = "") -> bool:
        """触发故障转移"""
        ...
    
    def register_cluster(self, cluster) -> None:
        """注册服务集群"""
        ...
    
    def get_cluster_status(self, cluster_name: str) -> Dict[str, Any]:
        """获取集群状态"""
        ...
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取故障转移系统状态"""
        ...


class IErrorHandlingSystemService(Protocol):
    """错误处理系统服务接口"""
    
    def initialize(self, config_file: Optional[str] = None) -> bool:
        """初始化错误处理系统"""
        ...
    
    def start(self) -> bool:
        """启动错误处理系统"""
        ...
    
    def stop(self) -> bool:
        """停止错误处理系统"""
        ...
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        ...
    
    def run_health_check(self) -> Dict[str, Any]:
        """运行健康检查"""
        ...
    
    def run_comprehensive_test(self) -> Dict[str, Any]:
        """运行综合测试"""
        ...


class IStartupSequenceService(Protocol):
    """启动序列服务接口"""
    
    def register_task(self, task) -> None:
        """注册启动任务"""
        ...
    
    def start_sequence(self) -> Dict[str, Any]:
        """启动序列"""
        ...
    
    def get_progress(self) -> Dict[str, Any]:
        """获取启动进度"""
        ...
    
    def register_progress_callback(self, callback: Callable) -> None:
        """注册进度回调"""
        ...


class IDependencyValidatorService(Protocol):
    """依赖验证服务接口"""
    
    def add_checker(self, checker) -> None:
        """添加依赖检查器"""
        ...
    
    def validate_all(self, parallel: bool = True) -> Dict[str, Any]:
        """验证所有依赖"""
        ...
    
    def get_validation_summary(self) -> Dict[str, Any]:
        """获取验证摘要"""
        ...


class IResourcePreloaderService(Protocol):
    """资源预加载服务接口"""
    
    def add_preloader(self, preloader) -> None:
        """添加预加载器"""
        ...
    
    def preload_all(self, parallel: bool = True) -> Dict[str, Any]:
        """预加载所有资源"""
        ...
    
    def get_preload_summary(self) -> Dict[str, Any]:
        """获取预加载摘要"""
        ...


class IStartupProgressService(Protocol):
    """启动进度服务接口"""
    
    def start_monitoring(self, total_tasks: int) -> None:
        """开始监控"""
        ...
    
    def stop_monitoring(self) -> None:
        """停止监控"""
        ...
    
    def get_current_metrics(self) -> Optional[Dict[str, Any]]:
        """获取当前指标"""
        ...
    
    def get_startup_analysis(self) -> Dict[str, Any]:
        """获取启动分析"""
        ...


class IStartupOptimizerService(Protocol):
    """启动优化服务接口"""
    
    def start(self) -> None:
        """启动优化器"""
        ...
    
    def stop(self) -> None:
        """停止优化器"""
        ...
    
    def optimize_task_execution_order(self, tasks: List) -> List:
        """优化任务执行顺序"""
        ...
    
    def get_optimization_report(self) -> Dict[str, Any]:
        """获取优化报告"""
        ...


class IStartupHealthService(Protocol):
    """启动健康服务接口"""
    
    def add_health_check(self, health_check) -> None:
        """添加健康检查"""
        ...
    
    def run_all_checks(self, parallel: bool = True) -> Dict[str, Any]:
        """运行所有健康检查"""
        ...
    
    def get_health_summary(self) -> Dict[str, Any]:
        """获取健康摘要"""
        ...
    
    def get_detailed_report(self) -> Dict[str, Any]:
        """获取详细报告"""
        ...


class IStartupConfigService(Protocol):
    """启动配置服务接口"""
    
    def load_config(self, config_file: Optional[str] = None) -> Dict[str, Any]:
        """加载配置"""
        ...
    
    def save_config(self, config: Optional[Dict[str, Any]] = None, file_path: Optional[str] = None) -> bool:
        """保存配置"""
        ...
    
    def get_config(self) -> Dict[str, Any]:
        """获取当前配置"""
        ...
    
    def update_config(self, updates: Dict[str, Any]) -> bool:
        """更新配置"""
        ...
    
    def get_config_summary(self) -> Dict[str, Any]:
        """获取配置摘要"""
        ...


# 服务接口注册表
SERVICE_INTERFACES = {
    'config': IConfigService,
    'logger': ILoggerService,
    'influxdb': IInfluxDBService,
    'mysql': IMySQLService,
    'kafka': IKafkaService,
    'data_processor': IDataProcessorService,
    'data_loader': IDataLoaderService,
    'model': IModelService,
    'model_trainer': IModelTrainerService,
    'model_version': IModelVersionService,
    'online_learner': IOnlineLearnerService,
    'strategy': IStrategyService,
    'prediction_engine': IPredictionEngineService,
    'health_check': IHealthCheckService,
    'metrics': IMetricsService,
    'application': IApplicationService,
    'lifecycle': ILifecycleService,
    'error_handler': IErrorHandlerService,
    'circuit_breaker': ICircuitBreakerService,
    'recovery': IRecoveryService,
    'degradation': IDegradationService,
    'failover': IFailoverService,
    'error_handling_system': IErrorHandlingSystemService,
    'startup_sequence': IStartupSequenceService,
    'dependency_validator': IDependencyValidatorService,
    'resource_preloader': IResourcePreloaderService,
    'startup_progress': IStartupProgressService,
    'startup_optimizer': IStartupOptimizerService,
    'startup_health': IStartupHealthService,
    'startup_config': IStartupConfigService
}


def get_service_interface(service_name: str) -> Optional[type]:
    """
    根据服务名称获取服务接口
    
    Args:
        service_name: 服务名称
        
    Returns:
        服务接口类型
    """
    return SERVICE_INTERFACES.get(service_name)


def get_all_service_interfaces() -> Dict[str, type]:
    """
    获取所有服务接口
    
    Returns:
        所有服务接口字典
    """
    return SERVICE_INTERFACES.copy()


if __name__ == "__main__":
    # 测试代码
    from loguru import logger
    
    # 测试服务接口获取
    config_interface = get_service_interface('config')
    logger.info(f"配置服务接口: {config_interface}")
    
    # 测试所有接口
    all_interfaces = get_all_service_interfaces()
    logger.info(f"所有服务接口数量: {len(all_interfaces)}")
    
    for name, interface in all_interfaces.items():
        logger.info(f"服务接口: {name} -> {interface.__name__}")