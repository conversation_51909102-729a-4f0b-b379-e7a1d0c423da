<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.crypto.trading.trade.repository.mapper.statistics.OrderStatisticsMapper">

    <!-- 基础映射结果集 -->
    <resultMap id="BaseResultMap" type="com.crypto.trading.trade.model.entity.statistics.OrderStatisticsEntity">
        <id column="id" property="id"/>
        <result column="stats_date" property="statsDate"/>
        <result column="stats_hour" property="statsHour"/>
        <result column="symbol" property="symbol"/>
        <result column="strategy" property="strategy"/>
        <result column="total_orders" property="totalOrders"/>
        <result column="success_orders" property="successOrders"/>
        <result column="failed_orders" property="failedOrders"/>
        <result column="canceled_orders" property="canceledOrders"/>
        <result column="total_amount" property="totalAmount"/>
        <result column="avg_execution_time" property="avgExecutionTime"/>
        <result column="min_execution_time" property="minExecutionTime"/>
        <result column="max_execution_time" property="maxExecutionTime"/>
        <result column="success_rate" property="successRate"/>
        <result column="avg_price_deviation" property="avgPriceDeviation"/>
        <result column="updated_time" property="updatedTime"/>
        <result column="created_time" property="createdTime"/>
    </resultMap>

    <!-- 基础字段列表 -->
    <sql id="Base_Column_List">
        id, stats_date, stats_hour, symbol, strategy, total_orders, success_orders, 
        failed_orders, canceled_orders, total_amount, avg_execution_time, min_execution_time, 
        max_execution_time, success_rate, avg_price_deviation, updated_time, created_time
    </sql>

    <!-- 分页查询订单统计数据 -->
    <select id="pageOrderStatistics" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_order_statistics
        <where>
            <if test="statsDate != null and statsDate != ''">
                AND stats_date = #{statsDate}
            </if>
            <if test="symbol != null and symbol != ''">
                AND symbol = #{symbol}
            </if>
            <if test="strategy != null and strategy != ''">
                AND strategy = #{strategy}
            </if>
        </where>
        ORDER BY stats_date DESC, stats_hour DESC
    </select>
    
    <!-- 批量插入统计数据 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO t_order_statistics (
            stats_date, stats_hour, symbol, strategy, total_orders, success_orders, 
            failed_orders, canceled_orders, total_amount, avg_execution_time, min_execution_time, 
            max_execution_time, success_rate, avg_price_deviation, updated_time, created_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.statsDate},
                #{item.statsHour},
                #{item.symbol},
                #{item.strategy},
                #{item.totalOrders},
                #{item.successOrders},
                #{item.failedOrders},
                #{item.canceledOrders},
                #{item.totalAmount},
                #{item.avgExecutionTime},
                #{item.minExecutionTime},
                #{item.maxExecutionTime},
                #{item.successRate},
                #{item.avgPriceDeviation},
                #{item.updatedTime},
                #{item.createdTime}
            )
        </foreach>
    </insert>
    
    <!-- 获取日期范围内的成功率趋势 -->
    <select id="getSuccessRateTrend" resultType="java.util.Map">
        SELECT 
            stats_date as date,
            ROUND(AVG(success_rate), 2) as success_rate
        FROM t_order_statistics
        WHERE stats_date BETWEEN #{startDate} AND #{endDate}
        <if test="symbol != null and symbol != ''">
            AND symbol = #{symbol}
        </if>
        <if test="strategy != null and strategy != ''">
            AND strategy = #{strategy}
        </if>
        GROUP BY stats_date
        ORDER BY stats_date
    </select>
    
    <!-- 获取交易对的统计汇总 -->
    <select id="getSymbolSummary" resultType="java.util.Map">
        SELECT 
            symbol,
            SUM(total_orders) as total_orders,
            SUM(success_orders) as success_orders,
            SUM(failed_orders) as failed_orders,
            SUM(canceled_orders) as canceled_orders,
            SUM(total_amount) as total_amount,
            ROUND(SUM(success_orders) * 100.0 / SUM(total_orders), 2) as success_rate
        FROM t_order_statistics
        WHERE stats_date BETWEEN #{startDate} AND #{endDate}
        GROUP BY symbol
        ORDER BY total_orders DESC
    </select>
    
    <!-- 获取策略的统计汇总 -->
    <select id="getStrategySummary" resultType="java.util.Map">
        SELECT 
            strategy,
            SUM(total_orders) as total_orders,
            SUM(success_orders) as success_orders,
            SUM(failed_orders) as failed_orders,
            SUM(canceled_orders) as canceled_orders,
            SUM(total_amount) as total_amount,
            ROUND(SUM(success_orders) * 100.0 / SUM(total_orders), 2) as success_rate
        FROM t_order_statistics
        WHERE stats_date BETWEEN #{startDate} AND #{endDate}
        GROUP BY strategy
        ORDER BY total_orders DESC
    </select>
    
    <!-- 获取执行时间统计 -->
    <select id="getExecutionTimeSummary" resultType="java.util.Map">
        SELECT 
            symbol,
            ROUND(AVG(avg_execution_time), 2) as avg_time,
            MIN(min_execution_time) as min_time,
            MAX(max_execution_time) as max_time
        FROM t_order_statistics
        WHERE stats_date BETWEEN #{startDate} AND #{endDate}
        GROUP BY symbol
        ORDER BY avg_time
    </select>
    
    <!-- 获取小时维度的订单分布 -->
    <select id="getHourlyDistribution" resultType="java.util.Map">
        SELECT 
            stats_hour as hour,
            SUM(total_orders) as total_orders
        FROM t_order_statistics
        WHERE stats_date BETWEEN #{startDate} AND #{endDate}
        <if test="symbol != null and symbol != ''">
            AND symbol = #{symbol}
        </if>
        <if test="strategy != null and strategy != ''">
            AND strategy = #{strategy}
        </if>
        GROUP BY stats_hour
        ORDER BY stats_hour
    </select>
</mapper>