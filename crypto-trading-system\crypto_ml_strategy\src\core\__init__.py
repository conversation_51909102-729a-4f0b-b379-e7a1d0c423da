"""
Crypto ML Strategy - 核心数据类型和基础组件

该包包含系统的核心数据结构和基础组件，为其他模块提供基础支持。

主要组件:
- validation_result_types: 验证结果数据类型定义

作者: Crypto ML Strategy Team
版本: 1.0.0
"""

from .validation_result_types import (
    ValidationResult,
    IntegrationTestResult,
    calculate_confidence_interval,
    test_statistical_significance,
    calculate_effect_size
)

__all__ = [
    'ValidationResult',
    'IntegrationTestResult', 
    'calculate_confidence_interval',
    'test_statistical_significance',
    'calculate_effect_size'
]