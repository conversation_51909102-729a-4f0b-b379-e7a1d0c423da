"""
启动进度监控模块

提供crypto_ml_strategy项目的启动进度监控功能，包括实时进度跟踪、
性能指标收集、进度可视化和启动分析。

Author: Crypto ML Strategy Team
Date: 2024-12-20
Version: 1.0.0
"""

import asyncio
import time
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Dict, List, Optional, Callable
from loguru import logger

from .startup_sequence_core import StartupPhase, StartupTask, StartupTaskStatus
from .error_handler_core import (
    CryptoMLException, ErrorCode, ErrorSeverity, ErrorCategory,
    ErrorContext, handle_exception
)


class ProgressEventType(Enum):
    """进度事件类型枚举"""
    PHASE_STARTED = "phase_started"
    PHASE_COMPLETED = "phase_completed"
    TASK_STARTED = "task_started"
    TASK_COMPLETED = "task_completed"
    TASK_FAILED = "task_failed"
    MILESTONE_REACHED = "milestone_reached"
    WARNING_ISSUED = "warning_issued"
    ERROR_OCCURRED = "error_occurred"


@dataclass
class ProgressEvent:
    """进度事件"""
    event_type: ProgressEventType
    timestamp: datetime
    phase: Optional[StartupPhase] = None
    task_name: Optional[str] = None
    message: str = ""
    details: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "event_type": self.event_type.value,
            "timestamp": self.timestamp.isoformat(),
            "phase": self.phase.value if self.phase else None,
            "task_name": self.task_name,
            "message": self.message,
            "details": self.details
        }


@dataclass
class StartupMetrics:
    """启动指标"""
    start_time: datetime
    current_time: datetime = field(default_factory=datetime.now)
    total_tasks: int = 0
    completed_tasks: int = 0
    failed_tasks: int = 0
    running_tasks: int = 0
    current_phase: Optional[StartupPhase] = None
    phase_durations: Dict[StartupPhase, float] = field(default_factory=dict)
    task_durations: Dict[str, float] = field(default_factory=dict)
    memory_usage_mb: float = 0.0
    cpu_usage_percent: float = 0.0
    
    @property
    def elapsed_time(self) -> float:
        """获取已用时间"""
        return (self.current_time - self.start_time).total_seconds()
    
    @property
    def progress_percentage(self) -> float:
        """获取进度百分比"""
        if self.total_tasks == 0:
            return 0.0
        return (self.completed_tasks / self.total_tasks) * 100
    
    @property
    def success_rate(self) -> float:
        """获取成功率"""
        total_finished = self.completed_tasks + self.failed_tasks
        if total_finished == 0:
            return 0.0
        return (self.completed_tasks / total_finished) * 100
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "start_time": self.start_time.isoformat(),
            "current_time": self.current_time.isoformat(),
            "elapsed_time": self.elapsed_time,
            "total_tasks": self.total_tasks,
            "completed_tasks": self.completed_tasks,
            "failed_tasks": self.failed_tasks,
            "running_tasks": self.running_tasks,
            "progress_percentage": self.progress_percentage,
            "success_rate": self.success_rate,
            "current_phase": self.current_phase.value if self.current_phase else None,
            "phase_durations": {phase.value: duration for phase, duration in self.phase_durations.items()},
            "memory_usage_mb": self.memory_usage_mb,
            "cpu_usage_percent": self.cpu_usage_percent
        }


class ProgressReporter:
    """进度报告器"""
    
    def __init__(self, report_interval: float = 1.0):
        self.report_interval = report_interval
        self.last_report_time = 0.0
        self.report_callbacks: List[Callable[[StartupMetrics], None]] = []
    
    def add_callback(self, callback: Callable[[StartupMetrics], None]) -> None:
        """添加报告回调"""
        self.report_callbacks.append(callback)
    
    def should_report(self) -> bool:
        """判断是否应该报告"""
        current_time = time.time()
        if current_time - self.last_report_time >= self.report_interval:
            self.last_report_time = current_time
            return True
        return False
    
    def report_progress(self, metrics: StartupMetrics) -> None:
        """报告进度"""
        if not self.should_report():
            return
        
        for callback in self.report_callbacks:
            try:
                callback(metrics)
            except Exception as e:
                logger.error(f"进度报告回调执行失败: {e}")
    
    def console_reporter(self, metrics: StartupMetrics) -> None:
        """控制台进度报告"""
        phase_name = metrics.current_phase.value if metrics.current_phase else "未知"
        progress_bar = self._create_progress_bar(metrics.progress_percentage)
        
        logger.info(
            f"启动进度: {progress_bar} {metrics.progress_percentage:.1f}% "
            f"({metrics.completed_tasks}/{metrics.total_tasks}) "
            f"阶段: {phase_name} "
            f"耗时: {metrics.elapsed_time:.1f}s"
        )
    
    def _create_progress_bar(self, percentage: float, width: int = 20) -> str:
        """创建进度条"""
        filled = int(width * percentage / 100)
        bar = "█" * filled + "░" * (width - filled)
        return f"[{bar}]"


class StartupAnalyzer:
    """启动分析器"""
    
    def __init__(self):
        self.events: List[ProgressEvent] = []
        self.bottlenecks: List[Dict[str, Any]] = []
        self.recommendations: List[str] = []
    
    def add_event(self, event: ProgressEvent) -> None:
        """添加事件"""
        self.events.append(event)
    
    def analyze_startup_performance(self, metrics: StartupMetrics) -> Dict[str, Any]:
        """分析启动性能"""
        analysis = {
            "overall_performance": self._analyze_overall_performance(metrics),
            "phase_analysis": self._analyze_phase_performance(metrics),
            "task_analysis": self._analyze_task_performance(metrics),
            "bottlenecks": self._identify_bottlenecks(metrics),
            "recommendations": self._generate_recommendations(metrics)
        }
        
        return analysis
    
    def _analyze_overall_performance(self, metrics: StartupMetrics) -> Dict[str, Any]:
        """分析整体性能"""
        target_time = 10.0  # 目标启动时间10秒
        
        performance_rating = "excellent"
        if metrics.elapsed_time > target_time * 2:
            performance_rating = "poor"
        elif metrics.elapsed_time > target_time * 1.5:
            performance_rating = "fair"
        elif metrics.elapsed_time > target_time:
            performance_rating = "good"
        
        return {
            "elapsed_time": metrics.elapsed_time,
            "target_time": target_time,
            "performance_rating": performance_rating,
            "time_efficiency": min(100, (target_time / metrics.elapsed_time) * 100),
            "success_rate": metrics.success_rate,
            "total_tasks": metrics.total_tasks,
            "failed_tasks": metrics.failed_tasks
        }
    
    def _analyze_phase_performance(self, metrics: StartupMetrics) -> Dict[str, Any]:
        """分析阶段性能"""
        phase_analysis = {}
        
        for phase, duration in metrics.phase_durations.items():
            # 设置各阶段的目标时间
            target_times = {
                StartupPhase.PRE_STARTUP: 1.0,
                StartupPhase.DEPENDENCY_CHECK: 1.0,
                StartupPhase.CORE_INITIALIZATION: 3.0,
                StartupPhase.SERVICE_STARTUP: 3.0,
                StartupPhase.POST_STARTUP: 2.0
            }
            
            target_time = target_times.get(phase, 2.0)
            efficiency = min(100, (target_time / duration) * 100) if duration > 0 else 100
            
            phase_analysis[phase.value] = {
                "duration": duration,
                "target_time": target_time,
                "efficiency": efficiency,
                "status": "on_time" if duration <= target_time else "slow"
            }
        
        return phase_analysis
    
    def _analyze_task_performance(self, metrics: StartupMetrics) -> Dict[str, Any]:
        """分析任务性能"""
        if not metrics.task_durations:
            return {}
        
        # 找出最慢的任务
        slowest_tasks = sorted(
            metrics.task_durations.items(),
            key=lambda x: x[1],
            reverse=True
        )[:5]
        
        # 计算平均任务时间
        avg_task_time = sum(metrics.task_durations.values()) / len(metrics.task_durations)
        
        return {
            "total_tasks": len(metrics.task_durations),
            "average_task_time": avg_task_time,
            "slowest_tasks": [
                {"name": name, "duration": duration}
                for name, duration in slowest_tasks
            ]
        }
    
    def _identify_bottlenecks(self, metrics: StartupMetrics) -> List[Dict[str, Any]]:
        """识别瓶颈"""
        bottlenecks = []
        
        # 检查阶段瓶颈
        for phase, duration in metrics.phase_durations.items():
            if duration > 3.0:  # 超过3秒的阶段
                bottlenecks.append({
                    "type": "phase_bottleneck",
                    "name": phase.value,
                    "duration": duration,
                    "description": f"阶段 {phase.value} 耗时过长: {duration:.2f}s"
                })
        
        # 检查任务瓶颈
        for task_name, duration in metrics.task_durations.items():
            if duration > 2.0:  # 超过2秒的任务
                bottlenecks.append({
                    "type": "task_bottleneck",
                    "name": task_name,
                    "duration": duration,
                    "description": f"任务 {task_name} 耗时过长: {duration:.2f}s"
                })
        
        # 检查失败率瓶颈
        if metrics.success_rate < 90:
            bottlenecks.append({
                "type": "reliability_bottleneck",
                "name": "task_failures",
                "success_rate": metrics.success_rate,
                "description": f"任务成功率过低: {metrics.success_rate:.1f}%"
            })
        
        return bottlenecks
    
    def _generate_recommendations(self, metrics: StartupMetrics) -> List[str]:
        """生成优化建议"""
        recommendations = []
        
        # 基于整体性能的建议
        if metrics.elapsed_time > 10.0:
            recommendations.append("考虑增加并行任务数量以减少启动时间")
        
        # 基于阶段性能的建议
        for phase, duration in metrics.phase_durations.items():
            if phase == StartupPhase.DEPENDENCY_CHECK and duration > 1.0:
                recommendations.append("优化依赖检查，考虑并行检查或缓存结果")
            elif phase == StartupPhase.CORE_INITIALIZATION and duration > 3.0:
                recommendations.append("优化核心组件初始化，考虑延迟加载非关键组件")
        
        # 基于失败率的建议
        if metrics.failed_tasks > 0:
            recommendations.append("检查失败任务的原因，改进错误处理和重试机制")
        
        # 基于资源使用的建议
        if metrics.memory_usage_mb > 1024:
            recommendations.append("监控内存使用，考虑优化资源预加载策略")
        
        return recommendations


class StartupProgressMonitor:
    """启动进度监控器"""
    
    def __init__(self):
        self.metrics: Optional[StartupMetrics] = None
        self.events: List[ProgressEvent] = []
        self.reporter = ProgressReporter()
        self.analyzer = StartupAnalyzer()
        self.monitoring = False
        self.monitor_task: Optional[asyncio.Task] = None
        self.event_callbacks: List[Callable[[ProgressEvent], None]] = []
        
        # 注册默认的控制台报告器
        self.reporter.add_callback(self.reporter.console_reporter)
    
    def start_monitoring(self, total_tasks: int) -> None:
        """开始监控"""
        self.metrics = StartupMetrics(
            start_time=datetime.now(),
            total_tasks=total_tasks
        )
        self.monitoring = True
        self.monitor_task = asyncio.create_task(self._monitor_loop())
        
        logger.info(f"启动进度监控已开始 (总任务数: {total_tasks})")
    
    async def stop_monitoring(self) -> None:
        """停止监控"""
        self.monitoring = False
        if self.monitor_task:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass
        
        logger.info("启动进度监控已停止")
    
    def add_event_callback(self, callback: Callable[[ProgressEvent], None]) -> None:
        """添加事件回调"""
        self.event_callbacks.append(callback)
    
    def add_progress_callback(self, callback: Callable[[StartupMetrics], None]) -> None:
        """添加进度回调"""
        self.reporter.add_callback(callback)
    
    def on_phase_started(self, phase: StartupPhase) -> None:
        """阶段开始事件"""
        if not self.metrics:
            return
        
        self.metrics.current_phase = phase
        
        event = ProgressEvent(
            event_type=ProgressEventType.PHASE_STARTED,
            timestamp=datetime.now(),
            phase=phase,
            message=f"开始阶段: {phase.value}"
        )
        
        self._add_event(event)
    
    def on_phase_completed(self, phase: StartupPhase, duration: float) -> None:
        """阶段完成事件"""
        if not self.metrics:
            return
        
        self.metrics.phase_durations[phase] = duration
        
        event = ProgressEvent(
            event_type=ProgressEventType.PHASE_COMPLETED,
            timestamp=datetime.now(),
            phase=phase,
            message=f"阶段完成: {phase.value} ({duration:.2f}s)",
            details={"duration": duration}
        )
        
        self._add_event(event)
    
    def on_task_started(self, task: StartupTask) -> None:
        """任务开始事件"""
        if not self.metrics:
            return
        
        self.metrics.running_tasks += 1
        
        event = ProgressEvent(
            event_type=ProgressEventType.TASK_STARTED,
            timestamp=datetime.now(),
            phase=task.phase,
            task_name=task.name,
            message=f"开始任务: {task.name}"
        )
        
        self._add_event(event)
    
    def on_task_completed(self, task: StartupTask) -> None:
        """任务完成事件"""
        if not self.metrics:
            return
        
        self.metrics.running_tasks = max(0, self.metrics.running_tasks - 1)
        
        if task.status == StartupTaskStatus.COMPLETED:
            self.metrics.completed_tasks += 1
            event_type = ProgressEventType.TASK_COMPLETED
            message = f"任务完成: {task.name} ({task.get_duration():.2f}s)"
        else:
            self.metrics.failed_tasks += 1
            event_type = ProgressEventType.TASK_FAILED
            message = f"任务失败: {task.name} - {task.error}"
        
        if task.get_duration() > 0:
            self.metrics.task_durations[task.name] = task.get_duration()
        
        event = ProgressEvent(
            event_type=event_type,
            timestamp=datetime.now(),
            phase=task.phase,
            task_name=task.name,
            message=message,
            details={
                "duration": task.get_duration(),
                "status": task.status.value,
                "error": str(task.error) if task.error else None
            }
        )
        
        self._add_event(event)
    
    def on_milestone_reached(self, milestone: str, details: Dict[str, Any] = None) -> None:
        """里程碑事件"""
        event = ProgressEvent(
            event_type=ProgressEventType.MILESTONE_REACHED,
            timestamp=datetime.now(),
            message=f"达到里程碑: {milestone}",
            details=details or {}
        )
        
        self._add_event(event)
    
    def _add_event(self, event: ProgressEvent) -> None:
        """添加事件"""
        self.events.append(event)
        self.analyzer.add_event(event)
        
        # 触发事件回调
        for callback in self.event_callbacks:
            try:
                callback(event)
            except Exception as e:
                logger.error(f"事件回调执行失败: {e}")
    
    async def _monitor_loop(self) -> None:
        """监控循环"""
        while self.monitoring:
            try:
                if self.metrics:
                    # 更新当前时间
                    self.metrics.current_time = datetime.now()
                    
                    # 更新系统资源使用情况
                    await self._update_resource_usage()
                    
                    # 报告进度
                    self.reporter.report_progress(self.metrics)
                
                await asyncio.sleep(1.0)  # 每秒更新一次
                
            except Exception as e:
                logger.error(f"进度监控循环异常: {e}")
                await asyncio.sleep(1.0)
    
    async def _update_resource_usage(self) -> None:
        """更新资源使用情况"""
        try:
            import psutil
            
            # 更新内存使用
            memory = psutil.virtual_memory()
            self.metrics.memory_usage_mb = (memory.total - memory.available) / (1024 * 1024)
            
            # 更新CPU使用率
            self.metrics.cpu_usage_percent = psutil.cpu_percent(interval=None)
            
        except Exception as e:
            logger.debug(f"更新资源使用情况失败: {e}")
    
    def get_current_metrics(self) -> Optional[StartupMetrics]:
        """获取当前指标"""
        return self.metrics
    
    def get_events(self) -> List[ProgressEvent]:
        """获取所有事件"""
        return self.events.copy()
    
    def get_startup_analysis(self) -> Dict[str, Any]:
        """获取启动分析"""
        if not self.metrics:
            return {"error": "监控未启动"}
        
        return self.analyzer.analyze_startup_performance(self.metrics)
    
    def generate_startup_report(self) -> Dict[str, Any]:
        """生成启动报告"""
        if not self.metrics:
            return {"error": "监控未启动"}
        
        return {
            "summary": self.metrics.to_dict(),
            "events": [event.to_dict() for event in self.events],
            "analysis": self.get_startup_analysis(),
            "event_count": len(self.events),
            "monitoring_duration": self.metrics.elapsed_time
        }


# 全局启动进度监控器
global_startup_monitor = StartupProgressMonitor()