package com.crypto.trading.sdk.response.model;

import java.math.BigDecimal;
import java.time.Instant;

/**
 * 交易数据模型
 * 表示一个交易记录
 */
public class TradeData {

    /**
     * 交易ID
     */
    private Long id;

    /**
     * 交易对
     */
    private String symbol;

    /**
     * 事件类型
     */
    private String eventType;

    /**
     * 事件时间
     */
    private Long eventTime;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 数量
     */
    private BigDecimal quantity;

    /**
     * 成交额
     */
    private BigDecimal quoteQuantity;

    /**
     * 交易时间
     */
    private Long time;

    /**
     * 买方是否是做市商
     */
    private boolean isBuyerMaker;

    /**
     * 是否是最优价格匹配
     */
    private boolean isBestMatch;

    /**
     * 接收时间
     */
    private Instant receiveTime;

    /**
     * 默认构造函数
     */
    public TradeData() {
    }

    /**
     * 构造函数
     *
     * @param symbol        交易对
     * @param eventType     事件类型
     * @param eventTime     事件时间
     * @param id            交易ID
     * @param price         价格
     * @param quantity      数量
     * @param time          交易时间
     * @param isBuyerMaker  买方是否是做市商
     */
    public TradeData(String symbol, String eventType, Long eventTime, Long id, 
                    BigDecimal price, BigDecimal quantity, Long time, boolean isBuyerMaker) {
        this.symbol = symbol;
        this.eventType = eventType;
        this.eventTime = eventTime;
        this.id = id;
        this.price = price;
        this.quantity = quantity;
        this.time = time;
        this.isBuyerMaker = isBuyerMaker;
        
        // 默认计算成交额
        this.quoteQuantity = price.multiply(quantity);
        
        // 默认设置为最优价格匹配
        this.isBestMatch = true;
        
        // 设置接收时间为当前时间
        this.receiveTime = Instant.now();
    }

    /**
     * 获取交易ID
     *
     * @return 交易ID
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置交易ID
     *
     * @param id 交易ID
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取交易对
     *
     * @return 交易对
     */
    public String getSymbol() {
        return symbol;
    }

    /**
     * 设置交易对
     *
     * @param symbol 交易对
     */
    public void setSymbol(String symbol) {
        this.symbol = symbol;
    }

    /**
     * 获取事件类型
     *
     * @return 事件类型
     */
    public String getEventType() {
        return eventType;
    }

    /**
     * 设置事件类型
     *
     * @param eventType 事件类型
     */
    public void setEventType(String eventType) {
        this.eventType = eventType;
    }

    /**
     * 获取事件时间
     *
     * @return 事件时间
     */
    public Long getEventTime() {
        return eventTime;
    }

    /**
     * 设置事件时间
     *
     * @param eventTime 事件时间
     */
    public void setEventTime(Long eventTime) {
        this.eventTime = eventTime;
    }

    /**
     * 获取价格
     *
     * @return 价格
     */
    public BigDecimal getPrice() {
        return price;
    }

    /**
     * 设置价格
     *
     * @param price 价格
     */
    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    /**
     * 获取数量
     *
     * @return 数量
     */
    public BigDecimal getQuantity() {
        return quantity;
    }

    /**
     * 设置数量
     *
     * @param quantity 数量
     */
    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    /**
     * 获取成交额
     *
     * @return 成交额
     */
    public BigDecimal getQuoteQuantity() {
        return quoteQuantity;
    }

    /**
     * 设置成交额
     *
     * @param quoteQuantity 成交额
     */
    public void setQuoteQuantity(BigDecimal quoteQuantity) {
        this.quoteQuantity = quoteQuantity;
    }

    /**
     * 获取交易时间
     *
     * @return 交易时间
     */
    public Long getTime() {
        return time;
    }

    /**
     * 设置交易时间
     *
     * @param time 交易时间
     */
    public void setTime(Long time) {
        this.time = time;
    }

    /**
     * 获取买方是否是做市商
     *
     * @return 买方是否是做市商
     */
    public boolean isBuyerMaker() {
        return isBuyerMaker;
    }

    /**
     * 设置买方是否是做市商
     *
     * @param buyerMaker 买方是否是做市商
     */
    public void setBuyerMaker(boolean buyerMaker) {
        isBuyerMaker = buyerMaker;
    }

    /**
     * 获取是否是最优价格匹配
     *
     * @return 是否是最优价格匹配
     */
    public boolean isBestMatch() {
        return isBestMatch;
    }

    /**
     * 设置是否是最优价格匹配
     *
     * @param bestMatch 是否是最优价格匹配
     */
    public void setBestMatch(boolean bestMatch) {
        isBestMatch = bestMatch;
    }

    /**
     * 获取接收时间
     *
     * @return 接收时间
     */
    public Instant getReceiveTime() {
        return receiveTime;
    }

    /**
     * 设置接收时间
     *
     * @param receiveTime 接收时间
     */
    public void setReceiveTime(Instant receiveTime) {
        this.receiveTime = receiveTime;
    }
    
    /**
     * 获取数量 (别名方法，与getQuantity相同)
     *
     * @return 数量
     */
    public BigDecimal getQty() {
        return quantity;
    }
    
    /**
     * 获取成交额 (别名方法，与getQuoteQuantity相同)
     *
     * @return 成交额
     */
    public BigDecimal getQuoteQty() {
        return quoteQuantity;
    }

    @Override
    public String toString() {
        return "TradeData{" +
                "id=" + id +
                ", symbol='" + symbol + '\'' +
                ", eventType='" + eventType + '\'' +
                ", eventTime=" + eventTime +
                ", price=" + price +
                ", quantity=" + quantity +
                ", quoteQuantity=" + quoteQuantity +
                ", time=" + time +
                ", isBuyerMaker=" + isBuyerMaker +
                ", isBestMatch=" + isBestMatch +
                ", receiveTime=" + receiveTime +
                '}';
    }
} 