#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
序列化兼容性测试模块

该模块提供序列化/反序列化兼容性测试功能，确保Python和Java之间
的数据序列化格式完全兼容。
"""

import json
import pickle
import time
import gzip
import zlib
from typing import Dict, List, Any, Optional, Union, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime, timezone
import uuid
import struct
from loguru import logger

from .java_api_core import ValidationResult, PerformanceMetrics


@dataclass
class SerializationTestCase:
    """序列化测试用例"""
    name: str
    data: Any
    expected_format: str
    encoding: str = 'utf-8'
    compression: Optional[str] = None


class SerializationCompatibilityTester:
    """
    序列化兼容性测试器
    
    测试Python和Java之间的序列化/反序列化兼容性。
    """
    
    def __init__(self):
        """初始化序列化兼容性测试器"""
        self.test_results: List[ValidationResult] = []
        self.performance_metrics: List[PerformanceMetrics] = []
        logger.info("序列化兼容性测试器初始化完成")
    
    def test_json_compatibility(self) -> List[ValidationResult]:
        """
        测试JSON序列化兼容性
        
        Returns:
            测试结果列表
        """
        logger.info("开始JSON序列化兼容性测试")
        results = []
        
        test_cases = self._create_json_test_cases()
        
        for test_case in test_cases:
            result = self._test_json_serialization(test_case)
            results.append(result)
            self.test_results.append(result)
        
        logger.info(f"JSON兼容性测试完成，共测试{len(results)}个用例")
        return results
    
    def _create_json_test_cases(self) -> List[SerializationTestCase]:
        """
        创建JSON测试用例
        
        Returns:
            测试用例列表
        """
        return [
            SerializationTestCase(
                name="basic_types",
                data={
                    "string": "test",
                    "integer": 123,
                    "float": 123.45,
                    "boolean": True,
                    "null": None,
                    "array": [1, 2, 3],
                    "object": {"nested": "value"}
                },
                expected_format="json"
            ),
            SerializationTestCase(
                name="signal_message",
                data={
                    "messageId": str(uuid.uuid4()),
                    "messageType": "signal",
                    "timestamp": int(time.time() * 1000),
                    "data": {
                        "strategyId": "unified-ml-strategy",
                        "symbol": "BTCUSDT",
                        "signalType": "BUY",
                        "signalStrength": 0.85,
                        "timeFrame": "1h",
                        "riskAssessment": {
                            "marketRisk": 0.35,
                            "volatilityRisk": 0.42,
                            "overallRisk": 0.38
                        }
                    }
                },
                expected_format="json"
            ),
            SerializationTestCase(
                name="kline_data",
                data={
                    "messageId": str(uuid.uuid4()),
                    "messageType": "kline",
                    "timestamp": int(time.time() * 1000),
                    "data": {
                        "symbol": "BTCUSDT",
                        "interval": "1m",
                        "open": 50000.0,
                        "high": 50100.0,
                        "low": 49900.0,
                        "close": 50050.0,
                        "volume": 1000.0,
                        "openTime": int(time.time() * 1000),
                        "closeTime": int(time.time() * 1000) + 60000
                    }
                },
                expected_format="json"
            ),
            SerializationTestCase(
                name="unicode_data",
                data={
                    "chinese": "中文测试",
                    "emoji": "🚀📈💰",
                    "special_chars": "!@#$%^&*()_+-=[]{}|;':\",./<>?"
                },
                expected_format="json"
            ),
            SerializationTestCase(
                name="large_numbers",
                data={
                    "large_int": 9223372036854775807,  # Long.MAX_VALUE in Java
                    "large_float": 1.7976931348623157e+308,  # Double.MAX_VALUE
                    "small_float": 4.9e-324,  # Double.MIN_VALUE
                    "precision_float": 0.123456789012345
                },
                expected_format="json"
            )
        ]
    
    def _test_json_serialization(self, test_case: SerializationTestCase) -> ValidationResult:
        """
        测试JSON序列化
        
        Args:
            test_case: 测试用例
            
        Returns:
            验证结果
        """
        start_time = time.time()
        
        try:
            # 序列化
            serialized = json.dumps(test_case.data, ensure_ascii=False)
            
            # 反序列化
            deserialized = json.loads(serialized)
            
            # 验证数据一致性
            if self._compare_data(test_case.data, deserialized):
                return ValidationResult(
                    test_name=f"json_serialization_{test_case.name}",
                    success=True,
                    message=f"JSON序列化测试通过: {test_case.name}",
                    details={
                        "test_case": test_case.name,
                        "serialized_size": len(serialized),
                        "data_type": type(test_case.data).__name__
                    },
                    execution_time_ms=(time.time() - start_time) * 1000,
                    timestamp=datetime.now(timezone.utc)
                )
            else:
                return ValidationResult(
                    test_name=f"json_serialization_{test_case.name}",
                    success=False,
                    message=f"数据不一致: {test_case.name}",
                    details={
                        "test_case": test_case.name,
                        "original_type": type(test_case.data).__name__,
                        "deserialized_type": type(deserialized).__name__
                    },
                    execution_time_ms=(time.time() - start_time) * 1000,
                    timestamp=datetime.now(timezone.utc)
                )
                
        except Exception as e:
            logger.error(f"JSON序列化测试异常: {e}")
            return ValidationResult(
                test_name=f"json_serialization_{test_case.name}",
                success=False,
                message=f"序列化异常: {str(e)}",
                details={"exception": str(e), "test_case": test_case.name},
                execution_time_ms=(time.time() - start_time) * 1000,
                timestamp=datetime.now(timezone.utc)
            )
    
    def _compare_data(self, original: Any, deserialized: Any) -> bool:
        """
        比较原始数据和反序列化数据
        
        Args:
            original: 原始数据
            deserialized: 反序列化数据
            
        Returns:
            是否一致
        """
        try:
            if type(original) != type(deserialized):
                # 特殊处理：JSON中的数字可能会改变类型
                if isinstance(original, (int, float)) and isinstance(deserialized, (int, float)):
                    return abs(original - deserialized) < 1e-10
                return False
            
            if isinstance(original, dict):
                if set(original.keys()) != set(deserialized.keys()):
                    return False
                return all(self._compare_data(original[k], deserialized[k]) 
                          for k in original.keys())
            
            elif isinstance(original, list):
                if len(original) != len(deserialized):
                    return False
                return all(self._compare_data(original[i], deserialized[i]) 
                          for i in range(len(original)))
            
            elif isinstance(original, float):
                return abs(original - deserialized) < 1e-10
            
            else:
                return original == deserialized
                
        except Exception as e:
            logger.error(f"数据比较异常: {e}")
            return False


class JsonCompatibilityValidator:
    """
    JSON兼容性验证器
    
    专门验证JSON格式的兼容性。
    """
    
    def __init__(self):
        """初始化JSON兼容性验证器"""
        logger.info("JSON兼容性验证器初始化完成")
    
    def validate_java_json_compatibility(self, data: Dict[str, Any]) -> ValidationResult:
        """
        验证Java JSON兼容性
        
        Args:
            data: 待验证数据
            
        Returns:
            验证结果
        """
        start_time = time.time()
        
        try:
            # 检查Java不支持的特性
            issues = []
            
            # 检查NaN和Infinity
            if self._contains_nan_or_infinity(data):
                issues.append("包含NaN或Infinity值，Java JSON可能不支持")
            
            # 检查过长的数字
            if self._contains_oversized_numbers(data):
                issues.append("包含超出Java数值范围的数字")
            
            # 检查特殊字符
            if self._contains_problematic_unicode(data):
                issues.append("包含可能导致编码问题的Unicode字符")
            
            if issues:
                return ValidationResult(
                    test_name="java_json_compatibility",
                    success=False,
                    message=f"发现{len(issues)}个兼容性问题",
                    details={"issues": issues},
                    execution_time_ms=(time.time() - start_time) * 1000,
                    timestamp=datetime.now(timezone.utc)
                )
            
            # 测试序列化
            json_str = json.dumps(data, ensure_ascii=False)
            
            # 测试反序列化
            parsed_data = json.loads(json_str)
            
            return ValidationResult(
                test_name="java_json_compatibility",
                success=True,
                message="Java JSON兼容性验证通过",
                details={
                    "json_size": len(json_str),
                    "field_count": self._count_fields(data)
                },
                execution_time_ms=(time.time() - start_time) * 1000,
                timestamp=datetime.now(timezone.utc)
            )
            
        except Exception as e:
            logger.error(f"Java JSON兼容性验证异常: {e}")
            return ValidationResult(
                test_name="java_json_compatibility",
                success=False,
                message=f"验证异常: {str(e)}",
                details={"exception": str(e)},
                execution_time_ms=(time.time() - start_time) * 1000,
                timestamp=datetime.now(timezone.utc)
            )
    
    def _contains_nan_or_infinity(self, data: Any) -> bool:
        """检查是否包含NaN或Infinity"""
        if isinstance(data, float):
            import math
            return math.isnan(data) or math.isinf(data)
        elif isinstance(data, dict):
            return any(self._contains_nan_or_infinity(v) for v in data.values())
        elif isinstance(data, list):
            return any(self._contains_nan_or_infinity(item) for item in data)
        return False
    
    def _contains_oversized_numbers(self, data: Any) -> bool:
        """检查是否包含超出Java范围的数字"""
        if isinstance(data, int):
            # Java Long范围
            return data < -9223372036854775808 or data > 9223372036854775807
        elif isinstance(data, float):
            # Java Double范围
            return abs(data) > 1.7976931348623157e+308
        elif isinstance(data, dict):
            return any(self._contains_oversized_numbers(v) for v in data.values())
        elif isinstance(data, list):
            return any(self._contains_oversized_numbers(item) for item in data)
        return False
    
    def _contains_problematic_unicode(self, data: Any) -> bool:
        """检查是否包含问题Unicode字符"""
        if isinstance(data, str):
            # 检查控制字符和其他可能有问题的字符
            for char in data:
                code = ord(char)
                # 控制字符（除了常见的空白字符）
                if 0 <= code <= 31 and code not in [9, 10, 13]:  # tab, newline, carriage return
                    return True
                # 私有使用区域
                if 0xE000 <= code <= 0xF8FF:
                    return True
        elif isinstance(data, dict):
            return any(self._contains_problematic_unicode(v) for v in data.values())
        elif isinstance(data, list):
            return any(self._contains_problematic_unicode(item) for item in data)
        return False
    
    def _count_fields(self, data: Any) -> int:
        """计算字段数量"""
        if isinstance(data, dict):
            return len(data) + sum(self._count_fields(v) for v in data.values())
        elif isinstance(data, list):
            return sum(self._count_fields(item) for item in data)
        return 0


class DataTypeValidator:
    """
    数据类型兼容性验证器
    
    验证Python和Java之间的数据类型兼容性。
    """
    
    def __init__(self):
        """初始化数据类型验证器"""
        self.type_mappings = self._create_type_mappings()
        logger.info("数据类型验证器初始化完成")
    
    def _create_type_mappings(self) -> Dict[str, Dict[str, Any]]:
        """
        创建Python-Java类型映射
        
        Returns:
            类型映射字典
        """
        return {
            'python_to_java': {
                'str': 'String',
                'int': 'Long',
                'float': 'Double',
                'bool': 'Boolean',
                'list': 'List',
                'dict': 'Map',
                'NoneType': 'null'
            },
            'java_to_python': {
                'String': 'str',
                'Integer': 'int',
                'Long': 'int',
                'Float': 'float',
                'Double': 'float',
                'Boolean': 'bool',
                'List': 'list',
                'Map': 'dict',
                'null': 'NoneType'
            }
        }
    
    def validate_type_compatibility(self, data: Any) -> ValidationResult:
        """
        验证类型兼容性
        
        Args:
            data: 待验证数据
            
        Returns:
            验证结果
        """
        start_time = time.time()
        
        try:
            incompatible_types = []
            self._check_type_compatibility(data, incompatible_types, "root")
            
            if incompatible_types:
                return ValidationResult(
                    test_name="type_compatibility",
                    success=False,
                    message=f"发现{len(incompatible_types)}个不兼容类型",
                    details={"incompatible_types": incompatible_types},
                    execution_time_ms=(time.time() - start_time) * 1000,
                    timestamp=datetime.now(timezone.utc)
                )
            
            return ValidationResult(
                test_name="type_compatibility",
                success=True,
                message="类型兼容性验证通过",
                details={"data_type": type(data).__name__},
                execution_time_ms=(time.time() - start_time) * 1000,
                timestamp=datetime.now(timezone.utc)
            )
            
        except Exception as e:
            logger.error(f"类型兼容性验证异常: {e}")
            return ValidationResult(
                test_name="type_compatibility",
                success=False,
                message=f"验证异常: {str(e)}",
                details={"exception": str(e)},
                execution_time_ms=(time.time() - start_time) * 1000,
                timestamp=datetime.now(timezone.utc)
            )
    
    def _check_type_compatibility(self, data: Any, 
                                 incompatible_types: List[Dict[str, Any]], 
                                 path: str) -> None:
        """
        递归检查类型兼容性
        
        Args:
            data: 数据
            incompatible_types: 不兼容类型列表
            path: 数据路径
        """
        python_type = type(data).__name__
        java_type = self.type_mappings['python_to_java'].get(python_type)
        
        if java_type is None:
            incompatible_types.append({
                'path': path,
                'python_type': python_type,
                'java_type': 'unknown',
                'compatible': False
            })
        
        # 递归检查嵌套数据
        if isinstance(data, dict):
            for key, value in data.items():
                self._check_type_compatibility(
                    value, incompatible_types, f"{path}.{key}"
                )
        elif isinstance(data, list):
            for i, item in enumerate(data):
                self._check_type_compatibility(
                    item, incompatible_types, f"{path}[{i}]"
                )


class CompressionTester:
    """
    压缩兼容性测试器
    
    测试不同压缩算法的兼容性。
    """
    
    def __init__(self):
        """初始化压缩测试器"""
        logger.info("压缩兼容性测试器初始化完成")
    
    def test_compression_compatibility(self, data: bytes) -> List[ValidationResult]:
        """
        测试压缩兼容性
        
        Args:
            data: 待压缩数据
            
        Returns:
            测试结果列表
        """
        results = []
        
        # 测试gzip压缩
        gzip_result = self._test_gzip_compression(data)
        results.append(gzip_result)
        
        # 测试zlib压缩
        zlib_result = self._test_zlib_compression(data)
        results.append(zlib_result)
        
        return results
    
    def _test_gzip_compression(self, data: bytes) -> ValidationResult:
        """测试gzip压缩"""
        start_time = time.time()
        
        try:
            # 压缩
            compressed = gzip.compress(data)
            
            # 解压缩
            decompressed = gzip.decompress(compressed)
            
            # 验证数据一致性
            success = data == decompressed
            
            return ValidationResult(
                test_name="gzip_compression",
                success=success,
                message=f"gzip压缩测试{'通过' if success else '失败'}",
                details={
                    "original_size": len(data),
                    "compressed_size": len(compressed),
                    "compression_ratio": len(compressed) / len(data) if data else 0
                },
                execution_time_ms=(time.time() - start_time) * 1000,
                timestamp=datetime.now(timezone.utc)
            )
            
        except Exception as e:
            logger.error(f"gzip压缩测试异常: {e}")
            return ValidationResult(
                test_name="gzip_compression",
                success=False,
                message=f"压缩异常: {str(e)}",
                details={"exception": str(e)},
                execution_time_ms=(time.time() - start_time) * 1000,
                timestamp=datetime.now(timezone.utc)
            )
    
    def _test_zlib_compression(self, data: bytes) -> ValidationResult:
        """测试zlib压缩"""
        start_time = time.time()
        
        try:
            # 压缩
            compressed = zlib.compress(data)
            
            # 解压缩
            decompressed = zlib.decompress(compressed)
            
            # 验证数据一致性
            success = data == decompressed
            
            return ValidationResult(
                test_name="zlib_compression",
                success=success,
                message=f"zlib压缩测试{'通过' if success else '失败'}",
                details={
                    "original_size": len(data),
                    "compressed_size": len(compressed),
                    "compression_ratio": len(compressed) / len(data) if data else 0
                },
                execution_time_ms=(time.time() - start_time) * 1000,
                timestamp=datetime.now(timezone.utc)
            )
            
        except Exception as e:
            logger.error(f"zlib压缩测试异常: {e}")
            return ValidationResult(
                test_name="zlib_compression",
                success=False,
                message=f"压缩异常: {str(e)}",
                details={"exception": str(e)},
                execution_time_ms=(time.time() - start_time) * 1000,
                timestamp=datetime.now(timezone.utc)
            )


if __name__ == "__main__":
    # 测试代码
    tester = SerializationCompatibilityTester()
    json_validator = JsonCompatibilityValidator()
    type_validator = DataTypeValidator()
    compression_tester = CompressionTester()
    
    # 测试JSON兼容性
    json_results = tester.test_json_compatibility()
    
    for result in json_results:
        logger.info(f"JSON测试结果: {result.test_name} - {result.success}")
    
    # 测试压缩兼容性
    test_data = b"test data for compression"
    compression_results = compression_tester.test_compression_compatibility(test_data)
    
    for result in compression_results:
        logger.info(f"压缩测试结果: {result.test_name} - {result.success}")