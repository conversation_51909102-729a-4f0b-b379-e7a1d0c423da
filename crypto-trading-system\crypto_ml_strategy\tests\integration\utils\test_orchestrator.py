#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试执行框架模块

负责管理和执行集成测试套件，支持并行执行、依赖管理、超时控制等功能。
"""

import asyncio
import threading
import time
import uuid
from concurrent.futures import ThreadPoolExecutor, as_completed
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from pathlib import Path
from typing import Dict, List, Optional, Callable, Any, Set
from loguru import logger
import json


class TestStatus(Enum):
    """测试状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    PASSED = "passed"
    FAILED = "failed"
    SKIPPED = "skipped"
    TIMEOUT = "timeout"
    ERROR = "error"


class TestPriority(Enum):
    """测试优先级枚举"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4


@dataclass
class TestResult:
    """测试结果"""
    test_id: str
    name: str
    status: TestStatus
    start_time: datetime
    end_time: Optional[datetime] = None
    duration: float = 0.0
    error_message: Optional[str] = None
    error_traceback: Optional[str] = None
    output: str = ""
    metrics: Dict[str, Any] = field(default_factory=dict)
    artifacts: List[str] = field(default_factory=list)


@dataclass
class TestCase:
    """测试用例定义"""
    test_id: str
    name: str
    description: str
    test_function: Callable
    dependencies: List[str] = field(default_factory=list)
    priority: TestPriority = TestPriority.NORMAL
    timeout: int = 300  # 默认5分钟超时
    retry_count: int = 0
    tags: List[str] = field(default_factory=list)
    setup_function: Optional[Callable] = None
    teardown_function: Optional[Callable] = None
    expected_duration: Optional[float] = None


@dataclass
class TestSuite:
    """测试套件"""
    suite_id: str
    name: str
    description: str
    test_cases: List[TestCase] = field(default_factory=list)
    setup_function: Optional[Callable] = None
    teardown_function: Optional[Callable] = None
    parallel_execution: bool = True
    max_workers: int = 4


class TestOrchestrator:
    """测试执行编排器"""
    
    def __init__(self, max_workers: int = 4, default_timeout: int = 300):
        """
        初始化测试编排器
        
        Args:
            max_workers: 最大并行工作线程数
            default_timeout: 默认测试超时时间（秒）
        """
        self.max_workers = max_workers
        self.default_timeout = default_timeout
        self.test_suites: Dict[str, TestSuite] = {}
        self.test_results: Dict[str, TestResult] = {}
        self.execution_order: List[str] = []
        self.dependency_graph: Dict[str, Set[str]] = {}
        self.running_tests: Set[str] = set()
        self.completed_tests: Set[str] = set()
        self.failed_tests: Set[str] = set()
        self.progress_callbacks: List[Callable] = []
        self.resource_locks: Dict[str, threading.Lock] = {}
        self._stop_execution = False
        
        logger.info(f"测试编排器已初始化 - 最大工作线程: {max_workers}")
    
    def register_test_suite(self, test_suite: TestSuite) -> None:
        """
        注册测试套件
        
        Args:
            test_suite: 测试套件
        """
        self.test_suites[test_suite.suite_id] = test_suite
        
        # 构建依赖图
        for test_case in test_suite.test_cases:
            self.dependency_graph[test_case.test_id] = set(test_case.dependencies)
        
        logger.info(f"测试套件已注册: {test_suite.name} ({len(test_suite.test_cases)} 个测试)")
    
    def add_progress_callback(self, callback: Callable[[Dict[str, Any]], None]) -> None:
        """
        添加进度回调函数
        
        Args:
            callback: 进度回调函数
        """
        self.progress_callbacks.append(callback)
    
    def create_resource_lock(self, resource_name: str) -> threading.Lock:
        """
        创建资源锁
        
        Args:
            resource_name: 资源名称
        
        Returns:
            threading.Lock: 资源锁
        """
        if resource_name not in self.resource_locks:
            self.resource_locks[resource_name] = threading.Lock()
        return self.resource_locks[resource_name]
    
    def _validate_dependencies(self) -> List[str]:
        """
        验证测试依赖关系
        
        Returns:
            List[str]: 验证错误列表
        """
        errors = []
        all_test_ids = set()
        
        # 收集所有测试ID
        for suite in self.test_suites.values():
            for test_case in suite.test_cases:
                all_test_ids.add(test_case.test_id)
        
        # 检查依赖关系
        for suite in self.test_suites.values():
            for test_case in suite.test_cases:
                for dep in test_case.dependencies:
                    if dep not in all_test_ids:
                        errors.append(f"测试 {test_case.test_id} 依赖不存在的测试: {dep}")
        
        # 检查循环依赖
        visited = set()
        rec_stack = set()
        
        def has_cycle(test_id: str) -> bool:
            visited.add(test_id)
            rec_stack.add(test_id)
            
            for dep in self.dependency_graph.get(test_id, set()):
                if dep not in visited:
                    if has_cycle(dep):
                        return True
                elif dep in rec_stack:
                    return True
            
            rec_stack.remove(test_id)
            return False
        
        for test_id in all_test_ids:
            if test_id not in visited:
                if has_cycle(test_id):
                    errors.append(f"检测到循环依赖，涉及测试: {test_id}")
        
        return errors
    
    def _calculate_execution_order(self) -> List[str]:
        """
        计算测试执行顺序（拓扑排序）
        
        Returns:
            List[str]: 执行顺序
        """
        # 计算入度
        in_degree = {}
        all_tests = set()
        
        for suite in self.test_suites.values():
            for test_case in suite.test_cases:
                all_tests.add(test_case.test_id)
                in_degree[test_case.test_id] = 0
        
        for test_id in all_tests:
            for dep in self.dependency_graph.get(test_id, set()):
                in_degree[test_id] += 1
        
        # 拓扑排序
        queue = [test_id for test_id, degree in in_degree.items() if degree == 0]
        execution_order = []
        
        while queue:
            # 按优先级排序
            queue.sort(key=lambda x: self._get_test_priority(x), reverse=True)
            current = queue.pop(0)
            execution_order.append(current)
            
            # 更新依赖此测试的其他测试的入度
            for suite in self.test_suites.values():
                for test_case in suite.test_cases:
                    if current in test_case.dependencies:
                        in_degree[test_case.test_id] -= 1
                        if in_degree[test_case.test_id] == 0:
                            queue.append(test_case.test_id)
        
        return execution_order
    
    def _get_test_priority(self, test_id: str) -> int:
        """获取测试优先级"""
        for suite in self.test_suites.values():
            for test_case in suite.test_cases:
                if test_case.test_id == test_id:
                    return test_case.priority.value
        return TestPriority.NORMAL.value
    
    def _get_test_case(self, test_id: str) -> Optional[TestCase]:
        """获取测试用例"""
        for suite in self.test_suites.values():
            for test_case in suite.test_cases:
                if test_case.test_id == test_id:
                    return test_case
        return None
    
    async def execute_all_tests(self) -> Dict[str, TestResult]:
        """
        执行所有测试
        
        Returns:
            Dict[str, TestResult]: 测试结果
        """
        logger.info("开始执行所有测试...")
        
        # 验证依赖关系
        validation_errors = self._validate_dependencies()
        if validation_errors:
            error_msg = "依赖关系验证失败:\n" + "\n".join(validation_errors)
            logger.error(error_msg)
            raise ValueError(error_msg)
        
        # 计算执行顺序
        self.execution_order = self._calculate_execution_order()
        logger.info(f"测试执行顺序已计算: {len(self.execution_order)} 个测试")
        
        # 重置状态
        self.test_results.clear()
        self.running_tests.clear()
        self.completed_tests.clear()
        self.failed_tests.clear()
        self._stop_execution = False
        
        # 执行套件设置
        await self._execute_suite_setups()
        
        try:
            # 并行执行测试
            await self._execute_tests_parallel()
        finally:
            # 执行套件清理
            await self._execute_suite_teardowns()
        
        logger.info(f"所有测试执行完成 - 总计: {len(self.test_results)}")
        return self.test_results
    
    async def _execute_suite_setups(self) -> None:
        """执行套件设置"""
        for suite in self.test_suites.values():
            if suite.setup_function:
                try:
                    logger.info(f"执行套件设置: {suite.name}")
                    if asyncio.iscoroutinefunction(suite.setup_function):
                        await suite.setup_function()
                    else:
                        suite.setup_function()
                except Exception as e:
                    logger.error(f"套件设置失败 {suite.name}: {str(e)}")
                    raise
    
    async def _execute_suite_teardowns(self) -> None:
        """执行套件清理"""
        for suite in self.test_suites.values():
            if suite.teardown_function:
                try:
                    logger.info(f"执行套件清理: {suite.name}")
                    if asyncio.iscoroutinefunction(suite.teardown_function):
                        await suite.teardown_function()
                    else:
                        suite.teardown_function()
                except Exception as e:
                    logger.error(f"套件清理失败 {suite.name}: {str(e)}")
    
    async def _execute_tests_parallel(self) -> None:
        """并行执行测试"""
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            futures = {}
            
            for test_id in self.execution_order:
                if self._stop_execution:
                    break
                
                # 等待依赖完成
                await self._wait_for_dependencies(test_id)
                
                if self._can_execute_test(test_id):
                    test_case = self._get_test_case(test_id)
                    if test_case:
                        future = executor.submit(self._execute_single_test, test_case)
                        futures[future] = test_id
                        self.running_tests.add(test_id)
            
            # 等待所有测试完成
            for future in as_completed(futures):
                test_id = futures[future]
                try:
                    result = future.result()
                    self.test_results[test_id] = result
                    self.completed_tests.add(test_id)
                    self.running_tests.discard(test_id)
                    
                    if result.status == TestStatus.FAILED:
                        self.failed_tests.add(test_id)
                    
                    # 通知进度
                    await self._notify_progress()
                    
                except Exception as e:
                    logger.error(f"测试执行异常 {test_id}: {str(e)}")
                    self.failed_tests.add(test_id)
                    self.completed_tests.add(test_id)
                    self.running_tests.discard(test_id)
    
    async def _wait_for_dependencies(self, test_id: str) -> None:
        """等待依赖测试完成"""
        dependencies = self.dependency_graph.get(test_id, set())
        
        while dependencies and not self._stop_execution:
            remaining_deps = dependencies - self.completed_tests
            if not remaining_deps:
                break
            
            # 检查是否有依赖失败
            failed_deps = dependencies & self.failed_tests
            if failed_deps:
                logger.warning(f"测试 {test_id} 的依赖失败: {failed_deps}")
                # 可以选择跳过或继续执行
                break
            
            await asyncio.sleep(0.1)  # 短暂等待
    
    def _can_execute_test(self, test_id: str) -> bool:
        """检查是否可以执行测试"""
        if test_id in self.completed_tests or test_id in self.running_tests:
            return False
        
        dependencies = self.dependency_graph.get(test_id, set())
        return dependencies.issubset(self.completed_tests)
    
    def _execute_single_test(self, test_case: TestCase) -> TestResult:
        """执行单个测试"""
        result = TestResult(
            test_id=test_case.test_id,
            name=test_case.name,
            status=TestStatus.RUNNING,
            start_time=datetime.now()
        )
        
        logger.info(f"开始执行测试: {test_case.name}")
        
        try:
            # 执行测试设置
            if test_case.setup_function:
                test_case.setup_function()
            
            # 执行测试主体（带超时）
            timeout = test_case.timeout or self.default_timeout
            
            if asyncio.iscoroutinefunction(test_case.test_function):
                # 异步测试函数
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    test_result = loop.run_until_complete(
                        asyncio.wait_for(test_case.test_function(), timeout=timeout)
                    )
                finally:
                    loop.close()
            else:
                # 同步测试函数
                import signal
                
                def timeout_handler(signum, frame):
                    raise TimeoutError(f"测试超时: {timeout}秒")
                
                signal.signal(signal.SIGALRM, timeout_handler)
                signal.alarm(timeout)
                
                try:
                    test_result = test_case.test_function()
                finally:
                    signal.alarm(0)
            
            # 处理测试结果
            if isinstance(test_result, dict):
                result.metrics.update(test_result)
            
            result.status = TestStatus.PASSED
            logger.info(f"测试通过: {test_case.name}")
            
        except TimeoutError as e:
            result.status = TestStatus.TIMEOUT
            result.error_message = str(e)
            logger.error(f"测试超时: {test_case.name}")
            
        except Exception as e:
            result.status = TestStatus.FAILED
            result.error_message = str(e)
            import traceback
            result.error_traceback = traceback.format_exc()
            logger.error(f"测试失败: {test_case.name} - {str(e)}")
            
        finally:
            # 执行测试清理
            try:
                if test_case.teardown_function:
                    test_case.teardown_function()
            except Exception as e:
                logger.error(f"测试清理失败: {test_case.name} - {str(e)}")
            
            result.end_time = datetime.now()
            result.duration = (result.end_time - result.start_time).total_seconds()
        
        return result
    
    async def _notify_progress(self) -> None:
        """通知进度更新"""
        total_tests = len(self.execution_order)
        completed_count = len(self.completed_tests)
        running_count = len(self.running_tests)
        failed_count = len(self.failed_tests)
        
        progress_info = {
            'total_tests': total_tests,
            'completed_tests': completed_count,
            'running_tests': running_count,
            'failed_tests': failed_count,
            'progress_percentage': (completed_count / total_tests * 100) if total_tests > 0 else 0,
            'current_time': datetime.now().isoformat()
        }
        
        for callback in self.progress_callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(progress_info)
                else:
                    callback(progress_info)
            except Exception as e:
                logger.error(f"进度回调失败: {str(e)}")
    
    def stop_execution(self) -> None:
        """停止测试执行"""
        self._stop_execution = True
        logger.warning("测试执行已停止")
    
    def get_execution_summary(self) -> Dict[str, Any]:
        """获取执行摘要"""
        total_tests = len(self.test_results)
        passed_tests = sum(1 for r in self.test_results.values() if r.status == TestStatus.PASSED)
        failed_tests = sum(1 for r in self.test_results.values() if r.status == TestStatus.FAILED)
        timeout_tests = sum(1 for r in self.test_results.values() if r.status == TestStatus.TIMEOUT)
        error_tests = sum(1 for r in self.test_results.values() if r.status == TestStatus.ERROR)
        
        total_duration = sum(r.duration for r in self.test_results.values())
        
        return {
            'total_tests': total_tests,
            'passed_tests': passed_tests,
            'failed_tests': failed_tests,
            'timeout_tests': timeout_tests,
            'error_tests': error_tests,
            'success_rate': (passed_tests / total_tests * 100) if total_tests > 0 else 0,
            'total_duration': total_duration,
            'average_duration': total_duration / total_tests if total_tests > 0 else 0
        }


def create_test_orchestrator(max_workers: int = 4, default_timeout: int = 300) -> TestOrchestrator:
    """
    创建测试编排器实例
    
    Args:
        max_workers: 最大工作线程数
        default_timeout: 默认超时时间
    
    Returns:
        TestOrchestrator: 编排器实例
    """
    return TestOrchestrator(max_workers=max_workers, default_timeout=default_timeout)