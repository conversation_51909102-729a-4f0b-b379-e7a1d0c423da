package com.crypto.trading.common.util;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.UUID;
import java.util.concurrent.ThreadLocalRandom;

/**
 * ID生成器工具类
 * <p>
 * 提供各种ID生成方法
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class IdGenerator {

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS");
    private static final String ALPHA_NUMERIC_CHARS = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    
    private IdGenerator() {
        throw new IllegalStateException("Utility class");
    }
    
    /**
     * 生成UUID
     *
     * @return UUID字符串（不含连字符）
     */
    public static String generateUuid() {
        return UUID.randomUUID().toString().replace("-", "");
    }
    
    /**
     * 生成订单ID
     * <p>
     * 格式：ORDER_前缀 + 时间戳 + 随机数
     * </p>
     *
     * @return 订单ID
     */
    public static String generateOrderId() {
        return "ORDER_" + generateTimeBasedId();
    }
    
    /**
     * 生成客户端订单ID
     * <p>
     * 格式：CLIENT_前缀 + 时间戳 + 随机数
     * </p>
     *
     * @return 客户端订单ID
     */
    public static String generateClientOrderId() {
        return "CLIENT_" + generateTimeBasedId();
    }
    
    /**
     * 生成信号ID
     * <p>
     * 格式：SIGNAL_前缀 + 时间戳 + 随机数
     * </p>
     *
     * @return 信号ID
     */
    public static String generateSignalId() {
        return "SIGNAL_" + generateTimeBasedId();
    }
    
    /**
     * 生成交易ID
     * <p>
     * 格式：TRADE_前缀 + 时间戳 + 随机数
     * </p>
     *
     * @return 交易ID
     */
    public static String generateTradeId() {
        return "TRADE_" + generateTimeBasedId();
    }
    
    /**
     * 生成基于时间的ID
     * <p>
     * 格式：时间戳 + 5位随机数
     * </p>
     *
     * @return 时间戳ID
     */
    private static String generateTimeBasedId() {
        String timestamp = LocalDateTime.now().format(DATE_TIME_FORMATTER);
        int random = ThreadLocalRandom.current().nextInt(10000, 100000);
        return timestamp + random;
    }
    
    /**
     * 生成指定长度的随机字母数字字符串
     *
     * @param length 字符串长度
     * @return 随机字母数字字符串
     */
    public static String generateRandomAlphanumeric(int length) {
        if (length <= 0) {
            throw new IllegalArgumentException("Length must be greater than 0");
        }
        
        StringBuilder sb = new StringBuilder(length);
        ThreadLocalRandom random = ThreadLocalRandom.current();
        
        for (int i = 0; i < length; i++) {
            int index = random.nextInt(ALPHA_NUMERIC_CHARS.length());
            sb.append(ALPHA_NUMERIC_CHARS.charAt(index));
        }
        
        return sb.toString();
    }
}