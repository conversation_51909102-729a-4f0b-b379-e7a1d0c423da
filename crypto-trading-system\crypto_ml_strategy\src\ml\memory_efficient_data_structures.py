"""
Crypto ML Strategy - 内存高效数据结构

该模块实现了针对加密货币交易数据处理优化的内存高效数据结构，
包括自定义DataFrame、循环缓冲区、压缩时间序列存储、内存池和性能分析器。

主要功能：
- MemoryOptimizedDataFrame: 减少内存占用的自定义DataFrame实现
- CircularBuffer: 流数据的环形缓冲区，自动内存管理
- CompressedTimeSeriesStorage: 历史时间序列数据的压缩存储
- MemoryPool: 频繁分配/释放的对象池系统
- DataStructureProfiler: 内存使用监控和优化建议

作者: Crypto ML Strategy Team
日期: 2024-01-20
版本: 1.0.0
"""

import gc
import sys
import threading
import weakref
from collections import deque
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Union, Tuple, Iterator, Type
import numpy as np
import pandas as pd
from infrastructure.logging.logging_core_manager import get_logger
from benchmarks.performance_logging_core import get_global_performance_logger
from infrastructure.error_handling_system import get_global_error_handler
import lz4.frame
import pickle
import psutil
from pathlib import Path


@dataclass
class MemoryUsageStats:
    """内存使用统计"""
    total_memory_mb: float
    used_memory_mb: float
    available_memory_mb: float
    process_memory_mb: float
    gc_collections: int
    object_count: int
    timestamp: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "total_memory_mb": self.total_memory_mb,
            "used_memory_mb": self.used_memory_mb,
            "available_memory_mb": self.available_memory_mb,
            "process_memory_mb": self.process_memory_mb,
            "gc_collections": self.gc_collections,
            "object_count": self.object_count,
            "timestamp": self.timestamp.isoformat()
        }


class MemoryOptimizedDataFrame:
    """内存优化的DataFrame实现"""
    
    def __init__(self, data: Optional[Dict[str, np.ndarray]] = None, 
                 optimize_dtypes: bool = True):
        self.logger = get_logger(f"{__name__}.MemoryOptimizedDataFrame")
        self.perf_logger = get_global_performance_logger()
        self.error_handler = get_global_error_handler()
        
        self._data: Dict[str, np.ndarray] = data or {}
        self._index: Optional[np.ndarray] = None
        self._columns: List[str] = list(self._data.keys()) if data else []
        self._lock = threading.RLock()
        
        if optimize_dtypes and data:
            self._optimize_dtypes()
    
    def _optimize_dtypes(self) -> None:
        """优化数据类型以减少内存使用"""
        try:
            with self._lock:
                for column, array in self._data.items():
                    if array.dtype == np.float64:
                        # 检查是否可以降级为float32
                        if np.all(np.isfinite(array)):
                            min_val, max_val = np.min(array), np.max(array)
                            if (min_val >= np.finfo(np.float32).min and 
                                max_val <= np.finfo(np.float32).max):
                                self._data[column] = array.astype(np.float32)
                                self.logger.debug(f"Optimized {column} from float64 to float32")
                    
                    elif array.dtype == np.int64:
                        # 检查是否可以降级为int32或int16
                        min_val, max_val = np.min(array), np.max(array)
                        if (min_val >= np.iinfo(np.int32).min and 
                            max_val <= np.iinfo(np.int32).max):
                            if (min_val >= np.iinfo(np.int16).min and 
                                max_val <= np.iinfo(np.int16).max):
                                self._data[column] = array.astype(np.int16)
                                self.logger.debug(f"Optimized {column} from int64 to int16")
                            else:
                                self._data[column] = array.astype(np.int32)
                                self.logger.debug(f"Optimized {column} from int64 to int32")
                                
        except Exception as e:
            self.error_handler.handle_error(e, context={"operation": "dtype_optimization"})
    
    def add_column(self, name: str, data: np.ndarray) -> None:
        """添加列"""
        with self._lock:
            if len(self._data) > 0:
                expected_length = len(next(iter(self._data.values())))
                if len(data) != expected_length:
                    raise ValueError(f"Data length {len(data)} doesn't match expected {expected_length}")
            
            self._data[name] = data
            if name not in self._columns:
                self._columns.append(name)
    
    def get_column(self, name: str) -> Optional[np.ndarray]:
        """获取列数据"""
        with self._lock:
            return self._data.get(name)
    
    def memory_usage(self) -> Dict[str, int]:
        """计算内存使用情况"""
        with self._lock:
            usage = {}
            total = 0
            
            for column, array in self._data.items():
                size = array.nbytes
                usage[column] = size
                total += size
            
            usage["total"] = total
            return usage
    
    def to_pandas(self) -> pd.DataFrame:
        """转换为pandas DataFrame"""
        with self._lock:
            return pd.DataFrame(self._data)
    
    @classmethod
    def from_pandas(cls, df: pd.DataFrame, optimize_dtypes: bool = True) -> 'MemoryOptimizedDataFrame':
        """从pandas DataFrame创建"""
        data = {col: df[col].values for col in df.columns}
        return cls(data, optimize_dtypes)


class CircularBuffer:
    """环形缓冲区，用于流数据的内存高效存储"""
    
    def __init__(self, capacity: int, dtype: Type = float):
        self.capacity = capacity
        self.dtype = dtype
        self.logger = get_logger(f"{__name__}.CircularBuffer")
        
        self._buffer = np.empty(capacity, dtype=dtype)
        self._head = 0
        self._tail = 0
        self._size = 0
        self._full = False
        self._lock = threading.RLock()
    
    def append(self, item: Any) -> None:
        """添加元素到缓冲区"""
        with self._lock:
            self._buffer[self._head] = item
            
            if self._full:
                self._tail = (self._tail + 1) % self.capacity
            
            self._head = (self._head + 1) % self.capacity
            
            if self._head == self._tail:
                self._full = True
            
            self._size = min(self._size + 1, self.capacity)
    
    def extend(self, items: List[Any]) -> None:
        """批量添加元素"""
        for item in items:
            self.append(item)
    
    def get_latest(self, n: int = 1) -> np.ndarray:
        """获取最新的n个元素"""
        with self._lock:
            if n > self._size:
                n = self._size
            
            if n == 0:
                return np.array([], dtype=self.dtype)
            
            if self._head >= n:
                return self._buffer[self._head - n:self._head].copy()
            else:
                # 跨越边界的情况
                part1 = self._buffer[self.capacity - (n - self._head):]
                part2 = self._buffer[:self._head]
                return np.concatenate([part1, part2])
    
    def get_all(self) -> np.ndarray:
        """获取所有有效数据"""
        with self._lock:
            if self._size == 0:
                return np.array([], dtype=self.dtype)
            
            if not self._full:
                return self._buffer[:self._head].copy()
            else:
                # 缓冲区已满，需要重新排序
                return np.concatenate([
                    self._buffer[self._tail:],
                    self._buffer[:self._tail]
                ])
    
    def clear(self) -> None:
        """清空缓冲区"""
        with self._lock:
            self._head = 0
            self._tail = 0
            self._size = 0
            self._full = False
    
    def is_full(self) -> bool:
        """检查缓冲区是否已满"""
        return self._full
    
    def size(self) -> int:
        """获取当前大小"""
        return self._size
    
    def memory_usage(self) -> int:
        """获取内存使用量（字节）"""
        return self._buffer.nbytes


class CompressedTimeSeriesStorage:
    """压缩时间序列数据存储"""
    
    def __init__(self, compression_level: int = 4):
        self.compression_level = compression_level
        self.logger = get_logger(f"{__name__}.CompressedTimeSeriesStorage")
        self.perf_logger = get_global_performance_logger()
        
        self._compressed_data: Dict[str, bytes] = {}
        self._metadata: Dict[str, Dict[str, Any]] = {}
        self._lock = threading.RLock()
    
    def store_series(self, key: str, timestamps: np.ndarray, 
                    values: np.ndarray, metadata: Optional[Dict[str, Any]] = None) -> None:
        """存储时间序列数据"""
        try:
            with self._lock:
                # 准备数据
                data_dict = {
                    'timestamps': timestamps,
                    'values': values,
                    'dtype': str(values.dtype),
                    'shape': values.shape
                }
                
                # 序列化
                serialized = pickle.dumps(data_dict, protocol=pickle.HIGHEST_PROTOCOL)
                
                # 压缩
                compressed = lz4.frame.compress(
                    serialized, 
                    compression_level=self.compression_level
                )
                
                self._compressed_data[key] = compressed
                self._metadata[key] = {
                    'original_size': len(serialized),
                    'compressed_size': len(compressed),
                    'compression_ratio': len(serialized) / len(compressed),
                    'timestamp': datetime.now(),
                    'metadata': metadata or {}
                }
                
                self.logger.debug(f"Stored series {key}, compression ratio: "
                                f"{self._metadata[key]['compression_ratio']:.2f}")
                
        except Exception as e:
            self.error_handler.handle_error(e, context={"key": key, "operation": "store_series"})
            raise
    
    def load_series(self, key: str) -> Optional[Tuple[np.ndarray, np.ndarray]]:
        """加载时间序列数据"""
        try:
            with self._lock:
                if key not in self._compressed_data:
                    return None
                
                # 解压缩
                compressed = self._compressed_data[key]
                decompressed = lz4.frame.decompress(compressed)
                
                # 反序列化
                data_dict = pickle.loads(decompressed)
                
                return data_dict['timestamps'], data_dict['values']
                
        except Exception as e:
            self.error_handler.handle_error(e, context={"key": key, "operation": "load_series"})
            return None
    
    def get_metadata(self, key: str) -> Optional[Dict[str, Any]]:
        """获取元数据"""
        with self._lock:
            return self._metadata.get(key)
    
    def list_keys(self) -> List[str]:
        """列出所有键"""
        with self._lock:
            return list(self._compressed_data.keys())
    
    def remove_series(self, key: str) -> bool:
        """删除时间序列"""
        with self._lock:
            if key in self._compressed_data:
                del self._compressed_data[key]
                del self._metadata[key]
                return True
            return False
    
    def memory_usage(self) -> Dict[str, Any]:
        """计算内存使用情况"""
        with self._lock:
            total_compressed = sum(len(data) for data in self._compressed_data.values())
            total_original = sum(meta['original_size'] for meta in self._metadata.values())
            
            return {
                'total_compressed_bytes': total_compressed,
                'total_original_bytes': total_original,
                'compression_ratio': total_original / max(total_compressed, 1),
                'series_count': len(self._compressed_data),
                'average_compression': np.mean([meta['compression_ratio'] 
                                              for meta in self._metadata.values()])
            }


class MemoryPool:
    """对象池系统，减少频繁分配/释放的开销"""
    
    def __init__(self, object_type: Type, initial_size: int = 10, max_size: int = 100):
        self.object_type = object_type
        self.max_size = max_size
        self.logger = get_logger(f"{__name__}.MemoryPool")
        
        self._pool: deque = deque()
        self._in_use: weakref.WeakSet = weakref.WeakSet()
        self._lock = threading.RLock()
        self._created_count = 0
        self._reused_count = 0
        
        # 预创建对象
        for _ in range(initial_size):
            self._create_object()
    
    def _create_object(self) -> Any:
        """创建新对象"""
        try:
            if hasattr(self.object_type, '__call__'):
                obj = self.object_type()
            else:
                obj = self.object_type
            
            self._created_count += 1
            return obj
        except Exception as e:
            self.logger.error(f"Failed to create object of type {self.object_type}: {e}")
            raise
    
    def acquire(self) -> Any:
        """获取对象"""
        with self._lock:
            if self._pool:
                obj = self._pool.popleft()
                self._reused_count += 1
            else:
                obj = self._create_object()
            
            self._in_use.add(obj)
            return obj
    
    def release(self, obj: Any) -> None:
        """释放对象回池中"""
        with self._lock:
            if obj in self._in_use:
                self._in_use.discard(obj)
                
                if len(self._pool) < self.max_size:
                    # 重置对象状态（如果有reset方法）
                    if hasattr(obj, 'reset'):
                        obj.reset()
                    
                    self._pool.append(obj)
                # 如果池已满，让对象被垃圾回收
    
    def get_stats(self) -> Dict[str, Any]:
        """获取池统计信息"""
        with self._lock:
            return {
                'pool_size': len(self._pool),
                'in_use_count': len(self._in_use),
                'created_count': self._created_count,
                'reused_count': self._reused_count,
                'reuse_ratio': self._reused_count / max(self._created_count, 1),
                'max_size': self.max_size
            }
    
    def clear(self) -> None:
        """清空池"""
        with self._lock:
            self._pool.clear()
            self._in_use.clear()


class DataStructureProfiler:
    """数据结构内存使用分析器"""
    
    def __init__(self):
        self.logger = get_logger(f"{__name__}.DataStructureProfiler")
        self.perf_logger = get_global_performance_logger()
        
        self._snapshots: List[MemoryUsageStats] = []
        self._lock = threading.RLock()
    
    def take_snapshot(self) -> MemoryUsageStats:
        """获取当前内存使用快照"""
        try:
            # 系统内存信息
            memory = psutil.virtual_memory()
            process = psutil.Process()
            process_memory = process.memory_info()
            
            # GC信息
            gc_stats = gc.get_stats()
            total_collections = sum(stat['collections'] for stat in gc_stats)
            
            # 对象计数
            object_count = len(gc.get_objects())
            
            snapshot = MemoryUsageStats(
                total_memory_mb=memory.total / 1024 / 1024,
                used_memory_mb=memory.used / 1024 / 1024,
                available_memory_mb=memory.available / 1024 / 1024,
                process_memory_mb=process_memory.rss / 1024 / 1024,
                gc_collections=total_collections,
                object_count=object_count
            )
            
            with self._lock:
                self._snapshots.append(snapshot)
                
                # 保留最近100个快照
                if len(self._snapshots) > 100:
                    self._snapshots = self._snapshots[-100:]
            
            return snapshot
            
        except Exception as e:
            self.logger.error(f"Failed to take memory snapshot: {e}")
            raise
    
    def analyze_memory_trend(self, window_size: int = 10) -> Dict[str, Any]:
        """分析内存使用趋势"""
        with self._lock:
            if len(self._snapshots) < window_size:
                return {"error": "Insufficient snapshots for trend analysis"}
            
            recent_snapshots = self._snapshots[-window_size:]
            
            # 计算趋势
            process_memory_values = [s.process_memory_mb for s in recent_snapshots]
            object_count_values = [s.object_count for s in recent_snapshots]
            
            memory_trend = np.polyfit(range(len(process_memory_values)), process_memory_values, 1)[0]
            object_trend = np.polyfit(range(len(object_count_values)), object_count_values, 1)[0]
            
            return {
                "memory_trend_mb_per_snapshot": memory_trend,
                "object_trend_per_snapshot": object_trend,
                "current_memory_mb": recent_snapshots[-1].process_memory_mb,
                "memory_change_mb": recent_snapshots[-1].process_memory_mb - recent_snapshots[0].process_memory_mb,
                "object_change": recent_snapshots[-1].object_count - recent_snapshots[0].object_count,
                "analysis_window": window_size,
                "recommendation": self._generate_recommendations(memory_trend, object_trend)
            }
    
    def _generate_recommendations(self, memory_trend: float, object_trend: float) -> List[str]:
        """生成优化建议"""
        recommendations = []
        
        if memory_trend > 1.0:  # 内存增长超过1MB每快照
            recommendations.append("检测到内存使用持续增长，建议检查内存泄漏")
        
        if object_trend > 1000:  # 对象数量增长超过1000每快照
            recommendations.append("对象数量快速增长，建议使用对象池或优化对象生命周期")
        
        if memory_trend > 0.5 and object_trend > 500:
            recommendations.append("内存和对象数量同时增长，建议进行垃圾回收优化")
        
        if not recommendations:
            recommendations.append("内存使用模式正常")
        
        return recommendations
    
    def get_memory_summary(self) -> Dict[str, Any]:
        """获取内存使用摘要"""
        with self._lock:
            if not self._snapshots:
                return {"error": "No snapshots available"}
            
            latest = self._snapshots[-1]
            
            if len(self._snapshots) > 1:
                first = self._snapshots[0]
                memory_change = latest.process_memory_mb - first.process_memory_mb
                object_change = latest.object_count - first.object_count
            else:
                memory_change = 0
                object_change = 0
            
            return {
                "current_memory_mb": latest.process_memory_mb,
                "available_memory_mb": latest.available_memory_mb,
                "memory_usage_percent": (latest.used_memory_mb / latest.total_memory_mb) * 100,
                "total_snapshots": len(self._snapshots),
                "memory_change_mb": memory_change,
                "object_change": object_change,
                "gc_collections": latest.gc_collections,
                "timestamp": latest.timestamp.isoformat()
            }


# 全局实例
_global_profiler: Optional[DataStructureProfiler] = None


def get_global_profiler() -> DataStructureProfiler:
    """获取全局内存分析器实例"""
    global _global_profiler
    
    if _global_profiler is None:
        _global_profiler = DataStructureProfiler()
    
    return _global_profiler


# 便捷函数
def create_optimized_dataframe(data: Dict[str, np.ndarray]) -> MemoryOptimizedDataFrame:
    """创建内存优化的DataFrame"""
    return MemoryOptimizedDataFrame(data, optimize_dtypes=True)


def create_circular_buffer(capacity: int, dtype: Type = float) -> CircularBuffer:
    """创建环形缓冲区"""
    return CircularBuffer(capacity, dtype)


def create_compressed_storage(compression_level: int = 4) -> CompressedTimeSeriesStorage:
    """创建压缩时间序列存储"""
    return CompressedTimeSeriesStorage(compression_level)


def create_memory_pool(object_type: Type, initial_size: int = 10, 
                      max_size: int = 100) -> MemoryPool:
    """创建内存池"""
    return MemoryPool(object_type, initial_size, max_size)


class MemoryEfficientDataStructures:
    """
    内存高效数据结构管理器
    
    这是内存优化系统的主要管理类，提供了完整的内存高效数据结构功能，
    包括内存优化DataFrame、环形缓冲区、压缩存储和对象池管理。
    
    主要功能：
    - 内存优化DataFrame管理
    - 环形缓冲区操作
    - 压缩时间序列存储
    - 对象池管理
    - 内存使用分析
    
    使用示例：
        mem_structures = MemoryEfficientDataStructures()
        await mem_structures.initialize()
        df = mem_structures.create_optimized_dataframe(data)
        buffer = mem_structures.create_circular_buffer(1000)
        stats = mem_structures.get_memory_statistics()
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化内存高效数据结构管理器
        
        Args:
            config: 配置字典，包含缓冲区大小、压缩级别等设置
        """
        self.config = config or {}
        self.logger = get_logger(f"{__name__}.MemoryEfficientDataStructures")
        self.profiler = get_global_profiler()
        
        # 管理的数据结构
        self._dataframes: Dict[str, MemoryOptimizedDataFrame] = {}
        self._buffers: Dict[str, CircularBuffer] = {}
        self._compressed_storage: Dict[str, CompressedTimeSeriesStorage] = {}
        self._memory_pools: Dict[str, MemoryPool] = {}
        
        # 统计信息
        self._creation_count = 0
        self._total_memory_saved = 0.0
        self._lock = threading.RLock()
        
        logger.info("MemoryEfficientDataStructures initialized")
    
    async def initialize(self) -> bool:
        """
        初始化数据结构管理器
        
        Returns:
            是否初始化成功
        """
        try:
            self.logger.info("初始化内存高效数据结构管理器")
            
            # 创建默认数据结构
            default_config = self.config.get("defaults", {})
            
            # 创建默认环形缓冲区
            if "default_buffer_size" in default_config:
                self.create_circular_buffer(
                    "default", 
                    default_config["default_buffer_size"]
                )
            
            # 创建默认压缩存储
            if "default_compression_level" in default_config:
                self.create_compressed_storage(
                    "default",
                    default_config["default_compression_level"]
                )
            
            # 创建默认对象池
            if "default_pool_config" in default_config:
                pool_config = default_config["default_pool_config"]
                self.create_memory_pool(
                    "default",
                    pool_config.get("object_type", dict),
                    pool_config.get("initial_size", 10),
                    pool_config.get("max_size", 100)
                )
            
            # 开始内存监控
            self.profiler.take_snapshot()
            
            self.logger.info("内存高效数据结构管理器初始化完成")
            return True
            
        except Exception as e:
            self.logger.error(f"内存高效数据结构管理器初始化失败: {e}")
            return False
    
    def create_optimized_dataframe(self, name: str, data: Dict[str, np.ndarray],
                                 optimize_dtypes: bool = True) -> MemoryOptimizedDataFrame:
        """
        创建内存优化DataFrame
        
        Args:
            name: DataFrame名称
            data: 数据字典
            optimize_dtypes: 是否优化数据类型
            
        Returns:
            内存优化DataFrame实例
        """
        try:
            with self._lock:
                df = MemoryOptimizedDataFrame(data, optimize_dtypes)
                self._dataframes[name] = df
                self._creation_count += 1
                
                # 估算内存节省
                original_size = sum(arr.nbytes for arr in data.values())
                optimized_size = df.memory_usage()["total_memory_mb"] * 1024 * 1024
                memory_saved = original_size - optimized_size
                self._total_memory_saved += memory_saved
                
                self.logger.info(f"创建优化DataFrame: {name}, "
                               f"内存节省: {memory_saved / 1024 / 1024:.1f}MB")
                
                return df
                
        except Exception as e:
            self.logger.error(f"创建优化DataFrame失败: {e}")
            raise
    
    def create_circular_buffer(self, name: str, capacity: int, 
                             dtype: Type = float) -> CircularBuffer:
        """
        创建环形缓冲区
        
        Args:
            name: 缓冲区名称
            capacity: 容量
            dtype: 数据类型
            
        Returns:
            环形缓冲区实例
        """
        try:
            with self._lock:
                buffer = CircularBuffer(capacity, dtype)
                self._buffers[name] = buffer
                self._creation_count += 1
                
                self.logger.info(f"创建环形缓冲区: {name}, 容量: {capacity}")
                return buffer
                
        except Exception as e:
            self.logger.error(f"创建环形缓冲区失败: {e}")
            raise
    
    def create_compressed_storage(self, name: str, 
                                compression_level: int = 4) -> CompressedTimeSeriesStorage:
        """
        创建压缩时间序列存储
        
        Args:
            name: 存储名称
            compression_level: 压缩级别
            
        Returns:
            压缩时间序列存储实例
        """
        try:
            with self._lock:
                storage = CompressedTimeSeriesStorage(compression_level)
                self._compressed_storage[name] = storage
                self._creation_count += 1
                
                self.logger.info(f"创建压缩存储: {name}, 压缩级别: {compression_level}")
                return storage
                
        except Exception as e:
            self.logger.error(f"创建压缩存储失败: {e}")
            raise
    
    def create_memory_pool(self, name: str, object_type: Type,
                          initial_size: int = 10, max_size: int = 100) -> MemoryPool:
        """
        创建内存池
        
        Args:
            name: 内存池名称
            object_type: 对象类型
            initial_size: 初始大小
            max_size: 最大大小
            
        Returns:
            内存池实例
        """
        try:
            with self._lock:
                pool = MemoryPool(object_type, initial_size, max_size)
                self._memory_pools[name] = pool
                self._creation_count += 1
                
                self.logger.info(f"创建内存池: {name}, 类型: {object_type.__name__}")
                return pool
                
        except Exception as e:
            self.logger.error(f"创建内存池失败: {e}")
            raise
    
    def get_dataframe(self, name: str) -> Optional[MemoryOptimizedDataFrame]:
        """获取DataFrame"""
        with self._lock:
            return self._dataframes.get(name)
    
    def get_buffer(self, name: str) -> Optional[CircularBuffer]:
        """获取环形缓冲区"""
        with self._lock:
            return self._buffers.get(name)
    
    def get_compressed_storage(self, name: str) -> Optional[CompressedTimeSeriesStorage]:
        """获取压缩存储"""
        with self._lock:
            return self._compressed_storage.get(name)
    
    def get_memory_pool(self, name: str) -> Optional[MemoryPool]:
        """获取内存池"""
        with self._lock:
            return self._memory_pools.get(name)
    
    def list_structures(self) -> Dict[str, List[str]]:
        """列出所有数据结构"""
        with self._lock:
            return {
                "dataframes": list(self._dataframes.keys()),
                "buffers": list(self._buffers.keys()),
                "compressed_storage": list(self._compressed_storage.keys()),
                "memory_pools": list(self._memory_pools.keys())
            }
    
    def remove_structure(self, structure_type: str, name: str) -> bool:
        """
        移除数据结构
        
        Args:
            structure_type: 结构类型 (dataframe, buffer, storage, pool)
            name: 结构名称
            
        Returns:
            是否成功移除
        """
        try:
            with self._lock:
                if structure_type == "dataframe" and name in self._dataframes:
                    del self._dataframes[name]
                    return True
                elif structure_type == "buffer" and name in self._buffers:
                    del self._buffers[name]
                    return True
                elif structure_type == "storage" and name in self._compressed_storage:
                    del self._compressed_storage[name]
                    return True
                elif structure_type == "pool" and name in self._memory_pools:
                    pool = self._memory_pools[name]
                    pool.clear()
                    del self._memory_pools[name]
                    return True
                
                return False
                
        except Exception as e:
            self.logger.error(f"移除数据结构失败: {e}")
            return False
    
    def get_memory_statistics(self) -> Dict[str, Any]:
        """
        获取内存统计信息
        
        Returns:
            内存统计字典
        """
        try:
            with self._lock:
                # 获取当前内存快照
                current_snapshot = self.profiler.take_snapshot()
                
                # 计算各类数据结构的内存使用
                dataframe_memory = 0
                for df in self._dataframes.values():
                    dataframe_memory += df.memory_usage()["total_memory_mb"]
                
                buffer_memory = 0
                for buffer in self._buffers.values():
                    buffer_memory += buffer.capacity * 8 / 1024 / 1024  # 假设8字节per item
                
                storage_memory = 0
                for storage in self._compressed_storage.values():
                    usage = storage.memory_usage()
                    storage_memory += usage["total_compressed_bytes"] / 1024 / 1024
                
                pool_memory = 0
                for pool in self._memory_pools.values():
                    stats = pool.get_stats()
                    pool_memory += stats["pool_size"] * 0.1  # 估算每个对象0.1MB
                
                return {
                    "timestamp": current_snapshot.timestamp.isoformat(),
                    "total_structures": self._creation_count,
                    "current_structures": {
                        "dataframes": len(self._dataframes),
                        "buffers": len(self._buffers),
                        "compressed_storage": len(self._compressed_storage),
                        "memory_pools": len(self._memory_pools)
                    },
                    "memory_usage_mb": {
                        "dataframes": dataframe_memory,
                        "buffers": buffer_memory,
                        "compressed_storage": storage_memory,
                        "memory_pools": pool_memory,
                        "total": dataframe_memory + buffer_memory + storage_memory + pool_memory
                    },
                    "memory_saved_mb": self._total_memory_saved / 1024 / 1024,
                    "system_memory": {
                        "process_memory_mb": current_snapshot.process_memory_mb,
                        "available_memory_mb": current_snapshot.available_memory_mb,
                        "memory_usage_percent": (current_snapshot.used_memory_mb / current_snapshot.total_memory_mb) * 100
                    },
                    "profiler_summary": self.profiler.get_memory_summary()
                }
                
        except Exception as e:
            self.logger.error(f"获取内存统计失败: {e}")
            return {"error": "Failed to get memory statistics"}
    
    def optimize_memory_usage(self) -> Dict[str, Any]:
        """
        优化内存使用
        
        Returns:
            优化结果字典
        """
        try:
            self.logger.info("开始内存使用优化")
            
            optimization_results = {
                "actions_taken": [],
                "memory_freed_mb": 0,
                "structures_optimized": 0
            }
            
            with self._lock:
                # 优化DataFrame
                for name, df in self._dataframes.items():
                    try:
                        original_memory = df.memory_usage()["total_memory_mb"]
                        df.optimize_memory()
                        new_memory = df.memory_usage()["total_memory_mb"]
                        
                        if original_memory > new_memory:
                            freed = original_memory - new_memory
                            optimization_results["memory_freed_mb"] += freed
                            optimization_results["structures_optimized"] += 1
                            optimization_results["actions_taken"].append(
                                f"优化DataFrame {name}: 释放 {freed:.1f}MB"
                            )
                    except Exception as e:
                        self.logger.warning(f"优化DataFrame {name} 失败: {e}")
                
                # 清理空的内存池
                empty_pools = []
                for name, pool in self._memory_pools.items():
                    stats = pool.get_stats()
                    if stats["pool_size"] == 0 and stats["in_use_count"] == 0:
                        empty_pools.append(name)
                
                for name in empty_pools:
                    del self._memory_pools[name]
                    optimization_results["actions_taken"].append(f"移除空内存池: {name}")
                
                # 触发垃圾回收
                import gc
                collected = gc.collect()
                if collected > 0:
                    optimization_results["actions_taken"].append(f"垃圾回收: 清理 {collected} 个对象")
            
            self.logger.info(f"内存优化完成: 释放 {optimization_results['memory_freed_mb']:.1f}MB, "
                           f"优化 {optimization_results['structures_optimized']} 个结构")
            
            return optimization_results
            
        except Exception as e:
            self.logger.error(f"内存优化失败: {e}")
            return {"error": "Memory optimization failed"}
    
    def export_memory_report(self) -> Dict[str, Any]:
        """
        导出内存报告
        
        Returns:
            内存报告字典
        """
        try:
            report = {
                "timestamp": datetime.now().isoformat(),
                "configuration": self.config,
                "memory_statistics": self.get_memory_statistics(),
                "structure_details": {},
                "optimization_recommendations": self._generate_optimization_recommendations()
            }
            
            # 添加详细的结构信息
            with self._lock:
                # DataFrame详情
                report["structure_details"]["dataframes"] = {}
                for name, df in self._dataframes.items():
                    report["structure_details"]["dataframes"][name] = df.memory_usage()
                
                # 缓冲区详情
                report["structure_details"]["buffers"] = {}
                for name, buffer in self._buffers.items():
                    report["structure_details"]["buffers"][name] = {
                        "capacity": buffer.capacity,
                        "size": buffer.size(),
                        "utilization": buffer.size() / buffer.capacity if buffer.capacity > 0 else 0
                    }
                
                # 压缩存储详情
                report["structure_details"]["compressed_storage"] = {}
                for name, storage in self._compressed_storage.items():
                    report["structure_details"]["compressed_storage"][name] = storage.memory_usage()
                
                # 内存池详情
                report["structure_details"]["memory_pools"] = {}
                for name, pool in self._memory_pools.items():
                    report["structure_details"]["memory_pools"][name] = pool.get_stats()
            
            self.logger.info("内存报告导出完成")
            return report
            
        except Exception as e:
            self.logger.error(f"导出内存报告失败: {e}")
            return {"error": "Failed to export memory report"}
    
    def _generate_optimization_recommendations(self) -> List[str]:
        """生成优化建议"""
        try:
            recommendations = []
            stats = self.get_memory_statistics()
            
            if isinstance(stats, dict) and "error" not in stats:
                memory_usage = stats.get("memory_usage_mb", {})
                total_memory = memory_usage.get("total", 0)
                
                # 基于内存使用的建议
                if total_memory > 1000:  # 超过1GB
                    recommendations.append("内存使用量较高，建议启用压缩存储或增加内存池使用")
                
                # 基于结构数量的建议
                current_structures = stats.get("current_structures", {})
                total_structures = sum(current_structures.values())
                
                if total_structures > 100:
                    recommendations.append("数据结构数量较多，建议定期清理未使用的结构")
                
                # 基于系统内存的建议
                system_memory = stats.get("system_memory", {})
                usage_percent = system_memory.get("memory_usage_percent", 0)
                
                if usage_percent > 80:
                    recommendations.append("系统内存使用率较高，建议优化内存使用或增加系统内存")
            
            if not recommendations:
                recommendations.append("内存使用状况良好，无需特殊优化")
            
            return recommendations
            
        except Exception as e:
            self.logger.error(f"生成优化建议失败: {e}")
            return ["无法生成优化建议"]


# 全局内存高效数据结构管理器实例
_global_memory_structures: Optional[MemoryEfficientDataStructures] = None


def get_global_memory_structures() -> MemoryEfficientDataStructures:
    """获取全局内存高效数据结构管理器实例"""
    global _global_memory_structures
    
    if _global_memory_structures is None:
        _global_memory_structures = MemoryEfficientDataStructures()
    
    return _global_memory_structures


# 模块级别的日志器
module_logger = get_logger(__name__)