package com.binance.connector.futures.client.utils;

import okhttp3.OkHttpClient;
import java.util.concurrent.TimeUnit;

/**
 * OkHttpUtils类，用于提供OkHttpClient的实例
 * 支持自定义超时设置
 */
public final class OkHttpUtils {

    private static final int DEFAULT_CONNECT_TIMEOUT = 30000; // 30秒
    private static final int DEFAULT_READ_TIMEOUT = 60000;    // 60秒
    private static final int DEFAULT_WRITE_TIMEOUT = 60000;   // 60秒

    private OkHttpUtils() {
        // 私有构造函数，防止实例化
    }

    /**
     * 获取默认配置的OkHttpClient实例
     *
     * @return OkHttpClient实例
     */
    public static OkHttpClient getOkHttpClient() {
        // 从系统属性中读取超时设置，如果不存在则使用默认值
        int connectTimeout = Integer.getInteger("http.connection.timeout", DEFAULT_CONNECT_TIMEOUT);
        int readTimeout = Integer.getInteger("http.read.timeout", DEFAULT_READ_TIMEOUT);
        int writeTimeout = Integer.getInteger("http.write.timeout", DEFAULT_WRITE_TIMEOUT);

        System.out.println("创建OkHttpClient: connectTimeout=" + connectTimeout + 
                           "ms, readTimeout=" + readTimeout + 
                           "ms, writeTimeout=" + writeTimeout + "ms");

        return new OkHttpClient.Builder()
                .connectTimeout(connectTimeout, TimeUnit.MILLISECONDS)
                .readTimeout(readTimeout, TimeUnit.MILLISECONDS)
                .writeTimeout(writeTimeout, TimeUnit.MILLISECONDS)
                .retryOnConnectionFailure(true)
                .build();
    }
}