#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
优化的多时间框架同步器

针对crypto_ml_strategy项目第3阶段优化的多时间框架数据同步组件。
实现高效的1m、5m、15m、1h、4h、1d六个时间框架数据同步机制，
确保时间序列对齐和数据一致性，兼容Java模块API。

主要优化：
1. 内存高效的数据结构
2. 快速时间对齐算法
3. 智能缓存策略
4. 异步处理支持
5. 性能监控和指标
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any, Union, Callable
from datetime import datetime, timedelta
import threading
import asyncio
from collections import defaultdict, deque
from dataclasses import dataclass, field
import time
from enum import Enum
import weakref
from concurrent.futures import ThreadPoolExecutor
import psutil
import gc


class TimeframeType(Enum):
    """时间框架类型枚举"""
    MINUTE_1 = "1m"
    MINUTE_5 = "5m"
    MINUTE_15 = "15m"
    HOUR_1 = "1h"
    HOUR_4 = "4h"
    DAY_1 = "1d"


class SyncStrategy(Enum):
    """同步策略枚举"""
    NEAREST_NEIGHBOR = "nearest"
    LINEAR_INTERPOLATION = "linear"
    FORWARD_FILL = "ffill"
    BACKWARD_FILL = "bfill"
    CUBIC_SPLINE = "cubic"


@dataclass
class TimeframeSpec:
    """时间框架规格"""
    name: str
    minutes: int
    priority: int  # 1=最高优先级
    weight: float  # 融合权重
    max_cache_size: int
    enabled: bool = True
    
    def __post_init__(self):
        """验证参数"""
        if self.minutes <= 0:
            raise ValueError("minutes must be positive")
        if not 0 <= self.weight <= 1:
            raise ValueError("weight must be between 0 and 1")
        if self.max_cache_size <= 0:
            raise ValueError("max_cache_size must be positive")


@dataclass
class SyncConfig:
    """同步配置"""
    strategy: SyncStrategy = SyncStrategy.NEAREST_NEIGHBOR
    tolerance_ms: int = 5000  # 时间容差毫秒
    max_gap_ms: int = 300000  # 最大间隙毫秒（5分钟）
    min_overlap_ratio: float = 0.7  # 最小重叠比例
    enable_async: bool = True
    max_workers: int = 4
    memory_limit_mb: int = 512
    cache_ttl_seconds: int = 3600  # 缓存生存时间


@dataclass
class PerformanceMetrics:
    """性能指标"""
    sync_latency_ms: float = 0.0
    memory_usage_mb: float = 0.0
    cache_hit_ratio: float = 0.0
    data_quality_score: float = 0.0
    throughput_records_per_sec: float = 0.0
    last_update: datetime = field(default_factory=datetime.now)


class OptimizedTimeframeSync:
    """优化的多时间框架同步器"""
    
    def __init__(self, config: Optional[Dict] = None):
        """
        初始化优化的时间框架同步器
        
        Args:
            config: 配置字典
        """
        self.logger = logging.getLogger('cryptoMlStrategy.data.optimized_sync')
        
        # 默认时间框架配置
        self.timeframe_specs = {
            TimeframeType.MINUTE_1: TimeframeSpec("1m", 1, 1, 0.1, 1440),
            TimeframeType.MINUTE_5: TimeframeSpec("5m", 5, 2, 0.2, 576),
            TimeframeType.MINUTE_15: TimeframeSpec("15m", 15, 3, 0.3, 384),
            TimeframeType.HOUR_1: TimeframeSpec("1h", 60, 4, 0.4, 168),
            TimeframeType.HOUR_4: TimeframeSpec("4h", 240, 5, 0.0, 42),
            TimeframeType.DAY_1: TimeframeSpec("1d", 1440, 6, 0.0, 30)
        }
        
        # 同步配置
        self.sync_config = SyncConfig()
        if config:
            self._update_config(config)
        
        # 启用的时间框架
        self.enabled_timeframes = {
            tf_type: spec for tf_type, spec in self.timeframe_specs.items()
            if spec.enabled and spec.weight > 0
        }
        
        # 高效数据存储：使用弱引用避免内存泄漏
        self._data_store = defaultdict(lambda: defaultdict(lambda: deque()))
        self._sync_cache = weakref.WeakValueDictionary()
        
        # 性能监控
        self.metrics = PerformanceMetrics()
        self._stats = {
            'total_syncs': 0,
            'successful_syncs': 0,
            'cache_hits': 0,
            'memory_cleanups': 0,
            'start_time': time.time()
        }
        
        # 线程池和锁
        self._executor = ThreadPoolExecutor(max_workers=self.sync_config.max_workers)
        self._lock = threading.RLock()
        
        # 内存监控
        self._memory_monitor_active = True
        self._start_memory_monitor()
        
        self.logger.info(f"优化时间框架同步器初始化完成，启用框架: {list(self.enabled_timeframes.keys())}")
    
    def _update_config(self, config: Dict):
        """更新配置"""
        if 'sync' in config:
            sync_cfg = config['sync']
            self.sync_config.strategy = SyncStrategy(sync_cfg.get('strategy', 'nearest'))
            self.sync_config.tolerance_ms = sync_cfg.get('tolerance_ms', 5000)
            self.sync_config.max_gap_ms = sync_cfg.get('max_gap_ms', 300000)
            self.sync_config.min_overlap_ratio = sync_cfg.get('min_overlap_ratio', 0.7)
            self.sync_config.enable_async = sync_cfg.get('enable_async', True)
            self.sync_config.max_workers = sync_cfg.get('max_workers', 4)
            self.sync_config.memory_limit_mb = sync_cfg.get('memory_limit_mb', 512)
            self.sync_config.cache_ttl_seconds = sync_cfg.get('cache_ttl_seconds', 3600)
        
        if 'timeframes' in config:
            for tf_name, tf_config in config['timeframes'].items():
                tf_type = TimeframeType(tf_name)
                if tf_type in self.timeframe_specs:
                    spec = self.timeframe_specs[tf_type]
                    spec.weight = tf_config.get('weight', spec.weight)
                    spec.enabled = tf_config.get('enabled', spec.enabled)
                    spec.max_cache_size = tf_config.get('max_cache_size', spec.max_cache_size)
    
    def _start_memory_monitor(self):
        """启动内存监控线程"""
        def monitor():
            while self._memory_monitor_active:
                try:
                    # 检查内存使用
                    process = psutil.Process()
                    memory_mb = process.memory_info().rss / 1024 / 1024
                    self.metrics.memory_usage_mb = memory_mb
                    
                    # 如果超过限制，执行清理
                    if memory_mb > self.sync_config.memory_limit_mb:
                        self._cleanup_memory()
                    
                    time.sleep(30)  # 每30秒检查一次
                except Exception as e:
                    self.logger.error(f"内存监控错误: {e}")
                    time.sleep(60)
        
        monitor_thread = threading.Thread(target=monitor, daemon=True)
        monitor_thread.start()
    
    def _cleanup_memory(self):
        """清理内存"""
        try:
            with self._lock:
                # 清理过期缓存
                current_time = time.time()
                expired_keys = []
                
                for key, (data, timestamp) in list(self._sync_cache.items()):
                    if current_time - timestamp > self.sync_config.cache_ttl_seconds:
                        expired_keys.append(key)
                
                for key in expired_keys:
                    if key in self._sync_cache:
                        del self._sync_cache[key]
                
                # 限制数据存储大小
                for symbol_data in self._data_store.values():
                    for tf_type, data_deque in symbol_data.items():
                        if tf_type in self.timeframe_specs:
                            max_size = self.timeframe_specs[tf_type].max_cache_size
                            while len(data_deque) > max_size:
                                data_deque.popleft()
                
                # 强制垃圾回收
                gc.collect()
                self._stats['memory_cleanups'] += 1
                
                self.logger.info(f"内存清理完成，清理了 {len(expired_keys)} 个过期缓存项")
                
        except Exception as e:
            self.logger.error(f"内存清理失败: {e}")
    
    def add_data_point(self, symbol: str, timeframe: str, data: Dict[str, Any]) -> bool:
        """添加数据点到指定时间框架"""
        from .optimized_sync_core import OptimizedSyncCore
        return OptimizedSyncCore.add_data_point(self, symbol, timeframe, data)
    
    def sync_timeframes(self, symbol: str, target_timeframe: Optional[str] = None) -> Optional[pd.DataFrame]:
        """同步多时间框架数据"""
        from .optimized_sync_core import OptimizedSyncCore
        return OptimizedSyncCore.sync_timeframes(self, symbol, target_timeframe)
    
    def fuse_features(self, synchronized_df: pd.DataFrame, 
                     feature_columns: Optional[List[str]] = None) -> pd.DataFrame:
        """融合多时间框架特征"""
        from .optimized_sync_features import OptimizedSyncFeatures
        return OptimizedSyncFeatures.fuse_features(self, synchronized_df, feature_columns)
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """获取性能指标"""
        from .optimized_sync_features import OptimizedSyncFeatures
        return OptimizedSyncFeatures.get_performance_metrics(self)
    
    def validate_sync_quality(self, synchronized_df: pd.DataFrame, symbol: str) -> Dict[str, Any]:
        """验证同步质量"""
        from .optimized_sync_features import OptimizedSyncFeatures
        return OptimizedSyncFeatures.validate_sync_quality(self, synchronized_df, symbol)
    
    def export_sync_data(self, symbol: str, timeframe: Optional[str] = None,
                        format: str = 'csv') -> Optional[str]:
        """导出同步数据"""
        from .optimized_sync_features import OptimizedSyncFeatures
        return OptimizedSyncFeatures.export_sync_data(self, symbol, timeframe, format)
    
    def cleanup_resources(self):
        """清理资源"""
        from .optimized_sync_features import OptimizedSyncFeatures
        OptimizedSyncFeatures.cleanup_resources(self)
    
    def __del__(self):
        """析构函数"""
        try:
            self.cleanup_resources()
        except Exception:
            pass