#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据流集成测试套件

测试Java API到Python ML模块的完整数据管道，验证多时间周期数据同步、
数据质量检查和缓存性能。
"""

import asyncio
import sys
import time
from pathlib import Path
from typing import Dict, Any, List
from datetime import datetime, timedelta
from loguru import logger

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from tests.integration.utils.test_orchestrator import TestCase, TestSuite, TestPriority
from tests.integration.fixtures.mock_services import get_mock_manager
from tests.integration.config.integration_test_config import get_test_config

# 导入需要测试的模块
try:
    from src.data.api_client import APIClient
    from src.data.cache_manager import CacheManager
    from src.data.data_access_service import DataAccessService
    from src.data.data_quality_checker import DataQualityChecker
    from src.data.multi_timeframe_manager import MultiTimeframeManager
    from src.data.realtime_processor import RealtimeProcessor
    from src.kafka_client import KafkaClient
    from src.data_processor import DataProcessor
except ImportError as e:
    logger.warning(f"无法导入某些模块，将使用Mock实现: {e}")


async def test_api_client_integration() -> Dict[str, Any]:
    """测试API客户端集成"""
    logger.info("执行API客户端集成测试...")
    
    start_time = time.time()
    test_config = get_test_config()
    mock_manager = get_mock_manager()
    java_api = mock_manager.get_java_api()
    
    try:
        # 测试市场数据获取
        response = await java_api.request("GET", "/market/data", {
            "symbol": "BTCUSDT",
            "timeframe": "1m",
            "limit": 100
        })
        
        assert response.status_code == 200, f"API响应状态码错误: {response.status_code}"
        assert response.data is not None, "API响应数据为空"
        
        # 测试历史数据获取
        history_response = await java_api.request("GET", "/market/history", {
            "symbol": "BTCUSDT",
            "timeframe": "1h",
            "start_time": int((datetime.now() - timedelta(days=1)).timestamp() * 1000),
            "end_time": int(datetime.now().timestamp() * 1000)
        })
        
        assert history_response.status_code == 200, "历史数据API调用失败"
        assert isinstance(history_response.data, list), "历史数据格式错误"
        
        execution_time = time.time() - start_time
        
        return {
            "execution_time": execution_time,
            "api_calls_made": 2,
            "response_time_ms": execution_time * 1000,
            "data_points_received": len(history_response.data),
            "test_type": "api_client_integration",
            "performance_check": execution_time < 1.0  # 1秒内完成
        }
        
    except Exception as e:
        logger.error(f"API客户端集成测试失败: {str(e)}")
        raise


async def test_kafka_data_flow() -> Dict[str, Any]:
    """测试Kafka数据流"""
    logger.info("执行Kafka数据流测试...")
    
    start_time = time.time()
    mock_manager = get_mock_manager()
    kafka_service = mock_manager.get_kafka_service()
    
    # 测试数据
    test_messages = [
        {
            "symbol": "BTCUSDT",
            "timeframe": "1m",
            "timestamp": int(time.time() * 1000),
            "open": 45000.0,
            "high": 45100.0,
            "low": 44900.0,
            "close": 45050.0,
            "volume": 1000.0
        },
        {
            "symbol": "ETHUSDT", 
            "timeframe": "1m",
            "timestamp": int(time.time() * 1000),
            "open": 3000.0,
            "high": 3010.0,
            "low": 2990.0,
            "close": 3005.0,
            "volume": 5000.0
        }
    ]
    
    try:
        # 测试消息生产
        for message in test_messages:
            await kafka_service.produce("market_data", message, key=message["symbol"])
        
        # 验证消息消费
        received_messages = kafka_service.get_messages("market_data")
        
        assert len(received_messages) >= len(test_messages), "消息数量不匹配"
        
        # 验证消息内容
        for i, received_msg in enumerate(received_messages[-len(test_messages):]):
            original_msg = test_messages[i]
            assert received_msg["value"]["symbol"] == original_msg["symbol"], "消息内容不匹配"
            assert received_msg["value"]["timeframe"] == original_msg["timeframe"], "时间框架不匹配"
        
        execution_time = time.time() - start_time
        
        return {
            "execution_time": execution_time,
            "messages_sent": len(test_messages),
            "messages_received": len(received_messages),
            "message_latency_ms": execution_time * 1000 / len(test_messages),
            "test_type": "kafka_data_flow",
            "throughput_msg_per_sec": len(test_messages) / execution_time if execution_time > 0 else 0
        }
        
    except Exception as e:
        logger.error(f"Kafka数据流测试失败: {str(e)}")
        raise


async def test_multi_timeframe_synchronization() -> Dict[str, Any]:
    """测试多时间框架数据同步"""
    logger.info("执行多时间框架数据同步测试...")
    
    start_time = time.time()
    test_config = get_test_config()
    timeframes = test_config.test_data.timeframes
    
    # 模拟多时间框架数据
    sync_results = {}
    
    try:
        for timeframe in timeframes:
            # 模拟数据同步过程
            await asyncio.sleep(0.01)  # 模拟处理时间
            
            # 验证时间对齐
            current_time = datetime.now()
            
            # 根据时间框架计算对齐时间
            if timeframe == "1m":
                aligned_time = current_time.replace(second=0, microsecond=0)
            elif timeframe == "5m":
                aligned_time = current_time.replace(minute=(current_time.minute // 5) * 5, second=0, microsecond=0)
            elif timeframe == "15m":
                aligned_time = current_time.replace(minute=(current_time.minute // 15) * 15, second=0, microsecond=0)
            elif timeframe == "1h":
                aligned_time = current_time.replace(minute=0, second=0, microsecond=0)
            elif timeframe == "4h":
                aligned_time = current_time.replace(hour=(current_time.hour // 4) * 4, minute=0, second=0, microsecond=0)
            elif timeframe == "1d":
                aligned_time = current_time.replace(hour=0, minute=0, second=0, microsecond=0)
            else:
                aligned_time = current_time
            
            sync_results[timeframe] = {
                "aligned": True,
                "timestamp": int(aligned_time.timestamp() * 1000),
                "data_points": 100,  # 模拟数据点数
                "sync_latency_ms": 10  # 模拟同步延迟
            }
        
        execution_time = time.time() - start_time
        
        # 验证所有时间框架都成功同步
        all_synced = all(result["aligned"] for result in sync_results.values())
        avg_latency = sum(result["sync_latency_ms"] for result in sync_results.values()) / len(sync_results)
        
        return {
            "execution_time": execution_time,
            "timeframes_tested": len(timeframes),
            "sync_success_rate": 100.0 if all_synced else 0.0,
            "average_sync_latency_ms": avg_latency,
            "max_sync_latency_ms": max(result["sync_latency_ms"] for result in sync_results.values()),
            "sync_results": sync_results,
            "test_type": "multi_timeframe_sync",
            "performance_check": avg_latency < 50  # 50ms内完成同步
        }
        
    except Exception as e:
        logger.error(f"多时间框架同步测试失败: {str(e)}")
        raise


async def test_data_quality_validation() -> Dict[str, Any]:
    """测试数据质量验证"""
    logger.info("执行数据质量验证测试...")
    
    start_time = time.time()
    
    # 测试数据集（包含正常和异常数据）
    test_data = [
        # 正常数据
        {"timestamp": int(time.time() * 1000), "open": 45000.0, "high": 45100.0, "low": 44900.0, "close": 45050.0, "volume": 1000.0},
        {"timestamp": int(time.time() * 1000) + 60000, "open": 45050.0, "high": 45150.0, "low": 44950.0, "close": 45100.0, "volume": 1200.0},
        
        # 异常数据
        {"timestamp": int(time.time() * 1000) + 120000, "open": 45100.0, "high": 44000.0, "low": 45200.0, "close": 45150.0, "volume": 800.0},  # high < low
        {"timestamp": int(time.time() * 1000) + 180000, "open": -1000.0, "high": 45300.0, "low": 45000.0, "close": 45200.0, "volume": 900.0},  # 负价格
        {"timestamp": int(time.time() * 1000) + 240000, "open": 45200.0, "high": 45400.0, "low": 45100.0, "close": 45300.0, "volume": 0.0},  # 零成交量
    ]
    
    try:
        quality_results = {
            "total_records": len(test_data),
            "valid_records": 0,
            "invalid_records": 0,
            "validation_errors": [],
            "data_completeness": 0.0,
            "data_accuracy": 0.0
        }
        
        # 模拟数据质量检查
        for i, record in enumerate(test_data):
            is_valid = True
            errors = []
            
            # 检查OHLC逻辑
            if record["high"] < record["low"]:
                is_valid = False
                errors.append("high_less_than_low")
            
            if record["open"] < 0 or record["close"] < 0:
                is_valid = False
                errors.append("negative_price")
            
            if record["volume"] <= 0:
                is_valid = False
                errors.append("invalid_volume")
            
            if record["open"] > record["high"] or record["open"] < record["low"]:
                is_valid = False
                errors.append("open_out_of_range")
            
            if record["close"] > record["high"] or record["close"] < record["low"]:
                is_valid = False
                errors.append("close_out_of_range")
            
            if is_valid:
                quality_results["valid_records"] += 1
            else:
                quality_results["invalid_records"] += 1
                quality_results["validation_errors"].extend(errors)
        
        # 计算质量指标
        quality_results["data_completeness"] = (quality_results["valid_records"] / quality_results["total_records"]) * 100
        quality_results["data_accuracy"] = quality_results["data_completeness"]  # 简化计算
        
        execution_time = time.time() - start_time
        
        return {
            "execution_time": execution_time,
            "records_processed": len(test_data),
            "validation_rate_per_sec": len(test_data) / execution_time if execution_time > 0 else 0,
            "data_quality_score": quality_results["data_completeness"],
            "error_detection_rate": (quality_results["invalid_records"] / quality_results["total_records"]) * 100,
            "quality_results": quality_results,
            "test_type": "data_quality_validation",
            "performance_check": execution_time < 0.5  # 0.5秒内完成验证
        }
        
    except Exception as e:
        logger.error(f"数据质量验证测试失败: {str(e)}")
        raise


async def test_cache_performance() -> Dict[str, Any]:
    """测试缓存性能"""
    logger.info("执行缓存性能测试...")
    
    start_time = time.time()
    
    # 模拟缓存操作
    cache_operations = {
        "cache_hits": 0,
        "cache_misses": 0,
        "cache_writes": 0,
        "total_operations": 0
    }
    
    try:
        # 模拟缓存测试场景
        test_keys = [f"BTCUSDT_1m_{i}" for i in range(100)]
        cache_data = {}
        
        # 写入缓存测试
        write_start = time.time()
        for key in test_keys:
            cache_data[key] = {
                "timestamp": int(time.time() * 1000),
                "price": 45000.0 + (hash(key) % 1000),
                "volume": 1000.0
            }
            cache_operations["cache_writes"] += 1
            cache_operations["total_operations"] += 1
        write_time = time.time() - write_start
        
        # 读取缓存测试（命中）
        read_hit_start = time.time()
        for key in test_keys[:80]:  # 80%命中率
            if key in cache_data:
                cache_operations["cache_hits"] += 1
            cache_operations["total_operations"] += 1
        read_hit_time = time.time() - read_hit_start
        
        # 读取缓存测试（未命中）
        read_miss_start = time.time()
        for i in range(20):  # 20个未命中
            key = f"ETHUSDT_1m_{i}"
            if key not in cache_data:
                cache_operations["cache_misses"] += 1
            cache_operations["total_operations"] += 1
        read_miss_time = time.time() - read_miss_start
        
        # 计算性能指标
        cache_hit_rate = (cache_operations["cache_hits"] / (cache_operations["cache_hits"] + cache_operations["cache_misses"])) * 100
        avg_write_time = (write_time / cache_operations["cache_writes"]) * 1000  # ms
        avg_read_time = ((read_hit_time + read_miss_time) / (cache_operations["cache_hits"] + cache_operations["cache_misses"])) * 1000  # ms
        
        execution_time = time.time() - start_time
        
        return {
            "execution_time": execution_time,
            "cache_hit_rate": cache_hit_rate,
            "cache_operations": cache_operations,
            "avg_write_time_ms": avg_write_time,
            "avg_read_time_ms": avg_read_time,
            "operations_per_second": cache_operations["total_operations"] / execution_time if execution_time > 0 else 0,
            "test_type": "cache_performance",
            "performance_check": cache_hit_rate >= 80.0 and avg_read_time < 1.0  # 80%命中率，1ms内读取
        }
        
    except Exception as e:
        logger.error(f"缓存性能测试失败: {str(e)}")
        raise


async def test_end_to_end_data_pipeline() -> Dict[str, Any]:
    """测试端到端数据管道"""
    logger.info("执行端到端数据管道测试...")
    
    start_time = time.time()
    mock_manager = get_mock_manager()
    
    pipeline_metrics = {
        "stages_completed": 0,
        "total_stages": 6,
        "stage_timings": {},
        "data_throughput": 0,
        "error_count": 0
    }
    
    try:
        # 阶段1: API数据获取
        stage_start = time.time()
        java_api = mock_manager.get_java_api()
        api_response = await java_api.request("GET", "/market/data", {"symbol": "BTCUSDT"})
        pipeline_metrics["stage_timings"]["api_fetch"] = (time.time() - stage_start) * 1000
        pipeline_metrics["stages_completed"] += 1
        
        # 阶段2: 数据验证
        stage_start = time.time()
        if api_response.status_code == 200 and api_response.data:
            # 模拟数据验证
            await asyncio.sleep(0.01)
        pipeline_metrics["stage_timings"]["data_validation"] = (time.time() - stage_start) * 1000
        pipeline_metrics["stages_completed"] += 1
        
        # 阶段3: Kafka消息发送
        stage_start = time.time()
        kafka_service = mock_manager.get_kafka_service()
        await kafka_service.produce("market_data", api_response.data, key="BTCUSDT")
        pipeline_metrics["stage_timings"]["kafka_produce"] = (time.time() - stage_start) * 1000
        pipeline_metrics["stages_completed"] += 1
        
        # 阶段4: 数据处理
        stage_start = time.time()
        # 模拟数据处理（特征提取、清洗等）
        await asyncio.sleep(0.02)
        pipeline_metrics["stage_timings"]["data_processing"] = (time.time() - stage_start) * 1000
        pipeline_metrics["stages_completed"] += 1
        
        # 阶段5: 缓存更新
        stage_start = time.time()
        # 模拟缓存更新
        await asyncio.sleep(0.005)
        pipeline_metrics["stage_timings"]["cache_update"] = (time.time() - stage_start) * 1000
        pipeline_metrics["stages_completed"] += 1
        
        # 阶段6: 结果输出
        stage_start = time.time()
        # 模拟结果输出到下游系统
        await asyncio.sleep(0.01)
        pipeline_metrics["stage_timings"]["result_output"] = (time.time() - stage_start) * 1000
        pipeline_metrics["stages_completed"] += 1
        
        execution_time = time.time() - start_time
        total_pipeline_latency = sum(pipeline_metrics["stage_timings"].values())
        
        # 计算吞吐量（假设处理了1000个数据点）
        pipeline_metrics["data_throughput"] = 1000 / execution_time if execution_time > 0 else 0
        
        return {
            "execution_time": execution_time,
            "total_pipeline_latency_ms": total_pipeline_latency,
            "pipeline_completion_rate": (pipeline_metrics["stages_completed"] / pipeline_metrics["total_stages"]) * 100,
            "data_throughput_per_sec": pipeline_metrics["data_throughput"],
            "stage_timings": pipeline_metrics["stage_timings"],
            "bottleneck_stage": max(pipeline_metrics["stage_timings"], key=pipeline_metrics["stage_timings"].get),
            "test_type": "end_to_end_pipeline",
            "performance_check": total_pipeline_latency < 100.0  # 100ms内完成整个管道
        }
        
    except Exception as e:
        logger.error(f"端到端数据管道测试失败: {str(e)}")
        pipeline_metrics["error_count"] += 1
        raise


async def test_data_consistency_validation() -> Dict[str, Any]:
    """测试数据一致性验证"""
    logger.info("执行数据一致性验证测试...")
    
    start_time = time.time()
    mock_manager = get_mock_manager()
    
    consistency_results = {
        "cross_timeframe_consistency": True,
        "data_integrity_score": 0.0,
        "timestamp_alignment_errors": 0,
        "price_consistency_errors": 0,
        "volume_consistency_errors": 0
    }
    
    try:
        # 模拟多时间框架数据一致性检查
        timeframes = ["1m", "5m", "15m", "1h"]
        base_data = {
            "1m": [
                {"timestamp": 1640995200000, "open": 45000, "high": 45100, "low": 44900, "close": 45050, "volume": 1000},
                {"timestamp": 1640995260000, "open": 45050, "high": 45150, "low": 44950, "close": 45100, "volume": 1200},
                {"timestamp": 1640995320000, "open": 45100, "high": 45200, "low": 45000, "close": 45150, "volume": 1100},
                {"timestamp": 1640995380000, "open": 45150, "high": 45250, "low": 45050, "close": 45200, "volume": 1300},
                {"timestamp": 1640995440000, "open": 45200, "high": 45300, "low": 45100, "close": 45250, "volume": 1400},
            ]
        }
        
        # 验证时间戳对齐
        for timeframe in timeframes:
            if timeframe == "1m":
                continue
                
            # 模拟时间框架聚合验证
            if timeframe == "5m":
                # 5分钟应该包含5个1分钟数据点
                expected_volume = sum(d["volume"] for d in base_data["1m"])
                expected_high = max(d["high"] for d in base_data["1m"])
                expected_low = min(d["low"] for d in base_data["1m"])
                
                # 验证聚合数据一致性
                if expected_volume != 6000:  # 1000+1200+1100+1300+1400
                    consistency_results["volume_consistency_errors"] += 1
                
                if expected_high != 45300:
                    consistency_results["price_consistency_errors"] += 1
                
                if expected_low != 44900:
                    consistency_results["price_consistency_errors"] += 1
        
        # 计算一致性分数
        total_checks = 10  # 假设进行了10项检查
        error_count = (consistency_results["timestamp_alignment_errors"] + 
                      consistency_results["price_consistency_errors"] + 
                      consistency_results["volume_consistency_errors"])
        
        consistency_results["data_integrity_score"] = ((total_checks - error_count) / total_checks) * 100
        consistency_results["cross_timeframe_consistency"] = error_count == 0
        
        execution_time = time.time() - start_time
        
        return {
            "execution_time": execution_time,
            "consistency_score": consistency_results["data_integrity_score"],
            "timeframes_validated": len(timeframes),
            "consistency_results": consistency_results,
            "test_type": "data_consistency_validation",
            "performance_check": consistency_results["data_integrity_score"] >= 95.0
        }
        
    except Exception as e:
        logger.error(f"数据一致性验证测试失败: {str(e)}")
        raise


def create_data_flow_integration_test_suite() -> TestSuite:
    """
    创建数据流集成测试套件
    
    Returns:
        TestSuite: 数据流集成测试套件
    """
    
    test_cases = [
        TestCase(
            test_id="data_flow.api_client_integration",
            name="API客户端集成测试",
            description="测试与Java API的连接和数据获取功能",
            test_function=test_api_client_integration,
            priority=TestPriority.HIGH,
            timeout=60,
            tags=["api", "integration", "data_flow"]
        ),
        
        TestCase(
            test_id="data_flow.kafka_data_flow",
            name="Kafka数据流测试",
            description="测试Kafka消息的生产、传递和消费",
            test_function=test_kafka_data_flow,
            dependencies=["data_flow.api_client_integration"],
            priority=TestPriority.HIGH,
            timeout=45,
            tags=["kafka", "messaging", "data_flow"]
        ),
        
        TestCase(
            test_id="data_flow.multi_timeframe_sync",
            name="多时间框架同步测试",
            description="测试多时间框架数据的同步和对齐",
            test_function=test_multi_timeframe_synchronization,
            dependencies=["data_flow.kafka_data_flow"],
            priority=TestPriority.HIGH,
            timeout=90,
            tags=["timeframe", "synchronization", "data_flow"]
        ),
        
        TestCase(
            test_id="data_flow.data_quality_validation",
            name="数据质量验证测试",
            description="测试数据质量检查和异常数据处理",
            test_function=test_data_quality_validation,
            dependencies=["data_flow.api_client_integration"],
            priority=TestPriority.NORMAL,
            timeout=60,
            tags=["quality", "validation", "data_flow"]
        ),
        
        TestCase(
            test_id="data_flow.cache_performance",
            name="缓存性能测试",
            description="测试缓存系统的性能和命中率",
            test_function=test_cache_performance,
            dependencies=["data_flow.api_client_integration"],
            priority=TestPriority.NORMAL,
            timeout=45,
            tags=["cache", "performance", "data_flow"]
        ),
        
        TestCase(
            test_id="data_flow.data_consistency",
            name="数据一致性验证测试",
            description="验证跨时间框架的数据一致性",
            test_function=test_data_consistency_validation,
            dependencies=["data_flow.multi_timeframe_sync"],
            priority=TestPriority.HIGH,
            timeout=75,
            tags=["consistency", "validation", "data_flow"]
        ),
        
        TestCase(
            test_id="data_flow.end_to_end_pipeline",
            name="端到端数据管道测试",
            description="测试完整的数据处理管道性能",
            test_function=test_end_to_end_data_pipeline,
            dependencies=[
                "data_flow.api_client_integration",
                "data_flow.kafka_data_flow",
                "data_flow.data_quality_validation",
                "data_flow.cache_performance"
            ],
            priority=TestPriority.CRITICAL,
            timeout=120,
            tags=["pipeline", "end_to_end", "performance", "data_flow"]
        )
    ]
    
    return TestSuite(
        suite_id="data_flow_integration_suite",
        name="数据流集成测试套件",
        description="测试Java API到Python ML模块的完整数据管道，验证多时间周期数据同步、数据质量检查和缓存性能",
        test_cases=test_cases,
        parallel_execution=True,
        max_workers=3
    )


if __name__ == "__main__":
    # 示例：运行数据流集成测试
    import asyncio
    from ..utils.test_orchestrator import create_test_orchestrator
    from ..reports.test_reporter import create_test_reporter
    from ..fixtures.mock_services import setup_mock_environment, teardown_mock_environment
    
    async def run_data_flow_tests():
        """运行数据流集成测试"""
        await setup_mock_environment()
        
        try:
            orchestrator = create_test_orchestrator(max_workers=3)
            test_suite = create_data_flow_integration_test_suite()
            orchestrator.register_test_suite(test_suite)
            
            results = await orchestrator.execute_all_tests()
            
            reporter = create_test_reporter()
            summary = orchestrator.get_execution_summary()
            
            report_files = reporter.generate_all_reports(
                test_results=results,
                execution_summary=summary,
                report_name="data_flow_integration_report"
            )
            
            logger.info(f"数据流集成测试完成，报告已生成: {report_files}")
            
        finally:
            await teardown_mock_environment()
    
    asyncio.run(run_data_flow_tests())