package com.crypto.trading.common.constant;

/**
 * 订单相关枚举
 * <p>
 * 定义订单相关的各种枚举类型
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class OrderEnums {

    /**
     * 订单类型枚举
     */
    public enum OrderType {
        /**
         * 市价单
         */
        MARKET("MARKET", "市价单"),

        /**
         * 限价单
         */
        LIMIT("LIMIT", "限价单"),
        
        /**
         * 止损单
         */
        STOP("STOP", "止损单"),

        /**
         * 止损限价单
         */
        STOP_LOSS_LIMIT("STOP_LOSS_LIMIT", "止损限价单"),

        /**
         * 止盈限价单
         */
        TAKE_PROFIT_LIMIT("TAKE_PROFIT_LIMIT", "止盈限价单"),

        /**
         * 限价止损单
         */
        LIMIT_MAKER("LIMIT_MAKER", "限价只挂单");

        private final String code;
        private final String desc;

        OrderType(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

        /**
         * 根据code获取枚举
         *
         * @param code 编码
         * @return 枚举
         */
        public static OrderType getByCode(String code) {
            for (OrderType type : values()) {
                if (type.getCode().equals(code)) {
                    return type;
                }
            }
            return null;
        }
    }

    /**
     * 订单方向枚举
     */
    public enum OrderSide {
        /**
         * 买入
         */
        BUY("BUY", "买入"),

        /**
         * 卖出
         */
        SELL("SELL", "卖出");

        private final String code;
        private final String desc;

        OrderSide(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

        /**
         * 根据code获取枚举
         *
         * @param code 编码
         * @return 枚举
         */
        public static OrderSide getByCode(String code) {
            for (OrderSide side : values()) {
                if (side.getCode().equals(code)) {
                    return side;
                }
            }
            return null;
        }
    }

    /**
     * 持仓方向枚举
     */
    public enum PositionSide {
        /**
         * 做多
         */
        LONG("LONG", "做多"),

        /**
         * 做空
         */
        SHORT("SHORT", "做空"),

        /**
         * 双向
         */
        BOTH("BOTH", "双向");

        private final String code;
        private final String desc;

        PositionSide(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

        /**
         * 根据code获取枚举
         *
         * @param code 编码
         * @return 枚举
         */
        public static PositionSide getByCode(String code) {
            for (PositionSide side : values()) {
                if (side.getCode().equals(code)) {
                    return side;
                }
            }
            return null;
        }
    }

    /**
     * 时效类型枚举
     */
    public enum TimeInForce {
        /**
         * 成交为止，订单会一直有效，直到被成交或者取消
         */
        GTC("GTC", "成交为止"),

        /**
         * 立即成交并取消剩余
         */
        IOC("IOC", "立即成交并取消剩余"),

        /**
         * 全部立即成交否则取消
         */
        FOK("FOK", "全部立即成交否则取消");

        private final String code;
        private final String desc;

        TimeInForce(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

        /**
         * 根据code获取枚举
         *
         * @param code 编码
         * @return 枚举
         */
        public static TimeInForce getByCode(String code) {
            for (TimeInForce type : values()) {
                if (type.getCode().equals(code)) {
                    return type;
                }
            }
            return null;
        }
    }

    /**
     * 订单状态枚举
     */
    public enum OrderStatus {
        /**
         * 新建订单
         */
        NEW("NEW", "新建订单"),

        /**
         * 部分成交
         */
        PARTIALLY_FILLED("PARTIALLY_FILLED", "部分成交"),

        /**
         * 完全成交
         */
        FILLED("FILLED", "完全成交"),

        /**
         * 已撤销
         */
        CANCELED("CANCELED", "已撤销"),

        /**
         * 订单被拒绝
         */
        REJECTED("REJECTED", "订单被拒绝"),

        /**
         * 订单过期
         */
        EXPIRED("EXPIRED", "订单过期");

        private final String code;
        private final String desc;

        OrderStatus(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

        /**
         * 根据code获取枚举
         *
         * @param code 编码
         * @return 枚举
         */
        public static OrderStatus getByCode(String code) {
            for (OrderStatus status : values()) {
                if (status.getCode().equals(code)) {
                    return status;
                }
            }
            return null;
        }
    }
}