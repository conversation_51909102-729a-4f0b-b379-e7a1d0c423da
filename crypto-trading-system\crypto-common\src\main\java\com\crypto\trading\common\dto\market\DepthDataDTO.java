package com.crypto.trading.common.dto.market;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 深度数据传输对象
 * <p>
 * 用于在系统各层之间传递订单簿深度数据
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class DepthDataDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 交易对（例如：BTCUSDT）
     */
    private String symbol;

    /**
     * 最后更新ID
     */
    private Long lastUpdateId;

    /**
     * 深度级别
     * <p>
     * 可能的值: 5, 10, 20, 50, 100, 500, 1000
     * </p>
     */
    private Integer limit;

    /**
     * 买单列表
     * <p>
     * 每个元素是一个长度为2的数组，第一个元素是价格，第二个元素是数量
     * </p>
     */
    private List<PriceQuantity> bids;

    /**
     * 卖单列表
     * <p>
     * 每个元素是一个长度为2的数组，第一个元素是价格，第二个元素是数量
     * </p>
     */
    private List<PriceQuantity> asks;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 构造函数
     */
    public DepthDataDTO() {
    }

    /**
     * 构造函数
     *
     * @param symbol       交易对
     * @param lastUpdateId 最后更新ID
     * @param limit        深度级别
     * @param bids         买单列表
     * @param asks         卖单列表
     * @param updateTime   更新时间
     */
    public DepthDataDTO(String symbol, Long lastUpdateId, Integer limit,
                        List<PriceQuantity> bids, List<PriceQuantity> asks,
                        LocalDateTime updateTime) {
        this.symbol = symbol;
        this.lastUpdateId = lastUpdateId;
        this.limit = limit;
        this.bids = bids;
        this.asks = asks;
        this.updateTime = updateTime;
    }

    /**
     * 获取交易对
     *
     * @return 交易对
     */
    public String getSymbol() {
        return symbol;
    }

    /**
     * 设置交易对
     *
     * @param symbol 交易对
     */
    public void setSymbol(String symbol) {
        this.symbol = symbol;
    }

    /**
     * 获取最后更新ID
     *
     * @return 最后更新ID
     */
    public Long getLastUpdateId() {
        return lastUpdateId;
    }

    /**
     * 设置最后更新ID
     *
     * @param lastUpdateId 最后更新ID
     */
    public void setLastUpdateId(Long lastUpdateId) {
        this.lastUpdateId = lastUpdateId;
    }

    /**
     * 获取深度级别
     *
     * @return 深度级别
     */
    public Integer getLimit() {
        return limit;
    }

    /**
     * 设置深度级别
     *
     * @param limit 深度级别
     */
    public void setLimit(Integer limit) {
        this.limit = limit;
    }
    
    /**
     * 设置深度
     * 这是setLimit的别名方法
     *
     * @param depth 深度级别
     */
    public void setDepth(int depth) {
        this.limit = depth;
    }

    /**
     * 获取买单列表
     *
     * @return 买单列表
     */
    public List<PriceQuantity> getBids() {
        return bids;
    }

    /**
     * 设置买单列表
     *
     * @param bids 买单列表
     */
    public void setBids(List<PriceQuantity> bids) {
        this.bids = bids;
    }

    /**
     * 获取卖单列表
     *
     * @return 卖单列表
     */
    public List<PriceQuantity> getAsks() {
        return asks;
    }

    /**
     * 设置卖单列表
     *
     * @param asks 卖单列表
     */
    public void setAsks(List<PriceQuantity> asks) {
        this.asks = asks;
    }

    /**
     * 获取更新时间
     *
     * @return 更新时间
     */
    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "OrderBookDTO{" +
                "symbol='" + symbol + '\'' +
                ", lastUpdateId=" + lastUpdateId +
                ", limit=" + limit +
                ", bids=" + bids +
                ", asks=" + asks +
                ", updateTime=" + updateTime +
                '}';
    }

    /**
     * 价格和数量对
     */
    public static class PriceQuantity implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 价格
         */
        private BigDecimal price;

        /**
         * 数量
         */
        private BigDecimal quantity;

        /**
         * 构造函数
         */
        public PriceQuantity() {
        }

        /**
         * 构造函数
         *
         * @param price    价格
         * @param quantity 数量
         */
        public PriceQuantity(BigDecimal price, BigDecimal quantity) {
            this.price = price;
            this.quantity = quantity;
        }

        /**
         * 获取价格
         *
         * @return 价格
         */
        public BigDecimal getPrice() {
            return price;
        }

        /**
         * 设置价格
         *
         * @param price 价格
         */
        public void setPrice(BigDecimal price) {
            this.price = price;
        }

        /**
         * 获取数量
         *
         * @return 数量
         */
        public BigDecimal getQuantity() {
            return quantity;
        }

        /**
         * 设置数量
         *
         * @param quantity 数量
         */
        public void setQuantity(BigDecimal quantity) {
            this.quantity = quantity;
        }

        @Override
        public String toString() {
            return "[" + price + ", " + quantity + "]";
        }
    }
} 