package com.crypto.trading.trade.service.order.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.crypto.trading.common.constant.OrderEnums;
import com.crypto.trading.common.dto.trade.OrderDTO;
import com.crypto.trading.common.dto.trade.OrderRequestDTO;
import com.crypto.trading.common.dto.trade.OrderResponseDTO;
import com.crypto.trading.common.exception.BusinessException;
import com.crypto.trading.common.util.DateUtil;
import com.crypto.trading.common.util.JsonUtil;
import com.crypto.trading.trade.model.entity.order.OrderEntity;
import com.crypto.trading.trade.model.entity.order.status.OrderStatusEntity;
import com.crypto.trading.trade.model.entity.statistics.OrderStatisticsEntity;
import com.crypto.trading.trade.repository.mapper.order.OrderMapper;
import com.crypto.trading.trade.repository.mapper.order.status.OrderStatusMapper;
import com.crypto.trading.trade.repository.mapper.statistics.OrderStatisticsMapper;
import com.crypto.trading.trade.service.order.OrderManagementService;
import com.crypto.trading.trade.service.trade.OrderConverterService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 订单管理服务实现类
 */
@Service
public class OrderManagementServiceImpl implements OrderManagementService {
    
    private static final Logger log = LoggerFactory.getLogger(OrderManagementServiceImpl.class);
    
    /**
     * 订单缓存，用于减少数据库访问
     * 格式: orderId -> OrderEntity
     */
    private final Map<String, OrderEntity> orderCache = new ConcurrentHashMap<>();
    
    /**
     * 客户端订单ID缓存，用于根据clientOrderId快速查找orderId
     * 格式: clientOrderId -> orderId
     */
    private final Map<String, String> clientOrderIdCache = new ConcurrentHashMap<>();
    
    /**
     * 缓存容量
     */
    private static final int CACHE_CAPACITY = 1000;
    
    @Autowired
    private OrderMapper orderMapper;
    
    @Autowired
    private OrderStatusMapper orderStatusMapper;
    
    @Autowired
    private OrderStatisticsMapper orderStatisticsMapper;
    
    @Autowired
    private OrderConverterService orderConverterService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OrderDTO createOrder(OrderRequestDTO request) {
        log.info("创建订单请求: {}", request);
        
        // 将请求DTO转换为订单实体
        OrderEntity orderEntity = orderConverterService.convertToEntity(request);
        
        // 调用内部方法创建订单
        OrderEntity createdOrder = createOrderInternal(orderEntity);
        
        // 将实体转换为DTO返回
        return orderConverterService.convertToDTO(createdOrder);
    }

    @Override
    public OrderEntity findOrder(String orderId, String clientOrderId) {
        log.info("查找订单: orderId={}, clientOrderId={}", orderId, clientOrderId);
        
        // 优先使用orderId查询
        if (orderId != null && !orderId.isEmpty()) {
            return getOrderById(orderId);
        }
        
        // 如果orderId为空，使用clientOrderId查询
        if (clientOrderId != null && !clientOrderId.isEmpty()) {
            return getOrderByClientOrderId(clientOrderId);
        }
        
        return null;
    }

    @Override
    public List<OrderResponseDTO> findOrders(String symbol, String status, int limit) {
        log.info("查询订单列表: symbol={}, status={}, limit={}", symbol, status, limit);
        
        // 构建查询条件
        LambdaQueryWrapper<OrderEntity> queryWrapper = new LambdaQueryWrapper<>();
        if (symbol != null && !symbol.isEmpty()) {
            queryWrapper.eq(OrderEntity::getSymbol, symbol);
        }
        if (status != null && !status.isEmpty()) {
            queryWrapper.eq(OrderEntity::getStatus, status);
        }
        queryWrapper.orderByDesc(OrderEntity::getCreatedTime);
        
        // 查询订单
        Page<OrderEntity> page = new Page<>(1, limit);
        IPage<OrderEntity> orderPage = orderMapper.selectPage(page, queryWrapper);
        
        // 转换为响应DTO
        List<OrderResponseDTO> result = new ArrayList<>();
        for (OrderEntity order : orderPage.getRecords()) {
            result.add(orderConverterService.convertToResponseDTO(order));
        }
        
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OrderDTO cancelOrder(String orderId, String clientOrderId) {
        log.info("取消订单: orderId={}, clientOrderId={}", orderId, clientOrderId);
        
        // 查找订单
        OrderEntity order = findOrder(orderId, clientOrderId);
        if (order == null) {
            throw new BusinessException("订单不存在");
        }
        
        // 检查订单状态是否可取消
        if (OrderEnums.OrderStatus.FILLED.name().equals(order.getStatus()) ||
            OrderEnums.OrderStatus.CANCELED.name().equals(order.getStatus()) ||
            OrderEnums.OrderStatus.REJECTED.name().equals(order.getStatus()) ||
            OrderEnums.OrderStatus.EXPIRED.name().equals(order.getStatus())) {
            throw new BusinessException("当前订单状态不可取消: " + order.getStatus());
        }
        
        // 更新订单状态为已取消
        OrderEntity updatedOrder = updateOrderStatus(
                order.getOrderId(), 
                OrderEnums.OrderStatus.CANCELED.name(), 
                "用户手动取消", 
                null);
        
        // 转换为DTO返回
        return orderConverterService.convertToDTO(updatedOrder);
    }

    @Override
    public List<OrderResponseDTO> findOrdersByStrategyId(String strategyId, int limit) {
        log.info("根据策略ID查询订单: strategyId={}, limit={}", strategyId, limit);
        
        // 构建查询条件
        LambdaQueryWrapper<OrderEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderEntity::getStrategyId, strategyId)
                .orderByDesc(OrderEntity::getCreatedTime);
        
        // 查询订单
        Page<OrderEntity> page = new Page<>(1, limit);
        IPage<OrderEntity> orderPage = orderMapper.selectPage(page, queryWrapper);
        
        // 转换为响应DTO
        List<OrderResponseDTO> result = new ArrayList<>();
        for (OrderEntity order : orderPage.getRecords()) {
            result.add(orderConverterService.convertToResponseDTO(order));
        }
        
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteOrder(String orderId) {
        log.info("删除订单: orderId={}", orderId);
        
        // 查找订单
        OrderEntity order = getOrderById(orderId);
        if (order == null) {
            log.warn("要删除的订单不存在: {}", orderId);
            return false;
        }
        
        // 逻辑删除订单
        order.setDeleted(1);
        order.setUpdatedTime(System.currentTimeMillis());
        int result = orderMapper.updateById(order);
        
        // 从缓存中移除
        orderCache.remove(orderId);
        clientOrderIdCache.remove(order.getClientOrderId());
        
        return result > 0;
    }

    @Override
    public long countOrders(String symbol, String status) {
        log.info("统计订单数量: symbol={}, status={}", symbol, status);
        
        // 构建查询条件
        LambdaQueryWrapper<OrderEntity> queryWrapper = new LambdaQueryWrapper<>();
        if (symbol != null && !symbol.isEmpty()) {
            queryWrapper.eq(OrderEntity::getSymbol, symbol);
        }
        if (status != null && !status.isEmpty()) {
            queryWrapper.eq(OrderEntity::getStatus, status);
        }
        
        // 查询数量
        return orderMapper.selectCount(queryWrapper);
    }

    /**
     * 内部创建订单方法
     * 
     * @param order 订单实体
     * @return 创建后的订单实体
     */
    @Transactional(rollbackFor = Exception.class)
    private OrderEntity createOrderInternal(OrderEntity order) {
        log.info("创建订单: {}", order);
        
        // 设置创建时间和更新时间
        if (order.getCreatedTime() == null) {
            order.setCreatedTime(System.currentTimeMillis());
        }
        
        if (order.getUpdatedTime() == null) {
            order.setUpdatedTime(System.currentTimeMillis());
        }
        
        // 如果状态为空，设置为NEW
        if (order.getStatus() == null || order.getStatus().isEmpty()) {
            order.setStatus(OrderEnums.OrderStatus.NEW.name());
        }
        
        // 保存订单
        orderMapper.insert(order);
        
        // 创建订单状态记录
        OrderStatusEntity statusEntity = new OrderStatusEntity.Builder()
                .orderId(order.getOrderId())
                .clientOrderId(order.getClientOrderId())
                .oldStatus("")
                .newStatus(order.getStatus())
                .reason("订单创建")
                .details(JsonUtil.toJsonString(order))
                .build();
        
        orderStatusMapper.insert(statusEntity);
        
        // 更新缓存
        updateOrderCache(order);
        
        // 更新统计数据
        updateStatisticsFromOrder(order);
        
        log.info("订单创建成功: {}", order);
        return order;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OrderEntity updateOrderStatus(String orderId, String newStatus, String reason, String details) {
        log.info("更新订单状态: orderId={}, newStatus={}, reason={}", orderId, newStatus, reason);
        
        // 查询订单
        OrderEntity order = getOrderById(orderId);
        if (order == null) {
            log.error("订单不存在: {}", orderId);
            throw new BusinessException("订单不存在: " + orderId);
        }
        
        // 如果状态没有变化，直接返回
        if (order.getStatus().equals(newStatus)) {
            log.info("订单状态未变化，无需更新: {}", orderId);
            return order;
        }
        
        String oldStatus = order.getStatus();
        
        // 更新订单状态
        order.setStatus(newStatus);
        order.setUpdatedTime(System.currentTimeMillis());
        
        // 根据状态设置额外字段
        if (OrderEnums.OrderStatus.FILLED.name().equals(newStatus)) {
            order.setExecutedTime(System.currentTimeMillis());
        } else if (OrderEnums.OrderStatus.REJECTED.name().equals(newStatus) ||
                 OrderEnums.OrderStatus.EXPIRED.name().equals(newStatus) ||
                 OrderEnums.OrderStatus.CANCELED.name().equals(newStatus)) {
            // 对于失败的订单，记录原因
            if (details != null && !details.isEmpty()) {
                Map<String, Object> detailsMap = JsonUtil.parseJsonToMap(details);
                if (detailsMap.containsKey("errorCode")) {
                    order.setErrorCode(detailsMap.get("errorCode").toString());
                }
                if (detailsMap.containsKey("errorMessage")) {
                    order.setErrorMessage(detailsMap.get("errorMessage").toString());
                }
            }
        }
        
        // 更新订单
        orderMapper.updateById(order);
        
        // 创建订单状态记录
        OrderStatusEntity statusEntity = new OrderStatusEntity.Builder()
                .orderId(orderId)
                .clientOrderId(order.getClientOrderId())
                .oldStatus(oldStatus)
                .newStatus(newStatus)
                .reason(reason)
                .details(details)
                .build();
        
        orderStatusMapper.insert(statusEntity);
        
        // 更新缓存
        updateOrderCache(order);
        
        // 更新统计数据
        updateStatisticsFromOrder(order);
        
        log.info("订单状态更新成功: {}", order);
        return order;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchUpdateOrderStatus(List<String> orderIds, String newStatus, String reason, String details) {
        log.info("批量更新订单状态: orderIds={}, newStatus={}, reason={}", orderIds, newStatus, reason);
        
        if (orderIds == null || orderIds.isEmpty()) {
            return 0;
        }
        
        int updatedCount = 0;
        long currentTime = System.currentTimeMillis();
        
        // 批量查询订单
        List<OrderEntity> orders = orderMapper.selectBatchIds(orderIds);
        List<OrderStatusEntity> statusEntities = new ArrayList<>();
        
        for (OrderEntity order : orders) {
            // 如果状态没有变化，跳过
            if (order.getStatus().equals(newStatus)) {
                continue;
            }
            
            String oldStatus = order.getStatus();
            
            // 更新订单状态
            order.setStatus(newStatus);
            order.setUpdatedTime(currentTime);
            
            // 根据状态设置额外字段
            if (OrderEnums.OrderStatus.FILLED.name().equals(newStatus)) {
                order.setExecutedTime(currentTime);
            }
            
            // 创建订单状态记录
            OrderStatusEntity statusEntity = new OrderStatusEntity.Builder()
                    .orderId(order.getOrderId())
                    .clientOrderId(order.getClientOrderId())
                    .oldStatus(oldStatus)
                    .newStatus(newStatus)
                    .reason(reason)
                    .details(details)
                    .updateTime(currentTime)
                    .build();
            
            statusEntities.add(statusEntity);
            updatedCount++;
            
            // 更新缓存
            updateOrderCache(order);
            
            // 更新统计数据
            updateStatisticsFromOrder(order);
        }
        
        // 批量更新订单
        if (!orders.isEmpty()) {
            for (OrderEntity order : orders) {
                orderMapper.updateById(order);
            }
        }
        
        // 批量插入状态记录
        if (!statusEntities.isEmpty()) {
            for (OrderStatusEntity statusEntity : statusEntities) {
                orderStatusMapper.insert(statusEntity);
            }
        }
        
        log.info("批量更新订单状态成功: 更新数量={}", updatedCount);
        return updatedCount;
    }

    @Override
    public OrderEntity getOrderById(String orderId) {
        // 先从缓存中查询
        OrderEntity cachedOrder = orderCache.get(orderId);
        if (cachedOrder != null) {
            return cachedOrder;
        }
        
        // 从数据库查询
        OrderEntity order = orderMapper.findByOrderId(orderId);
        if (order != null) {
            // 更新缓存
            updateOrderCache(order);
        }
        
        return order;
    }

    @Override
    public OrderEntity getOrderByClientOrderId(String clientOrderId) {
        // 先从clientOrderId缓存中获取orderId
        String orderId = clientOrderIdCache.get(clientOrderId);
        if (orderId != null) {
            return getOrderById(orderId);
        }
        
        // 从数据库查询
        OrderEntity order = orderMapper.findByClientOrderId(clientOrderId);
        if (order != null) {
            // 更新缓存
            updateOrderCache(order);
        }
        
        return order;
    }

    @Override
    public List<OrderEntity> getOrdersBySignalId(String signalId) {
        return orderMapper.findBySignalId(signalId);
    }

    @Override
    public IPage<OrderEntity> getOrdersBySymbolAndTimeRange(
            String symbol, long startTime, long endTime, int page, int size) {
        
        Page<OrderEntity> pageParam = new Page<>(page, size);
        
        // 使用LambdaQueryWrapper构建查询条件
        LambdaQueryWrapper<OrderEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderEntity::getSymbol, symbol)
                .ge(OrderEntity::getCreatedTime, startTime)
                .le(OrderEntity::getCreatedTime, endTime)
                .eq(OrderEntity::getIsDeleted, false)
                .orderByDesc(OrderEntity::getCreatedTime);
        
        return orderMapper.selectPage(pageParam, queryWrapper);
    }

    @Override
    public IPage<OrderEntity> getOrdersByStatus(String status, int page, int size) {
        Page<OrderEntity> pageParam = new Page<>(page, size);
        
        // 使用LambdaQueryWrapper构建查询条件
        LambdaQueryWrapper<OrderEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderEntity::getStatus, status)
                .eq(OrderEntity::getIsDeleted, false)
                .orderByDesc(OrderEntity::getCreatedTime);
        
        return orderMapper.selectPage(pageParam, queryWrapper);
    }

    @Override
    public List<OrderStatusEntity> getOrderStatusHistory(String orderId) {
        return orderStatusMapper.findByOrderId(orderId);
    }

    @Override
    public Map<String, Object> getOrderStatistics(
            String startDate, String endDate, String symbol, String strategy) {
        
        log.info("获取订单统计数据: startDate={}, endDate={}, symbol={}, strategy={}", 
                startDate, endDate, symbol, strategy);
        
        // 查询统计数据
        List<OrderStatisticsEntity> statsList = orderStatisticsMapper.findByDateRange(startDate, endDate);
        
        // 过滤符合条件的数据
        List<OrderStatisticsEntity> filteredStats = new ArrayList<>();
        for (OrderStatisticsEntity stats : statsList) {
            if ((symbol == null || symbol.isEmpty() || stats.getSymbol().equals(symbol)) &&
                (strategy == null || strategy.isEmpty() || stats.getStrategy().equals(strategy))) {
                filteredStats.add(stats);
            }
        }
        
        // 统计汇总数据
        int totalOrders = 0;
        int successOrders = 0;
        int failedOrders = 0;
        int canceledOrders = 0;
        BigDecimal totalAmount = BigDecimal.ZERO;
        
        for (OrderStatisticsEntity stats : filteredStats) {
            totalOrders += stats.getTotalOrders();
            successOrders += stats.getSuccessOrders();
            failedOrders += stats.getFailedOrders();
            canceledOrders += stats.getCanceledOrders();
            totalAmount = totalAmount.add(stats.getTotalAmount());
        }
        
        // 计算成功率
        BigDecimal successRate = BigDecimal.ZERO;
        if (totalOrders > 0) {
            successRate = new BigDecimal(successOrders)
                    .divide(new BigDecimal(totalOrders), 4, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal("100"));
        }
        
        // 组装结果
        Map<String, Object> result = new HashMap<>();
        result.put("startDate", startDate);
        result.put("endDate", endDate);
        result.put("symbol", symbol);
        result.put("strategy", strategy);
        result.put("totalOrders", totalOrders);
        result.put("successOrders", successOrders);
        result.put("failedOrders", failedOrders);
        result.put("canceledOrders", canceledOrders);
        result.put("totalAmount", totalAmount);
        result.put("successRate", successRate);
        result.put("detailedStats", filteredStats);
        
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateStatisticsFromOrder(OrderEntity order) {
        log.info("更新订单统计数据: orderId={}, status={}", order.getOrderId(), order.getStatus());
        
        if (order == null) {
            return false;
        }
        
        // 获取订单日期（yyyyMMdd格式）
        String orderDate = DateUtil.formatTimestamp(order.getCreatedTime(), "yyyyMMdd");
        String symbol = order.getSymbol();
        String strategy = order.getStrategy();
        
        // 查询或创建统计记录
        OrderStatisticsEntity stats = orderStatisticsMapper.findByDateAndSymbolAndStrategy(
                orderDate, symbol, strategy);
        
        if (stats == null) {
            // 创建新的统计记录
            stats = new OrderStatisticsEntity();
            stats.setStatsDate(orderDate);
            stats.setSymbol(symbol);
            stats.setStrategy(strategy);
            stats.setTotalOrders(0);
            stats.setSuccessOrders(0);
            stats.setFailedOrders(0);
            stats.setCanceledOrders(0);
            stats.setTotalAmount(BigDecimal.ZERO);
            stats.setCreatedTime(System.currentTimeMillis());
            stats.setUpdatedTime(System.currentTimeMillis());
            
            orderStatisticsMapper.insert(stats);
        }
        
        // 更新统计数据
        updateStatsBasedOnOrderStatus(stats, order);
        
        // 更新到数据库
        stats.setUpdatedTime(System.currentTimeMillis());
        orderStatisticsMapper.updateById(stats);
        
        log.info("订单统计数据更新成功: statsDate={}, symbol={}, strategy={}", 
                orderDate, symbol, strategy);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean recalculateStatistics(String statsDate) {
        log.info("重新计算统计数据: statsDate={}", statsDate);
        
        // 解析统计日期
        long startTime, endTime;
        try {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
            Date date = dateFormat.parse(statsDate);
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);
            startTime = calendar.getTimeInMillis();
            
            calendar.add(Calendar.DAY_OF_MONTH, 1);
            endTime = calendar.getTimeInMillis() - 1;
        } catch (Exception e) {
            log.error("解析统计日期失败: {}", e.getMessage());
            return false;
        }
        
        // 查询该日期的所有订单
        List<OrderEntity> orders = orderMapper.findByTimeRange(startTime, endTime);
        
        // 按小时、交易对和策略分组
        Map<String, List<OrderEntity>> groupedOrders = new HashMap<>();
        for (OrderEntity order : orders) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTimeInMillis(order.getCreatedTime());
            int hour = calendar.get(Calendar.HOUR_OF_DAY);
            
            String key = statsDate + "_" + hour + "_" + order.getSymbol() + "_" + order.getStrategy();
            groupedOrders.computeIfAbsent(key, k -> new ArrayList<>()).add(order);
        }
        
        // 清除该日期的旧统计数据
        LambdaQueryWrapper<OrderStatisticsEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderStatisticsEntity::getStatsDate, statsDate);
        orderStatisticsMapper.delete(queryWrapper);
        
        // 重新计算统计数据
        for (Map.Entry<String, List<OrderEntity>> entry : groupedOrders.entrySet()) {
            String[] parts = entry.getKey().split("_");
            String date = parts[0];
            int hour = Integer.parseInt(parts[1]);
            String symbol = parts[2];
            String strategy = parts[3];
            List<OrderEntity> orderList = entry.getValue();
            
            // 创建统计记录
            OrderStatisticsEntity stats = new OrderStatisticsEntity.Builder()
                    .statsDate(date)
                    .statsHour(hour)
                    .symbol(symbol)
                    .strategy(strategy)
                    .totalOrders(orderList.size())
                    .build();
            
            // 计算统计数据
            int successCount = 0;
            int failedCount = 0;
            int canceledCount = 0;
            BigDecimal totalAmount = BigDecimal.ZERO;
            long totalExecutionTime = 0;
            long minExecutionTime = Long.MAX_VALUE;
            long maxExecutionTime = 0;
            
            for (OrderEntity order : orderList) {
                String status = order.getStatus();
                if (OrderEnums.OrderStatus.FILLED.name().equals(status)) {
                    successCount++;
                    // 计算交易金额
                    BigDecimal amount = calculateOrderAmount(order);
                    totalAmount = totalAmount.add(amount);
                    
                    // 计算执行时间
                    if (order.getExecutedTime() != null && order.getCreatedTime() != null) {
                        long executionTime = order.getExecutedTime() - order.getCreatedTime();
                        totalExecutionTime += executionTime;
                        minExecutionTime = Math.min(minExecutionTime, executionTime);
                        maxExecutionTime = Math.max(maxExecutionTime, executionTime);
                    }
                } else if (OrderEnums.OrderStatus.REJECTED.name().equals(status) ||
                         OrderEnums.OrderStatus.EXPIRED.name().equals(status)) {
                    failedCount++;
                } else if (OrderEnums.OrderStatus.CANCELED.name().equals(status)) {
                    canceledCount++;
                }
            }
            
            // 设置统计数据
            stats.setSuccessOrders(successCount);
            stats.setFailedOrders(failedCount);
            stats.setCanceledOrders(canceledCount);
            stats.setTotalAmount(totalAmount);
            
            if (successCount > 0) {
                stats.setAvgExecutionTime(totalExecutionTime / successCount);
                if (minExecutionTime != Long.MAX_VALUE) {
                    stats.setMinExecutionTime(minExecutionTime);
                }
                stats.setMaxExecutionTime(maxExecutionTime);
            }
            
            // 计算成功率
            if (stats.getTotalOrders() > 0) {
                BigDecimal successRate = new BigDecimal(stats.getSuccessOrders())
                        .divide(new BigDecimal(stats.getTotalOrders()), 4, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal("100"));
                stats.setSuccessRate(successRate);
            }
            
            // 保存统计记录
            orderStatisticsMapper.insert(stats);
        }
        
        log.info("统计数据重新计算成功: statsDate={}, recordCount={}", statsDate, groupedOrders.size());
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int cleanupExpiredData(int retentionDays) {
        log.info("清理过期订单数据: retentionDays={}", retentionDays);
        
        // 计算过期时间点
        long expireTime = System.currentTimeMillis() - (long) retentionDays * 24 * 60 * 60 * 1000;
        
        // 查询过期的订单
        List<OrderEntity> expiredOrders = orderMapper.findByCreatedTimeBefore(expireTime);
        int count = 0;
        
        // 批量删除
        for (OrderEntity order : expiredOrders) {
            // 逻辑删除订单
            order.setDeleted(1);
            orderMapper.updateById(order);
            count++;
            
            // 清理缓存
            orderCache.remove(order.getOrderId());
            clientOrderIdCache.remove(order.getClientOrderId());
        }
        
        log.info("清理过期订单数据完成: 共清理{}条记录", count);
        return count;
    }
    
    /**
     * 根据订单状态更新统计数据
     *
     * @param stats 统计实体
     * @param order 订单实体
     */
    private void updateStatsBasedOnOrderStatus(OrderStatisticsEntity stats, OrderEntity order) {
        String status = order.getStatus();
        
        if (OrderEnums.OrderStatus.FILLED.name().equals(status)) {
            // 成功订单
            stats.setSuccessOrders(stats.getSuccessOrders() + 1);
            
            // 计算交易金额
            BigDecimal amount = calculateOrderAmount(order);
            stats.setTotalAmount(stats.getTotalAmount().add(amount));
            
            // 计算执行时间
            if (order.getExecutedTime() != null && order.getCreatedTime() != null) {
                long executionTime = order.getExecutedTime() - order.getCreatedTime();
                
                // 更新执行时间统计
                if (stats.getAvgExecutionTime() == null) {
                    stats.setAvgExecutionTime(executionTime);
                } else {
                    long totalTime = stats.getAvgExecutionTime() * (stats.getSuccessOrders() - 1) + executionTime;
                    stats.setAvgExecutionTime(totalTime / stats.getSuccessOrders());
                }
                
                // 更新最小执行时间
                if (stats.getMinExecutionTime() == null || executionTime < stats.getMinExecutionTime()) {
                    stats.setMinExecutionTime(executionTime);
                }
                
                // 更新最大执行时间
                if (stats.getMaxExecutionTime() == null || executionTime > stats.getMaxExecutionTime()) {
                    stats.setMaxExecutionTime(executionTime);
                }
            }
        } else if (OrderEnums.OrderStatus.REJECTED.name().equals(status) || 
                 OrderEnums.OrderStatus.EXPIRED.name().equals(status)) {
            // 失败订单
            stats.setFailedOrders(stats.getFailedOrders() + 1);
        } else if (OrderEnums.OrderStatus.CANCELED.name().equals(status)) {
            // 取消订单
            stats.setCanceledOrders(stats.getCanceledOrders() + 1);
        }
    }
    
    /**
     * 计算订单金额
     *
     * @param order 订单实体
     * @return 订单金额
     */
    private BigDecimal calculateOrderAmount(OrderEntity order) {
        if (order.getExecutedPrice() != null && order.getExecutedQuantity() != null) {
            return BigDecimal.valueOf(order.getExecutedPrice()).multiply(BigDecimal.valueOf(order.getExecutedQuantity()));
        } else if (order.getPrice() != null && order.getQuantity() != null) {
            return BigDecimal.valueOf(order.getPrice()).multiply(BigDecimal.valueOf(order.getQuantity()));
        } else {
            return BigDecimal.ZERO;
        }
    }
    
    /**
     * 更新订单缓存
     *
     * @param order 订单实体
     */
    private void updateOrderCache(OrderEntity order) {
        // 检查缓存是否需要清理
        if (orderCache.size() > CACHE_CAPACITY) {
            clearExpiredCache();
        }
        
        // 更新订单缓存
        orderCache.put(order.getOrderId(), order);
        
        // 更新客户端订单ID缓存
        if (order.getClientOrderId() != null) {
            clientOrderIdCache.put(order.getClientOrderId(), order.getOrderId());
        }
    }
    
    /**
     * 清理过期缓存
     */
    private void clearExpiredCache() {
        // 保留最近的一半缓存
        if (orderCache.size() <= CACHE_CAPACITY / 2) {
            return;
        }
        
        // 按创建时间排序
        List<Map.Entry<String, OrderEntity>> entries = new ArrayList<>(orderCache.entrySet());
        entries.sort(Comparator.comparing(e -> e.getValue().getCreatedTime()));
        
        // 移除旧的一半
        int removeCount = entries.size() / 2;
        for (int i = 0; i < removeCount; i++) {
            Map.Entry<String, OrderEntity> entry = entries.get(i);
            orderCache.remove(entry.getKey());
            
            // 同时清理clientOrderIdCache
            String clientOrderId = entry.getValue().getClientOrderId();
            if (clientOrderId != null) {
                clientOrderIdCache.remove(clientOrderId);
            }
        }
        
        log.info("清理订单缓存: 移除数量={}, 剩余数量={}", removeCount, orderCache.size());
    }
}