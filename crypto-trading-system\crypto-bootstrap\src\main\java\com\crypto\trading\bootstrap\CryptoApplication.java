package com.crypto.trading.bootstrap;

import com.crypto.trading.bootstrap.config.BootstrapConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.retry.annotation.EnableRetry;

/**
 * 应用程序入口类
 * 负责启动整个应用程序
 * 启用Spring Retry功能以支持HTTP请求重试
 */
@SpringBootApplication
@EnableRetry
public class CryptoApplication {
    
    private static final Logger log = LoggerFactory.getLogger(CryptoApplication.class);
    
    /**
     * 主方法
     * 
     * @param args 命令行参数
     */
    public static void main(String[] args) {
        try {
            log.info("正在启动虚拟货币量化交易系统...");
            
            // 显示JDK版本信息
            log.info("JDK版本: {}", System.getProperty("java.version"));
            log.info("JDK供应商: {}", System.getProperty("java.vendor"));
            log.info("JDK路径: {}", System.getProperty("java.home"));
            
            // 启动Spring应用
            SpringApplication app = new SpringApplication(CryptoApplication.class);
            app.run(args);
            
            log.info("虚拟货币量化交易系统启动成功");
            
        } catch (Exception e) {
            log.error("虚拟货币量化交易系统启动失败: {}", e.getMessage(), e);
            System.exit(1);
        }
    }
}