package com.crypto.trading.sdk.config;

import com.crypto.trading.common.enums.BinanceEnvironment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Configuration;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

/**
 * 币安API配置类
 * 包含API密钥、密钥、基础URL等配置项
 * 该配置类带有条件注解，只有在没有名为binanceApiConfigBean的bean时才生效
 * 这样可以避免与bootstrap模块中的BinanceApiConfig冲突
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "binance")
@ConditionalOnMissingBean(name = "binanceApiConfigBean")
public class BinanceApiConfig {
    private static final Logger log = LoggerFactory.getLogger(BinanceApiConfig.class);
    private static final String HMAC_SHA256_ALGORITHM = "HmacSHA256";

    /**
     * 币安API密钥
     */
    @Value("${binance.api.key:}")
    private String apiKey;

    /**
     * 币安API密钥
     */
    @Value("${binance.api.secret:}")
    private String secretKey;

    /**
     * 币安USDT合约API基础URL
     */
    @Value("${binance.api.baseUrl.usdtFutures:https://fapi.binance.com}")
    private String usdtFuturesBaseUrl;

    /**
     * 币安币本位合约API基础URL
     */
    @Value("${binance.api.baseUrl.coinFutures:https://dapi.binance.com}")
    private String coinFuturesBaseUrl;

    /**
     * 币安USDT合约WebSocket基础URL
     */
    @Value("${binance.api.wsUrl.usdtFutures:wss://fstream.binance.com}")
    private String usdtFuturesWsBaseUrl;

    /**
     * 币安币本位合约WebSocket基础URL
     */
    @Value("${binance.api.wsUrl.coinFutures:wss://dstream.binance.com}")
    private String coinFuturesWsBaseUrl;

    /**
     * 是否使用测试网络
     */
    @Value("${binance.api.use-testnet:false}")
    private boolean useTestnet;

    /**
     * 测试网络USDT合约API基础URL
     */
    @Value("${binance.api.baseUrl.usdtFuturesTestnet:https://testnet.binancefuture.com}")
    private String usdtFuturesTestnetBaseUrl;

    /**
     * 测试网络USDT合约WebSocket基础URL
     */
    @Value("${binance.api.wsUrl.usdtFuturesTestnet:wss://stream.binancefuture.com}")
    private String usdtFuturesTestnetWsBaseUrl;

    /**
     * 测试网络币本位合约API基础URL
     */
    @Value("${binance.api.baseUrl.coinFuturesTestnet:https://testnet.binancefuture.com}")
    private String coinFuturesTestnetBaseUrl;

    /**
     * 测试网络币本位合约WebSocket基础URL
     */
    @Value("${binance.api.wsUrl.coinFuturesTestnet:wss://dstream.binancefuture.com}")
    private String coinFuturesTestnetWsBaseUrl;

    /**
     * 代理主机
     */
    @Value("${binance.api.proxy.host:}")
    private String proxyHost;

    /**
     * 代理端口
     */
    @Value("${binance.api.proxy.port:0}")
    private int proxyPort;

    /**
     * 代理用户名
     */
    @Value("${binance.api.proxy.username:}")
    private String proxyUsername;

    /**
     * 代理密码
     */
    @Value("${binance.api.proxy.password:}")
    private String proxyPassword;

    /**
     * 连接超时时间(毫秒) - 增加到30秒以处理缓慢的网络连接
     */
    @Value("${binance.api.timeout.connect:30000}")
    private int connectTimeout;

    /**
     * 读取超时时间(毫秒) - 增加到60秒以处理可能的慢响应
     */
    @Value("${binance.api.timeout.read:60000}")
    private int readTimeout;

    /**
     * 写入超时时间(毫秒) - 增加到60秒以处理可能的慢连接
     */
    @Value("${binance.api.timeout.write:60000}")
    private int writeTimeout;

    /**
     * 最大重试次数 - 增加到5次以提高成功率
     */
    @Value("${binance.api.retry.max:5}")
    private int maxRetries;

    /**
     * 接收窗口时间(毫秒) - 用于请求有效期的参数
     */
    @Value("${binance.api.recvWindow:60000}")
    private Long recvWindow;

    /**
     * 获取币安API密钥
     *
     * @return API密钥
     */
    public String getApiKey() {
        return apiKey;
    }

    /**
     * 获取币安API密钥
     *
     * @return API密钥
     */
    public String getSecretKey() {
        return secretKey;
    }

    /**
     * 获取当前环境（测试网或生产环境）
     * 
     * @return 当前币安环境枚举
     */
    public BinanceEnvironment getCurrentEnvironment() {
        return BinanceEnvironment.fromUseTestnet(useTestnet);
    }

    /**
     * 获取USDT合约API基础URL
     *
     * @return USDT合约API基础URL
     */
    public String getUsdtFuturesBaseUrl() {
        return getCurrentEnvironment().getUsdtFuturesApiBaseUrl();
    }

    /**
     * 获取测试网络USDT合约API基础URL
     *
     * @return 测试网络USDT合约API基础URL
     */
    public String getUsdtFuturesTestnetBaseUrl() {
        return BinanceEnvironment.TESTNET.getUsdtFuturesApiBaseUrl();
    }

    /**
     * 获取币本位合约API基础URL
     *
     * @return 币本位合约API基础URL
     */
    public String getCoinFuturesBaseUrl() {
        return getCurrentEnvironment().getCoinFuturesApiBaseUrl();
    }

    /**
     * 获取USDT合约WebSocket基础URL
     *
     * @return USDT合约WebSocket基础URL
     */
    public String getUsdtFuturesWsBaseUrl() {
        return getCurrentEnvironment().getUsdtFuturesWsBaseUrl();
    }

    /**
     * 获取币本位合约WebSocket基础URL
     *
     * @return 币本位合约WebSocket基础URL
     */
    public String getCoinFuturesWsBaseUrl() {
        return getCurrentEnvironment().getCoinFuturesWsBaseUrl();
    }

    /**
     * 获取测试网络USDT合约WebSocket基础URL
     *
     * @return 测试网络USDT合约WebSocket基础URL
     */
    public String getUsdtFuturesTestnetWsBaseUrl() {
        return BinanceEnvironment.TESTNET.getUsdtFuturesWsBaseUrl();
    }

    /**
     * 获取测试网络币本位合约WebSocket基础URL
     *
     * @return 测试网络币本位合约WebSocket基础URL
     */
    public String getCoinFuturesTestnetWsBaseUrl() {
        return BinanceEnvironment.TESTNET.getCoinFuturesWsBaseUrl();
    }

    /**
     * 是否使用测试网络
     *
     * @return 是否使用测试网络
     */
    public boolean isUseTestnet() {
        return useTestnet;
    }

    /**
     * 获取代理主机
     *
     * @return 代理主机
     */
    public String getProxyHost() {
        return proxyHost;
    }

    /**
     * 获取代理端口
     *
     * @return 代理端口
     */
    public int getProxyPort() {
        return proxyPort;
    }

    /**
     * 获取代理用户名
     *
     * @return 代理用户名
     */
    public String getProxyUsername() {
        return proxyUsername;
    }

    /**
     * 获取代理密码
     *
     * @return 代理密码
     */
    public String getProxyPassword() {
        return proxyPassword;
    }

    /**
     * 是否配置了代理
     *
     * @return 是否配置了代理
     */
    public boolean hasProxy() {
        return proxyHost != null && !proxyHost.isEmpty() && proxyPort > 0;
    }

    /**
     * 获取连接超时时间(毫秒)
     *
     * @return 连接超时时间
     */
    public int getConnectTimeout() {
        return connectTimeout;
    }

    /**
     * 获取读取超时时间(毫秒)
     *
     * @return 读取超时时间
     */
    public int getReadTimeout() {
        return readTimeout;
    }

    /**
     * 获取写入超时时间(毫秒)
     *
     * @return 写入超时时间
     */
    public int getWriteTimeout() {
        return writeTimeout;
    }

    /**
     * 获取最大重试次数
     *
     * @return 最大重试次数
     */
    public int getMaxRetries() {
        return maxRetries;
    }

    /**
     * 获取接收窗口时间(毫秒)
     *
     * @return 接收窗口时间
     */
    public Long getRecvWindow() {
        return recvWindow;
    }

    /**
     * 生成币安API签名
     *
     * @param data 请求参数字符串
     * @return 签名字符串
     */
    public String generateSignature(String data) {
        return generateHmacSha256(data, secretKey);
    }

    /**
     * 计算HMAC-SHA256签名
     *
     * @param data 输入字符串
     * @param key  密钥
     * @return HMAC-SHA256签名（十六进制字符串）
     */
    private String generateHmacSha256(String data, String key) {
        try {
            Mac hmacSha256 = Mac.getInstance(HMAC_SHA256_ALGORITHM);
            SecretKeySpec secretKeySpec = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), HMAC_SHA256_ALGORITHM);
            hmacSha256.init(secretKeySpec);
            byte[] hashBytes = hmacSha256.doFinal(data.getBytes(StandardCharsets.UTF_8));

            return bytesToHex(hashBytes);
        } catch (Exception e) {
            log.error("生成HMAC-SHA256签名失败", e);
            throw new RuntimeException("生成签名失败", e);
        }
    }

    /**
     * 将字节数组转换为十六进制字符串
     *
     * @param bytes 字节数组
     * @return 十六进制字符串
     */
    private String bytesToHex(byte[] bytes) {
        StringBuilder hexString = new StringBuilder();
        for (byte b : bytes) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) {
                hexString.append('0');
            }
            hexString.append(hex);
        }
        return hexString.toString();
    }

        /**
     * 限流配置
     */
    @Setter
    @Getter
    private RateLimitConfig rateLimit = new RateLimitConfig();
    
    /**
     * 限流配置内部类
     */
    @Data
    public static class RateLimitConfig {
        /**
         * 权重限制
         */
        @Value("${binance.rateLimit.weightLimit:1200}")
        private int weightLimit = 1200;
        
        /**
         * 权重计算间隔（分钟）
         */
        @Value("${binance.rateLimit.weightInterval:1}")
        private int weightInterval = 1;
        
        /**
         * 订单限制
         */
        @Value("${binance.rateLimit.orderLimit:100}")
        private int orderLimit = 100;
        
        /**
         * 订单计算间隔（秒）
         */
        @Value("${binance.rateLimit.orderInterval:10}")
        private int orderInterval = 10;
    }

    @Override
    public String toString() {
        return "BinanceApiConfig{" +
                "apiKey='" + (apiKey != null && !apiKey.isEmpty() ? "******" : "未设置") + '\'' +
                ", secretKey='" + (secretKey != null && !secretKey.isEmpty() ? "******" : "未设置") + '\'' +
                ", usdtFuturesBaseUrl='" + usdtFuturesBaseUrl + '\'' +
                ", coinFuturesBaseUrl='" + coinFuturesBaseUrl + '\'' +
                ", usdtFuturesWsBaseUrl='" + usdtFuturesWsBaseUrl + '\'' +
                ", coinFuturesWsBaseUrl='" + coinFuturesWsBaseUrl + '\'' +
                ", useTestnet=" + useTestnet +
                ", usdtFuturesTestnetBaseUrl='" + usdtFuturesTestnetBaseUrl + '\'' +
                ", coinFuturesTestnetBaseUrl='" + coinFuturesTestnetBaseUrl + '\'' +
                ", usdtFuturesTestnetWsBaseUrl='" + usdtFuturesTestnetWsBaseUrl + '\'' +
                ", coinFuturesTestnetWsBaseUrl='" + coinFuturesTestnetWsBaseUrl + '\'' +
                ", hasProxy=" + hasProxy() +
                ", rateLimit={weightLimit=" + rateLimit.weightLimit +
                ", orderLimit=" + rateLimit.orderLimit + "}" +
                '}';
    }
}
