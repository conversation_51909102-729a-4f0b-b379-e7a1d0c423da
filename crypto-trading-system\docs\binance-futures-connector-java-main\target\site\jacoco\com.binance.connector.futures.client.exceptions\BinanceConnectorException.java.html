<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>BinanceConnectorException.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">io.github.binance:binance-futures-connector-java</a> &gt; <a href="index.source.html" class="el_package">com.binance.connector.futures.client.exceptions</a> &gt; <span class="el_source">BinanceConnectorException.java</span></div><h1>BinanceConnectorException.java</h1><pre class="source lang-java linenums">package com.binance.connector.futures.client.exceptions;

/**
 * 币安期货API连接器异常类
 * 用于处理连接器相关的运行时异常
 */
public class BinanceConnectorException extends RuntimeException {

    // 序列化版本号
    private static final long serialVersionUID = 1L;

    /**
     * 构造函数
     * @param fullErrMsg 完整错误信息
     */
    public BinanceConnectorException(String fullErrMsg) {
<span class="fc" id="L17">        super(fullErrMsg);</span>
<span class="fc" id="L18">    }</span>

}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.7.************</span></div></body></html>