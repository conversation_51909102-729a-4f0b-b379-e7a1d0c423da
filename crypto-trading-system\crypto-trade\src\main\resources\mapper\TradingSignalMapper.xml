<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.crypto.trading.trade.repository.mapper.TradingSignalMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.crypto.trading.trade.model.entity.TradingSignalEntity">
        <id column="id" property="id"/>
        <result column="signal_id" property="signalId"/>
        <result column="symbol" property="symbol"/>
        <result column="strategy" property="strategy"/>
        <result column="signal_type" property="signalType"/>
        <result column="confidence" property="confidence"/>
        <result column="price" property="price"/>
        <result column="quantity" property="quantity"/>
        <result column="stop_loss" property="stopLoss"/>
        <result column="take_profit" property="takeProfit"/>
        <result column="order_type" property="orderType"/>
        <result column="time_in_force" property="timeInForce"/>
        <result column="leverage_level" property="leverageLevel"/>
        <result column="additional_params" property="additionalParams"/>
        <result column="processed" property="processed"/>
        <result column="processed_time" property="processedTime"/>
        <result column="process_result" property="processResult"/>
        <result column="generated_time" property="generatedTime"/>
        <result column="created_time" property="createdTime"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <!-- 查询指定时间内的所有信号 -->
    <select id="findSignalsByTimeRange" resultMap="BaseResultMap">
        SELECT * FROM t_trading_signal
        WHERE generated_time BETWEEN #{startTime} AND #{endTime}
        AND is_deleted = 0
        ORDER BY generated_time DESC
    </select>

    <!-- 按信号类型和置信度查询信号 -->
    <select id="findSignalsByTypeAndConfidence" resultMap="BaseResultMap">
        SELECT * FROM t_trading_signal
        WHERE signal_type = #{signalType}
        AND confidence >= #{minConfidence}
        AND is_deleted = 0
        ORDER BY generated_time DESC
        LIMIT #{limit}
    </select>

    <!-- 统计各策略在指定时间段内的信号数量 -->
    <select id="countSignalsByStrategy" resultType="java.util.Map">
        SELECT strategy, COUNT(*) as count
        FROM t_trading_signal
        WHERE generated_time BETWEEN #{startTime} AND #{endTime}
        AND is_deleted = 0
        GROUP BY strategy
    </select>

    <!-- 批量更新信号处理状态 -->
    <update id="batchUpdateProcessStatus">
        UPDATE t_trading_signal
        SET processed = #{processed},
            processed_time = #{processedTime},
            process_result = #{processResult}
        WHERE signal_id IN
        <foreach collection="signalIds" item="signalId" open="(" separator="," close=")">
            #{signalId}
        </foreach>
    </update>

</mapper>