#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
基础版主入口文件，逐步测试模块导入
"""

import os
import sys
import signal
import time
from typing import Dict, Any, Optional
from loguru import logger

# 添加src目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 逐步导入模块进行测试
try:
    logger.info("开始导入核心配置模块...")
    from core.config import Config
    logger.info("✓ Config模块导入成功")
    
    logger.info("开始导入Kafka客户端...")
    from api.kafka_client import KafkaClient
    logger.info("✓ KafkaClient模块导入成功")
    
    # 暂时跳过有问题的data模块，直接测试其他模块
    logger.info("开始导入ML模块...")
    # from ml.models.model_trainer import ModelTrainer
    # logger.info("✓ ModelTrainer模块导入成功")
    
    logger.info("所有基础模块导入成功！")
    
except ImportError as e:
    logger.error(f"模块导入失败: {e}")
    sys.exit(1)


class BasicTradingStrategyApp:
    """基础版交易策略应用"""
    
    def __init__(self):
        """初始化应用"""
        logger.info("初始化基础版交易策略应用")
        self.config = Config()
        self.running = False
        
        # 初始化Kafka客户端
        self.kafka_client = KafkaClient(self.config)
        
        # 注册信号处理器
        signal.signal(signal.SIGINT, self.handle_signal)
        signal.signal(signal.SIGTERM, self.handle_signal)
    
    def start(self):
        """启动应用"""
        logger.info("启动基础版交易策略应用")
        self.running = True
        
        # 测试配置读取
        kafka_servers = self.config.get('kafka', 'bootstrap_servers')
        logger.info(f"Kafka服务器配置: {kafka_servers}")
        
        strategy_params = self.config.get_strategy_param('lppl.window_size')
        logger.info(f"LPPL窗口大小: {strategy_params}")
        
        # 简单的主循环
        while self.running:
            try:
                logger.info("应用运行中...")
                time.sleep(5)  # 每5秒输出一次状态
                
            except Exception as e:
                logger.error(f"主循环异常: {str(e)}")
                time.sleep(1)
    
    def handle_signal(self, signum, frame):
        """处理终止信号"""
        logger.info(f"接收到信号 {signum}，准备关闭应用...")
        self.running = False
    
    def stop(self):
        """停止应用"""
        logger.info("关闭基础版交易策略应用")
        self.running = False
        if hasattr(self, 'kafka_client'):
            self.kafka_client.clean_up()


def main():
    """程序入口"""
    app = BasicTradingStrategyApp()
    
    try:
        app.start()
    except KeyboardInterrupt:
        logger.info("收到键盘中断")
    except Exception as e:
        logger.error(f"应用运行异常: {str(e)}")
    finally:
        app.stop()


if __name__ == "__main__":
    main()