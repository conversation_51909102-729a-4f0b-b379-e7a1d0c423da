package com.crypto.trading.market.consumer;

import com.crypto.trading.common.dto.market.TradeDTO;
import com.crypto.trading.market.repository.InfluxDBRepository;
import com.crypto.trading.market.repository.MarketDataRepository;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.annotation.RetryableTopic;
import org.springframework.kafka.retrytopic.TopicSuffixingStrategy;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.retry.annotation.Backoff;
import org.springframework.stereotype.Component;

import java.util.concurrent.atomic.AtomicLong;

/**
 * 交易数据Kafka消费者
 * 专门负责消费原始交易数据并写入数据库
 * 使用Kafka的重试机制和死信队列确保数据可靠性
 */
@Component
public class TradeDataConsumer {

    private static final Logger log = LoggerFactory.getLogger(TradeDataConsumer.class);

    @Autowired
    private InfluxDBRepository influxDBRepository;

    @Autowired
    private MarketDataRepository mySQLMarketDataRepository;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 处理成功计数器
     */
    private final AtomicLong successCount = new AtomicLong(0);

    /**
     * 处理失败计数器
     */
    private final AtomicLong failureCount = new AtomicLong(0);

    /**
     * 消费交易原始数据并写入数据库
     * 使用重试机制和死信队列确保数据可靠性
     */
    @RetryableTopic(
            attempts = "3",
            backoff = @Backoff(delay = 1000, multiplier = 2.0),
            autoCreateTopics = "true",
            topicSuffixingStrategy = TopicSuffixingStrategy.SUFFIX_WITH_INDEX_VALUE,
            dltStrategy = org.springframework.kafka.retrytopic.DltStrategy.FAIL_ON_ERROR,
            include = {Exception.class}
    )
    @KafkaListener(
            topics = "trade.raw.data",
            groupId = "trade-database-consumer-group",
            containerFactory = "kafkaListenerContainerFactory"
    )
    public void consumeTradeData(
            @Payload String message,
            @Header(KafkaHeaders.RECEIVED_TOPIC) String topic,
            @Header(KafkaHeaders.RECEIVED_PARTITION) int partition,
            @Header(KafkaHeaders.OFFSET) long offset,
            ConsumerRecord<String, String> record,
            Acknowledgment acknowledgment) {

        String key = record.key();
        
        log.debug("接收到交易数据: topic={}, partition={}, offset={}, key={}", 
                topic, partition, offset, key);

        try {
            // 解析JSON消息
            TradeDTO tradeDTO = objectMapper.readValue(message, TradeDTO.class);
            
            if (tradeDTO == null) {
                log.warn("交易数据解析为空: key={}, message={}", key, message);
                acknowledgment.acknowledge();
                return;
            }

            // 写入InfluxDB
            boolean influxSuccess = writeToInfluxDB(tradeDTO);
            
            // 写入MySQL
            boolean mysqlSuccess = writeToMySQL(tradeDTO);

            // 只有两个数据库都写入成功才确认消息
            if (influxSuccess && mysqlSuccess) {
                acknowledgment.acknowledge();
                successCount.incrementAndGet();
                
                log.debug("交易数据写入数据库成功: symbol={}, tradeId={}, time={}", 
                        tradeDTO.getSymbol(), tradeDTO.getId(), tradeDTO.getTime());
            } else {
                // 如果任何一个数据库写入失败，抛出异常触发重试
                String errorMsg = String.format("交易数据写入数据库失败: symbol=%s, tradeId=%s, influxSuccess=%s, mysqlSuccess=%s",
                        tradeDTO.getSymbol(), tradeDTO.getId(), influxSuccess, mysqlSuccess);
                throw new RuntimeException(errorMsg);
            }

        } catch (Exception e) {
            failureCount.incrementAndGet();
            log.error("处理交易数据异常: topic={}, partition={}, offset={}, key={}, error={}", 
                    topic, partition, offset, key, e.getMessage(), e);
            
            // 重新抛出异常，触发重试机制
            throw new RuntimeException("处理交易数据失败", e);
        }
    }

    /**
     * 写入InfluxDB
     */
    private boolean writeToInfluxDB(TradeDTO tradeDTO) {
        try {
            influxDBRepository.saveTradeData(tradeDTO);
            return true;
        } catch (Exception e) {
            log.error("写入交易数据到InfluxDB失败: symbol={}, tradeId={}, error={}", 
                    tradeDTO.getSymbol(), tradeDTO.getId(), e.getMessage(), e);
            return false;
        }
    }

    /**
     * 写入MySQL
     */
    private boolean writeToMySQL(TradeDTO tradeDTO) {
        try {
            mySQLMarketDataRepository.saveTradeData(tradeDTO);
            return true;
        } catch (Exception e) {
            log.error("写入交易数据到MySQL失败: symbol={}, tradeId={}, error={}", 
                    tradeDTO.getSymbol(), tradeDTO.getId(), e.getMessage(), e);
            return false;
        }
    }

    /**
     * 处理死信队列消息
     */
    @KafkaListener(
            topics = "trade.raw.data-dlt",
            groupId = "trade-database-consumer-group-dlt"
    )
    public void handleDltMessage(
            @Payload String message,
            @Header(KafkaHeaders.RECEIVED_TOPIC) String topic,
            @Header(KafkaHeaders.RECEIVED_PARTITION) int partition,
            @Header(KafkaHeaders.OFFSET) long offset,
            ConsumerRecord<String, String> record,
            Acknowledgment acknowledgment) {

        String key = record.key();
        
        log.error("交易数据进入死信队列: topic={}, partition={}, offset={}, key={}, message={}", 
                topic, partition, offset, key, message);

        // 这里可以实现特殊的处理逻辑，比如：
        // 1. 发送告警通知
        // 2. 写入特殊的错误日志表
        // 3. 尝试其他恢复策略
        
        // 确认死信队列消息，避免重复处理
        acknowledgment.acknowledge();
    }

    /**
     * 获取处理成功计数
     */
    public long getSuccessCount() {
        return successCount.get();
    }

    /**
     * 获取处理失败计数
     */
    public long getFailureCount() {
        return failureCount.get();
    }

    /**
     * 重置计数器
     */
    public void resetCounters() {
        successCount.set(0);
        failureCount.set(0);
    }
}
