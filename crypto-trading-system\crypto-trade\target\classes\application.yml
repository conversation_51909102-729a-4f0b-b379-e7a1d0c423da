server:
  port: 8082
  servlet:
    context-path: /trade

spring:
  application:
    name: crypto-trade
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **************************************************************************************************************************
    username: root
    password: root
  kafka:
    bootstrap-servers: localhost:9092
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
      acks: all
      retries: 3
      batch-size: 16384
      buffer-memory: 33554432
    consumer:
      group-id: crypto-trade
      auto-offset-reset: earliest
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      enable-auto-commit: false
      max-poll-records: 100
  task:
    execution:
      pool:
        # 虚拟线程配置
        core-size: 10
        max-size: 100
        queue-capacity: 500
        keep-alive: 60s
        virtual-threads: true

mybatis-plus:
  mapper-locations: classpath:mapper/**/*.xml
  type-aliases-package: com.crypto.trading.trade.model.entity
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

logging:
  level:
    root: info
    com.crypto.trading: debug
  file:
    name: logs/crypto-trade.log

# 交易执行配置
trade:
  execution:
    dryRun: true
    rateLimit:
      order: 10
      query: 20
    timeout: 5000
    statusQueryInterval: 1000
    maxRetries: 3
    retryInterval: 2000
    
# Binance API配置
binance:
  api:
    # API密钥和密钥（实际值应通过环境变量或其他安全方式提供）
    key: ${BINANCE_API_KEY:your-api-key}
    secret: ${BINANCE_API_SECRET:your-api-secret}
    # API基础URL
    base-url: https://fapi.binance.com
    # WebSocket URL
    websocket-url: wss://fstream.binance.com
    # 是否使用测试网络
    use-testnet: false
    # 测试网API基础URL
    testnet-base-url: https://testnet.binancefuture.com
    # 测试网WebSocket URL
    testnet-websocket-url: wss://stream.binancefuture.com
    # 连接超时（毫秒）
    connection-timeout: 3000
    # 读取超时（毫秒）
    read-timeout: 5000
    # 写入超时（毫秒）
    write-timeout: 5000
    # 重试次数
    max-retries: 3

# Kafka主题配置
kafka:
  topic:
    order:
      execution:
        result: order.execution.result
        error: order.execution.error
      status:
        update: order.status.update
    partitions: 3
    replication-factor: 1

# Resilience4j配置
resilience4j:
  ratelimiter:
    instances:
      orderRateLimiter:
        limitForPeriod: 10
        limitRefreshPeriod: 1s
        timeoutDuration: 100ms
      queryRateLimiter:
        limitForPeriod: 20
        limitRefreshPeriod: 1s
        timeoutDuration: 100ms