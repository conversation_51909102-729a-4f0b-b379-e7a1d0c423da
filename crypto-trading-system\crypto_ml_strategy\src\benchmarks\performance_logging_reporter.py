"""
Crypto ML Strategy - 性能监控报告器

该模块实现了性能监控的报告生成功能，包括错误率跟踪、
性能趋势分析和自动报告生成。

主要功能：
- ErrorRateTracker: 错误率跟踪器
- PerformanceReporter: 性能报告生成器
- TrendAnalyzer: 性能趋势分析器
- AlertManager: 性能告警管理器

作者: Crypto ML Strategy Team
日期: 2024-01-20
版本: 1.0.0
"""

import json
import os
import threading
import time
from collections import defaultdict, deque
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Union, Tuple
from loguru import logger
import statistics
from pathlib import Path


@dataclass
class ErrorRateMetrics:
    """错误率指标"""
    function_name: str
    total_calls: int
    error_calls: int
    error_rate: float
    avg_execution_time_ms: float
    last_error_time: Optional[datetime]
    error_types: Dict[str, int] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "function_name": self.function_name,
            "total_calls": self.total_calls,
            "error_calls": self.error_calls,
            "error_rate": self.error_rate,
            "avg_execution_time_ms": self.avg_execution_time_ms,
            "last_error_time": self.last_error_time.isoformat() if self.last_error_time else None,
            "error_types": self.error_types
        }


@dataclass
class PerformanceAlert:
    """性能告警"""
    alert_type: str
    severity: str  # LOW, MEDIUM, HIGH, CRITICAL
    function_name: str
    message: str
    threshold: float
    current_value: float
    timestamp: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "alert_type": self.alert_type,
            "severity": self.severity,
            "function_name": self.function_name,
            "message": self.message,
            "threshold": self.threshold,
            "current_value": self.current_value,
            "timestamp": self.timestamp.isoformat()
        }


class ErrorRateTracker:
    """错误率跟踪器"""
    
    def __init__(self, window_size: int = 1000):
        self.window_size = window_size
        self._function_calls: Dict[str, deque] = defaultdict(lambda: deque(maxlen=window_size))
        self._function_errors: Dict[str, deque] = defaultdict(lambda: deque(maxlen=window_size))
        self._function_times: Dict[str, deque] = defaultdict(lambda: deque(maxlen=window_size))
        self._error_types: Dict[str, Dict[str, int]] = defaultdict(lambda: defaultdict(int))
        self._lock = threading.RLock()
    
    def record_call(self, function_name: str, execution_time_ms: float, 
                   error: Optional[Exception] = None) -> None:
        """记录函数调用"""
        with self._lock:
            timestamp = datetime.now()
            
            # 记录调用
            self._function_calls[function_name].append(timestamp)
            self._function_times[function_name].append(execution_time_ms)
            
            # 记录错误
            if error:
                self._function_errors[function_name].append(timestamp)
                error_type = type(error).__name__
                self._error_types[function_name][error_type] += 1
    
    def get_error_rate(self, function_name: str) -> ErrorRateMetrics:
        """获取函数的错误率指标"""
        with self._lock:
            calls = self._function_calls.get(function_name, deque())
            errors = self._function_errors.get(function_name, deque())
            times = self._function_times.get(function_name, deque())
            error_types = self._error_types.get(function_name, {})
            
            total_calls = len(calls)
            error_calls = len(errors)
            error_rate = error_calls / max(total_calls, 1)
            avg_time = statistics.mean(times) if times else 0.0
            last_error = max(errors) if errors else None
            
            return ErrorRateMetrics(
                function_name=function_name,
                total_calls=total_calls,
                error_calls=error_calls,
                error_rate=error_rate,
                avg_execution_time_ms=avg_time,
                last_error_time=last_error,
                error_types=dict(error_types)
            )
    
    def get_all_error_rates(self) -> List[ErrorRateMetrics]:
        """获取所有函数的错误率指标"""
        with self._lock:
            return [
                self.get_error_rate(function_name)
                for function_name in self._function_calls.keys()
            ]
    
    def get_high_error_rate_functions(self, threshold: float = 0.1) -> List[ErrorRateMetrics]:
        """获取高错误率的函数"""
        return [
            metrics for metrics in self.get_all_error_rates()
            if metrics.error_rate >= threshold and metrics.total_calls >= 10
        ]


class TrendAnalyzer:
    """性能趋势分析器"""
    
    def __init__(self, history_size: int = 10000):
        self.history_size = history_size
        self._performance_history: deque = deque(maxlen=history_size)
        self._lock = threading.RLock()
    
    def add_performance_data(self, function_name: str, execution_time_ms: float,
                           memory_usage_mb: float, cpu_usage_percent: float) -> None:
        """添加性能数据"""
        with self._lock:
            data_point = {
                "timestamp": datetime.now(),
                "function_name": function_name,
                "execution_time_ms": execution_time_ms,
                "memory_usage_mb": memory_usage_mb,
                "cpu_usage_percent": cpu_usage_percent
            }
            self._performance_history.append(data_point)
    
    def analyze_trend(self, function_name: str, metric: str, 
                     duration_minutes: int = 60) -> Dict[str, Any]:
        """分析性能趋势"""
        with self._lock:
            cutoff_time = datetime.now() - timedelta(minutes=duration_minutes)
            
            # 过滤数据
            relevant_data = [
                point for point in self._performance_history
                if (point["function_name"] == function_name and 
                    point["timestamp"] >= cutoff_time and
                    metric in point)
            ]
            
            if len(relevant_data) < 2:
                return {"trend": "insufficient_data", "data_points": len(relevant_data)}
            
            # 提取指标值
            values = [point[metric] for point in relevant_data]
            timestamps = [point["timestamp"] for point in relevant_data]
            
            # 计算趋势
            trend_analysis = self._calculate_trend(values, timestamps)
            
            return {
                "function_name": function_name,
                "metric": metric,
                "duration_minutes": duration_minutes,
                "data_points": len(relevant_data),
                "trend": trend_analysis["trend"],
                "slope": trend_analysis["slope"],
                "correlation": trend_analysis["correlation"],
                "current_value": values[-1],
                "avg_value": statistics.mean(values),
                "min_value": min(values),
                "max_value": max(values),
                "std_dev": statistics.stdev(values) if len(values) > 1 else 0
            }
    
    def _calculate_trend(self, values: List[float], timestamps: List[datetime]) -> Dict[str, Any]:
        """计算趋势"""
        try:
            if len(values) < 2:
                return {"trend": "insufficient_data", "slope": 0, "correlation": 0}
            
            # 转换时间戳为数值
            base_time = timestamps[0]
            time_values = [(ts - base_time).total_seconds() for ts in timestamps]
            
            # 计算线性回归
            n = len(values)
            sum_x = sum(time_values)
            sum_y = sum(values)
            sum_xy = sum(x * y for x, y in zip(time_values, values))
            sum_x2 = sum(x * x for x in time_values)
            sum_y2 = sum(y * y for y in values)
            
            # 斜率
            slope = (n * sum_xy - sum_x * sum_y) / (n * sum_x2 - sum_x * sum_x)
            
            # 相关系数
            numerator = n * sum_xy - sum_x * sum_y
            denominator = ((n * sum_x2 - sum_x * sum_x) * (n * sum_y2 - sum_y * sum_y)) ** 0.5
            correlation = numerator / denominator if denominator != 0 else 0
            
            # 判断趋势
            if abs(correlation) < 0.3:
                trend = "stable"
            elif slope > 0:
                trend = "increasing"
            else:
                trend = "decreasing"
            
            return {
                "trend": trend,
                "slope": slope,
                "correlation": correlation
            }
            
        except Exception as e:
            logger.error(f"Failed to calculate trend: {e}")
            return {"trend": "error", "slope": 0, "correlation": 0}
    
    def get_performance_summary(self, duration_minutes: int = 60) -> Dict[str, Any]:
        """获取性能摘要"""
        with self._lock:
            cutoff_time = datetime.now() - timedelta(minutes=duration_minutes)
            
            # 过滤数据
            recent_data = [
                point for point in self._performance_history
                if point["timestamp"] >= cutoff_time
            ]
            
            if not recent_data:
                return {"summary": "no_data"}
            
            # 按函数分组
            function_data = defaultdict(list)
            for point in recent_data:
                function_data[point["function_name"]].append(point)
            
            # 生成摘要
            summary = {
                "duration_minutes": duration_minutes,
                "total_data_points": len(recent_data),
                "unique_functions": len(function_data),
                "functions": {}
            }
            
            for function_name, points in function_data.items():
                execution_times = [p["execution_time_ms"] for p in points]
                memory_usage = [p["memory_usage_mb"] for p in points]
                cpu_usage = [p["cpu_usage_percent"] for p in points]
                
                summary["functions"][function_name] = {
                    "call_count": len(points),
                    "avg_execution_time_ms": statistics.mean(execution_times),
                    "max_execution_time_ms": max(execution_times),
                    "avg_memory_usage_mb": statistics.mean(memory_usage),
                    "max_memory_usage_mb": max(memory_usage),
                    "avg_cpu_usage_percent": statistics.mean(cpu_usage),
                    "max_cpu_usage_percent": max(cpu_usage)
                }
            
            return summary


class AlertManager:
    """性能告警管理器"""
    
    def __init__(self):
        self._alert_thresholds = {
            "execution_time_ms": 1000,  # 1秒
            "memory_usage_mb": 500,     # 500MB
            "cpu_usage_percent": 80,    # 80%
            "error_rate": 0.1           # 10%
        }
        self._active_alerts: List[PerformanceAlert] = []
        self._alert_history: deque = deque(maxlen=1000)
        self._lock = threading.RLock()
    
    def set_threshold(self, metric: str, threshold: float) -> None:
        """设置告警阈值"""
        with self._lock:
            self._alert_thresholds[metric] = threshold
    
    def check_performance_alert(self, function_name: str, metrics: Dict[str, float]) -> List[PerformanceAlert]:
        """检查性能告警"""
        alerts = []
        
        with self._lock:
            for metric, value in metrics.items():
                threshold = self._alert_thresholds.get(metric)
                if threshold is None:
                    continue
                
                if value > threshold:
                    severity = self._calculate_severity(metric, value, threshold)
                    
                    alert = PerformanceAlert(
                        alert_type="performance_threshold",
                        severity=severity,
                        function_name=function_name,
                        message=f"{metric} ({value:.2f}) exceeded threshold ({threshold:.2f})",
                        threshold=threshold,
                        current_value=value,
                        timestamp=datetime.now()
                    )
                    
                    alerts.append(alert)
                    self._active_alerts.append(alert)
                    self._alert_history.append(alert)
        
        return alerts
    
    def _calculate_severity(self, metric: str, value: float, threshold: float) -> str:
        """计算告警严重程度"""
        ratio = value / threshold
        
        if ratio >= 3.0:
            return "CRITICAL"
        elif ratio >= 2.0:
            return "HIGH"
        elif ratio >= 1.5:
            return "MEDIUM"
        else:
            return "LOW"
    
    def get_active_alerts(self) -> List[PerformanceAlert]:
        """获取活跃告警"""
        with self._lock:
            # 清理过期告警（1小时前的）
            cutoff_time = datetime.now() - timedelta(hours=1)
            self._active_alerts = [
                alert for alert in self._active_alerts
                if alert.timestamp >= cutoff_time
            ]
            
            return self._active_alerts.copy()
    
    def get_alert_summary(self) -> Dict[str, Any]:
        """获取告警摘要"""
        with self._lock:
            active_alerts = self.get_active_alerts()
            
            severity_counts = defaultdict(int)
            function_counts = defaultdict(int)
            
            for alert in active_alerts:
                severity_counts[alert.severity] += 1
                function_counts[alert.function_name] += 1
            
            return {
                "total_active_alerts": len(active_alerts),
                "severity_breakdown": dict(severity_counts),
                "function_breakdown": dict(function_counts),
                "thresholds": self._alert_thresholds.copy()
            }


class PerformanceReporter:
    """性能报告生成器"""
    
    def __init__(self, output_dir: str = "logs/performance_reports"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        self.error_tracker = ErrorRateTracker()
        self.trend_analyzer = TrendAnalyzer()
        self.alert_manager = AlertManager()
        self._lock = threading.RLock()
    
    def record_performance(self, function_name: str, execution_time_ms: float,
                          memory_usage_mb: float, cpu_usage_percent: float,
                          error: Optional[Exception] = None) -> None:
        """记录性能数据"""
        with self._lock:
            # 记录到各个组件
            self.error_tracker.record_call(function_name, execution_time_ms, error)
            self.trend_analyzer.add_performance_data(
                function_name, execution_time_ms, memory_usage_mb, cpu_usage_percent
            )
            
            # 检查告警
            metrics = {
                "execution_time_ms": execution_time_ms,
                "memory_usage_mb": memory_usage_mb,
                "cpu_usage_percent": cpu_usage_percent
            }
            
            alerts = self.alert_manager.check_performance_alert(function_name, metrics)
            
            # 记录告警日志
            for alert in alerts:
                logger.warning(f"Performance alert: {alert.message}", **alert.to_dict())
    
    def generate_report(self, duration_minutes: int = 60) -> Dict[str, Any]:
        """生成性能报告"""
        with self._lock:
            report = {
                "report_timestamp": datetime.now().isoformat(),
                "duration_minutes": duration_minutes,
                "error_rates": [metrics.to_dict() for metrics in self.error_tracker.get_all_error_rates()],
                "high_error_functions": [metrics.to_dict() for metrics in self.error_tracker.get_high_error_rate_functions()],
                "performance_summary": self.trend_analyzer.get_performance_summary(duration_minutes),
                "alert_summary": self.alert_manager.get_alert_summary(),
                "active_alerts": [alert.to_dict() for alert in self.alert_manager.get_active_alerts()]
            }
            
            return report
    
    def save_report(self, report: Dict[str, Any], filename: Optional[str] = None) -> Path:
        """保存报告到文件"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"performance_report_{timestamp}.json"
        
        report_path = self.output_dir / filename
        
        try:
            with open(report_path, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Performance report saved: {report_path}")
            return report_path
            
        except Exception as e:
            logger.error(f"Failed to save performance report: {e}")
            raise
    
    def generate_and_save_report(self, duration_minutes: int = 60) -> Path:
        """生成并保存性能报告"""
        report = self.generate_report(duration_minutes)
        return self.save_report(report)
    
    def get_function_trend_analysis(self, function_name: str, 
                                   duration_minutes: int = 60) -> Dict[str, Any]:
        """获取特定函数的趋势分析"""
        metrics = ["execution_time_ms", "memory_usage_mb", "cpu_usage_percent"]
        
        analysis = {}
        for metric in metrics:
            analysis[metric] = self.trend_analyzer.analyze_trend(
                function_name, metric, duration_minutes
            )
        
        return {
            "function_name": function_name,
            "duration_minutes": duration_minutes,
            "trend_analysis": analysis,
            "error_metrics": self.error_tracker.get_error_rate(function_name).to_dict()
        }


# 全局性能报告器实例
_global_performance_reporter: Optional[PerformanceReporter] = None


def get_global_performance_reporter() -> PerformanceReporter:
    """获取全局性能报告器实例"""
    global _global_performance_reporter
    
    if _global_performance_reporter is None:
        _global_performance_reporter = PerformanceReporter()
    
    return _global_performance_reporter


# 模块级别的日志器
module_logger = logger.bind(name=__name__)