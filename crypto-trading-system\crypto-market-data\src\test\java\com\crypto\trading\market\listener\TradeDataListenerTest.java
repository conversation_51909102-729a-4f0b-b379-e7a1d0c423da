package com.crypto.trading.market.listener;

import com.crypto.trading.market.processor.TradeDataProcessor;
import com.crypto.trading.sdk.websocket.BinanceWebSocketClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.List;
import java.util.function.Consumer;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * TradeDataListener单元测试
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class TradeDataListenerTest {

    @Mock
    private BinanceWebSocketClient webSocketClient;

    @Mock
    private TradeDataProcessor tradeDataProcessor;

    @InjectMocks
    private TradeDataListener tradeDataListener;

    private List<String> symbols;

    @BeforeEach
    void setUp() {
        // 在每次测试前重置所有mock
        clearInvocations(webSocketClient, tradeDataProcessor);
        
        symbols = Arrays.asList("BTCUSDT", "ETHUSDT");
        ReflectionTestUtils.setField(tradeDataListener, "symbols", symbols);
        
        // 使用lenient和doAnswer处理大小写不敏感的情况
        lenient().doAnswer(invocation -> {
            String symbol = invocation.getArgument(0);
            if ("BTCUSDT".equalsIgnoreCase(symbol)) return 1;
            if ("ETHUSDT".equalsIgnoreCase(symbol)) return 2;
            return 0;
        }).when(webSocketClient).subscribeAggTrade(anyString(), any(Consumer.class));
    }

    @Test
    void testStartTradeDataSubscription() {
        // 调用被测试方法
        tradeDataListener.startTradeDataSubscription();

        // 验证每个符号都订阅了，使用anyString()匹配
        verify(webSocketClient, times(2)).subscribeAggTrade(anyString(), any(Consumer.class));
    }

    @Test
    void testStopTradeDataSubscription() {
        // 首先启动订阅
        tradeDataListener.startTradeDataSubscription();
        
        // 清除之前的调用记录，只关注stopTradeDataSubscription的效果
        clearInvocations(webSocketClient);

        // 调用被测试方法
        tradeDataListener.stopTradeDataSubscription();

        // 验证每个连接都被关闭
        verify(webSocketClient).closeConnection(1);
        verify(webSocketClient).closeConnection(2);
    }

    @Test
    void testTradeMessageHandler() {
        // 捕获订阅时传递的消息处理器
        ArgumentCaptor<Consumer<String>> handlerCaptor = ArgumentCaptor.forClass(Consumer.class);
        
        // 通过调用startTradeDataSubscription来获取Handler
        tradeDataListener.startTradeDataSubscription();
        
        // 验证方法调用并捕获参数
        verify(webSocketClient, times(2)).subscribeAggTrade(anyString(), handlerCaptor.capture());
        
        // 获取第一个捕获的Consumer（BTCUSDT的Handler）
        Consumer<String> handler = handlerCaptor.getAllValues().get(0);
        
        // 测试有效的交易消息
        String validMessage = "{\"e\":\"aggTrade\",\"E\":1625128665000,\"s\":\"BTCUSDT\",\"a\":123456789,\"p\":\"35000.00\",\"q\":\"0.5\",\"T\":1625128665000,\"m\":false}";
        
        // 执行处理器
        handler.accept(validMessage);
        
        // 验证处理器被调用
        verify(tradeDataProcessor, times(1)).processTradeData(eq(validMessage));
        
        // 清除之前的调用记录
        clearInvocations(tradeDataProcessor);
        
        // 测试无效的消息类型
        String invalidTypeMessage = "{\"e\":\"kline\",\"E\":1625128665000,\"s\":\"BTCUSDT\"}";
        
        // 执行处理器，不应该抛出异常
        try {
            handler.accept(invalidTypeMessage);
        } catch (Exception e) {
            fail("处理器不应该抛出异常: " + e.getMessage());
        }
        
        // 验证处理器没有被调用（因为消息类型不匹配）
        verify(tradeDataProcessor, never()).processTradeData(any(String.class));
        
        // 测试格式错误的消息
        String malformedMessage = "not a json message";
        
        // 执行处理器，不应该抛出异常
        try {
            handler.accept(malformedMessage);
        } catch (Exception e) {
            fail("处理器不应该抛出异常: " + e.getMessage());
        }
        
        // 验证处理器没有被调用（因为格式错误）
        verify(tradeDataProcessor, never()).processTradeData(any(String.class));
    }

    @Test
    void testNestedMessageFormat() {
        // 捕获订阅时传递的消息处理器
        ArgumentCaptor<Consumer<String>> handlerCaptor = ArgumentCaptor.forClass(Consumer.class);

        // 通过调用startTradeDataSubscription来获取Handler
        tradeDataListener.startTradeDataSubscription();

        // 验证方法调用并捕获参数
        verify(webSocketClient, times(2)).subscribeAggTrade(anyString(), handlerCaptor.capture());

        // 获取第一个捕获的Consumer
        Consumer<String> handler = handlerCaptor.getAllValues().get(0);

        // 测试嵌套格式的WebSocket消息（实际币安WebSocket格式）
        String nestedMessage = """
            {
              "stream" : "ethusdt@aggTrade",
              "data" : {
                "e" : "aggTrade",
                "E" : 1750514562285,
                "a" : 111236374,
                "s" : "ETHUSDT",
                "p" : "2418.79",
                "q" : "0.133",
                "f" : 171164350,
                "l" : 171164350,
                "T" : 1750514562131,
                "m" : true
              }
            }
            """;

        // 执行处理器
        handler.accept(nestedMessage);

        // 验证处理器被调用
        verify(tradeDataProcessor, times(1)).processTradeData(eq(nestedMessage));
    }

    @Test
    void testNestedMessageWithInvalidType() {
        // 捕获订阅时传递的消息处理器
        ArgumentCaptor<Consumer<String>> handlerCaptor = ArgumentCaptor.forClass(Consumer.class);

        // 通过调用startTradeDataSubscription来获取Handler
        tradeDataListener.startTradeDataSubscription();

        // 验证方法调用并捕获参数
        verify(webSocketClient, times(2)).subscribeAggTrade(anyString(), handlerCaptor.capture());

        // 获取第一个捕获的Consumer
        Consumer<String> handler = handlerCaptor.getAllValues().get(0);

        // 测试嵌套格式但消息类型错误的WebSocket消息
        String nestedInvalidMessage = """
            {
              "stream" : "ethusdt@depth",
              "data" : {
                "e" : "depthUpdate",
                "E" : 1750514562285,
                "s" : "ETHUSDT",
                "u" : 123456789
              }
            }
            """;

        // 执行处理器
        handler.accept(nestedInvalidMessage);

        // 验证处理器没有被调用（因为消息类型不匹配）
        verify(tradeDataProcessor, never()).processTradeData(any(String.class));
    }
}