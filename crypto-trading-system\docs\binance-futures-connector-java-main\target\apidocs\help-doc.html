<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (21) on Fri Apr 04 16:06:08 CST 2025 -->
<title>API 帮助 (io.github.binance:binance-futures-connector-java 3.0.5 API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-04-04">
<meta name="description" content="help">
<meta name="generator" content="javadoc/HelpWriter">
<link rel="stylesheet" type="text/css" href="stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="script.js"></script>
<script type="text/javascript" src="script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="script-dir/jquery-ui.min.js"></script>
</head>
<body class="help-page">
<script type="text/javascript">var pathtoroot = "./";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="切换导航链接"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="index.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="overview-tree.html">树</a></li>
<li><a href="index-all.html">索引</a></li>
<li class="nav-bar-cell1-rev">帮助</li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>帮助：</p>
<ul>
<li><a href="#help-navigation">导航</a></li>
<li><a href="#help-pages">页</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>帮助：&nbsp;</li>
<li><a href="#help-navigation">导航</a>&nbsp;|&nbsp;</li>
<li><a href="#help-pages">页</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="搜索">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<h1 class="title">JavaDoc 帮助</h1>
<ul class="help-toc">
<li><a href="#help-navigation">导航</a>: 
<ul class="help-subtoc">
<li><a href="#search">搜索</a></li>
</ul>
</li>
<li><a href="#help-pages">页面类型</a>: 
<ul class="help-subtoc">
<li><a href="#overview">概览</a></li>
<li><a href="#package">程序包</a></li>
<li><a href="#class">类或接口</a></li>
<li><a href="#doc-file">其他文件</a></li>
<li><a href="#use">使用</a></li>
<li><a href="#tree">树 (类分层结构)</a></li>
<li><a href="#all-packages">所有程序包</a></li>
<li><a href="#all-classes">所有类和接口</a></li>
<li><a href="#index">索引</a></li>
</ul>
</li>
</ul>
<hr>
<div class="sub-title">
<h2 id="help-navigation">导航</h2>
从 <a href="index.html">概览</a> 页开始，您可以使用每页中的链接以及每页顶部导航栏中的链接来浏览文档。 使用 <a href="index-all.html">索引</a> 和搜索框可以导航到特定声明和概要页，包括：<a href="allpackages-index.html">所有程序包</a>, <a href="allclasses-index.html">所有类和接口</a>
<section class="help-section" id="search">
<h3>搜索</h3>
<p>可以搜索模块、程序包、类型、字段、方法、系统属性以及 API 中定义的其他术语的定义。可以使用部分或完整名称搜索这些项，（可选）也可以使用“驼峰大小写式”缩写，或使用空格分隔的多个搜索词进行搜索。一些示例：</p>
<ul class="help-section-list">
<li><code>"j.l.obj"</code> 匹配 "java.lang.Object"</li>
<li><code>"InpStr"</code> 匹配 "java.io.InputStream"</li>
<li><code>"math exact long"</code> 匹配 "java.lang.Math.absExact(long)"</li>
</ul>
<p>有关搜索功能的完整说明，请参阅 <a href="https://docs.oracle.com/en/java/javase/21/docs/specs/javadoc/javadoc-search-spec.html">Javadoc 搜索规范</a>。</p>
</section>
</div>
<hr>
<div class="sub-title">
<h2 id="help-pages">页面类型</h2>
以下各部分介绍了此集合中不同类型的页面。
<section class="help-section" id="overview">
<h3>概览</h3>
<p><a href="index.html">概览</a> 页面是此 API 文档的首页, 提供了所有程序包的列表及其概要。此页面也可能包含这些程序包的总体说明。</p>
</section>
<section class="help-section" id="package">
<h3>程序包</h3>
<p>每个程序包都有一页，其中包含它的类和接口的列表及其概要。这些页可以包含以下类别：</p>
<ul class="help-section-list">
<li>接口</li>
<li>类</li>
<li>枚举类</li>
<li>异常错误类</li>
<li>批注接口</li>
</ul>
</section>
<section class="help-section" id="class">
<h3>类或接口</h3>
<p>每个类、接口、嵌套类和嵌套接口都有自己独立的页面。如果这些部分中每个部分的条目为空或不适用，则省略这些条目。</p>
<ul class="help-section-list">
<li>类继承图</li>
<li>直接子类</li>
<li>所有已知子接口</li>
<li>所有已知实现类</li>
<li>类或接口声明</li>
<li>类或接口说明</li>
</ul>
<br>
<ul class="help-section-list">
<li>嵌套类概要</li>
<li>枚举常量概要</li>
<li>字段概要</li>
<li>属性概要</li>
<li>构造器概要</li>
<li>方法概要</li>
<li>必需元素概要</li>
<li>可选元素概要</li>
</ul>
<br>
<ul class="help-section-list">
<li>枚举常量详细资料</li>
<li>字段详细资料</li>
<li>属性详细资料</li>
<li>构造器详细资料</li>
<li>方法详细资料</li>
<li>元素详细资料</li>
</ul>
<p><span class="help-note">注:</span> 批注接口有必需的元素和可选的元素，但没有方法。 只有枚举类有枚举常量。 记录类的组件显示为记录类声明的一部分。 属性是 JavaFX 的一个特性。</p>
<p>概要条目按字母顺序排列，而详细说明则按其在源代码中出现的顺序排列。这有助于保持程序员所建立的逻辑分组。</p>
</section>
<section class="help-section" id="doc-file">
<h3>其他文件</h3>
<p>程序包和模块所包含的页面中可能带有与附近声明相关的附加信息。</p>
</section>
<section class="help-section" id="use">
<h3>使用</h3>
<p>每个已文档化的程序包、类和接口都有各自的“使用”页面。此页面介绍了使用给定类或程序包的任何部分的程序包、类、方法、构造器和字段。对于给定的类或接口 A，其“使用”页面包含 A 的子类、声明为 A 的字段、返回 A 的方法，以及带有类型为 A 的参数的方法和构造器。访问此页面的方法是：首先转至程序包、类或接口，然后单击导航栏中的“使用”链接。</p>
</section>
<section class="help-section" id="tree">
<h3>树 (类分层结构)</h3>
<p>对于所有程序包，都有一个 <a href="overview-tree.html">类分层结构</a> 页，以及每个程序包的分层结构。每个分层结构页都包含类的列表和接口的列表。从 <code>java.lang.Object</code> 开始，按继承结构对类进行排列。接口不从 <code>java.lang.Object</code> 继承。</p>
<ul class="help-section-list">
<li>查看“概览”页面时，单击“树”将显示所有程序包的分层结构。</li>
<li>查看特定程序包、类或接口页时，单击“树”将仅显示该程序包的分层结构。</li>
</ul>
</section>
<section class="help-section" id="all-packages">
<h3>所有程序包</h3>
<p><a href="allpackages-index.html">所有程序包</a> 包含文档中所有程序包的按字母顺序排列的索引。</p>
</section>
<section class="help-section" id="all-classes">
<h3>所有类和接口</h3>
<p><a href="allclasses-index.html">所有类和接口</a> 包含文档中所有类和接口（包括批注接口、枚举类和记录类）的按字母顺序排列的索引。</p>
</section>
<section class="help-section" id="index">
<h3>索引</h3>
<p><a href="index-all.html">索引</a> 包含文档中所有类、接口、构造器、方法和字段的按字母顺序排列的索引，以及概要页（例如 <a href="allpackages-index.html">所有程序包</a>, <a href="allclasses-index.html">所有类和接口</a>）。</p>
</section>
</div>
<hr>
<span class="help-footnote">此帮助文件适用于由标准 doclet 生成的 API 文档。</span></main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &#169; 2025. All rights reserved.</small></p>
</footer>
</div>
</div>
</body>
</html>
