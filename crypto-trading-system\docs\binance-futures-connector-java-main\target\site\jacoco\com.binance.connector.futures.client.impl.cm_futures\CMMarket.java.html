<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>CMMarket.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">io.github.binance:binance-futures-connector-java</a> &gt; <a href="index.source.html" class="el_package">com.binance.connector.futures.client.impl.cm_futures</a> &gt; <span class="el_source">CMMarket.java</span></div><h1>CMMarket.java</h1><pre class="source lang-java linenums">package com.binance.connector.futures.client.impl.cm_futures;

import com.binance.connector.futures.client.enums.HttpMethod;
import com.binance.connector.futures.client.utils.ParameterChecker;
import java.util.LinkedHashMap;
import com.binance.connector.futures.client.impl.futures.Market;
import com.binance.connector.futures.client.utils.ProxyAuth;

/**
 * &lt;h2&gt;Coin-Margined Market Endpoints&lt;/h2&gt;
 * All endpoints under the
 * &lt;a href=&quot;https://developers.binance.com/docs/derivatives/coin-margined-futures/general-info&quot;&gt;Market Data Endpoint&lt;/a&gt;
 * section of the API documentation will be implemented in this class.
 * &lt;br&gt;
 * Response will be returned in &lt;i&gt;String format&lt;/i&gt;.
 */
public class CMMarket extends Market {
    public CMMarket(String productUrl, String baseUrl, String apiKey, boolean showLimitUsage, ProxyAuth proxy) {
<span class="fc" id="L19">        super(productUrl, baseUrl, apiKey, showLimitUsage, proxy);</span>
<span class="fc" id="L20">    }</span>

    /**
     * Mark Price and Funding Rate
     * &lt;br&gt;&lt;br&gt;
     * GET /v1/premiumIndex
     * &lt;br&gt;
     * @param
     * parameters LinkedHashedMap of String,Object pair
     *            where String is the name of the parameter and Object is the value of the parameter
     * &lt;br&gt;&lt;br&gt;
     * symbol -- optional/string -- the trading symbol &lt;br&gt;
     * pair -- optional/string -- the trading pair &lt;br&gt;
     * @return String
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/coin-margined-futures/market-data/Index-Price-and-Mark-Price&quot;&gt;
     *     https://developers.binance.com/docs/derivatives/coin-margined-futures/market-data/Index-Price-and-Mark-Price&lt;/a&gt;
     */
    public String markPrice(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L38">        return super.markPrice(parameters);</span>
    }

    /**
     * 24 hour rolling window price change statistics. Careful when accessing this with no symbol.
     * &lt;br&gt;&lt;br&gt;
     * GET /v1/ticker/24hr
     * &lt;br&gt;
     * @param
     * parameters LinkedHashedMap of String,Object pair
     *            where String is the name of the parameter and Object is the value of the parameter
     * &lt;br&gt;&lt;br&gt;
     * pair -- optional/string -- the trading pair &lt;br&gt;
     * @return String
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/coin-margined-futures/market-data/24hr-Ticker-Price-Change-Statistics&quot;&gt;
     *     https://developers.binance.com/docs/derivatives/coin-margined-futures/market-data/24hr-Ticker-Price-Change-Statistics&lt;/a&gt;
     */
    public String ticker24H(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L56">        return super.ticker24H(parameters);</span>
    }

    /**
     * Latest price for a symbol or symbols.
     * &lt;br&gt;&lt;br&gt;
     * GET /v1/ticker/price
     * &lt;br&gt;
     * https://developers.binance.com/docs/derivatives/coin-margined-futures/market-data/Symbol-Price-Ticker
     * @param
     * parameters LinkedHashedMap of String,Object pair
     *            where String is the name of the parameter and Object is the value of the parameter
     * &lt;br&gt;&lt;br&gt;
     * pair -- optional/string -- the trading pair &lt;br&gt;
     * @return String
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/coin-margined-futures/market-data/Symbol-Price-Ticker&quot;&gt;
     *     https://developers.binance.com/docs/derivatives/coin-margined-futures/market-data/Symbol-Price-Ticker&lt;/a&gt;
     */
    public String tickerSymbol(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L75">        return super.tickerSymbol(parameters);</span>
    }

    /**
     * Best price/qty on the order book for a symbol or symbols.
     * &lt;br&gt;&lt;br&gt;
     * GET /v1/ticker/bookTicker
     * &lt;br&gt;
     * https://developers.binance.com/docs/derivatives/coin-margined-futures/market-data/Symbol-Order-Book-Ticker
     * @param
     * parameters LinkedHashedMap of String,Object pair
     *            where String is the name of the parameter and Object is the value of the parameter
     * &lt;br&gt;&lt;br&gt;
     * pair -- optional/string -- the trading pair (Only applicable in COIN-M Futures) &lt;br&gt;
     * @return String
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/coin-margined-futures/market-data/Symbol-Order-Book-Ticker&quot;&gt;
     *     https://developers.binance.com/docs/derivatives/coin-margined-futures/market-data/Symbol-Order-Book-Ticker&lt;/a&gt;
     */
    public String bookTicker(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L94">        return super.bookTicker(parameters);</span>
    }

    /**
     * Open Interest History
     * &lt;br&gt;&lt;br&gt;
     * GET /futures/data/openInterestHist
     * &lt;br&gt;
     * https://developers.binance.com/docs/derivatives/coin-margined-futures/market-data/Open-Interest
     * @param
     * parameters LinkedHashedMap of String,Object pair
     *            where String is the name of the parameter and Object is the value of the parameter
     * &lt;br&gt;&lt;br&gt;
     * pair -- mandatory/string -- the trading pair &lt;br&gt;
     * period -- mandatory/enum -- &quot;5m&quot;,&quot;15m&quot;,&quot;30m&quot;,&quot;1h&quot;,&quot;2h&quot;,&quot;4h&quot;,&quot;6h&quot;,&quot;12h&quot;,&quot;1d&quot; &lt;br&gt;
     * limit -- optional/long -- default 30, max 500 &lt;br&gt;
     * startTime -- optional/long -- Start Time &lt;br&gt;
     * endTime -- optional/long -- End Time &lt;br&gt;
     * @return String
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/coin-margined-futures/market-data/Open-Interest&quot;&gt;
     *     https://developers.binance.com/docs/derivatives/coin-margined-futures/market-data/Open-Interest&lt;/a&gt;
     */
    public String openInterestStatistics(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L117">        ParameterChecker.checkParameter(parameters, &quot;pair&quot;, String.class);</span>
<span class="fc" id="L118">        ParameterChecker.checkParameter(parameters, &quot;period&quot;, String.class);</span>
<span class="fc" id="L119">        return super.openInterestStatistics(parameters);</span>
    }

    /**
     * Top Trader Long/Short Ratio (Positions)
     * &lt;br&gt;&lt;br&gt;
     * GET /futures/data/topLongShortPositionRatio
     * &lt;br&gt;
     * https://developers.binance.com/docs/derivatives/coin-margined-futures/market-data/Top-Trader-Long-Short-Ratio
     * @param
     * parameters LinkedHashedMap of String,Object pair
     *            where String is the name of the parameter and Object is the value of the parameter
     * &lt;br&gt;&lt;br&gt;
     * pair -- mandatory/string -- the trading pair &lt;br&gt;
     * period -- mandatory/enum -- &quot;5m&quot;,&quot;15m&quot;,&quot;30m&quot;,&quot;1h&quot;,&quot;2h&quot;,&quot;4h&quot;,&quot;6h&quot;,&quot;12h&quot;,&quot;1d&quot; &lt;br&gt;
     * limit -- optional/long -- default 30, max 500 &lt;br&gt;
     * startTime -- optional/long -- Start Time &lt;br&gt;
     * endTime -- optional/long -- End Time &lt;br&gt;
     * @return String
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/coin-margined-futures/market-data/Top-Trader-Long-Short-Ratio&quot;&gt;
     *     https://developers.binance.com/docs/derivatives/coin-margined-futures/market-data/Top-Trader-Long-Short-Ratio&lt;/a&gt;
     */
    public String topTraderLongShortPos(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L142">        ParameterChecker.checkParameter(parameters, &quot;pair&quot;, String.class);</span>
<span class="fc" id="L143">        ParameterChecker.checkParameter(parameters, &quot;period&quot;, String.class);</span>
<span class="fc" id="L144">        return super.topTraderLongShortPos(parameters);</span>
    }

    /**
     * Top Trader Long/Short Ratio (Accounts)
     * &lt;br&gt;&lt;br&gt;
     * GET /futures/data/topLongShortAccountRatio
     * &lt;br&gt;
     * https://developers.binance.com/docs/derivatives/coin-margined-futures/market-data/Top-Long-Short-Account-Ratio
     * @param
     * parameters LinkedHashedMap of String,Object pair
     *            where String is the name of the parameter and Object is the value of the parameter
     * &lt;br&gt;&lt;br&gt;
     * pair -- mandatory/string -- the trading pair &lt;br&gt;
     * period -- mandatory/enum -- &quot;5m&quot;,&quot;15m&quot;,&quot;30m&quot;,&quot;1h&quot;,&quot;2h&quot;,&quot;4h&quot;,&quot;6h&quot;,&quot;12h&quot;,&quot;1d&quot; &lt;br&gt;
     * limit -- optional/long -- default 30, max 500 &lt;br&gt;
     * startTime -- optional/long -- Start Time &lt;br&gt;
     * endTime -- optional/long -- End Time &lt;br&gt;
     * @return String
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/coin-margined-futures/market-data/Top-Long-Short-Account-Ratio&quot;&gt;
     *     https://developers.binance.com/docs/derivatives/coin-margined-futures/market-data/Top-Long-Short-Account-Ratio&lt;/a&gt;
     */
    public String topTraderLongShortAccs(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L167">        ParameterChecker.checkParameter(parameters, &quot;pair&quot;, String.class);</span>
<span class="fc" id="L168">        ParameterChecker.checkParameter(parameters, &quot;period&quot;, String.class);</span>
<span class="fc" id="L169">        return super.topTraderLongShortAccs(parameters);</span>
    }

    /**
     * Long/Short Ratio
     * &lt;br&gt;&lt;br&gt;
     * GET /futures/data/globalLongShortAccountRatio
     * &lt;br&gt;
     * https://developers.binance.com/docs/derivatives/coin-margined-futures/market-data/Long-Short-Ratio
     * @param
     * parameters LinkedHashedMap of String,Object pair
     *            where String is the name of the parameter and Object is the value of the parameter
     * &lt;br&gt;&lt;br&gt;
     * pair -- mandatory/string -- the trading pair &lt;br&gt;
     * period -- mandatory/enum -- &quot;5m&quot;,&quot;15m&quot;,&quot;30m&quot;,&quot;1h&quot;,&quot;2h&quot;,&quot;4h&quot;,&quot;6h&quot;,&quot;12h&quot;,&quot;1d&quot; &lt;br&gt;
     * limit -- optional/long -- default 30, max 500 &lt;br&gt;
     * startTime -- optional/long -- Start Time &lt;br&gt;
     * endTime -- optional/long -- End Time &lt;br&gt;
     * @return String
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/coin-margined-futures/market-data/Long-Short-Ratio&quot;&gt;
     *     https://developers.binance.com/docs/derivatives/coin-margined-futures/market-data/Long-Short-Ratio&lt;/a&gt;
     */
    public String longShortRatio(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L192">        ParameterChecker.checkParameter(parameters, &quot;pair&quot;, String.class);</span>
<span class="fc" id="L193">        ParameterChecker.checkParameter(parameters, &quot;period&quot;, String.class);</span>
<span class="fc" id="L194">        return super.longShortRatio(parameters);</span>
    }

<span class="fc" id="L197">    private final String BASIS = &quot;/futures/data/basis&quot;;</span>
    /**
     * For COIN-M Futures Only
     * &lt;br&gt;&lt;br&gt;
     * GET /futures/data/basis
     * &lt;br&gt;
     * https://developers.binance.com/docs/derivatives/coin-margined-futures/market-data/Basis
     * @param
     * parameters LinkedHashedMap of String,Object pair
     *            where String is the name of the parameter and Object is the value of the parameter
     * &lt;br&gt;&lt;br&gt;
     * pair -- mandatory/string -- the trading pair &lt;br&gt;
     * contractType -- mandatory/enum -- CURRENT_QUARTER, NEXT_QUARTER, PERPETUAL
     * period -- mandatory/enum -- &quot;5m&quot;,&quot;15m&quot;,&quot;30m&quot;,&quot;1h&quot;,&quot;2h&quot;,&quot;4h&quot;,&quot;6h&quot;,&quot;12h&quot;,&quot;1d&quot;
     * limit -- optional/long -- Default 30,Max 500
     * startTime -- optional/long
     * endTime -- optional/long
     * @return String
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/coin-margined-futures/market-data/Basis&quot;&gt;
     *     https://developers.binance.com/docs/derivatives/coin-margined-futures/market-data/Basis&lt;/a&gt;
     */
    public String basis(LinkedHashMap&lt;String, Object&gt; parameters) {
<span class="fc" id="L219">        ParameterChecker.checkParameter(parameters, &quot;pair&quot;, String.class);</span>
<span class="fc" id="L220">        ParameterChecker.checkParameter(parameters, &quot;contractType&quot;, String.class);</span>
<span class="fc" id="L221">        ParameterChecker.checkParameter(parameters, &quot;period&quot;, String.class);</span>
<span class="fc" id="L222">        return getRequestHandler().sendPublicRequest(getBaseUrl(), BASIS, parameters, HttpMethod.GET, getShowLimitUsage());</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.7.202105040129</span></div></body></html>