package com.crypto.trading.trade.executor;

import com.crypto.trading.common.dto.trade.OrderDTO;
import com.crypto.trading.common.enums.OrderSide;
import com.crypto.trading.common.enums.OrderStatus;
import com.crypto.trading.common.enums.OrderType;
import com.crypto.trading.common.exception.BusinessException;
import com.crypto.trading.common.exception.ErrorCode;
import com.crypto.trading.sdk.client.BinanceApiClient;
import com.crypto.trading.sdk.exception.ApiException;
import com.crypto.trading.trade.config.TradeExecutionConfig;
import com.crypto.trading.trade.model.entity.order.OrderEntity;
import com.crypto.trading.trade.repository.mapper.order.OrderExecutionLogMapper;
import com.crypto.trading.trade.service.TradeExecutionService;
import com.crypto.trading.trade.service.order.OrderManagementService;
import io.github.resilience4j.ratelimiter.RateLimiter;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 币安交易执行器测试类
 * <p>
 * 测试币安交易执行器的功能
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class BinanceTradeExecutorTest {

    @Mock
    private BinanceApiClient binanceApiClient;

    @Mock
    private TradeExecutionService tradeExecutionService;

    @Mock
    private OrderManagementService orderManagementService;

    @Mock
    private OrderExecutionLogMapper orderExecutionLogMapper;

    @Mock
    private TradeExecutionConfig tradeExecutionConfig;

    @Mock
    private RateLimiter orderRateLimiter;

    @InjectMocks
    private BinanceTradeExecutor binanceTradeExecutor;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.openMocks(this);
    }

    /**
     * 测试执行市价单 - 成功
     */
    @Test
    public void testExecuteMarketOrder_Success() {
        // 准备测试数据
        OrderEntity order = createTestOrderEntity();
        Map<String, Object> orderStatus = new HashMap<>();
        orderStatus.put("symbol", "BTCUSDT");
        orderStatus.put("orderId", "123456");
        orderStatus.put("clientOrderId", "test123");
        orderStatus.put("status", "NEW");

        // 模拟依赖服务的行为
        when(tradeExecutionConfig.isDryRun()).thenReturn(false);
        when(orderRateLimiter.acquirePermission()).thenReturn(true);
        when(binanceApiClient.placeMarketOrder(anyString(), anyString(), anyString(), any(BigDecimal.class), anyString()))
                .thenReturn("123456");
        when(binanceApiClient.queryOrder(anyString(), anyString(), isNull()))
                .thenReturn(orderStatus);
        when(tradeExecutionService.processOrderExecutionResult(any(OrderDTO.class)))
                .thenReturn(CompletableFuture.completedFuture(null));

        // 执行测试
        OrderEntity result = binanceTradeExecutor.executeMarketOrder(order).join();

        // 验证结果
        assertNotNull(result);
        assertEquals("123456", result.getOrderId());
        assertEquals(OrderStatus.NEW.getCode(), result.getStatus());

        // 验证依赖服务的调用
        verify(binanceApiClient, times(1)).placeMarketOrder(anyString(), anyString(), anyString(), any(BigDecimal.class), anyString());
        verify(binanceApiClient, times(1)).queryOrder(anyString(), anyString(), isNull());
        verify(orderManagementService, times(1)).updateOrderStatus(anyString(), anyString(), anyString(), anyString());
        verify(tradeExecutionService, times(1)).processOrderExecutionResult(any(OrderDTO.class));
    }

    /**
     * 测试执行市价单 - API异常
     */
    @Test
    public void testExecuteMarketOrder_ApiException() {
        // 准备测试数据
        OrderEntity order = createTestOrderEntity();

        // 模拟依赖服务的行为
        when(tradeExecutionConfig.isDryRun()).thenReturn(false);
        when(orderRateLimiter.acquirePermission()).thenReturn(true);
        when(binanceApiClient.placeMarketOrder(anyString(), anyString(), anyString(), any(BigDecimal.class), anyString()))
                .thenThrow(new ApiException(400, "Invalid parameter"));
        when(tradeExecutionService.processOrderExecutionError(anyString(), anyString(), anyInt(), anyString()))
                .thenReturn(CompletableFuture.completedFuture(null));

        // 执行测试并验证异常
        Exception exception = assertThrows(RuntimeException.class, () -> {
            try {
                binanceTradeExecutor.executeMarketOrder(order).join();
            } catch (Exception e) {
                // 异常可能被CompletableFuture包装，需要获取原始异常
                if (e.getCause() instanceof BusinessException) {
                    BusinessException be = (BusinessException) e.getCause();
                    assertEquals(String.valueOf(ErrorCode.BINANCE_API_ERROR.getCode()), be.getErrorCode());
                }
                throw e;
            }
        });

        // 验证依赖服务的调用
        verify(binanceApiClient, times(1)).placeMarketOrder(anyString(), anyString(), anyString(), any(BigDecimal.class), anyString());
        verify(orderManagementService, times(1)).updateOrderStatus(anyString(), eq(OrderStatus.REJECTED.getCode()), anyString(), anyString());
        verify(tradeExecutionService, times(1)).processOrderExecutionError(anyString(), anyString(), anyInt(), anyString());
    }

    /**
     * 测试执行市价单 - 试运行模式
     */
    @Test
    public void testExecuteMarketOrder_DryRun() {
        // 准备测试数据
        OrderEntity order = createTestOrderEntity();

        // 模拟依赖服务的行为
        when(tradeExecutionConfig.isDryRun()).thenReturn(true);
        when(tradeExecutionService.processOrderExecutionResult(any(OrderDTO.class)))
                .thenReturn(CompletableFuture.completedFuture(null));

        // 执行测试
        OrderEntity result = binanceTradeExecutor.executeMarketOrder(order).join();

        // 验证结果
        assertNotNull(result);
        assertTrue(result.getOrderId().startsWith("SIM"));
        assertEquals(OrderStatus.FILLED.getCode(), result.getStatus());
        assertEquals(1.0, result.getExecutedQuantity());
        assertNotNull(result.getExecutedPrice());

        // 验证依赖服务的调用
        verify(binanceApiClient, never()).placeMarketOrder(anyString(), anyString(), anyString(), any(BigDecimal.class), anyString());
        verify(orderManagementService, times(1)).updateOrderStatus(anyString(), eq(OrderStatus.FILLED.getCode()), anyString(), isNull());
        verify(tradeExecutionService, times(1)).processOrderExecutionResult(any(OrderDTO.class));
    }

    /**
     * 测试查询订单状态
     */
    @Test
    public void testQueryOrderStatus() {
        // 准备测试数据
        String symbol = "BTCUSDT";
        String orderId = "123456";
        String clientOrderId = "test123";
        Map<String, Object> orderStatus = new HashMap<>();
        orderStatus.put("symbol", symbol);
        orderStatus.put("orderId", orderId);
        orderStatus.put("clientOrderId", clientOrderId);
        orderStatus.put("status", "FILLED");

        // 模拟依赖服务的行为
        when(tradeExecutionConfig.isDryRun()).thenReturn(false);
        when(binanceApiClient.queryOrder(eq(symbol), eq(orderId), eq(clientOrderId)))
                .thenReturn(orderStatus);

        // 执行测试
        Map<String, Object> result = binanceTradeExecutor.queryOrderStatus(symbol, orderId, clientOrderId).join();

        // 验证结果
        assertNotNull(result);
        assertEquals(symbol, result.get("symbol"));
        assertEquals(orderId, result.get("orderId"));
        assertEquals(clientOrderId, result.get("clientOrderId"));
        assertEquals("FILLED", result.get("status"));

        // 验证依赖服务的调用
        verify(binanceApiClient, times(1)).queryOrder(eq(symbol), eq(orderId), eq(clientOrderId));
    }

    /**
     * 测试查询订单状态 - 试运行模式
     */
    @Test
    public void testQueryOrderStatus_DryRun() {
        // 准备测试数据
        String symbol = "BTCUSDT";
        String orderId = "123456";
        String clientOrderId = "test123";

        // 模拟依赖服务的行为
        when(tradeExecutionConfig.isDryRun()).thenReturn(true);

        // 执行测试
        Map<String, Object> result = binanceTradeExecutor.queryOrderStatus(symbol, orderId, clientOrderId).join();

        // 验证结果
        assertNotNull(result);
        assertEquals(symbol, result.get("symbol"));
        assertEquals(orderId, result.get("orderId"));
        assertEquals(clientOrderId, result.get("clientOrderId"));
        assertEquals("FILLED", result.get("status"));

        // 验证依赖服务的调用
        verify(binanceApiClient, never()).queryOrder(anyString(), anyString(), anyString());
    }

    /**
     * 测试取消订单
     */
    @Test
    public void testCancelOrder() {
        // 准备测试数据
        OrderEntity order = createTestOrderEntity();

        // 模拟依赖服务的行为
        when(tradeExecutionConfig.isDryRun()).thenReturn(false);
        when(tradeExecutionService.processOrderExecutionResult(any(OrderDTO.class)))
                .thenReturn(CompletableFuture.completedFuture(null));

        // 执行测试
        OrderEntity result = binanceTradeExecutor.cancelOrder(order).join();

        // 验证结果
        assertNotNull(result);
        assertEquals(OrderStatus.CANCELED.getCode(), result.getStatus());

        // 验证依赖服务的调用
        verify(orderManagementService, times(1)).updateOrderStatus(anyString(), eq(OrderStatus.CANCELED.getCode()), anyString(), isNull());
        verify(tradeExecutionService, times(1)).processOrderExecutionResult(any(OrderDTO.class));
    }

    /**
     * 创建测试用的OrderEntity
     *
     * @return OrderEntity实例
     */
    private OrderEntity createTestOrderEntity() {
        OrderEntity orderEntity = new OrderEntity();
        orderEntity.setId(1L);
        orderEntity.setOrderId("123456");
        orderEntity.setClientOrderId("test123");
        orderEntity.setSymbol("BTCUSDT");
        orderEntity.setSide(OrderSide.BUY.getCode());
        orderEntity.setPositionSide("BOTH");
        orderEntity.setType(OrderType.MARKET.getCode());
        orderEntity.setQuantity(1.0);
        orderEntity.setPrice(50000.0);
        orderEntity.setStatus(OrderStatus.NEW.getCode());
        orderEntity.setCreatedTime(System.currentTimeMillis());
        return orderEntity;
    }
}