#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
在线学习器模块

实现增量学习和模型在线更新功能。
支持从实时市场数据中持续学习，动态调整模型参数。
"""

import os
import pickle
import json
from typing import Dict, List, Optional, Any, Union, Tuple
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
from collections import deque
from loguru import logger

try:
    from sklearn.linear_model import SGDClassifier, PassiveAggressiveClassifier
    from sklearn.preprocessing import StandardScaler
    from sklearn.metrics import accuracy_score
    import joblib
except ImportError:
    logger.warning("scikit-learn not installed, online learning functionality will be limited")
    SGDClassifier = None
    PassiveAggressiveClassifier = None
    StandardScaler = None
    accuracy_score = None
    joblib = None

try:
    import torch
    import torch.nn as nn
    import torch.optim as optim
except ImportError:
    logger.warning("PyTorch not installed, neural network online learning will be limited")
    torch = None
    nn = None
    optim = None


class OnlineLearner:
    """
    在线学习器类，实现增量学习和模型在线更新。
    
    支持功能：
    - 增量学习算法（SGD、Passive-Aggressive等）
    - 神经网络在线微调
    - 概念漂移检测
    - 模型性能监控
    - 自适应学习率调整
    - 特征重要性在线更新
    """
    
    def __init__(self, model_version_manager, learning_rate: float = 0.01, 
                 buffer_size: int = 1000, update_frequency: int = 10):
        """
        初始化在线学习器
        
        Args:
            model_version_manager: 模型版本管理器
            learning_rate: 学习率
            buffer_size: 数据缓冲区大小
            update_frequency: 更新频率（每N个样本更新一次）
        """
        self.logger = logger.bind(component="OnlineLearner")
        
        self.model_version_manager = model_version_manager
        self.learning_rate = learning_rate
        self.buffer_size = buffer_size
        self.update_frequency = update_frequency
        
        # 在线学习模型
        self.online_models = {}  # {symbol: online_model}
        self.feature_scalers = {}  # {symbol: scaler}
        
        # 数据缓冲区
        self.data_buffers = {}  # {symbol: deque of (features, label)}
        self.prediction_buffers = {}  # {symbol: deque of (prediction, actual)}
        
        # 性能监控
        self.performance_metrics = {}  # {symbol: metrics}
        self.concept_drift_detectors = {}  # {symbol: drift_detector}
        
        # 学习统计
        self.learning_stats = {
            'total_updates': 0,
            'successful_updates': 0,
            'concept_drifts_detected': 0,
            'models_retrained': 0
        }
        
        # 配置参数
        self.min_samples_for_update = 10
        self.performance_window_size = 100
        self.drift_threshold = 0.1
        self.retrain_threshold = 0.05
        
        self.logger.info("在线学习器初始化完成")
    
    def initialize_online_model(self, symbol: str, feature_names: List[str]) -> bool:
        """
        初始化在线学习模型
        
        Args:
            symbol: 交易对符号
            feature_names: 特征名称列表
        
        Returns:
            初始化是否成功
        """
        try:
            if SGDClassifier is None:
                self.logger.error("scikit-learn未安装，无法初始化在线学习模型")
                return False
            
            # 创建SGD分类器用于在线学习
            online_model = SGDClassifier(
                loss='log_loss',  # 逻辑回归损失
                learning_rate='adaptive',
                eta0=self.learning_rate,
                random_state=42,
                warm_start=True  # 允许增量学习
            )
            
            # 创建特征标准化器
            scaler = StandardScaler()
            
            # 初始化数据缓冲区
            self.data_buffers[symbol] = deque(maxlen=self.buffer_size)
            self.prediction_buffers[symbol] = deque(maxlen=self.performance_window_size)
            
            # 初始化性能指标
            self.performance_metrics[symbol] = {
                'accuracy': 0.0,
                'samples_processed': 0,
                'last_update': datetime.now(),
                'drift_score': 0.0
            }
            
            # 初始化概念漂移检测器
            self.concept_drift_detectors[symbol] = ConceptDriftDetector(
                window_size=self.performance_window_size,
                threshold=self.drift_threshold
            )
            
            self.online_models[symbol] = online_model
            self.feature_scalers[symbol] = scaler
            
            self.logger.info(f"在线学习模型初始化成功: {symbol}")
            return True
            
        except Exception as e:
            self.logger.error(f"初始化在线学习模型失败: {str(e)}")
            return False
    
    def update(self, market_data: Dict[str, Any]) -> bool:
        """
        使用新的市场数据更新模型
        
        Args:
            market_data: 市场数据字典
        
        Returns:
            更新是否成功
        """
        try:
            symbol = market_data.get('symbol')
            
            if not symbol:
                return False
            
            # 确保模型已初始化
            if symbol not in self.online_models:
                # 尝试从现有模型获取特征信息
                feature_names = self._get_feature_names_from_existing_model(symbol)
                if feature_names:
                    self.initialize_online_model(symbol, feature_names)
                else:
                    self.logger.warning(f"无法获取特征信息，跳过在线学习: {symbol}")
                    return False
            
            # 提取特征
            features = self._extract_features(market_data)
            
            if not features:
                return False
            
            # 添加到数据缓冲区
            self.data_buffers[symbol].append({
                'features': features,
                'timestamp': datetime.now(),
                'market_data': market_data
            })
            
            # 检查是否需要更新模型
            if len(self.data_buffers[symbol]) >= self.update_frequency:
                return self._perform_online_update(symbol)
            
            return True
            
        except Exception as e:
            self.logger.error(f"在线学习更新失败: {str(e)}")
            return False
    
    def _get_feature_names_from_existing_model(self, symbol: str) -> Optional[List[str]]:
        """
        从现有模型获取特征名称
        
        Args:
            symbol: 交易对符号
        
        Returns:
            特征名称列表
        """
        try:
            # 从模型版本管理器获取活跃模型信息
            model_info = self.model_version_manager.get_active_model_version('unified_ml')
            
            if model_info and 'training_config' in model_info:
                training_config = model_info['training_config']
                if isinstance(training_config, str):
                    training_config = json.loads(training_config)
                
                features = training_config.get('features', [])
                if features:
                    return features
            
            return None
            
        except Exception as e:
            self.logger.error(f"获取特征名称失败: {str(e)}")
            return None
    
    def _extract_features(self, market_data: Dict[str, Any]) -> Optional[Dict[str, float]]:
        """
        从市场数据中提取特征
        
        Args:
            market_data: 市场数据
        
        Returns:
            特征字典
        """
        try:
            features = {}
            
            # 从K线数据提取基础特征
            kline_data = market_data.get('kline', {})
            
            if kline_data:
                for timeframe, kline in kline_data.items():
                    if isinstance(kline, dict):
                        # 基础价格特征
                        open_price = kline.get('open', 0)
                        high_price = kline.get('high', 0)
                        low_price = kline.get('low', 0)
                        close_price = kline.get('close', 0)
                        volume = kline.get('volume', 0)
                        
                        if close_price > 0 and open_price > 0:
                            features[f'price_change_{timeframe}'] = (close_price - open_price) / open_price
                            features[f'price_range_{timeframe}'] = (high_price - low_price) / open_price
                            features[f'close_price_{timeframe}'] = close_price
                            features[f'volume_{timeframe}'] = volume
            
            # 从深度数据提取特征
            depth_data = market_data.get('depth')
            if depth_data:
                features['spread'] = depth_data.get('spread', 0)
                features['bid_ask_ratio'] = depth_data.get('bid_ask_ratio', 1.0)
                features['mid_price'] = depth_data.get('mid_price', 0)
            
            return features if features else None
            
        except Exception as e:
            self.logger.error(f"特征提取失败: {str(e)}")
            return None
    
    def _perform_online_update(self, symbol: str) -> bool:
        """
        执行在线模型更新
        
        Args:
            symbol: 交易对符号
        
        Returns:
            更新是否成功
        """
        try:
            if symbol not in self.online_models or symbol not in self.data_buffers:
                return False
            
            model = self.online_models[symbol]
            scaler = self.feature_scalers[symbol]
            data_buffer = self.data_buffers[symbol]
            
            if len(data_buffer) < self.min_samples_for_update:
                return False
            
            # 准备训练数据
            features_list = []
            labels_list = []
            
            for data_point in data_buffer:
                features = data_point['features']
                
                # 生成标签（简化版本，实际应用中可能需要更复杂的标签生成逻辑）
                label = self._generate_label(data_point)
                
                if label is not None:
                    # 转换特征为数组
                    feature_array = self._features_dict_to_array(features, symbol)
                    if feature_array is not None:
                        features_list.append(feature_array)
                        labels_list.append(label)
            
            if len(features_list) < self.min_samples_for_update:
                return False
            
            X = np.array(features_list)
            y = np.array(labels_list)
            
            # 特征标准化
            if not hasattr(scaler, 'scale_'):
                # 首次拟合
                X_scaled = scaler.fit_transform(X)
            else:
                # 增量更新标准化器
                X_scaled = scaler.transform(X)
                # 可以考虑部分拟合标准化器
            
            # 在线学习更新
            if not hasattr(model, 'classes_'):
                # 首次训练
                model.fit(X_scaled, y)
            else:
                # 增量学习
                model.partial_fit(X_scaled, y)
            
            # 更新性能指标
            self._update_performance_metrics(symbol, X_scaled, y)
            
            # 检测概念漂移
            drift_detected = self._detect_concept_drift(symbol, X_scaled, y)
            
            if drift_detected:
                self.logger.warning(f"检测到概念漂移: {symbol}")
                self.learning_stats['concept_drifts_detected'] += 1
                
                # 可以考虑重新训练模型或调整学习率
                self._handle_concept_drift(symbol)
            
            # 清空缓冲区
            data_buffer.clear()
            
            # 更新统计
            self.learning_stats['total_updates'] += 1
            self.learning_stats['successful_updates'] += 1
            
            self.logger.debug(f"在线学习更新完成: {symbol}, 样本数: {len(y)}")
            return True
            
        except Exception as e:
            self.logger.error(f"在线模型更新失败: {str(e)}")
            return False
    
    def _generate_label(self, data_point: Dict[str, Any]) -> Optional[int]:
        """
        生成训练标签
        
        Args:
            data_point: 数据点
        
        Returns:
            标签（-1: 卖出, 0: 持有, 1: 买入）
        """
        try:
            # 简化的标签生成逻辑
            # 实际应用中可能需要基于未来价格变化、交易结果等生成标签
            
            features = data_point['features']
            
            # 基于价格变化生成标签
            price_changes = [v for k, v in features.items() if 'price_change' in k]
            
            if price_changes:
                avg_change = np.mean(price_changes)
                
                if avg_change > 0.01:  # 1%以上涨幅
                    return 1  # 买入信号
                elif avg_change < -0.01:  # 1%以上跌幅
                    return -1  # 卖出信号
                else:
                    return 0  # 持有信号
            
            return 0  # 默认持有
            
        except Exception as e:
            self.logger.error(f"标签生成失败: {str(e)}")
            return None
    
    def _features_dict_to_array(self, features: Dict[str, float], symbol: str) -> Optional[np.ndarray]:
        """
        将特征字典转换为数组
        
        Args:
            features: 特征字典
            symbol: 交易对符号
        
        Returns:
            特征数组
        """
        try:
            # 获取预期的特征名称列表
            expected_features = self._get_feature_names_from_existing_model(symbol)
            
            if not expected_features:
                # 如果没有预期特征列表，使用当前特征
                expected_features = sorted(features.keys())
            
            # 按顺序提取特征值
            feature_values = []
            for feature_name in expected_features:
                value = features.get(feature_name, 0.0)  # 缺失特征用0填充
                feature_values.append(float(value))
            
            return np.array(feature_values)
            
        except Exception as e:
            self.logger.error(f"特征转换失败: {str(e)}")
            return None
    
    def _update_performance_metrics(self, symbol: str, X: np.ndarray, y: np.ndarray) -> None:
        """
        更新性能指标
        
        Args:
            symbol: 交易对符号
            X: 特征数组
            y: 标签数组
        """
        try:
            if symbol not in self.online_models:
                return
            
            model = self.online_models[symbol]
            
            # 预测准确率
            if hasattr(model, 'predict'):
                y_pred = model.predict(X)
                accuracy = accuracy_score(y, y_pred) if accuracy_score else 0.0
            else:
                accuracy = 0.0
            
            # 更新性能指标
            metrics = self.performance_metrics[symbol]
            metrics['accuracy'] = accuracy
            metrics['samples_processed'] += len(y)
            metrics['last_update'] = datetime.now()
            
            # 添加到预测缓冲区用于概念漂移检测
            if symbol in self.prediction_buffers:
                for i in range(len(y)):
                    self.prediction_buffers[symbol].append({
                        'predicted': y_pred[i] if 'y_pred' in locals() else 0,
                        'actual': y[i],
                        'timestamp': datetime.now()
                    })
            
        except Exception as e:
            self.logger.error(f"更新性能指标失败: {str(e)}")
    
    def _detect_concept_drift(self, symbol: str, X: np.ndarray, y: np.ndarray) -> bool:
        """
        检测概念漂移
        
        Args:
            symbol: 交易对符号
            X: 特征数组
            y: 标签数组
        
        Returns:
            是否检测到概念漂移
        """
        try:
            if symbol not in self.concept_drift_detectors:
                return False
            
            drift_detector = self.concept_drift_detectors[symbol]
            
            # 计算当前批次的性能
            model = self.online_models[symbol]
            if hasattr(model, 'predict'):
                y_pred = model.predict(X)
                current_accuracy = accuracy_score(y, y_pred) if accuracy_score else 0.0
            else:
                current_accuracy = 0.0
            
            # 检测漂移
            drift_detected = drift_detector.detect_drift(current_accuracy)
            
            # 更新漂移分数
            self.performance_metrics[symbol]['drift_score'] = drift_detector.get_drift_score()
            
            return drift_detected
            
        except Exception as e:
            self.logger.error(f"概念漂移检测失败: {str(e)}")
            return False
    
    def _handle_concept_drift(self, symbol: str) -> None:
        """
        处理概念漂移
        
        Args:
            symbol: 交易对符号
        """
        try:
            # 增加学习率以快速适应新概念
            if symbol in self.online_models:
                model = self.online_models[symbol]
                if hasattr(model, 'eta0'):
                    model.eta0 = min(model.eta0 * 1.5, 0.1)  # 增加学习率但不超过0.1
            
            # 可以考虑其他处理策略：
            # 1. 重新训练模型
            # 2. 调整模型参数
            # 3. 增加数据收集频率
            
            self.logger.info(f"概念漂移处理完成: {symbol}")
            
        except Exception as e:
            self.logger.error(f"概念漂移处理失败: {str(e)}")
    
    def get_online_prediction(self, symbol: str, features: Dict[str, float]) -> Optional[Dict[str, Any]]:
        """
        使用在线学习模型进行预测
        
        Args:
            symbol: 交易对符号
            features: 特征字典
        
        Returns:
            预测结果
        """
        try:
            if symbol not in self.online_models:
                return None
            
            model = self.online_models[symbol]
            scaler = self.feature_scalers[symbol]
            
            # 转换特征
            feature_array = self._features_dict_to_array(features, symbol)
            if feature_array is None:
                return None
            
            # 标准化
            if hasattr(scaler, 'scale_'):
                feature_array_scaled = scaler.transform(feature_array.reshape(1, -1))
            else:
                feature_array_scaled = feature_array.reshape(1, -1)
            
            # 预测
            if hasattr(model, 'predict'):
                prediction = model.predict(feature_array_scaled)[0]
                
                if hasattr(model, 'predict_proba'):
                    probabilities = model.predict_proba(feature_array_scaled)[0]
                    confidence = probabilities.max()
                else:
                    confidence = 0.5
                
                return {
                    'signal': int(prediction),
                    'confidence': float(confidence),
                    'model_type': 'online_learning',
                    'symbol': symbol,
                    'timestamp': datetime.now().isoformat()
                }
            
            return None
            
        except Exception as e:
            self.logger.error(f"在线预测失败: {str(e)}")
            return None
    
    def get_learning_stats(self) -> Dict[str, Any]:
        """
        获取学习统计信息
        
        Returns:
            学习统计字典
        """
        try:
            stats = self.learning_stats.copy()
            
            # 添加模型性能信息
            stats['model_performance'] = {}
            for symbol, metrics in self.performance_metrics.items():
                stats['model_performance'][symbol] = {
                    'accuracy': metrics['accuracy'],
                    'samples_processed': metrics['samples_processed'],
                    'drift_score': metrics['drift_score'],
                    'last_update': metrics['last_update'].isoformat()
                }
            
            # 添加缓冲区状态
            stats['buffer_status'] = {}
            for symbol, buffer in self.data_buffers.items():
                stats['buffer_status'][symbol] = {
                    'buffer_size': len(buffer),
                    'max_size': buffer.maxlen
                }
            
            return stats
            
        except Exception as e:
            self.logger.error(f"获取学习统计失败: {str(e)}")
            return {}
    
    def save_online_models(self, save_path: str) -> bool:
        """
        保存在线学习模型
        
        Args:
            save_path: 保存路径
        
        Returns:
            保存是否成功
        """
        try:
            if not self.online_models:
                return True
            
            os.makedirs(save_path, exist_ok=True)
            
            for symbol, model in self.online_models.items():
                model_file = os.path.join(save_path, f"online_model_{symbol}.pkl")
                
                model_data = {
                    'model': model,
                    'scaler': self.feature_scalers.get(symbol),
                    'performance_metrics': self.performance_metrics.get(symbol, {}),
                    'timestamp': datetime.now().isoformat()
                }
                
                if joblib:
                    joblib.dump(model_data, model_file)
                else:
                    with open(model_file, 'wb') as f:
                        pickle.dump(model_data, f)
            
            self.logger.info(f"在线学习模型保存成功: {save_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"保存在线学习模型失败: {str(e)}")
            return False


class ConceptDriftDetector:
    """概念漂移检测器"""
    
    def __init__(self, window_size: int = 100, threshold: float = 0.1):
        """
        初始化概念漂移检测器
        
        Args:
            window_size: 滑动窗口大小
            threshold: 漂移阈值
        """
        self.window_size = window_size
        self.threshold = threshold
        self.performance_history = deque(maxlen=window_size)
        self.baseline_performance = None
    
    def detect_drift(self, current_performance: float) -> bool:
        """
        检测概念漂移
        
        Args:
            current_performance: 当前性能指标
        
        Returns:
            是否检测到漂移
        """
        self.performance_history.append(current_performance)
        
        if len(self.performance_history) < self.window_size // 2:
            return False
        
        if self.baseline_performance is None:
            self.baseline_performance = np.mean(list(self.performance_history)[:self.window_size // 2])
            return False
        
        # 计算最近性能与基线性能的差异
        recent_performance = np.mean(list(self.performance_history)[-(self.window_size // 2):])
        performance_drop = self.baseline_performance - recent_performance
        
        # 检测是否超过阈值
        if performance_drop > self.threshold:
            # 更新基线
            self.baseline_performance = recent_performance
            return True
        
        return False
    
    def get_drift_score(self) -> float:
        """
        获取漂移分数
        
        Returns:
            漂移分数（0-1之间）
        """
        if len(self.performance_history) < 2 or self.baseline_performance is None:
            return 0.0
        
        recent_performance = np.mean(list(self.performance_history)[-min(10, len(self.performance_history)):])
        drift_score = max(0, self.baseline_performance - recent_performance) / max(self.baseline_performance, 0.01)
        
        return min(drift_score, 1.0)