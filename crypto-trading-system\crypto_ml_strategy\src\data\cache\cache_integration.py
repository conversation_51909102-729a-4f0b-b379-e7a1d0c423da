#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
缓存系统集成组件

将实时缓存系统与OptimizedTimeframeSync、数据质量验证系统和其他组件集成。
提供统一的缓存管理接口和智能缓存策略。
"""

import logging
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Any, Union, Callable
from datetime import datetime, timedelta
import time
import threading
import hashlib
import json

from .cache_core import (
    RealTimeCacheManager, CacheConfig, CacheKey, CacheEntry, 
    CacheLevel, CacheStrategy
)
from .cache_strategies import CacheStrategyFactory, IntelligentPreloader
from .cache_storage import CacheBackendFactory


class IntegratedCacheManager:
    """集成缓存管理器"""
    
    def __init__(self, config: Optional[Dict] = None):
        """
        初始化集成缓存管理器
        
        Args:
            config: 配置字典
        """
        self.logger = logging.getLogger('cryptoMlStrategy.data.cache_integration')
        
        # 默认配置
        self.config = {
            'cache': {
                'max_memory_mb': 512,
                'max_entries': 10000,
                'default_ttl_seconds': 3600,
                'eviction_strategy': 'adaptive',
                'enable_compression': True,
                'enable_preloading': True
            },
            'integration': {
                'sync_with_timeframe_sync': True,
                'sync_with_quality_system': True,
                'enable_kafka_caching': True,
                'cache_ml_features': True,
                'cache_indicators': True
            },
            'performance': {
                'target_hit_ratio': 0.9,
                'target_access_time_ms': 1.0,
                'enable_performance_monitoring': True,
                'auto_optimization': True
            }
        }
        
        if config:
            self._deep_update(self.config, config)
        
        # 创建缓存配置
        cache_config = CacheConfig(
            max_memory_mb=self.config['cache']['max_memory_mb'],
            max_entries=self.config['cache']['max_entries'],
            default_ttl_seconds=self.config['cache']['default_ttl_seconds'],
            eviction_strategy=CacheStrategy(self.config['cache']['eviction_strategy']),
            enable_compression=self.config['cache']['enable_compression'],
            enable_preloading=self.config['cache']['enable_preloading']
        )
        
        # 初始化缓存管理器
        self.cache_manager = RealTimeCacheManager(cache_config)
        
        # 注册后端
        self._register_backends()
        
        # 注册策略
        self._register_strategies()
        
        # 智能预加载器
        self.preloader = None
        if self.config['cache']['enable_preloading']:
            self.preloader = CacheStrategyFactory.create_preloader(self.cache_manager)
        
        # 集成组件引用
        self._timeframe_sync = None
        self._quality_manager = None
        
        # 性能监控
        self._performance_monitor_active = True
        self._start_performance_monitor()
        
        # 统计信息
        self._integration_stats = {
            'timeframe_sync_hits': 0,
            'quality_cache_hits': 0,
            'ml_feature_hits': 0,
            'indicator_hits': 0,
            'kafka_cache_hits': 0,
            'total_integrations': 0
        }
        
        self._lock = threading.RLock()
        
        self.logger.info("集成缓存管理器初始化完成")
    
    def _deep_update(self, base_dict: Dict, update_dict: Dict):
        """深度更新字典"""
        for key, value in update_dict.items():
            if key in base_dict and isinstance(base_dict[key], dict) and isinstance(value, dict):
                self._deep_update(base_dict[key], value)
            else:
                base_dict[key] = value
    
    def _register_backends(self):
        """注册缓存后端"""
        try:
            # L1内存缓存
            memory_backend = CacheBackendFactory.create_partitioned_backend(
                partition_count=8,
                backend_factory=lambda: CacheBackendFactory.create_memory_backend(
                    max_size=self.config['cache']['max_entries'] // 8,
                    enable_compression=self.config['cache']['enable_compression']
                )
            )
            self.cache_manager.register_backend(CacheLevel.L1_MEMORY, memory_backend)
            
            # L2持久化缓存（可选）
            if self.config['cache'].get('enable_persistent_cache', False):
                persistent_backend = CacheBackendFactory.create_persistent_backend()
                self.cache_manager.register_backend(CacheLevel.L2_PERSISTENT, persistent_backend)
            
        except Exception as e:
            self.logger.error(f"注册缓存后端失败: {e}")
    
    def _register_strategies(self):
        """注册缓存策略"""
        try:
            # 注册所有支持的淘汰策略
            for strategy in CacheStrategy:
                try:
                    policy = CacheStrategyFactory.create_eviction_policy(strategy)
                    self.cache_manager.register_eviction_policy(strategy, policy)
                except Exception as e:
                    self.logger.warning(f"注册策略 {strategy.value} 失败: {e}")
                    
        except Exception as e:
            self.logger.error(f"注册缓存策略失败: {e}")
    
    def integrate_timeframe_sync(self, timeframe_sync_instance):
        """
        集成OptimizedTimeframeSync
        
        Args:
            timeframe_sync_instance: OptimizedTimeframeSync实例
        """
        try:
            self._timeframe_sync = timeframe_sync_instance
            
            # 包装同步方法以添加缓存
            original_sync = timeframe_sync_instance.sync_timeframes
            
            def cached_sync_timeframes(symbol: str, target_timeframe: Optional[str] = None):
                # 生成缓存键
                cache_key = CacheKey(
                    symbol=symbol,
                    timeframe=target_timeframe or "auto",
                    data_type="synchronized_data"
                )
                
                # 尝试从缓存获取
                cached_data = self.cache_manager.get(cache_key)
                if cached_data is not None:
                    with self._lock:
                        self._integration_stats['timeframe_sync_hits'] += 1
                    
                    if self.preloader:
                        self.preloader.record_access(cache_key.to_string(), True)
                    
                    return cached_data
                
                # 缓存未命中，执行原始同步
                result = original_sync(symbol, target_timeframe)
                
                if result is not None:
                    # 缓存结果
                    self.cache_manager.put(cache_key, result, ttl_seconds=300)  # 5分钟TTL
                
                if self.preloader:
                    self.preloader.record_access(cache_key.to_string(), False)
                
                with self._lock:
                    self._integration_stats['total_integrations'] += 1
                
                return result
            
            # 替换方法
            timeframe_sync_instance.sync_timeframes = cached_sync_timeframes
            
            self.logger.info("OptimizedTimeframeSync集成完成")
            
        except Exception as e:
            self.logger.error(f"集成OptimizedTimeframeSync失败: {e}")
    
    def integrate_quality_manager(self, quality_manager_instance):
        """
        集成数据质量管理器
        
        Args:
            quality_manager_instance: IntegratedQualityManager实例
        """
        try:
            self._quality_manager = quality_manager_instance
            
            # 包装质量验证方法以添加缓存
            original_validate = quality_manager_instance.validate_and_repair_data
            
            def cached_validate_and_repair_data(data: pd.DataFrame, symbol: str = "", timeframe: str = ""):
                # 生成数据指纹作为缓存键的一部分
                data_fingerprint = self._generate_data_fingerprint(data)
                
                cache_key = CacheKey(
                    symbol=symbol or "unknown",
                    timeframe=timeframe or "unknown",
                    data_type="quality_validated",
                    timestamp=int(data_fingerprint, 16) % 1000000  # 使用指纹的一部分作为时间戳
                )
                
                # 尝试从缓存获取
                cached_result = self.cache_manager.get(cache_key)
                if cached_result is not None:
                    with self._lock:
                        self._integration_stats['quality_cache_hits'] += 1
                    
                    return cached_result
                
                # 缓存未命中，执行原始验证
                result = original_validate(data, symbol, timeframe)
                
                # 缓存结果（只缓存质量分数较高的结果）
                if len(result) >= 2 and hasattr(result[1], 'overall_score') and result[1].overall_score > 0.7:
                    self.cache_manager.put(cache_key, result, ttl_seconds=1800)  # 30分钟TTL
                
                with self._lock:
                    self._integration_stats['total_integrations'] += 1
                
                return result
            
            # 替换方法
            quality_manager_instance.validate_and_repair_data = cached_validate_and_repair_data
            
            self.logger.info("数据质量管理器集成完成")
            
        except Exception as e:
            self.logger.error(f"集成数据质量管理器失败: {e}")
    
    def cache_ml_features(self, symbol: str, timeframe: str, features: Dict[str, Any], 
                         feature_type: str = "general") -> bool:
        """
        缓存ML特征
        
        Args:
            symbol: 交易对符号
            timeframe: 时间框架
            features: 特征数据
            feature_type: 特征类型
            
        Returns:
            是否缓存成功
        """
        try:
            cache_key = CacheKey(
                symbol=symbol,
                timeframe=timeframe,
                data_type=f"ml_features_{feature_type}"
            )
            
            success = self.cache_manager.put(cache_key, features, ttl_seconds=1800)  # 30分钟TTL
            
            if success:
                with self._lock:
                    self._integration_stats['total_integrations'] += 1
            
            return success
            
        except Exception as e:
            self.logger.error(f"缓存ML特征失败: {e}")
            return False
    
    def get_ml_features(self, symbol: str, timeframe: str, 
                       feature_type: str = "general") -> Optional[Dict[str, Any]]:
        """
        获取缓存的ML特征
        
        Args:
            symbol: 交易对符号
            timeframe: 时间框架
            feature_type: 特征类型
            
        Returns:
            特征数据或None
        """
        try:
            cache_key = CacheKey(
                symbol=symbol,
                timeframe=timeframe,
                data_type=f"ml_features_{feature_type}"
            )
            
            features = self.cache_manager.get(cache_key)
            
            if features is not None:
                with self._lock:
                    self._integration_stats['ml_feature_hits'] += 1
            
            return features
            
        except Exception as e:
            self.logger.error(f"获取ML特征失败: {e}")
            return None
    
    def cache_indicators(self, symbol: str, timeframe: str, indicators: Dict[str, Any]) -> bool:
        """
        缓存技术指标
        
        Args:
            symbol: 交易对符号
            timeframe: 时间框架
            indicators: 指标数据
            
        Returns:
            是否缓存成功
        """
        try:
            cache_key = CacheKey(
                symbol=symbol,
                timeframe=timeframe,
                data_type="indicators"
            )
            
            success = self.cache_manager.put(cache_key, indicators, ttl_seconds=600)  # 10分钟TTL
            
            if success:
                with self._lock:
                    self._integration_stats['total_integrations'] += 1
            
            return success
            
        except Exception as e:
            self.logger.error(f"缓存技术指标失败: {e}")
            return False
    
    def get_indicators(self, symbol: str, timeframe: str) -> Optional[Dict[str, Any]]:
        """
        获取缓存的技术指标
        
        Args:
            symbol: 交易对符号
            timeframe: 时间框架
            
        Returns:
            指标数据或None
        """
        try:
            cache_key = CacheKey(
                symbol=symbol,
                timeframe=timeframe,
                data_type="indicators"
            )
            
            indicators = self.cache_manager.get(cache_key)
            
            if indicators is not None:
                with self._lock:
                    self._integration_stats['indicator_hits'] += 1
            
            return indicators
            
        except Exception as e:
            self.logger.error(f"获取技术指标失败: {e}")
            return None
    
    def cache_kafka_message(self, topic: str, message: Dict[str, Any], 
                          ttl_seconds: int = 60) -> bool:
        """
        缓存Kafka消息
        
        Args:
            topic: Kafka主题
            message: 消息内容
            ttl_seconds: 生存时间
            
        Returns:
            是否缓存成功
        """
        try:
            # 生成消息指纹
            message_fingerprint = self._generate_message_fingerprint(message)
            
            cache_key = CacheKey(
                symbol=message.get('symbol', 'unknown'),
                timeframe=message.get('timeframe', 'unknown'),
                data_type=f"kafka_{topic}",
                timestamp=int(message_fingerprint, 16) % 1000000
            )
            
            success = self.cache_manager.put(cache_key, message, ttl_seconds=ttl_seconds)
            
            if success:
                with self._lock:
                    self._integration_stats['total_integrations'] += 1
            
            return success
            
        except Exception as e:
            self.logger.error(f"缓存Kafka消息失败: {e}")
            return False
    
    def get_kafka_message(self, topic: str, symbol: str, timeframe: str) -> Optional[Dict[str, Any]]:
        """
        获取缓存的Kafka消息
        
        Args:
            topic: Kafka主题
            symbol: 交易对符号
            timeframe: 时间框架
            
        Returns:
            消息内容或None
        """
        try:
            # 由于消息指纹未知，需要搜索匹配的缓存项
            # 这里简化处理，实际应用中可能需要更复杂的索引机制
            cache_key = CacheKey(
                symbol=symbol,
                timeframe=timeframe,
                data_type=f"kafka_{topic}"
            )
            
            message = self.cache_manager.get(cache_key)
            
            if message is not None:
                with self._lock:
                    self._integration_stats['kafka_cache_hits'] += 1
            
            return message
            
        except Exception as e:
            self.logger.error(f"获取Kafka消息失败: {e}")
            return None
    
    def _generate_data_fingerprint(self, data: pd.DataFrame) -> str:
        """生成数据指纹"""
        try:
            # 使用数据的形状、列名和部分数值生成指纹
            fingerprint_data = {
                'shape': data.shape,
                'columns': list(data.columns),
                'dtypes': str(data.dtypes.to_dict()),
                'sample_values': str(data.head(3).values.tolist()) if len(data) > 0 else ""
            }
            
            fingerprint_str = json.dumps(fingerprint_data, sort_keys=True)
            return hashlib.md5(fingerprint_str.encode()).hexdigest()
            
        except Exception:
            return hashlib.md5(str(time.time()).encode()).hexdigest()
    
    def _generate_message_fingerprint(self, message: Dict[str, Any]) -> str:
        """生成消息指纹"""
        try:
            # 排除时间戳字段，使用其他字段生成指纹
            fingerprint_data = {k: v for k, v in message.items() if 'time' not in k.lower()}
            fingerprint_str = json.dumps(fingerprint_data, sort_keys=True)
            return hashlib.md5(fingerprint_str.encode()).hexdigest()
            
        except Exception:
            return hashlib.md5(str(time.time()).encode()).hexdigest()
    
    def _start_performance_monitor(self):
        """启动性能监控线程"""
        def monitor():
            while self._performance_monitor_active:
                try:
                    # 获取性能统计
                    stats = self.cache_manager.get_statistics()
                    cache_stats = stats['cache_stats']
                    
                    # 检查性能目标
                    hit_ratio = cache_stats['hit_ratio']
                    avg_access_time = cache_stats['avg_access_time_ms']
                    
                    target_hit_ratio = self.config['performance']['target_hit_ratio']
                    target_access_time = self.config['performance']['target_access_time_ms']
                    
                    # 性能告警
                    if hit_ratio < target_hit_ratio:
                        self.logger.warning(f"缓存命中率低于目标: {hit_ratio:.3f} < {target_hit_ratio}")
                    
                    if avg_access_time > target_access_time:
                        self.logger.warning(f"访问延迟高于目标: {avg_access_time:.1f}ms > {target_access_time}ms")
                    
                    # 自动优化（如果启用）
                    if self.config['performance']['auto_optimization']:
                        self._auto_optimize_performance(hit_ratio, avg_access_time)
                    
                    time.sleep(300)  # 每5分钟检查一次
                    
                except Exception as e:
                    self.logger.error(f"性能监控错误: {e}")
                    time.sleep(600)  # 出错后10分钟重试
        
        monitor_thread = threading.Thread(target=monitor, daemon=True)
        monitor_thread.start()
    
    def _auto_optimize_performance(self, hit_ratio: float, avg_access_time: float):
        """自动优化性能"""
        try:
            target_hit_ratio = self.config['performance']['target_hit_ratio']
            target_access_time = self.config['performance']['target_access_time_ms']
            
            # 如果命中率低，考虑增加缓存大小或调整TTL
            if hit_ratio < target_hit_ratio * 0.9:
                # 可以在这里实现动态调整缓存配置的逻辑
                self.logger.info("考虑增加缓存容量或调整TTL以提高命中率")
            
            # 如果访问时间高，考虑优化数据结构或启用压缩
            if avg_access_time > target_access_time * 1.5:
                self.logger.info("考虑优化数据结构或调整压缩策略以降低访问延迟")
                
        except Exception as e:
            self.logger.error(f"自动优化失败: {e}")
    
    def get_integration_statistics(self) -> Dict[str, Any]:
        """获取集成统计信息"""
        try:
            cache_stats = self.cache_manager.get_statistics()
            
            with self._lock:
                integration_stats = self._integration_stats.copy()
            
            # 计算集成命中率
            total_hits = (
                integration_stats['timeframe_sync_hits'] +
                integration_stats['quality_cache_hits'] +
                integration_stats['ml_feature_hits'] +
                integration_stats['indicator_hits'] +
                integration_stats['kafka_cache_hits']
            )
            
            integration_hit_ratio = total_hits / max(1, integration_stats['total_integrations'])
            
            return {
                'cache_statistics': cache_stats,
                'integration_statistics': integration_stats,
                'integration_hit_ratio': integration_hit_ratio,
                'preloader_statistics': self.preloader.get_statistics() if self.preloader else {},
                'performance_targets': {
                    'target_hit_ratio': self.config['performance']['target_hit_ratio'],
                    'target_access_time_ms': self.config['performance']['target_access_time_ms'],
                    'current_hit_ratio': cache_stats['cache_stats']['hit_ratio'],
                    'current_access_time_ms': cache_stats['cache_stats']['avg_access_time_ms']
                },
                'config': self.config
            }
            
        except Exception as e:
            self.logger.error(f"获取集成统计失败: {e}")
            return {}
    
    def clear_cache(self, cache_type: Optional[str] = None) -> int:
        """
        清空缓存
        
        Args:
            cache_type: 缓存类型（可选，如果指定则只清空特定类型）
            
        Returns:
            清除的条目数量
        """
        try:
            if cache_type is None:
                # 清空所有缓存
                return self.cache_manager.clear()
            else:
                # 清空特定类型的缓存
                # 这里需要实现按类型清空的逻辑
                # 由于当前架构限制，暂时返回0
                self.logger.warning(f"按类型清空缓存功能待实现: {cache_type}")
                return 0
                
        except Exception as e:
            self.logger.error(f"清空缓存失败: {e}")
            return 0
    
    def shutdown(self):
        """关闭集成缓存管理器"""
        try:
            self._performance_monitor_active = False
            
            if self.preloader:
                self.preloader.shutdown()
            
            self.cache_manager.shutdown()
            
            self.logger.info("集成缓存管理器已关闭")
            
        except Exception as e:
            self.logger.error(f"关闭集成缓存管理器失败: {e}")