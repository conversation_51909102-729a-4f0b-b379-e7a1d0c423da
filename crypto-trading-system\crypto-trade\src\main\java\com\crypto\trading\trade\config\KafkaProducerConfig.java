package com.crypto.trading.trade.config;

import com.crypto.trading.common.config.AbstractKafkaProducerConfig;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;

/**
 * Kafka生产者配置类
 * <p>
 * 配置Kafka生产者，用于发送交易结果消息
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Configuration
public class KafkaProducerConfig extends AbstractKafkaProducerConfig {

    /**
     * 交易结果主题
     */
    @Value("${trade.result.topic:trade.result}")
    private String tradeResultTopic;

    /**
     * 订单状态主题
     */
    @Value("${trade.order.status.topic:order.status}")
    private String orderStatusTopic;

    /**
     * 配置Avro序列化的Kafka生产者工厂
     *
     * @return Avro序列化的Kafka生产者工厂
     */
    @Bean
    public ProducerFactory<String, Object> avroProducerFactory() {
        return createAvroProducerFactory();
    }

    /**
     * 配置Avro序列化的Kafka模板
     *
     * @return Avro序列化的Kafka模板
     */
    @Bean
    public KafkaTemplate<String, Object> avroKafkaTemplate() {
        return createKafkaTemplate(avroProducerFactory(), tradeResultTopic);
    }
    
    /**
     * 配置JSON序列化的Kafka生产者工厂
     *
     * @return JSON序列化的Kafka生产者工厂
     */
    @Bean
    public ProducerFactory<String, Object> jsonProducerFactory() {
        return createJsonProducerFactory();
    }

    /**
     * 配置JSON序列化的Kafka模板
     *
     * @return JSON序列化的Kafka模板
     */
    @Bean
    public KafkaTemplate<String, Object> jsonKafkaTemplate() {
        return createKafkaTemplate(jsonProducerFactory(), orderStatusTopic);
    }

    /**
     * 获取交易结果主题
     *
     * @return 交易结果主题
     */
    public String getTradeResultTopic() {
        return tradeResultTopic;
    }

    /**
     * 获取订单状态主题
     *
     * @return 订单状态主题
     */
    public String getOrderStatusTopic() {
        return orderStatusTopic;
    }

    /**
     * 获取模块名称，用于构建事务ID
     *
     * @return 模块名称
     */
    @Override
    protected String getModuleName() {
        return "trade";
    }

    /**
     * 获取确认模式
     * 交易模块使用"all"确认模式，确保高可靠性
     *
     * @return 确认模式
     */
    @Override
    protected String getAcks() {
        return "all";
    }

    /**
     * 获取重试次数
     * 交易模块使用5次重试，提高可靠性
     *
     * @return 重试次数
     */
    @Override
    protected int getRetries() {
        return 5;
    }

    /**
     * 是否启用幂等性发送
     * 交易模块始终启用幂等性发送，确保消息不会重复
     *
     * @return 是否启用幂等性发送
     */
    @Override
    protected boolean isEnableIdempotence() {
        return true;
    }

    /**
     * 获取事务ID前缀
     * 交易模块使用"trade-result"作为事务ID前缀
     *
     * @return 事务ID前缀
     */
    @Override
    protected String getTransactionalId() {
        return "trade-result";
    }
}