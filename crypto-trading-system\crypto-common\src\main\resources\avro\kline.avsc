{"namespace": "com.crypto.trading.common.avro", "type": "record", "name": "KlineData", "doc": "K线数据Avro模式定义", "fields": [{"name": "id", "type": "string", "doc": "唯一消息ID"}, {"name": "timestamp", "type": "long", "doc": "消息时间戳(毫秒)"}, {"name": "type", "type": "string", "doc": "消息类型，固定为KLINE"}, {"name": "source", "type": "string", "doc": "消息来源模块"}, {"name": "version", "type": "string", "doc": "消息版本"}, {"name": "data", "type": {"type": "record", "name": "KlineDataContent", "fields": [{"name": "symbol", "type": "string", "doc": "交易对符号"}, {"name": "interval", "type": "string", "doc": "K线间隔"}, {"name": "openTime", "type": "long", "doc": "开盘时间(毫秒)"}, {"name": "closeTime", "type": "long", "doc": "收盘时间(毫秒)"}, {"name": "open", "type": "double", "doc": "开盘价"}, {"name": "high", "type": "double", "doc": "最高价"}, {"name": "low", "type": "double", "doc": "最低价"}, {"name": "close", "type": "double", "doc": "收盘价"}, {"name": "volume", "type": "double", "doc": "交易量"}, {"name": "quoteVolume", "type": "double", "doc": "报价资产交易量"}, {"name": "trades", "type": "int", "doc": "交易笔数"}, {"name": "completed", "type": "boolean", "doc": "是否已完成"}]}, "doc": "K线数据内容"}]}