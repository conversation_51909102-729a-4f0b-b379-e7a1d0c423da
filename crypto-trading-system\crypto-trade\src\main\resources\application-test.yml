spring:
  # 数据源配置（使用H2内存数据库）
  datasource:
    url: jdbc:h2:mem:testdb;MODE=MySQL;DB_CLOSE_DELAY=-1
    username: sa
    password: 
    driver-class-name: org.h2.Driver
    
  # H2 控制台配置
  h2:
    console:
      enabled: true
      path: /h2-console
      
  # 测试环境Kafka配置
  kafka:
    bootstrap-servers: localhost:9092
    consumer:
      auto-offset-reset: earliest
      # 测试环境可以使用String序列化器
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
    producer:
      value-serializer: org.apache.kafka.common.serialization.StringSerializer

# 测试环境应用配置
crypto:
  trade:
    # 强制设置为试运行模式
    execution:
      enabled: true
      dry-run: true
      
    # 风控测试配置
    risk:
      max-order-value-usdt: 10.0  # 测试环境限制更严格
      
    # API限速配置（测试环境可以更宽松）
    rate-limiter:
      order-requests:
        capacity: 1000
        rate-per-second: 100
      query-requests:
        capacity: 1000
        rate-per-second: 100