<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (21) on Fri Apr 04 16:06:08 CST 2025 -->
<title>com.binance.connector.futures.client.impl 类分层结构 (io.github.binance:binance-futures-connector-java 3.0.5 API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-04-04">
<meta name="description" content="tree: package: com.binance.connector.futures.client.impl">
<meta name="generator" content="javadoc/PackageTreeWriter">
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="package-tree-page">
<script type="text/javascript">var pathtoroot = "../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="切换导航链接"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="../../../../../../index.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li>类</li>
<li>使用</li>
<li class="nav-bar-cell1-rev">树</li>
<li><a href="../../../../../../index-all.html">索引</a></li>
<li><a href="../../../../../../help-doc.html#tree">帮助</a></li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list"></div>
<div class="nav-list-search"><a href="../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="搜索">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 class="title">程序包com.binance.connector.futures.client.impl的分层结构</h1>
</div>
<span class="package-hierarchy-label">程序包分层结构:</span>
<ul class="horizontal contents-list">
<li><a href="../../../../../../overview-tree.html">所有程序包</a></li>
</ul>
<section class="hierarchy">
<h2 title="类分层结构">类分层结构</h2>
<ul>
<li class="circle">java.lang.<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html" class="type-name-link external-link" title="java.lang中的类或接口">Object</a>
<ul>
<li class="circle">com.binance.connector.futures.client.impl.<a href="FuturesClientImpl.html" class="type-name-link" title="com.binance.connector.futures.client.impl中的类">FuturesClientImpl</a> (implements com.binance.connector.futures.client.<a href="../FuturesClient.html" title="com.binance.connector.futures.client中的接口">FuturesClient</a>)
<ul>
<li class="circle">com.binance.connector.futures.client.impl.<a href="CMFuturesClientImpl.html" class="type-name-link" title="com.binance.connector.futures.client.impl中的类">CMFuturesClientImpl</a></li>
<li class="circle">com.binance.connector.futures.client.impl.<a href="UMFuturesClientImpl.html" class="type-name-link" title="com.binance.connector.futures.client.impl中的类">UMFuturesClientImpl</a></li>
</ul>
</li>
<li class="circle">com.binance.connector.futures.client.impl.<a href="WebsocketClientImpl.html" class="type-name-link" title="com.binance.connector.futures.client.impl中的类">WebsocketClientImpl</a> (implements com.binance.connector.futures.client.<a href="../WebsocketClient.html" title="com.binance.connector.futures.client中的接口">WebsocketClient</a>)
<ul>
<li class="circle">com.binance.connector.futures.client.impl.<a href="CMWebsocketClientImpl.html" class="type-name-link" title="com.binance.connector.futures.client.impl中的类">CMWebsocketClientImpl</a></li>
<li class="circle">com.binance.connector.futures.client.impl.<a href="UMWebsocketClientImpl.html" class="type-name-link" title="com.binance.connector.futures.client.impl中的类">UMWebsocketClientImpl</a></li>
</ul>
</li>
</ul>
</li>
</ul>
</section>
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &#169; 2025. All rights reserved.</small></p>
</footer>
</div>
</div>
</body>
</html>
