#!/usr/bin/env python3
"""
Crypto ML Strategy - 包级别导入测试脚本

该脚本通过包级别导入测试重组后的目录结构。
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
project_root = os.path.join(os.path.dirname(__file__), '..')
sys.path.insert(0, project_root)

def test_package_imports():
    """测试包级别导入"""
    print("开始包级别导入测试...")
    print("=" * 50)
    
    total_tests = 0
    passed_tests = 0
    failed_tests = []
    
    # 测试包级别导入
    test_packages = [
        # 核心包
        ("src.core", None),
        ("src.validators", None),
        ("src.coordinators", None),
        
        # 主要功能包
        ("src.ml", None),
        ("src.benchmarks", None),
        ("src.infrastructure", None),
        ("src.data", None),
        
        # 其他包
        ("src.indicators", None),
        ("src.strategy", None),
        ("src.risk_management", None),
        ("src.utils", None),
        ("src.api", None),
        ("src.docs", None),
        ("src.config", None),
    ]
    
    for package_name, _ in test_packages:
        total_tests += 1
        try:
            package = __import__(package_name, fromlist=[''])
            print(f"[PASS] {package_name}")
            
            # 检查__all__属性
            if hasattr(package, '__all__'):
                exports = package.__all__
                print(f"       导出: {exports}")
            
            passed_tests += 1
        except Exception as e:
            print(f"[FAIL] {package_name}: {str(e)}")
            failed_tests.append((package_name, str(e)))
    
    # 测试特定类的导入（通过包）
    print("\n测试特定类导入...")
    specific_tests = [
        ("src.core", "ValidationResult"),
        ("src.validators", "LatencyValidator"),
        ("src.coordinators", "SystemPerformanceValidator"),
        ("src.ml", "MLInferenceOptimizer"),
        ("src.benchmarks", "BenchmarkFramework"),
    ]
    
    for package_name, class_name in specific_tests:
        total_tests += 1
        try:
            package = __import__(package_name, fromlist=[class_name])
            if hasattr(package, class_name):
                getattr(package, class_name)
                print(f"[PASS] {package_name}.{class_name}")
                passed_tests += 1
            else:
                print(f"[FAIL] {package_name}.{class_name}: 类不存在")
                failed_tests.append((f"{package_name}.{class_name}", "类不存在"))
        except Exception as e:
            print(f"[FAIL] {package_name}.{class_name}: {str(e)}")
            failed_tests.append((f"{package_name}.{class_name}", str(e)))
    
    # 测试子包导入
    print("\n测试子包导入...")
    subpackage_tests = [
        ("src.data.cache", None),
        ("src.data.clients", None),
        ("src.data.quality", None),
        ("src.data.sync", None),
        ("src.infrastructure.logging", None),
        ("src.infrastructure.startup", None),
        ("src.ml.models", None),
        ("src.ml.distillation", None),
    ]
    
    for package_name, _ in subpackage_tests:
        total_tests += 1
        try:
            package = __import__(package_name, fromlist=[''])
            print(f"[PASS] {package_name}")
            passed_tests += 1
        except Exception as e:
            print(f"[FAIL] {package_name}: {str(e)}")
            failed_tests.append((package_name, str(e)))
    
    # 输出结果
    print("\n" + "=" * 50)
    success_rate = (passed_tests / total_tests) * 100
    print(f"测试完成: {passed_tests}/{total_tests} 通过 ({success_rate:.1f}%)")
    
    if failed_tests:
        print("\n失败的测试:")
        for package, error in failed_tests:
            print(f"  - {package}: {error}")
    else:
        print("\n所有包导入测试通过！")
    
    # 生成报告
    report = f"""
# 包级别导入测试报告

测试时间: {datetime.now().isoformat()}
测试总数: {total_tests}
通过测试: {passed_tests}
失败测试: {len(failed_tests)}
成功率: {success_rate:.1f}%

## 失败的测试
"""
    
    if failed_tests:
        for package, error in failed_tests:
            report += f"- {package}: {error}\n"
    else:
        report += "无失败测试\n"
    
    try:
        with open('package_import_test_report.txt', 'w', encoding='utf-8') as f:
            f.write(report)
        print(f"\n报告已保存到 package_import_test_report.txt")
    except Exception as e:
        print(f"无法保存报告: {e}")
    
    return len(failed_tests) == 0

if __name__ == "__main__":
    success = test_package_imports()
    sys.exit(0 if success else 1)