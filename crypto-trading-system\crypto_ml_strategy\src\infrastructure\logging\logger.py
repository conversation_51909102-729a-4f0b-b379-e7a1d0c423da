#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
日志管理模块

配置日志记录器，支持控制台输出和文件输出。
"""

import os
import sys
from pathlib import Path
from typing import Optional
import logging
import logging.config
from loguru import logger


def setup_logger(log_level: str = "INFO", log_to_file: bool = True) -> logger:
    """
    设置日志记录器
    
    Args:
        log_level: 日志级别，默认为INFO
        log_to_file: 是否记录到文件，默认为True
    
    Returns:
        配置好的logger实例
    """
    # 清除之前的处理器
    logger.remove()
    
    # 添加控制台输出
    logger.add(
        sys.stderr,
        level=log_level,
        format="<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
    )
    
    # 添加文件输出
    if log_to_file:
        # 创建日志目录
        base_dir = Path(__file__).parent.parent
        log_dir = base_dir / "logs"
        os.makedirs(log_dir, exist_ok=True)
        
        # 按天轮转的日志文件
        logger.add(
            log_dir / "crypto_ml_strategy_{time:YYYY-MM-DD}.log",
            rotation="00:00",  # 每天午夜轮转
            retention="30 days",  # 保留30天
            level=log_level,
            format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | {name}:{function}:{line} - {message}",
            encoding="utf-8"
        )
        
        # 错误日志单独保存
        logger.add(
            log_dir / "crypto_ml_strategy_error_{time:YYYY-MM-DD}.log",
            rotation="00:00",
            retention="30 days",
            level="ERROR",
            format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | {name}:{function}:{line} - {message}",
            encoding="utf-8"
        )
    
    logger.info("日志系统已初始化")
    return logger


class InterceptHandler(logging.Handler):
    """
    拦截标准库日志的处理器，转发到loguru
    """
    
    def emit(self, record):
        # 获取对应的loguru级别（如果可能）
        try:
            level = logger.level(record.levelname).name
        except ValueError:
            level = record.levelno
        
        # 查找调用者
        frame, depth = logging.currentframe(), 2
        while frame.f_code.co_filename == logging.__file__:
            frame = frame.f_back
            depth += 1
        
        logger.opt(depth=depth, exception=record.exc_info).log(
            level, record.getMessage()
        )


def setup_standard_logging():
    """
    配置标准库日志，将其转发到loguru
    用于兼容使用logging模块的第三方库
    """
    # 移除已存在的处理器
    logging.root.handlers = []
    
    # 设置标准库日志的处理器为我们的拦截器
    logging.basicConfig(handlers=[InterceptHandler()], level=0)
    
    # 为已存在的所有标准库日志记录器添加拦截器
    for name in logging.root.manager.loggerDict.keys():
        logging.getLogger(name).handlers = []
        logging.getLogger(name).propagate = True


def load_logging_config_file(config_file: Optional[str] = None) -> None:
    """
    从配置文件加载日志配置
    
    Args:
        config_file: 配置文件路径，默认为'config/logging.conf'
    """
    if config_file is None:
        base_dir = Path(__file__).parent.parent
        config_file = str(base_dir / "config" / "logging.conf")
    
    if os.path.exists(config_file):
        try:
            # 从文件加载配置
            logging.config.fileConfig(config_file)
            logger.info(f"从文件加载日志配置: {config_file}")
            
            # 添加拦截器
            logging.root.handlers = [InterceptHandler()]
        except Exception as e:
            logger.error(f"加载日志配置文件失败: {str(e)}")
    else:
        logger.warning(f"日志配置文件不存在，使用默认配置: {config_file}")
        setup_standard_logging()