package com.crypto.trading.market.manager;

import com.crypto.trading.market.config.MarketDataConfig;
import com.crypto.trading.market.config.WebSocketConfig;
import com.crypto.trading.market.listener.DepthDataListener;
import com.crypto.trading.market.listener.KlineDataListener;
import com.crypto.trading.market.listener.TradeDataListener;
import com.crypto.trading.market.websocket.WebSocketHealthChecker;
import com.crypto.trading.sdk.websocket.WebSocketConnectionPool;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.lang.reflect.Field;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * WebSocketManager单元测试
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class WebSocketManagerTest {

    @Mock
    private MarketDataConfig marketDataConfig;

    @Mock
    private WebSocketConfig webSocketConfig;

    @Mock
    private DepthDataListener depthDataListener;

    @Mock
    private KlineDataListener klineDataListener;

    @Mock
    private TradeDataListener tradeDataListener;

    @Mock
    private WebSocketConnectionPool connectionPool;

    @Mock
    private WebSocketHealthChecker healthChecker;

    @InjectMocks
    private WebSocketManager webSocketManager;

    @BeforeEach
    void setUp() throws Exception {
        // 配置市场数据
        when(marketDataConfig.isDepthEnabled()).thenReturn(true);
        when(marketDataConfig.isKlineEnabled()).thenReturn(true);
        when(marketDataConfig.isTradeEnabled()).thenReturn(true);

        // 配置WebSocket
        when(webSocketConfig.getConnectTimeout()).thenReturn(5000);
        when(webSocketConfig.getReconnectInterval()).thenReturn(1000L);
        
        // 手动设置虚拟线程执行器，避免空指针异常
        Field executorField = WebSocketManager.class.getDeclaredField("virtualThreadExecutor");
        executorField.setAccessible(true);
        executorField.set(webSocketManager, Executors.newVirtualThreadPerTaskExecutor());
    }

    @Test
    void testInit() {
        // 初始化WebSocketManager
        webSocketManager.init();

        // 验证虚拟线程执行器已创建
        ExecutorService executor = webSocketManager.getVirtualThreadExecutor();
        assertNotNull(executor);
        assertFalse(executor.isShutdown());
    }

    @Test
    void testStartListeners() {
        // 启动监听器
        webSocketManager.startListeners();

        // 验证各个监听器已启动
        verify(depthDataListener).startDepthDataSubscription();
        verify(klineDataListener).startKlineDataSubscription();
        verify(tradeDataListener).startTradeDataSubscription();

        // 验证运行状态
        assertTrue(webSocketManager.isRunning());
    }

    @Test
    void testStopListeners() {
        // 设置运行状态为true
        try {
            Field runningField = WebSocketManager.class.getDeclaredField("running");
            runningField.setAccessible(true);
            runningField.set(webSocketManager, new java.util.concurrent.atomic.AtomicBoolean(true));
        } catch (Exception e) {
            fail("设置运行状态失败: " + e.getMessage());
        }

        // 停止监听器
        webSocketManager.stopListeners();

        // 验证各个监听器已停止
        verify(depthDataListener).stopDepthDataSubscription();
        verify(klineDataListener).stopKlineDataSubscription();
        verify(tradeDataListener).stopTradeDataSubscription();

        // 验证运行状态
        assertFalse(webSocketManager.isRunning());
    }

    @Test
    void testStartListenersByType() {
        // 启动深度数据监听器
        boolean result = webSocketManager.startListenersByType("depth");

        // 验证深度数据监听器已启动
        verify(depthDataListener).startDepthDataSubscription();
        verify(klineDataListener, never()).startKlineDataSubscription();
        verify(tradeDataListener, never()).startTradeDataSubscription();

        // 验证结果
        assertTrue(result);

        // 启动K线数据监听器
        result = webSocketManager.startListenersByType("kline");

        // 验证K线数据监听器已启动
        verify(klineDataListener).startKlineDataSubscription();

        // 验证结果
        assertTrue(result);

        // 启动交易数据监听器
        result = webSocketManager.startListenersByType("trade");

        // 验证交易数据监听器已启动
        verify(tradeDataListener).startTradeDataSubscription();

        // 验证结果
        assertTrue(result);

        // 启动未知类型监听器
        result = webSocketManager.startListenersByType("unknown");

        // 验证结果
        assertFalse(result);
    }

    @Test
    void testStopListenersByType() {
        // 停止深度数据监听器
        boolean result = webSocketManager.stopListenersByType("depth");

        // 验证深度数据监听器已停止
        verify(depthDataListener).stopDepthDataSubscription();
        verify(klineDataListener, never()).stopKlineDataSubscription();
        verify(tradeDataListener, never()).stopTradeDataSubscription();

        // 验证结果
        assertTrue(result);

        // 停止K线数据监听器
        result = webSocketManager.stopListenersByType("kline");

        // 验证K线数据监听器已停止
        verify(klineDataListener).stopKlineDataSubscription();

        // 验证结果
        assertTrue(result);

        // 停止交易数据监听器
        result = webSocketManager.stopListenersByType("trade");

        // 验证交易数据监听器已停止
        verify(tradeDataListener).stopTradeDataSubscription();

        // 验证结果
        assertTrue(result);

        // 停止未知类型监听器
        result = webSocketManager.stopListenersByType("unknown");

        // 验证结果
        assertFalse(result);
    }

    @Test
    void testRegisterConnection() {
        // 注册连接
        webSocketManager.registerConnection(1, "test-connection");

        // 验证健康检查器已注册连接
        verify(healthChecker).registerConnection(1, "test-connection");
    }

    @Test
    void testUnregisterConnection() {
        // 先注册连接
        webSocketManager.registerConnection(1, "test-connection");

        // 注销连接
        webSocketManager.unregisterConnection("test-connection");

        // 验证健康检查器已注销连接
        verify(healthChecker).unregisterConnection(1);
    }

    @Test
    void testGetActiveConnectionCount() {
        // 模拟活跃连接数
        when(connectionPool.getActiveConnectionCount()).thenReturn(5);

        // 获取活跃连接数
        int count = webSocketManager.getActiveConnectionCount();

        // 验证结果
        assertEquals(5, count);
    }

    @Test
    void testGetTotalConnectionCount() {
        // 模拟总连接数
        when(connectionPool.getTotalConnectionCount()).thenReturn(10);

        // 获取总连接数
        int count = webSocketManager.getTotalConnectionCount();

        // 验证结果
        assertEquals(10, count);
    }

    @Test
    void testGetHealthChecker() {
        // 获取健康检查器
        WebSocketHealthChecker checker = webSocketManager.getHealthChecker();

        // 验证结果
        assertNotNull(checker);
        assertSame(healthChecker, checker);
    }
}