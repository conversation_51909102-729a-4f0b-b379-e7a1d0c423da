package com.crypto.trading.trade.model.entity.order;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 订单实体类
 * <p>
 * 表示一个交易订单
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("t_order")
public class OrderEntity {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 订单ID
     */
    private String orderId;

    /**
     * 客户端订单ID
     */
    private String clientOrderId;

    /**
     * 交易对
     */
    private String symbol;

    /**
     * 买卖方向
     */
    private String side;

    /**
     * 持仓方向（"LONG"、"SHORT"或"BOTH"）
     */
    private String positionSide;

    /**
     * 订单类型
     */
    private String orderType;

    /**
     * 订单数量
     */
    private Double quantity;

    /**
     * 订单价格
     */
    private Double price;

    /**
     * 已执行数量
     */
    private Double executedQuantity;

    /**
     * 成交价格
     */
    private Double executedPrice;

    /**
     * 订单状态
     */
    private String status;

    /**
     * 策略ID
     */
    private String strategy;

    /**
     * 信号ID
     */
    private String signalId;

    /**
     * 错误代码
     */
    private String errorCode;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    @TableField(fill = FieldFill.INSERT)
    private Long createdTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updatedTime;

    /**
     * 执行时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    private Long executedTime;

    /**
     * 删除标记
     */
    @TableLogic
    private Integer deleted;
    
    /**
     * 交易时效（GTC、IOC、FOK等）
     */
    private String timeInForce;

    /**
     * 备注
     */
    private String remark;

    /**
     * 获取订单类型
     *
     * @return 订单类型
     */
    public String getOrderType() {
        return this.orderType;
    }

    /**
     * 获取订单类型（别名）
     *
     * @return 订单类型
     */
    public String getType() {
        return this.orderType;
    }
    
    /**
     * 设置订单类型
     *
     * @param orderType 订单类型
     */
    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }
    
    /**
     * 设置订单类型（别名）
     *
     * @param type 订单类型
     */
    public void setType(String type) {
        this.orderType = type;
    }

    /**
     * 获取策略ID
     *
     * @return 策略ID
     */
    public String getStrategy() {
        return this.strategy;
    }
    
    /**
     * 获取策略ID（别名）
     *
     * @return 策略ID
     */
    public String getStrategyId() {
        return this.strategy;
    }
    
    /**
     * 设置策略ID
     *
     * @param strategy 策略ID
     */
    public void setStrategy(String strategy) {
        this.strategy = strategy;
    }
    
    /**
     * 设置策略ID（别名）
     *
     * @param strategyId 策略ID
     */
    public void setStrategyId(String strategyId) {
        this.strategy = strategyId;
    }

    /**
     * 获取删除标记
     *
     * @return 删除标记
     */
    public Integer getIsDeleted() {
        return this.deleted;
    }
    
    /**
     * 获取订单ID
     *
     * @return 订单ID
     */
    public String getOrderId() {
        return this.orderId;
    }

    /**
     * 设置订单ID
     *
     * @param orderId 订单ID
     */
    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    /**
     * 获取客户端订单ID
     *
     * @return 客户端订单ID
     */
    public String getClientOrderId() {
        return this.clientOrderId;
    }

    /**
     * 设置客户端订单ID
     *
     * @param clientOrderId 客户端订单ID
     */
    public void setClientOrderId(String clientOrderId) {
        this.clientOrderId = clientOrderId;
    }

    /**
     * 获取交易对
     *
     * @return 交易对
     */
    public String getSymbol() {
        return this.symbol;
    }

    /**
     * 设置交易对
     *
     * @param symbol 交易对
     */
    public void setSymbol(String symbol) {
        this.symbol = symbol;
    }

    /**
     * 获取买卖方向
     *
     * @return 买卖方向
     */
    public String getSide() {
        return this.side;
    }

    /**
     * 设置买卖方向
     *
     * @param side 买卖方向
     */
    public void setSide(String side) {
        this.side = side;
    }

    /**
     * 获取持仓方向
     *
     * @return 持仓方向
     */
    public String getPositionSide() {
        return this.positionSide;
    }

    /**
     * 设置持仓方向
     *
     * @param positionSide 持仓方向
     */
    public void setPositionSide(String positionSide) {
        this.positionSide = positionSide;
    }

    /**
     * 获取订单数量
     *
     * @return 订单数量
     */
    public Double getQuantity() {
        return this.quantity;
    }

    /**
     * 设置订单数量
     *
     * @param quantity 订单数量
     */
    public void setQuantity(Double quantity) {
        this.quantity = quantity;
    }

    /**
     * 获取订单价格
     *
     * @return 订单价格
     */
    public Double getPrice() {
        return this.price;
    }

    /**
     * 设置订单价格
     *
     * @param price 订单价格
     */
    public void setPrice(Double price) {
        this.price = price;
    }

    /**
     * 获取已执行数量
     *
     * @return 已执行数量
     */
    public Double getExecutedQuantity() {
        return this.executedQuantity;
    }

    /**
     * 设置已执行数量
     *
     * @param executedQuantity 已执行数量
     */
    public void setExecutedQuantity(Double executedQuantity) {
        this.executedQuantity = executedQuantity;
    }

    /**
     * 获取成交价格
     *
     * @return 成交价格
     */
    public Double getExecutedPrice() {
        return this.executedPrice;
    }

    /**
     * 设置成交价格
     *
     * @param executedPrice 成交价格
     */
    public void setExecutedPrice(Double executedPrice) {
        this.executedPrice = executedPrice;
    }

    /**
     * 获取订单状态
     *
     * @return 订单状态
     */
    public String getStatus() {
        return this.status;
    }

    /**
     * 设置订单状态
     *
     * @param status 订单状态
     */
    public void setStatus(String status) {
        this.status = status;
    }

    /**
     * 获取信号ID
     *
     * @return 信号ID
     */
    public String getSignalId() {
        return this.signalId;
    }

    /**
     * 设置信号ID
     *
     * @param signalId 信号ID
     */
    public void setSignalId(String signalId) {
        this.signalId = signalId;
    }

    /**
     * 获取错误代码
     *
     * @return 错误代码
     */
    public String getErrorCode() {
        return this.errorCode;
    }

    /**
     * 设置错误代码
     *
     * @param errorCode 错误代码
     */
    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    /**
     * 获取错误信息
     *
     * @return 错误信息
     */
    public String getErrorMessage() {
        return this.errorMessage;
    }

    /**
     * 设置错误信息
     *
     * @param errorMessage 错误信息
     */
    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    /**
     * 获取创建时间
     *
     * @return 创建时间
     */
    public Long getCreatedTime() {
        return this.createdTime;
    }

    /**
     * 设置创建时间
     *
     * @param createdTime 创建时间
     */
    public void setCreatedTime(Long createdTime) {
        this.createdTime = createdTime;
    }

    /**
     * 获取更新时间
     *
     * @return 更新时间
     */
    public Long getUpdatedTime() {
        return this.updatedTime;
    }

    /**
     * 设置更新时间
     *
     * @param updatedTime 更新时间
     */
    public void setUpdatedTime(Long updatedTime) {
        this.updatedTime = updatedTime;
    }

    /**
     * 获取执行时间
     *
     * @return 执行时间
     */
    public Long getExecutedTime() {
        return this.executedTime;
    }

    /**
     * 设置执行时间
     *
     * @param executedTime 执行时间
     */
    public void setExecutedTime(Long executedTime) {
        this.executedTime = executedTime;
    }
    
    /**
     * 获取交易时效
     *
     * @return 交易时效
     */
    public String getTimeInForce() {
        return this.timeInForce;
    }
    
    /**
     * 设置交易时效
     *
     * @param timeInForce 交易时效
     */
    public void setTimeInForce(String timeInForce) {
        this.timeInForce = timeInForce;
    }
    
    /**
     * 订单实体构建器
     * <p>
     * 用于构建订单实体对象
     * </p>
     */
    public static class Builder {
        private Long id;
        private String orderId;
        private String clientOrderId;
        private String symbol;
        private String side;
        private String positionSide;
        private String orderType;
        private Double quantity;
        private Double price;
        private Double executedQuantity;
        private Double executedPrice;
        private String status;
        private String strategy;
        private String signalId;
        private String errorCode;
        private String errorMessage;
        private String remark;
        private Long createdTime;
        private Long updatedTime;
        private Long executedTime;
        private Integer deleted;
        private String timeInForce;

        public Builder id(Long id) {
            this.id = id;
            return this;
        }

        public Builder orderId(String orderId) {
            this.orderId = orderId;
            return this;
        }

        public Builder clientOrderId(String clientOrderId) {
            this.clientOrderId = clientOrderId;
            return this;
        }

        public Builder symbol(String symbol) {
            this.symbol = symbol;
            return this;
        }

        public Builder side(String side) {
            this.side = side;
            return this;
        }

        public Builder positionSide(String positionSide) {
            this.positionSide = positionSide;
            return this;
        }

        public Builder type(String orderType) {
            this.orderType = orderType;
            return this;
        }
        
        /**
         * 设置订单类型（别名方法）
         *
         * @param orderType 订单类型
         * @return Builder实例
         */
        public Builder orderType(String orderType) {
            this.orderType = orderType;
            return this;
        }

        public Builder quantity(Double quantity) {
            this.quantity = quantity;
            return this;
        }

        public Builder price(Double price) {
            this.price = price;
            return this;
        }

        public Builder executedQuantity(Double executedQuantity) {
            this.executedQuantity = executedQuantity;
            return this;
        }

        public Builder executedPrice(Double executedPrice) {
            this.executedPrice = executedPrice;
            return this;
        }

        public Builder status(String status) {
            this.status = status;
            return this;
        }

        public Builder strategyId(String strategy) {
            this.strategy = strategy;
            return this;
        }
        
        /**
         * 设置策略ID（别名方法）
         *
         * @param strategy 策略ID
         * @return Builder实例
         */
        public Builder strategy(String strategy) {
            this.strategy = strategy;
            return this;
        }

        public Builder signalId(String signalId) {
            this.signalId = signalId;
            return this;
        }

        public Builder errorCode(String errorCode) {
            this.errorCode = errorCode;
            return this;
        }

        public Builder errorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
            return this;
        }

        public Builder remark(String remark) {
            this.remark = remark;
            return this;
        }

        public Builder createdTime(Long createdTime) {
            this.createdTime = createdTime;
            return this;
        }

        public Builder updatedTime(Long updatedTime) {
            this.updatedTime = updatedTime;
            return this;
        }

        public Builder executedTime(Long executedTime) {
            this.executedTime = executedTime;
            return this;
        }

        public Builder deleted(Integer deleted) {
            this.deleted = deleted;
            return this;
        }
        
        /**
         * 设置交易时效
         *
         * @param timeInForce 交易时效
         * @return Builder实例
         */
        public Builder timeInForce(String timeInForce) {
            this.timeInForce = timeInForce;
            return this;
        }

        public OrderEntity build() {
            OrderEntity order = new OrderEntity();
            order.id = this.id;
            order.orderId = this.orderId;
            order.clientOrderId = this.clientOrderId;
            order.symbol = this.symbol;
            order.side = this.side;
            order.positionSide = this.positionSide;
            order.orderType = this.orderType;
            order.quantity = this.quantity;
            order.price = this.price;
            order.executedQuantity = this.executedQuantity;
            order.executedPrice = this.executedPrice;
            order.status = this.status;
            order.strategy = this.strategy;
            order.signalId = this.signalId;
            order.errorCode = this.errorCode;
            order.errorMessage = this.errorMessage;
            order.remark = this.remark;
            order.createdTime = this.createdTime;
            order.updatedTime = this.updatedTime;
            order.executedTime = this.executedTime;
            order.deleted = this.deleted;
            order.timeInForce = this.timeInForce;
            return order;
        }
    }
}