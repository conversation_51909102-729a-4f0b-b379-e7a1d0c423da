# Crypto ML Strategy 集成测试框架

## 概述

这是一个完整的集成测试框架，专门为crypto_ml_strategy模块设计，支持端到端的数据流测试、ML管道测试、风险管理测试和系统可靠性测试。

## 功能特性

### 🔧 核心组件

- **测试配置系统**: 支持多环境配置（dev/staging/prod-like）
- **测试数据生成器**: 生成多时间框架的加密货币市场数据
- **测试执行框架**: 支持并行执行和依赖管理的测试编排器
- **测试报告系统**: 生成HTML/JSON/XML多格式报告
- **Mock服务**: 模拟Java API和Kafka服务
- **数据流测试工具**: 专门的数据流验证和性能监控工具

### 📊 测试套件

1. **数据流集成测试** (`data_flow_integration_test_suite.py`)
   - API客户端集成测试
   - Kafka数据流测试
   - 多时间框架同步测试
   - 数据质量验证测试
   - 缓存性能测试
   - 端到端数据管道测试

2. **ML管道集成测试** (`ml_pipeline_integration_test_suite.py`)
   - 数据摄取管道测试
   - 特征工程管道测试
   - 模型训练管道测试
   - 预测引擎测试
   - 信号生成管道测试

3. **DeepSeek蒸馏模型测试** (`test_deepseek_distillation.py`)
   - 蒸馏模型训练测试
   - 推理性能测试
   - 知识转移有效性测试
   - 模型压缩分析测试

4. **在线学习集成测试** (`test_online_learning_integration.py`)
   - 增量学习机制测试
   - 模型版本管理测试
   - 性能监控测试
   - 自适应学习策略测试

5. **风险管理集成测试** (`risk_management_integration_test_suite.py`)
   - 风险控制引擎测试
   - 市场数据集成测试
   - 投资组合数据集成测试
   - 仓位管理集成测试
   - 止损止盈集成测试
   - 回撤控制集成测试
   - 紧急风险处理测试
   - 风险配置测试
   - 完整风险管理工作流测试

6. **风险控制系统专项测试** (`test_risk_control_system.py`)
   - 风险评估器功能测试
   - 风险事件处理测试
   - 紧急风险控制测试
   - 风险控制性能测试
   - 并发风险操作测试
   - 风险控制恢复机制测试

7. **仓位管理专项测试** (`test_position_management.py`)
   - 仓位计算方法测试
   - 仓位限制执行测试
   - 动态仓位调整测试
   - 仓位风险指标测试
   - 仓位相关性分析测试

8. **示例测试套件** (`example_test_suite.py`)
   - 基础功能测试
   - API集成测试
   - Kafka消息测试
   - 同步操作测试
   - 依赖关系测试

## 快速开始

### 安装依赖

```bash
pip install -r requirements.txt
```

### 基本使用

#### 1. 运行所有集成测试

```bash
# 运行所有测试套件
python tests/integration/run_integration_tests.py

# 运行特定测试套件
python tests/integration/run_integration_tests.py --suites data_flow ml_pipeline example

# 指定测试环境
python tests/integration/run_integration_tests.py --environment staging
```

#### 2. 运行数据流专项测试

```bash
# 运行数据流集成测试
python tests/integration/run_data_flow_tests.py

# 运行性能基准测试
python tests/integration/run_data_flow_tests.py --benchmark

# 指定日志级别
python tests/integration/run_data_flow_tests.py --log-level DEBUG
```

#### 3. 运行ML管道专项测试

```bash
# 运行ML管道集成测试
python tests/integration/run_ml_pipeline_tests.py

# 运行特定ML测试套件
python tests/integration/run_ml_pipeline_tests.py --suites ml_pipeline deepseek online_learning

# 指定测试环境
python tests/integration/run_ml_pipeline_tests.py --environment staging
```

#### 4. 运行风险管理专项测试

```bash
# 运行风险管理集成测试
python tests/integration/run_risk_management_tests.py

# 运行特定风险管理测试套件
python tests/integration/run_risk_management_tests.py --test-type unit
python tests/integration/run_risk_management_tests.py --test-type scenario
python tests/integration/run_risk_management_tests.py --test-type performance

# 运行所有风险管理测试并生成报告
python tests/integration/run_risk_management_tests.py --test-type all --save-results --generate-report

# 验证风险管理测试组件
python tests/integration/validate_risk_management_tests.py
```

#### 3. 验证测试环境

```bash
# 验证测试环境配置
python tests/integration/run_integration_tests.py --validate-only

# 仅生成测试数据
python tests/integration/run_integration_tests.py --generate-data-only
```

## 配置管理

### 环境配置

测试框架支持三种环境配置：

- **dev**: 开发环境（默认）
- **staging**: 预发布环境
- **prod-like**: 生产类似环境

### 配置文件

配置文件位于 `tests/integration/config/` 目录：

- `test_config_dev.yaml`: 开发环境配置
- `test_config_staging.yaml`: 预发布环境配置
- `test_config_prod-like.yaml`: 生产类似环境配置

### 环境变量覆盖

可以使用环境变量覆盖配置文件中的设置：

```bash
export CRYPTO_TEST_DB_HOST=localhost
export CRYPTO_TEST_DB_PORT=3306
export CRYPTO_TEST_KAFKA_SERVERS=localhost:9092
export CRYPTO_TEST_API_BASE_URL=http://localhost:8080/api
```

## 测试数据

### 自动生成

测试框架会自动生成以下类型的测试数据：

- **多时间框架数据**: 1m, 5m, 15m, 1h, 4h, 1d
- **多交易对数据**: BTCUSDT, ETHUSDT
- **异常数据**: 包含各种边界情况和异常情况
- **性能测试数据**: 大量数据用于性能测试

### 数据存储

生成的测试数据存储在 `tests/integration/data/` 目录：

```
tests/integration/data/
├── btcusdt/
│   ├── 1m.csv
│   ├── 5m.csv
│   └── ...
├── ethusdt/
│   ├── 1m.csv
│   ├── 5m.csv
│   └── ...
├── edge_cases/
│   ├── empty_dataset.csv
│   ├── extreme_volatility.csv
│   └── ...
└── performance/
    └── large_dataset_100mb.csv
```

## 性能监控

### 性能指标

测试框架监控以下性能指标：

- **延迟指标**: 平均延迟、最大延迟
- **吞吐量指标**: 每秒操作数、错误率
- **资源使用**: 内存使用、CPU使用
- **缓存性能**: 命中率、未命中率

### 性能阈值

默认性能阈值：

- 信号生成延迟: < 100ms
- 系统正常运行时间: > 99.9%
- 数据处理准确率: < 0.1%丢失率
- 内存使用: < 2GB峰值
- CPU利用率: < 80%峰值
- 缓存命中率: > 90%

## 报告生成

### 报告格式

测试框架生成多种格式的报告：

- **HTML报告**: 包含图表和可视化的详细报告
- **JSON报告**: 机器可读的结构化报告
- **XML报告**: JUnit兼容的XML格式报告
- **性能分析报告**: 专门的性能分析Markdown报告

### 报告位置

报告文件存储在 `tests/integration/reports/` 目录：

```
tests/integration/reports/
├── integration_test_dev_20240619_143022.html
├── integration_test_dev_20240619_143022.json
├── integration_test_dev_20240619_143022.xml
└── data_flow/
    ├── data_flow_integration_dev_20240619_143022.html
    └── performance_analysis_20240619_143022.md
```

## 高级功能

### 自定义测试套件

创建自定义测试套件：

```python
from tests.integration.utils.test_orchestrator import TestCase, TestSuite, TestPriority

def my_custom_test():
    # 测试逻辑
    return {"test_result": "success"}

def create_custom_test_suite():
    test_cases = [
        TestCase(
            test_id="custom.my_test",
            name="我的自定义测试",
            description="测试自定义功能",
            test_function=my_custom_test,
            priority=TestPriority.HIGH,
            timeout=60,
            tags=["custom", "integration"]
        )
    ]
    
    return TestSuite(
        suite_id="custom_suite",
        name="自定义测试套件",
        description="自定义功能测试",
        test_cases=test_cases
    )
```

### 错误注入测试

使用Mock服务进行错误注入测试：

```python
from tests.integration.fixtures.mock_services import get_mock_manager, ErrorInjectionRule

# 获取Mock管理器
mock_manager = get_mock_manager()
java_api = mock_manager.get_java_api()

# 添加错误注入规则
error_rule = ErrorInjectionRule(
    endpoint="/market/data",
    error_type="timeout",
    probability=0.1,  # 10%概率
    error_message="API timeout"
)
java_api.add_error_rule(error_rule)
```

### 性能基准测试

运行性能基准测试：

```python
from tests.integration.run_data_flow_tests import DataFlowTestRunner

runner = DataFlowTestRunner(environment="dev")
benchmark_results = await runner.run_performance_benchmark()
```

## 故障排除

### 常见问题

1. **测试环境连接失败**
   ```bash
   # 验证环境配置
   python tests/integration/run_integration_tests.py --validate-only
   ```

2. **Mock服务启动失败**
   ```bash
   # 检查端口占用
   netstat -an | grep 8080
   netstat -an | grep 9092
   ```

3. **测试数据生成失败**
   ```bash
   # 重新生成测试数据
   python tests/integration/run_integration_tests.py --generate-data-only
   ```

4. **性能测试超时**
   ```bash
   # 增加超时时间
   export CRYPTO_TEST_TIMEOUT=600  # 10分钟
   ```

### 日志调试

启用详细日志：

```bash
python tests/integration/run_integration_tests.py --log-level DEBUG
```

查看特定组件日志：

```python
from loguru import logger

# 配置日志过滤
logger.add("test_debug.log", filter="tests.integration.data", level="DEBUG")
```

## 贡献指南

### 添加新测试

1. 在相应的测试套件文件中添加测试函数
2. 更新测试套件的test_cases列表
3. 添加适当的文档和注释
4. 运行测试验证功能

### 添加新测试套件

1. 在 `tests/integration/suites/` 目录创建新文件
2. 实现测试套件创建函数
3. 在 `run_integration_tests.py` 中注册新套件
4. 更新 `__init__.py` 导出新组件

### 代码规范

- 使用类型注解
- 添加完整的文档字符串
- 遵循PEP 8编码规范
- 包含适当的错误处理
- 添加日志记录

## 许可证

本项目采用 MIT 许可证。详见 LICENSE 文件。

## 联系方式

如有问题或建议，请联系开发团队或提交 Issue。