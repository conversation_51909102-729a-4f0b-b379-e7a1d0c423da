package com.crypto.trading.bootstrap.lifecycle;

import com.crypto.trading.bootstrap.manager.StrategyManager;
import com.crypto.trading.bootstrap.order.StartupOrderManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

/**
 * 应用生命周期管理
 * 负责监听应用程序的生命周期事件并执行相应的操作
 */
@Component
public class ApplicationLifecycle implements ApplicationListener<ApplicationReadyEvent> {
    
    private static final Logger log = LoggerFactory.getLogger(ApplicationLifecycle.class);
    
    /**
     * 启动顺序管理器
     */
    private final StartupOrderManager startupOrderManager;
    
    /**
     * 关闭钩子
     */
    private final ShutdownHook shutdownHook;
    
    /**
     * 策略管理器
     */
    private final StrategyManager strategyManager;
    
    /**
     * 构造函数
     * 
     * @param startupOrderManager 启动顺序管理器
     * @param shutdownHook 关闭钩子
     * @param strategyManager 策略管理器
     */
    public ApplicationLifecycle(
            StartupOrderManager startupOrderManager,
            ShutdownHook shutdownHook,
            StrategyManager strategyManager) {
        this.startupOrderManager = startupOrderManager;
        this.shutdownHook = shutdownHook;
        this.strategyManager = strategyManager;
    }
    
    @Override
    public void onApplicationEvent(ApplicationReadyEvent event) {
        try {
            log.info("应用程序就绪事件触发，开始初始化模块...");
            
            // 初始化所有模块
            try {
                startupOrderManager.initializeAll();
                log.info("模块初始化完成");
            } catch (Exception e) {
                // 捕获初始化异常，但允许应用程序继续启动
                log.error("模块初始化过程中发生异常: {}", e.getMessage(), e);
                log.warn("应用程序将继续启动，但某些功能可能不可用");
            }
            
            // 启动Python策略模块
            startPythonStrategy();
            
            log.info("应用程序启动完成");
            
            // 注册虚拟线程的关闭监听器
            registerVirtualThreadShutdownListener();
            
        } catch (Exception e) {
            log.error("应用程序初始化过程中发生异常: {}", e.getMessage(), e);
            log.warn("应用程序将继续运行，但某些功能可能不可用");
            
            // 注册虚拟线程的关闭监听器，确保即使在异常情况下也能正常关闭
            try {
                registerVirtualThreadShutdownListener();
            } catch (Exception ex) {
                log.error("注册虚拟线程关闭监听器时发生异常: {}", ex.getMessage(), ex);
            }
        }
    }
    
    /**
     * 启动Python策略模块
     */
    private void startPythonStrategy() {
        log.info("正在启动Python策略模块...");
        try {
            boolean started = strategyManager.startStrategy();
            if (started) {
                log.info("Python策略模块启动成功或已配置为禁用。");
            } else {
                log.warn("启动Python策略模块失败，但应用程序将继续运行。请检查相关日志获取详细信息。");
                // 不再抛出异常，允许应用程序继续运行
            }
        } catch (Exception e) {
            // 捕获所有可能的异常，确保应用程序不会因策略模块启动失败而终止
            log.error("启动Python策略模块时发生异常，但应用程序将继续运行: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 注册虚拟线程的关闭监听器
     * 用于在JVM关闭前等待虚拟线程完成
     */
    private void registerVirtualThreadShutdownListener() {
        Thread shutdownListener = Thread.ofVirtual().name("VirtualThreadShutdownListener").start(() -> {
            // 这个虚拟线程会一直运行直到JVM关闭
            try {
                // 等待关闭信号
                while (!shutdownHook.isShuttingDown()) {
                    Thread.sleep(1000);
                }
                
                log.info("等待所有虚拟线程完成...");
                
                // 停止Python策略模块
                if (strategyManager.isRunning()) {
                    log.info("正在停止Python策略模块...");
                    strategyManager.stopStrategy();
                }
                
                // 等待其他虚拟线程完成
                // 这里可以添加更多等待逻辑
                
                log.info("所有虚拟线程已完成");
                
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.warn("虚拟线程关闭监听器被中断");
            }
        });
        
        log.info("已注册虚拟线程关闭监听器: {}", shutdownListener.getName());
    }
}
