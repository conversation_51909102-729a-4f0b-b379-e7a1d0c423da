{"namespace": "com.crypto.trading.common.avro", "type": "record", "name": "DepthData", "doc": "深度数据的Avro模式定义", "fields": [{"name": "symbol", "type": "string", "doc": "交易对符号，如BTCUSDT"}, {"name": "updateId", "type": "long", "doc": "更新ID"}, {"name": "timestamp", "type": "long", "doc": "消息生成时间（毫秒时间戳）"}, {"name": "bids", "type": {"type": "array", "items": {"type": "record", "name": "PriceLevel", "fields": [{"name": "price", "type": "double", "doc": "价格"}, {"name": "quantity", "type": "double", "doc": "数量"}]}}, "doc": "买单列表"}, {"name": "asks", "type": {"type": "array", "items": "PriceLevel"}, "doc": "卖单列表"}, {"name": "source", "type": "string", "doc": "数据来源", "default": "binance"}]}