#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Hematread 特征提取模块

实现Hematread策略特征提取，该策略结合动量和趋势强度指标，
用于识别市场的力量变化和潜在的转折点。
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Union


class HematreadFeatureExtractor:
    """Hematread特征提取器，用于分析动量和趋势强度"""

    def __init__(self, config: Dict = None):
        """
        初始化Hematread特征提取器
        
        Args:
            config: 配置参数字典，包括以下可选参数：
                - ma_periods: 移动平均周期列表，默认[9, 21, 50, 200]
                - volume_factor: 成交量因子，默认2.0
                - trend_strength_threshold: 趋势强度阈值，默认0.6
        """
        self.logger = logging.getLogger('cryptoMlStrategy.indicators.hematread')
        
        # 默认配置
        self.config = {
            'ma_periods': [9, 21, 50, 200],
            'volume_factor': 2.0,
            'trend_strength_threshold': 0.6
        }
        
        # 更新用户配置
        if config:
            self.config.update(config)
            
        self.logger.info("Hematread特征提取器初始化完成")

    def calculate_momentum(self, df: pd.DataFrame, periods: List[int], price_col: str = 'close') -> pd.DataFrame:
        """
        计算多周期价格动量
        
        Args:
            df: 价格数据DataFrame
            periods: 动量周期列表
            price_col: 价格列名称
            
        Returns:
            添加了动量特征的DataFrame
        """
        result_df = df.copy()
        
        for period in periods:
            # ROC (Rate of Change) 动量指标
            momentum_col = f'hema_momentum_{period}'
            result_df[momentum_col] = result_df[price_col].pct_change(period)
            
            # 添加动量EMA平滑
            result_df[f'{momentum_col}_ema'] = result_df[momentum_col].ewm(span=period//2 if period > 2 else 2).mean()
        
        return result_df

    def calculate_trend_strength(self, df: pd.DataFrame, 
                                 ma_periods: List[int] = None, 
                                 price_col: str = 'close') -> pd.DataFrame:
        """
        计算趋势强度指标
        
        Args:
            df: 价格数据DataFrame
            ma_periods: 移动平均周期列表
            price_col: 价格列名称
            
        Returns:
            添加了趋势强度特征的DataFrame
        """
        result_df = df.copy()
        ma_periods = ma_periods or self.config['ma_periods']
        
        # 计算各周期移动平均线
        for period in ma_periods:
            result_df[f'hema_ma_{period}'] = result_df[price_col].rolling(window=period).mean()
        
        # 计算短期和长期移动平均线之间的关系
        if len(ma_periods) >= 2:
            # 按照周期从小到大排序
            sorted_periods = sorted(ma_periods)
            
            # 计算相邻移动平均线的对齐情况
            for i in range(len(sorted_periods)-1):
                short_period = sorted_periods[i]
                long_period = sorted_periods[i+1]
                
                # 计算短期MA相对于长期MA的位置
                ratio_col = f'hema_ma_ratio_{short_period}_{long_period}'
                result_df[ratio_col] = result_df[f'hema_ma_{short_period}'] / result_df[f'hema_ma_{long_period}'] - 1.0
                
                # 计算短期MA和长期MA的交叉信号
                result_df[f'hema_ma_cross_{short_period}_{long_period}'] = np.where(
                    result_df[f'hema_ma_{short_period}'] > result_df[f'hema_ma_{long_period}'], 1, -1)
        
        # 计算整体趋势强度
        if len(ma_periods) >= 2:
            # 使用所有MA对的比率计算趋势强度
            ratio_cols = [col for col in result_df.columns if col.startswith('hema_ma_ratio_')]
            if ratio_cols:
                # 将比率转换为0-1的强度值
                for col in ratio_cols:
                    # 使用sigmoid函数将比率转换为0-1之间的值
                    result_df[f'{col}_strength'] = 1 / (1 + np.exp(-result_df[col] * 10))
                
                # 计算整体趋势强度
                strength_cols = [col for col in result_df.columns if col.endswith('_strength')]
                if strength_cols:
                    result_df['hema_trend_strength'] = result_df[strength_cols].mean(axis=1)
        
        return result_df

    def calculate_volume_factors(self, df: pd.DataFrame, 
                                volume_col: str = 'volume',
                                price_col: str = 'close') -> pd.DataFrame:
        """
        计算成交量因子，用于确认价格动量
        
        Args:
            df: 价格和成交量数据DataFrame
            volume_col: 成交量列名称
            price_col: 价格列名称
            
        Returns:
            添加了成交量因子的DataFrame
        """
        if volume_col not in df.columns:
            self.logger.warning(f"成交量列 {volume_col} 不存在，无法计算成交量因子")
            return df
            
        result_df = df.copy()
        volume_factor = self.config['volume_factor']
        
        # 计算成交量的移动平均
        result_df['hema_volume_ma'] = result_df[volume_col].rolling(window=20).mean()
        
        # 计算当前成交量相对于MA的倍数
        result_df['hema_volume_ratio'] = result_df[volume_col] / result_df['hema_volume_ma']
        
        # 计算价格变化方向
        result_df['hema_price_direction'] = np.sign(result_df[price_col].diff())
        
        # 计算成交量确认指标
        # 当价格上涨且成交量放大，或价格下跌且成交量萎缩时，视为强势确认
        result_df['hema_volume_confirm'] = np.where(
            (result_df['hema_price_direction'] > 0) & (result_df['hema_volume_ratio'] > volume_factor), 1,
            np.where((result_df['hema_price_direction'] < 0) & (result_df['hema_volume_ratio'] < 1/volume_factor), -1, 0)
        )
        
        # 添加成交量强度
        result_df['hema_volume_strength'] = (result_df['hema_volume_ratio'] - 1) * result_df['hema_price_direction']
        
        # 平滑成交量强度指标
        result_df['hema_volume_strength_smooth'] = result_df['hema_volume_strength'].ewm(span=5).mean()
        
        return result_df

    def combine_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        组合所有Hematread指标，生成最终信号
        
        Args:
            df: 已包含各项指标的DataFrame
            
        Returns:
            添加了最终Hematread信号的DataFrame
        """
        result_df = df.copy()
        
        # 检查必要的列是否存在
        required_cols = ['hema_trend_strength', 'hema_momentum_21_ema', 'hema_volume_strength_smooth']
        missing_cols = [col for col in required_cols if col not in result_df.columns]
        
        if missing_cols:
            self.logger.warning(f"缺少必要的列: {missing_cols}，无法生成完整Hematread信号")
            # 添加缺失的列，使后续代码不会出错
            for col in missing_cols:
                result_df[col] = 0.0
        
        # 计算Hematread综合得分
        # 趋势强度 (40%) + 动量 (40%) + 成交量确认 (20%)
        result_df['hema_score'] = (
            result_df['hema_trend_strength'] * 0.4 +
            np.sign(result_df['hema_momentum_21_ema']) * min(abs(result_df['hema_momentum_21_ema']), 0.1) / 0.1 * 0.4 +
            result_df['hema_volume_strength_smooth'] * 0.2
        )
        
        # 规范化得分到 -1 到 1 之间
        result_df['hema_score'] = result_df['hema_score'].clip(-1, 1)
        
        # 生成信号: 1 (买入), -1 (卖出), 0 (持有)
        trend_threshold = self.config['trend_strength_threshold']
        result_df['hema_signal'] = np.where(
            result_df['hema_score'] > trend_threshold, 1,
            np.where(result_df['hema_score'] < -trend_threshold, -1, 0)
        )
        
        # 添加趋势确认度（用于风险管理）
        result_df['hema_confidence'] = abs(result_df['hema_score'])
        
        return result_df

    def extract_features(self, df: pd.DataFrame, price_col: str = 'close', volume_col: str = 'volume') -> pd.DataFrame:
        """
        从DataFrame中提取Hematread特征
        
        Args:
            df: 包含价格和成交量数据的DataFrame
            price_col: 价格列名称
            volume_col: 成交量列名称
            
        Returns:
            添加了Hematread特征的DataFrame
        """
        if df is None or df.empty:
            return df
            
        try:
            # 1. 计算动量指标
            result_df = self.calculate_momentum(df, self.config['ma_periods'], price_col)
            
            # 2. 计算趋势强度
            result_df = self.calculate_trend_strength(result_df, self.config['ma_periods'], price_col)
            
            # 3. 计算成交量因子
            if volume_col in df.columns:
                result_df = self.calculate_volume_factors(result_df, volume_col, price_col)
            
            # 4. 组合指标生成信号
            result_df = self.combine_indicators(result_df)
            
            return result_df
            
        except Exception as e:
            self.logger.error(f"Hematread特征提取失败: {e}")
            return df