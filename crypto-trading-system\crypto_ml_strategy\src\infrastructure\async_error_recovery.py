#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
异步错误恢复服务模块

提供异步任务的错误恢复、重试机制和故障转移功能。
"""

import asyncio
import time
import traceback
from typing import Dict, Any, List, Optional, Callable, Awaitable, Union
from dataclasses import dataclass, field
from enum import Enum
from loguru import logger


class RecoveryStrategy(Enum):
    """恢复策略枚举"""
    IMMEDIATE_RETRY = "immediate_retry"
    EXPONENTIAL_BACKOFF = "exponential_backoff"
    LINEAR_BACKOFF = "linear_backoff"
    CIRCUIT_BREAKER = "circuit_breaker"
    FALLBACK = "fallback"


class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    RETRYING = "retrying"
    CANCELLED = "cancelled"


@dataclass
class RetryConfig:
    """重试配置"""
    max_retries: int = 3
    base_delay: float = 1.0
    max_delay: float = 60.0
    backoff_multiplier: float = 2.0
    jitter: bool = True
    strategy: RecoveryStrategy = RecoveryStrategy.EXPONENTIAL_BACKOFF


@dataclass
class CircuitBreakerConfig:
    """熔断器配置"""
    failure_threshold: int = 5
    recovery_timeout: float = 60.0
    half_open_max_calls: int = 3


@dataclass
class AsyncTask:
    """异步任务"""
    task_id: str
    func: Callable[..., Awaitable[Any]]
    args: tuple = field(default_factory=tuple)
    kwargs: dict = field(default_factory=dict)
    retry_config: RetryConfig = field(default_factory=RetryConfig)
    circuit_breaker_config: Optional[CircuitBreakerConfig] = None
    fallback_func: Optional[Callable[..., Awaitable[Any]]] = None
    
    # 运行时状态
    status: TaskStatus = TaskStatus.PENDING
    attempts: int = 0
    last_error: Optional[Exception] = None
    last_attempt_time: Optional[float] = None
    created_time: float = field(default_factory=time.time)
    completed_time: Optional[float] = None
    result: Any = None


class CircuitBreaker:
    """熔断器"""
    
    def __init__(self, config: CircuitBreakerConfig):
        self.config = config
        self.failure_count = 0
        self.last_failure_time = 0
        self.state = "closed"  # closed, open, half_open
        self.half_open_calls = 0
    
    def can_execute(self) -> bool:
        """检查是否可以执行"""
        if self.state == "closed":
            return True
        elif self.state == "open":
            if time.time() - self.last_failure_time > self.config.recovery_timeout:
                self.state = "half_open"
                self.half_open_calls = 0
                return True
            return False
        elif self.state == "half_open":
            return self.half_open_calls < self.config.half_open_max_calls
        
        return False
    
    def record_success(self) -> None:
        """记录成功"""
        if self.state == "half_open":
            self.state = "closed"
            self.failure_count = 0
        elif self.state == "closed":
            self.failure_count = max(0, self.failure_count - 1)
    
    def record_failure(self) -> None:
        """记录失败"""
        self.failure_count += 1
        self.last_failure_time = time.time()
        
        if self.state == "half_open":
            self.state = "open"
        elif self.state == "closed" and self.failure_count >= self.config.failure_threshold:
            self.state = "open"
        
        if self.state == "half_open":
            self.half_open_calls += 1


class AsyncErrorRecoveryService:
    """
    异步错误恢复服务
    
    提供异步任务的错误恢复、重试机制和故障转移功能。
    """
    
    def __init__(self):
        self.tasks: Dict[str, AsyncTask] = {}
        self.circuit_breakers: Dict[str, CircuitBreaker] = {}
        self.running_tasks: Dict[str, asyncio.Task] = {}
        self.metrics = {
            "total_tasks": 0,
            "completed_tasks": 0,
            "failed_tasks": 0,
            "retried_tasks": 0,
            "circuit_breaker_trips": 0
        }
        
        logger.info("异步错误恢复服务初始化完成")
    
    async def execute_task(self, task: AsyncTask) -> Any:
        """
        执行异步任务
        
        Args:
            task: 异步任务
            
        Returns:
            任务执行结果
        """
        self.tasks[task.task_id] = task
        self.metrics["total_tasks"] += 1
        
        try:
            # 检查熔断器
            if task.circuit_breaker_config:
                circuit_breaker = self._get_circuit_breaker(task.task_id, task.circuit_breaker_config)
                if not circuit_breaker.can_execute():
                    logger.warning(f"任务 {task.task_id} 被熔断器阻止执行")
                    if task.fallback_func:
                        return await self._execute_fallback(task)
                    raise Exception("Circuit breaker is open")
            
            # 执行任务
            result = await self._execute_with_retry(task)
            
            # 记录成功
            if task.circuit_breaker_config:
                circuit_breaker.record_success()
            
            task.status = TaskStatus.COMPLETED
            task.completed_time = time.time()
            task.result = result
            self.metrics["completed_tasks"] += 1
            
            logger.info(f"任务 {task.task_id} 执行成功")
            return result
            
        except Exception as e:
            # 记录失败
            if task.circuit_breaker_config:
                circuit_breaker = self._get_circuit_breaker(task.task_id, task.circuit_breaker_config)
                circuit_breaker.record_failure()
                self.metrics["circuit_breaker_trips"] += 1
            
            task.status = TaskStatus.FAILED
            task.last_error = e
            self.metrics["failed_tasks"] += 1
            
            logger.error(f"任务 {task.task_id} 执行失败: {e}")
            
            # 尝试执行fallback
            if task.fallback_func:
                try:
                    return await self._execute_fallback(task)
                except Exception as fallback_error:
                    logger.error(f"任务 {task.task_id} fallback执行失败: {fallback_error}")
            
            raise
    
    async def _execute_with_retry(self, task: AsyncTask) -> Any:
        """带重试的任务执行"""
        last_exception = None
        
        for attempt in range(task.retry_config.max_retries + 1):
            try:
                task.attempts = attempt + 1
                task.last_attempt_time = time.time()
                task.status = TaskStatus.RUNNING if attempt == 0 else TaskStatus.RETRYING
                
                logger.debug(f"任务 {task.task_id} 第 {attempt + 1} 次尝试")
                
                # 执行任务
                result = await task.func(*task.args, **task.kwargs)
                return result
                
            except Exception as e:
                last_exception = e
                task.last_error = e
                
                logger.warning(f"任务 {task.task_id} 第 {attempt + 1} 次尝试失败: {e}")
                
                # 如果还有重试机会，计算延迟时间
                if attempt < task.retry_config.max_retries:
                    delay = self._calculate_retry_delay(task.retry_config, attempt)
                    logger.info(f"任务 {task.task_id} 将在 {delay:.2f} 秒后重试")
                    
                    await asyncio.sleep(delay)
                    self.metrics["retried_tasks"] += 1
        
        # 所有重试都失败了
        raise last_exception
    
    def _calculate_retry_delay(self, config: RetryConfig, attempt: int) -> float:
        """计算重试延迟时间"""
        if config.strategy == RecoveryStrategy.IMMEDIATE_RETRY:
            return 0
        elif config.strategy == RecoveryStrategy.LINEAR_BACKOFF:
            delay = config.base_delay * (attempt + 1)
        elif config.strategy == RecoveryStrategy.EXPONENTIAL_BACKOFF:
            delay = config.base_delay * (config.backoff_multiplier ** attempt)
        else:
            delay = config.base_delay
        
        # 限制最大延迟
        delay = min(delay, config.max_delay)
        
        # 添加抖动
        if config.jitter:
            import random
            delay = delay * (0.5 + random.random() * 0.5)
        
        return delay
    
    async def _execute_fallback(self, task: AsyncTask) -> Any:
        """执行fallback函数"""
        logger.info(f"执行任务 {task.task_id} 的fallback函数")
        
        try:
            return await task.fallback_func(*task.args, **task.kwargs)
        except Exception as e:
            logger.error(f"任务 {task.task_id} fallback执行失败: {e}")
            raise
    
    def _get_circuit_breaker(self, task_id: str, config: CircuitBreakerConfig) -> CircuitBreaker:
        """获取或创建熔断器"""
        if task_id not in self.circuit_breakers:
            self.circuit_breakers[task_id] = CircuitBreaker(config)
        return self.circuit_breakers[task_id]
    
    async def submit_task(self, task: AsyncTask) -> asyncio.Task:
        """
        提交异步任务
        
        Args:
            task: 异步任务
            
        Returns:
            asyncio.Task对象
        """
        async_task = asyncio.create_task(self.execute_task(task))
        self.running_tasks[task.task_id] = async_task
        
        # 任务完成后清理
        def cleanup_task(future):
            if task.task_id in self.running_tasks:
                del self.running_tasks[task.task_id]
        
        async_task.add_done_callback(cleanup_task)
        
        return async_task
    
    def cancel_task(self, task_id: str) -> bool:
        """
        取消任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            是否成功取消
        """
        if task_id in self.running_tasks:
            task = self.running_tasks[task_id]
            task.cancel()
            
            if task_id in self.tasks:
                self.tasks[task_id].status = TaskStatus.CANCELLED
            
            logger.info(f"任务 {task_id} 已取消")
            return True
        
        return False
    
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务状态"""
        if task_id not in self.tasks:
            return None
        
        task = self.tasks[task_id]
        
        return {
            "task_id": task.task_id,
            "status": task.status.value,
            "attempts": task.attempts,
            "created_time": task.created_time,
            "last_attempt_time": task.last_attempt_time,
            "completed_time": task.completed_time,
            "last_error": str(task.last_error) if task.last_error else None,
            "has_result": task.result is not None
        }
    
    def get_circuit_breaker_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取熔断器状态"""
        if task_id not in self.circuit_breakers:
            return None
        
        cb = self.circuit_breakers[task_id]
        
        return {
            "state": cb.state,
            "failure_count": cb.failure_count,
            "last_failure_time": cb.last_failure_time,
            "half_open_calls": cb.half_open_calls,
            "can_execute": cb.can_execute()
        }
    
    def get_metrics(self) -> Dict[str, Any]:
        """获取服务指标"""
        return {
            **self.metrics,
            "running_tasks_count": len(self.running_tasks),
            "total_tasks_count": len(self.tasks),
            "circuit_breakers_count": len(self.circuit_breakers)
        }
    
    def cleanup_completed_tasks(self, max_age_hours: int = 24) -> int:
        """清理已完成的任务"""
        cutoff_time = time.time() - (max_age_hours * 3600)
        
        tasks_to_remove = []
        for task_id, task in self.tasks.items():
            if (task.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED] and
                task.completed_time and task.completed_time < cutoff_time):
                tasks_to_remove.append(task_id)
        
        for task_id in tasks_to_remove:
            del self.tasks[task_id]
            if task_id in self.circuit_breakers:
                del self.circuit_breakers[task_id]
        
        logger.info(f"清理了 {len(tasks_to_remove)} 个已完成的任务")
        return len(tasks_to_remove)


# 全局异步错误恢复服务实例
_async_error_recovery_service: Optional[AsyncErrorRecoveryService] = None


def get_async_error_recovery_service() -> AsyncErrorRecoveryService:
    """获取全局异步错误恢复服务实例"""
    global _async_error_recovery_service
    if _async_error_recovery_service is None:
        _async_error_recovery_service = AsyncErrorRecoveryService()
    return _async_error_recovery_service


# 便捷装饰器
def async_retry(retry_config: Optional[RetryConfig] = None,
                circuit_breaker_config: Optional[CircuitBreakerConfig] = None,
                fallback_func: Optional[Callable] = None):
    """
    异步重试装饰器
    
    Args:
        retry_config: 重试配置
        circuit_breaker_config: 熔断器配置
        fallback_func: fallback函数
    """
    def decorator(func):
        async def wrapper(*args, **kwargs):
            task_id = f"{func.__name__}_{id(args)}_{id(kwargs)}"
            
            task = AsyncTask(
                task_id=task_id,
                func=func,
                args=args,
                kwargs=kwargs,
                retry_config=retry_config or RetryConfig(),
                circuit_breaker_config=circuit_breaker_config,
                fallback_func=fallback_func
            )
            
            service = get_async_error_recovery_service()
            return await service.execute_task(task)
        
        return wrapper
    return decorator