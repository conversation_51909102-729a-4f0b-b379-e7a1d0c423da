package com.crypto.trading.trade.model.dto;

import com.crypto.trading.trade.model.entity.order.OrderEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;

/**
 * 订单响应数据传输对象
 * 
 * <p>用于向API客户端返回订单信息</p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OrderResponseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单ID
     */
    private String orderId;

    /**
     * 客户端订单ID
     */
    private String clientOrderId;

    /**
     * 关联的交易信号ID
     */
    private String signalId;

    /**
     * 交易对
     */
    private String symbol;

    /**
     * 订单方向 BUY/SELL
     */
    private String side;

    /**
     * 仓位方向 BOTH/LONG/SHORT
     */
    private String positionSide;

    /**
     * 订单类型 MARKET/LIMIT/STOP等
     */
    private String orderType;

    /**
     * 订单有效期 GTC/IOC/FOK
     */
    private String timeInForce;

    /**
     * 交易数量
     */
    private BigDecimal quantity;

    /**
     * 订单价格
     */
    private BigDecimal price;

    /**
     * 执行价格
     */
    private BigDecimal executedPrice;

    /**
     * 已执行数量
     */
    private BigDecimal executedQuantity;

    /**
     * 订单状态 NEW/FILLED/CANCELED等
     */
    private String status;

    /**
     * 错误代码
     */
    private String errorCode;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 策略名称
     */
    private String strategy;

    /**
     * 创建时间（ISO格式）
     */
    private String createdTime;

    /**
     * 更新时间（ISO格式）
     */
    private String updatedTime;

    /**
     * 执行时间（ISO格式）
     */
    private String executedTime;

    /**
     * 默认构造函数
     */
    public OrderResponseDTO() {
    }

    /**
     * 从实体构造DTO
     *
     * @param entity 订单实体
     */
    public OrderResponseDTO(OrderEntity entity) {
        if (entity == null) {
            return;
        }
        
        this.orderId = entity.getOrderId();
        this.clientOrderId = entity.getClientOrderId();
        this.symbol = entity.getSymbol();
        this.side = entity.getSide();
        this.positionSide = entity.getPositionSide();
        this.orderType = entity.getType();
        this.timeInForce = null; // 目前OrderEntity中没有对应字段
        
        // 转换Double为BigDecimal
        if (entity.getQuantity() != null) {
            this.quantity = BigDecimal.valueOf(entity.getQuantity());
        }
        if (entity.getPrice() != null) {
            this.price = BigDecimal.valueOf(entity.getPrice());
        }
        if (entity.getExecutedPrice() != null) {
            this.executedPrice = BigDecimal.valueOf(entity.getExecutedPrice());
        }
        if (entity.getExecutedQuantity() != null) {
            this.executedQuantity = BigDecimal.valueOf(entity.getExecutedQuantity());
        }
        
        this.status = entity.getStatus();
        this.errorCode = entity.getErrorCode();
        this.errorMessage = entity.getErrorMessage();
        this.strategy = entity.getStrategyId();
        
        // 转换时间戳为ISO格式
        if (entity.getCreatedTime() != null) {
            this.createdTime = LocalDateTime.ofInstant(
                    Instant.ofEpochMilli(entity.getCreatedTime()), 
                    ZoneId.systemDefault()).toString();
        }
        
        if (entity.getUpdatedTime() != null) {
            this.updatedTime = LocalDateTime.ofInstant(
                    Instant.ofEpochMilli(entity.getUpdatedTime()), 
                    ZoneId.systemDefault()).toString();
        }
        
        if (entity.getExecutedTime() != null) {
            this.executedTime = LocalDateTime.ofInstant(
                    Instant.ofEpochMilli(entity.getExecutedTime()), 
                    ZoneId.systemDefault()).toString();
        }
    }

    // Getters and Setters

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getClientOrderId() {
        return clientOrderId;
    }

    public void setClientOrderId(String clientOrderId) {
        this.clientOrderId = clientOrderId;
    }

    public String getSignalId() {
        return signalId;
    }

    public void setSignalId(String signalId) {
        this.signalId = signalId;
    }

    public String getSymbol() {
        return symbol;
    }

    public void setSymbol(String symbol) {
        this.symbol = symbol;
    }

    public String getSide() {
        return side;
    }

    public void setSide(String side) {
        this.side = side;
    }

    public String getPositionSide() {
        return positionSide;
    }

    public void setPositionSide(String positionSide) {
        this.positionSide = positionSide;
    }

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public String getTimeInForce() {
        return timeInForce;
    }

    public void setTimeInForce(String timeInForce) {
        this.timeInForce = timeInForce;
    }

    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public BigDecimal getExecutedPrice() {
        return executedPrice;
    }

    public void setExecutedPrice(BigDecimal executedPrice) {
        this.executedPrice = executedPrice;
    }

    public BigDecimal getExecutedQuantity() {
        return executedQuantity;
    }

    public void setExecutedQuantity(BigDecimal executedQuantity) {
        this.executedQuantity = executedQuantity;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public String getStrategy() {
        return strategy;
    }

    public void setStrategy(String strategy) {
        this.strategy = strategy;
    }

    public String getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(String createdTime) {
        this.createdTime = createdTime;
    }

    public String getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(String updatedTime) {
        this.updatedTime = updatedTime;
    }

    public String getExecutedTime() {
        return executedTime;
    }

    public void setExecutedTime(String executedTime) {
        this.executedTime = executedTime;
    }

    @Override
    public String toString() {
        return "OrderResponseDTO{" +
                "orderId='" + orderId + '\'' +
                ", clientOrderId='" + clientOrderId + '\'' +
                ", symbol='" + symbol + '\'' +
                ", side='" + side + '\'' +
                ", positionSide='" + positionSide + '\'' +
                ", orderType='" + orderType + '\'' +
                ", status='" + status + '\'' +
                ", quantity=" + quantity +
                ", price=" + price +
                ", executedQuantity=" + executedQuantity +
                ", executedPrice=" + executedPrice +
                ", createdTime='" + createdTime + '\'' +
                '}';
    }
}