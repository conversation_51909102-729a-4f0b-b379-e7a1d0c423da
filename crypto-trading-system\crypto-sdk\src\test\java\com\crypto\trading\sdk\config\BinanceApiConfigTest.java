package com.crypto.trading.sdk.config;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.assertFalse;

/**
 * 币安API配置测试类
 */
public class BinanceApiConfigTest {

    private BinanceApiConfig config;

    @BeforeEach
    void setUp() {
        config = new BinanceApiConfig();
        // 使用反射设置属性，因为它们是通过@Value注入的
        ReflectionTestUtils.setField(config, "secretKey", "NhqPtmdSJYdKjVHjA7PZj4Mge3R5YNiP1e3UZjInClVN65XAbvqqM6A7H5fATj0j");
        ReflectionTestUtils.setField(config, "apiKey", "vmPUZE6mv9SD5VNHk4HlWFsOr6aKE2zvsw0MuIgwCIPy6utIco14y7Ju91duEh8A");
        ReflectionTestUtils.setField(config, "usdtFuturesBaseUrl", "https://fapi.binance.com");
        ReflectionTestUtils.setField(config, "coinFuturesBaseUrl", "https://dapi.binance.com");
        ReflectionTestUtils.setField(config, "usdtFuturesWsBaseUrl", "wss://fstream.binance.com");
        ReflectionTestUtils.setField(config, "coinFuturesWsBaseUrl", "wss://dstream.binance.com");
    }

    /**
     * 测试生成币安API签名
     */
    @Test
    void testGenerateSignature() {
        String data = "symbol=BTCUSDT&side=BUY&type=LIMIT&timeInForce=GTC&quantity=1&price=9000&timestamp=1591702613943";
        String expected = "71047745f2dc7637295506537657cb60ef76a0ef077704e3276c1c7f82612b81";
        
        String signature = config.generateSignature(data);
        assertNotNull(signature);
        assertEquals(expected, signature);
    }

    @Test
    public void testDefaultConstructor() {
        // 测试默认构造函数，注意属性是通过Spring注入的，此处只是验证构造函数能正常执行
        BinanceApiConfig config = new BinanceApiConfig();
        
        // 由于是通过Spring注入，直接实例化的对象属性为null或默认值
        assertNull(ReflectionTestUtils.getField(config, "apiKey"));
        assertNull(ReflectionTestUtils.getField(config, "secretKey"));
    }

    @Test
    public void testGetApiKeyAndSecretKey() {
        // 测试获取API密钥
        assertEquals("vmPUZE6mv9SD5VNHk4HlWFsOr6aKE2zvsw0MuIgwCIPy6utIco14y7Ju91duEh8A", config.getApiKey());
        assertEquals("NhqPtmdSJYdKjVHjA7PZj4Mge3R5YNiP1e3UZjInClVN65XAbvqqM6A7H5fATj0j", config.getSecretKey());
    }

    @Test
    public void testGetUsdtFuturesBaseUrl() {
        // 测试获取USDT保证金期货URL
        assertEquals("https://fapi.binance.com", config.getUsdtFuturesBaseUrl());
    }

    @Test
    public void testGetCoinFuturesBaseUrl() {
        // 测试获取币本位期货URL
        assertEquals("https://dapi.binance.com", config.getCoinFuturesBaseUrl());
    }

    @Test
    public void testGetUsdtFuturesWsBaseUrl() {
        // 测试获取USDT保证金期货WebSocket URL
        assertEquals("wss://fstream.binance.com", config.getUsdtFuturesWsBaseUrl());
    }

    @Test
    public void testGetCoinFuturesWsBaseUrl() {
        // 测试获取币本位期货WebSocket URL
        assertEquals("wss://dstream.binance.com", config.getCoinFuturesWsBaseUrl());
    }

    @Test
    public void testUseTestnet() {
        // 测试使用测试网络
        ReflectionTestUtils.setField(config, "useTestnet", true);
        ReflectionTestUtils.setField(config, "usdtFuturesTestnetBaseUrl", "https://testnet.binancefuture.com");
        ReflectionTestUtils.setField(config, "usdtFuturesTestnetWsBaseUrl", "wss://stream.binancefuture.com");
        
        // 验证测试网络标志
        assertTrue(config.isUseTestnet());
        
        // 测试网络模式下应该使用测试网URL
        assertEquals("https://testnet.binancefuture.com", config.getUsdtFuturesBaseUrl());
        assertEquals("wss://stream.binancefuture.com", config.getUsdtFuturesWsBaseUrl());
    }

    @Test
    public void testHasProxy() {
        // 测试代理配置
        // 初始状态无代理
        assertFalse(config.hasProxy());
        
        // 设置代理
        ReflectionTestUtils.setField(config, "proxyHost", "127.0.0.1");
        ReflectionTestUtils.setField(config, "proxyPort", 8080);
        
        // 验证代理设置
        assertTrue(config.hasProxy());
        assertEquals("127.0.0.1", config.getProxyHost());
        assertEquals(8080, config.getProxyPort());
    }

    @Test
    public void testTimeoutSettings() {
        // 测试超时设置
        ReflectionTestUtils.setField(config, "connectTimeout", 10000);
        ReflectionTestUtils.setField(config, "readTimeout", 20000);
        ReflectionTestUtils.setField(config, "writeTimeout", 30000);
        
        // 验证超时设置
        assertEquals(10000, config.getConnectTimeout());
        assertEquals(20000, config.getReadTimeout());
        assertEquals(30000, config.getWriteTimeout());
    }
} 