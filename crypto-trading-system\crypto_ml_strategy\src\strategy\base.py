#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
基础策略类模块

定义所有交易策略的基础接口和通用功能。
提供策略的生命周期管理、状态跟踪和性能监控。
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
from loguru import logger

from core.config import Config


class BaseStrategy(ABC):
    """
    基础策略抽象类
    
    定义所有交易策略必须实现的接口：
    - 市场数据处理
    - 信号生成
    - 状态管理
    - 性能监控
    """
    
    def __init__(self, config: Config):
        """
        初始化基础策略
        
        Args:
            config: 配置对象
        """
        self.config = config
        self.logger = logger.bind(component=self.__class__.__name__)
        
        # 策略基础信息
        self.strategy_id = config.get('strategy', 'strategy_id', 'base_strategy')
        self.strategy_name = self.__class__.__name__
        
        # 策略状态
        self.is_active = False
        self.start_time = None
        self.last_update_time = None
        
        # 性能统计
        self.total_signals = 0
        self.successful_signals = 0
        self.failed_signals = 0
        
        self.logger.info(f"基础策略初始化: {self.strategy_name}")
    
    @abstractmethod
    def on_market_data(self, market_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        处理市场数据并生成交易信号
        
        Args:
            market_data: 市场数据字典
        
        Returns:
            交易信号字典，如果没有信号则返回None
        """
        pass
    
    def start(self) -> bool:
        """
        启动策略
        
        Returns:
            启动是否成功
        """
        try:
            if self.is_active:
                self.logger.warning("策略已经在运行中")
                return True
            
            self.is_active = True
            self.start_time = datetime.now()
            self.last_update_time = self.start_time
            
            self.logger.info(f"策略启动成功: {self.strategy_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"策略启动失败: {str(e)}")
            return False
    
    def stop(self) -> bool:
        """
        停止策略
        
        Returns:
            停止是否成功
        """
        try:
            if not self.is_active:
                self.logger.warning("策略未在运行")
                return True
            
            self.is_active = False
            
            self.logger.info(f"策略停止成功: {self.strategy_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"策略停止失败: {str(e)}")
            return False
    
    def get_status(self) -> Dict[str, Any]:
        """
        获取策略状态
        
        Returns:
            策略状态字典
        """
        try:
            uptime = None
            if self.start_time:
                uptime = (datetime.now() - self.start_time).total_seconds()
            
            return {
                'strategy_id': self.strategy_id,
                'strategy_name': self.strategy_name,
                'is_active': self.is_active,
                'start_time': self.start_time.isoformat() if self.start_time else None,
                'last_update_time': self.last_update_time.isoformat() if self.last_update_time else None,
                'uptime_seconds': uptime,
                'total_signals': self.total_signals,
                'successful_signals': self.successful_signals,
                'failed_signals': self.failed_signals,
                'success_rate': self.get_success_rate()
            }
            
        except Exception as e:
            self.logger.error(f"获取策略状态失败: {str(e)}")
            return {}
    
    def get_success_rate(self) -> float:
        """
        获取成功率
        
        Returns:
            成功率（0-1之间）
        """
        try:
            if self.total_signals == 0:
                return 0.0
            
            return self.successful_signals / self.total_signals
            
        except Exception as e:
            self.logger.error(f"计算成功率失败: {str(e)}")
            return 0.0
    
    def update_signal_result(self, signal_id: str, success: bool) -> None:
        """
        更新信号结果
        
        Args:
            signal_id: 信号ID
            success: 是否成功
        """
        try:
            if success:
                self.successful_signals += 1
            else:
                self.failed_signals += 1
            
            self.logger.debug(f"信号结果更新: {signal_id}, 成功: {success}")
            
        except Exception as e:
            self.logger.error(f"更新信号结果失败: {str(e)}")
    
    def _update_last_update_time(self) -> None:
        """更新最后更新时间"""
        self.last_update_time = datetime.now()
    
    def _increment_signal_count(self) -> None:
        """增加信号计数"""
        self.total_signals += 1
    
    def validate_market_data(self, market_data: Dict[str, Any]) -> bool:
        """
        验证市场数据
        
        Args:
            market_data: 市场数据
        
        Returns:
            数据是否有效
        """
        try:
            # 基础验证
            if not isinstance(market_data, dict):
                return False
            
            # 检查必要字段
            required_fields = ['symbol', 'timestamp']
            for field in required_fields:
                if field not in market_data:
                    self.logger.warning(f"市场数据缺少必要字段: {field}")
                    return False
            
            # 检查数据时效性（可选）
            timestamp = market_data.get('timestamp')
            if timestamp:
                try:
                    if isinstance(timestamp, str):
                        data_time = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                    elif isinstance(timestamp, (int, float)):
                        data_time = datetime.fromtimestamp(timestamp / 1000)  # 假设是毫秒时间戳
                    else:
                        data_time = timestamp
                    
                    # 检查数据是否过于陈旧（超过1小时）
                    time_diff = (datetime.now() - data_time.replace(tzinfo=None)).total_seconds()
                    if time_diff > 3600:  # 1小时
                        self.logger.warning(f"市场数据过于陈旧: {time_diff}秒")
                        return False
                        
                except Exception as e:
                    self.logger.warning(f"时间戳解析失败: {str(e)}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"市场数据验证失败: {str(e)}")
            return False
    
    def create_signal_template(self, symbol: str, signal_type: str, 
                             signal_strength: float, confidence: float) -> Dict[str, Any]:
        """
        创建信号模板
        
        Args:
            symbol: 交易对符号
            signal_type: 信号类型（BUY/SELL/HOLD）
            signal_strength: 信号强度
            confidence: 置信度
        
        Returns:
            信号字典模板
        """
        try:
            import uuid
            
            signal = {
                'messageType': 'TRADING_SIGNAL',
                'signalId': str(uuid.uuid4()),
                'timestamp': datetime.now().isoformat(),
                'strategyId': self.strategy_id,
                'strategyName': self.strategy_name,
                'data': {
                    'symbol': symbol,
                    'signalType': signal_type,
                    'signalStrength': signal_strength,
                    'confidence': confidence
                }
            }
            
            return signal
            
        except Exception as e:
            self.logger.error(f"创建信号模板失败: {str(e)}")
            return {}
    
    def log_signal_generated(self, signal: Dict[str, Any]) -> None:
        """
        记录信号生成日志
        
        Args:
            signal: 生成的信号
        """
        try:
            signal_data = signal.get('data', {})
            symbol = signal_data.get('symbol', 'UNKNOWN')
            signal_type = signal_data.get('signalType', 'UNKNOWN')
            confidence = signal_data.get('confidence', 0)
            
            self.logger.info(f"信号生成: {symbol} - {signal_type} (置信度: {confidence:.3f})")
            
            # 更新统计
            self._increment_signal_count()
            self._update_last_update_time()
            
        except Exception as e:
            self.logger.error(f"记录信号日志失败: {str(e)}")
    
    def reset_statistics(self) -> None:
        """重置统计信息"""
        try:
            self.total_signals = 0
            self.successful_signals = 0
            self.failed_signals = 0
            
            self.logger.info("策略统计信息已重置")
            
        except Exception as e:
            self.logger.error(f"重置统计信息失败: {str(e)}")


class StrategyManager:
    """
    策略管理器类
    
    负责管理多个策略实例的生命周期和协调。
    """
    
    def __init__(self):
        """初始化策略管理器"""
        self.logger = logger.bind(component="StrategyManager")
        
        self.strategies = {}  # {strategy_id: strategy_instance}
        self.active_strategies = set()
        
        self.logger.info("策略管理器初始化完成")
    
    def register_strategy(self, strategy: BaseStrategy) -> bool:
        """
        注册策略
        
        Args:
            strategy: 策略实例
        
        Returns:
            注册是否成功
        """
        try:
            strategy_id = strategy.strategy_id
            
            if strategy_id in self.strategies:
                self.logger.warning(f"策略已存在: {strategy_id}")
                return False
            
            self.strategies[strategy_id] = strategy
            self.logger.info(f"策略注册成功: {strategy_id}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"策略注册失败: {str(e)}")
            return False
    
    def start_strategy(self, strategy_id: str) -> bool:
        """
        启动策略
        
        Args:
            strategy_id: 策略ID
        
        Returns:
            启动是否成功
        """
        try:
            if strategy_id not in self.strategies:
                self.logger.error(f"策略不存在: {strategy_id}")
                return False
            
            strategy = self.strategies[strategy_id]
            
            if strategy.start():
                self.active_strategies.add(strategy_id)
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"启动策略失败: {str(e)}")
            return False
    
    def stop_strategy(self, strategy_id: str) -> bool:
        """
        停止策略
        
        Args:
            strategy_id: 策略ID
        
        Returns:
            停止是否成功
        """
        try:
            if strategy_id not in self.strategies:
                self.logger.error(f"策略不存在: {strategy_id}")
                return False
            
            strategy = self.strategies[strategy_id]
            
            if strategy.stop():
                self.active_strategies.discard(strategy_id)
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"停止策略失败: {str(e)}")
            return False
    
    def get_all_strategies_status(self) -> Dict[str, Dict[str, Any]]:
        """
        获取所有策略状态
        
        Returns:
            所有策略状态字典
        """
        try:
            status = {}
            
            for strategy_id, strategy in self.strategies.items():
                status[strategy_id] = strategy.get_status()
            
            return status
            
        except Exception as e:
            self.logger.error(f"获取策略状态失败: {str(e)}")
            return {}
    
    def broadcast_market_data(self, market_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        向所有活跃策略广播市场数据
        
        Args:
            market_data: 市场数据
        
        Returns:
            生成的信号列表
        """
        try:
            signals = []
            
            for strategy_id in self.active_strategies:
                if strategy_id in self.strategies:
                    strategy = self.strategies[strategy_id]
                    
                    try:
                        signal = strategy.on_market_data(market_data)
                        if signal:
                            signals.append(signal)
                    except Exception as e:
                        self.logger.error(f"策略处理数据失败 {strategy_id}: {str(e)}")
            
            return signals
            
        except Exception as e:
            self.logger.error(f"广播市场数据失败: {str(e)}")
            return []