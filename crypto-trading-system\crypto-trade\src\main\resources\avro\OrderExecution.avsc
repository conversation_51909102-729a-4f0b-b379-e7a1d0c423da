{"namespace": "com.crypto.trading.trade.model.avro", "type": "record", "name": "OrderExecution", "doc": "订单执行信息，包含订单的执行结果", "fields": [{"name": "id", "type": "string", "doc": "订单执行唯一ID"}, {"name": "signalId", "type": "string", "doc": "关联的交易信号ID"}, {"name": "orderId", "type": "string", "doc": "交易所订单ID"}, {"name": "clientOrderId", "type": "string", "doc": "客户端订单ID"}, {"name": "symbol", "type": "string", "doc": "交易对，如BTCUSDT"}, {"name": "strategy", "type": "string", "doc": "策略名称"}, {"name": "orderType", "type": {"type": "enum", "name": "OrderType", "symbols": ["MARKET", "LIMIT", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET"]}, "doc": "订单类型"}, {"name": "side", "type": {"type": "enum", "name": "OrderSide", "symbols": ["BUY", "SELL"]}, "doc": "订单方向"}, {"name": "positionSide", "type": {"type": "enum", "name": "PositionSide", "symbols": ["BOTH", "LONG", "SHORT"]}, "default": "BOTH", "doc": "仓位方向"}, {"name": "quantity", "type": "double", "doc": "交易数量"}, {"name": "price", "type": ["null", "double"], "default": null, "doc": "订单价格，市价单为null"}, {"name": "executedPrice", "type": ["null", "double"], "default": null, "doc": "执行价格"}, {"name": "status", "type": {"type": "enum", "name": "OrderStatus", "symbols": ["NEW", "PARTIALLY_FILLED", "FILLED", "CANCELED", "REJECTED", "EXPIRED"]}, "doc": "订单状态"}, {"name": "createdTime", "type": "long", "doc": "订单创建时间（毫秒时间戳）"}, {"name": "executedTime", "type": ["null", "long"], "default": null, "doc": "订单执行时间（毫秒时间戳）"}, {"name": "errorCode", "type": ["null", "string"], "default": null, "doc": "错误代码，如果有"}, {"name": "errorMessage", "type": ["null", "string"], "default": null, "doc": "错误信息，如果有"}]}