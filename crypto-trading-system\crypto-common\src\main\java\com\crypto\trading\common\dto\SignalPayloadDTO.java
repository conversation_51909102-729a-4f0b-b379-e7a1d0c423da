package com.crypto.trading.common.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @name: SignalPayloadDTO
 * @author: zeek
 * @description: 策略信号的核心数据负载对象
 * @create: 2024-07-16 00:25
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SignalPayloadDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String strategyId;
    private String symbol;
    private String signalType;
    private String direction;
    private String positionSide; // 添加持仓方向字段
    private Double signalStrength;
    private BigDecimal price;
    private BigDecimal quantity;
    private String timeFrame;
    private ParametersDTO parameters;
    private RiskAssessmentDTO riskAssessment;
    
    /**
     * 获取持仓方向。
     * 该方法用于与订单系统交互，表示订单的持仓方向（多/空/双向）。
     * 
     * @return 持仓方向
     */
    public String getPositionSide() {
        return positionSide;
    }
    
    /**
     * 设置持仓方向。
     * 
     * @param positionSide 持仓方向
     */
    public void setPositionSide(String positionSide) {
        this.positionSide = positionSide;
    }
}
