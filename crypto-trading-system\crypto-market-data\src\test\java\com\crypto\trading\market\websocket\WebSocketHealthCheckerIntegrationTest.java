package com.crypto.trading.market.websocket;

import com.crypto.trading.market.config.WebSocketConfig;
import com.crypto.trading.sdk.websocket.WebSocketConnection;
import com.crypto.trading.sdk.websocket.WebSocketConnectionPool;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * WebSocketHealthChecker集成测试
 */
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = {WebSocketHealthChecker.class, WebSocketConfig.class})
@TestPropertySource(properties = {
        "market.websocket.health-check-interval=100",
        "market.websocket.max-fail-count=2",
        "market.websocket.reconnect-interval=50"
})
class WebSocketHealthCheckerIntegrationTest {

    @MockBean
    private WebSocketConnectionPool connectionPool;

    @Mock
    private WebSocketConnection connection;

    @Autowired
    private WebSocketHealthChecker healthChecker;

    @BeforeEach
    void setUp() {
        // 停止健康检查器，以便手动控制测试流程
        healthChecker.stop();
        
        // 清空连接状态映射
        Map<Integer, WebSocketHealthChecker.ConnectionStatus> statusMap = healthChecker.getConnectionStatusMap();
        statusMap.clear();
    }

    @Test
    void testAutoRepairConnection() throws InterruptedException {
        // 注册连接
        healthChecker.registerConnection(1, "test-connection");
        
        // 模拟连接存在
        when(connectionPool.hasConnection(1)).thenReturn(true);
        
        // 模拟连接不活跃
        when(connectionPool.isConnectionActive(1)).thenReturn(false);
        
        // 模拟获取连接
        when(connectionPool.getConnection(1)).thenReturn(connection);
        
        // 创建CountDownLatch等待修复操作完成
        CountDownLatch latch = new CountDownLatch(1);
        
        // 当连接的connect方法被调用时，释放CountDownLatch
        doAnswer(invocation -> {
            latch.countDown();
            return null;
        }).when(connection).connect();
        
        // 启动健康检查器
        healthChecker.start();
        
        // 等待修复操作完成
        boolean completed = latch.await(1000, TimeUnit.MILLISECONDS);
        
        // 停止健康检查器
        healthChecker.stop();
        
        // 验证修复操作是否完成
        assertTrue(completed, "修复操作未在预期时间内完成");
        
        // 验证连接被关闭和重新连接
        verify(connection, timeout(1000)).close();
        verify(connection, timeout(1000)).connect();
        
        // 验证总修复次数大于0
        assertTrue(healthChecker.getTotalRepairs() > 0, "总修复次数应大于0");
    }

    @Test
    void testConnectionStatusTracking() throws InterruptedException {
        // 注册连接
        healthChecker.registerConnection(1, "test-connection-1");
        healthChecker.registerConnection(2, "test-connection-2");
        
        // 模拟连接存在
        when(connectionPool.hasConnection(anyInt())).thenReturn(true);
        
        // 模拟连接1活跃，连接2不活跃
        when(connectionPool.isConnectionActive(1)).thenReturn(true);
        when(connectionPool.isConnectionActive(2)).thenReturn(false);
        
        // 模拟获取连接
        when(connectionPool.getConnection(anyInt())).thenReturn(connection);
        
        // 创建CountDownLatch等待健康检查完成
        CountDownLatch latch = new CountDownLatch(1);
        
        // 当连接的connect方法被调用时，释放CountDownLatch
        doAnswer(invocation -> {
            latch.countDown();
            return null;
        }).when(connection).connect();
        
        // 启动健康检查器
        healthChecker.start();
        
        // 等待健康检查完成，确保有足够时间进行多次检查
        Thread.sleep(500);
        
        // 停止健康检查器
        healthChecker.stop();
        
        // 获取连接状态映射
        Map<Integer, WebSocketHealthChecker.ConnectionStatus> statusMap = healthChecker.getConnectionStatusMap();
        
        // 验证连接1的失败计数为0
        assertEquals(0, statusMap.get(1).getFailCount(), "连接1的失败计数应为0");
        
        // 验证连接2的失败计数
        long connection2FailCount = statusMap.get(2).getFailCount();
        System.out.println("连接2的失败计数: " + connection2FailCount);
        
        // 由于我们设置了max-fail-count=2，连接2的失败计数应该被重置为0（在达到2后触发修复）
        // 或者大于0（如果检查刚开始）
        assertTrue(connection2FailCount >= 0, "连接2的失败计数应大于或等于0");
        
        // 验证总检查次数大于0
        assertTrue(healthChecker.getTotalChecks() > 0, "总检查次数应大于0");
        
        // 验证总修复次数大于0（因为连接2应该被修复至少一次）
        assertTrue(healthChecker.getTotalRepairs() > 0, "总修复次数应大于0");
    }

    @Test
    void testConcurrentChecks() throws InterruptedException {
        // 注册多个连接
        for (int i = 1; i <= 10; i++) {
            healthChecker.registerConnection(i, "test-connection-" + i);
        }
        
        // 模拟连接存在
        when(connectionPool.hasConnection(anyInt())).thenReturn(true);
        
        // 模拟连接活跃
        when(connectionPool.isConnectionActive(anyInt())).thenReturn(true);
        
        // 启动健康检查器
        healthChecker.start();
        
        // 等待健康检查完成
        Thread.sleep(300);
        
        // 停止健康检查器
        healthChecker.stop();
        
        // 验证总检查次数大于0
        assertTrue(healthChecker.getTotalChecks() > 0, "总检查次数应大于0");
        
        // 验证所有连接都被检查
        verify(connectionPool, atLeast(10)).isConnectionActive(anyInt());
    }
}