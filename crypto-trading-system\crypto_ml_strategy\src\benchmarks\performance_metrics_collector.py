"""
Crypto ML Strategy - 性能指标收集系统

该模块实现了多层次的性能指标收集功能，包括系统级、应用级和业务级
指标的收集、聚合、存储和分析。

主要功能：
- PerformanceMetricsCollector: 性能指标收集器主管理器
- SystemMetricsCollector: 系统级性能指标收集
- ApplicationMetricsCollector: 应用级性能指标收集
- MetricsAggregator: 指标聚合器
- MetricsStorage: 指标存储管理器

作者: Crypto ML Strategy Team
日期: 2024-01-20
版本: 1.0.0
"""

import asyncio
import threading
import time
from collections import defaultdict, deque
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Union, Callable
from infrastructure.logging.logging_core_manager import get_logger
from .performance_logging_core import get_global_performance_logger
import psutil
import gc
import statistics
import json
from pathlib import Path


@dataclass
class MetricsSnapshot:
    """指标快照"""
    timestamp: datetime
    system_metrics: Dict[str, float]
    application_metrics: Dict[str, float]
    business_metrics: Dict[str, float]
    custom_metrics: Dict[str, float] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "timestamp": self.timestamp.isoformat(),
            "system_metrics": self.system_metrics,
            "application_metrics": self.application_metrics,
            "business_metrics": self.business_metrics,
            "custom_metrics": self.custom_metrics
        }


@dataclass
class MetricsAggregation:
    """指标聚合结果"""
    metric_name: str
    time_window: str
    start_time: datetime
    end_time: datetime
    count: int
    min_value: float
    max_value: float
    avg_value: float
    p50_value: float
    p95_value: float
    p99_value: float
    std_dev: float
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "metric_name": self.metric_name,
            "time_window": self.time_window,
            "start_time": self.start_time.isoformat(),
            "end_time": self.end_time.isoformat(),
            "count": self.count,
            "min_value": self.min_value,
            "max_value": self.max_value,
            "avg_value": self.avg_value,
            "p50_value": self.p50_value,
            "p95_value": self.p95_value,
            "p99_value": self.p99_value,
            "std_dev": self.std_dev
        }


class SystemMetricsCollector:
    """系统级性能指标收集器"""
    
    def __init__(self):
        self.logger = get_logger(f"{__name__}.SystemMetricsCollector")
        self._process = psutil.Process()
        self._lock = threading.RLock()
    
    def collect_cpu_metrics(self) -> Dict[str, float]:
        """收集CPU指标"""
        try:
            return {
                "system_cpu_percent": psutil.cpu_percent(interval=0.1),
                "system_cpu_count": psutil.cpu_count(),
                "process_cpu_percent": self._process.cpu_percent(),
                "cpu_freq_current": psutil.cpu_freq().current if psutil.cpu_freq() else 0,
                "load_average_1m": psutil.getloadavg()[0] if hasattr(psutil, 'getloadavg') else 0
            }
        except Exception as e:
            self.logger.error(f"Failed to collect CPU metrics: {e}")
            return {}
    
    def collect_memory_metrics(self) -> Dict[str, float]:
        """收集内存指标"""
        try:
            system_memory = psutil.virtual_memory()
            process_memory = self._process.memory_info()
            
            return {
                "system_memory_total_mb": system_memory.total / 1024 / 1024,
                "system_memory_available_mb": system_memory.available / 1024 / 1024,
                "system_memory_used_mb": system_memory.used / 1024 / 1024,
                "system_memory_percent": system_memory.percent,
                "process_memory_rss_mb": process_memory.rss / 1024 / 1024,
                "process_memory_vms_mb": process_memory.vms / 1024 / 1024,
                "process_memory_percent": self._process.memory_percent()
            }
        except Exception as e:
            self.logger.error(f"Failed to collect memory metrics: {e}")
            return {}
    
    def collect_disk_metrics(self) -> Dict[str, float]:
        """收集磁盘指标"""
        try:
            disk_usage = psutil.disk_usage('/')
            disk_io = psutil.disk_io_counters()
            
            metrics = {
                "disk_total_gb": disk_usage.total / 1024 / 1024 / 1024,
                "disk_used_gb": disk_usage.used / 1024 / 1024 / 1024,
                "disk_free_gb": disk_usage.free / 1024 / 1024 / 1024,
                "disk_percent": (disk_usage.used / disk_usage.total) * 100
            }
            
            if disk_io:
                metrics.update({
                    "disk_read_bytes": disk_io.read_bytes,
                    "disk_write_bytes": disk_io.write_bytes,
                    "disk_read_count": disk_io.read_count,
                    "disk_write_count": disk_io.write_count
                })
            
            return metrics
        except Exception as e:
            self.logger.error(f"Failed to collect disk metrics: {e}")
            return {}
    
    def collect_network_metrics(self) -> Dict[str, float]:
        """收集网络指标"""
        try:
            net_io = psutil.net_io_counters()
            net_connections = len(psutil.net_connections())
            
            return {
                "network_bytes_sent": net_io.bytes_sent,
                "network_bytes_recv": net_io.bytes_recv,
                "network_packets_sent": net_io.packets_sent,
                "network_packets_recv": net_io.packets_recv,
                "network_connections_count": net_connections
            }
        except Exception as e:
            self.logger.error(f"Failed to collect network metrics: {e}")
            return {}
    
    def collect_all_system_metrics(self) -> Dict[str, float]:
        """收集所有系统指标"""
        all_metrics = {}
        
        all_metrics.update(self.collect_cpu_metrics())
        all_metrics.update(self.collect_memory_metrics())
        all_metrics.update(self.collect_disk_metrics())
        all_metrics.update(self.collect_network_metrics())
        
        return all_metrics


class ApplicationMetricsCollector:
    """应用级性能指标收集器"""
    
    def __init__(self):
        self.logger = get_logger(f"{__name__}.ApplicationMetricsCollector")
        self._lock = threading.RLock()
        self._start_time = time.time()
    
    def collect_python_metrics(self) -> Dict[str, float]:
        """收集Python运行时指标"""
        try:
            gc_stats = gc.get_stats()
            
            return {
                "python_gc_collections_gen0": gc_stats[0]['collections'] if gc_stats else 0,
                "python_gc_collections_gen1": gc_stats[1]['collections'] if len(gc_stats) > 1 else 0,
                "python_gc_collections_gen2": gc_stats[2]['collections'] if len(gc_stats) > 2 else 0,
                "python_gc_objects": len(gc.get_objects()),
                "python_threads_count": threading.active_count(),
                "python_uptime_seconds": time.time() - self._start_time
            }
        except Exception as e:
            self.logger.error(f"Failed to collect Python metrics: {e}")
            return {}
    
    def collect_asyncio_metrics(self) -> Dict[str, float]:
        """收集AsyncIO指标"""
        try:
            loop = asyncio.get_event_loop()
            
            return {
                "asyncio_tasks_count": len(asyncio.all_tasks(loop)),
                "asyncio_loop_running": 1.0 if loop.is_running() else 0.0,
                "asyncio_loop_closed": 1.0 if loop.is_closed() else 0.0
            }
        except Exception as e:
            self.logger.error(f"Failed to collect AsyncIO metrics: {e}")
            return {}
    
    def collect_file_descriptor_metrics(self) -> Dict[str, float]:
        """收集文件描述符指标"""
        try:
            process = psutil.Process()
            
            return {
                "file_descriptors_count": process.num_fds() if hasattr(process, 'num_fds') else 0,
                "open_files_count": len(process.open_files())
            }
        except Exception as e:
            self.logger.error(f"Failed to collect file descriptor metrics: {e}")
            return {}
    
    def collect_all_application_metrics(self) -> Dict[str, float]:
        """收集所有应用指标"""
        all_metrics = {}
        
        all_metrics.update(self.collect_python_metrics())
        all_metrics.update(self.collect_asyncio_metrics())
        all_metrics.update(self.collect_file_descriptor_metrics())
        
        return all_metrics


class BusinessMetricsCollector:
    """业务级性能指标收集器"""
    
    def __init__(self):
        self.logger = get_logger(f"{__name__}.BusinessMetricsCollector")
        self._custom_counters: Dict[str, int] = defaultdict(int)
        self._custom_gauges: Dict[str, float] = {}
        self._custom_timers: Dict[str, List[float]] = defaultdict(list)
        self._lock = threading.RLock()
    
    def increment_counter(self, name: str, value: int = 1) -> None:
        """增加计数器"""
        with self._lock:
            self._custom_counters[name] += value
    
    def set_gauge(self, name: str, value: float) -> None:
        """设置仪表值"""
        with self._lock:
            self._custom_gauges[name] = value
    
    def record_timer(self, name: str, value: float) -> None:
        """记录计时器值"""
        with self._lock:
            self._custom_timers[name].append(value)
            
            # 限制历史数据大小
            if len(self._custom_timers[name]) > 1000:
                self._custom_timers[name] = self._custom_timers[name][-500:]
    
    def collect_business_metrics(self) -> Dict[str, float]:
        """收集业务指标"""
        try:
            with self._lock:
                metrics = {}
                
                # 计数器指标
                for name, value in self._custom_counters.items():
                    metrics[f"counter_{name}"] = float(value)
                
                # 仪表指标
                for name, value in self._custom_gauges.items():
                    metrics[f"gauge_{name}"] = value
                
                # 计时器指标
                for name, values in self._custom_timers.items():
                    if values:
                        metrics[f"timer_{name}_avg"] = statistics.mean(values)
                        metrics[f"timer_{name}_min"] = min(values)
                        metrics[f"timer_{name}_max"] = max(values)
                        metrics[f"timer_{name}_count"] = len(values)
                        
                        if len(values) > 1:
                            sorted_values = sorted(values)
                            metrics[f"timer_{name}_p50"] = sorted_values[len(values) // 2]
                            metrics[f"timer_{name}_p95"] = sorted_values[int(len(values) * 0.95)]
                            metrics[f"timer_{name}_p99"] = sorted_values[int(len(values) * 0.99)]
                
                return metrics
        except Exception as e:
            self.logger.error(f"Failed to collect business metrics: {e}")
            return {}
    
    def reset_counters(self) -> None:
        """重置计数器"""
        with self._lock:
            self._custom_counters.clear()
    
    def clear_timers(self) -> None:
        """清空计时器历史"""
        with self._lock:
            self._custom_timers.clear()


class MetricsAggregator:
    """指标聚合器"""
    
    def __init__(self, window_sizes: List[str] = None):
        self.window_sizes = window_sizes or ["1m", "5m", "15m", "1h", "1d"]
        self.logger = get_logger(f"{__name__}.MetricsAggregator")
        self._metrics_history: deque = deque(maxlen=10000)
        self._lock = threading.RLock()
    
    def add_metrics_snapshot(self, snapshot: MetricsSnapshot) -> None:
        """添加指标快照"""
        with self._lock:
            self._metrics_history.append(snapshot)
    
    def aggregate_metrics(self, metric_name: str, time_window: str) -> Optional[MetricsAggregation]:
        """聚合指标"""
        try:
            window_duration = self._parse_time_window(time_window)
            if not window_duration:
                return None
            
            cutoff_time = datetime.now() - window_duration
            
            # 收集指定时间窗口内的指标值
            values = []
            with self._lock:
                for snapshot in self._metrics_history:
                    if snapshot.timestamp >= cutoff_time:
                        # 从所有指标类型中查找
                        all_metrics = {
                            **snapshot.system_metrics,
                            **snapshot.application_metrics,
                            **snapshot.business_metrics,
                            **snapshot.custom_metrics
                        }
                        
                        if metric_name in all_metrics:
                            values.append(all_metrics[metric_name])
            
            if not values:
                return None
            
            # 计算聚合统计
            sorted_values = sorted(values)
            
            return MetricsAggregation(
                metric_name=metric_name,
                time_window=time_window,
                start_time=cutoff_time,
                end_time=datetime.now(),
                count=len(values),
                min_value=min(values),
                max_value=max(values),
                avg_value=statistics.mean(values),
                p50_value=sorted_values[len(values) // 2],
                p95_value=sorted_values[int(len(values) * 0.95)],
                p99_value=sorted_values[int(len(values) * 0.99)],
                std_dev=statistics.stdev(values) if len(values) > 1 else 0
            )
            
        except Exception as e:
            self.logger.error(f"Failed to aggregate metrics {metric_name}: {e}")
            return None
    
    def _parse_time_window(self, time_window: str) -> Optional[timedelta]:
        """解析时间窗口"""
        try:
            if time_window.endswith('m'):
                minutes = int(time_window[:-1])
                return timedelta(minutes=minutes)
            elif time_window.endswith('h'):
                hours = int(time_window[:-1])
                return timedelta(hours=hours)
            elif time_window.endswith('d'):
                days = int(time_window[:-1])
                return timedelta(days=days)
            else:
                return None
        except ValueError:
            return None
    
    def get_available_metrics(self) -> List[str]:
        """获取可用的指标名称"""
        metrics_set = set()
        
        with self._lock:
            for snapshot in self._metrics_history:
                metrics_set.update(snapshot.system_metrics.keys())
                metrics_set.update(snapshot.application_metrics.keys())
                metrics_set.update(snapshot.business_metrics.keys())
                metrics_set.update(snapshot.custom_metrics.keys())
        
        return sorted(list(metrics_set))


class MetricsStorage:
    """指标存储管理器"""
    
    def __init__(self, storage_path: str = "logs/metrics"):
        self.storage_path = Path(storage_path)
        self.storage_path.mkdir(parents=True, exist_ok=True)
        self.logger = get_logger(f"{__name__}.MetricsStorage")
        self._lock = threading.RLock()
    
    def save_snapshot(self, snapshot: MetricsSnapshot) -> bool:
        """保存指标快照"""
        try:
            date_str = snapshot.timestamp.strftime("%Y-%m-%d")
            file_path = self.storage_path / f"metrics_{date_str}.jsonl"
            
            with self._lock:
                with open(file_path, 'a', encoding='utf-8') as f:
                    json.dump(snapshot.to_dict(), f, ensure_ascii=False)
                    f.write('\n')
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to save metrics snapshot: {e}")
            return False
    
    def load_snapshots(self, start_date: datetime, end_date: datetime) -> List[MetricsSnapshot]:
        """加载指定日期范围的快照"""
        snapshots = []
        
        try:
            current_date = start_date.date()
            end_date_only = end_date.date()
            
            while current_date <= end_date_only:
                date_str = current_date.strftime("%Y-%m-%d")
                file_path = self.storage_path / f"metrics_{date_str}.jsonl"
                
                if file_path.exists():
                    with open(file_path, 'r', encoding='utf-8') as f:
                        for line in f:
                            try:
                                data = json.loads(line.strip())
                                timestamp = datetime.fromisoformat(data['timestamp'])
                                
                                if start_date <= timestamp <= end_date:
                                    snapshot = MetricsSnapshot(
                                        timestamp=timestamp,
                                        system_metrics=data['system_metrics'],
                                        application_metrics=data['application_metrics'],
                                        business_metrics=data['business_metrics'],
                                        custom_metrics=data.get('custom_metrics', {})
                                    )
                                    snapshots.append(snapshot)
                            except Exception as e:
                                self.logger.warning(f"Failed to parse metrics line: {e}")
                
                current_date += timedelta(days=1)
            
        except Exception as e:
            self.logger.error(f"Failed to load metrics snapshots: {e}")
        
        return snapshots
    
    def cleanup_old_files(self, retention_days: int = 30) -> None:
        """清理过期文件"""
        try:
            cutoff_date = datetime.now() - timedelta(days=retention_days)
            
            for file_path in self.storage_path.glob("metrics_*.jsonl"):
                try:
                    # 从文件名提取日期
                    date_str = file_path.stem.replace("metrics_", "")
                    file_date = datetime.strptime(date_str, "%Y-%m-%d")
                    
                    if file_date < cutoff_date:
                        file_path.unlink()
                        self.logger.info(f"Deleted old metrics file: {file_path}")
                        
                except Exception as e:
                    self.logger.warning(f"Failed to process file {file_path}: {e}")
                    
        except Exception as e:
            self.logger.error(f"Failed to cleanup old metrics files: {e}")


class PerformanceMetricsCollector:
    """性能指标收集器主管理器"""
    
    def __init__(self, collection_interval: float = 1.0, storage_enabled: bool = True):
        self.collection_interval = collection_interval
        self.storage_enabled = storage_enabled
        
        # 组件初始化
        self.system_collector = SystemMetricsCollector()
        self.application_collector = ApplicationMetricsCollector()
        self.business_collector = BusinessMetricsCollector()
        self.aggregator = MetricsAggregator()
        self.storage = MetricsStorage() if storage_enabled else None
        
        # 日志和监控
        self.logger = get_logger(f"{__name__}.PerformanceMetricsCollector")
        self.perf_logger = get_global_performance_logger()
        
        # 收集控制
        self._collecting = False
        self._collection_task: Optional[asyncio.Task] = None
        self._lock = threading.RLock()
    
    async def start_collection(self) -> None:
        """开始指标收集"""
        with self._lock:
            if self._collecting:
                return
            
            self._collecting = True
            self._collection_task = asyncio.create_task(self._collection_loop())
            
            self.logger.info("Performance metrics collection started")
    
    async def stop_collection(self) -> None:
        """停止指标收集"""
        with self._lock:
            if not self._collecting:
                return
            
            self._collecting = False
            
            if self._collection_task and not self._collection_task.done():
                self._collection_task.cancel()
                try:
                    await self._collection_task
                except asyncio.CancelledError:
                    pass
            
            self.logger.info("Performance metrics collection stopped")
    
    async def _collection_loop(self) -> None:
        """指标收集循环"""
        while self._collecting:
            try:
                # 收集所有指标
                snapshot = await self.collect_snapshot()
                
                # 添加到聚合器
                self.aggregator.add_metrics_snapshot(snapshot)
                
                # 保存到存储
                if self.storage:
                    self.storage.save_snapshot(snapshot)
                
                # 等待下次收集
                await asyncio.sleep(self.collection_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error in metrics collection loop: {e}")
                await asyncio.sleep(self.collection_interval)
    
    async def collect_snapshot(self) -> MetricsSnapshot:
        """收集当前指标快照"""
        timestamp = datetime.now()
        
        # 并行收集各类指标
        system_metrics = await asyncio.get_event_loop().run_in_executor(
            None, self.system_collector.collect_all_system_metrics
        )
        
        application_metrics = await asyncio.get_event_loop().run_in_executor(
            None, self.application_collector.collect_all_application_metrics
        )
        
        business_metrics = await asyncio.get_event_loop().run_in_executor(
            None, self.business_collector.collect_business_metrics
        )
        
        return MetricsSnapshot(
            timestamp=timestamp,
            system_metrics=system_metrics,
            application_metrics=application_metrics,
            business_metrics=business_metrics
        )
    
    def get_aggregated_metrics(self, metric_name: str, time_window: str) -> Optional[MetricsAggregation]:
        """获取聚合指标"""
        return self.aggregator.aggregate_metrics(metric_name, time_window)
    
    def get_available_metrics(self) -> List[str]:
        """获取可用指标列表"""
        return self.aggregator.get_available_metrics()
    
    def add_custom_metric(self, name: str, value: float) -> None:
        """添加自定义指标"""
        self.business_collector.set_gauge(name, value)
    
    def increment_counter(self, name: str, value: int = 1) -> None:
        """增加计数器"""
        self.business_collector.increment_counter(name, value)
    
    def record_timing(self, name: str, duration_ms: float) -> None:
        """记录计时"""
        self.business_collector.record_timer(name, duration_ms)


# 全局指标收集器实例
_global_metrics_collector: Optional[PerformanceMetricsCollector] = None


def get_global_metrics_collector() -> PerformanceMetricsCollector:
    """获取全局指标收集器实例"""
    global _global_metrics_collector
    
    if _global_metrics_collector is None:
        _global_metrics_collector = PerformanceMetricsCollector()
    
    return _global_metrics_collector


# 模块级别的日志器
module_logger = get_logger(__name__)