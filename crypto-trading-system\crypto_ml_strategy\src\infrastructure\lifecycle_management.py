#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
生命周期管理模块

统一的生命周期管理入口，集成核心管理器和处理器，
提供完整的应用生命周期管理功能。
"""

from typing import Dict, Any, Optional
from loguru import logger

from .lifecycle_management_core import EnhancedLifecycleManager as CoreLifecycleManager
from .lifecycle_management_handlers import LifecycleHandlers
from .memory_monitor import MemoryMonitor
from .async_error_recovery import AsyncErrorRecoveryService
from .lifecycle_manager import AdvancedLifecycleManager
from .service_interfaces import IHealthCheckService, IMetricsService


class EnhancedLifecycleManager:
    """
    统一生命周期管理器
    
    集成核心管理器和处理器，提供完整的生命周期管理能力。
    """
    
    def __init__(self,
                 memory_monitor: MemoryMonitor,
                 async_error_recovery: AsyncErrorRecoveryService,
                 health_check_service: IHealthCheckService,
                 metrics_service: IMetricsService,
                 base_lifecycle_manager: Optional[AdvancedLifecycleManager] = None):
        """
        初始化统一生命周期管理器
        
        Args:
            memory_monitor: 内存监控服务
            async_error_recovery: 异步错误恢复服务
            health_check_service: 健康检查服务
            metrics_service: 指标服务
            base_lifecycle_manager: 基础生命周期管理器（向后兼容）
        """
        # 初始化核心管理器
        self.core_manager = CoreLifecycleManager(
            memory_monitor=memory_monitor,
            async_error_recovery=async_error_recovery,
            health_check_service=health_check_service,
            metrics_service=metrics_service,
            base_lifecycle_manager=base_lifecycle_manager
        )
        
        # 初始化处理器
        self.handlers = LifecycleHandlers(self.core_manager)
        
        logger.info("统一生命周期管理器初始化完成")
    
    async def start_application(self) -> None:
        """启动应用"""
        # 设置信号处理器
        self.handlers.setup_signal_handlers()
        
        # 启动核心应用
        await self.core_manager.start_application()
    
    async def stop_application(self) -> None:
        """停止应用"""
        await self.core_manager.stop_application()
    
    def get_status(self) -> Dict[str, Any]:
        """获取生命周期管理器状态"""
        return {
            "running": self.core_manager._running,
            "startup_time_seconds": self.core_manager._startup_time,
            "memory_monitoring": self.core_manager.memory_monitor.get_current_status(),
            "async_recovery_metrics": self.core_manager.async_error_recovery.get_metrics(),
            "base_lifecycle_status": (
                self.core_manager.base_lifecycle_manager.get_component_status() 
                if self.core_manager.base_lifecycle_manager else None
            )
        }