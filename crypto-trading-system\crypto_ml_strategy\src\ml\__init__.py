"""
Crypto ML Strategy - 机器学习模块

该包包含机器学习相关的所有组件，包括模型训练、在线学习、
推理优化和数据结构优化等功能。

主要子模块:
- models: 模型定义、训练和预测
- online_learning: 在线学习和增量训练
- training: 训练管道和配置管理
- ml_inference_optimizer: ML推理优化器
- model_optimization_pipeline: 模型优化管道
- memory_efficient_data_structures: 内存优化数据结构
- large_dataset_processor: 大数据集处理器

作者: Crypto ML Strategy Team
版本: 1.0.0
"""

from .ml_inference_optimizer import MLInferenceOptimizer
from .model_optimization_pipeline import ModelOptimizationPipeline
from .memory_efficient_data_structures import MemoryEfficientDataStructures
from .large_dataset_processor import LargeDatasetProcessor

__all__ = [
    'MLInferenceOptimizer',
    'ModelOptimizationPipeline',
    'MemoryEfficientDataStructures',
    'LargeDatasetProcessor'
]

__version__ = '1.0.0'
__author__ = 'Crypto ML Strategy Team'