package com.crypto.trading.trade.executor;

import com.crypto.trading.common.constant.OrderEnums.OrderStatus;
import com.crypto.trading.common.dto.trade.OrderDTO;
import com.crypto.trading.common.enums.OrderSide;
import com.crypto.trading.common.enums.OrderType;
import com.crypto.trading.common.exception.BusinessException;
import com.crypto.trading.common.exception.ErrorCode;
import com.crypto.trading.common.util.ThreadUtil;
import com.crypto.trading.sdk.client.BinanceApiClient;
import com.crypto.trading.sdk.exception.ApiException;
import com.crypto.trading.trade.config.TradeExecutionConfig;
import com.crypto.trading.trade.model.entity.order.OrderEntity;
import com.crypto.trading.trade.model.entity.order.OrderExecutionLogEntity;
import com.crypto.trading.trade.repository.mapper.order.OrderExecutionLogMapper;
import com.crypto.trading.trade.service.TradeExecutionService;
import com.crypto.trading.trade.service.order.OrderConverter;
import com.crypto.trading.trade.service.order.OrderManagementService;
import io.github.resilience4j.ratelimiter.RateLimiter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

/**
 * 币安交易执行器
 * <p>
 * 负责与币安API交互，执行交易操作
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
public class BinanceTradeExecutor {

    private static final Logger log = LoggerFactory.getLogger(BinanceTradeExecutor.class);

    @Autowired
    private BinanceApiClient binanceApiClient;

    @Autowired
    private TradeExecutionService tradeExecutionService;

    @Autowired
    private OrderManagementService orderManagementService;

    @Autowired
    private OrderExecutionLogMapper orderExecutionLogMapper;

    @Autowired
    private TradeExecutionConfig tradeExecutionConfig;

    @Autowired
    @Qualifier("orderRateLimiter")
    private RateLimiter orderRateLimiter;

    /**
     * 执行市价单
     *
     * @param order 订单实体
     * @return 执行结果的CompletableFuture
     */
    public CompletableFuture<OrderEntity> executeMarketOrder(OrderEntity order) {
        log.info("执行市价单: orderId={}, symbol={}, side={}, quantity={}",
                order.getOrderId(), order.getSymbol(), order.getSide(), order.getQuantity());

        // 如果是试运行模式，不实际下单
        if (tradeExecutionConfig.isDryRun()) {
            log.info("试运行模式，不实际下单: {}", order);
            // 模拟订单执行
            return simulateOrderExecution(order);
        }

        // 使用虚拟线程执行API调用
        return CompletableFuture.supplyAsync(() -> {
            // 创建执行日志
            String executionId = UUID.randomUUID().toString();
            OrderExecutionLogEntity.Builder logBuilder = new OrderExecutionLogEntity.Builder()
                    .executionId(executionId)
                    .orderId(order.getOrderId())
                    .clientOrderId(order.getClientOrderId())
                    .action("PLACE_MARKET_ORDER")
                    .requestData(order.toString());

            try {
                // 使用限速器
                orderRateLimiter.acquirePermission();

                // 将Double转换为BigDecimal
                BigDecimal quantity = order.getQuantity() != null ? 
                        BigDecimal.valueOf(order.getQuantity()) : null;

                // 调用API下单
                String orderId = binanceApiClient.placeMarketOrder(
                        order.getSymbol(),
                        order.getSide(),
                        order.getPositionSide(),
                        quantity,
                        order.getClientOrderId());

                // 查询订单状态
                Map<String, Object> orderStatus = binanceApiClient.queryOrder(
                        order.getSymbol(),
                        orderId,
                        null);

                // 记录成功日志
                logBuilder.success(true)
                        .responseData(orderStatus.toString());
                OrderExecutionLogEntity log = logBuilder.build();
                orderExecutionLogMapper.insert(log);

                // 更新订单信息
                order.setOrderId(orderId);
                order.setStatus(OrderStatus.NEW.getCode());
                order.setUpdatedTime(System.currentTimeMillis());

                // 更新数据库
                orderManagementService.updateOrderStatus(
                        orderId,
                        OrderStatus.NEW.getCode(),
                        "市价单已提交",
                        orderStatus.toString());

                // 发送订单执行结果
                OrderDTO orderDTO = OrderConverter.toOrderDTO(order);
                tradeExecutionService.processOrderExecutionResult(orderDTO);

                return order;
            } catch (ApiException e) {
                // 记录失败日志
                logBuilder.success(false)
                        .errorCode(String.valueOf(e.getCode()))
                        .errorMessage(e.getMessage());
                OrderExecutionLogEntity log = logBuilder.build();
                orderExecutionLogMapper.insert(log);

                // 更新订单状态为拒绝
                order.setStatus(OrderStatus.REJECTED.getCode());
                order.setErrorCode(String.valueOf(e.getCode()));
                order.setErrorMessage(e.getMessage());
                order.setUpdatedTime(System.currentTimeMillis());

                orderManagementService.updateOrderStatus(
                        order.getOrderId(),
                        OrderStatus.REJECTED.getCode(),
                        "API调用失败",
                        e.getMessage());

                // 发送订单执行错误
                tradeExecutionService.processOrderExecutionError(
                        order.getClientOrderId(),
                        order.getSymbol(),
                        e.getCode(),
                        e.getMessage());

                throw new BusinessException(ErrorCode.BINANCE_API_ERROR, "执行市价单失败: " + e.getMessage());
            } catch (Exception e) {
                // 记录失败日志
                logBuilder.success(false)
                        .errorCode("SYSTEM_ERROR")
                        .errorMessage(e.getMessage());
                OrderExecutionLogEntity log = logBuilder.build();
                orderExecutionLogMapper.insert(log);

                // 更新订单状态为拒绝
                order.setStatus(OrderStatus.REJECTED.getCode());
                order.setErrorCode("SYSTEM_ERROR");
                order.setErrorMessage(e.getMessage());
                order.setUpdatedTime(System.currentTimeMillis());

                orderManagementService.updateOrderStatus(
                        order.getOrderId(),
                        OrderStatus.REJECTED.getCode(),
                        "系统错误",
                        e.getMessage());

                // 发送订单执行错误
                tradeExecutionService.processOrderExecutionError(
                        order.getClientOrderId(),
                        order.getSymbol(),
                        -1,
                        e.getMessage());

                throw new BusinessException(ErrorCode.SYSTEM_ERROR, "执行市价单失败: " + e.getMessage());
            }
        }, ThreadUtil.toExecutor(Thread.ofVirtual().factory()));
    }

    /**
     * 查询订单状态
     *
     * @param symbol        交易对
     * @param orderId       订单ID
     * @param clientOrderId 客户端订单ID
     * @return 订单状态的CompletableFuture
     */
    public CompletableFuture<Map<String, Object>> queryOrderStatus(String symbol, String orderId, String clientOrderId) {
        log.info("查询订单状态: symbol={}, orderId={}, clientOrderId={}", symbol, orderId, clientOrderId);

        // 如果是试运行模式，返回模拟数据
        if (tradeExecutionConfig.isDryRun()) {
            log.info("试运行模式，返回模拟订单状态");
            return CompletableFuture.completedFuture(Map.of(
                    "symbol", symbol,
                    "orderId", orderId != null ? orderId : "123456789",
                    "clientOrderId", clientOrderId,
                    "status", "FILLED",
                    "executedQty", "1.0",
                    "price", "50000.0"
            ));
        }

        // 使用虚拟线程执行API调用
        return CompletableFuture.supplyAsync(() -> {
            try {
                // 调用API查询订单
                return binanceApiClient.queryOrder(symbol, orderId, clientOrderId);
            } catch (Exception e) {
                log.error("查询订单状态失败: symbol={}, orderId={}, clientOrderId={}, error={}",
                        symbol, orderId, clientOrderId, e.getMessage(), e);
                throw new BusinessException(ErrorCode.QUERY_ORDER_FAILED, "查询订单状态失败: " + e.getMessage());
            }
        }, ThreadUtil.toExecutor(Thread.ofVirtual().factory()));
    }

    /**
     * 取消订单
     *
     * @param order 订单实体
     * @return 取消结果的CompletableFuture
     */
    public CompletableFuture<OrderEntity> cancelOrder(OrderEntity order) {
        log.info("取消订单: orderId={}, symbol={}, clientOrderId={}",
                order.getOrderId(), order.getSymbol(), order.getClientOrderId());

        // 如果是试运行模式，不实际取消
        if (tradeExecutionConfig.isDryRun()) {
            log.info("试运行模式，不实际取消订单: {}", order);
            // 模拟订单取消
            order.setStatus(OrderStatus.CANCELED.getCode());
            order.setUpdatedTime(System.currentTimeMillis());
            return CompletableFuture.completedFuture(order);
        }

        // TODO: 实现实际的订单取消逻辑
        // 由于BinanceApiClient接口中没有提供cancelOrder方法，这里暂时只更新状态
        // 后续需要扩展BinanceApiClient接口，添加cancelOrder方法

        // 更新订单状态为取消
        order.setStatus(OrderStatus.CANCELED.getCode());
        order.setUpdatedTime(System.currentTimeMillis());

        orderManagementService.updateOrderStatus(
                order.getOrderId(),
                OrderStatus.CANCELED.getCode(),
                "用户取消",
                null);

        // 发送订单执行结果
        OrderDTO orderDTO = OrderConverter.toOrderDTO(order);
        tradeExecutionService.processOrderExecutionResult(orderDTO);

        return CompletableFuture.completedFuture(order);
    }

    /**
     * 模拟订单执行
     *
     * @param order 订单实体
     * @return 模拟执行结果的CompletableFuture
     */
    private CompletableFuture<OrderEntity> simulateOrderExecution(OrderEntity order) {
        // 始终设置模拟订单ID，确保以"SIM"开头
        order.setOrderId("SIM" + UUID.randomUUID().toString().substring(0, 8));

        // 设置模拟执行结果
        order.setStatus(OrderStatus.FILLED.getCode());
        order.setExecutedQuantity(order.getQuantity());
        
        // 模拟成交价格（使用订单价格，如果是市价单则使用随机价格）
        if (order.getPrice() != null) {
            order.setExecutedPrice(order.getPrice());
        } else {
            // 模拟一个合理的价格，这里简单起见使用50000作为基准价格
            Double basePrice = 50000.0;
            // 添加一些随机波动，±1%
            Double randomFactor = 0.98 + Math.random() * 0.04;
            order.setExecutedPrice(basePrice * randomFactor);
        }

        order.setUpdatedTime(System.currentTimeMillis());
        order.setExecutedTime(System.currentTimeMillis());

        // 更新数据库
        orderManagementService.updateOrderStatus(
                order.getOrderId(),
                OrderStatus.FILLED.getCode(),
                "模拟执行",
                null);

        // 发送订单执行结果
        OrderDTO orderDTO = OrderConverter.toOrderDTO(order);
        tradeExecutionService.processOrderExecutionResult(orderDTO);

        return CompletableFuture.completedFuture(order);
    }
}