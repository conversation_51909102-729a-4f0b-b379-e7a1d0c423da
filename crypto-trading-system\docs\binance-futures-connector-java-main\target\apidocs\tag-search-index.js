tagSearchIndex = [{"l":"Coin-Margined Market Endpoints","h":"类 com.binance.connector.futures.client.impl.cm_futures.CMMarket","d":"节","u":"com/binance/connector/futures/client/impl/cm_futures/CMMarket.html#coin-margined-market-endpoints-heading"},{"l":"Coin-Margined Portfolio Margin Endpoints","h":"类 com.binance.connector.futures.client.impl.cm_futures.CMPortfolioMargin","d":"节","u":"com/binance/connector/futures/client/impl/cm_futures/CMPortfolioMargin.html#coin-margined-portfolio-margin-endpoints-heading"},{"l":"Coin-Margined Trade Endpoints","h":"类 com.binance.connector.futures.client.impl.cm_futures.CMAccount","d":"节","u":"com/binance/connector/futures/client/impl/cm_futures/CMAccount.html#coin-margined-trade-endpoints-heading"},{"l":"Coin-Margined User Data Streams Endpoints","h":"类 com.binance.connector.futures.client.impl.cm_futures.CMUserData","d":"节","u":"com/binance/connector/futures/client/impl/cm_futures/CMUserData.html#coin-margined-user-data-streams-endpoints-heading"},{"l":"COIN-M Websocket Streams","h":"类 com.binance.connector.futures.client.impl.CMWebsocketClientImpl","d":"节","u":"com/binance/connector/futures/client/impl/CMWebsocketClientImpl.html#coin-m-websocket-streams-heading"},{"l":"Futures Websocket Streams","h":"类 com.binance.connector.futures.client.impl.WebsocketClientImpl","d":"节","u":"com/binance/connector/futures/client/impl/WebsocketClientImpl.html#futures-websocket-streams-heading"},{"l":"Market Endpoints","h":"类 com.binance.connector.futures.client.impl.futures.Market","d":"节","u":"com/binance/connector/futures/client/impl/futures/Market.html#market-endpoints-heading"},{"l":"Portfolio Margin Endpoints","h":"类 com.binance.connector.futures.client.impl.futures.PortfolioMargin","d":"节","u":"com/binance/connector/futures/client/impl/futures/PortfolioMargin.html#portfolio-margin-endpoints-heading"},{"l":"Trade Endpoints","h":"类 com.binance.connector.futures.client.impl.futures.Account","d":"节","u":"com/binance/connector/futures/client/impl/futures/Account.html#trade-endpoints-heading"},{"l":"USDⓈ-Margined Market Endpoints","h":"类 com.binance.connector.futures.client.impl.um_futures.UMMarket","d":"节","u":"com/binance/connector/futures/client/impl/um_futures/UMMarket.html#usd--margined-market-endpoints-heading"},{"l":"USDⓈ-Margined Trade Endpoints","h":"类 com.binance.connector.futures.client.impl.um_futures.UMAccount","d":"节","u":"com/binance/connector/futures/client/impl/um_futures/UMAccount.html#usd--margined-trade-endpoints-heading"},{"l":"USDⓈ-Margined User Data Streams Endpoints","h":"类 com.binance.connector.futures.client.impl.um_futures.UMUserData","d":"节","u":"com/binance/connector/futures/client/impl/um_futures/UMUserData.html#usd--margined-user-data-streams-endpoints-heading"},{"l":"USDⓈ-M Websocket Streams","h":"类 com.binance.connector.futures.client.impl.UMWebsocketClientImpl","d":"节","u":"com/binance/connector/futures/client/impl/UMWebsocketClientImpl.html#usd--m-websocket-streams-heading"},{"l":"User Data Streams Endpoints","h":"类 com.binance.connector.futures.client.impl.futures.UserData","d":"节","u":"com/binance/connector/futures/client/impl/futures/UserData.html#user-data-streams-endpoints-heading"}];updateSearchResults();