<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile default="true" name="Default" enabled="true" />
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <module name="crypto-common" />
        <module name="crypto-sdk" />
        <module name="crypto-market-data" />
        <module name="crypto-strategy" />
        <module name="crypto-bootstrap" />
        <module name="crypto-trade" />
      </profile>
    </annotationProcessing>
    <bytecodeTargetLevel>
      <module name="common" target="21" />
      <module name="crypto-common (1)" target="21" />
      <module name="crypto-common (2)" target="21" />
      <module name="crypto-strategy" target="21" />
      <module name="crypto-trading (1)" target="21" />
      <module name="crypto-trading (2)" target="21" />
      <module name="crypto-trading-system" target="21" />
      <module name="market-data" target="21" />
      <module name="virtual-currency-quant-system" target="21" />
    </bytecodeTargetLevel>
  </component>
  <component name="JavacSettings">
    <option name="ADDITIONAL_OPTIONS_OVERRIDE">
      <module name="crypto-bootstrap" options="-parameters --enable-preview" />
      <module name="crypto-common" options="-parameters --enable-preview" />
      <module name="crypto-common (1)" options="--enable-preview -parameters" />
      <module name="crypto-market-data" options="-parameters --enable-preview" />
      <module name="crypto-sdk" options="-parameters --enable-preview" />
      <module name="crypto-strategy" options="-parameters" />
      <module name="crypto-trade" options="-parameters --enable-preview" />
      <module name="crypto-trading-system" options="--enable-preview" />
    </option>
  </component>
</project>