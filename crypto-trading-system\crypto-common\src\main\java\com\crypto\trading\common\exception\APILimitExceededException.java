package com.crypto.trading.common.exception;

import java.io.Serial;

/**
 * API限流异常类
 * <p>
 * 用于处理币安API限流情况，继承自BusinessException
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class APILimitExceededException extends BusinessException {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 限流类型（权重限流或订单限流）
     */
    private final String limitType;

    /**
     * 剩余重试时间（毫秒）
     */
    private final long retryAfterMs;

    /**
     * 构造函数
     *
     * @param errorCode    错误码枚举
     * @param limitType    限流类型
     * @param retryAfterMs 剩余重试时间（毫秒）
     */
    public APILimitExceededException(ErrorCode errorCode, String limitType, long retryAfterMs) {
        super(String.valueOf(errorCode.getCode()), errorCode.getMessage());
        this.limitType = limitType;
        this.retryAfterMs = retryAfterMs;
    }

    /**
     * 构造函数
     *
     * @param errorCode    错误码枚举
     * @param message      错误信息
     * @param limitType    限流类型
     * @param retryAfterMs 剩余重试时间（毫秒）
     */
    public APILimitExceededException(ErrorCode errorCode, String message, String limitType, long retryAfterMs) {
        super(String.valueOf(errorCode.getCode()), message);
        this.limitType = limitType;
        this.retryAfterMs = retryAfterMs;
    }

    /**
     * 获取限流类型
     *
     * @return 限流类型
     */
    public String getLimitType() {
        return limitType;
    }

    /**
     * 获取剩余重试时间（毫秒）
     *
     * @return 剩余重试时间（毫秒）
     */
    public long getRetryAfterMs() {
        return retryAfterMs;
    }
}