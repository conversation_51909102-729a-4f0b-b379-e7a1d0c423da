<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>UMWebsocketClientImpl.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">io.github.binance:binance-futures-connector-java</a> &gt; <a href="index.source.html" class="el_package">com.binance.connector.futures.client.impl</a> &gt; <span class="el_source">UMWebsocketClientImpl.java</span></div><h1>UMWebsocketClientImpl.java</h1><pre class="source lang-java linenums">package com.binance.connector.futures.client.impl;

import com.binance.connector.futures.client.enums.DefaultUrls;
import com.binance.connector.futures.client.utils.RequestBuilder;
import com.binance.connector.futures.client.utils.WebSocketCallback;
import com.binance.connector.futures.client.utils.ParameterChecker;
import okhttp3.Request;

/**
 * &lt;h2&gt;USDⓈ-M  Websocket Streams&lt;/h2&gt;
 * All stream endpoints under the
 * &lt;a href=&quot;https://developers.binance.com/docs/derivatives/usds-margined-futures/websocket-market-streams/Connect&quot;&gt; Websocket Market Streams&lt;/a&gt; and
 * &lt;a href=&quot;https://developers.binance.com/docs/derivatives/usds-margined-futures/user-data-streams/Connect&quot;&gt; User Data Streams&lt;/a&gt;
 * section of the API documentation will be implemented in this class.
 * &lt;br&gt;
 * Response will be returned as callback.
 */
public class UMWebsocketClientImpl extends WebsocketClientImpl {

    public UMWebsocketClientImpl() {
<span class="nc" id="L21">        super(DefaultUrls.USDM_WS_URL);</span>
<span class="nc" id="L22">    }</span>

    public UMWebsocketClientImpl(String baseUrl) {
<span class="nc" id="L25">        super(baseUrl);</span>
<span class="nc" id="L26">    }</span>

    /**
     * Mark price and funding rate for all symbols pushed every 3 seconds or every second.
     * &lt;br&gt;&lt;br&gt;
     * &amp;lt;symbol&amp;gt;@markPrice or &amp;lt;symbol&amp;gt;@markPrice@1s
     * &lt;br&gt;&lt;br&gt;
     * Update Speed: 3000ms or 1000ms
     *
     * @param speed speed in seconds, can be 1 or 3
     * @param onMessageCallback onMessageCallback
     * @return int - Connection ID
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/usds-margined-futures/websocket-market-streams/Mark-Price-Stream&quot;&gt;
     * https://developers.binance.com/docs/derivatives/usds-margined-futures/websocket-market-streams/Mark-Price-Stream&lt;/a&gt;
     */
    public int allMarkPriceStream(int speed, WebSocketCallback onMessageCallback) {
<span class="nc" id="L42">        return allMarkPriceStream(speed, getNoopCallback(), onMessageCallback, getNoopCallback(), getNoopCallback());</span>
    }

    /**
     * Same as {@link #allMarkPriceStream(int, WebSocketCallback)} plus accepts callbacks for all major websocket connection events.
     *
     * @param speed speed in seconds, can be 1 or 3
     * @param onOpenCallback onOpenCallback
     * @param onMessageCallback onMessageCallback
     * @param onClosingCallback onClosingCallback
     * @param onFailureCallback onFailureCallback
     * @return int - Connection ID
     */
    public int allMarkPriceStream(int speed, WebSocketCallback onOpenCallback, WebSocketCallback onMessageCallback, WebSocketCallback onClosingCallback, WebSocketCallback onFailureCallback) {
<span class="nc" id="L56">        Request request = null;</span>
<span class="nc" id="L57">        final int defaultSpeed = 3;</span>
<span class="nc bnc" id="L58" title="All 2 branches missed.">        if (speed == defaultSpeed) {</span>
<span class="nc" id="L59">            request = RequestBuilder.buildWebsocketRequest(String.format(&quot;%s/ws/!markPrice@arr&quot;, getBaseUrl()));</span>
        } else {
<span class="nc" id="L61">            request = RequestBuilder.buildWebsocketRequest(String.format(&quot;%s/ws/!markPrice@arr@%ss&quot;, getBaseUrl(), speed));</span>
        }
<span class="nc" id="L63">        return super.createConnection(onOpenCallback, onMessageCallback, onClosingCallback, onFailureCallback, request);</span>
    }

    /**
     * Composite index information for index symbols pushed every second.
     * &lt;br&gt;&lt;br&gt;
     * &amp;lt;symbol&amp;gt;@compositeIndex
     * &lt;br&gt;&lt;br&gt;
     * Update Speed: 1000ms
     *
     * @param symbol trading symbol
     * @param onMessageCallback onMessageCallback
     * @return int - Connection ID
     * @see &lt;a href=&quot;https://developers.binance.com/docs/derivatives/usds-margined-futures/websocket-market-streams/Composite-Index-Symbol-Information-Streams&quot;&gt;
     * https://developers.binance.com/docs/derivatives/usds-margined-futures/websocket-market-streams/Composite-Index-Symbol-Information-Streams&lt;/a&gt;
     */
    public int compositeIndexSymbolInfo(String symbol, WebSocketCallback onMessageCallback) {
<span class="nc" id="L80">        ParameterChecker.checkParameterType(symbol, String.class, &quot;symbol&quot;);</span>
<span class="nc" id="L81">        return compositeIndexSymbolInfo(symbol, getNoopCallback(), onMessageCallback, getNoopCallback(), getNoopCallback());</span>
    }

    /**
     * Same as {@link #compositeIndexSymbolInfo(String, WebSocketCallback)} plus accepts callbacks for all major websocket connection events.
     *
     * @param symbol trading symbol
     * @param onOpenCallback onOpenCallback
     * @param onMessageCallback onMessageCallback
     * @param onClosingCallback onClosingCallback
     * @param onFailureCallback onFailureCallback
     * @return int - Connection ID
     */
    public int compositeIndexSymbolInfo(String symbol, WebSocketCallback onOpenCallback, WebSocketCallback onMessageCallback, WebSocketCallback onClosingCallback, WebSocketCallback onFailureCallback) {
<span class="nc" id="L95">        ParameterChecker.checkParameterType(symbol, String.class, &quot;symbol&quot;);</span>
<span class="nc" id="L96">        Request request = RequestBuilder.buildWebsocketRequest(String.format(&quot;%s/ws/%s@compositeIndex&quot;, getBaseUrl(), symbol.toLowerCase()));</span>
<span class="nc" id="L97">        return createConnection(onOpenCallback, onMessageCallback, onClosingCallback, onFailureCallback, request);</span>
    }

}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.7.202105040129</span></div></body></html>