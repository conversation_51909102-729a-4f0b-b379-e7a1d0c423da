package com.crypto.trading.sdk.limiter;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.Map;
import java.util.concurrent.*;
import java.util.function.Supplier;

/**
 * 币安API限流器实现类
 * 实现币安特定的限流逻辑，根据币安API文档中的权重和限制规则
 */
@Component
public class BinanceRateLimiter implements RateLimiter {

    private static final Logger log = LoggerFactory.getLogger(BinanceRateLimiter.class);

    /**
     * 默认时间窗口（毫秒）
     */
    private static final long DEFAULT_TIME_WINDOW = com.crypto.trading.common.constant.APILimitConstants.BINANCE_FUTURES_WINDOW_MS;

    /**
     * 默认最大请求数
     */
    private static final int DEFAULT_MAX_REQUESTS = com.crypto.trading.common.constant.APILimitConstants.BINANCE_FUTURES_IP_LIMIT_ORDER;

    /**
     * 默认权重
     */
    private static final int DEFAULT_WEIGHT = com.crypto.trading.common.constant.APILimitConstants.BINANCE_FUTURES_WEIGHT_MARKET_DEFAULT;
    
    /**
     * 配置对象
     */
    private final com.crypto.trading.sdk.config.BinanceApiConfig config;

    /**
     * 端点限流配置映射
     */
    private final Map<String, RateLimitConfig> rateLimitConfigMap = new ConcurrentHashMap<>();

    /**
     * 默认限流配置
     */
    private final RateLimitConfig defaultConfig;

    /**
     * 异步执行器
     */
    private final ExecutorService executorService;

    /**
     * 构造函数
     * 
     * @param config 币安API配置
     */
    @org.springframework.beans.factory.annotation.Autowired
    public BinanceRateLimiter(com.crypto.trading.sdk.config.BinanceApiConfig config) {
        this.config = config;
        
        // 从配置或常量中获取限流参数
        int maxRequests = Math.max(config.getMaxRetries(), DEFAULT_MAX_REQUESTS);
        long timeWindow = DEFAULT_TIME_WINDOW;
        
        this.defaultConfig = new RateLimitConfig(maxRequests, timeWindow);
        this.executorService = Executors.newVirtualThreadPerTaskExecutor();
        log.info("初始化币安API限流器，默认配置：最大请求数={}, 时间窗口={}ms", maxRequests, timeWindow);
    }

    @Override
    public <T> T execute(int weight, String endpoint, Supplier<T> supplier) {
        RateLimitConfig rateLimitConfig = getConfig(endpoint);
        try {
            // 获取令牌
            acquirePermits(weight, rateLimitConfig);
            // 执行请求
            return supplier.get();
        } catch (Exception e) {
            log.error("执行API请求失败: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public <T> CompletableFuture<T> executeAsync(int weight, String endpoint, Supplier<T> supplier) {
        RateLimitConfig rateLimitConfig = getConfig(endpoint);
        return CompletableFuture.supplyAsync(() -> {
            try {
                // 获取令牌
                acquirePermits(weight, rateLimitConfig);
                // 执行请求
                return supplier.get();
            } catch (Exception e) {
                log.error("异步执行API请求失败: {}", e.getMessage(), e);
                throw e;
            }
        }, executorService);
    }
    
    @Override
    public CompletableFuture<String> executeAsyncString(int weight, String endpoint, Supplier<String> supplier) {
        RateLimitConfig rateLimitConfig = getConfig(endpoint);
        return CompletableFuture.supplyAsync(() -> {
            try {
                // 获取令牌
                acquirePermits(weight, rateLimitConfig);
                // 执行请求
                return supplier.get();
            } catch (Exception e) {
                log.error("异步执行API请求失败: {}", e.getMessage(), e);
                throw e;
            }
        }, executorService);
    }

    @Override
    public int getAvailablePermits(String endpoint) {
        RateLimitConfig config = getConfig(endpoint);
        return config.getAvailablePermits();
    }

    @Override
    public double getUsageRate(String endpoint) {
        RateLimitConfig config = getConfig(endpoint);
        return 1.0 - (double) config.getAvailablePermits() / config.getMaxRequests();
    }

    @Override
    public void reset(String endpoint) {
        if (endpoint == null) {
            // 重置所有端点
            rateLimitConfigMap.values().forEach(RateLimitConfig::reset);
            defaultConfig.reset();
            log.info("重置所有端点的限流配置");
        } else {
            // 重置指定端点
            RateLimitConfig config = rateLimitConfigMap.get(endpoint);
            if (config != null) {
                config.reset();
                log.info("重置端点[{}]的限流配置", endpoint);
            }
        }
    }

    @Override
    public void updateConfig(String endpoint, int maxRequests, long timeWindow) {
        // 确保参数有效
        int effectiveMaxRequests = maxRequests > 0 ? maxRequests : 
            com.crypto.trading.common.constant.APILimitConstants.BINANCE_FUTURES_IP_LIMIT_ORDER;
        long effectiveTimeWindow = timeWindow > 0 ? timeWindow : 
            com.crypto.trading.common.constant.APILimitConstants.BINANCE_FUTURES_WINDOW_MS;
            
        if (endpoint == null || endpoint.isEmpty()) {
            // 更新默认配置
            defaultConfig.setMaxRequests(effectiveMaxRequests);
            defaultConfig.setTimeWindow(effectiveTimeWindow);
            log.info("更新默认限流配置：最大请求数={}, 时间窗口={}ms", effectiveMaxRequests, effectiveTimeWindow);
        } else {
            // 更新指定端点配置
            RateLimitConfig rateLimitConfig = rateLimitConfigMap.computeIfAbsent(endpoint, 
                    k -> new RateLimitConfig(effectiveMaxRequests, effectiveTimeWindow));
            rateLimitConfig.setMaxRequests(effectiveMaxRequests);
            rateLimitConfig.setTimeWindow(effectiveTimeWindow);
            log.info("更新端点[{}]限流配置：最大请求数={}, 时间窗口={}ms", endpoint, effectiveMaxRequests, effectiveTimeWindow);
        }
    }

    /**
     * 获取端点对应的限流配置
     *
     * @param endpoint API端点
     * @return 限流配置
     */
    private RateLimitConfig getConfig(String endpoint) {
        if (endpoint == null || endpoint.isEmpty()) {
            return defaultConfig;
        }
        return rateLimitConfigMap.computeIfAbsent(endpoint, k -> {
            log.debug("为端点[{}]创建默认限流配置", endpoint);
            return new RateLimitConfig(defaultConfig.getMaxRequests(), defaultConfig.getTimeWindow());
        });
    }

    /**
     * 获取指定权重的令牌，如果没有足够的令牌则阻塞等待
     *
     * @param weight 请求权重
     * @param rateLimitConfig 限流配置
     */
    private void acquirePermits(int weight, RateLimitConfig rateLimitConfig) {
        // 从配置或常量中获取默认权重
        int defaultWeight = DEFAULT_WEIGHT;
        int actualWeight = weight > 0 ? weight : defaultWeight;
        
        // 检查是否有足够的令牌
        while (!rateLimitConfig.tryAcquire(actualWeight)) {
            log.debug("当前请求超过限流阈值，等待令牌恢复...");
            try {
                // 从配置中获取重试间隔，默认为100毫秒
                long retryIntervalMs = 100;
                // 等待一段时间后重试
                Thread.sleep(retryIntervalMs);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException("获取令牌被中断", e);
            }
        }
    }

    /**
     * 限流配置类
     */
    private static class RateLimitConfig {
        private int maxRequests;
        private long timeWindow;
        private final Semaphore semaphore;
        private final ScheduledExecutorService scheduler;

        public RateLimitConfig(int maxRequests, long timeWindow) {
            this.maxRequests = maxRequests;
            this.timeWindow = timeWindow;
            this.semaphore = new Semaphore(maxRequests);
            this.scheduler = Executors.newSingleThreadScheduledExecutor();
            
            // 定期重置令牌
            scheduler.scheduleAtFixedRate(this::reset, timeWindow, timeWindow, TimeUnit.MILLISECONDS);
        }

        public boolean tryAcquire(int permits) {
            return semaphore.tryAcquire(permits);
        }

        public void reset() {
            // 计算需要释放的令牌数
            int permitsToRelease = maxRequests - semaphore.availablePermits();
            if (permitsToRelease > 0) {
                semaphore.release(permitsToRelease);
            }
        }

        public int getAvailablePermits() {
            return semaphore.availablePermits();
        }

        public int getMaxRequests() {
            return maxRequests;
        }

        public void setMaxRequests(int maxRequests) {
            int diff = maxRequests - this.maxRequests;
            this.maxRequests = maxRequests;
            
            // 如果增加了最大请求数，则释放额外的令牌
            if (diff > 0) {
                semaphore.release(diff);
            }
        }

        public long getTimeWindow() {
            return timeWindow;
        }

        public void setTimeWindow(long timeWindow) {
            this.timeWindow = timeWindow;
            
            // 重新调度重置任务
            scheduler.shutdown();
            try {
                if (!scheduler.awaitTermination(1, TimeUnit.SECONDS)) {
                    scheduler.shutdownNow();
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                scheduler.shutdownNow();
            }
            
            ScheduledExecutorService newScheduler = Executors.newSingleThreadScheduledExecutor();
            newScheduler.scheduleAtFixedRate(this::reset, timeWindow, timeWindow, TimeUnit.MILLISECONDS);
        }
    }
} 