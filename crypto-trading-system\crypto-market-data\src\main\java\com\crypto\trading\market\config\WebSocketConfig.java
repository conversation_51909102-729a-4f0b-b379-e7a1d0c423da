package com.crypto.trading.market.config;

import com.crypto.trading.sdk.websocket.BinanceWebSocketClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * WebSocket配置类
 * 负责提供WebSocket相关的配置和参数
 */
@Configuration
public class WebSocketConfig {

    private static final Logger log = LoggerFactory.getLogger(WebSocketConfig.class);

    /**
     * WebSocket自动重连间隔（毫秒）
     */
    @Value("${market.websocket.reconnect-interval:5000}")
    private long reconnectInterval;

    /**
     * WebSocket连接超时时间（毫秒）
     */
    @Value("${market.websocket.connect-timeout:5000}")
    private int connectTimeout;

    /**
     * WebSocket读取超时时间（毫秒）
     */
    @Value("${market.websocket.read-timeout:30000}")
    private int readTimeout;

    /**
     * WebSocket心跳间隔（毫秒）
     */
    @Value("${market.websocket.ping-interval:30000}")
    private long pingInterval;
    
    /**
     * WebSocket健康检查间隔（毫秒）
     */
    @Value("${market.websocket.health-check-interval:60000}")
    private long healthCheckInterval;
    
    /**
     * WebSocket连接最大失败次数
     */
    @Value("${market.websocket.max-fail-count:3}")
    private int maxFailCount;
    
    /**
     * WebSocket连接池初始容量
     */
    @Value("${market.websocket.pool-initial-capacity:16}")
    private int poolInitialCapacity;
    
    /**
     * WebSocket连接最大并发数
     */
    @Value("${market.websocket.max-concurrent-connections:100}")
    private int maxConcurrentConnections;

    /**
     * 获取WebSocket自动重连间隔
     */
    public long getReconnectInterval() {
        return reconnectInterval;
    }

    /**
     * 获取WebSocket连接超时时间
     */
    public int getConnectTimeout() {
        return connectTimeout;
    }

    /**
     * 获取WebSocket读取超时时间
     */
    public int getReadTimeout() {
        return readTimeout;
    }

    /**
     * 获取WebSocket心跳间隔
     */
    public long getPingInterval() {
        return pingInterval;
    }
    
    /**
     * 获取WebSocket健康检查间隔
     * 
     * @return 健康检查间隔（毫秒）
     */
    public long getHealthCheckInterval() {
        return healthCheckInterval;
    }
    
    /**
     * 获取WebSocket连接最大失败次数
     * 
     * @return 最大失败次数
     */
    public int getMaxFailCount() {
        return maxFailCount;
    }
    
    /**
     * 获取WebSocket连接池初始容量
     * 
     * @return 连接池初始容量
     */
    public int getPoolInitialCapacity() {
        return poolInitialCapacity;
    }
    
    /**
     * 获取WebSocket连接最大并发数
     * 
     * @return 最大并发连接数
     */
    public int getMaxConcurrentConnections() {
        return maxConcurrentConnections;
    }
} 