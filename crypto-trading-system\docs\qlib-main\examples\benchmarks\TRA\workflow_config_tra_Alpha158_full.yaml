qlib_init:
  provider_uri: "~/.qlib/qlib_data/cn_data"
  region: cn

market: &market csi300
benchmark: &benchmark SH000300

data_handler_config: &data_handler_config
  start_time: 2008-01-01
  end_time: 2020-08-01
  fit_start_time: 2008-01-01
  fit_end_time: 2014-12-31
  instruments: *market
  infer_processors:
    - class: RobustZScoreNorm
      kwargs:
        fields_group: feature
        clip_outlier: true
    - class: Fillna
      kwargs:
        fields_group: feature
  learn_processors:
    - class: CSRankNorm
      kwargs:
        fields_group: label
  label: ["Ref($close, -2) / Ref($close, -1) - 1"]

num_states: &num_states 3

memory_mode: &memory_mode sample

tra_config: &tra_config
  num_states: *num_states
  rnn_arch: LSTM
  hidden_size: 32
  num_layers: 1
  dropout: 0.0
  tau: 1.0
  src_info: LR_TPE

model_config: &model_config
  input_size: 158
  hidden_size: 256
  num_layers: 2
  rnn_arch: LSTM
  use_attn: True
  dropout: 0.2

port_analysis_config: &port_analysis_config
    strategy:
        class: TopkDropoutStrategy
        module_path: qlib.contrib.strategy
        kwargs:
            signal: <PRED>
            topk: 50
            n_drop: 5
    backtest:
        start_time: 2017-01-01
        end_time: 2020-08-01
        account: *********
        benchmark: *benchmark
        exchange_kwargs:
            limit_threshold: 0.095
            deal_price: close
            open_cost: 0.0005
            close_cost: 0.0015
            min_cost: 5

task:
  model:
    class: TRAModel
    module_path: qlib.contrib.model.pytorch_tra
    kwargs:
      tra_config: *tra_config
      model_config: *model_config
      model_type: RNN
      lr: 1e-3
      n_epochs: 100
      max_steps_per_epoch:
      early_stop: 20
      logdir: output/Alpha158_full
      seed: 0
      lamb: 1.0
      rho: 0.99
      alpha: 0.5
      transport_method: router
      memory_mode: *memory_mode
      eval_train: False
      eval_test: True
      pretrain: True
      init_state:
      freeze_model: False
      freeze_predictors: False
  dataset:
    class: MTSDatasetH
    module_path: qlib.contrib.data.dataset
    kwargs:
      handler:
        class: Alpha158
        module_path: qlib.contrib.data.handler
        kwargs: *data_handler_config
      segments:
        train: [2008-01-01, 2014-12-31]
        valid: [2015-01-01, 2016-12-31]
        test: [2017-01-01, 2020-08-01]
      seq_len: 60
      horizon: 2
      input_size:
      num_states: *num_states
      batch_size: 1024
      n_samples:
      memory_mode: *memory_mode
      drop_last: True
  record:
    - class: SignalRecord
      module_path: qlib.workflow.record_temp
      kwargs: 
        model: <MODEL>
        dataset: <DATASET>
    - class: SigAnaRecord
      module_path: qlib.workflow.record_temp
      kwargs: 
        ana_long_short: False
        ann_scaler: 252
    - class: PortAnaRecord
      module_path: qlib.workflow.record_temp
      kwargs:
        config: *port_analysis_config
