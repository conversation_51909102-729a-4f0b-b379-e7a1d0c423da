package com.crypto.trading.common.dto.market;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 行情数据传输对象
 * <p>
 * 用于在系统各层之间传递24小时行情数据
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class TickerDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 交易对（例如：BTCUSDT）
     */
    private String symbol;

    /**
     * 价格变动
     */
    private BigDecimal priceChange;

    /**
     * 价格变动百分比
     */
    private BigDecimal priceChangePercent;

    /**
     * 加权平均价
     */
    private BigDecimal weightedAvgPrice;

    /**
     * 上一次成交价
     */
    private BigDecimal prevClosePrice;

    /**
     * 最新价
     */
    private BigDecimal lastPrice;

    /**
     * 最新成交量
     */
    private BigDecimal lastQty;

    /**
     * 买一价
     */
    private BigDecimal bidPrice;

    /**
     * 买一量
     */
    private BigDecimal bidQty;

    /**
     * 卖一价
     */
    private BigDecimal askPrice;

    /**
     * 卖一量
     */
    private BigDecimal askQty;

    /**
     * 开盘价
     */
    private BigDecimal openPrice;

    /**
     * 最高价
     */
    private BigDecimal highPrice;

    /**
     * 最低价
     */
    private BigDecimal lowPrice;

    /**
     * 成交量
     */
    private BigDecimal volume;

    /**
     * 成交额
     */
    private BigDecimal quoteVolume;

    /**
     * 统计开始时间
     */
    private LocalDateTime openTime;

    /**
     * 统计结束时间
     */
    private LocalDateTime closeTime;

    /**
     * 第一笔成交ID
     */
    private Long firstId;

    /**
     * 最后一笔成交ID
     */
    private Long lastId;

    /**
     * 成交笔数
     */
    private Long count;

    /**
     * 构造函数
     */
    public TickerDTO() {
    }

    /**
     * 构造函数
     *
     * @param symbol            交易对
     * @param priceChange       价格变动
     * @param priceChangePercent 价格变动百分比
     * @param weightedAvgPrice  加权平均价
     * @param prevClosePrice    上一次成交价
     * @param lastPrice         最新价
     * @param lastQty           最新成交量
     * @param bidPrice          买一价
     * @param bidQty            买一量
     * @param askPrice          卖一价
     * @param askQty            卖一量
     * @param openPrice         开盘价
     * @param highPrice         最高价
     * @param lowPrice          最低价
     * @param volume            成交量
     * @param quoteVolume       成交额
     * @param openTime          统计开始时间
     * @param closeTime         统计结束时间
     * @param firstId           第一笔成交ID
     * @param lastId            最后一笔成交ID
     * @param count             成交笔数
     */
    public TickerDTO(String symbol, BigDecimal priceChange, BigDecimal priceChangePercent,
                   BigDecimal weightedAvgPrice, BigDecimal prevClosePrice, BigDecimal lastPrice,
                   BigDecimal lastQty, BigDecimal bidPrice, BigDecimal bidQty,
                   BigDecimal askPrice, BigDecimal askQty, BigDecimal openPrice,
                   BigDecimal highPrice, BigDecimal lowPrice, BigDecimal volume,
                   BigDecimal quoteVolume, LocalDateTime openTime, LocalDateTime closeTime,
                   Long firstId, Long lastId, Long count) {
        this.symbol = symbol;
        this.priceChange = priceChange;
        this.priceChangePercent = priceChangePercent;
        this.weightedAvgPrice = weightedAvgPrice;
        this.prevClosePrice = prevClosePrice;
        this.lastPrice = lastPrice;
        this.lastQty = lastQty;
        this.bidPrice = bidPrice;
        this.bidQty = bidQty;
        this.askPrice = askPrice;
        this.askQty = askQty;
        this.openPrice = openPrice;
        this.highPrice = highPrice;
        this.lowPrice = lowPrice;
        this.volume = volume;
        this.quoteVolume = quoteVolume;
        this.openTime = openTime;
        this.closeTime = closeTime;
        this.firstId = firstId;
        this.lastId = lastId;
        this.count = count;
    }

    /**
     * 获取交易对
     *
     * @return 交易对
     */
    public String getSymbol() {
        return symbol;
    }

    /**
     * 设置交易对
     *
     * @param symbol 交易对
     */
    public void setSymbol(String symbol) {
        this.symbol = symbol;
    }

    /**
     * 获取价格变动
     *
     * @return 价格变动
     */
    public BigDecimal getPriceChange() {
        return priceChange;
    }

    /**
     * 设置价格变动
     *
     * @param priceChange 价格变动
     */
    public void setPriceChange(BigDecimal priceChange) {
        this.priceChange = priceChange;
    }

    /**
     * 获取价格变动百分比
     *
     * @return 价格变动百分比
     */
    public BigDecimal getPriceChangePercent() {
        return priceChangePercent;
    }

    /**
     * 设置价格变动百分比
     *
     * @param priceChangePercent 价格变动百分比
     */
    public void setPriceChangePercent(BigDecimal priceChangePercent) {
        this.priceChangePercent = priceChangePercent;
    }

    /**
     * 获取加权平均价
     *
     * @return 加权平均价
     */
    public BigDecimal getWeightedAvgPrice() {
        return weightedAvgPrice;
    }

    /**
     * 设置加权平均价
     *
     * @param weightedAvgPrice 加权平均价
     */
    public void setWeightedAvgPrice(BigDecimal weightedAvgPrice) {
        this.weightedAvgPrice = weightedAvgPrice;
    }

    /**
     * 获取上一次成交价
     *
     * @return 上一次成交价
     */
    public BigDecimal getPrevClosePrice() {
        return prevClosePrice;
    }

    /**
     * 设置上一次成交价
     *
     * @param prevClosePrice 上一次成交价
     */
    public void setPrevClosePrice(BigDecimal prevClosePrice) {
        this.prevClosePrice = prevClosePrice;
    }

    /**
     * 获取最新价
     *
     * @return 最新价
     */
    public BigDecimal getLastPrice() {
        return lastPrice;
    }

    /**
     * 设置最新价
     *
     * @param lastPrice 最新价
     */
    public void setLastPrice(BigDecimal lastPrice) {
        this.lastPrice = lastPrice;
    }

    /**
     * 获取最新成交量
     *
     * @return 最新成交量
     */
    public BigDecimal getLastQty() {
        return lastQty;
    }

    /**
     * 设置最新成交量
     *
     * @param lastQty 最新成交量
     */
    public void setLastQty(BigDecimal lastQty) {
        this.lastQty = lastQty;
    }

    /**
     * 获取买一价
     *
     * @return 买一价
     */
    public BigDecimal getBidPrice() {
        return bidPrice;
    }

    /**
     * 设置买一价
     *
     * @param bidPrice 买一价
     */
    public void setBidPrice(BigDecimal bidPrice) {
        this.bidPrice = bidPrice;
    }

    /**
     * 获取买一量
     *
     * @return 买一量
     */
    public BigDecimal getBidQty() {
        return bidQty;
    }

    /**
     * 设置买一量
     *
     * @param bidQty 买一量
     */
    public void setBidQty(BigDecimal bidQty) {
        this.bidQty = bidQty;
    }

    /**
     * 获取卖一价
     *
     * @return 卖一价
     */
    public BigDecimal getAskPrice() {
        return askPrice;
    }

    /**
     * 设置卖一价
     *
     * @param askPrice 卖一价
     */
    public void setAskPrice(BigDecimal askPrice) {
        this.askPrice = askPrice;
    }

    /**
     * 获取卖一量
     *
     * @return 卖一量
     */
    public BigDecimal getAskQty() {
        return askQty;
    }

    /**
     * 设置卖一量
     *
     * @param askQty 卖一量
     */
    public void setAskQty(BigDecimal askQty) {
        this.askQty = askQty;
    }

    /**
     * 获取开盘价
     *
     * @return 开盘价
     */
    public BigDecimal getOpenPrice() {
        return openPrice;
    }

    /**
     * 设置开盘价
     *
     * @param openPrice 开盘价
     */
    public void setOpenPrice(BigDecimal openPrice) {
        this.openPrice = openPrice;
    }

    /**
     * 获取最高价
     *
     * @return 最高价
     */
    public BigDecimal getHighPrice() {
        return highPrice;
    }

    /**
     * 设置最高价
     *
     * @param highPrice 最高价
     */
    public void setHighPrice(BigDecimal highPrice) {
        this.highPrice = highPrice;
    }

    /**
     * 获取最低价
     *
     * @return 最低价
     */
    public BigDecimal getLowPrice() {
        return lowPrice;
    }

    /**
     * 设置最低价
     *
     * @param lowPrice 最低价
     */
    public void setLowPrice(BigDecimal lowPrice) {
        this.lowPrice = lowPrice;
    }

    /**
     * 获取成交量
     *
     * @return 成交量
     */
    public BigDecimal getVolume() {
        return volume;
    }

    /**
     * 设置成交量
     *
     * @param volume 成交量
     */
    public void setVolume(BigDecimal volume) {
        this.volume = volume;
    }

    /**
     * 获取成交额
     *
     * @return 成交额
     */
    public BigDecimal getQuoteVolume() {
        return quoteVolume;
    }

    /**
     * 设置成交额
     *
     * @param quoteVolume 成交额
     */
    public void setQuoteVolume(BigDecimal quoteVolume) {
        this.quoteVolume = quoteVolume;
    }

    /**
     * 获取统计开始时间
     *
     * @return 统计开始时间
     */
    public LocalDateTime getOpenTime() {
        return openTime;
    }

    /**
     * 设置统计开始时间
     *
     * @param openTime 统计开始时间
     */
    public void setOpenTime(LocalDateTime openTime) {
        this.openTime = openTime;
    }

    /**
     * 获取统计结束时间
     *
     * @return 统计结束时间
     */
    public LocalDateTime getCloseTime() {
        return closeTime;
    }

    /**
     * 设置统计结束时间
     *
     * @param closeTime 统计结束时间
     */
    public void setCloseTime(LocalDateTime closeTime) {
        this.closeTime = closeTime;
    }

    /**
     * 获取第一笔成交ID
     *
     * @return 第一笔成交ID
     */
    public Long getFirstId() {
        return firstId;
    }

    /**
     * 设置第一笔成交ID
     *
     * @param firstId 第一笔成交ID
     */
    public void setFirstId(Long firstId) {
        this.firstId = firstId;
    }

    /**
     * 获取最后一笔成交ID
     *
     * @return 最后一笔成交ID
     */
    public Long getLastId() {
        return lastId;
    }

    /**
     * 设置最后一笔成交ID
     *
     * @param lastId 最后一笔成交ID
     */
    public void setLastId(Long lastId) {
        this.lastId = lastId;
    }

    /**
     * 获取成交笔数
     *
     * @return 成交笔数
     */
    public Long getCount() {
        return count;
    }

    /**
     * 设置成交笔数
     *
     * @param count 成交笔数
     */
    public void setCount(Long count) {
        this.count = count;
    }

    @Override
    public String toString() {
        return "TickerDTO{" +
                "symbol='" + symbol + '\'' +
                ", priceChange=" + priceChange +
                ", priceChangePercent=" + priceChangePercent +
                ", weightedAvgPrice=" + weightedAvgPrice +
                ", prevClosePrice=" + prevClosePrice +
                ", lastPrice=" + lastPrice +
                ", lastQty=" + lastQty +
                ", bidPrice=" + bidPrice +
                ", bidQty=" + bidQty +
                ", askPrice=" + askPrice +
                ", askQty=" + askQty +
                ", openPrice=" + openPrice +
                ", highPrice=" + highPrice +
                ", lowPrice=" + lowPrice +
                ", volume=" + volume +
                ", quoteVolume=" + quoteVolume +
                ", openTime=" + openTime +
                ", closeTime=" + closeTime +
                ", firstId=" + firstId +
                ", lastId=" + lastId +
                ", count=" + count +
                '}';
    }
} 