package com.crypto.trading.market.config;

import com.crypto.trading.market.service.impl.DownloadTaskTracker;
import com.crypto.trading.market.service.impl.optimizer.SmartChunkDownloader;
import com.crypto.trading.market.service.impl.resumable.ResumableDownloadEngine;
import org.springframework.context.annotation.Configuration;

/**
 * 断点续传下载配置类
 */
@Configuration
public class ResumableDownloadConfig {
    // ResumableDownloadEngine已经通过@Component注解自动注册为Bean
    // 所以我们不需要手动创建它
}