#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
快速导入测试

测试修复后的导入路径是否正常工作
"""

import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试关键导入"""
    results = {}
    
    # 测试1: CacheIntegration导入
    try:
        from data.cache import CacheIntegration
        results['CacheIntegration'] = True
        print("✅ CacheIntegration导入成功")
    except Exception as e:
        results['CacheIntegration'] = False
        print(f"❌ CacheIntegration导入失败: {e}")
    
    # 测试2: DataValidator导入
    try:
        from data.quality.data_validator import DataValidator
        results['DataValidator'] = True
        print("✅ DataValidator导入成功")
    except Exception as e:
        results['DataValidator'] = False
        print(f"❌ DataValidator导入失败: {e}")
    
    # 测试3: DataQualityCore导入
    try:
        from data.quality import DataQualityCore
        results['DataQualityCore'] = True
        print("✅ DataQualityCore导入成功")
    except Exception as e:
        results['DataQualityCore'] = False
        print(f"❌ DataQualityCore导入失败: {e}")
    
    # 测试4: 数据库客户端导入
    try:
        from data.clients.influxdb_client import InfluxDBClient
        from data.clients.mysql_client import MySQLClient
        results['DatabaseClients'] = True
        print("✅ 数据库客户端导入成功")
    except Exception as e:
        results['DatabaseClients'] = False
        print(f"❌ 数据库客户端导入失败: {e}")
    
    # 测试5: DataProcessor导入
    try:
        from data.data_processor import DataProcessor
        results['DataProcessor'] = True
        print("✅ DataProcessor导入成功")
    except Exception as e:
        results['DataProcessor'] = False
        print(f"❌ DataProcessor导入失败: {e}")
    
    return results

def main():
    """主函数"""
    print("开始快速导入测试...")
    print("=" * 50)
    
    results = test_imports()
    
    print("\n" + "=" * 50)
    print("测试结果汇总:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有导入测试通过！")
        return True
    else:
        print(f"⚠️ {total - passed} 个导入需要进一步修复")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)