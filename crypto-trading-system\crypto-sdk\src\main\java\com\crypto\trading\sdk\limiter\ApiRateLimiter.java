package com.crypto.trading.sdk.limiter;

/**
 * @name: ApiRateLimiter
 * @author: zeek
 * @description: API请求限流器的通用接口。
 *               定义了获取和尝试获取许可的方法，实现类可以通过不同算法（如令牌桶、漏桶）来控制请求速率。
 * @create: 2024-07-16 05:40
 */
public interface ApiRateLimiter {

    /**
     * 尝试获取一个许可。
     * 此方法会立即返回，不会阻塞。
     *
     * @return 如果成功获取许可，则返回 true；否则返回 false。
     */
    boolean tryAcquire();

    /**
     * 尝试获取指定数量的许可。
     * 此方法会立即返回，不会阻塞。
     *
     * @param permits 要获取的许可数量。
     * @return 如果成功获取许可，则返回 true；否则返回 false。
     */
    boolean tryAcquire(int permits);

    /**
     * 获取一个许可。
     * 如果当前没有可用的许可，此方法会阻塞，直到获取到许可为止。
     */
    void acquire();

    /**
     * 获取指定数量的许可。
     * 如果当前没有足够的可用许可，此方法会阻塞，直到获取到所有请求的许可为止。
     *
     * @param permits 要获取的许可数量。
     */
    void acquire(int permits);
}
