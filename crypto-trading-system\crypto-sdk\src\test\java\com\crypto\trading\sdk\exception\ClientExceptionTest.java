package com.crypto.trading.sdk.exception;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 客户端异常测试类
 */
public class ClientExceptionTest {

    @Test
    public void testConstructorWithCodeAndMessage() {
        // 测试带代码和消息的构造函数
        int code = 4001;
        String message = "客户端测试异常消息";
        
        ClientException exception = new ClientException(code, message);
        
        // 验证属性
        assertEquals(code, exception.getCode());
        assertEquals(message, exception.getMessage());
        assertEquals(message, exception.getErrorMessage());
        assertNull(exception.getCause());
    }

    @Test
    public void testConstructorWithCodeMessageAndCause() {
        // 测试带代码、消息和原因的构造函数
        int code = 4002;
        String message = "客户端测试异常消息与原因";
        Throwable cause = new RuntimeException("原始异常");
        
        ClientException exception = new ClientException(code, message, cause);
        
        // 验证属性
        assertEquals(code, exception.getCode());
        assertEquals(message, exception.getMessage());
        assertEquals(message, exception.getErrorMessage());
        assertSame(cause, exception.getCause());
    }

    @Test
    public void testInheritance() {
        // 测试继承关系
        ClientException exception = new ClientException(4003, "继承测试");
        
        assertTrue(exception instanceof ApiException);
    }

    @Test
    public void testErrorCode() {
        // 测试错误代码是否在正确的范围内
        ClientException exception = new ClientException(4004, "错误代码测试");
        
        int code = exception.getCode();
        assertTrue(code >= 4000 && code < 5000, "客户端异常代码应该在4000-4999范围内");
    }
} 