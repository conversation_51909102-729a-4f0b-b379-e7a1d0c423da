package com.crypto.trading.bootstrap.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * 分片下载状态响应
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChunkStatusResponse {
    /**
     * 任务ID
     */
    private String taskId;
    
    /**
     * 任务状态
     * PENDING: 待处理
     * IN_PROGRESS: 处理中
     * COMPLETED: 已完成
     * FAILED: 失败
     */
    private String status;
    
    /**
     * 总分片数
     */
    private int totalChunks;
    
    /**
     * 已完成分片数
     */
    private int completedChunks;
    
    /**
     * 失败分片数
     */
    private int failedChunks;
    
    /**
     * 分片状态详情映射
     * key: 分片ID
     * value: 分片状态
     */
    private Map<String, ChunkStatus> chunkDetails;
    
    /**
     * 任务创建时间
     */
    private String createdAt;
    
    /**
     * 任务更新时间
     */
    private String updatedAt;
    
    /**
     * 错误消息（如果有）
     */
    private String errorMessage;
    
    /**
     * 分片状态枚举
     */
    public enum ChunkStatus {
        PENDING,
        IN_PROGRESS,
        COMPLETED,
        FAILED
    }
} 