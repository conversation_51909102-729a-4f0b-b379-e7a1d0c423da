#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据质量验证核心框架

为crypto_ml_strategy项目第3阶段任务2实现的数据质量验证核心组件。
提供统一的质量检查接口、评分系统和配置管理。

主要功能：
1. 质量检查基础框架
2. 质量评分算法
3. 配置管理系统
4. 性能监控
5. 集成接口
"""

import logging
from typing import Dict, List, Tuple, Optional, Any, Union, Protocol
from datetime import datetime, timedelta
import time
import threading
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from enum import Enum
import numpy as np
import pandas as pd


class QualityLevel(Enum):
    """质量等级枚举"""
    EXCELLENT = "excellent"  # 0.9-1.0
    GOOD = "good"           # 0.7-0.9
    FAIR = "fair"           # 0.5-0.7
    POOR = "poor"           # 0.3-0.5
    CRITICAL = "critical"   # 0.0-0.3


class ValidationResult(Enum):
    """验证结果枚举"""
    PASS = "pass"
    WARNING = "warning"
    FAIL = "fail"
    ERROR = "error"


@dataclass
class QualityIssue:
    """质量问题描述"""
    issue_type: str
    severity: ValidationResult
    description: str
    affected_columns: List[str]
    affected_rows: List[int]
    suggested_fix: Optional[str] = None
    timestamp: datetime = field(default_factory=datetime.now)


@dataclass
class QualityMetrics:
    """质量指标"""
    overall_score: float  # 0.0-1.0
    completeness_score: float  # 缺失值相关
    consistency_score: float   # 一致性相关
    accuracy_score: float      # 准确性相关
    timeliness_score: float    # 时效性相关
    validity_score: float      # 有效性相关
    
    total_records: int
    valid_records: int
    missing_values: int
    outliers_detected: int
    
    quality_level: QualityLevel
    issues: List[QualityIssue] = field(default_factory=list)
    check_duration_ms: float = 0.0
    timestamp: datetime = field(default_factory=datetime.now)


@dataclass
class QualityConfig:
    """质量检查配置"""
    # 缺失值配置
    max_missing_ratio: float = 0.1  # 最大缺失值比例
    missing_value_indicators: List[Any] = field(default_factory=lambda: [None, np.nan, '', 'NULL', 'null'])
    
    # 异常值配置
    outlier_detection_method: str = 'iqr'  # 'iqr', 'zscore', 'isolation_forest'
    outlier_threshold: float = 3.0  # Z-score阈值或IQR倍数
    max_outlier_ratio: float = 0.05  # 最大异常值比例
    
    # 数据范围配置
    price_min: float = 0.0001  # 最小价格
    price_max: float = 1000000.0  # 最大价格
    volume_min: float = 0.0  # 最小成交量
    volume_max: float = 1e12  # 最大成交量
    
    # 时间相关配置
    max_time_gap_seconds: int = 300  # 最大时间间隙（秒）
    future_data_tolerance_seconds: int = 60  # 未来数据容忍度
    
    # 性能配置
    max_check_duration_ms: float = 50.0  # 最大检查时间
    enable_parallel_checks: bool = True
    max_workers: int = 4
    
    # 修复配置
    enable_auto_repair: bool = True
    repair_missing_method: str = 'interpolate'  # 'ffill', 'bfill', 'interpolate', 'mean'
    repair_outlier_method: str = 'clip'  # 'clip', 'remove', 'interpolate'


class QualityValidator(Protocol):
    """质量验证器协议"""
    
    def validate(self, data: pd.DataFrame, config: QualityConfig) -> List[QualityIssue]:
        """执行质量验证"""
        ...
    
    def get_validator_name(self) -> str:
        """获取验证器名称"""
        ...


class QualityRepairer(Protocol):
    """质量修复器协议"""
    
    def repair(self, data: pd.DataFrame, issues: List[QualityIssue], 
              config: QualityConfig) -> Tuple[pd.DataFrame, List[str]]:
        """执行质量修复"""
        ...
    
    def get_repairer_name(self) -> str:
        """获取修复器名称"""
        ...


class DataQualityChecker:
    """数据质量检查器核心类"""
    
    def __init__(self, config: Optional[QualityConfig] = None):
        """
        初始化数据质量检查器
        
        Args:
            config: 质量检查配置
        """
        self.logger = logging.getLogger('cryptoMlStrategy.data.quality_core')
        self.config = config or QualityConfig()
        
        # 验证器和修复器注册表
        self._validators: Dict[str, QualityValidator] = {}
        self._repairers: Dict[str, QualityRepairer] = {}
        
        # 性能统计
        self._stats = {
            'total_checks': 0,
            'successful_checks': 0,
            'failed_checks': 0,
            'total_repairs': 0,
            'successful_repairs': 0,
            'average_check_time_ms': 0.0,
            'start_time': time.time()
        }
        
        # 线程锁
        self._lock = threading.RLock()
        
        self.logger.info("数据质量检查器初始化完成")
    
    def register_validator(self, validator: QualityValidator) -> bool:
        """
        注册质量验证器
        
        Args:
            validator: 验证器实例
            
        Returns:
            是否注册成功
        """
        try:
            name = validator.get_validator_name()
            self._validators[name] = validator
            self.logger.info(f"验证器注册成功: {name}")
            return True
        except Exception as e:
            self.logger.error(f"验证器注册失败: {e}")
            return False
    
    def register_repairer(self, repairer: QualityRepairer) -> bool:
        """
        注册质量修复器
        
        Args:
            repairer: 修复器实例
            
        Returns:
            是否注册成功
        """
        try:
            name = repairer.get_repairer_name()
            self._repairers[name] = repairer
            self.logger.info(f"修复器注册成功: {name}")
            return True
        except Exception as e:
            self.logger.error(f"修复器注册失败: {e}")
            return False
    
    def check_data_quality(self, data: pd.DataFrame, symbol: str = "", 
                          timeframe: str = "") -> QualityMetrics:
        """
        检查数据质量
        
        Args:
            data: 要检查的数据
            symbol: 交易对符号
            timeframe: 时间框架
            
        Returns:
            质量指标
        """
        start_time = time.time()
        
        try:
            with self._lock:
                self._stats['total_checks'] += 1
            
            if data is None or data.empty:
                return self._create_empty_metrics("数据为空")
            
            # 执行所有验证器
            all_issues = []
            for validator_name, validator in self._validators.items():
                try:
                    issues = validator.validate(data, self.config)
                    all_issues.extend(issues)
                except Exception as e:
                    self.logger.error(f"验证器 {validator_name} 执行失败: {e}")
                    all_issues.append(QualityIssue(
                        issue_type="validator_error",
                        severity=ValidationResult.ERROR,
                        description=f"验证器 {validator_name} 执行失败: {str(e)}",
                        affected_columns=[],
                        affected_rows=[]
                    ))
            
            # 计算质量指标
            metrics = self._calculate_quality_metrics(data, all_issues)
            
            # 更新性能统计
            check_duration = (time.time() - start_time) * 1000
            metrics.check_duration_ms = check_duration
            
            with self._lock:
                self._stats['successful_checks'] += 1
                self._update_average_time(check_duration)
            
            # 检查性能目标
            if check_duration > self.config.max_check_duration_ms:
                self.logger.warning(f"质量检查超时: {check_duration:.1f}ms > {self.config.max_check_duration_ms}ms")
            
            self.logger.debug(f"质量检查完成: {symbol} {timeframe}, 分数: {metrics.overall_score:.3f}")
            return metrics
            
        except Exception as e:
            with self._lock:
                self._stats['failed_checks'] += 1
            
            self.logger.error(f"数据质量检查失败: {e}")
            return self._create_error_metrics(str(e))
    
    def repair_data_quality(self, data: pd.DataFrame, metrics: QualityMetrics) -> Tuple[pd.DataFrame, List[str]]:
        """
        修复数据质量问题
        
        Args:
            data: 原始数据
            metrics: 质量指标（包含问题列表）
            
        Returns:
            修复后的数据和修复日志
        """
        try:
            if not self.config.enable_auto_repair:
                return data, ["自动修复已禁用"]
            
            with self._lock:
                self._stats['total_repairs'] += 1
            
            repaired_data = data.copy()
            repair_logs = []
            
            # 按严重程度排序问题
            sorted_issues = sorted(metrics.issues, 
                                 key=lambda x: self._get_severity_priority(x.severity))
            
            # 执行修复
            for repairer_name, repairer in self._repairers.items():
                try:
                    relevant_issues = [issue for issue in sorted_issues 
                                     if self._is_repairer_applicable(repairer_name, issue)]
                    
                    if relevant_issues:
                        repaired_data, logs = repairer.repair(repaired_data, relevant_issues, self.config)
                        repair_logs.extend(logs)
                        
                except Exception as e:
                    error_msg = f"修复器 {repairer_name} 执行失败: {str(e)}"
                    self.logger.error(error_msg)
                    repair_logs.append(error_msg)
            
            with self._lock:
                self._stats['successful_repairs'] += 1
            
            self.logger.info(f"数据质量修复完成，执行了 {len(repair_logs)} 个修复操作")
            return repaired_data, repair_logs
            
        except Exception as e:
            self.logger.error(f"数据质量修复失败: {e}")
            return data, [f"修复失败: {str(e)}"]
    
    def _calculate_quality_metrics(self, data: pd.DataFrame, issues: List[QualityIssue]) -> QualityMetrics:
        """计算质量指标"""
        try:
            total_records = len(data)
            total_cells = total_records * len(data.columns)
            
            # 统计问题
            missing_values = data.isnull().sum().sum()
            outliers_detected = len([issue for issue in issues if issue.issue_type == 'outlier'])
            
            # 计算各项分数
            completeness_score = 1.0 - (missing_values / max(1, total_cells))
            
            # 一致性分数（基于异常值比例）
            consistency_score = 1.0 - (outliers_detected / max(1, total_records))
            
            # 准确性分数（基于错误问题数量）
            error_issues = len([issue for issue in issues if issue.severity == ValidationResult.FAIL])
            accuracy_score = 1.0 - (error_issues / max(1, len(issues) + 1))
            
            # 时效性分数（基于时间相关问题）
            time_issues = len([issue for issue in issues if 'time' in issue.issue_type.lower()])
            timeliness_score = 1.0 - (time_issues / max(1, len(issues) + 1))
            
            # 有效性分数（基于数据范围问题）
            validity_issues = len([issue for issue in issues if 'range' in issue.issue_type.lower()])
            validity_score = 1.0 - (validity_issues / max(1, len(issues) + 1))
            
            # 综合分数（加权平均）
            overall_score = (
                completeness_score * 0.3 +
                consistency_score * 0.25 +
                accuracy_score * 0.2 +
                timeliness_score * 0.15 +
                validity_score * 0.1
            )
            
            # 确定质量等级
            quality_level = self._determine_quality_level(overall_score)
            
            return QualityMetrics(
                overall_score=overall_score,
                completeness_score=completeness_score,
                consistency_score=consistency_score,
                accuracy_score=accuracy_score,
                timeliness_score=timeliness_score,
                validity_score=validity_score,
                total_records=total_records,
                valid_records=total_records - len([issue for issue in issues if issue.severity == ValidationResult.FAIL]),
                missing_values=missing_values,
                outliers_detected=outliers_detected,
                quality_level=quality_level,
                issues=issues
            )
            
        except Exception as e:
            self.logger.error(f"质量指标计算失败: {e}")
            return self._create_error_metrics(str(e))
    
    def _determine_quality_level(self, score: float) -> QualityLevel:
        """确定质量等级"""
        if score >= 0.9:
            return QualityLevel.EXCELLENT
        elif score >= 0.7:
            return QualityLevel.GOOD
        elif score >= 0.5:
            return QualityLevel.FAIR
        elif score >= 0.3:
            return QualityLevel.POOR
        else:
            return QualityLevel.CRITICAL
    
    def _get_severity_priority(self, severity: ValidationResult) -> int:
        """获取严重程度优先级"""
        priority_map = {
            ValidationResult.ERROR: 0,
            ValidationResult.FAIL: 1,
            ValidationResult.WARNING: 2,
            ValidationResult.PASS: 3
        }
        return priority_map.get(severity, 999)
    
    def _is_repairer_applicable(self, repairer_name: str, issue: QualityIssue) -> bool:
        """判断修复器是否适用于特定问题"""
        # 简单的名称匹配逻辑，可以根据需要扩展
        return any(keyword in repairer_name.lower() for keyword in issue.issue_type.lower().split('_'))
    
    def _create_empty_metrics(self, reason: str) -> QualityMetrics:
        """创建空数据的质量指标"""
        return QualityMetrics(
            overall_score=0.0,
            completeness_score=0.0,
            consistency_score=0.0,
            accuracy_score=0.0,
            timeliness_score=0.0,
            validity_score=0.0,
            total_records=0,
            valid_records=0,
            missing_values=0,
            outliers_detected=0,
            quality_level=QualityLevel.CRITICAL,
            issues=[QualityIssue(
                issue_type="empty_data",
                severity=ValidationResult.ERROR,
                description=reason,
                affected_columns=[],
                affected_rows=[]
            )]
        )
    
    def _create_error_metrics(self, error_msg: str) -> QualityMetrics:
        """创建错误情况的质量指标"""
        return QualityMetrics(
            overall_score=0.0,
            completeness_score=0.0,
            consistency_score=0.0,
            accuracy_score=0.0,
            timeliness_score=0.0,
            validity_score=0.0,
            total_records=0,
            valid_records=0,
            missing_values=0,
            outliers_detected=0,
            quality_level=QualityLevel.CRITICAL,
            issues=[QualityIssue(
                issue_type="check_error",
                severity=ValidationResult.ERROR,
                description=error_msg,
                affected_columns=[],
                affected_rows=[]
            )]
        )
    
    def _update_average_time(self, duration_ms: float):
        """更新平均检查时间"""
        current_avg = self._stats['average_check_time_ms']
        total_checks = self._stats['successful_checks']
        
        if total_checks <= 1:
            self._stats['average_check_time_ms'] = duration_ms
        else:
            self._stats['average_check_time_ms'] = (
                (current_avg * (total_checks - 1) + duration_ms) / total_checks
            )
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        with self._lock:
            elapsed_time = time.time() - self._stats['start_time']
            throughput = self._stats['successful_checks'] / max(1, elapsed_time)
            
            return {
                'statistics': self._stats.copy(),
                'performance': {
                    'average_check_time_ms': self._stats['average_check_time_ms'],
                    'throughput_checks_per_sec': throughput,
                    'success_rate': self._stats['successful_checks'] / max(1, self._stats['total_checks'])
                },
                'registered_components': {
                    'validators': list(self._validators.keys()),
                    'repairers': list(self._repairers.keys())
                },
                'config': {
                    'max_missing_ratio': self.config.max_missing_ratio,
                    'outlier_threshold': self.config.outlier_threshold,
                    'max_check_duration_ms': self.config.max_check_duration_ms,
                    'enable_auto_repair': self.config.enable_auto_repair
                }
            }