#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Mock服务和Fixtures模块

提供Java API和Kafka服务的模拟实现，支持测试数据fixtures、环境设置、
测试隔离、状态重置和可控的错误注入机制。
"""

import asyncio
import json
import random
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable, Union
from dataclasses import dataclass, field
from unittest.mock import Mock, MagicMock
from pathlib import Path
import uuid
from loguru import logger


@dataclass
class MockResponse:
    """模拟响应"""
    status_code: int = 200
    data: Any = None
    headers: Dict[str, str] = field(default_factory=dict)
    delay: float = 0.0
    error: Optional[Exception] = None


@dataclass
class ErrorInjectionRule:
    """错误注入规则"""
    endpoint: str
    error_type: str  # 'timeout', 'connection_error', 'server_error', 'invalid_data'
    probability: float = 0.1  # 错误概率
    error_message: str = "Mock error"
    status_code: int = 500
    active: bool = True


class MockJavaAPI:
    """Java API模拟服务"""
    
    def __init__(self, base_url: str = "http://localhost:8080/api"):
        """
        初始化Java API模拟服务
        
        Args:
            base_url: API基础URL
        """
        self.base_url = base_url
        self.responses: Dict[str, MockResponse] = {}
        self.request_history: List[Dict[str, Any]] = []
        self.error_rules: List[ErrorInjectionRule] = []
        self.is_running = False
        self._lock = threading.Lock()
        
        # 设置默认响应
        self._setup_default_responses()
        
        logger.info(f"Java API模拟服务已初始化: {base_url}")
    
    def _setup_default_responses(self) -> None:
        """设置默认响应"""
        # 市场数据端点
        self.responses["/market/data"] = MockResponse(
            status_code=200,
            data={
                "symbol": "BTCUSDT",
                "price": 45000.0,
                "volume": 1000.0,
                "timestamp": int(time.time() * 1000)
            }
        )
        
        # 历史数据端点
        self.responses["/market/history"] = MockResponse(
            status_code=200,
            data=self._generate_mock_historical_data()
        )
        
        # 订单端点
        self.responses["/orders"] = MockResponse(
            status_code=200,
            data={
                "order_id": str(uuid.uuid4()),
                "status": "filled",
                "symbol": "BTCUSDT",
                "side": "buy",
                "quantity": 0.1,
                "price": 45000.0
            }
        )
        
        # 账户信息端点
        self.responses["/account"] = MockResponse(
            status_code=200,
            data={
                "account_id": "test_account",
                "balance": 10000.0,
                "available_balance": 9000.0,
                "positions": []
            }
        )
        
        # 策略配置端点
        self.responses["/strategy/config"] = MockResponse(
            status_code=200,
            data={
                "strategy_id": "ml_strategy",
                "parameters": {
                    "risk_level": 0.02,
                    "max_position_size": 0.1,
                    "stop_loss": 0.05
                }
            }
        )
    
    def _generate_mock_historical_data(self) -> List[Dict[str, Any]]:
        """生成模拟历史数据"""
        data = []
        base_price = 45000.0
        current_time = int(time.time() * 1000)
        
        for i in range(100):
            # 模拟价格波动
            price_change = random.uniform(-0.02, 0.02)
            base_price *= (1 + price_change)
            
            data.append({
                "timestamp": current_time - (i * 60000),  # 每分钟一个数据点
                "open": base_price,
                "high": base_price * 1.01,
                "low": base_price * 0.99,
                "close": base_price,
                "volume": random.uniform(500, 2000)
            })
        
        return list(reversed(data))  # 按时间正序
    
    def set_response(self, endpoint: str, response: MockResponse) -> None:
        """
        设置端点响应
        
        Args:
            endpoint: API端点
            response: 模拟响应
        """
        with self._lock:
            self.responses[endpoint] = response
        logger.debug(f"设置响应: {endpoint}")
    
    def add_error_rule(self, rule: ErrorInjectionRule) -> None:
        """
        添加错误注入规则
        
        Args:
            rule: 错误注入规则
        """
        with self._lock:
            self.error_rules.append(rule)
        logger.debug(f"添加错误注入规则: {rule.endpoint}")
    
    def remove_error_rule(self, endpoint: str) -> None:
        """
        移除错误注入规则
        
        Args:
            endpoint: API端点
        """
        with self._lock:
            self.error_rules = [rule for rule in self.error_rules if rule.endpoint != endpoint]
        logger.debug(f"移除错误注入规则: {endpoint}")
    
    def clear_error_rules(self) -> None:
        """清除所有错误注入规则"""
        with self._lock:
            self.error_rules.clear()
        logger.debug("清除所有错误注入规则")
    
    async def request(self, method: str, endpoint: str, 
                     data: Optional[Dict[str, Any]] = None,
                     headers: Optional[Dict[str, str]] = None) -> MockResponse:
        """
        模拟API请求
        
        Args:
            method: HTTP方法
            endpoint: API端点
            data: 请求数据
            headers: 请求头
        
        Returns:
            MockResponse: 模拟响应
        """
        # 记录请求历史
        request_record = {
            "timestamp": datetime.now().isoformat(),
            "method": method,
            "endpoint": endpoint,
            "data": data,
            "headers": headers
        }
        
        with self._lock:
            self.request_history.append(request_record)
        
        # 检查错误注入规则
        for rule in self.error_rules:
            if rule.active and rule.endpoint == endpoint:
                if random.random() < rule.probability:
                    await self._inject_error(rule)
        
        # 获取响应
        response = self.responses.get(endpoint, MockResponse(
            status_code=404,
            data={"error": "Endpoint not found"}
        ))
        
        # 模拟延迟
        if response.delay > 0:
            await asyncio.sleep(response.delay)
        
        # 抛出错误（如果设置）
        if response.error:
            raise response.error
        
        logger.debug(f"API请求: {method} {endpoint} -> {response.status_code}")
        return response
    
    async def _inject_error(self, rule: ErrorInjectionRule) -> None:
        """注入错误"""
        if rule.error_type == "timeout":
            await asyncio.sleep(30)  # 模拟超时
            raise TimeoutError(rule.error_message)
        
        elif rule.error_type == "connection_error":
            raise ConnectionError(rule.error_message)
        
        elif rule.error_type == "server_error":
            raise Exception(f"Server Error: {rule.error_message}")
        
        elif rule.error_type == "invalid_data":
            # 返回无效数据而不是抛出异常
            pass
    
    def get_request_history(self) -> List[Dict[str, Any]]:
        """获取请求历史"""
        with self._lock:
            return self.request_history.copy()
    
    def clear_request_history(self) -> None:
        """清除请求历史"""
        with self._lock:
            self.request_history.clear()
        logger.debug("清除请求历史")
    
    def reset(self) -> None:
        """重置模拟服务"""
        self.clear_request_history()
        self.clear_error_rules()
        self._setup_default_responses()
        logger.info("Java API模拟服务已重置")


class MockKafkaService:
    """Kafka服务模拟"""
    
    def __init__(self, bootstrap_servers: List[str] = None):
        """
        初始化Kafka模拟服务
        
        Args:
            bootstrap_servers: Kafka服务器列表
        """
        self.bootstrap_servers = bootstrap_servers or ["localhost:9092"]
        self.topics: Dict[str, List[Dict[str, Any]]] = {}
        self.consumers: Dict[str, List[Callable]] = {}
        self.producers: Dict[str, Any] = {}
        self.is_running = False
        self._lock = threading.Lock()
        self.error_rules: List[ErrorInjectionRule] = []
        
        logger.info(f"Kafka模拟服务已初始化: {self.bootstrap_servers}")
    
    def create_topic(self, topic_name: str) -> None:
        """
        创建主题
        
        Args:
            topic_name: 主题名称
        """
        with self._lock:
            if topic_name not in self.topics:
                self.topics[topic_name] = []
                self.consumers[topic_name] = []
        logger.debug(f"创建主题: {topic_name}")
    
    def delete_topic(self, topic_name: str) -> None:
        """
        删除主题
        
        Args:
            topic_name: 主题名称
        """
        with self._lock:
            if topic_name in self.topics:
                del self.topics[topic_name]
            if topic_name in self.consumers:
                del self.consumers[topic_name]
        logger.debug(f"删除主题: {topic_name}")
    
    async def produce(self, topic: str, message: Dict[str, Any], 
                     key: Optional[str] = None) -> None:
        """
        生产消息
        
        Args:
            topic: 主题名称
            message: 消息内容
            key: 消息键
        """
        # 检查错误注入
        for rule in self.error_rules:
            if rule.active and rule.endpoint == f"produce:{topic}":
                if random.random() < rule.probability:
                    raise Exception(f"Kafka produce error: {rule.error_message}")
        
        # 创建主题（如果不存在）
        self.create_topic(topic)
        
        # 添加消息
        message_record = {
            "timestamp": int(time.time() * 1000),
            "key": key,
            "value": message,
            "topic": topic,
            "partition": 0,
            "offset": len(self.topics[topic])
        }
        
        with self._lock:
            self.topics[topic].append(message_record)
        
        # 通知消费者
        await self._notify_consumers(topic, message_record)
        
        logger.debug(f"生产消息到 {topic}: {len(str(message))} 字节")
    
    def subscribe(self, topic: str, callback: Callable[[Dict[str, Any]], None]) -> None:
        """
        订阅主题
        
        Args:
            topic: 主题名称
            callback: 消息回调函数
        """
        self.create_topic(topic)
        
        with self._lock:
            self.consumers[topic].append(callback)
        
        logger.debug(f"订阅主题: {topic}")
    
    def unsubscribe(self, topic: str, callback: Callable[[Dict[str, Any]], None]) -> None:
        """
        取消订阅
        
        Args:
            topic: 主题名称
            callback: 消息回调函数
        """
        with self._lock:
            if topic in self.consumers and callback in self.consumers[topic]:
                self.consumers[topic].remove(callback)
        
        logger.debug(f"取消订阅主题: {topic}")
    
    async def _notify_consumers(self, topic: str, message: Dict[str, Any]) -> None:
        """通知消费者"""
        consumers = self.consumers.get(topic, [])
        
        for consumer in consumers:
            try:
                if asyncio.iscoroutinefunction(consumer):
                    await consumer(message)
                else:
                    consumer(message)
            except Exception as e:
                logger.error(f"消费者回调失败: {str(e)}")
    
    def get_messages(self, topic: str, from_offset: int = 0) -> List[Dict[str, Any]]:
        """
        获取消息
        
        Args:
            topic: 主题名称
            from_offset: 起始偏移量
        
        Returns:
            List[Dict[str, Any]]: 消息列表
        """
        with self._lock:
            messages = self.topics.get(topic, [])
            return messages[from_offset:]
    
    def clear_topic(self, topic: str) -> None:
        """
        清空主题
        
        Args:
            topic: 主题名称
        """
        with self._lock:
            if topic in self.topics:
                self.topics[topic].clear()
        logger.debug(f"清空主题: {topic}")
    
    def reset(self) -> None:
        """重置Kafka服务"""
        with self._lock:
            self.topics.clear()
            self.consumers.clear()
            self.producers.clear()
            self.error_rules.clear()
        logger.info("Kafka模拟服务已重置")


class TestFixtures:
    """测试Fixtures管理器"""
    
    def __init__(self, fixtures_dir: Optional[str] = None):
        """
        初始化Fixtures管理器
        
        Args:
            fixtures_dir: Fixtures目录路径
        """
        self.fixtures_dir = Path(fixtures_dir) if fixtures_dir else Path(__file__).parent
        self.fixtures_data: Dict[str, Any] = {}
        self.setup_functions: List[Callable] = []
        self.teardown_functions: List[Callable] = []
        
        # 加载fixtures数据
        self._load_fixtures()
        
        logger.info(f"测试Fixtures管理器已初始化: {self.fixtures_dir}")
    
    def _load_fixtures(self) -> None:
        """加载fixtures数据"""
        fixtures_file = self.fixtures_dir / "test_fixtures.json"
        
        if fixtures_file.exists():
            try:
                with open(fixtures_file, 'r', encoding='utf-8') as f:
                    self.fixtures_data = json.load(f)
                logger.info(f"加载fixtures数据: {len(self.fixtures_data)} 个项目")
            except Exception as e:
                logger.error(f"加载fixtures数据失败: {str(e)}")
        else:
            # 创建默认fixtures数据
            self._create_default_fixtures()
    
    def _create_default_fixtures(self) -> None:
        """创建默认fixtures数据"""
        default_fixtures = {
            "test_symbols": ["BTCUSDT", "ETHUSDT"],
            "test_timeframes": ["1m", "5m", "15m", "1h", "4h", "1d"],
            "test_user": {
                "user_id": "test_user_001",
                "api_key": "test_api_key",
                "secret_key": "test_secret_key"
            },
            "test_orders": [
                {
                    "order_id": "order_001",
                    "symbol": "BTCUSDT",
                    "side": "buy",
                    "quantity": 0.1,
                    "price": 45000.0,
                    "status": "filled"
                }
            ],
            "test_market_data": {
                "BTCUSDT": {
                    "price": 45000.0,
                    "volume": 1000.0,
                    "change_24h": 0.02
                },
                "ETHUSDT": {
                    "price": 3000.0,
                    "volume": 5000.0,
                    "change_24h": -0.01
                }
            }
        }
        
        self.fixtures_data = default_fixtures
        self._save_fixtures()
    
    def _save_fixtures(self) -> None:
        """保存fixtures数据"""
        fixtures_file = self.fixtures_dir / "test_fixtures.json"
        
        try:
            with open(fixtures_file, 'w', encoding='utf-8') as f:
                json.dump(self.fixtures_data, f, indent=2, ensure_ascii=False)
            logger.debug("Fixtures数据已保存")
        except Exception as e:
            logger.error(f"保存fixtures数据失败: {str(e)}")
    
    def get_fixture(self, name: str) -> Any:
        """
        获取fixture数据
        
        Args:
            name: fixture名称
        
        Returns:
            Any: fixture数据
        """
        return self.fixtures_data.get(name)
    
    def set_fixture(self, name: str, data: Any) -> None:
        """
        设置fixture数据
        
        Args:
            name: fixture名称
            data: fixture数据
        """
        self.fixtures_data[name] = data
        self._save_fixtures()
    
    def add_setup_function(self, func: Callable) -> None:
        """
        添加设置函数
        
        Args:
            func: 设置函数
        """
        self.setup_functions.append(func)
    
    def add_teardown_function(self, func: Callable) -> None:
        """
        添加清理函数
        
        Args:
            func: 清理函数
        """
        self.teardown_functions.append(func)
    
    async def setup(self) -> None:
        """执行设置"""
        logger.info("执行测试环境设置...")
        
        for func in self.setup_functions:
            try:
                if asyncio.iscoroutinefunction(func):
                    await func()
                else:
                    func()
            except Exception as e:
                logger.error(f"设置函数执行失败: {str(e)}")
                raise
    
    async def teardown(self) -> None:
        """执行清理"""
        logger.info("执行测试环境清理...")
        
        for func in reversed(self.teardown_functions):  # 逆序执行清理
            try:
                if asyncio.iscoroutinefunction(func):
                    await func()
                else:
                    func()
            except Exception as e:
                logger.error(f"清理函数执行失败: {str(e)}")


class MockServiceManager:
    """Mock服务管理器"""
    
    def __init__(self):
        """初始化Mock服务管理器"""
        self.java_api = MockJavaAPI()
        self.kafka_service = MockKafkaService()
        self.fixtures = TestFixtures()
        self.is_initialized = False
        
        logger.info("Mock服务管理器已初始化")
    
    async def start_services(self) -> None:
        """启动所有Mock服务"""
        if self.is_initialized:
            return
        
        logger.info("启动Mock服务...")
        
        # 启动fixtures设置
        await self.fixtures.setup()
        
        # 设置Kafka主题
        self.kafka_service.create_topic("market_data")
        self.kafka_service.create_topic("trading_signals")
        self.kafka_service.create_topic("order_updates")
        
        self.is_initialized = True
        logger.info("所有Mock服务已启动")
    
    async def stop_services(self) -> None:
        """停止所有Mock服务"""
        if not self.is_initialized:
            return
        
        logger.info("停止Mock服务...")
        
        # 执行fixtures清理
        await self.fixtures.teardown()
        
        # 重置服务
        self.java_api.reset()
        self.kafka_service.reset()
        
        self.is_initialized = False
        logger.info("所有Mock服务已停止")
    
    async def reset_all(self) -> None:
        """重置所有服务"""
        await self.stop_services()
        await self.start_services()
        logger.info("所有Mock服务已重置")
    
    def get_java_api(self) -> MockJavaAPI:
        """获取Java API模拟服务"""
        return self.java_api
    
    def get_kafka_service(self) -> MockKafkaService:
        """获取Kafka模拟服务"""
        return self.kafka_service
    
    def get_fixtures(self) -> TestFixtures:
        """获取测试Fixtures"""
        return self.fixtures


# 全局Mock服务管理器实例
_mock_manager = None


def get_mock_manager() -> MockServiceManager:
    """
    获取Mock服务管理器实例（单例）
    
    Returns:
        MockServiceManager: Mock服务管理器
    """
    global _mock_manager
    if _mock_manager is None:
        _mock_manager = MockServiceManager()
    return _mock_manager


async def setup_mock_environment() -> MockServiceManager:
    """
    设置Mock测试环境
    
    Returns:
        MockServiceManager: Mock服务管理器
    """
    manager = get_mock_manager()
    await manager.start_services()
    return manager


async def teardown_mock_environment() -> None:
    """清理Mock测试环境"""
    manager = get_mock_manager()
    await manager.stop_services()


# 装饰器：自动设置和清理Mock环境
def with_mock_environment(func):
    """Mock环境装饰器"""
    async def wrapper(*args, **kwargs):
        manager = await setup_mock_environment()
        try:
            return await func(*args, **kwargs)
        finally:
            await teardown_mock_environment()
    
    return wrapper