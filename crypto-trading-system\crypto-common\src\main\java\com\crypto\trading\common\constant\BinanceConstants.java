package com.crypto.trading.common.constant;

/**
 * 币安API常量类
 * <p>
 * 定义币安API相关的常量，如API基础URL、WebSocket URL、请求头等
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public final class BinanceConstants {

    /**
     * 私有构造函数，防止实例化
     */
    private BinanceConstants() {
        throw new IllegalStateException("常量类不允许实例化");
    }

    /**
     * 币安期货主网基础URL (USDT合约)
     */
    public static final String API_BASE_URL_FUTURES_USDT = "https://fapi.binance.com";

    /**
     * 币安期货测试网基础URL (USDT合约)
     */
    public static final String API_BASE_URL_FUTURES_USDT_TESTNET = "https://testnet.binancefuture.com";

    /**
     * 币安期货主网基础URL (币本位合约)
     */
    public static final String API_BASE_URL_FUTURES_COIN = "https://dapi.binance.com";

    /**
     * 币安期货测试网基础URL (币本位合约)
     */
    public static final String API_BASE_URL_FUTURES_COIN_TESTNET = "https://testnet.binancefuture.com";

    /**
     * 币安期货WebSocket URL (USDT合约)
     */
    public static final String WS_BASE_URL_FUTURES_USDT = "wss://fstream.binance.com";

    /**
     * 币安期货WebSocket URL (币本位合约)
     */
    public static final String WS_BASE_URL_FUTURES_COIN = "wss://dstream.binance.com";

    /**
     * 币安期货WebSocket URL (USDT合约测试网)
     */
    public static final String WS_BASE_URL_FUTURES_USDT_TESTNET = "wss://stream.binancefuture.com";

    /**
     * 币安期货WebSocket URL (币本位合约测试网)
     */
    public static final String WS_BASE_URL_FUTURES_COIN_TESTNET = "wss://dstream.binancefuture.com";

    /**
     * API密钥请求头
     */
    public static final String API_KEY_HEADER = "X-MBX-APIKEY";

    /**
     * 签名参数名
     */
    public static final String SIGNATURE_PARAM = "signature";

    /**
     * 时间戳参数名
     */
    public static final String TIMESTAMP_PARAM = "timestamp";

    /**
     * 接收窗口参数名
     */
    public static final String RECV_WINDOW_PARAM = "recvWindow";

    /**
     * 默认接收窗口时间（毫秒）
     */
    public static final long DEFAULT_RECV_WINDOW = 5000L;

    /**
     * 默认订单响应类型
     */
    public static final String DEFAULT_RESPONSE_TYPE = "RESULT";

    /**
     * 默认用户流数据ID的有效期（分钟）
     */
    public static final long DEFAULT_LISTEN_KEY_VALIDITY = 60L;

    /**
     * 默认WebSocket连接保活间隔（毫秒）
     */
    public static final long DEFAULT_WEBSOCKET_KEEPALIVE_INTERVAL = 30000L;

    /**
     * 默认WebSocket重连间隔（毫秒）
     */
    public static final long DEFAULT_WEBSOCKET_RECONNECT_INTERVAL = 5000L;

    /**
     * 默认WebSocket重连最大尝试次数
     */
    public static final int DEFAULT_WEBSOCKET_MAX_RECONNECT_ATTEMPTS = 5;

    /**
     * 默认K线数据订阅批量大小
     */
    public static final int DEFAULT_KLINE_SUBSCRIPTION_BATCH_SIZE = 10;

    /**
     * 默认深度数据更新间隔（毫秒）
     */
    public static final long DEFAULT_DEPTH_UPDATE_INTERVAL = 100L;

    /**
     * 默认深度数据订阅级别
     */
    public static final String DEFAULT_DEPTH_LEVEL = "20";

    /**
     * 默认用户数据流更新时间（分钟）
     */
    public static final long DEFAULT_USER_DATA_STREAM_KEEPALIVE = 30L;

    /**
     * 永续合约类型标识
     */
    public static final String CONTRACT_TYPE_PERPETUAL = "PERPETUAL";

    /**
     * 交割合约类型标识
     */
    public static final String CONTRACT_TYPE_DELIVERY = "DELIVERY";

    /**
     * 合约状态 - 交易中
     */
    public static final String CONTRACT_STATUS_TRADING = "TRADING";

    /**
     * 合约状态 - 已过期
     */
    public static final String CONTRACT_STATUS_EXPIRED = "EXPIRED";

    /**
     * 交易对分隔符
     */
    public static final String SYMBOL_DELIMITER = "_";
} 