package com.crypto.trading.market.repository;

import com.crypto.trading.common.dto.market.DepthDataDTO;
import com.crypto.trading.common.dto.market.KlineDataDTO;
import com.crypto.trading.common.dto.market.TradeDTO;
import com.crypto.trading.market.config.InfluxDBConfig;
import com.crypto.trading.market.config.MarketDataConfig;
import com.influxdb.client.InfluxDBClient;
import com.influxdb.client.WriteApi;
import com.influxdb.client.write.Point;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * InfluxDBRepository单元测试
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class InfluxDBRepositoryTest {

    @Mock
    private InfluxDBClient influxDBClient;

    @Mock
    private WriteApi writeApi;

    @Mock
    private InfluxDBConfig influxDBConfig;

    @Mock
    private MarketDataConfig marketDataConfig;
    
    @Mock
    private ExecutorService virtualThreadExecutor;

    @InjectMocks
    private InfluxDBRepository influxDBRepository;

    @BeforeEach
    void setUp() {
        when(influxDBClient.makeWriteApi()).thenReturn(writeApi);
        doReturn(100).when(marketDataConfig).getInfluxDbBatchSize();
        doReturn(1000).when(marketDataConfig).getInfluxDbFlushInterval();
        doReturn("market_data").when(influxDBConfig).getBucket();
        doReturn("crypto_org").when(influxDBConfig).getOrg();
        
        // 配置virtualThreadExecutor执行立即运行
        doAnswer(invocation -> {
            Runnable runnable = invocation.getArgument(0);
            runnable.run();
            return null;
        }).when(virtualThreadExecutor).submit(any(Runnable.class));

        // 手动初始化
        influxDBRepository.init();
    }

    @Test
    void testSaveKlineData() throws Exception {
        // 准备测试数据
        LocalDateTime openTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(1622535600000L), ZoneId.systemDefault());
        LocalDateTime closeTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(1622535659999L), ZoneId.systemDefault());
        
        KlineDataDTO klineDataDTO = new KlineDataDTO(
            "BTCUSDT", 
            "1m", 
            openTime,
            closeTime,
            new BigDecimal("35000.00"),
            new BigDecimal("35100.00"),
            new BigDecimal("34900.00"),
            new BigDecimal("35050.00"),
            new BigDecimal("10.5"),
            new BigDecimal("367500.00"),
            150L,
            new BigDecimal("5.2"),
            new BigDecimal("180000.00"),
            true
        );

        // 调用被测试方法
        influxDBRepository.saveKlineData(klineDataDTO);

        // 手动触发处理批次
        ReflectionTestUtils.invokeMethod(influxDBRepository, "processBatch");

        // 验证结果
        ArgumentCaptor<List<Point>> pointsCaptor = ArgumentCaptor.forClass(List.class);
        verify(writeApi).writePoints(eq("market_data"), eq("crypto_org"), pointsCaptor.capture());
        
        List<Point> capturedPoints = pointsCaptor.getValue();
        assertEquals(1, capturedPoints.size());
        // 由于Point对象的内部状态不易访问，我们只能验证点的数量
    }

    @Test
    void testSaveDepthData() throws Exception {
        // 准备测试数据
        List<DepthDataDTO.PriceQuantity> bids = new ArrayList<>();
        bids.add(new DepthDataDTO.PriceQuantity(new BigDecimal("34950.00"), new BigDecimal("1.5")));
        bids.add(new DepthDataDTO.PriceQuantity(new BigDecimal("34900.00"), new BigDecimal("2.5")));
        
        List<DepthDataDTO.PriceQuantity> asks = new ArrayList<>();
        asks.add(new DepthDataDTO.PriceQuantity(new BigDecimal("35050.00"), new BigDecimal("1.0")));
        asks.add(new DepthDataDTO.PriceQuantity(new BigDecimal("35100.00"), new BigDecimal("2.0")));
        
        DepthDataDTO depthDataDTO = new DepthDataDTO(
            "BTCUSDT", 
            123456789L, 
            20, 
            bids, 
            asks, 
            LocalDateTime.now()
        );

        // 调用被测试方法
        influxDBRepository.saveOrderBookData(depthDataDTO);

        // 手动触发处理批次
        ReflectionTestUtils.invokeMethod(influxDBRepository, "processBatch");

        // 验证结果
        ArgumentCaptor<List<Point>> pointsCaptor = ArgumentCaptor.forClass(List.class);
        verify(writeApi).writePoints(eq("market_data"), eq("crypto_org"), pointsCaptor.capture());
        
        List<Point> capturedPoints = pointsCaptor.getValue();
        assertEquals(1, capturedPoints.size());
    }

    @Test
    void testSaveTradeData() throws Exception {
        // 准备测试数据
        TradeDTO tradeDTO = new TradeDTO(
                987654321L,
                "BTCUSDT",
                new BigDecimal("35000.00"),
                new BigDecimal("0.5"),
                new BigDecimal("17500.00"),
                LocalDateTime.now(),
                false,
                true
        );

        // 调用被测试方法
        influxDBRepository.saveTradeData(tradeDTO);

        // 手动触发处理批次
        ReflectionTestUtils.invokeMethod(influxDBRepository, "processBatch");

        // 验证结果
        ArgumentCaptor<List<Point>> pointsCaptor = ArgumentCaptor.forClass(List.class);
        verify(writeApi).writePoints(eq("market_data"), eq("crypto_org"), pointsCaptor.capture());
        
        List<Point> capturedPoints = pointsCaptor.getValue();
        assertEquals(1, capturedPoints.size());
    }

    @Test
    void testBatchProcessing() throws Exception {
        // 准备多个测试数据
        for (int i = 0; i < 5; i++) {
            LocalDateTime openTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(1622535600000L + i * 60000), ZoneId.systemDefault());
            LocalDateTime closeTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(1622535659999L + i * 60000), ZoneId.systemDefault());
            
            KlineDataDTO klineDataDTO = new KlineDataDTO(
                "BTCUSDT", 
                "1m", 
                openTime,
                closeTime,
                new BigDecimal("35000.00").add(new BigDecimal(i * 10)),
                new BigDecimal("35100.00").add(new BigDecimal(i * 10)),
                new BigDecimal("34900.00").add(new BigDecimal(i * 10)),
                new BigDecimal("35050.00").add(new BigDecimal(i * 10)),
                new BigDecimal("10.5"),
                new BigDecimal("367500.00"),
                150L + i,
                new BigDecimal("5.2"),
                new BigDecimal("180000.00"),
                true
            );

            influxDBRepository.saveKlineData(klineDataDTO);
        }

        // 手动触发处理批次
        ReflectionTestUtils.invokeMethod(influxDBRepository, "processBatch");

        // 验证结果
        ArgumentCaptor<List<Point>> pointsCaptor = ArgumentCaptor.forClass(List.class);
        verify(writeApi).writePoints(eq("market_data"), eq("crypto_org"), pointsCaptor.capture());
        
        List<Point> capturedPoints = pointsCaptor.getValue();
        assertEquals(5, capturedPoints.size());
    }

    @Test
    void testDestroy() throws Exception {
        // 调用销毁方法
        influxDBRepository.destroy();

        // 验证WriteApi被关闭
        verify(writeApi).close();
    }
} 