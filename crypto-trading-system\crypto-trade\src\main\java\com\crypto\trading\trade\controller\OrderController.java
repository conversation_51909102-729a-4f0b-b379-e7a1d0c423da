package com.crypto.trading.trade.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.crypto.trading.common.dto.response.ApiResult;
import com.crypto.trading.common.dto.trade.OrderDTO;
import com.crypto.trading.common.dto.trade.OrderRequestDTO;
import com.crypto.trading.common.dto.trade.OrderResponseDTO;
import com.crypto.trading.common.enums.OrderStatus;
import com.crypto.trading.common.exception.BusinessException;
import com.crypto.trading.common.exception.ErrorCode;
import com.crypto.trading.trade.model.entity.order.OrderEntity;
import com.crypto.trading.trade.model.entity.order.status.OrderStatusEntity;
import com.crypto.trading.trade.service.order.OrderManagementService;
import com.crypto.trading.trade.util.OrderConverter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 订单管理控制器
 * 
 * <p>提供订单管理相关的REST API</p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@RestController
@RequestMapping("/api/v1/orders")
public class OrderController {

    private static final Logger log = LoggerFactory.getLogger(OrderController.class);

    @Autowired
    private OrderManagementService orderManagementService;

    /**
     * 创建订单
     *
     * @param request 订单请求
     * @return 订单响应
     */
    @PostMapping
    public ApiResult<OrderResponseDTO> createOrder(@Valid @RequestBody OrderRequestDTO request) {
        log.info("接收到创建订单请求: {}", request);
        
        try {
            // 生成客户端订单ID（如果未提供）
            if (request.getClientOrderId() == null || request.getClientOrderId().isEmpty()) {
                request.setClientOrderId("CID" + UUID.randomUUID().toString().replaceAll("-", "").substring(0, 16));
            }
            
            // 创建订单
            OrderDTO createdOrder = orderManagementService.createOrder(request);
            
            // 转换为响应DTO
            OrderResponseDTO response = new OrderResponseDTO();
            response.setOrderId(createdOrder.getOrderId());
            response.setClientOrderId(createdOrder.getClientOrderId());
            response.setSymbol(createdOrder.getSymbol());
            response.setSide(createdOrder.getSide());
            response.setPositionSide(createdOrder.getPositionSide());
            response.setType(createdOrder.getType());
            response.setQuantity(createdOrder.getQuantity());
            response.setPrice(createdOrder.getPrice());
            response.setExecutedQuantity(createdOrder.getExecutedQuantity());
            response.setExecutedPrice(createdOrder.getExecutedPrice());
            response.setStatus(createdOrder.getStatus());
            response.setStrategy(createdOrder.getStrategyId());
            response.setCreatedTime(createdOrder.getCreatedTime());
            response.setUpdatedTime(createdOrder.getUpdatedTime());
            
            return ApiResult.success(response);
        } catch (BusinessException e) {
            log.error("创建订单失败: {}", e.getMessage());
            return ApiResult.error(ErrorCode.valueOf(e.getErrorCode()), e.getMessage());
        } catch (Exception e) {
            log.error("创建订单异常", e);
            return ApiResult.error(ErrorCode.BUSINESS_ERROR.getCode(), "创建订单失败: " + e.getMessage());
        }
    }

    /**
     * 根据订单ID获取订单
     *
     * @param orderId 订单ID
     * @return 订单响应
     */
    @GetMapping("/{orderId}")
    public ApiResult<OrderResponseDTO> getOrderById(@PathVariable String orderId) {
        log.info("查询订单: orderId={}", orderId);
        
        try {
            OrderEntity order = orderManagementService.findOrder(orderId, null);
            if (order == null) {
                return ApiResult.error(ErrorCode.NOT_FOUND.getCode(), "订单不存在");
            }
            
            OrderResponseDTO response = OrderConverter.toOrderResponseDTO(order);
            return ApiResult.success(response);
        } catch (Exception e) {
            log.error("查询订单异常", e);
            return ApiResult.error(ErrorCode.SYSTEM_ERROR.getCode(), "查询订单失败: " + e.getMessage());
        }
    }

    /**
     * 根据客户端订单ID获取订单
     *
     * @param clientOrderId 客户端订单ID
     * @return 订单响应
     */
    @GetMapping("/client/{clientOrderId}")
    public ApiResult<OrderResponseDTO> getOrderByClientOrderId(@PathVariable String clientOrderId) {
        log.info("查询订单: clientOrderId={}", clientOrderId);
        
        try {
            OrderEntity order = orderManagementService.findOrder(null, clientOrderId);
            if (order == null) {
                return ApiResult.error(ErrorCode.NOT_FOUND.getCode(), "订单不存在");
            }
            
            OrderResponseDTO response = OrderConverter.toOrderResponseDTO(order);
            return ApiResult.success(response);
        } catch (Exception e) {
            log.error("查询订单异常", e);
            return ApiResult.error(ErrorCode.SYSTEM_ERROR.getCode(), "查询订单失败: " + e.getMessage());
        }
    }

    /**
     * 取消订单
     *
     * @param orderId 订单ID
     * @return 操作结果
     */
    @DeleteMapping("/{orderId}")
    public ApiResult<OrderResponseDTO> cancelOrder(@PathVariable String orderId) {
        log.info("取消订单: orderId={}", orderId);
        
        try {
            OrderDTO canceledOrder = orderManagementService.cancelOrder(orderId, null);
            
            OrderResponseDTO response = new OrderResponseDTO();
            response.setOrderId(canceledOrder.getOrderId());
            response.setClientOrderId(canceledOrder.getClientOrderId());
            response.setSymbol(canceledOrder.getSymbol());
            response.setSide(canceledOrder.getSide());
            response.setPositionSide(canceledOrder.getPositionSide());
            response.setType(canceledOrder.getType());
            response.setQuantity(canceledOrder.getQuantity());
            response.setPrice(canceledOrder.getPrice());
            response.setExecutedQuantity(canceledOrder.getExecutedQuantity());
            response.setExecutedPrice(canceledOrder.getExecutedPrice());
            response.setStatus(canceledOrder.getStatus());
            response.setStrategy(canceledOrder.getStrategyId());
            response.setCreatedTime(canceledOrder.getCreatedTime());
            response.setUpdatedTime(canceledOrder.getUpdatedTime());
            
            return ApiResult.success(response);
        } catch (BusinessException e) {
            log.error("取消订单失败: {}", e.getMessage());
            return ApiResult.error(ErrorCode.valueOf(e.getErrorCode()), e.getMessage());
        } catch (Exception e) {
            log.error("取消订单异常", e);
            return ApiResult.error(ErrorCode.SYSTEM_ERROR.getCode(), "取消订单失败: " + e.getMessage());
        }
    }

    /**
     * 分页查询订单列表
     *
     * @param symbol 交易对
     * @param status 订单状态
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param page 页码
     * @param size 每页大小
     * @return 订单列表
     */
    @GetMapping
    public ApiResult<List<OrderResponseDTO>> getOrders(
            @RequestParam(required = false) String symbol,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) Long startTime,
            @RequestParam(required = false) Long endTime,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size) {
        
        log.info("查询订单列表: symbol={}, status={}, startTime={}, endTime={}, page={}, size={}",
                symbol, status, startTime, endTime, page, size);
        
        try {
            List<OrderResponseDTO> responseList = new ArrayList<>();
            
            if (symbol != null && !symbol.isEmpty() && startTime != null && endTime != null) {
                // 按交易对和时间范围查询
                responseList = orderManagementService.findOrders(symbol, status, size);
            } else if (status != null && !status.isEmpty()) {
                // 按状态查询
                responseList = orderManagementService.findOrders(null, status, size);
            } else {
                return ApiResult.error(ErrorCode.PARAM_ERROR, "请提供查询条件");
            }
            
            return ApiResult.success(responseList);
        } catch (Exception e) {
            log.error("查询订单列表异常", e);
            return ApiResult.error(ErrorCode.SYSTEM_ERROR.getCode(), "查询订单列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取订单状态历史
     *
     * @param orderId 订单ID
     * @return 状态历史列表
     */
    @GetMapping("/{orderId}/status-history")
    public ApiResult<List<OrderStatusEntity>> getOrderStatusHistory(@PathVariable String orderId) {
        log.info("查询订单状态历史: orderId={}", orderId);
        
        try {
            List<OrderStatusEntity> statusHistory = orderManagementService.getOrderStatusHistory(orderId);
            return ApiResult.success(statusHistory);
        } catch (Exception e) {
            log.error("查询订单状态历史异常", e);
            return ApiResult.error(ErrorCode.SYSTEM_ERROR.getCode(), "查询订单状态历史失败: " + e.getMessage());
        }
    }

    /**
     * 获取订单统计数据
     *
     * @param startDate 开始日期（格式：yyyyMMdd）
     * @param endDate 结束日期（格式：yyyyMMdd）
     * @param symbol 交易对（可选）
     * @param strategy 策略名称（可选）
     * @return 统计数据
     */
    @GetMapping("/statistics")
    public ApiResult<Map<String, Object>> getOrderStatistics(
            @RequestParam String startDate,
            @RequestParam String endDate,
            @RequestParam(required = false) String symbol,
            @RequestParam(required = false) String strategy) {
        
        log.info("查询订单统计数据: startDate={}, endDate={}, symbol={}, strategy={}",
                startDate, endDate, symbol, strategy);
        
        try {
            Map<String, Object> statistics = orderManagementService.getOrderStatistics(startDate, endDate, symbol, strategy);
            return ApiResult.success(statistics);
        } catch (Exception e) {
            log.error("查询订单统计数据异常", e);
            return ApiResult.error(ErrorCode.SYSTEM_ERROR.getCode(), "查询订单统计数据失败: " + e.getMessage());
        }
    }

    /**
     * 重新计算统计数据
     *
     * @param date 日期（格式：yyyyMMdd）
     * @return 操作结果
     */
    @PostMapping("/statistics/recalculate")
    public ApiResult<Boolean> recalculateStatistics(@RequestParam String date) {
        log.info("重新计算统计数据: date={}", date);
        
        try {
            boolean result = orderManagementService.recalculateStatistics(date);
            return ApiResult.success(result);
        } catch (Exception e) {
            log.error("重新计算统计数据异常", e);
            return ApiResult.error(ErrorCode.SYSTEM_ERROR.getCode(), "重新计算统计数据失败: " + e.getMessage());
        }
    }
}