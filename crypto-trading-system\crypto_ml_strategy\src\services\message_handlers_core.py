#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
消息处理核心模块

包含核心消息处理器类和K线消息处理逻辑，
实现完整数据流：Kafka消息 → 数据处理 → 技术指标计算 → ML预测 → 信号生成
"""

import time
import asyncio
from typing import Dict, Any, Optional
from loguru import logger

from .infrastructure.intelligent_cache import IntelligentCacheService
from .services.technical_indicator_service import TechnicalIndicatorService
from .services.prediction_engine_service import PredictionEngineService
from .infrastructure.async_error_recovery import AsyncErrorRecoveryService, AsyncTask
from .infrastructure.service_interfaces import IDataProcessorService, IStrategyService, IKafkaService, IMetricsService


class EnhancedMessageHandlers:
    """
    增强的消息处理器核心类
    
    集成缓存、技术指标、ML预测和异步错误恢复，
    实现高性能的完整数据流处理。
    """
    
    def __init__(self,
                 cache_service: IntelligentCacheService,
                 technical_indicator_service: TechnicalIndicatorService,
                 prediction_engine_service: PredictionEngineService,
                 async_error_recovery: AsyncErrorRecoveryService,
                 data_processor: IDataProcessorService,
                 strategy_service: IStrategyService,
                 kafka_service: IKafkaService,
                 metrics_service: IMetricsService):
        """
        初始化增强消息处理器
        
        Args:
            cache_service: 智能缓存服务
            technical_indicator_service: 技术指标服务
            prediction_engine_service: 预测引擎服务
            async_error_recovery: 异步错误恢复服务
            data_processor: 数据处理服务
            strategy_service: 策略服务
            kafka_service: Kafka服务
            metrics_service: 指标服务
        """
        self.cache_service = cache_service
        self.technical_indicator_service = technical_indicator_service
        self.prediction_engine = prediction_engine_service
        self.async_error_recovery = async_error_recovery
        self.data_processor = data_processor
        self.strategy_service = strategy_service
        self.kafka_service = kafka_service
        self.metrics_service = metrics_service
        
        logger.info("增强消息处理器初始化完成")
    
    async def handle_kline_message(self, message: Dict[str, Any]) -> None:
        """
        处理K线消息
        
        Args:
            message: K线消息数据
        """
        start_time = time.time()
        
        try:
            self.metrics_service.increment_counter("kline_messages_received")
            
            # 1. 检查缓存
            cache_key = f"kline_{message.get('symbol')}_{message.get('timestamp')}"
            cached_result = self.cache_service.get(cache_key)
            
            if cached_result:
                logger.debug(f"使用缓存的K线处理结果: {cache_key}")
                if cached_result.get('signal'):
                    self.kafka_service.publish_signal(cached_result['signal'])
                return
            
            # 2. 数据处理
            processed_data = self.data_processor.process_kline_data(message)
            
            if not processed_data:
                logger.warning("K线数据处理失败，跳过后续处理")
                return
            
            # 3. 技术指标计算
            indicator_result = self.technical_indicator_service.calculate_all_indicators(
                processed_data, 
                symbol=message.get('symbol', 'BTCUSDT'), 
                timeframe=message.get('timeframe', '1m')
            )
            
            # 4. ML预测（异步错误恢复包装）
            prediction_task = AsyncTask(
                task_id=f"predict_kline_{message.get('symbol')}_{time.time()}",
                func=self.prediction_engine.predict,
                kwargs={
                    'features': {
                        'processed_data': processed_data,
                        'technical_indicators': indicator_result,
                        'message_type': 'kline'
                    },
                    'symbol': message.get('symbol', 'BTCUSDT'),
                    'timeframe': message.get('timeframe', '1m')
                }
            )
            
            prediction_result = await self.async_error_recovery.execute_task(prediction_task)
            
            # 5. 信号生成
            signal = self.strategy_service.on_market_data({
                'processed_data': processed_data,
                'indicators': indicator_result,
                'prediction': prediction_result,
                'message_type': 'kline'
            })
            
            # 6. 缓存结果和发布信号
            result_data = {
                'processed_data': processed_data,
                'indicators': indicator_result,
                'prediction': prediction_result,
                'signal': signal
            }
            
            self.cache_service.put(cache_key, result_data, ttl=60.0)
            
            if signal:
                self.kafka_service.publish_signal(signal)
                self.metrics_service.increment_counter("signals_generated")
            
            # 记录处理时间
            processing_time = (time.time() - start_time) * 1000
            self.metrics_service.record_histogram("kline_processing_time_ms", processing_time)
            
            logger.debug(f"K线消息处理完成 - {message.get('symbol')}, 处理时间: {processing_time:.1f}ms")
            
        except Exception as e:
            logger.error(f"K线消息处理失败: {e}")
            self.metrics_service.increment_counter("kline_processing_errors")