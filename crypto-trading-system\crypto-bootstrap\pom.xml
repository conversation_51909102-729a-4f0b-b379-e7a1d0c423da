<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.crypto.trading</groupId>
        <artifactId>crypto-trading</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>crypto-bootstrap</artifactId>
    <name>crypto-bootstrap</name>
    <description>应用启动模块 - 负责系统初始化和生命周期管理</description>

    <dependencies>
        <!-- 公共模块依赖 -->
        <dependency>
            <groupId>com.crypto.trading</groupId>
            <artifactId>crypto-common</artifactId>
        </dependency>
        
        <!-- 其他功能模块依赖 -->
        <dependency>
            <groupId>com.crypto.trading</groupId>
            <artifactId>crypto-sdk</artifactId>
        </dependency>
        
        <!-- 市场数据模块依赖 -->
        <dependency>
            <groupId>com.crypto.trading</groupId>
            <artifactId>crypto-market-data</artifactId>
        </dependency>
        
        <!-- Spring Retry -->
        <dependency>
            <groupId>org.springframework.retry</groupId>
            <artifactId>spring-retry</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>
        
        <!-- Spring Web -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <!-- Spring Boot Maven 插件 -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <mainClass>com.crypto.trading.bootstrap.CryptoApplication</mainClass>
                    <excludes>
                        <exclude>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                        </exclude>
                    </excludes>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
