#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据质量验证系统集成组件

将数据质量验证系统与OptimizedTimeframeSync和其他现有组件集成。
提供统一的数据质量管理接口。
"""

import logging
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any, Union
from datetime import datetime
import time
import threading

from .data_quality_core import (
    DataQualityChecker, QualityConfig, QualityMetrics, QualityLevel
)
from .data_quality_validators import (
    MissingValueValidator, OutlierValidator, RangeValidator, TimeSeriesValidator
)
from .data_quality_repairers import (
    MissingValueRepairer, OutlierRepairer, RangeRepairer, 
    DataSmoother, TimeSeriesRepairer
)
from .data_quality_metrics import QualityMonitor


class IntegratedQualityManager:
    """集成质量管理器"""
    
    def __init__(self, config: Optional[Dict] = None):
        """
        初始化集成质量管理器
        
        Args:
            config: 配置字典
        """
        self.logger = logging.getLogger('cryptoMlStrategy.data.quality_integration')
        
        # 默认配置
        self.config = {
            'quality_check': {
                'enable_real_time': True,
                'enable_batch_mode': True,
                'auto_repair': True,
                'performance_target_ms': 50,
                'quality_threshold': 0.7
            },
            'integration': {
                'sync_with_timeframe_sync': True,
                'kafka_validation': True,
                'java_api_compatibility': True,
                'enable_quality_feedback': True
            },
            'monitoring': {
                'enable_alerts': True,
                'enable_trends': True,
                'history_retention_days': 30
            }
        }
        
        if config:
            self._deep_update(self.config, config)
        
        # 初始化组件
        quality_config = QualityConfig()
        self._update_quality_config(quality_config)
        
        self.quality_checker = DataQualityChecker(quality_config)
        self.quality_monitor = QualityMonitor(self.config.get('monitoring', {}))
        
        # 注册验证器
        self._register_validators()
        
        # 注册修复器
        self._register_repairers()
        
        # 性能统计
        self._stats = {
            'total_integrations': 0,
            'successful_checks': 0,
            'auto_repairs': 0,
            'quality_improvements': 0,
            'start_time': time.time()
        }
        
        # 线程锁
        self._lock = threading.RLock()
        
        self.logger.info("集成质量管理器初始化完成")
    
    def _deep_update(self, base_dict: Dict, update_dict: Dict):
        """深度更新字典"""
        for key, value in update_dict.items():
            if key in base_dict and isinstance(base_dict[key], dict) and isinstance(value, dict):
                self._deep_update(base_dict[key], value)
            else:
                base_dict[key] = value
    
    def _update_quality_config(self, quality_config: QualityConfig):
        """更新质量配置"""
        if 'quality_check' in self.config:
            qc_config = self.config['quality_check']
            quality_config.enable_auto_repair = qc_config.get('auto_repair', True)
            quality_config.max_check_duration_ms = qc_config.get('performance_target_ms', 50)
    
    def _register_validators(self):
        """注册验证器"""
        validators = [
            MissingValueValidator(),
            OutlierValidator(),
            RangeValidator(),
            TimeSeriesValidator()
        ]
        
        for validator in validators:
            success = self.quality_checker.register_validator(validator)
            if success:
                self.logger.info(f"验证器注册成功: {validator.get_validator_name()}")
    
    def _register_repairers(self):
        """注册修复器"""
        repairers = [
            MissingValueRepairer(),
            OutlierRepairer(),
            RangeRepairer(),
            DataSmoother(),
            TimeSeriesRepairer()
        ]
        
        for repairer in repairers:
            success = self.quality_checker.register_repairer(repairer)
            if success:
                self.logger.info(f"修复器注册成功: {repairer.get_repairer_name()}")
    
    def validate_and_repair_data(self, data: pd.DataFrame, symbol: str = "", 
                                timeframe: str = "") -> Tuple[pd.DataFrame, QualityMetrics, List[str]]:
        """
        验证和修复数据质量
        
        Args:
            data: 要处理的数据
            symbol: 交易对符号
            timeframe: 时间框架
            
        Returns:
            (修复后的数据, 质量指标, 修复日志)
        """
        start_time = time.time()
        
        try:
            with self._lock:
                self._stats['total_integrations'] += 1
            
            # 执行质量检查
            metrics = self.quality_checker.check_data_quality(data, symbol, timeframe)
            
            # 记录质量指标到监控器
            if self.config['monitoring']['enable_alerts']:
                self.quality_monitor.record_quality_metrics(symbol, timeframe, metrics)
            
            repair_logs = []
            repaired_data = data.copy()
            
            # 自动修复（如果启用）
            if (self.config['quality_check']['auto_repair'] and 
                metrics.overall_score < self.config['quality_check']['quality_threshold']):
                
                repaired_data, repair_logs = self.quality_checker.repair_data_quality(data, metrics)
                
                # 重新检查修复后的质量
                if repair_logs:
                    post_repair_metrics = self.quality_checker.check_data_quality(
                        repaired_data, symbol, timeframe
                    )
                    
                    # 计算质量改善
                    improvement = post_repair_metrics.overall_score - metrics.overall_score
                    if improvement > 0:
                        with self._lock:
                            self._stats['quality_improvements'] += 1
                        
                        repair_logs.append(f"质量改善: {improvement:.3f} ({metrics.overall_score:.3f} -> {post_repair_metrics.overall_score:.3f})")
                        metrics = post_repair_metrics  # 使用修复后的指标
                    
                    with self._lock:
                        self._stats['auto_repairs'] += 1
            
            # 更新统计
            processing_time = (time.time() - start_time) * 1000
            if processing_time <= self.config['quality_check']['performance_target_ms']:
                with self._lock:
                    self._stats['successful_checks'] += 1
            
            self.logger.debug(f"数据质量处理完成: {symbol} {timeframe}, 分数: {metrics.overall_score:.3f}, 耗时: {processing_time:.1f}ms")
            
            return repaired_data, metrics, repair_logs
            
        except Exception as e:
            self.logger.error(f"数据质量处理失败: {e}")
            # 返回原始数据和错误指标
            error_metrics = QualityMetrics(
                overall_score=0.0,
                completeness_score=0.0,
                consistency_score=0.0,
                accuracy_score=0.0,
                timeliness_score=0.0,
                validity_score=0.0,
                total_records=len(data) if data is not None else 0,
                valid_records=0,
                missing_values=0,
                outliers_detected=0,
                quality_level=QualityLevel.CRITICAL,
                issues=[]
            )
            return data, error_metrics, [f"处理失败: {str(e)}"]
    
    def integrate_with_timeframe_sync(self, sync_instance, symbol: str, 
                                    synchronized_data: pd.DataFrame) -> Tuple[pd.DataFrame, Dict[str, Any]]:
        """
        与OptimizedTimeframeSync集成
        
        Args:
            sync_instance: OptimizedTimeframeSync实例
            symbol: 交易对符号
            synchronized_data: 同步后的数据
            
        Returns:
            (质量验证后的数据, 质量报告)
        """
        try:
            if not self.config['integration']['sync_with_timeframe_sync']:
                return synchronized_data, {}
            
            # 对同步后的数据进行质量验证
            validated_data, metrics, repair_logs = self.validate_and_repair_data(
                synchronized_data, symbol, "multi_timeframe"
            )
            
            # 生成质量报告
            quality_report = {
                'quality_score': metrics.overall_score,
                'quality_level': metrics.quality_level.value,
                'issues_found': len(metrics.issues),
                'repairs_applied': len(repair_logs),
                'processing_time_ms': metrics.check_duration_ms,
                'recommendations': self._generate_recommendations(metrics)
            }
            
            # 如果质量太低，提供反馈给同步器
            if (self.config['integration']['enable_quality_feedback'] and 
                metrics.overall_score < 0.5):
                
                quality_report['sync_feedback'] = {
                    'suggest_parameter_adjustment': True,
                    'problematic_timeframes': self._identify_problematic_timeframes(synchronized_data, metrics),
                    'recommended_actions': [
                        "检查数据源质量",
                        "调整同步策略参数",
                        "增加数据验证步骤"
                    ]
                }
            
            return validated_data, quality_report
            
        except Exception as e:
            self.logger.error(f"与时间框架同步器集成失败: {e}")
            return synchronized_data, {'error': str(e)}
    
    def validate_kafka_message(self, message: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """
        验证Kafka消息格式
        
        Args:
            message: Kafka消息
            
        Returns:
            (是否有效, 验证错误列表)
        """
        try:
            if not self.config['integration']['kafka_validation']:
                return True, []
            
            errors = []
            
            # 检查必需字段
            required_fields = ['data', 'timestamp', 'symbol']
            for field in required_fields:
                if field not in message:
                    errors.append(f"缺少必需字段: {field}")
            
            # 检查数据字段
            if 'data' in message:
                data = message['data']
                required_data_fields = ['open', 'high', 'low', 'close', 'volume']
                
                for field in required_data_fields:
                    if field not in data:
                        errors.append(f"数据中缺少字段: {field}")
                    else:
                        try:
                            value = float(data[field])
                            if value < 0 and field != 'close':  # close可能为负（某些衍生品）
                                errors.append(f"字段 {field} 值为负: {value}")
                        except (ValueError, TypeError):
                            errors.append(f"字段 {field} 不是有效数值: {data[field]}")
            
            # 检查时间戳
            if 'timestamp' in message:
                try:
                    timestamp = int(message['timestamp'])
                    current_time = int(time.time() * 1000)
                    
                    # 检查时间戳合理性（不能太旧或太新）
                    if timestamp < current_time - 86400000:  # 1天前
                        errors.append(f"时间戳过旧: {timestamp}")
                    elif timestamp > current_time + 60000:  # 1分钟后
                        errors.append(f"时间戳过新: {timestamp}")
                        
                except (ValueError, TypeError):
                    errors.append(f"时间戳格式无效: {message['timestamp']}")
            
            return len(errors) == 0, errors
            
        except Exception as e:
            self.logger.error(f"Kafka消息验证失败: {e}")
            return False, [f"验证过程出错: {str(e)}"]
    
    def get_quality_dashboard_data(self, symbols: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        获取质量仪表板数据
        
        Args:
            symbols: 要包含的交易对列表
            
        Returns:
            仪表板数据
        """
        try:
            dashboard_data = {
                'summary': {},
                'trends': {},
                'alerts': {},
                'performance': {},
                'recommendations': []
            }
            
            # 获取质量摘要
            dashboard_data['summary'] = self.quality_monitor.get_quality_summary()
            
            # 获取趋势数据
            if symbols:
                trends = {}
                for symbol in symbols:
                    for timeframe in ['1m', '5m', '15m', '1h']:
                        trend = self.quality_monitor.get_quality_trend(symbol, timeframe, '1h')
                        if trend:
                            key = f"{symbol}_{timeframe}"
                            trends[key] = {
                                'avg_score': trend.avg_score,
                                'trend_direction': 'improving' if trend.score_trend > 0 else 'declining',
                                'sample_count': trend.sample_count
                            }
                dashboard_data['trends'] = trends
            
            # 获取活跃告警
            active_alerts = self.quality_monitor.get_active_alerts()
            dashboard_data['alerts'] = {
                'total_active': len(active_alerts),
                'by_severity': {},
                'recent': []
            }
            
            # 按严重程度分组告警
            for alert in active_alerts:
                severity = alert.severity
                if severity not in dashboard_data['alerts']['by_severity']:
                    dashboard_data['alerts']['by_severity'][severity] = 0
                dashboard_data['alerts']['by_severity'][severity] += 1
            
            # 最近告警
            dashboard_data['alerts']['recent'] = [
                {
                    'message': alert.message,
                    'severity': alert.severity,
                    'symbol': alert.affected_symbol,
                    'timestamp': alert.timestamp.isoformat()
                }
                for alert in active_alerts[:5]  # 最近5个
            ]
            
            # 性能数据
            dashboard_data['performance'] = {
                'checker_stats': self.quality_checker.get_statistics(),
                'monitor_stats': self.quality_monitor.get_statistics(),
                'integration_stats': self._stats.copy()
            }
            
            # 生成建议
            dashboard_data['recommendations'] = self._generate_dashboard_recommendations(dashboard_data)
            
            return dashboard_data
            
        except Exception as e:
            self.logger.error(f"获取仪表板数据失败: {e}")
            return {}
    
    def _generate_recommendations(self, metrics: QualityMetrics) -> List[str]:
        """生成质量改善建议"""
        recommendations = []
        
        try:
            if metrics.completeness_score < 0.8:
                recommendations.append("数据完整性较低，建议检查数据源连接和采集流程")
            
            if metrics.consistency_score < 0.7:
                recommendations.append("数据一致性问题较多，建议启用异常值检测和修复")
            
            if metrics.timeliness_score < 0.8:
                recommendations.append("数据时效性问题，建议检查时间同步和数据延迟")
            
            if len(metrics.issues) > 10:
                recommendations.append("数据问题较多，建议启用自动修复功能")
            
            if metrics.overall_score < 0.5:
                recommendations.append("整体数据质量较差，建议全面检查数据管道")
            
        except Exception as e:
            self.logger.error(f"生成建议失败: {e}")
        
        return recommendations
    
    def _identify_problematic_timeframes(self, data: pd.DataFrame, metrics: QualityMetrics) -> List[str]:
        """识别有问题的时间框架"""
        problematic = []
        
        try:
            # 分析列名中的时间框架标识
            timeframes = ['1m', '5m', '15m', '1h', '4h', '1d']
            
            for tf in timeframes:
                tf_columns = [col for col in data.columns if tf in col]
                if tf_columns:
                    # 检查该时间框架的数据质量
                    tf_data = data[tf_columns]
                    missing_ratio = tf_data.isnull().sum().sum() / (len(tf_data) * len(tf_columns))
                    
                    if missing_ratio > 0.2:  # 20%以上缺失
                        problematic.append(tf)
            
        except Exception as e:
            self.logger.error(f"识别问题时间框架失败: {e}")
        
        return problematic
    
    def _generate_dashboard_recommendations(self, dashboard_data: Dict) -> List[str]:
        """生成仪表板建议"""
        recommendations = []
        
        try:
            summary = dashboard_data.get('summary', {})
            overall_stats = summary.get('overall_stats', {})
            
            if overall_stats.get('avg_score', 1.0) < 0.7:
                recommendations.append("整体数据质量偏低，建议加强数据验证")
            
            alerts = dashboard_data.get('alerts', {})
            if alerts.get('total_active', 0) > 5:
                recommendations.append("活跃告警较多，建议优先处理高严重程度告警")
            
            performance = dashboard_data.get('performance', {})
            integration_stats = performance.get('integration_stats', {})
            
            if integration_stats.get('successful_checks', 0) / max(1, integration_stats.get('total_integrations', 1)) < 0.9:
                recommendations.append("质量检查成功率较低，建议检查系统配置")
            
        except Exception as e:
            self.logger.error(f"生成仪表板建议失败: {e}")
        
        return recommendations
    
    def get_integration_statistics(self) -> Dict[str, Any]:
        """获取集成统计信息"""
        with self._lock:
            elapsed_time = time.time() - self._stats['start_time']
            
            return {
                'integration_stats': self._stats.copy(),
                'performance': {
                    'integrations_per_second': self._stats['total_integrations'] / max(1, elapsed_time),
                    'success_rate': self._stats['successful_checks'] / max(1, self._stats['total_integrations']),
                    'auto_repair_rate': self._stats['auto_repairs'] / max(1, self._stats['total_integrations']),
                    'quality_improvement_rate': self._stats['quality_improvements'] / max(1, self._stats['auto_repairs'])
                },
                'component_stats': {
                    'quality_checker': self.quality_checker.get_statistics(),
                    'quality_monitor': self.quality_monitor.get_statistics()
                },
                'config': self.config
            }
    
    def shutdown(self):
        """关闭集成管理器"""
        try:
            self.quality_monitor.shutdown()
            self.logger.info("集成质量管理器已关闭")
        except Exception as e:
            self.logger.error(f"关闭集成管理器失败: {e}")